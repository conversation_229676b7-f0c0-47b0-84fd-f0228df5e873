<?
if (!$active_product) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
	    	<td><? new infoBox(array(array('text' => '<span class="largeFont redIndicator"><b>'.TEXT_PRODUCT_NOT_FOUND.'</b></span><br><br>' . sprintf(TEXT_PRODUCT_NOT_FOUND_OPTIONS, tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'), tep_href_link('/')))), 0, 'class="messageBox"', 'class="messageRow"', 'class="messageData"', 0); ?></td>
		</tr>
	</table>
<?
} else {
	$extra_buy_button_html = '';
	$custom_type = tep_get_custom_product_type($products_id);
	
	$gst_popup = '/popup/gst.html';
	
	if ($_SESSION['languages_id'] == 2 || $_SESSION['languages_id'] == 3) {
		$gst_popup = '/popup/gst_cn.html';
	}
	
	switch($custom_type) {
		case 2:
			$isCDKey = true;
			break;
		default:
			$isPowerLevelling = true;
	}
		
	if ($isCDKey) {
		$html_framework = "&nbsp;";//Form controls for power levelling level selection, etc. Not needed.
		$option_html_array = array();
		$option_html_array = tep_get_options_html($products_id, 0);
		
		$product_info_query = tep_db_query("select p.products_id, p.products_model, p.products_quantity, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id 
											from " . TABLE_PRODUCTS . " p  
											where p.products_status = '1' 
												and p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "'");
		$product_info = tep_db_fetch_array($product_info_query);
		
		$prod_desc_array = tep_get_products_complete_description($product_info['products_id']);
		$product_info = array_merge($product_info, $prod_desc_array);
		
		$normal_price = $product_info['products_price'];
		
		if ($new_price = tep_get_products_special_price($products_id)) {
	      	$products_price = '<s>' . $currencies->format($product_info['products_price']) . '</s> <span class="productSpecialPrice">' . $currencies->display_price_nodiscount($products_id, $new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
	    } else {
	    	$products_price = $currencies->display_price($products_id, $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']), 1, ($show_it==2 ? true : false));
	    }
	} else if ($isPowerLevelling) {
		//Start Power Levelling
		$cust_discount_array = tep_get_customer_discount_array($customer_id, (int)$products_id, 'product');
		$got_discount = abs($cust_discount_array['individual'] + $cust_discount_array['group']) > 0 ? true : false;
		
		$html_framework = '';
		$range_resource_array = tep_get_bracket_range($datapool_parent_id);
		$start_level_range = tep_get_start_level($range_resource_array["start"], $range_resource_array["range"], $range_resource_array["mode"]);
		$level_label = tep_get_bracket_value($datapool_parent_id, KEY_PL_LEVEL_LABEL);
		$class_label = tep_get_bracket_value($datapool_parent_id, KEY_PL_CLASS_LABEL);
		$base_price_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_BASE_PRICE);
		$base_time_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_BASE_TIME);
		$min_time_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_MIN_TIME);
		$product_currency = tep_get_products_field($products_id, 'products_base_currency');
        $base_price_value_for_js = $currencies->advance_currency_conversion($base_price_value, $product_currency, $_SESSION['currency'], false, 'buy');
        
		if (is_null($index)) {	// Add to cart
			$latest_price = $latest_eta = 0;

			//if ($datapool_parent_id > 0 && count($range_resource_array["range"]) > 0) {	// must at least has one bracket to work
			if ($datapool_parent_id > 0) {	// must at least has one bracket to work
				if (count($range_resource_array["range"]) <= 1) {	// Only one or no bracket implies no need to waste time for level selection
					$desired_level_value = count($range_resource_array["range"]) < 1 ? 0 : $range_resource_array["range"][0]['level'];
					
					$current_level_html = $start_level_range[1]['text'] . tep_draw_hidden_field('cmbCurrentLevel', $start_level_range[1]['id'], 'id="cmbCurrentLevel"');
					$desired_level_html = (trim($range_resource_array["range"][0]['alias']) != '' ? $range_resource_array["range"][0]['alias'] : $desired_level_value) . tep_draw_hidden_field('cmbDesiredLevel', $desired_level_value, 'id="cmbDesiredLevel"');
					
					$option_html_array = tep_get_options_html($products_id, $desired_level_value);
					$starting_path = tep_get_level_name_path($datapool_parent_id, ' > ');
					$level_tree_array = array();
					tep_get_datapool_subtree_array($datapool_parent_id, $level_tree_array, 0, $starting_path);
					//	Get current class selection if any
					tep_display_selection_option_html($level_tree_array, $start_level_range[1]['id'], $current_class_selection);
					
					//	Get desired class selection if any
					$sel_current_class = (int)$HTTP_POST_VARS["cmbCurrentClass"] > 0 ? (int)$HTTP_POST_VARS["cmbCurrentClass"] : ($current_class_selection[0]['id'] > 0 ? $current_class_selection[0]['id'] : $datapool_parent_id);
					//$selected_current_class = isset($HTTP_POST_VARS["cmbCurrentClass"]) ? (int)$HTTP_POST_VARS["cmbCurrentClass"] : $current_class_selection[0]['id'];
					$desired_class_starting_path = tep_get_level_name_path($sel_current_class, ' > ');
					$desired_class_level_tree_array = array();
					tep_get_datapool_subtree_array($sel_current_class, $desired_class_level_tree_array, 0, $desired_class_starting_path);
					
					if ($sel_current_class != $datapool_parent_id) {
						$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
						$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
						if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
							$desired_class_level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
																	   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
																			'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
																			'name' => $data_pool_level_row['data_pool_level_name'], 
																			'ident' => 0, 
																			'path' => $desired_class_starting_path,
																			'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
																			'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
																			'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
																			'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
																			'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
																			'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
																			'child' => $desired_class_level_tree_array)
																);
						}
					}
					
					tep_display_selection_option_html($desired_class_level_tree_array, $desired_level_value, $desired_class_selection);
					
					tep_calculate_bracket_price($datapool_parent_id, $start_level_range[1]['id'], $desired_level_value, $latest_price, $latest_eta, $latest_msg, $custom_tags);
				} else {
					$current_level_html = 	tep_draw_pull_down_menu("cmbCurrentLevel", $start_level_range, $HTTP_POST_VARS["cmbCurrentLevel"], ' id="cmbCurrentLevel" onChange="invoke_start_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'current_class_div\', \'desired_class_div\', \'options_div\')" ') . 
											'<noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript>';
					if ((int)$HTTP_POST_VARS["cmbCurrentLevel"] > 0) {
						$starting_path = tep_get_level_name_path($datapool_parent_id, ' > ');
						$level_tree_array = array();
						tep_get_datapool_subtree_array($datapool_parent_id, $level_tree_array, 0, $starting_path);
						
						//	Get current class selection if any
						tep_display_selection_option_html($level_tree_array, $HTTP_POST_VARS["cmbCurrentLevel"], $current_class_selection);
						
						$desired_level_array = array( array('id' => '', 'text' => 'Please Select') );
						$desired_level_array = array_merge($desired_level_array, tep_get_desired_level_array($datapool_parent_id, (int)$HTTP_POST_VARS["cmbCurrentLevel"]));
						
						if ((int)$HTTP_POST_VARS["cmbDesiredLevel"] > 0 && (int)$HTTP_POST_VARS["cmbDesiredLevel"] > (int)$HTTP_POST_VARS["cmbCurrentLevel"]) {
							//	Get desired class selection if any
							$sel_current_class = (int)$HTTP_POST_VARS["cmbCurrentClass"] > 0 ? (int)$HTTP_POST_VARS["cmbCurrentClass"] : $datapool_parent_id;
							$desired_class_starting_path = tep_get_level_name_path($sel_current_class, ' > ');
							$desired_class_level_tree_array = array();
							tep_get_datapool_subtree_array($sel_current_class, $desired_class_level_tree_array, 0, $desired_class_starting_path);
							
							if ($sel_current_class != $datapool_parent_id) {
								$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . (int)$HTTP_POST_VARS["cmbCurrentClass"] . "'";
								$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
								if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
									$desired_class_level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
																			   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
																					'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
																					'name' => $data_pool_level_row['data_pool_level_name'], 
																					'ident' => 0, 
																					'path' => $desired_class_starting_path,
																					'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
																					'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
																					'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
																					'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
																					'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
																					'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
																					'child' => $desired_class_level_tree_array)
																		);
								}
							}
							
							tep_display_selection_option_html($desired_class_level_tree_array, $HTTP_POST_VARS["cmbDesiredLevel"], $desired_class_selection);
							
							tep_calculate_bracket_price($datapool_parent_id, $HTTP_POST_VARS["cmbCurrentLevel"], $HTTP_POST_VARS["cmbDesiredLevel"], $latest_price, $latest_eta, $latest_msg, $custom_tags);
						} else {
							$desired_class_selection = array();
							$HTTP_POST_VARS["cmbDesiredLevel"] = '';
						}
					} else {
						$current_class_selection = array();
						$desired_level_array = array(array('id' => '', 'text' => '---'));
					}
					$desired_level_html = tep_draw_pull_down_menu("cmbDesiredLevel", $desired_level_array, $HTTP_POST_VARS["cmbDesiredLevel"], ' id="cmbDesiredLevel" onChange="invoke_desired_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'desired_class_div\', \'options_div\')" ');
					if (count($desired_level_array) > 1) {
						$desired_level_html .= '<noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript>';
					}
					$option_html_array = tep_get_options_html($products_id, tep_not_null($HTTP_POST_VARS["cmbDesiredLevel"]) ? $HTTP_POST_VARS["cmbDesiredLevel"] : 0);
				}
				
				$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $latest_price, $latest_eta, $min_time_value, $option_html_array, $HTTP_POST_VARS["cp_option"]);
                
				$current_class_html = '
						<div id="current_class_div" style="padding-top:0.1em;'.(count($current_class_selection) ? '' : ' display: none;').'">
							<table width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $class_label).'</td>
									<td class="inputField">'.tep_draw_pull_down_menu("cmbCurrentClass", $current_class_selection, '', ' id="cmbCurrentClass" onChange="invoke_class_tasks(\''.$datapool_parent_id.'\', \'cmbDesiredLevel\', \'cmbCurrentClass\', \'cmbDesiredClass\', \'desired_class_div\')"').'<span id="current_class_loading"></span><noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript></td>
								</tr>
							</table>
						</div>';
				$desired_class_html = '
						<div id="desired_class_div" style="padding-top:0.1em;'.(count($desired_class_selection) ? '' : ' display: none;').'">
							<table width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $class_label).'</td>
									<td class="inputField">'.tep_draw_pull_down_menu("cmbDesiredClass", $desired_class_selection, '', ' id="cmbDesiredClass"').'</td>
								</tr>
							</table>
						</div>';
				
				if (count($range_resource_array["range"]) > 0) {
					$html_framework .= '	<div style="padding-top:1em;">
											<table width="100%" cellspacing="0" cellpadding="2">
												<tr>
						 							<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $level_label).'</td>
													<td class="inputField">'.$current_level_html.'&nbsp;<span id="current_field_loading"></span></td>
						 						</tr>
											</table>
										</div>';
				}
				
				$html_framework .= $current_class_html;
				
				if (count($range_resource_array["range"]) > 0) {
					$html_framework .= '<div style="padding-top:0.1em;">
											<table width="100%" cellspacing="0" cellpadding="2">
												<tr>
						 							<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $level_label).'</td>
													<td valign="top" class="inputField">'.$desired_level_html.'
														<span id="end_level_suggestion_section" class="messageStackWarning"></span>
														&nbsp;<span id="desired_field_loading"></span>
													</td>
						 						</tr>
											</table>
										</div>';
				}
				
				$html_framework .= $desired_class_html;
			}
		} else {
			if (count($range_resource_array["range"]) <= 1) {
				if ($custom_cart["info"]["current_level"] != $start_level_range[1]['id']) {
					$custom_cart["info"]["current_level"] = $cart->contents[$products_id]['custom'][$cart->get_custom_prd_type($products_id)][$index]['content']["info"]["current_level"] = $start_level_range[1]['id'];
				}
				if ($custom_cart["info"]["desired_level"] != $range_resource_array["range"][0]['level']) {
					$custom_cart["info"]["desired_level"] = $cart->contents[$products_id]['custom'][$cart->get_custom_prd_type($products_id)][$index]['content']["info"]["current_level"] = $range_resource_array["range"][0]['level'];
				}
				
				$current_level_html = $start_level_range[1]['text'] . tep_draw_hidden_field('cmbCurrentLevel', $start_level_range[1]['id']);
				$desired_level_html = (trim($range_resource_array["range"][0]['alias']) != '' ? $range_resource_array["range"][0]['alias'] : $range_resource_array["range"][0]['level']) . tep_draw_hidden_field('cmbDesiredLevel', $range_resource_array["range"][0]['level']);
			} else {
				$desired_level_array = array( array('id' => '', 'text' => 'Please Select') );
				$desired_level_array = array_merge($desired_level_array, tep_get_desired_level_array($datapool_parent_id, $custom_cart["info"]["current_level"]));
				
				$current_level_html = tep_draw_pull_down_menu("cmbCurrentLevel", $start_level_range, $custom_cart["info"]["current_level"], ' id="cmbCurrentLevel" onChange="invoke_start_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'current_class_div\', \'desired_class_div\', \'options_div\')" ');
				$current_level_html .= '<noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript>';
				if ($custom_cart["info"]["current_level"] < 1)	$custom_cart["info"]["desired_level"] = 0;
				
				$desired_level_html = tep_draw_pull_down_menu("cmbDesiredLevel", $desired_level_array, $custom_cart["info"]["desired_level"], ' id="cmbDesiredLevel" onChange="invoke_desired_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'desired_class_div\', \'options_div\')" ');
				if ($custom_cart["info"]["desired_level"] > 0)	$desired_level_html .= '<noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript>';
			}
			
			$option_html_array = tep_get_options_html($products_id, $custom_cart["info"]["desired_level"]);
			
			$starting_path = tep_get_level_name_path($datapool_parent_id, ' > ');
			$level_tree_array = array();
			tep_get_datapool_subtree_array($datapool_parent_id, $level_tree_array, 0, $starting_path);
			
			//	Get current class selection if any
			tep_display_selection_option_html($level_tree_array, $custom_cart["info"]["current_level"], $current_class_selection);
			
			//	Get desired class selection if any
			$sel_current_class = $custom_cart["info"]["current_class"] > 0 ? $custom_cart["info"]["current_class"] : $datapool_parent_id;
			$desired_class_starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$desired_class_level_tree_array = array();
			tep_get_datapool_subtree_array($sel_current_class, $desired_class_level_tree_array, 0, $desired_class_starting_path);
			
			if ($sel_current_class != $datapool_parent_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . (int)$sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$desired_class_level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
															   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
																	'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
																	'name' => $data_pool_level_row['data_pool_level_name'], 
																	'ident' => 0, 
																	'path' => $desired_class_starting_path,
																	'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
																	'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
																	'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
																	'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
																	'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
																	'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
																	'child' => $desired_class_level_tree_array)
														);
				}
			}
			
			tep_display_selection_option_html($desired_class_level_tree_array, $custom_cart["info"]["desired_level"], $desired_class_selection);
			
			tep_calculate_bracket_price($datapool_parent_id, $custom_cart["info"]["current_level"], $custom_cart["info"]["desired_level"], $latest_price, $latest_eta, $latest_msg, $custom_tags);
			
			$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $latest_price, $latest_eta, $min_time_value, $option_html_array, $custom_cart["option"]);
			
			if (count($range_resource_array["range"]) > 0) {
				$html_framework .= '	<div style="padding-top:1em;">
										<table width="100%" cellspacing="0" cellpadding="2">
											<tr>
					 							<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $level_label).'</td>
												<td class="inputField">'.$current_level_html.'&nbsp;<span id="current_field_loading"></span></td>
					 						</tr>
										</table>
									</div>';
			}
			$html_framework .= '	<div id="current_class_div" style="padding-top:0.1em;'.(count($current_class_selection) ? '' : ' display: none;').'">
									<table width="100%" cellspacing="0" cellpadding="2">
										<tr>
											<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $class_label).'</td>
											<td class="inputField">'.tep_draw_pull_down_menu("cmbCurrentClass", $current_class_selection, $custom_cart["info"]["current_class"], 'id="cmbCurrentClass" onChange="invoke_class_tasks(\''.$datapool_parent_id.'\', \'cmbDesiredLevel\', \'cmbCurrentClass\', \'cmbDesiredClass\', \'desired_class_div\')" ').'<span id="current_class_loading"></span><noscript>&nbsp;'.tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"','gray_submit_button',false).'</noscript></td>
										</tr>
									</table>
								</div>';
			if (count($range_resource_array["range"]) > 0) {
				$html_framework .= '	<div style="padding-top:0.1em;">
										<table width="100%" cellspacing="0" cellpadding="2">
											<tr>
					 							<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $level_label).'</td>
												<td valign="top" class="inputField">'.$desired_level_html.'
													<span id="end_level_suggestion_section" class="messageStackWarning">'.$latest_msg.'</span>
													&nbsp;<span id="desired_field_loading"></span>
												</td>
					 						</tr>
										</table>
									</div>';
			}
			$html_framework .= '<div id="desired_class_div" style="padding-top:0.1em;'.(count($desired_class_selection) ? '' : ' display: none;').'">
									<table width="100%" cellspacing="0" cellpadding="2">
										<tr>
											<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $class_label).'</td>
											<td class="inputField">'.tep_draw_pull_down_menu("cmbDesiredClass", $desired_class_selection, $custom_cart["info"]["desired_class"], ' id="cmbDesiredClass"').'</td>
										</tr>
									</table>
								</div>';
		}
	} //End Power Levelling
?>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript">
		
		var selectionTitle = Array();
		selectionTitle['id'] = '';
		selectionTitle['text'] = 'Please Select';
		
		function appendToSelect(select, value, content) {
			var opt = document.createElement('option');
			opt.value = value;
			opt.text = content;
			select.options.add(opt);
		}
		
		function clearOptionList(obj) {
			while (obj.options.length > 0) {
				obj.remove(0);
			}
		}
		
		//	By Wei Chen
		function invoke_start_level_tasks(id, selObj1, selObj2, divClass1, divClass2, divOptions) {
			var start_level_obj = DOMCall(selObj1);
			var end_level_obj = DOMCall(selObj2);
			var div_class_obj = DOMCall(divClass1);
			var div_desired_class_obj = DOMCall(divClass2);
			var div_option_obj = DOMCall(divOptions);
			var level_suggest_obj = DOMCall('end_level_suggestion_section');
			var current_loading_span_obj = DOMCall('current_field_loading');
			
			start_level_obj.disabled = true;
			div_class_obj.innerHTML = '';
			div_desired_class_obj.innerHTML = '';
			level_suggest_obj.innerHTML = '';
			current_loading_span_obj.innerHTML = 'Loading...';
			
			if (start_level_obj.value > 0) {
				var ref_url = "custom_product_xmlhttp.php?action=start_level_task&level_id="+id+"&s_level="+start_level_obj.value+'&lang='+'<?=$languages_id?>';
				jQuery.ajax({
					url:ref_url,
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
			      		// res = xmlhttp.responseText;
			      		
			      		clearOptionList(end_level_obj);
			      		if (jQuery(xml).find('desired_selection').length > 0) {
			      			appendToSelect(end_level_obj, selectionTitle['id'], selectionTitle['text']);
			      			
			      			var option_sel = '';
				      		jQuery(xml).find('desired_selection option').each(function(){
				      			option_sel = jQuery(this);
				      			appendToSelect(end_level_obj, option_sel.attr("index"), option_sel.text());
						    });
			      		}
			      		
			      		if (end_level_obj.options.length < 2) {
			      			clearOptionList(end_level_obj);
			      			appendToSelect(end_level_obj, '', '---');
			      		}
			      		
			      		selection = jQuery(xml).find("class_selection");
			      		if (jQuery(xml).find("class_selection") > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_CURRENT_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbCurrentClass';
					    	class_sel_obj.id = 'cmbCurrentClass';
					    	
					    	class_sel_obj.onchange=function() {
								invoke_class_tasks(id, selObj2, 'cmbCurrentClass', 'cmbDesiredClass', divClass2);
							}
							
			      			var option_sel = '';
				      		for (var i=0; i < jQuery(xml).find("class_selection").length; i++) {
				      			option_sel = selection.getElementsByTagName("option")[i];
				      			appendToSelect(class_sel_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
						    }
				      		
				      		var loading_span_obj = document.createElement('SPAN');
						    loading_span_obj.setAttribute('id', 'current_class_loading');
						    loading_span_obj.setAttribute('className', 'inputField');
							loading_span_obj.setAttribute('class', 'inputField');
						    
						    dCell2.appendChild(class_sel_obj);
						    dCell2.appendChild(loading_span_obj);
						    
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		if (jQuery(xml).find("option_selection").length) {
			      			div_option_obj.innerHTML = jQuery(xml).find("option_selection").text();
						} else {
			      			div_option_obj.innerHTML = '';
			      		}
			      		
			      		reset_price();
			      		current_loading_span_obj.innerHTML = '';
			      		
			      		start_level_obj.disabled = false;
			      	}
			    });
			} else {
				clearOptionList(end_level_obj);
				appendToSelect(end_level_obj, '', '---');
				
				// Reset Price and ETA, and show global options, do it first here for safe in case the xmlhttp doesn't work
				reset_price();
				
				var ref_url = "custom_product_xmlhttp.php?action=list_global_options&level_id="+id+'&lang='+'<?=$languages_id?>';
				jQuery.ajax({
					url:ref_url,
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
						if (jQuery(xml).find('option_selection').length > 0) {
							div_option_obj.innerHTML = jQuery(xml).find('option_selection').text()
						} else {
				  			div_option_obj.innerHTML = '';
				  		}
				  		
				  		start_level_obj.disabled = false;
				  		
				  		// Reset Price and ETA, and show global options
						reset_price();
						
						current_loading_span_obj.innerHTML = '';
					}
				});
			}
		}
		
		function invoke_desired_level_tasks(id, selObj1, selObj2, divClass, divOptions) {
			var start_level_obj = DOMCall(selObj1);
			var end_level_obj = DOMCall(selObj2);
			var div_class_obj = DOMCall(divClass);
			var div_option_obj = DOMCall(divOptions);
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			var level_suggest_obj = DOMCall('end_level_suggestion_section');
			var desired_loading_span_obj = DOMCall('desired_field_loading');
			
			var current_class_obj = DOMCall('cmbCurrentClass');
			
			if (current_class_obj != null) {
				var sel_class_value = current_class_obj.value;
			} else {
				var sel_class_value = id;
			}
			
			start_level_obj.disabled = true;
			end_level_obj.disabled = true;
			
			div_class_obj.innerHTML = '';
			level_suggest_obj.innerHTML = '';
			desired_loading_span_obj.innerHTML = 'Loading...';
			
			if (end_level_obj.value > 0) {
				var ref_url = "custom_product_xmlhttp.php?action=end_level_task&level_id="+id+"&s_level="+start_level_obj.value+"&e_level="+end_level_obj.value+"&class_root_id="+sel_class_value+"&lang="+'<?=$languages_id?>';
				jQuery.ajax({
					url:ref_url,
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
			      		if (jQuery(xml).find("class_selection > option").length > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_DESIRED_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbDesiredClass';
					    	class_sel_obj.id = 'cmbDesiredClass';
					    	
			      			var option_sel = '';
				      		jQuery(xml).find("class_selection > option").each(function() {
				      			option_sel = jQuery(this);
				      			appendToSelect(class_sel_obj, option_sel.attr("index"), option_sel.text());
						    });
				      		
				      		dCell2.appendChild(class_sel_obj);
				      		
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		if (jQuery(xml).find("option_selection").length > 0) {
							div_option_obj.innerHTML = jQuery(xml).find("option_selection").text();
						} else {
			      			div_option_obj.innerHTML = '';
			      		}
			      		
			      		if (jQuery(xml).find("bracket_info").length > 0) {
			      			if (jQuery(xml).find("bracket_info > price").length > 0 ) {
								var price = jQuery(xml).find("bracket_info > price").text();
								hidden_price_obj.value = price;
							}
							
							if (jQuery(xml).find("bracket_info > eta").length > 0) {
								var eta = jQuery(xml).find("bracket_info > eta").text();
								hidden_eta_obj.value = eta;
							}
							
							if (jQuery(xml).find("bracket_info > msg").length > 0) {
								var msg = jQuery(xml).find("bracket_info > msg").text();
								
								if (msg.length > 0) {
									level_suggest_obj.innerHTML = msg;
								}
							}
							
							show_price_and_eta_info(price, 0, eta, 0);
			      		}
			      		
			      		desired_loading_span_obj.innerHTML = '';
			      		start_level_obj.disabled = false;
						end_level_obj.disabled = false;
					}
				});
				
			} else {
				// Reset Price and ETA, and show global options, do it first here for safe in case the xmlhttp doesn't work
				reset_price();
				
				var ref_url = "custom_product_xmlhttp.php?action=list_global_options&level_id="+id+'&lang='+'<?=$languages_id?>';
				jQuery.ajax({
					url:ref_url,
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
						if (jQuery(xml).find('option_selection').length > 0) {
							div_option_obj.innerHTML = jQuery(xml).find('option_selection').text();
						} else {
				  			div_option_obj.innerHTML = '';
				  		}
				  		start_level_obj.disabled = false;
						end_level_obj.disabled = false;
						
						// Reset Price and ETA, and show global options
						reset_price();
						desired_loading_span_obj.innerHTML = '';
					}
				});
			}
		}
		
		function invoke_class_tasks(id, selObj2, classSelObj1, classSelObj2, divClass) {
			var end_level_obj = DOMCall(selObj2);
			var current_class_obj = DOMCall(classSelObj1);
			var desired_class_obj = DOMCall(classSelObj2);
			var div_class_obj = DOMCall(divClass);
			var current_class_loading_span_obj = DOMCall('current_class_loading');
			
			if (end_level_obj != null && end_level_obj.value > 0) {
				end_level_obj.disabled = true;
				current_class_obj.disabled = true;
				
				current_class_loading_span_obj.innerHTML = ' Loading...';
				div_class_obj.innerHTML = '';
				
				var ref_url = "custom_product_xmlhttp.php?action=class_task&level_id="+id+"&e_level="+end_level_obj.value+"&class_root_id="+current_class_obj.value+'&lang='+'<?=$languages_id?>';
				jQuery.ajax({
					url:ref_url,
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
			      		if (jQuery(xml).find('class_selection').length > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_DESIRED_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbDesiredClass';
					    	class_sel_obj.id = 'cmbDesiredClass';
					    	
				      		jQuery(xml).find('class_selection').each(function(){
				      			appendToSelect(class_sel_obj, jQuery(this).attr("index"), jQuery(this).text());
						    });
				      		
				      		dCell2.appendChild(class_sel_obj);
				      		
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		current_class_loading_span_obj.innerHTML = '';
			      		end_level_obj.disabled = false;
						current_class_obj.disabled = false;
					}
				});
			}
		}
		
		function reset_price() {
			//var buy_price_price_obj = DOMCall('calced_price');
			var quote_price_obj = DOMCall('quote_price_div');
			var quote_eta_obj = DOMCall('quote_eta_div');
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			
			//buy_price_price_obj.innerHTML = currency(0);
			quote_price_obj.innerHTML = quote_eta_obj.innerHTML = '';
			hidden_price_obj.value = hidden_eta_obj.value = 0;
			
			show_price_and_eta_info(0, 0, 0, 0);	// Reset to base price and time
		}
		
		function show_price_and_eta_info(base_price, extra_price, base_eta, extra_eta) {
			var ind_discount = <?=trim($cust_discount_array["individual"]) != '' ? $cust_discount_array["individual"] : 0 ?>;
			var grp_discount = <?=trim($cust_discount_array["group"]) != '' ? $cust_discount_array["group"] : 0 ?>;
			var start_price = <?=trim($base_price_value_for_js) != '' ? $base_price_value_for_js : 0 ?>;
			var start_time = <?=trim($base_time_value) != '' ? $base_time_value : 0 ?>;
			var min_time = <?=trim($min_time_value) != '' ? $min_time_value : 0 ?>;
			var approximate_str = '<?=TEXT_APPROXIMATE_ETA?>';

            //var buy_price_price_obj = DOMCall('calced_price');
			var quote_price_obj = DOMCall('quote_price_div');
			var quote_eta_obj = DOMCall('quote_eta_div');
			
			if (base_price == null || trim_str(base_price) == '') base_price = 0;
			if (extra_price == null) extra_price = 0;
			if (base_eta == null || trim_str(base_eta) == '') base_eta = 0;
			if (extra_eta == null) extra_eta = 0;

			var total_price = parseFloat(start_price) + parseFloat(base_price) + parseFloat(extra_price);
			var total_eta = parseFloat(start_time) + parseFloat(base_eta) + parseFloat(extra_eta);
			total_eta = (total_eta < min_time) ? min_time : total_eta;
			
			var eta_days = Math.floor(total_eta/24);
			var eta_hours = 0;
			if (total_eta < 24 || total_eta % 24 > 0) {
				eta_hours = Math.ceil( total_eta - (eta_days * 24));
				
				if (eta_hours == 24) {
					eta_days++;
					eta_hours = 0;
				}
			}
			
			var days_str = (eta_days > 0) ? eta_days + ' day' + (eta_days > 1 ? 's' : '') : '';
			var hrs_str = (eta_hours > 0) ? ' ' + eta_hours + ' hour' + (eta_hours > 1 ? 's' : '') : '';
			
			var original_price = total_price;
			var total_discount = ind_discount + grp_discount;
			var got_discount = false;
			
			if (Math.abs(total_discount) > 0) {
				//	Got discount
				got_discount = true;
				if (total_discount > 0) {
					total_price = total_price + total_price * Math.abs(total_discount) / 100;
				} else {
					total_price = total_price - total_price * Math.abs(total_discount) / 100;
				}
			}
			
			if (got_discount) {
				quote_price_obj.innerHTML = '<s>' + currency(original_price) + '</s>&nbsp;&nbsp;' + currency(total_price) + (Math.abs(ind_discount) > 0 ? '<span class="requiredInfo">*</span>' : '');
			} else {
				quote_price_obj.innerHTML = currency(total_price);
			}
			
			// display GST description
			pop_up_link = "<?=tep_href_link($gst_popup);?>";
			quote_price_obj.innerHTML += "<?=(tep_not_null($_SESSION['RegionGST']['tax_title']) ? '<br /><span style=\"font-weight: normal;\"><a href=\"javascript:;\" onclick=\"window.open(\'" + pop_up_link + "\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');\">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</a></span>' : '');?>";
			
			//buy_price_price_obj.innerHTML = currency(total_price);
			if (days_str.length < 0 && hrs_str.length < 0) {
				quote_eta_obj.innerHTML = approximate_str + ' 0 hour.';
			} else {
				quote_eta_obj.innerHTML = approximate_str + ' ' + days_str + hrs_str;
			}
		}
		
		function update_option_price_and_eat(sel_obj, id) {
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			
			var opts_price = 0;
			var opts_eta = 0;
			
			var optionRegExp  = /cp_option\[(\d+)\]/i;
			
			var form_ele_num = document.mainForm.elements.length;
			for (i=0; i < form_ele_num; i++) {
				var ele_obj = document.mainForm.elements[i];
				var res_array = optionRegExp.exec(ele_obj.name);
				
				if (res_array != null && res_array.length > 0) {
					var opt_id = res_array[1];
					
					if (cp_option_price[opt_id] != null || cp_option_eta[opt_id] != null) {
						switch(ele_obj.type) {
							case 'text':
							case 'textarea':
								if (trim_str(ele_obj.value) != '') {
									if (cp_option_price[opt_id][0] != null)	opts_price += parseFloat(cp_option_price[opt_id][0]);
									if (cp_option_eta[opt_id][0] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][0]);
								}
								break;
							case 'radio':
								if (ele_obj.checked) {
									var sub_id = ele_obj.value;
									if (trim_str(sub_id) != '') {
										if (cp_option_price[opt_id][sub_id] != null)	opts_price += parseFloat(cp_option_price[opt_id][sub_id]);
										if (cp_option_eta[opt_id][sub_id] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][sub_id]);
									}
								}
								break;
							default :
								var sub_id = ele_obj.value;
								if (trim_str(sub_id) != '') {
									if (cp_option_price[opt_id][sub_id] != null)	opts_price += parseFloat(cp_option_price[opt_id][sub_id]);
									if (cp_option_eta[opt_id][sub_id] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][sub_id]);
								}
								break;
						}
					}
				}
			}

            show_price_and_eta_info(hidden_price_obj.value, opts_price, hidden_eta_obj.value, opts_eta);
		}
		
		function submitValidate() {
			var current_level_obj = DOMCall('cmbCurrentLevel');
			var desired_level_obj = DOMCall('cmbDesiredLevel');
			
			var errorcount = 0;
			var errormsg = '<?=JS_ERROR?>';
			
			if (current_level_obj != null && current_level_obj.selectedIndex < 1) {
				errorcount++;
				errormsg = errormsg + "* Current " + '<?=$level_label?>' + " is missing.\n";
			}
			
			if (desired_level_obj != null && desired_level_obj.selectedIndex < 1) {
				errorcount++;
				errormsg = errormsg + "* Desired " + '<?=$level_label?>' + " is missing.\n";
			}

<? if ($isPowerLevelling) { ?>			
			var options_checking_result = options_validation();
			if (options_checking_result != true) {
				errorcount++;
				errormsg += "\n" + 'Other Info Error:' + "\n" + options_checking_result;
			}
<? } ?>		
			
			if (errorcount == 0) {
				return true;;
				//document.mainForm.submit();
			} else {
				alert(errormsg);
				return false;
			}
		}
		
		function submitDirectTopupValidate() {
			var errormsg = '<?=JS_ERROR?>';
			var errorcount = 0;
			
			if (!parseInt(jQuery("#buyqty").val())>0) {
				errormsg = errormsg + "* Invalid quantity.\n";
			    errorcount++;
			} else {
				var patt1=/^game_info/gi;
				var str;
				jQuery("form#mainForm input, form#mainForm textarea, form#mainForm select").each(function(){
				    str = jQuery(this).attr('name');
				    if (errorcount==0 && str.match(patt1) && jQuery(this).val() == '') {
				    	errormsg = errormsg + "* Incomplete game account info.\n";
				        errorcount++;
				    }
				});
			}
			
			if (errorcount == 0) {
				return true;;
			} else {
				alert(errormsg);
				return false;
			}
		}
		
		function currency(anynum) {
			var cur_symbol_left = '<?=$currencies->currencies[$currency]["symbol_left"]?>';
			var cur_symbol_right = '<?=$currencies->currencies[$currency]["symbol_right"]?>';
//			var cur_value = '<?=$currencies->currencies[$currency]["sell_value"]?>';

//			anynum = eval(anynum * cur_value);
            workNum = Math.abs((Math.round(anynum*100)/100));
            workStr = "" + workNum;
            if (workStr.indexOf(".")==-1) { workStr += ".00"; }
            dStr = workStr.substr(0,workStr.indexOf("."));
            dNum = dStr - 0;
            pStr = workStr.substr(workStr.indexOf("."));
            while (pStr.length<3) { pStr += "0"; }

            if (dNum >= 1000) {
                dLen = dStr.length;
                dStr = parseInt(""+(dNum/1000))+","+dStr.substring(dLen-3,dLen);
            }

            if (dNum >= 1000000) {
                dLen = dStr.length;
                dStr = parseInt(""+(dNum/1000000))+","+dStr.substring(dLen-7,dLen);
            }

            retval = dStr + pStr;
            if (anynum < 0) { retval = "("+retval+")"; }

			if (cur_symbol_left != '') {
				return cur_symbol_left + retval;
			} else {
				return retval + cur_symbol_right;
			}
		}
	</script>
	<!--form name="mainForm" action="<?=tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, tep_get_all_get_params())?>" method="post"-->
<?
if ($isPowerLevelling) {
    $currency_count_index = 'pw_0';
    
	if ((tep_not_null($html_framework) && is_null($index)) || $isCDKey) {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "");
		$show_it = $status_info['show'];
		
		if ($show_it) {
			$extra_buy_button_html =    tep_draw_hidden_field('products_id', $product_info['products_id']) .
                                        tep_image_button2('green', 'javascript:void(0)', ($bdn=='y' ? IMAGE_BUTTON_UPDATE : IMAGE_BUTTON_BUY_NOW), 120, ' id="' . $currency_count_index . '" onclick="if(submitValidate()){pfv(this.id)}"');
		} else {
			$extra_buy_button_html = tep_image_button2('green', 'javascript:void(0)', IMAGE_BUTTON_BUY_NOW);
		}
	} else if (tep_not_null($html_framework) && !is_null($index)) { 
		$extra_buy_button_html = tep_image_button2('green', 'javascript:void(0)', IMAGE_BUTTON_UPDATE, 120, ' id="' . $currency_count_index . '" onclick="if(submitValidate()) {pfv(this.id)}" name="CartUpdate"');
	}
	
	ob_start();
	echo tep_draw_form('mainForm', tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, tep_get_all_get_params(array('action')) . (($isPowerLevelling) ? 'action=buy_pwl' : 'action=buy_now')), 'post', 'onSubmit="return submitValidate();"');
	echo '	<table border="0" width="100%" cellspacing="0" cellpadding="0">';
	
	if ($messageStack->size('cart_success') > 0) { 
		echo '	<tr>
					<td>'.$messageStack->output('cart_success').'</td>
				</tr>';
 	} else if ($messageStack->size('cart_failure') > 0) { 
		echo '	<tr>
					<td>'.$messageStack->output('cart_failure').'</td>
				</tr>';
	}
	
	echo '		<tr id="data_'.$currency_count_index.'"
                    data-pid="'.$products_id.'" 
                    data-products-bundle="'.$product_info['products_bundle'].'" 
                    data-dm=""
                    data-cpt-id="1"
                    data-name="'.$product_info['products_name'].'">
					<td style="width: 515px; padding: 5px 15px 5px 5px;">
						<h1>'.$product_info['products_name'].'</h1>';
	echo '				<table border="0" width="100%" cellspacing="0" cellpadding="0">
			  				<tr>
								<td>Additional Add-ons:</td>
							</tr>
                            <tr>
                                <td style="text-align: right"> ' . TEXT_FIELD_REQUIRED . ' </td>
                            </tr>
			  				<tr>
								<td>';
	echo $html_framework;
	
	if ($isPowerLevelling) {
        $product_currency = tep_get_products_field($products_id, 'products_base_currency');
        $latest_price = $currencies->advance_currency_conversion($latest_price, $product_currency, $currency, false, 'buy');
		echo tep_draw_hidden_field('datapool_parent_id', $datapool_parent_id);
		echo tep_draw_hidden_field('hidden_quote_price', $latest_price, 'id="hidden_quote_price"');
		echo tep_draw_hidden_field('hidden_quote_eta', $latest_eta, 'id="hidden_quote_eta"');
	}
	
	if (count($option_html_array)) {
		$custom_bracket_tags_shown = false;
		echo '						<div class="inputLabelBold">'.TEXT_ENTER_OTHER_INFO.'</div>
									<div id="options_div" style="padding-top:0.2em; display: block;">';
		
		foreach ($option_html_array as $option_key => $option_res) {
			if ($option_res["data_pool_options_input_type"] == 999) {
				echo '					<table width="100%" cellspacing="2" cellpadding="0">
											<tr><td colspan="2"></td></tr>
											<tr>
												<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
				      						</tr>
				      						<tr>
				      							<td colspan="2">
				      								<div id="custom_bracket_tags_div">';
      			
      			if (!$custom_bracket_tags_shown && count($custom_tags)) {
      				$custom_bracket_tags_html = '		<table width="100%" cellspacing="0" cellpadding="0">';
      				foreach ($custom_tags as $custom_bracket_tags_res) {
      					$custom_bracket_tags_html .= '		<tr>
																<td width="40%" valign="top" class="inputLabel">'.$custom_bracket_tags_res["display_label"].'</td>
																<td valign="top" class="inputField">'.$custom_bracket_tags_res["value"].'</td>
															</tr>';
      				}
      				$custom_bracket_tags_shown = true;
      				$custom_bracket_tags_html .= '		</table>';
      			}
      			echo $custom_bracket_tags_html;
				echo '								</div>
												</td>
											</tr>
											<tr>
												<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
												<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div">'.(isset($total_price_info) && is_array($total_price_info) ? $total_price_info["eta_text"] : '').'</div></td>
											</tr>
											<tr>
				      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
				      						</tr>
				      						<tr><td colspan="2"></td></tr>
										</table>';
			} else {
				$field_resource = tep_draw_option_field($option_key, $option_res, $custom_cart["option"][$option_key]);
                $title = $option_res["data_pool_options_title"];
                if ($option_res["data_pool_options_required"] == 1)	$title .= ' <font color="red" style="padding-left:1px">*</font>';
				echo '					<table width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td width="40%" valign="top" class="inputLabel">'.$title.'</td>
												<td valign="top" class="inputField">'.$field_resource["field"].'</td>
											</tr>
										</table>';
			}
		}
	}
	echo '							</div>
									<script type="text/javascript" language="javascript">' . tep_preload_options_js($products_id) . '</script>' . "\n";
	echo '							<div id="price_info_div" style="padding-top:0.2em;"></div>
									<table border="0" width="100%" cellspacing="0" cellpadding="2">
										';
 	if (tep_not_null($html_framework)) { 
 		//
	} else { 
		echo '							<tr>
											<td valign="top" align="center" class="productSmallText" colspan="2"><font color="#FF0000">'.TEXT_PRODUCT_NOT_AVAILABLE.'</font></td>
										</tr>';
	}
	
	if ($product_info['products_date_available'] > date('Y-m-d H:i:s')) {
		echo '							<tr>
								    		<td align="center" class="productSmallText" colspan="2">'.sprintf(TEXT_DATE_AVAILABLE, tep_date_long($product_info['products_date_available'])).'</td>
										</tr>';
	} else { 
		echo '							<tr>
								    		<td align="center" class="productSmallText" colspan="2">'.sprintf(TEXT_DATE_ADDED, tep_date_long($product_info['products_date_added'])).'</td>
										</tr>';
	}
	
	echo '								<tr>
											<td align="center" class="productSmallText" colspan="2"><br></td>
										</tr>
									</table>
								</td>
			  				</tr>
						</table>
					</td>
					<td width="*%" style="border-left:1px dotted #cccccc;padding:20px 20px;" valign="top">
						<div id="quote_price_div">'.(isset($total_price_info) && is_array($total_price_info) ? ($got_discount ? '<s>'.$total_price_info["price_text"].'</s>&nbsp;&nbsp;'.$currencies->display_price($product_info['products_id'], $total_price_info["price"], $product_tax).(abs($cust_discount_array['individual']) > 0 ? '<span class="requiredInfo">*</span>' : '') : $total_price_info["price_text"]) : '').
							(tep_not_null($_SESSION['RegionGST']['tax_title']) ? '<br /><span style="font-weight: normal;"><a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</a></span>' : '') . '
						</div>
						<div id="quote_op_div">' . TEXT_OP . '</div>' . 
						$extra_buy_button_html . 
				'	</td>
				</tr>
			</table>';
$product_info_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$product_info_content_string,13); 
} else {
	ob_start();
	//echo tep_draw_form('mainForm', 'javascript:add_to_shopping_cart_cp(\''.tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, tep_get_all_get_params(array('action')) . (($isPowerLevelling) ? '' : 'action=buy_now')).'\')' , 'get');
	echo tep_draw_hidden_field('products_id', $product_info['products_id'] , ' id="products_id"');
	if ($product_info['products_bundle'] == 'yes') {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "products_bundle");
		echo tep_draw_hidden_field('products_bundle', 'yes',' id="products_bundle"');
	} else if ($product_info['products_bundle_dynamic'] == 'yes') {	
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "products_bundle_dynamic");
	} else {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "");
	}
	
	$show_it = $status_info['show'];
	$op_popup = 'http://kb.offgamers.com/en/category/my-account/wor-token/';
	
	if ($languages_id == 2 || $languages_id == 3) {
		$op_popup = 'http://kb.offgamers.com/zhcn/category/my-account/wor-token/';
	}
	
	$cust_group_select_sql = "	SELECT customers_groups_name 
								FROM " . TABLE_CUSTOMERS_GROUPS . " 
								WHERE customers_groups_id = '" . tep_db_input($customers_groups_id) . "'";
	$cust_group_result_sql = tep_db_query($cust_group_select_sql);
	$cust_group_row = tep_db_fetch_array($cust_group_result_sql);
	
?>
	<h1><?=$product_info['products_name'].$ogm_fb_obj->get_FB_button('like')?></h1>
	<div class="dotted_line"></div>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="2" height="681px" class="subCategoryBox">
					<tr>
						<td colspan="2" height="15px">
							<img src="images/pixel_trans.gif" border="0" alt="" width="1" height="15">
						</td>
					</tr>
					<tr>
						<td width="40%" align="center" valign="top" style="padding:0px 20px;" height="*%">
							<table border="0" cellspacing="0" cellpadding="0" width="90%" id="td_cart_info">
								<tr>
									<td>
<?
									$pro_img_w = $pro_img_h = '';
									
									$pro_img = $page_info->get_products_info($products_id, 'products_image', $languages_id, $default_languages_id);
									list($pro_img_src, $pro_img_w, $pro_img_h) = $page_info->get_product_dimension($pro_img);
									echo tep_image($pro_img_src, $page_info->get_products_info($products_id, 'products_image_title', $languages_id, $default_languages_id), $pro_img_w, $pro_img_h). '</a>';
									
?>
									</td>
								</tr>
								<tr>
							    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '5')?></td>
								</tr>
									<td class="main"><b><?=$product_info['products_name']?></b></td>
								</tr>
							    <tr>
							        <td class="main"><?= TEXT_SELLING_DELIVERY_TIME . ': ' . (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-')?></td>
							    <tr>
								<tr>
							    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '5')?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '8')?></td>
								</tr>
								<tr>
									<td class="main">
										<b>
											<font color="red">
<?
											if ($new_price = tep_get_products_special_price($products_id)) {
										      	$products_price = '<s>' . $currencies->format($product_info['products_price']) . '</s> <span class="productSpecialPrice">' . $currencies->display_price_nodiscount($products_id, $new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
										    } else {
										    	$products_price = $currencies->display_price($products_id, $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']), 1, ($show_it==2 ? true : false));
										    }
										    echo $products_price;
?>
											</font>
										</b>
										<?php
											# display GST description
											if (tep_not_null($_SESSION['RegionGST']['tax_title'])) {
												echo '<br /><a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</a>';
											}
										?>
									</td>
								</tr>
								<tr>
									<td valign="center" class="main">
										<table border="0" cellspacing="0" cellpadding="0" width="100%">
											<tr>
												<td width="50%">
<?
	if (abs($currencies->rebate_point)) {
?>
													<a href="<?=$op_popup?>" target="_blank" >
														<?=$cust_group_row['customers_groups_name'] . ': ' . $currencies->rebate_point . ' ' . TEXT_OP . ' ' . TEXT_REBATE?>
													</a>
<?
	}
?>
												</td>
												<td class="main" width="*%" style="text-align:center;">
<?
	echo tep_image_button2('green', tep_href_link(FILENAME_DEFAULT, 'cPath='.$cPath.'&pid='.$product_info['products_id']), IMAGE_BUTTON_BUY_NOW, ' id="btn_buynow" ');
?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
						<td width="*%" class="main" align="<?=$product_description_align?>" valign="top" style="border-left:1px dotted #A4A4A4;padding:0px 20px;">
							<?=stripslashes($product_info['products_description'])?>
						</td>
					</tr>
					<tr>
						<td colspan="2" height="15px">
							<img src="images/pixel_trans.gif" border="0" alt="" width="1" height="15">
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<?
$product_info_2_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$product_info_2_content_string,13); 
?>
	<script>
		var loadingIcon = '<?=tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20)?>';
		
		function select_delivery_mode(mid) {
			jQuery("td.delivery_mode_bg .delivery_mode_bg_content").hide();
			jQuery("td.delivery_mode_bg").removeClass("delivery_mode_bg_selected");
			
			jQuery("td#delivery_mode_bg_"+mid).addClass('delivery_mode_bg_selected');
			jQuery("td#delivery_mode_bg_"+mid+" .delivery_mode_bg_content").show();
			
			jQuery(".div_add_cart_button").hide();
			jQuery("div#div_add_cart_button_"+mid).show();
		}
		
		function submit_topup(pid, qty) {
			var submit_flag = true;
			jQuery("tr#tr_top_up_error_msg").hide();
			jQuery('div#div_top_up_error_msg').text('');
			var submit_data = 'action=direct_topup_insert_cart&delivery_mode=6&pid='+pid+'&';
			jQuery('#td_cart_info input, #td_cart_info select, #td_cart_info textarea').each(function() { 
				if (jQuery(this).val()=='') {
					jQuery(this).focus();
					submit_flag = false;
				}
		
				if (jQuery(this).attr('name')=='buyqty') {
					submit_data += 'txt_qty=' + jQuery(this).val() + '&';
				} else {
					submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '&';
				}
		    });
		    
		    if (submit_flag) {
				jQuery.ajax({
					url:'<?=tep_href_link("checkout_xmlhttp.php")?>',
					data:submit_data,
					type: 'POST',
					dataType: 'xml',
					timeout: 60000,
					error: function(){
						
					},
					success: function(xml) {
						if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '' ) {
							jQuery('div#div_top_up_error_msg').text(jQuery(xml).find('error_message').text());
							jQuery("tr#tr_top_up_error_msg").show();
						} else {
							
							jQuery("div.delivery_mode_bg_content input").val('');
							
							var total_item = jQuery(xml).find('total_item').text();
							var product_name = jQuery(xml).find('product_name').text();
							var subtotal = jQuery(xml).find('subtotal').text();
							
							jQuery('#mini_cart_added_item').html(product_name);
							jQuery('#mini_cart_total b').html(subtotal);
							jQuery('.itemQty').html(total_item);
							
							PopUpAddToCart();
							
							clearInterval(minicart_interval);
							
							minicart_interval = setInterval("hide_footer_popup('footer_cart')", 8000);
						}
					}
				});
			}
		}
	</script>
<?
}

// following </form> comment by boon hock
?>
</form>
<?
}
?>
<div class="break_line"></div>