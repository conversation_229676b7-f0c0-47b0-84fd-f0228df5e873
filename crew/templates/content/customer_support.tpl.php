<h1><?=HEADING_TITLE ?></h1> 
<div class="solidThickLine"><!-- --></div>
<div class="break_line"></div>

<?php if ($messageStack->size('customer_support') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div class="breakLine"><!-- --></div>
		<div style="padding-left:5pt;"><?=$messageStack->output('customer_support'); ?></div>
		<div class="breakLine"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } 

	if ($languages_id == 1) {
		$tab2_js_action = "selectTab('tab2content','2','2')";
		$tab3_js_action = "javascript:void(0);";
	} else {
		$tab3_js_action = $tab2_js_action = "window.open('http://webchat.b.qq.com/webchat.htm?sid=2188z8p8p8p8z8p8K8K8z', '_blank', 'height=400, width=500,toolbar=no,scrollbars=no,menubar=no,status=no');";
	}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="0"> 
				<tr>
					<td>
						<?=TEXT_LONG_DESCRIPTION ?>
						<br><br>
						<div>					
							<div class="tab2Header">
								<ul class="tab2">
									<li class="tab2Selected" id="tab2contentitem1"><a href="javascript:selectTab('tab2content','1','2')"><span><font><?=HEADING_SEND_A_MESSAGE ?></font></span></a></li>
									<? if ($chat_live_flag){ ?>
									<li class="tab2NotSelected" id="tab2contentitem2"><a href="javascript:void(0);" onclick="<?=$tab2_js_action?>"><span><font><?=HEADING_CHAT_LIVE ?></font></span></a></li>
									<li class="tab2NotSelected" id="tab3contentitem3"><a href="javascript:void(0);" onclick="<?=$tab3_js_action?>"><span><font><?=HEADING_BUYBACK_CHAT_LIVE ?></font></span></a></li>
									<?} ?>
								</ul>
							</div>
							<div class="tab2Content" id="tab2content1"> 
								<div style="padding: 10px;">		
<?php
	if ($send_message_flag && $languages_id == 1) {
?>
                                    <div><iframe frameborder="0" scrolling="auto" src="http://hosted.comm100.com/EmailTicket/AdminPanel/GetWebInfoToTicket.aspx?webToTicketFormId=87&siteId=145086" width="700px" height="365px" style="background-color:Transparent;" ></iframe></div>
<?php
	} else {
		if ($notice_box_flag) {
			$notice_message_obj = new messageStack;
			$notice_message_obj->add('notice_message', TEMPORARY_CLOSED_DOWN_MESSAGE, 'notice_box');
			if ($notice_message_obj->size('notice_message') > 0) {
?>
										<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); float: left;">
											<div>
												<div class="breakLine"><!-- --></div>
												<div style="display: block; color: black;"><b><?=$notice_message_obj->output('notice_message')?></b></div>
												<div class="breakLine"><!-- --></div>
											</div>
										</div>
										<div class="breakLine" style="height:10px;"><!-- --></div>
<?
			}
		}
?>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td><?=TECHNICAL_DIFFICULTIES ?></td>
											<td>
												<table border="0" cellpadding="2" cellspacing="0" width="100%">
													<tr>
														<td align="left" cellpadding="0" cellspacing="0"> <b><?=SUBMISSION_FORM_REQUEST_LINK_TITLE ?></b></td>
													</tr>
													<tr>
														<td align="left">
															<ul class="bullet">
																<!-- for send a message tab close-->
																<li style="margin-left:-15px;padding-left: 0px;white-space:nowrap"><b><a href="<?=tep_href_link(FILENAME_EVENT, 'news_id=1580')?>"><?=LINK_POWER_LEVELING_ACCOUNT_RESUBMISSION ?></a></li>
																<li style="margin-left:-15px;padding-left: 0px;white-space:nowrap"><b><a href="<?=tep_href_link(FILENAME_EVENT, 'news_id=1533')?>"><?=LINK_GAME_CARD_ISSUES_SUBMISSION ?></a></li>
															</ul>
														</td>
													</tr>
				    							</table>
											</td>
										</tr>
									</table>	
<? 
	}
?>	
								</div>
								</form>
							</div>
								<div class="tab2Content" id="tab2content2" style="display:none">
								<div style="padding: 10px;">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td>
												<?=TECHNICAL_DIFFICULTIES ?>
											</td>
											<td>
												<table border="0" cellpadding="2" cellspacing="0" width="100%">
													<tr>
														<td align="left" cellpadding="0" cellspacing="0"> <b><?=SUBMISSION_FORM_REQUEST_LINK_TITLE ?></b></td>
													</tr>
													<tr>
														<td align="left">
															<ul class="bullet">
																<!-- for buy chat live tab close-->
																<li style="margin-left:-15px;padding-left: 0px;white-space:nowrap"><b><a href="<?=tep_href_link(FILENAME_EVENT, 'news_id=1580')?>"><?=LINK_POWER_LEVELING_ACCOUNT_RESUBMISSION ?></a></li>
																<li style="margin-left:-15px;padding-left: 0px;white-space:nowrap"><b><a href="<?=tep_href_link(FILENAME_EVENT, 'news_id=1533')?>"><?=LINK_GAME_CARD_ISSUES_SUBMISSION ?></a></li>
															</ul>
														</td>
													</tr>
				    							</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
							<? if ($languages_id == 1) { ?> 
							<?php echo sprintf (GOT_A_QUESTION,  tep_href_link('http://kb.offgamers.com/'))?>
						<? } ?>
						</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
<script language="javascript">
selectIssue();
function selectTab(tabname,tabid,totaltab) {
	for (var i = 1 ; i <= totaltab ; i++) {
		jQuery('#' + tabname + i).hide();
		jQuery('#'+ tabname + 'item' + i).attr('class','tab2NotSelected');
	}
	jQuery('#' + tabname + tabid).show();
	jQuery('#'+ tabname + 'item' + tabid).attr('class','tab2Selected');
}

//to display the select issue combo box.
function selectIssue() {
	var issue = jQuery('#issue').val();

	//jQuery('#tab2content1 TR').hide();
	jQuery('#div_issue').show();
	jQuery('#div_buttons').show();
	jQuery('#div_message').show();
	jQuery('#div_email_address').show();
	jQuery('#issue_text_1').hide();
	jQuery('#issue_text_2').hide();

	if (issue == "<?=OPTION_ISSUE_1 ?>") {
		jQuery('#div_order_number').show();
		jQuery('#div_phone_number').show();
		jQuery('#div_game_title').show();
		jQuery('#div_will_be_in_game').show();
		jQuery('#div_in_game_duration').show();
		jQuery('#issue_text_1').show();
	} else if (issue == '<?=OPTION_ISSUE_2 ?>') {
		jQuery('#div_order_number').show();
		jQuery('#div_phone_number').show();
		jQuery('#div_account_name').show();
		jQuery('#div_account_password').show();
		jQuery('#div_character_name').show();
		jQuery('#div_region').show();
		jQuery('#div_faction').show();
		jQuery('#div_server').show();
		jQuery('#issue_text_2').show();
	} else if (issue == "<?=OPTION_ISSUE_3 ?>") {
		jQuery('#div_order_number').show();
		jQuery('#div_error_found').show();
		jQuery('#div_attach_file').show();
	} else if (issue == "<?=OPTION_ISSUE_4 ?>") {
		jQuery('#div_order_number').show();
		jQuery('#div_game_title').show();
		jQuery('#div_account_name').show();
		jQuery('#div_account_password').show();
		jQuery('#div_character_name').show();
		jQuery('#div_realm').show();
		jQuery('#div_faction').show();
		jQuery('#div_class').show();
		jQuery('#div_region').show();
	} else if (issue == "<?=OPTION_ISSUE_5 ?>") {
		jQuery('#div_order_number').show();
		jQuery('#div_product_type').show();
	} else if (issue == "<?=OPTION_ISSUE_6 ?>" || issue == "<?=OPTION_ISSUE_7 ?>") {
		jQuery('#div_order_number').show();
	} else if (issue == "<?=OPTION_ISSUE_8 ?>") {
		jQuery('#div_payment_method').show();
	} else if (issue == "<?=OPTION_ISSUE_9 ?>") {
		jQuery('#div_sell_order_number').show();
	}
}

function validation() {
	var issue = jQuery('#issue').val();
	var error_message = "";

/*if (issue == "<?=OPTION_ISSUE_1 ?>") {
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		}

		if (document.contact_customer_support.phone_number.value == "") {
			error_message += "<?=ERROR_PHONE_NUMBER ?>\n";
		}
	}
	else if (issue == '<?=OPTION_ISSUE_2 ?>') {
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		}	

		if (document.contact_customer_support.phone_number.value == "") {
			error_message += "<?=ERROR_PHONE_NUMBER ?>\n";
		}

		if (document.contact_customer_support.account_name.value == "") {
			error_message += "<?=ERROR_ACCOUNT_NAME ?>\n";
		}

		if (document.contact_customer_support.account_password.value == "") {
			error_message += "<?=ERROR_ACCOUNT_PASSWORD ?>\n";
		}

		if (document.contact_customer_support.character_name.value == "") {
			error_message += "<?=ERROR_CHARACTER_NAME ?>\n";
		}

		if (document.contact_customer_support.region.value == "") {
			error_message += "<?=ERROR_REGION ?>\n";
		}

		if (document.contact_customer_support.faction.value == "") {
			error_message += "<?=ERROR_FACTION ?>\n";
		}

		if (document.contact_customer_support.server.value == "") {
			error_message += "<?=ERROR_SERVER ?>\n";
		}
	}
	else if (issue == "<?=OPTION_ISSUE_3 ?>") {
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER  ?>\n";
		}
	}
	else if (issue == "<?=OPTION_ISSUE_4 ?>") {
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		}
		
		if (document.contact_customer_support.account_name.value == "") {
			error_message += "<?=ERROR_ACCOUNT_NAME ?>\n";
		}

		if (document.contact_customer_support.account_password.value == "") {
			error_message += "<?=ERROR_ACCOUNT_PASSWORD ?>\n";
		}

		if (document.contact_customer_support.character_name.value == "") {
			error_message += "<?=ERROR_CHARACTER_NAME ?>\n";
		}

		if (document.contact_customer_support.realm.value == "") {
			error_message += "<?=ERROR_REALM ?>\n";
		}

		if (document.contact_customer_support.faction.value == "") {
			error_message += "<?=ERROR_FACTION ?>\n";
		}

		if (document.contact_customer_support.class1.value == "") {
			error_message += "<?=ERROR_CLASS ?>\n";
		}

		if (document.contact_customer_support.region.value == "") {
			error_message += "<?=ERROR_REGION ?>\n";
		}
	}
	else*/ if (issue == "<?=OPTION_ISSUE_5 ?>") { 
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		} /*else if (!is_numeric (document.contact_customer_support.order_number.value)) {
			//error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		}*/

		if (document.contact_customer_support.product_type.value == "") {
			error_message += "<?=ERROR_PRODUCT_TYPE ?>\n";
		}
	}
	else if (issue == "<?=OPTION_ISSUE_6 ?>" || issue == "<?=OPTION_ISSUE_7 ?>") {
		if (document.contact_customer_support.order_number.value == "") {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		} /*else if (is_numeric (document.contact_customer_support.order_number.value)) {
			error_message += "<?=ERROR_ORDER_NUMBER ?>\n";
		}*/
	}
	/*else if (issue == "<?=OPTION_ISSUE_8 ?>") {
		if (document.contact_customer_support.payment_method.value == "") {
			error_message += "<?=ERROR_PAYMENT_METHOD ?>\n";
		}
	}*/
	else if (issue == "<?=OPTION_ISSUE_9 ?>") {
		if (document.contact_customer_support.sell_order_number.value == "") {
			error_message += "<?=ERROR_SELL_ORDER_NUMBER ?>\n";
		} /*else if (is_numeric (document.contact_customer_support.sell_order_number.value)) {
			error_message += "<?=ERROR_SELL_ORDER_NUMBER ?>\n";
		}*/
	}

	if (document.contact_customer_support.customers_email_address.value == "") {
		error_message += "<?=ERROR_EMAIL_ADDRESS ?>\n";
	}

	if (document.contact_customer_support.message.value == "") {
		error_message += "<?=ERROR_MESSAGE ?>\n";
	}

	if (error_message) {
		alert (error_message);
		//return false;
	} else {
		document.contact_customer_support.submit();
		//return true;
	}
}

function validation_chat() {
	var customer_email = jQuery('#txt_customer_email').val();
	var error_flag = false;
	var error_message = "";

	if (customer_email=='') {
		error_flag = true;
		error_message = error_message + '<?=ERROR_EMAIL_ADDRESS ?>\n'; 
	}
	
	/*
	if (document.getElementById('orderNumber').value == "") {
		error_flag = true;
		error_message = error_message + '<?=ERROR_ORDER_NUMBER?>\n';
	} else if (!is_numeric(document.getElementById('orderNumber').value)) {
		error_flag = true;
		error_message = error_message + '<?=ERROR_ORDER_NUMBER ?>\n';
	}	
	*/
	if (error_flag) {
		alert(error_message);
		return false;
	} else {
		return true;	
	}
	
	/*if (error_message) {
	alert (error_message);
	}
	else {
		document.contact_customer_support.submit();
	}*/
	
}

function validation_email_form() {
	var customer_email = jQuery('#customer_email').val();
	var customer_email_hidden = jQuery('#customer_email_hidden').val();
	var error_flag = false;
	var error_message = "";
	
	if (customer_email=='') {
		error_flag = true;
		error_message = error_message + '<?=ERROR_EMAIL_ADDRESS ?>\n'; 
	} else if (!IsValidEmail(customer_email || customer_email_hidden)) {
		error_flag = true;
		error_message = error_message + '<?=ERROR_EMAIL_FORMAT ?>\n'; 
	}
	
	if (document.getElementById('subject').value == "") {
		error_flag = true;
		error_message = error_message + '<?=ERROR_SUBJECT?>\n';
	} 
	/*
	if (document.getElementById('productType').value == "") {
		error_flag = true;
		error_message = error_message + '<?=ERROR_PRODUCT_TYPE?>\n';
	} 
	*/
	if (document.getElementById('tellUsUrIssue').value == "") {
		error_flag = true;
		error_message = error_message + '<?=ERROR_TELLUSURISSUE?>\n';
	} 
	/*
	else if (!is_numeric(document.getElementById('orderNumber').value)) {
		error_flag = true;
		error_message = error_message + '<?=ERROR_ORDER_NUMBER ?>\n';
	}	
	*/
	
	if (!checkTotalFileSize()) {
		error_flag = true;
		error_message = error_message + 'Your total attachment file has exceeded 5mb. Please send your attachement in an e-mail <NAME_EMAIL> with the ticket number as your subject. Thank you.\n';
	}
	
	if (error_flag) {
		alert(error_message);
		return false;
	} else {
		return true;	
	}
}

function IsValidEmail(email)
	{
	var filter = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
	return filter.test(email);
	}
</script>
<script>
var needValidate = false;
var defaultVal2 = "Type your question here and click Chat Now.";

function  openchatwindow(cbForm,windowName) {
	var res = true;
	var msg = document.getElementById("lp_msgfield");
	
	if(needValidate){
		var validateMessage = "Before you start your chat, type in the following fields:\r\n ";

		if(msg.value.length==0 || msg.value==defaultVal2 ){
			validateMessage += "Your Question\r\n";
			res=false;
		}
		if(res==false){
			alert(validateMessage);
		}
	}
	
    var formurl = cbForm.action;
    if(formurl.indexOf('?')==-1)
        formurl += '?';
    var elemCol = cbForm.elements;
    for(var ie=0; ie<elemCol.length; ie++){
        var et = elemCol[ie].type;
        if(et=="text" || et=="hidden" || et=="radio" || et=="textarea"){
            formurl += elemCol[ie].name + "=" + escape(elemCol[ie].value) + "&" ;
        }
    }
	
	if(res==true) {
		window.open(formurl, windowName,'width=550,height=420,resizable=yes');
	}
	return res;
}
</script>
<script>
	var submitted = false;
	
	function onSubmit(form_obj) {
		if (!submitted){
			submitted = true;
			form_obj.submit();
			return true;
		}
		return false;
	}
	
	function checkTotalFileSize() {
		if (navigator.appName == "Microsoft Internet Explorer")
		{/*
			var myFSO = new ActiveXObject("Scripting.FileSystemObject");
		    var filepath1 = document.message.attfile1.value;
		    var filepath2 = document.message.attfile2.value;
		    var thefile1 = myFSO.getFile(filepath1);
		    var thefile2 = myFSO.getFile(filepath2);
		    var size1 = thefile1.size;
		    var size2 = thefile1.size;
		    if (size1 + size2 > <?=$max_size;?>) {
				return false;
			//alert('Your total attachment file has exceeded 5mb. Please send your attachement in an e-mail <NAME_EMAIL> with the ticket number as your subject. Thank you.');
			//document.message.reset();
			}
			*/
	 	} else {
			var val1 = document.getElementById('attfile1').files[0] != undefined ? document.getElementById('attfile1').files[0].fileSize : 0;
			var val2 = document.getElementById('attfile2').files[0] != undefined ? document.getElementById('attfile2').files[0].fileSize : 0;
			if (val1+val2 > <?=$max_size;?>) {
		 		return false;
			}
		}
		
		return true;
	}
</script>