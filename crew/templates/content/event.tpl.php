<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=$content_row['events_name']?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table class="newsOutline" border="0" cellpadding="0" cellspacing="0" width="100%">
				<tr>
					<td><?=nl2br($content_row['content'])?></td>
				</tr>
				<tr>
					<td>
						<br><div class="row_separator"></div><br>
					</td>
				</tr>
<? if (tep_not_null(SHARETHIS_PUBLISHER_ID)) { 
	$sharethis_url = tep_get_cur_page_url('affiliate');
?>
				<tr>
					<td>
						<div class="shareThis shareThisButtonwrapper">
							<script language="javascript" type="text/javascript">
								//Use a customized callback routine. For example:
								//Create a sharelet with button element set to false and the custom handler
								var object = SHARETHIS.addEntry({
										title:'<?=tep_db_input(META_TAG_TITLE)?>',
										url: '<?=$sharethis_url?>'
									},
									{button:false});
									//Create customized button and attach it to the share object
									document.write('<span id="share"><a href="javascript:void(0);" class="stbutton" style="padding: 1px 5px 5px 22px; background: transparent url(<?=tep_href_link(DIR_WS_ICONS . 'icon_share_this.png')?>) no-repeat scroll 0 0 !important"><b><?=LINK_SHARE_THIS_PAGE?></b></a></span>');
									var element = document.getElementById("share");
									object.attachButton(element);
							</script>
						</div>
					</td>
				</tr>
<? } ?>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=$page_obj->get_html_simple_rc_box(FORM_TITLE, $form_content,11)?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
</table>