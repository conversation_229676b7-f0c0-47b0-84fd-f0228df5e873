<style type="text/css">
div.loginColumn {
	width:950px;
}

div.loginBoxHeader {
	width:939px;
}

div.loginBoxHeaderCenter {
	width:929px;
}
</style>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>login.js"></script>
<div class="vspacing"></div>

<table cellpadding="0" cellspacing="0" border="0" width="100%">
	
<?
		if ($action == 'show_login') {
?>
	<tr>
		<td colspan="3">
			<h1><?=HEADER_ACCOUNT_LOGIN?></h1>
			<div class="solidThickLine"></div>
			<div class="breakLine"><!-- --></div>
		</td>
	</tr>
	<tr valign="top">
		<td>
			<div class="loginColumn" style="float:left;">
				<div class="loginColumnBorder" id="ezSignUp">
					<div class="loginBoxHeader">
						<div class="loginBoxHeaderLeft"></div>
						<div class="loginBoxHeaderCenter">
							<font style="padding-top: 8px;">
								<h1 style="color:#ffffff"><?=TEXT_RETURNING_CUSTOMER?></h1><br>
							</font>
						</div><!-- loginBoxHeaderCenter -->
						<div class="loginBoxHeaderRight"><!-- --></div>
					</div><!-- loginBoxHeader -->
					<div class="breakLine"><!-- --></div>
					<div class="loginBoxContent">
					<?=tep_draw_form('login', tep_href_link(FILENAME_EXPRESS_LOGIN, tep_get_all_get_params(array('id', 'fb_action', 'action')).'fb_action=process_login', 'SSL'), 'post')?>
							<table border="0" cellspacing="0" cellpadding="0" width="100%">	
<?		if ($messageStack->size('express_login') > 0) { ?>
								<tr>
									<td colspan="2">
										<div><?=$messageStack->output('express_login')?></div>
										<div><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></div>
									</td>
								</tr>
<?		} ?>
								<tr height="25px">
									<td class="ezInputLabel"><?=ENTRY_EMAIL_ADDRESS?></td>
									<td><?=tep_draw_input_field('email_address', '', 'id="entry_email" style="width:200px" size="50" class="ezInputField" onfocus="getFocus(this.id, \'\', \'\')" onblur="getBlur(this.id, \'\', \'\')"')?></td>
								</tr>
								<tr><td style="height: 8px"></td></tr>
								<tr height="25px">
				                    <td class="ezInputLabel"><?=ENTRY_PASSWORD?></td>
				                    <td><?=tep_draw_password_field('password', '', 'id="entry_password" style="width:200px" size="50" class="ezInputField" onfocus="getFocus(this.id, \'\', \'\')" onblur="getBlur(this.id, \'\', \'\')"')?></td>
								</tr>
							</table>
							<div class="breakLine"></div>
							<div class="dottedLine"><!-- --></div>
							<div style="width:455px; float:left">
								<div style="float:left;padding:6px 0px;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0pt 8px;"><!-- --></div>
									<div class="loginBoxLink"><?=sprintf(TEXT_SIGN_IN_TROUBLE, tep_href_link(FILENAME_CUSTOMER_SUPPORT))?></div>
								</div>
								<div style="float:right">
                                    <?=tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_LOGIN, '', 'onclick="document.login.btn_login.click()"')?>
								</div>
							</div><!-- buttonLogin -->
							<?=tep_submit_button('', '', 'style="display:none" name="btn_login"') ?>
						</form>
						<div class="breakLine" style="height:103px"></div>
					</div><!-- loginBoxContent -->
				</div><!-- loginColumnBorder -->
				<div class="breakLine"><!-- --></div>
			</div><!-- loginColumn -->
		</td>
<?
		} else if ($action == 'show_create_new') {
?>
	<script language="javascript">
		function showhide (obj) {
			var obj_name = '#'+obj.id.split("_", 1);
			jQuery(obj_name+'_label').hide();
			jQuery(obj_name+','+obj_name+'_asterisk').show();
		}
	</script>
	<tr>
		<td colspan="3">
			<h1><?=HEADER_CREATE_ACCOUNT?></h1>
			<div class="solidThickLine"></div>
			<div class="breakLine"><!-- --></div>
		</td>
	</tr>
	<tr valign="top">
		<td>
			<div class="loginColumn" style="float:left">
				<div class="loginColumnBorder">
					<div class="loginBoxHeader">
						<div class="loginBoxHeaderLeft"><!-- --></div>
						<div class="loginBoxHeaderCenter">
							<font style="padding-top: 8px;">
								<h1 style="color:#ffffff"><?=TEXT_NEW_CUSTOMER?></h1><br>
							</font>
						</div><!-- loginBoxHeaderCenter -->
						<div class="loginBoxHeaderRight"><!-- --></div>
					</div><!-- loginBoxHeader -->
					<div class="breakLine"><!-- --></div>
					<div style="clear:both" id="ezSignUp">
						<div class="loginBoxContent" align="center">
							<?=tep_draw_form('create_account_form', tep_href_link(FILENAME_CREATE_ACCOUNT, tep_get_all_get_params(array('id', 'fb_action', 'action')).'fb_action=process_create&action=create_account', 'SSL'), 'post', 'onSubmit=""') . tep_draw_hidden_field('action', 'create_account')?>
								<!-- start Easy Sign Up main table -->
								<table border="0" cellspacing="0" cellpadding="0" width="100%">
<?		if ($messageStack->size('express_login') > 0) { ?>
									<tr>
										<td colspan="4">
											<div><?=$messageStack->output('express_login')?></div>
											<div><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></div>
										</td>
									</tr>
<?		} ?>
									<tr height="25px">
										<td class="ezInputLabel" style="text-indent:0.35cm"><?=ENTRY_EMAIL_ADDRESS?></td>
										<td colspan="3">
<?
		if (tep_not_null($fb_user_email)) {
			echo '<label id="email_label">'.$fb_user_email.'&nbsp;&nbsp;(<a id="email_edit_link" href="javascript:void(0);" onclick="showhide(this);">'.BUTTON_EDIT.'</a>)</label>';
			echo tep_draw_input_field('account_email_address', $fb_user_email, 'class="ezInputField" style="width: 215px; display:none;" id="email" onfocus="getFocus(this.id, \'messageEmail\', \''.ENTRY_EMAIL_NOTICE.'\')" onblur="ez_validation(this.id, \'messageEmail\', \''. ENTRY_EMAIL_NOTICE.'\', \''.ENTRY_EMAIL_JS_ERROR.'\')"').'&nbsp;';		
		} else {
			echo tep_draw_input_field('account_email_address', '', 'class="ezInputField" style="width: 215px;" id="email" onfocus="getFocus(this.id, \'messageEmail\', \''.ENTRY_EMAIL_NOTICE.'\')" onblur="ez_validation(this.id, \'messageEmail\', \''. ENTRY_EMAIL_NOTICE.'\', \''.ENTRY_EMAIL_JS_ERROR.'\')"').'&nbsp;';
		}
		echo '&nbsp;<span id="email_asterisk" class="'.(tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? 'requiredInfo' : '').'" style="display:none;">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>';
?>
											<span id="img_messageEmail" style="display: inline-block;"></span>
										</td>
									</tr>
									<tr valign="top" height="30px">
										<td></td>
										<td colspan="3"><span id="messageEmail" style="display: inline-block; width: 350px;"></span></td>
									</tr>
									<tr>
										<td colspan="4" height="5px">&nbsp;</td>
									</tr>
									<tr>
										<td class="dottedLine" colspan="4">&nbsp;</td>
									</tr>
									<tr>
										<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
									</tr>
									<tr height="25px">
										<td class="ezInputLabel"><?=ENTRY_FIRST_NAME?></td>
										<td>
<?
		if (tep_not_null($fb_user_firstname)) {
			echo '<label id="firstname_label">'.$fb_user_firstname.'&nbsp;&nbsp;(<a id="firstname_edit_link" href="javascript:void(0);" onclick="showhide(this);">'.BUTTON_EDIT.'</a>)</label>';
			echo tep_draw_input_field('firstname', $fb_user_firstname, 'class="ezInputField" style="width: 120px; display:none;" id="firstname" onfocus="getFocus(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\', \''.sprintf(ENTRY_FIRSTNAME_JS_ERROR, ENTRY_FIRST_NAME_MIN_LENGTH).'\')"');
		} else {
			echo tep_draw_input_field('firstname', $fb_user_firstname, 'class="ezInputField" style="width: 120px" id="firstname" onfocus="getFocus(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\', \''.sprintf(ENTRY_FIRSTNAME_JS_ERROR, ENTRY_FIRST_NAME_MIN_LENGTH).'\')"');
		}
		echo '&nbsp;<span id="firstname_asterisk" class="'.(tep_not_null(ENTRY_FIRST_NAME_TEXT) ? 'requiredInfo' : '').'" style="display:none;">' . ENTRY_FIRST_NAME_TEXT . '</span>';
?>
											<span id="img_messageFName" style="display: inline-block;"></span>
										</td>
										<td class="ezInputLabel" style="width: 75px;text-indent: 0px"><?=ENTRY_LAST_NAME?></td>
										<td>
<?
		if (tep_not_null($fb_user_lastname)) {
			echo '<label id="lastname_label">'.$fb_user_lastname.'&nbsp;&nbsp;(<a id="lastname_edit_link" href="javascript:void(0);" onclick="showhide(this);">'.BUTTON_EDIT.'</a>)</label>';
			echo tep_draw_input_field('lastname', $fb_user_lastname, 'class="ezInputField" style="width: 120px; display:none;" id="lastname" onfocus="getFocus(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\', \''.sprintf(ENTRY_LASTNAME_JS_ERROR, ENTRY_LAST_NAME_MIN_LENGTH).'\')"');
		} else {
			echo tep_draw_input_field('lastname', $fb_user_lastname, 'class="ezInputField" style="width: 120px" id="lastname" onfocus="getFocus(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\', \''.sprintf(ENTRY_LASTNAME_JS_ERROR, ENTRY_LAST_NAME_MIN_LENGTH).'\')"');
		}
		echo '&nbsp;<span id="lastname_asterisk" class="'.(tep_not_null(ENTRY_LAST_NAME_TEXT) ? 'requiredInfo' : '').'" style="display:none;">' . ENTRY_LAST_NAME_TEXT . '</span>';
?>
											<span id="img_messageLName" style="display: inline-block;"></span>
										</td>
									</tr>
									<tr valign="top" height="30px">
										<td></td>
										<td><span id="messageFName" style="display: inline-block; width: 155px;"></span></td>
										<td></td>
										<td><span id="messageLName" style="display: inline-block; width: 145px;"></span></td>
									</tr>
									<tr>
										<td class="dottedLine" colspan="4" style="line-height:5px;">&nbsp;</td>
									</tr>
									<tr><td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '100%', '15'); ?>
                                                                                    	<div id="captcha" style="padding-left: 135px;display: none">
                        	<div class="formRow">
<?

if (SIGN_UP_CAPTCHA_FIELD == 1) {
	echo '
            <script type="text/javascript">
                    jQuery(document).ready(function() {
                            jQuery("#captcha").show();
                            jQuery("#login_form").attr(\'onsubmit\', \'\');
                    });
            </script>';
	recaptcha :: login_captcha_html();
}
?>
	</div>
            <div class="clrFx"></div>
            <div class="vspacing"></div>
	</div>
                                                                                    </td></tr>
<?
									if (count($newsletter_group_array)) {
										foreach ($newsletter_group_array as $news_grp_id => $news_grp_title) {
?>
									<tr>
										
										<td colspan="4">
											<span><?=tep_draw_checkbox_field('newsletter[]', $news_grp_id, false, 'id="ng_'.$news_grp_id.'" style="boder: #2B5C88"')?></span>
											<span class="ezNewsletter" style="color: #303030;"><label for="<?='ng_' . $news_grp_id?>"><?=$news_grp_title?></label></span>
										</td>
										
									</tr>
<?
										}
									}
?>
									<tr>
										<td colspan="4">
											<?=tep_draw_checkbox_field('agreed','1',false,' id="agreed" onblur="ez_validation(this.id, \'messageAgreed\', \'\', \''.ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR.'\')" ') .'&nbsp; '. TEXT_I_HAVE_READ_AND_AGREE . TEXT_TERMS_AND_POLICY ?>
											<span id="img_messageAgreed" style="display: inline-block;"></span>
										</td>
									</tr>
									<tr valign="top" height="15px">
										<td colspan="4"><!-- --></td>
									</tr>
									<tr>
										<td class="dottedLine" colspan="4" style="line-height:5px;">&nbsp;</td>
									</tr>
									<tr>
										<td colspan="4" style="padding: 0.2cm 0cm 0.2cm 0cm">
                                            <div class="rfloat"><?=tep_image_button2('green', 'javascript:void(0);', BUTTON_SUBMIT, '', 'onclick="document.create_account_form.submit()"')?></div>
                                        </td>
									</tr>
								</table>
								<!-- end Easy Sign Up main table -->
															
								<!--div class="breakLine"></div-->
							</form>
						</div><!-- ezSignUp -->
					</div><!-- loginBoxContent -->				
				</div><!-- loginColumnBorder -->
			</div><!-- loginColumn -->
		</td>
<?
		}
?>
	</tr>
</table>
<div style="clear:both; padding:0.25cm"></div>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>easy_signup.js"></script>
<script language="javascript">
<!--
	var form = '';
	var error_message = '';
	var submitted = false;
	var error = false;
	var check_isNaN = false;
	
	function check_input(field_name, field_size, message, check_isNaN) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = trim_str(form.elements[field_name].value);
		
			if ((field_value == '' || field_value.length < field_size) || (check_isNaN == true && validateInteger(trim_str(field_value)) == true)) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_checkbox(field_name, message) {
		var isChecked = false;
	
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var chkbox = form.elements[field_name];
		
			if (chkbox.checked == true) {
				isChecked = true;
			}
		}
		
		if (isChecked == false) {
			error_message = error_message + "* " + message + "\n";
			error = true;
		}
	}
	
	function check_radio(field_name, message) {
		var isChecked = false;
	
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var radio = form.elements[field_name];
		
			for (var i=0; i<radio.length; i++) {
				if (radio[i].checked == true) {
					isChecked = true;
					break;
				}
			}
		
			if (isChecked == false) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_select(field_name, field_default, message) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = form.elements[field_name].value;
	
			if (field_value == field_default) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_password(field_name_1, field_name_2, field_size, message_1, message_2) {
		if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
			var password = form.elements[field_name_1].value;
			var password_confirmation = form.elements[field_name_2].value;
	
			if (password == '' || password.length < field_size) {
				error_message = error_message + "* " + message_1 + "\n";
				error = true;
			} else if (password != password_confirmation) {
				error_message = error_message + "* " + message_2 + "\n";
				error = true;
			}
		}
	}
	
	function check_password_new(field_name_1, field_name_2, field_name_3, field_size, message_1, message_2, message_3) {
		if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
			var password_current = form.elements[field_name_1].value;
			var password_new = form.elements[field_name_2].value;
			var password_confirmation = form.elements[field_name_3].value;
			
			if (password_current == '' || password_current.length < field_size) {
				error_message = error_message + "* " + message_1 + "\n";
				error = true;
			} else if (password_new == '' || password_new.length < field_size) {
				error_message = error_message + "* " + message_2 + "\n";
				error = true;
			} else if (password_new != password_confirmation) {
				error_message = error_message + "* " + message_3 + "\n";
				error = true;
			}
		}
	}
	
	function check_form(form_name) {
		if (submitted == true) {
			alert("<?php echo JS_ERROR_SUBMITTED; ?>");
			return false;
		}
		
		error = false;
		form = eval('document.'+form_name);
		
		error_message = "<?php echo JS_ERROR; ?>";
		var emailaddr = form.elements["account_email_address"].value;
		var contactno = form.elements["contactnumber"].value;
		
		if (validateEmail(emailaddr) != true) {
			error_message = error_message + "* " + "<?php echo ENTRY_EMAIL_JS_ERROR; ?>" + "\n";
			error = true;		
		}
		
		check_input("firstname", <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_FIRST_NAME_ERROR; ?>", true);
		check_input("lastname", <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_LAST_NAME_ERROR; ?>", true);
		check_select("country", "", "<?php echo ENTRY_COUNTRY_ERROR; ?>");
		
		if (trim_str(contactno).length > 0 && isNaN(trim_str(contactno))) {
			error_message = error_message + "* " + "<?php echo ENTRY_CONTACT_NUMBER_JS_ERROR; ?>" + "\n";
			error = true;
		} else if (isNaN(trim_str(contactno)) == false) {
			check_input("contactnumber", <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>, "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>");
		}
		
		check_checkbox("agreed", "<?php echo ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR; ?>");
		
		if (error == true) {
			alert(error_message);
			return false;
		} else {
			submitted = true;
			return true;
		}
	}

	function ez_validation(elObjID, msgObjID, noticemsg, errormsg) {
		var elObj = document.getElementById(elObjID);
		var msgObj = document.getElementById(msgObjID);
	
		var img_cross = '<img src="images/icons/error.gif" width="12px" height="12px" style="padding-left:1px;"> ';
		var js_error = false;
		
		getBlur(elObjID, msgObjID, noticemsg);
		
		switch (elObjID) {
			case 'email':
				if (trim_str(elObj.value).length > 0 && validateEmail(elObj.value) != true) {js_error = true;}
				break;
				
			case 'passwd':
				if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?> )) {js_error = true;} 
				break;
	
			case 'password_confirmation':
				if (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) != trim_str(document.getElementById('passwd').value) || trim_str(elObj.value).length != trim_str(document.getElementById('passwd').value).length) {js_error = true;} 
				else if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>)) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_PASSWORD_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH); ?>";} 
				break;
			
			case 'firstname':
				if (elObj.value.toLowerCase() == 'null' || 
					(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) )
					) {js_error = true;} 
				break;
						
			case 'lastname':
				if (elObj.value.toLowerCase() == 'null' || 
					(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) ) 
					) {js_error = true;} 
				break;
	
			case 'country':
				if (elObj.value == null || trim_str(elObj.value) == '') {js_error = true;} 
				break;
				
			case 'contactnumber':
				if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && isNaN(elObj.value))) {js_error = true;} 
				else if (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>";}
				break;
				
			case 'day':
				if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('month').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0 ) && trim_str(document.getElementById('month').value).length > 0 ) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ))
				{js_error = true;}
				break;
				
			case 'month':
				if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
				{js_error = true;}
				break;
	
			case 'year':
				if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('month').value).length == 0 )) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('month').value).length > 0 ) ||
					((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
				{js_error = true;}
				break;
				
			case 'billingaddress1':
				if ((elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>)) &&
					(trim_str(document.getElementById('billingaddress2').value).length > 0  || trim_str(document.getElementById('billingpostcode').value).length > 0  || 
					 trim_str(document.getElementById('billingcountry').value).length > 0 || trim_str(document.getElementById('state').value).length > 0 ))  {js_error = true;} 
				break;
				
			case 'billingaddress2':
				if (trim_str(document.getElementById('billingaddress2').value).length != 0 && trim_str(document.getElementById('billingaddress2').value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?> ) {js_error = true;} 
				break;
	
			case 'billingcity':
				if (trim_str(document.getElementById('billingcity').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) )) 
				{js_error = true;} 
				break;
							
			case 'billingpostcode':
				if (trim_str(document.getElementById('billingaddress1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) )) 
				{js_error = true;} 
				break;
						
			case 'billingcountry':
				if (trim_str(document.getElementById('billingaddress1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) == '')) {js_error = true;} 
				break;
	
			case 'state':
				if ((trim_str(document.getElementById('billingaddress1').value).length > 0 && trim_str(document.getElementById('billingcountry').value).length > 0) && 
					(elObj.type == 'text' && (trim_str(elObj.value).toLowerCase() == 'null' || 
					(trim_str(elObj.value).length > 0 && (isNaN(elObj.value) == false || trim_str(elObj.value).length < <?php echo ENTRY_STATE_MIN_LENGTH; ?>))))) {js_error = true;}
				break;
			
			case 'agreed':
				if (elObj.checked == '') {js_error = true;}
				elObj.className = "";
				break;
				
			default:
				break;
		}
		
		if (js_error == true) {
			signup_form_error = true;
			msgObj.style.color="red";
			msgObj.innerHTML = img_cross + errormsg;
		} else {
			msgObj.innerHTML = '';
		}
	}

<?	if (!isset($_REQUEST['fb_action'])) { ?>
	window.onload = function() {
		jQuery('#email').val('');
		jQuery('#passwd').val('');
	}
<?	} ?>
//-->
</script>