<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"></div>

<?php if ($messageStack->size('facebook_connect') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div style="padding:10px;"><?=$messageStack->output('facebook_connect'); ?></div>
		<div class="breakLine"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php 
} 
ob_start();
?>

	<table border="0" width="100%" cellspacing="10" cellpadding="0">
		<tr>
	    	<td>
				<div>
					<?=TEXT_CONTENT?>
				</div>
				<div class="breakLine" style="height:20px"><!-- --></div>
				
				<div>
					<?=TEXT_SUBCONTENT?>
				</div>
				<div class="dottedLine">&nbsp;</div>
<?
		if (isset($_SESSION['fb_uid'])) {
			echo sprintf(TEXT_FB_CONNECTED, $ogm_fb_obj->get_FB_picture('square'), $ogm_fb_obj->get_FBML_tag('name'));
			echo '<br><br>' . $ogm_fb_obj->get_FB_button('disconnect', LABEL_FB_DISCONNECT);
		} elseif ($ogm_fb_obj->get_FB_user_id() !== FALSE) {
			echo $ogm_fb_obj->get_FB_button('disconnect', LABEL_FB_DISCONNECT);
		} else {
			echo $ogm_fb_obj->get_FB_button('login_with_facebook');
		}
?>
			</td>
		</tr>
	</table>
<?
	$facebook_connect_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$facebook_connect_content_string,13); 
?>