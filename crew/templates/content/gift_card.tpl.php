<?
$counter = 0;
$account_history_info_link_extend = '';
$enabled_tab1_link = FALSE;
$enabled_tab2_link = FALSE;

ob_start();
    if ($messageStack->size('gift_card') > 0) {
?>
    <div class="loginColumnBorder" style="background-color: #FFFDDB">
        <div class="breakLine"><!-- --></div>
        <div style="padding-left:5pt;"><?=$messageStack->output('gift_card'); ?></div>
        <div class="breakLine"><!-- --></div>
    </div>
    <div class="vspacing"></div>
<?
    }
$response_message = ob_get_contents();
ob_end_clean();

ob_start();
?>
<div id="c_pwgc" class="tab_row2">
    <div class="lfloat">
<?
    if (isset($_SESSION['gift_card_pwgc_success'])) {
        $enabled_tab1_link = TRUE;
        $sc_message = '';
        $order_id = $_SESSION['gift_card_pwgc_success']['orders_id'];
        
        if (tep_session_is_registered('customer_id')) {
            $account_history_info_link_extend = 'order_id='.$order_id;
            
            $customer_sc_array = store_credit::get_current_credits_balance($customer_id);
            if (isset($customer_sc_array['sc_currency_id'])) {
                $sc_currency_code = $currencies->get_code_by_id($customer_sc_array['sc_currency_id']);
                $sc_balance = $currencies->format( ($customer_sc_array['sc_reverse']+$customer_sc_array['sc_irreverse']) - ($customer_sc_array['sc_reverse_reserve_amt']+$customer_sc_array['sc_irreverse_reserve_amt']), true, $sc_currency_code, 1);
                $sc_message = '<span class="hd5" style="font-size:14px">' . sprintf(SUCCESS_CREDIT_BALANCE, $sc_balance) . '</span>';

                if ($_SESSION['gift_card_pwgc_success']['with_store_credit']) {
                    $sc_message .= '<br>' . SUCCESS_CREDIT_BALANCE_DESC;
                }
            }
        }
?>
        <table class="pwgc_success_tbl" border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td class="lbox alignR">
                    <span class="hd1" style="font-size:18px"><?=SUCCESS_PURCHASE_WITH_GIFT_CARD?></span>
                    <br>
                    <span class="hd5" style="font-size:14px"><?=sprintf(SUCCESS_PURCHASE_WITH_GIFT_CARD_ORDER_INFO, $order_id)?></span>
                </td>
                <td class="rbox" rowspan="4">
                    <?=tep_image_button2('gray_big_tall',tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, $account_history_info_link_extend, 'SSL'), BUTTON_VIEW_ORDER, 180) ?>
                </td>
            </tr>
            <tr><td><div class="vspacing" style="height: 30px;"></div></td></tr>
<?
        if (tep_not_empty($sc_message)) {
?>
            <tr><td class="alignR"><?=$sc_message?></td></tr>
            <tr><td><div class="vspacing" style="height: 30px;"></div></td></tr>
<?
        }
?>
            <tr>
                <td class="lbox alignR">
                    <a href="<?=tep_href_link(FILENAME_GIFT_CARD, 'tab=1', 'SSL')?>"><?=IMAGE_BUTTON_CONTINUE_SHOPPING?></a>
                    &nbsp;|&nbsp;
                    <a href="<?=tep_href_link(FILENAME_REDEEM_POINT, '', 'SSL')?>"><?=BUTTON_CHECK_OP_BALANCE?></a>
                    &nbsp;|&nbsp;
                    <a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=TITLE_MY_PROFILE?></a>
                </td>
            </tr>
        </table>
        <div class="vspacing"></div>
        <div class="dotborder"></div>
        <div class="vspacing"></div>
<?
        unset($_SESSION['gift_card_pwgc_success']);
    } else {
        $step3_etype = 0;
        $step3_email = '';
        $step3_fname = '';
        $step3_lname = '';
        $step3_error_msg = '';
        $step3_tbody_class = '';
        
        if ($tab == 1) {
            $step3_etype = $email_acc_type;
//            $step3_email = $email_address;
//            $step3_fname = $fname;
//            $step3_lname = $lname;
            
            if (count($step3_error_array) > 0) {
                $step3_tbody_class = ' highlight';
                $step3_error_msg = '<span class="gc_fail">' . implode('<br>', $step3_error_array) . '</span>';
            }
            
            echo $response_message;
        }
        
        echo tep_draw_form('gcform', tep_href_link(FILENAME_GIFT_CARD, tep_get_all_get_params(array('tab','param1','param2')) . 'tab=1', 'SSL'), 'post', 'id="pwgc_form"') . tep_draw_hidden_field('etype', '', 'class="etype"');
?>
        <div class="gc_hd"><span class="hd5"><?=HEADING_STEP_1_TITLE?></span></div>
        <div class="vspacing"></div>
        <div>
            <table style="width:100%" cellpadding="8" cellspacing="0">
                <thead>
                <tr>
                    <td width="150px"><span class="hdsC5"><?=TEXT_GC_QUANTITY?>:</span></td>
                    <td width="300px">
<?
        $ttl_set = count($filtered_arr['t1']['gc']);
        foreach (array(1,2,3) as $idx) {
            $counter += 1;
            $sel = $idx == $ttl_set ? true : false;
            echo tep_draw_radio_field('qty', $idx, $sel, 'id="q'.$counter.'" onclick="gc_qty(this)" style="margin: 0"') . '<label class="gc_qty_lbl" for="q'.$counter.'">'.$idx.TEXT_GC_PC.($idx>1?TEXT_GC_MANAY_PC:'').'</label>';
        }
?>
                    </td>
                    <td></td>
                </tr>
                </thead>
<?
        foreach ($filtered_arr['t1']['gc'] as $idx => $data) {
            $counter += 1;
            $sno = $data['sno'];
            $pin = $data['pin'];
            $tbody_class = isset($data['tbody_class']) ? $data['tbody_class'] : '';
            $msg_class = isset($data['msg_class']) ? $data['msg_class'] : '';
            $data_val = isset($data['data-val']) ? $data['data-val'] : '';
            $data_cur = isset($data['data-cur']) ? $data['data-cur'] : '';
            $msg = isset($data['msg']) ? $data['msg'] : '';
?>
                <tbody id="<?=$counter?>" class="<?=$tbody_class?>">
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_SERIAL_NO?>:</span></td>
                        <td><div class="ihd1"><?=tep_draw_input_field('sno[]', $sno, 'id="sno_'.$counter.'" style="width:290px;background-color: #FFF;" size="50" class="ezInputField" onblur="gc_verify(this.id)" autocomplete="off"')?></div></td>
                        <td rowspan="2" valign="top"><span class="gc_info<?=$msg_class?>" id="msg_<?=$counter?>" data-val="<?=$data_val?>" data-cur="<?=$data_cur?>"><?=$msg?></span></td>
                    </tr>
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_PIN_CODE?>:</span></td>
                        <td><div class="ihd1"><?=tep_draw_password_field('pcode[]', $pin, 'id="pcode_'.$counter.'" style="width:290px;background-color: #FFF;" size="50" class="ezInputField" onblur="gc_verify(this.id)" autocomplete="off"')?></div></td>
                    </tr>
                </tbody>
<?
        }
?>
                <tfoot>
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_TOTAL_VALUE?>:</span></td>
                        <td><span id="pwgc_ttlv" class="lbl_ttl hd3" data-default="0.00" data-val="<?=$filtered_arr['t1']['ttl_val']?>" data-cur="<?=$filtered_arr['t1']['ttl_cur']?>"><?=$filtered_arr['t1']['ttl']?></span></td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <div class="vspacing"></div>
        <div class="gc_hd"><span class="hd5"><?=HEADING_PWGC_STEP_2_TITLE?></span></div>
        <div class="vspacing"></div>
        <div>
            <table style="width:100%" cellpadding="8" cellspacing="0">
                <tr>
                    <td width="150px"><span class="hdsC5"><?=TEXT_GAME?>:</span></td>
                    <td width="300px">
                        <div class="shd1">
<?
        echo tep_draw_pull_down_menu('game_id', $game_list_array, $game_id, 'id="game_list" style="width:305px;" onfocus="load_game_list(this)" onchange="load_game_details(this)" class="'.(count($game_list_array)>1?'game_loaded':'').'"'); 
?>
                        </div>
                    </td>
                    <td valign="top"><span id="msg_game_list"></span></td>
                </tr>
                <tbody id="tby_game_list" style="<?=(count($product_list_array)>1?'':'display:none;')?>">
                    <tr>
                        <td><?=TEXT_PRODUCT?>:</td>
                        <td>
                            <div class="shd1"><?=tep_draw_pull_down_menu('product_id', $product_list_array, $product_id, 'id="gtype" style="width:305px;" onchange="calc_amount()"')?></div>
                        </td>
                        <td valign="top"><span id="msg_gtype"></span></td>
                    </tr>
                    <tr id="tr_qty">
                        <td><?=ENTRY_QTY?>:</td>
                        <td><div class="shd1"><?=tep_draw_pull_down_menu('game_qty', $qty_array, $game_qty, 'id="gqty" style="width:150px;" onchange="calc_amount()"')?></div></td>
                        <td></td>
                    </tr>
                </tbody>
                <tr>
                    <td><?=TEXT_AMOUNT?>:</td>
                    <td><span id="msg_amount"><?=$pwgc_amount?></span></td>
                    <td></td>
                </tr>
                <tbody id="tby2_game_list" style="<?=(count($dm_list_array)>1?'':'display:none;')?>">
                <tr>
                    <td><?=ENTRY_DELIVERY_METHOD?>:</td>
                    <td><div class="shd1"><?=tep_draw_pull_down_menu('game_dm', $dm_list_array, $game_dm, 'id="gdm" style="width:305px;" onchange="load_dm(this)"')?></div></td>
                    <td></td>
                </tr>
                </tbody>
                <tbody id="dm_info_list">
<?
        foreach ($dm_info_array as $field) {
            echo "<tr><td></td><td><div class='shd1 ihd1'>" . $field . "</div></td><td></td></tr>";
        }
?>
                </tbody>
            </table>
        </div>
<?
        if (!isset($_SESSION['customer_id'])) {
            
?>
        <div class="vspacing"></div>
        <div class="gc_hd"><span class="hd5"><?=HEADING_PWGC_STEP_3_TITLE?></span></div>
        <div class="vspacing"></div>
        <div>
            <table style="width:100%" cellpadding="8" cellspacing="0">
                <tr>
                    <td width="150px"></td>
                    <td>
                        <div class="lfloat" style="padding:0 10px"><?=tep_image_button2('gray_short', 'javascript:void(0);', TEXT_IS_EXISTING_CUSTOMER, '200px', 'onclick="chk_form(\'pwgc_form\', 0)"')?></div>
                        <div class="lfloat" style="padding:0 10px"><?=tep_image_button2('gray_short', 'javascript:void(0);', TEXT_IS_NEW_CUSTOMER, '200px', 'onclick="chk_form(\'pwgc_form\', 1)"')?></div>
                        <div class="clrFx"></div>
                    </td>
            </table>
        </div>
<?
        }
?>
        <div class="vspacing"></div>
        <div class="dotborder">
<?
        if (isset($_SESSION['customer_id'])) {
?>
            <div class="vspacing"></div>
            <div class="rfloat"><?=tep_image_button2('red', 'javascript:void(0);', IMAGE_BUTTON_CHECKOUT, '', 'onclick="chk_form(\'pwgc_form\', 0)"')?></div>
            <div class="clrFx"></div>
<?
        }
?>
        </div>
    </form>
<?
    }
?>
    </div>
    <div class="rfloat">
        <div>
            <span class="hds3" style="line-height:20px"><?=TEXT_GC_FAQ_AWARD_TITLE?></span>
            <a title="OffGamers 2011 Top10 Reviews Gold Award" href="http://game-card-review.toptenreviews.com/offgamers-review.html" target="_blank">
                <img style="border:0 none" width="112" height="90" title="OffGamers 2011 Top10 Reviews Gold Award" alt="OffGamers 2011 Top10 Reviews Gold Award" src="http://image.offgamers.com/img/footer/award2013.gif">
            </a>
        </div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H1?></span>
        <div><?=TEXT_GC_FAQ_C1?></div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H2?></span>
        <div><?=TEXT_GC_FAQ_C2?></div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H3?></span>
        <div><?=TEXT_GC_FAQ_C3?></div>
    </div>
    <div class="clrFx"></div>
</div>

<?
$purchase_gc_tab = ob_get_contents();
ob_end_clean();

ob_start();
?>
<div id="c_rgc" class="tab_row2">
    <div class="lfloat">
<?
    if (isset($_SESSION['gift_card_rgc_success'])) {
        $enabled_tab2_link = TRUE;
?>
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="4%"></td>
                <td align="right" width="9%"><?=tep_image(DIR_WS_IMAGES . 'successful.gif', '', 54, 56)?>&nbsp;</td>
                <td><h1><?=SUCCESS_REDEEMED?></h1></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td><?=sprintf(SUCCESS_CREDITED, $_SESSION['gift_card_rgc_success']['total_amount'])?></td>
            </tr>
            <tr>
                <td colspan="3"><div class="vspacing" style="height: 40px;"></div></td>
            </tr>
            <tr>
                <td></td>
                <td colspan="2">
                    <?=tep_image_button2('gray_short',tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT, '', 'SSL'), BUTTON_VIEW_SC_BALANCE, '', 'style="float:right"') ?>
                    &nbsp;
                    <?=tep_image_button2('gray_short',tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'), BUTTON_BROWSE_MORE_GAMES, '', 'style="float:right"') ?>
                </td>
            </tr>
        </table>
        <div class="vspacing"></div>
        <div class="dotborder"></div>
        <div class="vspacing"></div>
<?
        unset($_SESSION['gift_card_rgc_success']);
    } else {
        $step3_etype = 0;
        $step3_email = '';
        $step3_fname = '';
        $step3_lname = '';
        $step3_tbody_class = '';
        $step3_error_msg = '';
        
        if ($tab == 2) {
            $step3_etype = $email_acc_type;
            $step3_email = $email_address;
            $step3_fname = $fname;
            $step3_lname = $lname;
            
            if (count($step3_error_array) > 0) {
                $step3_tbody_class = ' highlight';
                $step3_error_msg = '<span class="gc_fail">' . implode('<br>', $step3_error_array) . '</span>';
            }
            
            echo $response_message;
        }
        
        echo tep_draw_form('gcform', tep_href_link(FILENAME_GIFT_CARD, tep_get_all_get_params(array('tab','status','amt')) . 'tab=2', 'SSL'), 'post', 'id="rgc_form"');
?>
        <div class="gc_hd"><span class="hd5"><?=HEADING_STEP_1_TITLE?></span></div>
        <div class="vspacing"></div>
        <div>
            <table style="width:100%" cellpadding="8" cellspacing="0">
                <thead>
                <tr>
                    <td width="150px"><span class="hdsC5"><?=TEXT_GC_QUANTITY?>:</span></td>
                    <td width="300px">
<?
        $counter += 10; 
        $ttl_set = count($filtered_arr['t2']['gc']);
        foreach (array(1,2,3) as $idx) {
            $counter += 1;
            $sel = $idx == $ttl_set ? true : false;
            echo tep_draw_radio_field('qty', $idx, $sel, 'id="q'.$counter.'" onclick="gc_qty(this)" style="margin: 0"') . '<label class="gc_qty_lbl" for="q'.$counter.'">'.$idx.TEXT_GC_PC.($idx>1?TEXT_GC_MANAY_PC:'').'</label>';
        }
?>
                    </td>
                    <td></td>
                </tr>
                </thead>
<?
        foreach ($filtered_arr['t2']['gc'] as $idx => $data) {
            $counter += 1;
            $sno = $data['sno'];
            $pin = $data['pin'];
            $tbody_class = isset($data['tbody_class']) ? $data['tbody_class'] : '';
            $msg_class = isset($data['msg_class']) ? $data['msg_class'] : '';
            $data_val = isset($data['data-val']) ? $data['data-val'] : '';
            $data_cur = isset($data['data-cur']) ? $data['data-cur'] : '';
            $msg = isset($data['msg']) ? $data['msg'] : '';
?>
                <tbody id="<?=$counter?>" class="<?=$tbody_class?>">
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_SERIAL_NO?>:</span></td>
                        <td><div class="ihd1"><?=tep_draw_input_field('sno[]', $sno, 'id="sno_'.$counter.'" style="width:290px;background-color: #FFF;" size="50" class="ezInputField" onblur="gc_verify(this.id)" autocomplete="off"')?></div></td>
                        <td rowspan="2" valign="top"><span class="gc_info<?=$msg_class?>" id="msg_<?=$counter?>" data-val="<?=$data_val?>" data-cur="<?=$data_cur?>"><?=$msg?></span></td>
                    </tr>
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_PIN_CODE?>:</span></td>
                        <td><div class="ihd1"><?=tep_draw_password_field('pcode[]', $pin, 'id="pcode_'.$counter.'" style="width:290px;background-color: #FFF;" size="50" class="ezInputField" onblur="gc_verify(this.id)" autocomplete="off"')?></div></td>
                    </tr>
                </tbody>
<?
        }
?>
                <tfoot>
                    <tr>
                        <td><span class="hdsC5"><?=TEXT_TOTAL_VALUE?>:</span></td>
                        <td><span class="lbl_ttl hd3" data-default="0.00" data-val="<?=$filtered_arr['t2']['ttl_val']?>" data-cur="<?=$filtered_arr['t2']['ttl_cur']?>"><?=$filtered_arr['t2']['ttl']?></span></td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
<?php
        if (!isset($_SESSION['customer_id'])) {
?>
        <div class="vspacing"></div>
        <div class="gc_hd"><span class="hd5"><?=HEADING_STEP_2_TITLE?></span></div>
        <div class="vspacing"></div>
        <div>
            <table style="width:100%" cellpadding="8" cellspacing="0">
                <tr>
                    <td width="150px"></td>
                    <td>
                        <div class="lfloat" style="padding:0 10px"><?=tep_image_button2('gray_short', 'javascript:void(0);', TEXT_IS_EXISTING_CUSTOMER, '200px', 'onclick="chk_form(\'rgc_form\', 0)"')?></div>
                        <div class="lfloat" style="padding:0 10px"><?=tep_image_button2('gray_short', 'javascript:void(0);', TEXT_IS_NEW_CUSTOMER, '200px', 'onclick="chk_form(\'rgc_form\', 1)"')?></div>
                        <div class="clrFx"></div>
                    </td>
            </table>
        </div>
<?
        }
?>
        <div class="vspacing"></div>
        <div class="dotborder">
<?
        if (isset($_SESSION['customer_id'])) {
?>
            <div class="vspacing"></div>
            <div class="rfloat"><?=tep_image_button2('green', 'javascript:void(0);', BUTTON_REDEEM, '', 'onclick="chk_form(\'rgc_form\', 0)"')?></div>
            <div class="clrFx"></div>
<?
        }
?>
        </div>
    </form>
<?
    }
?>
    </div>
    <div class="rfloat">
        <div>
            <span class="hds3" style="line-height:20px"><?=TEXT_GC_FAQ_AWARD_TITLE?></span>
            <a title="OffGamers 2011 Top10 Reviews Gold Award" href="http://game-card-review.toptenreviews.com/offgamers-review.html" target="_blank">
                <img style="border:0 none" width="112" height="90" title="OffGamers 2011 Top10 Reviews Gold Award" alt="OffGamers 2011 Top10 Reviews Gold Award" src="http://image.offgamers.com/img/footer/award2013.gif">
            </a>
        </div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H1?></span>
        <div><?=TEXT_GC_FAQ_C1?></div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H2?></span>
        <div><?=TEXT_GC_FAQ_C2?></div>
        <div class="vspacing"></div>
        <span class="hds3"><?=TEXT_GC_FAQ_H3?></span>
        <div><?=TEXT_GC_FAQ_C3?></div>
    </div>
    <div class="clrFx"></div>
</div>

<?
$redeem_gc_tab = ob_get_contents();
ob_end_clean();
?>
<style>
    label {display: inline;font-size: inherit;margin-bottom: 0;}
    input[type="radio"], input[type="checkbox"] {margin: 0;}
    #c_pwgc .shd1, #c_rgc .shd1 {width: 305px;}
    #msg_amount {font-weight:bold;line-height: 21px;}
    span.load_img_fm {width: 16px; height: 16px; display: block;margin-top: 5px;}
    span.gc_succ, span.gc_fail {display: block;line-height: 15px;padding: 3px 0 0 25px;min-height: 22px;}
    span.gc_succ {background: url("images/icons/success.gif") no-repeat scroll 0 4px transparent;}
    span.gc_fail {background: url("images/icons/cancel-round.png") no-repeat scroll 0 4px transparent;}
    label.gc_qty_lbl {padding-right: 40px;}
    .gc_hd {background-color: #BFE8F3; padding: 5px 10px;}
    .gc_email>input {width:290px;background-color: #FFF;}
    .tab_row2 {padding: 30px;background-color: #FFF;}
    .tab_row2>.lfloat {border-right: 1px solid #CECECE;min-height: 560px;padding-right: 30px;width: 688px;}
    .tab_row2>.rfloat {width: 160px;}
    .tab_row2 .highlight {background-color:#FFFDDB}
    div#c_pwgc div.wl {height: 1px; border-top: 1px dotted black; text-align: center; position: relative; width: 305px;}
    div#c_pwgc div.wl span {position: relative; top: -.7em; background: white; display: inline-block;}
    div#c_pwgc div.connect_with_fb_btn {margin: 0 auto; width: 170px;}
    .pwgc_success_tbl {padding: 20px 0}
    .pwgc_success_tbl .alignR {text-align: right;}
    .pwgc_success_tbl .lbox {padding-top: 13px;}
    .pwgc_success_tbl .rbox {padding-left: 5px; text-align: center; vertical-align: top; width: 200px;}
</style>
<script>
    var html_content = '<center><?=TEXT_PURCHASE_WITH_GIFT_CARD_CONFIRMATION . "<br><br>" . tep_image_button2('gray_short', 'javascript:void(0);', BUTTON_CONFIRM_CHECKOUT, '', ' onclick="document.getElementById(\\\'pwgc_form\\\').submit()"')?></center>';
    function gc_qty(req_row_obj){
        var tbl_obj=jQuery('#'+req_row_obj.id).parents('table'),
            ttl_row=tbl_obj.find('tbody').length, 
            req_row=req_row_obj.value;
        if(ttl_row>req_row){
            for(var i=ttl_row;i>req_row;i--){tbl_obj.find('tbody:last').remove()}
            gc_cal_ttl(tbl_obj);
        }else if(ttl_row<req_row){
            var fid=parseInt(tbl_obj.find('tbody').attr('id')),
                nxt=fid+ttl_row-1,
                searchfor = '_'+fid;
            for(var i=ttl_row;i<req_row;i++){nxt=nxt+1;tbl_obj.append('<tbody>'+tbl_obj.find("tbody:first").html().replace(new RegExp(searchfor, 'g'), '_'+nxt)+'</tbody>');jQuery('#sno_'+nxt+', #pcode_'+nxt).val('');progress_row(nxt,0,-1)}
        }
    }
    
    function gc_verify(f_id){
        var msg,
            allow=1,
            target=f_id.split('_').slice(-1),
            rqf={sno:'',pcode:''},
            ttype=jQuery('#'+f_id).parents('div.tab_row2').attr('id');
            
        jQuery('#'+f_id).parents('tbody').find('input').each(function(){
            if( !this.value )   allow=0;
        });
        
        //check duplicate
        jQuery.each(rqf, function(i,v) {
            rqf[i]=jQuery('#'+i+'_'+target).val();
            if(i=='sno'){
                jQuery('#'+f_id).parents('table').find('input[name="sno[]"][id!="sno_'+target+'"]').each(function() {
                    if(this.value!='' && rqf.sno==this.value){
                        msg = 'INVALID';
                        allow=-1;
                    }
                });
            }
        });
        
        if(allow==1){
            jQuery.ajax({
                type: "post",
                url: 'gift_card_xmlhttp.php?act=chk',
                data: {'sno':rqf.sno, 'pcode':rqf.pcode, 'ttype':ttype},
                dataType: 'json',
                beforeSend:  function() {
                    progress_row(target,1);
                },
                error: function(){
                    progress_row(target,0,0,'Please try again...','','');
                },
                success: function(data) {
                    progress_row(target,0,data.status,data.msg,data.amt,data.currency);
                }
            });
        }else if(allow==-1){
            progress_row(target,0,0,msg,'','');
        }else{
            progress_row(target,0,-1);
        }
    }
    
    function gc_cal_ttl(tbl_obj){
        var amt=0,
            lbl_obj=tbl_obj.find('span[class~="lbl_ttl"]'),
            ccurr=lbl_obj.attr('data-cur'),
            cdefault=lbl_obj.attr('data-default');
            progress_ttl(lbl_obj,1,'',cdefault,ccurr);
        
        tbl_obj.find('span.gc_info[data-val!=""]').each(function(){
            if(ccurr!=jQuery(this).attr('data-cur')){
                progress_ttl(lbl_obj,0,cdefault,cdefault,ccurr);
                gc_verify('sno_'+this.id);amt=0;return false;
            }
            amt+=parseFloat(jQuery(this).attr('data-val'));
        });
        
        if(amt>0){
            jQuery.ajax({
                type: "post",
                url: 'gift_card_xmlhttp.php?act=format',
                data: {'amt':amt, 'ccurr':ccurr},
                async: false,
                dataType: 'json',
                beforeSend:  function() {},
                error: function(){progress_ttl(lbl_obj,0,cdefault,cdefault,ccurr)},
                success: function(data) {progress_ttl(lbl_obj,0,data.famt,amt,ccurr)}
            });
        }else{progress_ttl(lbl_obj,0,cdefault,cdefault,ccurr)}
        if(tbl_obj.attr('id'),tbl_obj.parents('div.tab_row2').attr('id') == 'c_pwgc'){calc_amount()}
    }
    
    function progress_row(id,loading,success,msg,data_v,data_c){
        var t=jQuery('#msg_'+id).parents('table'),
            h=jQuery('#msg_'+id).parents('tbody'),
            m=jQuery('#msg_'+id);
            h.removeClass('highlight');
            m.removeClass('gc_succ gc_fail');
        if(loading){
            h.find('input').attr('disabled',true);
            jQuery('#msg_'+id).attr('data-val', '').attr('data-cur', '').addClass('load_img load_img_fm').html('');
        }else{
            if(success>=0){
                if(success==1){
                    m.addClass('gc_succ');
                    t.find('span[class~="lbl_ttl"]').attr('data-cur',data_c);
                }else if(success==0){
                    m.addClass('gc_fail');
                    h.addClass('highlight');
                }
                h.find('input').removeAttr('disabled');
                jQuery('#msg_'+id).attr('data-val', data_v).attr('data-cur', data_c).removeClass('load_img load_img_fm').html(msg);
                gc_cal_ttl(t);
            }else{
                data_v=data_c=msg='';
                jQuery('#msg_'+id).attr('data-val', data_v).attr('data-cur', data_c).removeClass('load_img load_img_fm').html(msg);
            }
        }
    }
    
    function progress_ttl(ttl_obj, loading, msg, data_v, data_c){
        if(loading){
            ttl_obj.attr('data-val', data_v).attr('data-cur', data_c).addClass('load_img load_img_fm').html(msg);
        }else{
            ttl_obj.attr('data-val', data_v).attr('data-cur', data_c).removeClass('load_img load_img_fm').html(msg);
        }
    }
    
    function chk_form(form_id, etype){
        var valid=1;
        
        jQuery('form[id="'+form_id+'"] span.gc_info[data-val=""]').each(function() {
            valid=0;
        });
        
        if(valid){
            jQuery('#'+form_id+' .etype').val(etype);
            
            if(form_id=='pwgc_form'){
                jQuery('#general_content').css({padding:'35px 0 20px'}).html(html_content);
                show_fancybox('general_popup_box');
            }else{
                document.getElementById(form_id).submit();
            }
        }else{alert("<?=ERROR_INVALID_FORM_SUBMIT?>")}
        return false;
    }
    
    function email_verify(ele_id){
        var b4_email=jQuery('#'+ele_id).val(),
            currentform=jQuery('#'+ele_id).parents('form'),
            gctype=(currentform.attr('id')=='pwgc_form')?jQuery('input[name=gctype]:checked').val():'';
        
        if (gctype=='m') {
            jQuery('#tbl_fb').show();
        }else if(gctype=='g'){
            jQuery('#tbl_fb').hide();
        }
        
        setTimeout(function(){
            var jq_msg = jQuery('#msg_'+ele_id),
                jq_tbody = jQuery('#tby_'+ele_id);
                
            if(b4_email!='' && b4_email==jQuery('#'+ele_id).val()){
                jq_msg.removeClass('gc_fail').html('');
                jQuery.ajax({
                    type: "post",
                    url: 'gift_card_xmlhttp.php?act=chk_email',
                    data: {'email':b4_email,'gctype':gctype},
                    dataType: 'json',
                    beforeSend:  function() {jq_tbody.html('');jq_msg.addClass('load_img load_img_fm');},
                    error: function(){jq_msg.removeClass('load_img load_img_fm');},
                    success: function(data) {
                        jq_msg.removeClass('load_img load_img_fm');
                        if(data.status==0){jq_msg.addClass('gc_fail').html(data.content);}else{jq_tbody.html(data.content);}
                    }
                });
            }else if(b4_email==''){
                jq_msg.removeClass('gc_fail').html('');
                jq_tbody.html('');jq_msg.removeClass('load_img load_img_fm');
            }
        }, 1000);
    }
    
    function load_game_list(ele_obj){
        if (!jQuery('#'+ele_obj.id).hasClass('game_loaded')) {
            var jq_msg=jQuery('#msg_'+ele_obj.id),d,textToInsert=[],i=0;
            
            jQuery.ajax({
                type: "post",
                url: 'gift_card_xmlhttp.php?act=get_game_list',
                dataType: 'json',
                beforeSend:  function() {jq_msg.addClass('load_img load_img_fm');},
                error: function(){jq_msg.removeClass('load_img load_img_fm');alert('Please try again...')},
                success: function(data) {
                    jQuery('#'+ele_obj.id).addClass('game_loaded');
                    jq_msg.removeClass('load_img load_img_fm');
                    jQuery.each(data.sorting, function() {
                        d=data[this];
                        textToInsert[i++] = '<option value="'+d.cPath+'">'+d.name+'</option>';
                    });
                    jQuery('#'+ele_obj.id).append(textToInsert.join(''));
                }
            });
        }
    }
    
    function load_game_details(ele_obj){
        var textToInsert=[],i=0;
        
        jQuery.ajax({
            type: "post",
            url: 'gift_card_xmlhttp.php?act=get_product_list',
            data: {'cPath':ele_obj.value},
            dataType: 'json',
            beforeSend:  function(){jQuery('#msg_gtype').addClass('load_img load_img_fm');jQuery('#gtype option[value!="0"]').remove();jQuery('#dm_info_list').html('');jQuery('#tby_'+ele_obj.id+', #tby2_'+ele_obj.id).show()},
            error: function(){jQuery('#msg_gtype').removeClass('load_img load_img_fm');alert('Please try again...')},
            success: function(data){
                jQuery.each(data, function(){
                    textToInsert[i++] = '<option value="'+this.id+'">'+this.text+'</option>';
                });
                
                jQuery('#msg_gtype').removeClass('load_img load_img_fm');
                jQuery('#gtype').append(textToInsert.join(''));
                jQuery('#msg_amount').html('0.00');
            }
        });
    }
    
    function load_dm(ele_obj){
        var textToInsert=[],i=0;
        if(ele_obj.value=='6'){
            jQuery.ajax({
                type: "post",
                url: 'gift_card_xmlhttp.php?act=get_dtu_details',
                data: {'pid':jQuery('#gtype').val()},
                dataType: 'json',
                beforeSend:  function(){},
                error: function(){alert('Please try again...')},
                success: function(data){
                    jQuery.each(data, function(key, val){
                        textToInsert[i++] = "<tr><td></td><td><div class='shd1 ihd1'>"+val+"</div></td><td></td></tr>";
                    });
                    jQuery('#dm_info_list').html(textToInsert.join(''));
                }
            });
        }else{
            jQuery('#dm_info_list').html('');
        }
    }
    
    function calc_amount(){
        var textToInsert=[],i=0;
        var pid=jQuery('#gtype').val();
        var qty=jQuery('#gqty').val();
        
        jQuery.ajax({
            type: "post",
            url: 'gift_card_xmlhttp.php?act=get_product_details',
            data: {'pid':pid, 'buyqty':qty, 'gc_amt':jQuery('#pwgc_ttlv').attr('data-val'), 'gc_curr':jQuery('#pwgc_ttlv').attr('data-cur')},
            dataType: 'json',
            beforeSend:  function(){jQuery('#tr_qty').removeClass('highlight');jQuery('#gdm option[value!="0"]').remove();jQuery('#dm_info_list').html('');jQuery('#msg_amount').addClass('load_img load_img_fm').html('');},
            error: function(){alert('Please try again...')},
            success: function(data){
                jQuery.each(data.delivery_mode, function(key, val){
                    textToInsert[i++] = '<option value="'+key+'" '+(key==5?'selected':'')+'>'+val+'</option>';
                });

                jQuery('#gdm').append(textToInsert.join(''));
                jQuery('#msg_amount').removeClass('load_img load_img_fm').html(data.formatted_price);

                if (data.alert_notice!='') {
                    jQuery('#tr_qty').addClass('highlight');
                    alert(data.alert_notice);
                }
            }
        });
    }
    
    function etype_changed(radio_obj){
        var etype_arr=radio_obj.id.split('_'),
            prefix = 'tr_'+etype_arr[0],
            hide_id=prefix+'_'+(etype_arr.slice(-1)=='ec'?'nc':'ec');
        
        jQuery('#'+hide_id).hide();
        jQuery('#tr_'+radio_obj.id).show();
    }
</script>
<?
if (isset($_SESSION['need_sc_usage_qna']) && $_SESSION['need_sc_usage_qna'] == 'true') {
    echo '<script language="JavaScript">jQuery(document).ready(function(){ask_qna()})</script>';
}

if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }
?>

<div class="vspacing" style="height: 30px;"></div>
<div>
    <div class="lfloat"><?=tep_image(DIR_WS_IMAGES . 'gift_card.jpg', '')?></div>
    <div class="lfloat" style="padding: 10px; width: 602px;">
        <span style="font-size:40px"><?=NAVBAR_GC_TITLE?></span>
        <div><span style="font-size:20px;color:#4886BF;font-style:italic"><?=TEXT_GC_SUBHEADER?></span></div>
        <div class="vspacing" style="height:10px"></div>
        <span class="hd5"><?=TEXT_GC_SUB_DESC?></span>
    </div>
</div>
<div class="vspacing"></div>
<?
if (isset($_SESSION['customer_id'])) {
    echo sprintf(TEXT_GC_WELCOME_BACK, $_SESSION['customer_first_name'], "window.location.href='" . $shasso_obj->getLogoutURL(tep_href_link(FILENAME_LOGOFF, 'nextURL=' . FILENAME_GIFT_CARD, 'SSL')) . "'") . '<div class="vspacing" style="height:10px;"></div>';
}

$tab_content[] = array( 
    'label' => $enabled_tab1_link ? '<a href="'. tep_href_link(FILENAME_GIFT_CARD, 'tab=1', 'SSL') . '" style="padding:13px;color:#000;text-decoration: none;">' . TAB_HEADING_1 . '</a>' : TAB_HEADING_1, 
    'content' => $purchase_gc_tab,
    'selected' => $tab == 1 ? 1 : 0
);

$tab_content[] = array( 
    'label' => $enabled_tab2_link ? '<a href="'. tep_href_link(FILENAME_GIFT_CARD, 'tab=2', 'SSL') . '" style="padding:13px;color:#000;text-decoration: none;">' . TAB_HEADING_2 . '</a>' : TAB_HEADING_2, 
    'content' => $redeem_gc_tab,
    'selected' => $tab == 2 ? 1 : 0
);

$tab_content[] = array( 
    'label' => '<a href="'. tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL') . '" style="padding:13px;color:#000;text-decoration: none;">' . TAB_HEADING_3 . '</a>',
    'content' => '<div class="tab_row2" style="text-align: center;">'.tep_image(DIR_WS_IMAGES . 'lightbox-ico-loading.gif', '', '32', '32').'</div>',
    'selected' => $tab == 3 ? 1 : 0
);

echo $page_obj->get_html_tab($tab_content, 2);
?>