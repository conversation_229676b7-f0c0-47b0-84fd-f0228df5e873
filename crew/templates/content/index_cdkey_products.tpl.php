<?
if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }

$product_type_description = tep_get_categories_description($page_info->custom_products_type_id_cat);

?>

<h1><?=tep_get_categories_heading_title($current_category_id)?></h1>
<div><?=$product_type_description?></div>
<div class="vspacing"><!-- --></div>

<div class="break_line"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			<div id="theLayer"></div>
		</td>
	</tr>
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div id="list_product"><?=$page_info->get_product_listing()?></div></td>
				</tr>
			</table>
		</td>
	</tr>
</table>

<div class="break_line"></div>
<script language="JavaScript" type="text/javascript">

var alphabet = new Array();
alphabet = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','numeric'];

function show_alpha_div (div_id) {
	for (i=0; i < alphabet.length; i++) {
		var divname_tmp = alphabet[i];
		
		jQuery("#alpha_nav_" + divname_tmp + "_link").show();
		jQuery("#alpha_nav_" + divname_tmp + "_selected").hide();

		if (div_id != "alpha_nav_all_games"){
			if ('alpha_nav_' + divname_tmp == div_id)
				jQuery("#alpha_div_" + divname_tmp).show();
			else
				jQuery("#alpha_div_" + divname_tmp).hide();
			
			jQuery("#alpha_line_" + divname_tmp).hide();
		}
		else {
			jQuery("#alpha_div_" + divname_tmp).show();
			jQuery("#alpha_line_" + divname_tmp).show();
		}
	}

	jQuery("#" + div_id + "_link").hide();
	jQuery("#" + div_id + "_selected").show();
	
	if (div_id != "alpha_nav_all_games"){
		jQuery("#alpha_nav_all_games_link").show();
		jQuery("#alpha_nav_all_games_selected").hide();
	}
		
}
</script>