<?
$latest_news_obj = new latest_news();
$news_array = $latest_news_obj->cached_latest_news_column(1);

ob_start();
if (tep_not_null($news_array)) {
	foreach ($news_array as $key) {
		$news_url_parameter="news_id=" . $key['news_id'] . "";
		$image_size = array ( 	'width' => 90,
								'height' => 60
							);
?>
		<div class="<?=(tep_not_null($key['thumbnail_image_url']))?'tab_row dotborderBtm t2':'tab_row dotborderBtm t3'?>">
<? if (tep_not_null($key['thumbnail_image_url'])) { ?>
			<div class="lfloat">
				<a href="<?=tep_href_link(FILENAME_SEARCH_LATEST_NEWS,$news_url_parameter)?>"><?=tep_image($key['thumbnail_image_url'],'',$image_size['width'],$image_size['height']);?></a>
			</div>
<? }?>

			<div class="<?=(tep_not_null($key['thumbnail_image_url']))?'rfloat':'lfloat'?>">
				<a class="hd1" href="<?=tep_href_link(FILENAME_SEARCH_LATEST_NEWS,$news_url_parameter)?>"><?=eval_html($key['headline'])?></a>
				<div>
					<span class="date"><?=$key['date_added']?></span><br>
					<span style=""><?=eval_html($key['latest_news_summary']);?></span>
				</div>
			</div>
			<div style="clear:both"></div>
		</div>
<?
	}
}
?>
		<div class="tab_row" style="padding:20px 0px 0px 160px">
			<?=tep_image_button2('gray_short',tep_href_link(FILENAME_SEARCH_LATEST_NEWS),BUTTON_READ_MORE_NEWS,150);?>
		</div>
<?
$news_content_string = ob_get_contents();
ob_end_clean();

$news_tab_array[] = array(	'label' => BOX_INFORMATION_NEWS,
							'content' => $news_content_string);
# retrieve init data
include_once(DIR_WS_CLASSES . FILENAME_MAIN_PAGE);
$main_page = new main_page();
$main_content = $main_page->main_page_init();
?>

<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/mainpagetab.css">
<div id="mainTopBannerBox">
	<?php include_once('main_slider.php'); ?>
</div>
<div class="vspacing"><div class="lgShadow"></div></div>
<div id="mainMdlBannerBox" class="ogm_partition">
	<?php include_once('main_best_selling.php'); ?>
</div>
<div class="vspacing"></div>
<div id="mainContentBox" class="ogm_partition">
	<div class="halfBox lfloat"><?=$page_obj->get_html_tab($news_tab_array, 2)?></div>
	<div class="halfBox rfloat"><?php include_once('main_tab.php'); ?></div>
</div>
<?
    if ($enable_event_effect) {
        ob_start();
        echo '<div class="footer_' . $enable_event_effect . '_img">&nbsp;</div>';

        if ($enable_event_snow) {
            $flakeBottom = $enable_mobile_bar ? 478 : 363;
?>
        <script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>snowstorm/snowstorm-min.js"></script>
        <script>
            snowStorm.zIndex = 799;
            snowStorm.excludeMobile = false;
            snowStorm.snowCharacter = '&#10052;';
            snowStorm.flakeBottom = <?php echo $flakeBottom ?>;
            snowStorm.flakeWidth = 15;
            snowStorm.flakeHeight = 15;
        </script>
<?
        }

        $contentFooter = ob_get_contents();
        ob_end_clean();
    } else {
        echo '<div class="vspacing"></div>';
    }
?>