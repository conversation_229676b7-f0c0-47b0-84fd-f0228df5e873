<?
if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }

$product_type_description = tep_get_categories_description($page_info->custom_products_type_id_cat);
?>
<h1><?=tep_get_categories_heading_title($current_category_id).$ogm_fb_obj->get_FB_button('like')?></h1>
<div><?=$product_type_description?></div>
<div class="vspacing"><!-- --></div>

<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			<script language="javascript">
			<!--
				if (navigator.appName.toUpperCase().match(/MICROSOFT INTERNET EXPLORER/) != null) {
					document.write('<iframe id="theLayerIframe" src="javascript:;" marginwidth="0" marginheight="0" align="bottom" scrolling="no" frameborder="0" style="position:absolute; width:450px; left: 439; top: 351; visibility: hidden; display: block; filter: alpha(opacity=0);"></iframe>');
				}
			//-->
			</script>
			<div class="theLayer" id="theLayer"></div>
			<div class="theLayerBg" id="theLayerBg">&nbsp;</div>
			<script language="javascript">
			<!--
				var winW = document.body.scrollWidth;

				jQuery("#theLayerBg").css('width',winW);
				jQuery("#theLayerBg").css('position','absolute');
				jQuery("#theLayerBg").css('height',100);

				jQuery(document).ready(function(){
					var winH = jQuery("#footerBottomDiv").offset().top + 100;

					jQuery("#theLayerBg").css('height',winH + 15);
				});
			//-->
			</script>
			
		</td>
	</tr>
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div id="list_product"><?=$page_obj->get_html_simple_rc_box('',$page_info->get_product_listing(),13);?></div></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
  		<td align="left">
  			<div id="div_announcement_news" style="width: 100%;">
<?
	        $LATEST_NEWS_TYPE = '5';
			define('LATEST_NEWS_BOX', 'OGM2008');
	        include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
?>
			</div>
		</td>
	</tr>
</table>

<div class="break_line"></div>
<script language="JavaScript" type="text/javascript">

var alphabet = new Array();
alphabet = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','numeric'];

function show_alpha_div (div_id) {
	for (i=0; i < alphabet.length; i++) {
		var divname_tmp = alphabet[i];
		
		jQuery('#alpha_nav_' + divname_tmp + '_link').show();
		jQuery('#alpha_nav_' + divname_tmp + '_selected').hide();

		if (div_id != 'alpha_nav_all_games'){
			if ('alpha_nav_' + divname_tmp == div_id)
				jQuery('#alpha_div_' + divname_tmp).show();
			else
				jQuery('#alpha_div_' + divname_tmp).hide();
			
			jQuery('#alpha_line_' + divname_tmp).hide();
		}
		else {
			jQuery('#alpha_div_' + divname_tmp).show();
			jQuery('#alpha_line_' + divname_tmp).show();
		}
	}

	jQuery('#' + div_id + '_link').hide();
	jQuery('#' + div_id + '_selected').show();
	
	if (div_id != 'alpha_nav_all_games'){
		jQuery('#alpha_nav_all_games_link').show();
		jQuery('#alpha_nav_all_games_selected').hide();
	}
}
</script>