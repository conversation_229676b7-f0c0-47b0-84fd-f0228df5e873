<?
if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }

$product_type_description = tep_get_categories_description($page_info->custom_products_type_id_cat);

?>
<h1><?=tep_get_categories_heading_title($current_category_id)?></h1>
<div><?=$product_type_description?></div>
<div class="vspacing"><!-- --></div>

<div class="break_line"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div id="list_product"><?=$page_info->get_product_listing()?></div></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
  		<td>
			<div id="div_announcement_news" style="width: 100%;">
			<?
		        $LATEST_NEWS_TYPE = '5';
				define('LATEST_NEWS_BOX', 'OGM2008');
		        include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
			?>
			</div>
  		</td>
	</tr>
</table>

<div class="break_line"></div>
<script language="JavaScript" type="text/javascript">
	function display_product(pid) {
		if (jQuery('#collapse_'+pid).css('display') == 'none') {
			jQuery('#collapse_'+pid).fadeIn('fast');
			
			jQuery('#product_list_'+pid).html('<a href="javascript:;" onclick="display_product(\''+pid+'\');">&#8250;&nbsp;<?=LINK_HIDE_INFO?></a>');
		} else {
			jQuery('#collapse_'+pid).fadeOut('fast');
			
			jQuery('#product_list_'+pid).html('<a href="javascript:;" onclick="display_product(\''+pid+'\');">&#8250;&nbsp;<?=LINK_MORE_INFO?></a>');
		}
	}
	
	function chgImage(img_src) {
		var objImg = document.getElementById(img_src);

		if (objImg.src.match('<?=DIR_WS_ICONS."icon_arw_down.gif" ?>')) {
			document.location = '<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("page", "info", "x", "y")) . "page=" . $page . "&col=' + img_src + '&direction=asc")?>';
			objImg.src = '<?=DIR_WS_ICONS."icon_arw_up.gif" ?>';
		} else {
			document.location = '<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("page", "info", "x", "y")) . "page=" . $page . "&col=' + img_src + '&direction=desc")?>';
			objImg.src = '<?=DIR_WS_ICONS."icon_arw_down.gif"?>';
		}
	}
	
	function hla_more_info(ref_id) {
		if (jQuery('#more_info_link_'+ref_id).css('display') == 'block') {
			jQuery('#more_info_link_'+ref_id).css('display', 'none');
			jQuery('#more_info_'+ref_id).css('display', 'block');
			jQuery('#hide_info_link_'+ref_id).css('display', 'block');
		} else {
			jQuery('#hide_info_link_'+ref_id).css('display', 'none');
			jQuery('#more_info_'+ref_id).css('display', 'none');
			jQuery('#more_info_link_'+ref_id).css('display', 'block');
		}
	}
	
	function load_alternate_character(hla_account_id, hla_ref_id) {
		var hla_account_id_arr = eval('[' + hla_account_id + ']');
		var hla_ref_id_arr = eval('["' + hla_ref_id + '"]');
		var hla_loop = hla_account_id_arr.length;
		
		for (var hla_cnt=0; hla_cnt < hla_loop; hla_cnt ++ ) {
			hla_alternate_info(hla_account_id_arr[hla_cnt], hla_ref_id_arr[hla_cnt]);
		}
	}
	
	function hla_alternate_info(hla_account_id, ref_id) {
		jQuery.get('<?=tep_href_link("hla_xmlhttp.php?action=hla_alternate_char")?>&hla_account_id='+hla_account_id+'&ref_id='+ref_id, function(xml) {
			jQuery(xml).find('response').each(function(){
				var result = jQuery("result", this).text();
				var alternate_char_html = '';
				
				if (result > 0) {
					alternate_char_html += '<table border="0" cellspacing="0" cellpadding="0" width="100%">';
					alternate_char_html += '<tr>';
					alternate_char_html += '	<td valign="top" colspan="11" style="padding:10px;">';
					alternate_char_html += '	<b><?=TEXT_HLA_ALTERNATE_CHARACTERS?>:</b>';
					alternate_char_html += '	</td>';
					alternate_char_html += '</tr>';
					
					jQuery(xml).find('alternate_char').each(function(){
						var level = jQuery("level", this).text();
						var race_icon = jQuery("race_icon", this).text();
						var class_icon = jQuery("class_icon", this).text();
						var faction_icon = jQuery("faction_icon", this).text();
						var country = jQuery("country", this).text();
						var products_ref_id = jQuery("products_ref_id", this).text();
						var products_char_id = jQuery("products_char_id", this).text();
						var characters_name = jQuery("characters_name", this).text();
						var characters_description = jQuery("characters_description", this).text();
						
						alternate_char_html += '<tr>';
						alternate_char_html += '	<td style="width:7px;padding-top:50px;padding-bottom:0px;">&nbsp;</td>';
						alternate_char_html += '	<td style="width:70px;text-align:center;"><span style="font-size:20px;font-weight:bold;">'+level+'</span><br />#'+products_ref_id+'</td>';
						alternate_char_html += '	<td style="width:33px;">&nbsp;</td>';
						alternate_char_html += '	<td style="width:110px;text-align:center;">' + race_icon + '&nbsp;&nbsp;&nbsp;' + class_icon + '</td>';
						alternate_char_html += '	<td style="width:33px;">&nbsp;</td>';
						alternate_char_html += '	<td style="width:50px;text-align:center;">' + faction_icon + '</td>';
						alternate_char_html += '	<td style="width:33px;">&nbsp;</td>';
						alternate_char_html += '	<td style="width:223px;">';
						alternate_char_html += '	<span style="padding:0 18px;float:left;">';
						alternate_char_html += '		<a href="javascript:void(0);" onClick="window.open(\'<?=tep_href_link(FILENAME_VIEW_PROFILE, "char_id='+products_char_id+'")?>\', \''+characters_name+'\', \'width=\'+screen.width+\', height=\'+screen.height+\', top=0, left=0, fullscreen=yes, scrollbars=yes\'); return false;"><?=TEXT_HLA_VIEW_PROFILE?></a>';
						alternate_char_html += '	</span>';
						
						alternate_char_html += '	<span style="padding:0 18px;display:block;float:left;" id="more_info_link_'+products_char_id+'">';
						alternate_char_html += '		<a href="javascript:void(0);" onClick="hla_more_info(\''+products_char_id+'\');">&#8250;&nbsp;<?=LINK_MORE_INFO?></a>';
						alternate_char_html += '	</span>';
						alternate_char_html += '	<span style="padding:0 18px;display:none;float:left;" id="hide_info_link_'+products_char_id+'">';
						alternate_char_html += '		<a href="javascript:void(0);" onClick="hla_more_info(\''+products_char_id+'\');">&#8250;&nbsp;<?=LINK_HIDE_INFO?></a>';
						alternate_char_html += '	</span>';
						
						alternate_char_html += '	</td>';
						alternate_char_html += '	<td style="width:7px;">&nbsp;</td>';
						alternate_char_html += '</tr>';
						
						
						alternate_char_html += '<tr>';
						alternate_char_html += '	<td colspan="11">';
						alternate_char_html += '	<div id="more_info_'+products_char_id+'" style="padding:20px 32px;display:none;"><?=ENTRY_COUNTRY?> '+country+'<br />'+characters_description+'</div>';
						alternate_char_html += '	</td>';
						alternate_char_html += '</tr>';
					});
					
					alternate_char_html += '</table>';
					jQuery('#alternate_'+hla_account_id).html(alternate_char_html);
					jQuery('#alternate_'+hla_account_id).css('background-color', '#EFEFEF');
				}
			});
		});
	}
</script>