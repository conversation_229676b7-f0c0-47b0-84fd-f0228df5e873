<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center">
		<?
   			if ( (ALLOW_CATEGORY_DESCRIPTIONS == 'true') && (tep_not_null($category['categories_heading_title'])) ) {
     			echo $category['categories_heading_title'];
   			} else {
     			echo HEADING_TITLE;
   			}
 		?>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
	          		<td>
	          		<?
		          		$LATEST_NEWS_TYPE = '5';
						define('LATEST_NEWS_BOX', "classic");
						include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
					?>
	          		</td>
	          	</tr>
			</table>
		</td>
	</tr>
<?
$cat_img_path = '';

$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key('BUCKET_STATIC');
$aws_obj->set_filepath('images/category/');
$categories_image_info_array = $aws_obj->get_image_info($category['categories_image']);
unset($aws_obj);

if (tep_not_null($products_image_info_array)) {
	$cat_img_path = $products_image_info_array['src'];
} else if (file_exists(THEMA_IMAGES . "/category/". $category['categories_image'])) {
	$cat_img_path = THEMA_IMAGES . "/category/". $category['categories_image'];
}

if ( ((ALLOW_CATEGORY_DESCRIPTIONS == 'true') && (tep_not_null($category['categories_description']))) ||
	 tep_not_null($cat_img_path) ) { 
?>
    <tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="6">
          		<tr>
            		<td width="100%" align="left" valign="top" class="categoryDesc">
		            <?
		            if ( (ALLOW_CATEGORY_DESCRIPTIONS == 'true') && (tep_not_null($category['categories_description'])) ) { 
				    	//echo $breadcrumb->trail(' &raquo; ');
				        echo $category['categories_description']; 
			        } else echo '&nbsp;';
		            ?>			
            		</td>
        			<?
				    	if (tep_not_null($cat_img_path)) {
				    		echo '<td align="right" valign="top" nowrap>&nbsp;';
				    		echo tep_image($cat_img_path, $category['categories_name'], HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT);
				    		echo '</td>';
				    	}
					?>
          		</tr>
        	</table>
		</td>
	</tr>
<?
}
?>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="subCategoryBox">
              				<tr>
              				<?
							if (tep_session_is_registered('customer_id')) { 
								global $customer_id;
    							$customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
    							$customer_group = tep_db_fetch_array($customer_group_query);
    							if($customer_group['customers_groups_id']){
    								$cat_grp_id = $customer_group['customers_groups_id'];	
    							} else {
    								$cat_grp_id = '1';  // 1 = Guest
    							}
							}
    						if (isset($cPath) && strpos('_', $cPath)) {
								// check to see if there are deeper categories within the current category
      							$category_links = array_reverse($cPath_array);
      							for($i=0, $n=sizeof($category_links); $i<$n; $i++) {
        							$categories_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_status = '1' and c.parent_id = '" . (int)$category_links[$i] . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "'");
        							$categories = tep_db_fetch_array($categories_query);
        							if ($categories['total'] < 1) {
          								// do nothing, go through the loop
        							} else {
       									//   $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.categories_image, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_status = '1' and c.parent_id = '" . (int)$category_links[$i] . "' and cd.categories_groups in ('" . $cat_grp_id . "','0') and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' order by sort_order, cd.categories_name");
           								$categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.parent_id, cde.groups_id from categories as c, categories_description as cd, categories_groups as cde where c.parent_id = '" . (int)$category_links[$i] . "' 
                                        									and c.categories_status = '1' and ((cde.groups_id = '".$cat_grp_id. "') or (cde.groups_id = '".$const_alluseraccess. "'))
                                        									and c.categories_id = cd.categories_id and c.categories_id = cde.categories_id 
                                        									and cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name");
          								break; // we've found the deepest category the customer is in
        							}
      							}
    						} else {
    							//  $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.categories_image, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_status = '1' and c.parent_id = '" . (int)$current_category_id . "' and cd.categories_groups in ('" . $cat_grp_id . "','0') and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' order by sort_order, cd.categories_name");
      							$categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.parent_id from categories c, categories_description cd, categories_groups cde where c.parent_id = '" . (int)$current_category_id . "' 
                                						        	and c.categories_status = '1' and ((cde.groups_id = '".$cat_grp_id. "') or (cde.groups_id = '".$const_alluseraccess. "'))
                                        							and c.categories_id = cd.categories_id and c.categories_id = cde.categories_id
                                        							and cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name");
    						}
    						$number_of_categories = tep_db_num_rows($categories_query);
    						$rows = 0;    
							
     						while ($categories = tep_db_fetch_array($categories_query)) {
      							$rows++;
      							$cPath_new = tep_get_path($categories['categories_id']);
      							$width = (int)(100 / MAX_DISPLAY_CATEGORIES_PER_ROW) . '%';
       							echo '                <td class="subCategoryContents" align="left" width="' . $width . '" valign="top"><a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '" class="subCategoryNavigation">' . $categories['categories_name'] . '</a></td>' . "\n";

      							if ((($rows / MAX_DISPLAY_CATEGORIES_PER_ROW) == floor($rows / MAX_DISPLAY_CATEGORIES_PER_ROW)) && ($rows != $number_of_categories)) {
        							echo '              </tr>' . "\n";
        							echo '              <tr>' . "\n";
      							}
    						}

							// needed for the new products module shown below
    						$new_products_category_id = $current_category_id;
?>
              				</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
            		<td>
            		<? //include(DIR_WS_MODULES . FILENAME_NEW_PRODUCTS); ?>
                    <?
        				if($cPath){
            				list($cat_id, $subcat_id, $sub2cat_id) = explode("_", $cPath);
							if($subcat_id){
								if($sub2cat_id){
									$cat_id = $sub2cat_id;
								} else {
									$cat_id = $subcat_id;	
								}
							}
						} else {
							$cat_id = 0;
						}
      					
						//classic or normal
						/*
						$LATEST_NEWS_TYPE = "News";
						define('LATEST_NEWS_BOX', "classic");
				        include(DIR_WS_MODULES . FILENAME_LATEST_NEWS); 
				        echo "<p>";
				         */
						//classic or normal
						$LATEST_NEWS_TYPE = '2';
						define('LATEST_NEWS_BOX', "classic");
        				include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
        				
        				//classic or normal
						$LATEST_NEWS_TYPE = '3';
						define('LATEST_NEWS_BOX', "classic");
        				include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
        				
        				/*
        				//classic or normal
						$LATEST_NEWS_TYPE = "Specials";
						define('LATEST_NEWS_BOX', "classic");
        				include(DIR_WS_MODULES . FILENAME_LATEST_NEWS); 
         				echo "<p>";
        				*/
   					?>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
