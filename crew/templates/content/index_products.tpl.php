<?
/* bof catdesc for bts1a */
// Get the category name and description from the database
$category_query = tep_db_query("SELECT cd.categories_name, cd.categories_heading_title, cd.categories_description, cd.categories_image 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id = cd.categories_id 
								WHERE c.categories_status = '1' 
									AND c.categories_id = '" . (int)$current_category_id . "' 
									AND cd.language_id = '" . (int)$languages_id . "'");
$category = tep_db_fetch_array($category_query);
/* bof catdesc for bts1a */
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
	if ($success == 'CA') {
		$cart_link = tep_href_link(FILENAME_SHOPPING_CART);
		if ($bdn == 'y') {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_UPDATED, $cart_link), 'success');
		} else {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_ADDED, $cart_link), 'success');
		}
	} else if ($err == "SL") {
		$messageStack->add('add_to_cart', stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . ((int)$_REQUEST['sq'] > 0 ? sprintf(WARNING_PRODUCTS_SUGGESTED_QUANTITY, $_REQUEST['sq']) : ''), 'error');
		unset($err);
	}
	
	if ($messageStack->size('add_to_cart') > 0) {
		echo '	<tr align="left">
    				<td>';
		echo $messageStack->output('add_to_cart');
		echo '		</td>
				</tr>
				<tr>
     				<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>';
	}
?>
	<tr>
      	<td>
       		<table width="100%" height="30" border="0" cellpadding="0" cellspacing="0">
       			<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
					<td class="pageHeadingTitle" valign="middle" align="center">
					<?
           			if ( (ALLOW_CATEGORY_DESCRIPTIONS == 'true') && (tep_not_null($category['categories_heading_title'])) ) {
               			echo $category['categories_heading_title'];
           			} else {
              			echo HEADING_TITLE;
           			}
					?>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
					<td>
						<div class="row_separator"></div>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
           			
<?
// optional Product List Filter
if (PRODUCT_LIST_FILTER > 0) {
	if (isset($HTTP_GET_VARS['manufacturers_id'])) {
       	$filterlist_sql = "select distinct c.categories_id as id, cd.categories_name as name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_status = '1' and p.products_status = '1' and p.products_id = p2c.products_id and p2c.categories_id = c.categories_id and p2c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' and p.manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "' order by cd.categories_name";
    } else {
       	$filterlist_sql= "select distinct m.manufacturers_id as id, m.manufacturers_name as name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_MANUFACTURERS . " m where p.products_status = '1' and p.manufacturers_id = m.manufacturers_id and p.products_id = p2c.products_id and p2c.categories_id = '" . (int)$current_category_id . "' order by m.manufacturers_name";
    }
    $filterlist_query = tep_db_query($filterlist_sql);
    if (tep_db_num_rows($filterlist_query) > 1) {
    	echo '		<td align="center" class="main">' . tep_draw_form('filter', FILENAME_DEFAULT, 'get') . TEXT_SHOW . '&nbsp;';
        if (isset($HTTP_GET_VARS['manufacturers_id'])) {
        	echo tep_draw_hidden_field('manufacturers_id', $HTTP_GET_VARS['manufacturers_id']);
        	$options = array(array('id' => '', 'text' => TEXT_ALL_CATEGORIES));
        } else {
        	echo tep_draw_hidden_field('cPath', $cPath);
        	$options = array(array('id' => '', 'text' => TEXT_ALL_MANUFACTURERS));
        }
        echo tep_draw_hidden_field('sort', $HTTP_GET_VARS['sort']);
        while ($filterlist = tep_db_fetch_array($filterlist_query)) {
        	$options[] = array('id' => $filterlist['id'], 'text' => $filterlist['name']);
        }
        echo tep_draw_pull_down_menu('filter_id', $options, (isset($HTTP_GET_VARS['filter_id']) ? $HTTP_GET_VARS['filter_id'] : ''), 'onchange="this.form.submit()"');
        echo '</form></td>' . "\n";
	}
}

// Get the right image for the top-right
$image = DIR_WS_IMAGES . 'table_background_list.gif';
if (isset($HTTP_GET_VARS['manufacturers_id'])) {
	$image = tep_db_query("select manufacturers_image from " . TABLE_MANUFACTURERS . " where manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "'");
    $image = tep_db_fetch_array($image);
    $image = $image['manufacturers_image'];
} elseif ($current_category_id) {
   	$image = tep_db_query("	SELECT categories_image 
   							FROM " . TABLE_CATEGORIES_DESCRIPTION . " 
   							WHERE categories_id = '" . (int)$current_category_id . "'");
   	$image = tep_db_fetch_array($image);
  	$image = $image['categories_image'];
}
?>
			</table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
	          		<td>
	          		<?
		          		$LATEST_NEWS_TYPE = '5';
						define('LATEST_NEWS_BOX', "classic");
				        include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
					?>
	          		</td>
	          	</tr>
			</table>
		</td>
	</tr>
<?
if ( (ALLOW_CATEGORY_DESCRIPTIONS == 'true') && (tep_not_null($category['categories_description'])) ) {
    echo '	<tr>
      			<td align="left" class="categoryDesc">'.$category['categories_description'] .'</td>
			</tr>';
}

if (tep_session_is_registered('customer_id')) {
	global $customer_id;
	$customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
	$customer_group = tep_db_fetch_array($customer_group_query);
	if($customer_group['customers_groups_id']){
		$cat_grp_id = $customer_group['customers_groups_id'];	
	} else {
		$cat_grp_id = '1';  // 1 = Guest
	}
}
?>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '1')?></td>
    </tr>
<?
if ($cPath){
	list($cat_id, $subcat_id,$sub2cat_id) = explode("_", $cPath);
	if($subcat_id){
		if($sub2cat_id){
			$cat_id = $sub2cat_id;
		} else {
			$cat_id = $subcat_id;	
		}
	}
} else {
	$cat_id = 0;	
}
?>
    <tr>
      	<td>
<?
	    //classic or normal
		$LATEST_NEWS_TYPE = '2';
		define('LATEST_NEWS_BOX', "classic");
	    include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
?>
		</td>
	</tr>
	<tr>
      	<td>
<?  
	    //classic or normal
		$LATEST_NEWS_TYPE = '3';
		define('LATEST_NEWS_BOX', "classic");
	    include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
?>
        </td>
	</tr>
</table>