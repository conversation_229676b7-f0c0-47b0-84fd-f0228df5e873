<table border="0" width="100%" cellspacing="0" cellpadding="0">
    <tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
      <?
         $sql=mysql_query('SELECT * FROM info_news where language_id=' . (int)$languages_id)
         or die(mysql_error());
         $row=mysql_fetch_array($sql);
      ?>
      <tr>
      <td class="main" valign="top">
      <?
          //echo str_replace("\r\n", "<br>", stripslashes($row['Text']));
          echo stripslashes($row['Text']);   
      ?>
      </td>
      </tr>
      <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          <tr class="infoBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>        
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
					<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
    </table>