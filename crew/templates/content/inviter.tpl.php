<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/mainpagetab.css">

<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"></div>

<?php 
	$div_upper = '';
	if ($messageStack->size('inviter') > 0) { 
		for($msg=0; $msg < $messageStack->size('inviter'); $msg++) {
			if ($messageStack->messages[$msg]['params'] == 'class="messageStackSuccess"') {
				$messageStack->messages[$msg]['text'] = str_replace(tep_image(DIR_WS_ICONS . 'success.gif', ICON_SUCCESS), tep_image(DIR_WS_IMAGES."successful.gif", ICON_SUCCESS, 54, 56, 'style="margin:0px 8px 0px 30px; vertical-align: middle;"'), $messageStack->messages[$msg]['text']);
				$messageStack->messages[$msg]['params'] .= ' style="font-size:18px; font-weight:bold;"';
				$div_upper = 'style="height:20px"';
			}
		}
	?>
<? ob_start();?>
		<div>
			<div class="breakLine" <?=(isset($_REQUEST['action']) && $_REQUEST['action']=='success_invite' ? $div_upper : '') ?>><!-- --></div>
			<div style="margin-left:10px; margin-right:-10px;"><?=$messageStack->output('inviter'); ?></div>
<?php if ($_REQUEST['action'] == 'success_invite') { ?>
			<div class="dottedLine" style="line-height:30px; margin:0px 15px;">&nbsp;</div>
			<div style="margin-left:105px;">
				<div class="breakLine" style="height:15px;"><!-- --></div>
				<style type="text/css">
					#success_links .green_button font { text-align:left; }
					#success_links .green_button span { width:<?=(isset($language_code) && tep_not_null($language_code) ? ($language_code=='en' ? '245px' : '175px' ) : (tep_not_null($languages_id) && $languages_id==1 ? '245px' : '175px') ); ?> }
					#success_links .green_button { width:<?=(isset($language_code) && tep_not_null($language_code) ? ($language_code=='en' ? '245px' : '175px' ) : (tep_not_null($languages_id) && $languages_id==1 ? '245px' : '175px') ); ?> }
				</style>
				<table id="success_links" border="0" width="60%" cellpadding="0" cellspacing="3">
					<tr><td style="font-size:16px;font-weight:bold;"><?=QUESTION_WHAT_TODO_NEXT?></td></tr>
					<tr><td><?=tep_div_button(2, IMAGE_BUTTON_CONTINUE_SHOPPING_FOR_PRODUCTS_SERVICES, tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'), '', 'green_button') ?></td></tr>
					<tr><td><?=tep_div_button(2, IMAGE_BUTTON_CHECK_MY_AFFILIATE_EARNING, tep_href_link(FILENAME_MY_AFFILIATE_OVERVIEW, '', 'SSL'), '', 'green_button') ?></td></tr>
					<tr><td>&nbsp;</td></tr>
					<tr><td>&nbsp;</td></tr>
				</table>
				<div class="breakLine" style="height:35px;"><!-- --></div>
			</div>
<?php } ?>
			<div class="breakLine"><!-- --></div>
		</div>
<?
$inviter_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$inviter_content_string,13); 
?>	<div class="breakLine"><!-- --></div>
<?php } else if (isset($_POST["ids"])) { ?>
	<div class="loginColumnBorder">
		<div>
			<div class="breakLine" <?=$div_upper?>><!-- --></div>
			<div style="margin-left:10px; margin-right:-10px;">
				<table id="success_links" border="0" width="60%" cellpadding="0" cellspacing="3">
					<tr>
						<td>
							<!--style type="text/css">
								a.FB_Link { color:red; }
							</style-->
<?php		foreach ($_POST["ids"] AS $fb_id) { ?>
							<fb:name uid=<?=$fb_id?> useyou=false></fb:name> has been Invited.<br>
<?php		} ?>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
<? 
	}
?>

<div class="loginColumnBorder">
	<div style="height: 59px; background-image: url('<?=DIR_WS_IMAGES."bg_affiliate01.gif"?>'); vertical-align:middle;">
		<div style="float:left; padding:12px 0px 0px 15px; font-size:16px; display:inline;"><b><?=HEADING_SUB_TITLE?></b></div>
		<div style="float:right; padding:12px 8px 0px 0px; display:inline; vertical-align:middle;">
			<?=MY_AFFILIATE_LINK."&nbsp;&nbsp;<b><a href='".tep_href_link('/?a_aid='.$customer_id, '', 'SSL')."'>".tep_href_link('/?a_aid='.$customer_id, '', 'SSL')."</a></b>"?>
		</div>
	</div>
	
	<table border="0" width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td>
				<div style="padding:10px;">
					<div style="position:relative;width:100%;height:39px;">
						<div style="position:absolute;height:39px;z-index:9;">
							
							<div id="tab0_selected" style="float:left;visibility:visible;opacity:100;filter:alpha(opacity=100)">
								<div class="tabHeaderLeftSelected"></div>
								<div class="tabHeaderCenterSelected" style="width:233px;" onclick="switch_tab('tab0');">
									<span style="vertical-align:middle;"><b><?=FACEBOOK_INVITE_A_FRIEND_TAB ?></b></span>
								</div>
								<div class="tabHeaderRightSelected" style="margin-left:0px;"></div>
							</div>
							
							<div id="tab1_selected" style="float:left;visibility:visible;opacity:0;filter:alpha(opacity=0);margin-left:-3px;">
								<div class="tabHeaderLeftSelected" style="margin-left:0;"></div>
								<div class="tabHeaderCenterSelected" style="width:233px; margin-left:0;" onclick="switch_tab('tab1');">
									<span style="vertical-align:middle;"><b><?=INVITE_A_FRIEND_TAB ?></b></span>
								</div>
								<div class="tabHeaderRightSelected"></div>
							</div>
							
							<div id="tab2_selected" style="float:left;visibility:visible;opacity:0;filter:alpha(opacity=0);margin-left:-3px;">
								<div class="tabHeaderLeftSelected" style="margin-left:0;"></div>
								<div class="tabHeaderCenterSelected" style="width:234px; margin-left:0;" onclick="switch_tab('tab2');">
									<span style="vertical-align:middle;"><b><?=MANUALLY_INVITE_A_FRIEND_TAB ?></b></span>
								</div>
								<div class="tabHeaderRightSelected" style="margin-left:0px;"></div>
							</div>
						</div>
						
						<div style="position:absolute;height:39px;width:100%;">
							<div style="height:9px;"><img src="images/blank.gif" height="9"></div>
							<div style="height:39px;width:100%;display: block;">
								<div class="tabHeaderLeft" style="display:inline;"></div>
								<div id="tab_mainpage" class="tabHeaderCenter" style="position: relative; width:735px; display:inline;">
									
									<div style="width:239px;padding-top:8px;" onclick="switch_tab('tab0');">
										<span style="vertical-align:middle;"><b><?=FACEBOOK_INVITE_A_FRIEND_TAB ?></b></span>
									</div>
									<div><img src="<?=DIR_WS_IMAGES.'/tab_blk_breakline.gif'?>" width="1" height="30"><!-- --></div>
									
									<div style="width:239px;padding-top:8px;" onclick="switch_tab('tab1');">
										<span style="vertical-align:middle;"><b><?=INVITE_A_FRIEND_TAB ?></b></span>
									</div>
									<div><img src="<?=DIR_WS_IMAGES.'/tab_blk_breakline.gif'?>" width="1" height="30"><!-- --></div>
									
									<div style="width:239px;padding-top:8px;" onclick="switch_tab('tab2');">
										<span style="vertical-align:middle;"><b><?=MANUALLY_INVITE_A_FRIEND_TAB ?></b></span>
									</div>
									
								</div>
								<div class="tabHeaderRight" style="float: right; margin-left: -2px; display: inline;"></div>
							</div>
						</div>
					</div>

					<!-- Start of VIRAL INVITER -->
					<div id="tab_content" class="tabContent" style="margin-left: 0px;">
						
						<span id="tab0" style="display:block;position:relative;">
							<!-- START - FACEBOOK INVITE -->
							<table border="0" width="100%" cellspacing="2" cellpadding="0" class="tabContentTable">
								<tr><td style="height:20px;">&nbsp;</td></tr>
								<tr>
									<td><?=$ogm_fb_obj->get_FB_multi_friend_selector()?></td>
								</tr>
								<tr><td class="dottedLine" colspan="3">&nbsp;</td></tr>
							</table>
							<!-- END - FACEBOOK INVITE -->
						</span>
						
						<span id="tab1" style="display:none;">
							<?=tep_draw_form('vi_add_friends_form', tep_href_link(FILENAME_INVITER, '', 'SSL'), 'post', '') ?>
								<?=tep_draw_hidden_field('step', $step, 'id="step"') ?>
								<?=tep_draw_hidden_field('vi_session_id', $_REQUEST['vi_session_id'], 'id="vi_session_id"') ?>
								<table border="0" width="100%" cellspacing="0" cellpadding="0" class="tabContentTable">
									<tr><td style="height:20px;">&nbsp;</td></tr>
									<tr>
										<td style="width:100px;text-align:right;padding-right:15px;"><?=ENTRY_EMAIL_PROVIDER ?>&nbsp;</td>
										<td>
											<select name="vi_provider" id="vi_provider">
												<option value=""><?=PULL_DOWN_DEFAULT ?></option>
												<?php
													// get plugin services
													if (sizeof($oi_services)) {
														foreach ($oi_services as $type => $providers)	{
															if ($type == 'email') {
																$contents .= '<optgroup label="'. $inviter->pluginTypes[$type] .'" style="line-height:35px;">';
																
																foreach ($providers as $provider => $details) {
																	$contents .= '<option value="'.$provider.'"'. (($_REQUEST['vi_provider']==$provider) ? ' selected' : '') .'>'.$details['name'].'</option>';
																}
																$contents .= '</optgroup>';
															}
														}
													}
												?>
												<?=$contents ?>
											</select>
										</td>
									</tr>
									<tr><td colspan="2" class="breakLine">&nbsp;</td></tr>
									
									<tr>
										<td style="width:100px;text-align:right;padding-right:15px;"><?=ENTRY_YOUR_EMAIL ?>&nbsp;</td>
										<td><?=tep_draw_input_field('vi_email', tep_get_customers_email($customer_id), 'id="vi_email" style="width:350px;" autocomplete="off"')?></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td><span style="font-size:10px;color:#A3A3A3;"><?=TEXT_ENTER_YOUR_VERIFY_EMAIL ?></span></td>
									</tr>
									<tr><td colspan="2" class="breakLine"></td></tr>
									
									<tr>
										<td style="width:100px;text-align:right;padding-right:15px;"><?=ENTRY_PASSWORD ?>&nbsp;</td>
										<td><?=tep_draw_password_field('vi_password', '', 'id="vi_password" style="width:350px;" autocomplete="off" ')?></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td><span style="font-size:10px;color:#A3A3A3;"><?=TEXT_WE_WONT_STORE_YOUR_PASSWORD ?></span></td>
									</tr>
									<tr><td colspan="2" class="breakLine">&nbsp;</td></tr>

									<!-- START inviter's contact list -->
									<tr>
										<td style="width:100px;text-align:right;padding-right:15px;vertical-align:top;"><span id="entry_to" style="display:<?=$visible ?>;"><?=ENTRY_TO ?></span>&nbsp;</td>
										<td>
											<div id="invite_list" style="height:250px;width:480px;display:<?=$visible ?>;border:1px solid #CCCCCC;overflow:auto;">
												<div>
													<table width="100%" border="0" cellpadding="4" cellspacing="0">
														<tr>
															<td width="5%"><?=tep_draw_checkbox_field('check_all', 'all', true, 'id="check_all" onClick="toggleAll(\'invite_list\', this.id)"') ?></td>
															<td><?=ENTRY_SELECT_ALL_OR_NONE ?></td>
														</tr>
													</table>
												</div>
												<div class="dottedLine" style="line-height:1px;margin:0px;">&nbsp;</div>
												
												<div style="display:block;background-color:#F4F4F4;overflow:auto;">
													<table width="100%" border="0" cellpadding="4" cellspacing="0">
													<?php
														// show contacts
														if ($inviter->showContacts()) {
															if (count($contacts)) {
																
																for ($box=0; $box < count($vi_list); $box++) {	//foreach ($contacts as $inv_contact => $inv_name) {
																	echo '<tr><td width="5%">' . tep_draw_checkbox_field('check_'.$box, $vi_list[$box], true, 'id="check_'.$box.'" onClick="toggleSelectAll(\'invite_list\', \'check_all\')"') . tep_draw_hidden_field('inv_contact_'.$box, $vi_list[$box]) . tep_draw_hidden_field('inv_name_'.$box, $vi_contacts[$vi_list[$box]]) . "</td>";
																	echo ($plugType=='email') ? "<td>".$vi_list[$box]." &nbsp;</td>" : '';
																	echo "<td>".$vi_contacts[$vi_list[$box]]." &nbsp;</td></tr>";
																}
															}
														}
													?>
													</table>
												</div>
											</div>
										</td>
									</tr>
									<!-- END -->								
									
									<tr><td class="dottedLine" colspan="2">&nbsp;</td></tr>
									<tr style="background-color:#F4F4F4;height:100px;">
										<td>&nbsp;</td>
										<td>
											<?=tep_div_button(1, '&nbsp;'. IMAGE_BUTTON_ADD_FRIENDS .'&nbsp;', 'vi_add_friends_form', 'id="btn_add_friend" style="float:left;display:'. $add_visible .'"', 'green_button', false, 'assign_vi_value(document.vi_add_friends_form, \'get_contacts\')')?>
											<?=tep_div_button(1, '&nbsp;'. IMAGE_BUTTON_INVITE .'&nbsp;', 'vi_add_friends_form', 'id="btn_invite" style="padding-right:10px; float:left;display:'. $visible .'"', 'green_button', false, 'assign_vi_value(document.vi_add_friends_form, \'send_invites\')')?>
											<?=tep_div_button(1, '&nbsp;'. BUTTON_CANCEL .'&nbsp;', 'vi_add_friends_form', 'style="float:left;display:'.$visible .'"', 'green_button', false, 'assign_vi_value(document.vi_add_friends_form, \'\')')?>
										</td>
									</tr>
								</table>
							</form>
						</span>
						
								
						<span id="tab2" style="display:none;">
							<?=tep_draw_form('vi_add_mannually_form', tep_href_link(FILENAME_INVITER, '', 'SSL'), 'post', '')?>
								<?=tep_draw_hidden_field('step', $step, 'id="step"') ?>
								<?=tep_draw_hidden_field('vi_session_id', $_REQUEST['vi_session_id'], 'id="vi_session_id"') ?>
								<table border="0" width="100%" cellspacing="0" cellpadding="0" class="tabContentTable">
									<tr><td style="height:20px;">&nbsp;</td></tr>
									<tr>
										<td style="width:80px;vertical-align:top;text-align:left;padding:0 20px;"><?=ENTRY_FROM ?>&nbsp;</td>
										<td colspan="2"><b><?=tep_get_customers_email($customer_id) ?></b>&nbsp;</td>
									</tr>
									<tr><td colspan="3" class="breakLine">&nbsp;</td></tr>
									<tr>
										<td style="width:80px;vertical-align:top;text-align:left;padding:0 20px;"><?=ENTRY_TO ?>&nbsp;</td>
										<td colspan="2"><?=tep_draw_textarea_field('mi_to', 'soft', '60', '5', '', 'id="mi_to"')?></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td colspan="2"><span style="font-size:10px;color:#A3A3A3;"><?=TEXT_USE_COMMAS_TO_SEPARATE_EMAILS ?></span></td>
									</tr>
									<tr><td colspan="3" class="breakLine">&nbsp;</td></tr>
									<!-- START inviter's contact list -->
									<tr><td class="dottedLine" colspan="3">&nbsp;</td></tr>
									
									<tr style="background-color:#F4F4F4;height:80px;">
										<td style="width:80px;">&nbsp;</td>
										<td style="padding:20px 0;">
											<?=tep_div_button(1, '&nbsp; '. IMAGE_BUTTON_INVITE .' &nbsp;', 'vi_add_mannually_form', 'style="float:left; padding-right:10px;"', 'green_button', false, 'assign_vi_value(document.vi_add_mannually_form, \'add_manually\')') ?>
											<?=tep_div_button(1, '&nbsp; '. BUTTON_CANCEL .' &nbsp;', 'vi_add_mannually_form', 'style="float:left; padding-right:10px;"', 'green_button', false, 'assign_vi_value(document.vi_add_mannually_form, \'\')')?>
										</td>
										<td style="width:50%;padding:0 5px;"><span style="font-size:10px;color:#A3A3A3;"><?=TEXT_MANNUALLY_INVITE_CANCEL ?></span></td>
									</tr>
									<!-- END -->
								</table>
							</form>
						</span>
						
					</div>
					<!-- End of VIRAL INVITER | select_link_zones -->

					<div style="position: relative;width:100%;">
						<div style="position: absolute;width:100%;z-index:2;">
							<div class="tabFooterLeft" style="background-image:url(/images/campaign_bottom_left.png);"></div>
							<div class="tabFooterCenter"></div>
							<div class="tabFooterRight" style="background-image:url(/images/campaign_bottom_right.png);"></div>
						</div>
						<div style="position: absolute;width:100%;z-index:1;">
							<div class="tabFooterCenterLine" style="background-image: url(images/campaign_center_bottom_gray.png);"></div>
						</div>
					</div>
					<div class="breakLine" style="height:15px;">&nbsp;</div>
				</div>

			</td>
		</tr>
	</table>
</div>
<script>
	var hidden_id;
	
	jQuery.noConflict();
	jQuery(document).ready(function () {
		//adjust_alignment();
		
		var js_step = "<?=$_REQUEST['step']?>";
		var js_page = "<?=$_REQUEST['page']?>";
		document.getElementById('vi_password').value = '';
		
		if (js_step =='add_mannually') {
			switch_tab('tab2');
		} else if (js_step == 'get_contacts' || js_step == 'send_invites') {
			switch_tab('tab1');
		}
	});
	
	function adjust_alignment (tab_id) {
		if (tab_id == "" || tab_id == undefined) {		
			var tab_content_width = jQuery("#tab_content")[0].clientWidth;		
			tab_content_width = eval(tab_content_width + "-" + 10);		
			jQuery(".tabHeaderCenter").width(tab_content_width);	
		}
	}
	
	function switch_tab(tab_id) {
		jQuery("#"+tab_id+"_selected").css({opacity: "100"});	
		jQuery("#"+tab_id).show();	
		jQuery("#tab_content span").not("#"+tab_id).each(function(i){		
			jQuery("#"+this.id).hide();		
			jQuery("#"+this.id+"_selected").css({opacity: "0"});	
		});
		
		hidden_id = tab_id;
	}
	
	function row_tab (row_obj, classname, action) {	
		if (action == "Over") {		
			jQuery(row_obj).removeClass();		
			jQuery(row_obj).addClass(classname+action);	
		} else {		
			jQuery(row_obj).removeClass();		
			jQuery(row_obj).addClass(classname+action);	
		}
	}
	
	var sstatus = '';
	function sort_status(div_id) {
		if (sstatus == "block") {	jQuery("#" + div_id).css("display", "none"); sstatus="";	}
		else {	
			jQuery("#" + div_id).css("display", "block");
			jQuery("#" + div_id).css("margin-top", "3px");
			sstatus="block";
		}
	}
	
	function assign_vi_value(frm, str_value) {	
		if (str_value.length > 0) {
			frm.step.value = str_value;
		} else { 
			frm.step.value = ''; 
		}
		frm.submit();
	}
	
	function ok(box_element) {
		var maxchars = 100; 
		
 		if(box_element.value.length > maxchars) {
   			alert("<?=MESSAGE_LENGTH_EXCEEDED ?>" + (box_element.value.length - maxchars));
   			box_element.value = box_element.value.substring(0, maxchars);
   		}
   	}
</script>