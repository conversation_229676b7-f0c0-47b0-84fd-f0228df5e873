﻿<h1><?=HEADING_TITLE?></h1>
<div class="solidThickLine"></div>
<div class="breakLine"></div>

<?php if ($messageStack->size('inviter_x') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div>
			<div class="breakLine"><!-- --></div>
			<div style="padding-left:5px;"><?=$messageStack->output('inviter_x'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>

<?
if ($action != 'success_optout') {
?>
<div class="loginColumnBorder" style="padding:20px;width:95%">
	<?=tep_draw_form('inviter_x_form', tep_href_link(FILENAME_INVITER_X, '', 'SSL'), 'post', '')?>
		<?=tep_draw_hidden_field('code', $_REQUEST['code'], 'id="code"')  ?>
		<?=tep_draw_hidden_field('rec', $_REQUEST['rec'], 'id="rec"')  ?>
		<?=tep_draw_hidden_field('confirm_submit', '', 'id="confirm_submit"')  ?>
		<table align="center" border="0" width="100%" cellpadding="0" cellspacing="0">
			<tr>
				<td style="width:50px;">&nbsp;</td>
				<td><?=MESSAGE_INVITE_EXPLANATION ?></td>
				<td style="width:50px;">&nbsp;</td>
			</tr>
			<tr><td colspan="3"><div class="breakLine">&nbsp;</div></td></tr>
			<tr>
				<td style="width:50px;">&nbsp;</td>
				<td><?=sprintf(MESSAGE_OPT_OUT_EMAIL, "<strong>".$invitee_email."</strong>") ?></td>
				<td style="width:50px;">&nbsp;</td>
			</tr>
			<tr><td colspan="3"><div class="breakLine">&nbsp;</div></td></tr>
			<tr>
				<td style="width:50px;">&nbsp;</td>
				<td><?=tep_draw_checkbox_field('email_optout', 'optout', false, 'id="email_optout" checked') . TEXT_OPT_OUT_EMAIL ?></td>
				<td style="width:50px;">&nbsp;</td>
			</tr>
			<tr><td colspan="3"><div class="breakLine" style="height:20px;">&nbsp;</div></td></tr>
			<tr>
				<td style="width:50px;">&nbsp;</td>
				<td>
					<div style="float:right;align:right;display:block;">
						<?=tep_div_button(1, '&nbsp;'. BUTTON_CONFIRM .'&nbsp;', 'inviter_x_form', 'name="btn_confirm" id="btn_confirm" style="float:left;"', 'green_button', false, 'check_input(document.inviter_x_form, document.getElementById(\'email_optout\'))')?>
						<?=tep_div_button(1, '&nbsp;'. BUTTON_CANCEL .'&nbsp;', 'inviter_x_form', 'name="btn_cancel" id="btn_cancel" style="float:left;"', 'green_button')?>
					</div>
				</td>
				<td style="width:50px;">&nbsp;</td>
			</tr>
		</table>
	</form>
</div>

<script language="javascript">
function check_input(form_element, element) {
	var form = form_element;
	if(element.checked) {
		document.getElementById('confirm_submit').value = "btn_confirm";
		form.submit();
	}
	else {
		alert('<?=ERROR_EMPTY_OPTION?>');
	}
}	
</script>
<?
}
?>