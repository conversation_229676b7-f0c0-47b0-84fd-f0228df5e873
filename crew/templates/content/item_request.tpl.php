<? echo tep_draw_form('item_request', tep_href_link(FILENAME_ITEM_REQUEST, 'action=send')); ?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
      <tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?
  if ($messageStack->size('contact') > 0) {
?>
      <tr>
        <td><?php echo $messageStack->output('contact'); ?></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
<?
  }

  if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'success')) {
?>
      <tr>
        <td class="main" align="center"><?=TEXT_SUCCESS?></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          <tr class="buttonBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
					<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
<?php
  } else {
?>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          <tr class="infoBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main"><?php echo ENTRY_NAME; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('name','','size=50'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_EMAIL; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('email','','size=50'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_REALM; ?></td>
              </tr>
              <tr>
                <td class="main">
                <?php 
                
                $groups_array[] = array('text' => 'Select Reamls',
                                        'id' => '');
                $groups_array[] = array('text' => 'USEAST',
                                        'id' => 'USEAST');
                $groups_array[] = array('text' => 'USWEST',
                                        'id' => 'USWEST');
                $groups_array[] = array('text' => 'EUROPE',
                                        'id' => 'EUROPE');
                $groups_array[] = array('text' => 'ASIA',
                                        'id' => 'ASIA');
                echo tep_draw_pull_down_menu('categories_id', $groups_array, '','');
                ?>&nbsp;&nbsp;
                <? echo ENTRY_REALM_LADDER; ?>&nbsp;<? echo tep_draw_checkbox_field('ladder', ''); ?>
                &nbsp;&nbsp;<? echo ENTRY_REALM_HARDCORE; ?>&nbsp;<? echo tep_draw_checkbox_field('hardcore', ''); ?>
                </td>
              </tr>
                            
              <tr>
                <td class="main"><?php echo ENTRY_ITEM_NAME; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('item','','size=50'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_ITEM_QTY; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('qty','','size=8 maxlength=3'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_PRICE; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('price'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_COMMENTS; ?></td>
              </tr>
              <tr>
                <td class="main">
                <?php 
                //echo tep_draw_textarea_field('comments','','50','10'); 
                echo tep_draw_textarea_field('comments', 'soft', '50', '5');
                ?>
                </td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          <tr class="buttonBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
                	<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'item_request', 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
<?php
  }
  
?>

    </table></form>

