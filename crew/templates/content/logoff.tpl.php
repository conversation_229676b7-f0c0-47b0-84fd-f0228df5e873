<div class="vspacing"></div>
<div style="paddingfloat:left;width:600px">
<? ob_start(); ?>
<div class="breakLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td class="spacingInfo" style="padding:20px"><?=TEXT_MAIN?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
				<tr class="buttonBoxContents">
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								<td align="right">
									<?=tep_image_button2('green', tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), IMAGE_BUTTON_CONTINUE) ?>
								</td>
								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
<?
$logoff_info_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box(HEADING_TITLE,$logoff_info_content_string,11); 
?>
</div>

