<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script type="text/javascript">
<!--
	var global_loading_message = 'Loading';
	
    function showError(msg_id) {
    	var errorMsg = '';
		if (msg_id == 1) {
    		errorMsg = '<?=TEXT_QUANTITY_INVALID?>';
    	} else if (msg_id == 2) {
    		errorMsg = '<?=TEXT_FORM_INCOMPLETE?>';
    	} else if (msg_id == 3) {
    		errorMsg = '<?=TEXT_NOT_ACCEPT_BUYBACK?>';
    	} else if (msg_id != null) {
    		errorMsg = msg_id;
    	}

        document.getElementById('fvl_tbody_error').className = 'hide';
        document.getElementById('fvl_span_error').innerHTML = errorMsg;
        document.getElementById('fvl_tbody_error').className = 'show';
    }
	
	function set_selected_game(game_cat_id, server_cat_id) {
		//Simulate changing game list dropdown.
		if (server_cat_id == 'undefined') {
			server_cat_id = '0';
		}
		var gameListObj = document.getElementById('fvl_input_game_select');
		var languages_id = '<?=$languages_id?>';
		gameListObj.value = game_cat_id;
		onFavLinksGameSelection(gameListObj, languages_id, server_cat_id);
	}
//-->
</script>
<?
if ($messageStack->size($content) > 0) { 
	echo $messageStack->output($content);
}
?>
<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"><!-- --></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
<? ob_start();?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding-left:0px;padding-right:0px;">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0" >
							<tbody class="<?=($fvl_error_found) ? 'show' : 'hide'?>" id="fvl_tbody_error" name="fvl_tbody_error">
								<tr>
									<td align="left" valign="top" class="errorText">
										<div id="fvl_icon_error" name="fvl_icon_error" style="float: left;"><?=tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_WARNING)?></div>
										<div id="fvl_span_error" name="fvl_span_error"><?=$fvl_error_message?></div>
									</td>
								</tr>
							</tbody>
							<tr>
								<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
							</tr>
							<tbody class="<?=($fvl_notice_found) ? 'show' : 'hide'?>" id="fvl_tbody_notice" name="fvl_tbody_notice">
								<tr>
									<td align="left" valign="top" class="noticeText">
										<div id="fvl_icon_notice" name="fvl_icon_notice" style="float: left;"></div>
										<div id="fvl_span_notice" name="fvl_span_notice"><?=$fvl_notice_message?></div>
									</td>
								</tr>
							</tbody>
							<tr>
								<td><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							</tr>
						</table>
						<?=tep_draw_form('add_favourite_link', tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=add', 'SSL'))?>
							<table border="0" cellspacing="2" cellpadding="2">
								<tr>
									<td colspan="10"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
								<tbody class="show" id="fvl_tbody_step1" name="fvl_tbody_step1">
									<tr>
										<td rowspan="8" width="1" >&nbsp;</td>
										<td valign="top" class="inputField"><?=TEXT_GAME?></td>
										<td rowspan="8" width="1" >&nbsp;</td>
										<td valign="top">
											<?=tep_draw_pull_down_menu('fvl_input_game_select', $fvl_game_list_arr, (isset($form_values_arr['fvl_input_game_select']) && $form_values_arr['fvl_input_game_select'] ? $form_values_arr['fvl_input_game_select'] : ''), ' id="fvl_input_game_select" onChange="onFavLinksGameSelection(this, \''.$languages_id.'\', \'0\');" ')?>
											<div name="fvl_div_msgField1" id="fvl_div_msgField1" class="main"></div></td>
										<td rowspan="8" width="1">&nbsp;</td>
										<td valign="top" class="inputField"><?=TEXT_SERVER?></td>
										<td rowspan="8" width="1">&nbsp;</td>
										<td valign="top">
											<?=tep_draw_pull_down_menu('fvl_input_product_select', $fvl_server_list_arr, (isset($form_values_arr['fvl_input_product_select']) && $form_values_arr['fvl_input_product_select'] ? $form_values_arr['fvl_input_product_select'] : ''), 'id="fvl_input_product_select" disabled')?>
										</td>
										<td rowspan="8" width="1" >&nbsp;</td>
										<td colspan="9" align="right" valign="top">
											<div class="hide" id="fvl_tbody_step2" name="fvl_tbody_step2">
												<?=tep_div_button(1, IMAGE_BUTTON_ADD,'add_favourite_link', ' style="float:right" id="fvl_button_add" name="fvl_button_add"', 'gray_button') ?>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
							<?=tep_draw_hidden_field('fvl_div_game_cat_id', '', ' id="fvl_div_game_cat_id" ')?>
						</form>
						<table border="0" width="100%" cellspacing="5" cellpadding="0">
							<tr>
								<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							</tr>
							<tr>
								<td colspan="9" align="right" class="inputField">
									<?=tep_draw_form('filter_by_game', tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=select_filter', 'SSL'))?>
										<?=TEXT_FILTER_BY . ' : ' . tep_draw_pull_down_menu('fvl_input_filter_game_select', $filter_by_game_arr, (isset($form_values_arr['fvl_input_filter_game_select']) ? $form_values_arr['fvl_input_filter_game_select'] : 0), 'onchange="this.form.submit();")')?>
									</form>
								</td>
							</tr>
							<tr>
								<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							</tr>
						</table>
            		</td>
            	</tr>
			</table>
<?
$my_favourite_links_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$my_favourite_links_content_string,13); 
?>			
		</td>
	</tr>
	<tr>
		<td>
<? ob_start();?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding-left:0px;padding-right:0px;">
				<tr><td class="infoBoxTitle" style="padding-left:12px;"><?=BUTTON_SEARCH?></td></tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
                			<tr>
                				<td>
                					<?=tep_draw_form('fav_form', tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=batch_action', 'SSL'))?>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td class="boxHeaderLeftTable">&nbsp;</td>
											<td class="boxHeaderCenterTable">
												<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr valign="center">
													<th width="20" align="center"><?=tep_draw_checkbox_field('select_all', '', false, 'id="select_all" onclick="javascript:void(setCheckboxes(\'fav_form\', \'select_all\',\'server_batch\'));"')?></th>
												<?
													echo "<th width={$col_titles[1]['width']} align={$col_titles[1]['align']}>{$col_titles[1]['title']}</th>";
													echo "<th width={$col_titles[7]['width']} align={$col_titles[7]['align']}>{$col_titles[7]['title']}</th>";
													echo "<th width={$col_titles[2]['width']} align={$col_titles[2]['align']}>{$col_titles[2]['title']}</th>";
													//echo "<td width={$col_titles[3]['width']} align={$col_titles[3]['align']}>{$col_titles[3]['title']}</th>";
													echo "<th width={$col_titles[3]['width']} align={$col_titles[3]['align']}>{$col_titles[3]['title']}</th>";
													echo "<th width={$col_titles[4]['width']} align={$col_titles[4]['align']}>{$col_titles[4]['title']}</th>";
													echo "<th width={$col_titles[5]['width']} align={$col_titles[5]['align']}>&nbsp;{$col_titles[5]['title']}</th>";
													echo "<th width={$col_titles[6]['width']} align={$col_titles[6]['align']}>{$col_titles[6]['title']}</th>";
												?>
												</tr>
											</table>
								    		</td>
								    		<td class="boxHeaderRightTable">&nbsp;</td>
								    		<td align="right" width="1">&nbsp;</td>
										</tr>
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td align="right" width="1">&nbsp;</td>
											<td >
												<table border="0" cellpadding="0" cellspacing="1" height="205" width="100%">
											    	<tbody>
											    	<tr>
											    		<td height="90%" colspan="<?=$num_titles?>" valign="top">
											    			<div id="fvl_div_fav_links" name="fvl_div_fav_links" style="position:relative; height:<?=($fav_links_num_rows > 0 ? '205' : '0')?>; width:100%; overflow:auto;"><?=$favLinksHTML?></div>
														</td>
											    	</tr>
							                    	<tr>
							                      		<td colspan="<?=$num_titles?>" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
							                    	</tr>
							                    	<tr>
								            			<td>
								            				<table border="0" width="100%" cellpadding="0" cellspacing="0">
																<tr>
																	<td width="45%" align="left">
																	<?
																		$batch_action_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
																									array('id' => 'AddPresale', 'text' => TEXT_HEADING_PRESALE_ADD_NOTIFY),
																									array('id' => 'RemovePresale', 'text' => TEXT_PRESALE_REMOVE_NOTIFY),
																									array('id' => 'RemoveFav', 'text' => SELECT_TEXT_DELETE_FAV)
																									);
																		echo tep_draw_pull_down_menu('batch_action', $batch_action_array, '', 'onChange="return confirmBatchAction(this)"');
																	?>
																	</td>
																	<td>
																		<div class="main"><?=sprintf(TEXT_LINKS_FOUND, '<span id="num_results">'.$fav_links_num_rows.'</span>');?>&nbsp;</div>
																	</td>
																</tr>
															</table>
								            			</td>
								            		</tr>
													
													<tr>
														<td height="5" colspan="<?=$num_titles?>"></td>
													</tr>
												    </tbody>
												</table>
											</td>
											<td align="right" width="1">&nbsp;</td>
											<td align="right" width="1">&nbsp;</td>
										</tr>
									</table>
									</form>
                				</td>
                			</tr>
                		</table>
                	</td>
                </tr>
			</table>
<?
$my_favourite_links_search_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$my_favourite_links_search_content_string,13); 
?>	
		</td>
	</tr>
</table>
		<div class="break_line"></div>
<script type="text/javascript">
<!--
<?
	$pre_select_game = isset($form_values_arr['fvl_input_game_select']) ? $form_values_arr['fvl_input_game_select'] : '0';
	if ($pre_select_game) {
		echo "set_selected_game('".$pre_select_game."', '0');\n";
	}
?>
	function confirmBatchAction(selObj) {
		if (trim_str(selObj.value) != '') {
			if (trim_str(selObj.value) == 'RemoveFav') {
				var agree = confirm('<?=JS_CONFIRM_DELETE_FAV?>');
				if (agree) {
					selObj.form.submit();
					return true;
				}
			} else {
				selObj.form.submit();
				return true;
			}
		}
		
		selObj.selectedIndex = 0;
		return false;
	}
//-->
</script>