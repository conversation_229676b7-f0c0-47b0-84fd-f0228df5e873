<?
$infolinks_groups_id = '15'; //hardcode group id
$inforlink_select_sql = "SELECT i.infolinks_id, i.infolinks_title, ic.infolinks_contents_id  
                         FROM ". TABLE_INFOLINKS ." AS i
                         INNER JOIN ". TABLE_INFOLINKS_CONTENTS ." AS ic 
                            ON (i.infolinks_id=ic.infolinks_id)
                         WHERE i.infolinks_groups_id='". $infolinks_groups_id ."'
                         ORDER BY i.infolinks_sort_order, i.infolinks_id";

$inforlink_result_sql = tep_db_query($inforlink_select_sql);

$tips_text_flag = false;
$tips_text = '';

if (tep_db_num_rows($inforlink_result_sql) > 0) {
    while ($inforlink_row = tep_db_fetch_array($inforlink_result_sql)) {
	     $tips_text .= '&nbsp;<a onmouseover="this.className=\'showHandOver\';" onClick="openNewWin(\''. tep_href_link(FILENAME_INFOLINKS, 'action=order_history&id='. $inforlink_row['infolinks_id'] .'&content_id='. $inforlink_row['infolinks_contents_id']) .'\');">'. $inforlink_row['infolinks_title'] .'</a><br>';
	}
	$tips_text_flag = true;
}
?>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>ajaxfileupload.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script type="text/javascript">
<!--
	var divhtml = '';
	var countdown_secs_default = 60;
	var countdown_secs = 0;
	var coundown_active = false;
	var lockdown_form_secs = 2;
	var SID = '<?=SID?>';
	var languages_id = '<?=$languages_id?>';
	var history_timerID = 0;
	var global_loading_message = 'Loading...';
	
	jQuery(document).ready(function(){
		show_initial_record();
	});
				
	function show_initial_record () {
		var order_status_id = document.getElementById('odh_input_order_status_select').value;
		var product_type = document.getElementById('odh_input_product_type_select').value;
		var order_no = document.getElementById('odh_input_order_no').value;
		var game_cat_id = document.getElementById('odh_input_game_select').value;
		var start_date = document.getElementById('odh_input_start_date').value;
		var end_date = document.getElementById('odh_input_end_date').value;
		search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
	}
	
	function upload_ss (req_grp_id) {
		var display_html = '';
		display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action", "history_type", "startdate")), "POST", "enctype=\'multipart/form-data\'")?>';
		display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
		display_html += '<table border="0" width="500" style="font-weight:normal;font-size:12px;">';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SS_REF_TITLE?></td><td><?=TEXT_SS_REF_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
		display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
		display_html += '<tr valign="top"><td style="text-align:right;" nowrap><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_1", "", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", "file", true)?></td></tr>';
		display_html += '<tr valign="top"><td style="text-align:right;" nowrap><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_2", "", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", "file", true)?></td></tr>';
		display_html += "</table>";
		display_html += "</form>";
		
		jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');

		jQuery('#jconfirm_submit').click(function() { 
			ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'processing', display_html);
			jquery_confirm_box(display_html+'<table><tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');
		});
	}
	
	function ajaxFileUpload (req_grp_id, file_id_1, file_id_2, loading_id, bb_status, display_html)
	{
		jQuery.ajaxFileUpload
		(
			{
				url:'<?=tep_href_link("supplier_xmlhttp.php")?>?action=redirect_upload_to_curl&req_grp_id='+req_grp_id+'&file_name_1='+file_id_1+'&file_name_2='+file_id_2+'&sup_language=<?=$sup_language?>',
				secureuri:false,
				fileClass:'upload_class',
				dataType: 'xml',
				success: function(xml){
			     	jQuery(xml).find('response').each(function(){
						var validation_result = jQuery("validation_result", this).text();
						var error_msg_3 = jQuery("error_msg_3", this).text();
						
						if (validation_result != 'done' && error_msg_3 == '') {
							var error_msg_1 = jQuery("error_msg_1", this).text();
							var error_msg_2 = jQuery("error_msg_2", this).text();
							var error_html = '';
							var display_with_error_html = '';
							
							error_html += "<table border='0' width='100%' style='display:block;color:red;font-size:12px;'>";
							error_html += '<tr><td style="font-weight:bold;"><?=TEXT_ERROR_MSG?>:</td></tr>';
							
							if (error_msg_1 != '') {
								error_msg_1 = get_error_msg(error_msg_1);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>]</td><td>'+error_msg_1+'</td></tr>';	
							}
							if (error_msg_2 != '') {
								error_msg_2 = get_error_msg(error_msg_2);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>]</td><td>'+error_msg_2+'</td></tr>';	
							}
							
							if (error_msg_1 == '' && error_msg_2 == '') {
								var error_msg = '<?=ERROR_UPLOAD_FAILED?>';
								error_html += '<tr><td>&nbsp;</td><td>'+error_msg+'</td></tr>';	
							}
							
							error_html += "</table>";
							
							display_with_error_html = error_html + display_html; 
							jquery_confirm_box(display_with_error_html, 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');
							
							// OK button
							jQuery('#jconfirm_submit').click(function() { 
								ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', bb_status, display_html);
								jquery_confirm_box(display_html+'<table><tr><td><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');
							});
							
							// Cancel button
							jQuery('#jconfirm_cancel').click(function() { 
								jQuery.unblockUI();	
								
								if (bb_status == 'verifying') {
									var order_status_id = document.getElementById('odh_input_order_status_select').value;
									var product_type = document.getElementById('odh_input_product_type_select').value;
									var order_no = document.getElementById('odh_input_order_no').value;
									var game_cat_id = document.getElementById('odh_input_game_select').value;
									var start_date = document.getElementById('odh_input_start_date').value;
									var end_date = document.getElementById('odh_input_end_date').value;
									search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
								}
								
							});
						} else {
							jQuery.unblockUI();
							var order_status_id = document.getElementById('odh_input_order_status_select').value;
							var product_type = document.getElementById('odh_input_product_type_select').value;
							var order_no = document.getElementById('odh_input_order_no').value;
							var game_cat_id = document.getElementById('odh_input_game_select').value;
							var start_date = document.getElementById('odh_input_start_date').value;
							var end_date = document.getElementById('odh_input_end_date').value;
							search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
						}
			      });
			   }
			}
		)
		return false;
	}
	
	function get_error_msg (msg) {
		var result = '';
		switch (msg) {
			case '1':
			case '2':
			case '10':
				result = '<?=ERROR_FILE_SIZE_EXCEEDED?>';
			break;
			case '3':
				result = '<?=ERROR_UPLOAD_PARTIAL?>';
			break;
			case '4':
				result = '<?=WARNING_NO_FILE_UPLOADED?>';
			break;
			case '6':
				result = '<?=ERROR_NO_TMP_DIR?>';
			break;
			case '8':
				result = '<?=ERROR_NO_UPLOAD_FILE?>';
			break;
			case '9':
				result = '<?=ERROR_FILETYPE_NOT_ALLOWED?>';
			break;
			default:
				result = '<?=ERROR_DESTINATION_NOT_WRITEABLE?>';
			break;
		}
		return result;
	}
	
	function check_file_ext(path){
		var ext = path.split(".");
		var file_ext = ext[ext.length-1].toLowerCase();
		if(file_ext == "jpg" || file_ext == "jpeg"){
			return true;
		} else {
			return false;
		}
	}
	
	function checking_file(file_id){
		if (typeof(file_id) == 'undefined' || file_id == null) {	
	  		return;
	  	} else {
			if (!check_file_ext(file_id.value)){
				alert ("<?=JS_INVALID_SS_FILE_EXT?>\n");
				file_id.value = '';
			}
		}
	}
	
	function validate_confirmed_qty(req_id, req_grp_id) {
		jQuery("#confirm_"+req_grp_id).hide();
		stop_coundown_activity();
		var customer_id = '<?=$customer_id?>';
		var sent_qty = parseInt(document.getElementById('sent_qty_'+req_grp_id).value);
		//var delivered_id = document.getElementById('delivered_id_'+req_grp_id).value;
		var req_qty = parseInt(document.getElementById('request_qty_'+req_grp_id).value);
		var req_id = parseInt(req_id);
		
		jQuery.post("<?=tep_href_link('supplier_xmlhttp.php', 'action=update_buyback_request_group')?>",{
	    req_grp_id: req_grp_id, sent_qty: sent_qty, req_id: req_id, customer_id: customer_id
	    
	 	},function(xml){
		     jQuery(xml).find('response').each(function(){
		        var error_msg = jQuery("error_msg", this).text();
		        var result = jQuery("result", this).text();
		        
		        if (result == 'updated') {
<?
		        	if (BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
?>
			        	var display_html = '';
						display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action", "history_type", "startdate")), "POST", "enctype=\'multipart/form-data\'")?>';
						display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
						display_html += '<table border="0" width="100%" style="font-size:12px;">';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
						display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
						display_html += '<tr><td align="right" nowrap><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_1", "", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", "file", true)?></td></tr>';
						display_html += '<tr><td align="right" nowrap><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_2", "", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", "file", true)?></td></tr>';
						display_html += "</table>";
						display_html += "</form>";
						
						jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');
						
						jQuery('#jconfirm_submit').click(function() { 
							ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'verifying', display_html);
							jquery_confirm_box(display_html+'<table><tr><td><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>', '', '<?=BUTTON_OK?>', '<?=BUTTON_CANCEL?>');
						});
						
						jQuery('#jconfirm_cancel').click(function() { 
							//jQuery("#confirm_sent_"+req_grp_id).submit();
							var order_status_id = document.getElementById('odh_input_order_status_select').value;
							var product_type = document.getElementById('odh_input_product_type_select').value;
							var order_no = document.getElementById('odh_input_order_no').value;
							var game_cat_id = document.getElementById('odh_input_game_select').value;
							var start_date = document.getElementById('odh_input_start_date').value;
							var end_date = document.getElementById('odh_input_end_date').value;
							search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
						});
<?
		        	} else {
?>
		        		var order_status_id = document.getElementById('odh_input_order_status_select').value;
						var product_type = document.getElementById('odh_input_product_type_select').value;
						var order_no = document.getElementById('odh_input_order_no').value;
						var game_cat_id = document.getElementById('odh_input_game_select').value;
						var start_date = document.getElementById('odh_input_start_date').value;
						var end_date = document.getElementById('odh_input_end_date').value;
						search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
<?
		        	}
?>
		        } else {
		        	alert(error_msg);
		        	jQuery("#confirm_"+req_grp_id).show();
		        }
		   	 });
	  	});
	}

	function add_search_results_row(res_xml_obj, order_status_id, row_style) {
		if (typeof res_xml_obj != 'object') return;
		
		var custom_product_type_id = res_xml_obj.getElementsByTagName('custom_product_type_id')[0].firstChild.nodeValue;
		
		if (custom_product_type_id == 0) {
			// Currency Product
			var req_grp_id = res_xml_obj.getElementsByTagName('req_grp_id')[0].firstChild.nodeValue;
		  	var req_id = res_xml_obj.getElementsByTagName('req_id')[0].firstChild.nodeValue;
			var show_restock = trim_str(res_xml_obj.getElementsByTagName('show_restock')[0].firstChild.nodeValue);
			var restock_character = trim_str(res_xml_obj.getElementsByTagName('restock_character')[0].firstChild.nodeValue);
			var confirmed_qty = trim_str(res_xml_obj.getElementsByTagName('confirmed_qty')[0].firstChild.nodeValue);
			var game_name = res_xml_obj.getElementsByTagName('game_name')[0].firstChild.nodeValue;
			var delivery_method = res_xml_obj.getElementsByTagName('delivery_method')[0].firstChild.nodeValue;
			var server_name = res_xml_obj.getElementsByTagName('server_name')[0].firstChild.nodeValue;
			var req_qty = res_xml_obj.getElementsByTagName('req_qty')[0].firstChild.nodeValue;
			var amount = res_xml_obj.getElementsByTagName('amount')[0].firstChild.nodeValue;
			var status_name = res_xml_obj.getElementsByTagName('status_name')[0].firstChild.nodeValue;
			var show_expiry = res_xml_obj.getElementsByTagName('show_expiry')[0].firstChild.nodeValue;
			var expiry_time = res_xml_obj.getElementsByTagName('expiry_time')[0].firstChild.nodeValue;
			var buyback_remarks = res_xml_obj.getElementsByTagName('buyback_remarks')[0].firstChild.nodeValue;
			var total_queue = res_xml_obj.getElementsByTagName('total_queue')[0].firstChild.nodeValue;
			var current_status_id = res_xml_obj.getElementsByTagName('current_status_id')[0].firstChild.nodeValue;
			// allow_to_upload: for the user who havent upload the ss yet.
			var allow_to_upload = res_xml_obj.getElementsByTagName('allow_to_upload')[0].firstChild.nodeValue;
			
			if (req_grp_id == 'undefined' || req_grp_id == null)	return;
		
			if (req_id == 'undefined' || req_id == null)	return;
			
			divhtml += '<form name="confirm_sent_'+req_grp_id+'" id="confirm_sent_'+req_grp_id+'" action="<?=tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent')?>" method="post" enctype="multipart/form-data">'
					+ '<table width="100%" border="0" cellpadding="0" cellspacing="0">';
			
			divhtml += '<tr align="center">';
			
			divhtml += "<td class='ordersRecords' width='<?=$col_titles[0]['width']?>' style='padding:10px 0px 10px 0px;text-align:center;'>"+req_grp_id+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_titles[1]['width']?>' style='text-align:center;'>"+delivery_method+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_titles[2]['width']?>' style='text-align:center;'>"+game_name+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_titles[3]['width']?>' style='text-align:center;'>"+server_name+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_titles[4]['width']?>'>"+restock_character+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_titles[5]['width']?>' style='text-align:center;'>"+req_qty+"</td>";
	
			//if restock character available, let supplier fill in 2nd list qty
			divhtml += "<td class='ordersRecords' width='<?=$col_titles[6]['width']?>' align='center' style='text-align:center;'>";
			if (show_restock == '1' && (order_status_id == '1' || order_status_id == '0') && total_queue == '0') {
				divhtml += '<input type="hidden" name="request_qty['+req_grp_id+']" value="'+req_qty+'" id="request_qty_'+req_grp_id+'"><input type="text" name="sent_qty['+req_grp_id+']" size="3" maxlength="9" id="sent_qty_'+req_grp_id+'">';
				//divhtml += '<input type="hidden" name="request_qty['+req_grp_id+']" value="'+req_qty+'" id="request_qty_'+req_grp_id+'"><input type="text" name="delivered_id['+req_grp_id+']" size="3" maxlength="30" id="delivered_id_'+req_grp_id+'">';
			} else {
				divhtml += ""+confirmed_qty+"";
			}
			divhtml += "</td>";
	
			divhtml += "<td class='ordersRecords' width='<?=$col_titles[7]['width']?>' style='text-align:center;'>"+amount+"</td>";
	
			//Action Column
			divhtml += "<td class='ordersRecords' width='<?=$col_titles[8]['width']?>' style='text-align:center;'>"+status_name+"</td>";
			divhtml += "<td class='ordersRecords' width='<?=$col_titles[9]['width']?>' style='text-align:center;' nowrap>";
			if (current_status_id == 1) {
				//Pending or 'Draft'
				divhtml += '<input name="request_group_id" value="'+req_grp_id+'" type="hidden">' 
						+  '<input name="btn_action" id="btn_action" value="" type="hidden">'
						+  '<input name="request_id" value="'+req_id+'" type="hidden">';
						
				if (show_restock == '1' && total_queue == '0') {
					divhtml += 	'<div style="padding:3px 0px;text-align:center;" id="confirm_'+req_grp_id+'"><input name="request_qty['+req_grp_id+']" value="'+req_qty+'" type="hidden">' + 
								'<a href="javascript:void(0);" class="vip_button_link" onclick="return validate_confirmed_qty(\''+req_id+'\', \''+req_grp_id+'\');"><?=BUTTON_CONFIRM?></a></div>';
				}
				
				divhtml += '<div style="padding:3px 0px;text-align:center;"><a href="javascript:document.confirm_sent_'+req_grp_id+'.btn_action.value=\'cancel\';javascript:document.getElementById(\'confirm_sent_'+req_grp_id+'\').submit();" onClick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\');"><?=tep_image(DIR_WS_IMAGES . "icon-close.gif", "", "10", "10")?></a></div>';
			} else {
				//Button for popup report
				divhtml += '<div style="padding:3px 0px;text-align:center;"><a href="javascript:void(0);" class="vip_button_link" onclick="showMe(\''+req_grp_id+'\', \'<?=$languages_id?>\');"><?=TEXT_VIEW?></a><br><span class="title-text" id="wbb_div_msgField_'+req_grp_id+'"></span></div>';
				if (allow_to_upload == 'yes' && current_status_id != 4) {
					divhtml += '<div style="padding:3px 0px;text-align:center;"><a href="javascript:void(0);" class="vip_button_link" onclick="upload_ss(\''+req_grp_id+'\'); return false;"><?=TEXT_UPLOAD_SS?></a></div>';
				}
			}
			divhtml += "</td>";
		} else {
			// HLA Product
			var req_grp_id = res_xml_obj.getElementsByTagName('req_grp_id')[0].firstChild.nodeValue;
		  	var req_id = res_xml_obj.getElementsByTagName('req_id')[0].firstChild.nodeValue;
			var game_name = res_xml_obj.getElementsByTagName('game_name')[0].firstChild.nodeValue;
			var ref_no = res_xml_obj.getElementsByTagName('ref_no')[0].firstChild.nodeValue;
			var lvl = res_xml_obj.getElementsByTagName('lvl')[0].firstChild.nodeValue;
			var race_n_class = res_xml_obj.getElementsByTagName('race_n_class')[0].firstChild.nodeValue;
			var amount = res_xml_obj.getElementsByTagName('amount')[0].firstChild.nodeValue;
			var status_name = res_xml_obj.getElementsByTagName('status_name')[0].firstChild.nodeValue;
			var buyback_remarks = res_xml_obj.getElementsByTagName('buyback_remarks')[0].firstChild.nodeValue;
			var current_status_id = res_xml_obj.getElementsByTagName('current_status_id')[0].firstChild.nodeValue;
			var received = res_xml_obj.getElementsByTagName('received')[0].firstChild.nodeValue;
			var info_submitted = res_xml_obj.getElementsByTagName('info_submitted')[0].firstChild.nodeValue;
			
			if (req_grp_id == 'undefined' || req_grp_id == null)	return;
		
			if (req_id == 'undefined' || req_id == null)	return;
			
			divhtml += '<form name="confirm_sent_'+req_grp_id+'" id="confirm_sent_'+req_grp_id+'" action="<?=tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent')?>" method="post" enctype="multipart/form-data">'
					+ '<table width="100%" border="0" cellpadding="0" cellspacing="0">';
			
			divhtml += '<tr align="center" valign="top">';
			
			divhtml += "<td class='ordersRecords' width='<?=$col_hla_titles[0]['width']?>' style='padding:10px 0px;text-align:center;'>"+req_grp_id+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[1]['width']?>' style='padding:10px 0px;text-align:center;'>"+game_name+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[2]['width']?>' style='padding:10px 0px;text-align:center;'>"+ref_no+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[3]['width']?>' style='padding:10px 0px;text-align:center;'>"+lvl+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[4]['width']?>' style='padding:10px 0px;text-align:center;'>"+race_n_class+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[7]['width']?>' style='padding:10px 0px;text-align:center;'>"+amount+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[8]['width']?>' style='padding:10px 0px;text-align:center;'>"+status_name+"</td>"
					+ "<td class='ordersRecords' width='<?=$col_hla_titles[9]['width']?>' style='padding:10px 0px;text-align:center;'>";
			if (received == 0 && (current_status_id != 3 && current_status_id != 4)) {
				if (info_submitted == 0) {
					//Pending or 'Draft'
					divhtml += 	"<div style='padding:3px 0px;text-align:center;'><a href='buyback_order_form.php?bo_id="+req_grp_id+"'><?=BUTTON_SUBMIT?></a><div>"
							+ '<div style="padding:3px 0px;text-align:center;"><a href="javascript:void(0);" onClick="return cancel_buyback_order(\''+req_grp_id+'\');"><?=tep_image(DIR_WS_IMAGES . "icon-close.gif", "", "10", "10")?></a></div>'
							+  '<input name="request_group_id" value="'+req_grp_id+'" type="hidden">' 
							+  '<input name="btn_action" id="btn_action" value="" type="hidden">'
							+  '<input name="request_id" value="'+req_id+'" type="hidden">'
							+  '<input name="cancel_comment_'+req_grp_id+'" id="cancel_comment_'+req_grp_id+'" value="" type="hidden">';
				} else if (info_submitted == 1) {
					divhtml += 	"<div style='padding:3px 0px;text-align:center;'><a href='buyback_order_form.php?action=edit_info&bo_id="+req_grp_id+"'><?=BUTTON_EDIT?></a><div>"
							+  '<input name="cancel_comment['+req_grp_id+']" id="cancel_comment_'+req_grp_id+'" value="" type="hidden">';
				}	
			}
		}
		
		divhtml += "</tr>";

		if (show_expiry > 0) {
		
			divhtml += '<tr>'
					+  '	<td colspan="<?=(count($col_titles)+1)?>">'
					+  '		<table bgcolor="#e0ecf4" border="0" cellpadding="2" cellspacing="2" width="100%">'
					+  '			<tr>'
					+  '				<td>'
					+  '					<table border="0" cellpadding="2" cellspacing="1" width="100%">'
					+  '						<tr style="color:red;">'
					+  '							<td class="main" width="30%"><?=TEXT_EXPIRES?></td>'
					+  '							<td class="main" colspan="2" width="70%">' +  expiry_time + ' +10 <?=TEXT_MINUTES?></td>'
					+  '						</tr>'
					+  '					</table>'
					+  '				</td>'
					+  '			</tr>'
					+  '		</table>'
					+  '	</td>'
					+  '</tr>';
			

		}
		
		if (buyback_remarks.length) {
			divhtml += '<tr>';
			
			divhtml += '<td class="ordersRecords" bgcolor="#ffffcc" colspan="<?=(count($col_titles)+1)?>" style="padding:10px;">' + buyback_remarks + '</td>'
					+ '</tr>';
		}
		
		divhtml += '</table>'
				+ '</form>';
		
	}
	
	function show_search_results_div() {
		document.getElementById('odh_div_search_results').innerHTML = divhtml;
	}

	function toggleCountdown(cbChecked) {
		if (cbChecked == true) {
			start_coundown_activity();
		} else {
			stop_coundown_activity();
		}
	}
	function start_coundown_activity() {
		coundown_active = true;
		countdown_secs = countdown_secs_default;
		document.getElementById('timer_main').innerHTML = '<span id="timer_main" name="timer_main"><?=sprintf(TEXT_SECONDS_COUNTDOWN, '<span id="timer_display" name="timer_display"></span>')?></span>';
		doCountDown();
	}
	function stop_coundown_activity() {
		coundown_active = false;
		disableDivInputs(false);
		document.getElementById('timer_main').innerHTML = '<span id="timer_display" name="timer_display"></span>';
	}
	
	function submit_form() {
		document.order_history_search.submit();
	}

	function doCountDown(manual) {
		if (manual == null || !manual) {
			countdown_secs -= 1;
			document.getElementById('timer_display').innerHTML = countdown_secs;
			if (coundown_active == false) {
				document.getElementById('timer_main').innerHTML = '';
				return;
			}
		}
		
		if (countdown_secs > 0) {
			if (countdown_secs == lockdown_form_secs) {
				disableDivInputs(true);
			}
			history_timerID = setTimeout("doCountDown()",1000);
		} else {
			if (history_timerID) {
				clearTimeout(history_timerID);
				history_timerID = 0;
			}
			
			disableDivInputs(false);
			var order_status_id = document.getElementById('odh_input_order_status_select').value;
			var product_type = document.getElementById('odh_input_product_type_select').value;
			var order_no = document.getElementById('odh_input_order_no').value;
			var game_cat_id = document.getElementById('odh_input_game_select').value;
			var start_date = document.getElementById('odh_input_start_date').value;
			var end_date = document.getElementById('odh_input_end_date').value;
			search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
		}
	}

	function disableDivInputs(bool) {
		//Enter here during form lockdown
		document.getElementById('odh_input_order_status_select').disabled=bool;
		document.getElementById('odh_input_product_type_select').disabled=bool;
		document.getElementById('odh_input_order_no').disabled=bool;
		document.getElementById('odh_input_game_select').disabled=bool;
		document.getElementById('odh_input_start_date').disabled=bool;
		document.getElementById('odh_input_end_date').disabled=bool;
		document.getElementById('odh_button_search').disabled=bool;
		if (bool == true) {
			document.getElementById('odh_div_search_results').innerHTML = '<p class="main" valign="middle" align="center">Please wait for refresh.';
		}
	}

	function search_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id) {
		stop_coundown_activity();
		onOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id)
	}
	
	function showMe(buyback_req_grp_id, languages_id) {
		var server_action = 'show_order_report';
		var pop_out;

		if (buyback_req_grp_id != '' && languages_id != '') {
			pop_out_loading = '<table border="0" width="400" id="loading" cellspacing="0" cellpadding="0" align="center"><tr><td>';
			pop_out_loading += '<table width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
			pop_out_loading += '<tr><td><?=tep_draw_separator("pixel_trans.gif", "100%", "18")?></td></tr>';
			pop_out_loading += '<tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES . "loading.gif", "", "20", "20")?></td></tr>';
			pop_out_loading += '<tr><td><?=tep_draw_separator("pixel_trans.gif", "100%", "18")?></td></tr>';
			pop_out_loading += '</table>';
			pop_out_loading += '</td></tr></table>';
			
			jQuery().ajaxStart(function(){
				jQuery.blockUI.defaults.message = pop_out_loading;
			}).ajaxComplete(function(){
				jQuery("#loading").hide();
			});
			var mode = 0; // 1 = VIP, 0 = Normal
			var ref_url = "<?=tep_href_link('supplier_xmlhttp.php')?>?action="+server_action+"&buyback_req_grp_id="+buyback_req_grp_id+"&slang="+languages_id+"&SID="+SID+"&mode="+mode;
			
			jQuery.get(ref_url, function(xml){
				jQuery(xml).find('response').each(function(){
					var game_name = jQuery("game_name", this).text();
					var server_name = jQuery("server_name", this).text();
					var req_qty = jQuery("req_qty", this).text();
					var uom = jQuery("uom", this).text();
					var product_name = jQuery("product_name", this).text();
					var total_price = jQuery("total_price", this).text();
					var sender_character = jQuery("sender_character", this).text();
					var restk_character = jQuery("restk_character", this).text();
					var delivery_time = jQuery("delivery_time", this).text();
					var contact_name = jQuery("contact_name", this).text();
					var contact_no = jQuery("contact_no", this).text();
					var show_comments = jQuery("show_comments", this).text();
					var order_reference = jQuery("order_reference", this).text();
		      		
		      		pop_out = '<table width="447" border="0" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
					pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=HEADER_ORDER_REPORT?></b></div></td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_GAME?>:</td><td class="smallText">&nbsp;'+game_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_SERVER?>:</td><td class="smallText">&nbsp;'+server_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_QUANTITY?>:</td><td class="smallText">&nbsp;'+req_qty+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_TOTAL_PRICE?>:</td><td class="smallText">&nbsp;'+total_price+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CHAR_NAME?>:</td><td class="smallText">&nbsp;'+sender_character+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=ENTRY_RECEIVER_CHAR_NAME?>:</td><td class="smallText">&nbsp;'+restk_character+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_DELIVERY_TIME?>:</td><td class="smallText">&nbsp;'+delivery_time+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CONTACT_NAME?>:</td><td class="smallText">&nbsp;'+contact_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CONTACT_NO?>:</td><td class="smallText">&nbsp;'+contact_no+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_ADDITIONAL_COMMENTS?>:</td><td class="smallText">&nbsp;'+show_comments+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_ORDER_REFERENCE?>:</td><td class="smallText">&nbsp;'+order_reference+'</td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding: 20px 0 0"><!-- --></div></td></tr>';
					pop_out += '</table>';
					
					jQuery('#general_content').html(pop_out);
					jQuery('#general_content').css('padding', '0px');
					show_fancybox('general_popup_box');
				});
			});
		}
	}
	
	function hideMe() {
		jQuery.unblockUI();return false;
	}
	
    function popUp(URL) {
        eval("page = window.open(URL, '', 'width=640,height=480,top=100,left=100,scrollbars=yes');");
    }
    
    function cancel_buyback_order(bo_id) {
    	if (bo_id != '') {
			jQuery('#footerBar').css('display', 'none');
			set_fancybox_position();
			
			pop_out = '<?=tep_draw_form("buy_now", tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."action=buy_now&products_id="), "post", "")?>';
      		pop_out += '<table id="popup_dispute_box" width="100%" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
			pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=POPUP_HEADER_BUYBACK_ORDER_CANCEL?></b></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2">';
			pop_out += '<div id="popup_box_loading" style="text-align:center;display:none;">';
      		pop_out += '<?=tep_image(DIR_WS_IMAGES . "lightbox-ico-loading.gif", "", "32", "32", "style=\"padding:50px;\"")?>';
      		pop_out += '</div>';
      		pop_out += '<div id="popup_box_desc">';
			pop_out += '<div style="padding:12px 20px 5px 20px;font-weight:bold;"><?=POPUP_TEXT_DETAILS?><br /><?=sprintf(POPUP_LIMIT_CHARACTERS, '120')?></div><div style="padding:0 20px;"><?=tep_draw_textarea_field("cancel_comment", "", "53", "4", "", 'id="cancel_comment" maxlength="120"')?></div><div id="cancel_comment_error_msg" style="padding:0 20px;color:red;font-weight:bold;"></div>';
			pop_out += '<div class="dottedLine" style="padding: 20px 0 0"><!-- --></div>';
			pop_out += '<table border="0" cellspacing="0" cellpadding="10" align="center"><tr><td nowrap><div class="green_button_fix_width"><a href="javascript:cancel_buyback_order_submit(\''+bo_id+'\');"><span><font><?=BUTTON_SUBMIT ?></font></span></a></div></td></tr></table>';
			pop_out += '</table>';
			pop_out += '</form>';
			
			jQuery("#fancy_content").html(pop_out);
			jQuery(".fancy_close_footer").css('display', 'block');
			
			realign_fancybox("popup_dispute_box");
		}
	}
	
	function cancel_buyback_order_submit(bo_id) {
		var no_error = validate_char_length('cancel_comment', '120', 'cancel_comment_error_msg', '<?=POPUP_LIMIT_CHARACTERS_ERROR?>');
		if (no_error) {
			jQuery('#cancel_comment_'+bo_id).val(jQuery("#cancel_comment").val());
			document.getElementById('confirm_sent_'+bo_id).btn_action.value='cancel';
			document.getElementById('confirm_sent_'+bo_id).submit();
		}
	}
//-->
</script>
<h1><?=HEADER_TITLE_MY_ORDER_HISTORY?></h1>
<div class="breakLine"><!-- --></div>

<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
<? ob_start(); ?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding-left:0px;padding-right:0px;">
				<tr><td class="infoBoxTitle" style="padding-left:12px;"><?=BUTTON_SEARCH?></td></tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table cellpadding="2" border="0" cellspacing="0" width="100%">
		                <tbody>
		                    <tr>
		                      <td>
								<!-- Start inner table -->
								<?=tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action', 'startdate', 'odh_input_product_type_select')) . 'action=search'))?>
						    	<table border="0" width="100%" cellspacing="5" cellpadding="0" class="buttonBox">
				                	<tr>
				            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				            		</tr>
				            		<tbody class="show" id="odh_tbody_step1" name="odh_tbody_step1">
				                		<tr>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                		<td align="left" valign="top" class="search_title"><?=ENTRY_ORDER_STATUS?>:</td>
											<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                			<td align="left" valign="top" width="30%"><?=tep_draw_pull_down_menu('odh_input_order_status_select', $order_status_arr, (isset($form_values_arr['odh_input_order_status_select']) && $form_values_arr['odh_input_order_status_select'] ? $form_values_arr['odh_input_order_status_select'] : ''), ' id="odh_input_order_status_select" ')?>
											</td>
											<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                			<td align="left" valign="top" class="search_title"><?=ENTRY_PRODUCT_TYPE?>:</td>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                		<td align="left" valign="top"><?=tep_draw_pull_down_menu('odh_input_product_type_select', $product_type_arr, (isset($form_values_arr['odh_input_product_type_select']) && $form_values_arr['odh_input_product_type_select'] ? $form_values_arr['odh_input_product_type_select'] : ''), ' id="odh_input_product_type_select" ')?></td>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				                		<tr>
					                		<td align="left" valign="top" class="search_title"><?=ENTRY_ORDER_NUMBER?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('odh_input_order_no', (isset($form_values_arr['odh_input_order_no']) && $form_values_arr['odh_input_order_no'] ? $form_values_arr['odh_input_order_no'] : ''), 'id="odh_input_order_no" size="16" maxlength="16"')?></td>
				                			<td align="left" valign="top" class="search_title"><?=ENTRY_GAME?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_pull_down_menu('odh_input_game_select', $wbb_game_list_arr, (isset($form_values_arr['odh_input_game_select']) && $form_values_arr['odh_input_game_select'] ? $form_values_arr['odh_input_game_select'] : ''), ' id="odh_input_game_select" ')?></td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				                		<tr>
					                		<td align="left" valign="top" class="search_title"><?=ENTRY_START_DATE?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('odh_input_start_date', (isset($odh_input_start_date) && tep_not_null($odh_input_start_date) ? $odh_input_start_date : ''), ' id="odh_input_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.odh_input_start_date); }" onKeyPress="return noEnterKey(event)" ', '', false)?>
					                			<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.odh_input_start_date);return false;" HIDEFOCUS><img name="popcal1" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
					                		</td>
					                		<td align="left" valign="top" class="search_title"><?=ENTRY_END_DATE?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('odh_input_end_date', (isset($odh_input_end_date) && tep_not_null($odh_input_end_date) ? $odh_input_end_date : ''), ' id="odh_input_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.odh_input_end_date); }" onKeyPress="return noEnterKey(event)" ', '', false)?>
				                				<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.odh_input_end_date);return false;" HIDEFOCUS><img name="popcal2" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
				                			</td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				            		</tbody>
				                	<tr>
				            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				            		</tr>
				                	<tr>
				                		<td colspan="9" align="left" valign="top">
				                		    <table border="0" width="100%" cellspacing="2" cellpadding="0">
				                		        <tr>
				                		            <td valign="top" width="70%">
		                                            </td>
		                                            <td align="right" valign="top" width="15%"><?=tep_div_button(2, IMAGE_BUTTON_SEARCH,'javascript:stop_coundown_activity(); submit_form();', 'id="odh_button_search" name="odh_button_search"', 'gray_button') ?></td>
                                        			<td align="right" valign="top" width="15%"><?=tep_div_button(2, ALT_BUTTON_RESET,tep_href_link(FILENAME_MY_ORDER_HISTORY, 'action=reset_session'), '', 'gray_button') ?></td>
		                                        </tr>
		                                    </table>
				                		</td>
				            		</tr>
				            	</table>
						        </form>
								<!-- end inner table -->
							</td>
						</tr>
		                </tbody>
					</table>
            		</td>
            	</tr>
			</table>
<?
$buyback_order_search_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$buyback_order_search_content_string,13); 
?>
		</td>
	</tr>
	<tr>
		<td>
<? ob_start(); ?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding-left:0px;padding-right:0px;" valign="top">
				<tr class="infoBoxContents">
            		<td valign="top">
            			<table border="0" width="100%" cellspacing="0" cellpadding="0" valign="top">
            				<tr><td class="infoBoxTitle" style="padding:12px 12px 12px 12px;"><?=BUTTON_SEARCH?></td></tr>
                			<tr>
                				<td valign="top">
									<table border="0" cellpadding="0" cellspacing="0" width="100%" valign="top">
										<tr>
											<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
											<td class="boxHeaderLeftHigherTable">
												<?=tep_draw_separator('pixel_trans.gif', '1', '1')?>
												</td>
											<td class="boxHeaderCenterTable">
												<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr valign="center">
													<tbody id="regular_header" style="display:<?=$form_values_arr['odh_input_product_type_select'] == 'game_currency' ? 'block' : 'none';?>">
												<?
													foreach ($col_titles as $col_array) {
														echo "<th width='".$col_array['width']."' align='".$col_array['align']."'>{$col_array['title']}</th>";
													}
												?>
													<th width="4">&nbsp;</th>
													</tbody>
													<tbody id="hla_header" style="display:<?=$form_values_arr['odh_input_product_type_select'] == 'hla_product' ? 'block' : 'none';?>">
												<?
													foreach ($col_hla_titles as $col_hla_array) {
														echo "<th width='".$col_hla_array['width']."' align='".$col_hla_array['align']."'>{$col_hla_array['title']}</th>";
													}
												?>
													</tbody>	
												</tr>
											</table>
								    		</td>
								    		<td class="boxHeaderRightHigherTable"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								    		<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
										</tr>
										<tr>
											<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
											<td colspan="3">
												<table border="0" cellpadding="0" cellspacing="1" height="205" width="100%">
											    	<tbody>
											    	<tr>
											    		<td height="90%" colspan="<?=$num_titles?>" align="center">
											    			<div id="odh_div_search_results" name="odh_div_search_results" style="position:relative; height:205px; overflow:auto;text-align:center;"></div>
														</td>
											    	</tr>
							                    	<tr>
							                      		<td colspan="<?=$num_titles?>" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
							                    	</tr>
													<tr>
														<td align="center" colspan="<?=$num_titles?>">
															<table border="0" cellpadding='0' cellspacing='0' width='100%'>
																<tr>
																	<td align='center' colspan='<?=$num_titles?>'>
																		<div id='auto_refresh_bar' class='title-text'>
																			<table border="0" cellpadding='0' cellspacing='0' align="center">
																			<tr>
																				<td align='center'><?=sprintf(TEXT_RESULT_SEARCH, '<span id="num_results">&nbsp;</span>');?>&nbsp;</td>
																			</tr>
																			<tr>
																				<td align='center'>
																					<div style="text-align:center;">
																						<span id="timer_main" name="timer_main"><span id="timer_display" name="timer_display"></span></span>
																					</div>
																				</td>
																			</tr>
																			<tr>
																				<td align='center'>
																					<div class="title-text" align="center" style="margin:0px 8px;">
																						<?=tep_div_button(2, BUTTON_REFRESH,'javascript:countdown_secs=0;doCountDown(true);', 'style="float:left;"', 'gray_button') ?>
																					</div>
																				</td>
																			</tr>
																			</table>
																		</div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td height="5" colspan="<?=$num_titles?>"></td>
													</tr>
												    </tbody>
												</table>
											</td>
											<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
										</tr>
									</table>
                				</td>
                			</tr>
                		</table>
                	</td>
                </tr>
			</table>
<?
$buyback_order_search_result_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$buyback_order_search_result_content_string,13); 
?>
		</td>
	</tr>
</table>
<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
<script type="text/javascript">
<!--
<?
	if ($odh_auto_refresh_results_bool) {
		echo 'toggleCountdown(true);';
	}
?>
//-->
</script>