<? if (file_exists(DIR_FS_JAVASCRIPT . 'payment_xmlhttp.js.php')) { include_once (DIR_FS_JAVASCRIPT . 'payment_xmlhttp.js.php'); } ?>
<?
	$notice_message_obj = new messageStack;
	$notice_message_obj->add('notice_message', TEXT_ANNOUNCEMENT, 'notice_box');
?>

<!--spiffycal start-->
<script language="javascript" src="includes/general.js"></script>
<link rel="styleSheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>spiffyCal/spiffyCal_v2_1.css">
<script type="text/javascript" SRC="<?=DIR_WS_JAVASCRIPT?>spiffyCal/spiffyCal_v2_1.js"></script>
<script type="text/javascript" SRC="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'xmlhttp.js'?>"></script>
<div id="spiffycalendar" class="text"></div>
<!--spiffycal end -->

	<table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<table width="100%" height="30" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
		</tr>
<?
if ($notice_message_obj->size('notice_message') > 0) {
?>
		<tr>
			<td>
				<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); float: left;">
					<div>
						<div class="breakLine"><!-- --></div>
						<div style="display: block; color: black;"><b><?=$notice_message_obj->output('notice_message')?></b></div>
						<div class="breakLine"><!-- --></div>
					</div>
				</div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
		</tr>
<?
}

	if (tep_not_empty($form_content)) {
?>
		<tr>
			<td>
				<table cellpadding="2" border="0" cellspacing="0" width="100%">
					<tr>
						<td>
<?
	switch($action) {
		case 'show_report':
			echo $page_obj->get_html_simple_rc_box('',$form_content,13);
		    break;
		case 'show_request':
			//Select account type
			echo $page_obj->get_html_simple_rc_box('',$form_content,13);
			break;
		case 'show_balance':
			echo $page_obj->get_html_simple_rc_box('',$form_content,13);
			break;
		case 'confirm_withdraw':
			//Select account type
			echo $page_obj->get_html_simple_rc_box('',$form_content,13);
			break;
	}
?>
						</td>
					</tr>
				</table>
			</td>
		</tr>
<?php
	}
?>
		<tr>
			<td>
				<table cellpadding="2" border="0" cellspacing="0" width="100%">
					<tr>
						<td>
<? ob_start(); ?>
								<div class="breakLine"></div>
								<div class="boxHeader" style="width:100%;">
									<div class="boxHeaderLeft"><!-- --></div>
									<div class="boxHeaderCenter" style="width:720px;display:inline;">
										<font style="display:inline-block; color:#fff; padding-top:5px;"><?=QUESTION_AND_ANSWER_HEADER ?></font>
									</div>
									<div class="boxHeaderRight"><!-- --></div>
								</div>
								<div class="breakLine"><!-- --></div>
								
								<div style="padding:5px;" class="langInfoSizeMedium langInfoLineHeight1_5">
									<?=QUESTION_AND_ANSWER_CONTENTS?>
								</div>
<?
$acc_statement_qna_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$acc_statement_qna_content_string,13); 
?>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	<div><iframe width="188" height="166" name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
<!-- Floating div start -->
<link type="text/css" rel="styleSheet" src="<?=DIR_WS_JAVASCRIPT?>DimmingDiv/dimming.css">
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>DimmingDiv/dimmingdiv.js"></script>
<script type="text/javascript">
	backupFloatingDivHTML('payment_report');
</script>
<!-- Floating div end -->