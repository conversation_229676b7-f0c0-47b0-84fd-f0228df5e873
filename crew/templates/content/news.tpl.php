<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}

$cat_id = $HTTP_GET_VARS["cPath"];
$news_id = (int)$HTTP_GET_VARS["news_id"];
?>
			<h1><?=HEADING_TITLE ?></h1>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
	  	<?
		$this_site_id = 0;
		if (defined('SITE_ID')) {
			$this_site_id = SITE_ID;
		}
		$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
	 	
	  	if ($news_id > 0) {
 			$listing_sql = "select lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. "  as ln inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd on (ln.news_id = lnd .news_id) where ln.status = '1' and $news_display_sites_where_str 
						    and ( if(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
 								  if ((select count(lnd.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
 										where " . $news_display_sites_where_str . " 
 										and lnd.news_id ='".$news_id."' 
 										and lnd.language_id ='". $languages_id. "'
										and lnd.headline <> ''), 
										0,
				 						lnd.language_id = '".$default_languages_id."')
										)
									 )
						    and lnd.news_id ='".$news_id."'"; 
	  	} else if (tep_not_null($cat_id)) {
			$cat_path_array = explode('_', $cat_id);
			$last_cat_ids_array = array(end($cat_path_array), -999);
			
			$news_groups_cat = array(0);
			$news_select_sql = "	SELECT news_id 
									FROM ".TABLE_LATEST_NEWS_CATEGORIES."  
									WHERE categories_id IN ('".implode("','", $last_cat_ids_array)."')";
			$news_result_sql = tep_db_query($news_select_sql);
			while ($news_row = tep_db_fetch_array($news_result_sql)) {
				$news_groups_cat[] = $news_row['news_id'];
			}
			
			$listing_sql = "select lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. "  as ln inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd on (ln.news_id = lnd .news_id) where ln.status = '1' and $news_display_sites_where_str 
 							    and ( if(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
	 								  if ((select count(lnd.news_id) > 0 from " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
	 										where " . $news_display_sites_where_str . " 
	 										and lnd.news_id ='".$news_id."' 
	 										and lnd.language_id ='". $languages_id. "'
											and lnd.headline <> ''), 
											0,
 					 						lnd.language_id = '".$default_languages_id."')
 											)
 										 )
 							    and news_groups_id='".$_REQUEST["news_type"]."' 
 							    and ln.news_id IN ('".implode("','", $news_groups_cat)."') 
 							    order by date_added DESC"; 
 			unset($news_groups_cat);
		} else {
			//$listing_sql = 'select news_id, headline, content, date_added, url from ' . TABLE_LATEST_NEWS . " news where status = '1' and $news_display_sites_where_str and language = '". $languages_id. "' and news_groups_id='".$_REQUEST["news_type"]."' order by date_added DESC";
			$listing_sql = "select lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. "  as ln inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd on (ln.news_id = lnd .news_id) where ln.status = '1' and $news_display_sites_where_str 
 							    and ( if(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
	 								  if ((select count(lnd.news_id) > 0 from " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
	 										where " . $news_display_sites_where_str . " 
	 										and lnd.news_id ='".$news_id."' 
	 										and lnd.language_id ='". $languages_id. "'
											and lnd.headline <> ''), 
											0,
 					 						lnd.language_id = '".$default_languages_id."')
 											)
 										 )
 							    and news_groups_id='".$_REQUEST["news_type"]."' 
 							    order by date_added DESC";
		}
		
		if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) {
			$listing_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_LATEST_NEWS_PAGE, $listing_sql, $listing_numrows);
			$listing_query = tep_db_query($listing_sql);
		} else {
			$listing_split = new splitPageResults($listing_sql, MAX_DISPLAY_LATEST_NEWS_PAGE);
			$listing_numrows = $listing_split->number_of_rows;
			$listing_query = tep_db_query($listing_split->sql_query);
		}
		if ( ($listing_numrows > 0) && ( (PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3') )) {
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
  				<tr>
    				<td class="pageResultsText" align="right">
					<?
					if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) { 
						echo TEXT_RESULT_PAGE . ' ';
                        $listing_split->display_links($listing_numrows, MAX_DISPLAY_LATEST_NEWS_PAGE, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y')));                       
					} else {
                        echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')));
                    }
                    ?>
                    </td>
    				<td class="pageResultsText" align="right">&nbsp;</td>
  				</tr>
			</table>
<?  	}
		
		$info_box_contents = array();
		$news_group_name_select_sql = "SELECT news_groups_name FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " WHERE news_groups_id='" . $_REQUEST["news_type"] . "' AND language_id = '" . $_SESSION["languages_id"] . "'";
		$news_group_name_result_sql = tep_db_query($news_group_name_select_sql);
		if ($news_group_name_row = tep_db_fetch_array($news_group_name_result_sql)) {
			$info_box_contents[] = array(	'align' => 'left',
	    									'params' => 'class="latestNewsBoxHeading"',
	           	    	                  	'text'  => $news_group_name_row["news_groups_name"]);
			new contentBoxHeading($info_box_contents);
		}
	
		$count = 0;
		while ($listing = tep_db_fetch_array($listing_query)) {
			$info_box_contents = array();
		  	if(isset($HTTP_GET_VARS["news_id"]) AND $HTTP_GET_VARS["news_id"] == $listing["news_id"]) {
		  		$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
		  										'text' => 	'<table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'
		                                        			.'<td align="left">'
		                                        			.'<div class="latestNewsTitle">'.eval_html($listing["headline"]).'</div>'
		                                        			.'</td>'
		                                        			.'<td align="right">'
		                                        			.'<div class="latestNewsDate">'.tep_date_long($listing["date_added"].'</div>')
		                                        			.'</td>'
		                                        			."</tr></table>\n"
											);
			} else {
				$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
												'text' => 	'<table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'
	                             				.'</td>'
	                                  			."</tr></table>\n"
											);
			}
			//  new infoBoxHeading($info_box_contents, true, true);
		    $info_box_contents = array();
		    
		    if (preg_match_all("/(?:#DC_)([^#]+)(?:#)?/is", $listing['content'], $sys_token_array)) {
				if (count($sys_token_array[1])) {
					for ($sysCnt=0; $sysCnt < count($sys_token_array[1]); $sysCnt++) {
						$coupon_stat_select_sql = "	SELECT (c.uses_per_coupon - COUNT(crt.unique_id)) AS remaining_uses
													FROM " . TABLE_COUPONS . " AS c 
													LEFT JOIN " . TABLE_COUPON_REDEEM_TRACK . " AS crt 
														ON c.coupon_id=crt.coupon_id 
													WHERE coupon_code = '" . $sys_token_array[1][$sysCnt] . "'
													GROUP BY crt.coupon_id";
						$coupon_stat_result_sql = tep_db_query($coupon_stat_select_sql);
	    				
	    				if ($coupon_stat_row = tep_db_fetch_array($coupon_stat_result_sql)) {
	    					$listing['content'] = str_replace($sys_token_array[0][$sysCnt], $coupon_stat_row['remaining_uses'], $listing['content']);
	    				}
					}
				}
			}
			
		  	if ($listing['url']) {
				$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
												'text' => 	'<div class="latestNewsTitle">'.eval_html($listing["headline"]).'</div>'.
		                   									'<div class="latestNewsDate">'.tep_date_long($listing["date_added"]).'</div>'
		                   									.'<table border="0"><tr><td>&nbsp;&nbsp;</td>' .'<td class="latestNewsBoxContents">'. nl2br(eval_html($listing['content'])) .'<br><br><a href=http://'.$listing["url"].' class="latestNewsLink">[URL]</a><br>'. '</td></tr></table><br>');
			} else {
				$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
		     									'text' => 	'<DIV class="latestNewsTitle">'.eval_html($listing["headline"]).'</div>'.
		                   									'<div class="latestNewsDate">'.tep_date_long($listing["date_added"]).'</div>'
		      												.'<table border="0"><tr><td>&nbsp;&nbsp;</td>' .'<td class="latestNewsBoxContents">'. nl2br(eval_html($listing['content'])) . '</td></tr></table><br>');
			}
			
			$display_these[$count]= $info_box_contents[0]['text'];
		    $count++;
		}
	 	
		echo '	<table class="newsOutline" cellSpacing=0 cellPadding=0 width="100%" border=0>
					<tr>
	    				<td>
	    					<table cellSpacing=0 cellPadding=0 width="100%" border=0>
	       						<tbody>
	       							<tr>
	       								<td>
	       	';
	 	for($i=0; $i< sizeof($display_these) ; $i++) {
	 		echo $display_these[$i]; 
		} 
		echo '</td></tr></tbody></table></td></tr></table>';
		
	  	if ( ($listing_numrows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  			<tr>
	    			<td class="pageResultsText" align="right">
					<? 	if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) {
							//      echo TEXT_RESULT_PAGE . ' ';
	                        //    $listing_split->display_links($listing_numrows, MAX_DISPLAY_LATEST_NEWS_PAGE, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y')));    
						} else {
	                    	//    echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')));
						}
					?>
					</td>
	  			</tr>
			</table>
<?		} ?>
		</td>
	</tr>
	 <tr>
    	<td>
    		<?=tep_div_button(2, IMAGE_BUTTON_BACK,'javascript:history.back(-1);', '', 'gray_button') ?>
		</td>
	</tr>
</table>
		<div class="break_line"></div>