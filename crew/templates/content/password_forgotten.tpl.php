			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>
<?=tep_draw_form('password_forgotten', tep_href_link(FILENAME_PASSWORD_FORGOTTEN, 'action=process', 'SSL'))?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
    	<td class="instruction"><br><?=TEXT_MAIN?></td>
   	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('password_forgotten') > 0) {
?>
	<tr>
		<td><?=$messageStack->output('password_forgotten')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
    	<td>
    		<table border="0" width="100%" height="100%" cellspacing="1" cellpadding="2" class="inputBox">
          		<tr class="inputBoxContents">
            		<td>
            			<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
              				</tr>
              				<tr>
                				<td><span class="inputLabelBold"><?=ENTRY_EMAIL_ADDRESS?></span>&nbsp;<span class="inputField"><?=tep_draw_input_field('email_address')?></span></td>
              				</tr>
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
			    <tr>
			    	<td>
			    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
			          		<tr class="buttonBoxContents">
			            		<td>
			            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
			              				<tr>
			                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			                				<td>
			                					<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_LOGIN, '', 'SSL'), '', 'gray_button') ?>
											</td>
			                				<td align="right">
			                					<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'password_forgotten', 'style="float:right"', 'gray_button') ?>
											</td>
			                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			              				</tr>
			            			</table>
			            		</td>
			          		</tr>
			        	</table>
			        </td>
				</tr>
        	</table>
        </td>
	</tr>
</table></form>
		<div class="break_line"></div>