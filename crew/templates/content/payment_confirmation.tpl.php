<style>
	td {
		font-size:12px;
	}
	#cartItem {
		font-size: 18px;
	}
	div.bodyContentCenterLeftWide {
		width: 100% !important;
	}
	#TotalAmount {
		background-color: #e5f6fe;
		margin: 7px 35px 0;
		padding: 17px;
		
	}	
	#TotalAmount td {
		font-weight: bold;
		font-size: 18px;
	}
	.dashed-top {
		border-top:1px dashed lightgray;
	}
	.dashed-bottom {
		border-bottom:1px dashed lightgray;
	}
	.info-column {
		line-height:20px;
		font-weight:bold;
		text-align:left;
		padding-left: 50px;
	}
	.td-text-middle {
		text-align: right;
	}
	.last-column {
		padding-bottom: 10px;
	}
	.first-column {
		padding-top: 10px;
	}
	.total-amount {
		line-height:20px;
		font-weight:bold;
		text-align:left;
	}
	#button-group {
		margin: 55px;
		width: 78%;
	}
	#cancel-button {
		float:left;
	}
	#checkout-button {
		float:left;
		padding-left: 15px;
	}
</style>
	
<?php
$total_op = 0;

echo tep_draw_form('confirmPayment', tep_href_link(FILENAME_CHECKOUT_XMLHTTP, '', 'NONSSL'), 'get');
$op_query = tep_db_query("select op_rebate from " . TABLE_ORDERS_PRODUCTS . " where orders_id = " . $_SESSION['confirmation_order_id']);
while ($op_result = tep_db_fetch_array($op_query)) {
	if (isset($op_result['op_rebate']) && !empty($op_result['op_rebate'])) { 
		$total_op += $op_result['op_rebate'];
	}
}
ob_start();
?>
<div id="cartItem" style="padding-top: 15px; width: 100%;">
	<?php
		if (isset($_REQUEST['error_message']) && tep_not_null($_REQUEST['error_message'])) {
			$messageStack->add('checkout_confirmation', htmlspecialchars($_REQUEST['error_message']));
		}

		if ($messageStack->size('checkout_confirmation') > 0) {
			?>
			<div>
				<div class="info-column" style="font-weight: normal;"><?= $messageStack->output('checkout_confirmation') ?></td>
			</div>
		<?php
		}
		?>
	<table width="99%">
		<?php
		foreach ($order->products as $viewData) {
			?>
			<tr>
				<td width="30%" class="info-column"><?php echo TITLE_PRODUCT_TITLE ?></td>
				<td width="20%" class="td-text-middle">:</td>
				<td width="50%"> <?php echo $viewData['name'] ?></td>
			</tr>
			<tr>
				<td width="30%" class="info-column"><?php echo TITLE_QUANTITY ?></td>
				<td width="20%" class="td-text-middle">:</td>
				<td width="50%"><?php echo $viewData['qty'] ?></td>
			</tr>
			<tr>
				<td width="30%" class="info-column last-column"><?php echo TITLE_ORDER_AMOUNT ?></td>
				<td width="20%" class="td-text-middle last-column">:</td>
				<td class="last-column" width="50%">
					<?php 
						$productPrice = round($currencies->advance_currency_conversion($viewData['final_price'], DEFAULT_CURRENCY, $order->info['currency'], false, 'buy'), $currencies->currencies[$order->info['currency']]['decimal_places']);
						$productPrice = number_format(($productPrice), $currencies->currencies[$order->info['currency']]['decimal_places'], $currencies->currencies[$order->info['currency']]['decimal_point'], $currencies->currencies[$order->info['currency']]['thousands_point']);
						echo $currencies->currencies[$order->info['currency']]['symbol_left'] . $productPrice . $currencies->currencies[$order->info['currency']]['symbol_right'];
					?></td>
			</tr>
			<?php
		}
		?> 		
		<tr><td class="dashed-top">&nbsp;</td><td class="dashed-top">&nbsp;</td><td class="dashed-top">&nbsp;</td></tr>
		<?php
		foreach ($order->totals as $addData) {
			if (isset($addData['title']) && !empty($addData['title'])) {
				if (isset($addData['class']) && $addData['class'] == 'ot_total' && !empty($addData['text'])) {
					$orderTotal = strip_tags($addData['text']);
					break;
				} else if (isset($addData['class']) && $addData['class'] == 'ot_surcharge' && !empty($addData['text'])) {
					$surcharge = strip_tags($addData['text']);
					break;
				}
					
				?>
		
				<tr>
					<td width="30%" class="info-column"><?php echo $GLOBALS[$addData['class']]->display_title ?></td>
					<td width="20%" class="td-text-middle">:</td>
					<td width="50%"><?php echo strip_tags($addData['text']) ?></td>
				</tr>
				<?php
			}
		}
		?> 
		<tr><td class="dashed-bottom">&nbsp;</td><td class="dashed-bottom">&nbsp;</td><td class="dashed-bottom">&nbsp;</td></tr>
		<tr>
			<td width="30%" class="info-column first-column"><?php echo TITLE_PAY_TO ?></td>
			<td width="20%" class="first-column td-text-middle">:</td>
			<td width="50%" class="first-column"><?php echo $$payment->display_title ?></td>
		</tr>
		<?php
		if (isset($surcharge) && !empty($surcharge)) {
			?>
			<tr>
				<td width="30%" class="info-column"><?php echo TITLE_PROCESSING_FEE ?></td>
				<td width="20%" class="td-text-middle">:</td>
				<td width="50%"><?php echo $surcharge ?></td>
			</tr>
			<?php
		}
		?>
	</table>
	<div id="TotalAmount">
		<table width="99%">
			<tr>
				<td width="41%" class="total-amount"><?php echo TITLE_TOTAL_AMOUNT ?></td>
				<td width="10%" class="td-text-middle">:</td>
				<td width="49%">
					<?php 
					if (isset($order->info['total']) && !empty($order->info['total'])) {
						echo '<b>'.$order->info['total'].'</b>';
					}
					?>
				</td>
			</tr>
		</table>
	</div>
	<table width="99%">
		<tr><td>&nbsp;</td></tr>
		<tr>
			<td width="30%" class="info-column"><?php echo ENTRY_REBATE ?></td>
			<td width="20%" class="td-text-middle">:</td>
			<td width="50%"><?php echo tep_display_op($total_op)?></td>
		</tr>
	</table>
<?php 
	if ((isset($_SESSION['checkout_otp']) && $_SESSION['checkout_otp'] == '0') || !isset($_COOKIE['ogm']['cp'])) {
		$customer_email_select_sql = "	SELECT customers_email_address
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . $_SESSION['customer_id'] . "'";
		$customer_email_result_sql = tep_db_query($customer_email_select_sql);
		if ($customer_email_row = tep_db_fetch_array($customer_email_result_sql)) {
?>
	<div id="device-pin">
			<table width="99%">
			<tr><td>&nbsp;</td></tr>
			<tr>
				<td width="49%" class="info-column" style="font-weight: normal;"><?php echo TITLE_DEVICEPIN_MESSAGE ?></td>
				<td width="0%" class="td-text-middle">:</td>
				<td width="51%" style="color:blue"><?php echo $customer_email_row['customers_email_address']?></td>
			</tr>
			<tr>
				<td width="49%" class="info-column" ><?php echo TITLE_DEVICEPIN ?></td>
				<td width="0%" class="td-text-middle">:</td>
				<td width="51%"><?php echo tep_draw_input_field('devicePin', '', 'style="width: 200px; id="devicePin"')?></td>
			</tr>
			<tr>
				<td width="49%" class="info-column" >&nbsp;</td>
				<td width="0%" class="td-text-middle">&nbsp;</td>
				<td width="51%" style="font-size: 11px;"><a href="javascript:location.reload();"><?php echo LINK_RESEND_SECURITY_PIN?></a></td>
			</tr>
		</table>
	</div>
<?php
		} else {
			echo 'MISSING EMAIL ADDRESS';
		}
	}
?>
	<div id="button-group">
		<input type="hidden" name="action" value="pg_confirm_payment">
		<div id="cancel-button">
			<?php echo tep_image_button2('gray_tall', tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'NONSSL'), TITLE_BUTTON_CANCEL, '100','onclick="blockUI_disable();"') ?>
		</div>
		<div id="checkout-button">
			<?php echo tep_image_button2('red', 'javascript:void(0)', TITLE_BUTTON_CONFIRM_PAYMENT, 260,'onclick="blockUI_disable();document.confirmPayment.submit();"') ?>
		</div>
	</div>	
</div>
</form>
<div style="clear:both"></div>
<?php
$content_string = ob_get_contents();
ob_end_clean();
echo '<div class="vspacing"></div>';
echo '<div style="width:500px;margin: 0 auto;">';
echo $page_obj->get_html_simple_rc_box(HEADING_CONFIRM_PAYMENT, $content_string, 12);
echo '</div>';
?>