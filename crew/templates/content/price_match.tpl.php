<? echo tep_draw_form('price_match', tep_href_link(FILENAME_PRICE_MATCH, 'action=send')); ?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
        <td class="font12Info"><?=TEXT_FOUND_CHEAPER_INFO?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('contact') > 0) {
?>
    <tr>
    	<td><?=$messageStack->output('contact')?></td>
	</tr>
    <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'success')) {
?>
	<tr>
    	<td class="info" align="center"><? //echo tep_image(DIR_WS_IMAGES . 'table_background_man_on_board.gif', HEADING_TITLE, '0', '0', 'align="left"') . TEXT_SUCCESS; 
			echo TEXT_SUCCESS;
?>
        </td>
	</tr>
    <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td align="right">
                					<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), 'style="float:right"', 'gray_button') ?>
								</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
} else {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputBox">
          		<tr class="inputBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="inputLabel"><?=ENTRY_NAME?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_input_field('name','','size=50')?></td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_EMAIL?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_input_field('email','','size=50')?></td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_ITEM_NAME?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_input_field('item','','size=50')?></td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_REALM?></td>
              				</tr>
              				<tr>
                				<td class="inputField">
<?
	$groups_array[] = array('text' => 'Select Reamls',
                           	'id' => '');
	$groups_array[] = array('text' => 'USEAST',
							'id' => 'USEAST');
	$groups_array[] = array('text' => 'USWEST',
							'id' => 'USWEST');
	$groups_array[] = array('text' => 'EUROPE',
							'id' => 'EUROPE');
	$groups_array[] = array('text' => 'ASIA',
							'id' => 'ASIA');
    echo tep_draw_pull_down_menu('categories_id', $groups_array, '','');
?>
	&nbsp;&nbsp;<?=ENTRY_REALM_LADDER?>&nbsp;<?=tep_draw_checkbox_field('ladder', '')?>
	&nbsp;&nbsp;<?=ENTRY_REALM_HARDCORE?>&nbsp;<?=tep_draw_checkbox_field('hardcore', '')?>
								</td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_ITEM_URL." (From Other store)"?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_input_field('url','http://','size=50', '', false)?></td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_PRICE?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_input_field('price')?></td>
              				</tr>
              				<tr>
                				<td class="inputLabel"><?=ENTRY_COMMENTS?></td>
              				</tr>
              				<tr>
                				<td class="inputField"><?=tep_draw_textarea_field('comments', 'soft', '150', '6', '', 'style="width:100%;"')?></td>
              				</tr>
              				<tr>
              					<td>
              						<table border="0" width="100%" cellspacing="0" cellpadding="0" class="buttonBox">
          								<tr class="buttonBoxContents">
            								<td>
            									<table border="0" width="100%" cellspacing="0" cellpadding="2">
						              				<tr>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						                				<td align="right">
															<?=tep_image_submit('continue.gif', IMAGE_BUTTON_CONTINUE)?>
														</td>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						              				</tr>
						            			</table>
						            		</td>
	          							</tr>
        							</table>
              					</td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
}
?>
</table></form>