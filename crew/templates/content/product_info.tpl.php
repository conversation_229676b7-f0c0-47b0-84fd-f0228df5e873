<script language="javascript" src="includes/general.js"></script>
<?
$extra_buy_button_html = '';
echo tep_draw_form('cart_quantity', tep_href_link(FILENAME_PRODUCT_INFO, tep_get_all_get_params(array('action')) . 'action=buy_now')); 
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($product_check['total'] < 1)
{
?>
	<tr>
    	<td><? new infoBox(array(array('text' => TEXT_PRODUCT_NOT_FOUND)), 0, 'class="messageBox"', 'class="messageRow"', 'class="messageData"', 0); ?></td>
	</tr>
    <tr>
    	<td><? echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td align="right">
                					<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), 'style="float:right"', 'gray_button') ?>
								</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
} else {
//	$product_info_query = tep_db_query("select p.products_id, p.products_description_image, pd.products_name, pd.products_description, p.products_model, p.products_quantity, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, p.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
	$product_info_query = tep_db_query("SELECT p.products_id, pd.products_description_image, pd.products_name, pd.products_description, p.products_model, p.products_quantity, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, pd.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id 
										FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
										WHERE p.products_status = '1' 
											AND p.products_id = '" . (int)$_GET['products_id'] . "' 
											AND pd.products_id = p.products_id 
											AND pd.language_id = '" . (int)$languages_id . "'");

    $product_info = tep_db_fetch_array($product_info_query);
	$normal_price = $product_info['products_price'];
	$pid = $product_info['products_id'];

	$qty = $product_info['products_quantity'];
  	$products_bundle = $product_info['products_bundle'];
  	$products_bundle_dynamic = $product_info['products_bundle_dynamic'];
  	
  	$cat_cfg_array = tep_get_cfg_setting($pid, 'product');
  	
	if ($products_bundle == "yes") {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "products_bundle");
	} else if ($products_bundle_dynamic == "yes") {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "products_bundle_dynamic");
	} else {
		$status_info = tep_product_add_to_cart_permission($product_info['products_id'], "");
	}
	
	$show_it = $status_info["show"];
	
    tep_db_query("update " . TABLE_PRODUCTS_DESCRIPTION . " set products_viewed = products_viewed+1 where products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and language_id = '" . (int)$languages_id . "'");
	
    if ($new_price = tep_get_products_special_price($product_info['products_id'])) {
    	$products_price = '<s>' . $currencies->format($normal_price) . '</s> <span class="productSpecialPrice">' . $currencies->display_price_nodiscount($product_info['products_id'], $new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    } else {
    	$products_price = $currencies->display_price($product_info['products_id'], $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']), 1, ($show_it==2 ? true : false), $cat_cfg_array['PRE_ORDER_DISCOUNT']);
    }
	
    if (tep_not_null($product_info['products_model'])) {
      	$products_name = $product_info['products_name'] . '<br><span class="productText">[' . $product_info['products_model'] . ']</span>';
    } else {
      	$products_name = $product_info['products_name'];
    }
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
	if ($success == 'CA') {
		$cart_link = tep_href_link(FILENAME_SHOPPING_CART);
		if ($bdn == 'y') {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_UPDATED, $cart_link), 'success');
		} else {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_ADDED, $cart_link), 'success');
		}
	} else {
		if ($err == "SL") {
			$messageStack->add('add_to_cart', stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . ((int)$_REQUEST['sq'] > 0 ? sprintf(WARNING_PRODUCTS_SUGGESTED_QUANTITY, $_REQUEST['sq']) : ''), 'error');
	  	} else if ($err == "OW") {
	  		$messageStack->add('add_to_cart', stripslashes(WARNING_PRODUCTS_OVERWEIGHT), 'error');
	  	} else if ($err == "UW") {
	  		$messageStack->add('add_to_cart', stripslashes(WARNING_PRODUCTS_UNDERWEIGHT), 'error');
	  	} else if ($err == "ES") {
	  		$messageStack->add('add_to_cart', stripslashes(WARNING_EMPTY_SELECTIONS), 'error');
	  	}
	  	unset($err);
	}
	
	if ($messageStack->size('add_to_cart') > 0) {
		echo '	<tr align="left">
    				<td colspan="2">';
		echo $messageStack->output('add_to_cart');
		echo '		</td>
				</tr>
				<tr>
     				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>';
	}
?>
          		<tr>
            		<td valign="top" class="productTitle"><?=$products_name?></td>
            		<td class="price" align="right" valign="top"><?=$products_price?></td>
          		</tr>
          		<tr>
          			<td>&nbsp;</td>
          			<td class="productText" align="right" valign="top">
          			<?
	                if (!$show_it) {
						echo tep_div_button(2, IMAGE_BUTTON_OUT_OF_STOCK,'javascript:;', '', 'lightgray_button_fix_width');
					} else {
						echo tep_draw_form('buy_now', tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'products_id')) . 'action=buy_now&products_id='. $pid), 'POST', '') . 
	            	        	'<INPUT type="hidden" name="buy_now_qty" value="'.$qty.'">
	            	        	<INPUT type="hidden" name="products_bundle" value="'.$products_bundle.'">
	            	        	<INPUT type="hidden" name="products_bundle_dynamic" value="'.$products_bundle_dynamic.'">
	            	        	';
	            	    
						if ($products_bundle_dynamic=="yes") {
	            	        if ($bdn == 'y') {
	            	        	if (tep_session_is_registered('customer_id')) {
									$prod_bd_query = tep_db_query("SELECT customers_basket_quantity FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = ".(int)$customer_id." AND products_id = " . $product_info['products_id'] . " ORDER BY products_id");
						  			$prod_bd = tep_db_fetch_array($prod_bd_query);
						  			if ($prod_bd['customers_basket_quantity']){
						  				$pro_qty = $prod_bd['customers_basket_quantity'];	
						  			} else {
						  				$pro_qty = 1;
						  			}
						  		} else {
						  			$pro_qty = $cart->contents[$product_info['products_id']]['qty'];
						  		}
						  	} else {
						  		$pro_qty = $_REQUEST["buyqty"] ? $_REQUEST["buyqty"] : 1;
						  	}
	            	        echo "Qty: ".tep_draw_input_field('buyqty', $pro_qty, 'size="4" maxlength="4" class="qtyInput" onKeyUp="valueCheckVal()"')."<br>";
						} else {
	          	            echo "Qty: ".tep_draw_input_field('buyqty', '1', 'size="4" maxlength="4" class="qtyInput" onKeyUp="buyQtyCheck()"')."<br>";
						}
						
						if ($bdn=='y') {
							if ($products_bundle_dynamic=="yes")
								$hreftag = "javascript:return valueCheck()";
							else
								$hreftag = "javascript:document.buy_now.submit()";
								
							$extra_buy_button_html = tep_div_button(2, IMAGE_BUTTON_UPDATE,$hreftag, '', 'gray_button');
						}
						else if ($show_it == 1) {
							if ($products_bundle_dynamic=="yes")
								$hreftag = "javascript:return valueCheck()";
							else
								$hreftag = "javascript:document.buy_now.submit()";

							$extra_buy_button_html = tep_div_button(2, IMAGE_BUTTON_IN_CART,$hreftag, '', 'green_button');
						}
						else {
							if ($products_bundle_dynamic=="yes")
								$hreftag = "javascript:return valueCheck()";
							else
								$hreftag = "javascript:document.buy_now.submit()";

							$extra_buy_button_html = tep_div_button(2, IMAGE_BUTTON_PRE_ORDER,$hreftag, '', 'yellow_button');
						}
	          	        echo tep_draw_hidden_field('products_id', $product_info['products_id']) . $extra_buy_button_html;
	          	        if ($bdn != 'y' && $show_it == 2) echo "<br>ETA: ".tep_get_eta_string($status_info["pre_order_time"]);
					}
				?>
          			</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
	</tr>
    <tr>
    	<td class="productSmallText" valign="middle" align="center">
			<table border="0" cellspacing="0" cellpadding="2" align="center">
            	<tr>
              		<td align="center" class="productSmallText">
              		<?
              		if (tep_not_null($product_info['products_image'])) {
						echo tep_image(THEMA_IMAGES."products/" . $product_info['products_image'], $product_info['products_name'], '', '', 'hspace="5" vspace="5"');
              		} else {
              			$pro_img = "label_nopic.gif";
              			echo tep_image(THEMA_IMAGES."products/" . $pro_img, $product_info['products_name'], '', '', 'hspace="5" vspace="5"');
              		}
              		?>
              		</td>
            	</tr>
            	<tr>
            	<?
            		$product_description_align  = tep_not_null(PRODUCT_DESCRIPTION_ALIGN) ? PRODUCT_DESCRIPTION_ALIGN : "center";
            		$product_description_width  = tep_not_null(PRODUCT_DESCRIPTION_WIDTH) ? PRODUCT_DESCRIPTION_WIDTH : "170px";
            	?>
            		<td class="productSmallText" align="<?=$product_description_align?>">
            			<div style="width: <?=$product_description_width?>;"><?=stripslashes($product_info['products_description'])?></div>
            		</td>
            	</tr>
				<tr>
					<td valign="top" align="center" class="productSmallText">
					<?
					if($product_info['products_description_image']){
						echo tep_image(THEMA_IMAGES."products/" . $product_info['products_description_image'], addslashes($product_info['products_name']), '', '', 'hspace="5" vspace="5"');
					}
					?>
					</td>
				</tr>
          	</table>
			<table border="0" cellspacing="0" cellpadding="2" align="center">
				<tr>
					<td class="productSmallText">
          	<!-- start bundle -->
            <?
		  	if ($product_info['products_bundle'] == "yes" || $product_info['products_bundle_dynamic'] == "yes")
		  	{
		  		$products_bundle = $product_info['products_bundle'];
		  		echo "This product contains the following items:";
		  		if ($product_info['products_bundle_dynamic'] == "yes") {
		  			if ($bdn == 'y') {
			  			$prod_bd_query = tep_db_query("SELECT customers_basket_quantity FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = ".(int)$customer_id." AND products_id = " . $product_info['products_id'] . " ORDER BY products_id");
		  				$prod_bd = tep_db_fetch_array($prod_bd_query);
		  				if ($prod_bd["customers_basket_quantity"]) {
		  					$pro_qty = $prod_bd["customers_basket_quantity"];
		  				} else {
		  					$pro_qty = 1;
		  				}
		  			} else {
		  				$pro_qty = 1;
		  			}
		  			if ($show_it)
		  				echo "<br>&raquo; Weight:<b> " . tep_draw_input_field('products_bundle_dynamic_qty', '0', 'size="5" readOnly') . " / ".tep_draw_input_field('products_bundle_dynamic_wei', $product_info['products_bundle_dynamic_qty'] * $pro_qty, 'size="5" readOnly')."</b>";
		  		}
				
				$bundle_query = tep_db_query("	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, p.products_id, p.products_price
												FROM products p 
												INNER JOIN products_description pd
											  		ON p.products_id=pd.products_id
												INNER JOIN products_bundles pb
											  		ON pb.subproduct_id=pd.products_id 
												WHERE pb.bundle_id = '" . tep_db_input(tep_get_prid($products_id)) . "' and language_id = '" . (int)$languages_id . "'");
												
				$sub_count = 0;
				while ($bundle_data = tep_db_fetch_array($bundle_query)) {
					if ($bundle_data['products_bundle'] == "yes" || $bundle_data['products_bundle_dynamic'] == "yes") {
						// uncomment the following line to display subproduct qty 
                        echo "<br>&raquo; <b>" . $bundle_data['subproduct_qty'] . " x " . $bundle_data['products_name'] . "</b>";
						echo "<br>&raquo; <b> " . $bundle_data['products_name'] . "</b>";
						$bundle_query_nested = tep_db_query("	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, p.products_id, p.products_price
																FROM products p 
																INNER JOIN products_description pd
											  						ON p.products_id=pd.products_id
																INNER JOIN products_bundles pb
											  						ON pb.subproduct_id=pd.products_id 
																WHERE pb.bundle_id = '" . $bundle_data['products_id'] . "' AND language_id = '" . (int)$languages_id . "'");
						while ($bundle_data_nested = tep_db_fetch_array($bundle_query_nested)) {
      						// uncomment the following line to display subproduct qty 
                        	echo "<br><i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" . $bundle_data_nested['subproduct_qty'] . " x " . $bundle_data_nested['products_name'] . "</i>";
							//$product_info_query1 = tep_db_query("select p.products_id, pd.products_name, pd.products_description, p.products_model, p.products_quantity, p.products_bundle, p.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_id = '" . $bundle_data['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
							$product_info_query1 = tep_db_query("	SELECT p.products_id, pd.products_name, pd.products_description, p.products_model, p.products_quantity, p.products_bundle, pd.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id 
																	FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
																	WHERE p.products_status = '1' 
																		AND p.products_id = '" . $bundle_data['products_id'] . "' 
																		AND pd.products_id = p.products_id 
																		AND pd.language_id = '" . (int)$languages_id . "'");
					    	
					    	$product_info1 = tep_db_fetch_array($product_info_query1);
							$normal_price1 = $product_info1['products_price'];
							echo "<br><i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" . $bundle_data_nested['products_name'] . "</i>";
							//$bundle_sum += $bundle_data_nested['products_price']*$bundle_data_nested['subproduct_qty'];
							$bundle_sum += $normal_price1*$bundle_data_nested['subproduct_qty'];
						}
					} else {
						$dynamic_subproduct_select_sql  = "	SELECT p.products_id, p.products_bundle_dynamic_qty, pd.products_name, pd.products_description, p.products_quantity, p.products_bundle, p.products_price, p.products_purchase_mode, p.products_pre_order_level, p.products_eta 
															FROM " . TABLE_PRODUCTS . " AS p, " . 
																TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															WHERE p.products_status=1 AND p.products_id = '" . $bundle_data['products_id'] . "' 
																AND pd.products_id = p.products_id AND pd.language_id = '" . (int)$languages_id . "'";
						$dynamic_subproduct_result_sql = tep_db_query($dynamic_subproduct_select_sql);
						$product_info2 = tep_db_fetch_array($dynamic_subproduct_result_sql);
						
				    	$normal_price2 = $product_info2['products_price'];
                    	if ($product_info['products_bundle_dynamic'] == "yes") {
                    		if (tep_db_num_rows($dynamic_subproduct_result_sql)) {
                    			// Get SYSTEM_PRODUCT_ETA category configuration of this single product.
								$cat_cfg_array = tep_get_cfg_setting($product_info2["products_id"], 'product', 'SYSTEM_PRODUCT_ETA');
								
	                    		if ($bdn == 'y') {
	                    			if (tep_session_is_registered('customer_id')) {
	                    				$cust_basket_bundle_select_sql = "SELECT subproducts_quantity FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE products_bundle_id = '".tep_get_prid($products_id)."' AND customers_id = ".(int)$customer_id." AND subproducts_id = " . $product_info2['products_id'] . " ORDER BY subproducts_id";
						  				$cust_basket_bundle_result_sql = tep_db_query($cust_basket_bundle_select_sql);
					  					$cust_basket_bundle = tep_db_fetch_array($cust_basket_bundle_result_sql);
					  					$bundle_subproduct_qty = $cust_basket_bundle['subproducts_quantity'];
					  				} else {
					  					$bundle_subproduct_qty = $cart->contents[$products_id]['bundle'][$product_info2['products_id']]['sub_qty'];
					  				}
	                    		}
	                    		if (!$bundle_subproduct_qty) {
	                    			$bundle_subproduct_qty = 0;
	                    		}
	                    		
	                    		$bundle_weight_select_sql = "SELECT subproduct_weight FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = ".tep_get_prid($products_id)." AND subproduct_id = " . $product_info2["products_id"];
	                    		$bundle_weight_result_sql = tep_db_query($bundle_weight_select_sql);
								$bundle_weight_row = tep_db_fetch_array($bundle_weight_result_sql);
								$bundle_weight = $bundle_weight_row["subproduct_weight"];
								
								$delivery_mode_select_sql = "	SELECT products_delivery_mode_id 
																FROM " . TABLE_PRODUCTS_DELIVERY_INFO . " 
																WHERE products_id = '".(int)$pid."'";
								$delivery_mode_result_sql = tep_db_query($delivery_mode_select_sql);
								$delivery_mode_num = tep_db_num_rows($delivery_mode_result_sql);
								if ($delivery_mode_num) {
									$cur_product_out_of_stock = false;
								} else {
									$skip_stock_checking = tep_single_product_skip_stock_check($product_info2["products_id"]);
									
									if ($skip_stock_checking['state'] == -1) {
										$cur_product_out_of_stock = true;
									} else if ($skip_stock_checking['state'] == 0) {
										if ($product_info2['products_quantity'] - $bundle_data['subproduct_qty'] < $skip_stock_checking['stock_level']) {
											$cur_product_out_of_stock = true;
										} else {
											$cur_product_out_of_stock = false;
										}
									} else {
										$cur_product_out_of_stock = false;
									}
								}
								
					    		echo "<br>&raquo; <b> " . ($show_it && !$cur_product_out_of_stock ? tep_draw_input_field("q_".$product_info2['products_id'], $bundle_subproduct_qty, ' size="2" onKeyUp="valueCheckVal()" ') : '') .'&nbsp;'. $bundle_data['subproduct_qty'] . " x " .$bundle_data['products_name'] . " (weight = ".$bundle_weight.")</b>";
					    		if ($cur_product_out_of_stock) echo '<span class="markProductOutOfStock">'.STOCK_MARK_PRODUCT_OUT_OF_STOCK.'</span>';
					    		echo '&nbsp;<span id="SW_'.$product_info2['products_id'].'" class="markProductOutOfStock"></span>';
					    		
					    		$dyn_pre_order = false;
					    		$subproduct_pre_order_level = ($product_info2["products_pre_order_level"] != '') ? $product_info2["products_pre_order_level"] : '';
					    		$subproduct_eta = $product_info2["products_eta"] != '' ? $product_info2["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
					    		
					    		if ($product_info2['products_purchase_mode'] == "2") {
					    			echo '&nbsp;<span class="markProductOutOfStock">ETA: '.tep_get_eta_string($subproduct_eta).'</span>';
					    		} else if ($product_info2['products_purchase_mode'] == "4") {
					    			echo '&nbsp;<span id="PO_'.$product_info2['products_id'].'" class="markProductOutOfStock"></span>';
					    			$dyn_pre_order = tep_not_null($subproduct_pre_order_level) ? true : false;
					    			$eta_string = 'ETA: '.tep_get_eta_string($subproduct_eta);
					    		}
					    		
					    		echo tep_draw_hidden_field("p_".$product_info2['products_id'],$bundle_weight);
					    		
					    		if (!$cur_product_out_of_stock) {
					    			$bundleV_array[] = array('id' => $product_info2['products_id'], 'skip_checking' => $skip_stock_checking['state']==1 ? "1" : "0", 'pre_order_checking' => $dyn_pre_order ? "1" : "0", 
					    									 'pre_order_info' => $subproduct_pre_order_level . ':~:' .$eta_string, 'stock_qty' => $product_info2['products_quantity'], 'out_of_stock_level' => ($skip_stock_checking['state']==0 ? (int)$skip_stock_checking['stock_level'] : 0), 'subproduct_qty' => $bundle_data['subproduct_qty']);
					    		}
								$vc = $product_info2['products_id'];
					    	}
                    	} else {
                    		echo "<br>&raquo; <b> " . $bundle_data['subproduct_qty'] . " x " . $bundle_data['products_name'] . "</b>";
                    	}
						//echo tep_draw_hidden_field("add_bundle_dynamic","1");
                    	$bundle_sum += $bundle_data['products_price']*$bundle_data['subproduct_qty'];
						$bundle_sum += $normal_price2*$bundle_data['subproduct_qty'];
					}
					$sub_count++;
				}
				
				global $bundleV;
				$bundleV = _serialize($bundleV_array);
				echo tep_draw_hidden_field("bundleV", $bundleV);
				
				$bundle_saving = $bundle_sum - $product_info['products_price'];
				$bundle_sum = $currencies->display_price('', $bundle_sum, tep_get_tax_rate($product_info['products_tax_class_id']));
				$bundle_saving =  $currencies->display_price('', $bundle_saving, tep_get_tax_rate($product_info['products_tax_class_id']));
                // comment out the following line to hide the "saving" text
				//echo "<p><b>Cost of separate parts: " . $bundle_sum . '<br><font color="red">You save ' . $bundle_saving . '</font></b>';
				/*
				$bdValue_query = tep_db_query("	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, p.products_id, p.products_price
												FROM products p 
												INNER JOIN products_description pd
												  	ON p.products_id=pd.products_id
												INNER JOIN products_bundles pb
											  		ON pb.subproduct_id=pd.products_id 
												WHERE pb.bundle_id = " . tep_get_prid($products_id) . " and language_id = '" . (int)$languages_id . "'");
				*/
				$cnt = count($bundleV_array);
		  	} 
		  	?>
		  		</td>
		  	</tr>
		  	<?
		  	if ($product_info['products_bundle_dynamic'] == "yes" && STOCK_ALLOW_CHECKOUT != 'true') {
				echo '<tr><td nowrap class="messageStockWarning" align="center"><span id="OutOfStock"></span></td></tr>';
			}
			?>
		  </table>
<!-- end bundle -->
<?
    	$products_attributes_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_ATTRIBUTES . " patrib where patrib.products_id='" . (int)$HTTP_GET_VARS['products_id'] . "' and patrib.options_id = popt.products_options_id and popt.language_id = '" . (int)$languages_id . "'");
    	$products_attributes = tep_db_fetch_array($products_attributes_query);
    	if ($products_attributes['total'] > 0) {
?>
          	<table border="0" cellspacing="0" cellpadding="2">
            	<tr>
              		<td class="main" colspan="2"><?=TEXT_PRODUCT_OPTIONS?></td>
            	</tr>
<?
      		$products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_ATTRIBUTES . " patrib where patrib.products_id='" . (int)$HTTP_GET_VARS['products_id'] . "' and patrib.options_id = popt.products_options_id and popt.language_id = '" . (int)$languages_id . "' order by popt.products_options_name");
      		while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
        		$products_options_array = array();
        		$products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_ATTRIBUTES . " pa, " . TABLE_PRODUCTS_OPTIONS_VALUES . " pov where pa.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$languages_id . "'");
        		while ($products_options = tep_db_fetch_array($products_options_query)) {
          			$products_options_array[] = array('id' => $products_options['products_options_values_id'], 'text' => $products_options['products_options_values_name']);
          			if ($products_options['options_values_price'] != '0') {
            			$products_options_array[sizeof($products_options_array)-1]['text'] .= ' (' . $products_options['price_prefix'] . $currencies->display_price((int)$HTTP_GET_VARS['products_id'], $products_options['options_values_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) .') ';
          			}
        		}
				
        		if (isset($cart->contents[$HTTP_GET_VARS['products_id']]['attributes'][$products_options_name['products_options_id']])) {
          			$selected_attribute = $cart->contents[$HTTP_GET_VARS['products_id']]['attributes'][$products_options_name['products_options_id']];
        		} else {
          			$selected_attribute = false;
        		}
?>
            	<tr>
              		<td class="main"><?=$products_options_name['products_options_name'] . ':'?></td>
              		<td class="main"><?=tep_draw_pull_down_menu('id[' . $products_options_name['products_options_id'] . ']', $products_options_array, $selected_attribute)?></td>
            	</tr>
<?      	} ?>
          	</table>
<?    	} ?>
		</td>
	</tr>
    <tr>
    	<td>
		<? include(DIR_WS_MODULES . FILENAME_DYNAMIC_MOPICS); ?>
		</td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
    </tr>
    <tr>
    	<td align="right"><?=$extra_buy_button_html?></td>
    </tr>
<?
	if ($cat_cfg_array['PRODUCT_REVIEW'] == 'true') {
    	$reviews_query = tep_db_query("select count(*) as count from " . TABLE_REVIEWS . " where products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "'");
    	$reviews = tep_db_fetch_array($reviews_query);
    	if ($reviews['count'] > 0) {
?>
	<tr>
    	<td class="main"><?=TEXT_CURRENT_REVIEWS . ' ' . $reviews['count']?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
    	}
	}
    if (tep_not_null($product_info['products_url'])) {
?>
    <tr>
    	<td class="main"><?php echo sprintf(TEXT_MORE_INFORMATION, tep_href_link(FILENAME_REDIRECT, 'action=url&goto=' . urlencode($product_info['products_url']), 'NONSSL', true, false)); ?></td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?	}

    if ($product_info['products_date_available'] > date('Y-m-d H:i:s')) {
?>
	<tr>
    	<td align="center" class="productSmallText"><?php echo sprintf(TEXT_DATE_AVAILABLE, tep_date_long($product_info['products_date_available'])); ?></td>
	</tr>
<?
    } else {
?>
	<tr>
    	<td align="center" class="productSmallText"><?php echo sprintf(TEXT_DATE_ADDED, tep_date_long($product_info['products_date_added'])); ?></td>
	</tr>
<?	} ?>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="productSmallText">
                					<? //$history_referer = preg_replace("/(&)?(err=)([^&]+)/i", "", $_SERVER['HTTP_REFERER']);?>
                					<? //echo tep_image_button(THEMA.'button_back.gif', IMAGE_BUTTON_BACK, $history_referer) ; ?>
                					<?=tep_div_button(2, IMAGE_BUTTON_BACK,'javascript:history.back(1)', '', 'gray_button') ?>
                				</td>
                				<td class="productSmallText" align="right" valign="top">
<?	if ($products_bundle_dynamic) {?>
			<script>
          	<!--
          		valueCheckVal()
          		function valueCheckVal() {
          			<? $aa = $product_info['products_bundle_dynamic_qty'] * $pro_qty; ?>
					var total = <?=$aa?>;
					var out_of_stock = 0;
					var sub_prod_out_of_stock = false;
					
					if(!validateInteger(document.cart_quantity.buyqty.value)){
						document.cart_quantity.buyqty.value	= 1;
						return false;
					} else if (document.cart_quantity.buyqty.value == '') {
						return false;
					}
					
					if (total == (document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>)) {
						document.cart_quantity.products_bundle_dynamic_wei.value = (document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>);
					} else {
						total =	document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>;
						document.cart_quantity.products_bundle_dynamic_wei.value = (document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>);
					}
					<? 
					if($product_info['products_bundle_dynamic_qty']) {
						for ($i=0;$i<$cnt;$i++) {
							$vc = $bundleV_array[$i]['id'];
							$subprod_qty = $bundleV_array[$i]['subproduct_qty'];
							$stock_qty = $bundleV_array[$i]['stock_qty'];
							$skip_checking = $bundleV_array[$i]['skip_checking'];
							$pre_order_checking = $bundleV_array[$i]['pre_order_checking'];
							list($pre_order_level, $pre_order_eta) = explode(':~:', $bundleV_array[$i]['pre_order_info']);
							$stock_comparing_qty = $stock_qty - (int)$bundleV_array[$i]['out_of_stock_level'];
							?>
							var this_subproduct_id_<?=$i?> = eval('document.cart_quantity.q_<?=$vc?>');
							var this_value;
							sub_prod_out_of_stock = false;
							
							if (!validateInteger(document.cart_quantity.q_<?=$vc?>.value)) {
								document.cart_quantity.q_<?=$vc?>.value	= '';
								eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "";
							<?	if ($pre_order_checking) { ?>
									eval(document.getElementById("PO_"+<?=$vc?>)).innerHTML = "";
							<?	} ?>
							} else {
							<? 	if ($skip_checking) {?>
									// skip stock checking
							<? 	} else {?>
									var ordered_qty = eval(document.cart_quantity.q_<?=$vc?>.value * <?=$subprod_qty?>);
									if (ordered_qty && ordered_qty > <?=$stock_comparing_qty ? $stock_comparing_qty : 0?>) {
										out_of_stock = 1;
										sub_prod_out_of_stock = true;
										eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "<?=STOCK_MARK_PRODUCT_OUT_OF_STOCK?>";
									} else {
										eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "";
									}
							<?	}
								
								if ($pre_order_checking) { ?>
									if (document.cart_quantity.q_<?=$vc?>.value > 0 && !sub_prod_out_of_stock) {
										var pre_ordered_qty = eval(document.cart_quantity.q_<?=$vc?>.value * <?=$subprod_qty?>);
										var stock_qty = <?=$stock_qty ? $stock_qty : 0?>;
										var pre_order_diff = stock_qty - pre_ordered_qty;
										
										if (pre_order_diff != null && pre_order_diff < <?=$pre_order_level?>) {
											eval(document.getElementById("PO_"+<?=$vc?>)).innerHTML = "<?=$pre_order_eta?>";
										} else {
											eval(document.getElementById("PO_"+<?=$vc?>)).innerHTML = "";
										}
									} else {
										eval(document.getElementById("PO_"+<?=$vc?>)).innerHTML = "";
									}
							<?	} ?>
							}
							if (this_value) {
								this_value = eval(document.cart_quantity.q_<?=$vc?>.value * document.cart_quantity.p_<?=$vc?>.value) + eval(this_value);
							} else {
								this_value = eval(document.cart_quantity.q_<?=$vc?>.value * document.cart_quantity.p_<?=$vc?>.value);
							}
					<? } 
					}
					?>
					
					eval(document.getElementById("OutOfStock")).innerHTML = (out_of_stock) ? "<br><?=OUT_OF_STOCK_CANT_CHECKOUT?>" : "";
					
					document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
					if(total<this_value){
						alert ("Please adjust qty - more than total weight!!");
						document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
						return false;
					}
          		}
				
				// When Add to Cart or Update Cart button is pressed
				function valueCheck() {
					<? $aa = $product_info['products_bundle_dynamic_qty'] * $pro_qty; ?>
					var total = <?=$aa?>;
					var out_of_stock = 0;
					
					if(total == (document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>)){
							//alert(total);
					} else {
						total =	document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>;
						document.cart_quantity.products_bundle_dynamic_wei.value = (document.cart_quantity.buyqty.value*<?=$product_info['products_bundle_dynamic_qty']?>);
					}
					<?
					
					if ($product_info['products_bundle_dynamic_qty']) {
						for($i=0;$i<$cnt;$i++){
							$vc = $bundleV_array[$i]['id'];
							$subprod_qty = $bundleV_array[$i]['subproduct_qty'];
							$stock_qty = $bundleV_array[$i]['stock_qty'];
							$skip_checking = $bundleV_array[$i]['skip_checking'];
							$stock_comparing_qty = $stock_qty - (int)$bundleV_array[$i]['out_of_stock_level'];
					?>
							var this_subproduct_id_<?=$i?> = eval('document.cart_quantity.q_<?=$vc?>');
							var this_value;
							
							if (!validateInteger(document.cart_quantity.q_<?=$vc?>.value)) {
								document.cart_quantity.q_<?=$vc?>.value	= '';
								eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "";
							} else {
							<? 	if ($skip_checking) {?>
									// skip stock checking
							<? 	} else {?>
									var ordered_qty = eval(document.cart_quantity.q_<?=$vc?>.value * <?=$subprod_qty?>);
									if (ordered_qty && ordered_qty > <?=$stock_comparing_qty ? $stock_comparing_qty : 0 ?>) {
										out_of_stock = 1;
										eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "<?=STOCK_MARK_PRODUCT_OUT_OF_STOCK?>";
									} else {
										eval(document.getElementById("SW_"+<?=$vc?>)).innerHTML = "";
									}
							<?	} ?>
							}
							
							if (this_value) {
								this_value = eval(document.cart_quantity.q_<?=$vc?>.value * document.cart_quantity.p_<?=$vc?>.value) + eval(this_value);
							} else {
								this_value = eval(document.cart_quantity.q_<?=$vc?>.value * document.cart_quantity.p_<?=$vc?>.value);
							}
					<? } ?>
					
					if (out_of_stock) {
						alert ("Some items are out of stock!\nPlease enter smaller quantity for out of stock items!");
						eval(document.getElementById("OutOfStock")).innerHTML = "<br><?=OUT_OF_STOCK_CANT_CHECKOUT?>";
						return false;
					} else {
						eval(document.getElementById("OutOfStock")).innerHTML = "";
					}
					
					if (total < this_value) {
						alert ("Please adjust qty - more than total weight!!");
						document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
						return false;
					} else {
						if (this_value < 1) {
							alert ("Please select at least one item!!");
							return false;
						} else {
							if (total > this_value) {
								alert ("Please adjust qty - less than total weight!!");
								document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
								return false;
							} else {
								document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
							}
						}
					}
					/*
					if (total>this_value) {
						alert ("Please adjust qty - less than total weight!!");
						document.cart_quantity.products_bundle_dynamic_qty.value = this_value;
						return false;
					}*/
					<? } ?>
					return true;
				}
			-->
			</script>
<?	} ?>
								</td>
								</form>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
<?

//Commented for x-sell
//    if ((USE_CACHE == 'true') && empty($SID)) {
//      echo tep_cache_also_purchased(3600);
//    } else {
//      include(DIR_WS_MODULES . FILENAME_ALSO_PURCHASED_PRODUCTS);
//    }
//  }
//Added for x sell
	if ($cat_cfg_array['SHOW_ALSO_PURCHASED_PRODUCTS'] == "true") {
		if ((USE_CACHE == 'true') && !SID) { 
	    	echo tep_cache_also_purchased(3600); 
	     	//include(DIR_WS_MODULES . FILENAME_XSELL_PRODUCTS); 
	   	} else {
	    	//include(DIR_WS_MODULES . FILENAME_XSELL_PRODUCTS); 
	    	include(DIR_WS_MODULES . FILENAME_ALSO_PURCHASED_PRODUCTS);
	    }
	}
}
?>
    	</td>
	</tr>
</table>
</form>
<?
if ($cat_cfg_array['PRODUCT_REVIEW'] == 'true') { 
?>
    <!-- //Product review -->
    <table border="0" width="100%" cellspacing="0" cellpadding="0">
    	<tr>
    		<td>
        		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          			<tr>
            			<!--td class="pageHeading" valign="top"><//?php echo $products_name; ?></td-->
            			<!--td class="pageHeading" align="right" valign="top"><//?php echo $products_price; ?></td-->
          			</tr>
        		</table>
        	</td>
		</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
      	<tr>
        	<td>
        		<table width="100%" border="0" cellspacing="0" cellpadding="2">
          			<tr>
            			<td valign="top">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	$reviews_query_raw = "select r.reviews_id, left(rd.reviews_text, 100) as reviews_text, r.reviews_rating, r.date_added, r.customers_name from " . TABLE_REVIEWS . " r, " . TABLE_REVIEWS_DESCRIPTION . " rd where r.products_id = '" . (int)$product_info['products_id'] . "' and r.reviews_id = rd.reviews_id and rd.languages_id = '" . (int)$languages_id . "' order by r.reviews_id desc";
  	$reviews_split = new splitPageResults($reviews_query_raw, MAX_DISPLAY_NEW_REVIEWS);
	
  	if ($reviews_split->number_of_rows > 0) {
    	if ((PREV_NEXT_BAR_LOCATION == '1') || (PREV_NEXT_BAR_LOCATION == '3')) {
?>
					        	<tr>
					            	<td>
					            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
					                  		<tr>
					                    		<td class="pageResultsText"><?php echo $reviews_split->display_count(TEXT_DISPLAY_NUMBER_OF_REVIEWS); ?></td>
					                    		<td align="right" class="pageResultsText"><?php echo TEXT_RESULT_PAGE . ' ' . $reviews_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info'))); ?></td>
					                  		</tr>
					                	</table>
					                </td>
								</tr>
					            <tr>
					            	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
<?    	}
    	
    	$reviews_query = tep_db_query($reviews_split->sql_query);
    	while ($reviews = tep_db_fetch_array($reviews_query)) {
?>
								<tr>
					            	<td>
					            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
					                  		<tr>
					                    		<td class="main"><?php echo '<a href="' . tep_href_link(FILENAME_PRODUCT_REVIEWS_INFO, 'products_id=' . $product_info['products_id'] . '&reviews_id=' . $reviews['reviews_id']) . '"><u><b>' . sprintf(TEXT_REVIEW_BY, tep_output_string_protected($reviews['customers_name'])) . '</b></u></a>'; ?></td>
							                    <td class="productSmallText" align="right"><?php echo sprintf(TEXT_REVIEW_DATE_ADDED, tep_date_long($reviews['date_added'])); ?></td>
											</tr>
					                	</table>
					                </td>
								</tr>
								<tr>
					            	<td>
					            		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
					                  		<tr class="infoBoxContents">
					                    		<td>
					                    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					                      				<tr>
					                        				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
					                        				<td valign="top" class="main"><?php echo tep_break_string(tep_output_string_protected($reviews['reviews_text']), 60, '-<br>') . ((strlen($reviews['reviews_text']) >= 100) ? '..' : '') . '<br><br><i>' . sprintf(TEXT_REVIEW_RATING, tep_image(THEMA_IMAGES . 'stars_' . $reviews['reviews_rating'] . '.gif', sprintf(TEXT_OF_5_STARS, $reviews['reviews_rating'])), sprintf(TEXT_OF_5_STARS, $reviews['reviews_rating'])) . '</i>'; ?></td>
					                        				<td width="10" align="right"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
					                      				</tr>
					                    			</table>
					                    		</td>
					                  		</tr>
					                	</table>
					                </td>
								</tr>
					            <tr>
					            	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
<?		} ?>
<?  } else { ?>
								<tr>
					            	<td><?php new infoBox(array(array('text' => TEXT_NO_REVIEWS))); ?></td>
								</tr>
					            <tr>
					            	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
<?  }
	
  	if (($reviews_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3'))) {
?>
					    		<tr>
					            	<td>
					            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
					                  		<tr>
					                    		<td class="pageResultsText"><? echo $reviews_split->display_count(TEXT_DISPLAY_NUMBER_OF_REVIEWS); ?></td>
					                    		<td align="right" class="pageResultsText"><? echo TEXT_RESULT_PAGE . ' ' . $reviews_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info'))); ?></td>
					                  		</tr>
					                	</table>
					                </td>
								</tr>
					            <tr>
					            	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
<?	} ?>
					            <!--tr>
					            	<td>
					            		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
					                  		<tr class="infoBoxContents">
					                    		<td>
					                    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					                      				<tr>
					                        				<td width="10"><?php //echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
					                        				<td class="main"><?php //echo tep_image_button('button_back.gif', IMAGE_BUTTON_BACK, tep_href_link(FILENAME_PRODUCT_INFO, tep_get_all_get_params())) ; ?></td>
									                        <td class="main" align="right"><?php //echo tep_image_button('button_write_review.gif', IMAGE_BUTTON_WRITE_REVIEW, tep_href_link(FILENAME_PRODUCT_REVIEWS_WRITE, tep_get_all_get_params())) ; ?></td>
									                        <td width="10"><?php //echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
					                      				</tr>
					                    			</table>
					                    		</td>
					                  		</tr>
					                	</table>
					                </td>
								</tr-->
							</table>
						</td>
					    <td width="<?php echo SMALL_IMAGE_WIDTH + 10; ?>" align="right" valign="top">
					    	<table border="0" cellspacing="0" cellpadding="2">
					        	<tr>
					            	<td align="center" class="smallText"-->
<?
	/*
  		if (tep_not_null($product_info['products_image'])) {
?>
			<script language="javascript">
			<!--
				document.write('<?php echo '<a href="javascript:popupWindow(\\\'' . tep_href_link(FILENAME_POPUP_IMAGE, 'pID=' . $product_info['products_id']) . '\\\')">' . tep_image(DIR_WS_IMAGES . $product_info['products_image'], addslashes($product_info['products_name']), SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'hspace="5" vspace="5"') . '<br>' . TEXT_CLICK_TO_ENLARGE . '</a>'; ?>');
			//-->
			</script>
			<noscript>
				<?php echo '<a href="' . tep_href_link(DIR_WS_IMAGES . $product_info['products_image']) . '" target="_blank">' . tep_image(DIR_WS_IMAGES . $product_info['products_image'], $product_info['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'hspace="5" vspace="5"') . '<br>' . TEXT_CLICK_TO_ENLARGE . '</a>'; ?>
			</noscript>
<?
  		}
  		echo '<p><!--a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now') . '">' . tep_image_button('button_in_cart.gif', IMAGE_BUTTON_IN_CART) . '</a></p-->';
  	*/
?>
					                </td>
								</tr>
							</table>
						</td>
					</tr>
    			</table>
    		</td>
      	</tr>
	</table>
    
    <!-- Product review write -->
    <? echo tep_draw_form('product_reviews_write', tep_href_link(FILENAME_PRODUCT_REVIEWS_WRITE, 'action=process&products_id=' . $HTTP_GET_VARS['products_id']), 'post', 'onSubmit="return checkForm();"'); ?>
    <table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          			<tr>
          				<td>
          					<b><font color=blue>Write A Review For&nbsp;<font color=red><?=$products_name?></font><font></b>
          				</td>
            			<!--td class="pageHeading" valign="top"><?php //echo $products_name; ?></td-->
            			<!--td class="pageHeading" align="right" valign="top"><?php //echo $products_price; ?></td-->
          			</tr>
        		</table>
        	</td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
<?
  	if ($messageStack->size('review') > 0) {
?>
      	<tr>
        	<td><? echo $messageStack->output('review'); ?></td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
<?  } ?>
      	<tr>
        	<td>
        		<table width="100%" border="0" cellspacing="0" cellpadding="2">
          			<tr>
            			<td valign="top">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	if (tep_session_is_registered('customer_id')) {
?>
         						<tr>
	  								<td class="main"><? echo '<b>' . SUB_TITLE_FROM . '</b> ' . tep_output_string_protected($customer['customers_firstname'] . ' ' . $customer['customers_lastname']); ?></td>
      								<INPUT name="customers_firstname" type="hidden" value="<?=$customer['customers_firstname'];?>">
	  							</tr> 
<?	} else { ?>
              					<tr>
                					<td class="main">
                  						<table border="0" width="100%" cellspacing="0" cellpadding="2">
                    						<tr>
                      							<td class="main" width="80"><? echo ENTRY_FIRST_NAME; ?></td>
                      							<td class="main"><? echo tep_draw_input_field('customers_firstname'); ?></td>
                    						</tr>
                    						<tr>
                      							<td class="main" width="80"><? echo ENTRY_LAST_NAME; ?></td>
                      							<td class="main"><? echo tep_draw_input_field('customers_lastname'); ?></td>
                    						</tr>
                  						</table>
                					</td>
              					</tr>
<?	} ?>
              					<tr>
                					<td class="main"><b><? echo SUB_TITLE_REVIEW; ?></b></td>
              					</tr>
              					<tr>
                					<td>
                						<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
                  							<tr class="infoBoxContents">
                    							<td>
                    								<table border="0" width="100%" cellspacing="2" cellpadding="2">
                      									<tr>
                        									<td class="main"><? echo tep_draw_textarea_field('review', 'soft', 60, 15); ?></td>
                      									</tr>
                      									<tr>
                        									<td class="smallText" align="right"><?=TEXT_NO_HTML?></td>
                      									</tr>
                      									<tr>
                        									<td class="main"><? echo '<b>' . SUB_TITLE_RATING . '</b> ' . TEXT_BAD . ' ' . tep_draw_radio_field('rating', '1') . ' ' . tep_draw_radio_field('rating', '2') . ' ' . tep_draw_radio_field('rating', '3') . ' ' . tep_draw_radio_field('rating', '4') . ' ' . tep_draw_radio_field('rating', '5') . ' ' . TEXT_GOOD; ?></td>
                      									</tr>
                    								</table>
                    							</td>
                  							</tr>
                						</table>
                					</td>
              					</tr>
              					<tr>
                					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
              					</tr>
              					<tr>
                					<td>
                						<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
                  							<tr class="buttonBoxContents">
                    							<td>
                    								<table border="0" width="100%" cellspacing="0" cellpadding="2">
                      									<tr>
                        									<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                        									<td class="main">
                        										<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_PRODUCT_INFO, tep_get_all_get_params(array('reviews_id', 'action'))), '', 'gray_button') ?>
															</td>
															<td class="main" align="right"><?php echo tep_submit_button(IMAGE_BUTTON_CONTINUE, IMAGE_BUTTON_CONTINUE, '', 'green_submit_button', $false); ?></td>
                        									<td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                      									</tr>
                    								</table>
                    							</td>
                  							</tr>
                						</table>
                					</td>
              					</tr>
            				</table>
            			</td>
            			<td width="<?php echo SMALL_IMAGE_WIDTH + 10; ?>" align="right" valign="top">
            				<table border="0" cellspacing="0" cellpadding="2">
              					<tr>
                					<td align="center" class="smallText">
<?
/*
  	if (tep_not_null($product_info['products_image'])) {
?>
		<script language="javascript"><!--
			document.write('<//?php echo '<a href="javascript:popupWindow(\\\'' . tep_href_link(FILENAME_POPUP_IMAGE, 'pID=' . $product_info['products_id']) . '\\\')">' . tep_image(DIR_WS_IMAGES . $product_info['products_image'], addslashes($product_info['products_name']), SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'hspace="5" vspace="5"') . '<br>' . TEXT_CLICK_TO_ENLARGE . '</a>'; ?>');
	//--></script>
		<noscript>
			<?php echo '<a href="' . tep_href_link(DIR_WS_IMAGES . $product_info['products_image']) . '" target="_blank">' . tep_image(DIR_WS_IMAGES . $product_info['products_image'], $product_info['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'hspace="5" vspace="5"') . '<br>' . TEXT_CLICK_TO_ENLARGE . '</a>'; ?>
		</noscript>
<?
  	}
  	echo '<p><!-- a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now') . '">' . tep_image_button('button_in_cart.gif', IMAGE_BUTTON_IN_CART) . '</a></p-->';
*/
?>
               						</td>
              					</tr>
            				</table>
          				</td>
        			</table>
        		</td>
      		</tr>
    	</table>
    </form>
<? } ?>