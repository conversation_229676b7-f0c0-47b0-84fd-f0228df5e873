<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
// create column list
$define_list = array('PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
                     'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
                     'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
                     'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
                     'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
                     'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
                     'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW);
asort($define_list);
$column_list = array();
reset($define_list);
while (list($key, $value) = each($define_list)) {
	if ($value > 0) $column_list[] = $key;
}

$select_column_list = '';

for ($i=0, $n=sizeof($column_list); $i<$n; $i++) {
	switch ($column_list[$i]) {
    	case 'PRODUCT_LIST_MODEL':
        	$select_column_list .= 'p.products_model, ';
        	break;
      	case 'PRODUCT_LIST_MANUFACTURER':
        	$select_column_list .= 'm.manufacturers_name, ';
        	break;
      	//case 'PRODUCT_LIST_QUANTITY':
        	//$select_column_list .= 'p.products_quantity, ';
        	//break;
      	case 'PRODUCT_LIST_IMAGE':
        	$select_column_list .= 'pd.products_image, p.products_bundle, p.products_bundle_dynamic, ';
        	break;
      	case 'PRODUCT_LIST_WEIGHT':
        	$select_column_list .= 'p.products_weight, ';
        	break;
   	}
}
  	$select_str = "select p.products_id, " . $select_column_list . " pd.products_name, p.products_price, p.products_tax_class_id, p.products_date_added, m.manufacturers_name ";
  	$from_str = " from " . TABLE_PRODUCTS . " as p, " . TABLE_CATEGORIES . " as c, " . TABLE_PRODUCTS_TO_CATEGORIES . " as p2c left join " . TABLE_MANUFACTURERS . " as m on (p.manufacturers_id = m.manufacturers_id), " . TABLE_PRODUCTS_DESCRIPTION . " as pd ";
  	$where_str = " where c.categories_status=1 and p.products_id = p2c.products_id and c.categories_id = p2c.categories_id and p.products_status = '1' and ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' ";
  	$order_str = " order by p.products_date_added DESC, pd.products_name";
  	$listing_sql = $select_str . $from_str . $where_str . $order_str;
?>
	<tr>
		<td><?	require(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING); ?></td>
	</tr>
</table>