<script type="text/javascript">
    var submitRedeem = 0;
    function redeemPoint(){
        jQuery('#redeemBtn').css({"display":"none"});
        if(submitRedeem == 0){
            submitRedeem = 1;
            document.redeem_point.submit();	
        }
    }
</script>
<h1><?= TITLE_MY_PROFILE ?></h1>
<div class="breakLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
    <tr>
        <td valign="top">
            <? ob_start(); ?>
            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="center">
                        <?
                        if (isset($_REQUEST['redeem']) && is_numeric($_REQUEST['redeem'])) {
                            $store_points_redeem_amount_select_sql = "	SELECT store_points_redeem_amount, store_points_request_currency, store_points_request_currency_amount 
													FROM " . TABLE_STORE_POINTS_REDEEM . " WHERE 
													store_points_redeem_id = '" . (int) $_REQUEST['redeem'] . "' 
														AND user_id = '" . (int) $customer_id . "' 
														AND user_role = 'customer'";
                            $store_points_redeem_amount_result_sql = tep_db_query($store_points_redeem_amount_select_sql);
                            if ($store_points_redeem_amount_row = tep_db_fetch_array($store_points_redeem_amount_result_sql)) {
                                $store_points_redeem_amount = (int) $store_points_redeem_amount_row['store_points_redeem_amount'];
                                $redeem_sucess_msg = sprintf(TEXT_REDEEMED_SUCCESS, $store_points_redeem_amount . ' ' . TEXT_OFFGAMERS_POINTS, $currencies->format($store_points_redeem_amount_row['store_points_request_currency_amount'], false, $store_points_redeem_amount_row['store_points_request_currency']), $_REQUEST['redeem'])
                                ?>
                                <table border="0" width="98%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '100%', '20') ?></td>
                                    </tr>
                                    <tr>
                                        <td align="center" valign="top" width="12%"><?= tep_image(DIR_WS_IMAGES . 'successful.gif', '', 54, 56) ?>&nbsp;</td>
                                        <td style="font-family: Arial,Tahoma,Verdana,Helvetica,sans-serif;font-size:20px;"><?= $redeem_sucess_msg ?></td>
                                    </tr>
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '100%', '20') ?></td>
                                    </tr>
                                </table>
                                <?
                            }
                        } else {
                            echo tep_draw_form('redeem_point', tep_href_link(FILENAME_REDEEM_POINT, 'action=redeem_point'), 'POST', '') . tep_draw_hidden_field("buyqty", '1');
                            ?>
                            <table border="0" width="98%" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td><?= tep_draw_separator('pixel_trans.gif', '100%', '10') ?></td>
                                </tr>
                                <tr>
                                    <td style="font-size:24px; font-weight:bold; text-align: center;">
                                        <? echo sprintf(TEXT_STORE_POINT_BALANCE, '<div class="wor-medium"></div>' . $printStorePoint); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><?= tep_draw_separator('pixel_trans.gif', '100%', '15') ?></td>
                                </tr>
                                <? if ($printStorePoint >= tep_get_configuration_key_value('OP_MIN_REDEMPTION')) { ?>
                                    <tr>
                                        <td align="right"><?= $sp_object->show_redeem_button() ?></td>
                                    </tr>
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '100%', '10') ?></td>
                                    </tr>
                                <? } ?>
                            </table>
                            </form>
                        <? } ?>
                    </td>
                </tr>
            </table>
            <?
            $op_balance_content_string = ob_get_contents();
            ob_end_clean();

            echo $page_obj->get_html_simple_rc_box('', $op_balance_content_string, 13);
            ?>
        </td>
    </tr>
    <tr>
        <td><?= tep_draw_separator('pixel_trans.gif', '100%', '10') ?></td>
    </tr>
    <tr>
        <td>
            <? ob_start(); ?>
            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="center">
                        <table border="0" width="98%" cellspacing="0" cellpadding="0" align="center">
                            <tr>
                                <td><?= tep_draw_separator('pixel_trans.gif', '100%', '6') ?></td>
                            </tr>
                            <tr>
                                <td style="font-size:16px;font-weight:bold;"><?= HEADER_TITLE_POINTS_HISTORY; ?></td>
                            </tr>
                            <tr>
                                <td><?= tep_draw_separator('pixel_trans.gif', '100%', '6') ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="boxHeaderLeft"><!-- --></div>
                                    <div class="boxHeaderCenter" style="width:8px"><!-- --></div>
                                    <div class="boxHeaderCenter" style="width:122px">
                                        <font><?= TABLE_HEADING_DATE ?></font>
                                    </div>
                                    <div class="boxHeaderDivider"><!-- --></div>
                                    <div class="boxHeaderCenter" style="width:122px;">
                                        <font><?= TABLE_HEADING_ORDER_NO ?></font>
                                    </div>
                                    <div class="boxHeaderDivider"><!-- --></div>
                                    <div class="boxHeaderCenter" style="width:222px;">
                                        <font style="text-align:right;"><?= TABLE_HEADING_POINTS_EARNED ?></font>
                                    </div>
                                    <div class="boxHeaderDivider"><!-- --></div>
                                    <div class="boxHeaderCenter" style="width:122px;">
                                        <font style="text-align:center;"><?= TABLE_HEADING_POINT_STATUS ?></font>
                                    </div>
                                    <div class="boxHeaderCenter" style="width:20px"><!-- --></div>
                                    <div class="boxHeaderRight"><!-- --></div>
                                </td>
                            </tr>
                            <tr>
                                <td><?= tep_draw_separator('pixel_trans.gif', '100%', '6') ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                        <?
                                        $orders_info_select_sql = "select store_points_history_date, store_points_history_debit_amount, store_points_history_credit_amount, store_points_history_trans_id, store_points_history_activity_type, store_points_history_activity_desc from " . TABLE_STORE_POINTS_HISTORY . " where customer_id = '" . (int) $customer_id . "' AND store_points_history_activity_desc_show = 1 union select date_purchased as store_points_history_date, cc_type as store_points_history_debit_amount, cc_type as store_points_history_credit_amount, orders_id as store_points_history_trans_id, cc_type as store_points_history_activity_type, orders_rebated as store_points_history_activity_desc from " . TABLE_ORDERS . " where customers_id = '" . (int) $customer_id . "' and orders_status = 3 and orders_rebated = 0 order by store_points_history_date desc";
                                        $orders_info_query = tep_db_query($orders_info_select_sql);
                                        while ($orders_info_row = tep_db_fetch_array($orders_info_query)) {
                                            $points_rebated = 0;
                                            $sp_history_activity_type = array('MI', 'MR', 'P', 'D', 'PD');

                                            if (in_array($orders_info_row['store_points_history_activity_type'], $sp_history_activity_type)) {
                                                if ($orders_info_row['store_points_history_activity_type'] == 'P') {
                                                    ?>
                                                    <tr>
                                                        <td class="main" width="158px"><?= $orders_info_row['store_points_history_date'] ?></td>
                                                        <td class="main" width="133px"><?= $orders_info_row['store_points_history_trans_id'] ?></td>
                                                        <td class="main" width="236px" style="text-align:right;"><?= (int) $orders_info_row['store_points_history_credit_amount'] ?></td>
                                                        <td class="main" style="text-align:center;"><?= 'Credited' ?></td>
                                                    </tr>
                                                <? } else if ($orders_info_row['store_points_history_activity_type'] == 'PD') { ?>
                                                    <tr>
                                                        <td class="main" width="158px"><?= $orders_info_row['store_points_history_date'] ?></td>
                                                        <td class="main" width="133px"><?= $orders_info_row['store_points_history_trans_id'] ?></td>
                                                        <td class="main" width="236px" style="text-align:right;">-<?= (int) $orders_info_row['store_points_history_debit_amount'] ?></td>
                                                        <td class="main" style="text-align:center;"><?= 'Debit' ?></td>
                                                    </tr>
                                                <? } else { ?>
                                                    <tr>
                                                        <td class="main" width="158px"><?= $orders_info_row['store_points_history_date'] ?></td>
                                                        <td class="main" colspan="3"><?= nl2br($orders_info_row['store_points_history_activity_desc']) ?></td>
                                                    </tr>
                                                    <?
                                                }
                                            } else {
                                                $orders_products_select_sql = "	SELECT products_good_delivered_price, final_price, op_rebate 
										FROM " . TABLE_ORDERS_PRODUCTS . " 
										WHERE orders_id = '" . (int) $orders_info_row['store_points_history_trans_id'] . "' 
											AND parent_orders_products_id < 1 
											AND orders_products_is_compensate = 0";
                                                $orders_products_result_sql = tep_db_query($orders_products_select_sql);
                                                while ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
                                                    $points_rebated += (int) ($orders_products_row['op_rebate']);
                                                }
                                                ?>
                                                <tr>
                                                    <td class="main" width="158px"><?= $orders_info_row['store_points_history_date'] ?></td>
                                                    <td class="main" width="133px"><?= $orders_info_row['store_points_history_trans_id'] ?></td>
                                                    <td class="main" width="236px" style="text-align:right;"><?= $points_rebated ?></td>
                                                    <td class="main" style="text-align:center;"><?= (($orders_info_row['store_points_history_activity_desc'] == 1) ? 'Credited' : 'Pending') ?></td>
                                                </tr>
                                            <? } ?>
                                            <tr><td colspan="5"><div class="row_separator"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></td></tr>									
                                            <?
                                        }
                                        ?>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td><?= tep_draw_separator('pixel_trans.gif', '100%', '5') ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <?
            $op_history_content_string = ob_get_contents();
            ob_end_clean();

            echo $page_obj->get_html_simple_rc_box('', $op_history_content_string, 13);
            ?>
        </td>
    </tr>
    <tr>
        <td style="font-weight:bold">&nbsp;<?= TEXT_OP_NOTICE ?></td>
    </tr>
</table>
<div class="break_line"></div>