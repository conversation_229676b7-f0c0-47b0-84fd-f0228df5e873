<?
global $customers_ordered_sc, $gst_amt;

$customers_ordered_converted_sc = $customers_ordered_sc;
if ($customers_sc_currency != $currency) {
    $customers_ordered_converted_sc = $currencies->advance_currency_conversion($customers_ordered_sc, $customers_sc_currency, $currency);
}

// Sub-Total without tax and surcharge
$customers_ordered_converted_sc_subtotal = $customers_ordered_converted_sc;
$customers_ordered_converted_sc += $gst_amt;

$cart = new sc_shopping_cart;
$cart->restore_contents();

if (file_exists('checkout_xmlhttp.js.php')) {
    include_once ('checkout_xmlhttp.js.php');
}
?>
<link rel="stylesheet" href="images/css/cssmenu.css">
<script type="text/javascript" src="<?= DIR_WS_JAVASCRIPT ?>jquery-latest.pack.js"></script>
<script language="javascript" src="<?= DIR_WS_JAVASCRIPT ?>ogm_tooltips/ogm_tooltips.js"></script>
<link rel="stylesheet" href="<?= DIR_WS_JAVASCRIPT ?>ogm_tooltips/ogm_tooltips.css" type="text/css">
<script language="javascript">
    function payment_methods_display(pmt_id) {
        if (jQuery('#pmt_' + pmt_id).css('display') == 'none') {
            jQuery('#pmt_' + pmt_id).fadeIn('fast');
            jQuery('#sh_' + pmt_id).attr('class', 'boxHeaderCollapse');
        } else {
            jQuery('#pmt_' + pmt_id).fadeOut('fast');
            jQuery('#sh_' + pmt_id).attr('class', 'boxHeaderExpand');
        }
    }
</script>
<table cellpadding="7" cellspacing="0" border="0" width="100%" height="618px">
    <?
    if (isset($_REQUEST['error_message']) && tep_not_null($_REQUEST['error_message'])) {
        $messageStack->add('checkout_payment', $_REQUEST['error_message']);
    }

    if ($messageStack->size('checkout_payment') > 0) {
        ?>
        <tr>
            <td><?= $messageStack->output('checkout_payment') ?></td>
        </tr>
        <tr>
            <td><?= tep_draw_separator('pixel_trans.gif', '100%', '5') ?></td>
        </tr>
        <?
    }
    ?>
    <tr>
        <td valign="top">
            <h1><?= HEADING_TITLE ?></h1>
            <?= tep_draw_separator("pixel_trans.gif", "100%", "3") ?>
            <div style="float:left; width:100%">
                <div class="loginColumnBorder" id="body_content_div" style="float:left; width:100%">
                    <?
                    $selection = $payment_modules->selection($currency, $payment);
                    $sorted_selection = $payment_modules->sort_selection($selection);

                    $default_selected = '';
                    $radio_buttons = 0;
                    if (class_exists('ot_surcharge')) {
                        $ot_surcharge = new ot_surcharge();
                        $ot_surcharge->fees_type = 'sc_topup';
                    }

                    $payment_list_arr = array();
                    $payment_gateway_array = array();

                    $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
  											FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
  											INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
  												ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($languages_id) . "')
  											WHERE payment_methods_types_mode = 'RECEIVE'
  												AND FIND_IN_SET('SC', pmt.payment_methods_types_sites)";
                    $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                    if (tep_db_num_rows($payment_methods_types_result_sql) < 1) {
                        $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
	  											FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
	  											INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
	  												ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($default_languages_id) . "')
	  											WHERE payment_methods_types_mode = 'RECEIVE'
  													AND FIND_IN_SET('SC', pmt.payment_methods_types_sites)";
                        $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                    }
                    
                    if(isset($sorted_selection[0])){
                        $payment_list_arr[0]['show'] = 0;
                        $payment_list_arr[0]['description'] = TEXT_FEATURED_PAYMENT_METHODS;
                        $payment_list_arr[0]['type'] = 'payment'; 
                        if(tep_not_null($payment)){
                            $payment_array = explode(':~:', $payment);
                            if (sizeof($payment_array) > 1) {
                                if($payment_array[0]=='pm_0'){
                                    $payment_list_arr[0]['show'] = 1;
                                    $payment_gateway_category_show = true;
                                }
                            } 
                        }
                    }
                    while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
                        if (isset($selection[$payment_methods_types_row['payment_methods_types_id']])) {
                            $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 0;
                            $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['description'] = $payment_methods_types_row['payment_methods_types_description'];
                            $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['type'] = 'payment';

                            if (tep_not_null($payment) && $payment_gateway_category_show!=true) {
                                if (isset($selection[$payment_methods_types_row['payment_methods_types_id']]['show'])) {
                                    $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 1;
                                    $payment_gateway_category_show = true;
                                }
                            }
                        }
                    }
                    if(!$payment_gateway_category_show) {
                        if(isset($payment_list_arr[0])){
                            $payment_list_arr[0]['show'] = 1;
                            $payment_gateway_category_show = true;
                        }
                    }
                    
                    // GST : disable currency drop down when GST condition valid
                    if (!tep_not_null($_SESSION['RegionGST']['tax_title'])) {
                        $payment_list_arr[] = array('show' => 0,
                            'description' => 'MORE PAYMENT METHODS',
                            'type' => 'currency'
                        );
                    }

                    if (tep_not_null($checkout_button)) {
                        $cart_html .= $payment_modules->draw_confirm_button();
                    } else {
                        $cart_html .= '<table border="0" cellspacing="0" cellpadding="0" width="100%">
									<tr>
										<td width="500px"></td>
										<td>' .
                                tep_image_button2('gray', 'javascript:this.void(0)', IMAGE_BUTTON_SECURE_CHECKOUT, 200) .
                                '	</td>
									</tr>
									<tr>
										<td colspan="2"> ' . tep_draw_separator("pixel_trans.gif", "100%", "8") . '</td>
									</tr>
								</table>';
                    }

                    $count_payment_methods = 0;
                    $selected_payment_flag = 0; // 0 = none, 1 = default currency, 2 = USD (others currency)
                    $displayed_payment_methods_array = array();
                    $show_paypal_flag = true;

                    foreach ($payment_list_arr as $gw_id => $payment_cat_display) {
                        $pg_class = '';
                        $pgt_class_name = 'boxHeaderCollapse';

                        $payment_methods_array = array();

                        switch ($payment_cat_display['type']) {
                            case 'currency':
                                $pg_class = 'class="hide"';
                                $pgt_class_name = 'boxHeaderExpand';
                                break;
                            default:
                                if ($payment_gateway_category_show) {
                                    if (tep_not_null($payment)) {
                                        $pg_class = 'class="hide"';
                                        $pgt_class_name = 'boxHeaderExpand';

                                        if ($payment_cat_display['show'] == 1) {
                                            $pg_class = '';
                                            $pgt_class_name = 'boxHeaderCollapse';
                                        }
                                    }
                                }
                                break;
                        }

                        if (isset($payment_cat_display['type']) && $payment_cat_display['type'] == 'payment') {

                            foreach ($sorted_selection[$gw_id] as $pm_id => $pm_info_array) {

                                $pm_html = '';
                                if ($pm_info_array['payment_gateway_code'] == 'paypal' || $pm_info_array['payment_gateway_code'] == 'paypalEC') {
                                    $show_paypal_flag = false;
                                }
                                $supported_currencies_array = array();
                                $unique_name = $gw_id . ':~:' . $pm_info_array['payment_gateway_code'] . ':~:' . $pm_id;
                                $payment_title = $pm_info_array['payment_methods_description_title'];
                                $pro_img = $pm_info_array['payment_methods_logo'];

                                $pro_img_w = 0;
                                $pro_img_h = 0;

                                for ($curr_cnt = 0; $curr_cnt < sizeof($pm_info_array['currency']); $curr_cnt++) {
                                    $supported_currencies_array[] = array('id' => $pm_info_array['currency'][$curr_cnt], 'text' => $pm_info_array['currency'][$curr_cnt]);
                                }

                                if (is_object($ot_surcharge)) {
                                    $surcharge_amt = $ot_surcharge->calculate_surcharge_by($unique_name, $currency, 4, $customers_ordered_converted_sc);
                                    $surcharge_html = " <div id='surcharge_" . $radio_buttons . "'><table><tr><td>" . ($surcharge_amt > 0 ? "(+ " . $currencies->format($surcharge_amt, false, $currency) . " " . ENTRY_PAYMENT_SURCHARGE . ")" : "") . "</td></tr></table></div>";
                                } else {
                                    $surcharge_html = "  <div id='surcharge_" . $radio_buttons . "'><table><tr><td></td></tr></table></div>";
                                }

                                $payment_title = $pm_info_array['payment_methods_description_title'] . $surcharge_html;

                                if ($pm_info_array['payment_methods_receive_status_mode'] == 1) {
                                    $radio_para = 'onclick="updateScCurrency(' . $radio_buttons . ', \'change\');"';

                                    if (tep_not_null($payment) && $payment == 'pm_' . $unique_name) {
                                        $pm_html .= '<div class="moduleRowSelected" id="defaultSelected" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateScCurrency(' . $radio_buttons . ', \'\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                        $default_selected = $radio_buttons;
                                        $selected_payment_flag = 1;
                                    } else {
                                        $pm_html .= '<div class="moduleRow" id="row_pm_' . $radio_buttons . '" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateScCurrency(' . $radio_buttons . ', \'\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                    }
                                } else {
                                    $radio_para = 'disabled';
                                    $payment_title .= '<br>' . $pm_info_array['payment_methods_status_message'];

                                    $pm_html .= '	<div class="moduleRow" id="row_pm_' . $radio_buttons . '" valign="top">';
                                }

                                $pm_html .= '			<table border="0" width="98%" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
                                            </tr>
                                            <tr>
                                                <td width="3%"></td>
                                                <td width="5%">' . tep_draw_radio_field('payment', 'pm_' . $unique_name, false, ' id="pm_' . $radio_buttons . '" ' . $radio_para) . '</td>';

                                $aws_obj = new ogm_amazon_ws();
                                $aws_obj->set_bucket_key('BUCKET_STATIC');
                                $aws_obj->set_filepath('images/payment/');
                                $pm_logo_path = '';

                                $image_info_array = $aws_obj->get_image_info($pro_img);

                                if (tep_not_null($image_info_array)) {
                                    $pm_logo_path = $image_info_array['src'];
                                } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'payment/' . $pro_img)) {
                                    $pm_logo_path = DIR_WS_IMAGES . 'payment/' . $pro_img;
                                }

                                if (tep_not_null($pm_logo_path))
                                    list($pro_img_w, $pro_img_h) = getimagesize($pm_logo_path);

                                $pm_html .= "<td width='8%'>" . ($pro_img_w > 0 ? tep_image($pm_logo_path, '', $pro_img_w, $pro_img_h) : '&nbsp;') . "</td>";

                                unset($aws_obj);

                                $pm_html .= '			<td class="main" style="padding-left:12px;">' . $payment_title . '</td>
                                                <td width="10%" style="text-align:right;">' .
                                        //tep_draw_pull_down_menu('cr_' . $radio_buttons, $supported_currencies_array, $_SESSION['currency'], ' disabled onchange="updateScCurrency(' . $radio_buttons . ', \'change\');" id="cr_' . $radio_buttons . '"') .
                                        '</td>
                                            </tr>';

                                //if ($pm_info_array['show_billing_address'] == 'True' || $pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_ic'] == 'True') {  // currenct for mobile money only
                                $pm_html .= '<tr>
                                                <td width="8%" colspan="2"></td>
                                                <td colspan="3">
                                                    <div class="' . ($payment == 'pm_' . $unique_name ? '' : 'hide') . ' system_cust_pm_info" id="pm_cust_info_' . $radio_buttons . '">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="0">';

                                $display_confirm_link = false;
                                if ($pm_info_array['show_billing_address'] == 'True') {
                                    $display_confirm_link = true;
                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2">' .
                                            tep_address_label($customer_id, $billto, true, ' ', '<br>') . '<br>' .
                                            '<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=scchkout', 'SSL') . '" id="edit_address"><b>' . TEXT_EDIT_ADDRESS . '</b></a>
                                                                </td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_surname'] == 'True' || $pm_info_array['show_city'] == 'True') {
                                    $account_sql = "SELECT customers_telephone, customers_default_address_id, customers_lastname
                                                    FROM " . TABLE_CUSTOMERS . "
                                                    WHERE customers_id ='" . (int) $customer_id . "'";
                                    $account_result_sql = tep_db_query($account_sql);
                                    $account = tep_db_fetch_array($account_result_sql);
                                }

                                if ($pm_info_array['show_city'] == 'True' || $pm_info_array['show_street'] == 'True' || $pm_info_array['show_zip'] == 'True') {
                                    $customers_info_select_sql = "	SELECT entry_street_address, entry_postcode, entry_city
                                                                    FROM " . TABLE_ADDRESS_BOOK . "
                                                                    WHERE address_book_id = '" . (int) $account['customers_default_address_id'] . "'
                                                                        AND customers_id ='" . (int) $customer_id . "'";
                                    $customers_info_result_sql = tep_db_query($customers_info_select_sql);
                                    $customers_info_row = tep_db_fetch_array($customers_info_result_sql);
                                }

                                if ($pm_info_array['show_contact_number'] == 'True') {
                                    $display_confirm_link = true;
                                    $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                                    if (!isset($_SESSION['customers_telephone'])) {
                                        $_SESSION['customers_telephone'] = $account['customers_telephone'];
                                    }
                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td width="15%">' . ENTRY_PAYMENT_CONTACT_NUMBER . '</td>
                                                                <td>+' . $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . tep_draw_input_field('telephone_number', tep_mask_telephone($_SESSION['customers_telephone']), ' id="telephone_number" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_ic'] == 'True') {
                                    $display_confirm_link = true;
                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER . '</td>
                                                                <td>' . tep_draw_input_field('identify_number', (isset($_SESSION['identify_number']) ? $_SESSION['identify_number'] : ''), ' id="identify_number" ') . '<br>' . TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_surname'] == 'True') {
                                    $display_confirm_link = true;
                                    if (!isset($_SESSION['checkout_surname'])) {
                                        $_SESSION['checkout_surname'] = $account['customers_lastname'];
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_SURNAME . '</td>
                                                                <td>' . tep_draw_input_field('checkout_surname', $_SESSION['checkout_surname'], ' id="checkout_surname" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_housenumber'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_housenumber'])) {
                                        $_SESSION['checkout_housenumber'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER . '</td>
                                                                <td>' . tep_draw_input_field('checkout_housenumber', $_SESSION['checkout_housenumber'], ' id="checkout_housenumber" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_street'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_street'])) {
                                        $_SESSION['checkout_street'] = $customers_info_row['entry_street_address'];
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_STREET . '</td>
                                                                <td>' . tep_draw_input_field('checkout_street', $_SESSION['checkout_street'], ' id="checkout_street" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_city'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_city'])) {
                                        $_SESSION['checkout_city'] = $customers_info_row['entry_city'];
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top">
                                                                <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_CITY . '</td>
                                                                <td>' . tep_draw_input_field('checkout_city', $_SESSION['checkout_city'], ' id="checkout_city" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_zip'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_zip'])) {
                                        $_SESSION['checkout_zip'] = $customers_info_row['entry_postcode'];
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ZIP . '</td>
                                                                <td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_zip'], ' id="checkout_zip" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_accountname'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_accountname'])) {
                                        $_SESSION['checkout_accountname'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME . '</td>
                                                                <td>' . tep_draw_input_field('checkout_accountname', $_SESSION['checkout_accountname'], ' id="checkout_accountname" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_accountnumber'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_accountnumber'])) {
                                        $_SESSION['checkout_accountnumber'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER . '</td>
                                                                <td>' . tep_draw_input_field('checkout_accountnumber', $_SESSION['checkout_accountnumber'], ' id="checkout_accountnumber" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_account_number'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_account_number'])) {
                                        $_SESSION['checkout_account_number'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNT_NUMBER . '</td>
                                                                <td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_account_number'], ' id="checkout_account_number" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_bankcode'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_bankcode'])) {
                                        $_SESSION['checkout_bankcode'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BANKCODE . '</td>
                                                                <td>' . tep_draw_input_field('checkout_bankcode', $_SESSION['checkout_bankcode'], ' id="checkout_bankcode" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_branchcode'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_branchcode'])) {
                                        $_SESSION['checkout_branchcode'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BRANCHCODE . '</td>
                                                                <td>' . tep_draw_input_field('checkout_branchcode', $_SESSION['checkout_branchcode'], ' id="checkout_branchcode" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_directdebittext'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_directdebittext'])) {
                                        $_SESSION['checkout_directdebittext'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT . '</td>
                                                                <td>' . tep_draw_input_field('checkout_directdebittext', $_SESSION['checkout_directdebittext'], ' id="checkout_directdebittext" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_vouchernumber'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['show_voucher_number'])) {
                                        $_SESSION['show_voucher_number'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER . '</td>
                                                                <td>' . tep_draw_input_field('checkout_voucher_number', $_SESSION['show_voucher_number'], ' id="checkout_voucher_number" ') . '</td>
                                                            </tr>';
                                }

                                if ($pm_info_array['show_vouchervalue'] == 'True') {
                                    $display_confirm_link = true;

                                    if (!isset($_SESSION['checkout_voucher_value'])) {
                                        $_SESSION['checkout_voucher_value'] = '';
                                    }

                                    $pm_html .= '			<tr>
                                                                <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                            </tr>
                                                            <tr valign="top" width="15%">
                                                                <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE . '</td>
                                                                <td>' . tep_draw_input_field('checkout_voucher_value', $_SESSION['checkout_voucher_value'], ' id="checkout_voucher_value" ') . '</td>
                                                            </tr>';
                                }

                                if ($display_confirm_link) {
                                    $pm_html .= '			<tr>
                                                                <td colspan="2"><div><a href="javascript:update_pm_cust_info(\'pm_cust_info_' . $radio_buttons . '\')"><b>' . LINK_UPDATE_CUSTOMER_PAYMENT_INFO . '</b></a></div></td>
                                                            </tr>';
                                }
                                $pm_html .= '			</table>
                                                    </div>
                                                </td>
                                            </tr>';
                                //}
                                $pm_html .= '	<tr>
                                                <td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
                                            </tr>
                                        </table>
                                    </div>';

                                $displayed_payment_methods_array[] = $pm_id;
                                $payment_methods_array[] = array('id' => 'vmenu_pm_' . $pm_id, 'desc' => $pm_html, 'href' => '');
                                $radio_buttons++;
                                $count_payment_methods++;
                            }


                            $payment_gateway_array[] = array('desc' => $payment_cat_display['description'],
                                'href' => 'javascript:void(0);',
                                'slist' => $payment_methods_array,
                                'default_open' => $payment_cat_display['show'],
                            );
                        }
                    }

                    $pg_footer_html = '	<div style="width:100%;">
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
								</tr>
								<tr>
									<td width="550px" style="" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr>
												<td>' . TEXT_INFO_CHANGE_CHECKOUT_CURRENCY . '</td>
											</tr>
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
											</tr>
											<tr>
												<td><b>' . TEXT_INFO_CURRENT_CHECKOUT_CURRENCY . ':</b>&nbsp;&nbsp;' . tep_draw_pull_down_menu('sel_checkout_currency', $currencies_array, $currency, ' id="sel_checkout_currency" onchange="set_localization_value(\'currency\' , this.value)" style="width:200px;" ') . '</td>
											</tr>
                                            <tr><td><br /><br /></td></tr>
                                            <tr><td>' . TEXT_INFO_PAYMENT_SURCHARGE_DESC . '</td></tr>
										</table>
									</td>
									<td width="*%" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr>
												<td id="div_sc_subtotal_balance">
													<div style="width:200px;">' .
                            $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . TEXT_SUB_TOTAL . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont">' . $currencies->format($customers_ordered_converted_sc_subtotal, false, $currency) . '</font></b></span></td></tr></table>', 43) .
                            '</div>
												</td>
											</tr>
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '2') . '</td>
											</tr>
										</table>';
                    if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst') && tep_not_null($_SESSION['RegionGST']['tax_title'])) {
                        $pg_footer_html .= '			<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
											</tr>
											<tr>
												<td style="text-align:right;width:200px"><div style="width:200px;">' .
                                $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . $ot_gst->gst_display_title . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont">' . $ot_gst->output[0]['display_text'] . '</font></b></td></tr></table>', 43) .
                                '</div></td>
											</tr>
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '2') . '</td>
											</tr>
										</table>';
                    }

                    $pg_footer_html .= '				<div id="div_sc_surcharge" style="display:none;">
											<table border="0" width="100%" cellspacing="0" cellpadding="0">
												<tr>
													<td style="text-align:right;width:200px" id="div_sc_surcharge_balance">
														<div style="width:200px;">' .
                            $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . $ot_surcharge->display_title . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont"></font></b></span></td></tr></table>', 43) .
                            '				</div>
													</td>
												</tr>
												<tr>
													<td>' . tep_draw_separator('pixel_trans.gif', '100%', '2') . '</td>
												</tr>
											</table>
										</div>
									</div>
									<div>
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr>
												<td id="div_sc_total_balance" style="text-align:right;width:200px">
													<div style="width:200px;">' .
                            $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . TEXT_TOTAL . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont">' . $currencies->format($customers_ordered_converted_sc, false, $currency) . '</font></b></span></td></tr></table>', 43) .
                            '	</div>
												</td>
											</tr>
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
											</tr>';

                    $sc_promotion_percentage = store_credit::get_sc_promotion_percentage();
                    if ($sc_promotion_percentage > 0) {
                        $new_sc_to_deliver = $customers_ordered_sc * (1 + ($sc_promotion_percentage / 100));

                        $pg_footer_html .= '			<tr>
                                            <td id="div_sc_total_balance" style="text-align:right;width:200px">
                                                <div id ="extraSc" style="width:200px;">' .
                                $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . sprintf(TEXT_INFO_SC_PROMOTION_PERCENTAGE, $sc_promotion_percentage) . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont">' . $currencies->format($new_sc_to_deliver, false, $customers_sc_currency) . '</font></b></span></td></tr></table>', 43) .
                                '
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
                                        </tr>';
                    }
                    if (isset($opContent['op'])) {
                        $opDiv = '<div id="scOP" style="width:200px;">' . $page_obj->get_html_simple_rc_box('', '<table><tr class="rebate"><td style="width:87px;">' . $opContent['op']['name'] . '</td><td valign="top">:</td><td class="paymentPriceValue"><span class="op"><b><font class="largeFont">' . $opContent['op']['value'] . '</font></b></span></td></tr></table>', 43) . '</div>';
                    } else {
                        $opDiv = '<div id="scOP" style="width:200px;display:none;"></div>';
                    }
                    $pg_footer_html .= '                <tr>
                                            <td id="div_sc_op" style="text-align:right;width:200px">
                                            ' . $opDiv . '
                                            </td>
                                        </tr>
                                        ';
                    if (isset($opContent['op'])) {
                        $pg_footer_html .= '<tr>
                                            <td>' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
                                        </tr>';
                    }
                    if (isset($opContent['extra_op'])) {
                        $opExtraDiv = '<div id="scExtraOp" style="width:200px;">' . $page_obj->get_html_simple_rc_box('', '<table><tr class="rebate"><td style="width:87px;">' . $opContent['extra_op']['name'] . '</td><td valign="top">:</td><td class="paymentPriceValue"><span class="extraOp"><b><font class="largeFont">' . $opContent['extra_op']['value'] . '</font></b></span></td></tr></table>', 43) . '</div>';
                    } else {
                        $opExtraDiv = '<div id="scExtraOp" style="width:200px;display:none;"></div>';
                    }
                    $pg_footer_html .= '                <tr>
                                            <td id="div_sc_extra_op" style="text-align:right;width:200px">
                                               ' . $opExtraDiv . '
                                            </td>
                                        </tr>';
                    $pg_footer_html .= '				<tr>
											<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
										</tr>
									</table>';

                    echo $page_obj->get_html_expandable_vmenu_box(sprintf(HEADING_SUBTITLE, $currencies->currencies[$currency]['title'] . ' (' . $currencies->currencies[$currency]['symbol_left'] . $currencies->currencies[$currency]['symbol_right'] . ')'), $payment_gateway_array, 12, '', 12);

                    if ((!isset($_SESSION['RegionGST']['tax_title']) || !tep_not_null($_SESSION['RegionGST']['tax_title'])) && ($count_payment_methods < 5 || $show_paypal_flag === true) && $currency != DEFAULT_CURRENCY) {
                        $selection = $payment_modules->selection(DEFAULT_CURRENCY, $payment);
                        $sorted_selection = $payment_modules->sort_selection($selection);

                        $payment_list_arr = array();
                        $payment_gateway_array = array();
                        $payment_gateway_category_show = false;

                        $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
												FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
												INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
													ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($languages_id) . "')
												WHERE payment_methods_types_mode = 'RECEIVE'
													AND FIND_IN_SET('SC', pmt.payment_methods_types_sites)";
                        $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                        if (tep_db_num_rows($payment_methods_types_result_sql) < 1) {
                            $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
													FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
													INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
														ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($default_languages_id) . "')
													WHERE payment_methods_types_mode = 'RECEIVE'
														AND FIND_IN_SET('SC', pmt.payment_methods_types_sites)";
                            $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                        }

                        while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
                            $show_after_filter_flag = false;

                            if (count($selection[$payment_methods_types_row['payment_methods_types_id']])) {
                                foreach ($selection[$payment_methods_types_row['payment_methods_types_id']] as $pg_id_loop => $pm_data_loop) {
                                    if (is_int($pg_id_loop)) {
                                        foreach ($pm_data_loop as $pm_id_loop => $pm_data_loop) {
                                            if (!in_array($pm_id_loop, $displayed_payment_methods_array)) {
                                                $show_after_filter_flag = true;
                                                break 2;
                                            }
                                        }
                                    }
                                }
                            }

                            if ($show_after_filter_flag && isset($selection[$payment_methods_types_row['payment_methods_types_id']])) {
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 0;
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['description'] = $payment_methods_types_row['payment_methods_types_description'];
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['type'] = 'payment';

                                if (tep_not_null($payment) && $selected_payment_flag == 0) {
                                    if (isset($selection[$payment_methods_types_row['payment_methods_types_id']]['show'])) {
                                        $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 1;
                                        $payment_gateway_category_show = true;
                                    }
                                }
                            }
                        }

                        echo '<br>';
                        echo '<div style="width:45%;float:left;margin-top:5px;"><div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></div>';
                        echo '<div style="width:10%;text-align:center;float:left;"><b>' . TEXT_INFO_PAYMENT_IN_USD_OR . '</b></div>';
                        echo '<div style="width:45%;float:left;margin-top:5px;"><div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></div>';
                        echo '<br><br>';

                        $payment_gateway_array = array();
                        $display_flag = false;

                        if ($customers_sc_currency != DEFAULT_CURRENCY) {
                            $customers_ordered_converted_sc_other = $currencies->advance_currency_conversion($customers_ordered_sc, $customers_sc_currency, DEFAULT_CURRENCY);
                        }

                        foreach ($payment_list_arr as $gw_id => $payment_cat_display) {
                            $pg_class = '';
                            $pgt_class_name = 'boxHeaderCollapse';
                            $actual_pg_show = 0;

                            if ($payment_gateway_category_show) {
                                if (tep_not_null($payment)) {
                                    $pg_class = 'class="hide"';
                                    $pgt_class_name = 'boxHeaderExpand';

                                    if ($payment_cat_display['show'] == 1) {
                                        $pg_class = '';
                                        $pgt_class_name = 'boxHeaderCollapse';
                                    }
                                }
                            }

                            if (isset($payment_cat_display['type']) && $payment_cat_display['type'] == 'payment') {
                                $payment_methods_array = array();

                                foreach ($sorted_selection[$gw_id] as $pm_id => $pm_info_array) {

                                    if ($show_paypal_flag === true && $count_payment_methods >= 5 && ($pm_info_array['payment_gateway_code'] != 'paypal' || $pm_info_array['payment_gateway_code'] != 'paypalEC')) {
                                        continue;
                                    }
                                    if (in_array($pm_id, $displayed_payment_methods_array)) {
                                        continue;
                                    }
                                    $actual_pg_show++;
                                    $display_flag = true;

                                    $pm_html = '';

                                    $supported_currencies_array = array();
                                    $unique_name = $gw_id . ':~:' . $pm_info_array['payment_gateway_code'] . ':~:' . $pm_id;
                                    $payment_title = $pm_info_array['payment_methods_description_title'];
                                    $pro_img = $pm_info_array['payment_methods_logo'];

                                    $pro_img_w = 0;
                                    $pro_img_h = 0;

                                    for ($curr_cnt = 0; $curr_cnt < sizeof($pm_info_array['currency']); $curr_cnt++) {
                                        $supported_currencies_array[] = array('id' => $pm_info_array['currency'][$curr_cnt], 'text' => $pm_info_array['currency'][$curr_cnt]);
                                    }

                                    if (is_object($ot_surcharge)) {
                                        $surcharge_amt = $ot_surcharge->calculate_surcharge_by($unique_name, DEFAULT_CURRENCY, 4, $customers_ordered_converted_sc_other);
                                        $surcharge_html = " <div id='surcharge_" . $radio_buttons . "'><table><tr><td>" . ($surcharge_amt > 0 ? "(+ " . $currencies->format($surcharge_amt, false, DEFAULT_CURRENCY) . " " . ENTRY_PAYMENT_SURCHARGE . ")" : "") . "</td></tr></table></div>";
                                    } else {
                                        $surcharge_html = "  <div id='surcharge_" . $radio_buttons . "'><table><tr><td></td></tr></table></div>";
                                    }

                                    $payment_title = $pm_info_array['payment_methods_description_title'] . $surcharge_html;

                                    if ($pm_info_array['payment_methods_receive_status_mode'] == 1) {
                                        $radio_para = 'onclick="updateScCurrency(' . $radio_buttons . ', \'change\', \'' . DEFAULT_CURRENCY . '\');"';

                                        if (tep_not_null($payment) && $payment == 'pm_' . $unique_name) {
                                            $pm_html .= '<div class="moduleRowSelected" id="defaultSelected" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateScCurrency(' . $radio_buttons . ', \'\', \'' . DEFAULT_CURRENCY . '\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                            $default_selected = $radio_buttons;
                                            $selected_payment_flag = 2;
                                        } else {
                                            $pm_html .= '<div class="moduleRow" id="row_pm_' . $radio_buttons . '" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateScCurrency(' . $radio_buttons . ', \'\', \'' . DEFAULT_CURRENCY . '\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                        }
                                    } else {
                                        $radio_para = 'disabled';
                                        $payment_title .= '<br>' . $pm_info_array['payment_methods_status_message'];

                                        $pm_html .= '	<div class="moduleRow" id="row_pm_' . $radio_buttons . '" valign="top">';
                                    }

                                    $pm_html .= '			<table border="0" width="98%" cellspacing="0" cellpadding="0">
													<tr>
								    					<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
													</tr>
													<tr>
														<td width="3%"></td>
														<td width="5%">' . tep_draw_radio_field('payment', 'pm_' . $unique_name, false, ' id="pm_' . $radio_buttons . '" ' . $radio_para) . '</td>';

                                    $aws_obj = new ogm_amazon_ws();
                                    $aws_obj->set_bucket_key('BUCKET_STATIC');
                                    $aws_obj->set_filepath('images/payment/');
                                    $pm_logo_path = '';

                                    $image_info_array = $aws_obj->get_image_info($pro_img);

                                    if (tep_not_null($image_info_array)) {
                                        $pm_logo_path = $image_info_array['src'];
                                    } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'payment/' . $pro_img)) {
                                        $pm_logo_path = DIR_WS_IMAGES . 'payment/' . $pro_img;
                                    }

                                    if (tep_not_null($pm_logo_path))
                                        list($pro_img_w, $pro_img_h) = getimagesize($pm_logo_path);

                                    $pm_html .= "<td width='8%'>" . ($pro_img_w > 0 ? tep_image($pm_logo_path, '', $pro_img_w, $pro_img_h) : '&nbsp;') . "</td>";

                                    unset($aws_obj);

                                    $pm_html .= '			<td class="main" style="padding-left:12px;">' . $payment_title . '</td>
														<td width="10%" style="text-align:right;">' .
                                            //tep_draw_pull_down_menu('cr_' . $radio_buttons, $supported_currencies_array, $_SESSION['currency'], ' disabled onchange="updateScCurrency(' . $radio_buttons . ', \'change\');" id="cr_' . $radio_buttons . '"') .
                                            '</td>
													</tr>';

                                    //if ($pm_info_array['show_billing_address'] == 'True' || $pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_ic'] == 'True') {  // currenct for mobile money only
                                    $pm_html .= '<tr>
														<td width="8%" colspan="2"></td>
														<td colspan="3">
															<div class="' . ($payment == 'pm_' . $unique_name ? '' : 'hide') . ' system_cust_pm_info" id="pm_cust_info_' . $radio_buttons . '">
																<table border="0" width="100%" cellspacing="0" cellpadding="0">';

                                    $display_confirm_link = false;
                                    if ($pm_info_array['show_billing_address'] == 'True') {
                                        $display_confirm_link = true;
                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr>
																		<td colspan="2">' .
                                                tep_address_label($customer_id, $billto, true, ' ', '<br>') . '<br>' .
                                                '<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=scchkout', 'SSL') . '" id="edit_address"><b>' . TEXT_EDIT_ADDRESS . '</b></a>
																		</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_surname'] == 'True' || $pm_info_array['show_city'] == 'True') {
                                        $account_sql = "SELECT customers_telephone, customers_default_address_id, customers_lastname
															FROM " . TABLE_CUSTOMERS . "
															WHERE customers_id ='" . (int) $customer_id . "'";
                                        $account_result_sql = tep_db_query($account_sql);
                                        $account = tep_db_fetch_array($account_result_sql);
                                    }

                                    if ($pm_info_array['show_city'] == 'True' || $pm_info_array['show_street'] == 'True' || $pm_info_array['show_zip'] == 'True') {
                                        $customers_info_select_sql = "	SELECT entry_street_address, entry_postcode, entry_city
																			FROM " . TABLE_ADDRESS_BOOK . "
																			WHERE address_book_id = '" . (int) $account['customers_default_address_id'] . "'
																				AND customers_id ='" . (int) $customer_id . "'";
                                        $customers_info_result_sql = tep_db_query($customers_info_select_sql);
                                        $customers_info_row = tep_db_fetch_array($customers_info_result_sql);
                                    }

                                    if ($pm_info_array['show_contact_number'] == 'True') {
                                        $display_confirm_link = true;
                                        $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                                        if (!isset($_SESSION['customers_telephone'])) {
                                            $_SESSION['customers_telephone'] = $account['customers_telephone'];
                                        }
                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td width="15%">' . ENTRY_PAYMENT_CONTACT_NUMBER . '</td>
																		<td>+' . $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . tep_draw_input_field('telephone_number', tep_mask_telephone($_SESSION['customers_telephone']), ' id="telephone_number" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_ic'] == 'True') {
                                        $display_confirm_link = true;
                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER . '</td>
																		<td>' . tep_draw_input_field('identify_number', (isset($_SESSION['identify_number']) ? $_SESSION['identify_number'] : ''), ' id="identify_number" ') . '<br>' . TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_surname'] == 'True') {
                                        $display_confirm_link = true;
                                        if (!isset($_SESSION['checkout_surname'])) {
                                            $_SESSION['checkout_surname'] = $account['customers_lastname'];
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_SURNAME . '</td>
																		<td>' . tep_draw_input_field('checkout_surname', $_SESSION['checkout_surname'], ' id="checkout_surname" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_housenumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_housenumber'])) {
                                            $_SESSION['checkout_housenumber'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER . '</td>
																		<td>' . tep_draw_input_field('checkout_housenumber', $_SESSION['checkout_housenumber'], ' id="checkout_housenumber" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_street'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_street'])) {
                                            $_SESSION['checkout_street'] = $customers_info_row['entry_street_address'];
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_STREET . '</td>
																		<td>' . tep_draw_input_field('checkout_street', $_SESSION['checkout_street'], ' id="checkout_street" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_city'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_city'])) {
                                            $_SESSION['checkout_city'] = $customers_info_row['entry_city'];
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top">
																		<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_CITY . '</td>
																		<td>' . tep_draw_input_field('checkout_city', $_SESSION['checkout_city'], ' id="checkout_city" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_zip'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_zip'])) {
                                            $_SESSION['checkout_zip'] = $customers_info_row['entry_postcode'];
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ZIP . '</td>
																		<td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_zip'], ' id="checkout_zip" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_accountname'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_accountname'])) {
                                            $_SESSION['checkout_accountname'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME . '</td>
																		<td>' . tep_draw_input_field('checkout_accountname', $_SESSION['checkout_accountname'], ' id="checkout_accountname" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_accountnumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_accountnumber'])) {
                                            $_SESSION['checkout_accountnumber'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER . '</td>
																		<td>' . tep_draw_input_field('checkout_accountnumber', $_SESSION['checkout_accountnumber'], ' id="checkout_accountnumber" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_account_number'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_account_number'])) {
                                            $_SESSION['checkout_account_number'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNT_NUMBER . '</td>
																		<td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_account_number'], ' id="checkout_account_number" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_bankcode'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_bankcode'])) {
                                            $_SESSION['checkout_bankcode'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BANKCODE . '</td>
																		<td>' . tep_draw_input_field('checkout_bankcode', $_SESSION['checkout_bankcode'], ' id="checkout_bankcode" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_branchcode'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_branchcode'])) {
                                            $_SESSION['checkout_branchcode'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BRANCHCODE . '</td>
																		<td>' . tep_draw_input_field('checkout_branchcode', $_SESSION['checkout_branchcode'], ' id="checkout_branchcode" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_directdebittext'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_directdebittext'])) {
                                            $_SESSION['checkout_directdebittext'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT . '</td>
																		<td>' . tep_draw_input_field('checkout_directdebittext', $_SESSION['checkout_directdebittext'], ' id="checkout_directdebittext" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_vouchernumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['show_voucher_number'])) {
                                            $_SESSION['show_voucher_number'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER . '</td>
																		<td>' . tep_draw_input_field('checkout_voucher_number', $_SESSION['show_voucher_number'], ' id="checkout_voucher_number" ') . '</td>
																	</tr>';
                                    }

                                    if ($pm_info_array['show_vouchervalue'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_voucher_value'])) {
                                            $_SESSION['checkout_voucher_value'] = '';
                                        }

                                        $pm_html .= '			<tr>
																		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																	</tr>
																	<tr valign="top" width="15%">
																		<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE . '</td>
																		<td>' . tep_draw_input_field('checkout_voucher_value', $_SESSION['checkout_voucher_value'], ' id="checkout_voucher_value" ') . '</td>
																	</tr>';
                                    }

                                    if ($display_confirm_link) {
                                        $pm_html .= '			<tr>
																		<td colspan="2"><div><a href="javascript:update_pm_cust_info(\'pm_cust_info_' . $radio_buttons . '\')"><b>' . LINK_UPDATE_CUSTOMER_PAYMENT_INFO . '</b></a></div></td>
																	</tr>';
                                    }
                                    $pm_html .= '			</table>
															</div>
														</td>
													</tr>';
                                    //}
                                    $pm_html .= '	<tr>
								    					<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
													</tr>
												</table>
											</div>';
                                    $payment_methods_array[] = array('id' => 'vmenu_pm_' . $pm_id, 'desc' => $pm_html, 'href' => '');
                                    $radio_buttons++;
                                }
                            }

                            if ($actual_pg_show > 0) {
                                $payment_gateway_array[] = array('id' => 'vmenu_pg_' . $gw_id,
                                    'desc' => $payment_cat_display['description'],
                                    'href' => 'javascript:void(0);',
                                    'slist' => $payment_methods_array,
                                    'default_open' => $payment_cat_display['show'],
                                );
                            }
                        }

                        if ($display_flag) {
                            echo $page_obj->get_html_expandable_vmenu_box(sprintf(HEADING_SUBTITLE, $currencies->currencies[DEFAULT_CURRENCY]['title'] . ' (' . $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'] . $currencies->currencies[DEFAULT_CURRENCY]['symbol_right'] . ')'), $payment_gateway_array, 12, '', 12);
                        }
                    }

                    echo '<br><br>';
                    echo '<div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div>';
                    echo '<br>';

                    $pg_footer_html .= '	<tr>
								<td colspan="4">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
							</tr>
							<tr>
								<td colspan="3">
									<div style="float: right;">
										<div id="checkout_button_div" style="width:200px;">' . $cart_html . '</div>
									</div>
								</td>
								<td>&nbsp;</td>
							</tr>
							<tr>
								<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
							</tr>
						</table>
					</div>';
                    echo $pg_footer_html;
					
					if (isset($_SESSION['need_sc_usage_qna']) && $_SESSION['need_sc_usage_qna'] == 'true') {
						if (isset($_SESSION['rp_qna_flag']) && $_SESSION['rp_qna_flag'] == false) {
							//do nth
						} else {
                            ?>
                            <script language="JavaScript">
                                jQuery(document).ready(function() {
                                    checkoutQnA();
        <?php
        if ($messageStack->size('qna') > 0) {
            echo 'jQuery("#div_action_msg").html("<font color=red>' . ENTRY_QNA_MISMATCH_ANSWER_ERROR . '<font>");';
        }
        ?>
                                });
                            </script>
                            <?
						}
						$_SESSION['rp_qna_flag'] = false;
					}
                    ?>
                </div>
            </div>
            <div style="width:14px;float:left;">&nbsp;</div>
        </td>
    </tr>
</table>
<script>
    function updateScCurrency(sel, user_action, currency) {
        var objRef = DOMCall('pm_' + sel);

        currency = currency || '<?= $currency ?>';

        if (typeof(objRef) == 'undefined' || objRef == null) {
            return false;
        }

        if ((objRef.checked != true && user_action == '') || user_action != '') {
            var server_action = 'set_sc_currency';
            var display_html = '';
            var ref_url = 'checkout_xmlhttp.php?action=' + server_action + '&currency=' + currency + '&curr_code=' + currency + '&payment_info=' + objRef.value + '&sel=' + sel;

            selected_pm_sel = sel;
            selected_pm_info = objRef.value;
            selected_currency = '<?= $currency ?>';

            hide_payment_customer_info();

            //disabledFormInputs('payment_methods_form', 'disabled');
            objRef.checked = true;

            var loading_button = '<?= tep_image_button2('red', 'javascript:this.void(0)', TEXT_IS_LOADING, 200) ?>';

            jQuery('#checkout_button_div').html(loading_button);

            if (typeof(curr_code) != 'undefined' && curr_code != null) {
                curr_code.disabled = false;
            }
            blockUI_disable();
            jQuery.ajax({
                url: ref_url,
                type: 'GET',
                dataType: 'xml',
                timeout: 30000,
                error: function() {
                    jQuery.unblockUI();

                },
                success: function(xml) {
                    jQuery("#sel_checkout_currency").val(currency);

                    //jQuery.unblockUI();
                    var error = jQuery(xml).find('error').text();
                    var shopping_cart = jQuery(xml).find('cart_display').text();
                    var surcharge_display = jQuery(xml).find('surcharge_display').text();

                    if (error == '') {
                        jQuery('#cartbox_div').html(shopping_cart);
                        jQuery('#surcharge_' + sel).html(surcharge_display);
                        jQuery('#checkout_button_div').html(jQuery(xml).find('button_html').text());
                        if (jQuery(xml).find('total_surcharge').text() != '' && jQuery(xml).find('total_surcharge').text() != '+') {
                            jQuery('#div_sc_surcharge').show()
                            jQuery('#div_sc_surcharge_balance font').html(jQuery(xml).find('total_surcharge').text());
                        } else {
                            jQuery('#div_sc_surcharge').hide();
                            jQuery('#div_sc_surcharge_balance font').html('');
                        }

                        jQuery('#div_sc_subtotal_balance span.price font').html(jQuery(xml).find('subtotal_amount').text());
                        jQuery('#div_sc_total_balance span.price font').html(jQuery(xml).find('total_amount').text());
                        if (jQuery(xml).find('op').text() != null) {
                            jQuery('#scOP').html(jQuery(xml).find('op').text());
                            jQuery('#scOP').show();
                        } else {
                            jQuery('#scOP').hide();
                        }
                        if (jQuery(xml).find('extra_op').text() != null) {
                            jQuery('#scExtraOp').html(jQuery(xml).find('extra_op').text());
                            jQuery('#scExtraOp').show();
                        } else {
                            jQuery('#scExtraOp').hide();
                        }
                        if (jQuery(xml).find('extra_sc').text() != null) {
                            jQuery('#extraSc').html(jQuery(xml).find('extra_sc').text());
                            jQuery('#extraSc').show();
                        } else {
                            jQuery('#extraSc').hide();
                        }
                    } else {
                        alert('Error! Please Try again.');
                        location.reload(true);
                    }

                    objRef.checked = true;
                    jQuery.unblockUI();
                    jQuery('#pm_cust_info_' + sel).fadeIn('fast');
                    jQuery('#pm_cust_info_' + sel + ' input').attr('disabled', false);
                }
            });
        }

    }

    function popup_cfm_billing_info(act) {
        var action = act != undefined ? act : 'get_billing_content',
                submit_data = '';

        jQuery.ajax({
            url: 'customer_xmlhttp.php?action=' + action,
            data: submit_data,
            type: 'POST',
            dataType: 'xml',
            timeout: 60000,
            beforeSend: function() {
                blockUI_disable();
            },
            error: function() {
                jQuery.unblockUI();
                alert('Please try again.');
            },
            success: function(xml) {
                jQuery.unblockUI();
                if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '') {
                    alert(jQuery(xml).find('error_message').text());
                } else if (jQuery(xml).find('reload').length > 0) {
                    location.reload();
                } else if (jQuery(xml).find('redirect').length > 0) {
                    document.location.href = jQuery(xml).find('redirect').text();
                } else {
                    if (jQuery(xml).find('billing_info_status').text() == 0) {
                        custom_general_popup_box(jQuery(xml).find('content').text(), jQuery(xml).find('header').text(), '550px', {padding: '0px'});
                    } else {
                        document.checkout_confirmation.submit();
                    }
                }
            }
        });
    }

    function cfm_billing() {
        jQuery.ajax({
            type: "POST",
            url: 'customer_xmlhttp.php?action=cfm_billing',
            data: jQuery("#billing_form").serialize(),
            async: false,
            dataType: "xml",
            beforeSend: function() {
                jQuery('#billing_form div.error_msg').html('');
            },
            error: function() {
                alert('Please try again.');
            },
            success: function(xml) {
                if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '') {
                    jQuery('#billing_form div.error_msg').html(jQuery(xml).find('error_message').text());
                } else if (jQuery(xml).find('form_submit').length > 0) {
                    document.checkout_confirmation.submit();
                } else if (jQuery(xml).find('redirect').length > 0) {
                    document.location.href = jQuery(xml).find('redirect').text();
                } else {
                    if (jQuery(xml).find('content').length > 0) {
                        custom_general_popup_box(jQuery(xml).find('content').text(), jQuery(xml).find('header').text(), '530px', {padding: '0px'});
                    }
                }
            }
        });
    }

    function cfm_security_token() {
        jQuery.ajax({
            type: 'GET',
            url: 'customer_xmlhttp.php?action=match_security_tac',
            data: {'answer': jQuery('#enter_token_div input[name=answer]').val()},
            dataType: 'xml',
            beforeSend: function() {
                jQuery('#token_request_icon, #token_request_msg').html('');
            },
            success: function(xml) {
                if (jQuery(xml).find('error').length > 0 && jQuery(xml).find('error').text() != '-') {
                    jQuery("#token_request_icon").html(jQuery(xml).find('error_icon').text());
                    jQuery("#token_request_msg").html(jQuery(xml).find('error').text());
                } else {
                    blockUI_disable();
                    jQuery('div.popup_close_button').click();

                    document.checkout_confirmation.submit();
                }
            }
        });
    }
<? if (tep_not_null($payment)) { ?>
        jQuery(document).ready(function() {
    <? if (isset($selected_payment_flag) && $selected_payment_flag == 2) { ?>
                updateScCurrency('<?= $default_selected ?>', 'change', '<?= DEFAULT_CURRENCY ?>');
    <? } else { ?>
                updateScCurrency('<?= $default_selected ?>', 'change');
    <? } ?>
        });
<? } ?>
</script>