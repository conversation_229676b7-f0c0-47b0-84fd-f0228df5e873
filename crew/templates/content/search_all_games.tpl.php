<div class="vspacing"></div>
<div class=""><!-- middleBox -->
    <!-- Alpphabet Seletor Bar - Start -->
    <div class="tabTop"><h1 style="padding-left:20px;"><?= HEADING_TITLE ?></h1></div>
    <div class="gameBox gameBoxExtend solborder2bs" style="background-color:#E3E3E3;padding:5px 18px;">
        <table id="sag_game_filter_bar" cellpadding="3" cellspacing="0" border="0" width="100%" style="background-color:#e3e3e3;">
            <tr>
                <td class="sagames-name"><span class="hds5"><?= HEADING_TITLE_FILTER ?>:</span><?= $filtering_bar_object_array['search_box']; ?></td>
                <td class="sagames-letter"><?= $filtering_bar_object_array['letter']; ?></td>
                <td class="sagames-lang"><?= $filtering_bar_object_array['language']; ?></td>
                <td class="sagames-producttype"><?= $filtering_bar_object_array['product_type']; ?></td>
                <td class="sagames-platform"><?= $filtering_bar_object_array['platform']; ?></td>
                <td class="sagames-genre"><?= $filtering_bar_object_array['genre']; ?></td>
                <td class="sagames-fav"><?= $filtering_bar_object_array['favorite']; ?></td>
            </tr>
        </table>
    </div>
    <!--div class="middleBoxHeader" style="height:7px; background-position: 0 -590px"></div-->
    <!-- Alpphabet Seletor Bar - End -->

    <!-- Game Listing Box - Start -->
    <?
    $return_result = $search_all_games_obj->generate_html_body_content();

    if (tep_not_null($return_result)) {
        $result_style = 'display:none;';
        $button_style = 'display:block;';

        if ($search_all_games_obj->show_more_remain_chunk == 0) {
            $button_style = 'display:none;';
        }
    } else {
        $result_style = 'display:block;';
        $button_style = 'display:none;';
    }

    echo "	<div id='game_content' class='middleBoxContent'>" . $return_result . "</div>
				<div id='div_no_results' style='padding:10px;text-align:center;" . $result_style . "'>" . MESSAGE_SEARCH_HAS_NO_RESULTS . "</div>";
    unset($search_all_games_obj);
    ?>
    <script>
        jQuery(document).ready(function() {
            jQuery('#fav-star').click(function() {
                var f_fav_val = jQuery('#f_fav').val();
                reset_data();

                if ((f_fav_val == '0') || (f_fav_val == '')) {
                    jQuery('#f_fav').val('1');
                    jQuery('#fav-star').addClass('fav_star_selected');
                } else {
                    jQuery('#f_fav').val('0');
                    jQuery('#fav-star').removeClass('fav_star_selected');
                }
                reload_sag_data();
            });

            jQuery('#sags_box input').keyup(function() {
                var current_keyword = jQuery('#sags_box input').val().replace(/([\\'"])/g, "\\$1");
                setTimeout("search_buffer('" + current_keyword + "')", 1000);
            });

            jQuery('#sags_box span[class~="right_img"]').click(function() {
                if (jQuery(this).is('[class~="clear_img"]')) {
                    jQuery('#sags_box input').val('');
                    jQuery('#sags_box span.right_img').addClass('empty load_img').removeClass('clear_img');
                    reload_sag_data();
                }
            });
        });

        function reset_data() {
            jQuery('#sags_box input').val('');
            jQuery('#sags_box span.right_img').addClass('empty').removeClass('clear_img load_img');
            cdd_trigger_cancel_action('sag', 'f_language', false);
            cdd_trigger_cancel_action('sag', 'f_product_type', false);
            cdd_trigger_cancel_action('sag', 'f_platform', false);
            cdd_trigger_cancel_action('sag', 'f_genre', false);
            cdd_trigger_cancel_action('sag', 'f_letter', false);
            jQuery('#f_fav').val('0');
            jQuery('#fav-star').removeClass('fav_star_selected');
        }

        function reset_and_refresh() {
            reset_data();
            reload_sag_data();
        }

        function search_buffer(buffered_keyword) {
            var keyword = jQuery('#sags_box input').val();

            if (keyword.length >= 2 && buffered_keyword == keyword) {
                jQuery('#sags_box span.right_img').addClass('load_img').removeClass('empty');
                jQuery('#f_fav').val('0');
                jQuery('#fav-star').removeClass('fav_star_selected');
                reload_sag_data();
            } else if (keyword.length == 0 && jQuery('#sags_box span.right_img').is(':not([class~="empty"])')) {
                jQuery('#sags_box span.right_img').addClass('load_img empty');
                jQuery('#f_fav').val('0');
                jQuery('#fav-star').removeClass('fav_star_selected');
                reload_sag_data();
            }
        }

        function reload_sag_data(pg) {
            var st = jQuery('#sags_box input').val();
            var gl = jQuery('#f_language a span.sel span[class~="value"]').html();
            var pt = jQuery('#f_product_type a span.sel span[class~="value"]').html();
            var pf = jQuery('#f_platform a span.sel span[class~="value"]').html();
            var gn = jQuery('#f_genre a span.sel span[class~="value"]').html();
            var fv = jQuery('#f_fav').val();
            var lt = jQuery('#f_letter a span.sel span[class~="value"]').html();
            var pg = pg > 0 ? pg : 1;
            var content = records = '';

            jQuery.ajax({
                type: "post",
                url: 'search_all_games_xmlhttp.php',
                data: {'action': 'get_new_games_list', 'gKeyword': st, 'glanguage': gl, 'platform': pf, 'genre': gn, 'product_type': pt, 'favorite': fv, 'letter': lt, 'page': pg},
                dataType: 'xml',
                beforeSend: function() {
                    pop_out = '<div id="loader" style="padding:10px;text-align:center"><img src="/images/loading.gif" width="20" height="20"></div>';
                    jQuery("#game_content").html(pop_out);
                    jQuery('#div_no_results').hide();
                    jQuery('html, body').animate({scrollTop: 0}, 'slow');
                },
                error: function() {
                    jQuery('#sags_box span.right_img').removeClass('load_img');
                    jQuery('#loader').remove();
                    jQuery('#div_no_results').show();
                },
                success: function(xml) {
                    jQuery('#sags_box span.right_img').removeClass('load_img');
                    jQuery('#sags_box span.right_img:not([class~="empty"])').addClass('clear_img');
                    jQuery('#sags_box span.right_img[class~="empty"]').removeClass('clear_img');
                    jQuery('#sags_box input[value=""]').val(jQuery('#sags_box input').attr('title'));
                    jQuery('#loader').remove();

                    jQuery(xml).find('response').each(function() {
                        content = jQuery("result", this).text();
                        records = jQuery("records", this).text();

                        if (records == '0') {
                            jQuery('#div_no_results').show();
                        } else {
                            jQuery("#game_content").html(content);
                        }
                    });
                }
            });
        }
    </script>
    <!-- Game Listing Box - End -->
</div>