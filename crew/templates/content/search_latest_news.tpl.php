<?
$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key('BUCKET_STATIC');
$aws_obj->set_filepath('images/news/');

if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}

$news_id = (int)$_REQUEST["news_id"];
$tag = (int)$_REQUEST["tag"];

// Release Date Value
// 1 - All Time
// 2 - This Month
// 3 - This Week
// 4 - Today

if (!empty($_REQUEST["released_date"])) {
	$released_date = (int)$_REQUEST["released_date"];
}
else {
	$released_date = "1";
}

if (!empty($_REQUEST["sort_by"])) {
	$sort_by = (int)$_REQUEST["sort_by"];
}
else {
	$sort_by = "1";
}

if ($_REQUEST["news_type"]) {
	$news_type = $_REQUEST["news_type"];
	$news_type_array = explode("_", $news_type);
}

$news_group_array = array();
$categories_sql = "	SELECT latest_news_groups.news_groups_id, latest_news_groups_description.news_groups_name FROM ".TABLE_LATEST_NEWS_GROUPS." LEFT JOIN ".TABLE_LATEST_NEWS_GROUPS_DESCRIPTION." 
					ON latest_news_groups.news_groups_id = latest_news_groups_description.news_groups_id WHERE language_id = '".$_SESSION["languages_id"]."'
					AND latest_news_groups.news_groups_display_status = '1' ORDER BY latest_news_groups.news_groups_sort_order";
$categories_query = tep_db_query($categories_sql);

while ($categories_row = tep_db_fetch_array($categories_query)) {
	$checkedflag = '';
	
	if ($news_type) {
		foreach ($news_type_array as $news_type_row) {
			if ($news_type_row == $categories_row['news_groups_id']) {
				$checkedflag = " checked";
			}
		}
	} else {
		if ($new_news_type)
			$new_news_type .= "_".$categories_row['news_groups_id']; 
		else
			$new_news_type = $categories_row['news_groups_id'];
		 
		$checkedflag = '';
	}
	
	$news_group_array[] = array (	'news_groups_id' => $categories_row['news_groups_id'],
									'news_groups_name' => $categories_row['news_groups_name'],
									'checked' => $checkedflag);	
}

if (empty($_REQUEST["news_type"])) {
	$news_type = $new_news_type;
}

function mySortTags($a, $b)
{
	return (strtoupper($a['tag_name']) < strtoupper($b['tag_name'])) ? -1 : 1;
}

if($_GET['ajax'] == "1") {
	printListing($news_type,$news_id,$cat_id,$languages_id,$default_languages_id,$tag,$released_date,$sort_by);
	exit;
}
else if ($_GET['rss'] == "1") {
	generateRSS($news_type,$languages_id,$default_languages_id);
	exit;
}

?>
<link rel="stylesheet" href="<?=DIR_WS_IMAGES?>css/latest_news.css" type="text/css" />
<center>
<div class="lnBodyTop"><!-- --></div>
<div class="lnBody2Column">
	<div class="lnBodyColumn1">
		<div class="lnBodyColumn1Gray">
			<div style="padding:15px">
				<h2><?=HEADING_BROWSE_NEWS ?></h2>
				<?=TEXT_CHOOSE_FILTER ?>
				<div style="width:210px;" class="line"><!-- --></div>
				<h3><?=HEADING_CATEGORIES ?></h3>
				<div class="breakLine"><!-- --></div>
				<form name="select_news_type">
				<div style="clear:both">
					<ul class="checkbox" style="margin:0 0 0 10px" id="news_type_list">
<?php
	$categories_sql = "	SELECT lng.news_groups_id, lngd.news_groups_name 
						FROM ".TABLE_LATEST_NEWS_GROUPS." as lng
						INNER JOIN ".TABLE_LATEST_NEWS_GROUPS_DESCRIPTION." as lngd 
							ON lng.news_groups_id = lngd.news_groups_id 
						WHERE language_id = '".$_SESSION["languages_id"]."'
							AND lng.news_groups_display_status = '1' 
						ORDER BY lng.news_groups_sort_order";
	$categories_query = tep_db_query($categories_sql);

	foreach ($news_group_array as $news_group_row) {
?>
						<li>
							<div style="float:left"><input onclick="javascript:generateListing('','','')" type="checkbox" name="news_type" value="<?=$news_group_row['news_groups_id'] ?>" id="news_type_<?=$news_group_row['news_groups_id'] ?>"<?=$news_group_row['checked'] ?>></div>
							<div class="lbl" onClick="javascript:tongleCheckBox('news_type_<?=$news_group_row['news_groups_id'] ?>')" style="cursor:pointer"><?=$news_group_row['news_groups_name']; ?></div>
						</li>
<?php
	}
?>
					</ul>
				</div>
				</form>
				<div class="halfBreakLine"><!-- --></div>
				<div style="width:210px;" class="line"><!-- --></div>
				<h3><?=HEADING_RELEASED_DATE ?></h3>
				<div class="breakLine"><!-- --></div>
				<div style="margin:0 0 0 10px;">
					<form>
					<select name="released_date" onChange="javascript:generateListing('','','')" id="released_date">
						<option value="1"<?=($released_date == 1)? ' selected' : '' ?>><?=SELECT_OPTION_DATE1 ?></option>
						<option value="2"<?=($released_date == 2)? ' selected' : '' ?>><?=SELECT_OPTION_DATE2 ?></option>
						<option value="3"<?=($released_date == 3)? ' selected' : '' ?>><?=SELECT_OPTION_DATE3 ?></option>
						<option value="4"<?=($released_date == 4)? ' selected' : '' ?>><?=SELECT_OPTION_DATE4 ?></option>
					</select>
					</form>
				</div>
			</div>
		</div>
		<div style="width:244px;height:1px;background-color:#bdbdbd;clear:both;"><!-- --></div>
		<div style="padding:15px;clear:both;">
			<h2 class="mediumFont"><?=HEADING_TAGS ?></h2>
			<div style="width:210px" class="line"><!-- --></div>
<?php
	$tags_query = "	SELECT c.tag_id, c.tag_name, c.tag_counter
					FROM ".TABLE_LATEST_NEWS_TAG_CATEGORIES." AS c 
					WHERE c.tag_counter > 0
					GROUP BY c.tag_name 
					ORDER BY c.tag_counter DESC";
	$tags_query_result = tep_db_query($tags_query);
	
	$vals = array();
	$tags_rows = array();
	while ($tags_row = tep_db_fetch_array($tags_query_result)) {
		$vals["{$tags_row['tag_counter']}"] = $tags_row['tag_counter'];
		$tags_rows[] = $tags_row; 
	}
	
	$maxFreq = $minFreq = 0;
	if (tep_not_null($vals)) {
		$maxFreq = max($vals);
		$minFreq = min($vals);
	}
	
	$freqSize = $maxFreq - $minFreq;
	$freqSpacing	= $freqSize / 20;
	
	if ($freqSpacing < 1)	$freqSpacing = 1;
	
	foreach ($tags_rows as $tags_row) {
		$tagClass = round($tags_row['tag_counter'] / $freqSpacing);

		$escape_tage_name = str_replace(' ', '-', $tags_row['tag_name']);

		$tags_result[] = array (
			'tag_id' => $tags_row['tag_id'],
			'tag_name' => $tags_row['tag_name'],
			'url' => "javascript:generateListing('".(int) $tags_row['tag_id']."','','".$escape_tage_name."')",
			'cloud' => $tagClass
		);
	}
	
	if (tep_not_null($tags_result)) {
		usort($tags_result, 'mySortTags');
	}
?>

			<div style="clear:both;">
<?php
	if (tep_not_null($tags_result)) {
		foreach ($tags_result as $tags_row) {
			echo '<a href="'.$tags_row['url'].'" class="tag'.$tags_row['cloud'].'">'.$tags_row['tag_name'].'</a>';
		}
	}
?>
			</div>
		</div>
		<div style="width:244px;height:1px;background-color:#bdbdbd;clear:both;"><!-- --></div>
		<div style="padding:15px;clear:both;">
			
		</div>
	
	</div>
	<div class="lnBodyColumn2">
		<div style="padding:20px;float:left;display:none" id="latest_news_body_loading">
			<?=tep_image(DIR_WS_IMAGES . "lightbox-ico-loading.gif"); ?>
		</div>
		<div style="padding:20px;float:left" id="latest_news_body_div">
			<?php
				if ($news_id) {
					printContent($news_type,$news_id,$cat_id,$languages_id,$default_languages_id,$tag,$released_date,$sort_by);
				} else {
					printListing($news_type,$news_id,$cat_id,$languages_id,$default_languages_id,$tag,$released_date,$sort_by);
				}
			?>
		</div>
		<?php
			if ($news_id) {
				echo tep_draw_hidden_field('page_type', 'news', 'id="page_type"');
			} else {
				echo tep_draw_hidden_field('page_type', 'listing', 'id="page_type"');
			}
		?>
	</div>
</div>
<div class="lnBodyBottom"><!-- --></div>
</center>
<?php
echo tep_draw_hidden_field('tag', $tag, 'id="select_tag"');
?>
<script language="JavaScript">
	function generateListing(hyperlinkTag,sortedByField,tagName) {
		var news_type = "";
		var released_date = "";
		var tag;

		jQuery(document.select_news_type.news_type).each(function() {
		    if (this.checked == true) {
		        if (news_type)
					news_type = news_type + '_' + this.value;
				else
					news_type = this.value;
		    }
		});
		
		if (sortedByField == "sort_by1") {
			sort_by = jQuery("#sort_by1").val();
		} else if (sortedByField == "sort_by") {
			sort_by = jQuery("#sort_by").val();
		} else if (jQuery("#sort_by").val()) {
			sort_by = jQuery("#sort_by").val();
		} else {
			sort_by = 1;
		}
		
		released_date = jQuery("#released_date").val();
		
		if (hyperlinkTag == 0) {
			tag = jQuery("#select_tag").val();
		} else {
			jQuery("#select_tag").val(hyperlinkTag);
			tag = hyperlinkTag;
		}
		
		var page_type = jQuery("#page_type").val();
		
		if (page_type == "news" || hyperlinkTag) {
			if (hyperlinkTag)
				document.location.href='/latest-news/tag/'+tag+'/'+tagName+'?news_type='+news_type+'&released_date='+released_date+'&sort_by='+sort_by;
			else
				document.location.href='<?=FILENAME_SEARCH_LATEST_NEWS ?>?news_type='+news_type+'&released_date='+released_date+'&tag='+tag+'&sort_by='+sort_by;
		} else if (page_type == "listing") {
			jQuery("#latest_news_body_div").hide();
			jQuery("#latest_news_body_loading").show();
			jQuery.get("<?=FILENAME_SEARCH_LATEST_NEWS ?>", {"ajax": "1" , "news_type": news_type,"released_date": released_date,"tag":tag,"sort_by":sort_by}, function(html){
				jQuery("#latest_news_body_div").html(jQuery(html));
				jQuery("#latest_news_body_div").show();
				jQuery("#latest_news_body_loading").hide();
			});
		}
	}
	function tongleCheckBox (id) {
		if (jQuery("#"+id).attr('checked') == true) {
			jQuery("#"+id).attr('checked',false);
		} else {
			jQuery("#"+id).attr('checked',true);
		}
		
		generateListing ('','','');
	}
</script>
<?php
// Functions
function printContent($news_type,$news_id,$cat_id,$languages_id,$default_languages_id,$tag,$released_date,$sort_by) {
	global $aws_obj;
	$this_site_id = 0;
	if (defined('SITE_ID')) {
		$this_site_id = SITE_ID;
	}
	
	$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

  	if ($news_id > 0) {
		if ($_GET['preview'] == '1') {
			$status_check_where_clause = "where $news_display_sites_where_str";
		} else {
			$status_check_where_clause = "where ln.status = '1' and $news_display_sites_where_str";
		}
		$page_sql = "select lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. "  as ln inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd on (ln.news_id = lnd .news_id) $status_check_where_clause  
					    and ( if(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
 								  if ((select count(lnd.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
 										where " . $news_display_sites_where_str . " 
 										and lnd.news_id ='".$news_id."' 
 										and lnd.language_id ='". $languages_id. "'
										and lnd.headline <> ''), 
										0,
				 						lnd.language_id = '".$default_languages_id."')
										)
									 ) 
						and lnd.headline <> '' 
						and lnd.news_id ='".$news_id."'"; 

		$page_query = tep_db_query($page_sql);
		$page = tep_db_fetch_array($page_query);
		if(!tep_not_null($page['content'])) {
			$default_news_select_sql = "SELECT content, headline
										FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
										WHERE news_id = '". $page['news_id'] ."'
											AND is_default = 1";
			$default_news_result_sql = tep_db_query($default_news_select_sql);
			if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
				$page['content'] = $default_news_row['content'];
				$page['headline'] = $default_news_row['headline'];
			}

		}
		// Obtain article tags link

		$printtag = "";

		$tag_select_sql = "	SELECT c.tag_name,c.tag_id
							FROM " . TABLE_LATEST_NEWS_TAG_CATEGORIES . " AS c 
							INNER JOIN " . TABLE_LATEST_NEWS_TAG_CONTENT . " AS c2
								ON c.tag_id=c2.tag_id 
							WHERE c2.content_id = '" . (int) $page['news_id'] . "'";
		$tag_result_sql = tep_db_query($tag_select_sql);
		
		while ($tag_row = tep_db_fetch_array($tag_result_sql)) {
			if ($printtag)
				$printtag .= ', <a href="'.FILENAME_SEARCH_LATEST_NEWS.'?tag='.(int) $tag_row['tag_id'].'&news_type='.$news_type.'&released_date='.$released_date.'&sort_by='.$sort_by.'">'.$tag_row['tag_name'].'</a>';
			else
				$printtag = '<a href="'.FILENAME_SEARCH_LATEST_NEWS.'?tag='.(int) $tag_row['tag_id'].'&news_type='.$news_type.'&released_date='.$released_date.'&sort_by='.$sort_by.'">'.$tag_row['tag_name'].'</a>';;
		}
		
		if (empty($printtag)){
			$printtag = TEXT_UNTAGS;
		}

		// Obtain next & previous navigation
		
		$print_tag_name = "";
		$previousArticleURL = "";
		$nextArticleURL = "";
		
		if ($tag) {
			$tag_content_array = array();
	
			$tag_content_select_sql = "	SELECT c.content_id, c2.tag_name
										FROM ".TABLE_LATEST_NEWS_TAG_CONTENT." c,
										".TABLE_LATEST_NEWS_TAG_CATEGORIES." c2  
										WHERE c.tag_id = c2.tag_id
										AND c.tag_id = '".$tag."'
										AND c.content_type = 'ln'";
			$tag_content_result_sql = tep_db_query($tag_content_select_sql);
			while ($tag_content_row = tep_db_fetch_array($tag_content_result_sql)) {
				$tag_content_array[] = $tag_content_row['content_id'];
				$print_tag_name = $tag_content_row['tag_name']; 
			}
		}
	
		if ($released_date) {
			$today_date = date("Y-m-d");
			$last_week_date = date('Y-m-d', strtotime('-1 week'));
			$last_month_date = date('Y-m-d', strtotime('-1 month'));
	
			if ($released_date == "4") {
				$released_date_condition = "ln.date_added >= '".$today_date." 00:00:00' AND ln.date_added <= '".$today_date." 23:59:59'";
			}
			else if ($released_date == "3") {
				$released_date_condition = "ln.date_added >= '".$last_week_date."'";
			}
			else if ($released_date == "2") {
				$released_date_condition = "ln.date_added >= '".$last_month_date."'";
			}
		}

		if ($sort_by == "1"){
			$order_by_statement = "order by date_added DESC";
		}
		else {
			$order_by_statement = "order by date_added ASC";
		}
		
		$exitflag = false;
		$breakflag = false;
		$listing_sql = "select lnd.news_id, lnd.headline, lnd.latest_news_summary, lnd.content, ln.date_added, ln.url, ln.latest_news_url_alias, ln.news_groups_id, lnc.news_groups_id from ". TABLE_LATEST_NEWS. "  as ln 
						inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd 
							on (ln.news_id = lnd .news_id) 
								inner join " . TABLE_LATEST_NEWS_GROUPS . " as lnc 
									on (lnc.news_groups_id = ln.news_groups_id) 
						where ln.status = '1' and lnc.news_groups_display_status = '1' and $news_display_sites_where_str 
						   and lnd.language_id = '". $languages_id. "' && lnd.headline <> ''"
							.(($news_type) ? " AND ln.news_groups_id IN ('".str_replace("_", "','", $news_type)."') " : " ")
							.(($released_date_condition) ? " AND ".$released_date_condition : " ")." "
							.(($tag) ? " AND ln.news_id IN ('".implode("','", $tag_content_array)."') " : " ")."
						    ".$order_by_statement;
		$listing_query = tep_db_query($listing_sql);
		
		while ($listing = tep_db_fetch_array($listing_query)) {
			if(!tep_not_null($listing['content'])) {
				$default_news_select_sql = "SELECT news_id
											FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
											WHERE news_id = '". $listing['news_id'] ."'
												AND is_default = 1";
				$default_news_result_sql = tep_db_query($default_news_select_sql);
				if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
					$listing['news_id'] = $default_news_row['news_id'];
				}

			}
			$previousArticleURL = $currentArticleURL;
			$currentArticleURL = $nextArticleURL;
			$nextArticleURL = "/".$listing["latest_news_url_alias"]."-ln-".$listing["news_id"].".ogm?news_type=".$news_type."&tag=".$tag."&released_date=".$released_date."&sort_by=".$sort_by;
			if ($exitflag) {
				$breakflag = true;
				break;
			}
			else if ($listing['news_id'] == $news_id) {
				$exitflag = true;
			}
		}
		
		if ($breakflag == false) {
			$nextArticleURL = "";
		}

		if ($print_tag_name) {
			echo "<b>".TEXT_TAGS.":</b> ".eval_html($print_tag_name);
		}
?>
			<div class="line" style="width:671px"><!-- --></div>
			<div style="float:left">
				<h1><?=eval_html($page["headline"]) ?></h1>
				<small><?=tep_date_long($page["date_added"]) ?></small>
			</div>
			<div style="clear:both"><!-- --></div>
			<div class="line" style="width:671px"><!-- --></div>
			
<?
		if ($aws_obj->is_aws_s3_enabled()) {
			if ($aws_obj->is_image_exists($page['news_id'] .'_2_'.$_SESSION['languages_id'].'.jpg')) {
				echo tep_image($aws_obj->get_image_url_by_instance());
				echo '<div class="breakLine"><!-- --></div>';
			} 
		} else {
			if (file_exists(DIR_FS_CATALOG. 'images/news/'. $page['news_id'] .'_2_'.$_SESSION['languages_id'].'.jpg')) {
				echo tep_image(DIR_WS_IMAGES . 'news/'.$page['news_id']."_2_".$_SESSION['languages_id'].".jpg"); 
				echo '<div class="breakLine"><!-- --></div>';
			} 
		}
?>
			<div style="float:right;width:70px;margin:10px 0 10px 10px;border:solid 1px #bdbdbd">
				<div style="padding:10px">
					<a href="http://twitter.com/share" class="twitter-share-button" data-count="vertical">Tweet</a><script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
					<div class="breakLine"><!-- --></div>
					<script src="/includes/javascript/fbShare.js" type="text/javascript"></script>
					<a name="fb_share" type="box_count" share_url="<?='http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'] ?>"></a>
					<div class="breakLine"><!-- --></div>
					<script type="text/javascript">
					(function() {
					var s = document.createElement('SCRIPT'), s1 = document.getElementsByTagName('SCRIPT')[0];
					s.type = 'text/javascript';
					s.async = true;
					s.src = 'http://widgets.digg.com/buttons.js';
					s1.parentNode.insertBefore(s, s1);
					})();
					</script>
					<a class="DiggThisButton DiggMedium"></a>
					<div class="breakLine"><!-- --></div>
					<script src="http://www.stumbleupon.com/hostedbadge.php?s=5"></script>
					<div class="breakLine"><!-- --></div>
					<script language="javascript" type="text/javascript"> 
						//Use a customized callback routine. For example:
						//Create a sharelet with button element set to false and the custom handler
						var object = SHARETHIS.addEntry({
								title:'<?=eval_html($page["headline"]) ?>',
								url: '<?='http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'] ?>'
							},
							{button:false, onmouseover: false, offsetLeft: -122, offsetTop: -150, onclick:custom_st_fn}
						);
						//Create customized button and attach it to the share object
						document.write('<span id="share"><a href="javascript:void(0);" class="ln_stbutton_background"><span>Share</span></a></span>'); //class="stbutton"
						var element = document.getElementById("share");
						object.attachButton(element);
						
						function custom_st_fn() {
							jQuery("#stwrapper").mouseout(function(){
								jQuery("div.stclose").trigger('click');
							});
							return true;
						}
					</script>
					<div style="clear:both"><!-- --></div>
				</div>
			</div>
			<p class="mediumFont" id='newsContent'><?=eval_html($page["content"]) ?></p>
			<div class="breakLine"><!-- --></div>
<?php
		if (!empty($printtag)){
?>
			<div style="break:both;margin:5px 0 0 0">
				<div class="ln_tags"><font style="padding:1px 0 0 8px;display:block;font-weight:bold;"><?=TEXT_TAGS ?></font></div>
				<div style="float:left;padding:2px 0 0 7px"><?=eval_html($printtag); ?></div>
			</div>
			<div class="halfBreakLine"><!-- --></div>
<?php
		}
?>
			<div class="dottedLine"><!-- --></div>
			<div style="clear:both;">
				<div style="float:left">
					<div class="lnPreviousIcon"><!-- --></div>
<?php
			if ($previousArticleURL) {
?>
					<div style="float:left;margin:0 0 0 5px;font-weight:bold">
						<a href="<?=$previousArticleURL ?>"><?=TEXT_PREVIOUS ?></a>
					</div>
<?php
			}
			else {
?>
					<div style="float:left;margin:0 0 0 5px;font-weight:bold;color:#AAA">
						<?=TEXT_PREVIOUS ?>
					</div>
<?php
			}
?>
				</div>
				<div style="float:right">
<?php
			if ($nextArticleURL) {
?>
					<div style="float:left;margin:0 5px 0 0;font-weight:bold">
						<a href="<?=$nextArticleURL ?>"><?=TEXT_NEXT ?></a>
					</div>
<?php
			}
			else {
?>
					<div style="float:left;margin:0 5px 0 0;font-weight:bold;color:#AAA">
						<?=TEXT_NEXT ?>
					</div>
<?php
			}
?>
					<div class="lnNextIcon"><!-- --></div>
				</div>
				<div style="clear:both"><!-- --></div>
			</div>
<?php
		echo tep_draw_hidden_field('sort_by', $sort_by , 'id="sort_by"');
  	}
}

function printListing($news_type,$news_id,$cat_id,$languages_id,$default_languages_id,$tag,$released_date,$sort_by) {
	global $aws_obj;
	$this_site_id = 0;
	
	if (defined('SITE_ID')) {
		$this_site_id = SITE_ID;
	}
	
	$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

	$print_tag_name = "";
	if ($tag) {
		$tag_content_array = array();

		$tag_content_select_sql = "	SELECT content_id 
									FROM ".TABLE_LATEST_NEWS_TAG_CONTENT."  
									WHERE tag_id = '".$tag."'
									AND content_type = 'ln'";
		$tag_content_result_sql = tep_db_query($tag_content_select_sql);
		while ($tag_content_row = tep_db_fetch_array($tag_content_result_sql)) {
			$tag_content_array[] = $tag_content_row['content_id']; 
		}

		$tag_content_select_sql = "	SELECT tag_name 
									FROM ".TABLE_LATEST_NEWS_TAG_CATEGORIES."   
									WHERE tag_id = '".$tag."'";
		$tag_content_result_sql = tep_db_query($tag_content_select_sql);
		$tag_content_row = tep_db_fetch_array($tag_content_result_sql);
		
		$print_tag_name = $tag_content_row['tag_name'];
	}
	
	if ($released_date) {
		$today_date = date("Y-m-d");
		$last_week_date = date('Y-m-d', strtotime('-1 week'));
		$last_month_date = date('Y-m-d', strtotime('-1 month'));

		if ($released_date == "4") {
			$released_date_condition = "ln.date_added >= '".$today_date." 00:00:00' AND ln.date_added <= '".$today_date." 23:59:59'";
		}
		else if ($released_date == "3") {
			$released_date_condition = "ln.date_added >= '".$last_week_date."'";
		}
		else if ($released_date == "2") {
			$released_date_condition = "ln.date_added >= '".$last_month_date."'";
		}
	}

	if ($sort_by == "1"){
		$order_by_statement = "order by date_added DESC";
	}
	else {
		$order_by_statement = "order by date_added ASC";
	}

	$listing_sql = "select lnd.news_id, lnd.headline, lnd.content, lnd.latest_news_summary, ln.date_added, ln.url, ln.latest_news_url_alias, lnc.news_groups_id from ". TABLE_LATEST_NEWS. "  as ln 
					inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd 
						on (ln.news_id = lnd .news_id) 
							inner join " . TABLE_LATEST_NEWS_GROUPS . " as lnc 
								on (lnc.news_groups_id = ln.news_groups_id) 
					where ln.status = '1' and lnc.news_groups_display_status = '1' and $news_display_sites_where_str 
					    AND lnd.language_id = '" . $_SESSION['languages_id'] . "' AND lnd.headline <> ''"
						.(($news_type) ? " AND ln.news_groups_id IN ('".str_replace("_", "','", $news_type)."') " : " ")
						.(($released_date_condition) ? " AND ".$released_date_condition : " ")." "
						.(($tag) ? " AND ln.news_id IN ('".implode("','", $tag_content_array)."') " : " ")."
					    ".$order_by_statement;
?>
			<div style="float:left">
				<h2 class="mediumFont"><?=NAVBAR_TITLE ?><font style="font-weight:normal;"><?=($print_tag_name) ? ': '.$print_tag_name : '' ?></font></h2>
			</div>
			<div style="float:right">
				<div style="float:left;padding:3px 3px 0 0;display:block;"><?=TEXT_SUBSCRIBE ?> :</div>
				<div style="float:left;"><a href="search_latest_news.php?rss=1&news_type=<?=$news_type?>" class="ln_rssIcon" style="cursor:pointer">&nbsp;</a></div>
			</div>
			<div style="clear:both"><!-- --></div>
<?php

	if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) {
		$listing_split = new splitPageResults($_REQUEST['page'], MAX_DISPLAY_LATEST_NEWS_PAGE, $listing_sql, $listing_numrows);
		$listing_query = tep_db_query($listing_sql);
	} else {
		$listing_split = new splitPageResults($listing_sql, MAX_DISPLAY_LATEST_NEWS_PAGE);
		$listing_numrows = $listing_split->number_of_rows;
		$listing_query = tep_db_query($listing_split->sql_query);
	}
	if ( ($listing_numrows > 0) && ( (PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3') )) {
?>
			<div class="dottedLine" style="width:671px"><!-- --></div>
			<div style="clear:both">
				<div style="float:left;padding:5px 0">
					<?
					if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) { 
						echo TEXT_RESULT_PAGE . ' ';
	                    $listing_split->display_links($listing_numrows, MAX_DISPLAY_LATEST_NEWS_PAGE, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'ajax')));                       
					} else {
	                    echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y','ajax')));
	                }
	                ?>
	            	<div style="clear:both"><!-- --></div>
				</div>
				<div style="float:right">
					<form>
					<select name="sort_by" onChange="javascript:generateListing('','sort_by','')" id="sort_by">
						<option value="1"<?=($sort_by == 1)? ' selected' : '' ?>><?=SELECT_OPTION_SORT_BY1 ?></option>
						<option value="2"<?=($sort_by == 2)? ' selected' : '' ?>><?=SELECT_OPTION_SORT_BY2 ?></option>
					</select>
					</form>
				</div>
				<div style="clear:both"><!-- --></div>
			</div>
			<div class="dottedLine" style="width:671px"><!-- --></div>
<?php  	
	}

	echo '			<ul class="listing">';
	if ($listing_numrows <= 0) {
		echo "<li style='width:671px'>".TEXT_NO_ARTICLE."</li>";
	}
	while ($listing = tep_db_fetch_array($listing_query)) {
		if(!tep_not_null($listing['content'])) {
			$default_news_select_sql = "SELECT content, headline, latest_news_summary
										FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
										WHERE news_id = '". $listing['news_id'] ."'
											AND is_default = 1";
			$default_news_result_sql = tep_db_query($default_news_select_sql);
			if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
				$listing['content'] = $default_news_row['content'];
				$listing['headline'] = $default_news_row['headline'];
				$listing['latest_news_summary'] = $default_news_row['latest_news_summary'];
			}

		}
		
	  	if(isset($_REQUEST["news_id"]) AND $_REQUEST["news_id"] == $listing["news_id"]) {
			echo eval_html($listing["headline"])." - ".tep_date_long($listing["date_added"]);
		}
		
	    if (preg_match_all("/(?:#DC_)([^#]+)(?:#)?/is", $listing['content'], $sys_token_array)) {
			if (count($sys_token_array[1])) {
				for ($sysCnt=0; $sysCnt < count($sys_token_array[1]); $sysCnt++) {
					$coupon_stat_select_sql = "	SELECT (c.uses_per_coupon - COUNT(crt.unique_id)) AS remaining_uses
												FROM " . TABLE_COUPONS . " AS c 
												LEFT JOIN " . TABLE_COUPON_REDEEM_TRACK . " AS crt 
													ON c.coupon_id=crt.coupon_id 
												WHERE coupon_code = '" . $sys_token_array[1][$sysCnt] . "'
												GROUP BY crt.coupon_id";
					$coupon_stat_result_sql = tep_db_query($coupon_stat_select_sql);
    				
    				if ($coupon_stat_row = tep_db_fetch_array($coupon_stat_result_sql)) {
    					$listing['content'] = str_replace($sys_token_array[0][$sysCnt], $coupon_stat_row['remaining_uses'], $listing['content']);
    				}
				}
			}
		}

		$printtag = '';
		
		$tag_select_sql = "	SELECT c.tag_name,c.tag_id
							FROM " . TABLE_LATEST_NEWS_TAG_CATEGORIES . " AS c 
							," . TABLE_LATEST_NEWS_TAG_CONTENT . " AS c2
							WHERE c.tag_id=c2.tag_id
							AND c2.content_id = '" . (int) $listing['news_id'] . "'";
		$tag_result_sql = tep_db_query($tag_select_sql);
		
		while ($tag_row = tep_db_fetch_array($tag_result_sql)) {
			if ($printtag)
				$printtag .= ', <a href="'.FILENAME_SEARCH_LATEST_NEWS.'?tag='.(int) $tag_row['tag_id'].'&news_type='.$news_type.'&released_date='.$released_date.'&sort_by='.$sort_by.'">'.$tag_row['tag_name'].'</a>';
			else
				$printtag = '<a href="'.FILENAME_SEARCH_LATEST_NEWS.'?tag='.(int) $tag_row['tag_id'].'&news_type='.$news_type.'&released_date='.$released_date.'&sort_by='.$sort_by.'">'.$tag_row['tag_name'].'</a>';;
		}
		
		if (empty($printtag)){
			$printtag = TEXT_UNTAGS;
		}

	  	if ($listing['url']) {
?>
				<li>
					<div class="mediumFont" style="float:left;width:570px">
						<b><a href="<?=$listing["url"] ?>"><?=eval_html($listing["headline"]) ?></a></b><br>
						<small><?=tep_date_long($listing["date_added"]) ?></small>
						<div class="breakLine"><!-- --></div>
						<?php
							$print_summary = nl2br(eval_html($listing['latest_news_summary']));
							if (mb_substr($print_summary, 200, 5)){
								$print_summary = mb_substr($print_summary, 0, 200);
								$print_summary .= "..";
							}
							
							echo $print_summary;
						?>
						<div style="break:both;margin:5px 0 0 0">
							<div class="ln_tags"><font style="padding:1px 0 0 8px;display:block;font-weight:bold;"><?=TEXT_TAGS ?></font></div>
							<div style="float:left;padding:2px 0 0 7px"><?=eval_html($printtag); ?></div>
						</div>
					</div>
<?
			if ($aws_obj->is_aws_s3_enabled()) {
				if ($aws_obj->is_image_exists($listing['news_id'] .'_1_'.$_SESSION['languages_id'].'.jpg')) {
?>
						<div style="float:right;width:100px">
							<a href="<?=$listing["url"] ?>" class="imageBorder"><?=tep_image($aws_obj->get_image_url_by_instance(),'','90','60'); ?></a>
						</div>
<?
				} 
			} else {
				if (file_exists(DIR_FS_CATALOG. 'images/news/'. $listing['news_id'] .'_1_'.$_SESSION['languages_id'].'.jpg')) {
?>
						<div style="float:right;width:100px">
							<a href="<?=$listing["url"] ?>" class="imageBorder"><?=tep_image(DIR_WS_IMAGES . 'latest_news/'.$listing['news_id']."_1_".$_SESSION['languages_id'].".jpg","border='0'",'90','60'); ?></a>
						</div>
<?
				} 
			}
?>
				</li>
<?
		} else {
?>
				<li>
					<div class="mediumFont" style="float:left;width:550px">
						<b><a href="/<?=$listing["latest_news_url_alias"]."-ln-".$listing["news_id"].".ogm?news_type=".$news_type."&tag=".$tag."&released_date=".$released_date.'&sort_by='.$sort_by ?>"><?=eval_html($listing["headline"]) ?></a></b><br>
						<small><?=tep_date_long($listing["date_added"]) ?></small>
						<div class="breakLine"><!-- --></div>
						<?php
							$print_summary = nl2br(eval_html($listing['latest_news_summary']));
							if (mb_substr($print_summary, 200, 5)){
								$print_summary = mb_substr($print_summary, 0, 200);
								$print_summary .= "..";
							}
							
							echo $print_summary;
						?>
						<div style="break:both;margin:5px 0 0 0">
							<div class="ln_tags"><font style="padding:1px 0 0 8px;display:block;font-weight:bold;"><?=TEXT_TAGS ?></font></div>
							<div style="float:left;padding:2px 0 0 7px"><?=eval_html($printtag); ?></div>
						</div>
					</div>
<?
			if ($aws_obj->is_aws_s3_enabled()) {
				if ($aws_obj->is_image_exists($listing['news_id'] .'_1_'.$_SESSION['languages_id'].'.jpg')) {
?>
					<div style="float:right;width:100px">
						<a class="imageBorder" href="/<?=$listing["latest_news_url_alias"]."-ln-".$listing["news_id"].".ogm?news_type=".$news_type."&tag=".$tag."&released_date=".$released_date.'&sort_by='.$sort_by ?>"><?=tep_image($aws_obj->get_image_url_by_instance(),'','90','60'); ?></a>
					</div>
<?
				} 
			} else {
				if (file_exists(DIR_FS_CATALOG. 'images/news/'. $listing['news_id'] .'_1_'.$_SESSION['languages_id'].'.jpg')) {
?>
					<div style="float:right;width:110px;margin:0 0 0 10px;">
						<a class="imageBorder" href="/<?=$listing["latest_news_url_alias"]."-ln-".$listing["news_id"].".ogm?news_type=".$news_type."&tag=".$tag."&released_date=".$released_date.'&sort_by='.$sort_by ?>"><?=tep_image(DIR_WS_IMAGES . 'news/'.$listing['news_id']."_1_".$_SESSION['languages_id'].".jpg","border='0'",'90','60'); ?></a>
					</div>
<?
				} 
			}
?>
				</li>
<?
		}
	}
	echo '			</ul>';
 	
  	if ( ($listing_numrows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
			<div style="clear:both;padding:10px 0 0 0">
				<div style="float:left;padding:5px 0">
					<?
					if (PROJECT_VERSION == "osCommerce 2.2-MS1" OR defined("LATEST_NEWS_MS1")) { 
						echo TEXT_RESULT_PAGE . ' ';
	                    $listing_split->display_links($listing_numrows, MAX_DISPLAY_LATEST_NEWS_PAGE, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y','ajax')));                       
					} else {
	                    echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y','ajax')));
	                }
	                ?>
				</div>
				<div style="float:right">
					<form>
					<select name="sort_by1" onChange="javascript:generateListing('','sort_by1','')" id="sort_by1">
						<option value="1"<?=($sort_by == 1)? ' selected' : '' ?>><?=SELECT_OPTION_SORT_BY1 ?></option>
						<option value="2"<?=($sort_by == 2)? ' selected' : '' ?>><?=SELECT_OPTION_SORT_BY2 ?></option>
					</select>
					</form>
				</div>
				<div style="clear:both"><!-- --></div>
			</div>
			<div class="dottedLine"><!-- --></div>
<?		
	} 
}

function generateRSS($news_type,$languages_id,$default_languages_id) {
	$this_site_id = 0;
	if (defined('SITE_ID')) {
		$this_site_id = SITE_ID;
	}
	
	$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

	header("Content-Type: text/xml; charset=UTF-8"); 

	echo '<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:atom="http://www.w3.org/2005/Atom" version="2.0">
	<channel>
		<title>'.RSS_TITLE.'</title>
        <link>'.tep_href_link(FILENAME_SEARCH_LATEST_NEWS, tep_get_all_get_params(), 'NONSSL').'</link>
		<description>'.RSS_DESCRIPTION.'</description>
        <ttl>60</ttl>
';
	
	$listing_sql = "select lnd.news_id, lnd.headline, lnd.latest_news_summary, ln.date_added, ln.url,ln.latest_news_url_alias,ln.news_groups_id from ". TABLE_LATEST_NEWS. "  as ln 
						inner join " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd on (ln.news_id = lnd .news_id) 
							inner join " . TABLE_LATEST_NEWS_GROUPS . " as lnc on (lnc.news_groups_id = ln.news_groups_id) 
						where ln.status = '1' and lnc.news_groups_display_status = '1' and $news_display_sites_where_str 
                        ".(($news_type) ? " AND ln.news_groups_id IN ('".str_replace("_", "','", $news_type)."') " : " ")."
					    and lnd.language_id = '". $languages_id. "' && lnd.headline <> '' && lnd.content <> ''
						ORDER BY ln.date_added DESC LIMIT 0 , 20";
					
	$listing_query = tep_db_query($listing_sql);
	while ($listing = tep_db_fetch_array($listing_query)) {
		$print_summary = nl2br(eval_html($listing['latest_news_summary']));
		$time = strtotime($listing['date_added']);
		$pubdate = date('D, d M Y H:i:s',$time);

		echo '<item>
				 <title><![CDATA['. $listing["headline"] .']]></title>
				 <link>'.HTTP_SERVER.$listing["latest_news_url_alias"].'-ln-'.$listing["news_id"].'.ogm</link>
                 <guid isPermaLink="false">'.HTTP_SERVER.$listing["latest_news_url_alias"].'-ln-'.$listing["news_id"].'.ogm</guid>
				 <description><![CDATA['.$print_summary.']]></description>
				 <pubDate>'.$pubdate.' +0800</pubDate>
			 </item>
';
	}

	echo '</channel>
</rss>';
}
?>