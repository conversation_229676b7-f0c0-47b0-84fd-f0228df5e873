<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="breakLine"><!-- --></div>


<?php if ($messageStack->size('set_secret_qna') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div style="padding:10px;"><?=$messageStack->output('set_secret_qna'); ?></div>
		<div><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>


<? 
if (tep_not_null($customers_security_obj->get_customer_registered_security_question())) {
	ob_start(); 
?>
	<table border="0" cellpadding="0" cellspacing="10" width="100%;">
 		<tr>
			<td>
				<div style="font-size:16px;font-weight:bold;"><?=HEADER_TITLE_CHANGE_SECRET_QNA?></div>
				<div class="breakLine"><!-- --></div>
							
				<!-- Start Secret Q&A content-->
				<div><?=FORM_REQUIRED_MSG ?></div>
				<div class="breakLine"><!-- --></div>
				<div><?=TEXT_SECRET_QNA_ISSET ?></div>
				<div class="breakLine" style="height:20px;"><!-- --></div>
				<div class="dottedLine"><!-- --></div>
				<div style="height:45px;">
					<div style="float:left;">
						<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
						<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
					</div>
				</div>
				<!-- End Tab content-->
			</td>
		</tr>
	</table>
	
<?
	$set_secret_qna_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$set_secret_qna_content_string,13);
} else { 
	ob_start();
?>
	<table border="0" cellpadding="0" cellspacing="0" width="100%;">
 		<tr>
 			<td width="20px">&nbsp;</td>
			<td>
				<br>
				<div style="font-size:16px; font-weight:bold;"><?=HEADER_TITLE_SECRET_QUESTION?></div>
				<div><?=FORM_REQUIRED_MSG ?></div>
				<div class="breakLine" style="height:20px;"><!-- --></div>
				
				<!-- Start Secret Q&A content-->
				<?=tep_draw_form('set_secret_qna_form', tep_href_link(FILENAME_SET_SECRET_QNA, 'action=set_secret_qna', 'SSL'), 'post', 'onSubmit="return check_form(\'set_secret_qna_form\');" autocomplete="off"') ?>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td width="90px" NOWRAP><?=SET_SECRET_QUESTION ?></td>
							<td width="*%" nowrap>
								<?=tep_draw_pull_down_menu('secretquestion', $customers_security_obj->get_customer_security_questions_list(), '', 'id="secretquestion" onfocus="jQuery(\'#div_secretquestion\').show();" onblur="jQuery(\'#div_secretquestion\').hide();" ').'&nbsp;';?>
								<?=(tep_not_null(SET_SECRET_QUESTION_TEXT) ? '<span class="redIndicator">' . SET_SECRET_QUESTION_TEXT . '</span>': '') ?>
							</td>
							<td width="280px" style="vertical-align:top;">
								<div id="div_secretquestion" style="padding-left: 5px;position:absolute;width:233px;display:none;">
									<div style="float:left;background:url('<?=DIR_WS_ICONS?>icon_note.gif');width:14px;height:14px;margin-right:4px;"></div>
									<div style="float:left;width:200px;"><?=ENTRY_SECRET_ANSWER_QUESTION_NOTICE?></div>
								</div>
							</td>
						</tr>
						
						<tr><td colspan=3><?=tep_draw_separator('pixel_trans.gif', '100%', '5'); ?></td></tr>
						
						<tr height="25px">
							<td><?=SET_ANSWER ?></td>
							<td nowrap>
								<?=tep_draw_input_field('answer', '', 'style="width: 342px" id="answer" class="ezInputField" onfocus="jQuery(\'#div_answer\').show();" onblur="jQuery(\'#div_answer\').hide();" ').'&nbsp;' ?>
								<?=(tep_not_null(SET_ANSWER_TEXT) ? '<span class="redIndicator">' . SET_ANSWER_TEXT . '</span>': '') ?>
							</td>
							<td style="vertical-align:top;">
								<div id="div_answer" style="padding-left: 5px;position:absolute;width:233px;display:none;">
									<div style="float:left;background:url('<?=DIR_WS_ICONS?>icon_note.gif');width:14px;height:14px;margin-right:4px;"></div>
									<div style="float:left;width:200px;"><?=ENTRY_SECRET_ANSWER_NOTICE?></div>
								</div>
							</td>
						</tr>
						
						<tr valign="top" height="30px">
							<td></td>
							<td colspan="2"><span id="messageSecretAnswer" style="display: inline-block; width: 500px; color: #666666;"></span></td>
						</tr>
						
						<tr>
							<td colspan="3" class="dottedLine">&nbsp;</td>
 						</tr>
						<tr height="45px">
							<td>
								<div style="float:left;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
									<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
 								</div>
							</td>
							<td colspan="2">
								<div style="float:right">
									<?=tep_div_button(1, '&nbsp; '. IMAGE_BUTTON_SAVE_PROFILE .' &nbsp; ', 'set_secret_qna', 'style="float:right;padding-bottom: 0.2cm;"', 'green_button', false, 'if(check_form(\'set_secret_qna_form\')){document.set_secret_qna_form.submit()}') ?>
									<!--input type="submit" name="submit" value="submit"-->
								</div>
							</td>
						</tr>
					</table>
				</form>
				<!-- End Tab content-->
			</td>
			<td width="20px">&nbsp;</td>
		</tr>
	</table>
<?php 
	$set_secret_qna_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$set_secret_qna_content_string,13);
} 
?>

<script language="javascript">
<!--
	var form = "";
	var submitted = false;
	var error = false;
	var error_message = "";
	
	function check_input(field_name, field_size, message) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = form.elements[field_name].value;
		
			if (field_value == '' || field_value.length < field_size) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_select(field_name, field_default, message) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = form.elements[field_name].value;
	
			if (field_value == field_default) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_form(form_name) {
		if (submitted == true) {
			alert("<?php echo JS_ERROR_SUBMITTED; ?>");
			return false;
		}
		
		error = false;
		form = eval('document.'+form_name);
		error_message = "<?php echo JS_ERROR; ?>";
		
		check_select("secretquestion", "", "<?php echo ENTRY_SECRET_QUESTION_ERROR; ?>");
		check_input("answer", <?php echo ENTRY_SECRET_ANSWER_MIN_LENGTH ?>, "<?=sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH)?>");
		
		if (error == true) {
			alert(error_message);
			return false;
		} else {
			submitted = true;
			return true;
		}
	}
//-->
</script>