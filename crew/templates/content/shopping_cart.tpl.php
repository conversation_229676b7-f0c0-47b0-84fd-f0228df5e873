
<!--div class="solidThickLine"></div>
<div class="grayLineBox"-->
<?
if ($messageStack->size('add_to_cart') > 0) {
    echo '<div style="padding:5px;">' . $messageStack->output('add_to_cart') . '</div>';
}

$SHOW_IMAGES_IN_SHOPPING_CART = false; // Temporary have this flag to not display products image. Set to true if wish to show

$pm_status = false;
$pm_period = 0;
$normal_total = $special_total = 0;

if ($cart->count_contents() > 0) { //check promotions
    if (tep_session_is_registered('customer_id')) {
        if (STORE_PROMOTION == 'true') {
            $num = $cart->get_products();

            if (sizeof($num) >= 1) {
                //echo "im more then 0 items.";
                $promotions_query = tep_db_query("select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value from promotions where promotions_status = '1' order by promotions_id");
                $promotions = tep_db_fetch_array($promotions_query);
                if ($promotions['promotions_id']) {
                    $pm_date_from = $promotions['promotions_from'];
                    $pm_date_to = $promotions['promotions_to'];
                    $pm_value = $promotions['promotions_min_value'];

                    $pm_todate = split("[/]", date('m/d/Y'));
                    if ($pm_todate[2])
                        $pmtoday = mktime(0, 0, 0, $pm_todate[0], $pm_todate[1], $pm_todate[2]);

                    $pm_dtfrom = split("[/]", tep_date_short($pm_date_from));
                    if ($pm_dtfrom[2])
                        $pmfrom = mktime(0, 0, 0, $pm_dtfrom[0], $pm_dtfrom[1], $pm_dtfrom[2]);

                    $pm_dtto = split("[/]", tep_date_short($pm_date_to));
                    if ($pm_dtto[2]) {
                        $pmto = mktime(0, 0, 0, $pm_dtto[0], $pm_dtto[1], $pm_dtto[2]);
                        //echo "$pmfrom <= $pmtoday && $pmtoday <= $pmto";
                        if ($pmfrom <= $pmtoday && $pmtoday <= $pmto) {
                            //echo "PM start n end date";
                            $pm_status = true; //on the promotions
                            $pm_period = 1;
                        } else {
                            if ($pmtoday >= $pmto && $pmfrom <= $pmtoday) {
                                //echo "PM already end date!";	
                                $pm_status = false; //on the promotions
                                $pm_period = 0;
                            }
                        }
                    }
                }
            }
        }
    }

    echo tep_draw_form('cart_quantity', tep_href_link(FILENAME_SHOPPING_CART, 'action=update_product'));

    $display_cust_price_col = false;

    $cust_select_sql = "SELECT customers_discount, customers_groups_id 
						FROM " . TABLE_CUSTOMERS . " 
						WHERE customers_id = '" . (int) $customer_id . "'";
    $cust_result_sql = tep_db_query($cust_select_sql);
    $cust_row_sql = tep_db_fetch_array($cust_result_sql);

    $cust_group_select_sql = "	SELECT customers_groups_name 
								FROM " . TABLE_CUSTOMERS_GROUPS . " 
								WHERE customers_groups_id = '" . (int) $cust_row_sql['customers_groups_id'] . "'";
    $cust_group_result_sql = tep_db_query($cust_group_select_sql);
    $cust_group_row_sql = tep_db_fetch_array($cust_group_result_sql);

    // Merging store changes: Get the customer group discount
    if (abs($cust_row_sql['customers_discount']) > 0) {
        $display_cust_price_col = true;
    } else {
        reset($cart->contents);
        while (list($products_id, ) = each($cart->contents)) {
            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
            if (abs($customers_groups_discount) > 0) {
                $display_cust_price_col = true;
                break;
            }
        }
    }

    $info_box_contents = array();
    $products = $cart->get_products();

    for ($i = 0; $i < count($products); $i++) { // Push all attributes information in an array
        if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
            while (list($option, $value) = each($products[$i]['attributes'])) {
                echo tep_draw_hidden_field('id[' . $products[$i]['id'] . '][' . $option . ']', $value);
                $attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix
                		                 	from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
                        		            where pa.products_id = '" . $products[$i]['id'] . "'
                        		            	and pa.options_id = '" . $option . "'
                                       			and pa.options_id = popt.products_options_id
                                       			and pa.options_values_id = '" . $value . "'
                                       			and pa.options_values_id = poval.products_options_values_id
                                       			and popt.language_id = poval.language_id
                                       			and poval.products_options_values_name <> ''
                                       			and (IF(popt.language_id='" . tep_db_input($languages_id) . "', 1, popt.language_id='" . tep_db_input($default_languages_id) . "'))
                                       		");
                $attributes_values = tep_db_fetch_array($attributes);

                $products[$i][$option]['products_options_name'] = $attributes_values['products_options_name'];
                $products[$i][$option]['options_values_id'] = $value;
                $products[$i][$option]['products_options_values_name'] = $attributes_values['products_options_values_name'];
                $products[$i][$option]['options_values_price'] = $attributes_values['options_values_price'];
                $products[$i][$option]['price_prefix'] = $attributes_values['price_prefix'];
            }
        }
    }

    $prod_quantity_array = array();
    $custom_prod_index = array();

    $op_rebate = 0;
    for ($i = 0; $i < sizeof($products); $i++) {
        if ((int) $products[$i]['custom_products_type_id'] > 0) {
            if ((int) $products[$i]['custom_products_type_id'] == 4) {
                $unique_pid = $products[$i]['id'] . '_' . $products[$i]['custom_content']['hla_account_id'];
            } else {
                $custom_prod_index[$products[$i]['id']] = (int) $custom_prod_index[$products[$i]['id']] + 1;
                $unique_pid = $products[$i]['id'] . '_' . $custom_prod_index[$products[$i]['id']];
            }
        } else {
            $unique_pid = $products[$i]['id'];
        }
        $cat_name_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = " . $products[$i]['id'] . " and products_is_link=0 order by products_id");
        $cat_name = tep_db_fetch_array($cat_name_query);
        $cat_path = tep_output_generated_category_path_sq($cat_name['categories_id']);

        $prod_bd_query = tep_db_query("select products_bundle, products_bundle_dynamic, products_bundle_dynamic_qty from " . TABLE_PRODUCTS . " where products_id = " . $products[$i]['id']);
        $prod_bd = tep_db_fetch_array($prod_bd_query);
        $prod_bundle = $prod_bd['products_bundle'];
        $prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];

        if ($products[$i]['pm_price'] == 'FREE') {
            $display_free = false;
        }


        if (isset($cart->contents[$products[$i]['id']]['custom']['game_currency'])) {
            $custom_name = 'game_currency';
        } else if (isset($cart->contents[$products[$i]['id']]['custom']['cdkey'])) {
            $custom_name = 'cdkey';
        } else if (isset($cart->contents[$products[$i]['id']]['custom']['store_credit'])) {
            $custom_name = 'store_credit';
        } else if (isset($cart->contents[$products[$i]['id']]['custom']['hla'])) {
            $custom_name = 'hla';
        }

        if ((int) $products[$i]['custom_products_type_id'] < 1) {
            foreach ($cart->contents[$products[$i]['id']]['custom'][$custom_name] as $count => $extra_info_array) {
                if ($custom_name == 'cdkey' || $custom_name == 'store_credit') {
                    $product_qty = $cart->contents[$products[$i]['id']]['qty'];
                } else {
                    $product_qty = $extra_info_array['qty'];
                }

                $products_name = '<b>' . $products[$i]['name'] . '</b>' . ($custom_name == 'store_credit' ? ' (' . tep_get_customer_store_credit_currency($customer_id, false) . ')' : '') . '<br>';

                if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
                    reset($products[$i]['attributes']);
                    while (list($option, $value) = each($products[$i]['attributes'])) {
                        $products_name .= "<i> - " . $products[$i][$option]['products_options_name'] . ' ' . $products[$i][$option]['products_options_values_name'] . "</i><br>";
                    }
                }

                if ($extra_info_array['char_name']) {
                    $products_name .= '' .
                            sprintf(TEXT_CHARACTER_NAME, $extra_info_array['char_name']) .
                            (tep_not_null($extra_info_array['char_account_name']) ? '<br>' . sprintf(TEXT_CHARACTER_ACCOUNT_NAME, $extra_info_array['char_account_name']) : '') .
                            (tep_not_null($extra_info_array['char_account_pwd']) ? '<br>' . sprintf(TEXT_CHARACTER_ACCOUNT_PASSWORD, $extra_info_array['char_account_pwd']) : '') .
                            (tep_not_null($extra_info_array['char_wow_account']) ? '<br>' . sprintf(TEXT_CHARACTER_ACCOUNT_WOW, $extra_info_array['char_wow_account']) : '') . '<br>';
                }

                $info_box_contents[] = array('params' => '');
                $cur_row = sizeof($info_box_contents) - 1;
                $free_item = "\n";

                $display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true);

                $info_box_contents[$cur_row]['op_rebate'] = $currencies->rebate_point;

                if (STOCK_CHECK == 'true') {
                    if ($prod_bundle == 'yes') {
                        $status_info = tep_product_add_to_cart_permission($products[$i]['id'], 'products_bundle');
                    } else if ($prod_bundle_dynamic == 'yes') {
                        $status_info = tep_product_add_to_cart_permission($products[$i]['id'], 'products_bundle_dynamic');
                    } else {
                        $product_instance_id = tep_not_null($extra_info_array['hla_account_id']) ? $extra_info_array['hla_account_id'] : '';
                        $status_info = tep_product_add_to_cart_permission($products[$i]['id'], '', $product_instance_id);
                    }

                    if ($status_info['show'] == 0) {
                        $info_box_contents[$cur_row]['message'] = '	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); color: black;">
	    																<div class="breakLine"></div>
	    																&nbsp;&nbsp;' . tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 12, 12) . '&nbsp;&nbsp;' . INFO_INACTIVE_PRODUCT . '
	    																<div class="breakLine"></div>
	    															</div>';
                    }

                    // Products Checkout Quantity Control
                    if (tep_not_null($products[$i]['checkout_qty_exceed']) && ($products[$i]['checkout_qty_exceed'] == 1)) {
                        $info_box_contents[$cur_row]['message'] = '	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); color: black;">
	    																<div class="breakLine"></div>
	    																<table border="0" cellpadding="0" cellspacing="0">
	    																<tr>
	    																	<td>&nbsp;&nbsp;' . tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 12, 12) . '&nbsp;&nbsp;</td>
	    																	<td>' . (tep_not_null($products[$i]['checkout_qty_orders']) ? ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS : ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED) . '</td>
	    																</tr>';

                        if (tep_not_null($products[$i]['checkout_qty_orders'])) {
                            $info_box_contents[$cur_row]['message'] .= '	<tr>
		    																	<td>&nbsp;</td>
		    																	<td>';
                            foreach ($products[$i]['checkout_qty_orders'] as $num => $checkout_qty_orders) {
                                $info_box_contents[$cur_row]['message'] .= '<a href="' . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $checkout_qty_orders) . '">' . $checkout_qty_orders . '</a><br />';
                            }
                            $info_box_contents[$cur_row]['message'] .= '		</td>
		    																</tr>';
                        }

                        $info_box_contents[$cur_row]['message'] .= '     </table>
	    																<div class="breakLine"></div>
	    															</div>';
                    }
                }

                if ($products[$i]['pm_price'] == 'FREE') {
                    if ($display_free) {
                        $free_item = "* Get 1 free\n";
                    }
                }

                $info_box_contents[$cur_row]['product_name'] = $products_name . $free_item . $cat_path;
                if ($prod_bundle_dynamic == 'yes') {
                    $info_box_contents[$cur_row]['qty'] = $products[$i]['quantity'] . tep_draw_hidden_field('cart_quantity[]', $products[$i]['quantity']) . tep_draw_hidden_field('products_id[]', $unique_pid);
                } else {
                    $info_box_contents[$cur_row]['qty'] = tep_draw_input_field('cart_quantity[' . $unique_pid . '_' . ($count + 1) . ']', $product_qty, 'size="4" class="qtyInput" onKeyPress="return disableenter(this,event)"') . tep_draw_hidden_field('products_id[]', $unique_pid . '_' . ($count + 1));
                }

                $normal_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty);
                $special_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']);

                //CGDiscountSpecials start
                if ($display_cust_price_col) {
                    /*
                      $info_box_contents[$cur_row][] = array(	'align' => 'center',
                      'params' => 'class="shoppingCartRecords" valign="top" width="19%"',
                      'text' => $currencies->format($products[$i]['normal_price']));
                     */
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE' && !$count) {
                        $prod_qty = ($product_qty > 1) ? $product_qty - 1 : $product_qty;
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']) . '</b>';
                    }
                } else {
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE' && !$count) {
                        if ($display_free) {
                            $final_qty = $product_qty - 1;
                        } else {
                            $final_qty = $product_qty;
                        }

                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $final_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']) . '</b>';
                    }
                }

                $info_box_contents[$cur_row]['delete_icon'] = tep_image_submit('icon-close.gif', IMAGE_BUTTON_DELETE, 'name="DeleteBtn_' . $unique_pid . '_' . ($count + 1) . '"', '', '', '', '/images/');
            }
        } else {
            if ((int) $products[$i]['custom_products_type_id'] == '2') {
                $product_qty = $products[$i]['quantity'];
                $products_name = '<b>' . $products[$i]['name'] . '</b><br>';

                if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
                    reset($products[$i]['attributes']);
                    while (list($option, $value) = each($products[$i]['attributes'])) {
                        $products_name .= "<i> - " . $products[$i][$option]['products_options_name'] . ' ' . $products[$i][$option]['products_options_values_name'] . "</i><br>";
                    }
                }

                if ($products[$i]['custom_content'] && isset($products[$i]['custom_content']['delivery_mode'])) {
                    if ($products[$i]['custom_content']['delivery_mode'] == 6) {
                        include_once(DIR_WS_CLASSES . 'direct_topup.php');
                        $direct_topup_obj = new direct_topup();

                        if ($prod_bundle == 'yes' || $prod_bundle_dynamic == 'yes') {
                            $bundle_select_sql = "	SELECT pp.products_id
													FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
													INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
														ON pdi.products_id = pp.products_id 
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
														ON pp.products_id=pb.subproduct_id
													WHERE pb.bundle_id = '" . tep_get_prid($products[$i]['id']) . "'
														AND pdi.products_delivery_mode_id = '6'
													LIMIT 1";
                            $bundle_result_sql = tep_db_query($bundle_select_sql);
                            $bundle_row = tep_db_fetch_array($bundle_result_sql);
                            $game_input_array = $direct_topup_obj->get_game_input($bundle_row['products_id']);
                        } else {
                            $game_input_array = $direct_topup_obj->get_game_input($products[$i]['id']);
                        }

                        foreach ($products[$i]['custom_content'] as $top_up_key_loop => $top_up_data_loop) {
                            switch ($top_up_key_loop) {
                                case 'account':
                                case 'server':
                                    $products_name .= $game_input_array[$top_up_key_loop]['top_up_info_display'] . ': ';
                                    if ($top_up_key_loop == 'server') {
                                        if (!isset($direct_topup_servers_array[$products[$i]['id']])) {
                                            $direct_topup_servers_array[$products[$i]['id']]['servers'] = $direct_topup_obj->get_servers($products[$i]['id']);
                                        }
                                        $products_name .= ( isset($direct_topup_servers_array[$products[$i]['id']]['servers'][$top_up_data_loop]) ? $direct_topup_servers_array[$products[$i]['id']]['servers'][$top_up_data_loop] : '-' );
                                    } else {
                                        $products_name .= $top_up_data_loop;
                                    }
                                    $products_name .= '<BR>';
                                    break;
                                case 'character':
                                    if (stristr($top_up_data_loop, '##')) {
                                        $character_tmp = explode("##", $top_up_data_loop);
                                        $top_up_data_loop = $character_tmp[1];
                                    }
                                    $products_name .= $game_input_array[$top_up_key_loop]['top_up_info_display'] . ': ' . $top_up_data_loop;
                                    $products_name .= '<BR>';
                                    break;
                            }
                        }
                    }

                    if (isset($products[$i]['custom_content']['delivery_mode'])) {
                        switch ($products[$i]['custom_content']['delivery_mode']) {
                            case '5':
                                $products_name .= TEXT_INFO_DELIVERY_MODE . ': ' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . '<BR>';
                                break;
                            case '6':
                                $products_name .= TEXT_INFO_DELIVERY_MODE . ': ' . TEXT_INFO_DELIVERY_DIRECT_TOP_UP . '<BR>';
                                break;
                            case '7':
                                $products_name .= TEXT_INFO_DELIVERY_MODE . ': ' . TEXT_INFO_DELIVERY_IN_STORE_PICKUP . '<BR>';
                                break;
                        }
                    }
                }

                $info_box_contents[] = array('params' => '');
                $cur_row = sizeof($info_box_contents) - 1;
                $free_item = "\n";

                $display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true);

                $info_box_contents[$cur_row]['op_rebate'] = $currencies->rebate_point;
                $info_box_contents[$cur_row]['product_name'] = $products_name;

                $info_box_contents[$cur_row]['qty'] = tep_draw_input_field('cart_quantity[' . $unique_pid . ']', $product_qty, 'size="4" class="qtyInput" onKeyPress="return disableenter(this,event)"') . tep_draw_hidden_field('products_id[]', $unique_pid);

                // Products Checkout Quantity Control
                if (tep_not_null($products[$i]['checkout_qty_exceed']) && ($products[$i]['checkout_qty_exceed'] == 1)) {
                    $info_box_contents[$cur_row]['message'] = '	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); color: black;">
    																<div class="breakLine"></div>
    																<table border="0" cellpadding="0" cellspacing="0">
    																<tr>
    																	<td>&nbsp;&nbsp;' . tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 12, 12) . '&nbsp;&nbsp;</td>
    																	<td>' . (tep_not_null($products[$i]['checkout_qty_orders']) ? ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS : ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED) . '</td>
    																</tr>';

                    if (tep_not_null($products[$i]['checkout_qty_orders'])) {
                        $info_box_contents[$cur_row]['message'] .= '	<tr>
	    																	<td>&nbsp;</td>
	    																	<td>';
                        foreach ($products[$i]['checkout_qty_orders'] as $num => $checkout_qty_orders) {
                            $info_box_contents[$cur_row]['message'] .= '<a href="' . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $checkout_qty_orders) . '">' . $checkout_qty_orders . '</a><br />';
                        }
                        $info_box_contents[$cur_row]['message'] .= '		</td>
	    																</tr>';
                    }

                    $info_box_contents[$cur_row]['message'] .= '     </table>
    																<div class="breakLine"></div>
    															</div>';
                }

                $normal_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty);
                $special_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']);

                //CGDiscountSpecials start
                if ($display_cust_price_col) {
                    /*
                      $info_box_contents[$cur_row][] = array(	'align' => 'center',
                      'params' => 'class="shoppingCartRecords" valign="top" width="19%"',
                      'text' => $currencies->format($products[$i]['normal_price']));
                     */
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE' && !$count) {
                        $prod_qty = ($product_qty > 1) ? $product_qty - 1 : $product_qty;
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']) . '</b>';
                    }
                } else {
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE' && !$count) {
                        if ($display_free) {
                            $final_qty = $product_qty - 1;
                        } else {
                            $final_qty = $product_qty;
                        }

                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $final_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']) . '</b>';
                    }
                }

                $info_box_contents[$cur_row]['delete_icon'] = tep_image_submit('icon-close.gif', IMAGE_BUTTON_DELETE, 'name="DeleteBtn_' . $unique_pid . '"', '', '', '', '/images/');
            } else {
                $product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';
                $currencies->product_instance_id = $product_instance_id;

                if ($products[$i]['custom_products_type_id'] == '3') {
                    $products_name = $products[$i]['name'] . ' (' . tep_get_customer_store_credit_currency($customer_id, false) . ')';
                } else if ($products[$i]['custom_products_type_id'] == '4') {
                    $products_name = '<b>' . $products[$i]['name'] . '</b>';

                    $hla_char_sql = "	SELECT phd.products_hla_characters_name 
										FROM " . TABLE_PRODUCTS_HLA . " ph 
										LEFT JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS phc 
											ON phc.products_hla_id = ph.products_hla_id 
										LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS phd 
											ON phd.products_hla_characters_id = phc.products_hla_characters_id 
												AND (IF(phd.language_id = '" . tep_db_input($languages_id) . "', 1, phd.language_id = '" . tep_db_input($default_languages_id) . "')) 
										WHERE ph.products_hla_id = '" . (int) $product_instance_id . "' 
											AND ph.products_id = '" . (int) $products[$i]['id'] . "' 
											AND phd.products_hla_characters_name <> ''";
                    $hla_char_result = tep_db_query($hla_char_sql);
                    while ($hla_char_row = tep_db_fetch_array($hla_char_result)) {
                        $products_name .= '<br />&raquo;&nbsp;' . $hla_char_row['products_hla_characters_name'];
                    }

                    $products_name .= '<br /><br />';
                    $products_name .= $cat_path;
                } else {
                    $products_name = '<a href="' . tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id=' . $products[$i]['id'] . '&index=' . $custom_prod_index[$products[$i]['id']]) . '" CLASS="productNavigation">' . $products[$i]['name'] . '</a>';
                }

                if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
                    reset($products[$i]['attributes']);
                    while (list($option, $value) = each($products[$i]['attributes'])) {
                        $products_name .= '<i> - ' . $products[$i][$option]['products_options_name'] . ' ' . $products[$i][$option]['products_options_values_name'] . '</i><br>';
                    }
                }

                $info_box_contents[] = array('params' => '');
                $cur_row = sizeof($info_box_contents) - 1;

                $display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], true);
                $info_box_contents[$cur_row]['op_rebate'] = $currencies->rebate_point;

                if ($products[$i]['pm_price'] == 'FREE') {
                    $free_item = '* Get 1 free';
                    $info_box_contents[$cur_row]['product_name'] = $products_name . $free_item;
                } else {
                    $info_box_contents[$cur_row]['product_name'] = $products_name;
                }

                if ((int) $products[$i]['custom_products_type_id'] == 1) {
                    $info_box_contents[$cur_row]['qty'] = $products[$i]['quantity'] . tep_draw_hidden_field('products_id[]', $unique_pid);
                } else {
                    if (STOCK_CHECK == 'true') {
                        if ($prod_bundle == 'yes') {
                            $status_info = tep_product_add_to_cart_permission($products[$i]['id'], 'products_bundle');
                        } else if ($prod_bundle_dynamic == 'yes') {
                            $status_info = tep_product_add_to_cart_permission($products[$i]['id'], 'products_bundle_dynamic');
                        } else {
                            $status_info = tep_product_add_to_cart_permission($products[$i]['id'], '', $product_instance_id);
                        }

                        if ($status_info['show'] == 0) {
                            $info_box_contents[$cur_row]['message'] = '	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); color: black;">
		    																<div class="breakLine"></div>
		    																&nbsp;&nbsp;' . tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 12, 12) . '&nbsp;&nbsp;' . INFO_INACTIVE_PRODUCT . '
		    																<div class="breakLine"></div>
		    															</div>';
                        }

                        // Products Checkout Quantity Control
                        if (tep_not_null($products[$i]['checkout_qty_exceed']) && ($products[$i]['checkout_qty_exceed'] == 1)) {
                            $info_box_contents[$cur_row]['message'] = '	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); color: black;">
		    																<div class="breakLine"></div>
		    																<table border="0" cellpadding="0" cellspacing="0">
		    																<tr>
		    																	<td>&nbsp;&nbsp;' . tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 12, 12) . '&nbsp;&nbsp;</td>
		    																	<td>' . (tep_not_null($products[$i]['checkout_qty_orders']) ? ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS : ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED) . '</td>
		    																</tr>';

                            if (tep_not_null($products[$i]['checkout_qty_orders'])) {
                                $info_box_contents[$cur_row]['message'] .= '	<tr>
			    																	<td>&nbsp;</td>
			    																	<td>';
                                foreach ($products[$i]['checkout_qty_orders'] as $num => $checkout_qty_orders) {
                                    $info_box_contents[$cur_row]['message'] .= '<a href="' . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $checkout_qty_orders) . '">' . $checkout_qty_orders . '</a><br />';
                                }
                                $info_box_contents[$cur_row]['message'] .= '		</td>
			    																</tr>';
                            }

                            $info_box_contents[$cur_row]['message'] .= '     </table>
		    																<div class="breakLine"></div>
		    															</div>';
                        }
                    }

                    if ((int) $products[$i]['custom_products_type_id'] == 4) { // HLA
                        $info_box_contents[$cur_row]['qty'] = $products[$i]['quantity'] . tep_draw_hidden_field('products_id[]', $unique_pid);
                    } else {
                        $info_box_contents[$cur_row]['qty'] = tep_draw_input_field('cart_quantity[' . $products[$i]['id'] . ']', $products[$i]['quantity'], 'size="4" class="qtyInput" onKeyPress="return disableenter(this,event)"') . tep_draw_hidden_field('products_id[]', $products[$i]['id']);
                    }
                }

                $normal_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity']);
                $special_total += $currencies->display_noformat_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], $products[$i]['discounts']);

                //CGDiscountSpecials start
                if ($display_cust_price_col) {
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE') {
                        if ($products[$i]['quantity'] > 1) {
                            $prod_qty = $products[$i]['quantity'] - 1;
                        }
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], $products[$i]['discounts']) . '</b>';
                    }
                } else {
                    $info_box_contents[$cur_row]['unit_price'] = $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), 1, $products[$i]['discounts']);

                    if ($products[$i]['pm_price'] == 'FREE') {
                        if ($products[$i]['quantity'] > 1) {
                            $prod_qty = $products[$i]['quantity'] - 1;
                        }
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']) . '</b>';
                    } else {
                        $info_box_contents[$cur_row]['amount'] = '<b>' . $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], $products[$i]['discounts']) . '</b>';
                    }
                }

                if ((int) $products[$i]['custom_products_type_id'] == 1 || (int) $products[$i]['custom_products_type_id'] == 4) {
                    $info_box_contents[$cur_row]['delete_icon'] = tep_image_submit('icon-close.gif', IMAGE_BUTTON_DELETE, 'name="DeleteBtn_' . $unique_pid . '"', '', '', '', '/images/');
                } else {
                    $info_box_contents[$cur_row]['delete_icon'] = tep_image_submit('icon-close.gif', IMAGE_BUTTON_DELETE, 'name="DeleteBtn_' . $products[$i]['id'] . '"', '', '', '', '/images/');
                }
            }
        }
    }

    ob_start();
    ?>

    <div style="padding:0px;">
        <div class="h1_tbl">
            <div style="width:428px">
                <span class="hdsU4"><?= TABLE_HEADING_PRODUCTS ?></span>
            </div>
            <div style="width:113px;">
                <span class="hdsU4"><?= TABLE_HEADING_UNIT_PRICE ?></span>
            </div>
            <div style="width:93px;">
                <span class="hdsU4"><?= TABLE_HEADING_QUANTITY ?></span>
            </div>
            <div style="width:144px;">
                <span class="hdsU4"><?= TABLE_HEADING_POINTS_EARNED ?></span>
            </div>
            <div>
                <span class="hdsU4"><?= TABLE_HEADING_AMOUNT ?></span>
            </div>
        </div>
        <?
        $print_first_entry_flag = true;

        foreach ($info_box_contents as $info_box_contents_row) {
//			if ($print_first_entry_flag == false) {
//				echo '		<div class="dottedLine"><!-- --></div>';
//			} else {
//				$print_first_entry_flag = false;
//			}

            $op_rebate += $info_box_contents_row['op_rebate'];
            ?>
            <div class="hc1_tbl">
                <div class="first" style="width:428px">
                    <font><?= $info_box_contents_row['product_name'] ?></font>
                </div>
                <div style="width:113px;">
                    <font style="text-align:right;"><?= $info_box_contents_row['unit_price'] ?></font>
                </div>
                <div style="width:93px;">
                    <font style="text-align:center;"><?= $info_box_contents_row['qty'] ?></font>
                </div>
                <div style="width:144px;">
                    <?php echo tep_display_op($info_box_contents_row['op_rebate']); ?>
                </div>
                <div style="width:100px;">
                    <font style="text-align:right;"><?= $info_box_contents_row['amount'] ?></font>
                </div>
                <div class="last">
                    <div style="padding:4px 0px"><?= $info_box_contents_row['delete_icon'] ?></div>
                </div>
            </div>
            <? if (isset($info_box_contents_row['message'])) { ?>
                <div>
                    <div class="divTableCell" style="width:13px">&nbsp;<!-- --></div>
                    <div class="divTableCell" style="width:911px;">
                        <span class="markProductOutOfStock"><?= $info_box_contents_row['message'] ?></span>
                    </div>
                </div>
                <div class="halfBreakLine"><!-- --></div>
                <?
            }
        }
        ?>
        <div class="hc1_tbl">
            <?
            if (isset($_SESSION['cart_update_error'])) {
                echo '			<div class="first" style="padding-bottom: 15px;width: 100%;">' . $_SESSION['cart_update_error'] . '</div>';

                unset($_SESSION['cart_update_error']);
            }

            foreach ($navigation->path as $nav_path) {
                if (isset($nav_path['get']['cPath'])) {
                    $continue_shopping_page = $nav_path['page'];
                    $nav_mode = $nav_path['mode'];
                    break;
                } else {
                    $continue_shopping_page = FILENAME_DEFAULT;
                    $nav_mode = 'NONSSL';
                }
            }

            $gst_popup = '/popup/gst.html';
            if ($_SESSION['languages_id'] == 2 || $_SESSION['languages_id'] == 3) {
                $gst_popup = '/popup/gst_cn.html';
            }
            ?>

            <div class="first">
                <?= tep_image_button2('gray_short', tep_href_link($continue_shopping_page, tep_array_to_string($nav_path['get'], array('action')), $nav_mode), IMAGE_BUTTON_CONTINUE_SHOPPING) ?>
                <?= tep_submit_button(IMAGE_BUTTON_UPDATE_CART, '', 'name="QtyUpdateBtn" style="display:none;"', '', false) ?>
                <?= tep_image_button2('gray_short', 'javascript:document.cart_quantity.QtyUpdateBtn.click();', IMAGE_BUTTON_UPDATE_CART) ?>
            </div>
            <div class="last" style="width:250px;">
                <?
                echo tep_image_button2('gray_box', 'javascript:void(0);', '<span class="left" style="width:80px;">' . TEXT_TOTAL . '</span><span class="price"><b><font class="largeFont">' . $currencies->format($cart->show_total(), false) . '</font></b></span>', 250);
                if (tep_not_null($_SESSION['RegionGST']['tax_title'])) {
                    echo tep_image_button2('gray_box', 'javascript:void(0);', '<a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</a>', 250);
                }
                $wTip = tep_display_op('<font class="largeFont">' . $op_rebate . '</font>');
                echo tep_image_button2('gray_box', 'javascript:void(0);', '<span class="left" style="width:80px;">' . ENTRY_REBATE . ':</span><span class="op">' . $wTip . '</span>', 250);
                echo tep_image_button2('red', tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'), IMAGE_BUTTON_CHECKOUT, 250)
                ?>
            </div>
        </div>
        <div style="clear:both"></div>
        <div style="padding: 5px"><?= SHOPPING_CART_PRICE_DISCLAIMER ?></div>
    </div>
    </form>
    <script type="text/javascript">
        jQuery(function() {
            jQuery(".wTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "left", keepAlive: true});
            jQuery("body").click(function() {
                jQuery('#tiptip_holder').hide();
            });
        });
    </script>  
    <?
    $content_string = ob_get_contents();
    ob_end_clean();
} else {
    if (isset($_SESSION['trace'])) {
        unset($_SESSION['trace']);
    }

    ob_start();
    ?>
    <div style="padding:15px;">
        <font class="largeFont"><?= TEXT_CART_EMPTY ?></font>
    </div>
    <div class="hc1_tbl">
        <div class="last">
            <?= tep_image_button2('gray_short', tep_href_link(FILENAME_DEFAULT, '', 'NONSSL'), IMAGE_BUTTON_CONTINUE) ?>
        </div>
    </div>
    <div style="clear:both"></div>
    <?
    $content_string = ob_get_contents();
    ob_end_clean();
}
echo '<div class="vspacing"></div>';
echo '<div>';
echo $page_obj->get_html_simple_rc_box(HEADING_TITLE, $content_string, 12);
echo '</div>';
?>