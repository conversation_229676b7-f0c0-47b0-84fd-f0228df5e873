<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="solidThickLine"></div>
<div class="breakLine"><!-- --></div>

<?php if ($messageStack->size('offline_payment_info') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div>
			<div class="breakLine"><!-- --></div>
			<div><?=$messageStack->output('offline_payment_info'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine" style="line-height:10px;"><!-- --></div>
<?php } ?>

<div class="loginColumnBorder" style="width:100%;">
	<table border="0" width="100%" cellspacing="10" cellpadding="5">
  		<tr>
  			<td>
			<?=tep_draw_form('offline_payment_info_form', tep_href_link(FILENAME_RECEIVE_PAYMENT_INFO, 'order_id='.$order_id, 'SSL'), 'post', '') ?>
			<?=tep_draw_hidden_field('action', 'process')?>
	    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE ?></div>
	    		<div class="breakLine" style="height:20px;"><!-- --></div>
	    		
				<table border="0" width="100%" cellspacing="0" cellpadding="5">
	          	<?php //if ($payment_confirm_complete_days > 0) {	?>
	          		<tr>
	          			<td style="width:20%;"><?=TITLE_ORDER_NUMBER ?></td>
	          			<td><?=$order_id ?></td>
	          		</tr>
	          		<tr>
	          			<td style="width:20%;"><?=TITLE_PAYMENT_METHOD ?></td>
	          			<td><?=$pm_obj->payment_methods_description_title ?></td>
	          		</tr>

	          		<tr>
	          			<td style="width:20%;vertical-align:top;"><?=TITLE_PAYMENT_INFO ?>&nbsp;</td>
						<td><?=(tep_not_null($offline_payment_info_row) ? nl2br($offline_payment_info_row['authorisation_result']) : tep_draw_textarea_field('offline_payment_info', 'soft', '78', '8', '', 'id="offline_payment_info"') ) ?>&nbsp;</td>
	          		</tr>

					<tr><td colspan="2" class="dottedLine">&nbsp;</td></tr>
					<tr>
						<td colspan="2">
							<div style="display: block; margin-top:10px;">
								<div style="float:left; width:50px; display: inline-block;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
									<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
								</div>
								<?=(tep_not_null($offline_payment_info_row)) ? '' : tep_div_button(1, '&nbsp; &nbsp; &nbsp; '. BUTTON_SUBMIT .' &nbsp; &nbsp; &nbsp;', 'offline_payment_info_form', 'style="float:right;"', 'green_button', false, 'if (check_form(\'offline_payment_info_form\')) {document.offline_payment_info_form.submit();}') ?>
							</div>
						</td>
					</tr>
				<?php	/*} else {	?>
					<tr><td><?=MESSAGE_NRP_USING?></td></tr>
				<?php	}*/	?>					
	  			</table>
			</form>
			</td>
		</tr>
	</table>
</div>


<script language="javascript">
function check_form(frm_obj) {
	if (document.getElementById('offline_payment_info') != null) {
		var payment_info = document.getElementById('offline_payment_info');
		var check = false;
		
		if (payment_info.value.length > 0 && payment_info.value != '' && payment_info.value != 0) {
			check = true;
		}
		else {
			alert('<?=JS_ERROR_MESSAGE_1 ?>');
			payment_info.focus();
		}
		
		return check;
	}
}
</script>