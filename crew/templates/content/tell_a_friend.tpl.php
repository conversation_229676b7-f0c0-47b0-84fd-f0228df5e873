<?=tep_draw_form('email_friend', tep_href_link(FILENAME_TELL_A_FRIEND, 'action=process&products_id=' . $HTTP_GET_VARS['products_id']))?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?	if ($messageStack->size('friend') > 0) { ?>
		<tr>
			<td><?=$messageStack->output('friend')?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?	} ?>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main"><b><?=FORM_TITLE_CUSTOMER_DETAILS?></b></td>
									<td class="requiredInfo" align="right"><?php echo FORM_REQUIRED_INFORMATION; ?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
								<tr class="infoBoxContents">
									<td>
										<table border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main"><?php echo FORM_FIELD_CUSTOMER_NAME; ?></td>
												<td class="main"><?php echo tep_draw_input_field('from_name'); ?></td>
											</tr>
											<tr>
												<td class="main"><?php echo FORM_FIELD_CUSTOMER_EMAIL; ?></td>
												<td class="main"><?php echo tep_draw_input_field('from_email_address'); ?></td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr>
						<td class="main"><b><?php echo FORM_TITLE_FRIEND_DETAILS; ?></b></td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
								<tr class="infoBoxContents">
									<td>
										<table border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main"><?php echo FORM_FIELD_FRIEND_NAME; ?></td>
												<td class="main"><?php echo tep_draw_input_field('to_name') . '&nbsp;<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>'; ?></td>
											</tr>
											<tr>
												<td class="main"><?php echo FORM_FIELD_FRIEND_EMAIL; ?></td>
												<td class="main"><?php echo tep_draw_input_field('to_email_address') . '&nbsp;<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>'; ?></td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr>
						<td class="main"><b><?=FORM_TITLE_FRIEND_MESSAGE?></b></td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
								<tr class="infoBoxContents">
									<td><?=tep_draw_textarea_field('message', 'soft', 40, 8)?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
					<tr class="buttonBoxContents">
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
									<td><?=tep_div_button(2, IMAGE_BUTTON_BACK, 'javascript:history.back(-1);','','gray_button')?></td>
									<td align="right"><?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'email_friend','style="float:right;"','gray_button')?></td>
									<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</form>
		
