<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"></div>

<?php if ($messageStack->size('verification_submission') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div style="padding:10px;"><?=$messageStack->output('verification_submission'); ?></div>
		<div class="breakLine"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>
<? 
	ob_start();
	echo tep_draw_form('upload_form', tep_href_link(FILENAME_VERIFICATION_SUBMISSION_FORM, 'action=upload', 'SSL'), 'post', 'enctype="multipart/form-data"');
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="0" class="newInputBox">
		
	    <tr bgcolor="#bdbdbd" height="23px">
			<td class="tableTitle" valign="middle">
				<a name="submit"></a>&nbsp;&nbsp;
				<?=tep_image(DIR_WS_IMAGES.'icon_header_arrow.gif', '', '6', '10', 'border="0"').' '.FORM_TAB_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_image(DIR_WS_IMAGES.'pixel_trans.gif', '', '100%', '13', 'border="0"')?></td>
		</tr>
		<tr>
			<td class="message" nowrap>&nbsp;<?=FORM_TAB_SUB_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_image(DIR_WS_IMAGES.'pixel_trans.gif', '', '100%', '13', 'border="0"')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_image(DIR_WS_IMAGES.'pixel_trans.gif', '', '100%', '15', 'border="0"')?></td>
		</tr>
		<tr>
			<td>
				<table width="100%" cellspacing="5" cellpadding="2" border="0">
<?
	if (!$upload_purchase_authorization_permission) {
?>
				<tr>
					<td class="inputLabelNew" align="left" valign="top"><?=ENTRY_PURCHASE_AUTHORIZATION_FORM?></td>
				</tr>
				<tr>
					<td>
						<?=tep_draw_input_field('doc_1', '', 'size="42"', 'file')?>
						<br><span><?=REMARK_UPLOAD_SIZE_LIMIT?></span>
					</td>									
				</tr>
<?
	}
	if (!$upload_utility_bill_permission) {
?>
				<tr>
					<td class="inputLabelNew" align="left" valign="top"><?=ENTRY_UTILITY_BILL?></td>
				</tr>
				<tr>
					<td>
						<?=tep_draw_input_field('doc_2', '', 'size="42"', 'file')?>
						<br><span><?=REMARK_UPLOAD_SIZE_LIMIT?></span>
					</td>									
				</tr>
<?
	}
	if (!$upload_photo_identification_permission) {
?>
				<tr>
					<td class="inputLabelNew" align="left" valign="top"><?=ENTRY_PHOTO_IDENTIFICATION?></td>
				</tr>
				<tr>
					<td>
						<?=tep_draw_input_field('doc_3', '', 'size="42"', 'file')?>
						<br><span><?=REMARK_UPLOAD_SIZE_LIMIT?></span>
					</td>									
				</tr>
<?
	}
	if (!$upload_credit_card_front_permission) {
?>
				<tr>
					<td class="inputLabelNew" align="left" valign="top"><?=ENTRY_CREDIT_CARD_FRONT?></td>
				</tr>
				<tr>
					<td>
						<?=tep_draw_input_field('doc_4', '', 'size="42"', 'file')?>
						<br><span><?=REMARK_UPLOAD_SIZE_LIMIT?></span>
					</td>									
				</tr>
<?
	}
	if (!$upload_credit_card_back_permission) {
?>
				<tr>
					<td class="inputLabelNew" align="left" valign="top"><?=ENTRY_CREDIT_CARD_BACK?></td>
				</tr>
				<tr>
					<td>
						<?=tep_draw_input_field('doc_5', '', 'size="42"', 'file')?>
						<br><span><?=REMARK_UPLOAD_SIZE_LIMIT?></span>
					</td>									
				</tr>
<?
	}
?>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_image(DIR_WS_IMAGES.'pixel_trans.gif', '', '100%', '20', 'border="0"')?></td>
		</tr>
	    <tr><td style="background-color:#FFF"><?=tep_image(DIR_WS_IMAGES.'pixel_trans.gif', '', '100%', '10', 'border="0"')?></td></tr>
<?
	if (!$upload_purchase_authorization_permission || !$upload_utility_bill_permission || !$upload_photo_identification_permission || !$upload_credit_card_front_permission || !$upload_credit_card_back_permission) {
?>
	    <tr>
			<td style="background-color:#FFF">
				<div style="height:50px; display: block;">
					<div style="float:left; width:50px; display: inline-block;">
						<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
						<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
					</div>
					<?=tep_div_button(1, '&nbsp; '. BUTTON_SUBMIT .' &nbsp; ', 'upload_form', 'style="float:right;"', 'green_button', false, '') ?>
				</div>
			</td>
		</tr>
<?
	}
?>
	</table>
	</form>
<?
	$verification_submission_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$verification_submission_content_string,13); 
?>