<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>vip_xmlhttp.js"></script>
<style>
	.vipInventoryTitle {
		font-size: 11px;
		font-weight: bold;
		padding:8px;
	}
</style>
<?
if ($messageStack->size($content) > 0) {
	echo '	<table cellspacing="2" cellpadding="0">
			  	<tr><td>' . $messageStack->output($content) . '</td></tr>
			  	<tr>
					<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>
			</table>';
}
?>
			<div><h1><?=NAVBAR_TITLE?></h1></div>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
    	<td colspan="10">
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
    			<tr><td class="infoBoxTitle"><?=HEADING_TITLE?></td></tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
                			<tr>
                				<td>
                					<?=tep_draw_form('load_server', FILENAME_MY_VIP_INVENTORY_UPDATE)?>
                					<table border="0" cellpadding="2" cellspacing="0" width="100%">
									<td width="10%" class="vipInventoryTitle" nowrap><?=TEXT_SELECT_GAME?></b></p></td>
									<td width="90%"><?=tep_draw_pull_down_menu('cID', $active_game_array, $cID, 'onChange="this.form.submit();"')?></td>
									</table>
									</form>
                				</td>
                			</tr>
                			<tr><td><div class="row_separator"></div></td></tr>
<?	if ($cID) { ?>
		<tr>
			<td>
				<table border="0" cellpadding="2" cellspacing="0" width="100%">
					<tr>
						<td width="10%" class="vipInventoryTitle" nowrap><?=TEXT_WORKING_DAY?></td>
						<td width="90%" align="left">
						<?
							$workday_text_array = array();
							for ($work_cnt=0; $work_cnt < count($workingdays_array); $work_cnt++) {
								$work_day = $workingdays_array[$work_cnt];
								
								$workday_text_array[] = (defined('TEXT_DAY_'.$work_day) ? constant('TEXT_DAY_'.$work_day) : $work_day);
							}
							
							echo implode(', ', $workday_text_array);
						?>
						</td>
						
					</tr>
					<tr>
						<td width="10%" class="vipInventoryTitle" nowrap><?=TEXT_WORKING_TIME?></td>
						<td width="90%" align="left">
						<?					
							echo implode(' - ', $workingtime_array);
						?>
						</td>
						
					</tr>
				</table>
			</td>
		</tr>
<?	} ?>
                		</table>
                	</td>
                </tr>
            </table>
        </td>
    </tr>

	<tr>
		<td width="3"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
<?	if ($cID) { 
		echo tep_draw_form('vip_inventory_form', tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, tep_get_all_get_params(array('action'))."action=batch_action"), 'post', 'enctype="multipart/form-data"');
		echo tep_draw_hidden_field('cID', $cID);
		echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array));
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox" style="padding-left:0px;padding-right:0px;">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
            				<tr>
								<td><p style="text-align:left;"><?=NOTE_SELECT_SERVER?></td>
							</tr>
                			<tr>
                				<td>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td class="boxHeaderLeftTable">&nbsp;</td>
											<td class="boxHeaderCenterTable">
											<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr>
								    				<th width="1%" align="left"><?=tep_draw_checkbox_field('select_all', '', false, 'id="select_all" onclick="javascript:void(setCheckboxes(\'vip_inventory_form\',\'select_all\',\'server_batch\'));"')?></th>
								    				<th width="50%" align="left"><?=TABLE_HEADER_SERVER_NAME?></td>
								    				<th width="20%" align="center"><?=TABLE_HEADER_SERVER_QUANTITY?></th>
								    				<th width="20%" align="center"><?=TABLE_HEADER_SERVER_MIN_QTY_TO_SELL?></th>
								    				<th width="10%"><?=TABLE_HEADER_SERVER_DELETE?></th>
								    			</tr>
								    		</table>
								    		</td>
								    		<td class="boxHeaderRightTable">&nbsp;</td>
								    		<td align="right" width="1">&nbsp;</td>
										</tr>
									</table>
                				</td>
                			</tr>
		            		<tr>
			    				<td>
			    					<table border="0" cellpadding="0" cellspacing="0" width="100%">
			    						<tr>
			    							<td width="1">&nbsp;</td>
			    							<td width="7">&nbsp;</td>
						    				<td>
						    					<div style=" width:100%; height:100%;">
							    					<table border="0" width="100%" height="50px" cellspacing="0" cellpadding="0">
							    						<tbody>
							    							<?=$server_html?>
							    							<tr><td colspan="7"><div class="row_separator"></div></td></tr>
							    							<tr class="ordersListing">
							    								<td colspan="7">
							    									<table border="0" width="100%" cellspacing="0" cellpadding="0">
							    										<tr height="35">
										    								<td width="1" align="center">&nbsp;</td>
										    								<td width="1%" align="center">&nbsp;</td>
										    								<td colspan="5"><div style="font-weight:bold;font-size:12px;"><?=TEXT_BATCH_UPDATE?></div></td>
										    							</tr>
										    							<tr height="25">
																			<td width="1" align="center">&nbsp;</td>
																			<td width="1%" align="center">&nbsp;</td>
																			<td width="50%" align="left" style="padding:10px;">
																				<div><?=TEXT_SELECT_ALL_SERVER?></div>
																			</td>
																			<td width="20%" align="center"><?=tep_draw_input_field("txtBatchInvQty", '', 'id="txtBatchInvQty" size="10" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
																			<td width="20%" align="center"><?=tep_draw_input_field("txtBatchMinQty", '', 'id="txtBatchMinQty" size="10" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
																			<td width="##DYNAMIC_WIDTH##" align="center">&nbsp;</td>
																			<td width="1" align="center">&nbsp;</td>
																		</tr>
																		<tr>
																			<td align="right" colspan="7">
																				<?=tep_div_button(2, BUTTON_BATCH_UPDATE,'javascript:apply_batch(\'txtBatchInvQty\', \'txtBatchMinQty\', \'chkBatchDelete\')', 'style="float:right;"', 'gray_button') ?>
																			</td>
																		</tr>
							    									</table>
							    								</td>
							    							</tr>
							    						</tbody>
							    					</table>
						    					</div>
						    				</td>
						    				<td width="1">&nbsp;</td>
								    		<td width="5">&nbsp;</td>
										</tr>
									</table>
			    				</td>
							</tr>
							<tr><td height="25"><div class="breakLineImg"></div></td></tr>
							<tr>
								<td colspan="7" class="ordersBoxHeading" valign="top">
									<table border="0" width="100%" height="50" cellspacing="0" cellpadding="15" valign="top">
										<tr>
											<td>
											<table border="0" width="100%" height="50" cellspacing="0" cellpadding="0" valign="top">
											<tr valign="top">
												<td align="left" style="padding-right:5px;" width="150">
													<?=tep_draw_input_field('csv_import', '', 'size="20"', 'file')?>
												</td>
												<td align="left" style="padding-right:5px;" width="80">
													<?=tep_draw_hidden_field('btn_csv_type', '', 'id="btn_csv_type"')?>
													<?=tep_div_button(2, BUTTON_IMPORT,'javascript:document.vip_inventory_form.btn_csv_type.value =\'import\';document.vip_inventory_form.submit();', '', 'gray_button') ?>
												</td>
												<td align="left">
													<?=(isset($_POST['btn_csv_type']) && ($_POST['btn_csv_type'] == "import")) ? '' : tep_div_button(2, BUTTON_EXPORT,'javascript:document.vip_inventory_form.btn_csv_type.value =\'export\';document.vip_inventory_form.submit();', '', 'gray_button') ?>
												</td>
							    			</tr>
							    			</table>
							    			</td>
							    			<td align="right" width="100">
						    					<?=tep_div_button(1, BUTTON_SUBMIT,'vip_inventory_form', 'style="float:right;"', 'gray_button') ?>
						    				</td>
							    		</tr>
						    		</table>
						    	</td>
			    			</tr>
                		</table>
                	</td>
                </tr>
            </table>
			</form>
		</td>
	</tr>
	

<?	} ?>
</table>
    	
<script language="javascript">
<!--
	function showHideBox(box_id, toggle_id) {
	    var box_obj = DOMCall(box_id);
	    var toggle_obj = DOMCall(toggle_id);
	    
	    if (box_obj != null) {
	    	if (box_obj.className == 'hide') {
		        box_obj.className = 'show';
		        toggle_obj.src = '<?=DIR_WS_ICONS . 'icon_collapse.gif'?>';
		    } else {
		        box_obj.className = 'hide';
		        toggle_obj.src = '<?=DIR_WS_ICONS . 'icon_expand.gif'?>';
		    }
	    }
	}
	
	function apply_batch (batch_inv_qty, batch_min_qty, batch_del) {
		var inv_qty = jQuery("#"+batch_inv_qty).val();
		var min_qty = jQuery("#"+batch_min_qty).val();
		var is_delete = jQuery("#"+batch_del).attr('checked');
		
		var checkbox_selected = 0;
		jQuery(".server_batch").each(function () {
			if(jQuery(this).attr('checked') == true) {
				jQuery("#inv_qty_"+this.value).val(inv_qty);
				jQuery("#min_qty_"+this.value).val(min_qty);
				//jQuery(".delete_batch").attr('checked', is_delete);
				//alert(this.value);
				checkbox_selected += 1;
			};
		});
		
		if (checkbox_selected == 0) {
			alert('<?=TEXT_PLS_SELECT_CHECKBOX?>');
		}
	}
	
	function delete_server (pid) {
		var status	= confirm('<?=TEXT_CONFIRM_DELETE?>');
		
		if(status == true) {
			vip_del_sup_inv(pid);
		}
	}
//-->
</script>