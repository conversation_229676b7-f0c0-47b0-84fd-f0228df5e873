<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>ajaxfileupload.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>jquery.tooltip.packed.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>vip_xmlhttp.js?20101217"></script>
<?
if ($messageStack->size($content) > 0) {
	echo '	<table cellspacing="2" cellpadding="0">
			  	<tr><td>' . $messageStack->output($content) . '</td></tr>
			  	<tr>
					<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>
			</table>';
}
?>
<script type="text/javascript">
jQuery(document).ready(function() {
  // add BeautyTips functionality
  jQuery('[title]#order_status_tips').bt({width: '380'});
  get_captcha_img(".captcha_img");
});
</script>

<script type="text/javascript">
<!--
	var divhtml = '';
	var countdown_secs_default = 60;
	var countdown_secs = 0;
	var coundown_active = false;
	var lockdown_form_secs = 2;
	var languages_id = '<?=$languages_id?>';
	var history_timerID = 0;
	var global_loading_message = '<?=TEXT_LOADING_MESSAGE?>';
	var global_no_result_found = '<?=TEXT_NO_RECORD_FOUND?>';
	
	jQuery(document).ready(function(){
		onVipAwaitingOrderRefresh();
	});
	
	function upload_ss (req_grp_id) {
		var display_html = '';
		
		display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action", "startdate")), "POST", "enctype=\"multipart/form-data\"")?>';
		display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
		display_html += '<table border="0" width="500" style="font-weight:normal;font-size:12px;">';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SS_REF_TITLE?></td><td><?=TEXT_SS_REF_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
		display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
		display_html += '<tr><td align="right" style="text-align:right;"><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_1", "", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", "file")?></td></tr>';
		display_html += '<tr><td align="right" style="text-align:right;"><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_2", "", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", "file")?></td></tr>';
		display_html += "</table>";
		display_html += "</form>";
		
		jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
		
		jQuery('#jconfirm_submit').click(function() { 
			ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'processing', display_html);
			jquery_confirm_box(display_html+'<table><tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
		});
	}
	
	function ajaxFileUpload (req_grp_id, file_id_1, file_id_2, loading_id, bb_status, display_html)
	{
		
		jQuery.ajaxFileUpload
		(
			{
				url:'<?=tep_href_link("vip_xmlhttp.php")?>?action=redirect_upload_to_curl&req_grp_id='+req_grp_id+'&file_name_1='+file_id_1+'&file_name_2='+file_id_2+'&sup_language=<?=$sup_language?>',
				secureuri:false,
				fileClass:'upload_class',
				dataType: 'xml',
				success: function(xml){
			     	jQuery(xml).find('response').each(function(){
						var validation_result = jQuery("validation_result", this).text();
						var error_msg_3 = jQuery("error_msg_3", this).text();
						if (validation_result != 'done' && error_msg_3 == '') {
							var error_msg_1 = jQuery("error_msg_1", this).text();
							var error_msg_2 = jQuery("error_msg_2", this).text();
							
							var error_html = '';
							var display_with_error_html = '';
							
							error_html += "<table border='0' width='100%' style='display:block;color:red;font-size:12px;'>";
							error_html += '<tr><td style="font-weight:bold;"><?=TEXT_ERROR_MSG?>:</td></tr>';
							
							if (error_msg_1 != '') {
								error_msg_1 = get_error_msg(error_msg_1);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>]</td><td>'+error_msg_1+'</td></tr>';	
							}
							if (error_msg_2 != '') {
								error_msg_2 = get_error_msg(error_msg_2);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>]</td><td>'+error_msg_2+'</td></tr>';	
							}
							
							if (error_msg_1 == '' && error_msg_2 == '') {
								var error_msg = '<?=ERROR_UPLOAD_FAILED?>';
								error_html += '<tr><td>&nbsp;</td><td>'+error_msg+'</td></tr>';	
							}
							
							error_html += "</table>";
							
							display_with_error_html = error_html + display_html; 
							jquery_confirm_box(display_with_error_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
							
							// OK button
							jQuery('#jconfirm_submit').click(function() { 
								ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', bb_status, display_html);
								jquery_confirm_box(display_html+'<table><tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
							});
							
							// Cancel button
							jQuery('#jconfirm_cancel').click(function() { 
								jQuery.unblockUI();	
								
								if (bb_status == 'verifying') {
									search_vip_order_history();
								}
								
							});
						} else {
							jQuery.unblockUI();
							search_vip_order_history();
						}
			      });
			   }
			}
		)
		return false;
	}
	
	function get_error_msg (msg) {
		var result = '';
		switch (msg) {
			case '1':
			case '2':
			case '10':
				result = '<?=ERROR_FILESIZE_EXCEED?>';
			break;
			case '3':
				result = '<?=ERROR_UPLOAD_PARTIAL?>';
			break;
			case '4':
				result = '<?=ERROR_NO_UPLOAD_FILE?>';
			break;
			case '6':
				result = '<?=ERROR_NO_TMP_DIR?>';
			break;
			case '8':
				result = '<?=ERROR_NO_UPLOAD_FILE?>';
			break;
			case '9':
				result = '<?=ERROR_FILETYPE_NOT_ALLOWED?>';
			break;
			default:
				result = '<?=ERROR_DESTINATION_NOT_WRITEABLE?>';
			break;
		}
		return result;
	}
	
	function check_file_ext(path){
		var ext = path.split(".");
		var file_ext = ext[ext.length-1].toLowerCase();
		if(file_ext == "jpg" || file_ext == "jpeg"){
			return true;
		} else {
			return false;
		}
	}
	
	function checking_file(file_id){
		if (typeof(file_id) == 'undefined' || file_id == null) {	
	  		return;
	  	} else {
			if (!check_file_ext(file_id.value)){
				alert ("<?=JS_INVALID_SS_FILE_EXT?>\n");
				file_id.value = '';
			}
		}
	}
	
	function validate_confirmed_qty(req_id, req_grp_id) {
		jQuery("#confirm_"+req_grp_id).attr("onClick", '');
		
		stop_coundown_activity();
		var customer_id = '<?=$customer_id?>';

		var sent_qty = parseInt(document.getElementById('sent_qty_'+req_grp_id).value);
		var req_qty = parseInt(document.getElementById('request_qty_'+req_grp_id).value);
		var req_id = parseInt(req_id);
		
		jQuery.post("<?=tep_href_link('vip_xmlhttp.php', 'action=update_buyback_request_group')?>",{
	    req_grp_id: req_grp_id, sent_qty: sent_qty, req_id: req_id, customer_id: customer_id
	 	},function(xml){
		     jQuery(xml).find('response').each(function(){
		        var error_msg = jQuery("error_msg", this).text();
		        var result = jQuery("result", this).text();
		        
		        if (result == 'updated') {
		        	
<?
		        	if (BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
?>
			        	var display_html = '';
						display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action", "startdate")), "POST", "enctype=\'multipart/form-data\'")?>';
						display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
						display_html += '<table border="0" width="100%" style="font-size:12px;">';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_SS_REF_TITLE?></td><td><?=TEXT_SS_REF_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
						display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
						display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_1", "", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", "file")?></td></tr>';
						display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_input_field("ss_image_2", "", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", "file")?></td></tr>';
						display_html += "</table>";
						display_html += "</form>";
						
						jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
						
						jQuery('#jconfirm_submit').click(function() { 
							ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'verifying', display_html);
							jquery_confirm_box(display_html+'<table><tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
						});
						
						jQuery('#jconfirm_cancel').click(function() { 
							search_vip_order_history();
						});
<?
		        	} else {
?>
						search_vip_order_history();
<?
		        	}
?>
		        } else {
		        	jQuery("#confirm_"+req_grp_id).attr("onClick", 'return validate_confirmed_qty(\''+req_id+'\', \''+req_grp_id+'\');');
		        	alert(error_msg);
		        }
		   	 });
	  	});
	}
	
	function validate_request_qty(opId, max_qty) {
		stop_coundown_activity();
		var error = false;
		var errormsg = '';
		var submit_qty = parseInt(document.getElementById('submit_quantity_'+opId).value);
		var char_id = trim_str(document.getElementById('vip_character_name_'+opId).value);
		if(char_id == ''){
			error = true;
			errormsg += "<?=ERROR_NO_TRADE_CHARACTER?>\n";
		}
		
		if (submit_qty < 0 || isNaN(submit_qty)){
			error = true;
			errormsg += "<?=ERROR_INVALID_QTY?>";
		} 
		
		if(error){
			alert(errormsg);
			return false;
		} else {
			return true;
		}
	}

	function add_search_results_row(res_xml_obj, is_last_row) {
		if (typeof(res_xml_obj) != 'object') return;
		
		var req_grp_id = res_xml_obj.getElementsByTagName('req_grp_id')[0];
	  	if (typeof(req_grp_id) == 'undefined' || req_grp_id == null) {	
	  		return;
	  	} else {
	  		req_grp_id = req_grp_id.firstChild.nodeValue;
	  	}
	  	
	  	var req_id = res_xml_obj.getElementsByTagName('req_id')[0];
	  	if (typeof(req_id) == 'undefined' || req_id == null) {
			return;	  	
	  	} else {
	  		req_id = req_id.firstChild.nodeValue;
	  	}
	  	
	  	var game_name = res_xml_obj.getElementsByTagName('game_name')[0];
	  	if (typeof(game_name) == 'undefined' || game_name == null) {
			game_name = '';
		} else {
			game_name = game_name.firstChild.nodeValue;
		}
		
		var server_name = res_xml_obj.getElementsByTagName('server_name')[0];
		if (typeof(server_name) == 'undefined' || server_name == null) {
			server_name = '';
		} else {
			server_name = server_name.firstChild.nodeValue;
		}
		
		var req_qty = res_xml_obj.getElementsByTagName('req_qty')[0];
		if (typeof(req_qty) == 'undefined' || req_qty == null) {
			req_qty = '';
		} else {
			req_qty = req_qty.firstChild.nodeValue;
		}
		
		var confirmed_qty = res_xml_obj.getElementsByTagName('confirmed_qty')[0];
		if (typeof(confirmed_qty) == 'undefined' || confirmed_qty == null) {
			confirmed_qty = '';
		} else {
			confirmed_qty = trim_str(confirmed_qty.firstChild.nodeValue);
		}
		
		var amount = res_xml_obj.getElementsByTagName('amount')[0];
		if (typeof(amount) == 'undefined' || amount == null) {
			amount = '';
		} else {
			amount = amount.firstChild.nodeValue;
		}
		
		var show_restock = res_xml_obj.getElementsByTagName('show_restock')[0];
		if (typeof(show_restock) == 'undefined' || show_restock == null) {
			show_restock = '';
		} else {
			show_restock = trim_str(show_restock.firstChild.nodeValue);
		}
		
		var restock_character = res_xml_obj.getElementsByTagName('restock_character')[0];
		if (typeof(restock_character) == 'undefined' || restock_character == null) {
			restock_character = '';
		} else {
			restock_character = trim_str(restock_character.firstChild.nodeValue);
		}
		
		var status_name = res_xml_obj.getElementsByTagName('status_name')[0];
		if (typeof(status_name) == 'undefined' || status_name == null) {
			status_name = '';
		} else {
			status_name = status_name.firstChild.nodeValue;
		}
		
		var current_status_id = res_xml_obj.getElementsByTagName('current_status_id')[0];
		if (typeof(current_status_id) == 'undefined' || current_status_id == null) {
			current_status_id = '';
		} else {
			current_status_id = current_status_id.firstChild.nodeValue;
		}
		
		var show_expiry = res_xml_obj.getElementsByTagName('show_expiry')[0];
		if (typeof(show_expiry) == 'undefined' || show_expiry == null) {
			show_expiry = '';
		} else {
			show_expiry = show_expiry.firstChild.nodeValue;
		}
		
		var expiry_time = res_xml_obj.getElementsByTagName('expiry_time')[0];
		if (typeof(expiry_time) == 'undefined' || expiry_time == null) {
			expiry_time = '';
		} else {
			expiry_time = expiry_time.firstChild.nodeValue;
		}
		
		var buyback_remarks = res_xml_obj.getElementsByTagName('buyback_remarks')[0];
		if (typeof(buyback_remarks) == 'undefined' || buyback_remarks == null) {
			buyback_remarks = '';
		} else {
			buyback_remarks = buyback_remarks.firstChild.nodeValue;
		}
		
		var trade_mode = res_xml_obj.getElementsByTagName('trade_type')[0];
		if (typeof(trade_mode) == 'undefined' || trade_mode == null) {
			trade_mode = '';
		} else {
			trade_mode = trade_mode.firstChild.nodeValue;
		}
		
		var trade_method = res_xml_obj.getElementsByTagName('trade_method')[0];
		if (typeof(trade_method) == 'undefined' || trade_method == null) {
			trade_method = '';
		} else {
			trade_method = trade_method.firstChild.nodeValue;
		}
		
		// allow_to_upload: for the user who havent upload the ss yet.
		var allow_to_upload = res_xml_obj.getElementsByTagName('allow_to_upload')[0];
		if (typeof(allow_to_upload) == 'undefined' || allow_to_upload == null) {
			allow_to_upload = '';
		} else {
			allow_to_upload = allow_to_upload.firstChild.nodeValue;
		}
		
		divhtml += '<form name="confirm_sent_'+req_grp_id+'" action="<?=tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent')?>" method="post" id="confirm_sent_'+req_grp_id+'" enctype="multipart/form-data">'
				+ '<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding:10px 0px 10px 0px;">'
				+ '<tr>'
				+ '<td width="<?=$col_titles[0]['width']?>" align="<?=$col_titles[0]['align']?>" class="ordersRecords" style="padding:10px 0px 10px 0px;" style="text-align:<?=$col_titles[0]['align']?>;">'+req_grp_id+'</td>'
				+ '<td width="<?=$col_titles[1]['width']?>" align="<?=$col_titles[1]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[1]['align']?>;">'+trade_method+'</td>'
				+ '<td width="<?=$col_titles[2]['width']?>" align="<?=$col_titles[2]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[2]['align']?>;">'+game_name+'<br><small>'+server_name+'</small></td>'
				+ '<td width="<?=$col_titles[3]['width']?>" align="<?=$col_titles[3]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[3]['align']?>;">&nbsp;'+restock_character+'</td>';
		divhtml += '<td width="<?=$col_titles[5]['width']?>" align="<?=$col_titles[5]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[5]['align']?>;">'+req_qty+'</td>';
		
		//if restock character available, let supplier fill in 2nd list qty
		divhtml += '<td width="<?=$col_titles[6]['width']?>" align="<?=$col_titles[6]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[6]['align']?>;">';
		if (current_status_id == '1' && show_restock == '1') {
			divhtml += '<input type="text" name="sent_qty['+req_grp_id+']" size="7" maxlength="9" id="sent_qty_'+req_grp_id+'">';
		} else {
			divhtml += confirmed_qty;
		}
		
		divhtml += '</td>';

		divhtml += '<td width="<?=$col_titles[7]['width']?>" align="<?=$col_titles[7]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[7]['align']?>;">'+amount+'</td>'		
				+ '<td width="<?=$col_titles[8]['width']?>" align="<?=$col_titles[8]['align']?>" class="ordersRecords" style="text-align:<?=$col_titles[8]['align']?>;">'+status_name+'</td>';
		
		//Action Column
		divhtml += '<td width="<?=$col_titles[9]['width']?>" align="<?=$col_titles[9]['align']?>" style="text-align:<?=$col_titles[9]['align']?>;">';
		if (current_status_id == '1') {
			//Processing
			divhtml += '<input name="request_group_id" value="'+req_grp_id+'" type="hidden">' 
					+  '<input name="btn_action_'+req_grp_id+'" id="btn_action_'+req_grp_id+'" value="" type="hidden">'
					+  '<input name="request_id" value="'+req_id+'" type="hidden">';

			if (show_restock == '1') {
				divhtml += 	'<input name="request_qty['+req_grp_id+']" id="request_qty_'+req_grp_id+'" value="'+req_qty+'" type="hidden">' + 
							'<a href="javascript:void(0);" class="vip_button_link" id="confirm_'+req_grp_id+'" onclick="return validate_confirmed_qty(\''+req_id+'\', \''+req_grp_id+'\');"><?=BUTTON_CONFIRM?></a><br/>' + 
							'<a href="javascript:document.getElementById(\'btn_action_'+req_grp_id+'\').value=\'cancel\';javascript:document.getElementById(\'confirm_sent_'+req_grp_id+'\').submit();" onClick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\');"><?=tep_image(DIR_WS_IMAGES . "icon-close.gif", "", "10", "10")?></a>';
			} else {
				divhtml += '<a href="javascript:document.getElementById(\'btn_action_'+req_grp_id+'\').value=\'cancel\';javascript:document.getElementById(\'confirm_sent_'+req_grp_id+'\').submit();" onClick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\');"><?=tep_image(DIR_WS_IMAGES . "icon-close.gif", "", "10", "10")?></a>';
			}
		} else {
			//Button for popup report
			divhtml += '<a href="javascript:void(0);" class="vip_button_link" onclick="showMe(\''+req_grp_id+'\', \'<?=$languages_id?>\');"><?=TEXT_VIEW?></a><br><span class="title-text" id="wbb_div_msgField_'+req_grp_id+'"></span>';
			if (allow_to_upload == 'yes' && current_status_id != 4) {
				divhtml += '<a href="javascript:void(0);" class="vip_button_link" onclick="upload_ss(\''+req_grp_id+'\'); return false;"><?=TEXT_UPLOAD_SS?></a>';
			}
		}
		
		divhtml += "</td>"
				+ "</tr>";
				
		// show expiry and screenshot
		if(show_expiry > 0){
			divhtml += '<tr>'
					+  '	<td colspan="<?=(count($col_titles))?>">'
					+  '		<table bgcolor="#e0ecf4" border="0" cellpadding="2" cellspacing="2" width="100%">'
					+  '			<tr>'
					+  '				<td>'
					+  '					<table border="0" cellpadding="2" cellspacing="1" width="100%">'
					+  '						<tr>'
					+  '							<td class="title-text" width="20%"><?=TEXT_EXPIRES?></td>'
					+  '							<td class="title-text" colspan="2" width="80%">'+ expiry_time +' +10 <?=TEXT_MINUTES?></td>'
					+  '						</tr>'
					+  '					</table>'
					+  '				</td>'
					+  '			</tr>'
					+  '		</table>'
					+  '	</td>'
					+  '</tr>';
		}
		
		if (buyback_remarks.length) {
			divhtml += '<tr>'
					+ '		<td class="title-text" bgcolor="#ffffcc" colspan="<?=(count($col_titles))?>" style="padding:5px;">' + buyback_remarks + '</td>'
					+ '</tr>';
		}
		
		divhtml +='		<tr>'
				+ '			<td width="100%" height="1" colspan="<?=count($col_titles)?>"><img src="<?=DIR_WS_IMAGES . "space.gif"?>" width="1" height="1"></td>'
				+ '		</tr>'
				+ '</table>'
				+ '</form>';
		
		if (is_last_row != 1) {
			divhtml +='<table width="100%" border="0" cellpadding="0" cellspacing="0">'
					+ '     <tr>'
					+ '			<td width="100%" height="1" colspan="<?=count($col_titles)?>"><div class="row_separator"></div></td>'
					+ '		</tr>'
					+ '</table>';
		}
	}
	
	function add_vip_result_row(res_xml_obj) {
		if (typeof(res_xml_obj) != 'object') return;
		
		var orders_products_id = res_xml_obj.getElementsByTagName('orders_products_id')[0];
		if (typeof(orders_products_id) == 'undefined' || orders_products_id == null) {
			return;
		} else {
			orders_products_id = orders_products_id.firstChild.nodeValue;
		}
		
		var game_name = res_xml_obj.getElementsByTagName('game_name')[0];
		if (typeof(game_name) == 'undefined' || game_name == null) {
			game_name = '';
		} else {
			game_name = game_name.firstChild.nodeValue;
		}
		
		var path_name = res_xml_obj.getElementsByTagName('path_name')[0];
		if (typeof(path_name) == 'undefined' || path_name == null) {
			path_name = '';
		} else {
			path_name = path_name.firstChild.nodeValue;
		}
		
		var quantity = res_xml_obj.getElementsByTagName('quantity')[0];
		if (typeof(quantity) == 'undefined' || quantity == null) {
			quantity = '0';
		} else {
			quantity = quantity.firstChild.nodeValue;
		}
		
		var allocate_time = res_xml_obj.getElementsByTagName('allocate_time')[0];
		if (typeof(allocate_time) == 'undefined' || allocate_time == null) {
			allocate_time = '0';
		} else {
			allocate_time = allocate_time.firstChild.nodeValue;
		}
		
		var trade_customers_price = res_xml_obj.getElementsByTagName('trade_customers_price')[0];
		if (typeof(trade_customers_price) == 'undefined' || trade_customers_price == null) {
			trade_customers_price = '0';
		} else {
			trade_customers_price = trade_customers_price.firstChild.nodeValue;
		}
		
		var trade_customers_price_display = res_xml_obj.getElementsByTagName('trade_customers_price_display')[0];
		if (typeof(trade_customers_price_display) == 'undefined' || trade_customers_price_display == null) {
			trade_customers_price_display = '0';
		} else {
			trade_customers_price_display = trade_customers_price_display.firstChild.nodeValue;
		}
		
		var trade_us_price = res_xml_obj.getElementsByTagName('trade_us_price')[0];
		if (typeof(trade_us_price) == 'undefined' || trade_us_price == null) {
			trade_us_price = '0';
		} else {
			trade_us_price = trade_us_price.firstChild.nodeValue;
		}
		
		var trade_us_price_display = res_xml_obj.getElementsByTagName('trade_us_price_display')[0];
		if (typeof(trade_us_price_display) == 'undefined' || trade_us_price_display == null) {
			trade_us_price_display = '0';
		} else {
			trade_us_price_display = trade_us_price_display.firstChild.nodeValue;
		}
		
		var bo_exact_qty_msg = res_xml_obj.getElementsByTagName('bo_exact_qty_msg')[0];
		if (typeof(bo_exact_qty_msg) == 'undefined' || bo_exact_qty_msg == null) {
			bo_exact_qty_msg = '';
		} else {
			bo_exact_qty_msg = bo_exact_qty_msg.firstChild.nodeValue;
		}
		
		var trading_method = res_xml_obj.getElementsByTagName('trading_method')[0];
		if (typeof(trading_method) == 'undefined' || trading_method == null) {
			trading_method = '';
		} else {
			trading_method = trading_method.firstChild.nodeValue;
		}
		
		vipdivhtml += '<form name="accept_order_'+orders_products_id+'" action="<?=tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=accept_order')?>" method="post" id="accept_order_'+orders_products_id+'">'
					+ '<input type="hidden" name="trade_type_'+orders_products_id+'" id="trade_type_'+orders_products_id+'">'
					+ '<table width="100%" border="0" cellpadding="0" cellspacing="0">'
					+ '<tr valign="top">'
					+ '	<td width="<?=$vip_col_titles[0]['width']?>" align="<?=$vip_col_titles[0]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[0]['align']?>;">'+game_name+'<br/><small>'+path_name+'</small><br />( '+trading_method+' )</td>'
					+ '	<td width="<?=$vip_col_titles[1]['width']?>" align="<?=$vip_col_titles[1]['align']?>"  class="ordersRecords" style="text-align:<?=$vip_col_titles[1]['align']?>;">'+quantity+'</td>'
					+ '	<td width="<?=$vip_col_titles[2]['width']?>" align="<?=$vip_col_titles[2]['align']?>"  class="ordersRecords" style="text-align:<?=$vip_col_titles[2]['align']?>;"><input name="submit_quantity['+orders_products_id+']" size="10" type="text" id="submit_quantity_'+orders_products_id+'"></td>'
					+ '	<td width="<?=$vip_col_titles[3]['width']?>" align="<?=$vip_col_titles[3]['align']?>"  class="ordersRecords" style="text-align:<?=$vip_col_titles[3]['align']?>;"><input name="vip_character_name['+orders_products_id+']" size="10" type="text" id="vip_character_name_'+orders_products_id+'"></td>'
					+ '	<td width="<?=($vip_col_titles[4]['width'] + $vip_col_titles[5]['width'])?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords" colspan="2" style="text-align:<?=$vip_col_titles[4]['align']?>;"><input type="hidden" name="opId" value="'+orders_products_id+'">'
					+ '		<table border="0" cellpadding="0" cellspacing="0">';

		if(trade_customers_price != 0){
			vipdivhtml += '		<tr>'
						+ '			<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[4]['align']?>;"><?=TEXT_TRADE_CUSTOMERS_PRICE?>'+trade_customers_price_display+'</td>'
						+ '			<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[5]['align']?>;">'
						+ '				<div style="float: right;" onclick="return validate_request_qty(\''+orders_products_id+'\', \''+quantity+'\'); false" class="gray_button"><a href="javascript:pre_submit(\''+orders_products_id+'\', \'trade_customer\');"><span><font><?=BUTTON_TRADE_CUSTOMERS?></font></span></a></div>'
						+ '	        </td>'
						+ '		</tr>';
		}
		
		if(trade_us_price != 0){		
			vipdivhtml += '		<tr>'
						+ '			<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[4]['align']?>;"><?=TEXT_TRADE_WITH_US_PRICE?>'+trade_us_price_display+'</td>'
						+ '			<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[5]['align']?>;">'
						+ '				<div style="float: right;" onclick="return validate_request_qty(\''+orders_products_id+'\', \''+quantity+'\'); false" class="gray_button"><a href="javascript:pre_submit(\''+orders_products_id+'\', \'trade_us\');"><span><font><?=BUTTON_TRADE_WITH_US?></font></span></a></div>'
						+ '	        </td>'
						+ '		</tr>';
		}
		
		vipdivhtml += '			<tr>'
					+ '				<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[4]['align']?>;">&nbsp;</td>'
					+ '				<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords" style="text-align:<?=$vip_col_titles[5]['align']?>;">'
					+ '					<div style="float: right;" onclick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\')" class="gray_button"><a href="javascript:pre_submit(\''+orders_products_id+'\', \'trade_cancel\');"><span><font><?=BUTTON_TRADE_CANCEL?></font></span></a></div>'
					+ '	       		 </td>'
					+ ' 		</tr>'
					+ '		</table>'
					+ ' </td>'
					+ '</tr>';
		
		vipdivhtml += '<tr>'
					+  '	<td colspan="<?=(count($col_titles))?>">'
					+  '    <table>'
					+  '    	<tr>'
					+  '      		<td width="125">'
					+  ' 				<div style="position:relative;height:43px;">'
					+  '  					<div style="position:absolute;" class="loading_img"><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\"bb_loading\" style=\"display:block;\"")?></div>'
					+  '					<div style="position:absolute;"><div class="captcha_img" style=""></div></div>'
					+  '  				</div>'
					+  '			</td>'
					+  '			<td>'
					+  ' 				<div style="padding-top: 6px;padding-left:5px;"><div style="padding-bottom:5px;"><?=sprintf(TEXT_REFRESH_CAPTCHA_VIP, ".captcha_img")?></div><?=tep_draw_input_field("captcha_code_'+orders_products_id+'", TEXT_CAPTCHA_ANSWER, " style=\"height:25px;font-size:10px;color:silver;padding:3.5px;\" onClick=\"clear_input_field(\'captcha_code_'+orders_products_id+'\')\" size=\"2\" maxlength=\"2\" id=\"captcha_code_'+orders_products_id+'\"").' '.TEXT_CAPTCHA_INSTRUCTION?></div>'
					+  '			</td>'
					+  '		</tr>'
					+  '	</table>'
					+  '   </td> ';
		
		vipdivhtml += '		<tr>'
					+ '			<td class="ordersRecords" colspan="2"><b><?=TEXT_EXPIRES?></b>'+allocate_time+'</td>';
		if (trade_us_price != 0 || trade_customers_price != 0) {
			vipdivhtml	+= '		<td colspan="<?=(count($col_titles)-1)?>" class="ordersRecords"><span class="redIndicator">'+bo_exact_qty_msg+'</span></td>';
		} else {
			vipdivhtml	+= '		<td colspan="<?=(count($col_titles)-1)?>" class="ordersRecords"><span class="redIndicator">&nbsp;</span></td>';
		}
		
		vipdivhtml	+= '	</tr>'
					+ '		<tr>'
					+ '			<td height="1" colspan="<?=count($vip_col_titles)?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>'
					+ '		</tr>'
					+ '	</table>'
					+ '</form>';
						
	}
		                   		    
	function show_search_results_div(orderType) {
		if(orderType == 'vip'){
			document.getElementById('vip_awaiting_order_list').innerHTML = vipdivhtml;
			get_captcha_img(".captcha_img");
		} else {
			document.getElementById('vipodh_div_search_results').innerHTML = divhtml;
		}
	}

	function toggleCountdown(cbChecked) {
		if (cbChecked == true) {
			start_coundown_activity();
		} else {
			stop_coundown_activity();
		}
	}
	function start_coundown_activity() {
		coundown_active = true;
		countdown_secs = countdown_secs_default;
		document.getElementById('timer_vip_main').innerHTML = '<span id="timer_vip_main" name="timer_vip_main"><?=sprintf(TEXT_SECONDS_COUNTDOWN, '<span id="timer_vip_display" name="timer_vip_display"></span>')?></span>';
		doCountDown();
	}
	function stop_coundown_activity() {
		coundown_active = false;
		document.getElementById('timer_vip_main').innerHTML = '<span id="timer_vip_display" name="timer_vip_display"></span>';
	}
    
	function vip_refresh_order() {
        countdown_secs = 0;
        if (coundown_active == false) {
            doCountDown(true);
        }
    }
    
	function doCountDown(manual) {
		if (manual == null || !manual) {
			countdown_secs -= 1;
			document.getElementById('timer_vip_display').innerHTML = countdown_secs;
			if (coundown_active == false) {
				document.getElementById('timer_vip_main').innerHTML = '';
				return;
			}
		}
				
		if (countdown_secs > 0) {
			history_timerID = setTimeout("doCountDown()",2000);
		} else {
			if (history_timerID) {
				clearTimeout(history_timerID);
				history_timerID = 0;
			}
			onVipAwaitingOrderRefresh();
		}
	}

	function search_vip_order_history() {
		var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
		var product_type = document.getElementById('vipodh_input_product_type_select').value;
		var order_no = document.getElementById('vipodh_input_order_no').value;
		var game_cat_id = document.getElementById('vipodh_input_game_select').value;
		var start_date = document.getElementById('vipodh_input_start_date').value;
		var end_date = document.getElementById('vipodh_input_end_date').value;
		onVipOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id);
	}
	
	function showHideBox() {
	    var item = 'contentTextBoxAll';
    	if (DOMCall(item).className == 'hide') {
    		DOMCall(item).className = 'show';
    		DOMCall(item +'_rotate').className = 'hide';
    		DOMCall(item +'_image').innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_collapse.gif')?>';
    	} else {
    		DOMCall(item).className = 'hide';
    		DOMCall(item +'_rotate').className = 'show';
    		DOMCall(item +'_image').innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_expand.gif')?>';
    	}
    }
    
    function popUp(url) {
        eval("page = window.open(url, '', 'width=640,height=480,top=100,left=100,scrollbars=yes');");
    }
    
    function pre_submit(oId, trade_type) {
    	jQuery("#trade_type_"+oId).val(trade_type);
    	jQuery("#accept_order_"+oId).submit();
    }
    
    function showMe(buyback_req_grp_id, languages_id) {
		var server_action = 'show_order_report';
		var pop_out;
		
		if (buyback_req_grp_id != '' && languages_id != '') {
			pop_out_loading = '<table border="0" width="400" cellspacing="0" cellpadding="0" align="center"><tr><td>';
			pop_out_loading += '<table width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
			pop_out_loading += '<tr><td><?=tep_draw_separator("pixel_trans.gif", "100%", "18")?></td></tr>';
			pop_out_loading += '<tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES . "loading.gif", "", "20", "20")?></td></tr>';
			pop_out_loading += '<tr><td><?=tep_draw_separator("pixel_trans.gif", "100%", "18")?></td></tr>';
			pop_out_loading += '</table>';
			pop_out_loading += '</td></tr></table>';
			
			jQuery().ajaxStart(function(){
				jQuery.blockUI.defaults.message = pop_out_loading;
				jQuery.blockUI({ 
						overlayCSS: {
							backgroundColor:'black',
							opacity:'0.8', 
							cursor:'default'
						},
						css: { 
							border:	'0px',
							textAlign:'left', 
							cursor:'default',
							width:'400', 
				            top:  (jQuery(window).height() /2) - 261 + 'px', 
				            left: (jQuery(window).width() / 2) - 168 + 'px', 
					        padding: '0px', 
					        opacity: '1'
						}
					});
			}).ajaxComplete(function(){
				jQuery("#loading").hide();
			});
			
			var mode = 1; // 1 = VIP, 0 = Normal
			var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_req_grp_id="+buyback_req_grp_id+"&slang="+languages_id+"&mode="+mode;
			
			jQuery.get(ref_url, function(xml){
				jQuery(xml).find('response').each(function(){
					var game_name = jQuery("game_name", this).text();
					var server_name = jQuery("server_name", this).text();
					var req_qty = jQuery("req_qty", this).text();
					var uom = jQuery("uom", this).text();
					var product_name = jQuery("product_name", this).text();
					var total_price = jQuery("total_price", this).text();
					var sender_character = jQuery("sender_character", this).text();
					var restk_character = jQuery("restk_character", this).text();
					var delivery_time = jQuery("delivery_time", this).text();
					var contact_name = jQuery("contact_name", this).text();
					var contact_no = jQuery("contact_no", this).text();
					var show_comments = jQuery("show_comments", this).text();
					var order_reference = jQuery("order_reference", this).text();
		      		
		      		pop_out = '<table width="400" border="0" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
					pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=HEADER_ORDER_REPORT?></b></div><div style="float:right;padding:10px 15px"><div style="float:left"><a href="javascript:;" onClick="hideMe();" class="text-decoration: none;"><font color="#000"><u><b><?=TEXT_LIST_CLOSE ?></b></u></font></a></div><div style="float:left;display:block;margin: 3px 5px" class="closeIconBlack" onClick="hideMe();"><!-- --></div></div></td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_GAME?>:</td><td class="smallText">&nbsp;'+game_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_SERVER?>:</td><td class="smallText">&nbsp;'+server_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_QUANTITY?>:</td><td class="smallText">&nbsp;'+req_qty+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_TOTAL_PRICE?>:</td><td class="smallText">&nbsp;'+total_price+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CHAR_NAME?>:</td><td class="smallText">&nbsp;'+sender_character+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=ENTRY_RECEIVER_CHAR_NAME?>:</td><td class="smallText">&nbsp;'+restk_character+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_DELIVERY_TIME?>:</td><td class="smallText">&nbsp;'+delivery_time+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CONTACT_NAME?>:</td><td class="smallText">&nbsp;'+contact_name+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_CONTACT_NO?>:</td><td class="smallText">&nbsp;'+contact_no+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_ADDITIONAL_COMMENTS?>:</td><td class="smallText">&nbsp;'+show_comments+'</td></tr>';
					pop_out += '<tr><td class="smallText" width="50%" style="padding:10px;text-align:right;"><?=TEXT_ORDER_REFERENCE?>:</td><td class="smallText">&nbsp;'+order_reference+'</td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding: 20px 0 0"><!-- --></div></td></tr>';
					pop_out += '</table>';
					
					jQuery.blockUI.defaults.message = pop_out;
					jQuery.blockUI({ 
						overlayCSS: {
							backgroundColor:'black',
							opacity:'0.8', 
							cursor:'default'
						},
						css: { 
							border:	'0px',
							textAlign:'left', 
							cursor:'default',
							width:'400', 
				            top:  (jQuery(window).height() /2) - 261 + 'px', 
				            left: (jQuery(window).width() / 2) - 168 + 'px', 
					        padding: '0px', 
					        opacity: '1'
						}
					});
				});
			});
		}
	}
	
	function hideMe() {
		jQuery.unblockUI();return false;
	}
//-->
</script>

<style>
	.search_title {
			font-weight: bold;
			font-size: 11px;
	}
</style>

<div><h1><?=NAVBAR_TITLE?></h1></div>
<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox" style="padding:0px;">
				<tr><td class="infoBoxTitle" style="padding:5px 0px 5px 12px;"><?=TEXT_VIP_AWAITING_ORDERS_LIST?></td></tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
                			<tr>
                				<td>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td class="boxHeaderLeftTable">&nbsp;</td>
											<td class="boxHeaderCenterTable" colspan="<?=count($vip_col_titles)?>">
											<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<!--start vip orders -->
												<tr valign="center">
												<?
													foreach ($vip_col_titles as $vip_col_array) {
														echo "<th width='".$vip_col_array['width']."' align='".$vip_col_array['align']."'>{$vip_col_array['title']}</th>";
													}
												?>
												</tr>
											</table>
								    		</td>
								    		<td class="boxHeaderRightTable">&nbsp;</td>
								    		<td align="right" width="1">&nbsp;</td>
										</tr>
										
										<tr>
											<td align="right" width="1">&nbsp;</td>
									    	<td height="90%" colspan="<?=count($vip_col_titles)+2?>">
										    	<div id="vip_awaiting_order_list" name="vip_awaiting_order_list" style="position:relative; height:160px; overflow:auto;"><?=$vip_order_html?></div>
											</td>
											<td align="right" width="1">&nbsp;</td>
									    </tr>
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td align="center" colspan="10">
												<table border="0" cellpadding='0' cellspacing='0' width="100%">
													<tr><td><div class="row_separator"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></div></td></tr>
													<tr>
														<td align='center' colspan='<?=count($vip_col_titles)?>'>
															<div id='auto_refresh_bar' class='title-text'>
																<table border="0" cellpadding='0' cellspacing='0' align="center">
																<tr>
																	<td align='center'><?=sprintf(TEXT_RESULT_SEARCH, '<span id="total_order">0</span>');?>&nbsp;</td>
																</tr>
																<tr>
																	<td align='center'>
																		<div style="text-align:center;">
																			<span id="timer_vip_main" name="timer_vip_main"><span id="timer_vip_display" name="timer_vip_display"></span></span>
																		</div>
																	</td>
																</tr>
																<tr>
																	<td align='center'>
																		<div class="title-text" align="center" style="margin:0px 8px;">
																			<?=tep_div_button(2, BUTTON_REFRESH,'javascript:vip_refresh_order();', 'style="float:left;"', 'gray_button') ?>
																		</div>
																	</td>
																</tr>
																</table>
															</div>
														</td>
													</tr>
												</table>
											</td>
											<td align="right" width="1">&nbsp;</td>
										</tr>
									</table>
                				</td>
                			</tr>
                		</table>
                	</td>
                </tr>
            </table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox" style="padding:0px;">
				<tr><td class="infoBoxTitle" style="padding:5px 0px 5px 12px;"><?=BUTTON_SEARCH?></td></tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table cellpadding="2" border="0" cellspacing="0" width="100%">
		                <tbody>
		                    <tr>
		                      <td>
								<!-- Start inner table -->
								<?=tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action', 'startdate')) . 'action=search'), "post")?>
						    	<table border="0" width="100%" cellspacing="5" cellpadding="0" class="buttonBox">
				                	<tr>
				            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				            		</tr>
				            		<tbody class="show" id="odh_tbody_step1" name="odh_tbody_step1">
				                		<tr>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                		<td align="left" valign="top" class="search_title"><?=TEXT_ORDER_STATUS?>:</td>
											<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                			<td align="left" valign="top" width="30%"><?=tep_draw_pull_down_menu('vipodh_input_order_status_select', $order_status_arr, (isset($form_values_arr['vipodh_input_order_status_select']) && $form_values_arr['vipodh_input_order_status_select'] ? $form_values_arr['vipodh_input_order_status_select'] : ''), ' id="vipodh_input_order_status_select" ')?>
				                			    <?='&nbsp;' . tep_image(DIR_WS_ICONS . 'help_info.gif', '', '12', '12', ' id="order_status_tips" title="'.htmlspecialchars(TEXT_HELP_ORDER_STATUS_SELECT).'" ')?>
												<noscript><?='<br>' . TEXT_HELP_ORDER_STATUS_SELECT?></noscript>
											</td>
											<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                			<td align="left" valign="top" class="search_title"><?=TEXT_PRODUCT_TYPE?>:</td>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                		<td align="left" valign="top"><?=tep_draw_pull_down_menu('vipodh_input_product_type_select', $product_type_arr, (isset($form_values_arr['vipodh_input_product_type_select']) && $form_values_arr['vipodh_input_product_type_select'] ? $form_values_arr['vipodh_input_product_type_select'] : ''), ' id="vipodh_input_product_type_select" ')?></td>
				                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				                		<tr>
					                		<td align="left" valign="top" class="search_title"><?=TEXT_ORDER_NO?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_order_no', (isset($form_values_arr['vipodh_input_order_no']) && $form_values_arr['vipodh_input_order_no'] ? $form_values_arr['vipodh_input_order_no'] : ''), 'id="vipodh_input_order_no" size="22" maxlength="16"')?></td>
				                			<td align="left" valign="top" class="search_title"><?=TEXT_GAME?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_pull_down_menu('vipodh_input_game_select', $wbb_game_list_arr, (isset($form_values_arr['vipodh_input_game_select']) && $form_values_arr['vipodh_input_game_select'] ? $form_values_arr['vipodh_input_game_select'] : ''), ' id="vipodh_input_game_select" ')?></td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				                		<tr>
					                		<td align="left" valign="top" class="search_title"><?=TEXT_START_DATE?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_start_date', (isset($vipodh_input_start_date) && tep_not_null($vipodh_input_start_date) ? $vipodh_input_start_date : ''), ' id="vipodh_input_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.vipodh_input_start_date); }" onKeyPress="return noEnterKey(event)" ', '', false)?>
					                			<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.vipodh_input_start_date);return false;" HIDEFOCUS><img name="popcal1" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
					                		</td>
					                		<td align="left" valign="top" class="search_title"><?=TEXT_END_DATE?>:</td>
				                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_end_date', (isset($vipodh_input_end_date) && tep_not_null($vipodh_input_end_date) ? $vipodh_input_end_date : ''), ' id="vipodh_input_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.vipodh_input_end_date); }" onKeyPress="return noEnterKey(event)" ', '', false)?>
				                				<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.vipodh_input_end_date);return false;" HIDEFOCUS><img name="popcal2" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
				                			</td>
				                		</tr>
					                	<tr>
					            			<td colspan="4"></td>
					            		</tr>
				            		</tbody>
				                	<tr>
				            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				            		</tr>
				                	<tr>
				                		<td colspan="9" align="left" valign="top">
				                		    <table border="0" width="100%" cellspacing="2" cellpadding="0">
				                		        <tr>
				                		            <td valign="top">
				                		            	&nbsp;
		                                            </td>
		                                            <td align="right" valign="top" width="100">
		                                            	<?=tep_div_button(2, TEXT_SEARCH,'javascript:search_vip_order_history();', ' style="float:left;"', 'gray_button') ?>
		                                            </td>
		                                            <td align="right" valign="top" width="100">
		                                            	<?=tep_div_button(2, BUTTON_RESET,tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, 'action=reset_session'), 'style="float:left;"', 'gray_button') ?>
		                                            </td>
		                                        </tr>
		                                    </table>
				                		</td>
				            		</tr>
				            	</table>
						        </form>
								<!-- end inner table -->
							</td>
						</tr>
		                </tbody>
					</table>
            		</td>
            	</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox" style="padding:0px;">
				<tr><td class="infoBoxTitle" style="padding:5px 0px 5px 12px;"><?=BUTTON_SEARCH?></td></tr>
				<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
                			<tr>
                				<td>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
											<td class="boxHeaderLeftHigherTable">
												<?=tep_draw_separator('pixel_trans.gif', '1', '1')?>
											</td>
											<td class="boxHeaderCenterTable" colspan="<?=count($vip_col_titles)?>">
												<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr valign="center">
												<?
													foreach ($col_titles as $col_array) {
														echo "<th width='".$col_array['width']."' align='".$col_array['align']."'>{$col_array['title']}</th>";
													}
												?>
													<th width="4">&nbsp;</th>
												</tr>
											</table>
								    		</td>
								    		<td class="boxHeaderRightHigherTable"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								    		<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
										</tr>
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td colspan="<?=count($vip_col_titles)+2?>">
												<table border="0" cellpadding="0" cellspacing="1" height="205" width="100%">
											    	<tbody>
											    	<tr>
											    		<td height="90%" colspan="<?=$num_titles?>">
												    		<div id="vipodh_div_search_results" name="vipodh_div_search_results" style="text-align:center;position:relative; height:205; overflow:auto;"><?=TEXT_PLS_CLICK_SEARCH_BTN?></div>
														</td>
											    	</tr>
							                    	<tr><td colspan="<?=$num_titles?>" height="1"><div class="row_separator"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></div></td></tr>
													<tr>
														<td align="center" colspan="<?=$num_titles?>">
															<table border="0" width="100%" cellpadding='0' cellspacing='0'>
																<tr>
																	<td align="center" colspan='<?=$num_titles?>'>
																		<div id='auto_refresh_bar' class='title-text'>
																		<table border="0" cellpadding='0' cellspacing='0' align="center">
																		<tr>
																			<td align='center'><?=sprintf(TEXT_RESULT_SEARCH, '<span id="num_results">0</span>');?>&nbsp;</td>
																		</tr>
																		<tr>
																			<td align='center'>
																				<div class="title-text" align="center" style="margin:0px 8px;">
																					<?=tep_div_button(2, BUTTON_REFRESH,'javascript:search_vip_order_history();', 'style="float:left;"', 'gray_button') ?>
																				</div>
																			</td>
																		</tr>
																		</table>
																		</div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td height="5" colspan="<?=$num_titles?>"></td>
													</tr>
												    </tbody>
												</table>
											</td>
											<td align="right" width="1">&nbsp;</td>
										</tr>
									</table>
                				</td>
                			</tr>
                		</table>
                	</td>
                </tr>
			</table>
		</td>
	</tr>
</tbody>
</table>

<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
<script type="text/javascript">
<?
	if ($vipodh_auto_refresh_results_bool) {
		echo 'toggleCountdown(true);';
	}
?>
//-->
</script>