<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>vip_xmlhttp.js"></script>
<?
if ($messageStack->size($content) > 0) {
	echo '	<table cellspacing="2" cellpadding="0">
			  	<tr><td>' . $messageStack->output($content) . '</td></tr>
			  	<tr>
					<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>
			</table>';
}
?>
<div><h1><?=NAVBAR_TITLE?></h1></div>
<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
    	<td colspan="10">
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
    			<tr><td class="infoBoxTitle"><?=HEADING_TITLE?></td></tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
                			<tr>
                				<td>
                					<?=tep_draw_form('load_server', FILENAME_MY_VIP_REGISTER_SERVER)?>
									<table border="0" cellpadding="2" cellspacing="0" width="100%">
										<tr>
											<td width="10%" nowrap><p><?=TEXT_SELECT_SERVER?></p></td>
											<td width="90%"><?=tep_draw_pull_down_menu('cID', $active_game_array, $cID, 'id="cID" onChange="this.form.submit();"')?><div id="select_server"></div></td>
										</tr>
									</table>
									</form>
                				</td>
                			</tr>
<?	if ($cID) { ?>
		<tr>
			<td>
				<?=tep_draw_form('save_setting', tep_href_link(FILENAME_MY_VIP_REGISTER_SERVER, 'action=save_setting&cID='.$cID, 'NONSSL', false), 'post', 'onSubmit="return check_working_time();"' )?>
				<table border="0" cellpadding="2" cellspacing="0" width="100%">
					<tr>
						<td width="10%"><p><?=TEXT_WORKING_DAY?></p></td>
						<td width="90%" align="left">
							<?
								for ($day_cnt = 1; $day_cnt <= 7; $day_cnt++){
									$day_checkbox = $day_cnt;
									if ($day_cnt == 7){
										$day_checkbox = 0;
									}
									echo tep_draw_checkbox_field('working_days[]', (string)$day_checkbox, (isset($working_days_array) && in_array($day_checkbox, $working_days_array) ? true : false)) . (defined('TEXT_DAY_'.$day_checkbox) ? constant('TEXT_DAY_'.$day_checkbox) : $day_checkbox).'&nbsp;';
								}
							?>
						</td>
					</tr>
					<tr>
						<td width="10%"><p><?=TEXT_WORKING_TIME?></p></td>
						<td width="90%" align="left">
						<?									
							$hour_options = array();
							for ($hours_cnt = 0; $hours_cnt < 24; $hours_cnt++) {
								$hour_options[] = array('id' => date("H:i", mktime($hours_cnt, 0, 0, date("m"), date("d"), date("Y"))),
														'text' => date("H:i", mktime($hours_cnt, 0, 0, date("m"), date("d"), date("Y")))
														);
							}
							echo tep_draw_pull_down_menu('start_working_time', $hour_options, (isset($working_time_array) && is_array($working_time_array) ? $working_time_array[0] : ''), 'id=start_working_time') . ' - ' . tep_draw_pull_down_menu('end_working_time', $hour_options, (isset($working_time_array) && is_array($working_time_array) ? $working_time_array[1] : ''), 'id=end_working_time') . '&nbsp;' . TEXT_TIME_ZONE;
						?>	
						</td>
					</tr>
					<tr>
						<td colspan="2" align="right">
							<?=tep_div_button(1, BUTTON_VIP_REG_SERVER_SAVE,'save_setting', 'style="float:right;"', 'gray_button') ?>
						</td>
					</tr>
				</table>
				</form>
				<script language="JavaScript">
				function check_working_time(){
					start_time = document.getElementById('start_working_time').value;
					start_hour = start_time.substring(0,2);
					end_time = document.getElementById('end_working_time').value;
					end_hour = end_time.substring(0,2);
					if (start_hour > end_hour) {
						alert('<?=JS_ERROR_INVALID_WORKING_TIME?>');
						return false;
					}
					return true;
				}
				</script>
			</td>
		</tr>
<?	} ?>
									
                		</table>
                	</td>
                </tr>
            </table>
        </td>
    </tr>
    
	<tr>
		<td width="3"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	
	<tr>
    	<td colspan="10">
<?	if ($cID) { ?>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
            				<tr>
								<td><p style="text-align:left;"><?=NOTE_SELECT_SERVER?></p></td>
							</tr>
                			<tr>
                				<td>
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td align="right" width="1">&nbsp;</td>
											<td class="boxHeaderLeftTable">&nbsp;</td>
											<td class="boxHeaderCenterTable">
											<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr>
								    				<th width="60%" align="left"><?=TABLE_HEADER_SERVER_NAME?></th>
								    				<th class="" align="center" width="20%"><?=TABLE_HEADER_SERVER_QUANTITY?></th>
								    				<th class="" width="20%" ><?=TABLE_HEADER_SERVER_SELECTION?></th>
								    			</tr>
								    		</table>
								    		</td>
								    		<td class="boxHeaderRightTable">&nbsp;</td>
								    		<td align="right" width="1">&nbsp;</td>
										</tr>
									</table>
                				</td>
                			</tr>
		            		<tr>
			    				<td>
			    					<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td width="1">&nbsp;</td>
											<td width="7">&nbsp;</td>
											<td>
											<div style=" width:100%; min-height:300px; overflow:auto;">
						    					<table border="0" width="100%" height="50px" cellspacing="0" cellpadding="0" id="serverTable" style="overflow: hidden">
						    						<tbody>
						    						</tbody>
						    					</table>
					    					</div>
								    		</td>
								    		<td width="1">&nbsp;</td>
								    		<td width="5">&nbsp;</td>
										</tr>
									</table>
			    				</td>
							</tr>
                		</table>
                	</td>
                </tr>
            </table>
<?	} ?>
        </td>
    </tr>
	</tbody>
</table>
<script language="JavaScript">
	get_full_server_list('<?=$cID?>', 'A', 'serverTable');
</script>