<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>vip_xmlhttp.js"></script>
<?
if ($messageStack->size($content) > 0) {
	echo '	<table cellspacing="2" cellpadding="0">
			  	<tr><td>' . $messageStack->output($content) . '</td></tr>
			  	<tr>
					<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
				</tr>
			</table>';
}
?>

<? if ($report_sql == '') {?>
<script type="text/javascript">
<!--
	function validStartAndEndDate(start_date, end_date) {
		var s = start_date.split('-');
		var e = end_date.split('-');

		var startDateObj = new Date(s[0],s[1],s[2]);
		var endDateObj = new Date(e[0],e[1],e[2]);

		if(endDateObj.getTime() < startDateObj.getTime()) {
			return false;
		} else {
			return true;
		}
	}

	function validate_report_form(){
		var game_id = document.getElementById('cID').value;

		var start_date = document.getElementById('report_input_start_date').value;
		if(start_date.length > 0) {
			if (!validateDate(start_date)) {
				alert('Start date is not a valid date format as requested!');
				document.getElementById('report_input_start_date').focus();
				document.getElementById('report_input_start_date').select();
				return false;
			}
		}

		var end_date = document.getElementById('report_input_end_date').value;
		if(end_date.length > 0) {
			if (!validateDate(end_date)) {
				alert('End date is not a valid date format as requested!');
				document.getElementById('report_input_end_date').focus();
				document.getElementById('report_input_end_date').select();
				return false;
			}
		}

		if (start_date.length > 0 && end_date.length > 0) {
			if (!validStartAndEndDate(start_date, end_date)) {
				alert('Start Date is greater than End Date!');
				document.getElementById('report_input_start_date').focus();
				document.getElementById('report_input_start_date').select();
				return false;
			}
		}

		if (game_id == 0){
			alert('<?=JS_ERROR_INVALIDE_GAME_ID?>');
			document.getElementById('cID').focus();
			return false;
		}

		return true;
	}

//-->
</script>
<? }?>

<div><h1><?=NAVBAR_TITLE?></h1></div>
<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" class="infoBox" style="padding-left:0px;padding-right:0px;">
				<tr><td class="infoBoxTitle" style="padding-left:12px;"><?=HEADER_TITLE_MY_VIP_REPORT?></td></tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
                			<tr>
                				<td>
									<table border="0" cellpadding="5" cellspacing="5" width="100%">
										<tbody>
<? if ($action == '' || $cID == 0) {?>
			<?=tep_draw_form('vip_report', tep_href_link(FILENAME_MY_VIP_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report'), 'post', 'enctype="multipart/form-data"');?>
			<tr>
				<td width="25%" class="vipTextTitle" nowrap><?=TEXT_SELECT_GAME?> :</td>
				<td width="75%" colspan="3"><?=tep_draw_pull_down_menu('cID', $active_game_array, (isset($form_values_arr['cID']) ? $form_values_arr['cID'] : '0') , ' id="cID"')?></td>
			</tr>
			<tr>
				<td width="25%" class="vipTextTitle" nowrap><?=TEXT_REPORT_TYPE?> :</td>
				<td width="75%" colspan="3"><?=tep_draw_pull_down_menu('report_type', $report_type_array, (isset($form_values_arr['report_type']) ? $form_values_arr['report_type'] : '1') )?></td>
			</tr>
			<tr>
				<td width="20%" class="vipTextTitle" nowrap><?=TEXT_START_DATE?> :</td>
				<td width="25%"><?=tep_draw_input_field('report_input_start_date', (isset($form_values_arr['report_input_start_date']) && $form_values_arr['report_input_start_date'] ? $form_values_arr['report_input_start_date'] : ''), ' id="report_input_start_date" size="15" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.vip_report.report_input_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
					<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.vip_report.report_input_start_date);return false;" HIDEFOCUS><img name="popcal1" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
				</td>
				<td width="20%" class="vipTextTitle" nowrap><?=TEXT_END_DATE?> :</td>
				<td width="35%"><?=tep_draw_input_field('report_input_end_date', (isset($form_values_arr['report_input_end_date']) && $form_values_arr['report_input_end_date'] ? $form_values_arr['report_input_end_date'] : ''), ' id="report_input_end_date" size="15" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.vip_report.report_input_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
					<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.vip_report.report_input_end_date);return false;" HIDEFOCUS><img name="popcal2" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
				</td>
			</tr>
			<tr>
				<td colspan="3" align="right">
					&nbsp;
				</td>
				<td align="right">
					<?=tep_div_button(2, BUTTON_REPORT, 'javascript:document.vip_report.submit();', 'onclick="return validate_report_form();" style="float:left;"', 'gray_button') ?>
					<?=tep_div_button(2, BUTTON_RESET, tep_href_link(FILENAME_MY_VIP_REPORT, 'action=reset_session'), 'style="float:left;"', 'gray_button') ?>
		        </td>
			</tr>
			</form>
<? } else {
		$row_count = 0;
		$total = count($vipReportObj->start_date_display_array);

		$table_width = $total * '100';
?>
			<tr>
				<td>
					<table border="0" cellpadding="2" cellspacing="2" width="400">
						<tr>
							<td width="10%" class="vipTextTitle"><?=TEXT_GAME?> :</td>
							<td width="30%"><?=(defined("DISPLAY_NAME_CAT_ID_".$cID) ? constant ("DISPLAY_NAME_CAT_ID_".$cID) : tep_get_categories_name($cID, $languages_id))?></td>
						</tr>
						<tr>
							<td width="10%" class="vipTextTitle"><?=TEXT_REPORT_TYPE?> :</td>
							<td width="30%"><?=$report_type_array[$report_type-1]['text']?></td>
						</tr>
						<tr>
							<td width="10%" class="vipTextTitle"><?=TEXT_DATE?> :</td>
							<td width="30%"><?=date("Y-m-d", $vipReportObj->start_date) . ' ' .TEXT_UNTIL . ' ' .date("Y-m-d", $vipReportObj->end_date)?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
				<table border="0" cellpadding="2" cellspacing="2" width="100%">
				<tr>
					<td>
					<div style="width:700px;float:left;overflow:scroll;">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td align="right" width="1">&nbsp;</td>
						<td class="boxHeaderLeftTable">&nbsp;</td>
						<td class="boxHeaderCenterTable" colspan="<?=count($vip_col_titles)?>">
						<table border="0" cellpadding="0" cellspacing="0" width="<?=$table_width+150?>">
							<!--start vip orders -->
							<tr valign="center">
								<th width="150" style="padding-left:5px;"><?=TEXT_SERVER_NAME?></th>
<?
								global $currencies;
								$_DECIMAL_PLACES_DISPLAY = 4; //Display
								$currencies->set_decimal_places($_DECIMAL_PLACES_DISPLAY);
								$total_summary = array();

								for($day_cnt= count($vipReportObj->start_date_display_array)-1; $day_cnt >= 0; $day_cnt--){
									echo '<th width="100">' . date("Y-m-d", $vipReportObj->start_date_display_array[$day_cnt]) . '</th>';
								}
?>
							</tr>

						</table>
			    		</td>
			    		<td class="boxHeaderRightTable">&nbsp;</td>
			    		<td align="right" width="1">&nbsp;</td>
					</tr>
					</table>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td align="right" width="1">&nbsp;</td>
						<td width="1">&nbsp;</td>
						<td>
							<table border="0" cellpadding="0" cellspacing="0" width="<?=$table_width+150?>">
<?
							foreach($buybackSupplierObj->products_arr as $pid => $pinfo) {
							if(isset($vipSupplierObj->registered_servers[$pid])){
								//$row_style = ($row_count%2) ? 'ordersListingOver' : 'ordersListingOut' ;
								echo '<tr class="ordersListing" onMouseOver="rowOverEffect(this, \'ordersListingOver\')" onMouseOut="rowOutEffect(this, \'ordersListingOut\')">
											<td width="150" style="padding-left:5px;">' . strip_tags($pinfo['categories_name']) . '</td>';
								for($day_count = count($vipReportObj->start_date_display_array)-1; $day_count >= 0; $day_count--){
									$date_now = $vipReportObj->start_date_display_array[$day_count];

									if (!isset($total_summary[$date_now]) || !is_array($total_summary[$date_now])) {
										$total_summary[$date_now] = array(	'quantity' => 0,
																			'price' => 0
																			);
									}
									echo '<td align="center" nowrap="nowrap" width="100">';
									if(isset($vipReportObj->report_info[$pid][$date_now])){

										$total_summary[$date_now]['quantity'] += $vipReportObj->report_info[$pid][$date_now]['quantity'];
										if($vipReportObj->report_type == 3){
											$total_summary[$date_now]['price'] += ($vipReportObj->report_info[$pid][$date_now]['quantity'] * $vipReportObj->report_info[$pid][$date_now]['price']);
										} else {
											$total_summary[$date_now]['price'] += $vipReportObj->report_info[$pid][$date_now]['price'];
										}
										echo $vipReportObj->report_info[$pid][$date_now]['quantity'];
										if($vipReportObj->report_info[$pid][$date_now]['price'] > 0){
											$p_price = $vipReportObj->report_info[$pid][$date_now]['price'];
											$p_quantity = $vipReportObj->report_info[$pid][$date_now]['quantity'];
											if($vipReportObj->report_type == 3){
												echo '<hr noshade size="1">'. $currencies->format($p_quantity * $p_price, true, $_SESSION['currency']);
											} else if ($vipReportObj->report_type == 4){
												echo '<hr noshade size="1">'. $currencies->format($p_price, false, $_SESSION['currency']);
											} else {
												echo '<hr noshade size="1">'. $currencies->format($p_price, true, $_SESSION['currency']);
											}
										}
									} else {
										echo '&nbsp;';
									}
									echo '</td>';
								}
								$row_count++;
								echo '</tr>';

								if ($row_count != 0) {
									echo '<tr><td colspan="'.($total + 1).'"><div class="row_separator"></div></td></tr>';
								}
							}
						}
		//$row_style = ($row_count%2) ? 'ordersListingOver' : 'ordersListingOut';
?>
						<tr class="ordersListing">
							<td class="vipTextTitle" style="padding-left:5px;">
								<b>
<?
									switch ($vipReportObj->report_type) {
										case '1':
											echo TEXT_TOTAL_PRODUCTION_PER_DAYS;
											break;
										case '2':
											echo TEXT_TOTAL_STOCK_LEFT_PER_DAYS;
											break;
										case '3':
											echo TEXT_TOTAL_PRODUCTION_PER_DAYS . '<hr noshade size="1">' . TEXT_TOTAL_REVENUE_PRODUCTION_PER_DAYS;
											break;
										case '4':
											echo TEXT_TOTAL_COMPLETED_ORDERS . '<hr noshade size="1">' . TEXT_TOTAL_REVENUE_COMPLETED_ORDERS;
											break;
									}
?>
								</b>
							</td>
<?
					foreach($total_summary as $summary_date => $total_arr){
						echo '<td class="reportRecords" align="center" nowrap="nowrap" width="100">'.($total_arr['quantity'] > 0 ? $total_arr['quantity'] : '&nbsp;');

						if ($total_arr['price']) {
							if ($vipReportObj->report_type < 4) {
								echo '<hr noshade size="1">' . $currencies->format($total_arr['price'], true, $_SESSION['currency']);
							} else {
								echo '<hr noshade size="1">' . $currencies->format($total_arr['price'], false, $_SESSION['currency']);
							}
						}
						echo '</td>';
					}
					echo '</tr>';
?>
						</table>
					</td>
					<td width="1">&nbsp;</td>
					<td align="right" width="1">&nbsp;</td>
				</tr>
				</table>
				</div>
				</td>
			</tr>
			<table>
			<td>
		</tr>
<?
   }
?>
						</tbody>
						</table>
		              	</td>
		            </tr>
            	</table>
              	</td>
            </tr>
        </table>
        </td>
	</tr>
</tbody>
</table>
<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>