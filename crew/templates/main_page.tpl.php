<?php
//$earth_hour = 'false';
//$curr_time = time();
//$event_start = isset($_GET['start_date']) ? $_GET['start_date'] : strtotime('2013-03-23 20:30:00');
//$event_end = isset($_GET['end_date']) ? $_GET['end_date'] : strtotime('2013-03-23 21:30:00');
//if ($curr_time > $event_start && $curr_time < $event_end) {
//    $earth_hour = 'true';
//}
//
//if ($earth_hour == 'true' && strstr($PHP_SELF, FILENAME_LOGIN_POPUP) === FALSE) {
//    tep_redirect('http://static.offgamers.com/offgamers_earth_hour.html');
//    exit;
//}
$contentFooter = '';
$enable_event_effect = 'xmas';  // 'xmas', 'cny', 'patrick'
$enable_event_snow = '';  // 'xmas', 'cny'
$enable_event_splash = '';  // 'xmas', 'cny'
$enable_mobile_bar = false;
$enable_cookie_law = true;
$is_mobile = 0;

if(MOBILE_DOWN_FOR_MAINTENANCE == 'false') {
    if (isset($_SESSION['is_mobile_device'])) {
        $is_mobile_device = $_SESSION['is_mobile_device'];
    } else {
        require_once(DIR_WS_CLASSES . FILENAME_USER_AGENT);
        $user_agent_obj = new user_agent();
        $is_mobile_device = $user_agent_obj->is_mobile() === TRUE ? 'true' : 'false';
        $_SESSION['is_mobile_device'] = $is_mobile_device;
    }

    if ($is_mobile_device == 'true') {
        $is_mobile = (isset($_GET['is_mobile']) && $_GET['is_mobile'] > 0) ? $_GET['is_mobile'] : (isset($_COOKIE['is_mobile']) ? $_COOKIE['is_mobile'] : 0);
        
        if ((int) $is_mobile > 0 || $is_mobile === 'true') {
            $enable_mobile_bar = $is_mobile == 2 ? false : true;
            $expire = time() + 86400;
            tep_setcookie('is_mobile', $is_mobile, $expire, $cookie_path, $cookie_domain);
            
            # Home page only
            if ($content === CONTENT_INDEX_DEFAULT) {
                if (isset($_GET['iwan'])) {
                    if ((int) $_GET['iwan'] === 2) {
                        $expire = time() - 604800;
                        tep_setcookie('iwan', 2, $expire, $cookie_path, $cookie_domain);

                        tep_redirect(tep_href_link('/v2'));
                    } else {
                        # desktop
                        $expire = time() + 604800;
                        tep_setcookie('iwan', 1, $expire, $cookie_path, $cookie_domain);

//                        tep_redirect(tep_href_link('/'));
                    }
                } else if (isset($_COOKIE['iwan']) && (int) $_COOKIE['iwan'] === 1) {
                    // stay
                } else {
                    tep_redirect(tep_href_link('/v2'));
                }
            }
        } else {
            if (in_array($content, array(CONTENT_INDEX_DEFAULT, CONTENT_INDEX_CDKEY_PRODUCTS, CONTENT_CUSTOM_PRODUCT_INFO))) {
                $path = '';

                switch ($content) {
                    case CONTENT_INDEX_CDKEY_PRODUCTS:
                    case CONTENT_CUSTOM_PRODUCT_INFO:
                        if (isset($page_info->tpl) && $page_info->tpl == 2) {
                            if (isset($_REQUEST['products_id']) && (int)$_REQUEST['products_id'] != 0) {
                                # product detail page
                                if ($product_url_alias = getProductsUrlAlias((int)$_REQUEST['products_id'])) {
                                    $path = '/buynow' . '/' . $product_url_alias . '.html';
                                }
                            } else if ($categories_url_alias = category::get_categories_url_alias($page_info->main_game_id)) {
                                # category page
                                if ($tag_key = getSinglePathTagKeyByGameID($page_info->main_game_id)) {
                                    $path = '/' . str_ireplace('_', '-', strtolower(implode('/', $tag_key))) . '/' . $categories_url_alias; // '/?page=product_listing&gm=' . $page_info->main_game_id;
                                } else {
                                    $path = '/' . $categories_url_alias;
                                }
                            }

                            tep_redirect(tep_href_link('/v2') . $path);
                        }
                        break;
                    case CONTENT_INDEX_DEFAULT:
                        if (isset($_GET['iwan'])) {
                            if ((int) $_GET['iwan'] === 2) {
                                $expire = time() - 604800;
                                tep_setcookie('iwan', 2, $expire, $cookie_path, $cookie_domain);

                                tep_redirect(tep_href_link('/v2'));
                            } else {
                                # desktop
                                $expire = time() + 604800;
                                tep_setcookie('iwan', 1, $expire, $cookie_path, $cookie_domain);

//                                tep_redirect(tep_href_link('/'));
                            }
                        } else if (isset($_COOKIE['iwan']) && (int) $_COOKIE['iwan'] === 1) {
                            // stay
                        } else {
                            tep_redirect(tep_href_link('/v2'));
                        }
                        break;
                    default:
                        tep_redirect(tep_href_link('/v2'));
                        break;
                }
            }
        }
    } else {
        # desktop version
        # Home page only
        if ($content === CONTENT_INDEX_DEFAULT) {
            if (isset($_GET['iwan'])) {
                if ((int) $_GET['iwan'] === 2) {
                    $expire = time() - 604800;
                    tep_setcookie('iwan', 2, $expire, $cookie_path, $cookie_domain);

                    tep_redirect(tep_href_link('/v2'));
                } else {
                    # desktop
                    $expire = time() + 604800;
                    tep_setcookie('iwan', 1, $expire, $cookie_path, $cookie_domain);

//                    tep_redirect(tep_href_link('/'));
                }
            } else if (isset($_COOKIE['iwan']) && (int) $_COOKIE['iwan'] === 1) {
                // stay
            } else {
                tep_redirect(tep_href_link('/v2'));
            }
        }
    }
}

if ($enable_event_effect) {
    $cache_key = 'event/' . $enable_event_effect . '/expiry_time';
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $enable_event_effect = $cache_result;
    } else {
        $curr_time = time();
        $event_start = strtotime('2014-12-15 12:00:00');
        $event_end = strtotime('2015-01-06 12:00:00');
        $cache_time = 1*60*60;//2592000;    // default cycle 30 days

        if ($curr_time < $event_start) {
            // future start time
            $enable_event_effect = '';
            $cache_time = 10 + $event_start - $curr_time;
        } else if ($curr_time < $event_end) {
            // within event period
            $cache_time = 10 + $event_end - $curr_time;
        } else {
            // event ended
            $enable_event_effect = '';
        }

        $memcache_obj->store($cache_key, $enable_event_effect, $cache_time);	// Cache for 30 days
    }
}

if (isset($_COOKIE['noticebar_cookie'])) {
    $enable_cookie_law = false;
} else {
    $cookies_expired = time() + 60 * 60 * 24 * 365; // 1 year expiry date;
    tep_setcookie('noticebar_cookie', '1', $cookies_expired, $cookie_path, $cookie_domain);
}

//Get current time
$mtime = microtime();
//Split seconds and microseconds
$mtime = explode(" ",$mtime);
//Create one value for start time
$mtime = $mtime[1] + $mtime[0];
//Write start time into a variable
$tstart = $mtime;

$shared_error_region_disabled = true;

$maintenance_message = '';
// give the visitors a message that the website will be down at ... time
if ( (WARN_BEFORE_DOWN_FOR_MAINTENANCE == 'true') && (DOWN_FOR_MAINTENANCE == 'false') ) {
	$maintenance_message = sprintf(TEXT_BEFORE_DOWN_FOR_MAINTENANCE, PERIOD_BEFORE_DOWN_FOR_MAINTENANCE, TEXT_MAINTENANCE_PERIOD_TIME);
}

require_once(DIR_WS_CLASSES . 'page.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);

$page_obj = new page();
$layout_style = $page_obj->get_layout_style();
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?=HTML_PARAMS?>>
<head>
	<META HTTP-EQUIV="Expires" CONTENT="Tue, 04 Dec 2005 21:29:02 GMT">
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET;?>">
	<? include_once(DIR_WS_INCLUDES . 'meta_tags.php'); ?>
	<title><?=META_TAG_TITLE?></title>
	<meta name="description" content="<?=META_TAG_DESCRIPTION?>">
	<meta name="keywords" content="<?=META_TAG_KEYWORDS?>">
	<meta name="msvalidate.01" content="896CB15AAEE62C55E3B4A7DAC578EBAB" />
    <meta name="alexaVerifyID" content="no0m1ug6IcdnfaHnTQL_tju4too" />
    <meta name="verify-webtopay" content="edfa059392ea7394f93d094b2a9e6c81" />
<?
	// 1 - Index, Follow
	// 2 - Index, Not Follow
	// 3 - No Index, Follow
	// 4 - No Index, Not Follow

if (META_TAG_ROBOTS == "1" || META_TAG_ROBOTS == "3") {
?>
	<meta name="ROBOTS" content="NOODP">
<?
} else if (META_TAG_ROBOTS == "2" || META_TAG_ROBOTS == "4") {
?>
	<meta name="robots" content="NOINDEX, NOFOLLOW">
<?
}
echo $ogm_fb_obj->get_FB_open_graph_tags();
?>
	<base href="<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG; ?>">
<?	if ($content == CONTENT_INDEX_DEFAULT) { echo '<meta name="verify-v1" content="s8lqdZNqoo+FZXPMVU5YRoR2SBFwyRV5Qs1nK+xVs4g=">' . "\n"; } ?>
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/font_awesome/css/font-awesome.min.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap-responsive.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap-formhelpers.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap-select.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>select2/select2.css" media="screen">
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/userBar.css" media="screen">
    
    <link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/infobox.css">
	<link rel="stylesheet" type="text/css" href="<?php echo DIR_WS_IMAGES . 'css/'.(isset($language_code) && tep_not_null($language_code) ? 'stylesheet_'.$language_code.'.css' : 'stylesheet_en.css'); ?>">
	<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/ogm_jquery.lightbox.css" media="screen">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="image_src" href="<?=DIR_WS_IMAGES?>offgamers_logo.jpg">
	<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/dhtmlTooltip.css">
	<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/jquery-ui.min.1.8.22.css">
    <link rel="stylesheet" href="<?=DIR_WS_IMAGES?>css/mainpage.css?20150130" type="text/css" media="screen" />
    
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>jquery.1.7.2.js"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>jquery-ui.min.1.8.22.js"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>general.js"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . FILENAME_JS_LAYOUT_OGM?>"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>ogm_jquery.js?20130924"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>ogm_jquery.lightbox.js"></script>

	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'layout_ogm2011.js?20140409'?>"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>jquery.megamenu.min.js"></script>
	<?
	if (isset($javascript) && tep_not_null($javascript) && file_exists(DIR_FS_JAVASCRIPT . basename($javascript))) {
		$js_path_parts = pathinfo($javascript);
		if (strtolower($js_path_parts["extension"]) == 'js') {
			echo '<script type="text/javascript" src="'.DIR_WS_JAVASCRIPT.basename($javascript).'"></script>';
		} else {
			include_once(DIR_FS_JAVASCRIPT . basename($javascript));
		}
	}
?>
	<script type="text/javascript" language="JavaScript">
		function MM_preloadImages() {	//v3.0
  			var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    		var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    		if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
		}

		function MM_swapImgRestore() { //v3.0
		  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
		}

		function MM_findObj(n, d) { //v4.01
		  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
		    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
		  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
		  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
		  if(!x && d.getElementById) x=d.getElementById(n); return x;
		}

		function MM_swapImage() { //v3.0
		  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
		   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
		}

        function clear_mobile_cookie() {
            jQuery.ajax({
                type: "get",
                url: 'customer_xmlhttp.php',
                data: {'action':'clear_mobile_cookie'},
                dataType: 'xml',
                success: function(xml) {
                    var result = jQuery(xml).find('response').text();
                    if (result == 'success') {
                        window.location.href="<?=tep_href_link(MOBILE_SITE_URL)?>";
                    }
                }
            });
        }
	</script>
	<!--[if IE]>
		<style type="text/css">
		.ie_fix {
			float: left;
		}
		</style>
	<![endif]-->
</head>
<body spellcheck="false" class="<?php echo $page_obj->get_body_classes($enable_event_effect); ?>">
<?php
    if (DOWN_FOR_MAINTENANCE != 'true') {
        $ServerSrciptName = $_SERVER['SCRIPT_NAME'];
        $ServerSrciptNameFirstChar = substr($ServerSrciptName, 0, 1);
        if($ServerSrciptNameFirstChar == "/"){
            $ServerSrciptName = substr($ServerSrciptName, 1);
        }
        $originUrl = urlencode(tep_href_link($ServerSrciptName, tep_get_all_get_params(array('country','currency','language'))));
?>
        <script type="text/javascript">
            var HTTP_ORIGIN_URL = "<?php echo $originUrl?>";
            var LOGIN_URL = "<?php echo $shasso_obj->getLoginURL()?>";
            var TEXT_ACCOUNT_LOGIN_HEADER = "<?php echo TEXT_ACCOUNT_LOGIN_HEADER?>";
        </script>
<?php
        include_once('_userBar.php');
    }

    if ($enable_mobile_bar) {
        echo '<div id="blackbar2"><a href="javascript:void(0)" onclick="clear_mobile_cookie()" style="line-height:100px;color:#FFF;font-size: 48px;text-decoration:none;" target="_self"><center>' . TEXT_SWITCH_TO_MOBILE . '</center></a></div>';
    }

    if ($enable_event_splash) {
        echo '<div id="preload-splash_' . $enable_event_effect . '_img"></div>';
    }
    
    if ($enable_cookie_law) {
?>
    <div id="noticeBar">
        <div class="container">
            <div class="notice"><?php echo TEXT_COOKIE_NOTICE_DESC; ?></div>
            <div class="hide-icon"></div>
        </div>
    </div>
<?
    }
?>
	<div id="blueBar">
		<div class="container lyr115">
			<div id="pageHeader"><? include_once('header.php'); ?></div>
		</div>
        <div class="icon_try_beta">
            <img src="<?=DIR_WS_IMAGES?>ogm_new_corner.png" usemap="#bac_to_old">
            <map name="bac_to_old" id="bac_to_old"><area style="outline:none;" shape="poly" coords="380,350,10,0,120,0" href="/?iwan=2"></map>
        </div>
	</div>
<?
	if (tep_not_null($maintenance_message)) {
		echo '<div id="yellowBar" class="lyr000">
				<div class="container lyr100">
					<div style="padding: 15px">
					<table border="0" cellspacing="0" cellpadding="2">
						<tr>
							<td width="1%" valign="top">'.tep_image(DIR_WS_ICONS . 'warning_large.gif', '', 29, 29).'</td>
							<td width="100%">
								<span style="font-size: 20px; font-weight: bold; color:red; font-family: Arial, Verdana, sans-serif;">
								'.$maintenance_message.'
								</span>
							</td>
						</tr>
					</table>
					</div>
				</div>
			</div>';
	}
?>
	<div id="blackBar" class="lyr000 <?=$page_obj->blk_bar_flag?>"></div>
	<div id="globalContainer" class="useBlk lyr100">
		<div id="content">
<?
	//if ($_SERVER['REQUEST_URI'] != '/') {
	//	echo '<div class="breakLine"><!-- --></div>';
	//	echo '<div>' . $breadcrumb->trail(' > ') . '</div>';
	//}

	/*-- GA :: Classify Your Site Visitor [START] --*/
	if (tep_not_null($customers_groups_id)) {
		if (!isset($_SESSION['GA_CUSTOMERS_GROUPS_ID']) || (isset($_SESSION['GA_CUSTOMERS_GROUPS_ID']) && $_SESSION['GA_CUSTOMERS_GROUPS_ID'] != $customers_groups_id)) {
			$_SESSION['GA_CUSTOMERS_GROUPS_ID'] = $customers_groups_id;
			$_SESSION['GA_CUSTOMERS_GROUPS_NAME'] = tep_get_customers_groups_name($customers_groups_id);
		}
	}
	/*-- GA :: Classify Your Site Visitor [END] --*/

	if ($layout_style == "3column") {
?>
			<div class="bodyContentLeft"><? include_once(DIR_WS_INCLUDES . 'column_left.php'); ?></div>
			<div class="bodyContentCenter">
				<div class="bodyContentCenterLeft"><? include_once($page_obj->get_template_file()); ?></div>
				<div class="bodyContentCenterRight"><? include_once(DIR_WS_INCLUDES . 'column_right.php'); ?></div>
				<div style="clear:both"></div>
			</div>
			<div style="clear: both;"></div>
<?
	} else if ($layout_style == "1column") {
		include_once($page_obj->get_template_file());
	} else if ($layout_style == "onlyleftcolumn") {
?>
			<div class="vspacing"></div>
			<div class="bodyContentLeft"><? include_once(DIR_WS_INCLUDES . 'column_left.php'); ?></div>
			<div class="bodyContentCenter"><? include_once($page_obj->get_template_file()); ?></div>
			<div style="clear: both;"></div>
<?
	} else if ($layout_style == "onlyrightcolumn") {
?>
			<div class="bodyContentCenterLeftWide"><? include_once($page_obj->get_template_file()); ?></div>
			<div class="bodyContentCenterRight"><? include_once(DIR_WS_INCLUDES . 'column_right.php'); ?></div>
			<div style="clear:both"></div>
<?
	}
?>
		</div>
		<div id="contentFooter"><?php echo $contentFooter; ?></div>
	</div>
	<div id="pageFooter">
		<div id="footerTop"><div id="footerTopContainer"><? include_once(DIR_WS_INCLUDES . 'footer.php'); ?></div></div>
		<div id="footerBtm">
			<div id="footerBtmContainer">
				<div class="vspacing"></div>
                <div class="description"><?php echo TEXT_COPYRIGHT_INFORMATION; ?></div>
                <div class="img_container">
                    <div class="img"></div>
                </div>
			</div>
		</div>
	</div>
<?
if (DOWN_FOR_MAINTENANCE != 'true') {
	//include_once(DIR_WS_INCLUDES . 'footer_bar.php');
}
?>
<div id="OGM_hiddenContainer">
	<? include_once(DIR_WS_INCLUDES . 'hidden_content.php'); ?>
	<div id="fancy_box" class="fancy_box" style="display:none;">
		<div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
		<div class="fancy_inner" style="display: block;">
			<div id="fancy_close" class="fancy_close" style="display: none;"></div>
			<div class="fancy_frame_bg">
				<div class="fancy_bg fancy_bg_n"></div>
				<div class="fancy_bg fancy_bg_ne"></div>
				<div class="fancy_bg fancy_bg_e"></div>
				<div class="fancy_bg fancy_bg_se"></div>
				<div class="fancy_bg fancy_bg_s"></div>
				<div class="fancy_bg fancy_bg_sw"></div>
				<div class="fancy_bg fancy_bg_w"></div>
				<div class="fancy_bg fancy_bg_nw"></div>
			</div>
			<div id="fancy_content" class="fancy_content"></div>
		</div>
	</div>
	<div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>
</div>
<!-- FB API required codes - I have to be at here before load the FB JS SDK -->
<div id="fb-root"></div>
<!-- FB API required codes -->
<script language="javascript">
	<!--
	var config = {	reset_password: "<?php echo (int)$customers_upkeep_obj->getUpkeepValue('reset_password')?>",
                reset_phone: "<?php echo isset($_SESSION['RESET_PHONE_NO']) ? $_SESSION['RESET_PHONE_NO'] : '0'?>",
                cookies_enabled: "<?php echo $cookies_started ? '1' : '0'; ?>",
				defaultCountry: "<?=$country?>",
				special_notice_cookies_enabled: "<?php echo $special_notice_cookies_started ? '1' : '0'; ?>",
                splash_enabled: "<?php echo !empty($enable_event_splash) && !isset($_COOKIE['ogm_se']) ? $enable_event_splash : 0 ?>",
				language_code: "<?php echo $language_code; ?>",
				generateRegionalBox_ajaj_url: "<?php echo tep_href_link(FILENAME_PRESTART_JSON, tep_get_all_get_params()); ?>",
				generateRegionalBox_ajax_url: "<?php echo tep_href_link(FILENAME_PRESTART_XMLHTTP, tep_get_all_get_params()); ?>",
				regionalFormAction: "<?php echo tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('loc_country', 'currency', 'language'))); ?>",
				loginFormAction: "<?php echo tep_href_link(FILENAME_LOGIN); ?>",
				actionSave: "<?php echo TEXT_SAVE; ?>",
				actionForgotPassword: "<b><?php echo LOGIN_BOX_PASSWORD_FORGOTTEN; ?></b>",
				actionContinue: "<?php echo IMAGE_BUTTON_CONTINUE; ?>",
				loadingText: "<?php echo TEXT_IS_LOADING; ?>",
				regionalTitle: "<?php echo BOX_HEADING_REGIONAL_SETTING; ?>",
				regionalNote: "<?php echo TEXT_REGIONAL_NOTE; ?>",
				loginTitle: "<?php echo LOGIN_BOX_HEADING; ?>",
				entryCountry: "<?php echo TEXT_REGIONAL_COUNTRY; ?>",
				entryCurrency: "<?php echo TEXT_CURRENCY.':'; ?>",
				entryLanguage: "<?php echo TEXT_LANGUAGE.':'; ?>",
				entryEmail: "<?php echo ENTRY_EMAIL_ADDRESS; ?>",
				entryPassword: "<?php echo ENTRY_PASSWORD; ?>",
				loginSelection: "<?php echo LOGIN_BOX_LOGIN_SELECTION; ?>",
				newCustomerSelection: "<?php echo LOGIN_BOX_CREATE_ACCOUNT_SELECTION; ?>",
				forgotPasswordSelection: "<?php echo LOGIN_BOX_SEND_PASSWORD_SELECTION; ?>"
			};
	<?=$ogm_fb_obj->get_FB_js_controller()?>
	//-->
</script>
<script language="JavaScript">
	function MM_preloadImages() {	//v3.0
		var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
		var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
		if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
	}
</script>
<?php
if ($ogm_fb_obj->FB_connect_switch) {
    include_once(DIR_WS_INCLUDES . 'google_tag_manager.php');
    echo '<script src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>';
}
?>
<div id="dhtmlTooltip"></div>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>userBar.js"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap.js"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>bootstrap/2.3.2/bootstrap-formhelpers-selectbox.js"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>select2/select2.js"></script>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>dhtml_tooltip.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>tipTip/jquery.tipTip.minified.js"></script>
<link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>tipTip/tipTip.css">
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT.FILENAME_JS_JQUERY_IEPNGHACK?>"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>php.packed.js"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>ogm_fb_connect.js?********"></script>
<?	if ($content == CONTENT_CHECKOUT_SUCCESS && tep_not_null(OVERTURE_ACCOUNT_ID)) { include_once (DIR_FS_JAVASCRIPT . 'overture_conversion.js'); } ?>
<?	if ($content == CONTENT_CHECKOUT_SUCCESS && file_exists(DIR_FS_JAVASCRIPT . 'yahoo_search_marketing.js.php')) { include_once (DIR_FS_JAVASCRIPT . 'yahoo_search_marketing.js.php'); } ?>
<?=$ogm_fb_obj->get_KT_tracking_script();?>

<!-- Start Alexa Certify Javascript -->
<script type="text/javascript" src="https://d31qbv1cthcecs.cloudfront.net/atrk.js"></script><script type="text/javascript">_atrk_opts = { atrk_acct: "RdgZf1aYS5000V", domain:"offgamers.com"}; atrk ();</script><noscript><img src="https://d5nxst8fruw4z.cloudfront.net/atrk.gif?account=RdgZf1aYS5000V" style="display:none" height="1" width="1" alt="" /></noscript>
<!-- End Alexa Certify Javascript -->
<!-- <?php echo (isset($_SESSION['customer_id']) ? $_SESSION['customer_id'] : ''); ?> -->
</body>
</html>