<?
	
	//if($_POST['val']==1){
		//require_once('includes/application_top.php');
		//tep_redirect('index.php?theme='.$theme.'&cPath='.$theme.'');	
		//header("Location: ".'index.php?theme='.$theme.'&cPath='.$theme);
		//header("Location: ".'index.php?theme=16&cPath=16');
		
		//<INPUT type="hidden" name="theme_hid" value="<?=$theme">
	//}
?>
<script>
	window.location.href=("index.php?theme=16&cPath=16");
</script>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Select games</title>
<script>
function redirect(a)
{
	window.location.href=("index.php?theme="+a+"&cPath=" + a);
	return;	
	
}
</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0">
<!--table align="center" width="600" border="0"  cellpadding="0" cellspacing="0">
	  <tr><td>
	  <form name="sel_theme">
	  <select name="theme">
		<option value="">Choose your games</option>
		<option value="16">D2</option>
		<option value="17">L2</option>
		<option value="18">RO</option>
	  </select>
	  <INPUT type="button" value="Go" onclick="redirect(document.sel_theme.theme.value);">
	  </form>
	  </td></tr>
</table-->


</body>
</html>