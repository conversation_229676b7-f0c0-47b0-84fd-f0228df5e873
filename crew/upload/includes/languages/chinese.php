<?
/*
  	$Id: english.php,v 1.14 2006/06/20 10:10:29 chan Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
	// look in your $PATH_LOCALE/locale directory for available locales
	// or type locale -a on the server.
	// Examples:
	// on RedHat try 'en_US'
	// on FreeBSD try 'en_US.ISO_8859-1'
	// on Windows try 'en', or 'English'

	@setlocale(LC_TIME, 'en_US.ISO_8859-1');
//	define('KEY_SP_LANG', "sp_lang");//Moved to application top
	define('KEY_SP_TIME_ZONE', "sp_time_zone");
	define('KEY_SPS_RSTK_CHAR_SET', "rstk_char_set");

	// charset for web pages and emails
	//define('CHARSET', 'gb2312');
	define('CHARSET', 'utf-8');
	define('EMAIL_CHARSET', 'gb2312');
	
	define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
	define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // this is used for strftime()
	define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
	define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');
	
	define('LOCAL_STORE_NAME', '卧虎游戏');
	define('LOCAL_STORE_SLOGAN', '您的游戏联盟');
 
	define('EMAIL_LOCAL_STORE_EMAIL', '<EMAIL>');
	define('LOCAL_STORE_EMAIL_SIGNATURE', '顺祝商祺'."\n\n".'卧虎游戏团队 启'."\n\n".'*************************************************'."\n".LOCAL_STORE_NAME.' - 您的游戏联盟'."\n".'*************************************************'."\n".'电邮: <a href="mailto:'.EMAIL_LOCAL_STORE_EMAIL.'">' . EMAIL_LOCAL_STORE_EMAIL.'</a>');

	
	// Global entries for the <html> tag
	define('HTML_PARAMS','dir="LTR" lang="en"');

	define('BUTTON_MIN_CHAR_LENGTH', 11);

	// page title
	define('TITLE', STORE_NAME);

	// if USE_DEFAULT_LANGUAGE_CURRENCY is true, use the following currency, instead of the applications default currency (used when changing language)
	define('LANGUAGE_CURRENCY', 'USD');

    define('HEADER_TITLE_TOP', '网站主页');
    define('HEADER_TITLE_ABOUT_US', '关于我们');
    define('HEADER_TITLE_CONTACT_US', '联络我们');
    define('HEADER_TITLE_DATE', '卧虎正式时间');
    define('HEADER_TITLE_FAQ', '常见问题');
    define('HEADER_TITLE_FORUM', '论坛');
    define('HEADER_TITLE_LOGIN', '我的帐户');
    define('HEADER_TITLE_PARTNERSHIP', '合作伙伴');
    define('HEADER_TITLE_POLL', '投票站');
    define('HEADER_TITLE_ORDER_FLOW_INFO', '下单提款流程图');
    define('HEADER_TITLE_MY_ACCOUNT', '我的帐户');
    define('HEADER_TITLE_MY_ACCOUNT_MGMT', '帐号管理');
    define('HEADER_TITLE_MY_ORDER_HISTORY', '单子状态');
    define('HEADER_TITLE_MY_FAVOURITE_LINKS', '快捷下单');
    define('HEADER_TITLE_MY_PAYMENT_HISTORY', '款项记录');
    define('HEADER_TITLE_MY_CREDIT_BALANCE', '结存余额 ');
    define('HEADER_TITLE_BUYBACK', '我要卖');
    define('HEADER_TITLE_CATALOG', '我要买');
    define('HEADER_TITLE_TRADE', '我要交易');
    define('HEADER_TITLE_CATEGORIES', '选择游戏:');
    define('HEADER_TITLE_WELCOME', '欢迎');
    define('HEADER_TITLE_SIGNUP', '注册');
    define('HEADER_TITLE_FORGOT_PASSWORD', '忘记密码?');
    define('HEADER_TITLE_ACTIVATE_ACCOUNT', '激活帐号');
	define('HEADER_TITLE_SIGN_OFF', '登出');
	define('HEADER_TITLE_HELP_DESK', '服务台');
	define('TABLE_HEADING_ACTION', '运作');
	define('HEADER_TITLE_WEBSITE_NOTE', '网站声明');
	define('HEADER_TITLE_MY_VIP_ACCOUNT', 'VIP帐户');
	define('HEADER_TITLE_MY_VIP_INVENTORY_UPDATE', '库存批量管理列表');
	define('HEADER_TITLE_MY_VIP_REGISTER_SERVER', '登记库存列表');
	define('HEADER_TITLE_MY_VIP_ORDERS_HISTORY', '订单操作');
	define('HEADER_TITLE_MY_VIP_REPORT', '销售报告');
	
	define('ENTRY_EMAIL_ADDRESS', '信箱邮址');
	define('ENTRY_PASSWORD', '密码');
	define('TEXT_LINK_SECURE_LOGIN', '安全方式登录');

	define('TEXT_SECURITY_CODE', '验证码');
	define('TEXT_REMEMBER_ME', '记住我的帐号');
	define('TEXT_LATEST_NEWS', '最新新闻');
	define('TEXT_NOTICE_BOARD', '布告栏');
	define('TEXT_GAME_INFO', '游戏资讯');
	define('TEXT_TIPS', 'Tips');
	define('TEXT_POLL_QUESTION', '评估我们的服务.');
	define('TEXT_ACCOUNT_FREEZE', '抱歉，您的帐号已被封锁，请联系网站客服谢谢');
	define('TEXT_PIN_NUMBER', 'PIN 号码');
	define('TEXT_PINNUMBER_REQUIRED', '需要PIN 号码才能更新 : %s');
	
	define('TEXT_ACCOUNT_TOTAL', '账号总额');
	define('TEXT_ACTIVE', '正常');
	define('TEXT_BACK', '返回');
	define('TEXT_CONFIRM', '确定');
	define('TEXT_FINISHED', '完成，请现在新建我的账号.');
	define('TEXT_INACTIVE', '已满');
	define('TEXT_LIST_CLOSE', '暂停');
	define('TEXT_MAIN_PLEASE_SELECT_GAME', '《---请选择游戏，查询报价 ！');
	define('TEXT_MESSAGE_LIST_CLOSE', '已提交订单已饱和，请您在倒数到0后再行提交。（现在倒数：<b>%d</b>）');
	define('TEXT_MESSAGE_BREAK_TIME', '卧虎网站暂停交易，服务器将在倒数时间到来时重新启动，给您带来不便敬请谅解 （倒数时间：%02d小时%02d分钟）');
	define('TEXT_WEBSITE_NOTE_DESC', '<ol><li>游戏提示您：与本公司交易咨询时，请认准网站公布的采购QQ，避免有人冒充卧虎游戏采购客服进而给您带来不必要的损失。<li>供应商注册卧虎游戏帐号时会设定相应的密保问答（Q&A），这是我们提供相应安全服务的最后保障，请妥善保管并牢记答案；如因密保问答（Q&A）丢失或泄漏导致您帐号资金和信息损失，卧虎公司概不负责，请千万注意。<li>卧虎游戏针对游戏服务器暂时维护（重启）时的交易金币回流现象，提醒用户务必在维护（重启）后交易，如因用户不遵守此规定造成的交易损失，卧虎游戏概不负责。<li>卧虎游戏投诉与意见信箱：<a href="mailto:<EMAIL>?subject=卧虎游戏投诉或意见信箱"><span class="redIndicator"><EMAIL></span></a> 接到投诉后12小时内回复解决意见</li></ol>');
	define('TEXT_OPTIONAL', '(可不填)');
	define('TEXT_VIP_ORDER_FLOW', '流程说明');
	
	define('TEXT_NEXT', '下一页');
	define('TEXT_SAVE', '储存');
	define('TEXT_SEARCH', '寻找');
	define('TEXT_SUBMIT', '提交');
	define('TEXT_UPDATE', '更新');
	define('TEXT_UPDATE_AND_NEXT', '更新 >>');
	define('TEXT_VIEW', '查看');
	define('TEXT_WITHDRAW', '提款');
	
	define('LINK_MAIN_MAKE_ORDER_NOW', '我要下单');
	
	define('IMAGE_BUTTON_LOGIN', '登入');
	define('IMAGE_BUTTON_CONTINUE', '继续');

	define('ICON_ERROR', '错误');
	define('ICON_SUCCESS', '成功');
	define('ICON_WARNING', '警告');
	
	define('BUTTON_ADD', '添加');
	define('BUTTON_ADD_PM', '添加收款账户');
	define('BUTTON_AGREE', '同意');
	define('BUTTON_BACK', '返回');
 
	define('BUTTON_BATCH_UPDATE', '集体更新');
	define('BUTTON_CANCEL', '取消');
	define('BUTTON_CONFIRM', '确定');
	define('BUTTON_CONTINUE', '继续');
	define('BUTTON_DELETE', '删除');
	define('BUTTON_DISAGREE', '不同意');
	define('BUTTON_EDIT', '更改');
 
	define('BUTTON_EXPORT', '输出');
	define('BUTTON_EXPORT_TEMPLATE', '输出模板');
	define('BUTTON_IMPORT', '输入');
	define('BUTTON_PRESALE_NOTICE', '加入开收通知');
	define('BUTTON_PRESALE_REMOVE', '删除开收通知');
	define('BUTTON_RESET', '重设');
	define('BUTTON_REFRESH', '刷新');
	define('BUTTON_REPORT', '报告');
	define('BUTTON_SEARCH', '寻找');
	define('BUTTON_SHOW_ALL_PRODUCTS', '显示全部服务器');
	define('BUTTON_SHOW_SELLING_PRODUCTS', '显示销售服务器');
	define('BUTTON_SUBMIT', '提交');
	define('BUTTON_SIGN_IN', '登入');
	define('BUTTON_SIGN_UP', '注册');
	define('BUTTON_SIGN_OUT', '登出');

	define('ALT_BUTTON_ADD', '添加');
	define('ALT_BUTTON_ADD_PM', '添加收款账户');
	define('ALT_BUTTON_AGREE', '同意');
 
	define('ALT_BUTTON_BATCH_UPDATE', '集体更新');
	define('ALT_BUTTON_BACK', '返回之前一页');
	define('ALT_BUTTON_CANCEL', '取消');
	define('ALT_BUTTON_CONFIRM', '确定');
	define('ALT_BUTTON_CONTINUE', '继续');
	define('ALT_BUTTON_DELETE', '删除');
	define('ALT_BUTTON_DISAGREE', '不同意');
	define('ALT_BUTTON_EDIT', '更改');
 
	define('ALT_BUTTON_EXPORT', '输出');
	define('ALT_BUTTON_IMPORT', '输入');
	define('ALT_BUTTON_PRESALE_NOTICE', '加入开收通知');
	define('ALT_BUTTON_PRESALE_REMOVE', '删除开收通知');
	define('ALT_BUTTON_REFRESH', '刷新');
	define('ALT_BUTTON_RESET', '重设');
	define('ALT_BUTTON_SEARCH', '寻找');
	define('ALT_BUTTON_SHOW_ALL_PRODUCTS', '点击这里显示全服务器');
	define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', '点击这里只显示您销售的服务器');
	define('ALT_BUTTON_SUBMIT', '提交表格');
	define('ALT_BUTTON_SIGN_IN', '登入');
	define('ALT_BUTTON_SIGN_UP', '注册');
	define('ALT_BUTTON_SIGN_OUT', '登出');
	
    define('TEXT_WELCOME', '欢迎 %s 来到卧虎采购网!');
	define('TEXT_REFRESH_WHEN_CHANGE', '(任何更换将刷新网页)');
	define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="fieldRequired">* </span>');
	define('TEXT_ERRORS_FOUND', '请再继续操作前更新以下错误');

    define('TEXT_MONTH', '月');
    define('TEXT_DAY', '日');
    define('TEXT_YEAR', '年');

    define('TEXT_MONTH_1', '一月');
    define('TEXT_MONTH_2', '二月');
    define('TEXT_MONTH_3', '三月');
    define('TEXT_MONTH_4', '四月');
    define('TEXT_MONTH_5', '五月');
    define('TEXT_MONTH_6', '六月');
    define('TEXT_MONTH_7', '七月');
    define('TEXT_MONTH_8', '八月');
    define('TEXT_MONTH_9', '九月');
    define('TEXT_MONTH_10', '十月');
    define('TEXT_MONTH_11', '十一月');
    define('TEXT_MONTH_12', '十二月');
	
	define('TEXT_CLOSED_DOWN', '关闭');
	define('TEXT_PAYMENT_ACCOUNT_NAME', '账号别名（例如：工商-上海）');
	define('TEXT_PRIMARY_ACCOUNT', '首要账号');

	define('TITLE_TRANS_PAYMENT', '款项编号 %s');
	define('TITLE_TRANS_SUPPLIER_ORDER', '供应商单子 %s');
	define('TITLE_TRANS_PWL_ORDER', '代练单子 %s');
	define('TITLE_TRANS_BUYBACK_ORDER', '单子编号 %s');
	define('TEXT_DISPLAY_NUMBER_OF_RECORDS', '显示<b>%d</b> to <b>%d</b> (of <b>%d</b> 记录)');
	define('TEXT_RESULT_PAGE', 'Page %s of %s');
	
	define('TEXT_HELP_PIN_NUMBER', '请准备您的PIN号码');
	
	//Common fields
	define('TEXT_SERVER', '服务器');
	define('TEXT_GAME', '游戏');
	define('TEXT_QUANTITY', '数量'); //units
	define('TEXT_AMOUNT', '总额'); //$$$
	define('TEXT_AMOUNT_WITH_CURRENCY', '总额'); //$$$
	define('TEXT_STATUS', '状态');
	define('TEXT_ACTION', '运作');
	define('TEXT_ORDER_NO', '单子 #');
	define('TEXT_ORDER_STATUS', '单子状态');
	define('TEXT_PAYMENT_STATUS', '款项状态');
	define('TEXT_START_DATE', '起始日期');
	define('TEXT_END_DATE', '终结日期');
  
	define('TEXT_LOADING_MESSAGE', '正在加载...');
	define('TEXT_WEBSITE_TIPS', '免责声明');
	
	define('PULL_DOWN_DEFAULT', '请选择');

	define('ENTRY_COUNTRY_TEXT', '*');
 
	define('ENTRY_QUESTION', '问题');
	define('ENTRY_ANSWER', '答案');


	define('ENTRY_PASSWORD_ERROR', '您的密码必须最少有 ' . ENTRY_PASSWORD_MIN_LENGTH . ' 字元.');
 
	define('TEXT_CURRENT_PASSWORD', '目前的密码');

	define('TEXT_UNKNOWN_ERROR', '发现错误，请再尝试');
	define('TEXT_MUST_LOGIN', '需要登入才被允许');

	define('TEXT_ALL_PAGES', '所有页');
	
	define('TEXT_TOP', '上面');
	
	define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE', '<p align="center">密保模式问答设定的通知</p><p>尊敬的供货商：</p><p>&nbsp;&nbsp;&nbsp;&nbsp;卧虎游戏现升级账户安全系统，将原有PIN码模式转变为更加方便管理和安全可靠的<span class="redIndicator">密保模式Q&A</span>（安全问题+自设回答），您可通过登录帐户用PIN码在<span class="redIndicator">帐号管理</span>中设定您的密保问答，今后密保问答将取代原有的PIN码的安全保障的作用，您从现在登录开始还有<span class="redIndicator">15天</span>的时间做相应的设定，请及时完成相关的设定；<span class="redIndicator">15天</span>后如果还是没有设定密保问答，系统将自动锁定您的帐户导致您的不便。关于密保问答的修改和使用方法以及忘记后的补发程序，请参考网页上的相关资讯或登录客服平台，由客服人员给您做相关解答。</p><p>&nbsp;&nbsp;&nbsp;&nbsp;卧虎游戏感谢您为账户安全所做的一切，谢谢。</p><br><p align="right">卧虎游戏团队启</p><br><p><a href="%s" class="subModelLink">现在就去设定密保模式</a><br><a href="javascript:;" %s class="subModelLink">稍后再说</a><br>目前剩余时间：<span class="redIndicator">%d天</span></p>');
	define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE', '密保模式问答设定的通知');
	
	define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', 'Warning: The downloadable products directory does not exist: ' . DIR_FS_DOWNLOAD . '. Downloadable products will not work until this directory is valid.');
	define('WARNING_NO_FILE_UPLOADED', '警告: 没文件上传。'); 
	define('WARNING_MUST_BE_VIP_MEMBER', '警告: 你必须是VIP用户才能享有这个功能'); 

	define('PREVNEXT_BUTTON_PREV', '&lt;&lt;');
	define('PREVNEXT_BUTTON_NEXT', '&gt;&gt;');
	
	define('TEXT_AUTO_REFRESH', '自动刷新');
	
	define('JS_NO_GAME_SELECTED', '请选择游戏');
 	define('JS_ERROR_SUBMITTED', '您的表格已提交了。 请按 Ok 和稍等片刻就完成了。');
	define('JS_ERROR', '您的表格在处理中发现错误！\n请修改：\n\n');
		
	define('ERROR_EMAIL_ADDRESS_INVALID', '错误：电子邮件地址无效！');
	define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', '这个电子邮件地址已存在！');
	
	define('ERROR_MSN_ADDRESS_INVALID', '错误： MSN地址无效！');
	define('ERROR_YAHOO_ADDRESS_INVALID', '错误： YAHOO地址无效！'); 
	
	define('FOOTER_TEXT_LOGIN_FROM', '您登陆的IP是%s');
	
	define('EMAIL_SEPARATOR', '------------------------------------------------------');
	define('EMAIL_SUBJECT_BUYBACK', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Buyback Order Update #%s")));
	define('EMAIL_TEXT_BODY_CUSTOMER','您的单子已被更新至以下状态。
	
	供应商单子总结:
	'.EMAIL_SEPARATOR."\n");
	define('EMAIL_TEXT_ORDER_NUMBER', '单子编号: ');
	define('EMAIL_TEXT_DATE_ORDERED', '单子日期: ');
	define('EMAIL_TEXT_STATUS_UPDATE', "状态: %s");
	define('EMAIL_TEXT_BUYBACK_ORDER_CANCELLED', "您逾期的单子已经被删除。");
	define('EMAIL_FOOTER', "\n\n".LOCAL_STORE_EMAIL_SIGNATURE);
	
	//customer order lock
	define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
	define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
	define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
 

	//admin log (log table)
	define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
	define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');
	
	//customer order comment remark
	define('TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via VIP Order: ##BO##%d##');
	define('TEXT_VIP_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'The following items have been delivered:');
	
	
	define('LOCAL_ANNOUNCEMENTS', '即时新闻');
	define('LOCAL_PROMOTIONS', '网站动态');
	define('LOCAL_NEWS', '同业新闻');
	define('LOCAL_GAME_INFO', '网游资讯');
	define('LOCAL_LATEST_NEWS_DETAILS', '[详细新闻]');
	
	define('LOCAL_META_TAG_TITLE', '卧虎游戏 － 中国专业采购、销售和交易欧美网络游戏网');
	define('LOCAL_META_TAG_DESCRIPTION', '卧虎游戏是全球专业游戏交易商: 是欧美网络游戏最大销售商: 魔兽世界,天堂2,行会战争,黑客帝国,最终幻想,星球大战,无尽任务,World of Warcraft Gold, Lineage 2 Adena, EverQuest Platinum, EQ2 Plat, SWG credits,MXO info,guild wars gold,ffxi gil,eve isk and more！');
	define('LOCAL_META_TAG_KEYWORDS', 'cn.offgamers.com, 卧虎游戏, 魔兽世界,天堂2,行会战争,黑客帝国,World of warcraft gold, WoW gold,  Lineage 2 Adena, EverQuest Platinum, EQ2 Plat,魔兽金币,wow gold,天堂币,游戏交易,网络游戏交易,网游交易,装备交易,帐号交易,qq币,游戏交易平台,游戏交易网,虚拟币交易');
	
	define('DISPLAY_NAME_CAT_ID_194', '魔兽世界（美服）');
	define('DISPLAY_NAME_CAT_ID_195', '魔兽世界（欧服）');
	define('DISPLAY_NAME_CAT_ID_1802', '混乱在线');
	define('DISPLAY_NAME_CAT_ID_1877', '霸王');
	define('DISPLAY_NAME_CAT_ID_1684', '末日战车');
	define('DISPLAY_NAME_CAT_ID_1964', '惊天动地');
	define('DISPLAY_NAME_CAT_ID_1735', '英雄城市');
	define('DISPLAY_NAME_CAT_ID_1695', '恶棍城市');
	define('DISPLAY_NAME_CAT_ID_1454', '龙与地下城');
	define('DISPLAY_NAME_CAT_ID_1496', '无尽任务II');
	define('DISPLAY_NAME_CAT_ID_1809', '最终幻想XI');
	define('DISPLAY_NAME_CAT_ID_1401', '行会战争');
	define('DISPLAY_NAME_CAT_ID_1834', '英雄');
	define('DISPLAY_NAME_CAT_ID_1773', '天堂II (美服）');
	define('DISPLAY_NAME_CAT_ID_1807', '冒险岛');
	define('DISPLAY_NAME_CAT_ID_1736', '丝路');
	define('DISPLAY_NAME_CAT_ID_1982', '暗黑之门-伦敦');
	define('DISPLAY_NAME_CAT_ID_2060', '英雄诗歌');
	define('DISPLAY_NAME_CAT_ID_2172', '魔戒指环王（美服）');
	define('DISPLAY_NAME_CAT_ID_2208', '魔戒指环王（欧服）');
	define('DISPLAY_NAME_CAT_ID_2843', '双月');
	define('DISPLAY_NAME_CAT_ID_2919', '天堂II (东南亚服务器）');
	define('DISPLAY_NAME_CAT_ID_2217', '热血江湖');
	define('DISPLAY_NAME_CAT_ID_2842', '九龙争霸');
	define('DISPLAY_NAME_CAT_ID_2844', '第二人生');
	define('DISPLAY_NAME_CAT_ID_2845', '卓越之剑');
	define('DISPLAY_NAME_CAT_ID_2921', '十二之天');
	define('DISPLAY_NAME_CAT_ID_3050', '光之国度（美服）');
	define('DISPLAY_NAME_CAT_ID_3051', '海盗王（美服）');
	define('DISPLAY_NAME_CAT_ID_3081', '盖亚在线');
	define('DISPLAY_NAME_CAT_ID_3082', '骑士在线');
	define('DISPLAY_NAME_CAT_ID_3083', '刀');
	define('DISPLAY_NAME_CAT_ID_3263', '科南时代（美服）');
	define('DISPLAY_NAME_CAT_ID_2235', '科南时代（欧服）');
	define('DISPLAY_NAME_CAT_ID_3248', '天堂II (欧服）');
	define('DISPLAY_NAME_CAT_ID_1979', '战锤（欧服）');
	define('DISPLAY_NAME_CAT_ID_3425', '战锤（美服）');
	define('DISPLAY_NAME_CAT_ID_3406', '洛奇');
	define('DISPLAY_NAME_CAT_ID_3808', '希望');
	define('DISPLAY_NAME_CAT_ID_3818', '蒸汽幻想');
	define('DISPLAY_NAME_CAT_ID_3816', '安特罗皮亚世界');
	define('DISPLAY_NAME_CAT_ID_3832', '倚天2 EU');
	define('DISPLAY_NAME_CAT_ID_3844', '倚天2 US');
		
	// Down For Maintenance
	define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', '注意：将于%s启动%s维护时间');
	define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', '注意：现在网站已经正在维护当中');
	
	//payment pop up menu
 
	define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', '付款消息');
 
	define('POP_PAYMENT_REPORT_DATETIME', '日期/时间');

	//state
	$COUNTRY_STATE_CHINESE_ARRAY = array('1394' => '安徽', //Anhui
                                    	 '1395' => '北京', //Beijing
                                     	 '1396' => '重庆', //Chongqing
                                    	 '1397' => '福建', //Fujian
                                    	 '1398' => '甘肃', //Gansu
                                    	 '1399' => '广东', //Guangdong
                                    	 '1400' => '广西', //Guangxi
                                    	 '1401' => '贵州', //Guizhou
                                    	 '1402' => '海南', //Hainan
                                    	 '1403' => '河北', //Hebei
                                    	 '1404' => '黑龙江', //Heilongjiang
                                    	 '1405' => '河南', //Henan
                                    	 '1406' => '香港', //Hong Kong
                                    	 '1407' => '湖北', //Hubei
                                    	 '1408' => '湖南', //Hunan
                                    	 '1409' => '内蒙古', //Inner Mongolia
                                    	 '1410' => '江苏', //Jiangsu
                                    	 '1411' => '江西', //Jiangxi
                                    	 '1412' => '吉林', //Jilin
                                    	 '1413' => '辽宁', //Liaoning
                                    	 '1414' => '澳门', //Macau
                                    	 '1415' => '宁夏', //Ningxia
                                    	 '1416' => '陕西', //Shaanxi
                                    	 '1417' => '山东', //Shandong
                                    	 '1418' => '上海', //Shanghai
                                    	 '1419' => '山西', //Shanxi
                                    	 '1420' => '四川', //Sichuan
                                    	 '1421' => '天津', //Tianjin
                                    	 '1422' => '新疆', //Xinjiang
                                    	 '1423' => '云南', //Yunnan
                                    	 '1424' => '浙江' //Zhejiang
                                    	);
    //countries
	$COUNTRY_CHINESE_ARRAY = array('44' => '中国');
	

?>