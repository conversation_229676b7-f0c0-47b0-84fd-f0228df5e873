<?
if (isset($_POST['ss_name'])) {
	$filename = "delivery/".$_POST['ss_name'];
}

$user_ip_in_binary = tep_ip_in_binary_form($_SERVER['REMOTE_ADDR']);
$allow_ip = array( 	array('ip' => '127.0.0.1', 'subnet' => 32),
					array('ip' => '*************', 'subnet' => 32),
					array('ip' => '**************', 'subnet' => 32)
				);

if (strpos($_SERVER['HTTP_REFERER'], 'show_image.php') === 0) {
	foreach ($allow_ip as $ip_info) {
		$current_ip_in_binary = tep_ip_in_binary_form($ip_info['ip']);
		
		$test_user_ip_str = substr($user_ip_in_binary, 0, $ip_info['subnet']);
		
		if (strpos($current_ip_in_binary, $test_user_ip_str) === 0) {
			if (file_exists($filename) && is_readable($filename)) {
				echo file_get_contents($filename);
			}
		}
	}
}

function tep_ip_in_binary_form($ip) {
	$ip_bin_str = '';
	
	$ip_array = explode('.', $ip);
	
	for ($i=0; $i < count($ip_array); $i++) {
		$this_ip_value = ((int)$ip_array[$i] < 0 || (int)$ip_array[$i] > 255) ? 0 : (int)$ip_array[$i];
		
		$ip_bin_str .= sprintf("%08s", decbin($this_ip_value));
	}
	
	return $ip_bin_str;
}
?>