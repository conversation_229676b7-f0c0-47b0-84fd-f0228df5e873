<?php

// include server parameters
require('includes/configure.php');

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

//define('HTTP_SERVER', 'http://www.dev3.offgamers.lan/');
//define('HTTP_SERVER', 'http://carerra.localhost/');
//define('HTTP_SERVER', 'http://carerra-w.dpodium.com/');

switch ($action) {
	case 'ajax_upload':
		$language = $_REQUEST['sup_language'];
		if (isset($_REQUEST['customer_id']) && isset($_REQUEST['req_grp_id']) && (check_buyback_ss_database($_REQUEST['customer_id'], $_REQUEST['req_grp_id']) == true)) {
			if (isset($_REQUEST['file_name_1']) && isset($_REQUEST['file_name_2'])) {
				$fileElementName = '';
				$fileElementName2 = '';
				
				$fileElementName = $_REQUEST['file_name_1'];
				$fileElementName2 = $_REQUEST['file_name_2'];
				
				$filename = 'delivery_'.$_REQUEST['req_grp_id'].'_1.jpg';
				$filename2 = 'delivery_'.$_REQUEST['req_grp_id'].'_2.jpg';
				$folder = "delivery";
				$directory = $folder.'/'.$filename;
				$directory2 = $folder.'/'.$filename2;
				
				$imageinfo = getimagesize($_FILES[$fileElementName]['tmp_name']);
				
				$proceed_flag = true;
                if(!isset($imageinfo['mime']) || !in_array($imageinfo['mime'], array('image/jpg','image/jpeg'))) {
					echo '<error_msg_1>9</error_msg_1>';
					$proceed_flag = false;
				}
				
				$imageinfo2 = getimagesize($_FILES[$fileElementName2]['tmp_name']);
				if (!isset($imageinfo2['mime']) || !in_array($imageinfo2['mime'], array('image/jpg','image/jpeg'))) {
					echo '<error_msg_2>9</error_msg_2>';
					$proceed_flag = false;
				}
				
				if(	$proceed_flag ) {
	                    move_uploaded_file($_FILES[$fileElementName]['tmp_name'], $directory);
	                    move_uploaded_file($_FILES[$fileElementName2]['tmp_name'], $directory2);
						
	                    if (is_file($directory) && is_file($directory2)) {
	                    	update_buyback_ss_database($_REQUEST['customer_id'], $_REQUEST['req_grp_id']);
	                    }
						
	                    @unlink($_FILES[$fileElementName]);
	                    @unlink($_FILES[$fileElementName2]);
						
	                    echo '<validation_result>done</validation_result>';
				}
			}
		} else {
			echo '<error_msg_3>Screenshot uploaded</error_msg_3>';
		}
		break;
}

function check_buyback_ss_database($customer_id, $bb_req_grp_id) {
	$url = HTTP_SERVER."remote_upload.php?action=checking";
	
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL,$url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	curl_setopt($ch, CURLOPT_REFERER, basename($_SERVER['SCRIPT_NAME']));
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_TIMEOUT, 30);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, array('bb_req_grp_id' => $bb_req_grp_id, 'customer_id' => $customer_id));
	
	$output = curl_exec($ch);      
	curl_close($ch);
	
	switch ($output) {
		case 'Approved':
			return true;
		break;
		default:
			return false;
		break;
	}
}

function update_buyback_ss_database($customer_id, $bb_req_grp_id) {
	$url = HTTP_SERVER."remote_upload.php?action=update_buyback_ss";

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL,$url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	curl_setopt($ch, CURLOPT_REFERER, basename($_SERVER['SCRIPT_NAME']));
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_TIMEOUT, 30);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, array('bb_req_grp_id' => $bb_req_grp_id, 'customer_id' => $customer_id));
	
	$output = curl_exec($ch);      
	curl_close($ch);
	
	switch ($output) {
		case 'success':
			return true;
		break;
		default:
			return false;
		break;
	}
}
?>