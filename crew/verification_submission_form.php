<?php
require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/verificationForm/index');

if (!tep_session_is_registered('customer_id')) {
	tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
} else {
	$upload_purchase_authorization_permission = tep_customer_info_verification($customer_id, 'files_001_locked');
	$upload_utility_bill_permission = tep_customer_info_verification($customer_id, 'files_002_locked');
	$upload_photo_identification_permission = tep_customer_info_verification($customer_id, 'files_003_locked');
	$upload_credit_card_front_permission = tep_customer_info_verification($customer_id, 'files_004_locked');
	$upload_credit_card_back_permission = tep_customer_info_verification($customer_id, 'files_005_locked');

	if (	$upload_purchase_authorization_permission *
			$upload_utility_bill_permission *
			$upload_photo_identification_permission *
			$upload_credit_card_front_permission *
			$upload_credit_card_back_permission == 1) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
	}
}

require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_VERIFICATION_SUBMISSION_FORM);

if ($_GET['action'] == 'upload') {
	require_once(DIR_WS_CLASSES . 'upload.php');

	$file_uploaded = false;
	$doc_id_array = array('doc_1' => '001', 'doc_2' => '002', 'doc_3' => '003', 'doc_4' => '004', 'doc_5' => '005');
	$maxsize_limit = 500*1024;

	$check_query = tep_db_query("SELECT customers_id FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " WHERE customers_id = ".$customer_id);
	if(!$check = tep_db_fetch_array($check_query)) {
		tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT, array('customers_id' => $customer_id));
	}

	foreach ($_FILES AS $file_io_name => $file_info) {
		if (tep_not_null($file_info['name'])) {
			$doc_id = $doc_id_array[$file_io_name];

			$icons_image = new upload($file_io_name, '', '0775', array('gif', 'jpg', 'png'), '', '', $maxsize_limit);
			$icons_image->set_destination(DIR_FS_DATA_LL_SUBMISSION);

			if ($icons_image->parse() && $icons_image->save($customer_id.'_'.$doc_id.'_'.date("YmdHis"), 'small')) {
				$file_uploaded = true;

				$update_data_array = array ('files_'.$doc_id => $icons_image->filename, 'files_'.$doc_id.'_locked' => 1);
				tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT, $update_data_array, "update", " customers_id=".$customer_id);

				$cache_key = TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . '/customers_id/' . (int)$customer_id . '/files_' . $doc_id . '_locked';
    			$memcache_obj->delete($cache_key, 0);

				$file_path = DIR_FS_DATA_LL_SUBMISSION.$icons_image->filename;

				$fh = fopen($file_path , 'r') or die("can't open file");
				$theData = fread($fh, filesize($file_path));
				fclose($fh);

				$theData = base64_encode($theData);
				$theData = tep_encrypt_data($theData);

				$fh = fopen($file_path, 'w') or die("can't open file");
				fwrite($fh, $theData);
				fclose($fh);

				$insert_data_array = array ('log_users_id' => $customer_id,
											'log_users_type' => 'customers',
											'log_customers_id' => $customer_id,
											'log_docs_id' => $doc_id,
											'log_IP' => tep_get_ip_address(),
											'log_datetime' => 'now()',
											'log_filename' => $icons_image->filename,
											'log_action' => 'UPLOAD');
				tep_db_perform(TABLE_VERIFICATION_DOC_LOG, $insert_data_array);

				foreach ($icons_image->get_output_messages() as $message_array) {
					$messageStack->add_session('verification_submission', '['.$file_info['name'].'] '.$message_array['message'], $message_array['type']);
					$messageStack->add_session('account', '['.$file_info['name'].'] '.$message_array['message'], $message_array['type']);
				}
				unset($icons_image, $update_data_array, $insert_data_array);
			} else {
				foreach ($icons_image->get_output_messages() as $message_array) {
					$messageStack->add_session('verification_submission', '['.$file_info['name'].'] '.$message_array['message'], $message_array['type']);
					$messageStack->add_session('account', '['.$file_info['name'].'] '.$message_array['message'], $message_array['type']);
				}
				unset($icons_image);
			}
		}
	}

	if ($file_uploaded) {
		tep_file_uploaded_email_notification($customer_id);
	}

	// If completely submit all documents, redirect to Account Summary page since Customer not allowed to see this page
	if (tep_customer_info_verification($customer_id, 'files_001_locked') *
		tep_customer_info_verification($customer_id, 'files_002_locked') *
		tep_customer_info_verification($customer_id, 'files_003_locked') *
		tep_customer_info_verification($customer_id, 'files_004_locked') *
		tep_customer_info_verification($customer_id, 'files_005_locked') == 1) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
	}

	tep_redirect(tep_href_link(FILENAME_VERIFICATION_SUBMISSION_FORM, '', 'SSL'));
}

$content = CONTENT_VERIFICATION_SUBMISSION_FORM;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');

?>