<?php

require_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');

tep_db_connect() or die('Unable to connect to database server!');

define('DIR_FS_HLA_PROFILE', DIR_FS_HLA . 'hla/profile/');

if (isset($_GET['char_id'])) {
	$profile_url = '';
	
	$get_url_select_sql = " SELECT products_hla_characters_url 
							FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " 
							WHERE products_hla_characters_id = '".tep_db_input($_GET['char_id'])."'";
	$get_url_result_sql = tep_db_query($get_url_select_sql);
	if ($get_url_row = tep_db_fetch_array($get_url_result_sql)) {
		if (tep_not_null($get_url_row['products_hla_characters_url'])) {
			$profile_url = $get_url_row['products_hla_characters_url'];
			echo '<iframe src ="'.$profile_url.'" frameborder="1" scrolling="auto" style="position:absolute;top:0px;left:0px;width:100%;height:100%;margin:auto;z-index:999">
				  <p>Profile are not available. Please try again later.</p>
				</iframe>';
			die();
		}
	}
	
	if (!tep_not_null($profile_url)) {
		$file_path = DIR_FS_HLA_PROFILE . $_GET['char_id'].'.html';
		$content_info_arr = array();
		
		if (file_exists($file_path)) {
			$handle = fopen($file_path, "r");
			$content_info = fread($handle, filesize($file_path));
			fclose($handle);
			
			$content_info_arr = unserialize($content_info);
		} else {
			echo 'Profile are not available. Please try again later.';
			die();
		}
	}
} else {
	die();
}

?>