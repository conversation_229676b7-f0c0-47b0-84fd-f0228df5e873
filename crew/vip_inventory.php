<?php

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_VIP_INVENTORY_UPDATE);

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}	
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_VIP_INVENTORY_UPDATE;

$cID = isset($_REQUEST['cID']) ? $_REQUEST['cID'] : '';
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

$active_game_array = array(array('id' => 0, 'text' => PULL_DOWN_DEFAULT));
tep_get_vip_categories($active_game_array);

$vipSupObj = new vip_supplier($customer_id);

switch ($action) {
	case 'batch_action':
		if (isset($_POST['btn_csv_type']) && $_POST['btn_csv_type'] == 'import') {
			if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
				if ($_FILES['csv_import']["size"] > 0) {
					$import_error = false;
					$filename = ($_FILES['csv_import']['tmp_name']);
				    $handle = fopen($filename, 'r+');
					
				    $must_have_field = array(TABLE_CSV_HEADER_SERVER_ID => 0, TABLE_CSV_HEADER_SERVER_QUANTITY => 0, TABLE_CSV_HEADER_SERVER_MIN_QUANTITY => 0, TABLE_CSV_HEADER_SERVER_DELETE => 0);
				    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    	$header_exists_count = 0;
				    	for ($i=0; $i < count($data); $i++) {
				    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
				    			$must_have_field[trim($data[$i])] = $i;
				    			$header_exists_count++;
				    		}
				    	}
						
				    	if ($header_exists_count != count($must_have_field)) {
				    		$messageStack->add_session($content, "Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
				    		$import_error = true;
				    	}
				    }
					
				    if (!$import_error) {
				    	$imported_products_array = array();
						
				    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    		if (trim($data[0]) == '') {	// Assume this row is useless
			    				continue;
			    			}
							
				    		$product_id_str = $data[$must_have_field[TABLE_CSV_HEADER_SERVER_ID]];
							$inv_qty_str = $data[$must_have_field[TABLE_CSV_HEADER_SERVER_QUANTITY]];
							$min_qty_str = $data[$must_have_field[TABLE_CSV_HEADER_SERVER_MIN_QUANTITY]];
							$delete_str = $data[$must_have_field[TABLE_CSV_HEADER_SERVER_DELETE]];
							
				    		if (!isset($vipSupObj->registered_servers[$product_id_str])) {
				    			$messageStack->add_session($content, sprintf(ERROR_IMPORTED_PRODUCT_NOT_MATCH, $product_id_str), 'error');
				    			$import_error = true;
				    		} else {
				    			$imported_products_array[] = array(	'prd_id' => (int)$product_id_str,
										    						'quantity' => (int)$inv_qty_str,
										    						'min_quantity' => (int)$min_qty_str,
										    						'delete' => $delete_str
								    								);
					    		if (tep_not_null($inv_qty_str)) {
					    			if (!preg_match('/[1-9][0-9]*/is', $inv_qty_str) && $inv_qty_str != '0') {
						    			$messageStack->add_session($content, sprintf(ERROR_IMPORTED_PRODUCT_QUANTITY_INVALID, $product_id_str), 'error');
						    			$import_error = true;
						    		}
					    		}
					    		
					    		if (tep_not_null($min_qty_str)) {
					    			if (!preg_match('/[1-9][0-9]*/is', $min_qty_str) && $min_qty_str != '0') {
						    			$messageStack->add_session($content, sprintf(ERROR_IMPORTED_PRODUCT_MIN_QUANTITY_INVALID, $product_id_str), 'error');
						    			$import_error = true;
						    		}
					    		}
					    		
					    	}
				    	}
						
				    	fclose($handle);
				    	
				    	if ($import_error) {
				    		tep_redirect(tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, 'cID=' . $cID));
				    	} else {
				    		$messageStack->add($content, WARNING_UPDATE_IMPORTED_DATA, 'warning');
				    	}
				    } else {
				    	fclose($handle);
				    	tep_redirect(tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, 'cID=' . $cID));
				    }
				} else {
					$messageStack->add_session($content, ERROR_NO_UPLOAD_FILE, 'error');
					tep_redirect(tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, 'cID=' . $cID));
				}
			} else {
				$messageStack->add_session($content, WARNING_NO_FILE_UPLOADED, 'warning');
				tep_redirect(tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, 'cID=' . $cID));
			}
		} else if (isset($_POST['btn_csv_type']) && $_POST['btn_csv_type'] == 'export') {
			$export_csv_array = tep_array_unserialize($_POST["serialized_export_csv_array"]);
			$export_csv_data = '';
			
			if (count($export_csv_array)) {
				foreach ($export_csv_array as $pid => $res) {
					$tmp_cvs_data_array = array();
					for ($i=0; $i < count($res); $i++) {
						$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
					}
					$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
				}
			}
			
			if (tep_not_null($export_csv_data)) {
				$filename = 'inventory_'.date('YmdHi').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			}
		} else {
			$inventory_info = $_POST['inv_qty'];
			$min_info = $_POST['min_qty'];
			$deletion_info = isset($_POST['delete_batch']) && is_array($_POST['delete_batch']) ? $_POST['delete_batch'] : array();
			
			if (is_array($inventory_info) && count($inventory_info)) {
				foreach ($inventory_info as $pid => $qty) {
					if (in_array($pid, $deletion_info)) {
						$vipSupObj->delete_inventory($pid);
					} else {
						$vipSupObj->update_inventory($pid, $qty);
					}
				}
			}
			
			if (is_array($min_info) && count($min_info)) {
				foreach ($min_info as $pid => $min_qty) {
					if (in_array($pid, $deletion_info)) {
						;
					} else {
						$vipSupObj->update_min_quantity($pid, $min_qty);
					}
				}
			}
			
			$messageStack->add_session($content, SUCCESS_VIP_INVENTORY_UPDATED, 'success');
			tep_redirect(tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, 'cID=' . $cID));
		}
		
		break;
}

if ($cID > 0)	$buybackSupplierObj = new buyback_supplier($cID);

$vipSupObj->get_supplier_setting($cID);

$workingdays_array = array();
if (isset($vipSupObj->game_settings['WORKING_DAYS'])) {
	$workingdays_array = explode (',', $vipSupObj->game_settings['WORKING_DAYS']);
}

$workingtime_array = array();
if (isset($vipSupObj->game_settings['WORKING_TIME'])) {
	$workingtime_array = explode (',', $vipSupObj->game_settings['WORKING_TIME']);
}

$server_html = '';
$export_csv_array['HEADER'] = array(TABLE_CSV_HEADER_SERVER_ID, TABLE_CSV_HEADER_SERVER_NAME, TABLE_CSV_HEADER_SERVER_QUANTITY, TABLE_CSV_HEADER_SERVER_MIN_QUANTITY, TABLE_CSV_HEADER_SERVER_DELETE);

$row_count = 0;
if (isset($buybackSupplierObj->products_arr) && is_array($buybackSupplierObj->products_arr)) {
	if (isset($_POST['btn_csv_type']) && $_POST['btn_csv_type'] == 'import') {
		$total_imported = count($imported_products_array);
		
		for ($prod_cnt=0; $prod_cnt < $total_imported; $prod_cnt++) {
			$pid = $imported_products_array[$prod_cnt]['prd_id'];
			if (isset($buybackSupplierObj->products_arr[$pid])) {
				//$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				$row_style = 'ordersListing';
				
				$categories_name = strip_tags($buybackSupplierObj->products_arr[$pid]['categories_name']);
				
				if ($row_count != 0) {
					$server_html .= '<tr><td colspan="7"><div class="row_separator"></div></td></tr>';
				}
				
				$server_html .= '<tr id="row_'.$pid.'" height="25" class="'.$row_style.'" onMouseOver="this.className =\'ordersListingOver\'" onMouseOut="this.className =\'ordersListingOut\'">
									<td width="1%">'.tep_draw_checkbox_field('server_batch[]', $pid, false).'</td>
									<td width="50%" align="left">'.$categories_name.'&nbsp;</td>
									<td width="20%" align="center">'.tep_draw_input_field('inv_qty['.$pid.']', $imported_products_array[$prod_cnt]['quantity'], ' size="10" maxlength="10"').'</td>
									<td width="20%" align="center">'.tep_draw_input_field('min_qty['.$pid.']', $imported_products_array[$prod_cnt]['min_quantity'], ' size="10" maxlength="10"').'</td>
									<td width="##DYNAMIC_WIDTH##">'.tep_draw_checkbox_field('delete_batch[]', $pid, $imported_products_array[$prod_cnt]['delete'] == '1' ? true : false).'</td>
								  </tr>';
				
				$export_csv_array[$pid] = array($pid, $pinfo['categories_name'], $vipSupObj->registered_servers[$pid]['qty'], $vipSupObj->registered_servers[$pid]['min_qty'], 0);
				
				$row_count++;
			}
		}
	} else {
		foreach ($buybackSupplierObj->products_arr as $pid => $pinfo) {
			if (isset($vipSupObj->registered_servers[$pid])) {
				//$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				$separator = '';
				if ($row_count != 0) {
					$separator .= '<tr id="separator_'.$pid.'"><td colspan="7"><div class="row_separator"></div></td></tr>';
				}
				
				$row_style = 'ordersListing';
				
				$server_html .= '<tr id="row_'.$pid.'" height="25" onMouseOver="this.className =\'ordersListingOver\'" onMouseOut="this.className =\'ordersListingOut\'">
									<td>
									<table cellpadding="0" cellspacing="0" width="100%">
									'.$separator.'
									<tr class="'.$row_style.'">
										<td width="1%">'.tep_draw_checkbox_field('server_batch[]', $pid, false, 'class="server_batch"').'</td>
										<td width="50%" align="left">'.$pinfo['categories_name'].'&nbsp;</td>
										<td width="20%" align="center">'.tep_draw_input_field('inv_qty['.$pid.']', $vipSupObj->registered_servers[$pid]['qty'], ' id="inv_qty_'.$pid.'" class="inv_qty" size="10" maxlength="10"').'</td>
										<td width="20%" align="center">'.tep_draw_input_field('min_qty['.$pid.']', $vipSupObj->registered_servers[$pid]['min_qty'], ' id="min_qty_'.$pid.'" class="min_qty" size="10" maxlength="10"').'</td>
										<td width="##DYNAMIC_WIDTH##" align="center"><a href="javascript:void(0);" onClick="delete_server(\''.$pid.'\'); return false;">'.tep_image(DIR_WS_IMAGES . 'icon-close.gif', '', '10', '10').'</a></td>
									  </tr>
									</table>
									</td>
								</tr>';
				
				
				
				$pinfo['categories_name'] = strip_tags($pinfo['categories_name']);
				
				//tep_draw_checkbox_field('delete_batch[]', $pid, false, 'class="delete_batch"');
				$export_csv_array[$pid] = array($pid, $pinfo['categories_name'], $vipSupObj->registered_servers[$pid]['qty'], $vipSupObj->registered_servers[$pid]['min_qty'], 0);
				
				$row_count++;
			}
		}
	}
	
	if ($row_count < 9) {
		$server_html = str_replace("##DYNAMIC_WIDTH##", "20%", $server_html);
	} else {
		$server_html = str_replace("##DYNAMIC_WIDTH##", "17%", $server_html);
	}
	
}
//$javascript = 'vip_xmlhttp.js';
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>