<?php
require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'edit_order.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'affiliate.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_VIP_ORDERS_HISTORY);

if (file_exists(DIR_WS_LANGUAGES . $language . '/email_contents.php')) {
	include(DIR_WS_LANGUAGES . $language . '/email_contents.php');
}

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_VIP_ORDERS_HISTORY;
//Define the javascript file
//$javascript = 'vip_xmlhttp.js';

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
define('DISPLAY_CURRENCY_DECIMAL', 2);

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_VIP_ORDERS_HISTORY;
$new_session = false;
if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
	$new_session = true;
}
$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = '';
}

//default setting for search vip order history
$allow_search_num_days_ago = 100;
$vipodh_input_order_status_select = '0';
$vipodh_input_product_type_select = 1;
$vipodh_input_order_no = '';
$vipodh_input_game_select = 0;

$start_date = $vipodh_input_start_date = '';

if (isset($_POST['vipodh_input_start_date']) && tep_not_null($_POST['vipodh_input_start_date'])) {
	$_POST['vipodh_input_start_date'] = $start_date = $vipodh_input_start_date = strstr($vipodh_input_start_date, ':') ? $_POST['vipodh_input_start_date'].' 00:00:00' : $_POST['vipodh_input_start_date'];
} else if (isset($_GET['startdate']) && tep_not_null($_GET['startdate'])) {
	$_POST['vipodh_input_start_date'] = $start_date = $vipodh_input_start_date = strstr($vipodh_input_start_date, ':') ? $_GET['startdate'].' 00:00:00' : $_GET['startdate'];
}

$end_date = $vipodh_input_end_date = '';

/**
 * Validate the post
**/
$errorCount = 0;
switch ($action) {
	case 'reset_session':
	    unset($_SESSION[$form_session_name]);
	    tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
	    break;
	case 'confirm_sent':
		//1 = pending, 2 = processing, 3 = completed
		//trade with us (update buyback_quantity_confirmed) - buyback status = 1 to 2
		//trade with customer(update buyback_quantity_confirmed, buyback_quantity_received) - buyback status = 1 to 3 if code match, 1 to 2 if code mismatch
		$buyback_request_group_id = isset($_POST['request_group_id']) ? (int)$_POST['request_group_id'] : 0;
		$buyback_request_id = isset($_POST['request_id']) ? (int)$_POST['request_id'] : 0;
		$buyback_quantity_confirmed = isset($_POST['sent_qty'][$buyback_request_group_id]) ? (int)$_POST['sent_qty'][$buyback_request_group_id] : 0;
		//$buyback_request_customers_code = isset($_POST['trade_code'][$buyback_request_group_id]) ? (string)$_POST['trade_code'][$buyback_request_group_id] : 0;
		$buyback_request_customers_code = 0;
		$vip_error = false;
		
		//if (isset($_POST['btn_cancel_'.$buyback_request_group_id])) {	// Cancel this order
		if (isset($_POST['btn_action_'.$buyback_request_group_id]) && $_POST['btn_action_'.$buyback_request_group_id] == 'cancel') {
			$dealing_type_select_sql = "SELECT br.buyback_dealing_type, orders_id, orders_products_id 
										FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
										INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
											ON (brg.buyback_request_group_id = br.buyback_request_group_id)
										WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
											AND br.orders_id <> 0
											AND br.orders_products_id <> 0
											AND brg.customers_id = '" . $_SESSION['customer_id'] . "' 
											AND br.buyback_request_id='" . tep_db_input($buyback_request_id) . "'";
			$dealing_type_select_result = tep_db_query($dealing_type_select_sql);
			if ($dealing_type_select_row = tep_db_fetch_array($dealing_type_select_result)) {
				$vipOrderCancelObj = new vip_order($dealing_type_select_row['orders_products_id']);
				
				if ($vipOrderCancelObj->order_accepted_cancellation($_SESSION['customer_id'], $buyback_request_group_id, 'VIP_DEDUCT_ORDER_CANCEL_AFTER_ACCEPTED')) {
					$to_name = $to_email = '';
					
					$customer_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address 
													FROM " . TABLE_CUSTOMERS . "
													WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
					$customer_info_result_sql = tep_db_query($customer_info_select_sql);
					if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
						$to_name = $customer_info_row['customers_firstname'].' '.$customer_info_row['customers_lastname'];
						$to_email = $customer_info_row['customers_email_address'];
					}
					
					$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
				}
				
				//unlock order for dealing type = ofp_deal_with_customers
				if ($dealing_type_select_row['buyback_dealing_type'] == 'ofp_deal_with_customers') {
					//check any others order are assigned for the same order with deal with customers
					$pending_order_select_sql = "SELECT br.buyback_dealing_type, orders_id
												FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
												INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id)
												WHERE brg.buyback_status_id = '1'
													AND br.orders_id = '" . tep_db_input($dealing_type_select_row['orders_id']) . "'
													AND br.orders_products_id <> 0
													AND buyback_dealing_type = 'ofp_deal_with_customers'";
					$pending_order_select_result = tep_db_query($pending_order_select_sql);
					if (tep_db_num_rows($pending_order_select_result) == 0) {
						//check is system lock the order
						$check_order_lock_sql = "	SELECT orders_locked_by 
				    								FROM " . TABLE_ORDERS . " 
				    								WHERE orders_id = '" . tep_db_input($dealing_type_select_row['orders_id']) . "'";
						$check_order_lock_result = tep_db_query($check_order_lock_sql);
						if ($check_order_lock_row = tep_db_fetch_array($check_order_lock_result)) {
							if ($check_order_lock_row['orders_locked_by'] == 0) {
								//unlock order
								tep_customer_order_locking($dealing_type_select_row['orders_id'], '0', 'unlock');
							}
						}
					}
				}
			} else {
				$messageStack->add_session($content, ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED, 'error');
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
		break;
	case 'accept_order':
		//trade with us - buyback status = 1
		//trade with customer - buyback status = 1
		//update buyback_request_quantity
		
		$error = false;
		$orders_products_id = isset($_POST['opId']) ? (int)$_POST['opId'] : 0;
		$character_name = isset($_POST['vip_character_name'][$orders_products_id]) ? $_POST['vip_character_name'][$orders_products_id] : '';
		$submit_qty = isset($_POST['submit_quantity'][$orders_products_id]) ? (int)$_POST['submit_quantity'][$orders_products_id] : 0; 
		
		$trade_type = isset($_POST['trade_type_'.$orders_products_id]) ? $_POST['trade_type_'.$orders_products_id] : '';
		$captcha_code = isset($_POST['captcha_code_'.$orders_products_id]) ? $_POST['captcha_code_'.$orders_products_id] : '';
		
		$buyback_status = '1';
		$show_character = '0';
		
		$select_vip_awaiting_orders = 	"SELECT orders_products_id, products_id, vip_order_allocation_quantity, vip_order_allocation_time
										FROM " . TABLE_VIP_ORDER_ALLOCATION . "
										WHERE orders_products_id='".tep_db_input($orders_products_id)."' AND customers_id='".tep_db_input($_SESSION['customer_id'])."'";
		
		$select_vip_awaiting_orders_result = tep_db_query($select_vip_awaiting_orders);
		if ($select_vip_awaiting_orders_row = tep_db_fetch_array($select_vip_awaiting_orders_result)) {	
			$vipOrderObj = new vip_order($select_vip_awaiting_orders_row['orders_products_id']);
			$vipOrderObj->get_orders_details();
			$selected_cat_id = $vipOrderObj->order_detail['buyback_categories_id'];
			$selected_product_id = $select_vip_awaiting_orders_row['products_id'];
			
			//check order request is expired
			if (!$vipOrderObj->check_vip_mode($selected_cat_id) || $vipOrderObj->order_request_expired() || !$vipOrderObj->check_purchase_eta($select_vip_awaiting_orders_row['orders_products_id'], $selected_product_id)) {
				$memcache_obj->delete(TABLE_VIP_ORDER_ALLOCATION . '/vip_allocation_list/xml/customers_id/'.$_SESSION['customer_id'], 0);
				tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
			} else {
				if ($trade_type == 'trade_cancel') { // Cancel button is clicked
					//delete order request
					$vipOrderObj->order_request_cancellation($_SESSION['customer_id'], $orders_products_id, $select_vip_awaiting_orders_row['products_id'], 'VIP_DEDUCT_SUPPLIER_REJECT');
					$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
					
					$memcache_obj->delete(TABLE_VIP_ORDER_ALLOCATION . '/vip_allocation_list/xml/customers_id/'.$_SESSION['customer_id'], 0);
					
					tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
				}
				
				if (isset($_SESSION['securimage_code_value']) && ($_SESSION['securimage_code_value']!== '')) {
				    if ($_SESSION['securimage_code_value'] != strtolower(trim($captcha_code))) {
				    	$messageStack->add_session($content, ERROR_INVALID_CODE);
			  	 		$error = true;
				    }
				} else {
					$messageStack->add_session($content, ERROR_INVALID_CODE);
			  	 	 $error = true;
				}
				
				if ($character_name == '') {
					$error = true;
					$messageStack->add_session($content, ERROR_NO_TRADE_CHARACTER);
				}
				
				if ($submit_qty == '' || !is_numeric($submit_qty)) {
					$error = true;
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
				}
				
				if($submit_qty > 0){
					$buybackSupplierObj = new buyback_supplier($selected_cat_id, $selected_product_id);
					$buybackSupplierObj->vip = true;
					$buybackSupplierObj->calculate_offer_price();
					// check available qty
					$max_qty = (int)$vipOrderObj->tep_get_max_qty($selected_product_id, $select_vip_awaiting_orders_row['orders_products_id'], $_SESSION['customer_id']);
					$min_qty = (int)$buybackSupplierObj->vip_min_qty;
					$min_purchase_qty = (int)$buybackSupplierObj->vip_min_purchse_qty;

					if($min_purchase_qty > 0 && $min_qty > 0 && $min_purchase_qty < $max_qty){
						$upper_min_qty = (int)$max_qty - $min_purchase_qty;
						if (($submit_qty >= $min_qty && $submit_qty <= $upper_min_qty)
							|| $submit_qty == $max_qty) {
							;
						} else {
							$error = true;
							$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
						}
					} else {
						if ($submit_qty < $min_qty || $submit_qty > $max_qty) {
							$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
							$error = true;
						}
					}
					// Can allow <= Qty
					/*
					if ($submit_qty != $max_qty) {
						$error = true;
						$messageStack->add_session($content, TEXT_QUANTITY_MAX_NOT_MATCH);
					}
					*/
				} else {
					$error = true;
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
				}
				
				// trade with us or customers
				switch ($trade_type) {
					case 'trade_us':
						$vip_mode = '1';
						$dealing_type = 'vip_deal_on_game';
						$buyback_status = '1';
						$show_character = '0';
						$vipOrderObj->calculate_trade_us_price();
						$buyback_amount =$vipOrderObj->order_detail['trade_us_price'] * $submit_qty;
						$order_unit_price = $vipOrderObj->order_detail['trade_us_price'];
						$buyback_request_customer_org_code = 0;
						$buyback_request_supplier_code = 0;
						
						break;
					case 'trade_customer':
						$vip_mode = '1';
						$dealing_type = 'ofp_deal_with_customers';
						$buyback_status = '1';
						$show_character = '0';
						$vipOrderObj->calculate_trade_customers_price($_SESSION['customer_id']);
						$buyback_amount = $vipOrderObj->order_detail['trade_customers_price'] * $submit_qty;
						$order_unit_price = $vipOrderObj->order_detail['trade_customers_price'];
						$buyback_request_customer_org_code = tep_rand(1, 9, 7);
						$buyback_request_supplier_code = tep_rand(1, 9, 7);
						
						tep_customer_order_locking($vipOrderObj->order_detail['orders_id'], '0', 'unlock');
						tep_customer_order_locking($vipOrderObj->order_detail['orders_id'], '0', 'lock');
						
						break;
				}
				
				if ($order_unit_price <= 0) {
					$error = true;
					$messageStack->add_session($content, ERROR_PLS_TRY_AGAIN);
				}
				
				// Temporary Processing to prevent insert unwanted record;
				$matchcase = md5($vipOrderObj->order_detail['orders_id'].':~:'.$max_qty);
				$concurrentProcessObj = new concurrent_process(FILENAME_MY_VIP_ORDERS_HISTORY, $matchcase, $submit_qty);
				
				//usleep(rand(500000, 2000000)); // Delay execution in microseconds
				usleep(1500000);
				if($concurrentProcessObj->concurrent_process_matching()) {	// If there is > 1 orders matched
					//if (array_sum($concurrentProcessObj->extra_info_arr) > $max_qty) {
						if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
							$error = true;
							$messageStack->add_session($content, ERROR_SUBMITTED_BY_OTHER_USER);
						}
					//}
				}
				
				if(!$error){
					$game_name = tep_get_categories_name($vipOrderObj->order_detail['buyback_categories_id']);
					$cron_open_restock_id_array = array();
					
					$select_supplier_info = "SELECT c.customers_firstname, c.customers_lastname, c.customers_telephone, c.customers_email_address, c.customers_dob, 
											c.customers_gender, ab.entry_street_address, ab.entry_city, ab.entry_zone_id, c.customers_mobile, c.customers_msn, 
											c.customers_yahoo, c.customers_qq, c.customers_icq 		
											FROM " . TABLE_CUSTOMERS . " AS c 
											INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab
												ON(c.customers_id=ab.customers_id) 
											WHERE c.customers_id=".(int)$_SESSION['customer_id'];
								
					$select_supplier_info_result = tep_db_query($select_supplier_info);
					$select_supplier_info_row = tep_db_fetch_array($select_supplier_info_result);
					
					$expired_time_str = date("Y-m-d H:i:s", $vipOrderObj->tep_get_vip_expired_time('vip_order'));
					
					// Create buyback order
			    	$buyback_request_group_arr = array('customers_id' => (int)$_SESSION['customer_id'],
													   'buyback_request_group_date' => 'now()',
													   'buyback_request_group_expiry_date' => $expired_time_str,
													   'remote_addr' => tep_get_ip_address(),
													   'currency' => DEFAULT_CURRENCY,
													   'currency_value' => $currencies->get_value(DEFAULT_CURRENCY, 'buy'),
													   'buyback_request_group_comment' => '',
													   'buyback_request_group_served' => '1',
													   'buyback_request_group_user_type' => (int)tep_get_account_created_from($_SESSION['customer_id']),
													   'buyback_request_contact_name' => $select_supplier_info_row['customers_firstname'].' '.$select_supplier_info_row['customers_lastname'],
													   'buyback_request_contact_telephone' => $select_supplier_info_row['customers_telephone'],
													   'buyback_request_group_site_id' => SITE_ID
														);
														
			    	tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_group_arr);
			    	$buyback_request_group_id = tep_db_insert_id();
					
					$buyback_request_array = array(	'buyback_request_group_id' => $buyback_request_group_id,
												   	'products_id' => $vipOrderObj->order_detail['products_id'],
												   	'buyback_request_quantity' => $submit_qty,
												   	'buyback_amount' => $buyback_amount,
												   	'buyback_sender_character' => $character_name,
												   	'buyback_dealing_type' => $dealing_type,
												   	'buyback_unit_price' => $order_unit_price,
												   	'buyback_request_customer_org_code' => $buyback_request_customer_org_code,
													'buyback_request_supplier_code' => $buyback_request_supplier_code,
													'orders_id' => $vipOrderObj->order_detail['orders_id'],
													'orders_products_id' => $vipOrderObj->order_detail['orders_products_id']
												   );
					tep_db_perform(TABLE_BUYBACK_REQUEST, $buyback_request_array);
					
					$cron_open_restock_id_array = array('buyback_request_group_id' => $buyback_request_group_id,
														'orders_products_id' => $vipOrderObj->order_detail['orders_products_id'],
														'created_date' => 'now()');
					tep_db_perform(TABLE_CRON_OPEN_RESTOCK_ID, $cron_open_restock_id_array);
					
					// Insert buyback history comment
					$buyback_history_data_array = array('buyback_status_id' => $buyback_status,
			    			   							'buyback_request_group_id' => $buyback_request_group_id,
			    			   							'date_added' => 'now()',
			    			   							'customer_notified' => '1',
			    			   							'set_as_buyback_remarks' => '0'
			    			   							);
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
					
					$comment = '##BO##'.$buyback_request_group_id.'## - '.$submit_qty.' by '.$select_supplier_info_row['customers_firstname'].' '.$select_supplier_info_row['customers_lastname'];
			    	
			    	$comment_array = array(	'orders_id' => $vipOrderObj->order_detail['orders_id'],
											'orders_status_id' => '0',
											'date_added' => 'now()',
											'customer_notified' => '0',
											'comments' => $comment,
											'comments_type' => '0',
											'set_as_order_remarks' => '0',
											'changed_by' => $select_supplier_info_row['customers_email_address']
											);
					tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
					
					$buyback_request_grp_array = array(	'buyback_status_id' => $buyback_status,
	    												'show_restock' => $show_character,
	    												'buyback_request_order_type' => $vip_mode
	    												);
					tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_grp_array, 'update', " buyback_request_group_id='" . $buyback_request_group_id . "'");
	    			
					$supplier_info_array = array (	'buyback_request_group_id' => $buyback_request_group_id,
													'buyback_order_info_customer_firstname' => $select_supplier_info_row['customers_firstname'],
													'buyback_order_info_customer_lastname' => $select_supplier_info_row['customers_lastname'],
													'buyback_order_info_customer_telephone' => $select_supplier_info_row['customers_telephone'],
													'buyback_order_info_customer_email' => $select_supplier_info_row['customers_email_address'],
													'buyback_order_info_customer_dob' => $select_supplier_info_row['customers_dob'],
													'buyback_order_info_customer_gender' => $select_supplier_info_row['customers_gender'],
													'buyback_order_info_customer_address' => $select_supplier_info_row['entry_street_address'],
													'buyback_order_info_customer_city' => $select_supplier_info_row['entry_city'],
													'buyback_order_info_customer_state' => $select_supplier_info_row['entry_zone_id'],
													'buyback_order_info_customer_mobile' => $select_supplier_info_row['customers_mobile'],
													'buyback_order_info_customer_msn' => $select_supplier_info_row['customers_msn'],
													'buyback_order_info_customer_yahoo' => $select_supplier_info_row['customers_yahoo'],
													'buyback_order_info_customer_qq' => $select_supplier_info_row['customers_qq'],
													'buyback_order_info_customer_icq' => $select_supplier_info_row['customers_icq']
												);
					tep_db_perform(TABLE_BUYBACK_ORDER_INFO, $supplier_info_array);
					
					$select_order_allocated = "DELETE FROM " . TABLE_VIP_ORDER_ALLOCATION . " WHERE orders_products_id='" . $orders_products_id . "'";
					tep_db_query($select_order_allocated);
					
					// ADMIN EMAIL
					$unit_selling_price = (double)$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['customer_price'];
					$profit_margin = $unit_selling_price > 0 ? ( ($unit_selling_price - $order_unit_price) / $unit_selling_price ) * 100 : '--';
					$profit_margin = sprintf('%.3f', $profit_margin);
					
					//email to notify supplier.
					//add email template to email content chinese
					$email_subject = sprintf(EMAIL_NEW_VIP_ORDER_SUBJECT, $buyback_request_group_id, array_search($buyback_status, $PAYMENT_STATUS_ARRAY), $game_name);
					$cat_path = tep_output_generated_category_path($vipOrderObj->order_detail['products_id'], 'product');
					$currencies->set_decimal_places(DISPLAY_CURRENCY_DECIMAL);
					
					$buyback_amount_display = $currencies->format($buyback_amount, true, DEFAULT_CURRENCY, '', 'buy');
					//$buyback_amount_display = $_SESSION['currency'] . $currencies->apply_currency_exchange((double)$buyback_amount, $_SESSION['currency'], '', 'buy');
					
					$email_greeting = tep_mb_convert_encoding(
											tep_get_email_greeting(	tep_mb_convert_encoding($select_supplier_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), 
																	tep_mb_convert_encoding($select_supplier_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET),
																	$select_supplier_info_row['customers_gender']), CHARSET, EMAIL_CHARSET);
					
					$email_text = "\n".sprintf(EMAIL_NEW_VIP_ORDERS_SUMMARY, $buyback_request_group_id, tep_date_long(date("Y-m-d")." 00:00:00"), 
									$select_supplier_info_row['customers_email_address'], $VIP_TRADE_MODE[$dealing_type], $submit_qty, $cat_path, 
									$buyback_amount_display, $buyback_amount_display, array_search($buyback_status, $BUYBACK_ORDER_STATUS_ARRAY))
											."\n\n" . EMAIL_VIP_ORDER_GUIDE . "\n" .EMAIL_LOCAL_STORE_EMAIL_SIGNATURE;
											
					tep_mail(tep_mb_convert_encoding($select_supplier_info_row['customers_firstname'] . ' ' . $select_supplier_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $select_supplier_info_row['customers_email_address'], $email_subject, $email_greeting . $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					
					if (round($profit_margin) < 15) { // Only notify when gross profit is < 20%
						// Email to Admin
						$jr_email_to_array = tep_parse_email_string(JUNIOR_PURCHASE_TEAM_EMAIL);
						for ($i=0; $i < count($jr_email_to_array); $i++) {
							tep_mail($jr_email_to_array[$i]['name'], $jr_email_to_array[$i]['email'], $email_subject, $email_greeting . $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						}
						
						$margin_cfg_array = tep_get_cfg_setting($vipOrderObj->order_detail['products_id'], 'product', 'BUYBACK_PROFIT_MARGIN');
						if ($profit_margin < (double)$margin_cfg_array['BUYBACK_PROFIT_MARGIN']) {
							$cell_span_style = ' style="color:#ff0000;" ';
						} else {
							$cell_span_style = ' style="color:#000000;" ';
						}
						
						$profit_margin = $profit_margin . '%';
						$admin_buyback_product_list = 	'<table border="0" cellspacing="2" cellpadding="2">'.
											  			'	<tr>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">Product</td>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Maximum Qty</td>'.
											 			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Available Qty</td>'.
														'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Actual Qty</td>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Price (USD)</td>' .
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Selling Price (USD)</td>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Profit Margin</td>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">First List Selling Quantity</td>'.
											  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Amount (USD)</td>'.
											  			'	</tr>'.
														'	<tr>'.
											  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px"><span '.$cell_span_style.'>'.$cat_path.'</span></td>'.
											  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$max_qty.'</span></td>'.
														'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['forecast_available_qty'].'</span></td>'.
														'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['forecast_actual_qty'].'</span></td>'.
												  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$order_unit_price.'</span></td>' .
												  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$unit_selling_price.'</span></td>' .
												  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$profit_margin.'</span></td>'.
														'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$submit_qty.'</span></td>'.
														'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="right"><span '.$cell_span_style.'>'.$buyback_amount.'</span></td>'.
														'	</tr>'.
														'</table>';
						
						$admin_email_text = "\n".sprintf(EMAIL_ADMIN_NEW_VIP_ORDERS_SUMMARY, $buyback_request_group_id, tep_date_long(date("Y-m-d")." 00:00:00"), 
											$select_supplier_info_row['customers_email_address'], $VIP_TRADE_MODE[$dealing_type], $admin_buyback_product_list, 
											$buyback_amount_display, array_search($buyback_status, $BUYBACK_ORDER_STATUS_ARRAY))
											."\n\n" . EMAIL_VIP_ORDER_GUIDE . "\n" .EMAIL_LOCAL_STORE_EMAIL_SIGNATURE;
												
						$admin_email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);			
						for ($i=0; $i < count($admin_email_to_array); $i++) {
							tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], $email_subject, $email_greeting . $admin_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						}
					}
					
					/*
					$aft_obj = new anti_fraud();
					$aft_module = $aft_obj->getAftModule();
					$aft_module->set_order_id($buyback_request_group_id);
					$aft_module->set_customers_id($_SESSION['customer_id']);
					$aft_module->set_transaction_type('BO');  // Customers Order /Buyback Order
					$aft_module->execute_query_call(); // execute the API call*/
				}
				
				//$concurrentProcessObj->concurrent_process_cleanup();
				usleep(1500000); // For make sure to run $concurrentProcessObj->concurrent_process_matching() b4 update or delete the record;
				$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
											page_name = '".FILENAME_MY_VIP_ORDERS_HISTORY.":DONE', 
											match_case = '".$vipOrderObj->order_detail['orders_id'].':~:'.$max_qty."' 
											WHERE temp_id = '".(int)$concurrentProcessObj->concurrent_process_insert_id."'";
				tep_db_query($update_temp_process_sql);
			}
		}
		
		$memcache_obj->delete(TABLE_VIP_ORDER_ALLOCATION . '/vip_allocation_list/xml/customers_id/'.$_SESSION['customer_id'], 0);
	    tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
	    
		break;
	case 'search':
	default:
		//Display pending orders on initial load (no post)
		//Assigned here so we know what to render in the switch() below.
		if (!tep_not_null($action))	$action = 'search';
		
		if (isset($_POST['vipodh_input_order_status_select']) && trim($_POST['vipodh_input_order_status_select'])) {
			$vipodh_input_order_status_select = tep_db_prepare_input($_POST['vipodh_input_order_status_select']);
		}
		if (isset($_POST['vipodh_input_product_type_select'])) $vipodh_input_product_type_select = tep_db_prepare_input($_POST['vipodh_input_product_type_select']);
		if (isset($_POST['vipodh_input_order_no'])) $vipodh_input_order_no = tep_db_prepare_input($_POST['vipodh_input_order_no']);
		if (isset($_POST['vipodh_input_game_select'])) $vipodh_input_game_select = tep_db_prepare_input($_POST['vipodh_input_game_select']);		
		$vipodh_input_start_date = isset($_POST['vipodh_input_start_date']) ? tep_db_prepare_input($_POST['vipodh_input_start_date']) : date('Y-m-d');
		
		if (tep_not_null($vipodh_input_start_date)) {
			if (strstr($vipodh_input_start_date, ':')) {
				//user selected time as well.
				if (!strstr($vipodh_input_start_date, '00:00:00')) {
					$vipodh_input_start_date = $vipodh_input_start_date.':00';
				}
			} else {
				//user did not select time
				$vipodh_input_start_date = $vipodh_input_start_date.' 00:00:00';
			}
		}
		
		$vipodh_input_end_date = isset($_POST['vipodh_input_end_date']) ? tep_db_prepare_input($_POST['vipodh_input_end_date']) : date('Y-m-d');
		
		if (tep_not_null($vipodh_input_end_date)) {
			if (strstr($vipodh_input_end_date, ':')) {
				//user selected time as well.
				if (!strstr($vipodh_input_end_date, '23:59:59')) {
					$vipodh_input_end_date = $vipodh_input_end_date.':59';
				}
			} else {
				//user did not select time
				$vipodh_input_end_date = $vipodh_input_end_date.' 23:59:59';
			}
		}
		
		$date_range = 0;
				
		if ((isset($_POST['vipodh_input_start_date']) && tep_not_null($_POST['vipodh_input_start_date'])) && 
		(isset($_POST['vipodh_input_end_date']) && tep_not_null($_POST['vipodh_input_end_date']))) {
			$date_range = tep_day_diff($vipodh_input_start_date, $vipodh_input_end_date);
			
			if ($date_range > $allow_search_num_days_ago) {
				$vipodh_input_end_date = tep_date_ctrl($vipodh_input_start_date, '+', 'day', $allow_search_num_days_ago);
			}
		} else {
			if (isset($_POST['vipodh_input_start_date']) && tep_not_null($_POST['vipodh_input_start_date'])) {
				$date_range = tep_day_diff($vipodh_input_start_date, date("Y-m-d 23:59:59"));
				
				if ($date_range > $allow_search_num_days_ago) {
					$vipodh_input_end_date = date("Y-m-d H:i:s", strtotime($vipodh_input_start_date." +".$allow_search_num_days_ago." day"));
				}
			} elseif (isset($_POST['vipodh_input_end_date']) && tep_not_null($_POST['vipodh_input_end_date'])) {
				$vipodh_input_start_date = date("Y-m-d H:i:s", strtotime($vipodh_input_end_date." -".$allow_search_num_days_ago." day"));
			}
		}
		break;
}

/**
 * Save form vars to session
 */
if($new_session || $action == 'search'){
	$_SESSION[$form_session_name]['vipodh_input_order_status_select'] = $vipodh_input_order_status_select;
	$_SESSION[$form_session_name]['vipodh_input_product_type_select'] = $vipodh_input_product_type_select;
	$_SESSION[$form_session_name]['vipodh_input_order_no'] = $vipodh_input_order_no;
	$_SESSION[$form_session_name]['vipodh_input_game_select'] = $vipodh_input_game_select;
	$_SESSION[$form_session_name]['vipodh_input_start_date'] = $vipodh_input_start_date;
	$_SESSION[$form_session_name]['vipodh_input_end_date'] = $vipodh_input_end_date;
} else {
	$vipodh_input_order_status_select = $_SESSION[$form_session_name]['vipodh_input_order_status_select'];
	$vipodh_input_product_type_select = $_SESSION[$form_session_name]['vipodh_input_product_type_select'];
	$vipodh_input_order_no = $_SESSION[$form_session_name]['vipodh_input_order_no'];
	$vipodh_input_game_select = $_SESSION[$form_session_name]['vipodh_input_game_select'];
	$vipodh_input_start_date = $_SESSION[$form_session_name]['vipodh_input_start_date'];
	$vipodh_input_end_date = $_SESSION[$form_session_name]['vipodh_input_end_date'];
	$start_date = $vipodh_input_start_date;
	$end_date = $vipodh_input_end_date;
}

/**
 * Start preparing search form. Save doing this if redirecting on error.
 */

$product_type_arr[] = array('id' => 'game_currency', 'text' => TEXT_GAME_CURRENCY);

//Order statuses. Reflects the processing cycle.
$order_status_arr = tep_get_buyback_status(true);

//All order status
$default_order_status_array = array(array('id' => '0', 'text' => TEXT_ALL_ORDER_STATUS));
$order_status_arr = array_merge($default_order_status_array, $order_status_arr);

//Game listing
$wbb_game_list_arr = tep_get_game_list_arr(array(array('id' => '0', 'text' => TEXT_ALL_GAMES)));

/**
 * Prepare for display results in div
 */
//Default
$col_titles = array();
$col_titles[0] = array('width' => 45, 'align' => 'left', 'title' => TEXT_ORDER_NO);
$col_titles[1] = array('width' => 55, 'align' => 'left', 'title' => TABLE_HEADING_DELIVERY_METHOD);
$col_titles[2] = array('width' => 110, 'align' => 'left', 'title' => TEXT_SERVER);
$col_titles[3] = array('width' => 80, 'align' => 'left', 'title' => TABLE_HEADING_RECEIVER_CHAR_NAME);
$col_titles[5] = array('width' => 40, 'align' => 'center', 'title' => TABLE_HEADING_ORDER_QUANTITY);
$col_titles[6] = array('width' => 70, 'align' => 'center', 'title' => TABLE_HEADING_CONFIRM_QUANTITY);
$col_titles[7] = array('width' => 55, 'align' => 'center', 'title' => TEXT_AMOUNT_WITH_CURRENCY);
$col_titles[8] = array('width' => 55, 'align' => 'center', 'title' => TEXT_STATUS);
$col_titles[9] = array('width' => 55, 'align' => 'center', 'title' => TEXT_ACTION);

// Begin VIP awaiting order
$vip_col_titles = array();
$vip_col_titles[0] = array('width' => 180, 'align' => 'left', 'title' => TABLE_HEADING_SERVER_NAME);
$vip_col_titles[1] = array('width' => 90, 'align' => 'left', 'title' => TABLE_HEADING_ORDER_AWAITING_ACCEPT_QUANTITY);
$vip_col_titles[2] = array('width' => 90, 'align' => 'left', 'title' => TABLE_HEADING_SUBMIT_QUANTITY);
$vip_col_titles[3] = array('width' => 90, 'align' => 'left', 'title' => TABLE_HEADING_SENDERS_CHARACTER_ID);
$vip_col_titles[4] = array('width' => 160, 'align' => 'left', 'title' => TABLE_HEADING_PRICE_PER_UNIT);
$vip_col_titles[5] = array('width' => 120, 'align' => 'left', 'title' => TABLE_HEADING_ACTION);

//End VIP awaiting order

$num_titles = count($col_titles);
$form_values_arr = $_SESSION[$form_session_name];
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
