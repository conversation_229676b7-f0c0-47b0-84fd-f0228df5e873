<?php

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_VIP_REGISTER_SERVER);

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	if (is_object($navigation)) {
		$navigation->set_snapshot();
	}
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_VIP_REGISTER_SERVER;
//Define the javascript file

$cID = isset($_REQUEST['cID']) ? $_REQUEST['cID'] : '';
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

$active_game_array = array(array('id' => 0, 'text' => PULL_DOWN_DEFAULT));
tep_get_vip_categories($active_game_array);

$vipSupObj = new vip_supplier($customer_id);

switch ($action) {
	case 'save_setting':
		//hardcode set default time zone for china.
		$time_zone = 'GMT +8';
		$working_time = array();
		$start_working_time = isset($_POST['start_working_time']) ? $_POST['start_working_time'] : '';
		$end_working_time = isset($_POST['end_working_time']) ? $_POST['end_working_time'] : '';
		$working_days = isset($_POST['working_days']) ? $_POST['working_days'] : '';
		
		if (substr($start_working_time, 0, 2) > substr($end_working_time, 0, 2)) {
			$messageStack->add_session($content, JS_ERROR_INVALID_WORKING_TIME, 'error');
			tep_redirect(tep_href_link(FILENAME_MY_VIP_REGISTER_SERVER, 'cID=' . $_REQUEST['cID']));	
		}
		
		if (tep_not_null($start_working_time) && tep_not_null($end_working_time)) {
			$working_time[] = $start_working_time;
			$working_time[] = $end_working_time;
		}
		
		$vipSupObj->set_supplier_setting($content, $cID, 'WORKING_DAYS', $working_days, $messageStack, SUCCESS_VIP_WORKING_DAYS_UPDATED);
		$vipSupObj->set_supplier_setting($content, $cID, 'WORKING_TIME', $working_time, $messageStack, SUCCESS_VIP_WORKING_TIME_UPDATED);
		$vipSupObj->set_supplier_setting($content, $cID, 'TIME_ZONE', $time_zone, $messageStack);
		tep_redirect(tep_href_link(FILENAME_MY_VIP_REGISTER_SERVER, 'cID=' . $_REQUEST['cID']));
		break;
}

$vipSupObj->get_supplier_setting($cID);

$working_days_array = array();
$working_hours_array = array();
if (isset($vipSupObj->game_settings['WORKING_DAYS'])) {
	$working_days_array = explode (',', $vipSupObj->game_settings['WORKING_DAYS']);
}

if (isset($vipSupObj->game_settings['WORKING_TIME'])) {
	$working_time_array = explode (',', $vipSupObj->game_settings['WORKING_TIME']);
}

//$javascript = 'vip_xmlhttp.js';
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_VIP_REGISTER_SERVER, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>