<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

if ( !isset($_SERVER['HTTP_REFERER']) || (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE) ) {
	echo '<response>You are not allowed to access from outside</response>';
	exit;
}

require_once(DIR_WS_FUNCTIONS . 'supplier.php');
include_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'edit_order.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'affiliate.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	//$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	//$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

$action = $HTTP_GET_VARS['action'];

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

// Internal Language
$languages_id = (isset($_SESSION['languages_id'])) ? $_SESSION['languages_id'] : '';

// Webpage Language
$sup_languages_id = (isset($_SESSION['languages_id'])) ? $_SESSION['languages_id'] : '';
$sup_language = (isset($_SESSION['language'])) ? $_SESSION['language'] : '';
$customer_id = isset($_SESSION['customer_id']) ? (int)$_SESSION['customer_id'] : '';
$start_date = isset($HTTP_GET_VARS['start_date']) ? urldecode($HTTP_GET_VARS['start_date']) : '';
$end_date = isset($HTTP_GET_VARS['end_date']) ? urldecode($HTTP_GET_VARS['end_date']) : '';
$buyback_cat_id = isset($HTTP_GET_VARS['game_id']) ? (int)$HTTP_GET_VARS['game_id'] : '';

$order_status_id = isset($_GET['order_status_id']) ? $_GET['order_status_id'] : '';
$product_type = isset($_GET['product_type']) ? $_GET['product_type'] : '';
$order_no = isset($_GET['order_no']) ? (int)$_GET['order_no'] : '';
$game_cat_id = isset($_GET['game_cat_id']) ? (int)$_GET['game_cat_id'] : '';

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case 'get_captcha_img':
			echo '<captcha_img><![CDATA['.tep_image('securimage_show.php?sid='.md5(uniqid(time()))).']]></captcha_img>';
			break;
		case 'full_server_list':
			$list_by_aphabet = isset($HTTP_GET_VARS['list_by']) && tep_not_null($HTTP_GET_VARS['list_by']) ? $HTTP_GET_VARS['list_by'] : 'A';
			
            $xml_str = "<servers>\n";
            if ($buyback_cat_id > 0) {
                $buybackSupplierObj = new buyback_supplier($buyback_cat_id);
                $vipSupObj = new vip_supplier($customer_id);
                
                if (isset($buybackSupplierObj->products_arr) && is_array($buybackSupplierObj->products_arr)) {
                	foreach ($buybackSupplierObj->products_arr as $pid => $pinfo) {
						$pinfo['categories_name'] = strip_tags($pinfo['categories_name']);
						//if (strtoupper(substr($pinfo['categories_name'], 0, 1)) == strtoupper($list_by_aphabet)) {
							$xml_str .= '<server>';
							$xml_str .= '<id><![CDATA['.$pid.']]></id>';
							$xml_str .= '<name><![CDATA['.$pinfo['categories_name'].']]></name>';
							
							if (isset($vipSupObj->registered_servers[$pid])) {
								$xml_str .= '<registered><![CDATA[1]]></registered>';
								$xml_str .= '<qty><![CDATA['.$vipSupObj->registered_servers[$pid]['qty'].']]></qty>';
								$xml_str .= '<link><![CDATA[<a href="javascript:;" onClick="vip_del_inv(this, '.$pid.')"><b>'.BUTTON_DELETE.'</b></a>]]></link>';
							} else {
								$xml_str .= '<registered><![CDATA[0]]></registered>';
								$xml_str .= '<qty><![CDATA['.tep_draw_input_field('inv_qty', '', ' size="10" maxlength="10" id="inv_qty_'.$pid.'" ').']]></qty>';
								$xml_str .= '<link><![CDATA[<a href="javascript:;" onClick="vip_add_inv(this, '.$pid.')"><b>'.BUTTON_ADD.'</b></a>]]></link>';
							}
							
							$xml_str .= "</server>\n";
						//}
					}
				}
            }
            
            $xml_str .= "</servers>\n";
            
            echo $xml_str;
            
			break;
		case 'add_server':
			if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_register_server.php")) {
				include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_register_server.php");
			}
			
			$product_id = isset($HTTP_GET_VARS['pid']) && tep_not_null($HTTP_GET_VARS['pid']) ? $HTTP_GET_VARS['pid'] : '';
			$qty = isset($HTTP_GET_VARS['qty']) && tep_not_null($HTTP_GET_VARS['qty']) ? (int)$HTTP_GET_VARS['qty'] : '';
			$vipSupObj = new vip_supplier($customer_id);
			
			if ($qty) {
				$old_qty = $vipSupObj->get_old_qty($product_id);
				if($old_qty > 0){
					$qty = $old_qty;
				} else {
					$vipSupObj->update_inventory($product_id, $qty, 'insert');
				}
				
				$xml_str = '<qty><![CDATA['.$qty.']]></qty>';
				$xml_str .= '<link><![CDATA[<a href="javascript:;" onClick="vip_del_inv(this, '.$pid.')"><b>'.BUTTON_VIP_REG_SERVER_DELETE_SERVER.'</b></a>]]></link>';
			} else {
				$xml_str = '<error>1</error>';
			}
			
			echo $xml_str;
			
			break;
    	case 'del_server':
    		if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_register_server.php")) {
				include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_register_server.php");
			}
			
			$product_id = isset($HTTP_GET_VARS['pid']) && tep_not_null($HTTP_GET_VARS['pid']) ? $HTTP_GET_VARS['pid'] : '';
			$vipSupObj = new vip_supplier($customer_id);
			$vipSupObj->delete_inventory($product_id);
			
			if (isset($_GET['return_xml']) && $_GET['return_xml'] == 'no') {
				
			} else {
				$xml_str = '<qty><![CDATA['.tep_draw_input_field('inv_qty', '', ' size="10" maxlength="10" id="inv_qty_'.$pid.'" ').']]></qty>';
				$xml_str .= '<link><![CDATA[<a href="javascript:;" onClick="vip_add_inv(this, '.$pid.')"><b>'.BUTTON_VIP_REG_SERVER_ADD_SERVER.'</b></a>]]></link>';
				echo $xml_str;
			}
			break;
		case 'get_vip_offer':
			if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php")) {
				include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php");
			}
			
            require_once(DIR_WS_CLASSES . 'anti_robot.php');
            $anti_robot_obj = new anti_robot();
            
            if ($anti_robot_obj->traffic_handling() === TRUE) {
                echo '<gotoURL>' . tep_href_link(FILENAME_WAITING_ROOM) . '</gotoURL>';
                echo '</response>';
                exit;
            }
            
            unset($anti_robot_obj);
            
			tep_xmlhttp_get_vip_awaiting_order();
			break;
    	case 'get_vip_order':
	    	if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php")) {
				include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php");
			}
			
    	    $xml_str = '';
    	    $allow_search_num_days_ago = 100;
			
			$start_date_str = $end_date_str = ' 1 ';
			
			if (tep_not_null($start_date)) {
				if (strpos($start_date, ':') !== false) {
					$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
				} else {
					$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . " 00:00:00'";
				}
			} else {
//				$sformat = 'Y-m-d H:i:s';
//				$start_date = date($sformat);
//				$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
			}
			
			if (tep_not_null($end_date)) {
				if (strpos($end_date, ':') !== false) {
					$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
				} else {
					$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . " 23:59:59'";
				}
			} else {
//				$eformat = 'Y-m-d H:i:s';
//				$end_date = date($eformat);
//				$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
			}
			
			$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
												br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed,  br.restock_character AS saved_restock_character,
												br.buyback_dealing_type, br.buyback_request_supplier_code, br.orders_products_id, p.products_cat_id_path, brg.buyback_request_order_type, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name 
											FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
											LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON brg.buyback_request_group_id=br.buyback_request_group_id
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (br.products_id = p.products_id)
											INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
												ON (brg.buyback_status_id=bs.buyback_status_id AND bs.language_id='".$sup_languages_id."')
											WHERE brg.customers_id = '" . tep_db_input($customer_id) . "' 
												AND brg.buyback_request_order_type = '1' 
											 	AND " . $start_date_str . " 
											 	AND " . $end_date_str . "
												AND br.orders_id <> 0
												AND br.orders_products_id <> 0 ";
			
			if ($order_status_id != 0) {
			    $buyback_request_select_sql .= " AND brg.buyback_status_id = '".$order_status_id."' ";
			}
			if ($order_no) {
				$buyback_request_select_sql .= " AND brg.buyback_request_group_id = '$order_no'";
			}
			
			$cat_parent_path = tep_get_categories_parent_path($game_cat_id);
			if (tep_not_null($cat_parent_path)) {
				$cat_parent_array = explode("_",$cat_parent_path);
				$top_parent_id = $cat_parent_array[1];
				
				$cat_parent_path .= $game_cat_id."_";
			} else {
				$cat_parent_path = "_".$game_cat_id."_";
				$top_parent_id = (int)$game_cat_id;
			}
			
			if ((int)$game_cat_id>0) {
				$cat_parent_path = str_replace('_', '\_', $cat_parent_path);
				$buyback_request_select_sql .= " AND p.products_cat_id_path LIKE '".$cat_parent_path."%'";
			}

			$buyback_request_select_sql .= "\n         GROUP BY brg.buyback_request_group_id
											           ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc ";
											           
			$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
			$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);
			
			$xml_str .= "\n<results>";
			while ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
				$product_cat_path_array = explode("_",$buyback_request_row['products_cat_id_path']);
				$buyback_request_row['categories_id'] = $product_cat_path_array[count($product_cat_path_array)-2];	// Last element is '_'
				
				$order_latest_remark = '';
				$allow_to_upload = 'no';
				
				$currencies->set_decimal_places($currencies->currencies[$buyback_request_row['currency']]['decimal_places']);
    			
    			// $allow_to_upload: for the user who havent upload the ss yet.
    			if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
					$allow_to_upload = 'yes';
				}
    			
				$order_latest_remark = '';
		
				//Get restock character.
				$show_restock = 0;
				$supplier_trade_code = '*******';
				
				if ((int)$buyback_request_row['buyback_status_id'] != 1) {
			        $receiver_char_name_message = '';
			  	} else {
			  		$show_restock = (int)$buyback_request_row['show_restock'];
//			    	if ($total_queue == 0) {
			    		if ($show_restock) {
			    			if ((int)$buyback_request_row['buyback_status_id'] == 1) {
			    				
			    				$receiver_char_name_message = vip_order::tep_get_customer_trading_char($buyback_request_row['orders_products_id']);	
			    				$supplier_trade_code = $buyback_request_row['buyback_request_supplier_code'];
			    			} 
//			    			else if ($buyback_request_row['buyback_dealing_type'] == 'vip_deal_on_game' && (int)$buyback_request_row['buyback_status_id'] == 1) {
//			    				
//			    				if (tep_not_null($buyback_request_row['saved_restock_character'])) {
//			    					$receiver_char_name_message = $buyback_request_row['saved_restock_character'];
//			    				} else {
//			    					$receiver_char_name_message = tep_get_buyback_restock_char($buyback_request_row['products_id']);
//			    				}
//			    			}
			    			
			    			
			    		} else {
			    			$receiver_char_name_message = TEXT_CHARACTER_NAME_IN_PREPARATION;
			    		}
//				    } else if ($total_queue > 0) {
//				        $receiver_char_name_message = sprintf(TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE, $total_queue);
//				    }
				}
				
				$delivery_info_select_sql = "	SELECT opei.orders_products_extra_info_value 
												FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (opei.orders_products_id = op.parent_orders_products_id)
												WHERE op.orders_products_id = '".(int)$buyback_request_row['orders_products_id']."' 
													AND opei.orders_products_extra_info_key = 'delivery_mode'";
				$delivery_info_result_sql = tep_db_query($delivery_info_select_sql);
				if ($delivery_info_row = tep_db_fetch_array($delivery_info_result_sql)) {
					$trade_mode = tep_get_supplier_trade_mode($delivery_info_row['orders_products_extra_info_value']);
					$delivery_method = tep_display_supplier_trade_mode($trade_mode);
				}
	
				$buyback_main_cat_name = tep_get_buyback_main_cat_info($buyback_request_row['categories_id']);
				$product_path_name = tep_display_category_path(tep_get_product_path_name($buyback_request_row['products_id']), $buyback_request_row['categories_id'], 'catalog', false);
				$buyback_game_name = (defined('DISPLAY_NAME_CAT_ID_'.$buyback_main_cat_name['id'])? constant('DISPLAY_NAME_CAT_ID_'.$buyback_main_cat_name['id']) : $buyback_main_cat_name['text']);
				
				$xml_str .= "\n<row>";
				$xml_str .= "\n<req_grp_id><![CDATA[".$buyback_request_row['buyback_request_group_id']."]]></req_grp_id>";
				$xml_str .= "\n<req_id><![CDATA[".$buyback_request_row['buyback_request_id']."]]></req_id>";
				$xml_str .= "\n<game_name><![CDATA[".$buyback_game_name."]]></game_name>";
				$xml_str .= "\n<delivery_method><![CDATA[".tep_display_supplier_trade_mode($buyback_request_row['buyback_dealing_type'])."]]></delivery_method>";
				$xml_str .= "\n<server_name><![CDATA[".$product_path_name."]]></server_name>";
				$xml_str .= "\n<req_qty><![CDATA[".$buyback_request_row['buyback_request_quantity']."]]></req_qty>";
				$xml_str .= "\n<confirmed_qty><![CDATA[".$buyback_request_row['buyback_quantity_confirmed']."]]></confirmed_qty>";
				$xml_str .= "\n<amount><![CDATA[".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."]]></amount>";
				$xml_str .= "\n<show_restock><![CDATA[".$show_restock."]]></show_restock>";
				$xml_str .= "\n<restock_character><![CDATA[".$receiver_char_name_message."]]></restock_character>";
				$xml_str .= "\n<status_name><![CDATA[".$buyback_request_row['buyback_status_name']."]]></status_name>";
				$xml_str .= "\n<current_status_id><![CDATA[".$buyback_request_row['buyback_status_id']."]]></current_status_id>";
				$xml_str .= "\n<trade_type><![CDATA[".$buyback_request_row['buyback_dealing_type']."]]></trade_type>";
				$xml_str .= "\n<trade_method><![CDATA[".$delivery_method."]]></trade_method>";
				// allow_to_upload: for the user who havent upload the ss yet.
				$xml_str .= "\n<allow_to_upload><![CDATA[".$allow_to_upload."]]></allow_to_upload>";
				
				//Get expirable	
				$show_expiry = 0;
				$expiry_time = '00:00:00';
				if ((int)$buyback_request_row['buyback_status_id'] == 1 || (int)$buyback_request_row['buyback_status_id'] == 2) {
					if ((int)$buyback_request_row['buyback_status_id'] == 2) {
						$total_received_select_sql = "SELECT sum(br.buyback_quantity_received) as total_received 
														FROM ".TABLE_BUYBACK_REQUEST." AS br
														WHERE br.buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
														GROUP BY buyback_request_group_id";
						$total_received_result_sql = tep_db_query($total_received_select_sql);
						if ($total_received_row = tep_db_fetch_array($total_received_result_sql)) {
							$total_received = (int)$total_received_row['total_received'];
						}
						if ($total_received == 0) {
							$show_expiry = 1;
						}
					} else {
						$show_expiry = 1;
					}
					
					if ($show_expiry) {
						$expiry_time = gmdate('H:i:s', strtotime($buyback_request_row['buyback_request_group_expiry_date'])+ (8*60*60));
					}	
				} 
				
				$buyback_remark_select_sql = "	SELECT comments 
												FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
												WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
													AND set_as_buyback_remarks = 1 
													AND customer_notified = 1 
													AND (comments IS NOT NULL AND comments <> '')";
				$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
				$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
				$order_latest_remark = $buyback_remark_row['comments'];
			
				$xml_str .= "\n<show_expiry><![CDATA[".$show_expiry."]]></show_expiry>";
				$xml_str .= "\n<expiry_time><![CDATA[".$expiry_time."]]></expiry_time>";
				$xml_str .= "\n<buyback_remarks><![CDATA[".nl2br($order_latest_remark)."]]></buyback_remarks>";
				$xml_str .= "\n</row>";
			}
			$xml_str .= "\n</results>";
			$xml_str .= "\n<num_results><![CDATA[".$buyback_request_num_rows."]]></num_results>";
			echo $xml_str;

    		break;
    	case 'update_buyback_request_group':
    		if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php")) {
					include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "vip_orders_history.php");
			}
			
			$supplier_email = tep_get_customers_email($_SESSION['customer_id']);
			$log_object = new log_files($supplier_email); //keep supplier email address
			
    		$buyback_request_group_id = isset($_POST['req_grp_id']) ? (int)$_POST['req_grp_id'] : 0;
			$buyback_quantity_confirmed = isset($_POST['sent_qty']) ? (int)$_POST['sent_qty'] : 0;
			$buyback_request_id = isset($_POST['req_id']) ? (int)$_POST['req_id'] : 0;
			$buyback_request_customers_code = 0;
			
    		if($buyback_request_group_id && $buyback_request_id && $buyback_quantity_confirmed){
				$restock_character = '';
				// look for VIP order only
				$buyback_request_select_sql = "	SELECT br.products_id, br.buyback_unit_price, br.buyback_request_quantity, br.buyback_dealing_type, brg.buyback_status_id,
													br.orders_id, br.orders_products_id, br.buyback_request_customer_org_code, brg.buyback_request_group_date, br.restock_character, l.locking_by
												FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
												INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id)
												LEFT JOIN " . TABLE_LOCKING . " AS l
													ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
												WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
													AND brg.buyback_status_id = 1
													AND br.orders_id <> 0
													AND br.orders_products_id <> 0
													AND brg.customers_id = '".$_POST['customer_id']."' 
													AND br.buyback_request_id='" . tep_db_input($buyback_request_id) . "'";
				$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
				if ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
					$vipOrderObj = new vip_order($buyback_request_row['orders_products_id']);
					
					$vipOrderObj->get_orders_details();
					$buyback_request_quantity = $buyback_request_row['buyback_request_quantity'];
					$products_id = $buyback_request_row['products_id'];
					$orders_products_id = $buyback_request_row['orders_products_id'];
					
					if (!tep_not_null($buyback_quantity_confirmed) || ($buyback_quantity_confirmed != $buyback_request_quantity)) {
					//if (!is_numeric($buyback_quantity_confirmed) || $buyback_quantity_confirmed < 0 || $buyback_quantity_confirmed > $buyback_request_quantity) {
						echo '<error_msg>'.TEXT_ERROR_INVALID_QTY.'</error_msg>';
					} else {
						$restock_character = $vipOrderObj->tep_get_customer_trading_char($orders_products_id);
						$error = false;
						
						// Temporary Processing to prevent insert unwanted record;
						$matchcase = md5($buyback_request_group_id.':~:'.$buyback_request_row['buyback_status_id']);
						$concurrentProcessObj = new concurrent_process('vip_xmlhttp.php', $matchcase, $buyback_quantity_confirmed);
						
						//usleep(rand(500000, 2000000)); // Delay execution in microseconds
						usleep(1500000);
						if($concurrentProcessObj->concurrent_process_matching()) {
							if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
								$error = true;
								echo '<error_msg>'.ERROR_SUBMITTED_BY_OTHER_USER.'</error_msg>';
							}
						}
						
						if(!$error) {
							$game_name = '';
							//no error
							$update_confirmed_qty_array = array('buyback_quantity_confirmed' => $buyback_quantity_confirmed,
																'restock_character' => $restock_character,
																'buyback_request_customer_matching_code' => $buyback_request_customers_code
																);
							tep_db_perform(TABLE_BUYBACK_REQUEST, $update_confirmed_qty_array, 'update', "buyback_request_group_id='$buyback_request_group_id' AND buyback_request_id='$buyback_request_id'");
							//Automatically set status to complete if customer code match & Hide character status // else hold on processing and tag it.
							$buyback_status_id = 0;
							$buyback_quantity_received = $buyback_quantity_confirmed;
							
							if ($buyback_request_row['buyback_dealing_type'] == 'ofp_deal_with_customers' || $buyback_request_row['buyback_dealing_type'] == 'vip_deal_on_game') {	//  Trade code removed as requested - KIM
								$buyback_status_id = 2;
								//update buyback quantity received
								$update_buyback_request = "	UPDATE " . TABLE_BUYBACK_REQUEST . " 
															SET buyback_quantity_received = '" . $buyback_quantity_received . "' 
															WHERE buyback_request_group_id='" . $buyback_request_group_id ."' 
																AND buyback_request_id='" . $buyback_request_id . "'";
								tep_db_query($update_buyback_request);
								
								//update buyback_status and hide character name
								$update_buyback_status_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP ." 
															  	SET buyback_status_id='" . $buyback_status_id . "', 
															  	show_restock='0', 
															  	buyback_request_group_last_modified=now() 
															 	WHERE buyback_request_group_id='" . $buyback_request_group_id . "'";
								tep_db_query($update_buyback_status_sql);
								
								$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
																	'buyback_status_id' => $buyback_status_id,
																	'date_added' => 'now()',
																	'customer_notified' => '0',
																	'comments' => COMMENTS_VIP_TRADE_COMPLETED
																	);
								tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
								
								if ($buyback_status_id == '3') {
									$buyback_status_history_update_arr = array('set_as_buyback_remarks' => '0');
									tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_status_history_update_arr, 'update', "buyback_request_group_id= '" . (int)$buyback_request_group_id . "'");
									
									$buyback_history_complete_array = array('buyback_request_group_id' => $buyback_request_group_id,
																			'buyback_status_id' => '0',
																			'date_added' => 'now()',
																			'customer_notified' => '1',
																			'set_as_buyback_remarks' => '1',
																			'comments' => COMMENTS_PROCESSING_TO_COMPLETED
																	);
									tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_complete_array);
								}
								
								//update product qty
								$product_info_select_sql = "SELECT products_skip_inventory, products_main_cat_id 
															FROM " . TABLE_PRODUCTS . " 
															WHERE products_id = '" . tep_db_input($products_id) . "'";
								$product_info_result_sql = tep_db_query($product_info_select_sql);
								if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
									$game_name = tep_get_categories_name($product_info_row['products_main_cat_id']);
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 	'products_quantity',
																							'operator'=> '+', 
																							'value'=> $buyback_quantity_received),
																	array(	'field_name'=> 	'products_actual_quantity',
																							'operator'=> '+', 
																							'value'=> $buyback_quantity_received)
																);
									}
									tep_set_product_qty($products_id, $update_qty_array, true, sprintf(LOG_BUYBACK_ORDER, $buyback_request_group_id), '');
								}
								
								//update the quantity to order product.
								$edit_order_obj = new edit_order($vipOrderObj->order_detail['orders_id']);
								$customer_email =  tep_get_customers_email($customer_id);
								$edit_order_obj->deliver_order($buyback_quantity_received, $vipOrderObj->order_detail['orders_products_id'], $customer_email, true, $messageStack);
								$edit_order_obj->set_customers_comment($vipOrderObj->order_detail['orders_id'], '0', sprintf(TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK, $buyback_request_group_id), '0');
								
								//check any others order are assigned for the same order with deal with customers
								$pending_order_select_sql = "SELECT br.buyback_dealing_type
															FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
															INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
																ON (brg.buyback_request_group_id = br.buyback_request_group_id)
															WHERE brg.buyback_status_id = '1'
																AND br.orders_id = '" . tep_db_input($vipOrderObj->order_detail['orders_id']) . "'
																AND br.orders_products_id <> 0
																AND buyback_dealing_type = 'ofp_deal_with_customers'";
								$pending_order_select_result = tep_db_query($pending_order_select_sql);
								if (tep_db_num_rows($pending_order_select_result) == 0) {
									//check is system lock the order
									$check_order_lock_sql = "	SELECT orders_locked_by 
							    								FROM " . TABLE_ORDERS . " 
							    								WHERE orders_id = '" . tep_db_input($vipOrderObj->order_detail['orders_id']) . "'";
									$check_order_lock_result = tep_db_query($check_order_lock_sql);
									if ($check_order_lock_row = tep_db_fetch_array($check_order_lock_result)) {
										if ($check_order_lock_row['orders_locked_by'] == 0) {
											//unlock order
											tep_customer_order_locking($vipOrderObj->order_detail['orders_id'], '0', 'unlock');
										}
									}
								}
							}
							
							$char_name = $received = '';
							$confirmation_duration = 0;
							$insert_orders_products_history_1 = $insert_orders_products_history_2 = array();
							
							//Update to TABLE_ORDERS_PRODUCTS_HISTORY
							$get_parent_id_select_sql = "	SELECT parent_orders_products_id 
															FROM " . TABLE_ORDERS_PRODUCTS . " 
															WHERE orders_products_id = '".(int)$buyback_request_row['orders_products_id']."'";
							$get_parent_id_result_sql = tep_db_query($get_parent_id_select_sql);
							if($get_parent_id_row = tep_db_fetch_array($get_parent_id_result_sql)){
								$extra_info_array = tep_draw_products_extra_info($get_parent_id_row['parent_orders_products_id']);
								$char_name = $extra_info_array['char_name'];
								if (!tep_customer_delivery_confirmation($extra_info_array['delivery_mode'])) {
									$insert_orders_products_history_1 = array('received' => '1');
								}
								$buyback_main_cat = tep_get_buyback_main_cat_info($products_id, 'product');
								
								$confirmation_duration_select_sql = "	SELECT buyback_setting_value 
																		FROM " . TABLE_BUYBACK_SETTING . " 
																		WHERE buyback_setting_table_name = 'buyback_categories' 
																		AND buyback_setting_reference_id = '".(int)$buyback_main_cat['id']."' 
																		AND buyback_setting_key = 'customer_confirmation_duration' LIMIT 1";
								$confirmation_duration_result_sql =	tep_db_query($confirmation_duration_select_sql);
								if ($confirmation_duration_row = tep_db_fetch_array($confirmation_duration_result_sql)) {
									$confirmation_duration = $confirmation_duration_row['buyback_setting_value'];
								}
							}
							
							$insert_orders_products_history_2 = array(	'buyback_request_group_id' => $buyback_request_group_id,
												                        'orders_id' => $buyback_request_row['orders_id'],
																		'orders_products_id' => $buyback_request_row['orders_products_id'],
																		'date_added' => 'now()',
																		'last_updated' => 'now()',
																		'date_confirm_delivered' => date("Y-m-d H:i:s", mktime(date("H"), date("i")+(int)$confirmation_duration, date("s"), date("m"), date("d"), date("Y"))),
																		'delivered_amount' => $buyback_quantity_confirmed,
																		'delivered_character' => $char_name,
																		'changed_by' => $customer_email
																		);
																		
							$insert_orders_products_history = array_merge($insert_orders_products_history_1, $insert_orders_products_history_2);
							tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);
							
							if (tep_not_null($buyback_request_row['locking_by'])) {
								$vipOrderObj->order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, $_POST['customer_id']);
							}
							/*
							$rstk_char_date_select_sql = "	SELECT date_added, NOW() as today_date
															FROM " . TABLE_BUYBACK_STATUS_HISTORY . " 
															WHERE buyback_request_group_id = '".(int)$buyback_request_group_id."' 
																AND comments = 'Show Restock Character'";
							$rstk_char_date_result_sql = tep_db_query($rstk_char_date_select_sql);
							if ($rstk_char_date_row = tep_db_fetch_array($rstk_char_date_result_sql)) {
								$mins = tep_day_diff($rstk_char_date_row['date_added'], $rstk_char_date_row['today_date'], 'sec') / 60;
								if ($mins < 2) {
									$admin_email = "Vince <<EMAIL>>, KenLee <<EMAIL>>";
									$admin_email_to_array = tep_parse_email_string($admin_email);
									$email_content = 'Buyback Order: '.$buyback_request_group_id."\n";
									$email_content .= 'Open RSTK Time: '.$rstk_char_date_row['date_added']."\n";
									$email_content .= 'Confirm Buyback Order Time: '.$rstk_char_date_row['today_date']."\n\n";
									
									$email_subject = '[OffGamers] Alert - Buyback #'.$buyback_request_group_id.' Completed in short period ('.$game_name.')';
						        	for ($email_cnt=0; $email_cnt < count($admin_email_to_array); $email_cnt++) {
						        		@tep_mail($admin_email_to_array[$email_cnt]['name'], $admin_email_to_array[$email_cnt]['email'], $email_subject, $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						        	}
								}
							}
							*/
							echo '<result>updated</result>';
						}
						
						//$concurrentProcessObj->concurrent_process_cleanup();
						usleep(1500000);
						$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
													page_name = 'vip_xmlhttp.php:DONE', 
													match_case = '".$buyback_request_group_id.':~:'.$buyback_request_row['buyback_status_id']."' 
													WHERE temp_id = '".(int)$concurrentProcessObj->concurrent_process_insert_id."'";
						tep_db_query($update_temp_process_sql);
					}
				} else {
					// can't find buyback request order
					echo '<error_msg>'.WARNING_INVALID_ORDERS.'</error_msg>';
				}

			} else {
				// invalid buyback request group no, buyback request no, confirm qty.
				echo '<error_msg>'.TEXT_ERROR_INVALID_QTY.'</error_msg>';
			}
    		break;
    	case 'redirect_upload_to_curl':
    			$customer_id = $_SESSION['customer_id'];
    			$req_grp_id = $_GET['req_grp_id'];
    			$file_name_1 = $_GET['file_name_1'];
    			$file_name_2 = $_GET['file_name_2'];
    			$sup_language = $_GET['sup_language'];
    			
    			$max_file_size = '800'; // KB
				$allow_file_type = array('jpg');
				$result = '';
				$result2 = '';
				$error = "";
				
				if (!empty($file_name_1)) {
					$result = check_uploading_file ($file_name_1, $allow_file_type, $max_file_size);
				}
				
				if (!empty($file_name_2)) {
					$result2 = check_uploading_file ($file_name_2, $allow_file_type, $max_file_size);
				}
				
				if ($result == 'approved' && $result2 == 'approved') {
					$file_1 = $_FILES[$file_name_1]['tmp_name'];
					$file_2 = $_FILES[$file_name_2]['tmp_name'];
	    			unset($response);
	    			
                    $url = tep_upload_href_link(FILENAME_UPLOAD, "action=ajax_upload&customer_id=".$customer_id."&req_grp_id=".$req_grp_id."&file_name_1=".$file_name_1."&file_name_2=".$file_name_2."&sup_language=".$sup_language, 'SSL');
                    $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL,$url);
                    curl_setopt($ch, CURLOPT_VERBOSE, 1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                    curl_setopt($ch, CURLOPT_POST, 1);
					curl_setopt($ch, CURLOPT_POSTFIELDS, array($file_name_1 => "@$file_1", $file_name_2 => "@$file_2"));
                    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
                    $response = curl_exec($ch);
                    curl_close($ch);

                    echo $response;
				} else {
					if ($result != 'approved') {
						echo '<error_msg_1><![CDATA['.$result.']]></error_msg_1>';
					}
					if ($result2 != 'approved') {
						echo '<error_msg_2><![CDATA['.$result2.']]></error_msg_2>';
					}
				}
    		break;
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';

function check_uploading_file ($fileElementName, $allow_file_type, $max_file_size) {
	$msg = '';
	$pathinfo_arr = pathinfo($_FILES[$fileElementName]['name']);
	$file_type = strtolower($pathinfo_arr['extension']);
	
	if (($max_file_size * 1024) > $_FILES[$fileElementName]['size']) {
		if ($_FILES[$fileElementName]['error'] > 0) {
			$msg = $_FILES[$fileElementName]['error'];
		} elseif (empty($_FILES[$fileElementName]['tmp_name']) || $_FILES[$fileElementName]['tmp_name'] == 'none') {
			$msg = '8';
		} else {
			if (in_array($file_type, $allow_file_type)) {
				$msg = 'approved';
			} else {
				$msg = '9';
			}
		}
	} else {
		$msg = '10';
	}
	return $msg;
}

function tep_xmlhttp_product_buyback_info($buyback_parent_cat_id, $buyback_products_id) {
	global $currencies, $sup_languages_id;
	
	$xml_str = '';
    $showError = false;
    $min_value = 0;
    $max_value = 0;
    $product_name = '&nbsp;';
    $product_id = '&nbsp;';
	
    if ($buyback_parent_cat_id > 0) {
        $actual_cat_id = tep_get_actual_product_cat_id($buyback_products_id);
		
		$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
		$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
		$buybackSupplierObj->calculate_offer_price();
		if (!isset($buybackSupplierObj->products_arr[$buyback_products_id])) {
			$showError = true;
		} else {
			$product_info_arr = $buybackSupplierObj->products_arr[$buyback_products_id];
			if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
				$showError = true;
			}
		}
    }
	
    if ($showError) {
        $xml_str .= "<error>3</error>";
        $xml_str .= "<notice>0</notice>";
    } else {
       	$unit_price_base = (float)$product_info_arr['avg_offer_price'];	/*USD*/
		$unit_price_localised = $currencies->do_raw_conversion($unit_price_base, true, $_SESSION['currency']);	/*Localised*/
    	$unit_price_localised_display = $currencies->format($unit_price_base, true, $_SESSION['currency']);
		
        $xml_str = "<error>0</error>";
        $xml_str .= "<notice>1</notice>";
	    $xml_str .= "<product_id><![CDATA[".$buyback_products_id."]]></product_id>";
	    $xml_str .= "<product_name><![CDATA[".$product_info_arr['categories_name']."]]></product_name>";
		$xml_str .= "<unit_price><![CDATA[".$unit_price_localised."]]></unit_price>";
	    $xml_str .= "<unit_price_display><![CDATA[".$unit_price_localised_display."]]></unit_price_display>";
        $xml_str .= "<min><![CDATA[".$product_info_arr['min_qty']."]]></min>";
        $xml_str .= "<max><![CDATA[".$product_info_arr['max_qty']."]]></max>";
	    $xml_str .= "<product_unit_name><![CDATA[".$product_info_arr['qty_unit']."]]></product_unit_name>";
	    
	    if (isset($product_info_arr['upper_min_qty']) && $product_info_arr['upper_min_qty'] > 0 && $product_info_arr['upper_min_qty'] < $product_info_arr['max_qty']) {
			$xml_str .= "<bo_exact_qty_msg><![CDATA[".sprintf(TEXT_MATCH_BACKORDER_EXACT_AMOUNT, $product_info_arr['upper_min_qty'], $product_info_arr['max_qty'], $product_info_arr['upper_min_qty']+1, $product_info_arr['max_qty']-1)."]]></bo_exact_qty_msg>";
		}
    }
    
    echo '<selected_product>';
    echo $xml_str;
    echo '</selected_product>';
    
    unset($buybackProductObj);
}

function tep_xmlhttp_get_vip_awaiting_order() {
	global $memcache_obj;
	$xml_str = '';
	
	$cache_key = TABLE_VIP_ORDER_ALLOCATION . '/vip_allocation_list/xml/customers_id/'.$_SESSION['customer_id'];
	
	$cache_result = $memcache_obj->fetch($cache_key);
	
	if ($cache_result !== FALSE) {
		$xml_str = $cache_result;
	} else {
		$awaiting_vip_order_sql = 	"SELECT voa.orders_products_id, voa.customers_id, voa.products_id, voa.vip_order_allocation_quantity, voa.vip_order_allocation_time, op.orders_id, op.parent_orders_products_id 
									FROM " . TABLE_VIP_ORDER_ALLOCATION . " AS voa 
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
										ON(voa.orders_products_id=op.orders_products_id) 
									WHERE voa.customers_id='" . $_SESSION['customer_id'] . "'";
		$awaiting_vip_order_result = tep_db_query($awaiting_vip_order_sql);
		$vip_row_count = 0;
		$vip_order_awaiting_num_row = tep_db_num_rows($awaiting_vip_order_result);
		
		$xml_str .= "\n<results>";
		while($awaiting_vip_order_row = tep_db_fetch_array($awaiting_vip_order_result)){
			$vipOrderObj = new vip_order($awaiting_vip_order_row['orders_products_id']);
			$vipOrderObj->get_orders_details();
			$selected_product_id = $vipOrderObj->order_detail['products_id'];
			$selected_cat_id = $vipOrderObj->order_detail['buyback_categories_id'];
			$request_cancellation_duration = '';
			$trade_customers_price = '';
			$trade_customers_price_display = '';
			$trade_us_price = '';
			$trade_us_price_display = '';
			if ($vipOrderObj->check_vip_mode($selected_cat_id) && !$vipOrderObj->order_request_expired() && $vipOrderObj->check_purchase_eta($awaiting_vip_order_row['orders_products_id'], $selected_product_id)){
				
				//get vip request cancellation duration
				$request_cancellation_duration = ($vipOrderObj->vip_request_cancellation_duration * 60);
				
				$trade_customers_price = "\n<trade_customers_price><![CDATA[0]]></trade_customers_price>";
				$trade_customers_price_display = "\n<trade_customers_price_display><![CDATA[0]]></trade_customers_price_display>";
				if (in_array('ofp_deal_with_customers', $vipOrderObj->trade_mode)) {
					$vipOrderObj->calculate_trade_customers_price($_SESSION['customer_id']);
					$trade_customers_price = "\n<trade_customers_price><![CDATA[" . ( isset($vipOrderObj->order_detail['trade_customers_price']) ? $vipOrderObj->order_detail['trade_customers_price'] : '0' ) . "]]></trade_customers_price>";
					$trade_customers_price_display = "\n<trade_customers_price_display><![CDATA[" . ( isset($vipOrderObj->order_detail['trade_customers_price_display']) ? $vipOrderObj->order_detail['trade_customers_price_display'] : '0' ) . "]]></trade_customers_price_display>";
				}
				
				$trade_us_price = "\n<trade_us_price><![CDATA[0]]></trade_us_price>";
				$trade_us_price_display = "\n<trade_us_price_display><![CDATA[0]]></trade_us_price_display>";
				if (in_array('vip_deal_on_game', $vipOrderObj->trade_mode)) { 
					$vipOrderObj->calculate_trade_us_price();
					$trade_us_price = "\n<trade_us_price><![CDATA[" . ( isset($vipOrderObj->order_detail['trade_us_price']) ? $vipOrderObj->order_detail['trade_us_price'] : '0' ) . "]]></trade_us_price>";
					$trade_us_price_display = "\n<trade_us_price_display><![CDATA[" . ( isset($vipOrderObj->order_detail['trade_us_price_display']) ? $vipOrderObj->order_detail['trade_us_price_display'] : '0' ) . "]]></trade_us_price_display>";
				}
			}
			
			if (tep_not_null($trade_customers_price) || tep_not_null($trade_us_price)) {
				$game_name = (defined('DISPLAY_NAME_CAT_ID_'.$vipOrderObj->order_detail['buyback_categories_id']) ? constant ('DISPLAY_NAME_CAT_ID_'.$vipOrderObj->order_detail['buyback_categories_id']) : $vipOrderObj->order_detail['buyback_categories_name']);
				$expiry_time = gmdate('H:i:s', strtotime($awaiting_vip_order_row['vip_order_allocation_time'])+ (8*60*60) + $request_cancellation_duration);
				
				//get qty range for this product
				$backorder_qty_msg = '';
				$buybackSupplierObj = new buyback_supplier($selected_cat_id, $selected_product_id);
				$buybackSupplierObj->vip = true;
				$buybackSupplierObj->calculate_offer_price();
				// check available qty
				$max_qty = (int)$vipOrderObj->tep_get_max_qty($selected_product_id, $awaiting_vip_order_row['orders_products_id'], $_SESSION['customer_id']);
				$min_qty = (int)$buybackSupplierObj->vip_min_qty;
				$min_purchase_qty = (int)$buybackSupplierObj->vip_min_purchse_qty;
				
				if($min_purchase_qty > 0 && $min_qty > 0 && $min_purchase_qty < $max_qty){
					$upper_min_qty = (int)$max_qty - $min_purchase_qty;
					$backorder_qty_msg .= "<bo_exact_qty_msg><![CDATA[".sprintf(TEXT_MATCH_BACKORDER_EXACT_AMOUNT, $upper_min_qty, $max_qty, $upper_min_qty+1, $max_qty-1)."]]></bo_exact_qty_msg>";
				}
				//$backorder_qty_msg .= "<bo_exact_qty_msg><![CDATA[" . sprintf(TEXT_MATCH_BACKORDER_AMOUNT_ONLY, $max_qty ) . "]]></bo_exact_qty_msg>";
				
				$orders_products_id = $awaiting_vip_order_row['parent_orders_products_id'] == 0 ? $awaiting_vip_order_row['orders_products_id'] : $awaiting_vip_order_row['parent_orders_products_id'];
				$delivery_id = tep_draw_products_extra_info($orders_products_id, 'delivery_mode');
				if (tep_not_null($delivery_id['delivery_mode'])) {
					$trade_mode = tep_get_supplier_trade_mode($delivery_id['delivery_mode']);
					$delivery_method = tep_display_supplier_trade_mode($trade_mode);
					$trading_method = sprintf(TEXT_TRADE_METHOD, $delivery_method);
				}
				
				$xml_str .= "\n<orders>";
				$xml_str .= "\n<orders_products_id><![CDATA[" . $awaiting_vip_order_row['orders_products_id'] . "]]></orders_products_id>";
				$xml_str .= "\n<game_name><![CDATA[" . $game_name . "]]></game_name>";
				$xml_str .= "\n<path_name><![CDATA[" . $vipOrderObj->order_detail['category_cat_path'] . "]]></path_name>";
				$xml_str .= "\n<quantity><![CDATA[" . $max_qty . "]]></quantity>";
				$xml_str .= "\n<allocate_time><![CDATA[" . $expiry_time . "]]></allocate_time>";
				$xml_str .= "\n<trading_method><![CDATA[" . $trading_method . "]]></trading_method>";
				$xml_str .= $trade_customers_price;
				$xml_str .= $trade_customers_price_display;
				$xml_str .= $trade_us_price;
				$xml_str .= $trade_us_price_display;
				$xml_str .= $backorder_qty_msg;
				$xml_str .= "\n</orders>";
			}
		}
		$xml_str .= "\n</results>";
		$xml_str .= "\n<total_order><![CDATA[" . $vip_order_awaiting_num_row . "]]></total_order>";
		
		$memcache_obj->store($cache_key, $xml_str, 300);	// Store for 300 seconds / 5 minutes
	}
	echo $xml_str;
}
?>