<?php
// include server parameters
require_once('includes/configure.php');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
if (!isset($PHP_SELF))
    $PHP_SELF = $_SERVER['PHP_SELF'];

if ($request_type == 'NONSSL') {
    define('DIR_WS_CATALOG', DIR_WS_HTTP_CATALOG);
} else {
    define('DIR_WS_CATALOG', DIR_WS_HTTPS_CATALOG);
}

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');
// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');
// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');
require(DIR_WS_FUNCTIONS . 'general.php');
// Customer info verifications functions
require(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
require(DIR_WS_CLASSES . 'anti_robot.php');
require(DIR_WS_CLASSES . 'recaptcha.php');

// set the application parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

// set the cookie domain
$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
$filename = basename($_SERVER['SCRIPT_FILENAME']);
$REMOTE_ADDR = tep_get_ip_address();

// define how the session functions will be used
require(DIR_WS_FUNCTIONS . 'sessions.php');

// set the session name and save path
tep_session_name('OFFGAMERS'); //osCsid
tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
if (function_exists('session_set_cookie_params')) {
    session_set_cookie_params(0, $cookie_path, $cookie_domain);
} elseif (function_exists('ini_set')) {
    ini_set('session.cookie_lifetime', '0');
    ini_set('session.cookie_path', $cookie_path);
    ini_set('session.cookie_domain', $cookie_domain);
}

// set the session ID if it exists
if (isset($_POST[tep_session_name()])) {
    tep_session_id($_POST[tep_session_name()]);
} elseif (($request_type == 'SSL') && isset($_GET[tep_session_name()])) {
    tep_session_id($_GET[tep_session_name()]);
}

// start the session
if (SESSION_FORCE_COOKIE_USE == 'True') {
    tep_setcookie('cookie_test', 'please_accept_for_session', time() + 60 * 60 * 24 * 30, $cookie_path, $cookie_domain);

    if (isset($_COOKIE['cookie_test'])) {
        tep_session_start();
    }
} elseif (SESSION_BLOCK_SPIDERS == 'True') {
    $user_agent = strtolower(getenv('HTTP_USER_AGENT'));
    tep_session_start();
} else {
    tep_session_start();
}

$memcache_obj = new OGM_Cache_MemCache();
$anti_robot_obj = new anti_robot();
$required_to_wait = $anti_robot_obj->waiting_handling();

if (!isset($_SESSION['anti_robot_captcha']) || $required_to_wait !== TRUE || !isset($_SESSION['customer_id'])) {
    // redirect to prev page.
    $redirect_url = $anti_robot_obj->getReturnURL('/');
    tep_redirect($redirect_url);
} else {
    if ($anti_robot_obj->isCaptchaRequiredRequest() && isset($_POST['recaptcha_response_field'])) {
        if (recaptcha::captcha_validation($_POST['recaptcha_challenge_field'],$_POST['recaptcha_response_field']) === true) {
            $anti_robot_obj->updateCaptchaFlag($success = 1);
        }
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html dir="LTR" lang="en" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <META HTTP-EQUIV="Expires" CONTENT="Tue, 04 Dec 2005 21:29:02 GMT">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<?php
    if ($anti_robot_obj->isCaptchaRequiredRequest() !== true) {
        echo '<meta http-equiv="Refresh" content="180">';
    }
?>
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js "></script>
        <script>
            var count = 180;
            function handleTimer() {
              if(count === 0) {
                clearInterval(timer);
              } else {
                $('#count_num').html(count);
                count--;
              }
            }
            $(document).ready(function(){
                if($('#count_num').length){
                    var timer = setInterval(function() { handleTimer(count); }, 1000);
                }
            });
        </script>
    </head>
    <body style="font-family: arial, sans-serif; background-color: #fff; color: #000; padding:20px; font-size:18px;">
<?php
    if ($anti_robot_obj->isCaptchaRequiredRequest()) {
?>
        <div style="max-width:442px;">
            <hr size="1" noshade="" style="color:#ccc; background-color:#ccc;">
            <br>
            To continue, please type the characters below:
            <br><br>
            <form method="post">
<?php
            recaptcha :: login_captcha_html();
?>
                <div align="right"><input type="submit" name="submit" value="Submit"></div>
            </form>
            <br>
            <hr size="1" noshade="" style="color:#ccc; background-color:#ccc;">
            <div style="font-size:13px;">
                <b>About this page</b>
                <br><br>
                Our systems have detected unusual traffic from your computer network. This page checks to see if it's really you sending the requests, and not a robot.
            </div>
        </div>
<?php
    } else {
        if ($anti_robot_obj->isPhoneVerifiedRequest()) {
?>
            <div style="max-width:442px;font-size:15px;">
            Sorry, your had hit the maximum allowed visits to the requested page.<br>
            Page will refresh in <span id="count_num">180</span> seconds to update your page visit status
            <br><br>
            Please wait...
            </div>
<?php
        } else {
?>
            <div style="max-width:442px;font-size:15px;">
            Sorry, your requested page had reached maximum allowed visits.<br>
            Page will refresh in <span id="count_num">180</span> seconds to update your queue status
            <br><br>
            Please wait... or <a href="<?php echo HTTP_SERVER . DIR_WS_HTTP_CATALOG . FILENAME_ACCOUNT_ACTIVATE . '?verify=phone'; ?>">verify your mobile</a> to direct access.
            </div>
<?php
        }
    }
    
    unset($anti_robot_obj);
?>
    </body>
</html>
