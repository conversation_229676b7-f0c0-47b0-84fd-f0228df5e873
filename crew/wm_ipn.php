<?
/*
  	$Id: wm_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: WebMoney Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/wm/classes/wm_ipn_class.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

//Process payment
if (isset($_POST) && count($_POST)) {
	$payment = 'webmoney';
    
    // load selected payment module
    include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'payment.php');
    
    $payment_modules = new payment($payment);
    
    $currency_code_select_sql = "	SELECT pmi.currency_code 
    								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " AS pmi 
    								INNER JOIN " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " AS pmis 
    									ON (pmis.payment_methods_instance_id = pmi.payment_methods_instance_id) 
    								WHERE pmis.payment_methods_instance_setting_key = 'MODULE_PAYMENT_WEBMONEY_ID' 
    									AND pmis.payment_methods_instance_setting_value = '" . tep_db_input(trim($_POST['LMI_PAYEE_PURSE'])) . "' 
    								LIMIT 1";
    $currency_code_result_sql = tep_db_query($currency_code_select_sql);
    $currency_code_row = tep_db_fetch_array($currency_code_result_sql);
    
    $$payment->get_merchant_account($currency_code_row['currency_code']);
    
    $wm_ipn = new wm_ipn($_POST);
    
    include_once(DIR_WS_CLASSES . 'order.php');
    $orders_id = $wm_ipn->get_order_id();
    $order = new order($orders_id);
    
	if (isset($_POST['LMI_PREREQUEST'])) {
		if ($_POST['LMI_PREREQUEST'] == '1') {	// Prerequest form
			/********************************************************
				(1)	Varify the merchant's purse
	   			(3)	Verify the payment amount
			********************************************************/
			if ($wm_ipn->validate_receiver_account($$payment->webmoney_id)) {
				if ($wm_ipn->validate_transaction_data($$payment, $order, true)) {
					echo 'Yes';	// WebMoney will only proceed if we respond 'Yes'
					return;
				}
			}
		}
	} else {
		/********************************************************
			(1)	Verify if the data have been sent by Merchant WebMoney Transfer (data source)
   			(2)	Verify if the data haven't been modified (data integrity)
   			(3)	Verify the payment amount
   			(4)	Verify the merchant's purse
   			(5)	Verify the payment mode (test or operative mode) 
		********************************************************/
		include_once(DIR_WS_CLASSES . 'log.php');
		$log_object = new log_files('system');
		
		if ($wm_ipn->validate_receiver_account($$payment->webmoney_id)) {
			if ($wm_ipn->validate_transaction_data($$payment, $order, false)) {
				if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
					$payment_success = $wm_ipn->authenticate($order, $orders_id, $$payment);
					/*
					if ($payment_success) {
						tep_redirect(tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'));
					}
					*/
				}
			}
		}
	}
}
?>