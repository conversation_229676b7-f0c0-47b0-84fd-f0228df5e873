<?
/*
  	$Id: wpcallback.php,v MS1a 2003/04/06 21:30
  	Author : 	<PERSON> (grae<PERSON>@conkie.net)
  	Title: WorldPay Payment Callback Module V4.0 Version 1.4
	
  	Revisions:
		Version 1.5 Cleaned up code, moved static English to language file to allow for bi-lingual use,
	        		Now posting language code to WP, Redirect on failure now to Checkout Payment,
					Reduced re-direct time to 8 seconds, added MD5, made callback dynamic 
					NOTE: YOU MUST CHANGE THE CALLBACK URL IN WP ADMIN TO <wpdisplay item="MC_callback">
		Version 1.4 Removes boxes to prevent users from clicking away before update, 
				Fixes currency for Yen, 
				Redirects to Checkout_Process after 10 seconds or click by user
		Version 1.3 Fixes problem with Multi Currency
		Version 1.2 Added Sort Order and Default order status to work with snapshots after 14 Jan 2003
		Version 1.1 Added Worldpay Pre-Authorisation ability
		Version 1.0 Initial Payment Module

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003
  Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_WPCALLBACK);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_WPCALLBACK, '', 'NONSSL'));

if ($_REQUEST["MC_oGM"] == $OFFGAMERS) {
	/********************************************************
		NOTE: authMode = E (for pre-authorisation)
						 A (for immediate authorisation)
	********************************************************/
	
	$payment = 'worldpay';
	
	include_once(DIR_WS_CLASSES . 'payment.php');
	$payment_modules = new payment($payment);
	
	$$payment->get_merchant_account($_REQUEST['authCurrency']);
	
	if (tep_not_null($$payment->callback_password) && trim($$payment->callback_password) != $_REQUEST['callbackPW']) {
		if (tep_session_is_registered('wp_order_logged')) tep_session_unregister('wp_order_logged');
	} else {
		$wp_data_array = array(	'orders_id' => tep_db_prepare_input($_REQUEST['cartId']), 
						  		'transaction_id' => tep_db_prepare_input($_REQUEST['transId']),
						  		'transaction_status' => ($transStatus == "Y" ? 1 : 0),
						  		'transaction_amount' => tep_db_prepare_input($_REQUEST['amount']),
	                      		'transaction_currency' => tep_db_prepare_input($_REQUEST['authCurrency']),
	                      		'transaction_text_amount' => tep_db_prepare_input(html_entity_decode($_REQUEST['authAmountString'])),
	                      		'credit_card_type' => tep_db_prepare_input($_REQUEST['cardType']),
	                      		'credit_card_owner' => tep_db_prepare_input($_REQUEST['name']),
	                      		'email_address' => tep_db_prepare_input($_REQUEST['email']),
	                      		'billing_address' => tep_db_prepare_input($_REQUEST['address']),
	                      		'country' => tep_db_prepare_input($_REQUEST['countryString']),
	                      		'country_code' => tep_db_prepare_input($_REQUEST['country']),
	                      		'ip_address' => tep_db_prepare_input($_REQUEST['ipAddress']),
	                      		'tel' => tep_db_prepare_input($_REQUEST['tel']),
	                      		'fax' => tep_db_prepare_input($_REQUEST['fax']),
	                      		'check_result' => tep_db_prepare_input($_REQUEST['AVS']),
	                      		'alert_message' => tep_db_prepare_input($_REQUEST['wafMerchMessage']),
	                      		'authorisation_mode' => tep_db_prepare_input($_REQUEST['authMode'])
	                      		);
		if (isset($_REQUEST['authentication'])) {
			$wp_data_array['cardholder_aut_result'] = tep_db_prepare_input($_REQUEST['authentication']);
		}
		
		tep_db_perform(TABLE_PAYMENT_EXTRA_INFO, $wp_data_array);
		
		if (!tep_session_is_registered('wp_order_logged')) tep_session_register('wp_order_logged');
		$wp_order_logged = 'true';
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<? require(DIR_WS_INCLUDES . 'meta_tags.php'); ?>
<title><?=META_TAG_TITLE?></title>
<base href="<? echo (getenv('HTTPS') == 'on' ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG; ?>">
<link rel="stylesheet" type="text/css" href="<?php echo DIR_WS_IMAGES . 'css/'.(isset($language_code) && tep_not_null($language_code) ? 'stylesheet_'.$language_code.'.css' : 'stylesheet_en.css'); ?>">
</head>
<?
if (isset($transStatus) && $transStatus == "Y") {
?>
	<body onload="return document.wp_payment_success.submit();">
<?
		$url = tep_href_link(FILENAME_CHECKOUT_PROCESS, $cartId, 'NONSSL', false); 
		echo "\n".tep_draw_form('wp_payment_success', $url, 'post');
 		echo tep_draw_hidden_field('transStatus', $transStatus) . "\n";
?>
		</form>
	</body>
<?
} else {
?>
	<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0">
	<!-- header //-->
	<!-- header_eof //-->
	
	<!-- body //-->
		<table border="0" width="100%" cellspacing="3" cellpadding="3">
	  		<tr>
	    		<td width="<?=BOX_WIDTH?>" valign="top">
	    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="0" cellpadding="2">
					<!-- left_navigation //-->
					<?
						// Next line Removed for version 1.4 and above
						// require(DIR_WS_INCLUDES . 'column_left.php'); 
						// Delete next line if using column
						echo tep_draw_separator('pixel_trans.gif', '100%', '10');
					?>
					<!-- left_navigation_eof //-->
	    			</table>
	    		</td>
	<!-- body_text //-->
	    		<td width="100%" valign="top">
	    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
	    				<tr>
	    					<td><?=tep_draw_separator('pixel_trans.gif', '10', '100')?></td>
	    				</tr>
	<?
	// Failure
	//$url = tep_href_link(FILENAME_DEFAULT, '', 'NONSSL', false); 
	$url = tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_WORLDPAY_TEXT_ERROR_MESSAGE), 'SSL', true, false);
	//echo "<meta http-equiv='Refresh' content='3; Url=\"$url\"'>";
	?>
	    				<tr>
	      					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '50')?></td>
	    				</tr>
	    				<tr>
	    					<td align="center" colspan="2">
	    						<table border="0" width="70%" cellspacing="0" cellpadding="2" class="infoBox">
									<tr class="infoBoxContents">
										<td>
											<table align="center" border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td align="center" class="headerErrorTitle">
														<?=HEADING_TITLE?>
													</td>
												</tr>
												<tr>
							    					<td><?=tep_draw_separator('pixel_trans.gif', '10', '10')?></td>
							    				</tr>
							    				<tr>
													<td align="left" class="spacingInstruction">
														<?=WP_TEXT_FAILURE?>
		  											</td>
		  										</tr>
		  										<tr>
							    					<td><?=tep_draw_separator('pixel_trans.gif', '10', '10')?></td>
							    				</tr>
		  										<tr>
		  											<td>
						            					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						            						<tr>
						                						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						                						<td class="main" align="right"><? echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, $url) ; ?></td>
						                						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						                					</td>
						                				</table>
						                			</td>
						                		</tr>
		  										<tr>
				      								<td><?=tep_draw_separator('pixel_trans.gif', '100%', '30')?></td>
				    							</tr>
				    							<tr>
													<td class="spacingInstruction">
														<?=WP_TEXT_HEADING?>
														<br><br>
		  												<? echo '<WPDISPLAY ITEM=banner>'?>
		  												<br><br>
		  											</td>
		  										</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
	    			</table>
	    		</td>
	<!-- body_text_eof //-->
	    		<td width="<?=BOX_WIDTH?>" valign="top">
	    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="0" cellpadding="2">
	<!-- right_navigation //-->
	<?
	// Next line Removed for version 1.4
	// require(DIR_WS_INCLUDES . 'column_right.php'); 
	// Delete next line if using column
	echo tep_draw_separator('pixel_trans.gif', '100%', '10');
	?>
	<!-- right_navigation_eof //-->
	    			</table>
	    		</td>
	  		</tr>
		</table>
	<!-- body_eof //-->
	
	<!-- footer //-->
	<?
	// Next line Optional for version 1.4 or above
	// require(DIR_WS_INCLUDES . 'footer.php'); 
	?>
	<!-- footer_eof //-->
	</body>
<?
}
?>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>