<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <script type="text/javascript">
<?php
$res = '';
$refreshBar = 0;
$xTitle = '';
if (isset($_GET['res']) && !empty($_GET['res'])) {
    $res = json_decode(base64_decode($_GET['res']), true);
}

if (isset($_GET['refreshBar']) && !empty($_GET['refreshBar'])) {
    $refreshBar = $_GET['refreshBar'];
}

if (isset($_GET['xTitle']) && !empty($_GET['xTitle'])) {
    $xTitle = $_GET['xTitle'];
}
?>
<?php if (isset($_GET['rtype']) && !empty($_GET['rtype'])) { ?>
                window.onload =
                        function() {
    <?php
    switch ($_GET['rtype']) {
        case 'login':
            if ($res) {
                ?>
                                        window.top.xdomainLoginPopup("<?php echo $res['xSrc']; ?>", "<?php echo $xTitle; ?>");
                <?php
            }
            break;
        case 'regional':
            if ($res) {
                ?>
                                        window.top.xdomainRegionalPopup("<?php echo $res['xSrc']; ?>", "<?php echo $xTitle; ?>");
                <?php
            }
            break;
        case 'profile':
            if ($res) {
                if (isset($_GET['cStyle']) && !empty($_GET['cStyle'])) {
                    ?>
                                            window.top.xdomainProfileMenu("<?php echo $res['xSrc']; ?>", "<?php echo $_GET['cStyle']; ?>");
                    <?php
                }
            }
            break;
        case 'ogm':
            if ($res) {
                if (isset($_GET['cStyle']) && !empty($_GET['cStyle'])) {
                    ?>
                                            window.top.xdomainSelectNextorks("<?php echo $res['xSrc']; ?>", "<?php echo $_GET['cStyle']; ?>");
                    <?php
                }
            }
            break;
        case 'loginPopupSize':
            ?>
                                    window.top.setPopupHeight(0, 'login');
            <?php
            break;
        case 'forgotPasswordSize':
            ?>
                                    window.top.setPopupHeight(0, 'fpassword');
            <?php
            break;
        case 'loginCaptchaSize':
            ?>
                                    window.top.setPopupHeight(1, 'login');
            <?php
            if ($refreshBar == 1) {
                ?>
                                        window.top.reloadTopNavigate();
                <?php
            }
            break;
        case 'pm':
            if ($res) {
                if (isset($_GET['cStyle']) && !empty($_GET['cStyle'])) {
                    ?>
                                            window.top.xdomainPmList("<?php echo $res['xSrc']; ?>", "<?php echo $_GET['cStyle']; ?>");
                    <?php
                }
            }
            break;
        case 'pmHeight':
            if (isset($_GET['total'])) {
                $height = (int) $_GET['total'];
                ?>
                                        window.top.adjustMenuHeight('<?php echo 'xdomainPmList'; ?>', '<?php echo $height; ?>');
                <?php
            }
            break;
        case 'order':
            if ($res) {
                if (isset($_GET['cStyle']) && !empty($_GET['cStyle'])) {
                    ?>
                                            window.top.xdomainOrder("<?php echo $res['xSrc']; ?>", "<?php echo $_GET['cStyle']; ?>");
                    <?php
                }
            }
            break;
        case 'chatMsg':
            if (isset($_GET['message'])) {
                ?>
                                        alert('<?php echo $_GET['message']; ?>');
                <?php
            }
            break;
        case 'expressLogin':
            if ($res) {
                ?>
                                        window.top.popupExpressLogin("<?php echo $res['xSrc']; ?>", "<?php echo $xTitle; ?>");
                <?php
            }
            break;
    }
    ?>
                        };
<?php } ?>
        </script>
    </head>
    <body></body>
</html>