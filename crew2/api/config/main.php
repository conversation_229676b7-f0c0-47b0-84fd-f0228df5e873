<?php

use offgamers\base\components\ElasticSearch;
use yii\helpers\ArrayHelper;

$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/../../common/config/params-encoded.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'open-api',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'api\controllers',
    'bootstrap' => ['log'],
    'modules' => [],
    'components' => [
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'request' => [
            'enableCookieValidation' => false,
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ],
        ],
        'response' => [
            'class' => 'yii\web\Response',
            'on beforeSend' => function ($event) {
                $response = $event->sender;
                if ($response->format === 'json') {
                    $response->statusCode = $response->statusCode >= 300 ? 200 : $response->statusCode;
                    $response->data = [
                        'success' => $response->isSuccessful,
                        'response' => $response->data,
                    ];
                }
                \offgamers\base\models\IncomingRequestLog::updateOutput($response);

                $tags = ArrayHelper::getValue(Yii::$app->params, 'es_incoming_log_data.tags', []);
                $startTime = ArrayHelper::getValue(Yii::$app->params, 'es_incoming_log_data.start_time', 0);

                $request = Yii::$app->request;
                $response = [
                    'code' => $response->getStatusCode(),
                    'body' => $response->data,
                ];
                ElasticSearch::storeIncomingLog($request, $response, $tags, $startTime);
            },
            'formatters' => [
                \yii\web\Response::FORMAT_JSON => [
                    'class' => 'yii\web\JsonResponseFormatter',
                    'prettyPrint' => YII_DEBUG,
                    'encodeOptions' => JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE,
                ],
            ],
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
            ],
        ],
    ],
    'params' => $params,
];
