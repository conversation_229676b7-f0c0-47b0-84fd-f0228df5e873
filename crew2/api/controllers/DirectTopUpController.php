<?php

namespace api\controllers;

use Yii;
use yii\web\BadRequestHttpException;
use api\models\PimaPostBackForm;

class DirectTopUpController extends \yii\web\Controller
{
    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionUpdateStatus($publisher_id, $top_up_id, $orders_id, $signature)
    {
        $model = new PimaPostBackForm();
        $model->load(Yii::$app->request->get(), '');
        $model->loadData();

        if ($model->validate()) {
            if (Yii::$app->request->isPost && $model->allowUpdate()) {
                $model->load(Yii::$app->request->post(), '');
                $model->updateTopUpStatus();
                echo "<script>window.close();</script>";
                Yii::$app->end();
            } elseif($model->allowUpdate()) {
                return $this->renderPartial('update', ['orders_id' => $orders_id]);
            }
        }

        throw new BadRequestHttpException();
    }
}