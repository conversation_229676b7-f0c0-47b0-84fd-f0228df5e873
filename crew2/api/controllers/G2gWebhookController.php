<?php

namespace api\controllers;

use common\models\resellers\G2G;
use offgamers\base\components\ElasticSearch;
use offgamers\base\models\IncomingRequestLog;
use Yii;
use yii\helpers\Json;
use yii\web\Controller;

class G2gWebhookController extends Controller
{
    private $start_time = 0;

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');
        $this->start_time = microtime(true);

        return parent::beforeAction($action);
    }

    public function actionIndex($id = 1)
    {
        $g2g = (new G2G);
        $g2g->reseller = $id;
        $g2g->getConfig();
        $request = Yii::$app->request;
        $header = $request->getHeaders();
        $signature = $header->get('g2g-signature');
        $timestamp = $header->get('g2g-timestamp');
        $url = Yii::$app->request->getAbsoluteUrl();

        if ($g2g->verifyWebhookSignature($url, $timestamp, $signature)) {
            $body = $request->getRawBody();
            $data = Json::decode($body);
            (new IncomingRequestLog())->createRequestLog(['tag' => 'G2G_' . ($data['payload']['order_id'] ?? '')]);
            ElasticSearch::init($this->start_time, ['G2G_' . ($data['payload']['order_id'] ?? '')]);

            // Fix Weird Bugs that cannot get delivery list instantly
            sleep(2);
            $g2g->processPostBack($data);
            Yii::$app->response->statusCode = 200;
            Yii::$app->end();
        }

        Yii::$app->response->statusCode = 401;
        Yii::$app->end();
    }
}