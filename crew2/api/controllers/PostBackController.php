<?php

namespace api\controllers;

use Aws\Sns\Exception\InvalidSnsMessageException;
use Aws\Sns\Message;
use Aws\Sns\MessageValidator;
use common\components\publishers\CodeWholeSale;
use common\components\publishers\DTOneDVS;
use common\models\GamePublisher;
use common\models\PublisherSetting;
use offgamers\base\components\ElasticSearch;
use offgamers\base\components\Guzzle;
use offgamers\base\models\IncomingRequestLog;
use offgamers\base\models\LogSesBounce;
use offgamers\base\models\ms\Inventory;
use Yii;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\Response;

/**
 * Site controller
 */
class PostBackController extends Controller
{
    private $start_time = 0;

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        Yii::$app->response->format = 'json';

        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');
        $this->start_time = microtime(true);

        return parent::beforeAction($action);
    }

    public function actionPing()
    {
        return true;
    }

    /*
    // Disable the function as BD discontinue selling publisher codeswholesale products
    public function actionCodeswholesale()
    {
        $data = Yii::$app->getRequest()->getBodyParams();
        $publisher_list = GamePublisher::findAll(['profile' => 'CodeWholeSale', 'status' => 1]);
        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');

        (new IncomingRequestLog())->createRequestLog();
        ElasticSearch::init($this->start_time);

        foreach ($publisher_list as $publisher) {
            if ($publisher->game_publisher_id) {
                $profile = new CodeWholeSale();
                $profile->publisher = $publisher->game_publisher_id;
                $profile->getExistingConfig();
                $hash = hash('sha256', $profile->configuration_data['API_KEY'] . $profile->configuration_data['API_SIGNATURE']);
                if ($hash == $data['authHash']) {
                    switch ($data['type']) {
                        case 'STOCK':
                            $product_list = [];
                            foreach ($data['products'] as $product) {
                                $product_list[] = $product['productId'];
                            }
                            $profile->syncProductListWithProductId($product_list);
                            break;

                        case 'PRODUCT_UPDATED':
                        case 'NEW_PRODUCT':
                            $profile->syncProduct($data['productId']);
                            break;

                        case 'PRODUCT_HIDDEN':
                            $profile->disableProduct($data['productId']);
                            break;

                        case 'PREORDER':
                            $inventory_model = new Inventory();
                            $inventory_model->checkOrder(['publisher_order_id' => $data['orderId']]);
                            break;
                    }
                }
            }
        }
        return 'success';
    }
    */

    public function actionMintRoute()
    {
        try {
            (new IncomingRequestLog())->createRequestLog();
            ElasticSearch::init($this->start_time);

            $data = Json::decode(Yii::$app->getRequest()->getRawBody());
            (new Inventory())->customRequest('restock', 'mint-route-post-back', ['data' => $data, 'signature' => Yii::$app->getRequest()->getHeaders()->get('x-ids-signature')]);
            Yii::$app->response->statusCode = 202;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to process MintRoute Batch Request', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }

        return true;
    }

    public function actionFuluPostBack($publisher_id)
    {
        try {
            (new IncomingRequestLog())->createRequestLog();
            ElasticSearch::init($this->start_time);

            $data = Json::decode(Yii::$app->getRequest()->getRawBody());
            (new Inventory())->customRequest('direct-top-up', 'fulu-post-back', ['raw_data' => $data['data'], 'publisher_id' => $publisher_id]);
            Yii::$app->response->statusCode = 200;
        } catch (\Exception $e) {
        }

        return true;
    }

    public function actionDtone($publisher_id)
    {
        try {
            (new IncomingRequestLog())->createRequestLog();
            ElasticSearch::init($this->start_time);

            $dtone = new DTOneDVS();
            $dtone->publisher = $publisher_id;
            $dtone->validatePostBack(Yii::$app->getRequest()->getAuthCredentials());
            $data = Json::decode(Yii::$app->getRequest()->getRawBody());
            (new Inventory())->customRequest('direct-top-up', 'dtone-post-back', ['data' => $data]);
            Yii::$app->response->statusCode = 200;
            return true;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to process DTOne Postback', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
        // Pretend we're not here if the message is invalid.
        http_response_code(404);
        die();
    }

    public function actionPipwaveRfi()
    {
        // custom response format for Pipwave RFI
        Yii::$app->response->on(Response::EVENT_BEFORE_SEND, function ($event) {
            $response = $event->sender;
            $response->data = $response->data['response'];
            \offgamers\base\models\IncomingRequestLog::updateOutput($response);
            $response->statusCode = 200;
        });
        // get POST data
        $data = Yii::$app->getRequest()->getBodyParams();

        // Create incoming log
        (new IncomingRequestLog())->createRequestLog();
        ElasticSearch::init($this->start_time);

        try {
            $request = Yii::$app->request;
            if ($request->isPost) {
                $pipwave = new \common\components\PipwaveRFI();
                if ($pipwave->authentication($data)) {
                    $response = $pipwave->getOrdersDetail();
                } else {
                    $response = [
                        'status' => 'fail',
                        'message' => 'Error - Authentication',
                    ];
                }

                Yii::$app->response->data = $response;
            } else {
                Yii::$app->response->data = [
                    'status' => 'fail',
                    'message' => 'Error - HTTP Method',
                ];
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send('Pipwave RFI', [
                [
                    'color' => 'warning',
                    'title' => 'Pipwave RFI Error actionPipwaveRfi()',
                    'text' => $e->getTraceAsString(),
                    'ts' => time(),
                ],
            ], 'DEBUG');

            Yii::$app->response->data = [
                'status' => 'fail',
                'message' => 'Error - Processing The Request',
            ];
        }
    }

    public function actionSnsHandler()
    {
        (new IncomingRequestLog())->createRequestLog();
        ElasticSearch::init($this->start_time);

        // Make sure the SNS-provided header exists.
        if (!isset($_SERVER['HTTP_X_AMZ_SNS_MESSAGE_TYPE'])) {
            throw new \RuntimeException('SNS message type header not provided.');
        }

        $data = Json::decode(Yii::$app->getRequest()->getRawBody());
        $message = new Message($data);

        $validator = new MessageValidator(function ($certUrl) {
            try {
                $client = new \offgamers\base\components\Guzzle();
                $response = $client->get($certUrl);
                return $response->getBody();
            } catch (\Exception $e) {
                Yii::$app->slack->send('Error on reading AWS Cert', array(
                    array(
                        'color' => 'danger',
                        'text' => json_encode($e->getMessage())
                    ),
                ), 'DEBUG');
                return false;
            }
        });

        // Validate the message and log errors if invalid.
        try {
            $validator->validate($message);
        } catch (InvalidSnsMessageException $e) {
            // Pretend we're not here if the message is invalid.
            http_response_code(404);
            error_log('SNS Message Validation Error: ' . $e->getMessage());
            die();
        }

        if (!empty(Yii::$app->params['sns.topic.arn.whitelist']) && in_array($data['TopicArn'], Yii::$app->params['sns.topic.arn.whitelist'])) {
            switch ($data['Type']) {
                case 'SubscriptionConfirmation':
                    $client = new Guzzle();
                    $client->request('GET', $data['SubscribeURL']);
                    break;

                case 'Notification':
                    $body = Json::decode($data['Message']);
                    switch ($body['notificationType']) {
                        case 'Bounce':
                            // Ignore soft bounce
                            if ($body['bounce']['bounceType'] == 'Transient') {
                                break;
                            }
                            $recipient_list = $body['bounce']['bouncedRecipients'];
                            foreach ($recipient_list as $recipient) {
                                $email_address = LogSesBounce::emailPurify($recipient['emailAddress']);
                                LogSesBounce::createBounceRecord($email_address, $body['bounce']['bounceType'], $body['bounce']['bounceSubType'], $data['Message']);
                            }
                            break;

                        case 'Complaint':
                            $recipient_list = $body['complaint']['complainedRecipients'];
                            foreach ($recipient_list as $recipient) {
                                $email_address = LogSesBounce::emailPurify($recipient['emailAddress']);
                                LogSesBounce::createBounceRecord($email_address, $body['notificationType'], '', $data['Message']);
                            }
                            break;
                    }
                    break;
            }
        }


        return true;
    }

    public function actionBamboo($publisher_id)
    {
        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');
        (new IncomingRequestLog())->createRequestLog();
        ElasticSearch::init($this->start_time);

        $data = Yii::$app->getRequest()->getBodyParams();
        $secret_key = $data['SecretKey'] ?? '';
        $request_id = $data['RequestId'] ?? '';

        if (empty($secret_key) || empty($request_id) || empty($publisher_id)) {
            // Pretend we're not here if the secret key doesn't match
            http_response_code(404);
            error_log('SecretKey, RequestId or publisher_id is not being set correctly');
            die();
        }

        $publisher_setting = PublisherSetting::find()->where([
            'publisher_id' => $publisher_id,
            'key' => 'NOTIFICATION_SECRET_KEY',
            'value' => $secret_key
        ])->exists();

        if (!$publisher_setting) {
            http_response_code(404);
            error_log('Publisher setting not found');
            die();
        }

        $inventory_model = new Inventory();
        $inventory_model->checkOrder(['publisher_order_id' => $request_id]);

        return 'success';
    }
}
