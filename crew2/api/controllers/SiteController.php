<?php

namespace api\controllers;

use Yii;
use yii\web\Controller;
use yii\web\HttpException;

class SiteController extends Controller
{

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionError(){
        $status_code = 404;
        $exception = Yii::$app->getErrorHandler()->exception;
        if($exception instanceof HttpException){
            $status_code = $exception->statusCode;
        }
        http_response_code($status_code);
        die();
    }
}