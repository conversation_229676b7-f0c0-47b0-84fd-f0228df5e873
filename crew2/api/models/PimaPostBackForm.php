<?php

namespace api\models;

use common\models\OrdersTopUp;
use common\models\OrdersTopUpRemark;
use Yii;
use yii\base\Model;
use yii\db\Expression;
use yii\db\Query;
use yii\web\BadRequestHttpException;

class PimaPostBackForm extends Model
{
    public $publisher_id;
    public $top_up_id;

    public $orders_id;

    public $signature;

    public $top_up_status;

    public $remarks;

    public $secret_key;

    public $publisher_ref_id;

    private $orders_top_up_model;

    public function rules()
    {
        return [
            [['orders_id', 'publisher_id', 'top_up_id', 'top_up_status'], 'integer'],
            [['publisher_ref_id', 'secret_key', 'signature', 'remarks'], 'string'],
            [['orders_id', 'publisher_id', 'top_up_id', 'signature'], 'required'],
            ['top_up_status', 'in', 'range' => [3, 10]],
            ['signature', 'validateSignature']
        ];
    }

    public function loadData()
    {
        $this->loadOrdersTopUpModel();
        $this->getSecretKey();
    }

    public function validateSignature($attribute, $params)
    {
        if (hash_hmac('sha256', $this->publisher_id . '&' . $this->orders_id . '&' . $this->top_up_id, $this->secret_key) !== $this->signature) {
            $this->addError($attribute, 'Invalid Signature');
        }
    }

    public function loadOrdersTopUpModel()
    {
        $this->orders_top_up_model = OrdersTopUp::findOne($this->top_up_id);
        if (!$this->orders_top_up_model) {
            throw new BadRequestHttpException();
        }
    }

    public function allowUpdate()
    {
        $otu = $this->orders_top_up_model;
        if ($otu->top_up_status == 1 && $otu->result_code == 1513 && (strtotime($otu->top_up_created_date) + (86400 * 3)) > time()) {
            return true;
        }
    }

    public function updateTopUpStatus()
    {
        $otu_model = $this->orders_top_up_model;
        if ($this->allowUpdate()) {
            $time = new Expression('NOW()');
            $otu_model->top_up_status = (string)$this->top_up_status;
            $otu_model->publishers_ref_id = $this->publisher_ref_id;
            $otu_model->result_code = ($this->top_up_status == 3 ? '2000' : '1513');
            $otu_model->publishers_response_time = $time;
            $otu_model->save();

            $remark_model = new OrdersTopUpRemark();
            $remark_model->top_up_id = $otu_model->top_up_id;
            $remark_model->data_added = $time;
            $remark_model->changed_by = 'system';
            $remark_model->remark = ($otu_model->top_up_status == 3 ? 'Top-up: Reloaded' : 'Top-up: Failed ' . '<br>' . nl2br(strip_tags($this->remarks)));
            $remark_model->save();

            if ($this->top_up_status == 10) {
                Yii::$app->slack->send('*Failed Delivery #' . $this->orders_id . ' delivery on PIMA DTU API*', [
                    [
                        'color' => 'warning',
                        'text' => "Orders ID : <" . Yii::$app->params['oldcrew.baseUrl'] . "/orders.php?oID=" . $this->orders_id . "&action=edit|" . $this->orders_id . ">" . " \n Error : " . $this->remarks . " \n `Action` : Reattempt delivery or contact publishers with error message",
                    ],
                ], 'BDT_DTU');
            }
        }
    }

    public function getSecretKey()
    {
        $secret_key = (new Query)
            ->select(['publishers_configuration_value'])
            ->from('publishers_configuration')
            ->where(['publishers_id' => $this->publisher_id, 'publishers_configuration_key' => 'SECRET_KEY'])
            ->scalar();

        $this->secret_key = $secret_key;
    }
}