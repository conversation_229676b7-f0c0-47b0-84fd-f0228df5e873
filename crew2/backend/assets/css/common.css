
.dataTables_wrapper .dataTable .filter th .btn, .dataTables_wrapper .dataTable .filter td .btn {
    margin-top: 0rem !important;
    margin-bottom: 2px;
    margin-right: 5px;
}

.prettyprint2 {
    max-width: 150px;
    min-height: 100px;
    overflow: auto;
    word-wrap: break-word;
}
.table-log-modal .table th{
    width: 20%;
    min-width: 90px;
}
.table-log-modal .table td{
    word-break: break-word;
}
.table-log-content .table th:nth-child(2), .table-log-content .table td:nth-child(2){
    width:40%;
    min-width:300px;
    word-break: break-word;
}
.table-log-content .table th:nth-child(3), .table-log-content .table td:nth-child(3){
    width:20%;
    min-width:150px;
    word-break: break-word;
}
.table-log-content .table th:nth-child(4),  .table-log-content.table td:nth-child(4){
    width:10%;
    min-width:90px;
}
.table th, .table td {
    vertical-align: center;
}
.uppy-DashboardContent-bar {
    z-index: 54 !important;
}

.uppy-Dashboard-AddFilesPanel {
    z-index: 55 !important;
}

.uppy-DashboardItem-progress, uppy-DashboardItem-progress {
    z-index: 52 !important;;
}

.uppy-StatusBar {
    z-index: 51 !important;
}

.uppy .uppy-Informer {
    z-index: 55 !important;
}

.uppy .uppy-Informer {
    z-index: 55 !important;
}

.uppy-Informer[aria-hidden=true] {
    z-index: -1000 !important;
}

html.swal2-shown, body.swal2-shown {
    overflow-y: hidden !important;
    height: auto !important;
}

.note-toolbar {
    z-index: 50 !important;
}

.help-block {
    font-size: smaller;
    color: red;
}

.glyphicon-address-card:before {
  content: "\f2bb";
}

.glyphicon-history:before {
  content: "\f1da";
}

.glyphicon-thumbs-up:before {
    content: "\f164";
}

.glyphicon-thumbs-down:before {
    content: "\f165";
}

.glyphicon-check:before {
    content: "\f00c";
}

.glyphicon-times:before {
    content: "\f00d";
}

.glyphicon-check-circle:before {
    content: "\f058";
}

.glyphicon-ban-circle:before {
    content: "\f05e";
}

.btn-group>.btn-group:not(:last-child)>.btn, .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-color:black;
    color:black;
}

.bootstrap-duallistbox-container .btn-group .btn {
    border-color:black;
    color:black;
}

.remarks {
    text-align: right;
}

