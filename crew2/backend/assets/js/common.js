if ($('#m_table_1').length) {
  var DatatablesDataSourceAjaxServer = function () {
    $.fn.dataTable.Api.register('column().title()', function () {
      return $(this.header()).text().trim()
    })

    var initTable = function (url, conf, column, columnDef, order, initComplete) {
      let table = $('#m_table_1')
      let options = Object.assign({
        searching: true,
        tabIndex: false,
        responsive: true,
        searchDelay: 500,
        processing: true,
        order: order,
        dom: '<\'row\'<\'col-sm-6\'l><\'offset-sm-6 float-right\'>>' + '<\'row\'<\'col-sm-12\'tr>>' + '<\'row\'<\'col-sm-5\'i><\'col-sm-7\'p>>',
        ajax: {
          url: url,
          type: 'GET',
          data: {
            date: $('#datepicker1').val(),
          },
        },
        columns: column,
        columnDefs: columnDef,
        initComplete: initComplete,
      }, conf)
      table = table.DataTable(options)
    }

    return {
      init: function (url, conf, column, columnDef, order, initComplete) {
        $('#m_table_1').DataTable().destroy()
        $('.filter').remove()
        initTable(url, conf, column, columnDef, order, initComplete)
      },
    }
  }()
}

function getFormData(form){
  var unindexed_array = form.serializeArray();
  var indexed_array = {};

  $.map(unindexed_array, function(n, i){
    indexed_array[n['name']] = n['value'];
  });

  return indexed_array;
}


function viewLogDetail (url, id, path) {
  getView(url + '?id=' + id + (path != 'undefined' ? '&s3_path=' + path : ''))
}

function getView (url) {
  mApp.blockPage()
  $.ajax({
    method: 'GET',
    url: url,
    dataType: 'json',
  }).done(function (data) {
    jQuery('#generalModal .modal-body').html(data['body'])
    jQuery('#generalModal .modal-title').text(data['title'])
    var pretty_print = $('.prettyprint2')
    for (var i = 0; i < pretty_print.length; i++) {
      var json = $(pretty_print[i]).data('raw')
      json = getJson(json)
      if (typeof json == 'object') {
        $.each(json, function (index, e) {
          json[index] = getJson(e)
        })
      }
      $(pretty_print[i]).html('<pre class=\'prettyprint\' style=\'max-height:200px;overflow-x:scroll\'>' + JSON.stringify(json, null, 4) + '<pre>')
    }
    mApp.unblockPage()
    jQuery('#generalModal').modal('show')
  }).fail(function ($data) {
    mApp.unblockPage()
    swal('Fail to retrieve content from s3', 'error')
  })
}

function getJson (json) {
  var count = 0
  while (IsJsonString(json)) {
    json = JSON.parse(json)
    count++
    if (count > 3) {
      break
    }
  }
  return json
}

function IsJsonString (str) {
  try {
    if (!isNaN(str) || typeof str == 'object') {
      return false
    }
    JSON.parse(str)
  }
  catch (e) {
    return false
  }
  return true
}

function copyToClipboard(element, attr_name) {
  var $temp = $("<input>");
  $("body").append($temp);
  $temp.val($(element).data(attr_name)).select();
  document.execCommand("copy");
  $temp.remove();
}

function viewImage(url){
  Swal.fire({
    imageUrl: url
  })
}