<?php

namespace backend\components;

use Yii;
use common\models\Countries;
use common\models\PipwavePaymentMapper;

class Dashboard {

    public $status = 3;
    public $fr_date;
    public $to_date;

    public function __construct() {
        $this->fr_date = date("Y-m-d 00:00:00", strtotime("-7 day"));
        $this->to_date = date("Y-m-d 23:59:59", strtotime("-1 day"));
    }

    public function getTotalSales() {
        $cache = Yii::$app->get('cache', false);
        $key = "/SalesDashboard/TotalSales/" . ($this->status == 3 ? "Complete" : "Process") . "/" . date("Ymd", strtotime($this->fr_date));
        $result = $cache->get($key);
        if ($result === false) {
            $result = [
                "total_order" => 0,
                "total_sales" => 0,
                "completed_order" => 0,
                "completed_sales" => 0,
                "payment_method" => [],
                "country" => [],
                "product" => [],
                "customer" => []
            ];

            $t_ctry = [];
            $t_prod = [];
            $t_cust = [];
            $t_pm = [];

            $res = Yii::$app->db_slave_offgamers
                    ->createCommand('SELECT o.orders_id, o.orders_status, o.customers_id, o.customers_groups_id,
                    o.payment_method, o.payment_methods_id, ot1.value AS ot_subtotal, ot2.value AS ot_total,
                    c.customers_country_dialing_code_id 
                FROM orders AS o
                INNER JOIN orders_total AS ot1
                    ON ot1.orders_id = o.orders_id
                    AND ot1.class = "ot_subtotal"
                INNER JOIN orders_total AS ot2
                    ON ot2.orders_id = o.orders_id
                    AND ot2.class = "ot_total"
                INNER JOIN customers AS c
                    ON c.customers_id = o.customers_id
                LEFT JOIN orders_extra_info AS oei1
                    ON oei1.orders_id = o.orders_id
                    AND oei1.orders_extra_info_key = "site_id"
                WHERE o.date_purchased >= "' . $this->fr_date . '"
                    AND o.date_purchased <= "' . $this->to_date . '"
                    AND o.orders_status IN (' . $this->status . ') 
                    AND (oei1.orders_extra_info_value IS NULL OR oei1.orders_extra_info_value != 5)
                GROUP BY o.orders_id')
                    ->queryAll();
            foreach ($res as $row) {
                $result["total_order"] += 1;
                $result["total_sales"] += $row["ot_subtotal"];

                // country
                if (!isset($t_ctry[$row["customers_country_dialing_code_id"]])) {
                    $t_ctry[$row["customers_country_dialing_code_id"]] = [
                        "id" => $row["customers_country_dialing_code_id"],
                        "sales" => 0,
                        "list" => []
                    ];
                }
                if (!in_array($row["customers_id"], $t_ctry[$row["customers_country_dialing_code_id"]]["list"])) {
                    $t_ctry[$row["customers_country_dialing_code_id"]]["list"][] = $row["customers_id"];
                }
                $t_ctry[$row["customers_country_dialing_code_id"]]["sales"] += $row["ot_subtotal"];

                // payment method
                if (!isset($t_pm[$row["payment_methods_id"]])) {
                    $t_pm[$row["payment_methods_id"]] = [
                        "id" => $row["payment_methods_id"],
                        "name" => ($row["payment_methods_id"] ? $row["payment_method"] : "Store Credit"),
                        "order" => 0,
                        "sales" => 0
                    ];
                }
                $t_pm[$row["payment_methods_id"]]["order"] += 1;
                $t_pm[$row["payment_methods_id"]]["sales"] += $row["ot_total"];

                // customer group
                if (!isset($t_cust[$row["customers_groups_id"]])) {
                    $t_cust[$row["customers_groups_id"]] = [
                        "sales" => 0,
                        "list" => []
                    ];
                }
                if (!in_array($row["customers_id"], $t_cust[$row["customers_groups_id"]]["list"])) {
                    $t_cust[$row["customers_groups_id"]]["list"][] = $row["customers_id"];
                }
                $t_cust[$row["customers_groups_id"]]["sales"] += $row["ot_subtotal"];

                // product ( processing / completed )
                $_row = Yii::$app->db_slave_offgamers
                        ->createCommand('SELECT op.products_id, op.products_name, op.final_price, op.products_quantity,
                            op.products_good_delivered_price
                        FROM orders_products AS op
                        WHERE op.orders_id = ' . $row["orders_id"] . '
                            AND op.orders_products_is_compensate = 0
                            AND op.products_bundle_id = 0')
                        ->queryOne();
                if (!isset($t_prod[$_row["products_id"]])) {
                    $t_prod[$_row["products_id"]] = [
                        "id" => $_row["products_id"],
                        "name" => $_row["products_name"],
                        "order" => 0,
                        "sales" => 0
                    ];
                }
                $t_prod[$_row["products_id"]]["order"] += 1;
                $t_prod[$_row["products_id"]]["sales"] += ($_row['final_price'] * $_row["products_quantity"]);

                // completed order
                if ($row["orders_status"] == 3) {
                    if (isset($_row['products_good_delivered_price'])) {
                        $result["completed_sales"] += $_row['products_good_delivered_price'];
                    }
                    $result["completed_order"] += 1;
                }
            }

            // country
            $res = array_column($t_ctry, "sales");
            array_multisort($res, SORT_DESC, $t_ctry);
            $t_ctry = array_slice($t_ctry, 0, 10);
            foreach ($t_ctry as $id => $val) {
                $m_ctry = Countries::find()->where(["countries_id" => $val["id"]])->one();
                $result["country"][$val["id"]] = [
                    "name" => (isset($m_ctry->countries_name) ? $m_ctry->countries_name : "Unknown"),
                    "customer" => count($val["list"]),
                    "sales" => $val["sales"]
                ];
            }

            // payment method
            $res = array_column($t_pm, "order");
            array_multisort($res, SORT_DESC, $t_pm);
            $t_pm = array_slice($t_pm, 0, 5);
            foreach ($t_pm as $id => $val) {
                $result["payment_method"][$val["id"]] = [
                    "name" => $val["name"],
                    "order" => $val["order"],
                    "sales" => $val["sales"],
                    "rp" => ""
                ];

                if ($val["id"] != 0) {
                    $m_ppm = PipwavePaymentMapper::find()->where(['pm_id' => $val["id"], 'site_id' => 0])->one();
                    if (isset($m_ppm->id)) {
                        $result["payment_method"][$val["id"]]["rp"] = (new PipwavePaymentMapper)->getIsRpOptions($m_ppm->is_rp);
                    }
                }
            }

            // customer
            foreach ($t_cust as $id => $val) {
                $result["customer"][$id] = [
                    "sales" => $val["sales"],
                    "customer" => count($val["list"])
                ];
            }

            // product
            $res = array_column($t_prod, "order");
            array_multisort($res, SORT_DESC, $t_prod);
            $t_prod = array_slice($t_prod, 0, 5);
            foreach ($t_prod as $id => $val) {
                $result["product"][$val["id"]] = [
                    "name" => $val["name"],
                    "order" => $val["order"],
                    "sales" => $val["sales"]
                ];
            }

            unset($t_prod, $t_cust, $t_ctry, $t_pm);

            // cache result for 1 day
            $cache->set($key, $result, 86400);
        }
        return $result;
    }

    public function getSales() {
        $cache = Yii::$app->get('cache', false);
        $key = "/Sales/TotalSales/" . ($this->status == 3 ? "Complete" : "Process") . "/" . date("Ymd", strtotime($this->fr_date)) . "/" . date("Ymd", strtotime($this->to_date));
        $result = $cache->get($key);
        if ($result === false) {
            $result = [
                "total_order" => 0,
                "total_sales" => 0,
                "completed_order" => 0,
                "completed_sales" => 0,
                "payment_method" => [],
                "country" => [],
                "product" => [],
                "customer" => []
            ];

            $t_ctry = [];
            $t_prod = [];
            $t_cust = [];
            $t_pm = [];

            $res = Yii::$app->db_slave_offgamers
                    ->createCommand('SELECT o.orders_id, o.orders_status, o.customers_id, o.customers_groups_id,
                    o.payment_method, o.payment_methods_id, ot1.value AS ot_subtotal, ot2.value AS ot_total,
                    c.customers_country_dialing_code_id
                FROM orders AS o
                INNER JOIN orders_total AS ot1
                    ON ot1.orders_id = o.orders_id
                    AND ot1.class = "ot_subtotal"
                INNER JOIN orders_total AS ot2
                    ON ot2.orders_id = o.orders_id
                    AND ot2.class = "ot_total"
                INNER JOIN customers AS c
                    ON c.customers_id = o.customers_id
                LEFT JOIN orders_extra_info AS oei1
                    ON oei1.orders_id = o.orders_id
                    AND oei1.orders_extra_info_key = "site_id"
                WHERE o.date_purchased >= "' . $this->fr_date . '"
                    AND o.date_purchased <= "' . $this->to_date . '"
                    AND o.orders_status IN (' . $this->status . ')
                    AND (oei1.orders_extra_info_value IS NULL OR oei1.orders_extra_info_value != 5)
                GROUP BY o.orders_id')
                    ->queryAll();
            foreach ($res as $row) {
                $result["total_order"] += 1;
                $result["total_sales"] += $row["ot_subtotal"];

                // country
                if (!isset($t_ctry[$row["customers_country_dialing_code_id"]])) {
                    $t_ctry[$row["customers_country_dialing_code_id"]] = [
                        "id" => $row["customers_country_dialing_code_id"],
                        "sales" => 0,
                        "list" => []
                    ];
                }
                if (!in_array($row["customers_id"], $t_ctry[$row["customers_country_dialing_code_id"]]["list"])) {
                    $t_ctry[$row["customers_country_dialing_code_id"]]["list"][] = $row["customers_id"];
                }
                $t_ctry[$row["customers_country_dialing_code_id"]]["sales"] += $row["ot_subtotal"];

                // payment method
                if (!isset($t_pm[$row["payment_methods_id"]])) {
                    $t_pm[$row["payment_methods_id"]] = [
                        "id" => $row["payment_methods_id"],
                        "name" => ($row["payment_methods_id"] ? $row["payment_method"] : "Store Credit"),
                        "order" => 0,
                        "sales" => 0
                    ];
                }
                $t_pm[$row["payment_methods_id"]]["order"] += 1;
                $t_pm[$row["payment_methods_id"]]["sales"] += $row["ot_total"];

                // customer group
                if (!isset($t_cust[$row["customers_groups_id"]])) {
                    $t_cust[$row["customers_groups_id"]] = [
                        "sales" => 0,
                        "list" => []
                    ];
                }
                if (!in_array($row["customers_id"], $t_cust[$row["customers_groups_id"]]["list"])) {
                    $t_cust[$row["customers_groups_id"]]["list"][] = $row["customers_id"];
                }
                $t_cust[$row["customers_groups_id"]]["sales"] += $row["ot_subtotal"];

                // product ( processing / completed )
                $_row = Yii::$app->db_slave_offgamers
                        ->createCommand('SELECT op.products_id, op.products_name, op.final_price, op.products_quantity,
                            op.products_good_delivered_price
                        FROM orders_products AS op
                        WHERE op.orders_id = ' . $row["orders_id"] . '
                            AND op.orders_products_is_compensate = 0
                            AND op.products_bundle_id = 0')
                        ->queryOne();
                if (!isset($t_prod[$_row["products_id"]])) {
                    $t_prod[$_row["products_id"]] = [
                        "id" => $_row["products_id"],
                        "name" => $_row["products_name"],
                        "order" => 0,
                        "sales" => 0
                    ];
                }
                $t_prod[$_row["products_id"]]["order"] += 1;
                $t_prod[$_row["products_id"]]["sales"] += ($_row['final_price'] * $_row["products_quantity"]);

                // completed order
                if ($row["orders_status"] == 3) {
                    if (isset($_row['products_good_delivered_price'])) {
                        $result["completed_sales"] += $_row['products_good_delivered_price'];
                    }
                    $result["completed_order"] += 1;
                }
            }

            // cache result for 1 day
            $cache->set($key, $result, 86400);


            // country
            $cache = Yii::$app->get('cache', false);
            $key = "/Sales/TotalSales/" . ($this->status == 3 ? "Complete" : "Process") . "/" . date("Ymd", strtotime($this->fr_date)) . "/" . date("Ymd", strtotime($this->to_date)) . "/Country";
            $result = $cache->get($key);
            if ($result === false) {
                $res = array_column($t_ctry, "sales");
                array_multisort($res, SORT_DESC, $t_ctry);
                foreach ($t_ctry as $id => $val) {
                    $m_ctry = Countries::find()->where(["countries_id" => $val["id"]])->one();
                    $result["country"][$val["id"]] = [
                        "name" => (isset($m_ctry->countries_name) ? $m_ctry->countries_name : "Unknown"),
                        "customer" => count($val["list"]),
                        "sales" => $val["sales"]
                    ];
                }
            }
            $cache->set($key, $result, 86400);
        }
    }

    public function getNewUser() {
        $cache = Yii::$app->get('cache', false);
        $key = "/SalesDashboard/TotalNewUser/" . date("Ymd", strtotime($this->fr_date));
        $result = $cache->get($key);
        if ($result === false) {
            $result = [
                "ogm" => 0,
                "total" => 0
            ];

            $res = Yii::$app->db_slave_offgamers
                    ->createCommand("SELECT COUNT(ci.customers_info_id) AS register, ci.account_created_site
                FROM customers_info AS ci
                WHERE ci.customers_info_date_account_created >= '" . $this->fr_date . "'
                    AND ci.customers_info_date_account_created < '" . $this->to_date . "'
                GROUP BY account_created_site")
                    ->queryAll();
            foreach ($res as $row) {
                if (preg_match("/ogm/i", $row["account_created_site"]) || preg_match("/gamernizer/i", $row["account_created_site"])) {
                    $result["ogm"] += (int) $row["register"];
                }
                $result["total"] += (int) $row["register"];
            }

            // cache result for 1 day
            $cache->set($key, $result, 86400);
        }

        return $result;
    }

}
