<?php

$params = array_merge(require __DIR__ . '/../../common/config/params.php', require __DIR__ . '/../../common/config/params-encoded.php', require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php', require __DIR__ . '/params-local.php');

return [
    'id' => 'crew2',
    'language' => 'en',
    'name' => 'OffGamers Crew',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'backend\controllers',
    'bootstrap' => ['log', 'report_queue'],
    'modules' => [
        'admin' => [
            'class' => 'mdm\admin\Module',
            'controllerMap' => [
                'user' => [
                    'class' => 'backend\controllers\admin\UserController',
                ],
                'system' => [
                    'class' => 'backend\controllers\admin\SystemController',
                ],
                'blocked-email' => [
                    'class' => 'backend\controllers\admin\BlockedEmailController',
                ],
            ],
        ],
        'gridview' => [
            'class' => '\kartik\grid\Module',
        ]
    ],
    'as access' => [
        'class' => 'mdm\admin\components\AccessControl',
        'allowActions' => ['site/login', 'site/error', 'site/reset-password', 'site/request-password-reset']
    ],
    'components' => [
        'report_queue' => [
            'class' => \console\components\ReportQueue::class,
            'queue_name' => 'CREW2_REPORT_QUEUE'
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
            'cache' => 'cache',
            'cacheKey' => 'authManager'
        ],
        'metronic' => [
            'class' => 'offgamers\metronic\Metronic',
            'sourcePath' => '@backend/theme/metronics/assets',
            'buildPath' => 'demo/demo11'
        ],
        'assetManager' => [
            'appendTimestamp' => true,
            'bundles' => [
                'mervick\emojionearea\Asset' => [
                    'sourcePath' => '@vendor/mervick/emojionearea',
                    'js' => [!YII_DEBUG ? 'dist/emojionearea.min.js' : 'dist/emojionearea.js'],
                    'css' => [!YII_DEBUG ? 'dist/emojionearea.min.css' : 'dist/emojionearea.css'],
                    'depends' => [
                        'yii\web\JqueryAsset',
                        'backend\assets\EmojiOneAsset'
                    ]
                ],
                'yii\web\JqueryAsset' => [
                    'js' => [],
                    'depends' => [
                        'offgamers\metronic\bundles\MetronicAsset'
                    ]
                ],
                'yii\bootstrap\BootstrapAsset' => false,
                'yii\bootstrap\BootstrapPluginAsset' => false,
                'yii\bootstrap4\BootstrapAsset' => false,
                'yii\bootstrap4\BootstrapPluginAsset' => false,
            ],
        ],
        'view' => [
            'theme' => [
                'pathMap' => [
                    '@app/views' => '@app/theme/metronics/views',
                    '@mdm/admin/views' => '@backend/views/admin'
                ],
            ],
        ],
        'request' => [
            'csrfParam' => '_csrf-backend',
        ],
        'user' => [
            'identityClass' => 'common\models\User',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-backend', 'httpOnly' => true],
            'on afterLogin' => ['common\models\User', 'afterLogin'],
        ],
        'session' => [
            // this is the name of the session cookie used for login on the backend
            'class' => 'yii\web\DbSession',
            'sessionTable' => 'crew2_session',
            'cookieParams' => [
                'httpOnly' => true,
                'secure' => true,
                'SameSite' => yii\web\Cookie::SAME_SITE_STRICT
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [],
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/messages',
                ],
            ],
        ],
    ],
    'params' => $params,
];
