<?php

namespace backend\controllers;

use backend\models\ApiRestockRequestForm;
use common\models\ApiRestockRequest;
use Yii;
use yii\data\Pagination;

class ApiRestockRequestController extends BaseController
{
   
    public $menu = 'Inventory/Restock';

    public function actionIndex()
    {
        $load = false;
        $provider = [];
        $model = new ApiRestockRequestForm();
        $model->scenario = "search_filt";

    
        if (Yii::$app->request->isGet) {
            $load = $model->load(Yii::$app->request->get(), '');
        } else if (Yii::$app->request->isPost) {
            $load = $model->load(Yii::$app->request->post());
        }
        if ($load && $model->validate()) {
            $provider = $model->search();
            if ($provider) {
                $provider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);
            }
        }

        return $this->render('index', [
            'model'    => $model,
            'provider' => $provider,
            
        ]);
    }
   
    public function actionUpdate($id = '')
    {
        $model = new ApiRestockRequestForm($id);

        $newModel = ApiRestockRequest::findOne($id);
        
        if (!(Yii::$app->request->isPost)) {
            return $this->render('update', ['model' => $model]);
        }
        if (Yii::$app->request->isPost) {

            $newModel->load($this->request->post());
            $newModel->save();
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect(['index']);
            
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            return $this->redirect(['index']);
        }
 
    }


}
