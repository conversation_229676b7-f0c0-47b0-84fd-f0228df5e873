<?php

namespace backend\controllers;

use Yii;
use yii\web\Controller;
use yii\filters\AccessControl;
use mdm\admin\components\Helper;

/**
 * Base Controller
 */
abstract class BaseController extends Controller {

    public $menu = '';

    public function beforeAction($action){
        $this->setActiveMenu($this->menu);
        return parent::beforeAction($action);
    }

    protected function setActiveMenu($menu = ''){
        Yii::$app->view->params['activeMenu'] = $menu;
    }

    public function behaviors() {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function ($rule, $action) {
                            if (Yii::$app->authManager->getAssignment('superadmin', Yii::$app->user->id)) {
                                return true;
                            }
                            
                            $action      = Yii::$app->controller->action->id;
                            $controller  = Yii::$app->controller->id;
                            $modules     = (Yii::$app->controller->module->id && Yii::$app->controller->module->id != 'crew2') ? "/".Yii::$app->controller->module->id : "";

                            $route       = "$modules/$controller/$action";
                            $post        = Yii::$app->request->post();

                            if (Helper::checkRoute($route)) {
                                return true;
                            } else {
                                Yii::$app->session->setFlash('site', 'FLASH_WARNING_PERMISSION_DENIED');
                            }
                        }
                    ],
                ],
            ],
        ];
    }
}