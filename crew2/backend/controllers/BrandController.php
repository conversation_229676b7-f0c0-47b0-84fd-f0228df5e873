<?php

namespace backend\controllers;

use backend\models\BrandForm;
use backend\models\BrandSearch;
use common\models\Categories;
use common\models\Countries;
use common\models\GameGenreDescription;
use common\models\GameLanguageDescription;
use common\models\GamePlatformDescription;
use common\models\GameRegionDescription;
use Exception;
use offgamers\base\models\ms\Product;
use Yii;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

class BrandController extends BaseController
{
    public $menu = 'Product/Brand';

    public function actionIndex()
    {
        $filterModel = new BrandSearch();
        $provider = $filterModel->search(Yii::$app->request->get(), 'brand_id');

        return $this->render(
            'index',
            [
                'provider' => $provider,
                'filter' => $filterModel,
            ]
        );
    }

    public function actionCreate()
    {
        $model = new BrandForm();

        $model->setAttributes(Yii::$app->request->post('BrandForm', []), false);

        $model->image = UploadedFile::getInstance($model, 'image');

        if ($model->validate()) {
            if ($model->create()) {
                Yii::$app->session->setFlash(
                    "success",
                    Yii::t("site", "FLASH_SUCCESS")
                );
            } else {
                if(!empty($model->getErrorMsg())) {
                    $this->displayFlashMsg($model->getErrorMsg());
                }
            }

            return $this->redirect('index');
        }

        $model->clearErrors();

        return $this->render(
            'create_or_update',
            $this->getFormDatas($model, 'create')
        );
    }

    public function actionDelete()
    {
        $brandId = Yii::$app->request->get('id');

        $model = new BrandForm();

        $response = $model->deleteById($brandId);

        if ($response) {
            Yii::$app->session->setFlash(
                "success",
                Yii::t("site", "FLASH_SUCCESS")
            );
        }

        return $this->redirect('index');
    }

    public function actionUpdate()
    {
        $brandId = Yii::$app->request->get('id');

        $model = new BrandForm();

        $model->setAttributes(Yii::$app->request->post('BrandForm', []), false);

        $model->image = UploadedFile::getInstance($model, 'image');

        if ($model->validate()) {
            if ($model->update()) {
                Yii::$app->session->setFlash(
                    "success",
                    Yii::t("site", "FLASH_SUCCESS")
                );
            } else {
                if(!empty($model->getErrorMsg())) {
                    $this->displayFlashMsg($model->getErrorMsg());
                }
            }
            return $this->redirect('index');
        } 

        $data = $this->findBrandData($brandId);

        $model->initForUpdate($data);

        return $this->render(
            'create_or_update',
            $this->getFormDatas($model, 'update')
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionGetBrandJson()
    {
        $brandId = Yii::$app->request->get('id');

        return BrandForm::getBrandById($brandId);
    }

    public function actionGetCategoryJson()
    {
        $categoryId = Yii::$app->request->get('id');

        $categories = Categories::getSecondLayerCategoryList();

        return Json::encode($categories[$categoryId]);
    }

    public function actionCheckSeoUrlExists()
    {
        $seoUrl = Yii::$app->request->get('seo_url');
        $brandId = Yii::$app->request->get('brand_id');

        $result = (new BrandForm())->checkSeoUrlExists($seoUrl, $brandId);

        return Json::encode($result);
    }

    public function actionSyncAlgolia()
    {
        (new Product())->customRequest('brand', 'sync-algolia');
    }

    public function actionSyncBrandType()
    {
        (new Product())->customRequest('brand', 'sync-brand-type');
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findBrandData($brandId)
    {
        $model = new BrandForm();

        try {
            return $model->get($brandId);
        } catch (Exception $exception) {
            throw new NotFoundHttpException($exception->getMessage());
        }
    }

    private function getFormDatas(BrandForm $model, string $action): array
    {
        return [
            'model' => $model,
            'action' => $action,
            'categories' => Categories::getSecondLayerCategoryList() ?? [],
            'countries' => Countries::countriesNameID() ?? [],
            'brands' => BrandSearch::getBrandsForBrandCategory() ?? [],
            'regions' => GameRegionDescription::getGameRegionDescId() ?? [],
            'languages' => GameLanguageDescription::getGameLanguageDescId() ?? [],
            'platforms' => GamePlatformDescription::getGamePlatformDescId() ?? [],
            'genres' => GameGenreDescription::getGameGenreDescId() ?? [],
        ];
    }

    private function displayFlashMsg($msgs): void
    {
        $temp_str = "";
        foreach ($msgs as $key => $msg) {
            $temp_str .= "<li>" . $msg . "</li>";
        }
        Yii::$app->session->setFlash(
            "error",
            sprintf(Yii::t("site", "FLASH_ERROR_OG"), $temp_str)
        );
    }
}
