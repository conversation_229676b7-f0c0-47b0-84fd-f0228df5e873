<?php

namespace backend\controllers;

use common\models\CategoriesDiscountRules;
use backend\models\CategoryPromotion;
use backend\models\CategoryForm;
use Exception;
use Yii;
use yii\filters\VerbFilter;
use yii\web\NotFoundHttpException;

class CategoryController extends BaseController {

    public $menu = 'Product/Category';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionIndex()
    {
        $filter = new CategoryForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('index', [
            'provider' => $provider,
            'filter' => $filter,
        ]);
    }

    public function actionCreate()
    {
        $model = new CategoryForm();
        $input = Yii::$app->request->post();
        if ($input && $model->load($input)) {
            $model->setCategoryImage();
            if ($model->validate()) {
                $result = $model->save();
                if ($result && $result['status']) {
                    Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                } else {
                    Yii::$app->session->setFlash("error", $result['message'] ?? '');
                }
                return $this->redirect('index');
            }
        }

        return $this->render('form', [
            'model' => $model
        ]);
    }

    public function actionUpdate($id)
    {
        $model = new CategoryForm();
        try {
            $model->get($id);
        } catch (Exception $exception) {
            throw new NotFoundHttpException($exception->getMessage());
        }

        $input = Yii::$app->request->post();
        if ($input && $model->load($input)) {
            $model->setCategoryImage();
            if ($model->validate() && $model->save()) {
                $result = $model->save();
                if ($result && $result['status']) {
                    Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                } else {
                    Yii::$app->session->setFlash("error", $result['message'] ?? '');
                }
                return $this->redirect(['update', 'id' => $id]);
            }
        }
        return $this->render('form', [
            'model' => $model
        ]);
    }

    public function actionDelete($id)
    {
        $model = new CategoryForm();
        if ($model->delete($id)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }

        return $this->redirect('index');
    }

    public function actionGetDiscountRuleDetail()
    {
        Yii::$app->getResponse()->format = 'json';
        return CategoriesDiscountRules::getCategoriesDiscountRulesDetail(Yii::$app->request->get('rules_id'));
    }

    public function actionSavePromotion()
    {
        $category_promotion_model = new CategoryPromotion();
        $input = Yii::$app->request->post();
        Yii::$app->getResponse()->format = 'json';
        if ($input && $category_promotion_model->load($input) && $category_promotion_model->validate()) {
            return $this->returnResponse(true, $category_promotion_model->save());
        }
        return $this->returnResponse(false, [], $category_promotion_model->getErrors());
    }

    public function actionGetPromotion()
    {
        $input = Yii::$app->request->get();
        Yii::$app->getResponse()->format = 'json';
        return (new CategoryPromotion())->get($input['category_promotion_id']);
    }

    public function actionGetPromotionList()
    {
        $category_id = Yii::$app->request->get('category_id');
        Yii::$app->getResponse()->format = 'json';
        return (new CategoryPromotion())->getList($category_id);
    }

    public function actionCheckSeoUrlExists()
    {
        $seo_url = Yii::$app->request->get('seo-url');
        $category_id = Yii::$app->request->get('category-id');
        return (bool)(new CategoryForm())->checkSeoUrlExists($seo_url, $category_id);
    }

    public function actionDeletePromotion()
    {
        $input = Yii::$app->request->get();
        Yii::$app->getResponse()->format = 'json';
        return (new CategoryPromotion())->delete($input);
    }

    private function returnResponse($status, $data, $error = []) {
        return [
            'status' => $status,
            'data' => $data,
            'errors' => $error
        ];
    }

}
