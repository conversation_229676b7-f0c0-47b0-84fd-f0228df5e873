<?php

namespace backend\controllers;

use backend\models\CouponRedeemTrackForm;
use common\models\CouponRedeemTrack;
use Yii;
use yii\data\Pagination;
use yii\web\NotFoundHttpException;

class CouponRedeemTrackController extends BaseController
{

    public $menu = 'Sales/Order/Coupon Redemption';

    public function actionIndex()
    {
        $load = false;
        $provider = [];
        $model = new CouponRedeemTrackForm();
        $model->scenario = "search_filt";


        if (Yii::$app->request->isGet) {
            $load = $model->load(Yii::$app->request->get(), '');
        } else if (Yii::$app->request->isPost) {
            $load = $model->load(Yii::$app->request->post());
        }
        if ($load && $model->validate()) {
            $provider = $model->search();
            if ($provider) {
                $provider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);
            }
        }

        return $this->render('index', [
                    'model' => $model,
                    'provider' => $provider,
        ]);
    }

    protected function findModel($id)
    {
        if (($model = CouponRedeemTrack::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionDelete($id)
    {
        $m = $this->findModel($id);
        if ($m->order_id) {
            $m->coupon_id = 0;
            if ($m->save()) {
                // update order history
                $osh_q = new \common\models\OrdersStatusHistory();
                $osh_q->orders_id = $m->order_id;
                $osh_q->orders_status_id = 0;
                $osh_q->date_added = new \yii\db\Expression('NOW()');
                $osh_q->customer_notified = 2;
                $osh_q->comments = "Release coupon";
                $osh_q->comments_type = '0';
                $osh_q->changed_by = Yii::$app->user->identity->username;
                $osh_q->save();

                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index']);
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return $this->redirect(['index']);
    }

}
