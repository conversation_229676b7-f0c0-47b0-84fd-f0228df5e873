<?php

namespace backend\controllers;


use Yii;
use backend\models\CouponsGenerationForm;
use common\models\CouponsGeneration;
use common\models\CouponsGenerationSearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\Coupons;
use common\models\CouponsSearch;

/**
 * CouponsController implements the CRUD actions for CouponsGeneration model.
 */
class CouponsController extends BaseController
{
    public $menu = 'Marketing/Coupons';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CouponsGeneration models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CouponsGenerationSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CouponsGeneration model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CouponsGeneration model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CouponsGenerationForm();

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $id = $model->process()) {
            return $this->redirect(['view', 'id' => $id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    public function actionApprove($id)
    {
        $model = $this->findModel($id);
        if ($model) {
            $model->generateCoupon();
            return $this->redirect(['list-coupon', 'id' => $id]);
        }
    }

    public function actionCancel($id)
    {
        $model = $this->findModel($id);
        if ($model) {
            $model->cancelGenerationRequest();
            return $this->redirect(['index']);
        }
    }

    public function actionListCoupon($id)
    {
        $coupon = $this->findModel($id)->getTitle();
        $searchModel = new CouponsSearch();
        $searchModel->coupon_generation_id = $id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('list-coupon', [
            'coupon' => $coupon,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CouponsGeneration model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionViewCode($id)
    {
        $model = Coupons::findOne($id);
        return $this->render('view-coupon', [
            'coupon' => $this->findModel($model->coupon_generation_id)->getTitle(),
            'model' => $model
        ]);
    }

    /**
     * Displays a single CouponsGeneration model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionCancelCode($id)
    {
        $model = Coupons::findOne($id);
        $model->cancelCoupon();

        return $this->redirect(['list-coupon', 'id' => $model->coupon_generation_id]);
    }


    public function actionCleanUpExpiredCoupon()
    {
        Coupons::cleanUpExpiredCoupon();
        return $this->redirect(['index']);
    }

    /**
     * Finds the CouponsGeneration model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CouponsGeneration the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CouponsGeneration::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
