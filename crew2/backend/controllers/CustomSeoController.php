<?php

namespace backend\controllers;

use Yii;
use common\models\CustomSeoForm;
use yii\helpers\Json;
use yii\helpers\HtmlPurifier;

class CustomSeoController extends BaseController {

    public $menu = 'Marketing/SEO';

    public function actionIndex() {
        $filter = new CustomSeoForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('index', [
                    'provider' => $provider,
                    'filter' => $filter,
        ]);
    }

    public function actionUpdate() {
        $model = new CustomSeoForm();
        $post = Yii::$app->request->post();

        if ($post && $model->load($post)) {
            $model->save($post);
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } else {
            $input = Yii::$app->request->get();
            $data = [
                'type' => strip_tags(HtmlPurifier::process($input['type'])),
                'id' => (int)HtmlPurifier::process($input['id']),
                'force_default' => (!empty($input['resetDefault']) ? true : false),
            ];
            $default = $model->getDefault($data);
            $model->type = $default['type'];
            $model->id = $default['id'];
        }

        return $this->render('update', [
                    'data' => $default,
                    'model' => $model
        ]);
    }

    public function actionClone($id) {
        $model = new CustomSeoForm();
        if ($post = Yii::$app->request->post()) {
            $data = [
                'id' => $id,
                'type' => $post['type'],
                'clone_id' => $post['clone_id']
            ];
            return $model->cloneData($data);
        } else {
            return $this->renderPartial('clone-modal', ['source_id' => $id]);
        }
    }

    public function actionGetItemList() {
        $input = Yii::$app->request->post();
        $output = (new CustomSeoForm())->getItemList($input);
        natcasesort($output);
        $output = array_flip($output);
        return Json::encode($output);
    }

}
