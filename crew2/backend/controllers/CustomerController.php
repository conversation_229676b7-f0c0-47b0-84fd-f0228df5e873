<?php

namespace backend\controllers;

use backend\models\admin\form\EmailVerifyForm;
use backend\models\CustomerForm;
use Yii;

class CustomerController extends BaseController
{

    public $menu = 'Customer/Profile';

    public function actionIndex()
    {
        $load = false;
        $provider = [];
        $model = new CustomerForm();
        $model->scenario = "search_filt";

        if (Yii::$app->request->isGet) {
            $load = $model->load(Yii::$app->request->get(), '');
        } else if (Yii::$app->request->isPost) {
            $load = $model->load(Yii::$app->request->post());
        }
        if ($load && $model->validate()) {
            $provider = $model->search();
            if ($provider) {
                $provider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);
            }
        }

        return $this->render('index', [
            'model' => $model,
            'provider' => $provider,
        ]);
    }

    public function actionUpdate()
    {
        if (Yii::$app->request->get('id')) {
            $id = (int) Yii::$app->request->get('id');
            if ($id) {
                $model = new CustomerForm($id);
                if ($model->customersInfo) {
                    $email = $model->customersInfo->customers_email_address;

                    $bmodel = new EmailVerifyForm($email);
                    $model->emailValidation = $bmodel->emailValidation;
                    $model->sesBounce = $bmodel->sesBounce;
                    $model->isNewRecord = false;

                    $post = Yii::$app->request->post();
                    if ($post) {
                        $post['id'] = $model->customerTaxInfo->id;
                        if ($model->update($post)) {
                            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                        }
                    }

                    return $this->render('update', ['model' => $model]);
                }
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return $this->redirect(['index']);
    }

    public function actionUpdateBlockStatus()
    {
        if (Yii::$app->request->get('id')) {
            $id = (int)Yii::$app->request->get('id');
            if ($id) {
                $model = new CustomerForm($id);
                if ($model->customersInfo) {
                    $email = $model->customersInfo->customers_email_address;

                    $bmodel = new EmailVerifyForm($email);
                    $bmodel->isNewRecord = false;

                    $post = Yii::$app->request->post();
                    if ($post) {
                        if ($bmodel->updateBlockAndVerifyStatus($post)) {
                            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                        }
                    }

                    return $this->render('update', ['model' => $model]);
                }
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return $this->redirect(['index']);
    }

}
