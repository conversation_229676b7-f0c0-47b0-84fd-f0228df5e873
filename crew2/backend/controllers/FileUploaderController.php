<?php

namespace backend\controllers;

use yii\base\InvalidArgumentException;
use Yii;
use backend\models\FileUploadForm;
use yii\web\UploadedFile;
use yii\helpers\Json;

class FileUploaderController extends BaseController
{
    public $menu = 'Tool/Media';

    public function actionIndex()
    {
        $model = new FileUploadForm();
        return $this->render('index', ['model' => $model]);
    }

    public function actionList()
    {
        $model = new FileUploadForm();
        return $this->render('list', ['model' => $model]);
    }

    public function actionUpload()
    {
        Yii::$app->response->format = \yii\web\response::FORMAT_JSON;

        $model = new FileUploadForm();

        if (Yii::$app->request->isPost) {
            $model->load($_POST);
            $model->imageFile = UploadedFile::getInstance($model, 'imageFile');
            $timestamp = time();

            try {
                if ($model->validate()) {
                    $ret = $model->uploadImages();
                } else {
                    $message = '';
                    foreach ($model->getErrors() as $field => $errors) {
                        foreach ($errors as $error) {
                            $message .= $model->getAttributeLabel($field) . ': ' . $error . "\n";
                        }
                    }
                    throw new \yii\web\HttpException(500, $message);
                }
            } catch (\Exception $e) {
                throw new \yii\web\HttpException(500, $e->getMessage());
            }
            return Json::encode(['uploadURL' => $ret['url'], 'fileName' => $ret['fileName'], 'totalTime' => time() - $timestamp]);
        }
    }

    public function actionGetUploadHistory()
    {
        $model = new FileUploadForm();
        $model->load($_GET);
        $data = $model->getUploadHistory();

        $return_arr = [
            'recordTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $data
        ];

        return Json::encode($return_arr, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE);
    }

    public function actionDelete($directory, $path){
        Yii::$app->response->format = \yii\web\response::FORMAT_JSON;
        $model = new FileUploadForm();
        $model->directory = $directory;
        $model->deleteFile($path);
    }
}