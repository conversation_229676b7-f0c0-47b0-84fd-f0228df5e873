<?php

namespace backend\controllers;

use Yii;
use common\models\GameProductAttributeForm;
use yii\base\Exception;
use yii\filters\VerbFilter;
use yii\helpers\Json;

/**
 * GameProductAttributeController implements the CRUD actions for GameProductAttribute model.
 */
class GameProductAttributeController extends BaseController
{

    public $menu = 'Product/Game Key/Attribute';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all GameProductAttribute models.
     * @return mixed
     */
    public function actionIndex()
    {
        $filter = new GameProductAttributeForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('index', [
            'provider' => $provider,
            'filter' => $filter,
        ]);
    }

    public function actionCreate()
    {
        $model = new GameProductAttributeForm();

        $post = Yii::$app->request->post();

        if ($post && $model->create($post)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        }

        return $this->render('create', [
            'data' => [],
            'model' => $model
        ]);
    }

    public function actionUpdate($id)
    {
        $model = new GameProductAttributeForm();
        $data = $model->get($id);
        $post = Yii::$app->request->post();

        if ($post && $model->update($id, $post)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        }

        return $this->render('update', [
            'data' => $data['data'],
            'model' => $model
        ]);
    }

    public function actionNextSortOrder($type)
    {
        $model = new GameProductAttributeForm();

        return $model->nextSortOrder($type);
    }

}
