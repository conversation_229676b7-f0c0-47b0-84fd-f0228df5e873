<?php

namespace backend\controllers;

use Yii;
use yii\web\Response;
use yii\web\NotFoundHttpException;
use common\models\GamePublisher;
use common\models\GamePublisherSetting;
use common\models\GamePublisherSearch;

class GamePublisherController extends BaseController {

    public $menu = 'Product/Game Key/Publisher';

    public function actionIndex() {
        $searchModel = new GamePublisherSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
                    'searchModel' => $searchModel,
                    'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate() {
        $model = new GamePublisher();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            foreach ($_POST['SETTING'] as $key => $val) {
                $setting = new GamePublisherSetting();
                $setting->game_publisher_id = $model->game_publisher_id;
                $setting->key = $key;
                $setting->value = $val;
                $setting->save();
            }
            Yii::$app->response->format = Response::FORMAT_JSON;
            if (Yii::$app->request->isAjax) {
                return $model;
            }
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } elseif (Yii::$app->request->isAjax) {
            return $this->renderAjax('_form', [
                        'model' => $model
            ]);
        } else {
            return $this->render('create', [
                        'model' => $model,
            ]);
        }
    }

    public function actionUpdate($id) {
        $model = $this->findModel($id);
        $settings = GamePublisherSetting::find()->where(['game_publisher_id' => $model->game_publisher_id])->all();
        if ($model->load(Yii::$app->request->post())) {
            if ($model->getDirtyAttributes(array('profile'))) {
                GamePublisherSetting::deleteAll(['game_publisher_id' => $model->game_publisher_id]);
                foreach ($_POST['SETTING'] as $key => $val) {
                    $setting = new GamePublisherSetting();
                    $setting->game_publisher_id = $model->game_publisher_id;
                    $setting->key = $key;
                    $setting->value = $val;
                    $setting->save();
                }
            } else {
                foreach ($_POST['SETTING'] as $key => $val) {
                    $new_record = true;
                    foreach ($settings as $index => $setting) {
                        if ($setting->key === $key) {
                            if ($setting->value !== $val) {
                                $setting->value = $val;
                                $setting->save();
                            }
                            $new_record = false;
                            unset($settings[$index]);
                            break;
                        }
                    }
                    if ($new_record) {
                        $setting = new GamePublisherSetting();
                        $setting->game_publisher_id = $model->game_publisher_id;
                        $setting->key = $key;
                        $setting->value = $val;
                        $setting->save();
                    }
                }

                foreach ($settings as $index => $setting) {
                    GamePublisherSetting::deleteAll(array(
                        'key' => $setting->key,
                        'game_publisher_id' => $model->game_publisher_id
                    ));
                }
            }
            $model->save();
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } else {
            return $this->render('update', [
                        'model' => $model,
            ]);
        }
    }

    public function actionSync($id) {
        $model = $this->findModel($id);
        if ($model->game_publisher_id) {
            $class = "common\components\publishers\\$model->profile";
            $profile = new $class();
            $profile->publisher = $model->game_publisher_id;
            $profile->syncProductList();
        }
    }

    public function actionDelete($id) {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionGetProfile($id) {
        $class = "common\components\publishers\\$id";
        $profile = new $class();
        return $profile->renderConfigurationField();
    }

    protected function findModel($id) {
        if (($model = GamePublisher::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

}
