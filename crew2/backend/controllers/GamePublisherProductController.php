<?php

namespace backend\controllers;

use Yii;
use common\models\GameProductForm;
use common\models\GamePublisherProductForm;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\helpers\Url;

/**
 * GamePublisherProductController implements the CRUD actions for GamePublisherProduct model.
 */
class GamePublisherProductController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public $menu = 'Product/Game Key/Publisher';

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all GamePublisherProduct models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new GamePublisherProductForm();
        $provider = $searchModel->search(Yii::$app->request->get());

        if (!isset($searchModel->status) || !isset($searchModel->game_publisher_id)) {
            return $this->redirect(['game-publisher/index']);
        }

        return $this->render('index', [
            'is_new' => ($searchModel->status == 0 || $searchModel->status == 5),
            'provider' => $provider,
            'searchModel' => $searchModel,
        ]);
    }


    public function actionView($id)
    {
        $data = (new GamePublisherProductForm())->get($id);

        return Json::encode([
            'title' => 'Game Publisher Product',
            'id' => $data,
            'body' => $this->renderPartial('view', [
                'data' => $data['data']
            ])
        ]);
    }

    public function actionEditUrlAlias($id)
    {
        $data = (new GamePublisherProductForm())->get($id);

        return Json::encode([
            'title' => 'Edit Game Publisher Product',
            'id' => $data,
            'body' => $this->renderPartial('edit-url-alias', [
                'data' => $data['data']
            ])
        ]);
    }

    public function actionUpdate($id)
    {
        if ($post = Yii::$app->request->post()) {
            $post['id'] = $id;
            $params = (new GamePublisherProductForm())->save($post);
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect(Url::to([
                'game-publisher-product/index',
                'GamePublisherProductForm[game_publisher_id]' => $params['game_publisher_id'],
                'GamePublisherProductForm[status]' => $params['status']
            ]));
        } else {
            $data = (new GamePublisherProductForm())->getGameProduct($id);
            $form_model = new GamePublisherProductForm();

            return $this->render('update', [
                'form_model' => $form_model,
                'data' => $data['data']
            ]);
        }
    }


    public function actionBatchUpdate()
    {
        $input = Yii::$app->request->post();
        (new GamePublisherProductForm())->batchUpdateProduct($input);
    }

    public function actionUpdateTitleUrlAlias()
    {
        $post = Yii::$app->request->post();

        $res = (new GamePublisherProductForm())->checkUrlAlias($post['url_alias']);

        if (!$res) {
            return Json::encode(['success' => false, 'message' => 'Duplicate Url Alias']);
        } else {
            $data = [
                'id' => $post['id'],
                'title' => $post['title'],
                'url_alias' => $post['url_alias']
            ];
            $res = (new GamePublisherProductForm())->updateTitleUrlAlias($data);
        }

        return Json::encode($res);
    }


}
