<?php

namespace backend\controllers;

use Yii;
use yii\data\ActiveDataProvider;
use yii\filters\VerbFilter;
use backend\models\LocationRestrictionForm;
use common\models\LocationRestriction;
use yii\helpers\ArrayHelper;

class LocationRestrictionController extends \backend\controllers\BaseController {

    public $menu = 'E-Commerce/Location Restriction';

    public function behaviors() {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'assign' => ['post'],
                    'remove' => ['post'],
                    'refresh' => ['post'],
                ],
            ],
        ];
    }

    public function actionIndex() {
        $dataProvider = new ActiveDataProvider([
            'query' => LocationRestriction::find()
            ->alias('lr')
            ->joinWith('countries c', 'lr.country_iso_code2 = c.countries_iso_code_2')
            ->orderBy('c.countries_name'),
        ]);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        $ctry = Yii::$app->country->getCountryList();
        $cur = Yii::$app->currency->getCurrencyList();

        return $this->render('index', [
                    'dataProvider' => $dataProvider,
                    'f_ctry' => $ctry,
                    'f_cur' => $cur
        ]);
    }

    public function actionRefresh() {
        $model = new LocationRestrictionForm(Yii::$app->request->post('c_code'));
        Yii::$app->getResponse()->format = 'json';
        return $model->getCurrency();
    }

    /**
     * Deletes an existing LocationRestriction model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id) {
        $model = $this->findModel($id);
        $beforeAttributes = $model->attributes;

        if ($model->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        }

        return $this->redirect(['index']);
    }

    public function actionSetting(){
        $id = Yii::$app->request->get('id');
        $countryArray = Yii::$app->country->getCountryList();
        $model = ($id ? $this->findModel($id) : new LocationRestriction);

        if (Yii::$app->request->post()) {
            $currencies = Yii::$app->request->post('currencies', []);
            $form = new LocationRestrictionForm(Yii::$app->request->post('c_code', $model->country_iso_code2));
            $form->addRemoveRestrictions($currencies);
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect(['index']);
        }

        if ($model->isNewRecord) {
            $existing_country = ArrayHelper::map(LocationRestriction::find()->select('country_iso_code2')->asArray()->all(),'country_iso_code2','country_iso_code2');
            $countryArray = array_diff_key($countryArray, $existing_country);
        }
        
        return $this->render('setting', [
            'model' => $model,
            'countries' => $countryArray,
        ]);
    }

    /**
     * Finds the LocationRestriction model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return LocationRestriction the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id) {
        if (($model = LocationRestriction::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

}
