<?php

namespace backend\controllers;

use backend\models\admin\form\ManualOtpVerifyForm;
use common\components\SMS;
use common\models\Customers;
use common\models\OtpManual;
use Yii;

class ManualOtpController extends BaseController
{
    public function actionIndex()
    {
        $request = Yii::$app->request;
        $data = $request->isPost ? $request->post() : $request->get();
        $model = new ManualOtpVerifyForm();
        $model->attributes = $data;
        $countries = $model->getCountries();
        $model->country = $model->getCountry($model->country_id);


        if ($request->isGet && (!$model->country_id || !$model->customer_hp)) {
            return $this->render('index', [
                'model' => $model,
                'post' => $data,
                'countries' => $countries
            ]);
        }

        if (!$model->validate()) {
            $errors = '';

            foreach ($model->errors as $error) {
                $errors .= $error[0] . '. ';
            }

            Yii::$app->session->setFlash('error', $errors);

            return $this->render('index', [
                'model' => $model,
                'post' => [],
                'countries' => $countries
            ]);
        }

        if (!SMS::verifyPhoneNumber($model->country['countries_international_dialing_code'], $model->customer_hp)) {
            Yii::$app->session->setFlash('error', 'Invalid mobile number');

            return $this->render('index', [
                'model' => $model,
                'post' => [],
                'countries' => $countries
            ]);
        }

        if ($this->customerExist($model)) {
            Yii::$app->session->setFlash('error', 'This customer is registered before');

            return $this->render('index', [
                'model' => $model,
                'post' => [],
                'countries' => $countries
            ]);
        }

        $model->getOtpManuals();

        return $this->render('index', [
            'model' => $model,
            'post' => $data,
            'countries' => $countries
        ]);
    }

    public function actionGenerateOtp()
    {
        $request = Yii::$app->request;
        $model = new ManualOtpVerifyForm();

        $model->attributes = $request->post();

        if (!$model->validate()) {
            Yii::$app->session->setFlash('error', 'Please try again to search the mobile number');
        }

        $country = $model->getCountry($model->country_id);
        $fullHp = $country['countries_international_dialing_code'] . $request->post('customer_hp');

        OtpManual::updateAll(['status' => OtpManual::STATUS_EXPIRED], [
            'country_id' => $request->post('country_id'),
            'hp' => $fullHp,
            'status' => OtpManual::STATUS_NEW
        ]);

        $token = rand(100000, 999999);

        $otpManual = new OtpManual();
        $otpManual->attributes = [
            'country_id' => $request->post('country_id'),
            'hp' => $fullHp,
            'status' => OtpManual::STATUS_NEW,
            'token' => $token,
            'remark' => $request->post('remark'),
            'expired_at' => time() + Yii::$app->params['manual_otp.lifetime'],
            'updated_by' => Yii::$app->user->identity->username
        ];
        $otpManual->save();

        Yii::$app->session->setFlash('success', 'OTP is : ' . $token);

        return $this->redirect([
            'index',
            'country_id' => $request->post('country_id'),
            'customer_hp' => $request->post('customer_hp')
        ]);
    }

    private function customerExist(ManualOtpVerifyForm $model)
    {
        return Customers::findOne([
            'customers_country_dialing_code_id' => $model->country['countries_id'],
            'customers_telephone' => $model->customer_hp
        ]);
    }
}
