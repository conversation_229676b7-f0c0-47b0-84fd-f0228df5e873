<?php

namespace backend\controllers;

use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\response;
use yii\web\UploadedFile;
use backend\models\FileUploadForm;
use common\models\MobileRechargeDenoForm;
use common\models\MobileRechargeOperatorForm;
use common\models\MobileRechargeRegionForm;

class MobileRechargeController extends BaseController {

    public function actionRegionList() {
        $this->setActiveMenu('Product/Mobile Recharge/Region');
        $filter = new MobileRechargeRegionForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('region/index', [
                    'dataProvider' => $provider,
                    'searchModel' => $filter,
        ]);
    }

    public function actionCreateRegion() {
        $this->setActiveMenu('Product/Mobile Recharge/Region');
        $model = new MobileRechargeRegionForm();

        if ($model->load(Yii::$app->request->post()) && $model->update()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('region-list');
        }

        return $this->render('region/create', [
                    'model' => $model,
        ]);
    }

    public function actionUploadOperatorImage($id = 0) {
        Yii::$app->response->format = response::FORMAT_JSON;

        $model = new FileUploadForm();

        if (Yii::$app->request->isPost) {
            $model->imageFile = UploadedFile::getInstance($model, 'imageFile');
            $model->directory = 'infolink';
            $model->subDirectory = 'mobile-recharge';
            $model->allowOverwrite = 1;
            $model->imageCompressionRatio = 1;

            try {
                if ($model->validate()) {
                    $ret = $model->uploadImages($id . '_' . time());
                } else {
                    $message = '';
                    foreach ($model->getErrors() as $field => $errors) {
                        foreach ($errors as $error) {
                            $message .= $model->getAttributeLabel($field) . ': ' . $error . "\n";
                        }
                    }
                    throw new HttpException(500, $message);
                }
            } catch (Exception $e) {
                throw new HttpException(500, $e->getMessage());
            }
            return Json::encode(['uploadURL' => $ret['url'], 'fileName' => $ret['fileName']]);
        }
    }

    public function actionUpdateRegion($id) {
        $this->setActiveMenu('Product/Mobile Recharge/Region');
        $model = (new MobileRechargeRegionForm);

        $model->load($model->get($id)['data'], '');

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->update($id)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('region-list');
        }

        return $this->render('region/update', [
                    'model' => $model,
        ]);
    }

    public function actionOperatorList() {
        $this->setActiveMenu('Product/Mobile Recharge/Operator');

        $filter = new MobileRechargeOperatorForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('operator/index', [
                    'dataProvider' => $provider,
                    'searchModel' => $filter,
        ]);
    }

    public function actionCreateOperator() {
        $this->setActiveMenu('Product/Mobile Recharge/Operator');

        $model = new MobileRechargeOperatorForm();

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->update()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('operator-list');
        }

        return $this->render('operator/create', [
                    'model' => $model,
        ]);
    }

    public function actionUpdateOperator($id) {
        $this->setActiveMenu('Product/Mobile Recharge/Operator');

        $model = (new MobileRechargeOperatorForm());

        $model->load($model->get($id)['data'], '');

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->update($id)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('operator-list');
        }

        return $this->render('operator/update', [
                    'model' => $model,
        ]);
    }

    public function actionDenoList() {
        $this->setActiveMenu('Product/Mobile Recharge/Denomination');

        $filter = new MobileRechargeDenoForm();
        $provider = $filter->search(Yii::$app->request->get());

        return $this->render('deno/index', [
                    'dataProvider' => $provider,
                    'searchModel' => $filter,
        ]);
    }

    public function actionCreateDeno() {
        $this->setActiveMenu('Product/Mobile Recharge/Denomination');

        $model = new MobileRechargeDenoForm();

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->update()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('deno-list');
        }

        return $this->render('deno/create', [
                    'model' => $model,
        ]);
    }

    public function actionUpdateDeno($id) {
        $this->setActiveMenu('Product/Mobile Recharge/Denomination');

        $model = (new MobileRechargeDenoForm());

        $model->load($model->get($id)['data'], '');

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->update($id)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('deno-list');
        }

        return $this->render('deno/update', [
                    'model' => $model,
        ]);
    }

    public function actionBatchUpdateDeno() {
        $input = Yii::$app->request->post();
        (new MobileRechargeDenoForm())->batchUpdate($input);
    }

}
