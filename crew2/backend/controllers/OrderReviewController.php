<?php

namespace backend\controllers;


use Yii;
use yii\base\Exception;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use common\models\OrderReviewForm;

/**
 * OrderReviewController implements the CRUD actions for OrderReviewController model.
 */
class OrderReviewController extends BaseController {

    public $menu = 'E-Commerce/Order Review List';

    /**
     * {@inheritdoc}
     */
    public function behaviors() {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all OrderReviewController models.
     * @return mixed
     */
    public function actionIndex() {
        $filter = new OrderReviewForm();
        $provider = $filter->search(Yii::$app->request->get(),'review_id');


        return $this->render('index', [
            'provider' => $provider,
            'filter' => $filter,
        ]);
    }

    public function actionUpdate($review_id) {
        $model = new OrderReviewForm();
        $data = $model->get($review_id);

        $post = Yii::$app->request->post();

        if ($post && $model->update($review_id, $post)) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        }

        return $this->render('update', [
            'data' => $data['data'],
            'model' => $model
        ]);
    }

}
