<?php

namespace backend\controllers;

use Yii;
use common\models\PipwavePaymentMapper;
use common\models\PipwavePaymentMapperHistory;
use backend\models\PipwavePaymentMapperForm;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * PipwavePaymentMapperController implements the CRUD actions for PipwavePaymentMapper model.
 */
class PipwavePaymentMapperController extends Controller {

    /**
     * {@inheritdoc}
     */
    public function behaviors() {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PipwavePaymentMapper models.
     * @return mixed
     */
    public function actionIndex() {
        $model = new PipwavePaymentMapperForm();
        $dataProvider = $model->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PipwavePaymentMapper model.
     * @param string $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id) {
        $model = new PipwavePaymentMapperForm();
        $historyModel = $model->searchHistory($id);

        return $this->render('view', [
                    'model' => $this->findModel($id),
                    'historyModel' => $historyModel['model'],
                    'historyData' => $historyModel['data']
        ]);
    }

    /**
     * Creates a new PipwavePaymentMapper model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate() {
        $model = new PipwavePaymentMapper();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index']);
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        return $this->render('create', [
                    'model' => $model,
        ]);
    }

    /**
     * Updates an existing PipwavePaymentMapper model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id) {
        $model = $this->findModel($id);
        $beforeAttributes = $model->attributes;

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index']);
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        return $this->render('update', [
                    'model' => $model,
        ]);
    }

    /**
     * Deletes an existing PipwavePaymentMapper model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id) {
        $model = $this->findModel($id);
        $beforeAttributes = $model->attributes;

        if ($model->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        }

        return $this->redirect(['index']);
    }

    /**
     * Finds the PipwavePaymentMapper model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return PipwavePaymentMapper the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id) {
        if (($model = PipwavePaymentMapper::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('pipwave-mapper', 'The requested page does not exist.'));
    }

    public function actionPaymentMethod($id) {
        $str = "<option>Select</option>";

        if ($id) {
            $sql = \common\models\PaymentMethods::find()
                    ->where(["payment_methods_parent_id" => $id, "payment_methods_receive_status" => 1])
                    ->andWhere(["not", ["payment_methods_title" => null]])
                    ->orderBy(['payment_methods_title' => SORT_ASC])
                    ->asArray()
                    ->all();
            foreach ($sql as $row) {
                $str .= "<option value='" . $row['payment_methods_id'] . "'>" . $row['payment_methods_title'] . "</option>";
            }
        }
        return $str;
    }

}
