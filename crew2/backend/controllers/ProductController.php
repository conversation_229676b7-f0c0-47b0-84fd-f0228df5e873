<?php

namespace backend\controllers;

use common\models\G2gProductsMapping;
use common\models\ProductSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ProductController implements the CRUD actions for Products model.
 */
class ProductController extends Controller
{
    public $menu = 'Product';
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all Products models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Updates an existing Products model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $products_id Products ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($products_id)
    {
        $model = $this->findModel($products_id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Finds the Products model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $products_id Products ID
     * @return G2gProductsMapping the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($products_id)
    {
        if (($model = G2gProductsMapping::findOne(['products_id' => $products_id])) !== null) {
            return $model;
        } else {
            $model = new G2gProductsMapping();
            $model->products_id = $products_id;
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
