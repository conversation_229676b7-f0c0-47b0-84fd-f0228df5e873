<?php

namespace backend\controllers;


use Yii;
use yii\base\Exception;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use common\models\ProductReviewForm;

/**
 * OrderReviewController implements the CRUD actions for OrderReviewController model.
 */
class ProductReviewController extends BaseController {

    public $menu = 'E-Commerce/Product Review Blacklist';

    /**
     * {@inheritdoc}
     */
    public function behaviors() {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
            ],
        ];
    }

    /**
     * Lists all OrderReviewController models.
     * @return mixed
     */
    public function actionIndex() {
        $filter = new ProductReviewForm();
        $provider = $filter->search(Yii::$app->request->get(),'blacklist_id');


        return $this->render('index', [
            'provider' => $provider,
            'filter' => $filter,
        ]);
    }

    public function actionDelete($id) {
        $filter = new ProductReviewForm();
        $filter->deleteById($id);

        return $this->redirect('index');
    }

    public function actionAdd($type, $id) {
        $filter = new ProductReviewForm();

        $input = ['type' => $type, 'id' => $id];

        $filter->addBlacklist($input);

        return $this->redirect('index');
    }

    public function actionGetItemList(){
        $input = Yii::$app->request->post();
        $output = (new ProductReviewForm())->getItemList($input);
        $output = array_flip($output);
        ksort($output);
        return Json::encode($output);

    }


}
