<?php

namespace backend\controllers;
use common\models\ProductReviewForm;
use common\models\ProductsConfigForm;
use common\models\ProductsTaxCategoryForm;
use Yii;
use yii\filters\VerbFilter;

/**
 * OrderReviewController implements the CRUD actions for OrderReviewController model.
 */
class ProductsConfigController extends BaseController
{

    public $menu = 'Product/Settings';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
            ],
        ];
    }

    /**
     * Lists all OrderReviewController models.
     * @return mixed
     */
    public function actionIndex()
    {
        $model = new ProductsConfigForm();
        $model->categories_list = $model->getCategoriesList();

        $post = Yii::$app->request->post();
        if (!empty($post)) {
            if (isset($post['form'])) {
                $model->form = $post['form'];
                $model->saveProductConfig();
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
                return $this->redirect(['index']);
            }
            return $this->redirect(['index']);
        }

        return $this->render('index', [
            'model' => $model
        ]);
    }

    public function actionDelete($id)
    {
        $filter = new ProductReviewForm();
        $filter->deleteById($id);

        return $this->redirect('index');
    }

    public function actionAdd($type, $id)
    {
        $filter = new ProductReviewForm();

        $input = ['type' => $type, 'id' => $id];

        $filter->addBlacklist($input);

        return $this->redirect('index');
    }

    public function actionGetProductsTaxForm($cid)
    {
        $form = new ProductsConfigForm();
        $models = $form->getProductsList($cid);
        return $this->renderAjax('_form_products_tax', ['models' => $models]);
    }

    public function actionExportProducts()
    {
        $model = new ProductsTaxCategoryForm();
        $model->exportAllProducts();
    }


}
