<?php

namespace backend\controllers;

use Yii;
use yii\web\Response;
use yii\web\NotFoundHttpException;
use common\models\Publisher;
use common\models\PublisherSetting;
use common\models\PublisherProduct;
use common\models\PublisherProductSearch;

class PublisherController extends BaseController
{

    public $menu = 'Product';

    public function actionIndex()
    {
        $searchModel = new Publisher();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        $model = new Publisher();
        $model->scenario = 'save';
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                foreach ($_POST['SETTING'] as $key => $val) {
                    $setting = new PublisherSetting();
                    $setting->publisher_id = $model->publisher_id;
                    $setting->key = $key;
                    $setting->value = $val;
                    $setting->save();
                }
            }

            Yii::$app->response->format = Response::FORMAT_JSON;
            if (Yii::$app->request->isAjax) {
                return $model;
            }
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } elseif (Yii::$app->request->isAjax) {
            return $this->renderAjax('_form', [
                'model' => $model
            ]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = 'save';
        $settings = PublisherSetting::find()->where(['publisher_id' => $model->publisher_id])->all();
        if ($model->load(Yii::$app->request->post())) {
            if ($model->getDirtyAttributes(array('profile'))) {
                PublisherSetting::deleteAll(['publisher_id' => $model->publisher_id]);
                if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                    foreach ($_POST['SETTING'] as $key => $val) {
                        $setting = new PublisherSetting();
                        $setting->publisher_id = $model->publisher_id;
                        $setting->key = $key;
                        $setting->value = $val;
                        $setting->save();
                    }
                }
            } else {
                if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                    foreach ($_POST['SETTING'] as $key => $val) {
                        $new_record = true;
                        foreach ($settings as $index => $setting) {
                            if ($setting->key === $key) {
                                if ($setting->value !== $val) {
                                    $setting->value = $val;
                                    $setting->save();
                                }
                                $new_record = false;
                                unset($settings[$index]);
                                break;
                            }
                        }
                        if ($new_record) {
                            $setting = new PublisherSetting();
                            $setting->publisher_id = $model->publisher_id;
                            $setting->key = $key;
                            $setting->value = $val;
                            $setting->save();
                        }
                    }
                }
                if (isset($settings) && !empty($settings)) {
                    foreach ($settings as $index => $setting) {
                        PublisherSetting::deleteAll(array(
                            'key' => $setting->key,
                            'publisher_id' => $model->publisher_id
                        ));
                    }
                }
            }
            $model->save();
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    public function actionSync($id)
    {
        $model = $this->findModel($id);
        if ($model->publisher_id) {
            ini_set("memory_limit", "-1");
            set_time_limit(0);
            $model->updateAttributes(['last_sync' => time()]);
            if (isset(Publisher::PUBLISHER_LIST[$model->profile])) {
                $class = Publisher::PUBLISHER_LIST[$model->profile];
                $profile = new $class();
                $profile->publisher = $model->publisher_id;
                $profile->sync();
            }
        }
    }

    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionGetProfile($id)
    {
        if (isset(Publisher::PUBLISHER_LIST[$id])) {
            $class = Publisher::PUBLISHER_LIST[$id];
            $profile = new $class();
            return $profile->renderConfigurationField();
        }
    }


    /**
     * Lists all PublisherProduct models.
     * @param int $publisher_id Publisher ID
     * @return string
     */
    public function actionProductList($publisher_id)
    {
        $searchModel = new PublisherProductSearch();
        $searchModel->publisher_id = $publisher_id;
        $dataProvider = $searchModel->search($this->request->queryParams);
        $dataProvider->pagination->pageSize = (Yii::$app->request->get('pageSize') ?: 20);

        return $this->render('product-list', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PublisherProduct model.
     * @param int $publisher_product_id Publisher Product ID
     * @return string
     */
    public function actionViewProduct($publisher_product_id)
    {
        return $this->render('product-view', [
            'model' => PublisherProduct::findOne($publisher_product_id),
        ]);
    }

    protected function findModel($id)
    {
        if (($model = Publisher::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

}
