<?php

namespace backend\controllers;

use Yii;
use yii\bootstrap\ActiveForm;
use yii\web\Response;
use backend\models\ReportPoForm;
use backend\models\TaxReportForm;
use backend\models\CouponUsageReportForm;
use backend\models\NoPurchaseReportForm;
use common\models\Categories;
use common\models\CustomersGroups;
use common\models\PaymentMethods;
use common\models\PipwavePaymentMapper;
use backend\components\Dashboard;

class ReportController extends BaseController
{

    public $menu = 'Report';

    public function actionPurchaseOrders()
    {
        $this->setActiveMenu('Report/Purchase Order');

        $searchModel = new ReportPoForm();
        $dataProvider = $searchModel->searchReport(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
                    'searchModel' => $searchModel,
                    'dataProvider' => $dataProvider
        ]);
    }

    /*
     * CEO
     * Sales report - # of orders - average order amount - total sales
     * - per day
     * - (per week, good to have but not really necessary)
     * - per month
     * - per year
     * Broken down by territory, product, product types.
     *
     * BDT
     * Total Sales By Country (Daily,Weekly,Monthly,Annual)
     * Total Sales by Currency (Daily,Weekly,Monthly,Annual)
     * Total Sales by PG (Daily,Weekly,Monthly,Annual) ($/%)
     * Sales by RP/NRP/Semi NRP (Daily,Weekly,Monthly,Annual) ($/%)
     * Sales by Customer Group (Countries/Continents)
     * Unique customers count by Countries (Activate/Inactive last 30days)
     * Average Size of order in $ by customer groups
     *
     * Product Sales by Countries / Country Sales by Products (%/$)
     * Customer Purchase Pattern Analysis Report: How frequent they repeat their purchase after last Order (Daily Purchase,Weekly Purchase,Monthly Purchase)
     * Top Product with high Repeat Purchase
     * Reseller Report by Countries / Continents ($/%) + Unique Number of Reseller
     * Price Recon Manual input/ System crawling + analysis vs Our selling Price
     * PG Sales performance by Selected date with a certain amount ( Paypal Campaign Query) in Count of Unique User (New / Existing/Total) / Amount in $
     *
     * ANB
     * Undelivered Order Report On That Particular Month
     * All Undelivered Report
     * CD keys Closing Stock Report
     * Refund report on that Particular month
     * Prior Order Undelivered, Delivered in current month
     * Negative SC balance
     * Sales Report 3
     *
     * MKT
     * New Paid Users
     * New Paid Users Revenue
     */

    public function actionDashboard()
    {
        $this->setActiveMenu('Report/Dashboard');

        $sr_obj = new Dashboard();
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "process") {
                $sr_obj->status = "2, 3";
            }
        }

        $data = $sr_obj->getTotalSales();
        $data["sign_up"] = $sr_obj->getNewUser();

        $data["customer_group"] = CustomersGroups::getGroupList();
        $data["_date"] = $sr_obj->fr_date;
        $data["_status"] = ($sr_obj->status == 3 ? "complete" : "process");

        return $this->render('dashboard', $data);
    }

    public function actionTax()
    {
        $this->setActiveMenu('Report/Tax');

        $provider = [];
        $model = new TaxReportForm();
        $profile = $model->getTaxProfile();
        $country = Yii::$app->country->getCountryList();

        if (Yii::$app->request->isPost) {
            $countries_code = Yii::$app->request->post('country_code', []);
            $load = $model->load(Yii::$app->request->post());
            $result = false;
            if ($load && !empty($countries_code)) {
                foreach ($countries_code as $value) {
                    $model['country_code'] = $value;
                    if ($model->validate()) {
                        $result = $model->addDeliverQueue();
                    }
                }
            }
            if ($result == true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />" .
                        "An email with report download link will be sent to <b>" . $model->report_recipient . "</b> once it is ready"
                );
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        return $this->render('tax', [
                    'model' => $model,
                    "provider" => $provider,
                    "profile" => $profile,
                    "country" => $country
        ]);
    }

    public function actionCampaign()
    {
        $this->setActiveMenu('Report/Campaign');

        $model = (new \backend\models\CampaignForm());
        if (Yii::$app->request->isPost) {
            $result = false;

            $model->cat_id = Yii::$app->request->post('cat_id', []);
            $model->payment_method = Yii::$app->request->post('payment_method', []);
            $model->ip_country_id = Yii::$app->request->post('ip_country_id', []);
            $model->exclude_customer_group = Yii::$app->request->post('exclude_customer_group', []);
            $model->use_payment_method = Yii::$app->request->post('use_payment_method', []);
            $model->load(Yii::$app->request->post());

            if ($model->validate()) {
                $result = $model->addDeliverQueue();
            }

            if ($result == true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />" .
                        "An email with report download link will be sent to <b>" . $model->recipient . "</b> once it is ready"
                );
                $model = (new \backend\models\CampaignForm());
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        return $this->render('campaign', [
                    "model" => $model,
                    "f_cat" => Categories::getSecondLayerCategory(),
                    "f_cur" => Yii::$app->currency->getCurrencyList(),
                    "f_ctry" => \common\models\Countries::countriesNameID(),
                    "f_pm" => PipwavePaymentMapper::getPaymentMethodList(),
                    "f_cust_grp" => CustomersGroups::getGroupList()
        ]);
    }

    public function actionCoupon()
    {
        $this->setActiveMenu('Report/Coupon');

        $model = new CouponUsageReportForm();

        if (Yii::$app->request->isPost) {

            $model->paymentMethods = Yii::$app->request->post('paymentMethods', []);
            $model->excludeCustomerGroup = Yii::$app->request->post('excludeCustomerGroup', []);
            $model->load(Yii::$app->request->post());

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }

            if ($model->validate()) {
                $model->saveToQueue();
            }

            if ($model->isRequestSavedToDeliveryQueue()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />" .
                        "An email with report download link will be sent to <b>" . $model->reportRecipient . "</b> once it is ready"
                );
                $model = new CouponUsageReportForm();
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        return $this->render('coupon', [
                    "model" => $model,
                    "f_pm" => PipwavePaymentMapper::getOGPaymentList(),
                    "f_cust_grp" => CustomersGroups::getGroupList(),
        ]);
    }

    public function actionNoPurchase()
    {
        $this->setActiveMenu('Report/No Purchase');
        $model = (new \backend\models\NoPurchaseReportForm());

        if (Yii::$app->request->isPost) {
            $result = false;
            $model->load(Yii::$app->request->post());

            if ($model->validate()) {
                $result = $model->addDeliverQueue();
            }

            if ($result == true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />" .
                        "An email with report download link will be sent to <b>" . $model->recipient . "</b> once it is ready"
                );
                $model = (new \backend\models\NoPurchaseReportForm());
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }
        return $this->render('no_purchase', ["model" => $model]);
    }

    public function actionSales() {
        $this->setActiveMenu('Report/Sales');
        $model = (new \backend\models\SalesReportForm());

        if (Yii::$app->request->isPost) {
            $result = false;
            $model->load(Yii::$app->request->post());
            $model->excl_cat = Yii::$app->request->post('excl_cat', []);

            if ($model->validate()) {
                $result = $model->addDeliverQueue();
            }

            if ($result == true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />" .
                    "An email with report download link will be sent to <b>" . $model->recipient . "</b> once it is ready"
                );
                $model = (new \backend\models\SalesReportForm());
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            }
        }

        $model->excl_cat = Yii::$app->params["report.exclude.category"];
        return $this->render('sales', [
            "model" => $model,
            "f_cat" => Categories::getSecondLayerCategory(),
        ]);
    }

}