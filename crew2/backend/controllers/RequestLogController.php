<?php

namespace backend\controllers;

use offgamers\base\components\LogComponent;
use Yii;
use offgamers\base\models\IncomingRequestLog;
use offgamers\base\models\OutgoingRequestLog;
use offgamers\base\models\DevDebugLog;
use offgamers\base\models\ModelAuditHistoryLog;
use yii\helpers\Json;
use yii\helpers\HtmlPurifier;

class RequestLogController extends BaseController
{
    public function actionIncomingLog()
    {
        $searchModel = new IncomingRequestLog();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('incoming-table', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionOutgoingLog()
    {
        $searchModel = new OutgoingRequestLog();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('outgoing-table', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionDebugLog()
    {
        return $this->render('debug-table');
    }

    public function actionModelAuditLog($table, $id)
    {
        $table = strip_tags(HtmlPurifier::process($table));
        $id = (int)HtmlPurifier::process($id);
        return $this->render('model-audit-table', ['table' => $table, 'id' => $id]);
    }


    public function actionGetModelAuditLog($date, $table, $id)
    {
        $date = explode(' - ', $date);
        $today = date('Y-m-d', time());
        $new_data = [];

        if (!isset($date[1])) {
            $date = array(date('Y-m-d', strtotime('-1 months today')), $today);
        }

        $table = HtmlPurifier::process(preg_replace('/[0-9\@\.\;\" "]+/', '', $table));
        $id = (int)HtmlPurifier::process($id);
        
        $data = ModelAuditHistoryLog::find()->where(['table' => $table, 'reference_data_id' => $id])
            ->andWhere(['>=', 'created_at', strtotime($date[0])])->andWhere(['<=', 'created_at', strtotime($date[1]) + 86400 - 1])
            ->asArray()->all();

        $logged_data = (new LogComponent())->getModelAuditLog($table, $id, $date[0], $date[1]);

        foreach ($logged_data as $key => $value) {
            foreach ($value as $list) {
                $list['path'] = $key;
                $data[] = $list;
            }
        }

        foreach ($data as $item) {
            $new_data[] = [
                'model_audit_history_log_id' => $item['model_audit_history_log_id'],
                'description' => $item['description'],
                'type' => $item['action'],
                'created_at' => $item['created_at'],
                'updated_by' => $item['updated_by'],
                'date' => date('d/m/Y H:i:s A', $item['created_at']),
                'path' => (!empty($item['path']) ? $item['path'] : null)
            ];
        }

        $return_arr = [
            'recordTotal' => count($new_data),
            'recordsFiltered' => count($new_data),
            'data' => $new_data
        ];

        return Json::encode($return_arr, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE);
    }

    public function actionGetDebugLog($date)
    {
        $date = explode(' - ', $date);
        $today = date('Y-m-d', time());
        $new_data = [];
        $data = [];

        if (!isset($date[1])) {
            $date = array($today, $today);
        }

        $data = DevDebugLog::find()->where(['>=', 'created_at', strtotime($date[0])])->andWhere(['<=', 'created_at', strtotime($date[1]) + 86400 - 1])->asArray()->all();

        $logged_data = (new LogComponent())->getDevDebugLog($date[0], $date[1]);

        foreach ($logged_data as $key => $value) {
            foreach ($value as $list) {
                $list['path'] = $key;
                $data[] = $list;
            }
        }

        foreach ($data as $item) {
            $new_data[] = [
                'dev_debug_log_id' => $item['dev_debug_log_id'],
                'app' => $item['app'],
                'tag' => $item['tag'],
                'date' => date('d/m/Y H:i:s A', $item['created_at']),
                'created_at' => $item['created_at'],
                'path' => (!empty($item['path']) ? $item['path'] : null)
            ];
        }

        $return_arr = [
            'recordTotal' => count($new_data),
            'recordsFiltered' => count($new_data),
            'data' => $new_data
        ];

        return Json::encode($return_arr, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE);
    }

    public function actionViewIncomingLog($id, $s3_path = '', $json = false)
    {
        $id = (int)HtmlPurifier::process($id);
        if (!empty($s3_path) && $s3_path != 'null' && (substr_compare($s3_path, '.json', -5) === 0)) {
            $data = (new LogComponent())->getLogByPath($s3_path);
        } else {
            $data = IncomingRequestLog::find()->where(['incoming_request_log_id' => $id])->asArray()->one();
        }

        if ($json) {
            return $this->renderJsonContent($data, 'incoming_log', $id);
        }

        return Json::encode([
            'title' => 'Incoming Request Log',
            'id' => $id,
            'body' => $this->renderPartial('view-incoming', [
                'data' => $data
            ])
        ]);
    }

    public function actionViewOutgoingLog($id, $s3_path = '', $json = false)
    {
        $id = (int)HtmlPurifier::process($id);
        if (!empty($s3_path) && $s3_path != 'null' && (substr_compare($s3_path, '.json', -5) === 0)) {
            $data = (new LogComponent())->getLogByPath($s3_path);
        } else {
            $data = OutgoingRequestLog::find()->where(['outgoing_request_log_id' => $id])->asArray()->one();
        }

        if ($json) {
            return $this->renderJsonContent($data, 'outgoing_log', $id);
        }

        return Json::encode([
            'title' => 'Outgoing Request Log',
            'id' => $id,
            'body' => $this->renderPartial('view-outgoing', [
                'data' => $data
            ])
        ]);
    }

    public function actionViewDebugLog($id, $s3_path = '', $json = false)
    {
        if (!empty($s3_path) && $s3_path != 'null') {
            $raw = (new LogComponent())->getLogByPath($s3_path);
            foreach ($raw as $item) {
                if ($item['dev_debug_log_id'] == $id) {
                    $data = $item;
                    break;
                }
            }
        } else {
            $data = DevDebugLog::find()->where(['dev_debug_log_id' => $id])->asArray()->one();
        }

        if ($json) {
            return $this->renderJsonContent($data, 'debug_log', $id);
        }

        return Json::encode([
            'title' => 'Debug Log',
            'id' => '',
            'body' => $this->renderPartial('view-debug', [
                'data' => $data
            ])
        ]);
    }

    public function actionViewModelAuditLog($id, $s3_path = '', $json = false)
    {
        if (!empty($s3_path) && $s3_path != 'null') {
            $data = (new LogComponent())->getLogByPath($s3_path, $id, 'model_audit_history_log_id');
        } else {
            $data = ModelAuditHistoryLog::find()->where(['model_audit_history_log_id' => $id])->asArray()->one();
        }

        if ($json) {
            return $this->renderJsonContent($data, 'model_audit_log', $id);
        }

        return Json::encode([
            'title' => 'View Model Audit Log',
            'id' => $id,
            'body' => $this->renderPartial('view-model-audit', [
                'data' => $data
            ])
        ]);
    }

    private function renderJsonContent($data, $type, $id)
    {
        Yii::$app->response->setDownloadHeaders($type . '_' . $id . '.json', 'application.json');
        foreach ($data as $key => $value) {
            $data[$key] = LogComponent::decodeJson($value);
        }
        return JSON::encode($data);
    }

}
