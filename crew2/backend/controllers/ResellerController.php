<?php

namespace backend\controllers;

use common\models\G2gProductsMapping;
use common\models\Reseller;
use common\models\resellers\G2G;
use common\models\ResellerSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\Response;
use yii\web\NotFoundHttpException;

class ResellerController extends BaseController
{
    public $menu = 'Product';

    public function actionIndex()
    {
        $searchModel = new Reseller();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionG2gOrder($id)
    {
        if ($this->request->isPost) {
            $g2g = new G2G;
            $g2g->reseller = $id;
            $g2g->processDelivery(Yii::$app->request->post('g2g_order_id'), true);
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('/reseller/g2g-order?id=' . $id);
        }

        return $this->render('process-order');
    }

    public function actionCreate()
    {
        $model = new Reseller();
        $model->scenario = 'save';
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                foreach ($_POST['SETTING'] as $key => $val) {
                    $setting = new ResellerSetting();
                    $setting->reseller_id = $model->reseller_id;
                    $setting->key = $key;
                    $setting->value = $val;
                    $setting->save();
                }
            }

            Yii::$app->response->format = Response::FORMAT_JSON;

            if (Yii::$app->request->isAjax) {
                return $model;
            }

            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));

            return $this->redirect('index');
        } elseif (Yii::$app->request->isAjax) {
            return $this->renderAjax('_form', [
                'model' => $model
            ]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = 'save';
        $settings = ResellerSetting::find()->where(['reseller_id' => $model->reseller_id])->all();
        if ($model->load(Yii::$app->request->post())) {
            if ($model->getDirtyAttributes(array('profile'))) {
                ResellerSetting::deleteAll(['reseller_id' => $model->reseller_id]);
                if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                    foreach ($_POST['SETTING'] as $key => $val) {
                        $setting = new ResellerSetting();
                        $setting->reseller_id = $model->reseller_id;
                        $setting->key = $key;
                        $setting->value = $val;
                        $setting->save();
                    }
                }
            } else {
                if (isset($_POST['SETTING']) && !empty($_POST['SETTING'])) {
                    foreach ($_POST['SETTING'] as $key => $val) {
                        $new_record = true;
                        foreach ($settings as $index => $setting) {
                            if ($setting->key === $key) {
                                if ($setting->value !== $val) {
                                    $setting->value = $val;
                                    $setting->save();
                                }
                                $new_record = false;
                                unset($settings[$index]);
                                break;
                            }
                        }
                        if ($new_record) {
                            $setting = new ResellerSetting();
                            $setting->reseller_id = $model->reseller_id;
                            $setting->key = $key;
                            $setting->value = $val;
                            $setting->save();
                        }
                    }
                }
                if (isset($settings) && !empty($settings)) {
                    foreach ($settings as $index => $setting) {
                        ResellerSetting::deleteAll(array(
                            'key' => $setting->key,
                            'reseller_id' => $model->reseller_id
                        ));
                    }
                }
            }
            $model->save();
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    public function actionGetProfile($id)
    {
        if (isset(Reseller::RESELLER_LIST[$id])) {
            $class = Reseller::RESELLER_LIST[$id];
            $profile = new $class();
            return $profile->renderConfigurationField();
        }
    }

    protected function findModel($id)
    {
        if (($model = Reseller::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

}
