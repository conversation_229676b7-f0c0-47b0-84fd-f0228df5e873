<?php

namespace backend\controllers;

use Yii;
use common\models\CustomSeoForm;
use yii\helpers\Json;
use yii\helpers\HtmlPurifier;

class SeoController extends BaseController
{

    public $menu = 'E-Commerce/SEO';

    public function actionIndex()
    {
        $filter = new CustomSeoForm();
        $queryString = Yii::$app->request->get();
        $queryString['CustomSeoForm']['is_general_seo'] = 1;
        $provider = $filter->search($queryString);
        return $this->render('index', [
            'provider' => $provider,
            'filter' => $filter,
        ]);
    }

    public function actionUpdate()
    {
        $model = new CustomSeoForm();
        $post = Yii::$app->request->post();
        $default = [];

        if ($post && $model->load($post)) {
            $model->save($post);
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->redirect('index');
        } else {
            $input = Yii::$app->request->get();
            if (!empty($input['type'])) {
                $data = [
                    'type' => htmlspecialchars_decode(strip_tags(HtmlPurifier::process($input['type']))),
                    'id' => (int) HtmlPurifier::process($input['id'])
                ];
                $default = $model->getDefault($data);
                $model->type = $default['type'];
                $model->id = $default['id'];
            }
        }

        return $this->render('update', [
            'data' => $default,
            'model' => $model
        ]);
    }

    public function actionGetItemList()
    {
        $input = Yii::$app->request->post();
        $output = (new CustomSeoForm())->getItemList($input);
        natcasesort($output);
        $output = array_flip($output);
        return Json::encode($output);
    }
}
