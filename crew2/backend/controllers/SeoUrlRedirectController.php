<?php

namespace backend\controllers;

use common\models\SeoUrlRedirect;
use common\models\SeoUrlRedirectSearch;
use Exception;
use Yii;
use yii\bootstrap\ActiveForm;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class SeoUrlRedirectController extends BaseController
{

    public $menu = 'E-Commerce/SEO URL Redirect';

    public function actionIndex()
    {
        $searchModel = new SeoUrlRedirectSearch();

        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        $model = new SeoUrlRedirect();

        if (Yii::$app->request->isPost) {
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }
        }

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                return $this->redirect(['index']);
            } else {
                $this->setErrorMessages($model->getErrors());
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if (Yii::$app->request->isPost) {
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }
        }

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                return $this->redirect(['index']);
            } else {
                $this->setErrorMessages($model->getErrors());
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * @throws \Throwable
     */
    public function actionDelete($id)
    {
        try {
            $model = $this->findModel($id);
            $model->delete();
        } catch (Exception $exception) {
            throw new Exception(
                sprintf("Error deleting record due to [%s]",
                    $exception->getMessage()));
        }

        return $this->redirect(['index']);
    }

    protected function findModel($id)
    {
        if (($model = SeoUrlRedirect::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    private function setErrorMessages(array $errors): void
    {
        $err_str = "";
        $error = json_decode($errors, true);

        foreach ($error as $i => $err) {
            $err_str .= "<br />" . $err[0];
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . $err_str);
    }

}
