<?php

namespace backend\controllers;

use Yii;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use common\models\LoginForm;
use mdm\admin\models\form\PasswordResetRequest;
use yii\helpers\Json;
use mdm\admin\models\form\ResetPassword;
use yii\base\InvalidParamException;
use yii\web\BadRequestHttpException;

/**
 * Site controller
 */
class SiteController extends \backend\controllers\BaseController
{

    public function behaviors()
    {
        return ArrayHelper::merge(
            parent::behaviors(), [
                'access' => [
                    'rules' => [
                        [
                            'actions' => ['login', 'error', 'request-password-reset', 'reset-password'],
                            'allow' => true,
                        ],
                        [
                            'actions' => ['logout', 'index'],
                            'allow' => true,
                            'roles' => ['@'],
                        ],
                    ],
                ],
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'logout' => ['post'],
                    ],
                ],
            ]
        );
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
        ];
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        return $this->render('index');
    }

    /**
     * Login action.
     *
     * @return string
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $model = new LoginForm();
        if ($model->load(Yii::$app->request->post()) && $model->login()) {
            return $this->goBack();
        } else {
            $model->password = '';
            $this->layout = Yii::$app->params['loginLayout'];
            return $this->render('login', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Logout action.
     *
     * @return string
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->goHome();
    }

    /**
     * Change password
     * @return string
     */
    public function actionChangePassword()
    {
        $model = new \mdm\admin\models\form\ChangePassword();
        if ($model->load(Yii::$app->getRequest()->post()) && $model->change()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return $this->goHome();
        }

        return $this->render('change-password', [
            'model' => $model,
        ]);
    }

    public function actionResetPassword($token)
    {
        try {
            $model = new ResetPassword($token);
        } catch (InvalidParamException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $this->layout = Yii::$app->params['loginLayout'];
        $login_model = new LoginForm();

        if ($model->load(Yii::$app->getRequest()->post(), '') && $model->validate() && $model->resetPassword()) {
            $return_array = ['success' => true, 'message' => 'New password was saved.'];
            return Json::encode($return_array);
        } elseif (!empty($model->getErrors())) {
            $return_array = ['success' => false, 'message' => Json::encode($model->getErrors())];
            return Json::encode($return_array);
        }

        return $this->render('login', ['model' => $login_model]);
    }

    /**
     * Request reset password
     * @return string
     */
    public function actionRequestPasswordReset()
    {
        $model = new PasswordResetRequest();
        $model->load(Yii::$app->request->get(), '');
        $model->validate();
        if ($model->load(Yii::$app->request->get(), '') && $model->validate()) {
            if ($model->sendEmail()) {
                $return_array = ['success' => true, 'message' => 'Check your email for further instructions.'];
            } else {
                $return_array = ['success' => false, 'message' => '\'Sorry, we are unable to reset password for email provided.\''];
            }
        } else {
            $return_array = ['success' => false, 'message' => implode("<br>", $model->getErrors('email'))];
        }
        return Json::encode($return_array);
    }

}


