<?php

namespace backend\controllers;

use Yii;
use yii\helpers\Url;
use backend\models\StaticPageForm;
use common\models\StaticPageType;

class StaticPageController extends BaseController {

    public $menu = 'E-Commerce/Static Page';

    public function actionIndex() {
        $model = new StaticPageForm();
        $dataProvider = $model->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate() {
        $model = new StaticPageForm();
        if (Yii::$app->request->post()) {
            $model->staticPageContentPost = Yii::$app->request->post('StaticPageContent');
            $model->static_page_type_id = Yii::$app->request->post('StaticPageForm')['static_page_type_id'];
            $model->scenario = 'create_new';
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect('index');
            }
        }

        if (Yii::$app->request->get('static_page_type_id')) {
            $model->static_page_type_id = Yii::$app->request->get('static_page_type_id');
        }

        $model->isNewRecord = true;
        return $this->render('create', [
                    'model' => $model,
        ]);
    }

    public function actionUpdate($id) {
        $model = new StaticPageForm($id);
        if (Yii::$app->request->post()) {
            $model->staticPageContentPost = Yii::$app->request->post('StaticPageContent');
            $model->static_page_type_id = Yii::$app->request->post('StaticPageForm')['static_page_type_id'];
            $model->scenario = 'static_page_update';
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect('index');
            }
        }
        return $this->render('update', [
                    'model' => $model,
        ]);
    }

    public function actionDelete($id) {
        $model = new StaticPageForm($id);

        if ($model->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }

        return $this->redirect('index');
    }

    # Static Page Type Sections

    public function actionStaticPageType() {
        $model = new StaticPageForm();
        $dataProvider = $model->searchType();
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('static-page-type/index', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreateType() {
        $model = new StaticPageType();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS") . "<br />"
                        . "Create the first " . $model->type_title . " static page, or click <a href='" . Url::toRoute('static-page/static-page-type') . "'>here</a> to go back.");
                return $this->redirect(['create', 'static_page_type_id' => $model->static_page_type_id]);
            } else {
                $error = "";
                if ($model->getErrors()) {
                    $error = "<br />" . $this->errorString($model->getErrors());
                }
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . $error);
            }
        }

        return $this->render('static-page-type/create', [
                    'model' => $model,
        ]);
    }

    public function actionUpdateType($id) {
        $model = $this->findModel($id);
        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['static-page-type']);
            } else {
                $error = "";
                if ($model->getErrors()) {
                    $error = "<br />" . $this->errorString($model->getErrors());
                }
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . $error);
            }
        }
        return $this->render('static-page-type/update', [
                    'model' => $model,
        ]);
    }

    public function actionDeleteType($id) {
        $model = $this->findModel($id);
        if ($model->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        }
        return $this->redirect(['static-page-type']);
    }

    protected function findModel($id) {
        if (($model = StaticPageType::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException("The requested page does not exist.");
    }

    protected function errorString($error) {
        $errorString = '';
        if ($error) {
            $errorString = '&nbsp;:<br>';
            foreach ($error as $key => $value) {
                foreach ($value as $param => $text) {
                    $errorString .= $text;
                }
                $errorString .= '<br>';
            }
        }
        return $errorString;
    }

}
