<?php

namespace backend\controllers;

use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\bootstrap\ActiveForm;
use yii\data\Pagination;
use backend\models\StoreCreditForm;
use yii\helpers\HtmlPurifier;

/**
 * Site controller
 */
class StoreCreditController extends \backend\controllers\BaseController {

    public $menu = 'Sales/Store Credit';

    public function actions() {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
        ];
    }

    private function exportToCSV($model, $provider) {
        header('Content-Type: text/x-csv');
        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Content-Disposition: attachment; filename="Store_Credit_Movement_' . date('Y-m-d', strtotime($model->start_date)) . '-' . date('Y-m-d', strtotime($model->end_date)) . '.csv"');
        header('Pragma: no-cache');

        // header
        $sequence = [
            'count' => '#',
            'request_id' => 'Transaction ID',
            'created_date' => 'Date/Time',
            'order_id' => 'Order ID',
            'activity_title' => 'Activity',
            'requesting_id' => 'By',
            'user_id' => 'Customer ID',
            'param_2' => 'Payment Gateway',
            'activity_description' => 'Comment',
            'transaction_currency' => 'Currency',
            'transaction_type_debit' => 'Debit',
            'transaction_type_credit' => 'Credit',
            'new_amount' => 'Balance',
        ];
        echo implode('|', $sequence) . "\n";

        // body
        $numbering = 0;
        foreach ($provider as $data) {
            $numbering++;

            // create a $dt object with the UTC timezone
            $ts = explode('.', $data['created_date']);
            $dt = new \DateTime('@' . $ts[0]);
            // change the timezone of the object without changing it's time
            $dt->setTimezone(new \DateTimeZone('Asia/Kuala_Lumpur'));
            $createdDate = $dt->format('Y-m-d H:i:s');

            echo $numbering . '|';
            echo $data['request_id'] . '|';
            echo $createdDate . '|';
            echo ((isset($data['order_id'])) ? $data['order_id'] : 'NA') . '|';
            echo $data['activity_title'] . '|';
            echo $data['requesting_id'] . '|';
            echo $data['user_id'] . '|';
            echo $data['param_2'] . '|';
            echo trim(preg_replace('/\s+/', ' ', str_replace('|', '-', $data['activity_description']))) . '|';
            echo $data['transaction_currency'] . '|';
            echo (($data['transaction_type'] == 'SUBTRACT_CREDIT') ? $data['transaction_amount'] : '-') . '|';
            echo (($data['transaction_type'] == 'ADD_CREDIT') ? $data['transaction_amount'] : '-') . '|';
            echo $data['new_amount'];
            echo "\n";
        }
        exit;
    }

    public function actionValidation() {
        $model = new StoreCreditForm;

        if ($model->load(Yii::$app->request->post())) {
            if (isset($model->report_type)) {
                $model->scenario = ($model->report_type == 2) ? 'sc_movement' : 'by_customer';
            } elseif (isset($model->transaction_type)) {
                $model->scenario = ($model->transaction_type == 'add' || $model->transaction_type == 'deduct') ? 'transaction_credit' : 'convert_credit';
            }

            Yii::$app->response->format = 'json';
            return ActiveForm::validate($model);
        }
    }

    public function actionIndex() {
        $export = Yii::$app->request->post('submit_type');

        $model = new StoreCreditForm;
        $data = $model->load(Yii::$app->request->post());

        $provider = [];
        $pages = 0;
        $startAt = 0;
        $endAt = 0;
        $invalid_characters = ['"', "'", "(", ")", "{", "}"];

        if (Yii::$app->request->isAjax) {
            // prevent loading asset again
            \yii\base\Event::on(\yii\web\View::className(), \yii\web\View::EVENT_AFTER_RENDER, function ($e) {
                $e->sender->assetBundles = [];
            });

            $formType = Yii::$app->request->get('form_type');

            switch ($formType) {
                case 'add':
                    $model->scenario = 'transaction_credit';
                    $titleText = Yii::t('store-credit', 'Manual Addition');
                    break;
                case 'deduct':
                    $model->scenario = 'transaction_credit';
                    $titleText = Yii::t('store-credit', 'Manual Deduction');
                    break;
                case 'convert':
                    $model->scenario = 'convert_credit';
                    $titleText = Yii::t('store-credit', 'Store Credit Conversion');
                    break;
            }

            $model->transaction_type = $formType;
            $model->start_date = str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process(Yii::$app->request->get('start_date'))));
            $model->end_date = str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process(Yii::$app->request->get('end_date'))));
            $model->getBalance(str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process(Yii::$app->request->get('customers_id')))));

            return Json::encode([
                        'title' => $titleText,
                        'body' => $this->renderAjax('_form', [
                            'model' => $model,
                            'form_type' => $formType
                        ])
            ]);
        } else {
            if (!$data) {
                $page = Yii::$app->request->get('page');
                $page = isset($page) ? intval($page) : 1;

                $pageSize = Yii::$app->request->get('pagesize');
                $pageSize = isset($pageSize) ? intval($pageSize) : 20;

                $transactionId = str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process(Yii::$app->request->get('transaction_id'))));

                $data = $model->load(Yii::$app->request->get(), '');

                $model->scPage = $page;
                $model->scPageSize = $pageSize;

                if (isset($transactionId) && !empty($transactionId)) {
                    $model->end_date = date('Y-m-d');
                    $model->report_type = 1;
                }
            }

            if ($data) {
                // Do export to csv
                if (isset($export) && $export == 'export') {
                    if ($csvProvider = $model->getAllTransaction()) {
                        $this->exportToCSV($model, $csvProvider);
                    }
                }

                if ($provider = $model->search()) {
                    $pages = new Pagination([
                        'defaultPageSize' => $model->scPageSize,
                        'totalCount' => $model->scCount,
                        'pageSizeLimit' => false,
                        'params' => [
                            'page' => $model->scPage,
                            'pagesize' => $model->scPageSize,
                            'report_type' => $model->report_type,
                            'transaction_id' => $model->transaction_id,
                            'customers_id' => $model->customers_id,
                            'start_date' => $model->start_date,
                            'end_date' => $model->end_date,
                            'activity' => $model->activity,
                        ]
                    ]);

                    // Custom row numbering
                    $startAt = $model->scPageSize * ($model->scPage - 1) + 1;
                    if ($model->scCount < $model->scPageSize) {
                        $endAt = $model->scCount;
                    } elseif (($model->scCount / $model->scPageSize) < $model->scPage) {
                        $endAt = $model->scCount - $model->scPageSize * $model->scPage + $model->scPageSize + $startAt - 1;
                    } else {
                        $endAt = ($model->scPageSize) + $startAt - 1;
                    }
                } else {
                    $model->errors = true;
                    $provider = [];
                }
            }

            return $this->render('index', [
                        'model' => $model,
                        'provider' => $provider,
                        'pages' => $pages,
                        'startAt' => $startAt,
                        'endAt' => $endAt,
            ]);
        }
    }

    public function actionCreate() {
        $permissionError = false;
        $model = new StoreCreditForm;
        $model->load(Yii::$app->request->post());

        $model->requesting_id = Yii::$app->user->identity->username;
        $model->requesting_role = 'admin';

        switch ($model->transaction_type) {
            case 'convert':
                if (Yii::$app->user->can('[Store Credit] Manual Convert')) {
                    $model->activity = 'MX';
                } else {
                    $permissionError = true;
                    Yii::$app->session->setFlash('site', 'FLASH_WARNING_PERMISSION_DENIED');
                }
                break;
            case 'add':
                if (Yii::$app->user->can('[Store Credit] Manual Add')) {
                    $model->activity = 'MI';
                } else {
                    $permissionError = true;
                    Yii::$app->session->setFlash('site', 'FLASH_WARNING_PERMISSION_DENIED');
                }
                break;
            case 'deduct':
                if (Yii::$app->user->can('[Store Credit] Manual Deduct')) {
                    $model->activity = 'MR';
                } else {
                    $permissionError = true;
                    Yii::$app->session->setFlash('site', 'FLASH_WARNING_PERMISSION_DENIED');
                }
                break;
        }

        if (!$permissionError) {
            // Save data
            $model->save();
        }

        return $this->redirect([
                    'index',
                    'customers_id' => $model->customers_id,
                    'start_date' => $model->start_date,
                    'end_date' => $model->end_date,
                    'report_type' => 1,
        ]);
    }

    public function actionScExchangeRate() {
        $amount = Yii::$app->request->get('amount');
        $fromCurr = Yii::$app->request->get('from_curr');
        $toCurr = Yii::$app->request->get('to_curr');

        return Json::encode([
                    'conversion_amount' => Yii::$app->currency->advanceCurrencyConversion($amount, $fromCurr, $toCurr),
                    'conversion_rate' => Yii::$app->currency->advanceCurrencyConversionRate($fromCurr, $toCurr),
        ]);
    }

    public function actionGetOpenCloseBalance() {
        $snapshotDate = Yii::$app->request->get('sd');
        $blanceType = Yii::$app->request->get('t');

        $model = new StoreCreditForm;
        $balanceList = $model->getOpenCloseBalance($snapshotDate, $blanceType);

        return Json::encode(['html' => $balanceList]);
    }

    public function actionUpdateAccount($customersId, $action) {
        $permissionError = false;
        Yii::$app->response->format = \yii\web\response::FORMAT_JSON;

        $model = new StoreCreditForm;
        $model->customers_id = $customersId;
        $model->requesting_id = Yii::$app->user->identity->username;
        $model->requesting_role = 'admin';

        switch ($action) {
            case 'activate':
                if (Yii::$app->user->can('[Store Credit] Activate Account')) {
                    $model->scenario = 'sc_activate';
                } else {
                    $permissionError = true;
                    $response = ['error' => Yii::t('store-credit', 'You don\'t have the permission to activate the Store Credit account')];
                }
                break;
            case 'suspend':
                if (Yii::$app->user->can('[Store Credit] Suspend Account')) {
                    $model->scenario = 'sc_suspend';
                } else {
                    $permissionError = true;
                    $response = ['error' => Yii::t('store-credit', 'You don\'t have the permission to suspend the Store Credit account')];
                }
                break;
        }

        if (!$permissionError) {
            $response = $model->updateAccountStatus($customersId);
        }

        return Json::encode($response);
    }

    public function actionQueue() {
        $model = (new \common\models\StoreCreditForm);
        $data = \common\models\DeliverQueue::find()->where(['type' => 'SC'])->orderBy('created_at DESC')->limit(50)->asArray()->all();
        $total = \common\models\DeliverQueue::find()->where(['type' => 'SC'])->count();

        return $this->render('queue', ['model' => $model, 'data' => $data, 'total' => $total]);
    }

    public function actionBatchUpdate() {
        $model = (new \common\models\StoreCreditForm);
        $model->scenario = 'batch_update';

        if (Yii::$app->user->can('[Store Credit] Manual Add') && Yii::$app->user->can('[Store Credit] Manual Deduct') && Yii::$app->user->can('[Store Credit] Manual Convert')) {
            if (Yii::$app->request->isPost && $model->load(Yii::$app->request->post()) && $model->validate()) {
                $resp = $model->createQueue();
                if ($resp) {
                    Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                } else {
                    Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
                }
                return $this->redirect('queue');
            }
        } else {
            Yii::$app->session->setFlash('site', 'FLASH_WARNING_PERMISSION_DENIED');
        }

        return $this->render('batch-update', [
                    'model' => $model,
                    'f_cur' => Yii::$app->currency->getCurrencyList(),
                    'f_ctry' => Yii::$app->country->getCountryList()
        ]);
    }

}
