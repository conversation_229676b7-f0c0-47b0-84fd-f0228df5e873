<?php

namespace backend\controllers;

use common\models\OrdersTaxRuleDetailsForm;
use common\models\OrdersTaxRulesForm;
use offgamers\base\models\OrdersTaxRuleMapping;
use Yii;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;
use backend\models\TaxForm;
use common\models\OrdersTaxConfiguration;
use common\models\OrdersTaxConfigurationDescription;
use common\models\OrdersTaxCustomers;
use yii\helpers\HtmlPurifier;

class TaxController extends BaseController {

    public $menu = 'Finance/Tax';

    public function actionIndex() {
        $model = new TaxForm();
        $dataProvider = $model->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('index', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Tax setting.
     * If creation is successful, the browser will be redirected to the 'index' page.
     * @return mixed
     */
    public function actionCreate() {
        $model = new TaxForm();
        $model->isNewRecord = true;
        $model->scenario = "setting";
        $post = Yii::$app->request->post();

        if ($post) {
            $model->load($post);
            $model->orders_tax_title = $post["TaxForm"]["orders_tax_title"];
            $model->orders_tax_title_short = $post["TaxForm"]["orders_tax_title_short"];
            $model->orders_tax_message = $post["orders_tax_message"];
            $result = $model->process();

            if ($result === true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index']);
            } else {
                $err_str = "";
                $error = json_decode($result, true);
                foreach ($error as $i => $err) {
                    $err_str .= "<br />" . $err[0];
                }
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . $err_str);
            }
        }

        return $this->render('create', ['model' => $model]);
    }

    /**
     * Updates an existing Tax setting.
     * If update is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id = "") {
        $model = new TaxForm();
        $model->getSetting($id);
        $model->scenario = "setting";
        $post = Yii::$app->request->post();

        if ($post) {
            $model->load($post);
            $model->orders_tax_id = $id;
            $model->orders_tax_title = $post["TaxForm"]["orders_tax_title"];
            $model->orders_tax_title_short = $post["TaxForm"]["orders_tax_title_short"];
            $model->orders_tax_message = $post["orders_tax_message"];
            $result = $model->process();

            if ($result === true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index']);
            } else {
                $err_str = "";
                $error = json_decode($result, true);
                foreach ($error as $i => $err) {
                    $err_str .= "<br />" . $err[0];
                }
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . $err_str);
            }
        }

        return $this->render('update', ['model' => $model]);
    }

    public function actionTaxCountryCurrency($id = "") {
        if ($id) {
            $m_ctry = \common\models\Countries::findOne(['countries_iso_code_2' => $id]);
            if (isset($m_ctry->countries_currencies_id)) {
                $m_cur = \common\models\Currencies::findOne($m_ctry->countries_currencies_id);
            }
        }
        return (isset($m_cur->code) ? $m_cur->code : "");
    }

    /**
     * Deletes an existing Tax setting.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id) {
        if ($this->findModel($id)->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }

        return $this->redirect(['index']);
    }

    protected function findModel($id) {
        if (($model = OrdersTaxConfiguration::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Manage Business Tax Application Form.
     * If update is successful, the browser will refresh application form.
     * @param integer $id
     * @param integer $field_id
     * @return mixed
     */
    public function actionBusiness($id) {
        $model = new TaxForm();
        $model->getSetting($id);
        $model->scenario = "business";

        $post = Yii::$app->request->post();
        if ($post) {
            if ($model->load($post) && $model->validate()) {
                $m = $this->findModel($id);
                $m->business_tax_status = $model->business_tax_status;
                $m->save();

                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            } else {
                Yii::$app->session->setFlash("success", Yii::t("error", "FLASH_ERROR"));
            }
            return $this->redirect(['business', 'id' => $id]);
        }

        $form = json_decode($model->business_tax_form, true);
        return $this->render('_form_business', [
                    'model' => $model,
                    'f_data' => $form
        ]);
    }

    public function actionUpdateBusinessForm($id = "", $field_id = "") {
        $id = (int)HtmlPurifier::process($id);
        $field_id = (int)HtmlPurifier::process($field_id);
        $f_data = [];
        $post = Yii::$app->request->post();
        $id = (isset($post["id"]) ? $post["id"] : $id);

        $model = new TaxForm();
        $model->getSetting($id);

        if ($post && $model->orders_tax_id) {
            $result = $model->processForm($post);
            if ($result === true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return Json::encode(['success' => true, 'message' => Yii::t("site", "FLASH_SUCCESS")]);
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . ($result === false ? "" : "<br />" . $result));
                return Json::encode(['success' => false, 'message' => Yii::t("site", "FLASH_ERROR") . ($result === false ? "" : "<br />" . $result)]);
            }
        }

        if ($field_id) {
            $form = json_decode($model->business_tax_form, true);
            if ($form && isset($form[1])) {
                foreach ($form[1] as $cnt => $field) {
                    if ($field["id"] == $field_id) {
                        $f_data = $field;
                        break;
                    }
                }
            }
        }

        $request = Yii::$app->request;

        if ($request->isAjax) {
            return Json::encode([
                        'title' => ($f_data ? $field["title"] : "New"),
                        'body' => $this->renderPartial('_form_wizard', [
                            'id' => $id,
                            'field_id' => $field_id,
                            'data' => $f_data
                        ])
            ]);
        }else {
            return $this->render('_form_wizard', [
                    'id' => $id,
                    'field_id' => $field_id,
                    'data' => $f_data
            ]);
        }
    }

    public function actionDeleteBusinessForm($id, $field_id) {
        $model = new TaxForm();
        $model->getSetting($id);
        if ($model->orders_tax_id) {
            if ($model->deleteForm($field_id) === true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return Json::encode(['success' => true, 'message' => Yii::t("site", "FLASH_SUCCESS")]);
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return Json::encode(['success' => false, 'message' => Yii::t("site", "FLASH_ERROR")]);
    }

    /**
     * Business Tax Application from customer.
     * @return mixed
     */
    public function actionBusinessList() {
        $this->setActiveMenu('Customer/Tax Request');

        $model = new TaxForm();
        $dataProvider = $model->searchApplication(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('business-list', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdateApplicationStatus($id = "", $status = "") {
        if (Yii::$app->request->isPost) {
            $post = Yii::$app->request->post();

            if (Yii::$app->request->post("id")) {
                $post["id"] = (int) Yii::$app->request->post("id");
            }

            if (Yii::$app->request->post("status")) {
                $post["status"] = (int) Yii::$app->request->post("status");
            }
        }

        $id = (isset($post["id"]) ? $post["id"] : $id);
        $status = (isset($post["status"]) ? $post["status"] : $status);

        $model = OrdersTaxCustomers::findOne($id);
        if ($post && $model->id) {
            $m = new TaxForm();
            $result = $m->processApplication($post);
            if ($result === true) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return Json::encode(['success' => true, 'message' => Yii::t("site", "FLASH_SUCCESS")]);
            } else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . ($result === false ? "" : "<br />" . $result));
                return Json::encode(['success' => false, 'message' => Yii::t("site", "FLASH_ERROR") . ($result === false ? "" : "<br />" . $result)]);
            }
        }

        return Json::encode([
                    'title' => "Business Tax Request : " . TaxForm::getApplicationStatus($status),
                    'body' => $this->renderPartial('_form_status', [
                        'model' => $model,
                        'status' => $status
                    ])
        ]);
    }

    protected function findRuleModel($id) {
        if (($model = OrdersTaxRuleMapping::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionRules($id) {
        $tax_profile = OrdersTaxConfiguration::findOne($id);

        $model = new OrdersTaxRulesForm();
        $dataProvider = $model->getTaxRules($id);
        $dataProvider->pagination->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);

        return $this->render('rule_list', [
            'model' => $model,
            'dataProvider' => $dataProvider,
            'orders_tax_id' => $id,
            'country_name'=>$tax_profile->country_name,
        ]);
    }

    public function actionUpdateRule($id) {

        $modelrules = new OrdersTaxRulesForm();
        $modelrules->getTaxRuleInfo($id);
        $modeldetail = $modelrules->tax_rule_items;
        $tax_profile = OrdersTaxConfiguration::findOne($modelrules->orders_tax_id);
        $tax_details_id = [];
        $validate = false;
        $post = Yii::$app->request->post();
        if ($post) {
            if(!empty($post['OrdersTaxRulesForm']))
            {
                $orders_tax_rules = $post['OrdersTaxRulesForm'];
            }
            if(!empty($post['OrdersTaxRuleDetailsForm']))
            {
                $orders_tax_rules_details = $post['OrdersTaxRuleDetailsForm'];
                foreach($orders_tax_rules_details as $item)
                {
                    $validate = true;
                    if(!isset($item['condition']) || !isset($item['condition']))
                    {
                        $validate = false;
                    }
                }
            }
            if($validate)
            {

                $modelrules->isNewRecord = false;
                $modelrules->description = $orders_tax_rules['description'];
                $modelrules->rule_type = $orders_tax_rules['rule_type'];
                $tax_rule_id = $modelrules->saveTaxRule();
                foreach($orders_tax_rules_details as $index => $item){
                    if(!empty($item['tax_rule_detail_id']))
                    {
                        $detail = new OrdersTaxRuleDetailsForm();
                        $detail->getTaxRuleItems($item['tax_rule_detail_id']);
                        $detail->fieldname = $item['fieldname'];
                        $detail->condition = $item['condition'];
                        $detail->values = $item['values'];
                        $detail->isNewRecord = false;
                        $detail->saveItem();
                        $tax_details_id[] = $item['tax_rule_detail_id'];
                    }else{
                        $detail = new OrdersTaxRuleDetailsForm();
                        $detail->tax_rule_id = $orders_tax_rules['tax_rule_id'];
                        $detail->fieldname = $item['fieldname'];
                        $detail->condition = $item['condition'];
                        $detail->values = $item['values'];
                        $detail->isNewRecord = true;
                        $detail->saveItem();
                    }
                }
                foreach($modeldetail as $item)
                {
                    if(!in_array($item->tax_rule_detail_id, $tax_details_id))
                    {
                        $detail = new OrdersTaxRuleDetailsForm();
                        $detail->tax_rule_detail_id = $item->tax_rule_detail_id;
                        $detail->removed = true;
                        $detail->removeItem();
                    }
                }
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['rules', 'id' => $modelrules->orders_tax_id]);
            }else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
                return $this->redirect(Yii::$app->request->referrer ?: ['index']);
            }
        }
        return $this->render('_form_rules', ['model' => $modelrules, 'detail' => (empty($modeldetail)) ? [new OrdersTaxRuleDetailsForm()] : $modeldetail, 'country' => $tax_profile->country_name]);


    }

    public function actionCreateRule($id) {
        $modelrules = new OrdersTaxRulesForm();
        $modeldetail = [new OrdersTaxRuleDetailsForm()];
        $modelrules->orders_tax_id = $id;
        $tax_profile = OrdersTaxConfiguration::findOne($modelrules->orders_tax_id);
        $post = Yii::$app->request->post();
        if ($post) {
            $validate = false;
            if(!empty($post['OrdersTaxRulesForm']))
            {
                $orders_tax_rules = $post['OrdersTaxRulesForm'];
            }
            if(!empty($post['OrdersTaxRuleDetailsForm']))
            {
                $orders_tax_rules_details = $post['OrdersTaxRuleDetailsForm'];
                foreach($orders_tax_rules_details as $item)
                {
                    $validate = true;
                    if(!isset($item['condition']) || !isset($item['condition']))
                    {
                        $validate = false;
                    }
                }
            }
            if($validate)
            {
                $modelrules->isNewRecord = true;
                $modelrules->orders_tax_id = $orders_tax_rules['orders_tax_id'];
                $modelrules->description = $orders_tax_rules['description'];
                $modelrules->rule_type = $orders_tax_rules['rule_type'];
                $tax_rule_id = $modelrules->saveTaxRule();
                foreach($orders_tax_rules_details as $item){
                    $model = new OrdersTaxRuleDetailsForm();
                    $model->isNewRecord = true;
                    $model->tax_rule_id = $tax_rule_id;
                    $model->fieldname = $item['fieldname'];
                    $model->condition = $item['condition'];
                    $model->values = $item['values'];
                    $model->saveItem();
                }
                    Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                    return $this->redirect(['rules', 'id' => $modelrules->orders_tax_id]);
            }else {
                Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
                return $this->redirect(Yii::$app->request->referrer ?: ['index']);
            }
        }


        return $this->render('_form_rules', ['model' => $modelrules, 'detail' => (empty($modeldetail)) ? [new OrdersTaxRuleDetailsForm()] : $modeldetail,'country' => $tax_profile->country_name]);
    }

    public function actionDisableRule($id) {
        $model = new OrdersTaxRulesForm();
        if($model->disableTaxRule($id))
        {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }
        return $this->redirect(Yii::$app->request->referrer ?: ['index']);
    }
    public function actionEnableRule($id) {
        $model = new OrdersTaxRulesForm();
        if($model->enableTaxRule($id))
        {
        Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
    }
        return $this->redirect(Yii::$app->request->referrer ?: ['index']);
    }

}
