<?php

namespace backend\controllers\admin;

use backend\controllers\BaseController;
use backend\models\admin\form\EmailVerifyForm;
use yii\helpers\HtmlPurifier;
use Yii;

class BlockedEmailController extends BaseController
{

    public function actionIndex()
    {
        $post = Yii::$app->request->post();
        $get = Yii::$app->request->get();
        $model = new EmailVerifyForm();
        $invalid_characters = ['"', "'", "(", ")", "{", "}"];
        if ($post) {
            $email = str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process($post['customer_email_address'])));
            $data = $model->getCustomerByEmail([$email]);
        } else if ($get) {
            $email = str_replace($invalid_characters, '', strip_tags(HtmlPurifier::process($get['email'])));
            $data = $model->getCustomerByEmail([$email]);
            $post['customer_email_address'] = $email;
        }

        return $this->render('index', [
            'model' => $model,
            'post'  => $post,
        ]);

    }

    public function actionUpdateEmailStatus()
    {
        $model = new EmailVerifyForm();
        $post  = Yii::$app->request->post();

        if ($post) {
            $email = isset($post['email']) ? $post['email'] : '';
            $data = $model->getCustomerByEmail($email);
            if ($model->updateValidationStatus($post)) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index', 'email' => $email]);
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return $this->redirect(['index', 'email' => $email]);
    }

    public function actionUpdateEmailBounce()
    {
        $model = new EmailVerifyForm();
        $post = Yii::$app->request->post();

        if ($post) {
            $email = isset($post['email']) ? $post['email'] : '';
            $data = $model->getCustomerByEmail($email);
            $update = $model->updateEmailBlockStatus($post);
            if ($update) {
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                return $this->redirect(['index', 'email' => $email]);
            }
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return $this->redirect(['index', 'email' => $email]);
    }

}
