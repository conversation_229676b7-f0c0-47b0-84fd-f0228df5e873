<?php

namespace backend\controllers\admin;

use Yii;
use offgamers\base\components\AWS;
use backend\controllers\BaseController;
use yii\web\HttpException;

class SystemController extends BaseController {

    public $menu = 'Administrator/Cache';

    public function actionService() {
        return $this->render('//system/index');
    }

    public function actionClearMemcache() {
        $cache = Yii::$app->get('cache', false);
        $cache->flush();
        return true;
    }

    public function actionClearCfCache($distribution_id) {
        $distribution_list = Yii::$app->params['aws.cf.list'];
        foreach ($distribution_list as $distribution) {
            if ($distribution['distribution_id'] == $distribution_id) {
                if (empty($distribution['aws.key']) || empty($distribution['aws.secret']) || empty($distribution['aws.region']) || empty($distribution['aws.version'])) {
                    $aws = Yii::$app->aws;
                } else {
                    $aws = new AWS([
                        'key' => $distribution['aws.key'],
                        'secret' => $distribution['aws.secret'],
                        'region' => $distribution['aws.region'],
                        'version' => $distribution['aws.version'],
                    ]);
                }
                if (!$aws->getCF()->invalidateCache($distribution_id, ['/*'])) {
                    throw new HttpException('Fail to clear CF Cache');
                } else {
                    return true;
                }
                break;
            }
        }
        throw new HttpException('Fail to clear CF Cache');
    }

}
