<?php

namespace backend\controllers\admin;

use Yii;
use mdm\admin\models\Assignment;
use backend\models\admin\search\Assignment as AssignmentSearch;

/**
 * AssignmentController implements the CRUD actions for Assignment model.
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class UserController extends \mdm\admin\controllers\AssignmentController {

    public function actionIndex() {
        if ($this->searchClass === null) {
            $searchModel = new AssignmentSearch;
            $dataProvider = $searchModel->search(Yii::$app->getRequest()->getQueryParams(), $this->userClassName, $this->usernameField, $this->getChildRoles());
        } else {
            $class = $this->searchClass;
            $searchModel = new $class;
            $dataProvider = $searchModel->search(Yii::$app->getRequest()->getQueryParams());
        }

        $this->extraColumns[] = 'roles';

        $this->extraColumns[] = [
            'label' => 'Status',
            'filter' => \yii\helpers\Html::activeDropDownList($searchModel, 'status', array('10' => 'Active', '0' => 'Inactive'), ['prompt' => '', 'class' => 'form-control']),
            'content' => function ($model) {
                $status = array('0' => 'Inactive', '10' => 'Active');
                return $status[$model->status];
            }
        ];

        return $this->render('index', [
                    'dataProvider' => $dataProvider,
                    'searchModel' => $searchModel,
                    'idField' => $this->idField,
                    'usernameField' => $this->usernameField,
                    'extraColumns' => $this->extraColumns,
                    'searchClass' => $this->getChildRoles()
        ]);
    }

    public function actionUpdate($id = "") {
        Yii::$app->view->params['activeMenu'] = 'Administration/Admin/User';
        
        $model = (new \backend\models\admin\form\UserForm);
        $model->scenario = "create";

        if ($id) {
            $model->scenario = "update";
            $model->search($id);
        }

        if (Yii::$app->request->isPost && $model->load(Yii::$app->request->post()) && $model->validate()) {
            $model->update();
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));

            if ($model->scenario == "create") {
                $m_user = \common\models\User::findOne(['email' => $model->email]);
                if ($m_user->status) {
                    return $this->redirect(['/admin/assignment/view', 'id' => $m_user->id]);
                }
            }
            return $this->redirect(['/admin/user/index']);
        }

        return $this->render('user', ['model' => $model]);
    }

    public function actionView($id) {
        // get all available roles/permissions
        $model = $this->findModel($id);
        $assignmentArray = $this->filterItems($model->getItems());

        return $this->render('view', [
                    'model' => $model,
                    'assignmentArray' => $assignmentArray,
                    'idField' => $this->idField,
                    'usernameField' => $this->usernameField,
                    'fullnameField' => $this->fullnameField,
        ]);
    }

    public function actionDelete($id) {
        $model = \common\models\User::findOne($id);

        if ($model && $model->delete()) {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }

        return $this->redirect(['index']);
    }

    /**
     * Assign items
     * @param string $id
     * @return array
     */
    public function actionAssign($id) {
        $items = Yii::$app->getRequest()->post('items', []);
        $model = new Assignment($id);
        $success = $model->assign($items);
        Yii::$app->getResponse()->format = 'json';
        return array_merge($this->filterItems($model->getItems()), ['success' => $success]);
    }

    /**
     * Assign items
     * @param string $id
     * @return array
     */
    public function actionRevoke($id) {
        $items = Yii::$app->getRequest()->post('items', []);
        $model = new Assignment($id);
        $success = $model->revoke($items);
        Yii::$app->getResponse()->format = 'json';
        return array_merge($this->filterItems($model->getItems()), ['success' => $success]);
    }

    // filter assignments list
    protected function filterItems($unFilterItems) {
        if (!Yii::$app->authManager->getAssignment('superadmin', Yii::$app->user->id)) {
            if (($keys = array_keys($unFilterItems['available'], 'permission'))) {
                foreach ($keys as $key) {
                    unset($unFilterItems['available'][$key]);
                }
            }
            $unFilterItems['available'] = array_intersect_key($unFilterItems['available'], array_flip($this->getChildRoles()));
        }

        return $unFilterItems;
    }

    // get child roles for login user
    protected function getChildRoles() {
        $childRoles = array();
        $roles = Yii::$app->authManager->getRolesByUser(Yii::$app->user->id);
        foreach ($roles as $key => $role) {
            $childs = Yii::$app->authManager->getChildren($key);
            foreach ($childs as $childKey => $child) {
                if ($child->type == 1) {
                    $childRoles[] = $childKey;
                }
            }
        }

        return $childRoles;
    }

}
