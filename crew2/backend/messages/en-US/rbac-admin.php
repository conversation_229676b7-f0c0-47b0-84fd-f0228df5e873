<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Activate' => '',
    'Are you sure you want to activate this user?' => '',
    'Assign' => '',
    'Assignment' => '',
    'Assignments' => '',
    'Change' => '',
    'Change Password' => '',
    'Create' => '',
    'Delete' => '',
    'Email will be use as username during login.' => '',
    'For new user you can' => '',
    'ID' => '',
    'If you forgot your password you can' => '',
    'Login' => '',
    'Name' => '',
    'Please choose your new password:' => '',
    'Please fill out the following fields to change password:' => '',
    'Please fill out the following fields to login:' => '',
    'Please fill out the following fields to signup:' => '',
    'Please fill out your email. A link to reset password will be sent there.' => '',
    'Remove' => '',
    'Request password reset' => '',
    'Reset password' => '',
    'Save' => '',
    'Search for assigned' => '',
    'Search for available' => '',
    'Send' => '',
    'Signup' => '',
    'Update' => '',
    'Username' => '',
    'Users' => '',
    'reset it' => '',
    'signup' => '',
];
