<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Code' => '',
    'Create' => '',
    'Create Topic' => '',
    'Create thi' => '',
    'Created By' => '',
    'Created Date' => '',
    'Delete' => '',
    'Description' => '',
    'First' => '',
    'Last' => '',
    'Manual Created' => '',
    'Page Size' => '',
    'System Created' => '',
    'Title' => '',
    'Token Count' => '',
    'Topic' => '',
    'Topic ID' => '',
    'Topic cannot be empty' => '',
    'Type' => '',
    'Update' => '',
    'Update Topic' => '',
    'Updated Date' => '',
];
