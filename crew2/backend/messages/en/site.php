<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    "FLASH_SUCCESS" => "<strong>Success</strong><br />Congrats, it works!",
    "FLASH_WARNING" => "<strong>Attention</strong><br />Your request could not be processed at this time.",
    "FLASH_WARNING_PERMISSION_DENIED" => "<strong>Attention</strong><br />Your request could not be processed at this time : Permission Denied.",
    "FLASH_ERROR" => "<strong>Oops!</strong><br />Unexpected error, please contact technicians and tell them what happened.",
    "FLASH_ERROR_OG" => "<strong>Oops!</strong><br /><ul>%s</ul>",
];
