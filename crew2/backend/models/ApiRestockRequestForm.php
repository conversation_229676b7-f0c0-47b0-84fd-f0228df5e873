<?php

namespace backend\models;

use common\models\Products;
use common\models\ApiRestockRequest;
use common\models\ProductsDescription;
use Yii;
use yii\base\Model;
use yii\data\ArrayDataProvider;

class ApiRestockRequestForm extends Model
{

    public $order_id, $product_id, $status;
    public $request_id, $product_info, $api_restock_info;
    public $list = [];
    public $pageSize = 10, $page = 1, $resultCount = 1, $startAt = 1, $endAt = 10;
    public $isNewRecord, $errors = false;

    public function __construct($requestId = '')
    {
        if (isset($requestId) && !empty($requestId)) {
            $this->getApiRestockInfo($requestId);
        }
    }

    public function rules()
    {
        return [
            [['order_id', 'status', 'product_id'], 'integer'],
            [['order_id', 'product_id', 'status'], 'validateSearch', 'skipOnEmpty' => false, 'skipOnError' => false, 'on' => 'search_filt'],
        ];
    }

    public function getApiRestockInfo($requestId)
    {
        $this->api_restock_info = ApiRestockRequest::findOne(['api_restock_request_id' => $requestId]);
        $this->product_info = ProductsDescription::find()->select('products_name')->where(['products_id' => $this->api_restock_info->products_id,'language_id' => 1])->one();
    }

    public function validateSearch()
    {
        $_GET['order_id']     = $this->order_id;
        $_GET['product_id']   = $this->product_id;
        $_GET['status']       = $this->status;

        return true;
    }

    public function search()
    {
        $query = ApiRestockRequest::find()->alias('c');
        $query->select([
            'c.api_restock_request_id',
            'c.orders_id',
            'c.products_id',
            'c.publisher_order_id',
            'c.status',
            'c.created_at',
            'c.updated_at',
            'p.products_name',
           ]);
        $query->orderBy("c.api_restock_request_id ASC");
        $query->joinWith("productsDescription p", "p.products_id = c.products_id AND p.language_id = 1");
      
        // filter by order_id
        if ($this->order_id) {
            $query->where(['c.orders_id' => $this->order_id]);
        }

        // filter by product_id
        if ($this->product_id) {
            $query->andWhere(['c.products_id' => $this->product_id]);
        }

        // filter by status == 0
        if ($this->status == '0') {
            $query->andWhere(['c.status' => '0']);
        } 

        // filter by status
        if ($this->status) {
            $query->andWhere(['c.status' => $this->status]);
        } 

        return new ArrayDataProvider([
            'allModels'  => $query->asArray()->all(),
            'pagination' => [
                'pageSize' => $this->pageSize,
            ],
        ]);
    }

   
 

}
