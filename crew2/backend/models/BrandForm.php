<?php

namespace backend\models;

use common\components\image\ImageOptimizerBase;
use common\models\ApiFormModel;
use common\models\Categories;
use common\models\Countries;
use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;


class BrandForm extends ApiFormModel
{
    private const BRAND_STATUSES = [
        0 => 'Inactive',
        1 => 'Active',
    ];

    private const ACTION_CREATE = 'create';
    private const ACTION_UPDATE = 'update';
    private const VALID_ACTIONS = [
        self::ACTION_CREATE,
        self::ACTION_UPDATE
    ];

    private array $data = [];

    private array $return_msg = [];

    public $brand_id;
    public $name_search;
    public $image;
    public $seo_url;
    public $show_in_search_result;
    public $search_keyword;
    public $status;
    public $sort_order;
    public $image_base64;
    public $image_extension;

    /**
     * {language_id} => [
     *   'brand_description_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'name' => 'test',
     *   'notice' => 'test',
     *   'description' => 'test',
     * ]
     */
    public $brand_descriptions = [];
    /**
     * {language_id} => [
     *   'brand_metadata_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'meta_title' => 'test',
     *   'meta_description' => 'test',
     *   'meta_keyword' => 'test',
     * ]
     */
    public $brand_metadatas = [];
    /**
     * Update for publisher, developer, release_date, game_region,
     * game_language, game_platform, game_genre, releated links
     * {key} => [
     *   'brand_game_info_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'key' => 'test',
     *   'value' => 'test',
     * ]
     */
    public $brand_game_infos = [];
    /**
     * {index} => [
     *   'brand_game_info_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'sub_id' => 1,
     *   'type' => 1 | 2,
     * ]
     */
    public $brand_categories = [];
    /**
     * {index} => [
     *   'brand_soft_block_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'country_code' => MY,
     * ]
     */
    public $brand_soft_blocks = [];
    /**
     * {index} => [
     *   'brand_hard_block_id' => 1, {not required for create}
     *   'brand_id' => 1, {not required for create}
     *   'country_code' => MY,
     * ]
     */
    public $brand_hard_blocks = [];
    /**
     * {index} => [1,2,3]
     */
    public $brand_categories_id_delete_bucket = [];
    public $related_links_id_delete_bucket = [];
    public $related_links_form = [];
    public $brand_categories_form = [];
    public $brands_id_bucket = [];
    public $categories_id_bucket = [];
    public $temp_file_image;

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'brand';
        $this->method = 'get';
        $this->searchField = ['name_search', 'status'];
        parent::__construct();
    }

    /**
     * @inheritDoc
     */
    public function rules()
    {
        return [
            [['image'],
                'file',
                'skipOnEmpty' => false,
                'mimeTypes' => 'image/*',
                'maxSize' => (int)(str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024),
                'message' => 'Please upload an image'],
            [['sort_order', 'show_in_search_result', 'status'], 'integer'],
            [['seo_url'], 'required'],
            [['seo_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @throws HttpException
     * @throws Exception
     */
    public function get(int $brandId)
    {
        $this->action = 'view';

        $params = [
            'brand_id' => $brandId,
        ];

        $response = $this->request($params);

        if ($this->returnNotificationResult($response)) {
            return $response;
        }

        throw new Exception("Error getting brand : $brandId");
    }

    public function create(): bool
    {
        $this->action = 'create';

        try {
            $response = $this->getCreateOrUpdateResponse();

            if (!$response) {
                array_push($this->return_msg, "Failed to create Brand.");
            } else {
                Yii::$app
                    ->cache
                    ->delete('brands/brand_category');
            }

            return $response;
        } catch (Exception $exception) {
            array_push($this->return_msg, sprintf("Error creating brand due to : %s", $exception->getMessage()));
            return false;
        }
    }

    public function update(): bool
    {
        $this->action = 'update';

        try {
            $response = $this->getCreateOrUpdateResponse();

            if (!$response) {
                array_push($this->return_msg, "Failed to update Brand.");
            } else {
                $this->removeBrandCategoriesAndBrandCache($this->brand_id);
            }

            return $response;
        } catch (Exception $exception) {
            array_push($this->return_msg, sprintf("Unable to update brand due to : %s", $exception->getMessage()));
            return false;
        }
    }

    public function deleteById(int $brandId): bool
    {
        $this->action = 'delete';

        try {
            $params = [
                'brand_id' => $brandId,
            ];

            $returnNotificationResult = $this->returnNotificationResult(
                $this->request($params),
                true
            );

            if ($returnNotificationResult) {
                $this->removeBrandCategoriesAndBrandCache($brandId);
            } else {
                Yii::$app->session->setFlash(
                    "error",
                    "Failed to delete Brand Id : $brandId."
                );
            }

            return $returnNotificationResult;
        } catch (Exception $exception) {
            Yii::$app->session->setFlash(
                "error",
                sprintf(
                    "Error deleting brand due to : %s",
                    $exception->getMessage()
                )
            );
        }

        return false;
    }

    /**
     * @throws Exception
     */
    public function checkSeoUrlExists(string $seoUrl, $brandId): bool
    {
        $seoUrl = trim($seoUrl);

        if (!empty($brandId)) {
            $brand = $this->get($brandId);
            if (!empty($brand['seo_url']) && (strcasecmp($brand['seo_url'], $seoUrl) === 0)) {
                return false;
            }
        }

        $this->action = 'check-seo-url-exists';

        $params = [
            'seo_url' => $seoUrl,
        ];

        return $this->returnNotificationResult($this->request($params));
    }

    /**
     * @inheritDoc
     */
    public function afterValidate()
    {
        // If update and image is not updated, bypass this error
        if (!empty($this->temp_file_image) && !empty($this->getErrors('image'))) {
            $this->clearErrors('image');
        }

        parent::afterValidate();
    }

    public function initForUpdate(array $data): void
    {
        $this->setAttributes($data, false);

        $this->brand_soft_blocks = [];
        $this->brand_hard_blocks = [];

        $idCountriesCode = array_flip(Countries::countriesCodeID());

        if (!empty($data['brand_soft_blocks'])) {
            foreach ($data['brand_soft_blocks'] as $brandSoftBlocks) {
                $this->brand_soft_blocks['value'][] = $idCountriesCode[$brandSoftBlocks['country_code']];
            }
        }

        if (!empty($data['brand_hard_blocks'])) {
            foreach ($data['brand_hard_blocks'] as $brandHardBlocks) {
                $this->brand_hard_blocks['value'][] = $idCountriesCode[$brandHardBlocks['country_code']];
            }
        }

        $this->brand_descriptions = [];
        if (!empty($data['brand_descriptions'])) {
            foreach ($data['brand_descriptions'] as $brandDescription) {
                $this->brand_descriptions[$brandDescription['language_id']] = $brandDescription;
            }
        }

        $this->brand_metadatas = [];
        if (!empty($data['brand_metadatas'])) {
            foreach ($data['brand_metadatas'] as $brandMetadata) {
                $this->brand_metadatas[$brandMetadata['language_id']] = $brandMetadata;
            }
        }

        $gameRegionIds = $this->getGameInfoIds('game_region');
        $gameLanguageIds = $this->getGameInfoIds('game_language');
        $gamePlatformIds = $this->getGameInfoIds('game_platform');
        $gameGenreIds = $this->getGameInfoIds('game_genre');

        foreach ($this->brand_game_infos as $index => $gameInfo) {
            if (
                $gameInfo['key'] == 'game_region'
                || $gameInfo['key'] == 'game_language'
                || $gameInfo['key'] == 'game_platform'
                || $gameInfo['key'] == 'game_genre'
            ) {
                unset($this->brand_game_infos[$index]);
                continue;
            }

            if ($gameInfo['key'] == 'related_link') {
                $decode = Json::decode($gameInfo['value']);
                $decode['value'] = json_encode($decode, JSON_UNESCAPED_SLASHES);
                $decode['brand_game_info_id'] = $gameInfo['brand_game_info_id'];
                $this->related_links_form[] = $decode;
            }

            $this->brand_game_infos[$gameInfo['key']] = $this->brand_game_infos[$index];
            unset($this->brand_game_infos[$index]);
        }

        $this->brand_game_infos['game_region']['value'] = $gameRegionIds;
        $this->brand_game_infos['game_language']['value'] = $gameLanguageIds;
        $this->brand_game_infos['game_platform']['value'] = $gamePlatformIds;
        $this->brand_game_infos['game_genre']['value'] = $gameGenreIds;

        $brands = BrandSearch::getBrandsForBrandCategory();
        $categories = Categories::getSecondLayerCategoryList();

        foreach ($this->brand_categories as $index => $brandCategory) {
            $subId = $this->brand_categories[$index]['sub_id'];

            if ($brandCategory['type'] == 1) {
                $brand = $brands[$subId];

                $this->brand_categories[$index]['name'] = $brand['name'];
                $this->brand_categories[$index]['status'] = $brand['status'];
                $this->brands_id_bucket[] = $subId;
            }

            if ($brandCategory['type'] == 2) {
                if(!isset($categories[$subId])){
                    unset($this->brand_categories[$index]);
                    continue;
                }

                $category = $categories[$subId];

                $this->brand_categories[$index]['name'] = $category['categories_name'];
                $this->brand_categories[$index]['status'] = $category['status'];
                $this->categories_id_bucket[] = $subId;
            }
        }

        $this->temp_file_image = $data['image_url'] ?? null;
        $this->image = $data['image_url'] ?? null;

        $this->clearErrors();
    }

    public static function getBrandById(int $brandId): string
    {
        return Yii::$app
            ->cache
            ->getOrSet(
                "brands/brand/$brandId",
                function () use ($brandId) {
                    $model = new self();
                    $brand = $model->get($brandId);
                    $brand['type'] = 1;
                    return Json::encode($brand);
                }
            );
    }

    public static function getBrandStatuses(): array
    {
        return self::BRAND_STATUSES;
    }

    /**
     * @throws HttpException
     * @throws Exception
     */
    private function getCreateOrUpdateResponse(): bool
    {
        if (empty($this->temp_file_image)) {
            $image = $this->compressAndGetImage();

            $this->image_base64 = base64_encode($image);
            $this->image_extension = $this->image->extension;
        }

        $this->constructRequest($this->toArray());

        $temp_msg = $this->request($this->data);

        if (($temp_msg["status"] !== 201)) {
            array_push($this->return_msg, $temp_msg["message"]);
        }

        return $this->returnNotificationResult($temp_msg, true);
    }

    /**
     * @throws Exception
     */
    private function returnNotificationResult(array $arrayResult, bool $showErrorFlashMessage = false): bool
    {
        if ($this->action == 'view' && in_array(intval($arrayResult['status']), array_keys(self::getBrandStatuses()))) {
            return true;
        }

        if (empty($arrayResult) || !isset($arrayResult['status'])) {
            array_push($this->return_msg, "Failed to create Brand due to invalid response from ms-product.");
            return false;
        }

        $isStatusCodeOK = $arrayResult['status'] == 200
            || $arrayResult['status'] == 201
            || $arrayResult['status'] == 204;

        if (!$isStatusCodeOK && $showErrorFlashMessage) {
            $message = $arrayResult['message'] ?? "issues on the ms-product.";
            array_push($this->return_msg, "Failed to create Brand due to " . $message);
        }

        return $isStatusCodeOK;
    }

    /**
     * @return false|string
     * @throws Exception
     */
    private function compressAndGetImage()
    {
        $filename = $this->image->name;
        $outputPath = rtrim(sys_get_temp_dir(), '/') . '/' . $filename;

        /**
         * @var ImageOptimizerBase $imageOptimizer
         */
        $imageOptimizer = Yii::$app->imageOptimizer->getClient();
        $imageCompressionRatio = Yii::$app->params['brand.image.compression.ratio'];

        if (in_array($this->image->type, $imageOptimizer->getSupportedMime()) && $imageCompressionRatio > -1) {
            $imageOptimizer->setCompressionRatio($imageCompressionRatio);
            $imageOptimizer->compressImageFromFile(
                $this->image->tempName,
                rtrim(
                    sys_get_temp_dir(),
                    '/'
                ),
                $filename
            );
        } else {
            move_uploaded_file($this->image->tempName, $outputPath);
        }

        if (!file_exists($outputPath)) {

            throw new Exception(
                "Image : [$filename] unable to be uploaded. Please select other image and try again."
            );
        }

        $image = file_get_contents($outputPath);
        unlink($outputPath);

        return $image;
    }

    /**
     * @throws Exception
     */
    private function constructRequest(array $params)
    {
        if (!in_array($this->action, self::VALID_ACTIONS)) {
            throw new Exception("Invalid action given : [$this->action]");
        }

        if ($this->action == self::ACTION_UPDATE && empty($params['brand_id'])) {
            throw new Exception("Missing brand id for update operation");
        }

        if (!empty($params['brand_id'])) {
            $this->data['brand_id'] = $this->brand_id;
        }

        $this->initBrand($params);
        $this->initBrandDescriptions($params);
        $this->initBrandMetadatas($params);
        $this->initBrandGameInfos($params);
        $this->initBrandCategories($params);
        $this->initBrandCountryBlocks($params, 'brand_soft_blocks');
        $this->initBrandCountryBlocks($params, 'brand_hard_blocks');

        if (!empty($params['related_links_id_delete_bucket'])) {
            $this->data['related_links_id_delete_bucket'] = $params['related_links_id_delete_bucket'];
        }

        if (!empty($params['brand_categories_id_delete_bucket'])) {
            $this->data['brand_categories_id_delete_bucket'] = $params['brand_categories_id_delete_bucket'];
        }

        unset($params);
    }

    private function initBrand(array $params): void
    {
        if (!isset($params['show_in_search_result'])) {
            throw new Exception("Missing Show in search result status.");
        }

        if (!isset($params['status'])) {
            throw new Exception("Missing status");
        }

        if (!isset($params['seo_url']) && $params['seo_url']) {
            throw new Exception("Missing SEO URL");
        }

        if (
            ($this->action == self::ACTION_UPDATE && empty($params['temp_file_image']))
            || $this->action == self::ACTION_CREATE
        ) {
            if (empty($params['image_base64'])) {
                throw new Exception("Missing Image base64");
            }

            if (empty($params['image_extension'])) {
                throw new Exception("Missing Image Extension");
            }
        }

        if ($this->action == self::ACTION_UPDATE && !empty($params['temp_file_image'])) {
            $this->data['temp_file_image'] = $params['temp_file_image'];
        }

        $this->data['show_in_search_result'] = intval($params['show_in_search_result']);
        $this->data['status'] = intval($params['status']);
        $this->data['image_base64'] = trim($params['image_base64']);
        $this->data['image_extension'] = trim($params['image_extension']);
        $this->data['search_keyword'] = trim($params['search_keyword']);
        $this->data['seo_url'] = trim($params['seo_url']);

        if (!empty($params['sort_order'])) {
            $this->data['sort_order'] = trim($params['sort_order']);
        }

        if (isset($params['short_description'])) {
            $this->data['short_description'] = trim($params['short_description']);
        }
    }

    private function initBrandDescriptions(array $params): void
    {
        $brandDescriptions = [];

        if (!isset($params['brand_descriptions'])) {
            throw new Exception("Brand descriptions must be set!");
        }

        if (!isset($params['brand_descriptions'][1]['name'])) {
            throw new Exception("Brand NAME for English must be set!");
        }

        foreach ($params['brand_descriptions'] as $languageId => $brandDescription) {
            if (!empty($brandDescription['brand_description_id'])) {
                $brandDescriptions[$languageId]['brand_description_id'] = $brandDescription['brand_description_id'];
            }

            $brandDescriptions[$languageId]['name'] = isset($brandDescription['name'])
                ? trim($brandDescription['name']) : "";
            $brandDescriptions[$languageId]['notice'] = isset($brandDescription['notice'])
                ? trim($brandDescription['notice']) : "";
            $brandDescriptions[$languageId]['short_description'] = isset($brandDescription['short_description'])
                ? trim($brandDescription['short_description']) : "";
            $brandDescriptions[$languageId]['description'] = isset($brandDescription['description'])
                ? trim($brandDescription['description']) : "";

            if (
                isset($brandDescriptions[$languageId]['notice'])
                || isset($brandDescriptions[$languageId]['name'])
                || isset($brandDescriptions[$languageId]['description'])
            ) {
                $brandDescriptions[$languageId]['language_id'] = $languageId;

                if (!empty($this->brand_id)) {
                    $brandDescriptions[$languageId]['brand_id'] = $params['brand_id'];
                }
            }
        }

        $this->data['brand_descriptions'] = array_values($brandDescriptions);
    }

    private function initBrandMetadatas(array $params): void
    {
        $brandMetadatas = [];

        if (!isset($params['brand_metadatas'])) {
            return;
        }

        foreach ($params['brand_metadatas'] as $languageId => $brandMetadata) {
            if (isset($brandMetadata['brand_metadata_id']) && $brandMetadata['brand_metadata_id']) {
                $brandMetadatas[$languageId]['brand_metadata_id'] = $brandMetadata['brand_metadata_id'];
            }

            $is_new_record = isset($brandMetadata['brand_metadata_id']);

            if (isset($brandMetadata['meta_title']) && ($is_new_record || $brandMetadata['meta_title'])) {
                $brandMetadatas[$languageId]['meta_title'] = trim($brandMetadata['meta_title']);
            }

            if (isset($brandMetadata['meta_keyword']) && ($is_new_record || $brandMetadata['meta_keyword'])) {
                $brandMetadatas[$languageId]['meta_keyword'] = trim($brandMetadata['meta_keyword']);
            }

            if (isset($brandMetadata['meta_description']) && ($is_new_record || $brandMetadata['meta_description'])) {
                $brandMetadatas[$languageId]['meta_description'] = trim($brandMetadata['meta_description']);
            }

            if (
                isset($brandMetadatas[$languageId]['meta_title'])
                || isset($brandMetadatas[$languageId]['meta_keyword'])
                || isset($brandMetadatas[$languageId]['meta_description'])
                || isset($brandMetadatas[$languageId]['brand_metadata_id'])
            ) {
                $brandMetadatas[$languageId]['language_id'] = $languageId;

                if (!empty($this->brand_id)) {
                    $brandMetadatas[$languageId]['brand_id'] = $params['brand_id'];
                }
            }
        }

        if (!empty($brandMetadatas)) {
            $this->data['brand_metadatas'] = array_values($brandMetadatas);
        }
    }

    /**
     * @throws Exception
     */
    private function initBrandGameInfos(array $params): void
    {
        $brandGameInfos = [];

        if (!isset($params['brand_game_infos'])) {
            return;
        }

        if (isset($params['brand_game_infos']['publisher']['value'])) {
            $brandGameInfos[] = $this->collectBrandGameInfos(
                'publisher',
                $params['brand_game_infos']['publisher']
            );
        }

        if (isset($params['brand_game_infos']['developer']['value'])) {
            $brandGameInfos[] = $this->collectBrandGameInfos(
                'developer',
                $params['brand_game_infos']['developer']
            );
        }

        if (isset($params['brand_game_infos']['release_date']['value'])) {
            $brandGameInfos[] = $this->collectBrandGameInfos(
                'release_date',
                $params['brand_game_infos']['release_date']
            );
        }

        if (isset($params['brand_game_infos']['game_region'])) {
            $gameRegions = $params['brand_game_infos']['game_region'];
            $this->loadGameInfos($gameRegions, 'game_region', $brandGameInfos);
        }

        if (isset($params['brand_game_infos']['game_language'])) {
            $gameLanguages = $params['brand_game_infos']['game_language'];
            $this->loadGameInfos($gameLanguages, 'game_language', $brandGameInfos);
        }

        if (isset($params['brand_game_infos']['game_platform'])) {
            $gamePlatforms = $params['brand_game_infos']['game_platform'];
            $this->loadGameInfos($gamePlatforms, 'game_platform', $brandGameInfos);
        }

        if (isset($params['brand_game_infos']['game_genre'])) {
            $gameGenres = $params['brand_game_infos']['game_genre'];
            $this->loadGameInfos($gameGenres, 'game_genre', $brandGameInfos);
        }

        if (isset($params['related_links_form'])) {
            foreach ($params['related_links_form'] as $relatedLink) {
                $value = $relatedLink['value'];

                if (empty($value)) {
                    throw new Exception('Related links value cannot be empty');
                }

                $relatedLink['value'] = Json::decode($value);

                $brandGameInfos[] = $this->collectBrandGameInfos('related_link', $relatedLink);
            }
        }

        if (!empty($brandGameInfos)) {
            $this->data['brand_game_infos'] = $brandGameInfos;
        }
    }

    private function initBrandCategories(array $params): void
    {
        $brandCategories = [];

        if (isset($params['brand_categories_form'])) {
            foreach ($params['brand_categories_form'] as $brandCategory) {
                $brandCategoryDecoded = json_decode($brandCategory, true);

                if (!isset($brandCategoryDecoded['sub_id']) || !isset($brandCategoryDecoded['type'])) {
                    throw new Exception("sub_id and type needed for Brand Category.");
                }

                $brandCategory = [
                    'type' => $brandCategoryDecoded['type'],
                    'sub_id' => $brandCategoryDecoded['sub_id']
                ];

                if (!empty($params['brand_id'])) {
                    $brandCategory['brand_id'] = $params['brand_id'];
                }

                $brandCategories[] = $brandCategory;
            }
        }

        if (!empty($brandCategories)) {
            $this->data['brand_categories'] = $brandCategories;
        }
    }

    private function initBrandCountryBlocks(array $params, $keyName): void
    {
        $brandCountryBlocks = [];
        $countriesCodeId = Countries::countriesCodeID();

        if (!empty($params[$keyName]['value'])) {
            foreach ($params[$keyName]['value'] as $brandBlock) {
                $brandCountryBlock = [
                    'country_code' => $countriesCodeId[$brandBlock]
                ];

                if (!empty($this->brand_id)) {
                    $brandCountryBlock['brand_id'] = $this->brand_id;
                }

                $brandCountryBlocks[] = $brandCountryBlock;
            }
        }

        $this->data[$keyName] = $brandCountryBlocks;
    }

    private function loadGameInfos(array $params, string $key, &$brandGameInfos): void
    {
        foreach ($params['value'] as $value) {
            $brandGameInfos[] = $this->getBrandGameInfoArray($key, $value);
        }
    }

    private function collectBrandGameInfos(string $key, array $valueArray): array
    {
        if (!isset($valueArray['value'])) {
            throw new Exception("Value must be set for [$key] : " . print_r($valueArray));
        }

        $brandGameInfoId = null;
        if (!empty($valueArray['brand_game_info_id'])) {
            $brandGameInfoId = $valueArray['brand_game_info_id'];
        }

        $value = $key == 'related_link' ? Json::encode($valueArray['value']) : $valueArray['value'];

        return $this->getBrandGameInfoArray($key, $value, $brandGameInfoId);
    }

    private function getBrandGameInfoArray(string $key, string $value, ?int $brandGameInfoId = null): array
    {
        $brandGameInfo = [];

        if (!empty($this->brand_id)) {
            $brandGameInfo['brand_id'] = $this->brand_id;
        }

        if (!empty($brandGameInfoId)) {
            $brandGameInfo['brand_game_info_id'] = $brandGameInfoId;
        }

        $brandGameInfo['key'] = $key;
        $brandGameInfo['value'] = $value;

        return $brandGameInfo;
    }

    private function getGameInfoIds(string $key): array
    {
        $ids = array_map(function (array $element) {
            return $element['value'];
        }, array_filter($this->brand_game_infos, function (array $element) use ($key) {
            return $element['key'] == $key;
        }));

        return array_values($ids);
    }

    private function removeBrandCategoriesAndBrandCache(int $brandId): void
    {
        Yii::$app
            ->cache
            ->delete('brands/brand_category');
        Yii::$app
            ->cache
            ->delete("brands/brand/$brandId");
    }

    public function getErrorMsg(): array
    {
        return $this->return_msg;
    }
}
