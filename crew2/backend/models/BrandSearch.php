<?php

namespace backend\models;

use backend\models\BrandForm;
use Yii;
use yii\db\Query;

class BrandSearch extends BrandForm
{
    /**
     * @inheritDoc
     */
    public function rules()
    {
        return [
            ['status', 'integer'],
            [['status', 'name_search'], 'safe'],
        ];
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'index';

        return parent::search($params, $key);
    }

    public static function getBrandsForBrandCategory(): array
    {
        $data = Yii::$app->cache->getOrSet('brands/brand_category', function () {
            $response = (new Query)
                ->select('b.brand_id, bd.name, b.status')
                ->from('brand b')
                ->leftJoin('brand_description bd', 'b.brand_id = bd.brand_id AND bd.language_id = 1')
                ->all(Yii::$app->db_og);

            $brands = [];
            foreach ($response as $brand) {
                $brands[$brand['brand_id']] = [
                    'brand_id' => $brand['brand_id'],
                    'name' => $brand['name'],
                    'type' => 1,
                    'status' => $brand['status'],
                ];
            }

            return $brands;
        });

        uasort($data, function ($a, $b) {
            $retval = $a['name'] <=> $b['name'];
            return $retval;
        });

        return $data;
    }
}
