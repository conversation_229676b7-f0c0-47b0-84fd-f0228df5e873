<?php

namespace backend\models;

use common\models\DeliverQueue;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\validators\EmailValidator;
use offgamers\base\components\Currency;
use offgamers\base\models\CurrenciesHistory;

class CampaignForm extends Model
{

    public $start_date, $end_date;
    public $currency = "USD";
    public $min_purchase_per_transaction = 0.01, $min_purchase = 0.01, $max_purchase = "", $amt_per_draw = 0.01;
    public $ogm_only = true;
    public $cat_id = [];
    public $payment_method = [];    // paypal 25, 354
    public $ip_country_id = [];
    public $exclude_customer_group = [1, 9];    // 1 Guest, 9 Employee
    public $recipient;
    public $use_start_date, $use_end_date;
    public $use_payment_method = [];
    private $s3;

    public function rules()
    {
        return [
            [['start_date', 'end_date', 'currency', 'min_purchase_per_transaction', 'min_purchase', 'amt_per_draw', 'recipient'], 'required'],
            ['end_date', 'compare', 'compareAttribute' => 'start_date', 'operator' => '>='],
            ['use_end_date', 'compare', 'compareAttribute' => 'use_start_date', 'operator' => '>='],
            [['min_purchase_per_transaction', 'min_purchase', 'max_purchase', 'amt_per_draw'], 'number', 'min' => 0.01],
            ['max_purchase', 'compare', 'compareAttribute' => 'min_purchase', 'operator' => '>=', 'message' => 'Max Purchase must be greater than Min Purchase'],
            [['use_start_date', 'use_end_date'], 'safe'],
            [['recipient'], 'string'],
            [['recipient'], 'validateMail'],
            ['use_start_date', 'required', 'when' => function($model)
                {
                    return (($model->use_end_date != '') || !empty($model->use_payment_method));
                },
                'whenClient' => "function (attribute, value) {
                    return (($('#use_end_date').val() != '') || ($(\"[name='use_payment_method[]_helper2']\" ).text() != ''));
                }"
            ],
            ['use_end_date', 'required', 'when' => function($model)
                {
                    return (($model->use_start_date != '') || !empty($model->use_payment_method));
                },
                'whenClient' => "function (attribute, value) {
                    return (($('#use_start_date').val() != '') || ($(\"[name='use_payment_method[]_helper2']\" ).text() != ''));
                }"
            ],
            ['use_payment_method', 'required', 'when' => function($model)
                {
                    return (($model->use_start_date != '') || ($model->use_end_date != ''));
                },
                'whenClient' => "function (attribute, value) {
                    return (($('#use_start_date').val() != '') || ($('#use_start_date').val() != ''));
                }"
            ],
        ];
    }

    public function attributeLabels()
    {
        return [
            'use_start_date' => 'Start Date',
            'use_end_date' => 'End Date',
            'use_payment_method' => 'Payment Method',
        ];
    }

    public function validateMail($attribute, $params)
    {
        $email = explode(",", $this->recipient);
        if ($email) {
            $err = [];
            $validator = new EmailValidator;

            foreach ($email as $val) {
                if (!$validator->validate(trim($val))) {
                    $err[] = $val;
                }
            }

            if ($err) {
                $this->addError($attribute, implode(", ", $err) . " is not a valid email.");
            }
        }
    }

    public function addDeliverQueue()
    {
        $extra = [
            'type' => 'campaign',
            'params' => [
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'currency' => $this->currency,
                'min_purchase_per_transaction' => $this->min_purchase_per_transaction,
                'min_purchase' => $this->min_purchase,
                'max_purchase' => $this->max_purchase,
                'amt_per_draw' => $this->amt_per_draw,
                'ogm_only' => $this->ogm_only,
                'cat_id' => $this->cat_id,
                'payment_method' => $this->payment_method,
                'ip_country_id' => $this->ip_country_id,
                'exclude_customer_group' => $this->exclude_customer_group,
                'use_start_date' => $this->use_start_date,
                'use_end_date' => $this->use_end_date,
                'use_payment_method' => $this->use_payment_method,
                'recipient' => $this->recipient
            ]
        ];

        $q = new DeliverQueue();
        $q->type = "REPORT";
        $q->id = 1;
        $q->extra_info = Json::encode($extra);
        $q->created_at = time();
        return $q->save();
    }

    public function generateReport()
    {
        $data = [];
        if (isset($this->recipient) && !empty($this->recipient)) {
            ini_set("memory_limit", "-1");
            set_time_limit(0);

            $cust_group = \common\models\CustomersGroups::getGroupList();

            $filename = "campaign/" . date("Ymd", strtotime($this->start_date)) . "_" . date("Ymd", strtotime($this->end_date)) . "_" . date("YmdHis") . ".csv";
            $header = [
                "Last Order Number",
                "Last Order Date & Time",
                "Customer ID",
                "Customer Group",
                "PayPal Email Address",
                "OffGamers Email Address",
                "Country of Residence",
                "Country of IP",
                "Order Number & Date Time of Purchase",
                "Product Purchased",
                "Total Amount Paid (" . $this->currency . ")",
                "First Time Purchase Date",
                "Use Specific Payment Method",
                "Chances",
                "Lucky Score",
                "Winning Score"
            ];
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, $header);

            $product = array();
            if (!empty($this->cat_id)) {
                foreach ($this->cat_id as $num => $val) {
                    $res = \common\models\Products::find()->select(['products_id'])
                                    ->where(['like', 'products_cat_id_path', '_' . $val . '_'])
                                    ->asArray()->all();
                    foreach ($res as $row) {
                        $product[] = $row["products_id"];
                    }
                }
            }

            $res = Yii::$app->db_slave_offgamers->createCommand("SELECT o.customers_id, o.customers_email_address,
                    o.orders_id, o.date_purchased, o.customers_email_address, o.currency,
                    api.info_value as payer_email, oei.orders_extra_info_value,
                    op.products_good_delivered_price, op.products_name, 
                    ctry.countries_name, ctry2.countries_name AS checkout_countries, c.customers_flag, c.customers_groups_id
                FROM orders AS o
                INNER JOIN orders_products AS op
                    ON o.orders_id = op.orders_id
                INNER JOIN orders_extra_info AS oei
                    ON oei.orders_id = o.orders_id
                    AND oei.orders_extra_info_key = 'site_id'
                LEFT JOIN orders_extra_info AS oei2
                    ON oei2.orders_id = o.orders_id
                    AND oei2.orders_extra_info_key = 'ip_country'
                LEFT JOIN analysis_pg_info AS api
                    ON o.orders_id = api.orders_id AND api.info_key = 'payer_email'
                INNER JOIN address_book AS ab
                    ON ab.customers_id = o.customers_id
                LEFT JOIN countries AS ctry
                    ON ctry.countries_id = ab.entry_country_id
                LEFT JOIN countries AS ctry2
                    ON ctry2.countries_id = oei2.orders_extra_info_value
                INNER JOIN customers AS c
                    ON c.customers_id = o.customers_id
                WHERE o.date_purchased >= '" . $this->start_date . "'
                    AND o.date_purchased <= '" . $this->end_date . "'
                    AND o.orders_status = 3
                    AND o.orders_cb_status IS NULL
                    AND op.products_bundle_id = 0 " .
                            ($this->exclude_customer_group ? "AND o.customers_groups_id NOT IN (" . implode(",", $this->exclude_customer_group) . ") " : "") .
                            ($this->payment_method ? "AND o.payment_methods_id IN (" . implode(",", $this->payment_method) . ") " : "") .
                            ($product ? "AND op.products_id IN (" . implode(",", $product) . ") " : "") .
                            ($this->ogm_only ? "AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5) " : "") .
                            ($this->ip_country_id ? "AND oei2.orders_extra_info_value IN (" . implode(",", $this->ip_country_id) . ") " : "") . "
                GROUP BY o.orders_id")
                    ->queryAll();
            foreach ($res as $row) {
                $flag = [];
                if (!empty($row['customers_flag'])) {
                    /*
                     * flag
                     * 1 = normal flag
                     * 2 = non-reversible payment only flag
                     * 4 = chargeback flag
                     */
                    $flag = explode(",", $row['customers_flag']);
                }

                // exclude chargeback customer
                if (!in_array(4, $flag)) {
                    $delivered_price = $row['products_good_delivered_price'];
                    if ($this->currency != "USD") {
                        $rate = Yii::$app->currency->advanceCurrencyConversionRateHist($row["date_purchased"], $row["currency"], $this->currency);
                        if ($rate["rate"] > 0) {
                            $delivered_price = $delivered_price * $rate["rate"];
                        } else {
                            $delivered_price = Yii::$app->currency->advanceCurrencyConversion($delivered_price, $row["currency"], $this->currency, true, "spot");
                        }
                    }

                    if (!$this->min_purchase_per_transaction || ($this->min_purchase_per_transaction && ($delivered_price >= $this->min_purchase_per_transaction))) {
                        if (isset($data[$row['customers_id']])) {
                            $data[$row['customers_id']]['total_amount'] += $delivered_price;
                            $data[$row['customers_id']]['total_order'] += 1;
                            if (!in_array($row['payer_email'], $data[$row['customers_id']]['payer_email'])) {
                                $data[$row['customers_id']]['payer_email'][] = $row['payer_email'];
                            }
                            if (!in_array($row['customers_email_address'], $data[$row['customers_id']]['mail'])) {
                                $data[$row['customers_id']]['mail'][] = $row["customers_email_address"];
                            }
                            if (!in_array($row['checkout_countries'], $data[$row['customers_id']]['country_ip'])) {
                                $data[$row['customers_id']]['country_ip'][] = $row["checkout_countries"];
                            }
                            $data[$row['customers_id']]['order'][] = ($row["orders_extra_info_value"] == 5 ? "G2G" : "OGM") . "-" . $row["orders_id"] . " (" . $row["date_purchased"] . ")";
                            $data[$row['customers_id']]['product_name'][] = $row["products_name"];
                        } else {
                            // First Time Purchase Date
                            $_row1 = Yii::$app->db_slave_offgamers->createCommand("SELECT o.date_purchased
                                    FROM orders AS o
                                    INNER JOIN orders_extra_info AS oei
                                        ON oei.orders_id = o.orders_id
                                        AND oei.orders_extra_info_key = 'site_id'
                                    WHERE o.orders_status = 3
                                        AND o.customers_id = " . $row['customers_id'] . "
                                        AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
                                    ORDER BY o.orders_id")
                                    ->queryOne();

                            // Not use specific Payment Method within date range
                            $use_payment_method = "";
                            if ($this->use_start_date && $this->use_end_date && $this->use_payment_method) {
                                $_row2 = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id
                                        FROM orders AS o
                                        INNER JOIN orders_extra_info AS oei
                                            ON oei.orders_id = o.orders_id
                                            AND oei.orders_extra_info_key = 'site_id'
                                        WHERE o.date_purchased >= '" . $this->use_start_date . "'
                                            AND o.date_purchased <= '" . $this->use_end_date . "'
                                            AND o.orders_status != 5
                                            AND o.customers_id = " . $row['customers_id'] . "
                                            AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
                                            AND o.payment_methods_id IN (" . implode(",", $this->use_payment_method) . ")
                                        ORDER BY o.orders_id DESC")
                                        ->queryOne();
                                if (isset($_row2["orders_id"])) {
                                    $use_payment_method = "Yes";
                                } else {
                                    $use_payment_method = "No";
                                }
                            }

                            $data[$row['customers_id']] = array(
                                'cust_group' => $cust_group[$row['customers_groups_id']],
                                'payer_email' => array($row['payer_email']),
                                'total_amount' => $delivered_price,
                                'country' => $row['countries_name'],
                                'country_ip' => array($row['checkout_countries']),
                                'total_order' => 1,
                                'mail' => array($row["customers_email_address"]),
                                'order' => array(($row["orders_extra_info_value"] == 5 ? "G2G" : "OGM") . "-" . $row["orders_id"] . " (" . $row["date_purchased"] . ")"),
                                'product_name' => array($row["products_name"]),
                                'first_purchase_date' => $_row1["date_purchased"],
                                'use_payment_method' => $use_payment_method
                            );
                        }
                    }
                }
            }

            foreach ($data as $cid => $info) {
                if (($info['total_amount'] >= $this->min_purchase) && (!$this->max_purchase || ($this->max_purchase && ($info['total_amount'] < $this->max_purchase)))) {
                    $oid = $odate = "";

                    $row = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id, o.date_purchased
                        FROM orders AS o
                        INNER JOIN orders_extra_info AS oei
                            ON oei.orders_id = o.orders_id
                            AND oei.orders_extra_info_key = 'site_id'
                        WHERE o.date_purchased >= '" . $this->start_date . "'
                            AND o.orders_status = 3
                            AND o.customers_id = " . $cid . "
                            AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
                        ORDER BY o.orders_id DESC")
                            ->queryOne();
                    if (isset($row["orders_id"])) {
                        $oid = $row["orders_id"];
                        $odate = $row["date_purchased"];
                    }

                    $chances_a = $info['total_amount'] / $this->amt_per_draw;
                    $chances_b = floor($chances_a);
                    $chances = (int) $chances_b;

                    $lucky_score = mt_rand(1, 100);
                    $winning_score = $chances * $lucky_score;

                    $str = [
                        "last_order_id" => $oid,
                        "last_order_datetime" => $odate,
                        "customer_id" => $cid,
                        "customer_group" => $info['cust_group'],
                        "paypal_email" => str_replace(",", "", implode('; ', $info['payer_email'])),
                        "email" => implode('; ', $info['mail']),
                        "country_of_residence" => $info['country'],
                        "checkout_country" => implode('; ', $info["country_ip"]),
                        "order_id" => implode('; ', $info["order"]),
                        "product_name" => str_replace(",", "", implode('; ', $info["product_name"])),
                        "total_sales" => $info['total_amount'],
                        'first_purchase_date' => $info['first_purchase_date'],
                        'use_payment_method' => $info['use_payment_method'],
                        "chance" => $chances,
                        "lucky_score" => $lucky_score,
                        "winning_score" => $winning_score
                    ];
                    fputcsv($fp, $str);
                }
            }

            rewind($fp);

            $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
            $this->s3->saveContent($filename, $fp);

            $content = 'Start Date : ' . $this->start_date . "\n" .
                    'End Date : ' . $this->end_date . "\n" .
                    'Sales Currency : ' . $this->currency . "\n" .
                    'Min Purchase per Transaction : ' . $this->min_purchase_per_transaction . "\n" .
                    'Min Purchase : ' . $this->min_purchase . "\n" .
                    'Max Purchase : ' . $this->max_purchase . "\n" .
                    'Amount per Draw : ' . $this->amt_per_draw . "\n" .
                    'OffGamers Only : ' . ($this->ogm_only ? "Yes" : "No") . "\n";

            $str = "All";
            if ($this->cat_id) {
                $m_res = [];
                foreach ($this->cat_id as $num => $id) {
                    $m_cat = \common\models\CategoriesDescription::find()
                            ->select(['categories_name'])
                            ->where(['categories_id' => $id, 'language_id' => 1])
                            ->one();
                    $m_res[] = $m_cat->categories_name;
                }
                $str = implode(", ", $m_res);
            }
            $content .= 'Category : ' . $str . "\n";

            $str = "All";
            if ($this->payment_method) {
                $m_res = [];
                foreach ($this->payment_method as $num => $id) {
                    $m_pm = \common\models\PaymentMethods::find()
                            ->select(['payment_methods_title'])
                            ->where(['payment_methods_id' => $id])
                            ->one();
                    $m_res[] = $m_pm->payment_methods_title;
                }
                $str = implode(", ", $m_res);
            }
            $content .= 'Payment Method : ' . $str . "\n";

            $str = "All";
            if ($this->ip_country_id) {
                $m_res = [];
                foreach ($this->ip_country_id as $num => $id) {
                    $m_ctry = \common\models\Countries::find()
                            ->select(['countries_name'])
                            ->where(['countries_id' => $id])
                            ->one();
                    $m_res[] = $m_ctry->countries_name;
                }
                $str = implode(", ", $m_res);
            }
            $content .= 'Checkout IP Country : ' . $str . "\n";

            $str = "All";
            if ($this->exclude_customer_group) {
                $m_res = [];
                foreach ($this->exclude_customer_group as $num => $id) {
                    $m_res[] = $cust_group[$id];
                }
                $str = implode(", ", $m_res);
            }
            $content .= 'Exclude Customer Group : ' . $str . "\n\n";

            if ($this->use_start_date && $this->use_end_date && $this->use_payment_method) {
                $str = "";
                $m_res = [];
                foreach ($this->use_payment_method as $num => $id) {
                    $m_pm = \common\models\PipwavePaymentMapper::find()
                            ->select(['pm_display_name', 'pg_display_name'])
                            ->where(['pm_Id' => $id])
                            ->one();
                    $m_res[] = $m_pm['pm_display_name'] . '(' . $m_pm['pg_display_name'] . ')';
                }
                $str = implode(", ", $m_res);

                $content .= "Additional Information : customer use specific payment method(s) within date range \n";
                $content .= "Start Date : " . $this->use_start_date . "\n" .
                        "End Date : " . $this->use_end_date . "\n" .
                        "Payment Method : " . $str . "\n\n";
            }

            $subject = "Campaign Report " . $this->start_date . " - " . $this->end_date;
            $content .= "The following document valid to download 5 days from the date of issue : \n" .
                    $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime
            $recipient = explode(",", $this->recipient);
            foreach ($recipient as $num => $val) {
                Yii::$app->mailer->compose()
                        ->setFrom(Yii::$app->params["noreply"]["default"])
                        ->setReplyTo(Yii::$app->params["noreply"]["default"])
                        ->setTo(trim($val))
                        ->setSubject($subject)
                        ->setTextBody($content)
                        ->send();
            }
        }

        return true;
    }

}
