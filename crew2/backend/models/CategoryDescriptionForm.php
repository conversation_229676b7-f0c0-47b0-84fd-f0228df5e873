<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\helpers\ArrayHelper;

class CategoryDescriptionForm extends Model
{
    public $categories_id;
    public $language_id;
    public $name;
    public $notice;
    public $short_description;
    public $description;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_id', 'language_id'], 'integer'],
            [['name', 'notice', 'short_description', 'description'], 'string'],
            [['name'], "required"]
        ];
    }

    public function customValidation($attribute, $params) {
        $this->addError("name", "Error");
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories ID',
            'language_id' => 'Language ID',
            'name' => 'Categories Name',
            'notice' => 'Notice',
            'short_description' => 'Short Description',
            'description' => 'Description'
        ];
    }

}
