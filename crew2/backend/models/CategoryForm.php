<?php

namespace backend\models;

use common\models\ApiFormModel;
use common\models\CategoryGroupPurchaseLimit;
use common\models\CategoryMetadata;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;

class CategoryForm extends ApiFormModel
{
    public $category_id;
    public $categories_id;
    public $brand_id;
    public $cdrules_id;

    public $seo_url;
    public $sort_order;
    public $status;

    public $name;

    public $category_description = [];
    public $category_group_purchase_limit = [];

    public $category_image;
    public $image;
    public $image_base64;
    public $image_extension;
    public $image_url;

    public $category_metadata = [];
    public $category_customer_group_restriction = [];
    public $category_soft_block = [];
    public $category_hard_block = [];
    public $category_payment_restriction = [];
    public $category_payment_type = [];

    public $type;

    public function rules()
    {
        return [
            [['sort_order', 'status', 'cdrules_id', 'categories_id', 'category_id'], 'integer'],
            [['seo_url', 'name'], 'string'],
            [['category_payment_restriction', 'category_customer_group_restriction', 'category_soft_block',
                'category_hard_block', 'category_payment_type','image_base64', 'image_extension', 'category_metadata',
                'category_group_purchase_limit'], 'safe'],
            [['category_image'], 'file',
                'skipOnEmpty' => true,
                'mimeTypes' => 'image/*',
                'maxSize' => (int)(str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024),
                'message' => 'Please upload an image'],
            [['seo_url'], 'validateDuplicateSeoURL'],
            [['brand_id', 'sort_order', 'seo_url'], 'required']
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'category';
        $this->method = 'get';
        $this->searchField = ['name', 'status'];
        parent::__construct();
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'index';
        return parent::search($params, $key);
    }

    public function get($id)
    {
        $this->action = 'view';
        $result = $this->request($id);
        $this->initApiData($result);
    }

    public function load($data, $formName = null)
    {
        $this->category_description = $this->getRequestCategoryDescription();
        $this->category_metadata = $this->getRequestMetadata();
        $this->category_group_purchase_limit = $this->getRequestPurchaseLimit();

        $this->category_customer_group_restriction = Yii::$app->request->post('category_customer_group_restriction', []);
        $this->category_soft_block = Yii::$app->request->post('category_soft_block', []);
        $this->category_hard_block = Yii::$app->request->post('category_hard_block', []);
        $this->category_payment_type = Yii::$app->request->post('category_payment_type', []);
        $this->category_payment_restriction = Yii::$app->request->post('category_payment_restriction', []);

        return parent::load($data, $formName);
    }

    public function save()
    {
        $this->setImageDetail();

        $this->action = 'save';
        $request = [
            'category_id' => $this->category_id,
            'categories_id' => $this->categories_id,
            'brand_id' => $this->brand_id,
            'seo_url' => $this->seo_url,
            'status' => $this->status,
            'sort_order' => $this->sort_order,
            'cdrules_id' => $this->cdrules_id,
            'category_customer_group_restriction' => $this->category_customer_group_restriction,
            'category_soft_block' => $this->category_soft_block,
            'category_hard_block' => $this->category_hard_block,
            'category_payment_restriction' => $this->category_payment_restriction,
            'category_payment_type' => $this->category_payment_type,
            'image_base64' => $this->image_base64,
            'image_extension' => $this->image_extension,
            'category_description' => $this->category_description,
            'category_metadata' => $this->category_metadata,
            'category_group_purchase_limit' => $this->category_group_purchase_limit,
        ];

        return $this->request($request);
    }

    public function setCategoryImage()
    {
        $this->category_image = UploadedFile::getInstance($this, 'category_image');
    }

    public function setImageDetail()
    {
        if ($this->category_image) {
            $image = $this->compressImage($this->category_image);
            $this->image_base64 = base64_encode($image);
            $this->image_extension = $this->category_image->extension;
        }
    }

    private function compressImage($image)
    {
        $filename = $image->name;
        $outputPath = rtrim(sys_get_temp_dir(), '/') . '/' . $filename;

        /**
         * @var \common\components\image\ImageOptimizerBase $imageOptimizer
         */
        $imageOptimizer = Yii::$app->imageOptimizer->getClient();
        $imageCompressionRatio = Yii::$app->params['category.image.compression.ratio'];

        if (in_array($image->type, $imageOptimizer->getSupportedMime()) && $imageCompressionRatio > -1) {
            $imageOptimizer->setCompressionRatio($imageCompressionRatio);
            $imageOptimizer->compressImageFromFile(
                $image->tempName,
                rtrim(sys_get_temp_dir(),
                    '/'),
                $filename);
        } else {
            move_uploaded_file($image->tempName, $outputPath);
        }

        $replacedImage = file_get_contents($outputPath);
        unlink($outputPath);

        return $replacedImage;
    }

    private function getRequestCategoryDescription()
    {
        $description_form_name = (new CategoryDescriptionForm())->formName();
        return !empty($_POST[$description_form_name]) ? $_POST[$description_form_name] : [];
    }

    private function getRequestMetadata()
    {
        $custom_seo_form_name = (new CategoryMetadata())->formName();
        return !empty($_POST[$custom_seo_form_name]) ? $_POST[$custom_seo_form_name] : [];
    }

    private function getRequestPurchaseLimit()
    {
        $purchase_limit_form_name = (new CategoryGroupPurchaseLimit())->formName();
        return !empty($_POST[$purchase_limit_form_name]) ? $_POST[$purchase_limit_form_name] : [];
    }

    public function initApiData($data)
    {
        $this->category_id = $data['category_id'];
        $this->categories_id = $data['categories_id'];
        $this->brand_id = $data['brand_id'] ?? '';
        $this->seo_url = $data['seo_url'] ?? '';
        $this->image_url = $data['image_url'] ?? '';
        $this->sort_order = $data['sort_order'] ?? '';
        $this->status = $data['status'] ?? '';
        $this->category_description = ArrayHelper::index($data['category_description'], 'language_id');
        $this->category_metadata = ArrayHelper::index($data['category_metadata'], 'language_id');
        $this->cdrules_id = $data['category_discount_list']['cdrules_id'] ?? '';
        $this->category_group_purchase_limit = ArrayHelper::index($data['category_group_purchase_limit'], 'customers_group_id');
        $this->category_customer_group_restriction = ArrayHelper::getColumn($data['category_customer_group_restriction'], 'customers_group_id');
        $this->category_soft_block = ArrayHelper::getColumn($data['category_soft_block'], 'country_code');
        $this->category_hard_block = ArrayHelper::getColumn($data['category_hard_block'], 'country_code');
        $this->category_payment_restriction = ArrayHelper::getColumn($data['category_payment_restriction'], 'payment_methods_id');
        $this->category_payment_type = ArrayHelper::index($data['category_payment_type'], 'type');
        $this->name = $this->category_description[1]['name'] ?? '';
    }

    public function checkSeoUrlExists($custom_seo, $category_id)
    {
        $this->action = 'check-duplicate-seo-url';
        $request = [
            'category_id' => $category_id,
            'seo_url' => $custom_seo,
        ];
        return $this->request($request);
    }

    public function delete($category_id)
    {
        $this->action = "delete";
        return $this->request($category_id);
    }

    public function validateDuplicateSeoURL($attribute, $params, $validator)
    {
        if ($this->checkSeoUrlExists($this->seo_url, $this->category_id)) {
            $this->addError($attribute, "The SEO URL already taken.");
        }
    }

    public function validatePurchaseLimit()
    {
        $purchaseLimitList = $this->getRequestPurchaseLimit();
        if (!empty($purchaseLimitList)) {
            foreach($purchaseLimitList as $purchaseLimit) {
                if (!$this->validateNumberWithoutMathExpression($purchaseLimit['amount'])) {
                    $this->addError("*", "Please insert decimal for Purchase Limit ['Amount']. ");
                    break;
                }
                if (!$this->validateInteger($purchaseLimit['x_minute'])) {
                    $this->addError("*", "Please insert integer for Purchase Limit ['X minute']. ");
                    break;
                }
            }
        }
    }

    private function validateNumberWithoutMathExpression($value)
    {
        return !$value || (is_numeric($value) && preg_match("/^[\d.]+$/", $value));
    }

    private function validateInteger($value)
    {
        return !$value || preg_match("/^\d+$/", $value);
    }

    public function customValidate() {
        $this->validatePurchaseLimit();

        if (!empty($this->getErrors()))  {
            return false;
        }
        return true;
    }

    public function validate($attributeNames = null, $clearErrors = true)
    {
        $modelValidationResult = parent::validate($attributeNames, $clearErrors);

        return $modelValidationResult && $this->customValidate();
    }
}
