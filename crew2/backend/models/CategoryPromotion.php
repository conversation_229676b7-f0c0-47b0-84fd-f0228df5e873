<?php

namespace backend\models;

use common\models\ApiFormModel;

class CategoryPromotion extends ApiFormModel
{
    public $category_promotion_id;
    public $category_id;
    public $start_date;
    public $end_date;
    public $unlimited_purchase_per_user;
    public $max_purchase_per_user;
    public $total_promo_quantity;
    public $status;

    public $category_promotion_customer_group = [];

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_promotion_id', 'category_id', 'total_promo_quantity', 'max_purchase_per_user'], 'integer'],
            [['start_date', 'end_date', 'unlimited_purchase_per_user',
                'total_promo_quantity', 'category_promotion_customer_group', 'status'], 'safe'],
            [['start_date', 'end_date', 'category_id'], 'required']
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'category';
        $this->method = 'get';
        $this->searchField = [];
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_promotion_id' => 'Categories Promotion ID',
            'category_id' => 'Category ID',
            'unlimited_purchase_per_user' => 'Unlimited Purchase per User',
            'max_purchase_per_user' => 'Max Purchase per User',
            'total_promo_quantity' => 'Total Promo Quantity',
            'status' => 'Status',
            'start_date' => 'Start Date',
            'end_date' => 'End Date'
        ];
    }

    public function load($data, $formName = null)
    {
        $this->category_promotion_customer_group = $this->getRequestCategoryPromotionCustomer();
        return parent::load($data, $formName);
    }

    public function save()
    {
        $this->action = 'save-promotion';
        $request = [
            'category_promotion_id' => $this->category_promotion_id,
            'category_id' => $this->category_id,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'unlimited_purchase_per_user' => $this->unlimited_purchase_per_user ?: 0,
            'max_purchase_per_user' => $this->max_purchase_per_user ?: 0,
            'total_promo_quantity' => $this->total_promo_quantity ?:0,
            'category_promotion_customer_group' => $this->category_promotion_customer_group,
        ];
        return $this->request($request);
    }

    public function get($category_promotion_id)
    {
        $this->action = 'view-promotion';
        return $this->request($category_promotion_id);
    }

    public function getList($category_id)
    {
        $this->action = 'get-promotion-list';
        return $this->request($category_id);
    }

    public function delete($category_promotion_id)
    {
        $this->action = "delete-promotion";
        return $this->request($category_promotion_id);
    }

    private function getRequestCategoryPromotionCustomer()
    {
        $request = [];
        $promotion_customer_form_name = (new CategoryPromotionCustomer())->formName();
        if (!empty($_POST[$promotion_customer_form_name])) {
            foreach($_POST[$promotion_customer_form_name] as $key => $value) {
                $request[] = [
                    "customers_group_id" => $key,
                    "promo_quantity" => $value['promo_quantity'],
                    "discount_percentage" => $value['discount_percentage'],
                    "op_reward" => $value['op_reward'],
                ];
            }
            return $request;
        }
        return [];
    }

    public function validateCategoryPromotionCustomer()
    {
        $categoryPromotionCustomerList = $this->getRequestCategoryPromotionCustomer();
        if (!empty($categoryPromotionCustomerList)) {

            foreach($categoryPromotionCustomerList as $categoryPromotionCustomer) {
                if (!$this->validateInteger($categoryPromotionCustomer['promo_quantity'])) {
                    $this->addError("*", "Please insert integer for promo quantity ");
                    break;
                }
                if (!$this->validateNumberWithoutMathExpression($categoryPromotionCustomer['discount_percentage'])) {
                    $this->addError("*", "Please insert decimal for discount percentage. ");
                    break;
                }
                if (!$this->validateNumberWithoutMathExpression($categoryPromotionCustomer['op_reward'])) {
                    $this->addError("*", "Please insert decimal for Offgamers Point. ");
                    break;
                }
            }
        }
    }

    private function validateNumberWithoutMathExpression($value)
    {
        return !$value || (is_numeric($value) && preg_match("/^[\d.]+$/", $value));
    }

    private function validateInteger($value)
    {
        return !$value || preg_match("/^\d+$/", $value);
    }

    public function customValidate() {
        $this->validateCategoryPromotionCustomer();

        if (!empty($this->getErrors()))  {
            return false;
        }
        return true;
    }

    public function validate($attributeNames = null, $clearErrors = true)
    {
        $modelValidationResult = parent::validate($attributeNames, $clearErrors);

        return $modelValidationResult && $this->customValidate();
    }

}
