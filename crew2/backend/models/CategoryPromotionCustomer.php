<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\helpers\ArrayHelper;

class CategoryPromotionCustomer extends Model
{
    public $category_promotion_customer_group_id;
    public $category_promotion_id;
    public $customers_group_id;
    public $promo_quantity;
    public $discount_percentage;
    public $op_reward;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_promotion_customer_group_id', 'category_promotion_id', 'customers_group_id',
                'promo_quantity'], 'integer'],
            [['discount_percentage', 'op_reward'], 'number']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_promotion_customer_group_id' => 'Category Promotion Customer Group ID',
            'category_promotion_id' => 'Category Promotion ID',
            'customers_group_id' => 'Customers Group ID',
            'promo_quantity' => 'Promo Quantity',
            'discount_percentage' => 'Discount Percentage',
            'op_reward' => 'OP Reward'
        ];
    }

}
