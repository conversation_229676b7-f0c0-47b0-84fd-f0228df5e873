<?php

namespace backend\models;


use Yii;
use common\models\CouponRedeemTrack;
use common\models\Coupons;
use yii\base\Model;
use yii\data\ArrayDataProvider;

class CouponRedeemTrackForm extends Model
{

    public $order_id, $coupon_info;
    public $list = [];
    public $pageSize = 10, $page = 1, $resultCount = 1, $startAt = 1, $endAt = 10;
    public $isNewRecord, $errors = false;

    public function __construct($orderId = '')
    {
        if (isset($orderId) && !empty($orderId)) {
            $this->getCouponRedeemInfo($orderId);
        }
    }

    public function rules()
    {
        return [
            [['order_id'], 'integer'],
            [['order_id'], 'validateSearch', 'skipOnEmpty' => false, 'skipOnError' => false, 'on' => 'search_filt'],
        ];
    }

    public function getCouponRedeemInfo($orderId)
    {
        $this->coupon_info = CouponRedeemTrack::findOne(['order_id' => $orderId]);
    }

    public function validateSearch()
    {
        if (($this->order_id == "")) {
            $this->addError('order_id', 'Order ID cannot be empty');
            return false;
        } else {
            $_GET['order_id'] = $this->order_id;
        }
        return true;
    }

    public function search()
    {
        $query = CouponRedeemTrack::find()->alias('c');
        $query->select([
            'c.unique_id',
            'c.coupon_id',
            'c.order_id',
            'c.customer_id',
            'c.redeem_date',
            'p.coupon_code',
           ]);
        $query->orderBy("c.order_id ASC");
        $query->joinWith("couponCode p", "p.coupon_id = c.coupon_id" );
      
        // filter by order_id
        if ($this->order_id) {
            $query->where(['c.order_id' => $this->order_id]);
        }

        return new ArrayDataProvider([
            'allModels'  => $query->asArray()->all(),
            'pagination' => [
                'pageSize' => $this->pageSize,
            ],
        ]);
    }

   
 

}
