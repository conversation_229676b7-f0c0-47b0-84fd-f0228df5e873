<?php

namespace backend\models;

use backend\validators\ReportRecipientEmailValidator;
use common\models\DeliverQueue;
use yii\base\Model;
use yii\helpers\Json;

class CouponUsageReportForm extends Model
{
    public $startDate;
    public $endDate;
    public $previousPurchaseDate;
    public $subsequentPurchaseDate;
    public $minPurchase = 0.01;
    public $reportRecipient;
    public $couponCode;
    public $couponGenerationId;
    public $paymentMethods = [];
    public $excludeCustomerGroup = [];
    private $isRequestSavedToDeliveryQueue = false;

    public function rules(): array
    {
        return [
            [['startDate', 'endDate', 'reportRecipient'], 'required'],
            ['endDate', 'compare', 'compareAttribute' => 'startDate',
                'operator' => '>=', 'message' => 'Date must not be BEFORE the Start date'],
            ['previousPurchaseDate', 'compare', 'compareAttribute' => 'startDate',
                'operator' => '<=', 'message' => 'Date must not be AFTER the Start date'],
            ['subsequentPurchaseDate', 'compare', 'compareAttribute' => 'endDate',
                'operator' => '>=', 'message' => 'Date must not be BEFORE the End date'],
            [['minPurchase'], 'number', 'min' => 0.01],
            [['couponGenerationId'], 'number'],
            [['minPurchase', 'couponCode', 'couponGenerationId', 'reportRecipient'], 'trim'],
            [['reportRecipient'], 'string'],
            [['reportRecipient'], ReportRecipientEmailValidator::class],
            [['couponCode', 'couponGenerationId'], function ($attribute)
            {
                if ($this->couponCode && $this->couponGenerationId) {
                    $this->addError($attribute, "Fill in either Coupon Code or Coupon Generation ID");
                }
            }],
        ];
    }

    public function saveToQueue(): void
    {
        $extra = $this->getExtra();

        $deliverQueue = new DeliverQueue();
        $deliverQueue->type = "REPORT";
        $deliverQueue->id = 1;
        $deliverQueue->extra_info = Json::encode($extra);
        $deliverQueue->created_at = time();

        $this->isRequestSavedToDeliveryQueue = $deliverQueue->save();
    }

    public function isRequestSavedToDeliveryQueue(): bool
    {
        return $this->isRequestSavedToDeliveryQueue;
    }

    private function getExtra(): array
    {
        return [
            'type' => 'coupon',
            'params' => [
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
                'previousPurchaseDate' => empty($this->previousPurchaseDate)
                    ? null : $this->previousPurchaseDate,
                'subsequentPurchaseDate' => empty($this->subsequentPurchaseDate)
                    ? null : $this->subsequentPurchaseDate,
                'minPurchase' => $this->minPurchase,
                'reportRecipient' => $this->reportRecipient,
                'paymentMethods' => $this->paymentMethods,
                'excludeCustomerGroup' => $this->excludeCustomerGroup,
                'couponCode' => empty($this->couponCode) ? null : $this->couponCode,
                'couponGenerationId' => empty($this->couponGenerationId) ? null : $this->couponGenerationId,
            ]
        ];
    }
}
