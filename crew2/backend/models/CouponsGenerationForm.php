<?php

namespace backend\models;

use common\models\CouponsGeneration;
use common\models\CouponsGenerationDescription;
use Yii;

class CouponsGenerationForm extends \yii\base\Model
{
    public $code_type;
    public $fixed_code;
    public $title;
    public $coupon_code_prefix;
    public $coupon_code_suffix;
    public $coupon_amount;
    public $coupon_minimum_order;
    public $max_cap;
    public $coupon_start_date;
    public $coupon_expire_date;
    public $coupon_number;
    public $uses_per_coupon;
    public $uses_per_coupon_unlimited;
    public $uses_per_user;
    public $uses_per_user_unlimited;
    public $restrict_to_products;
    public $restrict_to_categories;
    public $restrict_to_customers;
    public $restrict_to_customers_groups;
    public $restrict_to_currency_id;
    public $restrict_to_payment_id;
    public $coupon_generation_status;
    public $coupon_type;
    public $mobile_only;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_minimum_order', 'max_cap'], 'number', 'min' => 0, 'max' => 9999],
            [['coupon_start_date', 'coupon_expire_date', 'restrict_to_products', 'restrict_to_categories', 'restrict_to_customers', 'restrict_to_currency_id', 'restrict_to_payment_id', 'restrict_to_customers_groups'], 'safe'],
            [['coupon_number'], 'integer', 'min' => 1, 'max' => 99999],
            [['uses_per_coupon', 'uses_per_user'], 'integer', 'min' => 0, 'max' => 99999],
            [['coupon_amount', 'code_type', 'fixed_code'], 'string'],
            [['uses_per_coupon_unlimited', 'uses_per_user_unlimited', 'coupon_generation_status'], 'string', 'max' => 1],
            [['coupon_code_prefix', 'coupon_code_suffix'], 'string', 'max' => 5],
            [['fixed_code'], 'string', 'max' => 32],
            [['title'], 'string', 'max' => 64],
            [['title', 'code_type', 'coupon_start_date', 'coupon_expire_date', 'coupon_amount', 'coupon_type'], 'required'],
            [['fixed_code'], 'validateFixedCode'],
            [['coupon_amount'], 'validateCouponAmount'],
            [['coupon_expire_date'], 'validateCouponDate'],
            [['coupon_minimum_order', 'max_cap'], 'default', 'value' => '0'],
            [['coupon_code_prefix', 'coupon_code_suffix', 'fixed_code'], 'match', 'pattern' => '/^[a-z0-9]*$/i'],
            [
                ['coupon_number', 'uses_per_coupon', 'uses_per_user'],
                'required',
                'isEmpty' => function ($value) {
                    return $value === "";
                },
            ],
            [['restrict_to_products', 'restrict_to_categories', 'restrict_to_customers'], 'validateCommaNumberValue'],
            [['mobile_only'], 'integer', 'min' => 0, 'max' => 1],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'Title' => 'Title',
            'Code Type' => 'Code Type',
            'Fixed Code' => 'Fixed Code',
            'coupon_code_prefix' => 'Coupon Code Prefix',
            'coupon_code_suffix' => 'Coupon Code Suffix',
            'coupon_amount' => 'Coupon Amount',
            'coupon_minimum_order' => 'Coupon Minimum Order',
            'max_cap' => 'Max Cap',
            'coupon_start_date' => 'Coupon Start Date',
            'coupon_expire_date' => 'Coupon Expire Date',
            'coupon_number' => 'Number Of Coupon To Generate',
            'uses_per_coupon' => 'Uses Per Coupon',
            'uses_per_coupon_unlimited' => 'Uses Per Coupon Unlimited',
            'uses_per_user' => 'Uses Per User',
            'uses_per_user_unlimited' => 'Uses Per User Unlimited',
            'restrict_to_products' => 'Restrict To Products (Products ID, Comma Seperated)',
            'restrict_to_categories' => 'Restrict To Categories (Categories ID, Comma Seperated)',
            'restrict_to_customers' => 'Restrict To Customers (Customers ID, Comma Seperated)',
            'restrict_to_customers_groups' => 'Restrict To Customers Groups',
            'restrict_to_currency_id' => 'Restrict To Currency ID',
            'restrict_to_payment_id' => 'Restrict To Payment ID',
            'coupon_generation_status' => 'Coupon Generation Status',
            'requester_id' => 'Requester ID',
            'mobile_only' => 'Mobile Only',
            'created_by' => 'Creator',
            'date_created' => 'Date Created',
            'date_modified' => 'Date Modified',
        ];
    }

    public function process()
    {
        $model = new CouponsGeneration();

        $model->load([
            'coupon_minimum_order' => $this->coupon_minimum_order,
            'max_cap' => $this->max_cap,
            'coupon_start_date' => $this->coupon_start_date,
            'coupon_expire_date' => $this->coupon_expire_date,
            'coupon_number' => $this->coupon_number,

            'uses_per_coupon_unlimited' => ($this->uses_per_coupon_unlimited ? 'Y' : 'N'),
            'uses_per_coupon' => ($this->uses_per_coupon_unlimited ? 0 : $this->uses_per_coupon),

            'uses_per_user_unlimited' => ($this->uses_per_user_unlimited ? 'Y' : 'N'),
            'uses_per_user' => ($this->uses_per_user_unlimited ? 0 : $this->uses_per_user),

            'restrict_to_products' => $this->restrict_to_products,
            'restrict_to_categories' => $this->restrict_to_categories,
            'restrict_to_customers' => $this->restrict_to_customers,

            'restrict_to_customers_groups' => (!empty($this->restrict_to_customers_groups) ? implode(',', $this->restrict_to_customers_groups) : 'ALL'),
            'restrict_to_currency_id' => (!empty($this->restrict_to_currency_id) ? implode(',', $this->restrict_to_currency_id) : ''),
            'restrict_to_payment_id' => (!empty($this->restrict_to_payment_id) ? implode(',', $this->restrict_to_payment_id) : ''),

            'coupon_generation_status' => 'P',

            'mobile_only' => ($this->mobile_only ?: 0),

            // Force Requester ID to 1 and replace with created_by
            'requester_id' => 1,
            'created_by' => Yii::$app->user->identity->username,

        ], '');

        if ($this->code_type == 0) {
            $model->coupon_code_prefix = '';
            $model->coupon_code_suffix = '';
            $model->fixed_code = $this->fixed_code;
            $model->coupon_number = 1;
        } else {
            $model->coupon_code_prefix = $this->coupon_code_prefix;
            $model->coupon_code_suffix = $this->coupon_code_suffix;
        }

        if ($this->coupon_type == 'P') {
            $model->coupon_type = 'P';
            $model->coupon_amount = (float)rtrim($this->coupon_amount, '%');
        } else {
            $model->coupon_type = 'F';
            $model->coupon_amount = $this->coupon_amount;
        }

        $model->validate();

        $model->save();

        if ($model->coupon_generation_id) {
            $description = new CouponsGenerationDescription();
            $description->load([
                'coupon_generation_id' => $model->coupon_generation_id,
                'language_id' => 1,
                'coupon_generation_name' => $this->title,
            ], '');
            $description->save();
        }

        return $model->coupon_generation_id;
    }

    public function validateFixedCode($attribute, $params, $validator)
    {
        if (!empty($this->fixed_code) && CouponsGeneration::checkIsDuplicate($this->fixed_code)) {
            $this->addError($attribute, 'Duplicate Coupon Code Found');
        }
    }

    public function validateCouponAmount($attribute, $params, $validator)
    {
        $amount = trim($this->coupon_amount, '%');
        if (!is_numeric($amount)) {
            $this->addError($attribute, 'Only Number and Percentage Allowed');
        }
        if ($this->isPercentageCoupon() && $amount > 100) {
            $this->addError($attribute, 'Coupon Amount cannot exceed 100%');
        }
    }

    public function validateCouponDate($attribute, $params, $validator)
    {
        if ($this->coupon_start_date > $this->coupon_expire_date) {
            $this->addError('coupon_expire_date', 'Invalid Date');
        }
    }

    public function validateCommaNumberValue($attribute, $params, $validator)
    {
        $list = explode(",", $this->$attribute);
        foreach ($list as $item) {
            if (ctype_digit($item) === false) {
                $this->addError($attribute, 'Invalid ' . $attribute);
                break;
            }
        }
    }

    private function isPercentageCoupon()
    {
        if (substr($this->coupon_amount, -1) == '%') {
            return true;
        }
        return false;
    }
}