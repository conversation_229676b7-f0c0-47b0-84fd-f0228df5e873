<?php

namespace backend\models;

use backend\models\TaxForm;
use common\models\Customers;
use common\models\CustomersInfo;
use common\models\OrdersTaxConfiguration;
use common\models\OrdersTaxConfigurationDescription;
use common\models\OrdersTaxCustomers;
use common\models\OrdersTaxCustomersHistory;
use Yii;
use yii\base\Model;
use yii\data\ArrayDataProvider;

class CustomerForm extends Model
{

    public $customers_id, $tax_pending_status, $tax_verified_status, $tax_failed_status, $tax_canceled_status;
    public $business_tax_remarks, $business_tax_show_customer, $orders_tax_customers_status;
    public $customersInfo, $customerTaxInfo, $customerTaxHistory, $taxConfig, $taxConfigDesc;
    public $list = [];
    public $pageSize = 10, $page = 1, $resultCount = 1, $startAt = 1, $endAt = 10;
    public $isNewRecord, $errors = false;
    public $emailValidation, $sesBounce;

    public function __construct($customerId = '')
    {
        if (isset($customerId) && !empty($customerId)) {
            $this->getCustomerInfo($customerId);
        }
    }

    public function rules()
    {
        return [
            [
                [
                    'customers_id',
                    'tax_pending_status',
                    'tax_verified_status',
                    'tax_failed_status',
                    'tax_canceled_status'
                ],
                'integer'
            ],
            [['business_tax_remarks'], 'string', 'max' => 255],
            [['business_tax_remarks', 'orders_tax_customers_status', 'customers_id'], 'required', 'on' => 'update_tax'],
            [['business_tax_show_customer', 'orders_tax_customers_status', 'customers_id'], 'safe'],
            [
                [
                    'customers_id',
                    'tax_pending_status',
                    'tax_verified_status',
                    'tax_failed_status',
                    'tax_canceled_status'
                ],
                'validateSearch',
                'skipOnEmpty' => false,
                'skipOnError' => false,
                'on' => 'search_filt'
            ],
        ];
    }

    public function getCustomerInfo($customerId)
    {
        $this->customers_id = $customerId;
        $this->customersInfo = Customers::findOne($customerId);
        $this->customerTaxInfo = OrdersTaxCustomers::findOne(['customers_id' => $customerId]);
        if ($this->customerTaxInfo) {
            $this->customerTaxHistory = OrdersTaxCustomersHistory::find()->where(['customers_id' => $customerId])->orderBy(['id' => SORT_DESC])->all();
            $this->taxConfig = OrdersTaxConfiguration::findOne(['orders_tax_id' => $this->customerTaxInfo->orders_tax_id]);
            $this->taxConfigDesc = OrdersTaxConfigurationDescription::findOne([
                'orders_tax_id' => $this->customerTaxInfo->orders_tax_id,
                'language_id' => 1
            ]);

        }
    }

    public function validateSearch()
    {
        if (($this->customers_id == "") && empty($this->tax_failed_status) && empty($this->tax_pending_status) && empty($this->tax_verified_status) && empty($this->tax_canceled_status)) {
            $this->addError('customers_id', 'Customer ID cannot be empty');
            return false;
        } else {
            $_GET['customers_id'] = $this->customers_id;
            $_GET['tax_failed_status'] = $this->tax_failed_status;
            $_GET['tax_pending_status'] = $this->tax_pending_status;
            $_GET['tax_verified_status'] = $this->tax_verified_status;
            $_GET['tax_canceled_status'] = $this->tax_canceled_status;
        }

        return true;
    }

    public function search()
    {
        $query = Customers::find()->alias('c');
        $query->select([
            'c.customers_id',
            'c.customers_firstname',
            'c.customers_lastname',
            'c.customers_email_address',
            'c.customers_status',
            'ci.customers_info_date_account_created',
            'ci.customers_info_date_of_last_logon',
            'ci.customer_info_selected_country'
        ]);
        $query->orderBy("c.customers_id ASC");
        $query->joinWith("customersInfo ci", "ci.customers_info_id = c.customers_id");
        $query->joinWith("ordersTaxCustomers otc", "otc.customers_id = c.customers_id");

        // filter by customer_id
        if ($this->customers_id) {
            $query->where(['c.customers_id' => $this->customers_id]);
        }

        // filter by tax request status
        $tax_status = [];
        if (!empty($this->tax_pending_status)) {
            $tax_status[] = 0;
        }
        if (!empty($this->tax_verified_status)) {
            $tax_status[] = 1;
        }
        if (!empty($this->tax_failed_status)) {
            $tax_status[] = 2;
        }
        if (!empty($this->tax_canceled_status)) {
            $tax_status[] = 3;
        }
        if ($tax_status) {
            $query->andWhere(['in', 'otc.orders_tax_customers_status', $tax_status]);
        }

        return new ArrayDataProvider([
            'allModels' => $query->asArray()->all(),
            'pagination' => [
                'pageSize' => $this->pageSize,
            ],
        ]);
    }

    public function update($input)
    {
        $model = new TaxForm();
        return $model->processApplication($input);
    }

    public function getDynamicFormValue()
    {
        $html = '';

        if ($this->customerTaxInfo) {
            $html .= '<div class="form-group m-form__group row"><div class="col-lg-12">';

            if ($this->customerTaxInfo->orders_tax_customers_data) {
                $inputArraySnapshot = json_decode($this->customerTaxInfo->orders_tax_customers_data, true);
                $inputArray = $inputArraySnapshot['field_data'];
                foreach ($inputArray as $key => $value) {
                    $sort['sort_order'][$key] = $value['sort_order'];
                    $sort['id'][$key] = $value['id'];
                }
                # sort by sort_order asc and then id asc
                array_multisort($sort['sort_order'], SORT_ASC, $sort['id'], SORT_ASC, $inputArray);

                foreach ($inputArray as $key => $value) {
                    // Skip status = false, skip basic mandatory field, skip dynamic input type label or information text
                    if ($value['status'] != 1 || $value['id'] == 1 || $value['id'] == 2 || $value['type'] == 'display_label' || $value['type'] == 'information_text') {
                        continue;
                    }
                    $html .= '<div>';
                    $html .= '<label class="customer-view-label">' . $value['title'] . ':</label>';
                    $html .= $value['value'];
                    $html .= '</div>';
                }
            }

            $html .= '</div></div>';
        }
        return $html;
    }

    public function getTaxVerificationSetting()
    {
        return [
            0 => Yii::t('tax-module', 'Pending'),
            1 => Yii::t('tax-module', 'Verified'),
            2 => Yii::t('tax-module', 'Fail'),
            3 => Yii::t('tax-module', 'Cancel / Remove'),
        ];
    }

}
