<?php

namespace backend\models;

use common\models\ImageConfiguration;
use yii\base\InvalidArgumentException;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\UploadedFile;
use common\components\ShortPixel;

class FileUploadForm extends \yii\base\Model
{
    public $imageFile;
    public $uploadedFile;
    public $imageCompressionRatio;
    public $directory;
    public $s3_directory;
    public $subDirectory;
    public $allowOverwrite = 1;

    /**
     * @var \offgamers\base\components\AWSS3
     */
    private $s3_obj;

    public function rules()
    {
        return [
            [['imageFile'], 'file', 'skipOnEmpty' => false, 'mimeTypes' => 'image/*', 'maxSize' => (int)(str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024)],
            [['uploadedFile'], 'file', 'skipOnEmpty' => true],
            [['allowOverwrite'], 'integer'],
            [['subDirectory'], 'parseSubDirectory'],
            [['directory', 'imageCompressionRatio', 'subDirectory'], 'safe']
        ];
    }

    public function parseSubDirectory()
    {
        $this->subDirectory = trim(strtolower(preg_replace("![^a-z0-9/]+!i", "-", $this->subDirectory)), "-");
    }

    public function attributeLabels()
    {
        return [
            'directory' => 'Directory',
            'subDirectory' => 'Sub Directory',
            'imageCompressionRatio' => 'Compression Level',
            'allowOverwrite' => 'Overwrite Existing File'
        ];
    }

    public function uploadImages($filename = '')
    {
        $filename = (!empty($filename) ? $filename : $this->imageFile->baseName);
        $filename = trim(strtolower(preg_replace("![^a-z0-9_]+!i", "-", $filename)), "-") . '.' . $this->imageFile->extension;
        $output_path = rtrim(sys_get_temp_dir(), '/') . '/' . $filename;

        $this->getS3Object();

        $s3_path = trim($this->s3_directory, '/') . '/' . (!empty($this->subDirectory) ? trim($this->subDirectory, "/") . "/" : '') . $filename;

        if (!$this->allowOverwrite) {
            if ($this->s3_obj->doesContentExist($s3_path)) {
                throw new \Exception('File Already Exist in S3');
            }
        }

        /**
         * @var \common\components\image\ImageOptimizerBase $imageOptimizer
         */
        $imageOptimizer = Yii::$app->imageOptimizer->getClient();

        if (in_array($this->imageFile->type, $imageOptimizer->getSupportedMime()) && $this->imageCompressionRatio > -1) {
            $imageOptimizer->setCompressionRatio($this->imageCompressionRatio);
            $imageOptimizer->compressImageFromFile($this->imageFile->tempName, rtrim(sys_get_temp_dir(), '/'), $filename);

        } else {
            move_uploaded_file($this->imageFile->tempName, $output_path);
        }

        $image = file_get_contents($output_path);
        unlink($output_path);

        if ($path = $this->s3_obj->saveContent($s3_path, $image, false, 2592000, array('ContentType' => $this->imageFile->type))) {
            $url = $this->s3_obj->getObjectUrl($this->s3_obj->parsePath($s3_path));
            return ['fileName' => $filename, 'url' => (!preg_match("@^https?://@", $url) ? "https://" : "") . $url];
        }
    }

    public function getUploadHistory()
    {
        $this->getS3Object();
        $directory = trim($this->s3_directory, '/') . '/' . trim($this->subDirectory, "/");
        $list = $this->s3_obj->listObject($directory);
        $result = [];
        if (is_array($list) && count($list) > 0) {
            foreach ($list as $item) {
                $filename = explode("/", $item["Key"]);
                $filename = end($filename);
                $timestamp = strtotime($item['LastModified']);
                $url = $this->s3_obj->getObjectUrl($item['Key']);
                $result[] = [
                    'fileName' => $filename,
                    'key' => $item['Key'],
                    'directory' => $this->directory,
                    'url' => (!preg_match("@^https?://@", $url) ? "https://" : "") . $url,
                    'lastModified' => date('d/m/Y H:i:s A', $timestamp),
                    'timestamp' => $timestamp,
                    'size' => (integer)($item['Size'] / 1000) . 'kb',
                ];
            }
            return $result;
        }
        return [];
    }

    public function deleteFile($path)
    {
        $this->getS3Object();
        $path = $this->s3_obj->cleanPrefix($path);
        if (!$this->s3_obj->deleteContent($path)) {
            throw new InvalidArgumentException('Fail to delete ' . $path);
        }
    }

    private function getS3Object()
    {
        $configuration = ImageConfiguration::getDirectory();

        foreach ($configuration as $config) {
            if ($config['image_category'] == $this->directory) {
                $this->s3_obj = Yii::$app->aws->getS3($config['bucket']);
                $this->s3_directory = $config['path'];
                break;
            }
        }

        if (!$this->s3_obj) {
            throw new InvalidArgumentException('Invalid Directory');
        }
    }
}