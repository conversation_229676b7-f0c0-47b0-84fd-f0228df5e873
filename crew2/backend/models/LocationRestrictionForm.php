<?php

namespace backend\models;

use common\models\LocationRestriction;
use common\models\LocationRestrictionHistory;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\helpers\HtmlPurifier;

class LocationRestrictionForm extends Model {

    public $countryCodeIso2;
    public $countryCurrencyInfo;

    public function __construct($countryCodeIso2) {
        $this->countryCodeIso2 = $countryCodeIso2;
        $this->getCountryCurrencyInfo($countryCodeIso2);
    }

    // function to validate the  XSS(cross-site-scripting ).
    public function beforeValidate()
    {        
        foreach (array_keys($this->getAttributes()) as $attr) {
            if(!empty($this->$attr)) {
                // validate the html tags is attributes
                $this->$attr = strip_tags(HtmlPurifier::process($this->$attr));
            }
        }

        return parent::beforeValidate();// to keep parent validator available 
    }

    public function getCountryCurrencyInfo($countryCodeIso2) {
        $this->countryCurrencyInfo = LocationRestriction::find()->where(['country_iso_code2' => $this->countryCodeIso2])->one();
    }

    public function addRemoveRestrictions($currencies) {

        // validate each currency record
        foreach (array_keys($currencies) as $key){
            if(isset($currencies[$key]) && !empty($currencies[$key])) {
                // validate the html tags is attributes
                $currencies[$key] = strip_tags(HtmlPurifier::process($currencies[$key]));
            }
        }
        
        // Check if exist in restriction table?
        if ($this->countryCurrencyInfo) {
            // Set param update
            $oldRestrictionList = Json::decode($this->countryCurrencyInfo->restriction_info);
            $this->countryCurrencyInfo->restriction_info = Json::encode($currencies);
            $this->countryCurrencyInfo->update();
        } else {
            $oldRestrictionList = [];
            $restriction = new LocationRestriction;
            $restriction->country_iso_code2 = $this->countryCodeIso2;
            $restriction->restriction_info = Json::encode($currencies);
            $restriction->save();

            $this->getCountryCurrencyInfo($this->countryCodeIso2);
        }

        // clear cahce key for frontend list
        $countryCodeIso2 = ($this->countryCodeIso2) ? $this->countryCodeIso2 : $this->countryCurrencyInfo->country_iso_code2;
        Yii::$app->frontend_cache->delete('restriction_currency/' . $countryCodeIso2);

        $this->updateRestrictionsHistory(['old_list' => $oldRestrictionList, 'new_list' => $currencies]);
    }

    public function getCurrency() {
        if (empty($this->countryCodeIso2)) {
            return [];
        }

        $exists = [];

        // Get currencies list
        $currencyList = Yii::$app->currency->currency_list;
        $currencyListArray = [];
        foreach ($currencyList as $key => $value) {
            $currencyListArray[$key] = $value['title'] . ' (' . $key . ')';
        }

        // Find in restrictions table
        $data = $this->countryCurrencyInfo;
        if (isset($data->restriction_info) && !empty($data->restriction_info)) {
            $restrictedCurrencyArray = json_decode($data->restriction_info);
            foreach ($restrictedCurrencyArray as $key => $value) {
                if (isset($currencyListArray[$value]) && !empty($currencyListArray[$value])) {
                    $exists[$value] = $currencyListArray[$value];
                }
                unset($currencyListArray[$value]);
            }
        }

        return [
            'available' => $currencyListArray,
            'assigned' => $exists,
        ];
    }

    private function updateRestrictionsHistory($data) {
        // Insert restrictions history
        $restrictionHistory = new LocationRestrictionHistory;
        $restrictionHistory->location_restriction_id = $this->countryCurrencyInfo->id;
        $restrictionHistory->country_iso_code2 = $this->countryCodeIso2;
        $restrictionHistory->restriction_history = json_encode(['old' => $data['old_list'], 'new' => $data['new_list']]);
        $restrictionHistory->save();

        $this->getCountryCurrencyInfo($this->countryCodeIso2);
    }

}
