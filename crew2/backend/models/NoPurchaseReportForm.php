<?php

namespace backend\models;

use common\models\DeliverQueue;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\validators\EmailValidator;
use offgamers\base\components\Currency;
use offgamers\base\models\CurrenciesHistory;
use common\models\OrdersStatus;

class NoPurchaseReportForm extends Model
{

    public $signup_fr_date, $signup_to_date;
    public $start_date, $end_date;
    public $recipient;
    private $s3;

    public function rules()
    {
        return [
            [['signup_fr_date', 'signup_to_date', 'start_date', 'end_date', 'recipient'], 'required'],
            ['signup_to_date', 'compare', 'compareAttribute' => 'signup_fr_date', 'operator' => '>='],
            ['end_date', 'compare', 'compareAttribute' => 'start_date', 'operator' => '>='],
            ['start_date', 'compare', 'compareAttribute' => 'signup_fr_date', 'operator' => '>='],
            ['end_date', 'compare', 'compareAttribute' => 'signup_fr_date', 'operator' => '>='],
            [['recipient'], 'string'],
            [['recipient'], 'validateMail'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'signup_fr_date' => 'Sign-Up Start Date',
            'signup_to_date' => 'Sign-Up End Date',
            'start_date' => 'Order Start Date',
            'end_date' => 'Order End Date',
        ];
    }

    public function validateMail($attribute, $params)
    {
        $email = explode(",", $this->recipient);
        if ($email) {
            $err = [];
            $validator = new EmailValidator;

            foreach ($email as $val) {
                if (!$validator->validate(trim($val))) {
                    $err[] = $val;
                }
            }

            if ($err) {
                $this->addError($attribute, implode(", ", $err) . " is not a valid email.");
            }
        }
    }

    public function addDeliverQueue()
    {
        $extra = [
            'type' => 'no-purchase',
            'params' => [
                'signup_fr_date' => $this->signup_fr_date,
                'signup_to_date' => $this->signup_to_date,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'recipient' => $this->recipient
            ]
        ];

        $q = new DeliverQueue();
        $q->type = "REPORT";
        $q->id = 1;
        $q->extra_info = Json::encode($extra);
        $q->created_at = time();
        return $q->save();
    }

    public function generateReport()
    {
        if (isset($this->recipient) && !empty($this->recipient)) {
            ini_set("memory_limit", "-1");
            set_time_limit(0);

            $cust_group = \common\models\CustomersGroups::getGroupList();
            $order_status = (new OrdersStatus)->getOrdersStatus();
            $countries = \common\models\Countries::countriesNameID();

            $filename = "no-purchase/" . date("Ymd", strtotime($this->signup_fr_date)) . "_" . date("Ymd", strtotime($this->signup_to_date)) . "_" . date("YmdHis") . ".csv";
            $header = [
                "Sign-Up Date",
                "Customer ID",
                "Customer Last Name",
                "Customer Email Address",
                "Customer Group",
                "Country",
                "Phone Number",
                "eKYC Status",
                "Last Login",
                "Last Purchase Date",
                "Order ID",
                "Product",
                "Order Total",
                "Coupon Name",
                "Coupon Value",
                "Order Status"
            ];

            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, $header);

            // sign-up
            $res = Yii::$app->db_slave_offgamers->createCommand("SELECT ci.customers_info_id, ci.customers_info_date_of_last_logon, 
                        ci.customers_info_date_account_created 
                    FROM customers_info AS ci 
                    WHERE ci.customers_info_date_account_created >= '" . $this->signup_fr_date . "'
                        AND ci.customers_info_date_account_created <= '" . $this->signup_to_date . "'")
                    ->queryAll();
            foreach ($res as $row) {
                if ($this->start_date && $this->end_date) {
                    $sales = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id
                            FROM orders AS o
                            WHERE o.customers_id = " . $row["customers_info_id"] . "
                                AND o.date_purchased >= '" . $this->start_date . "'
                                AND o.date_purchased <= '" . $this->end_date . "'")
                            ->queryOne();

                    if (!isset($sales["orders_id"])) {
                        // no purchase
                        $str = [
                            "signup" => $row["customers_info_date_account_created"],
                            "cid" => $row["customers_info_id"],
                            "last_name" => "",
                            "email" => "",
                            "customer_group" => "",
                            "country" => "",
                            "phone" => "",
                            "eKYC" => "No",
                            "last_login" => $row["customers_info_date_of_last_logon"],
                            "date_purchased" => "",
                            "oid" => "",
                            "product" => "",
                            "order_total" => "",
                            "coupon" => "",
                            "coupon_value" => "",
                            "order_status" => ""
                        ];

                        // customer info
                        $_row = Yii::$app->db_slave_offgamers->createCommand("SELECT c.customers_lastname, c.customers_email_address,
                                    c.customers_groups_id, c.customers_country_dialing_code_id, c.customers_telephone
                                FROM customers AS c
                                WHERE c.customers_id = " . $str["cid"])
                                ->queryOne();
                        $str["last_name"] = $_row["customers_lastname"];
                        $str["email"] = $_row["customers_email_address"];
                        $str["customer_group"] = (isset($cust_group[$_row["customers_groups_id"]]) ? $cust_group[$_row["customers_groups_id"]] : "Unknown");
                        $str["country"] = (isset($countries[$_row["customers_country_dialing_code_id"]]) ? $countries[$_row["customers_country_dialing_code_id"]] : "Unknown");
                        $str["phone"] = $_row["customers_telephone"];

                        // eKYC
                        $_row = Yii::$app->db_slave_offgamers->createCommand("SELECT cvd.files_003_status
                                FROM customers_verification_document AS cvd
                                WHERE cvd.customers_id = " . $str["cid"])
                                ->queryOne();
                        $str["eKYC"] = ((isset($_row["files_003_status"]) && $_row["files_003_status"]) ? "Yes" : "No");

                        // last sales
                        $_row = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id, o.date_purchased, o.orders_status,
                                    ot1.value AS subtotal, ot2.title AS coupon_code, ot2.value AS coupon_value 
                                FROM orders AS o 
                                LEFT JOIN orders_total AS ot1 
                                    ON ot1.orders_id = o.orders_id 
                                    AND ot1.class = 'ot_subtotal' 
                                LEFT JOIN orders_total AS ot2 
                                    ON ot2.orders_id = o.orders_id 
                                    AND ot2.class = 'ot_coupon' 
                                WHERE o.customers_id = " . $str["cid"] . "
                                    AND o.date_purchased >= '" . $str["signup"] . "'
                                ORDER BY o.date_purchased DESC")
                                ->queryOne();
                        if (isset($_row["orders_id"])) {
                            $str["date_purchased"] = $_row["date_purchased"];
                            $str["oid"] = $_row["orders_id"];
                            $str["order_total"] = $_row["subtotal"];
                            $str["order_status"] = (isset($order_status[$_row["orders_status"]]) ? $order_status[$_row["orders_status"]] : "Unknown");
                            $str["coupon_value"] = $_row["coupon_value"];

                            $coupon_code = trim(str_replace("Discount Coupons:", "", $_row["coupon_code"]));

                            // product
                            $_res = Yii::$app->db_slave_offgamers->createCommand("SELECT op.products_name
                                    FROM orders_products AS op
                                    WHERE op.orders_id = " . $str["oid"] . "
                                        AND op.products_bundle_id = 0
                                        AND op.orders_products_is_compensate = 0")
                                    ->queryAll();
                            $cnt = count($_res);
                            foreach ($_res as $_row) {
                                $str["product"] .= $_row["products_name"] . ($cnt > 1 ? "\n" : "");
                            }

                            // coupon
                            if ($coupon_code != "") {
                                $_row = Yii::$app->db_slave_offgamers->createCommand("SELECT cd.coupon_name
                                        FROM coupons AS c
                                        INNER JOIN coupons_description AS cd
                                            ON cd.coupon_id = c.coupon_id
                                        WHERE c.coupon_code = " . $coupon_code . "
                                            AND cd.language_id = 1")
                                            ->queryOne();

                                $str["coupon"] = $_row["coupon_name"];
                            } else {
                                $str["coupon_value"] = "N/A";
                                $str["coupon"] = "N/A";
                            }
                        }

                        fputcsv($fp, $str);
                        unset($str);
                    }
                }
            }

            rewind($fp);

            $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
            $this->s3->saveContent($filename, $fp);

            $subject = "No Purchase Report " . $this->signup_fr_date . " - " . $this->signup_to_date;
            $content = "Sign-Up Start Date : " . $this->signup_fr_date . "\n" .
                    "Sign-Up End Date : " . $this->signup_to_date . "\n" .
                    "Order Start Date : " . $this->start_date . "\n" .
                    "Order End Date : " . $this->end_date . "\n\n" .
                    "The following document valid to download 5 days from the date of issue : \n" .
                    $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime

            $recipient = explode(",", $this->recipient);
            foreach ($recipient as $num => $val) {
                Yii::$app->mailer->compose()
                        ->setFrom(Yii::$app->params["noreply"]["default"])
                        ->setReplyTo(Yii::$app->params["noreply"]["default"])
                        ->setTo(trim($val))
                        ->setSubject($subject)
                        ->setTextBody($content)
                        ->send();
            }
        }

        return true;
    }

}
