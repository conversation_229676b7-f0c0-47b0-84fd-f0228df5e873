<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\PipwavePaymentMapper;
use common\models\PipwavePaymentMapperHistory;

/**
 * PipwavePaymentMapperForm represents the model behind the search form of `common\models\PipwavePaymentMapper`.
 */
class PipwavePaymentMapperForm extends PipwavePaymentMapper
{
    // Site ID = 0 for OG
    public $site_id = 0;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'pm_id', 'pg_id', 'is_rp', 'site_id', 'is_refundable'], 'integer'],
            [['pipwave_payment_code', 'pm_display_name', 'pg_display_name', 'pg_code'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = PipwavePaymentMapper::find();
        $query->orderBy("pm_id ASC, id DESC");

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'pm_id' => $this->pm_id,
            'pg_id' => $this->pg_id,
            'is_rp' => $this->is_rp,
            'site_id' => $this->site_id,
            'is_refundable' => $this->is_refundable,
        ]);

        $query->andFilterWhere(['like', 'pipwave_payment_code', $this->pipwave_payment_code])
            ->andFilterWhere(['like', 'pm_display_name', $this->pm_display_name])
            ->andFilterWhere(['like', 'pg_display_name', $this->pg_display_name])
            ->andFilterWhere(['like', 'pg_code', $this->pg_code]);

        return $dataProvider;
    }

    public function searchHistory($id)
    {
        $historyModel = new PipwavePaymentMapperHistory;
        
        $query = PipwavePaymentMapperHistory::find();
        $query->orderBy("id DESC");
        $query->andFilterWhere(['pipwave_payment_mapper_id' => $id]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return ['model' => $historyModel, 'data' => $dataProvider];
    }
}
