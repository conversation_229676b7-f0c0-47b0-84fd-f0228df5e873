<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\SqlDataProvider;

class ReportPoForm extends Model {

    public $issue_date, $startDate, $endDate, $poDateRange;
    public $list = [];
    public $pageSize = 20, $page = 1;

    public function rules() {
        return [
            [['pageSize', 'poDateRange', 'startDate', 'endDate'], 'safe'],
        ];
    }

    public static function getPaymentTypeList() {
        return [
            'g' => 'Consignment',
            'c' => 'Pre-Payment',
            'd' => 'DTU Payment',
        ];
    }

    public static function getPurchaseOrderTypeList() {
        return [
            0 => 'Regular PO',
            1 => 'DTU Payment',
            2 => 'API Repleanish Payment',
            3 => 'Consignment Payment',
        ];
    }

    public static function getPaymentType($type) {
        $list = self::getPaymentTypeList();
        return (isset($list[$type]) ? $list[$type] : $type);
    }

    public static function getPurchaseOrderType($type) {
        $list = self::getPurchaseOrderTypeList();
        return (isset($list[$type]) ? $list[$type] : "");
    }

    public function searchReport($params) {
        $this->attributes = $params;
        $this->startDate = !empty($this->startDate) ? $this->startDate : date('Y-m-d', strtotime('first day of last month')) . ' 00:00:00';
        $this->endDate = !empty($this->endDate) ? $this->endDate : date('Y-m-d', strtotime('last day of last month')) . ' 23:59:59';

        $count = Yii::$app->db->createCommand("SELECT COUNT(DISTINCT(po.purchase_orders_id))
                FROM purchase_orders AS po
                LEFT JOIN purchase_orders_status_history AS posh
                    ON posh.purchase_orders_id = po.purchase_orders_id
                WHERE po.purchase_orders_issue_date >= :startdate
                    AND po.purchase_orders_issue_date <= :enddate
                    AND posh.purchase_orders_status_id = 2
                    AND posh.purchase_orders_status_history_id = (
                        SELECT purchase_orders_status_history_id
                        FROM purchase_orders_status_history
                        WHERE purchase_orders_id = posh.purchase_orders_id
                        AND purchase_orders_status_id = 2
                        ORDER BY purchase_orders_status_history_id ASC
                        LIMIT 1
                    )")
                ->bindValues([':startdate' => date($this->startDate),':enddate' => date($this->endDate)])
                ->queryScalar();

        $query = "SELECT po.purchase_orders_id, po.purchase_orders_ref_id, po.purchase_orders_type, po.payment_type, po.payment_term,
                    posh1.date_added AS issue_date,
                    posh2.purchase_orders_status_id AS process_status, posh2.date_added AS process_date,
                    posh3.purchase_orders_status_id AS final_status, posh3.date_added AS final_date
                FROM purchase_orders AS po
                INNER JOIN purchase_orders_status_history AS posh1
                    ON posh1.purchase_orders_id = po.purchase_orders_id
                LEFT JOIN purchase_orders_status_history AS posh2
                    ON posh2.purchase_orders_id = po.purchase_orders_id
                LEFT JOIN purchase_orders_status_history AS posh3
                    ON posh3.purchase_orders_id = po.purchase_orders_id
                    AND posh3.purchase_orders_status_id IN (3, 4)
                    AND posh3.purchase_orders_status_history_id = (
                        SELECT purchase_orders_status_history_id
                        FROM purchase_orders_status_history
                        WHERE purchase_orders_id = posh3.purchase_orders_id
                        AND purchase_orders_status_id IN (3, 4)
                        ORDER BY purchase_orders_status_history_id DESC
                        LIMIT 1
                    )
                WHERE po.purchase_orders_issue_date >= :startdate
                    AND po.purchase_orders_issue_date <= :enddate
                    AND posh1.purchase_orders_status_id = 0
                    AND posh2.purchase_orders_status_id = 2
                    AND posh2.purchase_orders_status_history_id = (
                        SELECT purchase_orders_status_history_id
                        FROM purchase_orders_status_history
                        WHERE purchase_orders_id = posh2.purchase_orders_id
                        AND purchase_orders_status_id = 2
                        ORDER BY purchase_orders_status_history_id ASC
                        LIMIT 1
                    )
                GROUP BY po.purchase_orders_id";

        $provider = new SqlDataProvider([
            'sql' => $query,
            'params' => [':startdate' => date($this->startDate),':enddate' => date($this->endDate)],
            'totalCount' => $count
        ]);

        return $provider;
    }

}
