<?php

namespace backend\models;

use common\models\CategoriesDescription;
use common\models\DeliverQueue;
use common\models\Products;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\validators\EmailValidator;

class SalesReportForm extends Model
{

    public $report;
    public $start_date, $end_date;
    public $report_type;
    public $excl_cat = [];
    public $recipient;
    public $_excl_catname = [];
    public $_report = [
        1 => "Categories",
        2 => "Product Type"
    ];
    public $_report_type = [
        1 => "Daily",
        2 => "Monthly"
    ];
    public $s3;

    public function rules()
    {
        return [
            [['report', 'start_date', 'end_date', 'recipient'], 'required'],
            ['end_date', 'compare', 'compareAttribute' => 'start_date', 'operator' => '>=', 'message' => 'Date must not be BEFORE the Start Date'],
            ['report_type', 'required', 'when' => function ($model) {
                return $model->report == 2;
            }],
            [['recipient'], 'string'],
            [['recipient'], 'validateMail'],
        ];
    }

    public function validateMail($attribute, $params)
    {
        $email = explode(",", $this->recipient);
        if ($email) {
            $err = [];
            $validator = new EmailValidator;

            foreach ($email as $val) {
                if (!$validator->validate(trim($val))) {
                    $err[] = $val;
                }
            }

            if ($err) {
                $this->addError($attribute, implode(", ", $err) . " is not a valid email.");
            }
        }
    }

    public function addDeliverQueue()
    {
        $extra = [
            'type' => 'sales',
            'params' => [
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'report' => $this->report,
                'report_type' => $this->report_type,
                'excl_cat' => $this->excl_cat,
                'recipient' => $this->recipient
            ]
        ];

        $q = new DeliverQueue();
        $q->type = "REPORT";
        $q->id = 1;
        $q->extra_info = Json::encode($extra);
        $q->created_at = time();
        return $q->save();
    }

    public function generateReport()
    {
        if (isset($this->recipient) && !empty($this->recipient)) {
            ini_set("memory_limit", "-1");
            set_time_limit(0);

            switch ($this->report) {
                case 1: // categories
                    $this->_categories();
                    break;
                case 2: // product type
                    $this->_product_type();
                    break;
            }
        }

        return true;
    }

    private function _categories()
    {
        // exclude product
        $excl_pid = $this->_getExcludeProduct();

        $cat_id = [];
        $res = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id 
                FROM orders AS o
                LEFT JOIN orders_status_stat AS oss
                ON oss.orders_id = o.orders_id
                    AND oss.orders_status_id = 7
                WHERE o.orders_status IN (7, 2, 3, 8)
                    AND oss.first_date >= '" . $this->start_date . "'
                    AND oss.first_date <= '" . $this->end_date . "'")
            ->queryAll();
        foreach ($res as $row) {
            $_row1 = Yii::$app->db_slave_offgamers->createCommand("SELECT op.orders_products_id, op.products_id, op.custom_products_type_id, op.products_categories_id, 
                        SUM(op.final_price * op.products_quantity) as total_purchase_amount, 
                        SUM(op.products_good_delivered_price) AS total_delivered_amount, 
                        SUM(op.products_canceled_price) AS total_canceled_amount, 
                        SUM(op.products_reversed_price) AS total_reversed_amount 
                    FROM orders_products AS op 
                    WHERE op.orders_id = " . $row["orders_id"] . " 
                        AND op.parent_orders_products_id = 0 
                        AND op.orders_products_is_compensate = 0 
                        AND op.custom_products_type_id = 2")
                ->queryOne();
            if (isset($_row1["orders_products_id"]) && ($_row1["custom_products_type_id"] == 2)) {
                if (!in_array($_row1["products_id"], $excl_pid)) {
                    $pg_fee = 0;
                    $_row2 = Yii::$app->db_slave_offgamers->createCommand("SELECT ot.value
                                FROM orders_total AS ot
                                WHERE ot.orders_id = " . $row["orders_id"] . "
                                    AND ot.class = 'ot_surcharge'")
                        ->queryOne();
                    if (isset($_row2["value"])) {
                        $pg_fee = $_row2["value"];
                    }

                    if (!isset($cat_id[$_row1["products_categories_id"]])) {
                        $cat_id[$_row1["products_categories_id"]] = array(
                            "transaction" => 0,
                            "pg_fee" => 0,
                            "amount" => 0,
                            "delivered" => 0,
                            "cancel" => 0,
                            "reversed" => 0,
                        );
                    }

                    $cat_id[$_row1["products_categories_id"]]["transaction"] += 1;
                    $cat_id[$_row1["products_categories_id"]]["pg_fee"] += $pg_fee;
                    $cat_id[$_row1["products_categories_id"]]["amount"] += $_row1["total_purchase_amount"];
                    $cat_id[$_row1["products_categories_id"]]["delivered"] += $_row1["total_delivered_amount"];
                    $cat_id[$_row1["products_categories_id"]]["cancel"] += $_row1["total_canceled_amount"];
                    $cat_id[$_row1["products_categories_id"]]["reversed"] += $_row1["total_reversed_amount"];
                }
            }
        }

        $filepath = "";
        if ($cat_id) {
            $filename = "sales/" . date("Ymd", strtotime($this->start_date)) . "_" . date("Ymd", strtotime($this->end_date)) . "_" . date("YmdHis") . ".csv";
            $fp = fopen('php://temp', 'w+');

            $header = [
                "Category ID",
                "Category Name",
                "Transaction",
                "AOV (USD)",
                "PG Fee",
                "Amount (USD)",
                "Delivered (USD)",
                "Cancel (USD)",
                "Reversed (USD)"
            ];
            fputcsv($fp, $header);

            foreach ($cat_id as $cid => $val) {
                $row = Yii::$app->db_slave_offgamers->createCommand("SELECT c.categories_url_alias, cd.categories_name 
                        FROM categories AS c 
                        INNER JOIN categories_description AS cd 
                            ON cd.categories_id = c.categories_id 
                            AND cd.language_id = 1 
                        WHERE c.categories_id = " . $cid)
                    ->queryOne();
                if (isset($row["categories_name"])) {
                    $data = array(
                        $cid,
                        ($row["categories_name"] ? $row["categories_name"] : $row["categories_url_alias"]),
                        $cat_id[$cid]["transaction"],
                        ($cat_id[$cid]["transaction"] > 0 ? ($cat_id[$cid]["amount"] / $cat_id[$cid]["transaction"]) : 0),
                        $cat_id[$cid]["pg_fee"],
                        $cat_id[$cid]["amount"],
                        $cat_id[$cid]["delivered"],
                        $cat_id[$cid]["cancel"],
                        $cat_id[$cid]["reversed"]
                    );
                    fputcsv($fp, $data);
                    unset($data);
                }
            }
            rewind($fp);

            $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
            $this->s3->saveContent($filename, $fp);
            $filepath = $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime
        }

        $this->_sendMail($filepath);
    }

    private function _product_type()
    {
        // exclude product
        $excl_pid = $this->_getExcludeProduct();

        $total = [];
        $total_key = [];
        $fr_date = $this->start_date;
        $loop = true;
        do {
            if ($this->report_type == 1) {
                $to_date = date("Y-m-d 23:59:59", strtotime($fr_date));
                $key = date("Ymd", strtotime($fr_date));
                $key_val = date("Y-m-d", strtotime($fr_date));
            } else {
                $to_date = date("Y-m-t 23:59:59", strtotime($fr_date));
                $key = date("Ym", strtotime($fr_date));
                $key_val = date("M Y", strtotime($fr_date));
            }

            if (strtotime($to_date) >= strtotime($this->end_date)) {
                $to_date = $this->end_date;
                $loop = false;
            }

            if (!isset($total_key[$key])) {
                $total_key[$key] = $key_val;
            }

            $res = Yii::$app->db_slave_offgamers->createCommand("SELECT o.orders_id
                    FROM orders AS o
                    LEFT JOIN orders_status_stat AS oss
                    ON oss.orders_id = o.orders_id
                        AND oss.orders_status_id = 7
                    WHERE o.orders_status IN (7, 2, 3, 8)
                        AND oss.first_date >= '" . $fr_date . "'
                        AND oss.first_date <= '" . $to_date . "'")
                ->queryAll();
            foreach ($res as $row) {
                $_row1 = Yii::$app->db_slave_offgamers->createCommand("SELECT op.orders_products_id, op.products_id, op.custom_products_type_id, p.products_type, 
                        SUM(op.final_price * op.products_quantity) as total_purchase_amount, 
                        SUM(op.products_good_delivered_price) AS total_delivered_amount, 
                        SUM(op.products_canceled_price) AS total_canceled_amount, 
                        SUM(op.products_reversed_price) AS total_reversed_amount 
                    FROM orders_products AS op 
                    LEFT JOIN products AS p
                        ON p.products_id = op.products_id
                    WHERE op.orders_id = " . $row["orders_id"] . " 
                        AND op.parent_orders_products_id = 0 
                        AND op.orders_products_is_compensate = 0 
                        AND op.custom_products_type_id = 2")
                    ->queryOne();
                if (isset($_row1["orders_products_id"]) && ($_row1["custom_products_type_id"] == 2)) {
                    if (!in_array($_row1["products_id"], $excl_pid)) {
                        $pg_fee = 0;
                        $_row2 = Yii::$app->db_slave_offgamers->createCommand("SELECT ot.value
                                FROM orders_total AS ot
                                WHERE ot.orders_id = " . $row["orders_id"] . "
                                    AND ot.class = 'ot_surcharge'")
                            ->queryOne();
                        if (isset($_row2["value"])) {
                            $pg_fee = $_row2["value"];
                        }

                        $type = $_row1["products_type"];
                        if ($type == 0 || $type == 1) {
                            $type = 1;
                            // DTU
                            $_row3 = Yii::$app->db_slave_offgamers->createCommand("SELECT orders_products_id 
                                    FROM orders_products_extra_info
                                        WHERE orders_products_id = " . $_row1["orders_products_id"] . "
                                            AND orders_products_extra_info_key = 'delivery_mode'
                                            AND orders_products_extra_info_value = '6'")
                                ->queryOne();
                            if (isset($_row3["orders_products_id"])) {
                                $type = 0;
                            }
                        }

                        if (!isset($total[$type][$key])) {
                            $total[$type][$key] = array(
                                "transaction" => 0,
                                "pg_fee" => 0,
                                "amount" => 0,
                                "delivered" => 0,
                                "cancel" => 0,
                                "reversed" => 0,
                            );
                        }

                        $total[$type][$key]["transaction"] += 1;
                        $total[$type][$key]["pg_fee"] += $pg_fee;
                        $total[$type][$key]["amount"] += $_row1["total_purchase_amount"];
                        $total[$type][$key]["delivered"] += $_row1["total_delivered_amount"];
                        $total[$type][$key]["cancel"] += $_row1["total_canceled_amount"];
                        $total[$type][$key]["reversed"] += $_row1["total_reversed_amount"];

                    }
                }
            }

            if ($this->report_type == 1) {
                $fr_date = date("Y-m-d 00:00:00", strtotime($to_date . " +1 day"));
            } else {
                $fr_date = date("Y-m-01 00:00:00", strtotime($to_date . " +1 month"));
            }
        } while ($loop);

        $filepath = "";
        if ($total) {
            $filename = "sales/" . date("Ymd", strtotime($this->start_date)) . "_" . date("Ymd", strtotime($this->end_date)) . "_" . date("YmdHis") . ".csv";
            $fp = fopen('php://temp', 'w+');

            $header = [
                "Transaction",
                "AOV (USD)",
                "PG Fee",
                "Amount (USD)",
                "Delivered (USD)",
                "Cancel (USD)",
                "Reversed (USD)"
            ];

            $cpt = Yii::$app->params["product.type"];
            foreach ($cpt as $cpt_id => $cpt_name) {
                $data[] = $cpt_name;
                fputcsv($fp, array_merge($data, $header));

                foreach ($total_key as $key => $key_val) {
                    if (isset($total[$cpt_id][$key])) {
                        $data = array(
                            $key_val,
                            $total[$cpt_id][$key]["transaction"],
                            ($total[$cpt_id][$key]["transaction"] > 0 ? ($total[$cpt_id][$key]["amount"] / $total[$cpt_id][$key]["transaction"]) : 0),
                            $total[$cpt_id][$key]["pg_fee"],
                            $total[$cpt_id][$key]["amount"],
                            $total[$cpt_id][$key]["delivered"],
                            $total[$cpt_id][$key]["cancel"],
                            $total[$cpt_id][$key]["reversed"]
                        );
                    } else {
                        $data = array($key_val);
                    }
                    fputcsv($fp, $data);
                    unset($data);
                }

                fputcsv($fp, array());
            }
            rewind($fp);

            $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
            $this->s3->saveContent($filename, $fp);
            $filepath = $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime
        }

        $this->_sendMail($filepath);
    }

    private function _getExcludeProduct()
    {
        $product = [];
        if ($this->excl_cat) {
            foreach ($this->excl_cat as $num => $cid) {
                $res = Products::find()->select(['products_id'])
                    ->where(['like', 'products_cat_id_path', '_' . $cid . '_'])
                    ->asArray()->all();
                foreach ($res as $row) {
                    $product[] = $row["products_id"];
                }

                $m_cat = CategoriesDescription::find()
                    ->select(['categories_name'])
                    ->where(['categories_id' => $cid, 'language_id' => 1])
                    ->one();
                $this->_excl_catname[] = $m_cat->categories_name;
            }
        }

        return $product;
    }

    private function _sendMail($filepath) {
        $content = 'Start Date : ' . $this->start_date . "\n" .
            'End Date : ' . $this->end_date . "\n" .
            'Report : ' . ($this->_report[$this->report] ?? "N/A") . "\n" .
            'Report Type : ' . ($this->_report_type[$this->report_type] ?? "N/A") . "\n" .
            'Exclude Category : ' . (count($this->_excl_catname) ? implode(", ", $this->_excl_catname) : "N/A") . "\n\n";

        $subject = "Sales Report " . $this->start_date . " - " . $this->end_date;
        if ($filepath) {
            $content .= "The following document valid to download 5 days from the date of issue : \n" . $filepath;
        } else {
            $content .= "No record found";
        }

        $recipient = explode(",", $this->recipient);
        foreach ($recipient as $num => $val) {
            Yii::$app->mailer->compose()
                ->setFrom(Yii::$app->params["noreply"]["default"])
                ->setReplyTo(Yii::$app->params["noreply"]["default"])
                ->setTo(trim($val))
                ->setSubject($subject)
                ->setTextBody($content)
                ->send();
        }
    }
}
