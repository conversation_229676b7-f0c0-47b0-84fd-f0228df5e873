<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\StaticPage;
use common\models\StaticPageContent;
use common\models\StaticPageContentHistory;
use common\models\StaticPageType;

class StaticPageForm extends Model {

    public $title, $content, $language_id, $staticPageId, $staticPageContentPost, $static_page_type_id;
    public $staticPage = false, $staticPageContent = false;
    public $isNewRecord;

    public function __construct($staticPageId = '') {
        if (isset($staticPageId) && !empty($staticPageId)) {
            $this->getStaticPageInfo($staticPageId);
        } else {
            $this->staticPage = new StaticPage;
        }
    }

    public function rules() {
        return [
            [['title', 'content', 'static_page_type_id'], 'required', 'on' => 'create_new'],
            [['title', 'content', 'static_page_type_id'], 'required', 'on' => 'static_page_update'],
        ];
    }

    public function validateContent() {
        $mainContent = $this->staticPageContentPost[1]['content'];
        if (!strip_tags($mainContent)) {
            return false;
        }
        return true;
    }

    public function setStaticPageContentData() {
        foreach ($this->staticPageContentPost as $languageId => $value) {
            $this->title[$languageId] = (!empty($value['title'])) ? $value['title'] : $this->staticPageContentPost[1]['title'];
            $this->content[$languageId] = (!empty($value['content'])) ? $value['content'] : $this->staticPageContentPost[1]['content'];
        }
    }

    public function getStaticPageInfo($staticPageId) {
        $this->staticPageId = $staticPageId;
        $this->staticPage = StaticPage::findOne($staticPageId);
        $language = Yii::$app->enum->getLanguage('listData');
        foreach ($language as $i => $v) {
            $staticPageContent = $this->getStaticPageContentByLang($this->staticPageId, $i);
            $this->title[$i] = $staticPageContent->title;
            $this->content[$i] = $staticPageContent->content;
            $this->staticPageContent[$i] = ['title' => $staticPageContent->title, 'content' => $staticPageContent->content];
        }
        $this->static_page_type_id = $this->staticPage->static_page_type_id;
    }

    public function getStaticPageContentByLang($staticPageId, $lang) {
        return StaticPageContent::findOne(['static_page_id' => $staticPageId, 'language_id' => $lang]);
    }

    public function search($params) {
        $query = StaticPage::find();
        $query->orderBy("static_page_id ASC");

        $this->load($params);
        if ($this->validate()) {
            $query->andFilterWhere([
                'title' => $this->title,
            ]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public function save() {
        $formUpdateStatus = false;

        if ($this->scenario == 'create_new') {
            $dbAction = 'save';
            $textStatusA = 'created';
            $textStatusB = 'creating';
        } else {
            $dbAction = 'update';
            $textStatusA = 'updated';
            $textStatusB = 'updating';
        }

        $this->setStaticPageContentData();

        if ($this->validate() && $this->validateContent()) {
            $this->staticPage->static_page_type_id = $this->static_page_type_id;
            $this->staticPage->title = $this->staticPageContentPost[1]['title'];

            if ($this->staticPage->$dbAction()) {
                $this->staticPageId = $this->staticPage->static_page_id;

                $this->updateStaticPageContent();
                Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                $formUpdateStatus = true;
            } else {
                if ($this->staticPage->getErrors()) {
                    $errorString = $this->errorString($model->getErrors());
                    Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . $errorString);
                } else {
                    $this->updateStaticPageContent();
                    Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
                    $formUpdateStatus = true;
                }
            }
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . "Incomplete Form");
        }

        return $formUpdateStatus;
    }

    public function updateStaticPageContent() {
        $staticPageContentValidation = false;
        $staticPageContentError = [];

        $language = Yii::$app->enum->getLanguage('listData');
        foreach ($language as $i => $v) {
            // get current language
            $staticPageContent = $this->getStaticPageContentByLang($this->staticPageId, $i);
            // If not found add new
            if (!$staticPageContent) {
                $staticPageContent = new StaticPageContent;
                $staticPageContent->static_page_id = $this->staticPageId;
                $staticPageContent->language_id = $i;
            }
            // set attributes
            $staticPageContent->title = $this->title[$i];
            $staticPageContent->content = $this->content[$i];

            // validate attributes
            if ($staticPageContent->isNewRecord) {
                $updateStatus = $staticPageContent->save();
            } else {
                $updateStatus = $staticPageContent->update();
            }

            if ($updateStatus) {
                if ($i == 1) {
                    $staticPageContentValidation = true;
                } elseif ($i > 1 && $staticPageContentValidation == true) {
                    $staticPageContentValidation = true;
                } else {
                    $staticPageContentValidation = false;
                }
            } else {
                $staticPageContentError[$v] = $staticPageContent->getErrors();
                $staticPageContentValidation = (empty($staticPageContent->getErrors())) ? true : false;
            }
        }

        // return error if form validation failed
        if ($staticPageContentValidation == false) {
            $errorMsg = '';
            foreach ($staticPageContentError as $key => $value) {
                $errorMsg .= $key . '&nbsp;-&nbsp;';
                foreach ($value as $param => $text) {
                    $errorMsg .= json_encode($text) . '&nbsp;';
                }
                $errorMsg .= '<br>';
            }
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . "Missing input" . "<br />" . json_encode($staticPageContentValidation) . '<br />' . $errorMsg);
        } else {
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
        }
    }

    public function delete() {
        $model = StaticPageType::findOne(['static_page_type_id' => $this->static_page_type_id]);
        if ($model->type_status == 1) {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_WARNING") . "<br />" . $model->type_title . " is mandatory static content type.");
            return false;
        }

        if (StaticPageContent::deleteAll(['static_page_id' => $this->staticPageId])) {
            $this->staticPage->delete();
            return true;
        }

        Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
        return false;
    }

    # Static Page Type Sections

    public function searchType() {
        $query = StaticPageType::find();
        $query->orderBy("static_page_type_id ASC");

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public function getStaticPageType() {
        $includeUsage = false;

        $types = StaticPageType::find()->asArray()->all();
        $usage = StaticPage::find()
                ->select('static_page_type_id')
                ->groupBy('static_page_type_id')
                ->createCommand()
                ->queryAll();

        if ($this->staticPageId) {
            $includeUsage = StaticPage::findOne($this->staticPageId);
        }

        $typeArray = [];
        foreach ($types as $type) {
            $typeArray[$type['static_page_type_id']] = $type['type_title'];
        }

        foreach ($usage as $type) {
            if (!$includeUsage) {
                unset($typeArray[$type['static_page_type_id']]);
            } elseif ($includeUsage && $includeUsage->static_page_type_id != $type['static_page_type_id']) {
                unset($typeArray[$type['static_page_type_id']]);
            }
        }

        return $typeArray;
    }

    protected function errorString($error) {
        $errorString = '';
        if ($error) {
            $errorString = '&nbsp;:<br>';
            foreach ($error as $key => $value) {
                foreach ($value as $param => $text) {
                    $errorString .= $text;
                }
                $errorString .= '<br>';
            }
        }
        return $errorString;
    }

}
