<?php

namespace backend\models;

use DateTime;
use Yii;
use yii\base\Model;
use yii\base\Exception;
use yii\helpers\Json;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\NotFoundHttpException;
use common\models\Customers;
use common\components\AdminCom;
use common\components\LogFilesCom;

class StoreCreditForm extends Model
{

    public $transaction_type, $amount, $comment, $show_customer, $currency, $to_currency, $to_amount, $conversion_rate;
    public $report_type, $transaction_id, $orders_id, $reference_id, $customers_id, $start_date, $end_date, $activity, $list_type, $snapshot_date;
    public $requesting_id, $requesting_role, $customers_name, $scList = [], $scCount = 1, $scPageSize = 10, $scPage = 1, $user_status;
    public $sort = 'DESC';
    public $errors = false;

    public function load($data, $formName = null)
    {
        $sourceName = $formName;
        if (!$formName) {
            $formName = $this->formName();
            if($sourceName === ''){
                $data[$formName] = $data;
            }
        }

        if (empty($data[$formName])) {
            return false;
        }

        if (!empty($data[$formName])) {
            $invalid_characters = ['"', "'", "(", ")", "{", "}"];
            foreach (array_keys($data[$formName]) as $attr) {
                if (!empty($data[$formName][$attr])) {
                    // validate the html tags is attributes
                    $data[$formName][$attr] = str_replace($invalid_characters, '', strip_tags(\yii\helpers\HtmlPurifier::process($data[$formName][$attr])));
                }
            }
        }

        return parent::load($data, $formName);

    }

    // function to validate the  XSS(cross-site-scripting ).
    public function beforeValidate()
    {

        $invalid_characters = ['"', "'", "(", ")", "{", "}"];
        foreach (array_keys($this->getAttributes()) as $attr) {
            if (!empty($this->$attr)) {
                // validate the html tags is attributes
                $this->$attr = str_replace($invalid_characters, '', strip_tags(\yii\helpers\HtmlPurifier::process($this->$attr)));
            }
        }

        return parent::beforeValidate();// to keep parent validator available
    }

    public function rules()
    {
        return [
            [['comment'], 'string', 'max' => 255],
            [['transaction_id', 'orders_id', 'reference_id', 'customers_id', 'activity'], 'string', 'max' => 125],
            [['amount', 'to_amount', 'conversion_rate'], 'double'],
            [['customers_id', 'report_type'], 'integer'],
            [['transaction_type', 'currency', 'to_currency', 'list_type'], 'string'],
            [['show_customer'], 'string', 'max' => 1, 'message' => 'Include in customers store credit history'],
            [['currency', 'amount', 'comment', 'transaction_type'], 'required', 'on' => 'transaction_credit'],
            [['to_currency', 'comment', 'transaction_type'], 'required', 'on' => 'convert_credit'],
            ['customers_id', 'either', 'skipOnEmpty' => false, 'params' => ['other' => 'transaction_id'], 'on' => 'by_customer'],
            [['customers_id', 'transaction_id', 'amount'], 'filter', 'filter' => 'trim'],
            [['start_date', 'end_date'], 'required', 'on' => ['by_customer', 'sc_movement']],
            [['start_date', 'end_date', 'snapshot_date'], 'date', 'format' => 'php:Y-m-d'],
            ['end_date', 'compare', 'compareAttribute' => 'start_date',
                'operator' => '>=', 'message' => 'End date must not be BEFORE the Start date'],
            [['start_date', 'end_date'], 'validateDates'],
            [['customers_id'], 'required', 'on' => ['sc_activate', 'sc_suspend']],
        ];
    }

    public function either($attribute, $params)
    {
        if (!empty($this->$attribute)) {
            return;
        }

        if (!is_array($params['other'])) {
            $params['other'] = [$params['other']];
        }

        foreach ($params['other'] as $field) {
            if (!empty($this->$field)) {
                return;
            }
        }

        $fieldsLabels = [$this->getAttributeLabel($attribute)];
        foreach ($params['other'] as $field) {
            $fieldsLabels[] = $this->getAttributeLabel($field);
        }

        $this->addError($attribute, Yii::t('store-credit', 'One of fields "{fieldList}" is required.', [
            'fieldList' => implode('"", "', $fieldsLabels),
        ]));
    }

    public function validateDates($attribute)
    {
        $startDateTime = new DateTime($this->start_date);
        $endDateTime = new DateTime($this->end_date);

        $interval = $startDateTime->diff($endDateTime);
        $dateSearchInterval = intval(Yii::$app->params['date.search.within']);
        $withinYear = ($dateSearchInterval / 12);

        if (($interval->y >= $withinYear) && ($interval->d > 0)) {
            $this->addError(
                $attribute,
                Yii::t('store-credit', 'Please choose within "{months}" months.', [
                'months' => $dateSearchInterval
            ]));
        }
    }

    // Check if customers info exist
    private function getCustomersInfo($customersId)
    {
        $custObj = Customers::findOne($customersId);

        if ($custObj) {
            $this->customers_id = $custObj->customers_id;
            $this->customers_name = $custObj->customers_firstname . ' ' . $custObj->customers_lastname;
            return $custObj;
        }

        return false;
    }

    public function storeCreditApi($requestType = '')
    {
        $response = false;
        $urlParams = '';
        $extraParams = '';
        $method = 'POST';

        switch (strtolower($requestType)) {
            case 'view':
                $apiEndpoint = 'store-credits/' . $this->customers_id;
                $method = 'GET';
                $extraParams = [
                    'checking_type' => 'BALANCE',
                ];
                break;
            case 'post':
                $apiEndpoint = 'store-credits';
                $extraParams = [
                    'reference_id' => (isset($this->reference_id) && !empty($this->reference_id)) ? $this->reference_id : $this->customers_id,
                    'customers_id' => $this->customers_id,
                    'customers_role' => 'customers',
                    'transaction_type' => ($this->transaction_type == 'add') ? 'ADD_CREDIT' : 'DEDUCT_CREDIT',
                    'requesting_id' => $this->requesting_id,
                    'requesting_role' => $this->requesting_role,
                    'amount' => floatval($this->amount),
                    'total_amount' => floatval($this->amount),
                    'currency' => $this->currency,
                    'activity' => $this->activity,
                    'message' => $this->comment,
                    'show_customer' => strval($this->show_customer),
                    'allow_negative' => ($this->transaction_type == 'add') ? 0 : 1,
                ];

                // If from batch processing failed Extra SC from an order
                if (isset($this->orders_id) && !empty($this->orders_id)) {
                    $extraParams['orders_id'] = $this->orders_id;
                }
                break;
            case 'put':
                $apiEndpoint = 'store-credits/' . $this->customers_id;
                $method = 'PUT';

                if ($this->scenario == 'sc_activate' || $this->scenario == 'sc_suspend') {
                    $extraParams = [
                        'customers_role' => 'customers',
                        'transaction_type' => ($this->scenario == 'sc_activate') ? 'SC_ACTIVATE' : 'SC_SUSPEND',
                        'requesting_id' => $this->requesting_id,
                        'requesting_role' => $this->requesting_role,
                    ];
                } else {
                    $extraParams = [
                        'customers_role' => 'customers',
                        'transaction_type' => 'CONVERT_CREDIT',
                        'requesting_id' => $this->requesting_id,
                        'requesting_role' => $this->requesting_role,
                        'currency' => $this->to_currency,
                        'conversion_rate' => floatval($this->conversion_rate),
                        'activity' => $this->activity,
                        'message' => $this->comment,
                        'show_customer' => strval($this->show_customer),
                    ];
                }
                break;
            default:
                $apiEndpoint = 'store-credits';
                $method = 'GET';
                if (isset($this->list_type)) {
                    $extraParams = [
                        'list_type' => $this->list_type,
                        'snapshot_date' => strtotime($this->snapshot_date),
                    ];
                } else {
                    $extraParams = [
                        'request_id' => $this->transaction_id,
                        'customers_id' => $this->customers_id,
                        'list_type' => 'TRANSACTION',
                        'start_date' => strtotime($this->start_date),
                        'end_date' => (strpos($this->end_date, ":") ? strtotime($this->end_date) : strtotime('+1 day', strtotime($this->end_date))),
                        'activity' => $this->activity,
                        'page' => intval($this->scPage),
                        'limit' => intval($this->scPageSize),
                        'sort' => $this->sort,
                    ];
                }
                break;
        }

        // Clean data params if empty
        $extraParams = array_filter($extraParams, function ($value) {
            return ($value !== null && $value !== false && $value !== '');
        });

        $client = new \GuzzleHttp\Client([
            'base_uri' => Yii::$app->params['micro.service.storecredit']['baseUrl'] . '/',
            'verify' => Yii::$app->params['guzzel.verifySsl'],
        ]);

        $extraParams['source'] = Yii::$app->params['micro.service.storecredit']['key'];
        $extraParams['time'] = time();
        $extraParams['signature'] = md5(Yii::$app->params['micro.service.storecredit']['key'] . $extraParams['time'] . "|" . Yii::$app->params['micro.service.storecredit']['secret']);

        // Use retry method for curl to microservice
        $i = 0;
        while ($i++ < 3) {
            try {
                $response = $client->request($method, $apiEndpoint . $urlParams, ['json' => $extraParams]);
                return Json::decode($response->getBody());
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                $subject = Yii::t('store-credit', 'Store Credit Microservice');
                $response = $e->getResponse();
                $errorMessage = $response->getStatusCode();
                Yii::$app->slack->send($subject, [
                    [
                        'color' => 'warning',
                        'title' => Yii::t('store-credit', 'Request Type') . '-' . $requestType,
                        'text' => $errorMessage,
                        'ts' => time()
                    ]
                ], 'DEBUG'
                );
            }
            // Delays the program execution
            if ($i < 3) {
                sleep($i);
            }
        }

        if (!$response) {
            $this->errors = true;
            return ['status' => false, 'error' => ['code' => $errorMessage]];
        }
    }

    public function search()
    {
        if (!empty($this->customers_id) && empty($this->transaction_id)) {
            $this->getBalance($this->customers_id);
        }

        $allModels = $this->getStoreCreditList();

        return new \yii\data\ArrayDataProvider([
            'allModels' => $allModels,
            'key' => 'request_id',
            'pagination' => [
                'pageSize' => $this->scPageSize,
            ],
        ]);
    }

    public function getStoreCreditList()
    {
        $arrayResult = $this->storeCreditApi();

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            throw new NotFoundHttpException;
        }

        if ($arrayResult['status'] == false) {
            $this->errors = true;
            $this->scList = '';
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . (isset($arrayResult['error']['code']) ? $arrayResult['error']['code'] : $arrayResult['error']));
        }

        $this->scList = (isset($arrayResult['result']['Items'])) ? $arrayResult['result']['Items'] : '';

        if ($this->report_type == 1) {
            if ($this->scList) {
                $custObj = $this->getCustomersInfo($this->scList[0]['user_id']);
                $this->getBalance($this->scList[0]['user_id']);
            } else {
                $custObj = $this->getCustomersInfo($this->customers_id);

                if ($custObj && !empty($this->transaction_id)) {
                    $this->addError('transaction_id', Yii::t('store-credit', 'Transaction Not Found'));
                } elseif (!empty($this->transaction_id) && !empty($this->customers_id) && !$custObj) {
                    $this->addError('customers_id', Yii::t('store-credit', 'Customer Not Found'));
                    $this->addError('transaction_id', Yii::t('store-credit', 'Transaction Not Found'));
                } elseif (!empty($this->transaction_id)) {
                    $this->addError('transaction_id', Yii::t('store-credit', 'Transaction Not Found'));
                } elseif (!$custObj) {
                    $this->addError('customers_id', Yii::t('store-credit', 'Customer Not Found'));
                }
            }
        }

        if (isset($arrayResult['result']['Page'])) {
            // incase the wrong page is shown and empty
            if ($arrayResult['result']['Page']['total_page'] != 0 && $arrayResult['result']['Page']['total_page'] < $arrayResult['result']['Page']['page']) {
                $this->scPage = $arrayResult['result']['Page']['total_page'];
                $arrayResult = $this->storeCreditApi();
                $this->scList = $arrayResult['result']['Items'];
            }
            // set default pages based on response
            $this->scCount = $arrayResult['result']['Page']['total_record'];
            $this->scPageSize = $arrayResult['result']['Page']['limit'];
            $this->scPage = $arrayResult['result']['Page']['page'];
        }

        return $this->scList;
    }

    public function getAllTransaction()
    {
        $this->scPage = 1;
        $this->scPageSize = 200;

        $arrayResult = $this->storeCreditApi();

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            throw new NotFoundHttpException;
        }

        if ($arrayResult['status']) {
            $allListArray = $arrayResult['result']['Items'];

            if (isset($arrayResult['result']['Page'])) {
                if ($arrayResult['result']['Page']['total_page'] > 1) {
                    for ($i = 2; $i <= $arrayResult['result']['Page']['total_page']; $i++) {
                        $this->scPage = $i;
                        $arrayResult = $this->storeCreditApi();
                        $allListArray = array_merge($allListArray, $arrayResult['result']['Items']);
                    }
                }
            }

            return $allListArray;
        } else {
            $this->errors = true;
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . (isset($arrayResult['error']['code']) ? $arrayResult['error']['code'] : $arrayResult['error']));
            return false;
        }
    }

    public function getBalance($customersId)
    {
        $this->customers_id = $customersId;
        $arrayResult = $this->storeCreditApi('view');
        if (isset($arrayResult['status']) && $arrayResult['status']) {
            $this->currency = $arrayResult['result']['Items']['currency'];
            $this->amount = $arrayResult['result']['Items']['balance'];
            $this->user_status = (isset($arrayResult['result']['Items']['user_status'])) ? $arrayResult['result']['Items']['user_status'] : 'NA';
        } elseif (isset($arrayResult['error']['code']) && $arrayResult['error']['code'] == 14) {
            $this->user_status = 'ACTIVE';
        }
    }

    public function save()
    {
        $creditLimit = AdminCom::checkAdminCreditLimit($this->amount, $this->currency);
        if (!$creditLimit['status'] && $this->transaction_type == 'add') {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . $creditLimit['message']);
            return false;
        }

        if (!$this->getCustomersInfo($this->customers_id)) {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR"));
            return false;
        }

        if ($this->transaction_type == 'convert') {
            $arrayResult = $this->storeCreditApi('put');
        } else {
            $arrayResult = $this->storeCreditApi('post');
        }

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            throw new NotFoundHttpException();
        }

        if ($arrayResult['status']) {
            $this->transaction_id = $arrayResult['result']['request_id'];

            $subject = Yii::t('store-credit', 'Store Credit ') . $this->transaction_type . ' - ' . date("F j, Y H:i");
            $message = Yii::t('store-credit', 'Customer ID : ') . $this->customers_id . "\n";
            $message .= Yii::t('store-credit', 'Transaction ID : ') . $this->transaction_id . "\n";
            $message .= "\n";
            $message .= Yii::t('store-credit', 'Changed by : ') . $this->requesting_id;

            Yii::$app->slack->send(
                $subject, [
                [
                    'color' => 'warning',
                    'title' => Yii::t('store-credit', 'Manual ') . $this->transaction_type . ' - ' . $this->currency . $this->amount,
                    'title_link' => Url::to(['index', 'transaction_id' => $this->transaction_id, 'start_date' => $this->start_date], true),
                    'text' => $message,
                    'ts' => time()
                ]
            ], 'OGP'
            );
            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return true;
        } else {
            // Reverse Admin Credit Limit Deduction
            AdminCom::reverseAdminCreditLimit($this->amount, $this->currency);
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . $arrayResult['error']['message']);
            return false;
        }
    }

    public function getOpenCloseBalance($snapshotDate, $type = 'open')
    {
        $this->snapshot_date = $snapshotDate;
        $this->list_type = ($type == 'open') ? 'OPENBALANCE' : 'CLOSEBALANCE';

        $arrayResult = $this->storeCreditApi();

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            throw new NotFoundHttpException;
        }

        $this->scList = (isset($arrayResult['result']['Items'])) ? $arrayResult['result']['Items'] : '';

        $balanceTable = '';
        // Create html table for the list
        $balanceTable .= '<div id="w0" class="grid-view">';
        $balanceTable .= '<table class="table table-striped table-bordered">';
        $balanceTable .= '<thead><tr>';
        $balanceTable .= '<th style="vertical-align:top;">Date/Time</th>';
        $balanceTable .= '<th style="text-align:center; vertical-align:top;">Currency</th>';
        $balanceTable .= '<th style="text-align:center; vertical-align:top;">Balance</th>';
        $balanceTable .= '</tr></thead><tbody>';

        // Loop resul to generate full list
        if ($this->scList) {
            foreach ($this->scList as $value) {
                $balanceTable .= '<tr>';
                $balanceTable .= '<td>' . $value['snapshot_date'] . '</td>';
                $balanceTable .= '<td style="text-align:center; vertical-align:top;">' . $value['currency'] . '</td>';
                $balanceTable .= '<td style="text-align:center; vertical-align:top;">' . $value['balance'] . '</td>';
                $balanceTable .= '</tr>';
            }
        } else {
            $balanceTable .= '<td colspan="11"><div class="empty">No results found.</div></td>';
        }

        $balanceTable .= '</tbody></table></div>';

        return $balanceTable;
    }

    public function updateAccountStatus($customersId)
    {
        if (!$this->getCustomersInfo($this->customers_id)) {
            return ['error' => Yii::t('store-credit', 'Customer Not Found')];
        }

        $arrayResult = $this->storeCreditApi('put');

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            throw new NotFoundHttpException();
        }

        if ($arrayResult['status']) {
            $subject = Yii::t('store-credit', 'Store Credit Account Status Update to ') . (($this->scenario == 'sc_activate') ? 'Active' : 'Suspended') . ' - ' . date("F j, Y H:i");
            $message = Yii::t('store-credit', 'Customer ID : ') . $this->customers_id . "\n";
            $message .= "\n";
            $message .= Yii::t('store-credit', 'Changed by : ') . $this->requesting_id;

            Yii::$app->slack->send(
                $subject, [
                [
                    'color' => 'warning',
                    'title' => Yii::t('store-credit', 'Account ') . (($this->scenario == 'sc_activate') ? 'Active' : 'Suspended'),
                    'text' => $message,
                    'ts' => time()
                ]
            ], 'OGP'
            );

            $logFiles = new LogFilesCom($this->requesting_id);
            $logFiles->insertCustomerHistoryLog($this->customers_id, $subject);
            unset($logFiles);

            Yii::$app->session->setFlash("success", Yii::t("site", "FLASH_SUCCESS"));
            return ['success' => $arrayResult['result']['message'], 'sc_status' => (($this->scenario == 'sc_activate') ? 'suspend' : 'activate')];
        } else {
            Yii::$app->session->setFlash("error", Yii::t("site", "FLASH_ERROR") . "<br />" . $arrayResult['error']['message']);
            return ['error' => $arrayResult['error']['message']];
        }
    }

}
