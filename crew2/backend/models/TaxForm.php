<?php

namespace backend\models;

use common\models\OrdersTaxConfiguration;
use common\models\OrdersTaxConfigurationDescription;
use common\models\OrdersTaxConfigurationLog;
use common\models\OrdersTaxCustomers;
use common\models\OrdersTaxCustomersHistory;
use common\models\Customers;
use common\models\Countries;
use common\models\Currencies;
use Yii;
use yii\base\Model;
use yii\helpers\Url;
use yii\helpers\HtmlPurifier;
use yii\db\ActiveQuery;
use yii\data\ActiveDataProvider;

class TaxForm extends Model
{

    public $orders_tax_id;
    public $country_code;
    public $country_name;
    public $currency;
    public $orders_tax_percentage;
    public $business_tax_percentage;
    public $orders_tax_status;
    public $orders_provide_invoice_status;
    public $business_tax_status;
    public $orders_include_reverse_charge;
    public $start_datetime;
    public $address_1;
    public $address_2;
    public $address_3;
    public $contact;
    public $website;
    public $tax_invoice_title;
    public $tax_registration_name;
    public $gst_registration_no;
    public $company_name;
    public $company_logo;
    public $business_tax_form;
    public $orders_tax_title;
    public $orders_tax_title_short;
    public $orders_tax_message;
    public $customers_id;
    public $business_name;
    public $business_tax_number;
    public $orders_tax_customers_status;
    public $remarks, $show_customer, $form_type; // customer detail page
    public $isNewRecord; // create new page

    // function to validate the  XSS(cross-site-scripting ).
    public function beforeValidate()
    {
        foreach (array_keys($this->getAttributes()) as $attr){
            if(!empty($this->$attr)){
                // validate the html tags is attributes
                if(!is_array($this->$attr)){
                    $this->$attr = strip_tags(HtmlPurifier::process($this->$attr));
                }
            }
        }

        return parent::beforeValidate();// to keep parent validator available
    }

    public function rules()
    {
        return [
            [
                'remarks',
                'filter',
                'filter' => function ($value) {
                    return HtmlPurifier::process($value);
                },
            ],
            [
                [
                    'country_code',
                    'currency',
                    'orders_tax_percentage',
                    'business_tax_percentage',
                    'orders_tax_status',
                    'start_datetime',
                    'tax_invoice_title',
                    'tax_registration_name',
                    'gst_registration_no',
                    'company_name',
                    'website',
                    'address_1',
                    'contact',
                    'orders_provide_invoice_status',
                    'orders_include_reverse_charge',
                    // orders_tax_configuration_description
                    'orders_tax_title',
                    'orders_tax_message',
                    'orders_tax_title_short',
                ],
                'required',
                'on' => 'setting',
            ],
            [['orders_tax_id', 'business_tax_status'], 'required', 'on' => 'business'],
            [['orders_tax_percentage', 'business_tax_percentage'], 'number', 'min' => 0],
            [
                ['tax_invoice_title', 'tax_registration_name', 'address_1', 'address_2', 'address_3'],
                'string',
                'max' => 64
            ],
            [['contact', 'website', 'gst_registration_no', 'company_name'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_tax_title' => 'Title',
            'country_code' => 'Location',
            'orders_tax_percentage' => 'Sales Tax',
            'business_tax_percentage' => 'Business Tax',
            'orders_tax_status' => 'Status',
            'business_tax_status' => 'Status',
            'start_datetime' => 'Start Date',
            'orders_tax_message' => 'Pre-Checkout Message',
            'tax_invoice_title' => 'Invoice Title',
            'tax_registration_name' => 'Tax Name',
            'orders_tax_title_short' => 'Tax Short Form',
            'gst_registration_no' => 'Tax Registration No',
            'company_name' => 'Company Name',
            'website' => 'Website',
            'address_1' => 'Address',
            'contact' => 'Contact',
            'orders_provide_invoice_status' => 'email invoice to customer',
            'orders_include_reverse_charge' => 'show Reverse Charge detail ( tax amount and deduction )',
        ];
    }

    public function getSetting($id)
    {
        $model = OrdersTaxConfiguration::findOne($id);
        $attr = $model->attributes();
        foreach ($attr as $i => $name) {
            $this->$name = $model->$name;
        }

        $m = OrdersTaxConfigurationDescription::findOne(['orders_tax_id' => $id, 'language_id' => 1]);
        if (isset($m->orders_tax_title)) {
            $this->orders_tax_title = $m->orders_tax_title;
            $this->orders_tax_title_short = $m->orders_tax_title_short;
        }
    }

    public function validatePreCheckoutMessage()
    {
        if (isset($this->orders_tax_message[1]) && !empty($this->orders_tax_message[1])) {
            return true;
        } else {
            $this->addError('*', 'Pre-Checkout English Message cannot be blank.');
        }
        return false;
    }

    public function process()
    {
        if ($this->validate() && $this->validatePreCheckoutMessage()) {
            if (!$this->orders_tax_id) {
                // new record
                $m_ctry = Countries::findOne(['countries_iso_code_2' => $this->country_code]);
                $model = new OrdersTaxConfiguration();
                $model->business_tax_form = '{"1":[{"id":"1","title":"Business Name","name":"input_business_name","mandatory":"1","sort_order":"10","error_message":"Input Error Message","type":"text_box","size":"30","max_char":"50","pre_text":"Pre-Text","post_text":"Post-Text","status":"1"},{"id":"2","title":"Tax Number","name":"input_tax_number","mandatory":"1","sort_order":"20","error_message":"Input Error Message","type":"text_box","size":"30","max_char":"50","pre_text":"Pre-Text","post_text":"Post-Text","status":"1"}]}';
            } else {
                // update
                $model = OrdersTaxConfiguration::findOne($this->orders_tax_id);
            }

            $attr = $model->attributes();
            foreach ($attr as $i => $name) {
                if (isset($this->$name)) {
                    $model->$name = $this->$name;
                }
            }

            if (!$this->orders_tax_id && isset($m_ctry->countries_name)) {
                $model->country_name = $m_ctry->countries_name;
            }

            if ($model->save()) {
                if (!$this->orders_tax_id) {
                    $model->refresh();
                    $model->orders_tax_id;
                }

                $language = Yii::$app->enum->getLanguage('listData');
                foreach ($language as $id => $val) {
                    if (isset($this->orders_tax_message[$id]) && !empty($this->orders_tax_message[$id])) {
                        $m = OrdersTaxConfigurationDescription::findOne([
                            'orders_tax_id' => $model->orders_tax_id,
                            'language_id' => $id
                        ]);
                        if (!isset($m->orders_tax_id)) {
                            // new record
                            $m = new OrdersTaxConfigurationDescription();
                            $m->orders_tax_id = $model->orders_tax_id;
                            $m->language_id = $id;
                        } else {
                            $m->isNewRecord = false;
                        }
                        $m->orders_tax_title = $this->orders_tax_title;
                        $m->orders_tax_title_short = $this->orders_tax_title_short;
                        $m->orders_tax_message = $this->orders_tax_message[$id];
                        $res = $m->save();
                        if (($id == 1) && ($m->isNewRecord == true) && !$res) {
                            return json_encode($m->getErrors());
                        }
                    }
                }

                $this->reportNotification("[CREW2] Tax Setting", $this->orders_tax_title,
                    Url::to(['tax/update', 'id' => $model->orders_tax_id]));
                return true;
            }
            return json_encode($model->getErrors());
        }
        return json_encode($this->getErrors());
    }

    public function deleteForm($field_id)
    {
        if ($this->business_tax_form) {
            $form = json_decode($this->business_tax_form, true);

            if ($field_id) {
                foreach ($form as $lang_id => $val) {
                    $count = count($val);
                    for ($i = 0; $count > $i; $i++) {
                        if ($val[$i]["id"] == $field_id) {
                            unset($form[$lang_id][$i]);
                            $form[$lang_id] = array_values($form[$lang_id]);
                            break;
                        }
                    }
                }
            }
        }

        $m = OrdersTaxConfiguration::findOne($this->orders_tax_id);
        $m->business_tax_form = json_encode($form);
        if ($m->save()) {
            return true;
        }

        return false;
    }

    public function processForm($input)
    {
        $field = [
            'title' => "",
            'sort_order' => 10,
            'error_message' => "",
            'type' => "",
            'pre_text' => "",
            'post_text' => "",
        ];

        foreach (array_keys($input) as $attr){
            if(!empty($input[$attr])){
                // validate the html tags is attributes
                $input[$attr] = strip_tags(HtmlPurifier::process($input[$attr]));
            }
        }

        // default field
        foreach ($field as $key => $val) {
            if (isset($input[$key])) {
                $field[$key] = $input[$key];
            } else {
                return "Missing default field " . $key;
            }
        }

        // field type mandatory input
        $rules = self::getFormTypeRules();
        if (isset($rules[$input['type']])) {
            foreach ($rules[$input['type']] as $i => $key) {
                if (isset($input[$key])) {
                    $field[$key] = $input[$key];
                } else {
                    return "Missing mandatory field " . $key;
                }
            }
        } else {
            return "Invalid input type " . $input['type'];
        }

        $field['name'] = 'input_' . str_replace(' ', '_', trim(strtolower($input['title'])));
        $field['status'] = 1;
        $field['mandatory'] = (isset($input['mandatory']) && $input['mandatory'] ? 1 : 0);

        if ($this->business_tax_form) {
            $append = false;
            $form = json_decode($this->business_tax_form, true);

            // update existing field
            if ($input['field_id']) {
                foreach ($form as $lang_id => $val) {
                    $count = count($val);
                    for ($i = 0; $count > $i; $i++) {
                        if ($val[$i]["id"] == $input['field_id']) {
                            $field['id'] = $input['field_id'];
                            $form[$lang_id][$i] = $field;
                            $append = true;
                            break;
                        }
                    }
                }
            }

            if (!$append) {
                $field['id'] = count($form[1]) + 1;
                $form[1][] = $field;
            }
        } else {
            $field['id'] = 1;
            $form[1][] = $field;
        }

        $m = OrdersTaxConfiguration::findOne($this->orders_tax_id);
        $m->business_tax_form = json_encode($form);
        if ($m->save()) {
            $this->reportNotification("[CREW2] Business Tax Application Form", $m->orders_tax_title,
                Url::to(['tax/business', 'id' => $this->orders_tax_id]));
            return true;
        }

        return false;
    }

    public static function getFormTypeList()
    {
        return [
            "text_box" => "Text Box",
            "text_area" => "Text Area",
            "dropdown_menu" => "Dropdown Menu",
            "radio_button" => "Radio Button",
            "date_selection" => "Date Selection",
            "display_label" => "Display Label",
            "information_text" => "Information Text",
        ];
    }

    public static function getFormType($type)
    {
        $list = self::getFormTypeList();
        return $list[$type] ? $list[$type] : '';
    }

    public static function getFormTypeRules()
    {
        return [
            "text_box" => ["size", "max_char"],
            "text_area" => ["row", "column"],
            "dropdown_menu" => ["options"],
            "radio_button" => ["options"],
            "date_selection" => ["date_from", "date_period"],
            "display_label" => [],
            "information_text" => ["options"],
        ];
    }

    public function getTaxDescriptionByLang($taxId, $lang)
    {
        return OrdersTaxConfigurationDescription::findOne(['orders_tax_id' => $taxId, 'language_id' => $lang]);
    }

    public function search($params)
    {
        $query = OrdersTaxConfiguration::find()->alias('otc');
        $query->select([
            'otc.orders_tax_id',
            'otc.country_name',
            'otc.currency',
            'otc.orders_tax_status',
            'otc.orders_tax_percentage',
            'otc.business_tax_percentage',
        ]);
        $query->orderBy("otc.country_name ASC");

        if ($params) {
            $this->country_name = (isset($params['country']) ? $params['country'] : "");
            $this->currency = (isset($params['currency']) ? $params['currency'] : "");
            $this->orders_tax_percentage = (isset($params['sales_tax']) ? $params['sales_tax'] : "");
            $this->business_tax_percentage = (isset($params['business_tax']) ? $params['business_tax'] : "");

            $query->andWhere(['like', 'otc.country_name', $this->country_name]);
            $query->andWhere(['like', 'otc.currency', $this->currency]);
            $query->andWhere(['like', 'otc.orders_tax_percentage', $this->orders_tax_percentage]);
            $query->andWhere(['like', 'otc.business_tax_percentage', $this->business_tax_percentage]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public function searchApplication($params)
    {
        $query = OrdersTaxCustomers::find();

        if ($params) {
            $this->customers_id = (isset($params['customers_id']) ? $params['customers_id'] : "");
            $this->business_name = (isset($params['business_name']) ? $params['business_name'] : "");
            $this->business_tax_number = (isset($params['tax_number']) ? $params['tax_number'] : "");
            $this->country_name = (isset($params['country_name']) ? $params['country_name'] : "");
            $this->orders_tax_customers_status = (isset($params['status']) ? $params['status'] : "");

            if ($this->customers_id) {
                $query->andWhere(['=', 'customers_id', $this->customers_id]);
            }
            if ($this->orders_tax_customers_status != "") {
                $query->andWhere(['=', 'orders_tax_customers_status', $this->orders_tax_customers_status]);
            }

            $query->andWhere(['like', 'business_name', $this->business_name]);
            $query->andWhere(['like', 'business_tax_number', $this->business_tax_number]);
            $query->andWhere(['like', 'country_name', $this->country_name]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public static function getApplicationStatusList()
    {
        return [
            0 => 'Pending',
            1 => 'Verified',
            2 => 'Fail',
            3 => 'Canceled',
        ];
    }

    public static function getApplicationStatus($type)
    {
        $list = self::getApplicationStatusList();
        return (isset($list[$type]) ? $list[$type] : "");
    }

    public function processApplication($input)
    {
        $m = OrdersTaxCustomers::findOne(['id' => $input['id']]);
        if (isset($m->id)) {
            if (isset($input['status'])) {
                $m->orders_tax_customers_status = $input['status'];
            }
            $m->updated_at = time();

            $data = json_decode($m->orders_tax_customers_data, true);
            if (!empty($input['remarks'])) {
                $data['remarks'] = strip_tags(HtmlPurifier::process($input['remarks']));
            }
            $data['updated_at'] = $m->updated_at;
            $data['updated_by'] = Yii::$app->user->identity->username;
            $data['show_customer'] = (isset($input['show_customer']) && $input['show_customer'] ? 1 : 0);

            $m->orders_tax_customers_data = json_encode($data);
            if ($m->save()) {
                $this->reportNotification("[CREW2] Business Application", $m->customers_id,
                    Url::to(['tax/update', 'id' => $m->customers_id]));
                return true;
            }
        }

        return false;
    }

    public function reportNotification($subject, $title, $link)
    {
        Yii::$app->slack->send($subject, [
            [
                'color' => 'warning',
                'author_name' => Yii::$app->user->identity->username,
                'title' => $title,
                'title_link' => $link,
                'ts' => time(),
            ],
        ], 'TAX');
    }

    public function getTaxCountry()
    {
        if ($this->isNewRecord) {
            // Get country code used in tax
            $query = OrdersTaxConfiguration::find()->select(['country_code']);
            $command = $query->createCommand();
            $data = $command->queryAll();
            $taxCountryCodeArray = [];
            if ($data) {
                foreach ($data as $taxRow) {
                    $taxCountryCodeArray[] = $taxRow['country_code'];
                }
            }
            // filter out the used ones
            return array_diff_key(Yii::$app->country->getCountryList(), array_flip($taxCountryCodeArray));
        } else {
            return Yii::$app->country->getCountryList();
        }
    }

}
