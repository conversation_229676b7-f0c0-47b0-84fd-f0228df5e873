<?php

namespace backend\models;

use common\models\DeliverQueue;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\validators\EmailValidator;

class TaxReportForm extends Model
{

    public $start_date;
    public $end_date;
    public $country_code;
    public $adv_search, $adv_email_postfix;
    public $report_recipient;

    public function rules()
    {
        return [
            [['country_code', 'start_date', 'end_date', 'report_recipient'], 'required'],
            [['adv_search', 'adv_email_postfix', 'report_recipient'], 'string'],
            [['report_recipient'], 'validateMail'],
        ];
    }

    public function validateMail($attribute, $params)
    {

        $email = explode(",", $this->report_recipient);
        if ($email) {
            $err = [];
            $validator = new EmailValidator;

            foreach ($email as $val) {
                if (!$validator->validate(trim($val))) {
                    $err[] = $val;
                }
            }

            if ($err) {
                $this->addError($attribute, implode(", ", $err) . " is not a valid email.");
            }
        }
    }

    public function getTaxProfile()
    {
        $result = [];

        $res = Yii::$app->db_slave_offgamers
                ->createCommand('SELECT otc.country_code, otc.country_name, otcd.orders_tax_title_short
                FROM orders_tax_configuration AS otc
                INNER JOIN orders_tax_configuration_description AS otcd
                    ON otcd.orders_tax_id = otc.orders_tax_id
                WHERE otcd.language_id = 1
                ORDER BY otc.country_name')
                ->queryAll();
        foreach ($res as $row) {
            $result[$row["country_code"]] = $row["country_name"] . " - " . $row["orders_tax_title_short"];
        }
        return $result;
    }

    public function addDeliverQueue()
    {
          $extra = [
              'type' => 'tax',
              'params' => [
                  'start_date' => $this->start_date,
                  'end_date' => $this->end_date,
                  'country_code' => $this->country_code,
                  'adv_search' => boolval($this->adv_search),
                  'adv_email_postfix' => $this->adv_email_postfix,
                  'recipient' => $this->report_recipient
              ]
          ];

        $q = new DeliverQueue();
        $q->type = "REPORT";
        $q->id = 1;
        $q->extra_info = Json::encode($extra);
        $q->created_at = time();
        return $q->save();
    }
}
