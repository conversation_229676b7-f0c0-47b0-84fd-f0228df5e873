<?php

namespace backend\models\admin\form;

use common\models\LogSesBounceHistory;
use offgamers\base\models\EmailValidation;
use offgamers\base\models\LogSesBounce;
use yii\base\Model;

class EmailVerifyForm extends Model
{
    public $emailAddress;
    public $emailValidation, $sesBounce, $sesBounceHistory;
    public $isNewRecord, $errors = false;
    public function __construct($email = '')
    {
        if (isset($email) && !empty($email)) {
            $this->emailAddress = $email;
            $this->getCustomerByEmail($email);
        }
    }

    public function updateBlockAndVerifyStatus($input)
    {
        // update only if the email is already not validated
        $this->updateValidationStatus($input);

        // update only if the email is blocked
        $this->updateEmailBlockStatus($input);
        return true;
    }

    public function getCustomerByEmail($email)
    {
        $this->emailAddress = $email;
        $this->emailValidation = EmailValidation::findOne(['email' => $email]);
        $this->sesBounce = LogSesBounce::findOne(['email' => $email]);

        $this->sesBounceHistory = LogSesBounceHistory::find()->where(['email' => $email])
            ->all();
    }

    public function updateValidationStatus($input)
    {
        if (!empty($this->emailValidation) && isset($this->emailValidation->mx_found) && $this->emailValidation->mx_found == 0) {
            $this->emailValidation->mx_found = isset($input['valid_email']) ? $input['valid_email'] : 0;
            $this->emailValidation->save();

        }
        return true;
    }

    public function updateEmailBlockStatus($input)
    {
        if (isset($this->sesBounce) && !empty($this->sesBounce)) {

            $status = isset($input['email_blocked']) ? $input['email_blocked'] : 0;
            if ($status == 0) {
                $this->saveSESBounceHistory();
                $this->sesBounce->delete();
            }

        }
        return true;
    }

    private function saveSESBounceHistory()
    {

        // using new table
        $history = new LogSesBounceHistory();
        $history->email = $this->sesBounce->email;
        $history->bounce_type = $this->sesBounce->bounced_type;
        $history->bounce_sub_type = $this->sesBounce->bounced_sub_type;
        $history->error_string = $this->sesBounce->error_string;
        $history->save();

    }

}
