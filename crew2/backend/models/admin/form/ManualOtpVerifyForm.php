<?php

namespace backend\models\admin\form;

use common\models\Countries;
use common\models\OtpManual;
use Yii;
use yii\base\Model;
use yii\helpers\ArrayHelper;

class ManualOtpVerifyForm extends Model
{
    public $customer_hp;
    public $country_id;
    public $otpManuals;
    public $country;

    public function rules()
    {
        return [
            [['customer_hp'], 'number', 'message' => 'Mobile number must be number only'],
            [['country_id'], 'required', 'message' => 'Please select the country'],
            [['customer_hp'], 'required', 'message' => 'Please fill in mobile number'],
            [['country'], 'safe'],
        ];
    }

    public function getOtpManuals()
    {
        if (!$this->country_id) {
            return [];
        }

        $country = $this->getCountry($this->country_id);

        if (!$country) {
            return [];
        }

        $fullHp = $country['countries_international_dialing_code'] . $this->customer_hp;

        $this->otpManuals = OtpManual::find()
            ->select(['created_at', 'token', 'expired_at', 'remark', 'updated_by'])
            ->where([
                'country_id' => $this->country_id,
                'hp' => $fullHp,
            ])
            ->orderBy(['created_at' => SORT_DESC])
            ->asArray()
            ->all();
    }

    public function getCountries()
    {
        $cache = Yii::$app->cache;

        $countries = $cache->get('countries');

        if ($countries) {
            return $countries;
        }

        $data = [];

        $countries = Countries::find()
            ->select([
                'countries_id',
                'countries_name',
                'countries_international_dialing_code',
                'countries_display'
            ])
            ->where(['countries_display' => 1])
            ->orderBy(['countries_name' => SORT_ASC])
            ->asArray()
            ->all();

        foreach ($countries as $country) {
            $data[$country['countries_id']] = $country;
        }

        $cache->set('countries', $data);

        return $data;
    }

    public function getCountry($id)
    {
        $cache = Yii::$app->cache;

        $countries = $cache->get('countries');

        return ArrayHelper::getValue($countries, $id);
    }
}
