<?php

namespace backend\models\admin\form;

use Yii;
//use backend\models\admin\User;
use yii\base\Model;

class UserForm extends Model {

    public $id, $username, $first_name, $last_name, $email, $password, $status;

    // function to validate the  XSS(cross-site-scripting ).
    public function beforeValidate()
    {
        foreach (array_keys($this->getAttributes()) as $attr){
            if(!empty($this->$attr)){
                // validate the html tags is attributes
                $this->$attr = strip_tags(\yii\helpers\HtmlPurifier::process($this->$attr));
            }
        }

        return parent::beforeValidate();// to keep parent validator available
    }
    /**
     * @inheritdoc
     */

    public function rules() {
        return [
            ['first_name', 'filter', 'filter' => 'trim'],
            ['first_name', 'string', 'min' => 2, 'max' => 32],
            ['last_name', 'filter', 'filter' => 'trim'],
            ['last_name', 'string', 'min' => 2, 'max' => 32],
            ['email', 'filter', 'filter' => 'trim'],
            ['email', 'email'],
            ['email', 'unique', 'targetClass' => 'backend\models\admin\User', 'message' => 'This email address has already been taken.', 'on' => 'create'],
            ['password', 'string', 'min' => 6],
            [['first_name', 'last_name', 'email', 'password', 'status'], 'required', 'on' => 'create'],
            [['first_name', 'last_name', 'email', 'status'], 'required', 'on' => 'update'],
        ];
    }

    public function attributeLabels() {
        return [
            'firstname' => 'First Name',
            'lastname' => 'Last Name',
        ];
    }

    public function search($id) {
        $m_data = \common\models\User::findOne($id);

        $this->id = $id;
        $this->first_name = $m_data->first_name;
        $this->last_name = $m_data->last_name;
        $this->email = $m_data->email;
        $this->status = $m_data->status;
    }

    public function update() {
        $m_user = (new \backend\models\admin\User);
        if ($this->scenario == "create") {
            $m_user->isNewRecord;
            $m_user->generateAuthKey();
        } else {
            $m_user = \common\models\User::findOne(['id' => $this->id]);
        }

        $m_user->username = $this->email;
        $m_user->first_name = $this->first_name;
        $m_user->last_name = $this->last_name;
        $m_user->email = $this->email;
        if ($this->password) {
            $m_user->setPassword($this->password);
        }
        $m_user->status = ($this->status ? 10 : 0);
        $m_user->save();
    }

}
