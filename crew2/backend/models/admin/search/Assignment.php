<?php

namespace backend\models\admin\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * AssignmentSearch represents the model behind the search form about Assignment.
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class Assignment extends Model
{
    public $id, $username, $roles, $status;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'username', 'roles', 'status'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('rbac-admin', 'ID'),
            'username' => Yii::t('rbac-admin', 'Username'),
            'name' => Yii::t('rbac-admin', 'Name'),
            'status' => Yii::t('rbac-admin', 'Status'),
        ];
    }

    /**
     * Create data provider for Assignment model.
     * @param  array $params
     * @param  \yii\db\ActiveRecord $class
     * @param  string $usernameField
     * @return \yii\data\ActiveDataProvider
     */
    public function search($params, $class, $usernameField, $childRoles)
    {
        $user_table = \common\models\User::tableName();
        $query = $class::find()->alias('u')->select(['u.id', 'u.username', 'GROUP_CONCAT(a.item_name) as `roles`','u.status'])->joinWith('authAssignments a', false)->groupBy(['u.id']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        if (Yii::$app->authManager->getAssignment('superadmin', Yii::$app->user->id) && !($this->load($params) && $this->validate())) {
            return $dataProvider;
        }

        if ($this->roles) {
            $matched_user = \common\models\AuthAssignment::find()->select('DISTINCT(user_id)')->where(['like', 'item_name', $this->roles])->asArray()->all();
            $condition = ['u.id' => \yii\helpers\ArrayHelper::getColumn($matched_user, 'user_id')];
            $query->andFilterWhere($condition);
        }

        if($this->status !== null){
            $query->andFilterWhere(['u.status' => $this->status]);
        }

        if (!($this->load($params) && $this->validate())) {
            return $dataProvider;
        }

        $query->andFilterWhere(['like', 'u' . '.' . $usernameField, $this->username]);

        return $dataProvider;
    }
}
