<?php

use mdm\admin\components\MenuHelper;

\backend\assets\AppAsset::register($this);

$callback = function ($menu) {
    return [
        'label' => $menu['name'],
        'url' => [
            $menu['route'],
            'icon' => $menu['data'],
            'items' => $menu['children']
        ]
    ];
};

$data = [
    [
        'label' => 'Dashboard',
        'url' => ['/site/index'],
        'icon' => 'flaticon-dashboard',
    ],
    [
        'label' => 'Customer',
        'icon' => 'flaticon-users',
        'items' => [
            [
                'label' => 'Profile',
                'url' => ['/customer/index'],
            ],
            [
                'label' => 'Business Tax Request',
                'url' => ['/tax/business-list'],
            ],
            [
                'label' => 'Manual OTP Request',
                'url' => ['/manual-otp/index'],
            ],
        ]
    ],
    [
        'label' => 'Product',
        'icon' => 'flaticon-interface-9',
        'items' => [
            [
                'label' => 'Brand',
                'url' => ['/brand/index'],
            ],
            [
                'label' => 'Category',
                'url' => ['/category/index'],
            ],
            [
                'label' => 'Product',
                'url' => ['/product/index'],
            ],
            [
                'label' => 'Game Key',
                'items' => [
                    [
                        'label' => 'Publisher',
                        'url' => ['/game-publisher/index'],
                    ],
                    [
                        'label' => 'Attribute',
                        'url' => ['/game-product-attribute/index'],
                    ]
                ]
            ],
            [
                'label' => 'Mobile Recharge',
                'items' => [
                    [
                        'label' => 'Operator',
                        'url' => ['/mobile-recharge/operator-list'],
                    ],
                    [
                        'label' => 'Denomination',
                        'url' => ['/mobile-recharge/deno-list'],
                    ],
                    [
                        'label' => 'Region',
                        'url' => ['/mobile-recharge/region-list'],
                    ],
                ]
            ],
            [
                'label' => 'Publisher',
                'url' => ['/publisher/index'],
            ],
            [
                'label' => 'Reseller',
                'url' => ['/reseller/index'],
            ],
            [
                'label' => 'Settings',
                'url' => ['/products-config/index'],
            ],
        ]
    ],
    [
        'label' => 'Inventory',
        'icon' => 'flaticon-open-box',
        'items' => [
            [
                'label' => 'Restock',
                'url' => ['/api-restock-request/index'],
            ]

        ]
    ],
    [
        'label' => 'Sales',
        'icon' => 'flaticon-coins',
        'items' => [
            [
                'label' => 'Store Credit',
                'items' => [
                    [
                        'label' => 'Statement',
                        'url' => ['/store-credit/index']
                    ],
                    [
                        'label' => 'Batch Update',
                        'url' => ['/store-credit/queue']
                    ]
                ]
            ],
            [
                'label' => 'Order',
                'items' => [
                    [
                        'label' => 'Coupon Redemption',
                        'url' => ['/coupon-redeem-track/index']
                    ],
                ]
            ]
        ]
    ],
    [
        'label' => 'Finance',
        'icon' => 'flaticon-piggy-bank',
        'items' => [
            [
                'label' => 'Tax',
                'url' => ['/tax/index'],
            ],
            [
                'label' => 'Payment Method',
                'items' => [
                    [
                        'label' => 'Pipwave Mapper',
                        'url' => ['/pipwave-payment-mapper/index'],
                    ]
                ]
            ]
        ]
    ],
    [
        'label' => 'Marketing',
        'icon' => 'flaticon-diagram',
        'items' => [
            [
                'label' => 'SEO',
                'url' => ['/custom-seo/index'],
            ],
            [
                'label' => 'Promotion',
                'url' => ['/promo-content/index'],
            ],
            [
                'label' => 'Coupon',
                'url' => ['/coupons/index'],
            ],
        ]
    ],
    [
        'label' => 'Report',
        'icon' => 'flaticon-graphic',
        'items' => [
            [
                'label' => 'Purchase Order',
                'url' => ['/report/purchase-orders'],
            ],
            [
                'label' => 'Tax',
                'url' => ['/report/tax'],
            ],
            [
                'label' => 'Campaign',
                'url' => ['/report/campaign'],
            ],
            [
                'label' => 'Coupon',
                'url' => ['/report/coupon'],
            ],
            [
                'label' => 'No Purchase',
                'url' => ['/report/no-purchase'],
            ],
            [
                'label' => 'Sales',
                'url' => ['/report/sales'],
            ],
        ]
    ],
    [
        'label' => 'E-Commerce',
        'icon' => 'flaticon-cart',
        'items' => [
            [
                'label' => 'Location Restriction',
                'url' => ['/location-restriction/index'],
            ],
            [
                'label' => 'Static Page',
                'url' => ['/static-page/index'],
            ],
            [
                'label' => 'SEO URL Redirect',
                'url' => ['/seo-url-redirect/index'],
            ],
            [
                'label' => 'Rating',
                'items' => [
                    [
                        'label' => 'Order Review',
                        'url' => ['/order-review/index'],
                    ],
                    [
                        'label' => 'Setting',
                        'url' => ['/product-review/index'],
                    ],
                ]
            ],
        ]
    ],
    [
        'label' => 'Tool',
        'icon' => 'flaticon-paper-plane',
        'items' => [
            [
                'label' => 'Media',
                'url' => ['/file-uploader/list'],
            ]
        ]
    ],
    [
        'label' => 'Administration',
        'icon' => 'flaticon-cogwheel-2',
        'items' => [
            [
                'label' => 'Admin',
                'items' => [
                    [
                        'label' => 'User',
                        'url' => ['/admin/user/index'],
                    ],
                    [
                        'label' => 'Role',
                        'url' => ['/admin/role/index'],
                    ],
                    [
                        'label' => 'Permission',
                        'url' => ['/admin/permission/index'],
                    ],
                    [
                        'label' => 'Route',
                        'url' => ['/admin/route/index'],
                    ]
                ]
            ],
            [
                'label' => 'Blocked Email Address',
                'url' => ['/admin/blocked-email/index'],
            ],
            [
                'label' => 'Cache',
                'url' => ['/admin/system/service'],
            ],
            [
                'label' => 'Log',
                'items' => [
                    [
                        'label' => 'Incoming',
                        'url' => ['/request-log/incoming-log'],
                    ],
                    [
                        'label' => 'Outgoing',
                        'url' => ['/request-log/outgoing-log'],
                    ],
                    [
                        'label' => 'Debug',
                        'url' => ['/request-log/debug-log'],
                    ]
                ]
            ]
        ]
    ]
];

$current_route = Yii::$app->controller->getRoute();
$module = Yii::$app->controller->module->id;

if (!empty($this->params['activeMenu'])) {
    $active_menu = explode("/", $this->params['activeMenu']);
    $cur_item = &$data;
    foreach ($active_menu as $label) {
        foreach ($cur_item as $index => &$menu) {
            if ($menu['label'] === $label) {
                $menu['active'] = true;
                $cur_item = &$menu['items'];
            }
        }
    }
}

foreach ($data as $menu_index => &$menu) {
    checkSubMenuPermission($data, $menu_index);
}

function checkSubMenuPermission(&$data, $index)
{
    if (isset($data[$index]['items'])) {
        foreach ($data[$index]['items'] as $sub_index => &$sub_menu) {
            if ($item = checkSubMenuPermission($data[$index]['items'], $sub_index)) {
                $data[$index]['items'][$sub_index] = $item;
            } else {
                unset($data[$index]['items'][$sub_index]);
            }
        }
        if (empty($data[$index]['items'])) {
            unset($data[$index]);
        }
    } else {
        if (isset($data[$index]['url'][0]) && (!mdm\admin\components\Helper::checkRoute(ltrim($data[$index]['url'][0])))) {
            unset($data[$index]);
        }
    }
    return ($data[$index] ?? null);
}

?>
<button class="m-aside-left-close  m-aside-left-close--skin-light " id="m_aside_left_close_btn"><i class="la la-close"></i></button>
<div id="m_aside_left" class="m-grid__item m-aside-left  m-aside-left--skin-light" style="width: 250px;">

    <!-- BEGIN: Aside Menu -->
    <div id="m_ver_menu" class="m-aside-menu  m-aside-menu--skin-light m-aside-menu--submenu-skin-light " m-menu-vertical="1" m-menu-scrollable="0" m-menu-dropdown-timeout="500">
        <?php
        echo offgamers\metronic\widgets\Menu::widget(['items' => $data]);
        ?>
    </div>
    <!-- END: Aside Menu -->
</div>