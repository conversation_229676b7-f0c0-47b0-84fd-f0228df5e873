<?php

use yii\helpers\Html;

/* @var $this \yii\web\View */
/* @var $content string */


$asset = Yii::$app->metronic->registerCoreAssets($this);

$directoryAsset = Yii::$app->assetManager->getPublishedUrl($asset->sourcePath);

$this->beginPage() ?>

    <!DOCTYPE html>
    <html lang="<?php echo Yii::$app->language; ?>">
    <!-- begin::Head -->
    <head>
        <meta charset="<?php echo Yii::$app->charset; ?>"
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
        <?php echo Html::csrfMetaTags(); ?>
        <title><?php echo Html::encode($this->title); ?></title>

        <!--begin::Web font -->
        <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.16/webfont.js"></script>
        <script>
            WebFont.load({
                google: {"families": ["Poppins:300,400,500,600,700", "Roboto:300,400,500,600,700"]},
                active: function () {
                    sessionStorage.fonts = true;
                }
            });
        </script>
        <link rel="icon" type="image/x-icon" href="/icon.png" />
        <?php $this->head() ?>
    </head>
    <!-- end::Head -->


    <!-- begin::Body -->
    <body class="m--skin- m-header--fixed m-header--fixed-mobile m-aside-left--enabled m-aside-left--skin-dark m-aside-left--fixed m-aside-left--offcanvas m-footer--push m-aside--offcanvas-default">
    <?php $this->beginBody() ?>

    <!-- begin:: Page -->
    <div class="m-grid m-grid--hor m-grid--root m-page">
        <div class="m-grid__item m-grid__item--fluid m-grid m-grid--ver-desktop m-grid--desktop m-grid--tablet-and-mobile m-grid--hor-tablet-and-mobile m-login m-login--1 <?= (Yii::$app->controller->action->id == 'login' ? 'm-login--signin' : 'm-login--signup'); ?>" id="m_login">
            <div class="m-grid__item m-grid__item--order-tablet-and-mobile-2 m-login__aside" style="margin-left:auto;margin-right:auto">
                <div class="m-stack m-stack--hor m-stack--desktop">
                    <div class="m-stack__item m-stack__item--fluid">

                        <div class="m-login__wrapper" style="padding-top: 5%;">
                            <div class="m-login__logo">
                                <a href="/"> <img src="<?php echo $directoryAsset ?>/app/media/img/logo_65x65.png"> </a>
                            </div>

                            <?= $content ?>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- end:: Page -->
    <?php $this->endBody() ?>
    </body>
    <!-- end::Body -->
    </html>
<?php $this->endPage() ?>