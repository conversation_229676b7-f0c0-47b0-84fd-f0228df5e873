<?php

use yii\helpers\Html;

/* @var $content string */

$asset = Yii::$app->metronic->registerCoreAssets($this);

offgamers\metronic\bundles\GlyphiconFixAsset::register($this);

\yii\web\YiiAsset::register($this);

$directoryAsset = Yii::$app->assetManager->getPublishedUrl($asset->sourcePath);

$this->beginPage()
?>

<!DOCTYPE html>

<html lang="<?php echo Yii::$app->language; ?>">

    <!-- begin::Head -->
    <head>
        <meta charset="<?php echo Yii::$app->charset; ?>"
              <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
              <?php echo Html::csrfMetaTags(); ?>
        <title><?php echo Html::encode($this->title); ?></title>
        <!--begin::Web font -->
        <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.16/webfont.js"></script>
        <script>
            WebFont.load({
                google: {"families": ["Poppins:300,400,500,600,700", "Roboto:300,400,500,600,700"]},
                active: function () {
                    sessionStorage.fonts = true;
                }
            });
        </script>

        <!--end::Web font -->
        <?php $this->head() ?>
        <!--end::Page Vendors Styles -->
        <link rel="icon" type="image/x-icon" href="/icon.png" />
    </head>

    <!-- end::Head -->

    <!-- begin::Body -->
    <?php $this->beginBody() ?>

    <body class="m-content--skin- m-header--fixed m-header--fixed-mobile m-aside-left--enabled m-aside-left--skin-light m-aside--offcanvas-default">

        <!-- begin:: Page -->
        <div class="m-grid m-grid--hor m-grid--root m-page">

            <!-- BEGIN: Header -->

            <?php echo $this->render('header', ['directoryAsset' => $directoryAsset]); ?>

            <!-- END: Header -->

            <!-- begin::Body -->
            <div class="m-grid__item m-grid__item--fluid m-grid m-grid--ver-desktop m-grid--desktop m-body">

                <!-- BEGIN: Left Aside -->

                <?php
                    echo $this->render('left', array());
                ?>
                <!-- END: Left Aside -->
                <div class="m-grid__item m-grid__item--fluid m-wrapper">

                    <!-- BEGIN: Subheader -->
                    <div class="m-subheader ">
                        <div class="d-flex align-items-center">
                            <div class="mr-auto">
                                <h3 class="m-subheader__title m-subheader__title--separator"><?php echo Html::encode($this->title); ?></h3>
                                <?php
                                if (isset($this->params['breadcrumbs'])) {
                                    echo offgamers\metronic\widgets\Breadcrumbs::widget([
                                        'options' => ['class' => 'm-subheader__breadcrumbs m-nav m-nav--inline'],
                                        'itemTemplate' => '<li class="m-nav__item">{link}</li>',
                                        'labelTemplate' => '<span class="m-nav__link-text">{label}</span>',
                                        'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                                    ]);
                                }
                                ?>
                            </div>

                            <?php if (isset($this->params['subHeader']) && !empty($this->params['subHeader'])) { ?>
                                <div>
                                    <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" m-dropdown-toggle="hover" aria-expanded="true">
                                        <a href="#" class="m-portlet__nav-link btn btn-lg btn-secondary  m-btn m-btn--outline-2x m-btn--air m-btn--icon m-btn--icon-only m-btn--pill  m-dropdown__toggle">
                                            <i class="la la-plus m--hide"></i>
                                            <i class="la la-ellipsis-h"></i>
                                        </a>
                                        <div class="m-dropdown__wrapper">
                                            <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                                            <div class="m-dropdown__inner">
                                                <div class="m-dropdown__body">
                                                    <div class="m-dropdown__content">
                                                        <ul class="m-nav">
                                                            <?php if (isset($this->params["subHeader"]["title"])) { ?>
                                                                <li class="m-nav__section m-nav__section--first">
                                                                    <span class="m-nav__section-text"><?= $this->params["subHeader"]["title"]; ?></span>
                                                                </li>
                                                            <?php } ?>
                                                            <?php foreach ($this->params["subHeader"]["option"] as $num => $val) { ?>
                                                                <li class="m-nav__item">
                                                                    <a href="<?= $val["link"]; ?>" class="m-nav__link">
                                                                        <span class="m-nav__link-text <?= ($val["active"] ? "m--font-primary" : ""); ?>"><?= $val["title"]; ?></span>
                                                                    </a>
                                                                </li>
                                                            <?php } ?>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                    <!-- END: Subheader -->

                    <!-- display success message -->
                    <div class="m-content" id="msg-flash">
                        <?php if (Yii::$app->session->hasFlash('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                </button>
                                <?= Yii::$app->session->getFlash('success') ?>
                            </div>
                        <?php endif; ?>

                        <!-- display error message -->
                        <?php if (Yii::$app->session->hasFlash('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                </button>
                                <?= Yii::$app->session->getFlash('error') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php
                    if (isset($this->params['layoutContent']) && $this->params['layoutContent'] == 'block') {
                        echo $this->render('content_block', ['content' => $content]);
                    } else {
                        echo $this->render('content', ['content' => $content]);
                    }
                    ?>

                </div>
            </div>

            <!-- end:: Body -->

            <!-- begin::Footer -->
            <?php echo $this->render('footer', array()); ?>

            <!-- end::Footer -->
        </div>

        <!-- end:: Page -->

        <!-- begin::Scroll Top -->
        <div id="m_scroll_top" class="m-scroll-top">
            <i class="la la-arrow-up"></i>
        </div>

    </body>
    <?php $this->endBody() ?>
    <!-- end::Body -->
</html>
<?php $this->endPage() ?>