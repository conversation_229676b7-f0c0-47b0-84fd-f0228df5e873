<?php
/* @var $this yii\web\View */
/* @var $form yii\bootstrap\ActiveForm */

/* @var $model \common\models\LoginForm */

use yii\helpers\Html;
use yii\bootstrap\ActiveForm;

$this->title = Yii::t('site', 'Login');
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="m-login__signin">
    <div class="m-login__head">
        <h3 class="m-login__title">Login to Crew</h3>
    </div>

    <?php $form = ActiveForm::begin(['id' => 'login-form', 'action' => ['site/login'], 'options' => ['class' => 'm-login__form m-form']]); ?>
    <div class="form-group m-form__group">
        <?= $form->field($model, 'username')->textInput(['autofocus' => true, 'class' => 'form-control m-input', 'placeholder' => 'Username'])->label(false) ?>
    </div>
    <div class="form-group m-form__group">
        <?= $form->field($model, 'password')->passwordInput(['class' => 'form-control m-input m-login__form-input--last', 'placeholder' => 'Password'])->label(false) ?>
    </div>
    <div class="row m-login__form-sub">
        <div class="col m--align-left">
            <?= $form->field($model, 'rememberMe')->checkbox() ?>
        </div>
        <div class="col m--align-right">
            <a href="javascript:;" id="m_login_forget_password" class="m-link">Forget Password ?</a>
        </div>
    </div>
    <div class="m-login__form-action" style="text-align:center">
        <?= Html::submitButton('Login', ['class' => 'btn btn-focus m-btn m-btn--pill m-btn--custom m-btn--air', 'name' => 'login-button']); ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<div class="m-login__forget-password">
    <div class="m-login__head">
        <h3 class="m-login__title">Forgotten Password ?</h3>
        <div class="m-login__desc">Enter your email to reset your password:</div>
    </div>
    <form class="m-login__form m-form" action="">
        <div class="form-group m-form__group">
            <input class="form-control m-input" type="text" placeholder="Email" name="email" id="m_email" autocomplete="off">
        </div>
        <div class="m-login__form-action">
            <button id="m_login_forget_password_submit" class="btn btn-focus m-btn m-btn--pill m-btn--custom m-btn--air">Request</button>
            <button id="m_login_forget_password_cancel" class="btn btn-outline-focus m-btn m-btn--pill m-btn--custom">Cancel</button>
        </div>
    </form>
</div>
<div class="m-login__signup">
    <div class="m-login__head">
        <h3 class="m-login__title"><?= Yii::t('rbac-admin', 'Reset password'); ?></h3>
        <div class="m-login__desc"><?= Yii::t('rbac-admin', 'Please choose your new password:') ?></div>
    </div>
    <form class="m-login__form m-form" method="post" action="" _lpchecked="1">
        <div class="form-group m-form__group">
            <input class="form-control m-input" type="password" placeholder="Password" name="password">
        </div>
        <div class="form-group m-form__group">
            <input class="form-control m-input m-login__form-input--last" type="password" placeholder="Confirm Password" name="retypePassword">
        </div>
        <div class="m-login__form-action">
            <button id="m_login_signup_submit" class="btn btn-focus m-btn m-btn--pill m-btn--custom m-btn--air">Reset</button>
            <button id="m_login_signup_cancel" class="btn btn-outline-focus  m-btn m-btn--pill m-btn--custom">Cancel</button>
        </div>
    </form>
</div>

<style>
    .help-block-error {
        color: red;
        font-size: smaller;
    }
</style>

<?php
$this->registerJs(<<< JS
//== Class Definition
var SnippetLogin = function() {

    var login = $('#m_login');

    var showErrorMsg = function(form, type, msg) {
        var alert = $('<div class="m-alert m-alert--outline alert alert-' + type + ' alert-dismissible" role="alert">\
			<button type="button" class="close" data-dismiss="alert" aria-label="Close"></button>\
			<span></span>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        mUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

    //== Private Functions

    var displaySignUpForm = function() {
        login.removeClass('m-login--forget-password');
        login.removeClass('m-login--signin');

        login.addClass('m-login--signup');
        mUtil.animateClass(login.find('.m-login__signup')[0], 'flipInX animated');
    }

    var displaySignInForm = function() {
        login.removeClass('m-login--forget-password');
        login.removeClass('m-login--signup');

        login.addClass('m-login--signin');
        mUtil.animateClass(login.find('.m-login__signin')[0], 'flipInX animated');
        //login.find('.m-login__signin').animateClass('flipInX animated');
    }

    var displayForgetPasswordForm = function() {
        login.removeClass('m-login--signin');
        login.removeClass('m-login--signup');

        login.addClass('m-login--forget-password');
        //login.find('.m-login__forget-password').animateClass('flipInX animated');
        mUtil.animateClass(login.find('.m-login__forget-password')[0], 'flipInX animated');

    }

    var handleFormSwitch = function() {
        $('#m_login_forget_password').click(function(e) {
            e.preventDefault();
            displayForgetPasswordForm();
        });

        $('#m_login_forget_password_cancel').click(function(e) {
            e.preventDefault();
            displaySignInForm();
        });

        $('#m_login_signup').click(function(e) {
            e.preventDefault();
            displaySignUpForm();
        });

        $('#m_login_signup_cancel').click(function(e) {
            e.preventDefault();
            displaySignInForm();
        });
    }

    var handleSignInFormSubmit = function() {
        $('#m_login_signin_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');

            form.validate({
                rules: {
                    email: {
                        required: true,
                        email: true
                    },
                    password: {
                        required: true
                    }
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('m-loader m-loader--right m-loader--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '',
                success: function(response, status, xhr, form) {
                	// similate 2s delay
                	setTimeout(function() {
	                    btn.removeClass('m-loader m-loader--right m-loader--light').attr('disabled', false);
	                    showErrorMsg(form, 'danger', 'Incorrect username or password. Please try again.');
                    }, 2000);
                }
            });
        });
    }

    var handleSignUpFormSubmit = function() {
        $('#m_login_signup_submit').click(function(e) {
            e.preventDefault();

            var btn = $(this);
            var form = $(this).closest('form');

            form.validate({
                rules: {
                    password: {
                        required: true
                    },
                    retypePassword: {
                        required: {
                          equalTo: "input[name='password']"
                        }
                    }
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('m-loader m-loader--right m-loader--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '',
                method: 'post',
                dataType: 'json',
                success: function(response, status, xhr, form) {
                	// similate 2s delay
                	console.log(response);
                	btn.removeClass('m-loader m-loader--right m-loader--light').attr('disabled', false);
                    form.clearForm();
                    form.validate().resetForm();
                    displaySignInForm();
                    var signInForm = login.find('.m-login__signin form');
                    signInForm.clearForm();
                    signInForm.validate().resetForm();

                    showErrorMsg(signInForm, 'success', response.message);
                    
                	// setTimeout(function() {
	                //     btn.removeClass('m-loader m-loader--right m-loader--light').attr('disabled', false);
	                //     form.clearForm();
	                //     form.validate().resetForm();
                    //
	                //     // display signup form
	                //     displaySignInForm();
	                //     var signInForm = login.find('.m-login__signin form');
	                //     signInForm.clearForm();
	                //     signInForm.validate().resetForm();
                    //
	                //     showErrorMsg(signInForm, 'success', 'Thank you. To complete your registration please check your email.');
	                // }, 2000);
                }
            });
        });
    }

    var handleForgetPasswordFormSubmit = function() {
        $('#m_login_forget_password_submit').click(function(e) {
            e.preventDefault();
            
            var btn = $(this);
            var form = $(this).closest('form');

            form.validate({
                rules: {
                    email: {
                        required: true,
                        email: true
                    }
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('m-loader m-loader--right m-loader--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/site/request-password-reset',
                dataType : 'json',
                success: function(response, status, xhr, form) {
                    btn.removeClass('m-loader m-loader--right m-loader--light').attr('disabled', false); // remove 
                    form.clearForm(); // clear form
                    form.validate().resetForm(); // reset validation states
    
                    // display signup form
                    displaySignInForm();
                    var signInForm = login.find('.m-login__signin form');
                    signInForm.clearForm();
                    signInForm.validate().resetForm();
                    showErrorMsg(signInForm, (response.success ? 'success' : 'warning'), response.message);
                }
            });
        });
    }

    //== Public Functions
    return {
        // public functions
        init: function() {
            handleFormSwitch();
            handleSignInFormSubmit();
            handleSignUpFormSubmit();
            handleForgetPasswordFormSubmit();
        }
    };
}();

//== Class Initialization
jQuery(document).ready(function() {
    SnippetLogin.init();
});
JS
        , \yii\web\View::POS_END);
