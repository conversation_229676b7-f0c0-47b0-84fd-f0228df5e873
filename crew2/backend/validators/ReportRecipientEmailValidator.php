<?php

namespace backend\validators;

use yii\validators\EmailValidator;
use yii\validators\Validator;

class ReportRecipientEmailValidator extends Validator
{
    /**
     * {@inheritdoc}
     */
    public function validateAttribute($model, $attribute)
    {
        $emails = explode(",", $model->$attribute);

        if ($emails) {
            $emailStringErrors = [];
            $validator = new EmailValidator();

            foreach ($emails as $email) {
                $email = trim($email);

                if (!$validator->validate($email)) {
                    $emailStringErrors[] = $email;
                }
            }

            if ($emailStringErrors) {
                $this->addError($model, $attribute, implode(", ", $emailStringErrors) . " is not a valid email.");
            }
        }
    }

}