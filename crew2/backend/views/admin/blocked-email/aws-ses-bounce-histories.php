<div class="m-portlet m-portlet--accent m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-time"></i>
                </span>
                <span class="m-portlet__head-text">
                    Log History
                </span>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="m-scrollable" data-scrollable="true" data-height="300" data-mobile-height="300">
            <?php
            foreach ($model->sesBounceHistory as $key => $history) {
                ?>

                <div class="m-timeline-2">
                    <div class="m-timeline-2__items  m--padding-top-25">
                        <div class="m-timeline-2__item">
                            <div class="m-timeline-2__item-cricle">
                                <i class="fa fa-genderless m--font-info"></i>
                            </div>
                            <div class="m-timeline-2__item-text  m--padding-top-5">
                                <b><?=$history->created_at;?></b><br />
                                <strong>Email Address</strong> : <?=$history->email;?><br/>
                                <strong>Bounce Type</strong> : <?=$history->bounce_type?><br/>
                                <strong>Bounce Sub-Type</strong> : <?=$history->bounce_sub_type;?><br/>
                                <strong>Raw Data</strong> :
                                <span  style=" word-break: break-all;">
                                    <?=$history->error_string;?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>