<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ActiveForm;
use kartik\widgets\SwitchInput;

?>
<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    AWS SES Bounce
                </h3>
            </div>
        </div>
    </div>

    <?php
    $form = ActiveForm::begin([
        'id' => 'email-bounce-customers',
        'action' => Url::toRoute("/admin/blocked-email/update-email-bounce"),
        'options' => [
            'class' => 'm-form m-form--fit m-form--label-align-right ',

        ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="m-form__section">
            <?php
            if(isset($model->sesBounce) && !empty($model->sesBounce)){?>
                <div class="form-group m-form__group row">
                    <label class="col-lg-2">Email Address</label>
                    <div class="col-lg-4"><?= $model->sesBounce->email; ?></div>
                </div>
                <input type="hidden" name="email" value="<?= $model->sesBounce->email; ?>" />

                <div class="form-group m-form__group row">
                    <label class="col-lg-2">Email Address Blocked
                    </label>
                    <div class="col-lg-4">
                       <?=SwitchInput::widget([
                        'name' => 'email_blocked',
                        'value' => (isset($model->sesBounce) && !empty($model->sesBounce)) ?1:0,
                        'disabled' => (isset($model->sesBounce) && !empty($model->sesBounce)) ?false:true,
                        'pluginOptions' => [
                            'onText' => '<i class="fa fa-check"></i>',
                            'offText' => '<i class="fa fa-times"></i>',
                        ]
                    ]);?>
                </div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Bounce Type
                </label>
                <div class="col-lg-4"><?=$model->sesBounce->bounced_type ?></div>

                <label class="col-lg-2">Created At </label>
                <div class="col-lg-4"><?= $model->sesBounce->created_datetime; ?></div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Bounce Sub-Type</label>
                <div class="col-lg-4"><?= $model->sesBounce->bounced_sub_type; ?></div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Raw Data
                </label>
                <div class="col-lg-10" style=" word-break: break-all;">

                    <?= $model->sesBounce->error_string; ?>
                    
                </div>

            </div>
        <?php }else{?>
            <div style="text-align:center;" class="form-group m-form__group ">
                No Result Found
            </div>
        <?php }?>

        <?php if (isset($model->sesBounceHistory) && !empty($model->sesBounceHistory)) {?>
            <div class="form-group m-form__group row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <?= $this->render('aws-ses-bounce-histories', ['model' => $model]); ?>
                </div>
            </div>
        <?php }?>
        
    </div>
</div>
<?php
if(isset($model->sesBounce) && !empty($model->sesBounce)
){?>
    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2">  
                </div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>

                </div>
            </div>
        </div>
    </div>
<?php }?>
<?php ActiveForm::end(); ?>

</div>