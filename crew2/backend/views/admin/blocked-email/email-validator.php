<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ActiveForm;
use kartik\widgets\SwitchInput;

?>
<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Email Validator
                </h3>
            </div>
        </div>
    </div>
    <!--begin::Form-->
    <?php
    $form = ActiveForm::begin([
        'id' => 'email-validate-customers',
        'action' => Url::toRoute("/admin/blocked-email/update-email-status"),
        'options' => [
            'class' => 'm-form m-form--fit m-form--label-align-right ',

        ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="m-form__section">
            <?php
            if(isset($model->emailValidation) && !empty($model->emailValidation)){?>
                <div class="form-group m-form__group row">
                    <label class="col-lg-2">Email Address</label>
                    <div class="col-lg-4"><?= $model->emailValidation->email; ?></div>
                </div>
                <input type="hidden" name="email" value="<?= $model->emailValidation->email; ?>" />

                <div class="form-group m-form__group row">
                    <label class="col-lg-2">Service</label>
                    <div class="col-lg-4"><?= $model->emailValidation->profile; ?></div>
                </div>

                <div class="form-group m-form__group row">
                    <label class="col-lg-2">MX Record
                        <br/>(valid email address)
                    </label>
                    <div class="col-lg-4">
                        <?=SwitchInput::widget([
                            'name' => 'valid_email',
                            'value' => (!isset($model->emailValidation->mx_found) || $model->emailValidation->mx_found == 1) ?1:0,
                            'disabled' => (!isset($model->emailValidation->mx_found) || $model->emailValidation->mx_found == 1) ?true:false,
                            'pluginOptions' => [
                                'onText' => '<i class="fa fa-check"></i>',
                                'offText' => '<i class="fa fa-times"></i>',
                            ]
                        ]);?>
                    </div>

                    <label class="col-lg-2">Score <i data-toggle="m-tooltip" title="Score between 0 and 100, reflect quality and deliverability" class="fa fa-question-circle"></i></label>
                    <div class="col-lg-4"><?= $model->emailValidation->score; ?></div>
                </div>

                <div class="form-group m-form__group row">
                    <label class="col-lg-2">SMTP
                    </label>
                    <div class="col-lg-4"><?= ($model->emailValidation->smtp_check==1)?"True":"False"; ?></div>

                    <label class="col-lg-2">Created At </label>
                    <div class="col-lg-4"><?= date("Y-m-d H:i:s",$model->emailValidation->created_at); ?></div>
                </div>

                <div class="form-group m-form__group row">
                    <label class="col-lg-2">Raw Data
                    </label>
                    <div class="col-lg-10" style=" word-break: break-all;">

                        <?= $model->emailValidation->raw; ?>

                    </div>

                </div>
            <?php }else{?>
                <div style="text-align:center;" class="form-group m-form__group  m-grid-col-center">
                    No Result Found
                </div>
            <?php }?>
        </div>
    </div>

    <?php if(isset($model->emailValidation) && !empty($model->emailValidation)){?>
        <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
            <div class="m-form__actions m-form__actions--solid">
                <div class="row">
                    <div class="col-lg-2">

                    </div>
                    <div class="col-lg-10">

                        <button type="submit" class="btn btn-success"
                        <?=($model->emailValidation->mx_found != 1)?'':' disabled="disabled" ';?>
                        >Submit</button>
                    </div>
                </div>
            </div>
        </div>
    <?php }?>
    <?php ActiveForm::end(); ?>
</div>