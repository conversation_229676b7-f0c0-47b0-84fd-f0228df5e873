<?php

use yii\helpers\Html;

use yii\bootstrap\ActiveForm;

$this->title = 'Blocked Email Address';

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = 'Blocked Email Address';
$this->params['layoutContent'] = 'block';

Yii::$app->view->params['activeMenu'] = 'Administration/Blocked Email';

$form = ActiveForm::begin([
            'id' => 'customer-email-search-form',
            'action' => ['/admin/blocked-email/index'],
            'method' => 'post',
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
        ]);

?>

<div class="m-portlet  m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
       
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Email Address</label>
            <div class="col-lg-4">
                <input type="email" id="customer-email-address" class="form-control" name="customer_email_address" aria-invalid="false"
                value="<?=(isset($post['customer_email_address']))?$post['customer_email_address']:''?>"
                >
            </div>
        </div>
        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_search" name="submit_search" class="btn btn-primary">Search</button>
                <button type="button" class="btn btn-default" onclick="_reset();">Reset</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<?php if(isset($post['customer_email_address'])){?>
<?= $this->render('email-validator', ['model' => $model]); ?>
<?= $this->render('aws-ses-bounce', ['model' => $model]); ?>
<?php }?>
<script type="text/javascript">
    function _reset() {
        $(':input').not(':button, :submit, :reset, :hidden, :checkbox, :radio').val('');
        $(':checkbox, :radio').prop('checked', false);
    }
</script>