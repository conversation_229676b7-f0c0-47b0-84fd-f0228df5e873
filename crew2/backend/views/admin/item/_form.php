<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use mdm\admin\components\RouteRule;
use mdm\admin\AutocompleteAsset;
use yii\helpers\Json;
use mdm\admin\components\Configs;

/* @var $this yii\web\View */
/* @var $model mdm\admin\models\AuthItem */
/* @var $form yii\widgets\ActiveForm */
/* @var $context mdm\admin\components\ItemController */

$context = $this->context;
$labels = $context->labels();
$rules = Configs::authManager()->getRules();
unset($rules[RouteRule::RULE_NAME]);
$source = Json::htmlEncode(array_keys($rules));

$js = <<<JS
    $('#rule_name').autocomplete({
        source: $source,
    });
JS;
AutocompleteAsset::register($this);
$this->registerJs($js);
?>

<div class="auth-item-form">
    <?php
    $form = ActiveForm::begin([
                'id' => 'item-form',
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ]
    ]);
    ?>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'name')->textInput(['maxlength' => 64])->label(false) ?>
            </div>
            <label class="col-lg-2 col-form-label">Rule Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'ruleName')->textInput(['id' => 'rule_name'])->label(false) ?>
            </div>
        </div>
        
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Description</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'description')->textarea(['rows' => 6])->label(false) ?>
            </div>
            <label class="col-lg-2 col-form-label">Data</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'data')->textarea(['rows' => 6])->label(false) ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    
    <?php ActiveForm::end(); ?>
</div>
