<?php

use mdm\admin\AnimateAsset;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\helpers\Json;
use yii\web\YiiAsset;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model mdm\admin\models\AuthItem */
/* @var $context mdm\admin\components\ItemController */

$context = $this->context;
$labels = $context->labels();

$this->title = "View";

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = ['label' => 'Admin', 'url' => ['/admin/user/index']];
$this->params['breadcrumbs'][] = ['label' => Yii::t('rbac-admin', $labels['Items']), 'url' => ['index']];
$this->params['layoutContent'] = 'block';

Yii::$app->view->params['activeMenu'] = 'Administration/Admin/' . rtrim($labels['Items'], "s");

AnimateAsset::register($this);
YiiAsset::register($this);
$opts = Json::htmlEncode([
            'items' => $model->getItems(),
        ]);
$this->registerJs("var _opts = {$opts};");
$this->registerJs($this->render('_script.js'));
$animateIcon = ' <i class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></i>';
?>

<div class="auth-item-view m-portlet m-portlet--mobile m-form">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $model->name; ?>
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <div class="col-sm-5">
                <input class="form-control search" data-target="available"
                       placeholder="<?= Yii::t('rbac-admin', 'Search for available'); ?>">
                <select multiple size="20" class="form-control list" data-target="available"></select>
            </div>
            <div class="col-sm-2" align="center">
                <br><br>
                <?=
                Html::a('&gt;&gt;' . $animateIcon, ['assign', 'id' => $model->name], [
                    'class' => 'btn btn-success btn-assign',
                    'data-target' => 'available',
                    'title' => Yii::t('rbac-admin', 'Assign'),
                ]);
                ?><br><br>
                <?=
                Html::a('&lt;&lt;' . $animateIcon, ['remove', 'id' => $model->name], [
                    'class' => 'btn btn-danger btn-assign',
                    'data-target' => 'assigned',
                    'title' => Yii::t('rbac-admin', 'Remove'),
                ]);
                ?>
            </div>
            <div class="col-sm-5">
                <input class="form-control search" data-target="assigned"
                       placeholder="<?= Yii::t('rbac-admin', 'Search for assigned'); ?>">
                <select multiple size="20" class="form-control list" data-target="assigned"></select>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-12 m--align-right">
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>
</div>