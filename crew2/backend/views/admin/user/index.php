<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\Pjax;
use mdm\admin\components\Helper;

$this->title = 'User';

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = ['label' => 'Admin', 'url' => ['/admin/user/index']];
$this->params['breadcrumbs'][] = 'User';
$this->params['layoutContent'] = 'block';

Yii::$app->view->params['activeMenu'] = 'Administration/Admin/User';

$columns = [
    ['class' => 'yii\grid\SerialColumn'],
    $usernameField,
];

if (!empty($extraColumns)) {
    $columns = array_merge($columns, $extraColumns);
}

$columns[] = [
    'class' => 'yii\grid\ActionColumn',
    'template' => Helper::filterActionColumn('{update} {view} {delete}'),
    'contentOptions' => ['style' => 'white-space: nowrap;'],
    'buttons' => [
        'view' => function ($url, $model) { // <--- here you can override or create template for a button of a given name
            return ($model->status ? Html::a('<span class="glyphicon glyphicon-address-card"></span>', ['/admin/assignment/view', 'id' => (string) $model->id], [
                        'title' => 'Assignment',
                        'aria-label' => 'Assignment',
                        'data-pjax' => '0',
                            ]
                    ) : false);
        }
    ],
];
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('user/update'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <?php Pjax::begin(); ?>
            <?=
            GridView::widget([
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'columns' => $columns,
            ]);
            ?>
            <?php Pjax::end(); ?>
        </div>
    </div>
</div>