<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ActiveForm;
use kartik\widgets\SwitchInput;

$this->title = 'User';

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = ['label' => 'Admin', 'url' => ['/admin/user/index']];
$this->params['breadcrumbs'][] = ['label' => 'User', 'url' => ['/admin/user/index']];
$this->params['layoutContent'] = 'block';

Yii::$app->view->params['activeMenu'] = 'Administration/Admin/User';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= !empty($model->email) ? $model->email : "New"; ?>
                </h3>
            </div>
        </div>
    </div>

    <!--begin::Form-->
    <?php
    $form = ActiveForm::begin([
                'id' => 'form-signup',
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">First Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'first_name')->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Last Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'last_name')->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">
                Email Address<br />
                <span class="m-form__help">login username</span>
            </label>
            <div class="col-lg-4">
                <?= $form->field($model, 'email')->label(false); ?>

            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Password</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'password')->passwordInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'status')->widget(SwitchInput::classname(), [
                    'value' => (isset($model->status) && $model->status == 10) ? true : false,
                    'options' => [
                        'checked' => (isset($model->status) && $model->status == 10) ? true : false,
                    ],
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('/admin/user/index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>