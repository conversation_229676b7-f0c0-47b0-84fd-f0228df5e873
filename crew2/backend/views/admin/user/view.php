<?php

use mdm\admin\AnimateAsset;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\helpers\Json;
use yii\web\YiiAsset;

$this->title = 'User';

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = ['label' => 'Admin', 'url' => ['/admin/user/index']];
$this->params['breadcrumbs'][] = ['label' => 'User', 'url' => ['/admin/user/index']];
$this->params['layoutContent'] = 'block';

Yii::$app->view->params['activeMenu'] = 'Administration/Admin/User';

AnimateAsset::register($this);
YiiAsset::register($this);

$opts = Json::htmlEncode([
            'items' => $assignmentArray,
        ]);

$this->registerJs("var _opts = {$opts};");
$this->registerJs($this->render('_script.js'));
$animateIcon = ' <i class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></i>';
?>

<div class="assignment-index">
    <div class="m-portlet m-portlet--mobile m-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        Role and Permission<small><?= $model->email; ?></small>
                    </h3>
                </div>
            </div>
        </div>

        <div class="m-portlet__body">
            <div class="form-group m-form__group row">
                <div class="col-lg-5">
                    <input class="form-control search" data-target="available" placeholder="Available">
                    <select multiple size="20" class="form-control list" data-target="available"></select>
                </div>
                <div class="col-lg-2" align="center">
                    <?=
                    Html::a('&gt;&gt;' . $animateIcon, ['assign', 'id' => (string) $model->id], [
                        'class' => 'btn btn-success btn-assign',
                        'data-target' => 'available',
                        'title' => Yii::t('rbac-admin', 'Assign'),
                    ]);
                    ?>
                    <br /><br />
                    <?=
                    Html::a('&lt;&lt;' . $animateIcon, ['revoke', 'id' => (string) $model->id], [
                        'class' => 'btn btn-danger btn-assign',
                        'data-target' => 'assigned',
                        'title' => Yii::t('rbac-admin', 'Remove'),
                    ]);
                    ?>
                </div>
                <div class="col-lg-5">
                    <input class="form-control search" data-target="assigned" placeholder="Assign">
                    <select multiple size="20" class="form-control list" data-target="assigned"></select>
                </div>
            </div>
        </div>

        <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
            <div class="m-form__actions m-form__actions--solid">
                <div class="row">
                    <div class="col-lg-12 m--align-right">
                        <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Back</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>