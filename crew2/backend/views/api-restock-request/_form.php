<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use yii\widgets\LinkPager;
?>

<div class="customer-list">
    <div class="row">
        <div class="col-xl-12">
            <div class="m-portlet m-portlet--mobile">
                <div class="m-portlet__body">
                    <?php
                    echo GridView::widget([
                        'dataProvider' => $provider,
                        'columns' => [
                            [
                                'header' => 'Request ID',
                                'attribute' => 'api_restock_request_id',  
                            ],
                            [
                                'header' => 'Order ID',
                                'attribute' => 'orders_id',
                                'value' => function ($data) {
                                    if($data['orders_id'] == ''){
                                        return '-';
                                    }
                                    else{
                                        return $data['orders_id'];
                                    }
                                }
                            ],
                            [
                                'header' => 'Product ID',
                                'attribute' => 'products_id',
                                'value' => function ($data) {
                                    if($data['products_id'] == ''){
                                        return '-';
                                    }
                                    else{
                                        return $data['products_id'];
                                    }
                                }
                            ],
                            [
                                'header' => 'Products Name',
                                'attribute' => 'products_name',
                                'value' => function ($data) {
                                    if($data['products_name'] == ''){
                                        return '-';
                                    }
                                    else{
                                        return $data['products_name'];
                                    }
                                }
                                
                               
                            ],
                            [
                                'header' => 'Status',
                                'attribute' => 'status',
                                'value' => function ($data) {
                                    if($data['status'] == 0){
                                        return 'New Request';
                                    }
                                    if($data['status'] == 1){
                                        return 'Success';
                                    }
                                    if($data['status'] == 2){
                                        return 'Failed/Reattempt';
                                    }
                                    if($data['status'] == 3){
                                        return 'Preorder';
                                    }
                                    if($data['status'] == 5){
                                        return 'Transaction Void, Generate New Id';
                                    }
                                    if($data['status'] == 9){
                                        return 'Margin Block';
                                    }
                                  
                                },
                            ],
                            [
                                'header' => 'Action',
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{update}'),
                                'contentOptions' => ['style' => 'white-space: nowrap; width: 60px'],
                                'buttons' => [
                                    'update' => function ($url, $model, $key) {
                                       
                                        return Html::a('<span><i class="la la-pencil-square"></i></span>', ['/api-restock-request/update', 'id' => (string) $model['api_restock_request_id']] ,['title' => 'Update','data-toggle'=>'m-tooltip','data-placement'=>'bottom']);
                                    }
                                ],
                            ],
                        ],
                        'layout' => "{summary}\n{items}",
                    ]);

       
                    $form = ActiveForm::begin([
                                'id' => 'api-restock-request-pagesize-form',
                                'method' => 'get',
                                'action' => ['/api-restock-request/index']
                    ]);

                    echo Html::hiddenInput('page', $model->page);
                    echo Html::hiddenInput('order_id', $model->order_id);
                    echo Html::hiddenInput('product_id', $model->product_id);
                    echo Html::hiddenInput('status', $model->status);
                 

                    echo \backend\widgets\MetronicPager::renderPager($provider);

                    ActiveForm::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>