<?php

use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use backend\assets\DataTableAsset;

DataTableAsset::register($this);

$this->title = 'Restock';

$this->params['breadcrumbs'][] = 'Inventory';
$this->params['breadcrumbs'][] = ['label' => 'Restock', 'url' => ['index']];
$this->params['layoutContent'] = 'block';

$form = ActiveForm::begin([
            'id' => 'api-restock-request-search-form',
            'action' => ['/api-restock-request/index'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
        ]);
?>
<div class="m-portlet m-portlet--primary <?= ($provider ? "m-portlet--collapsed" : ""); ?> m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Order ID</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'order_id')->textInput(['id' => 'order-id'])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">Product ID</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'product_id')->textInput(['id' => 'product-id'])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'status')->dropDownList([
                    '' => '',
                    '0' => 'New Request',
                    '1' => 'Success',
                    '2' => 'Failed/Reattempt',
                    '3' => 'Preorder',
                    '5' => 'Transaction Void, Generate New Id',
                    '9' => 'Margin Block'
                    ],
                    [
                        'class' => 'form-control',
                    ],
                )->label(false);
                ?>
            </div>
        </div>
      
        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_search" name="submit_search" class="btn btn-primary">Search</button>
                <button type="button" class="btn btn-default" onclick="_reset();">Reset</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<!-- Render the api restock list -->
<?php
if (!$model->errors && $provider) {
    echo (isset($provider) && $provider) ? $this->render('_form', [
                'model' => $model,
                'provider' => $provider,
            ]) : '';
}
?>


<script type="text/javascript">
    function _reset() {
        $('#order-id').val('');
        $('#product-id').val('');
    }
</script>