<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use backend\assets\DataTableAsset;

DataTableAsset::register($this);

$this->title = 'Restock';

$this->params['breadcrumbs'][] = 'Inventory';
$this->params['breadcrumbs'][] = ['label' => 'Restock', 'url' => ['index']];
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head" style="background-color:#00c5dc;">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text" style="color:white">
                   Request ID : <?=$model->api_restock_info->api_restock_request_id?>
                </h3>
            </div>
        </div>
    </div>
    <!--begin::Form-->
    <?php
    $form = ActiveForm::begin([
                'id' => 'form-update-api-restock-request',
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Request ID</label>
            <div class="col-lg-4 col-form-label">
                <?=$model->api_restock_info->api_restock_request_id?>
            </div>
            <label class="col-lg-2 col-form-label">Order ID</label>
            <div class="col-lg-4 col-form-label">
                <?=$model->api_restock_info->orders_id?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Product ID</label>
            <div class="col-lg-4 col-form-label">
                <?php if($model->api_restock_info->products_id == '') { ?>
                    -
                <?php } else{ ?>
                    <?=$model->api_restock_info->products_id?>
                <?php } ?>
            </div>
            <label class="col-lg-2 col-form-label">Product Name</label>
            <div class="col-lg-4 col-form-label">
                <?php  if(!empty($model->product_info)){ ?>
                    <?=$model->product_info->products_name?>
                <?php } else {?>
                     -
                <?php } ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Created At</label>
            <div class="col-lg-4 col-form-label">
                <?=date("Y-m-d H:i:s",$model->api_restock_info->created_at)?>
            </div>

            <label class="col-lg-2 col-form-label">Updated At</label>
            <div class="col-lg-4 col-form-label">
                <?=date("Y-m-d H:i:s",$model->api_restock_info->updated_at)?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Publisher Order ID</label>
            <div class="col-lg-4 col-form-label">
                <?php  if(!empty($model->api_restock_info->publisher_order_id)){ ?>
                    <?=$model->api_restock_info->publisher_order_id?>
                <?php } else {?>
                     -
                <?php } ?>
              
            </div>
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4"> 

                <?php if($model->api_restock_info->status == '0' || $model->api_restock_info->status == '9' ){?>
                   
                    <?php if($model->api_restock_info->status == '0') { ?>
                        <input class="col-form-label" value="New Request" style="color:#575962; background-color:white; border:none" disabled>
                    <?php } if($model->api_restock_info->status == '9') { ?>
                        <input class="col-form-label" value="Margin Block" style="color:#575962; background-color:white; border:none" disabled>
                    <?php } ?> 

                
                <?php } else { 

                    if($model->api_restock_info->status == '3'){

                        echo $form->field($model->api_restock_info, 'status')->dropDownList(
                            [
                                '0' => 'New Request',
                                '1' => 'Success',
                                '2' => 'Failed/Reattempt',
                                '3' => 'Preorder',
                                '5' => 'Transaction Void, Generate New Id',
                            ], 
                            [
                                'class' => 'form-control',
                                
                            ],
                            )->label(false);

                    }

                    if($model->api_restock_info->status == '9'){

                        echo $form->field($model->api_restock_info, 'status')->dropDownList(
                            [
                                '0' => 'New Request',
                                '1' => 'Success',
                                '2' => 'Failed/Reattempt',
                                '5' => 'Transaction Void, Generate New Id',
                                '9' => 'Margin Block'
                            ], 
                            [
                                'class' => 'form-control',
                            ],
                            )->label(false);
                    }

                    if($model->api_restock_info->status == '1' || $model->api_restock_info->status == '2' || $model->api_restock_info->status == '5'){

                        echo $form->field($model->api_restock_info, 'status')->dropDownList(
                            [
                                '0' => 'New Request',
                                '1' => 'Success',
                                '2' => 'Failed/Reattempt',
                                '5' => 'Transaction Void, Generate New Id',
                            ], 
                            [
                                'class' => 'form-control',
                                
                            ],
                            )->label(false);
                    }
                   
                } ?>
                
            </div>
        </div>     
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2">

                </div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Save</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('/api-restock-request/index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>

