<?php
$this->registerJs($this->render('scripts/brand_category_script.js'));
?>

<h5>Brand Category</h5>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Brand</label>
    <div id="brand-select-container" class="col-lg-4">
        <select id="brand-select" class='selectpicker form-control' <?php echo (!$can_update_brand ? 'disabled' : '') ?>>
            <option value="0">Select</option>
            <?php if (!empty($brands)) { ?>
                <?php foreach ($brands as $brand) { ?>
                    <option value="<?= $brand['brand_id'] ?>"><?= $brand['name'] ?></option>
                <?php } ?>
            <?php } ?>
        </select>
        <div class="help-block"></div>
    </div>
    <div class="col-lg-2">
        <button type="button" id="brand-add-btn" class="btn btn-primary" onclick="addBrandCategory(1)">Add</button>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Category</label>
    <div id="category-select-container" class="col-lg-4">
        <select id="category-select" class='selectpicker form-control' <?php echo (!$can_update_brand ? 'disabled' : '') ?>>
            <option value="0">Select</option>
            <?php if (!empty($categories)) { ?>
                <?php foreach ($categories as $category) { ?>
                    <option value="<?= $category['categories_id'] ?>"><?= $category['categories_name'] ?></option>
                <?php } ?>
            <?php } ?>
        </select>
        <div class="help-block"></div>
    </div>
    <div class="col-lg-2">
        <button type="button" id="category-add-btn" class="btn btn-primary" onclick="addBrandCategory(2)">Add</button>
    </div>
</div>

<div id="brand-category-container" class="form-group m-form__group row" style="display: <?= empty($model->brand_categories) ? 'none' : 'block' ?>">
    <table id="table-brand-category"
           class="table m-table m-table--head-bg-accent table-striped- table-bordered table-hover table-checkable">
        <thead>
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Status</th>
            <th>Action</th>
        </tr>
        </thead>
        <tbody>
        <?php
        foreach ($model->brand_categories as $brandCategory) {
            if(!isset($brandCategory['name'])){
                continue;
            }
        ?>
            <tr>
                <td><?= ($brandCategory['name'] ?? '')?></td>
                <td><?= ($brandCategory['type'] == 1) ? 'Brand' : 'Category' ?></td>
                <td><?= ($brandCategory['status'] == 1) ? '<span class="m-badge m-badge--success m-badge--wide m-badge--rounded">Active</span>' : '<span class="m-badge m-badge--danger m-badge--wide m-badge--rounded">Inactive</span>' ?></td>
                <td>
                    <?php if($can_update_brand) { ?>
                        <a title='Delete' class='delete-row' style='cursor: pointer' onclick='removeBrandCategoryRow(this, <?= $brandCategory['sub_id'] ?>, <?= $brandCategory['type'] ?>, <?= $brandCategory['brand_category_id'] ?>)'><i class='la la-trash'></i></a>
                    <?php } ?>
                </td>
            </tr>
        <?php } ?>
        </tbody>
    </table>
    <div id="brand-category-delete-bucket-field"></div>
</div>


<script>

    let brandsIdBucket = <?= json_encode($model->brands_id_bucket) ?>;
    let categoriesIdBucket = <?= json_encode($model->categories_id_bucket) ?>;

    if (brandsIdBucket == null) {
        brandsIdBucket = [];
    }

    if (categoriesIdBucket == null) {
        categoriesIdBucket = [];
    }

    function addBrandCategory(typeId) {

        mApp.blockPage();


        let bucketIdList = [];
        let subId = '';
        let brandCategoryName = '';
        let url = '';
        let brandId = null;
        let errorMessageContainer = null;
        let addButton = null;

        <?php if ($model->brand_id) { ?>
            brandId = <?= $model->brand_id ?>;
        <?php } ?>

        if (typeId == 1) {
            subId = parseInt($('#brand-select option:selected').val());
            brandCategoryName = $('#brand-select option:selected').text();
            bucketIdList = brandsIdBucket;
            url = '/brand/get-brand-json?id=' + subId;
            errorMessageContainer = $("#brand-select-container .help-block");
            addButton = $('#brand-add-btn');
            disableAddButton(addButton);
        } else {
            subId = parseInt($('#category-select option:selected').val());
            brandCategoryName = $('#category-select option:selected').text();
            bucketIdList = categoriesIdBucket;
            url = '/brand/get-category-json?id=' + subId;
            errorMessageContainer = $("#category-select-container .help-block");
            addButton = $('#category-add-btn');
            disableAddButton(addButton);
        }

        errorMessageContainer.empty();

        if (subId == 0) {
            brandCategoryAddErrorMessage(errorMessageContainer, "Please select a valid brand or category.");
            enableAddButton(addButton);
            mApp.unblockPage();
            return;
        }

        if (brandId == subId) {
            brandCategoryAddErrorMessage(errorMessageContainer, "Cannot select the same brand.");
            enableAddButton(addButton);
            mApp.unblockPage();
            return;
        }

        for (let i = 0; i < bucketIdList.length; i++) {
            if (bucketIdList[i] == subId) {
                brandCategoryAddErrorMessage(errorMessageContainer, brandCategoryName + " is already in the list.");
                enableAddButton(addButton);
                mApp.unblockPage();
                return;
            }
        }

        $.ajax({
            method: "GET",
            url: url,
            dataType: 'json'
        }).done(function (data) {
            $('#brand-category-container').show();
            let typeId = data['type'];
            let type = typeId == 1 ? 'Brand' : 'Category';
            let categoryStatus = data['status'] == 1 ? '<span class="m-badge m-badge--success m-badge--wide m-badge--rounded">Active</span>' : '<span class="m-badge m-badge--danger m-badge--wide m-badge--rounded">Inactive</span>';
            let brandCategoryObject = makeBrandCategoryObject(subId, typeId);
            let markup = "<tr>" +
                "<td>" + brandCategoryName + "</td>" +
                "<td>" + type + "</td>" +
                "<td>" + categoryStatus + "</td>" +
                "<td>" +
                "<a title='Delete' class='delete-row' style='cursor: pointer' onclick='removeBrandCategoryRow(this, " + subId + ", " + typeId + ")'><i class='la la-trash'></i></a>" +
                "<input type='hidden' name='BrandForm[brand_categories_form][]' value='" + JSON.stringify(brandCategoryObject) + "'/>" +
                "</td>" +
                "</tr>";
            $("#table-brand-category").append(markup);
            $("#brand-category-" + subId).remove();
            if (typeId == 1) {
                brandsIdBucket.push(subId);
            } else {
                categoriesIdBucket.push(subId);
            }

            enableAddButton(addButton);
            mApp.unblockPage();

        }).fail(function () {
            brandCategoryAddErrorMessage(errorMessageContainer, 'Fail to add Brand or Category : ' + brandCategoryName + '. Please try again.');
            enableAddButton(addButton);
            mApp.unblockPage();
        });
    }

    function brandCategoryAddErrorMessage(container, message) {
        container.append(message);
    }

    function enableAddButton(addButton) {
        addButton.css('cursor', 'pointer');
        addButton.prop('disabled', false);
    }

    function disableAddButton(addButton) {
        addButton.css('cursor', 'default');
        addButton.prop('disabled', 'disabled');
    }

    function removeBrandCategoryRow(r, id, type, bCategoryId = null) {

        let i = r.parentNode.parentNode.rowIndex;
        let table = document.getElementById("table-brand-category");

        table.deleteRow(i);

        id = parseInt(id);
        if (type == 1) {
            brandsIdBucket = removeIdFromBucket(brandsIdBucket, id);
        } else {
            categoriesIdBucket = removeIdFromBucket(categoriesIdBucket, id);
        }

        if (bCategoryId != null) {
            let markup = "<input type='hidden' id='brand-category-" + id + "' name='BrandForm[brand_categories_id_delete_bucket][]' value='" + bCategoryId + "'/>";
            $("#brand-category-delete-bucket-field").append(markup);
        }

        const rows = table.getElementsByTagName("tr");
        let rowCount = 0;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i].getElementsByTagName("td").length > 0) {
                rowCount++;
            }
        }

        if (rowCount == 0) {
            $('#brand-category-container').css('display', 'none');
        }
    }

    function removeIdFromBucket(array, id) {
        let index = array.indexOf(id);
        if (index > -1) {
            array.splice(index, 1);
        }

        return array;
    }

    function makeBrandCategoryObject(id, type) {
        let object = {};
        object["sub_id"] = id;
        object["type"] = type;

        return object;
    }

</script>