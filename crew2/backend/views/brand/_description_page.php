<?php

use kartik\widgets\DatePicker;
use backend\assets\DualListBoxAsset;

DualListBoxAsset::register($this);

$this->registerJs($this->render('scripts/description_page_script.js'));

?>


<div class="m-portlet m-portlet--tabs">
    <div class="m-portlet__head">
        <div class="m-portlet__head-tools">
            <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                <?php foreach ($language as $i => $v) { ?>
                    <li class="nav-item m-tabs__item">
                        <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#dp_tabs_<?= $i; ?>" role="tab">
                            <?= $v; ?>
                        </a>
                    </li>
                <?php } ?>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="tab-content">
            <?php
            foreach ($language as $i => $v) {
                ?>
                <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="dp_tabs_<?= $i; ?>" role="tabpanel">
                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Notice</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_descriptions[$i][notice]")->textarea(['rows' => 6, 'readonly' => !$can_update_brand])->label(false); ?>
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Short Description</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_descriptions[$i][short_description]")->textarea(['rows' => 6, 'readonly' => !$can_update_seo])->label(false); ?>
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Description</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_descriptions[$i][description]")->textarea(['rows' => 6, 'readonly' => !$can_update_brand])->label(false); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Publisher</label>
    <div class="col-lg-10">
        <?= $form->field($model, 'brand_game_infos[publisher][value]')->textInput(['class' => 'form-control', 'readonly' => !$can_update_brand])->label(false); ?>

        <?php
        if (isset($model->brand_game_infos['publisher']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[publisher][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Developer</label>
    <div class="col-lg-10">
        <?= $form->field($model, 'brand_game_infos[developer][value]')->textInput(['class' => 'form-control', 'readonly' => !$can_update_brand])->label(false); ?>

        <?php
        if (isset($model->brand_game_infos['developer']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[developer][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Release Date</label>
    <div class="col-lg-4">
        <?=
        $form->field($model, 'brand_game_infos[release_date][value]')->widget(DatePicker::classname(), [
            'options' => ['id' => 'release_date', 'readonly' => !$can_update_brand],
            'disabled' => !$can_update_brand,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'todayBtn' => 'linked',
                'autoclose' => true,
                'todayHighlight' => true,
            ]
        ])->label(false);
        ?><?php
        if (isset($model->brand_game_infos['release_date']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[release_date][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Region</label>
    <div class="col-lg-9">
        <?=
        $form->field($model, 'brand_game_infos[game_region][value][]')->dropDownList($regions, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?><?php
        if (isset($model->brand_game_infos['game_region']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[game_region][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
    <label class="col-lg-1">
        <a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/game_database_config.php?selected_box=infolinks&config_type=region" target="_blank" rel="noopener noreferrer">Edit</a>
    </label>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Language</label>
    <div class="col-lg-9">
        <?=
        $form->field($model, 'brand_game_infos[game_language][value][]')->dropDownList($languages, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?><?php
        if (isset($model->brand_game_infos['game_language']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[game_language][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
    <label class="col-lg-1">
        <a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/game_database_config.php?selected_box=infolinks&config_type=language" target="_blank" rel="noopener noreferrer">Edit</a>
    </label>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Platform</label>
    <div class="col-lg-9">
        <?=
        $form->field($model, 'brand_game_infos[game_platform][value][]')->dropDownList($platforms, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?><?php
        if (isset($model->brand_game_infos['game_platform']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[game_platform][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
    <label class="col-lg-1">
        <a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/game_database_config.php?selected_box=infolinks&config_type=platform" target="_blank" rel="noopener noreferrer">Edit</a>
    </label>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Genre</label>
    <div class="col-lg-9">
        <?=
        $form->field($model, 'brand_game_infos[game_genre][value][]')->dropDownList($genres, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?><?php
        if (isset($model->brand_game_infos['game_genre']['brand_game_info_id'])) {
            echo $form->field($model, "brand_game_infos[game_genre][brand_game_info_id]")->hiddenInput()->label(false);
        }
        ?>
    </div>
    <label class="col-lg-1">
        <a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/game_database_config.php?selected_box=infolinks&config_type=genre" target="_blank" rel="noopener noreferrer">Edit</a>
    </label>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Related Link</label>
    <div class="col-lg-9">
        <div class="m-portlet">
            <div class="m-portlet__body">
                <div class="form-group m-form__group row">
                    <label class="col-lg-2 col-form-label">Label</label>
                    <div class="col-lg-10">
                        <div id="related-link-label-container" class="form-group related-link">
                            <input type="text" id="related-link-label" class="form-control" <?php echo (!$can_update_brand ? 'readonly' : ''); ?>>
                            <div class="help-block"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group m-form__group row">
                    <label class="col-lg-2 col-form-label">Url</label>
                    <div class="col-lg-10">
                        <div id="related-link-url-container" class="form-group related-link">
                            <input type="text" id="related-link-url" class="form-control" <?php echo (!$can_update_brand ? 'readonly' : ''); ?>>
                            <div class="help-block"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="table-related-link-container" class="m-section" style="display: <?= empty($model->related_links_form) ? 'none' : 'block' ?>">
            <div class="m-section__content">
                <table id="table-related-link" class="table m-table m-table--head-bg-accent table-striped- table-bordered table-hover table-checkable">
                    <thead>
                    <tr>
                        <th>Label</th>
                        <th>Url</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($model->related_links_form as $index => $relatedLink) { ?>
                        <tr>
                            <td><?= $relatedLink['label'] ?></td>
                            <td><?= $relatedLink['url'] ?></td>
                            <td>
                                <?php if($can_update_brand) { ?>
                                    <a title='Delete' class='delete-row' style='cursor: pointer' onclick='removeRelatedLinkRow(this, <?= $relatedLink['brand_game_info_id'] ?>)'><i class='la la-trash'></i></a>
                                <?php } ?>
                                <input type='hidden' name='BrandForm[related_links_form][$index][value]' value='<?= $relatedLink['value'] ?>'/>
                                <?php if (isset($relatedLink['brand_game_info_id'])) { ?>
                                    <input type='hidden' name='BrandForm[related_links_form][$index][brand_game_info_id]' value='<?= $relatedLink['brand_game_info_id'] ?>'/>
                                <?php } ?>
                            </td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>
                <div id="related-link-delete-bucket-field"></div>
            </div>
        </div>
    </div>
    <div class="col-lg-1">
        <button type="button" id="related-link-add-btn" class="btn btn-primary btn-sm" onclick="addRelatedLink()">Add</button>
    </div>
</div>


<script>

    function addRelatedLink() {
        let label = $('#related-link-label').val().trim();
        let labelContainer = $('#related-link-label-container .help-block');
        let url = $('#related-link-url').val().trim();
        let urlContainer = $('#related-link-url-container .help-block');

        labelContainer.empty();
        urlContainer.empty();

        if (!Boolean(label)) {
            labelContainer.append("Please fill in Label.");
            return;
        }

        if (!Boolean(url)) {
            urlContainer.append("Please fill in Url.");
            return;
        }

        if (!isValidUrl(url)) {
            urlContainer.append("Please enter a valid Url.");
            return;
        }

        let relatedLinkObject = makeRelatedLinkObject(label, url);

        let markup = "<tr>" +
            "<td>" + label + "</td>" +
            "<td>" + url + "</td>" +
            "<td>" +
            "<a title='Delete' class='delete-row' style='cursor: pointer' onclick='removeRelatedLinkRow(this)'><i class='la la-trash'></i></a>" +
            "<input type='hidden' name='BrandForm[related_links_form][][value]' value='" + JSON.stringify(relatedLinkObject) + "'/>" +
            "</td>" +
            "</tr>";

        $("#table-related-link-container").show();
        $("#table-related-link").append(markup);

        $("#related-link-label").val('');
        $("#related-link-url").val('');

        $('#related-link-add-btn').css('cursor', 'default');
        $('#related-link-add-btn').prop('disabled', 'disabled');
    }

    function removeRelatedLinkRow(r, brandGameInfoId = null) {
        let i = r.parentNode.parentNode.rowIndex;
        let table = document.getElementById("table-related-link");

        table.deleteRow(i);

        if (brandGameInfoId != null) {
            let markup = "<input type='hidden'  name='BrandForm[related_links_id_delete_bucket][]' value='" + brandGameInfoId + "'/>";
            $("#related-link-delete-bucket-field").append(markup);
        }

        const rows = table.getElementsByTagName("tr");
        let rowCount = 0;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i].getElementsByTagName("td").length > 0) {
                rowCount++;
            }
        }

        if (rowCount == 0) {
            $('#table-related-link-container').css('display', 'none');
        }
    }

    function makeRelatedLinkObject(label, url) {
        return {
            'label': label,
            'url': url
        };
    }

    function isValidUrl(url) {
        const regexp = /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/;
        return regexp.test(url)
    }

</script>