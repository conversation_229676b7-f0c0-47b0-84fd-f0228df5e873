<?php

$pages = [
    0 => 'General',
    1 => 'Description',
    2 => 'SEO',
    3 => 'Restriction',
];

$language = Yii::$app->enum->getLanguage('listData');
$this->registerJs($this->render('scripts/form_script.js'));
$can_update_seo = Yii::$app->user->can('[SEO] Brand');
$can_update_brand = Yii::$app->user->can('[Product] Edit Brand');

?>

<div class="m-portlet__body">
    <ul class="nav nav-pills nav-pills--accent m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm"
        role="tablist">
        <li class="nav-item m-tabs__item">
            <a href="#p_tabs_0" id="page_0" class="nav-link m-tabs__link active" data-toggle="tab" role="tab">
                <i class="la la-cog"></i>
                General
            </a>
        </li>
        <li class="nav-item m-tabs__item">
            <a href="#p_tabs_1" id="page_1" class="nav-link m-tabs__link" data-toggle="tab" role="tab">
                <i class="la la-comment"></i>
                Description
            </a>
        </li>
        <li class="nav-item m-tabs__item">
            <a href="#p_tabs_2" id="page_2" class="nav-link m-tabs__link" data-toggle="tab" role="tab">
                <i class="la la-newspaper-o"></i>
                SEO
            </a>
        </li>
        <li class="nav-item m-tabs__item">
            <a href="#p_tabs_3" id="page_3" class="nav-link m-tabs__link" data-toggle="tab" role="tab">
                <i class="la la-map-marker"></i>
                Restriction
            </a>
        </li>
    </ul>

    <div class="tab-content">

        <?= $form->field($model, "brand_id")->hiddenInput(['value' => $model->brand_id])->label(false) ?>
        <?php foreach ($pages as $pageIndex => $pageName) { ?>

            <div class="tab-pane <?= ($pageIndex == 0 ? 'show active' : ''); ?>" id="p_tabs_<?= $pageIndex; ?>" role="tabpanel">

                <?php if ($pageIndex === 0) { ?>

                    <?= $this->render('_general_page', [
                        'form' => $form,
                        'model' => $model,
                        'language' => $language,
                        'brands' => $brands,
                        'categories' => $categories,
                        'can_update_brand' => $can_update_brand,
                        'can_update_seo' => $can_update_seo,
                    ]) ?>

                <?php } ?>

                <?php if ($pageIndex === 1) { ?>

                    <?= $this->render('_description_page', [
                        'form' => $form,
                        'model' => $model,
                        'regions' => $regions,
                        'languages' => $languages,
                        'platforms' => $platforms,
                        'genres' => $genres,
                        'language' => $language,
                        'can_update_brand' => $can_update_brand,
                        'can_update_seo' => $can_update_seo,
                    ]) ?>

                <?php } ?>

                <?php if ($pageIndex === 2) { ?>

                    <?= $this->render('_seo_page', [
                        'form' => $form,
                        'model' => $model,
                        'language' => $language,
                        'can_update_brand' => $can_update_brand,
                        'can_update_seo' => $can_update_seo,
                    ]) ?>

                <?php } ?>

                <?php if ($pageIndex === 3) { ?>

                    <?= $this->render('_restriction_page', [
                        'form' => $form,
                        'model' => $model,
                        'countries' => $countries,
                        'can_update_brand' => $can_update_brand,
                        'can_update_seo' => $can_update_seo,
                    ]) ?>

                <?php } ?>

            </div>

        <?php } ?>

    </div>

</div>

<?php
if(!$can_update_brand){
    $this->registerJs(<<<SCRIPT
        setTimeout(function(){
        $(".bootstrap-duallistbox-container").find("*").prop("disabled",true);
        },500);
SCRIPT,
        \yii\web\View::POS_READY);
}
