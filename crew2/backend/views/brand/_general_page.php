<?php

use yii\helpers\Html;
use kartik\widgets\SwitchInput;
use kartik\widgets\FileInput;

$this->registerJs($this->render('scripts/general_page_script.js'));

if(empty($model->sort_order)){
    $model->sort_order = 500000;
}
?>

<div class="m-portlet m-portlet--tabs">
    <div class="m-portlet__head">
        <div class="m-portlet__head-tools">
            <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                <?php foreach ($language as $i => $v) { ?>
                    <li class="nav-item m-tabs__item">
                        <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#gp_tabs_<?= $i; ?>" role="tab">
                            <?= $v; ?>
                        </a>
                    </li>
                <?php } ?>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="tab-content">
            <?php
            foreach ($language as $i => $v) {
                ?>
                <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="gp_tabs_<?= $i; ?>" role="tabpanel">
                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Name<?= ($i == 1 ? '<span style="color: red;">*</span>' : ''); ?></label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_descriptions[$i][name]")->textInput(['readonly' => !$can_update_brand])->label(false); ?>
                            <?php if (isset($model->brand_descriptions[$i]['brand_description_id'])) {
                                echo $form->field($model, "brand_descriptions[$i][brand_description_id]")->hiddenInput()->label(false);
                            } ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<div id="preview-image" class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Image<span style="color: red;">*</span></label>
    <div class="col-lg-10">
        <?php
        $clientValidation = ['enableClientValidation' => true];
        if (!empty($model->image)) {
            $clientValidation = ['enableClientValidation' => false];
            ?>
            <?php
            if (!empty($model->temp_file_image)) {
                echo Html::input('hidden', 'BrandForm[temp_file_image]', $model->temp_file_image, ['id' => 'temp_file_image', 'readonly' => !$can_update_brand]);
            }
            ?>
            <div id="file-preview" class="file-preview">
                <div id="remove-img-view" class="close text-right <?php echo (!$can_update_brand ? 'hide' : '') ?>">×</div>
                <div class="file-preview-thumbnails">
                    <div class="file-preview-frame">
                        <?php echo Html::img($model->temp_file_image, ['class' => 'file-preview-image', 'alt' => $model->name_search, 'title' => $model->name_search]); ?>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
            <?php echo Html::button('<i class="fa fa-ban"></i> Remove', ['class' => 'btn btn-default btn-remove-img', 'id' => 'btn-remove-img', 'disabled' => !$can_update_brand]); ?>
        <?php } ?>

        <div id="file-src-container-image" class="<?php echo empty($model->image) ? '' : 'hide'; ?>">
            <?php
            echo $form->field($model, 'image', $clientValidation)->widget(FileInput::classname(), [
                'pluginOptions' => [
                    'resizeImage' => true,
                    'showCaption' => false,
                    'showUpload' => false,
                    'showRemove' => false,
                    'browseClass' => 'btn btn-primary',
                    'browseIcon' => '<i class="fa fa-camera"></i> ',
                    'browseLabel' => 'Select',
                ],
                'options' => ['accept' => 'image/*', 'readonly' => !$can_update_brand]
            ])->label(false);
            ?>
        </div>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Show in Search Result</label>
    <div class="col-lg-3">
        <?php
        echo $form
            ->field($model, 'show_in_search_result')
            ->widget(SwitchInput::class, [
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ],
                'readonly' => !$can_update_brand
            ])->label(false);
        ?>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Search Keyword</label>
    <div class="col-lg-10">
        <?= $form->field($model, 'search_keyword')->textarea(['rows' => 5, 'readonly' => !$can_update_brand])->label(false); ?>
        <span class="m-form__help">Separate multiple Search Keyword with comma</span>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Status</label>
    <div class="col-lg-3">
        <?php
        echo $form
            ->field($model, 'status')
            ->widget(SwitchInput::class, [
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ],
                'readonly' => !$can_update_brand
            ])->label(false);
        ?>
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Sort Order</label>
    <div class="col-lg-4">
        <?= $form->field($model, 'sort_order')->textInput(['class' => 'form-control','readonly' => !$can_update_brand])->label(false); ?>
    </div>
</div>

<div class="m-separator m-separator--dashed m-separator--lg"></div>


<?= $this->render('_brand_category', [
    'model' => $model,
    'brands' => $brands,
    'categories' => $categories,
    'can_update_brand' => $can_update_brand
]) ?>
