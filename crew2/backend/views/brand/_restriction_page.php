<?php

use backend\assets\DualListBoxAsset;

DualListBoxAsset::register($this);

$this->registerJs($this->render('scripts/restriction_page_script.js'));

?>
<div class="form-group m-form__group row">
    <label class="col-lg-2">Soft Block
        <i data-toggle="m-tooltip" data-width="auto" class="m-form__heading-help-icon flaticon-info"
           title="Ecommerce > Regional Setting > Location restriction"></i></label>
    <div class="col-lg-10">
        <?=
        $form->field($model, 'brand_soft_blocks[value][]')->dropDownList($countries, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?>
    </div>
</div>

<div class="m-separator m-separator--dashed m-separator--m"></div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Hard Block
        <i data-toggle="m-tooltip" data-width="auto" class="m-form__heading-help-icon flaticon-info"
           title="Customer's IP restriction"></i></label>
    <div class="col-lg-10">
        <?=
        $form->field($model, 'brand_hard_blocks[value][]')->dropDownList($countries, [
            'class' => 'form-control list m-datatable__pager-size',
            'multiple size' => '12',
            'data-target' => 'available'
        ])->label(false);
        ?>
    </div>
</div>