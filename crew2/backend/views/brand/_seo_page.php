
<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">SEO URL<span style="color: red;">*</span></label>
    <div class="col-lg-10">
        <?= $form->field($model, 'seo_url')->textInput(['class' => 'form-control', 'readonly' => !$can_update_brand])->label(false); ?>
        <input type='hidden' id='seo-url-exist-validation' name='BrandForm[seo_url_validation]'/>
    </div>
</div>

<div class="m-portlet m-portlet--tabs">
    <div class="m-portlet__head">
        <div class="m-portlet__head-tools">
            <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                <?php foreach ($language as $i => $v) { ?>
                    <li class="nav-item m-tabs__item">
                        <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab"
                           href="#sp_tabs_<?= $i; ?>" role="tab">
                            <?= $v; ?>
                        </a>
                    </li>
                <?php } ?>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="tab-content">
            <?php
            foreach ($language as $i => $v) {

                if (isset($model->brand_metadatas[$i]['brand_metadata_id'])) {
                    echo $form->field($model, "brand_metadatas[$i][brand_metadata_id]")->hiddenInput()->label(false);
                }

                ?>
                <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="sp_tabs_<?= $i; ?>" role="tabpanel">
                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Meta Title</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_metadatas[$i][meta_title]")->textInput(['readonly' => !$can_update_seo])->label(false); ?>
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Meta Keyword</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_metadatas[$i][meta_keyword]")->textarea(['rows' => 6, 'readonly' => !$can_update_seo])->label(false); ?>
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-lg-2 col-form-label">Meta Description</label>
                        <div class="col-lg-10">
                            <?= $form->field($model, "brand_metadatas[$i][meta_description]")->textarea(['rows' => 6, 'readonly' => !$can_update_seo])->label(false); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>