<?php

use yii\helpers\Url;
use yii\widgets\ActiveForm;

$form = ActiveForm::begin([
    'id' => 'form-brand-id',
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right',
    ],
]);

$this->title = 'Brand';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = ['label' => 'Brand', 'url' => ['/brand/index']];
$this->params['layoutContent'] = 'block';

?>

<div id="brand-validation-error-message-container" class="alert alert-danger fade show" role="alert"
     style="display: none">
    <strong>Oops!</strong><br>
    <div id="general-error-container" style="display: none">
        <u>General</u>
        <ul id="general-error-list"></ul>
    </div>
    <div id="seo-error-container" style="display: none">
        <u>SEO</u>
        <ul id="seo-error-list"></ul>
    </div>
</div>

<div class="m-portlet m-portlet--mobile">

    <div class="m-portlet__head btn-accent" style="background: #00c5dc">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text" style="color:white">
                    <?= ($action == 'create') ? 'New' : $model->name_search ?>
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <button id="brand-form-submit-btn" type="submit"
                            class="m-portlet__nav-link btn btn-secondary m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="la la-save"></i></button>
                </li>
            </ul>
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a id="brand-form-cancel-link" href="<?= Url::toRoute('/brand/index'); ?>"
                       class="m-portlet__nav-link m-portlet__nav-link--icon">
                        <i class="la la-close" style="color: white;"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <?= $this->render('_form', [
        'form' => $form,
        'model' => $model,
        'countries' => $countries,
        'regions' => $regions,
        'languages' => $languages,
        'platforms' => $platforms,
        'genres' => $genres,
        'brands' => $brands,
        'categories' => $categories
    ]) ?>

</div>


<?php ActiveForm::end(); ?>

