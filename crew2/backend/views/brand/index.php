<?php

use backend\models\BrandForm;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\helpers\Html;

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = $this->title = 'Brand';
$this->params['layoutContent'] = 'block';

$brandStatuses = BrandForm::getBrandStatuses();
?>

<div class="brand-list-index">

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title"></div>
            </div>

            <div class="m-portlet__head-tools">
                <div class="m-portlet__head-tools">
                    <div class="m--align-right">
                        <a href="<?= Url::toRoute('brand/create'); ?>"
                           class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                        </a>
                    </div>
                </div>
            </div>

        </div>

        <div class="m-portlet__body">
            <div class="m-section">
                <div class="m-section__content">
                    <?=
                    GridView::widget([
                        'dataProvider' => $provider,
                        'filterModel' => $filter,
                        'filterUrl' => $filter->filterUrl,
                        'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
                        'columns' => [
                            [
                                'header' => '#',
                                'content' => function ($model, $key, $index) use ($filter) {
                                    return $index + $filter->startAt;
                                },
                            ],
                            [
                                'header' => 'Brand',
                                'attribute' => 'name_search',
                                'contentOptions' => ['style' => 'width: 50%;word-wrap: break-word;'],
                            ],
                            [
                                'header' => 'Status',
                                'attribute' => 'status',
                                'filter' => Html::activeDropDownList(
                                    $filter,
                                    'status',
                                    $brandStatuses,
                                    ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']
                                ),
                                'content' => function ($model) {
                                    return ($model['status'] == '1') ? '<span class="m-badge m-badge--success m-badge--wide m-badge--rounded">Active</span>' : '<span class="m-badge m-badge--danger m-badge--wide m-badge--rounded">Inactive</span>';
                                }
                            ],
                            [
                                'header' => 'Action',
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{update} {delete}'),
                                'contentOptions' => ['style' => 'white-space: nowrap;'],
                                'urlCreator' => function ($action, $model) {
                                    $brandId = $model['brand_id'];

                                    if ($action === 'update') {
                                        return Url::toRoute("brand/update?id=$brandId");
                                    }

                                    if ($action === 'delete') {
                                        return Url::toRoute("brand/delete?id=$brandId");
                                    }

                                    return '';
                                }
                            ],
                        ],
                    ]);

                    echo $filter->renderPageSizer();
                    ?>
                </div>
            </div>
        </div>
    </div>

</div>