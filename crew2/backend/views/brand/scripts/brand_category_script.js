$(document).ready(function () {

    let brandAddButton = $('#brand-add-btn');
    let categoryAddButton = $('#category-add-btn');
    let brandErrorMessageContainer = $("#brand-select-container .help-block");
    let categoryErrorMessageContainer = $("#category-select-container .help-block");

    disableAddButton(brandAddButton);
    disableAddButton(categoryAddButton);

    onChangeSelectBox($('#brand-select'), brandAddButton, brandErrorMessageContainer);
    onChangeSelectBox($('#category-select'), categoryAddButton, categoryErrorMessageContainer);

    function onChangeSelectBox(selectBox, addButton, errorMessageContainer) {
        $(selectBox).on("change", function(){
            if($(this).val() == 0){
                disableAddButton(addButton);
            }else{
                enableAddButton(addButton);
            }
            errorMessageContainer.empty();
        });
    }

    function disableAddButton(addButton) {
        addButton.css('cursor', 'default');
        addButton.prop('disabled', 'disabled');
    }

    function enableAddButton(addButton) {
        addButton.css('cursor', 'pointer');
        addButton.prop('disabled', false);
    }
});