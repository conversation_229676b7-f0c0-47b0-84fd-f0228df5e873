$(document).ready(function () {

    let regionList = $('select[name=\"BrandForm[brand_game_infos][game_region][value][]\"]');
    regionList.bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: 'Selected',
        nonSelectedListLabel: 'Options'
    });

    let languageList = $('select[name=\"BrandForm[brand_game_infos][game_language][value][]\"]');
    languageList.bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: 'Selected',
        nonSelectedListLabel: 'Options'
    });

    let platformList = $('select[name=\"BrandForm[brand_game_infos][game_platform][value][]\"]');
    platformList.bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: 'Selected',
        nonSelectedListLabel: 'Options'
    });

    let genreList = $('select[name=\"BrandForm[brand_game_infos][game_genre][value][]\"]');
    genreList.bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: 'Selected',
        nonSelectedListLabel: 'Options'
    });

    let relatedLinkAddButton = $('#related-link-add-btn');

    relatedLinkAddButton.css('cursor', 'default');
    relatedLinkAddButton.prop('disabled', 'disabled');

    $('.related-link input').keyup(function () {

        let empty = false;
        $('.related-link input').each(function () {
            if ($(this).val().length == 0) {
                empty = true;
            }
        });

        if (empty) {
            relatedLinkAddButton.attr('disabled', 'disabled');
            relatedLinkAddButton.css('cursor', 'default');
        } else {
            relatedLinkAddButton.attr('disabled', false);
            relatedLinkAddButton.css('cursor', 'pointer');
        }
    });

});