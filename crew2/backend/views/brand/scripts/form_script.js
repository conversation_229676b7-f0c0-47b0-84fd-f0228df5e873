$(document).ready(function () {

    $("input").keydown(function(event){
        if(event.keyCode == 13) {
            event.preventDefault();
            return false;
        }
    });

    $('#form-brand-id').on('beforeValidate', function (event) {
        mApp.blockPage();

        const nameVal = $("input[name='BrandForm[brand_descriptions][1][name]']").val().trim();
        const seoUrlVal = $('#brandform-seo_url').val().trim();
        const image = $('#brandform-image');
        const temp_image = $('#temp_file_image').length ? $('#temp_file_image').val().trim() : null;
        const brandErrorMessageContainer = $('#brand-validation-error-message-container');
        const generalErrorContainer = $('#general-error-container');
        const generalErrorList = $('#general-error-list');
        const seoErrorContainer = $('#seo-error-container');
        const seoErrorList = $('#seo-error-list');
        const seoUrlExistValidationInput = $("input[name='BrandForm[seo_url_validation]']");
        let seoUrlExistValidation = seoUrlExistValidationInput.length && seoUrlExistValidationInput.val().length ? seoUrlExistValidationInput.val().trim() : null;
        let errorCount = 0;
        let errorObj = {};
        errorObj['General'] = [];
        errorObj['SEO'] = [];

        brandErrorMessageContainer.hide();
        generalErrorContainer.hide();
        seoErrorContainer.hide();
        generalErrorList.empty();
        seoErrorList.empty();

        $('#brand-form-submit-btn').prop("disabled",true);
        $('#brand-form-cancel-link').on("click", false);

        if (!Boolean(nameVal)) {
            errorObj['General'].push('Name');
        }

        if (!Boolean(temp_image) && image.get(0).files.length === 0) {
            errorObj['General'].push('Image');
        }

        if (!Boolean(seoUrlVal)) {
            errorObj['SEO'].push('SEO Url');
        }

        if (Boolean(seoUrlVal) && seoUrlVal.length > 255) {
            errorObj['SEO'].push('SEO Url exceeds max characters');
        }

        if (errorObj['General'].length > 0) {
            generalErrorContainer.show();
            for(let i=0;i<errorObj['General'].length;i++) {
                const errorMessage = '<li>' + errorObj['General'][i] + '</li>';
                generalErrorList.append(errorMessage);
                errorCount++;
            }
        }

        if (errorObj['SEO'].length > 0) {
            seoErrorContainer.show();
            for(let i=0;i<errorObj['SEO'].length;i++) {
                const errorMessage = '<li>' + errorObj['SEO'][i] + '</li>';
                seoErrorList.append(errorMessage);
                errorCount++;
            }
        }

        if (Boolean(seoUrlExistValidation)) {
            seoErrorList.empty();
            seoErrorContainer.show();
            seoErrorList.append('<li>' + seoUrlExistValidation + '</li>');
            errorCount++;
        }
        const seoUrl = $("input[name='BrandForm[seo_url]']").val().trim();
        const seoUrlHelpBlock = ".field-brandform-seo_url .help-block";

        if (!checkIfValidURLSlug(seoUrl)) {
            $(seoUrlHelpBlock).empty();
            $(seoUrlHelpBlock).append("SEO Url is not valid.");
            seoErrorList.empty();
            seoErrorContainer.show();
            seoErrorList.append('<li>SEO Url is not valid.</li>');
            errorCount++;
        }

        $('#brand-form-submit-btn').prop("disabled",false);
        $('#brand-form-cancel-link').off("click");

        const hasErrors = errorCount > 0;
        if (hasErrors) {
            brandErrorMessageContainer.show();
            mApp.unblockPage();
            return false;
        }
    });

    const brandNameEnInput = "input[name='BrandForm[brand_descriptions][1][name]']";
    const seoUrlInput = "input[name='BrandForm[seo_url]";

    $(seoUrlInput).focusout(function(){
        validateSeoUrl();
    });

    $(seoUrlInput).on("change keyup",function() {
        validateSeoUrl();
    });

    $(brandNameEnInput).focusout(function () {
        validateSeoUrl();

        const name = $(this).val().trim();
        const inputBrandNameEnErrorMessageBlock = $('.field-brandform-brand_descriptions-1-name .help-block');

        inputBrandNameEnErrorMessageBlock.empty();

        if (!Boolean(name)) {
            inputBrandNameEnErrorMessageBlock.append("Name cannot be blank.");
        }
    });

    $(brandNameEnInput).on("change keyup input",function() {
        const name = $(this).val().trim();
        const brandId = $("input[name='BrandForm[brand_id]']").val().trim();

        if (Boolean(name) && !Boolean(brandId)) {
            const urlSlug = slugify(name);

            $('#brandform-seo_url').val(urlSlug);
        }
    });

    function checkIfValidURLSlug(str) {
        // Regular expression to check if string is a valid url slug
        const regexExp = /^[a-z0-9]+(?:-[a-z0-9]+)*$/g;

        return regexExp.test(str);
    }

    function slugify(text) {
        return text
            .toString()                           // Cast to string (optional)
            .normalize('NFKD')            // The normalize() using NFKD method returns the Unicode Normalization Form of a given string.
            .toLowerCase()                  // Convert the string to lowercase letters
            .trim()                                  // Remove whitespace from both sides of a string (optional)
            .replace(/\s+/g, '-')            // Replace spaces with -
            .replace(/[^\w\-]+/g, '')     // Remove all non-word chars
            .replace(/\-\-+/g, '-')       // Replace multiple - with single -
            .replace(/\-$/g, '');
    }

    function validateSeoUrl() {
        const brandId = $("input[name='BrandForm[brand_id]']").val().trim();
        const seoUrl = $("input[name='BrandForm[seo_url]']").val().trim();
        const seoUrlHelpBlock = ".field-brandform-seo_url .help-block";

        $(seoUrlHelpBlock).empty();

        $.ajax({
            method: "GET",
            url: '/brand/check-seo-url-exists?seo_url=' + seoUrl + '&brand_id=' + brandId,
            dataType: 'json',
        }).done(function (data) {
            const $hiddenInput = $("input[name='BrandForm[seo_url_validation]']");
            if (typeof data == "boolean" && data === true) {
                $(seoUrlHelpBlock).empty();
                $(seoUrlHelpBlock).append("SEO Url exists.");
                $hiddenInput.val("SEO Url exists.");
            } else {
                $hiddenInput.val("");
            }
        });
    }

});
