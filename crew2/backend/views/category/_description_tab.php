<?php

use backend\models\CategoryDescriptionForm;

$language = Yii::$app->enum->getLanguage('listData');
?>

<div class="tab-pane" role="tabpanel" id="tab2">
    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__head">
            <div class="m-portlet__head-tools">
                <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                    <?php foreach ($language as $i => $v) { ?>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link <?= ($i === 1 ? 'active' : '') ?>" data-toggle="tab" href="#f_tabs_<?= $i ?>" role="tab">
                                <?= $v ?>
                            </a>
                        </li>
                    <?php } ?>
                </ul>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="tab-content">
                <?php foreach ($language as $i => $v) { ?>
                    <?php
                    $category_form_model = new CategoryDescriptionForm();
                    ?>
                    <div class="tab-pane <?= ($i === 1 ? 'show active' : '') ?>" id="f_tabs_<?= $i ?>" role="tabpanel">
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Notice</label>
                            <div class="col-lg-10">
                                <?= $form->field($category_form_model, "[$i]notice")
                                    ->textarea(['rows' => 6,'value' => $model['category_description'][$i]['notice'] ?? '', 'readonly' => !$can_update_category])
                                    ->label(false) ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Short Description</label>
                            <div class="col-lg-10">
                                <?= $form->field($category_form_model, "[$i]short_description")
                                    ->textarea(['rows' => 6, 'value' => $model['category_description'][$i]['short_description'] ?? '', 'readonly' => !$can_update_seo])
                                    ->label(false) ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Description</label>
                            <div class="col-lg-10">
                                <?= $form->field($category_form_model, "[$i]description")
                                    ->textarea(['rows' => 6, 'value' => $model['category_description'][$i]['description'] ?? '', 'readonly' => !$can_update_category])
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>