<?php

use yii\helpers\Html;
use kartik\widgets\SwitchInput;
use kartik\widgets\FileInput;
use backend\models\CategoryDescriptionForm;
use common\models\BrandOptions;

$language = Yii::$app->enum->getLanguage('listData');

$brandList = (new BrandOptions())->getBrandList();

if(empty($model->sort_order)){
    $model->sort_order = 50000;
}
?>

<div class="tab-pane active" role="tabpanel" id="tab1">
    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__head">
            <div class="m-portlet__head-tools">
                <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                    <?php foreach ($language as $i => $v) { ?>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link <?= ($i === 1 ? 'active' : '') ?>" data-toggle="tab" href="#m_tabs_<?= $i ?>" role="tab">
                                <?= $v ?>
                            </a>
                        </li>
                    <?php } ?>
                </ul>
                <?= $form->field($model, "category_id")->hiddenInput(['maxlength' => true])->label(false) ?>
                <?= $form->field($model, "categories_id")->hiddenInput(['maxlength' => true])->label(false) ?>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="tab-content">
                <?php foreach ($language as $i => $v) { ?>
                    <?php
                    $category_form_model = new CategoryDescriptionForm();
                    ?>
                    <div class="tab-pane <?= ($i === 1 ? 'show active' : '') ?>" id="m_tabs_<?= $i ?>" role="tabpanel">
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Name<?= ($i == 1 ? '<span style="color: red;">*</span>' : '') ?></label>
                            <div class="col-lg-10">
                                <?= $form->field($category_form_model, "[$i]name", ['enableClientValidation' => $i === 1])
                                    ->textInput(['maxlength' => true, 'id' => "category-name-$i", 'value' => $model['category_description'][$i]['name'] ?? '', 'readonly' => !$can_update_category])
                                    ->label(false) ?>
                                <?= $form->field($category_form_model, "[$i]language_id")->hiddenInput(['value' => $i])->label(false) ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Primary Brand<span style="color: red;">*</span></label>
        <div class="col-lg-10">
            <?=
            $form->field($model, 'brand_id')->dropDownList($brandList, [
                'prompt' => 'Select',
                'class' => 'selectpicker m-datatable__pager-size',
                'disabled' => !$can_update_category
            ])->label(false);
            ?>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Image</label>
        <div class="col-lg-10">
            <?php
            $clientValidation = ['enableClientValidation' => true];
            if (!empty($model->image_url)) {
                $clientValidation = ['enableClientValidation' => false];
                ?>

                <div id="file-preview" class="file-preview">
                    <div id="remove-img-view" class="close text-right btn-remove-img <?php echo (!$can_update_category ? 'hide' : '') ?>">×</div>
                    <div class="file-preview-thumbnails">
                        <div class="file-preview-frame">
                            <?php echo Html::img($model->image_url, ['class' => 'file-preview-image']); ?>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <?php echo Html::button('<i class="fa fa-ban"></i> Remove', ['class' => 'btn btn-default btn-remove-img', 'id' => 'btn-remove-img', 'disabled' => !$can_update_category]); ?>
            <?php } ?>

            <div id="file-src-container-image" class="<?php echo empty($model->image_url) ? '' : 'hide'; ?>">
                <?= $form->field($model, 'category_image', $clientValidation)->widget(FileInput::class, [
                    'pluginOptions' => [
                        'resizeImage' => true,
                        'showCaption' => false,
                        'showUpload' => false,
                        'showRemove' => false,
                        'browseClass' => 'btn btn-primary',
                        'browseIcon' => '<i class="fa fa-camera"></i> ',
                        'browseLabel' => 'Select',

                    ],
                    'options' => ['accept' => 'image/*', 'readonly' => !$can_update_category]
                ])->label(false)
                ?>
            </div>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'status')->widget(SwitchInput::class, [
                'value' => '1',
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                    'size' => 'medium',
                    'handleWidth'=>20,
                ],
                'readonly' => !$can_update_category
            ])->label(false) ?>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Sort Order<span style="color: red;">*</span></label>
        <div class="col-lg-4">
            <?= $form->field($model, 'sort_order')->textInput(['readonly' => !$can_update_category])->label(false) ?>
        </div>
    </div>
</div>

<?php
$script = <<<JS
$(document).ready(function () {
    const btnRemoveImg = $('#btn-remove-img');
    btnRemoveImg.removeClass('btn-default').addClass('btn-primary btn-sm');
    btnRemoveImg.click(function() {
        removeImageComponents();
    });

    $('#remove-img-view').click(function() {
        removeImageComponents();
    });
    
});

function removeImageComponents() {
    $('#file-preview').remove();
    $('#file-src-container-image').removeClass('hide');
    $('#btn-remove-img').remove();
    $('#temp_file_image').remove();
}
    
JS;
$this->registerJs($script, $this::POS_END);
?>
