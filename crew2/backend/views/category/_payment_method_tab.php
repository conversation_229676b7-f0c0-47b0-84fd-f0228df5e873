<?php

use common\models\PipwavePaymentMapper;

$payment_method_list = PipwavePaymentMapper::getOGPaymentList() ?? [];
?>

<div class="tab-pane" role="tabpanel" id="tab7">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Payment Type</label>
        <div class="col-lg-10">
            <div class="m-checkbox-inline" style="padding:5px">
                <label class="m-checkbox" style="margin-right:50px">
                    <input type="checkbox" name="category_payment_type[]"  value="1"
                        <?= isset($model->category_payment_type[1]) ? 'checked' : ''  ?>> RP
                    <span></span>
                </label>
                <label class="m-checkbox" style="margin-right:50px">
                    <input type="checkbox" name="category_payment_type[]" value="2"
                        <?= isset($model->category_payment_type[2]) ? 'checked' : ''  ?>> NRP
                    <span></span>
                </label>
                <label class="m-checkbox" style="margin-right:50px">
                    <input type="checkbox" name="category_payment_type[]" value="3"
                        <?= isset($model->category_payment_type[3]) ? 'checked' : ''  ?>> Semi RP
                    <span></span>
                </label>
            </div>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2">
            Payment Method
        </label>
        <div class="col-lg-10">
            <?php echo
            $form->field($model, 'category_payment_restriction')->dropDownList($payment_method_list, [
                'name' => 'category_payment_restriction[]',
                'class' => 'form-control list m-datatable__pager-size form-multiple-list',
                'multiple size' => '12',
                'data-target' => 'available'
            ])->label(false);
            ?>
        </div>
    </div>
</div>