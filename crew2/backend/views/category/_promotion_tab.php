<?php

use yii\helpers\Html;
use kartik\datetime\DateTimePicker;
use common\models\CategoriesDiscountRules;
use common\models\CustomersGroups;

$discount_rules_list = CategoriesDiscountRules::getCategoriesDiscountRules() ?? [];
$customer_group_list = CustomersGroups::getGroupList() ?? [];
?>

<div class="tab-pane" role="tabpanel" id="tab4">
    <div class="promotion-discount-rule m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        Discount
                    </h3>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">Discount Rule</label>
                <div class="col-lg-4">
                    <?= Html::dropDownList('', $model->cdrules_id ?? '', $discount_rules_list, [
                        'id' => 'select_cdrules_id',
                        'prompt' => 'Select',
                        'class' => 'selectpicker m-datatable__pager-size'
                    ]) ?>
                    <?= $form->field($model, "cdrules_id")->hiddenInput(['id' => 'cdrules_id'])->label(false) ?>
                </div>
                <div class="col-lg-4 d-inline">
                    <button id="add-discount" type="button" class="btn btn-primary m-btn--wide">Add</button>&nbsp;
                    <?= Html::a("Edit",Yii::$app->params['oldcrew.baseUrl'] . "/categories_discount_group_rules.php",['target'=>'_blank']) ?>
                </div>
            </div>
            <div id="d-discount-rules" class="form-group m-form__group row hide">
                <div class="col-lg-11">
                    <div class=" m-portlet__body">
                        <div class="m-section">
                            <div class="m-section__content">
                                <div class="table-responsive">
                                    <table id="tbl-discount-rules" class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>Customer Group</th>
                                            <th>Discount %</th>
                                            <th>OffGamers Point</th>
                                        </tr>
                                        </thead>
                                        <tbody>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-1">
                    <button id="delete-discount" type="button" class="remove-item-4 btn btn-danger" style="margin-top:28px">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php if ($model->category_id) { ?>
        <div class="promotion-discount-rule m-portlet m-portlet--mobile">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Promotion
                        </h3>
                    </div>
                </div>
            </div>
            <div class="promotion m-portlet__body">
                <div class="promotion-form">
                    <div class="form-group m-form__group row">
                        <div class="col-lg-4">
                            <div class="m-form__group m-form__group--inline">
                                <div class="m-form__label">
                                    <label>Start Date</label>
                                </div>
                                <?= DateTimePicker::widget([
                                    'name' => 'CategoryPromotion[start_date]',
                                    'id' => 'promotion-start-date',
                                    'removeButton' => false,
                                    'pickerButton' => ['icon' => 'time'],
                                    'pluginOptions' => [
                                        'autoclose' => true,
                                        'format' => 'yyyy-mm-dd hh:ii'
                                    ]
                                ])
                                ?>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="m-form__group m-form__group--inline">
                                <div class="m-form__label">
                                    <label>End Date</label>
                                </div>
                                <div>
                                    <?= DateTimePicker::widget([
                                        'name' => 'CategoryPromotion[end_date]',
                                        'id' => 'promotion-end-date',
                                        'removeButton' => false,
                                        'pickerButton' => ['icon' => 'time'],
                                        'pluginOptions' => [
                                            'autoclose' => true,
                                            'format' => 'yyyy-mm-dd hh:ii'
                                        ]
                                    ]) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-2">
                            <div class="m-form__group m-form__group--inline">
                                <button id="add-promotion" type="button" class="btn btn-accent m-btn--wide">New</button>
                            </div>
                        </div>
                    </div>

                    <?= Html::hiddenInput('CategoryPromotion[category_id]', $model->category_id) ?>
                    <?= Html::hiddenInput('CategoryPromotion[category_promotion_id]', '') ?>

                    <div class="m-portlet m-portlet--bordered m-portlet--bordered-semi m-portlet--rounded">
                        <div class="m-portlet__body">
                            <br>
                            <div class="row">
                                <label class="col-lg-3 col-form-label">Max purchase per user</label>
                                <div class="col-lg-3">
                                    <?= Html::textInput('CategoryPromotion[max_purchase_per_user]', '', ['class' => 'form-control']) ?>
                                </div>
                                <div class="col-lg-2" style="padding:12px">
                                    <?= Html::checkbox('CategoryPromotion[unlimited_purchase_per_user]', '') ?>
                                    Unlimited
                                </div>
                                <div class="col-lg-3">
                                    <button type="button" class="btn-save-promotion add-item-4 btn btn-primary"><i class="la la-save"></i></button>
                                    <button id="btn-remove-promotion" type="button" class="remove-item-4 btn btn-danger"><i class="fa fa-trash"></i></button>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <label class="col-lg-3 col-form-label">
                                    Total Promo Qty
                                    <i data-toggle="m-tooltip" class="m-form__heading-help-icon flaticon-info"
                                       title="1. Total Promo Qty >= Promo Qty for specific group(s) with Discount % declared.
                                       2. If specific group(s) with Discount % declared without Promo Qty,will share remaining Total Promo Qty,first come first serve.
                                       3. Customer Group without Promo Qty and Discount % set will not entitled to Promotion"></i>
                                </label>

                                <div class="col-lg-3">
                                    <?= Html::textInput('CategoryPromotion[total_promo_quantity]', '', ['maxlength'=>191,'class'=>"form-control"]) ?>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="m-section__content">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                <tr>
                                                    <th>Customer Group</th>
                                                    <th>Promo Qty</th>
                                                    <th>Discount %</th>
                                                    <th>OffGamers Point</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <?php foreach ($customer_group_list as $i => $v) { ?>
                                                    <tr>
                                                        <td><?= $v ?></td>
                                                        <td><?= Html::textInput("CategoryPromotionCustomer[$i][promo_quantity]", '',
                                                                ['id'=> "promo_quantity-" . $i ,'maxlength'=>191,'class'=>"form-control promotion-customer-quantities"]) ?></td>
                                                        <td><?= Html::textInput("CategoryPromotionCustomer[$i][discount_percentage]", '',
                                                                ['id'=> "discount_percentage-" . $i, 'maxlength'=>191,'class'=>"form-control promotion-customer-discounts"]) ?></td>
                                                        <td><?= Html::textInput("CategoryPromotionCustomer[$i][op_reward]", '',
                                                                ['id'=> "op_reward-" . $i, 'maxlength'=>191,'class'=>"form-control promotion-customer-points"]) ?></td>
                                                    </tr>
                                                <?php } ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="promotion_activity"></div>
            </div>
        </div>

        <div class="promotion-discount-rule m-portlet m-portlet--mobile">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Promotion Activity
                        </h3>
                    </div>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="m-section__content">
                    <div class="table-responsive">
                        <table id="promotion-table" class="table table-bordered">
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    <?php } ?>
</div>

<?php
$script = <<< JS
populateDiscountRuleDetail = () => {
    var discount_value = $("#select_cdrules_id").val();
    if (discount_value) {
        $("#cdrules_id").val(discount_value);
        $.get('get-discount-rule-detail', { rules_id: discount_value }, function (data) {
            $("#tbl-discount-rules > tbody").empty();
            $.each(data, function(index, value) {
                $("#tbl-discount-rules > tbody").append('<tr>' 
                  + '<td>' + value.customers_groups_name + '</td>'
                  + '<td>' + value.cdgr_discount + '</td>'
                  + '<td>' + value.cdgr_wor + '</td>'
                  + '</tr>'  
                );
            });
            $("#d-discount-rules").removeClass("hide");
        }).fail(function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: xhr.responseText
            })
        });
    }
};

updateDiscountRulesTable = (discountRules) => {
    let allCurrencies = $.extend({}, currencies.available, currencies.assigned);
    let assignedIndex = $.map(currencies.assigned, (value, index) => {
        return index;
    });
    let options = new Array();
    $.each(allCurrencies, function(index, value) {
        let selected = '';
        if ($.inArray(index, assignedIndex) >= 0) {
            selected = 'selected';
        }
        options.push('<option value="' + index + '" ' + selected + '>'+ value + '</option>');
    });
}

$(document).on('click', '#add-discount', function(event) {
    populateDiscountRuleDetail();
});

$(document).on('click', '#delete-discount', function(event) {
    $("#cdrules_id").val('');
    $("#select_cdrules_id").val('');
    $('#select_cdrules_id').selectpicker('refresh');
    $("#d-discount-rules").removeClass("hide").addClass("hide");
});

$(function() {
    populateDiscountRuleDetail();
});

$(document).on('click', '.btn-save-promotion', function(event) {
    var startDate = $("input[name='CategoryPromotion[start_date]']").val();
    var endDate = $("input[name='CategoryPromotion[end_date]']").val();
    
    if (!startDate) {
        return Swal.fire('', 'Start Date cannot be Blank', 'error');
    }
    
    if (!endDate) {
        return Swal.fire('', 'End Date cannot be Blank', 'error');
    }
    
    mApp.blockPage();
    
    var request = {}
    $("input[name^='CategoryPromotion']").map(function() {
        request[$(this).attr("name")] = $(this).val();
    });
    request["CategoryPromotion[unlimited_purchase_per_user]"] = 
        $("input[name='CategoryPromotion[unlimited_purchase_per_user]']").is(":checked") ? 1 : 0;
    
    if (request["CategoryPromotion[category_promotion_id]"]) {
        savePromotion(request, false);
    } else {
        savePromotion(request, true);
    }
});

function savePromotion(values, isCreate) {
    $.ajax({
        method: "POST",
        url: '/category/save-promotion',
        data: values,
        dataType: 'json'
    }).done(function (result) {
        if (result.status == true) {
            var data = result.data;
            if (isCreate) {
                appendIntoPromotionTable(data.category_promotion_id, data.status, data.start_date, data.end_date);
                clearPromotionForm();
            } else {
                $("#td-promotion-date-" + data.category_promotion_id).html(data.start_date + " - " + data.end_date);
                $("#td-status-" + data.category_promotion_id).html(data.status);
            }
            $("input[name='CategoryPromotion[category_promotion_id]']").val(data.category_promotion_id);
            
            Swal.fire('', 'Success!', 'success');
        } else {
            var swalErrorMessage = '';
            var errorList = result.errors;
            $.each(errorList, function(index, obj) {
                swalErrorMessage = obj[0] + "<br>";
            });
            Swal.fire(
                '',
                swalErrorMessage,
                'warning',
            )
        }
        
        mApp.unblockPage();
    });
}

function viewPromotion(id) {
    mApp.blockPage();
    
    $.ajax({
        method: "get",
        url: '/category/get-promotion',
        data: {
            category_promotion_id: id
        },
        dataType: 'json'
    }).done(function (data) {
        $("input[name='CategoryPromotion[category_promotion_id]']").val(data.category_promotion_id);
        $("input[name='CategoryPromotion[start_date]']").val(data.start_date);
        $("input[name='CategoryPromotion[end_date]']").val(data.end_date);
        $("input[name='CategoryPromotion[max_purchase_per_user]']").val(data.max_purchase_per_user);
        $("input[name='CategoryPromotion[total_promo_quantity]']").val(data.total_promo_quantity);
        if (data.unlimited_purchase_per_user === "1") {
            $("input[name='CategoryPromotion[unlimited_purchase_per_user]']").prop('checked', true);
        } else {
            $("input[name='CategoryPromotion[unlimited_purchase_per_user]']").prop('checked', false);
        }
        settingMaxPurchasePerUser();
        
        const customerGroup = data.category_promotion_customer_group;
        $.each(customerGroup, function(key, value){
            $("input[name='CategoryPromotionCustomer[" + value.customers_group_id + "][promo_quantity]']").val(value.promo_quantity);
            $("input[name='CategoryPromotionCustomer[" + value.customers_group_id + "][discount_percentage]']").val(value.discount_percentage);
            $("input[name='CategoryPromotionCustomer[" + value.customers_group_id + "][op_reward]']").val(value.op_reward);
        });
        
        mApp.unblockPage();
    });
}

function settingMaxPurchasePerUser() {
    const checked = $("input[name='CategoryPromotion[unlimited_purchase_per_user]']").is(":checked");
    const inputMaxPurchasePerUser = $("input[name='CategoryPromotion[max_purchase_per_user]']");
    if (checked) {
        inputMaxPurchasePerUser.val("");
        inputMaxPurchasePerUser.attr("disabled", true);
    } else {
        inputMaxPurchasePerUser.attr("disabled", false);
    }
}

$(document).on("click", "input[name='CategoryPromotion[unlimited_purchase_per_user]']", function(event) {
    settingMaxPurchasePerUser();
});

function clearPromotionForm() {
    $("input[name^='CategoryPromotion']").val("");
    $("input[name='CategoryPromotion[unlimited_purchase_per_user]']").val("");
    $("input[name='CategoryPromotion[max_purchase_per_user]']").attr("disabled", false);
    $("input[name='CategoryPromotion[category_id]']").val($("input[name='CategoryForm[category_id]']").val());
}

$(document).on("click", "#add-promotion", function(event) {
    clearPromotionForm();
});

function deletePromotion(id) {
    Swal.fire({
        title: 'Delete this item?',
        showCloseButton: false,
        showCancelButton: true,
        showConfirmButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        focusConfirm: false
    }).then((result) => {
        if (result.value) {
            mApp.blockPage();
            
            $.ajax({
                method: "get",
                url: '/category/delete-promotion',
                data: {
                    category_promotion_id: id
                },
                dataType: 'json'
            }).done(function (data) {
                $("#td-promotion-date-" + id).parent().remove();
                clearPromotionForm();
                mApp.unblockPage();
                
                Swal.fire('', 'Success!', 'success');
            });
        }
    });
}

$(document).on("click", "#btn-remove-promotion", function(event) {
    const promotionID = $("input[name='CategoryPromotion[category_promotion_id]']").val();
    if (promotionID) {
        deletePromotion(promotionID);
    }
});

function retrievePromotionList() {
    const categoryID = $("input[name='CategoryForm[category_id]']").val().trim();
    
    if (categoryID) {
        $.ajax({
            method: "GET",
            url: '/category/get-promotion-list',
            data: { category_id: categoryID },
            dataType: 'json'
        }).done(function (data) {
            $("#promotion-table > tbody").empty();
            $.each(data, function(key, value) {
                appendIntoPromotionTable(value.category_promotion_id, value.status, value.start_date, value.end_date);
            });
        });
    }
}

function appendIntoPromotionTable(categoryPromotionId, status, startDate, endDate) {
    $("#promotion-table > tbody").append("<tr><td id='td-promotion-date-" + categoryPromotionId + "'>" +
        startDate + " - " + endDate + "</td><td id='td-status-" + categoryPromotionId + "'>" + status + "</td><td>" + 
        "<a href='javascript:viewPromotion(" + categoryPromotionId + ")'><i class='fa fa-pen'></i></a>&nbsp;" +
        "<a href='javascript:deletePromotion(" + categoryPromotionId + ")'><i class='fa fa-trash'></i></a></td></tr>");
}

$(function() {
    retrievePromotionList();
});

JS;
$this->registerJs($script, $this::POS_END);