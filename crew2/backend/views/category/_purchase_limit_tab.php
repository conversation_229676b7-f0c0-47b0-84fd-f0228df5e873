<?php

use common\models\CustomersGroups;
use common\models\CategoryGroupPurchaseLimit;

$customer_group_list = CustomersGroups::getGroupList() ?? [];
?>
<div class="tab-pane" role="tabpanel" id="tab5">
    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        Threshold
                    </h3>
                </div>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="row">
                <div class="col-lg-12">
                    <div class="m-section__content">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>Customer Group</th>
                                    <th>Amount (USD)</th>
                                    <th>X Minute</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($customer_group_list as $i => $v) { ?>
                                    <?php
                                    $group_purchase_limit_model = new CategoryGroupPurchaseLimit();
                                    ?>
                                    <tr>
                                        <td><?= $v ?></td>
                                        <td><?= $form->field($group_purchase_limit_model, "[$i]amount")->textInput(['maxlength' => true,
                                                'class' => 'form-control purchase_limit_amount',
                                                'value' => $model->category_group_purchase_limit[$i]['amount'] ?? ''])->label(false) ?></td>
                                        <td>
                                            <?= $form->field($group_purchase_limit_model, "[$i]x_minute")->textInput(['maxlength' => true,
                                                'class' => 'form-control purchase_limit_minute',
                                                'value' => $model->category_group_purchase_limit[$i]['x_minute'] ?? ''])->label(false) ?>
                                            <?= $form->field($group_purchase_limit_model, "[$i]customers_group_id")->hiddenInput(['value' => $i, 'class' => ''])->label(false) ?>
                                        </td>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
