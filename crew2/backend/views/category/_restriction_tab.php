<?php

use yii\helpers\Html;
use common\models\Countries;
use common\models\CustomersGroups;

$countries_list = Countries::countriesCodeName() ?? [];
$customer_group_list = CustomersGroups::getGroupList() ?? [];
?>

<div class="tab-pane" role="tabpanel" id="tab6">
    <div class="form-group m-form__group row" style="display:none">
        <label class="col-lg-2">
            Customer Group
        </label>
        <div class="col-lg-10">

            <?= $form->field($model, 'category_customer_group_restriction')->dropDownList($customer_group_list, [
                    'name' => 'category_customer_group_restriction[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available',
//                    'value' => $model->category_customer_group_restriction ?? []
                ])->label(false)
            ?>
        </div>
    </div>

    <div class="m-separator m-separator--dashed m-separator--m" style="display:none"></div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2">
            Soft Block
            <i data-toggle="m-tooltip" data-width="auto" class="m-form__heading-help-icon flaticon-info" title="eCommerce > Regional Setting > Location restriction"></i>
        </label>
        <div class="col-lg-10">
            <?= $form->field($model, 'category_soft_block')->dropDownList($countries_list, [
                    'name' => 'category_soft_block[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available',
//                    'value' => $model->category_soft_block ?? []
                ])->label(false)
            ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2">
            Hard Block
            <i data-toggle="m-tooltip" data-width="auto" class="m-form__heading-help-icon flaticon-info" title="Customer's IP restriction"></i>
        </label>
        <div class="col-lg-10">
            <?= $form->field($model, 'category_hard_block')->dropDownList($countries_list, [
                    'name' => 'category_hard_block[]',
                    'class' => 'form-control list m-datatable__pager-size form-multiple-list',
                    'multiple size' => '12',
                    'data-target' => 'available',
//                    'value' => $model->category_hard_block ?? []
                ])->label(false)
            ?>
        </div>
    </div>
</div>
