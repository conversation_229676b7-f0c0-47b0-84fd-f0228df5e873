$(document).ready(function(){
    // $(".form-multiple-list").bootstrapDualListbox({
    //     moveOnSelect: false,
    //     selectedListLabel: "Restricted",
    //     nonSelectedListLabel: "Allow"
    // });
    $("select[name='category_customer_group_restriction[]']").bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: "Restricted",
        nonSelectedListLabel: "Allow"
    });
    $("select[name='category_soft_block[]']").bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: "Restricted",
        nonSelectedListLabel: "Allow"
    });
    $("select[name='category_hard_block[]']").bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: "Restricted",
        nonSelectedListLabel: "Allow"
    });
    $("select[name='category_payment_restriction[]']").bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: "Restricted",
        nonSelectedListLabel: "Allow"
    });
});