<?php

use common\models\CategoryMetadata;

$language = Yii::$app->enum->getLanguage('listData');
?>
<div class="tab-pane" role="tabpanel" id="tab3">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">SEO URL<span style="color: red;">*</span></label>
        <div class="col-lg-10">
            <?= $form->field($model, 'seo_url')->textInput(['maxlength' => true, 'id' => 'seo_url', 'readonly' => !$can_update_category])->label(false) ?>
            <input type='hidden' id='hidden-seo-url-error' value="" />
        </div>
    </div>

    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__head">
            <div class="m-portlet__head-tools">
                <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                    <?php foreach ($language as $i => $v) { ?>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link <?= ($i === 1 ? 'active' : '') ?>" data-toggle="tab" href="#s_tabs_<?= $i ?>" role="tab">
                                <?= $v ?>
                            </a>
                        </li>
                    <?php } ?>
                </ul>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="tab-content">
                <?php foreach ($language as $i => $v) { ?>
                    <?php
                    $category_metadata_model = new CategoryMetadata();
                    $category_metadata_model->language_id = $i;
                    ?>
                    <div class="tab-pane <?= ($i === 1 ? 'show active' : '') ?>" id="s_tabs_<?= $i ?>" role="tabpanel">
                        <div class = "form-group m-form__group row">
                            <label class = "col-lg-2 col-form-label">Meta Title</label>
                            <div class = "col-lg-10">
                                <?= $form->field($category_metadata_model, "[$i]title")->textInput(['maxlength' => true,
                                    'value' => $model['category_metadata'][$i]['title'] ?? '', 'readonly' => !$can_update_seo])->label(false) ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Meta Keyword</label>
                            <div class="col-lg-10">
                                <?= $form->field($category_metadata_model, "[$i]keyword")->textarea(['rows' => 6,
                                    'value' => $model['category_metadata'][$i]['keyword'] ?? '', 'readonly' => !$can_update_seo])->label(false) ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Meta Description</label>
                            <div class="col-lg-10">
                                <?= $form->field($category_metadata_model, "[$i]description")->textarea(['rows' => 6,
                                    'value' => $model['category_metadata'][$i]['description'] ?? '', 'readonly' => !$can_update_seo])->label(false) ?>
                                <?= $form->field($category_metadata_model, "[$i]language_id")->hiddenInput(['value' => $i])->label(false) ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>
