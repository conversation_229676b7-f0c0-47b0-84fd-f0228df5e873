<?php

use yii\helpers\Url;
use yii\widgets\ActiveForm;
use backend\assets\DualListBoxAsset;

$this->title = 'Category';
$this->params['breadcrumbs'][] = 'Product & Category';
$this->params['breadcrumbs'][] = ['label' => 'Category', 'url' => ['index']];
$this->params['layoutContent'] = 'block';

$can_update_seo = Yii::$app->user->can('[SEO] Category');
$can_update_category = Yii::$app->user->can('[Product] Edit Category');
?>

<?php
DualListBoxAsset::register($this);

$max_size = (int) (str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024);

$this->registerJs($this->render('_script.js'), $this::POS_END);
?>

<?php
$form = ActiveForm::begin([
    'id' => 'category-form',
    'options' => [
        'enctype' => 'multipart/form-data',
        'class' => 'm-form m-form--fit m-form--label-align-right',
    ],
]);
?>

<div id="validation-error-message-container" class="alert alert-danger <?= empty($model->getErrors()) ? 'hide' : ''  ?>" role="alert">
    <strong>Heads up!</strong><br>
    Change a few things up and try submitting again.
    <div id="error-container">
        <u id="error-list">
            <?php
                if (!empty($model->getErrors())) {
                    foreach($model->getErrors() as $error) {
                        echo "<li>$error[0]</li>";
                    }
                }
            ?>
        </u>
    </div>
</div>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head btn-accent" style="background: #00c5dc">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text" style="color:white">
                    <?= isset($model->name) && !empty($model->name) ? $model->name : "New" ?>
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <button id="brand-form-submit-btn" type="submit"
                            class="m-portlet__nav-link btn btn-secondary m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill">
                        <i class="la la-save"></i></button>
                </li>
            </ul>
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a id="brand-form-cancel-link" href="<?= Url::toRoute('/category'); ?>"
                       class="m-portlet__nav-link m-portlet__nav-link--icon">
                        <i class="la la-close" style="color: white;"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="m-portlet__body">
        <ul class="nav nav-pills nav-pills--accent m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm" role="tablist">
            <li class="nav-item m-tabs__item">
                <a href="#tab1" class="nav-link m-tabs__link active" data-toggle="tab" aria-controls="tab1" role="tab">
                    <i class="la la-cog"></i>
                    General
                </a>
            </li>
            <li class="nav-item m-tabs__item">
                <a href="#tab2" class="nav-link m-tabs__link" data-toggle="tab" aria-controls="tab2" role="tab">
                    <i class="la la-comment"></i>
                    Description
                </a>
            </li>
            <li class="nav-item m-tabs__item">
                <a href="#tab3" class="nav-link m-tabs__link " data-toggle="tab" aria-controls="tab3" role="tab">
                    <i class="la la-newspaper-o"></i>
                    SEO
                </a>
            </li>
            <li class="nav-item m-tabs__item" style="display:none">
                <a href="#tab4" class="nav-link m-tabs__link " data-toggle="tab" aria-controls="tab4" role="tab">
                    <i class="la la-tag"></i>
                    Promotion
                </a>
            </li>
            <li class="nav-item m-tabs__item" style="display:none">
                <a href="#tab5" class="nav-link m-tabs__link " data-toggle="tab" aria-controls="tab5" role="tab">
                    <i class="la la-cart-plus"></i>
                    Purchase Limit
                </a>
            </li>
            <li class="nav-item m-tabs__item">
                <a href="#tab6" class="nav-link m-tabs__link" data-toggle="tab" aria-controls="tab6" role="tab">
                    <i class="la la-map-marker"></i>
                    Restriction
                </a>
            </li>
            <li class="nav-item m-tabs__item" style="display:none">
                <a href="#tab7" class="nav-link m-tabs__link " data-toggle="tab" aria-controls="tab7" role="tab">
                    <i class="la la-cc-visa"></i>
                    Payment Method
                </a>
            </li>
        </ul>

        <div class="tab-content">
            <?= $this->render('_general_tab', ['form' => $form, 'model' => $model, 'can_update_category' => $can_update_category, 'can_update_seo' => $can_update_seo])?>

            <?= $this->render('_description_tab', ['form' => $form, 'model' => $model, 'can_update_category' => $can_update_category, 'can_update_seo' => $can_update_seo])?>

            <?= $this->render('_seo_tab', ['form' => $form, 'model' => $model, 'can_update_category' => $can_update_category, 'can_update_seo' => $can_update_seo])?>

            <?= $this->render('_promotion_tab', ['form' => $form, 'model' => $model ])?>

            <?= $this->render('_purchase_limit_tab', ['form' => $form, 'model' => $model ])?>

            <?= $this->render('_restriction_tab', ['form' => $form, 'model' => $model, 'can_update_category' => $can_update_category, 'can_update_seo' => $can_update_seo])?>

            <?= $this->render('_payment_method_tab', ['form' => $form, 'model' => $model])?>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<?php

$script = <<<JS
function validateSeoUrl() 
{
    const categoryID = $("input[name='CategoryForm[category_id]']").val().trim();
    const seoUrl = $("input[name='CategoryForm[seo_url]']").val().trim();
    const seoURLHelpBlock = $("#seo_url").next(".help-block");
    const seoURLHiddenError = $("#hidden-seo-url-error");

    seoURLHelpBlock.empty();
    $.ajax({
        method: "GET",
        url: '/category/check-seo-url-exists?seo-url=' + seoUrl + '&category-id=' + categoryID,
    }).done(function (data) {
        if (data) {
            seoURLHelpBlock.append("SEO Url already taken by other.");
            seoURLHiddenError.val("SEO Url already taken by other.");
        } else {
            seoURLHiddenError.val("");
        }
    });
}

$(function () 
{
    const categoryID = $("input[name='CategoryForm[category_id]']").val();
    if (categoryID.length === 0) {
        $("#category-name-1").on('keyup keydown keypress onpaste', function() {
            const name = $(this).val().trim();
            if (Boolean(name)) {
                const urlSlug = name.toLowerCase().replace(/ /g, '-').replace(/[^\w-]+/g, '');
                $("#seo_url").val(urlSlug);
            }
        }).on('focusout', function() {
            validateSeoUrl();
        });
    }
    
    $("#seo_url").on('change onpaste', function() {
        validateSeoUrl();
    });
    
    $('#category-form').on('beforeValidate', function (event) {
        mApp.blockPage();

        const validationErrorMessageContainer = $("#validation-error-message-container");
        var errorCount = 0;
        var seoUrlError = $("#hidden-seo-url-error").val();
        const errorList = $("#error-list");

        validationErrorMessageContainer.removeClass("hide").addClass("hide");
        errorList.empty();

        if (seoUrlError != null && seoUrlError.length > 0) {
            displayErrorMessageContainer("<li>" + seoUrlError + "</li>");
            return false;
        }
        
        if (!verifyPurchaseLimit()) {
            return false;
        }
        
        setTimeout(function() {
            var errorMessage = "";
            $('.help-block').each(function(index, obj) {
                if ($(this).text().length > 0) {
                    errorMessage += "<li>" + $(this).text() + "</li>";
                    errorCount++;
                }
            });
            
            if (errorCount > 0) {
                displayErrorMessageContainer(errorMessage);
                return false;
            }
            return true;
        }, 800);
    });
});

function displayErrorMessageContainer(errorMessage) 
{
    $("#error-list").empty().append(errorMessage);
    $("#validation-error-message-container").removeClass("hide");
    mApp.unblockPage();
}

function verifyPurchaseLimit() 
{
    const amountList = $(".purchase_limit_amount");
    for(var i=0; i < amountList.length; i++) {
        if ($(amountList[i]).val() && !validateNumeric($(amountList[i]).val())) {
            displayErrorMessageContainer("<li>Please insert numbers for Purchase Limit [Amount]</li>");
            return false;
        }
    }
    
    const minuteList = $(".purchase_limit_minute");
    for(var j=0; j < minuteList.length; j++) {
        if ($(minuteList[i]).val() && !validateInteger($(minuteList[i]).val())) {
            displayErrorMessageContainer("<li>Please insert integer for Purchase Limit [X-Minute]</li>");
            return false;
        }
    }
    return true;
}

function validateNumeric(value) 
{
    return !isNaN(value) && (value.match(/^-?\d+$/) || value.match(/^\d+\.\d+$/));
}

function validateInteger(value) 
{
    return value.match(/^-?\d+$/);
}

JS;
$this->registerJs($script, $this::POS_END);


if (!$can_update_category) {
    $this->registerJs(
        <<<SCRIPT
        setTimeout(function(){
        $(".bootstrap-duallistbox-container").find("*").prop("disabled",true);
        },500);
SCRIPT,
        \yii\web\View::POS_READY
    );
}
?>


