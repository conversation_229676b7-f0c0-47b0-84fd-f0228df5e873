<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;

$this->title = 'Category';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Category';
$this->params['layoutContent'] = 'block';
?>

<div class="game-publisher-index m-portlet m-portlet--mobile">

    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Category
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?php
        $statusTypes = ['1' => 'Active', '0' => 'Inactive']; ?>
        <?=
        \yii\grid\GridView::widget([
            'dataProvider' => $provider,
            'filterModel' => $filter,
            'filterUrl' => $filter->filterUrl,
            'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
            'columns' => [
                [
                    'header' => '#',
                    'content' => function ($model, $key, $index) use ($filter) {
                        return $index + $filter->startAt;
                    },
                ],
                [
                    'attribute' => 'name',
                    'filter' => Html::activeInput('text', $filter, 'name', ['class' => 'form-control']),
                ],
                [
                    'attribute' => 'status',
                    'content' => function ($model) {
                        if ($model['status'] == 1) {
                            return '<span class="m-badge m-badge--info m-badge--wide m-badge--rounded">Active</span>';
                        } else {
                            return '<span class="m-badge m-badge--metal m-badge--wide m-badge--rounded">Inactive</span>';
                        }
                    },
                    'filter' => Html::activeDropDownList($filter, 'status', $statusTypes, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']),
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{update} {delete}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                ],
            ],
        ]);

        echo $filter->renderPageSizer();

        ?>
    </div>
</div>