<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use yii\widgets\LinkPager;
?>

<div class="customer-list">
    <div class="row">
        <div class="col-xl-12">
            <div class="m-portlet m-portlet--mobile">
                <div class="m-portlet__body">
                    <?php
                    echo GridView::widget([
                        'dataProvider' => $provider,
                        'columns' => [
                            [
                                'header' => 'Order ID',
                                'attribute' => 'order_id',
                            ],
                            [
                                'header' => 'Customer ID',
                                'attribute' => 'customer_id',
                            ],
                            [
                                'header' => 'Coupon Code',
                                'attribute' => 'coupon_code',
                            ],
                            [
                                'header' => 'Redeem Date',
                                'attribute' => 'redeem_date',
                            ],
                            [
                                'header' => 'Action',
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{delete}'),
                                'contentOptions' => ['style' => 'white-space: nowrap; width: 60px'],
                                'buttons' => [
                                    'delete' => function ($url, $model)
                                    {
                                        if ($model['coupon_id']) {
                                            return Html::a('<span><i class="la la-trash"></i></span>', Url::to(['coupon-redeem-track/delete', 'id' => $model['unique_id']]), ['title' => 'Delete', 'data-toggle' => 'm-tooltip', 'data-placement' => 'bottom', 'data-confirm' => 'Are you sure you want to delete this item?']);
                                        }
                                    }
                                ]
                            ],
                        ],
                        'layout' => "{summary}\n{items}",
                    ]);


                    $form = ActiveForm::begin([
                                'id' => 'coupon-redeem-track-pagesize-form',
                                'method' => 'get',
                                'action' => ['/coupon-redeem-track/index']
                    ]);

                    echo Html::hiddenInput('page', $model->page);
                    echo Html::hiddenInput('order_id', $model->order_id);


                    echo \backend\widgets\MetronicPager::renderPager($provider);

                    ActiveForm::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
