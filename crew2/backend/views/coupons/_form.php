<?php

use common\models\CustomersGroups;
use common\models\PipwavePaymentMapper;
use common\models\Currencies;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DateTimePicker;
use kartik\widgets\SwitchInput;
?>

<div class="coupons-generation-form">
    <?php
    $form = ActiveForm::begin([
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ],
    ]);
    ?>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Title</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'title')->textInput(['maxlength' => true])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Code Type</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'code_type')->dropDownList(
                        [0 => 'Fixed Code', 1 => 'Dynamic Code'], [
                    'prompt' => 'Select',
                    'onchange' => 'changeCouponType();',
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>
        </div>

        <!--fixed code-->
        <?= $form->field($model, 'fixed_code', ['template' => '
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">{label}</label>
                <div class="col-lg-4">
                    {input}
                </div>
                {error}{hint}
            </div>'])->textInput(['maxlength' => true]); ?>

        <!--dynamic code-->
        <?= $form->field($model, 'coupon_code_prefix', ['template' => '
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">{label}</label>
                <div class="col-lg-4">
                    {input}
                </div>
                {error}{hint}
            </div>'])->textInput(['maxlength' => true]); ?>

        <?= $form->field($model, 'coupon_code_suffix', ['template' => '
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">{label}</label>
                <div class="col-lg-4">
                    {input}
                </div>
                {error}{hint}
            </div>'])->textInput(['maxlength' => true]); ?>

        <?= $form->field($model, 'coupon_number', ['template' => '
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">{label}</label>
                <div class="col-lg-4">
                    {input}
                </div>
                {error}{hint}
            </div>'])->textInput(['maxlength' => true]); ?>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Coupon Value</label>
            <div class="col-lg-2">
                <?=
                $form->field($model, 'coupon_amount', ['template' => '
                    <div class="input-group m-input-group">
                        {input}
                    </div>
                    {error}{hint}'])->textInput(['maxlength' => true])->label(false);
                ?>
            </div>
            <div class="col-lg-2">
                <?=
                $form->field($model, 'coupon_type')->dropDownList(
                        ['F' => 'USD', 'P' => '%'], [
                    'selected' => 'F', 'class' => 'selectpicker m-datatable__pager-size', 'data-width' => '100px']
                )->label(false);
                ?>
            </div>

            <label class="col-lg-2 col-form-label">Coupon Min Order Amount</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'coupon_minimum_order', ['template' => '
                    <div class="input-group m-input-group">
                        <div class="input-group-prepend"><span class="input-group-text">USD</span></div>
                        {input}
                    </div>
                    {error}{hint}'])->textInput(['maxlength' => true])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Max Cap</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'max_cap', ['template' => '
                    <div class="input-group m-input-group">
                        <div class="input-group-prepend"><span class="input-group-text">USD</span></div>
                        {input}
                    </div>
                    {error}{hint}'])->textInput(['maxlength' => true])->label(false); ?>
            </div>
            <div class="col-lg-6">&nbsp;</div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date</label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'coupon_start_date')->widget(DateTimePicker::class, [
                    'pluginOptions' => [
                        'autoclose' => true,
                        'format' => 'yyyy-mm-dd hh:ii',
                    ],
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Expire Date</label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'coupon_expire_date')->widget(DateTimePicker::class, [
                    'pluginOptions' => [
                        'autoclose' => true,
                        'format' => 'yyyy-mm-dd hh:ii',
                    ],
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-3 col-form-label">Unlimited Uses Per Coupon</label>
            <div class="col-lg-3">
                <?php
                echo $form
                        ->field($model, 'uses_per_coupon_unlimited')
                        ->widget(SwitchInput::class, [
                            'options' => ['onchange' => 'changeCouponUsesUnlimited();'],
                            'pluginOptions' => [
                                'onText' => '<i class="fa fa-check"></i>',
                                'offText' => '<i class="fa fa-times"></i>',
                            ]
                        ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Uses Per Coupon</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'uses_per_coupon')->textInput()->label(false) ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-3 col-form-label">Unlimited Uses Per User</label>
            <div class="col-lg-3">
                <?php
                echo $form
                        ->field($model, 'uses_per_user_unlimited')
                        ->widget(SwitchInput::class, [
                            'options' => ['onchange' => 'changeUserCouponUsesUnlimited();'],
                            'pluginOptions' => [
                                'onText' => '<i class="fa fa-check"></i>',
                                'offText' => '<i class="fa fa-times"></i>',
                            ]
                        ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Uses Per User</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'uses_per_user')->textInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-3 col-form-label">Mobile Only</label>
            <div class="col-lg-3">
                <?php
                echo $form
                    ->field($model, 'mobile_only')
                    ->widget(SwitchInput::class, [
                        'pluginOptions' => [
                            'onText' => '<i class="fa fa-check"></i>',
                            'offText' => '<i class="fa fa-times"></i>',
                        ]
                    ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Restrict To Categories</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'restrict_to_categories')->textarea(['rows' => 5])->label(false); ?>
                <span class="m-form__help">Separate Categories ID with comma</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Restrict To Product</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'restrict_to_products')->textarea(['rows' => 5])->label(false); ?>
                <span class="m-form__help">Separate Product ID with comma</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Restrict To Customer</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'restrict_to_customers')->textarea(['rows' => 5])->label(false); ?>
                <span class="m-form__help">Separate Customer ID with comma</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Restrict To Customer Group</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'restrict_to_customers_groups')->dropDownList(
                        CustomersGroups::getGroupList(), ['multiple' => 'multiple', 'size' => 5])->label(false)
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Restrict To Currency</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'restrict_to_currency_id')->dropDownList(
                        Currencies::getCurrenciesList(), ['multiple' => 'multiple', 'size' => 5])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Restrict To Payment Method</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'restrict_to_payment_id')->dropDownList(
                        PipwavePaymentMapper::getOGPaymentList(), ['multiple' => 'multiple', 'size' => 5])->label(false);
                ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>

</div>

<?php
$this->registerJs(<<<SCRIPT
    $(document).ready(function(){
            $(".field-couponsgenerationform-coupon_number").hide();
            $(".field-couponsgenerationform-fixed_code").hide();
            $(".field-couponsgenerationform-coupon_code_prefix").hide();
            $(".field-couponsgenerationform-coupon_code_suffix").hide();
            changeCouponType();
            changeCouponUsesUnlimited();
            changeUserCouponUsesUnlimited();
    });
    
    function changeCouponType(){
        switch($("#couponsgenerationform-code_type").val()){
            case "0":
                $(".field-couponsgenerationform-coupon_number").hide();
                $(".field-couponsgenerationform-fixed_code").show();
                $(".field-couponsgenerationform-coupon_code_prefix").hide();
                $(".field-couponsgenerationform-coupon_code_suffix").hide();
                $("#couponsgenerationform-coupon_number").val(1);
            break;
            
            case "1":
                $(".field-couponsgenerationform-coupon_number").show();
                $(".field-couponsgenerationform-fixed_code").hide();
                $(".field-couponsgenerationform-coupon_code_prefix").show();
                $(".field-couponsgenerationform-coupon_code_suffix").show();
            break;
        }
    }
    
    function changeCouponUsesUnlimited(){
        switch($("#couponsgenerationform-uses_per_coupon_unlimited").prop('checked')){
            case false:
                $("#couponsgenerationform-uses_per_coupon").removeAttr('disabled');
            break;
            
            case true:
                $("#couponsgenerationform-uses_per_coupon").val(0).attr('disabled',true);
            break;
        }
    }
    
    function changeUserCouponUsesUnlimited(){
        switch($("#couponsgenerationform-uses_per_user_unlimited").prop('checked')){
            case false:
                $("#couponsgenerationform-uses_per_user").removeAttr('disabled');
            break;
            
            case true:
                $("#couponsgenerationform-uses_per_user").val(0).attr('disabled',true);
            break;
        }
    }
SCRIPT
        , \yii\web\View::POS_END
);


