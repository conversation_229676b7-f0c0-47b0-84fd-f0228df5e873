<?php

use common\models\CouponsGeneration;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;
use common\models\Coupons;

$this->title = 'Coupon';

$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = 'Coupon';
$this->params['layoutContent'] = 'block';
?>
<div class="coupons-generation-index m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <?= Html::a('<span><i class="fa fa-broom"></i><span> Clean Expired Coupon', ['clean-up-expired-coupon'], ['class' => 'btn btn-warning m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill']) ?>
                    <?= Html::a('<span><i class="la la-plus"></i><span> New</span></span>', ['create'], ['class' => 'btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill']) ?>
                </div>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'title',
                [
                    'label' => 'Code Type',
                    'content' => function ($model) {
                        return ($model['fixed_code'] == '' ? 'Dynamic Code' : 'Fixed Code');
                    },
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                ],
                [
                    'label' => 'Coupon Type',
                    'filter' => Html::activeDropDownList($searchModel, 'coupon_type', Coupons::getCouponTypeList(), ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) {
                        return Coupons::getCouponType($model['coupon_type']);
                    },
                ],
                [
                    'attribute' => 'coupon_amount',
                    'content' => function ($model) {
                        return ($model['coupon_type'] == 'P' ? $model['coupon_amount'] . '%' : 'USD$ ' . $model['coupon_amount']);
                    },
                    'contentOptions' => ['style' => 'width: 150px; text-align: right;'],
                ],
                [
                    'label' => 'Status',
                    'filter' => Html::activeDropDownList($searchModel, 'coupon_generation_status', CouponsGeneration::getCouponGenerationStatusList(), ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) {
                        return CouponsGeneration::getCouponGenerationStatus($model['coupon_generation_status']);
                    },
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{coupons/view} {coupons/approve} {coupons/cancel} {coupons/list-coupon}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'coupons/view' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-eye"></span>', Url::to([
                                                'coupons/view',
                                                'id' => $model['coupon_generation_id'],
                                            ]), ['title' => 'View']
                            );
                        },
                        'coupons/approve' => function ($url, $model, $key) {
                            if ($model['coupon_generation_status'] == 'P') {
                                return Html::a('<span class="fa fa-user-check"></span>', Url::to([
                                                    'coupons/approve',
                                                    'id' => $model['coupon_generation_id'],
                                                ]), ['title' => 'Approve']
                                );
                            }
                        },
                        'coupons/cancel' => function ($url, $model, $key) {
                            if ($model['coupon_generation_status'] == 'P') {
                                return Html::a('<span class="fa fa-ban"></span>', Url::to([
                                                    'coupons/cancel',
                                                    'id' => $model['coupon_generation_id'],
                                                ]), ['title' => 'Reject']
                                );
                            }
                        },
                        'coupons/list-coupon' => function ($url, $model, $key) {
                            if ($model['coupon_generation_status'] == 'Y') {
                                return Html::a('<span class="fa fa-list"></span>', Url::to([
                                                    'coupons/list-coupon',
                                                    'id' => $model['coupon_generation_id'],
                                                ]), ['title' => 'Coupon List']
                                );
                            }
                        },
                    ],
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>
