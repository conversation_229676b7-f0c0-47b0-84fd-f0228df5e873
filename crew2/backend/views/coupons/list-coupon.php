<?php

use common\models\CouponRedeemTrack;
use common\models\Coupons;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

$this->title = 'Coupon';

$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = ['label' => 'Coupon', 'url' => ['index']];
$this->params['breadcrumbs'][] = 'Coupon Code';
$this->params['layoutContent'] = 'block';
?>
<div class="coupons-generation-index m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $coupon; ?>
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'coupon_code',
                [
                    'label' => 'Coupon Type',
                    'filter' => Html::activeDropDownList($searchModel, 'coupon_type', Coupons::getCouponTypeList(), ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) {
                        return Coupons::getCouponType($model['coupon_type']);
                    },
                ],
                [
                    'attribute' => 'coupon_amount',
                    'content' => function ($model) {
                        return ($model['coupon_type'] == 'P' ? $model['coupon_amount'] . '%' : 'USD$ ' . $model['coupon_amount']);
                    },
                    'contentOptions' => ['style' => 'width: 150px; text-align: right;'],
                ],
                [
                    'attribute' => 'coupon_active',
                    'filter' => Html::activeDropDownList($searchModel, 'coupon_active', Coupons::getCouponStatusList(), ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) {
                        return Coupons::getCouponStatus($model['coupon_active']);
                    },
                ],
                [
                    'label' => 'Redeem Count',
                    'headerOptions' => ['style' => 'white-space: nowrap;'],
                    'content' => function ($model) {
                        return CouponRedeemTrack::getRedeemCount($model['coupon_id']) . '/' . ($model['uses_per_coupon'] > 0 ? $model['uses_per_coupon'] : 'Unlimited');
                    },
                    'contentOptions' => ['style' => 'white-space: nowrap; text-align: right;'],
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{coupons/view-code} {coupons/cancel-code}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'coupons/view-code' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-eye"></span>', Url::to([
                                                'coupons/view-code',
                                                'id' => $model['coupon_id'],
                                            ])
                            );
                        },
                        'coupons/cancel-code' => function ($url, $model, $key) {
                            if ($model['coupon_active'] !== 'D' && $model['coupon_active'] !== 'N') {
                                return Html::a('<span class="fa fa-ban"></span>', Url::to([
                                                    'coupons/cancel-code',
                                                    'id' => $model['coupon_id'],
                                                ])
                                );
                            }
                        },
                    ],
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>
