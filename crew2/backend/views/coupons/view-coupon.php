<?php

use common\models\Coupons;
use common\models\CouponsGeneration;
use yii\helpers\Html;
use yii\widgets\DetailView;
use yii\helpers\Url;

$this->title = 'Coupon';

$code = $model->coupon_code;
$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = ['label' => 'Coupon', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => 'Coupon Code', 'url' => ['list-coupon', 'id' => $model->coupon_generation_id]];
$this->params['layoutContent'] = 'block';

\yii\web\YiiAsset::register($this);
?>
<div class="coupons-view m-portlet m-portlet--mobile m-form">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $code ?> <small>Coupon Code</small>
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <?php
                    if ($model->coupon_active !== 'D' && $model->coupon_active !== 'N') {
                        if (mdm\admin\components\Helper::checkRoute('coupons/cancel-code')) {
                            echo Html::a('Disable Code', ['coupons/cancel-code', 'id' => $model['coupon_id']], ['class' => 'btn btn-warning m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill']);
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <?=
        DetailView::widget([
            'model' => $model,
            'attributes' => [
                'coupon_generation_id',
                'coupon_id',
                [
                    'attribute' => 'coupon_type',
                    'value' => function ($model) {
                        $m = CouponsGeneration::find()->where(['coupon_generation_id' => $model->coupon_generation_id])->one();
                        return ($m->fixed_code ? "Fixed Code" : "Dynamic Code");
                    },
                    'contentOptions' => ['style' => "width: 70%;"]
                ],
                [
                    'label' => 'Coupon Status',
                    'attribute' => 'coupon_active',
                    'value' => function ($model) {
                        return $model->getCouponStatus($model->coupon_active);
                    },
                ],
                'coupon_code',
                [
                    'label' => 'Coupon Value',
                    'attribute' => 'coupon_amount',
                    'value' => function ($model) {
                        return ($model['coupon_type'] == 'P' ? $model['coupon_amount'] . '%' : 'USD$ ' . $model['coupon_amount']);
                    },
                ],
                [
                    'label' => 'Coupon Min Order Amount',
                    'attribute' => 'coupon_minimum_order',
                    'value' => function ($model) {
                        return 'USD$ ' . $model['coupon_minimum_order'];
                    },
                ],
                [
                    'label' => 'Max Cap',
                    'attribute' => 'max_cap',
                    'value' => function ($model) {
                        return 'USD$ ' . $model['max_cap'];
                    },
                ],
                'coupon_start_date',
                'coupon_expire_date',
                [
                    'attribute' => 'uses_per_coupon',
                    'value' => function ($model) {
                        return ($model['uses_per_coupon_unlimited'] != 'Y' ? $model['uses_per_coupon'] : 'Unlimited');
                    },
                ],
                [
                    'attribute' => 'uses_per_user',
                    'value' => function ($model) {
                        return ($model['uses_per_user_unlimited'] != 'Y' ? $model['uses_per_user'] : 'Unlimited');
                    },
                ],
                [
                    'attribute' => 'mobile_only',
                    'value' => function ($model) {
                        return ($model['mobile_only'] == 1 ? 'YES' : 'NO');
                    },
                ],
                'restrict_to_products:ntext',
                'restrict_to_categories:ntext',
                'restrict_to_customers:ntext',
                [
                    'format' => 'html',
                    'attribute' => 'restrict_to_customers_groups',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getCustomersGroupRestrictionDescription($model['restrict_to_customers_groups']));
                    },
                ],
                [
                    'label' => 'Restrict To Currency',
                    'format' => 'html',
                    'attribute' => 'restrict_to_currency_id',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getCurrenciesRestrictionDescription($model['restrict_to_currency_id']));
                    },
                ],
                [
                    'label' => 'Restrict To Payment Method',
                    'format' => 'html',
                    'attribute' => 'restrict_to_payment_id',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getPaymentRestrictionDescription($model['restrict_to_payment_id']));
                    },
                ],
                'date_created',
                'date_modified',
            ],
        ])
        ?>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10 m--align-right">
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute(['list-coupon', 'id' => $model->coupon_generation_id]); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>
</div>
