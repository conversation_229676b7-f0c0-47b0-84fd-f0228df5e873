<?php

use common\models\Coupons;
use yii\helpers\Html;
use yii\widgets\DetailView;
use yii\helpers\Url;

$this->title = 'Coupon';

$title = $model->getTitle();
$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = ['label' => 'Coupon', 'url' => ['index']];
$this->params['layoutContent'] = 'block';

\yii\web\YiiAsset::register($this);
?>
<div class="coupons-generation-view m-portlet m-portlet--mobile m-form">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $title ?>
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <?php
                    if ($model->coupon_generation_status == 'P') {
                        if (mdm\admin\components\Helper::checkRoute('coupons/approve')) {
                            echo Html::a('Approve', ['coupons/approve', 'id' => $model['coupon_generation_id']], ['class' => 'btn btn-primary m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill']);
                        }

                        echo "\n";

                        if (mdm\admin\components\Helper::checkRoute('coupons/cancel')) {
                            echo Html::a('Cancel', ['coupons/cancel', 'id' => $model['coupon_generation_id']], ['class' => 'btn btn-outline-danger m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill']);
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <?=
        DetailView::widget([
            'model' => $model,
            'attributes' => [
                'coupon_generation_id',
                [
                    'label' => 'Status',
                    'attribute' => 'coupon_generation_status',
                    'value' => function ($model) {
                        return $model->getCouponGenerationStatus($model->coupon_generation_status);
                    },
                ],
                [
                    'label' => 'Title',
                    'value' => function ($model) {
                        return $model->getTitle();
                    },
                ],
                [
                    'label' => 'Code Type',
                    'value' => function ($model) {
                        return ($model->fixed_code ? "Fixed Code" : "Dynamic Code");
                    },
                ],
                'fixed_code',
                'coupon_code_prefix',
                'coupon_code_suffix',
                [
                    'label' => 'Number Of Coupon To Generate',
                    'attribute' => 'coupon_number',
                    'contentOptions' => ['style' => "width: 70%;"]
                ],
                [
                    'label' => 'Coupon Value',
                    'attribute' => 'coupon_amount',
                    'value' => function ($model) {
                        return ($model['coupon_type'] == 'P' ? $model['coupon_amount'] . '%' : 'USD$ ' . $model['coupon_amount']);
                    },
                ],
                [
                    'label' => 'Coupon Min Order Amount',
                    'attribute' => 'coupon_minimum_order',
                    'value' => function ($model) {
                        return 'USD$ ' . $model['coupon_minimum_order'];
                    },
                ],
                [
                    'label' => 'Max Cap',
                    'attribute' => 'max_cap',
                    'value' => function ($model) {
                        return 'USD$ ' . $model['max_cap'];
                    },
                ],
                [
                    'label' => 'Start Date',
                    'attribute' => 'coupon_start_date'
                ],
                [
                    'label' => 'Expire Date',
                    'attribute' => 'coupon_expire_date'
                ],
                [
                    'attribute' => 'uses_per_coupon',
                    'value' => function ($model) {
                        return ($model['uses_per_coupon_unlimited'] != 'Y' ? $model['uses_per_coupon'] : 'Unlimited');
                    },
                ],
                [
                    'attribute' => 'uses_per_user',
                    'value' => function ($model) {
                        return ($model['uses_per_user_unlimited'] != 'Y' ? $model['uses_per_user'] : 'Unlimited');
                    },
                ],
                [
                    'attribute' => 'mobile_only',
                    'value' => function ($model) {
                        return ($model['mobile_only'] == 1 ? 'YES' : 'NO');
                    },
                ],
                'restrict_to_categories:ntext',
                'restrict_to_products:ntext',
                'restrict_to_customers:ntext',
                [
                    'format' => 'html',
                    'attribute' => 'restrict_to_customers_groups',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getCustomersGroupRestrictionDescription($model['restrict_to_customers_groups']));
                    },
                ],
                [
                    'label' => 'Restrict To Currency',
                    'format' => 'html',
                    'attribute' => 'restrict_to_currency_id',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getCurrenciesRestrictionDescription($model['restrict_to_currency_id']));
                    },
                ],
                [
                    'label' => 'Restrict To Payment Method',
                    'format' => 'html',
                    'attribute' => 'restrict_to_payment_id',
                    'value' => function ($model) {
                        return implode('<br>', Coupons::getPaymentRestrictionDescription($model['restrict_to_payment_id']));
                    },
                ],
                'date_created',
                'date_modified',
            ],
        ])
        ?>

    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10 m--align-right">
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>
</div>