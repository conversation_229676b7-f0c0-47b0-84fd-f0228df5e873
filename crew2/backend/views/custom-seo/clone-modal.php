<?php

use yii\bootstrap4\Modal;
use yii\helpers\HTML;

/* @var $source_id integer */

Modal::begin([
    'id' => 'clone-custom-seo-modal',
    'title' => 'Duplicate Template',
    'toggleButton' => false
]);


$type_list = array('Category', 'Game Blog', 'Products');

?>
<form id="custom-seo-clone" method="get" action="/custom-seo/clone">
    <?= HTML::hiddenInput('source_id', $source_id) ?>
    <div class="form-group m-form__group row">
        <label class="col-4 col-form-label">Type</label>
        <div class="col-8">
            <?= HTML::dropDownList('template_type', '', array_combine($type_list, $type_list), array('class' => 'form-control m-input', 'prompt' => '')) ?>
        </div>
    </div>

    <div id="first_selection" class="form-group m-form__group row" style="display:none">
        <label class="col-12 col-form-label"></label>
        <div class="col-12">
            <?= HTML::dropDownList('first_selection', '', [], array('id' => 'first_selection_select', 'class' => 'form-control m-input', 'prompt' => '')) ?>
        </div>
    </div>

    <div id="second_selection" class="form-group m-form__group row m-hidden" style="display:none">
        <label class="col-4 col-form-label"></label>
        <div class="col-8">
            <?= HTML::dropDownList('second_selection', '', [], array('id' => 'second_selection_select', 'class' => 'form-control m-input', 'prompt' => '', 'multiple' => true)) ?>
        </div>
    </div>

    <div class="m-form__actions">
        <button type="submit" class="btn btn-primary">Submit</button>
    </div>
</form>
<?php
Modal::end();
?>

<script>
  $('#custom-seo-clone select[name="template_type"]').change(function() {
    mApp.blockPage();
    let sel = $('#custom-seo-clone #first_selection');
    let text = '';
    let ele = $('#custom-seo-clone #first_selection_select');
    ele.bootstrapDualListbox('destroy');
    ele.attr('multiple', false);
    ele.val('');
    if ($(this).val() !== 'Products') {
      ele.attr('multiple', true);
      sel.show().find('label').text($(this).find('option:selected').text());
    }
    else {
      sel.show().find('label').text('Category');
    }
    let data = {
      type: $(this).val(),
      list: ele.val(),
    };
    getItemList(data, function(data) {
      ele.find('option').remove();
      $.each(data, function(i, e) {
        ele.append('<option value=' + e + '>' + i + '</option>');
      });
      if ($('#custom-seo-clone select[name="template_type"]').val() !== 'Products') {
        ele.bootstrapDualListbox();
      }
      mApp.unblockPage();
    });
  });

  $('#custom-seo-clone select[name="first_selection"]').change(function() {
    if ($('#custom-seo-clone select[name="template_type"]').val() === 'Products') {
      mApp.blockPage();
      let sel = $('#custom-seo-clone #second_selection');
      let text = '';
      let ele = $('#custom-seo-clone  #second_selection_select');
      sel.show().find('label').text('Products');
      let data = {
        type: $('#custom-seo-clone select[name="template_type"]').val(),
        list: $('#custom-seo-clone  #first_selection_select').val(),
      };
      getItemList(data, function(data) {
        ele.find('option').remove();
        $.each(data, function(i, e) {
          ele.append('<option value=' + e + '>' + i + '</option>');
        });
        ele.val('');
        second.bootstrapDualListbox('refresh', true);
        mApp.unblockPage();
      });
    }
  });

  $('#custom-seo-clone').on('submit', function(e) {
    e.preventDefault();
    let type = $('#custom-seo-clone select[name="template_type"]').val();
    let id = ($('#custom-seo-clone #second_selection_select').val() != '' ? $('#custom-seo-clone #second_selection_select').val() : $('#custom-seo-clone #first_selection_select').val());
    let url = '/custom-seo/clone?id=' + $('#custom-seo-clone input[name="source_id"]').val();
    mApp.blockPage();
    $.ajax({
      method: 'POST',
      url: url,
      data: {
        'clone_id': id,
        'type': type,
      },
    }).done(function(data) {
      console.log(data);
      mApp.unblockPage();
      Swal.fire(
          '',
          'Success!',
          'success',
      ).then((result) => {
        if (result.value) {
          location.reload();
        }
      });
    }).fail(function(jqXHR, textStatus, errorThrown) {
      mApp.unblockPage();
      Swal.fire(
          '',
          errorThrown,
          'error',
      );
    });
  });

  $(document).ready(function() {
    second = $('#custom-seo-clone #second_selection_select').bootstrapDualListbox();
  });

</script>
