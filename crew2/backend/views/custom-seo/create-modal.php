<?php

use yii\bootstrap4\Modal;
use yii\helpers\HTML;

Modal::begin([
    'id' => 'create-custom-seo-modal',
    'title' => 'Create SEO',
    'toggleButton' => false
]);


$type_list = array('Category', 'Game Blog', 'Products');
?>

<form id="custom-seo-create">
    <div class="form-group m-form__group row">
        <label class="col-4 col-form-label">Type</label>
        <div class="col-8">
            <?= HTML::dropDownList('template_type', '', array_combine($type_list, $type_list), ['class' => 'form-control m-input', 'prompt' => '']) ?>
        </div>
    </div>

    <div id="first_selection_div" class="form-group m-form__group row" style="display:none">
        <label class="col-4 col-form-label"></label>
        <div class="col-8">
            <?= HTML::dropDownList('first_selection', '', [], ['class' => 'form-control m-input', 'prompt' => '']) ?>
        </div>
    </div>

    <div id="second_selection_div" class="form-group m-form__group row m-hidden" style="display:none">
        <label class="col-4 col-form-label"></label>
        <div class="col-8">
            <?= HTML::dropDownList('second_selection', '', [], ['class' => 'form-control m-input', 'prompt' => '']) ?>
        </div>
    </div>

    <div class="m-form__actions">
        <button type="submit" class="btn btn-primary">Create</button>
    </div>
</form>
<?php
Modal::end();

$this->registerJs(<<<JS
    function getItemList(data, success) {
        $.ajax({
            method: "POST",
            url: '/custom-seo/get-item-list',
            data: data,
            dataType: 'json',
        }).done(function(data){
            success(data);
        });
        
    }
JS
    , \yii\web\View::POS_END
);

$this->registerJs(<<<JS
    $('select[name="template_type"]').change(function(){
        mApp.blockPage();
        let sel = $('#first_selection_div');
        let text = '';
        let ele = $('select[name="first_selection"]');
        ele.val('');
        if($(this).val() !== 'Products'){
            sel.show().find('label').text(\$(this).find('option:selected').text());
            $('#second_selection_div').hide();
        }
        else{
            sel.show().find('label').text('Category');
        }
        let data = {
            type : $('select[name="template_type"]').val(),
            list : $('select[name="first_selection"]').val()
        };

        var s = '';
        $("#first_selection").empty();
        getItemList(data,function(data){
            ele.find('option').remove();
            $.each(data,function(i,e){
                s += ('<option value='+e+'>'+i+'</option>');
            });
            ele.append(s);
            ele.val('');
            mApp.unblockPage();
        });
    });

    $('select[name="first_selection"]').change(function(){
        if($('select[name="template_type"]').val() === 'Products'){
            mApp.blockPage();
            let sel = $('#second_selection_div');
            let text = '';
            let ele = $('select[name="second_selection"]');
            sel.show().find('label').text('Products');
            let data = {
                type : $('select[name="template_type"]').val(),
                list : $('select[name="first_selection"]').val()
            };
            var s = '';
            $("#second_selection").empty();
            getItemList(data,function(data){
                ele.find('option').remove();
                $.each(data,function(i,e){
                    s += ('<option value='+e+'>'+i+'</option>');
                });
                ele.append(s);
                ele.val('');
                mApp.unblockPage();
            });
        }
    });
    
    $('#custom-seo-create').on('submit',function(e){
        e.preventDefault();
        let type = $('select[name="template_type"]').val();
        let id = ($('select[name="second_selection"]').val() != '' ? $('select[name="second_selection"]').val() : $('select[name="first_selection"]').val());
        let url = '/custom-seo/update?type='+type+'&id='+id;
        window.location.href = url;
    });
    
JS
    , \yii\web\View::POS_READY
);

?>


