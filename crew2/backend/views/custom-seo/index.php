<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;

$this->title = 'SEO';

$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = 'SEO';
$this->params['layoutContent'] = 'block';

echo Yii::$app->controller->renderPartial('create-modal');

\backend\assets\DualListBoxAsset::register($this);
?>
<div class="game-product-attribute-index">

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        &nbsp;
                    </h3>
                </div>
            </div>

            <div class="m-portlet__head-tools">
                <div class="m-portlet__head-tools">
                    <div class="m--align-right">
                        <?php  if (Yii::$app->user->can('/custom-seo/get-item-list')||Yii::$app->user->can('/custom-seo/*')) {
                            echo '<a href="javascript:void(0);" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill" onclick="$(\'#create-custom-seo-modal\').modal(\'toggle\');">
                            <span>
                                <i class="la la-plus"></i>
                                <span>New</span>
                            </span>
                        </a>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="m-portlet__body">
            <div class="m-section">
                <!--begin: Datatable -->
                <div class="m-section__content">
                    <?php
                    echo \yii\grid\GridView::widget([
                        'dataProvider' => $provider,
                        'filterModel' => $filter,
                        'filterUrl' => $filter->filterUrl,
                        'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
                        'columns' => [
                            [
                                'header' => '#',
                                'content' => function ($model, $key, $index) use ($filter) {
                                    return $index + $filter->startAt;
                                },
                            ],
                            [
                                'attribute' => 'type',
                                'filter' => Html::activeDropDownList($filter, 'type', [
                                    '1' => 'Home',
                                    '2' => 'Browse',
                                    '3' => 'Search',
                                    '4' => 'Game Key',
                                    '5' => 'Category',
                                    '6' => 'Products',
                                    '7' => 'Game Blog'
                                        ], [
                                    'prompt' => '',
                                    'class' => 'form-control'
                                ]),
                            ],
                            'title',
                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{update} {clone}'),
                                'contentOptions' => ['style' => 'white-space: nowrap;'],
                                'buttons' => [
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<span class="glyphicon glyphicon-pencil"></span>', \yii\helpers\Url::to([
                                                            'custom-seo/update',
                                                            'id' => $model['reference_data_id'],
                                                            'type' => $model['type']
                                                        ])
                                        );
                                    },
                                    'clone' => function ($url, $model, $key) {
                                        return
                                                Html::a('<span class="fa fa-copy"></span>', 'javascript:void(0)', [
                                                    'onclick' => 'requestCloneModal("' . Url::to([
                                                        'custom-seo/clone',
                                                        'id' => $model['id']
                                                    ]) . '")'
                                        ]);
                                    },
                                ]
                            ],
                        ],
                    ]);

                    echo $filter->renderPageSizer();

                    Modal::begin([
                        'title' => '',
                        'id' => 'generalModal',
                        'size' => Modal::SIZE_LARGE
                    ]);

                    Modal::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function requestCloneModal(url) {
        $.ajax({
            method: 'GET',
            url: url,
        }).done(function (data) {
            $('#clone-custom-seo-modal').remove();
            $('body').append(data);
            $('#clone-custom-seo-modal').modal('show');
        });
    }
</script>