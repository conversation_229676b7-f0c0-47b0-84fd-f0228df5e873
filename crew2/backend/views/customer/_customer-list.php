<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use yii\widgets\LinkPager;
?>

<div class="customer-list">
    <div class="row">
        <div class="col-xl-12">
            <div class="m-portlet m-portlet--mobile">
                <div class="m-portlet__body">
                    <?php
                    echo GridView::widget([
                        'dataProvider' => $provider,
                        'columns' => [
                            [
                                'header' => 'Customer ID',
                                'attribute' => 'customers_id',
                                'format' => 'raw',
                                'value' => function($data) {
                                    $html = '<a href="' . Yii::$app->params['oldcrew.baseUrl'] . '/customers.php?cID=' . $data['customers_id'] . '&action=edit" target="_blank">' . $data['customers_id'] . '</a>';
                                    return $html;
                                },
                            ],
                            [
                                'header' => 'Email Address',
                                'attribute' => 'customers_email_address',
                            ],
                            [
                                'header' => 'Account Created',
                                'attribute' => 'customers_info_date_account_created',
                            ],
                            [
                                'header' => 'Last Logon',
                                'attribute' => 'customers_info_date_of_last_logon',
                            ],
                            [
                                'header' => 'Status',
                                'attribute' => 'customers_status',
                                'value' => function ($data) {
                                    return ($data['customers_status'] == 1) ? 'Active' : 'Not Active';
                                },
                            ],
                            [
                                'header' => 'Action',
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{update}'),
                                'contentOptions' => ['style' => 'white-space: nowrap;'],
                                'buttons' => [
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<span class="glyphicon glyphicon-pencil"></span>', ['/customer/update', 'id' => (string) $model['customers_id']], [
                                                    'title' => 'Update',
                                                    'aria-label' => 'Update'
                                        ]);
                                    }
                                ],
                            ],
                        ],
                        'layout' => "{summary}\n{items}",
                    ]);

                    $form = ActiveForm::begin([
                                'id' => 'customer-pagesize-form',
                                'method' => 'get',
                                'action' => ['/customer/index']
                    ]);

                    echo Html::hiddenInput('page', $model->page);
                    echo Html::hiddenInput('customers_id', $model->customers_id);
                    echo Html::hiddenInput('tax_pending_status', $model->tax_pending_status);
                    echo Html::hiddenInput('tax_verified_status', $model->tax_verified_status);
                    echo Html::hiddenInput('tax_failed_status', $model->tax_failed_status);

                    echo \backend\widgets\MetronicPager::renderPager($provider);

                    ActiveForm::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>