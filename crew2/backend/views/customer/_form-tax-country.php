<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Tax
                </h3>
            </div>
        </div>
    </div>

    <?php if (!$model->customerTaxInfo) { ?>
        <div class="m-portlet__body">
            <div class="m-form__section">
                N/A
            </div>
        </div>
        <?php
    } else {
    $option = ['class' => 'form-control'];
    $fieldIdArray = [];
    $disabled = $model->customerTaxInfo->orders_tax_customers_status == 3;

    $form = ActiveForm::begin([
        'id' => 'tax-customer-form',
        'options' => [
            'class' => 'm-form m-form--fit m-form--label-align-right',
        ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="m-form__section">
            <div class="form-group m-form__group row">
                <label class="col-lg-2">Country</label>
                <div class="col-lg-4">
                    <?= $model->taxConfig->country_name ?>
                </div>

                <label class="col-lg-2">Title</label>
                <div class="col-lg-4">
                    <?= $model->taxConfigDesc->orders_tax_title ?>
                </div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Order Tax (%)</label>
                <div class="col-lg-4">
                    <?= $model->taxConfig->orders_tax_percentage ?>
                </div>

                <label class="col-lg-2">Business Tax (%)</label>
                <div class="col-lg-4">
                    <?= $model->taxConfig->business_tax_percentage ?>
                </div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Business Name</label>
                <div class="col-lg-4">
                    <?= $model->customerTaxInfo->business_name ?>
                </div>

                <label class="col-lg-2"><?= $model->taxConfigDesc->orders_tax_title_short; ?> Number</label>
                <div class="col-lg-4">
                    <?= $model->customerTaxInfo->business_tax_number ?>
                </div>
            </div>

            <?php
            if ($model->customerTaxInfo && $model->customerTaxInfo->orders_tax_customers_data) {
            $inputArraySnapshot = json_decode($model->customerTaxInfo->orders_tax_customers_data, true);
            $inputArray = $inputArraySnapshot['field_data'];
            foreach ($inputArray as $key => $value) {
                $sort['sort_order'][$key] = $value['sort_order'];
                $sort['id'][$key] = $value['id'];
            }
            # sort by sort_order asc and then id asc
            array_multisort($sort['sort_order'], SORT_ASC, $sort['id'], SORT_ASC, $inputArray);

            if (count($inputArray)) {
            $cnt = 0;

            foreach ($inputArray as $key => $value) {
                // Skip status = false, skip basic mandatory field, skip dynamic input type label or information text
                if ($value['status'] != 1 || $value['id'] == 1 || $value['id'] == 2 || $value['type'] == 'display_label' || $value['type'] == 'information_text') {
                    continue;
                }

                if ($cnt == 0) {
                    ?>
                    <div class="form-group m-form__group row">
                <?php } ?>
                <label class="col-lg-2"><?= $value['title']; ?></label>
                <div class="col-lg-4">
                    <?= $value['value']; ?>
                </div>
                <?php if ($cnt) { ?>
                    </div>
                    <?php
                    $cnt = -1;
                }
                $cnt++;
            }
            ?>
            <?php if ($cnt) { ?>
        </div>
        <?php } ?>
        <?php
        }
        }
        ?>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <?php
                $verificationStatusArray = $model->getTaxVerificationSetting();
                if ($model->customerTaxInfo->orders_tax_customers_status != 3) {
                    unset($verificationStatusArray[3]);
                }
                echo $form->field($model->customerTaxInfo, 'orders_tax_customers_status')
                    ->dropDownList($verificationStatusArray, [
                        'disabled' => $disabled,
                        'class' => 'selectpicker m-datatable__pager-size',
                        'name' => 'status',
                        'onchange' => "if ($(this).val() == 2) {
                                        $('#business-tax-show-customer').show();
                                    } else {
                                        $('#business-tax-show-customer').hide();
                                    }"
                    ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Remarks</label>
            <div class="col-lg-10">
                <?= HTML::textarea('remarks', '',
                    array_merge($option, ['disabled' => $disabled, 'required' => true, 'rows' => 4])); ?>
                <div id="business-tax-show-customer" style="display: none;">
                    <?= Html::checkbox('show_customer', false,
                        ['id' => '_check-business-tax-show-customer', 'label' => "show remarks to customer"]); ?>
                </div>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <!-- tax log -->
                <div class="m-portlet m-portlet--accent m-portlet--collapsed m-portlet--head-solid-bg m-portlet--head-sm"
                     m-portlet="true" id="m_portlet_tools_2">
                    <div class="m-portlet__head">
                        <div class="m-portlet__head-caption">
                            <div class="m-portlet__head-title">
                                    <span class="m-portlet__head-icon">
                                        <i class="flaticon-time"></i>
                                    </span>
                                <span class="m-portlet__head-text">
                                        Remarks History
                                    </span>
                            </div>
                        </div>
                        <div class="m-portlet__head-tools">
                            <ul class="m-portlet__nav">
                                <li class="m-portlet__nav-item">
                                    <a href="#" m-portlet-tool="toggle"
                                       class="m-portlet__nav-link m-portlet__nav-link--icon"><i
                                                class="la la-angle-down"></i></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="m-portlet__body">
                        <div class="m-scrollable" data-scrollable="true" data-height="300" data-mobile-height="300">
                            <?php
                            foreach ($model->customerTaxHistory as $key => $value) {
                                $_color = "ancent";
                                $custDataArray = json_decode($value->orders_tax_customers_data, true);
                                if (isset($custDataArray['canceled_at'])) {
                                    $_color = "danger";
                                    $_date = date('Y-m-d H:i:s', $custDataArray['canceled_at']);
                                    $_changeby = $model->customerTaxInfo->customers_id;
                                } else {
                                    if (isset($custDataArray['updated_at'])) {
                                        $_color = "warning";
                                        $_date = date('Y-m-d H:i:s', $custDataArray['updated_at']);
                                        $_changeby = $custDataArray['updated_by'];
                                    } else {
                                        $_color = "info";
                                        $_date = date('Y-m-d H:i:s', $custDataArray['created_at']);
                                        $_changeby = $model->customerTaxInfo->customers_id;
                                    }
                                }
                                ?>

                                <div class="m-timeline-2">
                                    <div class="m-timeline-2__items  m--padding-top-25">
                                        <div class="m-timeline-2__item">
                                            <div class="m-timeline-2__item-cricle">
                                                <i class="fa fa-genderless m--font-<?= $_color; ?>"></i>
                                            </div>
                                            <div class="m-timeline-2__item-text  m--padding-top-5">
                                                <b><?= $_date; ?></b><br/>
                                                <?= $_changeby; ?><br/>
                                                Status
                                                : <?= $model->getTaxVerificationSetting()[$value->orders_tax_customers_status]; ?>
                                                <br/>
                                                <?= (isset($custDataArray['remarks']) && !isset($custDataArray['canceled_at']) ? $custDataArray['remarks'] : ''); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit"
                            class="btn btn-success" <?= ($model->customerTaxInfo->orders_tax_customers_status == 3) ? "disabled" : ""; ?>>
                        Submit
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php ActiveForm::end(); ?>
<?php } ?>
</div>