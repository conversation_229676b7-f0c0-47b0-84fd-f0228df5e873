<?php

use yii\helpers\Html;
use yii\bootstrap\ActiveForm;

$this->title = 'Profile';

$this->params['breadcrumbs'][] = 'Customer';
$this->params['breadcrumbs'][] = 'Profile';
$this->params['layoutContent'] = 'block';

$form = ActiveForm::begin([
    'id' => 'customer-search-form',
    'action' => ['/customer/index'],
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right',
    ],
]);
?>
<div class="m-portlet m-portlet--primary <?= ($provider ? "m-portlet--collapsed" : ""); ?> m-portlet--head-solid-bg m-portlet--head-sm"
     m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i
                                class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Customer ID</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'customers_id')->textInput(['id' => 'customers-id'])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Business Tax</label>
            <div class="col-lg-2 col-form-label">
                <?= $form->field($model, 'tax_pending_status')->inline()->checkbox()->label('Pending'); ?>
            </div>
            <div class="col-lg-2 col-form-label">
                <?= $form->field($model, 'tax_verified_status')->inline()->checkbox()->label('Verified'); ?>
            </div>
            <div class="col-lg-2 col-form-label">
                <?= $form->field($model, 'tax_failed_status')->checkbox()->label('Fail'); ?>
            </div>
            <div class="col-lg-2 col-form-label">
                <?= $form->field($model, 'tax_canceled_status')->checkbox()->label('Canceled'); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_search" name="submit_search" class="btn btn-primary">Search</button>
                <button type="button" class="btn btn-default" onclick="_reset();">Reset</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<!-- Render the customer list -->
<?php
if (!$model->errors && $provider) {
    echo (isset($provider) && $provider) ? $this->render('_customer-list', [
        'model' => $model,
        'provider' => $provider,
    ]) : '';
}
?>

<script type="text/javascript">
    function _reset() {
        $(':input').not(':button, :submit, :reset, :hidden, :checkbox, :radio').val('');
        $(':checkbox, :radio').prop('checked', false);
    }
</script>