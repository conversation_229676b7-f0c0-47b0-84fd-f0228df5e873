<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;

$this->title = 'Profile';

$this->params['breadcrumbs'][] = 'Customer';
$this->params['breadcrumbs'][] = ['label' => 'Profile', 'url' => ['customer/index']];
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Profile
                </h3>
            </div>
        </div>
    </div>
    <!--begin::Form-->
    <?php
    $form = ActiveForm::begin([
                'id' => 'form-update-customers',
                'action' => Url::toRoute("/customer/update-block-status?id=" . $model->customers_id),
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right',
                ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="m-form__section">
            <div class="form-group m-form__group row">
                <label class="col-lg-2">Customer ID</label>
                <div class="col-lg-4">
                    <a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/customers.php?cID=<?= $model->customers_id ?>&action=edit" target="_blank"><?= $model->customers_id ?></a>
                </div>

                <label class="col-lg-2">Status</label>
                <div class="col-lg-4">
                    <?= (isset($model->customersInfo->customers_status) && $model->customersInfo->customers_status == '1') ? 'Active' : 'Inactive'; ?>
                </div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">First Name</label>
                <div class="col-lg-4"><?= $model->customersInfo->customers_firstname; ?></div>

                <label class="col-lg-2">Last Name</label>
                <div class="col-lg-4"><?= $model->customersInfo->customers_lastname; ?></div>
            </div>

            <div class="m-separator m-separator--dashed m-separator--lg"></div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Email Address</label>
                <div class="col-lg-4"><?= $model->customersInfo->customers_email_address; ?></div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-lg-2">Valid Email Address</label>
                <div class="col-lg-4">
                    <?=
                    SwitchInput::widget([
                        'name' => 'valid_email',
                        'value' => (!isset($model->emailValidation->mx_found) || $model->emailValidation->mx_found == 1) ? 1 : 0,
                        'disabled' => (!isset($model->emailValidation->mx_found) || $model->emailValidation->mx_found == 1) ? true : false,
                        'pluginOptions' => [
                            'onText' => '<i class="fa fa-check"></i>',
                            'offText' => '<i class="fa fa-times"></i>',
                        ]
                    ]);
                    ?>
                </div>

                <label class="col-lg-2">Email Address Blocked</label>
                <div class="col-lg-4">
                    <?=
                    SwitchInput::widget([
                        'name' => 'email_blocked',
                        'value' => (isset($model->sesBounce) && !empty($model->sesBounce)) ? 1 : 0,
                        'disabled' => (isset($model->sesBounce) && !empty($model->sesBounce)) ? false : true,
                        'pluginOptions' => [
                            'onText' => '<i class="fa fa-check"></i>',
                            'offText' => '<i class="fa fa-times"></i>',
                        ]
                    ]);
                    ?>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2">

                </div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success" <?= (isset($model->emailValidation->mx_found) && $model->emailValidation->mx_found != '1') || (isset($model->sesBounce) && !empty($model->sesBounce)) ? '' : ' disabled="disabled" '; ?>>Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('/customer/index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<?= $this->render('_form-tax-country', ['model' => $model]); ?>