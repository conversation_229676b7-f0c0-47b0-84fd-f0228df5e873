<?php
/* @var $this yii\web\View */

/* @var $model backend\models\FileUploadForm */

use yii\bootstrap4\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\web\View;
use yii\helpers\Html;
use common\models\ImageConfiguration;

$this->title = 'Media';

$this->params['breadcrumbs'][] = 'Tool';
$this->params['breadcrumbs'][] = ['label' => 'Media', 'url' => ['file-uploader/list']];
$this->params['layoutContent'] = 'block';

\backend\assets\DataTableAsset::register($this);
\backend\assets\AppAsset::register($this);
\backend\assets\UppyAssets::register($this);

$fieldName = Html::getInputName($model, 'imageFile');
$max_size = (int) (str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024);
$model->subDirectory = (empty($model->subDirectory) ? date('Y/m', time()) : '');

$this->registerJS(<<<JS
  var t = $('#uploadedImage').DataTable(
    {
      dom: '<\'row\'<\'col-sm-6\'l><\'col-sm-6 m--align-right\'B>>' + '<\'row\'<\'col-sm-12\'tr>>' + '<\'row\'<\'col-sm-5\'i><\'col-sm-7\'p>>',
      buttons: [
        {
          extend: 'excel',
          text: '<i class="fa fa-download"></i> Export',
          title: null,
          exportOptions: {
            modifier: {
              selected: null,
            },
            columns: [0, 1],
          },
        },
      ],
      columnDefs: [
        {
            "targets": [1],
            "visible": false,
            "searchable": false
        }
      ],
      select: true,
    },
  )
  var uppy = Uppy.Core({
    onBeforeUpload: (files)=>{
          if($("#fileuploadform-directory").val() == ""){
            uppy.info('Directory Cannot Be Empty', 'error', 5000)
            return false;
          }
          form = $("#w0");
          uppy.setMeta(getFormData(form));
      },
      restrictions: {
      maxFileSize: $max_size,
      allowedFileTypes: ['image/*'],
    },
  }).use(Uppy.Dashboard, {
    inline: true,
    width: '100%',
    target: '#drag-drop-area',
    replaceTargetContent: true,
    showProgressDetails: true,
    debug: true,
  }).use(Uppy.XHRUpload, {
    endpoint: '/file-uploader/upload',
    formData: true,
    fieldName: '{$fieldName}',
    timeout: 300 * 1000,
    limit: 10,
    getResponseError: function (responseText, xhr) {
      return new Error(JSON.parse(responseText).message)
    },
  })
  

  uppy.on('before-upload',(file) =>{
    form = $("#w0");
    uppy.setMeta(getFormData(form));
    return false;
  })

  // And display uploaded files
  uppy.on('upload-success', (file, response) => {
    const data = JSON.parse(response.body)
    const url = data.uploadURL
    const fileName = data.fileName
    uppy.setFileState(file.id, {uploadURL: url})
    t.row.add([
      fileName,
      url,
      `
      <a href="javascript:void(0)" onclick='viewImage("`+url+`")' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"><i class="la la-search"></i></a>
      <a href="javascript:void(0)" onclick='copyToClipboard(this, "link")' data-link='`+url+`' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"><i class="la la-copy"></i></a>
      `,
    ]).draw(false)
  })
  uppy.on('upload-error', (file, error, response) => {
    uppy.info(error, 'error', 5000)
  })
JS
        , View::POS_READY);
?>

<?php
/**
 * @var \common\components\ImageOptimizer $imageOptimizer
 */
$imageOptimizer = Yii::$app->imageOptimizer;

if (empty($model->imageCompressionRatio)) {
    $model->imageCompressionRatio = $imageOptimizer->getDefaultRatio();
}
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    New
                </h3>
            </div>
        </div>
    </div>

    <?php
    $form = ActiveForm::begin([
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ]
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Directory</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'directory')->dropDownList(
                        ArrayHelper::map(ImageConfiguration::getDirectory(), 'image_category', 'image_category'), [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>

            <label class="col-lg-2 col-form-label">Sub-Directory</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'subDirectory')->textInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Compression Level</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'imageCompressionRatio')->dropDownList($imageOptimizer->getSupportedRatio(), [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>

            <div class="col-lg-6">
                <?= $form->field($model, 'allowOverwrite')->checkbox(); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <div id="drag-drop-area"></div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>


<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Media Uploaded
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <table id="uploadedImage" class="table table-striped- table-bordered table-hover table-checkable">
            <thead>
                <tr>
                    <th>File Name</th>
                    <th>Url</th>
                    <th>Action</th>
                </tr>
            </thead>
        </table>
    </div>
</div>