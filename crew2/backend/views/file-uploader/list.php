<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;
use kartik\daterange\DateRangePicker;
use kartik\datetime\DateTimePickerAsset;
use yii\bootstrap4\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\web\View;
use common\models\ImageConfiguration;

$this->title = 'Media';

$this->params['breadcrumbs'][] = 'Tool';
$this->params['breadcrumbs'][] = 'Media';
$this->params['layoutContent'] = 'block';

\backend\assets\DataTableAsset::register($this);
\backend\assets\CodePrettifyAsset::register($this);
DateTimePickerAsset::register($this);
\backend\assets\AppAsset::register($this);
Modal::begin([
    'title' => '',
    'id' => 'generalModal',
    'size' => Modal::SIZE_LARGE
]);

Modal::end();

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ]
        ]);
?>
<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Directory</label>
            <div class="col-lg-4" id="directory">
                <?=
                $form->field($model, 'directory')->dropDownList(ArrayHelper::map(ImageConfiguration::getDirectory(), 'image_category', 'image_category'), [
                    'prompt' => 'Select',
                    'onchange' => 'if($("#directory :selected").val() != 0){ $("#errors").addClass("hide"); } else { $("#errors").removeClass("hide"); };'
                ])->label(false);
                ?>
                <span id="errors" class="text-danger position-relative small align-bottom hide" style="bottom: 15px">Directory cannot be blank</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Sub-Directory</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'subDirectory')->textInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="button" id="submit_search" name="submit_search" class="btn btn-primary" onclick="query();">Search</button>
                <button type="reset" class="btn btn-default">Reset</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('file-uploader/index'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <div class="m-section__content">
                <table class="table table-striped- table-bordered table-hover table-checkable" id="m_table_1">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Url</th>
                            <th>Created At</th>
                            <th>Size</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
$this->registerJs(<<<JS
function query () {
 if($("#directory :selected").val() != 0){
   $("#errors").addClass("hide");
  let url = '/file-uploader/get-upload-history'
  let column = [
    {data: 'fileName'},
    {
      data: 'url',
      visible: false,
    },
    {
      data: {
        _: 'lastModified',
        filter: 'timestamp',
        sort: 'timestamp'
      },
    },
    {data: 'size'},
    {data: 'key'},
  ];
  let columnDef = [
    {
      targets: -1,
      title: 'Actions',
      orderable: false,
      render: function (data, type, full, meta) {
        return `
        <a href="javascript:void(0)" onclick='viewImage("`+full.url+`")' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"><i class="la la-search"></i></a>
        <a href="javascript:void(0)" onclick='copyToClipboard(this, "link")' data-link='`+full.url+`' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill"><i class="la la-copy"></i></a>
        <a href="javascript:void(0)" onclick='deleteItem(`+meta.row+`, "`+full.directory+`", "`+full.key+`");return false;' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill;"><i class="la la-trash"></i></a>
        `
      },
    },
  ];
  let order = [[2, 'desc']];
  let initComplete = function () {
    let table = $('#m_table_1').DataTable()
    let rowFilter = $('<tr class="filter"></tr>').appendTo($(table.table().header()))
    this.api().columns().every(function () {
      var column = this
      var input
      switch (column.title()) {
        case 'File Name':
        
          input = $(`
							<div class="input-group">
								<input type="text" class="form-control form-control-sm m-input" data-col-index="` + column.index() + `"/>
							</div>`)
            break;
           
          case 'Size':
            input = $(`<div class="input-group"></div>`)
            break;
        case 'Created At':
          input = $(`
							<div class="input-group date">
								<input type="text" class="form-control form-control-sm m-input" readonly placeholder="From" id="m_datepicker_1"
								 data-col-index="` + column.index() + `" data-custom="true" />
								<div class="input-group-append">
									<span class="input-group-text"><i class="la la-calendar-o glyphicon-th"></i></span>
								</div>
							</div>`)
          break
        case 'Actions':
          var search = $(`<button class="btn btn-brand m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-search"></i>
							    <span>Search</span>
							  </span>
							</button>`)
          var reset = $(`<button class="btn btn-secondary m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-close"></i>
							    <span>Reset</span>
							  </span>
							</button>`)
          $('<th>').append(search).append(reset).appendTo(rowFilter)
          $(search).on('click', function (e) {
            e.preventDefault()
            var params = {}
            $(rowFilter).find('.m-input').each(function () {
              var i = $(this).data('col-index')
              if ($(this).data('custom') != true) {
                table.column(i).search($(this).val() ? $(this).val() : '', false, false)
              }
            })
            if($("#m_datepicker_1").val()){
                $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
                  var filter_date = $('#m_datepicker_1').val().split(' - ')
                  if (filter_date) {
                    return (data[2] > moment(filter_date[0]).format('X') && data[2] < moment(filter_date[1]).format('X'))
                  }
                  return true
                })
            }
            table.table().draw()
          })
          $(reset).on('click', function (e) {
            e.preventDefault()
            $(rowFilter).find('.m-input').each(function (i) {
              $(this).val('')
              table.column($(this).data('col-index')).search('', false, false)
            })
            table.table().draw()
          })
          break
      }
      if (input) {
        $(input).appendTo($('<th>').appendTo(rowFilter))
      }
    })
    jQuery('#m_datepicker_1').
      daterangepicker({ 
        'timePicker': true,
        'timePickerIncrement': 15,
        'locale': {'format': 'YYYY-MM-DD hh:mm A'},
        'cancelButtonClasses': 'btn-secondary',
        'autoUpdateInput': false,
        'opens' : 'center',
        'cancelLabel': 'Clear'
      })
      .on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD hh:mm A') + ' - ' + picker.endDate.format('YYYY-MM-DD hh:mm A'));
      $(this).trigger('change');
      })
      .on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
      $(this).trigger('change');
    });
    $('#m_form_status, #m_form_type').selectpicker();
  };
  DatatablesDataSourceAjaxServer.init(url,{      
      dom: '<\'row\'<\'col-sm-6\'l><\'col-sm-6 m--align-right\'B>>' + '<\'row\'<\'col-sm-12\'tr>>' + '<\'row\'<\'col-sm-5\'i><\'col-sm-7\'p>>',
      buttons: [
        {
          extend: 'excel',
          text: '<i class="fa fa-download"></i> Export',
          className: 'btn-warning',
          title: null,
          exportOptions: {
            modifier: {
              selected: null,
            },
            columns: [0,1],
          }
        }
      ],
      select: true,
      ajax: {
          url: url,
          type: 'GET',
          data: getFormData($("#w0"))
      },
      }, column, columnDef, order, initComplete);
        $(".buttons-excel").removeClass("btn-secondary");
}else {
           $("#errors").removeClass("hide");
        return false;
   }
        }
function deleteItem(index, directory, key){
  Swal.fire({
      title: 'Confirm to Delete : '+key+'?',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      preConfirm: (login) => {
        return fetch('/file-uploader/delete?directory='+directory+'&path='+key)
          .then(response => {
            if (!response.ok) {
              throw new Error(response.statusText)
            }
          let table = $('#m_table_1').DataTable();
          let data = table.row(index).remove().draw();
          })
          .catch(error => {
            Swal.showValidationMessage(
              `Fail to Remove Item}`
            )
          })
      },
    allowOutsideClick: () => !Swal.isLoading()
    })
}
       
        

JS
        , \yii\web\View::POS_END);

