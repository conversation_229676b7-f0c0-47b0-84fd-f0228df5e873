<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
        ]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Type<span style="color: red;">*</span></label>
        <div class="col-lg-4">
            <?=
            HTML::dropDownList('type', (isset($data['type']) ? $data['type_id'] : ''), [
                '1' => 'Language',
                '2' => 'Genre',
                '3' => 'Feature',
                '4' => 'Platform',
                '5' => 'Region'
            ], ['class' => 'selectpicker m-datatable__pager-size', 'prompt' => 'Select','required' => true]);
            ?>
        </div>

        <label class="col-lg-2 col-form-label">Sort Order</label>
        <div class="col-lg-4">
            <?= HTML::input('text', 'sort_order', (isset($data['sort_order']) ? $data['sort_order'] : ''), ['class' => 'form-control']); ?>
        </div>
    </div>

    <?php $language = Yii::$app->enum->getLanguage('listData'); ?>
    <!--begin::Portlet-->
    <div class="m-portlet__body">
        <div class="m-portlet m-portlet--tabs">
            <div class="m-portlet__head">
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                        <?php foreach ($language as $i => $v) { ?>
                            <li class="nav-item m-tabs__item">
                                <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                    <?= $v; ?>
                                </a>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="tab-content">
                    <?php
                    $option = ['class' => 'form-control'];
                    foreach ($language as $i => $v) {
                        ?>
                        <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Description<?= ($i == 1 ? '<span style="color: red;">*</span>' : ''); ?></label>
                                <div class="col-lg-9">
                                    <?= HTML::textarea("description[$i]", (!empty($data['description'][$i]) ? $data['description'][$i] : ''), ($i == 1 ? array_merge($option, ['required' => true]) : $option)); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    <!--end::Portlet-->
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('game-product-attribute/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<?php
$this->registerJs(<<<JS
 $(document).ready(function () {
    $('select[name="type"]').on('change', function(e) {
        mApp.blockPage();
        $.ajax({
            method: "GET",
            url: '/game-product-attribute/next-sort-order?type='+$(this).val(),
        }).done(function (data) {
             mApp.unblockPage();
            $('input[name="sort_order"]').val(data);
        });
    }) ;
 });
JS
        , \yii\web\View::POS_READY);

