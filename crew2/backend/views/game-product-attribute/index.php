<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;

$this->title = 'Game Key';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Game Key';
$this->params['breadcrumbs'][] = 'Attribute';
$this->params['layoutContent'] = 'block';
?>

<div class="game-publisher-index m-portlet m-portlet--mobile">

    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Attribute
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?php $type = array('1' => 'Language', '2' => 'Genre', '3' => 'Feature', '4' => 'Platform', '5' => 'Region'); ?>
        <?=
        \yii\grid\GridView::widget([
            'dataProvider' => $provider,
            'filterModel' => $filter,
            'filterUrl' => $filter->filterUrl,
            'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
            'columns' => [
                [
                    'header' => '#',
                    'content' => function ($model, $key, $index) use ($filter) {
                        return $index + $filter->startAt;
                    },
                ],
                [
                    'attribute' => 'type',
                    'filter' => Html::activeDropDownList($filter, 'type', $type, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']),
                ],
                'description',
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{update}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                ],
            ],
        ]);

        echo $filter->renderPageSizer();

        Modal::begin([
            'title' => 'Test',
            'id' => 'generalModal',
            'size' => Modal::SIZE_LARGE
        ]);

        Modal::end();
        ?>
    </div>
</div>

<script>
    function viewItemDetails(url) {
        $.ajax({
            method: "GET",
            url: url,
            dataType: 'json'
        })
                .done(function (data) {
                    jQuery("#generalModal .modal-body").html(data['body']);
                    jQuery("#generalModal .modal-title").text(data['title']);
                    jQuery("#generalModal").modal('show');
                });
    }
</script>