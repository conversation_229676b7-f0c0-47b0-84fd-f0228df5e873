<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DatePicker;
use kartik\widgets\SwitchInput;
use common\models\GamePublisherProductForm;
?>

<?php
$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
            ],
        ]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Release Date</label>
        <div class="col-lg-4">
            <?php
            echo DatePicker::widget([
                'convertFormat' => true,
                'name' => 'release_date',
                'options' => ['placeholder' => 'Release Date'],
                'value' => $data['release_date'],
                'pluginOptions' => [
                    'format' => 'yyyy-MM-dd',
                    'autoclose' => true,
                    'orientation' => 'bottom'
                ]
            ]);
            ?>
        </div>
        <?php
        $status_list = GamePublisherProductForm::getUpdateStatusListWithPermission(false, $data['status']);
        if (!empty($status_list) && $data['status'] != 6) {
            ?>
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <div class="input-group m-input-group">
                    <?= HTML::dropDownList('status', $data['status'], GamePublisherProductForm::STATUS, ['class' => 'form-control selectpicker m-datatable__pager-size']); ?>
                </div>
            </div>
        <?php } ?>
    </div>

    <?php if (Yii::$app->user->can('[Game Product] Update Price')) { ?>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Cost</label>
            <div class="col-lg-4">
                <div class="input-group m-input-group">
                    <div class="input-group-prepend"><span class="input-group-text"><?= (!empty($data['product_currency']) ? $data['product_currency'] : ''); ?></span></div>
                    <input type="text" class="form-control m-input" disabled value="<?= (!empty($data['cost_price']) ? $data['cost_price'] : ''); ?>">
                </div>
            </div>

            <label class="col-lg-2 col-form-label">Price</label>
            <div class="col-lg-4">
                <div class="input-group m-input-group">
                    <div class="input-group-prepend"><span class="input-group-text"><?= (!empty($data['product_currency']) ? $data['product_currency'] : ''); ?></span></div>
                    <input type="text" class="form-control m-input" name="price" value="<?= (!empty($data['product_price']) ? $data['product_price'] : ''); ?>">
                </div>
            </div>
        </div>
    <?php } ?>

    <?php $language = Yii::$app->enum->getLanguage('listData'); ?>

    <!--begin::Portlet-->
    <div class="m-portlet__body">
        <div class="m-portlet m-portlet--tabs">
            <div class="m-portlet__head">
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                        <?php foreach ($language as $i => $v) { ?>
                            <li class="nav-item m-tabs__item">
                                <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                    <?= $v; ?>
                                </a>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="tab-content">
                    <?php foreach ($language as $i => $v) { ?>
                        <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Product Name</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("title[$i]", (!empty($data[$i]['title']) ? $data[$i]['title'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Page Description</label>
                                <div class="col-lg-9">
                                    <?= HTML::textarea("description[$i]", (!empty($data[$i]['description']) ? $data[$i]['description'] : ''), ["class" => "form-control m-input", "row" => 8]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Notice</label>
                                <div class="col-lg-9">
                                    <?= HTML::textarea("notice[$i]", (!empty($data[$i]['notice']) ? $data[$i]['notice'] : ''), ["class" => "form-control m-input", "row" => 8]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Product Image Title</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("product_image_title[$i]", (!empty($data[$i]['product_image_title']) ? $data[$i]['product_image_title'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Product Image</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("product_image_url[$i]", (!empty($data[$i]['product_image_url']) ? $data[$i]['product_image_url'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Header Image Title</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("header_image_title[$i]", (!empty($data[$i]['header_image_title']) ? $data[$i]['header_image_title'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Header Image URL</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("header_image_url[$i]", (!empty($data[$i]['header_image_url']) ? $data[$i]['header_image_url'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Background Image URL</label>
                                <div class="col-lg-9">
                                    <?= HTML::textInput("background_image_url[$i]", (!empty($data[$i]['background_image_url']) ? $data[$i]['background_image_url'] : ''), ["class" => "form-control m-input"]); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    <!--end::Portlet-->

    <?php
    foreach ($form_model::PRODUCT_ATTRIBUTE_ID as $attribute => $id) {
        echo HTML::beginTag('div', array('class' => 'm-form__group form-group'));
        $val = $data['attribute'][$id];
        echo HTML::label(ucfirst($attribute));
        echo HTML::beginTag('div', array('class' => 'm-checkbox-list'));

        foreach ($val as $i => $v) {
            echo HTML::beginTag('label', array('class' => 'm-checkbox'));
            echo HTML::checkbox($attribute . '[]', (in_array($i, $data[$attribute])), ['value' => $i]);
            echo $v . HTML::beginTag('span') . HTML::endTag('span');
            echo HTML::endTag('label');
        }
        echo HTML::endTag('div') . HTML::endTag('div');
    }
    ?>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?=
                        Url::to([
                            'game-publisher-product/index',
                            'GamePublisherProductForm[game_publisher_id]' => $data['game_publisher_id'],
                            'GamePublisherProductForm[status]' => 0
                        ]);
                        ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>