<?php

use yii\bootstrap4\Modal;
use common\models\GamePublisherProductForm;

Modal::begin([
    'id' => 'batchUpdateModal',
    'title' => 'Batch Update Product',
    'toggleButton' => ['label' => '<i class="la la-pencil-square"></i> Batch Update', 'class' => 'btn btn-primary m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill m--align-right'],
    'size' => Modal::SIZE_LARGE
]);

$product_type = GamePublisherProductForm::PRODUCT_TYPE;

$po_company = \common\models\PoCompany::findAll(array('po_company_status' => 1));
$po_array = \yii\helpers\ArrayHelper::map($po_company, 'po_company_code', 'po_company_name');

$default_class = ['class' => 'form-control'];

$hidden_class = ['type' => 'div', 'option' => ['class' => 'form-group hidden-toggle']];

$field_list = array(
    [
        'label' => 'Product Image',
        'name' => 'product_image_url',
        'type' => 'text',
        'option' => $default_class,
        'wrapper_option' => $hidden_class
    ],
    [
        'label' => 'Header Image URL',
        'name' => 'header_image_url',
        'type' => 'text',
        'option' => $default_class,
        'wrapper_option' => $hidden_class
    ],
    [
        'label' => 'Background Image URL',
        'name' => 'background_image_url',
        'type' => 'text',
        'option' => $default_class,
        'wrapper_option' => $hidden_class
    ],
    [
        'label' => 'Page Description',
        'name' => 'description',
        'type' => 'textarea',
        'option' => array_merge($default_class, ['rows' => 10]),
        'wrapper_option' => $hidden_class
    ],
    [
        'no_wrapper' => false,
        'type' => 'button',
        'name' => 'Submit',
        'option' => ['class' => 'btn btn-success m--align-right', 'onclick' => 'batchUpdate();']
    ]
);

if ($is_new) {
    array_unshift($field_list, [
        'label' => 'Category ID',
        'name' => 'category_id',
        'type' => 'text',
        'option' => $default_class,
        'wrapper_option' => $hidden_class
    ]);

    array_unshift($field_list, [
        'label' => 'Cost Company',
        'name' => 'cost_company',
        'type' => 'select',
        'items' => $po_array,
        'option' => ['class' => 'form-control selectpicker m-datatable__pager-size'],
        'wrapper_option' => $hidden_class,
    ]);
}

if (Yii::$app->user->can('[Game Product] Update Price')) {
    array_unshift($field_list, [
        'label' => 'Mark Up (%)',
        'name' => 'mark_up',
        'type' => 'text',
        'option' => $default_class,
        'wrapper_option' => $hidden_class
    ]);
}

$status_list = GamePublisherProductForm::getUpdateStatusListWithPermission($is_new);

if (!empty($status_list)) {
    array_unshift($field_list, [
        'label' => 'Status',
        'name' => 'status',
        'type' => 'select',
        'items' => $status_list,
        'option' => [
            'class' => 'form-control selectpicker m-datatable__pager-size',
            'onchange' => 'batchUpdateStatusChange(this);'
        ],
        'wrapper_option' => $hidden_class
    ]);
}

echo GamePublisherProductForm::renderCustomForm($field_list);

Modal::end();
