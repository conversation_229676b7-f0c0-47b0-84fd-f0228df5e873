<?php

use common\models\GamePublisherProductForm;

$default_class = ['class' => 'form-control'];

$field_list = array(
    [
        'label' => 'ID',
        'name' => 'id',
        'type' => 'text',
        'val' => $data['id'],
        'option' => ['class' => 'form-control'],
        'wrapper_option'=> ['type' => 'div', 'option' => ['class' => 'd-none']]
    ],
    [
        'label' => 'Title',
        'name' => 'title',
        'type' => 'text',
        'val' => $data['title'],
        'option' => $default_class
    ],
    [
        'label' => 'URL Alias',
        'name' => 'url_alias',
        'type' => 'text',
        'val' => $data['url_alias'],
        'option' => $default_class
    ],
    [
        'no_wrapper' => false,
        'type' => 'button',
        'url' => null,
        'name' => 'Submit',
        'option' => ['class' => 'btn btn-success m--align-right', 'onclick' => 'updateTitleUrlAlias();']
    ]
);

echo GamePublisherProductForm::renderCustomForm($field_list);

