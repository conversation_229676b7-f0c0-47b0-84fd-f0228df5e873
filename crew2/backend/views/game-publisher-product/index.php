<?php

/**
 * @var $searchModel array
 **/

use yii\helpers\Html;
use yii\bootstrap4\Modal;
use common\models\GamePublisher;
use common\models\GamePublisherProductForm;
use yii\helpers\Url;

$publisher_list = \yii\helpers\ArrayHelper::map(GamePublisher::find()->all(), 'game_publisher_id', 'title');

$this->title = 'Game Key';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Game Key';
$this->params['breadcrumbs'][] = ['label' => 'Publisher', 'url' => ['game-publisher/index']];
$this->params['breadcrumbs'][] = 'Product List';
$this->params['layoutContent'] = 'block';

if($searchModel['actual_mark_up'] === '') {
    unset($searchModel['actual_mark_up']);
}

?>

<div class="game-publisher-product-index">
    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        <?= (isset($publisher_list[$searchModel['game_publisher_id']]) ? $publisher_list[$searchModel['game_publisher_id']] : ""); ?>
                    </h3>
                </div>
            </div>

            <div class="m-portlet__head-tools">
                <div class="m-portlet__head-tools">
                    <?php
                    if (Yii::$app->user->can('[Game Product] Batch Update')) {
                        echo Yii::$app->controller->renderPartial('batch-update', ['is_new' => $is_new]);
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="m-portlet__body">
            <?php
            echo \yii\grid\GridView::widget([
                'dataProvider' => $provider,
                'filterModel' => $searchModel,
                'filterUrl' => $searchModel->filterUrl,
                'summary' => 'Showing <b>' . ($searchModel->startAt) . '-' . ($searchModel->endAt) . '</b> of <b>' . $searchModel->totalCount . '</b> items.',
                'columns' => [
                    [
                        'header' => '#',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'content' => function ($model, $key, $index) use ($searchModel) {
                            return $index + $searchModel->startAt;
                        },
                    ],
                    [
                        'class' => 'yii\grid\CheckboxColumn',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'checkboxOptions' => function ($model) use ($searchModel) {
                            return [
                                'value' => $model['id'],
                                'disabled' => (!empty($model['is_duplicate_flag']) || (($model['cost_price'] == 0) && ($model['status'] != 'Active')))
                            ];
                        },
                    ],
                    [
                        'attribute' => 'title',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                    ],
                    [
                        'header' => 'Flag',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'attribute' => 'flag',
                        'filter' => Html::activeDropDownList($searchModel, 'flag', GamePublisherProductForm::FLAG, [
                            'class' => 'form-control c-list btn-select selectpicker m-datatable__pager-size',
                            'prompt' => 'Select'
                        ]),
                        'content' => function ($model) {
                            $flag = [];
                            if ($model['out_of_stock_flag']) {
                                $flag[] .= '<i class="fa fa-cart-arrow-down" title="Out of Stock"></i>';
                            }
                            if ($model['pre_order_flag']) {
                                $flag[] = '<i class="fa fa-calendar-alt" style="color:blue;" title="Pre-Order"></i>';
                            }
                            if ($model['is_duplicate_flag']) {
                                $flag[] = '<i class="fa fa-exclamation-triangle" style="color:red;" title="Duplicate"></i>';
                            }
                            return implode(' ', $flag);
                        }
                    ],
                    [
                        'label' => 'Status',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'filter' => Html::activeDropDownList($searchModel, 'status', GamePublisherProductForm::STATUS,
                            ['class' => 'form-control c-list btn-select selectpicker m-datatable__pager-size']),
                        'attribute' => 'status'
                    ],
                    [
                        'label' => 'Actual Mark Up',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'filter' => Html::activeDropDownList($searchModel, 'actual_mark_up',
                            GamePublisherProductForm::MARK_UP, [
                                'class' => 'form-control c-list btn-select selectpicker m-datatable__pager-size',
                                'prompt' => 'Select'
                            ]),
                        'attribute' => 'actual_mark_up',
                        'content' => function ($model) {
                            return (!empty($model['actual_mark_up']) ? $model['actual_mark_up'] . "%" : '');
                        }
                    ],
                    [
                        'label' => 'Cost',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'content' => function ($model) {
                            return Yii::$app->currency->format($model['cost_currency'], $model['cost_price'], ' ');
                        },
                        'contentOptions' => ['style' => 'text-align:center; white-space: nowrap;']
                    ],
                    [
                        'label' => 'Price',
                        'headerOptions' => ['style' => 'vertical-align: top;'],
                        'content' => function ($model) {
                            $extra = '';
                            if ($model['game_product_id']) {
                                $extra = Yii::$app->currency->format($model['product_currency'],
                                    $model['product_price'], ' ');
                            }
                            return $extra;
                        },
                        'contentOptions' => ['style' => 'text-align:center; white-space: nowrap;']
                    ],
                    [
                        'class' => 'yii\grid\ActionColumn',
                        'template' => mdm\admin\components\Helper::filterActionColumn('{view} {edit-url-alias} {update} {link} {request-log/model-audit-log}'),
                        'contentOptions' => ['style' => 'white-space: nowrap;'],
                        'buttons' => [
                            'view' => function ($url, $model, $key) {
                                return
                                    Html::a('<span class="fa fa-eye"></span>', 'javascript:void(0)', [
                                        'onclick' => 'viewItemDetails("' . Url::to([
                                                'game-publisher-product/view',
                                                'id' => $model['id']
                                            ]) . '")'
                                    ]);
                            },
                            'edit-url-alias' => function ($url, $model, $key) {
                                if (empty($model['game_product_id']) && $model['status'] != "REMOVED") {
                                    return Html::a('<span class="fa fa-pen"></span>'
                                        , 'javascript:void(0)', [
                                            'onclick' => 'editUrlAlias("' . Url::to([
                                                    'game-publisher-product/edit-url-alias',
                                                    'id' => $model['id']
                                                ]) . '")'
                                        ]);
                                }
                            },
                            'update' => function ($url, $model, $key) {
                                $return_str = '';
                                if ($model['game_product_id'] && ($model['cost_price'] > 0 || ($model['cost_price'] == 0 && $model['status'] == "Active")) && $model['status'] != "REMOVED") {
                                    $return_str = Html::a('<span class="fa fa-edit"></span>', \yii\helpers\Url::to([
                                        'game-publisher-product/update',
                                        'id' => $model['game_product_id']
                                    ])
                                    );
                                    if (!empty(Yii::$app->params['frontendDomain']) && $model['game_product_id']) {
                                        $return_str .= Html::a('<span class="fa fa-link"></span>',
                                            Yii::$app->params['frontendDomain'] . '/buynow/' . $model['url_alias'] . '.html',
                                            ['target' => '_blank']
                                        );
                                    }
                                }
                                return $return_str;
                            },
                            'request-log/model-audit-log' => function ($url, $model, $key) {
                                if ($model['status'] != 'Pending') {
                                    return Html::a('<span class="fa fa-history"></span>', \yii\helpers\Url::to([
                                        'request-log/model-audit-log',
                                        'id' => $model['id'],
                                        'table' => 'game_publisher_product'
                                    ])
                                    );
                                }
                            },
                        ],
                        'contentOptions' => ['style' => 'text-align:center; white-space: nowrap;']
                    ],
                ],
            ]);

            echo $searchModel->renderPageSizer();

            Modal::begin([
                'title' => '',
                'id' => 'generalModal',
                'size' => Modal::SIZE_LARGE
            ]);

            Modal::end();
            ?>
        </div>
    </div>
</div>

<?php
$this->registerJs(<<<SCRIPT
SCRIPT
    , \yii\web\View::POS_END);

$this->registerJs(<<<JS
 $(document).ready(function () {
    $('button[data-target="#batchUpdateModal"]').on('click', function(e) {
        if(!jQuery('input[name="selection[]"]:checked').length){
            Swal.fire(
                '',
                'No Item Selected!',
                'warning'
            );
            e.stopPropagation(); 
        }
    }) ;
 });
JS
    , \yii\web\View::POS_READY)
?>


<script>
    function getUrlParams() {
        var params = {}

        if (location.search) {
            var parts = location.search.substring(1).split('&')

            for (var i = 0; i < parts.length; i++) {
                var nv = parts[i].split('=')
                if (!nv[0]) {
                    continue
                }
                params[decodeURIComponent(nv[0])] = nv[1] || true
            }
        }

        return params
    }

    function batchUpdateStatusChange(e) {
        var val = $(e).val()
        if (val !== '5') {
            $('#batchUpdateModal .hidden-toggle').removeClass('d-none')
        } else {
            $('#batchUpdateModal .hidden-toggle').addClass('d-none')
        }
    }

    function batchUpdate() {
        var checked_item = []
        var warning = []

        jQuery('input[name="selection[]"]:checked').each(function (i, e) {
            checked_item.push(jQuery(e).val())
        })

        var status = jQuery('select[name="status"]').val()

        if (status == '5') {
            var data = {
                'status': jQuery('select[name="status"]').val(),
                'items': checked_item,
            }
        } else {
            var data = {
                'product_type': jQuery('select[name="product_type"]').val(),
                'status': jQuery('select[name="status"]').val(),
                'cost_company': jQuery('select[name="cost_company"]').val(),
                'cost_company_name': jQuery('select[name="cost_company"] option:selected').text(),
                'product_image_url': jQuery('input[name="product_image_url"]').val(),
                'background_image_url': jQuery('input[name="background_image_url"]').val(),
                'header_image_url': jQuery('input[name="header_image_url"]').val(),
                'description': jQuery('textarea[name="description"]').val(),
                'game_publisher_id': getUrlParams()['GamePublisherProductForm[game_publisher_id]'],
                'items': checked_item,
            }

            if ($('input[name=\'mark_up\']').length > 0) {
                if (isNaN($('input[name=\'mark_up\']').val()) || $('input[name=\'mark_up\']').val() < 0) {
                    warning.push('Mark up must be positive number')
                }
                data['mark_up'] = jQuery('input[name="mark_up"]').val()
            }

            if ($('input[name=\'category_id\']').length > 0 && !$('input[name=\'category_id\']').is(':hidden')) {
                data['category_id'] = jQuery('input[name="category_id"]').val()
                if ($('input[name=\'category_id\']').val() == '') {
                    warning.push('Category Id is required')
                }
            }

            if (warning.length > 0) {
                Swal.fire(
                    '',
                    warning.join('<br>'),
                    'warning',
                )
                $('#batchUpdateModal').modal('hide')
                return
            }
        }
        mApp.blockPage()
        $('#batchUpdateModal').modal('hide')
        $.ajax({
            method: 'POST',
            url: '/game-publisher-product/batch-update',
            data: data,
        }).done(function (data) {
            mApp.unblockPage()
            Swal.fire(
                '',
                'Update Success!',
                'success',
            ).then((result) => {
                if (result.value) {
                    location.reload()
                }
            })
        }).fail(function (jqXHR, textStatus, errorThrown) {
            mApp.unblockPage()
            Swal.fire(
                '',
                errorThrown,
                'error',
            )
        })
    }

    function viewItemDetails(url) {
        $.ajax({
            method: 'GET',
            url: url,
            dataType: 'json',
        }).done(function (data) {
            jQuery('#generalModal .modal-body').html(data['body'])
            jQuery('#generalModal .modal-title').text(data['title'])
            var data = $('.prettyprint').text()
            if (IsJsonString(data)) {
                $('.prettyprint').text(JSON.stringify(JSON.parse($('.prettyprint').text()), null, 4))
            }
            jQuery('#generalModal').modal('show')
        })
    }

    function updateTitleUrlAlias() {
        var data = {
            id: $('input[name="id"]').val(),
            title: $('input[name="title"]').val(),
            url_alias: $('input[name="url_alias"]').val(),
        }
        mApp.blockPage()
        ajaxCall('POST', '/game-publisher-product/update-title-url-alias', data, function (data) {
            mApp.unblockPage()
            $('#generalModal').modal('hide')
            if (data.success) {
                location.reload()
            } else {
                Swal.fire(
                    '',
                    data.message,
                    'error',
                )
            }
        })
    }

    function editUrlAlias(url) {
        $.ajax({
            method: 'GET',
            url: url,
            dataType: 'json',
        }).done(function (data) {
            jQuery('#generalModal .modal-body').html(data['body'])
            jQuery('#generalModal .modal-title').text(data['title'])
            jQuery('#generalModal').modal('show')
        })
    }

    function ajaxCall(method, url, data, success) {
        $.ajax({
            method: method,
            url: url,
            dataType: 'json',
            data: data,
        }).done(function (data) {
            success(data)
        }).error(function (jqXHR, textStatus, errorThrown) {
            Swal.fire(
                '',
                errorThrown + textStatus,
                'error',
            )
        })
    }

    function IsJsonString(str) {
        try {
            JSON.parse(str)
        } catch (e) {
            return false
        }
        return true
    }
</script>
