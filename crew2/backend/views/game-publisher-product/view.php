<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

?>
<div class="game-publisher-product-base-view">

    <?= DetailView::widget([
        'model' => $data,
        'attributes' => [
            'id',
            'title',
            'game_publisher_id',
            'publisher_reference_id',
            'products_id',
            'game_product_id',
            'status',
            'mark_up',
            'actual_mark_up',
            'cost_price',
            'cost_currency',
            'product_price',
            'product_currency',
            'created_at',
            'updated_at',
            [
                'label' => 'Raw',
                'format' => 'html',
                'value' => "<pre class='prettyprint'>".$data['json_raw']."</pre>",
                'options' => [
                    'style'=>'max-width:150px; min-height:100px; overflow: auto; word-wrap: break-word;'
                ],
            ],
        ],
    ]) ?>

</div>

<?php
\backend\assets\CodePrettifyAsset::register($this);
$this->registerJs('
    var data = $(".prettyprint").text();
    if(IsJsonString(data)){
      $(".prettyprint").text(JSON.stringify(JSON.parse($(".prettyprint").text()), null, 4));
    }
  ');
$this->registerJs('
  function IsJsonString(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
  }
',\yii\web\view::POS_END);

?>