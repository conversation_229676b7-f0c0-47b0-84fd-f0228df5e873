<?php

use kartik\widgets\SwitchInput;
use yii\helpers\Html;

foreach ($configs as $config) {
    ?>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label"><?= HTML::label($config['label']); ?></label>
        <div class="col-lg-10">
            <?php
            switch ($config['type']) {
                case 'text':
                    echo Html::textInput('SETTING[' . $config['key'] . ']', ($data[$config['key']] ?? ''), $config['option']);
                    break;

                case 'select':
                    echo Html::dropDownList('SETTING[' . $config['key'] . ']', ($data[$config['key']] ?? ''), $config['items'], $config['option']);
                    break;
            }
            ?>
        </div>
    </div>

    <?php
}