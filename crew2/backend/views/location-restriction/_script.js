$('i.glyphicon-refresh-animate').hide();

$(document).ready(function(){
    var countries = $('select.c-list').val();
    if(countries == ''){
        $('#submit-btn').prop('disabled', true);
    } else {
        $('#submit-btn').prop('disabled', false);
    }
    let selectList = $('#allowed-currencies-dropdown');

    selectList.bootstrapDualListbox({
        moveOnSelect: false,
        selectedListLabel: "Restricted",
        nonSelectedListLabel: "Allow"
    });

    let updateCurrencies = (currencies) => {
        let allCurrencies = $.extend({}, currencies.available, currencies.assigned);
        let assignedIndex = $.map(currencies.assigned, (value, index) => {
            return index;
        });
        let options = new Array();
        $.each(allCurrencies, function(index, value) {
            let selected = '';
            if ($.inArray(index, assignedIndex) >= 0) {
                selected = 'selected';
            }
            options.push('<option value="' + index + '" ' + selected + '>'+ value + '</option>');
        });
        selectList.html(options.join("\n"));
        selectList.bootstrapDualListbox('refresh', true);
    }

    let populateCountryCurrencies = () => {
        var countries = $('select.c-list').val();
        $.post('refresh', { c_code: countries }, function (currencies) {
            updateCurrencies(currencies);
        }).fail(function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: xhr.responseText,
            })
        });
        return false;
    };


    $(document).on('change', '#country', function(event) {
        populateCountryCurrencies();
        var countries = $('select.c-list').val();
        if(countries == ''){
            $('#submit-btn').prop('disabled', true);
        } else {
            $('#submit-btn').prop('disabled', false);
        }
    });
    $('#country').trigger('change');
});