<?php

use yii\helpers\Url;
use yii\helpers\Json;
use yii\helpers\Html;
use yii\grid\GridView;

$this->title = 'Location Restriction';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = 'Location Restriction';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('location-restriction/setting'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <!--begin: Datatable -->
            <div class="m-section__content">
                <?=
                GridView::widget([
                    'dataProvider' => $dataProvider,
                    'layout' => "{summary}\n{items}",
                    'columns' => [
                        ['class' => 'yii\grid\SerialColumn'],
                        [
                            'header' => 'Location',
                            'content' => function ($model) use ($f_ctry) {
                                return (isset($f_ctry[$model["country_iso_code2"]]) ? $f_ctry[$model["country_iso_code2"]] : $model["country_iso_code2"]);
                            },
                            'contentOptions' => ['style' => "width: 15%;"]
                        ],
                        [
                            'header' => 'Allowed Currency',
                            'content' => function ($model) use ($f_cur) {
                                $info = Json::decode($model["restriction_info"]);
                                return implode(", ", array_diff_key($f_cur, array_flip($info)));
                            },
                            'contentOptions' => ['style' => "width: 20%;"]
                        ],
                        [
                            'header' => 'Restricted Currency',
                            'content' => function ($model) {
                                $info = Json::decode($model["restriction_info"]);
                                return implode(", ", $info);
                            },
                            'contentOptions' => ['style' => "width: 20%;"]
                        ],
                        [
                            'header' => 'Date Created',
                            'attribute' => 'created_at',
                            'format' => 'datetime',
                            'contentOptions' => ['style' => "width: 15%;"]
                        ],
                        [
                            'header' => 'Last Modified',
                            'attribute' => 'updated_at',
                            'format' => 'datetime',
                            'contentOptions' => ['style' => "width: 15%;"]
                        ],
                        [
                            'header' => 'Action',
                            'class' => 'yii\grid\ActionColumn',
                            'template' => mdm\admin\components\Helper::filterActionColumn('{setting} {delete}'),
                            'buttons' => ['setting' => function ($url, $model, $key) {
                                    return Html::a('<span class="fa fa-edit"></span>', Url::to(['location-restriction/setting','id' => $model['id']]), ['title' => 'Edit']
                                    );
                                }
                            ],
                            'contentOptions' => ['style' => 'white-space: nowrap;'],
                        ],
                    ]
                ]);
                ?>
                <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
            </div>
        </div>
    </div>
</div>