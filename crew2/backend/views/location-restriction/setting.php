<?php

use mdm\admin\AnimateAsset;
use yii\widgets\ActiveForm;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\helpers\Json;
use yii\web\YiiAsset;
use backend\assets\DualListBoxAsset;
DualListBoxAsset::register($this);

$this->title = 'Location Restriction';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = ['label' => $this->title, 'url' => ['location-restriction/index']];
$this->params['layoutContent'] = 'block';

AnimateAsset::register($this);
YiiAsset::register($this);
$opts = Json::htmlEncode([
            'currencies' => ($model ? Json::decode($model->restriction_info) : []),
        ]);

$this->registerJs("var _opts = {$opts};");

$this->registerJs($this->render('_script.js'));
$animateIcon = ' <i class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></i>';
$form = ActiveForm::begin([
    'method' => 'post',
]);

?>
<div class="m-portlet m-portlet--mobile m-form">
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label"><?= Yii::t('location-restriction', 'Location'); ?></label>
            <div class="col-lg-4">
                <?php echo Html::dropDownList('c_code', $model->country_iso_code2, $countries, ['class' => 'form-control c-list btn-select selectpicker m-datatable__pager-size', 'id' => 'country', 'prompt' => 'Select', 'disabled' => !$model->isNewRecord]); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label"><?= Yii::t('location-restriction', 'Currency'); ?></label>
            <div class="col-sm-10">
                <select multiple size="14" class="form-control list" name="currencies[]" id="allowed-currencies-dropdown" data-target="available"></select>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-12 m--align-right">
                    <button type="submit" id="submit-btn" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>