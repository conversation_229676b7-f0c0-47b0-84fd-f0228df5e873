<?php

use common\models\MobileRechargeOperatorForm;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$language = Yii::$app->enum->getLanguage('listData');
$operator_list = (new MobileRechargeOperatorForm)->getOperatorList();
$currency_list = Yii::$app->currency->getCurrencyList();

$form = ActiveForm::begin(['options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
        ]]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Type</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'type')->dropDownList([1 => 'Recharge', 2 => 'Bundle', 3 => 'Pin'], ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'status')->dropDownList(['0' => 'Inactive', '1' => 'Active', '2' => 'Disable'], ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
    </div>

    <?php $publisher_list = \yii\helpers\ArrayHelper::map(\common\models\Publisher::find()->select(['title', 'publisher_id'])->all(), 'publisher_id', 'title'); ?>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Publisher</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'publisher_id')->dropDownList($publisher_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Operator</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'operator_id')->dropDownList($operator_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Cost Currency</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'cost_currency')->dropDownList($currency_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Cost Price</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'cost_price')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Mark Up</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'mark_up')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Sales Currency</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'sales_currency')->dropDownList($currency_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Sales Price</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'sales_price')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Publisher Ref ID</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'publisher_ref_id')->textInput(['maxlength' => true])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Product ID</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'products_id')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <!--begin::Portlet-->
    <div class="m-portlet__body">
        <div class="m-portlet m-portlet--tabs">
            <div class="m-portlet__head">
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                        <?php foreach ($language as $i => $v) { ?>
                            <li class="nav-item m-tabs__item">
                                <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                    <?= $v; ?>
                                </a>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="tab-content">
                    <?php
                    foreach ($language as $i => $v) {
                        $description_model = new \common\models\MobileRechargeDenoDescription();
                        if (isset($model['description'][$i])) {
                            $description_model->load($model['description'][$i], '');
                        }
                        $description_model->language_id = $i;
                        ?>
                        <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                            <div class = "form-group m-form__group row">
                                <label class = "col-lg-3 col-form-label">Title</label>
                                <div class = "col-lg-9">
                                    <?= $form->field($description_model, '[' . $i . ']title')->textInput()->label(false); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Description</label>
                                <div class="col-lg-9">
                                    <?= $form->field($description_model, '[' . $i . ']description')->textarea(['rows' => 5, 'class' => 'summernote'])->label(false); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('mobile-recharge/deno-list'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<style>
    .note-toolbar {
        z-index: 50;
    }
</style>

<?php
$this->registerJs(<<<JS
var SummernoteDemo={init:function(){
  $('.summernote').summernote({
  height:150,
  toolbar: [
    ['style', ['bold', 'italic', 'underline', 'clear']],
    ['font', ['strikethrough', 'superscript', 'subscript']],
    ['fontsize', ['fontsize']],
    ['color', ['color']],
    ['para', ['ul', 'ol', 'paragraph']],
    ['codeview']
  ]
  })}};

SummernoteDemo.init();
JS
        , \yii\web\View::POS_READY);
