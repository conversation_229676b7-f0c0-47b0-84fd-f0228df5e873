<?php

use yii\bootstrap4\Modal;
use common\models\GamePublisherProductForm;

Modal::begin([
    'id' => 'batchUpdateModal',
    'title' => 'Batch Update Product',
    'toggleButton' => [
        'label' => '<i class="la la-pencil-square"></i> Batch Update',
        'class' => 'btn btn-primary m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill m--align-right',
        'style' => 'margin-left: 5px;'
    ],
]);

$default_class = ['class' => 'form-control'];
$hidden_class = ['type' => 'div', 'option' => ['class' => 'form-group hidden-toggle']];

$status_list = [0 => 'Inactive', 1 => 'Active'];
$field_list = array(
    [
        'label' => 'Status',
        'name' => 'status',
        'type' => 'select',
        'items' => $status_list,
        'option' => ['class' => $default_class, 'prompt' => '']
    ],
    [
        'label' => 'Products ID',
        'name' => 'products_id',
        'type' => 'text',
        'option' => $default_class
    ],
    [
        'label' => 'Mark Up (%)',
        'name' => 'mark_up',
        'type' => 'text',
        'option' => $default_class
    ],
    [
        'no_wrapper' => true,
        'type' => 'button',
        'name' => 'Submit',
        'option' => ['class' => 'btn btn-success m--align-right', 'onclick' => 'batchUpdate();']
    ]
);
?>

<form id="batchUpdateForm">
    <?php
    echo \common\models\MobileRechargeDenoForm::renderCustomForm($field_list);
    ?>
</form>
<?php
Modal::end();
