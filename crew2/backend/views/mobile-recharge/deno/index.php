<?php

use yii\bootstrap4\Modal;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

$this->title = 'Mobile Recharge';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Mobile Recharge';
$this->params['breadcrumbs'][] = 'Deno';
$this->params['layoutContent'] = 'block';

$status_list = ['0' => 'Inactive', '1' => 'Active', '2' => 'Disable'];
$type_list = [1 => 'Recharge', 2 => 'Bundle', 3 => 'Pin'];
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('mobile-recharge/create-deno'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
                <?= Yii::$app->controller->renderPartial('deno/batch-update', ['is_new' => true]) ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">

        <?php
        echo GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'filterUrl' => $searchModel->filterUrl,
            'summary' => 'Showing <b>' . ($searchModel->startAt) . '-' . ($searchModel->endAt) . '</b> of <b>' . $searchModel->totalCount . '</b> items.',
            'columns' => [
                [
                    'header' => '#',
                    'content' => function ($model, $key, $index) use ($searchModel) {
                        return $index + $searchModel->startAt;
                    },
                ],
                [
                    'class' => 'yii\grid\CheckboxColumn',
                    'checkboxOptions' => function ($model) use ($searchModel) {
                        return ['value' => $model['id']];
                    },
                ],
                'operator',
                'title',
                [
                    'label' => 'Type',
                    'filter' => Html::activeDropDownList($searchModel, 'type', $type_list, ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) use ($type_list) {
                        return $type_list[$model['type']];
                    }
                ],
                [
                    'label' => 'Status',
                    'filter' => Html::activeDropDownList($searchModel, 'status', $status_list, ['prompt' => '', 'class' => 'form-control']),
                    'content' => function ($model) use ($status_list) {
                        return $status_list[$model['status']];
                    }
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{mobile-recharge/update-deno}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'mobile-recharge/update-deno' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-edit"></span>', Url::to([
                                                'mobile-recharge/update-deno',
                                                'id' => $model['id']
                                            ])
                            );
                        }
                    ]
                ],
            ],
        ]);

        echo $searchModel->renderPageSizer();
        ?>
    </div>
</div>

<?php
$this->registerJs(<<<JS
    $('button[data-target="#batchUpdateModal"]').on('click', function(e) {
        if(!jQuery('input[name="selection[]"]:checked').length){
            Swal.fire(
                '',
                'No Item Selected!',
                'warning'
            );
            e.stopPropagation(); 
        }
    }) ;
JS
        , \yii\web\View::POS_READY);
?>

<script>
    function batchUpdate() {
        var checked_item = []
        var warning = []

        jQuery('input[name="selection[]"]:checked').each(function (i, e) {
            checked_item.push(jQuery(e).val())
        })

        var data = {
            'status': jQuery('select[name="status"]').val(),
            'products_id': jQuery('input[name="products_id"]').val(),
            'mark_up': jQuery('input[name="mark_up"]').val(),
            'items': checked_item,
        }

        mApp.blockPage()
        $('#batchUpdateModal').modal('hide')
        $.ajax({
            method: 'POST',
            url: '/mobile-recharge/batch-update-deno',
            data: data,
        }).done(function (data) {
            mApp.unblockPage()
            Swal.fire(
                    '',
                    'Update Success!',
                    'success',
                    ).then((result) => {
                if (result.value) {
                    location.reload()
                }
            })
        }).fail(function (jqXHR, textStatus, errorThrown) {
            mApp.unblockPage()
            Swal.fire(
                    '',
                    errorThrown,
                    'error',
                    )
        })
    }
</script>