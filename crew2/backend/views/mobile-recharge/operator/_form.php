<?php

use common\models\MobileRechargeRegionForm;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

\backend\assets\UppyAssets::register($this);
$max_size = (int) (str_replace('M', '', ini_get('post_max_size')) * 1024 * 1024);

$language = Yii::$app->enum->getLanguage('listData');
$operator_list = $model->getOperatorList();
$region_list = (new MobileRechargeRegionForm)->getRegionList();

$form = ActiveForm::begin(['options' => [
                'enctype' => 'multipart/form-data',
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
        ]]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'status')->dropDownList(['0' => 'Inactive', '1' => 'Active', '2' => 'Disable'], ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Region</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'region_id')->dropDownList($region_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Parent Operator</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'parent_operator_id')->dropDownList($operator_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Logo Path</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'logo_path')->textInput(['id' => 'logo_path'])->label(false); ?>
            <div class="UppyForm"></div>
            <img id="operator-img" width="200px" src="<?= $model->logo_path ?>">
        </div>
    </div>

    <!--begin::Portlet-->
    <div class="m-portlet__body">
        <div class="m-portlet m-portlet--tabs">
            <div class="m-portlet__head">
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                        <?php foreach ($language as $i => $v) { ?>
                            <li class="nav-item m-tabs__item">
                                <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                    <?= $v; ?>
                                </a>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="tab-content">
                    <?php
                    foreach ($language as $i => $v) {
                        $description_model = new \common\models\MobileRechargeOperatorDescription();
                        if (isset($model['description'][$i])) {
                            $description_model->load($model['description'][$i], '');
                        }
                        $description_model->language_id = $i;
                        ?>
                        <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                            <div class = "form-group m-form__group row">
                                <label class = "col-lg-3 col-form-label">Title</label>
                                <div class = "col-lg-9">
                                    <?= $form->field($description_model, '[' . $i . ']title')->textInput()->label(false); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Description</label>
                                <div class="col-lg-9">
                                    <?= $form->field($description_model, '[' . $i . ']description')->textarea(['rows' => 5])->label(false); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-3 col-form-label">Term</label>
                                <div class="col-lg-9">
                                    <?= $form->field($description_model, '[' . $i . ']terms')->textarea(['rows' => 5])->label(false); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    <!--end::Portlet-->
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('mobile-recharge/operator-list'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<?php
$this->registerJs(<<<SCRIPT
  const uppy = new Uppy.Core({
    autoProceed: true,
    restrictions: {
      maxFileSize: $max_size,
      allowedFileTypes: ['image/*'],
    },
    onBeforeUpload: (files) => {
      mApp.blockPage()
      uppy.setMeta({'_csrf-backend': $('input[name=\'_csrf-backend\']').val()})
    },
  })

  uppy.use(Uppy.FileInput, {
    target: '.UppyForm',
    replaceTargetContent: true,
  })

  uppy.use(Uppy.XHRUpload, {
    endpoint: '/mobile-recharge/upload-operator-image?id=$model->operator_id',
    formData: true,
    fieldName: 'FileUploadForm[imageFile]',
  })

  // And display uploaded files
  uppy.on('upload-success', (file, response) => {
    mApp.unblockPage()
    const data = JSON.parse(response.body)
    const url = data.uploadURL
    $('#operator-img').attr('src', url)
    $('#logo_path').val(url);
  })
SCRIPT
        , \yii\web\View::POS_READY);
?>