<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

$this->title = 'Mobile Recharge';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Mobile Recharge';
$this->params['breadcrumbs'][] = 'Operator';
$this->params['layoutContent'] = 'block';

$status_list = ['0' => 'Inactive', '1' => 'Active', '2' => 'Disable'];
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('mobile-recharge/create-operator'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'filterUrl' => $searchModel->filterUrl,
            'summary' => 'Showing <b>' . ($searchModel->startAt) . '-' . ($searchModel->endAt) . '</b> of <b>' . $searchModel->totalCount . '</b> items.',
            'columns' => [
                [
                    'header' => '#',
                    'content' => function ($model, $key, $index) use ($searchModel) {
                        return $index + $searchModel->startAt;
                    },
                ],
                'title',
                [
                    'label' => 'Status',
                    'filter' => Html::activeDropDownList($searchModel, 'status', $status_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']),
                    'content' => function ($model) use ($status_list) {
                        return $status_list[$model['status']];
                    }
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{mobile-recharge/update-operator} {mobile-recharge/deno-list}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'mobile-recharge/update-operator' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-pen"></span>', Url::to([
                                                'mobile-recharge/update-operator',
                                                'id' => $model['id']
                                            ])
                            );
                        },
                        'mobile-recharge/deno-list' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-list"></span>', Url::to([
                                                'mobile-recharge/deno-list',
                                                'MobileRechargeDenoForm[operator_id]' => $model['id']
                                            ])
                            );
                        }
                    ],
                ],
            ]
        ]);
        ?>

        <?= $searchModel->renderPageSizer(); ?>
    </div>
</div>