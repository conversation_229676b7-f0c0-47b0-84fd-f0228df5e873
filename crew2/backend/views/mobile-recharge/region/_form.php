<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;
?>

<?php
$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
            ],
        ]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Name</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'name')->textInput(['maxlength' => true])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Flag</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'flag')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">ISO3</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'iso3')->textInput(['maxlength' => true])->label(false); ?>
        </div>
        <label class="col-lg-2 col-form-label">Dial Code</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'prefix')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-4">
            <?=
            $form->field($model, 'status')->widget(SwitchInput::classname(), [
                'value' => (isset($model->status) && $model->status == 1) ? true : false,
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ]
            ])->label(false);
            ?>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('mobile-recharge/region-list'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>