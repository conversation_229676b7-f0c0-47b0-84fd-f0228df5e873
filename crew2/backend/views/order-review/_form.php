<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\StarRating;
use kartik\widgets\SwitchInput;

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-left',
            ],
        ]);

if (isset($model->status) && ($model->status == '2' || $model->status == '4')) {
    $model->status = false;
} else {
    $model->status = true;
}

if (isset($model->visibility) && $model->visibility == '1') {
    $model->visibility = true;
} else {
    $model->visibility = false;
}

$createdAt = date("Y-m-d H:i:s", $model['created_at']);
?>
<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= 'Customer Review'; ?>
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Order ID</label>
            <div class="col-lg-4"><a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/orders.php?oID=<?= $model->orders_id ?>&action=edit" target="_blank"><?= $model->orders_id ?></a></div>
            <label class="col-lg-2">Customer ID</label>
            <div class="col-lg-4"><a href="<?= Yii::$app->params['oldcrew.baseUrl'] ?>/customers.php?cID=<?= $model->created_by ?>&action=edit" target="_blank"><?= $model->created_by ?></a></div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Product Name</label>
            <div class="col-lg-10"><?= $model->products_name; ?></div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Review Score</label>
            <div class="col-lg-4"><?=
                StarRating::widget([
                    'name' => 'ReviewScore',
                    'value' => $model->review_score,
                    'pluginOptions' => ['displayOnly' => true, 'showCaption' => false, 'size' => 's',]
                ]);
                ?></div>
            <label class="col-lg-2">Review Date</label>
            <div class="col-lg-4"><?= $createdAt; ?></div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Comment</label>
            <div class="col-lg-10"><?= Html::encode($model->comment); ?></div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2">
                <label class="col-form-label">Enable Edit?</label>
                <i data-toggle="m-tooltip" class="m-form__heading-help-icon flaticon-info"
                   title="If enabled, customer will be able to edit their review."></i>
            </div>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'status')->widget(SwitchInput::classname(), [
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
            <div class="col-lg-2">
                <label class="col-form-label" style="padding-left:0px;">Show Review</label>
                <i data-toggle="m-tooltip" class="m-form__heading-help-icon flaticon-info"
                   title="If enabled, this Review Score will be counted and comment displayed in Store."></i>
            </div>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'visibility')->widget(SwitchInput::classname(), [
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <!--end::Portlet-->
    </div>
</div>
<div class="m-portlet m-portlet--mobile">
    <?= $this->render('_form_remarks', ['data' => $data['Remarks']]); ?>
    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary"
                            onclick="document.location = '<?= Url::toRoute('order-review/index'); ?>'">Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>