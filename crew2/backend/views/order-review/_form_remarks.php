<?php

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\widgets\SwitchInput;
?>
<div class="m-portlet__head">
    <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
            <h3 class="m-portlet__head-text">
                <?= 'Remarks'; ?>
            </h3>
        </div>
    </div>
</div>
<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label remarks">Remarks</label>
    <div class="col-lg-10">
        <?=
        Html::textArea('OrderReviewRemarks[remarks]', '', [
            'id' => 'OrderReviewRemarks',
            'rows' => 5,
            'class' => 'form-control'
        ]);
        ?>
        <?=
        Html::textInput('OrderReviewRemarks[remarks_id]', 0, [
            'id' => 'RemarksId',
            'type' => 'hidden',
        ]);
        ?>
    </div>
</div>
<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label remarks">Show Customer</label>
    <div class="col-lg-4">
        <?=
        SwitchInput::widget([
            'name' => 'OrderReviewRemarks[remarks_status]',
            'pluginOptions' => [
                'onText' => '<i class="fa fa-check"></i>',
                'offText' => '<i class="fa fa-times"></i>',
            ],
            'value' => 0,
        ])
        ?>
    </div>
</div>
<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label"></label>
    <div class="col-lg-10">
        <div class="m-portlet m-portlet--accent m-portlet--collapsed m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <span class="m-portlet__head-icon">
                            <i class="flaticon-time"></i>
                        </span>
                        <span class="m-portlet__head-text">
                            Remarks History
                        </span>
                    </div>
                </div>
                <div class="m-portlet__head-tools">
                    <ul class="m-portlet__nav">
                        <li class="m-portlet__nav-item">
                            <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body">
                <div class="m-scrollable" data-scrollable="true" data-height="300" data-mobile-height="300">
                    <?php foreach ($data as $idx => $remark) { ?>
                        <div class="m-timeline-2">
                            <div class="m-timeline-2__items  m--padding-top-25">
                                <div class="m-timeline-2__item">
                                    <div class="m-timeline-2__item-cricle">
                                        <i class="fa fa-genderless m--font-warning"></i>
                                    </div>
                                    <div class="m-timeline-2__item-text  m--padding-top-5">
                                        <b><?= date('Y-m-d H:i:s', $remark['updated_at']); ?></b><br />
                                        <?= $remark['updated_by']; ?><br />
                                        Remarks : <?= Html::encode($remark['remarks']); ?><br />
                                        Show Customer: <a href="#" class="remark_trigger" data-value="<?= $remark['status']; ?>" data-idx="<?php echo $idx; ?>"><?php echo $remark['status'] == 2 ? 'Hide' : 'Show'; ?></a><br />
                                        <input type="hidden" value="<?= $remark['status']; ?>" name="OrderReviewRemarksHist['<?php echo $idx; ?>'][remarks_status]" id="remarks_status_<?php echo $idx; ?>">
                                        <input type="hidden" value="<?= $remark['remark_id']; ?>" name="OrderReviewRemarksHist['<?php echo $idx; ?>'][remarks_id]" id="remarks_id_<?php echo $idx; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>