$(function () {
    $(document).on('click', '.remark_trigger', function (event) {
        event.preventDefault();
        let remarkStatus = $(this).parent().find('input:hidden:first');
        let triggerEl = $(this);
        let idx = $(this).data('idx');
        let showHide = 1;
        let showHideText = 'Show';
        if (triggerEl.data('value') == 1) {
            showHide = 2;
            showHideText = 'Hide';
        }
        triggerEl.data('value', showHide);
        remarkStatus.val(showHide);
        triggerEl.html(showHideText);
    });
});