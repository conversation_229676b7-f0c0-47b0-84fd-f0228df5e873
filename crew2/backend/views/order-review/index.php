<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use common\models\OrderReviewForm;
use yii\bootstrap4\Modal;
use kartik\widgets\StarRating;
use kartik\daterange\DateRangePicker;

$this->title = 'Order Review';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = 'Rating';
$this->params['breadcrumbs'][] = 'Order Review';
$this->params['layoutContent'] = 'block';

$filter_date = (isset($filter->date_fr) && isset($filter->date_to) ? date('Y-m-d H:i:s', $filter->date_fr) . '/' . date('Y-m-d H:i:s', $filter->date_to) : null);
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__body">
        <?=
        \yii\grid\GridView::widget([
            'dataProvider' => $provider,
            'filterModel' => $filter,
            'filterUrl' => $filter->filterUrl,
            'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
            'columns' => [
                [
                    'header' => '#',
                    'content' => function ($model, $key, $index) use ($filter) {
                        return $index + $filter->startAt;
                    },
                ],
                [
                    'attribute' => 'orders_id',
                    'header' => 'Order ID',
                    'filter' => Html::activeTextInput($filter, 'orders_id', ['class' => 'form-control']),
                    'content' => function ($model, $key, $index) use ($provider) {
                        $html = '<a href="' . Yii::$app->params['oldcrew.baseUrl'] . '/orders.php?oID=' . $model['orders_id'] . '&action=edit" target="_blank">' . $model['orders_id'] . '</a>';
                        return $html;
                    },

                ],
                [
                    'attribute' => 'products_name',
                    'header' => 'Products Name',
                    'headerOptions' => ['style' => 'white-space: nowrap; max-width: 10%'],
                    'filter' => Html::activeTextInput($filter, 'products_name', ['class' => 'form-control']),
                ],
                [
                    'attribute' => 'review_score',
                    'content' => function ($model, $key, $index) use ($provider) {
                        return StarRating::widget([
                            'name' => 'review_score',
                            'value' => $model['review_score'],
                            'pluginOptions' => ['displayOnly' => true, 'showCaption' => false, 'size' => 'sm',]
                        ]);
                    },
                    'filter' => Html::activeDropDownList($filter, 'review_score', OrderReviewForm::RATING_SCORE, ['class' => 'form-control c-list btn-select selectpicker m-datatable__pager-size']),
                    'headerOptions' => ['style' => 'max-width:10%'],
                ],
                [
                    'attribute' => 'comment',
                    'filter' => Html::activeTextInput($filter, 'comment', ['class' => 'form-control']),
                    'contentOptions' => ['style' => 'word-break: break-word;'],
                ],
                [
                    'attribute' => 'created_at',
                    'header' => 'Date',
                    'filter' => DateRangePicker::widget([
                        'model' => $filter,
                        'attribute' => 'created_at',
                        'convertFormat' => true,
                        'presetDropdown' => true,
                        'pluginOptions' => [
                            'timePicker' => true,
                            'timePickerIncrement' => 1,
                            'locale' => [
                                'separator' => '/',
                                'format' => 'Y-m-d H:i:s'
                            ]
                        ]
                    ]),
                    'headerOptions' => ['style' => 'width: 12%'],
                    'contentOptions' => ['style' => 'word-break: break-word;'],
                ],
                [
                    'header' => 'Last Update',
                    'headerOptions' => ['style' => 'width: 12%'],
                    'content' => function ($model, $key, $index) use ($provider) {
                        return $model['updated_by'];
                    },
                    'contentOptions' => ['style' => 'word-break: break-word;'],
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{update}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'urlCreator' => function ($action, $model, $key, $index) {

                        if ($action == "update") {
                            return Url::to(['update', 'review_id' => $key]);

                        }
                    },
                ],
            ],
        ]);


        echo $filter->renderPageSizer();

        Modal::begin([
            'title' => 'Test',
            'id' => 'generalModal',
            'size' => Modal::SIZE_LARGE
        ]);

        Modal::end();
        ?>
    </div>
</div>