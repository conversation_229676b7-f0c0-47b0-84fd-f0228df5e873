<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
?>

<?php
$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--label-align-right m-form--state-',
            ]
        ]);
?>

<div class="m-portlet__body">
    <div class="m-form__section">
        <div class="m-form__heading">
            <h3 class="m-form__heading-title">Pipwave</h3>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Payment Code</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'pipwave_payment_code')->textInput(['maxlength' => true])->label(false); ?>
            </div>
        </div>
    </div>

    <div class="m-separator m-separator--dashed m-separator--lg"></div>

    <div class="m-form__section">
        <div class="m-form__heading">
            <h3 class="m-form__heading-title">OffGamers Payment Method</h3>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Payment Gateway</label>
            <div class="col-lg-4">
                <?php
                $row = ArrayHelper::map(\common\models\PaymentMethods::find()
                                        ->where(["payment_methods_receive_status" => 1, "payment_methods_parent_id" => 0])
                                        ->andWhere(["not", ["payment_methods_title" => null]])
                                        ->asArray()
                                        ->all(), "payment_methods_id", "payment_methods_title");
                echo $form->field($model, 'pg_id')->dropDownList($row, [
                    'id' => '__pg_id',
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size',
                    'onchange' => '
                        $.get("' . Url::toRoute('/pipwave-payment-mapper/payment-method') . '", { id: $(this).val() } )
                            .done(function(data) {
                                $("#__pm_id").html(data);
                            }
                        );
                    '
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Display Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'pg_display_name')->textInput(['maxlength' => true])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Payment Method</label>
            <div class="col-lg-4">
                <?php
                $row = ArrayHelper::map(\common\models\PaymentMethods::find()
                                        ->where(["payment_methods_receive_status" => 1, "payment_methods_parent_id" => $model->pg_id])
                                        ->andWhere(["not", ["payment_methods_title" => null]])
                                        ->asArray()
                                        ->all(), "payment_methods_id", "payment_methods_title");

                echo $form->field($model, 'pm_id')->dropDownList($row, [
                    'id' => '__pm_id',
                    'prompt' => 'Select',
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Display Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'pm_display_name')->textInput(['maxlength' => true])->label(false); ?>
            </div>
        </div>
    </div>

    <div class="m-separator m-separator--dashed m-separator--lg"></div>

    <div class="m-form__section">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Payment Type</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'is_rp')->dropDownList($model->getIsRpList(), [
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>

            <label class="col-lg-2 col-form-label">Refundable</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'is_refundable')->dropDownList($model->getIsRefundableList(), [
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>