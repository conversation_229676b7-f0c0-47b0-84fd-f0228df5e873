<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;

$this->title = 'Pipwave Mapper';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = 'Payment Method';
$this->params['breadcrumbs'][] = 'Pipwave Mapper';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('pipwave-payment-mapper/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <!--begin: Datatable -->
            <div class="m-section__content">
                <?=
                GridView::widget([
                    'dataProvider' => $dataProvider,
                    'filterModel' => $model,
                    'layout' => "{summary}\n{items}",
                    'columns' => [
                        ['class' => 'yii\grid\SerialColumn'],
                        [
                            'header' => 'Pipwave Payment Code',
                            'attribute' => 'pipwave_payment_code',
                        ],
                        [
                            'header' => 'Payment Gateway',
                            'attribute' => 'pg_display_name',
                        ],
                        [
                            'header' => 'Payment Method',
                            'attribute' => 'pm_display_name',
                        ],
                        [
                            'header' => 'Action',
                            'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
                            'class' => 'yii\grid\ActionColumn',
                            'template' => '{update} {view} {delete}',
                            'contentOptions' => ['style' => 'white-space: nowrap;'],
                            'buttons' => [
                                'view' => function ($url) { // <--- here you can override or create template for a button of a given name
                                    return Html::a('<span class="glyphicon glyphicon-history"></span>', $url, [
                                                'title' => 'History',
                                                'aria-label' => 'History',
                                                'data-pjax' => '0',
                                                    ]
                                    );
                                }
                            ],
                        ],
                    ],
                ]);
                ?>
                <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
            </div>
        </div>
    </div>
</div>
