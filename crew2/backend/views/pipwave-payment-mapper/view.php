<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\DetailView;
use yii\grid\GridView;

$this->title = 'Pipwave Mapper';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = 'Payment Method';
$this->params['breadcrumbs'][] = ['label' => 'Pipwave Mapper', 'url' => ['/pipwave-payment-mapper/index']];
$this->params['layoutContent'] = 'block';

$this->params['activeMenu'] = 'Finance/Payment Method/Pipwave Mapper';

\yii\web\YiiAsset::register($this);
?>

<div class="m-portlet m-portlet--mobile m-form">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $model->pg_display_name . " : " . $model->pm_display_name; ?><small>History</small>
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <?=
                GridView::widget([
                    'dataProvider' => $historyData,
                    'columns' => [
                        [
                            'header' => 'Changed By',
                            'attribute' => 'changed_by'
                        ],
                        [
                            'header' => 'Created At',
                            'attribute' => 'created_at',
                            'value' => function($data) {
                                return date('Y-m-d h:i:s A', $data['created_at']);
                            }
                        ],
                        [
                            'header' => 'Data Changes',
                            'attribute' => 'history_data',
                            'format' => 'raw',
                            'value' => function($data) {
                                $string = "Changes:<br>";
                                $historyArray = json_decode($data['history_data'], true);

                                foreach ($historyArray as $key => $value) {
                                    if ($key == 'before') {
                                        if (empty($value)) {
                                            $string .= "New Entry";
                                            break;
                                        } else {
                                            $string .= "<br>Before: <br>";
                                            foreach ($value as $keyData => $data) {
                                                $string .= $keyData . " => " . $data . "<br>";
                                            }
                                        }
                                    } else {
                                        $string .= "<br>After: <br>";
                                        foreach ($value as $keyData => $data) {
                                            $string .= $keyData . " => " . $data . "<br>";
                                        }
                                    }
                                }

                                return $string;
                            },
                        ]
                    ],
                ]);
                ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-12 m--align-right">
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>
</div>