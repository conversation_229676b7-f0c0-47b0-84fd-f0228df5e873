<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;

$this->title = 'Product Review Blacklist';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = 'Product Review Blacklist';
$this->params['layoutContent'] = 'block';

echo Yii::$app->controller->renderPartial('create-modal');

\backend\assets\DualListBoxAsset::register($this);
?>
<div class="game-product-attribute-index">

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        &nbsp;
                    </h3>
                </div>
            </div>

            <div class="m-portlet__head-tools">
                <div class="m-portlet__head-tools">
                    <div class="m--align-right">
                        <?php  if (Yii::$app->user->can('/product-review/get-item-list')||Yii::$app->user->can('/product-review/*')) {
                            echo '<a href="javascript:void(0);"
                           class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill"
                           onclick="$(\'#add-product-blacklist\').modal(\'toggle\');">
                            <span>
                                <i class="la la-plus"></i>
                                <span>New</span>
                            </span>
                        </a>';
                            }
                            ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="m-portlet__body">
            <div class="m-section">
                <!--begin: Datatable -->
                <div class="m-section__content">
                    <?php
                    echo \yii\grid\GridView::widget([
                        'dataProvider' => $provider,
                        'filterModel' => $filter,
                        'filterUrl' => $filter->filterUrl,
                        'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
                        'columns' => [
                            [
                                'header' => '#',
                                'content' => function ($model, $key, $index) use ($filter) {
                                    return $index + $filter->startAt;
                                },
                            ],
                            [
                                'attribute' => 'item_type',
                                'header' => 'Item Type',
                                'filter' => Html::activeDropDownList($filter, 'item_type', [
                                        '' => '',
                                        'Products' => 'Products',
                                    'Category' => 'Category',
                                ], [
                                    'class' => 'form-control'
                                ]),
                            ],
                            [
                                'attribute' => 'item_name',
                                'header' => 'Item Name',
                                'filter' => Html::textInput('item_name', $filter->item_name, ['class' => 'form-control']),
                            ],
                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{delete}'),
                                'contentOptions' => ['style' => 'white-space: nowrap;'],
                                'buttons' => [
                                    'delete' => function ($url, $model, $key) {
                                        return Html::a('<span class="glyphicon glyphicon-trash"></span>', 'javascript:void(0)', [
                                            'onclick' => 'deleteBlacklist("' . Url::to([
                                                    'product-review/delete',
                                                    'id' => $model['blacklist_id'],
                                                ]) . '")'
                                        ]);
                                    },
                                ]
                            ],
                        ],
                    ]);

                    echo $filter->renderPageSizer();

                    Modal::begin([
                        'title' => '',
                        'id' => 'generalModal',
                        'size' => Modal::SIZE_LARGE
                    ]);

                    Modal::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function deleteBlacklist(url) {
        Swal.fire({
            title: 'Delete this item?',
            showCloseButton: false,
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
            focusConfirm: false,
        }).then((result) => {
            if (result.value) {
                window.location.href = url;
            } else {
            }
        });
    }
</script>