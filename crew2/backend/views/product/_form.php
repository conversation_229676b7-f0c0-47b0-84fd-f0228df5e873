<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var common\models\G2gProductsMapping $model */
/** @var yii\widgets\ActiveForm $form */
?>


<?php $form = ActiveForm::begin([
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
    ],
]); ?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Products Id</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'products_id')->textInput()->label(false); ?>
        </div>
    </div>


    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">G2G Offer ID</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'g2g_offer_id')->textInput()->label(false); ?>
        </div>
    </div>

</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('product/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>
