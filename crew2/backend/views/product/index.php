<?php

use common\models\Products;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;

/** @var yii\web\View $this */
/** @var common\models\ProductSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = 'Products';
$this->params['breadcrumbs'][] = $this->title;
$this->params['layoutContent'] = 'block';
?>

<div class="game-publisher-index m-portlet m-portlet--mobile">

    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Product List
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">

                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">

        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'products_id',
                [
                    'label' => 'Products Name',
                    'format' => 'text',
                    'value' => function ($item) {
                        return \common\models\ProductsDescription::findOne(['products_id' => $item->products_id, 'language_id' => 1])->products_name;
                    }
                ],
                'products_model',
                [
                    'class' => ActionColumn::className(),
                    'template' => '{update}',
                    'urlCreator' => function ($action, Products $model, $key, $index, $column) {
                        return Url::toRoute([$action, 'products_id' => $model->products_id]);
                    }
                ],
            ],
        ]); ?>

        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>
