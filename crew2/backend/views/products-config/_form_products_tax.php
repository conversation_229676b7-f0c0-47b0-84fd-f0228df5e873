<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use kartik\widgets\SwitchInput;

?>

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
                <div class="m-portlet__head-title">
                    <h3 class="m-portlet__head-text">
                        <div id="products_list_header"></div>
                    </h3>
                </div>
            </div>
        </div>

        <!--begin::Form-->
        <?php
        $form = ActiveForm::begin([
            'action' => ['/products-config/index'],
            'method' => 'post',
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
        ]);
        ?>

        <?php if ($models) { ?>
            <div class="m-portlet__body">
                <div class="m-form__section">
                    <?php for ($k = 0; $k < sizeof($models); $k++) { ?>
                        <?php
                        $model = $models[$k];
                        $pid = $model->products_id;
                        ?>

                        <div class="form-group m-form__group row">
                            <?php echo (isset($model->products_id)) ? Html::activeHiddenInput($model, "products_id",
                                ['name' => 'form' . '[' . $pid . '][products_id]',]) : '' ?>
                            <label class="col-lg-2">Product ID</label>
                            <div class="col-lg-10"
                                 name="form[<?= $pid ?>][products_id]"><?= $model->products_id ?></div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2">Product Name</label>
                            <div class="col-lg-10"
                                 name="form[<?= $pid ?>][product_name]"><?= $model->product_name ?></div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-2">MRV <i data-toggle="m-tooltip"
                                                          data-html="true"
                                                          title="Voucher Type <br>MRV : Multi-Redemption Voucher <br>Non-MRV : Non Multi-Redemption Voucher"
                                                          class="fa fa-question-circle"></i></label>
                            <div class="col-lg-4">
                                <?= SwitchInput::widget([
                                    'name' => 'form[' . $pid . '][category]',
                                    'value' => ($model->category) ? 1 : 0,
                                    'pluginEvents' => [
                                        'switchChange.bootstrapSwitch' => 'function(e){ fieldControl(e.currentTarget.checked,' . $pid . '); }'
                                    ],
                                    'pluginOptions' => [
                                        'onText' => '<i class="fa fa-check"></i>',
                                        'offText' => '<i class="fa fa-times"></i>',
                                    ],
                                ]); ?>
                            </div>
                        </div>
                        <div id="<?= $pid ?>-face_value_div" <?= (!$model->category) ? "style='display:none;'" : '' ?>
                             class="form-group m-form__group row">
                            <label class="col-lg-2 col-form-label">Face Value</label>
                            <div class="col-lg-2">
                                <?=
                                $form->field($model,
                                    '[' . $pid . ']currency')->dropDownList(Yii::$app->currency->getCurrencyList(), [
                                    'name' => 'form[' . $pid . '][currency]',
                                    'prompt' => 'Select',
                                    'disabled' => !$model->category,
                                ])->label(false); ?>
                            </div>
                            <div class="col-lg-4">
                                <?=
                                $form->field($model, '[' . $pid . ']products_value')->textInput([
                                    'name' => 'form[' . $pid . '][products_value]',
                                    'disabled' => !$model->category,
                                ])->label(false);
                                ?>
                            </div>
                        </div>
                        <?php if (sizeof($models) !== $k + 1) { ?>
                            <div class="m-separator m-separator--dashed m-separator--lg"></div>
                        <?php } else { ?>
                            <br>
                        <?php } ?>
                    <?php } ?>
                </div>
            </div>

            <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
                <div class="m-form__actions m-form__actions--solid">
                    <div class="row">
                        <div class="col-lg-2"></div>
                        <div class="col-lg-10">
                            <button type="submit" class="btn btn-success" id="btn-submit">Save</button>
                            <button type="button" class="btn btn-secondary"
                                    onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        <?php } else { ?>
            <div class="m-portlet__body">
                <div class="m-form__section">
                    <div class="form-group m-form__group row" style="display: block">
                        <div style="text-align: center">No result found.</div>
                    </div>
                </div>
            </div>
        <?php } ?>

        <?php ActiveForm::end(); ?>
    </div>

<?php

$checkMrvStatus = <<< JS
    function fieldControl(status,pid) {
        let currency_selector = 'select[name="form[' + pid + '][currency]"]';
        let productsValue_selector = 'input[name="form[' + pid + '][products_value]"]';
        let faceValueId_selector = '#' + pid + '-face_value_div';
        if (status) {
            $(currency_selector).prop( "disabled", false );
            $(productsValue_selector).prop( "disabled", false );
            $(faceValueId_selector).show();
        } else {
            $(currency_selector).prop( "disabled", true );
            $(productsValue_selector).prop( "disabled", true );
            $(faceValueId_selector).hide();
        }
    }
JS;
$this->registerJs($checkMrvStatus);
?>