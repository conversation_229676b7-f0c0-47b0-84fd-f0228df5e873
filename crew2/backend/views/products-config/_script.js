$('#search-product').click(function () {
    var cid = $('select[name="categories_id"]').val();
    if (cid) {
        var cat_name = $('select[name="categories_id"]').find('option:selected').text();
        $.blockUI();
        $.ajax({
            url: '/products-config/get-products-tax-form?cid=' + cid,
            success: function (result) {
                $('#products_tax_form').html(result);
                $('#products_list_header').text(cat_name);
                initTooltips();
                $.unblockUI();
            }
        });
    } else {
        $('#products_tax_form').empty();
    }
});

// tooltip
var initTooltip = function (el) {
    var skin = el.data('skin') ? 'm-tooltip--skin-' + el.data('skin') : '';
    var width = el.data('width') == 'auto' ? 'm-tooltop--auto-width' : '';
    var triggerValue = el.data('trigger') ? el.data('trigger') : 'hover';
    var placement = el.data('placement') ? el.data('placement') : 'left';

    el.tooltip({
        trigger: triggerValue,
        template: '<div class="m-tooltip ' + skin + ' ' + width + ' tooltip" role="tooltip">\
                <div class="arrow"></div>\
                <div class="tooltip-inner"></div>\
            </div>'
    });
}

var initTooltips = function () {
    // init bootstrap tooltips
    $('[data-toggle="m-tooltip"]').each(function () {
        initTooltip($(this));
    });
}