<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$this->title = 'Settings';

$this->params['breadcrumbs'][] = 'Products';
$this->params['breadcrumbs'][] = 'Settings';
$this->params['layoutContent'] = 'block';

$form = ActiveForm::begin([
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right',
    ],
]);

$this->registerJs($this->render('_script.js'));
?>

<div class="row">
    <div class="col-lg-12" style="display: block">
        <a type="button" id="export-products" class="btn btn-warning m-btn m-btn--icon m-btn--wide" href='<?= Url::toRoute('export-products'); ?>' download
           alt="Download CSV" style="float: right; color: #212529">
            <i class="fa fa-download"></i> Export All
        </a>
    </div>
</div>
<br>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm"
     m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
            <span class="m-portlet__head-icon">
                <i class="flaticon-search"></i>
            </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i
                                class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-form__section">
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">Category</label>
                <div class="col-lg-4">
                    <?=
                    html::dropDownList('categories_id', null, $model->categories_list, [
                        'id' => 'categories',
                        'prompt' => 'Select',
                        'class' => 'form-control'
                    ]);
                    ?>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="button" id="search-product" name="search-product" class="btn btn-primary">Search
                    </button>
                    <button type="button" class="btn btn-default"
                            onclick="document.location = '<?= Url::toRoute('index'); ?>'">Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="products_tax_form"></div>