<?php

use kartik\widgets\DateTimePicker;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;

if (!$model->isNewRecord) {
    $model->start_time = date('Y-m-d H:i', (int) $model->start_time);
    $model->end_time = date('Y-m-d H:i', (int) $model->end_time);
}

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
            ],
        ]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Title</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'title')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Page Title</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'page_title')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Start Time</label>
        <div class="col-lg-4">
            <?=
            $form->field($model, 'start_time', ['enableClientValidation' => false])->widget(DateTimePicker::classname(), [
                'removeButton' => false,
                'pickerButton' => ['icon' => 'time'],
                'pluginOptions' => [
                    'autoclose' => true,
                    'format' => 'yyyy-mm-dd hh:ii',
                ]
            ])->label(false);
            ?>
        </div>

        <label class="col-lg-2 col-form-label">End Time</label>
        <div class="col-lg-4">
            <?=
            $form->field($model, 'end_time', ['enableClientValidation' => false])->widget(DateTimePicker::classname(), [
                'removeButton' => false,
                'pickerButton' => ['icon' => 'time'],
                'pluginOptions' => [
                    'autoclose' => true,
                    'format' => 'yyyy-mm-dd hh:ii'
                ]
            ])->label(false);
            ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">HTML</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'html')->textarea(['rows' => 12, 'class' => 'form-control'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">CSS</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'css')->textarea(['rows' => 12, 'class' => 'form-control'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">JavaScript</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'javascript')->textarea(['rows' => 12, 'class' => 'form-control'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">JSON Data</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'json_data')->textarea(['rows' => 12, 'class' => 'form-control'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-10">
            <?=
            $form->field($model, 'status')->widget(SwitchInput::classname(), [
                'value' => (isset($model->status) && $model->status == '1') ? true : false,
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ]
            ])->label(false);
            ?>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('promo-content/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>