<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;

$this->title = 'Promotion';

$this->params['breadcrumbs'][] = 'Marketing';
$this->params['breadcrumbs'][] = 'Promotion';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('promo-content/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <!--begin: Datatable -->
            <div class="m-section__content">
                <?=
                GridView::widget([
                    'dataProvider' => $dataProvider,
                    'layout' => "{summary}\n{items}",
                    'columns' => [
                        ['class' => 'yii\grid\SerialColumn'],
                        'title',
                        [
                            'attribute' => 'start_time',
                            'content' => function ($model) {
                                return date('Y-m-d H:i:s A', $model->start_time);
                            }
                        ],
                        [
                            'attribute' => 'end_time',
                            'content' => function ($model) {
                                return date('Y-m-d H:i:s A', $model->end_time);
                            }
                        ],
                        [
                            'class' => 'yii\grid\ActionColumn',
                            'template' => mdm\admin\components\Helper::filterActionColumn('{update} {delete}'),
                            'contentOptions' => ['style' => 'white-space: nowrap;'],
                        ],
                    ],
                ]);
                ?>
                <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
            </div>
        </div>
    </div>
</div>