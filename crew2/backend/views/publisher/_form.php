<?php

use common\models\Publisher;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;

/* @var $model \common\models\Publisher */
?>

<?php
$form = ActiveForm::begin([
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
    ],
]);

$list = array_keys($model::PUBLISHER_LIST);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Title</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'title')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Profile</label>
        <div class="col-lg-4">
            <?=
            $form->field($model, 'profile')->dropDownList(
                array_combine($list, $list), [
                'onchange' => 'loadPublisherConfig(this)',
                'prompt' => 'Select',
                'class' => 'selectpicker m-datatable__pager-size'
            ])->label(false);
            ?>
        </div>
        <label class="col-lg-2 col-form-label">Status</label>
        <div class="col-lg-4">
            <?=
            $form->field($model, "status")->widget(SwitchInput::classname(), [
                'value' => (isset($model->status) && $model->status == 1) ? true : false,
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ]
            ])->label(false);
            ?>
        </div>
    </div>

    <div id="publisher_setting">
        <?php
        if ($model->publisher_id) {
            if (isset(Publisher::PUBLISHER_LIST[$model->profile])) {
                $class = Publisher::PUBLISHER_LIST[$model->profile];
                if (class_exists($class)) {
                    $profile = new $class();
                    $profile->publisher = $model->publisher_id;
                    $profile->getExistingConfig();
                    echo $profile->renderConfigurationField();
                }
            }
        }
        ?>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('publisher/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<script>
    function loadPublisherConfig(profile = null) {
        $("#publisher_setting").html('');
        if (profile.value != null && profile.value != '') {
            $.ajax({
                url: "/publisher/get-profile?id=" + profile.value, success: function (result) {
                    $("#publisher_setting").html(result);
                }
            });
        }
    }
</script>