<?php

use common\models\PublisherProduct;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;

/** @var yii\web\View $this */
/** @var common\models\PublisherProductSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = 'Publisher Products';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="publisher-product-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'layout' => "{summary}\n{items}",
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'sku',
            'name',
            'cost_price',
            'cost_currency',
            [
                'class' => 'yii\grid\ActionColumn',
                'header' => 'Action',
                'template' => mdm\admin\components\Helper::filterActionColumn('{view}'),
                'buttons' => [
                    'view' => function ($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-eye-open"></span>',
                            \yii\helpers\Url::to([
                                'publisher/view-product',
                                'publisher_product_id' => $model['publisher_product_id'],
                                'publisher_id' => $model['publisher_id']
                            ])
                        );
                    }
                ]
            ],
        ],
    ]); ?>
    <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>


</div>
