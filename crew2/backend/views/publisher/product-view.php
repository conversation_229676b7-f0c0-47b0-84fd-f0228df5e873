<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/** @var yii\web\View $this */
/** @var common\models\PublisherProduct $model */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Publisher', 'url' => ['index', 'publisher_id' => $model->publisher_id]];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
    <div class="publisher-product-view">

        <h1><?= Html::encode($this->title) ?></h1>

        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'publisher_product_id',
                'publisher_id',
                'name',
                'description:ntext',
                'cost_price',
                'cost_currency',
                'sku',
                'denomination',
                'attribute_1',
                'attribute_2',
                'attribute_3',
                [
                    'label' => 'Raw',
                    'format' => 'text',
                    'value' => '',
                    'contentOptions' => [
                        'class' => 'prettyprint2',
                        'data-raw' => (!empty($model->raw) ? $model->raw : '')
                    ],
                ],
                'status',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>

    </div>

<?php
$js = <<< JS
    var pretty_print = $('.prettyprint2')
    for (var i = 0; i < pretty_print.length; i++) {
      var json = $(pretty_print[i]).data('raw')
      json = getJson(json)
      if (typeof json == 'object') {
        $.each(json, function (index, e) {
          json[index] = getJson(e)
        })
      }
      $(pretty_print[i]).html('<pre class=\'prettyprint\' style=\'max-height:800px;overflow-x:scroll\'>' + JSON.stringify(json, null, 4) + '<pre>')
    }

JS;
$this->registerJs($js, \yii\web\View::POS_READY);