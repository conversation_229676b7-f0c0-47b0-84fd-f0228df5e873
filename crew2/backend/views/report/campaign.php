<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DateTimePicker;
use kartik\switchinput\SwitchInput;
use backend\assets\DualListBoxAsset;

DualListBoxAsset::register($this);

$this->title = 'Campaign Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Campaign';
$this->params['layoutContent'] = 'block';

$this->registerJs(
        "$(document).ready(function () {
            let catList = $('select[name=\"cat_id[]\"]');
            catList.bootstrapDualListbox({
                moveOnSelect: false,
                selectedListLabel: 'Selected',
                nonSelectedListLabel: 'Options'
            });

            let pmList = $('select[name=\"payment_method[]\"]');
            pmList.bootstrapDualListbox({
                moveOnSelect: false,
                selectedListLabel: 'Selected',
                nonSelectedListLabel: 'Options'
            });

            let custGrpList = $('select[name=\"exclude_customer_group[]\"]');
            custGrpList.bootstrapDualListbox({
                moveOnSelect: false,
                selectedListLabel: 'Selected',
                nonSelectedListLabel: 'Options'
            });

            let ctryList = $('select[name=\"ip_country_id[]\"]');
            ctryList.bootstrapDualListbox({
                moveOnSelect: false,
                selectedListLabel: 'Selected',
                nonSelectedListLabel: 'Options'
            });

            let usePmList = $('select[name=\"use_payment_method[]\"]');
            usePmList.bootstrapDualListbox({
                moveOnSelect: false,
                selectedListLabel: 'Selected',
                nonSelectedListLabel: 'Options'
            });
    });"
);

$form = ActiveForm::begin([
            'id' => 'campaign',
            'action' => ['/report/campaign'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
                'autocomplete' => "off"
            ],
        ]);
?>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'start_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'start_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'end_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'end_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Sales Currency<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'currency')->dropDownList($f_cur, [
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2">Min Purchase Per Transaction<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?= $form->field($model, 'min_purchase_per_transaction')->textInput(['class' => 'form-control', 'placeholder' => 0.01])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Min Total Purchase Amount</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'min_purchase')->textInput(['class' => 'form-control', 'placeholder' => 0.01])->label(false); ?>
            </div>
            <label class="col-lg-2">Max Total Purchase Amount</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'max_purchase')->textInput(['class' => 'form-control'])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Purchase Amount Per Draw<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?= $form->field($model, 'amt_per_draw')->textInput(['class' => 'form-control', 'placeholder' => 0.01])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">OffGamers Only</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'ogm_only')->widget(SwitchInput::classname(), [
                    'value' => $model->ogm_only,
                    'options' => [
                        'checked' => $model->ogm_only,
                    ],
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Category</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'cat_id')->dropDownList($f_cat, [
                    'name' => 'cat_id[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Payment Method</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'payment_method')->dropDownList($f_pm, [
                    'name' => 'payment_method[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Exclude Customer Group</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'exclude_customer_group')->dropDownList($f_cust_grp, [
                    'name' => 'exclude_customer_group[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Checkout IP Country</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'ip_country_id')->dropDownList($f_ctry, [
                    'name' => 'ip_country_id[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="m-form__group row">
            <div class="col-lg-12">Additional Information : customer use specific payment method(s) within date range</div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'use_start_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'use_start_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'use_end_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'use_end_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Payment Method</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'use_payment_method')->dropDownList($f_pm, [
                    'name' => 'use_payment_method[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Recipient<span style="color: red;">*</span></label>
            <div class="col-lg-7">
                <?= $form->field($model, 'recipient')->textInput(['placeholder' => 'use comma separator for more recipient'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_btn" name="submit_btn" class="btn btn-warning" value="export"><i class="fa fa-download"></i> Export</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>
