<?php

use kartik\widgets\DateTimePicker;
use yii\bootstrap\ActiveForm;
use backend\assets\DualListBoxAsset;
DualListBoxAsset::register($this);

$this->title = 'Coupon Usage Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Coupon';

$this->params['layoutContent'] = 'block';

$this->registerJs($this->render('coupon_form_script.js'));

$form = ActiveForm::begin([
            'id' => 'coupon-filt',
            'action' => ['/report/coupon'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
                'autocomplete' => "off"
            ],
            'enableAjaxValidation' => true
        ]);
?>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'startDate')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'startDate'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'endDate')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'endDate'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Completed Order before Coupon Usage Start Date</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'previousPurchaseDate')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'previousPurchaseDate'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Subsequent purchase after Coupon Usage End Date</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'subsequentPurchaseDate')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'subsequentPurchaseDate'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true]
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2">Min Total Purchase Amount (USD)</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'minPurchase')->textInput(['class' => 'form-control', 'placeholder' => 0.01])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2">Payment Method</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'paymentMethods')->dropDownList($f_pm, [
                    'name' => 'paymentMethods[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Exclude Customer Group</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'excludeCustomerGroup')->dropDownList($f_cust_grp, [
                    'name' => 'excludeCustomerGroup[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="m-form__group row">
            <div class="col-lg-12">Coupon</div>
        </div>

        <div class="form-group m-form__group row">

            <label id="coupon_code_input_label" class="col-lg-2">Coupon Code</label>
            <div id="coupon_code_input_form" class="col-lg-4">
                <?= $form->field($model, 'couponCode')
                    ->textInput([
                        'id' => 'couponCode',
                        'class' => 'form-control',
                        'placeholder' => 'e.g FEB15'
                    ])
                    ->label(false); ?>
            </div>

            <label id="coupon_generation_id_input_label" class="col-lg-2">Coupon Generation Id</label>
            <div id="coupon_generation_id_input_form" class="col-lg-4">
                <?= $form->field($model, 'couponGenerationId')
                    ->textInput([
                        'id' => 'coupon_generation_id',
                        'class' => 'form-control',
                        'placeholder' => 'e.g 1336'
                    ])
                    ->label(false); ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Recipient <span style="color: red;">*</span></label>
            <div class="col-lg-7">
                <?= $form->field($model, 'reportRecipient')->textInput(['placeholder' => 'use comma separator for more recipient'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_btn" name="submit_btn" class="btn btn-warning" value="export"><i class="fa fa-download"></i> Export</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>