<?php

use yii\helpers\Url;

$this->title = 'Tax Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Sales';

$this->params['subHeader'] = [
    "title" => "Order Status",
    "option" => [
        [
            "link" => Url::to(['index', 'status' => 'process'], true),
            "title" => "Processing & Completed",
            "active" => ($_status == "process" ? true : false)
        ],
        [
            "link" => Url::to(['index', 'status' => 'complete'], true),
            "title" => "Completed",
            "active" => ($_status == "complete" ? true : false)
        ]
    ]
];
$this->params['layoutContent'] = 'block';
?>

<div class="text-right">
    <small><i>Report Date : <?= date("Y-m-d", strtotime($_date)); ?></i></small>
</div>
<div class="m-portlet ">
    <div class="m-portlet__body  m-portlet__body--no-padding">
        <div class="row m-row--no-padding m-row--col-separator-xl">
            <div class="col-md-12 col-lg-6 col-xl-3">
                <div class="m-widget24">
                    <div class="m-widget24__item">
                        <h4 class="m-widget24__title">
                            Total Sales
                        </h4><br>
                        <div class="m-widget24__desc m--font-brand">
                            <h3><?= Yii::$app->currency->format("USD", $total_sales); ?></h3>
                        </div>
                        <div class="progress m-progress--sm">
                            <div class="progress-bar m--bg-brand" role="progressbar" style="width: <?= ($total_sales ? round(($completed_sales / $total_sales) * 100) : 0); ?>%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="m-widget24__change">
                            <span class="m--font-brand"><?= Yii::$app->currency->format("USD", $completed_sales); ?></span>
                            <br />Completed
                        </span>
                        <span class="m-widget24__number">
                            <?= ($total_sales ? (($completed_sales > $total_sales) ? 100 : round(($completed_sales / $total_sales) * 100)) : 0); ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-3">
                <div class="m-widget24">
                    <div class="m-widget24__item">
                        <h4 class="m-widget24__title">
                            New Orders
                        </h4><br>
                        <div class="m-widget24__desc m--font-danger">
                            <h3><?= number_format($total_order, 0, "", ","); ?></h3>
                        </div>
                        <div class="progress m-progress--sm">
                            <div class="progress-bar m--bg-danger" role="progressbar" style="width: <?= ($total_order ? round(($completed_order / $total_order) * 100) : 0); ?>%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="m-widget24__change">
                            <span class="m--font-danger"><?= number_format($completed_order, 0, "", ","); ?></span>
                            <br />Completed
                        </span>
                        <span class="m-widget24__number">
                            <?= ($total_order ? round(($completed_order / $total_order) * 100) : 0); ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-3">
                <div class="m-widget24">
                    <div class="m-widget24__item">
                        <h4 class="m-widget24__title">
                            Avg Order Amount
                        </h4><br>
                        <div class="m-widget24__desc m--font-info">
                            <h3><?= Yii::$app->currency->format("USD", ($total_order ? ($total_sales / $total_order) : 0)); ?></h3>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-3">
                <div class="m-widget24">
                    <div class="m-widget24__item">
                        <h4 class="m-widget24__title">
                            New Customers
                        </h4><br>
                        <div class="m-widget24__desc m--font-success">
                            <h3><?= number_format($sign_up["ogm"], 0, "", ","); ?></h3>
                        </div>
                        <div class="progress m-progress--sm">
                            <div class="progress-bar m--bg-success" role="progressbar" style="width: <?= ($sign_up["total"] ? round(($sign_up["ogm"] / $sign_up["total"]) * 100) : 0); ?>%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="m-widget24__change">
                            <span class="m--font-success"><?= number_format($sign_up["total"], 0, "", ","); ?></span>
                            <br />Total
                        </span>
                        <span class="m-widget24__number">
                            <?= ($sign_up["total"] ? round(($sign_up["ogm"] / $sign_up["total"]) * 100) : 0); ?>%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-12">
        <div class="m-portlet  m-portlet--full-height ">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Top Products
                        </h3>
                    </div>
                </div>
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-pills nav-pills--brand m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm" role="tablist">
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link active" data-toggle="tab" href="<?= Url::toRoute('/sales-report/product'); ?>" onclick="document.location = '<?= Url::toRoute('/sales-report/product'); ?>'" role="tab">
                                More
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="m-portlet__body">
                <div class="m-widget16">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="m-widget16__head">
                                <div class="m-widget16__item">
                                    <span class="m-widget16__sceduled" style="width: 60%;">
                                        &nbsp;
                                    </span>
                                    <span class="m-widget16__amount m--align-right" style="width: 20%;">
                                        Order
                                    </span>
                                    <span class="m-widget16__amount m--align-right" style="width: 20%;">
                                        Sales
                                    </span>
                                </div>
                            </div>

                            <div class="m-widget16__body">
                                <?php foreach ($product as $pid => $val) { ?>
                                    <div class="m-widget16__item">
                                        <span class="m-widget16__date" style="width: 60%;">
                                            <?= $val["name"]; ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand" style="width: 20%;">
                                            <?= number_format($val["order"], 0, "", ","); ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand" style="width: 20%;">
                                            <?= Yii::$app->currency->format("USD", $val["sales"]); ?>
                                        </span>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-6">
        <div class="m-portlet  m-portlet--full-height ">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Country
                        </h3>
                    </div>
                </div>
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-pills nav-pills--brand m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm" role="tablist">
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link active" data-toggle="tab" href="#m_widget4_tab1_content" role="tab">
                                More
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="m-portlet__body">
                <div class="m-widget16">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="m-widget16__head">
                                <div class="m-widget16__item">
                                    <span class="m-widget16__sceduled">
                                        &nbsp;
                                    </span>
                                    <span class="m-widget16__amount m--align-right">
                                        Customer
                                    </span>
                                    <span class="m-widget16__amount m--align-right">
                                        Sales
                                    </span>
                                </div>
                            </div>

                            <div class="m-widget16__body">
                                <?php foreach ($country as $id => $val) { ?>
                                    <div class="m-widget16__item">
                                        <span class="m-widget16__date">
                                            <?= $val["name"]; ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand">
                                            <?= number_format($val["customer"], 0, "", ","); ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand">
                                            <?= Yii::$app->currency->format("USD", $val["sales"]); ?>
                                        </span>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="col-xl-6">
        <div class="m-portlet  m-portlet--full-height ">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Customer Group
                        </h3>
                    </div>
                </div>
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-pills nav-pills--brand m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm" role="tablist">
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link active" data-toggle="tab" href="#m_widget4_tab1_content" role="tab">
                                More
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="m-portlet__body">
                <div class="m-widget16">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="m-widget16__head">
                                <div class="m-widget16__item">
                                    <span class="m-widget16__sceduled">
                                        &nbsp;
                                    </span>
                                    <span class="m-widget16__amount m--align-right">
                                        Customer
                                    </span>
                                    <span class="m-widget16__amount m--align-right">
                                        Sales
                                    </span>
                                </div>
                            </div>

                            <div class="m-widget16__body">
                                <?php
                                foreach ($customer_group as $id => $val) {
                                    if ($id != 1) {
                                        ?>
                                        <div class="m-widget16__item">
                                            <span class="m-widget16__date">
                                                <?= $val; ?>
                                            </span>
                                            <span class="m-widget16__price m--align-right m--font-brand">
                                                <?= number_format((isset($customer[$id]["customer"]) ? $customer[$id]["customer"] : 0), 0, "", ","); ?>
                                            </span>
                                            <span class="m-widget16__price m--align-right m--font-brand">
                                                <?= Yii::$app->currency->format("USD", (isset($customer[$id]["sales"]) ? $customer[$id]["sales"] : 0)); ?>
                                            </span>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-12">
        <div class="m-portlet m-portlet--full-height m-portlet--fit ">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">
                    <div class="m-portlet__head-title">
                        <h3 class="m-portlet__head-text">
                            Payment Method
                        </h3>
                    </div>
                </div>
                <div class="m-portlet__head-tools">
                    <ul class="nav nav-pills nav-pills--brand m-nav-pills--align-right m-nav-pills--btn-pill m-nav-pills--btn-sm" role="tablist">
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link active" data-toggle="tab" href="#m_widget4_tab1_content" role="tab">
                                More
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="m-portlet__body">
                <div class="m-widget16">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="m-widget16__head">
                                <div class="m-widget16__item">
                                    <span class="m-widget16__sceduled" style="width: 50%;">
                                        &nbsp;
                                    </span>
                                    <span class="m-widget16__amount" style="width: 10%;">
                                        &nbsp;
                                    </span>
                                    <span class="m-widget16__amount m--align-right" style="width: 20%;">
                                        Order
                                    </span>
                                    <span class="m-widget16__amount m--align-right" style="width: 20%;">
                                        Sales
                                    </span>
                                </div>
                            </div>

                            <div class="m-widget16__body">
                                <?php foreach ($payment_method as $id => $val) { ?>
                                    <div class="m-widget16__item">
                                        <span class="m-widget16__date" style="width: 50%;">
                                            <?= $val["name"]; ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand" style="width: 10%;">
                                            <?php if ($val["rp"] != "") { ?>
                                                <span class="m-badge m-badge--info m-badge--wide" style="line-height: normal; min-height: auto;"><?= $val["rp"]; ?></span>
                                            <?php } ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand" style="width: 20%;">
                                            <?= number_format($val["order"], 0, "", ","); ?>
                                        </span>
                                        <span class="m-widget16__price m--align-right m--font-brand" style="width: 20%;">
                                            <?= Yii::$app->currency->format("USD", $val["sales"]); ?>
                                        </span>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>