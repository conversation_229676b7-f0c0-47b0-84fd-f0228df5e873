<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\LinkPager;
use yii\widgets\ActiveForm;
use kartik\daterange\DateRangePicker;
use backend\models\ReportPoForm;
use kartik\export\ExportMenu;

$this->title = 'Report';

$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Purchase Order';
$this->params['layoutContent'] = 'block';

$gridColumns = [
    ['class' => 'yii\grid\SerialColumn'],
    [
        'header' => 'ID',
        'attribute' => 'purchase_orders_id',
    ],
    [
        'header' => 'Ref #',
        'attribute' => 'purchase_orders_ref_id',
    ],
    [
        'header' => 'Type',
        'attribute' => 'purchase_orders_type',
        'content' => function ($model) {
            if ($model["payment_term"]) {
                return $model['payment_term'] . 'day-Term';
            } else {
                return ReportPoForm::getPaymentType($model["payment_type"]) . " (" . ReportPoForm::getPurchaseOrderType($model["purchase_orders_type"]) . ")";
            }
        },
    ],
    [
        'header' => 'Issue Date',
        'attribute' => 'issue_date',
        'filter' => DateRangePicker::widget([
            'name' => 'poDateRange',
            'value' => $searchModel->startDate . ' - ' . $searchModel->endDate,
            'startAttribute' => 'startDate',
            'endAttribute' => 'endDate',
            'convertFormat' => true,
            'pluginOptions' => [
                'locale' => [
                    'format' => 'Y-m-d H:i:s',
                ],
                'opens' => 'left',
                'timePicker' => false,
            ],
        ]),
    ],
    [
        'header' => 'Processing Date',
        'attribute' => 'process_date',
    ],
    [
        'header' => 'Last Status',
        'content' => function ($model) {
            return ($model["final_status"] == 3 ? "Completed" : ($model["final_status"] == 4 ? "Cancel" : "-"));
        }
    ],
    [
        'header' => 'Completed / Cancel Date',
        'content' => function ($model) {
            return (in_array($model["final_status"], [3, 4]) ? $model["final_date"] : "-");
        }
    ],
];
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <?=
                    ExportMenu::widget([
                        'dataProvider' => $dataProvider,
                        'columns' => $gridColumns,
                        'filename' => "po_report_" . date("Ymd", strtotime($searchModel->startDate)) . '_' . date("Ymd", strtotime($searchModel->endDate)),
                        'asDropdown' => false,
                        'target' => ExportMenu::TARGET_SELF,
                        'showConfirmAlert' => false,
                        'clearBuffers' => true,
                        'exportConfig' => [
                            ExportMenu::FORMAT_CSV => [
                                'label' => "Export",
                                'icon' => "fa fa-download",
                                'iconOptions' => ['style' => 'color: #000 !important;'],
                                'linkOptions' => [
                                    "class" => "btn btn-warning m-btn m-btn--icon m-btn--wide"
                                ],
                                'options' => [
                                    "title" => "",
                                    "style" => "list-style-type: none !important"
                                ],
                            ],
                            ExportMenu::FORMAT_EXCEL_X => false,
                            ExportMenu::FORMAT_TEXT => false,
                            ExportMenu::FORMAT_EXCEL => false,
                            ExportMenu::FORMAT_HTML => false,
                            ExportMenu::FORMAT_PDF => false
                        ],
                    ]);
                    ?>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <!--begin: Datatable -->
            <div class="m-section__content">
                <?=
                GridView::widget([
                    'dataProvider' => $dataProvider,
                    'filterModel' => $searchModel,
                    'layout' => "{summary}\n{items}",
                    'columns' => $gridColumns,
                ]);
                ?>
                <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
            </div>
        </div>
    </div>
</div>