<?php

use kartik\widgets\DateTimePicker;
use yii\bootstrap\ActiveForm;

$this->title = 'No Purchase Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'No Purchase';

$this->params['layoutContent'] = 'block';

$form = ActiveForm::begin([
            'id' => 'no-purchase-filt',
            'action' => ['/report/no-purchase'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
                'autocomplete' => "off"
            ]
        ]);
?>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Sign-Up Start Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'signup_fr_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'signup_fr_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'signup_fr_date' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Sign-Up End Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'signup_to_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'signup_to_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'signup_to_date' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Order Start Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'start_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'start_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'start_date' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Order End Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'end_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'end_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd hh:ii:ss', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'end_date' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Recipient <span style="color: red;">*</span></label>
            <div class="col-lg-7">
                <?= $form->field($model, 'recipient')->textInput(['placeholder' => 'use comma separator for more recipient'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_btn" name="submit_btn" class="btn btn-warning" value="export"><i class="fa fa-download"></i> Export</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>