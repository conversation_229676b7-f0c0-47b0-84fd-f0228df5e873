<?php

use kartik\widgets\DateTimePicker;
use yii\bootstrap\ActiveForm;
use backend\assets\DualListBoxAsset;

DualListBoxAsset::register($this);

$this->title = 'Sales Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Sales';

$this->params['layoutContent'] = 'block';

$this->registerJs(
    "$(document).ready(function () {
        let catList = $('select[name=\"excl_cat[]\"]');
        catList.bootstrapDualListbox({
            moveOnSelect: false,
            selectedListLabel: 'Selected',
            nonSelectedListLabel: 'Options',
        });
        
    });"
);

$form = ActiveForm::begin([
            'id' => 'sales',
            'action' => ['/report/sales'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
                'autocomplete' => "off"
            ]
        ]);
?>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Report <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'report')->dropDownList($model->_report, [
                    'id' => 'report',
                    'class' => 'selectpicker m-datatable__pager-size',
                    'onchange' => "resetCheck()"
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'start_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'start_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'end_date')->widget(DateTimePicker::classname(), [
                    'options' => ['id' => 'end_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayBtn' => 'linked',
                        'autoclose' => true,
                        'todayHighlight' => true
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row hide" id="report_type_opt">
            <label class="col-lg-2 col-form-label">Report Type <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'report_type')->dropDownList($model->_report_type, [
                    'id' => 'report_type',
                    'class' => 'selectpicker m-datatable__pager-size',
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2">Exclude Category</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'excl_cat')->dropDownList($f_cat, [
                    'name' => 'excl_cat[]',
                    'class' => 'form-control list m-datatable__pager-size',
                    'multiple size' => '12',
                    'data-target' => 'available'
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Recipient <span style="color: red;">*</span></label>
            <div class="col-lg-7">
                <?= $form->field($model, 'recipient')->textInput(['placeholder' => 'use comma separator for more recipient'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_btn" name="submit_btn" class="btn btn-warning" value="export"><i class="fa fa-download"></i> Export</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<script type="text/javascript">
    function resetCheck()
    {
        setTimeout(function () {
            if ($('select[name="SalesReportForm[report]"]').val() == 1) {
                $('#report_type_opt').addClass('hide');
            } else if ($('select[name="SalesReportForm[report]"]').val() == 2) {
                $('#report_type_opt').removeClass('hide');
            }
        }, 1);
    }
</script>
