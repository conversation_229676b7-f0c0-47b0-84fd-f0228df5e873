<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use kartik\widgets\DatePicker;
use kartik\switchinput\SwitchInput;
use backend\assets\DualListBoxAsset;
DualListBoxAsset::register($this);

$this->title = 'Tax Report';
$this->params['breadcrumbs'][] = 'Report';
$this->params['breadcrumbs'][] = 'Tax';

$this->params['layoutContent'] = 'block';

$this->registerJs($this->render('_script.js'));

$form = ActiveForm::begin([
            'id' => 'tax-filt',
            'action' => ['/report/tax'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
                'autocomplete' => "off"
            ],
        ]);
?>

<div class="m-portlet m-portlet--primary <?= ($provider ? "m-portlet--collapsed" : ""); ?> m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'start_date')->widget(DatePicker::classname(), [
                    'options' => ['id' => 'start_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date <span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'end_date')->widget(DatePicker::classname(), [
                    'options' => ['id' => 'end_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => date('Y-m-d')]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Tax Profile <span style="color: red;">*</span></label>
            <div class="col-lg-10">
                <?= 
                    $form->field($model, 'country_code')->dropDownList($profile, [
                        'name' => 'country_code[]',
                        'class' => 'form-control list m-datatable__pager-size',
                        'multiple size' => '12',
                        'data-target' => 'available'

                    ])->label(false);
                ?> 
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Advance Search</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'adv_search')->widget(SwitchInput::classname(), [
                    'value' => (isset($model->adv_search) && ($model->adv_search == 1) ? true : false),
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ],
                    'options' => [
                        'onchange' => "if ($(this).is(':checked')) {
                                            $('#adv_opt').show();
                                        } else {
                                            $('#adv_opt').hide();
                                        }"
                    ]
                ])->label(false);
                ?>
                <span class="m-form__help">Checkout IP, Mobile Dial Code, or Billing Address match with Tax Profile country</span>
            </div>
        </div>
        <div class="form-group m-form__group row" id="adv_opt" style="display: <?= (isset($model->adv_search) && ($model->adv_search == 1) ? "display" : "none"); ?>">
            <label class="col-lg-2 col-form-label">Email Postfix</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'adv_email_postfix')->textInput(['placeholder' => 'sg'])->label(false); ?>
            </div>
        </div>

        <div class="m-separator m-separator--dashed m-separator--lg"></div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Recipient <span style="color: red;">*</span></label>
            <div class="col-lg-7">
                <?= $form->field($model, 'report_recipient')->textInput(['placeholder' => 'use comma separator for more recipient'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" id="submit_btn" name="submit_btn" class="btn btn-warning" value="export"><i class="fa fa-download"></i> Export</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>