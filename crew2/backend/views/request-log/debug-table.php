<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;
use kartik\daterange\DateRangePicker;
use kartik\datetime\DateTimePickerAsset;
use yii\bootstrap\ActiveForm;

$this->title = 'Debug Log';

$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = 'Log';
$this->params['breadcrumbs'][] = 'Debug';
$this->params['layoutContent'] = 'block';

\backend\assets\DataTableAsset::register($this);
\backend\assets\CodePrettifyAsset::register($this);
DateTimePickerAsset::register($this);
\backend\assets\AppAsset::register($this);

Modal::begin([
    'title' => '',
    'id' => 'generalModal',
    'size' => Modal::SIZE_LARGE
]);

Modal::end();

$form = ActiveForm::begin([
            'action' => ['#'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
        ]);
?>

<div class="m-portlet m-portlet--primary m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Date</label>
            <div class="col-lg-4">
                <div class="input-group drp-container">
                    <?php
                    $addon = <<< HTML
<div class="input-group-append">
    <span class="input-group-text">
        <i class="fas fa-calendar-alt"></i>
    </span>
</div>
HTML;

                    echo $addon . DateRangePicker::widget([
                        'id' => 'datepicker1',
                        'name' => 'kvdate',
                        'convertFormat' => true,
                        'startAttribute' => 'from_date',
                        'endAttribute' => 'to_date',
                        'pluginOptions' => [
                            'locale' => ['format' => 'Y-m-d'],
                        ],
                    ]);
                    ?>
                </div>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="button" id="search" name="search" class="btn btn-primary" onclick="query();">Search</button>
                <button type="reset" class="btn btn-default">Reset</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__body">
        <table class="table table-striped- table-bordered table-hover table-checkable" id="m_table_1">
            <thead>
                <tr>
                    <th>App</th>
                    <th>Tag</th>
                    <th>Created At</th>
                    <th>Action</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<?php
$this->registerJs(<<<JS
function query () {
  let url = '/request-log/get-debug-log'
  let column = [
    {data: 'app'},
    {data: 'tag'},
    {
      data: {
        _: 'date',
        filter: 'created_at',
        sort: 'created_at'
      },
    },
    {data: 'dev_debug_log_id'},
  ];
  let columnDef = [
    {
      targets: -1,
      title: 'Actions',
      orderable: false,
      render: function (data, type, full, meta) {
        return `
                        <a href="javascript:void(0)" onclick='viewLogDetail("view-debug-log","` + data + `","` + full.path + `")' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" title="View">
                          <i class="la la-search"></i>
                        </a>
                        <a href="/request-log/view-debug-log?id=` + data + `&json=true` + (full.path ? '&s3_path=' + full.path : '') + `" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" title="View">
                          <i class="la la-save"></i>
                        </a>
                        `
      },
    },
  ];
  let order = [[2, 'desc']];
  let initComplete = function () {
    let table = $('#m_table_1').DataTable()
    let rowFilter = $('<tr class="filter"></tr>').appendTo($(table.table().header()))
    this.api().columns().every(function () {
      var column = this
      var input
      switch (column.title()) {
        case 'Tag':
        case 'App':
            input = $(`<select class="form-control form-control-sm form-filter m-input" title="Select" data-col-index="` + column.index() + `">
                        <option value="">Select</option></select>`);
            column.data().unique().sort().each(function(d, j) {
                $(input).append('<option value="' + d + '">' + d + '</option>');
            });
            break;
        case 'Created At':
          input = $(`
							<div class="input-group date">
								<input type="text" class="form-control form-control-sm m-input" readonly placeholder="From" id="m_datepicker_1"
								 data-col-index="` + column.index() + `" data-custom="true" />
								<div class="input-group-append">
									<span class="input-group-text"><i class="la la-calendar-o glyphicon-th"></i></span>
								</div>
							</div>`)
          break
        case 'Actions':
          var search = $(`<button class="btn btn-brand m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-search"></i>
							    <span>Search</span>
							  </span>
							</button>`)
          var reset = $(`<button class="btn btn-secondary m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-close"></i>
							    <span>Reset</span>
							  </span>
							</button>`)
          $('<th>').append(search).append(reset).appendTo(rowFilter)
          $(search).on('click', function (e) {
            e.preventDefault()
            var params = {}
            $(rowFilter).find('.m-input').each(function () {
              var i = $(this).data('col-index')
              if ($(this).data('custom') != true) {
                table.column(i).search($(this).val() ? $(this).val() : '', false, false)
              }
            })
            if($("#m_datepicker_1").val()){
                $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
                  var filter_date = $('#m_datepicker_1').val().split(' - ')
                  if (filter_date) {
                    return (data[2] > moment(filter_date[0]).format('X') && data[2] < moment(filter_date[1]).format('X'))
                  }
                  return true
                })
            }
            table.table().draw()
          })
          $(reset).on('click', function (e) {
            e.preventDefault()
            $(rowFilter).find('.m-input').each(function (i) {
              $(this).val('')
              table.column($(this).data('col-index')).search('', false, false)
            })
            table.table().draw()
          })
          break
      }
      if (input) {
        $(input).appendTo($('<th>').appendTo(rowFilter))
      }
    })
    jQuery('#m_datepicker_1').
      daterangepicker({
        'timePicker': true,
        'timePickerIncrement': 15,
        'locale': {'format': 'YYYY-MM-DD hh:mm A'},
        'cancelButtonClasses': 'btn-secondary',
        'autoUpdateInput': true,
      }, function (start, end, label) {
        var val = start.format('YYYY-MM-DD hh:mm A') + ' - ' + end.format('YYYY-MM-DD hh:mm A')
      }).val('');
    $('#m_form_status, #m_form_type').selectpicker();
  };
  DatatablesDataSourceAjaxServer.init(url,{}, column, columnDef, order, initComplete);
}

jQuery(document).ready(function () {
  query();
});
JS
        , \yii\web\View::POS_END);
