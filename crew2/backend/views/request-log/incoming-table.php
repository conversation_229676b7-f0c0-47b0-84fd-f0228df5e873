<?php

use offgamers\base\models\IncomingRequestLog;
use yii\helpers\Html;
use yii\bootstrap4\Modal;
use kartik\datetime\DateTimePickerAsset;
use yii\grid\GridView;

$this->title = 'Incoming Request Log';

$this->params['breadcrumbs'][] = 'Administration';
$this->params['breadcrumbs'][] = 'Log';
$this->params['breadcrumbs'][] = 'Incoming Request';
$this->params['layoutContent'] = 'block';

\backend\assets\CodePrettifyAsset::register($this);
DateTimePickerAsset::register($this);
\backend\assets\AppAsset::register($this);
?>
<div class="table-log-modal">
    <?php
        Modal::begin([
            'title' => '',
            'id' => 'generalModal',
            'size' => Modal::SIZE_LARGE
        ]);

        Modal::end();
    ?>
</div>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__body table-log-content">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'request_url',
                'tag',
                [
                    'attribute' => 'created_at',
                    'filterInputOptions' => [
                        'class' => 'form-control m-input',
                        'id' => 'm_datepicker_1',
                        'readonly' => true
                    ],
                    'content' => function ($model) {
                        return date('Y-m-d H:i:s A', $model->created_at);
                    }
                ],
                [
                    'attribute' => 'status_code',
                    'filterInputOptions' => [
                        'class' => 'form-control m-input',
                        'name' => $searchModel->formName() . '[status_code_filter]',
                        'value' => $searchModel->status_code_filter
                    ],
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => mdm\admin\components\Helper::filterActionColumn('{view-incoming-log}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'view-incoming-log' => function ($url, $model, $key) {
                            return
                                    Html::a('<i class="fa fa-eye"></i>', 'javascript:void(0)', [
                                        'onclick' => 'viewLogDetail("view-incoming-log","' . $model['incoming_request_log_id'] . '","' . IncomingRequestLog::getS3Path($model) . '")',
                                        'class' => 'm-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill'
                                    ]) .
                                    Html::a('<i class="la la-save"></i>', \yii\helpers\Url::to([
                                                'view-incoming-log',
                                                'id' => $model['incoming_request_log_id'],
                                                'json' => 'true',
                                                's3_path' => IncomingRequestLog::getS3Path($model)
                                            ]), ['class' => 'm-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill']
                            );
                        },
                    ]
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>

<?php
$this->registerJs(<<<JS
    jQuery("#m_datepicker_1").daterangepicker({"timePicker":true,"timePickerIncrement":15,"locale":{"format":"YYYY-MM-DD hh:mm A"},"cancelButtonClasses":"btn-secondary","autoUpdateInput":false})
    .on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD hh:mm A') + ' - ' + picker.endDate.format('YYYY-MM-DD hh:mm A'));
      $(this).trigger('change');
    }).on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
      $(this).trigger('change');
    });

JS
        , \yii\web\View::POS_READY);
