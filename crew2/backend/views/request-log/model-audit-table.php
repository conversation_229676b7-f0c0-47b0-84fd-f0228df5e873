<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap4\Modal;
use kartik\daterange\DateRangePicker;
use kartik\datetime\DateTimePickerAsset;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Model Audit History Log';
$this->params['breadcrumbs'][] = $this->title;
\backend\assets\DataTableAsset::register($this);
\backend\assets\CodePrettifyAsset::register($this);
DateTimePickerAsset::register($this);
\backend\assets\AppAsset::register($this);

Modal::begin([
    'title' => '',
    'id' => 'generalModal',
    'size' => Modal::SIZE_LARGE
]);

Modal::end();

?>
    <div class="game-product-attribute-index">
        <div class="m-form m-form--label-align-right m--margin-top-20 m--margin-bottom-30">
            <div class="row align-items-center">
                <div class="col-md-4 order-2 order-xl-1">
                    <div class="form-group m-form__group row align-items-center">
                        <div class="col-md-12">
                            <div class="m-form__group m-form__group--inline">
                                <div class="m-form__label">
                                    <label>Date:</label>
                                </div>
                                <div class="m-form__control">
                                    <?php
                                    echo '<div class="input-group drp-container">';
                                    echo DateRangePicker::widget([
                                        'id' => 'datepicker1',
                                        'name' => 'kvdate',
                                        'convertFormat' => true,
                                        'startAttribute' => 'from_date',
                                        'endAttribute' => 'to_date',
                                        'pluginOptions' => [
                                            'locale' => ['format' => 'Y-m-d'],
                                        ]
                                    ]);
                                    echo '</div>';

                                    ?>
                                </div>
                            </div>
                            <div class="d-md-none m--margin-bottom-10"></div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 order-1 order-xl-2">
                    <a href="javascript:void(0)" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill" onclick="query();">
                    <span>
                        <span>Search</span>
                    </span>
                    </a>
                    <div class="m-separator m-separator--dashed d-xl-none"></div>
                </div>
            </div>
        </div>
        <table class="table table-striped- table-bordered table-hover table-checkable" id="m_table_1">
            <thead>
            <tr>
                <th>Type</th>
                <th>Description</th>
                <th>Updated By</th>
                <th>Created At</th>
                <th>Action</th>
            </tr>
            </thead>
        </table>

    </div>


<?php

$this->registerJs(<<<JS

function query () {
  let url = '/request-log/get-model-audit-log?table=$table&id=$id';
  let column = [
      {data: 'type'},
      {data: 'description'},
      {data: 'updated_by'},
      {
        data: {
          _: 'date',
          filter: 'created_at',
          sort: 'created_at'
        },
      },
      {data: 'model_audit_history_log_id'},
    ];
    let columnDef = [
    {
      targets: -1,
      title: 'Actions',
      orderable: false,
      render: function (data, type, full, meta) {
        return `
                        <a href="javascript:void(0)" onclick='viewLogDetail("view-model-audit-log","` + data + `","` + full.path + `")' class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" title="View">
                          <i class="la la-search"></i>
                        </a>
                        <a href="/request-log/view-model-audit-log?id=` + data + `&json=true` + (full.path ? '&s3_path=' + full.path : '') + `" class="m-portlet__nav-link btn m-btn m-btn--hover-brand m-btn--icon m-btn--icon-only m-btn--pill" title="View">
                          <i class="la la-save"></i>
                        </a>
                        `
      },
    },
  ];
    let order = [[3, 'desc']];
    let initComplete = function () {
    let table = $('#m_table_1').DataTable();
    var rowFilter = $('<tr class="filter"></tr>').appendTo($(table.table().header()))

    this.api().columns().every(function () {
      var column = this
      var input
      switch (column.title()) {
        case 'Description':
          input = $(`<input type="text" class="form-control form-control-sm form-filter m-input" data-col-index="` + column.index() + `"/>`)
          break;
          
        case 'Updated By':
        case 'Type':
            input = $(`<select class="form-control form-control-sm form-filter m-input" title="Select" data-col-index="` + column.index() + `">
                        <option value="">Select</option></select>`);
            column.data().unique().sort().each(function(d, j) {
                $(input).append('<option value="' + d + '">' + d + '</option>');
            });
        break;
							
        case 'Created At':
          input = $(`
							<div class="input-group date">
								<input type="text" class="form-control form-control-sm m-input" readonly placeholder="From" id="m_datepicker_1"
								 data-col-index="` + column.index() + `" data-custom="true" />
								<div class="input-group-append">
									<span class="input-group-text"><i class="la la-calendar-o glyphicon-th"></i></span>
								</div>
							</div>`)
          break
        case 'Actions':
          var search = $(`<button class="btn btn-brand m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-search"></i>
							    <span>Search</span>
							  </span>
							</button>`)

          var reset = $(`<button class="btn btn-secondary m-btn btn-sm m-btn--icon">
							  <span>
							    <i class="la la-close"></i>
							    <span>Reset</span>
							  </span>
							</button>`)

          $('<th>').append(search).append(reset).appendTo(rowFilter)

          $(search).on('click', function (e) {
            e.preventDefault()
            var params = {}
            $(rowFilter).find('.m-input').each(function () {
              var i = $(this).data('col-index')
              if ($(this).data('custom') != true) {
                table.column(i).search($(this).val() ? $(this).val() : '', false, false)
              }

            })
            if($("#m_datepicker_1").val()){
                $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
                  var filter_date = $('#m_datepicker_1').val().split(' - ')
                  if (filter_date) {
                    return (data[2] > moment(filter_date[3]).format('X') && data[3] < moment(filter_date[1]).format('X'))
                  }
                  return true
                })
            }
            table.table().draw()
          })

          $(reset).on('click', function (e) {
            e.preventDefault()
            $(rowFilter).find('.m-input').each(function (i) {
              $(this).val('')
              table.column($(this).data('col-index')).search('', false, false)
            })
            table.table().draw()
          })
          break
      }
      if (input) {
        $(input).appendTo($('<th>').appendTo(rowFilter))
      }
    })
    jQuery('#m_datepicker_1').
      daterangepicker({
        'timePicker': true,
        'timePickerIncrement': 15,
        'locale': {'format': 'YYYY-MM-DD hh:mm A'},
        'cancelButtonClasses': 'btn-secondary',
        'autoUpdateInput': true,
      }, function (start, end, label) {
        var val = start.format('YYYY-MM-DD hh:mm A') + ' - ' + end.format('YYYY-MM-DD hh:mm A');
        jQuery('#w6').val(val).trigger('change');
      }).val('');

  }
  DatatablesDataSourceAjaxServer.init(url, {}, column, columnDef, order, initComplete)
}

jQuery(document).ready(function () {
  query();
});

JS
    , \yii\web\View::POS_END);




