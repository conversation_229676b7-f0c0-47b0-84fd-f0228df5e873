<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
if(!empty($data))
{
?>
<div>

    <?= DetailView::widget([
            'model' => $data,
            'attributes' => [
                'incoming_request_log_id',
                'app',
                'log_level',
                'source_ip',
                'requester',
                'request_url',
                'request_method',
                'tag',
                'status_code',
                [
                    'label' => 'Request Header',
                    'format' => 'text',
                    'value' => '',
                    'contentOptions' => [
                        'class' => 'prettyprint2',
                        'data-raw' => (!empty($data['request_header']) ? $data['request_header'] : '')
                    ],
                ],
                [
                    'label' => 'Request Data',
                    'format' => 'text',
                    'value' => '',
                    'contentOptions' => [
                        'class' => 'prettyprint2',
                        'data-raw' => (!empty($data['request_data']) ? $data['request_data'] : '')
                    ],
                ],
                [
                    'label' => 'Response Body',
                    'format' => 'text',
                    'value' => '',
                    'contentOptions' => [
                        'class' => 'prettyprint2',
                        'data-raw' => (!empty($data['response_body']) ? $data['response_body'] : '')
                    ],
                ],
                'time_consume',
                [
                    'attribute' => 'created_at',
                    'format' => 'text',
                    'value' => isset($data['created_at'])?date('d/m/Y h:i:s A',$data['created_at']):'N/A'
                ],
                [
                    'attribute' => 'updated_at',
                    'format' => 'text',
                    'value' => isset($data['updated_at'])?date('d/m/Y h:i:s A',$data['updated_at']):"N/A"
                ],
            ],
        ]) ?>

</div>
<?php 
}
?>