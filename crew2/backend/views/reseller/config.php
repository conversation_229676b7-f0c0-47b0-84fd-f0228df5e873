<?php

use kartik\widgets\SwitchInput;
use yii\helpers\Html;

$odd = true;
foreach ($configs as $config) {
    if ($odd) {
        ?>
        <div class="form-group m-form__group row">
        <?php } ?>
        <label class="col-lg-2 col-form-label"><?= $config['label']; ?></label>
        <div class="col-lg-4">
            <?php
            switch ($config['type']) {
                case 'text':
                    echo Html::textInput('SETTING[' . $config['key'] . ']', (!empty($data[$config['key']]) ? $data[$config['key']] : ''), $config['option']);
                    break;
                case 'textarea':
                    echo Html::textarea('SETTING[' . $config['key'] . ']', (!empty($data[$config['key']]) ? $data[$config['key']] : ''), $config['option']);
                    break;
                case 'select':
                    echo Html::dropDownList('SETTING[' . $config['key'] . ']', (!empty($data[$config['key']]) ? $data[$config['key']] : ''), $config['items'], $config['option']);
                    break;
            }
            ?>
        </div>
        <?php if (!$odd) { ?>
        </div>
        <?php
        $odd = true;
    } else {
        $odd = false;
    }
}
if (!$odd) {
    ?>
    </div>
    <?php
}
?>
