<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;
use mdm\admin\components\Helper;

$this->title = 'Reseller';

$this->params['breadcrumbs'][] = 'Product';
$this->params['breadcrumbs'][] = 'Reseller';
$this->params['layoutContent'] = 'block';

$status_list = (Yii::$app->enum->getStatus('listData'));
?>

<div class="game-reseller-index m-portlet m-portlet--mobile">

    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('reseller/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{summary}\n{items}",
            'columns' => [
                'reseller_id',
                'title',
                'profile',
                [
                    'label' => 'Status',
                    'filter' => Html::activeDropDownList($searchModel, 'status', $status_list, ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']),
                    'content' => function ($model) use ($status_list) {
                        return $status_list[$model->status];
                    }
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => \mdm\admin\components\Helper::filterActionColumn('{update} {option}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'option' => function ($url, $model, $key) {
                            $className = $model::RESELLER_LIST[$model->profile];
                            if (class_exists($className)) {
                                $class = new $className;
                                $class->reseller = $model->reseller_id;
                                return $class->renderColumn($url, $model, $key);
                            }
                            return '';
                        },
                    ]
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>