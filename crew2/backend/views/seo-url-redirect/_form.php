<?php

use yii\helpers\Url;
use yii\widgets\ActiveForm;


$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
            ],
            'enableAjaxValidation' => true
        ]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Old URL</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'old_url')->textarea(['rows' => 3, 'class' => 'form-control', 'placeholder' => 'Relative path e.g path/to/url'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">New URL</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'new_url')->textarea(['rows' => 3, 'class' => 'form-control', 'placeholder' => 'Relative path e.g path/to/url'])->label(false); ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Redirect Type</label>
        <div class="col-lg-4">
            <?php
                echo $form->field($model, 'redirect_type')->dropDownList($model->getRedirectionTypes(), [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
            ?>
        </div>
    </div>

</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('seo-url-redirect/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>