<?php

use backend\widgets\MetronicPager;
use common\models\SeoUrlRedirect;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\helpers\Html;


$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = $this->title = 'SEO URL Redirect';
$this->params['layoutContent'] = 'block';

$redirectionTypes = (new SeoUrlRedirect())->getRedirectionTypes();
?>


<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title"></div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('seo-url-redirect/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>

    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <div class="m-section__content">
                <?=
                GridView::widget([
                    'dataProvider' => $dataProvider,
                    'filterModel' => $searchModel,
                    'layout' => "{summary}\n{items}",
                    'columns' => [
                        ['class' => 'yii\grid\SerialColumn'],
                        [
                            'header' => 'Old URL',
                            'attribute' => 'old_url',
                            'contentOptions' => ['style' => 'width: 50%;word-wrap: break-word;'],
                        ],
                        [
                            'header' => 'New URL',
                            'attribute' => 'new_url',
                            'contentOptions' => ['style' => 'width: 50%;word-wrap: break-word;'],
                        ],
                        [
                            'header' => 'Redirect Type',
                            'attribute' => 'redirect_type',
                            'filter' => Html::activeDropDownList(
                                    $searchModel,
                                    'redirect_type',
                                    $redirectionTypes,
                                    ['prompt' => 'Select', 'class' => 'selectpicker m-datatable__pager-size']),
                            'content' => function ($model) use ($redirectionTypes) {
                                return $redirectionTypes[$model->redirect_type];
                            }
                        ],
                        [
                            'header' => 'Actions',
                            'class' => 'yii\grid\ActionColumn',
                            'template' => mdm\admin\components\Helper::filterActionColumn('{update} {delete}'),
                            'contentOptions' => ['style' => 'white-space: nowrap;'],
                        ],
                    ],
                ]);
                ?>
                <?= MetronicPager::renderPager($dataProvider); ?>
            </div>
        </div>
    </div>
</div>