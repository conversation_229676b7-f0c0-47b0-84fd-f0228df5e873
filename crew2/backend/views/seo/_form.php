<?php

use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;

$form = ActiveForm::begin([
    'options' => [
        'class' => 'm-form m-form--label-align-right',
    ],
]);

$language = Yii::$app->enum->getLanguage('listData');
?>

<div class="m-portlet__body">
    <div class="m-portlet m-portlet--tabs">
        <div class="m-portlet__head">
            <div class="m-portlet__head-tools">
                <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                    <?php foreach ($language as $i => $v) { ?>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                <?= $v; ?>
                            </a>
                        </li>
                    <?php } ?>
                </ul>
            </div>
        </div>
        <div class="m-portlet__body">
            <div class="tab-content">
                <?php
                $option = ['class' => 'form-control'];
                $value = (!empty($data['description']) ? $data['description'] : []);
                $trans_model = new \common\models\CustomSeoTranslation();
                foreach ($language as $i => $v) {
                    if (!empty($data[$i])) {
                        $trans_model->load($data[$i], '');
                    }
                    ?>
                    <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">

                        <div class="form-group m-form__group row">
                            <label class="col-lg-3 col-form-label">Meta Title <?= ($i == 1 ? '*' : '') ?></label>
                            <div class="col-lg-9">
                                <?= $form->field($trans_model, "[$i]meta_title")->textInput()->label(false); ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-3 col-form-label">Meta Keyword</label>
                            <div class="col-lg-9">
                                <?= $form->field($trans_model, "[$i]meta_keyword")->textarea(['rows' => 6])->label(false); ?>
                            </div>
                        </div>
                        <div class="form-group m-form__group row">
                            <label class="col-lg-3 col-form-label">Meta Description</label>
                            <div class="col-lg-9">
                                <?= $form->field($trans_model, "[$i]meta_description")->textarea(['rows' => 6])->label(false); ?>
                            </div>
                        </div>

                        <div class="form-group m-form__group row">
                            <label class="col-lg-3 col-form-label">Status</label>
                            <div class="col-lg-4">
                                <?=
                                        $form->field($trans_model, "[$i]status")->widget(SwitchInput::classname(), [
                                            'value' => (isset($trans_model->status) && $trans_model->status == 1) ? true : false,
                                            'pluginOptions' => [
                                                'onText' => '<i class="fa fa-check"></i>',
                                                'offText' => '<i class="fa fa-times"></i>',
                                            ]
                                        ])->label(false);
                                    ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<?= $form->field($model, "type")->hiddenInput()->label(false); ?>
<?= $form->field($model, 'id')->hiddenInput()->label(false); ?>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>