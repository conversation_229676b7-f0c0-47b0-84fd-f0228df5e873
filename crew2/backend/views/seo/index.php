<?php

use yii\helpers\Html;

$this->title = 'SEO';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = 'SEO';
$this->params['layoutContent'] = 'block';

\backend\assets\DualListBoxAsset::register($this);
?>
<div class="game-product-attribute-index">

    <div class="m-portlet m-portlet--mobile">
        <div class="m-portlet__body">
            <div class="m-section">
                <!--begin: Datatable -->
                <div class="m-section__content">
                    <?php
                    echo \yii\grid\GridView::widget([
                        'dataProvider' => $provider,
                        'summary' => 'Showing <b>' . ($filter->startAt) . '-' . ($filter->endAt) . '</b> of <b>' . $filter->totalCount . '</b> items.',
                        'columns' => [
                            [
                                'header' => '#',
                                'content' => function ($model, $key, $index) use ($filter) {
                                    return $index + $filter->startAt;
                                },
                            ],
                            [
                                'attribute' => 'type'
                            ],
                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => mdm\admin\components\Helper::filterActionColumn('{update}'),
                                'contentOptions' => ['style' => 'white-space: nowrap;'],
                                'buttons' => [
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<span class="glyphicon glyphicon-pencil"></span>',
                                            \yii\helpers\Url::to([
                                                'seo/update',
                                                'id' => $model['reference_data_id'],
                                                'type' => $model['type']
                                            ])
                                        );
                                    }
                                ]
                            ],
                        ],
                    ]);

                    echo $filter->renderPageSizer();

                    ?>
                </div>
            </div>
        </div>
    </div>
</div>