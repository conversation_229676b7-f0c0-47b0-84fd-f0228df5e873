<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ActiveForm;

$this->title = 'My Profile';

$this->params['breadcrumbs'][] = 'My Profile';
$this->params['layoutContent'] = 'block';
?>


<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Change Password
                </h3>
            </div>
        </div>
    </div>

    <?php
    $form = ActiveForm::begin([
                'id' => 'form-change',
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ]
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Old Password</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'oldPassword')->passwordInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">New Password</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'newPassword')->passwordInput()->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Re-Type Password</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'retypePassword')->passwordInput()->label(false); ?>
            </div>
        </div>
    </div>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('index'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>