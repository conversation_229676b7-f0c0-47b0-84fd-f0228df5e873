<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$language = Yii::$app->enum->getLanguage('listData');

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
        ]]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Type</label>
        <div class="col-lg-10">
            <?=
                    $form->field($model, 'static_page_type_id')->dropDownList($model->getStaticPageType(), [
                        'prompt' => "Select",
                        'class' => 'selectpicker m-datatable__pager-size',
                        'required' => true
                    ])
                    ->label(false);
            ?>
        </div>
    </div>

    <div class="form-group m-form__group row">
        <div class="col-lg-12">
            <div class="m-portlet m-portlet--tabs">
                <div class="m-portlet__head">
                    <div class="m-portlet__head-tools">
                        <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                            <?php foreach ($language as $i => $v) { ?>
                                <li class="nav-item m-tabs__item">
                                    <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                        <?= $v; ?>
                                    </a>
                                </li>
                            <?php } ?>
                        </ul>
                    </div>
                </div>
                <div class="m-portlet__body">
                    <div class="tab-content">
                        <?php
                        $option = ['class' => 'form-control'];
                        $value = (!empty($data['description']) ? $data['description'] : []);
                        $trans_model = new \common\models\CustomSeoTranslation();
                        foreach ($language as $i => $v) {

                            $contentModel = new \common\models\StaticPageContent();

                            if (isset($model['content'][$i])) {
                                $contentModel->load($model['staticPageContent'][$i], '');
                                $contentModel->static_page_id = $model->staticPageId;
                            }

                            $contentModel->language_id = $i;
                            ?>
                            <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                                <div class="form-group m-form__group row">
                                    <label class="col-lg-2 col-form-label">Title</label>
                                    <div class="col-lg-10">
                                        <?= $form->field($contentModel, '[' . $i . ']title')->textInput(['required' => ($i == 1 ? true : false)])->label(false); ?>
                                    </div>
                                </div>

                                <div class="form-group m-form__group row">
                                    <label class="col-lg-2 col-form-label">Content</label>
                                    <div class="col-lg-10">
                                        <?= $form->field($contentModel, '[' . $i . ']content')->textarea(['rows' => 6, 'class' => 'summernote'])->label(false); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success" onclick="checkInputContent();">Save</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('static-page/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<style>
    .note-toolbar {
        z-index: 50;
    }
</style>

<script type="text/javascript">
    function checkInputContent() {
        $('input:invalid').each(function () {
            // Find the tab-pane that this element is inside, and get the id
            var test = $(this).closest('.tab-pane');
            var id = test.attr('id');
            // Find the link that corresponds to the pane and have it show
            $('.nav a[href="#' + id + '"]').tab('show');
            // Only want to do it once
            return false;
        });
    }
</script>

<?php
$this->registerJs(<<<JS
var SummernoteDemo={init:function(){
  $('.summernote').summernote({
  height:350,
  toolbar: [
    ['style', ['bold', 'italic', 'underline', 'clear']],
    ['font', ['strikethrough', 'superscript', 'subscript']],
    ['fontsize', ['fontsize']],
    ['color', ['color']],
    ['para', ['ul', 'ol', 'paragraph']],
    ['codeview']
  ]
  })}};

SummernoteDemo.init();
JS
        , \yii\web\View::POS_READY);
