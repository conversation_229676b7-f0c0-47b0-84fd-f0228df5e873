<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;

$this->title = 'Static Page';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = 'Static Page';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('static-page/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                    &nbsp;
                    <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" m-dropdown-toggle="hover" aria-expanded="true">
                        <a href="#" class="m-portlet__nav-link btn btn-lg btn-secondary  m-btn m-btn--icon m-btn--icon-only m-btn--pill  m-dropdown__toggle">
                            <i class="la la-ellipsis-h m--font-brand"></i>
                        </a>
                        <div class="m-dropdown__wrapper">
                            <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                            <div class="m-dropdown__inner">
                                <div class="m-dropdown__body">
                                    <div class="m-dropdown__content">
                                        <ul class="m-nav">
                                            <li class="m-nav__section m-nav__section--first">
                                                <span class="m-nav__section-text"><?= Yii::t('store-credit', 'Quick Actions') ?></span>
                                            </li>
                                            <li class="m-nav__item">
                                                <a href="<?= Url::toRoute('static-page/static-page-type'); ?>" class="m-nav__link">
                                                    <i class="m-nav__link-icon la la-plus"></i>
                                                    <span class="m-nav__link-text">Page Type</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <!--begin: Datatable -->
            <?=
            GridView::widget([
                'dataProvider' => $dataProvider,
                'layout' => '{summary} {items}',
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    'title',
                    [
                        'header' => 'Updated By',
                        'attribute' => 'changed_by',
                        'value' => function($model) {
                            return $model->getLastEdit($model->static_page_id)['by'];
                        },
                        'contentOptions' => ['style' => 'width: 20%;'],
                    ],
                    [
                        'header' => 'Last Update',
                        'attribute' => 'updated_at',
                        'value' => function($model) {
                            return $model->getLastEdit($model->static_page_id)['date'];
                        },
                        'contentOptions' => ['style' => 'width: 15%;'],
                    ],
                    [
                        'header' => 'Action',
                        'class' => 'yii\grid\ActionColumn',
                        'template' => mdm\admin\components\Helper::filterActionColumn('{update} {delete}'),
                        'contentOptions' => ['style' => 'white-space: nowrap;'],
                    ],
                ]
            ]);
            ?>
            <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
        </div>
    </div>
</div>
