<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\SwitchInput;

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
        ]]);
?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Title</label>
        <div class="col-lg-10">
            <?= $form->field($model, 'type_title')->textInput(['maxlength' => true])->label(false); ?>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Code</label>
        <div class="col-lg-4">
            <?= $form->field($model, 'type_code')->textInput(['maxlength' => true, ($model->isNewRecord ? '' : 'disabled') => ($model->isNewRecord ? '' : 'disabled')])->label(false); ?>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <label class="col-lg-2 col-form-label">Mandatory</label>
        <div class="col-lg-10">
            <?=
            $form->field($model, 'type_status')->widget(SwitchInput::classname(), [
                'value' => (isset($model->type_status) && $model->type_status == '1') ? true : false,
                'pluginOptions' => [
                    'onText' => '<i class="fa fa-check"></i>',
                    'offText' => '<i class="fa fa-times"></i>',
                ]
            ])->label(false);
            ?>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success" onclick="checkInputContent();">Save</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('static-page/static-page-type'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>