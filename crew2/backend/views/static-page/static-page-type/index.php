<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\Pjax;

$this->title = 'Static Page Type';

$this->params['breadcrumbs'][] = 'E-Commerce';
$this->params['breadcrumbs'][] = ['label' => 'Static Page', 'url' => ['static-page/index']];
$this->params['breadcrumbs'][] = 'Static Page Type';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('static-page/create-type'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <?=
            GridView::widget([
                'dataProvider' => $dataProvider,
                'layout' => '{summary} {items}',
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    [
                        'header' => 'Type',
                        'attribute' => 'type_title',
                    ],
                    [
                        'header' => 'Mandatory',
                        'attribute' => 'type_status',
                        'value' => function($model) {
                            return ($model['type_status'] == 1) ? 'Yes' : 'No';
                        },
                        'contentOptions' => ['style' => 'width: 15%;'],
                    ],
                    [
                        'header' => 'Updated By',
                        'attribute' => 'changed_by',
                        'contentOptions' => ['style' => 'width: 20%;'],
                    ],
                    [
                        'header' => 'Last Update',
                        'attribute' => 'updated_at',
                        'format' => 'datetime',
                        'contentOptions' => ['style' => 'width: 15%;'],
                    ],
                    [
                        'header' => 'Action',
                        'class' => 'yii\grid\ActionColumn',
                        'template' => mdm\admin\components\Helper::filterActionColumn('{static-page/update} {static-page/delete}'),
                        'contentOptions' => ['style' => 'white-space: nowrap;'],
                        'buttons' => [
                            'static-page/update' => function ($url, $model, $key) {
                                return Html::a('<span class="glyphicon glyphicon-pencil"></span>', Url::to([
                                                    'static-page/update-type',
                                                    'id' => $model['static_page_type_id']
                                                ])
                                );
                            },
                            'static-page/delete' => function ($url, $model, $key) {
                                if ($model['changed_by'] == "system") {
                                    return false;
                                } else {
                                    return Html::a('<span class="glyphicon glyphicon-trash"></span>', Url::to([
                                                        'static-page/delete-type',
                                                        'id' => $model['static_page_type_id']
                                                    ]), ['title' => 'Delete', 'aria-label' => 'Delete', 'data-pjax' => '0', 'data-confirm' => 'Are you sure you want to delete this item?', 'data-method' => 'post']
                                    );
                                }
                            }
                        ],
                    ],
                ],
            ]);
            ?>
            <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
        </div>
    </div>
</div>
