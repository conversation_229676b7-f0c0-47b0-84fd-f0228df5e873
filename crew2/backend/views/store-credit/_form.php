<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$this->params['layoutContent'] = 'block';

// Get currency list
$currencyArray = Yii::$app->currency->getCurrencyList();

switch ($form_type) {
    case 'add':
        $amountText = Yii::t('store-credit', 'Add Amount : ');
        $currencySets = '+ ' . $model->currency;
        $submitButton = Yii::t('store-credit', 'Add');
        $model->amount = '';
        break;
    case 'deduct':
        $amountText = Yii::t('store-credit', 'Deduct Amount : ');
        $currencySets = '- ' . $model->currency;
        $submitButton = Yii::t('store-credit', 'Deduct');
        $model->amount = '';
        break;
    case 'convert':
        // remove current currency from list
        unset($currencyArray[$model->currency]);
        $submitButton = Yii::t('store-credit', 'Convert');
        break;
}

$form = ActiveForm::begin([
    'id' => 'store-credit-form',
    'class' => 'form-horizontal',
    'action' => ['/store-credit/create'],
    'options' => [
        'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
    ],
    'enableAjaxValidation' => true,
    'validationUrl' => Url::toRoute('store-credit/validation'),
]);

echo Html::hiddenInput('StoreCreditForm[transaction_type]', $model->transaction_type);
echo Html::hiddenInput('StoreCreditForm[customers_id]', $model->customers_id);
echo Html::hiddenInput('StoreCreditForm[currency]', $model->currency);
echo Html::hiddenInput('StoreCreditForm[start_date]', $model->start_date);
echo Html::hiddenInput('StoreCreditForm[end_date]', $model->end_date);

?>

<div class="m-portlet__body">
    <div class="form-group m-form__group row">
        <?php
            // Store Credit Conversion
        if ($form_type == 'convert') {
            echo '<label class="col-lg-2 text-left col-form-label">' . Yii::t('store-credit', 'Current Blanace Amount : ') . '</label>';
            echo '<label class="col-lg-3 text-right col-form-label">';
            echo $model->currency;
            echo '</label><div class="col-lg-7 col-form-label">';
            echo Html::hiddenInput('StoreCreditForm[amount]', $model->amount);
            echo Yii::$app->currency->format($model->currency, $model->amount) . '</div>';
            echo '<label class="col-lg-2 text-left col-form-label">' . Yii::t('store-credit', 'Convert Amount to : ') . '</label>';
            echo '<label class="col-lg-3 text-right">';
            echo $form->field($model, 'to_currency')->dropDownList($currencyArray, ['prompt'=>'Select...'])->label(false);
            echo '</label><div class="col-lg-7">';
            echo $form->field($model, 'to_amount')->textInput(['id' => 'to_amount', 'disabled' => true])->label(false) . '</div>';
            echo '<label class="col-lg-2 text-left col-form-label">' . Yii::t('store-credit', 'Conversion Rate : ') . '</label>';
            echo '<label class="col-lg-3 text-right">';
            echo '</label><div class="col-lg-7">';
            echo $form->field($model, 'conversion_rate')->textInput(['id' => 'conversion_rate'])->label(false) . '</div>';
        } else {
            // Add/Deduct Store Credit
            echo '<label class="col-lg-2 text-left col-form-label">' . $amountText . '</label>';
            echo '<label class="col-lg-3 text-right">';
            if (!$model->currency) {
                echo '<div class="row"><div class="col-lg-2 text-right">';
                echo $currencySets;
                echo '</div>';
                echo '<div class="col-lg-10 text-right">';
                echo $form->field($model, 'currency')->dropDownList($currencyArray, ['prompt'=>'Select...'])->label(false);
                echo '</div></div>';
            } else {
                echo $currencySets;
            }
            echo '</label><div class="col-lg-7">';
            echo $form->field($model, 'amount')->textInput(['id' => 'amount', 'maxlength' => 11])->label(false) . '</div>';
        }
        ?>
        <label class="col-lg-5 text-left col-form-label"><?= Yii::t('store-credit', 'Comment : ') ?></label>
        <div class="col-lg-7">
            <?= $form->field($model, 'comment')->textarea(['id' => 'comment', 'maxlength' => '255', 'rows' => 6])->label(false) ?>
        </div>
        <label class="col-lg-5 text-left col-form-label"></label>
        <div class="col-lg-7">
            <?= $form->field($model, 'show_customer')->checkbox() ?>
        </div>
    </div>
    <div class="form-group m-form__group row">
        <div class="col-lg-12 m--align-right">
            <?= Html::button(Yii::t('store-credit', 'Close'), ['class' => 'btn btn-default', 'data-dismiss' => 'modal']) ?>
            <?= Html::submitButton($submitButton, ['id' => 'sc_form_submit', 'class' => 'btn m-btn btn-primary']) ?>
        </div>
    </div>
</div>

<?php

ActiveForm::end();

$this->registerJs(<<<JS
 $(document).ready(function () {
    $('select[name="StoreCreditForm[to_currency]"]').on('change', function(e) {
        $.ajax({
            method: "GET",
            url: '/store-credit/sc-exchange-rate?amount='+$('input[name="StoreCreditForm[amount]"]').val()+'&from_curr='+$('input[name="StoreCreditForm[currency]"]').val()+'&to_curr='+$(this).val(),
            dataType: 'json',
        }).done(function (data) {
            $('input[name="StoreCreditForm[to_amount]"]').val(data['conversion_amount']);
            $('input[name="StoreCreditForm[conversion_rate]"]').val(data['conversion_rate']);
        }).fail(function () {
            $('#storecreditform-to_currency').prop('selectedIndex',0);
            alert('Failed to convert. Please check currency status');
        });
    });

    $('#store-credit-form').on('beforeSubmit', function() {
        mApp.blockPage();
        $('#sc_form_submit').attr('disabled', true).addClass('m-loader m-loader--light m-loader--left');

    });
 });
JS
    , \yii\web\View::POS_READY);
?>