<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use yii\widgets\LinkPager;
use yii\bootstrap4\Modal;

$gridColumns = [
    [
        'header' => 'Date',
        'attribute' => 'created_date',
        'headerOptions' => ['style' => 'vertical-align:top;'],
        'contentOptions' => ['style' => 'width:15%;'],
        'value' => function ($data) {
            // create a $dt object with the UTC timezone
            $ts = explode('.', $data['created_date']);
            $dt = new DateTime('@' . $ts[0]);
            // change the timezone of the object without changing it's time
            $dt->setTimezone(new DateTimeZone('Asia/Kuala_Lumpur'));
            return $dt->format('Y-m-d H:i');
        },
    ],
    [
        'header' => 'Activity',
        'attribute' => 'activity_title',
        'headerOptions' => ['style' => 'vertical-align:top;'],
        'format' => 'html',
        'value' => function ($data) {
            $actText = '';

            if ($data['order_id'] != 0) {
                switch ($data['activity']) {
                    case 'D':
                        $actText .= 'Redeem #';
                        break;
                    default:
                        $actText .= 'Customer Order';
                        break;
                }
                $actText .= ' ' . $data['order_id'] . ' - ';
            }

            $actText .= $data['activity_title'] . ' (by ' . $data['requesting_id'] . ')<br>';
            $actText .= '(Transaction ID: ' . $data['request_id'] . ')<br>';
            $actText .= '<strong>Comment:</strong><br>' . $data['activity_description'];

            return $actText;
        },
    ],
    [
        'header' => 'Currency',
        'attribute' => 'transaction_currency',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center; width:10%;'],
    ],
    [
        'header' => 'Debit',
        'headerOptions' => ['style' => 'text-align:center;'],
        'contentOptions' => ['style' => 'text-align:center; width:10%;'],
        'value' => function ($data) {
            return ($data['transaction_type'] == 'SUBTRACT_CREDIT') ? number_format($data['transaction_amount'], 2, '.', ',') : '-';
        },
    ],
    [
        'header' => 'Credit',
        'headerOptions' => ['style' => 'text-align:center;'],
        'contentOptions' => ['style' => 'text-align:center; width:10%;'],
        'value' => function ($data) {
            return ($data['transaction_type'] == 'ADD_CREDIT') ? number_format($data['transaction_amount'], 2, '.', ',') : '-';
        },
    ],
    [
        'header' => 'Balance',
        'attribute' => 'new_amount',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center; width:10%;'],
        'value' => function ($data) {
            return number_format($data['new_amount'], 2, '.', ',');
        }
    ],
];
?>

<!-- Store Credit Form -->
<div class="m-portlet m-portlet--mobile">
    <!-- Store Credit List Title -->
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">Statement</h3>
            </div>
        </div>
        <!-- Add/Deduct/Convert Store Credit Action -->
        <?php if ((Yii::$app->user->can('[Store Credit] Manual Add') || Yii::$app->user->can('[Store Credit] Manual Deduct') || Yii::$app->user->can('[Store Credit] Manual Convert') || Yii::$app->user->can('[Store Credit] Suspend Account')) && $model->user_status == 'ACTIVE') { ?>
            <div class="m-portlet__head-tools">
                <ul class="m-portlet__nav">
                    <li class="m-portlet__nav-item">
                        <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" m-dropdown-toggle="hover" aria-expanded="true">
                            <a href="#" class="m-portlet__nav-link btn btn-lg btn-secondary  m-btn m-btn--icon m-btn--icon-only m-btn--pill  m-dropdown__toggle">
                                <i class="la la-ellipsis-h m--font-brand"></i>
                            </a>
                            <div class="m-dropdown__wrapper">
                                <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                                <div class="m-dropdown__inner">
                                    <div class="m-dropdown__body">
                                        <div class="m-dropdown__content">
                                            <ul class="m-nav">
                                                <li class="m-nav__section m-nav__section--first">
                                                    <span class="m-nav__section-text"><?= Yii::t('store-credit', 'Quick Actions') ?></span>
                                                </li>
                                                <?php if (!$model->currency || isset(Yii::$app->currency->currency_list[$model->currency])) { ?>
                                                    <?php if (Yii::$app->user->can('[Store Credit] Manual Add')) { ?>
                                                        <li class="m-nav__item">
                                                            <a href="javascript:void(0)" class="m-nav__link" onclick="viewItemDetails('<?= Url::to(['/store-credit', 'form_type' => 'add', 'customers_id' => $model->customers_id, 'start_date' => $model->start_date, 'end_date' => $model->end_date]) ?>')">
                                                                <i class="m-nav__link-icon la la-plus"></i>
                                                                <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Manual Addition') ?></span>
                                                            </a>
                                                        </li>
                                                    <?php } ?>
                                                    <?php if ($model->currency) { ?>
                                                        <?php if (Yii::$app->user->can('[Store Credit] Manual Deduct')) { ?>
                                                            <li class="m-nav__item">
                                                                <a href="javascript:void(0)" class="m-nav__link" onclick="viewItemDetails('<?= Url::to(['/store-credit', 'form_type' => 'deduct', 'customers_id' => $model->customers_id, 'start_date' => $model->start_date, 'end_date' => $model->end_date]) ?>')">
                                                                    <i class="m-nav__link-icon la la-minus"></i>
                                                                    <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Manual Deduction') ?></span>
                                                                </a>
                                                            </li>
                                                        <?php } ?>
                                                        <?php if (Yii::$app->user->can('[Store Credit] Manual Convert')) { ?>
                                                            <li class="m-nav__item">
                                                                <a href="javascript:void(0)" class="m-nav__link" onclick="viewItemDetails('<?= Url::to(['/store-credit', 'form_type' => 'convert', 'customers_id' => $model->customers_id, 'start_date' => $model->start_date, 'end_date' => $model->end_date]) ?>')">
                                                                    <i class="m-nav__link-icon la la-exchange"></i>
                                                                    <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Store Credit Conversion') ?></span>
                                                                </a>
                                                            </li>
                                                        <?php } ?>
                                                    <?php } ?>
                                                <?php } else { ?>
                                                    <li class="m-nav__item">
                                                        <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Currency disabled. Transaction actions are not allowed.') ?></span>
                                                        </a>
                                                    </li>
                                                <?php } ?>
                                                <?php if ($model->currency && Yii::$app->user->can('[Store Credit] Suspend Account')) { ?>
                                                    <li class="m-nav__item">
                                                        <a href="javascript:void(0)" class="m-nav__link" onclick="updateAcc('<?= $model->customers_id ?>', 'suspend')">
                                                            <i class="m-nav__link-icon la la-unlock"></i>
                                                            <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Suspend Account') ?></span>
                                                        </a>
                                                    </li>
                                                <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        <?php } ?>
        <!-- Activate Suspended Account -->
        <?php if ((Yii::$app->user->can('[Store Credit] Activate Account')) && $model->user_status != 'ACTIVE') { ?>
            <div class="m-portlet__head-tools">
                <ul class="m-portlet__nav">
                    <li class="m-portlet__nav-item">
                        <div class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" m-dropdown-toggle="hover" aria-expanded="true">
                            <a href="#" class="m-portlet__nav-link btn btn-lg btn-secondary  m-btn m-btn--icon m-btn--icon-only m-btn--pill  m-dropdown__toggle">
                                <i class="la la-ellipsis-h m--font-brand"></i>
                            </a>
                            <div class="m-dropdown__wrapper">
                                <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                                <div class="m-dropdown__inner">
                                    <div class="m-dropdown__body">
                                        <div class="m-dropdown__content">
                                            <ul class="m-nav">
                                                <li class="m-nav__section m-nav__section--first">
                                                    <span class="m-nav__section-text"><?= Yii::t('store-credit', 'Quick Actions') ?></span>
                                                </li>
                                                <?php if ($model->currency && Yii::$app->user->can('[Store Credit] Activate Account')) { ?>
                                                    <li class="m-nav__item">
                                                        <a href="javascript:void(0)" class="m-nav__link" onclick="updateAcc('<?= $model->customers_id ?>', 'activate')">
                                                            <i class="m-nav__link-icon la la-unlock-alt"></i>
                                                            <span class="m-nav__link-text"><?= Yii::t('store-credit', 'Activate Account') ?></span>
                                                        </a>
                                                    </li>
                                                <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        <?php } ?>
    </div>
    <!-- Store Credit List Body -->
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-3">Customer ID</label>
            <div class="col-lg-3"><?= $model->customers_id; ?></div>
            <label class="col-lg-3">Customer Name</label>
            <div class="col-lg-3"><?= $model->customers_name; ?></div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-3">Store Credit Balance</label>
            <div class="col-lg-3"><?= $model->currency . " " . $model->amount; ?></div>
            <label class="col-lg-3">Store Credit Status</label>
            <div class="col-lg-3"><?= $model->user_status; ?></div>
        </div>

        <?php
        echo GridView::widget([
            'dataProvider' => $provider,
            'columns' => $gridColumns,
            'layout' => "{summary}\n{items}",
            'summary' => 'Showing <b>' . ($startAt) . '-' . ($endAt) . '</b> of <b>' . $model->scCount . '</b> items.'
        ]);

        $form = ActiveForm::begin([
                    'id' => 'po-pagesize-form',
                    'method' => 'get',
                    'action' => ['/store-credit']
        ]);

        // Page size drop down options
        $dropdown = Html::dropDownList(
                        'pagesize', $model->scPageSize, [
                    '20' => '20',
                    '50' => '50',
                    '100' => '100',
                    '200' => '200',
                        ], ['onchange' => 'this.form.submit()', 'class' => 'selectpicker m-datatable__pager-size paging-width']
        );

        // display pagination
        $linkPager = LinkPager::widget([
                    'options' => [
                        'class' => 'pagination'
                    ],
                    'pageCssClass' => 'paginate_button page-item',
                    'firstPageCssClass' => 'paginate_button page-item previous',
                    'prevPageCssClass' => 'paginate_button page-item previous',
                    'lastPageCssClass' => 'paginate_button page-item previous',
                    'nextPageCssClass' => 'paginate_button page-item previous',
                    'firstPageLabel' => Yii::t('store-credit', ' << '),
                    'lastPageLabel' => Yii::t('store-credit', ' >> '),
                    'hideOnSinglePage' => false,
                    'pagination' => $pages,
        ]);

        // table footer
        echo '<div class="dataTables_wrapper m_datatable m-datatable m-datatable--default m-datatable--loaded m-datatable--scroll">';
        echo '<div class="m-datatable__pager m-datatable--paging-loaded clearfix">';
        echo '<div class="dataTables_paginate paging_simple_numbers m-datatable__pager-nav">' . $linkPager . '</div>';
        echo '<div class="m-datatable__pager-info">';
        echo $dropdown;
        echo '</div></div></div>';

        echo Html::hiddenInput('report_type', $model->report_type);
        echo Html::hiddenInput('transaction_id', $model->transaction_id);
        echo Html::hiddenInput('customers_id', $model->customers_id);
        echo Html::hiddenInput('start_date', $model->start_date);
        echo Html::hiddenInput('end_date', $model->end_date);
        echo Html::hiddenInput('activity', $model->activity);
        echo Html::hiddenInput('page', $model->scPage);

        ActiveForm::end();
        ?>
    </div>
</div>

<?php
Modal::begin([
    'title' => 'Store Credit',
    'id' => 'generalModal',
    'size' => Modal::SIZE_LARGE
]);
Modal::end();
?>

<script type="text/javascript">
    function viewItemDetails(url) {
        $.ajax({
            method: "GET",
            url: url,
            dataType: 'json'
        })
                .done(function (data) {
                    jQuery("#generalModal .modal-body").html(data['body']);
                    jQuery("#generalModal .modal-title").text(data['title']);
                    jQuery("#generalModal").modal('show');
                });
    }

    function updateAcc(custId, action) {
        Swal.fire({
            title: 'Confirm to ' + action + ' : ' + custId + '?',
            showCancelButton: true,
            showLoaderOnConfirm: true,
            preConfirm: (login) => {
                return fetch('/store-credit/update-account?customersId=' + custId + '&action=' + action)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(response.statusText)
                            }
                            return response.json();
                        })
                        .then(json => {
                            jQuery("#submit_search").trigger("click");
                        })
                        .catch(error => {
                            Swal.showValidationMessage(`Fail to Update Account Status`)
                        })
            },
            allowOutsideClick: () => !Swal.isLoading()
        })
    }
</script>