<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use yii\widgets\LinkPager;
use yii\bootstrap4\Modal;

$gridColumns = [
    [
        'header' => 'Transaction ID',
        'attribute' => 'request_id',
        'headerOptions' => ['style' => 'vertical-align:top;'],
    ],
    [
        'header' => 'Date/Time',
        'attribute' => 'created_date',
        'headerOptions' => ['style' => 'vertical-align:top;'],
        'value' => function ($data) {
            // create a $dt object with the UTC timezone
            $ts = explode('.', $data['created_date']);
            $dt = new DateTime('@' . $ts[0]);
            // change the timezone of the object without changing it's time
            $dt->setTimezone(new DateTimeZone('Asia/Kuala_Lumpur'));
            return $dt->format('Y-m-d H:i');
        },
    ],
    [
        'header' => 'Activity',
        'attribute' => 'activity_title',
        'headerOptions' => ['style' => 'vertical-align:top;'],
        'format' => 'html',
        'value' => function ($data) {
            $actText = '';
            if ($data['order_id'] != 0) {
                $actText .= 'Customer Order ' . $data['order_id'] . ' - ';
            }
            $actText .= $data['activity_title'];
            return $actText;
        },
    ],
    [
        'header' => 'By',
        'attribute' => 'requesting_id',
        'headerOptions' => ['style' => 'vertical-align:top;'],
    ],
    [
        'header' => 'Customer ID',
        'attribute' => 'user_id',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
    ],
    [
        'header' => 'Payment Gateway',
        'attribute' => 'param_2',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
    ],
    [
        'header' => 'Comment',
        'attribute' => 'activity_description',
        'headerOptions' => ['style' => 'vertical-align:top;'],
    ],
    [
        'header' => 'Currency',
        'attribute' => 'transaction_currency',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
    ],
    [
        'header' => 'Debit',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
        'value' => function ($data) {
            return ($data['transaction_type'] == 'SUBTRACT_CREDIT') ? number_format($data['transaction_amount'], 2, '.', ',') : '-';
        },
    ],
    [
        'header' => 'Credit',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
        'value' => function ($data) {
            return ($data['transaction_type'] == 'ADD_CREDIT') ? number_format($data['transaction_amount'], 2, '.', ',') : '-';
        },
    ],
    [
        'header' => 'Balance',
        'attribute' => 'new_amount',
        'headerOptions' => ['style' => 'text-align:center; vertical-align:top;'],
        'contentOptions' => ['style' => 'text-align:center;'],
        'value' => function ($data) {
            return number_format($data['new_amount'], 2, '.', ',');
        }
    ],
];

echo $this->render('_store-credit-opening-balance', ['model' => $model]);
echo $this->render('_store-credit-closing-balance', ['model' => $model]);
?>
<div class="store-credit-list">
    <div class="row">
        <div class="col-xl-12">
            <!-- Store Credit Form -->
            <div class="m-portlet m-portlet--mobile">
                <!-- Store Credit List Body -->
                <div class="m-portlet__body">
                    <?php
                    echo GridView::widget([
                        'dataProvider' => $provider,
                        'columns' => $gridColumns,
                        'layout' => "{items}",
                    ]);

                    $form = ActiveForm::begin([
                                'id' => 'po-pagesize-form',
                                'method' => 'get',
                                'action' => ['/store-credit']
                    ]);

                    // Page size drop down options
                    $dropdown = Html::dropDownList(
                                    'pagesize', $model->scPageSize, [
                                '20' => '20',
                                '50' => '50',
                                '100' => '100',
                                '200' => '200',
                                    ], ['onchange' => 'this.form.submit()', 'class' => 'selectpicker m-datatable__pager-size paging-width']
                    );

                    // display pagination
                    $linkPager = LinkPager::widget([
                                'options' => [
                                    'class' => 'pagination'
                                ],
                                'pageCssClass' => 'paginate_button page-item',
                                'firstPageCssClass' => 'paginate_button page-item previous',
                                'prevPageCssClass' => 'paginate_button page-item previous',
                                'lastPageCssClass' => 'paginate_button page-item previous',
                                'nextPageCssClass' => 'paginate_button page-item previous',
                                'firstPageLabel' => Yii::t('store-credit', ' << '),
                                'lastPageLabel' => Yii::t('store-credit', ' >> '),
                                'hideOnSinglePage' => false,
                                'pagination' => $pages,
                    ]);

                    // table footer
                    echo '<div class="dataTables_wrapper m_datatable m-datatable m-datatable--default m-datatable--loaded m-datatable--scroll">';
                    echo '<div class="m-datatable__pager m-datatable--paging-loaded clearfix">';
                    echo '<div class="dataTables_paginate paging_simple_numbers m-datatable__pager-nav">' . $linkPager . '</div>';
                    echo '<div class="m-datatable__pager-info">';
                    echo $dropdown . '<span class="m-datatable__pager-detail">Displaying <b>' . ($startAt) . '-' . ($endAt) . '</b> of <b>' . $model->scCount . '</b> items.</span>';
                    echo '</div></div></div>';

                    echo Html::hiddenInput('report_type', $model->report_type);
                    echo Html::hiddenInput('transaction_id', $model->transaction_id);
                    echo Html::hiddenInput('customers_id', $model->customers_id);
                    echo Html::hiddenInput('start_date', $model->start_date);
                    echo Html::hiddenInput('end_date', $model->end_date);
                    echo Html::hiddenInput('activity', $model->activity);
                    echo Html::hiddenInput('page', $model->scPage);

                    ActiveForm::end();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>