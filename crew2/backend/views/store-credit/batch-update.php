<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$this->title = 'Store Credit';

$this->params['breadcrumbs'][] = 'Sales';
$this->params['breadcrumbs'][] = ['label' => 'Store Credit', 'url' => ['store-credit/index']];
$this->params['breadcrumbs'][] = ['label' => 'Batch Update', 'url' => ['store-credit/queue']];
$this->params['layoutContent'] = 'block';
?>

<div class="alert alert-info m-alert m-alert--icon m-alert--air m-alert--square m--margin-bottom-30" role="alert">
    <div class="m-alert__icon">
        <i class="flaticon-exclamation-1"></i>
    </div>
    <div class="m-alert__text">
        Batch update will submit request to cronjob queue
    </div>
</div>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    New
                </h3>
            </div>
        </div>
    </div>

    <!--begin::Form-->
    <?php
    $form = ActiveForm::begin([
                'id' => 'store-credit-batch-update',
                'action' => ['/store-credit/batch-update'],
                'options' => [
                    'class' => 'm-form m-form--fit m-form--label-align-right m-form--group-seperator-dashed',
                ],
    ]);
    ?>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Transaction<span style="color: red;">*</span></label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'transaction_type')->dropDownList($model->_trans_opt, [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size',
                    'onchange' => "if ($(this).val() == 'CONVERT_CREDIT') {
                            $('#add-deduct-credit').hide();
                            $('#convert-credit').show();
                            $('#convert-credit-id').show();
                        } else {
                            $('#customers_id').attr('disabled', false);
                            $('#customers_email').attr('disabled', false);
                            $('#add-deduct-credit').show();
                            $('#convert-credit').hide();
                            $('#convert-credit-id').hide();
                            $('.field-customers_id .help-block').text('');
                            $('.field-customers_email .help-block').text('');
                        }"
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row" id="add-deduct-credit">
            <label class="col-lg-2 col-form-label">Currency<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'currency')->dropDownList($f_cur, [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Amount<span style="color: red;">*</span></label>
            <div class="col-lg-4">
                <?= $form->field($model, 'amount')->textInput(['id' => 'amount', 'placeholder' => '0.00'])->label(false); ?>
            </div>
        </div>

        <div class="form-group m-form__group row" id="convert-credit" style="display: none;">
            <label class="col-lg-2 col-form-label">Convert To</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'convert_currency')->dropDownList($f_cur, [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size',
                ])->label(false);
                ?>
                <span class="m-form__help">Will not convert for customer without store credit account, or currency same as Convert To</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Conversion Type</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, '_rate_type')->dropDownList(["sell" => "Sell Rate", "buy" => "Buy Rate", "spot" => "Spot Rate"], [
                    'class' => 'selectpicker m-datatable__pager-size'
                ])->label(false);
                ?>
                <span class="m-form__help">Applied when conversion required</span>
            </div>
        </div>

        <div class="form-group m-form__group row" id="convert-credit-id" style="display: none;">
            <label class="col-lg-2 col-form-label">Phone Country (optional)</label>
            <div class="col-lg-10">
                <?=
                $form->field($model, '_phone_ctry')->dropDownList($f_ctry, [
                    'prompt' => 'Select',
                    'class' => 'selectpicker m-datatable__pager-size',
                    'onchange' => "if ($(this).val() != '') {
                            $('#customers_id').attr('disabled', true);            
                            $('#customers_email').attr('disabled', true);
                            $('.field-customers_id .help-block').text('');
                            $('.field-customers_email .help-block').text('');    
                        } else {
                            $('#customers_id').attr('disabled', false);
                            $('#customers_email').attr('disabled', false);
                        }"
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Customer ID<span style="color: red;">*</span></label>
            <div class="col-lg-10">
                <?= $form->field($model, 'customers_id')->textarea(['id' => 'customers_id', 'rows' => 5])->label(false) ?>
                <label>-- or --</label>
            </div>

            <label class="col-lg-2 col-form-label">Customer Email Address<span style="color: red;">*</span></label>
            <div class="col-lg-10">
                <?= $form->field($model, 'customers_email')->textarea(['id' => 'customers_email', 'rows' => 5])->label(false) ?>
                <span class="m-form__help">Separate multiple Customer ID or Email Address with comma</span>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Comment<span style="color: red;">*</span></label>
            <div class="col-lg-10">
                <?= $form->field($model, 'comment')->textarea(['id' => 'comment', 'rows' => 2])->label(false) ?>
                <?= $form->field($model, 'show_customer')->checkbox(['show_customer' => ''])->label(false) ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-12 col-form-label"><label>Email Notification (Optional)</label></div>
            <label class="col-lg-2 col-form-label">Subject</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'email_subject')->input(['id' => 'email_subject'])->label(false) ?>
            </div>
            <label class="col-lg-2 col-form-label">Message</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'email_message')->textarea(['id' => 'email_message', 'rows' => 5])->label(false) ?>
            </div>
        </div>
    </div>
    
    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('store-credit/queue'); ?>'">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
    <!--end::Form-->
</div>