<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ActiveForm;
use kartik\widgets\DatePicker;

$this->title = 'Store Credit';

$this->params['breadcrumbs'][] = 'Sales';
$this->params['breadcrumbs'][] = 'Store Credit';
$this->params['breadcrumbs'][] = 'Statement';
$this->params['layoutContent'] = 'block';

// render report type
$reportType = '_store-credit-list-customer';
$customersIdDisabled = false;
$exportDisabled = true;
$exportHide = 'hide';
if (isset($model->report_type) && $model->report_type != 1) {
    $reportType = '_store-credit-list-movement';
    $customersIdDisabled = true;
    $exportDisabled = false;
    $exportHide = '';
}

// Set current date
$start_date = ($model->start_date) ? $model->start_date : date('Y-m-d');
$end_date = ($model->end_date) ? $model->end_date : date('Y-m-d');

$form = ActiveForm::begin([
            'id' => 'store-credit-search-form',
            'action' => ['/store-credit'],
            'options' => [
                'class' => 'm-form m-form--fit m-form--label-align-right',
            ],
            'enableAjaxValidation' => true,
            'validationUrl' => Url::toRoute('store-credit/validation'),
        ]);
?>

<div class="m-portlet m-portlet--primary <?= ($provider ? "m-portlet--collapsed" : ""); ?> m-portlet--head-solid-bg m-portlet--head-sm" m-portlet="true" id="m_portlet_tools_2">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <span class="m-portlet__head-icon">
                    <i class="flaticon-search"></i>
                </span>
                <h3 class="m-portlet__head-text">
                    Search
                </h3>
            </div>
        </div>
        <div class="m-portlet__head-tools">
            <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                    <a href="#" m-portlet-tool="toggle" class="m-portlet__nav-link m-portlet__nav-link--icon"><i class="la la-angle-down"></i></a>
                </li>
            </ul>
        </div>
    </div>
    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label"><?= Yii::t('store-credit', 'Report Type') ?></label>
            <div class="col-lg-10">
                <?=
                $form->field($model, 'report_type')->dropDownList([
                    1 => "By Customer",
                    2 => "Store Credit Movement"
                        ], [
                    'id' => 'report_type',
                    'class' => 'selectpicker m-datatable__pager-size',
                    'onchange' => "resetCheck()"
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Transaction ID</label>
            <div class="col-lg-4">
                <?php echo $form->field($model, 'transaction_id')->textInput(['id' => 'transaction_id'])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">Customer ID</label>
            <div class="col-lg-4">
                <?php echo $form->field($model, 'customers_id')->textInput(['id' => 'customers_id', 'disabled' => $customersIdDisabled])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date</label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'start_date')->widget(DatePicker::classname(), [
                    'options' => ['value' => $start_date, 'id' => 'start_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => '+1d', 'datesDisabled' => '+1d'],
                    'pluginEvents' =>[
                        'changeDate' => 'function(e) {
                            $endDateVal = $("#end_date").val();
                            if (Boolean($endDateVal.trim())) {
                                $(".field-end_date p.help-block-error").html("");
                            }
                        }',
                    ]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">End Date</label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'end_date')->widget(DatePicker::classname(), [
                    'options' => ['value' => $end_date, 'id' => 'end_date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true, 'endDate' => '+1d', 'datesDisabled' => '+1d'],
                    'pluginEvents' =>[
                        'changeDate' => 'function(e) {
                            $startDateVal = $("#start_date").val();
                            if (Boolean($startDateVal.trim())) {
                                $(".field-start_date p.help-block-error").html("");
                           }
                        }',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label"><?= Yii::t('store-credit', 'Activity') ?></label>
            <div class="col-lg-4">
                <?php
                echo $form->field($model, 'activity')->dropDownList([
                    'X' => Yii::t('store-credit', 'Cancel'),
                    'C' => Yii::t('store-credit', 'Compensate'),
                    'MI' => Yii::t('store-credit', 'Manual Addition'),
                    'MR' => Yii::t('store-credit', 'Manual Deduction'),
                    'MX' => Yii::t('store-credit', 'Manual Conversion'),
                    'P' => Yii::t('store-credit', 'Purchase'),
                    'R' => Yii::t('store-credit', 'Refund'),
                    'B' => Yii::t('store-credit', 'Bonus'),
                    'D' => Yii::t('store-credit', 'Redeem'),
                    'S' => Yii::t('store-credit', 'Store Credit Top Up'),
                    'XS' => Yii::t('store-credit', 'Extra Store Credit'),
                    'GC' => Yii::t('store-credit', 'Gift Card Redemption'),
                    'PW' => Yii::t('store-credit', 'OffGamers Payment'),
                    'RP' => Yii::t('store-credit', 'Re-Purchase'),
                    'V' => Yii::t('store-credit', 'Currency Conversion')
                        ], [
                    'prompt' => 'All Activities',
                    'id' => 'activity',
                    'class' => 'selectpicker m-datatable__pager-size',
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <?= Html::hiddenInput('submit_type') ?>
                <?= Html::submitButton(Yii::t('store-credit', 'Search'), ['id' => 'submit_search', 'class' => 'btn btn-primary', 'value' => 'search', 'name' => 'submit_search', 'onclick' => 'submitType("search")']) ?>
                <?= Html::resetButton(Yii::t('store-credit', 'Reset'), ['id' => 'reset', 'class' => 'btn btn-default', 'onclick' => 'resetCheck()']) ?>
                <?= Html::submitButton('<i class="fa fa-download"></i> Export', ['type' => 'button', 'name' => 'submit_export', 'id' => 'submit_export', 'class' => 'btn btn-warning', 'value' => 'export', 'disabled' => $exportDisabled, 'onclick' => 'submitType("export")']) ?>
            </div>
        </div>
    </div>
</div>
<?php ActiveForm::end(); ?>

<?php
if (!$model->errors) {
    echo (isset($model->report_type)) ? $this->render($reportType, [
                'model' => $model,
                'provider' => $provider,
                'pages' => $pages,
                'startAt' => $startAt,
                'endAt' => $endAt
            ]) : '';
}
?>

<script type="text/javascript">
    function submitType(type)
    {
        $('input[name="submit_type"]').val(type);
    }

    function resetCheck()
    {
        setTimeout(function () {
            if ($('select[name="StoreCreditForm[report_type]"]').val() == 1) {
                $('input[name="StoreCreditForm[customers_id]"]').prop('disabled', false);
                $('#submit_export').prop('disabled', true);
                $('#submit_export').addClass('hide');
            } else {
                $('input[name="StoreCreditForm[customers_id]"]').prop('disabled', true);
                $('#submit_export').prop('disabled', false);
                $('#submit_export').removeClass('hide');
            }
        }, 1);
    }
</script>