<?php

use yii\helpers\Url;
use yii\helpers\Json;

$this->title = 'Store Credit';

$this->params['breadcrumbs'][] = 'Sales';
$this->params['breadcrumbs'][] = ['label' => 'Store Credit', 'url' => ['store-credit/index']];
$this->params['breadcrumbs'][] = 'Batch Update';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Queue
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('store-credit/batch-update'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-section">
            <span class="m-section__sub">
                Total <b><?= number_format($total); ?></b> record(s)
            </span>

            <!--begin: Datatable -->
            <div class="m-section__content">
                <table class="table table-striped- table-bordered table-hover table-checkable">
                    <thead>
                        <tr>
                            <th>Customer ID</th>
                            <th>Transaction</th>
                            <th>Currency</th>
                            <th style="text-align: right;">Amount</th>
                            <th>Comment</th>
                            <th>Date Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($data) { ?>
                            <?php
                            foreach ($data as $num => $val) {
                                $extra = Json::decode($val["extra_info"]);
                                ?>
                                <tr>
                                    <td><?= $val["id"]; ?></td>
                                    <td><?= isset($extra["transaction_type"]) && isset($model->_trans_opt[$extra["transaction_type"]]) ? $model->_trans_opt[$extra["transaction_type"]] : ""; ?></td>
                                    <td><?= isset($extra["currency"]) ? $extra["currency"] : (isset($extra["to_currency"]) ? $extra["to_currency"] : ""); ?></td>
                                    <td style="text-align: right;"><?= isset($extra["amount"]) ? $extra["amount"] : ""; ?></td>
                                    <td><?= isset($extra["comment"]) ? $extra["comment"] : ""; ?></td>
                                    <td><?= date("Y-m-d H:i:s", $val["created_at"]); ?></td>
                                </tr>
                            <?php } ?>
                        <?php } else { ?>
                            <tr>
                                <td colspan="6">No result found.</td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
            <small><i>Maximum display 50 latest records</i></small>
        </div>
    </div>
</div>