<?php
$this->title = 'Cache';

$this->params['breadcrumbs'] = [];
$this->params['breadcrumbs'][] = 'Administrator';
$this->params['breadcrumbs'][] = 'Cache';
$this->params['layoutContent'] = 'block';

$cf_list = Yii::$app->params['aws.cf.list'];
?>

<div class="alert alert-info m-alert m-alert--icon m-alert--air m-alert--square m--margin-bottom-30" role="alert">
    <div class="m-alert__icon">
        <i class="flaticon-exclamation-1"></i>
    </div>
    <div class="m-alert__text">
        Invalidate items or file from caches
    </div>
</div>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    Memcached
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <button class="btn btn-primary" style="margin-bottom: 5px;" onclick="clearMemcache();">Memcached</button>
            </div>
        </div>
    </div>
</div>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    CloudFront
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <?php foreach ($cf_list as $cf) { ?>
                    <button class="btn btn-primary" style="margin-bottom: 5px;" onclick="clearCF('<?= $cf['distribution_id']; ?>');"><?= $cf['title']; ?></button>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<script>
    function clearMemcache() {
        $.ajax('/admin/system/clear-memcache').done(function () {
            alert('Memcache Flushed')
        })
    }

    function clearCF(id) {
        $.ajax('/admin/system/clear-cf-cache?distribution_id=' + id).done(function () {
            alert('Cloudfront Cache Flushed')
        }).fail(function () {
            alert('Fail to flush Cloudfront Cache')
        })
    }
</script>