<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DatePicker;
use kartik\widgets\SwitchInput;

$form = ActiveForm::begin([
            'options' => [
                'class' => 'm-form m-form--label-align-right m-form--state-',
            ]
        ]);

$language = Yii::$app->enum->getLanguage('listData');
?>

<div class="m-portlet__body">
    <div class="m-form__section">
        <div class="m-form__heading" id="_tax_profile">
            <h3 class="m-form__heading-title"><i class="la la-briefcase"></i> Tax Profile</h3>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Location</label>
            <div class="col-lg-4">
                <?php
                if ($model->orders_tax_id) {
                    echo $form->field($model, 'country_name')->textInput(['disabled' => true])->label(false);
                } else {
                    echo $form->field($model, 'country_code')->dropDownList($model->getTaxCountry(), [
                        'prompt' => 'Select',
                        'class' => 'selectpicker m-datatable__pager-size',
                        'onchange' => '
                            $.get("' . Url::toRoute('/tax/tax-country-currency') . '", { id: $(this).val() } )
                                .done(function(data) {
                                    $("#__tax_cur").val(data);
                                }
                            );
                        '
                    ])->label(false);
                }
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Currency</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'currency')->dropDownList(Yii::$app->currency->getCurrencyList(), [
                    'id' => '__tax_cur',
                    'prompt' => 'Select',
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Title</label>
            <div class="col-lg-10">
                <?= $form->field($model, 'orders_tax_title')->textInput()->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Sales Tax (%)</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'orders_tax_percentage')->textInput([
                    'maxlength' => 9,
                    'format' => ['decimal', 2]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Business Tax (%)</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'business_tax_percentage')->textInput([
                    'maxlength' => 9,
                    'format' => ['decimal', 2]
                ])->label(false);
                ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Start Date</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'start_datetime')->widget(DatePicker::classname(), [
                    'options' => ['value' => $model->start_datetime, 'id' => 'start-date'],
                    'pluginOptions' => ['format' => 'yyyy-mm-dd', 'todayBtn' => 'linked', 'autoclose' => true, 'todayHighlight' => true]
                ])->label(false);
                ?>
            </div>
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'orders_tax_status')->widget(SwitchInput::classname(), [
                    'value' => (isset($model->orders_tax_status) && $model->orders_tax_status == '1') ? true : false,
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>
    </div>

    <div class="m-separator m-separator--dashed m-separator--lg"></div>

    <div class="m-form__section">
        <div class="m-form__heading" id="_precheckout">
            <h3 class="m-form__heading-title">
                <i class="la la-info-circle"></i> Pre-Checkout Message
                <i data-toggle="m-tooltip" data-width="auto" class="m-form__heading-help-icon flaticon-info" title="Taxation Information at Pre-Checkout Page"></i>
            </h3>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <div class="m-portlet m-portlet--tabs">
                    <div class="m-portlet__head">
                        <div class="m-portlet__head-tools">
                            <ul class="nav nav-tabs m-tabs-line m-tabs-line--primary m-tabs-line--2x" role="tablist">
                                <?php foreach ($language as $i => $v) { ?>
                                    <li class="nav-item m-tabs__item">
                                        <a class="nav-link m-tabs__link <?= ($i == 1 ? ' active' : ''); ?>" data-toggle="tab" href="#m_tabs_<?= $i; ?>" role="tab">
                                            <?= $v; ?>
                                        </a>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    </div>
                    <div class="m-portlet__body">
                        <div class="tab-content">
                            <?php
                            foreach ($language as $i => $v) {
                                $data = (isset($model->orders_tax_id) ? $model->getTaxDescriptionByLang($model->orders_tax_id, $i) : "");
                                ?>
                                <div class="tab-pane <?= ($i == 1 ? 'show active' : ''); ?>" id="m_tabs_<?= $i; ?>" role="tabpanel">
                                    <div class="form-group m-form__group row">
                                        <label class="col-lg-2 col-form-label">Message</label>
                                        <div class="col-lg-10">
                                            <?= HTML::textarea("orders_tax_message[$i]", (isset($data->orders_tax_message) ? $data->orders_tax_message : ""), ['rows' => 8, 'class' => 'form-control']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="m-separator m-separator--dashed m-separator--lg"></div>

    <div class="m-form__section">
        <div class="m-form__heading" id="_invoice">
            <h3 class="m-form__heading-title">
                <i class="la la-file-text"></i> Invoice
            </h3>
        </div>

        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Invoice Title</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'tax_invoice_title')->textInput(['maxlength' => 64])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Tax Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'tax_registration_name')->textInput(['maxlength' => 64])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">Tax Short Form</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'orders_tax_title_short')->textInput(['maxlength' => 64])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Tax Register No</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'gst_registration_no')->textInput(['maxlength' => 32])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Company Name</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'company_name')->textInput(['maxlength' => 32])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">Website</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'website')->textInput(['maxlength' => 32])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Address</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'address_1')->textInput(['maxlength' => 64])->label(false); ?>
                <?= $form->field($model, 'address_2')->textInput(['maxlength' => 64])->label(false); ?>
                <?= $form->field($model, 'address_3')->textInput(['maxlength' => 64])->label(false); ?>
            </div>
            <label class="col-lg-2 col-form-label">Contact</label>
            <div class="col-lg-4">
                <?= $form->field($model, 'contact')->textInput(['maxlength' => 32])->label(false); ?>
            </div>
        </div>
        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <?= $form->field($model, 'orders_include_reverse_charge')->checkbox()->label(false); ?>
                <?= $form->field($model, 'orders_provide_invoice_status')->checkbox()->label(false); ?>
            </div>
        </div>
    </div>
</div>

<div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
    <div class="m-form__actions m-form__actions--solid">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
                <button type="submit" class="btn btn-success">Save</button>
                <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('tax/index'); ?>'">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php ActiveForm::end(); ?>

<style>
    :target:before {
        content:"";
        display:block;
        height:90px; /* fixed header height*/
        margin:-90px 0 0; /* negative fixed header height */
    }
</style>

<!-- begin::Quick Nav -->
<ul class="m-nav-sticky" style="margin-top: 30px;">
    <li class="m-nav-sticky__item" data-toggle="m-tooltip" title="Tax Profile" data-placement="left">
        <a href="#_tax_profile">
            <i class="la la-briefcase m--font-info"></i>
        </a>
    </li>
    <li class="m-nav-sticky__item" data-toggle="m-tooltip" title="Pre-Checkout Message" data-placement="left">
        <a href="#_precheckout">
            <i class="la la-info-circle m--font-info"></i>
        </a>
    </li>
    <li class="m-nav-sticky__item" data-toggle="m-tooltip" title="Invoice" data-placement="left">
        <a href="#_invoice">
            <i class="la la-file-text m--font-info"></i>
        </a>
    </li>
</ul>