<?php

use yii\bootstrap4\Modal;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DatePicker;
use kartik\widgets\SwitchInput;
use backend\models\TaxForm;

$this->title = 'Tax';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = ['label' => 'Tax', 'url' => ['tax/index']];
$this->params['breadcrumbs'][] = 'Business Tax Form';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    <?= $model->orders_tax_title; ?>
                </h3>
            </div>
        </div>
    </div>

    <?php
    $option = ['class' => 'form-control'];
    $form = ActiveForm::begin([
                'options' => [
                    'class' => 'm-form m-form--label-align-left- m-form--state-',
                ]
    ]);
    ?>

    <div class="m-portlet__body">
        <div class="form-group m-form__group row">
            <label class="col-lg-2 col-form-label">Status</label>
            <div class="col-lg-4">
                <?=
                $form->field($model, 'business_tax_status')->widget(SwitchInput::classname(), [
                    'value' => (isset($model->business_tax_status) && $model->business_tax_status == 1) ? true : false,
                    'pluginOptions' => [
                        'onText' => '<i class="fa fa-check"></i>',
                        'offText' => '<i class="fa fa-times"></i>',
                    ]
                ])->label(false);
                ?>
            </div>
        </div>

        <div class="form-group m-form__group row">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Label</th>
                            <th>Type</th>
                            <th>Mandatory</th>
                            <th>Sort Order</th>
                            <th style="width: 10%;">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // form in default language EN = 1
                        if ($f_data) {
                            foreach ($f_data as $lang_id => $field) {
                                // sort by `sort_order`
                                $sort = array_column($field, "sort_order");
                                array_multisort($sort, SORT_ASC, $field);

                                $count = count($field);
                                for ($i = 0; $count > $i; $i++) {
                                    $default = (in_array($field[$i]["name"], ["input_business_name", "input_tax_number"]) ? true : false);
                                    ?>
                                    <tr id="field_<?= $field[$i]["id"]; ?>">
                                        <td><?= $field[$i]["title"]; ?><?= ($default ? "*" : ""); ?></td>
                                        <td><?= TaxForm::getFormType($field[$i]["type"]); ?></td>
                                        <td><?= ($field[$i]["mandatory"] == 1 ? "Yes" : "No"); ?></td>
                                        <td><?= $field[$i]["sort_order"]; ?></td>
                                        <td>
                                            <?=
                                            Html::a('<span class="fa fa-pen"></span>', 'javascript:void(0)', [
                                                'onclick' => 'updateBusinessForm("' . Url::to(['tax/update-business-form',
                                                    'id' => $model->orders_tax_id,
                                                    'field_id' => $field[$i]["id"]]) . '")'
                                            ]);
                                            ?>
                                            <?php
                                            if (!$default) {
                                                echo Html::a('<span class="fa fa-trash-alt"></span>', 'javascript:void(0)', [
                                                    'onclick' => 'deleteBusinessForm("' . Url::to(['tax/delete-business-form',
                                                        'id' => $model->orders_tax_id,
                                                        'field_id' => $field[$i]["id"]]) . '")'
                                                ]);
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                        } else {
                            ?>
                            <tr>
                                <td colspan="6">
                                    <div class="empty">No result found.</div>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>

                <div class="m--align-right">
                    <a href="javascript:void(0)" class="btn btn-accent btn-sm btn-brand m-btn m-btn--icon m-btn--pill m-btn--wide" onclick="updateBusinessForm('<?= Url::to(['tax/update-business-form', 'id' => $model->orders_tax_id, 'field_id' => '']); ?>');">
                        <span>
                            <i class="la la-plus"></i>
                            <span>Add</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>

    </div>

    <?php
    Modal::begin([
        'title' => '',
        'id' => 'generalModal',
        'size' => Modal::SIZE_LARGE
    ]);

    Modal::end();
    ?>

    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-8">
                    <button type="submit" class="btn btn-success">Save</button>
                    <button type="button" class="btn btn-secondary" onclick="document.location = '<?= Url::toRoute('tax/index'); ?>'">Cancel</button>
                </div>
                <div class="col-lg-2 m--align-right">
                    <button type="button" class="btn btn-primary" onclick="document.location = '<?= Url::toRoute('tax/index'); ?>'">Back</button>
                </div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<script>
    function updateBusinessForm(url) {
        $.ajax({
            method: 'GET',
            url: url,
            dataType: 'json',
        }).done(function (data) {
            jQuery('#generalModal .modal-body').html(data['body'])
            jQuery('#generalModal .modal-title').text(data['title'])
            jQuery('#generalModal').modal('show')
        })
    }

    function deleteBusinessForm(url) {
        var result = confirm("Are you sure you want to delete this item?");
        if (result == true) {
            mApp.blockPage()
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    window.top.location.reload();
                },
                error: function (data) {
                    mApp.unblockPage()
                    Swal.fire(
                            '',
                            data.message,
                            'error',
                            )
                }
            });
        }
    }
</script>