<?php

use common\widgets\DynamicFormWidget;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use yii\web\YiiAsset;
?>

<?php

$this->title = 'Tax Rule';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = ['label' => 'Tax', 'url' => ['tax/index']];
$this->params['breadcrumbs'][] = 'Tax Profile Rules';
$this->params['breadcrumbs'][] = ['label' => $country, 'url' => ['tax/rules','id' => $model->orders_tax_id]];
$this->params['layoutContent'] = 'block';
$form = ActiveForm::begin([
        'id'=>'ruleform',
    'options' => [
        'class' => 'm-form m-form--label-align-right m-form--state-',
    ]
]);
YiiAsset::register($this);

$js = '
jQuery(".dynamicform_wrapper").on("afterInsert", function(e, item) {
    jQuery(".dynamicform_wrapper .panel-title-address").each(function(index) {
        jQuery(this).html("Rule " + (index + 1))
    });
});

jQuery(".dynamicform_wrapper").on("afterDelete", function(e) {
    jQuery(".dynamicform_wrapper .panel-title-address").each(function(index) {
        jQuery(this).html("Rule " + (index + 1))
    });
});
';

$this->registerJs($js);

$idx = 0;
?>
<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    New
                </h3>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <div class="m-form__section">
            <?php echo (isset($model->orders_tax_id))?Html::activeHiddenInput($model, "orders_tax_id"):'' ?>
            <?= Html::activeHiddenInput($model, "tax_rule_id"); ?>
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">Tax Rule Name</label>
                <div class="col-lg-4">
                    <?=
                    $form->field($model, 'description')->textInput([
                        'maxlength' => 200,
                    ])->label(false); ?>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-lg-2 col-form-label">Rule Type</label>
                <div class="col-lg-4">
                    <?=
                    $form->field($model, 'rule_type')->dropDownList($model::RULE_TYPE, [
                        'id' => '_config_type',
                        'class' => 'selectpicker m-datatable__pager-size',
                    ])->label(false); ?>
                </div>
            </div>
        </div>
        <?php DynamicFormWidget::begin([
            'widgetContainer' => 'dynamicform_wrapper', // required: only alphanumeric characters plus "_" [A-Za-z0-9_]
            'widgetBody' => '.container-items', // required: css class selector
            'widgetItem' => '.item', // required: css class
            'limit' => 4, // the maximum times, an element can be cloned (default 999)
            'min' => 0, // 0 or 1 (default 1)
            'insertButton' => '.add-item', // css class
            'deleteButton' => '.remove-item', // css class
            'model' => $detail[0],
            'formId' => 'ruleform',
            'formFields' => [
                'fieldname',
                'condition',
                'values',
            ],
        ]); ?>

        <div class="panel panel-default">

            <div class="panel-body container-items"><!-- widgetContainer -->
                <?php foreach ($detail as $index => $item): ?>
                    <div class="item panel panel-default"><!-- widgetBody -->
                        <div class="panel-heading">
                            <span class="panel-title-address">Rule <?= ($index + 1) ?></span>
                            <button type="button" class="pull-right remove-item btn btn-danger btn-xs"><i
                                        class="fa fa-minus"></i></button>
                            <div class="clearfix"></div>
                        </div>
                        <div class="panel-body">
                            <?php
                            if (! $item->isNewRecord) {
                                echo Html::activeHiddenInput($item, "[{$index}]tax_rule_detail_id");
                            }
                            ?>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-2 col-form-label">Field</label>
                                <div class="col-lg-4">
                                    <?=
                                    $form->field($item, "[{$index}]fieldname")->dropDownList($item::FIELDNAME, [
                                        'prompt' => 'Select',
                                    ])->label(false); ?>
                                </div>
                                <label class="col-lg-2 col-form-label">Condition Type</label>
                                <div class="col-lg-4">
                                    <?=
                                    $form->field($item, "[{$index}]condition")->dropDownList($item::CONDITION_TYPE, [
                                    ])->label(false); ?>
                                </div>
                            </div>
                            <div class="form-group m-form__group row">
                                <label class="col-lg-2 col-form-label">Value</label>
                                <div class="col-lg-8">
                                    <?=
                                    $form->field($item, "[{$index}]values")->textInput([
                                        'placeholder' => 'Currency Example: SGD; Country Code Example: SG'
                                    ])->label(false); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="panel-heading">
                <button type="button" class="pull-left add-item btn btn-success btn-xs"><i class="fa fa-plus"></i> Add
                    rule
                </button>
                <div class="clearfix"></div>
            </div>
        </div>
        <?php DynamicFormWidget::end(); ?>

    </div>
    <div class="m-portlet__foot m-portlet__no-border m-portlet__foot--fit">
        <div class="m-form__actions m-form__actions--solid">
            <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-10">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-secondary"
                            onclick="document.location = '<?= Url::toRoute(['rules','id'=>$model->orders_tax_id]); ?>'">Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end();
    ?>
</div>
