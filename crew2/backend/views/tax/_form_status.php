<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$form = ActiveForm::begin([
            'id' => '_form',
            'options' => [
                'class' => 'm-form m-form--label-align-left- m-form--state-',
            ],
        ]);
?>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Customer ID</label>
    <div class="col-lg-4"><?= $model->customers_id; ?></div>

    <label class="col-lg-2">Tax Country</label>
    <div class="col-lg-4"><?= $model->country_name; ?></div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Tax Number</label>
    <div class="col-lg-4"><?= $model->business_tax_number; ?></div>
    
    <label class="col-lg-2">Business Name</label>
    <div class="col-lg-4"><?= $model->business_name; ?></div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2">Remarks</label>
    <div class="col-lg-10">
        <div class="form-group field-remarks required">
            <textarea id="remarks" class="form-control m-input" name="remarks" maxlength="255" rows="3" required></textarea>
        </div>
        <?php if ($status == 2) { ?>
            <label>
                <input type="checkbox" id="show_customer" name="show_customer" value="1"> show remarks to customer
            </label>
        <?php } ?>
    </div>
</div>

<div class="m-separator"></div>

<div class="form-group m-form__group row">
    <div class="col-lg-12 m--align-right">
        <button type="button" class="btn btn-success" onclick="submitForm(<?= $model->id; ?>, <?= $status; ?>);">Save</button>
    </div>
</div>

<?php ActiveForm::end(); ?>

<script>
    function submitForm(id, status) {
        var formData = $("#_form").serialize() + "&id=" + id + "&status=" + status;
        mApp.blockPage()

        $.ajax({
            url: '/tax/update-application-status',
            type: 'POST',
            data: formData,
            success: function (data) {
                window.top.location.reload();
            },
            error: function (data) {
                mApp.unblockPage()
                Swal.fire(
                        '',
                        data.message,
                        'error',
                        )
            }
        });
    }
</script>