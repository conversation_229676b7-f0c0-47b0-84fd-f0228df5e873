<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use kartik\widgets\DatePicker;
use kartik\widgets\SwitchInput;
use backend\models\TaxForm;

$option = ['class' => 'form-control'];
$form = ActiveForm::begin([
            'id' => '_form',
            'options' => [
                'class' => 'm-form m-form--label-align-left- m-form--state-',
            ],
        ]);
?>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Label</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="title" id="title" value="<?= htmlentities(isset($data["title"]) ? $data["title"] : ""); ?>">
        <span class="m-form__help">
            <label class="m-checkbox">
                <input type="checkbox" name="mandatory" id="mandatory" <?= (isset($data["mandatory"]) && ($data["mandatory"] == 1) ? "checked" : ""); ?>> Mandatory
                <span></span>
            </label>
        </span>
    </div>

    <label class="col-lg-2 col-form-label">Sort Order</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="sort_order" id="sort_order" value="<?= (isset($data["sort_order"]) ? $data["sort_order"] : ""); ?>">
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Error Message</label>
    <div class="col-lg-10">
        <input type="text" class="form-control m-input" name="error_message" id="error_message" value="<?= htmlentities(isset($data["error_message"]) ? $data["error_message"] : ""); ?>">
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Type</label>
    <div class="col-lg-4">
        <?=
        HTML::dropDownList('type', (isset($data["type"]) ? $data["type"] : ""), TaxForm::getFormTypeList(), [
            'class' => 'form-control',
            'onchange' => "if ($(this).val() == 'text_box') {
                $('[id^=\"f_\"]').hide();
                $('#f_size_max_char').show();
            } else if ($(this).val() == 'text_area') {
                $('[id^=\"f_\"]').hide();
                $('#f_row_col').show();
            } else if ($(this).val() == 'dropdown_menu' || $(this).val() == 'radio_button' || $(this).val() == 'information_text') {
                $('[id^=\"f_\"]').hide();
                $('#f_textarea').show();
            } else if ($(this).val() == 'date_selection') {
                $('[id^=\"f_\"]').hide();
                $('#f_date').show();
            } else {
                $('[id^=\"f_\"]').hide();
            }"
        ])
        ?>
    </div>
</div>

<div class="form-group m-form__group row" id="f_size_max_char" style="display: <?= (!isset($data["type"]) || (isset($data["type"]) && ($data["type"] == "text_box")) ? "" : "none"); ?>;">
    <label class="col-lg-2 col-form-label">Size</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="size" id="size" value="<?= (isset($data["size"]) ? $data["size"] : ""); ?>">
    </div>

    <label class="col-lg-2 col-form-label">Maximum Character</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="max_char" id="max_char" value="<?= (isset($data["max_char"]) ? $data["max_char"] : ""); ?>">
    </div>
</div>

<div class="form-group m-form__group row" id="f_row_col" style="display: <?= ((isset($data["type"]) && ($data["type"] == "text_area")) ? "" : "none"); ?>;">
    <label class="col-lg-2 col-form-label">Row</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="row" id="row" value="<?= (isset($data["row"]) ? $data["row"] : ""); ?>">
    </div>

    <label class="col-lg-2 col-form-label">Column</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="column" id="column" value="<?= (isset($data["column"]) ? $data["column"] : ""); ?>">
    </div>
</div>

<div class="form-group m-form__group row" id="f_textarea" style="display: <?= ((isset($data["type"]) && in_array($data["type"], ["dropdown_menu", "radio_button", "information_text"])) ? "" : "none"); ?>;">
    <label class="col-lg-2 col-form-label">Option value</label>
    <div class="col-lg-4">
        <textarea class="form-control m-input" name="options" id="options"><?= htmlentities(isset($data["options"]) ? $data["options"] : ""); ?></textarea>
        <span class="m-form__help">Each option must be in new line</span>
    </div>
</div>

<div class="form-group m-form__group row" id="f_date" style="display: <?= ((isset($data["type"]) && ($data["type"] == "date_selection")) ? "" : "none"); ?>;">
    <label class="col-lg-2 col-form-label">From</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="date_from" id="date_from" value="<?= (isset($data["date_from"]) ? $data["date_from"] : ""); ?>">
        <span class="m-form__help">Option : YYYY-MM-DD, "TODAY" to use system current date</span>
    </div>

    <label class="col-lg-2 col-form-label">Period (days)</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="date_period" id="date_period" value="<?= (isset($data["date_period"]) ? $data["date_period"] : ""); ?>">
    </div>
</div>

<div class="form-group m-form__group row">
    <label class="col-lg-2 col-form-label">Pre Text</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="pre_text" id="pre_text" value="<?= htmlentities(isset($data["pre_text"]) ? $data["pre_text"] : ""); ?>">
        <span class="m-form__help">e.g. country code</span>
    </div>

    <label class="col-lg-2 col-form-label">Post Text</label>
    <div class="col-lg-4">
        <input type="text" class="form-control m-input" name="post_text" id="post_text" value="<?= htmlentities(isset($data["post_text"]) ? $data["post_text"] : ""); ?>">
        <span class="m-form__help">e.g. @example.com</span>
    </div>
</div>

<div class="m-separator"></div>

<div class="form-group m-form__group row">
    <div class="col-lg-12 m--align-right">
        <button type="button" class="btn btn-success" onclick="submitForm(<?= $id; ?>, <?= $field_id; ?>);">Save</button>
    </div>
</div>

<?php ActiveForm::end(); ?>

<script>
    function submitForm(id, field_id) {
        if (typeof field_id === 'undefined') {
            field_id = "";
        }

        var formData = $("#_form").serialize() + "&id=" + id + "&field_id=" + field_id;
        mApp.blockPage();

        $.ajax({
            url: '/tax/update-business-form',
            type: 'POST',
            data: formData,
            success: function (data) {
                window.top.location.reload();
            },
            error: function (data) {
                mApp.unblockPage()
                Swal.fire(
                        '',
                        data.message,
                        'error',
                        )
            }
        });
    }
</script>