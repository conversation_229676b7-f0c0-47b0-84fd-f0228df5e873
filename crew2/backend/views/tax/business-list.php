<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use kartik\daterange\DateRangePicker;
use yii\bootstrap4\Modal;
use backend\models\TaxForm;

$this->title = 'Business Tax Request';

$this->params['breadcrumbs'][] = 'Customer';
$this->params['breadcrumbs'][] = 'Business Tax Request';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'layout' => '{summary} {items}',
            'filterModel' => $model,
            'columns' => [
                [
                    'header' => 'Customer ID',
                    'attribute' => 'customers_id',
                    'filter' => Html::textInput('customers_id', $model->customers_id, ['class' => 'form-control']),
                ],
                [
                    'header' => 'Business Name',
                    'attribute' => 'business_name',
                    'filter' => Html::textInput('business_name', $model->business_name, ['class' => 'form-control']),
                ],
                [
                    'header' => 'Tax Number',
                    'attribute' => 'business_tax_number',
                    'contentOptions' => ['style' => 'width: 15%;'],
                    'filter' => Html::textInput('tax_number', $model->business_tax_number, ['class' => 'form-control']),
                ],
                [
                    'header' => 'Tax Country',
                    'attribute' => 'country_name',
                    'contentOptions' => ['style' => 'width: 15%;'],
                    'filter' => Html::textInput('country_name', $model->country_name, ['class' => 'form-control']),
                ],
                [
                    'header' => 'Status',
                    'attribute' => 'orders_tax_customers_status',
                    'value' => function ($data) {
                        return TaxForm::getApplicationStatus($data["orders_tax_customers_status"]);
                    },
                    'contentOptions' => ['style' => 'width: 15%;'],
                    'filter' => Html::dropDownList('status', $model->orders_tax_customers_status,
                        TaxForm::getApplicationStatusList(), [
                            'prompt' => 'Select',
                            'class' => 'form-control selectpicker m-datatable__pager-size',
                        ]),
                ],
                [
                    'header' => 'Action',
                    'class' => 'yii\grid\ActionColumn',
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'template' => mdm\admin\components\Helper::filterActionColumn('{customer/update} {tax/verify} {tax/fail}'),
                    'buttons' => [
                        'customer/update' => function ($url, $model, $key) {
                            return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', Url::to([
                                'customer/update',
                                'id' => $model['customers_id']
                            ]), ['title' => 'View']
                            );
                        },
                        'tax/verify' => function ($url, $model, $key) {
                            if ($model['orders_tax_customers_status'] == 1 || $model['orders_tax_customers_status'] == 3) {
                                return false;
                            } else {
                                return Html::a('<span class="fa fa-user-check"></span>', null, [
                                    'href' => 'javascript:void(0);',
                                    'onclick' => 'updateStatus("' . Url::to([
                                            '/tax/update-application-status',
                                            'id' => $model['id'],
                                            'status' => 1
                                        ]) . '")',
                                ]);
                            }
                        },
                        'tax/fail' => function ($url, $model, $key) {
                            if ($model['orders_tax_customers_status'] == 2 || $model['orders_tax_customers_status'] == 3) {
                                return false;
                            } else {
                                return Html::a('<span class="fa fa-ban"></span>', null, [
                                    'href' => 'javascript:void(0);',
                                    'onclick' => 'updateStatus("' . Url::to([
                                            '/tax/update-application-status',
                                            'id' => $model['id'],
                                            'status' => 2
                                        ]) . '")',
                                ]);
                            }
                        },
                    ],
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>

<?php
Modal::begin([
    'title' => 'Business Tax',
    'id' => 'generalModal',
    'size' => Modal::SIZE_LARGE
]);

Modal::end();
?>

<script type="text/javascript">
    function updateStatus(url) {
        $.ajax({
            method: "GET",
            url: url,
            dataType: 'json'
        }).done(function (data) {
            jQuery("#generalModal .modal-body").html(data['body']);
            jQuery("#generalModal .modal-title").text(data['title']);
            jQuery("#generalModal").modal('show');
        });
    }
</script>