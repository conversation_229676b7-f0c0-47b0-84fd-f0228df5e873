<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

$this->title = 'Tax';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = 'Tax';
$this->params['layoutContent'] = 'block';
?>

<div class="m-portlet m-portlet--mobile">
    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute('tax/create'); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?php
        echo GridView::widget([
            'dataProvider' => $dataProvider,
            'layout' => '{summary} {items}',
            'filterModel' => $model,
            'filterSelector' => '#pageSize',
            'columns' => [
                [
                    'header' => 'Country',
                    'attribute' => 'country_name',
                    'filter' => Html::textInput('country', $model->country_name, ['class' => 'form-control'])
                ],
                [
                    'header' => 'Currency',
                    'attribute' => 'currency',
                    'filter' => Html::textInput('currency', $model->currency, ['class' => 'form-control'])
                ],
                [
                    'header' => 'Sales Tax (%)',
                    'attribute' => 'orders_tax_percentage',
                    'filter' => Html::textInput('sales_tax', $model->orders_tax_percentage, ['class' => 'form-control'])
                ],
                [
                    'header' => 'Status',
                    'attribute' => 'orders_tax_status',
                    'content' => function ($model) {
                        return ($model['orders_tax_status'] == '1' ? '<span class="m-badge m-badge--info m-badge--wide m-badge--rounded">Active</span>' : '<span class="m-badge m-badge--metal m-badge--wide m-badge--rounded">Inactive</span>');
                    },
                ],
                [
                    'header' => 'Business Tax (%)',
                    'attribute' => 'business_tax_percentage',
                    'filter' => Html::textInput('business_tax', $model->business_tax_percentage, ['class' => 'form-control'])
                ],
                [
                    'header' => 'Action',
                    'class' => 'yii\grid\ActionColumn',
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'template' => mdm\admin\components\Helper::filterActionColumn('{update} {business} {rules}'),
                    'buttons' => [
                        'business' => function ($url, $model, $key) {
                            return Html::a('<span class="fa fa-edit"></span>', Url::to([
                                                'tax/business',
                                                'id' => $model['orders_tax_id']
                                            ]), ['title' => 'Business Tax Form']
                            );
                        },
                        'rules' => function ($url, $model, $key) {
                            return Html::a('<span class="fas fa-list"></span>', Url::to([
                                'tax/rules',
                                'id' => $model['orders_tax_id']
                            ]), ['title' => 'Tax Profile Rules']
                            );
                        },
                    ]
                ],
            ]
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>