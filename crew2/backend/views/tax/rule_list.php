<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

$this->title = 'Tax';

$this->params['breadcrumbs'][] = 'Finance';
$this->params['breadcrumbs'][] = 'Tax';
$this->params['breadcrumbs'][] = 'Tax Profile Rules';
$this->params['breadcrumbs'][] = $country_name;
$this->params['layoutContent'] = 'block';

$status_list = (Yii::$app->enum->getStatus('listData'));
?>

<div class="game-publisher-index m-portlet m-portlet--mobile">

    <div class="m-portlet__head">
        <div class="m-portlet__head-caption">
            <div class="m-portlet__head-title">
                <h3 class="m-portlet__head-text">
                    &nbsp;
                </h3>
            </div>
        </div>

        <div class="m-portlet__head-tools">
            <div class="m-portlet__head-tools">
                <div class="m--align-right">
                    <a href="<?= Url::toRoute(['tax/create-rule','id'=>$orders_tax_id]); ?>" class="btn btn-accent m-btn m-btn--custom m-btn--icon m-btn--air m-btn--pill">
                        <span>
                            <i class="la la-plus"></i>
                            <span>New</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="m-portlet__body">
        <?=
        GridView::widget([
            'dataProvider' => $dataProvider,
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
['attribute' => 'Description', 'value' => 'taxRule.description'],
['attribute' => 'Rule Type', 'value' => 'taxRule.rule_type'],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => \mdm\admin\components\Helper::filterActionColumn('{update} {action}'),
                    'contentOptions' => ['style' => 'white-space: nowrap;'],
                    'buttons' => [
                        'update' => function ($url, $model, $key) {
                            return Html::a('<span class="fas fa-pen"></span>', Url::to([
                                'tax/update-rule',
                                'id' => $model['tax_rule_id'],
                            ]), ['title' => 'Edit Tax Rule']
                            );
                        },
                        'action' => function ($url, $model, $key) {
            if($model['taxRule']['status'] == '1'){
                return Html::a('<span class="fas fa-times-circle"></span>', Url::to([
                    'tax/disable-rule',
                    'id' => $model['tax_rule_id']
                ]), ['title' => 'Disable Tax Rule']
                );
            }else{
                return Html::a('<span class="fas fa-check-circle"></span>', Url::to([
                    'tax/enable-rule',
                    'id' => $model['tax_rule_id']
                ]), ['title' => 'Enable Tax Rule']
                );
            }
                        },
                    ]
                ],
            ],
        ]);
        ?>
        <?= \backend\widgets\MetronicPager::renderPager($dataProvider); ?>
    </div>
</div>