<?php

namespace backend\widgets;

use Yii;
use yii\helpers\Html;

class MetronicPager
{

    /**
     * @return string
     * @throws \Exception
     * @var $dataProvider \yii\data\ActiveDataProvider
     */
    public static function renderPager($dataProvider)
    {
        $pages = new \yii\data\Pagination([
            'defaultPageSize' => $dataProvider->pagination->pageSize,
            'totalCount' => $dataProvider->totalCount,
            'pageSizeLimit' => false,
        ]);

        $str = '<form method="get"><div class=" dataTables_wrapper m_datatable m-datatable m-datatable--default m-datatable--loaded m-datatable--scroll"><div class="m-datatable__pager m-datatable--paging-loaded clearfix">';

        $str .= '<div class="dataTables_paginate paging_simple_numbers m-datatable__pager-nav">';

        $str .= \yii\widgets\LinkPager::widget([
            'firstPageLabel' => ' << ',
            'lastPageLabel' => ' >> ',
            'hideOnSinglePage' => false,
            'pagination' => $pages,
            'options' => [
                'class' => 'pagination'
            ],
            'pageCssClass' => 'paginate_button page-item',
            'firstPageCssClass' => 'paginate_button page-item previous',
            'prevPageCssClass' => 'paginate_button page-item previous',
            'lastPageCssClass' => 'paginate_button page-item previous',
            'nextPageCssClass' => 'paginate_button page-item previous',
        ]);

        $str .= '</div>';

        $str .= '<div class="m-datatable__pager-info">';

        $str .= Html::dropDownList('pageSize', $dataProvider->pagination->pageSize, [
            '20' => '20',
            '50' => '50',
            '100' => '100',
            '200' => '200',
        ], [
                'onchange' => 'this.form.submit()',
                'class' => 'selectpicker m-datatable__pager-size paging-width',
            ]
        );

        foreach ($_GET as $key => $value) {
            if ($key !== 'pageSize') {
                if (is_array($value)) {
                    foreach ($value as $new_key => $new_val) {
                        $name = $key . '[' . $new_key . ']';
                        $str .= Html::input('text', $name, $new_val, ['style' => 'display:none']);
                    }
                } else {
                    $str .= Html::input('text', $key, $value, ['style' => 'display:none']);
                }
            }
        }

        $str .= '</div></div></div></form>';

        return $str;
    }

}