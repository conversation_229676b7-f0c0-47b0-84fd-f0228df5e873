<?php

namespace common\components;

use common\components\AWSS3;
use common\components\AWSSes;
use common\components\AWSApiGateway;

use Aws\Sdk;

use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;

class AWS extends Component
{

    /**
     * @var
     */
    public $key;
    public $secret;
    public $region;
    public $version;

    public $s3;
    public $ses;
    public $ag;
    public $sqs;
    public $sns;
    public $aml;
    public $cf;

    private $_config;
    private $_sdk = null;

    private $instances = [];

    /**
     * Init Component
     */
    public function init()
    {
        if (!$this->key) {
            throw new InvalidConfigException("Key can't be empty!");
        }
        if (!$this->secret) {
            throw new InvalidConfigException("Secret can't be empty!");
        }
        if (!$this->region) {
            throw new InvalidConfigException("Region can't be empty!");
        }
        $this->_config = [
            'version' => $this->version,
            'region' => $this->region,
            'credentials' => [
                'key' => $this->key,
                'secret' => $this->secret,
            ],
        ];
    }

    /**
     * @return \Aws\Sdk
     */
    public function getSdk()
    {
        if ($this->_sdk === null) {
            $this->_sdk = new Sdk($this->_config);
        }
        return $this->_sdk;
    }

    public function reportError($class, $function, $message, \Exception $ex = null)
    {
        $subject = "AWS Reporting";

        $content = "Class: " . $class . "\n" . "Function: " . $function . "\n" . "Request : " . getenv("REQUEST_URI") . " [ " . date("F j, Y H:i") . " ]\n\n" . "Message: \n" . $message;
        if ($ex) {
            $content .= "\n\nError: \n\n" . $ex->__toString();
        }

        Yii::$app->slack->send($subject, array(
            array(
                'color' => 'warning',
                'text' => $content
            )
        ));
    }

    /**
     * @return \common\components\AWSS3
     */
    public function getS3($bucket_tag = 'default')
    {
        if (empty($this->s3[$bucket_tag])) {
            throw new InvalidConfigException("Bucket tag " . $bucket_tag . " is not configured!");
        }
        if (empty($this->instances['s3'][$bucket_tag])) {
            $this->instances['s3'][$bucket_tag] = new AWSS3($this, $bucket_tag, $this->s3[$bucket_tag]);
        }
        return $this->instances['s3'][$bucket_tag];
    }

    /**
     * @return \common\components\AWSCF
     */
    public function getCF()
    {
        if (empty($this->instances['cf'])) {
            $this->instances['cf'] = new AWSCF($this, $this->cf);
        }
        return $this->instances['cf'];
    }


    /**
     * @return \common\components\AWSSES
     */
    public function getSes()
    {
        if (empty($this->instances['ses'])) {
            $this->instances['ses'] = new AWSSes($this, $this->ses);
        }
        return $this->instances['ses'];
    }

    /**
     * @return \common\components\AWSApiGateway
     */
    public function getAG()
    {
        if (empty($this->instances['ag'])) {
            $this->instances['ag'] = new AWSApiGateway($this, $this->ag);
        }
        return $this->instances['ag'];
    }

    /**
     * @return \common\components\AWSMachineLearning
     */
    public function getAml($model)
    {
        $config = [];
        switch ($model) {
            case 'cc-general':
                $config = [
                    'key' => \Yii::$app->params['aws.aml.ccgen.key'],
                    'secret' => \Yii::$app->params['aws.aml.ccgen.secret'],
                    'region' => '',
                    'endpoint' => \Yii::$app->params['aws.aml.ccgen.endpoint'],
                    'model' => \Yii::$app->params['aws.aml.ccgen.model'],
                ];
                break;
            case 'general':
                $config = [
                    'key' => \Yii::$app->params['aws.aml.gen.key'],
                    'secret' => \Yii::$app->params['aws.aml.gen.secret'],
                    'region' => '',
                    'endpoint' => \Yii::$app->params['aws.aml.gen.endpoint'],
                    'model' => \Yii::$app->params['aws.aml.gen.model'],
                ];
                break;
        }
        if (!$config['key'] || !$config['secret']) {
            return 'no permission';
        } else {
            if (empty($this->instances['aml'])) {
                $this->instances['aml'] = new AWSMachineLearning($this, $config);
            }
        }

        return $this->instances['aml'];
    }

    /**
     * @return \common\components\AWSSqs
     */
    public function getSqs($queue_tag)
    {
        if (empty($this->sqs[$queue_tag])) {
            throw new InvalidConfigException("Queue tag " . $queue_tag . " is not configured!");
        }
        if (empty($this->instances['sqs'][$queue_tag])) {
            $this->instances['sqs'][$queue_tag] = new AWSSqs($this, $queue_tag, $this->sqs[$queue_tag]);
        }
        return $this->instances['sqs'][$queue_tag];
    }

    /**
     * @return \common\components\AWSSns
     */
    public function getSns($notification_tag)
    {
        if (empty($this->sns[$notification_tag])) {
            throw new InvalidConfigException("Notification tag " . $notification_tag . " is not configured!");
        }
        if (empty($this->instances['sns'][$notification_tag])) {
            $this->instances['sns'][$notification_tag] = new AWSSns($this, $notification_tag, $this->sns[$notification_tag]);
        }
        return $this->instances['sns'][$notification_tag];
    }
}
