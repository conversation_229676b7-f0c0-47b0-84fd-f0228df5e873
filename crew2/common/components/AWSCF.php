<?php

namespace common\components;

use Yii;


class AWSCF
{

    /**
     * @var \common\components\AWS
     */
    private $_aws;

    /**
     * @var \Aws\Ses\CloudFrontClient
     */
    private $_instance;

    public function __construct(\common\components\AWS $aws = null, $config = false)
    {
        if (!isset($aws)) {
            $aws = Yii::$app->aws;
            if ($config === false) {
                $config = $aws->ses;
            }
        }
        if ($config === false) {
            $config = [];
        }
        $this->_aws = $aws;
        $extra_args = [];
        if (!empty($config['region'])) {
            $extra_args['region'] = $config['region'];
        }
        if (!empty(Yii::$app->params['proxy'])) {
            $extra_args['http'] = [
                'proxy' => Yii::$app->params['proxy']
            ];
        }
        if (!empty($config['key'])) {
            $extra_args['credentials']['key'] = $config['key'];
        }
        if (!empty($config['secret'])) {
            $extra_args['credentials']['secret'] = $config['secret'];
        }
        $this->_instance = $aws->getSdk()->createCloudFront($extra_args);
    }

    /**
     * @return \Aws\Ses\CloudFrontClient the instance
     */
    public function getInstance()
    {
        return $this->_instance;
    }

    public function invalidateCache($distribution_id, $file_list)
    {
        if (!$this->_instance) {
            return;
        }
        try {
            $this->_instance->createInvalidation([
                'DistributionId' => $distribution_id,
                'InvalidationBatch' => [
                    'CallerReference' => time(),
                    'Paths' => [
                        'Items' => $file_list,
                        'Quantity' => count($file_list)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }
    }

    protected function reportError($functionName, $message, $exception = null)
    {
        $this->_aws->reportError(get_called_class(), $functionName, $message, $exception);
    }
}
