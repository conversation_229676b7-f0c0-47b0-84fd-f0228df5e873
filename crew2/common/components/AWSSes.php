<?php

namespace common\components;

use Yii;
use common\models\MailLetter;

//http://docs.aws.amazon.com/aws-sdk-php/v3/api/api-email-2010-12-01.html

class AWSSes implements \common\components\MailerInterface {

    /**
     * @var \common\components\AWS
     */
    private $_aws;

    /**
     * @var \Aws\Ses\SesClient
     */
    private $_instance;

    public function __construct(\common\components\AWS $aws = null, $config = false) {
        if (!isset($aws)) {
            $aws = Yii::$app->aws;
            if ($config === false) {
                $config = $aws->ses;
            }
        }
        if ($config === false) {
            $config = [];
        }
        $this->_aws = $aws;
        $extra_args = [];
        if (!empty($config['region'])) {
            $extra_args['region'] = $config['region'];
        }
        if (!empty(Yii::$app->params['proxy'])) {
            $extra_args['http'] = [
                'proxy' => Yii::$app->params['proxy']
            ];
        }
        if (!empty($config['key'])) {
            $extra_args['credentials']['key'] = $config['key'];
        }
        if (!empty($config['secret'])) {
            $extra_args['credentials']['secret'] = $config['secret'];
        }
        $this->_instance = $aws->getSdk()->createSes($extra_args);
    }

    /**
     * @return \Aws\Ses\SesClient the instance
     */
    public function getInstance() {
        return $this->_instance;
    }

    /**
     * @inheritdoc
     */
    public function sendEmail(MailLetter $letter) {
        if (!$this->_instance) {
            return false;
        }
        if ($this->isThrottleStatus()) {
            return false;
        }
        return $this->_sendEmail($letter);
    }

    protected function _sendEmail(MailLetter $letter) {
        $return = false;
        try {
            //$result = $this->_instance->sendEmail($this->parseLetter($letter));
            $args = $this->parseLetter($letter);
            $command = $this->_instance->getCommand('SendRawEmail', $args);
            $result = $this->_instance->execute($command);
            if (!isset($result->get('@metadata')['statusCode']) || $result->get('@metadata')['statusCode'] != 200) {
                $this->reportError(__FUNCTION__, "Return status: " . var_export($result, true));
            } else {
                $return = $result->get('MessageId');
            }
        } catch (\Aws\Exception\AwsException $e) {
            if (strtolower($e->getAwsErrorCode()) == 'throttling') {
                $this->setThrottleStatus($letter);
                $this->reportError(__FUNCTION__, "Aws Throttling", $e);
            } else {
                $this->reportError(__FUNCTION__, "Unhandled Aws Exception", $e);
            }
        } catch (\Exception $e) {
            $this->reportError(__FUNCTION__, "Unknown Exception", $e);
        }

        return $return;
    }

    protected function parseLetter(MailLetter $letter) {
        $letter_opt = [
            'Destination' => [
                'ToAddresses' => Yii::$app->mailer->formatEmailArray($letter->getTo()),
            ],
            'RawMessage' => [
                'Data' => $letter->buildRawMail(Yii::$app->mailer->lineFeed),
            ],
            'Source' => Yii::$app->mailer->formatEmailArray($letter->getFrom(), true),
        ];
        if (!empty($letter->getCc())) {
            $letter_opt['Destination']['CcAddresses'] = Yii::$app->mailer->formatEmailArray($letter->getCc());
        }
        if (!empty($letter->getBcc())) {
            $letter_opt['Destination']['BccAddresses'] = Yii::$app->mailer->formatEmailArray($letter->getBcc());
        }
        return $letter_opt;
    }

    protected function setThrottleStatus(MailLetter $letter) {
        $ses_status = $this->_getStatus();
        if ($ses_status['status'] != 'LOCKED') {
            $cache_key = $ses_status['key'];
            unset($ses_status['key']);

            $get_remaining_time = Yii::$app->params['aws.ses.throttleDuration'];
            $ses_status = array('duration' => $get_remaining_time, 'status' => 'LOCKED');
            MemCache::set($cache_key, $ses_status, $get_remaining_time, MemCache::CACHE_PERFORMANCE);
        }
    }

    protected function isThrottleStatus() {
        $ses_status = $this->_getStatus();
        return $ses_status['status'] == 'LOCKED';
    }

    protected function _getStatus() {
        $cache_key = get_called_class() . '/Status/array';
        $return_array = MemCache::get($cache_key);

        if ($return_array === FALSE) {
            $return_array = [
                'status' => '',
            ];
        }

        $return_array['key'] = $cache_key;

        return $return_array;
    }

    protected function reportError($functionName, $message, $exception = null) {
        $this->_aws->reportError(get_called_class(), $functionName, $message, $exception);
    }
}
