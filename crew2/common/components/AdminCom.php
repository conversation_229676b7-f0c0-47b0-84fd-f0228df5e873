<?php

namespace common\components;

use Yii;
use common\models\Admin;
use common\models\AdminCreditLimit;

class AdminCom
{

    public static function checkAdminCreditLimit($amount, $currency)
    {
        $actionAllowed = false;
        $message = Yii::t('store-credit', 'You had reached the allowed daily send money limit');

        $adminEmail = Yii::$app->user->identity->username;
        $addAmountInUsd = Yii::$app->currency->advanceCurrencyConversion($amount, $currency, 'USD', true, 'sell');

        $adminInfo = Admin::find()->select(['admin_id'])->andWhere(['admin_email_address' => $adminEmail])->one();
        if ($adminInfo) {
            $adminCreditLimitInfo = AdminCreditLimit::findOne($adminInfo['admin_id']);
            if ($adminCreditLimitInfo) {
                if ($adminCreditLimitInfo->admin_credit_limit_total + $addAmountInUsd <= $adminCreditLimitInfo->admin_credit_limit_max) {
                    $actionAllowed = true;
                    // Update limit total
                    $adminCreditLimitInfo->admin_credit_limit_total = $adminCreditLimitInfo->admin_credit_limit_total + $addAmountInUsd;
                    $adminCreditLimitInfo->save();
                }
            } else {
                $message = Yii::t('store-credit', 'Your allowed daily send money limit is not set');
            }
        } else {
            $message = Yii::t('store-credit', 'Your allowed daily send money limit is not set');
        }

        return ['status' => $actionAllowed, 'message' => $message];
    }

    public function checkCreditLimit($currency, $amount, $deduct = false)
    {
        $usd_amt = Yii::$app->currency->advanceCurrencyConversion($amount, $currency, 'USD', true, 'sell');
        $m_adm = Admin::findOne(['admin_email_address' => Yii::$app->user->identity->username]);
        if (isset($m_adm->admin_id)) {
            $m_acl = AdminCreditLimit::findOne(['admin_id' => $m_adm->admin_id]);
            $spent = $m_acl->admin_credit_limit_total + $usd_amt;
            if (isset($m_acl->admin_id) && ($m_acl->admin_credit_limit_max >= $spent)) {
                if ($deduct) {
                    $m_acl->admin_credit_limit_total = $spent;
                    return $m_acl->save();
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    public static function reverseAdminCreditLimit($amount, $currency)
    {
        $adminEmail = Yii::$app->user->identity->username;
        $addAmountInUsd = Yii::$app->currency->advanceCurrencyConversion($amount, $currency, 'USD', true, 'sell');

        $adminInfo = Admin::find()->select(['admin_id'])->andWhere(['admin_email_address' => $adminEmail])->one();
        if ($adminInfo) {
            $adminCreditLimitInfo = AdminCreditLimit::findOne($adminInfo['admin_id']);
            if ($adminCreditLimitInfo) {
                if ($adminCreditLimitInfo->admin_credit_limit_total - $addAmountInUsd >= 0 && $adminCreditLimitInfo->admin_credit_limit_total != 0) {
                    // Update limit total
                    $adminCreditLimitInfo->admin_credit_limit_total = $adminCreditLimitInfo->admin_credit_limit_total - $addAmountInUsd;
                } else {
                    $adminCreditLimitInfo->admin_credit_limit_total = 0;
                }
                $adminCreditLimitInfo->save();
            }
        }
    }
}
