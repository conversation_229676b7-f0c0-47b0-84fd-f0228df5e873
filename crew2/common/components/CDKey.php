<?php

namespace common\components;

use common\models\CustomProductsCode;
use common\models\CustomProductsCodeLog;
use Yii;

class CDKey
{

    public function getCdkViewLog($cpcId)
    {
        $log = [];
        $info = '';

        $model = CustomProductsCodeLog::find()
                ->andWhere(['custom_products_code_id' => $cpcId])
                ->andWhere(['custom_products_code_log_user_role' => 'admin'])
                ->andWhere(['REGEXP', 'log_system_messages', '(<b>CD Key Status</b>: Actual --> Sold)'])
                ->orderBy(['custom_products_code_log_id' => SORT_DESC])
                ->one();

        if ($model) {
            $log['datetime'] = $model->log_time;
            $info = $model->log_time . ' - ' . strip_tags($model->log_system_messages) . "\n\n";
        }

        $model2 = CustomProductsCodeLog::find()
                ->andWhere(['custom_products_code_id' => $cpcId])
                ->andWhere(['custom_products_code_log_user_role' => 'customers'])
                ->andWhere(['REGEXP', 'log_system_messages', '(<b>CD Key View Status</b>: Not Viewed --> Viewed)'])
                ->orderBy(['custom_products_code_log_id' => SORT_DESC])
                ->one();

        if ($model2) {
            $info .= $model2->log_time . ' - Changes made:' . "\n" . 'CD Key View Status: Not Viewed (Downloaded) --> Viewed (Downloaded)';
        }

        $log['info'] = $info;

        return $log;
    }

    public function getCdkeyImg($cpcId)
    {
        $productCodeInfo = CustomProductsCode::findOne($cpcId);
        if (isset($productCodeInfo->custom_products_code_id)) {
            $customProductsCodeId = $productCodeInfo->custom_products_code_id;
            $toS3 = $productCodeInfo->to_s3;

            $theData = self::getCode($customProductsCodeId, $toS3);

            return ($theData !== false ? self::decryptData($theData) : '');
        }
    }

    public function getCode($cpcId, $isS3 = null)
    {
        $customProductCode = CustomProductsCode::findOne($cpcId);
        if ((int) $isS3 == 1) {
            return self::getFromAWS($customProductCode);
        }
    }

    private function getFromAWS($customProductCode)
    {
        $codeAddDatetime = $customProductCode->code_date_added;
        $productId = $customProductCode->products_id;

        $s3 = Yii::$app->aws->getS3('BUCKET_SECURE');

        $dir = date('Ym', strtotime($codeAddDatetime));
        $filename = $customProductCode->custom_products_code_id . '.key';
        $filepath = $dir . '/' . $productId . '/';

        $theData = $s3->getContent($filepath . $filename);

        if ($theData != '') {
            return $theData;
        }

        return false;
    }

    public static function decryptData($theData)
    {
        if ($theData) {
            $key = Yii::$app->params['cdk.secure.key'];
            $iv = Yii::$app->params['cdk.secure.key.iv'];

            $theData = substr($theData, 20);
            $theData = openssl_decrypt($theData, "AES-256-CBC", $key, OPENSSL_ZERO_PADDING, $iv);

            $theData = rtrim($theData, "\0");
            $theData = base64_decode($theData);
        }
        return $theData;
    }

    public function maskCdkey($theData, $type)
    {
        if ($type == 'soft') {
            // Split html tag <br>
            $mainSplit = preg_split('/(<br>)/', $theData);
            // clean empty array
            $main = array_filter($mainSplit);

            $finalStr = '';
            if (count($main) > 1) {
                foreach ($main as $result) {
                    $finalStr .= self::generateMaskStr($result);
                }
            } else {
                $newString = strip_tags($main[0]);
                $finalStr .= substr_replace($newString, '******', -1, 6);
            }
            return $finalStr;
        } else {
            switch (strtolower($type)) {
                case 'jpg':
                    $filetype = 'jpeg';
                    break;
                default:
                    $filetype = $type;
            }

            $imageTypeProcessing = 'image' . $filetype;

            $imageDecode = imagecreatefromstring($theData);
            imagepalettetotruecolor($imageDecode);

            $xSize = imagesx($imageDecode) - (imagesx($imageDecode) * 0.3);
            $ySize = imagesy($imageDecode);

            // Iterate over all pixels
            for ($x = 0; $x < $xSize; $x++) {
                for ($y = 0; $y < $ySize; $y++) {
                    // Get color, and transparency of this pixel
                    $col = imagecolorat($imageDecode, $x, $y);
                    // Extract alpha
                    $alpha = ($col & 0x7F000000) >> 24;
                    // Make black with original alpha
                    $repl = imagecolorallocatealpha($imageDecode, 0, 0, 0, $alpha);
                    // Replace in image
                    imagesetpixel($imageDecode, $x, $y, $repl);
                }
            }

            ob_start();
            $imageTypeProcessing($imageDecode);
            $imageData = ob_get_contents();
            ob_end_clean();

            $imageDataBase64 = base64_encode($imageData);

            return $base64 = 'data:image/' . $filetype . ';base64,' . $imageDataBase64;
        }
    }

    private function generateMaskStr($string)
    {
        // clean all html tags
        $newString = strip_tags($string);
        // check if : exists in the $newString
        if (strpos($newString, ':') === false) {
            return $newString . "\n";
        }
        // split caption and actual key when the first : found
        $split = explode(':', $newString, 2);
        // mask first 6 char
        $split[1] = substr_replace(trim($split[1]), '******', 0, 6);
        // generate new string for Pipwave RFI
        return implode(' : ', $split) . "\n";
    }

}
