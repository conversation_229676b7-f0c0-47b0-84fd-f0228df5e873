<?php

namespace common\components;

use Yii;

class CsvReportSender extends ErrorReportCom
{
    private string $filename;

    public function __construct(string $filename)
    {
        $this->filename = $filename;
    }

    /**
     * @inheritdocs
     */
    public function sendCsvMail($csvData, $csvHeader, $body, $to, $subject, $from)
    {
        $mailer = Yii::$app->mailer->compose()
            ->setFrom($from)
            ->setReplyTo($from)
            ->setTo($to)
            ->setSubject($subject)
            ->setHtmlBody($body);

        if (empty($csvData)) {
            return $mailer->send();
        }

        return $mailer
            ->attachContent(
                $this->createCsvString($csvData, $csvHeader),
                ['fileName' => $this->filename, 'contentType' => 'text/csv'])
            ->send();
    }

}