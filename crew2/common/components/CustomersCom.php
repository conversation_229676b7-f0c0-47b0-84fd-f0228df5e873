<?php

namespace common\components;

use Yii;
use common\models\Customers;

class CustomersCom
{
    // generate log string for customer info update
    private function getCustomersLogString($customerChangesFormattedArray)
    {
        $changesStr = '';

        if (count($customerChangesFormattedArray)) {
            for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                if (count($customerChangesFormattedArray[$i])) {
                    foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                        if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                            $changesStr = $res['title_text'] . "\n";
                            $changesStr .= $res['text'] . "\n";
                        } else {
                            $changesStr = $res['title_text'] . "\n";
                            $changesStr .= '<b>' . $res['text'] . '</b>: ';
                            $changesStr .= $res['from'] . (trim($res['to']) != '' ? ' --> ' . $res['to'] : '') . "\n";
                        }
                    }
                }
            }
        }

        return $changesStr;
    }

    // Check if customers info exist
    public function getCustomersInfo($customersId)
    {
        $custObj = Customers::findOne($customersId);
        
        if ($custObj) {
            return $custObj;
        }

        return false;
    }

    // update customers info for account closure
    public function setCustomersAccountClosure($customersId)
    {
        $closureStatus = false;
        $logFiles = new \common\components\LogFilesCom('system');

        // Update customers info
        $custObj = Customers::findOne($customersId);
        // set old status
        $customersStatusRowOld['customers_status'] = $custObj->customers_status;
        // set closure value
        $custObj->customers_email_address = 'erasure.' . $customersId . '@usergdprrequest.com';
        $custObj->customers_firstname = '';
        $custObj->customers_lastname = '';
        $custObj->customers_telephone = '';
        $custObj->customers_status = 9;
        $custObj->customers_aft_groups_id = 2; // TL1

        // get all address book
        $addBookObj = \common\models\AddressBook::findOne($custObj->customers_default_address_id);
        // set closure value
        $addBookObj->entry_firstname = '';
        $addBookObj->entry_lastname = '';
        $addBookObj->entry_street_address = '';
        $addBookObj->entry_suburb = '';
        $addBookObj->entry_postcode = '';
        $addBookObj->entry_city = '';
        $addBookObj->entry_state = '';
        $addBookObj->entry_country_id = '';
        $addBookObj->entry_zone_id = '';

        if ($addBookObj->save() && $custObj->save()) {
            $customersStatusRowNew['customers_status'] = $custObj->customers_status;
            // Log customers history
            $customersStatusArray = $logFiles->detectChanges($customersStatusRowOld, $customersStatusRowNew);
            $customerChangesFormattedArray = $logFiles->constructLogMessage($customersStatusArray);

            $changesStr = $this->getCustomersLogString($customerChangesFormattedArray);

            $logFiles->insertCustomerHistoryLog($custObj->customers_id, $changesStr);

            $closureStatus = true;
        }

        unset($custObj);
        unset($addBookObj);
        unset($logFiles);

        return $closureStatus;
    }
}
