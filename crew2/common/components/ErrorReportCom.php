<?php

namespace common\components;

use Yii;

class ErrorReportCom
{
    private $container = [];

    private $alertSetting = [
        'cron_alert_1' => [
            'setting' => [
                'icon_emoji' => ':inbox_tray:',
                'attachments' => [[
                    'color' => 'danger',
                    'text' => 'Alert!',
                    'pretext' => '[OG] Cronjob Failed',
                    'fields' => []
                ]]
            ],
            'field_pattern' => [
                'title' => '{alert_title}',
                'value' => '{alert_value}',
                'short' => false
            ]
        ],
    ];
    
    private function t($message, $params = [])
    {
        $p = [];
        foreach ((array) $params as $name => $value) {
            $p['{' . $name . '}'] = $value;
        }
        return ($p === []) ? $message : strtr($message, $p);
    }

    protected function createCsvString($csvData, $csvHeader)
    {
        // Open temp file pointer
        if (!$fp = fopen('php://temp', 'w+')) {
            return false;
        }
        
        fputcsv($fp, $csvHeader);
        
        // Loop data and write to file pointer
        foreach ($csvData as $data) {
            fputcsv($fp, $data);
        }
        
        // Place stream pointer at beginning
        rewind($fp);

        // Return the data
        return stream_get_contents($fp);
    }

    private function fireNotification()
    {
        if ($this->container) {
            $curlObj = new \common\components\CurlCom();
            
            foreach ($this->container as $alertType => $alertDataArray) {
                $alert_info = $this->alertSetting[$alertType];
                $url = isset(Yii::$app->params['slack.url']) ? Yii::$app->params['slack.url'] : '';
                
                $curlObj->ssl_verification = isset(Yii::$app->params['slack.sslVerification']) ? Yii::$app->params['slack.sslVerification'] : '';
                
                if ($url) {
                    $alertSetting = $alert_info['setting'];
                    $alert_field = $alert_info['field_pattern'];
                    
                    foreach ($alertDataArray as $data) {
                        $field = $alert_field;
                        $field['title'] = $this->t($alert_field['title'], $data);
                        $field['value'] = $this->t($alert_field['value'], $data);
                        
                        $alertSetting['attachments'][0]['fields'][] = $field;
                    }
                    $curlObj->sendPost($url, json_encode($alertSetting));
                }
            }
            
            unset($curlObj);
        }
    }

    public function errorReport($alertTitle, $alertValue, $errorType = 'cron')
    {
        // set alert trype
        switch ($errorType) {
            default:
                $alertType = 'cron_alert_1';
                break;
        }

        // process alert content
        $data = [
            'alert_title' => $alertTitle,
            'alert_value' => json_encode($alertValue),
        ];

        $this->container[$alertType][] = $data;

        // fire notifications
        $this->fireNotification();
    }

    public function sendCsvMail($csvData, $csvHeader, $body, $to, $subject, $from)
    {
        // Send the email, return the result
        return Yii::$app->mailer->compose()
            ->setFrom($from)
            ->setReplyTo($from)
            ->setTo($to)
            ->setSubject($subject)
            ->setHtmlBody($body)
            ->attachContent($this->createCsvString($csvData, $csvHeader), ['fileName' => 'attach.csv', 'contentType' => 'text/csv'])
            ->send();
    }
}
