<?php

namespace common\components;

use Yii;

class ExportCSV {

    public function __construct() {

    }

    public static function download($data, $header = [], $filename = null, $separator = ';') {
        /*         * *
         * export a data set to CSV output
         * @data        array
         * @header      example: 'datakey' => array('label' => 'colname', 'format' => 'datetime / status')
         * @filename    if set (defaults null) it echoes the output to browser using binary transfer headers
         * @separator   if set (defaults to ';') specifies the separator for each CSV field
         * * */
        $br = PHP_EOL;

        header("Cache-Control: public");
        header("Content-Description: File Transfer");
        header("Content-Disposition: attachment; filename=" . $filename);
        header("Content-Type: application/octet-stream");
        header("Content-Transfer-Encoding: binary");

        if ($header) {
            $names = '';
            foreach ($header as $key => $config) {
                $names .= $config['label'] . $separator;
            }
            $names = rtrim($names, $separator);
            echo $names . $br;
        }

        foreach ($data as $row) {
            $r = '';
            foreach ($header as $key => $config) {
                if (isset($row[$key])) {
                    $val = $row[$key];
                    if (!empty($config['format'])) {
                        switch ($config['format']) {
                            case 'datetime':
                                $val = date('Y-m-d H:i:s', $val);
                                break;
                            case 'date':
                                $val = date('Y-m-d', $val);
                                break;
                            case 'time':
                                $val = date('H:i:s', $val);
                                break;
                            case 'status':
                                $val = ($val == 1 ? 'Success' : 'Fail');
                                break;
                            case 'points':
                                $val = $val * 1000;
                                break;
                        }
                    }
                    $r .= utf8_decode($val) . $separator;
                } else {
                    $r .= $separator;
                }
            }

            $item = trim(rtrim($r, $separator)) . $br;
            echo $item;
        }
        unset($data);
    }

}
