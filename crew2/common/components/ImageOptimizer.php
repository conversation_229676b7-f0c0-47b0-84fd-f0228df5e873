<?php

namespace common\components;

use yii\base\Component;

class ImageOptimizer extends Component
{
    public $profile = 'ShortPixel';

    private $client;

    public function init()
    {
        $className = 'common\components\image\\'.$this->profile;
        if (class_exists($className)) {
            $this->client = new $className;
            $this->client->init();
        } else {
            throw new \yii\base\InvalidArgumentException('Wrong Image Optimization Profile');
        }

    }

    /**
     * @return \common\components\image\ImageOptimizerBase
     */
    public function getClient()
    {
        return $this->client;
    }

    public function getSupportedRatio()
    {
        return $this->getClient()->getSupportedRatio();
    }

    public function getDefaultRatio(){
        return $this->getClient()->getDefaultRatio();
    }

    public function getSupportedMime(){
        return $this->getClient()->getSupportedMime();
    }

}