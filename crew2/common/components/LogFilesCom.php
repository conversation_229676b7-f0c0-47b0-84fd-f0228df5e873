<?php

namespace common\components;

use Yii;

class LogFilesCom
{
    var $identity;

    public function __construct($identity)
    {
        $this->identity = $identity;
    }

    function detectChanges($oldData, $newData)
    {
        $changesArray = array();
        if (count($oldData) && count($newData)) {
            foreach ($oldData as $key => $value) {
                if (strcmp($newData[$key], $value) !== 0) {
                    $changesArray[$key] = array('from'=> $value, 'to'=> $newData[$key]);
                }
            }
        }
        return $changesArray;
    }

    function constructLogMessage($changesArray)
    {
        $messageStr = array();
        if (count($changesArray)) {
            foreach ($changesArray as $key => $changes) {
                $readableArray = $this->getReadableLogInput($key, $changes['from'], $changes['to']);
                if (count($readableArray)) {
                    $messageStr[] = $readableArray;
                }
            }
        }
        
        return $messageStr;
    }

    function getReadableLogInput($fieldName, $oldVal, $newVal)
    {
        $plainResult = false;
        
        $result = array();
        $oldVal = trim($oldVal);
        $newVal = trim($newVal);

        $titleText = '';
        $text = '';
        $oldString = '';
        $newString = '';
        switch ($fieldName) {
            case 'customers_status':
                $oldString = ($oldVal == 0) ? Yii::t('logs', 'Not Active') : (($oldVal == 9) ? Yii::t('logs', 'Account Closure') : Yii::t('logs', 'Active'));
                $newString = ($newVal == 0) ? Yii::t('logs', 'Not Active') : (($newVal == 9) ? Yii::t('logs', 'Account Closure') : Yii::t('logs', 'Active'));
                $text = Yii::t('logs', 'Customers Status');
                $titleText = Yii::t('logs', 'Changes made: Set for account closure');
                break;
            default:
                $displayLabel = array(
                    'customers_firstname' => 'First Name',
                    'customers_lastname' => 'Last Name',
                    'customers_email_address' => 'E-Mail Address',
                    'customers_telephone' => 'Telephone Number',
                    'customers_fax' => 'Fax Number',
                    'customers_mobile' => 'Mobile Number',
                    'customers_discount' => 'Customer Discount Rate',
                    'customers_phone_verified' => 'Phone Verification',
                    'customers_phone_verified_datetime' => 'Phone Verification Date',
                    'entry_street_address' => 'Street Address',
                    'entry_postcode' => 'Post Code',
                    'entry_city' => 'City',
                    'entry_company' => 'Company',
                    'entry_suburb' => 'Suburb',
                    'entry_state' => 'State',
                    'customers_msn' => 'MSN',
                    'customers_qq' => 'QQ',
                    'customers_icq' => 'ICQ',
                    'customers_yahoo' => 'YAHOO',
                    'vip_buyback_cummulative_point' => 'VIP Cummulative Point',
                    'custom_products_code_products_id' => 'CD Key Product',
                    'custom_products_code_file_name' => 'File Name',
                    'custom_products_code_remarks' => 'Remarks',
                    'custom_products_code_uploaded_by' => 'Uploaded By',
                    'verified_seller' => 'Verified Seller',
                    'seller_product_listing_limit' => 'seller_product_listing_limit'
                );
                
                $oldString = (trim($oldVal) != '') ? $oldVal : "EMPTY";
                $newString = (trim($newVal) != '') ? $newVal : "EMPTY";
                $text = tep_not_null($displayLabel[$fieldName]) ? $displayLabel[$fieldName] : $fieldName;
                
                break;
        }
        
        $result[$fieldName] = array('title_text' => $titleText, 'text' => $text, 'from' => $oldString, 'to' => $newString, 'plain_result' => ($plainResult ? '1' : '0') );
        return $result;
    }

    function insertCustomerHistoryLog($customersId, $customerRemarks = '')
    {
        $remarksAddedBy = (isset($this->identity) && !empty($this->identity)) ? $this->identity : 'system';

        $insertData = ['CustomersRemarksHistory' => [
            'customers_id' => $customersId,
            'date_remarks_added' => new \yii\db\Expression('NOW()'),
            'remarks' => $customerRemarks,
            'remarks_added_by' => $remarksAddedBy,
        ]];

        $insertModel = new \common\models\CustomersRemarksHistory();
        if ($insertModel->load($insertData) && $insertModel->save()) {
            return true;
        }

        return false;
    }
}
