<?php

namespace common\components;

use common\models\Products;
use common\models\Orders;
use common\models\OrdersProducts;
use common\models\OrdersStatusHistory;
use common\models\OrdersTopUp;
use common\models\OrdersTopUpRemark;
use common\models\OrdersCustomProducts;
use common\models\CustomOrder;
use common\models\CustomProductsCode;
use common\models\CustomersTopUpInfo;
use common\components\CDKey;
use Yii;

class PipwaveRFI
{
    public $orders_id;

    public function authentication($data)
    {
        if (isset($data['action']) && isset($data['timestamp']) && isset($data['api_key']) && isset($data['txn_id']) && isset($data['signature'])) {
            $sign = $data['signature'];
            unset($data['signature']);
            unset($data['version']);
            // Assign api_key from PARAM
            $data['api_secret'] = Yii::$app->params['pipwave.rfi.secret'];

            $genAuth = $this->generateSignature($data);
            if ($genAuth == $sign) {
                $this->orders_id = $data['txn_id'];
                return true;
            }
        }

        return false;
    }

    private function generateSignature($array)
    {
        ksort($array);
        $s = "";
        foreach ($array as $key => $value) {
            $s .= $key . ':' . $value;
        }
        return sha1($s);
    }

    private function getAdminGameInput($prodId)
    {
        $gameInputArray = [];

        $sql = (new \yii\db\Query())
            ->select(['top_up_info.top_up_info_id', 'top_up_info_lang.top_up_info_display', 'top_up_info.top_up_info_key', 'top_up_info.use_function', 'top_up_info.set_function'])
            ->from('top_up_info')
            ->innerJoin('top_up_info_lang', 'top_up_info.top_up_info_id = top_up_info_lang.top_up_info_id')
            ->where('top_up_info.products_id  = ' . $prodId)
            ->andWhere('top_up_info.top_up_info_type_id = 2')
            ->andWhere('top_up_info_lang.languages_id = 1')
            ->orderBy(['top_up_info.sort_order' => SORT_ASC])
            ->all();

        foreach ($sql as $id => $row) {
            $gameInputArray[$row['top_up_info_key']] = $row;
        }

        return $gameInputArray;
    }

    private function getCustomerInputValue($ordersProductsId)
    {
        $customerInputValue = array();

        $custTopupObj = CustomersTopUpInfo::find()->where(['orders_products_id' => $ordersProductsId])->all();
        foreach ($custTopupObj as $custTopInfo) {
            $customerInputValue[$custTopInfo->top_up_info_id] = $custTopInfo->top_up_value;
        }

        return $customerInputValue;
    }

    private function getServers($prodId)
    {
        $serversArray = [];
        $sql = (new \yii\db\Query())
            ->select(['publishers_games.publishers_server'])
            ->from('publishers_products')
            ->innerJoin('publishers_games', 'publishers_products.publishers_games_id = publishers_games.publishers_games_id')
            ->where('publishers_products.products_id  = ' . $prodId)
            ->all();

        foreach ($sql as $id => $row) {
            $serversArray = json_decode($row['publishers_server'], true);
        }
        return $serversArray;
    }

    private function topUpStatus($passId = '')
    {
        $topupStatusArray = [];
        $topupStatusArray[0] = 'Unknown';
        $topupStatusArray[1] = 'Pending';
        $topupStatusArray[3] = 'Reloaded';
        $topupStatusArray[10] = 'Failed';
        $topupStatusArray[11] = 'Not Found';
        
        return (isset($topupStatusArray[(int)$passId]) ? $topupStatusArray[(int)$passId] : $topupStatusArray[0]);
    }

    private function generateDtuInfo($dtu, $gameInput, $ordersProductsId, $prodId)
    {
        $returnText = '';
        $customerInputArray = $this->getCustomerInputValue($ordersProductsId);

        foreach ($gameInput as $topupInfoKeyLoop => $topupInfoDataLoop) {
            $returnText .= $topupInfoDataLoop['top_up_info_display'] . ': ';
            if ($topupInfoKeyLoop == 'server') {
                if (!isset($directTopupServersArray[$prodId])) {
                    $directTopupServersArray[$prodId] = $this->getServers($prodId);
                }
                $returnText .= (isset($directTopupServersArray[$prodId][$customerInputArray[$topupInfoDataLoop['top_up_info_id']]]) ? $directTopupServersArray[$prodId][$customerInputArray[$topupInfoDataLoop['top_up_info_id']]] : '-' );
                $returnText .= "\n";
            } else if ($topupInfoKeyLoop == 'character') {
                if (!empty($customerInputArray[$topupInfoDataLoop['top_up_info_id']])) {
                    $returnText .= str_replace("##", " - ", $customerInputArray[$topupInfoDataLoop['top_up_info_id']]);
                } else {
                    $returnText .= "-";
                }
                $returnText .= "\n";
            } else {
                $returnText .= ( isset($customerInputArray[$topupInfoDataLoop['top_up_info_id']]) ? $customerInputArray[$topupInfoDataLoop['top_up_info_id']] : '-') . "\n";
            }
        }

        if ($dtu) {
            $returnText .= "Top-up ID: " . $dtu->top_up_id;
            $returnText .= "\n";
            $returnText .= "Top-up Status: " . $this->topUpStatus($dtu->top_up_status);
            $returnText .= "\n";
            $returnText .= "Publisher Ref ID: " . (isset($dtu->publishers_ref_id) && !empty($dtu->publishers_ref_id) ? $dtu->publishers_ref_id : '-');
            $returnText .= "\n\n";

            if ($topupRemarks = OrdersTopUpRemark::find()->where(['top_up_id' => $dtu->top_up_id])->all()) {
                foreach ($topupRemarks as $remarks) {
                    $returnText .= $remarks['data_added'] . ' : ' . $remarks['remark'] . ' : ' . $remarks['changed_by'] . "\n";
                }
            }
        }

        return $returnText;
    }

    public function getOrdersDetail()
    {
        $status = 'fail';
        $message = '';
        $data = [];

        if ($ordersObj = Orders::find()->where(['orders_id' => $this->orders_id, 'orders_status' => 8])->one()) {
            // Get products details from order_products table
            if ($opObj = OrdersProducts::find()->where(['orders_id' => $this->orders_id, 'products_bundle_id' => 0, 'parent_orders_products_id' => 0])->all()) {
                $i = 0;
                foreach ($opObj as $orders) {
                    if($orders->products_id === -1){
                        continue;
                    }
                    $data[$i]['product'] = $orders->products_name;

                    // Get product info for bundle checking
                    $bundleStatus = Products::findOne($orders->products_id);

                    if ($bundleStatus->products_bundle_dynamic == 'yes') {
                        // do nothing
                    } else if ($bundleStatus->products_bundle == 'yes') {
                        $subProducts = OrdersProducts::find()
                            ->where([
                                'orders_id' => $this->orders_id,
                                'parent_orders_products_id' => $orders->orders_products_id,
                                'orders_products_is_compensate' => 0
                            ])->all();

                        $subProdCount = 0;
                        $deliveryInfoArray = [];
                        foreach ($subProducts as $subProd) {
                            $deliveryInfoArray = $this->processProductRFI($subProd);
                            foreach ($deliveryInfoArray as $deliveryInfo) {
                                $data[$i]['delivered_qty'] = $subProdCount + 1;
                                $data[$i]['delivery_info'][] = $deliveryInfo;
                                $subProdCount++;
                            }
                        }
                    } else {
                        $data[$i]['delivered_qty'] = $orders->products_delivered_quantity;
                        $data[$i]['delivery_info'] = $this->processProductRFI($orders);
                    }

                    $status = (isset($data[$i]['delivery_info']) && !empty($data[$i]['delivery_info'])) ? 'success' : 'fail';
                    $message .= (isset($data[$i]['delivery_info']) && !empty($data[$i]['delivery_info'])) ? '' : 'Error - Delivery Info Missing';

                    // index for product
                    $i++;
                }
            } else {
                $message = 'Error - Orders Missing';
            }
        } else {
            $message = 'Error - Orders Status not On Hold / Dispute';
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'deliveries' => $data,
        ];

        return array_filter($responseArray);
    }

    private function processProductRFI($orders)
    {
        $data = [];
        // Get details from custom_product_code
        if ($cpcObj = CustomProductsCode::find()->where(['orders_products_id' => $orders->orders_products_id])->all()) {
            $x = 0;
            foreach ($cpcObj as $cdkey) {
                // Check cdk type
                if ($imgSource = CDKey::getCdkeyImg($cdkey->custom_products_code_id)) {
                    $data[$x]['content']['type'] = ($cdkey->file_type == 'soft') ? 'text' : 'image';
                    $data[$x]['content']['value'] = 'Delivered CD Code' . "\n" .CDKey::maskCdkey($imgSource, $cdkey->file_type);

                    $z = 0;
                    // Look for cdk log if view or not date
                    if ($cdkView = CDKey::getCdkViewLog($cdkey->custom_products_code_id)) {
                        $data[$x]['timeline'][$z]['datetime'] = strtotime($cdkView['datetime']);
                        $data[$x]['timeline'][$z]['info'] = $cdkView['info'];
                    }
                    // indec for cdk
                    $x++;
                }
            }
        } elseif ($dtuObj = OrdersTopUp::find()->where(['orders_products_id' => $orders->orders_products_id])->all()) {
            // DTU Products
            $x = 0;
            foreach ($dtuObj as $dtu) {
                // Check cdk type
                if ($gameInput = $this->getAdminGameInput($orders->products_id)) {
                    $data[$x]['content']['type'] = 'text';
                    $data[$x]['content']['value'] = $this->generateDtuInfo($dtu, $gameInput, $orders->orders_products_id, $orders->products_id);

                    // find orders history related to the cdk delevery
                    if ($ohObj = OrdersStatusHistory::find()->where(['orders_id' => $this->orders_id, 'comments_type' => 2, 'customer_notified' => 1])->one()) {
                        $data[$x]['timeline'][0]['datetime'] = strtotime($ohObj->date_added);
                        $data[$x]['timeline'][0]['info'] = $ohObj->date_added . ' - ' . $ohObj->comments . "\n\n";
                    }
                    // indec for cdk
                    $x++;
                }
            }
        } elseif ($orders->custom_products_type_id == 3) {
            $data[0]['content']['type'] = 'text';
            $data[0]['content']['value'] = $this->generateScInfo($orders);

            // find orders history related to the cdk delevery
            if ($ohObj = OrdersStatusHistory::find()->where(['orders_id' => $this->orders_id, 'comments_type' => 2, 'customer_notified' => 1])->one()) {
                $data[0]['timeline'][0]['datetime'] = strtotime($ohObj->date_added);
                $data[0]['timeline'][0]['info'] = $ohObj->date_added . ' - ' . $ohObj->comments . "\n\n";
            }
        }

        return $data;
    }

    private function generateScInfo($orders)
    {
        if ($ocpInfo = OrdersCustomProducts::find()->where(['orders_products_id' => $orders->orders_products_id, 'orders_custom_products_key' => 'store_credit_currency'])->one()) {
            $customProductInfo = 'Purchased SC : ';        
            $customProductInfo .= Yii::$app->currency->format(Yii::$app->currency->internal_currency[$ocpInfo->orders_custom_products_value], $orders->products_delivered_quantity);
            // check store credit promotion data
            if ($ocpInfoExtra = OrdersCustomProducts::find()->where(['orders_products_id' => $orders->orders_products_id, 'orders_custom_products_key' => 'store_credit_promotion_percentage'])->one()) {
                $customProductInfo .= "\n" . "Extra SC: " . $ocpInfoExtra->orders_custom_products_value . "%";
                $new_sc_to_deliver = $orders->products_delivered_quantity * (1 + ($ocpInfoExtra->orders_custom_products_value / 100));
                $customProductInfo .= "\n" . "Total SC to Deliver: " . Yii::$app->currency->format(Yii::$app->currency->internal_currency[$ocpInfo->orders_custom_products_value], $new_sc_to_deliver);
            }
            
            return "\n\n" . "More Information:" . "\n" . "--------------------" . "\n" . $customProductInfo;
        }
    }

}
