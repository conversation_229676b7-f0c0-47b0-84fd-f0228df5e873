<?php

namespace common\components;
use Yii;
use common\components\CurlCom;

class PipwaveSls
{
    /**
     * Generates the signature for the given timestamp and endpoint.
     *
     * @param int $currrent_timestamp The current timestamp.
     * @param string $endpoint The endpoint.
     * @return string The generated signature.
     */
    public $sls_configuration;

    public function setConfig(){
        // var_dump(Yii::$app->params["pipwavesls.merchant_id"]);

        $this->sls_configuration['merchant_id'] = (isset($this->sls_configuration['merchant_id'])) ? $this->sls_configuration['merchant_id']: Yii::$app->params["pipwavesls.merchant_id"];
        $this->sls_configuration['api_key'] = (isset($this->sls_configuration['api_key'])) ? $this->sls_configuration['api_key']:Yii::$app->params["pipwavesls.api.key"];
        $this->sls_configuration['api_secret'] = (isset($this->sls_configuration['api_secret'])) ? $this->sls_configuration['api_secret']:Yii::$app->params["pipwavesls.secret"];

    }

    /**
     * Generates a signature for the given endpoint.
     *
     * @param int|null $currrent_timestamp The current timestamp. If not provided, the current time will be used.
     * @param string $endpoint The endpoint to generate the signature for.
     * @throws Exception If the signature cannot be generated.
     * @return string The generated signature.
     * sample call generateSignature($time, '/tax/calculate'),
     */
    protected function generateSignature($currrent_timestamp = null, $endpoint)
    {
        if(!$currrent_timestamp){
            $currrent_timestamp = time();
        }

        $sls_merchant_id = $this->sls_configuration['merchant_id'];
        $sls_api_key = $this->sls_configuration['api_key'];
        $sls_api_secret = $this->sls_configuration['api_secret'];
        $signature_str = $endpoint . $sls_api_key . $sls_merchant_id . $currrent_timestamp;
        return hash_hmac('sha256', $signature_str, $sls_api_secret);
    }

    public function currencyGetListRates(){
        $this->setConfig();
        return $this->callOpenApi('/currency/rates');
    }

    public function callOpenApi($endpoint,$payload=[]){
        $url = Yii::$app->params['pipwavesls.open.base_url'].$endpoint;
        $time = time();
        $header = [
            'Content-Type' => 'application/json',
            'pw-timestamp' => $time,
            'pw-merchant-id' => $this->sls_configuration['merchant_id'],
            'pw-api-key' => $this->sls_configuration['api_key'],
            'pw-signature' => $this->generateSignature($time, $endpoint),
        ];

        $curl = new CurlCom();
        $curl->request_headers = $header;
        $response = $curl->sendGet($url,$payload,true) ;
        if($response->code == 2000){
            return json_decode(json_encode($response->payload), true);
        }else{
            throw new Exception(['error_code'=>$response->message]);

        }
    }


}