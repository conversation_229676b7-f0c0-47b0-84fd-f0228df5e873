<?php

namespace common\components;

use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;

class SMS
{
    public static function verifyPhoneNumber($code, $number)
    {
        $fullNumber = $code . $number;
        $phoneUtil = PhoneNumberUtil::getInstance();

        if (!$code || !$number || strlen($fullNumber) <= 8) {
            return false;
        }

        try {
            $parseResult = $phoneUtil->parse('+' . preg_replace('/[\D]/', '', $fullNumber));

            if (!$phoneUtil->isValidNumber($parseResult)) {
                return false;
            }
        } catch (NumberParseException $ex) {
            return false;
        }

        return true;
    }
}