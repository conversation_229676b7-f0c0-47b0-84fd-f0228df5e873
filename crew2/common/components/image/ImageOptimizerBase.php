<?php

namespace common\components\image;

use yii\base\InvalidArgumentException;

abstract class ImageOptimizerBase
{
    public $timeout = 300;

    protected $ratio;

    abstract public function init();

    abstract public function compressImageFromFile($sourceFile, $outputPath, $fileName);

    abstract public function compressImageFromUrl($sourceFile, $outputPath, $fileName);

    public function setCompressionRatio($ratio)
    {
        $ratio = (int)$ratio;
        if (isset($this->getSupportedRatio()[$ratio])) {
            $this->ratio = (int)$ratio;
        } else {
            throw new InvalidArgumentException('Ratio is not supported in this api');
        }
    }

    public function getSupportedRatio()
    {
        return [];
    }

    public function getDefaultRatio()
    {
        return 1;
    }

    public function getSupportedMime()
    {
        return ['image/jpeg', 'image/png', 'image/gif'];
    }
}