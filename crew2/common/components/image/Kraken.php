<?php

namespace common\components\image;

use Yii;
use yii\base\InvalidArgumentException;
use offgamers\base\components\Guzzle;
use yii\base\ErrorException;
use yii\helpers\Json;

class Kraken extends ImageOptimizerBase
{
    public const IMAGE_COMPRESSION_RATIO = [-1 => 'Off', 0 => 'Lossless', 1 => 'Lossy'];

    /**
     * @var \Kraken $client
     */
    private $client;
    /**
     * @var \offgamers\base\components\Guzzle $guzzle_client
     */
    private $guzzle_client;

    public function init()
    {
        if (!empty(Yii::$app->params['kraken.key']) && !empty(Yii::$app->params['kraken.secret'])) {
            $proxy = (!empty(Yii::$app->params['proxy']) ? ['proxy' => Yii::$app->params['proxy']] : array());
            $this->client = new \Kraken(Yii::$app->params['kraken.key'], Yii::$app->params['kraken.secret'], $this->timeout, $proxy);
        } else {
            throw new InvalidArgumentException('<PERSON>io is not supported in this api');
        }
        $this->guzzle_client = new Guzzle();
    }

    public function compressImageFromFile($sourceFile, $outputPath, $fileName)
    {
        $outputFile = $outputPath . '/' . $fileName;
        move_uploaded_file($sourceFile, $outputFile);

        $params = array(
            "file" => $outputFile,
            "wait" => true
        );

        if ($this->ratio == 1) {
            $params["lossy"] = true;
        }

        $data = $this->client->upload($params);
        $res = $data;

        if ($res['success'] == true && !empty($res['kraked_url'])) {
            $this->downloadOutputFile($res['kraked_url'], $outputFile);
        } else {
            unlink($outputFile);
            throw new ErrorException('fail to compress file');
        }
        return true;
    }

    public function downloadOutputFile($url, $outputFile)
    {
        $options = [];
        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }
        $res = $this->guzzle_client->request('GET', $url, $options);
        $body = $res->getBody()->getContents();
        if (!empty($body)) {
            file_put_contents($outputFile, $body);
        }
    }

    public function compressImageFromUrl($sourceUrl, $outputPath, $fileName)
    {
        $outputFile = $outputPath . '/' . $fileName;

        $params = array(
            "url" => $sourceUrl,
            "wait" => true
        );

        if ($this->ratio == 1) {
            $params["lossy"] = true;
        }

        $data = $this->client->url($params);

        $res = $data;

        if ($res['success'] == true && !empty($res['kraked_url'])) {
            $this->downloadOutputFile($res['kraked_url'], $outputFile);
        } else {
            unlink($outputFile);
            throw new ErrorException('fail to compress file');
        }
        return true;
    }

    public function getSupportedRatio()
    {
        return self::IMAGE_COMPRESSION_RATIO;
    }
}