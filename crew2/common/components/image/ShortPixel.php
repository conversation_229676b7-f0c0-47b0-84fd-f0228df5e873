<?php

namespace common\components\image;

use Yii;
use ShortPixel as BaseShortPixel;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class ShortPixel extends ImageOptimizerBase
{
    public const IMAGE_COMPRESSION_RATIO = [-1 => 'Off', 0 => 'Lossless', 1 => 'Lossy', 2 => 'Glossy'];

    public function init()
    {
        if (!empty(Yii::$app->params['proxy'])) {
            BaseShortPixel\setCurlOptions(array(CURLOPT_PROXY => Yii::$app->params['proxy']));
        }
        if (!empty(Yii::$app->params['short.pixel.key'])) {
            BaseShortPixel\setKey(Yii::$app->params['short.pixel.key']);
        } else {
            throw new InvalidArgumentException('Ratio is not supported in this api');
        }
    }

    public function compressImageFromFile($sourceFile, $outputPath, $fileName)
    {
        $time_start = time();
        BaseShortPixel\fromFiles($sourceFile)->optimize($this->ratio)->wait(300)->toFiles($outputPath, $fileName);
        $time_end = time();

        if ($time_end - $time_start > 30) {
            Yii::$app->slack->send('Short Pixel Long Image Processing Time', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode(
                        [
                            'filename' => $fileName,
                            'start_time' => $time_start,
                            'end_time' => $time_end,
                            'time_consume' => ($time_end - $time_start) . ' second'
                        ]
                    )
                )
            ));
        }
    }

    public function compressImageFromUrl($sourceUrl, $outputPath, $fileName)
    {
        return BaseShortPixel\fromUrls($sourceUrl)->optimize($this->ratio)->wait(300)->toFiles($outputPath, $fileName);
    }

    public function getSupportedRatio()
    {
        return self::IMAGE_COMPRESSION_RATIO;
    }

    public function getSupportedMime()
    {
        return ['image/jpeg', 'image/png', 'image/gif','application/pdf'];
    }
}