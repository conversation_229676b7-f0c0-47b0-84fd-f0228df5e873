<?php

namespace common\components\image;

use Yii;
use yii\base\InvalidArgumentException;

class TinyPNG extends ImageOptimizerBase
{
    public const IMAGE_COMPRESSION_RATIO = [-1 => 'Off', 1 => 'Lossy'];

    public function init()
    {
        if (!empty(Yii::$app->params['proxy'])) {
            \Tinify\setProxy(Yii::$app->params['proxy']);
        }

        if (!empty(Yii::$app->params['tinypng.key'])) {
            \Tinify\setKey(Yii::$app->params['tinypng.key']);
        } else {
            throw new InvalidArgumentException();
        }
    }

    public function compressImageFromFile($sourceFile, $outputPath, $fileName)
    {
        $source = \Tinify\fromFile($sourceFile);
        $source->toFile($outputPath . '/' . $fileName);
    }

    public function compressImageFromUrl($sourceUrl, $outputPath, $fileName)
    {
        $source = \Tinify\fromUrl($sourceUrl);
        $source->toFile($outputPath . '/' . $fileName);
    }

    public function getSupportedRatio()
    {
        return self::IMAGE_COMPRESSION_RATIO;
    }

    public function getSupportedMime()
    {
        return ['image/jpeg', 'image/png'];
    }
}