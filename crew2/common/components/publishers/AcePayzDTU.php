<?php

namespace common\components\publishers;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \common\models\PublisherSetting;
use yii\base\InvalidArgumentException;

class AcePayzDTU extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'API_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Authentication Endpoint',
                'key' => 'API_ENDPOINT_AUTH',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Game Top Up Payment Endpoint',
                'key' => 'API_ENDPOINT_PAYMENT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Game Top Up Query Endpoint',
                'key' => 'API_ENDPOINT_QUERY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Login',
                'key' => 'API_LOGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Secret',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Currency',
                'key' => 'API_CURRENCY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial('config',
            array('configs' => $configuration, 'data' => $this->configuration_data));
    }

    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function renderColumn($url, $model, $key)
    {
        return null;
    }

}