<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use Guz<PERSON>Http\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class Aquipaga extends \offgamers\publisher\models\profile\Aquipaga
{
    const API_LOG_TAG = 'Aquipaga/products';

    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Username',
                'key' => 'USERNAME',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Terminal ID',
                'key' => 'TERMINAL_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Currency Code',
                'key' => 'CURRENCY_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token',
                'key' => 'TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Expiry',
                'key' => 'TOKEN_EXPIRY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Flag',
                'key' => 'GET_TOKEN_LOCK',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Timestamp',
                'key' => 'GET_TOKEN_LOCK_TIMESTAMP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient(1, static::API_LOG_TAG);
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_list = $this->getProductList()) {
                $this->processProductList($product_list);
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync Aquipaga Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Aquipaga (ID : $this->publisher) : ",
                [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @return array
     */
    protected function getProductList()
    {
        try {
            $product_list = [];
            $request_list = [];
            $requests = [];

            // Lets get a brand new token since we are synchronizing
            $token = $this->getToken(true);

            $i = 0;
            foreach (static::PRODUCT_TYPE_CODES as $product_type_code) {
                $url = $this->base_url . static::catalog_url;
                $data = [
                    'token' => $token,
                    'ProductTypeCode' => $product_type_code,
                ];
                $request = new Request('POST', $url, [], json_encode($data));

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, static::API_LOG_TAG, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $data,
                ]);
                $i ++;
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => $this->max_connection,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                ],
                'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                    if ($body = $this->checkError($response)) {
                        try {
                            foreach ($body['AvailableProductList'] as $item) {
                                $product_list[] = $item;
                            }
                        } catch (\Exception $e) {
                            $url = $request_list[$index]->getUri();
                            $this->appendError($url, 'Aquipaga Invalid Response');
                        }
                    } else {
                        $url = $request_list[$index]->getUri();
                        $this->appendError($url);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    /**
                     * @var Request[] $request_list
                     */
                    $url = $request_list[$index]->getUri();
    
                    if ($reason instanceof RequestException) {
                        $response = $reason->getResponse();
                        $this->checkError($response);
                        $this->appendError($url);
                    } elseif ($reason instanceof \Exception) {
                        $this->appendError($url, $reason->getMessage());
                    } else {
                        $this->appendError($url, 'Unknown error occurred');
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            return $product_list;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync product list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            if (!isset($product['CurrencyCode']) || $product['CurrencyCode'] != $this->currency_code) {
                continue;
            }
            foreach ($product['ProductOptionsList'] as $product_option) {
                $deno = $range_deno = '';
                $min_face_value = $product_option['MinMaxFaceRangeValue']['MinFaceValue'];
                $max_face_value = $product_option['MinMaxFaceRangeValue']['MaxFaceValue'];
                if ($min_face_value == $max_face_value) {
                    $deno = $min_face_value;
                } else {
                    $range_deno = $min_face_value . ' - ' . $max_face_value;
                }
                $cost_price = 0.00;
                if (!$range_deno) {
                    $cost_price = $product_option['MinMaxRangeValue']['MinWholesaleValue'];
                }
                $product_raw = $product;
                $product_raw['ThisProductOption'] = $product_option;
                $insert_product_list[] = [
                    'sku' => $product_option['ProductOptionCode'],
                    'denomination' => '' . $deno,
                    'attribute_1' => ($product['ProductCurrencyCode'] ?? ''),
                    'attribute_2' => $range_deno,
                    'attribute_3' => ($product['CountryIso'] ?? ''),
                    'name' => ($product_option['Name'] ?? ''),
                    'description' => ($product_option['Description'] ?? ''),
                    'cost_currency' => ($product['CurrencyCode'] ?? ''),
                    'cost_price' => $cost_price,
                    'raw' => $product_raw,
                ];
            }
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }

    /**
     * @param $url
     * @param string $error_msg
     * @return void
     */
    protected function appendError($url, string $error_msg = '')
    {
        $this->error_list[] = [
            'url' => (string)$url,
            'error_msg' => ($error_msg ?: $this->error_msg)
        ];

        $this->error_msg = '';
    }

}
