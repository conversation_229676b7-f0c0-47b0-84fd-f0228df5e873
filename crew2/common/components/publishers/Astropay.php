<?php

namespace common\components\publishers;

use Yii;


class Astropay extends \offgamers\publisher\models\profile\Astropay
{
    public $error_msg, $error_code;
    const API_ENDPOINT_CATEGORY = "api/retrieveProductCategory";
    const API_ENDPOINT_PRODUCT = "api/retrieveProductList";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Api Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Api Secret',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }
    
    public function renderColumn($url, $model, $key)
    {
        return null;
    }

}