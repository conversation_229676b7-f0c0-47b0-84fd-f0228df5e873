<?php

namespace common\components\publishers;

use common\models\Publisher;
use common\models\PublisherSetting;
use common\models\SyncPublisherProductModel;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Exception\ClientException;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class Bamboo extends \offgamers\publisher\models\profile\Bamboo {
    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    protected array $configuration_data = [];

    /**
     * @return array[]
     */
    protected function getFieldList() : array
    {
        return [
            [
                'label' => 'Publisher Id',
                'key' => 'PUBLISHER_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Username',
                'key' => 'USERNAME',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Secret',
                'key' => 'SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Notification Url',
                'key' => 'NOTIFICATION_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Notification Secret Key',
                'key' => 'NOTIFICATION_SECRET_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
        ];
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    public function sync() {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient();
        // $this->checkNotificationUrl();
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            $product_list = $this->getProductList();
            if(!$product_list)
                throw new \Exception("Failed to retrieve product list : " . $this->error_msg);

            $this->processProductList($product_list);
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Bamboo (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    protected function getProductList()
    {
        try {
            $publisher = Publisher::findOne(['publisher_id' => $this->publisher]);
            $currency = strtoupper(explode(' ', $publisher->title)[1]); // Publisher title format: "Bamboo USD"
            $page_size = 100;
            $params = [
                'CurrencyCode' => $currency,
                'PageSize' => $page_size,
                'PageIndex' => 0,
            ];

            $product_list = [];
            $request_list = [];
            $requests = [];

            // Get the first page synchronously
            $response = $this->sendRequest('GET', self::CATEGORY_URL, $params);
            $data = $this->verifyResponse($response);

            if (!$data) {
                Yii::$app->slack->send(
                    'Error fetching Bamboo API', array(
                        array(
                            'color' => 'warning',
                            'text' => Json::encode(['error_msg' => $this->error_msg]),
                        ),
                    )
                );
                return false;
            }

            $product_list = array_merge($product_list, $data['items'] ?? []);
            $total_items = $data['count'] ?? 0;
            $total_pages = ceil($total_items / $page_size);

            if ($total_pages <= 1) {
                return $product_list;
            }

            // Prepare asynchronous requests for the next pages
            for ($page = 1; $page < $total_pages; $page++) {
                $auth_params = $this->getAuthParams();
                $header = [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic ' . base64_encode("{$auth_params[0]}:{$auth_params[1]}"),
                ];
                $url = $this->base_url . '/' . self::CATEGORY_URL . '?' . http_build_query([
                    'CurrencyCode' => $currency,
                    'PageSize' => $page_size,
                    'PageIndex' => $page,
                ]);
                $request = new Request('GET', $url, $header);

                $request_list[$page] = $request;
                $requests[$page] = $this->client->getAsyncRequest($this->client, $request, 'Bamboo ' . $currency . '/products');
            }

            // Quick fix: Group 1 requests per batch
            $batches = array_chunk($requests, 1, true);

            // Wait 1 second before starting async requests to avoid colliding with first sync request
            sleep(1);

            foreach ($batches as $batch) {
                $pool = new Pool($this->client, $batch, [
                    'concurrency' => 2,
                    'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                        $body = (string)$response->getBody();
                        try {
                            $data = Json::decode($body);
                            if (!empty($data['items'])) {
                                $product_list = array_merge($product_list, $data['items']);
                            }
                        } catch (\Exception $e) {
                            if (isset($request_list[$index])) {
                                $url = $request_list[$index]->getUri();
                                Yii::$app->slack->send('Error fetching Bamboo API', array(
                                    array(
                                        'color' => 'warning',
                                        'text' => Json::encode(
                                            ['Bamboo Invalid Response', ['url' => $url, 'reason' => $e->getMessage(), 'body' => $body]]
                                        )
                                    )
                                ));
                            }
                        }
                    },
                    'rejected' => function ($reason, $index) use ($request_list) {
                        if (isset($request_list[$index])) {
                            $url = $request_list[$index]->getUri();
                            if ($reason instanceof ClientException) {
                                $status_code = $reason->getResponse()->getStatusCode();
                                $body = $reason->getResponse()->getBody();
                                Yii::$app->slack->send('Error fetching Bamboo API', array(
                                    array(
                                        'color' => 'warning',
                                        'text' => Json::encode(
                                            ['Bamboo Client Exception', ['url' => $url, 'http_code' => $status_code, 'body' => $body]]
                                        )
                                    )
                                ));
                            } else {
                                Yii::$app->slack->send('Error fetching Bamboo API', array(
                                    array(
                                        'color' => 'warning',
                                        'text' => Json::encode(
                                            ['Bamboo Request Exception', ['url' => $url, 'body' => $reason->getRequest()->getBody()]]
                                        )
                                    )
                                ));
                            }
                        }
                    },
                ]);

                $promise = $pool->promise();
                $promise->wait();

                // Wait for 1 second between each batch
                sleep(1);
            }

            return $product_list;
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Bamboo (ID : $this->publisher) : ", array(
                    array(
                        'color' => 'danger',
                        'text' => Json::encode([
                            'error' => 'Unhandled Exception',
                            'message' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]),
                    ),
                )
            );
            return false;
        }
    }

    protected function processProductList(array $data) {
        $insert_product_list = [];

        if (empty($data)) {
            throw new \Exception("Failed to process product list : " . $this->error_msg);
        }

        foreach($data as $item) {
            if (isset($item['products']) && count($item['products']) > 0) {
                foreach($item['products'] as $product) {
                    $denomination = '';

                    if (isset($product['minFaceValue']) && isset($product['maxFaceValue'])) {
                        $denomination = $product['minFaceValue'] == $product['maxFaceValue'] ? $product['minFaceValue'] : $product['minFaceValue'] . ' - ' . $product['maxFaceValue'];
                    } elseif (isset($product['minFaceValue'])) {
                        $denomination = $product['minFaceValue'];
                    } elseif (isset($product['maxFaceValue'])) {
                        $denomination = $product['maxFaceValue'];
                    }

                    $insert_product_list[] = [
                        'sku' => (string)$product['id'],
                        'denomination' => (string)$denomination,
                        // Set Publisher Currency ID as attributes
                        'attribute_1' => $product['price']['currencyCode'] ?? '',
                        'attribute_2' => (string)$product['price']['min'] ?? '',
                        'attribute_3' => (string)$product['price']['max'] ?? '',
                        'name' => $product['name'] ?? '',
                        'description' => $product['description'] ?? '',
                        'cost_currency' => $product['price']['currencyCode'] ?? '',
                        'cost_price' => 0.00,
                        'raw' => $product
                    ];
                }
            }
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_msg));
    }

    protected function checkNotificationUrl() : void {
        if (!$this->notification_url || !$this->notification_secret_key) {
            throw new \Exception("Bamboo notification url and secret key are not configured");
        }
        $response = $this->sendRequest("GET", self::NOTIFICATION_URL);
        if($data = $this->verifyResponse($response)) {
            if ($data['notificationUrl'] !== $this->notification_url || $data['secretKey'] !== $this->notification_secret_key) {
                $this->setNotificationUrl();
            }
        }
    }

    protected function setNotificationUrl() {
        $response = $this->sendRequest("POST", self::NOTIFICATION_URL, [
            'notificationUrl' => $this->notification_url,
            'secretKey' => $this->notification_secret_key
        ]);

        try {
            if($data = $this->verifyResponse($response)) {
                if ($data['notificationUrl'] !== $this->notification_url || $data['secretKey'] !== $this->notification_secret_key) {
                    $this->error_msg = [
                        "Failed to set notification URL or Secret Key",
                        "Request data: " . json_encode(['notificationUrl' => $this->notification_url, 'secretKey' => $this->notification_secret_key]),
                        "Response data: " . json_encode(['notificationUrl' => $data['notificationUrl'], 'secretKey' => $data['secretKey']])
                    ];
                    throw new \Exception(json_encode($this->error_msg));
                }
            }
        } catch(\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Bamboo (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }
}