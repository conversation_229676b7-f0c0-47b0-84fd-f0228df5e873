<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use Guz<PERSON>Http\Exception\RequestException;
use Guz<PERSON>Http\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use offgamers\publisher\models\PublisherSetting;
use Psr\Http\Message\ResponseInterface;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class BlackHawkNetworkV2 extends \offgamers\publisher\models\profile\BlackHawkNetworkV2
{
    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Certificate File Name',
                'key' => 'CERT_FILE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Certificate Secret',
                'key' => 'CERT_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Mid',
                'key' => 'MID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Apple Configuration ID (Comma Seperated)',
                'key' => 'APPLE_PRODUCT_CONFIGURATION_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->initClient(1, 'BHNV2_' . $this->publisher_id);
        $this->getConfig();
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_catalog = $this->getProductCatalog()) {
                foreach ($product_catalog as $catalog_url) {
                    if ($product_list = $this->getProductListByCatalog($catalog_url)) {
                        $product_array = $this->getProductDetails($product_list);
                        $this->processProductList($product_array);
                    }
                }
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync BlackHawkNetworkV2 Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync BlackHawkNetworkV2 (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @param array $products_url
     * @return array
     */
    protected function getProductDetails(array $products_url): array
    {
        $client = $this->client;

        $product_list = [];
        $request_list = [];
        $requests = [];

        $options = [
            'http_errors' => false,
            'cert' => [
                $this->cert_path . '/' . $this->cert_file,
                $this->cert_secret
            ],
            'curl' => [CURLOPT_SSLCERTTYPE => 'p12'],
        ];

        for ($i = 0; $i < count($products_url); $i++) {
            $request = new Request('GET', $products_url[$i], [], '');

            $request_list[$i] = $request;
            $url_link = explode('/', $products_url[$i]);
            $tag = end($url_link);
            $requests[$i] = $this->client->getAsyncRequest($this->client, $request, 'BHNV2_' . $tag);
        }

        $pool = new Pool($client, $requests, [
            'concurrency' => $this->max_connection,
            'options' => $options,
            'fulfilled' => function (Response $response, $index) use (&$product_list, $request_list) {
                if ($data = $this->verifyResponse($response)) {
                    if (!empty($data['details']['defaultProductConfigurationId']) && !empty($data['summary']['productName'])) {
                        $product_list[] = $data;
                    } else {
                        $this->appendError($request_list[$index], 'Product with empty sku / product name');
                    }
                } else {
                    $this->appendError($request_list[$index], 'Invalid response from publisher');
                }
            },
            'rejected' => function ($reason, $index) use ($request_list) {
                if ($reason instanceof RequestException) {
                    $response = $reason->getResponse();
                    if ($response) {
                        $message = (string)$response->getBody();
                        $this->appendError($request_list[$index], $message);
                    } else {
                        $this->appendError($request_list[$index], $reason->getMessage());
                    }
                } elseif ($reason instanceof \Exception) {
                    $this->appendError($request_list[$index], $reason->getMessage());
                } else {
                    $this->appendError($request_list[$index], 'Unknown error occurred');
                }
            }
        ]);

        $promise = $pool->promise();
        $promise->wait();

        return $product_list;
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            $deno = $product['details']['activationCharacteristics'];
            $range_denomination_list = '';
            if ($deno['isVariableValue']) {
                if (isset($deno['baseValueAmount']) && isset($deno['maxValueAmount'])) {
                    $range_denomination_list = $deno['baseValueAmount'] . '-' . $deno['maxValueAmount'];
                }
            } else {
                if (isset($deno['baseValueAmount'])) {
                    $range_denomination_list = $deno['baseValueAmount'];
                }
            }

            $insert_product_list[] = [
                'sku' => $product['details']['defaultProductConfigurationId'],
                'denomination' => (string)($deno['isVariableValue'] ? '' : $deno['baseValueAmount']),
                'attribute_1' => ($product['summary']['currency'] ?? ''),
                'attribute_2' => (string)$range_denomination_list,
                'attribute_3' => $product['summary']['locale'] ?? '',
                'name' => $product['summary']['productName'],
                'description' => ($product['details']['productDescription'] ?? ''),
                'cost_currency' => '',
                'cost_price' => 0.00,
                'raw' => $product
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }

    /**
     * @param $url
     * @param string $error_msg
     * @return void
     */
    protected function appendError($url, string $error_msg = '')
    {
        $this->error_list[] = [
            'url' => (string)$url->getUri(),
            'error_msg' => ($error_msg ?: $this->error_msg)
        ];

        $this->error_msg = '';
    }

    /**
     * @param ResponseInterface $response
     * @return array|null
     */
    protected function verifyResponse(ResponseInterface $response): ?array
    {
        $this->error_msg = '';

        $status_code = $response->getStatusCode();
        $response_body = $response->getBody();
        try {
            $data = Json::decode($response_body);
            if ($status_code < 300) {
                return $data;
            } else {
                if (isset($data['errorCode']) && isset($data['message'])) {
                    $this->error_msg = $data['errorCode'] . ' : ' . $data['message'];
                } else {
                    $this->error_msg = $data;
                }
            }
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage() . ' - ' . (string)$response_body;
        }

        return null;
    }
}