<?php

namespace common\components\publishers;

use Yii;
use common\models\SyncPublisherProductModel;
use yii\helpers\Json;
use yii\helpers\Html;
use yii\base\InvalidArgumentException;
use yii\helpers\Url;

class Boost extends \offgamers\publisher\models\profile\Boost
{
    public $error_msg, $error_code;
    const API_ENDPOINT_CATEGORY = "api/retrieveProductCategory";
    const API_ENDPOINT_PRODUCT = "api/retrieveProductList";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Api Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Publisher Code',
                'key' => 'PUBLISHER_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Operator',
                'key' => 'OPERATOR',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        set_time_limit(300);
        ini_set('memory_limit', '256M');
        $this->getExistingConfig();
        $this->initClient();
        $this->syncProductList();
    }

    public function syncProductList()
    {
        try {
            $product_list = [];
            $query_data = [];
            $query_data['publisherCode'] = $this->configuration_data['PUBLISHER_CODE'];

            if ($category_list = $this->getCategory($query_data)) {
                foreach ($category_list['productCategories'] as $category_key => $category_arr) {
                    if (isset($category_arr['categoryCode'])) {
                        $query_data['productCategory'] = $category_arr['categoryCode'];
                        $query_data['operator'] = $this->configuration_data['OPERATOR'];

                        if ($product_data = $this->getProduct($query_data)) {
                            foreach ($product_data['products'] as $key => $product_arr) {
                                $data = $this->parseData($product_arr);
                                if($data){
                                    $product_list[] = $data;
                                }else{
                                    $this->error_msg = "Unable To Insert Product (Missing Data) : \n" . JSON::encode($product_arr) . "\n";
                                    $this->reportError();
                                }
                            }
                        }
                    }
                }
                SyncPublisherProductModel::syncPublisherProductList($this->publisher, $product_list, true);
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                'Boost Product Exception Error',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    private function parseData($product)
    {
        if(isset($product['productName']) && isset($product['productCode'])){
            $data = [
                'name' => $product['productName'],
                'description' => $product['productDesc'] ?: '',
                'cost_currency' => $product['productCurrency'] ?: '',
                'cost_price' => 0.00,
                'sku' => $product['productCode'],
                'raw' => $product
            ];
    
            return $data;
        }

        return false;
       
    }

    private function getCategory($query_data)
    {
        $response = $this->sendRequest('POST', static::API_ENDPOINT_CATEGORY, $query_data);
        $data = $this->checkError($response);
        return $data;
    }

    private function getProduct($query_data)
    {
        $response = $this->sendRequest('POST', static::API_ENDPOINT_PRODUCT, $query_data);
        $data = $this->checkError($response);
        return $data;
    }

    private function checkError($response)
    {
        try {
            $status = $response->getStatusCode();

            try {
                $data = Json::decode($response->getBody());
            } catch (InvalidArgumentException $e) {
                $this->error_msg = $status . ' - ' . "Invalid Publisher Response";
            }

            if ($status == 200) {
                return $data;
            } else {
                if (!empty($data)) {
                    if (isset($data['errorMessage']) && isset($data['errorCode'])) {
                        $this->error_msg = $data['errorCode'] . ' - ' . $data['errorMessage'];
                    } else {
                        $this->error_msg = $status . ' - ' . "Invalid Publisher Response";
                    }
                } else {
                    $this->error_msg = $status . ' - ' . "No output for publisher response";
                }
            }
        } catch (\Exception $e) {
            $this->error_msg = $response->getStatusCode() . '-' . $response->getBody();
        }

        $this->reportError();

        return false;
    }

    private function reportError()
    {
        Yii::$app->slack->send(
            'Boost Product Error',
            array(
                array(
                    'color' => 'warning',
                    'text' => 'Error Message : \n' . $this->error_msg,
                ),
            )
        );
    }

}