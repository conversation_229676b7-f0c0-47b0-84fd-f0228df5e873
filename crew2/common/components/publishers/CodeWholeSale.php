<?php

namespace common\components\publishers;

use Yii;
use common\models\GamePublisherProductForm;
use yii\helpers\Json;


class CodeWholeSale extends \offgamers\publisher\models\profile\CodesWholeSale
{
    const AUTO_MARKUP = true;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Endpoint',
                'key' => 'API_ENDPOINT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Secret',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Signature',
                'key' => 'API_SIGNATURE',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Token',
                'key' => 'API_TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Token Expiry',
                'key' => 'API_TOKEN_EXPIRY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock',
                'key' => 'GET_TOKEN_LOCK',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Lock Timestamp',
                'key' => 'GET_TOKEN_LOCK_TIMESTAMP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial('config',
            array('configs' => $configuration, 'data' => $this->configuration_data));
    }

    public function disableProduct($product_id)
    {
        $data = [
            'game_publisher_id' => $this->publisher,
            'publisher_reference_id' => $product_id
        ];
        (new GamePublisherProductForm)->disableProduct($data);
    }

    public function syncProductList()
    {
        $product_list = $this->getProductCatalog();
        $this->processProductList(Json::decode($product_list)['items']);
    }

    public function syncProductListWithProductId($product_id_list)
    {
        foreach ($product_id_list as $product_id) {
            $this->syncProduct($product_id);
        }
    }

    public function syncProduct($product_id)
    {
        $response = $this->getProductByPublisherId($product_id);
        if(isset($response['productId'])){
            $data = $this->parseData($response);

            $request_data = ['item_list' => [$data], 'full_sync' => false];
            (new GamePublisherProductForm)->batchCreate($request_data, 'CodesWholeSale');
        }
    }

    private function processProductList($product_list)
    {
        $game_product = [];
        foreach ($product_list as $product) {
            $data = $this->parseData($product);
            $game_product[] = $data;
        }
        $request_data = ['item_list' => $game_product, 'full_sync' => true];
        (new GamePublisherProductForm)->batchCreate($request_data, 'CodesWholeSale');
    }

    private function parseData($product)
    {
        $region_title = $this->mapValue('region_title', $product['regions']);
        $platform = $this->mapValue('platform', [$product['platform']]);
        $region = $this->mapValue('region', $product['regions']);

        $title = html_entity_decode($product['name']) . (!empty($platform[0]) ? ' ' . strtoupper($platform[0]) : '') . ' [' . implode("/", $region_title) . ']';

        $data = [
            'title' => $title,
            'cost_price' => $this->findCostPrice($product['prices']),
            'cost_currency' => 'EUR',
            'language' => $this->mapValue('language', $product['languages']),
            'platform' => $platform,
            'region' => $region,
            'release_date' => date_format(date_create($product['releaseDate']), 'Y-m-d'),
            'model' => 'CWS' . '_' . $product['productId'],
            'url_alias' => $this->filter_special_char($title),
            'publisher_reference_id' => $product['productId'],
            'game_publisher_id' => $this->publisher,
            'out_of_stock_flag' => !($product['quantity'] > 0),
            'pre_order_flag' => ($product['quantity'] == 0 && $this->isPreOrder($product['releaseDate'])),
            'stock_quantity' => $product['quantity'],
            'auto_markup' => self::AUTO_MARKUP
        ];

        return $data;
    }

    private function mapValue($key, $value)
    {
        // TODO change to params
        $_mapped = [
            'region_title' => [
                'WORLDWIDE' => 'GLOBAL'
            ],
            'language' => [
                'en' => 'English',
                'de' => 'Dutch',
                'pl' => 'Polish',
                'fr' => 'French',
                'ru' => 'Russian',
                'es' => 'Spanish - Spain'
            ],
            'platform' => [
                'None' => false,
                'iTunes' => false,
                'Official website' => false,
                'Rockstar Social Club' => 'Rockstar',
                'GOG.com' => 'GOG',
                'Bethesda.net' => 'Bathesda',
                'ubi.com' => 'Ubi'
            ],
            'region' => [
                'WORLDWIDE' => 'Global (Worldwide)'
            ],
        ];

        $return_array = [];

        foreach ($value as $val) {
            if (isset($_mapped[$key][$val])) {
                if ($_mapped[$key][$val] === false) {
                    continue;
                } else {
                    $return_array[] = $_mapped[$key][$val];
                }
            } else {
                $return_array[] = $val;
            }
        }
        return $return_array;
    }

    //TODO : Remove in next release
    public function checkError($response)
    {
        return Json::decode($response->getBody());
    }


}