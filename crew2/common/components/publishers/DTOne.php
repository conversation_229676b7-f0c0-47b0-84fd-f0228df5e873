<?php

namespace common\components\publishers;

use common\models\MobileRechargeOperatorForm;
use common\models\MobileRechargeRegionForm;
use GuzzleHttp\Pool;
use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \common\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use \common\models\MobileRechargeDenoForm;
use GuzzleHttp\Psr7\Request;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;

class DTOne extends \offgamers\publisher\models\profile\DTOne
{
    use GuzzleTrait;

    public $configuration_data, $publisher;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Endpoint',
                'key' => 'API_ENDPOINT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'G&S Endpoint',
                'key' => 'GNS_API_ENDPOINT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Login',
                'key' => 'API_LOGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Secret',
                'key' => 'API_TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API KEY (Goods & Services)',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Secret (Goods & Services)',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Currency',
                'key' => 'API_CURRENCY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Mark Up',
                'key' => 'MARK_UP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Air Time Products ID',
                'key' => 'AIR_TIME_PRODUCTS_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Data Bundle Products ID',
                'key' => 'DATA_BUNDLE_PRODUCTS_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Top Up Method',
                'key' => 'TOP_UP_METHOD',
                'items' => ['simulation' => 'simulation', 'topup' => 'topup'],
                'type' => 'select',
                'option' => ['class' => 'form-control', 'prompt' => '']
            ]
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial('config',
            array('configs' => $configuration, 'data' => $this->configuration_data));
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function sync()
    {
        set_time_limit(300);
        ini_set('memory_limit', '256M');

        $c_id_list = [];
        $this->getExistingConfig();
        $this->initClient();

        $country_list = $this->getCountryList();

        if ($country_list) {
            $country_data = simplexml_load_string((string)$country_list->getBody());
            if (!empty($country_data->country) && !empty($country_data->countryid)) {
                $c_title_list = explode(",", $country_data->country);
                $c_id_list = explode(",", $country_data->countryid);

                $this->updateRegionData($c_id_list, $c_title_list);
            }
        }

        unset($country_list, $country_data);
        gc_collect_cycles();

        $operator_list = [];
        $operator_id_list = [];

        $requests = function ($country_list) {
            $uri = $this->configuration_data['API_ENDPOINT'];
            $header = [
                'Content-Type' => 'text/xml'
            ];
            for ($i = 0; $i < count($country_list); $i++) {
                $key = ($country_list[$i] . ((microtime(true) * 10000)));
                $default_array = [
                    'login' => $this->configuration_data['API_LOGIN'],
                    'key' => $key,
                    'md5' => $this->generateSignature($key)
                ];

                $data = [
                    'action' => 'pricelist',
                    'info_type' => 'country',
                    'content' => $country_list[$i]
                ];

                $request_data = array_merge($default_array, $data);
                yield new Request('POST', $uri, $header, $this->generateXmlFromArray($request_data));
            }
        };

        $pool = new Pool($this->client, $requests($c_id_list), [
            'concurrency' => 30,
            'options' => [
                'timeout' => 30,
                'proxy' => Yii::$app->params['proxy'],
            ],
            'fulfilled' => function ($response, $index) use (&$operator_list, &$operator_id_list) {
                $operator_data = simplexml_load_string((string)$response->getBody());
                if (!empty($operator_data->operator) && !empty($operator_data->operatorid)) {
                    $operator_list[(string)$operator_data->countryid] = $operator_data;
                    $list = explode(",", $operator_data->operatorid);
                    foreach ($list as $value) {
                        $operator_id_list[] = $value;
                    }
                }
            },
            'rejected' => function ($reason, $index) {
                if ($reason instanceof \GuzzleHttp\Exception\ClientException) {
                    $body = $reason->getResponse()->getBody();
                    $this->errorReporter(['Operator API 1', $body]);
                } else {
                    $this->errorReporter(['Operator API 2', $reason->getRequest()->getBody()]);
                }
            },
        ]);

        $promise = $pool->promise();
        $promise->wait();

        //sort operator list by id
        ksort($operator_list);
        $operator_list = array_values($operator_list);
        sort($operator_id_list);

        $this->updateOperatorData($operator_list);
        unset($country_list, $operator_list);

        $deno_list = [];

        $requests = function ($operator_list) {
            $uri = $this->configuration_data['API_ENDPOINT'];
            $header = [
                'Content-Type' => 'text/xml'
            ];
            for ($i = 0; $i < count($operator_list); $i++) {
                $key = ($operator_list[$i] . ((microtime(true) * 10000)));
                $default_array = [
                    'login' => $this->configuration_data['API_LOGIN'],
                    'key' => $key,
                    'md5' => $this->generateSignature($key)
                ];

                $data = [
                    'action' => 'pricelist',
                    'info_type' => 'operator',
                    'content' => $operator_list[$i]
                ];

                $request_data = array_merge($default_array, $data);
                yield new Request('POST', $uri, $header, $this->generateXmlFromArray($request_data));
            }
        };

        $pool = new Pool($this->client, $requests($operator_id_list), [
            'concurrency' => 30,
            'options' => [
                'timeout' => 30,
                'proxy' => Yii::$app->params['proxy'],
            ],
            'fulfilled' => function ($response, $index) use (&$deno_list, &$deno_id_list) {
                $deno_data = simplexml_load_string((string)$response->getBody());
                if (!empty($deno_data->product_list) && !empty($deno_data->wholesale_price_list)) {
                    $deno_list[(string)$deno_data->operatorid] = $deno_data;
                }
            },
            'rejected' => function ($reason, $index) {
                if ($reason instanceof \GuzzleHttp\Exception\ClientException) {
                    $body = $reason->getResponse()->getBody();
                    $this->errorReporter(['Denomination API 1', $body]);
                } else {
                    $this->errorReporter(['Denomination API 2', $reason->getRequest()->getBody()]);
                }

            },
        ]);

        $promise = $pool->promise();
        $promise->wait();

        //sort deno id by operator id
        ksort($deno_list);
        $deno_list = array_values($deno_list);

        $this->updateDenoData($deno_list);

        $this->syncGNSData();
    }

    public function syncGNSData()
    {
        $operator_data = Json::decode($this->getGNSOperator(7)->getBody());
        $operator_id_list = ArrayHelper::getColumn($operator_data['operators'], 'operator_id');
        $data_bundle = [];
        if (is_array($operator_id_list) && count($operator_id_list)) {
            for ($i = 0; $i < count($operator_id_list); $i++) {
                $response = $this->sendGNSRequest('GET', 'operators/' . $operator_id_list[$i] . '/products');
                $operator_data = Json::decode($response->getBody());
                $data_bundle = array_merge($data_bundle, $operator_data['fixed_value_recharges']);
                // DTOne Rate Limit @ 50 per second per server
                sleep(1);
            }
            $this->updateBundleData($data_bundle);
        }
    }

    private function updateRegionData($country_id_list, $countries_title_list)
    {
        (new MobileRechargeRegionForm)->batchCreate(array_combine($country_id_list, $countries_title_list));
    }

    private function updateOperatorData($operator_list)
    {
        (new MobileRechargeOperatorForm)->batchCreate($operator_list);
    }

    private function updateDenoData($deno_list)
    {
        (new MobileRechargeDenoForm)->batchCreate($this->publisher, $this->configuration_data['API_CURRENCY'], $this->configuration_data['MARK_UP'], $deno_list, $this->configuration_data['AIR_TIME_PRODUCTS_ID']);
    }

    private function updateBundleData($deno_list)
    {
        (new MobileRechargeDenoForm)->batchCreateBundle($this->publisher, $this->configuration_data['API_CURRENCY'], $this->configuration_data['MARK_UP'], $deno_list, $this->configuration_data['DATA_BUNDLE_PRODUCTS_ID']);
    }


    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {game-publisher-product/index}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a('<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            }
        ], $model, $key, -1);
    }

}