<?php

namespace common\components\publishers;

use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Yii;
use \common\models\MobileRechargeDenoForm;
use yii\helpers\Html;
use yii\helpers\Json;

class DTOneDVS extends \offgamers\publisher\models\profile\DTOneDVS
{
    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Endpoint',
                'key' => 'API_ENDPOINT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'API Secret',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Postback Endpoint',
                'key' => 'POSTBACK_ENDPOINT',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Callback API Key',
                'key' => 'CALL_BACK_API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Callback API Secret',
                'key' => 'CALL_BACK_API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Currency',
                'key' => 'API_CURRENCY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Mark Up',
                'key' => 'MARK_UP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Virtual Products ID',
                'key' => 'AIR_TIME_PRODUCTS_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function sync()
    {
        set_time_limit(300);
        ini_set('memory_limit', '256M');
        $this->getExistingConfig();
        $this->initClient();
        $this->syncOperatorList();
        $this->syncProductList();
    }

    public function syncOperatorList()
    {
        try {
            $operator_list = [];
            $return_arr = [];
            $requests = [];
            $request_list = [];

            $response = $this->getOperatorByPage(1);
            $total_page = $response->getHeaderLine('X-Total-Pages');

            $this->initClient();
            $this->getExistingConfig();

            for ($i = 1; $i <= $total_page; $i++) {
                $header = [
                    'Authorization' => 'Basic ' . base64_encode($this->configuration_data['API_KEY'] . ':' . $this->configuration_data['API_SECRET'])
                ];

                $request = new Request('GET', $this->configuration_data['API_ENDPOINT'] . '/operators?per_page=100&page=' . $i, $header, '');

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, 'DTOneDVS/operator');
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => 10,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                    'http_errors' => false
                ],
                'fulfilled' => function ($response, $index) use (&$operator_list, $request_list) {
                    $body = (string)$response->getBody();
                    try {
                        $list = Json::decode($body);
                        foreach ($list as $item) {
                            $operator_list[] = $item;
                        }
                    } catch (\Exception $e) {
                        $url = $request_list[$index]->getUri();
                        $this->errorReporter(['DTOne DVS Invalid Response', ['url' => $url, 'reason' => $e->getMessage(), 'body' => $body]]);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    $url = $request_list[$index]->getUri();
                    if ($reason instanceof ClientException) {
                        $status_code = $reason->getResponse()->getStatusCode();
                        $body = $reason->getResponse()->getBody();
                        $this->errorReporter(['DTOne DVS Client Exception', ['url' => $url, 'http_code' => $status_code, 'body' => $body]]);
                    } else {
                        $this->errorReporter(['DTOne DVS Request Exception', $reason->getRequest()->getBody()]);
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            foreach ($operator_list as $operator) {
                $return_arr[] = $this->processOperator($operator);
            }

            $this->updateOperatorData($return_arr);
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync operator list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    public function syncProductList()
    {
        try {
            $product_list = [];
            $return_arr = [];
            $request_list = [];
            $requests = [];

            $response = $this->getProductByPage(1);
            $total_page = $response->getHeaderLine('X-Total-Pages');

            $this->initClient();
            $this->getExistingConfig();

            for ($i = 1; $i <= $total_page; $i++) {
                $header = [
                    'Authorization' => 'Basic ' . base64_encode($this->configuration_data['API_KEY'] . ':' . $this->configuration_data['API_SECRET'])
                ];

                $request = new Request('GET', $this->configuration_data['API_ENDPOINT'] . '/products?per_page=100&page=' . $i, $header, '');

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, 'DTOneDVS/products');
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => 10,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                ],
                'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                    $body = (string)$response->getBody();
                    try {
                        $list = Json::decode($body);
                        foreach ($list as $item) {
                            $product_list[] = $item;
                        }
                    } catch (\Exception $e) {
                        $url = $request_list[$index]->getUri();
                        $this->errorReporter(['DTOne DVS Invalid Response', ['url' => $url, 'reason' => $e->getMessage(), 'body' => $body]]);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    $url = $request_list[$index]->getUri();
                    if ($reason instanceof ClientException) {
                        $status_code = $reason->getResponse()->getStatusCode();
                        $body = $reason->getResponse()->getBody();
                        $this->errorReporter(['DTOne DVS Client Exception', ['url' => $url, 'http_code' => $status_code, 'body' => $body]]);
                    } else {
                        $this->errorReporter(['DTOne DVS Request Exception', ['url' => $url, 'body' => $reason->getRequest()->getBody()]]);
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            foreach ($product_list as $product) {
                if ($product['service']['id'] == 1 && in_array($product['type'], ['FIXED_VALUE_RECHARGE', 'FIXED_VALUE_PIN_PURCHASE'])) {
                    if (isset($product['service']['subservice']['id']) && in_array($product['service']['subservice']['id'], [11, 12, 13])) {
                        $return_arr[] = $this->processProduct($product);
                    }
                }
            }

            $this->updateDenoData($return_arr);
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync product list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    public function processProduct($product)
    {
        return [
            'deno_id' => $product['id'],
            'operator_id' => $product['operator']['id'],
            'name' => $product['name'],
            'description' => $this->getDescription($product),
            'cost_currency' => $product['prices']['wholesale']['unit'],
            'cost_price' => $product['prices']['wholesale']['amount'],
            'type' => $this->getType($product)
        ];
    }

    public function processOperator($operator)
    {
        return [
            'region_code' => $operator['country']['iso_code'],
            'name' => $operator['name'],
            'operator_id' => $operator['id']
        ];
    }

    public function getDescription($product)
    {
        $list = [];

        if (!empty($product['description'])) {
            $list = array_merge($list, explode("\n", $product['description']));
        }

        if ($product['benefits']) {
            foreach ($product['benefits'] as $benefit) {
                if (!empty($benefit['additional_information'])) {
                    $list = array_merge($list, explode("\n", $benefit['additional_information']));
                }
            }
        }

        return $list;
    }

    public function getType($product)
    {
        // In OG, Rechage = 1, Bundle = 2, Pin = 3
        // In DTOne, subservice.id 11 = Airtime, 12 = Bundle, 13 = Data
        // Airtime could be fall in both FIXED_VALUE_PIN_PURCHASE and FIXED_VALUE_RECHARGE
        if ($product['type'] == 'FIXED_VALUE_PIN_PURCHASE' && $product['service']['subservice']['id'] == 11) {
            return 3;
        } elseif ($product['type'] == 'FIXED_VALUE_RECHARGE' && $product['service']['subservice']['id'] == 11) {
            return 1;
        }
        return 2;
    }

    public function getNextPage($response)
    {
        return $response->getHeaderLine('X-Next-Page');
    }

    public function getOperatorByPage($page)
    {
        return $this->sendRequest('GET', 'operators?per_page=100&page=' . $page);
    }

    public function getProductByPage($page)
    {
        return $this->sendRequest('GET', 'products?per_page=100&page=' . $page);
    }

    private function updateDenoData($deno_list)
    {
        (new MobileRechargeDenoForm)->batchCreateDvs($this->publisher, $this->configuration_data['API_CURRENCY'], $this->configuration_data['MARK_UP'], $deno_list, $this->configuration_data['AIR_TIME_PRODUCTS_ID']);
    }

    private function updateOperatorData($operator_list)
    {
        (new MobileRechargeDenoForm)->batchCreateDvsOperator($operator_list);
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {game-publisher-product/index}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            }
        ], $model, $key, -1);
    }

    public function validatePostBack($sign)
    {
        $this->getExistingConfig();
        if (!empty($this->configuration_data)) {
            if (isset($sign[0]) && isset($sign[1]) && isset($this->configuration_data['CALL_BACK_API_KEY']) && isset($this->configuration_data['CALL_BACK_API_SECRET'])) {
                return true;
            }
        }
        Yii::$app->slack->send('Fail to process DTOne Postback', array(
            array(
                'color' => 'warning',
                'text' => Json::encode([$sign, $this->configuration_data]),
            ),
        ));
    }

    public function errorReporter($error)
    {
        Yii::$app->slack->send('Error fetching DTOne DVS api', array(
            array(
                'color' => 'warning',
                'text' => Json::encode(
                    $error
                )
            )
        ));
    }

}