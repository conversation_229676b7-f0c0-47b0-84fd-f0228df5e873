<?php

namespace common\components\publishers;

use Yii;
use common\models\SyncPublisherProductModel;
use yii\helpers\Html;
use Exception;
use offgamers\publisher\models\Publisher;
use yii\helpers\Json;
use yii\helpers\Url;

class Devco extends \offgamers\publisher\models\profile\Devco
{
    public $error_msg, $error_code, $file_handler, $publisher_name;
    const API_GET_PRODUCT = "get_ProductList";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Username',
                'key' => 'USERNAME',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Public Key',
                'key' => 'PUBLIC_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Private Key',
                'key' => 'PRIVATE_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Source IP',
                'key' => 'SOURCE_IP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Notification Email',
                'key' => 'NOTIFICATION_EMAIL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->getExistingConfig();
        $this->initClient();
        $this->syncProductList();
    }

    public function syncProductList()
    {
        try {
            $product_list = [];
            $this->publisher_name = $this->getPublisherTitle();
            
            if(!$this->publisher_name){
                throw new Exception("Publisher Name not found in database.");
            }

            if ($product_data = $this->getProduct()) {
                foreach ($product_data as $key => $product_arr) {
                    if ($parse_data = $this->parseData($product_arr)) {
                        $product_list[] = $parse_data;
                    }
                }
            }

            SyncPublisherProductModel::syncPublisherProductList($this->publisher, $product_list, true);
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                'Devco Product Exception Error',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    private function parseData($product)
    {
        if (isset($product['OEM_PRODUCT_ID']) && isset($product['OEM_PRODUCT_Name'])) {
            $data = [
                'name' => $product['OEM_PRODUCT_Name'],
                'cost_currency' => $product['OEM_PRODUCT_BaseCurrencySymbol'] ?: '',
                'cost_price' => $product['OEM_PRODUCT_UnitPrice'] ?: '',
                'sku' => (string)$product['OEM_PRODUCT_ID'],
                'raw' => $product,
                'denomination' => $product['OEM_PRODUCT_SellPrice'] ?: '',
            ];

            return $data;
        } else {
            Yii::$app->slack->send(
                $this->publisher_name . ' Error Product Format',
                array(
                    array(
                        'color' => 'warning',
                        'text' => JSON::decode($product),
                    ),
                )
            );
        }
    }

    private function getProduct()
    {
        $response = $this->sendRequest('POST', static::API_GET_PRODUCT);
        $data = $this->checkError($response);
        if (!$data) {
            $this->reportError();
        }
        return $data;
    }

    private function reportError()
    {
        Yii::$app->slack->send(
            $this->publisher_name . ' Product Error',
            array(
                array(
                    'color' => 'warning',
                    'text' => 'Error Message : ' . $this->error_msg . '\n',
                ),
            )
        );
    }

    public function customRequest($action, $publisher_id, $param, $csv_file = false)
    {
        $this->publisher = $publisher_id;
        $this->getExistingConfig();
        $this->initClient();
        $this->publisher_name = $this->getPublisherTitle();
        $response = $this->sendRequest('POST', $action, $param);
        $data = $this->checkError($response);

        if (!empty($data)) {
            $title = "Devco " . $this->getEmailTitle($action, $param);

            if ($csv_file) {
                $this->setFile($data);
                $content = null;
            } elseif ($action == "get_ProductAvailability") {
                $content = "The product SKU ( " . $param['PRODUCT_ID'] . " ) remains " . $data . " of stock(s).";
            } elseif ($action == "get_AccountBalance") {
                $content = "The remaining account balance for " . $this->publisher_name . " is " . ($data['RESELLER_Currency'] ?: " ") . " " . ($data['RESELLER_Balance'] ?: "") . "<br><br>";
            }
        } else {
            $content = "No Record found.<br>";
        }

        $this->sendEmail($title, $this->configuration_data['NOTIFICATION_EMAIL'], $content);
    }


    protected function setFile($data)
    {
        $file_header = false;
        $header = [];

        if (!$tmp = fopen('php://temp', 'w+')) {
            throw new Exception('tmp directory not writable');
        }

        $this->file_handler = $tmp;

        // complete the header first
        foreach ($data as $key => $arr) {
            foreach ($arr as $key => $val) {
                if (!in_array($key, $header)) {
                    $header[] = $key;
                }
            }
        }
        fputcsv($this->file_handler, $header);

        foreach ($data as $key => $arr) {
            $csv_data = [];
            foreach ($header as $header_key) {
                $csv_data[] = isset($arr[$header_key]) ? $arr[$header_key] : '';
            }
            fputcsv($this->file_handler, $csv_data);
        }
    }

    protected function sendEmail($title, $email, $content = null)
    {
        if (!isset($content)) {
            $content = "The following document valid to download 5 days from the date of issue : <br><br>";

            rewind($this->file_handler);

            $s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');

            $expiry_time = 432000;

            $filename = strtolower($this->publisher_name) . "_" . $title . "_" . time() . ".csv";

            $s3->saveContent($filename, $this->file_handler);

            $content = $content . $this->publisher_name . " " . $title . "<br>" . $s3->getContentUrl($filename, true, $expiry_time);
        }

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo($email)
            ->setSubject($title)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
    }

    private function getPublisherTitle()
    {
        $publisher = Publisher::findOne(['publisher_id' => $this->publisher]);
        if (isset($publisher->title)) {
            return $publisher->title;
        } else {
            Yii::$app->slack->send('Error fetching Devco Publisher Details', array(
                    array(
                        'color' => 'warning',
                        'text' => 'Devco Publisher Not Found'
                    )
                )
            );
        }
        return false;
    }

    private function getEmailTitle($action, $param)
    {
        $checklist = [
            'get_OEMList' => 'Publisher OEM List',
            'get_BrandList' => 'Publisher Brand List',
            'get_ProductList' => 'Publisher Product List',
            'get_SalesTransaction_ByDateRange' => 'Publisher Sales Transaction',
            'get_FinancialTransaction_ByDateRange' => 'Publisher Financial Transaction',
            'get_AllProductAvailability' => 'Publisher All Product Availability',
            'get_ProductFormats' => 'Publisher Product Format',
            'get_AccountBalance' => 'Publisher Account Balance',
            'get_ProductAvailability' => 'Publisher Product Availability',
        ];

        foreach ($checklist as $checklist_key => $checklist_value) {
            if ($action == $checklist_key) {
                $title = $checklist[$checklist_key];
                if (array_key_exists("FROM_DATE", $param) && array_key_exists("TO_DATE", $param)) {
                    $title .= " From " . $param["FROM_DATE"] . " To " . $param["TO_DATE"];
                }
            }
        }

        return $title;
    }
}