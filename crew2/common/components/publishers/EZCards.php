<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class EZCards extends \offgamers\publisher\models\profile\EZCards
{
    const API_LOG_TAG = 'EZCards/products';

    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'API Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Access Token',
                'key' => 'ACCESS_TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Initial Delay (in seconds, supports decimals, like 0.5; default = 0.5)',
                'key' => 'CHECK_ORDER_INITIAL_DELAY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Subsequent Delay (in seconds, supports decimals, like 0.5; default = 0.5)',
                'key' => 'CHECK_ORDER_SUBS_DELAY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Max Retry Attempts; default = 1',
                'key' => 'CHECK_ORDER_MAX_ATTEMPTS',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient(1, static::API_LOG_TAG);
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_list = $this->getProductList()) {
                $this->processProductList($product_list);
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync EZCards Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync EZCards (ID : $this->publisher) : ",
                [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @return array
     */
    protected function getProductList()
    {
        try {
            $params = [
                'limit' => 100,
            ];
            $response = $this->getProductListFirstPage($params);
            $body = (string)$response->getBody();
            $array = Json::decode($body);
            $total_page = $array['data']['totalPage'] ?? 0;

            $product_list = [];
            $request_list = [];
            $requests = [];

            for ($i = 1; $i <= $total_page; $i++) {
                $header = $this->generateApiHeader();

                $url = $this->base_url . static::catalog_url . '?' . http_build_query(array_merge($params, [
                    'page' => $i,
                ]));
                $request = new Request('GET', $url, $header, '');

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, static::API_LOG_TAG);
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => $this->max_connection,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                ],
                'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                    $body = (string)$response->getBody();
                    try {
                        $data = Json::decode($body);
                        foreach ($data['data']['items'] as $item) {
                            $product_list[] = $item;
                        }
                    } catch (\Exception $e) {
                        $url = $request_list[$index]->getUri();
                        $this->errorReporter(['EZCards Invalid Response', ['url' => $url, 'reason' => $e->getMessage(), 'body' => $body]]);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    $url = $request_list[$index]->getUri();
                    if ($reason instanceof ClientException) {
                        $status_code = $reason->getResponse()->getStatusCode();
                        $body = $reason->getResponse()->getBody();
                        $this->errorReporter(['EZCards Client Exception', ['url' => $url, 'http_code' => $status_code, 'body' => $body]]);
                    } else {
                        $this->errorReporter(['EZCards Request Exception', ['url' => $url, 'body' => $reason->getRequest()->getBody()]]);
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            return $product_list;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync product list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    /**
     * @param array $params
     */
    protected function getProductListFirstPage($params)
    {
        return $this->sendRequest('GET', static::catalog_url, array_merge($params, [
            'page' => 1,
        ]));
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            if (!isset($product['format']) || $product['format'] != 'D' || !isset($product['isInstantDeliverySupported']) || !$product['isInstantDeliverySupported']) {
                continue;
            }
            $insert_product_list[] = [
                'sku' => $product['sku'],
                'denomination' => ($product['faceValue'] ?? ''),
                'attribute_1' => ($product['currency'] ?? ''),
                'attribute_3' => ($product['country'] ?? ''),
                'name' => ($product['name'] ?? ''),
                'description' => !empty($product['descriptions']) ? implode("\n", $product['descriptions']) : '',
                'cost_currency' => ($product['currency'] ?? ''),
                'cost_price' => ($product['prices'][0]['price'] ?? 0.00),
                'raw' => $product
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }
}
