<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use offgamers\publisher\models\PublisherSetting;
use Psr\Http\Message\ResponseInterface;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class EcoVoucher extends \offgamers\publisher\models\profile\EcoVoucher
{
    const API_LOG_TAG = 'EcoVoucher/products';

    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Distributor Code',
                'key' => 'DISTRIBUTOR_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Distributor Secret',
                'key' => 'DISTRIBUTOR_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient(1, static::API_LOG_TAG);
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_list = $this->getProductList()) {
                $this->processProductList($product_list);
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync EcoVoucher Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync EcoVoucher (ID : $this->publisher) : ",
                [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @return array
     */
    protected function getProductList()
    {
        $hash_params = ['distributorCode', 'timestamp'];
        $response = $this->sendRequest('GET', str_replace('{distributorCode}', $this->distributor_code, static::catalog_url), null, $hash_params);
        if ($data = $this->checkError($response)) {
            if (isset($data['products'])) {
                return $data['products'];
            }
        }
        return [];
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            $deno = implode(',', $product['amounts']);;
            $insert_product_list[] = [
                'sku' => $product['code'],
                'denomination' => $deno,
                'attribute_1' => ($product['currencyCode'] ?? ''),
                'name' => $product['name'],
                'raw' => $product,
                'cost_price' => 0.00,
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }
}
