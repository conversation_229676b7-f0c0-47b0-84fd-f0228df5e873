<?php

namespace common\components\publishers;

use Yii;

class Eneba extends \offgamers\publisher\models\profile\Eneba
{
    public $error_msg, $error_code;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Client ID',
                'key' => 'CLIENT_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Auth ID',
                'key' => 'AUTH_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Auth Secret',
                'key' => 'AUTH_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token',
                'key' => 'TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Expiry',
                'key' => 'TOKEN_EXPIRY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Flag',
                'key' => 'GET_TOKEN_LOCK',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Timestamp',
                'key' => 'GET_TOKEN_LOCK_TIMESTAMP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }
    
    public function renderColumn($url, $model, $key)
    {
        return null;
    }
}