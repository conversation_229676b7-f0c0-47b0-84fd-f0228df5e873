<?php

namespace common\components\publishers;

use \offgamers\publisher\models\profile\library\FuluAes;
use common\models\PublisherSetting;
use common\models\SyncPublisherProductModel;
use offgamers\base\traits\GuzzleTrait;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class FuluDTU extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher;
    private $error, $error_msg, $error_code;
    const API_GET_PRODUCT = "api/user-goods/list";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'API_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Secret ID',
                'key' => 'SECRET_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Secret Key',
                'key' => 'SECRET_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Notify Url',
                'key' => 'POSTBACK_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Currency',
                'key' => 'API_CURRENCY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        set_time_limit(300);
        ini_set('memory_limit', '256M');
        $this->getExistingConfig();
        $this->initClient();
        $this->syncProductList();
    }

    public function syncProductList()
    {
        try {
            $page = 1;
            $product_list = [];
            $query_data = [
                "timestamp" => time(),
                "page" => $page,
                "per_page" => "100" // max 100 product show per page
            ];

            for ($page_count = 1; $page_count <= $page; $page_count++) {
                if ($page_count > 1) {
                    $query_data['page'] = $page_count;
                }

                $aes = new FuluAes($this->configuration_data['SECRET_KEY']);
                $request_data = ['secret_id' => $this->configuration_data['SECRET_ID'], 'data' => $aes->encrypt($query_data)];

                if ($product_data = $this->getProduct($request_data)) {
                    foreach ($product_data['data'] as $key => $product_arr) {
                        $data = $this->parseData($product_arr);
                        if ($data) {
                            $product_list[] = $data;
                        } else {
                            $this->error = [
                                "message" => "Unable To Insert Product (Missing Data) : \n" . JSON::encode($product_arr) . "\n"
                            ];
                            $this->reportError();
                        }
                    }
                    if (isset($product_data['last_page']) && $product_data['last_page'] > $page) {
                        $page = $product_data['last_page'];
                    }
                }
            }

            if (!empty($product_list)) {
                SyncPublisherProductModel::syncPublisherProductList($this->publisher, $product_list, true);
            }

        } catch (\Exception $e) {
            Yii::$app->slack->send(
                'FULU DTU Product Exception Error',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    private function getProduct($params)
    {
        $response = $this->sendRequest(static::API_GET_PRODUCT, $params);
        $data = $this->checkError($response);
        if (!$data) {
            $this->reportError();
        }
        return $data;
    }

    private function reportError()
    {
        Yii::$app->slack->send(
            'FULU DTU Product Report Error',
            array(
                array(
                    'color' => 'warning',
                    'text' => 'Error Message : ' . JSON::encode($this->error) . '\n',
                ),
            )
        );
    }

    private function sendRequest($action, $params)
    {
        $url = $this->configuration_data['API_URL'] . '/' . $action;

        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->client->request('POST', $url, $options);
    }

    private function parseData($product)
    {
        if (isset($product['product_id']) && isset($product['name'])) {
            switch ($product['product_type']) {
                case '3':
                    $product_type = "Soft Pin";
                    break;
                case '4':
                    $product_type = "Direct Top Up";
                    break;
                default:
                    $product_type = "Undefined product type, please check with publisher.";
                    break;
            }

            $data = [
                'name' => $product['name'],
                'cost_currency' => $product['sales_currency'] ?: '',
                'cost_price' => $product['sales_price'] ?: '',
                'sku' => (string)$product['product_id'],
                'raw' => JSON::encode($product),
                'attribute_1' => $product_type,
                'attribute_2' => (string)$product['face_value'] ?: '',
            ];

            return $data;
        }

        return false;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();

            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == "1") {
                    $aes = new FuluAes($this->configuration_data['SECRET_KEY']);
                    return $aes->decrypt($data['data']);
                } else {
                    if (isset($data['status'])) {
                        $this->error_code = "1000";
                    }

                    $this->error = [
                        'data' => $data,
                        'message' => ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '')
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response,
                    'message' => ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '')
                ];
            }

            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}