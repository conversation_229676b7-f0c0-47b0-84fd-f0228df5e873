<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class LikeCard extends \offgamers\publisher\models\profile\LikeCard
{
    const API_LOG_TAG = 'LikeCard/products';

    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Device ID',
                'key' => 'DEVICE_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Hash Key',
                'key' => 'HASH_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Secret IV',
                'key' => 'SECRET_IV',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Secret Key',
                'key' => 'SECRET_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Security Code',
                'key' => 'SECURITY_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Phone',
                'key' => 'PHONE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'LikeCard BO Login Email',
                'key' => 'EMAIL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'LikeCard BO Login Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient(1, static::API_LOG_TAG);
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_list = $this->getProductList()) {
                $this->processProductList($product_list);
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync LikeCard Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync LikeCard (ID : $this->publisher) : ",
                [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @return array
     */
    protected function getProductList()
    {
        try {
            $response = $this->getCategories();
            $body = (string)$response->getBody();
            $data = Json::decode($body);
            $category_ids = !empty($data['data']) ? $this->getCategoryIds($data['data'], true) : [];

            $product_list = [];
            $request_list = [];
            $requests = [];

            $i = 0;
            foreach ($category_ids as $category_id) {
                $header = [];
                $payload = [
                    'categoryId' => $category_id,
                    'deviceId' => $this->device_id,
                    'email' => $this->email,
                    'password' => $this->password,
                    'securityCode' => $this->security_code,
                    'langId' => 1,
                ];

                $url = $this->base_url . static::products_url;
                $request = new Request('POST', $url, $header, http_build_query($payload));

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, static::API_LOG_TAG, [
                    'form_params' => $payload
                ]);
                $i ++;
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => $this->max_connection,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                ],
                'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                    $body = (string)$response->getBody();
                    try {
                        $data = Json::decode($body);
                        if (!isset($data['response']) || $data['response'] != 1) {
                            if ($data['message'] == 'No available products') {
                                return;
                            }
                            $url = $request_list[$index]->getUri();
                            $this->errorReporter(['LikeCard Invalid Response', ['url' => $url, 'reason' => '', 'body' => $body]]);
                        } else {
                            foreach ($data['data'] as $item) {
                                $product_list[] = $item;
                            }
                        }
                    } catch (\Exception $e) {
                        $url = $request_list[$index]->getUri();
                        $this->errorReporter(['LikeCard Invalid Response', ['url' => $url, 'reason' => $e->getMessage(), 'body' => $body]]);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    $url = $request_list[$index]->getUri();
                    if ($reason instanceof ClientException) {
                        $status_code = $reason->getResponse()->getStatusCode();
                        $body = $reason->getResponse()->getBody();
                        $this->errorReporter(['LikeCard Client Exception', ['url' => $url, 'http_code' => $status_code, 'body' => $body]]);
                    } else {
                        $this->errorReporter(['LikeCard Request Exception', ['url' => $url, 'body' => $reason->getRequest()->getBody()]]);
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            return $product_list;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync product list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    protected function getCategories()
    {
        return $this->sendRequest(static::categories_url, []);
    }

    protected function getCategoryIds($categories, $skip_with_child) {
        $category_ids = [];
        foreach ($categories as $category) {
            if (!$skip_with_child || empty($category['childs'])) {
                $category_ids[] = $category['id'];
            }
            if (!empty($category['childs'])) {
                $category_ids = array_merge($category_ids, $this->getCategoryIds($category['childs'], $skip_with_child));
            }
        }
        return $category_ids;
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            if (!isset($product['optionalFieldsExist']) || $product['optionalFieldsExist']) {
                continue;
            }
            if (!isset($product['available']) || !$product['available']) {
                continue;
            }
            $insert_product_list[] = [
                'sku' => '' . $product['productId'],
                'denomination' => '' . ($product['sellPrice'] ?? ''),
                'attribute_1' => ($product['productCurrency'] ?? ''),
                'name' => ($product['productName'] ?? ''),
                'cost_currency' => ($product['productCurrency'] ?? ''),
                'cost_price' => ($product['productPrice'] ?? 0.00),
                'raw' => $product
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }
}
