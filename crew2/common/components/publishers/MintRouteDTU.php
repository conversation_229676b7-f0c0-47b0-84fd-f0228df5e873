<?php

namespace common\components\publishers;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \common\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;

class MintRouteDTU extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'API_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Username',
                'key' => 'USER_NAME',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'PUBLIC KEY',
                'key' => 'PUBLIC_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'PRIVATE KEY',
                'key' => 'PRIVATE_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial('config',
            array('configs' => $configuration, 'data' => $this->configuration_data));
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a('<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            }
        ], $model, $key, -1);
    }

}