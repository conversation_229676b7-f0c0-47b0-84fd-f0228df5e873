<?php

namespace common\components\publishers;

use common\models\PublisherSetting;
use InvalidArgumentException;
use Yii;

class Neosurf extends \offgamers\publisher\models\profile\Neosurf
{
    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @return array[]
     */
    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'ID User',
                'key' => 'ID_USER',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Secret Key',
                'key' => 'SECRET_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'ID Reseller Prefix',
                'key' => 'ID_RESELLER_PREFIX',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }
    
    public function renderColumn($url, $model, $key)
    {
        return null;
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

}
