<?php

namespace common\components\publishers;

use Yii;
use \common\models\PublisherSetting;

class NetDragonDTU extends \yii\base\Model
{
    public $configuration_data, $publisher;
    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Private Key',
                'key' => 'PRIVATE_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Service Provider',
                'key' => 'SERVICE_PROVIDER',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Payment Channel Id',
                'key' => 'PAYMENT_CHANNEL_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Is For Test',
                'key' => 'IS_FOR_TEST',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }
    public function renderColumn($url, $model, $key)
    {
        return null;
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

}