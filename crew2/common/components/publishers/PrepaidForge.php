<?php

namespace common\components\publishers;

use common\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\base\Model;

class PrepaidForge extends Model
{
    public $configuration_data;
    public $publisher;

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Username',
                'key' => 'USER_NAME',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        ];
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function renderColumn($url, $model, $key)
    {
        return null;
    }
}
