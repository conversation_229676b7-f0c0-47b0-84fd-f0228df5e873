<?php

namespace common\components\publishers;

use common\models\SyncPublisherProductModel;
use Guz<PERSON><PERSON>ttp\Exception\ClientException;
use Guz<PERSON>Http\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

class Pro extends \offgamers\publisher\models\profile\Pro
{
    const API_LOG_TAG = 'PRO/products';

    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    public array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */

    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Client ID',
                'key' => 'CLIENT_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Client Secret',
                'key' => 'CLIENT_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Initial Delay (in seconds, supports decimals, like 0.5; default = 5)',
                'key' => 'CHECK_ORDER_INITIAL_DELAY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Subsequent Delay (in seconds, supports decimals, like 0.5; default = 1)',
                'key' => 'CHECK_ORDER_SUBS_DELAY',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Check Order Max Retry Attempts; default = 5',
                'key' => 'CHECK_ORDER_MAX_ATTEMPTS',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Maximum Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->getConfig();
        $this->initClient(1, static::API_LOG_TAG);
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            if ($product_list = $this->getProductList()) {
                $this->processProductList($product_list);
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync PRO Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync PRO (ID : $this->publisher) : ",
                [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @return array
     */
    protected function getProductList()
    {
        try {
            $page_size = 1000;
            $response = $this->getProductListFirstPage($page_size);
            $body = (string)$response->getBody();
            $array = Json::decode($body);
            $product_list = $array['data'] ?? [];
            $total_page = $array['page']['totalPages'] ?? 1;

            $request_list = [];
            $requests = [];

            for ($i = 2; $i <= $total_page; $i++) {
                $url = str_replace('{pageNum}', $i, str_replace('{pageSize}', $page_size, static::catalog_url));
                $payload = json_encode([
                    'id' => null,
                ]);
                $headers = $this->generateApiHeader($payload);

                $request = new Request('POST', $url, $headers, $payload);

                $request_list[$i] = $request;
                $requests[$i] = $this->client->getAsyncRequest($this->client, $request, static::API_LOG_TAG, [
                    'headers' => $headers,
                    'body' => $payload,
                ]);
            }

            $pool = new Pool($this->client, $requests, [
                'concurrency' => $this->max_connection,
                'options' => [
                    'timeout' => 30,
                    'proxy' => Yii::$app->params['proxy'],
                ],
                'fulfilled' => function ($response, $index) use (&$product_list, $request_list) {
                    if ($body = $this->checkError($response)) {
                        try {
                            foreach ($body['data'] as $item) {
                                $product_list[] = $item;
                            }
                        } catch (\Exception $e) {
                            $url = $request_list[$index]->getUri();
                            $this->appendError($url, 'PRO Invalid Response');
                        }
                    } else {
                        $url = $request_list[$index]->getUri();
                        $this->appendError($url);
                    }
                },
                'rejected' => function ($reason, $index) use ($request_list) {
                    /**
                     * @var Request[] $request_list
                     */
                    $url = $request_list[$index]->getUri();
    
                    if ($reason instanceof RequestException) {
                        $response = $reason->getResponse();
                        $this->checkError($response);
                        $this->appendError($url);
                    } elseif ($reason instanceof \Exception) {
                        $this->appendError($url, $reason->getMessage());
                    } else {
                        $this->appendError($url, 'Unknown error occurred');
                    }
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();

            return $product_list;
        } catch (\Exception $e) {
            Yii::$app->slack->send('Fail to sync product list', array(
                array(
                    'color' => 'warning',
                    'text' => $e->getTraceAsString(),
                ),
            ));
        }
    }

    protected function getProductListFirstPage($page_size)
    {
        $url = str_replace('{pageNum}', '1', str_replace('{pageSize}', $page_size, static::catalog_url));
        return $this->sendRequest($url, [
            'id' => null,
        ]);
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            if (!empty($product['platformConfig'])) {
                continue;
            }
            $insert_product_list[] = [
                'sku' => $product['id'] . '',
                'name' => ($product['goodsName'] ?? ''),
                'cost_currency' => ($product['costCurrency'] ?? ''),
                'cost_price' => ($product['payPrice'] ?? 0.00),
                'raw' => $product
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }

    /**
     * @param $url
     * @param string $error_msg
     * @return void
     */
    protected function appendError($url, string $error_msg = '')
    {
        $this->error_list[] = [
            'url' => (string)$url,
            'error_msg' => ($error_msg ?: $this->error_msg)
        ];

        $this->error_msg = '';
    }

}
