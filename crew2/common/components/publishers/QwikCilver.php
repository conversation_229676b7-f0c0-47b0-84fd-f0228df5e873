<?php

namespace common\components\publishers;

use GuzzleHttp\Psr7\Response;
use Yii;
use common\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use common\models\SyncPublisherProductModel;
use Guz<PERSON>Http\Pool;
use yii\helpers\Json;
use yii\helpers\Url;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;

class QwikCilver extends \offgamers\publisher\models\profile\QwikCilver
{
    /**
     * @var int
     */
    public int $publisher;
    /**
     * @var array
     */
    protected array $configuration_data = [];
    /**
     * @var array
     */
    protected array $error_list = [];

    /**
     * @return array[]
     */
    protected function getFieldList()
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Client ID',
                'key' => 'CLIENT_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Client Secret',
                'key' => 'CLIENT_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Username',
                'key' => 'USERNAME',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Password',
                'key' => 'PASSWORD',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Billing Name',
                'key' => 'BILLING_NAME',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Billing Email',
                'key' => 'BILLING_EMAIL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Billing Postcode',
                'key' => 'BILLING_POSTCODE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Billing Country Code',
                'key' => 'BILLING_COUNTRY_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Max Concurrent Connection',
                'key' => 'MAX_CONCURRENT_CONNECTION',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'default' => 100]
            ],
            [
                'label' => 'Token',
                'key' => 'TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Expiry',
                'key' => 'TOKEN_EXPIRY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Flag',
                'key' => 'GET_TOKEN_LOCK',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock Timestamp',
                'key' => 'GET_TOKEN_LOCK_TIMESTAMP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ]
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    /**
     * @return void
     * @throws InvalidArgumentException
     */
    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    /**
     * @return void
     */
    public function sync()
    {
        $this->publisher_id = $this->publisher;
        $this->initClient();
        $this->getConfig();
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            $this->getToken();
            if ($categories_id = $this->getRootCategoryId()) {
                if ($sku_list = $this->getProductSkuList($categories_id)) {
                    if ($product_list = $this->getProductDetail($sku_list)) {
                        $this->processProductList($product_list);
                    }
                }
            }

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync QwikCilver Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync QwikCilver (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * return int
     * @throws \Exception
     */
    protected function getRootCategoryId(): int
    {
        if (strpos($this->base_url, 'sandbox') === false) {
            $path = self::PRODUCT_LIST_URL;
        } else {
            $path = self::CATEGORY_URL;
        }

        $headers = $this->getHeaders($path);

        $response = $this->sendRequest('GET', $path, $headers);

        if ($data = $this->verifyResponse($response)) {
            if (isset($data['id'])) {
                return (int)$data['id'];
            }
            else{
                if (isset($data['root_category']['category_id'])) {
                    return (int)$data['root_category']['category_id'];
                }
            }
        }

        throw new \Exception('Failed to get root category from publisher' . $this->error_msg);
    }

    /**
     * @return array
     * @throws \Exception
     * @var integer $categories_id
     * @var integer $page
     */
    protected function getProductSkuList(int $categories_id, int $page = 1): array
    {
        $sku_list = [];
        $product_list = [];

        $url = self::PRODUCT_LIST_URL . "/$categories_id/products" . '?' . http_build_query(['limit' => 100, 'offset' => ($page - 1) * 100]);

        $headers = $this->getHeaders($url);

        $response = $this->sendRequest('GET', $url, $headers);

        if ($data = $this->verifyResponse($response)) {
            if (isset($data['products'])) {
                $product_list = $data['products'];
            }

            foreach ($product_list as $product) {
                $sku_list[] = $product['sku'];
            }

            // Get all products lists from next pages
            if (isset($data['productsCount']) && $data['productsCount'] > $page * 100) {
                $sku_list = array_merge($sku_list, $this->getProductSkuList($categories_id, $page + 1));
            }
        } else {
            $this->appendError($url, $this->error_msg);
        }

        if (!empty($sku_list)) {
            return $sku_list;
        }

        throw new \Exception('Failed to get sku list from publisher');
    }

    /**
     * @param array $sku_list
     * @return array
     */
    protected function getProductDetail(array $sku_list): array
    {
        $product_list = [];
        $requests = [];
        $request_list = [];

        for ($i = 0; $i < count($sku_list); $i++) {
            $path = self::PRODUCT_DETAILS_URL . '/' . $sku_list[$i];
            $headers = $this->getHeaders($path);
            $request = new Request('GET', $this->base_url . '/' . $path, $headers, '');

            $request_list[$i] = $request;
            $requests[$i] = $this->client->getAsyncRequest($this->client, $request, 'QwikCilver' . '/' . $sku_list[$i]);
        }

        $pool = new Pool($this->client, $requests, [
            'concurrency' => $this->max_connection,
            'options' => [
                'http_errors' => false,
                'timeout' => 30,
                'proxy' => Yii::$app->params['proxy'],
            ],
            'fulfilled' => function (Response $response, $index) use (&$product_list, $request_list) {
                if ($data = $this->verifyResponse($response)) {
                    if (!empty($data['sku']) && !empty($data['name'])) {
                        $product_list[] = $data;
                    } else {
                        $url = $request_list[$index]->getUri();
                        $this->appendError($url, 'Product with empty sku / product name');
                    }
                } else {
                    /**
                     * @var Request[] $request_list
                     */
                    $url = $request_list[$index]->getUri();
                    $this->appendError($url, 'Invalid response from publisher');
                }
            },
            'rejected' => function ($reason, $index) use ($request_list) {
                /**
                 * @var Request[] $request_list
                 */
                $url = $request_list[$index]->getUri();

                if ($reason instanceof RequestException) {
                    $response = $reason->getResponse();
                    $this->verifyResponse($response);
                    $this->appendError($url);
                } elseif ($reason instanceof \Exception) {
                    $this->appendError($url, $reason->getMessage());
                } else {
                    $this->appendError($url, 'Unknown error occurred');
                }
            }
        ]);

        $promise = $pool->promise();
        $promise->wait();

        return $product_list;
    }

    /**
     * @param $url
     * @param string $error_msg
     * @return void
     */
    protected function appendError($url, string $error_msg = '')
    {
        $this->error_list[] = [
            'url' => (string)$url,
            'error_msg' => ($error_msg ?: $this->error_msg)
        ];

        $this->error_msg = '';
    }

    /**
     * @param array $product_list
     * @return void
     */
    protected function processProductList(array $product_list)
    {
        $insert_product_list = [];
        foreach ($product_list as $product) {
            $denomination_list = '';
            $range_denomination_list = '';
            if ($product['price']['type'] == 'SLAB') {
                if (!empty($product['price']['denominations'])) {
                    $denomination_list = implode(',', $product['price']['denominations']);
                }
            } elseif ($product['price']['type'] == 'RANGE' && isset($product['price']['min'])) {
                if (!empty($product['price']['denominations'])) {
                    $range_denomination_list = implode(',', $product['price']['denominations']);
                }
                $denomination_list = $product['price']['min'] . (isset($product['price']['max']) ? '-' . $product['price']['max'] : '') . (!empty($product['price']['denomination']) ? '(' . implode(',', $product['price']['denominations']) . ')' : '');
            }

            $insert_product_list[] = [
                'sku' => $product['sku'],
                'denomination' => $denomination_list,
                # Set Publisher Currency ID as attributes
                'attribute_1' => ($product['price']['currency']['numericCode'] ?? ''),
                'attribute_2' => $range_denomination_list,
                'name' => $product['name'],
                'description' => $product['description'] ?? '',
                'cost_currency' => ($product['price']['currency']['code'] ?? ''),
                'cost_price' => 0.00,
                'raw' => $product
            ];
        }

        SyncPublisherProductModel::syncPublisherProductList($this->publisher_id, $insert_product_list, empty($this->error_list));
    }


}