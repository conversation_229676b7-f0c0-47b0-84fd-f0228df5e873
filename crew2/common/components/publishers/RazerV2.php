<?php

namespace common\components\publishers;

use Yii;

class RazerV2 extends \offgamers\publisher\models\profile\RazerV2
{

    /**
     * @return array[]
     */

    protected function getFieldList(): array
    {
        return [
            [
                'label' => 'API URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Username',
                'key' => 'USER_NAME',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Secret',
                'key' => 'SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control', 'required' => true]
            ],
            [
                'label' => 'Token',
                'key' => 'TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Expiry',
                'key' => 'TOKEN_EXPIRY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Get Token Lock',
                'key' => 'GET_TOKEN_LOCK',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Token Lock Timestamp',
                'key' => 'GET_TOKEN_LOCK_TIMESTAMP',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        ];
    }

    /**
     * @return string
     */
    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array(
                'configs' => $configuration,
                'data' => $this->configuration_data
            )
        );
    }

    public function renderColumn($url, $model, $key)
    {
        return null;
    }

    public function request($method, $url, $options)
    {
        // No need to return any client, crew2 will not call publisher API
        return null;
    }
}