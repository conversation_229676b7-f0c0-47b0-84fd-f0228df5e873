<?php

namespace common\components\publishers;

use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Yii;
use common\models\SyncPublisherProductModel;
use yii\helpers\Html;
use Exception;
use offgamers\publisher\models\Publisher;
use yii\helpers\Json;
use yii\helpers\Url;


class VTC extends \offgamers\publisher\models\profile\VTC
{
    public $publisher_name;

    public $error_list;
    const API_ENDPOINT_ALL_CATEGORY = "GetInfo/get-all-category";
    const API_ENDPOINT_CATEGORY = "GetInfo/get-category";
    const API_ENDPOINT_PRODUCT = "GetInfo/get-product";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Partner Code',
                'key' => 'PARTNER_CODE',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Private Key',
                'key' => 'PRIVATE_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Public Key',
                'key' => 'PUBLIC_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'TRIPLE_DES_KEY',
                'key' => 'TRIPLE_DES_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        try {
            $this->getConfig();
            $this->syncProductList();
            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync VTC Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync VTC (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    /**
     * @throws Exception
     */
    public function syncProductList()
    {
        $this->publisher_name = $this->getPublisherTitle();

        $query_data = [
            "partnerCode" => $this->partner_code,
            "categoryID" => "",
            "productID" => "",
            "productAmount" => "",
            "customerID" => "",
            "partnerTransID" => "CATEGORY/" . time(),
            "partnerTransDate" => date("YmdHis", time()),
            "data" => ""
        ];

        $query_data['dataSign'] = $this->generateSignature($query_data);

        if ($category_list = $this->getAllCategory($query_data)) {
            $sub_category_list = $this->getSubCategory($category_list);
            if ($sub_category_list) {
                $product_list = $this->getProductList($sub_category_list);
                if ($product_list) {
                    SyncPublisherProductModel::syncPublisherProductList($this->publisher, $product_list, true);
                }
            }
        }
    }

    protected function getSubCategory($category_list)
    {
        $return_arr = [];
        if ($sub_category_list = $this->processAsyncRequest(self::API_ENDPOINT_CATEGORY, $category_list)) {
            foreach ($sub_category_list as $sub_category) {
                if (is_array($sub_category)) {
                    foreach ($sub_category as $sub_category_item) {
                        if (isset($sub_category_item['CategoryID'])) {
                            $return_arr[] = $sub_category_item['CategoryID'];
                        }
                    }
                }
            }
        }
        return $return_arr;
    }

    protected function getProductList($sub_category_list)
    {
        $return_arr = [];
        if ($product_list = $this->processAsyncRequest(self::API_ENDPOINT_PRODUCT, $sub_category_list)) {
            foreach ($product_list as $product) {
                if (is_array($product)) {
                    foreach ($product as $sub_product_item) {
                        if (isset($sub_product_item['ProductID'])) {
                            $return_arr[] = [
                                'name' => $sub_product_item['ProductName'],
                                'sku' => $sub_product_item['ProductCode'],
                                'denomination' => (string)$sub_product_item['Value'],
                                'attribute_1' => (string)$sub_product_item['CategoryID'],
                                'attribute_2' => (string)$sub_product_item['ProductID'],
                                'raw' => $sub_product_item
                            ];
                        }
                    }
                }
            }
        }
        return $return_arr;
    }


    /**
     * @param string $path
     * @param array $category_list
     * @return array
     */
    protected function processAsyncRequest(string $path, array $category_list): array
    {
        $client = $this->client;

        $return_arr = [];
        $request_list = [];
        $requests = [];

        $options = [
            'http_errors' => false,
        ];

        for ($i = 0; $i < count($category_list); $i++) {
            $query_data = [
                "partnerCode" => $this->partner_code,
                "categoryID" => (string)$category_list[$i],
                "productID" => "",
                "productAmount" => "",
                "customerID" => "",
                "partnerTransID" => explode('/', $path)[1] ."/$category_list[$i]/" . time(),
                "partnerTransDate" => date("YmdHis", time()),
                "data" => ""
            ];

            $query_data['dataSign'] = $this->generateSignature($query_data);

            $url = $this->base_url . '/' . $path;

            $request = new Request('POST', $url, [
                'Content-Type' => 'application/json'
            ], Json::encode($query_data));

            $request_list[$i] = $request;

            $requests[$i] = $this->client->getAsyncRequest($this->client, $request, 'VTC_' . explode('/', $path)[1] . '_' . $category_list[$i]);
        }

        $pool = new Pool($client, $requests, [
            'concurrency' => 10,
            'options' => $options,
            'fulfilled' => function (Response $response, $index) use (&$return_arr, $request_list) {
                $data = $this->checkError($response);
                if ($data) {
                    $return_arr[] = $this->decodeDataInfo($data);
                } else {
                    $this->appendError($request_list[$index]);
                }
            },
            'rejected' => function ($reason, $index) use ($request_list) {
                if ($reason instanceof RequestException) {
                    $response = $reason->getResponse();
                    if ($response) {
                        $message = (string)$response->getBody();
                        $this->appendError($request_list[$index], $message);
                    } else {
                        $this->appendError($request_list[$index], $reason->getMessage());
                    }
                } elseif ($reason instanceof \Exception) {
                    $this->appendError($request_list[$index], $reason->getMessage());
                } else {
                    $this->appendError($request_list[$index], 'Unknown error occurred');
                }
            }
        ]);

        $promise = $pool->promise();
        $promise->wait();

        return $return_arr;
    }

    /**
     * @param Request $url
     * @param string $error_msg
     * @return void
     */
    protected function appendError(Request $url, string $error_msg = '')
    {
        $this->error_list[] = [
            'url' => (string)$url->getUri(),
            'data' => $url->getBody(),
            'error_msg' => ($error_msg ?: $this->error_msg)
        ];

        $this->error_msg = '';
    }

    private function getAllCategory($query_data)
    {
        $response = $this->sendRequest('POST', static::API_ENDPOINT_ALL_CATEGORY, $query_data);
        $data = $this->checkError($response);
        $return_arr = [];
        if ($category_list = $this->decodeDataInfo($data)) {
            foreach ($category_list as $category) {
                if (isset($category['CategoryID'])) {
                    $return_arr[] = $category['CategoryID'];
                }
            }
            return $return_arr;
        } else {
            throw new \Exception('Failed to fetch category list from VTC');
        }
    }

    private function getPublisherTitle()
    {
        $publisher = Publisher::findOne(['publisher_id' => $this->publisher]);
        if (isset($publisher->title)) {
            return $publisher->title;
        } else {
            throw new \Exception('VTC Publisher Not Found');
        }
    }
}