<?php

namespace common\components\publishers;

use Yii;
use common\models\SyncPublisherProductModel;
use yii\helpers\Html;
use Exception;
use offgamers\publisher\models\Publisher;
use yii\helpers\Json;
use yii\helpers\Url;

class Wogi extends \offgamers\publisher\models\profile\Wogi
{
    public $error_msg, $error_code, $file_handler, $publisher_name;
    const PRODUCT_API = "products";

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'User ID',
                'key' => 'USERID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Secret',
                'key' => 'SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Url',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'GST (%)',
                'key' => 'GST',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Low Margin (%)',
                'key' => 'LOW_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Minimum Margin (%)',
                'key' => 'MIN_MARGIN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>',
                    'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            },
            'product_list' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-list"></span>',
                    Url::to(['product-list', 'publisher_id' => $model->publisher_id])
                );
            }
        ], $model, $key, -1);
    }

    public function sync()
    {
        $this->getExistingConfig();
        $this->syncProductList();
    }

    public function syncProductList()
    {
        try {
            $product_list = [];
            $this->publisher_name = $this->getPublisherTitle();
            if (!$this->publisher_name) {
                throw new Exception("Publisher Name not found in database.");
            }
            if ($product_data = $this->getProduct()) {
                foreach ($product_data as $key => $product_arr) {
                    if ($parse_data = $this->parseData($product_arr)) {
                        $product_list[] = $parse_data;
                    }
                }
            }
            SyncPublisherProductModel::syncPublisherProductList($this->publisher, $product_list, true);
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                'Wogi Product Exception Error',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    private function parseData($product)
    {
        if (isset($product['productId']) && isset($product['productName']) && isset($product['baseValueAmount']) && isset($product['currency']) && isset($product['cardAmount'])) {
            $processing_fees = 0;
            $cost_price = 0;
            $tax = 0;
            $is_range_product = false;

            if ($product['cardAmount'] == 'range') {
                $is_range_product = true;
                $deno = $product['baseValueAmount'] . " - " . ($product['maxValueAmount'] ?? '');
            } elseif ($product['cardAmount'] == 'exact') {
                $deno = $product['baseValueAmount'];
            } else {
                $this->error_msg = "Unsupported Wogi template method. Product ID : " . $product['productId'] . "\n" . "Response : " . json_encode($product);
                $this->reportError();
            }

            if (!$is_range_product) {
                $processing_fees = $this->getProcessingFees($product);
                $cost_price = $this->costPriceCalculation($product, $processing_fees);
                if (!empty($this->configuration_data['GST']) && $this->configuration_data['GST'] > 0 && $processing_fees > 0) {
                    $tax = round($processing_fees * ($this->configuration_data['GST'] / 100), 2);
                    $cost_price += $tax;
                }
            }

            if (!empty($this->configuration_data['GST']) && $this->configuration_data['GST'] > 0) {
                $product['gst'] = $this->configuration_data['GST'];
            }

            $processing_fees_display = $this->costFormulaDisplay($product, $processing_fees, $is_range_product, $tax, $deno);

            $data = [
                'name' => $product['productName'],
                'cost_currency' => $product['currency'],
                'cost_price' => $cost_price,
                'sku' => (string)$product['productId'],
                'raw' => $product,
                'attribute_1' => (string)$product['productDiscountRate'],
                'attribute_2' => (string)$processing_fees,
                'attribute_3' => (string)$processing_fees_display,
                'denomination' => (string)$deno,
                'description' => $product['productDescription'] ?? '',

            ];

            return $data;
        } else {
            $this->error_msg = "";
        }
    }

    private function costPriceCalculation($product, $processing_fees)
    {
        $cost_price = 0;
        if (isset($product['productDiscountRate']) && $product['productDiscountRate'] > 0 && $product['productDiscountRate'] != 'n/a') {
            $cost_price = $product['baseValueAmount'] * ($product['productDiscountRate'] / 100) + $processing_fees;
        } else {
            $cost_price = $product['baseValueAmount'] + $processing_fees;
        }
        return $cost_price;
    }

    private function getProcessingFees($product)
    {
        $processing_fees = 0;
        if (isset($product['productProcessingFeeAmount']['amount']) && $product['productProcessingFeeAmount']['amount'] > 0 && $product['productProcessingFeeAmount']['amount'] != 'n/a') {
            $processing_fees = $product['productProcessingFeeAmount']['amount'];
        } elseif (isset($product['productProcessingFeeRate']) && $product['productProcessingFeeRate'] > 0 && $product['productProcessingFeeRate'] != 'n/a') {
            $processing_fees = $product['baseValueAmount'] * ($product['productProcessingFeeRate'] / 100);
        }
        return $processing_fees;
    }

    private function costFormulaDisplay($product, $processing_fees, $is_range_product, $tax, $deno)
    {
        $processing_fees_display = "";
        if (!$is_range_product) {
            $processing_fees_display = $product['currency'] . $deno . " Base Price * " . $product['productDiscountRate'] . "% Discount Rate + (" . $product['currency'] . $processing_fees . " Processing Fee + " . $product['currency'] . $tax . " GST)";
        } else {
            $processing_fees_display = $product['currency'] . $deno . " Base Price * " . $product['productDiscountRate'] . "% Discount Rate ";
            if ($product['productProcessingFeeAmount']['amount'] >= 0 && $product['productProcessingFeeAmount']['amount'] != "n/a") {
                $processing_fees_display .= "+ (" . $product['currency'] . $product['productProcessingFeeAmount']['amount'] . " Processing Fee";
            } elseif ($product['productProcessingFeeRate'] > 0 && $product['productProcessingFeeRate'] != "n/a") {
                $processing_fees_display .= "+ (" . $product['productProcessingFeeRate'] . "% Processing Fee Tax";
            } elseif (isset($processing_fees)) {
                $processing_fees_display .= "+ (" . $product['currency'] . $processing_fees . " Processing Fee";
            }

            if (!empty($this->configuration_data['GST']) && $this->configuration_data['GST'] > 0) {
                $processing_fees_display .= " * " . $this->configuration_data['GST'] . "% GST)";
            } else {
                $processing_fees_display .= ")";
            }
        }
        return $processing_fees_display;
    }

    private function getProduct()
    {
        $response = $this->sendRequest('GET', static::PRODUCT_API);
        $data = $this->checkError($response);
        if (!isset($data['products'])) {
            $this->reportError();
        }
        return $data['products'];
    }

    private function reportError()
    {
        Yii::$app->slack->send(
            $this->publisher_name . ' Product Error',
            array(
                array(
                    'color' => 'warning',
                    'text' => 'Error Message : ' . $this->error_msg . '\n',
                ),
            )
        );
    }

    private function getPublisherTitle()
    {
        $publisher = Publisher::findOne(['publisher_id' => $this->publisher]);
        if (isset($publisher->title)) {
            return $publisher->title;
        } else {
            Yii::$app->slack->send('Error fetching Wogi Publisher Details', array(
                    array(
                        'color' => 'warning',
                        'text' => 'Wogi Publisher Not Found'
                    )
                )
            );
        }
        return false;
    }
}