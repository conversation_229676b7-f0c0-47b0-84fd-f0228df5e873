<?php

namespace common\components\publishers;

use common\models\Publisher;
use common\models\PublisherSetting;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Html;
use yii\helpers\Json;

class Xoxo extends \offgamers\publisher\models\profile\Xoxo
{
    public $configuration_data;
    public $publisher;
    private string $publisher_name;
    private const EMAIL_FILE_EXPIRY_TIME = 432000; // 5 Days

    protected const PAGE_LIMIT = 500;

    protected function getFieldList()
    {
        return [
            [
                'label' => 'Base API URL',
                'key' => 'XOXO_BASE_API_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Client Id',
                'key' => 'XOXO_CLIENT_ID',
                'type' => 'textarea',
                'option' => ['class' => 'form-control', 'rows' => 6]
            ],
            [
                'label' => 'Client Secret',
                'key' => 'XOXO_CLIENT_SECRET',
                'type' => 'textarea',
                'option' => ['class' => 'form-control', 'rows' => 6]
            ],
            [
                'label' => 'Access Token',
                'key' => 'XOXO_ACCESS_TOKEN',
                'type' => 'textarea',
                'option' => ['class' => 'form-control', 'rows' => 6]
            ],
            [
                'label' => 'Refresh Token',
                'key' => 'XOXO_REFRESH_TOKEN',
                'type' => 'textarea',
                'option' => ['class' => 'form-control', 'rows' => 6]
            ]
        ];
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    /**
     * @return void
     */
    public function sync()
    {
        $this->publisher_id = $this->publisher;
        if ($publisher = Publisher::find()->select(['publisher_id', 'title'])->where(['publisher_id' => $this->publisher_id])->asArray()->one()) {
            $this->publisher_name = $publisher['title'];
        } else {
            Yii::$app->slack->send(
                "Failed to sync Xoxo (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => 'Invalid publisher ID.']),
                    ],
                ]
            );
        }

        $this->initClient();
        $this->getConfig();
        $this->syncProductList();
    }

    /**
     * @return void
     */
    protected function syncProductList()
    {
        try {
            $this->getToken();
            $page = 1;

            $all_vouchers = $this->getVouchers(self::PAGE_LIMIT, $page);
            $all_vouchers = $this->mapVouchersData($all_vouchers);
            $this->sendVoucherCsvToEmail($all_vouchers);

            if (!empty($this->error_list)) {
                Yii::$app->slack->send(
                    'Error Sync Xoxo Product List : ' . $this->publisher,
                    [
                        [
                            'color' => 'warning',
                            'text' => Json::encode($this->error_list),
                        ],
                    ],
                    'DEBUG'
                );
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Xoxo (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    protected function getVouchers(int $limit, int $page, array $extra_options = [], $all_vouchers = [])
    {
        $data = [
            'limit' => $limit,
            'page' => $page
        ];
        $data = array_merge($data, $extra_options);

        $params = [
            'query' => self::API_VOUCHERS_QUERY,
            'tag' => 'plumProAPI',
            'variables' => [
                'data' => $data
            ]
        ];

        try {
            $response = $this->sendRequest("POST", self::API_PATH, $this->getHeaders(), $params);
            if ($data = $this->verifyResponse($response)) {
                $vouchers = $data['data']['getVouchers']['data'];
                $call_again = count($vouchers) == $limit;

                $all_vouchers = array_merge($all_vouchers, $vouchers);
                if ($call_again) {
                    return $this->getVouchers($limit, $page + 1, $extra_options, $all_vouchers);
                } else {
                    return $all_vouchers;
                }
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send(
                "Failed to sync Xoxo (ID : $this->publisher) : ", [
                    [
                        'color' => 'warning',
                        'text' => Json::encode(['error_msg' => $this->error_msg, 'exception_msg' => $e->getMessage()]),
                    ],
                ]
            );
        }
    }

    protected function mapVouchersData(array $all_vouchers): array
    {
        $mapped_data = [];
        foreach ($all_vouchers as $voucher) {
            $mapped_data[] = [
                $voucher['productId'] ?? '',
                $voucher['name'] ?? '',
                $voucher['description'] ?? '',
                $voucher['orderQuantityLimit'] ?? '',
                $voucher['termsAndConditionsInstructions'] ?? '',
                $voucher['expiryAndValidity'] ?? '',
                $voucher['redemptionInstructions'] ?? '',
                $voucher['categories'] ?? '',
                $voucher['lastUpdateDate'] ?? '',
                $voucher['imageUrl'] ?? '',
                $voucher['currencyCode'] ?? '',
                $voucher['currencyName'] ?? '',
                $voucher['countryName'] ?? '',
                $voucher['countryCode'] ?? '',
                $voucher['valueType'] ?? '',
                $voucher['maxValue'] ?? '',
                $voucher['minValue'] ?? '',
                $voucher['valueDenominations'] ?? '',
                $voucher['tatInDays'] ?? '',
                $voucher['usageType'] ?? '',
                $voucher['deliveryType'] ?? '',
                $voucher['fee'] ?? '',
                $voucher['discount'] ?? '',
                $voucher['exchangeRate'] ?? '',
                $voucher['isPhoneNumberMandatory'] ?? '',
                isset($voucher['countries']) ? json_encode($voucher['countries']) : ''
            ];
        }

        return $mapped_data;
    }

    protected function getCsvHeaders(): array
    {
        return [
            'productId',
            'name',
            'description',
            'orderQuantityLimit',
            'termsAndConditionsInstructions',
            'expiryAndValidity',
            'redemptionInstructions',
            'categories',
            'lastUpdateDate',
            'imageUrl',
            'currencyCode',
            'currencyName',
            'countryName',
            'countryCode',
            'valueType',
            'maxValue',
            'minValue',
            'valueDenominations',
            'tatInDays',
            'usageType',
            'deliveryType',
            'fee',
            'discount',
            'exchangeRate',
            'isPhoneNumberMandatory',
            'countries',
        ];
    }

    /**
     * @param $url
     * @param $model
     * @param $key
     * @return mixed
     */
    public function renderColumn($url, $model, $key)
    {
        $template = \mdm\admin\components\Helper::filterActionColumn('{update} {sync} {product_list}');
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a(
                    '<span class="fa fa-sync-alt"></span>', 'javascript:void(0)',
                    ['onclick' => 'sync_publisher(' . $model->publisher_id . ')']
                );
            }
        ], $model, $key, -1);
    }

    private function sendVoucherCsvToEmail(array $all_vouchers)
    {
        if (!$tmp = fopen('php://temp', 'w+')) {
            throw new \Exception('tmp directory not writable');
        }

        fputcsv($tmp, $this->getCsvHeaders());
        foreach ($all_vouchers as $voucher) {
            fputcsv($tmp, $voucher);
        }

        rewind($tmp);

        $s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');

        $content = "The following document valid to download 5 days from the date of issue : <br><br>";

        $filename = strtolower($this->publisher_name) . "_product_list_" . date('YmdHis') . ".csv";

        $s3->saveContent($filename, $tmp);

        $content = $content . $this->publisher_name . " Product List<br>" . $s3->getContentUrl($filename, true, self::EMAIL_FILE_EXPIRY_TIME);

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["syncPublisherProduct.notification"])
            ->setSubject($this->publisher_name . ' Products List')
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
    }
}
