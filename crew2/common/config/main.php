<?php
$params = array_merge(require __DIR__ . '/params.php', require __DIR__ . '/params-encoded.php', require __DIR__ . '/params-local.php');

return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'language' => 'en-US',
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'components' => [
        'mutex' => [
            'class' => 'yii\mutex\MysqlMutex',
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/messages',
                ],
            ],
        ],
        'mailer' => [
            'class' => 'offgamers\base\mail\AWSSESMailer',
            'mailQueueName' => 'MAIL_QUEUE',
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'OGP' => $params['slack.webhook.ogp'],
                'TAX' => $params['slack.webhook.tax'],
                'DEBUG' => $params['slack.webhook.debug'],
                'ANB' => $params['slack.webhook.anb'],
                'INFO' => $params['slack.webhook.info'],
                'BDT_REPLENISH' => ($params['slack.webhook.bdt.replenish'] ?? ''),
                'BDT_DTU' => ($params['slack.webhook.bdt.dtu'] ?? ''),
            ]
        ],
        'currency' => [
            'class' => 'offgamers\base\components\Currency',
        ],
        'country' => [
            'class' => 'offgamers\base\components\Country',
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'imageOptimizer' => [
            'class' => 'common\components\ImageOptimizer'
        ],
        'geoip' => [
            'class' => 'dpodium\yii2-geoip\components\CGeoIP',
        ],
    ],
];
