<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "admin".
 *
 * @property int $admin_id
 * @property int $admin_groups_id
 * @property string $admin_firstname
 * @property string $admin_lastname
 * @property string $admin_email_address
 * @property string $admin_password
 * @property string $admin_created
 * @property string $admin_modified
 * @property string $admin_logdate
 * @property int $admin_lognum
 * @property int $admin_login_attempt
 */
class Admin extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['admin_groups_id', 'admin_lognum', 'admin_login_attempt'], 'integer'],
            [['admin_created', 'admin_modified', 'admin_logdate'], 'safe'],
            [['admin_firstname', 'admin_lastname'], 'string', 'max' => 32],
            [['admin_email_address'], 'string', 'max' => 96],
            [['admin_password'], 'string', 'max' => 40],
            [['admin_email_address'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'admin_id' => 'Admin ID',
            'admin_groups_id' => 'Admin Groups ID',
            'admin_firstname' => 'Admin Firstname',
            'admin_lastname' => 'Admin Lastname',
            'admin_email_address' => 'Admin Email Address',
            'admin_password' => 'Admin Password',
            'admin_created' => 'Admin Created',
            'admin_modified' => 'Admin Modified',
            'admin_logdate' => 'Admin Logdate',
            'admin_lognum' => 'Admin Lognum',
            'admin_login_attempt' => 'Admin Login Attempt',
        ];
    }
}
