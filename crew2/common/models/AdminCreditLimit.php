<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "admin_credit_limit".
 *
 * @property int $admin_id
 * @property string $admin_credit_limit_max
 * @property string $admin_credit_limit_total
 * @property string $reset_team_limit_used
 * @property string $reset_team_limit_max
 */
class AdminCreditLimit extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin_credit_limit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['admin_id'], 'required'],
            [['admin_id'], 'integer'],
            [['admin_credit_limit_max', 'admin_credit_limit_total', 'reset_team_limit_used', 'reset_team_limit_max'], 'number'],
            [['admin_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'admin_id' => 'Admin ID',
            'admin_credit_limit_max' => 'Admin Credit Limit Max',
            'admin_credit_limit_total' => 'Admin Credit Limit Total',
            'reset_team_limit_used' => 'Reset Team Limit Used',
            'reset_team_limit_max' => 'Reset Team Limit Max',
        ];
    }
}
