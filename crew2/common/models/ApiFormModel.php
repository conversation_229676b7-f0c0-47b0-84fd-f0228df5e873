<?php

namespace common\models;

use Yii;
use yii\data\Sort;
use yii\helpers\Html;

abstract class ApiFormModel extends \yii\base\Model
{
    public $title, $page, $pageSize, $totalCount, $startAt, $endAt, $params, $filterUrl, $searchField = array();

    use \offgamers\base\models\ms\BaseMsTrait;

    public function __construct()
    {
        $this->initFormModel();
    }

    public function init(){
        //DO NOTHING
    }

    private function initFormModel(){
        $params = $_GET;

        if (!empty($params)) {
            unset($params['page']);
            $this->filterUrl = \yii\helpers\Url::to(array_merge([\yii\helpers\Url::base()], $params));
        }

        $this->pageSize = (!empty($_GET['pageSize']) ? $_GET['pageSize'] : 20);
        $this->page = (!empty($_GET['page']) ? $_GET['page'] : 1);
    }


    public function setPagination($totalCount)
    {
        $this->totalCount = $totalCount;

        $this->startAt = ($this->page - 1) * $this->pageSize + 1;

        $this->endAt = (($this->page * $this->pageSize) > $this->totalCount ? $this->totalCount : $this->page * $this->pageSize);
    }

    public function setArrayDataProvider($data, $key = 'id')
    {
        $sort = new \yii\data\Sort([
            'attributes' => [
                'title'
            ],
        ]);
        return new \yii\data\ArrayDataProvider([
            'key' => $key,
            'allModels' => $data,
            'sort' => $sort,
            'pagination' => [
                'pageSize' => $this->pageSize,
                'pageSizeLimit' => false
            ],
        ]);
    }

    public function search($params,$key='id')
    {
        $this->load($params);
        return $this->setArrayDataProvider($this->getArrayData($params), $key);
    }

    public function getArrayData($params)
    {
        $body = ['page' => $this->page, 'pageSize' => $this->pageSize];

        $searchParams = (!empty($params[$this->formName()]) ? $params[$this->formName()] : []);

        $this->load($params);

        $params = array_merge($body, $searchParams);

        $response = $this->request($params);
        $this->setPagination($response['totalCount']);

        return $response['results'];
    }

    public function renderPager()
    {
        $pages = new \yii\data\Pagination([
            'defaultPageSize' => $this->pageSize,
            'totalCount' => $this->totalCount,
            'pageSizeLimit' => false,
        ]);

        return \yii\widgets\LinkPager::widget([
            'firstPageLabel' => ' << ',
            'lastPageLabel' => ' >> ',
            'hideOnSinglePage' => false,
            'pagination' => $pages,
            'options' => [
                'class' => 'pagination'
            ],
            'pageCssClass' => 'paginate_button page-item',
            'firstPageCssClass' => 'paginate_button page-item previous',
            'prevPageCssClass' => 'paginate_button page-item previous',
            'lastPageCssClass' => 'paginate_button page-item previous',
            'nextPageCssClass' => 'paginate_button page-item previous',
        ]);
    }

    public function hiddenData()
    {
        $str = '';
        foreach ($this->searchField as $field) {
            $field_name = self::formName() . '[' . $field . ']';
            $str .= Html::input('text', $field_name, $this->$field, ['style' => 'display:none']);
        }
        return $str;
    }

    public static function renderCustomForm($fields)
    {
        $str = '';

        foreach ($fields as $field) {

            if (!isset($field['no_wrapper'])) {

                $option = [];
                if (empty($field['wrapper_option'])) {
                    $option['type'] = 'div';
                    $option['option'] = ['class' => 'form-group'];
                } else {
                    $option['type'] = $field['wrapper_option']['type'];
                    $option['option'] = $field['wrapper_option']['option'];
                }

                $str .= HTML::beginTag($option['type'], $option['option']) . HTML::label($field['label']);
            }

            switch ($field['type']) {
                case 'textarea':
                    $str .= HTML::textarea($field['name'],
                        (!empty($field['val']) ? $field['val'] : ''), $field['option']);
                    break;

                case 'text':
                    $str .= HTML::textInput($field['name'],
                        (!empty($field['val']) ? $field['val'] : ''), $field['option']);
                    break;

                case 'prepend_text':
                    $str .= '<div class="input-group"><div class="input-group-prepend"><div class="input-group-text">' . (!empty($field['prepend']) ? $field['prepend'] : '') . '</div></div>';
                    $str .= HTML::textInput($field['name'],
                        (!empty($field['val']) ? $field['val'] : ''), $field['option']);
                    $str .= '</div>';
                    break;

                case 'select':
                    $str .= HTML::dropDownList($field['name'],
                        (!empty($field['val']) ? $field['val'] : ''), $field['items'],
                        $field['option']);
                    break;

                case 'link':
                    $str .= Html::a($field['name'], $field['url'], $field['option']);
                    break;

                case 'button':
                    $str .= Html::button($field['name'], $field['option']);
                    break;
            }
            if (!isset($field['no_wrapper'])) {
                $str .= HTML::endTag('div');
            }
        }

        return $str;
    }

    //TODO change to dynamic pager & support page params
    public function renderPageSizer()
    {
        $str = '<form method="get"><div class=" dataTables_wrapper m_datatable m-datatable m-datatable--default m-datatable--loaded m-datatable--scroll"><div class="m-datatable__pager m-datatable--paging-loaded clearfix">';

        $str .= '<div class="dataTables_paginate paging_simple_numbers m-datatable__pager-nav">'.$this->renderPager().'</div>';

        $str .= '<div class="m-datatable__pager-info">';

        $str .= Html::dropDownList('pageSize', $this->pageSize, [
            '20' => '20',
            '50' => '50',
            '100' => '100',
            '200' => '200',
        ], ['onchange' => 'this.form.submit()', 'class' => 'selectpicker m-datatable__pager-size paging-width']
        );

        $str .= $this->hiddenData();

        $str .= '</div></div></div></form>';

        return $str;
    }
}