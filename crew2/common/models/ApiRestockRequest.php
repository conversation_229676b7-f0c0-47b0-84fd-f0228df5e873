<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "api_restock_request".
 *
 * @property int $api_restock_request_id
 * @property int|null $orders_products_id
 * @property int $orders_id
 * @property int|null $custom_products_code_id
 * @property int|null $products_id
 * @property string|null $publisher_order_id
 * @property int|null $status
 * @property int $is_locked
 * @property int|null $created_at
 * @property int|null $updated_at
 *
 * @property Products $products
 */
class ApiRestockRequest extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_restock_request';
    }

    
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id', 'orders_id', 'custom_products_code_id', 'products_id', 'status', 'is_locked'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['publisher_order_id'], 'string', 'max' => 255],
            [['products_id'], 'exist', 'skipOnError' => true, 'targetClass' => Products::className(), 'targetAttribute' => ['products_id' => 'products_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_restock_request_id' => 'Api Restock Request ID',
            'orders_products_id' => 'Orders Products ID',
            'orders_id' => 'Orders ID',
            'custom_products_code_id' => 'Custom Products Code ID',
            'products_id' => 'Products ID',
            'publisher_order_id' => 'Publisher Order ID',
            'status' => 'Status',
            'is_locked' => 'Is Locked',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Products]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProducts()
    {
        return $this->hasOne(Products::className(), ['products_id' => 'products_id']);
    }

    public function getProductsDescription()
    {
        return $this->hasMany(ProductsDescription::class, ['products_id' => 'products_id'])->andOnCondition(['language_id' => '1']);
    }
}
