<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "brand".
 *
 * @property int $brand_id
 * @property string $name
 */
class BrandOptions extends ApiFormModel
{
    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'brand';
        $this->method = 'get';
        $this->searchField = ['name', 'status'];
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'brand_id' => 'Brand ID',
            'name' => 'Brand Name'
        ];
    }

    public function getBrandList()
    {
        $this->action = 'list';

        return Yii::$app->cache->getOrSet('brand/list', function () {
            $list = ArrayHelper::map(
                $this->request(),
                'brand_id',
                'name');
            asort($list);
            return $list;
        }, 3600);
    }

}
