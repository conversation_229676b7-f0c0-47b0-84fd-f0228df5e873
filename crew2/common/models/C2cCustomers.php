<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "c2c_customers".
 *
 * @property string $customers_id
 * @property string $username
 * @property string $seller_status '1'=active, '0'=inactive
 * @property string $verified_seller 1 = seller passed the verification during Sign Up as Seller
 * @property string $seller_group_id
 * @property string $seller_create_listing_permission '1'=enable, '0'=disable
 * @property int $seller_product_listing_limit '-1'=unlimited
 * @property string $newsletter
 * @property string $online_datetime
 * @property string $created_date
 * @property string $verified_date
 */
class C2cCustomers extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'c2c_customers';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'username', 'created_date'], 'required'],
            [['customers_id', 'seller_status', 'seller_group_id', 'seller_create_listing_permission', 'seller_product_listing_limit'], 'integer'],
            [['verified_seller', 'newsletter'], 'string'],
            [['online_datetime', 'created_date', 'verified_date'], 'safe'],
            [['username'], 'string', 'max' => 12],
            [['username'], 'unique'],
            [['customers_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'username' => 'Username',
            'seller_status' => 'Seller Status',
            'verified_seller' => 'Verified Seller',
            'seller_group_id' => 'Seller Group ID',
            'seller_create_listing_permission' => 'Seller Create Listing Permission',
            'seller_product_listing_limit' => 'Seller Product Listing Limit',
            'newsletter' => 'Newsletter',
            'online_datetime' => 'Online Datetime',
            'created_date' => 'Created Date',
            'verified_date' => 'Verified Date',
        ];
    }
}
