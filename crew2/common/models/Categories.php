<?php

namespace common\models;

use offgamers\base\models\ms\Product;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "categories".
 *
 * @property int $categories_id
 * @property int $parent_id
 * @property string $categories_parent_path
 * @property int $sort_order
 * @property string $date_added
 * @property string $last_modified
 * @property int $categories_pinned
 * @property int $categories_status
 * @property int $c2c_categories_status
 * @property string $categories_url_alias
 * @property int $categories_auto_seo
 * @property string $categories_auto_seo_type
 * @property string $products_count
 * @property int $custom_products_type_id
 * @property int $custom_products_type_child_id
 * @property int $categories_types_groups_id
 * @property int $categories_buyback_main_cat
 */
class Categories extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'parent_id',
                    'sort_order',
                    'categories_pinned',
                    'categories_status',
                    'c2c_categories_status',
                    'categories_auto_seo',
                    'products_count',
                    'custom_products_type_id',
                    'custom_products_type_child_id',
                    'categories_types_groups_id',
                    'categories_buyback_main_cat'
                ],
                'integer'
            ],
            [['date_added', 'last_modified'], 'safe'],
            [['categories_parent_path', 'categories_url_alias'], 'string', 'max' => 255],
            [['categories_auto_seo_type'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories ID',
            'parent_id' => 'Parent ID',
            'categories_parent_path' => 'Categories Parent Path',
            'sort_order' => 'Sort Order',
            'date_added' => 'Date Added',
            'last_modified' => 'Last Modified',
            'categories_pinned' => 'Categories Pinned',
            'categories_status' => 'Categories Status',
            'c2c_categories_status' => 'C2c Categories Status',
            'categories_url_alias' => 'Categories Url Alias',
            'categories_auto_seo' => 'Categories Auto Seo',
            'categories_auto_seo_type' => 'Categories Auto Seo Type',
            'products_count' => 'Products Count',
            'custom_products_type_id' => 'Custom Products Type ID',
            'custom_products_type_child_id' => 'Custom Products Type Child ID',
            'categories_types_groups_id' => 'Categories Types Groups ID',
            'categories_buyback_main_cat' => 'Categories Buyback Main Cat',
        ];
    }

    public function getCategoriesDescriptions()
    {
        return $this->hasMany(CategoriesDescription::class, ['categories_id' => 'categories_id']);
    }

    public static function getAllSubCategoryByCategoryId($category_list)
    {
        $output = self::getThirdLayerCategory($category_list);
        if ($output) {
            $fourth_layer = self::getFourthLayerCategory($output);
            if ($fourth_layer) {
                $output = array_merge($output, $fourth_layer);
            }
        }
        return $output;
    }

    public static function getCategoryPathByCategoryId($category_list)
    {
        $output = [];

        $path_list = self::find()
            ->select(['categories_id', 'categories_parent_path'])
            ->where(['categories_id' => $category_list])
            ->asArray()->all();

        foreach ($path_list as $item) {
            $output[] = $item['categories_parent_path'] . $item['categories_id'] . '_';
        }

        return $output;
    }

    public static function getThirdLayerCategory($category_list)
    {
        $path_list = self::getCategoryPathByCategoryId($category_list);

        $data = self::find()
            ->select('categories_id')
            ->where(['custom_products_type_id' => 2])
            ->andWhere(['categories_status' => 1])
            ->andWhere(['REGEXP', 'categories_parent_path', implode('|', $path_list)])
            ->asArray()->all();

        return ArrayHelper::getColumn($data, 'categories_id');
    }

    public static function getFourthLayerCategory($category_list)
    {
        $path_list = self::getCategoryPathByCategoryId($category_list);

        $data = self::find()
            ->select('categories_id')
            ->where(['categories_status' => 1])
            ->andWhere(['REGEXP', 'categories_parent_path', implode('|', $path_list)])
            ->asArray()->all();

        return ArrayHelper::getColumn($data, 'categories_id');
    }

    public static function getMetaKeyword($category_id, $language_id = false, $default = false)
    {
        $output = SearchKeywords::getMetaKeyword($category_id, 0, $language_id, $default);
        return $output;
    }

    public static function getMetaDescription($category_id, $language_id = false, $default = false)
    {
        $query = CategoriesDescription::find()
            ->where(['categories_description', 'language_id'])
            ->where(['categories_id' => $category_id])
            ->asArray();

        if ($language_id !== false) {
            $query->andWhere(['language_id' => $language_id]);
        }

        $output = $query->all();

        if ($default && (empty($output) || empty($output[0]['search_value'])) && $language_id != 1) {
            $output = self::getMetaDescription($category_id, 1, $default);
        }

        return $output;
    }

    public static function getSecondLayerCategory()
    {
        return Yii::$app->cache->getOrSet('category/layer2', function () {
            $output = (new Query)
                ->select('c.categories_id')
                ->from('category c')
                ->all(Yii::$app->db_og);

            $output = ArrayHelper::getColumn($output, 'categories_id');

            $categories = self::find()->alias('c')
                ->select(['c.categories_id', 'cd.categories_name'])
                ->joinWith('categoriesDescriptions cd', false)
                ->where(['cd.language_id' => 1])
                ->andWhere(['c.categories_id' => $output])
                ->orderBy('c.sort_order, cd.categories_name')
                ->asArray()->all();

            $list = \yii\helpers\ArrayHelper::map(
                $categories,
                'categories_id',
                'categories_name'
            );

            asort($list);
            return $list;
        }, 86400);
    }

    public static function getSecondLayerCategoryList()
    {
        $output = (new Query)
            ->select('c.categories_id')
            ->from('category c')
            ->all(Yii::$app->db_og);

        $output = ArrayHelper::getColumn($output, 'categories_id');

        $categories = self::find()->alias('c')
            ->select(['c.categories_id', 'cd.categories_name', 'c.categories_status'])
            ->joinWith('categoriesDescriptions cd', false)
            ->where(['cd.language_id' => 1])
            ->andWhere(['c.categories_id' => $output])
            ->orderBy('c.sort_order, cd.categories_name')
            ->asArray()->all();

        $updatedCategories = [];
        foreach ($categories as $category) {
            $updatedCategories[$category['categories_id']] = $category;
            // 2 for category, id for type
            $updatedCategories[$category['categories_id']]['type'] = 2;
            $updatedCategories[$category['categories_id']]['status'] = $category['categories_status'];
            unset($updatedCategories[$category['categories_id']]['categories_status']);
        }

        uasort($updatedCategories, function ($a, $b) {
            $retval = $a['categories_name'] <=> $b['categories_name'];
            return $retval;
        });

        return $updatedCategories;
    }
    
}
