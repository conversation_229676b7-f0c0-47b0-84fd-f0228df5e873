<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 *
 * @property int $cdrules_id
 * @property string $cdrules_title
 */
class CategoriesDiscountRules extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_discount_rules';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function getCategoriesDiscountRules(): array
    {
        return Yii::$app->cache->getOrSet('categories-discount-rules/rules-id', function () {
            return ArrayHelper::map(self::find()
                ->select(['cdrules_id', 'cdrules_title'])
                ->orderBy(['cdrules_title' => SORT_ASC])
                ->asArray()->all(),
                'cdrules_id', 'cdrules_title');
        }, 86400);
    }

    public static function getCategoriesDiscountRulesDetail($rules_id): array
    {
        return (new Query())->select(['c.customers_groups_name', 'cd.cdgr_discount', 'cd.cdgr_wor'])
            ->from('categories_discount_group_rules cd')
            ->innerJoin('customers_groups c', 'c.customers_groups_id = cd.cdgr_customer_group_id')
            ->where(['cd.cdrules_id' => $rules_id])
            ->orderBy('c.sort_order')
            ->all(Yii::$app->get('db_offgamers'));
    }

}
