<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "categories_structures".
 *
 * @property string $categories_structures_key
 * @property string $categories_structures_value
 */
class CategoriesStructures extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_structures';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_structures_key'], 'required'],
            [['categories_structures_value'], 'string'],
            [['categories_structures_key'], 'string', 'max' => 64],
            [['categories_structures_key'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_structures_key' => 'Categories Structures Key',
            'categories_structures_value' => 'Categories Structures Value',
        ];
    }
}
