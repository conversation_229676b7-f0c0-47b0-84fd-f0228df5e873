<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "categories_tagmap".
 *
 * @property string $id
 * @property string $game_id
 * @property string $tag_id
 */
class CategoriesTagmap extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_tagmap';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_id', 'tag_id'], 'required'],
            [['game_id', 'tag_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_id' => 'Game ID',
            'tag_id' => 'Tag ID',
        ];
    }

}
