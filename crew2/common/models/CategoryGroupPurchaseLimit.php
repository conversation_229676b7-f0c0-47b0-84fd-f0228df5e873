<?php

namespace common\models;

use Yii;
use yii\base\Model;
use yii\helpers\ArrayHelper;

class CategoryGroupPurchaseLimit extends Model
{
    public $categories_id;
    public $customers_group_id;
    public $amount;
    public $x_minute;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_id', 'customers_group_id','x_minute'], 'integer'],
            [['amount'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories ID',
            'customers_group_id' => 'Customer Group ID',
            'amount' => 'Amount',
            'x_minute' => 'X Minute'
        ];
    }

}
