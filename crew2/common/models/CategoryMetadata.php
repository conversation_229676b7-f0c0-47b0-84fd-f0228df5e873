<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "category_metadata".
 *
 * @property int $category_metadata_id
 * @property int $category_id
 * @property int $language_id
 * @property string $title
 * @property string $keyword
 * @property string $description
 *
 */

class CategoryMetadata extends \yii\base\Model
{
    public $category_metadata_id;
    public $category_id;
    public $language_id;
    public $title;
    public $description;
    public $keyword;
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_metadata_id', 'category_id', 'language_id'], 'integer'],
            [['keyword', 'description'], 'string'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_metadata_id' => 'Category Metadata ID',
            'category_id' => 'Category ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'keyword' => 'Keyword',
            'description' => 'Description',
        ];
    }

}
