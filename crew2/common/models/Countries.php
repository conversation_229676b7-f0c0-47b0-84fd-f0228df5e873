<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "countries".
 *
 * @property int $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property int $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property int $address_format_id
 * @property int $maxmind_support
 * @property string $aft_risk_type
 * @property int $countries_display
 * @property int $telesign_support
 */
class Countries extends \common\models\OrdersTaxConfiguration
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'countries';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['countries_currencies_id', 'address_format_id', 'maxmind_support', 'countries_display', 'telesign_support'], 'integer'],
            [['countries_name', 'countries_website_domain'], 'string', 'max' => 64],
            [['countries_iso_code_2'], 'string', 'max' => 2],
            [['countries_iso_code_3'], 'string', 'max' => 3],
            [['countries_international_dialing_code'], 'string', 'max' => 5],
            [['aft_risk_type'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'countries_id' => 'Countries ID',
            'countries_name' => 'Countries Name',
            'countries_iso_code_2' => 'Countries Iso Code 2',
            'countries_iso_code_3' => 'Countries Iso Code 3',
            'countries_currencies_id' => 'Countries Currencies ID',
            'countries_international_dialing_code' => 'Countries International Dialing Code',
            'countries_website_domain' => 'Countries Website Domain',
            'address_format_id' => 'Address Format ID',
            'maxmind_support' => 'Maxmind Support',
            'aft_risk_type' => 'Aft Risk Type',
            'countries_display' => 'Countries Display',
            'telesign_support' => 'Telesign Support',
        ];
    }

    public static function getInternationalDialingCodeById($countries_id)
    {
        return Yii::$app->cache->getOrSet('countries_international_dialing_code/countries_id/' . $countries_id, function () use ($countries_id)
                {
                    $result = self::find()->select('countries_international_dialing_code')->where(['countries_id' => $countries_id])->asArray()->one();
                    return ($result ? $result['countries_international_dialing_code'] : '');
                });
    }

    public static function countriesNameID()
    {
        return Yii::$app->cache->getOrSet('countries/name_id', function () {
            return \yii\helpers\ArrayHelper::map(self::find()->select('countries_id, countries_name')->asArray()->all(), 'countries_id', 'countries_name');
        }, 86400);
    }

    public static function countriesCodeID()
    {
        return Yii::$app->cache->getOrSet('countries/code_id', function () {
            return \yii\helpers\ArrayHelper::map(self::find()->select('countries_id, countries_iso_code_2')->asArray()->all(), 'countries_id', 'countries_iso_code_2');
        }, 86400);
    }

    public static function countriesCodeName()
    {
        return Yii::$app->cache->getOrSet('countries/code_name', function () {
            return \yii\helpers\ArrayHelper::map(self::find()->select('countries_iso_code_2, countries_name')->asArray()->all(), 'countries_iso_code_2', 'countries_name');
        }, 86400);
    }

}
