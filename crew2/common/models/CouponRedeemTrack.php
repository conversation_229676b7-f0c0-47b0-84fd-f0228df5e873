<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "coupon_redeem_track".
 *
 * @property int $unique_id
 * @property int $coupon_id
 * @property int $customer_id
 * @property string $redeem_date
 * @property string $redeem_ip
 * @property int $order_id
 */
class CouponRedeemTrack extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupon_redeem_track';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_id', 'customer_id', 'order_id'], 'integer'],
            [['redeem_date'], 'safe'],
            [['redeem_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'unique_id' => 'Unique ID',
            'coupon_id' => 'Coupon ID',
            'customer_id' => 'Customer ID',
            'redeem_date' => 'Redeem Date',
            'redeem_ip' => 'Redeem Ip',
            'order_id' => 'Order ID',
        ];
    }

    public static function getRedeemCount($coupon_id)
    {
        return self::find()->where(['coupon_id'=>$coupon_id])->count();
    }

    public function getCouponCode()
    {
        return $this->hasMany(Coupons::class, ['coupon_id' => 'coupon_id']);
    }
}
