<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "coupons_generation".
 *
 * @property int $coupon_generation_id
 * @property string $coupon_type
 * @property string $fixed_code
 * @property string $coupon_code_prefix
 * @property string $coupon_code_suffix
 * @property string $coupon_amount
 * @property string $coupon_minimum_order
 * @property string $max_cap
 * @property string $coupon_start_date
 * @property string $coupon_expire_date
 * @property int $coupon_number
 * @property int $uses_per_coupon
 * @property string $uses_per_coupon_unlimited
 * @property int $uses_per_user
 * @property string $uses_per_user_unlimited
 * @property string $restrict_to_products
 * @property string $restrict_to_categories
 * @property string $restrict_to_customers
 * @property string $restrict_to_customers_groups
 * @property string $restrict_to_currency_id
 * @property string $restrict_to_payment_id
 * @property string $coupon_generation_status
 * @property int $requester_id
 * @property int $mobile_only
 * @property string $created_by
 * @property string $date_created
 * @property string $date_modified
 */
class CouponsGeneration extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupons_generation';
    }


    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'date_created',
                'updatedAtAttribute' => 'date_modified',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_amount', 'coupon_minimum_order', 'max_cap'], 'number'],
            [['coupon_start_date', 'coupon_expire_date', 'date_created', 'date_modified'], 'safe'],
            [['coupon_number', 'uses_per_coupon', 'uses_per_user', 'requester_id'], 'integer'],
            [['fixed_code', 'restrict_to_products', 'restrict_to_categories', 'restrict_to_customers', 'restrict_to_currency_id', 'restrict_to_payment_id', 'created_by'], 'string'],
            [['coupon_type', 'uses_per_coupon_unlimited', 'uses_per_user_unlimited', 'coupon_generation_status'], 'string', 'max' => 1],
            [['coupon_code_prefix', 'coupon_code_suffix'], 'string', 'max' => 5],
            [['restrict_to_customers_groups'], 'string', 'max' => 64],
            [['mobile_only'], 'integer', 'min' => 0, 'max' => 1],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'coupon_generation_id' => 'Coupon Generation ID',
            'coupon_type' => 'Coupon Type',
            'Fixed Code' => 'Fixed Code',
            'coupon_code_prefix' => 'Coupon Code Prefix',
            'coupon_code_suffix' => 'Coupon Code Suffix',
            'coupon_amount' => 'Coupon Amount',
            'coupon_minimum_order' => 'Coupon Minimum Order',
            'max_cap' => 'Max Cap',
            'coupon_start_date' => 'Coupon Start Date',
            'coupon_expire_date' => 'Coupon Expire Date',
            'coupon_number' => 'Coupon Number',
            'uses_per_coupon' => 'Uses Per Coupon',
            'uses_per_coupon_unlimited' => 'Uses Per Coupon Unlimited',
            'uses_per_user' => 'Uses Per User',
            'uses_per_user_unlimited' => 'Uses Per User Unlimited',
            'restrict_to_products' => 'Restrict To Products',
            'restrict_to_categories' => 'Restrict To Categories',
            'restrict_to_customers' => 'Restrict To Customers',
            'restrict_to_customers_groups' => 'Restrict To Customers Groups',
            'restrict_to_currency_id' => 'Restrict To Currency ID',
            'restrict_to_payment_id' => 'Restrict To Payment ID',
            'coupon_generation_status' => 'Coupon Generation Status',
            'requester_id' => 'Requester ID',
            'mobile_only' => 'Mobile Only',
            'created_by' => 'Creator',
            'date_created' => 'Date Created',
            'date_modified' => 'Date Modified',
        ];
    }

    public static function getCouponGenerationStatusList()
    {
        return [
            'P' => 'Pending',
            'N' => 'Cancelled',
            'Y' => 'Approved',
        ];
    }

    public static function getCouponGenerationStatus($status)
    {
        $list = self::getCouponGenerationStatusList();
//        return $list[$status] ?? '';
        return $list[$status] ? $list[$status] : '';
    }

    private function generateDiscountCode($number_salt)
    {
        $count = 0;
        while (true) {
            $code = $this->generateHash($number_salt);
            if ($this->checkIsDuplicate($code) === false) {
                return $code;
            }
            $count++;

            if ($count > 20) {
                return false;
            }
        }
    }

    public static function checkIsDuplicate($code)
    {
        return Coupons::find()->where(['coupon_code' => $code])->exists();
    }

    public static function checkDuplicateGeneration($code)
    {
        return CouponsGeneration::find()->where(['fixed_code' => $code, 'coupon_generation_status' => ['']])->exists();
    }


    private function generateHash($number_salt)
    {
        $length = 20;

        srand((double)microtime() * 1000000);
        $code1 = @rand(100, 999);
        $code2 = @rand(100, 999);
        $code3 = @rand(100, 999);

        $date_key = date('YmdHis');
        $number_key = str_pad($number_salt, 5, '0', STR_PAD_LEFT);

        $master_key = md5($code1 . $number_key . $code2 . $date_key . $code3);
        $random_pos = @rand(0, 32 - (int)$length);
        $random_key = substr($master_key, $random_pos, $length);

        return $this->coupon_code_prefix . $random_key . $this->coupon_code_suffix;
    }

    public function getTitle()
    {
        $id = $this->coupon_generation_id;
        $key = self::tableName() . '/' . 'title' . '/' . $id;
        $data = Yii::$app->cache->getOrSet($key, function () use ($id) {
            $model = CouponsGenerationDescription::find()->select('coupon_generation_name')->where(['coupon_generation_id' => $id, 'language_id' => 1])->one();
            return (!empty($model) ? $model->coupon_generation_name : '');
        });
        return $data;
    }

    public function generateCoupon()
    {
        set_time_limit(0);
        $title = $this->getTitle();

        $coupon_count = $this->getGeneratedCouponCount();
        for ($i = 0; $this->coupon_number - $coupon_count > $i; $i++) {
            $coupon = new Coupons();
            $coupon->load([
                'coupon_generation_id' => $this->coupon_generation_id,
                'coupon_type' => $this->coupon_type,
                'coupon_amount' => $this->coupon_amount,
                'coupon_minimum_order' => $this->coupon_minimum_order,
                'max_cap' => $this->max_cap,
                'coupon_start_date' => $this->coupon_start_date,
                'coupon_expire_date' => $this->coupon_expire_date,
                'uses_per_coupon' => $this->uses_per_coupon,
                'uses_per_coupon_unlimited' => $this->uses_per_coupon_unlimited,
                'uses_per_user' => $this->uses_per_user,
                'uses_per_user_unlimited' => $this->uses_per_user_unlimited,
                'restrict_to_products' => $this->restrict_to_products,
                'restrict_to_categories' => $this->restrict_to_categories,
                'restrict_to_customers' => $this->restrict_to_customers,
                'restrict_to_customers_groups' => $this->restrict_to_customers_groups,
                'restrict_to_currency_id' => $this->restrict_to_currency_id,
                'restrict_to_payment_id' => $this->restrict_to_payment_id,
                'mobile_only' => $this->mobile_only,
                'coupon_active' => 'Y',
            ], '');

            $coupon->coupon_code = strtoupper(!empty($this->fixed_code) ? $this->fixed_code : $this->generateDiscountCode($i));
            $coupon->save();

            if ($coupon->coupon_id) {
                $description = new CouponsDescription();
                $description->load([
                    'coupon_generation_id' => $coupon->coupon_id,
                    'language_id' => 1,
                    'coupon_name' => $title,
                ], '');
                $description->save();
            }
        }

        if ($this->getGeneratedCouponCount() > $this->coupon_number) {
            //TODO Slack Notification
        }

        $this->coupon_generation_status = 'Y';
        $this->save();
        $this->createLog('Approve discount code generation.', 'Y');
    }

    public function cancelGenerationRequest()
    {
        $this->coupon_generation_status = 'N';
        $this->save();
        $this->createLog('Cancel discount code generation.', 'N');
    }

    public function getGeneratedCouponCount()
    {
        return Coupons::find()->where(['coupon_generation_id' => $this->coupon_generation_id])->count();
    }


    public function getCouponsGenerationDescription()
    {
        return $this->hasOne(CouponsGenerationDescription::class, ['coupon_generation_id' => 'coupon_generation_id'])->andWhere([CouponsGenerationDescription::tableName() . '.language_id' => 1]);
    }

    public function afterSave($insert, $changedAttribute)
    {
        if ($insert) {
            $this->createLog('Create discount code generation.', 'P');
        }
        return parent::afterSave($insert, $changedAttribute);
    }

    public function createLog($comments, $status = '')
    {
        $log = new CouponsGenerationStatusHistory;
        $log->load([
            'coupons_generation_id' => $this->coupon_generation_id,
            'coupons_generation_status' => $status,
            'comments' => $comments,
        ], '');
        $log->save();
    }

}
