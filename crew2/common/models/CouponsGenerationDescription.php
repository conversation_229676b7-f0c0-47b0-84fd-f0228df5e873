<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "coupons_generation_description".
 *
 * @property int $coupon_generation_id
 * @property int $language_id
 * @property string $coupon_generation_name
 * @property string $coupon_generation_description
 */
class CouponsGenerationDescription extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupons_generation_description';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_generation_id', 'language_id'], 'required'],
            [['coupon_generation_id', 'language_id'], 'integer'],
            [['coupon_generation_description'], 'string'],
            [['coupon_generation_name'], 'string', 'max' => 128],
            [['coupon_generation_id', 'language_id'], 'unique', 'targetAttribute' => ['coupon_generation_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'coupon_generation_id' => 'Coupon Generation ID',
            'language_id' => 'Language ID',
            'coupon_generation_name' => 'Coupon Generation Name',
            'coupon_generation_description' => 'Coupon Generation Description',
        ];
    }
}
