<?php

namespace common\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * CouponsGenerationSearch represents the model behind the search form of `common\models\CouponsGeneration`.
 */
class CouponsGenerationSearch extends CouponsGeneration
{
    public $title;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_generation_id', 'coupon_number', 'uses_per_coupon', 'uses_per_user', 'requester_id'], 'integer'],
            [['title', 'coupon_type', 'fixed_code', 'coupon_code_prefix', 'coupon_code_suffix', 'coupon_start_date', 'coupon_expire_date', 'uses_per_coupon_unlimited', 'uses_per_user_unlimited', 'restrict_to_products', 'restrict_to_categories', 'restrict_to_customers', 'restrict_to_customers_groups', 'restrict_to_currency_id', 'restrict_to_payment_id', 'coupon_generation_status', 'date_created', 'date_modified', 'created_by'], 'safe'],
            [['coupon_amount', 'coupon_minimum_order', 'max_cap'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CouponsGeneration::find();

        // add conditions that should always apply here
        $query->joinWith(['couponsGenerationDescription']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['date_created' => SORT_DESC]],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere(
            ['LIKE', CouponsGenerationDescription::tableName() . '.coupon_generation_name', $this->title]
        );

        // grid filtering conditions
        $query->andFilterWhere([
            'coupon_generation_id' => $this->coupon_generation_id,
            'coupon_amount' => $this->coupon_amount,
            'coupon_minimum_order' => $this->coupon_minimum_order,
            'max_cap' => $this->max_cap,
            'coupon_start_date' => $this->coupon_start_date,
            'coupon_expire_date' => $this->coupon_expire_date,
            'coupon_number' => $this->coupon_number,
            'uses_per_coupon' => $this->uses_per_coupon,
            'uses_per_user' => $this->uses_per_user,
            'requester_id' => $this->requester_id,
            'date_created' => $this->date_created,
            'date_modified' => $this->date_modified,
            'coupon_generation_status' => $this->coupon_generation_status,
        ]);

        $query->andFilterWhere(['like', 'coupon_type', $this->coupon_type])
            ->andFilterWhere(['like', 'fixed_code', $this->fixed_code])
            ->andFilterWhere(['like', 'coupon_code_prefix', $this->coupon_code_prefix])
            ->andFilterWhere(['like', 'coupon_code_suffix', $this->coupon_code_suffix])
            ->andFilterWhere(['like', 'uses_per_coupon_unlimited', $this->uses_per_coupon_unlimited])
            ->andFilterWhere(['like', 'uses_per_user_unlimited', $this->uses_per_user_unlimited])
            ->andFilterWhere(['like', 'restrict_to_products', $this->restrict_to_products])
            ->andFilterWhere(['like', 'restrict_to_categories', $this->restrict_to_categories])
            ->andFilterWhere(['like', 'restrict_to_customers', $this->restrict_to_customers])
            ->andFilterWhere(['like', 'restrict_to_customers_groups', $this->restrict_to_customers_groups])
            ->andFilterWhere(['like', 'restrict_to_currency_id', $this->restrict_to_currency_id])
            ->andFilterWhere(['like', 'restrict_to_payment_id', $this->restrict_to_payment_id])
            ->andFilterWhere(['like', 'created_by', $this->created_by]);

        return $dataProvider;
    }

}
