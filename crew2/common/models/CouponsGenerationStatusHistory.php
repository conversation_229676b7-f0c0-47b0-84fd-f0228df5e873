<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "coupons_generation_status_history".
 *
 * @property int $coupons_generation_status_history_id
 * @property int $coupons_generation_id
 * @property string $coupons_generation_status
 * @property string $date_added
 * @property string $comments
 * @property string $changed_by
 */
class CouponsGenerationStatusHistory extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupons_generation_status_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupons_generation_id'], 'integer'],
            [['date_added'], 'safe'],
            [['comments'], 'string'],
            [['coupons_generation_status'], 'string', 'max' => 1],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'coupons_generation_status_history_id' => 'Coupons Generation Status History ID',
            'coupons_generation_id' => 'Coupons Generation ID',
            'coupons_generation_status' => 'Coupons Generation Status',
            'date_added' => 'Date Added',
            'comments' => 'Comments',
            'changed_by' => 'Changed By',
        ];
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->date_added = date('Y-m-d H:i:s');
            $this->changed_by = Yii::$app->user->identity->username;
        }
        return parent::beforeSave($insert);
    }
}
