<?php

namespace common\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Coupons;

/**
 * CouponsSearch represents the model behind the search form of `common\models\Coupons`.
 */
class CouponsSearch extends Coupons
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_id', 'coupon_generation_id', 'uses_per_coupon', 'uses_per_user'], 'integer'],
            [['coupon_type', 'coupon_code', 'coupon_start_date', 'coupon_expire_date', 'uses_per_coupon_unlimited', 'uses_per_user_unlimited', 'restrict_to_products', 'restrict_to_categories', 'restrict_to_customers', 'restrict_to_customers_groups', 'restrict_to_currency_id', 'restrict_to_payment_id', 'coupon_active', 'date_created', 'date_modified'], 'safe'],
            [['coupon_amount', 'coupon_minimum_order', 'max_cap'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Coupons::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'coupon_id' => $this->coupon_id,
            'coupon_generation_id' => $this->coupon_generation_id,
            'coupon_amount' => $this->coupon_amount,
            'coupon_minimum_order' => $this->coupon_minimum_order,
            'max_cap' => $this->max_cap,
            'coupon_start_date' => $this->coupon_start_date,
            'coupon_expire_date' => $this->coupon_expire_date,
            'uses_per_coupon' => $this->uses_per_coupon,
            'uses_per_user' => $this->uses_per_user,
            'date_created' => $this->date_created,
            'date_modified' => $this->date_modified,
        ]);

        $query->andFilterWhere(['like', 'coupon_type', $this->coupon_type])
            ->andFilterWhere(['like', 'coupon_code', $this->coupon_code])
            ->andFilterWhere(['like', 'uses_per_coupon_unlimited', $this->uses_per_coupon_unlimited])
            ->andFilterWhere(['like', 'uses_per_user_unlimited', $this->uses_per_user_unlimited])
            ->andFilterWhere(['like', 'restrict_to_products', $this->restrict_to_products])
            ->andFilterWhere(['like', 'restrict_to_categories', $this->restrict_to_categories])
            ->andFilterWhere(['like', 'restrict_to_customers', $this->restrict_to_customers])
            ->andFilterWhere(['like', 'restrict_to_customers_groups', $this->restrict_to_customers_groups])
            ->andFilterWhere(['like', 'restrict_to_currency_id', $this->restrict_to_currency_id])
            ->andFilterWhere(['like', 'restrict_to_payment_id', $this->restrict_to_payment_id])
            ->andFilterWhere(['like', 'coupon_active', $this->coupon_active]);

        return $dataProvider;
    }
}
