<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "coupons_status_history".
 *
 * @property int $coupons_status_history_id
 * @property int $coupon_id
 * @property string $coupon_active
 * @property string $date_added
 * @property string $comments
 * @property string $changed_by
 */
class CouponsStatusHistory extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupons_status_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_id'], 'integer'],
            [['date_added'], 'safe'],
            [['comments'], 'string'],
            [['coupon_active'], 'string', 'max' => 1],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'coupons_status_history_id' => 'Coupons Status History ID',
            'coupon_id' => 'Coupon ID',
            'coupon_active' => 'Coupon Active',
            'date_added' => 'Date Added',
            'comments' => 'Comments',
            'changed_by' => 'Changed By',
        ];
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->date_added = date('Y-m-d H:i:s');
            $this->changed_by = Yii::$app->user->identity->username;
        }
        return parent::beforeSave($insert);
    }
}
