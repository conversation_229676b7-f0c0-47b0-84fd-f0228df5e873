<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "cron_customer_account_closure".
 *
 * @property string $customers_id Customers ID
 * @property int $closure_status 0-pending, 1-completed, 2-processing
 * @property int $created_at
 * @property int $executed_at
 * @property int $updated_at
 * @property string $requested_by
 */
class CronCustomerAccountClosure extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cron_customer_account_closure';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'created_at', 'executed_at', 'updated_at'], 'required'],
            [['customers_id', 'closure_status', 'created_at', 'executed_at', 'updated_at'], 'integer'],
            [['requested_by'], 'string', 'max' => 255],
            [['customers_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'closure_status' => 'Closure Status',
            'created_at' => 'Created At',
            'executed_at' => 'Executed At',
            'updated_at' => 'Updated At',
            'requested_by' => 'Requested By',
        ];
    }
}
