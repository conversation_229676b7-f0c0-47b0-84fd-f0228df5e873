<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

class Currencies extends \offgamers\base\models\Currencies
{

    public static function getCurrenciesList()
    {
        $data = Yii::$app->cache->getOrSet('currencies/list', function () {
            $list = ArrayHelper::map(
                Currencies::find()->select(['currencies_id', 'code'])->asArray()->all(),
                'currencies_id',
                'code');
            return $list;
        }, 3600);
        return $data;
    }
}