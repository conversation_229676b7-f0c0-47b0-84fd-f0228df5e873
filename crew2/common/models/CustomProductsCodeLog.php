<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "custom_products_code_log".
 *
 * @property int $custom_products_code_log_id
 * @property string $custom_products_code_log_user
 * @property string $custom_products_code_log_user_role
 * @property string $log_ip
 * @property string $log_time
 * @property int $custom_products_code_id
 * @property string $log_system_messages
 * @property string $log_user_messages
 */
class CustomProductsCodeLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'custom_products_code_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['log_time'], 'safe'],
            [['custom_products_code_id'], 'integer'],
            [['log_system_messages', 'log_user_messages'], 'required'],
            [['log_system_messages', 'log_user_messages'], 'string'],
            [['custom_products_code_log_user'], 'string', 'max' => 255],
            [['custom_products_code_log_user_role'], 'string', 'max' => 16],
            [['log_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'custom_products_code_log_id' => 'Custom Products Code Log ID',
            'custom_products_code_log_user' => 'Custom Products Code Log User',
            'custom_products_code_log_user_role' => 'Custom Products Code Log User Role',
            'log_ip' => 'Log Ip',
            'log_time' => 'Log Time',
            'custom_products_code_id' => 'Custom Products Code ID',
            'log_system_messages' => 'Log System Messages',
            'log_user_messages' => 'Log User Messages',
        ];
    }
}
