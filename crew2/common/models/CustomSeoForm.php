<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

class CustomSeoForm extends ApiFormModel
{
    public $id, $type, $title;

    // function to validate the  XSS(cross-site-scripting ).
    public function beforeValidate()
    {
        foreach (array_keys($this->getAttributes()) as $attr) {
            if (!empty($this->$attr)) {
                // validate the html tags is attributes
                $this->$attr = \yii\helpers\HtmlPurifier::process($this->$attr);
            }
        }

        return parent::beforeValidate(); // to keep parent validator available 
    }


    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['type', 'title'], 'string'],
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'custom-seo';
        $this->method = 'get';
        $this->searchField = ['type', 'title'];
        parent::__construct();
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'index';
        return parent::search($params, $key);
    }

    public function getItemList($input)
    {
        $this->action = 'get-item-list';
        return $this->request($input);
    }

    public function getDefault($input)
    {
        $this->action = 'get-default-meta';

        return $this->request($input);
    }

    public function save($input)
    {
        $this->action = 'save';
        $input = [
            'id' => $this->id,
            'type' => $this->type,
            'translation' => $input['CustomSeoTranslation']
        ];

        // clear cahce key for frontend list
        $language = Yii::$app->enum->getLanguage('listData');
        foreach ($language as $i => $val) {
            Yii::$app->frontend_cache->delete('custom-seo/get-seo-content/' . strtolower(str_replace(" ", "_", $this->type)) . '/' . $this->id . '/' . $i);
        }

        return $this->request($input);
    }

    public function cloneData($input)
    {
        $this->action = 'clone';
        return $this->request($input);
    }

    public function generateDefaultEntry()
    {
        $this->action = 'generate-default-entry';
        return $this->request();
    }
}
