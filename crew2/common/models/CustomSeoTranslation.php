<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "custom_seo_translation".
 *
 * @property int $custom_seo_id
 * @property int $language_id
 * @property string $title
 * @property string $meta_title
 * @property string $meta_keyword
 * @property string $meta_description
 * @property int $status
 */

class CustomSeoTranslation extends \yii\base\model
{
    public $language_id;

    public $meta_title;

    public $meta_description;

    public $meta_keyword;

    public $status;

    public $title;

    public function rules()
    {
        return [
            [['custom_seo_id', 'language_id', 'status'], 'integer'],
            [['meta_keyword', 'meta_description'], 'string'],
            [['title','meta_title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'title' => 'Title',
            'custom_seo_id' => 'Custom Seo ID',
            'language_id' => 'Language ID',
            'meta_title' => 'Meta Title',
            'meta_keyword' => 'Meta Keyword',
            'meta_description' => 'Meta Description',
            'status' => 'Status',
        ];
    }

}
