<?php

namespace common\models;

use Yii;
use yii\db\conditions\OrCondition;

/**
 * This is the model class for table "customers".
 *
 * @property int $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_dob
 * @property int $account_activated
 * @property string $customers_email_address
 * @property int $email_verified
 * @property string $serial_number
 * @property int $customers_default_address_id
 * @property int $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string $customers_newsletter
 * @property string $customers_group_name
 * @property int $customers_group_id
 * @property string $customers_discount
 * @property int $customers_groups_id
 * @property string $customers_aft_groups_id
 * @property int $customers_status
 * @property string $customers_flag
 * @property int $customers_phone_verified
 * @property string $customers_phone_verified_by
 * @property string $customers_phone_verified_datetime
 * @property int $affiliate_ref_id
 * @property int $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property string $customers_reserve_amount
 * @property string $customers_security_start_time
 * @property int $customers_disable_withdrawal
 */
class Customers extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_dob', 'customers_phone_verified_datetime', 'customers_security_start_time'], 'safe'],
            [['account_activated', 'email_verified', 'customers_default_address_id', 'customers_country_dialing_code_id', 'customers_group_id', 'customers_groups_id', 'customers_aft_groups_id', 'customers_status', 'customers_phone_verified', 'affiliate_ref_id', 'ref_id', 'customers_disable_withdrawal'], 'integer'],
            [['customers_discount', 'customers_reserve_amount'], 'number'],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname', 'customers_telephone', 'customers_mobile', 'customers_fax', 'customers_login_sites'], 'string', 'max' => 32],
            [['customers_email_address', 'customers_msn', 'customers_yahoo'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_qq', 'customers_icq'], 'string', 'max' => 20],
            [['customers_password', 'customers_pin_number'], 'string', 'max' => 40],
            [['customers_newsletter', 'customers_phone_verified_by', 'customers_merged_profile'], 'string', 'max' => 255],
            [['customers_group_name'], 'string', 'max' => 27],
            [['customers_flag'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'customers_gender' => 'Customers Gender',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_dob' => 'Customers Dob',
            'account_activated' => 'Account Activated',
            'customers_email_address' => 'Customers Email Address',
            'email_verified' => 'Email Verified',
            'serial_number' => 'Serial Number',
            'customers_default_address_id' => 'Customers Default Address ID',
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code ID',
            'customers_telephone' => 'Customers Telephone',
            'customers_mobile' => 'Customers Mobile',
            'customers_fax' => 'Customers Fax',
            'customers_msn' => 'Customers Msn',
            'customers_qq' => 'Customers Qq',
            'customers_yahoo' => 'Customers Yahoo',
            'customers_icq' => 'Customers Icq',
            'customers_password' => 'Customers Password',
            'customers_pin_number' => 'Customers Pin Number',
            'customers_newsletter' => 'Customers Newsletter',
            'customers_group_name' => 'Customers Group Name',
            'customers_group_id' => 'Customers Group ID',
            'customers_discount' => 'Customers Discount',
            'customers_groups_id' => 'Customers Groups ID',
            'customers_aft_groups_id' => 'Customers Aft Groups ID',
            'customers_status' => 'Customers Status',
            'customers_flag' => 'Customers Flag',
            'customers_phone_verified' => 'Customers Phone Verified',
            'customers_phone_verified_by' => 'Customers Phone Verified By',
            'customers_phone_verified_datetime' => 'Customers Phone Verified Datetime',
            'affiliate_ref_id' => 'Affiliate Ref ID',
            'ref_id' => 'Ref ID',
            'customers_merged_profile' => 'Customers Merged Profile',
            'customers_login_sites' => 'Customers Login Sites',
            'customers_reserve_amount' => 'Customers Reserve Amount',
            'customers_security_start_time' => 'Customers Security Start Time',
            'customers_disable_withdrawal' => 'Customers Disable Withdrawal',
        ];
    }

    public function getCustomersInfo()
    {
        return $this->hasOne(CustomersInfo::className(), ['customers_info_id' => 'customers_id']);
    }

    public function getOrdersTaxCustomers()
    {
        return $this->hasOne(OrdersTaxCustomers::className(), ['customers_id' => 'customers_id']);
    }

    public static function getCustomerGroupLastChange($customers_id, $customers_group_id)
    {
        $customers_group_name = CustomersGroups::getCustomerGroupDescriptionById($customers_group_id);

        $alternate_group_name = ($customers_group_name == 'Platinum+' ? 'PLATINUM PLUS' : strtoupper($customers_group_name));

        $query = CustomersRemarksHistory::find()
            ->select(['date_remarks_added'])
            ->where(['customers_id' => $customers_id])
            ->andWhere(new OrCondition([
                ['remarks' => 'Upgraded to ' . $alternate_group_name . ' by system'],
                ['LIKE', 'remarks', '--> ' . $customers_group_name . ''],
            ]))
            ->orderBy(['customers_remarks_history_id' => SORT_DESC])
            ->one();

        return ($query ? strtotime($query->date_remarks_added) : 0);
    }

    public function getCustomerPhoneNumber(){
        return '+'. Countries::getInternationalDialingCodeById($this->customers_country_dialing_code_id) .' '. $this->customers_telephone;
    }

    public static function getCustomerGroupChangeMsg($from, $to)
    {
        return "<b>Customer Group Status</b>: " . CustomersGroups::getCustomerGroupDescriptionById($from) . ' --> ' . CustomersGroups::getCustomerGroupDescriptionById($to);
    }

    public static function getCustomerFlagChangeMsg($flag, $enable_flag)
    {
        $msg = ($enable_flag == true ? 'Off --> On' : 'On --> Off');

        switch ($flag) {
            // Flag
            case 1:
                $msg = '<span class="flagOrange">Flag:</span> ' . $msg;
                break;
            //NRP Flag
            case 2:
                $msg = '<span class="flagPink">Non-reversible Payment Only Flag:</span> ' . $msg . ($enable_flag ? ' (<span class="redIndicator">Accept irreversible payment only</span>)' : '');
                break;
        }

        return $msg;
    }

    public static function updateCustomerRemarkHistory($customer_id, $remark_text, $changed_by = 'system')
    {
        $remark_history = new CustomersRemarksHistory();

        $remark_history->load(
            [
                'customers_id' => $customer_id,
                'date_remarks_added' => date('Y-m-d H:i:s'),
                'remarks' => "Changes made:\n" . (is_array($remark_text) ? implode("\n", $remark_text) : $remark_text),
                'remarks_added_by' => $changed_by,
            ]
            , '');

        $remark_history->save();
    }

}
