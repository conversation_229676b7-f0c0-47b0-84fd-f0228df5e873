<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "customers_groups".
 *
 * @property int $customers_groups_id
 * @property string $customers_groups_name
 * @property string $customers_groups_legend_color
 * @property string $customers_groups_payment_methods
 * @property string $customers_groups_extra_sc In Percentage
 * @property int $sort_order
 */
class CustomersGroups extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_groups';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_groups_payment_methods'], 'required'],
            [['customers_groups_payment_methods'], 'string'],
            [['customers_groups_extra_sc'], 'number'],
            [['sort_order'], 'integer'],
            [['customers_groups_name'], 'string', 'max' => 32],
            [['customers_groups_legend_color'], 'string', 'max' => 7],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_groups_id' => 'Customers Groups ID',
            'customers_groups_name' => 'Customers Groups Name',
            'customers_groups_legend_color' => 'Customers Groups Legend Color',
            'customers_groups_payment_methods' => 'Customers Groups Payment Methods',
            'customers_groups_extra_sc' => 'Customers Groups Extra Sc',
            'sort_order' => 'Sort Order',
        ];
    }

    public static function getCustomerGroupDescriptionById($group_id)
    {
        return Yii::$app->cache->getOrSet('customer_group_name/customers_groups_id/' . $group_id, function () use ($group_id) {
            $result = self::find()->select('customers_groups_name')->where(['customers_groups_id' => $group_id])->asArray()->one();
            return ($result ? $result['customers_groups_name'] : '');
        });
    }

    public static function getGroupList()
    {
        $data = Yii::$app->cache->getOrSet('customers_group/list', function () {
            $list = ArrayHelper::map(
                CustomersGroups::find()->select(['customers_groups_id', 'customers_groups_name'])->asArray()->all(),
                'customers_groups_id',
                'customers_groups_name');
            return $list;
        }, 3600);
        return $data;
    }

}
