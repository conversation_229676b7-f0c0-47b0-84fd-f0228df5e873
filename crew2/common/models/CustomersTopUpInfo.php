<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "customers_top_up_info".
 *
 * @property string $top_up_info_id
 * @property int $orders_products_id
 * @property string $top_up_value
 */
class CustomersTopUpInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_top_up_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['top_up_info_id', 'orders_products_id'], 'required'],
            [['top_up_info_id', 'orders_products_id'], 'integer'],
            [['top_up_value'], 'string', 'max' => 255],
            [['top_up_info_id', 'orders_products_id'], 'unique', 'targetAttribute' => ['top_up_info_id', 'orders_products_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'top_up_info_id' => 'Top Up Info ID',
            'orders_products_id' => 'Orders Products ID',
            'top_up_value' => 'Top Up Value',
        ];
    }
}
