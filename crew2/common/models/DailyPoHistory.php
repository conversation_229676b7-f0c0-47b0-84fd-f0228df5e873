<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "daily_po_history".
 *
 * @property int $id
 * @property int $ref_id
 * @property int $user_id
 * @property int $status
 * @property int $status_date
 * @property string $sales
 * @property string $sync_amount
 * @property int $sync_date
 * @property int $sync_status 0 = pending to sync, 1 = sync completed
 * @property int $listing_id
 * @property string $data
 * @property int $year
 * @property int $month
 * @property int $day
 * @property int $created_at
 * @property int $updated_at
 */
class DailyPoHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'daily_po_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'ref_id', 'user_id', 'status', 'status_date', 'sales', 'sync_amount', 'listing_id', 'year', 'month', 'day', 'created_at', 'updated_at'], 'required'],
            [['id', 'ref_id', 'user_id', 'status', 'status_date', 'sync_date', 'sync_status', 'listing_id', 'year', 'month', 'day', 'created_at', 'updated_at'], 'integer'],
            [['sales', 'sync_amount'], 'number'],
            [['data'], 'string'],
            [['id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'ref_id' => 'Ref ID',
            'user_id' => 'User ID',
            'status' => 'Status',
            'status_date' => 'Status Date',
            'sales' => 'Sales',
            'sync_amount' => 'Sync Amount',
            'sync_date' => 'Sync Date',
            'sync_status' => 'Sync Status',
            'listing_id' => 'Listing ID',
            'data' => 'Data',
            'year' => 'Year',
            'month' => 'Month',
            'day' => 'Day',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
