<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "daily_so_po_tmp_history".
 *
 * @property int $id
 * @property int $ref_id
 * @property int $type 1 = PO, 2 = SO
 * @property int $status 3 = completed, 2 = non-complete
 * @property int $status_date
 * @property int $created_at
 */
class DailySoPoTmpHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'daily_so_po_tmp_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'ref_id', 'type', 'status', 'status_date', 'created_at'], 'required'],
            [['id', 'ref_id', 'type', 'status', 'status_date', 'created_at'], 'integer'],
            [['ref_id', 'type'], 'unique', 'targetAttribute' => ['ref_id', 'type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'ref_id' => 'Ref ID',
            'type' => 'Type',
            'status' => 'Status',
            'status_date' => 'Status Date',
            'created_at' => 'Created At',
        ];
    }
}
