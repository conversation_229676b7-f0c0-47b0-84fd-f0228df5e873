<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "deliver_queue".
 *
 * @property string $type
 * @property string $id
 * @property string $extra_info data store in JSON format
 * @property string $created_at
 */
class DeliverQueue extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'deliver_queue';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'id', 'created_at'], 'required'],
            [['id', 'created_at'], 'integer'],
            [['extra_info'], 'string'],
            [['type'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'type' => 'Type',
            'id' => 'ID',
            'extra_info' => 'Extra Info',
            'created_at' => 'Created At',
        ];
    }
}
