<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "g2g_products_mapping".
 *
 * @property int $g2g_products_mapping_id
 * @property string $g2g_offer_id
 * @property int $products_id
 */
class G2gProductsMapping extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'g2g_products_mapping';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['g2g_offer_id', 'products_id'], 'required'],
            [['g2g_offer_id'], 'unique'],
            [['products_id'], 'unique'],
            [['products_id'], 'integer'],
            [['g2g_offer_id'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'g2g_products_mapping_id' => 'G2g Products Mapping ID',
            'g2g_offer_id' => 'G2g Offer ID',
            'products_id' => 'Products ID',
        ];
    }
}
