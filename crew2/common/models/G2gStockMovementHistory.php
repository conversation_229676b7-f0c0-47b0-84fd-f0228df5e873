<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "g2g_stock_movement_history".
 *
 * @property int $g2g_stock_movement_history_id
 * @property string|null $g2g_order_id
 * @property int|null $custom_products_code_id
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class G2gStockMovementHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
        ];
    }
    public static function tableName()
    {
        return 'g2g_stock_movement_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['custom_products_code_id', 'status'], 'integer'],
            [['status'], 'required'],
            [['g2g_order_id'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'g2g_stock_movement_history_id' => 'G2g Stock Movement History ID',
            'g2g_order_id' => 'G2g Order ID',
            'custom_products_code_id' => 'Custom Products Code ID',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
