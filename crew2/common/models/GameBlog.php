<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "game_blog".
 *
 * @property string $game_blog_id
 * @property string $custom_url
 * @property int $sort_order
 */
class GameBlog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_blog';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['custom_url'], 'string'],
            [['sort_order'], 'required'],
            [['sort_order'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_blog_id' => 'Game Blog ID',
            'custom_url' => 'Custom Url',
            'sort_order' => 'Sort Order',
        ];
    }

    public function getGameBlogDescription()
    {
        return $this->hasMany(GameBlogDescription::class, ['game_blog_id' => 'game_blog_id']);
    }

    public function getDefaultGameBlogDescription()
    {
        return $this->getGameBlogDescription()->where(['language_id' => 1]);
    }

}
