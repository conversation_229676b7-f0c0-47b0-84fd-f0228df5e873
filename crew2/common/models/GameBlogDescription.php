<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "game_blog_description".
 *
 * @property string $game_blog_id
 * @property int $language_id
 * @property string $game_blog_description
 */
class GameBlogDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_blog_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_blog_id', 'language_id', 'game_blog_description'], 'required'],
            [['game_blog_id', 'language_id'], 'integer'],
            [['game_blog_description'], 'string', 'max' => 64],
            [['game_blog_id', 'language_id'], 'unique', 'targetAttribute' => ['game_blog_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_blog_id' => 'Game Blog ID',
            'language_id' => 'Language ID',
            'game_blog_description' => 'Game Blog Description',
        ];
    }

    public static function getAllGameBlog()
    {
        return self::find()->select(['gb.custom_url', 'gb.game_blog_id', 'game_blog_description'])
            ->alias('gbd')
            ->innerJoin('game_blog gb', 'gb.game_blog_id = gbd.game_blog_id')
            ->where(['language_id' => 1])
            ->asArray()
            ->all();
    }
}
