<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "game_genre_description".
 *
 * @property int $game_genre_id
 * @property int $language_id
 * @property string $game_genre_description
 */
class GameGenreDescription extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_genre_description';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function getGameGenreDescId(): array
    {
        return Yii::$app->cache->getOrSet('game-genre/desc-id', function () {
            return ArrayHelper::map(
                self::find()
                ->select(['game_genre_id', 'game_genre_description'])
                ->where(['language_id' => 1])
                ->orderBy(['game_genre_id' => SORT_ASC])
                ->asArray()->all(),
                'game_genre_id',
                'game_genre_description'
            );
        }, 86400);
    }
}
