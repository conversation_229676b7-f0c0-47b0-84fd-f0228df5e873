<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "game_language_description".
 *
 * @property int $game_language_id
 * @property int $language_id
 * @property string $game_language_description
 */
class GameLanguageDescription extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_language_description';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function getGameLanguageDescId(): array
    {
        return Yii::$app->cache->getOrSet('game-lang/desc-id', function () {
            return ArrayHelper::map(
                self::find()
                ->select(['game_language_id', 'game_language_description'])
                ->where(['language_id' => 1])
                ->orderBy(['game_language_id' => SORT_ASC])
                ->asArray()->all(),
                'game_language_id',
                'game_language_description'
            );
        }, 86400);
    }
}
