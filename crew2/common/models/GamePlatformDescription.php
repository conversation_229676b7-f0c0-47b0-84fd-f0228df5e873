<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "game_platform_description".
 *
 * @property int $game_platform_id
 * @property int $language_id
 * @property string $game_platform_description
 */
class GamePlatformDescription extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_platform_description';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function getGamePlatformDescId(): array
    {
        return Yii::$app->cache->getOrSet('game-platform/desc-id', function () {
            return ArrayHelper::map(
                self::find()
                ->select(['game_platform_id', 'game_platform_description'])
                ->where(['language_id' => 1])
                ->orderBy(['game_platform_id' => SORT_ASC])
                ->asArray()->all(),
                'game_platform_id',
                'game_platform_description'
            );
        }, 86400);
    }
}
