<?php

namespace common\models;

use Yii;

class GameProductAttributeForm extends ApiFormModel
{

    public $id, $description, $type;

    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['type', 'description'], 'string'],
            [['type'], 'required'],
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'game-product-attribute';
        $this->method = 'get';
        $this->searchField = ['type', 'description'];
        parent::__construct();
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'index';
        return parent::search($params, $key);
    }

    public function get($id)
    {
        $this->action = 'view';

        $data = $this->request($id);

        return ['data' => $data];
    }

    public function create($input)
    {
        $this->action = 'create';

        if ($this->validate()) {
            $data = $this->request([
                'type' => $input['type'],
                'sort_order' => $input['sort_order'],
                'description' => $input['description']
            ]);

            return $data['id'];
        } else {
            Yii::$app->session->setFlash(
                "error",
                array_values($this->getFirstErrors())[0]
            );
        }
    }

    public function update($id, $input)
    {
        $this->action = 'update';

        $data = $this->request([
            'id' => $id,
            'type' => $input['type'],
            'sort_order' => $input['sort_order'],
            'description' => $input['description']
        ]);

        return $data['id'];
    }

    public function nextSortOrder($type)
    {
        $this->action = 'next-sort-order';

        $data = $this->request(['type' => $type]);

        return $data;
    }


}