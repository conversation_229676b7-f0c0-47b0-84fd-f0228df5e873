<?php

namespace common\models;

use Yii;
use yii\base\InvalidArgumentException;

class GamePublisherProductForm extends ApiFormModel
{
    const STATUS = array(
        0 => 'Pending',
        1 => 'Active',
        3 => 'Inactive',
        4 => 'Modified',
        5 => 'Excluded',
        6 => 'Removed'
    );

    const MARK_UP = array(
        '<3%',
        '>=3% & < 9%',
        '>=9% & < 12 %',
        '>=12% & < 15%',
        '>=15%'
    );

    const PRODUCT_TYPE = array('2' => 'Game Product', '3' => 'Mobile Reload', '1' => 'CD Key');

    const PRODUCT_ATTRIBUTE_ID = array('language' => 1, 'genre' => 2, 'feature' => 3, 'platform' => 4, 'region' => 5);

    const FLAG = array(
        'normal' => 'NORMAL',
        'out_of_stock' => 'OUT OF STOCK',
        'pre_order' => 'PRE-ORDER',
        'is_duplicate' => 'IS DUPLICATE'
    );

    public $title, $status, $game_publisher_id, $flag, $actual_mark_up, $changed_by;

    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['title', 'status', 'flag', 'game_publisher_id', 'actual_mark_up'], 'string'],
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'game-publisher-product';
        $this->method = 'get';
        $this->searchField = array('title', 'status', 'flag', 'game_publisher_id', 'actual_mark_up');
        parent::__construct();
    }

    public function request($params = [], $decode = true)
    {
        if (!empty($this->changed_by)) {
            $params['changed_by'] = $this->changed_by;
        }
        return parent::request($params, $decode);
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'index';
        return parent::search($params, $key);
    }


    public function get($id)
    {
        $this->action = 'view';

        $data = $this->request($id);

        return ['data' => $data];
    }

    public function batchCreate($data, $changed_by = '')
    {
        $this->action = 'batch-create';
        $this->changed_by = 'CodesWholeSale';

        $this->request($data);
    }

    public function batchUpdateProduct($data)
    {
        $this->action = 'batch-update-product';
        $this->changed_by = Yii::$app->user->identity->username;

        if (!Yii::$app->user->can('[Game Product] Update Price')) {
            unset($data['mark_up']);
        } else {
            if (isset($data['mark_up']) && $data['status'] != '5') {
                $data['mark_up'] = (double)$data['mark_up'];
                if ($data['mark_up'] < 0) {
                    throw new InvalidArgumentException('Not Allowed');
                }
            }
        }

        if (!Yii::$app->user->can('[Game Product] Update Status')) {
            $status_list = self::getUpdateStatusListWithPermission(true);
            if (!empty($data['status']) && empty($status_list[$data['status']])) {
                throw new InvalidArgumentException('Not Allowed');
            }
        }

        $this->request($data);
    }

    public function disableProduct($data)
    {
        $this->action = 'disable-product';
        $this->request($data);
    }

    public function getGameProduct($id)
    {
        $this->action = 'view-game-product';
        $data = $this->request($id);
        return ['data' => $data];
    }

    public function checkUrlAlias($url_alias)
    {
        $this->action = 'check-duplicate-url-alias';
        return $this->request($url_alias);
    }

    public function updateTitleUrlAlias($data)
    {
        $this->action = 'update-title-url-alias';
        return $this->request($data);
    }

    public function save($data)
    {
        $this->action = 'update-game-product';
        $this->changed_by = Yii::$app->user->identity->username;
        if (!Yii::$app->user->can('[Game Product] Update Price')) {
            unset($data['price']);
        }

        if (!Yii::$app->user->can('[Game Product] Update Status')) {
            $status_list = self::getUpdateStatusListWithPermission(false);
            if (empty($status_list[$data['status']])) {
                unset($data['status']);
            }
        }
        return $this->request($data);
    }

    public static function getUpdateStatusListWithPermission($new_product = true, $append = '')
    {
        $return_array = array();
        if ($new_product) {
            $return_array[5] = 'Excluded';
        }

        if (Yii::$app->user->can('[Game Product] Update Status')) {
            $return_array[3] = 'Inactive';
            $return_array[1] = 'Active';
        }

        if (!empty($append)) {
            $return_array[$append] = self::STATUS[$append];
        }

        asort($return_array);
        return $return_array;
    }


    public function patchGameProductCategoryPath()
    {
        $this->action = 'patch-game-product-category-path';

        return $this->request();
    }

    public function patchReleaseDate()
    {
        $this->action = 'patch-release-date';

        return $this->request();
    }

    public function patchSellingPrice()
    {
        $this->action = 'patch-selling-price';

        return $this->request();
    }
}