<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "game_publisher_setting".
 *
 * @property int $id
 * @property int $game_publisher_id
 * @property string $key
 * @property string $value
 *
 * @property GamePublisher $gamePublisher
 */
class GamePublisherSetting extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_publisher_setting';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_publisher_id'], 'integer'],
            [['key', 'value'], 'string', 'max' => 255],
            [['game_publisher_id'], 'exist', 'skipOnError' => true, 'targetClass' => GamePublisher::className(), 'targetAttribute' => ['game_publisher_id' => 'game_publisher_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_publisher_id' => 'Game Publisher ID',
            'key' => 'Key',
            'value' => 'Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGamePublisher()
    {
        return $this->hasOne(GamePublisher::className(), ['game_publisher_id' => 'game_publisher_id']);
    }
}
