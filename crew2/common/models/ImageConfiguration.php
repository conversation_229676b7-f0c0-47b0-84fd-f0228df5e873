<?php

namespace common\models;

use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "image_configuration".
 *
 * @property int $image_configuration_id
 * @property string $image_category
 * @property string $file_path
 * @property string $web_path
 * @property string $aws_s3_info
 * @property string $user_groups
 */
class ImageConfiguration extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'image_configuration';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_groups'], 'required'],
            [['user_groups'], 'string'],
            [['image_category'], 'string', 'max' => 16],
            [['file_path', 'web_path', 'aws_s3_info'], 'string', 'max' => 80],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'image_configuration_id' => 'Image Configuration ID',
            'image_category' => 'Image Category',
            'file_path' => 'File Path',
            'web_path' => 'Web Path',
            'aws_s3_info' => 'Aws S3 Info',
            'user_groups' => 'User Groups',
        ];
    }

    public static function getDirectory(){
        $result = Yii::$app->db->cache(function ($db) {
            return self::find()->select(['image_category','aws_s3_info'])->asArray()->all();
        });

        foreach($result as &$image_conf){
            $data = Json::decode($image_conf['aws_s3_info']);
            $image_conf['bucket'] = $data['bucket'];
            $image_conf['path'] = $data['path'];
            unset($image_conf['aws_s3_info']);
        }

        return $result;
    }
}
