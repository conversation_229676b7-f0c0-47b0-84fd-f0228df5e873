<?php

namespace common\models;

use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "country_currency_restrictions".
 *
 * @property string $id
 * @property string $country_iso_code2
 * @property string $restriction_info
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 */
class LocationRestriction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

    public static function tableName()
    {
        return 'location_restriction';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],  
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => null,
                'updatedByAttribute' => 'changed_by',
                'value' => Yii::$app->user->identity->username
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['country_iso_code2'], 'required'],
            [['restriction_info'], 'string'],
            [['created_at', 'updated_at'], 'integer'],
            [['country_iso_code2'], 'string', 'max' => 2],
            [['changed_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'country_iso_code2' => 'Country Iso Code2',
            'restriction_info' => 'Restriction Info',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'changed_by' => 'Changed By',
        ];
    }

    public function getCountries()
    {
        return $this->hasOne(Countries::className(), ['countries_iso_code_2' => 'country_iso_code2']);
    }
}
