<?php

namespace common\models;

use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "country_currency_restrictions_history".
 *
 * @property string $id
 * @property string $location_restriction_id
 * @property string $country_iso_code2
 * @property string $restriction_history
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 */
class LocationRestrictionHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'location_restriction_history';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => null,
                'updatedByAttribute' => 'changed_by',
                'value' => Yii::$app->user->identity->username,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['location_restriction_id', 'country_iso_code2'], 'required'],
            [['location_restriction_id', 'created_at', 'updated_at'], 'integer'],
            [['restriction_history'], 'string'],
            [['country_iso_code2'], 'string', 'max' => 2],
            [['changed_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'location_restriction_id' => 'Location Restriction ID',
            'country_iso_code2' => 'Country Iso Code2',
            'restriction_history' => 'Restriction History',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'changed_by' => 'Changed By',
        ];
    }
}
