<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "log_ses_bounce_history".
 *
 * @property string $email
 * @property string $bounce_type
 * @property string $bounce_sub_type
 * @property string $error_string
 * @property string $created_at
 * @property string $updated_at
 */
class LogSesBounceHistory extends \yii\db\ActiveRecord
{
    public function behaviors()
    {
        return [
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value'              => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'log_ses_bounce_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['email'], 'required'],
            [['error_string'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['email', 'bounce_type', 'bounce_sub_type'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'email'           => 'Email',
            'bounce_type'     => 'Bounced Type',
            'bounce_sub_type' => 'Bounced Sub Type',
            'error_string'    => 'Error String',
            'created_at'      => 'Created At',
            'updated_at'      => 'Updated At',
        ];
    }

}
