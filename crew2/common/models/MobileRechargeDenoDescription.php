<?php

namespace common\models;

use Yii;
use yii\base\Model;

/**
 * This is the model class for table "mobile_recharge_deno_description".
 *
 * @property int $deno_description_id
 * @property int $deno_id
 * @property int $language_id
 * @property string $title
 * @property string $description
 *
 * @property MobileRechargeDeno $deno
 */
class MobileRechargeDenoDescription extends Model
{
    public $title;
    public $language_id;
    public $description;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['language_id'], 'integer'],
            [['description'], 'string'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'deno_description_id' => 'Deno Description ID',
            'deno_id' => 'Deno ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'description' => 'Description',
        ];
    }
}
