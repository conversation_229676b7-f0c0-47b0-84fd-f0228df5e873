<?php

namespace common\models;

use Yii;

class MobileRechargeDenoForm extends ApiFormModel
{
    public $deno_id;
    public $type;
    public $operator_id;
    public $publisher_id;
    public $cost_currency;
    public $cost_price;
    public $sales_currency;
    public $sales_price;
    public $status;
    public $publisher_ref_id;
    public $products_id;
    public $mark_up;
    public $operator;
    public $description;
    public $title;

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'mobile-recharge';
        $this->method = 'get';
        $this->searchField = ['title'];
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'operator_id', 'publisher_id', 'cost_currency', 'cost_price', 'sales_currency', 'sales_price', 'status', 'publisher_ref_id'], 'required'],
            [['type', 'operator_id', 'publisher_id', 'status', 'products_id', 'mark_up'], 'integer'],
            [['cost_price', 'sales_price'], 'number'],
            [['cost_currency', 'sales_currency'], 'string', 'max' => 3],
            [['publisher_ref_id'], 'string', 'max' => 255],
            [['title', 'operator', 'publisher', 'description'], 'safe'],
        ];
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'get-deno-list';
        return parent::search($params, $key);
    }

    public function get($id)
    {
        $this->action = 'get-deno';

        $data = $this->request(['id' => $id]);

        return ['data' => $data];
    }

    public function update($id = 0)
    {
        $this->action = 'update-deno';

        $data = [
            'id' => $id,
            'type' => $this->type,
            'publisher_id' => $this->publisher_id,
            'operator_id' => $this->operator_id,
            'status' => $this->status,
            'mark_up' => $this->mark_up,
            'cost_currency' => $this->cost_currency,
            'cost_price' => $this->cost_price,
            'sales_currency' => $this->sales_currency,
            'sales_price' => $this->sales_price,
            'publisher_ref_id' => $this->publisher_ref_id,
            'products_id' => $this->products_id
        ];

        $description_form_name = (new MobileRechargeDenoDescription)->formName();

        if (!empty($_POST[$description_form_name])) {
            foreach ($_POST[$description_form_name] as $lang => $description) {
                $description_model = (new MobileRechargeDenoDescription);
                $description_model->load($description, '');
                if ($description_model->validate()) {
                    $data['description'][$lang] = $description;
                }
            }
        }

        $this->request($data);

        return true;
    }

    public function batchCreate($publisher_id, $currency, $mark_up, $list, $products_id)
    {
        $this->action = 'batch-create-deno';

        $this->request([
            'publisher_id' => $publisher_id,
            'currency' => $currency,
            'mark_up' => $mark_up,
            'list' => $list,
            'products_id' => $products_id
        ]);
    }

    public function batchCreateDvs($publisher_id, $currency, $mark_up, $list, $products_id)
    {
        $this->action = 'batch-create-dvs-deno';

        $this->request([
            'publisher_id' => $publisher_id,
            'currency' => $currency,
            'mark_up' => $mark_up,
            'list' => $list,
            'products_id' => $products_id
        ]);
    }

    public function batchCreateDvsOperator($list)
    {
        $this->action = 'batch-create-dvs-operator';

        $this->request([
            'list' => $list,
        ]);
    }

    public function batchCreateBundle($publisher_id, $currency, $mark_up, $list, $products_id)
    {
        $this->action = 'batch-create-bundle';

        $this->request([
            'publisher_id' => $publisher_id,
            'currency' => $currency,
            'mark_up' => $mark_up,
            'list' => $list,
            'products_id' => $products_id
        ]);
    }

    public function batchUpdate($params)
    {
        $this->action = 'batch-update-deno';

        return $this->request($params);
    }

}