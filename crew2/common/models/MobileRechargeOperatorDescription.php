<?php

namespace common\models;

use Yii;
use yii\base\Model;

/**
 * This is the model class for table "mobile_recharge_operator_description".
 *
 * @property int $operator_description_id
 * @property int $operator_id
 * @property int $language_id
 * @property string $title
 * @property string $description
 * @property string $terms
 *
 * @property MobileRechargeOperator $operator
 */
class MobileRechargeOperatorDescription extends Model
{
    public $language_id;
    public $title;
    public $description;
    public $terms;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['language_id'], 'integer'],
            [['description', 'terms'], 'string'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'operator_description_id' => 'Operator Description ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'description' => 'Description',
            'terms' => 'Terms',
        ];
    }
}
