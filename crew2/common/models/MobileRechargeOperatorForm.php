<?php

namespace common\models;

use Yii;

class MobileRechargeOperatorForm extends ApiFormModel
{
    public $operator_id;
    public $title;
    public $region_id;
    public $logo_path;
    public $status;
    public $parent_operator_id;
    public $created_at;
    public $updated_at;
    public $imageFile;
    public $description;

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'mobile-recharge';
        $this->method = 'get';
        $this->searchField = ['title'];
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['operator_id','status', 'parent_operator_id', 'region_id', 'created_at', 'updated_at'], 'integer'],
            [['title'], 'safe'],
            [['logo_path'], 'string', 'max' => 2048],
            [['imageFile'], 'file', 'extensions' => 'png, jpg'],
            [['description'], 'safe']
        ];
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'get-operator-list';
        return parent::search($params, $key);
    }

    public function get($id)
    {
        $this->action = 'get-operator';

        $data = $this->request(['id' => $id]);

        return ['data' => $data];
    }

    public function getOperatorList()
    {
        $this->action = 'get-all-operator';
        $key = 'mobile-recharge/operator-list';

        $data = Yii::$app->cache->getOrSet($key, function () {
            return $this->request();
        });

        return $data;
    }


    public function update($id = 0)
    {
        $this->action = 'update-operator';

        $data = [
            'id' => $id,
            'region_id' => $this->region_id,
            'logo_path' => $this->logo_path,
            'parent_operator_id' => $this->parent_operator_id,
            'status' => $this->status,
        ];

        $description_form_name = (new MobileRechargeOperatorDescription)->formName();

        if (!empty($_POST[$description_form_name])) {
            foreach ($_POST[$description_form_name] as $lang => $description) {
                $description_model = (new MobileRechargeOperatorDescription);
                $description_model->load($description, '');
                if ($description_model->validate()) {
                    $data['description'][$lang] = $description;
                }
            }
        }

        $this->request($data);

        return true;
    }

    public function batchCreate($list)
    {
        $this->action = 'batch-create-operator';

        $this->request(['list' => $list]);
    }

}