<?php

namespace common\models;

use Yii;

class MobileRechargeRegionForm extends ApiFormModel
{
    public $region_id;
    public $flag;
    public $iso3;
    public $name;
    public $prefix;
    public $status;
    public $created_at;
    public $updated_at;

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'mobile-recharge';
        $this->method = 'get';
        $this->searchField = ['name', 'iso3', 'prefix'];
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['flag', 'iso3', 'name', 'prefix', 'status'], 'required'],
            [['prefix', 'status', 'created_at', 'updated_at'], 'integer'],
            [['flag'], 'string', 'max' => 5],
            [['iso3'], 'string', 'max' => 3],
            [['name'], 'string', 'max' => 255],
        ];
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'get-region-list';
        return parent::search($params, $key);
    }

    public function get($id)
    {
        $this->action = 'get-region';

        $data = $this->request(['id' => $id]);

        return ['data' => $data];
    }

    public function getRegionList()
    {
        $this->action = 'get-all-region';
        $key = 'mobile-recharge/region-list';

        $data = Yii::$app->cache->getOrSet($key, function () {
            return $this->request();
        });

        return $data;
    }

    public function update($id = 0)
    {
        $this->action = 'update-region';

        $this->request([
            'id' => $id,
            'name' => strip_tags(\yii\helpers\HtmlPurifier::process($this->name)),
            'flag' => strip_tags(\yii\helpers\HtmlPurifier::process($this->flag)),
            'iso3' => strip_tags(\yii\helpers\HtmlPurifier::process($this->iso3)),
            'prefix' => strip_tags(\yii\helpers\HtmlPurifier::process($this->prefix)),
            'status' => strip_tags(\yii\helpers\HtmlPurifier::process($this->status))
        ]);

        return true;
    }

    public function batchCreate($list)
    {
        $this->action = 'batch-create-region';

        $this->request(['list' => $list]);
    }

    public function uploadImage()
    {

    }

}