<?php

namespace common\models;

use Yii;
use yii\helpers\HtmlPurifier;

class OrderReviewForm extends ApiFormModel
{

    public $review_score;
    public $review_id;
    public $orders_id;
    public $orders_products_id;
    public $products_id;
    public $categories_id;
    public $status;
    public $visibility;
    public $comment;
    public $created_at;
    public $created_by;
    public $updated_at;
    public $updated_by;
    public $products_name;
    public $date_fr;
    public $date_to;

    const RATING_SCORE = array('0' => 'Low Rating', '1' => '1 Star', '2' => '2 Stars', '3' => '3 Stars', '4' => '4 Stars', '5' => '5 Stars', '6' => 'All');

    public function rules()
    {
        return [
            [
                [
                    'review_id',
                    'orders_id',
                    'orders_products_id',
                    'products_id',
                    'categories_id',
                    'review_score',
                    'status',
                    'visibility'
                ],
                'integer'
            ],
            [['created_at', 'updated_at', 'date_fr', 'date_to'], 'safe'],
            [['comment', 'created_by', 'updated_by', 'products_name'], 'string'],
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'order-review';
        $this->method = 'get';
        $this->searchField = ['orders_id', 'created_at', 'products_name', 'review_score', 'comment'];
        parent::__construct();
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'get-all-order-review';

        return parent::search($params, $key);
    }

    public function get($review_id)
    {
        $this->action = 'view';

        $data = $this->request($review_id);

        $this->load($data, '');
        return ['data' => $data];
    }

    public function create($input)
    {
        $this->action = 'create';

        $data = $this->request([
            'type' => $input['type'],
            'sort_order' => $input['sort_order'],
            'description' => $input['description']
        ]);

        return $data['id'];
    }

    public function update($review_id, $input)
    {
        $this->action = 'update';
        $remarks_hist = array();

        if ($input['OrderReviewForm']['status'] == '0') {
            $status = null;
        } else {
            $status = '3';
        }

        $user = Yii::$app->user->identity->username;
        $remarks = HtmlPurifier::process($input['OrderReviewRemarks']['remarks']);
        $remarks_status = (isset($input['OrderReviewRemarks']['remarks_status']) ? '1' : '2');
        if (isset($input['OrderReviewRemarksHist'])) {
            $remarks_hist = $input['OrderReviewRemarksHist'];
        }

        $params = [
            'review_id' => $review_id,
            'status' => $status,
            'visibility' => $input['OrderReviewForm']['visibility'],
            'user' => $user,
            'OrderReviewRemarks' => ['remarks' => $remarks, 'remarks_status' => $remarks_status],
            'OrderReviewRemarksHist' => $remarks_hist
        ];

        $data = $this->request($params);

        return $data;
    }

    public function log($review_id, $activity)
    {
        $this->action = 'save-log';

        $user = Yii::$app->user->identity->username;

        $data = $this->request(['review_id' => $review_id, 'user' => $user, 'activity' => $activity]);

        $this->load($data, '');
        return ['data' => $data];
    }

}
