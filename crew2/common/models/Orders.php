<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders".
 *
 * @property int $orders_id
 * @property int $customers_id
 * @property string $customers_name
 * @property string $customers_company
 * @property string $customers_street_address
 * @property string $customers_suburb
 * @property string $customers_city
 * @property string $customers_postcode
 * @property string $customers_state
 * @property string $customers_country
 * @property string $customers_telephone_country
 * @property string $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property string $customers_email_address
 * @property int $customers_address_format_id
 * @property int $customers_groups_id
 * @property string $delivery_name
 * @property string $delivery_company
 * @property string $delivery_street_address
 * @property string $delivery_suburb
 * @property string $delivery_city
 * @property string $delivery_postcode
 * @property string $delivery_state
 * @property string $delivery_country
 * @property int $delivery_address_format_id
 * @property string $billing_name
 * @property string $billing_company
 * @property string $billing_street_address
 * @property string $billing_suburb
 * @property string $billing_city
 * @property string $billing_postcode
 * @property string $billing_state
 * @property string $billing_country
 * @property int $billing_address_format_id
 * @property string $payment_method
 * @property int $payment_methods_parent_id
 * @property int $payment_methods_id
 * @property string $cc_type
 * @property string $cc_owner
 * @property string $cc_number
 * @property string $cc_expires
 * @property string $last_modified
 * @property string $date_purchased
 * @property int $orders_status
 * @property int $orders_cb_status
 * @property string $orders_date_finished
 * @property string $currency
 * @property string $remote_addr
 * @property string $currency_value
 * @property int $paypal_ipn_id
 * @property string $pm_2CO_cc_owner_firstname
 * @property string $pm_2CO_cc_owner_lastname
 * @property int $orders_locked_by
 * @property string $orders_locked_from_ip
 * @property string $orders_locked_datetime
 * @property string $orders_follow_up_datetime
 * @property string $orders_tag_ids
 * @property int $orders_read_mode
 * @property int $orders_aft_executed
 * @property int $orders_rebated
 */
class Orders extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_address_format_id', 'customers_groups_id', 'delivery_address_format_id', 'billing_address_format_id', 'payment_methods_parent_id', 'payment_methods_id', 'orders_status', 'orders_cb_status', 'paypal_ipn_id', 'orders_locked_by', 'orders_read_mode', 'orders_aft_executed', 'orders_rebated'], 'integer'],
            [['last_modified', 'date_purchased', 'orders_date_finished', 'orders_locked_datetime', 'orders_follow_up_datetime'], 'safe'],
            [['currency_value'], 'number'],
            [['customers_name', 'customers_street_address', 'customers_country', 'customers_telephone_country', 'delivery_name', 'delivery_street_address', 'delivery_country', 'billing_name', 'billing_street_address', 'billing_country', 'payment_method', 'cc_owner', 'pm_2CO_cc_owner_firstname', 'pm_2CO_cc_owner_lastname'], 'string', 'max' => 64],
            [['customers_company', 'customers_suburb', 'customers_city', 'customers_state', 'customers_telephone', 'delivery_company', 'delivery_suburb', 'delivery_city', 'delivery_state', 'billing_company', 'billing_suburb', 'billing_city', 'billing_state', 'cc_number'], 'string', 'max' => 32],
            [['customers_postcode', 'delivery_postcode', 'billing_postcode'], 'string', 'max' => 10],
            [['customers_country_international_dialing_code'], 'string', 'max' => 5],
            [['customers_email_address'], 'string', 'max' => 96],
            [['remote_addr', 'orders_locked_from_ip'], 'string', 'max' => 128],
            [['cc_type'], 'string', 'max' => 20],
            [['cc_expires'], 'string', 'max' => 4],
            [['currency'], 'string', 'max' => 3],
            [['orders_tag_ids'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'customers_id' => 'Customers ID',
            'customers_name' => 'Customers Name',
            'customers_company' => 'Customers Company',
            'customers_street_address' => 'Customers Street Address',
            'customers_suburb' => 'Customers Suburb',
            'customers_city' => 'Customers City',
            'customers_postcode' => 'Customers Postcode',
            'customers_state' => 'Customers State',
            'customers_country' => 'Customers Country',
            'customers_telephone_country' => 'Customers Telephone Country',
            'customers_country_international_dialing_code' => 'Customers Country International Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'customers_email_address' => 'Customers Email Address',
            'customers_address_format_id' => 'Customers Address Format ID',
            'customers_groups_id' => 'Customers Groups ID',
            'delivery_name' => 'Delivery Name',
            'delivery_company' => 'Delivery Company',
            'delivery_street_address' => 'Delivery Street Address',
            'delivery_suburb' => 'Delivery Suburb',
            'delivery_city' => 'Delivery City',
            'delivery_postcode' => 'Delivery Postcode',
            'delivery_state' => 'Delivery State',
            'delivery_country' => 'Delivery Country',
            'delivery_address_format_id' => 'Delivery Address Format ID',
            'billing_name' => 'Billing Name',
            'billing_company' => 'Billing Company',
            'billing_street_address' => 'Billing Street Address',
            'billing_suburb' => 'Billing Suburb',
            'billing_city' => 'Billing City',
            'billing_postcode' => 'Billing Postcode',
            'billing_state' => 'Billing State',
            'billing_country' => 'Billing Country',
            'billing_address_format_id' => 'Billing Address Format ID',
            'payment_method' => 'Payment Method',
            'payment_methods_parent_id' => 'Payment Methods Parent ID',
            'payment_methods_id' => 'Payment Methods ID',
            'cc_type' => 'Cc Type',
            'cc_owner' => 'Cc Owner',
            'cc_number' => 'Cc Number',
            'cc_expires' => 'Cc Expires',
            'last_modified' => 'Last Modified',
            'date_purchased' => 'Date Purchased',
            'orders_status' => 'Orders Status',
            'orders_cb_status' => 'Orders Cb Status',
            'orders_date_finished' => 'Orders Date Finished',
            'currency' => 'Currency',
            'remote_addr' => 'Remote Addr',
            'currency_value' => 'Currency Value',
            'paypal_ipn_id' => 'Paypal Ipn ID',
            'pm_2CO_cc_owner_firstname' => 'Pm 2 C O Cc Owner Firstname',
            'pm_2CO_cc_owner_lastname' => 'Pm 2 C O Cc Owner Lastname',
            'orders_locked_by' => 'Orders Locked By',
            'orders_locked_from_ip' => 'Orders Locked From Ip',
            'orders_locked_datetime' => 'Orders Locked Datetime',
            'orders_follow_up_datetime' => 'Orders Follow Up Datetime',
            'orders_tag_ids' => 'Orders Tag Ids',
            'orders_read_mode' => 'Orders Read Mode',
            'orders_aft_executed' => 'Orders Aft Executed',
            'orders_rebated' => 'Orders Rebated',
        ];
    }
}
