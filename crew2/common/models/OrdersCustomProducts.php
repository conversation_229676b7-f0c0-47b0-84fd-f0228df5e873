<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_custom_products".
 *
 * @property int $orders_custom_products_id
 * @property int $products_id
 * @property int $orders_products_id
 * @property string $orders_custom_products_key
 * @property string $orders_custom_products_value
 * @property int $orders_custom_products_number
 */
class OrdersCustomProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_custom_products';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'orders_products_id', 'orders_custom_products_number'], 'integer'],
            [['orders_custom_products_value'], 'required'],
            [['orders_custom_products_value'], 'string'],
            [['orders_custom_products_key'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_custom_products_id' => 'Orders Custom Products ID',
            'products_id' => 'Products ID',
            'orders_products_id' => 'Orders Products ID',
            'orders_custom_products_key' => 'Orders Custom Products Key',
            'orders_custom_products_value' => 'Orders Custom Products Value',
            'orders_custom_products_number' => 'Orders Custom Products Number',
        ];
    }
}
