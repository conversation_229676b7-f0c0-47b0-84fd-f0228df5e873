<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_products".
 *
 * @property int $orders_products_id
 * @property int $orders_id
 * @property int $products_id
 * @property string $products_model
 * @property string $products_name
 * @property string $orders_products_store_price
 * @property string $products_price
 * @property string $final_price
 * @property int $op_rebate
 * @property int $op_rebate_delivered
 * @property string $products_tax
 * @property int $products_quantity
 * @property string $products_delivered_quantity
 * @property string $products_good_delivered_quantity
 * @property string $products_good_delivered_price
 * @property string $products_canceled_quantity
 * @property string $products_canceled_price
 * @property string $products_reversed_quantity
 * @property string $products_reversed_price
 * @property int $products_bundle_id
 * @property int $parent_orders_products_id
 * @property int $products_pre_order
 * @property int $custom_products_type_id
 * @property int $orders_products_is_compensate
 * @property int $orders_products_purchase_eta
 * @property int $products_categories_id
 *
 * @property ApiRestockRequest[] $apiRestockRequests
 */
class OrdersProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_products';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'products_id', 'op_rebate', 'op_rebate_delivered', 'products_quantity', 'products_bundle_id', 'parent_orders_products_id', 'products_pre_order', 'custom_products_type_id', 'orders_products_is_compensate', 'orders_products_purchase_eta', 'products_categories_id'], 'integer'],
            [['orders_products_store_price', 'products_price', 'final_price', 'products_tax', 'products_delivered_quantity', 'products_good_delivered_quantity', 'products_good_delivered_price', 'products_canceled_quantity', 'products_canceled_price', 'products_reversed_quantity', 'products_reversed_price'], 'number'],
            [['products_model'], 'string', 'max' => 32],
            [['products_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products ID',
            'orders_id' => 'Orders ID',
            'products_id' => 'Products ID',
            'products_model' => 'Products Model',
            'products_name' => 'Products Name',
            'orders_products_store_price' => 'Orders Products Store Price',
            'products_price' => 'Products Price',
            'final_price' => 'Final Price',
            'op_rebate' => 'Op Rebate',
            'op_rebate_delivered' => 'Op Rebate Delivered',
            'products_tax' => 'Products Tax',
            'products_quantity' => 'Products Quantity',
            'products_delivered_quantity' => 'Products Delivered Quantity',
            'products_good_delivered_quantity' => 'Products Good Delivered Quantity',
            'products_good_delivered_price' => 'Products Good Delivered Price',
            'products_canceled_quantity' => 'Products Canceled Quantity',
            'products_canceled_price' => 'Products Canceled Price',
            'products_reversed_quantity' => 'Products Reversed Quantity',
            'products_reversed_price' => 'Products Reversed Price',
            'products_bundle_id' => 'Products Bundle ID',
            'parent_orders_products_id' => 'Parent Orders Products ID',
            'products_pre_order' => 'Products Pre Order',
            'custom_products_type_id' => 'Custom Products Type ID',
            'orders_products_is_compensate' => 'Orders Products Is Compensate',
            'orders_products_purchase_eta' => 'Orders Products Purchase Eta',
            'products_categories_id' => 'Products Categories ID',
        ];
    }
}
