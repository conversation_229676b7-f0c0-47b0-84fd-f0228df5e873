<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "orders_status".
 *
 * @property int $orders_status_id
 * @property int $language_id
 * @property string $orders_status_name
 * @property int $orders_status_sort_order
 */
class OrdersStatus extends \yii\db\ActiveRecord {

    /**
     * {@inheritdoc}
     */
    public static function tableName() {
        return 'orders_status';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb() {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['orders_status_id', 'language_id'], 'required'],
            [['orders_status_id', 'language_id', 'orders_status_sort_order'], 'integer'],
            [['orders_status_name'], 'string', 'max' => 32],
            [['orders_status_id', 'language_id'], 'unique', 'targetAttribute' => ['orders_status_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels() {
        return [
            'orders_status_id' => 'Orders Status ID',
            'language_id' => 'Language ID',
            'orders_status_name' => 'Orders Status Name',
            'orders_status_sort_order' => 'Orders Status Sort Order',
        ];
    }

    public function getOrdersStatus() {
        $data = Yii::$app->cache->getOrSet('orders_status/list', function () {
            $list = ArrayHelper::map(
                            self::find()->select(['orders_status_id', 'orders_status_name'])
                                    ->where(['language_id' => 1])
                                    ->asArray()->all(), 'orders_status_id', 'orders_status_name'
            );
            return $list;
        }, 3600);
        return $data;
    }

}
