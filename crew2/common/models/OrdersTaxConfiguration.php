<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_tax_configuration".
 *
 * @property string $orders_tax_id
 * @property string $country_code countries_iso_code_2
 * @property string $country_name
 * @property string $currency
 * @property string $orders_tax_percentage
 * @property string $business_tax_percentage
 * @property string $orders_tax_status
 * @property int $orders_provide_invoice_status
 * @property int $business_tax_status
 * @property int $orders_include_reverse_charge
 * @property string $start_datetime
 * @property string $address_1
 * @property string $address_2
 * @property string $address_3
 * @property string $contact
 * @property string $website
 * @property string $tax_invoice_title
 * @property string $tax_registration_name
 * @property string $gst_registration_no
 * @property string $company_name
 * @property string $company_logo
 * @property string $business_tax_form
 */
class OrdersTaxConfiguration extends \yii\db\ActiveRecord {
    /*
     * innerJoin
     */

    public $orders_tax_title;

    /**
     * {@inheritdoc}
     */
    public static function tableName() {
        return 'orders_tax_configuration';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb() {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['country_name', 'address_1', 'contact', 'website', 'gst_registration_no', 'company_name'], 'required'],
            [['orders_tax_percentage', 'business_tax_percentage'], 'number'],
            [['orders_tax_status', 'business_tax_form'], 'string'],
            [['orders_provide_invoice_status', 'business_tax_status', 'orders_include_reverse_charge'], 'integer'],
            [['start_datetime'], 'safe'],
            [['country_code'], 'string', 'max' => 2],
            [['country_name', 'address_1', 'address_2', 'address_3', 'tax_invoice_title', 'tax_registration_name'], 'string', 'max' => 64],
            [['currency'], 'string', 'max' => 3],
            [['contact', 'website', 'gst_registration_no', 'company_name'], 'string', 'max' => 32],
            [['company_logo'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels() {
        return [
            'orders_tax_id' => 'Orders Tax ID',
            'country_code' => 'Country Code',
            'country_name' => 'Country Name',
            'currency' => 'Currency',
            'orders_tax_percentage' => 'Orders Tax Percentage',
            'business_tax_percentage' => 'Business Tax Percentage',
            'orders_tax_status' => 'Orders Tax Status',
            'orders_provide_invoice_status' => 'Orders Provide Invoice Status',
            'business_tax_status' => 'Business Tax Status',
            'orders_include_reverse_charge' => 'Orders Include Reverse Charge',
            'start_datetime' => 'Start Datetime',
            'address_1' => 'Address 1',
            'address_2' => 'Address 2',
            'address_3' => 'Address 3',
            'contact' => 'Contact',
            'website' => 'Website',
            'tax_invoice_title' => 'Tax Invoice Title',
            'tax_registration_name' => 'Tax Registration Name',
            'gst_registration_no' => 'Gst Registration No',
            'company_name' => 'Company Name',
            'company_logo' => 'Company Logo',
            'business_tax_form' => 'Business Tax Form',
        ];
    }

    public function getOrdersTaxConfigurationDescription() {
        return $this->hasMany(OrdersTaxConfigurationDescription::class, ['orders_tax_id' => 'orders_tax_id']);
    }

    public function getOrdersTaxCustomers() {
        return $this->hasMany(OrdersTaxCustomers::className(), ['orders_tax_id' => 'orders_tax_id']);
    }

    public function afterSave($insert, $changedAttributes) {
        $log[$this->tableName()] = [];
        $attr = $this->attributes();
        if ($insert) {
            foreach ($attr as $i => $name) {
                $log[$this->tableName()][$name] = ["before" => "", "after" => $this->$name];
            }
        } else {
            foreach ($attr as $i => $name) {
                if (isset($changedAttributes[$name]) && ($changedAttributes[$name] != $this->$name)) {
                    $log[$this->tableName()][$name] = ["before" => $changedAttributes[$name], "after" => $this->$name];
                }
            }
        }

        if ($log[$this->tableName()]) {
            $m = new OrdersTaxConfigurationLog();
            $m->log_users_id = Yii::$app->user->identity->username;
            $m->log_orders_tax_id = $this->orders_tax_id;
            $m->log_orders_tax_lang_id = 1;
            $m->log_data = json_encode($log[$this->tableName()]);
            $m->log_IP = Yii::$app->getRequest()->getUserIP();
            $m->log_datetime = date('Y-m-d H:i:s');
            $m->log_action = ($insert ? "CREATE" : "MODIFY");
            $m->save();
        }

        parent::afterSave($insert, $changedAttributes);
    }

    public function beforeDelete() {
        if (!parent::beforeDelete()) {
            return false;
        }

        OrdersTaxConfigurationDescription::deleteAll(['orders_tax_id' => $this->orders_tax_id]);

        $m = new OrdersTaxConfigurationLog();
        $m->log_users_id = Yii::$app->user->identity->username;
        $m->log_orders_tax_id = $this->orders_tax_id;
        $m->log_data = json_encode([]);
        $m->log_IP = Yii::$app->getRequest()->getUserIP();
        $m->log_datetime = date("Y-m-d H:i:s");
        $m->log_action = "DELETE";
        $m->save();
        return true;
    }

}
