<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_tax_configuration_description".
 *
 * @property string $orders_tax_id
 * @property string $language_id
 * @property string $orders_tax_title
 * @property string $orders_tax_title_short
 * @property string $orders_tax_message
 */
class OrdersTaxConfigurationDescription extends \yii\db\ActiveRecord {

    /**
     * {@inheritdoc}
     */
    public static function tableName() {
        return 'orders_tax_configuration_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb() {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            // Control in model form
            [['orders_tax_id', 'language_id', 'orders_tax_title', 'orders_tax_title_short', 'orders_tax_message'], 'required'],
            [['orders_tax_id', 'language_id'], 'integer'],
            [['orders_tax_message'], 'string'],
            [['orders_tax_title'], 'string', 'max' => 32],
            [['orders_tax_title_short'], 'string', 'max' => 10],
            [['orders_tax_id', 'language_id'], 'unique', 'targetAttribute' => ['orders_tax_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels() {
        return [
            'orders_tax_id' => 'Orders Tax ID',
            'language_id' => 'Language ID',
            'orders_tax_title' => 'Tax Title',
            'orders_tax_title_short' => 'Tax Title Short',
            'orders_tax_message' => 'Tax Message',
        ];
    }

    public function getOrdersTaxConfiguration() {
        return $this->hasOne(OrdersTaxConfiguration::className(), ['orders_tax_id' => 'orders_tax_id']);
    }

    public function afterSave($insert, $changedAttributes) {
        $log[$this->tableName()] = [];
        $attr = $this->attributes();
        if ($insert) {
            foreach ($attr as $i => $name) {
                $log[$this->tableName()][$name] = ["before" => "", "after" => $this->$name];
            }
        } else {
            foreach ($attr as $i => $name) {
                if (isset($changedAttributes[$name]) && ($changedAttributes[$name] != $this->$name)) {
                    $log[$this->tableName()][$name] = ["before" => $changedAttributes[$name], "after" => $this->$name];
                }
            }
        }

        if ($log[$this->tableName()]) {
            $m = new OrdersTaxConfigurationLog();
            $m->log_users_id = Yii::$app->user->identity->username;
            $m->log_orders_tax_id = $this->orders_tax_id;
            $m->log_orders_tax_lang_id = 1;
            $m->log_data = json_encode($log[$this->tableName()]);
            $m->log_IP = Yii::$app->getRequest()->getUserIP();
            $m->log_datetime = date('Y-m-d H:i:s');
            $m->log_action = ($insert ? "CREATE" : "MODIFY");
            $m->save();
        }

        parent::afterSave($insert, $changedAttributes);
    }

}
