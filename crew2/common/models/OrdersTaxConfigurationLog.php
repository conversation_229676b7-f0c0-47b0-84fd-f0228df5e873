<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_tax_configuration_log".
 *
 * @property string $log_id
 * @property string $log_users_id
 * @property int $log_orders_tax_id
 * @property int $log_orders_tax_lang_id
 * @property string $log_data
 * @property string $log_IP
 * @property string $log_datetime
 * @property string $log_action
 */
class OrdersTaxConfigurationLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_tax_configuration_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['log_users_id', 'log_orders_tax_id', 'log_orders_tax_lang_id', 'log_data', 'log_IP', 'log_action'], 'required'],
            [['log_orders_tax_id', 'log_orders_tax_lang_id'], 'integer'],
            [['log_data'], 'string'],
            [['log_datetime'], 'safe'],
            [['log_users_id'], 'string', 'max' => 255],
            [['log_IP'], 'string', 'max' => 15],
            [['log_action'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'log_id' => 'Log ID',
            'log_users_id' => 'Log Users ID',
            'log_orders_tax_id' => 'Log Orders Tax ID',
            'log_orders_tax_lang_id' => 'Log Orders Tax Language ID',
            'log_data' => 'Log Data',
            'log_IP' => 'Log I P',
            'log_datetime' => 'Log Datetime',
            'log_action' => 'Log Action',
        ];
    }
}
