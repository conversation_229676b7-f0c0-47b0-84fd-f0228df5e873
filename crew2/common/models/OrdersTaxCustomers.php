<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_tax_customers".
 *
 * @property string $id
 * @property string $customers_id
 * @property string $orders_tax_id
 * @property string $business_name
 * @property string $business_tax_number
 * @property string $orders_tax_customers_data
 * @property int $orders_tax_customers_status
 * @property int $created_at
 * @property int $updated_at
 */
class OrdersTaxCustomers extends \yii\db\ActiveRecord {

    /**
     * {@inheritdoc}
     */
    public static function tableName() {
        return 'orders_tax_customers';
    }

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['customers_id', 'orders_tax_id', 'business_name', 'business_tax_number'], 'required'],
            [['customers_id', 'orders_tax_id', 'orders_tax_customers_status', 'created_at', 'updated_at'], 'integer'],
            [['orders_tax_customers_data'], 'string'],
            [['business_name', 'business_tax_number'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels() {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'orders_tax_id' => 'Orders Tax ID',
            'business_name' => 'Business Name',
            'business_tax_number' => 'Business Tax Number',
            'orders_tax_customers_data' => 'Orders Tax Customers Data',
            'orders_tax_customers_status' => 'Orders Tax Customers Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getOrdersTaxConfiguration() {
        return $this->hasOne(OrdersTaxConfiguration::className(), ['orders_tax_id' => 'orders_tax_id']);
    }

    public function getCustomers() {
        return $this->hasOne(Customers::className(), ['customers_id' => 'customers_id']);
    }

    public function afterSave($insert, $changedAttribute) {
        $m = new OrdersTaxCustomersHistory();
        $m->customers_id = $this->customers_id;
        $m->orders_tax_customers_id = $this->id;
        $m->orders_tax_customers_data = $this->orders_tax_customers_data;
        $m->orders_tax_customers_status = $this->orders_tax_customers_status;
        $m->created_at = $this->updated_at;
        $m->updated_at = $this->updated_at;
        $m->save();

        return parent::afterSave($insert, $changedAttribute);
    }

}
