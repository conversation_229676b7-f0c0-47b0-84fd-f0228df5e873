<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_tax_customers_history".
 *
 * @property string $id
 * @property string $customers_id
 * @property string $orders_tax_customers_id
 * @property string $orders_tax_customers_data
 * @property int $orders_tax_customers_status
 * @property int $created_at
 * @property int $updated_at
 */
class OrdersTaxCustomersHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_tax_customers_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'orders_tax_customers_id'], 'required'],
            [['customers_id', 'orders_tax_customers_id', 'orders_tax_customers_status', 'created_at', 'updated_at'], 'integer'],
            [['orders_tax_customers_data'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'orders_tax_customers_id' => 'Orders Tax Customers ID',
            'orders_tax_customers_data' => 'Orders Tax Customers Data',
            'orders_tax_customers_status' => 'Orders Tax Customers Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
