<?php

namespace common\models;

use offgamers\base\models\OrdersTaxRuleDetails;
use offgamers\base\models\OrdersTaxRuleMapping;
use yii\base\Model;
use yii\data\ActiveDataProvider;

class OrdersTaxRuleDetailsForm extends Model
{

    public $tax_rule_detail_id;
    public $tax_rule_id;
    public $fieldname;
    public $condition;
    public $values;
    public $status;
    public $isNewRecord;
    public $removed;
    public $created_at;
    public $created_by;
    public $updated_at;
    public $updated_by;

    public function rules()
    {
        return [
            [['fieldname', 'condition'], 'required'],
        ];
    }

    const FIELDNAME = [
        'CURRENCY' => 'Checkout Currency',
        'BILLING_COUNTRY' => 'Billing Address Country',
        'IP_COUNTRY' => 'IP Country',
        'EMAIL_COUNTRY' => 'Email Country'
    ];
    const CONDITION_TYPE = ['EQUAL'];

    public function search($params)
    {
        $orders_tax_id = (isset($params['orders_tax_id']) ? $params['orders_tax_id'] : null);
        $query = OrdersTaxRuleMapping::find()->where(['orders_tax_id' => $orders_tax_id])
            ->orderBy(['sort_order' => SORT_ASC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public function getTaxRuleItems($id)
    {
        $model = OrdersTaxRuleDetails::findOne($id);
        $attr = $model->attributes();
        foreach ($attr as $i => $name) {
            $this->$name = $model->$name;
        }
    }

    public function saveItem()
    {
        if ($this->isNewRecord) {
            $model = new OrdersTaxRuleDetails();
            $model->tax_rule_id = $this->tax_rule_id;
            $model->fieldname = $this->fieldname;
            $model->condition = self::CONDITION_TYPE[$this->condition];
            $model->values = $this->values;
            $model->status = 1;
            $model->save();
        } else {
            $model = OrdersTaxRuleDetails::findOne($this->tax_rule_detail_id);
            $model->fieldname = $this->fieldname;
            $model->condition = self::CONDITION_TYPE[$this->condition];
            $model->values = $this->values;
            $model->save();
        }
    }

    public function removeItem()
    {
        if ($this->removed) {
            $model = OrdersTaxRuleDetails::findOne($this->tax_rule_detail_id);
            $model->delete();
        }
    }

}