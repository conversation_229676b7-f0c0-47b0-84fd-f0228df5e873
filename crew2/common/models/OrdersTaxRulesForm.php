<?php

namespace common\models;

use offgamers\base\models\OrdersTaxRuleDetails;
use offgamers\base\models\OrdersTaxRuleMapping;
use offgamers\base\models\OrdersTaxRules;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;

class OrdersTaxRulesForm extends Model
{

    public $tax_rule_mapping_id;
    public $orders_tax_id;
    public $tax_rule_id;
    public $status;
    public $sort_order;
    public $description;
    public $rule_type;
    public $created_at;
    public $created_by;
    public $updated_at;
    public $updated_by;
    public $tax_rule_items;
    public $isNewRecord;



    public function rules()
    {
        return [
        ];
    }

    const RULE_TYPE = ['OR' => 'OR', 'AND'=>'AND'];

    public function search($params)
    {
        $orders_tax_id = (isset($params['orders_tax_id'])?$params['orders_tax_id']:null);
        $query = OrdersTaxRuleMapping::find()->where(['orders_tax_id' => $orders_tax_id])
        ->orderBy(['sort_order' => SORT_ASC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        return $dataProvider;
    }

    public function getTaxRules($id)
    {
        $query = OrdersTaxRuleMapping::find()->alias('trm')
            ->select(['trm.orders_tax_id','trm.tax_rule_mapping_id','trm.tax_rule_id','tr.status','trm.sort_order','tr.description','tr.rule_type'])
            ->joinWith('taxRule tr')
            ->where(['trm.orders_tax_id' => $id])
            ->orderBy(['sort_order' => SORT_ASC]);


        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);
        return $dataProvider;
    }

    public function getTaxRuleInfo($id){
        $model = OrdersTaxRules::findOne($id);
        $attr = $model->attributes();
        foreach ($attr as $i => $name) {
            $this->$name = $model->$name;
        }
        $tax_rule_map = OrdersTaxRuleMapping::find()->where(['tax_rule_id' => $this->tax_rule_id])->asArray()->one();
        $this->orders_tax_id = $tax_rule_map['orders_tax_id'];
        $tax_rule_detail_ids = OrdersTaxRuleDetails::find()->select(['tax_rule_detail_id'])->where(['tax_rule_id' => $this->tax_rule_id])->asArray()->all();

        foreach($tax_rule_detail_ids as $tax_rule_detail_id){
            $detail = new OrdersTaxRuleDetailsForm();
            $detail->getTaxRuleItems($tax_rule_detail_id);
            $this->tax_rule_items[] = $detail;
        }
    }

    public function enableTaxRule($id)
    {
        $model = OrdersTaxRules::findOne($id);
        $model->status = 1;
        return $model->save();
    }

    public function disableTaxRule($id)
    {
        $model = OrdersTaxRules::findOne($id);
        $model->status = 0;
        return $model->save();
    }

    public function saveTaxRule()
    {
        if($this->isNewRecord) {
            $model = new OrdersTaxRules();
            $model->description = $this->description;
            $model->rule_type = SELF::RULE_TYPE[$this->rule_type];
            $model->status = 1;
            $model->save();
            $tax_rule_id = $model->getPrimaryKey();
            $model = new OrdersTaxRuleMapping();
            $model->orders_tax_id = $this->orders_tax_id;
            $model->tax_rule_id = $tax_rule_id;
            $model->status = 1;
            $model->sort_order = 50;
            $model->save();
        }else{
            $model = OrdersTaxRules::findOne($this->tax_rule_id);
            $model->description = $this->description;
            $model->rule_type = SELF::RULE_TYPE[$this->rule_type];
            $model->save();
            $tax_rule_id = $model->getPrimaryKey();
        }

        return $tax_rule_id;

    }



}