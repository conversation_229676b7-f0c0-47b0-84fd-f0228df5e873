<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_top_up".
 *
 * @property int $orders_products_id OGM order product ID
 * @property string $top_up_id Top-up ID
 * @property string $publishers_ref_id
 * @property string $currency_code
 * @property string $currency_settle_amount
 * @property string $top_up_status Top-up status, 1=Pending, 3=Processing, 10=Failed, 11=NotFound
 * @property string $publishers_response_time Last response time from publisher, like top-up successful time
 * @property double $customer_before_balance Customer's Balance before top-up
 * @property double $customer_after_balance Customer's Balance after top-up
 * @property string $game Customer's game to top-up
 * @property string $server Customer's server to top-up
 * @property string $account Customer's account to top-up
 * @property string $character Customer's character to top-up
 * @property string $publishers_id
 * @property string $top_up_response_info
 * @property string $top_up_process_flag 0 = pending, 1 = processing, 2=complete
 * @property int $result_code
 * @property string $top_up_last_processed_time
 * @property string $top_up_timestamp
 * @property string $top_up_created_date
 */
class OrdersTopUp extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_top_up';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id', 'publishers_id', 'result_code'], 'integer'],
            [['currency_settle_amount', 'customer_before_balance', 'customer_after_balance'], 'number'],
            [['top_up_status', 'top_up_response_info', 'top_up_process_flag'], 'string'],
            [['publishers_response_time', 'top_up_last_processed_time', 'top_up_timestamp', 'top_up_created_date'], 'safe'],
            [['publishers_ref_id'], 'string', 'max' => 32],
            [['currency_code'], 'string', 'max' => 3],
            [['game', 'server', 'account', 'character'], 'string', 'max' => 64],
            [['orders_products_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products ID',
            'top_up_id' => 'Top Up ID',
            'publishers_ref_id' => 'Publishers Ref ID',
            'currency_code' => 'Currency Code',
            'currency_settle_amount' => 'Currency Settle Amount',
            'top_up_status' => 'Top Up Status',
            'publishers_response_time' => 'Publishers Response Time',
            'customer_before_balance' => 'Customer Before Balance',
            'customer_after_balance' => 'Customer After Balance',
            'game' => 'Game',
            'server' => 'Server',
            'account' => 'Account',
            'character' => 'Character',
            'publishers_id' => 'Publishers ID',
            'top_up_response_info' => 'Top Up Response Info',
            'top_up_process_flag' => 'Top Up Process Flag',
            'result_code' => 'Result Code',
            'top_up_last_processed_time' => 'Top Up Last Processed Time',
            'top_up_timestamp' => 'Top Up Timestamp',
            'top_up_created_date' => 'Top Up Created Date',
        ];
    }
}
