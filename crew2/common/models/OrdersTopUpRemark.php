<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "orders_top_up_remark".
 *
 * @property string $orders_top_up_remark_id
 * @property string $top_up_id Top-up ID
 * @property string $data_added Record added time
 * @property string $remark Remark
 * @property string $changed_by
 */
class OrdersTopUpRemark extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_top_up_remark';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['top_up_id'], 'integer'],
            [['data_added'], 'safe'],
            [['remark'], 'required'],
            [['remark'], 'string'],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_top_up_remark_id' => 'Orders Top Up Remark ID',
            'top_up_id' => 'Top Up ID',
            'data_added' => 'Data Added',
            'remark' => 'Remark',
            'changed_by' => 'Changed By',
        ];
    }
}
