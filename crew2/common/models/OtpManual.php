<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%otp_manual}}".
 *
 * @property int $id
 * @property int $country_id
 * @property string $hp
 * @property string $token
 * @property string $updated_by
 * @property string $remark
 * @property string $expired_at
 * @property string $created_at
 * @property string $updated_at
 * @property Countries $country
 */
class OtpManual extends ActiveRecord
{
    const STATUS_NEW = 1;
    const STATUS_USED = 2;
    const STATUS_EXPIRED = 3;

    public static function tableName()
    {
        return '{{%otp_manual}}';
    }

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public function behaviors()
    {
        return [
            TimestampBehavior::class,
        ];
    }

    public function rules()
    {
        return [
            [[
                'country_id',
                'hp',
                'token',
                'status',
                'updated_by',
                'remark',
                'expired_at',
                'created_at',
                'updated_at'
            ], 'safe'],
        ];
    }

    public function getCountry()
    {
        return $this->hasOne(Countries::class, ['countries_id' => 'country_id']);
    }
}