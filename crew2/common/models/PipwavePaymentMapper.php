<?php

namespace common\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "pipwave_payment_mapper".
 *
 * @property string $id
 * @property string $pm_id
 * @property string $pipwave_payment_code
 * @property string $pm_display_name
 * @property string $pg_display_name
 * @property string $pg_id
 * @property string $pg_code
 * @property int $is_rp
 * @property int $site_id
 * @property int $is_refundable
 */
class PipwavePaymentMapper extends \yii\db\ActiveRecord {

    /**
     * {@inheritdoc}
     */
    public static function tableName() {
        return 'pipwave_payment_mapper';
    }

    /**
     * {@inheritdoc}
     */
    public function rules() {
        return [
            [['pipwave_payment_code', 'pm_display_name', 'pg_display_name', 'pm_id', 'pg_id', 'is_rp', 'is_refundable'], 'required'],
            [['pm_id', 'pg_id', 'is_rp', 'site_id', 'is_refundable'], 'integer'],
            [['pipwave_payment_code', 'pm_display_name', 'pg_display_name', 'pg_code'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels() {
        return [
            'id' => 'ID',
            'pipwave_payment_code' => 'Pipwave Payment Code',
            'pg_id' => 'Payment Gateway',
            'pg_display_name' => 'Payment Gateway Display Name',
            'pm_id' => 'Payment Method',
            'pm_display_name' => 'Payment Method Display Name',
            'is_rp' => 'Is RP',
            'site_id' => 'Site ID',
            'is_refundable' => 'Is Refundable',
        ];
    }

    public function beforeSave($insert) {
        $sql = PaymentMethods::find()->where(['payment_methods_id' => $this->pg_id])->one();
        if ($sql) {
            $this->pg_code = $sql->payment_methods_code;
        }

        $this->site_id = 0;

        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes) {
        parent::afterSave($insert, $changedAttributes);
        if ($changedAttributes) {
            $tableArray = ['before' => [], 'after' => []];
            foreach ($changedAttributes as $key => $value) {
                if ($value != $this->$key && $value != null) {
                    $tableArray['before'][$key] = $value;
                    $tableArray['after'][$key] = (isset($this->$key)) ? $this->$key : '';
                } elseif ($value == null) {
                    $tableArray['after'][$key] = $this->$key;
                }
            }
            if ($tableArray['after']) {
                $historyModel = new \common\models\PipwavePaymentMapperHistory;
                $historyModel->pipwave_payment_mapper_id = $this->id;
                $historyModel->history_data = json_encode($tableArray);
                $historyModel->changed_by = Yii::$app->user->identity->username;
                $historyModel->save();
            }
        }
    }

    public function beforeDelete() {
        if (!parent::beforeDelete()) {
            return false;
        }

        $historyModel = new \common\models\PipwavePaymentMapperHistory;
        $historyModel->pipwave_payment_mapper_id = $this->id;
        $historyModel->history_data = json_encode(['before' => $this->attributes, 'after' => []]);
        $historyModel->changed_by = Yii::$app->user->identity->username;
        $historyModel->save();
        return true;
    }

    public function getIsRpOptions($isRp) {
        switch ($isRp) {
            case 1:
                return Yii::t('pipwave-mapper', 'RP');
            case 2:
                return Yii::t('pipwave-mapper', 'Semi RP');
            default:
                return Yii::t('pipwave-mapper', 'NRP');
        }
    }

    public function getIsRpList() {
        return [1 => 'RP', 2 => 'Semi RP', 0 => 'NRP'];
    }

    public function getIsRefundable($isRefundable) {
        switch ($isRefundable) {
            case 1:
                return Yii::t('pipwave-mapper', 'Refundable');
            default:
                return Yii::t('pipwave-mapper', 'Non-Refundable');
        }
    }

    public function getIsRefundableList() {
        return [1 => 'Refundable', 0 => 'Non-Refundable'];
    }

    public static function getPaymentMethodList() {
        $data = Yii::$app->cache->getOrSet('payment_method/list', function () {
            $list = [];
            $pm_list = self::find()->select(['pm_id', 'pm_display_name', 'pg_display_name'])
                ->where(['site_id' => 0])
                ->asArray()
                ->orderBy('pm_display_name')
                ->all();

            foreach ($pm_list as $pm) {
                $list[$pm['pm_id']] = $pm['pm_display_name'] . '(' . $pm['pg_display_name'] . ')';
            }

            return $list;
        }, 3600);
        return $data;
    }

    public static function getOGPaymentList() {
        $data = Yii::$app->cache->getOrSet('payment_mapper/list', function () {
            $list = [];
            $pm_list = self::find()->select(['id', 'pm_display_name', 'pg_display_name'])
                                    ->where(['site_id' => 0])
                ->asArray()
                ->orderBy('pm_display_name')
                ->all();

            foreach ($pm_list as $pm) {
                $list[$pm['id']] = $pm['pm_display_name'] . '(' . $pm['pg_display_name'] . ')';
            }

            return $list;
        }, 3600);
        return $data;
    }

}
