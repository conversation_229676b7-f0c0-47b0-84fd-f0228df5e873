<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "pipwave_payment_mapper_history".
 *
 * @property string $id
 * @property string $pipwave_payment_mapper_id
 * @property string $history_data
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 */
class PipwavePaymentMapperHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pipwave_payment_mapper_history';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['pipwave_payment_mapper_id', 'changed_by'], 'required'],
            [['pipwave_payment_mapper_id', 'created_at', 'updated_at'], 'integer'],
            [['history_data'], 'string'],
            [['changed_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'pipwave_payment_mapper_id' => 'Pipwave Payment Mapper ID',
            'history_data' => 'History Data',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'changed_by' => 'Changed By',
        ];
    }
}
