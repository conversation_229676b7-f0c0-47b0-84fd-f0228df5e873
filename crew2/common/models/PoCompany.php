<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "po_company".
 *
 * @property string $po_company_id
 * @property string $po_company_code
 * @property string $po_company_status
 * @property string $po_company_name
 * @property string $po_company_contact_name
 * @property string $po_company_street_address
 * @property string $po_company_suburb
 * @property string $po_company_city
 * @property string $po_company_postcode
 * @property string $po_company_state
 * @property int $po_company_country_id
 * @property int $po_company_zone_id
 * @property int $po_company_format_id
 * @property string $po_company_telephone
 * @property string $po_company_fax
 * @property string $po_company_invoice_footer
 * @property string $po_company_gst_percentage
 */
class PoCompany extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'po_company';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['po_company_status', 'po_company_country_id', 'po_company_zone_id', 'po_company_format_id'], 'integer'],
            [['po_company_invoice_footer'], 'string'],
            [['po_company_gst_percentage'], 'number'],
            [['po_company_code'], 'string', 'max' => 2],
            [['po_company_name', 'po_company_suburb', 'po_company_city', 'po_company_state', 'po_company_telephone', 'po_company_fax'], 'string', 'max' => 32],
            [['po_company_contact_name', 'po_company_street_address'], 'string', 'max' => 64],
            [['po_company_postcode'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'po_company_id' => 'Po Company ID',
            'po_company_code' => 'Po Company Code',
            'po_company_status' => 'Po Company Status',
            'po_company_name' => 'Po Company Name',
            'po_company_contact_name' => 'Po Company Contact Name',
            'po_company_street_address' => 'Po Company Street Address',
            'po_company_suburb' => 'Po Company Suburb',
            'po_company_city' => 'Po Company City',
            'po_company_postcode' => 'Po Company Postcode',
            'po_company_state' => 'Po Company State',
            'po_company_country_id' => 'Po Company Country ID',
            'po_company_zone_id' => 'Po Company Zone ID',
            'po_company_format_id' => 'Po Company Format ID',
            'po_company_telephone' => 'Po Company Telephone',
            'po_company_fax' => 'Po Company Fax',
            'po_company_invoice_footer' => 'Po Company Invoice Footer',
            'po_company_gst_percentage' => 'Po Company Gst Percentage',
        ];
    }
}
