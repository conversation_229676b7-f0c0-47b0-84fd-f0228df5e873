<?php

namespace common\models;

use Yii;

class ProductReviewForm extends ApiFormModel
{

    public $blacklist_id;
    public $item_id;
    public $item_name;
    public $item_type;
    public $status;
    public $created_at;
    public $created_by;
    public $updated_at;
    public $updated_by;

    public function rules()
    {
        return [
            [
                [
                    'blacklist_id',
                    'item_id',
                    'status',
                ],
                'integer'
            ],
            [['created_at', 'updated_at',], 'safe'],
            [['item_type', 'created_by', 'updated_by', 'item_name'], 'string'],
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'product-review';
        $this->method = 'get';
        $this->searchField = ['blacklist_id'];
        parent::__construct();
    }

    public function search($params, $key = 'id')
    {
        $this->action = 'get-all-blacklist-items';

        if (!empty($params)) {
            $form_array = array();
            foreach ($params as $param_key => $value) {
                if($param_key ==$this->formName())
                {
                    foreach($value as $value_key => $value)
                    {
                        $form_array[$value_key] = $value;
                    }
                }else {
                    $form_array[$param_key] = $value;
                }
            }
            unset($params);
            $params[$this->formName()] = $form_array;
        }

        return parent::search($params, $key);
    }

    public function deleteById($blacklist_id)
    {
        $this->action = 'delete-by-blacklist-id';

        $params = [
            'blacklist_id' => $blacklist_id,
            'user' => Yii::$app->user->identity->username,
        ];

        $data = $this->request($params);

        return $data;

    }

    public function getItemList($input)
    {
        $this->action = 'get-item-list';
        return $this->request($input);
    }

    public function addBlacklist($input)
    {
        $this->action = 'add-blacklist';
        $input['user'] = Yii::$app->user->identity->username;
        return $this->request($input);
    }



}