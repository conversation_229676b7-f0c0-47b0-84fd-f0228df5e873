<?php

namespace common\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Products;

/**
 * ProductSearch represents the model behind the search form of `common\models\Products`.
 */
class ProductSearch extends Products
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'products_quantity', 'products_actual_quantity', 'products_status', 'products_display', 'products_skip_inventory', 'products_purchase_mode', 'products_tax_class_id', 'manufacturers_id', 'products_ordered', 'products_quantity_order', 'products_pre_order_level', 'products_out_of_stock_level', 'products_bundle_dynamic_qty', 'products_main_cat_id', 'products_auto_seo', 'products_sort_order', 'custom_products_type_id', 'custom_products_type_child_id', 'products_payment_mature_period', 'products_type'], 'integer'],
            [['products_model', 'products_base_currency', 'products_date_added', 'products_last_modified', 'products_date_available', 'products_add_to_cart_msg', 'products_preorder_msg', 'products_out_of_stock_msg', 'products_bundle', 'products_bundle_dynamic', 'products_cat_id_path', 'products_cat_path', 'products_url_alias', 'products_flag_id', 'products_buyback_quantity'], 'safe'],
            [['products_price', 'products_weight', 'products_ship_price', 'products_eta', 'products_buyback_price', 'products_quantity_fifo_cost', 'products_actual_quantity_fifo_cost'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Products::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        $query->andFilterWhere(['custom_products_type_id' => 2]);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'products_id' => $this->products_id
        ]);

        $query->andFilterWhere(['like', 'products_model', $this->products_model]);

        return $dataProvider;
    }
}
