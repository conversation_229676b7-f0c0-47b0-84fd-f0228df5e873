<?php

namespace common\models;

use Yii;
use yii\helpers\Json;

class ProductsConfigForm extends ApiFormModel
{

    public $categories_id;
    public $form;
    public $categories_list;

    public function rules()
    {
        return [
        ];
    }

    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'products-config';
        $this->method = 'get';
        $this->searchField = ['review_id'];
        parent::__construct();
    }

    public function getCategoriesList()
    {
        $output = Categories::getSecondLayerCategory();
        $output = array_flip($output);
        ksort($output);
        $output = array_flip($output);
        return $output;
    }

    public function getProductsList($cid)
    {
        $data = ['type' => 'products', 'list' => $cid];

        $output = $this->getItemList($data);
        $output = array_flip($output);
        ksort($output);
        $output = array_flip($output);
        $models = [];
        foreach ($output as $key => $value) {
            $form = new ProductsConfigForm();
            $model = $form->getProductTaxConfig($key);
            array_push($models, $model);
        }

        return $models;
    }

    public function getItemList($input)
    {
        $this->action = 'get-item-list';
        return $this->request($input);
    }

    public function getProductTaxConfig($pid)
    {
        $model = new ProductsTaxCategoryForm();
        $model->getProductsTaxCategoryByPid($pid);
        return $model;
    }

    public function saveProductConfig()
    {
        foreach ($this->form as $record) {
            $model = new ProductsTaxCategoryForm();
            $model->products_id = $record['products_id'];
            $model->category = (isset($record['category'])) ? 'MRV' : '';
            $model->currency = $record['currency'] ?? '';
            $model->products_value = $record['products_value'] ?? '';
            $model->delete = !$model->category;
            $model->saveRecord();
        }
    }
}
