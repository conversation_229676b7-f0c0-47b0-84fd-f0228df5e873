<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "products_cost".
 *
 * @property int $products_id
 * @property string $products_model
 * @property string $products_forex_status
 * @property string $products_original_cost
 * @property string $products_original_currency
 * @property string $products_cost
 * @property string $products_currency
 * @property string $products_cost_company_code
 * @property string $products_cost_company_name
 * @property string $products_last_modified
 */
class ProductsCost extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_cost';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'products_model', 'products_forex_status', 'products_original_cost', 'products_original_currency', 'products_cost', 'products_currency'], 'required'],
            [['products_id'], 'integer'],
            [['products_original_cost', 'products_cost'], 'number'],
            [['products_last_modified'], 'safe'],
            [['products_model', 'products_cost_company_name'], 'string', 'max' => 32],
            [['products_forex_status', 'products_original_currency', 'products_currency'], 'string', 'max' => 3],
            [['products_cost_company_code'], 'string', 'max' => 2],
            [['products_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'products_model' => 'Products Model',
            'products_forex_status' => 'Products Forex Status',
            'products_original_cost' => 'Products Original Cost',
            'products_original_currency' => 'Products Original Currency',
            'products_cost' => 'Products Cost',
            'products_currency' => 'Products Currency',
            'products_cost_company_code' => 'Products Cost Company Code',
            'products_cost_company_name' => 'Products Cost Company Name',
            'products_last_modified' => 'Products Last Modified',
        ];
    }
}
