<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "products_delivery_info".
 *
 * @property int $products_id
 * @property string $products_delivery_mode_id
 */
class ProductsDeliveryInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_delivery_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'products_delivery_mode_id'], 'required'],
            [['products_id', 'products_delivery_mode_id'], 'integer'],
            [['products_id', 'products_delivery_mode_id'], 'unique', 'targetAttribute' => ['products_id', 'products_delivery_mode_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'products_delivery_mode_id' => 'Products Delivery Mode ID',
        ];
    }
}
