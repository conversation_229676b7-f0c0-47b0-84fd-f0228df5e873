<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "products_description".
 *
 * @property int $products_id
 * @property int $language_id
 * @property string $products_name
 * @property string $products_alt_name
 * @property string $products_keyword
 * @property string $products_description
 * @property string $products_image
 * @property string $products_image_title
 * @property string $products_description_image
 * @property string $products_description_image_title
 * @property string $products_url
 * @property int $products_viewed
 * @property string $products_location
 * @property string $products_dtu_extra_info_1 DTU deno label
 * @property string $products_dtu_extra_info_2 DTU Deno calculation
 * @property string $products_dtu_extra_info_3 DTU deno label
 */
class ProductsDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_description';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['language_id'], 'required'],
            [['language_id', 'products_viewed'], 'integer'],
            [['products_description', 'products_location'], 'string'],
            [['products_name', 'products_alt_name', 'products_url'], 'string', 'max' => 255],
            [['products_keyword', 'products_image', 'products_image_title', 'products_description_image', 'products_description_image_title'], 'string', 'max' => 64],
            [['products_dtu_extra_info_1', 'products_dtu_extra_info_2', 'products_dtu_extra_info_3'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'language_id' => 'Language ID',
            'products_name' => 'Products Name',
            'products_alt_name' => 'Products Alt Name',
            'products_keyword' => 'Products Keyword',
            'products_description' => 'Products Description',
            'products_image' => 'Products Image',
            'products_image_title' => 'Products Image Title',
            'products_description_image' => 'Products Description Image',
            'products_description_image_title' => 'Products Description Image Title',
            'products_url' => 'Products Url',
            'products_viewed' => 'Products Viewed',
            'products_location' => 'Products Location',
            'products_dtu_extra_info_1' => 'Products Dtu Extra Info 1',
            'products_dtu_extra_info_2' => 'Products Dtu Extra Info 2',
            'products_dtu_extra_info_3' => 'Products Dtu Extra Info 3',
        ];
    }

    public function getProducts()
    {
        return $this->hasOne(Products::class, ['products_id' => 'products_id']);
    }
}
