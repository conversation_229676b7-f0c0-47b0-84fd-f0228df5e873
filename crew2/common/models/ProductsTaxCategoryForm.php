<?php

namespace common\models;

use offgamers\base\models\ProductsTaxCategory;
use \common\components\ExportCSV;
use yii\base\Model;
use Yii;

class ProductsTaxCategoryForm extends Model
{

    public $products_id;
    public $category;
    public $products_value;
    public $currency;
    public $created_at;
    public $created_by;
    public $updated_at;
    public $updated_by;
    public $isNewRecord;
    public $delete;
    public $product_name;


    public function rules()
    {
        return [
            [['category', 'products_value', 'currency'], 'required'],
            [['products_value'], 'number', 'min' => 0],
            [['products_id'], 'integer'],
            [['category'], 'string'],
        ];
    }

    const CATEGORY = ['MRV' => 'MRV'];

    public function getProductsTaxCategoryByPid($pid)
    {
        $model = new ProductsTaxCategory();
        $result = $model->find()->where(['products_id' => $pid])->asArray()->one();
        if (!empty($result)) {
            foreach ($result as $key => $value) {
                $this->$key = $value;
            }
            $this->isNewRecord = false;
        } else {
            $this->isNewRecord = true;
            $this->products_id = $pid;
        }
        $this->product_name = $this->getProductName($pid);

    }

    public function saveRecord()
    {
        $model = new ProductsTaxCategory();
        $result = $model->find()->where([
            'products_id' => $this->products_id,
        ])->asArray()->one();

        if (!empty($result)) {
            if ($this->delete) {
                $model = $model::findOne(['products_id' => $this->products_id]);
                $model->delete();
            } else {
                $model = $model::findOne(['products_id' => $this->products_id]);
                $model->category = $this->category;
                if (!($model->products_value == $this->products_value) or !($model->currency == $this->currency)) {
                    $model->products_value = $this->products_value;
                    $model->currency = $this->currency;
                    $model->updated_by = Yii::$app->user->identity->username;
                }
                $model->save();
            }
        } else {
            $model->products_id = $this->products_id;
            $model->category = $this->category;
            $model->products_value = $this->products_value;
            $model->currency = $this->currency;
            $model->created_by = Yii::$app->user->identity->username;
            $model->save();

        }

    }

    public function exportAllProducts()
    {
        $results = $this->getAllProductsTaxCategory();
        $file_name = date('YmdHis') . '_Products_Tax_Category_Report.csv';

        $result['header'] = [
            'products_id' => ['label' => 'Product ID', 'format' => ''],
            'product_name' => ['label' => 'Product Name', 'format' => ''],
            'category' => ['label' => 'Category', 'format' => ''],
            'currency' => ['label' => 'Currency', 'format' => ''],
            'products_value' => ['label' => 'Products Value', 'format' => ''],
        ];

        $result['data'] = $results;

        ExportCSV::download($result['data'], $result['header'], $file_name, ",");
        Yii::$app->end();
    }

    private function getProductName($products_id)
    {
        $model = new Products();
        $result = $model->find()->alias('p')
            ->select(['p.products_id', 'pd.products_name'])
            ->where(['p.products_id' => $products_id, 'pd.language_id' => 1])
            ->joinWith('productsDescription pd', false)
            ->asArray()
            ->one();
        return $result['products_name'];
    }

    private function getAllProductsTaxCategory()
    {
        $model = new ProductsTaxCategory();
        $queries = $model->find()->asArray()->all();

        $results = [];
        foreach ($queries as $result) {
            $result += ['product_name' => $this->getProductName($result["products_id"])];
            array_push($results, $result);
        }
        return $results;
    }


}