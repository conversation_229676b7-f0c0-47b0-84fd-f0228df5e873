<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "products_to_categories".
 *
 * @property int $products_id
 * @property int $categories_id
 * @property int $products_is_link
 */
class ProductsToCategories extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_to_categories';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'categories_id'], 'required'],
            [['products_id', 'categories_id', 'products_is_link'], 'integer'],
            [['products_id', 'categories_id'], 'unique', 'targetAttribute' => ['products_id', 'categories_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'categories_id' => 'Categories ID',
            'products_is_link' => 'Products Is Link',
        ];
    }

}
