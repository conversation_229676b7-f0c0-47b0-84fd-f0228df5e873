<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\Json;

/**
 * This is the model class for table "promo_content".
 *
 * @property int $id
 * @property string $title
 * @property string $page_title
 * @property int $start_time
 * @property int $end_time
 * @property string $html
 * @property string $css
 * @property string $javascript
 * @property string $json_data
 * @property int $status
 * @property string $modified_by
 * @property int $created_at
 * @property int $updated_at
 */
class PromoContent extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'promo_content';
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            TimestampBehavior::className(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'page_title'], 'filter', 'filter' => '\yii\helpers\HtmlPurifier::process'],
            [['title', 'page_title', 'start_time', 'end_time', 'html', 'css', 'javascript', 'json_data', 'status', 'modified_by'], 'required'],
            [['start_time', 'end_time', 'status', 'created_at', 'updated_at'], 'integer'],
            [['html', 'css', 'javascript', 'json_data'], 'string'],
            [['title', 'page_title', 'modified_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Title',
            'page_title' => 'Page Title',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'html' => 'Html',
            'css' => 'Css',
            'javascript' => 'Javascript',
            'json_data' => 'JSON Data',
            'status' => 'Status',
            'modified_by' => 'Modified By',
            'created_at' => 'Created At',
            'updated_at' => 'Modified At',
        ];
    }

    public function load($data, $formName = null)
    {
        if (!$formName) {
            $formName = $this->formName();
        }
        if (isset($data[$formName]['start_time'])) {
            $data[$formName]['start_time'] = strtotime($data[$formName]['start_time']);
        }
        if (isset($data[$formName]['end_time'])) {
            $data[$formName]['end_time'] = strtotime($data[$formName]['end_time']) + 59;
        }

        $ret = parent::load($data, $formName);
        $this->modified_by = Yii::$app->user->identity->username;

        if ($ret && $this->start_time >= $this->end_time) {
            Yii::$app->session->setFlash('error', 'End Date cannot same or earlier than Start Date');
            return false;
        }

        return $ret;
    }

    public function beforeSave($insert)
    {
        if ($this->title != 'DEFAULT') {
            $duplicate_record = self::find()
                ->select('id')
                ->where(['!=', 'title', 'DEFAULT'])
                ->andWhere(['!=', 'id', $this->id])
                ->andWhere([
                    'OR',
                    ['BETWEEN', 'start_time', $this->start_time, $this->end_time],
                    ['BETWEEN', 'end_time', $this->start_time, $this->end_time],
                    ['AND', ['>=', 'end_time', $this->end_time], ['<=', 'start_time', $this->start_time]],
                ])
                ->one();

            if ($duplicate_record) {
                Yii::$app->session->setFlash('error', 'Duplicate Event Found');
                return false;
            }
        }

        try {
            $data = JSON::decode($this->json_data);
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', 'Invalid Json Data');
            return false;
        }

        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        Yii::$app->frontend_cache->delete(self::tableName() . '/promotion');
        parent::afterSave($insert, $changedAttributes);
    }

}
