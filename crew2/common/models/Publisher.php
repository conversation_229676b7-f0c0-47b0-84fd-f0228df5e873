<?php

namespace common\models;

use backend\validators\SafeStringValidator;
use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\Url;

/**
 * This is the model class for table "publisher".
 *
 * @property int $publisher_id
 * @property string $title
 * @property string $profile
 * @property int $last_sync
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 *
 * @property PublisherSetting[] $publisherSettings
 */
class Publisher extends \yii\db\ActiveRecord
{

    const PUBLISHER_LIST = [
        'DTOne' => '\common\components\publishers\DTOne',
        'DTOneDVS' => '\common\components\publishers\DTOneDVS',
        'AcePayzDTU' => '\common\components\publishers\AcePayzDTU',
        'FuluDTU' => '\common\components\publishers\FuluDTU',
        'MintRouteDTU' => '\common\components\publishers\MintRouteDTU',
        'Xoxo' => '\common\components\publishers\Xoxo',
        'Tiki' => '\common\components\publishers\Tiki',
        'PrepaidForge' => '\common\components\publishers\PrepaidForge',
        'PlayCoin' => '\common\components\publishers\PlayCoin',
        'Boost' => '\common\components\publishers\Boost',
        'Devco' => '\common\components\publishers\Devco',
        'QwikCilver' => '\common\components\publishers\QwikCilver',
        'Bitnovo' => '\common\components\publishers\Bitnovo',
        'NetDragonDTU' => '\common\components\publishers\NetDragonDTU',
        'Astropay' => '\common\components\publishers\Astropay',
        'BlackHawkNetworkV2' => '\common\components\publishers\BlackHawkNetworkV2',
        'Bamboo' => '\common\components\publishers\Bamboo',
        'Eneba' => '\common\components\publishers\Eneba',
        'Wogi' => '\common\components\publishers\Wogi',
        'VTC' => '\common\components\publishers\VTC',
        'Razer' => '\common\components\publishers\RazerV2',
        'EcoVoucher' => '\common\components\publishers\EcoVoucher',
        'EZCards' => '\common\components\publishers\EZCards',
        'DTOneGC' => '\common\components\publishers\DTOneGC',
        'LikeCard' => '\common\components\publishers\LikeCard',
        'Aquipaga' => '\common\components\publishers\Aquipaga',
        'PRO' => '\common\components\publishers\Pro',
        'Neosurf' => '\common\components\publishers\Neosurf',
    ];

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publisher';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'profile'], 'required', 'on' => 'save'],
            ['title', SafeStringValidator::class],
            [['last_sync', 'status', 'created_at', 'updated_at'], 'integer'],
            [['title', 'profile'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publisher_id' => 'Publisher ID',
            'title' => 'Title',
            'profile' => 'Profile',
            'last_sync' => 'Last Sync',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPublisherSettings()
    {
        return $this->hasMany(PublisherSetting::className(), ['publisher_id' => 'publisher_id']);
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {

        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'publisher_id' => $this->publisher_id,
            'status' => $this->status
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'profile', $this->profile]);

        return $dataProvider;
    }

    public function getPublisherClass($profile)
    {
        return self::PUBLISHER_LIST[$profile] ?? null;
    }

    public function createUrl($action, $model, $key, $index)
    {
        $params = is_array($key) ? $key : ['id' => (string)$key];
        $params[0] = $action;
        return Url::toRoute($params);
    }

    public function renderActionColumn($template, $buttons, $model, $key, $index)
    {
        return preg_replace_callback('/\\{([\w\-\/]+)\\}/', function ($matches) use ($model, $key, $index, $buttons) {
            $name = $matches[1];

            if (isset($buttons[$name])) {
                $url = $this->createUrl($name, $model, $key, $index);
                return call_user_func($buttons[$name], $url, $model, $key);
            }

            return '';
        }, $template);
    }
}
