<?php

namespace common\models;

use Yii;


/**
 * This is the model class for table "publisher_product".
 *
 * @property int $publisher_product_id
 * @property int $publisher_id
 * @property string $name
 * @property string $description
 * @property float|null $cost_price
 * @property string $cost_currency
 * @property string $sku
 * @property string $denomination
 * @property string $attribute_1
 * @property string $attribute_2
 * @property string $attribute_3
 * @property int $created_at
 * @property int $updated_at
 */
class PublisherProduct extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publisher_product';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publisher_id', 'name', 'sku', 'raw'], 'required'],
            [['publisher_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['cost_price'], 'number'],
            [['name', 'sku', 'denomination', 'attribute_1', 'attribute_2', 'attribute_3'], 'string', 'max' => 255],
            [['description', 'raw'], 'string'],
            [['cost_currency'], 'string', 'length' => 3],
            [['denomination', 'attribute_1', 'attribute_2', 'attribute_3'], 'default', 'value' => '']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publisher_product_id' => 'Publisher Product ID',
            'publisher_id' => 'Publisher ID',
            'name' => 'Name',
            'description' => 'Description',
            'cost_price' => 'Cost Price',
            'cost_currency' => 'Cost Currency',
            'status' => 'Status',
            'sku' => 'Sku',
            'denomination' => 'Denomination',
            'attribute_1' => 'Attribute 1',
            'attribute_2' => 'Attribute 2',
            'attribute_3' => 'Attribute 3',
            'raw' => 'Raw',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getArrayData(): array
    {
        $return_data = [];

        $attributes = $this->attributeLabels();
        unset($attributes['publisher_product_id']);
        $attributes = array_keys($attributes);

        foreach ($attributes as $attribute){
            $return_data[$attribute] = $this->$attribute;
        }

        return $return_data;
    }
}
