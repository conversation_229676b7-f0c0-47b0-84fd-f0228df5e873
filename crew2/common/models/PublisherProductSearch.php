<?php

namespace common\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\PublisherProduct;

/**
 * PublisherProductSearch represents the model behind the search form of `common\models\PublisherProduct`.
 */
class PublisherProductSearch extends PublisherProduct
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publisher_product_id', 'publisher_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['name', 'description', 'cost_currency', 'sku', 'denomination', 'attribute_1', 'attribute_2', 'attribute_3', 'raw'], 'safe'],
            [['cost_price'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = PublisherProduct::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'publisher_product_id' => $this->publisher_product_id,
            'publisher_id' => $this->publisher_id,
            'cost_price' => $this->cost_price,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'cost_currency', $this->cost_currency])
            ->andFilterWhere(['like', 'sku', $this->sku])
            ->andFilterWhere(['like', 'denomination', $this->denomination])
            ->andFilterWhere(['like', 'attribute_1', $this->attribute_1])
            ->andFilterWhere(['like', 'attribute_2', $this->attribute_2])
            ->andFilterWhere(['like', 'attribute_3', $this->attribute_3])
            ->andFilterWhere(['like', 'raw', $this->raw]);

        return $dataProvider;
    }
}
