<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "publisher_setting".
 *
 * @property int $publisher_setting_id
 * @property int $publisher_id
 * @property string $key
 * @property string $value
 *
 * @property Publisher $publisher
 */
class PublisherSetting extends \yii\db\ActiveRecord
{
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publisher_setting';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publisher_id', 'key'], 'required'],
            [['publisher_id'], 'integer'],
            [['key'], 'string', 'max' => 255],
            [['value'], 'string', 'max' => 2048],
            [['publisher_id'], 'exist', 'skipOnError' => true, 'targetClass' => Publisher::className(), 'targetAttribute' => ['publisher_id' => 'publisher_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publisher_setting_id' => 'Publisher Setting ID',
            'publisher_id' => 'Publisher ID',
            'key' => 'Key',
            'value' => 'Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPublisher()
    {
        return $this->hasOne(Publisher::className(), ['publisher_id' => 'publisher_id']);
    }
}
