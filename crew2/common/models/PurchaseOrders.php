<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "purchase_orders".
 *
 * @property string $purchase_orders_id
 * @property string $purchase_orders_ref_id
 * @property int $supplier_id
 * @property string $supplier_name
 * @property string $supplier_company
 * @property string $supplier_street_address
 * @property string $supplier_suburb
 * @property string $supplier_city
 * @property string $supplier_postcode
 * @property string $supplier_state
 * @property string $supplier_country
 * @property string $supplier_telephone_country
 * @property string $supplier_country_international_dialing_code
 * @property string $supplier_telephone
 * @property string $supplier_email_address
 * @property int $supplier_address_format_id
 * @property string $delivery_location
 * @property string $delivery_name
 * @property string $delivery_company
 * @property string $delivery_street_address
 * @property string $delivery_suburb
 * @property string $delivery_city
 * @property string $delivery_postcode
 * @property string $delivery_state
 * @property string $delivery_country
 * @property int $delivery_address_format_id
 * @property string $payment_type
 * @property int $payment_term
 * @property int $payment_days_pay_wsc
 * @property int $store_payment_account_book_id
 * @property string $purchase_orders_contact_info
 * @property string $purchase_orders_issue_date
 * @property int $purchase_orders_status
 * @property int $purchase_orders_type
 * @property string $purchase_orders_date_finished
 * @property string $purchase_orders_paid_amount
 * @property string $purchase_orders_paid_currency
 * @property string $purchase_orders_bankcharges_included
 * @property string $purchase_orders_bankcharges_refunded
 * @property string $purchase_orders_gst_currency
 * @property string $purchase_orders_gst_value
 * @property string $purchase_orders_gst_amount
 * @property string $purchase_orders_gst_refunded
 * @property string $currency
 * @property string $suggested_currency_value
 * @property string $confirmed_currency_value
 * @property string $currency_usd_value
 * @property string $purchase_orders_tag_ids
 * @property string $last_modified
 * @property int $purchase_orders_last_printed_by
 * @property string $purchase_orders_last_printed_from_ip
 * @property string $purchase_orders_last_printed
 * @property int $purchase_orders_locked_by
 * @property string $purchase_orders_locked_from_ip
 * @property string $purchase_orders_locked_datetime
 * @property int $purchase_orders_paid_status
 * @property int $purchase_orders_billing_status
 * @property int $purchase_orders_verify_mode
 */
class PurchaseOrders extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'purchase_orders';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['supplier_id', 'supplier_address_format_id', 'delivery_address_format_id', 'payment_term', 'payment_days_pay_wsc', 'store_payment_account_book_id', 'purchase_orders_status', 'purchase_orders_type', 'purchase_orders_last_printed_by', 'purchase_orders_locked_by', 'purchase_orders_paid_status', 'purchase_orders_billing_status', 'purchase_orders_verify_mode'], 'integer'],
            [['purchase_orders_issue_date', 'purchase_orders_date_finished', 'last_modified', 'purchase_orders_last_printed', 'purchase_orders_locked_datetime'], 'safe'],
            [['purchase_orders_paid_amount', 'purchase_orders_bankcharges_included', 'purchase_orders_bankcharges_refunded', 'purchase_orders_gst_value', 'purchase_orders_gst_amount', 'purchase_orders_gst_refunded', 'suggested_currency_value', 'confirmed_currency_value', 'currency_usd_value'], 'number'],
            [['purchase_orders_ref_id', 'supplier_company', 'supplier_suburb', 'supplier_city', 'supplier_state', 'supplier_telephone', 'delivery_company', 'delivery_suburb', 'delivery_city', 'delivery_state'], 'string', 'max' => 32],
            [['supplier_name', 'supplier_street_address', 'supplier_country', 'supplier_telephone_country', 'delivery_name', 'delivery_street_address', 'delivery_country', 'purchase_orders_contact_info'], 'string', 'max' => 64],
            [['supplier_postcode', 'delivery_postcode'], 'string', 'max' => 10],
            [['supplier_country_international_dialing_code'], 'string', 'max' => 5],
            [['supplier_email_address'], 'string', 'max' => 96],
            [['delivery_location'], 'string', 'max' => 2],
            [['payment_type'], 'string', 'max' => 1],
            [['purchase_orders_paid_currency', 'purchase_orders_gst_currency', 'currency'], 'string', 'max' => 3],
            [['purchase_orders_tag_ids'], 'string', 'max' => 255],
            [['purchase_orders_last_printed_from_ip', 'purchase_orders_locked_from_ip'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'purchase_orders_id' => 'Purchase Orders ID',
            'purchase_orders_ref_id' => 'Purchase Orders Ref ID',
            'supplier_id' => 'Supplier ID',
            'supplier_name' => 'Supplier Name',
            'supplier_company' => 'Supplier Company',
            'supplier_street_address' => 'Supplier Street Address',
            'supplier_suburb' => 'Supplier Suburb',
            'supplier_city' => 'Supplier City',
            'supplier_postcode' => 'Supplier Postcode',
            'supplier_state' => 'Supplier State',
            'supplier_country' => 'Supplier Country',
            'supplier_telephone_country' => 'Supplier Telephone Country',
            'supplier_country_international_dialing_code' => 'Supplier Country International Dialing Code',
            'supplier_telephone' => 'Supplier Telephone',
            'supplier_email_address' => 'Supplier Email Address',
            'supplier_address_format_id' => 'Supplier Address Format ID',
            'delivery_location' => 'Delivery Location',
            'delivery_name' => 'Delivery Name',
            'delivery_company' => 'Delivery Company',
            'delivery_street_address' => 'Delivery Street Address',
            'delivery_suburb' => 'Delivery Suburb',
            'delivery_city' => 'Delivery City',
            'delivery_postcode' => 'Delivery Postcode',
            'delivery_state' => 'Delivery State',
            'delivery_country' => 'Delivery Country',
            'delivery_address_format_id' => 'Delivery Address Format ID',
            'payment_type' => 'Payment Type',
            'payment_term' => 'Payment Term',
            'payment_days_pay_wsc' => 'Payment Days Pay Wsc',
            'store_payment_account_book_id' => 'Store Payment Account Book ID',
            'purchase_orders_contact_info' => 'Purchase Orders Contact Info',
            'purchase_orders_issue_date' => 'Purchase Orders Issue Date',
            'purchase_orders_status' => 'Purchase Orders Status',
            'purchase_orders_type' => 'Purchase Orders Type',
            'purchase_orders_date_finished' => 'Purchase Orders Date Finished',
            'purchase_orders_paid_amount' => 'Purchase Orders Paid Amount',
            'purchase_orders_paid_currency' => 'Purchase Orders Paid Currency',
            'purchase_orders_bankcharges_included' => 'Purchase Orders Bankcharges Included',
            'purchase_orders_bankcharges_refunded' => 'Purchase Orders Bankcharges Refunded',
            'purchase_orders_gst_currency' => 'Purchase Orders Gst Currency',
            'purchase_orders_gst_value' => 'Purchase Orders Gst Value',
            'purchase_orders_gst_amount' => 'Purchase Orders Gst Amount',
            'purchase_orders_gst_refunded' => 'Purchase Orders Gst Refunded',
            'currency' => 'Currency',
            'suggested_currency_value' => 'Suggested Currency Value',
            'confirmed_currency_value' => 'Confirmed Currency Value',
            'currency_usd_value' => 'Currency Usd Value',
            'purchase_orders_tag_ids' => 'Purchase Orders Tag Ids',
            'last_modified' => 'Last Modified',
            'purchase_orders_last_printed_by' => 'Purchase Orders Last Printed By',
            'purchase_orders_last_printed_from_ip' => 'Purchase Orders Last Printed From Ip',
            'purchase_orders_last_printed' => 'Purchase Orders Last Printed',
            'purchase_orders_locked_by' => 'Purchase Orders Locked By',
            'purchase_orders_locked_from_ip' => 'Purchase Orders Locked From Ip',
            'purchase_orders_locked_datetime' => 'Purchase Orders Locked Datetime',
            'purchase_orders_paid_status' => 'Purchase Orders Paid Status',
            'purchase_orders_billing_status' => 'Purchase Orders Billing Status',
            'purchase_orders_verify_mode' => 'Purchase Orders Verify Mode',
        ];
    }
}
