<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "purchase_orders_status_history".
 *
 * @property string $purchase_orders_status_history_id
 * @property string $purchase_orders_id
 * @property int $purchase_orders_status_id
 * @property string $date_added
 * @property string $comments
 * @property int $comments_type
 * @property int $set_as_po_remarks
 * @property int $supplier_notified
 * @property string $changed_by
 */
class PurchaseOrdersStatusHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'purchase_orders_status_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['purchase_orders_id', 'purchase_orders_status_id', 'comments_type', 'set_as_po_remarks', 'supplier_notified'], 'integer'],
            [['date_added'], 'safe'],
            [['comments'], 'required'],
            [['comments'], 'string'],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'purchase_orders_status_history_id' => 'Purchase Orders Status History ID',
            'purchase_orders_id' => 'Purchase Orders ID',
            'purchase_orders_status_id' => 'Purchase Orders Status ID',
            'date_added' => 'Date Added',
            'comments' => 'Comments',
            'comments_type' => 'Comments Type',
            'set_as_po_remarks' => 'Set As Po Remarks',
            'supplier_notified' => 'Supplier Notified',
            'changed_by' => 'Changed By',
        ];
    }
}
