<?php

namespace common\models;

use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\Url;

/**
 * This is the model class for table "reseller".
 *
 * @property int $reseller_id
 * @property string $title
 * @property string $profile
 * @property int $last_sync
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class Reseller extends \yii\db\ActiveRecord
{
    const RESELLER_LIST = [
        'G2G' => '\common\models\resellers\G2G'
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'reseller';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'profile', 'status'], 'required', 'on' => 'save'],
            [['last_sync', 'status', 'created_at', 'updated_at'], 'integer'],
            [['title', 'profile'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'reseller_id' => 'Reseller ID',
            'title' => 'Title',
            'profile' => 'Profile',
            'last_sync' => 'Last Sync',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'reseller_id' => $this->reseller_id,
            'status' => $this->status
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'profile', $this->profile]);

        return $dataProvider;
    }

    public function createUrl($action, $model, $key, $index)
    {
        $params = is_array($key) ? $key : ['id' => (string)$key];
        $params[0] = $action;
        return Url::toRoute($params);
    }

    public function renderActionColumn($template, $buttons, $model, $key, $index)
    {
        return preg_replace_callback('/\\{([\w\-\/]+)\\}/', function ($matches) use ($model, $key, $index, $buttons) {
            $name = $matches[1];

            if (isset($buttons[$name])) {
                $url = $this->createUrl($name, $model, $key, $index);
                return call_user_func($buttons[$name], $url, $model, $key);
            }

            return '';
        }, $template);
    }
}
