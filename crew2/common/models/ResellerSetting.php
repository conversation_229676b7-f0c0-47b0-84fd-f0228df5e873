<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "reseller_setting".
 *
 * @property int $reseller_setting_id
 * @property int $reseller_id
 * @property string $key
 * @property string $value
 */
class ResellerSetting extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'reseller_setting';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['reseller_id', 'key', 'value'], 'required'],
            [['reseller_id'], 'integer'],
            [['key', 'value'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'reseller_setting_id' => 'Reseller Setting ID',
            'reseller_id' => 'Reseller ID',
            'key' => 'Key',
            'value' => 'Value',
        ];
    }
}
