<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "reseller_upgrade".
 *
 * @property int $id
 * @property int $customer_id
 * @property int $flag
 * @property int $reseller_since
 * @property int $last_process
 * @property int $created_at
 * @property int $updated_at
 */
class ResellerUpgrade extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'reseller_upgrade';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            TimestampBehavior::className(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_id', 'flag', 'reseller_since'], 'required'],
            [['customer_id', 'flag', 'reseller_since', 'last_process', 'created_at', 'updated_at'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customer_id' => 'Customer ID',
            'flag' => 'Flag',
            'reseller_since' => 'Reseller Since',
            'last_process' => 'Last Process',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
