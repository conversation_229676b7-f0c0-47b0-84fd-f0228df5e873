<?php

namespace common\models;

use DateTime;
use offgamers\base\models\ms\Order;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;

class ResellerUpgradeForm extends \yii\base\Model
{
    public $customer_group_id;
    public $downgrade_customer_group_id;
    public $year;
    public $month;
    public $timestamp;
    public $downgrade_excluded_customers_group;
    public $excluded_customers_id;

    public function setData($date, $customer_group_id, $downgrade_customer_group, $downgrade_excluded_customers_group, $excluded_customers_id)
    {
        $this->setDate($date);
        $this->setProcessCustomerGroup($customer_group_id);
        $this->setDowngradeCustomerGroup($downgrade_customer_group);
        $this->setExcludeCustomerGroup($downgrade_excluded_customers_group);
        $this->setExcludeCustomerId($excluded_customers_id);
    }

    public function setDate($date)
    {
        if (is_null($date)) {
            $date = date('Y/n');
        }
        list($year, $month) = explode("/", $date);
        $d = new DateTime;
        $d->setDate($year, $month, 1)->setTime(0, 0);
        if (($d && $d->getTimestamp() <= time() && $d->format('Y/n')) == false) {
            throw new InvalidArgumentException('Invalid Date');
        }
        $this->timestamp = $d->getTimestamp();
    }

    public function setProcessCustomerGroup($customer_group_id)
    {
        $this->customer_group_id = (is_null($customer_group_id) ? [6] : explode(',', $customer_group_id));
        foreach ($this->customer_group_id as &$group_id) {
            $group_id = (int)$group_id;
            if (!is_integer($group_id)) {
                throw new InvalidArgumentException('Invalid Reseller Group ID');
            }
        }
    }

    public function setExcludeCustomerGroup($customer_group_id)
    {
        $this->downgrade_excluded_customers_group = (is_null($customer_group_id) ? [] : explode(',', $customer_group_id));
        foreach ($this->customer_group_id as &$group_id) {
            $group_id = (int)$group_id;
            if (!is_integer($group_id)) {
                throw new InvalidArgumentException('Invalid Excluded Reseller Group ID');
            }
        }
    }

    public function setExcludeCustomerId($customers_id)
    {
        $this->excluded_customers_id = (is_null($customers_id) ? [] : explode(',', $customers_id));
        foreach ($this->excluded_customers_id as &$cust_id) {
            $cust_id = (int)$cust_id;
            if (!is_integer($cust_id)) {
                throw new InvalidArgumentException('Invalid Excluded Customers Id');
            }
        }
    }

    public function setDowngradeCustomerGroup($downgrade_customers_groups_id)
    {
        # Set as silver parameter not passed
        $this->downgrade_customer_group_id = (int)($downgrade_customers_groups_id ?? 3);
        if (!is_integer($this->downgrade_customer_group_id)) {
            throw new InvalidArgumentException('Invalid Downgrade Customer Group Id');
        }
    }

    public function getResellerList()
    {
        $reseller_list = Customers::find()
            ->select(['customers_id', 'customers_groups_id', 'customers_flag', 'customers_email_address', 'customers_country_dialing_code_id', 'customers_telephone'])
            ->where(['customers_status' => 1])
            ->andWhere(['customers_groups_id' => $this->customer_group_id])
            ->all();

        return $reseller_list;
    }

    public function process()
    {
        $reseller_list = $this->getResellerList();
        $reseller_info = [];

        foreach ($reseller_list as $reseller) {
            /* @var $reseller Customers */
            $customer_id = $reseller->customers_id;
            $customer_flag = (empty($reseller->customers_flag) ? [] : explode(',', $reseller->customers_flag));
            $profile = ResellerUpgrade::findOne(['customer_id' => $customer_id]);
            $request_fail = false;
            $excluded_for_downgrade = (in_array($reseller->customers_groups_id, $this->downgrade_excluded_customers_group) || in_array($reseller->customers_id, $this->excluded_customers_id));
            $customer_remarks = [];

            if (!$profile) {
                $profile = new ResellerUpgrade();
                $profile->load(
                    [
                        'customer_id' => $customer_id,
                        'reseller_since' => Customers::getCustomerGroupLastChange($customer_id, $reseller->customers_groups_id),
                        'flag' => 0
                    ], '');
            }

            // Get total delivered amount from orders ms
            try {
                $order_ms = (new Order);
                $order_ms->setRetries(3);
                $total_delivery_last_month = $order_ms->customRequest('order', 'get-total-order-amount', [
                    'customers_id' => $customer_id,
                    'start_time' => strtotime("first day of previous month today", $this->timestamp),
                    'end_time' => strtotime("last day of previous month 23:59:59", $this->timestamp)
                ]);
            } catch (\Exception $e) {
                $total_delivery_last_month = 0;
                $request_fail = true;
            }

            $reseller_data = [
                $reseller->customers_id,
                $reseller->customers_email_address,
                $reseller->getCustomerPhoneNumber(),
                CustomersGroups::getCustomerGroupDescriptionById($reseller->customers_groups_id),
                (in_array('2', $customer_flag) ? 'Y' : 'N'),
                date('d/m/Y', $profile->reseller_since),
                round($total_delivery_last_month, 2),
                ($request_fail == true ? 'Failed to get total order amount' : ''),
                $profile->flag
            ];

            // Ignore Reseller less one month
            $numbers_of_days = date('t', $this->timestamp);
            if ($profile->reseller_since > $this->timestamp - $numbers_of_days * 86400 || $request_fail) {
                $reseller_info[] = $reseller_data;
                continue;
            }


            // If last process date is later than current process month, Ignore Record
            if (is_null($profile->last_process) != true && date('n', $profile->last_process) >= date('n', $this->timestamp) &&
                date('Y', $profile->last_process) >= date('Y', $this->timestamp)) {
                $reseller_info[] = $reseller_data;
                continue;
            }

            if ($total_delivery_last_month >= 1000) {
                $profile->flag = ($profile->flag > -1 ? $profile->flag + 1 : 1);
                if ($profile->flag < 4) {
                    // Remove NRP Flag when count > 4
                    if ($profile->flag >= 4) {
                        if ($pos = array_search('2', $customer_flag)) {
                            unset($customer_flag[$pos]);
                            $reseller_data[4] = 'N';
                            $reseller_data[7] = 'Allow Reversible Payment';
                            $customer_remarks[] = $reseller->getCustomerFlagChangeMsg(2, false);
                            if(count($customer_flag) >= 1){
                                $reseller->customers_flag = implode(',', $customer_flag);
                            }
                            else{
                                $reseller->customers_flag = '';
                                $customer_remarks[] = $reseller->getCustomerFlagChangeMsg(1, false);
                            }
                        }
                    }
                }
            } else {
                $profile->flag = ($profile->flag >= 0 ? -1 : $profile->flag - 1);
                if ($profile->flag <= -2 && $excluded_for_downgrade === false) {
                    // Add Flag & NRP Flag to Customer Account
                    // 17/09/2020 Remove Flag NRP during downgrade as per requested by Alfred
//                    if (in_array('1', $customer_flag) == false) {
//                        $customer_remarks[] = $reseller->getCustomerFlagChangeMsg(1, true);
//                        $customer_flag[] = '1';
//                    }
//                    if (in_array('2', $customer_flag) == false) {
//                        $customer_remarks[] = $reseller->getCustomerFlagChangeMsg(2, true);
//                        $customer_flag[] = '2';
//                        $reseller_data[4] = 'Y';
//                    }
                    $reseller->customers_flag = implode(',', $customer_flag);
                    //Downgrade account to silver
                    $reseller_data[7] = 'Downgrade to Silver';
                    $customer_remarks[] = $reseller->getCustomerGroupChangeMsg($reseller->customers_groups_id, $this->downgrade_customer_group_id);
                    $reseller->customers_groups_id = $this->downgrade_customer_group_id;
                    $reseller_data[3] = CustomersGroups::getCustomerGroupDescriptionById($reseller->customers_groups_id);
                }
            }

            if ($profile->flag <= -2 && $excluded_for_downgrade === false) {
                // Delete from table when reseller downgrade to normal customer
                $profile->delete();
            } else {
                $profile->last_process = $this->timestamp;
                $profile->save();
            }
            $reseller_data['8'] = $profile->flag;
            $reseller_info[] = $reseller_data;

            $reseller->save();

            if ($customer_remarks) {
                $reseller->updateCustomerRemarkHistory($customer_id, $customer_remarks);
            }
        }

        $this->deleteExpiredData();

        $this->sendEmail($reseller_info);

    }

    private function sendEmail($reseller_info)
    {
        $csvHeader = ['Customer ID', 'Customer Email', 'Mobile Number', 'Customer Group', 'NRP Flag', 'Upgrade Date', 'Total Order Amount (USD)', 'Remarks', 'Flag'];

        $emailCSVObj = new \common\components\ErrorReportCom();

        $emailCSVObj->sendCsvMail(
            $reseller_info,
            $csvHeader,
            'Reseller Upgrade Report ' . date('d/m/Y', $this->timestamp),
            Yii::$app->params['email.resellerUpgrade'],
            'Reseller Upgrade Report ' . date('d/m/Y', $this->timestamp),
            Yii::$app->params["noreply"]["default"]
        );
    }

    private function deleteExpiredData()
    {
        $not_updated_reseller_list = ResellerUpgrade::find()->select('customer_id')->where(['<', 'last_process', $this->timestamp])->asArray()->all();
        $not_updated_reseller_list = ArrayHelper::getColumn($not_updated_reseller_list, 'customer_id');

        $validate_status = Customers::find()
            ->select('customers_id')
            ->where(['customers_id' => $not_updated_reseller_list])
            ->andWhere(['NOT IN', 'customers_groups_id', $this->customer_group_id])
            ->asArray()
            ->all();

        ResellerUpgrade::deleteAll(['customer_id' => ArrayHelper::getColumn($validate_status, 'customers_id')]);
    }

}