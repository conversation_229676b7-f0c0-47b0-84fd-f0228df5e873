<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "search_keywords".
 *
 * @property string $tpl_id
 * @property string $id
 * @property int $id_type `0: Category, 2: Game, 3: Product`
 * @property string $language_id
 * @property string $search_value
 */
class SearchKeywords extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'search_keywords';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tpl_id', 'id', 'id_type', 'language_id', 'search_value'], 'required'],
            [['tpl_id', 'id', 'id_type', 'language_id'], 'integer'],
            [['search_value'], 'string'],
            [['id', 'id_type', 'language_id'], 'unique', 'targetAttribute' => ['id', 'id_type', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'tpl_id' => 'Tpl ID',
            'id' => 'ID',
            'id_type' => 'Id Type',
            'language_id' => 'Language ID',
            'search_value' => 'Search Value',
        ];
    }

    public static function getMetaKeyword($id, $id_type, $language_id = false, $default = false)
    {
        $query = SearchKeywords::find()
            ->select(['language_id','search_value'])
            ->where(['id_type' => $id_type, 'id' => $id])
            ->asArray();

        if ($language_id !== false) {
            $query->andWhere(['language_id' => $language_id]);
        }

        $output = $query->all();

        if ($default && (empty($output) || empty($output[0]['search_value'])) && $language_id != 1) {
            $output = self::getMetaKeyword($id, 1);
        }

        return $output;
    }
}
