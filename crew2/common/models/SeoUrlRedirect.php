<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "seo_url_redirect".
 *
 * @property int $seo_url_redirect_id
 * @property string $old_url
 * @property string $new_url
 * @property int $redirect_type
 * @property int $created_at
 * @property int $updated_at
 */
class SeoUrlRedirect extends ActiveRecord
{
    private const REDIRECTION_TYPES = [
        301 => 'Permanent',
        302 => 'Temporary',
    ];

    private const CACHE_KEY = "seo_url_redirect/";

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'seo_url_redirect';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db');
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            ['class' => TimestampBehavior::class]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'seo_url_redirect_id' => 'SEO URL Redirect ID',
            'old_url' => 'Old Url',
            'new_url' => 'New Url',
            'redirect_type' => 'Redirect Type',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['old_url', 'new_url'], 'required'],
            ['redirect_type', 'required', 'message' => 'Please select redirection type'],
            [['created_at', 'updated_at'], 'integer'],
            [['old_url', 'new_url'], 'trim'],
            [['old_url', 'new_url'], 'filter', 'filter' => function ($value) {
                return ($value != '/') ? $this->encodeUrlBeforeSave($value) : $value;
            }],
            [['old_url', 'new_url'], 'string', 'max' => 1000],
            ['old_url', 'unique', 'targetAttribute' => ['old_url'], 'message' => 'This url record exists'],
            [['old_url', 'new_url'], function ($attribute) {
                if (!preg_match_all("/^(?!www\.|(?:http)s?:\/\/|[A-Za-z]:\\|\/\/).*/m", $this->$attribute)) {
                    $this->addError($attribute, 'Url must be a relative path. e.g path/to/site');
                }
            }],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function afterSave($insert, $changedAttributes)
    {
        $oldAttributeOldUrl = $changedAttributes['old_url'] ?? null;

        if (!$insert
            && !is_null($oldAttributeOldUrl)
            && (strcmp($oldAttributeOldUrl, $this->old_url) !== 0)) {
            $this->removeCache($oldAttributeOldUrl);
        }

        $this->removeCache($this->old_url);

        parent::afterSave($insert, $changedAttributes);
    }

    /**
     * {@inheritdoc}
     */
    public function afterDelete()
    {
        $this->removeCache($this->old_url);

        parent::afterDelete();
    }

    public function getRedirectionTypes(): array
    {
        return self::REDIRECTION_TYPES;
    }

    private function encodeUrlBeforeSave(string $url): string
    {
        $url = trim($url,'/');

        $url = rawurldecode($url);

        return mb_convert_encoding($url, "UTF-8");
    }

    private function getFrontEndCache()
    {
        return Yii::$app->frontend_cache;
    }

    private function getCacheKey(string $oldUrl): string
    {
        return sprintf("%s%s", self::CACHE_KEY, $oldUrl);
    }

    private function removeCache(string $url): void
    {
        $key = $this->getCacheKey($url);

        $cache = $this->getFrontEndCache();

        $cache->delete($key);
    }

}