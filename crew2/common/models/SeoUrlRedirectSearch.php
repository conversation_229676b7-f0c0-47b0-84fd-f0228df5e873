<?php

namespace common\models;

use yii\data\ActiveDataProvider;

class SeoUrlRedirectSearch extends SeoUrlRedirect
{

    public function rules()
    {
        return [
            ['redirect_type', 'integer'],
            [['old_url', 'new_url'], 'safe'],
        ];
    }

    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        $query->andFilterWhere([
            'redirect_type' => $this->redirect_type
        ]);

        $query->andFilterWhere(['like', 'old_url', $this->old_url])
            ->andFilterWhere(['like', 'new_url', $this->new_url]);

        return $dataProvider;
    }

}