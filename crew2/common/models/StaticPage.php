<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "static_page".
 *
 * @property int $static_page_id
 * @property string $title
 * @property int $static_page_type_id
 * @property int $created_at
 * @property int $updated_at
 *
 * @property StaticPageContent[] $staticPageContents
 */
class StaticPage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'static_page';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['static_page_type_id', 'title'], 'required'],
            [['created_at', 'updated_at'], 'integer'],
            [['title'], 'string', 'max' => 255],
            [['static_page_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => StaticPageType::className(), 'targetAttribute' => ['static_page_type_id' => 'static_page_type_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'static_page_id' => 'Static Page ID',
            'title' => 'Title',
            'static_page_type_id' => 'Static Page Type ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getStaticPageContents()
    {
        return $this->hasMany(StaticPageContent::className(), ['static_page_id' => 'static_page_id']);
    }

    public function getStaticPageType()
    {
        return $this->hasOne(StaticPageType::className(), ['static_page_type_id' => 'static_page_type_id']);
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);

        $historyModel = new \common\models\StaticPageContentHistory;
        $historyModel->static_page_id = $this->static_page_id;
        $historyModel->changed_by = Yii::$app->user->identity->username;
        $historyModel->save();
    }

    public function beforeDelete()
    {
        if (!parent::beforeDelete()) {
            return false;
        }

        $historyModel = new \common\models\StaticPageContentHistory;
        $historyModel->static_page_id = $this->static_page_id;
        $historyModel->changed_by = Yii::$app->user->identity->username;
        $historyModel->save();
        return true;
    }

    public function getLastEdit($staticPageId)
    {
        $historyModel = \common\models\StaticPageContentHistory::find(['static_page_id' => $staticPageId])->orderBy('created_at DESC')->one();
        return ['date' => date('Y-m-d H:i:s', $historyModel->created_at), 'by' => $historyModel->changed_by];
    }
}
