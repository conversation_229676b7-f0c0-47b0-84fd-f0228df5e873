<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "static_page_content".
 *
 * @property int $static_page_contentid
 * @property int $static_page_id
 * @property int $language_id
 * @property string $title
 * @property string $content
 *
 * @property StaticPage $staticPage
 */
class StaticPageContent extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'static_page_content';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['static_page_id', 'language_id'], 'integer'],
            [['title'], 'string', 'max' => 255],
            [['content'], 'string'],
            [['static_page_id'], 'exist', 'skipOnError' => true, 'targetClass' => StaticPage::className(), 'targetAttribute' => ['static_page_id' => 'static_page_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'static_page_contentid' => 'Static Page Contentid',
            'static_page_id' => 'Static Page ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'content' => 'Content',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getStaticPage()
    {
        return $this->hasOne(StaticPage::className(), ['static_page_id' => 'static_page_id']);
    }
}
