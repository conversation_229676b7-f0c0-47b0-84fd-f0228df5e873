<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "static_page_type".
 *
 * @property int $static_page_type_id
 * @property string $type_title
 * @property string $type_code
 * @property int $type_status
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 *
 * @property StaticPage[] $staticPages
 */
class StaticPageType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'static_page_type';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type_title', 'type_code'], 'required'],
            [['type_status', 'created_at', 'updated_at'], 'integer'],
            [['type_title', 'changed_by'], 'string', 'max' => 255],
            [['type_code'], 'string', 'max' => 5],
            [['type_code'], 'unique'],
            [['changed_by'], 'default', 'value'=> Yii::$app->user->identity->username],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'static_page_type_id' => 'Static Page Type ID',
            'type_title' => 'Type Title',
            'type_code' => 'Type Code',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'changed_by' => 'Changed By',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getStaticPages()
    {
        return $this->hasMany(StaticPage::className(), ['static_page_type_id' => 'static_page_type_id']);
    }

    public function beforeDelete()
    {
        if (!parent::beforeDelete()) {
            return false;
        }

        if (\common\models\StaticPage::findOne(['static_page_type_id' => $this->static_page_type_id])) {
            return false;
        }
        
        return true;
    }
}
