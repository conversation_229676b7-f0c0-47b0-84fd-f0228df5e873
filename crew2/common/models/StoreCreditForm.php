<?php

namespace common\models;

use Yii;
use yii\helpers\Json;

class StoreCreditForm extends \yii\base\Model {

    public $transaction_type;
    public $currency;
    public $amount;
    public $customers_id;
    public $customers_email;
    public $comment;
    public $convert_currency;
    public $email_subject,$email_message;
    public $requesting_id, $requesting_role, $show_customer, $allow_negative;
    public $_trans_opt = ["ADD_CREDIT" => "Add Credit", "DEDUCT_CREDIT" => "Deduct Credit", "CONVERT_CREDIT" => "Convert Currency"];
    public $_trans_type = ["ADD_CREDIT" => "add", "DEDUCT_CREDIT" => "deduct", "CONVERT_CREDIT" => "convert"];
    public $_activity = ["ADD_CREDIT" => "MI", "DEDUCT_CREDIT" => "MR", "CONVERT_CREDIT" => "MX"];
    public $_phone_ctry;
    public $_rate_type;

    public function __construct() {
        $this->requesting_id = (php_sapi_name() == "cli" ? "system" : Yii::$app->user->identity->username);
        $this->requesting_role = 'admin';
        $this->allow_negative = true;

        parent::__construct();
    }

    public function rules() {
        return [
            [['comment'], 'string', 'max' => 255],
            [['email_subject','email_message','_phone_ctry'],'string'],
            [['amount'], 'double'],
            [['transaction_type', 'comment', 'show_customer'], 'required', 'on' => 'batch_update'],
            ['customers_id', 'required', 'when' => function ($model) {
                return ((empty($model->customers_email)) && ($model->transaction_type == 'ADD_CREDIT' || $model->transaction_type == 'DEDUCT_CREDIT'));
            }, 'whenClient' => "function (attribute, value) {
                return (($(\"[id$='customers_email']\").val() == '') &&  ($(\"[id$='transaction_type']\").val() == 'ADD_CREDIT' || $(\"[id$='transaction_type']\").val() == 'DEDUCT_CREDIT'));
            }", 'on' => 'batch_update'],
            ['customers_email', 'required', 'when' => function ($model) {
                return ((empty($model->customers_id)) && ($model->transaction_type == 'ADD_CREDIT' || $model->transaction_type == 'DEDUCT_CREDIT'));
            }, 'whenClient' => "function (attribute, value) {
                return (($(\"[id$='customers_id']\").val() == '') &&  ($(\"[id$='transaction_type']\").val() == 'ADD_CREDIT' || $(\"[id$='transaction_type']\").val() == 'DEDUCT_CREDIT'));
            }", 'on' => 'batch_update'],
            [['currency', 'amount'], 'required', 'when' => function ($model) {
                    return ($model->transaction_type == 'ADD_CREDIT' || $model->transaction_type == 'DEDUCT_CREDIT');
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='transaction_type']\").val() == 'ADD_CREDIT' || $(\"[id$='transaction_type']\").val() == 'DEDUCT_CREDIT');
                }", 'on' => 'batch_update'],
            ['convert_currency', 'required', 'when' => function ($model) {
                    return $model->transaction_type == 'CONVERT_CREDIT';
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='transaction_type']\").val() == 'CONVERT_CREDIT');
                }", 'on' => 'batch_update'],
            ['customers_id', 'required', 'when' => function ($model) {
                    return ($model->transaction_type == 'CONVERT_CREDIT' && empty($model->customers_email) && empty($model->_phone_ctry));
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='transaction_type']\").val() == 'CONVERT_CREDIT') && ($(\"[id$='customers_email']\").val() == '') && ($(\"[id$='_phone_ctry']\").val() == '');
                }", 'on' => 'batch_update'],                
            ['customers_email', 'required', 'when' => function ($model) {
                    return ($model->transaction_type == 'CONVERT_CREDIT' && empty($model->customers_id) && empty($model->_phone_ctry) );
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='transaction_type']\").val() == 'CONVERT_CREDIT') && ($(\"[id$='customers_id']\").val() == '') && ($(\"[id$='_phone_ctry']\").val() == '');
                }", 'on' => 'batch_update'],
            ['email_message', 'required', 'when' => function ($model) {
                    return (!empty($model->email_subject));
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='email_subject']\").val() != '');
                }", 'on' => 'batch_update'],
            ['email_subject', 'required', 'when' => function ($model) {
                    return (!empty($model->email_message));
                }, 'whenClient' => "function (attribute, value) {
                    return ($(\"[id$='email_message']\").val() != '');
                }", 'on' => 'batch_update'],
            ['_rate_type', 'in', 'range' => ['sell', 'buy', 'spot']],
        ];
    }

    public function attributeLabels() {
        return [
            '_phone_ctry' => 'Phone Country',
            'customers_id' => 'Customer ID',
        ];
    }

    public function beforeValidate() {

        foreach (array_keys($this->getAttributes()) as $attr) {
            if(!empty($this->$attr) && !is_array($this->$attr)) {
               // validate the html tags is attributes
                $this->$attr = strip_tags(\yii\helpers\HtmlPurifier::process($this->$attr));
            }
        }
        $cid = [];

        // check customer
        if ($this->customers_id) {
            $invalid = [];
            
            $cust = array_unique(explode(",", $this->customers_id));
            foreach ($cust as $_num => $_id) {
                $_cid = trim($_id);
                if ($_cid && ctype_digit($_cid)) {
                    if (Customers::find()->where(['customers_id' => $_cid])->exists()) {
                        $cid[] = $_cid;
                    } else {
                        $invalid[] = $_id;
                    }
                } else if ($_cid != "") {
                    $invalid[] = $_id;
                }
            }

            if ($invalid) {
                $this->addError('customers_id', 'Customers ID ' . implode(', ', $invalid) . ' not exist');
                unset($invalid);
            } else if ($cid) {
                $this->customers_id = implode(",", array_unique($cid));
            }

        //Check on customer emails
        }else if($this->customers_email) {
            $invalid = [];
            $cust = array_unique(explode(",", $this->customers_email));
            foreach ($cust as $_num => $email) {
                $customer_email = trim($email);
                if ($customer_email && filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
                    if ($customer_data = Customers::find()->select(['customers_id'])->where(['customers_email_address' => $customer_email])->asArray()->one()) {
                        $cid[] = $customer_data['customers_id'];
                    } else {
                        $invalid[] = $email;
                    }
                } else if ($customer_email != "") {
                    $invalid[] = $email;
                }
            }

            if ($invalid) {
                $this->addError('customers_email', 'Customers Emails ' . implode(', ', $invalid) . ' not exist');
                unset($invalid);
            } else if ($cid) {
                $this->customers_id = implode(",", array_unique($cid));
            }
            
        }

        // add store credit, check admin daily transaction limit
        if ($this->customers_id && $this->transaction_type && $this->transaction_type == 'ADD_CREDIT' && $this->amount && $this->currency) {
            $amt = $this->amount * count(explode(",", $this->customers_id));
            $m_ac = (new \common\components\AdminCom);
            if (!$m_ac->checkCreditLimit($this->currency, $amt)) {
                $this->addError('amount', Yii::t('store-credit', 'You have reached the allowed daily send money limit'));
            }
        }

        return parent::beforeValidate();
    }

    public function createQueue() {
        $m_ac = (new \common\components\AdminCom);

        if ($this->customers_id) {
            $cust = array_unique(explode(",", $this->customers_id));
            foreach ($cust as $_num => $_cid) {
                $_cid = (int) $_cid;
                if ($_cid) {
                    $data = [];

                    switch ($this->transaction_type) {
                        case "ADD_CREDIT": case "DEDUCT_CREDIT":
                            if ($m_ac->checkCreditLimit($this->currency, $this->amount, true)) {
                                $data = [
                                    '_rate_type' => $this->_rate_type,
                                    'requesting_id' => $this->requesting_id,
                                    'requesting_role' => $this->requesting_role,
                                    'transaction_type' => $this->transaction_type,
                                    'activity' => $this->_activity[$this->transaction_type],
                                    'currency' => $this->currency,
                                    'amount' => $this->amount,
                                    'comment' => $this->comment,
                                    'show_customer' => $this->show_customer,
                                    'allow_negative' => $this->allow_negative,
                                    'email_subject' => $this->email_subject,
                                    'email_message' => $this->email_message
                                ];
                            }
                            break;

                        case "CONVERT_CREDIT":
                            $data = [
                                '_rate_type' => $this->_rate_type,
                                'requesting_id' => $this->requesting_id,
                                'requesting_role' => $this->requesting_role,
                                'transaction_type' => $this->transaction_type,
                                'activity' => $this->_activity[$this->transaction_type],
                                'to_currency' => $this->convert_currency,
                                'comment' => $this->comment,
                                'show_customer' => $this->show_customer,
                                'allow_negative' => $this->allow_negative,
                                'email_subject' => $this->email_subject,
                                'email_message' => $this->email_message
                            ];
                            break;
                    }

                    if ($data) {
                        $q = new DeliverQueue();
                        $q->type = "SC";
                        $q->id = $_cid;
                        $q->extra_info = Json::encode($data);
                        $q->created_at = time();
                        $q->save();
                    }

                }
            }

            return true;
        }

        if ($this->_phone_ctry) {
            if ($this->transaction_type == "CONVERT_CREDIT") {
                $m_ctry = Countries::findOne(['countries_iso_code_2' => $this->_phone_ctry]);
                if (isset($m_ctry->countries_id)) {
                    $data = [
                        '_phone_ctry' => $m_ctry->countries_id,
                        '_rate_type' => $this->_rate_type,
                        'requesting_id' => $this->requesting_id,
                        'requesting_role' => $this->requesting_role,
                        'transaction_type' => $this->transaction_type,
                        'activity' => $this->_activity[$this->transaction_type],
                        'to_currency' => $this->convert_currency,
                        'comment' => $this->comment,
                        'show_customer' => $this->show_customer,
                        'allow_negative' => $this->allow_negative,
                        'email_subject' => $this->email_subject,
                        'email_message' => $this->email_message
                    ];

                    $q = new DeliverQueue();
                    $q->type = "SC-Q";
                    $q->id = 0;
                    $q->extra_info = Json::encode($data);
                    $q->created_at = time();
                    $q->save();

                    return true;
                }
            }
        }

        return false;
    }

    public function deliverQueue($limit = 30) {
        $subject = '*[' . Yii::$app->id . '] Store Credit Deliver Queue*';
        $id = [];
        $message = [];
        $cnt = 0;
        $slack_cnt = 50;

        $sql = \common\models\DeliverQueue::find()->where(['type' => 'SC', 'lock_by' => null])->limit($limit)->asArray()->all();
        foreach ($sql as $num => $row) {
            $id[] = $row["dq_id"];
        }
        if ($id) {
            \common\models\DeliverQueue::updateAll(['lock_by' => $this->requesting_id], ['in', 'dq_id', $id]);
        }

        foreach ($sql as $num => $row) {
            $data = [];
            $extra = Json::decode($row["extra_info"]);

            // get current currency
            $m_scf = (new \backend\models\StoreCreditForm);
            $m_scf->getBalance($row["id"]);

            switch ($extra["transaction_type"]) {
                case "ADD_CREDIT": case "DEDUCT_CREDIT":
                    $request_type = "post";

                    $data = $extra;
                    $data['customers_id'] = $row["id"];

                    $text = "*Customer ID : " . $row['id'] . "* \n" .
                            "Transaction : " . $this->_trans_opt[$extra["transaction_type"]] . "\n" .
                            "Amount : " . $extra["currency"] . " " . $extra["amount"] . "\n" .
                            "Comment : " . $extra["comment"] . "\n";

                    if ($m_scf->currency != $extra["currency"]) {
                        $convert_to = $m_scf->currency ? $m_scf->currency : "USD";
                        $_amt = Yii::$app->currency->advanceCurrencyConversion($extra["amount"], $extra["currency"], $convert_to, true, ($extra["_rate_type"] ? $extra["_rate_type"] : "sell"));
                        $_rate = Yii::$app->currency->advanceCurrencyConversionRate($extra["currency"], $convert_to, ($extra["_rate_type"] ? $extra["_rate_type"] : "sell"));

                        $data["currency"] = $convert_to;
                        $data["amount"] = $_amt;

                        $text .= "Final Amount : " . $convert_to . " " . $_amt . "\n" .
                                "Rate : " . $_rate . " (" . $extra["_rate_type"] . ") \n";
                    }
                    break;

                case "CONVERT_CREDIT":
                    $text = "*Customer ID : " . $row['id'] . "* \n" .
                            "Transaction : " . $this->_trans_opt[$extra["transaction_type"]] . "\n";

                    if ($m_scf->currency && ($m_scf->currency != $extra["to_currency"])) {
                        $request_type = "put";

                        $data = $extra;
                        $data['customers_id'] = $row["id"];
                        if (!isset($extra["_rate_type"])) {
                            $extra["_rate_type"] = "sell";
                        }
                        $data['conversion_rate'] = Yii::$app->currency->advanceCurrencyConversionRate($m_scf->currency, $extra["to_currency"], $extra["_rate_type"]);

                        $text .= "Convert From : " . $m_scf->currency . "\n" .
                                "Convert To : " . $extra["to_currency"] . "\n" .
                                "Rate : " . $data['conversion_rate'] . " (" . $extra["_rate_type"] . ") \n" .
                                "Comment : " . $extra["comment"] . "\n";
                    } else {
                        \common\models\DeliverQueue::find()->where(['dq_id' => $row["dq_id"]])->one()->delete();
                    }
                    break;
            }

            if ($data) {
                // to-do
                $data["transaction_type"] = $this->_trans_type[$data["transaction_type"]];
                foreach ($data as $key => $val) {
                    if (substr($key, 0, 1) == "_") {
                        unset($data[$key]);
                    }
                }

                $m_scf = (new \backend\models\StoreCreditForm);
                $m_scf->setAttributes($data, false);
                $result = $m_scf->storeCreditApi($request_type);
                if ($result["status"] && isset($result["result"]["request_id"])) {
                    \common\models\DeliverQueue::find()->where(['dq_id' => $row["dq_id"]])->one()->delete();

                    // Send Email to complete the process
                    if($cust_data = Customers::find()->select(['customers_email_address','customers_firstname'])->where(['customers_id' => $row['id']])->asArray()->one()){
                        if(!empty($data['email_subject']) && !empty($data['email_message'])){
                            Yii::$app->mailer->compose()
                            ->setFrom(Yii::$app->params["noreply"]["default"])
                            ->setReplyTo(Yii::$app->params["noreply"]["default"])
                            ->setTo(trim($cust_data['customers_email_address']))
                            ->setSubject($data['email_subject'])
                            ->setHtmlBody($data['email_message'])
                            ->setTemplate('default')
                            ->setFirstname($cust_data['customers_firstname'])
                            ->send();
                        }
                    }

                    // Update orders_status_history if extra SC was delivered
                    if (isset($data['orders_id']) && !empty($data['orders_id']) && isset($data['activity']) && !empty($data['activity']) && $data['activity'] == "XS") {
                        $delivery_str = "SC: " . Yii::$app->currency->format($data["currency"], $data["amount"]) . "(" . $result["result"]["request_id"] . ")";
                        $osh_q = new \common\models\OrdersStatusHistory();
                        $osh_q->orders_id = $data['orders_id'];
                        $osh_q->orders_status_id = 0;
                        $osh_q->date_added = new \yii\db\Expression('NOW()');
                        $osh_q->customer_notified = '0';
                        $osh_q->comments = $delivery_str;
                        $osh_q->comments_type = 2;
                        $osh_q->changed_by = $this->requesting_id;
                        $osh_q->save();
                    }

                    $message[] = $text . "Transaction ID : " . $result["result"]["request_id"] . "\n\n";
                    $cnt++;
                } else {
                    Yii::$app->slack->send($subject, [[
                    'color' => 'danger',
                    'text' => $text .
                    "Request : " . Json::encode($m_scf->getAttributes()) . "\n" .
                    "Response : " . Json::encode($result)
                        ]], 'DEBUG');

                    // release lock
                    \common\models\DeliverQueue::updateAll(['lock_by' => null], ['in', 'dq_id', $row["dq_id"]]);
                    break;
                }
            }

            if ($cnt >= $slack_cnt && $message) {
                Yii::$app->slack->send($subject, [[
                'color' => 'good',
                'text' => implode(" ", $message)
                    ]], 'OGP');
                $message = [];
                $cnt = 0;
            }
        }

        if ($message) {
            Yii::$app->slack->send($subject, [[
            'color' => 'good',
            'text' => implode(" ", $message)
                ]], 'OGP');
        }
    }

    public function extractQueue() {
        $sql = \common\models\DeliverQueue::findOne(['type' => 'SC-Q', 'lock_by' => null]);
        if (isset($sql->dq_id)) {
            $sql->lock_by = $this->requesting_id;
            $sql->update();

            $extra = Json::decode($sql->extra_info);
            switch ($extra["transaction_type"]) {
                case "CONVERT_CREDIT":
                    if (isset($extra["_phone_ctry"])) {
                        $cust = Customers::find()->select('customers_id')->where(['customers_country_dialing_code_id' => $extra["_phone_ctry"]])->asArray()->all();
                        foreach ($cust as $num => $row) {
                            $data = [
                                '_rate_type' => $extra["_rate_type"],
                                'requesting_id' => $extra["requesting_id"],
                                'requesting_role' => $extra["requesting_role"],
                                'transaction_type' => $extra["transaction_type"],
                                'activity' => $extra["activity"],
                                'to_currency' => $extra["to_currency"],
                                'comment' => $extra["comment"],
                                'show_customer' => $extra["show_customer"],
                                'allow_negative' => $extra["allow_negative"]
                            ];

                            $q = new DeliverQueue();
                            $q->type = "SC";
                            $q->id = $row["customers_id"];
                            $q->extra_info = Json::encode($data);
                            $q->created_at = time();
                            $q->save();
                        }
                    }
                    break;
            }
            $sql->delete();
        }
    }

    public function downloadStatement($fr_date, $to_date) {
        $fr_date = ($fr_date ? $fr_date : date("Y-m-d H:i:s", strtotime('6 minutes ago')));
        $to_date = ($to_date ? $to_date : date("Y-m-d H:i:s"));

        $data = [
            'request_id' => '',
            'customers_id' => '',
            'start_date' => $fr_date,
            'end_date' => $to_date,
            'activity' => '',
            'scPage' => 1,
            'scPageSize' => 200, // max 200 in serverless
            'sort' => 'ASC'
        ];

        $m_sct = new StoreCreditTransaction();
        $m_scf = (new \backend\models\StoreCreditForm);
        $m_scf->setAttributes($data, false);

        $cnt = 1;
        $max = 1;
        do {
            $m_scf->scPage = $cnt;
            $result = $m_scf->storeCreditApi('statement');

            if ($result && $result['status'] && $result['result']['Items']) {
                foreach ($result['result']['Items'] as $num => $val) {
                    if (!StoreCreditTransaction::findOne($val['transaction_id'])) {
                        $m_sct->setAttributes($val, false);
                        $m_sct->isNewRecord = true;
                        $m_sct->save();
                    }
                }

                $max = $result['result']['Page']['total_page'];
            }
            $cnt++;
        } while ($cnt <= $max);
    }

}
