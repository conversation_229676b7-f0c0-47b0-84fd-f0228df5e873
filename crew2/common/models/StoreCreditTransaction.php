<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "store_credit_transaction".
 *
 * @property int $transaction_id
 * @property string $request_id
 * @property string $order_id
 * @property string $user_id
 * @property string $user_role
 * @property string $requesting_id
 * @property string $requesting_role
 * @property string $brand
 * @property string $activity
 * @property string $activity_title
 * @property string $activity_description
 * @property string $transaction_type
 * @property string $transaction_amount
 * @property string $transaction_currency
 * @property string $transaction_conversion_rate
 * @property string $previous_amount
 * @property string $previous_currency
 * @property string $new_amount
 * @property string $new_currency
 * @property int $allow_negative
 * @property int $free_conversion
 * @property string $param_1
 * @property string $param_2
 * @property string $param_3
 * @property string $created_date
 */
class StoreCreditTransaction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_credit_transaction';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['activity', 'transaction_type', 'created_date'], 'required'],
            [['activity_title', 'activity_description'], 'string'],
            [['transaction_amount', 'transaction_conversion_rate', 'previous_amount', 'new_amount', 'created_date'], 'number'],
            [['allow_negative', 'free_conversion'], 'integer'],
            [['request_id', 'order_id', 'user_id', 'user_role', 'requesting_id', 'requesting_role', 'transaction_type', 'transaction_currency', 'previous_currency', 'new_currency'], 'string', 'max' => 64],
            [['brand', 'activity'], 'string', 'max' => 20],
            [['param_1', 'param_2', 'param_3'], 'string', 'max' => 250],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'transaction_id' => 'Transaction ID',
            'request_id' => 'Request ID',
            'order_id' => 'Order ID',
            'user_id' => 'User ID',
            'user_role' => 'User Role',
            'requesting_id' => 'Requesting ID',
            'requesting_role' => 'Requesting Role',
            'brand' => 'Brand',
            'activity' => 'Activity',
            'activity_title' => 'Activity Title',
            'activity_description' => 'Activity Description',
            'transaction_type' => 'Transaction Type',
            'transaction_amount' => 'Transaction Amount',
            'transaction_currency' => 'Transaction Currency',
            'transaction_conversion_rate' => 'Transaction Conversion Rate',
            'previous_amount' => 'Previous Amount',
            'previous_currency' => 'Previous Currency',
            'new_amount' => 'New Amount',
            'new_currency' => 'New Currency',
            'allow_negative' => 'Allow Negative',
            'free_conversion' => 'Free Conversion',
            'param_1' => 'Param 1',
            'param_2' => 'Param 2',
            'param_3' => 'Param 3',
            'created_date' => 'Created Date',
        ];
    }
}
