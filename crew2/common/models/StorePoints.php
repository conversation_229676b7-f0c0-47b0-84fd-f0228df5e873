<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "store_points".
 *
 * @property int $customers_id
 * @property string $sp_amount
 * @property string $sp_last_modified
 */
class StorePoints extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_points';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id'], 'required'],
            [['customers_id'], 'integer'],
            [['sp_amount'], 'number'],
            [['sp_last_modified'], 'safe'],
            [['customers_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'sp_amount' => 'Sp Amount',
            'sp_last_modified' => 'Sp Last Modified',
        ];
    }
}
