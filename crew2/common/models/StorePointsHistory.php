<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "store_points_history".
 *
 * @property int $store_points_history_id
 * @property int $customer_id
 * @property string $store_points_history_date
 * @property string $store_points_history_debit_amount
 * @property string $store_points_history_credit_amount
 * @property string $store_points_history_after_balance
 * @property string $store_points_history_trans_type
 * @property string $store_points_history_trans_id
 * @property string $store_points_history_activity_type
 * @property string $store_points_history_activity_title
 * @property string $store_points_history_activity_desc
 * @property int $store_points_history_activity_desc_show
 * @property string $store_points_history_added_by
 * @property string $store_points_history_added_by_role
 * @property string $store_points_history_admin_messages
 */
class StorePointsHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_points_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_id', 'store_points_history_activity_desc_show'], 'integer'],
            [['store_points_history_date'], 'safe'],
            [['store_points_history_debit_amount', 'store_points_history_credit_amount', 'store_points_history_after_balance'], 'number'],
            [['store_points_history_activity_desc', 'store_points_history_admin_messages'], 'required'],
            [['store_points_history_activity_desc', 'store_points_history_admin_messages'], 'string'],
            [['store_points_history_trans_type'], 'string', 'max' => 10],
            [['store_points_history_trans_id'], 'string', 'max' => 255],
            [['store_points_history_activity_type'], 'string', 'max' => 2],
            [['store_points_history_activity_title', 'store_points_history_added_by'], 'string', 'max' => 128],
            [['store_points_history_added_by_role'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'store_points_history_id' => 'Store Points History ID',
            'customer_id' => 'Customer ID',
            'store_points_history_date' => 'Store Points History Date',
            'store_points_history_debit_amount' => 'Store Points History Debit Amount',
            'store_points_history_credit_amount' => 'Store Points History Credit Amount',
            'store_points_history_after_balance' => 'Store Points History After Balance',
            'store_points_history_trans_type' => 'Store Points History Trans Type',
            'store_points_history_trans_id' => 'Store Points History Trans ID',
            'store_points_history_activity_type' => 'Store Points History Activity Type',
            'store_points_history_activity_title' => 'Store Points History Activity Title',
            'store_points_history_activity_desc' => 'Store Points History Activity Desc',
            'store_points_history_activity_desc_show' => 'Store Points History Activity Desc Show',
            'store_points_history_added_by' => 'Store Points History Added By',
            'store_points_history_added_by_role' => 'Store Points History Added By Role',
            'store_points_history_admin_messages' => 'Store Points History Admin Messages',
        ];
    }
}
