<?php

namespace common\models;

use Yii;
use yii\base\Exception;
use yii\base\Model;
use yii\helpers\Json;
use yii\helpers\ArrayHelper;

class SyncPublisherProductModel extends Model
{
    public int $publisher_id;
    public string $publisher_name;

    public array $new_product_list = [];

    public array $modified_product_list = [];

    public \yii\db\Connection $db;

    public int $time;

    public bool $is_full_sync = true;

    /**
     * @var resource $file_handler
     */
    private $file_handler;

    public function rules(): array
    {
        return [
            [['publisher_id'], 'required'],
            [['publisher_id'], 'integer'],
            [['publisher_id'], 'validatePublisher'],
        ];
    }

    public function __construct($config = [])
    {
        parent::__construct($config);
    }


    public function setFileHandler()
    {
        if (!$tmp = fopen('php://temp', 'w+')) {
            throw new Exception('tmp directory not writable');
        }

        $this->file_handler = $tmp;

        fputcsv($this->file_handler, ['sku', 'name', 'denomination', 'attribute_1', 'attribute_2', 'attribute_3', 'cost_currency', 'cost_price', 'is_new', 'is_updated', 'cost_updated']);
    }

    public function getConnection()
    {
        if (!isset($this->db)) {
            $this->db = Yii::$app->db_og;
        }
        return $this->db;
    }

    public function validatePublisher($attribute, $params)
    {
        if ($publisher = Publisher::find()->select(['publisher_id', 'title'])->where(['publisher_id' => $this->publisher_id])->asArray()->one()) {
            $this->publisher_name = $publisher['title'];
        } else {
            $this->addError('publisher_id', 'Invalid Publisher ID.');
        }
    }

    public static function syncPublisherProductList(int $publisher_id, array $product_list, bool $is_full_sync)
    {
        try {
            $model = new self;
            $model->publisher_id = $publisher_id;

            if (!$model->validate()) {
                throw new \Exception(Json::encode($model->getErrors()));
            }

            $model->time = time();
            $model->setFileHandler();
            $model->is_full_sync = $is_full_sync;
            $model->sync($product_list);
        } catch (\Exception $e) {
            Yii::$app->slack->send('Failed to sync publisher product for publisher id : ' . $publisher_id, array(
                array(
                    'color' => 'warning',
                    'text' => $e->getMessage(),
                ),
            ));
        }
    }

    public function getExistingProductList(): array
    {
        return ArrayHelper::index(PublisherProduct::find()->select(['name', 'description', 'raw', 'cost_price', 'cost_currency', 'sku', 'created_at'])->where(['publisher_id' => $this->publisher_id])->asArray()->all(), 'sku');
    }

    public function sync(array $product_list)
    {
        $product_list = ArrayHelper::index($product_list, 'sku');
        $existing_product_list = $this->getExistingProductList();

        foreach ($product_list as $sku => $product) {
            $product_model = new PublisherProduct();

            $is_new_product = $is_cost_updated = $is_product_updated = false;

            unset($product_list[$sku]);

            if (!isset($existing_product_list[$product['sku']])) {
                $is_new_product = true;

                $data = array_merge($product, [
                    'publisher_id' => $this->publisher_id,
                    'raw' => Json::encode($product['raw']),
                    'status' => 1,
                    'created_at' => $this->time,
                    'updated_at' => $this->time
                ]);

                $product_model->load($data, '');

                if ($product_model->validate()) {
                    $this->new_product_list[] = $product_model->getArrayData();
                } else {
                    throw new \Exception(
                        'Failed to insert publisher products : ' . Json::encode([
                            'attributes ' => $data,
                            'errors' => $product_model->getErrors()
                        ])
                    );
                }
            } else {
                $old_product = $existing_product_list[$product['sku']];

                $data = array_merge($product, [
                    'publisher_id' => $this->publisher_id,
                    'status' => 1,
                    'raw' => Json::encode($product['raw']),
                    'created_at' => $old_product['created_at'],
                    'updated_at' => $this->time
                ]);

                $product_model->load($data, '');

                if (md5($data['raw']) != md5($old_product['raw'])) {
                    $is_product_updated = true;
                    if ($product_model->validate()) {
                        $this->modified_product_list[] = $product_model->getArrayData();
                    } else {
                        throw new \Exception(
                            'Failed to update publisher products : ' . Json::encode([
                                'attributes ' => $data,
                                'errors' => $product_model->getErrors()
                            ])
                        );
                    }

                    if ($product['cost_price'] != $old_product['cost_price']) {
                        $is_cost_updated = true;
                    }
                }

                unset($existing_product_list[$product['sku']]);
            }

            $this->addCsvRow($product_model, $is_new_product, $is_product_updated, $is_cost_updated);
        }

        $this->getConnection()->beginTransaction();

        $this->insertNewProduct();
        $this->updateModifiedProduct();

        if ($this->is_full_sync) {
            $this->disableDeprecatedProduct(array_keys($existing_product_list));
        }

        $this->getConnection()->getTransaction()->commit();

        $this->sendProductCSVToEmail();
    }

    public function insertNewProduct()
    {
        foreach (array_chunk($this->new_product_list, 100) as $list) {
            $this->getConnection()->createCommand()->batchInsert(PublisherProduct::tableName(), [
                'publisher_id',
                'name',
                'description',
                'cost_price',
                'cost_currency',
                'status',
                'sku',
                'denomination',
                'attribute_1',
                'attribute_2',
                'attribute_3',
                'raw',
                'created_at',
                'updated_at'
            ], $list)->execute();
        }
    }

    public function updateModifiedProduct()
    {
        foreach ($this->modified_product_list as $product) {
            $this->getConnection()->createCommand()->update(PublisherProduct::tableName(), $product, ['publisher_id' => $this->publisher_id, 'sku' => $product['sku']])->execute();
        }
    }

    public function disableDeprecatedProduct($deprecated_sku_list)
    {
        foreach ($deprecated_sku_list as $sku) {
            fputcsv($this->file_handler, [
                $sku,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            ]);
        }
        $this->getConnection()->createCommand()->update(PublisherProduct::tableName(), ['status' => 0], ['publisher_id' => $this->publisher_id, 'sku' => $deprecated_sku_list])->execute();
    }

    public function addCsvRow($product_model, $is_new_product, $is_product_updated, $is_cost_updated)
    {
        fputcsv($this->file_handler, [
            $product_model->sku,
            $product_model->name,
            $product_model->denomination,
            $product_model->attribute_1,
            $product_model->attribute_2,
            $product_model->attribute_3,
            $product_model->cost_currency,
            $product_model->cost_price,
            $is_new_product,
            $is_product_updated,
            $is_cost_updated
        ]);
    }

    private function sendProductCSVToEmail()
    {
        // Place stream pointer at beginning
        rewind($this->file_handler);

        $s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
        $content = "The following document valid to download 5 days from the date of issue : <br><br>";
        $expiry_time = 432000;

        $filename = 'report/' . strtolower($this->publisher_name) . "_product_list_" . $this->time . ".csv";

        $s3->saveContent($filename, $this->file_handler);

        $content = $content . $this->publisher_name . " Product List<br>" . $s3->getContentUrl($filename, true, $expiry_time);

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["syncPublisherProduct.notification"])
            ->setSubject($this->publisher_name . ' Products List')
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
    }
}