<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "top_up_info".
 *
 * @property string $top_up_info_id ID
 * @property int $products_id Product ID
 * @property string $top_up_info_title Top-up info title, such as game account
 * @property string $top_up_info_key key to passover, eg. gameaccount
 * @property string $top_up_info_description Top-up description
 * @property string $top_up_info_value If type id is 1, then this value will be use for passing over, else will grab from customers_top_up_info
 * @property string $top_up_info_type_id Top-up info type, 1 or 2
 * @property int $sort_order sorting
 * @property string $last_modified Record updated time
 * @property string $date_added Record added time
 * @property int $last_modified_by
 * @property string $use_function function to integrate
 * @property string $set_function Input type, textbox, select box or ...
 */
class TopUpInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'top_up_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'top_up_info_type_id', 'sort_order', 'last_modified_by'], 'integer'],
            [['last_modified', 'date_added'], 'safe'],
            [['set_function'], 'required'],
            [['set_function'], 'string'],
            [['top_up_info_title', 'top_up_info_key'], 'string', 'max' => 64],
            [['top_up_info_description', 'top_up_info_value', 'use_function'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'top_up_info_id' => 'Top Up Info ID',
            'products_id' => 'Products ID',
            'top_up_info_title' => 'Top Up Info Title',
            'top_up_info_key' => 'Top Up Info Key',
            'top_up_info_description' => 'Top Up Info Description',
            'top_up_info_value' => 'Top Up Info Value',
            'top_up_info_type_id' => 'Top Up Info Type ID',
            'sort_order' => 'Sort Order',
            'last_modified' => 'Last Modified',
            'date_added' => 'Date Added',
            'last_modified_by' => 'Last Modified By',
            'use_function' => 'Use Function',
            'set_function' => 'Set Function',
        ];
    }
}
