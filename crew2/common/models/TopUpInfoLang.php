<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "top_up_info_lang".
 *
 * @property string $top_up_info_id Top up info ID
 * @property string $top_up_info_display Display value
 * @property int $languages_id Language ID
 */
class TopUpInfoLang extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'top_up_info_lang';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['top_up_info_id', 'languages_id'], 'required'],
            [['top_up_info_id', 'languages_id'], 'integer'],
            [['top_up_info_display'], 'string', 'max' => 255],
            [['top_up_info_id', 'languages_id'], 'unique', 'targetAttribute' => ['top_up_info_id', 'languages_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'top_up_info_id' => 'Top Up Info ID',
            'top_up_info_display' => 'Top Up Info Display',
            'languages_id' => 'Languages ID',
        ];
    }
}
