<?php

namespace common\models;

use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "zones_info".
 *
 * @property int $geo_zone_id
 * @property string $geo_zone_info
 */
class ZonesInfo extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'zones_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['geo_zone_id'], 'integer'],
            [['geo_zone_info'], 'string']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'geo_zone_id' => 'GEO Zone ID',
            'geo_zone_info' => 'GEO Zone Info'
        ];
    }

}
