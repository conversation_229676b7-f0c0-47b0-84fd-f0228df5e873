<?php

namespace common\models;

use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "zones_to_geo_zones".
 *
 * @property int $association_id
 * @property int $zone_country_id
 * @property int $zone_id
 * @property int $geo_zone_id
 */
class ZonesToGeoZones extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'zones_to_geo_zones';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['association_id', 'zone_country_id', 'zone_id', 'geo_zone_id'], 'integer']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'association_id' => 'Association ID',
            'zone_id' => 'Zone ID',
            'zone_country_id' => 'Zone Country ID',
            'geo_zone_id' => 'Geo Zone ID'
        ];
    }

}
