<?php

namespace common\models\resellers;

use common\models\ResellerSetting;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use InvalidArgumentException;
use offgamers\base\models\ms\Order;
use offgamers\base\traits\GuzzleTrait;
use Yii;
use yii\helpers\Html;
use yii\helpers\Json;

class G2G
{
    const API_VERSION = 'v2';

    public $reseller;
    protected $configuration_data;
    protected $base_url;
    protected $api_key;
    protected $api_secret;
    protected $user_id;
    protected $webhook_token;

    protected $error_code;
    protected $error_message;

    use GuzzleTrait;

    protected function getFieldList()
    {
        return array(
            [
                'label' => 'Base URL',
                'key' => 'BASE_URL',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Api Key',
                'key' => 'API_KEY',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Api Secret',
                'key' => 'API_SECRET',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'Webhook Token',
                'key' => 'WEBHOOK_TOKEN',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
            [
                'label' => 'User ID',
                'key' => 'USER_ID',
                'type' => 'text',
                'option' => ['class' => 'form-control']
            ],
        );
    }

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->reseller)) {
                $config_list = ResellerSetting::findAll(['reseller_id' => $this->reseller]);

                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Reseller Id is empty');
            }
        }
    }

    public function getConfig()
    {
        $this->getExistingConfig();
        $this->api_key = $this->configuration_data['API_KEY'];
        $this->api_secret = $this->configuration_data['API_SECRET'];
        $this->user_id = $this->configuration_data['USER_ID'];
        $this->base_url = $this->configuration_data['BASE_URL'];
        $this->webhook_token = $this->configuration_data['WEBHOOK_TOKEN'];
    }

    public function renderConfigurationField()
    {
        $configuration = $this->getFieldList();

        return Yii::$app->controller->renderPartial(
            'config',
            array('configs' => $configuration, 'data' => $this->configuration_data)
        );
    }

    public function renderColumn($url, $model, $key)
    {
        $template = '{update} {sync}';
        return $model->renderActionColumn($template, [
            'sync' => function ($url, $model, $key) {
                return Html::a('<span class="fa fa-list"></span>', '/reseller/g2g-order?id=' . $model->reseller_id);
            }
        ], $model, $key, -1);
    }

    public function processPostBack($data)
    {
        switch ($data['event_type']) {
            case 'order.api_delivery':
                if (isset($data['payload']['order_id'])) {
                    $this->processDelivery($data['payload']['order_id']);
                }
                break;

            case 'offer.low_stock':
                if (isset($data['payload']['offer_id'])) {
                    $this->updateOfferQty($data['payload']['offer_id'], ['api_qty' => 10000]);
                }
                break;
        }
    }

    public function processDelivery($g2g_order_id, $reprocess_restock_request = false)
    {
        $request_data = [
            'reseller_id' => $this->reseller,
            'order_id' => $g2g_order_id,
            'reprocess_restock_request' => $reprocess_restock_request
        ];
        (new Order())->customRequest('order', 'process-g2g-order', $request_data);
    }

    public function generateSignature($path, $timestamp)
    {
        $string = '/' . self::API_VERSION . '/' . $path . $this->api_key . $this->user_id . $timestamp;
        return hash_hmac('sha256', $string, $this->api_secret);
    }

    public function updateOfferQty($offer_id, $data)
    {
        $response = $this->sendRequest('PATCH', 'offers/' . $offer_id, $data);
        if ($response->getStatusCode() === 200) {
            $response_body = $response->getBody();
            $data = Json::decode($response_body);
            if (isset($data['code']) && $data['code'] === 20000001) {
                return true;
            }
        }
        return false;
    }

    public function patchAllOfferList($offer_list)
    {
        // Create a Guzzle HTTP client
        $httpClient = new Client();
        $requests = [];
        $request_2 = [];

        foreach ($offer_list as $offer_id) {
            $requests[$offer_id] = $this->base_url . '/' . 'offers/' . $offer_id;
            $data = Json::encode(['api_qty' => 10000, 'low_stock_alert_qty' => 1000]);
            $time = time();
            $this->initClient();
            $signature = $this->generateSignature('offers/' . $offer_id, $time);
            $options = [
                'headers' => [
                    'g2g-api-key' => $this->api_key,
                    'g2g-userid' => $this->user_id,
                    'g2g-signature' => $signature,
                    'g2g-timestamp' => $time,
                    'Content-Type' => 'application/json'
                ],
                'json' => ['api_qty' => 10000],
                'http_errors' => false
            ];
            $headers = [
                'g2g-api-key' => $this->api_key,
                'g2g-userid' => $this->user_id,
                'g2g-signature' => $signature,
                'g2g-timestamp' => $time,
                'Content-Type' => 'application/json'
            ];
            $request = new Request('PATCH', $this->base_url . '/' . 'offers/' . $offer_id, $headers, $data);
            $request_2[] = $this->client->getAsyncRequest($this->client, $request, '', $options);
        }

        $pool = new Pool($httpClient, $request_2, [
            'concurrency' => 30,
            'fulfilled' => function ($response, $index) use (&$results, $offer_list) {
                // Handle the response for each file
                if ($response->getStatusCode() === 200) {
                    // Successful response
                    echo (string)$response->getBody();
                }
            },
            'rejected' => function ($reason, $index) use (&$results, $offer_list) {
                if ($reason instanceof ClientException) {
                    $status_code = $reason->getResponse()->getStatusCode();
                    $body = $reason->getResponse()->getBody();
                    echo (string)$body;
                } elseif ($reason instanceof \Exception) {
                    echo $reason->getMessage();
                }
            },
        ]);

        $pool->promise()->wait();
    }

    public function getAllOfferList()
    {
        $offer_list = [];

        $page = 1;

        while (true) {
            $body = [
                'page' => $page,
                'page_size' => 100
            ];
            $data = $this->sendRequest('POST', 'offers/search', $body, 0);
            if (!empty($data['results'])) {
                foreach ($data['results'] as $offer) {
                    $offer_list[] = [
                        'offer_id' => $offer['offer_id'],
                        'title' => $offer['title']
                    ];
                }
                // Stop processing when all offer list get
                if (100 > count($data['results'])) {
                    break;
                } else {
                    $page++;
                }
            }
        }

        return $offer_list;
    }

    public function verifyWebhookSignature($webhook_url, $timestamp, $signature)
    {
        $string = $webhook_url . $this->user_id . $timestamp;
        return hash_hmac('sha256', $string, $this->webhook_token) === $signature;
    }

    private function parseResponse($response, $throw_exception = true)
    {
        if ($data = $response->getBody()) {
            $parsed_data = Json::decode($data);
            if (isset($parsed_data['code']) && $parsed_data['code'] === 20000001) {
                return $parsed_data['payload'];
            } elseif (isset($parsed_data['code']) && isset($parsed_data['message'])) {
                $this->error_code = $parsed_data['code'];
                $this->error_message = $parsed_data['message'];
            }
        }

        if (!$throw_exception) {
            return $response;
        } else {
            Yii::$app->slack->send('Invalid Response from G2G API', array(
                array(
                    'color' => 'warning',
                    'text' => $this->error_code . ' : ' . $this->error_message
                ),
            ));
            throw new \Exception('Invalid Response from G2G API');
        }
    }

    private function sendRequest($method, $path, $body = [], $throw_exception = true)
    {
        $time = time();
        $this->getConfig();
        $this->initClient();

        $signature = $this->generateSignature($path, $time);

        $options = [
            'headers' => [
                'g2g-api-key' => $this->api_key,
                'g2g-userid' => $this->user_id,
                'g2g-signature' => $signature,
                'g2g-timestamp' => $time,
                'Content-Type' => 'application/json'
            ],
            'http_errors' => false
        ];


        if ($body) {
            $options['json'] = $body;
        }

        return $this->parseResponse($this->client->request($method, $this->base_url . '/' . $path, $options), $throw_exception);
    }
}