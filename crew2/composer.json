{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "dev", "require": {"php": ">=7.4.0", "yiisoft/yii2": "~2.0.6", "yiisoft/yii2-bootstrap": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.0.0", "mdmsoft/yii2-admin": "~2.0", "fortawesome/font-awesome": "*", "kartik-v/yii2-widgets": "dev-master", "kartik-v/yii2-grid": "dev-master", "kartik-v/yii2-export": "*", "kartik-v/yii2-datecontrol": "*", "kartik-v/yii2-tabs-x": "*", "kartik-v/yii2-date-range": "*", "yiisoft/yii2-jui": "^2.0", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-switchinput": "^1.3", "offgamers/yii2-metronic": "dev-main", "yiisoft/yii2-bootstrap4": "dev-master", "bower-asset/google-code-prettify": "^1.0", "bower-asset/bootstrap-duallistbox": "4.0.2", "offgamers/microservices-multiservice": "dev-main", "offgamers/microservices-publisher": "dev-main", "mervick/yii2-emojionearea": "*", "shortpixel/shortpixel-php": "dev-master", "tinify/tinify": "dev-master", "kraken-io/kraken-php": "dev-master", "dpodium/yii2-geoip": "~2.0.0", "aws/aws-php-sns-message-validator": "dev-master", "wbraganca/yii2-dynamicform": "*", "ext-json": "*", "giggsey/libphonenumber-for-php": "^8.0@dev"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^4.0", "codeception/module-asserts": "^1.0", "codeception/module-yii2": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/verify": "~0.5.0 || ~1.1.0", "symfony/browser-kit": ">=2.7 <=4.2.4"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}, {"type": "git", "url": "**************:tech-toolbox/metronic.git"}, {"type": "git", "url": "**************:tech-ogm/ms-publisher.git"}, {"type": "git", "url": "**************:tech-ogm/toolbox-multiservice.git"}]}