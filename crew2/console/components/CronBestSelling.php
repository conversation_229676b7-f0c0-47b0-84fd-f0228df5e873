<?php

namespace console\components;

use console\models\BestSelling;
use http\Exception;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class CronBestSelling
{
    private const MAX_BEST_SELLING_NO = 10;

    private const PRODUCT_TYPE_VOUCHER = "1";
    private const PRODUCT_TYPE_PHYSICAL_GOODS = "4";
    private const CACHE_KEY_BESTSELLING = "bestselling";

    protected string $start_time;

    protected string $date_from;
    protected string $date_to;

    protected array $country_code_list = [];

    protected array $categories_to_brand_list = [];
    protected array $dtu_product_list = [];
    protected array $product_sales_list = [];

    protected array $country_general_brand_list = [];
    protected array $country_voucher_brand_list = [];
    protected array $country_dtu_brand_list = [];
    protected array $country_physical_goods_brand_list = [];

    protected array $default_general_brand_list = [];
    protected array $default_voucher_brand_list = [];
    protected array $default_dtu_brand_list = [];
    protected array $default_physical_goods_brand_list = [];

    protected array $temp_general_brand_list = [];
    protected array $temp_default_voucher_brand_list = [];
    protected array $temp_default_dtu_brand_list = [];
    protected array $temp_default_physical_goods_brand_list = [];

    public function setData($date_from, $date_to): void
    {
        $this->date_from = (!empty($date_from)) ? date("Y-m-d 00:00:00", strtotime($date_from)) :
            date("Y-m-01 00:00:00", strtotime(date('Y-m-d') . " -1 month"));

        $this->date_to = (!empty($date_to)) ? date("Y-m-d 00:00:00", strtotime($date_to)) :
            date("Y-m-01 00:00:00", time());
    }

    public function process(): void
    {
        $this->processBestSelling();
    }

    public function processBestSelling(): void
    {
        $this->startCronjob();
        $this->getSales();

        if (!empty($this->product_sales_list)) {
            try {
                // Find DTU product based on the product list given
                $this->getDtuProductList();

                // Set Temporary List based on Default and Country
                $this->processTemporaryList();

                // Set Into Default List();
                $this->processDefaultList();

                // Lookup Default Country and Set into it, remove the country list by country code.
                $this->processDefaultCountryList();

                // Process All Country;
                $this->processAllCountryList();

                // Clear Temporary List;
                unset($this->temp_general_brand_list, $this->temp_default_voucher_brand_list, $this->temp_default_dtu_brand_list,
                    $this->temp_default_physical_goods_brand_list);

                // Store Default List
                $this->saveBestSellingList();

                // Clear Cache After Best Selling Save
                $this->clearCache();

            } catch (\Exception $e) {
                Yii::$app->slack->send("Best Selling Cron ($this->date_from - $this->date_to) : ", [
                    ['color' => "warning", 'text' => $e->getMessage() . "\n" . $e->getTraceAsString()]
                ], 'debug');
            }
        }

        $this->endCronjob();
    }

    private function startCronjob(): void
    {
        $this->start_time = date("Y-m-d H:i:s");
        $this->slackNotice("The cronjob started at " . $this->start_time);
    }

    private function endCronjob(): void
    {
        $endTime = date("Y-m-d H:i:s");
        $this->slackNotice("The cronjob started at " . $this->start_time . " ended at " . $endTime);
    }

    private function processDefaultCountryList()
    {
        $result = (new Query())
            ->select(['configuration_value'])
            ->from('configuration')
            ->where(['configuration_key' => 'STORE_COUNTRY'])
            ->one(Yii::$app->db_slave_offgamers);
        if ($result) {
            $this->default_general_brand_list = $this->setDefaultCountryList($result['configuration_value'], $this->country_general_brand_list, $this->default_general_brand_list);
            $this->default_voucher_brand_list = $this->setDefaultCountryList($result['configuration_value'], $this->country_voucher_brand_list, $this->default_voucher_brand_list);
            $this->default_dtu_brand_list = $this->setDefaultCountryList($result['configuration_value'], $this->country_dtu_brand_list, $this->default_dtu_brand_list);
            $this->default_physical_goods_brand_list = $this->setDefaultCountryList($result['configuration_value'], $this->country_physical_goods_brand_list, $this->default_physical_goods_brand_list);

            unset(
                $this->country_general_brand_list[$result['configuration_value']],
                $this->country_voucher_brand_list[$result['configuration_value']],
                $this->country_dtu_brand_list[$result['configuration_value']],
                $this->country_physical_goods_brand_list[$result['configuration_value']]
            );
        }
    }

    private function setDefaultCountryList($default_country_id, $country_list, $default_list):array
    {
        if (isset($country_list[$default_country_id])) {
            $default_brand_list = $country_list[$default_country_id];
            arsort($default_brand_list);
            $default_brand_list = array_keys($default_brand_list);

            $counter = count($default_brand_list);
            if (self::MAX_BEST_SELLING_NO > $counter) {
                $extra_brand_list = array_diff($default_list, $default_brand_list);
                foreach ($extra_brand_list as $num => $brand) {
                    if (self::MAX_BEST_SELLING_NO > $counter) {
                        $default_brand_list[] = $brand;
                        $counter++;
                    } else {
                        break;
                    }
                }
            } else if ($counter > self::MAX_BEST_SELLING_NO) {
                $array = array_chunk($default_brand_list, self::MAX_BEST_SELLING_NO);
                $default_brand_list = $array[0];
            }

            return $default_brand_list;
        }

        return [];
    }


    private function getSales(): void
    {
        $this->product_sales_list = (new Query())
            ->select([
                'p.products_id', 'p.products_type', 'SUM( op.products_good_delivered_price ) AS products_sales',
                'oei.orders_extra_info_value AS ip_country'
            ])
            ->from("orders o")
            ->innerJoin("orders_extra_info oei", "oei.orders_id = o.orders_id AND oei.orders_extra_info_key = 'ip_country'")
            ->innerJoin("orders_products op", "op.orders_id = o.orders_id ")
            ->innerJoin("products p", "op.products_id = p.products_id")
            ->where(['op.custom_products_type_id' => 2])
            ->andWhere("o.date_purchased >= :date_from", [':date_from' => $this->date_from])
            ->andWhere('o.date_purchased < :date_to', [":date_to" => $this->date_to])
            ->andWhere('o.orders_status = :orders_status', [":orders_status" => 3])
            ->groupBy(['oei.orders_extra_info_value', 'p.products_id'])
            ->having(['>', 'products_sales', '0'])
            ->all(Yii::$app->db_slave_offgamers);
    }

    private function getCategoryPath($productId): string
    {
        $category_path = "";

        $category = (new Query())
            ->select(['p2c.categories_id'])
            ->from("products p")
            ->innerJoin("products_to_categories p2c", "p.products_id = p2c.products_id")
            ->where(["p.products_id" => (int)$productId])
            ->andWhere(["p.products_status" => 1])
            ->one(Yii::$app->db_slave_offgamers);

        if ($category) {
            $categories = $this->getParentCategories($category['categories_id']);
            $categories = array_reverse($categories);
            $category_path = implode("_", $categories);

            if (!empty($category_path)) {
                $category_path .= "_";
            }

            $category_path .= $category['categories_id'];
        }

        return $category_path;
    }

    private function getParentCategories($categories_id, $parent_categories_array = [])
    {
        $parent_category = (new Query())
            ->select("parent_id")
            ->from("categories")
            ->where(['categories_id' => $categories_id])
            ->one(Yii::$app->db_slave_offgamers);
        if ($parent_category['parent_id'] == 0) {
            return $parent_categories_array;
        }

        $parent_categories_array[] = $parent_category['parent_id'];
        return $this->getParentCategories($parent_category['parent_id'], $parent_categories_array);
    }

    private function processTemporaryList()
    {
        $this->country_code_list = ArrayHelper::map((new Query())
            ->select(['countries_id', 'countries_iso_code_2'])
            ->from('countries')
            ->all(Yii::$app->db_slave_offgamers), 'countries_id','countries_iso_code_2');

        foreach ($this->product_sales_list as $sales_row) {
            $country_code = $sales_row['ip_country'];
            $selected_category_list = explode('_', $this->getCategoryPath($sales_row['products_id']));

            if (count($selected_category_list) > 0) {
                $category_list = array_reverse($selected_category_list);

                if (!empty($category_list)) {
                    foreach ($category_list as $categories_id) {
                        $brand_id = $this->getCategoriesToBrandList($categories_id);

                        if ($brand_id) {
                            $this->temp_general_brand_list[$brand_id] = $this->updateAndGetGlobalTempList($brand_id, $sales_row['products_sales'], $this->temp_general_brand_list);
                            $this->country_general_brand_list[$country_code][$brand_id] = $this->updateAndGetGlobalCountryCodeList($brand_id, $country_code, $sales_row['products_sales'], $this->country_general_brand_list);

                            if ($sales_row['products_type'] === null || $sales_row['products_type'] === self::PRODUCT_TYPE_VOUCHER) {
                                if (isset($this->dtu_product_list[$sales_row['products_id']])) {
                                    $this->temp_default_dtu_brand_list[$brand_id] = $this->updateAndGetGlobalTempList($brand_id, $sales_row['products_sales'], $this->temp_default_dtu_brand_list);
                                    $this->country_dtu_brand_list[$country_code][$brand_id] = $this->updateAndGetGlobalCountryCodeList($brand_id, $country_code, $sales_row['products_sales'], $this->country_dtu_brand_list);
                                }
                                else{
                                    $this->temp_default_voucher_brand_list[$brand_id] = $this->updateAndGetGlobalTempList($brand_id, $sales_row['products_sales'], $this->temp_default_voucher_brand_list);
                                    $this->country_voucher_brand_list[$country_code][$brand_id] = $this->updateAndGetGlobalCountryCodeList($brand_id, $country_code, $sales_row['products_sales'], $this->country_voucher_brand_list);
                                }
                            }

                            if ($sales_row['products_type'] === self::PRODUCT_TYPE_PHYSICAL_GOODS) {
                                $this->temp_default_physical_goods_brand_list[$brand_id] = $this->updateAndGetGlobalTempList($brand_id, $sales_row['products_sales'], $this->temp_default_physical_goods_brand_list);
                                $this->country_physical_goods_brand_list[$country_code][$brand_id] = $this->updateAndGetGlobalCountryCodeList($brand_id, $country_code, $sales_row['products_sales'], $this->country_physical_goods_brand_list);
                            }
                        }
                    }
                }
            }
        }
    }

    private function updateAndGetGlobalTempList($brand_id, $sales, $temp_default_list)
    {
        if (!empty($temp_default_list[$brand_id])) {
            return $temp_default_list[$brand_id] + $sales;
        }
        return $sales;
    }

    private function updateAndGetGlobalCountryCodeList($brand_id, $country_code, $sales, $country_list)
    {
        if (!empty($country_list[$country_code][$brand_id])) {
            return $country_list[$country_code][$brand_id] += $sales;
        }
        return $sales;
    }

    private function processDefaultList()
    {
        $this->default_general_brand_list = $this->processTemporaryListToDefaultList($this->temp_general_brand_list);
        $this->default_voucher_brand_list = $this->processTemporaryListToDefaultList($this->temp_default_voucher_brand_list);
        $this->default_dtu_brand_list = $this->processTemporaryListToDefaultList($this->temp_default_dtu_brand_list);
        $this->default_physical_goods_brand_list = $this->processTemporaryListToDefaultList($this->temp_default_physical_goods_brand_list);
    }

    private function processTemporaryListToDefaultList($temp_list):array
    {
        $list = [];
        arsort($temp_list);
        foreach ($temp_list as $brand_id => $val) {
            $list[] = $brand_id;
        }
        $array = array_chunk($list, self::MAX_BEST_SELLING_NO);
        if (!empty($array)) {
            return $array[0];
        }
        return [];
    }

    private function processAllCountryList()
    {
        $this->country_general_brand_list = $this->processSingleCountryList($this->country_general_brand_list, $this->default_general_brand_list);
        $this->country_voucher_brand_list = $this->processSingleCountryList($this->country_voucher_brand_list, $this->default_voucher_brand_list);
        $this->country_dtu_brand_list = $this->processSingleCountryList($this->country_dtu_brand_list, $this->default_dtu_brand_list);
        $this->country_physical_goods_brand_list = $this->processSingleCountryList($this->country_physical_goods_brand_list, $this->default_physical_goods_brand_list);
    }

    private function processSingleCountryList($country_list, $default_list):array
    {
        $result = [];
        foreach ($country_list as $country_code => $country_brand_list) {
            arsort($country_brand_list);
            $result[$country_code] = array_keys($country_brand_list);
        }

        foreach ($result as $country_code => $best_selling) {
            $counter = count($best_selling);

            if (self::MAX_BEST_SELLING_NO > $counter) {
                $default_brand_list = array_diff($default_list, $best_selling);
                foreach ($default_brand_list as $num => $brand) {
                    if (self::MAX_BEST_SELLING_NO > $counter) {
                        $result[$country_code][] = $brand;
                        $counter++;
                    } else {
                        break;
                    }
                }
            } else if ($counter > self::MAX_BEST_SELLING_NO) {
                $array = array_chunk($best_selling, self::MAX_BEST_SELLING_NO);
                $result[$country_code] = $array[0];
            }
        }
        return $result;
    }

    private function slackNotice($text)
    {
        Yii::$app->slack->send("Best Selling Cron ($this->date_from - $this->date_to) : ", [
            ['color' => "good", 'text' => $text]
        ]);
    }

    private function getCategoriesToBrandList($categoriesId)
    {
        if (isset($this->categories_to_brand_list[$categoriesId])) {
            return $this->categories_to_brand_list[$categoriesId];
        }

        $result = (new Query())
            ->select(['categories_id', 'brand_id'])
            ->from("category")
            ->where(['categories_id' => $categoriesId])
            ->one(Yii::$app->db_slave_og);

        if ($result) {
            $this->categories_to_brand_list[$result['categories_id']] = $result['brand_id'];
            return $result['brand_id'];
        }

        return false;
    }

    private function getDtuProductList()
    {
        $product_list = ArrayHelper::getColumn($this->product_sales_list, 'products_id');

        $this->dtu_product_list = ArrayHelper::map((new Query())
            ->select(['products_id'])
            ->from("products_delivery_info")
            ->where(['products_delivery_mode_id' => 6])
            ->andWhere(['IN', 'products_id', $product_list])
            ->all(Yii::$app->db_slave_offgamers), 'products_id', 'products_id');
    }

    /**
     * @throws \yii\db\StaleObjectException
     * @throws \Throwable
     */
    private function saveBestSellingList()
    {
        foreach($this->country_code_list as $country_id => $country_code) {
            $brand_list = [];

            $brand_list['general'] = $this->country_general_brand_list[$country_id] ?? $this->default_general_brand_list;
            $brand_list['voucher'] = $this->country_voucher_brand_list[$country_id] ?? $this->default_voucher_brand_list;
            $brand_list['dtu'] = $this->country_dtu_brand_list[$country_id] ?? $this->default_dtu_brand_list;
            $brand_list['physical_goods'] = $this->country_physical_goods_brand_list[$country_id] ?? $this->default_physical_goods_brand_list;

            $best_selling = BestSelling::findOne(['country_code' => $country_code]);
            if ($best_selling) {
                $best_selling->info = Json::encode($brand_list);
                $best_selling->update(true);
            } else {
                $best_selling = new BestSelling();
                $best_selling->country_code = $country_code;
                $best_selling->info = Json::encode($brand_list);
                $best_selling->save(true);
            }
        }
    }

    private function clearCache() {
        $languages = Yii::$app->enum->getLanguage('listData');
        foreach ($this->country_code_list as $country_code) {
            foreach ($this->country_code_list as $ip_country) {
                foreach ($languages as $language_id => $lang) {
                    Yii::$app->cache->delete(static::CACHE_KEY_BESTSELLING . "/$country_code/$ip_country/$language_id");
                }
            }
        }
    }
}
