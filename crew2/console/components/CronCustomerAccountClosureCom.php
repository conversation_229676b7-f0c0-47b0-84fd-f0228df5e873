<?php

namespace console\components;

use Yii;
use common\models\CronCustomerAccountClosure;
use yii\db\Query;

class CronCustomerAccountClosureCom
{
    // Check if there is any request & get their data
    private function getClosureRequest($status)
    {
        $reqClosure = CronCustomerAccountClosure::find()
            ->where(['closure_status' => $status])
            ->orderBy(['created_at' => SORT_ASC])
            ->limit(Yii::$app->params['cronAccountClosure.processingLimit'])
            ->all();

        if ($reqClosure) {
            return $reqClosure;
        }

        return false;
    }

    // Check if closure request reach its expiry date
    private function checkRequestExpiry($requestDate)
    {
        $now = time();
        $datediff = $now - $requestDate;

        if (round($datediff / (60 * 60 * 24)) >= Yii::$app->params['cronAccountClosure.expiryMaxDays']) {
            return true;
        }

        return false;
    }

    // delte request after status closure has been set
    private function deleteCustomersRequest($customersIdArray)
    {
        echo $customersIdStr = implode(", ", $customersIdArray);
        CronCustomerAccountClosure::deleteAll('customers_id IN (' . $customersIdStr . ')');
    }

    // set/update cron status for account closure request
    private function setStatus($customersId, $status)
    {
        $CustAccClosureObj = CronCustomerAccountClosure::findOne($customersId);
        $CustAccClosureObj->closure_status = $status;

        if ($status == 2) {
            $CustAccClosureObj->executed_at = time();
        } else {
            $CustAccClosureObj->updated_at = time();
        }

        $CustAccClosureObj->update();
        unset($CustAccClosureObj);
    }

    public function processClosureRequest()
    {
        // Process current batch
        if ($cronCustAccClosureInfo = $this->getClosureRequest(0)) {
            foreach ($cronCustAccClosureInfo as $custAccClosureInfo) {
                $customersObj = new \common\components\CustomersCom();
                $customersInfo = $customersObj->getCustomersInfo($custAccClosureInfo->customers_id);
                $expiryNow = $this->checkRequestExpiry($custAccClosureInfo->created_at);

                // Update customers information
                if ($expiryNow && $customersInfo && ($customersInfo->customers_status == '0')) {
                    // set status = 2 for processing
                    $this->setStatus($custAccClosureInfo->customers_id, 2);
                    if ($customersObj->setCustomersAccountClosure($custAccClosureInfo->customers_id)) {
                        // set status = 1 for completed process
                        $this->setStatus($custAccClosureInfo->customers_id, 1);
                    }
                }
                unset($customersObj);
            }
        }
    }

    public function sendRequestList()
    {
        // Format : Customers ID | Customers Name | G2G Seller | Customers Group Status | Date Request | Requester
        // within last 7 days
        $reqClosure = CronCustomerAccountClosure::find()
            ->select(['customers_id', 'created_at', 'requested_by'])
            ->andWhere(['closure_status' => 0])
            ->andWhere(['>=', 'created_at', strtotime('-7 day')])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        if ($reqClosure) {
            // prepare data for csv
            $num = 0;
            $csvHeader = array('Customers ID', 'Customers Name', 'G2G Seller', 'Customers Group Status', 'Requested Date', 'Requester');
            foreach ($reqClosure as $reqClosureInfo) {
                // get customers basic info
                $custInfoObj = \common\models\Customers::find()
                    ->select(['customers_firstname', 'customers_lastname', 'customers_groups_id'])
                    ->andWhere(['customers_id' => $reqClosureInfo->customers_id])
                    ->one();

                // get requester info
                $custRequesterInfoObj = \common\models\Admin::find()
                    ->select(['admin_email_address'])
                    ->andWhere(['admin_id' => $reqClosureInfo->requested_by])
                    ->one();

                // check if g2g seller
                $custC2cInfoObj = \common\models\C2cCustomers::find()
                    ->select(['customers_id'])
                    ->andWhere(['customers_id' => $reqClosureInfo->customers_id])
                    ->one();

                // get customers groups status
                $custGroupsInfoObj = \common\models\CustomersGroups::find()
                    ->select(['customers_groups_name'])
                    ->andWhere(['customers_groups_id' => $custInfoObj->customers_groups_id])
                    ->one();

                $cust[$num]['customers_id'] = $reqClosureInfo->customers_id;
                $cust[$num]['customers_name'] = $custInfoObj->customers_firstname . ' ' . $custInfoObj->customers_lastname;
                $cust[$num]['g2g_seller'] = ($custC2cInfoObj) ? 'Yes' : 'No';
                $cust[$num]['customers_group_status'] = $custGroupsInfoObj->customers_groups_name;
                $cust[$num]['requested_date'] = date('Y-m-d H:i:s', $reqClosureInfo->created_at);
                $cust[$num]['requester'] = $custRequesterInfoObj->admin_email_address;
                $num++;
            }

            $emailCSVObj = new \common\components\ErrorReportCom();
            return $emailCSVObj->sendCsvMail(
                $cust,
                $csvHeader,
                'Account Closure Report for ' . date('Y/m/d', strtotime('-7 day')) . ' - ' . date('Y/m/d'),
                Yii::$app->params['cronAccountClosure.emailRecipient'],
                Yii::$app->params['cronAccountClosure.emailSubject'],
                Yii::$app->params["noreply"]["default"]
            );
        }
    }

    public function deleteVerificationDoc()
    {
        $s3_prefix = 'user/verification';
        $s3 = Yii::$app->aws->getS3('BUCKET_UPLOAD');
        $doc_type = ['001', '002', '003', '004', '005'];

        $reqClosure = CronCustomerAccountClosure::find()
            ->select(['customers_id', 'created_at', 'requested_by'])
            ->andWhere(['closure_status' => 1])
            ->andWhere(['<=', 'updated_at', strtotime('-9 months')])
            ->orderBy(['created_at' => SORT_ASC])
            ->limit(Yii::$app->params['cronAccountClosure.processingLimit'])
            ->all();

        foreach ($reqClosure as $req) {
            $customer_id = $req->customers_id;

            $doc_list = (new Query)
                ->select(['log_docs_id', 'log_filename'])
                ->from('customers_verification_document_log')
                ->where(['log_customers_id' => $customer_id, 'log_action' => ['UPLOAD', 'MODIFY']])
                ->all();

            $file_list = [];

            foreach ($doc_list as $doc) {
                $doc_id = $doc['log_docs_id'];
                $filename = $doc['log_filename'];
                $ym = substr($filename, 0, 6);
                $s3_path = $doc_id . '/' . (is_numeric($ym) ? $ym . '/' : '') . $customer_id . '/' . $filename;
                $file_list[] = $s3_prefix . '/' . $s3_path;
            }

            $verification_doc = (new Query)
                ->from('customers_verification_document')
                ->where(['customers_id' => $customer_id])
                ->one();

            if ($verification_doc) {
                foreach ($doc_type as $file) {
                    $field_name = 'files_' . $file;
                    if ($verification_doc[$field_name]) {
                        $doc_id = $file;
                        $filename = $verification_doc[$field_name];
                        $ym = substr($filename, 0, 6);
                        $s3_path = $doc_id . '/' . (is_numeric($ym) ? $ym . '/' : '') . $customer_id . '/' . $filename;
                        $file_list[] = $s3_prefix . '/' . $s3_path;
                    }
                }
            }

            if ($s3->deleteContentByList(array_unique($file_list))) {
                if ($verification_doc) {
                    $update_column = [];
                    foreach ($doc_type as $file) {
                        $name = 'files_' . $file;
                        $status = 'files_' . $file . '_status';
                        $locked = 'files_' . $file . '_locked';
                        $update_column[$name] = '';
                        $update_column[$status] = 0;
                        $update_column[$locked] = 1;
                    }
                    Yii::$app->db->createCommand()->update('customers_verification_document', $update_column, ['customers_id' => $customer_id])->execute();
                }
                $req->delete();
            } elseif (empty($file_list)) {
                $req->delete();
            }
        }
    }
}
