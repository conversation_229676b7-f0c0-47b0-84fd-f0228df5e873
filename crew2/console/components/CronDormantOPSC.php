<?php

namespace console\components;

use common\models\DeliverQueue;
use common\models\StorePointsHistory;
use TrueBV\Punycode;
use Yii;
use yii\db\Expression;
use yii\helpers\Json;

/**
 * 1. Store Credit / OffGamers Points account dormant for 5 years or more
 * 2. 2 notifications : 30 days and 7 days
 * 3. zeroize Store Credit / OffGamers Points account after last notification deadline
 * 4. when zeroizing customer accounts, a list of customers affected need to be sent to ANB
 */
class CronDormantOPSC
{

    public $fr_date;    // 2016-12-31 23:59:59
    public $to_date;    // 2016-12-31 23:59:59
    public $action;
    public $limit;
    public $sleep;
    public $data;
    public $exclude_account_status;
    public $exclude_flag_status;
    public $_user = [
        "firstname" => "",
        "lastname" => "",
        "email" => "",
        "last_login" => "",
        "acc_status" => "",
        "cb_status" => "",
        "nrp_status" => "",
        "other_status" => "",
        "sc_cur" => "",
        "sc_bal" => "",
        "sc_bal_usd" => "",
        "sc_last_date" => "",
        "op_bal" => "",
        "op_last_date" => ""
    ];
    public $s3, $requestor, $requestor_role;

    public function __construct()
    {
        $this->exclude_account_status = [0, 9]; // inactive, closed
        $this->exclude_flag_status = [4]; // chargeback

        $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
        $this->requestor = "system";
        $this->requestor_role = "admin";
    }

    public function setData($action, $limit, $sleep, $fr_date, $to_date)
    {
        $option = ["sendq", "sendmail", "zeroize"];

        $this->fr_date = (!empty($fr_date) ? $fr_date : date("Y", strtotime("-6 years")) . "-01-01 00:00:00");
        $this->to_date = (!empty($to_date) ? $to_date : date("Y", strtotime("-6 years")) . "-12-31 23:59:59");
        $this->action = (in_array($action, $option) ? $action : "sendq");
        $this->limit = (int) $limit;
        $this->sleep = (int) $sleep;
    }

    public function process()
    {
        switch ($this->action) {
            case "sendq":
                $this->dormantStoreCredit();
                $this->dormantOP();
                $this->customerInfo();

                $this->sendEmailQueue();
                break;

            case "sendmail":
                $this->sendMail();
                break;

            case "zeroize":
                $this->dormantStoreCredit();
                $this->dormantOP();
                $this->customerInfo();

                $this->zeroize();
                $this->report();
                break;
        }
    }

    private function dormantStoreCredit()
    {
        $res = Yii::$app->db_slave_og
            ->createCommand("SELECT sct.user_id, MAX(sct.created_date) AS max_date
                FROM store_credit_transaction AS sct
                GROUP BY sct.user_id
                HAVING " . ($this->fr_date ? " max_date >= " . strtotime($this->fr_date) . " AND " : "") . "
                    max_date <= " . strtotime($this->to_date))
            ->queryAll();
        foreach ($res as $row) {
            $_row = Yii::$app->db_slave_og
                ->createCommand("SELECT DISTINCT(sct.user_id), sct.new_currency, sct.new_amount, sct.created_date
                    FROM store_credit_transaction AS sct
                    WHERE sct.user_id = " . $row['user_id'] . "
                        AND sct.created_date = " . $row['max_date'])
                ->queryOne();
            if ($_row['new_amount'] > 0) {
                // round up, sell rate
                $usd = Yii::$app->currency->advanceCurrencyConversion($_row['new_amount'], $_row['new_currency'], 'USD', true, 'sell');

                if (!isset($this->data[$row['user_id']])) {
                    $this->data[$row['user_id']] = $this->_user;
                }
                $this->data[$_row['user_id']]["sc_cur"] = $_row['new_currency'];
                $this->data[$_row['user_id']]["sc_bal"] = $_row['new_amount'];
                $this->data[$_row['user_id']]["sc_bal_usd"] = $usd;
                $this->data[$_row['user_id']]["sc_last_date"] = date("Y-m-d H:i:s", $_row['created_date']);
            }
        }
    }

    private function dormantOP()
    {
        $res = Yii::$app->db_slave_offgamers
            ->createCommand("SELECT sp.customers_id, sp.sp_amount, sp.sp_last_modified
                FROM store_points AS sp
                WHERE " . ($this->fr_date ? " sp.sp_last_modified >= '" . $this->fr_date . "' AND " : "") . "
                    sp.sp_last_modified <= '" . $this->to_date . "'")
            ->queryAll();
        foreach ($res as $row) {
            if ($row['sp_amount'] > 0) {
                if (!isset($this->data[$row['customers_id']])) {
                    $this->data[$row['customers_id']] = $this->_user;
                }
                $this->data[$row['customers_id']]["op_bal"] = $row['sp_amount'];
                $this->data[$row['customers_id']]["op_last_date"] = $row['sp_last_modified'];
            }
        }
    }

    private function customerInfo()
    {
        $filename = "/home/<USER>/" . $this->action . ".csv";
        $fp = fopen($filename, "w+");
        fputcsv($fp, array(
            "Customer ID",
            "Customer Email",
            "Last Login",
            "Account Status",
            "CB Status",
            "NRP Status",
            "Other Status",
            "Store Credit Balance",
            "Store Credit USD",
            "Store Credit Last Transaction",
            "OffGamers Point Balance",
            "OffGamers Point Last Transaction"
        ));

        if ($this->data) {
            foreach ($this->data as $uid => $data) {
                $row = Yii::$app->db_slave_offgamers
                    ->createCommand("SELECT c.customers_id, c.customers_firstname, c.customers_lastname, c.customers_email_address,
                        c.customers_status, c.customers_flag, ci.customers_info_date_of_last_logon
                    FROM customers AS c
                    INNER JOIN customers_info AS ci
                        ON ci.customers_info_id = c.customers_id
                    WHERE c.customers_id = " . $uid)
                    ->queryOne();
                if (!in_array($row['customers_status'], $this->exclude_account_status)) {
                    $cb_flag = $nrp_flag = $other_flag = "No";
                    $status = "Active";

                    // customer flag
                    $flag = explode(",", $row["customers_flag"]);
                    if (in_array(1, $flag)) {
                        $other_flag = "Yes";
                    }
                    if (in_array(2, $flag)) {
                        $nrp_flag = "Yes";
                    }
                    if (in_array(4, $flag)) {
                        $cb_flag = "Yes";
                    }

                    // customer status
                    switch ($row['customers_status']) {
                        case 1:
                            $status = "Active";
                            break;
                        case 0:
                            $status = "Inactive";
                            break;
                        case 9:
                            $status = "Closed";
                            break;
                    }

                    if (in_array($row["customers_status"], $this->exclude_account_status) || array_intersect($flag, $this->exclude_flag_status)) {
                        unset($this->data[$uid]);
                    } else {
                        $this->data[$uid]["firstname"] = $row['customers_firstname'];
                        $this->data[$uid]["lastname"] = $row['customers_lastname'];
                        $this->data[$uid]["email"] = $row['customers_email_address'];
                        $this->data[$uid]["last_login"] = $row['customers_info_date_of_last_logon'];
                        $this->data[$uid]["acc_status"] = $status;
                        $this->data[$uid]["cb_status"] = $cb_flag;
                        $this->data[$uid]["nrp_status"] = $nrp_flag;
                        $this->data[$uid]["other_status"] = $other_flag;

                        fputcsv($fp, array(
                            $uid,
                            $row['customers_email_address'],
                            $row['customers_info_date_of_last_logon'],
                            $status,
                            $cb_flag,
                            $nrp_flag,
                            $other_flag,
                            $this->data[$uid]["sc_cur"],
                            $this->data[$uid]["sc_bal"],
                            $this->data[$uid]["sc_bal_usd"],
                            $this->data[$uid]["sc_last_date"],
                            $this->data[$uid]["op_bal"],
                            $this->data[$uid]["op_last_date"]
                        ));
                    }
                }
            }
        }
    }

    private function sendEmailQueue()
    {
        if ($this->data) {
            $this->slackNotice("sendEmailQueue " . count($this->data) . " records");

            /*
             * opt1 : OffGamers Points only
             * opt2 : Store Credit only
             * opt3 : OffGamers Points and Store Credit
             */
            $subject1 = "Your OffGamers Points Is About to Expire";
            $content1 = "Dear {{firstname}},

This email is a reminder that you still have {{op}} OffGamers Points remaining in your OffGamers account.

It seems that you have been away for quite some time, and due to inactivity, the remaining balance points will be forfeited if no action is taken within {{deadline}} days from the date of this email.

To retain the OffGamers Points, you would need to login to our website, www.offgamers.com, to reactivate your account by using one of the methods listed below:

1. Redeem your balance OffGamers Points to OffGamers Store Credits; OR
2. Purchase product(s) that grant OffGamers Points rewards.

And you're good to go!

If you need any assistance, you can reach <NAME_EMAIL>. We are available 24/7.

Cheers,

OffGamers Team";

            $subject2 = "Your OffGamers Store Credits Is About to Expire";
            $content2 = "Dear {{firstname}},

This email is a reminder for you to take action on your account regarding your Store Credits.

It seems that you have been away for quite some time, and due to inactivity, your remaining balance of {{sc}} Store Credits will be forfeited if no action is taken within {{deadline}} days from the date of this email.

In order for you to retain any remaining Store Credits, you would need to login to our website, www.offgamers.com, to reactivate your account by using one of the methods listed below:

1. Top up your Store Credits Balance; OR
2. Make a purchase by using your Store Credits.

And you're good to go!

If you need any assistance, you can reach <NAME_EMAIL>. We are available 24/7.

Cheers,

OffGamers Team";

            $subject3 = "Your OffGamers Points and Store Credits Is About to Expire";
            $content3 = "Dear {{firstname}},

This email is a reminder for you to take action on your account regarding your OffGamers Points and Store Credits.

It seems that you have been away for quite some time, and due to inactivity, your remaining balance of {{op}} OffGamers Points and {{sc}} Store Credits will be forfeited if no action is taken within {{deadline}} days from the date of this email.

In order for you to retain any remaining OffGamers Points and Store Credits, you would need to login to our website, www.offgamers.com to reactivate your account by completing the TWO(2) steps below:

Step 1: For OffGamers Points
1. Redeem your balance OffGamers Points to OffGamers Store Credits; OR
2. Purchase product(s) that grant OffGamers Points rewards.

Step 2: For Store Credits
1. Top up your Store Credits Balance; OR
2. Make a purchase by using your Store Credits.

And you're good to go!

If you need any assistance, you can reach <NAME_EMAIL>. We are available 24/7.

Cheers,

OffGamers Team";

            foreach ($this->data as $uid => $data) {
                $subject = $content = "";

                if ($data["op_bal"] && empty($data["sc_bal"])) {
                    $subject = $subject1;
                    $content = $content1;
                } else if (empty($data["op_bal"]) && $data["sc_bal"]) {
                    $subject = $subject2;
                    $content = $content2;
                } else if ($data["op_bal"] && $data["sc_bal"]) {
                    $subject = $subject3;
                    $content = $content3;
                }

                if ($subject && $data["email"]) {
                    $firstname = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $data["firstname"]);
                    $content = str_replace("{{firstname}}", $firstname, $content);
                    $content = str_replace("{{op}}", $data["op_bal"], $content);
                    $content = str_replace("{{sc}}", $data["sc_cur"] . " " . $data["sc_bal"], $content);
                    $content = str_replace("{{deadline}}", $this->limit, $content);

                    $extra = [
                        'from' => Yii::$app->params["noreply"]["info"],
                        'replyTo' => Yii::$app->params["noreply"]["info"],
                        'to' => (new Punycode)->encode($data["email"]),
                        'subject' => $subject,
                        'body' => $content
                    ];

                    $q = new DeliverQueue();
                    $q->type = "MAIL";
                    $q->id = $uid;
                    $q->extra_info = Json::encode($extra);
                    $q->created_at = time();
                    $q->save();
                }
            }
        }
    }

    private function sendMail()
    {
        do {
            $loop = false;
            $this->slackNotice("sendMail start");

            $model = DeliverQueue::find()->where(['type' => 'MAIL', 'lock_by' => null])->limit($this->limit)->all();
            foreach ($model as $m_dq) {
                if (isset($m_dq->dq_id)) {
                    $loop = true;
                    $m_dq->lock_by = $this->requestor;
                    $m_dq->save();

                    $extra = Json::decode(utf8_encode($m_dq->extra_info));

                    if ($extra["to"]) {
                        Yii::$app->mailer->compose()
                            ->setFrom($extra["from"])
                            ->setReplyTo($extra["replyTo"])
                            ->setTo($extra["to"])
                            ->setSubject($extra["subject"])
                            ->setTextBody($extra["body"])
                            ->send();

                        DeliverQueue::find()->where(['dq_id' => $m_dq->dq_id])->one()->delete();
                    } else {
                        $this->slackNotice('Delivery Queue ID : ' . $m_dq->id . "\n" . Json::encode($extra), "danger");
                    }
                }
            }

            if ($loop) {
                $this->slackNotice("sendMail sleep " . $this->sleep . " seconds");
                sleep($this->sleep);
            }
        } while ($loop);

        $this->slackNotice("sendMail complete");
    }

    private function zeroize()
    {
        if ($this->data) {
            $this->slackNotice("zeroize " . count($this->data) . " records");

            $message = "Dormant Account, no transactions more that 6 years";

            foreach ($this->data as $uid => $data) {
                // Dormant OffGamers Points
                if ($data["op_bal"]) {
                    // zeroize
                    Yii::$app->db_offgamers
                        ->createCommand("UPDATE store_points SET sp_amount = 0.00, sp_last_modified = NOW()
                            WHERE customers_id = " . $uid . " LIMIT 1")
                        ->execute();

                    $m = new StorePointsHistory();
                    $m->customer_id = $uid;
                    $m->store_points_history_date = new Expression('NOW()');
                    $m->store_points_history_debit_amount = (double)$data["op_bal"];
                    $m->store_points_history_after_balance = 0.00;
                    $m->store_points_history_activity_type = "MR";
                    $m->store_points_history_activity_title = "Manual Deduction";
                    $m->store_points_history_activity_desc = $message;
                    $m->store_points_history_activity_desc_show = 1;
                    $m->store_points_history_added_by = $this->requestor;
                    $m->store_points_history_added_by_role = $this->requestor_role;
                    $m->save(false);
                }

                // Dormant Store Credit
                if ($data["sc_bal"]) {
                    $extra = [
                        '_rate_type' => "sell",
                        'requesting_id' => $this->requestor,
                        'requesting_role' => $this->requestor_role,
                        'transaction_type' => "DEDUCT_CREDIT",
                        'activity' => "MR",
                        'currency' => $data["sc_cur"],
                        'amount' => $data["sc_bal"],
                        'comment' => $message,
                        'show_customer' => true,
                        'allow_negative' => false
                    ];

                    $q = new DeliverQueue();
                    $q->type = "SC";
                    $q->id = $uid;
                    $q->extra_info = Json::encode($extra);
                    $q->created_at = time();
                    $q->save();
                }
            }

            $this->slackNotice("zeroize complete");
        }
    }

    private function report()
    {
        if ($this->data) {
            $subject = "Dormant OffGamers Points and Store Credit " . ($this->fr_date ? $this->fr_date . " " : "") . " until " . $this->to_date;
            $filename = "dormant/" . ($this->fr_date ? date("Ymd", strtotime($this->fr_date)) . "_" : "") . date("Ymd", strtotime($this->to_date)) . ".csv";

            $header = [
                "Customer ID",
                "Customer First Name",
                "Customer Last Name",
                "Customer Email",
                "Last Login Date",
                "Account Status",
                "CB Status",
                "NRP Flag",
                "Other Status",
                "SC Currency",
                "SC Balance",
                "SC Balance in USD Value",
                "SC Latest Activities Date & Time",
                "OP Balance",
                "OP Latest Activities Date & Time"
            ];
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, $header);

            foreach ($this->data as $uid => $data) {
                $str = [
                    $uid,
                    $data["firstname"],
                    $data["lastname"],
                    $data["email"],
                    $data["last_login"],
                    $data["acc_status"],
                    $data["cb_status"],
                    $data["nrp_status"],
                    $data["other_status"],
                    $data["sc_cur"],
                    $data["sc_bal"],
                    $data["sc_bal_usd"],
                    $data["sc_last_date"],
                    $data["op_bal"],
                    $data["op_last_date"],
                ];
                foreach ($str as &$val) {
                    $val = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $val);
                }
                fputcsv($fp, $str);
            }

            rewind($fp);
            $this->s3->saveContent($filename, $fp);
            $content = "The following document valid to download 5 days from the date of issue : \n" .
                $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime

            Yii::$app->mailer->compose()
                ->setFrom(Yii::$app->params["noreply"]["default"])
                ->setReplyTo(Yii::$app->params["noreply"]["default"])
                ->setTo(Yii::$app->params["cron.Setting"]["dormantOPSC"]["recipient"])
                ->setSubject($subject)
                ->setTextBody($content)
                ->send();

            $this->slackNotice("zeroize report sent");
        }
    }

    private function slackNotice($text, $type = "good")
    {
        Yii::$app->slack->send("Dormant OffGamers Points and Store Credit : " . $this->action, [
            [
                'color' => $type,
                'text' => $text
            ]
        ], 'DEBUG');
    }

}
