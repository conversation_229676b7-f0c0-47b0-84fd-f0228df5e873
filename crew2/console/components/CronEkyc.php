<?php

namespace console\components;

use Yii;

class CronEkyc
{

    public function __construct()
    {
        
    }

    /**
     * request eKYC from user at TL3, TL4, TL5
     * execute every 1 week
     */
    public function submitEkyc($limit = 5000)
    {
        $cnt = 0;
        $from = "<EMAIL>";
        $type = "eKYC";
        $aft = [];

        // aft group
        $res = Yii::$app->db_slave_offgamers->createCommand("SELECT customers_aft_groups_id, customers_aft_groups_name FROM customers_aft_groups")->queryAll();
        foreach ($res as $row) {
            $aft[$row["customers_aft_groups_id"]] = $row["customers_aft_groups_name"];
        }

        // last eKYC blast mail
        $row = Yii::$app->db_slave_og->createCommand("SELECT id
                FROM deliver_queue 
                WHERE type = '" . $type . "'
                ORDER BY id DESC")
                ->queryOne();
        $last_cid = (isset($row["id"]) && $row["id"]) ? $row["id"] : 0;

        $res = Yii::$app->db_slave_offgamers->createCommand("SELECT c.customers_id, c.customers_email_address, c.customers_aft_groups_id,
                    c.customers_firstname, c.customers_lastname, cvd.files_003_status
                FROM customers AS c
                LEFT JOIN customers_verification_document AS cvd
                ON cvd.customers_id = c.customers_id
                WHERE (cvd.files_003_status != 1 OR cvd.files_003_status IS NULL)
                    AND c.customers_id > " . $last_cid . "
                    AND c.customers_status = 1 
                    AND c.customers_aft_groups_id IN (3, 4, 5)" .
                    ($limit ? " LIMIT " . $limit : ""))
                ->queryAll();
        foreach ($res as $row) {
            $count = Yii::$app->db_slave_og->createCommand("SELECT id
                    FROM deliver_queue
                    WHERE type = '" . $type . "'
                        AND id = " . $row["customers_id"])
                    ->queryScalar();
            if (!$count) {
                // unlock eKYC
                if (is_null($row["files_003_status"])) {
                    Yii::$app->db_offgamers->createCommand("INSERT INTO customers_verification_document (customers_id, files_003_locked)
                        VALUES (" . $row["customers_id"] . ", 0)")
                            ->execute();
                } else {
                    Yii::$app->db_offgamers->createCommand("UPDATE customers_verification_document SET files_003_locked = 0
                        WHERE customers_id = " . $row["customers_id"] . " LIMIT 1")
                            ->execute();
                }

                Yii::$app->db_offgamers->createCommand("INSERT INTO customers_remarks_history (customers_id, date_remarks_added, remarks, remarks_added_by)
                    VALUES (" . $row["customers_id"] . ", NOW(), 'Perform eKYC because customer is " . ($aft[$row["customers_aft_groups_id"]] ?? "Unknown TL") . "', 'system')")
                        ->execute();

                // send notification mail
                $body = "Dear " . $row["customers_firstname"] . " " . $row["customers_lastname"] . "<br />
<br />
Thank you for being our loyal customer.<br />
<br />
Due to recent changes in Regulatory Compliance, you are required to perform KYC (Know Your Customer) verification to verify your identity with supporting documents through the link below.<br />
<br />
<a href='https://account.offgamers.com/verification-form/offgamers'>VERIFY NOW</a><br />
<br />
Alternatively, you may login to your account to submit the requested documents. The documents submitted will be kept strictly confidential.<br />
<br />
If we did not receive your documents in 3 weeks time upon receiving this email, your account's spending limit will be automatically downgraded.<br />
<br />
If you have any inquiries or require further assistance, please do not hesitate to let us know. We’re always ready to assist you the best we can.<br />
<br /><br />
Best regards,<br />
OffGamers Team";

                Yii::$app->mailer->compose()
                        ->setFrom($from)
                        ->setReplyTo($from)
                        ->setTo($row["customers_email_address"])
                        ->setSubject("Regarding Your OffGamers Account #" . $row["customers_id"])
                        ->setHtmlBody($body)
                        ->setTemplate('blank')
                        ->send();

                Yii::$app->db_og->createCommand("INSERT INTO deliver_queue (type, id, created_at)
                    VALUES ('" . $type . "', " . $row["customers_id"] . ", " . time() . ")")
                        ->execute();
                
                $cnt++;
            }
        }

        Yii::$app->slack->send("One Time eKYC blasting", [['color' => "info", 'text' => "Total blast : " . $cnt]], "INFO");
    }

    /**
     * 3 weeks after eKYC mail sent, downgrade TL3, TL4, TL5 to TL2 if no approved eKYC
     */
    public function downgradeTL()
    {
        $cnt = $del = 0;
        $tl = 12; // TL2
        $time = strtotime("-20 day", time());

        Yii::$app->db_og->createCommand("UPDATE deliver_queue
                SET lock_by = 'system'
                WHERE type = 'eKYC'
                    AND created_at <= " . $time)
                ->execute();

        $res = Yii::$app->db_slave_og->createCommand("SELECT dq.id
                FROM deliver_queue AS dq
                WHERE dq.type = 'eKYC'
                    AND dq.lock_by = 'system'
                    AND dq.created_at <= " . $time . "")
                ->queryAll();
        foreach ($res as $row) {
            $cnt++;

            $_row = Yii::$app->db_slave_offgamers->createCommand("SELECT cvd.files_003_status
                    FROM customers_verification_document AS cvd
                    WHERE cvd.customers_id = " . $row["id"])
                    ->queryOne();
            if ($_row['files_003_status'] != 1) {
                $del++;

                Yii::$app->db_offgamers->createCommand("UPDATE customers SET customers_aft_groups_id = " . $tl . " WHERE customers_id = " . $row["id"] . " LIMIT 1")
                        ->execute();

                Yii::$app->db_offgamers->createCommand("INSERT INTO customers_remarks_history (customers_id, date_remarks_added, remarks, remarks_added_by)
                    VALUES (" . $row["id"] . ", NOW(), 'Downgraded to TL2 due to no approved eKYC', 'system')")
                        ->execute();
            }

            Yii::$app->db_og->createCommand("DELETE FROM deliver_queue WHERE type = 'eKYC' AND lock_by = 'system' AND id = " . $row["id"])
                    ->execute();
        }

        Yii::$app->slack->send("One Time Trust Level downgrade", [['color' => "info", 'text' => "Total downgrade : " . $del . "/" . $cnt]], "INFO");
    }

}
