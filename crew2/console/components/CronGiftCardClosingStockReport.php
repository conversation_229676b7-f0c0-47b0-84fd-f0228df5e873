<?php

namespace console\components;

use common\components\CsvReportSender;
use DateTime;
use offgamers\base\components\Currency;
use Yii;
use yii\db\Connection;
use yii\db\Query;

class CronGiftCardClosingStockReport
{
    private Currency $currency;
    private Connection $db;
    private array $emailRecipients;
    private string $noReplyEmail;

    public function __construct()
    {
        $this->db = Yii::$app->db_slave_offgamers;
        $this->emailRecipients = Yii::$app->params["cron.Setting"]["closingStockReport"]["recipient"];
        $this->noReplyEmail = Yii::$app->params["noreply"]["default"];
        $this->currency = Yii::$app->currency;
    }

    public function run(): void
    {
        $data = [];
        $startDate = $this->getSnapshotStartDate();
        $endDate = $this->getSnapshotEndDate();
        $onHoldQtyArray = [];

        $onholdQtyResultSql = $this->getQuery()
            ->select([
                'products_id',
                'total_onhold' => 'COUNT(custom_products_code_id)'
            ])
            ->from('custom_products_code')
            ->where('status_id=:status_id', [':status_id' => -2])
            ->groupBy('products_id')
            ->all($this->db);

        foreach ($onholdQtyResultSql as $row) {
            $onHoldQtyArray[$row['products_id']] = $row['total_onhold'];
        }

        $orderResultSql = $this->getQuery()
            ->select([
                'sh.products_id',
                'sh.products_cat_path',
                'sh.products_quantity',
                'sh.products_actual_quantity',
                'p.products_price',
                'p.products_base_currency',
            ])
            ->from('stock_history sh')
            ->innerJoin('products p', 'sh.products_id = p.products_id AND p.custom_products_type_id = "2"')
            ->where('sh.stock_history_date>=:startDate', [':startDate' => $startDate])
            ->andWhere('sh.stock_history_date<:endDate', [':endDate' => $endDate])
            ->orderBy('sh.products_cat_path')
            ->all($this->db);

        $realDataCount = 0;
        foreach ($orderResultSql as $rowIndex => $row) {
            $unitPriceInfoArray = $this->getUnitPrice(
                $row['products_id'],
                $row['products_actual_quantity'],
                $startDate
            );

            $unitPrice = $unitPriceInfoArray['unit_price'];
            $srpPrice = $this
                ->currency
                ->advanceCurrencyConversion(
                    $row['products_price'],
                    $row['products_base_currency'],
                    'USD',
                    true,
                    'sell');

                if (isset($unitPriceInfoArray['info']) && count($unitPriceInfoArray['info']) > 0) {
                    foreach (($unitPriceInfoArray['info']) as $poIndex => $entityInfo) {

                        if ($poIndex === 0) {
                            $data[] = $this->constructDataRow(
                                $row['products_id'],
                                strip_tags($row['products_cat_path']),
                                $row['products_quantity'],
                                $row['products_actual_quantity'],
                                ($onHoldQtyArray[$row['products_id']] ?? 0),
                                $unitPrice,
                                ($row['products_actual_quantity'] * $unitPrice),
                                $entityInfo['currency'],
                                $entityInfo['unit_price_cur'],
                                ($row['products_actual_quantity'] * $entityInfo['unit_price_cur']),
                                $entityInfo['currency_rate'],
                                $srpPrice,
                                $row['products_base_currency'],
                                $row['products_price'],
                                strip_tags($entityInfo['entity']),
                                $entityInfo['qty'],
                                $entityInfo['unit_price'],
                                $entityInfo['po_id']
                            );
                            $realDataCount++;
                            continue;
                        }

                        $data[] = $this->constructDataRow(
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            $entityInfo['currency'],
                            $entityInfo['unit_price_cur'],
                            ($row['products_actual_quantity'] * $entityInfo['unit_price_cur']),
                            $entityInfo['currency_rate'],
                            '',
                            '',
                            '',
                            strip_tags($entityInfo['entity']),
                            $entityInfo['qty'],
                            $entityInfo['unit_price'],
                            $entityInfo['po_id']
                        );
                    }
                } else {

                    $data[] = $this->constructDataRow(
                        $row['products_id'],
                        strip_tags($row['products_cat_path']),
                        $row['products_quantity'],
                        $row['products_actual_quantity'],
                        ($onHoldQtyArray[$row['products_id']] ?? 0),
                        $unitPrice,
                        ($row['products_actual_quantity'] * $unitPrice),
                        '',
                        '',
                        '',
                        '',
                        $srpPrice,
                        $row['products_base_currency'],
                        $row['products_price']
                    );
                    $realDataCount++;
                }

        }

        $this->sendCsVToEmails($data, $realDataCount);

        unset($onHoldQtyArray);
        unset($orderResultSql);
        unset($data);
    }

    protected function getSnapshotStartDate(): string
    {
        return $this->getCurrentMonthDateTime()->format("Y-m-d 00:00:00");
    }

    protected function getSnapshotEndDate(): string
    {
        return $this->getCurrentMonthDateTime()->format("Y-m-d 00:40:00");
    }

    protected function getCurrentMonthDateTime(): DateTime
    {
        return new DateTime();
    }

    private function getUnitPrice(
        int $productId,
        int $actualQty,
        string $cutOffDate): array
    {
        $poEntityInfo = [];

        if ($actualQty > 0) {
            $remainingQty = $actualQty;
            $totalCost = 0;
            $batchLimit = 0;

            do {
                $hasStock = false;

                $cdKeyInfoResultSql = $this->getQuery()
                    ->select([
                        'zip_qty',
                        'zip_unit_price',
                        'purchase_orders_id',
                    ])
                    ->from('custom_product_vault')
                    ->where('products_id=:productsId', [':productsId' => $productId])
                    ->andWhere('zip_status=:zipStatus', [':zipStatus' => '3'])
                    ->andWhere('zip_date_unzipping<=:cutOffDate', [':cutOffDate' => $cutOffDate])
                    ->orderBy('zip_date_unzipping DESC')
                    ->limit(5)
                    ->offset($batchLimit)
                    ->all($this->db);

                foreach ($cdKeyInfoResultSql as $row) {
                    $hasStock = true;

                    $thisQty = ($remainingQty > $row['zip_qty']) ? $row['zip_qty'] : $remainingQty;

                    if (!empty($row['purchase_orders_id']) && is_numeric($row['purchase_orders_id'])) {
                        $poUnitPriceSelectResult = $this->getQuery()
                            ->select(['pop.products_unit_price', 'pop.products_usd_unit_price', 'po.currency', 'po.currency_usd_value'])
                            ->from('purchase_orders_products pop')
                            ->leftJoin('purchase_orders po', 'po.purchase_orders_id = pop.purchase_orders_id')
                            ->where(
                                'pop.purchase_orders_id=:purchaseOrdersId',
                                [':purchaseOrdersId' => $row['purchase_orders_id']])
                            ->andWhere('pop.products_id=:productsId', [':productsId' => $productId])
                            ->one($this->db);

                        $cdkPrice = $poUnitPriceSelectResult['products_usd_unit_price'] ?? 0;
                        $cdkPrice_cur = $poUnitPriceSelectResult['products_unit_price'] ?? 0;
                        $currency = $poUnitPriceSelectResult['currency'] ?? '';
                        $currency_rate = $poUnitPriceSelectResult['currency_usd_value'] ?? 0;

                        $poCompanySelectResult = $this->getQuery()
                            ->select([
                                'delivery_location',
                                'delivery_name',
                                'purchase_orders_ref_id'
                            ])
                            ->from('purchase_orders')
                            ->where(
                                'purchase_orders_id=:purchaseOrdersId',
                                [':purchaseOrdersId' => $row['purchase_orders_id']])
                            ->one($this->db);

                        if ($thisQty > 0) {
                            $poEntityInfo[] = [
                                'entity' => $poCompanySelectResult['delivery_name'],
                                'qty' => $thisQty,
                                'unit_price' => $cdkPrice,
                                'unit_price_cur' => $cdkPrice_cur,
                                'currency' => $currency,
                                'currency_rate' => $currency_rate,
                                'po_id' => $poCompanySelectResult['purchase_orders_ref_id']
                            ];
                        }

                    } else {
                        $cdkPrice = $row['zip_unit_price'];
                    }

                    $totalCost += $thisQty * $cdkPrice;
                    $remainingQty -= $thisQty;
                }

                $batchLimit += 5;

                unset($cdKeyInfoResultSql);

            } while ($remainingQty > 0 && $hasStock);

            $unitPrice = $totalCost / $actualQty;
        } else {
            $cdkeyInfoSelectResult = $this->getQuery()
                ->select([
                    'zip_qty',
                    'zip_unit_price',
                    'purchase_orders_id',
                ])
                ->from('custom_product_vault')
                ->where('products_id=:productsId', [':productsId' => $productId])
                ->andWhere('zip_status=:zipStatus', [':zipStatus' => '3'])
                ->andWhere('zip_date_unzipping<=:cutOffDate', [':cutOffDate' => $cutOffDate])
                ->orderBy('products_vault_id DESC')
                ->limit(1)
                ->one($this->db);

            if (!empty($cdkeyInfoSelectResult['purchase_orders_id'])
                && is_numeric($cdkeyInfoSelectResult['purchase_orders_id'])) {

                $poUnitPriceSelectResult = $this->getQuery()
                    ->select(['products_usd_unit_price'])
                    ->from('purchase_orders_products')
                    ->where(
                        'purchase_orders_id=:purchaseOrdersId',
                        [':purchaseOrdersId' => $cdkeyInfoSelectResult['purchase_orders_id']])
                    ->andWhere('products_id=:productsId', [':productsId' => $productId])
                    ->one($this->db);

                $unitPrice = $poUnitPriceSelectResult['products_usd_unit_price'] ?? 0;
            } else {
                $unitPrice = $cdkeyInfoSelectResult['zip_unit_price'] ?? 0;
            }

        }

        return [
            'unit_price' => $unitPrice,
            'info' => $poEntityInfo,
        ];
    }

    private function getQuery(): Query
    {
        return new Query();
    }

    private function getCsvFilename(): string
    {
        $currentDateTime = (new DateTime())->format("YmdHis");
        return sprintf(
            "%s_%s_%s.csv",
            strtotime($this->getSnapshotStartDate()),
            strtotime($this->getSnapshotEndDate()),
            $currentDateTime);
    }

    private function constructDataRow(
        $productId = '',
        $categoryPath = '',
        $availableQty = '',
        $actualQty = '',
        $onHoldQty = '',
        $unitCost = '',
        $totalCost = '',
        $currency = '',
        $unit_price_cur = '',
        $total_cost_cur = '',
        $currency_rate = '',
        $srp = '',
        $baseCurrency = '',
        $baseSellingPrice = '',
        $poEntity = '',
        $poQuantity = '',
        $poUnitPrice = '',
        $poId = ''
    ): array
    {
        return [
            'product_id' => $productId,
            'category_path' => $categoryPath,
            'available_qty' => $availableQty,
            'actual_qty' => $actualQty,
            'on_hold_qty' => $onHoldQty,
            'unit_cost' => $unitCost,
            'total_cost' => $totalCost,
            'currency' => $currency,
            'unit_cost_cur' => $unit_price_cur,
            'total_cost_cur' => $total_cost_cur,
            'currency_rate' => $currency_rate,
            'srp' => $srp,
            'base_currency' => $baseCurrency,
            'base_selling_price' => $baseSellingPrice,
            'po_entity' => $poEntity,
            'po_qty' => $poQuantity,
            'po_unit_price' => $poUnitPrice,
            'po_id' => $poId
        ];
    }

    private function sendCsVToEmails(array $data, int $realDataCount)
    {
        $subject = sprintf(
            "Daily Gift Card Closing Stock Report %s",
            $this->getCurrentMonthDateTime()->format("Y-m-d"));

        $header = [
            "Product ID",
            "Cat Path",
            "Available Qty",
            "Actual Qty",
            "On Hold Qty",
            "Unit Cost (USD)",
            "Total Cost (USD)",
            "Cost Currency (Original)",
            "Unit Cost (Original)",
            "Total Cost (Original)",
            "Forex Rate",
            "SRP(USD)",
            "Base Currency",
            "Base Selling Price",
            "PO Entity",
            "PO Qty",
            "PO Unit Price",
            "PO Id",
        ];

        $body = ($realDataCount > 0) ? sprintf( "%d record found", $realDataCount) : "No record found";

        return (new CsvReportSender($this->getCsvFilename()))->sendCsvMail(
            $data,
            $header,
            $body,
            $this->emailRecipients,
            $subject,
            $this->noReplyEmail
        );
    }
}