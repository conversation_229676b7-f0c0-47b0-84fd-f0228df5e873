<?php

namespace console\components;

use Yii;
use yii\db\Query;

class CronMonthlyPaymentGatewaySalesReport
{
    private $s3;
    private int $expiry_time; // Expiry time for s3 bucket link
    private string $email_content; // Contents to be sent to the email
    private string $file_url;
    private array $csv_data; // Data to be written as s3 bucket file

    private string $filename;
    private string $report_name;
    public string $action;
    public string $from_date;
    public string $to_date;

    public function __construct()
    {
        $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
        $this->expiry_time = 432000;
        $this->email_content = "The following document valid to download 5 days from the date of issue : <br><br>";
    }

    /**
     * Setting the below will generate PG Report by Sales Delivered
     * $orders_status = [7, 2, 3];
     * $with_payment_methods_parent_id_filter = true;
     * $with_delivered_sales = true;
     *
     * Setting the below will generate PG Report by Sales (With undelivered)
     * $orders_status = [7, 2, 3, 5, 8];
     * $with_payment_methods_parent_id_filter = false;
     * $with_delivered_sales = false;
     *
     * @param string $action
     * @param string $from_date
     * @param string $to_date
     * @return void
     */
    public function setData(
        string $action,
        string $from_date = '',
        string $to_date = ''
    ) {
        $this->action = $action;

        $this->from_date = !empty($from_date) ? $from_date : date('Y-m-d', strtotime("first day of last month")) . " 00:00:00";
        $this->to_date = !empty($to_date) ? $to_date : date('Y-m-d', strtotime("last day of last month")) . " 23:59:59";
    }

    public function process()
    {
        if ($this->action == 'all') {
            $this->csv_data = $this->getCsvHeaders();
            $this->prepareMonthlyReport();
        }

        if ($this->action == 'delivered') {
            $this->csv_data = $this->getCsvHeaders(true);
            $this->prepareDeliveredMonthlyReport();
        }
        $this->file_url = $this->saveFileToAwsS3();
        $this->sendEmail();
    }

    private function prepareMonthlyReport()
    {
        $this->filename = "report/pg_monthly_sales_report_" . date("YmdHis") . ".csv";
        $this->report_name = "PG Monthly Sales Report from " . $this->from_date . " to " . $this->to_date;

        $orders_status = [7, 2, 3, 5, 8];
        $this->csv_data = array_merge($this->csv_data, $this->getMonthlyOrderList($orders_status));
    }

    private function prepareDeliveredMonthlyReport()
    {
        $this->filename = "report/pg_monthly_sales_report_by_delivered_" . date("YmdHis") . ".csv";
        $this->report_name = "PG Monthly Sales Report By Delivered from " . $this->from_date . " to " . $this->to_date;

        $orders_status = [7, 2, 3];
        $this->csv_data = array_merge($this->csv_data, $this->getMonthlyOrderList($orders_status, true, true));
    }

    private function getMonthlyOrderListQuery(array $orders_status): Query
    {
        $select_columns = [
            'o.orders_id',
            'o.customers_id',
            'o.payment_methods_id',
            'o.payment_methods_parent_id',
            'o.currency',
            'o.currency_value',
            'o.customers_groups_id',
            'o.customers_telephone_country',
            'ot.value',
            'ot2.value AS surcharge',
            'ot3.value AS sub_total',
            'oss.first_date',
            'op.orders_products_id'
        ];

        return (new Query())
            ->select($select_columns)
            ->from('orders AS o')
            ->innerJoin('orders_total AS ot', "o.orders_id = ot.orders_id AND ot.class = 'ot_total'")
            ->innerJoin('orders_total AS ot3', "o.orders_id = ot3.orders_id AND ot3.class = 'ot_subtotal'")
            ->innerJoin('orders_status_stat AS oss', 'o.orders_id = oss.orders_id AND oss.orders_status_id = 7')
            ->leftJoin('orders_extra_info AS oei', "o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id'")
            ->leftJoin('orders_total AS ot2', "o.orders_id = ot2.orders_id AND ot2.class = 'ot_surcharge'")
            ->leftJoin('orders_products AS op', 'o.orders_id = op.orders_id AND custom_products_type_id = 3')
            ->where('oss.first_date>=:from_date', [':from_date' => $this->from_date])
            ->andWhere('oss.first_date<=:to_date', [':to_date' => $this->to_date])
            ->andWhere(['in', 'o.orders_status', $orders_status])
            ->andWhere(['or', 'oei.orders_extra_info_value=0', 'oei.orders_extra_info_value IS NULL'])
            ->groupBy('o.orders_id');
    }

    private function getMonthlyOrderList(
        array $orders_status,
        bool $with_payment_methods_parent_id_filter = false, // Set to true if generate `delivered report`
        bool $with_delivered_sales = false // Set to true if generate `delivered report`
    ): array
    {
        $customer_group_list = $this->getCustomerGroupList();
        $payment_gateway_list = $this->getPaymentGatewayList();

        $query = $this->getMonthlyOrderListQuery($orders_status);

        if ($with_payment_methods_parent_id_filter) {
            $query = $query->andWhere('o.payment_methods_parent_id > 0');
        }

        $rows = $query->all(Yii::$app->db_slave_offgamers);

        $orders_ids = array_column($rows, 'orders_id');

        if ($with_payment_methods_parent_id_filter) {
            $total_delivered_by_order_id_list = $this->getTotalDeliveredAmountFromOrderIds($orders_ids);
        }

        $monthly_orders = [];
        foreach ($rows as $row) {
            $csv_data = [];
            if ($with_delivered_sales) {
                $row['pg_delivered_sales'] = $total_delivered_by_order_id_list[$row['orders_id']] ?? 0;
                if ($row['pg_delivered_sales'] > 0) {
                    if ($row['pg_delivered_sales'] > ($row['value'] - $row['surcharge'])) {
                        $row['pg_delivered_sales'] = $row['value'] - $row['surcharge'];
                    }
                } else {
                    continue;
                }
                $csv_data[] = $this->numberFormat($row['pg_delivered_sales']);
            }

            $csv_data = array_merge([
                $row['customers_telephone_country'] ?: 'Unknown',
                $row['orders_id'],
                $row['first_date'],
                $customer_group_list[$row['customers_groups_id']] ?? $row['customers_groups_id'],
                $row['payment_methods_parent_id'] > 0 ? ($payment_gateway_list[$row['payment_methods_parent_id']] ?? '') : 'Full SC',
                $row['payment_methods_id'] > 0 ? ($payment_gateway_list[$row['payment_methods_id']] ?? '') : 'Full SC',
                $row['currency'],
                $this->numberFormat($row['sub_total'] * $row['currency_value']),
                $this->numberFormat($row['surcharge'] * $row['currency_value']),
                $this->numberFormat($row['value'] * $row['currency_value']),
                $this->numberFormat($row['sub_total']),
                $this->numberFormat($row['surcharge']),
                $this->numberFormat($row['value'])
            ], $csv_data);
            $csv_data[] = $row['customers_id'];
            $csv_data[] = $row['orders_products_id'] ? 'Yes' : '';

            $monthly_orders[] = $csv_data;
        }

        return $monthly_orders;
    }

    // This function send email to the recipient, if failed to send an email, a slack notification will be triggered
    private function sendEmail()
    {
        $this->email_content .= $this->report_name . "<br/>" . $this->file_url;

        $recipients = Yii::$app->params["cron.Setting"]["pgMonthlySalesReport"]["recipient"];
        echo "Sending email to: " . implode(', ', $recipients);
        $mail_status = Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["pgMonthlySalesReport"]["recipient"])
            ->setSubject($this->report_name)
            ->setHtmlBody($this->email_content)
            ->setTemplate('blank')
            ->send();

        if (!$mail_status) { // If email failed
            Yii::$app->slack->send('Failed to send ' . $this->report_name, [
                [
                    'color' => 'warning',
                    'text' => 'Failed to send email to the recipients: ' . implode(', ', $recipients)
                ]
            ]);
        }
    }

    // This functions save `$this->csv_data` into a csv and uploads to aws s3 bucket
    private function saveFileToAwsS3(): string // Returns s3 bucket file link
    {
        $fp = fopen('php://temp', 'w+');
        foreach ($this->csv_data as $row) {
            fputcsv($fp, $row);
        }

        rewind($fp);
        $this->s3->saveContent($this->filename, $fp);

        return $this->s3->getContentUrl($this->filename, true, $this->expiry_time);
    }

    // This functions get the list of total_delivered_amount based on orders_id
    private function getTotalDeliveredAmountFromOrderIds(array $orders_ids): array
    {
        foreach ($orders_ids as $idx => $order_id) {
            $orders_ids[$idx] = (int)$order_id;
        }
        $chunk_size = 5000;
        $total_delivered_by_order_id_list = [];

        foreach (array_chunk($orders_ids, $chunk_size) as $ids) {
            $query = (new Query())
                ->select([
                    'orders_id',
                    'products_good_delivered_price'
                ])
                ->from('orders_products')
                ->where(['in', 'orders_id', $ids]);

            $result = $query->all(Yii::$app->db_slave_offgamers);

            foreach ($result as $r) {
                if (isset($total_delivered_by_order_id_list[$r['orders_id']])) {
                    $total_delivered_by_order_id_list[$r['orders_id']] += $r['products_good_delivered_price'];
                } else {
                    $total_delivered_by_order_id_list[$r['orders_id']] = $r['products_good_delivered_price'];
                }
            }
        }

        return $total_delivered_by_order_id_list;
    }

    // This functions get the list of `payment_methods` and return a lookup list
    private function getPaymentGatewayList(): array
    {
        $query = (new Query())
            ->select([
                'payment_methods_id',
                'payment_methods_title'
            ])
            ->from('payment_methods');
        $result = $query->all(Yii::$app->db_slave_offgamers);

        $payment_gateways = [];
        foreach ($result as $r) {
            $payment_gateways[$r['payment_methods_id']] = $r['payment_methods_title'];
        }

        return $payment_gateways;
    }

    // This functions get the list of `customers_groups` and return a lookup list
    private function getCustomerGroupList(): array
    {
        $query = (new Query())
            ->select([
                'customers_groups_id',
                'customers_groups_name'
            ])
            ->from('customers_groups');
        $result = $query->all(Yii::$app->db_slave_offgamers);

        $customer_groups = [];
        foreach ($result as $r) {
            $customer_groups[$r['customers_groups_id']] = $r['customers_groups_name'];
        }

        return $customer_groups;
    }

    // This functions get the array of columns name for the csv
    private function getCsvHeaders(bool $with_delivered_sales = false): array
    {
        $headers = [];
        $headers[] = [
            'OG Report From',
            $this->from_date,
            ' until ',
            $this->to_date,
        ];
        $header_second_row = [
            'Country',
            'Order Number',
            'Payment Date',
            'Customer Group (When Place Order)',
            'Payment Gateway',
            'Payment Method',
            'Currency',
            'Sub Total',
            'Surcharge',
            'PG Purchase Amount',
            'Sub Total (USD)',
            'Surcharge (USD)',
            'PG Purchase Amount (USD)',
        ];

        if ($with_delivered_sales) {
            $header_second_row[] = 'PG Delivered Amount (USD)';
        }
        $header_second_row[] = 'Customer ID';
        $header_second_row[] = 'SC Reload Order';
        $headers[] = $header_second_row;

        return $headers;
    }

    // This functions acts the same with `number_format()` but with pre-filled params
    private function numberFormat(
        $value,
        int $decimals = 2,
        string $decimal_separator = '.',
        string $thousands_separator = ''
    ): string {
        return number_format($value, $decimals, $decimal_separator, $thousands_separator);
    }
}