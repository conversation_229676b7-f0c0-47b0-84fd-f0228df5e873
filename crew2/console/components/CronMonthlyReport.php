<?php

namespace console\components;

use Yii;
use dpodium\yii2\geoip;

class CronMonthlyReport
{

    private $s3;
    private $slack_title;
    private $content;
    private $expiry_time;

    public function __construct()
    {
        $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
        $this->slack_title = "Monthly Cronjob";
        $this->content = "The following document valid to download 5 days from the date of issue : <br><br>";
        $this->expiry_time = 432000;
    }

    public function resellerSales()
    {
        // Send out process start message
        $slack_message = "Reseller Sales Report Start : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );

        $reseller = implode(",", [6, 8, 14, 16, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]);
        $fr_date = date("Y-m", strtotime("last month")) . "-01 00:00:00";
        $to_date = date("Y-m") . "-01 00:00:00";

        $header = ['Customer ID', 'First Name', 'Last Name', 'Country', 'Product ID', '2nd Layer ID', '2nd Layer Title', '3rd Layer ID', '3rd Layer Title', 'Sales'];
        $filename = 'report/reseller_purchase_report.csv';
        $subject = "Reseller Monthly Report " . date("M Y", strtotime($fr_date));
        $fp = fopen('php://temp', 'w+');
        fputcsv($fp, ["Reseller Monthly Report"]);
        fputcsv($fp, $header);

        $res = Yii::$app->db_slave_offgamers->createCommand("SELECT countries_id, countries_name FROM countries")->queryAll();
        foreach ($res as $row) {
            $ctry_arr[$row["countries_id"]] = $row["countries_name"];
        }

        $res = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT c.customers_id, c.customers_firstname, c.customers_lastname, ci.customer_info_selected_country
                    FROM customers AS c
                    INNER JOIN customers_info AS ci
                        ON c.customers_id=ci.customers_info_id
                    WHERE c.customers_groups_id IN (" . $reseller . ")"
            )
            ->queryAll();
        foreach ($res as $row) {
            $prod = [];
            $_res = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT o.orders_id, op.products_id, op.products_good_delivered_price
                        FROM orders AS o
                        INNER JOIN orders_products AS op
                            ON o.orders_id = op.orders_id
                        INNER JOIN orders_status_stat AS oss
                            ON (o.orders_id = oss.orders_id AND oss.orders_status_id = 7)
                        LEFT JOIN orders_extra_info AS oei
                            ON (o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id' )
                        WHERE oss.first_date >= '" . $fr_date . "' 
                            AND oss.first_date < '" . $to_date . "'
                            AND o.customers_id = '" . $row['customers_id'] . "' 
                            AND o.orders_status IN (7, 2, 3, 8) 
                            AND o.customers_groups_id IN (" . $reseller . ")
                            AND op.parent_orders_products_id = 0
                            AND op.products_good_delivered_price > 0
                            AND op.orders_products_is_compensate = 0
                            AND oei.orders_extra_info_value != 5"
                )
                ->queryAll();
            foreach ($_res as $_row) {
                if (isset($prod[$_row["products_id"]])) {
                    $prod[$_row["products_id"]]["amount"] += $_row["products_good_delivered_price"];
                } else {
                    $_row2 = Yii::$app->db_slave_offgamers
                        ->createCommand(
                            "SELECT products_cat_id_path, products_cat_path
                                FROM products
                                WHERE products_id = " . $_row['products_id']
                        )
                        ->queryOne();
                    $prod[$_row["products_id"]] = [
                        "cat_path" => strip_tags($_row2["products_cat_path"]),
                        "id_path" => $_row2["products_cat_id_path"],
                        "amount" => $_row["products_good_delivered_price"]
                    ];
                }
            }

            foreach ($prod as $pid => $val) {
                $_prod_id = explode("_", $val["id_path"]);
                $_prod_cat = explode(">", $val["cat_path"]);
                $data = [
                    $row["customers_id"],
                    $row["customers_firstname"],
                    $row["customers_lastname"],
                    $ctry_arr[$row["customer_info_selected_country"]],
                    $pid,
                    $_prod_id[2],
                    str_replace("|", " ", $_prod_cat[1]),
                    $_prod_id[3],
                    str_replace("|", " ", isset($_prod_cat[2]) ? $_prod_cat[2] : $val["cat_path"]),
                    $val["amount"]
                ];
                fputcsv($fp, $data);
            }
        }

        rewind($fp);

        // s3 get file url
        $this->s3->saveContent($filename, $fp);
        $content = $this->content . "Reseller Monthly Report<br>" .
            $this->s3->getContentUrl($filename, true, $this->expiry_time);

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["repeatPurchase"]["recipientBDT"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
        // Send out process end message
        $slack_message .= "Reseller Sales Report End : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
    }

    public function newSignUpSales()
    {
        // Send out process start message
        $slack_message = "New Sign Up Sales Report Start : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
        /*
         * 1. New Registration
         * 2. New User with Purchase ( first purchase )
         * 3. Total Unqiue User Purchase
         * 4. New User Sales ( first purchase )
         */

        $start_date = date("Y-m-01 00:00:00", strtotime("-1 month"));
        $stop_date = date("Y-m-01 00:00:00", strtotime("-2 month"));
        $this_year = date("Y", strtotime($start_date));
        $last_year = date("Y", strtotime($start_date . " -1 year"));
        $last_month = date("n", strtotime($start_date)); // without leading zero

        $content = $this->content;

        $subject = "New Sign-Up and Sales, Unique Purchase " . date("M Y", strtotime($start_date));

        $site = [
            "ogm" => "OffGamers",
            "g2g" => "G2G",
            "gmz" => "Gamernizer",
            "shasso" => "Shasso",
            "unknown" => "Unknown",
            "total" => "Total"
        ];
        $status = [
            1 => "Pending",
            2 => "Processing",
            3 => "Completed",
            5 => "Cancel",
            7 => "Verifying",
            8 => "On Hold"
        ];

        // New Registration
        $exec = 0;
        $from_date = $start_date;
        $last_date = $stop_date;
        $result = [];

        $filename_1 = "report/new_sign_up_register.csv";

        $header[] = "";
        do {
            $to_date = date("Y-m-d 00:00:00", strtotime($from_date . " +1 month"));
            $year = date("Y", strtotime($from_date));
            $month = date("n", strtotime($from_date)); // without leading zero
            $header[] = date("M Y", strtotime($from_date));

            $result[$year][$month] = array(
                "ogm" => 0,
                "g2g" => 0,
                "gmz" => 0,
                "shasso" => 0,
                "unknown" => 0,
                "total" => 0
            );

            $res = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT COUNT(ci.customers_info_id) AS register, ci.account_created_site
			FROM customers_info AS ci
			WHERE ci.customers_info_date_account_created >= '" . $from_date . "'
                            AND ci.customers_info_date_account_created < '" . $to_date . "'
			GROUP BY account_created_site"
                )
                ->queryAll();
            foreach ($res as $row) {
                if (preg_match("/ogm/i", $row["account_created_site"])) {
                    $result[$year][$month]["ogm"] += (int)$row["register"];
                } elseif (preg_match("/g2g/i", $row["account_created_site"])) {
                    $result[$year][$month]["g2g"] += (int)$row["register"];
                } elseif (preg_match("/gamernizer/i", $row["account_created_site"])) {
                    $result[$year][$month]["gmz"] += (int)$row["register"];
                } elseif (preg_match("/shasso/i", $row["account_created_site"])) {
                    $result[$year][$month]["shasso"] += (int)$row["register"];
                } else {
                    $result[$year][$month]["unknown"] += (int)$row["register"];
                }

                $result[$year][$month]["total"] += (int)$row["register"];
            }

            $exec++;
            $from_date = date("Y-m-d 00:00:00", strtotime($start_date . " -1 year"));
            $last_date = date("Y-m-d 00:00:00", strtotime($stop_date . " -1 year"));
        } while ($exec < 2);

        if ($result) {
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, ["New Registration"]);
            fputcsv($fp, $header);

            foreach ($site as $key => $name) {
                fputcsv($fp, [
                        $name,
                        (isset($result[$this_year][$last_month][$key]) ? $result[$this_year][$last_month][$key] : ""),
                        (isset($result[$last_year][$last_month][$key]) ? $result[$last_year][$last_month][$key] : "")
                    ]
                );
            }
            rewind($fp);

            $this->s3->saveContent($filename_1, $fp);
            $content .= "New Sign Up Report <br> " . $this->s3->getContentUrl($filename_1, true, $this->expiry_time) . "<br><br>";

            unset($result);
        }

        // New User with Purchase ( first purchase )
        $exec = 0;
        $from_date = $start_date;
        $last_date = $stop_date;
        $result = [];

        $filename_2 = "report/new_sign_up_register_with_first_purchase.csv";

        do {
            $to_date = date("Y-m-d 00:00:00", strtotime($from_date . " +1 month"));
            $year = date("Y", strtotime($from_date));
            $month = date("n", strtotime($from_date)); // without leading zero

            $result[$year][$month] = [
                "count" => 0,
                "amount" => 0,
                "status" => [
                    1 => ["count" => 0, "amount" => 0],
                    2 => ["count" => 0, "amount" => 0],
                    3 => ["count" => 0, "amount" => 0],
                    5 => ["count" => 0, "amount" => 0],
                    7 => ["count" => 0, "amount" => 0],
                    8 => ["count" => 0, "amount" => 0]
                ]
            ];

            $res = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT ci.customers_info_id, ci.customers_info_date_account_created
			FROM customers_info AS ci 
			WHERE ci.customers_info_date_account_created >= '" . $from_date . "'
                            AND ci.customers_info_date_account_created < '" . $to_date . "'"
                )
                ->queryAll();
            foreach ($res as $row) {
                // first purchase
                $_row = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        "SELECT o.orders_id, o.orders_status
                            FROM orders AS o
                            INNER JOIN orders_extra_info AS oei
                                ON oei.orders_id = o.orders_id
                                AND oei.orders_extra_info_key = 'site_id'
                            WHERE o.customers_id = '" . $row["customers_info_id"] . "'
                                AND o.date_purchased >= '" . $row["customers_info_date_account_created"] . "'
                                AND o.date_purchased < '" . $to_date . "'
                                AND oei.orders_extra_info_value != 5
                            ORDER BY o.orders_id
                            LIMIT 1"
                    )
                    ->queryOne();
                if (!empty($_row)) {
                    $_row2 = Yii::$app->db_slave_offgamers
                        ->createCommand(
                            "SELECT ot1.value AS 'ot_subtotal'
                                FROM orders_total AS ot1
                                WHERE ot1.orders_id = " . $_row["orders_id"] . " AND ot1.class = 'ot_subtotal'"
                        )
                        ->queryOne();

                    $result[$year][$month]["count"] += 1;
                    $result[$year][$month]["amount"] += $_row2["ot_subtotal"];
                    $result[$year][$month]["status"][$_row["orders_status"]]["count"] += 1;
                    $result[$year][$month]["status"][$_row["orders_status"]]["amount"] += $_row2["ot_subtotal"];
                }
            }

            $exec++;
            $from_date = date("Y-m-d 00:00:00", strtotime($start_date . " -1 year"));
            $last_date = date("Y-m-d 00:00:00", strtotime($stop_date . " -1 year"));
        } while ($exec < 2);

        if ($result) {
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, ["New User with Purchase ( first purchase )"]);
            fputcsv($fp, [date("M Y", strtotime($last_year . "-" . $last_month . "-01")), "", "", "", "", "", "", "", "", "", "", "", "", "", date("M Y", strtotime($this_year . "-" . $last_month . "-01"))]);

            $str = [];
            for ($i = 0; 2 > $i; $i++) {
                $str[] = "Total Purchase";
                $str[] = "Total Purchase Amount (USD)";
                foreach ($status as $key => $val) {
                    $str[] = $val;
                    $str[] = "";
                }
            }
            fputcsv($fp, $str);
            unset($str);

            $yr = $last_year;
            for ($i = 0; 2 > $i; $i++) {
                $str[] = $result[$yr][$last_month]["count"];
                $str[] = "$" . round($result[$yr][$last_month]["amount"], 2);
                foreach ($status as $key => $val) {
                    $str[] = (isset($result[$yr][$last_month]["status"][$key]["count"]) ? $result[$yr][$last_month]["status"][$key]["count"] : "");
                    $str[] = "$" . (isset($result[$yr][$last_month]["status"][$key]["amount"]) ? round($result[$yr][$last_month]["status"][$key]["amount"], 2) : "0.00");
                }
                $yr = $this_year;
            }
            fputcsv($fp, $str);

            rewind($fp);
            $this->s3->saveContent($filename_2, $fp);
            $content .= "New Sign Up Sales (First purchase) <br>" . $this->s3->getContentUrl($filename_2, true, $this->expiry_time) . "<br><br>";
            unset($result);
        }

        // Total Unqiue User Purchase
        $exec = 0;
        $from_date = $start_date;
        $last_date = $stop_date;
        $result = [];

        $filename_3 = "report/new_sign_up_unique_purchase.csv";

        do {
            $to_date = date("Y-m-d 00:00:00", strtotime($from_date . " +1 month"));
            $year = date("Y", strtotime($from_date));
            $month = date("n", strtotime($from_date)); // without leading zero

            $row = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT COUNT(DISTINCT(o.customers_id)) AS cnt
			FROM orders AS o
			LEFT JOIN orders_extra_info AS oei
                            ON oei.orders_id = o.orders_id
                            AND oei.orders_extra_info_key = 'site_id'
			WHERE o.date_purchased >= '" . $from_date . "' AND o.date_purchased < '" . $to_date . "'
                            AND o.orders_status IN (7, 2, 3)
                            AND oei.orders_extra_info_value != 5"
                )
                ->queryOne();
            $result[$year][$month] = (isset($row["cnt"]) ? $row["cnt"] : 0);

            $exec++;
            $from_date = date("Y-m-d 00:00:00", strtotime($start_date . " -1 year"));
            $last_date = date("Y-m-d 00:00:00", strtotime($stop_date . " -1 year"));
        } while ($exec < 2);

        if ($result) {
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, ["Total Unique User Purchase"]);
            fputcsv($fp, [date("M Y", strtotime($last_year . "-" . $last_month . "-01")), date("M Y", strtotime($this_year . "-" . $last_month . "-01"))]);
            fputcsv($fp, [(isset($result[$last_year][$last_month]) ? $result[$last_year][$last_month] : ""), (isset($result[$this_year][$last_month]) ? $result[$this_year][$last_month] : "")]);

            rewind($fp);
            $this->s3->saveContent($filename_3, $fp);
            $content .= "Total Unique User Purchase <br>" . $this->s3->getContentUrl($filename_3, true, $this->expiry_time) . "<br><br>";
            unset($result);
        }

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["register"]["recipient"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();

        // Send out process end message
        $slack_message .= "New Sign Up Sales Report End : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
    }

    public function mgcProductSales()
    {
        // Send out process start message
        $slack_message = "MGC Product Sales Report Start : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
        $fr_date = date("Y-m", strtotime("last month")) . "-01 00:00:00";
        $to_date = date("Y-m") . "-01 00:00:00";

        $filename = "report/mgcProductSales_" . date("Y") . "_" . date("m") . "_report.csv";
        $header = ['Customer ID', 'Email Address', 'Registered Country', 'Country by Login IP', 'Total Purchase (USD)', 'Last Purchase Date'];
        $data = [];
        $subject = "MGC Product " . date("M Y", strtotime($fr_date)) . " Sales Report";

        $fp = fopen('php://temp', 'w+');
        fputcsv($fp, $header);
        $geoip_obj = new geoip\components\CGeoIP();

        $res = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT o.customers_id, c.customers_email_address,
                        ctry.countries_name, ci.customers_info_account_created_ip,
                        SUM(op.products_good_delivered_price) AS total_purchase, o.date_purchased
                    FROM orders AS o
                    INNER JOIN orders_products AS op
                        ON op.orders_id = o.orders_id
                    INNER JOIN customers AS c
                        ON c.customers_id = o.customers_id
                    INNER JOIN customers_info AS ci
                        ON ci.customers_info_id = o.customers_id
                    LEFT JOIN countries AS ctry
                        ON ctry.countries_iso_code_2 = ci.account_created_country
                    WHERE o.orders_status = 3
                        AND o.date_purchased >= '" . $fr_date . "'
                        AND o.date_purchased < '" . $to_date . "'
                        AND op.products_categories_id = " . Yii::$app->params["cron.Setting"]["mgc"]["categoryID"] . "
                        AND op.products_good_delivered_price > 0
                    GROUP BY o.customers_id"
            )
            ->queryAll();
        foreach ($res as $row) {
            $_row = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT customers_login_ip
                        FROM customers_login_history
                        WHERE customers_id = " . $row["customers_id"] . "
                            AND customers_login_date < '" . $row["date_purchased"] . "'
                        ORDER BY customers_login_date DESC"
                )
                ->queryOne();
            $login_ctry = (isset($_row["customers_login_ip"]) && !empty($_row["customers_login_ip"]) ? $geoip_obj->lookupCountryName($_row["customers_login_ip"]) : "Unknown");
            $reg_ctry = ($row["countries_name"] != null ? $row["countries_name"] : $geoip_obj->lookupCountryName($row["customers_info_account_created_ip"]));

            $str = [
                $row["customers_id"],
                $row["customers_email_address"],
                $reg_ctry,
                $login_ctry,
                $row["total_purchase"],
                $row["date_purchased"]
            ];
            fputcsv($fp, $str);
        }
        rewind($fp);

        $this->s3->saveContent($filename, $fp);
        $content = $this->content . $subject . " <br>" . $this->s3->getContentUrl($filename, true, $this->expiry_time);

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["mgc"]["recipient"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
        // Send out process end message
        $slack_message .= "MGC Product Sales Report End : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
    }

    public function cetKPI2()
    {
        // Send out process start message
        $slack_message = "CET KPI 2 Report Start : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
        /*
         * sales within 30 days
         * - first purchase
         * - repeat purchase for 2 months continuously
         * - not purchase in previous month
         * - only for completed order
         */
        $fr_date = date("Y-m-01 00:00:00", strtotime("-1 month"));
        $to_date = date("Y-m-01 00:00:00");
        $fr_date_2 = date("Y-m-01 00:00:00", strtotime("-2 month"));
        $fr_date_1_time = strtotime($fr_date);
        $fr_date_2_time = strtotime($fr_date_2);

        $current = [];
        $no_purchase = [
            $fr_date_1_time => [],
            $fr_date_2_time => []
        ];
        $filename = "report/cetKPI2_purchase_report.csv";
        $header = ['First Purchase', 'Purchase 2 Months Continuously', 'No Purchase in ' . date("M Y", $fr_date_2_time), 'No Purchase in ' . date("M Y", $fr_date_1_time)];
        $data = [
            "first" => 0,
            "repeat" => 0,
            "alt" => 0,
            "current" => 0
        ];
        $subject = "CET KPI Report 2 " . date("M Y", strtotime($fr_date));

        $fp = fopen('php://temp', 'w+');
        fputcsv($fp, $header);

        // purchase -1 current month
        $res = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT DISTINCT(o.customers_id)
                FROM orders AS o
                LEFT JOIN orders_extra_info AS oei
                    ON oei.orders_id = o.orders_id
                    AND oei.orders_extra_info_key = 'site_id'
                WHERE o.date_purchased >= '" . $fr_date . "'
                    AND o.date_purchased < '" . $to_date . "'
                    AND o.orders_status = 3
                    AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)
                GROUP BY o.customers_id
                ORDER BY o.orders_id"
            )
            ->queryAll();
        foreach ($res as $row) {
            $current[] = $row['customers_id'];

            // first purchase
            $_row = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT o.orders_id
                    FROM orders AS o
                    LEFT JOIN orders_extra_info AS oei
                        ON oei.orders_id = o.orders_id
                        AND oei.orders_extra_info_key = 'site_id'
                    WHERE o.customers_id = " . $row["customers_id"] . "
                        AND o.date_purchased < '" . $fr_date . "'
                        AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)"
                )
                ->queryOne();
            if (!$_row) {
                $data["first"] += 1;
            } else {
                // repeat purchase for 2 months continuously
                $_row = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        "SELECT o.orders_id
                        FROM orders AS o
                        LEFT JOIN orders_extra_info AS oei
                            ON oei.orders_id = o.orders_id
                            AND oei.orders_extra_info_key = 'site_id'
                        WHERE o.customers_id = " . $row["customers_id"] . "
                            AND o.date_purchased >= '" . $fr_date_2 . "'
                            AND o.date_purchased < '" . $fr_date . "'
                            AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)"
                    )
                    ->queryOne();
                if ($_row) {
                    $data["repeat"] += 1;
                } else {
                    // no purchase -2 current month
                    $data["alt"] += 1;
                    $no_purchase[$fr_date_2_time][] = $row['customers_id'];
                }
            }
        }

        // purchase in -2 current month
        $res = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT DISTINCT(o.customers_id)
                FROM orders AS o
                LEFT JOIN orders_extra_info AS oei
                    ON oei.orders_id = o.orders_id
                    AND oei.orders_extra_info_key = 'site_id'
                WHERE o.date_purchased >= '" . $fr_date_2 . "'
                    AND o.date_purchased < '" . $fr_date . "'
                    AND o.orders_status = 3
                    AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)
                GROUP BY o.customers_id
                ORDER BY o.orders_id"
            )
            ->queryAll();
        foreach ($res as $row) {
            // purchase in -2 current month, not purchase in -1 current month
            if (!in_array($row['customers_id'], $current)) {
                $no_purchase[$fr_date_1_time][] = $row['customers_id'];
                $data['current'] += 1;
            }
        }
        fputcsv($fp, $data);
        unset($data, $current);

        rewind($fp);

        $this->s3->saveContent($filename, $fp);
        $content = $this->content . $subject . " <br>" . $this->s3->getContentUrl($filename, true, $this->expiry_time);

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["repeatPurchase"]["recipient"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();

        /*
         * customer info and orders status
         * - no purchase in -1 current month
         * - no purchase in -2 current month
         * - new sign up
         */
        $header = ['Customer ID', 'Email Address', 'Customer Group', 'Sign Up Date', 'Last Purchase Date', 'Order ID', 'Product Name', 'Order Sub-Total (USD$)', 'Order Status'];
        $subject = "Repeat Purchase " . date("M Y", strtotime($to_date)) . " Report";
        $cnt = 1;
        $cgroup = [];

        $res = Yii::$app->db_slave_offgamers
            ->createCommand("SELECT customers_groups_id, customers_groups_name FROM customers_groups")
            ->queryAll();
        foreach ($res as $row) {
            $cgroup[$row['customers_groups_id']] = $row['customers_groups_name'];
        }
        $content = $this->content;

        foreach ($no_purchase as $key => $val) {
            $fp = fopen('php://temp', 'w+');
            fputcsv($fp, $header);

            foreach ($val as $_key => $id) {
                $row = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        "SELECT o.customers_id, c.customers_email_address, ci.customers_info_date_account_created,
                            c.customers_groups_id, o.orders_id, o.date_purchased, ot.value, os.orders_status_name
                        FROM orders AS o
                        INNER JOIN customers AS c
                            ON c.customers_id = o.customers_id
                        INNER JOIN customers_info AS ci
                            ON ci.customers_info_id = c.customers_id
                        LEFT JOIN orders_extra_info AS oei
                            ON oei.orders_id = o.orders_id
                            AND oei.orders_extra_info_key = 'site_id'
                        INNER JOIN orders_total AS ot
                            ON ot.orders_id = o.orders_id
                            AND ot.class = 'ot_subtotal'
                        LEFT JOIN customers_groups AS cg
                            ON cg.customers_groups_id = c.customers_groups_id
                        INNER JOIN orders_status AS os
                            ON os.orders_status_id = o.orders_status
                        WHERE o.customers_id = " . $id . "
                            AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)
                        ORDER BY o.orders_id DESC LIMIT 1"
                    )
                    ->queryOne();

                // orders products
                $_row = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        "SELECT op.products_name, op.products_good_delivered_price, op.products_canceled_price
                        FROM orders_products AS op
                        WHERE op.orders_id = " . $row['orders_id'] . "
                            AND op.products_bundle_id = 0
                            AND op.orders_products_is_compensate = 0
                        ORDER BY op.orders_products_id ASC LIMIT 1"
                    )
                    ->queryOne();
                $_stat = "";
                if (isset($_row["products_canceled_price"]) && ($_row["products_canceled_price"] > 0)) {
                    if ($_row["products_good_delivered_price"] > 0) {
                        $_stat = " - Partial Refund";
                    } else {
                        $_stat = " - Refund";
                    }
                }

                $str = [
                    $row["customers_id"],
                    $row["customers_email_address"],
                    (isset($cgroup[$row["customers_groups_id"]]) ? $cgroup[$row["customers_groups_id"]] : "Unknown"),
                    $row["customers_info_date_account_created"],
                    $row["date_purchased"],
                    $row["orders_id"],
                    (isset($_row["products_name"]) ? $_row["products_name"] : ""),
                    $row["value"],
                    $row["orders_status_name"] . $_stat
                ];
                fputcsv($fp, $str);
            }
            rewind($fp);

            $filename = 'report/' . $cnt . "_No_Purchase.csv";
            $this->s3->saveContent($filename, $fp);
            $content .= "No Purchase " . date("M_Y", $key) . " File " . $cnt . " <br>" .
                $this->s3->getContentUrl($filename, true, $this->expiry_time) . "<br><br>";
            $cnt++;
        }
        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["repeatPurchase"]["recipientBDT"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();

        // new sign up
        $header = ['Customer ID', 'Email Address', 'Customer Group', 'Sign Up Date', 'Sign Up Source', 'First Purchase Date', 'Order ID', 'Product Name', 'Order Sub-Total (USD$)', 'Order Status'];
        $subject = "New Sign Up " . date("M Y", strtotime($to_date)) . " Report";
        $filename = "report/cetKPI2_new_sign_up.csv";
        $total = [
            "OffGamers" => 0,
            "G2G" => 0,
            "Gamernizer" => 0,
            "Shasso" => 0,
            "Unknown" => 0
        ];

        $fp = fopen('php://temp', 'w+');
        fputcsv($fp, $header);

        $res = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT c.customers_id, c.customers_email_address, c.customers_groups_id,
                    ci.customers_info_date_account_created, ci.account_created_site 
                FROM customers_info AS ci
                INNER JOIN customers AS c 
                    ON c.customers_id = ci.customers_info_id 
                WHERE ci.customers_info_date_account_created >= '" . $fr_date . "'
                    AND ci.customers_info_date_account_created < '" . $to_date . "'"
            )
            ->queryAll();
        foreach ($res as $row) {
            // first purchase
            $_row = Yii::$app->db_slave_offgamers
                ->createCommand(
                    "SELECT o.orders_id, o.date_purchased, o.orders_status, ot.value, os.orders_status_name
                    FROM orders AS o
                    INNER JOIN orders_extra_info AS oei
                        ON oei.orders_id = o.orders_id
                        AND oei.orders_extra_info_key = 'site_id'
                    INNER JOIN orders_total AS ot
                        ON ot.orders_id = o.orders_id
                        AND ot.class = 'ot_subtotal'
                    INNER JOIN orders_status AS os
                        ON os.orders_status_id = o.orders_status
                    WHERE o.customers_id = '" . $row["customers_id"] . "'
                        AND o.date_purchased >= '" . $row["customers_info_date_account_created"] . "'
                        AND o.date_purchased < '" . $to_date . "'
                        AND (oei.orders_extra_info_value != 5 OR oei.orders_extra_info_value IS NULL)
                    ORDER BY o.orders_id ASC LIMIT 1"
                )
                ->queryOne();
            if (isset($_row["orders_id"])) {
                // orders products
                $_row2 = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        "SELECT op.products_name, op.products_good_delivered_price, op.products_canceled_price
                        FROM orders_products AS op
                        WHERE op.orders_id = " . $_row['orders_id'] . "
                            AND op.products_bundle_id = 0
                            AND op.orders_products_is_compensate = 0
                        ORDER BY op.orders_products_id ASC LIMIT 1"
                    )
                    ->queryOne();
                if (isset($_row2["products_canceled_price"]) && ($_row2["products_canceled_price"] > 0)) {
                    if ($_row2["products_good_delivered_price"] > 0) {
                        $_row["orders_status_name"] .= " - Partial Refund";
                    } else {
                        $_row["orders_status_name"] .= " - Refund";
                    }
                }
            }

            if (preg_match("/ogm/i", $row["account_created_site"])) {
                $site = "OffGamers";
            } elseif (preg_match("/g2g/i", $row["account_created_site"])) {
                $site = "G2G";
            } elseif (preg_match("/gamernizer/i", $row["account_created_site"])) {
                $site = "Gamernizer";
            } elseif (preg_match("/shasso/i", $row["account_created_site"])) {
                $site = "Shasso";
            } else {
                $site = "Unknown";
            }
            $total[$site] += 1;

            if (($site == "OffGamers") || ($site == "Gamernizer") || isset($_row["orders_id"])) {
                $str = [
                    $row["customers_id"],
                    $row["customers_email_address"],
                    (isset($cgroup[$row["customers_groups_id"]]) ? $cgroup[$row["customers_groups_id"]] : "Unknown"),
                    $row["customers_info_date_account_created"],
                    $site,
                    (isset($_row["orders_id"]) ? $_row["date_purchased"] : ""),
                    (isset($_row["orders_id"]) ? $_row["orders_id"] : ""),
                    (isset($_row["orders_id"]) ? $_row2["products_name"] : ""),
                    (isset($_row["orders_id"]) ? $_row["value"] : ""),
                    (isset($_row["orders_id"]) ? $_row["orders_status_name"] : ""),
                ];
                fputcsv($fp, $str);
            }
        }
        rewind($fp);

        $this->s3->saveContent($filename, $fp);
        $content = $this->content . $subject . " <br>" . $this->s3->getContentUrl($filename, true, $this->expiry_time);
        $body = "";
        foreach ($total as $site => $val) {
            $body .= $site . " : " . $val . "<br />";
        }

        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["repeatPurchase"]["recipientBDT"])
            ->setSubject($subject)
            ->setHtmlBody($content)
            ->setTemplate('blank')
            ->send();
        // Send out process end message
        $slack_message .= "CET KPI 2 Report End : " . date("Y-m-d H:i:s") . "\n";
        Yii::$app->slack->send(
            $this->slack_title,
            [
                [
                    'color' => 'good',
                    'text' => $slack_message,
                ]
            ],
            'INFO'
        );
    }

}
