<?php

namespace console\components;

use Yii;

class CronOrdersCdkeysCom
{

    public function __construct()
    {
        ;
    }

    public static function cleanupOrdersCdkeys($to_date = null)
    {
        if (empty($to_date)) {
            $to_date = strtotime("23:59:59 7 days ago");
        } else {
            $to_date = strtotime($to_date);
        }

        $to_date = date('Y-m-d H:i:s', $to_date);

        $sql = (new \yii\db\Query())
            ->createCommand()
            ->delete('orders_cdkeys', ['<', 'created_at', $to_date])
            ->execute();
        return $sql;
    }


}
