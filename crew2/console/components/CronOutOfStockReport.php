<?php

namespace console\components;

use console\models\Categories;
use console\models\Products;
use yii\db\Query;

use Yii;
use yii\helpers\ArrayHelper;

class CronOutOfStockReport
{

    private $fr_date;
    private $to_date;
    public $s3;

    private $product_lists;

    public function __construct()
    {
        $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
    }

    public function setData($fr_date, $to_date)
    {
        $this->fr_date = (!empty($fr_date)) ? date("Y-m-d 00:00:00", strtotime($fr_date)) : date("Y-m-01 00:00:00", strtotime(date('Y-m-01') . " -1 month"));
        $this->to_date = (!empty($to_date)) ? date("Y-m-d 00:00:00", strtotime($to_date)) : date("Y-m-01 00:00:00", strtotime("now"));
    }

    public function process()
    {
        $this->sendCsvToEmail();
    }

    /**
     * Computes out-of-stock duration
     *
     * @param string $start_date The start date of out-of-stock products
     * @param string $end_date The restock date or report end date (if on-going)
     *
     * @return string Total out-of-stock duration (in HH:MM:SS)
     * */
    public function calculateDuration($start_date, $end_date)
    {
        $out_of_stock_duration = date_diff(date_create($start_date), date_create($end_date));
        $total_hours = ($out_of_stock_duration->days * 24) + $out_of_stock_duration->h;
        $out_of_stock_duration = sprintf('%d:%02d:%02d', $total_hours, $out_of_stock_duration->i, $out_of_stock_duration->s);
        return $out_of_stock_duration;
    }

    /**
     * Get the list of filtered products
     *
     * @return array An array of products.
     */
    private function getProductsList()
    {
        $categories = Categories::getCategorieslist();
        $products = Products::getAllProductsList($categories);
        return $products;
    }

    /**
     * Retrieves data about out-of-stock periods for products.
     *
     * @param string $from_date The start date of the period to check.
     * @param string $to_date The end date of the period to check.
     *
     * @return array An array of rows representing out-of-stock products & periods.
     * */
    private function getOutOfStockData($from_date, $to_date)
    {
        // Get out of stock product within report period
        $query = new Query();
        $rows = $query->from('log_table')
            ->select(['log_id', 'log_time', 'log_products_id', 'log_field_name', 'log_from_value', 'log_to_value'])
            ->where([
                'log_field_name' => 'products_quantity',
                'log_products_id' => ArrayHelper::getColumn($this->product_lists, 'products_id')
            ])
            ->andWhere('log_time>=:from_date', [':from_date' => $from_date])
            ->andWhere('log_time<=:to_date', [':to_date' => $to_date])
            ->all(Yii::$app->db_slave_offgamers);

        // Initiate arrays
        $out_of_stock_times = [];
        $out_of_stock_rows = [];

        $out_of_stock_before_report_period = [];
        $existed_product_ids = [];

        foreach ($rows as $row) {
            $product_id = $row['log_products_id'];
            $existed_product_ids[] = $product_id;
            $from_value = $this->getProductStockValue($row['log_from_value']);
            $to_value = $this->getProductStockValue($row['log_to_value']);
            $log_time = $row['log_time'];

            // Continue looping if value is not numerical, e.g. ''
            // If $from_value == 'n/a' means newly added product
            if ((!is_numeric($from_value) && $from_value != 'n/a') || !is_numeric($to_value)) {
                continue;
            }

            // If out of stock before report period
            if (($from_value != 'n/a' && $from_value <= 0) && $to_value <= 0 && !isset($out_of_stock_before_report_period[$product_id])) {
                $out_of_stock_before_report_period[$product_id] = $log_time;
                continue;
            }

            // If out of stock between report period
            // If $from_value == 'n/a' means newly added product
            if (($from_value > 0 || $from_value == 'n/a') && $to_value <= 0) {
                $out_of_stock_times[$product_id] = $log_time;
                continue;
            }

            // If product restocked
            if ($from_value <= 0 && $to_value > 0) {
                // Set restock time
                $restock_time = $log_time;
                $out_of_stock_row_data = [];

                // Product out of stock and restock within report period
                if (isset($out_of_stock_times[$product_id])) {
                    $out_of_stock_time = $out_of_stock_times[$product_id];
                    // Unset out-of-stock time for the current product
                    unset($out_of_stock_times[$product_id]);
                } else { // Product out of stock before report period and restock within report period
                    $out_of_stock_time = $from_date;
                    $out_of_stock_row_data['out_of_stock_before_report_period'] = 1;
                }
                $out_of_stock_rows[$product_id][] = array_merge($out_of_stock_row_data, [
                    'out_of_stock_datetime' => $out_of_stock_time,
                    'restock_datetime' => $restock_time,
                    'total_out_of_stock_duration' => $this->calculateDuration($out_of_stock_time, $restock_time),
                ]);
            }
        }

        // Add rows for products still out of stock
        // Set the duration to report end date minus out of stock date
        foreach ($out_of_stock_times as $product_id => $out_of_stock_time) {
            $report_end_date = $to_date;
            $out_of_stock_rows[$product_id][] = [
                'out_of_stock_datetime' => $out_of_stock_time,
                'restock_datetime' => null,
                'total_out_of_stock_duration' => $this->calculateDuration($out_of_stock_time, $report_end_date),
            ];
        }

        // Create new table with above data, apply date filters here
        $new_table_rows = [];

        // Rows that are out of stock in report period
        foreach ($out_of_stock_rows as $product_id => $out_of_stock_periods) {
            foreach ($out_of_stock_periods as $out_of_stock_row) {
                $out_of_stock_datetime = $out_of_stock_row['out_of_stock_datetime'];
                $restock_datetime = $out_of_stock_row['restock_datetime'];
                if (($restock_datetime === null || $restock_datetime >= $from_date) && $out_of_stock_datetime <= $to_date) {
                    $new_table_rows[] = [
                        'log_products_id' => $product_id,
                        'out_of_stock_datetime' => $out_of_stock_datetime,
                        'restock_datetime' => $restock_datetime,
                        'total_out_of_stock_duration' => $out_of_stock_row['total_out_of_stock_duration'],
                        'out_of_stock_before' => $out_of_stock_row['out_of_stock_before_report_period'] ?? ''
                    ];
                }
            }
        }

        // Retrieve products that doesn't exist in the current report period and
        // have already out of stock before the report time
        $active_product_ids = ArrayHelper::getColumn($this->product_lists, 'products_id');
        $non_exist_product_ids = array_diff($active_product_ids, array_keys(array_flip($existed_product_ids)));
        $out_of_stock_before_report_period_products = $this->getOutOfStockDataBeforeReportPeriod($non_exist_product_ids, $from_date);

        foreach ($out_of_stock_before_report_period_products as $previous_out_of_stock_product) {
            $new_table_rows[] = [
                'log_products_id' => $previous_out_of_stock_product['log_products_id'],
                'out_of_stock_datetime' => $previous_out_of_stock_product['log_time'],
                'restock_datetime' => null,
                'total_out_of_stock_duration' => $this->calculateDuration($previous_out_of_stock_product['log_time'], $to_date),
                'out_of_stock_before' => 1
            ];
        }
        return $new_table_rows;
    }

    private function getOutOfStockDataBeforeReportPeriod(array $product_ids, string $from_date)
    {
        $checked_stock = [];
        $max_log_time_for_out_of_stock_products = [];

        $get_latest_log_id_command = Yii::$app->db->createCommand(
            "SELECT log_id as log_id 
            FROM log_table FORCE INDEX(idx_log_products_id_time)
            WHERE log_field_name = 'products_quantity'
            AND log_products_id = :product_id
            AND log_time < :from_date
            ORDER BY log_time DESC
            LIMIT 1
        ");

        $max_log_ids = [];
        foreach ($product_ids as $query_product_id) {
            $log_id = $get_latest_log_id_command->bindValues([
                ':product_id' => $query_product_id,
                ':from_date' => $from_date
            ])->queryScalar();
            if ($log_id) {
                $max_log_ids[] = $log_id;
            }
        }

        $log_id_from_products = (new Query())->from('log_table')
            ->select(['log_id', 'log_products_id', 'log_time', 'log_from_value', 'log_to_value'])
            ->where([
                'log_id' => $max_log_ids
            ])
            ->all(Yii::$app->db_slave_offgamers);

        foreach ($log_id_from_products as $log_id_from_product) {
            $product_id = $log_id_from_product['log_products_id'];

            // If product already recorded just skip
            if (in_array($product_id, $checked_stock)) {
                continue;
            }

            $from = $this->getProductStockValue($log_id_from_product['log_from_value']);
            $to = $this->getProductStockValue($log_id_from_product['log_to_value']);
            $log_time = $log_id_from_product['log_time'];

            // $from == 'n/a' = Newly added product
            if ((is_numeric($from) || $from == 'n/a') && is_numeric($to)) {
                $checked_stock[] = $product_id;
                if (($from > 0 || $from == 'n/a') && $to <= 0) {
                    $max_log_time_for_out_of_stock_products[] = [
                        'log_time' => $log_time,
                        'log_products_id' => $product_id
                    ];
                } elseif ($from == 0 && $to == 0) {
                    $max_log_time_for_out_of_stock_products[] = [
                        'log_time' => $from_date,
                        'log_products_id' => $product_id
                    ];
                }
            }
        }

        return $max_log_time_for_out_of_stock_products;
    }

    private function getProductStockValue($value)
    {
        if ($value == 'n/a') {
            return $value;
        }
        return explode(':~:', $value)[1] ?? '';
    }

    /**
     * Relates and filters data.
     *
     * @return array An array of rows representing out-of-stock products & periods.
     * */
    private function getFinalTable()
    {
        // Get the products list
        $products = $this->getProductsList();

        $this->product_lists = $products;

        // Get the out-of-stock data
        $out_of_stock_data = $this->getOutOfStockData($this->fr_date, $this->to_date);

        // Create an associative array with product_id as the key
        $products_assoc = [];

        if (isset($products) && is_array($products)) {
            foreach ($products as $product) {
                $products_assoc[$product['products_id']] = [
                    'products_name' => $product['products_name'],
                    'products_price' => $product['products_price'],
                    'products_base_currency' => $product['products_base_currency'],
                ];
            }
        }

        // Combine out-of-stock data with the products list
        $final_table_rows = [];
        foreach ($out_of_stock_data as $row) {
            $log_product_id = $row['log_products_id'];

            // Check if the product exists in the products list
            if (isset($products_assoc[$log_product_id])) {
                $product = $products_assoc[$log_product_id];
                $final_table_rows[] = [
                    'products_id' => $log_product_id,
                    'products_name' => "\"" . $product['products_name'] . "\"", // Escape double quote else layout will run
                    'out_of_stock_datetime' => $row['out_of_stock_datetime'],
                    'restock_datetime' => $row['restock_datetime'],
                    'out_of_stock_before' => $row['out_of_stock_before'],
                    'total_out_of_stock_duration' => $row['total_out_of_stock_duration'],
                    'products_price' => $product['products_price'],
                    'products_base_currency' => $product['products_base_currency']

                ];
            }
        }

        return $final_table_rows;
    }

    /**
     * Send the report as CSV via email
     *
     * @return bool Whether the email was sent successfully.
     */
    private function sendCsvToEmail()
    {
        $from_date = date("Ymd", strtotime($this->fr_date));
        $to_date = date("Ymd", strtotime($this->to_date));
        $from_date_with_hyphens = date("Y-m-d", strtotime($this->fr_date));

        $data = $this->getFinalTable();
        $header = ['Product ID', 'Product Name', 'Out Of Stock Datetime', 'Restock Datetime', 'Out of stock before ' . $from_date_with_hyphens, 'Total Out Of Stock Duration', 'Product Price', 'Product Base Currency'];

        // Convert the data to CSV format
        $csv_data = implode(",", $header) . "\n";
        foreach ($data as $row) {
            $csv_data .= implode(",", $row) . "\n";
        }

        $title = 'Out Of Stock Duration Report - ' . $from_date . '_' . $to_date;
        $filename = 'report/Out Of Stock Duration Report - ' . date('YmdHis') . ".csv";
        $this->s3->saveContent($filename, $csv_data);
        $content1 = (($data) ? count($data) . ' record found.' : 'No record found.');
        $content2 = "The following document valid to download 5 days from the date of issue : <br>" . $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime

        return Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params["noreply"]["default"])
            ->setReplyTo(Yii::$app->params["noreply"]["default"])
            ->setTo(Yii::$app->params["cron.Setting"]["outOfStockDuration"]["recipient"])
            ->setSubject($title)
            ->setHtmlBody($content1 . "<br>" . $content2)
            ->send();
    }
}