<?php

namespace console\components;

use Yii;
use common\models\CronProcessTrack;

class CronProcessTrackCom
{
    // Set cron start
    public function startCron($cronProcessRow)
    {
        $cronTrackObj = CronProcessTrack::findOne($cronProcessRow->cron_process_track_filename);
        $cronTrackObj->cron_process_track_in_action = 1;
        $cronTrackObj->cron_process_track_start_date = date('Y-m-d H:i:s');
        $cronTrackObj->cron_process_track_failed_attempt = 0;
        $cronTrackObj->save();
    }

    // Set cron end
    public function endCron($cronProcessRow)
    {
        $cronTrackObj = CronProcessTrack::findOne($cronProcessRow->cron_process_track_filename);
        $cronTrackObj->cron_process_track_in_action = 0;
        $cronTrackObj->save();
    }

    // Update cron failed
    public function setCronFailed($cronProcessRow, $failedMessageTitle)
    {
        $cronTrackObj = CronProcessTrack::findOne($cronProcessRow->cron_process_track_filename);
        $cronTrackObj->cron_process_track_failed_attempt = $failedCount + 1;
        $cronTrackObj->cron_process_track_start_date = date('y-m-d H:i:s');
        $cronTrackObj->save();
        // Send notification for max attempt reach for failed cron
        $this->cronFailedMaxAttempt($failedMessageTitle, 'OG cronjob failed at ' . $cronProcessRow->cron_process_track_start_date, $cronProcessRow);
    }

    // Check if cron track exist
    public function cronProcessCheck($cronName)
    {
        $cronTrack = CronProcessTrack::find()
            ->select(['cron_process_track_filename', 'cron_process_track_in_action', 'cron_process_track_start_date','cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process', 'cron_process_track_failed_attempt'])
            ->where(['cron_process_track_filename' => $cronName])
            ->one();
        
        if ($cronTrack) {
            return $cronTrack;
        }

        return false;
    }

    // Check for cron max attempt, now set max 3 tries
    public function cronFailedMaxAttempt($alertTitle, $alertValue, $cronProcessRow)
    {
        if ($cronProcessRow->cron_process_track_failed_attempt < 3) {
            // send notification
            $errorReportObj = new \common\components\ErrorReportCom();
            $errorReportObj->errorReport($alertTitle, $alertValue, 'cron');
            unset($errorReportObj);
        } else {
            $cronTrackObj = CronProcessTrack::findOne($cronProcessRow->cron_process_track_filename);
            $cronTrackObj->cron_process_track_in_action = 0;
            $cronTrackObj->cron_process_track_failed_attempt = 0;
            $cronTrackObj->save();
            unset($cronTrackObj);
        }
    }
}
