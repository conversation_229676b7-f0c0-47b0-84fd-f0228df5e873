<?php

namespace console\components;

use Yii;
use yii\db\Query;

class CronProductSalesReport
{
    protected $fr_date;
    protected $to_date;
    protected $product_cat_path_array = [];
    protected $refund_pm_id_array = [];

    public function setData($fr_date, $to_date)
    {
        $this->fr_date = (!empty($fr_date)) ? date("Y-m-d 00:00:00", strtotime($fr_date)) : date("Y-m-01 00:00:00", strtotime(date('Y-m-01') . " -1 month"));
        $this->to_date = (!empty($to_date)) ? date("Y-m-d 00:00:00", strtotime($to_date)) : date("Y-m-01 00:00:00", strtotime("now"));
    }

    public function process()
    {
        $this->getRefundablePaymentMethodList();
        $this->monthlyAFTProductsSalesReport();
    }

    private function monthlyAFTProductsSalesReport()
    {
        $data = [];

        $sales_activity_array = $this->getSalesActivityList($this->fr_date, $this->to_date);

        foreach ($sales_activity_array as $sales_activity_row) {
            $pid = $sales_activity_row['sales_activities_products_id'];

            if (!isset($data[$pid])) {
                $product_cat_path = $this->explodeProductCatPath($pid, $sales_activity_row['products_cat_path']);

                $data[$pid] = $this->initializeNewData($pid, $sales_activity_row['products_name'], $product_cat_path);
            }

            switch ($sales_activity_row['sales_activities_code']) {
                case 'D':
                case 'RD':
                    if ($sales_activity_row['sales_activities_amount'] > 0) {
                        $data[$pid]['delivered'] = $this->calculateSalesActivityDeliveredAmount(
                            $data[$pid]['delivered'],
                            $sales_activity_row['sales_activities_amount'],
                            $sales_activity_row['sales_activities_orders_id'],
                            $sales_activity_row['sales_activities_code']
                        );
                    }
                    break;

                case 'RVD':
                case 'RVRD':
                    $data[$pid]['on_hold'] = $this->calculateSalesActivityOnHoldAmount(
                        $data[$pid]['on_hold'],
                        $sales_activity_row['sales_activities_amount'],
                        $sales_activity_row['sales_activities_code']
                    );
                    break;
            };
        }

        $cb_lost_array = $this->getChargeBackOrderList($this->fr_date, $this->to_date);

        foreach ($cb_lost_array as $cb_lost_row) {
            $cb_product_array = $this->getReversedOrdersProductsList($cb_lost_row['orders_id'], null);

            foreach ($cb_product_array as $cb_product_row) {
                $total_amount = 0;
                $sub_product_array = [];

                $sub_product_result_array = $this->getReversedOrdersProductsList(null, $cb_product_row['orders_products_id']);

                foreach ($sub_product_result_array as $sub_product_row) {
                    $total_amount += $cb_product_row['orders_products_store_price'] * $sub_product_row['products_reversed_quantity'];

                    if (isset($sub_product_array[$sub_product_row['products_id']])) {
                        $sub_product_array[$sub_product_row['products_id']]['qty'] += $sub_product_row['products_reversed_quantity'];
                        $sub_product_array[$sub_product_row['products_id']]['amt'] += $cb_product_row['orders_products_store_price'];
                    } else {
                        $sub_product_array[$sub_product_row['products_id']] = [
                            'name' => $sub_product_row['products_name'],
                            'qty' => $sub_product_row['products_reversed_quantity'],
                            'amt' => $sub_product_row['orders_products_store_price']
                        ];
                    }
                }

                if (count($sub_product_array)) {
                    if($total_amount == 0){
                        continue;
                    }
                    foreach ($sub_product_array as $pid => $pinfo) {
                        $lost_amount = round(($cb_product_row['products_reversed_price'] / $total_amount) * ($pinfo['qty'] * $pinfo['amt']), 2);

                        if (!isset($data[$pid])) {
                            $data[$pid] = $this->initializeNewData($pid, $pinfo['name'], $this->getCatPath($pid));
                        }

                        $data[$pid]['lost'] += $lost_amount;
                    }
                } else {
                    $pid = $cb_product_row['products_id'];
                    $lost_amount = round($cb_product_row['products_reversed_price'], 2);

                    if (!isset($data[$pid])) {
                        $data[$pid] = $this->initializeNewData($pid, $cb_product_row['products_name'], $this->getCatPath($pid));
                    }

                    $data[$pid]['lost'] += $lost_amount;
                }
            }
        }

        $mail = new \common\components\ErrorReportCom();
        $header = ['Product ID', 'Product Name', 'Game Name', 'Delivered(USD)', 'On Hold(USD)', 'CB Lost(USD)'];

        return $mail->sendCsvMail($data, $header, (($data) ? count($data) . ' record found' : 'No record found'), Yii::$app->params["cron.Setting"]["aftProductSales"]["recipient"], 'Monthly AFT Product Sales Report - ' . $this->fr_date, Yii::$app->params["noreply"]["default"]);
    }

    private function getCatPath($pid)
    {
        if (isset($this->product_cat_path_array[$pid])) {
            return $this->product_cat_path_array[$pid];
        } else {
            $product_path_row = (new Query)
                ->select(["products_cat_path"])
                ->from("products")
                ->where("products_id = " . $pid)
                ->one(Yii::$app->db_slave_offgamers);

            return $this->explodeProductCatPath($pid, $product_path_row['products_cat_path']);
        }
    }

    private function explodeProductCatPath($pid, $products_cat_path)
    {
        if (!empty($products_cat_path)) {
            $cat_path_array = explode('>', $products_cat_path);

            if (!isset($this->product_cat_path_array[$pid])) {
                $this->product_cat_path_array[$pid] = $cat_path_array[1];
            }

            return $cat_path_array[1];
        }

        return '';
    }

    private function getReversedOrdersProductsList($orders_id = null, $orders_products_id = null)
    {
        $query_builder = (new Query())
            ->select(["orders_products_id", "products_name", "products_id", "products_reversed_price", "orders_products_store_price", "products_reversed_quantity"])
            ->from("orders_products");

        if (!empty($orders_id)) {
            $query_builder->where("products_reversed_price > 0");
            $query_builder->andWhere("orders_id = " . $orders_id);
        }

        if (!empty($orders_products_id)) {
            $query_builder->where("products_reversed_quantity > 0");
            $query_builder->andWhere("parent_orders_products_id = " . $orders_products_id);
        }

        return $query_builder->all(Yii::$app->db_slave_offgamers);
    }

    private function getRefundablePaymentMethodList()
    {
        $ppm_array = (new Query())
            ->select(['pm_id'])
            ->from("pipwave_payment_mapper")
            ->where("is_rp IN (1,2)")
            ->andWhere("site_id = 0")
            ->all(Yii::$app->db_slave_offgamers);

        foreach ($ppm_array as $ppm_row) {
            $this->refund_pm_id_array[$ppm_row['pm_id']] = $ppm_row['pm_id'];
        }
    }

    private function getSalesActivityList($fr_date, $to_date)
    {
        return (new Query())
            ->select([
                "sa.sales_activities_orders_id",
                "sa.sales_activities_products_id",
                "sa.sales_activities_code",
                "sa.sales_activities_amount",
                "p.products_cat_path",
                "pd.products_name"
            ])
            ->from("sales_activities sa")
            ->innerJoin('products p', "sa.sales_activities_products_id=p.products_id")
            ->innerJoin("products_description pd", "p.products_id=pd.products_id AND pd.language_id=1 ")
            ->innerJoin("orders o", "sa.sales_activities_orders_id=o.orders_id AND o.payment_methods_id IN(" . implode(',', $this->refund_pm_id_array) . ")")
            ->innerJoin("orders_extra_info oei", "sa.sales_activities_orders_id=oei.orders_id AND oei.orders_extra_info_key='site_id' AND oei.orders_extra_info_value='0' ")
            ->where("sa.sales_activities_code IN ('D', 'RD', 'RVD', 'RVRD')")
            ->andWhere("sa.sales_activities_date >= '{$fr_date}' ")
            ->andWhere("sa.sales_activities_date < '{$to_date}' ")
            ->all(Yii::$app->db_slave_offgamers);
    }

    private function getChargeBackOrderList($fr_date, $to_date)
    {
        return (new Query())
            ->select(["oss.orders_id"])
            ->from("orders_status_stat oss")
            ->innerJoin("orders o", "oss.orders_id=o.orders_id AND o.orders_cb_status = 2 ")
            ->innerJoin("orders_extra_info oei", "o.orders_id=oei.orders_id AND oei.orders_extra_info_key='site_id' AND oei.orders_extra_info_value='0' ")
            ->where("oss.orders_status_id = 3 ")
            ->andWhere("oss.latest_date >= '{$fr_date}'")
            ->andWhere("oss.latest_date < '{$to_date}'")
            ->all(Yii::$app->db_slave_offgamers);
    }

    private function checkOrderIsRefundablePaymentMethod($sales_activities_orders_id)
    {
        $is_rp = false;

        $order_row = (new Query())
            ->select(['payment_methods_id'])
            ->from("orders")
            ->where("orders_id = " . $sales_activities_orders_id)
            ->one(Yii::$app->db_slave_offgamers);

        if (!empty($order_row['payment_methods_id']) && is_numeric($order_row['payment_methods_id'])) {
            if (isset($this->refund_pm_id_array[$order_row['payment_methods_id']])) {
                $is_rp = true;
            }
        }

        return $is_rp;
    }

    private function calculateSalesActivityDeliveredAmount($total_delivered_amount, $sales_activity_delivered_amount, $sales_activities_orders_id, $sales_activities_code)
    {
        $is_rp = $this->checkOrderIsRefundablePaymentMethod($sales_activities_orders_id);

        if ($is_rp) {
            if ($sales_activities_code == 'D') {
                return $total_delivered_amount + $sales_activity_delivered_amount;
            } else {
                return $total_delivered_amount - $sales_activity_delivered_amount;
            }
        }

        return $total_delivered_amount;
    }

    private function calculateSalesActivityOnHoldAmount($total_on_hold_amount, $sales_activity_on_hold_amount, $sales_activities_code)
    {
        if ($sales_activities_code == 'RVD') {
            return $total_on_hold_amount + $sales_activity_on_hold_amount;
        } else {
            return $total_on_hold_amount - $sales_activity_on_hold_amount;
        }
    }

    private function initializeNewData($pid, $name, $game)
    {
        return [
            "pid" => $pid,
            'name' => $name,
            'game' => $game,
            'delivered' => 0,
            'on_hold' => 0,
            'lost' => 0
        ];
    }
}
