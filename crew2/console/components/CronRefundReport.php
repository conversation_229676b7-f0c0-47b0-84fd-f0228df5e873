<?php

namespace console\components;

use Yii;
use yii\db\Query;

class CronRefundReport
{

    public function setData($action, $fr_date, $to_date)
    {
        $this->action = (!empty($action)) ? $action : "daily";
        $this->fr_date = (!empty($fr_date)) ? date("Y-m-d 00:00:00", strtotime($fr_date)) : $this->generateDate("from", $action);
        $this->to_date = (!empty($to_date)) ? date("Y-m-d 00:00:00", strtotime($to_date)) : $this->generateDate("to", $action);
    }

    public function run() 
    {
        switch (strtolower($this->action)) {
            case "daily":
            case "monthly":
                $this->generateRefundReport();
                break;
        }
    }

    public function generateRefundReport()
    {
        $data = [];
        $csv_date = date("Y-m-d");

        $activity_code = "";
        $plus_or_minus_signs = "";
        $product_type_str = "";

        $refund_sales_activity_array = $this->getRefundSalesActivityList($this->fr_date, $this->to_date);

        foreach ($refund_sales_activity_array as $refund_sales_activity_row) {
            $orders_details = $this->getOrderDetail($refund_sales_activity_row["sales_activities_orders_id"]);
            $orders_products_details = $this->getOrderProductDetail($refund_sales_activity_row["sales_activities_orders_products_id"]);
            $entity_sales = $this->getEntitySales($refund_sales_activity_row["sales_activities_orders_id"]);
            $sales_activities_payment_gateway_str = $orders_details['pg_display_name'] . ' - ' . $orders_details['pm_display_name'];
            $discount_coupon_amount = $this->getDiscountCouponAmount($refund_sales_activity_row["sales_activities_orders_id"]);

            if ($refund_sales_activity_row['sales_activities_code'] == "RFD") {
                $activity_code = 'Refund +';
                $plus_or_minus_signs = "";
            } else if ($refund_sales_activity_row['sales_activities_code'] == "RFRD") {
                $activity_code = 'Refund -';
                $plus_or_minus_signs = "-";
            }

            if ($orders_products_details['custom_products_type_id'] == '2') {
                $product_type_str = $orders_products_details['is_ogc'] == '1' ? 'OGC' : ((strpos($orders_products_details['products_model'], 'MGC_') === 0) ? 'MGC' : 'CDK');
            } else if ($orders_products_details['custom_products_type_id'] == '3') {
                $product_type_str = 'SC';
            }

            $sales_refund_amount = $refund_sales_activity_row['sales_activities_amount'];
            $refund_to_pg = $this->getRefundAmount($refund_sales_activity_row['sales_activities_orders_id'], $refund_sales_activity_row['sales_activities_date'], $sales_refund_amount);
            $refund_to_sc = $this->getStoreCreditAmount($refund_to_pg, $sales_refund_amount, $discount_coupon_amount);
            $tax_amount = $refund_to_pg > 0 ? $this->getTaxAmount($refund_sales_activity_row['sales_activities_orders_id']) : 0;
            $order_rate = $orders_details['currency_value'];

            $data[] = [
                'date_time' => $refund_sales_activity_row['sales_activities_date'],
                'code_refund' => $activity_code,
                'order_id' => $refund_sales_activity_row['sales_activities_orders_id'],
                'company_sales' => $entity_sales,
                'order_currency' => $orders_details['currency'],
                'currency_rate' => $order_rate,
                'order_verifying_date' => $orders_details['verifying_date'],
                'order_processing_date' => $orders_details['processing_date'],
                'order_status' => $orders_details['orders_status_name'],
                'category_path' => $orders_products_details['products_cat_path'] . ' > ' . $orders_products_details['products_name'],
                'product_type' => $product_type_str,
                'payment_gateway' => $sales_activities_payment_gateway_str,
                'qty' => $refund_sales_activity_row['sales_activities_quantity'],
                'surcharge' => round($orders_details['surcharge_usd_amt'] * $order_rate, 2),
                'surcharge_usd' => round($orders_details['surcharge_usd_amt'], 2),
                'product_refund_amount_usd' => $plus_or_minus_signs . $sales_refund_amount,
                'refund_pg_usd' => $plus_or_minus_signs . $refund_to_pg,
                'refund_sc_usd' => $plus_or_minus_signs . $refund_to_sc,
                'refund_discount_usd' => $plus_or_minus_signs . $discount_coupon_amount,
                'refund_tax_usd' => $plus_or_minus_signs . $tax_amount,
                'product_refund_amount' => $plus_or_minus_signs . number_format($sales_refund_amount * $order_rate, 2, '.', ''),
                'refund_pg' => $plus_or_minus_signs . number_format($refund_to_pg * $order_rate, 2, '.', ''),
                'refund_sc' => $plus_or_minus_signs . number_format($refund_to_sc * $order_rate, 2, '.', ''),
                'refund_discount' => $plus_or_minus_signs . number_format($discount_coupon_amount * $order_rate, 2, '.', ''),
                'refund_tax' => $plus_or_minus_signs . number_format($tax_amount * $order_rate, 2, '.', ''),
            ];
        }

        $mail = new \common\components\ErrorReportCom();
        $header = array(
            'Date/Time', 'Code (Refund+/-)', 'Order ID', 'Company Sales', 'Order Currency', 'Currency Rate',
            'Order Verifying Date', 'Order Processing Date', 'Order Status', 'Category Path', 'Product Type',
            'Payment Gateway', 'Qty', 'Surcharge', 'Surcharge (USD)', 'Product Refund Amount (USD)', 'Refund-PG (USD)',
            'Refund SC (USD)', 'Refund Discount (USD)', 'Refund-Tax (USD)', 'Product Refund Amount', 'Refund-PG',
            'Refund SC', 'Refund Discount', 'Refund-Tax'
        );

        return $mail->sendCsvMail($data, $header, (($data) ? count($data) . ' record found' : 'No record found'), Yii::$app->params["cron.Setting"]["refundReport"]["recipient"], ucwords($this->action) . " Refund Report - {$csv_date}", Yii::$app->params["noreply"]["default"]);
    }

    private function generateDate($type, $action) 
    {
        if ($type == "from") {
            if ($action == "daily") {
                return date("Y-m-d 00:00:00", strtotime("Yesterday"));
            } else if ($action == "monthly") {
                return date("Y-m-01 00:00:00", strtotime(date('Y-m-01') . " -1 month"));
            }
        } else if ($type == "to") {
            if ($action == "daily") {
                return date("Y-m-d 00:00:00");
            } else if ($action == "monthly") {
                return date("Y-m-01 00:00:00", strtotime("now"));
            }
        }
        return '';
    }

    private function getRefundSalesActivityList($fr_date, $to_date) 
    {
        return (new Query())
            ->select([
                'sa.sales_activities_id', 'sa.sales_activities_orders_id', 'sa.sales_activities_orders_products_id',
                'sa.sales_activities_date', 'sa.sales_activities_code', 'sa.sales_activities_quantity', 'sales_activities_operator',
                'sales_activities_amount'
            ])
            ->from('sales_activities sa')
            ->where('sa.sales_activities_date >= "' . $fr_date . '"')
            ->andWhere('sa.sales_activities_date < "' . $to_date . '"')
            ->andWhere('sa.sales_activities_code IN ("RFD", "RFRD")')
            ->andWhere('sa.sales_activities_amount > 0')
            ->orderBy([
                'sa.sales_activities_date' => SORT_DESC,
                'sa.sales_activities_orders_id' => SORT_ASC
            ])
            ->all(Yii::$app->db_slave_offgamers);
    }

    private function getOrderDetail($orders_id) 
    {
        return (new Query())
            ->select([
                'o.orders_id as orders_id', 'oss.first_date AS verifying_date', 'oss2.first_date AS processing_date',
                'os.orders_status_name as orders_status_name', 'o.payment_methods_parent_id', 'o.payment_methods_id',
                'o.customers_name as customers_name', 'o.currency', 'o.currency_value', 'oei.orders_extra_info_value AS tax_rate',
                'ppm.pg_display_name', 'ppm.pm_display_name', 'ot.value AS surcharge_usd_amt'
            ])
            ->from('orders o')
            ->innerJoin('orders_status os', 'os.orders_status_id = o.orders_status')
            ->leftJoin('orders_extra_info oei', 'oei.orders_id = o.orders_id AND oei.orders_extra_info_key = "tax_percentage"')
            ->leftJoin('orders_status_stat oss', 'oss.orders_id = o.orders_id AND oss.orders_status_id = 7')
            ->leftJoin('orders_status_stat oss2', 'oss2.orders_id = o.orders_id AND oss2.orders_status_id = 2')
            ->leftJoin('orders_extra_info oei2', 'o.orders_id=oei2.orders_id AND oei2.orders_extra_info_key="site_id"')
            ->leftJoin('pipwave_payment_mapper ppm', 'o.payment_methods_id = ppm.pm_id AND ppm.site_id = oei2.orders_extra_info_value')
            ->leftJoin('orders_total ot', 'o.orders_id=ot.orders_id AND ot.class="ot_surcharge"')
            ->where('o.orders_id=' . $orders_id)
            ->limit(1)
            ->one(Yii::$app->db_slave_offgamers);
    }

    private function getOrdersTotalValue($orders_id, $class){
        $value = (new Query())
            ->select([
                'value'
            ])
            ->from('orders_total')
            ->where(['orders_id' => $orders_id, 'class' => $class])
            ->limit(1)
            ->scalar(Yii::$app->db_slave_offgamers);

        return ($value ?? 0);
    }

    private function getDiscountCouponAmount($orders_id)
    {
        return $this->getOrdersTotalValue($orders_id, 'ot_coupon');
    }

    private function getHandlingFeeAmount($orders_id)
    {
        return $this->getOrdersTotalValue($orders_id, 'ot_surcharge');
    }

    private function getOrderProductDetail($orders_product_id) 
    {
        return (new Query())
                ->select([
                    'op.products_id', 'op.products_name', 'op.products_model', 'op.final_price', 'op.custom_products_type_id',
                    'p.products_cat_path', 'IF(FIND_IN_SET(p.products_flag_id, 4), "1", "0") AS is_ogc'
                ])
                ->from('orders_products op')
                ->leftJoin('products p', 'op.products_id = p.products_id')
                ->where('op.orders_products_id = ' . $orders_product_id)
                ->limit(1)
                ->one(Yii::$app->db_slave_offgamers);
    }

    private function getEntitySales($orders_id) 
    {
        $entity_sales = "OG";

        $pipwave_payment = (new Query())
            ->select(['p.settlement_account'])
            ->from('pipwave_payment p')
            ->where('p.orders_id = ' . $orders_id)
            ->limit(1)
            ->one(Yii::$app->db_slave_offgamers);
        
        if ($pipwave_payment) {
            preg_match("/\[([^\]]*)\]/", $pipwave_payment['settlement_account'], $matches);
            $entity_sales = ( (isset($matches[1]) && !empty($matches[1])) ? $matches[1] : $pipwave_payment['settlement_account']);
        }

        return $entity_sales;
    }

    private function getRefundAmount($orders_id, $refund_date, $sales_activity_amount) 
    {
        $refund_details = (new Query())
                ->select(['store_refund_amount'])
                ->from('store_refund')
                ->where('store_refund_trans_id=' . $orders_id)
                ->andWhere('store_refund_date >= "' . $refund_date . '"')
                ->andWhere('store_refund_date <= DATE_ADD("' . $refund_date . '", INTERVAL 600 SECOND)')
                ->limit(1)
                ->one(Yii::$app->db_slave_offgamers);
        $refund_amount = $refund_details['store_refund_amount'] ?? 0;
        
        if ($refund_amount > 0 && $refund_amount > $sales_activity_amount) {
            return $sales_activity_amount;
        }

        return $refund_amount;
    }

    private function getStoreCreditAmount($refund_to_pg, $sales_refund_amount, $discount_coupon_amount)
    {
        $sc_amount = 0;
        if ($refund_to_pg <= 0) {
            $sc_amount = $sales_refund_amount - $discount_coupon_amount;
        }
        if ($sales_refund_amount > $refund_to_pg) {
            $sc_amount = $sales_refund_amount - $refund_to_pg - $discount_coupon_amount;
        }

        // Prevent Currency Rounding Issues
        if($sc_amount < 0.0005){
            $sc_amount = 0;
        }

        return $sc_amount;
    }

    private function getTaxAmount($orders_id) 
    {
        $orders_tax_info = (new Query())
                    ->select(['orders_extra_info_value'])
                    ->from('orders_extra_info')
                    ->where('orders_id=' . $orders_id)
                    ->andWhere('orders_extra_info_key="tax_amount"')
                    ->limit(1)
                    ->one(Yii::$app->db_slave_offgamers);
        
        return $orders_tax_info['orders_extra_info_value'] ?? 0;
    }

}
