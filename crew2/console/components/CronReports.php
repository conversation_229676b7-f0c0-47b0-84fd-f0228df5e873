<?php

namespace console\components;

use common\models\DeliverQueue;
use console\interfaces\ReportJob;
use Exception;
use Yii;
use yii\db\ActiveRecord;
use yii\helpers\Json;

class CronReports
{

    public $requestor;

    public function __construct()
    {
        $this->requestor = 'SYSTEM';
    }

    public function runReports()
    {
        $p = new DeliverQueue();
        $dq = $p->find()->where(['type' => 'REPORT'])->andWhere(['lock_by' => null])->orderBy(['created_at' => SORT_ASC])->one();
        if ($dq) {
            $dq->lock_by = $this->requestor;
            $dq->save();
            $extra = Json::decode($dq->extra_info);

            switch($extra['type']) {
                case "tax":
                    if ($this->runTaxReport($extra['type'], $extra['params'])) {
                        $dq->delete();
                    } else {
                        $dq->lock_by = null;
                        $dq->save();
                    }
                    break;

                case "campaign":
                    $model = (new \backend\models\CampaignForm());
                    $model->cat_id = $extra['params']['cat_id'];
                    $model->payment_method = $extra['params']['payment_method'];
                    $model->ip_country_id = $extra['params']['ip_country_id'];
                    $model->exclude_customer_group = $extra['params']['exclude_customer_group'];
                    $model->use_payment_method = $extra['params']['use_payment_method'];

                    if ($model->load($extra['params'], '')) {
                        if ($model->generateReport()) {
                            $dq->delete();
                        } else {
                            $dq->lock_by = null;
                            $dq->save();
                        }
                    }
                    break;

                case "coupon":
                    $this->generateReportForCoupon($dq, $extra);
                    break;

                case "no-purchase":
                case "sales":
                    if ($extra['type'] == "no-purchase") {
                        $model = (new \backend\models\NoPurchaseReportForm());
                    } else if ($extra['type'] == "sales") {
                        $model = (new \backend\models\SalesReportForm());
                        $model->excl_cat = $extra['params']['excl_cat'];
                    }

                    if ($model->load($extra['params'], '')) {
                        if ($model->generateReport()) {
                            $dq->delete();
                        } else {
                            $dq->lock_by = null;
                            $dq->save();
                        }
                    }
                    break;
            }
        }
    }

    public function runTaxReport($type, $params)
    {

        $rpt = new ReportJob();
        $rpt->type = $type;
        $rpt->params = $params;
        try {
            if ($rpt->taxReport()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception $e) {
            Yii::$app->slack->send(
                    'Fail to generate tax report', array(
                array(
                    'color' => 'warning',
                    'text' => json_encode(['params' => $rpt->params, 'error' => $e->getTraceAsString()]),
                ),
                    )
            );
            return false;
        }
    }

    private function generateReportForCoupon(ActiveRecord $deliverQueue, array $extras): void
    {
        $report = new ReportJob();
        $report->type = $extras['type'];
        $report->params = $extras['params'];

        try {
            if ($report->generateCouponUsageReport()) {
                $deliverQueue->delete();
            } else {
                $deliverQueue->lock_by = null;
                $deliverQueue->save();
            }
        } catch (Exception $exception) {
            echo "Failed generating report. Err msg : {$exception->getMessage()} , {$exception->getFile()} , {$exception->getLine()}\n";
            Yii::$app->slack->send(
                    'Fail to generate Coupon report', array(
                array(
                    'color' => 'warning',
                    'text' => json_encode(['params' => $params, 'error' => $exception->getTraceAsString()]),
                ),
                    )
            );
        }
    }

}
