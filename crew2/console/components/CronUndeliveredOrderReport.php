<?php

namespace console\components;

use Yii;
use yii\db\Query;

class CronUndeliveredOrderReport
{
    protected $fixed_product_type_array = [
        '2' => 'CD Key',
        '3' => 'SC',
        '6' => 'OGC',
        '7' => 'MGC'
    ];

    protected $ogc_product_array = [];
    protected $completed_orders = [];

    public function setData($action, $day1_date, $fr_date, $to_date, $report10_to_date)
    {
        $this->action = (!empty($action)) ? $action : "all";
        $this->day1_date = (!empty($day1_date)) ? date("Y-m-d 00:00:00", strtotime($day1_date)) : date("Y-m-01 00:00:00", strtotime("2021-01-01"));
        $this->fr_date = (!empty($fr_date)) ? date("Y-m-d 00:00:00", strtotime($fr_date)) : date("Y-m-01 00:00:00", strtotime(date('Y-m-01') . " -1 month"));
        $this->to_date = (!empty($to_date)) ? date("Y-m-d 00:00:00", strtotime($to_date)) : date("Y-m-01 00:00:00", strtotime("now"));
        $this->report10_to_date = (!empty($report10_to_date)) ? date("Y-m-d 00:00:00", strtotime($report10_to_date)) : date("Y-m-01 00:00:00", strtotime(date('Y-m-01') . " -1 month"));
    }

    public function process() 
    {
        $this->getOGCProductList();

        switch (strtolower($this->action)) {
            case "all":
                $this->monthlyUndeliveredOrderReport(1);
                $this->monthlyUndeliveredOrderReport(2);
                $this->monthlyUndeliveredOrderLatestStatusReport();
                break;

            case "1":
                $this->monthlyUndeliveredOrderReport(1);
                break;

            case "2":
                $this->monthlyUndeliveredOrderReport(2);
                break;

            case "10":
                $this->monthlyUndeliveredOrderLatestStatusReport();
                break;
        }
    }

    public function monthlyUndeliveredOrderReport($type)
    {
        $data = [];
        $undelivered_order_array = [];
        $csv_date = date('Y-m-d');

        if ($type == 1) {
            $undelivered_order_array = $this->getUndeliveryOrderList($this->fr_date, $this->to_date);
        } else if ($type == 2) {
            $undelivered_order_array = $this->getUndeliveryOrderList($this->day1_date, $this->to_date);
        } else {
            return ;
        }

        foreach($undelivered_order_array as $undelivered_order_row) {
            $data[] = [
                $undelivered_order_row['verified_date'],
                $undelivered_order_row['orders_id'],
                $undelivered_order_row['entity_sales'],
                $undelivered_order_row['payment_gateway'],
                $undelivered_order_row['payment_method'],
                $undelivered_order_row['currency'],
                number_format(($undelivered_order_row['value'] * $undelivered_order_row['currency_value']), 2, '.', ''),
                number_format($undelivered_order_row['tax_value'], 2, '.', ''),
                $undelivered_order_row['currency_value'],
                (double) $undelivered_order_row['value'],
                (double) $undelivered_order_row['tax_value'],
                ($undelivered_order_row['not_deliver_amt'] / (float) $undelivered_order_row['value']),
                $undelivered_order_row['not_deliver_amt'],
                $undelivered_order_row['pt_cd_key'],
                $undelivered_order_row['pt_ogc'],
                $undelivered_order_row['pt_mgc'],
                $undelivered_order_row['pt_sc']
            ];
        }

        $mail = new \common\components\ErrorReportCom();
        $header = array(
            'Order Verified Date', 
            'Order ID',
            'Company Sale',
            'Payment Gateway',
            'Payment Method',
            'Currency',
            'Subtotal Amount',
            'Tax',
            'Order Rate',
            'Subtotal Amount (USD)',
            'Tax (USD)',
            'Undelivered %',
            'Undelivered Amount	',
            'CDKey',
            'OGC',
            'MGC',
            'SC'
        );

        return $mail->sendCsvMail($data, $header, (($data) ? count($data) . ' record found' : 'No record found'), Yii::$app->params["cron.Setting"]["undeliveredOrder"]["recipient"], 'Monthly Undelivery Report ' . $type . ' - ' . $csv_date, Yii::$app->params["noreply"]["default"]);
    }

    public function monthlyUndeliveredOrderLatestStatusReport()
    {
        $data = [];
        $csv_date = date('Y-m-d');

        // Get Cancel Orders
        $cancelled_order_array = $this->getCancelledOrderList();
        foreach($cancelled_order_array as $cancelled_order_row) {
            $data[] = [
                $cancelled_order_row['verified_date'],
                $cancelled_order_row['orders_id'],
                '',
                $cancelled_order_row['entity_sales'],
                $cancelled_order_row['payment_gateway'],
                $cancelled_order_row['payment_method'],
                '', // Refund SC Date
                $cancelled_order_row['currency'],
                number_format(($cancelled_order_row['value'] * $cancelled_order_row['currency_value']), 2, '.', ''),
                '', // Delivered Amount
                '', // Refund To PG
                '', // Refund to SC
                '', // Minus Refund
                number_format($cancelled_order_row['value'] * $cancelled_order_row['currency_value'], 2, '.', ''),
                number_format($cancelled_order_row['tax_value'], 2, '.', ''),
                $cancelled_order_row['currency_value'],
                number_format($cancelled_order_row['value'], 2, '.', ''),
                '', // Deliver Amount (USD)
                number_format($cancelled_order_row['tax_value'], 2, '.', ''),
                '', // Refund To PG (USD)
                '', // Refund to SC (USD)
                '', // Minus Refund (USD)
                number_format($cancelled_order_row['value'], 2, '.', ''),
            ];
        }

        // Latest Activities Undelivered Orders
        $latest_order_array = $this->getLatestActivityOrderList();

        foreach ($latest_order_array as $latest_order_row) {
            $sc_refund_date = '';
            
            $order_product_amount_breakdown = [];
            $product_type_array = [];
            $order_product_array = $this->getOrderProductList($latest_order_row['orders_id']);
            
            foreach ($order_product_array as $order_product_row) {
                $refund_to_pg = $refund_to_sc = 0;

                $product_type = $this->getProductType($order_product_row['custom_products_type_id'], $order_product_row['products_id'], $order_product_row['products_model']);

                $deliver_amt = $this->calculateDeliverAmount($latest_order_row['orders_id'], $order_product_row['orders_products_id'], $this->fr_date, $this->to_date, 0, ['RD'], ['D']);
                
                $op_pos_refund_array = $this->getSalesActivityRefundList($latest_order_row['orders_id'], $order_product_row['orders_products_id'], $this->fr_date, $this->to_date);
                
                foreach ($op_pos_refund_array as $op_pos_refund_row) {
                    list($act_date, $act_time) = explode(' ', $op_pos_refund_row['sales_activities_date']);
                    list($hr, $min, $sec) = explode(':', $act_time);

                    $formatted_time = $act_date . ' ' . $hr . ':' . $min;

                    if (!isset($order_product_amount_breakdown[$formatted_time])) {
                        $order_product_amount_breakdown[$formatted_time] = $this->getStoreRefundAmount($latest_order_row['orders_id'], $formatted_time);
                    }

                    if ($order_product_amount_breakdown[$formatted_time] > 0) {
                        if ($order_product_amount_breakdown[$formatted_time] >= $op_pos_refund_row['sales_activities_amount']) {
                            $order_product_amount_breakdown[$formatted_time] -= $op_pos_refund_row['sales_activities_amount'];
        
                            $refund_to_pg += $op_pos_refund_row['sales_activities_amount'];
                        } else {
                            $refund_to_pg += $order_product_amount_breakdown[$formatted_time];
        
                            $refund_to_sc += ($op_pos_refund_row['sales_activities_amount'] - $order_product_amount_breakdown[$formatted_time]);
                            $sc_refund_date .= $formatted_time . " ";
                            
                            $order_product_amount_breakdown[$formatted_time] = 0;
                        }
                    } else {
                        $refund_to_sc += $op_pos_refund_row['sales_activities_amount'];
                        $sc_refund_date .= $formatted_time . " ";
                    }
                }

                $neg_delivery = $this->getSalesDeliverTotalAmount($latest_order_row['orders_id'], $order_product_row['orders_products_id'], $this->fr_date, $this->to_date, ['RFRD']);

                if (isset($product_type_array[$product_type]['deliver'])) {
                    $product_type_array[$product_type]['deliver'] += $deliver_amt;
                } else {
                    $product_type_array[$product_type]['deliver'] = $deliver_amt;
                }
        
                if (isset($product_type_array[$product_type]['refund_pg'])) {
                    $product_type_array[$product_type]['refund_pg'] += $refund_to_pg;
                } else {
                    $product_type_array[$product_type]['refund_pg'] = $refund_to_pg;
                }
        
                if (isset($product_type_array[$product_type]['refund_sc'])) {
                    $product_type_array[$product_type]['refund_sc'] += $refund_to_sc;
                    $product_type_array[$product_type]['refund_sc_date'] .= $sc_refund_date;
                } else {
                    $product_type_array[$product_type]['refund_sc'] = $refund_to_sc;
                    $product_type_array[$product_type]['refund_sc_date'] = $sc_refund_date;
                }
        
                if (isset($product_type_array[$product_type]['minus_refund'])) {
                    $product_type_array[$product_type]['minus_refund'] += $neg_delivery;
                } else {
                    $product_type_array[$product_type]['minus_refund'] = $neg_delivery;
                }

            }

            foreach($this->fixed_product_type_array as $product_type_key => $product_type_value) {
                if ( (isset($product_type_array[$product_type_key]['deliver']) && $product_type_array[$product_type_key]['deliver'] > 0) 
                    || ( isset($product_type_array[$product_type_key]['refund_pg']) && $product_type_array[$product_type_key]['refund_pg'] > 0 ) 
                    || ( isset($product_type_array[$product_type_key]['refund_sc']) && $product_type_array[$product_type_key]['refund_sc'] > 0) 
                    || ( isset($product_type_array[$product_type_key]['minus_refund']) && $product_type_array[$product_type_key]['minus_refund'] > 0) ) {

                    $data[] = [
                        $latest_order_row['verified_date'],
                        $latest_order_row['orders_id'],
                        $product_type_value ?? '',
                        $latest_order_row['entity_sales'],
                        $latest_order_row['payment_gateway'],
                        $latest_order_row['payment_method'],
                        (isset($product_type_array[$product_type_key]['refund_sc_date']) ? $product_type_array[$product_type_key]['refund_sc_date'] : ''),
                        $latest_order_row['currency'],
                        number_format(((double) $latest_order_row['tax_value'] * $latest_order_row['currency_value']), 2, '.', ''),
                        (isset($product_type_array[$product_type_key]['deliver']) ? $product_type_array[$product_type_key]['deliver'] * $latest_order_row['currency_value'] : 0) , // Delivered Amount
                        (isset($product_type_array[$product_type_key]['refund_pg']) ? $product_type_array[$product_type_key]['refund_pg'] * $latest_order_row['currency_value'] : 0), // Refund To PG
                        (isset($product_type_array[$product_type_key]['refund_sc']) ? $product_type_array[$product_type_key]['refund_sc'] * $latest_order_row['currency_value'] : 0),
                        (isset($product_type_array[$product_type_key]['minus_refund']) ? $product_type_array[$product_type_key]['minus_refund'] * $latest_order_row['currency_value'] : 0), // Minus Refund
                        '', //Canceled
                        (double) $latest_order_row['tax_value'],
                        (double) $latest_order_row['currency_value'],
                        number_format($latest_order_row['value'], 2, '.', ''),
                        (isset($product_type_array[$product_type_key]['deliver']) ? $product_type_array[$product_type_key]['deliver'] : 0),
                        number_format($latest_order_row['tax_value'], 2, '.', ''),
                        (isset($product_type_array[$product_type_key]['refund_pg']) ? $product_type_array[$product_type_key]['refund_pg'] : 0),
                        (isset($product_type_array[$product_type_key]['refund_sc']) ? $product_type_array[$product_type_key]['refund_sc'] : 0),
                        (isset($product_type_array[$product_type_key]['minus_refund']) ? $product_type_array[$product_type_key]['minus_refund'] : ''),
                        '', // Canceled (USD)
                    ];
                }
            }
        }

        $mail = new \common\components\ErrorReportCom();
        $header = array(
            'Order Verified Date',
            'Order ID',
            'Product Type',
            'Company Sale',
            'Payment Gateway',
            'Payment Method',
            'Refund SC Date',
            'Currency',
            'Subtotal Amount',
            'Delivered Amount',
            'Refund To PG',
            'Refund to SC',
            'Minus Refund',
            'Canceled',
            'Tax',
            'Order Rate',
            'Subtotal Amount (USD)',
            'Deliver Amount (USD)',
            'Tax (USD)',
            'Refund To PG (USD)',
            'Refund to SC (USD)',
            'Minus Refund (USD)',
            'Canceled (USD)',
        );

        return $mail->sendCsvMail($data, $header, (($data) ? count($data) . ' record found' : 'No record found'), Yii::$app->params["cron.Setting"]["undeliveredOrder"]["recipient"], 'Monthly Undelivery Report 10 - ' . $csv_date, Yii::$app->params["noreply"]["default"]);
    }

    private function getSettlementAccount($orders_id)
    {
        $pipwave_payment = (new Query())
            ->select(['p.settlement_account'])
            ->from('pipwave_payment p')
            ->where('p.orders_id = ' . $orders_id)
            ->limit(1)
            ->one(Yii::$app->db_slave_offgamers);
        if ($pipwave_payment) {
            preg_match("/\[([^\]]*)\]/", $pipwave_payment['settlement_account'], $matches);
            return ((isset($matches[1]) && !empty($matches[1])) ? $matches[1] : $pipwave_payment['settlement_account']);
        }

        return 'OG';
    }

    private function getUndeliveryOrderList($fr_date, $to_date) 
    {
        $data = [];

        $undelivered_orders = Yii::$app->db_slave_offgamers
            ->createCommand('select o.orders_id, o.currency, o.currency_value, o.date_purchased, ot.value
            , oss.first_date AS verified_date, ott.value AS tax_value, ppm.pm_display_name, ppm.pg_display_name
            from orders o
            INNER JOIN orders_status_history a ON o.orders_id = a.orders_id
            INNER JOIN orders_total ot ON o.orders_id = ot.orders_id AND ot.class = "ot_subtotal"
            INNER JOIN orders_status_stat oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 7
            LEFT JOIN orders_total ott ON o.orders_id = ott.orders_id AND ott.class = "ot_gst"
            LEFT JOIN pipwave_payment_mapper ppm ON o.payment_methods_id = ppm.pm_id AND ppm.site_id = 0
            INNER JOIN
            (
                select MAX(orders_status_history_id) as id 
                from orders_status_history
                where orders_id IN (
                    select o.orders_id
                    from orders o
                    FORCE INDEX (index_date_purchased)
                    INNER JOIN
                        orders_extra_info oei
                            ON o.orders_id = oei.orders_id
                                AND oei.orders_extra_info_key = "site_id"
                                AND oei.orders_extra_info_value = "0"
                    WHERE o.date_purchased >= "' . $fr_date . '"
                        AND o.date_purchased < "' . $to_date . '"
                        AND o.orders_status IN (2,3,5,7,8)
                )
                AND date_added < "' . $to_date . '"
                AND orders_status_id != 0
                GROUP BY orders_id
            ) b
            ON a.orders_status_history_id = b.id
            WHERE a.orders_status_id IN (2,7)
            ORDER BY o.orders_id ASC
            ')
            ->queryAll();

        foreach ($undelivered_orders as $row) {
            $product_type_array = array();

            $entity_sales = $this->getSettlementAccount($row['orders_id']);

            $not_deliver_amt =  $this->calculateDeliverAmount($row['orders_id'], null, $row['date_purchased'], $to_date, $row['value']);

            if ($not_deliver_amt > 0.01) {
                $order_product_array = $this->getOrderProductList($row['orders_id']);

                $product_type = 0;
                
                foreach ($order_product_array as $order_product_row) {
                    $op_not_deliver_amt = $this->calculateDeliverAmount($row['orders_id'], $order_product_row['orders_products_id'], $row['date_purchased'], $to_date, ((float) $order_product_row['final_price'] * $order_product_row['products_quantity']));

                    if ($op_not_deliver_amt > 0.01) {
                        $product_type = $this->getProductType($order_product_row['custom_products_type_id'], $order_product_row['products_id'], $product_type, $order_product_row['products_model']);

                        if (isset($product_type_array[$product_type])) {
                            $product_type_array[$product_type] += $op_not_deliver_amt;
                        } else {
                            $product_type_array[$product_type] = $op_not_deliver_amt;
                        }
                    }
                }
            
                $data[] = [
                    'verified_date' => $row['verified_date'],
                    'orders_id' => $row['orders_id'],
                    'entity_sales' => $entity_sales,
                    'payment_gateway' => $row['pg_display_name'] ?? 'Full SC',
                    'payment_method' => $row['pm_display_name'] ?? 'Full SC',
                    'currency' => $row['currency'],
                    'currency_value' => $row['currency_value'],
                    'tax_value' => $row['tax_value'],
                    'value' => $row['value'],
                    'tax_value' => $row['tax_value'],
                    'date_purchased' => $row['date_purchased'],
                    'not_deliver_amt' => $not_deliver_amt,
                    'pt_cd_key' => (isset($product_type_array[2]) ? $product_type_array[2] : ''),
                    'pt_sc' => (isset($product_type_array[3]) ? $product_type_array[3] : ''),
                    'pt_ogc' => (isset($product_type_array[6]) ? $product_type_array[6] : ''),
                    'pt_mgc' => (isset($product_type_array[7]) ? $product_type_array[7] : ''),
                ];
            }
        }

        return $data;
    }
    
    private function getCancelledOrderList() 
    {
        $data = [];

        $cancelled_orders = Yii::$app->db_slave_offgamers
            ->createCommand('select o.orders_id, o.currency, o.currency_value, o.date_purchased, ot.value
            , oss.first_date AS verified_date, ott.value AS tax_value, ppm.pm_display_name, ppm.pg_display_name
            from orders o
            INNER JOIN orders_status_history a ON o.orders_id = a.orders_id
            INNER JOIN orders_total ot ON o.orders_id = ot.orders_id AND ot.class = "ot_subtotal"
            INNER JOIN orders_status_stat oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 7
            LEFT JOIN orders_total ott ON o.orders_id = ott.orders_id AND ott.class = "ot_gst"
            LEFT JOIN pipwave_payment_mapper ppm ON o.payment_methods_id = ppm.pm_id AND ppm.site_id = 0
            INNER JOIN
            (
                select MAX(orders_status_history_id) as id 
                from orders_status_history
                where orders_id IN (
                    select o.orders_id
                    from orders o
                    INNER JOIN orders_extra_info oei 
                        ON o.orders_id = oei.orders_id 
                        AND oei.orders_extra_info_key = "site_id" 
                        AND oei.orders_extra_info_value = "0"
                    WHERE o.orders_id IN (
                        SELECT DISTINCT(orders_id)
                        FROM orders_status_history
                        WHERE date_added >= "' . $this->fr_date . '"
                        AND date_added < "' . $this->to_date . '"
                        AND orders_status_id = 5
                    )
                    AND o.date_purchased >= "' . $this->day1_date . '"
                    AND o.date_purchased < "' . $this->report10_to_date . '"
                )
                AND orders_status_id IN (2,3,7,8)
                AND date_added >= "' . $this->day1_date . '"
                AND date_added < "' . $this->report10_to_date . '"
                GROUP BY orders_id
            ) b ON a.orders_status_history_id = b.id
            WHERE a.orders_status_id IN (2,7)
            ORDER BY o.orders_id ASC')
            ->queryAll();

            foreach ($cancelled_orders as $row) {
                $entity_sales = $this->getSettlementAccount($row['orders_id']);

                $not_deliver_amt =  $this->calculateDeliverAmount($row['orders_id'], null, $row['date_purchased'], $this->to_date, $row['value']);

                if ($not_deliver_amt > 0.01) {
                    $data[] = [
                        'verified_date' => $row['verified_date'],
                        'orders_id' => $row['orders_id'],
                        'entity_sales' => $entity_sales,
                        'payment_gateway' => $row['pg_display_name'] ?? 'Full SC',
                        'payment_method' => $row['pm_display_name'] ?? 'Full SC',
                        'currency' => $row['currency'],
                        'currency_value' => $row['currency_value'],
                        'tax_value' => $row['tax_value'],
                        'value' => $row['value'],
                        'tax_value' => $row['tax_value'],
                        'date_purchased' => $row['date_purchased']
                    ];
                }
            }

            return $data;
    }

    private function getLatestActivityOrderList() 
    {
        $data = [];

        $latest_act_orders = Yii::$app->db_slave_offgamers
            ->createCommand('select o.orders_id, o.currency, o.currency_value, o.date_purchased, ot.value
            , oss.first_date AS verified_date, ott.value AS tax_value, ppm.pm_display_name, ppm.pg_display_name
            from orders o
            INNER JOIN orders_status_history a ON o.orders_id = a.orders_id
            INNER JOIN orders_total ot ON o.orders_id = ot.orders_id AND ot.class = "ot_subtotal"
            INNER JOIN orders_status_stat oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 7
            LEFT JOIN orders_total ott ON o.orders_id = ott.orders_id AND ott.class = "ot_gst"
            LEFT JOIN pipwave_payment_mapper ppm ON o.payment_methods_id = ppm.pm_id AND ppm.site_id = 0
            INNER JOIN (   
                
                select MAX(orders_status_history_id) as id 
                from orders_status_history
                where orders_id IN (

                    select o.orders_id
                    from orders o
                    INNER JOIN orders_extra_info oei 
                        ON o.orders_id = oei.orders_id 
                        AND oei.orders_extra_info_key = "site_id" 
                        AND oei.orders_extra_info_value = "0"
                    WHERE o.orders_id IN (

                        SELECT DISTINCT(sales_activities_orders_id)
                        FROM sales_activities
                        WHERE sales_activities_date >= "' . $this->fr_date . '"
                        AND sales_activities_date < "' . $this->to_date . '"

                    ) 
                    AND o.date_purchased >= "' . $this->day1_date . '"
                    AND o.date_purchased < "' . $this->report10_to_date . '"
                    AND o.orders_status IN (2,3,7,8)
                    
                )
                AND orders_status_id != 0
                AND date_added < "' . $this->report10_to_date . '"
                GROUP BY orders_id
            ) b ON a.orders_status_history_id = b.id

            WHERE a.orders_status_id IN (2,7)
            ORDER BY o.orders_id ASC')
            ->queryAll();

        foreach ($latest_act_orders as $row) {
            $entity_sales = $this->getSettlementAccount($row['orders_id']);

            $not_deliver_amt = $this->calculateDeliverAmount($row['orders_id'], null, $row['date_purchased'], $this->report10_to_date, $row['value']);

            if ($not_deliver_amt > 0.01) {
                $data[] = [
                    'verified_date' => $row['verified_date'],
                    'orders_id' => $row['orders_id'],
                    'entity_sales' => $entity_sales,
                    'payment_gateway' => $row['pg_display_name'] ?? 'Full SC',
                    'payment_method' => $row['pm_display_name'] ?? 'Full SC',
                    'currency' => $row['currency'],
                    'currency_value' => $row['currency_value'],
                    'tax_value' => $row['tax_value'],
                    'value' => $row['value'],
                    'tax_value' => $row['tax_value'],
                    'date_purchased' => $row['date_purchased']
                ];
            }
        }

        return $data;
    }

    private function calculateDeliverAmount($order_id, $orders_products_id, $date_purchased, $to_date, $initial_value, $pos_sales_activity_code_array =["D", "RFD"], $neg_sales_activity_code_array =["RD", "RFRD"]) 
    {
        $pos_amt = $this->getSalesDeliverTotalAmount($order_id, $orders_products_id, $date_purchased, $to_date, $pos_sales_activity_code_array);
        $neg_amt = $this->getSalesDeliverTotalAmount($order_id, $orders_products_id, $date_purchased, $to_date, $neg_sales_activity_code_array);

        $final_deliver_amt = (float) $initial_value - (float) $pos_amt + (float) $neg_amt;
        
        return $final_deliver_amt;
    }

    private function getSalesDeliverTotalAmount($orders_id, $orders_products_id, $fr_date, $to_date, $sales_activities_code_array) 
    {
        $queryBuilder = (new Query())
            ->select(['SUM(sales_activities_amount) as total_amount'])
            ->from('sales_activities')
            ->where('sales_activities_orders_id=' . $orders_id)
            
            ->andWhere('sales_activities_code IN ("' . implode('","',$sales_activities_code_array) . '")')
            ->andWhere('sales_activities_date > "' . $fr_date . '"')
            ->andWhere('sales_activities_date < "' . $to_date . '"');
        
        if (!empty($orders_products_id)) {
            $queryBuilder->andWhere('sales_activities_orders_products_id=' . $orders_products_id);
        }
        
        $delivery = $queryBuilder->limit(1)
            ->one(Yii::$app->db_slave_offgamers);

        return $delivery['total_amount'];
    }

    private function getOGCProductList() 
    {
        $ogc_result_array = (new Query())
            ->select(['products_id'])
            ->from('products')
            ->where('custom_products_type_id = 2')
            ->andWhere('FIND_IN_SET(products_flag_id, 4)')
            ->all(Yii::$app->db_slave_offgamers);

        foreach ($ogc_result_array as $ogc_result_row) {
            $this->ogc_product_array[] = $ogc_result_row['products_id'];
        }
    }

    private function getOrderProductList($orders_id) 
    {
        $order_product_array = (new Query())
            ->select(['orders_products_id', 'products_id', 'products_model', 'custom_products_type_id', 'final_price', 'products_quantity'])
            ->from('orders_products')
            ->where('orders_id = ' . $orders_id)
            ->andWhere('orders_products_is_compensate = 0')
            ->andWhere('parent_orders_products_id = 0')
            ->all(Yii::$app->db_slave_offgamers);
        
        return $order_product_array;
    }

    private function getProductType($custom_products_type_id, $products_id, $product_model) 
    {
        $product_type = $custom_products_type_id;
        if ($product_type == 0) {
            $sub_product = (new Query())
                ->select(['custom_products_type_id', 'products_model'])
                ->from('orders_products')
                ->where('parent_orders_products_id = ' . $products_id)
                ->limit(1)
                ->one(Yii::$app->db_slave_offgamers);

            if ($sub_product) {
                $product_type = $sub_product['custom_products_type_id'];
                $product_model = $sub_product['products_model'];
            }
        }

        if ($product_type == 2) {
            if (strpos($product_model, 'MGC_') === 0) {
                $product_type = 7;
            } else if (in_array($products_id, $this->ogc_product_array)) {
                $product_type = 6;
            }
        }

        return $product_type;
    }

    private function getSalesActivityRefundList($orders_id, $orders_products_id, $fr_date, $to_date) 
    {
        return (new Query())
            ->select(['sales_activities_date', 'sales_activities_amount'])
            ->from('sales_activities')
            ->where('sales_activities_orders_id=' . $orders_id)
            ->andWhere('sales_activities_orders_products_id=' . $orders_products_id)
            ->andWhere('sales_activities_code = "RFD" ')
            ->andWhere('sales_activities_date > "' . $fr_date . '"')
            ->andWhere('sales_activities_date < "' . $to_date . '"')
            ->all(Yii::$app->db_slave_offgamers);         
    }

    private function getStoreRefundAmount($orders_id, $formatted_time) 
    {
        $store_refund = (new Query())
            ->select(['store_refund_amount'])
            ->from('store_refund')
            ->where('store_refund_trans_id = ' . $orders_id)
            ->andWhere('store_refund_date >= "' . $formatted_time . ':00' . '"')
            ->andWhere('store_refund_date <= "' . $formatted_time . ':59' . '"')
            ->limit(1)
            ->one(Yii::$app->db_slave_offgamers);

        return $store_refund ? $store_refund['store_refund_amount'] : 0;
    }
}
