<?php

namespace console\components;

use Yii;
use common\components\CDKey;
use common\components\ZipFile;

class CronUnviewedCDK
{

    public function __construct()
    {
        ;
    }

    public function setData($fr_date, $to_date, $product)
    {
        $this->fr_date = (!empty($fr_date) ? $fr_date : date("Y-m-01 00:00:00", strtotime("-9 months")));
        $this->to_date = (!empty($to_date) ? $to_date : date("Y-m-t 23:59:59", strtotime($this->fr_date . " +5 months")));
        $this->product = (!empty($product) ? $product : "");
    }

    public function run()
    {
        $cpc_obj = new CDKey();
        $zip_obj = new ZipFile();

        $subject = "Unviewed CDK " . $this->fr_date . " until " . $this->to_date;
        $filename_1 = date("Ymd", strtotime($this->fr_date)) . "_" . date("Ymd", strtotime($this->to_date)) . ".csv";
        $filename_2 = "unviewed_cdk.zip";

        $header = [
            "Order ID",
            "Order Date",
            "Product Name",
            "Product ID",
            "Purchase Order",
            "Cost (USD)",
            "Selling Price",
            "File Type",
            "CD Key Info"
        ];
        $fp = fopen('php://temp', 'w+');
        fputcsv($fp, $header);

        if ($this->product) {
            $sql = "SELECT o.orders_id, o.date_purchased 
                    FROM orders AS o 
                    INNER JOIN orders_products AS op 
                        ON op.orders_id = o.orders_id 
                    WHERE o.date_purchased >= '" . $this->fr_date . "' 
                        AND o.date_purchased <= '" . $this->to_date . "' 
                        AND op.products_id IN (" . $this->product . ") 
                    GROUP BY o.orders_id";
        } else {
            $sql = "SELECT o.orders_id, o.date_purchased 
                    FROM orders AS o 
                    WHERE o.date_purchased >= '" . $this->fr_date . "' 
                        AND o.date_purchased <= '" . $this->to_date . "'";
        }

        $res = Yii::$app->db_slave_offgamers
                ->createCommand($sql)
                ->queryAll();
        foreach ($res as $row) {
            $_sql = "SELECT op.products_name, op.products_id,
                        po.purchase_orders_id, po.purchase_orders_ref_id,
                        cpc.custom_products_code_id, cpc.to_s3, cpc.code_date_added, cpc.file_type
                    FROM orders_products AS op
                    INNER JOIN custom_products_code AS cpc
                        ON op.orders_products_id = cpc.orders_products_id
                    LEFT JOIN purchase_orders AS po
                        ON cpc.purchase_orders_id = po.purchase_orders_id
                    WHERE op.orders_id = " . $row["orders_id"] . "
                        AND op.products_good_delivered_price > 0
                        AND cpc.custom_products_code_viewed = 0";
            $_res = Yii::$app->db_slave_offgamers
                    ->createCommand($_sql)
                    ->queryAll();
            foreach ($_res as $_row) {
                if ($_row['purchase_orders_id'] > 0) {
                    $_sql2 = "SELECT pop.products_usd_unit_price, p.products_price, p.products_base_currency
                            FROM purchase_orders_products AS pop
                            INNER JOIN products AS p ON pop.products_id = p.products_id
                            WHERE pop.purchase_orders_id = '" . $_row['purchase_orders_id'] . "'
                                AND pop.products_id = '" . $_row['products_id'] . "'";
                    $_row2 = Yii::$app->db_slave_offgamers
                            ->createCommand($_sql2)
                            ->queryOne();

                    $unit_cost = $_row2['products_usd_unit_price'];
                    $selling_price = $_row2['products_base_currency'] . ' ' . $_row2['products_price'];
                } else {
                    $_sql2 = "SELECT p.products_price, p.products_base_currency
                            FROM products AS p
                            WHERE p.products_id = '" . $_row['products_id'] . "'";
                    $_row2 = Yii::$app->db_slave_offgamers
                            ->createCommand($_sql2)
                            ->queryOne();

                    $unit_cost = 'No PO';
                    $selling_price = $_row2['products_base_currency'] . ' ' . $_row2['products_price'];
                }

                $cdk = $cpc_obj->getCode($_row['custom_products_code_id'], $_row['to_s3']);
                $cd_key_file_type = $_row['file_type'];

                if (!empty($cdk)) {
                    $cdk = $cpc_obj->decryptData($cdk);

                    switch ($_row['file_type']) {
                        case 'soft':
                            $cdk = preg_replace('/(<br>)+$/', '', $cdk);
                            break;
                        default:
                            $filename = $row['orders_id'] . '_' . $_row['products_id'] . '_' . $_row['custom_products_code_id'] . '.' . $cd_key_file_type;
                            $cdk_img = $cpc_obj->getCdkeyImg($_row['custom_products_code_id']);
                            if ($cdk_img) {
                                $zip_obj->addFile($cdk_img, $filename);
                            }

                            $cdk = $row['orders_id'] . '_' . $_row['products_id'] . '_' . $_row['custom_products_code_id'] . '.' . $cd_key_file_type;
                            break;
                    }
                } else {
                    $cdk = "Empty content";
                }

                $str = [
                    $row['orders_id'],
                    $row['date_purchased'],
                    str_replace(array('"', '<br>', '<BR>'), array('""', ''), strip_tags($_row['products_name'])),
                    $_row['products_id'],
                    $_row['purchase_orders_ref_id'],
                    $unit_cost,
                    $selling_price,
                    $cd_key_file_type,
                    $cdk
                ];
                fputcsv($fp, $str);
            }
        }

        rewind($fp);
        $file_1 = stream_get_contents($fp);
        $file_2 = $zip_obj->file();

        Yii::$app->mailer->compose()
                ->setFrom(Yii::$app->params["noreply"]["default"])
                ->setReplyTo(Yii::$app->params["noreply"]["default"])
                ->setTo(Yii::$app->params["cron.Setting"]["unviewedCDK"]["recipient"])
                ->setSubject($subject)
                ->setHtmlBody($subject)
                ->attachContent($file_1, ['fileName' => $filename_1, 'contentType' => 'text/csv'])
                ->attachContent($file_2, ['fileName' => $filename_2, 'contentType' => 'application/zip'])
                ->send();
    }

}
