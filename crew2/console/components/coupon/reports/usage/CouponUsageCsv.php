<?php

namespace console\components\coupon\reports\usage;

use Exception;

class CouponUsageCsv
{
    private const COUPON_FOLDER_NAME = 'coupon';

    private CouponUsageReport $couponReportForm;
    private $resource;

    private function __construct(CouponUsageReport $couponReport, $resource)
    {
        $this->couponReportForm = $couponReport;
        $this->resource = $resource;
    }

    public static function make(CouponUsageReport $couponReport): self
    {
        $resource = fopen('php://temp', 'w+');
        return new self($couponReport, $resource);
    }

    public function getFilename(): string
    {
        $startTime = strtotime($this->couponReportForm->startDate);
        $endTime = strtotime($this->couponReportForm->endDate);
        $currentDateTime = date("YmdHis");

        return sprintf("%s/%s_%s_%s.csv", self::COUPON_FOLDER_NAME, $startTime, $endTime, $currentDateTime);
    }

    public function createReportResource(array $results): void
    {
        $header = [
            "Customer ID",
            "Customer Group",
            "Sign Up Date",
            "Order ID",
            "Order Date",
            "Product(s)",
            "Payment Method",
            "Checkout Country",
            "Coupon Code",
            "Total Amount Paid (USD)",
        ];

        if ($this->couponReportForm->previousPurchaseDate) {
            $header[] = "Previous purchases count";
        }

        if ($this->couponReportForm->subsequentPurchaseDate) {
            $header[] = "Subsequent purchases count";
        }

        try {
            fputcsv($this->resource, $header);

            foreach ($results as $data) {
                fputcsv($this->resource, $data);
            }

            rewind($this->resource);
        } catch (Exception $exception) {
            throw new Exception(sprintf("Error on creating csv for file : [%s] due to : [%s]", $this->getFilename(), $exception->getMessage()));
        }
    }

    /**
     * @return false|resource
     */
    public function getResource()
    {
        return $this->resource;
    }

}