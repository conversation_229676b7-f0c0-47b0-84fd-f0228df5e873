<?php

namespace console\components\coupon\reports\usage;

use Exception;
use Yii;
use yii\db\Connection;

class CouponUsageFilterDataGenerator
{
    private const ORDER_STATUS_COMPLETE = 3;

    private CouponUsageReport $couponUsageReport;
    private Connection $database;

    public function __construct(
        CouponUsageReport $couponUsageReportForm,
        Connection $database
    ) {
        $this->couponUsageReport = $couponUsageReportForm;
        $this->database = $database;
    }

    public static function make(CouponUsageReport $couponUsageReport): self
    {
        $database = Yii::$app->db_slave_offgamers;
        return new self($couponUsageReport, $database);
    }

    /**
     * @throws Exception
     */
    public function generateFilterData(): array
    {
        $queryString = $this->getOrdersCouponQueryString();

        try {
            $results = $this->database
                ->createCommand($queryString)
                ->queryAll();

            for ($i = 0; $i < count($results); $i++) {
                $orderId = $results[$i]['order_id'];
                $customerId = $results[$i]['customers_id'];

                if ($this->couponUsageReport->previousPurchaseDate) {
                    $results[$i]['previous_purchases_count'] =
                        $this->getPreviousOrderPurchasesCount($orderId, $customerId);
                }

                if ($this->couponUsageReport->subsequentPurchaseDate) {
                    $results[$i]['subsequent_purchase_count'] =
                        $this->getSubsequentOrderPurchasesCount($orderId, $customerId);
                }
            }

            return $results;
        } catch (Exception $exception) {
            throw new Exception(
                sprintf("Error generating coupon usage filter data due to : [%s]", $exception->getMessage())
            );
        }
    }

    private function getOrdersCouponQueryString(): string
    {
        $orderCompleteStatusId = self::ORDER_STATUS_COMPLETE;
        $queryString = <<<SQL
            SELECT o.customers_id, 
                    cg.customers_groups_name AS "customer_group",
                    c.customers_info_date_account_created AS "sign_up_date",
                    o.orders_id AS "order_id",
                    o.date_purchased as "order_date",
                    GROUP_CONCAT(op.products_name ORDER BY op.products_name separator ',') AS 'products',
                    (CASE 
                        WHEN o.payment_methods_id = 0 THEN 'Store Credit' 
                        WHEN ppm.id IS NULL THEN o.payment_method
                        ELSE CONCAT(ppm.pm_display_name, '(', ppm.pg_display_name , ')') END)
                    AS 'payment_method',
                    IFNULL(ctr.countries_name,'Unknown') AS "checked_out_country",
                    cp.coupon_code AS "coupon_code",
                    SUM(op.products_good_delivered_price) AS "total_amount_paid"
            FROM orders o 
            INNER JOIN customers_groups cg 
            ON o.customers_groups_id = cg.customers_groups_id
            INNER JOIN customers_info c
            ON o.customers_id = c.customers_info_id
            INNER JOIN orders_total ot
            ON o.orders_id = ot.orders_id AND ot.class = 'ot_coupon'
            LEFT JOIN orders_extra_info oei
            ON o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id'
            LEFT JOIN orders_extra_info AS oei2
            ON oei2.orders_id = o.orders_id AND oei2.orders_extra_info_key = 'ip_country' 
            INNER JOIN orders_products op
            ON o.orders_id = op.orders_id
            INNER JOIN coupon_redeem_track cr
            ON o.orders_id = cr.order_id
            INNER JOIN coupons cp
            ON cr.coupon_id = cp.coupon_id
            LEFT JOIN countries ctr
            ON oei2.orders_extra_info_value = ctr.countries_id
            LEFT JOIN pipwave_payment_mapper ppm
            ON o.payment_methods_id = ppm.pm_id AND ppm.site_id = 0
            WHERE
            o.date_purchased >= '{$this->couponUsageReport->startDate}'
            AND 
            o.date_purchased <= '{$this->couponUsageReport->endDate}'  
            AND 
            o.orders_status = {$orderCompleteStatusId}
            AND
            o.orders_cb_status IS NULL
            AND 
            (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
        SQL;

        $queryString = $this->appendExcludeCustomerGroupQueryString($queryString);

        $queryString = $this->appendPaymentMethodQueryString($queryString);

        $queryString = $this->appendCouponCodeQueryString($queryString);

        $queryString = $this->appendCouponGenerationIdQueryString($queryString);

        $queryString .= " GROUP BY o.orders_id";

        $queryString .= " HAVING total_amount_paid >= {$this->couponUsageReport->minPurchase}";

        return $queryString;
    }

    /**
     * @throws \yii\db\Exception
     */
    private function getPreviousOrderPurchasesCount(int $orderId, int $customerId): int
    {
        $orderCompleteStatusId = self::ORDER_STATUS_COMPLETE;
        $previousPurchasesQueryString = <<<SQL
            SELECT COUNT(o.orders_id)
            FROM orders AS o
            INNER JOIN orders_extra_info AS oei
                ON oei.orders_id = o.orders_id 
                AND oei.orders_extra_info_key = 'site_id'
            WHERE o.orders_id < {$orderId}
                AND o.date_purchased >= "{$this->couponUsageReport->previousPurchaseDate}"
                AND o.customers_id = {$customerId}
                AND o.orders_status = {$orderCompleteStatusId}
                AND o.orders_cb_status IS NULL
                AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
            SQL;

        $previousPurchasesQueryString = $this->appendExcludeCustomerGroupQueryString($previousPurchasesQueryString);

        return $this->database
            ->createCommand($previousPurchasesQueryString)
            ->queryScalar();
    }

    /**
     * @throws \yii\db\Exception
     */
    private function getSubsequentOrderPurchasesCount(int $orderId, int $customerId): int
    {
        $orderCompleteStatusId = self::ORDER_STATUS_COMPLETE;
        $subsequentPurchasesQueryString = <<<SQL
            SELECT COUNT(o.orders_id)
            FROM orders AS o
            INNER JOIN orders_extra_info AS oei
                ON oei.orders_id = o.orders_id 
                AND oei.orders_extra_info_key = 'site_id'
            WHERE o.orders_id > {$orderId}
                AND o.date_purchased <= "{$this->couponUsageReport->subsequentPurchaseDate}"
                AND o.customers_id = {$customerId}
                AND o.orders_status = {$orderCompleteStatusId}
                AND o.orders_cb_status IS NULL 
                AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
            SQL;

        $subsequentPurchasesQueryString = $this
            ->appendExcludeCustomerGroupQueryString($subsequentPurchasesQueryString);

        return $this->database
            ->createCommand($subsequentPurchasesQueryString)
            ->queryScalar();
    }

    private function appendExcludeCustomerGroupQueryString(string $queryString): string
    {
        if (!empty($this->couponUsageReport->excludeCustomerGroup)) {
            $excludedCustomerGroupString = implode(",", $this->couponUsageReport->excludeCustomerGroup);
            $queryString .= " AND o.customers_groups_id NOT IN ({$excludedCustomerGroupString})";
        }

        return $queryString;
    }

    private function appendPaymentMethodQueryString(string $queryString): string
    {
        if (!empty($this->couponUsageReport->paymentMethods)) {
            $paymentMethodsString = implode(",", $this->couponUsageReport->paymentMethods);
            $queryString .= " AND ppm.id IN ($paymentMethodsString)";
        }

        return $queryString;
    }

    private function appendCouponCodeQueryString(string $queryString): string
    {
        if (!empty($this->couponUsageReport->couponCode)) {
            $queryString .= " AND cp.coupon_code = '{$this->couponUsageReport->couponCode}'";
        }

        return $queryString;
    }

    private function appendCouponGenerationIdQueryString(string $queryString): string
    {
        if (!empty($this->couponUsageReport->couponGenerationId)) {
            $queryString .= " AND cp.coupon_generation_id = '{$this->couponUsageReport->couponGenerationId}'";
        }

        return $queryString;
    }

}