<?php

namespace console\components\coupon\reports\usage;

use Exception;
use Yii;

class CouponUsageReport
{
    public $startDate;
    public $endDate;
    public $previousPurchaseDate;
    public $subsequentPurchaseDate;
    public $minPurchase;
    public $reportRecipient;
    public $couponCode;
    public $couponGenerationId;
    public $paymentMethods = [];
    public $excludeCustomerGroup = [];
    private bool $isReportGenerated = false;


    public function __construct(array $params)
    {
        $this->startDate = $params['startDate'];
        $this->endDate = $params['endDate'];
        $this->previousPurchaseDate = $params['previousPurchaseDate'];
        $this->subsequentPurchaseDate = $params['subsequentPurchaseDate'];
        $this->minPurchase = $params['minPurchase'];
        $this->paymentMethods = $params['paymentMethods'];
        $this->excludeCustomerGroup = $params['excludeCustomerGroup'];
        $this->couponCode = $params['couponCode'];
        $this->couponGenerationId = $params['couponGenerationId'];
        $this->reportRecipient = $params['reportRecipient'];
    }

    /**
     * @throws Exception
     */
    public function generateReportAndSendEmail(): void
    {
        $reportData = CouponUsageFilterDataGenerator::make($this);
        $results = $reportData->generateFilterData();

        $emailContentGenerator = new CouponUsageReportEmailContentGenerator($this);

        $emailContent = $emailContentGenerator->generateContentWithNoResults();

        if (count($results) > 0) {
            $couponUsageCsv = CouponUsageCsv::make($this);
            $couponUsageCsv->createReportResource($results);

            $couponS3Repository = CouponUsageS3::make();
            $couponS3Repository->saveCSV($couponUsageCsv);

            $emailContent = $emailContentGenerator
                ->generateContentWithResults($couponS3Repository->getS3FileDownloadUrl());
        }

        $this->sendEmailToRecipients($emailContentGenerator->getSubject(), $emailContent);

        $this->isReportGenerated = true;
    }

    public function isReportGenerated(): bool
    {
        return $this->isReportGenerated;
    }

    private function sendEmailToRecipients(string $subject, string $contents): void
    {
        $emails = explode(",", $this->reportRecipient);
        foreach ($emails as $email) {
            Yii::$app->mailer->compose()
                ->setFrom(Yii::$app->params["noreply"]["default"])
                ->setReplyTo(Yii::$app->params["noreply"]["default"])
                ->setTo(trim($email))
                ->setSubject($subject)
                ->setTextBody($contents)
                ->send();
        }
    }

}