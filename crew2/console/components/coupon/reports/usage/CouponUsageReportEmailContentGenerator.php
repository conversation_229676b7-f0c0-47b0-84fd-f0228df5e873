<?php

namespace console\components\coupon\reports\usage;

use common\models\CustomersGroups;
use common\models\PipwavePaymentMapper;

class CouponUsageReportEmailContentGenerator
{
    private CouponUsageReport $couponUsageReport;

    public function __construct(CouponUsageReport $couponUsageReport)
    {
        $this->couponUsageReport = $couponUsageReport;
    }

    public function generateContentWithResults(string $s3FileDownloadUrl): string
    {
        $content = $this->generateMainContent();

        $content .= sprintf(
            "\n\nThe following document valid to download 5 days from the date of issue : \n %s",
            $s3FileDownloadUrl
        );

        return $content;
    }

    public function generateContentWithNoResults(): string
    {
        $content = $this->generateMainContent();

        $content .= "\n\n0 Results Found\n";

        return $content;
    }

    public function getSubject(): string
    {
        return sprintf(
            "Coupon Usage Report %s - %s",
            $this->couponUsageReport->startDate,
            $this->couponUsageReport->endDate
        );
    }

    private function generateMainContent(): string
    {
        $content = $this->getMandatoryFilterString();

        return $this->getOptionalFilterString($content);
    }

    private function getMandatoryFilterString(): string
    {
        return sprintf(
            "Start Date : %s\nEnd Date : %s\nMinimum Total Purchase Amount(USD) : %s \n",
            $this->couponUsageReport->startDate,
            $this->couponUsageReport->endDate,
            $this->couponUsageReport->minPurchase
        );
    }

    private function getOptionalFilterString(string $content): string
    {
        $content .= $this->getPreviousPurchaseDateFilterString();

        $content .= $this->getSubsequentPurchaseDateFilterString();

        $content .= $this->getPaymentMethodFilterString();

        $content .= $this->getExcludedCustomerGroupFilterString();

        $content .= $this->getCouponCodeFilterString();

        $content .= $this->getCouponGenerationIdFilterString();

        return $content;
    }

    private function getPreviousPurchaseDateFilterString(): string
    {
        if ($this->couponUsageReport->previousPurchaseDate) {
            return sprintf(
                "Completed Order before Coupon usage Start date : %s\n",
                $this->couponUsageReport->previousPurchaseDate
            );
        }

        return '';
    }

    private function getSubsequentPurchaseDateFilterString(): string
    {
        if ($this->couponUsageReport->subsequentPurchaseDate) {
            return sprintf(
                "Subsequent purchase after Coupon Usage End Date : %s\n",
                $this->couponUsageReport->subsequentPurchaseDate
            );
        }
        return '';
    }

    private function getPaymentMethodFilterString(): string
    {
        $paymentMethodString = "All";
        if (!empty($this->couponUsageReport->paymentMethods)) {
            $paymentList = PipwavePaymentMapper::getOGPaymentList();
            $paymentMethods = [];
            foreach ($this->couponUsageReport->paymentMethods as $paymentMethodId) {
                $paymentMethods[] = $paymentList[$paymentMethodId];
            }

            $paymentMethodString = implode(",", $paymentMethods);
        }

        return sprintf("Payment Method : %s\n", $paymentMethodString);
    }

    private function getExcludedCustomerGroupFilterString(): string
    {
        $excludedCustomerGroupString = "None";
        if (!empty($this->couponUsageReport->excludeCustomerGroup)) {
            $customerGroups = [];

            $customerGroupsList = CustomersGroups::getGroupList();
            foreach ($this->couponUsageReport->excludeCustomerGroup as $customerGroupId) {
                $customerGroups[] = $customerGroupsList[$customerGroupId];
            }
            $excludedCustomerGroupString = implode(",", $customerGroups);
        }

        return sprintf("Excluded Customer Group : %s\n", $excludedCustomerGroupString);
    }

    private function getCouponCodeFilterString(): string
    {
        if ($this->couponUsageReport->couponCode) {
            return sprintf("Coupon Code : %s\n", $this->couponUsageReport->couponCode);
        }
        return '';
    }

    public function getCouponGenerationIdFilterString(): string
    {
        if ($this->couponUsageReport->couponGenerationId) {
            return sprintf(
                "Coupon Generation Id : %s\n",
                $this->couponUsageReport->couponGenerationId
            );
        }
        return '';
    }
}