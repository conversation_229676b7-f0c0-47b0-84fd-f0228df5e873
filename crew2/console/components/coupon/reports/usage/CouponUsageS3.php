<?php

namespace console\components\coupon\reports\usage;

use offgamers\base\components\AWSS3;
use Yii;

class CouponUsageS3
{
    private const EXPIRY_TIME = 432000;

    private AWSS3 $s3;
    private ?string $s3FileDownloadUrl;

    private function __construct(AWSS3 $s3)
    {
        $this->s3 = $s3;
    }

    public static function make(): CouponUsageS3
    {
       $s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');

        return new self($s3);
    }

    public function saveCSV(CouponUsageCsv $couponUsageCsv): void
    {
        $filenamePath = $couponUsageCsv->getFilename();
        $this->s3->saveContent($filenamePath, $couponUsageCsv->getResource());

        $this->s3FileDownloadUrl = $this->s3->getContentUrl(
            $filenamePath,
            true,
            self::EXPIRY_TIME
        );
    }

    public function getS3FileDownloadUrl(): ?string
    {
        return $this->s3FileDownloadUrl;
    }

}