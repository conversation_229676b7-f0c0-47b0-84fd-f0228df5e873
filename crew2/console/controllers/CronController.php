<?php

namespace console\controllers;

use console\components\CronBestSelling;
use console\components\CronGiftCardClosingStockReport;
use console\components\CronMonthlyPaymentGatewaySalesReport;
use console\components\CronReports;
use Exception;
use Yii;
use yii\console\Controller;
use yii\web\BadRequestHttpException;
use console\components\CronCustomerAccountClosureCom;
use common\models\GamePublisher;
use common\components\publishers\CodeWholeSale;
use offgamers\base\components\LogComponent;
use common\components\publishers\DTOne;
use common\components\publishers\DTOneDVS;
use common\models\ProductsDescription;
use common\models\ResellerUpgradeForm;
use offgamers\publisher\models\Publisher;
use offgamers\base\models\ms\Inventory;
use console\components\CronDormantOPSC;
use console\components\CronRefundReport;
use console\components\CronUndeliveredOrderReport;
use console\components\CronProductSalesReport;
use console\components\CronUnviewedCDK;
use console\components\CronOutOfStockReport;
use common\models\Products;
use yii\db\Query;
use common\models\CronProcessTrack;
use yii\helpers\BaseJson;

/**
 * Cron controller
 */
class CronController extends Controller
{

    public $customers_groups_id;
    public $downgrade_customers_groups_id;
    public $reseller_upgrade_date;
    public $reseller_upgrade_excluded_user_id;
    public $reseller_upgrade_downgrade_exclude_group_id;

    public function options($actionId)
    {
        switch ($actionId) {
            case 'upgrade-reseller-group':
                $option = [
                    'customers_groups_id',
                    'downgrade_customers_groups_id',
                    'reseller_upgrade_date',
                    'reseller_upgrade_excluded_user_id',
                    'reseller_upgrade_downgrade_exclude_group_id'
                ];
                break;
            default:
                $option = [];
                break;
        }
        return array_merge($option, parent::options($actionId));
    }

    // Cron for Customers Account Closure Weekly Notification
    public function actionAccountClosureNotification()
    {
        $cronCustAccClosure = new CronCustomerAccountClosureCom();
        $cronCustAccClosure->sendRequestList();
        unset($cronCustAccClosure);
    }

    // Cron for Customers Account Closure
    public function actionAccountClosureProcess()
    {
        $cronCustAccClosure = new CronCustomerAccountClosureCom();
        $cronCustAccClosure->processClosureRequest();
        unset($cronCustAccClosure);
    }

    public function actionAccountClosureVerificationDoc()
    {
        $cronCustAccClosure = new CronCustomerAccountClosureCom();
        $cronCustAccClosure->deleteVerificationDoc();
    }

    public function actionSyncCodeswholesale()
    {
        $publisher_list = GamePublisher::findAll(['profile' => 'CodeWholeSale', 'status' => 1]);
        foreach ($publisher_list as $publisher) {
            if ($publisher->game_publisher_id) {
                $profile = new CodeWholeSale();
                $profile->publisher = $publisher->game_publisher_id;
                $profile->syncProductList();
            }
        }
    }

    public function actionSyncDtone()
    {
        $publisher_list = Publisher::findAll(['profile' => 'DTOne', 'status' => 1]);
        foreach ($publisher_list as $publisher) {
            if ($publisher->publisher_id) {
                $publisher->updateAttributes(['last_sync' => time()]);
                $profile = new DTOne();
                $profile->publisher = $publisher->publisher_id;
                $profile->sync();
            }
        }
    }

    public function actionSyncDtoneDvs()
    {
        $publisher_list = Publisher::findAll(['profile' => 'DTOneDVS', 'status' => 1]);
        foreach ($publisher_list as $publisher) {
            if ($publisher->publisher_id) {
                $publisher->updateAttributes(['last_sync' => time()]);
                $profile = new DTOneDVS();
                $profile->publisher = $publisher->publisher_id;
                $profile->sync();
            }
        }
    }

    public function actionDailyLogArchive()
    {
        $l_com = new LogComponent();
        $l_com->archiveIncomingRequestLog();
        $l_com->archiveOutgoingRequestLog();
        $l_com->archiveModelAuditLog();
        $l_com->archiveDebugLog();
    }

    public function actionBatchUpdateAlgolia()
    {
        $model = new \offgamers\base\models\ms\Product;
        $model->customRequest('game-publisher-product', 'batch-update-algolia');
    }

    public function actionMonthlyReport()
    {
        $cron_monthly_report = new \console\components\CronMonthlyReport();
        $cron_monthly_report->resellerSales();
        $cron_monthly_report->newSignUpSales();
        $cron_monthly_report->mgcProductSales();
        $cron_monthly_report->cetKPI2();
    }

    public function actionSyncCurrenciesRate()
    {
        $currency = new \console\models\Currencies;
        $currency->syncCurrenciesRate();
    }

    public function actionAutoDeliver($limit = 30)
    {
        (new \common\models\StoreCreditForm)->deliverQueue($limit);
    }

    public function actionExtractDeliverQueue()
    {
        (new \common\models\StoreCreditForm)->extractQueue();
    }

    public function actionStoreCreditStatement($start_date = "", $end_date = "")
    {
        (new \common\models\StoreCreditForm)->downloadStatement($start_date, $end_date);
    }

    public function actionCleanupOrdersCdkeys($to_date = "")
    {
        \console\components\CronOrdersCdkeysCom::cleanupOrdersCdkeys($to_date);
    }

    // php yii cron/upgrade-reseller-group
    // --customers_groups_id 3,5
    // --downgrade_customers_groups_id 3
    // --reseller_upgrade_date example 2020/6
    // --reseller_upgrade_excluded_user_id 123456,234567
    // --reseller_upgrade_downgrade_exclude_group_id 3,5

    public function actionUpgradeResellerGroup()
    {
        $form_model = new ResellerUpgradeForm();
        $form_model->setData(
            $this->reseller_upgrade_date,
            $this->customers_groups_id,
            $this->downgrade_customers_groups_id,
            $this->reseller_upgrade_downgrade_exclude_group_id,
            $this->reseller_upgrade_excluded_user_id
        );
        $form_model->process();
    }

    public function actionMintRouteBatch()
    {
        try {
            (new Inventory())->customRequest('restock', 'mint-route-batch-restock');
        } catch (Exception $e) {
            Yii::$app->slack->send(
                'Fail to process MintRoute Batch Request',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    public function actionBambooBatch()
    {
        try {
            (new Inventory())->customRequest('restock', 'bamboo-batch-restock');
        } catch (Exception $e) {
            Yii::$app->slack->send(
                'Fail to process Bamboo Batch Request',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    public function actionPrepaidForgeBatch()
    {
        try {
            (new Inventory())->customRequest('restock', 'prepaid-forge-batch-restock');
        } catch (Exception $e) {
            Yii::$app->slack->send(
                'Fail to process Prepaid Forge Batch Request',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $e->getTraceAsString(),
                    ),
                )
            );
        }
    }

    /**
     * cron/dormant-op-sc
     * @var $action string : sendq, sendmail, zeroize
     * @var $limit integer :
     * - sendq : mail content deadline countdown (days)
     * - sendmail : num of mail send each execution
     * @var $sleep integer `second unit` : sleep after each batch $limit request processed
     * @var $fr_date datetime `2020-01-01 00:00:00`
     * @var $to_date datetime `2021-12-31 23:59:59`
     *
     * command :
     * - php yii cron/dormant-op-sc <action> <limit> <sleep> <fr_date> <to_date>
     */
    public function actionDormantOpSc($action = "sendq", $limit = 30, $sleep = 1, $fr_date = "", $to_date = "")
    {
        $cron = new CronDormantOPSC();
        $cron->setData($action, $limit, $sleep, $fr_date, $to_date);
        $cron->process();
    }

    public function actionReports()
    {
        $cron = new CronReports();
        $cron->runReports();
    }

    public function actionUnviewedCdk($fr_date = "", $to_date = "", $product = "")
    {
        $cron = new CronUnviewedCDK();
        $cron->setData($fr_date, $to_date, $product);
        $cron->run();
    }

    /**
     * cron/refund-report
     * @var $action string : daily, monthly
     * @var $fr_date datetime `2020-01-01 00:00:00`
     * @var $to_date datetime `2021-02-01 00:00:00`
     *
     * command :
     * - php yii cron/refund-report <action> <fr_date> <to_date>
     */
    public function actionRefundReport($action = "daily", $fr_date = "", $to_date = "")
    {
        $cron = new CronRefundReport();
        $cron->setData($action, $fr_date, $to_date);
        $cron->run();
    }

    /**
     * cron/monthly-undelivered-order-report
     * @var $action string : all, 1, 2, 10
     * @var $day1_date datetime `2020-01-01`
     * @var $fr_date datetime `2022-01-01`
     * @var $to_date datetime `2022-02-01`
     * @var $report10_to_date datetime `2022-02-01`
     *
     * command :
     * - php yii cron/monthly-undelivered-order-report <action> <day1_date> <fr_date> <to_date> <report10_to_date>
     */
    public function actionMonthlyUndeliveredOrderReport(
        $action = "all",
        $day1_date = "",
        $fr_date = "",
        $to_date = "",
        $report10_to_date = ""
    ) {
        $cron = new CronUndeliveredOrderReport();
        $cron->setData($action, $day1_date, $fr_date, $to_date, $report10_to_date);
        $cron->process();
    }

    /**
     * cron/monthly-product-sales-report
     * @var $fr_date datetime : `2022-01-01`
     * @var $to_date datetime : `2022-02-01`
     *
     * command :
     * - php yii cron/monthly-product-sales-report <action> <fr_date> <to_date>
     */
    public function actionMonthlyProductSalesReport($fr_date = "", $to_date = "")
    {
        $cron = new CronProductSalesReport();
        $cron->setData($fr_date, $to_date);
        $cron->process();
    }

    // php yii cron/monthly-gift-card-closing-stock-report
    public function actionGiftCardClosingStockReport()
    {
        try {
            (new CronGiftCardClosingStockReport())->run();
        } catch (Exception $exception) {
            Yii::$app->slack->send(
                'Error processing Monthly Gift Card Closing Stock Report',
                array(
                    array(
                        'color' => 'warning',
                        'text' => $exception->getTraceAsString(),
                    ),
                )
            );
        }
    }

    /**
     * php yii cron/best-selling
     * @var $fr_date datetime : `2022-01-01`
     * @var $to_date datetime : `2022-02-01`
     *
     * command :
     * - php yii cron/best-selling <action> <fr_date> <to_date>
     */
    public function actionBestSelling($fr_date = "", $to_date = "")
    {
        $cron = new CronBestSelling();
        $cron->setData($fr_date, $to_date);
        $cron->process();
    }

    public function actionWeekly()
    {
        (new \console\components\CronEkyc())->submitEkyc();
        (new \console\components\CronEkyc())->downgradeTL();
    }

    public function actionOutOfStockDurationReport($period = "manual", $fr_date = "", $to_date = "")
    {
        if ($period === 'weekly') {
            $fr_date = date('Y-m-d 00:00:00', strtotime('-1 week'));
            $to_date = date('Y-m-d 23:59:59', strtotime('yesterday'));
        } elseif ($period === 'monthly') {
            $fr_date = date('Y-m-d 00:00:00', strtotime('first day of last month'));
            $to_date = date('Y-m-d 23:59:59', strtotime('last day of last month'));
        } elseif ($period !== 'manual') {
            throw new BadRequestHttpException('Invalid period parameter. Accepted values are "manual", "weekly", or "monthly".');
        }

        $cron = new CronOutOfStockReport();
        $cron->setData($fr_date, $to_date);
        $cron->process();
    }

    /**
     * cron/monthly-payment-gateway-sales-report
     * @var string $action : "monthly" or "delivered_only"
     * @var string $from_date : `2022-01-01`
     * @var string $to_date : `2022-02-01`
     *
     * command :
     * - php yii cron/monthly-payment-gateway-sales-report <action> <from_date> <to_date>
     */
    public function actionMonthlyPaymentGatewaySalesReport(
        string $action = '',
        string $from_date = '',
        string $to_date = ''
    ) {
        if (!in_array($action, ['all', 'delivered'])) {
            echo "Action not allowed";
            exit;
        }

        $cron = new CronMonthlyPaymentGatewaySalesReport();
        $cron->setData($action, $from_date, $to_date);
        $cron->process();
    }

    public function actionExportProducts($status = 1, $language_id=1,$before_cutoff =1)
    {
        // Define the CSV folder path
        $csvFolderPath = Yii::getAlias('@app') . '/csv_exports/';

        // Create the folder if it doesn't exist
        if (!file_exists($csvFolderPath)) {
            mkdir($csvFolderPath, 0777, true); // Creates the folder recursively
        }

        // Your query to fetch data (using ProductsDescription model)

        if($before_cutoff){
            $query = ProductsDescription::find()->alias('pd')
            ->joinWith('products p', false)
            ->where(array('p.products_status' => 1, 'pd.language_id' => 1))
            ->andWhere(array('<','p.products_last_modified', '2023-12-01'));
        }else{
            $query = ProductsDescription::find()->alias('pd')
        ->joinWith('products p', false)
        ->where(array('p.products_status' => 1, 'pd.language_id' => 1))
        ->andWhere(array('>','p.products_last_modified', '2023-12-01'));
        }

        $batch_size = 100000;

        // Fetch data in batches
        $dataReader = $query->batch($batch_size);

        // Define the CSV file prefix
        $csvPrefix = 'products_data_';

        // Counter for file names
        $fileCounter = 1;
        $rowCounter = 0;

        foreach ($dataReader as $rows) {

            // Check if $rows is an array and not empty
            if (is_array($rows) && !empty($rows)) {
                // Define the CSV file path for each batch
                $csvFilePath = $csvFolderPath . $csvPrefix .'_status_'. $status.'_language_'. $language_id. '_is_cutoff_'. $before_cutoff. '_' . $fileCounter . '.csv';

                // Open the file pointer and write data to CSV
                $file = fopen($csvFilePath, 'w');

                // Write header (if needed) - assuming the first row contains headers
                if ($rowCounter === 0) {
                    $headers = array_keys($rows[0]->attributes);
                    fputcsv($file, $headers);
                }

                // Write data rows
                foreach ($rows as $row) {

                    fputcsv($file, $row->attributes);

                    // $rowData = array_map(function ($value) {
                    //     return mb_convert_encoding($value, 'UTF-8', 'ISO-8859-1');
                    // }, $row->attributes);

                }

                // Close the file pointer
                fclose($file);

                // Free memory
                unset($rows);
                gc_collect_cycles();

                // Increment the file counter
                $fileCounter++;
            }
        }

        echo "Product CSV export completed!\n";
    }

    public function actionImportProducts()
    {
        $folderPath = Yii::getAlias('@app') . '/csv_exports/';
        if(is_file($folderPath.'/failed.csv')){
            unlink($folderPath.'/failed.csv');
        }
        $error_file = fopen($folderPath.'/failed.csv', 'w');

        $db = Yii::$app->db;
        $tableName = 'products_description'; // Temporary table name
        $bk_tableName = 'backup_products_description';

        $sql = 'DROP TABLE IF EXISTS backup_products_description';
        $db->createCommand($sql)->execute();
        // CREATE PRODUCTS DESCRIPTION TABLE BACKUP
        $sql = 'CREATE TABLE '.$bk_tableName.' LIKE '.$tableName.'';
        $db->createCommand($sql)->execute();
        $sql = 'INSERT INTO  '.$bk_tableName.' SELECT * FROM '.$tableName.'';
        $db->createCommand($sql)->execute();

        // Get all CSV files in the specified folder
        $files = glob(rtrim($folderPath, '/') . '/products_data_*.csv');
        if (empty($files)) {
            echo "No CSV files found in the folder.\n";
            return Controller::EXIT_CODE_ERROR;
        }

        foreach ($files as $file) {
            var_dump($file);

            $fileHandle = fopen($file, 'r');
            $headers = fgetcsv($fileHandle);
            while (($row = fgetcsv($fileHandle)) !== false) {

                if (count($headers) !== count($row)) {

                    fputcsv($error_file, $row);

                    continue; // Skip if the number of columns doesn't match
                }

                $params = array_combine($headers, $row);

                if ($params === false) {
                    continue; // Skip if array_combine fails due to unequal lengths
                }
                $product_description = ProductsDescription::findOne(['products_id'=>$params['products_id'],'language_id'=>$params['language_id']]);

                if($product_description){
                    foreach ($params as $key => $value) {
                        $product_description->$key = $value;
                    }

                    if(!$product_description->save()){
                        // var_dump($params);
                        var_dump($product_description->errors);
                        fputcsv($error_file, $row);
                    }
                }


            }
            fclose($fileHandle);
        }

        echo "CSV files in $folderPath imported to $tableName successfully.\n";
        fclose($error_file);
        return Controller::EXIT_CODE_NORMAL;

    }

    public function actionSyncSale($type = 'PO', $day = "1", $limit = "1000")
    {
        set_time_limit(3600);
        ini_set("memory_limit", "2048M");
        switch ($type) {
            case "PO":
                $syncSaleCron = CronProcessTrack::findOne(['cron_process_track_filename' => 'cron_sync_daily_purchase']);
                break;
        }

        if ($syncSaleCron) {
            switch ($syncSaleCron->cron_process_track_in_action) {
                    //sync from order history
                case "1":
                    // $lastSyncDate = strtotime('-1 day');
                    // $start_date = $syncSaleCron->cron_process_track_start_date;
                    // $next_date = strtotime('+' . $day . 'day', $syncSaleCron->cron_process_track_start_date);
                    // $end_date = date("Y-m-d", $next_date) . " 00:00:00";

                    //do not trigger daily 00:00AM - 00:15AM
                    //do not trigger daily 23:45PM - 00:00AM
                    $currentTimestamp = time();
                    $currentDatetime = date("Hi", $currentTimestamp);
                    if($currentDatetime >= "2345" || ($currentDatetime >= "0000" && $currentDatetime <= "0015")){
                        break;
                    }

                    $lastSycnDateTime = $currentTimestamp;
                    $start_date = $syncSaleCron->cron_process_track_start_date;
                    $end_datetime = strtotime('+' . $day . 'day', strtotime($start_date));

                    if($lastSycnDateTime < $end_datetime){
                        $lastSyncDate = date("Y-m-d", $lastSycnDateTime) . " 00:00:00";
                        $end_datetime = strtotime($lastSyncDate);
                    }
                    $end_date = date("Y-m-d", $end_datetime) . " 00:00:00";

                    if($start_date >= $end_date){
                        break;
                    }

                    $syncSaleCron->cron_process_track_in_action = 19;
                    $syncSaleCron->update('cron_process_track_in_action');

                    switch ($type) {
                        case "PO":
                            $syncSaleResult = \common\models\OrdersStatusHistory::getLatestHistory($start_date, $end_date);
                            $syncSalesModel = new \common\models\DailySoPoTmpHistory();
                            foreach ($syncSaleResult as $value) {
                                $data = [
                                    'id' => $value['orders_id'],
                                    'ref_id' => $value['orders_status_history_id'],
                                    'type' => 1, //Purchased Order
                                    'status' => $value['orders_status_id'],
                                    'status_date' => strtotime($value['date_added']),
                                    'created_at' => time()
                                ];
                                $syncSalesModel->isNewRecord = true;
                                $syncSalesModel->attributes = $data;
                                $syncSalesModel->save();
                            }
                            break;
                    }
                    $syncSaleCron->cron_process_track_in_action = 2;
                    $syncSaleCron->cron_process_track_start_date = $end_date;
                    $syncSaleCron->update(['cron_process_track_in_action','cron_process_track_start_date']);
                    break;
                    //sync from temp sync table to order sales table
                case "2":
                    $syncSaleCron->cron_process_track_in_action = 29;
                    $syncSaleCron->update('cron_process_track_in_action');
                    $syncSalesModel = new \common\models\DailySoPoTmpHistory();
                    switch ($type) {
                        case "PO":
                            $syncSales = $syncSalesModel->find()->where(['type' => 1])->limit($limit)->orderBy(['created_at' => SORT_ASC])->all();
                            if ($syncSales) {
                                $dailingCountryListByISO = array_map('strtolower', \common\models\Countries::find()->select(["countries_name"])->indexBy('countries_iso_code_2')->asArray()->column());
                                $completedStatus = '3';
                                $orderPurchaseModel = new \common\models\DailyPoHistory();
                                $opModel = new \common\models\OrdersProducts();
                                $orderIds = \yii\helpers\ArrayHelper::map($syncSales, 'id', 'id');
                                $orderSales = $orderPurchaseModel->find()->where(['IN', 'id', $orderIds])->indexBy('id')->all();
                                $op = $opModel->find([])->innerJoin("orders", "orders.orders_id=op.orders_id")->alias('op')->where(['IN', 'op.orders_id', $orderIds])->select(['op.orders_id', 'op.products_id', 'customers_telephone_country', 'orders_cb_status','customers_id', 'products_good_delivered_price', 'products_model', 'custom_products_type_id', 'orders_products_is_compensate'])->asArray()->all(\Yii::$app->db_slave_offgamers);
                                $opArray = [];
                                if ($op) {
                                    foreach ($op as $opValue) {
                                        $opArray[$opValue['orders_id']][] = $opValue;
                                    }
                                }

                                foreach ($syncSales as $value) {
                                    if (isset($opArray[$value['id']])) {
                                        $opObject = $opArray[$value['id']];
                                        $currentDate = time();
                                        $salesData = [
                                            'id' => $value['id'],
                                            'ref_id' => $value['ref_id'],
                                            'user_id' => $opObject[0]['customers_id'],
                                            'status' => $value['status'],
                                            'status_date' => $value['status_date'],
                                            'sales' => 0,
                                            'sync_status' => 0,
                                            'year' => date('Y', $currentDate),
                                            'month' => date('m', $currentDate),
                                            'day' => date('d', $currentDate),
                                            'updated_at' => $currentDate
                                        ];

                                        //if status is completed
                                        if ($value['status'] == $completedStatus) {
                                            foreach ($opObject as $opObjectValue) {
                                                $country_iso2 = array_search(strtolower($opObjectValue['customers_telephone_country']), $dailingCountryListByISO);
                                                $extraData = [
                                                    'products_id' => $opObjectValue['products_id'],
                                                    // 'service_id' => "lgc_service_" . $opObjectValue['custom_products_type_child_id'],
                                                    // 'brand_id' => "lgc_game_" . $opObjectValue['game_id'],
                                                    'country_code' => $country_iso2,
                                                    'country_name' => $opObjectValue['customers_telephone_country'],
                                                ];
                                                $salesData['data'] =  BaseJson::encode($extraData);
                                                $salesData['listing_id'] = $opObjectValue['products_id'];
                                                //only G2G order, exclude Topup SC, composenate, charged back before
                                                if (($opObjectValue['orders_products_is_compensate'] == "0")
                                                    && ($opObjectValue['orders_cb_status'] == NULL)
                                                    && ($opObjectValue['products_model'] != "g2g-store-credit")
                                                ) {
                                                    if($opObjectValue['custom_products_type_id'] == "3"){
                                                    } else {
                                                        $salesData['sales'] = $opObjectValue['products_good_delivered_price'];
                                                    }
                                                }
                                            }
                                        }
                                        

                                        if (isset($orderSales[$value['id']])) {
                                            switch ($orderSales[$value['id']]->status) {
                                                case $completedStatus:
                                                    //completed to completed
                                                    if ($value['status'] == $completedStatus) {
                                                        $sync_amount = $salesData['sales'] - $orderSales[$value['id']]->sales;
                                                    } else {
                                                        //rollback from completed
                                                        $sync_amount = 0 - $orderSales[$value['id']]->sales;
                                                    }
                                                    break;
                                                default:
                                                    $sync_amount = $salesData['sales'];
                                            }
                                            $salesData['sync_amount'] = $sync_amount;
                                            $orderSales[$value['id']]->attributes = $salesData;
                                            $orderSales[$value['id']]->save();
                                        } else {
                                            if ($value['status'] == $completedStatus) {
                                                $orderPurchaseModel->isNewRecord = true;
                                                $salesData['sync_amount'] = $salesData['sales'];
                                                $salesData['created_at'] = $currentDate;
                                                $orderPurchaseModel->attributes = $salesData;
                                                $orderPurchaseModel->save();
                                            }
                                        }
                                    }
                                    $value->delete();
                                }

                                $syncSaleCron->cron_process_track_in_action = 2;
                                $syncSaleCron->update('cron_process_track_in_action');
                            } else {
                                //sycn to sls
                                $syncSaleCron->cron_process_track_in_action = 3;
                                $syncSaleCron->update('cron_process_track_in_action');
                            }
                            break;
                    }
                    break;
                    //sync to serverless
                case "3":
                    $syncSaleCron->cron_process_track_in_action = 39;
                    $syncSaleCron->update('cron_process_track_in_action');
                    switch ($type) {
                        case "PO":
                            $prefix = "so";
                            $completedStatus = '3';
                            $syncUrl = Yii::$app->params['g2g.sls.admin_url'] . "/order/legacy_daily_sales";
                            $orderSalesModel = new \common\models\DailyPoHistory();
                            $orderSales = $orderSalesModel->find()->where(['sync_status' => 0])->limit($limit)->indexBy('id')->all();
                            break;
                    }


                    if (isset($orderSales)) {
                        if ($orderSales) {
                            $curl_com = new \common\components\CurlCom();
                            $url = Yii::$app->params['g2g.sls.user_url'] . "/auth/token";                            

                            $bodyData = [
                                "api_key"=> Yii::$app->params['g2g.sls.key'],
                                "api_secret"=> Yii::$app->params['g2g.sls.secret'],
                                "ag_authorizer"=> "ADMIN",
                                "requester_type"=> "SYSTEM",
                                "user_id"=> "system"
                            ];

                            $json = json_encode($bodyData);
                            $response = $curl_com->sendPost($url, $json, true);
                            $token = false;
                            if($response->code == 2000 && isset($response->payload)){
                                $payload = json_decode(json_encode($response->payload), true);
                                $token = isset($payload["access_token"]) ? $payload["access_token"] : "";
                            }

                            if(!$token){
                                $syncSaleCron->cron_process_track_in_action = 3;
                                $syncSaleCron->update('cron_process_track_in_action');
                                Yii::$app->slack->send(
                                    'cronjob-orderSales: failed to get sysAuth token',
                                    array(
                                        array(
                                            'color' => 'warning',
                                            'text' => "",
                                        ),
                                    )
                                );
                                break;
                            }
                            
                            $body = [];
                            foreach ($orderSales as $sale) {
                                $type = "complete";
                                if ($sale['status'] != $completedStatus) {
                                    $type = 'rollback';
                                }
                                $bodayData = [
                                    $prefix . '_id' => (string)$sale['id'],
                                    'user_id' => (string)$sale['user_id'],
                                    'php_reference_id' => (string)$sale['ref_id'],
                                    'amount' => (string)$sale['sync_amount'],
                                    $prefix . '_type' => (string)$type,
                                    'status_date' => (string)($sale['status_date'] * 1000),
                                    $prefix . '_created_at' => (string)($sale['created_at'] * 1000),
                                ];
                                if (isset($sale['listing_id'])) {
                                    $bodayData['listing_id'] = $sale['listing_id'];
                                }
                                if (isset($sale['data']) && $sale['data']) {
                                    if(@json_decode($sale['data']) != NULL){
                                        $bodayData['data'] = json_decode($sale['data'], true);
                                    }                                }
                                $body[] = $bodayData;
                            }

                            // $token = GeneralCom::getToken('admin', 'admin', Yii::$app->params['og.serverless.admin']);
                            // $curl_com = new CurlCom();
                            // $url = $syncUrl;
                            $curl_com = new \common\components\CurlCom();
                            $curl_com->request_headers = [
                                'Authorization' => $token,
                                // 'x-api-key' => Yii::$app->params['admin.api.key']
                            ];

                            $json = json_encode($body);
                            // $response_json = $curl_com->curlPost($url, $json, '', false, $header);
                            $response = $curl_com->sendPost($syncUrl, $json, true);

                            $response_payload = [];
                            if(isset($response->payload)){
                                $response_payload = json_decode(json_encode($response->payload), true);
                            }
                            // $result = json_encode($response_json);

                            $currentTime = time();
                            // if (isset($result['payload']['success']) && $result['payload']['success']) {
                            //     //update sync date and sync status based on success ref_id
                            //     $orderSalesModel::updateAll(
                            //         [
                            //             'sync_status' => 1,
                            //             'sync_date' => $currentTime
                            //         ],
                            //         ['IN', 'id', $result['payload']['success']]

                            //     );
                            // }
                            if (isset($response_payload['fail']) && $response_payload['fail'] == 0) {
                                $orderSalesModel::updateAll(
                                    [
                                        'sync_status' => 1,
                                        'sync_date' => $currentTime
                                    ],
                                    ['IN', 'id', $orderSales]
                                );
                            } else {
                                //slack out the update failed case
                                // \Yii::$app->slack->send("cronjob-orderSales: failed to sync to sls ~ ");
                                Yii::$app->slack->send(
                                    'cronjob-orderSales: failed to sync to sls',
                                    array(
                                        array(
                                            'color' => 'warning',
                                            'text' => $currentTime . " ~ " . json_encode($response),
                                        ),
                                    )
                                );
                            }
                            // \Yii::$app->slack->send("test actionCheckNrpOrders: " . json_encode($result));
                            $syncSaleCron->cron_process_track_in_action = 3;
                            $syncSaleCron->update('cron_process_track_in_action');

                        } else {
                            $syncSaleCron->cron_process_track_in_action = 1;
                            $syncSaleCron->update('cron_process_track_in_action');
                        }
                    } else {
                        $syncSaleCron->cron_process_track_in_action = 3;
                        $syncSaleCron->update('cron_process_track_in_action');
                    }

                    break;
                    default:
                        $syncSaleCron->cron_process_track_failed_attempt = $syncSaleCron->cron_process_track_failed_attempt+1;
                        if($syncSaleCron->cron_process_track_failed_attempt == "15"){
                            // \Yii::$app->slack->send("cronjob-orderSales: failed to process CronProcessTrack ~ " . json_encode($syncSaleCron->attributes));
                            Yii::$app->slack->send(
                                'cronjob-orderSales: failed to process CronProcessTrack',
                                array(
                                    array(
                                        'color' => 'warning',
                                        'text' => json_encode($syncSaleCron->attributes),
                                    ),
                                )
                            );
                            $syncSaleCron->cron_process_track_failed_attempt = 0;
                        }
                        $syncSaleCron->update('cron_process_track_failed_attempt');
            }
        }
    }

    /**
     * cron/push-einvoice-to-queue
     *
     * command :
     * - php yii cron/push-einvoice-to-queue
     */
    public function actionPushEinvoiceToQueue() {
        // Query new einvoice submissions with einvoice_status = 1 from einvoice table
        $db = Yii::$app->db;
        $sql = "SELECT einvoice_id, orders_id, file_raw_data FROM einvoice WHERE einvoice_status = 1 ORDER BY created_at ASC LIMIT 100";
        $invoices = $db->createCommand($sql)->queryAll();

        foreach ($invoices as $invoice) {
            // Update DB record to pending
            $db->createCommand()->update(
                'einvoice',
                [
                    'einvoice_status' => 2, //pending
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['einvoice_id' => $invoice['einvoice_id']]
            )->execute();

            // Push data to EINVOICE_FIFO_QUEUE
            try {
                $this->pushToFifoQueue(
                    'EINVOICE_FIFO_QUEUE',
                    $invoice['file_raw_data'],
                    $invoice['orders_id']
                );
            } catch (\Exception $e) {
                // DO Nothing on SQS Failure
            }
        }
    }

    // There's a bug in toolbox-multiservice/components/AWSSQS.php, temporary fix
    private function pushToFifoQueue($queueName, $payload, $messageGroupId = 'default') {
        $sqs = Yii::$app->aws->getSQS($queueName);
        $sqsClient = $sqs->getInstance();
        $sqsConfig = Yii::$app->aws->sqs[$queueName];

        $params = [
            'MessageBody' => $payload,
            'QueueUrl' => $sqsConfig['queue_url'],
            'MessageGroupId' => $messageGroupId,
            'MessageDeduplicationId' => hash('sha256', $payload),
        ];
        
        return $sqsClient->sendMessage($params);
    }

}