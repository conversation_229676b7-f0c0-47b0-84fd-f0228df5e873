<?php

namespace console\controllers;

use yii\console\Controller;
use common\components\publishers\Devco;

/**
 * Devco controller
 */
class DevcoController extends Controller
{
    /**
     * devco/get-oem-list
     *
     * command :
     * - php yii devco/get-oem-list <publisher_id>
     */
    public function actionGetOemList($publisher_id = "")
    {
        (new Devco)->customRequest('get_OEMList', $publisher_id, [], true);
    }

    /**
     * devco/get-brand-list
     *
     * command :
     * - php yii devco/get-brand-list <publisher_id>
     */
    public function actionGetBrandList($publisher_id = "")
    {
        (new Devco)->customRequest('get_BrandList', $publisher_id, [], true);
    }

    /**
     * devco/get-product-list
     *
     * command :
     * - php yii devco/get-product-list <publisher_id>
     */
    public function actionGetProductList($publisher_id = "")
    {
        (new Devco)->customRequest('get_ProductList', $publisher_id, [], true);
    }

    /**
     * devco/get-product-availability
     * @var $sku string `DEVCO_USD_237`
     *
     * command :
     * - php yii devco/get-product-availability <publisher_id> <product_id>
     */
    public function actionGetProductAvailability($publisher_id = "", $product_id = "")
    {
        $param = [
            'PRODUCT_ID' => $product_id
        ];
        (new Devco)->customRequest('get_ProductAvailability', $publisher_id, $param, false);
    }

    /**
     * devco/get-sales-transaction
     * @var $fr_date datetime `2020-01-01`
     * @var $to_date datetime `2021-02-01`
     *
     * command :
     * - php yii devco/get-sales-transaction <publisher_id> <fr_date> <to_date>
     */
    public function actionGetSalesTransaction($publisher_id = "", $fr_date = "", $to_date = "")
    {
        $param = [
            'FROM_DATE' => $fr_date,
            'TO_DATE' => $to_date,
        ];
        (new Devco)->customRequest('get_SalesTransaction_ByDateRange', $publisher_id, $param, true);
    }

    /**
     * devco/get-financial-transaction
     * @var $fr_date datetime `2020-01-01`
     * @var $to_date datetime `2021-02-01`
     *
     * command :
     * - php yii devco/get-financial-transaction <publisher_id> <fr_date> <to_date>
     */
    public function actionGetFinancialTransaction($publisher_id = "", $fr_date = "", $to_date = "")
    {
        $param = [
            'FROM_DATE' => $fr_date,
            'TO_DATE' => $to_date,
        ];

        (new Devco)->customRequest('get_FinancialTransaction_ByDateRange', $publisher_id, $param, true);
    }

    /**
     * devco/get-all-product-availability
     *
     * command :
     * - php yii devco/get-all-product-availability <publisher_id>
     */
    public function actionGetAllProductAvailability($publisher_id = "")
    {
        (new Devco)->customRequest('get_AllProductAvailability', $publisher_id, [], true);
    }

    /**
     * devco/get-product-formats
     *
     * command :
     * - php yii devco/get-product-formats <publisher_id>
     */
    public function actionGetProductFormats($publisher_id = "")
    {
        (new Devco)->customRequest('get_ProductFormats', $publisher_id, [], true);
    }

    /**
     * devco/get-account-balance
     *
     * command :
     * - php yii devco/get-account-balance <publisher_id>
     */
    public function actionGetAccountBalance($publisher_id = "")
    {
        (new Devco)->customRequest('get_AccountBalance', $publisher_id, [], false);
    }
}