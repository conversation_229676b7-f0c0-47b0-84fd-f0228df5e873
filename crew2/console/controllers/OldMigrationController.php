<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use common\models\CategoriesStructures;
use common\models\Countries;
use common\models\Categories;
use common\models\CategoriesDescription;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class OldMigrationController extends Controller
{
    public $db;
    protected array $brand_list = [];
    protected array $restriction_list = [];
    protected array $category_notice_description_list = [];

    protected array $category_brand = [];
    protected array $brand_description = [];
    protected array $brand_metadata = [];
    protected array $category_description = [];
    protected array $category_metadata = [];
    protected array $soft_block = [];

    /**
     * {@inheritdoc}
     */
    public function actionMigrate($cat_list)
    {
        $this->db = Yii::$app->db_og;
        $this->initBrandList();
        $this->initRestrictionList();

        $categories_id_list_layer2 = explode(',', $cat_list);

        $categories_layer2_list = $this->getCategoriesListLayer2($categories_id_list_layer2);

        $this->getCategoriesNoticeDescription($categories_id_list_layer2);

        foreach ($categories_layer2_list as $category_row) {
            if (empty($this->brand_list[$category_row['parent_id']])) {
                $categories_layer1 = $this->getCategoriesLayer1($category_row['parent_id']);

                /**
                 * Check the layer 1 is it qualified
                 * 1. if the layer 1 is null
                 * 2. if the layer 1 have parent_id
                 */
                if (!$categories_layer1 || $categories_layer1['parent_id']) {
                    continue;
                }

                $brand = [
                    'seo_url' => $categories_layer1['categories_url_alias'],
                    'image_url' => '',
                    'show_in_search_result' => 1,
                    'search_keyword' => '',
                    'status' => $categories_layer1['categories_status'] ?? 1,
                    'sort_order' => $categories_layer1['sort_order'] ?? 0,
                ];

                $this->insert('{{%brand}}', $brand);

                $brand_id = Yii::$app->db_og->getLastInsertID();
                $this->brand_list[$categories_layer1['categories_id']] = $brand_id;

                $this->setBrandDetailArray($brand_id, $categories_layer1['categories_id']);

                // If more than 100 record then batch insert first
                $this->batchInsertBrandDetail(0);
            }

            // in case the categories_url_alias is empty, convert again from the name
            if (empty($category_row['categories_url_alias'])) {
                $category_row['categories_url_alias'] = $this->convertSeoURL(
                    $category_row['categories_id'],
                    'category'
                );
            }

            $category_data = [
                'categories_id' => $category_row['categories_id'],
                'brand_id' => $this->brand_list[$category_row['parent_id']],
                'seo_url' => $category_row['categories_url_alias'],
                'sort_order' => $category_row['sort_order'] ?: 5000,
                'status' => $category_row['categories_status'] ?? 0
            ];

            $this->insert('{{%category}}', $category_data);

            $category_id = Yii::$app->db_og->getLastInsertID();

            $this->setCategoryDetailArray($category_id, $category_row['categories_id']);

            $list = [];
            $list[] = [$this->brand_list[$category_row['parent_id']], 2, $category_row['categories_id']];
            $this->batchInsert('{{brand_category}}', ['brand_id', 'type', 'sub_id'], $list);

            // If more than 100 record then batch insert first
            $this->batchInsertCategoryDetail(0);
        }

        // Insert the balance.
        $this->batchInsertBrandDetail(0);
        $this->batchInsertCategoryDetail(0);
    }

    private function initBrandList()
    {
        $categories_brand_list = (new Query)
            ->select(['categories_id', 'brand_id'])
            ->from('categories_brand_list')
            ->all(Yii::$app->db_og);

        $this->brand_list = ArrayHelper::map($categories_brand_list, 'categories_id', 'brand_id');
    }

    private function initRestrictionList()
    {
        $allowed_list = [];

        $default_country = (new Query())
            ->select('countries_iso_code_2')
            ->from('configuration t')
            ->leftJoin('countries c', 't.configuration_value = c.countries_id')
            ->where(['t.configuration_key' => 'STORE_COUNTRY'])
            ->scalar();

        $country_list = ArrayHelper::getColumn(
            Countries::find()
                ->select(['countries_iso_code_2'])
                ->asArray()
                ->all(),
            "countries_iso_code_2"
        );

        $zones_info_list = (new Query())
            ->select(['c.countries_iso_code_2', 'zi.geo_zone_info'])
            ->from('geo_zones gz')
            ->innerJoin('zones_to_geo_zones zgz', 'gz.geo_zone_id = zgz.geo_zone_id')
            ->innerJoin('countries c', 'zgz.zone_country_id = c.countries_id')
            ->leftJoin('zones_info zi', 'zi.geo_zone_id = gz.geo_zone_id')
            ->where(['gz.geo_zone_type' => 1])
            ->all(Yii::$app->db_slave_offgamers);

        $sanction_country = [
            'CD',
            'CU',
            'IR',
            'KP',
            'LY',
            'SD',
            'SO',
            'SS',
            'SY',
            'YE'
        ];

        $unassigned_country = array_unique(
            array_diff($country_list, ArrayHelper::getColumn($zones_info_list, 'countries_iso_code_2'))
        );

        foreach ($zones_info_list as $zonesInfo) {
            if (!empty($zonesInfo['countries_iso_code_2']) && !empty($zonesInfo['geo_zone_info'])) {
                $zones_info_array = json_decode($zonesInfo['geo_zone_info'], JSON_OBJECT_AS_ARRAY);
                if (isset($zones_info_array['zone_categories_id']) && !empty($zones_info_array['zone_categories_id'])) {
                    foreach ($zones_info_array['zone_categories_id'] as $zones_info_row) {
                        if (empty($allowed_list[$zones_info_row])) {
                            $allowed_list[$zones_info_row] = [];
                        }

                        if ($zonesInfo['countries_iso_code_2'] == $default_country) {
                            $allowed_list[$zones_info_row] = array_merge(
                                $allowed_list[$zones_info_row],
                                $unassigned_country
                            );
                        }

                        $allowed_list[$zones_info_row][] = $zonesInfo['countries_iso_code_2'];
                    }
                }
            }
        }

        foreach ($allowed_list as $categories_id => $allow_country_list) {
            $this->restriction_list[$categories_id] = array_diff($country_list, $allow_country_list, $sanction_country);
        }
    }

    private function setBrandDetailArray($brand_id, $categories_id)
    {
        $this->setCategoryBrandArray($brand_id, $categories_id);
        $this->setBrandDescriptionArray($brand_id, $categories_id);
        $this->setBrandMetadataArray($brand_id, $categories_id);
    }

    private function batchInsertBrandDetail($min_row)
    {
        $this->batchInsertCategoryBrand($min_row);
        $this->batchInsertBrandDescription($min_row);
        $this->batchInsertBrandMetadata($min_row);
    }

    private function setCategoryDetailArray($category_id, $categories_id)
    {
        $this->insertCategoryDescriptionArray($category_id, $categories_id);
        $this->insertCategoryMetadataArray($category_id, $categories_id);
        $this->insertSoftBlockArray($category_id, $categories_id);
    }

    private function batchInsertCategoryDetail($min_row)
    {
        $this->batchInsertCategoryDescription($min_row);
        $this->batchInsertMetadata($min_row);
        $this->batchInsertSoftBlock($min_row);
    }

    private function getCategoriesListLayer2($categories_id_list)
    {
        return Categories::find()
            ->select(['parent_id', 'categories_id', 'categories_url_alias', 'sort_order', 'categories_status'])
            ->where(['IN', 'categories_id', $categories_id_list])
            ->andWhere(['<>', 'parent_id', '0'])
            ->asArray()
            ->all();
    }

    private function getCategoriesLayer1($categories_id)
    {
        $categories = Categories::find()
            ->select(['parent_id', 'categories_id', 'categories_url_alias', 'sort_order', 'categories_status'])
            ->where(['categories_id' => $categories_id])
            ->asArray()
            ->one();

        if (empty($categories['categories_url_alias'])) {
            $categories['categories_url_alias'] = $this->convertSeoURL($categories['categories_id'], 'brand');
        }

        return $categories;
    }

    private function getCategoriesDescriptionList($categories_id, $language_id = 0)
    {
        $query = CategoriesDescription::find()
            ->select(['language_id', 'categories_name', 'categories_description'])
            ->where(['categories_id' => $categories_id]);

        if ($language_id) {
            $query->andWhere(['language_id' => $language_id]);
        }

        return $query->asArray()->all();
    }

    private function setBrandDescriptionArray($brand_id, $categories_id)
    {
        $categories_description_list = $this->getCategoriesDescriptionList($categories_id);
        foreach ($categories_description_list as $categories_description) {
            $this->brand_description[] = [
                $brand_id,
                $categories_description['language_id'],
                $categories_description['categories_name'],
                $categories_description['categories_description']
            ];
        }
    }

    private function batchInsertBrandDescription($min_row)
    {
        if (count($this->brand_description) > $min_row) {
            $this->batchInsert('{{brand_description}}', ['brand_id', 'language_id', 'name', 'description']
                , $this->brand_description);
            $this->brand_description = [];
        }
    }

    private function setBrandMetadataArray($brandId, $categories_id)
    {
        $categories_metadata = $this->getCustomSeo($categories_id);
        if ($categories_metadata) {
            $custom_seo_translation_list = $this->getCustomSeoTranslation($categories_metadata['custom_seo_id']);
            foreach ($custom_seo_translation_list as $custom_seo_translation) {
                $this->brand_metadata[] = [
                    $brandId,
                    $custom_seo_translation['language_id'],
                    $custom_seo_translation['meta_title'],
                    $custom_seo_translation['meta_keyword'],
                    $custom_seo_translation['meta_description']
                ];
            }
        }
    }

    private function batchInsertBrandMetadata($min_row)
    {
        if (count($this->brand_metadata) > $min_row) {
            $this->batchInsert(
                '{{brand_metadata}}',
                ['brand_id', 'language_id', 'meta_title', 'meta_keyword', 'meta_description']
                ,
                $this->brand_metadata
            );
            $this->brand_metadata = [];
        }
    }

    private function setCategoryBrandArray($brandId, $categories_id)
    {
        $this->category_brand[] = [
            $brandId,
            $categories_id
        ];
    }

    private function batchInsertCategoryBrand($min_row)
    {
        if (count($this->category_brand) > $min_row) {
            $this->batchInsert('{{categories_brand_list}}', ['brand_id', 'categories_id']
                , $this->category_brand);
            $this->category_brand = [];
        }
    }

    private function getCustomSeo($categories_id)
    {
        return (new Query)
            ->select(['custom_seo_id'])
            ->from(['custom_seo'])
            ->where(['type' => 5, 'reference_data_id' => $categories_id])
            ->one();
    }

    private function getCustomSeoTranslation($customSeoID)
    {
        return (new Query)
            ->select(['language_id', 'meta_title', 'meta_keyword', 'meta_description'])
            ->from('custom_seo_translation')
            ->where(['custom_seo_id' => $customSeoID])
            ->all();
    }

    private function insertCategoryDescriptionArray($category_id, $categories_id)
    {
        $categories_description_layer2_list = $this->getCategoriesDescriptionList($categories_id);
        foreach ($categories_description_layer2_list as $categories_description_layer2) {
            $this->category_description[] = [
                $category_id,
                $categories_description_layer2['language_id'],
                    $categories_description_layer2['categories_name'] ?? '',
                    $this->category_notice_description_list[$categories_id][$categories_description_layer2['language_id']]['notice'] ?? '',
                    $this->category_notice_description_list[$categories_id][$categories_description_layer2['language_id']]['description'] ?? ($categories_description_layer2['categories_description'] ?? '')
            ];
        }
    }

    private function insertCategoryMetadataArray($category_id, $categories_id)
    {
        $categories_metadata = $this->getCustomSeo($categories_id);
        if ($categories_metadata) {
            $custom_seo_translation_list = $this->getCustomSeoTranslation($categories_metadata['custom_seo_id']);
            foreach ($custom_seo_translation_list as $custom_seo_translation) {
                $this->category_metadata[] = [
                    $category_id,
                        $custom_seo_translation['language_id'] ?? '',
                        $custom_seo_translation['meta_title'] ?? '',
                        $custom_seo_translation['meta_keyword'] ?? '',
                        $custom_seo_translation['meta_description'] ?? '',
                ];
            }
        }
    }

    private function insertSoftBlockArray($category_id, $categories_id)
    {
        if (isset($this->restriction_list[$categories_id])) {
            foreach ($this->restriction_list[$categories_id] as $restriction) {
                $this->soft_block[] = [
                    $category_id,
                    $restriction
                ];
            }
        }
    }

    private function batchInsertCategoryDescription($min_row)
    {
        if (count($this->category_description) > $min_row) {
            $this->batchInsert(
                '{{category_description}}',
                ['category_id', 'language_id', 'name', 'notice', 'description']
                ,
                $this->category_description
            );
            $this->category_description = [];
        }
    }

    private function batchInsertMetadata($min_row)
    {
        if (count($this->category_metadata) > $min_row) {
            $this->batchInsert(
                '{{category_metadata}}',
                ['category_id', 'language_id', 'title', 'keyword', 'description']
                ,
                $this->category_metadata
            );
            $this->category_metadata = [];
        }
    }

    private function batchInsertSoftBlock($min_row)
    {
        if (count($this->soft_block) > $min_row) {
            $this->batchInsert('{{category_soft_block}}', ['category_id', 'country_code']
                , $this->soft_block);
            $this->soft_block = [];
        }
    }

    // Type = Brand or Category
    private function convertSeoURL($categories_id, $type)
    {
        $categories_description_list = $this->getCategoriesDescriptionList($categories_id, 1);
        if (!empty($categories_description_list) && isset($categories_description_list[0])) {
            $seo_url = str_replace(
                    ' ',
                    '-',
                    preg_replace(
                        '/\s+/',
                        ' ',
                        preg_replace(
                            '/[^a-zA-Z0-9\s-]/',
                            "",
                            strtolower($categories_description_list[0]['categories_name'])
                        )
                    )
                ) . '-' . $categories_id;

            $this->slackNotice($categories_id, $type, $seo_url);
            return $seo_url;
        }
        $this->slackNotice($categories_id, $type, $categories_id);
        return $categories_id;
    }

    private function getCategoriesNoticeDescription($categories_id_array)
    {
        $front_template_result = (new Query())
            ->select(['ft.id', 'ftl.language_id', 'ftl.notice', 'ftl.description'])
            ->from('frontend_template ft')
            ->innerJoin('frontend_template_lang ftl', 'ft.tpl_id = ftl.tpl_id')
            ->where([
                'ft.id_type' => '0',
                'ft.id' => $categories_id_array
            ])
            ->all(Yii::$app->db_slave_offgamers);

        foreach ($front_template_result as $front_template_row) {
            if (!isset($this->category_notice_description_list[$front_template_row['id']])) {
                $this->category_notice_description_list[$front_template_row['id']] = [];
            }
            if (!isset($this->category_notice_description_list[$front_template_row['id']][$front_template_row['language_id']])) {
                $this->category_notice_description_list[$front_template_row['id']][$front_template_row['language_id']] = [];
            }
            $this->category_notice_description_list[$front_template_row['id']][$front_template_row['language_id']]['notice'] = $front_template_row['notice'];
            $this->category_notice_description_list[$front_template_row['id']][$front_template_row['language_id']]['description'] = $front_template_row['description'];
        }
    }

    private function slackNotice($categories_id, $type, $seo_url)
    {
        Yii::$app->slack->send("Category Migration ($type)", [
            [
                'color' => 'warning',
                'text' => "The $type (Categories ID - $categories_id) SEO URL updated as $seo_url"
            ]
        ]);
    }

    public function batchInsert($table, $columns, $rows)
    {
        $this->db->createCommand()->batchInsert($table, $columns, $rows)->execute();
    }

    public function insert($table, $columns)
    {
        $this->db->createCommand()->insert($table, $columns)->execute();
    }
}
