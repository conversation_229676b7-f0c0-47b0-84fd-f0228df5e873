<?php

namespace console\controllers;

use console\models\SitemapXML;
use yii\console\Controller;

class SitemapController extends Controller
{
    /*
     * Command to generate sitemap
     * php yii sitemap/index => generate and upload to s3
     * php yii sitemap/index false => generate without upload
    */
    public function actionIndex($s3Upload = true)
    {
        $sitemapXML = new SitemapXML();
        $sitemapXML->generateXML($s3Upload);
    }
}
