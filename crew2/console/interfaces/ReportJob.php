<?php

namespace console\interfaces;

use common\models\OrdersProductsExtraInfo;
use common\models\ProductsCost;
use console\components\coupon\reports\usage\CouponUsageReport;
use offgamers\base\components\Currency;
use offgamers\base\models\CurrenciesHistory;
use offgamers\base\models\OrdersTaxConfiguration;
use offgamers\base\models\ProductsTaxCategory;
use Yii;
use yii\base\BaseObject;
use yii\db\Exception;
use yii\helpers\Json;
use yii\queue\JobInterface;
use common\models\OrdersStatus;
use common\models\Countries;

class ReportJob extends BaseObject implements JobInterface
{

    public $start_time;
    private $s3;
    public $type, $params;

    public function execute($queue)
    {
        if (isset($this->type)) {
            switch ($this->type) {
                case 'tax':
                    $this->taxReport();
                    break;
            }
        }
    }

    public function taxReport()
    {
        ini_set("memory_limit", "-1");
        set_time_limit(0);
        /*
         * Customer Mobile Country & either one of
         * - IP Country / Address Country / Email Domain ( e.g. *.sg )
         */

        if (isset($this->params['recipient']) && !empty($this->params['recipient'])) {
            $m_ctry = Countries::find()->where(['countries_iso_code_2' => $this->params['country_code']])->one();
            if (isset($m_ctry->countries_id)) {
                $tax_profile = OrdersTaxConfiguration::find()->where(['country_code' => $this->params['country_code']]
                )->one();
                $home_currency = $tax_profile['currency'] ?? 'USD';
                $tax_percentage = $tax_profile['orders_tax_percentage'];
                $filename = "tax_report/" . $this->params['country_code'] . "_" . date(
                        "Ymd",
                        strtotime($this->params['start_date'])
                    ) . "_" . date("Ymd", strtotime($this->params['end_date'])) . "_" . date("YmdHis") . ".csv";
                $header = [
                    'Order',
                    'Order Date',
                    'Order Status',
                    'Product Id',
                    'Product Name',
                    'Bundle?',
                    'Payment Method',
                    'Currency',
                    'IP Address',
                    'IP Country',
                    'Selling Price (Home Currency)',
                    'Selling Price (USD)',
                    'Surcharge (+)',
                    'DC (-)',
                    'SC (-)',
                    'Tax (+)',
                    'Total',
                    'Tax %',
                    'Mobile Country',
                    'Address Country',
                    'Email',
                    'Merchant Name',
                    'MRV/Non-MRV/Store Credit',
                    'Order Quantity',
                    'Exchange Rate (Spot Rate)',
                    'Exchange Rate (Sell Rate)',
                    'Delivered Quantity',
                    'FVV',
                    'FVV Currency',
                    'FVV (Home Currency)',
                    'FVV USD',
                    'Cost',
                    'Cost (Home Currency)',
                    'Cost (USD)',
                    'Margin (Home Currency)',
                    'Margin (USD)',
                    'Profit (Home Currency)',
                    'Profit (USD)',
                    'GST0 (Home Currency)',
                    'GST0 (USD)',
                    'GST1 (Home Currency)',
                    'GST1 (USD)',
                    'GST2 (Home Currency)',
                    'GST2 (USD)',
                    'GST3 (Home Currency)',
                    'GST3 (USD)',
                    'Inv GST (USD)',
                    'Inv GST (Currency)',
                    'Total GST (Home Currency)',
                    'Total GST (USD)',
                ];
                $fp = fopen('php://temp', 'w+');
                fputcsv($fp, $header);

                $m_os = (new OrdersStatus)->getOrdersStatus();

                $res = Yii::$app->db_slave_offgamers
                    ->createCommand(
                        'SELECT o.orders_id, o.customers_id, o.customers_country, o.customers_telephone_country,
                            o.customers_email_address, o.payment_method, o.payment_methods_parent_id,
                            o.date_purchased, o.orders_status, o.currency, o.currency_value, o.remote_addr
                        FROM orders AS o
                        INNER JOIN customers AS c
                            ON c.customers_id = o.customers_id
                        LEFT JOIN orders_extra_info AS oei
                            ON oei.orders_id = o.orders_id
                            AND oei.orders_extra_info_key = "site_id"
                        WHERE o.date_purchased >= "' . date("Y-m-d 00:00:00", strtotime($this->params['start_date'])) . '"
                            AND o.date_purchased <= "' . date("Y-m-d 23:59:59", strtotime($this->params['end_date'])) . '"
                            AND o.customers_telephone_country = "' . $m_ctry->countries_name . '"
                            AND (oei.orders_extra_info_value IS NULL OR oei.orders_extra_info_value != 5)
                        GROUP BY o.orders_id'
                    )
                    ->queryAll();
                $currencies = new Currency();
                foreach ($res as $row) {
                    $tax_invoice = false;
                    $subtotal = $sc = $coupon = $tax = $total = "";
                    $surcharge = 0;
                    $cost = "";
                    $ip_country = "Unknown";
                    $orders_status = (isset($m_os[$row["orders_status"]]) ? $m_os[$row["orders_status"]] : $row["orders_status"]);

                    $m_ch = Yii::$app->db_slave_offgamers
                        ->createCommand(
                            'SELECT ch.buy_value, ch.spot_value
                                    FROM currencies_history AS ch
                                    WHERE ch.code = "' . $row["currency"] . '"
                                        AND ch.date_from <= "' . $row['date_purchased'] . '"
                                        AND ch.date_to >= "' . $row['date_purchased'] . '"'
                        )
                        ->queryOne();


                    if (isset($this->params['adv_search']) && ($this->params['adv_search'] == true)) {
                        $tax_rule_mappings = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                'SELECT * FROM orders_tax_rule_mapping otrm inner join orders_tax_rules otr on otrm.tax_rule_id = otr.tax_rule_id and otr.status  = 1 where otrm.orders_tax_id = ' . $tax_profile['orders_tax_id'] . ' AND otrm.status = 1 ORDER BY otrm.sort_order ASC'
                            )
                            ->queryAll();
                        $tax_rule_bol = [];
                        foreach ($tax_rule_mappings as $rule_idx => $tax_rule_mapping) {
                            $tax_rule = Yii::$app->db_slave_offgamers
                                ->createCommand(
                                    'SELECT * FROM orders_tax_rules where tax_rule_id = ' . $tax_rule_mapping['tax_rule_id'] . ' AND status = 1'
                                )
                                ->queryOne();
                            $tax_rule_items = Yii::$app->db_slave_offgamers
                                ->createCommand(
                                    'SELECT * FROM orders_tax_rule_details where tax_rule_id = ' . $tax_rule['tax_rule_id'] . ' AND status = 1'
                                )
                                ->queryAll();
                            $_row = Yii::$app->db_slave_offgamers
                                ->createCommand(
                                    'SELECT oei.orders_extra_info_value AS ip_country
                                FROM orders_extra_info AS oei
                                WHERE oei.orders_id = ' . $row["orders_id"] . '
                                    AND orders_extra_info_key = "ip_country"'
                                )
                                ->queryOne();
                            $tax_criteria_bol = [];
                            foreach ($tax_rule_items as $item_idx => $tax_rule_item) {
                                switch ($tax_rule_item['fieldname']) {
                                    case 'CURRENCY':
                                        if (($row["currency"] == $tax_rule_item['values']) || (empty($tax_rule_item['values']) && $row["currency"] == $home_currency)) {
                                            $tax_criteria_bol[$item_idx] = true;
                                        } else {
                                            $tax_criteria_bol[$item_idx] = false;
                                        }
                                        break;
                                    case 'BILLING_COUNTRY':
                                        if (!empty($tax_rule_item['values'])) {
                                            $ctry = Countries::find()->where(
                                                ['countries_iso_code_2' => $tax_rule_item['values']]
                                            )->one();
                                            $country = $ctry->countries_name;
                                        } else {
                                            $country = $m_ctry->countries_name;
                                        }
                                        if ($row["customers_country"] == $country) {
                                            $tax_criteria_bol[$item_idx] = true;
                                        } else {
                                            $tax_criteria_bol[$item_idx] = false;
                                        }
                                        break;
                                    case 'IP_COUNTRY':
                                        if (!empty($tax_rule_item['values'])) {
                                            $ctry = Countries::find()->where(
                                                ['countries_iso_code_2' => $tax_rule_item['values']]
                                            )->one();
                                            $country = $ctry->countries_name;
                                            $country_code = $ctry->countries_iso_code_2;
                                            $country_id = $ctry->countries_id;
                                        } else {
                                            $country = $m_ctry->countries_name;
                                            $country_code = $m_ctry->countries_iso_code_2;
                                            $country_id = $m_ctry->countries_id;
                                        }
                                        if (isset($_row["ip_country"]) && ($_row["ip_country"] == $country_id)) {
                                            $tax_criteria_bol[$item_idx] = true;
                                            $ip_country = $country;
                                        } else {
                                            $_row1 = Yii::$app->db_slave_offgamers
                                                ->createCommand(
                                                    'SELECT oei.orders_extra_info_value AS payment_ip_country
                                                                   FROM orders_extra_info AS oei
                                                                  WHERE oei.orders_id = ' . $row["orders_id"] . '
                                                                     AND orders_extra_info_key = "payment_ip_country"'
                                                )
                                                ->queryOne();
                                            if (isset($_row1["payment_ip_country"]) && ($_row1["payment_ip_country"] == $country_code)) {
                                                $tax_criteria_bol[$item_idx] = true;

                                                $_mctry = Countries::find()->where(
                                                    ['countries_iso_code_2' => $_row1["payment_ip_country"]]
                                                )->one();
                                                $ip_country = (isset($_mctry->countries_name) ? $_mctry->countries_name : $_row1["payment_ip_country"]);
                                            }
                                            $tax_criteria_bol[$item_idx] = false;
                                        }
                                        break;
                                    case 'EMAIL_COUNTRY':
                                        if (isset($this->params['adv_email_postfix']) && !empty($this->params['adv_email_postfix'])) {
                                            if (preg_match(
                                                "/\.(" . $this->params['adv_email_postfix'] . ")$/",
                                                $row["customers_email_address"]
                                            )) {
                                                $tax_criteria_bol[$item_idx] = true;
                                            } else {
                                                $tax_criteria_bol[$item_idx] = false;
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                }
                                if (($tax_rule['rule_type'] == 'OR')) {
                                    if (in_array(true, $tax_criteria_bol)) {
                                        $tax_rule_bol[$rule_idx] = true;
                                        break;
                                    } else {
                                        $tax_rule_bol[$rule_idx] = false;
                                    }
                                } else {
                                    if (in_array(false, $tax_criteria_bol)) {
                                        $tax_rule_bol[$rule_idx] = false;
                                        break;
                                    } else {
                                        $tax_rule_bol[$rule_idx] = true;
                                    }
                                }
                            }
                        }


                        $tax_invoice = !in_array(false, $tax_rule_bol);
                    }

                    if (!$tax_invoice) {
                        // invoice
                        $_row = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                "SELECT i.file_raw_data, i.invoice_number
                                FROM invoice AS i
                                WHERE i.orders_id = " . $row["orders_id"] . "
                                    AND invoice_type = 'tax-invoice'"
                            )
                            ->queryOne();
                        if (isset($_row['invoice_number']) && preg_match(
                                "/^INV" . $this->params['country_code'] . "/",
                                $_row["invoice_number"]
                            )) {
                            $tax_invoice = true;

                            $_data = json_decode($_row['file_raw_data'], true);
                            $ip_country = (isset($_data['checkout_info']['ip_country']) ? $_data['checkout_info']['ip_country'] : $ip_country);
                        }
                    }
                    $sc = $coupon = $tax = $surcharge = $total = 0;

                    if ($tax_invoice) {
                        $_res = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                'SELECT ot.value, ot.class FROM orders_total AS ot WHERE ot.orders_id = ' . $row["orders_id"]
                            )
                            ->queryAll();
                        foreach ($_res as $_row) {
                            switch ($_row["class"]) {
                                case "ot_subtotal":
                                    $subtotal = $_row["value"];
                                    break;
                                case "ot_gv":
                                    $sc = $_row["value"];
                                    break;
                                case "ot_coupon":
                                    $coupon = $_row["value"];
                                    break;
                                case "ot_gst":
                                    $tax = $_row["value"];
                                    break;
                                case "ot_surcharge":
                                    $surcharge = $_row["value"];
                                    break;
                                case "ot_total":
                                    $total = $_row["value"];
                                    break;
                            }
                        }

                        $_row = Yii::$app->db_slave_report
                            ->createCommand(
                                "SELECT go.total_purchase_in_usd FROM gp_order AS go WHERE go.order_id = " . $row["orders_id"]
                            )
                            ->queryOne();
                        $cost = (isset($_row["total_purchase_in_usd"]) ? $_row["total_purchase_in_usd"] : 0);

                        $_res = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                'SELECT op.orders_products_id, op.products_id, op.products_name,
                                op.final_price, op.products_quantity, op.products_good_delivered_quantity, op.products_good_delivered_price, op.custom_products_type_id
                            FROM orders_products AS op
                            WHERE op.orders_id = ' . $row["orders_id"] . '
                                AND op.products_bundle_id = 0
                                AND op.orders_products_is_compensate = 0'
                            )
                            ->queryAll();
                        foreach ($_res as $_row) {
                            //loop for all sub items
                            $bundle_flag = false;
                            $delivered_qty = $_row['products_good_delivered_quantity'];
                            $ops = Yii::$app->db_slave_offgamers
                                ->createCommand(
                                    'SELECT op.orders_products_id, op.products_id, op.products_name, op.products_bundle_id,
                                        op.products_good_delivered_quantity, op.final_price, op.products_quantity, op.custom_products_type_id
                                    FROM orders_products AS op
                                    WHERE op.orders_id = ' . $row["orders_id"] . '
                                    AND op.parent_orders_products_id = ' . $_row['orders_products_id'] . '
                                    ORDER BY op.orders_products_id'
                                )
                                ->queryAll();
                            if (count($ops) > 0) {
                                $bundle_flag = true;
                            }
                            $publisher_name_cost = $this->getPublisherNameCost($_row['orders_products_id']);
                            $po_supplier = $publisher_name_cost['publisher'];
                            $fvv = $fvv_home = $fvv_usd = 0;
                            $fvv_currency = '';
                            $cost_arr = $publisher_name_cost['cost'];
                            $bundle = 'No';
                            $fvv_arr = $this->getProductsFvv($_row['products_id']);
                            $selling_price_usd = $_row['final_price'];
                            $selling_price_home = $this->convertUSDToHome(
                                $selling_price_usd,
                                $row['date_purchased'],
                                $home_currency
                            );

                            $cost_home = 0;
                            $cost_usd = 0;
                            $cost = 0;

                            if ($bundle_flag) {
                                $bundle = 'Yes';
                                $mrv_type = 'NA';
                            } else {
                                if (empty($fvv_arr)) {
                                    $mrv_type = ($_row['custom_products_type_id'] == '3') ? 'SC' : 'Non-MRV';
                                } else {
                                    $mrv_type = 'MRV';
                                    $fvv_currency = $fvv_arr['fvv_currency'];
                                    $fvv_conv = $this->convertCurrency(
                                        $fvv_arr['fvv'],
                                        $row['date_purchased'],
                                        $fvv_arr['fvv_currency'],
                                        $home_currency
                                    );
                                    $fvv = $fvv_arr['fvv'];
                                    $fvv_home = $fvv_conv['home'];
                                    $fvv_usd = $fvv_conv['usd'];
                                }

                                if ($orders_status == 'Completed' && ($_row['custom_products_type_id'] != '3') && $delivered_qty > 0) {
                                    if (empty($cost_arr['currency_code'])) {
                                        Yii::$app->slack->send('Failed to get cost currency', array(
                                            array(
                                                'color' => 'warning',
                                                'text' => Json::encode([
                                                    'orders_id' => $row["orders_id"],
                                                    'orders_products_id' => $_row['orders_products_id'],
                                                    'products_id' => $_row['products_id']
                                                ]),
                                            ),
                                        ));
                                        return false;
                                    }
                                    $cost = $cost_arr['currency_settle_amount'];
                                    $cost_conv = $this->convertCurrency(
                                        $cost,
                                        $row['date_purchased'],
                                        $cost_arr['currency_code'],
                                        $home_currency
                                    );
                                    $cost_home = $cost_conv['home'];
                                    $cost_usd = $cost_conv['usd'];
                                }
                            }

                            $gst0 = $gst1 = $gst2 = $gst3 = 0;
                            $gst0_home = $gst0_usd = $gst1_home = $gst1_usd = $gst2_home = $gst2_usd = $gst3_home = $gst3_usd = 0;
                            if ($orders_status == 'Completed' && ($_row['custom_products_type_id'] != '3')) {
                                if ($delivered_qty > 0) {
                                    $gst = $this->getGST($_row['orders_products_id']);
                                    foreach ($gst as $key => $value) {
                                        if ($key == 'GST0') {
                                            $gst0 = empty($value) ? 0 : $value;
                                        } elseif ($key == 'GST1') {
                                            $gst1 = empty($value) ? 0 : $value;
                                        }
                                    }
                                    $gst0_conv = $this->convertCurrency(
                                        $gst0,
                                        $row['date_purchased'],
                                        $row['currency'],
                                        $home_currency
                                    );
                                    $gst0_home = $gst0_conv['home'];
                                    $gst0_usd = $gst0_conv['usd'];
                                    $gst1_conv = $this->convertCurrency(
                                        $gst1,
                                        $row['date_purchased'],
                                        $row['currency'],
                                        $home_currency
                                    );
                                    $gst1_home = $gst1_conv['home'];
                                    $gst1_usd = $gst1_conv['usd'];
                                    $gst2_home = ($mrv_type == 'MRV') ? (max(
                                            ($selling_price_home - $cost_home),
                                            0
                                        ) * ($tax_percentage / (100 + $tax_percentage)) * $delivered_qty) : 0;
                                    $gst2_usd = ($mrv_type == 'MRV') ? (max(
                                            ($selling_price_usd - $cost_usd),
                                            0
                                        ) * ($tax_percentage / (100 + $tax_percentage)) * $delivered_qty) : 0;
                                }
                                //gst3:
                                if ($delivered_qty > 0 || $bundle_flag) {
                                    $gst3 = Yii::$app->db_slave_offgamers
                                        ->createCommand(
                                            ' select currency_code, processing_fee_amount, tax_amount from pipwave_payment where orders_id = ' . $row['orders_id']
                                        )
                                        ->queryOne();
                                    if (!empty($gst3)) {
                                        $gst3_conv = $this->convertCurrency(
                                            $gst3['tax_amount'],
                                            $row['date_purchased'],
                                            $gst3['currency_code'],
                                            $home_currency
                                        );
                                        $gst3_home = $gst3_conv['home'];
                                        $gst3_usd = $gst3_conv['usd'];
                                    } else {
                                        $gst3_home = 0;
                                        $gst3_usd = 0;
                                    }
                                }
                            }

                            //convert usd to home currency - using type = buy

                            $surcharge = $this->convertUSDToHome($surcharge, $row['date_purchased'], $home_currency);
                            $coupon = $this->convertUSDToHome($coupon, $row['date_purchased'], $home_currency);
                            $sc = $this->convertUSDToHome($sc, $row['date_purchased'], $home_currency);
                            $tax = $this->convertUSDToHome($tax, $row['date_purchased'], $home_currency);
                            $total = $this->convertUSDToHome($total, $row['date_purchased'], $home_currency);

                            $str = [
                                "orders_id" => $row["orders_id"],
                                "order_date" => $row["date_purchased"],
                                "orders_status" => $orders_status,
                                'product_id' => $_row['products_id'],
                                "product_name" => $_row["products_name"],
                                "bundle" => $bundle,
                                "payment_method" => ($row["payment_methods_parent_id"] == 0 ? "Store Credit" : $row["payment_method"]),
                                "currency" => $row["currency"],
                                "ip" => $row["remote_addr"],
                                "ip_country" => $ip_country,
                                'selling_price_home' => $selling_price_home,
                                'selling_price_usd' => $selling_price_usd,
                                'surcharge_amt' => $surcharge,
                                'dc_amt' => $coupon,
                                'sc_amt' => $sc,
                                'tax_amt' => $tax,
                                'total_price' => $total,
//                            'sub_total_amt' => $subtotal,
                                'tax_amt_percentage' => $tax_percentage,
                                'mobile_country' => (isset($row["customers_telephone_country"]) ? $row["customers_telephone_country"] : ""),
                                'address_country' => (isset($row["customers_country"]) ? $row["customers_country"] : ""),
                                'email' => (isset($row["customers_email_address"]) ? $row["customers_email_address"] : ""),
                                //
                                'merchant_name' => implode(",", $po_supplier),
                                'mrv' => $mrv_type, // product determinant : MRV / Non-MRV / Store Credit
                                'order_qty' => $_row["products_quantity"],
                                'exchange_rate_spot' => (isset($m_ch['spot_value']) ? $m_ch['spot_value'] : ""),
                                'exchange_rate_buy' => (isset($row["currency_value"]) ? $row["currency_value"] : $m_ch['buy_value']),
                                'delivered_qty' => $delivered_qty,
                                'fvv' => $fvv,
                                'fvv_currency' => $fvv_currency,
                                'fvv_home' => $fvv_home,
                                'fvv_usd' => $fvv_usd,
                                'cost' => $cost,
                                'cost_home' => $cost_home,
                                'cost_usd' => $cost_usd,
                                'margin_home' => ($fvv_home != 'N/A' ? max($selling_price_home - $fvv_home, 0) : 'N/A'),
                                'margin_usd' => ($fvv_usd != 'N/A' ? max($selling_price_usd - $fvv_usd, 0) : 'N/A'),
                                'profit_home' => $selling_price_home - $cost_home,
                                'profit_usd' => $selling_price_usd - $cost_usd,
                                'gst0_home' => $gst0_home,
                                'gst0_usd' => $gst0_usd,
                                'gst1_home' => $gst1_home,
                                'gst1_usd' => $gst1_usd,
                                'gst2_home' => $gst2_home,
                                'gst2_usd' => $gst2_usd,
                                'gst3_home' => $gst3_home,
                                'gst3_usd' => $gst3_usd,
                                'inv_gst_usd' => $gst0_usd + $gst1_usd + $gst3_usd,
                                'inv_gst_currency' => $gst0_home + $gst1_home + $gst3_home,
                                'total_gst_home' => $gst0_home + $gst1_home + $gst2_home + $gst3_home,
                                'total_gst_usd' => $gst0_usd + $gst1_usd + $gst2_usd + $gst3_usd,
                            ];
                            fputcsv($fp, $str);

                            if ($bundle_flag) {
                                $pids = array_column($ops, 'products_id', $delivered_qty);
                                foreach ($ops as $op) {
                                    $delivered_qty = $op['products_good_delivered_quantity'];
                                    $publisher_name_cost = $this->getPublisherNameCost($op['orders_products_id']);
                                    $po_supplier = $publisher_name_cost['publisher'];
                                    $fvv = $fvv_home = $fvv_usd = 0;
                                    $fvv_currency = '';
                                    $cost_arr = $publisher_name_cost['cost'];
                                    $bundle = '';
                                    $fvv_arr = $this->getProductsFvv($op['products_id']);
                                    if (empty($fvv_arr)) {
                                        $mrv_type = ($_row['custom_products_type_id'] == '3') ? 'SC' : 'Non-MRV';
                                    } else {
                                        $mrv_type = 'MRV';
                                        $fvv_currency = $fvv_arr['fvv_currency'];
                                        $fvv_conv = $this->convertCurrency(
                                            $fvv_arr['fvv'],
                                            $row['date_purchased'],
                                            $fvv_arr['fvv_currency'],
                                            $home_currency
                                        );
                                        $fvv = $fvv_arr['fvv'];
                                        $fvv_home = $fvv_conv['home'];
                                        $fvv_usd = $fvv_conv['usd'];
                                    }

                                    $sp_arr = $this->getProductSellingPrice($op['products_id']);
                                    $selling_price_home = $selling_price_usd = 0;
                                    $selling_price = $sp_arr['selling_price'];
                                    $sp_conv = $this->convertCurrency(
                                        $selling_price,
                                        $row['date_purchased'],
                                        $sp_arr['currency'],
                                        $home_currency
                                    );
                                    $selling_price_home = $sp_conv['home'];
                                    $selling_price_usd = $sp_conv['usd'];

                                    $cost_home = 0;
                                    $cost_usd = 0;
                                    $cost = 0;
                                    $gst0 = $gst1 = $gst2 = $gst3 = 0;
                                    $gst0_home = $gst0_usd = $gst1_home = $gst1_usd = $gst2_home = $gst2_usd = $gst3_home = $gst3_usd = 0;
                                    if ($orders_status == 'Completed' && ($op['custom_products_type_id'] != '3') && $delivered_qty > 0) {
                                        if (empty($cost_arr['currency_code'])) {
                                            Yii::$app->slack->send('Failed to get cost currency', array(
                                                array(
                                                    'color' => 'warning',
                                                    'text' => Json::encode([
                                                        'orders_id' => $row["orders_id"],
                                                        'orders_products_id' => $op['orders_products_id'],
                                                        'products_id' => $op['products_id']
                                                    ]),
                                                ),
                                            ));
                                            return false;
                                        }

                                        $cost = $cost_arr['currency_settle_amount'];
                                        $cost_conv = $this->convertCurrency(
                                            $cost,
                                            $row['date_purchased'],
                                            $cost_arr['currency_code'],
                                            $home_currency
                                        );
                                        $cost_home = $cost_conv['home'];
                                        $cost_usd = $cost_conv['usd'];

                                        $gst = $this->getGST($op['orders_products_id']);
                                        $gst0 = $gst1 = $gst2 = $gst3 = 0;
                                        $gst0_home = $gst0_usd = $gst1_home = $gst1_usd = 0;
                                        foreach ($gst as $key => $value) {
                                            if ($key == 'GST0') {
                                                $gst0 = empty($value) ? 0 : $value;
                                            } elseif ($key == 'GST1') {
                                                $gst1 = empty($value) ? 0 : $value;
                                            }
                                        }
                                        $gst0_conv = $this->convertCurrency(
                                            $gst0,
                                            $row['date_purchased'],
                                            $row['currency'],
                                            $home_currency
                                        );
                                        $gst0_home = $gst0_conv['home'];
                                        $gst0_usd = $gst0_conv['usd'];
                                        $gst1_conv = $this->convertCurrency(
                                            $gst1,
                                            $row['date_purchased'],
                                            $row['currency'],
                                            $home_currency
                                        );
                                        $gst1_home = $gst1_conv['home'];
                                        $gst1_usd = $gst1_conv['usd'];
                                        $gst2_home = ($mrv_type == 'MRV') ? (max(
                                                ($selling_price_home - $cost_home),
                                                0
                                            ) * ($tax_percentage / (100 + $tax_percentage)) * $delivered_qty) : 0;
                                        $gst2_usd = ($mrv_type == 'MRV') ? (max(
                                                ($selling_price_usd - $cost_usd),
                                                0
                                            ) * ($tax_percentage / (100 + $tax_percentage)) * $delivered_qty) : 0;
                                        $gst3_usd = 0;
                                        $gst3_home = 0;
                                    }

                                    $str = [
                                        "orders_id" => $row["orders_id"],
                                        "order_date" => $row["date_purchased"],
                                        "orders_status" => $orders_status,
                                        'product_id' => $op['products_id'],
                                        "product_name" => $op["products_name"],
                                        "bundle" => $bundle,
                                        "payment_method" => ($row["payment_methods_parent_id"] == 0 ? "Store Credit" : $row["payment_method"]),
                                        "currency" => $row["currency"],
                                        "ip" => $row["remote_addr"],
                                        "ip_country" => $ip_country,
                                        'selling_price_home' => $selling_price_home,
                                        'selling_price_usd' => $selling_price_usd,
                                        'surcharge_amt' => $surcharge,
                                        'dc_amt' => $coupon,
                                        'sc_amt' => $sc,
                                        'tax_amt' => $tax,
                                        'total_price' => $total,
//                            'sub_total_amt' => $subtotal,
                                        'tax_amt_percentage' => $tax_percentage,
                                        'mobile_country' => (isset($row["customers_telephone_country"]) ? $row["customers_telephone_country"] : ""),
                                        'address_country' => (isset($row["customers_country"]) ? $row["customers_country"] : ""),
                                        'email' => (isset($row["customers_email_address"]) ? $row["customers_email_address"] : ""),
                                        //
                                        'merchant_name' => implode(",", $po_supplier),
                                        'mrv' => $mrv_type, // product determinant : MRV / Non-MRV / Store Credit
                                        'order_qty' => $_row["products_quantity"],
                                        'exchange_rate_spot' => (isset($m_ch['spot_value']) ? $m_ch['spot_value'] : ""),
                                        'exchange_rate_buy' => (isset($row["currency_value"]) ? $row["currency_value"] : $m_ch['buy_value']),
                                        'delivered_qty' => $delivered_qty,
                                        'fvv' => $fvv,
                                        'fvv_currency' => $fvv_currency,
                                        'fvv_home' => $fvv_home,
                                        'fvv_usd' => $fvv_usd,
                                        'cost' => $cost,
                                        'cost_home' => $cost_home,
                                        'cost_usd' => $cost_usd,
                                        'margin_home' => ($fvv_home != 'N/A' ? max(
                                            $selling_price_home - $fvv_home,
                                            0
                                        ) : 'N/A'),
                                        'margin_usd' => ($fvv_usd != 'N/A' ? max(
                                            $selling_price_usd - $fvv_usd,
                                            0
                                        ) : 'N/A'),
                                        'profit_home' => $selling_price_home - $cost_home,
                                        'profit_usd' => $selling_price_usd - $cost_usd,
                                        'gst0_home' => $gst0_home,
                                        'gst0_usd' => $gst0_usd,
                                        'gst1_home' => $gst1_home,
                                        'gst1_usd' => $gst1_usd,
                                        'gst2_home' => $gst2_home,
                                        'gst2_usd' => $gst2_usd,
                                        'gst3_home' => $gst3_home,
                                        'gst3_usd' => $gst3_usd,
                                        'inv_gst_usd' => $gst0_usd + $gst1_usd + $gst3_usd,
                                        'inv_gst_currency' => $gst0_home + $gst1_home + $gst3_home,
                                        'total_gst_home' => $gst0_home + $gst1_home + $gst2_home + $gst3_home,
                                        'total_gst_usd' => $gst0_usd + $gst1_usd + $gst2_usd + $gst3_usd,
                                    ];
                                    fputcsv($fp, $str);
                                }
                            }
                        }
                    }
                }

                rewind($fp);

                $this->s3 = Yii::$app->aws->getS3('BUCKET_REPOSITORY');
                $this->s3->saveContent($filename, $fp);

                $subject = $this->params['country_code'] . " Tax Sales Report " . date(
                        "Y-m-d",
                        strtotime($this->params['start_date'])
                    ) . " - " . date("Y-m-d", strtotime($this->params['end_date']));
                $content = "The following document valid to download 5 days from the date of issue : \n" .
                    $this->s3->getContentUrl($filename, true, 432000); // 5 days public access lifetime
                $recipient = explode(",", $this->params['recipient']);
                foreach ($recipient as $num => $val) {
                    Yii::$app->mailer->compose()
                        ->setFrom(Yii::$app->params["noreply"]["default"])
                        ->setReplyTo(Yii::$app->params["noreply"]["default"])
                        ->setTo(trim($val))
                        ->setSubject($subject)
                        ->setTextBody($content)
                        ->send();
                }
            }
        }
        return true;
    }

    /**
     * @throws \Exception
     */
    public function generateCouponUsageReport(): bool
    {
        $couponUsageReport = new CouponUsageReport($this->params);

        $couponUsageReport->generateReportAndSendEmail();

        return $couponUsageReport->isReportGenerated();
    }

    private function getPublisherNameCost($orders_products_id)
    {
        $po_ref = [];
        $cost = ['currency_code' => '', 'currency_settle_amount' => 0];
        $po_supplier = [];
        $_row3 = Yii::$app->db_slave_offgamers
            ->createCommand(
                "SELECT extra_info,products_id
                                        FROM log_delivered_products
                                        WHERE orders_products_id = " . $orders_products_id
            )
            ->queryOne();
        if (isset($_row3["extra_info"])) {
            $extra = json_decode($_row3["extra_info"], true);
            if (isset($extra["SOFTPIN"])) {
                $qty = 0;
                $total_cost = 0;
                foreach ($extra["SOFTPIN"]["po_id"] as $po_id => $val) {
                    $qty += count($val['cdkey_id']);
                    if (!empty($po_id)) {
                        $_res4 = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                'SELECT po.purchase_orders_ref_id, po.supplier_name, po.currency
                                                        FROM purchase_orders AS po
                                                        WHERE po.purchase_orders_id = ' . $po_id
                            )
                            ->queryAll();
                        foreach ($_res4 as $_row4) {
                            $po_ref[] = $_row4["purchase_orders_ref_id"];
                            if (!in_array($_row4["supplier_name"], $po_supplier)) {
                                $po_supplier[] = $_row4["supplier_name"];
                            }
                        }
                        $_row5 = Yii::$app->db_slave_offgamers
                            ->createCommand(
                                'SELECT pop.products_usd_unit_price 
                                                FROM purchase_orders_products AS pop
                                                WHERE pop.purchase_orders_id = ' . $po_id . ' and pop.products_id = ' . $_row3['products_id']
                            )
                            ->queryOne();

                        $total_cost += $_row5['products_usd_unit_price'] * count($val['cdkey_id']);
                    } else {
                        $cost = $this->getPINCurrencyCost($_row3['products_id']);
                        $currencies = new Currency();
                        $cost_usd = $currencies->advanceCurrencyConversion(
                            $cost['currency_settle_amount'],
                            $cost['currency_code'],
                            'USD'
                        );
                        $total_cost += $cost_usd * count($val['cdkey_id']);
                    }
                    $cost['currency_code'] = 'USD';
                    $cost['currency_settle_amount'] = $total_cost / $qty;
                }
            } elseif (isset($extra["API"])) {
                foreach ($extra["API"]["custom_products_code_id"] as $i => $code) {
                    $_res4 = Yii::$app->db_slave_offgamers
                        ->createCommand(
                            'SELECT lar.currency_code, lar.currency_settle_amount, pr.publishers_name
                                                    FROM log_api_restock AS lar
                                                    LEFT JOIN publishers_replenish AS pr
                                                        ON pr.publishers_replenish_id = lar.publishers_id
                                                    WHERE lar.custom_products_code_id = ' . $code
                        )
                        ->queryAll();
                    foreach ($_res4 as $_row4) {
                        if (!in_array($_row4["publishers_name"], $po_supplier)) {
                            $po_supplier[] = $_row4["publishers_name"];
                            $cost['currency_code'] = $_row4["currency_code"];
                            $cost['currency_settle_amount'] = $_row4["currency_settle_amount"];
                        }
                    }
                }
            } elseif (isset($extra["DTU"])) {
                foreach ($extra["DTU"]["top_up_id"] as $i => $code) {
                    $unit_cost = 0;
                    $_res4 = Yii::$app->db_slave_offgamers
                        ->createCommand(
                            'SELECT otu.publishers_ref_id, otu.currency_code, otu.currency_settle_amount,
                                                        p.publishers_name
                                                    FROM orders_top_up AS otu
                                                    LEFT JOIN publishers AS p
                                                        ON p.publishers_id = otu.publishers_id
                                                    WHERE otu.top_up_id = ' . $code
                        )
                        ->queryAll();
                    foreach ($_res4 as $_row4) {
                        $po_ref[] = "DTU-" . $_row4["publishers_ref_id"];
                        if (!in_array($_row4["publishers_name"], $po_supplier)) {
                            $po_supplier[] = $_row4["publishers_name"];
                            $cost['currency_code'] = $_row4["currency_code"];
                            $cost['currency_settle_amount'] = $_row4["currency_settle_amount"];
                        }
                    }
                }
            } elseif (isset($extra['PHYSICAL_GOODS'])) {
                $po_supplier[] = '';
                $_res4 = ProductsCost::find()->where(['products_id' => $_row3['products_id']])->asArray()->one();
                if ($_res4) {
                    $cost['currency_code'] = $_res4["products_currency"];
                    $cost['currency_settle_amount'] = $_res4["products_cost"];
                }
            }
        }
        return ['publisher' => $po_supplier, 'cost' => $cost];
    }

    private function getProductsFvv($products_id)
    {
        $result = [];
        $products_tax_category = ProductsTaxCategory::getProductsValue($products_id, 'MRV');
        if (!empty($products_tax_category)) {
            $result['tax_category'] = $products_tax_category['category'];
            $result['fvv'] = $products_tax_category['products_value'];
            $result['fvv_currency'] = $products_tax_category['currency'];
        }
        return $result;
    }

    private function getProductSellingPrice($products_id)
    {
        $sql = "SELECT products_price, products_base_currency FROM  products  WHERE products_id = '" . $products_id . "' ";
        $sql_result = Yii::$app->db_slave_offgamers
            ->createCommand($sql)->queryOne();
        return [
            'currency' => isset($sql_result['products_base_currency']) ? $sql_result['products_base_currency'] : '',
            'selling_price' => isset($sql_result['products_price']) ? $sql_result['products_price'] : '',
        ];
    }

    private function getPINCurrencyCost($products_id)
    {
        $sql = "SELECT products_cost, products_currency FROM  products_cost  WHERE products_id = '" . $products_id . "' ";
        $sql_result = Yii::$app->db_slave_offgamers
            ->createCommand($sql)->queryOne();

        return [
            'currency_code' => isset($sql_result['products_currency']) ? $sql_result['products_currency'] : '',
            'currency_settle_amount' => isset($sql_result['products_cost']) ? $sql_result['products_cost'] : '',
        ];
    }

    private function getGST($orders_product_id)
    {
        $result = [];
        $record = Yii::$app->db_slave_offgamers
            ->createCommand(
                'SELECT opei.orders_products_extra_info_key as col_key, opei.orders_products_extra_info_value AS col_value
                                FROM orders_products_extra_info AS opei
                                WHERE opei.orders_products_id = ' . $orders_product_id . '
                                    AND orders_products_extra_info_key in ("tax_type","tax_amount")'
            )
            ->queryAll();
        if (!empty($record)) {
            foreach ($record as $row) {
                if ($row['col_key'] == 'tax_type') {
                    $tax_type = $row['col_value'];
                } elseif ($row['col_key'] == 'tax_amount') {
                    $tax_amount = $row['col_value'];
                }
            }
            if (isset($tax_type)) {
                $result[$tax_type] = $tax_amount ?? 0;
            }
        }
        return $result;
    }

    private function convertCurrency($value, $date, $fr_currency, $home_currency)
    {
        $home = 0;
        $usd = 0;
        if ($fr_currency == $home_currency) {
            $home = $value;
            $usd = $this->convertToUSD($value, $date, $fr_currency);
        } elseif ($fr_currency == 'USD') {
            $usd = $value;
            $home = $this->convertUSDToHome($usd, $date, $home_currency);
        } else {
            $usd = $this->convertToUSD($value, $date, $fr_currency);
            $home = $this->convertUSDToHome($usd, $date, $home_currency);
        }
        return ['home' => $home, 'usd' => $usd];
    }

    private function convertUSDToHome($value, $date, $home_currency)
    {
        try {
            $currencies = new Currency();
            $conv = $currencies->advanceCurrencyConversionHist($value, $date, 'USD', $home_currency, false, 'buy');
            return $conv['new_number'];
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    private function convertToUSD($value, $date, $fr_currency)
    {
        try {
            $currencies = new Currency();
            $conv = $currencies->advanceCurrencyConversionHist($value, $date, $fr_currency, 'USD', false, 'spot');
            return $conv['new_number'];
        } catch (\Exception $e) {
            return 'N/A';
        }
    }
}
