<?php

use offgamers\base\controllers\Migration;
use yii\base\Security;
use yii\rbac\DbManager;
use mdm\admin\models\Route;

/**
 * Class m180614_014922_rbac_init
 */
class m180614_014922_rbac_init extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Checking for previous required migration from vendor package
        $dependency_sql = "SELECT * FROM migration WHERE version = 'm140506_102106_rbac_init'";
        $dependency = Yii::$app->get('db')->createCommand($dependency_sql)->queryOne();
        if ($dependency) {
            $this->addColumn('{{%user}}', 'first_name', 'varchar(32) DEFAULT NULL AFTER `username`');
            $this->addColumn('{{%user}}', 'last_name', 'varchar(32) DEFAULT NULL AFTER `first_name`');
            $this->addColumn('{{%user}}', 'last_login_at', 'int(11)');

            $password = '123456';
            $security = new Security;
            $adminData = [
                'id' => 1,
                'username' => "<EMAIL>",
                'email' => "<EMAIL>",
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'auth_key' => $security->generateRandomString(),
                'password_hash' => $security->generatePasswordHash($password),
                'password_reset_token' => null,
                'status' => 10,
                'created_at' => time(),
                'updated_at' => time(),
                'last_login_at' => '0'
            ];
            $this->insert('{{%user}}', $adminData);

            // add "admin" role and give this role the "updatePost" permission
            $auth = new DbManager;
            $auth->init();
            $admin = $auth->createRole('Superadmin');
            $auth->add($admin);

            // Assign roles to users.
            $auth->assign($admin, 1);

            // Add available routes
            try {
                $routeData = $this->_getRouteData();

                $this->_updateAuthItemTable($routeData, '2');

                foreach ($routeData as $key => $value) {
                    $this->_updateAuthItemChildTable('Superadmin', $value);
                }
            } catch (Exception $e) {
                echo "Exception: " . $e->getMessage() . "\n";
                return false;
            }

            $this->_updateMenuTable();

            echo "\n[ INSERT ] Superadmin created successfully. Kindly change the password in crew.\n\n";
        } else {
            echo "\n[ RBAC ] not exist. Run rbac migration script first\n\n";
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->execute('SET foreign_key_checks = 0');
        $this->truncateTable('{{%user}}');
        $this->truncateTable('{{%auth_item}}');
        $this->truncateTable('{{%auth_assignment}}');
        $this->truncateTable('{{%auth_item_child}}');
        $this->truncateTable('{{%menu}}');
        $this->dropColumn('{{%user}}', 'first_name');
        $this->dropColumn('{{%user}}', 'last_name');
        $this->dropColumn('{{%user}}', 'last_login_at');

        echo "m180614_014922_rbac_init reverted.\n";
    }

    private function _getRouteData()
    {
        return [
            '/*',
            '/site/index',
            '/admin/assignment/index',
            '/admin/role/index',
            '/admin/permission/index',
            '/admin/rule/index',
            '/admin/route/index',
            '/admin/user/index',
            '/admin/menu/index',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _updateMenuTable()
    {
        $dataInsert = [
            'id' => 1,
            'name' => 'Dashboard',
            'parent' => null,
            'route' => '/site/index',
            'order' => 0,
            'data' => 'fa fa-tachometer',
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 2,
            'name' => 'Administrator',
            'parent' => null,
            'route' => null,
            'order' => 1,
            'data' => 'fa fa-cogs',
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 3,
            'name' => 'Assignment',
            'parent' => 2,
            'route' => '/admin/assignment/index',
            'order' => 0,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 4,
            'name' => 'Role',
            'parent' => 2,
            'route' => '/admin/role/index',
            'order' => 1,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 5,
            'name' => 'Permission',
            'parent' => 2,
            'route' => '/admin/permission/index',
            'order' => 2,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 6,
            'name' => 'Rule',
            'parent' => 2,
            'route' => '/admin/rule/index',
            'order' => 3,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 7,
            'name' => 'Route',
            'parent' => 2,
            'route' => '/admin/route/index',
            'order' => 4,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 8,
            'name' => 'Menu',
            'parent' => 2,
            'route' => '/admin/menu/index',
            'order' => 5,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 9,
            'name' => 'User',
            'parent' => 2,
            'route' => '/admin/user/index',
            'order' => 6,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);
    }
}
