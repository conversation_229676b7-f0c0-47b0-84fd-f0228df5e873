<?php

use yii\db\Schema;
use offgamers\base\controllers\Migration;

/**
 * Class m181113_065358_webpush_init
 */
class m181113_065358_webpush_init extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }

        $this->_updateMenuTable();

        $auth = Yii::$app->authManager;

        // add "webpushApprove" permission
        $webpushApprove = $auth->createPermission('webpushApprove');
        $webpushApprove->description = 'Approval for webpush notification';
        $auth->add($webpushApprove);

        $this->_updateAuthItemChildTable('Superadmin', 'webpushApprove');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m181113_065358_webpush_init cannot be reverted.\n";

        return false;
    }

    private function _getRouteData()
    {
        return [
            '/webpush/site/index',
            '/webpush/segment/index',
            '/webpush/token/index',
            '/webpush/notification/index',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _updateMenuTable()
    {
        // Update User menu and sub-menu
        $dataUpdate = [
            'route' => null,
        ];
        $this->update('{{%menu}}', $dataUpdate, ['id' => 9]);

        $dataInsert = [
            'id' => 10,
            'name' => 'User List',
            'parent' => 9,
            'route' => '/admin/user/index',
            'order' => 0,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 11,
            'name' => 'User Signup',
            'parent' => 9,
            'route' => '/admin/user/signup',
            'order' => 1,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 12,
            'name' => 'Change Password',
            'parent' => 9,
            'route' => '/admin/user/change-password',
            'order' => 2,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        // Add Marketing webpush menu
        $dataInsert = [
            'id' => 13,
            'name' => 'Marketing',
            'parent' => null,
            'route' => null,
            'order' => 2,
            'data' => 'fa fa-bullhorn',
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 14,
            'name' => 'Segment',
            'parent' => 13,
            'route' => '/webpush/segment/index',
            'order' => 0,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 15,
            'name' => 'Token',
            'parent' => 13,
            'route' => '/webpush/token/index',
            'order' => 1,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);

        $dataInsert = [
            'id' => 16,
            'name' => 'Notification',
            'parent' => 13,
            'route' => '/webpush/notification/index',
            'order' => 2,
            'data' => null,
        ];
        $this->insert('{{%menu}}', $dataInsert);
    }
}
