<?php

use offgamers\base\controllers\Migration;

/**
 * Class m181226_092408_menu_font_awesome_icon
 */
class m181226_092408_menu_font_awesome_icon extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $dataUpdate = [
            'data' => 'fa fa-tachometer-alt',
        ];
        $this->update('{{%menu}}', $dataUpdate, ['id' => 1]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $dataUpdate = [
            'data' => 'fa fa-tachometer',
        ];
        $this->update('{{%menu}}', $dataUpdate, ['id' => 1]);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m181226_092408_menu_font_awesome_icon cannot be reverted.\n";

        return false;
    }
    */
}
