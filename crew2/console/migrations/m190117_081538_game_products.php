<?php

use yii\db\Schema;
use offgamers\base\controllers\Migration;

/**
 * Class m190117_081538_game_products
 */
class m190117_081538_game_products extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $publisher_menu = [
            'name' => 'Game Product',
            'parent' => null,
            'route' => null,
            'order' => 10,
            'data' => 'fa fa-bullhorn',
        ];

        $this->insert('{{%menu}}', $publisher_menu);
        $id = Yii::$app->db->getLastInsertID();
        $menu_list = [
            [
                'name' => 'Game Product Publisher',
                'parent' => $id,
                'route' => '/game-publisher/index',
                'order' => 10
            ],
            [
                'name' => 'Game Product Attribute',
                'parent' => $id,
                'route' => '/game-product-attribute/index',
                'order' => 20
            ]
        ];


        $this->update('{{%menu}}', ['order' => 20], ['id' => 2]);
        $this->update('{{%menu}}', ['order' => 30], ['id' => 13]);


        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }

        $this->createTable('game_publisher', [
            'game_publisher_id' => Schema::TYPE_PK,
            'title' => Schema::TYPE_STRING,
            'profile' => Schema::TYPE_STRING,
            'status' => Schema::TYPE_TINYINT,
            'last_sync' => Schema::TYPE_DATETIME,
            'created_at' => Schema::TYPE_DATETIME,
            'updated_at' => Schema::TYPE_DATETIME
        ], $tableOptions);

        $this->createTable('game_publisher_setting', [
            'id' => Schema::TYPE_PK,
            'game_publisher_id' => Schema::TYPE_INTEGER,
            'key' => Schema::TYPE_STRING,
            'value' => Schema::TYPE_STRING,
        ], $tableOptions);

        $this->addForeignKey('fk_game_publisher_setting_publisher_id', 'game_publisher_setting', 'game_publisher_id',
            'game_publisher', 'game_publisher_id', 'CASCADE');


        $this->createTable('game_publisher_product', [
            'game_publisher_product_id' => Schema::TYPE_PK,
            'title' => Schema::TYPE_STRING,
            'game_publisher_id' => Schema::TYPE_INTEGER,
            'publisher_reference_id' => Schema::TYPE_STRING,
            'game_product_id' => Schema::TYPE_INTEGER,
            'status' => Schema::TYPE_TINYINT,
            'out_of_stock_flag' => Schema::TYPE_BOOLEAN . ' DEFAULT FALSE',
            'pre_order_flag' => Schema::TYPE_BOOLEAN . ' DEFAULT FALSE',
            'stock_quantity' => Schema::TYPE_INTEGER,
            'cost_price' => Schema::TYPE_DOUBLE,
            'cost_currency' => Schema::TYPE_STRING,
            'auto_markup' => Schema::TYPE_BOOLEAN,
            'mark_up' => Schema::TYPE_INTEGER,
            'created_at' => Schema::TYPE_DATETIME,
            'updated_at' => Schema::TYPE_DATETIME,
            'json_raw' => Schema::TYPE_TEXT,
            'is_duplicate_flag' => Schema::TYPE_BOOLEAN . ' DEFAULT FALSE',
            'url_alias' => Schema::TYPE_STRING
        ], $tableOptions);

        $this->addForeignKey('fk_game_publisher_product_publisher_id', 'game_publisher_product', 'game_publisher_id',
            'game_publisher', 'game_publisher_id', 'CASCADE');


        $this->alterColumn('products', 'products_model', Schema::TYPE_STRING);
        $this->alterColumn('log_api_restock', 'sku', Schema::TYPE_STRING);

        $this->addColumn('products', 'products_type', Schema::TYPE_TINYINT);
        $this->createIndex('products_products_type', 'products', 'products_type');

        $this->createTable('game_product', [
            'game_product_id' => Schema::TYPE_PK,
            'products_id' => Schema::TYPE_INTEGER,
            'created_at' => Schema::TYPE_DATETIME,
            'updated_at' => Schema::TYPE_DATETIME,
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_product_id', 'game_product', 'products_id',
            'products', 'products_id', 'CASCADE');

        $this->addForeignKey('fk_game_publisher_game_product_id', 'game_publisher_product', 'game_product_id',
            'game_product', 'game_product_id');

        $this->createTable('game_product_description', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'header_image_url' => Schema::TYPE_STRING,
            'header_image_title' => Schema::TYPE_STRING,
            'background_image_url' => Schema::TYPE_STRING,
            'language_id' => Schema::TYPE_TINYINT,
            'notice' => Schema::TYPE_TEXT,
            'description' => Schema::TYPE_TEXT,
            'created_at' => Schema::TYPE_DATETIME,
            'updated_at' => Schema::TYPE_DATETIME,
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_description_product_id', 'game_product_description', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_genre', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'value' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_genre_product_id', 'game_product_genre', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_feature', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'value' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_feature_product_id', 'game_product_feature', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_language', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'value' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_language_product_id', 'game_product_language', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_platform', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'value' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_platform_product_id', 'game_product_platform', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_region', [
            'id' => Schema::TYPE_PK,
            'game_product_id' => Schema::TYPE_INTEGER,
            'value' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_region_product_id', 'game_product_region', 'game_product_id',
            'game_product', 'game_product_id', 'CASCADE');

        $this->createTable('game_product_attribute', [
            'game_product_attribute_id' => Schema::TYPE_PK,
            'type' => Schema::TYPE_TINYINT,
            'sort_order' => Schema::TYPE_INTEGER
        ], $tableOptions);

        $this->createTable('game_product_attribute_translation', [
            'id' => Schema::TYPE_PK,
            'game_product_attribute_id' => Schema::TYPE_INTEGER,
            'language_id' => Schema::TYPE_TINYINT,
            'value' => Schema::TYPE_STRING
        ], $tableOptions);

        $this->addForeignKey('fk_game_product_attribute_translation_attribute_id', 'game_product_attribute_translation',
            'game_product_attribute_id', 'game_product_attribute', 'game_product_attribute_id', 'CASCADE');

        $default_language = array(
            "Multilanguage",
            "Bulgarian",
            "Czech",
            "Danish",
            "Dutch",
            "English",
            "Finnish",
            "French",
            "German",
            "Greek",
            "Hungarian",
            "Italian",
            "Japanese",
            "Korean",
            "Norwegian",
            "Polish",
            "Portuguese",
            "Portuguese - Brazil",
            "Romanian",
            "Russian",
            "Simplified Chinese",
            "Spanish - Latin America",
            "Spanish - Spain",
            "Swedish",
            "Thai",
            "Traditional Chinese",
            "Turkish",
            "Ukrainian",
            "Vietnamese"
        );

        $default_feature = array(
            "Co-op",
            "Cross-Platform Multiplayer",
            "Local Co-op",
            "Local Multi-Player",
            "Multi-player",
            "Online Co-op",
            "Online Multi-Player",
            "Shared/Split Screen",
            "Single-player"
        );

        $default_platform = array(
            "Bathesda",
            "Battle.Net",
            "GOG",
            "Origin",
            "PSN",
            "Rockstar",
            "Steam",
            "Switch",
            "Ubi",
            "Uplay",
            "Windows Store",
            "Xbox Live",
            "iTunes"
        );
        $default_genre = array(
            "Action",
            "Adventure",
            "Casual",
            "Indie",
            "Massively Multiplayer",
            "RPG",
            "Racing",
            "Simulation",
            "Sports",
            "Strategy"
        );

        $default_region = array("Global (Worldwide)", "Asia", "EMEA", "EU", "PL", "RU", "US");

        foreach ($default_language as $i => $language) {
            $this->insert('game_product_attribute', [
                'type' => 1,
                'sort_order' => ($i + 1) * 100
            ]);

            $this->insert('game_product_attribute_translation', [
                'game_product_attribute_id' => Yii::$app->db->getLastInsertID(),
                'language_id' => 1,
                'value' => $language,
            ]);
        }

        foreach ($default_genre as $i => $genre) {
            $this->insert('game_product_attribute', [
                'type' => 2,
                'sort_order' => ($i + 1) * 100
            ]);

            $this->insert('game_product_attribute_translation', [
                'game_product_attribute_id' => Yii::$app->db->getLastInsertID(),
                'language_id' => 1,
                'value' => $genre
            ]);
        }

        foreach ($default_feature as $i => $feature) {
            $this->insert('game_product_attribute', [
                'type' => 3,
                'sort_order' => ($i + 1) * 100
            ]);

            $this->insert('game_product_attribute_translation', [
                'game_product_attribute_id' => Yii::$app->db->getLastInsertID(),
                'language_id' => 1,
                'value' => $feature
            ]);
        }


        foreach ($default_platform as $i => $platform) {
            $this->insert('game_product_attribute', [
                'type' => 4,
                'sort_order' => ($i + 1) * 100
            ]);

            $this->insert('game_product_attribute_translation', [
                'game_product_attribute_id' => Yii::$app->db->getLastInsertID(),
                'language_id' => 1,
                'value' => $platform
            ]);
        }

        foreach ($default_region as $i => $region) {
            $this->insert('game_product_attribute', [
                'type' => 5,
                'sort_order' => ($i + 1) * 100
            ]);

            $this->insert('game_product_attribute_translation', [
                'game_product_attribute_id' => Yii::$app->db->getLastInsertID(),
                'language_id' => 1,
                'value' => $region
            ]);
        }

        $auth = Yii::$app->authManager;

        $permission_list = [
            'batchUpdate' => 'Permission to batch update game product detail',
            'updatePrice' => 'Permission to update products price',
            'updateStatus' => 'Permission to update products status'
        ];

        try {
            foreach ($permission_list as $name => $description) {
                $newPermission = $auth->createPermission($name);
                $newPermission->description = $description;
                $auth->add($newPermission);
            }
        } catch (\Exception $exception) {
            // Do Nothing, Duplicate Permission
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('game_product_attribute_translation');
        $this->dropTable('game_product_attribute');
        $this->dropTable('game_product_language');
        $this->dropTable('game_product_genre');
        $this->dropTable('game_product_feature');
        $this->dropTable('game_product_platform');
        $this->dropTable('game_product_region');
        $this->dropTable('game_product_description');
        $this->dropTable('game_publisher_product');
        $this->dropTable('game_product');
        $this->dropTable('game_publisher_setting');
        $this->dropTable('game_publisher');
        $this->dropColumn('products', 'products_type');
        $this->delete('menu', ['name' => 'Game Product']);
        $this->delete('menu', ['route' => '/game-publisher/index']);
        $this->delete('menu', ['route' => '/game-product/index']);
        $this->delete('menu', ['route' => '/game-product-attribute/index']);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190117_081538_game_products cannot be reverted.\n";

        return false;
    }
    */
}
