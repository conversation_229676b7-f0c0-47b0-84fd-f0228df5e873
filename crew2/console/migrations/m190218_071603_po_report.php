<?php

use offgamers\base\controllers\Migration;

/**
 * Class m190218_071603_po_report
 */
class m190218_071603_po_report extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $publisher_menu = [
            'name' => 'Report',
            'parent' => null,
            'route' => null,
            'order' => 40,
            'data' => 'fa fa-chart-bar',
        ];

        $this->insert('{{%menu}}', $publisher_menu);
        $id = Yii::$app->db->getLastInsertID();
        $menu_list = [
            [
                'name' => 'Purchase Orders',
                'parent' => $id,
                'route' => '/report/purchase-orders',
                'order' => 10
            ],
        ];

        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('menu', ['name' => 'Report']);
        $this->delete('menu', ['route' => '/report/purchase-orders']);
        echo "m190218_071603_po_report reverted.\n";
    }
}
