<?php

use offgamers\base\controllers\Migration;
use common\models\GamePublisherProductForm;

/**
 * Class m190305_083959_game_product_path_fix
 */
class m190305_083959_game_product_path_fix extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->delete('{{%menu}}', ['name' => ['Rule', 'Menu', 'Change Password', 'User Signup', 'User List', 'User', 'Assignment']]);

        $this->update('{{%menu}}', ['order' => 100], ['name' => 'Administrator']);

        $dataUpdate = [
            'data' => 'fa fa-gamepad',
        ];

        $this->update('{{%menu}}', $dataUpdate, ['name' => 'Game Product']);

        $dataUpdate = [
            'name' => 'User',
            'route' => '/admin/user/index',
            'data' => '',
            'parent' => 2,
            'order' => 0
        ];

        $this->insert('{{%menu}}', $dataUpdate, ['name' => 'Assignment']);

        $menu_list = [
            'Game Product Publisher' => 'Publisher',
            'Game Product Attribute' => 'Attribute',
        ];

        foreach ($menu_list as $old => $new) {
            $this->update('{{menu}}', ['name' => $new], ['name' => $old]);
        }

        $permission_list = [
            'webpushApprove' => '[Webpush] Approve',
            'batchUpdate' => '[Game Product] Batch Update',
            'updatePrice' => '[Game Product] Update Price',
            'updateStatus' => '[Game Product] Update Status'
        ];

        foreach ($permission_list as $old => $new) {
            $this->update('{{auth_item}}', ['name' => $new], ['type' => 2, 'name' => $old]);
        }

        (new GamePublisherProductForm())->patchGameProductCategoryPath();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // roll back not applicable
        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190305_083959_game_product_path_fix cannot be reverted.\n";

        return false;
    }
    */
}
