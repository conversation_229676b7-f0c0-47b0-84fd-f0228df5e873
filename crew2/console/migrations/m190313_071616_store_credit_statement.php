<?php

use yii\db\Migration;

/**
 * Class m190313_071616_store_credit_statement
 */
class m190313_071616_store_credit_statement extends Migration
{
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->delete('menu', ['name' => 'Report']);
        $this->delete('menu', ['route' => '/report/purchase-orders']);

        $publisher_menu = [
            'name' => 'Report',
            'parent' => null,
            'route' => null,
            'order' => 40,
            'data' => 'fa fa-chart-bar',
        ];

        $this->insert('{{%menu}}', $publisher_menu);
        $id = Yii::$app->db->getLastInsertID();
        $menu_list = [
            [
                'name' => 'Purchase Orders',
                'parent' => $id,
                'route' => '/report/purchase-orders',
                'order' => 10
            ],
            [
                'name' => 'Store Credit',
                'parent' => $id,
                'route' => '/store-credit/index',
                'order' => 20
            ],
        ];

        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }
    }

    public function safeDown()
    {
        $this->delete('menu', ['route' => '/store-credit/index']);
        echo "m190313_071616_store_credit_statement reverted.\n";
    }
}
