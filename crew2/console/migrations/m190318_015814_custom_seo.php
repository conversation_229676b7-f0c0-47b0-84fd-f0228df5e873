<?php

use yii\db\Schema;
use offgamers\base\controllers\Migration;

/**
 * Class m190318_015814_custom_seo
 */
class m190318_015814_custom_seo extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('custom_seo', [
            'custom_seo_id' => Schema::TYPE_PK,
            'type' => Schema::TYPE_TINYINT,
            'reference_data_id' => Schema::TYPE_INTEGER,
        ], $tableOptions);

        $this->createTable('custom_seo_translation', [
            'custom_seo_translation_id' => Schema::TYPE_PK,
            'custom_seo_id' => Schema::TYPE_INTEGER,
            'language_id' => Schema::TYPE_TINYINT,
            'title' => Schema::TYPE_STRING,
            'meta_title' => Schema::TYPE_STRING,
            'meta_keyword' => Schema::TYPE_TEXT,
            'meta_description' => Schema::TYPE_TEXT,
            'status' => Schema::TYPE_TINYINT,
            'created_at' => Schema::TYPE_DATETIME,
            'updated_at' => Schema::TYPE_DATETIME,
        ], $tableOptions);

        $this->addForeignKey('custom_seo_translation', 'custom_seo_translation', 'custom_seo_id', 'custom_seo', 'custom_seo_id', 'CASCADE');

        (new \common\models\CustomSeoForm())->generateDefaultEntry();

        $dataUpdate = [
            'name' => 'Custom SEO',
            'route' => '/custom-seo/index',
            'data' => '',
            'parent' => 13,
            'order' => 3
        ];

        $this->insert('{{%menu}}', $dataUpdate);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('{{%menu}}', ['name' => 'Custom SEO']);
        $this->dropTable('custom_seo_translation');
        $this->dropTable('custom_seo');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190318_015814_custom_seo cannot be reverted.\n";

        return false;
    }
    */
}
