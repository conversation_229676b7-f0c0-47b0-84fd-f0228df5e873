<?php

use offgamers\base\controllers\Migration;
use yii\db\Schema;
use common\models\GamePublisherProductForm;

/**
 * Class m190319_082638_algolia_dm5
 */
class m190319_082638_algolia_md5 extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('game_product', 'algolia_hash', Schema::TYPE_STRING);

        (new GamePublisherProductForm())->patchReleaseDate();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('game_product', 'algolia_hash');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190319_082638_algolia_dm5 cannot be reverted.\n";

        return false;
    }
    */
}
