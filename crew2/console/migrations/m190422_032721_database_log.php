<?php

use offgamers\base\controllers\Migration;
use yii\db\Schema;

/**
 * Class m190422_032721_database_log
 */
class m190422_032721_database_log extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_log')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('outgoing_request_log', [
            'outgoing_request_log_id' => Schema::TYPE_PK,
            'app' => Schema::TYPE_STRING,
            'log_level' => Schema::TYPE_TINYINT,
            'request_url' => Schema::TYPE_TEXT,
            'status_code' => Schema::TYPE_INTEGER,
            'time_consume' => Schema::TYPE_INTEGER,
            'created_at' => Schema::TYPE_INTEGER . ' unsigned',
            'updated_at' => Schema::TYPE_INTEGER . ' unsigned'
        ], $tableOptions);

        $this->createTable('incoming_request_log', [
            'incoming_request_log_id' => Schema::TYPE_PK,
            'app' => Schema::TYPE_STRING,
            'log_level' => Schema::TYPE_TINYINT,
            'source_ip' => Schema::TYPE_STRING,
            'requester' => Schema::TYPE_STRING,
            'request_url' => Schema::TYPE_TEXT,
            'status_code' => Schema::TYPE_INTEGER,
            'time_consume' => Schema::TYPE_INTEGER,
            'created_at' => Schema::TYPE_INTEGER . ' unsigned',
            'updated_at' => Schema::TYPE_INTEGER . ' unsigned'
        ], $tableOptions);

        $this->createTable('model_audit_history_log', [
            'model_audit_history_log_id' => Schema::TYPE_PK,
            'app' => Schema::TYPE_STRING,
            'table' => Schema::TYPE_STRING,
            'reference_data_id' => Schema::TYPE_INTEGER,
            'action' => Schema::TYPE_STRING,
            'description' => Schema::TYPE_TEXT,
            'raw' => 'LONGTEXT',
            'updated_by' => Schema::TYPE_STRING,
            'created_at' => Schema::TYPE_INTEGER . ' unsigned',
        ], $tableOptions);


        $this->createTable('dev_debug_log', [
            'dev_debug_log_id' => Schema::TYPE_PK,
            'app' => Schema::TYPE_STRING,
            'tag' => Schema::TYPE_STRING,
            'raw' => 'LONGTEXT',
            'created_at' => Schema::TYPE_INTEGER . ' unsigned',
        ], $tableOptions);

        $this->init('db');

        $publisher_menu = [
            'name' => 'Log',
            'parent' => null,
            'route' => null,
            'order' => 90,
            'data' => 'fa fa-history',
        ];


        $this->insert('{{%menu}}', $publisher_menu);
        $id = $this->db->getLastInsertID();
        $menu_list = [
            [
                'name' => 'Incoming Log',
                'parent' => $id,
                'route' => '/request-log/incoming-log',
                'order' => 10
            ],
            [
                'name' => 'Outgoing Log',
                'parent' => $id,
                'route' => '/request-log/outgoing-log',
                'order' => 20
            ],
            [
                'name' => 'Debug Log',
                'parent' => $id,
                'route' => '/request-log/debug-log',
                'order' => 30
            ]
        ];

        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('outgoing_request_log');
        $this->dropTable('incoming_request_log');
        $this->dropTable('model_audit_history_log');
        $this->dropTable('dev_debug_log');

        $this->init('db');
        $menu_list = ['Log', 'Incoming Log', 'Outgoing log', 'Debug Log'];
        $this->delete('{{%menu}}', ['name' => $menu_list]);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190422_032721_database_log cannot be reverted.\n";

        return false;
    }
    */
}
