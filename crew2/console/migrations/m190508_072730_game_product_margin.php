<?php

use yii\db\Schema;
use offgamers\base\controllers\Migration;
use common\models\GamePublisherProductForm;

/**
 * Class m190508_072730_game_product_margin
 */
class m190508_072730_game_product_margin extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('game_publisher_product', 'selling_price', Schema::TYPE_DOUBLE);
        $this->addColumn('game_publisher_product', 'selling_currency', Schema::TYPE_STRING);
        $this->addColumn('game_publisher_product','actual_mark_up',Schema::TYPE_DOUBLE);
        $this->alterColumn('game_publisher_product','mark_up',Schema::TYPE_DOUBLE);
        (new GamePublisherProductForm())->patchSellingPrice();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('game_publisher_product', 'selling_price');
        $this->dropColumn('game_publisher_product', 'selling_currency');
        $this->dropColumn('game_publisher_product', 'actual_mark_up');
        $this->alterColumn('game_publisher_product', 'mark_up',Schema::TYPE_INTEGER);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190508_072730_game_product_margin cannot be reverted.\n";

        return false;
    }
    */
}
