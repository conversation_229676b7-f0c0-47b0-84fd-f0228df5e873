<?php

use offgamers\base\controllers\Migration;
use yii\db\Schema;

/**
 * Class m190710_082742_preorder_restock_api
 */
class m190710_082742_preorder_restock_api extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('api_restock_request', [
            'api_restock_request_id' => Schema::TYPE_PK,
            'orders_products_id' => Schema::TYPE_INTEGER,
            'custom_products_code_id' => Schema::TYPE_INTEGER,
            'publisher_order_id' => Schema::TYPE_STRING,
            'status' => Schema::TYPE_TINYINT,
            'created_at' => Schema::TYPE_INTEGER. ' UNSIGNED',
            'updated_at' => Schema::TYPE_INTEGER. ' UNSIGNED',
        ], $tableOptions);

        $this->addForeignKey('custom_products_code_id', 'api_restock_request', 'custom_products_code_id', 'custom_products_code', 'custom_products_code_id');
        $this->addForeignKey('orders_products_id', 'api_restock_request', 'orders_products_id', 'orders_products', 'orders_products_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('api_restock_request');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m190710_082742_preorder_restock_api cannot be reverted.\n";

        return false;
    }
    */
}
