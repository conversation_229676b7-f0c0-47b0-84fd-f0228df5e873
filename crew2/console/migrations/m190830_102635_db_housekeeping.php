<?php

use offgamers\base\controllers\Migration;

/**
 * Class m190830_102635_db_housekeeping
 */
class m190830_102635_db_housekeeping extends Migration {

    /**
     * {@inheritdoc}
     */
    public function safeUp() {
        $this->dropTable('jos_banner');
        $this->dropTable('jos_bannerclient');
        $this->dropTable('jos_bannertrack');
        $this->dropTable('jos_bfdbversions');
        $this->dropTable('jos_captcha_session');
        $this->dropTable('jos_categories');
        $this->dropTable('jos_community_activities');
        $this->dropTable('jos_community_activities_hide');
        $this->dropTable('jos_community_apps');
        $this->dropTable('jos_community_avatar');
        $this->dropTable('jos_community_config');
        $this->dropTable('jos_community_connect_users');
        $this->dropTable('jos_community_connection');
        $this->dropTable('jos_community_featured');
        $this->dropTable('jos_community_fields');
        $this->dropTable('jos_community_fields_values');
        $this->dropTable('jos_community_files');
        $this->dropTable('jos_community_friendgroup');
        $this->dropTable('jos_community_friendlist');
        $this->dropTable('jos_community_groups');
        $this->dropTable('jos_community_groups_bulletins');
        $this->dropTable('jos_community_groups_category');
        $this->dropTable('jos_community_groups_discuss');
        $this->dropTable('jos_community_groups_members');
        $this->dropTable('jos_community_mailq');
        $this->dropTable('jos_community_msg');
        $this->dropTable('jos_community_msg_recepient');
        $this->dropTable('jos_community_photos');
        $this->dropTable('jos_community_photos_albums');
        $this->dropTable('jos_community_photos_tag');
        $this->dropTable('jos_community_photos_tokens');
        $this->dropTable('jos_community_register');
        $this->dropTable('jos_community_register_auth_token');
        $this->dropTable('jos_community_reports');
        $this->dropTable('jos_community_reports_actions');
        $this->dropTable('jos_community_reports_reporter');
        $this->dropTable('jos_community_userpoints');
        $this->dropTable('jos_community_userpref');
        $this->dropTable('jos_community_users');
        $this->dropTable('jos_community_videos');
        $this->dropTable('jos_community_videos_category');
        $this->dropTable('jos_community_wall');
        $this->dropTable('jos_components');
        $this->dropTable('jos_contact_details');
        $this->dropTable('jos_content');
        $this->dropTable('jos_content_frontpage');
        $this->dropTable('jos_content_rating');
        $this->dropTable('jos_core_acl_aro');
        $this->dropTable('jos_core_acl_aro_groups');
        $this->dropTable('jos_core_acl_aro_map');
        $this->dropTable('jos_core_acl_aro_sections');
        $this->dropTable('jos_core_acl_groups_aro_map');
        $this->dropTable('jos_core_log_items');
        $this->dropTable('jos_core_log_searches');
        $this->dropTable('jos_fb_announcement');
        $this->dropTable('jos_fb_attachments');
        $this->dropTable('jos_fb_categories');
        $this->dropTable('jos_fb_config');
        $this->dropTable('jos_fb_config_backup');
        $this->dropTable('jos_fb_favorites');
        $this->dropTable('jos_fb_groups');
        $this->dropTable('jos_fb_messages');
        $this->dropTable('jos_fb_messages_text');
        $this->dropTable('jos_fb_moderation');
        $this->dropTable('jos_fb_ranks');
        $this->dropTable('jos_fb_sessions');
        $this->dropTable('jos_fb_smileys');
        $this->dropTable('jos_fb_subscriptions');
        $this->dropTable('jos_fb_users');
        $this->dropTable('jos_fb_version');
        $this->dropTable('jos_fb_whoisonline');
        $this->dropTable('jos_fbc_announcement');
        $this->dropTable('jos_fbc_attachments');
        $this->dropTable('jos_fbc_categories');
        $this->dropTable('jos_fbc_config');
        $this->dropTable('jos_fbc_config_backup');
        $this->dropTable('jos_fbc_favorites');
        $this->dropTable('jos_fbc_groups');
        $this->dropTable('jos_fbc_messages');
        $this->dropTable('jos_fbc_messages_text');
        $this->dropTable('jos_fbc_moderation');
        $this->dropTable('jos_fbc_ranks');
        $this->dropTable('jos_fbc_sessions');
        $this->dropTable('jos_fbc_smileys');
        $this->dropTable('jos_fbc_subscriptions');
        $this->dropTable('jos_fbc_users');
        $this->dropTable('jos_fbc_version');
        $this->dropTable('jos_fbc_whoisonline');
        $this->dropTable('jos_groups');
        $this->dropTable('jos_jce_extensions');
        $this->dropTable('jos_jce_groups');
        $this->dropTable('jos_jce_plugins');
        $this->dropTable('jos_jf_content');
        $this->dropTable('jos_jf_tableinfo');
        $this->dropTable('jos_jforms_c7a0c');
        $this->dropTable('jos_jforms_fields');
        $this->dropTable('jos_jforms_forms');
        $this->dropTable('jos_jforms_parameters');
        $this->dropTable('jos_jforms_tparameters');
        $this->dropTable('jos_jomcomment');
        $this->dropTable('jos_jomcomment_admin');
        $this->dropTable('jos_jomcomment_config');
        $this->dropTable('jos_jomcomment_fav');
        $this->dropTable('jos_jomcomment_mailq');
        $this->dropTable('jos_jomcomment_reported');
        $this->dropTable('jos_jomcomment_reports');
        $this->dropTable('jos_jomcomment_subs');
        $this->dropTable('jos_jomcomment_tb');
        $this->dropTable('jos_jomcomment_tb_sent');
        $this->dropTable('jos_jomcomment_votes');
        $this->dropTable('jos_kb_acronym');
        $this->dropTable('jos_kb_articles');
        $this->dropTable('jos_kb_attachments');
        $this->dropTable('jos_kb_category_map');
        $this->dropTable('jos_kb_categorys');
        $this->dropTable('jos_kb_comments');
        $this->dropTable('jos_kb_customfields');
        $this->dropTable('jos_kb_customfields_records');
        $this->dropTable('jos_kb_favourites');
        $this->dropTable('jos_kb_file_map');
        $this->dropTable('jos_kb_files');
        $this->dropTable('jos_kb_helpful');
        $this->dropTable('jos_kb_layouts');
        $this->dropTable('jos_kb_notes');
        $this->dropTable('jos_kb_questions');
        $this->dropTable('jos_kb_ratings');
        $this->dropTable('jos_languages');
        $this->dropTable('jos_menu');
        $this->dropTable('jos_menu_types');
        $this->dropTable('jos_messages');
        $this->dropTable('jos_messages_cfg');
        $this->dropTable('jos_migration_backlinks');
        $this->dropTable('jos_modules');
        $this->dropTable('jos_modules_menu');
        $this->dropTable('jos_moovur_log');
        $this->dropTable('jos_moovur_metadata');
        $this->dropTable('jos_myblog_admin');
        $this->dropTable('jos_myblog_bots');
        $this->dropTable('jos_myblog_categories');
        $this->dropTable('jos_myblog_config');
        $this->dropTable('jos_myblog_content_categories');
        $this->dropTable('jos_myblog_images');
        $this->dropTable('jos_myblog_mambots');
        $this->dropTable('jos_myblog_permalinks');
        $this->dropTable('jos_myblog_redirect');
        $this->dropTable('jos_myblog_tb_sent');
        $this->dropTable('jos_myblog_uploads');
        $this->dropTable('jos_myblog_user');
        $this->dropTable('jos_myblogc_admin');
        $this->dropTable('jos_myblogc_bots');
        $this->dropTable('jos_myblogc_categories');
        $this->dropTable('jos_myblogc_config');
        $this->dropTable('jos_myblogc_content_categories');
        $this->dropTable('jos_myblogc_images');
        $this->dropTable('jos_myblogc_mambots');
        $this->dropTable('jos_myblogc_permalinks');
        $this->dropTable('jos_myblogc_tb_sent');
        $this->dropTable('jos_myblogc_uploads');
        $this->dropTable('jos_myblogc_user');
        $this->dropTable('jos_newsfeeds');
        $this->dropTable('jos_plugins');
        $this->dropTable('jos_poll_data');
        $this->dropTable('jos_poll_date');
        $this->dropTable('jos_poll_menu');
        $this->dropTable('jos_polls');
        $this->dropTable('jos_sections');
        $this->dropTable('jos_session');
        $this->dropTable('jos_stats_agents');
        $this->dropTable('jos_templates_menu');
        $this->dropTable('jos_users');
        $this->dropTable('jos_vi_rating');
        $this->dropTable('jos_weblinks');

        $this->dropTable('qu_g_accounts');
        $this->dropTable('qu_g_activeviews');
        $this->dropTable('qu_g_authusers');
        $this->dropTable('qu_g_countries');
        $this->dropTable('qu_g_currencies');
        $this->dropTable('qu_g_exports');
        $this->dropTable('qu_g_fieldgroups');
        $this->dropTable('qu_g_filecontents');
        $this->dropTable('qu_g_files');
        $this->dropTable('qu_g_filter_conditions');
        $this->dropTable('qu_g_filters');
        $this->dropTable('qu_g_formfields');
        $this->dropTable('qu_g_gadgetproperties');
        $this->dropTable('qu_g_gadgets');
        $this->dropTable('qu_g_importexport');
        $this->dropTable('qu_g_installedtemplates');
        $this->dropTable('qu_g_languages');
        $this->dropTable('qu_g_logins');
        $this->dropTable('qu_g_logs');
        $this->dropTable('qu_g_mail_accounts');
        $this->dropTable('qu_g_mail_attachments');
        $this->dropTable('qu_g_mail_outbox');
        $this->dropTable('qu_g_mail_template_attachments');
        $this->dropTable('qu_g_mail_templates');
        $this->dropTable('qu_g_mails');
        $this->dropTable('qu_g_passwd_requests');
        $this->dropTable('qu_g_plannedtasks');
        $this->dropTable('qu_g_quicktasks');
        $this->dropTable('qu_g_recurrencepresets');
        $this->dropTable('qu_g_recurrencesettings');
        $this->dropTable('qu_g_roles');
        $this->dropTable('qu_g_rolesprivileges');
        $this->dropTable('qu_g_sections');
        $this->dropTable('qu_g_settings');
        $this->dropTable('qu_g_tasks');
        $this->dropTable('qu_g_userattributes');
        $this->dropTable('qu_g_users');
        $this->dropTable('qu_g_versions');
        $this->dropTable('qu_g_view_columns');
        $this->dropTable('qu_g_views');
        $this->dropTable('qu_g_wallpapers');
        $this->dropTable('qu_g_windows');
        $this->dropTable('qu_g_words');
        $this->dropTable('qu_nl_broadcasts');
        $this->dropTable('qu_nl_followups');
        $this->dropTable('qu_nl_newsletter_signups');
        $this->dropTable('qu_nl_newsletters');
        $this->dropTable('qu_nl_user_broadcasts');
        $this->dropTable('qu_nl_user_followups');
        $this->dropTable('qu_pap_affiliatescreens');
        $this->dropTable('qu_pap_affiliatetrackingcodes');
        $this->dropTable('qu_pap_banners');
        $this->dropTable('qu_pap_bannersinrotators');
        $this->dropTable('qu_pap_bannerwrappers');
        $this->dropTable('qu_pap_campaignattributes');
        $this->dropTable('qu_pap_campaigns');
        $this->dropTable('qu_pap_channels');
        $this->dropTable('qu_pap_commissiongroups');
        $this->dropTable('qu_pap_commissions');
        $this->dropTable('qu_pap_commissiontypeattributes');
        $this->dropTable('qu_pap_commissiontypes');
        $this->dropTable('qu_pap_coupons');
        $this->dropTable('qu_pap_cpmcommissions');
        $this->dropTable('qu_pap_dailyclicks');
        $this->dropTable('qu_pap_dailyimpressions');
        $this->dropTable('qu_pap_directlinkurls');
        $this->dropTable('qu_pap_impressions0');
        $this->dropTable('qu_pap_impressions1');
        $this->dropTable('qu_pap_impressions2');
        $this->dropTable('qu_pap_lifetime_referrals');
        $this->dropTable('qu_pap_monthlyclicks');
        $this->dropTable('qu_pap_monthlyimpressions');
        $this->dropTable('qu_pap_payout');
        $this->dropTable('qu_pap_payouthistory');
        $this->dropTable('qu_pap_rawclicks');
        $this->dropTable('qu_pap_recurringcommissionentries');
        $this->dropTable('qu_pap_recurringcommissions');
        $this->dropTable('qu_pap_rules');
        $this->dropTable('qu_pap_transactions');
        $this->dropTable('qu_pap_userincommissiongroup');
        $this->dropTable('qu_pap_userpayoutoptions');
        $this->dropTable('qu_pap_users');
        $this->dropTable('qu_pap_visitoraffiliates');
        $this->dropTable('qu_pap_visitors');
        $this->dropTable('qu_pap_visits');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown() {
        echo "m190830_102635_db_housekeeping cannot be reverted.\n";
        return true;
    }

}
