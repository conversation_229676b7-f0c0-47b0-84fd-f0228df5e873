<?php

/**
 * Class m190917_080004_restock_products_qty
 */
class m190917_080004_restock_products_qty extends \offgamers\base\controllers\Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $inventory = new \offgamers\base\models\ms\Inventory();
        $inventory->customRequest('migration', 'patch-restock-qty', []);

        $menu = \mdm\admin\models\Menu::findOne(['name' => 'Administrator']);

        $dataUpdate = [
            'name' => 'Service',
            'route' => '/admin/system/service',
            'data' => '',
            'parent' => $menu->id,
            'order' => 10
        ];

        $this->insert('{{%menu}}', $dataUpdate);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('{{%menu}}', ['route' => '/admin/system/service']);
        return true;
    }
}
