<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m190919_030505_mobile_recharge
 */
class m190919_030505_mobile_recharge extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('publisher', [
            'publisher_id' => $this->primaryKey(),
            'title' => $this->string(255)->notNull(),
            'profile' => $this->string(255)->notNull(),
            'last_sync' => $this->integer()->unsigned()->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('publisher_setting', [
            'publisher_setting_id' => $this->primaryKey(),
            'publisher_id' => $this->integer()->notNull(),
            'key' => $this->string(255)->notNull(),
            'value' => $this->string(255)->notNull()
        ], $tableOptions);

        $this->addForeignKey('fk_publisher_setting_publisher_id', 'publisher_setting', 'publisher_id','publisher', 'publisher_id', 'CASCADE');

        $this->createTable('mobile_recharge_region', [
            'region_id' => $this->primaryKey(),
            'flag' => $this->char(5)->notNull(),
            'iso3' => $this->char(3)->notNull(),
            'name' => $this->string(255)->notNull(),
            'prefix' => $this->smallInteger(3)->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('mobile_recharge_operator', [
            'operator_id' => $this->primaryKey(),
            'region_id' => $this->integer()->notNull(),
            'logo_path' => $this->string(2048)->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'parent_operator_id' => $this->integer(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('mobile_recharge_operator_description', [
            'operator_description_id' => $this->primaryKey(),
            'operator_id' => $this->integer()->notNull(),
            'language_id' => $this->smallInteger(1)->notNull(),
            'title' => $this->string(255)->notNull(),
            'description' => $this->text()->notNull(),
            'terms' => $this->text()->notNull()
        ], $tableOptions);

        $this->addForeignKey('fk_mobile_recharge_operator_description_operator_id', 'mobile_recharge_operator_description', 'operator_id','mobile_recharge_operator', 'operator_id', 'CASCADE');

        $this->createTable('mobile_recharge_deno', [
            'deno_id' => $this->primaryKey(),
            'type' => $this->tinyInteger(1)->notNull(),
            'operator_id' => $this->integer()->notNull(),
            'publisher_id' => $this->integer()->notNull(),
            'cost_currency' => $this->char(3)->notNull(),
            'cost_price' => $this->decimal(18,9)->notNull(),
            'sales_currency' => $this->char(3)->notNull(),
            'sales_price' => $this->decimal(18,9)->notNull(),
            'status' => $this->tinyInteger()->notNull(),
            'publisher_ref_id' => $this->string(255)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('mobile_recharge_deno_description', [
            'deno_description_id' => $this->primaryKey(),
            'deno_id' => $this->integer()->notNull(),
            'language_id' => $this->smallInteger(1)->notNull(),
            'title' => $this->string(255)->notNull(),
            'description' => $this->text()->notNull(),
        ], $tableOptions);

        $this->addForeignKey('mobile_recharge_deno_description_deno_id', 'mobile_recharge_deno_description', 'deno_id','mobile_recharge_deno', 'deno_id', 'CASCADE');
        $this->addForeignKey('mobile_recharge_deno_publisher_id', 'mobile_recharge_deno', 'publisher_id','publisher', 'publisher_id', 'CASCADE');
        $this->addForeignKey('mobile_recharge_deno_operator_id', 'mobile_recharge_deno', 'operator_id','mobile_recharge_operator', 'operator_id', 'CASCADE');

        $this->createTable('mobile_recharge_history', [
            'history_id' => $this->primaryKey(),
            'customer_id' => $this->integer()->unsigned()->notNull(),
            'prefix' => $this->smallInteger(3)->unsigned()->notNull(),
            'phone_no' => $this->string(255)->notNull(),
            'deno_id' => $this->integer()->notNull(),
            'orders_id' => $this->integer()->notNull(),
            'orders_products_id' => $this->integer()->notNull(),
            'publisher_order_id' => $this->string(255)->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('dtone_og_mapping', [
            'mapping_id' => $this->primaryKey(),
            'type' => $this->tinyInteger(1)->notNull(),
            'og_id' => $this->integer()->notNull(),
            'dtone_id' => $this->string(255)->notNull(),
            'description' => $this->string(255)->notNull()
        ], $tableOptions);

        $this->changeDb('db');

        $publisher_menu = [
            'name' => 'Mobile Recharge',
            'parent' => null,
            'route' => null,
            'order' => 15,
            'data' => 'fa fa-bullhorn',
        ];

        $this->insert('{{%menu}}', $publisher_menu);
        $id = Yii::$app->db->getLastInsertID();
        $menu_list = [
            [
                'name' => 'Publisher',
                'parent' => $id,
                'route' => '/publisher/index',
                'order' => 10
            ],
            [
                'name' => 'Operator',
                'parent' => $id,
                'route' => '/mobile-recharge/operator-list',
                'order' => 20
            ],
            [
                'name' => 'Denomination',
                'parent' => $id,
                'route' => '/mobile-recharge/deno-list',
                'order' => 30
            ],
            [
                'name' => 'Region',
                'parent' => $id,
                'route' => '/mobile-recharge/region-list',
                'order' => 40
            ]
        ];

        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }
        return true;

    }


    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('mobile_recharge_deno_description');
        $this->dropTable('mobile_recharge_deno');
        $this->dropTable('mobile_recharge_operator_description');
        $this->dropTable('mobile_recharge_operator');
        $this->dropTable('mobile_recharge_region');
        $this->dropTable('publisher_setting');
        $this->dropTable('publisher');
        $this->dropTable('mobile_recharge_history');
        $this->dropTable('dtone_og_mapping');
        $this->changeDb('db');
        $mobile_recharge_menu = \mdm\admin\models\Menu::find()->select('id')->where(['name' => 'Mobile Recharge'])->one();
        $this->delete('{{%menu}}', ['parent' => $mobile_recharge_menu->id]);
        $this->delete('{{%menu}}', ['id'=>$mobile_recharge_menu->id]);
    }

    private function changeDb($db){
        $this->init($db);
    }

}
