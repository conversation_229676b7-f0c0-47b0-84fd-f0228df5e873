<?php

use yii\db\Migration;

/**
 * Class m191021_070605_admin_sc_permission
 */
class m191021_070605_admin_sc_permission extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $auth = Yii::$app->authManager;

        $permission_list = [
            '[Store Credit] Manual Add' => 'Permission to add Store Credit manually',
            '[Store Credit] Manual Deduct' => 'Permission to deduct Store Credit manually',
            '[Store Credit] Manual Convert' => 'Permission to conver Store Credit manually'
        ];

        try {
            foreach ($permission_list as $name => $description) {
                $newPermission = $auth->createPermission($name);
                $newPermission->description = $description;
                $auth->add($newPermission);
            }
        } catch (\Exception $exception) {
            // Do Nothing, Duplicate Permission
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m191021_070605_admin_sc_permission reverted.\n";
    }
}
