<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m191021_071801_log_tagging
 */
class m191021_071801_log_tagging extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_log')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->addColumn('outgoing_request_log', 'tag', $this->string(255)->notNull());
        $this->addColumn('incoming_request_log', 'tag', $this->string(255)->notNull());
        $this->init('db');
        $this->alterColumn('user','username',$this->string(255)->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('outgoing_request_log', 'tag');
        $this->dropColumn('incoming_request_log', 'tag');
        $this->init('db');
        $this->alterColumn('user','username',$this->string(32)->notNull());
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m191021_071801_log_tagging cannot be reverted.\n";

        return false;
    }
    */
}
