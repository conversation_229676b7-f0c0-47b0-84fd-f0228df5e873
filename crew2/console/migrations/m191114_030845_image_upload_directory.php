<?php

use yii\db\Migration;

/**
 * Class m191114_030845_image_upload_directory
 */
class m191114_030845_image_upload_directory extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $toolbox_menu = [
            'name' => 'Toolbox',
            'parent' => null,
            'route' => null,
            'order' => 80,
            'data' => 'fa fa-wrench',
        ];

        $this->insert('{{%menu}}', $toolbox_menu);
        $id = Yii::$app->db->getLastInsertID();

        $file_uploader_menu = [
            'name' => 'File Uploader',
            'parent' => $id,
            'route' => null,
            'order' => 10,
        ];

        $this->insert('{{%menu}}', $file_uploader_menu);
        $id = Yii::$app->db->getLastInsertID();

        $menu_list = [
            [
                'name' => 'Upload',
                'parent' => $id,
                'route' => '/file-uploader/index',
                'order' => 10
            ],
            [
                'name' => 'Upload History',
                'parent' => $id,
                'route' => '/file-uploader/list',
                'order' => 20
            ],
        ];

        foreach ($menu_list as $menu) {
            $this->insert('{{%menu}}', $menu);
        }

        $image_configuration = [
            'image_category' => 'promo',
            'file_path' => '',
            'web_path' => '',
            'aws_s3_info' => json_encode(['bucket' => 'BUCKET_IMAGE', 'path' => 'promo/']),
            'user_groups' => 1
        ];

        $this->insert('{{%image_configuration}}', $image_configuration);

        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('crew2_session', [
            'id' => $this->char(40)->notNull(),
            'expire' => $this->integer(),
            'data' => 'BLOB'
        ], $tableOptions);

        $this->addPrimaryKey('crew_2_session_id','crew2_session','id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('{{%menu}}', ['name' => 'Upload History']);
        $this->delete('{{%menu}}', ['name' => 'Upload']);
        $this->delete('{{%menu}}', ['name' => 'File Uploader']);
        $this->delete('{{%menu}}', ['name' => 'Toolbox']);
        $this->delete('image_configuration', ['image_category' => 'promo']);
        $this->dropTable('crew2_session');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m191114_030845_image_upload_directory cannot be reverted.\n";

        return false;
    }
    */
}
