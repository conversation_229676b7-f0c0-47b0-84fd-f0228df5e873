<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m191114_063438_promo_page
 */
class m191114_063438_promo_page extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('promo_content', [
            'id' => $this->bigPrimaryKey(),
            'title' => $this->string()->notNull(),
            'page_title' => $this->string()->notNull(),
            'start_time' => $this->bigInteger()->notNull()->unsigned(),
            'end_time' => $this->bigInteger()->notNull()->unsigned(),
            'html' => 'MEDIUMTEXT NOT NULL',
            'css' => 'MEDIUMTEXT NOT NULL',
            'javascript' => 'MEDIUMTEXT NOT NULL',
            'json_data' => 'MEDIUMTEXT NOT NULL',
            'status' => $this->tinyInteger(1)->notNull()->unsigned(),
            'modified_by' => $this->string()->notNull(),
            'created_at' => $this->bigInteger()->notNull()->unsigned(),
            'updated_at' => $this->bigInteger()->notNull()->unsigned()
        ], $tableOptions);

        $bdt_menu = [
            'name' => 'BDT',
            'parent' => null,
            'route' => null,
            'order' => 35,
            'data' => 'fa fa-briefcase',
        ];

        $this->insert('{{%menu}}', $bdt_menu);
        $id = Yii::$app->db->getLastInsertID();

        $promotion_menu = [
            'name' => 'Promotion Page',
            'parent' => $id,
            'route' => '/promo-content/index',
            'order' => 100,
        ];

        $this->insert('{{%menu}}', $promotion_menu);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('promo_content');
        $this->delete('{{%menu}}', ['name' => 'Promotion Page']);
        $this->delete('{{%menu}}', ['name' => 'BDT']);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m191114_063438_promo_page cannot be reverted.\n";

        return false;
    }
    */
}
