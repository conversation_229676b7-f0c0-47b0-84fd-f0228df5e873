<?php

use common\models\OrdersTaxConfiguration;
use yii\db\Schema;
use yii\db\Migration;

/**
 * Class m191119_032011_tax_module_init
 */
class m191119_032011_tax_module_init extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add column for orders_tax_configuration
        $table = $this->db->schema->getTableSchema('orders_tax_configuration');
        (!isset($table->columns['orders_include_reverse_charge'])) ? $this->addColumn('orders_tax_configuration', 'orders_include_reverse_charge', Schema::TYPE_TINYINT . '(1) NOT NULL DEFAULT 0 AFTER orders_provide_invoice_status') : '';
        (!isset($table->columns['business_tax_status'])) ? $this->addColumn('orders_tax_configuration', 'business_tax_status', Schema::TYPE_TINYINT . '(1) NOT NULL DEFAULT 0 AFTER orders_provide_invoice_status') : '';
        (!isset($table->columns['business_tax_percentage'])) ? $this->addColumn('orders_tax_configuration', 'business_tax_percentage', Schema::TYPE_DECIMAL . '(6,2) NOT NULL DEFAULT 0.00 AFTER orders_tax_percentage') : '';
        (!isset($table->columns['business_tax_form'])) ? $this->addColumn('orders_tax_configuration', 'business_tax_form', Schema::TYPE_TEXT . ' AFTER company_logo') : '';
        (!isset($table->columns['country_name'])) ? $this->addColumn('orders_tax_configuration', 'country_name', 'VARCHAR(64) NOT NULL AFTER country_code') : '';
        
        $table = $this->db->schema->getTableSchema('orders_tax_configuration_log');
        (!isset($table->columns['log_orders_tax_lang_id'])) ? $this->addColumn('orders_tax_configuration_log', 'log_orders_tax_lang_id', Schema::TYPE_INTEGER . '(11) AFTER log_orders_tax_id') : '';
        (!isset($table->columns['log_data'])) ? $this->renameColumn('orders_tax_configuration_log', 'log_remarks', 'log_data') : '';

        // Add data to the new column
        $taxObj = OrdersTaxConfiguration::find()->all();
        foreach ($taxObj as $taxConfig) {
            $taxConfigObj = OrdersTaxConfiguration::findOne($taxConfig->orders_tax_id);
            $countryName = Yii::$app->country->getCountryList()[$taxConfigObj->country_code];
            $taxConfigObj->country_name = $countryName;
            $taxConfigObj->business_tax_form = $this->getInitDyanmicForm();
            $taxConfigObj->update();
        }

        // Add new table storing customer business data
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";

        if (!in_array('orders_tax_customers', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%orders_tax_customers}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'customers_id' => 'bigint(20) NOT NULL',
                    'orders_tax_id' => 'bigint(20) NOT NULL',
                    'country_name' => 'varchar(64) NOT NULL',
                    'business_name' => 'varchar(255) NOT NULL',
                    'business_tax_number' => 'varchar(255) NOT NULL',
                    'orders_tax_customers_data' => Schema::TYPE_TEXT,
                    'orders_tax_customers_status' => Schema::TYPE_TINYINT . '(1) NOT NULL DEFAULT 0',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_customers_id', 'orders_tax_customers', 'customers_id');
                $this->createIndex('idx_orders_tax_id', 'orders_tax_customers', 'orders_tax_id');
                $this->createIndex('idx_business_tax_number', 'orders_tax_customers', 'business_tax_number');
            }
        }
        // log changes to the data
        if (!in_array('orders_tax_customers_history', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%orders_tax_customers_history}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'customers_id' => 'bigint(20) NOT NULL',
                    'orders_tax_customers_id' => 'bigint(20) NOT NULL',
                    'orders_tax_customers_data' => Schema::TYPE_TEXT,
                    'orders_tax_customers_status' => Schema::TYPE_TINYINT . '(1) NOT NULL DEFAULT 0',
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_customers_id', 'orders_tax_customers_history', 'customers_id');
                $this->createIndex('idx_orders_tax_customers_id', 'orders_tax_customers_history', 'orders_tax_customers_id');
            }
        }

        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }

        $this->_updateMenuTable();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->execute('SET foreign_key_checks = 0');
        $table = $this->db->schema->getTableSchema('orders_tax_configuration');
        (isset($table->columns['orders_include_reverse_charge'])) ? $this->dropColumn('orders_tax_configuration', 'orders_include_reverse_charge') : '';
        (isset($table->columns['business_tax_status'])) ? $this->dropColumn('orders_tax_configuration', 'business_tax_status') : '';
        (isset($table->columns['business_tax_percentage'])) ? $this->dropColumn('orders_tax_configuration', 'business_tax_percentage') : '';
        (isset($table->columns['business_tax_form'])) ? $this->dropColumn('orders_tax_configuration', 'business_tax_form') : '';
        (isset($table->columns['country_name'])) ? $this->dropColumn('orders_tax_configuration', 'country_name') : '';

        $table = $this->db->schema->getTableSchema('orders_tax_configuration_log');
        (isset($table->columns['log_orders_tax_lang_id'])) ? $this->dropColumn('orders_tax_configuration_log', 'log_orders_tax_lang_id') : '';
        (isset($table->columns['log_data'])) ? $this->renameColumn('orders_tax_configuration_log', 'log_data', 'log_remarks') : '';
        
        $this->execute('DROP TABLE IF EXISTS `orders_tax_customers`');
        $this->execute('DROP TABLE IF EXISTS `orders_tax_customers_history`');
        $this->execute('SET foreign_key_checks = 1;');

        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }

        $this->delete('{{%menu}}', ['name' => 'Tax Country List']);
        $this->delete('{{%menu}}', ['name' => 'Tax Business List']);
        $this->delete('{{%menu}}', ['name' => 'Finance/Ecommerce']);

        echo "m191119_032011_tax_module_init reverted.\n";
    }

    private function getInitDyanmicForm()
    {
        return '{"1":[{"id":"1","title":"Business Name","name":"input_business_name","mandatory":"1","sort_order":"10","error_message":"Input Error Message","type":"text_box","size":"30","max_char":"50","pre_text":"Pre-Text","post_text":"Post-Text","status":"1"},{"id":"2","title":"Tax Number","name":"input_tax_number","mandatory":"1","sort_order":"20","error_message":"Input Error Message","type":"text_box","size":"30","max_char":"50","pre_text":"Pre-Text","post_text":"Post-Text","status":"1"}]}';
    }

    private function _getRouteData()
    {
        return [
            '/tax/*',
            '/tax/index',
            '/tax/update',
            '/tax/create',
            '/tax/set-tax-status',
            '/tax/business-list',
            '/tax/set-tax-business-status',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $anb_menu = [
            'name' => 'Finance/Ecommerce',
            'parent' => null,
            'route' => null,
            'order' => 25,
            'data' => 'fa fa-university',
        ];

        $this->insert('{{%menu}}', $anb_menu);
        $id = Yii::$app->db->getLastInsertID();

        $tax_menu = [
            'name' => 'Tax Country List',
            'parent' => $id,
            'route' => '/tax/index',
            'order' => 10,
        ];

        $this->insert('{{%menu}}', $tax_menu);

        $tax_menu = [
            'name' => 'Tax Business List',
            'parent' => $id,
            'route' => '/tax/business-list',
            'order' => 20,
        ];

        $this->insert('{{%menu}}', $tax_menu);
    }
}
