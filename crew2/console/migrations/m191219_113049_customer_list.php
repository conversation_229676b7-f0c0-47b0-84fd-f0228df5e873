<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m191219_113049_customer_list
 */
class m191219_113049_customer_list extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('orders_tax_configuration', 'start_datetime', Schema::TYPE_DATETIME . ' NOT NULL DEFAULT \'0000-00-00 00:00:00\'');

        $this->update('{{%menu}}', ['name' => 'Finance'], ['name' => 'Finance/Ecommerce']);

        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }

        $this->_updateMenuTable();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
        
        $this->delete('{{%menu}}', ['name' => 'Customer List']);
        $this->delete('{{%menu}}', ['name' => 'Customer']);

        echo "m191219_113049_customer_list cannot be reverted.\n";
    }

    private function _getRouteData()
    {
        return [
            '/customer/*',
            '/customer/index',
            '/customer/update',
            '/tax/update-dynamic-form-sort',
            '/tax/add-dynamic-form-input',
            '/tax/update-dynamic-form-status',
            '/tax/get-dynamic-form-info',
            '/tax/delete-dynamic-form-input',
            '/tax/update-dynamic-form-input',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $menu = [
            'name' => 'Customer',
            'parent' => null,
            'route' => null,
            'order' => 26,
            'data' => 'fa fa-users',
        ];

        $this->insert('{{%menu}}', $menu);
        $id = Yii::$app->db->getLastInsertID();

        $sub_menu = [
            'name' => 'Customer List',
            'parent' => $id,
            'route' => '/customer/index',
            'order' => 10,
        ];

        $this->insert('{{%menu}}', $sub_menu);
    }
}
