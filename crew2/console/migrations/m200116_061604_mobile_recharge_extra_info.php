<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m200116_061604_mobile_recharge_extra_info
 */
class m200116_061604_mobile_recharge_extra_info extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->addColumn('mobile_recharge_deno', 'mark_up', Schema::TYPE_INTEGER.' DEFAULT 0 AFTER `publisher_ref_id`');
        $this->addColumn('mobile_recharge_deno', 'products_id', Schema::TYPE_INTEGER.' DEFAULT 0 AFTER `mark_up`');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('mobile_recharge_deno', 'products_id');
        $this->dropColumn('mobile_recharge_deno', 'mark_up');
    }

    private function changeDb($db)
    {
        $this->init($db);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200116_061604_mobile_recharge_extra_info cannot be reverted.\n";

        return false;
    }
    */
}
