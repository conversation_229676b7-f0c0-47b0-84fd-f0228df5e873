<?php

use console\models\CurrenciesHistory;
use yii\db\Migration;

/**
 * Class m200228_030559_order_review
 */
class m200228_030559_order_review extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('orders_review', [
            'id' => $this->bigPrimaryKey(),
            'customers_id' => $this->integer(),
            'orders_id' => $this->integer()->unique(),
            'created_at' => $this->bigInteger()->notNull()->unsigned()
        ], $tableOptions);

        $this->createIndex('idx_orders_review_orders_id', 'orders_review', 'customers_id');

        $history_list = CurrenciesHistory::find()->where(['>', 'date_from', '2020-02-27 00:00:00'])->all();

        foreach ($history_list as $history) {
            $last_record = CurrenciesHistory::find()->where(['currencies_id' => $history->currencies_id])->andWhere(['<', 'id', $history->id])->orderBy('id DESC')->one();
            $history->version = ($last_record ? $last_record->version + 1 : 1);
            $next_record = CurrenciesHistory::find()->where(['currencies_id' => $history->currencies_id])->andWhere(['>', 'id', $history->id])->orderBy('id')->one();
            if($next_record){
                $history->date_to = $next_record->date_from;
            }
            $history->save();
        }

    }


    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('orders_review');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200228_030559_order_review cannot be reverted.\n";

        return false;
    }
    */
}
