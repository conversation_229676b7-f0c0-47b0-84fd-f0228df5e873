<?php

use yii\db\Migration;

/**
 * Class m200302_035649_hide_phone_num
 */
class m200302_035649_hide_phone_num extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->addColumn('mobile_recharge_history', 'hide_from_listing', 'tinyint(1) DEFAULT 0 AFTER `status`');

        $this->init('db_offgamers');

        $this->dropTable('rating_tags');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('mobile_recharge_history', 'hide_from_listing');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200302_035649_hide_phone_num cannot be reverted.\n";

        return false;
    }
    */
}
