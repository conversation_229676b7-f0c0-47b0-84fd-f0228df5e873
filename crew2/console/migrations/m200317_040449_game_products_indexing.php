<?php

use yii\db\Migration;

/**
 * Class m200317_040449_game_products_indexing
 */
class m200317_040449_game_products_indexing extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex('idx_game_publisher_product_publisher_reference_id', 'game_publisher_product', ['publisher_reference_id','game_publisher_id']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx_game_publisher_product_publisher_reference_id', 'game_publisher_product');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200317_040449_game_products_indexing cannot be reverted.\n";

        return false;
    }
    */
}
