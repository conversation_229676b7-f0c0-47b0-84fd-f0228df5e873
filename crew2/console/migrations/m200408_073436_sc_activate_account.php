<?php

use yii\db\Migration;

/**
 * Class m200408_073436_sc_activate_account
 */
class m200408_073436_sc_activate_account extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $auth = Yii::$app->authManager;

        $permission_list = [
            '[Store Credit] Activate Account' => 'Permission to activate Store Credit Account',
            '[Store Credit] Suspend Account' => 'Permission to suspend Store Credit Account',
        ];

        try {
            foreach ($permission_list as $name => $description) {
                $newPermission = $auth->createPermission($name);
                $newPermission->description = $description;
                $auth->add($newPermission);
            }
        } catch (\Exception $exception) {
            // Do Nothing, Duplicate Permission
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m200408_073436_sc_activate_account cannot be reverted.\n";
    }
}
