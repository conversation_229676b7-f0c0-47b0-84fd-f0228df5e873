<?php

use yii\db\Migration;

/**
 * Class m200622_170904_store_credit_bulk_update
 */
class m200622_170904_store_credit_bulk_update extends Migration {

    /**
     * {@inheritdoc}
     */
    public function init($db = 'db') {
        $this->db = $db;
        parent::init();
    }

    public function safeUp() {
        // add menu
        $menu = [
            'name' => 'Sales',
            'parent' => null,
            'route' => null,
            'order' => 15,
            'data' => 'fa fa-coins',
        ];
        $this->insert('{{%menu}}', $menu);
        $id = Yii::$app->db->getLastInsertID();

        $dataUpdate = [
            'parent' => $id,
        ];
        $this->update('{{%menu}}', $dataUpdate, ['route' => '/store-credit/index']);

        // create delivery queue table
        $this->changeDb('db_og');

        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('deliver_queue', [
            'dq_id' => $this->primaryKey(),
            'type' => $this->string(10)->notNull(),
            'id' => $this->integer()->unsigned()->notNull(),
            'extra_info' => $this->text()->comment('data store in JSON format'),
            'created_at' => $this->integer()->unsigned()->notNull(),
                ], $tableOptions);
        $this->createIndex('idx_type', 'deliver_queue', 'type');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown() {
        $menu = \mdm\admin\models\Menu::find()->select('id')->where(['name' => 'Report'])->one();
        $dataUpdate = [
            'parent' => $menu->id,
        ];
        $this->update('{{%menu}}', $dataUpdate, ['route' => '/store-credit/index']);

        $this->changeDb('db_og');
        $this->dropTable('deliver_queue');

        echo "m200622_170904_store_credit_batch_insert has been reverted.\n";

        return true;
    }

    private function changeDb($db) {
        $this->init($db);
    }

}
