<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m200629_094232_location_restriction
 */
class m200629_094232_location_restriction extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add new table storing location restrictions
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";

        if (!in_array('location_restriction', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%location_restriction}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'country_iso_code2' => 'char(2) NOT NULL',
                    'restriction_info' => Schema::TYPE_TEXT,
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'changed_by' => 'varchar(255) NOT NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_country_iso_code2', 'location_restriction', 'country_iso_code2');
            }
        }
        // log changes to the data
        if (!in_array('location_restriction_history', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%location_restriction_history}}', [
                    'id' => 'bigint(20) unsigned NOT NULL AUTO_INCREMENT',
                    'location_restriction_id' => 'bigint(20) NOT NULL',
                    'country_iso_code2' => 'char(2) NOT NULL',
                    'restriction_history' => Schema::TYPE_TEXT,
                    'created_at' => 'int(11) NULL',
                    'updated_at' => 'int(11) NULL',
                    'changed_by' => 'varchar(255) NOT NULL',
                    0 => 'PRIMARY KEY (`id`)',
                ], $tableOptions_mysql);
                $this->createIndex('idx_country_iso_code2', 'location_restriction_history', 'country_iso_code2');
                $this->createIndex('idx_location_restriction_id', 'location_restriction_history', 'location_restriction_id');
            }
        }

        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
        // Add menu item
        $this->_updateMenuTable();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop tables
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `location_restriction`');
        $this->execute('DROP TABLE IF EXISTS `location_restriction_history`');
        $this->execute('SET foreign_key_checks = 1;');
        // Remove routes and menu
        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
        // Delete menu item
        $this->delete('{{%menu}}', ['name' => 'Location Restriction']);

        echo "m200629_094232_location_restriction reverted.\n";
    }

    private function _getRouteData()
    {
        return [
            '/location-restriction/*',
            '/location-restriction/index',
            '/location-restriction/assign',
            '/location-restriction/remove',
            '/location-restriction/refresh',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $anb_menu = [
            'name' => 'Setting',
            'parent' => null,
            'route' => null,
            'order' => 70,
            'data' => 'fa fa-cog',
        ];

        $this->insert('{{%menu}}', $anb_menu);
        $id = Yii::$app->db->getLastInsertID();

        // Prepare menu item for index location restriction config
        $cc_menu = [
            'name' => 'Location Restriction',
            'parent' => $id,
            'route' => '/location-restriction/index',
            'order' => 30,
        ];

        $this->insert('{{%menu}}', $cc_menu);
    }
}
