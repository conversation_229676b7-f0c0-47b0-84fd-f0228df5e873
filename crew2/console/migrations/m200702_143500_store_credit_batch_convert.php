<?php

use yii\db\Schema;
use yii\db\Migration;

/**
 * Class m200702_143500_store_credit_batch_convert
 */
class m200702_143500_store_credit_batch_convert extends Migration {

    /**
     * {@inheritdoc}
     */
    public function init($db = 'db') {
        $this->db = $db;
        parent::init();
    }

    public function safeUp() {
        // create delivery queue table
        $this->changeDb('db_og');
        $this->addColumn('deliver_queue', 'lock_by', Schema::TYPE_STRING);
        $this->createIndex('idx_lock_by', 'deliver_queue', 'lock_by');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown() {
        $this->dropColumn('deliver_queue', 'lock_by');
        $this->dropIndex('idx_lock_by', 'deliver_queue');
        echo "m200702_143500_store_credit_batch_convert has been reverted.\n";

        return true;
    }

    private function changeDb($db) {
        $this->init($db);
    }

}
