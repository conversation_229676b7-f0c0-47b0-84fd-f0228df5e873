<?php

use yii\db\Migration;

/**
 * Class m200710_064513_log_indexing
 */
class m200710_064513_log_indexing extends Migration
{
    public function init($db = 'db_log')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->createIndex('idx_app', 'outgoing_request_log', 'app');
        $this->createIndex('idx_request_url', 'outgoing_request_log', 'request_url(2048)');
        $this->createIndex('idx_created_at', 'outgoing_request_log', 'created_at');
        $this->createIndex('idx_tag', 'outgoing_request_log', 'tag');

        $this->createIndex('idx_app', 'incoming_request_log', 'app');
        $this->createIndex('idx_request_url', 'incoming_request_log', 'request_url(2048)');
        $this->createIndex('idx_created_at', 'incoming_request_log', 'created_at');
        $this->createIndex('idx_tag', 'incoming_request_log', 'tag');

        $this->createIndex('idx_app', 'dev_debug_log', 'app');
        $this->createIndex('idx_created_at', 'dev_debug_log', 'created_at');
        $this->createIndex('idx_tag', 'dev_debug_log', 'tag');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx_app', 'outgoing_request_log');
        $this->dropIndex('idx_request_url', 'outgoing_request_log');
        $this->dropIndex('idx_created_at', 'outgoing_request_log');
        $this->dropIndex('idx_tag', 'outgoing_request_log');

        $this->dropIndex('idx_app', 'incoming_request_log');
        $this->dropIndex('idx_request_url', 'incoming_request_log');
        $this->dropIndex('idx_created_at', 'incoming_request_log');
        $this->dropIndex('idx_tag', 'incoming_request_log');

        $this->dropIndex('idx_app', 'dev_debug_log');
        $this->dropIndex('idx_created_at', 'dev_debug_log');
        $this->dropIndex('idx_tag', 'dev_debug_log');

    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200710_064513_log_indexing cannot be reverted.\n";

        return false;
    }
    */
}
