<?php

use yii\db\Migration;

/**
 * Class m200713_084027_storecredit_statement
 */
class m200713_084027_storecredit_statement extends Migration {

    public function init($db = 'db_og') {
        $this->db = $db;
        parent::init();
    }

    public function safeUp() {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('store_credit_transaction', [
            'transaction_id' => $this->primaryKey(),
            'request_id' => $this->string(64),
            'order_id' => $this->string(64),
            'user_id' => $this->string(64),
            'user_role' => $this->string(64),
            'requesting_id' => $this->string(64),
            'requesting_role' => $this->string(64),
            'brand' => $this->string(20),
            'activity' => $this->string(20)->notNull(),
            'activity_title' => $this->text(),
            'activity_description' => $this->text(),
            'transaction_type' => $this->string(64)->notNull(),
            'transaction_amount' => $this->decimal(20, 8),
            'transaction_currency' => $this->string(64),
            'transaction_conversion_rate' => $this->decimal(20, 8),
            'previous_amount' => $this->decimal(20, 8),
            'previous_currency' => $this->string(64),
            'new_amount' => $this->decimal(20, 8),
            'new_currency' => $this->string(64),
            'allow_negative' => $this->integer(),
            'free_conversion' => $this->integer(),
            'param_1' => $this->string(250),
            'param_2' => $this->string(250),
            'param_3' => $this->string(250),
            'created_date' => $this->decimal(18, 4)->notNull(),
                ], $tableOptions);
        $this->createIndex('idx_order_id', 'store_credit_transaction', 'order_id');
        $this->createIndex('idx_created_date', 'store_credit_transaction', 'created_date');
        $this->createIndex('idx_activity', 'store_credit_transaction', 'activity');
    }

    public function safeDown() {
        $this->dropTable('store_credit_transaction');

        echo "m200713_084027_storecredit_statement has been reverted.\n";
        return true;
    }

}
