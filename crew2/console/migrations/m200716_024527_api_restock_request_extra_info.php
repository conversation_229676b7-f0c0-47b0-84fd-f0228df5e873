<?php

use yii\db\Migration;

/**
 * Class m200716_024527_apI_restock_request_extra_info
 */
class m200716_024527_api_restock_request_extra_info extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('api_restock_request_extra_info', [
            'api_restock_request_extra_info_id' => $this->primaryKey(),
            'api_restock_request_id' => $this->integer()->notNull(),
            'key' => $this->string(255)->notNull(),
            'value' => $this->text()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_key', 'api_restock_request_extra_info', ['api_restock_request_id','key'], true);

        $this->addForeignKey('fk_api_restock_request_id', 'api_restock_request_extra_info', 'api_restock_request_id','api_restock_request', 'api_restock_request_id', 'CASCADE');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('api_restock_request_extra_info');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200716_024527_apI_restock_request_extra_info cannot be reverted.\n";

        return false;
    }
    */
}
