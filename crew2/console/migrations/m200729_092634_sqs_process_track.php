<?php

use yii\db\Migration;

/**
 * Class m200729_092634_sqs_process_track
 */
class m200729_092634_sqs_process_track extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_log')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('sqs_process_track', [
            'id' => $this->primaryKey(),
            'process_id' => $this->string(255)->notNull(),
            'input' => $this->text()->notNull(),
            'process' => $this->string(255)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('sqs_process_track');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200729_092634_sqs_process_track cannot be reverted.\n";

        return false;
    }
    */
}
