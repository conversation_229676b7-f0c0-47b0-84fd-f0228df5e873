<?php

use yii\db\Migration;

/**
 * Class m200804_032511_reseller_upgrade
 */
class m200804_032511_reseller_upgrade extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('reseller_upgrade', [
            'id' => $this->primaryKey(),
            'customer_id' => $this->integer(255)->unsigned()->notNull(),
            'flag' => $this->tinyInteger(1)->notNull(),
            'reseller_since' => $this->integer()->unsigned()->notNull(),
            'last_process' => $this->integer()->unsigned()->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_customer_id','reseller_upgrade','customer_id');
        $this->createIndex('idx_updated_at','reseller_upgrade','updated_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('reseller_upgrade');
    }

}
