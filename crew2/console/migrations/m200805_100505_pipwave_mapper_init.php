<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m200805_100505_pipwave_mapper_init
 */
class m200805_100505_pipwave_mapper_init extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add new table storing pipwave payment mapper history data
        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB";

        // log changes to the data
        if (!in_array('pipwave_payment_mapper_history', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%pipwave_payment_mapper_history}}', [
                    'id' => $this->primaryKey(),
                    'pipwave_payment_mapper_id' => $this->integer()->unsigned()->notNull(),
                    'history_data' => $this->text()->notNull(),
                    'created_at' => $this->integer()->unsigned()->notNull(),
                    'updated_at' => $this->integer()->unsigned()->notNull(),
                    'changed_by' => $this->string(255)->notNull(),
                ], $tableOptions_mysql);
                $this->createIndex('idx_pipwave_payment_mapper_id', 'pipwave_payment_mapper_history', 'pipwave_payment_mapper_id');
            }
        }

        // Add available routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }

        $this->_updateMenuTable();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop tables
        $this->dropTable('pipwave_payment_mapper_history');
        // Remove routes and menu
        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
        // Delete menu item
        $this->delete('{{%menu}}', ['name' => 'Pipwave Payment Mapper']);

        echo "m200805_100505_pipwave_mapper_init reverted.\n";
    }

    private function _getRouteData()
    {
        return [
            '/pipwave-payment-mapper/*',
            '/pipwave-payment-mapper/index',
            '/pipwave-payment-mapper/view',
            '/pipwave-payment-mapper/create',
            '/pipwave-payment-mapper/update',
            '/pipwave-payment-mapper/delete',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $menu = \mdm\admin\models\Menu::findOne(['name' => 'Setting']);

        // Prepare menu item for index location restriction config
        $cc_menu = [
            'name' => 'Pipwave Payment Mapper',
            'parent' => $menu->id,
            'route' => '/pipwave-payment-mapper/index',
            'order' => 40,
        ];

        $this->insert('{{%menu}}', $cc_menu);
    }
}
