<?php

use yii\db\Migration;

/**
 * Class m200923_003041_coupon_code_enhancement
 */
class m200923_003041_coupon_code_enhancement extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('coupons', 'max_cap', $this->decimal(8, 4)->notNull()->defaultValue(0)->after('coupon_minimum_order'));
        $this->addColumn('coupons', 'restrict_to_currency_id', $this->text()->notNull()->after('restrict_to_customers_groups'));
        $this->addColumn('coupons', 'restrict_to_payment_id', $this->text()->notNull()->after('restrict_to_currency_id'));

        $this->addColumn('coupons_generation', 'fixed_code', $this->string()->notNull()->after('coupon_type'));
        $this->addColumn('coupons_generation', 'max_cap', $this->decimal(8, 4)->notNull()->defaultValue(0)->after('coupon_minimum_order'));
        $this->addColumn('coupons_generation', 'restrict_to_currency_id', $this->text()->notNull()->after('restrict_to_customers_groups'));
        $this->addColumn('coupons_generation', 'restrict_to_payment_id', $this->text()->notNull()->after('restrict_to_currency_id'));
        $this->addColumn('coupons_generation', 'created_by', $this->string()->notNull()->after('requester_id'));


        $id = \mdm\admin\models\Menu::find()->select('id')->where(['name' => 'Marketing'])->one()->id;

        $coupons_menu = [
            'name' => 'Coupons',
            'parent' => $id,
            'route' => '/coupons/index',
            'order' => 50,
        ];

        $this->insert('{{%menu}}', $coupons_menu);

        $this->patchCreatedBy();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {

        $this->dropColumn('coupons', 'max_cap');
        $this->dropColumn('coupons', 'restrict_to_currency_id');
        $this->dropColumn('coupons', 'restrict_to_payment_id');

        $this->dropColumn('coupons_generation', 'fixed_code');
        $this->dropColumn('coupons_generation', 'max_cap');
        $this->dropColumn('coupons_generation', 'restrict_to_currency_id');
        $this->dropColumn('coupons_generation', 'restrict_to_payment_id');
        $this->dropColumn('coupons_generation', 'created_by');

        $this->delete('{{%menu}}', ['name' => 'Coupons']);

    }

    private function patchCreatedBy(){
        $sub_query = (new \yii\db\Query())
            ->select(['DISTINCT(requester_id) as $id'])
            ->from(['coupons_generation']);

        $admin_data = (new \yii\db\Query())
            ->select(['admin_id', 'admin_email_address'])
            ->from(['admin'])
            ->where(['admin_id' => $sub_query])
            ->all();

        $admin_data = \yii\helpers\ArrayHelper::map($admin_data, 'admin_id', 'admin_email_address');

        foreach($admin_data as $admin_id => $admin_email){
            \common\models\CouponsGeneration::updateAll(['created_by' => $admin_email],['requester_id' => $admin_id]);
        }
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200923_003041_coupon_code_enhancement cannot be reverted.\n";

        return false;
    }
    */
}
