<?php

use yii\db\Migration;

/**
 * Class m201007_014320_tnc_page
 */
class m201007_014320_tnc_page extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('static_page_type', [
            'static_page_type_id' => $this->primaryKey(),
            'type_title' => $this->string(255)->notNull(),
            'type_code' => $this->string(255),
            'type_status' => $this->smallInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
            'changed_by' => $this->string(255)->notNull(),
        ], $tableOptions);

        $this->createTable('static_page', [
            'static_page_id' => $this->primaryKey(),
            'title' => $this->string(255)->notNull(),
            'static_page_type_id' => $this->integer()->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('static_page_content', [
            'static_page_content_id' => $this->primaryKey(),
            'static_page_id' => $this->integer()->notNull(),
            'language_id' => $this->smallInteger(1)->notNull(),
            'title' => $this->string(255)->notNull(),
            'content' => 'LONGTEXT',
        ], $tableOptions);

        $this->createTable('static_page_content_history', [
            'id' => $this->primaryKey(),
            'static_page_id' => $this->integer()->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
            'changed_by' => $this->string(255)->notNull(),
        ], $tableOptions);
        $this->createIndex('idx_static_page_id', 'static_page_content_history', 'static_page_id');

        $this->addForeignKey('static_page_content_static_page_id', 'static_page_content', 'static_page_id','static_page', 'static_page_id', 'CASCADE');
        $this->addForeignKey('static_page_static_page_type_id', 'static_page', 'static_page_type_id','static_page_type', 'static_page_type_id', 'CASCADE');

        $typeData = [
            'static_page_type_id' => 1,
            'type_title' => 'Terms & Conditions',
            'type_code' => 'TNC',
            'type_status' => 1,
            'created_at' => time(),
            'updated_at' => time(),
            'changed_by' => 'system',
        ];
        $this->insert('{{%static_page_type}}', $typeData);

        $this->changeDb('db');

        // Add available routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }

        $this->_updateMenuTable();
    }

    public function safeDown()
    {
        $this->dropTable('static_page_content_history');
        $this->dropTable('static_page_content');
        $this->dropTable('static_page');
        $this->dropTable('static_page_type');

        $this->changeDb('db');
        
        // Remove routes and menu
        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
        // Delete menu item
        $this->delete('{{%menu}}', ['name' => 'Static Page']);
        echo "m201007_014320_tnc_page reverted.\n";
    }

    private function changeDb($db){
        $this->init($db);
    }

    private function _getRouteData()
    {
        return [
            '/static-page/*',
            '/static-page/index',
            '/static-page/create',
            '/static-page/update',
            '/static-page/delete',
            '/static-page/static-page-type',
            '/static-page/create-type',
            '/static-page/update-type',
            '/static-page/delete-type',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $menu = \mdm\admin\models\Menu::findOne(['name' => 'Setting']);

        // Prepare menu item for index location restriction config
        $cc_menu = [
            'name' => 'Static Page',
            'parent' => $menu->id,
            'route' => '/static-page/index',
            'order' => 50,
        ];

        $this->insert('{{%menu}}', $cc_menu);
    }
}
