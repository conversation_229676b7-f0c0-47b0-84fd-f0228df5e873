<?php

use yii\db\Migration;

/**
 * Class m201223_084910_orders_verify_queue_log
 */
class m201223_084910_orders_verify_queue_log extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('orders_verify_queue_log', [
            'id' => $this->primaryKey(),
            'orders_id' => $this->integer()->notNull(),
            'orders_products_id' => $this->integer(),
            'orders_datetime' => $this->dateTime(),
            'orders_type' => $this->tinyInteger(1)->comment("1-DTU, 2-SC, 3-Stock, 4-others"),
            'verify_status' => $this->tinyInteger(1)->comment("1-success 2-alert"),
            'reason' => $this->string(255),
            'raw_data' => $this->string(1000),
        ], $tableOptions);

        $this->createIndex(
            'idx_orders_id',
            'orders_verify_queue_log',
            'id'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        if ($this->db->getTableSchema('orders_verify_queue_log', true) === null) {
            return true;
        }else{
            $this->dropTable('orders_verify_queue_log');
            return true;
        }
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m201223_084910_orders_verify_queue_log cannot be reverted.\n";

        return false;
    }
    */
}
