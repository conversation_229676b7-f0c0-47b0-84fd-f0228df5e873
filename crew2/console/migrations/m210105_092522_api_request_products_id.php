<?php

use yii\db\Migration;

/**
 * Class m210105_092522_api_request_products_id
 */
class m210105_092522_api_request_products_id extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('api_restock_request', 'products_id', $this->integer()->after('custom_products_code_id'));
        $this->addColumn('api_restock_request', 'is_locked', $this->tinyInteger()->notNull()->defaultValue(0)->after('status'));
        $this->createIndex('idx_products_id', 'api_restock_request', 'products_id');
        $this->createIndex('idx_publisher_order_id', 'api_restock_request', 'publisher_order_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('api_restock_request', 'products_id');
        $this->dropColumn('api_restock_request', 'is_locked');
        $this->dropIndex('idx_publisher_order_id', 'api_restock_request');
    }
}
