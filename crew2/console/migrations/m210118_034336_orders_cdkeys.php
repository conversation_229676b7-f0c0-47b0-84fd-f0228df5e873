<?php

use yii\db\Migration;

/**
 * Class m210118_034336_orders_cdkeys
 */
class m210118_034336_orders_cdkeys extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db') {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('orders_cdkeys', [
            'id' => $this->primaryKey(),
            'orders_products_id' => $this->integer()->notNull(),
            'custom_products_code_id' => $this->integer()->notNull()->unique(),
            'status' => $this->tinyInteger()->notNull(),
            'data_encrypted' => $this->text(),
            'created_at' => $this->timestamp()->notNull(),
            'updated_at' => $this->timestamp()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_orders_cdkeys_id', 'orders_cdkeys', ['id'], true);
        $this->createIndex('idx_orders_cdkeys_cpid', 'orders_cdkeys', ['custom_products_code_id'], true);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('orders_cdkeys');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210118_034336_orders_cdkeys cannot be reverted.\n";

        return false;
    }
    */
}
