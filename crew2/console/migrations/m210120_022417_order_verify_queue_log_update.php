<?php

use yii\db\Migration;

/**
 * Class m210120_022417_order_verify_queue_log_update
 */
class m210120_022417_order_verify_queue_log_update extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->alterColumn('orders_verify_queue_log','raw_data','text');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m210120_022417_order_verify_queue_log_update cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210120_022417_order_verify_queue_log_update cannot be reverted.\n";

        return false;
    }
    */
}
