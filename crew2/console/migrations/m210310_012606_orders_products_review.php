<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m210310_012606_orders_products_review
 */
class m210310_012606_orders_products_review extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('orders_products_reviews', [
            'review_id' => $this->primaryKey(),
            'orders_id' => $this->integer(),
            'orders_products_id' => $this->integer(),
            'products_id' => $this->integer(),
            'categories_id' => $this->integer(),
            'review_score' => $this->integer(),
            'comment' => $this->text(),
            'status' => $this->tinyInteger(1)->comment("1-Draft 2-Completed 3-Editable 4-Edited"),
            'visibility' => $this->tinyInteger(1)->comment("1-Visible, 2-Not Visible"),
            'created_at' => $this->bigInteger()->unsigned(),
            'created_by' => $this->string(100),
            'updated_at' => $this->bigInteger()->unsigned(),
            'updated_by' => $this->string(100),
        ], $tableOptions);


        $this->createIndex('idx_review_id', 'orders_products_reviews', ['review_id'], true);

        $this->createTable('orders_products_reviews_remarks', [
            'remark_id' => $this->primaryKey(),
            'review_id' => $this->integer(),
            'remarks' => $this->text(),
            'status' => $this->tinyInteger(1)->comment("1-Normal 2-Internal Only"),
            'created_at' => $this->bigInteger()->unsigned(),
            'created_by' => $this->string(100),
            'updated_at' => $this->bigInteger()->unsigned(),
            'updated_by' => $this->string(100),
        ], $tableOptions);

        $this->createIndex('idx_remark_id', 'orders_products_reviews_remarks', ['remark_id'], true);

        $this->createTable('orders_products_reviews_logs', [
            'log_id' => $this->primaryKey(),
            'created_at' => $this->bigInteger()->unsigned(),
            'created_by' => $this->string(100),
            'activities' => $this->text(),
            'review_id' => $this->integer(),
        ], $tableOptions);
        $this->createIndex('idx_log_id', 'orders_products_reviews_logs', ['log_id'], true);

        $this->init('db');

        $tables = $this->db->schema->getTableNames();

        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
        }
        // Add menu item
        $this->_updateMenuTable();

        return true;

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('orders_products_reviews');
        $this->dropTable('orders_products_reviews_remarks');
        $this->dropTable('orders_products_reviews_logs');

        $this->init('db');

        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
        // Delete menu item
        $this->delete('{{%menu}}', ['name' => 'Order review List']);


        echo "m210310_012606_orders_products_review reverted.\n";
    }

    private function _getRouteData()
    {
        return [
            '/order-review/*',
            '/order-review/index',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $anb_menu = [
            'name' => 'Setting',
            'parent' => null,
            'route' => null,
            'order' => 70,
            'data' => 'fa fa-cog',
        ];

        $this->insert('{{%menu}}', $anb_menu);
        $id = Yii::$app->db->getLastInsertID();

        // Prepare menu item for index location restriction config
        $cc_menu = [
            'name' => 'Order review List',
            'parent' => $id,
            'route' => '/order-review/index',
            'order' => 30,
        ];

        $this->insert('{{%menu}}', $cc_menu);
    }
}
