<?php

use yii\db\Migration;

/**
 * Class m210310_031006_sc_request
 */
class m210310_031006_sc_request extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('store_credit_request', [
            'id' => $this->primaryKey(),
            'request_id' => $this->string(64),
            'reference_key' => $this->string(64),
            'reference_id' => $this->string(64),
            'activity_type' => $this->string(20)->notNull(),
            'transaction_type' => $this->string(64),
            'currency_from' => $this->string(64),
            'currency_from_amount' => $this->decimal(20, 8),
            'currency_to' => $this->string(64),
            'currency_to_amount' => $this->decimal(20, 8),
            'currency_rate' => $this->decimal(20, 8),
            'status' => $this->integer()->defaultValue(0),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_request_id', 'store_credit_request', 'request_id');
        $this->createIndex('idx_reference_key', 'store_credit_request', 'reference_key');
        $this->createIndex('idx_reference_id', 'store_credit_request', 'reference_id');
        $this->createIndex('idx_activity_type', 'store_credit_request', 'activity_type');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        if ($this->db->getTableSchema('store_credit_request', true) === null) {
            return true;
        } else {
            $this->dropTable('store_credit_request');
            echo "m210310_031006_sc_request has been reverted.\n";
            return true;
        }
    }
}
