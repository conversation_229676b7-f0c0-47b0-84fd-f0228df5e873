<?php

use yii\db\Migration;

/**
 * Class m210324_000909_log_ses_bounce
 */
class m210324_000909_log_ses_bounce extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('log_ses_bounce', 'bounced_type', $this->string(255)->defaultValue('')->after('email'));
        $this->addColumn('log_ses_bounce', 'bounced_sub_type', $this->string(255)->defaultValue('')->after('bounced_type'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('log_ses_bounce', 'bounced_type');
        $this->dropColumn('log_ses_bounce', 'bounced_sub_type');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210324_000909_log_ses_bounce cannot be reverted.\n";

        return false;
    }
    */
}
