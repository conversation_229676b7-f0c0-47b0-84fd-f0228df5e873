<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m210415_072056_order_rating_phase2
 */
class m210415_072056_order_rating_phase2 extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('products_reviews_blacklist', [
            'blacklist_id' => $this->primaryKey(),
            'item_id' => $this->integer(),
            'item_type' => $this->string(),
            'status' => $this->integer()->comment("1-active, 0-deleted"),
            'created_at' => $this->bigInteger()->unsigned(),
            'created_by' => $this->string(255),
            'updated_at' => $this->bigInteger()->unsigned(),
            'updated_by' => $this->string(255),
        ], $tableOptions);


        $this->createIndex('idx_blacklist_id', 'products_reviews_blacklist', ['blacklist_id'], true);

        $this->init('db');

        $tables = $this->db->schema->getTableNames();

        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
        }
        // Add menu item
        $this->_updateMenuTable();
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        return true;
    }

    private function _getRouteData()
    {
        return [
            '/product-review/*',
            '/product-review/index',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }

    private function _updateMenuTable()
    {
        // Tax Module menu
        $anb_menu = [
            'name' => 'Setting',
            'parent' => null,
            'route' => null,
            'order' => 70,
            'data' => 'fa fa-cog',
        ];

        $this->insert('{{%menu}}', $anb_menu);
        $id = Yii::$app->db->getLastInsertID();

        // Prepare menu item for index location restriction config
        $cc_menu = [
            'name' => 'Order review List',
            'parent' => $id,
            'route' => '/order-review/index',
            'order' => 30,
        ];

        $this->insert('{{%menu}}', $cc_menu);
    }
}
