<?php

use yii\db\Migration;

/**
 * Class m210505_130723_mail_validation
 */
class m210505_130723_mail_validation extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('email_validation', [
            'id' => $this->primaryKey(),
            'email' => $this->string(768)->unique()->notNull(),
            'profile' => $this->string(255)->notNull(),
            'mx_found' => $this->tinyInteger(1)->notNull(),
            'smtp_check' => $this->tinyInteger(1)->notNull(),
            'score' => $this->integer()->notNull(),
            'raw' => $this->text(),
            'created_at' => $this->bigInteger()->unsigned(),
            'updated_at' => $this->bigInteger()->unsigned(),
        ], $tableOptions);

        $this->createIndex('idx_email', 'email_validation', 'email', true);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('email_validation');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210505_130723_mail_validation cannot be reverted.\n";

        return false;
    }
    */
}
