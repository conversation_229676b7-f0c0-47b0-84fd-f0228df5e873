<?php

use yii\db\Migration;

/**
 * Class m210510_013334_orders_review_indexes
 */
class m210510_013334_orders_review_indexes extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->dropIndex('idx_review_id','orders_products_reviews');
        $this->createIndex('idx_orders_id', 'orders_products_reviews', ['orders_id']);
        $this->createIndex('idx_orders_products_id', 'orders_products_reviews', ['orders_products_id']);
        $this->createIndex('idx_products_id', 'orders_products_reviews', ['products_id']);
        $this->createIndex('idx_categories_id', 'orders_products_reviews', ['categories_id']);

        $this->dropIndex('idx_remark_id', 'orders_products_reviews_remarks');
        $this->createIndex('idx_review_id', 'orders_products_reviews_remarks', ['review_id']);

        return true;

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->createIndex('idx_review_id','orders_products_reviews', ['review_id']);
        $this->dropIndex('idx_orders_id', 'orders_products_reviews');
        $this->dropIndex('idx_orders_products_id', 'orders_products_reviews');
        $this->dropIndex('idx_products_id', 'orders_products_reviews');
        $this->dropIndex('idx_categories_id', 'orders_products_reviews');

        $this->createIndex('idx_remark_id', 'orders_products_reviews_remarks', ['remark_id']);
        $this->dropIndex('idx_review_id', 'orders_products_reviews_remarks');

        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210510_013334_orders_review_indexes cannot be reverted.\n";

        return false;
    }
    */
}
