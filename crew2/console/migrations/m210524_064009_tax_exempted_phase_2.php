<?php

use yii\db\Migration;

/**
 * Class m210524_064009_tax_exempted_phase_2
 */
class m210524_064009_tax_exempted_phase_2 extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $tables = $this->db->schema->getTableNames();
        $dbType = $this->db->driverName;

        if (!in_array('orders_tax_rules', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('orders_tax_rules', [
                    'tax_rule_id' => $this->primaryKey(),
                    'description' => $this->string(200),
                    'rule_type' => $this->string(3)->notNull(),
                    'status' => $this->tinyInteger()->comment("0 - disabled, 1 - enabled"),
                    'created_at' => $this->bigInteger()->unsigned(),
                    'created_by' => $this->string(255),
                    'updated_at' => $this->bigInteger()->unsigned(),
                    'updated_by' => $this->string(255),
                ], $tableOptions);
            }
        }

        if (!in_array('orders_tax_rule_mapping', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('orders_tax_rule_mapping', [
                    'tax_rule_mapping_id' => $this->primaryKey(),
                    'orders_tax_id' => $this->integer(11)->notNull()->unsigned(),
                    'tax_rule_id' => $this->integer()->notNull(),
                    'status' => $this->tinyInteger()->comment("0 - disabled, 1 - enabled"),
                    'sort_order' => $this->integer(),
                ], $tableOptions);
            }
            $this->createIndex('idx_orders_tax_id', 'orders_tax_rule_mapping', 'orders_tax_id');
            $this->addForeignKey('fk_tax_rule_map', 'orders_tax_rule_mapping', 'orders_tax_id', 'orders_tax_configuration', 'orders_tax_id', 'CASCADE', 'CASCADE');
            $this->addForeignKey('fk_tax_rule_id', 'orders_tax_rule_mapping', 'tax_rule_id', 'orders_tax_rules', 'tax_rule_id', 'CASCADE', 'CASCADE');
        }

        if (!in_array('orders_tax_rule_details', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('orders_tax_rule_details', [
                    'tax_rule_detail_id' => $this->primaryKey(),
                    'tax_rule_id' => $this->integer()->notNull(),
                    'fieldname' => $this->string(30)->notNull(),
                    'condition' => $this->string(20),
                    'values' => $this->string(),
                    'status' => $this->tinyInteger()->comment("0 - disabled, 1 - enabled"),
                    'created_at' => $this->bigInteger()->unsigned(),
                    'created_by' => $this->string(255),
                    'updated_at' => $this->bigInteger()->unsigned(),
                    'updated_by' => $this->string(255),
                ], $tableOptions);
            }
            $this->addForeignKey('fk_tax_rule_detail', 'orders_tax_rule_details', 'tax_rule_id', 'orders_tax_rules', 'tax_rule_id', 'CASCADE', 'CASCADE');
        }

        if (!in_array('products_tax_category', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('products_tax_category', [
                    'products_id' => $this->integer(11)->notNull(),
                    'category' => $this->string()->notNull(),
                    'products_value' => $this->decimal(19, 8)->comment("product FFV for MRV"),
                    'currency' => $this->char(3),
                    'created_at' => $this->bigInteger()->unsigned(),
                    'created_by' => $this->string(255),
                    'updated_at' => $this->bigInteger()->unsigned(),
                    'updated_by' => $this->string(255),
                ], $tableOptions);
                $this->addPrimaryKey('pk_product_tax_cat', 'products_tax_category', [
                    'products_id',
                    'category'
                ]);
            }
        }


        if (!in_array('products_tax_groups', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('products_tax_groups', [
                    'tax_group_id' => $this->primaryKey(),
                    'tax_group_name' => $this->string(),
                    'status' => $this->tinyInteger(),
                ], $tableOptions);
            }
        }

        if (!in_array('products_tax_group_mapping', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('products_tax_group_mapping', [
                    'tax_group_id' => $this->integer(),
                    'products_id' => $this->integer(),
                ], $tableOptions);
                $this->addPrimaryKey('pk_tax_grp_map', 'products_tax_group_mapping', ['tax_group_id', 'products_id']);
                $this->addForeignKey('fk_tax_group_id', 'products_tax_group_mapping', 'tax_group_id', 'products_tax_groups', 'tax_group_id', 'CASCADE', 'CASCADE');
            }
        }

        if (!in_array('orders_tax_history', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('orders_tax_history', [
                    'orders_id' => $this->integer(),
                    'orders_tax_id' => $this->integer(),
                    'tax_name' => $this->integer(),
                    'amount' => $this->decimal(19,8),
                    'tax_amount' => $this->decimal(19,8),
                ], $tableOptions);
            }
        }


        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->execute('SET foreign_key_checks = 0');
        $table = $this->db->schema->getTableSchema('orders_tax_configuration');
        (isset($table->columns['tax_item'])) ? $this->dropColumn('orders_tax_configuration', 'tax_item') : '';
        (isset($table->columns['tax_group_id'])) ? $this->dropColumn('orders_tax_configuration', 'tax_group_id') : '';

        $this->execute('DROP TABLE IF EXISTS `orders_tax_rule_mapping`');
        $this->execute('DROP TABLE IF EXISTS `orders_tax_rules`');
        $this->execute('DROP TABLE IF EXISTS `orders_tax_rule_details`');
        $this->execute('DROP TABLE IF EXISTS `products_tax_category`');
        $this->execute('DROP TABLE IF EXISTS `products_tax_groups`');
        $this->execute('DROP TABLE IF EXISTS `products_tax_group_mapping`');
        $this->execute('DROP TABLE IF EXISTS `products_tax_config`');
        $this->execute('DROP TABLE IF EXISTS `orders_tax_history`');
        $this->execute('SET foreign_key_checks = 1;');
        return true;
    }


    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210524_064009_tax_exempted_phase_2 cannot be reverted.\n";

        return false;
    }
    */
}
