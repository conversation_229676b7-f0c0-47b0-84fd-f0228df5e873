<?php

use yii\db\Migration;

/**
 * Class m210712_013257_checkout_transaction
 */
class m210712_013257_checkout_transaction extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

            $this->createTable('checkout_transaction', [
                'id' => $this->bigPrimaryKey(),
                'merchant_id' => $this->integer()->unsigned()->notNull(),
                'customers_id' => $this->integer()->unsigned()->notNull(),
                'orders_id' => $this->integer()->unsigned()->notNull(),
                'access_code' => $this->string()->notNull(),
                'expiry' => $this->integer()->unsigned()->notNull(),
                'transaction_detail' => $this->text()->notNull(),
                'created_at' => $this->integer()->unsigned()->notNull(),
                'updated_at' => $this->integer()->unsigned()->notNull(),
            ], $tableOptions);

            $this->createIndex('idx_merchant_id','checkout_transaction','merchant_id');
            $this->createIndex('idx_customers_id','checkout_transaction','customers_id');
            $this->createIndex('idx_access_code','checkout_transaction','access_code');
            $this->createIndex('idx_orders_id','checkout_transaction','orders_id');
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('checkout_transaction');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m210712_013257_checkout_transaction cannot be reverted.\n";

        return false;
    }
    */
}
