<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%log_ses_bounce_history}}`.
 */
class m210928_060750_create_log_ses_bounce_history_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {

        $this->createTable('log_ses_bounce_history', [
            'id'              => $this->bigPrimaryKey(),
            'email'           => $this->string(200)->defaultValue(""),
            'bounce_type'     => $this->string(200)->defaultValue(''),
            'bounce_sub_type' => $this->string(200)->defaultValue(''),
            'error_string'    => $this->text()->defaultValue('')->notNull(),
            'created_at'      => $this->dateTime()->unsigned()->notNull(),
            'updated_at'      => $this->dateTime()->unsigned()->notNull(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('log_ses_bounce_history');
    }
}
