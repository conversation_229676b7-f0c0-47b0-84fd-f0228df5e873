<?php

use yii\db\Migration;
use yii\db\Schema;

/**
 * Class m211022_093500_tax_permission
 */
class m211022_093500_tax_permission extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add availble routes
        try {
            $routeData = $this->_getRouteData();

            $this->_updateAuthItemTable($routeData, '2');

            foreach ($routeData as $key => $value) {
                $this->_updateAuthItemChildTable('Superadmin', $value);
            }
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $routeData = $this->_getRouteData();
        $this->_deleteAuthItemTable($routeData, '2');
        foreach ($routeData as $key => $value) {
            $this->_deleteAuthItemChildTable('Superadmin', $value);
        }
    }

    private function _getRouteData()
    {
        return [
            '/tax/verify',
            '/tax/fail',
        ];
    }

    private function _updateAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $dataInsert = [
                'name' => $value,
                'type' => $type,
                'description' => null,
                'rule_name' => null,
                'data' => null,
                'created_at' => time(),
                'updated_at' => time()
            ];
            $this->insert('{{%auth_item}}', $dataInsert);
        }
    }

    private function _deleteAuthItemTable($items, $type)
    {
        foreach ($items as $key => $value) {
            $this->delete('{{%auth_item}}', ['name' => $value]);
        }
    }

    private function _updateAuthItemChildTable($parent, $child)
    {
        $dataInsert = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->insert('{{%auth_item_child}}', $dataInsert);
    }

    private function _deleteAuthItemChildTable($parent, $child)
    {
        $dataDelete = [
            'parent' => $parent,
            'child' => $child,
        ];
        $this->delete('{{%auth_item_child}}', $dataDelete);
    }
}
