<?php

use yii\db\Migration;

/**
 * Class m211107_232210_order_delivery_address
 */
class m211107_232210_order_delivery_address extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = null;

        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('orders_delivery_address', [
            'orders_products_id' => $this->primaryKey(),
            'recipient_name' => $this->string(255),
            'contact_number' => $this->string(255),
            'addr_1' => $this->string(255),
            'addr_2' => $this->string(255),
            'city' => $this->string(255),
            'country_name' => $this->string(255),
            'state' => $this->string(255),
            'postcode' => $this->string(255),
        ], $tableOptions);


        $this->createIndex('idx_op_id', 'orders_delivery_address', ['orders_products_id'], true);

        return true;

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('orders_delivery_address');

        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m211107_232210_order_delivery_address cannot be reverted.\n";

        return false;
    }
    */
}
