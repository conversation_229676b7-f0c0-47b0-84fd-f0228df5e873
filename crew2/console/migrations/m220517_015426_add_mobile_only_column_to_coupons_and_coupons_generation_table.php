<?php

use yii\db\Migration;

/**
 * Handles adding columns to table `{{%coupons_and_coupons_generation}}`.
 */
class m220517_015426_add_mobile_only_column_to_coupons_and_coupons_generation_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn(
            '{{%coupons}}',
            'mobile_only',
            $this->tinyInteger(1)
                ->notNull()
                ->defaultValue(0)
                ->after('coupon_active'));

        $this->addColumn(
            '{{%coupons_generation}}',
            'mobile_only',
            $this->tinyInteger(1)
                ->notNull()
                ->defaultValue(0)
                ->after('requester_id'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%coupons}}', 'mobile_only');
        $this->dropColumn('{{%coupons_generation}}', 'mobile_only');
    }
}
