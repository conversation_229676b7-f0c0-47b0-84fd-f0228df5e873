<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%seo_url_redirect}}`.
 */
class m220519_004858_create_seo_url_redirect_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%seo_url_redirect}}', [
            'seo_url_redirect_id' => $this->primaryKey(),
            'old_url' => $this->string(1000)->notNull()->unique(),
            'new_url' => $this->string(1000)->notNull(),
            'redirect_type' => $this->integer(3)->notNull()->defaultValue(301),
            'created_at' => $this->integer()->notNull()->unsigned(),
            'updated_at' => $this->integer()->notNull()->unsigned(),
        ], 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%seo_url_redirect}}');
    }
}
