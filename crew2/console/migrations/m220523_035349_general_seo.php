<?php

use yii\db\Migration;

/**
 * Class m220523_035349_general_seo
 */
class m220523_035349_general_seo extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $new_types = [
            [8, 0],
            [9, 0],
            [10, 0],
            [11, 0],
        ];

        Yii::$app->db->createCommand()->batchInsert('custom_seo', ['type', 'reference_data_id'], $new_types)->execute();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $new_types = [8, 9, 10, 11];

        $custom_seo_ids = (new \yii\db\Query())->select(['custom_seo_id'])->from('custom_seo')
            ->where([
                'IN', 'type', $new_types
            ])->all();
        
        \Yii::$app->db->createCommand()->delete('custom_seo_translation', ['custom_seo_id' => $custom_seo_ids])->execute();

        \Yii::$app->db->createCommand()->delete('custom_seo', ['custom_seo_id' => $custom_seo_ids])->execute();
    }
}
