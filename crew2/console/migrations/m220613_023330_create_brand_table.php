<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%brand}}`.
 */
class m220613_023330_create_brand_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        // brand
        $this->createTable('{{%brand}}', [
            'brand_id' => $this->primaryKey(11)->unsigned(),
            'seo_url' => $this->string(255)->unique()->notNull(),
            'image_url' => $this->string(1024)->notNull(),
            'show_in_search_result' => $this->tinyInteger(1)->unsigned()->notNull()->defaultValue(0),
            'search_keyword' => $this->text()->notNull(),
            'status' => $this->tinyInteger(1)->unsigned()->notNull()->defaultValue(0),
            'sort_order' => $this->integer(5)->unsigned()->notNull()->defaultValue(0),
        ], $tableOptions);

        // brand_description
        $this->createTable('{{%brand_description}}', [
            'brand_description_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'language_id' => $this->tinyInteger(1)->unsigned()->notNull(),
            'name' => $this->string(255)->notNull(),
            'notice' => $this->text()->notNull(),
            'short_description' => $this->text()->notNull(),
            'description' => $this->text()->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_description-brand_id',
            'brand_description',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-brand_description-brand_id-language_id',
            'brand_description',
            ['brand_id', 'language_id'],
            true
        );

        $this->createIndex(
            'idx-brand_description-name',
            'brand_description',
            ['name'],
            false
        );

        // brand_metadata
        $this->createTable('{{%brand_metadata}}', [
            'brand_metadata_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'language_id' => $this->tinyInteger(1)->unsigned()->notNull(),
            'meta_title' => $this->string(255)->notNull(),
            'meta_keyword' => $this->text()->notNull(),
            'meta_description' => $this->text()->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_metadata-brand_id',
            'brand_metadata',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-brand_metadata-brand_id-language_id',
            'brand_metadata',
            ['brand_id', 'language_id'],
            true
        );

        // brand_game_info
        $this->createTable('{{%brand_game_info}}', [
            'brand_game_info_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'key' => $this->string(255)->notNull(),
            'value' => $this->string(1024)->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_game_info-brand_id',
            'brand_game_info',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-brand_game_info-brand_id-key',
            'brand_game_info',
            ['brand_id', 'key']
        );

        // brand_category
        $this->createTable('{{%brand_category}}', [
            'brand_category_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'type' => $this->tinyInteger(1)->unsigned()->notNull()->comment("1=brand,2=category"),
            'sub_id' => $this->integer(11)->unsigned()->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_category-brand_id',
            'brand_category',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        // brand_soft_block
        $this->createTable('{{%brand_soft_block}}', [
            'brand_soft_block_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'country_code' => $this->char(2)->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_soft_block-brand_id',
            'brand_soft_block',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-brand_soft_block-brand_id-country_code',
            'brand_soft_block',
            ['brand_id', 'country_code'],
            true
        );

        // brand_hard_block
        $this->createTable('{{%brand_hard_block}}', [
            'brand_hard_block_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'country_code' => $this->char(2)->notNull(),
        ], $tableOptions);

        $this->addForeignKey(
            'fk-brand_hard_block-brand_id',
            'brand_hard_block',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-brand_hard_block-brand_id-country_code',
            'brand_hard_block',
            ['brand_id', 'country_code'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%brand_description}}');
        $this->dropTable('{{%brand_metadata}}');
        $this->dropTable('{{%brand_game_info}}');
        $this->dropTable('{{%brand_category}}');
        $this->dropTable('{{%brand_soft_block}}');
        $this->dropTable('{{%brand_hard_block}}');
        $this->dropTable('{{%brand}}');
    }
}
