<?php

use yii\db\Migration;

/**
 * Class m220622_042256_create_category_promotion
 */
class m220622_042256_create_category_promotion extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%category}}', [
            'category_id' => $this->primaryKey(11)->unsigned(),
            'categories_id' => $this->integer(11)->unsigned()->notNull(),
            'brand_id' => $this->integer(11)->unsigned(),
            'seo_url' => $this->string(255)->notNull(),
            'image_url' => $this->string(1024)->notNull()->defaultValue(''),
            'sort_order' => $this->smallInteger(5)->unsigned()->notNull()->defaultValue(0),
            'status' => $this->tinyInteger(1)->unsigned()->notNull()->defaultValue(0)
        ], $tableOptions);
        $this->addForeignKey('fk-category-brand', 'category',
            'brand_id', 'brand', 'brand_id', 'CASCADE');
        $this->createIndex('idx-category-categories_id', 'category', ['categories_id']);
        $this->createIndex('uk-category-seo_url', 'category', ['seo_url'], true);

        $this->createTable('{{%category_metadata}}', [
            'category_metadata_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->defaultValue(null),
            'language_id' => $this->integer(11)->unsigned()->notNull()->defaultValue(0),
            'title' => $this->string(255)->notNull()->defaultValue(''),
            'keyword' => $this->text()->notNull()->defaultValue(''),
            'description' => $this->text()->notNull()->defaultValue(''),
        ], $tableOptions);
        $this->addForeignKey('fk-category_metadata-category', 'category_metadata',
            'category_id', 'category', 'category_id', 'CASCADE');

        $this->createTable('{{%category_description}}', [
            'category_description_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'language_id' => $this->tinyInteger(1)->unsigned()->notNull(),
            'name' => $this->string(255)->notNull(),
            'notice' => $this->text()->notNull()->defaultValue(''),
            'short_description' => $this->text()->notNull()->defaultValue(''),
            'description' => $this->text()->notNull()->defaultValue('')
        ], $tableOptions);
        $this->addForeignKey('fk-category_description-category', 'category_description',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-category_description-category_id-language_id', 'category_description', ['category_id', 'language_id'], true);

        $this->createTable('{{%category_hard_block}}', [
            'category_hard_block_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'country_code' => $this->char(2)->notNull()
        ], $tableOptions);
        $this->addForeignKey('fk-chb-category', 'category_hard_block',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-chb-category_id-country_code', 'category_hard_block', ['category_id', 'country_code'], true);

        $this->createTable('{{%category_soft_block}}', [
            'category_soft_block_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'country_code' => $this->char(2)->notNull()
        ], $tableOptions);
        $this->addForeignKey('fk-csb-category', 'category_soft_block',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-csb-category_id-country_code', 'category_soft_block', ['category_id', 'country_code'], true);

        $this->createTable('{{%category_customer_group_restriction}}', [
            'category_customer_group_restriction_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'customers_group_id' => $this->integer(11)->unsigned()->notNull()
        ], $tableOptions);
        $this->addForeignKey('fk-ccgr-category', 'category_customer_group_restriction',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-ccgr-category_id-customers_group_id', 'category_customer_group_restriction', ['category_id', 'customers_group_id'], true);

        $this->createTable('{{%category_payment_type}}', [
            'category_payment_type_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'type' => $this->tinyInteger(1)->unsigned()->notNull()
        ], $tableOptions);
        $this->addForeignKey('fk-cpt-category', 'category_payment_type',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-cpt-category_id-type', 'category_payment_type', ['category_id', 'type'], true);

        $this->createTable('{{%category_payment_restriction}}', [
            'category_payment_restriction_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'payment_methods_id' => $this->integer(11)->unsigned()->notNull()
        ], $tableOptions);
        $this->addForeignKey('fk-cpr-category', 'category_payment_restriction',
            'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-cpr-category_id-payment_methods_id', 'category_payment_restriction', ['category_id', 'payment_methods_id'], true);

        $this->createTable('{{%category_promotion}}', [
            'category_promotion_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->defaultValue(null),
            'start_date' => $this->dateTime()->notNull(),
            'end_date' => $this->dateTime()->notNull(),
            'unlimited_purchase_per_user' => $this->tinyInteger(1)->unsigned()->notNull()->defaultValue(0),
            'max_purchase_per_user' => $this->smallInteger(5)->unsigned()->notNull()->defaultValue(0),
            'total_promo_quantity' => $this->integer(7)->unsigned()->notNull()->defaultValue(0)
        ], $tableOptions);
        $this->addForeignKey('fk-category_promotion-category', 'category_promotion',
            'category_id', 'category', 'category_id', 'CASCADE');

        $this->createTable('{{%category_promotion_customer_group}}', [
            'category_promotion_customer_group_id' => $this->primaryKey(11)->unsigned(),
            'category_promotion_id' => $this->integer(11)->unsigned()->defaultValue(null),
            'customers_group_id' => $this->integer(11)->unsigned()->notNull(),
            'promo_quantity' => $this->smallInteger(5)->unsigned()->notNull()->defaultValue(0),
            'discount_percentage' => $this->decimal(6,2)->notNull()->defaultValue(0),
            'op_reward' => $this->decimal(15,4)->notNull()->defaultValue(0)
        ], $tableOptions);
        $this->addForeignKey('fk-cpcg-category_promotion', 'category_promotion_customer_group', 'category_promotion_id', 'category_promotion', 'category_promotion_id', 'CASCADE');
        $this->createIndex('idx-category_promotion_customer_group-customers_group_id', 'category_promotion_customer_group', ['customers_group_id']);

        $this->createTable('{{%category_group_purchase_limit}}', [
            'category_group_purchase_limit_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->notNull(),
            'customers_group_id' => $this->integer(11)->unsigned()->notNull(),
            'amount' => $this->decimal(15,4)->notNull()->defaultValue(0),
            'x_minute' => $this->smallInteger(5)->notNull()->defaultValue(0)
        ], $tableOptions);
        $this->addForeignKey('fk-cpcg-category', 'category_group_purchase_limit', 'category_id', 'category', 'category_id', 'CASCADE');

        $this->createTable('{{%category_discount_list}}', [
            'category_discount_list_id' => $this->primaryKey(11)->unsigned(),
            'category_id' => $this->integer(11)->unsigned()->defaultValue(null),
            'cdrules_id' => $this->integer(11)->unsigned()->notNull(),
        ], $tableOptions);
        $this->addForeignKey('fk-cdl-category', 'category_discount_list', 'category_id', 'category', 'category_id', 'CASCADE');
        $this->createIndex('uk-cdl-category_id-cdrules_id', 'category_discount_list', ['category_id', 'cdrules_id'], true);

        $this->createTable('{{%categories_brand_list}}', [
            'categories_brand_list_id' => $this->primaryKey(11)->unsigned(),
            'categories_id' => $this->integer(11)->unsigned()->notNull(),
            'brand_id' => $this->integer(11)->unsigned()->defaultValue(null),
        ], $tableOptions);
        $this->addForeignKey('fk-cbl-brand', 'categories_brand_list', 'brand_id', 'brand', 'brand_id', 'CASCADE');
        $this->createIndex('uk-categories_brand_list-categories_id', 'categories_brand_list', ['categories_id'], true);
        $this->createIndex('uk-categories_brand_list-brand_id', 'categories_brand_list', ['brand_id'], true);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%categories_brand_list}}');
        $this->dropTable('{{%category_promotion_customer_group}}');
        $this->dropTable('{{%category_hard_block}}');
        $this->dropTable('{{%category_soft_block}}');
        $this->dropTable('{{%category_customer_group_restriction}}');
        $this->dropTable('{{%category_payment_type}}');
        $this->dropTable('{{%category_payment_restriction}}');
        $this->dropTable('{{%category_group_purchase_limit}}');
        $this->dropTable('{{%category_description}}');
        $this->dropTable('{{%category_metadata}}');
        $this->dropTable('{{%category_discount_list}}');
        $this->dropTable('{{%category_promotion}}');
        $this->dropTable('{{%category}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m220622_042256_create_category_promotion cannot be reverted.\n";

        return false;
    }
    */
}
