<?php

use console\models\GameBlog;
use yii\{db\Connection, db\Migration, db\Query, helpers\ArrayHelper, helpers\Json};

/**
 * Class m220627_001328_migrate_game_blog_data_to_brand
 */
class m220627_001328_migrate_game_blog_data_to_brand extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $datas = $this->getDataToMigrate();

        foreach ($datas as $gameBlogId => $data) {
            $transaction = $this->getDb()->beginTransaction();
            try {
                $brandId = $this->saveBrandAndGetBrandId($data);

                if (isset($data['brand_description'])) {
                    $this->saveToBrandDescription($data['brand_description'], $brandId);
                }

                if (isset($data['brand_game_info'])) {
                    $this->saveToBrandGameInfo($data['brand_game_info'], $brandId);
                }

                if (isset($data['brand_category'])) {
                    $this->saveToBrandCategory($data['brand_category'], $brandId);
                }

                if (isset($data['brand_metadata'])) {
                    $this->saveToBrandMetadata($data['brand_metadata'], $brandId);
                }

                $transaction->commit();

            } catch (Exception $exception) {
                $transaction->rollBack();
                echo "Exception at adding brand data for GameBlog ID [$gameBlogId] due : "
                    . $exception->getMessage() . "\n";
                continue;
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('{{%brand}}');
        $this->delete('{{%brand_description}}');
        $this->delete('{{%brand_metadata}}');
        $this->delete('{{%brand_game_info}}');
        $this->delete('{{%brand_category}}');
        $this->execute('ALTER TABLE brand AUTO_INCREMENT = 1');
    }

    private function getDataToMigrate(): array
    {
        $gameBlogs = $this->getGameBlogs();
        $brandGameInfos = $this->getFrontendTemplateInfo();
        $brandNoticesAndDescriptions = $this->getNoticeAndDescription();
        $brandCategories = $this->getBrandCategoryAssociations();
        $brandMetaDatas = $this->getBrandMetadatas(array_keys($gameBlogs));

        $results = [];

        foreach ($gameBlogs as $gameBlogId => $data) {
            $results[$gameBlogId] = $data;

            if (isset($brandGameInfos[$gameBlogId])) {
                $results[$gameBlogId] = array_merge($results[$gameBlogId], $brandGameInfos[$gameBlogId]);
            }

            if (isset($brandNoticesAndDescriptions[$gameBlogId])) {
                $this->loadBrandDescription($brandNoticesAndDescriptions[$gameBlogId], $results, $gameBlogId);
            } else {
                $this->loadBrandDescription($data['brand_description'], $results, $gameBlogId);
            }

            if (isset($brandCategories[$gameBlogId])) {
                $results[$gameBlogId]['brand_category'] = $brandCategories[$gameBlogId];
            }

            if (isset($brandMetaDatas[$gameBlogId])) {
                $results[$gameBlogId]['brand_metadata'] = $brandMetaDatas[$gameBlogId];
            }
        }

        return $results;
    }

    private function getGameBlogs(): array
    {
        $resultGameBlog = $this->getQuery()
            ->select([
                'gb.game_blog_id',
                'gb.custom_url',
                'gb.sort_order',
                'gbd.language_id',
                'gbd.game_blog_description',
            ])
            ->from('game_blog gb')
            ->innerJoin('game_blog_description gbd', 'gb.game_blog_id = gbd.game_blog_id')
            ->where(['<>', 'gbd.language_id', 3])
            ->all($this->getOffgamersDB());

        $gameBlogs = [];
        foreach ($resultGameBlog as $gameBlog) {
            $gameBlogId = $gameBlog['game_blog_id'];
            $gameBlogs[$gameBlogId]['seo_url'] = $gameBlog['custom_url'] ?? '';
            $gameBlogs[$gameBlogId]['sort_order'] = $gameBlog['sort_order'] ?? '';
            $gameBlogs[$gameBlogId]['show_in_search_result'] = 1;
            $gameBlogs[$gameBlogId]['status'] = 1;
            $gameBlogs[$gameBlogId]['brand_description'][$gameBlog['language_id']]['name']
                = $gameBlog['game_blog_description'] ?? '';
        }

        unset($resultGameBlog);

        return $gameBlogs;
    }

    private function getFrontendTemplateInfo(): array
    {
        $results = $this->getQuery()
            ->select([
                'ft.id',
                'ft.tpl_id',
                'ft.logo_source',
                'ftl.game_keyword',
                'ftl.game_publisher',
                'ftl.game_developer',
                'ftl.game_release_date',
                'ftl.related_link_info',
            ])
            ->from('frontend_template ft')
            ->innerJoin('frontend_template_lang ftl', 'ft.tpl_id = ftl.tpl_id')
            ->where([
                'ftl.language_id' => 1,
                'ft.id_type' => 2,
            ])
            ->all($this->getOffgamersDB());

        $frontEndTemplateInfos = [];
        foreach ($results as $result) {
            $gameBlogId = $result['id'];
            $frontEndTemplateInfos[$gameBlogId]['image_url'] = $result['logo_source'] ?? '';
            $frontEndTemplateInfos[$gameBlogId]['search_keyword'] = $result['game_keyword'] ?? '';

            if (isset($result['game_publisher'])) {
                $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['publisher'] = [
                    'key' => 'publisher',
                    'value' => $result['game_publisher'],
                ];
            }

            if (isset($result['game_developer'])) {
                $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['developer'] = [
                    'key' => 'developer',
                    'value' => $result['game_developer'],
                ];
            }

            if (isset($result['game_release_date'])) {
                $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['release_date'] = [
                    'key' => 'release_date',
                    'value' => $result['game_release_date'],
                ];
            }

            if (isset($result['related_link_info'])) {
                $relatedLinks = json_decode($result['related_link_info'], true);

                $relatedLinks = is_array($relatedLinks) ? $relatedLinks : [];

                foreach ($relatedLinks as $link) {
                    $link = [
                        'label' => $link['label'] ?? '',
                        'url' => $link['link'] ?? ''
                    ];
                    $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['related_link'][] = [
                        'key' => 'related_link',
                        'value' => Json::encode($link),
                    ];
                }
            }

            $tplId = $result['tpl_id'];

            $gameLanguages = $this->getGameInfoByType($tplId, 'game_language');
            $gamePlatforms = $this->getGameInfoByType($tplId, 'game_platform');
            $gameRegions = $this->getGameInfoByType($tplId, 'game_region');
            $gameGenres = $this->getGameInfoByType($tplId, 'game_genre');

            $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['game_language'] = $gameLanguages;
            $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['game_platform'] = $gamePlatforms;
            $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['game_region'] = $gameRegions;
            $frontEndTemplateInfos[$gameBlogId]['brand_game_info']['game_genre'] = $gameGenres;
        }

        unset($results);

        return $frontEndTemplateInfos;
    }

    private function getNoticeAndDescription(): array
    {
        $results = $this->getQuery()
            ->select([
                'ft.id',
                'ftl.language_id',
                'ftl.notice',
                'ftl.description'
            ])
            ->from('frontend_template ft')
            ->innerJoin('frontend_template_lang ftl', 'ft.tpl_id = ftl.tpl_id')
            ->where(['id_type' => 2])
            ->AndWhere(['<>', 'ftl.language_id', 3])
            ->all($this->getOffgamersDB());

        $mappedResults = ArrayHelper::index($results, null, 'id');

        $noticesAndDescriptions = [];
        foreach ($mappedResults as $gameBlogId => $datas) {
            foreach ($datas as $data) {
                $noticesAndDescriptions[$gameBlogId][$data['language_id']] = $data;
            }
        }

        unset($results);
        unset($mappedResults);

        return $noticesAndDescriptions;
    }

    private function getBrandCategoryAssociations(): array
    {
        $results = $this->getQuery()
            ->select([
                'game_blog_id',
                'categories_id'
            ])
            ->from('game_blog_categories')
            ->all($this->getOffgamersDB());

        $mappedResults = ArrayHelper::index($results, null, 'game_blog_id');

        $brandCategories = [];
        foreach ($mappedResults as $gameBlogId => $associations) {
            foreach ($associations as $index => $association) {
                $brandCategories[$gameBlogId][$index]['type'] = 2;
                $brandCategories[$gameBlogId][$index]['sub_id'] = $association['categories_id'];
            }
        }

        unset($results);
        unset($mappedResults);

        return $brandCategories;
    }

    private function getBrandMetadatas(array $gameBlogIds): array
    {
        $results = $this->getQuery()
            ->select([
                'cs.reference_data_id',
                'cst.language_id',
                'cst.meta_title',
                'cst.meta_keyword',
                'cst.meta_description',
            ])
            ->from('custom_seo cs')
            ->innerJoin('custom_seo_translation cst', 'cs.custom_seo_id = cst.custom_seo_id')
            ->where([
                'cs.type' => 7,
            ])
            ->all(Yii::$app->db);

        $mappedResults = ArrayHelper::index($results, null, 'reference_data_id');

        $brandMetaDatas = [];
        foreach ($mappedResults as $gameBlogId => $datas) {
            foreach ($datas as $data) {
                $languageId = $data['language_id'];
                $brandMetaDatas[$gameBlogId][$languageId]['language_id'] = $languageId;
                $brandMetaDatas[$gameBlogId][$languageId]['meta_title'] = $data['meta_title'] ?? '';
                $brandMetaDatas[$gameBlogId][$languageId]['meta_keyword'] = $data['meta_keyword'] ?? '';
                $brandMetaDatas[$gameBlogId][$languageId]['meta_description'] = $data['meta_description'] ?? '';
            }
        }

        $enum = Yii::$app->enum;
        $languageIds = array_keys($enum->getLanguage('listData'));

        $gameBlogIdsToAddMetadata = array_values(array_diff($gameBlogIds, array_keys($brandMetaDatas)));

        foreach ($gameBlogIdsToAddMetadata as $gameBlogId) {
            $defaultMetaTitles = GameBlog::getDefaultMetaDataTitles();
            $defaultMetaKeywords = ArrayHelper::map(
                GameBlog::getMetaKeyword($gameBlogId),
                'language_id',
                'search_value'
            );
            $defaultMetaDescriptions = ArrayHelper::map(
                GameBlog::getMetaDescription($gameBlogId),
                'language_id',
                'description'
            );

            foreach ($languageIds as $languageId) {
                $brandMetaDatas[$gameBlogId][$languageId]['language_id'] = $languageId;
                $brandMetaDatas[$gameBlogId][$languageId]['meta_title'] = $defaultMetaTitles[$languageId] ?? '';
                $brandMetaDatas[$gameBlogId][$languageId]['meta_keyword'] = $defaultMetaKeywords[$languageId] ?? '';
                $brandMetaDatas[$gameBlogId][$languageId]['meta_description'] = $defaultMetaDescriptions[$languageId]
                    ?? '';
            }
        }

        unset($results);
        unset($mappedResults);
        unset($gameBlogIdsToAddMetadata);

        ksort($brandMetaDatas);

        return $brandMetaDatas;
    }

    private function saveBrandAndGetBrandId(array $data): int
    {
        $brand = [
            'seo_url' => $data['seo_url'] ?? '',
            'show_in_search_result' => $data['show_in_search_result'] ?? 1,
            'search_keyword' => $data['search_keyword'] ?? '',
            'status' => $data['status'] ?? 1,
            'sort_order' => $data['sort_order'] ?? 0,
        ];

        if (isset($data['image_url'])) {
            $brand['image_url'] = $data['image_url'];
        }

        $this->insert('{{%brand}}', $brand);

        return Yii::$app->db_og->getLastInsertID();
    }

    private function saveToBrandDescription(array $brandDescriptions, int $brandId): void
    {
        foreach ($brandDescriptions as $description) {
            $description['brand_id'] = $brandId;
            $this->insert('{{%brand_description}}', $description);
        }
    }

    private function saveToBrandMetadata(array $brandMetadatas, int $brandId): void
    {
        foreach ($brandMetadatas as $metadata) {
            $metadata['brand_id'] = $brandId;
            $this->insert('{{%brand_metadata}}', $metadata);
        }
    }

    private function saveToBrandGameInfo(array $brandGameInfos, int $brandId): void
    {
        foreach ($brandGameInfos as $key => $gameInfo) {
            if ($key == 'publisher' || $key == 'developer' || $key == 'release_date') {
                $gameInfo['brand_id'] = $brandId;
                $this->insert('{{%brand_game_info}}', $gameInfo);
            }

            if ($key == 'game_region' || $key == 'game_language' || $key == 'game_platform' || $key == 'game_genre') {
                foreach ($gameInfo as $info) {
                    $info['brand_id'] = $brandId;
                    $this->insert('{{%brand_game_info}}', $info);
                }
            }

            if ($key == 'related_link') {
                foreach ($gameInfo as $link) {
                    $link['brand_id'] = $brandId;
                    $this->insert('{{%brand_game_info}}', $link);
                }
            }
        }
    }

    private function saveToBrandCategory(array $brandCategories, int $brandId): void
    {
        foreach ($brandCategories as $category) {
            $category['brand_id'] = $brandId;
            $this->insert('{{%brand_category}}', $category);
        }
    }

    private function loadBrandDescription(array $brandDescriptions, array &$output, int $id): void
    {
        foreach ($brandDescriptions as $languageId => $description) {
            $output[$id]['brand_description'][$languageId]['language_id'] = $languageId;
            $output[$id]['brand_description'][$languageId]['notice'] = $description['notice'] ?? '';
            $output[$id]['brand_description'][$languageId]['short_description']
                = $description['short_description'] ?? '';
            $output[$id]['brand_description'][$languageId]['description'] = $description['description'] ?? '';
        }
    }

    private function getGameInfoByType(int $tplId, string $gameInfoType): array
    {
        $results = $this->getQuery()
            ->select([
                'ft.tpl_id',
                'gi.game_info_type',
                'GROUP_CONCAT(`game_info_id`) AS "ids"'
            ])
            ->from('frontend_template ft')
            ->innerJoin('frontend_template_to_game_info gi', 'ft.tpl_id = gi.tpl_id')
            ->where([
                'gi.tpl_id' => $tplId,
                'gi.game_info_type' => $gameInfoType,
            ])
            ->groupBy(['gi.tpl_id'])
            ->all($this->getOffgamersDB());

        $results = array_shift($results);

        if (empty($results)) {
            return [];
        }

        $ids = explode(',', $results['ids']);

        $gameInfos = [];
        foreach ($ids as $id) {
            $gameInfos[] = [
                'key' => $gameInfoType,
                'value' => $id,
            ];
        }

        unset($results);
        unset($ids);

        return $gameInfos;
    }

    private function getQuery(): Query
    {
        return new Query();
    }

    private function getOffgamersDB(): Connection
    {
        return Yii::$app->db_offgamers;
    }
}
