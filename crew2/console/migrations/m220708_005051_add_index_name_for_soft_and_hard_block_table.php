<?php

use yii\db\Migration;

/**
 * Class m220708_005051_add_index_name_for_soft_and_hard_block_table
 */
class m220708_005051_add_index_name_for_soft_and_hard_block_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            'idx-brand_soft_block-country_code',
            'brand_soft_block',
            ['country_code']
        );

        $this->createIndex(
            'idx-brand_hard_block-country_code',
            'brand_hard_block',
            ['country_code']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-brand_soft_block-country_code', 'brand_soft_block');
        $this->dropIndex('idx-brand_hard_block-country_code', 'brand_hard_block');
    }
}
