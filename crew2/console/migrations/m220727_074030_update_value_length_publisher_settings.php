<?php

use yii\db\Migration;

/**
 * Class m220727_074030_update_key_length_publisher_settings
 */
class m220727_074030_update_value_length_publisher_settings extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('publisher_setting','value',$this->string(2048)->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('publisher_setting','value',$this->string(255)->notNull());
    }
}
