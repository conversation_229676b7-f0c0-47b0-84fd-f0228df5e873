<?php

use yii\db\Migration;

/**
 * Class m220801_013238_create_best_selling
 */
class m220801_013238_create_best_selling extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%best_selling}}', [
            'best_selling_id' => $this->primaryKey(11)->unsigned(),
            'country_code' => $this->char(2)->notNull(),
            'info' => $this->text()->notNull()->defaultValue(""),
        ], $tableOptions);

        $this->createIndex(
            'uk-best_selling-country_code',
            'best_selling',
            ['country_code'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%best_selling}}');

        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m220801_013238_create_best_selling_ cannot be reverted.\n";

        return false;
    }
    */
}
