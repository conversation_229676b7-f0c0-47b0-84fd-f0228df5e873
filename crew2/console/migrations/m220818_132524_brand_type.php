<?php

use yii\db\Migration;

/**
 * Class m220818_132524_brand_type
 */
class m220818_132524_brand_type extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%brand_type}}', [
            'brand_type_id' => $this->primaryKey(11)->unsigned(),
            'brand_id' => $this->integer(11)->unsigned()->notNull(),
            'type' => $this->tinyInteger(1)->unsigned()->notNull()->comment("1=voucher,2=dtu,3=game_key,4=mobile_recharge,5=physical_goods"),
        ], $tableOptions);


        $this->addForeignKey(
            'fk-brand_type-brand_id',
            'brand_type',
            'brand_id',
            'brand',
            'brand_id',
            'CASCADE'
        );

        $this->createIndex(
            'uk-brand_type-type',
            'brand_type',
            ['brand_id','type'],
            true
        );

        $this->createTable('{{%algolia}}', [
            'algolia_id' => $this->primaryKey(11)->unsigned(),
            'object_id' => $this->string(255)->notNull(),
            'hash' => $this->string(255)->notNull(),
        ], $tableOptions);

        $this->createIndex(
            'uk-algolia-object_id',
            'algolia',
            ['object_id'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%brand_type}}');
        $this->dropTable('{{%algolia}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m220818_132524_brand_type cannot be reverted.\n";

        return false;
    }
    */
}
