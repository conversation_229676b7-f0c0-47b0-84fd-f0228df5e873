<?php

use yii\db\Migration;

/**
 * Class m221214_003317_seo_permission_control
 */
class m221214_003317_seo_permission_control extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $auth = Yii::$app->authManager;

        $permission_list = [
            '[Product] Edit Brand' => 'Permission to batch update game product detail',
            '[Product] Edit Category' => 'Permission to update products price',
            '[SEO] Brand' => 'Permission to update products status',
            '[SEO] Category' => 'Permission to update products status'
        ];

        foreach ($permission_list as $name => $description) {
            $newPermission = $auth->createPermission($name);
            $newPermission->description = $description;
            $auth->add($newPermission);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $auth = Yii::$app->authManager;

        $permission_list = [
            '[Product] Edit Brand' => 'Permission to batch update game product detail',
            '[Product] Edit Category' => 'Permission to update products price',
            '[SEO] Brand' => 'Permission to update products status',
            '[SEO] Category' => 'Permission to update products status'
        ];

        foreach ($permission_list as $name => $description) {
            $newPermission = $auth->getPermission($name);
            $auth->remove($newPermission);
        }
    }
}
