<?php

use yii\db\Migration;

/**
 * Class m230314_133746_publisher_product_listing
 */
class m230314_133746_publisher_product_listing extends Migration
{
    /**
     * {@inheritdoc}
     */

    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%publisher_product}}', [
            'publisher_product_id' => $this->bigPrimaryKey(),
            'publisher_id' => $this->integer()->notNull(),
            'name' => $this->string()->notNull(),
            'description' => $this->text()->notNull(),
            'cost_price' => $this->decimal(18,9),
            'cost_currency' => $this->char(3)->notNull(),
            'sku' => $this->string()->notNull(),
            'denomination' => $this->string()->notNull(),
            'attribute_1' => $this->string()->notNull(),
            'attribute_2' => $this->string()->notNull(),
            'attribute_3' => $this->string()->notNull(),
            'raw' => $this->text()->notNull(),
            'status' => $this->tinyInteger()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger()->notNull(),
        ], $tableOptions);

        $this->createIndex('index_publisher_id_sku','{{%publisher_product}}', ['publisher_id', 'sku'],true);

    }
    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%publisher_product}}');
    }
}
