<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%otp}}`.
 */
class m230530_052257_create_otp_table extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        $table = '{{%otp}}';
        $this->createTable($table, [
            'id' => $this->primaryKey(11)->unsigned(),
            'customer_id' => $this->integer(11)->null(),
            'country_id' => $this->integer(11)->notNull(),
            'hp' => $this->char(32)->notNull(),
            'reference_token' => $this->integer(11)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP')
        ], $tableOptions);

        $this->createIndex('index_customer_id_created_at', $table, ['customer_id', 'created_at']);
        $this->createIndex('index_country_id_hp', $table, ['country_id','hp']);
    }

    public function safeDown()
    {
        $this->dropTable('{{%otp}}');
    }
}
