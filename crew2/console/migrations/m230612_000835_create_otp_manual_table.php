<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%otp_manual}}`.
 */
class m230612_000835_create_otp_manual_table extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        $table = '{{%otp_manual}}';
        $this->createTable($table, [
            'id' => $this->primaryKey(11)->unsigned(),
            'country_id' => $this->integer(11)->notNull(),
            'hp' => $this->string(32)->notNull(),
            'token' => $this->char(6)->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'updated_by' => $this->string(100),
            'remark' => $this->text()->null(),
            'expired_at' => $this->bigInteger()->unsigned(),
            'created_at' => $this->bigInteger()->unsigned(),
            'updated_at' => $this->bigInteger()->unsigned()
        ], $tableOptions);

        $this->createIndex('index_country_id_hp_status', $table, ['country_id', 'hp', 'status']);
    }

    public function safeDown()
    {
        $this->dropTable('{{%otp_manual}}');
    }
}
