<?php

use yii\db\Migration;

/**
 * Class m230702_103454_g2g_reseller
 */
class m230702_103454_g2g_reseller extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('reseller', [
            'reseller_id' => $this->primaryKey()->unsigned(),
            'title' => $this->string(255)->notNull(),
            'profile' => $this->string(255)->notNull(),
            'last_sync' => $this->integer()->unsigned()->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createTable('reseller_setting', [
            'reseller_setting_id' => $this->primaryKey(),
            'reseller_id' => $this->integer()->notNull(),
            'key' => $this->string(255)->notNull(),
            'value' => $this->string(255)->notNull()
        ], $tableOptions);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('reseller');
        $this->dropTable('reseller_setting');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m230702_103454_g2g_reseller cannot be reverted.\n";

        return false;
    }
    */
}
