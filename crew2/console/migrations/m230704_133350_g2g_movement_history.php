<?php

use yii\db\Migration;

/**
 * Class m230704_133350_batch_restock
 */
class m230704_133350_g2g_movement_history extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';

        $this->createTable('g2g_products_mapping', [
            'g2g_products_mapping_id' => $this->primaryKey()->unsigned(),
            'g2g_offer_id' => $this->string()->notNull(),
            'products_id' => $this->integer()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_g2g_offer_id', 'g2g_products_mapping', 'g2g_offer_id');
        $this->createIndex('idx_products_id', 'g2g_products_mapping', 'products_id');

        $this->createTable('g2g_stock_movement_history', [
            'g2g_stock_movement_history_id' => $this->primaryKey()->unsigned(),
            'g2g_order_id' => $this->string(255),
            'custom_products_code_id' => $this->integer(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->integer()->unsigned()->notNull(),
            'updated_at' => $this->integer()->unsigned()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_custom_products_code_id', 'g2g_stock_movement_history', ['custom_products_code_id']);
        $this->createIndex('idx_g2g_order_id_custom_products_code_id', 'g2g_stock_movement_history', ['g2g_order_id', 'custom_products_code_id']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('g2g_products_mapping');
        $this->dropTable('g2g_stock_movement_history');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m230704_133350_batch_restock cannot be reverted.\n";

        return false;
    }
    */
}
