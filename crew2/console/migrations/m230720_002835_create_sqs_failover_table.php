<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%sqs_failover}}`.
 */
class m230720_002835_create_sqs_failover_table extends Migration
{
    public function init($db = 'db_og')
    {
        $this->db = $db;
        parent::init();
    }

    public function safeUp()
    {
        $this->createTable('{{%sqs_failover}}', [
            'id' => $this->primaryKey(11)->unsigned(),
            'sqs_tag' => $this->string(32)->notNull(),
            'data' => $this->text()->notNull(),
            'created_at' => $this->bigInteger()->unsigned(),
        ]);
    }

    public function safeDown()
    {
        $this->dropTable('{{%sqs_failover}}');
    }
}
