<?php

use offgamers\base\controllers\Migration;

/**
 * Class m230821_155200_remove_webpush
 */
class m230821_155200_remove_webpush extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // remove auth child
        Yii::$app->db->createCommand("DELETE FROM auth_item_child WHERE child LIKE '%webpush%'")->execute();

        // remove route
        Yii::$app->db->createCommand("DELETE FROM auth_item WHERE name LIKE '%webpush%' AND type = 2")->execute();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m230821_155200_remove_webpush cannot be reverted.\n";
        return true;
    }
}
