<?php

use yii\db\Migration;

/**
 * Class m231002_031517_restock_queue
 */
class m231002_031517_restock_queue extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $table = '{{%restock_request}}';
        $this->createTable($table, [
            'restock_request_id' => $this->primaryKey()->unsigned(),
            'request_profile' => $this->string(100)->notNull(),
            'request_profile_id' => $this->string(255)->notNull(),
            'request_reference_id' => $this->string(255)->notNull(),
            'status' => $this->tinyInteger(1)->notNull(),
            'created_at' => $this->bigInteger()->unsigned(),
            'updated_at' => $this->bigInteger()->unsigned()
        ]);

        $this->createIndex('uk_request_profile_reference_id', $table, ['request_profile', 'request_profile_id', 'request_reference_id'], true);
        $this->createIndex('idx_order_id', 'api_restock_request', ['orders_id']);
        $this->addColumn('{{%api_restock_request}}', 'restock_request_id', $this->integer(11)->unsigned());

        $this->addForeignKey(
            'fk_restock_request_id',
            '{{%api_restock_request}}',
            'restock_request_id',
            'restock_request',
            'restock_request_id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            'fk_restock_request_id',
            'api_restock_request'
        );
        $this->dropIndex('idx_order_id', 'api_restock_request');
        $this->dropColumn('{{%api_restock_request}}', 'restock_request_id');
        $this->dropTable('{{%restock_request}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m231002_031517_restock_queue cannot be reverted.\n";

        return false;
    }
    */
}
