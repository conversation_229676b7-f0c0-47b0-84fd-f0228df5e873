<?php

use yii\db\Migration;
use yii\db\mssql\Schema;

/**
 * Class m201022_180133_create_table_order_sale
 */
class m240112_180133_create_table_order_sale extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('daily_so_po_tmp_history', [
            'id' =>  Schema::TYPE_BIGINT . ' NOT NULL',
            'ref_id' => Schema::TYPE_BIGINT . ' NOT NULL',
            'type' => Schema::TYPE_TINYINT . '(4) NOT NULL COMMENT "1 = PO, 2 = SO"',
            'status' => Schema::TYPE_TINYINT . '(4) NOT NULL COMMENT "3 = completed, 2 = non-complete"',
            'status_date' => Schema::TYPE_INTEGER . ' NOT NULL',
            'created_at' => Schema::TYPE_INTEGER . ' NOT NULL',
        ], $tableOptions);
        $this->addPrimaryKey('tmp_history_primary_key', 'daily_so_po_tmp_history', ['ref_id', 'type']);
        $this->createIndex('idx_type', 'daily_so_po_tmp_history', 'type');
        $this->createIndex('idx_id_type', 'daily_so_po_tmp_history', ['id', 'type']);
        $this->createIndex('idx_status_date', 'daily_so_po_tmp_history', 'status_date');
        $this->createIndex('idx_created_at', 'daily_so_po_tmp_history', 'created_at');


        $this->createTable('daily_po_history', [
            'id' => "bigint(20) NOT NULL",
            'ref_id' => "bigint(20) NOT NULL",
            'user_id' => "bigint(20) NOT NULL",
            'status' => "tinyint(4) NOT NULL",
            'status_date' => "int(11) NOT NULL",
            'sales' => "decimal(19,8) NOT NULL",
            'sync_amount' => "decimal(19,8) NOT NULL",
            'sync_date' => "int(11) NOT NULL DEFAULT '0'",
            'sync_status' => "tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 = pending to sync, 1 = sync completed'",
            'listing_id' => "bigint(20) NOT NULL",
            'data' => "text COLLATE utf8_unicode_ci",
            'year' => "smallint(4) NOT NULL",
            'month' => "tinyint(2) NOT NULL",
            'day' => "smallint(3) NOT NULL",
            'created_at' => "int(11) NOT NULL",
            'updated_at' => "int(11) NOT NULL"
        ], $tableOptions);
        $this->addPrimaryKey('daily_po_history_primary_key', 'daily_po_history', ['id']);
        $this->createIndex('idx_status_date', 'daily_po_history', 'status_date');
        $this->createIndex('idx_sync_status', 'daily_po_history', 'sync_status');
        $this->createIndex('idx_sync_status_date', 'daily_po_history', ['sync_status', 'year', 'month', 'day']);
        $this->createIndex('idx_ref_id', 'daily_po_history', 'ref_id');

        //insert cron_pending_credit process
        $cronData = array(
            "cron_process_track_in_action" => 1,
            "cron_process_track_start_date" => "2023-01-01 00:00:00", //(2013-08-30 00:00:00) (first G2G order)
            "cron_process_track_failed_attempt" => 0,
            "cron_process_track_filename" => "cron_sync_daily_purchase",
        );
        $this->insert('cron_process_track', $cronData);
    }

    /**
     * {@inheritdoc}
     */
    public function down()
    {
        $this->dropTable('daily_so_po_tmp_history');
        $this->dropTable('daily_po_history');
        $this->delete('cron_process_track', '`process`="cron_sync_daily_purchase"');
        return true;
    }

}
