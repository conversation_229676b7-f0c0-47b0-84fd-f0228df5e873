<?php

use yii\db\Migration;

/**
 * Class m250628_204142_add_new_columns_to_invoice
 */
class m250630_204142_create_einvoice_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('einvoice', [
            'einvoice_id' => $this->primaryKey()->unsigned(),
            'invoice_id' => $this->integer()->notNull(),
            'orders_id' => $this->integer()->notNull(),
            'file_raw_data' => $this->text()->notNull(),
            'einvoice_status' => $this->tinyInteger(1)->notNull()->comment("0-not_required, 1-created, 2-pending, 3-success, 4-failed"),
            'einvoice_result' => $this->text()->comment("einvoice submission result"),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $tableOptions);

        $this->createIndex('idx_invoice_id_orders_id', 'einvoice', ['invoice_id', 'orders_id'], true);
        $this->createIndex('idx_orders_id', 'einvoice', 'orders_id');
        $this->createIndex('idx_einvoice_status', 'einvoice', 'einvoice_status');
        $this->createIndex('idx_created_at', 'einvoice', 'created_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx_invoice_id_orders_id', 'einvoice');
        $this->dropIndex('idx_orders_id', 'einvoice');
        $this->dropIndex('idx_einvoice_status', 'einvoice');
        $this->dropIndex('idx_created_at', 'einvoice');
        $this->dropTable('einvoice');
    }
}