<?php

namespace console\models;

use Yii;

/**
 * This is the model class for table "best_selling".
 *
 * @property int $best_selling_country_id
 * @property string $country_code
 * @property string $info
 */
class BestSelling extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'best_selling';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['best_selling_id'], 'integer'],
            [['country_code', 'info'], 'string']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'best_selling_id' => 'Best Selling ID',
            'country_code' => 'Country Code',
            'info' => 'Info',
        ];
    }
}
