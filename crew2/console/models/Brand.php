<?php

namespace console\models;

use common\models\ApiFormModel;
use yii\db\Exception;
use yii\web\HttpException;

class Brand extends ApiFormModel
{
    public function __construct()
    {
        $this->s_key = 'micro.service.product';
        $this->controller = 'brand';
        $this->method = 'get';
        parent::__construct();
    }

    /**
     * @throws HttpException|Exception
     */
    public function getBrandUrls(): array
    {
        $this->action = "index";
        $params['all'] = 1;

        $response = $this->request($params);

        if (!isset($response['results'])) {
            throw new Exception("No response from Brand Index API");
        }

        $results = $response['results'];
        return array_map(function (array $brand) {
            return "/".$brand['seo_url'];
        }, $results);
    }
}