<?php

namespace console\models;

use Yii;

class Categories extends \common\models\Categories
{
    public static function getActiveCategoriesFromCategoriesStructure()
    {
        $result = \common\models\CategoriesStructures::find()->select('categories_structures_value')->where(['categories_structures_key' => 'games'])->asArray()->one();
        // filter block category
        return array_diff(explode(",", $result['categories_structures_value']), Yii::$app->params['block.category.sitemap']);
    }

    public static function getAllActiveCategoriesUrl()
    {
        $categories_to_tag = array();
        $return_array = array();

        $active_categories = self::getActiveCategoriesFromCategoriesStructure();
        $categories = self::find()->select(array('categories_id', 'categories_url_alias'))->where(['categories_id' => $active_categories, 'categories_status' => 1])->asArray()->all();

        $tags_map = \common\models\CategoriesTagmap::find()->asArray()->all();

        foreach ($tags_map as $map) {
            $categories_to_tag[$map['game_id']] = $map['tag_id'];
        }

        $tags_url = CategoriesTag::getSeoGeneratedUrlList();

        foreach ($categories as $category) {
            if(isset($categories_to_tag[$category['categories_id']])){
                $_tag_id = $categories_to_tag[$category['categories_id']];
                $_url = $tags_url[$_tag_id] . '/' . $category['categories_url_alias'];
                array_push($return_array, $_url);
            }
        }

        return $return_array;
    }

    public static function getCategoriesList()
    {
        $categories = Categories::getSecondLayerCategoryList();
        $sub_categories = Categories::getAllSubCategoryByCategoryId($categories);

        return $sub_categories;
    }


}
