<?php

namespace console\models;

use Yii;

class CategoriesTag extends \common\models\CategoriesTag
{
    public static function getSeoGeneratedUrlList()
    {
        $tag_list = self::find()->where(array('tag_status'=>1))->orderBy('tag_id')->asArray()->all();
        $tags = array();
        $return_array = array();
        foreach($tag_list as $tags_row) {
            $tags[$tags_row["tag_lft"]] = [
                "id" => $tags_row["tag_id"],
                "lft" => $tags_row["tag_lft"],
                "rgt" => $tags_row['tag_rgt'],
                "name" => str_replace("_", "-", strtolower($tags_row['tag_key']))
            ];
        }
        foreach($tags as $tag){
            $return_array[$tag['id']] = self::generateCategoriesSeoPath($tags, $tag['lft']);
        }
        return $return_array;
    }

    private static function generateCategoriesSeoPath($tag_list, $target)
    {
        $arr = [];
        unset($tag_list[1]);
        foreach ($tag_list as $value) {
            if ($value["lft"] < $target && $value["rgt"] > $target) {
                array_push($arr, $value["name"]);
            } elseif ($target == $value["lft"]) {
                array_push($arr, $value["name"]);
            }
        }
        return "/" . implode("/", $arr);
    }
}
