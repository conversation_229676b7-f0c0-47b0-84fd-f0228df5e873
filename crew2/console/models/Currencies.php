<?php

namespace console\models;

use offgamers\base\models\CurrenciesBase;
use Yii;
use yii\helpers\Json;
use offgamers\base\traits\GuzzleTrait;
use common\components\PipwaveSls;

class Currencies extends \offgamers\base\models\Currencies
{
    use GuzzleTrait;

    protected function getCurrencyRate()
    {
        $data = false;

        try {
            $options = [];
            $this->initClient();
            $url = 'http://www.apilayer.net/api/live?access_key=' . Yii::$app->params['currencylayer.api.key'];

            if (!empty(Yii::$app->params['proxy'])) {
                $options['proxy'] = Yii::$app->params['proxy'];
            }

            $result = $this->client->request('GET', $url, $options, 3);

            $data = Json::decode($result->getBody(), 1);

            if (!empty($data['success']) && $data['success'] == true && !empty($data['quotes'])) {
                return $data['quotes'];
            } else {
                throw new \Exception("Invalid JSON Structure");
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send('Error Fetching Currency Update', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode([
                        'message' => $e->getMessage(),
                        'body' => $data,
                        'trace' => $e->getTraceAsString()
                    ])
                )
            ), 'DEBUG');
        }

        return false;
    }

    /*
        Getting rates from OpenExchange
    */
    public function getCurrencyRateFromOX()
    {
        $data = false;

        try {
            $options = [];
            $this->initClient();
            $url = 'https://openexchangerates.org/api/latest.json?app_id=' . Yii::$app->params['openexchangerates.app.id'];

            $result = $this->client->request('GET', $url, $options, 3);

            $data = Json::decode($result->getBody(), 1);

            if (!empty($data['rates'])) {
                return $data['rates'];
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send('Error Fetching Currency Update From OpenExchangeRates', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode([
                        'message' => $e->getMessage(),
                        'body' => $data,
                        'trace' => $e->getTraceAsString()
                    ])
                )
            ), 'DEBUG');
        }

        return [];
    }

    public function syncCurrenciesRate()
    {
        $csv_output = [];
        $slack_output = [];
        $new_rate = [];

        $currency_list = CurrenciesBase::find()
            ->where(['currencies_live_update' => 1])
            ->andWhere(['!=', 'code', 'USD'])
            ->orderBy('code')->all();

        //
        $pipwave_sls =  new PipwaveSls();
        $live_currency = $pipwave_sls->currencyGetListRates();

        if ($live_currency) {
            foreach ($live_currency as $pair => $rate) {
                if (strlen($pair) == 6 && substr($pair, 0, 3) == "USD") {
                    $currency = substr($pair, 3, 3);
                    $new_rate[$currency] = $rate;
                }
            }

            $time = new \yii\db\Expression('NOW()');

            foreach ($currency_list as $currency) {
                /* @var Currencies $currency */
                if (isset($new_rate[$currency->code])) {
                    $rate = round($new_rate[$currency->code], 8);

                    $buy_rate = round($rate - ($rate * $currency->buy_value_adjust / 100), 8);
                    $sell_rate = round($rate + ($rate * $currency->sell_value_adjust / 100), 8);
                    $variant = round(abs($currency->value - $rate) / $currency->value * 100, 2);

                    if ($variant >= 7) {
                        $slack_output[] = $currency->code . ' Variance >= 7% (PIPWAVE: ' . $variant . '%), Rate Update Cancelled';
                    } elseif ($variant > 1.5) {
                        $slack_output[] = $currency->code . ' Variance > 1.5% (PIPWAVE: ' . $variant . '%), Proceed';
                    }

                    // Update Currencies Rate when there is changes
                    if ($rate != $currency->value && $variant < 7) {
                        $currency->value = $rate;
                        $currency->buy_value = $buy_rate;
                        $currency->sell_value = $sell_rate;
                        $currency->last_updated = $time;
                        $currency->save();

                        $last_history = CurrenciesHistory::find()->where(['currencies_id' => $currency->currencies_id]
                        )->orderBy(['version' => SORT_DESC])->one();
                        $history = new CurrenciesHistory();
                        if ($last_history) {
                            $last_history->date_to = $time;
                            $last_history->save();
                        }
                        $history->load([
                            'currencies_id' => $currency->currencies_id,
                            'code' => $currency->code,
                            'buy_value' => $buy_rate,
                            'spot_value' => $rate,
                            'sell_value' => $sell_rate,
                            'date_from' => $time,
                            'date_to' => '2999-12-31 23:59:59',
                            'version' => ($last_history ? $last_history->version + 1 : 1),
                            'last_modified' => $time
                        ], '');

                        $history->save();
                    }

                    $csv_output[] = [
                        $currency->code,
                        $currency->title,
                        $currency->buy_value,
                        $currency->value,
                        $currency->sell_value,
                        $variant
                    ];
                } else {
                    $slack_output[] = $currency->code . ' Currency Rate N/A';
                }
            }
        }

        $csvHeader = array('Code', 'Currency', 'Bid (Buy USD)', 'Spot', 'Ask (Sell USD)', 'Variant');

        $emailCSVObj = new \common\components\ErrorReportCom();

        $emailCSVObj->sendCsvMail(
            $csv_output,
            $csvHeader,
            'Rate Update' . ' - ' . date('Y/m/d'),
            Yii::$app->params['email.rateUpdate'],
            'Rate Update' . ' - ' . date('Y/m/d'),
            Yii::$app->params["noreply"]["default"]
        );

        if ($slack_output) {
            Yii::$app->slack->send('Currencies Update Notification', array(
                array(
                    'color' => 'warning',
                    'text' => implode("\n", $slack_output)
                )
            ), 'ANB');
        }
    }


}