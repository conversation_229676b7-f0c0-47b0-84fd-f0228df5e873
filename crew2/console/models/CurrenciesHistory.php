<?php

namespace console\models;

use Yii;

/**
 * This is the model class for table "currencies_history".
 *
 * @property string $id
 * @property int $currencies_id
 * @property string $code
 * @property string $buy_value
 * @property string $spot_value
 * @property string $sell_value
 * @property string $date_from
 * @property string $date_to
 * @property int $version
 * @property string $last_modified
 */
class CurrenciesHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'currencies_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['currencies_id', 'code', 'buy_value', 'spot_value', 'sell_value', 'date_from', 'date_to', 'version'], 'required'],
            [['currencies_id', 'version'], 'integer'],
            [['buy_value', 'spot_value', 'sell_value'], 'number'],
            [['date_from', 'date_to', 'last_modified'], 'safe'],
            [['code'], 'string', 'max' => 3],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'currencies_id' => 'Currencies ID',
            'code' => 'Code',
            'buy_value' => 'Buy Value',
            'spot_value' => 'Spot Value',
            'sell_value' => 'Sell Value',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
            'last_modified' => 'Last Modified',
        ];
    }
}
