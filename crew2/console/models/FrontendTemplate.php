<?php

namespace console\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "frontend_template".
 *
 * @property string $tpl_id
 * @property string $id
 * @property int $id_type `0: Category, 2: Game, 3: Product`
 * @property string $background_source
 * @property string $background_color
 * @property string $logo_source
 * @property int $tpl_status
 * @property int $af_notice_enable_status `0: Disabled, 1: Enabled with hide option, 2: Enabled without hide option`
 */
class FrontendTemplate extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'frontend_template';
    }

    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public function getFrontendTemplateLang()
    {
        return $this->hasMany(FrontendTemplateLang::class, ['tpl_id' => 'tpl_id']);
    }

    public static function getFirstParagraph($content, $html_tag)
    {
        $content = str_replace("\n", "<br>", $content);
        $content = preg_replace('/<h1[^>]*>([\s\S]*?)<\/h1[^>]*>/', '', $content);
        $parts = explode($html_tag, $content);

        foreach ($parts as $part) {
            $string = trim(strip_tags($part));
            if ($string !== "") {
                return $string;
            }
        }

        return trim(strip_tags(implode("", $parts)));
    }

    public static function getTemplateDescription($id, $id_type, $language_id = false, $default = false)
    {
        $query = FrontendTemplate::find()
            ->alias('ft')
            ->select(['ft.*', 'ftl.*'])
            ->joinWith('frontendTemplateLang ftl')
            ->where(['ft.id' => $id, 'ft.id_type' => $id_type])
            ->asArray();

        if ($language_id !== false) {
            $query->andWhere(['ftl.language_id' => $language_id]);
        }

        $output = $query->one();

        if ($default && (empty($output) || empty($output[0]['description'])) && $language_id != 1) {
            $output = self::getTemplateDescription($id, 1, $default);
        }

        $data = [];

        if (!empty($output['frontendTemplateLang'])) {
            foreach ($output['frontendTemplateLang'] as $template) {
                $data[$template['language_id']] = self::getFirstParagraph($template['description'], '<br>');
            }
        }

        return $data;
    }
}
