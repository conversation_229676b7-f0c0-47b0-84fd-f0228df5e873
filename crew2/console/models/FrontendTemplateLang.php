<?php

namespace console\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "frontend_template_lang".
 *
 * @property string $tpl_id
 * @property int $language_id
 * @property string $description
 * @property string $notice
 * @property string $system_requirements
 * @property string $remark
 * @property string $gallery_info
 * @property string $game_title
 * @property string $game_publisher
 * @property string $game_developer
 * @property string $game_release_date
 * @property string $game_keyword
 * @property string $related_link_info
 */
class FrontendTemplateLang extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'frontend_template_lang';
    }

    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }
}
