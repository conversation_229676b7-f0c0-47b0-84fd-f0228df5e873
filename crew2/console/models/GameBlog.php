<?php

namespace console\models;

use common\models\GameBlog as GameBlogMain;
use common\models\SearchKeywords;
use Yii;

class GameBlog extends GameBlogMain
{
    public static function getDefaultMetaDataTitles(): array
    {
        $enum = Yii::$app->enum;
        $language = $enum->getLanguage('listData');
        $languageCode = $enum->getLanguage('getLanguageCode');

        $metaTitle = [];
        foreach ($language as $languageId => $lang_desc) {
            $metaTitle[$languageId] = Yii::t(
                'seo',
                'TEXT_BUY_TITLE',
                [],
                $languageCode[$languageId]
            ) . ' %s - ' . Yii::t(
                'seo',
                'TEXT_DEFAULT_TITLE',
                [],
                $languageCode[$languageId]
            );
        }

        return $metaTitle;
    }

    public static function getMetaKeyword($game_blog_id, $language_id = false, $default = false)
    {
        return SearchKeywords::getMetaKeyword($game_blog_id, 2, $language_id, $default);
    }

    public static function getMetaDescription($game_blog_id, $language_id = false, $default = false)
    {
        return FrontendTemplate::getTemplateDescription($game_blog_id, 2, $language_id, $default);
    }
}
