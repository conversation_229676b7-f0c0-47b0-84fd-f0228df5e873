<?php

namespace console\models;

use Yii;

/**
 * This is the model class for table "game_blog_description".
 *
 * @property string $game_blog_id
 * @property int $language_id
 * @property string $game_blog_description
 */
class GameBlogDescription extends \common\models\GameBlogDescription
{
    public static function getAllGameBlogUrl()
    {
        $game_blogs = self::getAllGameBlog();
        $url_list = array();
        foreach ($game_blogs as $game_blog) {
            $_url = '/game/' . urlencode($game_blog['custom_url']);
            array_push($url_list, $_url);
        }
        return $url_list;
    }

}