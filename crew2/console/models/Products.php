<?php

namespace console\models;

use Yii;

class Products extends \common\models\Products
{
    public static function getAllActiveProducts()
    {
        $activeCategory = Categories::getActiveCategoriesFromCategoriesStructure();
        $return_array = array();

        $products = self::find()->alias('p')
            ->select(array('p.products_id', 'p.products_url_alias', 'c.categories_id', 'c.categories_parent_path','pdi.products_delivery_mode_id'))
            ->joinWith('categories c', false)
            ->joinWith('productsFollowPrice pfp', false)
            ->joinWith('productsToGameBlog ptgb', false)
            ->joinWith('productsDeliveryInfo pdi', false)
            ->where(array('p.products_status' => 1, 'pfp.products_id' => null, 'c.categories_status' => 1))
            ->andWhere(array('<>', 'p.custom_products_type_id', 3))
            ->andWhere(array('or', array('=', 'p.products_display', 1), array('IS NOT', 'ptgb.products_id', null)))
            ->asArray()->all();

        foreach ($products as $product) {
            $cat_path = explode("_", trim($product['categories_parent_path'] . $product['categories_id'], '_'));
            if ($cat_checking = array_intersect($activeCategory, $cat_path)) {
                if (count($cat_checking) > 0) {
                    $_url = '/buynow/' . $product['products_url_alias'] . (!empty($product['products_delivery_mode_id']) && $product['products_delivery_mode_id'] == 6 ? '-dtu' : '') . '.html';
                    array_push($return_array, $_url);
                }
            }
        }
        return $return_array;
    }

    /*
     * Use square brackets [] to define array instead of array parentheses
     * Excluded:
     * 1. Inactive products - products_status
     * 2. Bundled products - products_bundle
     * 3. Auto restock products - products_model
     * 4. DTU products - products_delivery_info_id
     */
    public static function getAllProductsList($categories)
    {
        $product_list = self::find()->alias('p')
            ->select(array('p.products_id', 'pd.products_name', 'p.products_price', 'p.products_base_currency'))
            ->joinWith('categories c', false)
            ->joinWith('productsFollowPrice pfp', false)
            ->joinWith('productsToGameBlog ptgb', false)
            ->joinWith('productsDescription pd', false)
            ->joinWith('productsDeliveryInfo pdi', false)
            ->where([
                'p.products_status' => 1,
                'pfp.products_id' => null,
                'c.categories_status' => 1,
                'c.categories_id' => $categories,
                'pd.language_id' => 1,
            ])
            ->andWhere(['<>', 'p.products_type', 4])
            ->andWhere(['<>', 'p.custom_products_type_id', 3])
            ->andWhere(['<>', 'p.products_bundle', 'yes'])
            ->andWhere(['or', ['p.products_model' => ''], ['p.products_model' => null]])
            ->andWhere(['or', ['<>', 'pdi.products_delivery_mode_id', 6], ['pdi.products_delivery_mode_id' => null]])
            ->andWhere(['or', ['p.products_display' => 1], ['IS NOT', 'ptgb.products_id', null]])
            ->asArray()
            ->all();

        return $product_list;
    }
}


