<?php

namespace console\models;

use DOMDocument;
use Exception;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class SitemapXML
{
    private const SITEMAP_FOLDER = "sitemap";
    private const URL_ROW_LIMIT = 10000;
    private const FILE_SIZE_LIMIT = 52428800; // in kb

    private bool $s3Upload;
    private string $basePath;

    public function __construct()
    {
        ini_set("memory_limit", "-1");
        set_time_limit(0);
        $params = Yii::$app->params;
        $this->basePath = $params['frontendDomain'];
    }

    public function generateXML($s3Upload = true): void
    {
        $this->s3Upload = !($s3Upload === 'false');

        try {
            $sitemap_filename = "product";

            $brand_url = $this->generateBrandUrlList();
            $game_key_url = $this->generateGameKeyUrlList();
            $url_list = array_merge($brand_url, $game_key_url);
            $siteMapUrls = $this->getSitemapUrls($url_list, ['en', 'cn', 'id']);

            $indexFilename = "index.xml";

            $this->generateMultipleSiteMapAndUpload($siteMapUrls, $sitemap_filename, $indexFilename);
        } catch (Exception $exception) {
            $this->sendErrorToSlack($exception->getMessage());
        }
    }

    public function generateBrandUrlList()
    {
        $url_list = [];
        $brand_list = ArrayHelper::index(
            (new Query)
                ->select(['brand_id', 'seo_url'])
                ->from('brand b')
                ->where(['status' => 1, 'show_in_search_result' => 1])
                ->all(Yii::$app->db_og),
            'brand_id'
        );

        $mapped_list = ArrayHelper::index(
            (new Query)
                ->select(['brand_id', 'categories_id'])
                ->from('categories_brand_list cbl')
                ->all(Yii::$app->db_og),
            'brand_id'
        );

        $category_listing = ArrayHelper::index(
            (new Query)
                ->select(['brand_id', 'categories_id', 'seo_url'])
                ->from('category c')
                ->where(['status' => 1])
                ->all(Yii::$app->db_og),
            'categories_id'
        );

        foreach ($brand_list as $id => $brand) {
            $brand_url = $brand['seo_url'];

            $supported_list = (new Query)
                ->select(['brand_id', 'type', 'sub_id'])
                ->from('brand_category bc')
                ->where(['brand_id' => $brand['brand_id']])
                ->all(Yii::$app->db_og);

            // Ignore if the brand does not have any category assigned,
            if (empty($supported_list)) {
                continue;
            }

            $url_list[] = $brand_url;

            // only index first layer for game blog page
            if (!isset($mapped_list[$brand['brand_id']])) {
                continue;
            }

            foreach ($supported_list as $item) {
                $is_self = false;
                if ($this->isSelfItem($brand['brand_id'], $brand_url)) {
                    $is_self = true;
                }
                if ($item['type'] == 1) {
                    if (isset($brand_list[$item['sub_id']])) {
                        $sub_brand_url = $brand_list[$item['sub_id']]['seo_url'];
                        $url_list[] = $brand_url . '/' . $sub_brand_url;
                        $sub_brand_supported_item = (new Query)
                            ->select(['brand_id', 'type', 'sub_id'])
                            ->from('brand_category bc')
                            ->where(['brand_id' => $item['sub_id'], 'type' => 2])
                            ->all(Yii::$app->db_og);

                        foreach ($sub_brand_supported_item as $sub_item) {
                            if (isset($category_listing[$sub_item['sub_id']])) {
                                $category = $category_listing[$sub_item['sub_id']];
                                $category_url = $brand_url . '/' . $sub_brand_url . '/' . $category['seo_url'];
                                $url_list[] = $category_url;
                                $products_list = $this->getSupportedProduct($category['categories_id']);
                                foreach ($products_list as $product) {
                                    $url_list[] = $category_url . '/' . $product;
                                }
                            }
                        }
                    }
                } else {
                    if (isset($category_listing[$item['sub_id']])) {
                        $category = $category_listing[$item['sub_id']];
                        if (isset($brand_list[$category['brand_id']])) {
                            $sub_brand_url = '';
                            if (!$is_self) {
                                $sub_brand_url = $brand_list[$category['brand_id']]['seo_url'];
                                $url_list[] = $brand_url . '/' . $sub_brand_url;
                            }
                            $category_url = $brand_url . '/' . (!empty($sub_brand_url) ? $sub_brand_url . '/' : '') . $category['seo_url'];
                            $url_list[] = $category_url;
                            $products_list = $this->getSupportedProduct($category['categories_id']);

                            foreach ($products_list as $product) {
                                $url_list[] = $category_url . '/' . $product;
                            }
                        }
                    }
                }
            }
        }

        return $url_list;
    }

    public function generateGameKeyUrlList()
    {
        $return_arr = [];
        $game_key_list = (new Query)
            ->select(['products_url_alias'])
            ->from('products p')
            ->where(['products_type' => 2, 'products_status' => 1])
            ->all(Yii::$app->db_offgamers);

        foreach ($game_key_list as $game_key) {
            $return_arr[] = 'buynow/' . $game_key['products_url_alias'] . '.html';
        }

        return $return_arr;
    }

    public function isSelfItem($brand_id, $seo_url)
    {
        $supported_list = (new Query)
            ->select(['type', 'sub_id'])
            ->from('brand_category bc')
            ->where(['brand_id' => $brand_id])
            ->all(Yii::$app->db_og);

        $brand_list_2 = [];
        $category_list = [];

        foreach ($supported_list as $item) {
            if ($item['type'] == 1) {
                $brand_list_2[] = $item['sub_id'];
            } else {
                $category_list[] = $item['sub_id'];
            }
        }

        if (count($brand_list_2) > 1) {
            return false;
        }

        if ($category_list) {
            $category_brand_list = (new Query)
                ->select(['brand_id'])
                ->from('category c')
                ->where(['categories_id' => $category_list])
                ->distinct()
                ->all(Yii::$app->db_og);

            foreach ($category_brand_list as $category_brand) {
                $brand_list_2[] = $category_brand['brand_id'];
                $brand_list_2 = array_unique($brand_list_2);
            }
        }

        if (count($brand_list_2) > 1) {
            return false;
        }

        $brand_item = (new Query)
            ->select(['brand_id', 'seo_url'])
            ->from('brand b')
            ->where(['brand_id' => array_unique($brand_list_2)])
            ->all(Yii::$app->db_og);

        if (count($brand_item) == 1 && $brand_item[0]['seo_url'] == $seo_url) {
            return true;
        }

        return false;
    }

    public function getSupportedProduct($categories_id)
    {
        $return_arr = [];
        $categories_list = [];

        $layer3_category = (new Query)
            ->select(['categories_id'])
            ->from('categories')
            ->where(['parent_id' => $categories_id, 'categories_status' => 1, 'custom_products_type_id' => [2, 3]])
            ->all();

        foreach ($layer3_category as $category) {
            $categories_list[] = $category['categories_id'];
            $layer4_category = (new Query)
                ->select(['categories_id'])
                ->from('categories')
                ->where(['parent_id' => $category['categories_id'], 'categories_status' => 1])
                ->all();

            foreach ($layer4_category as $l4_cat) {
                $categories_list[] = $l4_cat['categories_id'];
            }
        }

        if ($categories_list) {
            $products_list = ArrayHelper::getColumn(
                (new Query)
                    ->select(['products_id'])
                    ->from('products_to_categories')
                    ->where(['categories_id' => $categories_list])
                    ->all(),
                'products_id'
            );

            foreach ($products_list as $products_id) {
                $product = Products::find()->where(['products_id' => $products_id, 'products_status' => 1])->asArray()->one();
                if ($product) {
                    $return_arr[] = $product['products_url_alias'];
                }
            }
        }

        return $return_arr;
    }

    private function getSitemapUrls(array $urlList, array $languageList): array
    {
        $sitemapUrls = [];
        $urlList = array_unique($urlList);
        foreach ($urlList as $url) {
            foreach ($languageList as $language) {
                $urlByLanguage = $this->basePath . '/' . $language . '/' . $url;
                $sitemapUrls[] = [
                    'loc' => $urlByLanguage,
                    'lastmod' => date("Y-m-d"),
                    'changefreq' => 'monthly',
                    'priority' => '0.5'
                ];
            }
        }

        return $sitemapUrls;
    }

    private function generateMultipleSiteMapAndUpload(
        array $siteMapUrls,
        string $siteMapName,
        string $indexFilename
    ): void {
        $totalSiteMapUrls = count($siteMapUrls);
        $totalFiles = (int)ceil($totalSiteMapUrls / self::URL_ROW_LIMIT);

        $fileNames = [];
        for ($i = 1; $i <= $totalFiles; $i++) {
            $offset = ($i - 1) * self::URL_ROW_LIMIT;
            $urls = array_slice($siteMapUrls, $offset, self::URL_ROW_LIMIT);

            $fileName = $siteMapName . '_' . $i . ".xml";

            $this->generateSitemapAndSave($urls, $fileName);

            $fileNames[] = $fileName;
        }

        $sitemaps = [];
        foreach ($fileNames as $fileName) {
            $sitemaps[] = [
                'loc' => $this->basePath . "/" . self::SITEMAP_FOLDER . "/" . $fileName,
                'lastmod' => date("Y-m-d"),
            ];
        }

        $this->generateSitemapIndexAndSave($sitemaps, $indexFilename);
    }

    private function invalidateCloudFrontCache(string $fileName)
    {
        $distribution_id = Yii::$app->params['aws.cf.distribution_id'];
        $cf_path = '/' . Yii::$app->aws->s3['BUCKET_SITEMAP']['prefix_path'] . '/' . $fileName;
        Yii::$app->aws->getCF()->invalidateCache($distribution_id, array($cf_path));
    }

    private function generateSitemapAndSave(array $sitemapUrls, string $fileName): void
    {
        $mainElementName = 'urlset';
        $childElementName = 'url';
        $attributes = [
            'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9',
        ];

        $this->generateXMLAndSave(
            $sitemapUrls,
            $attributes,
            $mainElementName,
            $childElementName,
            $fileName
        );
    }

    private function generateSitemapIndexAndSave(array $sitemaps, string $indexFilename): void
    {
        $mainElementName = 'sitemapindex';
        $childElementName = 'sitemap';
        $attributes = [
            'xmlns:xsi' => 'http://www.w3.org/2001/XMLSchema-instance',
            'xsi:schemaLocation' => 'http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/siteindex.xsd',
            'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9'
        ];

        $this->generateXMLAndSave(
            $sitemaps,
            $attributes,
            $mainElementName,
            $childElementName,
            $indexFilename
        );
    }

    private function generateXMLAndSave(
        array $elements,
        array $attributes,
        string $mainElementName,
        string $childElementName,
        string $fileName
    ): void {
        $document = new DOMDocument('1.0', 'utf-8');
        $documentElement = $document->createElement($mainElementName);

        foreach ($attributes as $key => $attr) {
            $documentElement->setAttribute($key, $attr);
        }

        foreach ($elements as $sitemap) {
            $container = $document->createElement($childElementName);
            foreach ($sitemap as $key => $value) {
                $element = $document->createElement($key);
                $element->nodeValue = $value;
                $container->appendChild($element);
            }
            $documentElement->appendChild($container);
        }

        $document->appendChild($documentElement);

        $path = Yii::getAlias('@console') . '/tmp/' . $fileName;

        $document->save($path);

        if (!file_exists($path)) {
            throw new Exception("File [$path] does not exists.");
        }

        $filesize = filesize($path);
        if ($filesize > self::FILE_SIZE_LIMIT) {
            throw new Exception("File [$path] $filesize kb exceed file size limit [" . self::FILE_SIZE_LIMIT . "] kb.");
        }

        if ($this->s3Upload) {
            $s3 = Yii::$app->aws->getS3('BUCKET_SITEMAP');
            $s3->saveFile($fileName, $path, false, 604800);
        }
    }

    private function sendErrorToSlack(string $message): void
    {
        Yii::$app->slack->send(
            "Sitemap XML Error",
            [
                [
                    'color' => 'warning',
                    'text' => $message,
                ],
            ]
        );
    }
}
