<?php

return [
    // Main offgamers db conection
    'db.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // log module
    'db.log.dsn' => 'mysql:host=localhost;dbname=offgamers_log',
    'db.log.username' => '',
    'db.log.password' => '',

    // og db connection
    'db.og.dsn' => 'mysql:host=localhost;dbname=og',
    'db.og.username' => '',
    'db.og.password' => '',

    // report db connection
    'db.report.dsn' => 'mysql:host=localhost;dbname=offgamers_report',
    'db.report.username' => '',
    'db.report.password' => '',

    // slave db connection
    'db.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',
    'db.log.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers_log',
    'db.og.slave.dsn' => 'mysql:host=localhost;dbname=og',
    'db.report.slave.dsn' => 'mysql:host=localhost;dbname=offgamers_report',

    // Mailer SMTP setup
    'mailer.transportClass' => 'Swift_SmtpTransport',
    'mailer.transportHost' => 'smtp.gmail.com',
    'mailer.transportUsername' => '',
    'mailer.transportPassword' => '',
    'mailer.transportPort' => '587',
    'mailer.transportEncryption' => 'tls',

    // Shasso API call
    'shasso.api.merchant' => 'ogm',
    'shasso.api.secret' => '123456',

    // Error notification using slack channel
    'slack.url' => '',
    'slack.webhook.default' => '',
    'slack.webhook.debug' => '',
    'slack.webhook.ogp' => '',
    'slack.webhook.tax' => '',
    'slack.webhook.anb' => '',
    'slack.webhook.info' => '',
    'slack.webhook.bdt.replenish' => '',
    'slack.webhook.bdt.dtu' => '',

    //image optimizer auth key
    'short.pixel.key' => '',
    'tinypng.key' => '',
    'kraken.key' => "",
    'kraken.secret' => "",

    // Currency Layer API Key
    'currencylayer.api.key' => '',

    // OpenExchangeRates App Id
    'openexchangerates.app.id' => '',

    // AWS
    'aws.key' => '',
    'aws.secret' => '',

    'aws.sqs.mailer.url'  => '',
    'aws.sqs.mailer.name'  => '',
    'aws.sqs.mailer.key'  => '',
    'aws.sqs.mailer.secret'  => '',

    'aws.s3.key' => '',
    'aws.s3.secret' => '',
    'aws.s3.bucket_key' => '',

    'aws.s3.attach.key' => '',
    'aws.s3.attach.secret' => '',
    'aws.s3.attach.bucket_key' => 's3-ms-toolbox',

    'aws.encrypt.log.key' => '',
    'aws.encrypt.log.secret' => '',
    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',

    'aws.s3.image.key' => '',
    'aws.s3.image.secret' => '',
    'aws.s3.image.bucket_key' => '',
    'aws.s3.image.cdn_domain' => '',

    'aws.s3.secure.key' => '',
    'aws.s3.secure.secret' => '',
    'aws.s3.secure.bucket_key' => 's3-secure',

    'aws.s3.repo.bucket_key' => '',

    'aws.cf.key' => '',
    'aws.cf.secret' => '',
    'aws.cf.distribution_id' => '',

    'micro.service.product' => [
        'baseUrl' => 'https://staging-ms-product.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.inventory' => [
        'baseUrl' => 'https://staging-ms-inventory.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.storecredit' => [
        'baseUrl' => 'https://staging-ms-storecredit.offgamers.com',
        'key' => 'backend',
        'secret' => '123456',
    ],

    'micro.service.order' => [
        'baseUrl' => 'https://staging-ms-order.offgamers.com',
        'key' => 'backend',
        'secret' => '123456',
    ],
    
    'aws.cf.list' => [
        [
            'title' => '',
            'distribution_id' => '',
            'aws.key' => '',
            'aws.secret' => '',
            'aws.region' => 'us-east-1',
            'aws.version' => 'latest'
        ],
        [
            'title' => '',
            'distribution_id' => '',
        ],
    ],

    // Pipwave RFI usage
    'pipwave.rfi.key' => 'piprfi',
    'pipwave.rfi.secret' => '123456',

    // CDK secure config
    'cdk.secure.cipher' => 'rijndael-128',
    'cdk.secure.key' => '2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8',
    'cdk.secure.key.iv' => '9S?C3B4WMQW6XM8P',

    //Pipwave SLS
    'pipwavesls.open.base_url' => "https://staging-open-api.pipwave.com",
    'pipwavesls.api.key' => "HQ18LSV6BV6S8GZLDTWDVH4EG7NKCZVO",
    'pipwavesls.merchant_id' => "OG-testing",
    'pipwavesls.secret' => "ENqlGQCBmjiVmcQRvfShUsciZn7OPXGyydrQOSENVe7",

    //G2G SLS 
    'g2g.sls.user_url' => "https://staging-sls.offgamers.com",
    'g2g.sls.admin_url' => "https://staging-sls-bed24f13.offgamers.com",
    'g2g.sls.key' => "Legacy",
    'g2g.sls.secret' => "",
];