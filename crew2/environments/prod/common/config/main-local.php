<?php

return [
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // slave server
            'slaveConfig' => [
                'username' => $params['db.username'],
                'password' => $params['db.password'],
                'charset' => 'utf8',
            ],
            // slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // slave server
            'slaveConfig' => [
                'username' => $params['db.username'],
                'password' => $params['db.password'],
                'charset' => 'latin1',
            ],
            // slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.og.dsn'],
            'username' => $params['db.og.username'],
            'password' => $params['db.og.password'],
            'charset' => 'utf8mb4',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_log' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.log.dsn'],
            'username' => $params['db.log.username'],
            'password' => $params['db.log.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_report' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.report.dsn'],
            'username' => $params['db.report.username'],
            'password' => $params['db.report.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_slave_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.slave.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_slave_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.og.slave.dsn'],
            'username' => $params['db.og.username'],
            'password' => $params['db.og.password'],
            'charset' => 'utf8mb4',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_slave_report' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.report.slave.dsn'],
            'username' => $params['db.report.username'],
            'password' => $params['db.report.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "",
            'servers' => [
                [
                    'host' => $params['cache.host'],
                    'port' => $params['cache.port'],
                    'weight' => 50,
                ],
            ],
        ],
        'frontend_cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "ogm/OffGamers/",
            'servers' => [
                [
                    'host' => $params['cache.host'],
                    'port' => $params['cache.port'],
                    'weight' => 50,
                ],
            ],
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => $params['aws.key'],
            'secret' => $params['aws.secret'],
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_TOOLBOX' => [
                    // Optional: Remove to use default
                    'key' => $params['aws.s3.attach.key'],
                    'secret' => $params['aws.s3.attach.secret'],
                    'region' => 'us-east-1',
                    'prefix_path' => '',

                    // Required
                    'bucket_key' => $params['aws.s3.attach.bucket_key'],
                    'acl' => 'private',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_SITEMAP' => [
                    'key' => $params['aws.s3.key'],
                    'secret' => $params['aws.s3.secret'],
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.s3.bucket_key'],
                    'acl' => 'public-read',
                    'prefix_path' => 'sitemap',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_ENCRYPT_LOG' => [
                    'key' => $params['aws.encrypt.log.key'],
                    'secret' => $params['aws.encrypt.log.secret'],
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.encrypt.log.bucket_key'],
                    'acl' => 'private',
                    'prefix_path' => 'log',
                    'storage' => 'STANDARD',
                    'sse_kms_key' => $params['aws.encrypt.log.kms_key']
                ],
                'BUCKET_IMAGE' => [
                    'key' => $params['aws.s3.image.key'],
                    'secret' => $params['aws.s3.image.secret'],
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.s3.image.bucket_key'],
                    'acl' => 'public-read',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                    'cdn_domain' => $params['aws.s3.image.cdn_domain']
                ],
                'BUCKET_SECURE' => [
                    'key' => $params['aws.s3.secure.key'],
                    'secret' => $params['aws.s3.secure.secret'],
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.s3.secure.bucket_key'],
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_REPOSITORY' => [
                    'key' => $params['aws.key'],
                    'secret' => $params['aws.secret'],
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.s3.repo.bucket_key'],
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_UPLOAD' => [
                    'region' => 'us-east-1',
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_ENCRYPT_KEY' => [
                    // Optional: Remove to use default
                    'key' => '',
                    'secret' => '',
                    'region' => '',
                    'bucket_key' => '',

                    // Required
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                    'sse_kms_key' => ''
                ],
            ],
            'sqs' => [
                'MAIL_QUEUE' => [
                    'queue_url' => $params['aws.sqs.mailer.url'],
                    'key' => $params['aws.sqs.mailer.key'],
                    'secret' => $params['aws.sqs.mailer.secret']
                ],
                'OUTGOING_LOG_QUEUE' => [
                    'queue_url' => '',
                ],
                'INCOMING_LOG_QUEUE' => [
                    'queue_url' => '',
                ],
                'EINVOICE_FIFO_QUEUE' => [
                    'queue_url' => '',
                    'region' => '',
                    'key' => '',
                    'secret' => '',
                ]
            ],
            'cf' => [
                'key' => $params['aws.cf.key'],
                'secret' => $params['aws.cf.secret'],
                'region' => 'us-east-1'
            ],
        ],
        'imageOptimizer' =>[
            'profile' => 'ShortPixel' // ShortPixel / Kraken / TinyPNG
        ]
    ],
];
