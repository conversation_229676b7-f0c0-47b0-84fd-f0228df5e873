<?php

return [
    'proxy' => '',
    'guzzel.verifySsl' => true,

    'frontendDomain' => 'https://www.offgamers.com',
    'supportEmail' => '<EMAIL>', // compulsory for yii2-admin PasswordResetRequest.php
    'supportedLanguage' => array('en','zh-CN','ID'),

    // cron account closure setup
    'cronAccountClosure.processingLimit' => 20,
    'cronAccountClosure.expiryMaxDays' => 14,
    'cronAccountClosure.emailSubject' => \Yii::t('app', '[OG Account Closure]'),
    'cronAccountClosure.emailRecipient' => ['<EMAIL>' => 'Dev Staging'],

    "syncPublisherProduct.notification" => ["<EMAIL>"],

    'email.rateUpdate' => '<EMAIL>',
    'email.resellerUpgrade' => '',

    // Mailer local
    'mailer.useFileTransport' => true,

    // Old-Crew Path
    'oldcrew.baseUrl' => 'https://crew.offgamers.com',

    // Shasso API call
    'shasso.api.baseUrl' => 'https://api2.shasso.com',

    // Error notification using slack channel
    'slack.sslVerification' => false,

    'cache.host' => 'localhost',
    'cache.port' => 11211,

    // Date search limiter used in store credit report as of now
    'date.search.within' => 12,

    // noreply sender
    'noreply' => [
        'default' => '<EMAIL>',
        'alert' => '<EMAIL>',
        'info' => '<EMAIL>',
        'order' => '<EMAIL>'
    ],

    // Block Category for sitemap.xml
    'block.category.sitemap' => [],

    // report exclude category
    'report.exclude.category' => [],
    'product.type' => [
        0 => "DTU",
        1 => "Voucher",
        2 => "Game Key",
        3 => "Mobile Recharge",
        4 => "Physical Goods"
    ],
];