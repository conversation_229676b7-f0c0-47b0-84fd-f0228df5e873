# Multiservice

Tools that can be shared among OffGamers internal projects to make life easier.

# Configuration 

params-encoded.php
```
    'slack.url' => '', // Slack URL
    'slack.webhook.default' => '', // Slack Default Webhook
    'slack.webhook.debug' => '',

    'aws.sqs.mail.url' => '', // Mail SQS Queue Url
    'aws.sqs.mail.key' => '', // Mail SQS Queue Key
    'aws.sqs.mail.secret' => '', // Mail SQS Queue Secret
    'aws.sqs.mail.region' => 'us-east-1', //Mail SQS Queue Region

    'aws.sqs.deadmail.url' => '', // Mail Dead letter SQS Queue Url
    'aws.sqs.deadmail.key' => '', // Mail Dead letter SQS Queue Key
    'aws.sqs.deadmail.secret' => '', // Mail Dead letter SQS Queue Secret
    'aws.sqs.deadmail.region' => 'us-east-1', //Mail Dead letter SQS Queue Region

    'aws.ses.key' => '', // SES Mail Key
    'aws.ses.secret' => '', // SES Mail Secret
    'aws.ses.region' => 'us-east-1', // SES Mail Region

    'aws.s3.attach.key' => '', // S3 Attachment Key
    'aws.s3.attach.secret' => '', // S3 Attachment Secret
    'aws.s3.attach.bucket_key' => '', // S3 Attachment Bucket Key
    'aws.s3.attach.region' => 'us-east-1', // S3 Attachment Region

    'aws.key' => '', // AWS KEY
    'aws.secret' => '', // AWS SECRET
    'aws.region' => 'us-east-1', // AWS REGION
```

# RUNNING MAIL QUEUES
  * Running Mail Queue
    * ```yii mail-queue/listen [timeout]```
      * Run the mail queue infinitely , optional timeout[0 - 20] long polling time.
    * ```yii mail-queue/run```
      * Run the mail queue by query once and execute task and exit on finish. Exit immediately on no queue.

  * Running Dead Mail Queue
    * ```yii dead-mail-queue/listen [timeout]```
      * Run the dead mail queue infinitely , optional timeout[0 - 20] long polling time.
    * ```yii dead-mail-queue/run```
      * Run the dead mail queue by query once and execute task and exit on finish. Exit immediately on no queue.

  * Additional Parameters
  ```
    --verbose, -v: print executing statuses into console.
    --isolate: verbose mode of a job execute. If enabled, execute result of each job will be printed.
    --color: highlighting for verbose mode.
  ```