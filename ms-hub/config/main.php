<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php')
);

return [
    'id' => 'multiservice',
    'language' => 'en-US',
    // the basePath of the application will be the `multiservice` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log', 'mail_queue', 'dead_mail_queue', 'pdf_queue', 'dead_pdf_queue', 'task_queue', 'dead_task_queue'],
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_og' => [
            'class' => 'yii\db\Connection',
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_log' => [
            'class' => 'yii\db\Connection',
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'mailer' => [
            'class' => 'offgamers\base\mail\AWSSESMailer',
            'mailQueueName' => 'MAIL_QUEUE',
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache'
        ],
        'mail_queue' => [
            'class' => \micro\components\MailQueue::class,
            'queue_name' => 'MAIL_QUEUE'
        ],
        'dead_mail_queue' => [
            'class' => \micro\components\MailQueue::class,
            'queue_name' => 'DEAD_MAIL_QUEUE'
        ],
        'pdf_queue' => [
            'class' => \micro\components\PdfQueue::class,
            'queue_name' => 'PDF_QUEUE'
        ],
        'dead_pdf_queue' => [
            'class' => \micro\components\PdfQueue::class,
            'queue_name' => 'DEAD_PDF_QUEUE'
        ],
        'task_queue' => [
            'class' => \micro\components\TaskQueue::class,
            'queue_name' => 'TASK_QUEUE'
        ],
        'dead_task_queue' => [
            'class' => \micro\components\TaskQueue::class,
            'queue_name' => 'DEAD_TASK_QUEUE'
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@micro/messages',
                    'sourceLanguage' => 'sys',
                ],
            ],
        ],
    ],
];


