<?php

$config = [
    'components' => [
        'db' => [
            'dsn' => 'mysql:host=localhost;dbname=offgamers',
            'username' => '',
            'password' => '',
        ],
        'db_offgamers' => [
            'dsn' => 'mysql:host=localhost;dbname=offgamers',
            'username' => '',
            'password' => '',
        ],
        'db_og' => [
            'dsn' => 'mysql:host=localhost;dbname=og',
            'username' => '',
            'password' => '',
        ],
        'db_log' => [
            'dsn' => 'mysql:host=localhost;dbname=offgamers_log',
            'username' => '',
            'password' => '',
        ],
        'cache' => [
            'servers' => [
                [
                    'host' => '',
                    'port' => 11211,
                    'weight' => 50,
                ],
            ],
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => '',
                'DEBUG' => ''
            ]
        ],
        'aws' => [
            'class' => 'offgamers\base\components\AWS',
            'key' => '',
            'secret' => '',
            'version' => 'latest',
            'region' => '',
            's3' => [
                'BUCKET_REPOSITORY' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_TOOLBOX' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_ENCRYPT_LOG' => [
                    'acl' => 'private',
                    'prefix_path' => 'log',
                    'storage' => 'STANDARD',
                    'bucket_key' => '',
                    'sse_kms_key' => ''
                ],
            ],
            'ses' => [],
            'sqs' => [
                'MAIL_QUEUE' => [
                    'queue_url' => ''
                ],
                'DEAD_MAIL_QUEUE' => [
                    'queue_url' => ''
                ],
                'PDF_QUEUE' => [
                    'queue_url' => ''
                ],
                'DEAD_PDF_QUEUE' => [
                    'queue_url' => ''
                ],
                'TASK_QUEUE' => [
                    'queue_url' => ''
                ],
                'DEAD_TASK_QUEUE' => [
                    'queue_url' => ''
                ]
            ]
        ],
    ],
];

return $config;