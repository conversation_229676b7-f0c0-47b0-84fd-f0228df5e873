<?php

namespace micro\interfaces;

use Yii;
use yii\queue\JobInterface;
use yii\base\BaseObject;
use yii\helpers\Json;
use offgamers\base\mail\AWSSESMessage;

class MailJob extends BaseObject implements JobInterface
{
    public $payload;
    public $start_time;

    public function execute($queue)
    {
        $process_track = Yii::$app->params['process'];

        $message = new AWSSESMessage();
        $message = $message->loadFromJSON($this->payload)->restoreAllS3Content();
                  
        if(!empty($message->getTemplate())){
            $content = Yii::$app->getView()->render('/email/' . $message->getTemplate(), [
                'data' => $this->payload ]);
            $message->setHtmlBody($content);
        }

        $process_track->updateProcess('start_email');

        $ses_obj = Yii::$app->aws->getSES();

        if ($message->isToBeSentRaw()) {
            if ($mail = $message->getRawMessage()) {
                $ses_obj->sendRawEmail($mail);
            }
            $message->deleteAttachmentFromS3();
        } else {

            if($mail = $message->getFormattedMessage()){
                $ses_obj->sendEmail($mail);
            }
        }

        $process_track->updateProcess('end_email');
    }
}
