<?php

namespace micro\interfaces;

use Yii;
use yii\base\BaseObject;
use yii\db\Exception;
use yii\helpers\Json;
use yii\queue\JobInterface;
use micro\models\Invoice;
use offgamers\base\components\Pdf;

class PdfJob extends BaseObject implements JobInterface
{
    public $data;
    public $start_time;

    public function execute($queue)
    {
        if (isset($this->data['filetype'])) {
            switch ($this->data['filetype']) {
                case 'invoice':
                    $this->generateInvoice();
                    break;
                case 'order_email':
                    $this->processEmailTemplate();
            }
        }
    }

    public function generateInvoice()
    {
        $process_track = Yii::$app->params['process'];

        if ($this->data['template'] == 'tax-invoice') {
            foreach ($this->data['order_total'] as $ot_id => $ot) {
                foreach ($ot as $ot_class_id => $ot_class) {
                    if (isset($ot_class['type']) && $ot_class['type'] == 'ot_gst' && 0 >= $ot_class['value']) {
                        if ((isset($this->data['checkout_info']['tax_invoice_reverse_amount']) && $this->data['checkout_info']['tax_invoice_reverse_amount'] > 0) === false) {
                            $this->data['template'] = 'invoice';
                        }
                    }
                }
            }
        }

        $content = Yii::$app->getView()->render('/pdf/' . $this->data['template'], $this->data);

        $pdf = new Pdf([
            "mode" => Pdf::MODE_UTF8,
            "format" => Pdf::FORMAT_A4,
            "orientation" => Pdf::ORIENT_PORTRAIT,
            "destination" => Pdf::DEST_STRING,
            "content" => $content,
            "options" => [
                "autoLangToFont" => true,
                'autoScriptToLang' => true,
                'ignore_invalid_utf8' => true,
                'tabSpaces' => 4,
            ],
        ]);

        $pdf->cssInline = 'td,th{padding:0}table{border-spacing:0}';
        $buffer = $pdf->render();

        $process_track->updateProcess('render_pdf');

        $s3 = Yii::$app->aws->getS3("BUCKET_REPOSITORY");
        $s3_path = 'Worker_PDF/ogm/' . $this->data['filetype'] . '/' . date("Ym") . '/' . $this->data['filename'];
        if ($s3->saveContent($s3_path, $buffer, false, null, ['ContentType' => 'application/pdf'])) {
            $process_track->updateProcess('store_s3');

            $from = Yii::$app->params['noreply']["order"];
            $to = [$this->data['customer']['email']];
            $subject = "Your invoice for order #" . $this->data['checkout_info']['order_number'];
            $body = Yii::$app->getView()->render('/email/invoice', ['data' => $this->data]);

            if (!empty($this->data['send_email']) && $this->data['send_email']) {
                $mailer = Yii::$app->mailer->compose()->setFrom($from)->setReplyTo($from)->setTo($to)->setSubject(
                    $subject
                )->setHtmlBody($body)->attachContent($buffer, [
                    'fileName' => $this->data['filename'],
                    'contentType' => 'application/pdf',
                ]);

                $process_track->updateProcess('mail_attachment');
                $mailer->send();
                $process_track->updateProcess('sqs_send_mail');
            }

            $invoice_number = $this->data['checkout_info']['invoice_number'];

            $invoice = Invoice::findOne(['invoice_number' => $invoice_number]);

            if ($invoice) {
                $invoice->invoice_file_domain = $s3->getBucketKey();
                $invoice->invoice_file_path = $s3_path;
                $invoice->save();
            } else {
                Yii::$app->slack->send('Invoice Number Not Found', [
                    [
                        'color' => 'warning',
                        'text' => Json::encode([
                            'invoice_number' => $invoice_number,
                            's3_domain' => $s3->getBucketKey(),
                            'path' => $s3_path,
                        ]),
                    ],
                ]);
            }
        } else {
            throw new Exception('Fail to upload data to s3');
        }
    }

    // This function will process Email to use HTML template
    public function processEmailTemplate()
    {
        // Set Language base on customer profile
        switch ($this->data['customer']['language']) {
            case 'cn':
            case 'zh-CN':
                $language = 'cn';
                break;
            case 'id':
                $language = 'id';
                break;
            default:
                $language = 'en';
                break;
        }
        Yii::$app->language = $language;
        // Check for paypal or moneybooker template
        $verifyLink = '';
        if (isset($this->data['customer']['payment_verification']) && !empty($this->data['customer']['payment_verification'])) {
            switch ($this->data['customer']['payment_verification']) {
                case 'paypal': // Regular Paypal email verification
                    $verifyLink = Yii::$app->params['shasso.domain'] . '/verify-email/index?action=verify&serialNumber=' . $this->data['customer']['serial_number'] . '&email=' . urlencode(
                            $this->data['customer']['payer_email']
                        );
                    break;
                case 'moneybooker': // Regular Moneybooker email verification
                    $verifyLink = Yii::$app->params['shasso.domain'] . '/verify-email/index?action=verify&serialNumber=' . $this->data['customer']['serial_number'] . '&email=' . urlencode(
                            $this->data['customer']['payer_email']
                        );
                    break;
            }
        }

        // If comment exist clean \n\r\t
        if (isset($this->data['orders']['comments']) && !empty($this->data['orders']['comments'])) {
            $this->data['orders']['comments'] = nl2br($this->data['orders']['comments']);
        }

        // Render HTML Email Body
        $body = Yii::$app->getView()->render('/email/' . $this->data['email_template'], [
            'data' => $this->data,
            'verifyLink' => $verifyLink,
        ]);

        // Generate Email Subject
        $emailSubject = $this->generateEmailSubject();

        $from = (isset($this->data['store_owner_email_address']) ? $this->data['store_owner_email_address'] : Yii::$app->params['noreply']["order"]);

        $mailer = Yii::$app->mailer->compose();
        $mailer->setFrom($from);
        $mailer->setReplyTo($from);
        $mailer->setTo($this->data['customer']['email']);
        $mailer->setSubject($emailSubject);
        $mailer->setHtmlBody($body);

        if(isset($this->data['orders']) && isset($this->data['orders']['attachments'])) {
            foreach($this->data['orders']['attachments'] as $attachment) {
                $mailer->attachContent(file_get_contents($attachment['s3Url']), [
                    'fileName' => $attachment['fileName'],
                    'contentType' => $attachment['contentType'],
                ]);
            }
        }

        $mailer->send();
    }

    private function generateEmailSubject()
    {
        switch ($this->data['email_template']) {
            case 'order-full-deliver':
            case 'order-partial-deliver':
            case 'order-comment':
            case 'order-genesis-cancel':
            case 'order-refund':
                $subject = Yii::t('email', 'TEXT_EMAIL_SUBJECT', ['orderId' => $this->data['orders']['orders_id']]);
                break;
            case 'order-mb-verify':
                $subject = Yii::t('email', 'TEXT_MONEYBOOKERS_VERIFY_SEND_SUBJECT');
                if (isset($this->data['customer']['payment_verification']) && !empty($this->data['customer']['payment_verification'])) {
                    $subject = Yii::t('email', 'TEXT_MONEYBOOKERS_VERIFY_SUBJECT');
                }
                break;
            case 'order-paypal-verify':
                $subject = Yii::t('email', 'TEXT_PAYPAL_VERIFY_SEND_SUBJECT');
                if (isset($this->data['customer']['payment_verification']) && !empty($this->data['customer']['payment_verification'])) {
                    $subject = Yii::t('email', 'TEXT_PAYPAL_VERIFY_SUBJECT');
                }
                break;
            case 'order-paypal-review':
                $subject = Yii::t('email', 'TEXT_PAYPAL_PAYMENT_REVIEW_SUBJECT');
                if (isset($this->data['customer']['payment_review']) && !empty($this->data['customer']['payment_review']) && $this->data['customer']['payment_review'] == 'paypal-echeck') {
                    $subject = Yii::t('email', 'TEXT_PAYPAL_PAYMENT_ECHECK_SUBJECT');
                }
                break;
            case 'order-code-cancel-issue':
            case 'order-cancel-issue':
                $subject = Yii::t('email', 'TEXT_ISSUE_EMAIL_SUBJECT', ['orderId' => $this->data['orders']['orders_id']]
                );
                if ($this->data['orders']['customers_request'] == 'cancel') {
                    $subject = Yii::t(
                        'email',
                        'TEXT_CANCEL_EMAIL_SUBJECT',
                        ['orderId' => $this->data['orders']['orders_id']]
                    );
                }
                break;
            case 'order-cancel-customer':
                $subject = Yii::t(
                    'email',
                    'TEXT_CANCEL_EMAIL_SUBJECT',
                    ['orderId' => $this->data['orders']['orders_id']]
                );
                break;
            case 'order-low-rating':
                $subject = Yii::t('email', 'TEXT_EMAIL_REVIEW_SUBJECT');
                break;
        }

        return (!empty($this->data['email_subject_prefix'])) ? $this->data['email_subject_prefix'] . ' ' . $subject : $subject;
    }
}