<?php

namespace micro\interfaces;

use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use offgamers\base\traits\GuzzleTrait;

class TaskJob extends BaseObject implements JobInterface
{
    use GuzzleTrait;
    public $data;
    public $start_time;

    public function execute($queue)
    {
        /* @var $model bool|\offgamers\base\models\ms\BaseMsTrait */
        $class = '\offgamers\base\models\ms\\' . $this->data['service'];
        $model = new $class();
        $model->customRequest($this->data['controller'], $this->data['action'], $this->data['params']);
    }

}