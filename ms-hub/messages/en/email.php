<?php

return [
    'TEXT_EMAIL_SUBJECT' => 'Order Update {orderId}',
    'TEXT_EMAIL_DELIVERED_TITLE' => 'Your order has been delivered',
    'TEXT_EMAIL_PART_DELIVERED_TITLE' => 'Order delivered, few more to go.',
    'TEXT_EMAIL_PART_DELIVERED_SUBTITLE' => 'The remaining items will be delivered to you soon. Thank you for your support and patience.',
    'TEXT_EMAIL_CANCELED_TITLE' => 'Your order has been canceled',
    'TEXT_EMAIL_GREETINGS' => 'Dear {firstname},',
    'TEXT_EMAIL_VIEW_ORDER' => 'View order details',
    'TEXT_EMAIL_ORDER_SUMMARY' => 'Order Summary',
    'TEXT_EMAIL_ORDER_NUMBER' => 'Order Number',
    'TEXT_EMAIL_ORDER_DATE' => 'Order Date',
    'TEXT_EMAIL_ORDER_PM' => 'Payment Method',
    'TEXT_EMAIL_ORDER_AMOUNT' => 'Amount',
    'TEXT_EMAIL_DELIVERED' => 'Delivered',
    'TEXT_EMAIL_RESTOCK' => 'Restock',
    'TEXT_EMAIL_DOWNLOAD_INVOICE' => 'Download order invoice',
    'TEXT_EMAIL_COMPENSATION_DELIVERY' => 'The following compensated items have been delivered:',
    'TEXT_EMAIL_SUPPORT' => 'If you need assistance, drop us an email at <a href="mailto:{emailSupport}" alt="{emailSupport}" title="{emailSupport}" style="color: #3bb3ff; text-decoration: none;">{emailSupport}</a> and provide us your order number or visit our <a href="{urlSupport}" target="_blank" alt="support" title="support" style="color: #3bb3ff; text-decoration: none;">support center</a>.',
    'TEXT_EMAIL_SIGNATURE' => '<br><br><br>Best regards,<br>OffGamers Team',

    // Moneybookers verify
    'TEXT_MONEYBOOKERS_VERIFY_SUBJECT' => 'Moneybookers Account Verification',
    'TEXT_MONEYBOOKERS_VERIFY_SEND_SUBJECT' => 'Moneybookers Account Verification Notice',
    'TEXT_MONEYBOOKERS_BODY' => '<br><br>You have made a payment of <span style="font-weight: 500; color: #55575f;">{ordersTotal}</span> to OffGamers.com using the following Moneybookers account (<span style="font-weight: 500; color: #55575f;">{payerEmail}</span>). Kindly verify this email address to proceed with this order.<br><br><a href="{verifyLink}" target="_blank" alt="verify this email" title="verify this email" style="display: inline-block; color: #3bb3ff; border: 1px solid #3bb3ff; font-size:14px; line-height: 18px; text-decoration: none; border-radius: 20px; padding: 10px 20px; cursor: pointer; margin-bottom: 10px;">Verify {payerEmail}</a><br>or use this link: <a href="{verifyLink}" target="_blank" alt="verify this email" title="verify this email" style="color: #3bb3ff; text-decoration: none;">{verifyLink}</a><br><br><br>This measure is taken to protect Moneybookers account owners from unauthorized charges. It is a one-time process unless there is a change to your Moneybookers email.',
    'TEXT_MONEYBOOKERS_DIFF_EMAIL_BODY' => '<br><br>You have made a payment of <span style="font-weight: 500; color: #55575f;">{ordersTotal}</span> to OffGamers.com using the following Moneybookers account (<span style="font-weight: 500; color: #55575f;">{payerEmail}</span>).<br><br>Please verify your Moneybookers account and follow the available instructions that was sent to (<span style="font-weight: 500; color: #55575f;">{custEmail}</span>). Once verified, we will proceed with your order. If you are unable to locate the email, please check your spam folders.',

    // Paypal verify
    'TEXT_PAYPAL_VERIFY_SUBJECT' => 'PayPal Account Verification',
    'TEXT_PAYPAL_VERIFY_SEND_SUBJECT' => 'PayPal Account Verification Notice',
    'TEXT_PAYPAL_BODY' => '<br><br>You have made a payment of <span style="font-weight: 500; color: #55575f;">{ordersTotal}</span> to OffGamers.com using the following PayPal account (<span style="font-weight: 500; color: #55575f;">{payerEmail}</span>). Kindly verify this email address to proceed with this order.<br><br><a href="{verifyLink}" target="_blank" alt="verify this email" title="verify this email" style="display: inline-block; color: #3bb3ff; border: 1px solid #3bb3ff; font-size:14px; line-height: 18px; text-decoration: none; border-radius: 20px; padding: 10px 20px; cursor: pointer; margin-bottom: 10px;">Verify {payerEmail}</a><br>or use this link: <a href="{verifyLink}" target="_blank" alt="verify this email" title="verify this email" style="color: #3bb3ff; text-decoration: none;">{verifyLink}</a><br><br><br>This measure is taken to protect PayPal account owners from unauthorized charges. It is a one-time process unless there is a change to your PayPal email.',
    'TEXT_PAYPAL_DIFF_EMAIL_BODY' => '<br><br>You have made a payment of <span style="font-weight: 500; color: #55575f;">{ordersTotal}</span> to OffGamers.com using the following PayPal account (<span style="font-weight: 500; color: #55575f;">{payerEmail}</span>).<br><br>Please verify your PayPal account and follow the available instructions that was sent to (<span style="font-weight: 500; color: #55575f;">{custEmail}</span>). Once verified, we will proceed with your order. If you are unable to locate the email, please check your spam folders.',

    // PayPal Payment Review
    'TEXT_PAYPAL_PAYMENT_REVIEW_SUBJECT' => 'PayPal Payment Review',
    'TEXT_PAYPAL_PAYMENT_REVIEW_BODY' => '<br><br>Your order <span style="font-weight: 500; color: #55575f;">{orderId}</span> is currently under review by PayPal. PayPal is holding your payment for review as a security measure and it might take up to 72 hours for the funds to clear. As soon as Paypal notify us that the review is cleared, we will proceed with your order accordingly.<br><br>If you would like to cancel the payment for this order, please contact PayPal for assistance. Read more about <a href="{paypalUrl}" target="_blank" alt="PayPal Payment Review" title="PayPal Payment Review" style="color: #3bb3ff; text-decoration: none;">PayPal Payment Review</a>.',

    // PayPal Payment Echeck
    'TEXT_PAYPAL_PAYMENT_ECHECK_SUBJECT' => 'PayPal Payment e-Check',
    'TEXT_PAYPAL_PAYMENT_ECHECK_BODY' => '<br><br>Your order <span style="font-weight: 500; color: #55575f;">{orderId}</span> is an eCheck that will take up to 3-6 business days for the funds to clear. Once the PayPal eCheck clears, we will notify you immediately.<br><br>If you do not wish to wait for the eCheck to clear, please contact PayPal directly to cancel the eCheck.',

    // Cancellation and Issue with order
    'TEXT_CANCEL_EMAIL_SUBJECT' => 'Order Cancellation Request: Order {orderId}',
    'TEXT_CANCEL_EMAIL_DESC' => '<br><br>Customer has requested to cancel order.',
    'TEXT_CANCEL_EMAIL_REFUND_METHOD' => 'Refund Method',
    'TEXT_CANCEL_EMAIL_REASON' => 'Reason',
    'TEXT_CANCEL_EMAIL_COMMENTS' => 'Additional comments or feedbacks:',
    'TEXT_ISSUE_EMAIL_SUBJECT' => 'Order Issue: Order {orderId}',
    'TEXT_ISSUE_EMAIL_DESC' => '<br><br>Customer has order with issue.',
    'TEXT_OCR_TO_CUSTOMER' => '<br>Thank you for contacting our help desk, we have received your request for the cancellation of :',
    'TEXT_OCR_TO_CUSTOMER_EXTRA' => 'The order is now placed under review. Once we have determined the product defect, delivered amount or work completed amount we will process your request at the soonest time possible.',
    'TEXT_OCR_CUSTOMER' => '<br>This email is notify you of the cancellation of :',

    // Refund PG/SC
    'TEXT_EMAIL_ORDER_REFUND_TITLE' => 'Your order has been canceled',
    'TEXT_EMAIL_ORDER_REFUND_PG' => 'We would like to inform you that this order <span style="font-weight: 500;">{orderId}</span> has been canceled.<br><br>Refund to PayPal account will take within 48 hours and the funds should be available to you within 1-5 business days. Refund to credit card and other payment methods can take up to 14 business days for the amount to reflect in your account.',
    'TEXT_EMAIL_ORDER_REFUND_SC' => 'We will issue a refund in the form of Store Credits and it will be available to you within 1-5 business days. You can then place a new order with the available Store Credits.',
    'TEXT_EMAIL_REFUND_TO' => 'Refund to',
    'TEXT_EMAIL_REFUND_CREDIT_PERIOD' => 'Credit period',
    'TEXT_EMAIL_REFUND_CREDIT_PERIOD_SC' => '1 - 5 business days',
    'TEXT_EMAIL_REFUND_CREDIT_PERIOD_PG' => '14 business days',


    'TEXT_REVIEW_EMAIL_CONTENT' => '<br><br>Thank you for your review on order <span style="font-weight: 500; color: #55575f;">{orderId}</span>. We apologize that our service did not satisfy your expectations and we would like the opportunity to talk and investigate your feedback further.',
    'TEXT_EMAIL_REVIEW_SUBJECT' => 'Got a minute?',
];
