<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "invoice".
 *
 * @property string $invoice_id
 * @property string $invoice_number
 * @property string $invoice_type
 * @property int $orders_id
 * @property string $invoice_file_domain
 * @property string $invoice_file_path
 * @property string $file_raw_data Data store in json format
 * @property string $created_datetime
 * @property string $last_modified_datetime
 */
class Invoice extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     * @throws \yii\base\InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function tableName()
    {
        return 'invoice';
    }


    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_datetime',
                'updatedAtAttribute' => 'last_modified_datetime',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'integer'],
            [['file_raw_data'], 'required'],
            [['file_raw_data'], 'string'],
            [['created_datetime', 'last_modified_datetime'], 'safe'],
            [['invoice_number', 'invoice_file_domain'], 'string', 'max' => 32],
            [['invoice_type'], 'string', 'max' => 16],
            [['invoice_file_path'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'invoice_id' => 'Invoice ID',
            'invoice_number' => 'Invoice Number',
            'invoice_type' => 'Invoice Type',
            'orders_id' => 'Orders ID',
            'invoice_file_domain' => 'Invoice File Domain',
            'invoice_file_path' => 'Invoice File Path',
            'file_raw_data' => 'File Raw Data',
            'created_datetime' => 'Created Datetime',
            'last_modified_datetime' => 'Last Modified Datetime',
        ];
    }
}
