<?php

namespace micro\models;

use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "sqs_process_track".
 *
 * @property int $id
 * @property int $process_id
 * @property string $input
 * @property string $process
 * @property int $created_at
 * @property int $updated_at
 */
class SqsProcessTrack extends \yii\db\ActiveRecord
{
    public $start_time;
    public $last_process_time;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sqs_process_track';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_log');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['process_id', 'input', 'process'], 'required'],
            [['created_at', 'updated_at'], 'integer'],
            [['input'], 'string'],
            [['process_id', 'process'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'process_id' => 'Process ID',
            'input' => 'Input',
            'process' => 'Process',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function updateProcess($process)
    {
        $this->process = $process;
        // Send Slow Process Notification
        $diff = microtime(true) - $this->last_process_time;
        if ($diff >= 3) {
            Yii::$app->slack->send('SQS Slow Process (' . $diff .' seconds)', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($this->getAttributes(null, ['input']))
                )
            ));
        }
        $this->save();
        $this->last_process_time = microtime(true);
    }

    public function beforeSave($insert){
        if($insert){
            $diff = time() - $this->start_time;
            $this->last_process_time = microtime(true);
            if ($diff >= 2) {
                Yii::$app->slack->send('PHP Proc Open Slow (' . $diff .' seconds)', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode($this->getAttributes(null, ['input']))
                    )
                ));
            }
        }
        return parent::beforeSave($insert);
    }
}
