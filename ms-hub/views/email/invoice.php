<?php

$domain = Yii::$app->params['frontend.domain'];

$request_e_invoice = "";
if (isset($data['customer']['mobile_country_code']) && $data['customer']['mobile_country_code'] == "60") {
    $request_e_invoice = "To request an e-invoice for this order, please complete this <a href='" . \Yii::$app->params['tax.info.einvoice'] . "' target='_blank'>form</a>.<br />";
}

echo "Thank you.
                    <br />
                    <br />
                        Hello " . $data['customer']['name'] . "
                    <br />
                    <br />
                        Thank you for shopping with us. We have attached a copy of the invoice for your reference. 
                    <br />
                    <br />
                        You may also login to your OffGamers account to see your <a href='{$domain}/account/purchase/order/{$data['checkout_info']['order_number']}'>Order History</a>. 
                    <br />" . $request_e_invoice . "
                    <br />
                        Thanks again for shopping at " . $data['company']['name'] . ". For any assistance, email us at <a href='mailto:<EMAIL>' target='_blank'><EMAIL></a> and we will get back to you. 
                    <br />
                    <br />
                        Regards, 
                    <br />
                        " . $data['company']['name'] . "
                     <br />
                     <br />";