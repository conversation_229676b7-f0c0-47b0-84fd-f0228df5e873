<?php

$domain = Yii::$app->params['frontend.domain'];
$emailSupport = Yii::$app->params['support.email'];
$urlSupport = Yii::$app->params['support.url'];
$rating = (isset(Yii::$app->params['order.ratingUrl']) && Yii::$app->params['order.ratingUrl']) ? Yii::$app->params['order.ratingUrl'] : $domain . '/account/purchase/order/' . $data['orders']['orders_id'] . '/review';

?>
<body style="margin: 0; background-color: #f2f2f2; font-family: 'Helvetica Neue', Helvetica, Arial, 'sans-serif';">

<!-- HEAD -->
<table style="table-layout: fixed; max-width: 600px; padding-top: 20px;" width="98%" cellpadding="0" cellspacing="0"
       border="0" align="center">
    <tr><!-- LOGO -->
        <td style="padding-top: 20px; padding-bottom: 30px;" align="center">
            <img src="https://static.offgamers.com/email-assets/og-logo-2023.png" height="45"
                 alt="OffGamers | Your Gaming Alliance" title="OffGamers | Your Gaming Alliance">
        </td>
    </tr>
    <tr>
        <td style="padding-top: 50px; padding-bottom: 50px; padding-left: 30px; padding-right: 30px; background-color: #3bb3ff; border-top-left-radius: 12px; border-top-right-radius: 12px; color: #ffffff;"
            align="center">
            <img src="https://static.offgamers.com/email-assets/icon-delivered.png" height="80px" alt="delivered"
                 title="delivered">
            <span style="font-size: 24px; padding-bottom: 10px; display: block; line-height: 1.2;"><?= Yii::t('email', 'TEXT_EMAIL_DELIVERED_TITLE') ?></span>
            <br>
            <a href="<?= $domain ?>/account/purchase/order/<?= $data['orders']['orders_id'] ?>" target="_blank"
               alt="View order details" title="View order details"
               style="display: inline-block; color: #ffffff; border: 1px solid #ffffff; font-size:14px; line-height: 18px; text-decoration: none; border-radius: 20px; padding: 10px 20px; cursor: pointer;"><?= Yii::t('email', 'TEXT_EMAIL_VIEW_ORDER') ?>
                &rarr;</a>
        </td>
    </tr>
</table>

<!-- SUMMARY -->
<table style="max-width: 600px; background-color: #f6f7f9; padding: 30px; font-size: 12px; color: #85889b; line-height: 1.5;"
       cellpadding="0" cellspacing="0" border="0" align="center" width="98%">
    <tr>
        <td style="padding-bottom: 10px; vertical-align: top;">
            <?= Yii::t('email', 'TEXT_EMAIL_ORDER_NUMBER') ?>
        </td>
        <td style="padding-bottom: 10px; text-align: right; vertical-align: top;">
            <span style="color: #55575f;"><?= $data['orders']['orders_id'] ?></span>
        </td>
    </tr>
    <tr>
        <td style="padding-bottom: 20px; border-bottom: 1px solid #e5e8ee; vertical-align: top;">
            <?= Yii::t('email', 'TEXT_EMAIL_ORDER_DATE') ?>
        </td>
        <td style="padding-bottom: 20px; text-align: right; border-bottom: 1px solid #e5e8ee; vertical-align: top;">
            <span style="color: #55575f;"><?= $data['orders']['date_purchased'] ?></span>
        </td>
    </tr>

    <?php
    // Check if compensate param isset & true
    if (isset($data['orders']['compensate_products']) && $data['orders']['compensate_products']) {
        echo '<tr>
                    <td style="padding-bottom: 10px; padding-top: 20px; vertical-align: top;">
                        ' . Yii::t('email', 'TEXT_EMAIL_COMPENSATION_DELIVERY') . '
                    </td>
                    <td style="padding-bottom: 10px; text-align: right; vertical-align: top;"></td>
                </tr>';
    }

    $i = 0;
    foreach ($data['orders']['orders_products'] as $key => $value) {
        $padding = '';
        if ($i == 0) {
            $padding = 'padding-top: 20px;';
        }

        if (isset($value['products_name']) && !empty($value['products_name'])) {
            echo '<tr>
                        <td style="padding-bottom: 10px; vertical-align: top; ' . $padding . '">
                            ' . $value['products_name'] . '&nbsp;&nbsp;&nbsp;x' . $value['products_quantity'] . '
                        </td>
                        <td style="padding-bottom: 10px; text-align: right; vertical-align: top; ' . $padding . '">
                            <span style="font-weight: 500; color: #55575f;">' . ($value['products_delivery_status'] == true ? Yii::t('email', 'TEXT_EMAIL_DELIVERED') : Yii::t('email', 'TEXT_EMAIL_RESTOCK')) . '</span>
                        </td>
                    </tr>';
            $i++;
        }
    }
    ?>
</table>

<!-- RATING -->
<table style="max-width: 600px; background-color: #ffffff;" width="98%" cellpadding="0" cellspacing="0" border="0"
       align="center">
    <tr>
        <td style="padding-top: 50px; padding-bottom: 50px;" align="center">
            <a href="<?= $rating ?>" target="_blank" alt="rating" title="rating"
               style="color: #3bb3ff; text-decoration: none;"><img
                        src="https://static.offgamers.com/email-assets/rating280x150px.png" width="200px" alt="rating"
                        title="rating"></a>
        </td>
    </tr>
</table>

<!-- SUPPORT -->
<table style="table-layout: fixed; max-width: 600px; font-size: 14px; color: #85889b; line-height: 1.5; background-color: #ffffff; padding-top: 20px; padding-bottom: 30px; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;"
       width="98%" cellpadding="0" cellspacing="0" border="0" align="center">
    <tr>
        <td style="padding-left: 30px; padding-right: 30px;">
            <?= Yii::t('email', 'TEXT_EMAIL_SUPPORT', ['emailSupport' => $emailSupport, 'urlSupport' => $urlSupport]) ?>
        </td>
    </tr>
</table>

<!-- FOOTER -->
<table style="table-layout: fixed; max-width: 600px; padding-top: 30px; padding-bottom: 50px; font-size: 12px; color: #85889b; text-align: center;"
       width="90%" cellpadding="0" cellspacing="0" border="0" align="center">
    <tr>
        <td>
            Copyright © <?= date('Y') ?> <a href="<?= $domain ?>" target="_blank" alt="www.offgamers.com"
                                            title="www.offgamers.com" style="color: #3bb3ff; text-decoration: none;">OffGamers</a>.
            All rights reserved.
        </td>
    </tr>
</table>
</body>