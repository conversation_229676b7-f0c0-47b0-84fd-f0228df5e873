<?php

$domain = Yii::$app->params['frontend.domain'];
$emailSupport = Yii::$app->params['support.email'];
$urlSupport = Yii::$app->params['support.url'];

?>
<body style="margin: 0; background-color: #f2f2f2; font-family: 'Helvetica Neue', Helvetica, Arial, 'sans-serif';">
    
    <!-- HEAD -->
    <table style="table-layout: fixed; max-width: 600px; padding-top: 20px;" width="90%" cellpadding="0" cellspacing="0" border="0" align="center">
        <tr><!-- LOGO -->
            <td style="padding-top: 20px; padding-bottom: 30px;" align="center">
                <img src="https://static.offgamers.com/email-assets/og-logo-2023.png" height="45" alt="OffGamers | Your Gaming Alliance" title="OffGamers | Your Gaming Alliance">
            </td>
        </tr>
        <tr>
            <td style="font-size: 14px; color: #85889b; line-height: 1.5; padding-top: 50px; padding-bottom: 30px; padding-left: 30px; padding-right: 30px; background-color: #ffffff; border-top-left-radius: 12px; border-top-right-radius: 12px; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                <?= Yii::t('email', 'TEXT_EMAIL_GREETINGS', ['firstname' => $data['customer']['firstname']]) ?>
                
                <?php 
                    if (isset($data['customer']['payment_verification']) && !empty($data['customer']['payment_verification'])) {
                        $paypalBody = 'TEXT_PAYPAL_BODY';
                    } else {
                        $paypalBody = 'TEXT_PAYPAL_DIFF_EMAIL_BODY';
                    }
                    
                    echo Yii::t('email', $paypalBody, [
                        'ordersTotal' => $data['orders']['orders_total'],
                        'payerEmail' => $data['customer']['payer_email'],
                        'custEmail' => $data['customer']['email'],
                        'verifyLink' => $verifyLink
                    ]);
                ?>
                
                <br><br><?= Yii::t('email', 'TEXT_EMAIL_SUPPORT', ['emailSupport' => $emailSupport, 'urlSupport' => $urlSupport]) ?>
                
                <?= Yii::t('email', 'TEXT_EMAIL_SIGNATURE') ?>
            </td>
        </tr>
    </table>
    
    <!-- FOOTER -->
    <table style="table-layout: fixed; max-width: 600px; padding-top: 30px; padding-bottom: 50px; font-size: 12px; color: #85889b; text-align: center;" width="90%" cellpadding="0" cellspacing="0" border="0" align="center">
        <tr>
            <td>
                Copyright © <?= date('Y') ?> <a href="<?= $domain ?>" target="_blank" alt="www.offgamers.com" title="www.offgamers.com" style="color: #3bb3ff; text-decoration: none;">OffGamers</a>. All rights reserved.
            </td>
        </tr>
    </table>
</body>