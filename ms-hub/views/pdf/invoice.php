<table cellpadding="0" cellspacing="0" style="width: 100%;">
    <tr>
        <td style="width: 200px; padding-right: 20px;">
            <img src="data:image/png;base64,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">
        </td>
        <td style="vertical-align: middle; padding-left: 20px;">
            <table cellpadding="0" cellspacing="0" style="width: 100%; font-size: 10px;">
                <tr>
                    <td style="width: 100%; height: 40px; border-left: 1px dotted gray; padding-left: 20px;">
                        Website: <?php echo $company["website"]; ?>
                    </td>
                </tr>
            </table>
        </td>
        <?php if (isset($checkout_info["payment_code"]) && ($checkout_info["payment_code"] == "jompay.jompay")) { ?>
            <td style="width: 250px;">
                <table cellpadding="0" cellspacing="0" style="width: 100%; padding-top: 10px; padding-bottom: 10px;">
                    <tr>
                        <td style="text-align: left; padding-right: 10px;">
                            <img src="data:image/png;base64,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">
                        </td>
                        <td style="border: 2px solid black; width: 100%; padding: 10px; font-size: 11px;">
                            <b>Biller Code: </b>40220<br />
                            <b>Ref-1: </b><?php echo (isset($customer["id"]) ? $customer["id"] : ""); ?><br />
                            <b>Ref-2: </b><?php echo $checkout_info["order_number"]; ?>
                        </td>
                    </tr>
                </table>
                <span style="font-size: 10px;"><b>JomPAY</b> online at Internet and Mobile Banking with your Current or Savings account</span>
            </td>
        <?php } ?>
    </tr>
</table>

<div style="height: 10px;"></div>
<div class="text-right" style="font-size: 48px;text-align:right;">INVOICE</div>
<div style="height: 10px;"></div>

<table cellpadding="0" cellspacing="0" style="width: 100%; vertical-align: top; font-size: 11px;">
    <tr>
        <td style="width: 45%;">
            <?php echo $customer["name"]; ?> <?php echo (isset($customer["id"]) ? "(" . $customer["id"] . ")" : ""); ?><br />
            <?php echo $customer["address"]; ?><br />
        </td>
        <td style="width: 10%;"></td>
        <td style="width: 45%;">
            <table cellpadding="0" cellspacing="0" style="vertical-align: top;">
                <tr>
                    <td style="width: 100px;">Invoice No.</td>
                    <td style="padding: 0 5px;">:</td>
                    <td><?php echo $checkout_info["invoice_number"]; ?></td>
                </tr>
                <tr>
                    <td style="width: 100px;">Order No.</td>
                    <td style="padding: 0 5px;">:</td>
                    <td><?php echo $checkout_info["order_number"]; ?></td>
                </tr>
                <tr>
                    <td style="width: 100px;">Date</td>
                    <td style="padding: 0 5px;">:</td>
                    <td><?php echo $checkout_info["invoice_date"]; ?></td>
                </tr>
                <tr>
                    <td style="width: 100px;">Payment Method</td>
                    <td style="padding: 0 5px;">:</td>
                    <td><?php echo $checkout_info["payment_method"]; ?></td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<div style="height: 30px;"></div>

<table cellpadding="0" cellspacing="0" style="width: 100%; font-size: 12px;">
    <tr>
        <td style="width: 45%; color: blue; border-bottom: 1px solid black; vertical-align: middle; padding: 10px 0px 10px 10px;">PRODUCT DESCRIPTION</td>
        <td style="width: 15%; color: blue; border-bottom: 1px solid black; vertical-align: middle; text-align: center; padding: 10px 0;">QTY.</td>
        <td style="width: 20%; color: blue; border-bottom: 1px solid black; vertical-align: middle; text-align: right; padding: 10px 0;">UNIT PRICE (<?php echo $checkout_info["currency"]; ?>)</td>
        <td style="width: 20%; color: blue; border-bottom: 1px solid black; vertical-align: middle; text-align: right; padding: 10px 10px 10px 0px;">SUBTOTAL (<?php echo $checkout_info["currency"]; ?>)</td>
    </tr>
    <?php for ($i = 0, $cnt = count($product); $cnt > $i; $i++) { ?>
        <?php $line = ($i > 0 ? "border-top: 1px dotted gray;" : ""); ?>
        <tr>
            <td style="<?php echo $line; ?> vertical-align: middle; padding: 10px 0px 10px 10px;"><?php echo $product[$i]["name"]; ?></td>
            <td style="<?php echo $line; ?> vertical-align: middle; text-align: center; padding: 10px 0;"><?php echo $product[$i]["quantity"]; ?></td>
            <td style="<?php echo $line; ?> vertical-align: middle; text-align: right; padding: 10px 0;"><?php echo $product[$i]["unit_price"]; ?></td>
            <td style="<?php echo $line; ?> vertical-align: middle; text-align: right; padding: 10px 10px 10px 0px;"><?php echo $product[$i]["total_price"]; ?></td>
        </tr>
    <?php } ?>
    <?php for ($i = 0, $cnt = (10 - $cnt); $cnt > $i; $i++) { ?>
        <tr><td colspan="4" style="padding: 10px 0;">&nbsp;</td></tr>
    <?php } ?>
    <?php if (isset($remark) && !empty($remark)) { ?>
        <tr>
            <td colspan="4">
                <table cellpadding="0" cellspacing="0" style="width: 100%; padding: 10px 10px; border: 1px dashed #bac4ca; color: #9292a2">
                    <tr><td><?php echo $remark; ?></td></tr>
                </table>
            </td>
        </tr>
        <tr><td colspan="4" style="padding: 10px 0;">&nbsp;</td></tr>
    <?php } ?>
    <?php for ($i = 0, $cnt = count($order_total); $cnt > $i; $i++) { ?>
        <?php for ($j = 0, $amt = count($order_total[$i]); $amt > $j; $j++) {
            if(isset($order_total[$i][$j]["type"]) &&  $order_total[$i][$j]["type"] == 'ot_gst' && 0 >=  $order_total[$i][$j]["value"]){
                continue;
            }
            $line = (($i > 0) && ($j == 0) ? "border-top: 1px dotted gray;" : "");
        ?>
            <tr>
                <?php
                $tax_title = $order_total[$i][$j]['title'];
                if (strtoupper($checkout_info['currency']) == 'IDR' && isset($order_total[$i][$j]['type']) && $order_total[$i][$j]['type'] == 'ot_gst') {
                    $tax_title = preg_replace('/\s*\(\d+%\)/', '', $tax_title);
                }
                ?>
                <td colspan="3" style="<?php echo $line; ?> vertical-align: middle; text-align: right; padding: 5px 0px 5px 10px;"><?php
                    echo (strpos($tax_title, 'Subtotal') !== FALSE ? 'Subtotal' : $tax_title);
                ?></td>
                <td style="<?php echo $line; ?> vertical-align: middle; text-align: right; padding: 5px 10px 5px 0px;"><?php echo $order_total[$i][$j]["value"]; ?></td>
            </tr>
        <?php } ?>
    <?php } ?>
    <tr bgcolor="#337ab7">
        <td colspan="3" style="vertical-align: middle; text-align: right; padding: 5px 0px 5px 10px; color: white;">Total</td>
        <td style="vertical-align: middle; text-align: right; padding: 5px 10px 5px 0px; color: white;"><?php echo $checkout_info["total"]; ?></td>
    </tr>
</table>