<?php

/**
 * Yii bootstrap file.
 * Used for enhanced IDE code autocompletion.
 */
class Yii extends \yii\BaseYii
{
    /**
     * @var BaseApplication|WebApplication the application instance
     */
    public static $app;
}

/**
 * Class BaseApplication
 * Used for properties that are identical for both WebApplication and ConsoleApplication
 *
 */
abstract class BaseApplication extends yii\base\Application
{
}

/**
 * Class WebApplication
 * Include only Web application related components here
 *
 * @property \yii\web\User $user The user component. This property is read-only. Extended component.
 * @property \yii\web\Response $response The response component. This property is read-only. Extended component.
 * @property \yii\web\ErrorHandler $errorHandler The error handler application component. This property is read-only. Extended component.
 *
 * @property \yii\mutex\MysqlMutex $mutex Mysql Mutex lock component. Use to obtain exclusive lock across application
 */
class WebApplication extends yii\web\Application
{
}