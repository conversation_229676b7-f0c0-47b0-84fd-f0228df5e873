<?php

namespace micro\components;

use Exception;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use micro\models\ApiRestockRequest;
use micro\models\CustomProductsCode;
use micro\models\CustomProductsCodeLog;
use micro\models\RestockRequest;
use offgamers\base\components\AWSS3;
use offgamers\base\models\DevDebugLog;
use offgamers\base\models\ms\Product;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class CdKeyCom
{
    const CODE_DELIVERY_MUTEX_LOCK = 'custom_products_code_delivery/%s';

    /**
     * @property AWSS3 $s3
     */
    public AWSS3 $s3;

    public function __construct()
    {
        $this->s3 = Yii::$app->aws->getS3('BUCKET_CD_KEY');
    }

    public function createCustomProductsCode($products_id, $extra_args = [])
    {
        $orders_products_id = (!empty($extra_args['orders_products_id']) ? $extra_args['orders_products_id'] : 0);
        $status_id = (!empty($extra_args['status']) ? $extra_args['status'] : -1);
        $remarks = (!empty($extra_args['remarks']) ? $extra_args['remarks'] : '');

        $model = new CustomProductsCode();
        $model->products_id = $products_id;
        $model->orders_products_id = $orders_products_id;
        $model->custom_products_code_viewed = 0;
        $model->status_id = $status_id;
        $model->to_s3 = -1;
        $model->remarks = $remarks;

        $model->save();
        return $model;
    }

    public function getCustomProductsCode($cpc_id, $products_id, $extra_args)
    {
        $model = null;

        if ((int)$cpc_id > 0) {
            $model = CustomProductsCode::findOne(['custom_products_code_id' => $cpc_id]);
        }

        if (!$model) {
            $model = $this->createCustomProductsCode($products_id, $extra_args);
        }

        return $model;
    }

    public function uploadCdKey($products_id, $rawData, $extra_args = [])
    {
        $file_type = (!empty($extra_args['file_type']) ? $extra_args['file_type'] : 'soft');
        $uploader = (!empty($extra_args['uploader']) ? $extra_args['uploader'] : 'system');
        $cpc_id = (!empty($extra_args['custom_products_code_id']) ? $extra_args['custom_products_code_id'] : 0);
        $status_id = (!empty($extra_args['status']) ? $extra_args['status'] : 1);

        $model = $this->getCustomProductsCode($cpc_id, $products_id, $extra_args);
        $model->rawData = $rawData;
        $model->save();

        if ($model->custom_products_code_id) {
            $model->file_name = (string)$model->custom_products_code_id;
            $model->file_type = $file_type;
            if ($this->uploadToS3($model)) {
                $model->to_s3 = 1;
                $model->code_uploaded_by = $uploader;
                $model->status_id = $status_id;
                $model->save();
            }
            // unset data after upload completed
            $model->rawData = null;
        }
        return $model;
    }

    public function uploadToS3($cpc_model)
    {
        $path = date('Ym', strtotime($cpc_model->code_date_added)) . '/' . $cpc_model->products_id . '/' . $cpc_model->custom_products_code_id . '.key';
        $data = $this->encryptKey($cpc_model->rawData);
        try {
            for ($i = 0; $i < 3; $i++) {
                if ($this->s3->saveContent($path, $data)) {
                    return true;
                }
                throw new \Exception("Failed to upload CDKey");
            }
        } catch (Exception $e) {
            $debug_msg = [
                'custom_products_code_id' => $cpc_model->custom_products_code_id,
                'file_type' => $cpc_model->file_type,
                'content' => $data
            ];
            DevDebugLog::generateDebugLog('Fail to upload cdkey', $debug_msg);
        }
        return false;
    }

    public function encryptKey($rawData)
    {
        $rawData = base64_encode($rawData);
        $prefix = sprintf("%020d", strlen($rawData));

        $key = Yii::$app->params['cpc.secure.key'];
        $iv = Yii::$app->params['cpc.secure.iv'];

        if (strlen($rawData) % 8) {
            $rawData = str_pad($rawData, strlen($rawData) + 8 - strlen($rawData) % 8, "\0");
        }

        $encrypted_data = openssl_encrypt($rawData, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);

        return $prefix . base64_encode($encrypted_data);
    }

    public function decryptKey($rawData)
    {
        $key = Yii::$app->params['cpc.secure.key'];
        $iv = Yii::$app->params['cpc.secure.iv'];

        $theData = substr($rawData, 20);
        $theData = openssl_decrypt($theData, "AES-256-CBC", $key, OPENSSL_ZERO_PADDING, $iv);

        $theData = rtrim($theData, "\0");
        $theData = base64_decode($theData);

        return $theData;
    }

    public function fetchMultipleCdKeyFromS3($cdkey_list, $retry = 0)
    {
        try {
            // Specify the bucket and file keys you want to request
            $fileKeys = $cdkey_list;

            // Create a Guzzle HTTP client
            $httpClient = new Client();

            $requests = [];
            $results = [];

            foreach ($cdkey_list as $cpc_model) {
                $path = date('Ym', strtotime($cpc_model->code_date_added)) . '/' . $cpc_model->products_id . '/' . $cpc_model->custom_products_code_id . '.key';
                $requests[] = $this->s3->getContentUrl($path, true, 600);
            }

            $pool_list = function ($requests) {
                for ($i = 0; $i < count($requests); $i++) {
                    yield new Request('GET', $requests[$i]);
                }
            };

            $pool = new Pool($httpClient, $pool_list($requests), [
                'concurrency' => 30,
                'fulfilled' => function ($response, $index) use (&$results, $fileKeys) {
                    $cpc_id = $fileKeys[$index]->custom_products_code_id;
                    // Handle the response for each file
                    if ($response->getStatusCode() === 200) {
                        // Successful response
                        $fileContents = (string)$response->getBody();
                        $results[$cpc_id] = $fileContents;
                    }
                },
                'rejected' => function ($reason, $index) use (&$results, $fileKeys) {
                    //TODO reprocess when partial failed
                    throw new Exception('Failed to obtain CDKey');
                },
            ]);

            $pool->promise()->wait();

            return $results;
        } catch (Exception $e) {
            if ($retry < 3) {
                $retry++;
                return $this->fetchMultipleCdKeyFromS3($cdkey_list, $retry);
            } else {
                Yii::$app->slack->send('Failed to get cdkey from S3', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode(array_keys($fileKeys)),
                    ),
                ));
            }
        }
    }

    public function processG2GCdkeyDelivery($data)
    {
        $mutex_lock = sprintf(self::CODE_DELIVERY_MUTEX_LOCK, $data['products_id']);
        $mutex_timeout = 5;
        $status_message = '';

        $delivered_array = [];
        $success_delivered_pin = [];
        $reprocess_restock_queue = false;

        if (Yii::$app->mutex->acquire($mutex_lock, $mutex_timeout)) {
            $g2g_order_id = $data['order_id'];
            $products_id = $data['products_id'];
            // Check Existing restock request
            $restock_request = RestockCom::getRestockProfile('G2G', $data['reseller_id'], $g2g_order_id);

            // If got reserved stock, get from restock request
            if ($restock_request === null) {
                // If no reserved stock, get from available stock from DB
                $cpc_list = CustomProductsCode::find()
                    ->select('custom_products_code_id')
                    ->where(['products_id' => $products_id, 'status_id' => 1, 'file_type' => 'soft'])
                    ->from('custom_products_code')
                    ->limit($data['pending_qty'])
                    ->asArray()
                    ->all();

                $cpc_list = ArrayHelper::getColumn($cpc_list, 'custom_products_code_id');

                $success_delivered_pin = $this->deliverSoftPinByCustomProductsCodeId($cpc_list, 1, -4);
                $reprocess_restock_queue = true;
            } elseif ($restock_request) {
                if ($restock_request->status == 0) {
                    $status_message = 'RESTOCK_IN_PROGRESS';
                } else {
                    $cpc_list = RestockCom::getReservedCustomProductCode($restock_request->restock_request_id);
                    $success_delivered_pin = $this->deliverSoftPinByCustomProductsCodeId($cpc_list, -5, -4);

                    // If request to reprocess existing restock queue
                    if (!empty($data['reprocess_restock_request'])) {
                        $reprocess_restock_queue = true;
                    }
                }
            }

            // If got available custom_products_code
            if (count($success_delivered_pin)) {
                $delivered_array = $this->updateG2GDeliveryLog($g2g_order_id, $products_id, $success_delivered_pin);
            }

            // Hack to prevent G2G Continuous notification causing deadloop on restock attempt
            if ($reprocess_restock_queue) {
                if (count($delivered_array) != $data['pending_qty']) {
                    // If existing inventory not enough
                    $restock_qty = $data['pending_qty'] - count($delivered_array);
                    if($this->processRestockQueue('G2G', $data['reseller_id'], $g2g_order_id, $products_id, $restock_qty, $data['order_currency'], $data['product_price_in_currency'], $data['product_price_in_usd'])){
                        $status_message = 'RESTOCK_IN_PROGRESS';
                    }
                    else{
                        $status_message = 'OUT_OF_STOCK';
                    }
                }
                else{
                    $status_message = 'FULLY_DELIVERED';
                }
            }

            Yii::$app->mutex->release($mutex_lock);
        }

        // Return available Key
        return [
            'status_message' => $status_message,
            'delivery_pin' => $delivered_array
        ];
    }

    private function deliverSoftPinByCustomProductsCodeId($custom_products_code_list, $from_status = 1, $to_status = 0)
    {
        $updated_array = [];
        foreach ($custom_products_code_list as $cpc_id) {
            if (CustomProductsCode::updateAll(['status_id' => $to_status],
                [
                    'custom_products_code_id' => $cpc_id,
                    'status_id' => $from_status
                ]
            )) {
                $updated_array[] = $cpc_id;
            }
        }
        return $updated_array;
    }

    private function updateG2GDeliveryLog($g2g_order_id, $products_id, $delivered_softpin_list)
    {
        $delivered_array = [];

        // Add delivered quantity to custom_products_code_log
        foreach ($delivered_softpin_list as $id) {
            $log = new CustomProductsCodeLog;
            $log->load([
                'custom_products_code_log_user' => '0',
                'custom_products_code_log_user_role' => 'admin',
                'log_ip' => Yii::$app->getRequest()->getUserIP(),
                'log_time' => date("Y-m-d H:i:s"),
                'custom_products_code_id' => $id,
                'log_system_messages' => "Changes made from G2G Order # " . $g2g_order_id . " :\n<b>CD Key Status</b>: Actual --> G2G Pull Pin"
            ], '');

            $log->save();
        }

        $cdkey_list = CustomProductsCode::findAll(['custom_products_code_id' => $delivered_softpin_list]);

        $pin_list = $this->fetchMultipleCdKeyFromS3($cdkey_list);

        foreach ($cdkey_list as $cpc_model) {
            $delivered_array[] = [
                'id' => $cpc_model->custom_products_code_id,
                'type' => $cpc_model->file_type,
                'content' => $this->decryptKey($pin_list[$cpc_model->custom_products_code_id])
            ];
        }

        // Deduct Available Quantity from products table
        if (count($delivered_array)) {
            // Prevent Error when update products qty cause delivery failed
            try {
                (new Product())->customRequest(
                    'product',
                    'g2g-stock-movement',
                    [
                        'products_id' => $products_id,
                        'custom_products_code' => $delivered_softpin_list,
                        'qty' => count($delivered_array),
                    ],
                );
            } catch (Exception $e) {
                Yii::$app->slack->send('Failed to update G2G stock movement qty (' . $g2g_order_id . ')', array(
                    array(
                        'color' => 'warning',
                        'text' => ''
                    )
                ));
            }
        }

        return $delivered_array;
    }

    public function processRestockQueue($request_profile, $request_profile_id, $request_reference_id, $products_id, $restock_qty, $order_currency, $product_price_in_currency, $product_price_in_usd)
    {
        $restock_com = new RestockCom([
            'request_profile' => $request_profile,
            'request_profile_id' => $request_profile_id,
            'request_reference_id' => $request_reference_id,
            'products_id' => $products_id,
            'restock_qty' => $restock_qty,
            'order_currency' => $order_currency,
            'product_price_in_currency' => $product_price_in_currency,
            'product_price_in_usd' => $product_price_in_usd
        ]);

        return $restock_com->processRestockRequest();
    }
}
