<?php

namespace micro\components;

use micro\models\ApiRestockRequest;
use micro\models\CustomProductsCode;
use micro\models\RestockRequest;
use offgamers\base\models\ms\Product;
use Yii;
use yii\base\BaseObject;
use yii\db\Exception;
use yii\helpers\ArrayHelper;

class RestockCom extends BaseObject
{
    public $restock_request_id;
    public $request_profile;
    public $request_profile_id;
    public $request_reference_id;

    public $products_id;
    public $restock_qty;

    public $products_name;
    public $products_model;
    public $product_cost_currency;
    public $product_cost;
    public $publisher_id;

    public $order_currency;
    public $product_price_in_currency;
    public $product_price_in_usd;

    public function processRestockRequest()
    {
        $product = (new Product())->getProductInfoByOrdersId(['products_id' => $this->products_id]);

        if (!empty($product['products_model'])) {
            $this->products_name = $product['products_name'];
            $this->products_model = $product['products_model'];
            $this->product_cost = (!empty($product['product_cost']) ? $product['product_cost'] : $product['product_original_cost']);
            $this->product_cost_currency = (!empty($product['product_cost_currency']) ? $product['product_cost_currency'] : $product['product_original_currency']);
            $this->publisher_id = $product['publisher'] ?? '';

            $restock_request = RestockRequest::find()
                ->where([
                    'request_profile' => $this->request_profile,
                    'request_profile_id' => $this->request_profile_id,
                    'request_reference_id' => $this->request_reference_id,
                ])
                ->one();

            // Create Restock Queue if not exist
            if ($restock_request === null) {
                $restock_request = new RestockRequest();
                $restock_request->request_profile = (string)$this->request_profile;
                $restock_request->request_profile_id = (string)$this->request_profile_id;
                $restock_request->request_reference_id = (string)$this->request_reference_id;
                $restock_request->status = 0;
                $restock_request->save();
            }
            else{
                $restock_request->status = 0;
                $restock_request->save();
            }

            $this->restock_request_id = $restock_request->restock_request_id;

            $this->createApiRestockRequest($restock_request->restock_request_id);
            $this->resetPendingTransaction();

            return $this->runRestockQueue();
        }

        return false;
    }

    private function resetPendingTransaction()
    {
        // Reset Margin Checking to pending
        ApiRestockRequest::updateAll(['status' => 0], ['restock_request_id' => $this->restock_request_id, 'status' => 9]);

        // Reset Preorder to failed for retry
        ApiRestockRequest::updateAll(['status' => 2], ['restock_request_id' => $this->restock_request_id, 'status' => 8]);
    }

    private function runRestockQueue()
    {
        $queue_data = [
            'request_profile' => $this->request_profile,
            'request_profile_id' => $this->request_profile_id,
            'request_reference_id' => $this->request_reference_id,
            'restock_request_id' => $this->restock_request_id,

            // Products Info
            'products_id' => $this->products_id,
            'sku' => $this->products_model,
            'products_name' => $this->products_name,
            'publisher' => $this->publisher_id,
            'product_cost' => $this->product_cost,
            'product_cost_currency' => $this->product_cost_currency,

            // Order Info / Restock Targeted Price
            'orders_currency' => $this->order_currency,
            'orders_products_currency_price' => $this->product_price_in_currency,
            'orders_products_price' => $this->product_price_in_usd,
        ];

        $sqs = Yii::$app->aws->getSQS('RESTOCK_QUEUE');

        return $sqs->pushMessage([
            'data' => $queue_data
        ]);
    }

    public static function getReservedCustomProductCode($restock_request_id, $status_id = -5)
    {
        $restock_success_list = self::getRestockSuccessCustomProductCode($restock_request_id);

        $get_reserved_cdkey = CustomProductsCode::find()
            ->select('custom_products_code_id')
            ->where(['custom_products_code_id' => $restock_success_list, 'status_id' => -5])
            ->from('custom_products_code')
            ->asArray()
            ->all();

        return ArrayHelper::getColumn($get_reserved_cdkey, 'custom_products_code_id');
    }

    public static function getRestockSuccessCustomProductCode($restock_request_id)
    {
        // Status (1 = Success, 2 = Failed)
        // Only process if status restock request has done processing
        $api_restock_request = ApiRestockRequest::find()
            ->select(['custom_products_code_id'])
            ->where([
                'restock_request_id' => $restock_request_id,
                'status' => 1,
                'is_locked' => 0
            ])
            ->asArray()
            ->all();

        return ArrayHelper::getColumn($api_restock_request, 'custom_products_code_id');
    }

    /**
     * This function
     * @param $request_restock_id
     * @return void
     * @throws Exception
     */
    protected function createApiRestockRequest($request_restock_id)
    {
        $pending_restock_qty = ApiRestockRequest::getPendingLineCountByRestockRequestId($request_restock_id);
        $create_line_count = $this->restock_qty - $pending_restock_qty;
        if ($create_line_count > 0) {
            $timestamp = time();
            $insert_data = [];
            for ($i = 0; $i < $create_line_count; $i++) {
                $insert_data[] = [$this->products_id, 0, $timestamp, $timestamp, $request_restock_id];
            }

            if (!empty($insert_data)) {
                // Insert records to `api_request_restock`
                Yii::$app->db->createCommand()->batchInsert(
                    ApiRestockRequest::tableName(),
                    ['products_id', 'status', 'created_at', 'updated_at', 'restock_request_id'],
                    $insert_data
                )->execute();
            }
        }
        elseif($create_line_count < 0){
            // TODO :: void pending transaction
        }
    }

    public static function getRestockProfile($request_profile, $request_profile_id, $request_reference_id)
    {
        return RestockRequest::findOne([
            'request_profile' => $request_profile,
            'request_profile_id' => $request_profile_id,
            'request_reference_id' => $request_reference_id,
        ]);
    }
}