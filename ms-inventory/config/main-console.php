<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
    require(__DIR__ . '/params-encoded.php')
);

return [
    'id' => 'ms-inventory',

    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log', 'restock_queue'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'mutex' => [
            'class' => 'yii\mutex\MysqlMutex',
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'DEBUG' => $params['slack.webhook.debug'],
                'BDT' => $params['slack.webhook.bdt'],
                'BDT_REPLENISH' => $params['slack.webhook.bdt.replenish'],
                'BDT_DTU' => $params['slack.webhook.bdt.dtu']
            ]
        ],
        'currency' => 'offgamers\base\components\Currency',
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'i18n' => [
            'translations' => [
                'seo' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    //'basePath' => '@app/messages',
                    'sourceLanguage' => 'en',
                    'fileMap' => [
                        'app' => 'seo.php',
                    ],
                ],
            ],
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'errorHandler' => [
            'class' => 'offgamers\base\components\ErrorHandler'
        ],
        'restock_queue' => [
            'class' => \offgamers\base\components\SQSQueue::class,
            'job_class' => '\micro\interfaces\RestockJob',
            'queue_name' => 'RESTOCK_QUEUE',
            // Reduced cache interval to prevent concurrent process
            'cache_interval' => 30,
        ]
    ]

];


