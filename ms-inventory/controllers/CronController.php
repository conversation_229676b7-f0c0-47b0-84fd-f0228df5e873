<?php

namespace micro\controllers;

use Yii;
use micro\models\ApiRestockRequest;
use micro\models\CustomProductsCode;
use micro\models\publishers\BlackHawkNetwork;
use micro\models\publishers\BlackHawkNetworkV2;
use micro\models\publishers\EcoVoucher;
use micro\models\publishers\Neosurf;
use micro\models\publishers\Xoxo;
use yii\helpers\Json;

class CronController extends \yii\console\Controller
{
    public function actionRefundBlackHawkOrder($api_restock_request_id)
    {
        $model = ApiRestockRequest::findOne($api_restock_request_id);
        $cpc_model = CustomProductsCode::findOne($model->custom_products_code_id);

        if ($cpc_model->status_id == '-1') {
            $publisher = new BlackHawkNetwork();
            $publisher->api_restock_request_id = $model->api_restock_request_id;

            try {
                $data = Json::decode(ApiRestockRequest::getExtraInfo($model->api_restock_request_id, 'REQUEST_PARAMS')['REQUEST_PARAMS'])['attribute'];
                $publisher->load($data, '');
                if ($publisher->processRequest('refund', $model->publisher_order_id) === false) {
                    throw new \Exception('Fail to refund order');
                }
            } catch (\Exception $e) {
                Yii::$app->slack->send('Failed to refund Black Hawk Order', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode([$api_restock_request_id, $e->getTraceAsString()])
                    )
                ), 'DEBUG');
            }
        }
    }

    public function actionRefundBlackHawk2Order($api_restock_request_id)
    {
        $model = ApiRestockRequest::findOne($api_restock_request_id);
        $cpc_model = CustomProductsCode::findOne($model->custom_products_code_id);

        if ($cpc_model->status_id == '-1') {
            $publisher = new BlackHawkNetworkV2();
            $publisher->api_restock_request_id = $model->api_restock_request_id;

            try {
                if ($publisher->voidTransaction($model->api_restock_request_id) === false) {
                    throw new \Exception('Fail to refund order');
                }
            } catch (\Exception $e) {
                Yii::$app->slack->send('Failed to refund Black Hawk Order', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode([$api_restock_request_id, $e->getTraceAsString()])
                    )
                ), 'DEBUG');
            }
        }
    }

    public function actionRefundEcoVoucherOrder($api_restock_request_id)
    {
        $model = ApiRestockRequest::findOne($api_restock_request_id);
        $cpc_model = CustomProductsCode::findOne($model->custom_products_code_id);

        if ($cpc_model->status_id == '-1') {
            $publisher = new EcoVoucher();
            $publisher->api_restock_request_id = $model->api_restock_request_id;

            try {
                if ($publisher->voidTransaction($model->api_restock_request_id) === false) {
                    throw new \Exception('Fail to refund order');
                }
            } catch (\Exception $e) {
                Yii::$app->slack->send('Failed to refund EcoVoucher Order', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode([$api_restock_request_id, $e->getTraceAsString()])
                    )
                ), 'DEBUG');
            }
        }
    }

    public function actionRefundNeosurfOrder($api_restock_request_id)
    {
        $model = ApiRestockRequest::findOne($api_restock_request_id);
        $cpc_model = CustomProductsCode::findOne($model->custom_products_code_id);

        if ($cpc_model->status_id == '-1') {
            $publisher = new Neosurf();
            $publisher->api_restock_request_id = $model->api_restock_request_id;

            try {
                if ($publisher->voidTransaction($model->api_restock_request_id) === false) {
                    throw new \Exception('Fail to refund order');
                }
            } catch (\Exception $e) {
                Yii::$app->slack->send('Failed to refund Neosurf Order', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode([$api_restock_request_id, $e->getTraceAsString()])
                    )
                ), 'DEBUG');
            }
        }
    }

    public function actionGetXoxoOrderDetails($api_restock_request_id, $sku)
    {
        $publisher = new Xoxo();
        $publisher->api_restock_request_id = $api_restock_request_id;
        $publisher->sku = $sku;
        $publisher->processOrder(3);
    }
}
