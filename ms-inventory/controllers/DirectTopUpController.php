<?php

namespace micro\controllers;

use micro\models\publishers\DTOneDVS;
use micro\models\publishers\MintRouteDTU;
use micro\models\publishers\NetDragonDTU;
use micro\models\publishers\RazerDTU;
use offgamers\base\models\ms\Order;
use micro\models\DirectTopUpModel;
use micro\models\publishers\FuluDTU;
use yii\helpers\Json;

class DirectTopUpController extends \offgamers\base\controllers\RestController
{
    public function actionProcessOrder()
    {
        $model = new DirectTopUpModel();
        return $model->processTopUp($this->input);
    }

    public function actionFuluPostBack()
    {
        $fulu_model = new FuluDTU();
        $order_id = $fulu_model->processPostBack($this->input);

        if ($order_id) {
            $order_id = str_replace('DTU_', '', $order_id);
            (new Order())->customRequest('direct-top-up', 'reset-top-up-status', ['sub_order_id' => $order_id]);
            return true;
        }
        return false;
    }

    public function actionDtonePostBack()
    {
        $dtone = new DTOneDVS();
        $order_id = $dtone->processPostBack($this->input['data']);

        if ($order_id) {
            (new Order())->customRequest('direct-top-up', 'reset-top-up-status', ['sub_order_id' => $order_id]);
            return true;
        }

        return false;
    }

    public function actionValidateMintRoute()
    {
        $mintroute_model = new MintRouteDTU();
        $mintroute_model->initValidation($this->input);
        $mintroute_model->orders_id = ($this->input['customer_id'] ?? 0);
        return $mintroute_model->validateAccount($this->input);
    }

    public function actionGetMintRouteServer()
    {
        $mintroute_model = new MintRouteDTU();
        return $mintroute_model->getServer($this->input);
    }

    public function actionValidateNetDragon()
    {
        $netdragon_model = new NetDragonDTU();
        $netdragon_model->initValidation($this->input);
        return $netdragon_model->validateAccount($this->input);
    }

    public function actionGetNetDragonServer()
    {
        $netdragon_model = new NetDragonDTU();
        return $netdragon_model->getServer($this->input);
    }

    public function actionGetFuluServer()
    {
        $fulu_model = new FuluDTU();
        return $fulu_model->getServer($this->input);
    }

    public function actionValidateFulu()
    {
        $fulu_model = new FuluDTU();
        $fulu_model->load($this->input, "");
        return $fulu_model->validateAccount($this->input);
    }

    public function actionGetCharacterRazer()
    {
        $razer_model = new RazerDTU();
        $razer_model->load($this->input, "");
        $razer_model->initRazerPublisher();
        return $razer_model->getCharacterList($this->input);
    }

    public function actionValidateRazer()
    {
        $razer_model = new RazerDTU();
        $razer_model->load($this->input, "");
        $razer_model->initRazerPublisher();
        return $razer_model->validateAccount(null, $this->input);
    }
}