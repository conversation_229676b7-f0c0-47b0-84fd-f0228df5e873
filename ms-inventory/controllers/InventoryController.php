<?php

namespace micro\controllers;

use micro\components\CdKeyCom;
use micro\models\CustomProductsCode;

class InventoryController extends \offgamers\base\controllers\RestController
{
    public function actionCheckCustomProductsCodeByOrdersProductsId()
    {
        $cpc = CustomProductsCode::find()
            ->select(['custom_products_code_id'])
            ->where(['orders_products_id' => $this->input, 'status_id' => '0'])
            ->asArray()
            ->all();
        return $cpc;
    }

    public function actionG2gOrderDelivery()
    {
        $data = $this->input;

        /**
         * $data includes
         * order_id
         * reseller_id
         * products_id
         * qty
         * pending_qty
         * order_currency
         * product_price_in_currency
         * product_price_in_usd
         */

        $cdkeyCom = new CdKeyCom();

        return $cdkeyCom->processG2GCdkeyDelivery($data);
    }
}