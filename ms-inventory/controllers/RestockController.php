<?php

namespace micro\controllers;

use micro\models\publishers\Bamboo;
use micro\models\publishers\MintRouteBatch;
use micro\models\publishers\PrepaidForge;
use offgamers\base\models\DevDebugLog;
use Yii;
use micro\models\ApiRestockRequest;
use micro\models\AutoRestockModel;
use offgamers\base\models\ms\Order;
use yii\helpers\Json;
use micro\models\ApiRestockRequestExtraInfo;
use micro\models\publishers\BlackHawkNetwork;

class RestockController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        $model = new AutoRestockModel();
        $model->load($this->input, "");
        return $model->processRestock();
    }

    public function actionCheckOrder()
    {
        $model = ApiRestockRequest::findOne(['publisher_order_id' => $this->input['publisher_order_id']]);
        $data = (new Order())->getRestockOrderDetail(['orders_products_id' => $model->orders_products_id]);
        $restock_model = new AutoRestockModel();
        $restock_model->load($data, "");
        return $restock_model->checkOrder($model);
    }

    public function actionVoidBlackHawkTransaction()
    {
        $api_restock_request_id = $this->input['api_restock_request_id'];

        $pending_transaction = ApiRestockRequestExtraInfo::find()->where(['key' => ['VOID_REFUND_TRANSACTION_STATUS', 'VOID_REQUEST_TRANSACTION_STATUS']])->andWhere(['api_restock_request_id' => $api_restock_request_id])->asArray()->all();

        foreach ($pending_transaction as $transaction) {
            $status = false;
            $model = ApiRestockRequest::findOne($transaction['api_restock_request_id']);
            if ($model) {
                $publisher = new BlackHawkNetwork();
                $publisher->api_restock_request_id = $model->api_restock_request_id;

                if ($transaction['key'] == 'VOID_REFUND_TRANSACTION_STATUS') {
                    $data = Json::decode(ApiRestockRequest::getExtraInfo($model->api_restock_request_id, 'REFUND_PARAMS')['REFUND_PARAMS'])['attribute'];
                    $publisher->load($data, '');
                    $status = $publisher->reverseTransaction('REFUND');
                } elseif ($transaction['key'] == 'VOID_REQUEST_TRANSACTION_STATUS') {
                    $data = Json::decode(ApiRestockRequest::getExtraInfo($model->api_restock_request_id, 'REQUEST_PARAMS')['REQUEST_PARAMS'])['attribute'];
                    $publisher->load($data, '');
                    $status = $publisher->reverseTransaction('REQUEST');
                }
            }

            if ($status == false) {
                Yii::$app->slack->send('Failed to void Black Hawk Order', array(
                    array(
                        'color' => 'warning',
                        'text' => \yii\helpers\Json::encode([$this->input, $pending_transaction]),
                    ),
                ), 'DEBUG');
            }
        }

        return true;
    }

    public function actionMintRouteBatchRestock()
    {
        try {
            $model = new MintRouteBatch();
            $model->batchRestock();
        } catch (\Exception $e) {
            DevDebugLog::generateDebugLog('MintRouteBatch', $e->getTraceAsString());
        }
        return true;
    }

    public function actionMintRoutePostBack()
    {
        try {
            $model = new MintRouteBatch();
            $model->orderPostBack($this->input['data'], $this->input['signature']);
        } catch (\Exception $e) {
            DevDebugLog::generateDebugLog('MintRouteBatch', $e->getTraceAsString());
        }
        return true;
    }

    public function actionBambooBatchRestock()
    {
        try {
            $model = new Bamboo();
            $model->batchRestock();
        } catch (\Exception $e) {
            DevDebugLog::generateDebugLog('BambooBatch', $e->getTraceAsString());
        }
        return true;
    }

    public function actionPrepaidForgeBatchRestock(){
        try {
            $model = new PrepaidForge();
            $model->batchRestock();
        } catch (\Exception $e) {
            DevDebugLog::generateDebugLog('PrepaidForgeBatch', $e->getTraceAsString());
        }
        return true;
    }
}