<?php

$config = [
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.slave.username'],
                'password' => $params['db.slave.password'],
                'charset' => 'utf8',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.slave.username'],
                'password' => $params['db.slave.password'],
                'charset' => 'latin1',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_log' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.log.dsn'],
            'username' => $params['db.log.username'],
            'password' => $params['db.log.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.log.slave.username'],
                'password' => $params['db.log.slave.password'],
                'charset' => 'utf8',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.log.slave.dsn']],
            ],
        ],
        'db_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.og.dsn'],
            'username' => $params['db.og.username'],
            'password' => $params['db.og.password'],
            'charset' => 'utf8mb4',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "ms-inventory/",
            'servers' => [
                [
                    'host' => $params['cache.host'],
                    'port' => $params['cache.port'],
                    'weight' => 50,
                ],
            ],
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => $params['aws.key'],
            'secret' => $params['aws.secret'],
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_ENCRYPT_LOG' => [
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.encrypt.log.bucket_key'],
                    'acl' => 'private',
                    'prefix_path' => 'log',
                    'storage' => 'STANDARD',
                    'sse_kms_key' => $params['aws.encrypt.log.kms_key']
                ],
                'BUCKET_CD_KEY' => [
                    'region' => 'us-east-1',
                    'bucket_key' => $params['aws.cdkey.bucket_key'],
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_TOOLBOX' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
                'BUCKET_ENCRYPT_KEY' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                    'sse_kms_key' => ''
                ],
            ],
            'sqs' => [
                'TASK_QUEUE' => [
                    'queue_url' => '',
                ],
                'OUTGOING_LOG_QUEUE' => [
                    'queue_url' => '',
                ],
                'RESTOCK_QUEUE' => [
                    'queue_url' => '',
                ]
            ]
        ],
    ],
];

return $config;