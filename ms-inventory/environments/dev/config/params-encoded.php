<?php

return [
    // Main offgamers db conection
    'db.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // offgamers db connection for non-utf8
    'db.og.dsn' => 'mysql:host=localhost;dbname=og',
    'db.og.username' => '',
    'db.og.password' => '',

    // log module
    'db.log.dsn' => 'mysql:host=localhost;dbname=offgamers_log',
    'db.log.username' => '',
    'db.log.password' => '',

    // slave db connection
    'db.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',
    'db.slave.username' => '',
    'db.slave.password' => '',

    // slave db connection
    'db.log.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers_log',
    'db.log.slave.username' => '',
    'db.log.slave.password' => '',

    'slack.webhook.default' => '',
    'slack.webhook.debug' => '',
    'slack.webhook.bdt' => '',
    'slack.webhook.bdt.replenish' => '',
    'slack.webhook.bdt.dtu' => '',

    'algolia.application' => '',
    'algolia.admin.key' => '',

    'api.credential' => [
        'backend' => '123456'
    ],

    //AWS
    'aws.key' => '',
    'aws.secret' => '',

    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',
    'aws.cdkey.bucket_key' => '',

    'cpc.secure.key' => '',
    'cpc.secure.iv' => '',

    //MintRoute API Credentials
    'mintroute.credential' => [
        'base_url' => '',
        'public_key' => '',
        'private_key' => '',
        'user_name' => '',
        'password' => ''
    ],

    //MintRoute Batch API Credentials
    'mintroutebatch.credential' => [
        'base_url' => 'https://sandbox.mintroute.com/voucher',
        'public_key' => '2hEOBC5p',
        'private_key' => '3e532a5046dee55491a2ff65f71b2acd',
        'user_name' => 'b.yen.bulk',
        'password' => 'tQqd7W92',
        'publisher_id' => 11,
        'products_info' => [
            170706 => [
                'DENO_ID' => 4,
                'RESTOCK_LEVEL' => 0,
                'RESTOCK_TO_QUANTITY' => 0
            ],
            170704 => [
                'DENO_ID' => 21,
                'RESTOCK_LEVEL' => 10,
                'RESTOCK_TO_QUANTITY' => 10
            ],
            170703 => [
                'DENO_ID' => 18,
                'RESTOCK_LEVEL' => 0,
                'RESTOCK_TO_QUANTITY' => 0
            ]
        ]
    ],

    //Unipin API Credentials
    'unipin.credential' => [
        'ID' => [
            'base_url' => 'https://dev-api.unipin.com',
            'key' => '9b42a14d-a986-40a9-b4cc-354be6aea6db',
            'secret' => 'w56kbwxuxh3heka3'
        ]
    ],

    //Razer API Credentials
    'razer.credential' => [
        'MY' => [
            'base_url' => 'https://sandbox-api.mol.com',
            'applicationCode' => '202004100420',
            'secret' => 'fipg2833'
        ],
    ],

    //CashLib API Credentials
    'cashlib.credential' => [
        'base_url' => '',
        'distributor_id' => '',
        'pos_id' => '',
        'api_key' => '',
        'test_env' => true
    ],

    // AcePayz
    'acepayz.credential' => [
        'base_url' => '',
        'user_name' => '',
        'password' => '',
        'key' => ''
    ],

    // BlackHawk
    'bhn.credential' => [
        'ID' => [
            'base_url' => [
            ],
            'account_num' => '',
            'mid' => '',
        ]
    ],

    // BlackHawk V2
    'bhnv2.credential' => [
        'SG1' => [
            'base_url' => '',
            'mid' => '',
            'cert_file' => '',
            'cert_secret' => '',
        ]
    ],

    // CMAP API - PaysGift voucher
    'cmap.credential' => [
        'base_url' => '',
        'sale_channel_code' => ''
    ],

    //VTC VCoin API Credentials
    'vcoin.credential' => [
        'base_url' => '',
        'public_key' => '',
        'private_key' => '',
        'user_name' => '',
        'password' => 'root'
    ],

    //VTC SCoin API Credentials
    'scoin.credential' => [
        'url' => '',
        'public_key' => '',
        'private_key' => '',
        'partner_code' => '',
        'triple_des_key' => ''
    ],

    //Bamboo API Credentials
    'bamboo.credential' => [
        '' => [
            'base_url' => '',
            'username' => '',
            'secret' => '',
            'publisher_id' => ''
        ]
    ],

    'bamboo.restock_info' => [
        0 => [
            'SKU' => 'BAMBOO_{REGION}_{api_product_id}_{value}',
            'RESTOCK_LEVEL' => 0,
            'RESTOCK_TO_QUANTITY' => 0
        ],
    ],

    //MyCard API Credentials
    'mycard.credential' => [
        'base_url' => '',
        'key' => '',
        'secret' => ''
    ],

    // Jeton API Credential
    'jeton.credential' => [
        'base_url' => '',
        'api_key' => '',
    ],

    //Fulu API Credentials
    'fulu.credential' => array(
        'base_url' => '',
        'secret_id' => '',
        'secret_key' => '',
    ),

    //Bigo API Credentials
    'bigo.credential' => array(
        'base_url' => '',
        'channel_id' => '',
        'channel_secret' => '',
        'public_ip' => '',
    ),

    // TWBS API Credentials
    'twbs.credential' => [
        'base_url' => 'https://dev.evoucher.cashtocode.com',
        'token' => '8cee8a3a405e507f1094c011f3b916f34e83de16',
    ],

    //Azteco API Credentials
    'azteco.credential' => [
        'base_url' => '',
        'api_key' => '',
    ],

    // OpenBucks Credentials
    'openbucks.credential' => [
        'base_url' => '',
        'api_key' => '',
        'distributor_id' => '',
        'cert_file' => '',
        'cert_secret' => ''
    ],

    // Prepaid Forge Batch Information
    'prepaidforge.products_info' => [
        0 => [
            'SKU' => 'PF_{CURRENCY}_{SKU}',
            'RESTOCK_LEVEL' => 0,
            'RESTOCK_TO_QUANTITY' => 0
        ],
    ],

    //Micro Service Credential
    'micro.service.order' => [
        'baseUrl' => 'https://staging-ms-order.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.product' => [
        'baseUrl' => 'https://staging-ms-product.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

];