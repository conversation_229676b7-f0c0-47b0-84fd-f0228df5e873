<?php

namespace micro\interfaces;

use micro\components\CdKeyCom;
use micro\exceptions\InvalidMarginException;
use micro\exceptions\RestockNonRetryableException;
use micro\exceptions\RestockRetryableException;
use micro\models\ApiRestockRequest;
use micro\models\publishers\RestockPublisherTrait;
use micro\models\RestockRequest;
use offgamers\base\models\ms\Order;
use offgamers\base\traits\GuzzleTrait;
use Yii;
use yii\base\BaseObject;
use yii\helpers\Json;
use yii\queue\JobInterface;
use micro\components\PublisherMapping;

class RestockJob extends BaseObject implements JobInterface
{
    use GuzzleTrait;

    private const RETRY_MAX_ATTEMPT = 3;
    private const RETRY_DELAY = 30;
    private const RESTOCK_REQUEST_MUTEX_LOCK = 'api_restock_request/%s';

    public $data;
    private $restock_request;
    private $request_profile;
    public $start_time;

    private bool $continue_restock = true;

    public function execute($queue)
    {
        $api_restock_request = false;
        $mutex_lock = null;
        $this->restock_request = RestockRequest::findOne($this->data['restock_request_id']);

        if (!$this->restock_request) {
            Yii::$app->slack->send("Failed to find restock_request (ID: " . $this->data['restock_request_id'] . ")");
            return false; // End queue if cannot find restock request
        }

        $this->request_profile = $this->restock_request->request_profile;
        // Lock `api_restock_request` to prevent pulling the same record
        try {
            // Obtain one pending delivery quantity
            $api_restock_request = ApiRestockRequest::getNextLineByRestockRequestId($this->restock_request->restock_request_id);
            if ($api_restock_request) {
                $mutex_lock = sprintf(self::RESTOCK_REQUEST_MUTEX_LOCK, $api_restock_request->api_restock_request_id);
                $mutex_timeout = 30;

                if (Yii::$app->mutex->acquire($mutex_lock, $mutex_timeout) && $api_restock_request->obtainLock()) {
                    $api_restock_request = $this->restockFromPublisher($api_restock_request);
                } else {
                    // Failed to obtain lock, probably another restock in progress
                    throw new RestockRetryableException('Failed to obtain lock, probably another restock in process');
                }
            }
        } catch (RestockNonRetryableException $e) {
            $this->restockCompleteCallback();
        } catch (\Exception $e) {
        } finally {
            if ($api_restock_request && $api_restock_request->is_locked) {
                $api_restock_request->releaseLock();
            }
            if ($mutex_lock) {
                Yii::$app->mutex->release($mutex_lock);
            }
            $this->continueRestock(($api_restock_request && $api_restock_request->status != 1));
        }
    }

    /**
     * @throws RestockNonRetryableException
     */
    protected function restockFromPublisher($api_restock_request)
    {
        $publisher = $this->getPublisher($this->data['sku']);

        $params = [
            'reseller_profile' => $this->request_profile,
            'orders_id' => $this->data['request_reference_id'],
            'products_id' => $this->data['products_id'],
            'products_name' => $this->data['products_name'] ?? '',
            'sku' => $this->data['sku'],

            'orders_currency' => $this->data['orders_currency'],
            'orders_products_currency_price' => $this->data['orders_products_currency_price'],
            'orders_products_price' => $this->data['orders_products_price'],

            'product_cost' => $this->data['product_cost'],
            'product_cost_currency' => $this->data['product_cost_currency'],

            'publisher' => $this->data['publisher'] ?? '',
            'customers_id' => $this->data['customers_id'] ?? '',
            'phone_country_code' => $this->data['phone_country_code'] ?? '',
            'customers_state' => $this->data['customers_state'] ?? '',

            'publisher_order_id' => $api_restock_request->publisher_order_id,
            'api_restock_request_id' => $api_restock_request->api_restock_request_id,
            'low_margin_notified' => ($this->data['low_margin_notified'] ?? false),
        ];

        $publisher->load($params, "");

        // Restock
        try {
            $publisher->processOrder($api_restock_request->status);
            // Set publisher_order_id in `api_restock_request` table
            if (!empty($publisher->publisher_order_id)) {
                $api_restock_request->publisher_order_id = (string)$publisher->publisher_order_id;
            }

            $api_restock_request = $this->processRestockCallBack($publisher, $api_restock_request);
            $api_restock_request->releaseLock();

            // Prevent Low Margin Notification Spam in same order
            if ($publisher->low_margin_notified) {
                $this->data['low_margin_notified'] = true;
            }

            // Send slack notification when restock failed
            if (in_array($api_restock_request->status, [2, 3])) {
                throw new \Exception('Failed to restock');
            }
        } catch (RestockNonRetryableException $e) {
            if ($e instanceof InvalidMarginException) {
                $api_restock_request->status = 9;
                $api_restock_request->save();
            }
            $this->continue_restock = false;
            $publisher->reportError($e);
        } catch (\Exception $e) {
            // Unknown Exception, Send Slack and queue for retry
            $publisher->reportError($e);
        } finally {
            if ($api_restock_request->status == 0) {
                $api_restock_request->status = 2;
            }
            $api_restock_request->save();
        }

        return $api_restock_request;
    }

    /**
     * @param RestockPublisherTrait $publisher
     * @return ApiRestockRequest
     */
    private function processRestockCallBack($publisher, $api_restock_request)
    {
        switch ($publisher->status_flag) {
            // Success
            case 1:
                $cdkey_com = new CdKeyCom();
                if (!empty($publisher->code_string)) {
                    $api_restock_request = $this->uploadSoftPin($publisher, $api_restock_request);
                } elseif (empty($api_restock_request->custom_products_code_id)) {
                    // Preorder Products, Create Dummy custom_product_code & log_api_restocks line
                    $cpc_model = $cdkey_com->createCustomProductsCode(
                        $this->data['products_id'],
                        [
                            'orders_products_id' => 0,
                            'remarks' => $publisher->getAPIProvider() . "_API",
                            'status' => -3
                        ]
                    );
                    $api_restock_request->custom_products_code_id = $cpc_model->custom_products_code_id;
                    $publisher->logAPIRequest(['method' => $publisher->getOrderUrl(), 'custom_products_code_id' => $cpc_model->custom_products_code_id]);
                    $api_restock_request->status = 8;
                }
                break;
            case 0:
            case 2:
                // Case 3 Should not exist
            case 3:
                $api_restock_request->status = 2;
                break;
            case -1:
                // Create new row when publisher return error and retry
                $api_restock_request->status = 5;
                $new_request = (new ApiRestockRequest());
                $new_request->load(['products_id' => $this->data['products_id'], 'status' => 0, 'restock_request_id' => $this->restock_request->restock_request_id], '');
                $new_request->save();
                break;
        }

        return $api_restock_request;
    }

    /**
     * @param RestockPublisherTrait $publisher
     * @param ApiRestockRequest $api_restock_request
     *
     * @return ApiRestockRequest
     */

    private function uploadSoftPin($publisher, $api_restock_request)
    {
        $cdkey_com = new CdKeyCom();

        $cd_key_extra_info = [
            'file_type' => $publisher->code_type,
            'remarks' => $publisher->getAPIProvider() . "_API",
            'custom_products_code_id' => $api_restock_request->custom_products_code_id,
            // Set CDKey status to reserved when its restock on demand
            'status' => ($this->request_profile == 'Manual' ? 1 : -5)
        ];

        $cdk_model = $cdkey_com->uploadCdKey(
            $this->data['products_id'],
            $publisher->code_string,
            $cd_key_extra_info
        );

        $custom_products_code_id = $cdk_model->custom_products_code_id;
        $api_restock_request->custom_products_code_id = $custom_products_code_id;

        $publisher->logAPIRequest([
            'method' => $publisher->getOrderUrl(),
            'custom_products_code_id' => $custom_products_code_id,
            'serialnumber' => $publisher->publisher_order_id
        ]);

        $api_restock_request->status = 1;

        return $api_restock_request;
    }

    protected function getPublisher($sku)
    {
        $prefix = explode("_", $sku)[0];
        if (!empty(PublisherMapping::PUBLISHER_PREFIX[$prefix])) {
            $class = "\micro\models\publishers\\" . PublisherMapping::PUBLISHER_PREFIX[$prefix];
            /* @var $publisher bool|RestockPublisherTrait */
            $publisher = new $class();
            return $publisher;
        }

        throw new RestockNonRetryableException('Publisher Profile Not Found');
    }

    private function continueRestock($is_error)
    {
        $delay = ($is_error ? self::RETRY_DELAY : 0);
        $current_retry = ($this->data['retry_attempt'] ?? 0);
        $retry_attempt = ($is_error ? $current_retry + 1 : 0);

        // Stop processing when hit max retry
        if ($retry_attempt > self::RETRY_MAX_ATTEMPT) {
            $this->continue_restock = false;
        }

        $this->data['retry'] = $retry_attempt;

        if ($this->continue_restock) {
            $next_item = ApiRestockRequest::getNextLineByRestockRequestId($this->restock_request->restock_request_id);
            if ($next_item) {
                // Insert Queue for next processing
                $sqs = Yii::$app->aws->getSQS('RESTOCK_QUEUE');
                $sqs->pushMessage([
                    'data' => $this->data
                ], $delay);
                // Prevent execute complete action before all pending restock complete
                return;
            }
        }

        $this->restockCompleteCallback();
    }

    private function restockCompleteCallback()
    {
        $data = $this->data;
        $data['reseller_id'] = $this->restock_request->request_profile_id;

        $this->restock_request->status = (ApiRestockRequest::find()->where(['restock_request_id' => $this->restock_request->request_profile_id, 'status' => [2, 3, 8, 9]])->exists() ? 2 : 1);
        $this->restock_request->save();

        switch ($this->request_profile) {
            case 'G2G':
                $request_data = [
                    'order_id' => $this->restock_request->request_reference_id,
                    'reseller_id' => $this->restock_request->request_profile_id
                ];
                (new Order())->customRequest('order', 'process-g2g-order', $request_data);
                break;

            case 'Manual':
                Yii::$app->slack->send("Manual Restock Request ID: #({$this->restock_request->request_profile_id}) " . ($this->restock_request->status == 1 ? 'Completed' : 'Failed'), [], 'BDT_REPLENISH');

                // Manual Restock Slack Notification
                break;

            case 'OffGamers':
                // Reprocess OG Order for delivery
                break;
        }
    }
}