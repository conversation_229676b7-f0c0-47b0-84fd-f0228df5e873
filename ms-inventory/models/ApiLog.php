<?php

namespace micro\models;

use offgamers\base\components\GeneralCom;
use Yii;
use yii\db\Expression;
use yii\helpers\Json;

/**
 * This is the model class for table "api_log".
 *
 * @property int $api_log_id
 * @property string $action
 * @property int $publishers_id
 * @property int $orders_products_id
 * @property string $publisher_ref_id
 * @property int $top_up_id
 * @property int $result_code
 * @property string $request_log
 * @property string $response_log
 * @property string $request_start
 * @property string $request_end
 * @property string $ip_address
 */
class ApiLog extends \yii\db\ActiveRecord
{
    
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publishers_id', 'orders_products_id', 'top_up_id', 'result_code'], 'integer'],
            [['request_log', 'response_log'], 'string'],
            [['request_start', 'request_end'], 'safe'],
            [['action', 'publisher_ref_id'], 'string', 'max' => 32],
            [['ip_address'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_log_id' => 'Api Log ID',
            'action' => 'Action',
            'publishers_id' => 'Publishers ID',
            'orders_products_id' => 'Orders Products ID',
            'publisher_ref_id' => 'Publisher Ref ID',
            'top_up_id' => 'Top Up ID',
            'result_code' => 'Result Code',
            'request_log' => 'Request Log',
            'response_log' => 'Response Log',
            'request_start' => 'Request Start',
            'request_end' => 'Request End',
            'ip_address' => 'Ip Address',
        ];
    }

    public static function createApiLog($publisher_id, $top_up_id, $url, $request_data, $action)
    {
        $model = new self;
        $model->request_start = new Expression('NOW()');
        $model->ip_address = GeneralCom::getIPAddress();
        $model->action = $action;
        $model->publishers_id = $publisher_id;
        $model->top_up_id = $top_up_id;

        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($request_data);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $model->request_log = $request_log;
        $model->save();
        return $model;
    }

    public function endLog($result_code, $response_log)
    {
        $this->result_code = $result_code;
        $this->response_log = Json::encode($response_log);
        $this->request_end = new Expression('NOW()');
        $this->update();
    }
}
