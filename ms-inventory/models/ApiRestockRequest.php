<?php

namespace micro\models;

use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "api_restock_request".
 *
 * @property int $api_restock_request_id
 * @property int $orders_products_id
 * @property int $custom_products_code_id
 * @property int $orders_id
 * @property int $products_id
 * @property string $publisher_order_id
 * @property int $status
 * @property int $is_locked
 * @property int $restock_request_id
 * @property int $created_at
 * @property int $updated_at
 *
 * @property CustomProductsCode $customProductsCode
 */
class ApiRestockRequest extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_restock_request';
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            \yii\behaviors\TimestampBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id', 'custom_products_code_id', 'orders_id', 'products_id', 'status', 'is_locked', 'restock_request_id', 'created_at', 'updated_at'], 'integer'],
            [['publisher_order_id'], 'string'],
            [['custom_products_code_id'], 'exist', 'skipOnError' => true, 'targetClass' => CustomProductsCode::className(), 'targetAttribute' => ['custom_products_code_id' => 'custom_products_code_id']],
            [['is_locked'], 'default', 'value' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_restock_request_id' => 'Api Restock Request ID',
            'orders_products_id' => 'Orders Products ID',
            'custom_products_code_id' => 'Custom Products Code ID',
            'orders_id' => 'Orders ID',
            'products_id' => 'Products ID',
            'publisher_order_id' => 'Publisher Order ID',
            'status' => 'Status',
            'is_locked' => 'Is Locked',
            'restock_request_id' => 'Restock Request ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomProductsCode()
    {
        return $this->hasOne(CustomProductsCode::class, ['custom_products_code_id' => 'custom_products_code_id']);
    }


    /**
     * @param $restock_request_id
     * @return ApiRestockRequest
     */
    public static function getNextLineByRestockRequestId($restock_request_id, $exclude_status = [1, 5, 8, 9])
    {
        /* @var ApiRestockRequest $restock_line */
        $restock_line = ApiRestockRequest::find()
            ->where([
                'restock_request_id' => $restock_request_id,
                'is_locked' => 0,
            ])
            ->andWhere(['not in', 'status', $exclude_status])
            ->one();

        return $restock_line;
    }

    public static function getPendingLineCountByRestockRequestId($restock_request_id)
    {
        return (new \yii\db\Query())->from(self::tableName())->where(['restock_request_id' => $restock_request_id])->andWhere(['NOT IN', 'status', [1, 5]])->count();
    }

    public static function getPendingLineCount($orders_products_id)
    {
        $count = (new \yii\db\Query())->from(self::tableName())->where(['orders_products_id' => $orders_products_id])->andWhere(['NOT IN', 'status', [1, 5]])->count();

        return $count;
    }

    /**
     * @param $orders_products_id
     * @return array|ApiRestockRequest[]
     */

    public static function getPendingDeliveryLine($orders_products_id)
    {
        $pending_line = self::find()->where(['orders_products_id' => $orders_products_id])->andWhere(['!=', 'status', 1])->andWhere(['!=', 'status', 5])->all();

        return $pending_line;
    }


    public static function createExtraInfo($api_restock_request_id, $key, $value)
    {
        try {
            $model = new ApiRestockRequestExtraInfo();
            $model->api_restock_request_id = $api_restock_request_id;
            $model->key = $key;
            $model->value = $value;
            $model->save();
        } catch (\Exception $e) {
            ApiRestockRequestExtraInfo::updateAll(['value' => $value], ['api_restock_request_id' => $api_restock_request_id, 'key' => $key]);
        }
    }

    public static function getExtraInfo($api_restock_request_id, $key = '')
    {
        $query = ApiRestockRequestExtraInfo::find()->select(['key', 'value'])->where(['api_restock_request_id' => $api_restock_request_id]);

        if (!empty($key)) {
            $query->andWhere(['key' => $key]);
        }

        $return_arr = ArrayHelper::map($query->asArray()->all(), 'key', 'value');

        return $return_arr;
    }

    public function obtainLock(): bool
    {
        if (Yii::$app->db->createCommand()->update(self::tableName(), ['is_locked' => 1], 'is_locked = 0 AND api_restock_request_id = ' . $this->api_restock_request_id)->execute()) {
            $this->refresh();
            return true;
        }
        return false;
    }

    public function releaseLock()
    {
        $this->is_locked = 0;
        $this->save();
    }

}