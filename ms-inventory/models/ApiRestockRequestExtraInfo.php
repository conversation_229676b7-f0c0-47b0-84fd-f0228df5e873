<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_restock_request_extra_info".
 *
 * @property int $api_restock_request_extra_info_id
 * @property int $api_restock_request_id
 * @property string $key
 * @property string $value
 *
 * @property ApiRestockRequest $apiRestockRequest
 */
class ApiRestockRequestExtraInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_restock_request_extra_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['api_restock_request_id', 'key', 'value'], 'required'],
            [['api_restock_request_id'], 'integer'],
            [['value'], 'string'],
            [['key'], 'string', 'max' => 255],
            [['api_restock_request_id'], 'exist', 'skipOnError' => true, 'targetClass' => ApiRestockRequest::className(), 'targetAttribute' => ['api_restock_request_id' => 'api_restock_request_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_restock_request_extra_info_id' => 'Api Restock Request Extra Info ID',
            'api_restock_request_id' => 'Api Restock Request ID',
            'key' => 'Key',
            'value' => 'Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getApiRestockRequest()
    {
        return $this->hasOne(ApiRestockRequest::className(), ['api_restock_request_id' => 'api_restock_request_id']);
    }

    public static function createExtraInfoLine($data){
        $extra_info = new ApiRestockRequestExtraInfo();
        $extra_info->load($data, '');
        $extra_info->save();

        return $extra_info;
    }
}
