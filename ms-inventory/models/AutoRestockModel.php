<?php

namespace micro\models;

use Yii;
use micro\components\CdKeyCom;
use micro\exceptions\OverDeliveryException;
use offgamers\base\models\ms\Product;
use micro\exceptions\InvalidMarginException;
use offgamers\base\models\ms\Order;
use micro\components\PublisherMapping;

class AutoRestockModel extends \yii\base\Model
{
    public $orders_id;
    public $orders_currency;
    public $orders_products_id;
    public $quantity;
    public $sku;
    public $orders_products_currency_price;
    public $orders_products_price;
    public $products_id;
    public $products_name;
    public $publisher_id;
    public $order_quantity;
    public $product_cost;
    public $product_cost_currency;
    public $publisher_name = '';
    public $customers_id;
    public $phone_country_code;
    public $customers_state;

    private $cdkeyCom;
    private $cpc_id_list = [];

    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'quantity', 'order_quantity', 'products_id', 'publisher_id', 'customers_id'], 'integer'],
            [['sku', 'products_name', 'product_cost_currency', 'publisher_name', 'orders_currency', 'phone_country_code', 'customers_state'], 'string'],
            [['orders_products_currency_price', 'orders_products_price', 'product_cost'], 'number'],
        ];
    }

    /**
     * @return bool|\micro\models\publishers\RestockPublisherTrait
     */
    private function detectSKU()
    {
        if (!empty($this->sku)) {
            $prefix = explode("_", $this->sku);
            if (!empty(PublisherMapping::PUBLISHER_PREFIX[$prefix[0]])) {
                $class = "\micro\models\publishers\\" . PublisherMapping::PUBLISHER_PREFIX[$prefix[0]];
                /* @var $publisher bool|\micro\models\publishers\RestockPublisherTrait */
                $publisher = new $class();
                $this->publisher_name = $publisher->getApiClassName();
                return $publisher;
            }
        }
        return false;
    }

    public function processRestock()
    {
        set_time_limit(300);
        $publisher_model = $this->detectSKU();
        if ($publisher_model) {
            try {
                $params = [
                    'orders_id' => $this->orders_id,
                    'orders_products_id' => $this->orders_products_id,
                    'products_id' => $this->products_id,
                    'products_name' => $this->products_name,
                    'sku' => $this->sku,
                    'orders_currency' => $this->orders_currency,
                    'orders_products_currency_price' => $this->orders_products_currency_price,
                    'orders_products_price' => $this->orders_products_price,
                    'product_cost' => $this->product_cost,
                    'product_cost_currency' => $this->product_cost_currency,
                    'publisher' => $this->publisher_id,
                    'customers_id' => $this->customers_id,
                    'phone_country_code' => $this->phone_country_code,
                    'customers_state' => $this->customers_state
                ];
                if ($this->publisher_id) {
                    $params['publisher'] = $this->publisher_id;
                }
                $publisher_model->load($params, "");
                $this->insertRestockLine();
                $pending_line = ApiRestockRequest::getPendingDeliveryLine($this->orders_products_id);

                if ($pending_line) {
                    foreach ($pending_line as $restock_model) {
                        $restock_model = $this->processRestockLine($publisher_model, $restock_model);
                        if (!$restock_model) {
                            // stop other line to process on error occur
                            break;
                        } elseif ($restock_model->status == 1 && !empty($restock_model->custom_products_code_id)) {
                            $this->cpc_id_list[] = $restock_model->custom_products_code_id;
                        }
                    }
                }
                $this->updateProductsQty();
            } catch (\Exception $e) {
                $publisher_model->reportError($e);
            }
        }
        return true;
    }

    /**
     * @param \micro\models\ApiRestockRequest $restock_model
     * @return bool
     */
    public function checkOrder($restock_model)
    {
        $publisher_model = $this->detectSKU();
        if ($publisher_model) {
            try {
                $params = [
                    'orders_id' => $this->orders_id,
                    'orders_products_id' => $this->orders_products_id,
                    'products_id' => $this->products_id,
                    'products_name' => $this->products_name,
                    'sku' => $this->sku,
                    'orders_currency' => $this->orders_currency,
                    'orders_products_price' => $this->orders_products_price,
                    'orders_products_currency_price' => $this->orders_products_currency_price,
                    'publisher' => $this->publisher_id,
                    'product_cost' => $this->product_cost,
                    'product_cost_currency' => $this->product_cost_currency,
                    'customers_id' => $this->customers_id
                ];
                $publisher_model->load($params, "");
                $restock_model = $this->processRestockLine($publisher_model, $restock_model);
                if ($restock_model->status == 1 && !empty($restock_model->custom_products_code)) {
                    $this->cpc_id_list[] = $restock_model->custom_products_code;
                }
                $this->updateProductsQty();
                if (!empty($this->cpc_id_list)) {
                    (new Order())->setDeliveryQueue(['orders_products_id' => $restock_model->orders_products_id, 'api_provider' => strtolower($this->publisher_name) . '_api']);
                }
            } catch (\Exception $e) {
                $publisher_model->reportError($e);
            }
        }
        return true;
    }

    /**
     * @param \micro\models\publishers\RestockPublisherTrait $publisher_model
     * @param \micro\models\ApiRestockRequest $restock_model
     * @return bool|\micro\models\ApiRestockRequest
     */
    private function processRestockLine($publisher_model, $restock_model)
    {
        $publisher_model->reset();
        $publisher_model->publisher_order_id = $restock_model->publisher_order_id;
        $publisher_model->api_restock_request_id = $restock_model->api_restock_request_id;
        $is_error = false;
        // Status : 0 (New Request), 1 (Success), 2 (Failed/Reattempt), 3 (Preorder), 5 (Transaction Void, Generate New Trx Id), 9 (Margin Block)
        try {
            if ($restock_model->status == 9) {
                // Reset Margin Block Line Status
                $restock_model->status = 0;
            }
            $publisher_model->processOrder($restock_model->status);
        } catch (InvalidMarginException $e) {
            $restock_model->status = 9;
            $is_error = true;
        } catch (\Exception $e) {
            $publisher_model->reportError($e);
            $is_error = true;
        }

        if ($restock_model->status == 0) {
            $restock_model->status = 2;
        }

        if (!empty($publisher_model->publisher_order_id)) {
            $restock_model->publisher_order_id = $publisher_model->publisher_order_id;
        }

        try {
            if ($publisher_model->status_flag == 1) {
                $cdkey_com = $this->getCDKeyCom();

                if (!empty($publisher_model->code_string)) {
                    $cdkey_extra_info = [
                        'file_type' => $publisher_model->code_type,
                        'orders_products_id' => $this->orders_products_id,
                        'remarks' => $publisher_model->getAPIProvider() . "_API",
                        'custom_products_code_id' => $restock_model->custom_products_code_id
                    ];

                    // Upload CDK to S3
                    $cdk_model = $cdkey_com->uploadCdKey($this->products_id, $publisher_model->code_string, $cdkey_extra_info);
                    $custom_products_code_id = $cdk_model->custom_products_code_id;
                    $restock_model->custom_products_code_id = $custom_products_code_id;

                    if ($cdk_model->status_id == 1) {
                        if ($restock_model->status == 3) {
                            $publisher_model->updateLogApiRequest(['method' => $publisher_model->getOrderUrl(), 'custom_products_code_id' => $custom_products_code_id],
                                ['serialnumber' => $publisher_model->publisher_order_id]);
                        } else {
                            $publisher_model->logAPIRequest([
                                'method' => $publisher_model->getOrderUrl(),
                                'custom_products_code_id' => $custom_products_code_id,
                                'serialnumber' => $publisher_model->publisher_order_id
                            ]);
                        }
                        $restock_model->status = 1;
                    }
                } else {
                    // Preorder Products, Create Dummy cpc & log_api_restocks line
                    if (empty($restock_model->custom_products_code_id)) {
                        $cpc_model = $cdkey_com->createCustomProductsCode(
                            $this->products_id,
                            ['orders_products_id' => $this->orders_products_id, 'remarks' => $publisher_model->getAPIProvider() . "_API", 'status' => -3]
                        );
                        $restock_model->custom_products_code_id = $cpc_model->custom_products_code_id;
                        $publisher_model->logAPIRequest(['method' => $publisher_model->getOrderUrl(), 'custom_products_code_id' => $cpc_model->custom_products_code_id]);
                        $restock_model->status = 3;
                    }
                }
            } elseif ($publisher_model->status_flag == 2) {
                // Order Created but pending checking
                $restock_model->status = 2;
            } elseif ($publisher_model->status_flag == 3) {
                // Order on-hold
                $restock_model->status = 3;
            } elseif ($publisher_model->status_flag == -1) {
                // Order Rejected, Create New Order ID on next attempt
                $restock_model->status = 5;
            }

            if (!in_array($restock_model->status, [1, 3]) && !$is_error) {
                $publisher_model->createException('CREATE_ORDER');
            }
        } catch (\Exception $e) {
            $publisher_model->reportError($e);
        }

        $restock_model->save();

        // Return false on error, prevent other line continue to deliver
        return ($restock_model->status == 1 ? $restock_model : false);
    }

    private function getCDKeyCom()
    {
        if (!$this->cdkeyCom) {
            $this->cdkeyCom = new CdKeyCom();
        }
        return $this->cdkeyCom;
    }


    private function updateProductsQty()
    {
        if (count($this->cpc_id_list)) {
            (new Product())->updateProductsQty([
                [
                    'products_id' => $this->products_id,
                    'custom_products_code' => $this->cpc_id_list,
                    'qty' => count($this->cpc_id_list),
                ],
            ]);
        }
    }

    public static function patchProductsQty()
    {
        $restock_history = ApiRestockRequest::find()
            ->select(['cpc.products_id', 'cpc.custom_products_code_id'])
            ->alias('rq')
            ->where(['IS NOT', 'rq.custom_products_code_id', null])
            ->andWhere(['rq.status' => 1])
            ->joinWith('customProductsCode cpc')
            ->asArray()
            ->all();

        $products_list = [];

        foreach ($restock_history as $record) {
            if (empty($products_list[$record['products_id']])) {
                $products_list[$record['products_id']] = [
                    'products_id' => $record['products_id'],
                    'custom_products_code' => [$record['custom_products_code_id']],
                    'qty' => 1,
                ];
            } else {
                $products_list[$record['products_id']]['qty'] += 1;
                $products_list[$record['products_id']]['custom_products_code'][] = $record['custom_products_code_id'];
            }
        }

        \offgamers\base\components\GeneralCom::slackStackNotification('debug', json_encode(array_values($products_list)), 'danger');

        (new Product())->updateProductsQty(array_values($products_list));
    }

    private function insertRestockLine()
    {
        $insert_array = [];

        $line_quantity = ApiRestockRequest::getPendingLineCount($this->orders_products_id);
        $delivered_quantity = CustomProductsCode::getDeliveredQuantity($this->orders_products_id);
        $restock_quantity = $this->quantity - $line_quantity;

        if ($restock_quantity > 0) {
            $timestamp = time();
            for ($i = 0; $i < $restock_quantity; $i++) {
                $insert_array[] = [$this->orders_products_id, $this->orders_id, $this->products_id, 0, $timestamp, $timestamp];
            }

            if ($insert_array) {
                Yii::$app->db->createCommand()->batchInsert(
                    ApiRestockRequest::tableName(),
                    ['orders_products_id', 'orders_id', 'products_id', 'status', 'created_at', 'updated_at'],
                    $insert_array
                )->execute();
            }
        }

        $validate_count = ApiRestockRequest::getPendingLineCount($this->orders_products_id);

        if ($validate_count > $this->quantity || $delivered_quantity + $validate_count > $this->order_quantity) {
            throw new OverDeliveryException("Orders Id : " . $this->orders_id . " Delivered Quantity + Restock Request Count more than ordered Quantity");
        }

        return true;
    }

}