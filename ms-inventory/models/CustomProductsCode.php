<?php

namespace micro\models;

use Yii;
use yii\db\Query;

class CustomProductsCode extends CustomProductsCodeBase
{
    public $rawData;

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'code_date_added',
                'updatedAtAttribute' => 'code_date_modified',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    public static function getDeliveredQuantity($orders_products_id)
    {
        $count = (new \yii\db\Query())
            ->from(self::tableName())
            ->where(['orders_products_id' => $orders_products_id])
            ->andWhere(['=', 'status_id', 0])
            ->count();

        return $count;
    }


}
