<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "custom_products_code".
 *
 * @property int $custom_products_code_id
 * @property int $products_id
 * @property int $orders_products_id
 * @property int $status_id
 * @property string $file_name
 * @property string $file_type
 * @property string $code_date_added
 * @property string $code_date_modified
 * @property string $code_uploaded_by
 * @property string $remarks
 * @property int $custom_products_code_viewed
 * @property string $purchase_orders_id
 * @property int $to_s3
 *
 * @property ApiRestockRequestItem[] $apiRestockRequestItems
 */
class CustomProductsCodeBase extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'custom_products_code';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'orders_products_id', 'status_id', 'custom_products_code_viewed', 'purchase_orders_id', 'to_s3'], 'integer'],
            [['code_date_added', 'code_date_modified'], 'safe'],
            [['remarks'], 'default', 'value' => ""],
            [['remarks'], 'string'],
            [['file_name'], 'string', 'max' => 50],
            [['file_type'], 'string', 'max' => 5],
            [['code_uploaded_by'], 'string', 'max' => 65],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'custom_products_code_id' => 'Custom Products Code ID',
            'products_id' => 'Products ID',
            'orders_products_id' => 'Orders Products ID',
            'status_id' => 'Status ID',
            'file_name' => 'File Name',
            'file_type' => 'File Type',
            'code_date_added' => 'Code Date Added',
            'code_date_modified' => 'Code Date Modified',
            'code_uploaded_by' => 'Code Uploaded By',
            'remarks' => 'Remarks',
            'custom_products_code_viewed' => 'Custom Products Code Viewed',
            'purchase_orders_id' => 'Purchase Orders ID',
            'to_s3' => 'To S3',
            'encrypt_flag' => 'Encrypt Flag',
            'encrypt_flag_2' => 'Encrypt Flag 2',
            'encrypt_flag_3' => 'Encrypt Flag 3',
            'encrypt_flag_4' => 'Encrypt Flag 4',
            'encrypt_flag_5' => 'Encrypt Flag 5',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getApiRestockRequestItems()
    {
        return $this->hasMany(ApiRestockRequestItem::className(), ['custom_products_code_id' => 'custom_products_code_id']);
    }
}
