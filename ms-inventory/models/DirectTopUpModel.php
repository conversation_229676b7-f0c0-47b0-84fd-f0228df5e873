<?php

namespace micro\models;

use offgamers\publisher\models\Publisher;

class DirectTopUpModel extends \yii\base\Model
{

    const DTU_PUBLISHER_MAPPING = [
        'Razer' => 'RazerDTU'
    ];

    public function processTopUp($data)
    {
        if ($publisher = Publisher::findOne(['publisher_id' => $data['publisher_id']])) {
            $profile = (self::DTU_PUBLISHER_MAPPING[$publisher->profile] ?? $publisher->profile);
            $class = "\micro\models\publishers\\" . $profile;
            $publisher_profile = new $class;
            $publisher_profile->load($data, "");
            return $publisher_profile->processTopUp();
        }
    }

}