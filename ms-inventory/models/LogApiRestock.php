<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "log_api_restock".
 *
 * @property string $id
 * @property string $custom_products_code_id
 * @property string $method
 * @property string $sku
 * @property string $description
 * @property string $currency_code
 * @property string $currency_rate
 * @property string $currency_settle_amount
 * @property string $amount
 * @property string $settle_amount
 * @property string $token
 * @property string $ack
 * @property string $serialnumber
 * @property string $flag_state N: New, U: Uploading, S: In-stock, E: Error
 * @property string $error_msg
 * @property string $api_provider
 * @property string $created_datetime
 * @property string $publishers_id
 * @property string $purchase_orders_id
 * @property string $api_withdrawal_ref_id Ref ID for Payment Withdrawal
 * @property string $api_withdrawal_status API withdrawal status, 0=No Withdrawal 1=Processing, 2=Approved, 3=Canceled, 4=Paid
 * @property string $api_withdrawal_temp_status API withdrawal temp status, 0=unchecked 1=checked
 * @property string $api_withdrawal_date Start Processing Date for Withdrawal
 * @property string $api_cb_status API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed, 4=Debit Note
 * @property string $api_cb_temp_status API Charge Back temp status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed, 4=Debit Note, 5=Paid, 6=Un-Paid
 * @property string $api_cb_deduction_po_id
 * @property string $api_cb_deduction_status API Charge Back deduction status, 0=Available for deduction 1=Deducted
 * @property string $api_cb_date Start Processing Date for Charge Back
 * @property string $changed_by
 */
class LogApiRestock extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'created_datetime',
                'updatedAtAttribute' => false,
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    public static function tableName()
    {
        return 'log_api_restock';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['custom_products_code_id', 'publishers_id', 'purchase_orders_id', 'api_cb_deduction_po_id'], 'integer'],
            [['method', 'currency_settle_amount', 'amount', 'settle_amount', 'ack', 'flag_state', 'api_provider'], 'required'],
            [['currency_rate', 'currency_settle_amount', 'amount', 'settle_amount'], 'number'],
            [['created_datetime', 'api_withdrawal_date', 'api_cb_date'], 'safe'],
            [['description', 'token', 'serialnumber', 'error_msg','ack'], 'default', 'value' => ""],
            [['publishers_id'], 'default', 'value' => 0],
            [['api_withdrawal_status', 'api_withdrawal_temp_status', 'api_cb_status', 'api_cb_temp_status', 'api_cb_deduction_status'], 'string'],
            [['method', 'ack'], 'string', 'max' => 16],
            [['sku', 'description', 'token', 'serialnumber', 'error_msg'], 'string', 'max' => 255],
            [['currency_code'], 'string', 'max' => 3],
            [['flag_state'], 'string', 'max' => 1],
            [['api_provider', 'api_withdrawal_ref_id'], 'string', 'max' => 32],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'custom_products_code_id' => 'Custom Products Code ID',
            'method' => 'Method',
            'sku' => 'Sku',
            'description' => 'Description',
            'currency_code' => 'Currency Code',
            'currency_rate' => 'Currency Rate',
            'currency_settle_amount' => 'Currency Settle Amount',
            'amount' => 'Amount',
            'settle_amount' => 'Settle Amount',
            'token' => 'Token',
            'ack' => 'Ack',
            'serialnumber' => 'Serialnumber',
            'flag_state' => 'Flag State',
            'error_msg' => 'Error Msg',
            'api_provider' => 'Api Provider',
            'created_datetime' => 'Created Datetime',
            'publishers_id' => 'Publishers ID',
            'purchase_orders_id' => 'Purchase Orders ID',
            'api_withdrawal_ref_id' => 'Api Withdrawal Ref ID',
            'api_withdrawal_status' => 'Api Withdrawal Status',
            'api_withdrawal_temp_status' => 'Api Withdrawal Temp Status',
            'api_withdrawal_date' => 'Api Withdrawal Date',
            'api_cb_status' => 'Api Cb Status',
            'api_cb_temp_status' => 'Api Cb Temp Status',
            'api_cb_deduction_po_id' => 'Api Cb Deduction Po ID',
            'api_cb_deduction_status' => 'Api Cb Deduction Status',
            'api_cb_date' => 'Api Cb Date',
            'changed_by' => 'Changed By',
        ];
    }
}
