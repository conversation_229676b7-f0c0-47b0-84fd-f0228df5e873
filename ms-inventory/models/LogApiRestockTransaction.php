<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "log_api_restock_transaction".
 *
 * @property string $transaction_id
 * @property string $log_api_provider
 * @property string $log_method
 * @property string $log_sku
 * @property string $log_description
 * @property string $log_ack
 * @property string $log_flag_state
 * @property string $log_msg
 * @property string $log_created_datetime
 */
class LogApiRestockTransaction extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'log_api_restock_transaction';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['transaction_id'], 'required'],
            [['transaction_id'], 'integer'],
            [['log_created_datetime'], 'safe'],
            [['log_api_provider', 'log_sku'], 'string', 'max' => 32],
            [['log_method'], 'string', 'max' => 20],
            [['log_description', 'log_msg'], 'string', 'max' => 255],
            [['log_ack'], 'string', 'max' => 10],
            [['log_flag_state'], 'string', 'max' => 1],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'transaction_id' => 'Transaction ID',
            'log_api_provider' => 'Log Api Provider',
            'log_method' => 'Log Method',
            'log_sku' => 'Log Sku',
            'log_description' => 'Log Description',
            'log_ack' => 'Log Ack',
            'log_flag_state' => 'Log Flag State',
            'log_msg' => 'Log Msg',
            'log_created_datetime' => 'Log Created Datetime',
        ];
    }
}
