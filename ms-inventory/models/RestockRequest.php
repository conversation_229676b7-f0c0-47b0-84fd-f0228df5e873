<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "restock_request".
 *
 * @property int $restock_request_id
 * @property string $request_profile
 * @property string $request_profile_id
 * @property string|null $request_reference_id
 * @property int $status
 * @property int|null $created_at
 * @property int|null $updated_at
 *
 * @property ApiRestockRequest[] $apiRestockRequests
 */
class RestockRequest extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'restock_request';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            \yii\behaviors\TimestampBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['request_profile', 'request_profile_id', 'request_reference_id', 'status'], 'required'],
            [['status', 'created_at', 'updated_at'], 'integer'],
            [['request_profile'], 'string', 'max' => 100],
            [['request_profile_id', 'request_reference_id'], 'string', 'max' => 255],
            [['request_profile', 'request_profile_id', 'request_reference_id'], 'unique', 'targetAttribute' => ['request_profile', 'request_profile_id', 'request_reference_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'restock_request_id' => 'Restock Request ID',
            'request_profile' => 'Request Profile',
            'request_profile_id' => 'Request Profile ID',
            'request_reference_id' => 'Request Reference ID',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[ApiRestockRequests]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getApiRestockRequests()
    {
        return $this->hasMany(ApiRestockRequest::class, ['restock_request_id' => 'restock_request_id']);
    }

    public function setAsFailed()
    {
        $this->status = 0;
        $this->save();
    }
}