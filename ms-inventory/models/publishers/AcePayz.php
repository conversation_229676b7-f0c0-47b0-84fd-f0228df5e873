<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class AcePayz extends \yii\base\Model
{
    private $baseUrl;
    private $username;
    private $password;
    private $key;
    private $error_code;

    const purchase_initiation_url = 'api/Account/Authentication';
    const create_order_url = 'api/PinTopUp/Payment';
    const query_order_url = 'api/PinTopUp/Query';
    const cache_token_acepayz = 'cache_token_acepayz';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['acepayz.credential'])) {
            $config = $params['acepayz.credential'];
            $this->baseUrl = $config['base_url'];
            $this->username = $config['user_name'];
            $this->password = $config['password'];
            $this->key = $config['key'];
        } else {
            throw new InvalidArgumentException('Missing AcePayz credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'PinTopUp';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->error_code == 002 || $this->error_code == 003) {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                if ($this->error_code == 004) {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $productsId = $this->getPublisherProductId();
                $token = $this->getToken();

                if ($token) {
                    $params = [
                        "Username" => $this->username,
                        "Password" => $this->password,
                        "ReferenceCode" => 'OGM' . $this->api_restock_request_id,
                        "ProductCode" => $productsId
                    ];

                    $params['Signature'] = $this->generateSignature($params, 'order');

                    $response = $this->sendRequest($params, static::create_order_url, $token);
                    $data = $this->checkError($response);
                    if ($data) {
                        $this->parseOrderResponse($data);
                    }
                } else {
                    $this->createException('CREATE_ORDER');
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        try {
            $token = $this->getToken();
            if ($token) {
                $params = [
                    "Username" => $this->username,
                    "Password" => $this->password,
                    "ReferenceCode" => 'OGM' . $this->api_restock_request_id,
                ];

                $params['Signature'] = $this->generateSignature($params, 'query');

                $response = $this->sendRequest($params, static::query_order_url, $token);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } else {
                $this->createException('CHECK_ORDER');
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (!empty($data['Token'])) {
                    return $data;
                } elseif (isset($data['StatusCode'])) {
                    if ($data['StatusCode'] == 000) {
                        return $data;
                    } elseif ($data['StatusCode'] == 001 || $data['StatusCode'] == 205) {
                        $this->status_flag = -1;
                    }
                    $this->error_code = $data['StatusCode'];
                    $this->error_msg = $this->checkStatusMessage($data['StatusCode']). "\n" . 'ReferenceCode : ' . $data['ReferenceCode'];
                    $this->error = ['message' => $this->error_msg];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $exception_array = [002, 003, 004];

        if (!empty($this->error_code) && !in_array($this->error_code,$exception_array)) {
            $this->createException('CREATE_ORDER');
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        if (!empty($data['TransactionCode'])) {
            $this->publisher_order_id = (string) $data['TransactionCode'];
            $this->settlement_amount = $this->product_cost;
            $this->settlement_currency = $this->product_cost_currency;

            if (!empty($data['SerialNo']) && !empty($data['PinNo'])) {
                $this->status_flag = 1;
                $code = $this->parseCdKey(['Serial Number' => $data['SerialNo'], 'Pin Number' => $data['PinNo']]);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            } else {
                $this->createException('CDKEY', null);
            }
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . (is_array($value) ? implode(",", $value) : $value);
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function getToken()
    {
        // Get token cache if exist
        if ($token = Yii::$app->cache->get(static::cache_token_acepayz)) {
            return $token;
        }

        $params = [
            "Username" => $this->username,
            "Password" => $this->password,
        ];

        $params['Signature'] = $this->generateSignature($params, 'init');

        $response = $this->sendRequest($params, static::purchase_initiation_url);

        $data = $this->checkError($response);

        if (!empty($data['Token'])) {
            // cache token last 20hours
            Yii::$app->cache->set(static::cache_token_acepayz, $data['Token'], 72000);
            return $data['Token'];
        }

        return false;
    }

    private function sendRequest($params, $url, $token = '')
    {
        $this->getConfig();

        $headers['Accept'] = 'application/json';
        if ($token) {
            $headers['Authorization'] = 'Bearer ' . $token;
        }

        $options = array(
            'headers' => $headers,
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->baseUrl . '/' . $url, $options);
    }

    private function generateSignature($body, $type)
    {
        $string = '';
        switch ($type) {
            case 'init':
                $params = ['Username', 'Password'];
                break;
            case 'order':
                $body['MerchantKey'] = $this->key;
                $params = ['Username', 'Password', 'ReferenceCode', 'ProductCode', 'MerchantKey'];
                break;
            case 'query':
                $body['MerchantKey'] = $this->key;
                $params = ['Username', 'Password', 'ReferenceCode', 'MerchantKey'];
                break;
        }
        foreach ($params as $key) {
            if (isset($body[$key])) {
                $string .= $body[$key];
            }
        }
        return md5($string);
    }

    private function checkStatusMessage($code)
    {
        $message = '';
        switch ($code) {
            case 001:
                $message = 'Fail';
                break;
            case 002:
                $message = 'Pending';
                break;
            case 003:
                $message = 'Duplicate ID';
                break;
            case 004:
                $message = 'Transaction not found';
                break;
            case 005:
                $message = 'Invalid Account / Phone No';
                break;
            case 101:
                $message = 'Invalid Login';
                break;
            case 102:
                $message = 'Invalid Signature';
                break;
            case 103:
                $message = 'Insufficient Credit';
                break;
            case 201:
                $message = 'Invalid Product Code';
                break;
            case 202:
                $message = 'Invalid Denomination';
                break;
            case 203:
                $message = 'Invalid Price';
                break;
            case 204:
                $message = 'Invalid Cost';
                break;
            case 205:
                $message = 'Insufficient Inventory';
                break;
        }

        return "($code) " . $message;
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'ACEPAYZ';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }
}
