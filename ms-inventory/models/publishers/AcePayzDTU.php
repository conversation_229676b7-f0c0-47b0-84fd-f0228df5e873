<?php

namespace micro\models\publishers;

use micro\models\ApiLog;

class AcePayzDTU extends \offgamers\publisher\models\profile\Acepayz
{

    use DirectTopUpPublisherTrait;

    public function getAPIProvider()
    {
        return 'AcePayzDTU';
    }

    public function processTopUp()
    {
        $return_array = [];

        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;

        try {
            $this->getExistingConfig();
            if ($this->checkMargin()) {
                if ($this->sub_orders_top_up_status == 0 || ($this->sub_orders_top_up_status == 2 && empty($this->publisher_order_id))) {
                    $return_array['status'] = $this->createOrder();
                } elseif (($this->sub_orders_top_up_status == 1 && !empty($this->publisher_order_id)) || $this->sub_orders_top_up_status == 2) {
                    $return_array['status'] = $this->checkOrder();
                } else {
                    $this->error_msg = 'Prevent Duplicate Submission, check transaction history and update status / order id';
                }

                if ($return_array['status'] == true) {
                    $return_array['error_code'] = '0';
                } else {
                    $return_array['error_code'] = $this->error_code;
                }

                $return_array['publisher_ref_id'] = $this->publisher_order_id;
                $return_array['error_msg'] = $this->error_msg;
            } else {
                //Margin Block
                $return_array['error_code'] = '1510';
            }

        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }


    public function checkOrder()
    {
        $this->initClient(1, 'AcepayzDTU/check_order/' . $this->orders_id);
        $url = $this->configuration_data['API_ENDPOINT_QUERY'];

        $request_data = [
            'Username' => $this->configuration_data['API_LOGIN'],
            'Password' => $this->configuration_data['API_SECRET'],
            'ReferenceCode' => $this->orders_id,
        ];

        $request_data['signature'] = $this->generateSignature($request_data, 'order');
        return $this->processOrder($request_data,$url);

    }

    public function createOrder()
    {
        $this->initClient(1, 'AcepayzDTU/create_order/' . $this->orders_id);
        $url = $this->configuration_data['API_ENDPOINT_PAYMENT'];

        $request_data = [
            'Username' => $this->configuration_data['API_LOGIN'],
            'Password' => $this->configuration_data['API_SECRET'],
            'ReferenceCode' => $this->orders_id,
            'ProductCode' => $this->publisher_product_id,
        ];

        foreach ($this->game_account_info as $key => $value) {
            if (!empty($value)) {
                $request_data[$key] = $value;
            }
        }

        $request_data['signature'] = $this->generateSignature($request_data, 'order');
        return $this->processOrder($request_data,$url);
    }

    private function processOrder($request_data,$url)
    {
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $request_data, 'fixed_value_r');

        $token = $this->getToken();
        $response = $this->sendRequest($request_data,$url,$token);

        $data = $this->checkError($response);



        if ($data) {
            $api_log->endLog('2000', $data);
            $this->publisher_order_id = $data['TransactionCode'];
            return true;
        }else{
            $api_log->endLog('2000', $this->error);
        }

        return false;
    }


}