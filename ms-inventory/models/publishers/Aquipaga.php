<?php

namespace micro\models\publishers;

use micro\models\PublisherProduct;

class Aquipaga extends \offgamers\publisher\models\profile\Aquipaga
{
    protected $product_type_code;

    protected $product_option_code;

    protected $amount;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: AQPG_MY1_Voucher_24401_3000
        if (count($sku) == 5) {
            $this->account = $sku[1];
            $this->product_type_code = $sku[2];
            $this->product_option_code = $sku[3];
            $this->amount = (float)($sku[4] / 100);
        } else {
            throw new \Exception('Invalid Aquipaga SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return self::create_order_url;
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_msg == 'Invalid ExternalId or Transaction does not exists') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $endpoint = static::create_order_url;
            $payload = [
                'ExternalId' => 'OG_' . $this->api_restock_request_id,
                "ProductTypeCode" => $this->product_type_code,
                "ProductOptionCode" => $this->product_option_code,
                "Amount" => $this->amount,
                "Quantity" => 1,
            ];
            $response = $this->sendRequest($endpoint, $payload);
            if (($data = $this->checkError($response)) && !empty($data['PaymentResultData'])) {
                $this->parseCdKey($data['PaymentResultData']);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder()
    {
        $endpoint = static::check_order_url;
        $payload = [
            'ExternalId' => 'OG_' . $this->api_restock_request_id,
        ];
        $response = $this->sendRequest($endpoint, $payload);
        if (($data = $this->checkError($response)) && !empty($data['TransactionData'])) {
            $this->parseCdKey($data['TransactionData']);
        }
    }

    /**
     * @param array $payment_result
     * @return void
     */
    public function parseCdKey($payment_result)
    {
        $this->publisher_order_id = '' . $payment_result['TransactionId'];

        $code = [];

        if (!empty($payment_result['Reference'])) {
            $code[] = 'Code : ' . $payment_result['Reference'];
        }

        if (!empty($payment_result['Observations'])) {
            $code[] = 'Pin : ' . $payment_result['Observations'];
        }

        if (!empty($payment_result['ReedemUrl'])) {
            $code[] = 'Redeem URL : ' . $payment_result['ReedemUrl'];
        }

        if (!empty($payment_result['ReceiptMessage'])) {
            $code[] = 'Instructions : ' . $payment_result['ReceiptMessage'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'Aquipaga';
    }

    public function getAPIProvider()
    {
        return 'AQUIPAGA_' . $this->getAccount();
    }
}
