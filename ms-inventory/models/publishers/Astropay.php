<?php

namespace micro\models\publishers;

use yii\helpers\Json;

class Astropay extends \offgamers\publisher\models\profile\Astropay
{
    private $denomination, $currency, $region;
    const CREATE_ORDER_URL = "api/issuance/create";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
        $this->parseSKU();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
            case 3:
                $this->status_flag = 2;
                $this->error_code = "9999";
                $this->error_msg = "Please Check With The Status For This Voucher : " . $this->api_restock_request_id;
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];

        //create order
        if ($this->checkMargin()) {
            try {
                $this->getTokenFromPublisher();
                $this->setHeader();

                $params = [
                    "country" => $this->region,
                    "card_currency" => $this->currency,
                    "card_amount" => $this->denomination,
                    "quantity" => 1
                ];

                $response = $this->sendRequest('POST', static::CREATE_ORDER_URL, $this->headers, $params, $this->getAPIProvider() . '_' . $this->orders_id);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function parseOrderResponse($data)
    {
        $checklist = [
            "code" => "Code",
            "expiration_year" => "Expiration Year",
            "expiration_month" => "Expiration Month"
        ];

        $cd_key_data = array();

        if (isset($data['uuid']) && isset($data['cards']) && count($data['cards']) >= 1) {
            $this->publisher_order_id = (string)$data['uuid'] ?: "";

            foreach ($checklist as $checklist_key => $checklist_value) {
                if (!empty($data['cards'][0][$checklist_key])) {
                    $cd_key_data[$checklist_value] = $data['cards'][0][$checklist_key];
                }
            }

            $code = $this->parseCdKey($cd_key_data);

            if (!empty($code)) {
                $this->code_string = $code;
            }

            if (!empty($code) && !empty($this->publisher_order_id)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                $this->status_flag = 2;
                $this->error_msg = "The Order Does Not Contain Required Parameter .";
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        if (count($sku) == 4) {
            $this->region = $sku[1];
            $this->currency = $sku[2];
            $this->denomination = (float)($sku[3] / 100);
        } else {
            throw new \Exception('Invalid Astropay SKU Format');
        }
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Astropay';
    }

    public function getAPIProvider()
    {
        return 'ASTROPAY';
    }

    public function getOrderUrl()
    {
        return "voucherGenerate";
    }

    public function getDeno()
    {
        return 0;
    }

}