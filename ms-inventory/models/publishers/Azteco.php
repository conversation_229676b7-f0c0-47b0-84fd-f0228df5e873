<?php

namespace micro\models\publishers;

use Exception;
use Psr\Http\Message\ResponseInterface;
use Yii;
use yii\base\InvalidArgumentException;
use yii\base\Model;
use yii\helpers\Json;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\Encoding\Encoding;

class Azteco extends Model
{
    use RestockPublisherTrait {
        reset as defaultReset;
    }

    private const ON_CHAIN_VOUCHER = "OV";
    private const LIGHTNING_VOUCHER = "LV";
    private const STAGE_ORDER = "/stage/%s/%f";
    private const STAGE_LIGHTNING_ORDER = "/stage_lightning/%s/%d";
    private const FINALIZE_ORDER = "/order/%d";
    private const FINALIZE_LIGHTNING_ORDER = "/order_lightning/%d";
    private const ON_CHAIN_VOUCHER_CODE_URL_FORM = "https://azte.co/redeem?code=%s";
    private const RESPONSE_SUCCESS = "success";
    private const VOUCHER_URLS = [
        "stage_url" => [
            self::ON_CHAIN_VOUCHER => self::STAGE_ORDER,
            self::LIGHTNING_VOUCHER => self::STAGE_LIGHTNING_ORDER,
        ],
        "finalize_url" => [
            self::ON_CHAIN_VOUCHER => self::FINALIZE_ORDER,
            self::LIGHTNING_VOUCHER => self::FINALIZE_LIGHTNING_ORDER,
        ]
    ];

    private $base_url;
    private $api_key;

    public function __construct()
    {
        $params = Yii::$app->params;
        if (!empty($params['azteco.credential'])) {
            $config = $params['azteco.credential'];
            $this->base_url = $config['base_url'];
            $this->api_key = $config['api_key'];
        } else {
            throw new InvalidArgumentException('Missing Azteco credentials config.');
        }

        parent::__construct();
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);

        if (empty($this->sku)) {
            throw new InvalidArgumentException('Missing Azteco SKU.');
        }

        if (count(explode("_", $this->sku)) !== 4) {
            throw new InvalidArgumentException(
                'Invalid Azteco SKU. Must be in AZTC_{VOUCHER_TYPE}_{CURRENCY}_{DENO} format.'
            );
        }

        if ($this->getVoucherType() != self::ON_CHAIN_VOUCHER && $this->getVoucherType() != self::LIGHTNING_VOUCHER) {
            throw new InvalidArgumentException(
                'Voucher Type must be either OV (on-chain voucher) or LV (lightning voucher). '
                . $this->getVoucherType() . ' given.'
            );
        }

        if (!is_int($this->getDeno())) {
            throw new InvalidArgumentException(
                'Denomination must be an integer. ' . $this->getDeno() . ' given.'
            );
        }

        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    /**
     * 1. stage order
     * 2. finalize order
     * 3. generate qr
     * @throws Exception
     */
    public function processOrder($status)
    {
        switch ($status) {
            case 0:
            case 2:
                if ($this->checkMargin()) {
                    $order_id = $this->stageOrderAndGetOrderId();

                    $this->finalizeOrder($order_id);
                }
                break;
        }
    }

    /**
     * @throws Exception
     */
    private function stageOrderAndGetOrderId(): int
    {
        try {
            $stage_response = $this->sendRequest($this->getStageOrderUrl(), "POST");

            $this->handleResponse($stage_response);

            $data = $this->parseResponse($stage_response);

            return $data["order_id"];
        } catch (Exception $e) {
            $message = sprintf("Unable to complete stage order due to [%s]", $e->getMessage());
            $this->setErrors($message);
            $newException = new Exception($message);
            $this->createException('CREATE_ORDER', $newException);
        }
    }

    /**
     * @throws Exception
     */
    private function handleResponse(?ResponseInterface $response): void
    {
        if (!$response instanceof ResponseInterface) {
            $message = "No response from Azteco";
            $this->setErrors($message);
            $this->status_flag = 2;
            throw new Exception($message);
        }

        $data = $this->parseResponse($response);

        if (!isset($data["status"])) {
            $message = "No status response from Azteco";
            $this->setErrors($message, (string)$response->getBody(), $response->getStatusCode());
            $this->status_flag = 2;
            throw new Exception($message);
        }

        if ($data["status"] != self::RESPONSE_SUCCESS) {
            $message = $data["message"] ?? "Failure response from Azteco";
            $this->setErrors($message, (string)$response->getBody(), $response->getStatusCode());
            $this->status_flag = 2;
            throw new Exception($message);
        }
    }

    private function parseResponse(ResponseInterface $response): array
    {
        return Json::decode($response->getBody())[0];
    }

    private function setErrors(string $message, ?string $response = null, ?int $status_code = null): void
    {
        $this->error_msg = $message;
        $this->error = [
            'message' => $this->error_msg,
        ];

        if (!is_null($response)) {
            $this->error['response'] = $response;
        }

        if (!is_null($status_code)) {
            $this->error['http_status'] = $status_code;
        }
    }

    /**
     * @throws Exception
     */
    private function finalizeOrder(int $order_id): void
    {
        try {
            $finalize_response = $this->sendRequest($this->getFinalizeOrderUrl($order_id), "PUT");

            $this->handleResponse($finalize_response);

            $data = $this->parseResponse($finalize_response);

            if (empty($data["reference_code"])) {
                throw new Exception("Missing reference code");
            }

            $this->publisher_order_id = $data["reference_code"];

            if ($this->getVoucherType() == self::ON_CHAIN_VOUCHER) {
                if (empty($data["voucher_code"])) {
                    throw new Exception("Missing on-chain voucher code `voucher_code`");
                }

                // https://api.azte.co/v1/docs/?shell#auto-populate-voucher-code-form-on-chain
                // https://azte.co/redeem?code=voucher_code
                $url_code = sprintf(self::ON_CHAIN_VOUCHER_CODE_URL_FORM, $data["voucher_code"]);
            } else {
                if (empty($data["lnurl"])) {
                    throw new Exception("Missing lightning voucher lightning url `lnurl`");
                }

                $url_code = $data["lnurl"];
            }

            $this->generateQrCode($url_code);

            $this->status_flag = 1;
        } catch (Exception $e) {
            $this->status_flag = $this->status_flag == 2 ? $this->status_flag : 3;
            $message = sprintf(
                "Unable to complete finalize stage due to : [%s] - Azteco order_id : %d",
                $e->getMessage(),
                $order_id
            );
            $this->setErrors($message);
            $newException = new Exception($message);
            $this->createException('CREATE_ORDER', $newException);
        }
    }

    /**
     * @throws Exception
     */
    private function generateQrCode(string $url_code): void
    {
        try {
            $qr = QrCode::create($url_code)
                ->setSize(300)
                ->setEncoding(new Encoding('UTF-8'));

            $writer = new PngWriter();

            $result = $writer->write($qr);

            // will be base64 encoded in CdKeyCom::encryptKey()
            $this->code_string = $result->getString();

            $this->code_type = "png";
        } catch (Exception $e) {
            $message = sprintf("Unable to generate QR code due to [%s]", $e->getMessage());
            throw new Exception($message);
        }
    }

    private function sendRequest(string $voucher_path, string $method): ?ResponseInterface
    {
        $options['headers'] = [
            "X-API-KEY" => $this->api_key
        ];

        $options['http_errors'] = false;

        $url = sprintf("%s%s", $this->base_url, $voucher_path);

        return $this->request($method, $url, $options);
    }

    private function getStageOrderUrl(): string
    {
        $voucher_type = $this->getVoucherType();

        $currency = $this->getCurrency();

        return sprintf(
            self::VOUCHER_URLS['stage_url'][$voucher_type],
            $currency,
            $this->getDeno()
        );
    }

    private function getFinalizeOrderUrl(int $order_id): string
    {
        $voucher_type = $this->getVoucherType();

        return sprintf(self::VOUCHER_URLS['finalize_url'][$voucher_type], $order_id);
    }

    private function getVoucherType(): string
    {
        return strtoupper(explode("_", $this->sku)[1]);
    }

    private function getCurrency(): string
    {
        return strtoupper(explode("_", $this->sku)[2]);
    }

    public function getOrderUrl()
    {
        return 'azteco/order';
    }

    public function getDeno()
    {
        return intval(explode("_", $this->sku)[3]);
    }

    public function getAPIProvider()
    {
        return 'AZTECO';
    }

    public function reset()
    {
        $this->defaultReset();
    }
}
