<?php

namespace micro\models\publishers;

use micro\components\CdKeyCom;
use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use micro\models\CustomProductsCode;
use micro\models\LogApiRestock;
use offgamers\base\models\DevDebugLog;
use offgamers\base\models\ms\Product;
use offgamers\publisher\models\Publisher;
use Psr\Http\Message\ResponseInterface;
use Ramsey\Uuid\Uuid;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Bamboo extends \offgamers\publisher\models\profile\Bamboo
{
    private $accounts;
    private $quantity = 1;
    private $region;
    private $voucher_param = [
        'Serial Number' => 'serialNumber',
        'Card Code' => 'cardCode',
        'PIN' => 'pin',
        'Expiry Date' => 'expirationDate',
    ];

    private $final_status = ["Succeeded", "Failed", "PartialFailed"];

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function getUrl($type)
    {
        switch ($type) {
            case 'getAccounts':
                $url = self::ACCOUNT_URL;
                break;
            case 'createOrder':
                $url = self::ORDER_URL;
                break;
            case 'checkOrder':
                $url = self::CHECK_ORDER_URL;
                break;
            default:
                $url = false;
        }
        return $url;
    }

    public function processOrder($status)
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->getAccounts();
                $this->createOrder();
                if (empty($this->error_msg)) {
                    sleep(3);
                    $this->checkOrder();
                }
                break;
            case 2:
            case 3:
                if (isset($this->publisher_order_id)) {
                    $this->checkOrder();
                } else {
                    $this->getAccounts();
                    $this->createOrder();
                }
                break;
        }
    }

    private function getAccounts()
    {
        if (!isset($this->accounts)) {
            $url = $this->getUrl('getAccounts');
            $method = 'GET';

            try {
                $response = $this->sendRequest($method, $url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseAccounts($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }

        if (isset($this->accounts[$this->region])) {
            return $this->accounts[$this->region];
        }

        $this->error_msg = "Currency ({$this->region}) not active on bamboo api";
        $this->createException('CREATE_ORDER');
    }

    private function parseAccounts($data)
    {
        if (isset($data['accounts'])) {
            foreach ($data['accounts'] as $account) {
                $this->accounts[$this->region] = $account;
            }
        }
    }

    public function createOrder($batch = false)
    {
        if (empty($this->publisher_order_id)) {
            $uuid = (string)$this->getRequestId();
            $this->publisher_order_id = $uuid;
        }
        $params = [
            'RequestId' => $this->publisher_order_id,
            'AccountId' => $this->accounts[$this->region]['id'],
            'Products' => [
                [
                    'ProductId' => $this->getPublisherProductId(),
                    'Quantity' => $this->quantity,
                    'Value' => $this->getProductValue()
                ]
            ]
        ];
        $url = $this->getUrl('createOrder');
        $method = 'POST';

        if ($batch || $this->checkMargin()) {
            try {
                $response = $this->sendRequest($method, $url, $params);
                $data = $this->checkError($response);
                if ($data) {
                    $this->status_flag = 2;
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            } finally {
                // Rate Limiting from publisher, sleep for 1 second after every request
                sleep(1);
            }
        }

        return true;
    }

    public function checkOrder($batch = false)
    {
        $url = $this->getUrl('checkOrder');
        $url .= $this->publisher_order_id;
        $method = 'GET';

        try {
            $response = $this->sendRequest($method, $url);
            $data = $this->checkError($response);
            if ($batch) {
                return $data;
            } else {
                return $this->parseCheckOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function parseCheckOrderResponse($data)
    {
        if ($this->publisher_order_id == $data['requestId']) {
            if ($data['status'] == 'Succeeded') {
                $items = $data['items'];
                if (!empty($items) && count($items) == 1) {
                    foreach ($items as $item) {
                        $cards = $item['cards'];
                        $this->settlement_amount = $data['total'];
                        $this->settlement_currency = $item['currencyCode'];
                        if (!empty($cards)) {
                            foreach ($cards as $card) {
                                $code = $this->parseCdKey($card);
                            }
                            if (!empty($code)) {
                                $this->code_string = $code;
                                $this->status_flag = 1;
                            }
                        } else {
                            $this->createException('CDKEY', null);
                        }
                    }
                } else {
                    $this->createException('CDKEY', null);
                }
            } elseif (in_array($data['status'], $this->final_status)) {
                $this->status_flag = -1;
                return false;
            } else {
                $this->status_flag = 2;
                return false;
            }
        }
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                return $data;
            } else {
                if (!empty($data)) {
                    if (isset($data['message'])) {
                        $this->error_code = $status;
                        $this->error_msg = $this->error_code . ' - ' . $data['message'];
                    } else {
                        $this->error_code = $status;
                        $this->error_msg = $this->error_code . ' - ' . $data['title'];
                    }
                } else {
                    $this->error_code = $status;
                    $this->error_msg = $this->error_code . ' - ' . $response->getReasonPhrase();
                }

                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        if (!empty($this->error_code)) {
            if ($this->error_code == '400') {
                $this->status_flag = -1;
                $this->createException('CREATE_ORDER');
            } else {
                if ($this->error_code == '404') {
                    $this->status_flag = 0;
                    $this->createException('CHECK_ORDER');
                }
            }
        }

        return false;
    }

    private function parseCdKey($voucher)
    {
        $code = [];

        if (isset($voucher['status']) && $voucher['status'] == 'Sold') {
            foreach ($this->voucher_param as $key => $value) {
                if (!empty($voucher[$value])) {
                    $code[] = $key . ' : ' . $voucher[$value];
                }
            }
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function getRequestId()
    {
        return Uuid::uuid4();
    }

    private function getRegion()
    {
        return explode("_", $this->sku)[1];
    }


    private function getPublisherProductId()
    {
        return (int)explode("_", $this->sku)[2];
    }

    private function getProductValue()
    {
        $value = (int)explode("_", $this->sku)[3];
        return number_format($value / 100, 2, '.', '');
    }

    public function getDeno()
    {
        return 0;
    }

    public function getOrderUrl()
    {
        return 'createOrder';
    }

    public function getApiClassName()
    {
        return 'BAMBOO';
    }


    public function getAPIProvider()
    {
        return 'BAMBOO_' . $this->getRegion();
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function batchRestock()
    {
        $bamboo_restock_data = Yii::$app->params['bamboo.restock_info'];

        foreach ($bamboo_restock_data as $p_id => $p_info) {
            $current_stock_level = CustomProductsCode::find()->where([
                'products_id' => $p_id,
                'status_id' => '1'
            ])->count();

            $pending_restock_quantity = ((new \yii\db\Query())->select("SUM(i.value)")
                ->from('api_restock_request t')
                ->leftJoin('api_restock_request_extra_info i', 't.api_restock_request_id = i.api_restock_request_id AND i.key="QUANTITY"')
                ->where([
                    'IN',
                    't.status',
                    [0, 3]
                ])->andWhere(['t.products_id' => $p_id])->scalar() ?? 0);

            $stock_level = $current_stock_level + $pending_restock_quantity;

            if ($p_info['RESTOCK_LEVEL'] >= $stock_level) {
                $restock_quantity = $p_info['RESTOCK_TO_QUANTITY'] - $stock_level;
                $single_order_quantity = 100;
                $full_order_quantity = intval($restock_quantity / $single_order_quantity);
                $partial_order_quantity = $restock_quantity % $single_order_quantity;

                for ($i = 0; $full_order_quantity > $i; $i++) {
                    $model = new ApiRestockRequest();
                    $model->status = 0;
                    $model->products_id = $p_id;
                    $model->save();

                    ApiRestockRequestExtraInfo::createExtraInfoLine([
                        'api_restock_request_id' => $model->api_restock_request_id,
                        'key' => 'QUANTITY',
                        'value' => '100',
                    ]);
                }

                if ($partial_order_quantity > 0) {
                    $model = new ApiRestockRequest();
                    $model->status = 0;
                    $model->products_id = $p_id;
                    $model->save();

                    ApiRestockRequestExtraInfo::createExtraInfoLine([
                        'api_restock_request_id' => $model->api_restock_request_id,
                        'key' => 'QUANTITY',
                        'value' => (string)$partial_order_quantity,
                    ]);
                }
            }
        }

        $this->checkExistingOrder();

        $this->createBatchRequest();
    }

    private function createBatchRequest()
    {
        $processing_request = ApiRestockRequest::find()->where(['status' => 0, 'orders_products_id' => null])->andWhere(['products_id' => array_keys(Yii::$app->params['bamboo.restock_info'])])->all();
        /**
         * @var ApiRestockRequest[] $processing_request
         */
        foreach ($processing_request as $request) {
            $p_info = Yii::$app->params['bamboo.restock_info'];
            if ($this->products_id != $request->products_id) {
                $this->products_id = $request->products_id;
                $this->sku = $p_info[$this->products_id]['SKU'];
                $this->getProductCost();
            }
            $this->getAccounts();
            $this->quantity = $this->getRestockQuantity($request->api_restock_request_id);
            $this->api_restock_request_id = $request->api_restock_request_id;
            $this->publisher_order_id = $request->publisher_order_id;
            $lock_status = false;
            try {
                if ($lock_status = $request->obtainLock()) {
                    $this->createOrder(true);
                }
            } catch (\Exception $e) {
                if (empty($this->error_msg)) {
                    $this->error_msg = $e->getMessage();
                }
                $this->reportBatchError();
            } finally {
                if (!empty($this->publisher_order_id)) {
                    $request->publisher_order_id = $this->publisher_order_id;
                    $request->status = 3;
                    $request->save();
                }
                if ($lock_status) {
                    $request->releaseLock();
                }
                $this->reset();
            }
        }
    }

    private function checkExistingOrder($order_id = '')
    {
        $processing_request = ApiRestockRequest::find()
            ->where(['status' => [3], 'orders_products_id' => null])
            ->andWhere(['products_id' => array_keys(Yii::$app->params['bamboo.restock_info'])]);

        if ($order_id) {
            $processing_request->andWhere(['publisher_order_id' => $order_id]);
        }
        $processing_request = $processing_request->all();
        /**
         * @var ApiRestockRequest[] $processing_request
         */
        foreach ($processing_request as $request) {
            $p_info = Yii::$app->params['bamboo.restock_info'];
            $this->sku = $p_info[$request->products_id]['SKU'];
            $this->getAccounts();
            $lock_status = false;
            try {
                if ($lock_status = $request->obtainLock()) {
                    if (!empty($request->publisher_order_id)) {
                        $this->publisher_order_id = $request->publisher_order_id;
                        $this->api_restock_request_id = $request->api_restock_request_id;
                        $this->products_id = $request->products_id;
                        $cpc_id_list = [];
                        $delivered_qty = 0;
                        $request_qty = $this->getRestockQuantity($this->api_restock_request_id);
                        if ($data = $this->checkOrder(true)) {
                            if ($this->publisher_order_id == $data['requestId']) {
                                if (in_array($data['status'], $this->final_status)) {
                                    $items = $data['items'];
                                    if (!empty($items) && count($items) == 1) {
                                        if ($this->getStockQuantity() > 0) {
                                            throw new \Exception('CDKey already created for this request, manual adjustment required');
                                        }
                                        foreach ($items as $item) {
                                            $cards = $item['cards'];
                                            $this->settlement_amount = number_format($data['total'] / count($cards), 2);
                                            $this->settlement_currency = $this->getRegion();
                                            if (!empty($cards)) {
                                                foreach ($cards as $card) {
                                                    $code = $this->parseCdKey($card);
                                                    $cpc_id_list[] = $this->createCustomProductsCode($code);
                                                }
                                            }
                                        }
                                        if (!empty($cpc_id_list)) {
                                            $delivered_qty = count($cpc_id_list);
                                            (new Product())->updateProductsQty([
                                                [
                                                    'products_id' => $this->products_id,
                                                    'custom_products_code' => $cpc_id_list,
                                                    'qty' => $delivered_qty,
                                                ],
                                            ]);
                                            ApiRestockRequestExtraInfo::createExtraInfoLine([
                                                'api_restock_request_id' => $this->api_restock_request_id,
                                                'key' => 'DELIVERED_QUANTITY',
                                                'value' => (string)$delivered_qty,
                                            ]);
                                            $request->status = 1;
                                            $request->save();
                                            $this->validateOrderCount($this->products_id, $request_qty, $delivered_qty);
                                        } else {
                                            $request->status = 2;
                                            $request->save();
                                            $this->validateOrderCount($this->products_id, $request_qty, $delivered_qty);
                                        }
                                    }
                                }
                            } else {
                                $this->error_msg = 'Order Id Mismatched';
                                $this->reportBatchError();
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                if ($this->error_code == 404) {
                    $request->status = 0;
                    $request->save();
                } else {
                    $this->error_msg = empty($this->error_msg) ? $e->getMessage() : $this->error_msg;
                    $this->reportBatchError();
                }
            } finally {
                if ($lock_status) {
                    $request->releaseLock();
                }
                $this->reset();
            }
        }
    }

    public function getProductCost()
    {
        $info = (new Product())->getProductInfoByOrdersId(['products_id' => $this->products_id]);

        $this->product_cost = (!empty($info['product_cost']) ? $info['product_cost'] : $info['product_original_cost']);
        $this->product_cost_currency = (!empty($info['product_cost_currency']) ? $info['product_cost_currency'] : $info['product_original_currency']);

        if (empty($this->product_cost) || empty($this->product_cost_currency)) {
            throw new \Exception('Missing Products Cost for ' . $this->products_id);
        }
    }

    private function getRestockQuantity($api_restock_request_id)
    {
        return ApiRestockRequestExtraInfo::find()->select('value')->where([
            'api_restock_request_id' => $api_restock_request_id,
            'key' => 'QUANTITY'
        ])->scalar();
    }

    private function getStockQuantity()
    {
        return LogApiRestock::find()
            ->where(['serialnumber' => $this->publisher_order_id, 'flag_state' => 'S'])
            ->count();
    }

    private function validateOrderCount($products_id, $request_qty, $delivered_qty)
    {
        if ($request_qty != $delivered_qty) {
            Yii::$app->slack->send("Bamboo Batch : Partial Delivery Requested Qty ($request_qty) > Delivered Quantity ($delivered_qty) - $products_id");
        }
    }

    private function createCustomProductsCode($code_string)
    {
        $cdkey_com = new CdKeyCom();
        $cdkey_extra_info = ['file_type' => 'soft', 'remarks' => "BAMBOO_API", 'custom_products_code_id' => null];

        // Upload CDK to S3
        $cdk_model = $cdkey_com->uploadCdKey($this->products_id, $code_string, $cdkey_extra_info);
        $custom_products_code_id = $cdk_model->custom_products_code_id;

        $this->logAPIRequest([
            'method' => $this->getOrderUrl(),
            'custom_products_code_id' => $custom_products_code_id,
            'serialnumber' => $this->publisher_order_id,
            'publisher_id' => $this->log_publisher_id,
            'sku' => $this->sku
        ]);

        return $custom_products_code_id;
    }

    private function reportBatchError()
    {
        Yii::$app->slack->send("Bamboo Batch Delivery Error : $this->products_id", [
            [
                'color' => 'warning',
                'text' => "Product Id : " . $this->products_id . "\n Publisher Order Id : " . $this->publisher_order_id . "\n Error : " . $this->error_msg,
            ],
        ], 'BDT_REPLENISH');
    }

    protected function sendRequest(string $method, string $path, array $params = []): ?ResponseInterface
    {
        $this->region = $this->getRegion();
        $this->publisher_id = Publisher::find()->where([
            'title' => 'Bamboo ' . $this->region,
            'profile' => 'Bamboo',
            'status' => 1
        ])->scalar();

        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        return parent::sendRequest($method, $path, $params);
    }
}

