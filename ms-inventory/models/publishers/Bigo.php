<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Bigo extends \yii\base\Model
{

    private $base_url;
    private $channel_id;
    private $channel_secret;
    private $public_ip;
    private $error_code;
    static $order_url = 'card/create';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['bigo.credential'])) {
            $config = $params['bigo.credential'];
            $this->base_url = $config['base_url'];
            $this->channel_id = $config['channel_id'];
            $this->channel_secret = $config['channel_secret'];
            $this->public_ip = $config['public_ip'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->getAPIProvider() . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return "card/create";
    }

    public function processOrder($status)
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;

        switch ($status) {
            case 0:
            case 2:
            case 3:
                $this->createOrder();
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $this->getConfig();

                $params = [
                    'channelId' => $this->channel_id,
                    'requestUniqueKey' => (string)$this->api_restock_request_id,
                    'amount' => $this->getPublisherProductAmount(),
                    'diamond' => $this->getPublisherDenoAmount(),
                    'currency' => 'USD',
                    'country' => $this->phone_country_code
                ];

                $response = $this->sendRequest($params, static::$order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseCdKey($data);
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (isset($data['code']) && ($data['code'] == 0)) {
                    $this->publisher_order_id = (string) $this->api_restock_request_id;
                    return $data;
                } elseif (isset($data['code'])) {
                    $this->error_code = $data['code'];
                    $this->error_msg = 'status : ' . $data['code'] . "\n" . 'message : ' . $data['message'];
                    $this->error = ['message' => $this->error_msg];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $status,
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }

            $this->createException('CREATE_ORDER');
        }

        return false;
    }


    private function parseCdKey($data)
    {
        if (isset($data['data']['cardId'])) {
            $this->status_flag = 1;
            $this->code_string = $data['data']['cardId'];
        }
        return null;
    }

    private function sendRequest($params, $url)
    {
        $options = [
            'headers' => [
                'sign' => $this->generateSignature($params)
            ],
            'json' => $params,
        ];

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getPublisherDenoAmount()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductAmount()
    {
        return explode("_", $this->sku)[1];
    }

    private function generateSignature($params)
    {
        $raw = Json::encode($params) . $this->public_ip;
        return strtolower(hash_hmac('sha256', $raw, $this->channel_secret));
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'Bigo';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

}
