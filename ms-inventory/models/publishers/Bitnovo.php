<?php

namespace micro\models\publishers;

use yii\helpers\Json;
use yii\base\InvalidArgumentException;

class Bitnovo extends \offgamers\publisher\models\profile\Bitnovo
{
    private $error_code;
    const CREATE_ORDER_URL = "BuyBitcoin/Order";
    const QUERY_ORDER_URL = "PurchaseCryptocurrencies/Order";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->error_code == "206") {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                if ($this->error_code == '100') {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];
        //create order
        if ($this->checkMargin()) {
            try {
                if ($this->getDeno() == 0) {
                    throw new InvalidArgumentException('Denomination given is 0 .');
                }

                $params = [
                    "amountEuroCents" => $this->getDeno(),
                    "apiUniqueId" => $this->api_restock_request_id,
                    "validateCustomer" => false,
                    "custom" => '',
                    "email" => '',
                    "phone" => '',
                ];

                $response = $this->sendRequest('POST', static::CREATE_ORDER_URL, $params);
                $data = $this->checkError($response);

                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        //check order
        try {
            if (empty($this->publisher_order_id)) {
                $this->createOrder();
            } else {
                $response = $this->sendRequest('GET', static::QUERY_ORDER_URL . "/" . $this->publisher_order_id);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    protected function checkError($response)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data)) {
                    if (isset($data['order']) && isset($data['hasError']) && !$data['hasError']) {
                        return $data;
                    } else {
                        if (!empty($data['errorCode']) && !empty($data['description'])) {
                            $this->error_code = $data['errorCode'];
                            $this->error_msg = $data['errorCode'] . ' - ' . $data['description'];
                        }
                    }
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (Bitnovo) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }

    private function parseOrderResponse($data)
    {
        $checklist = [
            "pin" => "Pin",
            "code" => "Code",
        ];

        $cd_key_data = array();
        if (isset($data['order']) && count($data['order']) >= 1) {
            $this->publisher_order_id = (string)$data['order']['orderId'] ?: "";

            foreach ($checklist as $checklist_key => $checklist_value) {
                if (!empty($data['order'][$checklist_key])) {
                    $cd_key_data[$checklist_value] = $data['order'][$checklist_key];
                }
            }

            $code = $this->parseCdKey($cd_key_data);

            if (!empty($code) && !empty($this->publisher_order_id)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                $this->status_flag = 2;
                $this->error_msg = "The Order Does Not Contain Required Parameter .";
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function getDeno()
    {
        return intval(explode("_", $this->sku)[1]);
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Bitnovo';
    }

    public function getAPIProvider()
    {
        return 'BITNOVO';
    }

    public function getOrderUrl()
    {
        return static::CREATE_ORDER_URL;
    }

}