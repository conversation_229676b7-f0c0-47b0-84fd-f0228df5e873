<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;
use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use offgamers\base\models\OutgoingRequestLog;

class BlackHawkNetwork extends \yii\base\Model
{
    private $base_url;
    private $url_list;
    private $mid;
    private $account_num;
    private $spec_version = '46';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->retry_attempt = 1;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        $region = explode("_", $this->sku)[1];
        if (!empty($params['bhn.credential'][$region])) {
            $config = $params['bhn.credential'][$region];
            $this->account_num = $config['account_num'];
            $this->mid = $config['mid'];
            $this->url_list = $config['base_url'];
        } else {
            throw new InvalidArgumentException('Missing BlackHawk credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'transaction';
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
            case 2:
                if ($this->checkMargin()) {
                    $this->processRequest('order');
                }
                break;
        }

    }

    public function processRequest($type, $trans_id = '')
    {
        switch ($type) {
            case 'order':
                $code = '745400';
                $key = 'REQUEST';
                break;
            case 'refund':
                $code = '775400';
                $key = 'REFUND';
                break;
            default:
                return false;
        }
        $this->getConfig();

        if (!empty($this->base_url) || $this->checkServerStatus()) {
            try {
                $params = $this->generateRequestParams($code);
                if ($type == 'refund') {
                    $data['request']['transaction']['retrievalReferenceNumber'] = $this->getRetrievalReferenceNumber();
                }
                if ($trans_id) {
                    $params['request']['transaction']['additionalTxnFields']['referenceTransactionId'] = $trans_id;
                }

                ApiRestockRequest::createExtraInfo($this->api_restock_request_id, $key . '_PARAMS', Json::encode(['attribute' => $this->getAttributes(), 'params' => $params]));

                $response = $this->sendRequest($params);

                $data = $this->checkError($response, $key);

                if ($data) {
                    switch ($type) {
                        case 'order':
                            $this->parseOrderResponse($data);
                            break;
                        case 'refund':
                            if (!$data) {
                                return false;
                            }
                            break;
                    }
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }

        return true;
    }

    public function reverseTransaction($type)
    {
        $data = ApiRestockRequest::getExtraInfo($this->api_restock_request_id);

        if (isset($data['VOID_' . $type . '_TRANSACTION_STATUS']) && isset($data[$type . '_PARAMS'])) {
            if ($data['VOID_' . $type . '_TRANSACTION_STATUS'] == 0) {
                $request_data = Json::decode($data[$type . '_PARAMS']);
                $this->load($request_data['attribute'], '');
                $this->getConfig();
                if (!empty($this->base_url) || $this->checkServerStatus()) {
                    $params = $this->refreshData($request_data['params']);
                    $response = $this->sendRequest($params, '/reverse');
                    $data = $this->checkError($response);
                    if ($data) {
                        ApiRestockRequestExtraInfo::deleteAll(['api_restock_request_id' => $this->api_restock_request_id, 'key' => 'VOID_' . $type . '_TRANSACTION_STATUS']);
                        return true;
                    } else {
                        $task_sqs = Yii::$app->aws->getSQS('TASK_QUEUE');
                        $task_sqs->pushMessage(['data' => ['service' => 'Inventory', 'controller' => 'restock', 'action' => 'void-black-hawk-transaction', 'params' => ['api_restock_request_id' => $this->api_restock_request_id]]], 900);
                    }
                }
            }
        }

        return false;
    }

    private function checkError($response, $type = '')
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            // Response Code 35 : Already Refunded
            if ($status == 200 && isset($data['response']['transaction']['responseCode']) && ($data['response']['transaction']['responseCode'] == 00 || ($type != 'REQUEST' && $data['response']['transaction']['responseCode'] == 35))) {
                return $data['response']['transaction'];
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->status_flag = -1;

        if ($type && ($status > 400 || (isset($data['response']['transaction']['responseCode']) && $this->isSoftDecline($data['response']['transaction']['responseCode'])))) {

            ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'VOID_' . $type . '_TRANSACTION_STATUS', '0');

            if ($type) {
                $this->reverseTransaction($type);
            }
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        $this->publisher_order_id = (string)$data['additionalTxnFields']['transactionUniqueId'];
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;

        if (!empty($data['additionalTxnFields'])) {
            $this->status_flag = 1;
            $code = $this->parseCdKey($data['additionalTxnFields']);
            if (!empty($code)) {
                $this->code_string = $code;
            }
        }
    }

    private function parseCdKey($coupons)
    {
        $code = [];
        foreach ($coupons as $key => $value) {
            switch ($key) {
                case 'redemptionPin':
                    $description = 'Redemption Pin';
                    break;
                case 'activationAccountNumber':
                    $description = 'Activation Account Number';
                    break;
                case 'redemptionAccountNumber':
                    $description = 'Redemption Account Number';
                    break;
                case 'expiryDate':
                    $description = 'Expiration Date (YYMMDDD)';
                    break;
                default:
                    continue 2;
            }
            $code[] = $description . ' : ' . $value;
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params, $url = '')
    {
        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . $url, $options);
    }

    private function generateRequestParams($code)
    {
        $request_time = $this->getRequestTime();

        return [
            'request' => [
                'header' => [
                    'signature' => 'BHNUMS',
                    'details' => [
                        'productCategoryCode' => '01',
                        'statusCode' => '00',
                        'specVersion' => $this->spec_version
                    ]
                ],
                'transaction' => [
                    'primaryAccountNumber' => $this->account_num,
                    'processingCode' => $code,
                    'transactionAmount' => $this->getPublisherProductAmount(),

                    'transmissionDateTime' => $request_time,

                    'localTransactionDate' => substr($request_time, 0, 6),
                    'localTransactionTime' => substr($request_time, 6),
                    'merchantCategoryCode' => '5411',
                    //Fixed Value : Digital Retailer
                    'pointOfServiceEntryMode' => '041',
                    //MID
                    'acquiringInstitutionIdentifier' => $this->mid,
                    'merchantIdentifier' => $this->mid . '    ',

                    'systemTraceAuditNumber' => $this->getSystemTraceAuditNumber(),
                    'retrievalReferenceNumber' => $this->getRetrievalReferenceNumber(),

                    //Fixed Value
                    'merchantTerminalId' => '00001     001   ',

                    'merchantLocation' => 'OFFGAMERS0000000000000000000000000000000',
                    //Fixed Value : IDR
                    'transactionCurrencyCode' => $this->getCurrencyIsoCode(),

                    'additionalTxnFields' => [
                        'productId' => $this->getPublisherProductId()
                    ]
                ]
            ]
        ];

    }


    private function refreshData($data)
    {
        $data['request']['transaction']['transmissionDateTime'] = $this->getRequestTime();
        $data['request']['transaction']['systemTraceAuditNumber'] = $this->getSystemTraceAuditNumber();
        return $data;
    }

    private function isSoftDecline($code)
    {
        if ($code == '74' || $code == '15') {
            return true;
        }
        return false;
    }

    private function getFakeRequestId()
    {
        // Generate Fake System Trace Audit Number
        return Yii::$app->db_log->createCommand('select max(outgoing_request_log_id) as max from ' . OutgoingRequestLog::tableName())->queryScalar();
    }

    private function getSystemTraceAuditNumber()
    {
        $next_outgoing_log_id = $this->getFakeRequestId();

        return str_pad(($next_outgoing_log_id + 1) % 1000000, 6, '0', STR_PAD_LEFT);
    }

    private function getRetrievalReferenceNumber()
    {
        $next_outgoing_log_id = $this->getFakeRequestId();

        return str_pad(($next_outgoing_log_id + 1) % 1000000000000, 12, '0', STR_PAD_LEFT);
    }


    private function getRequestTime()
    {
        $date = new \DateTime("now", new \DateTimeZone('UTC'));
        return $date->format('ymdHis');
    }

    private function checkServerStatus()
    {
        foreach ($this->url_list as $i => $url) {
            $this->base_url = $url;
            $cache_key = '/bhn/server_status/' . md5($url);

            $status = Yii::$app->cache->getOrSet($cache_key, function () {
                $params = [
                    "request" => [
                        "header" => [
                            "signature" => "BHNUMS",
                            "details" => [
                                "productCategoryCode" => "01",
                                "specVersion" => $this->spec_version
                            ]
                        ],
                        "transaction" => [
                            "transmissionDateTime" => $this->getRequestTime(),
                            "systemTraceAuditNumber" => $this->getSystemTraceAuditNumber(),
                            "networkManagementCode" => "301"
                        ]
                    ]
                ];
                $response = $this->sendRequest($params, '/network');
                try {
                    if ($response->getStatusCode() == 200) {
                        return true;
                    }
                } catch (\Exception $e) {
                    return false;
                }
            }, 900);

            if (!$status) {
                if (count($this->url_list) == $i + 1) {
                    $this->createException('CREATE_ORDER', null, ['Publisher Server Not Reachable']);
                }
            }

            return $status;
        }
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductAmount()
    {
        return str_pad(explode("_", $this->sku)[3] . '00', 12, '0', STR_PAD_LEFT);
    }

    private function getCurrencyIsoCode()
    {
        $iso_code = '';
        switch (explode("_", $this->sku)[1]) {
            case 'ID':
                $iso_code = '360';
                break;
        }

        return $iso_code;
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'BLACKHAWK';
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

