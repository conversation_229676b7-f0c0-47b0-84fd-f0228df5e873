<?php

namespace micro\models\publishers;

use offgamers\publisher\models\Publisher;
use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use Dompdf\Dompdf;
use Picqer\Barcode\BarcodeGeneratorPNG;

class BlackHawkNetworkV2 extends \offgamers\publisher\models\profile\BlackHawkNetworkV2
{
    const create_order_url = 'generateEGift';
    const void_order_url = 'voidEGift';
    const reverse_order_url = 'reverseEGift';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->retry_attempt = 1;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getOrderUrl()
    {
        return self::create_order_url;
    }

    public function processOrder($status)
    {
        $this->publisher_id = Publisher::find()->where([
            'title' => 'BlackHawkNetworkV2 ' . $this->getRegion(),
            'profile' => 'BlackHawkNetworkV2',
            'status' => 1
        ])->scalar();

        $this->initClient(1, 'BHNV2_' . $this->orders_id);
        $this->getConfig();
        switch ($status) {
            case 0:
                if ($this->checkMargin()) {
                    $this->createOrder();
                }
                break;
            case 2:
                $this->reverseTransaction('OG_' . $this->api_restock_request_id);
                break;
        }
    }

    private function createOrder()
    {
        $data = [
            'giftAmount' => $this->getPublisherProductAmount(),
            'productConfigurationId' => $this->getPublisherProductId(),
            'purchaserInfo' => [
                'state' => $this->customers_state,
                'countryCode' => $this->phone_country_code
            ],
            'retrievalReferenceNumber' => $this->getRetrievalReferenceNumber()
        ];
        $request_id = 'OG_' . $this->api_restock_request_id;

        $headers = [
            'contractId' => $this->mid
        ];

        $response = $this->sendRequest('POST', $this->base_url . '/' . self::order_url_prefix . '/' . self::create_order_url, $request_id, $data, $headers);

        if ($data = $this->checkError($response, $request_id, true)) {
            $this->publisher_order_id = $data['transactionId'];
            $account_id = $data['accountId'];
            $entity_url = explode('/', $data['entityId']);
            $egift_id = end($entity_url);
            ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'EGIFT_ID', (string)$egift_id);
            ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'SKU', $this->sku);
            $this->getAccount($account_id);
        }
    }

    private function getAccount($url)
    {
        $request_id = 'OG_A_' . $this->getFakeRequestId();

        $response = $this->sendRequest('GET', $url, $request_id);

        if ($data = $this->checkError($response, $request_id)) {
            $this->parseCdKey($data);
        }
    }

    public function voidTransaction($api_restock_request_id)
    {
        $this->sku = ApiRestockRequestExtraInfo::findOne(['key' => 'SKU', 'api_restock_request_id' => $api_restock_request_id])->value;
        $this->publisher_id = Publisher::find()->where([
            'title' => 'BlackHawkNetworkV2 ' . $this->getRegion(),
            'profile' => 'BlackHawkNetworkV2',
            'status' => 1
        ])->scalar();
        $this->getConfig();
        $this->orders_id = $api_restock_request_id;
        $request_id = 'OG_V_' . $this->getFakeRequestId();

        $egift_id = ApiRestockRequestExtraInfo::findOne(['key' => 'EGIFT_ID', 'api_restock_request_id' => $api_restock_request_id]);

        if ($egift_id) {
            $data = [
                'eGiftId' => $egift_id->value
            ];

            $response = $this->sendRequest('POST', $this->base_url . '/' . self::order_url_prefix . '/' . self::void_order_url, $request_id, $data);

            if ($data = $this->checkError($response, $request_id, true)) {
                if ($data['isVoid']) {
                    return true;
                }
            }
        }

        return false;
    }

    protected function isAppleGiftCard()
    {
        if (in_array($this->getPublisherProductId(), $this->apple_product_configuration_id_list)) {
            return true;
        }
        return false;
    }

    public function parseCdKey($data)
    {
        $code = [];

        if ($this->isAppleGiftCard()) {
            if (!empty($data['accountNumber'])) {
                $pin = $data['accountNumber'];
                $this->code_string = 'Apple Redeem Code : ' . $pin;
            }
        } else {
            if (isset($data['activationAccountNumber'])) {
                $code[] = 'Serial : ' . $data['activationAccountNumber'];
            }

            if (isset($data['accountNumber'])) {
                $code[] = 'Pin : ' . $data['accountNumber'];
            }

            if (isset($data['securityCode'])) {
                $code[] = 'Security Code : ' . $data['securityCode'];
            }

            if (isset($data['barCodeValue'])) {
                $this->code_type = 'png';
                $generator = new BarcodeGeneratorPNG();
                $imgSource = '<img style="background-color:white;width:500px;" src="data:image/png;base64,' . base64_encode(
                        $generator->getBarcode(
                            $data['barCodeValue'],
                            $generator::TYPE_CODE_128
                        )
                    ) . '">';
                // instantiate and use the dompdf class
                $dompdf = new Dompdf();
                foreach ($code as &$raw) {
                    $raw = '<span>' . $raw . '</span>';
                }
                $dompdf->loadHtml(
                    '<html><body style="width:500px;font-size:30px;text-align:center"><br>' . $imgSource . '<br>' . $data['barCodeValue'] . '<br>' . implode(
                        '<br>',
                        $code
                    ) . '</body></html>'
                );

                $dompdf->render();

                $dom = $dompdf->output();

                $im = new \Imagick();
                $im->readImageBlob($dom);
                $im->setImageFormat('png');
                $im->trimImage(0.1 * \Imagick::getQuantum());
                $im->borderImage('', '10', '10');

                $this->code_string = $im;
            } else {
                $this->code_string = implode('<br>', $code);
            }
        }

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    private function getRetrievalReferenceNumber()
    {
        $next_outgoing_log_id = $this->getFakeRequestId();

        return str_pad(($next_outgoing_log_id + 1) % 1000000000000, 12, '0', STR_PAD_LEFT);
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductAmount()
    {
        return (float)explode("_", $this->sku)[3] / 100;
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'BlackHawkNetworkV2';
    }

    public function getRegion()
    {
        return explode("_", $this->sku)[1];
    }

    public function getAPIProvider()
    {
        $region = $this->getRegion();
        return 'BLACKHAWKV2_' . substr($region, 0, 2);
    }

    public function reset()
    {
        $this->defaultReset();
    }

}