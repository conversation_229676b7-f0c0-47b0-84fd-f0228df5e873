<?php

namespace micro\models\publishers;

use yii\helpers\Json;

class Boost extends \offgamers\publisher\models\profile\Boost
{
    private $error_code;
    const CREATE_ORDER_URL = "api/orderProduct";
    const QUERY_ORDER_URL = "api/orderStatus";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->error_code == "VAS0003") {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                if ($this->error_code == 'VAS0008') {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];
        //create order
        if ($this->checkMargin()) {
            try {
                $params = [
                    "spTransID" => $this->api_restock_request_id,
                    "publisherCode" => $this->configuration_data['PUBLISHER_CODE'],
                    "productCode" => $this->getPublisherProductCode(),
                    "onBehalfOf" => 'OffGamers',
                    "msisdn" => '60123456789',
                    "operator" => $this->configuration_data['OPERATOR'],
                    "contactInfo" => 'www.offgamers.com',
                    "tacVerification" => 'FALSE',
                    "chargingType" => 'N',
                    "smsDelivery" => 'FALSE',
                ];
                $response = $this->sendRequest('POST', static::CREATE_ORDER_URL, $params);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        //check order
        try {
            $params = [
                "spTransID" => $this->api_restock_request_id,
            ];
            $response = $this->sendRequest('POST', static::QUERY_ORDER_URL, $params);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function checkError($response)
    {
        try {
            $status = $response->getStatusCode();

            try {
                $data = Json::decode($response->getBody());
            } catch (\InvalidArgumentException $e) {
                $this->error_code = $status;
                $this->error_msg = "Invalid Publisher Response";
            }

            if ($status == 200) {
                return $data;
            } else {
                if (!empty($data)) {
                    if (isset($data['errorMessage']) && isset($data['errorCode'])) {
                        if ($data['errorCode'] == 'VAS0007') {
                            $this->status_flag = -1;
                        }
                        $this->error_code = $data['errorCode'];
                        $this->error_msg = $data['errorCode'] . ' - ' . $data['errorMessage'];
                    } else {
                        $this->error_code = $status;
                        $this->error_msg = "Invalid Publisher Response";
                    }
                } else {
                    $this->error_code = $status;
                    $this->error_msg = "No output for publisher response";
                }
            }
        } catch (\Exception $e) {
            $this->error_code = $response->getStatusCode();
            $this->error_msg = $response->getBody();
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        $checklist = ["productName" => "Name", "productPin" => "Pin", "serialNumber" => "Serial"];
        if (isset($data['vasTransID']) && isset($data['products']) && count($data['products']) >= 1) {
            $this->publisher_order_id = (string)$data['vasTransID'];
            $cdkey_data = array();

            foreach ($data['products'] as $product_key => $product_arr) {
                $output = array();
                foreach ($checklist as $checklist_key => $checklist_value) {
                    if (isset($product_arr[$checklist_key])) {
                        if (count($data['products']) > 1 && $checklist_value == "Name") {
                            $output[$checklist_value] = $product_arr[$checklist_key];
                        } else {
                            if ($checklist_value != "Name") {
                                $output[$checklist_value] = $product_arr[$checklist_key];
                            }
                        }
                    }
                }
                $cdkey_data[] = $output;
            }
            $code = $this->parseCdKey($cdkey_data);
            if (!empty($code)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                $this->status_flag = 2;
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        if (isset($pinInfo)) {
            foreach ($pinInfo as $key => $value) {
                $code_string = "";
                foreach ($value as $k => $v) {
                    if ($k == 'Name') {
                        $code_string .= (is_array($v) ? implode(",", $v) : $v) . '<br>';
                    } else {
                        $code_string .= $k . ' : ' . (is_array($v) ? implode(",", $v) : $v) . '<br>';
                    }
                }
                $code[] = $code_string;
            }
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function getDeno()
    {
        return 0;
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Boost';
    }

    public function getAPIProvider()
    {
        return 'BOOST';
    }

    public function getOrderUrl()
    {
        return static::CREATE_ORDER_URL;
    }

    public function getPublisherProductCode()
    {
        return str_replace("-", "_", explode("_", $this->sku)[1]);
    }
}