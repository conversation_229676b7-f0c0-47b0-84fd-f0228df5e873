<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Cashlib extends \yii\base\Model
{
    private $distributor_id;
    private $api_key;
    private $pos_id;
    private $base_url;
    private $error_code;
    private $test_env;

    static $order_url = 'api/distributor/voucher_creation';

    static $check_order_url = 'api/distributor/voucher_enquiry';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['cashlib.credential'])) {
            $config = $params['cashlib.credential'];
            $this->distributor_id = $config['distributor_id'];
            $this->api_key = $config['api_key'];
            $this->pos_id = $config['pos_id'];
            $this->base_url = $config['base_url'];
            $this->test_env = $config['test_env'];
        } else {
            throw new InvalidArgumentException('Missing CashLib credentials config');
        }
    }

    public function getOrderUrl()
    {
        return "voucher_creation";
    }

    public function processOrder($status)
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getConfig();
        switch ($status) {
            case 0:
                $this->createOrder();
                if ($this->error_code == 16) {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == 41) {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $params = [
            'request_id' => $this->api_restock_request_id,
            'request_date' => $this->getRequestDate(),
            'test' => ($this->test_env ? 'Y' : 'N'),
            'distributor_id' => $this->distributor_id,
            'pos_id' => $this->pos_id,
            'value' => $this->getPublisherProductId(),
            'currency' => $this->getPublisherCurrency(),
            'country_of_purchase' => 'FRA',
            'crosscountry' => 'Y',
            'brand_id' => 0
        ];

        if ($this->checkMargin()) {
            try {
                $response = $this->sendRequest($params, static::$order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        $params = [
            'request_id' => microtime(true) * 1000,
            'request_date' => $this->getRequestDate(),
            'test' => ($this->test_env ? 'Y' : 'N'),
            'request_id_enquiry' => $this->api_restock_request_id,
            'distributor_id' => $this->distributor_id,
            'pos_id' => $this->pos_id
        ];

        try {
            $response = $this->sendRequest($params, static::$check_order_url);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }

    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200 && isset($data['status']) && $data['status'] == 0) {
                return $data;
            } else {
                if (!empty($data['status'])) {
                    $this->error_code = $data['status'];
                    $this->error_msg = $data['status'] . ' - ' . ($data['error_message'] ?? '');
                }

                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];

            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $exception_array = [16, 41];

        if (!empty($this->error_code) && !in_array($this->error_code, $exception_array)) {
            $this->createException('CREATE_ORDER');
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        if ($data['status'] == 0) {
            $this->publisher_order_id = (string)$data['mv_tid'];

            $code = $this->parseCdKey($data);
            if (!empty($code)) {
                $this->status_flag = 1;
                $this->code_string = $code;
            }

        }
    }

    private function parseCdKey($data)
    {
        $voucher_param = ['serial_number', 'code', 'expiry_date'];

        $code = [];
        foreach ($voucher_param as $value) {
            if (!empty($data[$value])) {
                $code[] = $value . ' : ' . $data[$value];
            }
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params, $url)
    {
        $this->getConfig();

        $options = array(
            'headers' => ['apikey' => $this->api_key],
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getRequestDate()
    {
        $date = new \DateTime("now", new \DateTimeZone('Europe/Paris'));
        return $date->format('Y-m-d H:i:s');
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[1];
    }

    private function getPublisherCurrency(){
        return explode("_", $this->sku)[2];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'CASHLIB';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }


}

