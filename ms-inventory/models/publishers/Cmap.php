<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Cmap extends \yii\base\Model
{
    private $baseUrl, $saleChannelCode;
    private $error_code;

    const create_order_code = 501;
    const cancel_order_code = 515;

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['cmap.credential'])) {
            $config = $params['cmap.credential'];
            $this->baseUrl = $config['base_url'];
            $this->saleChannelCode = $config['sale_channel_code'];
        } else {
            throw new InvalidArgumentException('Missing CMAP credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'CmapVcode';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        switch ($status) {
            case 0:
            case 2:
            case 3:
                $this->createOrder();
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $productsId = $this->getPublisherProductId();
                $productsCountry = $this->getPublisherProductCountry();

                $params = [
                    'api' => static::create_order_code,
                    'data' => [
                        'saleChannelCode' => $this->saleChannelCode,
                        'spcLinkCode' => $productsId,
                        'orderNo' => (string) $this->api_restock_request_id,
                        'country' => $productsCountry
                    ]
                ];

                $response = $this->sendRequest($params);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data['data']);
                }
            } catch (\Exception $e) {
                $this->cancelOrder();
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function cancelOrder()
    {
        try {
            $productsCountry = $this->getPublisherProductCountry();

            $params = [
                'api' => static::cancel_order_code,
                'data' => [
                    'orderNo' => (string) $this->api_restock_request_id,
                    'saleChannelCode' => $this->saleChannelCode,
                    'country' => $productsCountry
                ]
            ];

            $response = $this->sendRequest($params);
            $data = $this->checkError($response);
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }

        return ($data['status'] == 'ok') ? true : false;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == 'ok') {
                    return $data;
                } elseif (isset($data['errorCode']) && $data['status'] == 'fail') {
                    if ($data['errorCode'] == (-202)) {
                        $this->cancelOrder();
                    }

                    // -202 Duplicated Purchase number OR -214 Discarded coupon
                    if ($data['errorCode'] == (-202) || $data['errorCode'] == (-214)) {
                        $this->status_flag = -1;
                    }
                    
                    $this->error_code = $data['errorCode'];
                    $this->error_msg = '(' . $data['errorCode'] . ') ' . $data['reason']. "\n" . 'OrderNo : ' . (string) $this->api_restock_request_id;
                    $this->error = ['message' => $this->error_msg];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => ($data['errorCode'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        if (!empty($this->error_code)) {
            $this->createException();
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        if (!empty($data['cpnNo'])) {
            $this->publisher_order_id = (string) $this->api_restock_request_id;
            $this->settlement_amount = $this->product_cost;
            $this->settlement_currency = $this->product_cost_currency;

            if (!empty($data['cpnNo']) && !empty($data['url'])) {
                $date = \DateTime::createFromFormat('ymd', $data['virtualVO'][0]['validTermEndDate']);
                $newDateFormat = $date->format('Y-m-d');
                
                $this->status_flag = 1;
                $code = $this->parseCdKey([
                    'Voucher Link' => $data['url'],
                    'Voucher Code' => $data['cpnNo'],
                    'Expiry Date' => $newDateFormat
                ]);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            } else {
                $this->createException('CDKEY', null);
            }
        }
    }

    private function parseCdKey($voucherInfo)
    {
        $code = [];
        foreach ($voucherInfo as $key => $value) {
            $code[] = $key . ' : ' . (is_array($value) ? implode(",", $value) : $value);
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params)
    {
        $this->getConfig();

        $options = array(
            'form_params' => [
                'jsonBody' => Json::encode($params)
            ],
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->baseUrl, $options);
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductCountry()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'CMAPAPI';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }
}
