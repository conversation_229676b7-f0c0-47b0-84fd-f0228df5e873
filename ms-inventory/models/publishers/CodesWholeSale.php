<?php

namespace micro\models\publishers;

use Yii;
use yii\helpers\Json;
use yii\web\Response;

class CodesWholeSale extends \offgamers\publisher\models\profile\CodesWholeSale
{
    use RestockPublisherTrait {
        rules as restockRule;
        reset as defaultReset;
    }

    static $order_url = '/v2/orders';
    static $code_url = '/v2/codes';

    protected $code_id;

    protected $allow_preorder = false;

    public function __construct()
    {
        $this->settlement_currency = 'EUR';
    }

    public function rules()
    {
        return array_merge([
            [['publisher'], 'integer']
        ], $this->restockRule());
    }

    public function getOrderUrl()
    {
        return static::$order_url;
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                return $this->createOrder();
                break;
            case 2:
                if (!empty($this->publisher_order_id)) {
                    return $this->checkOrder();
                } else {
                    return $this->createOrder();
                }
                break;
            case 3:
                return $this->checkOrder();
                break;
        }
    }

    protected function getPublisherSKU()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        // Products Deno Not Available for CodesWholeSale
        return 0;
    }

    public function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                return $data;
            } else {
                if ($status == 401) {
                    $this->clearToken();
                    if (!empty($data['error_description'])) {
                        $this->error_msg = $data['error_description'];
                    } else {
                        $this->error_msg = '401 Unauthorized';
                    }
                } elseif ($status == 404) {
                    if (!empty($data['developerMessage']) || !empty($data['error_description'])) {
                        $this->error_msg = (!empty($data['developerMessage']) ? $data['developerMessage'] : $data['error_description']);
                    } else {
                        $this->error_msg = '404 Not Found';
                    }
                } elseif ($status == 409) {
                    $this->status_flag = -1;
                    $this->publisher_order_id = ($data['orderId'] ?? '');
                    $this->error_msg = 'Order Rejected';
                } else {
                    if (!empty($data['developerMessage']) || !empty($data['error_description'])) {
                        $this->error_msg = (!empty($data['developerMessage']) ? $data['developerMessage'] : $data['error_description']);
                    } else {
                        $this->error_msg = (Response::$httpStatuses[$status] ?? 'Unhandled Exception');
                    }
                }

                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }
        return false;
    }

    public function checkSKU()
    {
        try {
            $data = $this->getProductByPublisherId($this->getPublisherSKU());
            if ($data) {
                if ($data['releaseDate'] && $this->isPreOrder($data['releaseDate'])) {
                    $this->allow_preorder = true;
                }
                $this->product_cost = $this->findCostPrice($data['prices']);
            }
        } catch (\Exception $e) {
            $this->createException($e);
        }
        return true;
    }

    public function createOrder()
    {
        if ($this->checkSKU()) {
            if ($this->checkMargin()) {
                $this->getToken();
                $url = $this->configuration_data['API_ENDPOINT'] . static::$order_url;

                $options = array(
                    'http_errors' => false,
                    'headers' => ['Authorization' => 'bearer ' . $this->token],
                    'json' => [
                        'allowPreorder' => $this->allow_preorder,
                        'orderId' => $this->api_restock_request_id,
                        'products' => [
                            [
                                'productId' => $this->getPublisherSKU(),
                                'quantity' => 1,
                                'price' => $this->product_cost
                            ]
                        ]
                    ]
                );

                if (!empty(Yii::$app->params['proxy'])) {
                    $options['proxy'] = Yii::$app->params['proxy'];
                }

                try {
                    $response = $this->request('POST', $url, $options, 3);
                    $data = $this->checkError($response);

                    if ($data == false) {
                        $this->createException('CREATE_ORDER');
                    }
                } catch (\Exception $e) {
                    $this->createException('CREATE_ORDER', $e);
                }
                $this->parseOrderResponse($data);
            }
        }

        return true;
    }

    public function checkOrder()
    {
        $this->getToken();

        $url = $this->configuration_data['API_ENDPOINT'] . static::$order_url . '/' . $this->publisher_order_id;

        $options = array(
            'http_errors' => false,
            'headers' => ['Authorization' => 'bearer ' . $this->token],
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        try {
            $response = $this->request('GET', $url, $options);
            $data = $this->checkError($response);

            if ($data == false) {
                $this->createException('CHECK_ORDER');
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }

        $this->parseOrderResponse($data);
    }

    protected function parseOrderResponse($data)
    {
        if (!empty($data['orderId'])) {
            $this->publisher_order_id = $data['orderId'];
            $this->settlement_amount = $data['totalPrice'];
            $this->status_flag = 1;
        }
        if ($data['status'] == 'Completed') {
            if (!empty($data['products'][0]['codes'][0])) {
                $code = $data['products'][0]['codes'][0];
                if ($code['codeType'] == 'CODE_TEXT') {
                    $this->code_string = $code['code'];
                } elseif ($code['codeType'] == 'CODE_IMAGE') {
                    $this->code_id = $code['codeId'];
                    $image = $this->getImageCode();
                    if (!empty($image)) {
                        $file_name = explode(".", $code['filename']);
                        $this->code_type = end($file_name);
                        $this->code_string = $image;
                    }
                }
            }
        }
    }

    protected function getImageCode()
    {
        $this->getToken();

        $url = $this->configuration_data['API_ENDPOINT'] . static::$code_url . '/' . $this->code_id;

        $options = array(
            'http_errors' => false,
            'headers' => ['Authorization' => 'bearer ' . $this->token],
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        $response = $this->request('GET', $url, $options);

        $data = $this->checkError($response);

        if ($data == false) {
            $this->createException(' ');
        }

        return base64_decode($data['code']);
    }

    public function reset()
    {
        $this->allow_preorder = false;
        $this->code_id = null;
        $this->code_type = 'soft';
        $this->defaultReset();
    }
}