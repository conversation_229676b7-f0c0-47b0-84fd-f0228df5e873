<?php

namespace micro\models\publishers;

use micro\models\ApiLog;
use micro\models\DirectTopUpModel;
use Psr\Http\Message\ResponseInterface;
use Yii;
use yii\helpers\Json;

class DTOne extends \offgamers\publisher\models\profile\DTOne
{
    private $error_code;

    use DirectTopUpPublisherTrait;

    public function getAPIProvider()
    {
        return 'DTOne';
    }

    public function reserveOrderId()
    {
        $this->initClient();
        $request_data = [
            'action' => 'reserve_id'
        ];
        $data = $this->sendRequest($request_data);
        $response_data = simplexml_load_string((string)$data->getBody());

        if (!empty($response_data->reserved_id)) {
            return (integer)$response_data->reserved_id;
        }
        return false;
    }

    public function processTopUp()
    {
        $return_array = [];

        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;

        try {
            $this->getExistingConfig();
            if ($this->checkMargin()) {
                if ($this->sub_orders_top_up_status == 0 || ($this->sub_orders_top_up_status == 2 && empty($this->publisher_order_id))) {
                    $return_array['status'] = $this->createOrder();
                } elseif (($this->sub_orders_top_up_status == 1 && !empty($this->publisher_order_id)) || $this->sub_orders_top_up_status == 2) {
                    $return_array['status'] = $this->checkOrder();
                } else {
                    $this->error_msg = 'Prevent Duplicate Submission, check transaction history and update status / order id';
                }

                if ($return_array['status'] == true) {
                    $return_array['error_code'] = '0';
                } else {
                    $return_array['error_code'] = ($this->error_code != 999 ? '1511' : '9999');
                    if ($this->error_code == '100214' || $this->error_code == '1000214' || $this->error_code == '1000990') {
                        $return_array['new_request_id'] = true;
                    }
                }

                $return_array['publisher_ref_id'] = $this->publisher_order_id;
                $return_array['error_msg'] = $this->error_msg;
            } else {
                //Margin Block
                $return_array['error_code'] = '1510';
            }

        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }


    public function checkOrder()
    {
        $this->initClient(1, 'dtone/check_order/' . $this->orders_id);

        switch ($this->game_account_info['type']) {
            case 1:
            case 3:
                $request_data = [
                    'action' => 'trans_info',
                    'transactionid' => $this->publisher_order_id
                ];

                return $this->processOrder($request_data);
                break;

            case 2:
                return $this->processGNSOrder('GET', 'transactions/fixed_value_recharges/ext-' . $this->sub_orders_top_up_id, []);
                break;
        }

    }

    public function createOrder()
    {
        list($prefix, $phone) = explode(" ", $this->game_account_info['account']);
        $phone_no = '+' . $prefix . $phone;
        switch ($this->game_account_info['type']) {
            case 1:
            case 3:
                $this->publisher_order_id = $this->reserveOrderId();
                $this->initClient(1, 'dtone/topup/' . $this->orders_id);
                $request_data = [
                    'action' => (!empty($this->configuration_data['TOP_UP_METHOD']) ? $this->configuration_data['TOP_UP_METHOD'] : 'simulation'),
                    'destination_msisdn' => $phone_no,
                    'msisdn' => $phone_no,
                    'product' => $this->game_account_info['deno_id'],
                    'operatorid' => $this->game_account_info['operator_id'],
                    'reserved_id' => $this->publisher_order_id
                ];

                return $this->processOrder($request_data);
                break;

            case 2:
                $this->initClient(1, 'dtone/topup/' . $this->orders_id);
                $request_data = [
                    "account_number" => $phone_no,
                    "product_id" => $this->game_account_info['deno_id'],
                    "external_id" => $this->sub_orders_top_up_id,
                    "simulation" => (!empty($this->configuration_data['TOP_UP_METHOD']) && $this->configuration_data['TOP_UP_METHOD'] != 'simulation' ? 0 : 1)
                ];

                return $this->processGNSOrder('POST', 'transactions/fixed_value_recharges', $request_data);
                break;
        }

    }

    private function processOrder($request_data)
    {
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $this->configuration_data['API_ENDPOINT'], $request_data, 'fixed_value_r');

        $data = $this->sendRequest($request_data);

        $response_data = simplexml_load_string((string)$data->getBody());

        $api_log->endLog('2000', $response_data);

        $this->error_code = (integer)$response_data->error_code;

        if (!empty($response_data->transactionid) && empty($response_data->error_code)) {
            $this->publisher_order_id = (string)$response_data->transactionid;
            return true;
        } else {
            $data_array = [
                (string)($response_data->error_code ?? '') . ' - ' . (string)($response_data->error_txt ?? ''),
                'msidn : ' . $request_data['msisdn'],
                'Denomination : ' . $request_data['product'],
                'Operator ID : ' . $request_data['operatorid'],
                'Transaction ID : ' . (string)($response_data->transactionid ?? ($this->publisher_order_id ?? '')),
            ];

            $this->error_msg = implode("\n", $data_array);
        }

        return false;
    }

    private function processGNSOrder($method, $path, $request_data)
    {
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $this->configuration_data['GNS_API_ENDPOINT'], $request_data, $path);

        /**
         * @var $data ResponseInterface
         */
        $data = $this->sendGNSRequest($method, $path, $request_data, ['http_errors' => false]);

        $response_data = Json::decode((string)$data->getBody());

        $api_log->endLog('2000', $response_data);

        $status_code = $data->getStatusCode();

        if ($status_code >= 200 && $status_code < 300 && !empty($response_data['transaction_id']) && isset($response_data['status']) && $response_data['status'] == 0) {
            $this->publisher_order_id = $response_data['transaction_id'];
            return true;
        } else {
            $data_array = [
                'msidn : ' . $request_data['account_number'],
                'Denomination : ' . $request_data['product_id'],
                'OG Transaction ID : ' . $request_data['external_id']
            ];

            if (!empty($response_data['transaction_id'])) {
                $data_array[] = 'Transaction ID : ' . (string)$response_data['transaction_id'];
                if (!empty($response_data['status'])) {
                    $data_array[] = (string)$response_data['status'] . ' : ' . (!empty($response_data['status_message']) ? (string)$response_data['status_message'] : '');
                    $this->error_code = $response_data['status'];
                }
            }

            if (!empty($response_data['errors'])) {
                foreach ($response_data['errors'] as $error) {
                    $this->error_code = ($error['code'] ?? null);
                    $data_array[] = ($error['code'] ?? '') . ' - ' . ($error['message'] ?? '');
                }
            }

            $this->error_msg = implode("\n", $data_array);
        }

        return false;
    }
}