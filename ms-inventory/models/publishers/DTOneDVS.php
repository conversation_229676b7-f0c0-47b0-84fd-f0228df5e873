<?php

namespace micro\models\publishers;

use micro\models\ApiLog;
use yii\helpers\Json;

class DTOneDVS extends \offgamers\publisher\models\profile\DTOneDVS
{
    private $error_code, $top_up_status, $is_new_order;

    use DirectTopUpPublisherTrait;

    public function getAPIProvider()
    {
        return 'DTOneDVS';
    }

    public function processTopUp()
    {
        $return_array = [];

        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;

        try {
            $this->getExistingConfig();

            if ($this->sub_orders_top_up_status != 0) {
                $this->checkOrder();
            }

            if ($this->sub_orders_top_up_status == 0 || $this->is_new_order) {
                if ($this->getCost() && $this->checkMargin()) {
                    $this->createOrder();
                } else {
                    //Margin Block
                    $this->error_code = '1510';
                }
            }

            $return_array['status'] = ($this->top_up_status == 'reloaded');
            $return_array['error_code'] = $this->error_code;
            $return_array['top_up_status'] = $this->top_up_status;
            $return_array['publisher_ref_id'] = (string) $this->publisher_order_id;
            $return_array['error_msg'] = $this->error_msg;

        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }

    public function getCost()
    {
        $response = $this->sendRequest('GET', 'products/' . $this->game_account_info['deno_id']);
        if ($data = $this->validateResponse($response)) {
            if (!empty($data['prices']['wholesale']['unit']) && !empty($data['prices']['wholesale']['amount'])) {
                $this->product_cost = $data['prices']['wholesale']['amount'];
                $this->settlement_currency = strtoupper($data['prices']['wholesale']['unit']);
                return true;
            }
        }
        return false;
    }

    public function checkOrder()
    {
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $this->configuration_data['API_ENDPOINT'], ['external_id' => $this->sub_orders_top_up_id], 'transactions');

        $response = $this->sendRequest('GET', 'transactions?external_id=' . $this->sub_orders_top_up_id);

        if ($data = $this->validateResponse($response)) {
            $api_log->endLog('2000', $data);
            if (count($data) === 1 && $data[0]['external_id'] == $this->sub_orders_top_up_id) {
                $this->processOrderResponse($data[0]);
            }
        }

        $error_code = explode(' : ', $this->error);

        if (isset($error_code[0]) && $error_code[0] == 1008004) {
            $this->is_new_order = true;
        }
    }

    public function processOrderResponse($data)
    {
        $status = 'failed';
        $code = '1001';

        if (isset($data['id']) && isset($data['status']['message'])) {
            $this->publisher_order_id = $data['id'];
            switch ($data['status']['message']) {
                case "COMPLETED" :
                    $status = 'reloaded';
                    $code = '0';
                    break;

                case "CONFIRMED":
                    $status = 'pending';
                    $code = '1200';
                    break;

                default:
                    $this->error = $data['status']['id'] . ':' . $data['status']['message'];
                    break;
            }
        }

        $this->error_msg = $this->error;
        $this->top_up_status = $status;
        $this->error_code = $code;
    }

    public function createOrder()
    {
        list($prefix, $phone) = explode(" ", $this->game_account_info['account']);
        $phone_no = '+' . $prefix . $phone;
        $request_data = [
            'external_id' => (string)$this->sub_orders_top_up_id,
            'product_id' => $this->game_account_info['deno_id'],
            'auto_confirm' => true,
            'credit_party_identifier' => [
                'mobile_number' => $phone_no
            ],
            'callback_url' => $this->configuration_data['POSTBACK_ENDPOINT'] . $this->orders_id
        ];
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $this->configuration_data['API_ENDPOINT'], $request_data, 'transactions');

        $response = $this->sendRequest('POST', 'async/transactions', $request_data);

        if ($data = $this->validateResponse($response)) {
            $api_log->endLog('2000', $data);
            $this->processOrderResponse($data);
        }

        $this->error_msg = $this->error;
        return false;
    }

    public function processPostBack($data)
    {
        return ($data['external_id'] ?? false);
    }

}