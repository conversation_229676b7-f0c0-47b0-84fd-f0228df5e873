<?php

namespace micro\models\publishers;

class DTOneGC extends \offgamers\publisher\models\profile\DTOneGC
{
    protected $product_id;

    protected $currency_code;

    protected $denomination;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: DTONEGC_MY1_88_FIXED
        //Example: DTONEGC_MY1_39257_AED_6000
        if (count($sku) == 4 && $sku[3] === 'FIXED') { //Fixed value mode
            $this->account = $sku[1];
            $this->product_id = $sku[2];
            $this->currency_code = $sku[3];
        } else if (count($sku) == 5) { //Range value mode
            $this->account = $sku[1];
            $this->product_id = $sku[2];
            $this->currency_code = $sku[3];
            $this->denomination = (float)($sku[4] / 100);
        } else {
            throw new \Exception('Invalid DTOneGC SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return '/transactions';
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder(); //Do not attempt retry here
                if ($this->error_code == '1008004') {
                    //Only re-create order if original order not found.
                    //Other code means the order is processing by DTOne
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $endpoint = static::create_order_url;
            $payload = [
                'external_id' => 'OG_' . $this->api_restock_request_id,
                'product_id' => $this->product_id,
                'auto_confirm' => true,
            ];
            if ($this->currency_code !== 'FIXED') {
                $payload['calculation_mode'] = static::CALCULATION_MODE_DESTINATION_AMOUNT;
                $payload['destination'] = [
                    'unit_type' => 'CURRENCY',
                    'unit' => $this->currency_code,
                    'amount' => $this->denomination,
                ];
            }
            $response = $this->sendRequest('POST', $endpoint, $payload);
            if (($data = $this->checkError($response)) && $data['status']['message'] == 'CONFIRMED' && !empty($data['id'])) {
                $this->publisher_order_id = '' . $data['id'];
                usleep($this->check_order_initial_delay * 1000000); //Wait x seconds for a while for the order to actually create
                $this->checkOrder($this->check_order_max_retry_attempts);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder($retry_left = 0) {
        $endpoint = static::check_order_url;
        $payload = [
            'external_id' => 'OG_' . $this->api_restock_request_id,
        ];
        $response = $this->sendRequest('GET', $endpoint, $payload);
        if ($data = $this->checkError($response)) {
            $this->parseCdKey($data, true);
        }
        if ($this->status_flag != 1 && $retry_left > 0) {
            usleep($this->check_order_subsequent_delay * 1000000); //Wait another x seconds before retrying
            $this->checkOrder($retry_left - 1);
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data, $is_array = false)
    {
        if ($is_array && count($data) == 1) {
            $data = $data[0];
        }
        if (!isset($data['status']['message']) || $data['status']['message'] !== 'COMPLETED') {
            return;
        }
        
        $this->publisher_order_id = '' . $data['id'];

        $code = [];

        /*
        From DTOne DVS document:

        From the received callback parse the pin.code, pin.serial, product.pin.terms, product.pin.usage_info & product.pin.validity
        Depending on provider pin.code or pin.serial or both may be required for redemption

        For some Gift Cards products PIN URL is received instead of PIN code. Please contact our Solutions Delivery Team for instructions on providers that return PIN URL
        */

        if (!empty($data['pin']['code'])) {
            $code[] = 'Code : ' . $data['pin']['code'];
        }

        if (!empty($data['pin']['serial'])) {
            $code[] = 'Serial : ' . $data['pin']['serial'];
        }

        if (!empty($data['product']['pin']['usage_info'])) {
            $code[] = 'Instructions : ' . implode("<br>", $data['product']['pin']['usage_info']);
        }

        if (!empty($data['product']['pin']['validity']['quantity']) && !empty($data['product']['pin']['validity']['unit'])) {
            $code[] = 'Validity : ' . $data['product']['pin']['validity']['quantity'] . ' ' . $data['product']['pin']['validity']['unit'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'DTOneGC';
    }

    public function getAPIProvider()
    {
        return 'DTONEGC_' . $this->getAccount();
    }
}
