<?php

namespace micro\models\publishers;

use yii\helpers\Json;

class Devco extends \offgamers\publisher\models\profile\Devco
{
    const API_GET_ORDER_FUNCTION = "get_Vouchers";
    const API_CHECK_ORDER_FUNCTION = "get_SalesTransaction_ByReferenceId";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                if ($this->error_code == "01886") {
                    $this->checkOrder();
                }
                break;
            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == "01880") {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];
        //create order
        if ($this->checkMargin()) {
            try {
                $params = [
                    'PRODUCT_ID' => $this->getPublisherProductCode(),
                    'QUANTITY' => '1',
                    'REFERENCE_ID' => $this->api_restock_request_id
                ];
                $response = $this->sendRequest('POST', static::API_GET_ORDER_FUNCTION, $params, $this->getAPIProvider() . '_' . $this->orders_id);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        //check order
        try {
            $params = [
                'REFERENCE_ID' => $this->api_restock_request_id
            ];
            $response = $this->sendRequest('POST', static::API_CHECK_ORDER_FUNCTION, $params, $this->getAPIProvider() . '_' . $this->orders_id);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CREATE_ORDER', $e);
        }
        return true;
    }

    private function parseOrderResponse($data)
    {
        $checklist = [
            "OEM_VOUCHER_PIN" => "Pin",
            "OEM_VOUCHER_USERNAME" => "Username",
            "OEM_VOUCHER_PASSWORD" => "Password",
            "OEM_VOUCHER_CARD_NUMBER" => "Card Number",
            "OEM_VOUCHER_SERIAL" => "Serial Number",
            "OEM_VOUCHER_EXPIRATION_DATE" => "Expiration Date",
        ];

        if (isset($data['TRANSACTION_ID']) && isset($data['VOUCHERS'])) {
            $this->publisher_order_id = (string)$data['TRANSACTION_ID'];
            $cdkey_data = array();

            foreach ($checklist as $checklist_key => $checklist_value) {
                if (isset($data['VOUCHERS'][0][$checklist_key])) {
                    $cdkey_data[$checklist_value] = $data['VOUCHERS'][0][$checklist_key];
                }
            }

            $code = $this->parseCdKey($cdkey_data);

            if (!empty($code)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                //slack error
                $this->status_flag = 2;
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function getDeno()
    {
        return 0;
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Devco';
    }

    public function getAPIProvider()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);
        return 'DEVCO_' . $region;
    }

    public function getOrderUrl()
    {
        return static::API_GET_ORDER_FUNCTION;
    }

    public function getPublisherProductCode()
    {
        return explode("_", $this->sku)[2];
    }
}