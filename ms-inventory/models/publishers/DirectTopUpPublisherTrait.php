<?php

namespace micro\models\publishers;

use micro\exceptions\InvalidMarginException;
use offgamers\base\models\DevDebugLog;
use Yii;
use offgamers\publisher\models\Publisher;
use http\Exception\InvalidArgumentException;

trait DirectTopUpPublisherTrait
{
    public $publisher_id;
    public $orders_id;
    public $orders_currency;
    public $orders_products_id;
    public $orders_products_price;
    public $orders_products_currency_price;
    public $products_id;
    public $sub_products_id;
    public $products_name;
    public $product_cost;
    public $product_cost_currency;
    public $publisher_order_id;
    public $quantity;
    public $orders_top_up_id;
    public $sub_orders_top_up_id;
    public $sub_orders_top_up_status;
    public $publisher_name = '';
    public $game_account_info = [];
    public $publisher_product_id = '';

    public $settlement_amount;
    public $settlement_currency = 'USD';
    public $status_flag = 0;
    public $error_msg;
    public $error;

    protected $min_margin;
    protected $low_margin;

    protected $retry_attempt = 3;

    static $min_margin_params = 'restock_min_margin';
    static $low_margin_params = 'restock_low_margin';

    use \offgamers\base\traits\GuzzleTrait;

    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'quantity', 'products_id', 'sub_products_id', 'publisher_id', 'orders_top_up_id', 'sub_orders_top_up_id', 'sub_orders_top_up_status', 'publisher_order_id','publisher_product_id'], 'integer'],
            [['products_name', 'product_cost_currency', 'publisher_name', 'orders_currency'], 'string'],
            [['orders_products_currency_price', 'orders_products_price', 'product_cost'], 'number'],
            [['game_account_info'], 'safe'],
        ];
    }

    public function getPublisherConfig()
    {
        $publisher = Publisher::find()->select(['title', 'profile'])->where(['publishers_id' => $this->publisher_id])->one();
        if ($publisher) {
            $publisher->getPublisherSettings();
        }
    }


    protected function getCurrencyObj()
    {
        return Yii::$app->currency;
    }

    protected function getMarginPercentage()
    {
        if (isset($this->configuration_data['LOW_MARGIN']) && isset($this->configuration_data['MIN_MARGIN'])) {
            $this->min_margin = $this->configuration_data['MIN_MARGIN'];
            $this->low_margin = $this->configuration_data['LOW_MARGIN'];
        } elseif (!empty(Yii::$app->params[static::$min_margin_params]) && !empty(Yii::$app->params[static::$low_margin_params])) {
            $this->min_margin = Yii::$app->params[static::$min_margin_params];
            $this->low_margin = Yii::$app->params[static::$low_margin_params];
        } else {
            throw new InvalidArgumentException('Low / Minimum Margin is not set.');
        }
    }

    protected function checkMargin()
    {
        $this->getMarginPercentage();
        if (!empty($this->product_cost) && !empty($this->settlement_currency)) {
            if ($this->settlement_currency == $this->orders_currency) {
                $margin = (($this->orders_products_currency_price - $this->product_cost) / $this->orders_products_currency_price) * 100;
            } else {
                $cost_mst = $this->getCurrencyObj()->advanceCurrencyConversion($this->product_cost, $this->settlement_currency, 'USD', false, 'spot');
                $margin = (($this->orders_products_price - $cost_mst) / $this->orders_products_price) * 100;
            }

            $margin = number_format($margin, 2);
            if ($margin > $this->min_margin) {
                if ($margin <= $this->low_margin) {
                    $this->lowMarginReport($margin, 'LOW_MARGIN');
                }
                return true;
            } else {
                $this->lowMarginReport($margin, 'MIN_MARGIN');
            }
        } else {
            $this->error_msg = 'Missing Products Cost / Cost Currency';
        }
    }

    public function getSettleAmount()
    {
        if (!empty($this->settlement_currency) && !empty($this->settlement_amount)) {
            $currency_code = $this->settlement_currency;
            $currency_settle_amount = $this->settlement_amount;
        } else {
            $currency_code = $this->product_cost_currency;
            $currency_settle_amount = $this->product_cost;
        }

        $currency_rate = $this->getCurrencyObj()->advanceCurrencyConversionRate($currency_code, 'USD', 'spot');
        $settle_amount = $this->getCurrencyObj()->advanceCurrencyConversion($currency_settle_amount, 'USD');

        return [
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'currency_settle_amount' => $currency_settle_amount,
            'settle_amount' => $settle_amount,
        ];
    }

    public function lowMarginReport($margin, $type)
    {
        $offgamersoldcrew = (!empty(Yii::$app->params['old.crew.domain']) ? Yii::$app->params['old.crew.domain'] : 'https://crew.offgamers.com');

        if ($this->orders_currency == $this->settlement_currency) {
            $cost_str = $this->getCurrencyObj()->format($this->settlement_currency, $this->product_cost, " ");
            $price_str = $this->getCurrencyObj()->format($this->orders_currency, $this->orders_products_currency_price, " ");
        } else {
            if (strtoupper($this->orders_currency) !== 'USD') {
                $price_str = $this->getCurrencyObj()->format($this->orders_currency, $this->orders_products_currency_price, " ") . '(~' . $this->getCurrencyObj()->format('USD', $this->orders_products_price, " ") . ')';
            } else {
                $price_str = $this->getCurrencyObj()->format('USD', $this->orders_products_price, " ");
            }

            if (strtoupper($this->settlement_currency) !== 'USD') {
                $cost_mst = $this->getCurrencyObj()->advanceCurrencyConversion($this->product_cost, $this->settlement_currency, 'USD', false, 'spot');
                $cost_str = $this->getCurrencyObj()->format($this->settlement_currency, $this->product_cost, " ") . '(~' . $this->getCurrencyObj()->format('USD', $cost_mst, " ") . ')';
            } else {
                $cost_str = $this->getCurrencyObj()->format('USD', $this->product_cost, " ");
            }
        }
        $cost_str = html_entity_decode($cost_str);
        $price_str = html_entity_decode($price_str);
        $orders_id = $this->orders_id;
        switch ($type) {
            case 'LOW_MARGIN':
                $title = '*Low Margin Order #' . $orders_id . ' delivery on ' . $this->getAPIProvider() . ' DTU API*';
                $attachments = [
                    [
                        'color' => 'warning',
                        'text' => "Product ID : <" . $offgamersoldcrew . "/categories.php?pID=" . $this->products_id . "&action=new_product|" . $this->products_id . "> \n Product Name : " . $this->products_name . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . $this->low_margin . "%) \n `Action` : Revise Selling & Cost Setting",
                    ],
                ];
                break;
            case 'MIN_MARGIN':
                $title = '*Order #' . $orders_id . ' blocked from delivery on ' . $this->getAPIProvider() . ' DTU API*';
                $attachments = [
                    [
                        'color' => 'danger',
                        'text' => "Product ID : <" . $offgamersoldcrew . "/categories.php?pID=" . $this->products_id . "&action=new_product|" . $this->products_id . "> \n Product Name : " . $this->products_name . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . $this->min_margin . "%) \n `Action` : Revise Selling & Cost Setting then process the order <" . $offgamersoldcrew . "/orders.php?oID=" . $orders_id . "&action=edit|#" . $orders_id . ">",
                    ],
                ];
                break;
        }
        Yii::$app->slack->send($title, $attachments, 'BDT_DTU');
    }

    public function reportError(\Exception $e)
    {
        if ($e instanceof InvalidMarginException) {
            // Do Nothing, Notified at upper layer
        } else {
            $offgamersoldcrew = (!empty(Yii::$app->params['old.crew.domain']) ? Yii::$app->params['old.crew.domain'] : 'https://crew.offgamers.com');

            if (empty($this->error_msg)) {
                $this->error_msg = 'Unhandled Exception' . $e->getMessage();
            }

            Yii::$app->slack->send('*Failed Delivery #' . $this->orders_id . ' delivery on ' . str_replace('PIMA', '', $this->getAPIProvider()) . ' DTU API*', [
                [
                    'color' => 'warning',
                    'text' => "Orders ID : <" . $offgamersoldcrew . "/orders.php?oID=" . $this->orders_id . "&action=edit|" . $this->orders_id . ">\n Product Name : " . $this->products_name . " \n Error : " . $this->error_msg . " \n `Action` : Reattempt delivery or contact publishers with error message",
                ],
            ], 'BDT_DTU');

            if (!empty($this->error)) {
                $error = ['error_msg' => $this->error];
            }

            $error['exception'] = $this->getExceptionError($e);

            DevDebugLog::generateDebugLog('Failed Delivery #' . $this->orders_id . ' delivery on ' . $this->getAPIProvider() . ' DTU API', $error);
        }

        return true;
    }

    protected function getExceptionError(\Exception $e)
    {
        $return_array = [
            [
                'msg' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ],
        ];

        if ($e->getPrevious()) {
            $return_array = array_merge($return_array, $this->getExceptionError($e->getPrevious()));
        }

        return $return_array;
    }

    abstract function processTopUp();

}