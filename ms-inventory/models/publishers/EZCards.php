<?php

namespace micro\models\publishers;

class EZ<PERSON>ards extends \offgamers\publisher\models\profile\EZCards
{
    protected $product_code;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: EZC_MY1_AAU-QB-Q1J
        if (count($sku) == 3) {
            $this->account = $sku[1];
            $this->product_code = $sku[2];
        } else {
            throw new \Exception('Invalid EZCards SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return '/orders/instant';
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_code == 'RESOURCE_NOT_FOUND') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $endpoint = static::create_order_url;
            $payload = [
                'clientOrderNumber' => 'OG_' . $this->api_restock_request_id,
                'sku' => $this->product_code,
            ];
            $response = $this->sendRequest('POST', $endpoint, $payload);
            if (($data = $this->checkError($response)) && !empty($data['data']['transactionId'])) {
                $this->publisher_order_id = $data['data']['transactionId'];
                usleep($this->check_order_initial_delay * 1000000); //Wait x seconds for a while for the order to actually create
                $this->apiGetCodes($this->check_order_max_retry_attempts);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder()
    {
        if (!$this->publisher_order_id) {
            $endpoint = static::check_order_url;
            $payload = [
                'clientOrderNumber' => 'OG_' . $this->api_restock_request_id,
            ];
            $response = $this->sendRequest('GET', $endpoint, $payload);
            if (($data = $this->checkError($response)) && !empty($data['data']['items'][0]['transactionId'])) {
                $this->publisher_order_id = $data['data']['items'][0]['transactionId'];
            }
        }
        if ($this->publisher_order_id) {
            $this->apiGetCodes(); //Do not attempt retry here
        }
    }

    private function apiGetCodes($retry_left = 0) {
        $endpoint = str_replace('{transactionId}', $this->publisher_order_id, static::get_codes_url);
        $payload = [];
        $response = $this->sendRequest('GET', $endpoint, $payload);
        if ($data = $this->checkError($response)) {
            $this->parseCdKey($data);
        }
        if ($this->status_flag != 1 && $retry_left > 0) {
            usleep($this->check_order_subsequent_delay * 1000000); //Wait another x seconds before retrying
            $this->apiGetCodes($retry_left - 1);
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data)
    {
        $data = $data['data'][0]['codes'][0] ?? null;
        $code = [];

        if (isset($data['redeemCode'])) {
            $code[] = 'Code : ' . $data['redeemCode'];
        }

        if (isset($data['pinCode'])) {
            $code[] = 'Pin : ' . $data['pinCode'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'EZCards';
    }

    public function getAPIProvider()
    {
        return 'EZCARDS_' . $this->getAccount();
    }
}
