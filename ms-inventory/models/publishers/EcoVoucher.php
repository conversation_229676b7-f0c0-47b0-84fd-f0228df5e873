<?php

namespace micro\models\publishers;

use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use yii\helpers\ArrayHelper;

class EcoVoucher extends \offgamers\publisher\models\profile\EcoVoucher
{
    protected $product_code;

    protected $currency_code;

    protected $amount;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: ECV_MY1_8368b0b8-5232-4ce8-b547-509ccaeaacb0_GBP_5000
        if (count($sku) == 5) {
            $this->account = $sku[1];
            $this->product_code = $sku[2];
            $this->currency_code = $sku[3];
            $this->amount = (float)($sku[4] / 100);
        } else {
            throw new \Exception('Invalid EcoVoucher SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return '/v2/issue';
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_code == 'E305') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $hash_params = ['amount', 'distributorCode', 'productCode', 'timestamp'];
            $endpoint = static::create_order_url;
            $payload = [
                'distributorCode' => $this->distributor_code,
                'productCode' => $this->product_code,
                'amount' => number_format($this->amount, 2, '.', ''),
            ];
            $response = $this->sendRequest('POST', $endpoint, $payload, $hash_params);
            $this->publisher_order_id = $this->request_id;
            if ($data = $this->checkError($response)) {
                $this->parseCdKey($data);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder()
    {
        if ($this->publisher_order_id) {
            $hash_params = ['distributorCode', 'requestId', 'timestamp'];
            $endpoint = static::check_order_url;
            $payload = [
                'distributorCode' => $this->distributor_code,
                'requestId' => $this->publisher_order_id,
            ];
            $response = $this->sendRequest('POST', $endpoint, $payload, $hash_params);
            if ($data = $this->checkError($response)) {
                $this->parseCdKey($data);
            }
        }
    }

    /**
     * @param integer $api_restock_request_id
     */
    public function voidTransaction($api_restock_request_id)
    {
        $extra_info_key_value = ArrayHelper::map(ApiRestockRequestExtraInfo::findAll(['key' => ['SKU', 'VOUCHER_CODE'], 'api_restock_request_id' => $api_restock_request_id]), 'key', 'value');
        if (isset($extra_info_key_value['SKU']) && isset($extra_info_key_value['VOUCHER_CODE'])) {
            $this->sku = $extra_info_key_value['SKU'];
            $voucher_code = $extra_info_key_value['VOUCHER_CODE'];
            $this->parseSKU();
            $this->getPublisher();
            $this->getConfig();
            $this->orders_id = $api_restock_request_id;
    
            $hash_params = ['amount', 'currencyCode', 'distributorCode', 'timestamp', 'voucherCode'];
            $endpoint = static::refund_url;
            $payload = [
                'distributorCode' => $this->distributor_code,
                'voucherCode' => $voucher_code,
                'amount' => number_format($this->amount, 2, '.', ''),
                'currencyCode' => $this->currency_code,
            ];
            $response = $this->sendRequest('POST', $endpoint, $payload, $hash_params);
            if ($data = $this->checkError($response)) {
                return isset($data['voucherCode']) && $data['voucherCode'] == $voucher_code;
            }
    
        }
        return false;
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data)
    {
        ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'VOUCHER_CODE', $data['code']);
        ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'SKU', $this->sku);

        $code = [];

        if (isset($data['code'])) {
            $code[] = 'Code : ' . $data['code'];
        }

        if (isset($data['pin'])) {
            $code[] = 'Pin : ' . $data['pin'];
        }

        if (isset($data['expirationTimestamp'])) {
            $code[] = 'Expiration Date : ' . $data['expirationTimestamp'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'EcoVoucher';
    }

    public function getAPIProvider()
    {
        return 'ECOVOUCHER_' . $this->getAccount();
    }
}
