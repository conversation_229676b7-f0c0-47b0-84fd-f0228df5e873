<?php

namespace micro\models\publishers;

use yii\helpers\Json;
use yii\base\InvalidArgumentException;

class Eneba extends \offgamers\publisher\models\profile\Eneba
{
    private $denomination, $currency, $region;
    public const GET_GIFT_CARD = "S_purchaseGiftCards";
    public const CHECK_GIFT_CARD = "G_giftCards";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
        $this->getToken();
        $this->setHeader();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_msg == "No Record Found For Publisher (Eneba)") {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];

        //create order
        if ($this->checkMargin()) {
            try {
                $input_arr = [
                    "denomination" => $this->getDeno(),
                    "currency" => $this->getCurrency(),
                    "quantity" => 1
                ];
                $output_arr = ["actionId", "batchId"];

                $params = [
                    "query" => $this->paramToQuery("mutation", self::GET_GIFT_CARD, $input_arr, $output_arr)
                ];
                $response = $this->sendRequest('POST', $this->headers, $params);
                $data = $this->checkError($response, self::GET_GIFT_CARD);
                if (isset($data['data'][self::GET_GIFT_CARD]['batchId'])) {
                    $this->publisher_order_id = $data['data'][self::GET_GIFT_CARD]['batchId'];
                    $this->checkOrder();
                } else {
                    throw new \Exception($this->error_msg ?: "No Required Param For Publisher (Eneba) Response");
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        //check order
        if (!empty($this->publisher_order_id)) {
            try {
                $input_arr = [
                    "batchId" => $this->publisher_order_id
                ];
                $output_arr = [
                    "denomination" => [
                        "amount",
                        "currency"
                    ],
                    "code",
                    "state"
                ];

                $params = [
                    "query" => $this->paramToQuery("query", self::CHECK_GIFT_CARD, $input_arr, $output_arr)
                ];
                $response = $this->sendRequest('POST', $this->headers, $params);
                $data = $this->checkError($response, self::CHECK_GIFT_CARD);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CHECK_ORDER', $e);
            }
        } else {
            $this->status_flag = 2;
            $this->error_code = "9999";
            $this->error_msg = "Please Check With Publisher For The Status Of This Voucher : " . $this->api_restock_request_id;
        }
    }

    private function paramToQuery($type, $action, $input_arr, $output_arr)
    {
        $input_string = $this->createInputString($input_arr);
        $output_string = $this->createOutputString($output_arr);

        return $type . '{' . $action . '(' . ($type == "mutation" ? 'input: {' : '') . $input_string . ($type == "mutation" ? '}' : '') . ') { ' . $output_string . ' }}';
    }

    private function createInputString($params)
    {
        $input_string = "";
        foreach ($params as $key => $value) {
            if (isset($value)) {
                $input_string .= $key . ': ' . (gettype($value) == "string" ? '"' : '') . $value . (gettype($value) == "string" ? '" ' : ' ');
            } else {
                $input_string .= $key . ': ""';
            }
        }
        return $input_string;
    }

    private function createOutputString($output_arr)
    {
        $output_string = "";
        $output = "";
        foreach ($output_arr as $key => $value) {
            if (is_array($value)) {
                $output_string = "";
                foreach ($value as $index => $display) {
                    $output .= $display . " ";
                }
                $output_string .= $key . " { " . $output . "} ";
            } else {
                $output_string .= $value . " ";
            }
        }
        return $output_string;
    }

    private function parseOrderResponse($data)
    {
        if (isset($data['data'][self::CHECK_GIFT_CARD][0]['code'])) {
            $this->status_flag = 1;
            $code = $this->parseCdKey(['Code' => $data['data'][self::CHECK_GIFT_CARD][0]['code']]);

            if (!empty($code) && !empty($this->publisher_order_id)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                $this->status_flag = 2;
                $this->error_msg = "The Order Does Not Contain Required Parameter .";
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    protected function getToken($attempt = 0)
    {
        # Renew token when token expiry less than 15 min
        try {
            if (empty($this->token) || time() + 900 > $this->token_expiry) {
                if ($this->obtainLock()) {
                    $this->getTokenFromPublisher();
                } else {
                    sleep(3);
                    if ($attempt < 3) {
                        $this->getExistingConfig();
                        return $this->getToken($attempt + 1);
                    } else {
                        throw new \Exception('Failed to get token from Eneba (' . $this->publisher . ')');
                    }
                }
            } else {
                return $this->token;
            }
        } catch (\Exception $e) {
            $this->releaseLock();
            throw ($e);
        }
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Eneba';
    }

    public function getAPIProvider()
    {
        if (strpos($this->sku, '_') === false) {
            return $this->sku;
        } else {
            $parts = explode('_', $this->sku);
            if (isset($parts[1])) {
                $region = strtoupper(explode("_", $this->sku)[1]);
                return 'ENEBA_' . $region;
            } else {
                throw new InvalidArgumentException('Invalid SKU Deno');
            }
        }
    }

    public function getOrderUrl()
    {
        return "PurchaseGiftCard";
    }

    public function getDeno()
    {
        if (strpos($this->sku, '_') === false) {
            return $this->sku;
        } else {
            $parts = explode('_', $this->sku);
            if (isset($parts[2])) {
                return (float)$parts[2] / 100;
            } else {
                throw new InvalidArgumentException('Invalid SKU Deno');
            }
        }
    }

    public function getCurrency()
    {
        if (strpos($this->sku, '_') === false) {
            return $this->sku;
        } else {
            $parts = explode('_', $this->sku);
            if (isset($parts[1])) {
                return (string)$parts[1];
            } else {
                throw new InvalidArgumentException('Invalid SKU Currency');
            }
        }
    }

}