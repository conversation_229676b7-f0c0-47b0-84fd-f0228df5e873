<?php

namespace micro\models\publishers;

use offgamers\publisher\models\profile\library\FuluAes;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Fulu extends \yii\base\Model
{
    private $id;
    private $base_url;
    private $aes;
    private $error_code;

    const create_order_url = 'api/user-order/create';
    const check_order_url = 'api/user-order/details';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['fulu.credential'])) {
            $config = $params['fulu.credential'];
            $this->base_url = $config['base_url'];
            $this->id = $config['secret_id'];
            $this->aes = new FuluAes($config['secret_key']);
        } else {
            throw new InvalidArgumentException('Missing Fulu credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'user-order';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                for($i = 0; $i < 5; $i++){
                    if ($this->error_code == '1000') {
                        sleep(2);
                        $this->checkOrder();
                    }
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                if ($this->error_code == '1001') {
                    $this->createOrder();
                }
                break;
        }
    }

    private function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $params = [
                    'timestamp' => time(),
                    'product_id' => $this->getPublisherProductId(),
                    'buy_num' => 1,
                    'user_order_id' => 'OG_' . $this->api_restock_request_id
                ];

                $data = ['secret_id' => $this->id, 'data' => $this->aes->encrypt($params)];

                $response = $this->sendRequest(self::create_order_url, $data);
                $data = $this->checkError($response);

                if ($data) {
                    $this->parseOrderResponse($data);
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkOrder()
    {
        try {
            $params = [
                'timestamp' => time(),
                'user_order_id' => 'OG_' . $this->api_restock_request_id
            ];

            $data = ['secret_id' => $this->id, 'data' => $this->aes->encrypt($params)];

            $response = $this->sendRequest(self::check_order_url, $data);
            $data = $this->checkError($response);

            if ($data) {
                $this->parseOrderResponse($data);
            }

        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();

            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == "1") {
                    $return_data = $this->aes->decrypt($data['data']);
                    if (!empty($return_data['cards'])) {
                        return $return_data;
                    } else {
                        $this->error_code = '1000';
                        $this->error_msg = "订单处理中";
                        $this->error = [
                            'data' => $data
                        ];
                    }
                }
                else if(isset($data['message']) && ($data['message'] == '订单不存在' || $data['message'] == '订单不存在-1')){
                    $this->error_code = '1001';
                }
                else {
                    $this->error_code = '1002';
                    $this->error_msg = ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '');
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error_msg = $data['status'] ?? '';
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');

        return false;
    }

    private function parseOrderResponse($data)
    {
        $this->publisher_order_id = $data['order_id'];

        if (!empty($data['cards']) && !empty($data['cards'][0])) {
            $this->status_flag = 1;
            $code = $this->parseCdKey($data['cards'][0]);
            if (!empty($code)) {
                $this->code_string = $code;
            }
        }
    }

    private function parseCdKey($code_array)
    {
        $key_list = [];
        foreach ($code_array as $key => $code) {
            switch ($key) {
                case 'card_no':
                    $prefix = 'Serial';
                    break;
                case 'card_deadline':
                    $prefix = 'Expiry Date';
                    break;
                case 'card_pwd':
                    $prefix = 'Pin';
                    break;
                default:
                    $prefix = $key;
                    break;
            }
            $key_list[] = $prefix . ' : ' . $code;
        }
        return implode('<br>', $key_list);
    }

    private function sendRequest($path, $params)
    {
        $this->getConfig();

        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $path, $options);
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'FULU';
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

