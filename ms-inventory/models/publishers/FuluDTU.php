<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use micro\models\ApiLog;
use offgamers\publisher\models\PublisherSetting;
use yii\helpers\Json;
use offgamers\publisher\models\profile\library\FuluAes;

class FuluDTU extends \yii\base\Model
{
    use DirectTopUpPublisherTrait;

    public $publisher;
    private $configuration_data;
    private $error_code;
    private $aes;
    private $publisher_status;
    private $top_up_status;

    private $retry_order = false;

    const CREATE_ORDER_URL = 'api/user-order/create';
    const CHECK_ORDER_URL = 'api/user-order/details';
    const PRODUCT_DETAILS = 'api/user-goods/details';
    const USER_VERIFY = 'api/user-order-verify/recharge-info';

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getAPIProvider()
    {
        return 'Fulu_DTU';
    }

    public function processTopUp()
    {
        $return_array = [];

        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;

        try {
            $this->getExistingConfig();
            if ($this->checkMargin()) {
                if ($this->sub_orders_top_up_status == 0 || $this->sub_orders_top_up_status == 3) {
                    $return_array['status'] = $this->createOrder();
                } elseif ($this->sub_orders_top_up_status == 2) {
                    $return_array['status'] = $this->checkOrder();
                } else {
                    $this->error_msg = 'Prevent Duplicate Submission, check transaction history and update status / order id';
                }

                $return_array['error_code'] = $this->error_code;
                $return_array['top_up_status'] = $this->top_up_status;
                $return_array['publisher_ref_id'] = $this->publisher_order_id;
                $return_array['error_msg'] = $this->error_msg;
            } else {
                //Margin Block
                $return_array['error_code'] = '1510';
            }
        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }

    public function createOrder()
    {
        $this->retry_order = false;

        $params = [
            'timestamp' => time(),
            'product_id' => $this->getProductSku(),
            'buy_num' => $this->getProductQuantity(),
            'user_order_id' => 'DTU_' . $this->sub_orders_top_up_id
        ];

        $params = $this->processProductSetting($params,$this->game_account_info);

        if (!empty($this->configuration_data['POSTBACK_URL'])) {
            $params['user_notify_url'] = $this->configuration_data['POSTBACK_URL'] . '&orders_id=' . $this->orders_id;
        }

        return $this->processOrder($params, self::CREATE_ORDER_URL);
    }

    public function checkOrder()
    {
        $params = [
            'timestamp' => time(),
            'user_order_id' => 'DTU_' . $this->sub_orders_top_up_id
        ];

        return $this->processOrder($params, self::CHECK_ORDER_URL);
    }

    private function processOrder($request_data, $path)
    {
        $url = $this->configuration_data['API_URL'] . '/' . $path;

        $aes = new FuluAes($this->configuration_data['SECRET_KEY']);

        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $request_data, $path);

        $request_data = ['secret_id' => $this->configuration_data['SECRET_ID'], 'data' => $aes->encrypt($request_data)];

        $response = $this->sendRequest($url, $request_data);

        $data = $this->checkError($response);

        if ($data) {
            switch ($data['status']) {
                case "成功":
                    $status = 1;
                    $error_code = '0';
                    $top_up_status = 'reloaded';
                    break;
                case "处理中":
                    $status = 2;
                    $error_code = '1200';
                    $top_up_status = 'pending';
                    break;
                default:
                    $status = 3;
                    $error_code = '1001';
                    $top_up_status = 'failed';
                    break;
            }
            $this->publisher_status = $status;
            $this->error_code = $error_code;
            $this->top_up_status = $top_up_status;
            $this->publisher_order_id = $data['order_id'];
            $api_log->endLog('2000', $data);
            return true;
        } else {
            $this->top_up_status = 'failed';
            $this->error_code = '1001';
            $api_log->endLog('2000', $this->error);
        }

        if ($this->retry_order) {
            return $this->createOrder();
        }

        return false;
    }

    public function processPostBack($data)
    {
        try {
            $this->publisher = $data['publisher_id'];
            $this->getExistingConfig();
            $aes = new FuluAes($this->configuration_data['SECRET_KEY']);
            $response = $aes->decrypt($data['raw_data']);
            if (isset($response['user_order_id'])) {
                return $response['user_order_id'];
            }
        } catch (\Exception $e) {
        }

        return false;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();

            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == "1") {
                    $aes = new FuluAes($this->configuration_data['SECRET_KEY']);
                    return $aes->decrypt($data['data']);
                } else {
                    if (isset($data['message']) && ($data['message'] == '订单不存在' || $data['message'] == '订单不存在-1')) {
                        $this->retry_order = true;
                    }
                    if (isset($data['status'])) {
                        $this->error_code = "1000";
                    }
                    $this->error_msg = ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '');
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error_msg = ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '');
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }

    private function sendRequest($url, $params)
    {
        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $url, $options);
    }

    private function getProductQuantity()
    {
        if (strpos($this->publisher_product_id, '_') === false) {
            return 1;
        } else {
            $parts = explode('_', $this->publisher_product_id);
            if (isset($parts[1]) && ctype_digit($parts[1])) {
                return (int)$parts[1];
            } else {
                throw new InvalidArgumentException('Invalid Products Quantity');
            }
        }
    }

    private function getProductSku()
    {
        if (strpos($this->publisher_product_id, '_') === false) {
            return $this->publisher_product_id;
        } else {
            $parts = explode('_', $this->publisher_product_id);
            if (isset($parts[0])) {
                return (int)$parts[0];
            } else {
                throw new InvalidArgumentException('Invalid Products Quantity');
            }
        }
    }

    protected function request($method, $url, $options)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . $this->orders_id);
        $result = $this->client->request($method, $url, $options, $this->retry_attempt);
        return $result;
    }

    public function getServer($input)
    {
        try {
            $this->publisher = $input['publisher_id'];
            $this->products_id = $input['product_id'];
            $this->getExistingConfig();
            $return_data = [
                "field_list" => [],
                "server_list" => [],
                "platform_list" => [],
                "product_setting_list" => []
            ];
            $data = null;

            if (isset($this->products_id)) {
                $this->orders_id = $this->products_id;
                $product_setting = [];
                $input_count = 0;
                $dropdown_count = 0;
                $setting_count = 0;

                $params = [
                    "timestamp" => time(),
                    "product_id" => $this->products_id
                ];

                $url = $this->configuration_data['API_URL'] . '/' . self::PRODUCT_DETAILS;
                $aes = new FuluAes($this->configuration_data['SECRET_KEY']);
                $request_data = ['secret_id' => $this->configuration_data['SECRET_ID'], 'data' => $aes->encrypt($params)];

                $response = $this->sendRequest($url, $request_data);

                $data = $this->checkError($response);

                if (isset($data['charge_template'])) {
                    // Check all setting for the product
                    foreach ($data['charge_template'] as $setting => $setting_value) {
                        if ($setting_value['type'] == 1) { //input type

                            if ($input_count == 0) {
                                $return_data['field_list']['account'] = ['name' => $setting_value['alias'], 'type' => "string"];
                                $product_setting["account"] = $setting_value['charge_field_name'];
                            } elseif ($input_count == 1) {
                                $return_data['field_list']['character'] = ['name' => $setting_value['alias'], 'type' => "string"];
                                $product_setting["character"] = $setting_value['charge_field_name'];
                            } else {
                                $this->error_msg = "Current setting only support 2 string input field. Please contact publisher to adjust from their side. Contact DEV if publisher require customization.";
                            }

                            $input_count++;
                        } elseif ($setting_value['type'] == 2) {

                            if ($dropdown_count == 0) {
                                $return_data['field_list']['server'] = ['name' => $setting_value['alias'], 'type' => "dropdown"];
                                $product_setting["server"] = $setting_value['charge_field_name'];
                                foreach ($setting_value['options'] as $server) {
                                    if (isset($server['name']) && !in_array($server['name'], $return_data['server_list'])) {
                                        $return_data['server_list'][$server['name']] = $server['name'];
                                    }
                                }
                            } elseif ($dropdown_count == 1) {
                                $return_data['field_list']['platform'] = ['name' => $setting_value['alias'], 'type' => "dropdown"];
                                $product_setting["platform"] = $setting_value['charge_field_name'];
                                foreach ($setting_value['options'] as $platform) {
                                    if (isset($platform['name']) && !in_array($platform['name'], $return_data['platform_list'])) {
                                        $return_data['platform_list'][$platform['name']] = $platform['name'];
                                    }
                                }
                            } else {
                                $this->error_msg = "Current setting only support 2 dropdown input field. Please contact publisher to adjust from their side. Contact DEV if publisher require customization.";
                            }

                            $dropdown_count++;
                        } else {
                            $this->error_msg = "Current setting only support 2 string and 2 dropdown input field. Please contact publisher to adjust from their side. Contact DEV if publisher require customization.";
                        }

                        $setting_count++;
                        if ($setting_count > 4) {
                            $this->error_msg = "Exceed maximum input field available. Please contact publisher to adjust from their side. Contact DEV if publisher require customization.";
                        }
                    }
                    if (isset($data['name'])) {
                        $return_data['product_setting_list'] = $product_setting;
                    }
                } else {
                    $this->error_msg = "Empty publisher games setting for this product.";
                }
            } else {
                $this->error_msg = "No Product ID For Server Sync.";
            }

            if (!empty($this->error_msg)) {
                $return_data['error'] = $this->error_msg ;
                $return_data['error_response'] = $data;
                $this->reportError($data);
            }

            return $return_data;

        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
            $this->reportError();
        }
        return false;
    }

    private function reportError($response = null)
    {
        if ($this->error_msg) {
            Yii::$app->slack->send('Sync Server Error', array(
                array(
                    'color' => 'warning',
                    'text' => 'Product ID : '. $this->products_id . "\n" .
                        $this->error_msg . "\n" . (isset($response)? JSON::encode($response) : "")
                )
            ), 'DEBUG');
        }
        return true;
    }
    public function validateAccount($input, $return_data = false)
    {
        $this->publisher = $input['publisher_id'];
        $this->getExistingConfig();

        $params = [
            'timestamp' => time(),
            'product_id' => $this->getProductSku()
        ];

        $request_data = $this->processProductSetting($params,$input);

        $url = $this->configuration_data['API_URL'] . '/' . self::USER_VERIFY;

        $aes = new FuluAes($this->configuration_data['SECRET_KEY']);

        $request_data = ['secret_id' => $this->configuration_data['SECRET_ID'], 'data' => $aes->encrypt($request_data)];

        $response = $this->sendRequest($url, $request_data);

        $data = $this->checkError($response);

        if ($data) {
            return true;
        }

        return false;
    }

    public function processProductSetting($params, $game_account_info)
    {
        if (isset($game_account_info['product_setting'])) {
            foreach ($game_account_info['product_setting'] as $param_key => $param_value){
                $params[$param_value] = $game_account_info[$param_key];
            }
        }

        return $params;
    }

}