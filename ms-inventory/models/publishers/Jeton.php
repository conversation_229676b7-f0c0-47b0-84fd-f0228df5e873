<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Jeton extends \yii\base\Model
{

    private $base_url;
    private $api_key;
    private $error_code;
    private $token;
    static $token_url = 'auth/merchant/token';
    static $order_url = 'withdraw/voucher/jetonCash';
    static $cache_token_jeton = 'cache_token_jeton';
    static $key_format = 'Voucher Number : %s<br>Security : %s<br>Expiry Date : %s/%s (MM/yyyy)';

    use RestockPublisherTrait
    {
        reset as defaultReset;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['jeton.credential'])) {
            $config = $params['jeton.credential'];
            $this->base_url = $config['base_url'];
            $this->api_key = $config['api_key'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->getAPIProvider() . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return "voucher_creation";
    }

    public function processOrder($status)
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;

        switch ($status) {
            case 0:
            case 2:
            case 3:
                $this->createOrder();
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $this->getConfig();
                $this->token = $this->getToken();

                if ($this->token) {
                    $opt = [
                        'Authorization' => $this->token
                    ];

                    $params = [
                        'amount' => $this->getPublisherProductAmount(),
                        'country' => $this->phone_country_code,
                        'currency' => $this->getPublisherCurrency(),
                        'email' => $this->customers_id . '@offgamers.com',
                        'firstName' => $this->getName(),
                        'lastName' => $this->getName(),
                        'referenceNo' => (string) $this->api_restock_request_id
                    ];

                    $response = $this->sendRequest($params, static::$order_url, $opt);
                    $data = $this->checkError($response);
                    if ($data) {
                        $this->parseOrderResponse($data);
                    }
                } else {
                    $this->createException('CREATE_ORDER');
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (!empty($data['accessToken'])) {
                    // cache token
                    if (!empty($data['expiryTimeoutInMinutes'])) {
                        Yii::$app->cache->set(static::$cache_token_jeton, $data['accessToken'], ((int) $data['expiryTimeoutInMinutes'] * 60));
                    }
                    return $data;
                } else if (isset($data['status']) && ($data['status'] == "APPROVED")) {
                    return $data;
                } elseif (isset($data['status']) && $data['status'] != "APPROVED") {
                    $this->status_flag = -1;
                    $this->error_code = $data['code'];
                    $this->error_msg = 'status : ' . $data['status'] . "\n" . 'message : ' . $data['message'];
                    $this->error = ['message' => $this->error_msg];
                } else {
                    $this->status_flag = -1;
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $status,
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string) $response->getBody();
            }

            $this->createException('CREATE_ORDER');
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        if (isset($data['status']) && ($data['status'] == "APPROVED")) {
            $this->publisher_order_id = (string) $data['transactionId'];

            $code = $this->parseCdKey($data);
            if (!empty($code)) {
                $this->status_flag = 1;
                $this->code_string = $code;
            }
        }
    }

    private function parseCdKey($data)
    {
        $voucher_param = ['voucherNumber', 'secureCode', 'expiryMonth', 'expiryYear'];

        $code = [];
        foreach ($voucher_param as $value) {
            if (!empty($data[$value])) {
                $code[$value] = $data[$value];
            }
        }

        if (count($code) == count($voucher_param)) {
            return sprintf(self::$key_format, $code['voucherNumber'], $code['secureCode'], $code['expiryMonth'], $code['expiryYear']);
        }
        return null;
    }

    private function getToken()
    {
        // Get token cache if exist
        if ($token = Yii::$app->cache->get(static::$cache_token_jeton)) {
            return $token;
        }

        $params = [
            'apiKey' => $this->api_key
        ];

        $response = $this->sendRequest($params, static::$token_url);
        $data = $this->checkError($response);

        if (!empty($data['accessToken'])) {
            return $data['accessToken'];
        }

        return false;
    }

    private function sendRequest($params, $url, $opt = [])
    {
        $options = [
            'headers' => array_merge(['Content-Type' => 'application/json'], $opt),
            'json' => $params,
        ];

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getPublisherCurrency()
    {
        return explode("_", $this->sku)[1];
    }

    private function getPublisherProductAmount()
    {
        return explode("_", $this->sku)[2];
    }

    private function getName()
    {
        $lorem = [
            'Lorem', 'Ipsum', 'Dolor', 'Sit', 'Amet', 'Consectetuer', 'Adipiscing', 'Elit', 'Aenean', 'Commodo',
            'Ligula', 'Eget', 'Massa', 'Cum', 'Sociis', 'Natoque', 'Penatibus', 'Magnis', 'Dis', 'Parturient',
            'Montes', 'Nascetur', 'Ridiculus', 'Mus', 'Donec', 'Quam', 'Felis', 'Ultricies', 'Nec', 'Pellentesque',
            'Pretium', 'Quis', 'Sem', 'Nulla', 'Consequat', 'Enim', 'Pede', 'Justo', 'Fringilla', 'Vel',
            'Aliquet', 'Vulputate', 'Arcu', 'Rhoncus', 'Imperdiet', 'Venenatis', 'Vitae', 'Nullam', 'Dictum', 'Mollis'
        ];
        return $lorem[rand(0, count($lorem) - 1)];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'JETON';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

}
