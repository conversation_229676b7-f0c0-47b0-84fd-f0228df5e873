<?php

namespace micro\models\publishers;

class LikeCard extends \offgamers\publisher\models\profile\LikeCard
{
    protected $product_id;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: LKC_MY1_376
        if (count($sku) == 3) {
            $this->account = $sku[1];
            $this->product_id = $sku[2];
        } else {
            throw new \Exception('Invalid LikeCard SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return self::create_order_url;
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_code == 'No Data About This Order') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $endpoint = static::create_order_url;
            $now = time();
            $payload = [
                'referenceId' => 'OG_' . $this->api_restock_request_id,
                'productId' => $this->product_id,
                'time' => $now,
            ];
            $payload['hash'] = $this->generateHash($now);
            $response = $this->sendRequest($endpoint, $payload);
            if ($data = $this->checkError($response)) {
                $this->parseCdKey($data);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder()
    {
        $endpoint = static::check_order_url;
        $payload = [
            'referenceId' => 'OG_' . $this->api_restock_request_id,
        ];
        $response = $this->sendRequest($endpoint, $payload);
        if ($data = $this->checkError($response)) {
            $this->parseCdKey($data);
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data)
    {
        $this->publisher_order_id = '' . $data['orderId'];
        
        $data = $data['serials'][0] ?? null;
        $code = [];

        if (isset($data['serialCode'])) {
            $code[] = 'Code : ' . $this->decryptSerial($data['serialCode']);
        }
        if (!empty($data['additionalGiftTitle']) && !empty($data['additionalGiftSerial'])) {
            $code[] = $data['additionalGiftTitle'] . ' : ' . $data['additionalGiftSerial'];
        }
        if (isset($data['validTo'])) {
            $code[] = 'Validity : ' . $data['validTo'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }


    protected function generateHash($time) {
        $email = strtolower($this->email);
        $phone = $this->phone;
        $key = $this->hash_key;
        return hash('sha256',$time.$email.$phone.$key);
    }

    protected function decryptSerial($encrypted_txt) {
        $secret_key = $this->secret_key;
        $secret_iv = $this->secret_iv;
        $encrypt_method = 'AES-256-CBC';
        $key = hash('sha256', $secret_key);
      
        //iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
        $iv = substr(hash('sha256', $secret_iv), 0, 16);
      
        return openssl_decrypt(base64_decode($encrypted_txt), $encrypt_method, $key, 0, $iv);
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'LikeCard';
    }

    public function getAPIProvider()
    {
        return 'LIKECARD_' . $this->getAccount();
    }
}
