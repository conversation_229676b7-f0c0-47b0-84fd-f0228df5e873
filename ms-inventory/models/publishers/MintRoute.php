<?php

namespace micro\models\publishers;

use Yii;
use micro\models\publishers\library\AesCtr;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class MintRoute extends \yii\base\Model
{
    private $public_key;
    private $private_key;
    private $user_name;
    private $password;
    private $base_url;
    private $error_code;

    static $order_url = 'api/voucher';

    static $check_order_url = 'api/order_details';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['mintroute.credential'])) {
            $config = $params['mintroute.credential'];
            $this->public_key = $config['public_key'];
            $this->private_key = $config['private_key'];
            $this->user_name = $config['user_name'];
            $this->password = $config['password'];
            $this->base_url = $config['base_url'];
        } else {
            throw new InvalidArgumentException('Missing MinRoute credentials config');
        }
    }

    public function getOrderUrl()
    {
        return static::$order_url;
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order ID Exists
                // MintRoute Provide different error code for sandbox & production environment, 1005 is for staging, 1054 is for live
                if ($this->error_code == 1005 || $this->error_code == 1054) {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                // MintRoute Provide different error code for sandbox & production environment, 1010 is for staging, 1073 is for live
                if ($this->error_code == 1010 || $this->error_code == 1073) {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $params = [
            'denomination_id' => $this->getPublisherProductId(),
            'quantity' => 1,
            'orderid' => $this->api_restock_request_id,
            'short' => true,
            'location' => 'offgamers'
        ];

        if ($this->checkMargin()) {
            try {
                $response = $this->sendRequest($params, static::$order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseCreateOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        $params = [
            'orderid' => $this->api_restock_request_id,
            'short' => true
        ];

        try {
            $response = $this->sendRequest($params, static::$check_order_url);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseCheckOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }

    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                return $data;
            } else {
                if (!empty($data['error_code'])) {
                    $this->error_code = $data['error_code'];
                    $this->error_msg = $data['error_code'];

                }
                if (!empty($data['error'])) {
                    if (!empty($this->error_msg)) {
                        $this->error_msg .= " - ";
                    }
                    $this->error_msg .= $data['error'];
                }

                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];

            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $exception_array = [1005, 1010, 1054, 1073];

        if (!empty($this->error_code) && !in_array($this->error_code,$exception_array)) {
            $this->createException('CREATE_ORDER');
        }

        return false;
    }

    private function parseCreateOrderResponse($data)
    {
        if ($data['status'] == true) {
            $this->status_flag = 1;

            $this->publisher_order_id = (string)$data['orderId'];

            if (!empty($data['voucher'])) {
                $code = $this->parseCdKey($data['voucher']);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            }
        }
    }

    private function parseCheckOrderResponse($data)
    {
        if (!empty($data[$this->api_restock_request_id])) {
            $this->status_flag = 1;

            $order_data = array_values($data[$this->api_restock_request_id])[0];
            $this->publisher_order_id = (string)$order_data['order_id'];
            $this->settlement_amount = $this->product_cost;
            $this->settlement_currency = $this->product_cost_currency;

            if (!empty($order_data['vouchers']) && count($order_data['vouchers']) == 1) {
                $code = $this->parseCdKey(array_values($order_data['vouchers'])[0]);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            } else {
                $this->createException('CDKEY', null);
            }
        }
    }

    private function parseCdKey($voucher)
    {
        // two expiry date to fulfill with mintroute different result on different api
        $voucher_param = [
            'Serial Number' => 'Serial Number',
            'Pin Code' => 'Pin Code',
            'Expiry' => 'Expiry',
            'expired_date' => 'Expiry',
            'giftPin' => 'giftPin'
        ];

        $code = [];
        foreach ($voucher_param as $value) {
            if (!empty($voucher[$value])) {
                $code[] = $value . ' : ' . $voucher[$value];
            }
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params, $url)
    {
        $this->getConfig();

        $json = [
            'username' => $this->user_name,
            'password' => $this->password,
            'data' => [
                $params
            ]
        ];

        $json = Json::encode($json);

        $body = AesCtr::encrypt($json, $this->private_key, 256);
        $token = base64_encode($this->public_key);

        $post = http_build_query(array('postedinfo' => $body, 'token' => $token));

        $options = array(
            'body' => $post,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getPublisherProductId()
    {
        return (int)explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'MINTROUTE';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }


}

