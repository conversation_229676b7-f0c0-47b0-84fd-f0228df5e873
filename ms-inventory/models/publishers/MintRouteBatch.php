<?php

namespace micro\models\publishers;

use micro\components\CdKeyCom;
use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use micro\models\CustomProductsCode;
use micro\models\LogApiRestock;
use offgamers\base\models\DevDebugLog;
use offgamers\base\models\ms\Product;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class MintRouteBatch extends \yii\base\Model
{
    private $public_key;
    private $private_key;
    private $user_name;
    private $password;
    private $base_url;
    private $error_code;
    private $time;
    private $order_status;
    private $publisher_id;

    // Order url for publisher mapping
    static $normal_order_url = 'api/voucher';
    static $order_url = 'api/bulk_voucher';
    static $check_order_url = 'api/get_bulk_voucher';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function init()
    {
        $this->getConfig();
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['mintroutebatch.credential'])) {
            $config = $params['mintroutebatch.credential'];
            $this->public_key = $config['public_key'];
            $this->private_key = $config['private_key'];
            $this->user_name = $config['user_name'];
            $this->password = $config['password'];
            $this->base_url = $config['base_url'];
            $this->publisher_id = $config['publisher_id'];
            $this->initTime();
        } else {
            throw new InvalidArgumentException('Missing MinRoute credentials config');
        }
    }

    public function getOrderUrl()
    {
        return static::$order_url;
    }

    public function processOrder($status)
    {
        return true;
    }

    public function createOrder($deno_id, $qty)
    {
        $params = [
            'denomination_id' => $deno_id,
            'quantity' => $qty,
            'location' => 'offgamers',
            'partial_order' => true,
        ];

        $response = $this->sendRequest($params, static::$order_url);

        $data = $this->checkError($response);

        if ($data) {
            $this->parseCreateOrderResponse($data);
        }

        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                return $data;
            } elseif (!empty($data['order_status']) && $data['order_status'] == 'PROCESSING') {
                $this->order_status = 'PROCESSING';
                return false;
            } elseif (!empty($data['order_status']) && $data['order_status'] == 'FAILED') {
                $this->order_status = 'FAILED';
                $this->error = [
                    'order_status' => 'FAILED',
                ];
            } else {
                if (!empty($data['error_code'])) {
                    $this->error_code = $data['error_code'];
                    $this->error_msg = $data['error_code'];
                }
                if (!empty($data['error'])) {
                    if (!empty($this->error_msg)) {
                        $this->error_msg .= " - ";
                    }
                    $this->error_msg .= $data['error'];
                }

                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data,
                ];

            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString(),
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->reportError();

        return false;
    }

    private function parseCreateOrderResponse($data)
    {
        if ($data['status'] == true) {
            $this->publisher_order_id = (string)$data['orderId'];
        }
    }

    private function parseCdKeyList($data)
    {
        $order_data = array_values(array_values($data)[0])[0];

        if (!empty($order_data['vouchers'])) {
            $code_list = [];
            foreach (array_values($order_data['vouchers']) as $cdkey) {
                $code = $this->parseCdKey($cdkey);
                if (!empty($code)) {
                    $code_list[] = $code;
                }
            }
            if (!empty($code_list)) {
                return $code_list;
            }
        }

        return false;
    }

    private function parseCdKey($voucher)
    {
        // two expiry date to fulfill with mintroute different result on different api
        $voucher_param = [
            'Serial Number' => 'Serial Number',
            'Pin Code' => 'Pin Code',
            'Expiry' => 'Expiry',
            'expired_date' => 'Expiry',
            'giftPin' => 'giftPin',
        ];

        $code = [];
        foreach ($voucher_param as $value) {
            if (!empty($voucher[$value])) {
                $code[] = $value . ' : ' . $voucher[$value];
            }
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params, $url)
    {
        $json = [
            'username' => $this->user_name,
            'password' => $this->password,
            'data' => [
                $params,
            ],
        ];
        $this->orders_id = $this->api_restock_request_id;

        $credential = $this->public_key . '/' . $this->time->format('Ymd');

        $signature = $this->generateSignature($json);

        $header = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'algorithm="hmac-sha256",credential="' . $credential . '",signature="' . $signature . '"',
            'X-Mint-Date' => $this->time->format('Ymd\THis\Z'),
        ];

        $options = array(
            'headers' => $header,
            'json' => $json,
            'http_errors' => false,
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function initTime()
    {
        $this->time = (new \DateTime())->setTimeZone(new \DateTimeZone('UTC'));
    }

    private function generateSignature($params)
    {
        $params_string = 'POST' . http_build_query($params) . $this->time->format('Ymd\THi');

        return base64_encode(hash_hmac('sha256', $params_string, $this->private_key, true));
    }

    private function compareSignature($params, $signature)
    {
        $this->time = \DateTime::createFromFormat('Ymd\THis\Z', $params['Event']['CreatedOn'], new \DateTimeZone('UTC'));
        if ($this->generateSignature($params) == $signature) {
            $this->initTime();
            return true;
        }
        return false;
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'MINTROUTE';
    }

    public function reset()
    {
        $this->order_status = null;
        $this->error_code = null;
        $this->defaultReset();
    }

    public function batchRestock()
    {
        $mintroute_restock_data = Yii::$app->params['mintroutebatch.credential']['products_info'];

        foreach ($mintroute_restock_data as $p_id => $p_info) {
            $this->products_id = $p_id;
            $this->getProductCost();
            $current_stock_level = CustomProductsCode::find()->where(['products_id' => $p_id, 'status_id' => '1'])->count();

            $pending_restock_quantity = ((new \yii\db\Query())->select("SUM(i.value)")->from('api_restock_request t')->leftJoin('api_restock_request_extra_info i',
                    't.api_restock_request_id = i.api_restock_request_id AND i.key="QUANTITY"')->where(['NOT IN', 't.status', [1, 2, 5]])->andWhere(['t.products_id' => $p_id])->scalar() ?? 0);

            $stock_level = $current_stock_level + $pending_restock_quantity;
            if ($p_info['RESTOCK_LEVEL'] >= $stock_level) {
                $restock_quantity = $p_info['RESTOCK_TO_QUANTITY'] - $stock_level;
                $single_order_quantity = 100;
                $full_order_quantity = intval($restock_quantity / $single_order_quantity);
                $partial_order_quantity = $restock_quantity % $single_order_quantity;

                for ($i = 0; $full_order_quantity > $i; $i++) {
                    $model = new ApiRestockRequest();
                    $model->status = 0;
                    $model->products_id = $p_id;
                    $model->save();

                    ApiRestockRequestExtraInfo::createExtraInfoLine([
                        'api_restock_request_id' => $model->api_restock_request_id,
                        'key' => 'QUANTITY',
                        'value' => '100',
                    ]);
                }

                if ($partial_order_quantity > 0) {
                    $model = new ApiRestockRequest();
                    $model->status = 0;
                    $model->products_id = $p_id;
                    $model->save();

                    ApiRestockRequestExtraInfo::createExtraInfoLine([
                        'api_restock_request_id' => $model->api_restock_request_id,
                        'key' => 'QUANTITY',
                        'value' => (string)$partial_order_quantity,
                    ]);

                }
            }
        }

        $this->checkExistingOrder();

        $this->createBatchRequest();
    }

    public function orderPostBack($data, $signature)
    {
        if ($this->compareSignature($data, $signature)) {
            $this->checkExistingOrder($data['Event']['MintrouteOrderId']);
        } else {
            $this->time = \DateTime::createFromFormat('Ymd\THis\Z', $data['Event']['CreatedOn'], new \DateTimeZone('UTC'));
            Yii::$app->slack->send('Failed to validate MintRoute Batch Signature', array(
                array(
                    'color' => 'warning',
                    'text' => \yii\helpers\Json::encode([
                        'data' => $data,
                        'signature' => $signature,
                        'calculated_signature' => $this->generateSignature($data),
                        'signed_string' => 'POST' . http_build_query($data) . $this->time->format('Ymd\THi')
                    ]),
                ),
            ), 'DEBUG');
        }
    }

    private function createBatchRequest()
    {
        $processing_request = ApiRestockRequest::find()
            ->where(['status' => 0])
            ->andWhere(['products_id' => array_keys(Yii::$app->params['mintroutebatch.credential']['products_info'])])
            ->andWhere([
                'IS',
                'orders_products_id',
                new \yii\db\Expression('NULL')
            ])->limit(10)->all();
        $count = 0;
        foreach ($processing_request as $request) {
            $qty = $this->getRestockQuantity($request->api_restock_request_id);
            $deno_id = Yii::$app->params['mintroutebatch.credential']['products_info'][$request->products_id]['DENO_ID'];
            $this->api_restock_request_id = $request->api_restock_request_id;
            $this->createOrder($deno_id, $qty);
            if (!empty($this->publisher_order_id)) {
                $request->publisher_order_id = $this->publisher_order_id;
                $request->status = 3;
                $request->save();
            } elseif ($this->error_code == '1142') {
                // Hit Order Creation Limit
                break;
            } else {
                $this->reportError();
                break;
            }

            $this->reset();
            $count++;
            if ($count >= 10) {
                // Stop Processing after create ten order, MintRoute Limit
                break;
            }
        }
    }

    private function checkExistingOrder($order_id = '')
    {
        $processing_request = ApiRestockRequest::find()->where(['status' => 3])
            ->andWhere(['products_id' => array_keys(Yii::$app->params['mintroutebatch.credential']['products_info'])])
            ->andWhere([
                'IS',
                'orders_products_id',
                new \yii\db\Expression('NULL')
            ]);
        if ($order_id) {
            $processing_request->andWhere(['publisher_order_id' => $order_id]);
        }
        $processing_request = $processing_request->all();
        foreach ($processing_request as $request) {
            if ($request->obtainLock() && $request->status == 3) {
                if (!empty($request->publisher_order_id)) {
                    $this->publisher_order_id = $request->publisher_order_id;
                    $this->api_restock_request_id = $request->api_restock_request_id;
                    $this->products_id = $request->products_id;
                    $this->getProductCost();

                    if ($this->checkOrder($request->publisher_order_id)) {
                        $request->status = 1;
                        $request->save();

                    } elseif ($this->order_status == 'FAILED') {
                        $request->status = 2;
                        $request->save();
                    }
                    $this->reset();
                }
                $request->releaseLock();
            }
        }
    }

    public function getProductCost()
    {
        $info = (new Product())->getProductInfoByOrdersId(['products_id' => $this->products_id]);

        $this->product_cost = (!empty($info['product_cost']) ? $info['product_cost'] : $info['product_original_cost']);
        $this->product_cost_currency = (!empty($info['product_cost_currency']) ? $info['product_cost_currency'] : $info['product_original_currency']);

        if (empty($this->product_cost) || empty($this->product_cost_currency)) {
            throw new \Exception('Missing Products Cost for ' . $this->products_id);
        }
    }

    public function checkOrder($publisher_order_id)
    {
        $params = [
            'orderid' => $publisher_order_id,
            'short' => true,
        ];

        try {
            $response = $this->sendRequest($params, static::$check_order_url);
            $data = $this->checkError($response);
            if ($data) {
                if ($cdk_list = $this->parseCdKeyList($data)) {
                    $cpc_id_list = [];
                    $request_qty = $this->getRestockQuantity($this->api_restock_request_id);
                    $delivered_qty = count($cdk_list);
                    if ($this->validateOrderCount($this->products_id, $request_qty, $delivered_qty)) {
                        foreach ($cdk_list as $cdkey) {
                            $cpc_id_list[] = $this->createCustomProductsCode($cdkey);
                        }

                        (new Product())->updateProductsQty([
                            [
                                'products_id' => $this->products_id,
                                'custom_products_code' => $cpc_id_list,
                                'qty' => $delivered_qty,
                            ],
                        ]);

                        ApiRestockRequestExtraInfo::createExtraInfoLine([
                            'api_restock_request_id' => $this->api_restock_request_id,
                            'key' => 'DELIVERED_QUANTITY',
                            'value' => (string)$delivered_qty,
                        ]);
                        return true;
                    }
                }
            }
        } catch (\Exception $e) {
            DevDebugLog::generateDebugLog('MintRouteBatch', $e->getTraceAsString());
        }

        return false;
    }

    private function getRestockQuantity($api_restock_request_id)
    {
        return ApiRestockRequestExtraInfo::find()->select('value')->where(['api_restock_request_id' => $api_restock_request_id, 'key' => 'QUANTITY'])->scalar();
    }

    private function validateOrderCount($products_id, $request_qty, $delivered_qty)
    {
        $stock_count = LogApiRestock::find()
            ->where(['serialnumber' => $this->publisher_order_id, 'flag_state' => 'S'])
            ->count();

        if ($stock_count + $delivered_qty > $request_qty) {
            Yii::$app->slack->send("MintRoute Batch : Stock Quantity ($stock_count + $delivered_qty) > Delivered Quantity ($request_qty) " . $this->api_restock_request_id);
            return false;
        }

        if ($request_qty != $delivered_qty) {
            Yii::$app->slack->send("MintRoute Batch : Partial Delivery Requested Qty ($request_qty) > Delivered Quantity ($delivered_qty) - $products_id");
        }

        return true;
    }

    private function createCustomProductsCode($code_string)
    {
        $cdkey_com = new CdKeyCom();
        $cdkey_extra_info = ['file_type' => 'soft', 'remarks' => "MINTROUTE_API", 'custom_products_code_id' => null];

        // Upload CDK to S3
        $cdk_model = $cdkey_com->uploadCdKey($this->products_id, $code_string, $cdkey_extra_info);
        $custom_products_code_id = $cdk_model->custom_products_code_id;

        $this->logAPIRequest([
            'method' => self::$normal_order_url,
            'custom_products_code_id' => $custom_products_code_id,
            'serialnumber' => $this->publisher_order_id,
            'publisher_id' => $this->publisher_id,
            'sku' => 'MR_' . Yii::$app->params['mintroutebatch.credential']['products_info'][$this->products_id]['DENO_ID']
        ]);

        return $custom_products_code_id;
    }

    private function reportError()
    {
        Yii::$app->slack->send("MintRoute Batch Delivery Error : $this->products_id", [
            [
                'color' => 'warning',
                'text' => "Product Id : " . $this->products_id . "\n Error : " . Json::encode($this->error),
            ],
        ], 'BDT_REPLENISH');
    }

}

