<?php

namespace micro\models\publishers;

use micro\models\publishers\library\AesCtr;
use Yii;
use yii\base\InvalidArgumentException;
use micro\models\ApiLog;
use offgamers\publisher\models\PublisherSetting;
use yii\base\Model;
use yii\helpers\Json;

class MintRouteDTU extends Model
{
    use DirectTopUpPublisherTrait;

    public $publisher;
    private $configuration_data;
    private $error_code;
    private $public_key;
    private $private_key;
    private $user_name;
    private $password;
    private $base_url;
    private $time;
    private $top_up_status;

    const get_server_url = 'top_up/api/account_initialization';
    const account_validation_url = 'top_up/api/account_validation';
    const create_order_url = 'top_up/api/account_topup';
    const check_order_url = 'voucher/api/order_details';

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getAPIProvider()
    {
        return 'MintRoute_DTU';
    }

    public function processTopUp()
    {
        $return_array = [];

        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;

        try {
            $this->getExistingConfig();
            if ($this->sub_orders_top_up_status == 2 || $this->sub_orders_top_up_status == 3) {
                $return_array['status'] = $this->checkOrder();
                $return_array['error_code'] = $this->error_code;
                $return_array['top_up_status'] = $this->top_up_status;
                $return_array['publisher_ref_id'] = $this->publisher_order_id;
                $return_array['error_msg'] = $this->error_msg;
            } elseif ($this->checkMargin()) {
                $return_array['status'] = $this->createOrder();
                $return_array['error_code'] = $this->error_code;
                $return_array['top_up_status'] = $this->top_up_status;
                $return_array['publisher_ref_id'] = $this->publisher_order_id;
                $return_array['error_msg'] = $this->error_msg;
            } else {
                //Margin Block
                $return_array['error_code'] = '1510';
            }
        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }

    public function createOrder()
    {
        $validate_data = $this->getValidationRequiredData();
        $order_data = $this->getTopUpRequiredData();

        if ($validate_data != $order_data) {
            $this->validateAccount($this->game_account_info, true);
        }

        foreach ($order_data as $key => $val) {
            $request_params[$key] = $this->game_account_info[$val];
        }

        $request_params['denomination_id'] = $this->getPublisherDenoId();
        $request_params['order_id'] = "DTU_" . $this->sub_orders_top_up_id;

        $url = $this->configuration_data['API_URL'] . '/' . self::create_order_url;

        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $request_params, self::create_order_url);

        $response = $this->sendRequest($request_params, $url);

        $data = $this->checkError($response);

        if ($data) {
            $this->top_up_status = 'reloaded';
            $this->error_code = 0;
            $this->publisher_order_id = $data['account_details']['transaction_id'];
            $api_log->endLog('2000', $data);
            return true;
        } else {
            $this->top_up_status = 'failed';
            $this->error_code = '1001';
            $api_log->endLog('2000', $this->error);
        }
    }

    public function checkOrder()
    {
        $params = [
            'orderid' => "DTU_" . $this->sub_orders_top_up_id,
            'short' => true
        ];

        $url = $this->configuration_data['API_URL'] . '/' . self::check_order_url;

        $response = $this->sendCheckOrderRequest($params, $url);

        try{
            $data = Json::decode($response->getBody());
        }
        catch (\Exception $e){
            $data = $e->getMessage();
        }

        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $params, self::create_order_url);

        if (!empty($data[$params['orderid']])) {
            $order_data = array_values($data[$params['orderid']])[0];
            if (!empty($order_data['topups']) && count($order_data['topups']) == 1) {
                $top_up_status = array_values($order_data['topups'])[0];
                $this->top_up_status = 'reloaded';
                $this->error_code = 0;
                $this->publisher_order_id = $top_up_status['merchant_transaction_id'];
                $api_log->endLog('2000', $data);
                return true;
            }
        } elseif (!empty($data['error_code']) && $data['error_code'] === '1073') {
            return $this->createOrder();
        } else {
            $this->top_up_status = 'failed';
            $this->error_code = '1001';
            $api_log->endLog('2000', $data);
        }
    }

    public function initValidation($input)
    {
        $this->publisher = $input['publisher_id'];
        $this->publisher_product_id = $input['publisher_product_id'];
        $this->getExistingConfig();
    }

    public function validateAccount($input, $return_data = false)
    {
        $validate_field = $this->getValidationRequiredData();
        $request_params = [];
        foreach ($validate_field as $key => $val) {
            $request_params[$key] = $input[$val];
        }
        $request_params['denomination_id'] = $this->getPublisherDenoId();
        $result = $this->sendRequest($request_params, $this->configuration_data['API_URL'] . '/' . self::account_validation_url);
        $data = $this->checkError($result);
        if ($data) {
            if ($return_data) {
                foreach ($data['account_details'] as $k => $v) {
                    if ($k === 'roles') {
                        $this->game_account_info['packed_role_id'] = $v[0]['packed_role_id'];
                        continue;
                    }
                    $this->game_account_info[$k] = $v;
                }
            }
            return true;
        }
        return false;
    }

    public function getServer($input)
    {
        $this->publisher = $input['publisher_id'];
        $this->getExistingConfig();

        $request_params['denomination_id'] = $input['denomination_id'];
        $result = $this->sendRequest($request_params, $this->configuration_data['API_URL'] . '/' . self::get_server_url);
        $data = $this->checkError($result);
        if ($data && isset($data['data']['serverList'])) {
            $server_list = [];
            foreach ($data['data']['serverList'] as $continent => $list) {
                foreach ($list as $server) {
                    $server_list[$server['serverId']] = $server['serverName'];
                }
            }
            return $server_list;
        }
        return false;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();

            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == true) {
                    return $data;
                } else {
                    if (!empty($data['error'])) {
                        $this->error_msg = ($data['error_code'] ?? '') . ' : ' . ($data['error'] ?? '');
                    }
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error_msg = $status;
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response->getBody()
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }

    private function sendRequest($params, $url)
    {
        $this->initTime();

        $json = [
            'username' => $this->configuration_data['USER_NAME'],
            'password' => $this->configuration_data['PASSWORD'],
            'data' => $params,
        ];

        $credential = $this->configuration_data['PUBLIC_KEY'] . '/' . $this->time->format('Ymd');

        $signature = $this->generateSignature($json);

        $header = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'algorithm="hmac-sha256",credential="' . $credential . '",signature="' . $signature . '"',
            'X-Mint-Date' => $this->time->format('Ymd\THis\Z'),
        ];

        $options = array(
            'headers' => $header,
            'json' => $json,
            'http_errors' => false,
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $url, $options);
    }

    private function sendCheckOrderRequest($params, $url)
    {
        $json = [
            'username' => $this->configuration_data['USER_NAME'],
            'password' => $this->configuration_data['PASSWORD'],
            'data' => [
                $params
            ]
        ];

        $json = Json::encode($json);

        $body = AesCtr::encrypt($json, $this->configuration_data['PRIVATE_KEY'], 256);
        $token = base64_encode($this->configuration_data['PUBLIC_KEY']);

        $post = http_build_query(array('postedinfo' => $body, 'token' => $token));

        $options = array(
            'body' => $post,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $url, $options);
    }


    protected function request($method, $url, $options)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . $this->orders_id);
        return $this->client->request($method, $url, $options, $this->retry_attempt);
    }

    private function getProductType()
    {
        return explode('_', $this->publisher_product_id)[0];
    }

    private function getPublisherDenoId()
    {
        return (int)explode('_', $this->publisher_product_id)[1];
    }

    private function getValidationRequiredData()
    {
        $type = $this->getProductType();
        $return_arr = [];
        switch ($type) {
            case 'FREEFIRE':
            case 'RAZERGOLD':
            case 'JIOSAAVN':
            case 'NIMOTV':
                $return_arr = [
                    'account_id' => 'account'
                ];
                break;
            case 'MOBILELEGEND':
                $return_arr = [
                    'account_id' => 'account',
                    'zone_id' => 'character'
                ];
                break;
            case 'NETDRAGON':
                $return_arr = [
                    'account_id' => 'account',
                    'server_id' => 'server'
                ];
                break;
            case 'EROSNOW':
                $return_arr = [
                    'calling_code' => 'account',
                    'account_id' => 'character'
                ];
                break;
        }
        return $return_arr;
    }

    private function getTopUpRequiredData()
    {
        $type = $this->getProductType();
        $return_arr = [];
        switch ($type) {
            case 'FREEFIRE':
                $return_arr = [
                    'account_id' => 'account',
                    'packed_role_id' => 'packed_role_id'
                ];
                break;
            case 'MOBILELEGEND':
                $return_arr = [
                    'account_id' => 'account',
                    'customer_id' => 'customer_id',
                    'flow_id' => 'flow_id'
                ];
                break;
            case 'RAZERGOLD':
                $return_arr = [
                    'account_id' => 'account',
                    'validated_token' => 'validated_token',
                    'reference_id' => 'reference_id'
                ];
                break;
            case 'JIOSAAVN':
                $return_arr = [
                    'account_id' => 'account'
                ];
                break;
            case 'NETDRAGON':
                $return_arr = [
                    'account_id' => 'account',
                    'server_id' => 'server'
                ];
                break;
            case 'NIMOTV':
                $return_arr = [
                    'account_id' => 'account',
                    'user_name' => 'user_name'
                ];
                break;
            case 'EROSNOW':
                $return_arr = [
                    'account_id' => 'character',
                    'auth_token' => 'auth_token'
                ];
                break;
        }
        return $return_arr;
    }


    private function initTime()
    {
        $this->time = (new \DateTime())->setTimeZone(new \DateTimeZone('UTC'));
    }

    private function generateSignature($params)
    {
        $params_string = 'POST' . http_build_query($params) . $this->time->format('Ymd\THi');

        return base64_encode(hash_hmac('sha256', $params_string, $this->configuration_data['PRIVATE_KEY'], true));
    }


}