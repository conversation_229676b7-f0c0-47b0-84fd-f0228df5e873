<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;

class MyCard extends \yii\base\Model
{
    private $key;
    private $secret;
    private $base_url;

    const create_order_url = 'GetEplayCard';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['mycard.credential'])) {
            $config = $params['mycard.credential'];
            $this->key = $config['key'];
            $this->secret = $config['secret'];
            $this->base_url = $config['base_url'];
        } else {
            throw new InvalidArgumentException('Missing MyCard credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'GetCard';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        $this->createOrder();
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $productsId = $this->getPublisherProductId();

                $params = [
                    "FacId" => $this->key,
                    "FacPwd" => $this->secret,
                    "PrdId" => $productsId,
                    "PrdAmt" => 1,
                    "FacTradeSeq" => 'OGM' . $this->api_restock_request_id,
                    "StoreId" => '',
                    'StoreName' => '',
                    'SubStoreId' => '',
                    'SubStoreName' => ''
                ];

                $response = $this->sendRequest($params, static::create_order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }


            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = simplexml_load_string(html_entity_decode($response->getBody()));
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (isset($data->ReturnResult->Return->ReturnMsgNo)) {
                    if ($data->ReturnResult->Return->ReturnMsgNo == 1) {
                        return $data->ReturnResult->GetCardResult;
                    } else {
                        $code = ($data->ReturnResult->Return->ReturnMsgNo ?? '');
                        $msg = ($data->ReturnResult->Return->ReturnMsg ?? '');
                        $this->error_msg = $code . ' : ' . $msg;
                        $this->error = [
                            'message' => $this->error_msg
                        ];
                    }
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');

        return false;
    }

    private function parseOrderResponse($data)
    {
        $this->publisher_order_id = (string) $data->OrdTable->TrdSeq;

        if (!empty($data->PrdTable)) {
            $this->status_flag = 1;
            $code = $this->processData($data);
            if (!empty($code)) {
                $this->code_string = $code;
            }
        }
    }

    private function processData($data)
    {
        $code = (!is_array($data->PrdTable) ? [$data->PrdTable] : $data->PrdTable);
        $free_code = (!is_array($data->FreeSnTable) ? [$data->FreeSnTable] : $data->FreeSnTable);

        $code = $this->parseCdKey($code);
        $free_code = $this->parseFreeSN($free_code);
        return (!empty($code) ? implode("<br><br>", array_merge($code, $free_code)) : null);
    }

    private function parseCdKey($code_list)
    {
        $key_list = [];
        $params = ['CardId', 'CardPwd'];
        foreach ($code_list as $code) {
            $code = $this->checkEmpty($code, $params);
            if(!empty($code)){
                $key_list[] = $code;
            }
        }
        return $key_list;
    }

    private function parseFreeSN($code_list)
    {
        $key_list = [];
        $params = ['PrizeName', 'PrizeSn', 'PrizePW', 'PrizeID'];
        foreach ($code_list as $code) {
            $code = $this->checkEmpty($code, $params);
            if(!empty($code)){
                $key_list[] = $code;
            }
        }
        return $key_list;
    }

    private function checkEmpty($code, $params)
    {
        $return_data = [];
        $code = (array) $code;
        
        foreach ($params as $key) {
            $str = (!empty($code[$key]) && is_scalar($code[$key]) ? $key . ' : ' . $code[$key] : '');
            if(!empty($code)){
                $return_data[] = $str;
            }
        }

        return implode('<br>', $return_data);
    }


    private function sendRequest($params, $path)
    {
        $this->getConfig();

        $options = array(
            'form_params' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $path, $options);
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'MYCARD';
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

