<?php

namespace micro\models\publishers;

use micro\models\ApiRestockRequest;
use micro\models\ApiRestockRequestExtraInfo;
use yii\helpers\ArrayHelper;

class Neosurf extends \offgamers\publisher\models\profile\Neosurf
{
    protected $id_product;
    protected $currency_code;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: NEO_MY1_1_EUR
        if (count($sku) == 4) {
            $this->account = $sku[1];
            $this->id_product = $sku[2];
            $this->currency_code = $sku[3];
        } else {
            throw new \Exception('Invalid Neosurf SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return static::create_order_method;
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_code == '145') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @param integer $api_restock_request_id
     */
    public function voidTransaction($api_restock_request_id)
    {
        $extra_info_key_value = ArrayHelper::map(ApiRestockRequestExtraInfo::findAll(['key' => ['SKU'], 'api_restock_request_id' => $api_restock_request_id]), 'key', 'value');
        if (isset($extra_info_key_value['SKU'])) {
            $this->sku = $extra_info_key_value['SKU'];
            $this->parseSKU();
            $this->getPublisher();
            $this->getConfig();
            $this->orders_id = $api_restock_request_id;
            
            $method = static::refund_method;
            $payload = [
                'IDReseller' => rtrim($this->id_reseller_prefix, '-') . '-' . $this->api_restock_request_id,
                'IDTransaction' => 'OG_' . $this->api_restock_request_id,
                'reason_label' => 'OTHER',
            ];
            $response = $this->sendRequest($method, $payload);
            if ($data = $this->checkError($method, $response)) {
                return in_array($data['result_code'], [6, 7]);
            }
    
        }
        return false;
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $method = static::create_order_method;
            $transaction_id = 'OG_' . $this->api_restock_request_id;
            $payload = [
                'currency' => $this->currency_code,
                'IDProduct' => $this->id_product * 1,
                'IDReseller' => rtrim($this->id_reseller_prefix, '-') . '-'. $this->api_restock_request_id,
                'IDTransaction' => $transaction_id,
                'quantity' => 1,
            ];
            $response = $this->sendRequest($method, $payload);
            if ($data = $this->checkError($method, $response)) {
                $this->publisher_order_id = $transaction_id;
                $this->parseCdKey($data);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder()
    {
        $method = static::check_order_method;
        $transaction_id = 'OG_' . $this->api_restock_request_id;
        $payload = [
            'currency' => $this->currency_code,
            'IDProduct' => $this->id_product * 1,
            'IDReseller' => rtrim($this->id_reseller_prefix, '-') . '-' . $this->api_restock_request_id,
            'IDTransaction' => 'FLOGTAG' . $transaction_id,
            'quantity' => 1,
        ];
        $response = $this->sendRequest($method, $payload);
        if ($data = $this->checkError($method, $response)) {
            $this->publisher_order_id = $transaction_id;
            $this->parseCdKey($data);
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data)
    {
        $data = $data['dtickets']['item'] ?? null;
        $code = [];

        if (isset($data['pincode'])) {
            $code[] = 'Code : ' . $data['pincode'];
        }
        // if (isset($data['serial_nb'])) {
        //     $code[] = 'Serial : ' . $data['serial_nb'];
        // }
        if (isset($data['date_expiry'])) {
            $code[] = 'Expiration Date : ' . $data['date_expiry'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            ApiRestockRequest::createExtraInfo($this->api_restock_request_id, 'SKU', $this->sku);
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'Neosurf';
    }

    public function getAPIProvider()
    {
        return 'NEOSURF_' . $this->getAccount();
    }
}
