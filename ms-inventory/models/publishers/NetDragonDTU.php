<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use micro\models\ApiLog;
use offgamers\publisher\models\PublisherSetting;
use yii\helpers\Json;


class NetDragonDTU extends \yii\base\Model
{
    use DirectTopUpPublisherTrait;

    private $publisher, $configuration_data, $error_code, $top_up_status, $denomination, $currency, $publisher_sku, $prefix_url;

    const TOP_UP_URL = 'topup', CHECK_ORDER_URL = 'ResellerApi/order', ACCOUNT_VALIDATION_URL = 'validate', GET_SERVER_URL = 'serverlist';

    public function getAPIProvider()
    {
        return 'NetDragon';
    }

    public function processTopUp()
    {
        $return_array = [];
        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;
        try {
            $this->getExistingConfig();
            $this->parsePublisherProductId();
            if ($this->sub_orders_top_up_status == 0) {
                $return_array['status'] = $this->createOrder();
            } elseif ($this->sub_orders_top_up_status == 2 || $this->sub_orders_top_up_status == 3) {
                $return_array['status'] = $this->checkOrder();
                if (!$return_array['status']) {
                    $return_array['status'] = $this->createOrder();
                }
            } else {
                $this->error_msg = 'Prevent Duplicate Submission, check transaction history and update status / order id';
            }

            $return_array['error_code'] = $this->error_code;
            $return_array['top_up_status'] = $this->top_up_status;
            $return_array['publisher_ref_id'] = $this->publisher_order_id;
            $return_array['error_msg'] = $this->error_msg;
        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            if ($this->validateAccount($this->game_account_info, true)) {
                $request_params = array(
                    "id" => $this->sub_orders_top_up_id,
                    "jsonrpc" => "2.0",
                    "method" => 'topup',
                    "param" => array(
                        array(
                            "serviceProvider" => $this->configuration_data['SERVICE_PROVIDER'],
                            "txnId" => $this->sub_orders_top_up_id,
                            "user" => array(
                                "userId" => $this->game_account_info['account'] ?? "",
                                "zoneId" => $this->game_account_info['server'] ?? ""
                            ),
                            "price" => array(
                                "currency" => $this->currency,
                                "amount" => $this->denomination
                            ),
                            "sku" => $this->publisher_sku,
                            "quantity" => 1,
                            "paymentChannelId" => $this->configuration_data['PAYMENT_CHANNEL_ID'],
                            "isForTest" => $this->configuration_data['IS_FOR_TEST'],
                        )
                    )
                );

                $hash = $this->getHash($request_params);
                $request_params['param'][0]['signature'] = $hash;

                $path = $this->prefix_url . '/' . self::TOP_UP_URL;

                $response = $this->sendRequest('POST', $path, $request_params);

                $url = $this->configuration_data['BASE_URL'] . '/' . $path;
                $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $request_params, $path);
                $data = $this->checkError($response);

                if ($data && isset($data['id'])) {
                    $this->top_up_status = 'reloaded';
                    $this->publisher_order_id = $data['id'];
                    $this->error_code = 0;
                    $this->error_msg = "";
                    $api_log->endLog('2000', $data);
                    return true;
                } else {
                    $this->top_up_status = 'failed';
                    $this->error_code = '1001';
                    $api_log->endLog('2000', $this->error);
                }
            }
        } else {
            //Margin Block
            $this->error_code = '1510';
        }
        return false;
    }

    public function checkOrder()
    {
        $request_params = array(
            "id" => $this->sub_orders_top_up_id,
            "serviceProvider" => $this->configuration_data['SERVICE_PROVIDER'],
            "txnId" => $this->sub_orders_top_up_id,
            "userName" => $this->game_account_info['account'] ?? "",
            "beginTime" => "",
            "endTime" => ""
        );

        $hash_content = $request_params['id'] . $request_params['serviceProvider'] . $request_params['beginTime'] . $request_params['endTime'] . $request_params['txnId'] . $request_params['userName'];
        $request_params['signature'] = $this->convertToHash($hash_content);

        $response = $this->sendRequest('POST', self::CHECK_ORDER_URL, $request_params);

        $url = $this->configuration_data['BASE_URL'] . '/' . self::CHECK_ORDER_URL;
        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $request_params, self::CHECK_ORDER_URL);

        $data = $this->checkError($response);

        if ($data && isset($data['Data']) && isset($data['Data'][0]['txnId'])) {
            $this->top_up_status = 'reloaded';
            $this->publisher_order_id = $data['Data'][0]['txnId'];
            $this->error_code = 0;
            $api_log->endLog('2000', $data);
            return true;
        } else {
            $this->top_up_status = 'failed';
            $this->error_code = '1001';
            $api_log->endLog('2000', $this->error);
        }

        return false;
    }

    private function getPrefixUrl()
    {
        $url_prefix = explode('_', $this->publisher_sku)[0];
        switch (strtolower($url_prefix)) {
            case 'tokens':
                $this->prefix_url = 'HeApi';
                break;

            case 'eps':
                $this->prefix_url = 'EoApi';
                break;

            case 'cps':
                $this->prefix_url = 'EncoApi';
                break;
        }
    }

    public function validateAccount($input, $return_data = false)
    {
        $this->parsePublisherProductId();

        $request_params = array(
            "id" => "0",
            "jsonrpc" => "2.0",
            "method" => 'validate',
            "param" => array(
                array(
                    "serviceProvider" => $this->configuration_data['SERVICE_PROVIDER'],
                    "txnId" => "0",
                    "user" => array(
                        "userId" => $input['account'] ?? "",
                        "zoneId" => $input['server'] ?? ""
                    ),
                    "price" => array(
                        "currency" => $this->currency,
                        "amount" => $this->denomination
                    ),
                    "sku" => $this->publisher_sku,
                    "quantity" => 1,
                    "paymentChannelId" => $this->configuration_data['PAYMENT_CHANNEL_ID'],
                    "isForTest" => $this->configuration_data['IS_FOR_TEST'],
                )
            )
        );

        $hash = $this->getHash($request_params);
        $request_params['param'][0]['signature'] = $hash;

        $path = $this->prefix_url . '/' . self::ACCOUNT_VALIDATION_URL;

        $response = $this->sendRequest("POST", $path, $request_params);
        $data = $this->checkError($response);

        if ($data && isset($data['result'])) {
            $this->game_account_info['username'] = $data['result']['username'] ?? "";
            return true;
        }
        return false;
    }

    private function getHash($request_params)
    {
        $hash_content = $request_params['id'] . $request_params['jsonrpc'] . $request_params['method'] . $request_params['param'][0]['serviceProvider'] . $request_params['param'][0]['txnId'] . $request_params['param'][0]['user']['userId'] . $request_params['param'][0]['user']['zoneId'] . $request_params['param'][0]['price']['currency'] . $request_params['param'][0]['price']['amount'] . $request_params['param'][0]['sku'] . $request_params['param'][0]['quantity'] . $request_params['param'][0]['paymentChannelId'] . $request_params['param'][0]['isForTest'];

        return $this->convertToHash($hash_content);
    }

    private function convertToHash($hash_content)
    {
        $hash = hash_hmac('sha256', $hash_content, $this->configuration_data['PRIVATE_KEY']);
        return $hash;
    }

    public function initValidation($input)
    {
        $this->publisher = $input['publisher_id'];
        $this->publisher_product_id = $input['publisher_product_id'];
        $this->getExistingConfig();
    }

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    private function sendRequest($method, $activity, $data = null)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . $this->orders_id);

        $options = array(
            'http_errors' => false
        );

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $activity, $options);
    }

    private function checkError($response)
    {
        try {
            $status = $response->getStatusCode();

            $result = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($result)) {
                    if ((isset($result['result']) || (isset($result['RetMsg']) && isset($result['Data']))) && !isset($result['error'])) {
                        return $result;
                    } elseif (isset($result['RetMsg']) && !isset($result['Data'])) {
                        $this->error_code = "9999";
                        $this->error_msg = "No Order Found In The Output.";
                    } else {
                        if (!empty($result['error']) && isset($result['error']['code'])) {
                            $this->error_code = $result['error']['code'];
                            $this->error_msg = $result['error']['message'];
                        }
                    }
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (NETDRAGON) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }

    public function getServer($input)
    {
        try {
            $this->publisher = $input['publisher_id'];
            $this->publisher_product_id = $input['publisher_product_id'];

            $this->getExistingConfig();
            $this->parsePublisherProductId();
            $path = $this->prefix_url . '/' . self::GET_SERVER_URL;

            $response = $this->sendRequest('GET', $path, array());
            $status = $response->getStatusCode();

            $result = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($result)) {
                    if (isset($result['serverList'])) {
                        foreach ($result['serverList'] as $server => $server_partition) {
                            foreach ($server_partition as $list => $value) {
                                if (isset($value['serverId'])) {
                                    $server_list[$value['serverId']] = $server . ' - ' . $value['serverName'];
                                }
                            }
                        }
                        return $server_list;
                    } else {
                        $this->error_code = "9999";
                        $this->error_msg = "No Output For Publisher (NETDRAGON) Response";
                    }
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (NETDRAGON) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }

    protected function parsePublisherProductId()
    {
        $sku = explode('_', $this->publisher_product_id);
        if (count($sku) == 3) {
            $this->currency = $sku[1];
            $this->publisher_sku = str_replace('-', '_', $sku[0]);
            $this->denomination = (float)($sku[2] / 100);
            $this->getPrefixUrl();
        } else {
            throw new \Exception('Invalid NetDragon SKU Format');
        }
    }
}