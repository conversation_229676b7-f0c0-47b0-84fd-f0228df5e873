<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class OpenBucks extends \yii\base\Model
{
    private $base_url;
    private $distributor_id;
    private $cert_file;
    private $cert_secret;
    private $api_key;
    private $error_code;

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['openbucks.credential'])) {
            $config = $params['openbucks.credential'];
            $this->base_url = $config['base_url'];
            $this->distributor_id = $config['distributor_id'];
            $this->api_key = $config['api_key'];
            $this->cert_file = $config['cert_file'];
            $this->cert_secret = $config['cert_secret'];
        } else {
            throw new InvalidArgumentException('Missing OpenBucks credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'card/issue';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
            case 3:
                $this->cancelOrder();
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            $params = [
                'amount' => $this->getPublisherAmount(),
                'delivery_type' => 'RETURN',
                'distributor_id' => $this->distributor_id,
                'shop_id' => 'www.offgamers.com',
                'terminal_id' => (string)$this->customers_id,
                'retailer_id' => 'OffGamers',
                'country' => 'SG',
                'currency' => 'USD',
                'product_id' => $this->getPublisherProductId(),
                'utc_offset' => '+08:00',
                'customer' => [
                    'id_type' => 'NONE'
                ],
                'capture' => true
            ];

            $headers = [
                'Correlation-ID' => $this->getPublisherRefId()
            ];

            try {
                $response = $this->sendRequest('POST', '', $headers, $params);
                $data = $this->checkError($response);

                if ($data) {
                    $this->parseOrderResponse($data);
                } else {
                    $this->cancelOrder();
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function cancelOrder()
    {
        // cancel_order_already_processed
        try {
            $response = $this->sendRequest('DELETE', 'order_' . $this->distributor_id . '_' . $this->getPublisherRefId());
            if ($this->checkError($response)) {
                $this->status_flag = -1;
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status <= 299) {
                if (isset($data['distributor_id']) && isset($data['order_id'])) {
                    return $data;
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } elseif (!empty($data['code'])) {
                if ($data['code'] == 'cancel_order_already_processed' || $data['code'] == 'order_not_found') {
                    // Create New Request Id on Transaction Fail
                    $this->status_flag = -1;
                } else {
                    $this->error_msg = ($data['number'] ?? '') . ' : ' . ($data['message'] ?? '');
                    $this->error = [
                        'message' => $this->error_msg
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        if (!empty($data['card'])) {
            $this->publisher_order_id = $data['order_id'];
            $this->status_flag = 1;
            $card = $data['card'];
            if (!empty($card['serial']) && !empty($card['pin'])) {
                $pin_format = 'Serial : %s<br>Card Number : %s<br>Date : %s (GMT+8)<br>Terminal Id : %s';
                $this->code_string = sprintf($pin_format, $card['serial'], $card['pin'], date("Y-m-d H:i:s"), $this->customers_id);
            }
        }
        else{
            $this->createException('Invalid_CDKEY', 'Invalid CDKEY Structure');
        }
    }

    public function sendRequest($request_type, $url, $headers = [], $data = [])
    {
        $headers = array_merge($headers, ['Authorization' => 'Basic ' . base64_encode($this->api_key)]);

        $params = array(
            'http_errors' => false,
            'headers' => $headers,
            'cert' => [
                Yii::getAlias('@micro') . '/config/cert/' . $this->cert_file,
                $this->cert_secret
            ],
            'curl' => [CURLOPT_SSLCERTTYPE => 'p12'],
        );

        if ($data) {
            $params['json'] = $data;
        }

        if (!empty(Yii::$app->params['proxy'])) {
            $params['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request($request_type, $this->base_url . '/' . $url, $params);
    }

    private function getPublisherRefId()
    {
        return 'OGM_' . $this->api_restock_request_id;
    }

    private function getPublisherAmount()
    {
        return (float)explode("_", $this->sku)[2] / 100;
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[1];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'OPENBUCKS';
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

