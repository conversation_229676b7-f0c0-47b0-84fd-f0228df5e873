<?php

namespace micro\models\publishers;

use offgamers\publisher\models\Publisher;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class PlayCoin extends \yii\base\Model
{
    private $key;
    private $merchant_code;
    private $base_url;

    const create_order_url = 'get_vouchers';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $publisher_name = 'PlayCoin ' . $this->getRegion();

        $publisher = Publisher::findOne([
            'title' => $publisher_name,
            'profile' => 'PlayCoin'
        ]);

        $settings = ArrayHelper::map($publisher->getPublisherSettings()->asArray()->all(), 'key', 'value');

        if (!empty($settings['BASE_URL']) && !empty($settings['API_KEY']) && !empty($settings['MERCHANT_CODE'])) {
            $this->base_url = $settings['BASE_URL'];
            $this->key = $settings['API_KEY'];
            $this->merchant_code = $settings['MERCHANT_CODE'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->getAPIProvider() . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'get_pins';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        $this->createOrder();
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {

                $params = [
                    'merchant_code' => $this->merchant_code,
                    'method' => self::create_order_url,
                    'request_id' => 'OGM' . str_pad($this->api_restock_request_id, 5, 0, STR_PAD_LEFT),
                    'gp_id' => $this->getPublisherProductGroupId(),
                    'voucher_id' => $this->getPublisherProductId(),
                    'quantity' => 1,
                    'language' => 'en'
                ];

                $params['hash'] = $this->generateSignature($params);

                $response = $this->sendRequest($params);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function generateSignature($params)
    {
        $hash_str = $this->key;
        foreach ($params as $param) {
            $hash_str .= $param;
        }
        return md5('@@' . $hash_str . '@@');
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (isset($data['status']) && $data['status'] == 0 && !empty($data['pins'])) {
                    return $data;
                } else {
                    $this->error_msg = ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '');
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error_msg = ($data['status'] ?? '') . ' : ' . ($data['message'] ?? '');
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');

        return false;
    }

    private function parseOrderResponse($data)
    {
        $this->publisher_order_id = $data['request_id'];

        if (!empty($data['pins'])) {
            $this->status_flag = 1;
            $code = $this->parseCdKey($data['pins']);
            if (!empty($code)) {
                $this->code_string = $code;
            }
        }
    }

    private function parseCdKey($code_list)
    {
        $key_list = [];
        foreach ($code_list as $code) {
            $key_list[] = 'Activation Code : ' . $code['PIN_1'];
            if (!empty($code['PIN_3'])) {
                $key_list[] = 'Bonus Code : ' . $code['PIN_3'];
            }
        }
        return implode('<br>', $key_list);
    }

    private function sendRequest($params)
    {
        $this->getConfig();

        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $this->merchant_code, $options);
    }

    private function getPublisherProductGroupId()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[3];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'PISB';
    }

    public function getAPIProvider()
    {
        $region = $this->getRegion();
        return 'PISB_' . $region;
    }

    public function getRegion()
    {
        return explode("_", $this->sku)[1];
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

