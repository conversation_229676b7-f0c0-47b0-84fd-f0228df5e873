<?php

namespace micro\models\publishers;

use micro\components\CdKeyCom;
use micro\models\ApiRestockRequest;
use micro\models\CustomProductsCode;
use offgamers\base\models\ms\Product;
use offgamers\publisher\models\Publisher;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class PrepaidForge extends \yii\base\Model
{
    private $base_url;
    private $user_name;
    private $password;
    private $api_token;
    private $token_expiry;
    private $stock_list;
    private $is_check_order = false;
    private $is_new_order = false;
    private $is_batch_request = false;
    private $cost_setting = 0;
    const get_token_url = 'signInWithApi';
    const check_stock_url = 'findStocks';
    const create_order_url = 'createApiOrder';
    const check_order_url = 'getResponseOfSingleApiCodeRequest';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->cost_setting = $this->product_cost;
        $this->retry_attempt = 1;
        $this->getConfig();
    }

    public function getConfig()
    {
        $publisher_name = 'PrepaidForge ' . $this->getRegion();

        $publisher = Publisher::findOne([
            'title' => $publisher_name,
            'profile' => 'PrepaidForge'
        ]);

        $settings = ArrayHelper::map($publisher->getPublisherSettings()->asArray()->all(), 'key', 'value');

        if (!empty($settings['BASE_URL']) && !empty($settings['USER_NAME']) && !empty($settings['PASSWORD'])) {
            $this->base_url = $settings['BASE_URL'];
            $this->user_name = $settings['USER_NAME'];
            $this->password = $settings['PASSWORD'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->getAPIProvider() . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'createApiOrder';
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                if ($this->is_check_order) {
                    $this->checkOrder();
                }
                break;
            case 2:
            case 3:
                $this->checkOrder();
                if ($this->is_new_order) {
                    $this->createOrder();
                }
                break;
        }
    }

    private function getToken()
    {
        if ($this->token_expiry) {
            $time_diff = ($this->token_expiry - microtime(true) * 1000) / 1000;
            if ($time_diff >= 3) {
                return;
            } elseif ($time_diff > 0) {
                sleep(3);
            }
        }

        $params = [
            'email' => $this->user_name,
            'password' => $this->password
        ];

        $data = $this->sendRequest('POST', self::get_token_url, $params);

        if (isset($data['apiToken']) && isset($data['tokenValidUntil'])) {
            $this->api_token = $data['apiToken'];
            $this->token_expiry = $data['tokenValidUntil'];

            # Check Token timeout again
            $this->getToken();
        }
    }

    public function batchRestock()
    {
        $prepaid_force_restock_data = Yii::$app->params['prepaidforge.products_info'];
        $this->is_batch_request = true;
        foreach ($prepaid_force_restock_data as $p_id => $p_info) {
            $this->products_id = $p_id;
            $this->sku = $p_info['SKU'];
            $this->stock_list = [];
            $this->getProductCost();
            $this->reset();
            $this->initialize();

            $current_stock_level = CustomProductsCode::find()->where([
                'products_id' => $p_id,
                'status_id' => '1'
            ])->count();

            $pending_restock_quantity = ApiRestockRequest::find()->where(['NOT IN', 'status', [1, 2, 5]])->andWhere(['products_id' => $p_id])->count();

            $stock_level = $current_stock_level + $pending_restock_quantity;

            if ($p_info['RESTOCK_LEVEL'] >= $stock_level) {
                $restock_quantity = $p_info['RESTOCK_TO_QUANTITY'] - $stock_level;

                for ($i = 0; $restock_quantity > $i; $i++) {
                    $model = new ApiRestockRequest();
                    $model->status = 0;
                    $model->products_id = $p_id;
                    $model->save();
                }
            }

            foreach (ApiRestockRequest::find()->where(['products_id' => $p_id])->andWhere(['NOT IN', 'status', [1, 2, 5]])->all() as $model) {
                if ($model->obtainLock()) {
                    $this->api_restock_request_id = $model->api_restock_request_id;
                    $this->processOrder($model->status);

                    if ($this->publisher_order_id && $this->status_flag == 1 && !empty($this->code_string)) {
                        $cpc_id = $this->createCustomProductsCode($this->code_string, $this->code_type);
                        if ($cpc_id) {
                            $model->status = 1;
                            $model->custom_products_code_id = $cpc_id;
                            $model->save();

                            (new Product())->updateProductsQty([
                                [
                                    'products_id' => $this->products_id,
                                    'custom_products_code' => [$cpc_id],
                                    'qty' => 1,
                                ],
                            ]);
                        }
                    } else {
                        // Stop processing for this products when error found
                        $model->status = ($this->status_flag == -1 ? 5 : 2);
                        $model->save();
                        $model->releaseLock();

                        Yii::$app->slack->send("PrepaidForge Batch Delivery Error : $this->products_id", [
                            [
                                'color' => 'warning',
                                'text' => "Product Id : " . $this->products_id . "\n Error : " . Json::encode($this->error_msg),
                            ],
                        ], 'BDT_REPLENISH');
                        break;
                    }
                    $model->releaseLock();
                } else {
                    Yii::$app->slack->send("PrepaidForge Batch Delivery Error : $this->api_restock_request_id", [
                        [
                            'color' => 'warning',
                            'text' => "Product Id : " . $this->products_id . "\n Error : Failed to obtain order lock",
                        ],
                    ]);
                }
            }
        }
    }

    public function getProductCost()
    {
        $info = (new Product())->getProductInfoByOrdersId(['products_id' => $this->products_id]);

        $this->product_cost = (!empty($info['product_cost']) ? $info['product_cost'] : $info['product_original_cost']);
        $this->product_cost_currency = (!empty($info['product_cost_currency']) ? $info['product_cost_currency'] : $info['product_original_currency']);

        if (empty($this->product_cost) || empty($this->product_cost_currency)) {
            throw new \Exception('Missing Products Cost for ' . $this->products_id);
        }
    }

    private function checkStock($sku)
    {
        if ($this->stock_list) {
            return;
        }

        $available_stock_list = [];

        $params = [
            'skus' => [$sku],
            //TODO :: Add in Scan code, unable to test due to publisher don't have existing code with image
            'types' => ["TEXT"]
        ];

        $data = $this->sendRequest('POST', self::check_stock_url, $params);

        ArrayHelper::multisort($data, 'purchasePrice');

        $account_currency = $this->getRegion();
        if ($this->product_cost_currency != $account_currency) {
            $cost_price = $this->getCurrencyObj()->advanceCurrencyConversion($this->cost_setting, $this->product_cost_currency, $account_currency, false, 'spot');
        } else {
            $cost_price = $this->cost_setting;
        }

        foreach ($data as $stock) {
            if ($stock['product'] == $sku && $cost_price >= $stock['purchasePrice']) {
                $available_stock_list[] = [
                    'quantity' => $stock['quantity'],
                    'type' => $stock['type'],
                    'price' => $stock['purchasePrice']
                ];
            }
        }

        if (empty($available_stock_list)) {
            $this->error_msg = 'No available stock for cost less than ' . $this->getCurrencyObj()->format($account_currency, $cost_price, ' ');
        }

        $this->stock_list = $available_stock_list;
    }

    private function getNextAvailableStock()
    {
        foreach ($this->stock_list as $index => $stock) {
            $this->settlement_amount = $stock['price'];
            $this->product_cost = $stock['price'];
            $this->product_cost_currency = $this->getRegion();
            $this->settlement_currency = $this->getRegion();

            if ($stock['quantity'] == 1) {
                unset($this->stock_list[$index]);
            }

            $stock['quantity'] = $stock['quantity'] - 1;

            return $stock;
        }
    }

    private function createOrder()
    {
        $this->getToken();
        $this->checkStock($this->getPublisherProductId());
        try {
            $stock = $this->getNextAvailableStock();

            if ($stock && ($this->is_batch_request || $this->checkMargin())) {
                $params = [
                    "sku" => $this->getPublisherProductId(),
                    "price" => $stock['price'],
                    "codeType" => $stock['type'],
                    "customOrderReference" => "OG_" . $this->api_restock_request_id
                ];

                $data = $this->sendRequest('POST', self::create_order_url, $params);

                if ($data) {
                    $this->parseOrderResponse($data);
                }
            }
        } catch (\Exception $e) {
            $this->createException('CREATE_ORDER', $e);
        }


        return true;
    }

    private function checkOrder()
    {
        $this->getToken();
        try {
            $params = [
                "customOrderReference" => "OG_" . $this->api_restock_request_id
            ];

            $data = $this->sendRequest('POST', self::check_order_url, $params);

            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CREATE_ORDER', $e);
        }
        return true;
    }

    private function createCustomProductsCode($code_string, $code_type)
    {
        $cdkey_com = new CdKeyCom();
        $cdkey_extra_info = ['file_type' => $code_type, 'remarks' => $this->getAPIProvider() . "_API", 'custom_products_code_id' => null];

        // Upload CDK to S3
        $cdk_model = $cdkey_com->uploadCdKey($this->products_id, $code_string, $cdkey_extra_info);
        $custom_products_code_id = $cdk_model->custom_products_code_id;

        $this->logAPIRequest([
            'method' => $this->getOrderUrl(),
            'custom_products_code_id' => $custom_products_code_id,
            'serialnumber' => $this->publisher_order_id,
            'publisher_id' => $this->getAPIProvider(),
            'sku' => $this->sku
        ]);

        return $custom_products_code_id;
    }

    private function getImageCode($url)
    {
        $response = $this->sendRequest('GET', $url);
        return (string)$response->getBody();
    }

    private function checkError($response)
    {
        $status = $response->getStatusCode();
        try {
            $data = Json::decode($response->getBody());
            if ($status == 200) {
                return $data;
            } else {
                $error_code = ($data['code'] ?? 0);
                $error_msg = ($data['message'] ?? 'Unknown response from publishers');
                $this->error_msg = $error_code . ' - ' . $error_msg;
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $response
                ];

                if ($error_msg == 'Order Reference OG_' . $this->api_restock_request_id . ' is not used.') {
                    $this->is_new_order = true;
                } elseif ($error_msg == 'Order Reference OG_' . $this->api_restock_request_id . ' already used. Please use another one.') {
                    $this->is_check_order = true;
                } elseif ($error_msg == 'Something went wrong. We refunded your money, please try again.') {
                    $this->status_flag = -1;
                }
            }
            return false;
        } catch (\Exception $e) {
            $this->error_msg = (string)$response->getBody();

            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');
    }

    private function parseOrderResponse($data)
    {
        if (isset($data['orderReference'])) {
            $this->publisher_order_id = $data['orderReference'];

            switch ($data['codeType']) {
                case 'TEXT':
                    $code = [];
                    if (!empty($data['serial'])) {
                        $code[] = 'Serial : ' . $data['serial'];
                    }
                    if (!empty($data['code'])) {
                        $code[] = 'Code : ' . $data['code'];
                    }
                    if (!empty($code)) {
                        $this->status_flag = 1;
                        $this->code_string = implode('<br>', $code);
                    }
                    break;

                case 'SCAN':
                    if ($data['filetype']) {
                        $this->code_type = strtolower($data['fileType']);
                        $image = $this->getImageCode($data['downloadLink']);
                        if (!empty($image)) {
                            $this->status_flag = 1;
                            $this->code_string = $image;
                        }
                    }
                    break;
            }
        }
    }

    private function sendRequest($action, $path, $params = [], $parse = true)
    {
        $options = [
            'http_errors' => false
        ];

        if ($path !== self::get_token_url) {
            $options['headers'] = [
                'X-PrepaidForge-Api-Token' => $this->api_token
            ];
        }

        if (!empty($params)) {
            $options['json'] = $params;
        }

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        $response = $this->request($action, $this->base_url . '/' . $path, $options);

        if ($parse) {
            return $this->checkError($response);
        }

        return $response;
    }

    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[2];
    }

    public function getDeno()
    {
        return 0;
    }


    public function getApiClassName()
    {
        return 'PrepaidForge';
    }

    public function getAPIProvider()
    {
        $region = strtoupper($this->getRegion());
        return 'PrepaidForge_' . $region;
    }

    public function getRegion()
    {
        return explode("_", $this->sku)[1];
    }

    public function reset()
    {
        $this->defaultReset();
        $this->is_new_order = false;
        $this->is_check_order = false;
    }

}

