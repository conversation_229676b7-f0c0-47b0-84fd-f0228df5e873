<?php

namespace micro\models\publishers;

class Pro extends \offgamers\publisher\models\profile\Pro
{
    protected $product_id;

    use RestockPublisherTrait;

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        //Example: PRO_MY1_10781
        if (count($sku) == 3) {
            $this->account = $sku[1];
            $this->product_id = $sku[2];
        } else {
            throw new \Exception('Invalid PRO SKU Format');
        }
    }

    public function getOrderUrl()
    {
        return '/v1/submitOrder';
    }

    public function processOrder($status)
    {
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                if ($this->error_code == '6006') {
                    $this->createOrder();
                }
                break;
        }
    }

    /**
     * @return void
     */
    private function createOrder()
    {
        if ($this->checkMargin()) {
            $endpoint = static::create_order_url;
            $payload = [
                'ip' => '127.0.0.1',
                'merchantOrderId' => 'OG_' . $this->api_restock_request_id,
                'orderItemsBOList' => [
                    [
                        'priceGroupGoodsId' => $this->product_id . '',
                        'buyNumber' => 1,
                        'rechargePlatformConfig' => '',
                    ],
                ],
            ];
            $response = $this->sendRequest($endpoint, $payload);
            if (($data = $this->checkError($response)) && !empty($data['data']['order'])) {
                usleep($this->check_order_initial_delay * 1000000); //Wait x seconds for the PIN to be created
                $this->checkOrder($this->check_order_max_retry_attempts);
            }
        }
    }

    /**
     * @return void
     */
    private function checkOrder($retry_left = 0)
    {
        $endpoint = static::check_order_url;
        $payload = [
            'merchantOrderId' => 'OG_' . $this->api_restock_request_id,
        ];
        $response = $this->sendRequest($endpoint, $payload);
        if (($data = $this->checkError($response)) && !empty($data['data'])) {
            $this->parseCdKey($data);
            if ($this->status_flag != 1 && $retry_left > 0) {
                usleep($this->check_order_subsequent_delay * 1000000); //Wait another x second before retrying
                $this->checkOrder($retry_left - 1);
            }
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public function parseCdKey($data)
    {
        $data = $data['data']['orderItemsList'][0] ?? null;

        if (!isset($data['orderStatus']) || $data['orderStatus'] != 2) {
            return;
        }

        $pin_data = $data['orderCardsList'][0] ?? [];

        $code = [];

        if (isset($pin_data['cardNumber'])) {
            $code[] = 'Code : ' . $pin_data['cardNumber'];
        }

        if (isset($pin_data['cardPass'])) {
            $code[] = 'Pin : ' . $pin_data['cardPass'];
        }

        if (isset($pin_data['expirationTime'])) {
            $code[] = 'Validity : ' . $pin_data['expirationTime'];
        }

        $this->code_string = implode('<br>', $code);

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'PRO';
    }

    public function getAPIProvider()
    {
        return 'PRO_' . $this->getAccount();
    }
}
