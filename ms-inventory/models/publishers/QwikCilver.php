<?php

namespace micro\models\publishers;

use Dompdf\Dompdf;
use offgamers\publisher\models\Publisher;
use <PERSON><PERSON>qer\Barcode\BarcodeGeneratorPNG;
use Psr\Http\Message\ResponseInterface;

class QwikCilver extends \offgamers\publisher\models\profile\QwikCilver
{
    use RestockPublisherTrait;

    private string $denomination;
    private string $currency_id;
    private string $publisher_sku;
    private string $region;

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    /**
     * @throws \Exception
     * @var integer $status
     */
    public function processOrder($status)
    {
        $this->parseSKU();

        $this->publisher_id = Publisher::find()->where([
            'title' => 'QwikCilver ' . $this->region,
            'profile' => 'QwikCilver',
            'status' => 1
        ])->scalar();

        $this->getConfig();
        $this->initClient();
        $this->getToken();

        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;

        switch ($status) {
            case 0:
                $this->createOrder();
                if ($this->error_code == 5313) {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == 5320) {
                    $this->createOrder();
                }
        }
    }

    protected function createOrder()
    {
        if ($this->checkMargin()) {
            $path = self::ORDER_URL;
            $body = [
                "address" => [
                    'firstname' => $this->billing_name,
                    'email' => $this->billing_email,
                    'country' => $this->billing_country,
                    'postcode' => $this->billing_postcode,
                    'billToThis' => true
                ],
                'payments' => [
                    [
                        'code' => 'svc',
                        'amount' => $this->denomination
                    ]
                ],
                'products' => [
                    [
                        'sku' => $this->publisher_sku,
                        'price' => $this->denomination,
                        'qty' => 1,
                        'currency' => $this->currency_id
                    ]
                ],
                'refno' => 'OFFGAMERS_' . $this->api_restock_request_id,
                'syncOnly' => true,
                'deliveryMode' => 'API'
            ];

            $headers = $this->getHeaders($path, 'POST', $body);
            $response = $this->sendRequest('POST', $path, $headers, $body);
            if ($data = $this->verifyResponse($response)) {
                $this->parseOrderResponse($data);
            }
        }
    }

    protected function checkOrder()
    {
        if (empty($this->publisher_order_id)) {
            $this->getPublisherOrderId();
        }
        if (!empty($this->publisher_order_id)) {
            $this->getCards();
        }
    }

    protected function getPublisherOrderId()
    {
        $path = self::ORDER_URL . '/OFFGAMERS_' . $this->api_restock_request_id . '/status';
        $headers = $this->getHeaders($path);
        $response = $this->sendRequest('GET', $path, $headers);

        if ($data = $this->verifyResponse($response)) {
            if (!empty($data['orderId'])) {
                $this->publisher_order_id = $data['orderId'];
            }
        }
    }

    protected function getCards()
    {
        $path = self::ORDER_URL . '/' . $this->publisher_order_id . '/cards';
        $headers = $this->getHeaders($path);
        $response = $this->sendRequest('GET', $path, $headers);

        if ($data = $this->verifyResponse($response)) {
            if (isset($data['cards']) && count($data['cards']) === 1) {
                $this->parseCDKey($data['cards'][0]);
            }
        }
    }

    protected function parseOrderResponse($data)
    {
        if (!empty($data['orderId'])) {
            $this->publisher_order_id = $data['orderId'];
            if (!empty($data['status']) && $data['status'] == 'COMPLETE' && isset($data['cards']) && count($data['cards']) === 1) {
                $this->parseCDKey($data['cards'][0]);
            }
        }
    }

    protected function parseCdKey($card)
    {
        $label = $card['labels'];

        $code_field = ['cardNumber', 'cardPin', 'activationCode', 'activationUrl', 'validity'];

        $code = [];

        foreach ($code_field as $field) {
            if (!empty($card[$field])) {
                $code[] = $this->getLabel($label, $field) . ' : ' . $card[$field];
            }
        }

        if (!empty($card['barcode'])) {
            $this->code_type = 'png';
            $generator = new BarcodeGeneratorPNG();
            $imgSource = '<img style="background-color:white;width:500px;" src="data:image/png;base64,' . base64_encode($generator->getBarcode($card['barcode'], $generator::TYPE_CODE_128)) . '">';
            // instantiate and use the dompdf class
            $dompdf = new Dompdf();
            foreach ($code as &$raw) {
                $raw = '<span>' . $raw . '</span>';
            }
            $dompdf->loadHtml('<html><body style="width:500px;font-size:30px;text-align:center"><br>' . $imgSource . '<br><br>' . implode('<br>', $code) . '</body></html>');

            $dompdf->render();

            $dom = $dompdf->output();

            $im = new \Imagick();
            $im->readImageBlob($dom);
            $im->setImageFormat('png');
            $im->trimImage(0.1 * \Imagick::getQuantum());
            $im->borderImage('', '10', '10');

            $this->code_string = $im;
        } elseif (!empty($code)) {
            $this->code_string = implode('<br>', $code);
        }

        if (!empty($this->code_string)) {
            $this->status_flag = 1;
        }
    }

    protected function getLabel($label, $field)
    {
        if ($field == 'activationUrl') {
            return 'Activation URL';
        }
        return (!empty($label[$field]) ? $label[$field] : $field);
    }


    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        if (count($sku) == 5) {
            $this->region = $sku[1];
            $this->currency_id = (int)$sku[2];
            $this->publisher_sku = $sku[3];
            $this->denomination = (float)($sku[4] / 100);
        } else {
            throw new \Exception('Invalid QwikCilver SKU Format');
        }
    }

    protected function sendRequest(string $method, string $path, array $headers = [], array $params = []): ?ResponseInterface
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        return parent::sendRequest($method, $path, $headers, $params);
    }

    public function getDeno()
    {
        return 0;
    }

    public function getOrderUrl()
    {
        return 'order';
    }

    public function getApiClassName()
    {
        return 'QwikCilver';
    }

    public function getAPIProvider()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);
        return 'QuickCilver_' . $region;
    }

    public function reset()
    {
        $this->error_code = 0;
        $this->defaultReset();
    }
}