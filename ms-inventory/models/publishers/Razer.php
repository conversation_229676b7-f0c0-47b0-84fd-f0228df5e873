<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Razer extends \yii\base\Model
{
    private $key;
    private $secret;
    private $base_url;

    const purchase_initiation_url = 'pinstore/purchaseinitiation';
    const create_order_url = 'pinstore/purchaseconfirmation';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        $region = explode("_", $this->sku)[1];
        if (!empty($params['razer.credential'][$region])) {
            $config = $params['razer.credential'][$region];
            $this->key = $config['applicationCode'];
            $this->secret = $config['secret'];
            $this->base_url = $config['base_url'];
        } else {
            throw new InvalidArgumentException('Missing Razer credentials config');
        }
    }

    public function getOrderUrl()
    {
        return 'confirmation';
    }

    public function processOrder($status)
    {
        $this->getConfig();
        $this->createOrder();
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $productsId = $this->getPublisherProductId();
                $token = $this->getToken($productsId);
                $this->publisher_order_id = $token;

                if ($token) {
                    $params = [
                        "applicationCode" => $this->key,
                        "version" => "v1",
                        "referenceId" => 'OGM' . $this->api_restock_request_id,
                        "validatedToken" => $token
                    ];

                    $params['signature'] = $this->generateSignature($params, 'order');

                    $response = $this->sendRequest($params, static::create_order_url);
                    $data = $this->checkError($response);
                    if ($data) {
                        $this->parseOrderResponse($data);
                    }
                } else {
                    $this->createException('CREATE_ORDER');
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (!empty($data['validatedToken'])) {
                    return $data;
                } elseif (isset($data['purchaseStatusCode'])) {
                    if ($data['purchaseStatusCode'] == 00) {
                        return $data;
                    } else {
                        $this->error_msg = $this->checkStatusMessage($data['purchaseStatusCode']);
                        $this->error = [
                            'message' => $this->error_msg
                        ];
                    }
                } elseif (isset($data['initiationResultCode'])) {
                    $this->error_msg = $this->checkStatusMessage($data['initiationResultCode']);
                    $this->error = [
                        'message' => $this->error_msg
                    ];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => ($data['message'] ?? $data)
                ];
            }
            return false;
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');

        return false;
    }

    private function parseOrderResponse($data)
    {
        if ($this->validateSignature($data, 'order')) {
            $this->publisher_order_id = $this->publisher_order_id . '_' . (string)$data['paymentId'];
            $this->settlement_amount = $this->product_cost;
            $this->settlement_currency = $this->product_cost_currency;

            if (!empty($data['coupons'])) {
                $this->status_flag = 1;
                $code = $this->parseCdKey($data['coupons']);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            }
        }
    }

    private function parseCdKey($coupons)
    {
        $code = [];
        foreach ($coupons as $items) {
            foreach ($items as $key => $value) {
                $code[] = $key . ' : ' . (is_array($value) ? implode(",", $value) : $value);
            }
            $code[] = '';
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function getToken($products_id)
    {
        if (!empty($this->publisher_order_id)) {
            return explode('_', $this->publisher_order_id)[0];
        }

        $params = [
            "applicationCode" => $this->key,
            "version" => "v1",
            "referenceId" => 'OGM' . $this->api_restock_request_id,
            "productCode" => $products_id,
            "quantity" => 1,
        ];

        $params['signature'] = $this->generateSignature($params, 'init');

        $response = $this->sendRequest($params, static::purchase_initiation_url);

        $data = $this->checkError($response);

        if (!empty($data['validatedToken']) && $this->validateSignature($data, 'init')) {
            return $data['validatedToken'];
        }

        return false;
    }

    private function sendRequest($params, $url)
    {
        $this->getConfig();

        $options = array(
            'form_params' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function generateSignature($body, $type)
    {
        $string = '';
        switch ($type) {
            case 'init':
                $params = ['applicationCode', 'productCode', 'quantity', 'referenceId', 'version'];
                break;
            case 'order':
                $params = ['applicationCode', 'referenceId', 'version', 'validatedToken'];
                break;
        }
        foreach ($params as $key) {
            if (isset($body[$key])) {
                $string .= $body[$key];
            }
        }
        return md5($string . $this->secret);
    }

    private function validateSignature($body, $type)
    {
        $params = [];
        switch ($type) {
            case 'init':
                $params = ['applicationCode', 'productCode', 'quantity', 'referenceId', 'initiationResultCode', 'version'];
                break;
            case 'order':
                $params = ['applicationCode', 'productCode', 'quantity', 'deliveredQuantity', 'currency', 'unitPrice', 'referenceId', 'purchaseStatusCode', 'version',];
                break;
        }
        if (!empty($params) && !empty($body['signature'])) {
            $string = '';
            foreach ($params as $key) {
                if (!isset($body[$key])) {
                    return false;
                }
                $string .= $body[$key];
            }
            return (md5($string . $this->secret) == $body['signature']);
        }

        return false;
    }

    private function checkStatusMessage($code)
    {
        $message = '';
        switch ($code) {
            case 01:
                $message = 'Incomplete : Payment has not complete or in middle of processing';
                break;
            case 02:
                $message = 'Out Of Stock : None or partial stock delivered due to out of stock';
                break;
            case 04:
                $message = 'Insufficient Fund : Insufficient merchant\'s fund to perform request';
                break;
            case 05:
                $message = 'Invalid Product Code or not supported by merchant or RAZER GOLD.';
                break;
            case 06:
                $message = 'The same reference id has been used for Pin Purchase Initiation Request.';
                break;
            case 07:
                $message = 'Country or IP Address Not Supported';
                break;
            case 99:
                $message = 'Failed : Purchase Transaction is failed';
                break;
        }

        return "($code) " . $message;
    }


    private function getPublisherProductId()
    {
        return explode("_", $this->sku)[2];
    }

    public function getDeno()
    {
        return 0;
    }

    public function getApiClassName()
    {
        return 'Razer';
    }

    public function getAPIProvider()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);
        return 'RAZER_' . $region;
    }

    public function reset()
    {
        $this->defaultReset();
    }


}

