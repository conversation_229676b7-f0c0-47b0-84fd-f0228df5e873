<?php

namespace micro\models\publishers;

use DateTime;
use DateTimeZone;
use offgamers\base\traits\GuzzleTrait;
use micro\models\ApiLog;

class RazerDTU extends \offgamers\publisher\models\profile\RazerV2
{
    use DirectTopUpPublisherTrait;
    use GuzzleTrait;

    private $product_type, $amount, $top_up_status, $denomination, $currency, $publisher_sku;

    const TOP_UP_URL = 'recharge/order';
    const CHECK_ORDER_URL = 'recharge/order';
    const ACCOUNT_VALIDATION_URL = 'recharge/order/validate';

    const ACCOUNT_GET_CHARACTER_URL = 'recharge/gameInfo/account';

    public function getAPIProvider()
    {
        return 'Razer';
    }

    public function processTopUp()
    {
        $return_array = [];
        $this->publisher = $this->publisher_id;
        $this->settlement_currency = $this->product_cost_currency;
        try {
            $this->getConfig();
            $this->parseSKU();
            $this->getToken();
            if ($this->sub_orders_top_up_status == 0) {
                $return_array['status'] = $this->createOrder();
            } elseif ($this->sub_orders_top_up_status == 2 || $this->sub_orders_top_up_status == 3) {
                $return_array['status'] = $this->checkOrder();
                # Create Order When Order Doesn't Exist
                if ($this->error_code == '02.002') {
                    $return_array['status'] = $this->createOrder();
                }
            } else {
                $this->error_msg = 'Prevent Duplicate Submission, check transaction history and update status / order id';
            }

            $return_array['error_code'] = $this->error_code;
            $return_array['top_up_status'] = $this->top_up_status;
            $return_array['publisher_ref_id'] = $this->publisher_order_id;
            $return_array['error_msg'] = $this->error_msg;
        } catch (\Exception $e) {
            $this->reportError($e);
        }

        return $return_array;
    }

    protected function getRazerReferenceOrderId(): string
    {
        return 'RAZER_DTU_' . $this->sub_orders_top_up_id;
    }

    protected function createOrder()
    {
        $order_request_id = $this->getRazerReferenceOrderId();

        if ($this->checkMargin()) {
            if ($this->validateAccount($order_request_id, $this->game_account_info)) {
                $params = $this->getOrderParams($order_request_id, $this->game_account_info);

                $path = self::TOP_UP_URL;

                $response = $this->sendRequest("POST", $path, $params);

                $url = $this->configuration_data['BASE_URL'] . '/' . $path;

                $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, $params, $path);

                return $this->checkOrderResponse(self::TOP_UP_URL, $response, $api_log);
            }
        } else {
            //Margin Block
            $this->error_code = '1510';
        }

        return false;
    }

    protected function checkOrder()
    {
        $check_order_path = self::CHECK_ORDER_URL . '/' . $this->getRazerReferenceOrderId();

        $response = $this->sendRequest('GET', $check_order_path, []);

        $url = $this->configuration_data['BASE_URL'] . '/' . self::CHECK_ORDER_URL;

        $api_log = ApiLog::createApiLog($this->publisher_id, $this->orders_top_up_id, $url, [], self::CHECK_ORDER_URL);

        return $this->checkOrderResponse(self::TOP_UP_URL, $response, $api_log);
    }

    public function getCharacterList($game_account_info)
    {
        $this->parseSKU();

        $character_list = [];
        $game_id = null;

        switch ($this->product_type) {
            case 'RAGNAROK_ORIGIN':
                $game_id = '561';
                break;
        }

        if ($game_id) {
            $params = [
                'game_id' => $game_id,
                'user_name' => $game_account_info['account'],
                'server_id' => $game_account_info['server_id'],
            ];

            $path = self::ACCOUNT_GET_CHARACTER_URL;

            $response = $this->sendRequest("POST", $path, $params);

            if ($data = $this->parseResponse($response)) {
                if (!empty($data['characters']) && is_array($data['characters'])) {
                    foreach ($data['characters'] as $character) {
                        $character_list[$character['character_id']] = $character['character_name'];
                    }
                }
            }
        }

        return $character_list;
    }

    protected function checkOrderResponse($path, $response, $api_log): bool
    {
        $error = null;
        if ($data = $this->parseResponse($response)) {
            if (!empty($data['order_status'])) {
                if ($data['order_status'] == 'success') {
                    $this->top_up_status = 'reloaded';
                    $this->publisher_order_id = $data['order_number'];
                    $this->error_code = 0;
                    $this->error_msg = "";
                    $api_log->endLog('2000', $data);
                    return true;
                } elseif ($data['order_status'] == 'accepted') {
                    if ($path == self::TOP_UP_URL) {
                        sleep(5);
                        return $this->checkOrder();
                    }
                } else {
                    $this->error_code = 1001;
                    $this->error_msg = 'Order Status : ' . $data['order_status'] . "\n" . 'Razer Order Number : ' . ($data['order_number'] ?? 'Invalid');
                }
            }
            $error = $data;
        }

        $this->top_up_status = 'failed';
        $this->error_code = ($this->error_code == '02.002' ? '02.002' : '1001');
        $api_log->endLog('2000', ($error ?: $this->error));
        return false;
    }

    /**
     * @throws \Exception
     */
    public function validateAccount($order_request_id = null, $game_account_info = []): bool
    {
        $this->parseSKU();

        $request_id = $order_request_id ?: 'VALIDATE_' . microtime(true) * 10000;

        $params = $this->getOrderParams($request_id, $game_account_info);

        $path = self::ACCOUNT_VALIDATION_URL;

        $response = $this->sendRequest("POST", $path, $params);

        if ($data = $this->parseResponse($response)) {
            if (!empty($data['validate_status']) && strtolower($data['validate_status']) == 'success') {
                return true;
            }
        }

        return false;
    }

    protected function getOrderParams($request_id, $game_account_info): array
    {
        $validate_field = $this->getAccountDataMapping();
        $request_params = [];

        foreach ($validate_field as $key => $val) {
            $data = [
                'field_name' => $key,
                'value_code' => $game_account_info[$val]
            ];
            $request_params[] = $data;
        }

        return [
            'reference_id' => $request_id,
            'order_date' => $this->getOrderDateTime(),
            'amount' => ($this->product_type == 'RAZER' ? $this->amount : 0),
            'currency' => $this->currency,
            'product' => [
                'sku' => $this->publisher_sku,
                'custom_value' => $request_params
            ]
        ];
    }

    public function initRazerPublisher()
    {
        if ($this->publisher_id && empty($this->publisher)) {
            $this->publisher = $this->publisher_id;
        }
        $this->getConfig();
        $this->getToken();
    }

    protected function getOrderDateTime(): string
    {
        $datetime = new DateTime('now', new DateTimeZone('UTC'));

        $microtime = microtime(true);

        $datetime->setTimestamp((int)$microtime);

        $timestamp = $datetime->format("Y-m-d\TH:i:s");

        $milliseconds = sprintf("%03d", ($microtime - floor($microtime)) * 1000);

        return $timestamp . "." . $milliseconds . "Z";
    }

    public function request($method, $url, $options)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . $this->orders_id);

        return $this->client->request($method, $url, $options);
    }

    private function getAccountDataMapping(): array
    {
        $type = $this->product_type;

        switch ($type) {
            case 'RAGNAROK_ORIGIN':
                $return_arr = [
                    'userName' => 'account',
                    'serverId' => 'server',
                    'characterId' => 'character'
                ];
                break;

            case 'GENSHIN_IMPACT':
            case 'RAGNAROK_X_NEXT_GEN':
            case 'MARVEL_DUEL':
            case 'BADLANDERS':
            case 'HONKAI_STAR_RAILS':
                $return_arr = [
                    'userName' => 'account',
                    'serverId' => 'server',
                ];
                break;
            case 'ONE_PUNCH_MAN':
            case 'MOBILE_LEGENDS_BANG_BANG':
                $return_arr = [
                    'userName' => 'account',
                    'serverId' => 'platform',
                ];
                break;
            case 'RAZER':
                $return_arr = [
                    'email' => 'account'
                ];
                break;
            default:
                $return_arr = [
                    'userName' => 'account'
                ];
                break;
        }
        return $return_arr;
    }

    protected function parseSKU()
    {
        $sku = explode('_', $this->publisher_product_id);
        if (count($sku) >= 4) {
            $this->product_type = str_replace('-', '_', $sku[0]);
            $this->account = $sku[1];
            $this->currency = $sku[2];
            $this->publisher_sku = $sku[3];
            $this->amount = (!empty($sku[4]) ? $sku[4] / 100 : 0);
        } else {
            throw new \Exception('Invalid Razer DTU SKU Format');
        }
    }
}