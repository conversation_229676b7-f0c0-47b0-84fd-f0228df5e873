<?php

namespace micro\models\publishers;

class RazerV2 extends \offgamers\publisher\models\profile\RazerV2
{

    /**
     * @var mixed|string
     */
    private $currency;
    private $ref_sku;

    /**
     * @throws \Exception
     */
    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }


    /**
     * @throws \Exception
     */
    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
    }

    use RestockPublisherTrait {
        reset as defaultReset;
        request as defaultRequest;
    }

    protected function parseSKU()
    {
        $sku = explode('_', $this->sku);
        if (count($sku) == 4) {
            $this->account = $sku[1];
            $this->currency = $sku[2];
            $this->ref_sku = $sku[3];
        } else {
            throw new \Exception('Invalid RazerV2 SKU Format');
        }
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
            case 3:
                $this->checkOrder();
                break;
        }
    }

    protected function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];

        //create order
        if ($this->checkMargin()) {
            try {
                $this->getToken();

                $params = [
                    "currency" => $this->currency,
                    "reference_id" => 'PIN_' . $this->api_restock_request_id,
                    "product" => [
                        [
                            'sku' => $this->ref_sku,
                            'quantity' => 1
                        ]
                    ]
                ];

                $response = $this->sendRequest('POST', static::ORDER_PIN, $params);

                if ($data = $this->parseResponse($response)) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    protected function checkOrder()
    {
        try {
            $this->getToken();
            $response = $this->sendRequest('GET', static::ORDER_PIN . '/' . 'PIN_' . $this->api_restock_request_id, []);

            if ($data = $this->parseResponse($response)) {
                $this->parseOrderResponse($data);
            }

            // Order ID not found, recreate order
            if ($this->error_code === '01.011') {
                $this->createOrder();
            }
        } catch (\Exception $e) {
            $this->createException('CREATE_ORDER', $e);
        }
    }

    public function parseOrderResponse($data)
    {
        if (!empty($data['order_number']) && !empty($data['order_status'])) {
            $this->publisher_order_id = $data['order_number'];
            if (!empty($data['pins'][0])) {
                if ($code = $this->parseCdKey($data['pins'][0])) {
                    $this->status_flag = 1;
                    $this->code_string = $code;
                }
            }
        }
    }

    private function parseCdKey($pin)
    {
        $code = [];

        if (!empty($pin['serialNumber'])) {
            $code[] = 'Serial Number : ' . $pin['serialNumber'];
        }

        if (!empty($pin['pinCode'])) {
            $code[] = 'Pin : ' . $pin['pinCode'];
        }

        if (!empty($pin['serialNumber2'])) {
            $code[] = 'Serial Number 2: ' . $pin['serialNumber'];
        }

        if (!empty($pin['pinCode2'])) {
            $code[] = 'Pin 2: ' . $pin['pinCode'];
        }

        if (!empty($pin['pin_expiry_date'])) {
            $date = date_create($pin['pin_expiry_date']);
            $code[] = 'Expiry : ' . date_format($date, "d-m-Y") . ' (GMT+8)';
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function request($method, $url, $options){
        return $this->defaultRequest($method, $url, $options);
    }

    public function getApiClassName()
    {
        return 'RazerV2';
    }

    public function getAPIProvider()
    {
        return 'RAZER_' . $this->getAccount();
    }

    public function getOrderUrl()
    {
        return self::ORDER_PIN;
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }
}