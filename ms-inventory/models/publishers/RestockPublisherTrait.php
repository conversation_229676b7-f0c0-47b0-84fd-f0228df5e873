<?php

namespace micro\models\publishers;

use Yii;
use micro\models\LogApiRestock;
use offgamers\base\models\DevDebugLog;
use yii\base\InvalidArgumentException;
use micro\exceptions\InvalidMarginException;
use micro\exceptions\GameCodeException;
use micro\exceptions\InvalidSkuException;
use micro\exceptions\OrderCheckingException;
use micro\exceptions\OrderCreationException;
use offgamers\publisher\models\PublishersReplenish;

trait RestockPublisherTrait
{
    use \offgamers\base\traits\GuzzleTrait;

    public $sku;
    public $orders_id;
    public $orders_currency;
    public $orders_products_id;
    public $orders_products_price;
    public $orders_products_currency_price;
    public $products_id;
    public $products_name;
    public $product_cost;
    public $product_cost_currency;
    public $publisher_order_id;
    public $customers_id;
    public $phone_country_code;
    public $customers_state;
    public $settlement_amount;
    public $settlement_currency = 'USD';
    public $status_flag = 0;
    public $error_msg;
    public $error;
    public $code_string;
    public $code_type = 'soft';
    public $api_restock_request_id;
    public $reseller_profile;

    protected $min_margin;
    protected $low_margin;

    protected $retry_attempt = 1;

    public $low_margin_notified = false;

    static $min_margin_params = 'restock_min_margin';
    static $low_margin_params = 'restock_low_margin';

    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'quantity', 'products_id', 'api_restock_request_id', 'customers_id'], 'integer'],
            [['sku', 'products_name', 'product_cost_currency', 'orders_currency', 'phone_country_code', 'customers_state', 'publisher_order_id', 'reseller_profile'], 'string'],
            [['orders_products_currency_price', 'orders_products_price', 'product_cost'], 'number'],
            [['low_margin_notified'], 'boolean']
        ];
    }

    protected function getCurrencyObj()
    {
        return Yii::$app->currency;
    }

    abstract function load($data, $formName = null);

    protected function getMarginPercentage()
    {
        if (empty($this->min_margin) && empty($this->low_margin) && !empty(Yii::$app->params[static::$min_margin_params]) && !empty(Yii::$app->params[static::$low_margin_params])) {
            $this->min_margin = Yii::$app->params[static::$min_margin_params];
            $this->low_margin = Yii::$app->params[static::$low_margin_params];
        } else {
            if (empty($this->min_margin) && empty($this->low_margin)) {
                throw new InvalidArgumentException('Low / Minimum Margin is not set.');
            }
        }
    }

    protected function checkMargin()
    {
        $this->getMarginPercentage();
        if (!empty($this->product_cost) && !empty($this->product_cost_currency)) {
            if ($this->product_cost_currency == $this->orders_currency) {
                $margin = (($this->orders_products_currency_price - $this->product_cost) / $this->orders_products_currency_price) * 100;
            } else {
                if (empty($this->orders_products_price)) {
                    $this->orders_products_price = $this->getCurrencyObj()->advanceCurrencyConversion($this->orders_products_currency_price, $this->orders_currency, 'USD', false, 'spot');
                }
                $cost_mst = $this->getCurrencyObj()->advanceCurrencyConversion($this->product_cost, $this->product_cost_currency, 'USD', false, 'spot');
                $margin = (($this->orders_products_price - $cost_mst) / $this->orders_products_price) * 100;
            }

            $margin = number_format($margin, 2);
            if ($margin > $this->min_margin) {
                if ($margin <= $this->low_margin) {
                    $this->lowMarginReport($margin, 'LOW_MARGIN');
                }
                return true;
            } else {
                $this->lowMarginReport($margin, 'MIN_MARGIN');
                $this->createException('CHECK_MARGIN');
            }
        } else {
            $this->error_msg = 'Missing Products Cost / Cost Currency';
            $this->createException('INVALID_ARGUMENT');
        }
    }

    public function getSettleAmount()
    {
        if (!empty($this->settlement_currency) && !empty($this->settlement_amount)) {
            $currency_code = $this->settlement_currency;
            $currency_settle_amount = $this->settlement_amount;
        } else {
            $currency_code = $this->product_cost_currency;
            $currency_settle_amount = $this->product_cost;
        }

        $currency_rate = $this->getCurrencyObj()->advanceCurrencyConversionRate($currency_code, 'USD', 'spot');
        $settle_amount = $this->getCurrencyObj()->advanceCurrencyConversion($currency_settle_amount, 'USD');

        return [
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'currency_settle_amount' => $currency_settle_amount,
            'settle_amount' => $settle_amount,
        ];
    }

    public function lowMarginReport($margin, $type)
    {
        $offgamersoldcrew = (!empty(Yii::$app->params['old.crew.domain']) ? Yii::$app->params['old.crew.domain'] : 'https://crew.offgamers.com');

        if ($this->low_margin_notified) {
            return;
        }

        if ($this->orders_currency == $this->settlement_currency) {
            $cost_str = $this->getCurrencyObj()->format($this->settlement_currency, $this->product_cost, " ");
            $price_str = $this->getCurrencyObj()->format($this->orders_currency, $this->orders_products_currency_price, " ");
        } else {
            if (strtoupper($this->orders_currency) !== 'USD') {
                $price_str = $this->getCurrencyObj()->format($this->orders_currency, $this->orders_products_currency_price, " ") . '(~' . $this->getCurrencyObj()->format(
                        'USD',
                        $this->orders_products_price,
                        " "
                    ) . ')';
            } else {
                $price_str = $this->getCurrencyObj()->format('USD', $this->orders_products_price, " ");
            }

            if (strtoupper($this->settlement_currency) !== 'USD') {
                $cost_mst = $this->getCurrencyObj()->advanceCurrencyConversion($this->product_cost, $this->settlement_currency, 'USD', false, 'spot');
                $cost_str = $this->getCurrencyObj()->format($this->settlement_currency, $this->product_cost, " ") . '(~' . $this->getCurrencyObj()->format('USD', $cost_mst, " ") . ')';
            } else {
                $cost_str = $this->getCurrencyObj()->format('USD', $this->product_cost, " ");
            }
        }

        $cost_str = html_entity_decode($cost_str);
        $price_str = html_entity_decode($price_str);
        $slack_content = '';
        $title = '';

        switch ($type) {
            case 'LOW_MARGIN':
                $this->low_margin_notified = true;
                $title = '*Low Margin Order delivery on ' . $this->getAPIProvider() . ' Replenish API*';

                $slack_content = $this->getSlackNotificationOrderPrefix();
                $slack_content .= "Product ID : <" . $offgamersoldcrew . "/categories.php?pID=" . $this->products_id . "&action=new_product|" . $this->products_id . "> \n Product Name : " . $this->products_name . " \n SKU : " . $this->sku . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . $this->low_margin . "%) \n `Action` : Revise Selling & Cost Setting";
                break;
            case 'MIN_MARGIN':
                $title = '*Order blocked from delivery on ' . $this->getAPIProvider() . ' Replenish API*';
                $slack_content = $this->getSlackNotificationOrderPrefix();
                $slack_content .= "Product ID : <" . $offgamersoldcrew . "/categories.php?pID=" . $this->products_id . "&action=new_product|" . $this->products_id . "> \n Product Name : " . $this->products_name . " \n SKU : " . $this->sku . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . $this->min_margin . "%) \n `Action` : Revise Selling & Cost Setting then process the order";
                break;
        }

        if ($title && $slack_content) {
            $attachments = [
                [
                    'color' => ($type == 'MIN_MARGIN' ? 'danger' : 'warning'),
                    'text' => $slack_content
                ],
            ];

            Yii::$app->slack->send($title, $attachments, 'BDT_REPLENISH');
        }
    }

    private function getSlackNotificationOrderPrefix()
    {
        $offgamersoldcrew = (!empty(Yii::$app->params['old.crew.domain']) ? Yii::$app->params['old.crew.domain'] : 'https://crew.offgamers.com');
        switch ($this->reseller_profile) {
            case 'G2G':
                $slack_content = "G2G Orders ID : " . $this->orders_id . "\n";
                break;
            case 'Manual':
                $slack_content = "Manual Restock ID : " . $this->orders_id . "\n";
                break;
            default:
                $slack_content = "Orders ID : <" . $offgamersoldcrew . "/orders.php?oID=" . $this->orders_id . "&action=edit|" . $this->orders_id . ">\n";
                break;
        }
        return $slack_content;
    }

    public function createException($tag = '', $e = null, $extra_args = [])
    {
        $error_code = (!empty($e) ? $e->getCode() : 0);

        if ($extra_args) {
            $this->error = (!empty($this->error) ? array_merge($this->error, $extra_args) : $extra_args);
        }

        switch ($tag) {
            case 'CREATE_ORDER':
                $exception = new OrderCreationException(($this->error_msg ?: 'Fail to create order'), $error_code, $e);
                break;

            case 'CHECK_ORDER':
                $exception = new OrderCheckingException(($this->error_msg ?: 'Fail to check order'), $error_code, $e);
                break;

            case 'CHECK_SKU':
                $exception = new InvalidSkuException(($this->error_msg ?: 'SKU is not supported by ' . $this->getAPIProvider()), $error_code, $e);
                break;

            case 'CHECK_MARGIN':
                $exception = new InvalidMarginException(($this->error_msg ?: 'Margin lower than minimum margin ' . $this->getAPIProvider()), $error_code, $e);
                break;

            case 'CD_KEY':
                $exception = new GameCodeException(($this->error_msg ?: 'Fail to get CDKey content' . $this->getAPIProvider()), $error_code, $e);
                break;

            default:
                $exception = new \Exception(($this->error_msg ?: 'Fail to create order : ' . ($e ? $e->getMessage() : '')), $error_code, $e);
                break;
        }

        throw $exception;
    }

    public function reportError(\Exception $e)
    {
        if ($e instanceof InvalidMarginException) {
            // Do Nothing, Notified at upper layer
        } else {
            if (empty($this->error_msg)) {
                $this->error_msg = 'Unhandled Exception : ' . $e->getMessage();
            }

            $slack_content = $this->getSlackNotificationOrderPrefix();

            $slack_content .= "Product Name : " . $this->products_name . " \n SKU : " . $this->sku . " \n Error : " . $this->error_msg . " \n `Action` : Reattempt delivery or contact publishers with error message";

            Yii::$app->slack->send('*Failed Delivery #' . $this->orders_id . ' delivery on ' . $this->getAPIProvider() . ' Replenish API*', [
                [
                    'color' => 'warning',
                    'text' => $slack_content,
                ],
            ], 'BDT_REPLENISH');

            if (!empty($this->error)) {
                $error = ['error_msg' => $this->error];
            }

            $error['exception'] = $this->getExceptionError($e);

            DevDebugLog::generateDebugLog('Failed Delivery #' . $this->orders_id . ' delivery on ' . $this->getAPIProvider() . ' Replenish API', $error);
        }
        return true;
    }

    private function getExceptionError(\Exception $e)
    {
        $return_array = [
            [
                'msg' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ],
        ];

        if ($e->getPrevious()) {
            $return_array = array_merge($return_array, $this->getExceptionError($e->getPrevious()));
        }

        return $return_array;
    }

    protected function request($method, $url, $options)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));
        $result = $this->client->request($method, $url, $options, $this->retry_attempt);
        return $result;
    }

    public function reset()
    {
        $this->publisher_order_id = null;
        $this->settlement_amount = null;
        $this->error = null;
        $this->error_msg = '';
        $this->status_flag = 0;
        $this->api_restock_request_id = null;
    }

    public function logAPIRequest($params = [])
    {
        $model = new LogApiRestock();

        $settlement_info = $this->getSettleAmount();

        $data = [
            'ack' => "1",
            'amount' => $this->getDeno(),
            'serialnumber' => $this->publisher_order_id,
            'api_provider' => $this->getAPIProvider(),
            'sku' => $this->sku,
            'flag_state' => 'S',
            'publishers_id' => $this->getReplenishPublisherId(),
        ];

        $data = array_merge($data, $settlement_info, $params);

        $model->load($data, "");
        if (!$model->save()) {
            $raw = [
                'validation_error' => $model->getErrors(),
                'attributes' => '',
            ];

            $attachments = [
                [
                    'color' => 'danger',
                    'text' => \yii\helpers\Json::encode($raw),
                ],
            ];

            Yii::$app->slack->send('Error Saving Log API Restock', $attachments, 'BDT_REPLENISH');
        }
    }

    public function updateLogApiRequest($params, $conditions)
    {
        $model = LogApiRestock::findOne($conditions);
        if ($model) {
            $model->load($params, "");
            $model->save();
        } else {
            $this->logAPIRequest(array_merge($conditions, $params));
        }
    }

    private function getReplenishPublisherId()
    {
        $model = PublishersReplenish::findOne(['publishers_api_provider' => $this->getAPIProvider()]);
        return ($model ? $model->publishers_replenish_id : 0);
    }

    public function getApiClassName()
    {
        return $this->getAPIProvider();
    }

    public function getDeno()
    {
        return 0;
    }

    abstract public function getAPIProvider();

    abstract public function processOrder($status);

    abstract public function getOrderUrl();

}