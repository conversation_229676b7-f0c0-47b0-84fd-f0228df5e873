<?php

namespace micro\models\publishers;

use offgamers\publisher\models\Publisher;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class Tiki extends \yii\base\Model
{

    private $base_url;
    private $channel_id;
    private $channel_secret;
    private $error_code;
    static $order_url = 'card/create';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function getConfig()
    {
        $publisher = Publisher::findOne([
            'title' => 'Tiki',
            'profile' => 'Tiki'
        ]);

        $settings = ArrayHelper::map($publisher->getPublisherSettings()->asArray()->all(), 'key', 'value');

        if (!empty($settings['BASE_URL']) && !empty($settings['CHANNEL_ID']) && !empty($settings['CHANNEL_SECRET'])) {
            $this->base_url = $settings['BASE_URL'];
            $this->channel_id = $settings['CHANNEL_ID'];
            $this->channel_secret = $settings['CHANNEL_SECRET'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->getAPIProvider() . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return "card/create";
    }

    public function processOrder($status)
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;

        switch ($status) {
            case 0:
            case 2:
            case 3:
                $this->createOrder();
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $this->getConfig();

                $params = [
                    'channelId' => $this->channel_id,
                    'requestUniqueKey' => (string)$this->api_restock_request_id,
                    'amount' => $this->getPublisherProductAmount(),
                    'diamond' => $this->getPublisherDenoAmount(),
                    'currency' => 'USD',
                    'country' => $this->phone_country_code
                ];

                $response = $this->sendRequest($params, static::$order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseCdKey($data);
                }

            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (isset($data['code']) && ($data['code'] == 0)) {
                    $this->publisher_order_id = (string) $this->api_restock_request_id;
                    return $data;
                } elseif (isset($data['code'])) {
                    $this->error_code = $data['code'];
                    $this->error_msg = 'status : ' . $data['code'] . "\n" . 'message : ' . $data['message'];
                    $this->error = ['message' => $this->error_msg];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $status,
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }

            $this->createException('CREATE_ORDER');
        }

        return false;
    }


    private function parseCdKey($data)
    {
        if (isset($data['data']['cardId'])) {
            $this->publisher_order_id = $data['data']['requestUniqueKey'];
            $this->status_flag = 1;
            $this->code_string = $data['data']['cardId'];
        }
        return null;
    }

    private function sendRequest($params, $url)
    {
        $options = [
            'headers' => [
                'sign' => $this->generateSignature($params)
            ],
            'json' => $params,
        ];

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getPublisherDenoAmount()
    {
        return explode("_", $this->sku)[2];
    }

    private function getPublisherProductAmount()
    {
        return explode("_", $this->sku)[1];
    }

    private function generateSignature($params)
    {
        $raw = Json::encode($params);
        return strtolower(hash_hmac('sha256', $raw, $this->channel_secret));
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'Tiki';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

}
