<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;
use yii\base\Model;

class Twbs extends Model
{
    private $baseUrl;
    private $token;
    private $resultCode;
    private $isCheckOrderRequired;

    const order_url = 'api/v1/distributor/issue';
    const check_order_url = 'api/v1/distributor/issue/status';
    const key_format = 'Code : %s<br>Serial : %s<br>Expiry Date : %s (dd/MM/yyyy)';

    const invalid_order_reference_code = 14314;

    const method_post = "POST";
    const method_get = "GET";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        if (!empty($params['twbs.credential'])) {
            $config = $params['twbs.credential'];
            $this->baseUrl = $config['base_url'];
            $this->token = $config['token'];
        } else {
            throw new InvalidArgumentException('Missing TWBS credentials config');
        }
    }

    public function processOrder($status)
    {
        $this->getConfig();

        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->isCheckOrderRequired) {
                    $this->checkOrder();
                }
                break;
            case 2:
            case 3:
                $this->checkOrder();
                // Order id not found
                if ($this->resultCode === static::invalid_order_reference_code) {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $params = [
            'orderReference' => $this->api_restock_request_id,
            'amount' => $this->getAmount(),
            'currencyCode' => $this->getCurrencyCode(),
            'brand' => $this->getBrand(),
            'issueAsActive' => true,
            'paymentCurrencyCode' => $this->getPaymentCurrencyCode()
        ];

        if ($this->checkMargin()) {
            try {
                $response = $this->sendPostRequest($params, static::order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        $params = [
            'orderReference' => $this->api_restock_request_id,
        ];
        try {
            $response = $this->sendGetRequest($params, static::check_order_url);
            $data = $this->checkError($response);
            if ($data) {
                $this->error_msg = "The order are already processed.";
                $this->parseOrderResponse($data);

                $this->createException('CHECK_ORDER');
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();

            if ($status == 200) {
                if (!empty($data['resultCode'])) {
                    $this->resultCode = $data['resultCode'];
                }
                return $data;
            }
            if ($status == 504) {
                $this->isCheckOrderRequired = true;
                return false;
            }
            if (!empty($data['resultCode'])) {
                $this->resultCode = $data['resultCode'];

                if ($this->resultCode === static::invalid_order_reference_code) {
                    return false;
                }
            }
            if (!empty($data['error'])) {
                $this->error_msg = $data['error'];
            }
            if (!empty($data['message'])) {
                $this->error_msg = $data['message'];
            }
            $this->error = [
                'http_status' => $response->getStatusCode(),
                'error' => $data
            ];
        } catch (\Exception $e) {
            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $this->createException('CREATE_ORDER');
        return false;
    }

    private function parseOrderResponse($data)
    {
        if ($data['status'] == true) {
            $this->status_flag = 1;

            $this->publisher_order_id = (string)$data['reference'];

            if (!empty($data['code'])) {
                $code = $this->parseCdKey($data);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            }
        }
    }

    private function parseCdKey($data)
    {
        $voucher_param = ['code', 'serialNumber', 'validTo'];
        $code = [];
        foreach ($voucher_param as $value) {
            if (!empty($data[$value])) {
                $code[$value] = $data[$value];
            }
        }
        if (!empty($code['validTo'])) {
            $code['validTo'] = date("d/m/Y", strtotime($code['validTo']));
        }
        if (count($code) === count($voucher_param)) {
            return sprintf(self::key_format, $code['code'], $code['serialNumber'], $code['validTo']);
        }
        return null;
    }

    private function sendGetRequest($params, $url)
    {
        $body = [
            'query' => $params
        ];

        return $this->sendRequest($body, $url, static::method_get);
    }

    private function sendPostRequest($params, $url)
    {
        $body = [
            'json' => $params
        ];

        return $this->sendRequest($body, $url, static::method_post);
    }

    private function sendRequest($body, $url, $method)
    {
        $headers = [
            'Authorization' => 'token ' . $this->token,
            'Content-Type' => 'application/json'
        ];

        $options = [
            'headers' => $headers,
            'http_errors' => false
        ];

        if (is_array($body)) {
            $options = array_merge($options, $body);
        }

        return $this->request($method, $this->baseUrl . '/' . $url, $options);
    }

    private function getBrand(): string
    {
        $brand = explode("_", $this->sku)[1];

        return $this->getBrandName($brand);
    }

    private function getPaymentCurrencyCode()
    {
        $currency_code = $this->getCurrencyCode();

        if (in_array($currency_code, ['USD', 'EUR', 'AUD', 'CAD'])) {
            return $currency_code;
        }

        return 'USD';
    }

    private function getCurrencyCode()
    {
        return explode("_", $this->sku)[2];
    }

    private function getAmount()
    {
        return explode("_", $this->sku)[3];
    }

    private function getBrandName($brandCode): string
    {
        $capBrandCode = strtoupper($brandCode);
        switch ($capBrandCode) {
            case "VIVOCO":
                // Staging Use
                $brandName = "vivoco";
                break;
            case "CASHTOCODE":
                $brandName = "CashToCode E-Voucher";
                break;
            case "MIFINITY":
                $brandName = "mifinity";
                break;
            case "RAZEREUR":
                $brandName = "razereea";
                break;
            default:
                $brandName = "";
                break;
        }
        return $brandName;
    }

    public function getApiClassName()
    {
        return 'Twbs';
    }

    public function getAPIProvider()
    {
        $currency = strtoupper($this->getPaymentCurrencyCode());
        return "TWBS_" . $currency;
    }

    public function getDeno()
    {
        return 0;
    }

    public function getOrderUrl()
    {
        return "distribute/issue";
    }

    public function reset()
    {
        $this->defaultReset();
    }
}