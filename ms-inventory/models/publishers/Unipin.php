<?php

namespace micro\models\publishers;

use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Unipin extends \yii\base\Model
{
    private $key;
    private $secret;
    private $base_url;
    private $error_code;

    static $order_url = 'voucher/request';
    static $check_order_url = 'voucher/inquiry';

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->settlement_currency = $this->product_cost_currency;
    }

    public function getConfig()
    {
        $params = Yii::$app->params;
        $region = explode("_", $this->sku)[1];
        if (!empty($params['unipin.credential'][$region])) {
            $config = $params['unipin.credential'][$region];
            $this->key = $config['key'];
            $this->secret = $config['secret'];
            $this->base_url = $config['base_url'];
        } else {
            throw new InvalidArgumentException('Missing Unipin credentials config');
        }
    }

    public function getOrderUrl()
    {
        return static::$order_url;
    }

    public function processOrder($status)
    {
        $this->getConfig();
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->error_code == '708') {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == '716') {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $params = [
            'partner_guid' => $this->key,
            'denomination_code' => $this->getPublisherProductId(),
            'quantity' => 1,
            'reference_no' => 'OG_' . $this->api_restock_request_id
        ];

        $params['signature'] = hash('sha256', $this->key . $params['denomination_code'] . $params['quantity'] . $params['reference_no'] . $this->secret);

        if ($this->checkMargin()) {
            try {
                $response = $this->sendRequest($params, static::$order_url);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        $params = [
            'partner_guid' => $this->key,
            'reference_no' => 'OG_' . $this->api_restock_request_id
        ];

        $params['signature'] = hash('sha256', $this->key . $params['reference_no'] . $this->secret);

        try {
            $response = $this->sendRequest($params, static::$check_order_url);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
        return true;
    }

    private function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (!empty($data['status']) && $data['status'] == 1 && empty($data['error'])) {
                    return $data;
                } elseif (!empty($data['error'])) {
                    $this->error_msg = ($data['error']['message'] ?? '');
                    $this->error_code = ($data['error']['error_code'] ?? '');

                    if($this->error_code == '721'){
                        // Create New Request Id on Transaction Fail
                        $this->status_flag = -1;
                    }

                    $this->error = [
                        'code' => ($data['error']['error_code'] ?? ''),
                        'message' => $this->error_msg
                    ];
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $data
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $exception_array = ['708', '716'];

        if (!empty($this->error_code) && !in_array($this->error_code, $exception_array)) {
            $this->createException('CREATE_ORDER');
        }

        return false;
    }

    private function parseOrderResponse($data)
    {
        $mandatory_field = ['message', 'reference_no', 'order', 'total_amount', 'currency'];
        foreach ($mandatory_field as $field) {
            if (empty($field)) {
                $this->createException('CREATE_ORDER');
            }
        }
        if ($data['signature'] == hash('sha256', $data['message'] . $data['reference_no'] . $data['order'] . $data['total_amount'] . $data['currency'] . $this->secret)) {
            $this->status_flag = 1;

            $this->publisher_order_id = (string)$data['order'];
            $this->settlement_amount = $this->product_cost;
            $this->settlement_currency = $this->product_cost_currency;

            if (!empty($data['items'])) {
                $code = $this->parseCdKey($data['items']);
                if (!empty($code)) {
                    $this->code_string = $code;
                }
            }
        }
    }

    private function parseCdKey($voucher)
    {
        $code = [];
        foreach ($voucher as $items) {
            foreach ($items as $key => $value) {
                $code[] = $key . ' : ' . $value;
            }
            $code[] = '';
        }
        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function sendRequest($params, $url)
    {
        $this->getConfig();

        $options = array(
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request('POST', $this->base_url . '/' . $url, $options);
    }

    private function getPublisherProductId()
    {
        $arr = explode("_", $this->sku);
        return implode("_", array_splice($arr, 2));
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        return 'UNIPIN';
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }


}

