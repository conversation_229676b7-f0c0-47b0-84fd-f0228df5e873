<?php

namespace micro\models\publishers;

use offgamers\base\models\OutgoingRequestLog;
use Yii;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\TripleDES;


class VTC extends \yii\base\Model
{
    const KEY_FORMAT = 'Codes : %s<br>Serial : %s<br>Expiry Date : %s (dd/MM/yyyy)';
    private $public_key;
    private $private_key;
    private $user_name;
    private $url;
    private $error_code;
    private $api_provider;
    private $triple_des;
    private $partner_code;
    private $command_type;
    private $request_data;
    private $service_code;
    private $amount;
    private $quantity = 1;
    private $vtc_scoin = 'SVTC';
    private $vtc_vcoin = 'VTC';
    private $buy_card = 'BuyCard';
    private $get_card = 'GetCard';
    private $response_field = [
        'BuyCard' => [
            'ResponseCode',
            'OrgTransID',
            'VTCTransID',
            'PartnerBalance',
            'DataSign',
            'message'
        ],
        'GetCard' => [
            'ResponseCode',
            'OrgTransID',
            'ListCard'
        ]
    ];

    use RestockPublisherTrait {
        reset as defaultReset;
    }


    public function getConfig()
    {
        $params = Yii::$app->params;
        if ($this->api_provider == $this->vtc_scoin) {
            $credential = 'scoin.credential';
        } else {
            $credential = 'vcoin.credential';
        }
        if (!empty($params[$credential])) {
            $config = $params[$credential];
            $this->public_key = $config['public_key'];
            $this->private_key = $config['private_key'];
            $this->triple_des = $config['triple_des_key'];
            $this->partner_code = $config['partner_code'];
            $this->url = $config['url'];
        } else {
            throw new InvalidArgumentException('Missing ' . $this->api_provider . ' credentials config');
        }
    }

    public function getOrderUrl()
    {
        return $this->get_card;
    }

    public function processOrder($status)
    {
        $this->status_flag = 0;
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $cdkey = [];
        $this->getAPIProvider();
        $this->getConfig();
        $this->getServiceCode();
        $this->getAmount();
        if (!isset($this->publisher_order_id)) {
            $order = $this->createOrder();
            if (!empty($order) && $this->checkError($order)) {
                $cdkey = $this->checkOrder();
            }
        } else {
            $cdkey = $this->checkOrder();
        }
        if (!empty($cdkey) && $this->checkError($cdkey)) {
            $this->status_flag = 1;
            $this->code_string = $cdkey['ListCard'];
        }
    }

    public function createOrder()
    {
        $data = [];

        if ($this->checkMargin()) {
            try {
                $this->command_type = $this->buy_card;
                $this->getRequestData();
                $response = $this->soapRequest();
                $data = $this->parseResponse($response->RequestTransactionResult);
                return $data;
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return $data;
    }

    public function checkOrder()
    {
        $data = [];
        try {
            $this->command_type = $this->get_card;
            $this->getRequestData();
            $response = $this->soapRequest();
            $data = $this->parseResponse($response->RequestTransactionResult);
            return $data;
        } catch (\Exception $e) {
            $this->createException('CD_KEY', $e);
        }
        return $data;
    }

    private function checkError($data)
    {
        if(!$data || !$this->checkResponseFields($data))
        {
            return false;
        }
        switch ($this->command_type) {
            case $this->buy_card:
                try {
                    if ($this->verifyDataSign($data['message'], $data['DataSign'])) {
                        if (!$data['OrgTransID'] == 'OGM' . $this->api_restock_request_id) {
                            $this->error_msg = 'Response Transaction ID does not match. Request Id: OGM' . $this->api_restock_request_id .' Response Id: '.$data['OrgTransID'];
                            $this->error = [
                                'message' => $this->error_msg
                            ];
                            return false;
                        }
                        if ($data['ResponseCode'] !== '1') {
                            $this->error_msg = $this->checkStatusMessage($data['ResponseCode']);
                            $this->error = [
                                'message' => $this->error_msg
                            ];
                            if ($data['ResponseCode'] == '-506') {
                                $this->sendSlack();
                            }
                            return false;
                        }
                        $this->publisher_order_id = $data['VTCTransID'];
                        return true;
                    }
                    return false;
                } catch (\Exception $e) {
                    $this->createException('CHECK_ORDER', $e);
                }
                break;
            case $this->get_card:
                try {
                    if (!$data['OrgTransID'] == 'OGM' . $this->api_restock_request_id) {
                        $this->error_msg = 'Restock Request Id does not match.';
                        $this->error = [
                            'message' => $this->error_msg
                        ];
                        return false;
                    }
                    if ($data['ResponseCode'] !== '1') {
                        $this->error_msg = $this->checkStatusMessage($data['ResponseCode']);
                        $this->error = [
                            'message' => $this->error_msg
                        ];
                        return false;
                    }
                    return true;
                } catch (\Exception $e) {
                    $this->createException('CHECK_ORDER', $e);
                }
                break;
        }
        return false;

    }

    private function checkResponseFields($data){
        foreach($this->response_field[$this->command_type] as $field)
        {
            if(array_key_exists($field, $data))
            {
                if(empty($data[$field]) && $data[$field] != '0')
                {
                    $this->error_msg = 'Response field "'.$field.'" is empty. API Method: '.$this->command_type .' Response: '.json::encode($data);
                    $this->error = [
                        'message' => $this->error_msg
                    ];
                    return false;
                }
            }else{
                $this->error_msg = 'Response field "'.$field.'" is missing. API Method: '.$this->command_type.' Response: '.json::encode($data);
                $this->error = [
                    'message' => $this->error_msg
                ];
                return false;
            }
        }
        return true;
    }

    private function parseCdKey($key)
    {
        $card_code = '';
        $card_serial = '';
        $expired_date = '';
        if ($card_info_array = explode(':', $key)) {
            if(count($card_info_array) != 3){
                $this->error_msg = 'API Returned CDKey has incorrect field count. ';
                $this->error = [
                    'message' => $this->error_msg
                ];
                return false;
            }
            if(!isset($card_info_array[0]) || !isset($card_info_array[1]) || !isset($card_info_array[2]))
            {
                $this->error_msg = 'API Returned CDKey has missing field.';
                $this->error = [
                    'message' => $this->error_msg
                ];
                return false;
            }
            list($card_code, $card_serial, $expired_date) = $card_info_array;
        }
        return sprintf(self::KEY_FORMAT, $card_code, $card_serial, $expired_date);
    }

    private function parseResponse($response)
    {
        $data = [];
        if ($this->command_type == $this->buy_card) {
            $response = explode('|', $response);
            $data = [
                'ResponseCode' => ($response[0] ?? ''),
                'OrgTransID' => ($response[1] ?? ''),
                'VTCTransID' => ($response[2] ?? ''),
                'PartnerBalance' => ($response[3] ?? ''),
                'DataSign' => ($response[4] ?? ''),
            ];
            $message = $data;
            array_pop($message);
            $data['message'] = implode('-', $message);
            unset($message);
        } else if ($this->command_type == $this->get_card) {
            $response = $this->decryptResponse($response);
            $response = explode('|', $response);
            if ($cdkey = $this->parseCdKey(($response[2] ?? ''))) {
                $data = [
                    'ResponseCode' => ($response[0] ?? ''),
                    'OrgTransID' => ($response[1] ?? ''),
                    'ListCard' => $cdkey
                ];
            }else{
                return false;
            }
        }
        return $data;
    }

    private function checkStatusMessage($code)
    {
        switch ($code) {
            case '-55':
                $message = 'The account balance is insufficient to handle this transaction';
                break;
            case '-99':
                $message = 'Unspecified error';
                break;
            case '-290':
                $message = 'Transaction error';
                break;
            case '-302':
                $message = 'Partner does not exist or is on hiatus';
                break;
            case '-305':
                $message = 'Invalid signature';
                break;
            case '-307':
                $message = 'Invalid RequesData';
                break;
            case '-315':
                $message = 'CommandType must be passed';
                break;
            case '-316':
                $message = 'The version must be passed';
                break;
            case '-317':
                $message = 'Invalid number of cards';
                break;
            case '-318':
                $message = 'ServiceCode is incorrect';
                break;
            case '-320':
                $message = 'System interrupt';
                break;
            case '-500':
                $message = 'This card in stock is currently exhausted';
                break;
            case '-501':
                $message = 'Transaction failed';
                break;
            case '-502':
                $message = 'No transactions exist';
                break;
            case '-506':
                $message = 'Transaction already exists';
                break;
            default:
                $message = 'Unspecified error codes';
                break;
        }

        return "($code) " . $message;
    }

    public function getDeno()
    {
        return 0;
    }

    public function getAPIProvider()
    {
        $this->api_provider = explode("_", $this->sku)[0];
        if ($this->api_provider == 'VVTC') {
            $this->api_provider = 'VTC';
        }
        return $this->api_provider;
    }

    private function getServiceCode()
    {
        $this->service_code = explode("_", $this->sku)[1];
    }

    protected function getAmount()
    {
        $skuParts = explode('_', $this->sku);
        $this->amount = 0;
        if (isset($skuParts[2])) {
            $this->amount = $skuParts[2];
        }
    }

    public function getRequestData()
    {
        $request_data = '';
        $Quantity = $this->quantity; // Số lượng
        $ServiceCode = $this->service_code; // Mã dịch vụ
        $Amount = $this->amount; // Giá tiền
        $OrgTransID = 'OGM' . $this->api_restock_request_id; // Mã giao dịch
        $TransDate = date('YmdHis');
        if ($this->command_type == $this->buy_card) {
            $signature = $this->getDataSign($ServiceCode . '-' . $Amount . '-' . $Quantity . '-' . $this->partner_code . '-' . $TransDate . '-' . $OrgTransID);
            $request_data = '<?xml version="1.0" encoding="utf-8"?>
<RequestData xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <ServiceCode>' . $ServiceCode . '</ServiceCode>
    <Amount>' . $Amount . '</Amount>
    <Quantity>' . $Quantity . '</Quantity>
    <TransDate>' . $TransDate . '</TransDate>
    <OrgTransID>' . $OrgTransID . '</OrgTransID>
    <DataSign>' . $signature . '</DataSign>
</RequestData>';
        } else if ($this->command_type == $this->get_card) {
            $ServiceCode = $this->service_code; // Mã dịch vụ
            $Amount = $this->amount; // Giá tiền
            $OrgTransID = $this->publisher_order_id; // Mã giao dịch
            $signature = $this->getDataSign($ServiceCode . '-' . $Amount . '-' . $this->partner_code . '-' . $OrgTransID);
            $request_data = '<?xml version="1.0" encoding="utf-8"?>
<RequestData xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <ServiceCode>' . $ServiceCode . '</ServiceCode>
    <Account>null(empty)</Account>
    <Amount>' . $Amount . '</Amount>
    <TransDate>null(empty)</TransDate>
    <OrgTransID>' . $OrgTransID . '</OrgTransID>
    <DataSign>' . $signature . '</DataSign>
</RequestData>';
        }
        $this->request_data = $request_data;
    }

    private function getDataSign($message)
    {
        $key = (RSA::load($this->private_key))->withPadding(RSA::ENCRYPTION_PKCS1 | RSA::SIGNATURE_PKCS1)->withHash('sha1')->withMGFHash('sha1');
        $data_sign = $key->sign($message);
        return base64_encode($data_sign);
    }

    private function verifyDataSign($message, $data_sign)
    {
        $public = (RSA::load($this->public_key))->withPadding(RSA::ENCRYPTION_PKCS1 | RSA::SIGNATURE_PKCS1)->withHash('sha1')->withMGFHash('sha1');
        return $public->verify($message, base64_decode($data_sign));
    }

    private function decryptResponse($response)
    {
        $tripleDesKey = md5($this->triple_des);
        $tripleDesKey = str_ireplace('-', '', $tripleDesKey);
        $tripleDesKey = strtolower(str_ireplace(' ', '+', $tripleDesKey));
        $tripleDesKey = substr($tripleDesKey, 0, 24);
        $decrypt = new TripleDES('ecb');
        $decrypt->setKey($tripleDesKey);
        return $decrypt->decrypt(base64_decode($response));
    }

    private function soapRequest()
    {
        $response_body = '';
        $status_code = 0;
        $start_time = microtime(true);
        $result = null;
        $exception = null;

        $log_model = new OutgoingRequestLog();
        $log_model->app = Yii::$app->id;
        $log_model->log_level = 1;
        $log_model->request_url = $this->url;
        $log_model->request_method = strtoupper($this->command_type);
        $log_model->tag = $this->getAPIProvider() . '_' . $this->orders_id; //getApiProvider
        $headers = [];
        $body = $this->request_data;

        $log_model->request_header = Json::encode($headers);
        $log_model->request_data = Json::encode($body);
        $log_model->response_body = Json::encode($response_body);
        $log_model->status_code = 0;
        $log_model->save();

        try {
            $client = new \SoapClient($this->url);
            $param['requesData'] = $this->request_data;
            $param['partnerCode'] = $this->partner_code;
            $param['commandType'] = $this->command_type;
            $param['version'] = '1.0';
            $result = $client->__soapCall('RequestTransaction', ['parameters' => $param]);
            $request_body = Json::encode($result);
        } catch (\Exception $e) {
            $request_body = $e->getMessage();
        }

        $log_model->time_consume = (int)((microtime(true) - $start_time) * 1000);
        $log_model->status_code = (int)$status_code;
        $log_model->response_body = Json::encode($request_body);
        $log_model->response_header = Json::encode([]);
        $log_model->save();

        return $result;
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    private function sendSlack($message = '')
    {
        if(!empty($message))
        {
            Yii::$app->slack->send("VTC API DEBUG", [
                [
                    'color' => 'warning',
                    'text' => "Message: ". json::encode($message),
                ],
            ]);
        }else {
            Yii::$app->slack->send("VTC API Error : $this->api_restock_request_id", [
                [
                    'color' => 'warning',
                    'text' => "Error : " . Json::encode($this->error),
                ],
            ]);
        }
    }


}

