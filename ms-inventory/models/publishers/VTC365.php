<?php

namespace micro\models\publishers;

use offgamers\publisher\models\Publisher;

class VTC365 extends \offgamers\publisher\models\profile\VTC
{
    protected $api_provider;

    protected $category_id, $product_id, $denomination;
    const API_BUY_CARD = "Pay/buy-card";
    const API_GET_CARD_DETAILS = "GetInfo/get-carddata";
    const API_CHECK_CARD_DETAILS = "GetInfo/check-partner-order";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->tag = 'VTC_' . $this->orders_id;
        $this->parseSKU();
        $this->getPublisher();
        $this->getConfig();
    }

    public function getOrderUrl()
    {
        return self::API_BUY_CARD;
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                // Order Id Exists
                if ($this->error_code == '-5552') {
                    $this->checkOrder();
                }
                break;

            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == '-5550') {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        if ($this->checkMargin()) {
            try {
                $query_data = [
                    "partnerCode" => $this->partner_code,
                    "categoryID" => $this->category_id,
                    "productID" => $this->product_id,
                    "productAmount" => (string)$this->denomination,
                    "customerID" => "",
                    "partnerTransID" => 'OGM' . $this->api_restock_request_id,
                    "partnerTransDate" => date("YmdHis", time()),
                    // Quantity
                    "data" => "1"
                ];
                $query_data['dataSign'] = $this->generateSignature($query_data);
                $response = $this->sendRequest('POST', static::API_BUY_CARD, $query_data);
                $output = $this->checkError($response);
                if (isset($output['dataInfo'])) {
                    $order_response = $this->decryptResponse($output['dataInfo']);
                    if ($order_response) {
                        $this->parseOrderResponse($order_response);
                    }
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        try {
            // Check Publisher Order ID from Publisher
            if (empty($this->publisher_order_id)) {
                $query_data = [
                    "partnerCode" => $this->configuration_data['PARTNER_CODE'],
                    "categoryID" => '',
                    "productID" => '',
                    "productAmount" => '',
                    "customerID" => "",
                    "partnerTransID" => 'OGM' . $this->api_restock_request_id,
                    "partnerTransDate" => date("YmdHis", time()),
                    "data" => "",
                ];

                $query_data['dataSign'] = $this->generateSignature($query_data);
                $response = $this->sendRequest('POST', static::API_CHECK_CARD_DETAILS, $query_data);
                $output = $this->checkError($response);
                $data = $this->decodeDataInfo($output);
                if (isset($data['OrderID'])) {
                    $this->publisher_order_id = $data['OrderID'];
                }
            }

            // Check Softpin Response from publisher
            if (!empty($this->publisher_order_id)) {
                $query_data = [
                    "partnerCode" => $this->configuration_data['PARTNER_CODE'],
                    "categoryID" => '',
                    "productID" => '',
                    "productAmount" => '',
                    "customerID" => "",
                    "partnerTransID" => 'OGM' . $this->api_restock_request_id,
                    "partnerTransDate" => date("YmdHis", time()),
                    "data" => (string)$this->publisher_order_id,
                ];

                $query_data['dataSign'] = $this->generateSignature($query_data);

                $response = $this->sendRequest('POST', static::API_GET_CARD_DETAILS, $query_data);
                $output = $this->checkError($response);

                if (isset($output['dataInfo'])) {
                    $data = $this->decryptResponse($output['dataInfo']);
                    $this->parseOrderResponse($data);
                }
            }
        } catch (\Exception $e) {
            $this->createException('CHECK_ORDER', $e);
        }
    }

    private function parseOrderResponse($data)
    {
        $checklist = [
            "Code" => "Code",
            "Serial" => "Serial",
            "ExpriredDate" => "Expired Date",
        ];

        $cd_key_data = array();

        if (isset($data['orderID']) && isset($data['ListCard'])) {
            $this->publisher_order_id = (string)$data['orderID'] ?: "";

            foreach ($checklist as $checklist_key => $checklist_value) {
                if (!empty($data['ListCard'][0][$checklist_key])) {
                    if ($checklist_key == "ExpriredDate") {
                        $cd_key_data[$checklist_value] = date("Y-m-d H:i:s", strtotime($data['ListCard'][0][$checklist_key]));;
                    } else {
                        $cd_key_data[$checklist_value] = $data['ListCard'][0][$checklist_key];
                    }
                }
            }

            $code = $this->parseCdKey($cd_key_data);

            if (!empty($code) && !empty($this->publisher_order_id)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                $this->status_flag = 2;
                $this->error_msg = "The Order Does Not Contain Required Parameter .";
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function parseSKU()
    {
        $sku = explode('_', $this->sku);
        if (count($sku) == 4) {
            $this->api_provider = $sku[0];
            if ($this->api_provider == 'VTC365') {
                $this->api_provider = 'VTC';
            } else {
                $this->api_provider = 'SVTC';
            }
            $this->category_id = $sku[1];
            $this->product_id = $sku[2];
            $this->denomination = (float)($sku[3] / 100);
        } else {
            throw new \Exception('Invalid VTC365 SKU Format');
        }
    }

    public function getPublisher()
    {
        $publisher = Publisher::findOne([
            'title' => $this->api_provider,
            'profile' => 'VTC',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        }
    }

    public function reset()
    {
        $this->error_code = null;
        $this->error_msg = '';
        $this->defaultReset();
    }

    public function getAPIProvider()
    {
        return 'VTC';
    }
}