<?php

namespace micro\models\publishers;

use yii\helpers\Json;

class Wogi extends \offgamers\publisher\models\profile\Wogi
{
    const API_GET_ORDER_FUNCTION = "products/issue";
    const API_CHECK_ORDER_FUNCTION = "cards/find/transaction_ref_number";

    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);
        $this->initialize();
    }

    private function initialize()
    {
        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
        $this->getPublisher();
        $this->getExistingConfig();
    }

    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                if ($this->error_code == "vcard.processing.issue.transaction.duplicate") {
                    $this->checkOrder();
                }
                break;
            case 2:
            case 3:
                $this->checkOrder();
                if ($this->error_code == "vcard.management.vcard.find.not.found") {
                    $this->createOrder();
                }
                break;
        }
    }

    public function createOrder()
    {
        $this->min_margin = $this->configuration_data['MIN_MARGIN'];
        $this->low_margin = $this->configuration_data['LOW_MARGIN'];
        //create order
        if ($this->checkMargin()) {
            try {
                $params = [
                    'productId' => $this->getPublisherProductCode(),
                    'amount' => $this->getDeno(),
                    'transactionReferenceNumber' => $this->api_restock_request_id,
                    'issueDate' => time()
                ];
                $response = $this->sendRequest('POST', static::API_GET_ORDER_FUNCTION, $params, $this->getAPIProvider() . '_' . $this->orders_id);
                $data = $this->checkError($response);
                if ($data) {
                    $this->parseOrderResponse($data);
                }
            } catch (\Exception $e) {
                $this->createException('CREATE_ORDER', $e);
            }
        }
        return true;
    }

    public function checkOrder()
    {
        //check order
        try {
            $response = $this->sendRequest('GET', static::API_CHECK_ORDER_FUNCTION . "/" . $this->api_restock_request_id, null, $this->getAPIProvider() . '_' . $this->orders_id);
            $data = $this->checkError($response);
            if ($data) {
                $this->parseOrderResponse($data);
            }
        } catch (\Exception $e) {
            $this->createException('CREATE_ORDER', $e);
        }
        return true;
    }

    private function parseOrderResponse($data)
    {
        $checklist = ["voucherLink" => "Voucher Link", "expiryDate" => "Expiry Date"];
        $cdkey_data = array();

        if (isset($data['virtualCard']['vCardId']) && isset($data['virtualCard']['voucherLink'])) {
            $this->publisher_order_id = (string)$data['virtualCard']['vCardId'];

            foreach ($checklist as $checklist_key => $checklist_value) {
                if (isset($data['virtualCard'][$checklist_key])) {
                    if ($checklist_key == 'expiryDate' && strtotime($data['virtualCard']['expiryDate']) !== false) {
                        $cdkey_data[$checklist_value] = date('Y-m-d', strtotime($data['virtualCard'][$checklist_key]));
                    } else {
                        $cdkey_data[$checklist_value] = $data['virtualCard'][$checklist_key];
                    }
                }
            }

            $code = $this->parseCdKey($cdkey_data);
            if (!empty($code)) {
                $this->code_string = $code;
                $this->status_flag = 1;
            } else {
                //slack error
                $this->status_flag = 2;
            }
        } else {
            $this->createException('CDKEY', null);
        }
    }

    private function parseCdKey($pinInfo)
    {
        $code = [];
        foreach ($pinInfo as $key => $value) {
            $code[] = $key . ' : ' . $value;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    public function getDeno()
    {
        if (strpos($this->sku, '_') === false) {
            return $this->sku;
        } else {
            $parts = explode('_', $this->sku);
            if (isset($parts[3])) {
                return intval((int)$parts[3] / 100);
            } else {
                throw new InvalidArgumentException('Invalid Deno');
            }
        }
    }

    public function reset()
    {
        $this->error_code = null;
        $this->defaultReset();
    }

    public function getApiClassName()
    {
        return 'Wogi';
    }

    public function getAPIProvider()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);
        return 'WOGI_' . $region;
    }

    public function getOrderUrl()
    {
        return static::API_GET_ORDER_FUNCTION;
    }

    public function getPublisherProductCode()
    {
        if (strpos($this->sku, '_') === false) {
            return $this->sku;
        } else {
            $parts = explode('_', $this->sku);
            if (isset($parts[2])) {
                return (int)$parts[2];
            } else {
                throw new InvalidArgumentException('Invalid Products Codes');
            }
        }
    }
}