<?php

namespace micro\models\publishers;

use Exception;
use offgamers\publisher\models\Publisher;
use Psr\Http\Message\ResponseInterface;
use yii\base\InvalidArgumentException;
use yii\db\BaseActiveRecord;
use yii\helpers\Json;

class Xoxo extends \offgamers\publisher\models\profile\Xoxo
{
    use RestockPublisherTrait {
        reset as defaultReset;
    }

    public function load($data, $formName = null)
    {
        parent::load($data, $formName);

        $this->retry_attempt = 0;

        if (empty($this->sku)) {
            throw new InvalidArgumentException('Missing Xoxo SKU.');
        }

        if (count(explode("_", $this->sku)) !== 4) {
            throw new InvalidArgumentException(
                'Invalid Xoxo SKU. Must be in XOXO_{CURRENCY}_{PRODUCT_ID}_{DENO} format.'
            );
        }

        if (!is_int($this->getProductId())) {
            throw new InvalidArgumentException(
                'Product Id must be an integer. ' . $this->getProductId() . ' given.'
            );
        }

        if (!is_int($this->getDeno())) {
            throw new InvalidArgumentException(
                'Denomination must be an integer. ' . $this->getDeno() . ' given.'
            );
        }

        $this->settlement_amount = $this->product_cost;
        $this->settlement_currency = $this->product_cost_currency;
    }

    /**
     * @throws Exception
     */
    public function processOrder($status)
    {
        switch ($status) {
            case 0:
                $this->createOrder();
                break;
            case 2:
                $this->checkOrder();
                break;
            case 3:
                // This case is used to call get-order-details API via backend only
                $this->loadConfig();
                $orderResponse = $this->getOrder();
                $this->handleOrderResponse($orderResponse, 'getOrderDetails');
        }
    }

    /**
     * Load Xoxo publisher settings
     * @throws Exception
     */
    private function loadConfig(): void
    {
        // Get xoxo publisher settings
        $publisher_name = 'Xoxo ' . $this->getRegion();
        $publisher = Publisher::findOne([
            'title' => $publisher_name,
            'profile' => 'Xoxo'
        ]);

        // Publisher exists?
        if (!$publisher instanceof BaseActiveRecord) {
            $this->setStatusFlagAndThrowError(
                "Missing the Xoxo publisher in crew2. Please ensure that the title is Xoxo & profile is set to Xoxo"
            );
        }

        // Publisher active?
        if ($publisher->status != 1) {
            $this->setStatusFlagAndThrowError(
                "Please activate Xoxo publisher in crew2."
            );
        }

        $this->publisher_id = $publisher->publisher_id;
        $this->initClient();
        $this->getConfig();
        $this->getToken();

        if (empty($this->base_url)) {
            $this->setStatusFlagAndThrowError(
                "Please set the Base API URL for Xoxo publisher in crew2."
            );
        } else {
            if (filter_var($this->base_url, FILTER_VALIDATE_URL) === false) {
                $this->setStatusFlagAndThrowError(
                    "Please ensure the Base API URL is valid for Xoxo publisher in crew2."
                );
            }
        }
    }

    /**
     * @throws Exception
     */
    private function createOrder(): void
    {
        if ($this->checkMargin()) {
            $this->loadConfig();
            $this->placeOrder();
        }
    }

    /**
     * @throws Exception
     */
    private function checkOrder(): void
    {
        if ($this->checkMargin()) {
            $this->loadConfig();

            $orderResponse = $this->getOrder();

            // Order in xoxo given poNumber does not exists
            if ($orderResponse === false) {
                $this->placeOrder();
            } else {
                $this->handleOrderResponse($orderResponse, 'getOrderDetails');
            }
        }
    }

    /**
     * @throws Exception
     */
    private function placeOrder(): void
    {
        try {
            $params = [
                "productId" => $this->getProductId(),
                "quantity" => 1,
                "denomination" => $this->getDeno(),
                "poNumber" => $this->getPoNumber()
            ];

            $response = $this->sendCheckOrPlaceOrderRequest($params, 'placeOrder');
            $this->handleOrderResponse($response, 'placeOrder');
        } catch (Exception $exception) {
            $status = $this->status_flag == -1 ? $this->status_flag : 2;
            $this->setStatusFlagAndThrowError(
                "Issue with place order due to {$exception->getMessage()}",
                $status
            );
        }
    }

    /**
     * @throws Exception
     */
    private function handleOrderResponse(?ResponseInterface $response, string $key): void
    {
        $this->handleGeneralResponseError($response);

        if ($response->getStatusCode() != 200) {
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Unsuccessful response to place order from Xoxo"
            );
        }

        $responseArray = $this->parseResponse($response);

        $status = $responseArray['data'][$key]['status'] ?? null;
        if ($status != 1) {
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Unsuccessful response to place order from Xoxo"
            );
        }

        $order_id = $responseArray['data'][$key]['data']['orderId'] ?? null;
        if (is_null($order_id)) {
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Order Id missing from place order response from Xoxo"
            );
        }

        $this->publisher_order_id = strval($order_id);

        $order_status = $responseArray['data'][$key]['data']['orderStatus'] ?? null;
        if ($order_status != 'complete') {
            $this->status_flag = -1;
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Order status is not complete for Xoxo order id : {$this->publisher_order_id}"
            );
        }

        $delivery_status = $responseArray['data'][$key]['data']['deliveryStatus'] ?? null;
        if (is_null($delivery_status)) {
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Delivery status is missing from create order response from Xoxo for Xoxo order id : {$this->publisher_order_id}"
            );
        }

        if ($delivery_status != 'delivered') {
            $this->addErrorsForApiResponseAndThrowException(
                $response,
                "Delivery status is $delivery_status for Xoxo order id : {$this->publisher_order_id}"
            );
        }

        $vouchers = $responseArray['data'][$key]['data']['vouchers'] ?? null;
        if (empty($vouchers)) {
            throw new Exception(
                "No voucher found for Xoxo order id : {$this->publisher_order_id}"
            );
        }

        try {
            // Delivered case
            $code = $this->parseVoucher($vouchers);
            if (!empty($code)) {
                $this->status_flag = 1;
                $this->code_type = 'soft';
                $this->code_string = $code;
            }
        } catch (Exception $exception) {
            $message = "Issue while processing voucher code for Xoxo due to {$exception->getMessage()} " .
                "for Xoxo order id :[{$this->publisher_order_id}]";

            $this->addErrorsForApiResponseAndThrowException($response, $message);
        }
    }

    /**
     * @param array $vouchers
     * @return string
     * @throws Exception
     */
    private function parseVoucher(array $vouchers): string
    {
        $voucher = $vouchers[0];

        $type = $voucher['type'] ?? null;
        if (is_null($type)) {
            throw new Exception("No type found for voucher");
        }

        $code = '';

        switch ($type) {
            case 'code':
                $code = $this->constructCode($voucher, ['voucherCode'], $type);
                break;
            case 'url':
            case 'codePin':
            case 'urlPin':
                $code = $this->constructCode($voucher, ['pin', 'voucherCode'], $type);
                break;
        }

        return $code;
    }

    /**
     * @throws Exception
     */
    private function constructCode(array $voucherData, array $required_properties, string $type): string
    {
        $properties_label = [
            'voucherCode' => ($type == 'url' || $type == 'urlPin') ? 'Url' : 'Code',
            'pin' => 'Pin',
        ];
        $code = [];

        foreach ($required_properties as $property) {
            if (empty($voucherData[$property])) {
                if (in_array($type, array("url")) && $property == "pin") {
                    // Not sure if there is a case when type is "url" "pin" is not given
                } else {
                    throw new Exception("No $property found for voucher type : [$type] ");
                }
                continue;
            }

            $label = $properties_label[$property];
            $code[] = "$label : " . $voucherData[$property];
        }

        $validity = $voucherData['validity'] ?? null;
        if (!empty($validity)) {
            $code[] = "Validity : " . $validity;
        }

        return implode('<br>', $code);
    }

    /**
     * @return false|ResponseInterface
     */
    private function getOrder()
    {
        try {
            $params = [
                "poNumber" => $this->getPoNumber()
            ];
            $response = $this->sendCheckOrPlaceOrderRequest($params, 'getOrderDetails');

            $this->handleCheckOrderResponse($response);

            return $response;
        } catch (Exception $exception) {
            return false;
        }
    }

    private function parseResponse(ResponseInterface $response): array
    {
        return Json::decode($response->getBody());
    }

    private function sendCheckOrPlaceOrderRequest(array $request_params, string $request_key): ?ResponseInterface
    {
        $params = [
            "query" => "plumProAPI.mutation.$request_key",
            "tag" => "plumProAPI",
            "variables" => [
                "data" => $request_params
            ]
        ];

        return $this->sendRequest("POST", self::API_PATH, $this->getHeaders(), $params);
    }

    /**
     * @throws Exception
     */
    private function setStatusFlagAndThrowError(
        string $error_message,
        int $status = 2
    ): void {
        $this->status_flag = $status;
        if (empty($this->error)) {
            $this->setErrors($error_message);
        }
        $this->createException('CREATE_ORDER');
    }

    /**
     * @throws Exception
     */
    private function handleCheckOrderResponse(?ResponseInterface $response): void
    {
        $this->handleGeneralResponseError($response);

        if ($response->getStatusCode() != 200) {
            throw new Exception("Order not found");
        }
    }

    /**
     * @throws Exception
     */
    private function handleGeneralResponseError(?ResponseInterface $response): void
    {
        if (!$response instanceof ResponseInterface) {
            throw new Exception("No response from Xoxo");
        }
    }

    /**
     * @throws Exception
     */
    private function addErrorsForApiResponseAndThrowException(ResponseInterface $response, string $message): void
    {
        if ($data = Json::decode($response->getBody())) {
            if (isset($data['errorId']) && isset($data['errorInfo'])) {
                $error_id = $data['errorId'];
                $error_msg = $data['errorInfo'];
                $message = $error_id . ' : ' . $error_msg;
            } elseif (isset($data['error_description'])) {
                $message = $data['error_description'];
            }
        }
        $this->setErrors($message, (string)$response->getBody(), $response->getStatusCode());
        throw new Exception($message);
    }

    private function setErrors(string $message, ?string $response = null, ?int $status_code = null): void
    {
        $this->error_msg = $message;
        $this->error = [
            'message' => $this->error_msg,
        ];

        if (!is_null($response)) {
            $this->error['response'] = $response;
        }

        if (!is_null($status_code)) {
            $this->error['http_status'] = $status_code;
        }
    }

    private function getPoNumber(): string
    {
        return "OG_" . $this->api_restock_request_id;
    }

    public function getOrderUrl()
    {
        return 'xoxo/order';
    }

    public function getRegion()
    {
        return explode("_", $this->sku)[1];
    }

    private function getProductId(): int
    {
        return intval(explode("_", $this->sku)[2]);
    }

    public function getDeno()
    {
        return intval(explode("_", $this->sku)[3]);
    }

    public function getApiClassName()
    {
        return 'Xoxo';
    }

    public function getAPIProvider()
    {
        $region = strtoupper($this->getRegion());
        return 'XOXO_' . $region;
    }

    public function reset()
    {
        $this->defaultReset();
    }
}
