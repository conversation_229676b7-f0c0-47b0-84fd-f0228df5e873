<?php

namespace micro\components;

use Yii;
use yii\helpers\HtmlPurifier;

class Helper
{
    public static function purify($data)
    {
        if (is_array($data)) {
            return array_map([get_called_class(), 'purify'], $data);
        }

        return strip_tags(HtmlPurifier::process($data));
    }

    public static function contains($haystack, $needle, $toLowerCase = 0, $matched = 'all')
    {
        if ($toLowerCase) {
            $haystack = strtolower($haystack);
        }

        if (is_array($needle)) {
            foreach ($needle as $n) {
                if ($toLowerCase) {
                    $n = strtolower($n);
                }

                if ($matched == 'all' && strpos($haystack, $n) === false) {
                    return false;
                }

                if ($matched == 'any' && strpos($haystack, $n) !== false) {
                    return true;
                }
            }

            return $matched == 'all';
        }

        if ($toLowerCase) {
            $needle = strtolower($needle);
        }

        if (strpos($haystack, $needle) !== false) {
            return true;
        }

        return false;
    }

    public static function resp($status, $message, $data = [])
    {
        if (!in_array($status, [200, 301])) {
            Yii::$app->response->statusCode = $status;
        }

        return [
            'status' => $status,
            'message' => $message,
            'data' => $data,
        ];
    }

    public static function log($title, $message, $stop = 0)
    {
        $data = [
            'title' => $title,
            'message' => $message
        ];

        Yii::error($data);

        if ($stop) {
            echo "<pre>" . print_r($data, true) . "</pre>";
            Yii::$app->end();
        }
    }

    public static function dateDifferent($startDate, $endDate = null)
    {
        if (!$endDate) {
            $endDate = self::now();
        }
        return strtotime($endDate) - strtotime($startDate);
    }

    public static function now($operator = null, $seconds = null)
    {
        $time = time();
        if ($operator && $seconds) {
            switch ($operator) {
                case "+":
                    $time += $seconds;
                    break;
                case "-":
                    $time -= $seconds;
                    break;
            }

        }
        return date('Y-m-d H:i:s', $time);
    }

    public static function createRandomValue($length, $type = 'mixed')
    {
        if (($type != 'mixed') && ($type != 'chars') && ($type != 'digits')) {
            return false;
        }

        $rand_value = '';
        while (strlen($rand_value) < $length) {
            if ($type == 'digits') {
                $char = self::cpnRand(0, 9);
            } else {
                $char = chr(self::cpnRand(0, 255));
            }

            if ($type == 'mixed') {
                if (preg_match('/^[a-z0-9]$/i', $char)) {
                    $rand_value .= $char;
                }
            } else {
                if ($type == 'chars') {
                    if (preg_match('/^[a-z]$/i', $char)) {
                        $rand_value .= $char;
                    }
                } else {
                    if ($type == 'digits') {
                        if (preg_match('/^[0-9]$/', $char)) {
                            $rand_value .= $char;
                        }
                    }
                }
            }
        }

        return $rand_value;
    }

    public static function cpnRand($min = null, $max = null)
    {
        static $seeded;

        if (!isset($seeded)) {
            mt_srand((double)microtime() * 1000000);
            $seeded = true;
        }

        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                return $min;
            } else {
                return mt_rand($min, $max);
            }
        } else {
            return mt_rand();
        }
    }

    public static function isExpired($date)
    {
        return strtotime($date) < time();
    }

    public static function encrypt($string, $key)
    {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result .= $char;
        }

        return base64_encode($result);
    }

    public static function decrypt($string, $key)
    {
        $result = '';
        $string = base64_decode($string);

        if ($key != '') {
            for ($i = 0; $i < strlen($string); $i++) {
                $char = substr($string, $i, 1);
                $keychar = substr($key, ($i % strlen($key)) - 1, 1);
                $char = chr(ord($char) - ord($keychar));
                $result .= $char;
            }
        }

        return $result;
    }
}