<?php

use micro\components\IncomingLogQueue;
use micro\components\OutgoingLogQueue;

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php')
);

return [
    'id' => 'ms-log',

    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log','outgoing_log_queue', 'incoming_log_queue'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'outgoing_log_queue' => [
            'class' => OutgoingLogQueue::class,
            'queue_name' => 'OUTGOING_LOG_QUEUE',
            'attempts' => 3,
        ],
        'incoming_log_queue' => [
            'class' => IncomingLogQueue::class,
            'queue_name' => 'INCOMING_LOG_QUEUE',
            'attempts' => 3,
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'DEBUG' => $params['slack.webhook.debug'],
            ]
        ],
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ]
    ]
];


