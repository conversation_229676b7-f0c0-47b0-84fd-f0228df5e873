<?php

namespace micro\controllers;

use offgamers\base\models\SqsFailover;
use Yii;
use yii\console\Controller;
use yii\helpers\Json;

class SqsController extends Controller
{
    public function actionFailOver()
    {
        $aws = Yii::$app->aws;

        SqsFailover::find()->batch(500, function ($failovers) use ($aws) {
            foreach ($failovers as $failover) {
                $sqs = $aws->getSQS($failover->sqs_tag);
                $sqs->pushMessage(Json::decode($failover->data, 1));

                $failover->delete();
            }
        });


    }
}
