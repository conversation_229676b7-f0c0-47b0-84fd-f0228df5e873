<?php
$config = [
    'components' => [
        'db_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => '',
            'username' => '',
            'password' => '',
            'charset' => 'utf8mb4',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "",
            'servers' => [
                [
                    'host' => '',
                    'port' => 11211,
                    'weight' => 50,
                ],
            ],
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => '',
            'secret' => '',
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_TOOLBOX' => [
                    // Optional: Remove to use default
                    'key' => '',
                    'secret' => '',
                    'region' => 'us-east-1',
                    'prefix_path' => '',

                    // Required
                    'bucket_key' => '',
                    'acl' => 'private',
                    'storage' => 'STANDARD',
                ],
            ],
            'sqs' => [
                'OUTGOING_LOG_QUEUE' => [
                    'queue_url' => '',
                ],
                'INCOMING_LOG_QUEUE' => [
                    'queue_url' => '',
                ],
            ],
        ],
    ],
];

return $config;