<?php

namespace micro\interfaces;

use offgamers\base\components\ElasticSearch;
use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;

class Base<PERSON>ogJob extends BaseObject implements JobInterface
{
    public $data;
    public $type;
    public $start_time;
    public $index = 'incoming_log';

    public function execute($queue)
    {
        if ($this->type == 's3') {
            $s3_obj = Yii::$app->aws->getS3('BUCKET_TOOLBOX');
            $path = $this->data;
            $this->data = json_decode($s3_obj->getContent($path), 1);
            $s3_obj->deleteContent($path);
        }

        ElasticSearch::store($this->index, $this->data);
    }
}