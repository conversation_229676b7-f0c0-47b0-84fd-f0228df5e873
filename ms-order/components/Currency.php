<?php

namespace micro\components;

class Currency extends \offgamers\base\components\Currency
{
    public function getIdByCode($cur_code)
    {
        return array_search($cur_code, $this->internal_currency);
    }

    public function parseStringToNumber($currency, $value){
        $value = str_replace($this->currency_list[$currency]['symbol_left'], '', $value);
        $value = str_replace($this->currency_list[$currency]['symbol_right'], '', $value);
        $value = trim(strip_tags($value));
        $value = str_replace($this->currency_list[$currency]['thousands_point'], '', $value);
        return (float) str_replace($this->currency_list[$currency]['decimal_point'], '.', $value);
    }
}