<?php

namespace micro\components;

use Yii;
use yii\db\Query;
use yii\web\BadRequestHttpException;

class Customer
{
    public static function getOrderCustomerInfo($customer_id)
    {
        $customer = self::getCustomerInfo($customer_id);
        if ($customer) {
            $ab_info = self::getCustomersDefaultAddressBookInfo($customer_id, $customer['customers_default_address_id']);
            $phone_country_info = self::getCustomersCountriesInfo($customer['customers_country_dialing_code_id']);

            $country_info = null;
            if ($ab_info) {
                $country_info = self::getCustomersCountriesInfo($ab_info['entry_country_id']);
            }

            $telephone = '';
            if (!empty($customer['customers_telephone']) && !empty($customer['customers_country_dialing_code_id']) && $phone_country_info) {
                $telephone = self::parseTelephone($customer['customers_telephone'], $customer['customers_country_dialing_code_id'], $phone_country_info['countries_international_dialing_code']);
            }

            return [
                'customers_id' => $customer_id,
                'first_name' => $customer['customers_firstname'],
                'last_name' => $customer['customers_lastname'],
                'email' => $customer['customers_email_address'],
                'group_id' => $customer['customers_groups_id'],
                'company' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_company'] : ''),
                'street_address' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_street_address'] : ''),
                'suburb' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_suburb'] : ''),
                'city' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_city'] : ''),
                'state' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_state'] : ''),
                'postcode' => StringHelper::sanitizeUtf8String($ab_info ? $ab_info['entry_postcode'] : ''),
                'format_id' => ($phone_country_info ? $phone_country_info['address_format_id'] : ''),
                'country' => ($country_info ? $country_info['countries_name'] : ''),
                'phone_country' => ($phone_country_info ? $phone_country_info['countries_name'] : ''),
                'country_dialing_code_id' => ($phone_country_info ? $phone_country_info['countries_international_dialing_code'] : ''),
                'telephone' => $telephone,
            ];

        } else {
            throw new BadRequestHttpException('ERROR_INVALID_CUSTOMERS_ID');
        }
    }

    public static function getCustomerInfo($customer_id)
    {
        $customers_info = (new Query())
            ->select([
                'c.customers_id',
                'c.customers_firstname',
                'c.customers_lastname',
                'c.customers_email_address',
                'c.customers_default_address_id',
                'c.customers_country_dialing_code_id',
                'c.customers_telephone',
                'c.customers_groups_id',
            ])
            ->from('customers c')
            ->where(['c.customers_id' => $customer_id])
            ->limit(1)
            ->one(Yii::$app->db_offgamers);

        return $customers_info;
    }

    public static function getCustomersDefaultAddressBookInfo($customer_id, $address_book_id)
    {
        $address_book = (new Query())
            ->select([
                'ab.entry_company',
                'ab.entry_firstname',
                'ab.entry_lastname',
                'ab.entry_street_address',
                'ab.entry_suburb',
                'ab.entry_postcode',
                'ab.entry_city',
                'ab.entry_state',
                'ab.entry_country_id',
                'ab.entry_zone_id'
            ])
            ->from('address_book ab')
            ->where(['ab.customers_id' => $customer_id, 'ab.address_book_id' => $address_book_id])
            ->limit(1)
            ->one(Yii::$app->db_offgamers);

        return $address_book;
    }

    public static function getCustomersCountriesInfo($countries_id)
    {
        $return_arr = [
            'countries_id' => '',
            'countries_name' => '',
            'countries_iso_code_2' => '',
            'countries_iso_code_3' => '',
            'address_format_id' => 0,
            'countries_international_dialing_code' => '',
        ];

        $countries_info = (new Query())
            ->select([
                'c.countries_id',
                'c.countries_name',
                'c.countries_iso_code_2',
                'c.countries_iso_code_3',
                'c.address_format_id',
                'c.countries_international_dialing_code',
            ])
            ->from('countries c')
            ->where(['c.countries_id' => $countries_id])
            ->limit(1)
            ->one(Yii::$app->db_offgamers);

        if ($countries_info) {
            $return_arr = $countries_info;
        }

        return $return_arr;
    }

    public static function getCustomersZonesInfo($zone_id)
    {
        $zones_info = (new Query())
            ->select([
                'z.zone_name'
            ])
            ->from('zones z')
            ->where(['z.zone_id' => $zone_id])
            ->limit(1)
            ->one(Yii::$app->db_offgamers);

        return $zones_info;
    }

    public static function parseTelephone($telephone, $country_id, $country_code)
    {
        $telephone = preg_replace('/[^\d]/', '', $telephone);
        $extra_reg_rule = '';

        // Italy (Fixed line has one elading zero but Mobile does not have)
        if ($country_id == 105) {
            $extra_reg_rule = '(?:0)';
        }

        $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);

        if ($country_code != '') {
            while (strlen($telephone) > 10) {
                if (preg_match('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', $telephone)) {
                    $telephone = preg_replace('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', '$2', $telephone);
                    $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);
                } else {
                    break;
                }
            }
        }

        // Italy (Fixed line has one elading zero but Mobile does not have)
        if ($country_id == 105) {
            if (substr($telephone, 0, 2) == '03') { // Mobile number
                $telephone = substr($telephone, 1);
            }
        }

        return $telephone;
    }
}