<?php

namespace micro\components;

use micro\exceptions\QueueRetryableException;
use yii\queue\ExecEvent;

class G2gOrderQueue extends \offgamers\base\components\SQSQueue
{
    public $job_class = '\micro\interfaces\G2gOrderJob';

    public function handleError(ExecEvent $event)
    {
        // Do nothing when failed to obtain lock and proceed for retries
        if($event->error instanceof QueueRetryableException){
            return false;
        }

        return parent::handleError($event);
    }
}