<?php

namespace micro\components;

use micro\models\Countries;
use micro\models\CustomersRemarksHistory;
use yii\db\Expression;

class LogFilesComponent
{

    var $identity;
    var $table = 'log_table';

    public function __construct($identity)
    {
        $this->identity = $identity;
    }

    public function detectChanges($oldData, $newData)
    {
        $changesArray = array();
        if (count($oldData) && is_array($oldData) && count($newData) && is_array($newData)) {
            foreach ($oldData as $key => $value) {
                if (strcmp($newData[$key], $value) !== 0) {
                    $changesArray[$key] = array('from' => $value, 'to' => $newData[$key]);
                }
            }
        }
        return $changesArray;
    }

    public function constructLogMessage($changesArray)
    {
        $messageStr = array();
        if (count($changesArray)) {
            foreach ($changesArray as $key => $changes) {
                $readableArray = $this->getReadableLogInput($key, $changes['from'], $changes['to']);
                if (count($readableArray)) {
                    $messageStr[] = $readableArray;
                }
            }
        }

        return $messageStr;
    }

    public function getReadableLogInput($fieldName, $oldVal, $newVal)
    {
        $plainResult = false;

        $result = array();
        $oldVal = trim($oldVal);
        $newVal = trim($newVal);
        switch ($fieldName) {
            case 'customers_gender':
                if (!empty($oldVal)) {
                    $oldString = ($oldVal == 'm') ? 'Male' : 'Female';
                } else {
                    $oldString = 'EMPTY';
                }
                $newString = ($newVal == 'm') ? 'Male' : 'Female';
                $text = 'Gender';
                break;
            case 'customers_phone_verified':
                $oldString = ((int)$oldVal == '1') ? 'Verify' : 'Unverify';
                $newString = ((int)$newVal == '1') ? 'Verify' : 'Unverify';
                $text = 'Phone Verification';
                break;
            case 'entry_country_id':
                $country = Countries::getCountryName($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = Countries::getCountryName($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Country';
                break;
            case 'customers_country_dialing_code_id':
                $country = Countries::getCountryName($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = Countries::getCountryName($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Location';
                break;
            default:
                $displayLabel = array(
                    'customers_firstname' => 'First Name',
                    'customers_lastname' => 'Last Name',
                    'customers_email_address' => 'E-Mail Address',
                    'customers_telephone' => 'Telephone Number',
                    'customers_fax' => 'Fax Number',
                    'customers_discount' => 'Customer Discount Rate',
                    'customers_newsletter' => 'Newsletter',
                    'customers_phone_verified' => 'Phone Verification',
                    'customers_phone_verified_datetime' => 'Phone Verification Date',
                    'entry_street_address' => 'Street Address',
                    'entry_postcode' => 'Post Code',
                    'entry_city' => 'City',
                    'entry_company' => 'Company',
                    'entry_suburb' => 'Suburb',
                    'entry_state' => 'State',
                    'char_wow_account' => 'WOW Account Name',
                    'char_account_name' => 'Account Name',
                    'char_account_pwd' => 'Account Password',
                    'char_name' => 'Character Name',
                    'delivery_mode' => 'Delivery Mode'
                );
                $oldString = (trim($oldVal) != '') ? $oldVal : "EMPTY";
                $newString = (trim($newVal) != '') ? $newVal : "EMPTY";
                $text = isset($displayLabel[$fieldName]) ? $displayLabel[$fieldName] : $fieldName;
                break;
        }
        $result[$fieldName] = array('text' => $text, 'from' => $oldString, 'to' => $newString, 'plain_result' => ($plainResult ? '1' : '0'));
        return $result;
    }

    public function insertCustomerHistoryLog($customers_id, $updated_by, $remark)
    {
        $m_attr = array(
            "customers_id" => $customers_id,
            "date_remarks_added" => new Expression('NOW()'),
            "remarks" => $remark,
            "remarks_added_by" => $updated_by
        );
        $model = new CustomersRemarksHistory;
        $model->load($m_attr, '');
        $model->save();
    }

    public function contructChangesString($customerChangesArray, $allCustomersInfoChangesMade)
    {
        if (gettype($customerChangesArray) == "array" && sizeof($customerChangesArray) > 0) {
            foreach ($customerChangesArray as $field => $res) {
                if (!empty($allCustomersInfoChangesMade)) {
                    if (!preg_match('/(##)?(' . $field . ')(##)?/', $allCustomersInfoChangesMade)) {
                        $allCustomersInfoChangesMade .= "##" . $field;
                    }
                } else {
                    $allCustomersInfoChangesMade .= $field;
                }
            }
        }

        while (strstr($allCustomersInfoChangesMade, '####')) {
            $allCustomersInfoChangesMade = str_replace('####', '##', $allCustomersInfoChangesMade);
        }

        if (preg_match('/^##/', $allCustomersInfoChangesMade)) {
            $allCustomersInfoChangesMade = substr($allCustomersInfoChangesMade, 2);
        }

        if (preg_match('/##$/', $allCustomersInfoChangesMade)) {
            $allCustomersInfoChangesMade = substr($allCustomersInfoChangesMade, 0, -2);
        }

        return $allCustomersInfoChangesMade;
    }

}