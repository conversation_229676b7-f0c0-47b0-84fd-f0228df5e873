<?php

namespace micro\components;

use micro\models\CouponRedeemTrack;
use micro\models\Coupons;
use micro\models\CronGenesisOrders;
use micro\models\OrdersLogTable;
use offgamers\base\models\ms\Product;
use Yii;
use micro\models\Orders;
use yii\base\Exception;
use yii\db\Expression;
use micro\models\AnalysisPgInfo;
use micro\models\CronOrdersPaymentStatus;
use micro\models\CustomersTopUpInfo;
use micro\models\OrdersCustomProducts;
use micro\models\OrdersDeliveryAddress;
use micro\models\OrdersExtraInfo;
use micro\models\OrdersNotification;
use micro\models\OrdersProducts;
use micro\models\OrdersProductsExtraInfo;
use micro\models\OrdersStatusHistory;
use micro\models\OrdersStatusStat;
use micro\models\OrdersTaxConfiguration;
use micro\models\OrdersTaxConfigurationDescription;
use micro\models\OrdersTotal;
use micro\models\PipwavePaymentMapper;
use micro\models\TempProcess;
use micro\models\TopUpInfo;

class Order
{
    const CHANGE_BY = 'OGM SYSTEM';

    public static function moveOrder($orders_id, $currency, $paid_amount, $rules_action = 'NotRequire')
    {
        $ot_total = OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_total']);
        $amount = '';

        if (isset(Yii::$app->currency->currency_list[$currency])) {
            $amount = Yii::$app->currency->currency_list[$currency]['symbol_left'] . number_format(
                    $paid_amount,
                    Yii::$app->currency->currency_list[$currency]['decimal_places'],
                    Yii::$app->currency->currency_list[$currency]['decimal_point'],
                    Yii::$app->currency->currency_list[$currency]['thousands_point']
                ) . Yii::$app->currency->currency_list[$currency]['symbol_right'];
        }

        if ($ot_total && $amount == strip_tags($ot_total->text)) {
            $order_updated = Orders::updateAll(['orders_aft_executed' => 0, 'orders_status' => 7, 'last_modified' => (new Expression('NOW()'))], ['orders_id' => $orders_id, 'orders_status' => 1]);

            if ($order_updated) {
                // order status log
                $oss_data = [
                    'orders_id' => $orders_id,
                    'orders_status_id' => 7,
                    'customer_notified' => 1,
                    'comments' => '',
                    'date_added' => new Expression('NOW()'),
                    'changed_by' => self::CHANGE_BY,
                ];

                Order::updateOrdersStatusCounter($oss_data);
                OrdersStatusHistory::insertHistory($oss_data);

                try {
                    self::stockMovement($orders_id);
                } catch (Exception $e) {
                    Yii::$app->slack->send('Failed to deduct available Quantity ' . $orders_id, array(
                        array(
                            'color' => 'warning',
                            'text' => $e->getTraceAsString(),
                        ),
                    ));
                }

                if ($rules_action) {
                    self::runOrderGenesis($orders_id);
                }
            } else {
                $order_status = Orders::find()->select(['orders_status'])->where(['orders_id' => $orders_id])->scalar();
                if ($order_status == "7" && $rules_action != "" && $rules_action != "NotRequire") {
                    self::runOrderGenesis($orders_id);
                }
            }
        }
    }

    public static function runOrderGenesis($orders_id)
    {
        $genesis_row = CronGenesisOrders::find()->where(['orders_id' => $orders_id])->exists();

        if ($genesis_row === false) {
            $genesis_model = new CronGenesisOrders();
            $genesis_model->load([
                'orders_id' => $orders_id,
                'flag' => 0,
                'last_modified' => new Expression('NOW()'),
            ], '');

            $genesis_model->save();

            $m_attr = [
                'orders_locked_by' => '0',
                'orders_locked_datetime' => new Expression('NOW()'),
            ];

            Orders::updateAll($m_attr, ['orders_id' => $orders_id]);

            $m_attr = [
                'page_name' => 'OGM_ORDER_RUN_GENESIS',
                'match_case' => $orders_id,
                'created_date' => new Expression('NOW()'),
                'extra_info' => json_encode([
                    'locked_by' => 0,
                    'locked_ip' => '',
                ]),
            ];

            $temp_process = new TempProcess();
            $temp_process->load($m_attr, '');
            $temp_process->save();

            $m_attr = [
                'orders_log_admin_id' => '0',
                'orders_log_ip' => '',
                'orders_log_time' => new Expression('NOW()'),
                'orders_log_orders_id' => $orders_id,
                'orders_log_system_messages' => '##l_1##',
                'orders_log_filename' => 'orders.php',
            ];

            $log_model = new OrdersLogTable;
            $log_model->load($m_attr, '');
            $log_model->save();
        }
    }

    public static function customerInfoVerification($orders_id, $customers_id, $full_name, $email, $payment_methods_id, $payment_status)
    {
        $_pm_email = '';

        $payment_code_obj = PipwavePaymentMapper::findOne(['pm_id' => $payment_methods_id]);
        $payment_code = $payment_code_obj->pg_code ?? '';

        if ($payment_code) {
            switch ($payment_code) {
                case 'paypalEC':
                    $m_pp = AnalysisPgInfo::findOne([
                        'orders_id' => $orders_id,
                        'info_key' => 'payer_email',
                    ]);
                    $_pm_email = $m_pp ? $m_pp->info_value : '';
                    break;
                case 'moneybookers':
                    $m_pp = AnalysisPgInfo::findOne([
                        'orders_id' => $orders_id,
                        'info_key' => 'moneybooker_email',
                    ]);
                    $_pm_email = $m_pp ? $m_pp->info_value : '';
                    break;
            }
        }

        if (!empty($_pm_email)) {
            if ($payment_status == 'Review' || $payment_status == 'Echeck') {
                $store_owner = Yii::$app->params['noreply.sender'];
                $store_owner_email_address = Yii::$app->params['noreply.email'];

                // Data array to send to SQS
                $emailDataSqs = array(
                    'filetype' => 'order_email',
                    'email_template' => 'order-paypal-review',
                    'email_subject_prefix' => '',
                    'store_owner' => $store_owner,
                    'store_owner_email_address' => $store_owner_email_address,
                    'customer' => array(
                        'id' => $customers_id,
                        'firstname' => $full_name,
                        'language' => 'en',
                        'email' => $_pm_email,
                        'payment_review' => ($payment_status == 'Review') ? 'paypal-review' : 'paypal-echeck',
                    ),
                    'orders' => array(
                        'orders_id' => (int)$orders_id,
                    )
                );

                $sqs = Yii::$app->aws->getSQS('PDF_QUEUE');
                $sqs->pushMessage(['data' => $emailDataSqs]);

                // Compare customer email with paypal email
                if (!empty($email) && ($_pm_email != $email)) {   // Both same email
                    // If customer use diff email than paypal email
                    $emailDataSqs['customer']['email'] = $email;
                    $sqs = Yii::$app->aws->getSQS('PDF_QUEUE');
                    $sqs->pushMessage(['data' => $emailDataSqs]);
                }

                // Set Email for Paypal Review or Paypal eCheck
                if ($payment_status == 'Review') {
                    $paypal_orders_tag_id = Yii::$app->params['paypal.review.tag'];
                    $payment_history_text = 'PayPal payment review notification email sent';
                } else {
                    $paypal_orders_tag_id = Yii::$app->params['paypal.echeck.tag'];
                    $payment_history_text = 'PayPal eCheck notification email sent';
                }

                // Add Orders Status History
                $m_attr = [
                    'orders_id' => $orders_id,
                    'orders_status_id' => 0,
                    'date_added' => new Expression('NOW()'),
                    'customer_notified' => 1,
                    'comments' => $payment_history_text,
                    'changed_by' => self::CHANGE_BY,
                ];

                OrdersStatusHistory::insertHistory($m_attr);

                if (isset($paypal_orders_tag_id)) {
                    Orders::addPayPalTagging($orders_id, $paypal_orders_tag_id);
                }
            }
        }
    }

    public static function stockMovement($orders_id)
    {
        $op_list = OrdersProducts::find()->where(['orders_id' => $orders_id])->orderBy('orders_products_id ASC')->asArray()->all();
        $products = [];
        foreach ($op_list as $op) {
            if ($op['parent_orders_products_id']) {
                $products[$op['parent_orders_products_id']]['bundle'][] = $op;
            } else {
                $products[$op['orders_products_id']] = $op;
            }
        }

        foreach ($products as $product) {
            if (isset($product['bundle'])) {
                foreach ($product['bundle'] as $sub_product) {
                    self::stockDeduction($orders_id, $sub_product['products_id'], $sub_product['products_quantity']);
                }
            } else {
                if (!OrdersProducts::isDirectTopUp($product['orders_products_id'])) {
                    self::stockDeduction($orders_id, $product['products_id'], $product['products_quantity']);
                }
            }
        }
    }

    private static function stockDeduction($orders_id, $product_id, $quantity)
    {
        (new Product)->customRequest('product', 'verifying-stock-movement', [
            'products_id' => $product_id,
            'orders_id' => $orders_id,
            'quantity' => $quantity
        ]);
    }

    public static function updateOrdersStatusCounter($data, $increment = 1)
    {
        $latest_date = new Expression('NOW()');
        $o_id = isset($data['orders_id']) ? (int)$data['orders_id'] : 0;
        $o_status_id = isset($data['orders_status_id']) ? (int)$data['orders_status_id'] : 0;
        $o_latest_date = (isset($data['date_added']) && $data['date_added'] != $latest_date) ? $data['date_added'] : $latest_date;

        if (!isset($data['changed_by'])) {
            $data['changed_by'] = self::CHANGE_BY;
        }

        if ($o_id && $o_status_id > 0) {
            $m_oss = OrdersStatusStat::findOne(array('orders_id' => $o_id, 'orders_status_id' => $o_status_id));
            if (isset($m_oss->orders_id)) {
                $m_attr = array(
                    'occurrence' => $m_oss->occurrence + (int)$increment,
                    'latest_date' => $o_latest_date,
                    'changed_by' => $data['changed_by']
                );
                $m_condition = 'orders_id = :oid AND orders_status_id = :osi';
                $m_params = array(':oid' => $m_oss->orders_id, ':osi' => $m_oss->orders_status_id);

                OrdersStatusStat::updateAll($m_attr, $m_condition, $m_params);
            } else {
                $m_attr = array(
                    'orders_id' => $o_id,
                    'orders_status_id' => $o_status_id,
                    'occurrence' => (int)$increment,
                    'first_date' => new Expression('NOW()'),
                    'latest_date' => $o_latest_date,
                    'changed_by' => $data['changed_by']
                );
                $model = new OrdersStatusStat();
                $model->load($m_attr, '');
                if (!$model->save()) {
                    throw new \Exception(json_encode($model->getErrors()));
                }
            }
        }
    }

    /**
     * @throws \yii\web\BadRequestHttpException
     * @throws \Exception
     */
    public static function createOrder($data)
    {
        $db_time = new Expression('NOW()');
        $customers_id = $data['customers_id'];
        $customers_info = Customer::getOrderCustomerInfo($customers_id);

        if (!empty($customers_info)) {
            $order_data = [
                'customers_id' => $customers_id,
                'customers_email_address' => $customers_info['email'],
                'customers_groups_id' => $customers_info['group_id'],
                'customers_telephone_country' => $customers_info['phone_country'],
                'customers_country_international_dialing_code' => $customers_info['country_dialing_code_id'],
                'customers_telephone' => $customers_info['telephone'],
                'payment_method' => '',
                'payment_methods_id' => 0,
                'payment_methods_parent_id' => 0,
                'paypal_ipn_id' => 0,
                'date_purchased' => $db_time,
                'last_modified' => $db_time,
                'orders_status' => 1,
                'currency' => $data['currency'],
                'remote_addr' => $data['ip_address'],
                'currency_value' => $data['currency_value'],
            ];

            $order_data = array_merge($order_data, self::compileOrderAddressInfo($customers_info));

            $o_model = new Orders();
            $o_model->load($order_data, '');
            $o_model->save();

            $orders_id = $o_model->orders_id;

            $orders_payment_status = new CronOrdersPaymentStatus();

            $orders_payment_status->load([
                'orders_id' => $orders_id,
                'counter' => 0,
                'check_date' => $db_time,
            ], '');

            $orders_payment_status->save();

            OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'ip_country', (string)$data['country_id']);
            # record checkout Site-ID
            OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'site_id', '0');
            # record payment IP Address
            OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'payment_ip', $data['ip_address']);
            OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'payment_ip_country', $data['ip_country']);

            # order total
            $country_code = '';
            $tax_percentage = 0;
            $orders_total_amount = 0;
            $zero_price_products = false;
            $sc_used = false;
            $sc_amount_used = 0;
            $coupon_used = false;

            $ot_allow_zero_amt = ['ot_subtotal', 'ot_total', 'ot_gst'];

            foreach ($data['ot'] as $ot) {
                if (($ot['storage_value'] > 0) || (($ot['storage_value'] == 0) && in_array($ot['code'], $ot_allow_zero_amt))) {
                    $model = new OrdersTotal();
                    $model->load([
                        'orders_id' => $orders_id,
                        'title' => $ot['title'],
                        'text' => $ot['storage_text'],
                        'value' => $ot['storage_value'],
                        'class' => $ot['code'],
                        'sort_order' => $ot['sort_order'],
                    ], '');

                    if (!$model->save()) {
                        throw new \Exception(json_encode($model->getErrors()));
                    }

                    /* -- GST :: record GST percentage -- */
                    if (($ot['code'] == 'ot_gst')) {
                        if (isset($ot['output']) && $ot['output']) {
                            foreach ($ot['output'] as $output_key => $output_val) {
                                OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_' . $output_key, $output_val);
                                // get country code
                                if ($output_key == 'country') {
                                    $country_code = $output_val;
                                }
                                // get the tax amount
                                if ($output_key == 'percentage') {
                                    $tax_percentage = $output_val;
                                }
                            }
                        }
                    }

                    if (($ot['code'] == 'ot_total')) {
                        $orders_total_amount = $ot['storage_value'];
                    }

                    if (($ot['code'] == 'ot_subtotal') && (double)$ot['storage_value'] == 0) {
                        $zero_price_products = true;
                    }

                    if (($ot['code'] == 'ot_gv') && (double)$ot['storage_value'] > 0) {
                        $sc_used = true;
                        $sc_amount_used = (double)$ot['checkout_value'];
                    }

                    if (($ot['code'] == 'ot_coupon') && (double)$ot['storage_value'] > 0) {
                        $coupon_used = true;
                    }
                }
            }

            if ($orders_total_amount > 0) {
                OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'checkoutSite', 'pipwave');
            }

            // Add to orders_extra_info for regular tax percentage
            if ($country_code && $tax_percentage) {
                // find current percentage
                $tax_info = OrdersTaxConfiguration::getNormalTaxPercentage($country_code);
                if ($tax_info && $tax_percentage != $tax_info->orders_tax_percentage) {
                    // find tax descriptions EN language
                    OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_normal_percentage', $tax_info->orders_tax_percentage);
                    // check if reverse need to calculate?
                    if ($tax_info->business_tax_percentage == 0 && $tax_info->business_tax_status == 1 && $tax_info->orders_include_reverse_charge == 1) {
                        OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_invoice_reverse_charge', $tax_info->orders_include_reverse_charge);
                        $reverse_tax_amount = round(($orders_total_amount * $tax_info->orders_tax_percentage / 100), 2);
                        OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_invoice_reverse_amount', $reverse_tax_amount);
                    }
                }
                // for invoice snapshot
                $tax_desc = OrdersTaxConfigurationDescription::getTaxDescription($tax_info->orders_tax_id, 1);
                OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_short_title', $tax_desc->orders_tax_title_short);
                OrdersExtraInfo::insertOrdersExtraInfo($orders_id, 'tax_invoice_title', $tax_info->tax_invoice_title);
            }

            foreach ($data['products'] as $product) {
                $preorder_product_id_array = [];
                $m_attr = [
                    'orders_id' => $orders_id,
                    'products_id' => $product['id'],
                    'products_model' => substr($product['model'], 0, 32),
                    'products_name' => $product['name'],
                    'orders_products_store_price' => $product['storage_price']['normal_price'],
                    'products_price' => $product['storage_price']['price'],
                    'final_price' => $product['storage_price']['final_price'],
                    'op_rebate' => ($product['op_info']['rebate_point'] + $product['op_info']['rebate_point_extra']),
                    'op_rebate_delivered' => 0,
                    'products_quantity' => $product['quantity'],
                    'products_pre_order' => (in_array($product['id'], $preorder_product_id_array) ? 1 : 0),
                    'custom_products_type_id' => $product['custom_products_type_id'],
                    'products_categories_id' => $product['products_categories_id'],
                ];

                $op_id = OrdersProducts::createOrdersProducts($m_attr);

                OrdersProductsExtraInfo::insertExtraInfo($op_id, 'products_type', (!empty($product['products_type']) ? $product['products_type'] : 0));
                OrdersProductsExtraInfo::insertExtraInfo($op_id, 'sub_products_id', (!empty($product['sub_products_id']) ? $product['sub_products_id'] : 0));

                if ($tax_percentage) {
                    if (!empty($data['products_tax_info'][$product['id']])) {
                        $tax_item = $data['products_tax_info'][$product['id']];
                        if (!isset($tax_item['tax_amount'])) {
                            throw new Exception('ERROR_MISSING_TAX_AMOUNT');
                        }
                        OrdersProductsExtraInfo::insertExtraInfo($op_id, 'tax_amount', $tax_item['tax_amount']);
                        if (!isset($tax_item['bundle_items'])) {
                            OrdersProductsExtraInfo::insertExtraInfo($op_id, 'tax_type', $tax_item['tax_type']);
                            OrdersProductsExtraInfo::insertExtraInfo($op_id, strtolower('tax_' . $tax_item['tax_type'] . '_formula'), $tax_item['remarks']);
                        }
                    }
                }

                if ($product['op_info']['rebate_point_extra'] > 0) {
                    OrdersProductsExtraInfo::insertExtraInfo($op_id, 'OP_EXTRA', $product['op_info']['rebate_point_extra']);
                }

                #OP
                OrdersProductsExtraInfo::insertExtraInfo($op_id, 'OP_FORMULA', $product['op_info']['rebate_point_formula'] . '<br /><br />Extra OP: ' . $product['op_info']['rebate_point_extra']);

                # Record Order Product is bundle
                foreach ($product['products_package'] as $subproduct_id => $subproduct_array) {
                    $attr = [
                        'orders_id' => $orders_id,
                        'products_id' => $subproduct_id,
                        'products_model' => substr($subproduct_array['model'], 0, 32), // added for ogm
                        'products_name' => $subproduct_array['name'],
                        'orders_products_store_price' => $subproduct_array['normal_store_price'],
                        'products_price' => '0.00',
                        'final_price' => '0.00',
                        'op_rebate' => 0,
                        'op_rebate_delivered' => 0,
                        'products_quantity' => $subproduct_array['qty'] * $product['quantity'],
                        'products_bundle_id' => $product['id'],
                        'parent_orders_products_id' => $op_id,
                        'products_categories_id' => $product['products_categories_id'],
                        'custom_products_type_id' => $product['custom_products_type_id'],
                    ];

                    $sub_op_id = OrdersProducts::createOrdersProducts($attr);

                    if ($tax_percentage) {
                        if (!empty($data['products_tax_info'][$product['id']])) {
                            $tax_item = $data['products_tax_info'][$product['id']];
                            foreach ($tax_item['bundle_items'] as $bundle_sub_item) {
                                if ($bundle_sub_item['products_id'] == $subproduct_id) {
                                    OrdersProductsExtraInfo::insertExtraInfo($sub_op_id, 'tax_amount', $bundle_sub_item['tax_amount']);
                                    OrdersProductsExtraInfo::insertExtraInfo($sub_op_id, 'tax_type', $bundle_sub_item['tax_type']);
                                    OrdersProductsExtraInfo::insertExtraInfo($sub_op_id, 'tax_' . strtolower($bundle_sub_item['tax_type']) . '_formula', $bundle_sub_item['remarks']);
                                    break;
                                }
                            }
                        }
                    }
                }

                # Record custom content
                if (isset($product['custom_content']['delivery_info']) && is_array($product['custom_content']['delivery_info'])) {
                    foreach ($product['custom_content']['delivery_info'] as $_key => $_val) {
                        if (!empty($_val)) {
                            OrdersProductsExtraInfo::insertExtraInfo($op_id, $_key, $_val);
                        }
                    }


                    //save address
                    if (isset($product['custom_content']['deliver_addr'])) {
                        $delivery_address = $product['custom_content']['deliver_addr'];
                        $delivery_address['orders_products_id'] = $op_id;
                        OrdersDeliveryAddress::saveDeliveryAddress($delivery_address);
                    }

                    if (isset($product['custom_content']['delivery_info']['delivery_mode']) && $product['custom_content']['delivery_info']['delivery_mode'] == 6) {
                        if (!empty($product['custom_content']['top_up_info'])) {
                            $info_key_array = array_keys($product['custom_content']['top_up_info']);

                            if ($product['products_package']) {
                                # DTU does not support bundle item
                            } else {
                                # Single
                                if ($top_up_info_array = TopUpInfo::getTopUpInfo($info_key_array, $product['id'])) {
                                    foreach ($product['custom_content']['top_up_info'] as $extra_key => $extra_val) {
                                        if ($extra_val === "") {
                                            throw new Exception('Empty Direct Top Up Info');
                                        }
                                        CustomersTopUpInfo::insertTopUpInfo($top_up_info_array[$extra_key], $op_id, $extra_val);
                                    }
                                }
                            }
                        } else {
                            throw new Exception('Empty Direct Top Up Info');
                        }
                    }
                }

                # Register Order Custom Product
                if ($product['custom_products_type_id'] == 3) {
                    # Store Credit
                    if (isset($product['custom_content']['orders_sc_currency_code'])) {
                        $order_sc_currency_id = Yii::$app->currency->getIdByCode($product['custom_content']['orders_sc_currency_code']);
                        $m_attr = [
                            'products_id' => $product['id'],
                            'orders_products_id' => $op_id,
                            'orders_custom_products_key' => 'store_credit_currency',
                            'orders_custom_products_value' => (string)$order_sc_currency_id ?: Yii::$app->currency->getIdByCode($data['currency']),
                            'orders_custom_products_number' => '0',
                        ];
                        OrdersCustomProducts::insertOrdersCustomProduct($m_attr);
                    }
                }
            }

            $m_attr = [
                'orders_id' => $orders_id,
                'orders_status_id' => 1,
                'date_added' => $db_time,
                'customer_notified' => '1',
                'comments' => '',
                'changed_by' => self::CHANGE_BY
            ];

            OrdersStatusHistory::insertHistory($m_attr);
            self::updateOrdersStatusCounter($m_attr);

            unset($m_attr);
            $move_order = false;

            if ($sc_used) {
                $move_order = (new StoreCredit)->deductStoreCredit($customers_id, $orders_id, $sc_amount_used, $data['currency'], $order_data['customers_email_address']);
            }

            if (!empty($data['coupon_code']) && $coupon_used) {
                $coupon_id = Coupons::find()->select(['coupon_id'])->where(['coupon_code' => $data['coupon_code']])->scalar();
                if ($coupon_id) {
                    $coupon = new CouponRedeemTrack();
                    $coupon->load([
                        'coupon_id' => $coupon_id,
                        'customer_id' => $customers_id,
                        'redeem_date' => $db_time,
                        'redeem_ip' => $data['ip_address'],
                        'order_id' => $orders_id
                    ], '');
                    $coupon->save();
                }
            }

            if (($move_order && $orders_total_amount <= 0) || $zero_price_products || ($sc_used === false && $orders_total_amount == 0)) {
                # clear checkout quantity cache
                if (!empty($data['products'])) {
                    for ($i = 0, $cnt = count($data['products']); $cnt > $i; $i++) {
                        if ($data['products'][$i]['products_package']) {
                            # Bundle
                            foreach ($product['products_package'] as $subproduct_id => $subproduct_array) {
                                $cache_key = 'products_checkout_setting/exceed_limit_info/array/customers_id/' . $customers_id . '/products_id/' . $subproduct_id;
                                Yii::$app->cache->delete(Yii::$app->params['frontend.memcache.prefix'] . $cache_key);
                            }
                        } else {
                            $cache_key = 'products_checkout_setting/exceed_limit_info/array/customers_id/' . $customers_id . '/products_id/' . $data['products'][$i]['id'];
                            Yii::$app->cache->delete(Yii::$app->params['frontend.memcache.prefix'] . $cache_key);
                        }
                    }
                }

                $m_attr = [
                    'customers_id' => $customers_id,
                    'orders_id' => $orders_id,
                    'orders_type' => 'CO',
                    'site_id' => '0',
                ];

                OrdersNotification::insertOrdersNotification($m_attr);

                Order::moveOrder($orders_id, $data['currency'], $orders_total_amount);
            }

            TempProcess::deleteAll(['page_name' => 'OGMOrderModule', 'match_case' => $customers_id]);

            try {
                $sqs = Yii::$app->aws->getSQS('ORDER_QUEUE');
                $sqs->pushMessage(['data' => ['params' => ['order_id' => $orders_id, 'timestamp' => time()]]]);
            } catch (\Exception $e) {
                // DO Nothing on SQS Failure
            }
        } else {
            throw new Exception('ERROR_INVALID_CUSTOMERS');
        }

        return $orders_id;
    }

    private static function compileOrderAddressInfo($info): array
    {
        $return_arr = [];
        $prefix_list = ['customers', 'billing', 'delivery'];

        $data = [
            'name' => $info['first_name'] . ' ' . $info['last_name'],
            'company' => $info['company'],
            'street_address' => $info['street_address'],
            'suburb' => $info['suburb'],
            'city' => $info['city'],
            'postcode' => $info['postcode'],
            'state' => $info['state'],
            'country' => $info['country'],
            'address_format_id' => $info['format_id'],
        ];

        foreach ($prefix_list as $prefix) {
            foreach ($data as $k => $v) {
                $return_arr[$prefix . '_' . $k] = $v;
            }
        }

        return $return_arr;
    }
}