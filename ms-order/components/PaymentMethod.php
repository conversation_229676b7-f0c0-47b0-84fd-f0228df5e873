<?php

namespace micro\components;

use micro\models\PaymentConfigurationInfo;
use micro\models\PaymentConfigurationInfoDescription;
use micro\models\PaymentMethods;
use yii\base\BaseObject;
use yii\db\Query;

class PaymentMethod
{
    public static function getPaymentSetting($pm_id, $language_id = 1)
    {
        $pm_setting_array = self::getPaymentConfiguration($pm_id, $language_id);

        if (empty($pm_setting_array)) {
            $paymentGatewayId = PaymentMethods::find()
                ->select('payment_methods_parent_id')
                ->where(['payment_methods_id' => $pm_id])
                ->scalar();

            if ($paymentGatewayId) {
                $pm_setting_array = self::getPaymentConfiguration($paymentGatewayId, $language_id);
            }
        }

        return $pm_setting_array;
    }

    public static function getPaymentConfiguration($pm_id, $language_id = 1)
    {
        $payment_config = array();

        $query = new Query();
        $result = $query->select([
            'payment_configuration_info_key',
            'payment_configuration_info_value',
        ])
            ->from(PaymentConfigurationInfo::tableName() . ' pci')
            ->innerJoin(PaymentConfigurationInfoDescription::tableName() . ' pcid', 'pci.payment_configuration_info_id = pcid.payment_configuration_info_id')
            ->where(['pcid.languages_id' => $language_id, 'pci.payment_methods_id' => $pm_id])
            ->orderBy('pci.payment_configuration_info_sort_order')
            ->all();

        foreach ($result as $row) {
            $payment_config[$row['payment_configuration_info_key']] = $row['payment_configuration_info_value'];
        }
        return $payment_config;
    }


}