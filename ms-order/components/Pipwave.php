<?php

namespace micro\components;

use Yii;
use yii\base\Component;
use yii\helpers\Json;
use yii\helpers\Url;
use micro\models\Countries;
use micro\models\Customers;
use micro\models\CustomersInfo;
use micro\models\CustomersInfoVerification;
use micro\models\CustomersLoginIpHistory;
use micro\models\CustomersVerificationDocument;
use micro\models\Orders;
use micro\models\PipwavePaymentMapper;
use yii\web\HttpException;

class Pipwave extends Component
{
    public $merchant_id;
    public $key;
    public $secret;
    public $server_url;
    public $sdk_url;
    private $prefered_payment;
    private $exclude_payment;

    public function __construct()
    {
        $params = Yii::$app->params['pipwave'];
        $this->key = $params['key'];
        $this->secret = $params['secret'];
        $this->server_url = $params['url'];
        $this->sdk_url = $params['sdk'];
        parent::__construct();
    }

    public function validateIp($ip)
    {
        if ($ip) {
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
                return false;
            }
            return true;
        }
        return false;
    }

    public function getUserIp()
    {
        if (isset($_SERVER['HTTP_TRUE_CLIENT_IP'])) {
            return $_SERVER['HTTP_TRUE_CLIENT_IP'];
        }

        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && ($_SERVER['HTTP_X_FORWARDED_FOR'] != '')) {
            $ip_array = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            foreach ($ip_array as $ip) {
                // trim for safety measures
                $ip = trim($ip);
                // attempt to validate IP
                if ($this->validateIp($ip)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    public function generate_pw_signature($signatureParam)
    {
        ksort($signatureParam);
        $signature = "";
        foreach ($signatureParam as $key => $value) {
            $signature .= $key . ':' . $value;
        }
        return sha1($signature);
    }

    public function verifyNotificationSignature($data)
    {
        $signatureParams = [
            'api_key' => $this->key,
            'api_secret' => $this->secret,
            'txn_id' => $data['txn_id'],
            'amount' => $data['amount'],
            'currency_code' => $data['currency_code'],
            'pw_id' => $data['pw_id'],
            'transaction_status' => $data['transaction_status'],
            'timestamp' => $data['timestamp']
        ];

        if ($data['signature'] == $this->generate_pw_signature($signatureParams)) {
            return true;
        }
    }

    function send_request_to_pw($data, $pw_api_key)
    {
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('x-api-key:' . $pw_api_key));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_URL, $this->server_url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if ($response == false) {
            echo "<pre>";
            echo 'CURL ERROR: ' . curl_errno($ch) . '::' . curl_error($ch);
            die;
        }
        curl_close($ch);
        return json_decode($response, true);
    }
}