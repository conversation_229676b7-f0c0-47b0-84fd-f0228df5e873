<?php

namespace micro\components;

use offgamers\base\components\Guzzle;
use Yii;
use yii\helpers\Json;

class StoreCredit
{
    public $client;
    public $client_option;

    public function deductStoreCredit($customers_id, $order_id, $amount, $currency, $email)
    {
        $data = array(
            'orders_id' => $order_id,
            'reference_id' => $order_id,
            'amount' => $amount,
            'total_amount' => $amount,
            'currency' => $currency,
            'activity' => 'P',
            'transaction_type' => 'DEDUCT_CREDIT',
            'show_customer' => 1,
            'requesting_id' => $email,
            'requesting_role' => 'customer',
            'customers_id' => $customers_id,
            'customers_role' => 'customer'
        );

        // Clean data params if empty
        $data = array_filter($data, function ($value) {
            return ($value !== null && $value !== false && $value !== '');
        });

        $ms_config = Yii::$app->params['micro.service.storecredit'];
        $data['source'] = $ms_config['key'];
        $data['time'] = time();
        $data['signature'] = md5($ms_config['key'] . $data['time'] . "|" . $ms_config['secret']);

        $this->client = new \GuzzleHttp\Client(['proxy' => false]);
        $this->client_option = array(
            'headers' => array('Content-Type' => 'application/json'),
        );

        $response = $this->client->request('POST', $ms_config['baseUrl'] . '/store-credits', ['json' => $data]);

        $data = Json::decode($response->getBody());
        
        // Only Process
        if (!empty($data['status']) && !empty($data['result']['request_id']) && !empty($data['result']['message']) && $data['result']['message'] == 'Processed Successfully.') {
            return true;
        }
    }

}