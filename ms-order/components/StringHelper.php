<?php

namespace micro\components;

class StringHelper
{
    public static function sanitizeUtf8String($string)
    {
        if (self::checkUtf8Encoding($string)) {
            return $string;
        }
        $sanitized_string = '';
        for ($i = 0; $i < mb_strlen($string); $i++) {
            $char = mb_substr($string, $i, 1);
            // Check if the character is a valid UTF-8 character
            if (mb_check_encoding($char, 'UTF-8')) {
                $sanitized_string .= $char;
            }
        }
        return $sanitized_string;
    }

    public static function checkUtf8Encoding($str)
    {
        return mb_check_encoding($str, 'UTF-8');
    }

}