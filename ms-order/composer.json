{"name": "offgamers/microservices-order", "description": "Offgamers Yii2 Order Module Micro Services", "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "require": {"php": ">=7.3.0", "yiisoft/yii2": "~2.0.0", "offgamers/microservices-multiservice": "dev-main", "ext-fileinfo": "*", "ext-bcmath": "*", "ext-json": "*"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}, {"type": "vcs", "url": "**************:tech-ogm/toolbox-multiservice.git"}, {"type": "vcs", "url": "**************:tech-ogm/ms-publisher.git"}], "minimum-stability": "dev"}