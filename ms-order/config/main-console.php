<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
    require(__DIR__ . '/params-encoded.php')
);

return [
    'id' => 'ms-order',

    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log', 'order_queue', 'pipwave_queue', 'g2g_order_queue'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'mutex' => [
            'class' => 'yii\mutex\MysqlMutex',
        ],
        'mailer' => [
            'class' => 'offgamers\base\mail\AWSSESMailer',
            'mailQueueName' => 'MAIL_QUEUE',
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'DEBUG' => $params['slack.webhook.debug'],
                'MYCET' => $params['slack.webhook.mycet'],
                'CNCET' => $params['slack.webhook.cncet'],
                'BDT_G2G_ORDER' => ($params['slack.webhook.bdt_g2g'] ?? ''),
            ]
        ],
        'order_queue' => [
            'class' => \micro\components\OrderQueue::class,
            'queue_name' => 'ORDER_QUEUE',
            'attempts' => 3,
        ],
        'pipwave_queue' => [
            'class' => \micro\components\PipwaveQueue::class,
            'queue_name' => 'PIPWAVE_QUEUE',
            'attempts' => 3,
        ],
        'g2g_order_queue' => [
            'class' => \micro\components\G2gOrderQueue::class,
            'queue_name' => 'G2G_ORDER_QUEUE',
            'attempts' => 3,
        ],
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'currency' => 'micro\components\Currency',
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ]
    ]
];


