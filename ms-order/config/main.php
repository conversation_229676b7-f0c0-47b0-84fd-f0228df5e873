<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
    require(__DIR__ . '/params-encoded.php')
);

return [
    'id' => 'ms-order',

    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log', 'mutex'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'mutex' => [
            'class' => 'yii\mutex\MysqlMutex',
        ],
        'mailer' => [
            'class' => 'offgamers\base\mail\AWSSESMailer',
            'mailQueueName' => 'MAIL_QUEUE',
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'DEBUG' => $params['slack.webhook.debug'],
                'MYCET' => $params['slack.webhook.mycet'],
                'CNCET' => $params['slack.webhook.cncet'],
                'BDT_G2G_ORDER' => ($params['slack.webhook.bdt_g2g'] ?? ''),
            ]
        ],
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'currency' => 'micro\components\Currency',
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'request' => [
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ],
        ],
        'i18n' => [
            'translations' => [
                'seo' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    //'basePath' => '@app/messages',
                    'sourceLanguage' => 'en',
                    'fileMap' => [
                        'app' => 'seo.php',
                    ],
                ],
            ],
        ],
        'response' => [
            'class' => 'offgamers\base\components\Response',
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'errorHandler' => [
            'class' => 'offgamers\base\components\ErrorHandler'
        ]
    ]

];


