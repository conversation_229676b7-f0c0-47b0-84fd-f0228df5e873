<?php

namespace micro\controllers;

use micro\models\DirectTopUpModel;
use Yii;

class DirectTopUpController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        $model = new DirectTopUpModel();
        $model->load($this->input, '');
        return $model->processOrder();
    }

    public function actionResetTopUpStatus()
    {
        DirectTopUpModel::resetTopUpStatus($this->input);
        return true;
    }
}