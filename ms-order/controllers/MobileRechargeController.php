<?php

namespace micro\controllers;

use Yii;
use micro\models\MobileRechargeHistory;

class MobileRechargeController extends \offgamers\base\controllers\RestController
{
    public function actionGetRechargeHistory()
    {
        return MobileRechargeHistory::find()
            ->select(['prefix', 'phone_no'])
            ->where(['customer_id' => $this->input['customer_id']])
            ->andWhere(['hide_from_listing' => 0])
            ->distinct()
            ->orderBy(['history_id' => SORT_DESC])
            ->asArray()
            ->limit(5)
            ->all();
    }

    public function actionHideRechargeHistory()
    {
        MobileRechargeHistory::updateAll(['hide_from_listing' => 1], ['prefix' => $this->input['prefix'], 'phone_no' => $this->input['phone_no'], 'customer_id' => $this->input['customer_id']]);
        return true;
    }
}