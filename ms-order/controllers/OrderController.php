<?php

namespace micro\controllers;

use Yii;
use micro\models\Orders;
use micro\components\Order;
use micro\models\OrdersProducts;
use offgamers\base\components\TaxCom;

class OrderController extends \offgamers\base\controllers\RestController
{
    public function actionPlaceOrder()
    {
        $data = $this->input;
        $transaction = Yii::$app->db_offgamers->beginTransaction();
        $og_transaction = Yii::$app->db_og->beginTransaction();
        $error = '';
        $orders_id = 0;
        try {
            $orders_id = Order::createOrder($data);
            $transaction->commit();
            $og_transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $og_transaction->rollBack();
            $error = $e->getMessage();
        }
        return ['orders_id' => $orders_id, 'error' => $error];
    }

    public function actionSetDeliveryQueue()
    {
        OrdersProducts::setDeliveryQueue($this->input);
        return true;
    }

    public function actionGetTotalOrderAmount()
    {
        return (new Orders())->getTotalOrderAmount($this->input['customers_id'], $this->input['start_time'], $this->input['end_time']);
    }

    public function actionPushOrderQueue()
    {
        $sqs = Yii::$app->aws->getSQS('ORDER_QUEUE');
        return $sqs->pushMessage($this->input['payload']);
    }

    public function actionPushMailQueue()
    {
        $sqs = Yii::$app->aws->getSQS('PDF_QUEUE');
        return $sqs->pushMessage($this->input);
    }

    public function actionGetOrderTax()
    {
        $taxCom = new TaxCom;
        return $taxCom->calcTax($this->input);
    }

    public function actionProcessG2gOrder()
    {
        $data = $this->input;

        /**
         * $data['order_id'] = G2G Order ID
         * $data['reseller_id'] = Reseller id
         */

        if (empty($data['order_id']) || empty($data['reseller_id'])) {
            throw new \Exception("Missing order_id or reseller_id");
        }

        $sqs = Yii::$app->aws->getSQS("G2G_ORDER_QUEUE");
        return $sqs->pushMessage(['data' => $data]);
    }
}