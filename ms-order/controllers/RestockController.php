<?php

namespace micro\controllers;

use Yii;
use micro\models\AutoRestockModel;

class RestockController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        $model = new AutoRestockModel();
        $model->load($this->input,'');
        return $model->processRestock();
    }

    public function actionGetRestockItemDetail(){
        $model = new AutoRestockModel();
        $model->load($this->input,'');
        return $model->getRestockItemDetail();
    }

}