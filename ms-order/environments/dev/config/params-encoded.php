<?php
return [
    // Main offgamers db conection
    'db.dsn' => 'mysql:host=127.0.0.1;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // offgamers db connection for non-utf8
    'db.offgamers.dsn' => 'mysql:host=127.0.0.1;dbname=offgamers',
    'db.offgamers.username' => '',
    'db.offgamers.password' => '',

    // offgamers db connection for non-utf8
    'db.log.dsn' => 'mysql:host=127.0.0.1;dbname=offgamers_log',
    'db.log.username' => '',
    'db.log.password' => '',

    // offgamers db connection for utf8mb4
    'db.og.dsn' => 'mysql:host=127.0.0.1;dbname=og',
    'db.og.username' => '',
    'db.og.password' => '',

    'slack.webhook.default' => '',
    'slack.webhook.cncet' => '',
    'slack.webhook.mycet' => '',
    'slack.webhook.debug' => '',
    'slack.webhook.bdt_g2g' => '',

    'api.credential' => [
        'backend' => '123456'
    ],

    //AWS
    'aws.key' => '',
    'aws.secret' => '',

    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',

    //Micro Service Credential
    'micro.service.inventory' => [
        'baseUrl' => 'http://staging-ms-inventory.offgamers.local',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.product' => [
        'baseUrl' => 'https://staging-ms-product.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.storecredit' => [
        'baseUrl' => 'http://dev-ms-storecredit.offgamers.com',
        'key' => 'ms-order',
        'secret' => '123456'
    ],
];
