#!/usr/bin/env php
<?php

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require(__DIR__ . '/vendor/autoload.php');
require(__DIR__ . '/vendor/yiisoft/yii2/Yii.php');
$config = yii\helpers\ArrayHelper::merge(
    require(__DIR__ . '/config/main-console.php'),
    require(__DIR__ . '/config/main-local.php')
);
$application = new yii\console\Application($config);
$exitCode = $application->run();

exit($exitCode);