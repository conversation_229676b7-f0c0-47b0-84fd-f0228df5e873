<?php
return [
    // Main offgamers db conection
    'db.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // log module
    'db.log.dsn' => 'mysql:host=localhost;dbname=offgamers_log',
    'db.log.username' => '',
    'db.log.password' => '',

    // og new database
    'db.og.dsn' => 'mysql:host=localhost;dbname=og',
    'db.og.username' => '',
    'db.og.password' => '',

    // slave db connection
    'db.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',
    'db.slave.username' => '',
    'db.slave.password' => '',

    // slave db connection
    'db.log.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers_log',
    'db.log.slave.username' => '',
    'db.log.slave.password' => '',
    
    'slack.webhook.default' => '',
    'slack.webhook.cncet' => '',
    'slack.webhook.mycet' => '',
    'slack.webhook.debug' => '',
    'slack.webhook.bdt_g2g' => '',

    'api.credential' => [
        'backend' => '123456'
    ],

    //AWS
    'aws.key' => '',
    'aws.secret' => '',

    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',

    //Micro Service Credential
    'micro.service.inventory' => [
        'baseUrl' => 'http://staging-ms-inventory.offgamers.local',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.product' => [
        'baseUrl' => 'https://staging-ms-product.offgamers.com',
        'key' => 'backend',
        'secret' => '123456'
    ],

    'micro.service.storecredit' => [
        'baseUrl' => 'http://dev-ms-storecredit.offgamers.com',
        'key' => 'ms-order',
        'secret' => '123456'
    ],
];
