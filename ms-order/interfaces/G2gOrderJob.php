<?php

namespace micro\interfaces;

use micro\models\resellers\G2G;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use offgamers\base\traits\GuzzleTrait;

class G2gOrderJob extends BaseObject implements JobInterface
{
    use GuzzleTrait;

    public $data;
    public $start_time;

    public function execute($queue)
    {
        /**
         * $this->data should contain the below key
         * order_id = order_id from g2g
         * reseller_id = reseller_id from g2g
         */

        try {
            $g2g = new G2G();
            $g2g->reseller = $this->data['reseller_id'];
            $g2g->order_id = $this->data['order_id'];
            $g2g->reprocess_restock_request = ($this->data['reprocess_restock_request'] ?? false);
            $g2g->processDelivery();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

}