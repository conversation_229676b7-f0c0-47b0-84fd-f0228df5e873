<?php

namespace micro\interfaces;

use micro\exceptions\QueueRetryableException;
use Yii;
use micro\components\LogFilesComponent;
use micro\components\Order;
use micro\models\AddressBook;
use micro\models\AnalysisCreditCardInfo;
use micro\models\AnalysisPgInfo;
use micro\models\ApiTmBrowser;
use micro\models\ApiTmDevice;
use micro\models\ApiTmProxyIp;
use micro\models\ApiTmTransactionIdentifier;
use micro\models\ApiTmTrueIp;
use micro\models\Countries;
use micro\models\Customers;
use micro\models\CustomersInfo;
use micro\models\CustomersInfoVerification;
use micro\models\CustomersLoginIpHistory;
use micro\models\Orders;
use micro\models\OrdersExtraInfo;
use micro\models\OrdersStatusHistory;
use micro\models\PaymentExtraInfo;
use micro\models\PipwavePayment;
use micro\models\PipwavePaymentHistory;
use micro\models\PipwavePaymentMapper;
use micro\models\StoreRefund;
use micro\models\StoreRefundHistory;
use micro\models\Zones;
use yii\base\BaseObject;
use yii\helpers\Json;
use yii\queue\JobInterface;

class IpnJob extends BaseObject implements JobInterface
{
    public $data;
    public $start_time;
    const MUTEX_LOCK_NAME = 'pipwave-ipn/orders/%s';

    public function execute($queue)
    {
        try {
            $orderId = $this->data['txn_id'];
            if ($this->obtainLock($orderId)) {
                $this->processPipwaveNotification($this->data);
            }
        } finally {
            $this->releaseLock($orderId);
        }
    }

    public function processPipwaveNotification($data)
    {
        $orderId = $data['txn_id'];
        $orders = Orders::findOne($orderId);

        if ($orders) {
            $payment_mapper = PipwavePaymentMapper::getPaymentInfo($data['payment_method_code']);

            if (!$payment_mapper) {
                throw new \Exception("Pipwave Payment Mapper not Created");
            }

            try {
                # Duplicate Notification
                if (!PipwavePayment::updatePipwaveData($orderId, $data, $payment_mapper)) {
                    return false;
                }
                PipwavePaymentHistory::savePipwavePaymentHistory($data);
            } catch (\Exception $e) {
                $this->reportError('Fail to update Pipwave Payment', $e);
            }

            $customer_id = $orders->customers_id;

            try {
                $this->sendEmailToPG($data, $payment_mapper);
            } catch (\Exception $e) {
                $this->reportError('Fail to send PG email', $e);
            }

            try {
                $this->processAttachment($data, $payment_mapper);
            } catch (\Exception $e) {
                $this->reportError('Fail to upload attachment', $e);
            }

            try {
                $this->updateKount($data, $customer_id);
            } catch (\Exception $e) {
                $this->reportError('Fail to update Kount Info', $e);
            }

            try {
                $this->updatePaymentInfo($orderId, $payment_mapper, $data);
            } catch (\Exception $e) {
                $this->reportError('Fail to update Payment Info', $e);
            }

            try {
                $this->updateOrdersData($orderId, $customer_id, $data);
            } catch (\Exception $e) {
                $this->reportError('Fail to update Orders Data', $e);
            }

            if ($data['type'] == 'payment' || $data['type'] == 'requery' || $data['type'] == 'manual') {
                $pm_id = $payment_mapper['pm_id'];

                try {
                    $orders->setOrdersPaymentMethod($payment_mapper);
                } catch (\Exception $e) {
                    $this->reportError('Fail to update set orders payment method', $e);
                }

                try {
                    $orders->updateSurchargeAmount($data);
                } catch (\Exception $e) {
                    $this->reportError('Fail to update surcharge amount', $e);
                }

                try {
                    $this->paypalVerification($orderId, $data, $payment_mapper, $customer_id, $data['shipping_info']['name'], $payment_mapper['pm_id']);
                } catch (\Exception $e) {
                    $this->reportError('Fail to process paypal verification', $e);
                }

                if ($data['transaction_status'] == 10 && $data['status'] == 200) {
                    try {
                        Order::moveOrder($orderId, $data['currency_code'], $data['total_amount'], $data['rules_action']);
                    } catch (\Exception $e) {
                        $this->reportError('Fail to move order', $e);
                    }
                }
            } else {
                if ($data['type'] == 'refund') {
                    // api status is success
                    if (isset($data['status']) && $data['status'] == 200) {
                        //full refund or partial refund
                        try {
                            $this->processRefund($orderId, $data, $payment_mapper);
                        } catch (\Exception $e) {
                            $this->reportError('Fail to process refund', $e);
                        }
                    }
                }
            }
        }
    }

    public function reportError($title, $exception)
    {
        Yii::$app->slack->send($title, array(
            array(
                'color' => 'warning',
                'text' => $exception->getTraceAsString(),
            ),
        ));
    }

    public function sendEmailToPG($data, $payment_mapper)
    {
        if (count($data['matched_rules']) > 0 && $data['transaction_status'] == 10) {
            $notifyPg = true;
            $whatToDo = '';
            $sender = Yii::$app->params['noreply.sender'];
            $sender_email = Yii::$app->params['noreply.email'];
            foreach ($data['matched_rules'] as $rules) {
                if (isset($rules['what_to_do']) && !empty($rules['what_to_do'])) {
                    // Look for matched words in what_to_do param
                    if (preg_match('/notify-pg/', $rules['what_to_do'])) {
                        $whatToDo = 'notify-pg';
                    }
                    switch ($whatToDo) {
                        case 'notify-pg':
                            $pgCS = '';
                            $ccEmail = array();
                            $message = '';
                            $subject = '';
                            switch ($payment_mapper['pg_code']) {
                                case 'indomog':
                                    $pgCS = '<EMAIL>';
                                    $ccEmail = array('<EMAIL>', '<EMAIL>');
                                    //email to Indomog to verify to payment
                                    //cet will process the order when Indomog reply OK
                                    $message = "<html>
                                                    <head></head>
                                                    <body>
                                                        <p>Hi Support,</p>
                                                        <p>Kindly review order " . $data['txn_id'] . ". Customer's order is reach IDR20.000,000 in one day.</p>
                                                        <p>Thank You</p>
                                                        <p>Best Regards,</p>
                                                    </body>
                                                    </html>";
                                    $subject = 'Indomog Order Review ' . date("m/d/Y");
                                    $message = str_replace(array("\r", "\n"), '', $message);
                                    break;
                                case 'cimb':
                                    $pgCS = '<EMAIL>';
                                    $ccEmail = array(
                                        '<EMAIL>',
                                        '<EMAIL>'
                                    );
                                    break;
                                case 'iPay88':
                                    break;
                                case 'maybank':
                                    $pgCS = '<EMAIL>';
                                    $ccEmail = array(
                                        '<EMAIL>'
                                    );
                                    break;
                                case 'pbbank':
                                    $pgCS = '<EMAIL>';
                                    $ccEmail = array(
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>'
                                    );
                                    break;
                                case 'rhb':
                                    $pgCS = '<EMAIL>';
                                    $ccEmail = array(
                                        '<EMAIL>'
                                    );
                                    break;
                                case 'pipwavePG':
                                    switch ($payment_mapper['pg_display_name']) {
                                        case 'SafeCharge':
                                            //email to safecharge to verify the payment
                                            //email to customer ask for LL verification
                                            //either safecharge said OK or ID is verified, process the order
                                            $pgCS = '<EMAIL>';
                                            $ccEmail = array(
                                                '<EMAIL>'
                                            );
                                            break;
                                        case 'MOLPay':
                                            $pgCS = '<EMAIL>';
                                            $ccEmail = array(
                                                '<EMAIL>'
                                            );
                                            break;
                                        case 'M2UPay':
                                            $pgCS = '<EMAIL>';
                                            $ccEmail = array(
                                                '<EMAIL>'
                                            );
                                            break;
                                        case 'DOKU':
                                            $pgCS = '<EMAIL>';
                                            $ccEmail = array(
                                                '<EMAIL>'
                                            );
                                            break;
                                        case 'eGHL':
                                            $pgCS = '<EMAIL>';
                                            $ccEmail = array(
                                                '<EMAIL>'
                                            );
                                            break;
                                    }
                                    break;
                            }
                            if (empty($subject)) {
                                $subject = $payment_mapper['pg_display_name'] . ' Order Review ' . date("m/d/Y");
                            }
                            if (empty($message)) {
                                $CurrenciesObj = Yii::$app->currency;
                                $checkoutDataAmount = $CurrenciesObj->format($data['currency_code'], $data['amount']);
                                $message = "<html>
                                                <head></head>
                                                <body>
                                                    <p>Hi Support,</p>
                                                    <p>Kindly review order " . $data['txn_id'] . ". Customer's order is hitting our daily volume/amount threshold.</p>
                                                    <table>
                                                        <th>
                                                            <tr><td>Order Details:</td></tr>
                                                        </th>    
                                                        <tr>
                                                            <td>Order ID</td>
                                                            <td>" . $data['txn_id'] . "</td>
                                                        </tr> 
                                                        <tr>
                                                            <td>Payment Transaction ID</td>
                                                            <td>" . $data['pg_txn_id'] . "</td>
                                                        </tr> 
                                                        <tr>
                                                            <td>Order Amount</td>
                                                            <td>" . $checkoutDataAmount . "</td>
                                                        </tr> 
                                                        <tr>
                                                            <td>Payment Method</td>
                                                            <td>" . $payment_mapper['pm_display_name'] . "</td>
                                                        </tr>       
                                                    </table>
                                                    <p>Thank You</p>
                                                    <p>Best Regards,</p>
                                                </body>
                                                </html>";
                                $message = str_replace(array("\r", "\n"), '', $message);
                            }
                            if (!empty($pgCS)) {
                                if ($notifyPg) {
                                    Yii::$app->mailer->compose()
                                        ->setFrom([$sender_email => $sender])
                                        ->setTo($pgCS)
                                        ->setCc($ccEmail)
                                        ->setSubject($subject)
                                        ->setHtmlBody($message)
                                        ->send();
                                }
                                $notifyPg = false;
                            }
                            break;
                    }
                }
            }
        }
    }

    public function processAttachment($data, $payment_mapper)
    {
        if (isset($data['require_upload']) && $data['require_upload'] > 0) {
            //handle uploaded images
            if (isset($data['upload_files']) && count($data['upload_files']) > 0) {
                if (!empty($data['upload_remark'])) {
                    PaymentExtraInfo::updateAttachmentDetail($data['txn_id'], $data['upload_remark']);
                }
                $prefix = "OG/";
                $portalName = 'OffGamers';
                $receiverName = Yii::$app->params['cet.receiver'];
                $receiverEmail = Yii::$app->params['cet.email'];
                $sender = Yii::$app->params['noreply.sender'];
                $sender_email = Yii::$app->params['noreply.email'];
                foreach ($data['upload_files'] as $key => $file) {
                    if (!empty($file)) {
                        $key = $key + 1;
                        $ini_val = ini_get('upload_tmp_dir');
                        $upload_tmp_dir = $ini_val ?: sys_get_temp_dir();
                        $url = strtok($file, '?');
                        $filename = basename($url);
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $file);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        $img = curl_exec($ch);
                        curl_close($ch);
                        $image_filename = $upload_tmp_dir . '/' . $filename;
                        file_put_contents($image_filename, $img);
                        $files[] = $image_filename;
                        if ($img) {
                            $filetype = strtolower(pathinfo($image_filename, PATHINFO_EXTENSION));
                            $file_name = $data['txn_id'] . '-' . $key . '.' . $filetype;
                            $db_file_name = '{order_id}-' . $key . '.' . $filetype;
                            if (!OrdersExtraInfo::find()->where([
                                'orders_id' => $data['txn_id'],
                                'orders_extra_info_key' => 'payment_images_' . $key
                            ])->exists()) {
                                $s3 = Yii::$app->aws->getS3('BUCKET_UPLOAD');;
                                $file_date = date('Y') . '/' . date('m') . '/';
                                $filePath = 'payment/' . $file_date;
                                $s3->saveContent($prefix . $filePath . $file_name, $img, false, null, array('ContentType' => mime_content_type($image_filename)));
                                $extra_info = new OrdersExtraInfo;
                                $extra_info->load([
                                    'orders_id' => $data['txn_id'],
                                    'orders_extra_info_key' => 'payment_images_' . $key,
                                    'orders_extra_info_value' => $file_date . $db_file_name
                                ], '');
                                $extra_info->save();
                            }
                        }
                    }
                }
                if (count($files) > 0 && $data['transaction_status'] == 5) {
                    $CurrenciesObj = Yii::$app->currency;
                    $checkoutDataAmount = $CurrenciesObj->format($data['currency_code'], $data['amount']);
                    $cet_email_text = 'OffGamers customer had uploaded payment receipt.<br>Kindly process the order as soon as confirming payment received.';
                    if (isset($payment_mapper['pg_code']) && $payment_mapper['pg_code'] == 'indomog') {
                        //need to send attachment to indomog
                        $message = "<html> <head></head> <body> <p>Hi INDOMOG Customer Support,</p> <p>Greeting from " . $portalName . "<br><br>" . $portalName . " customer had uploaded payment receipt as attached in this email.<br>Kindly check your end and process the order as soon as confirming payment received.</p> <table> <th> <tr><td>Order Details:</td></tr> </th> <tr> <td>Order ID</td> <td>" . $data['txn_id'] . "</td> </tr> <tr> <td>Order Amount</td> <td>" . $checkoutDataAmount . "</td> </tr> <tr> <td>Payment Method</td> <td>" . $payment_mapper['pm_display_name'] . "</td> </tr> </table> <p>Thank You</p> <p>Regards,<br>" . $portalName . " Team</p> </body> </html>";
                        $subject = 'Please check payment for ' . $payment_mapper['pm_display_name'] . ' Order ' . $data['txn_id'];
                        $message = str_replace(array("\r", "\n"), '', $message);
                        $mailer = Yii::$app->mailer->compose()
                            ->setFrom([$sender_email => $sender])
                            ->setTo([Yii::$app->params['indomog.email'] => Yii::$app->params['indomog.receiver']])
                            ->setSubject($subject)
                            ->setHtmlBody($message);

                        foreach ($files as $file) {
                            $mailer->attach($file);
                        }

                        $mailer->send();

                        $cet_email_text = 'OffGamers customer had uploaded payment receipt.<br>Kindly check the payment status with INDOMOG (<EMAIL>) if this order does not process after an hour.';
                    }


                    //send to merchant support for all offline pm
                    $message = "<html><head></head> <body> <p>Hi OffGamers CET,</p> <p>$cet_email_text</p> <table> <th> <tr><td>Order Details:</td></tr> </th> <tr> <td>Order ID</td> <td>" . $data['txn_id'] . "</td> </tr> <tr> <td>Order Amount</td> <td>" . $checkoutDataAmount . "</td> </tr> <tr> <td>Payment Method</td> <td>" . $payment_mapper['pm_display_name'] . "</td> </tr> </table> <p>Thank You</p> </body> </html>";
                    $subject = 'Please check payment for ' . $payment_mapper['pm_display_name'] . ' Order ' . $data['txn_id'];
                    $message = str_replace(array("\r", "\n"), '', $message);
                    Yii::$app->mailer->compose()
                        ->setFrom([$sender_email => $sender])
                        ->setTo([$receiverEmail => $receiverName])
                        ->setSubject($subject)
                        ->setHtmlBody($message)
                        ->send();

                    foreach ($files as $file) {
                        @unlink($file);
                    }
                }
            }
        }
    }

    public function processRefund($orderId, $data, $payment_mapper)
    {
        if (isset($data['transaction_status']) && ($data['transaction_status'] == 20 || $data['transaction_status'] == 25)) {
            $store_refund = StoreRefund::findOne(['store_refund_trans_id' => $orderId]);
            if ($store_refund) {
                $pgRawData = (!empty($data['pg_raw_data']) ? Json::decode($data['pg_raw_data']) : []);
                $refund_id = $store_refund['store_refund_id'];
                //make sure the refund is not in complete status
                if ($store_refund['store_refund_status'] != '3') {
                    if (!empty($payment_mapper['pg_code'])) {
                        $insert_history = false;

                        $refund_history_sql_data_array = array(
                            'store_refund_id' => $refund_id,
                            'date_added' => date('Y-m-d H:i:s'),
                            'comments' => '',
                            'changed_by' => 'PGS',
                            'changed_by_role' => 'PGS'
                        );

                        switch ($payment_mapper['pg_code']) {
                            case 'paypalEC':
                                //success pg refund
                                if (isset($data['reverse_status']) && ($data['reverse_status'] == 10)) {
                                    // Insert exchange rate, update paid amount
                                    $refund_update_sql_data_array = array(
                                        'store_refund_status' => 3,
                                        'store_refund_is_processed' => 0,
                                        'store_refund_payments_reference' => $data['reverse_txn_id'], //use pg txn id instead of pw reverse id
                                        'store_refund_last_modified' => date('Y-m-d H:i:s')
                                    );
                                    $store_refund->load($refund_update_sql_data_array, '');
                                    $store_refund->save();

                                    $mcGross = (isset($pgRawData['mc_gross']) ? abs($pgRawData['mc_gross']) : 0);
                                    $mcFee = (isset($pgRawData['mc_fee']) ? abs($pgRawData['mc_fee']) : 0);
                                    $netRefund = $mcGross - $mcFee;
                                    $comments = "PayPal Refund Transaction ID: " . $data['reverse_txn_id'] . "\n" .
                                        "Gross Refund Amount: " . $mcGross . "\n" .
                                        "Fee Refund Amount: " . $mcFee . "\n" .
                                        "Net Refund Amount: " . $netRefund;
                                    $orders_status_history_sql_data_array = array(
                                        'orders_id' => $orderId,
                                        'orders_status_id' => 0,
                                        'date_added' => date('Y-m-d H:i:s'),
                                        'customer_notified' => 0,
                                        'comments' => $comments,
                                        'comments_type' => 1,
                                        'set_as_order_remarks' => 0,
                                        'changed_by' => 'PGS'
                                    );
                                    $orders_history = (new OrdersStatusHistory);
                                    $orders_history->load($orders_status_history_sql_data_array, '');
                                    $orders_history->save();

                                    // Insert refund payment history
                                    $refund_history_sql_data_array['store_refund_status'] = 3;
                                    $refund_history_sql_data_array['comments'] = $comments;
                                    $refund_history_sql_data_array['payee_notified'] = '1';
                                    $insert_history = true;
                                    //failed pg refund
                                } else {
                                    if (isset($data['reverse_status']) && ($data['reverse_status'] == 5)) {
                                        $refund_update_sql_data_array = array(
                                            'store_refund_is_processed' => 0,
                                            'store_refund_payments_reference' => $data['reverse_pw_id'],
                                            'store_refund_last_modified' => date('Y-m-d H:i:s')
                                        );
                                        $store_refund->load($refund_update_sql_data_array, '');
                                        $store_refund->save();

                                        $comments = "PayPal Refund Receive - failed " . $data['reverse_txn_id'] . "\n" .
                                            "Gross Refund Amount: " . (isset($pgRawData['mc_gross']) ? $pgRawData['mc_gross'] : 0) . "\n" .
                                            "Fee Refund Amount: " . (isset($pgRawData['mc_fee']) ? $pgRawData['mc_fee'] : 0) . "\n";

                                        $refund_history_sql_data_array['comments'] = $comments;
                                        $refund_history_sql_data_array['payee_notified'] = '1';
                                        $insert_history = true;
                                    }
                                }

                                break;
                            case 'alipay':
                                if (isset($pgRawData['result_details'])) {
                                    $result_details_array = explode("^", $pgRawData['result_details']);
                                    if (count($result_details_array) > 2) {
                                        $trade_no = (int)($result_details_array[0] ?? '');
                                        $refund_amt = (double)($result_details_array[1] ?? '');
                                        $status_flag = ($result_details_array[2] ?? '');
                                    }
                                }
                                //success pg refund
                                if (isset($data['reverse_status']) && ($data['reverse_status'] == 10)) {
                                    $refund_update_sql_data_array = array(
                                        'store_refund_status' => 3,
                                        'store_refund_is_processed' => 0,
                                        'store_refund_last_modified' => date('Y-m-d H:i:s')
                                    );
                                    $store_refund->load($refund_update_sql_data_array, '');
                                    $store_refund->save();

                                    $comments = "Alipay Refund Receive<BR>";
                                    $comments .= "Notify ID : " . ($pgRawData['notify_id'] ?? 'n/a') . "<BR>";
                                    $comments .= "Trade No.: " . (!empty($trade_no) ? $trade_no : 'n/a') . "<BR>";
                                    $comments .= "Refund Amount: " . (!empty($refund_amt) ? $refund_amt : 'n/a') . "<BR>";
                                    $comments .= "Status: " . (!empty($status_flag) ? $status_flag : 'n/a');

                                    $refund_history_sql_data_array['store_refund_status'] = 3;
                                    $refund_history_sql_data_array['comments'] = $comments;
                                    $refund_history_sql_data_array['payee_notified'] = '1';
                                    $insert_history = true;
                                    //failed pg refund
                                } else {
                                    if (isset($data['reverse_status']) && ($data['reverse_status'] == 5)) {
                                        $refund_update_sql_data_array = array(
                                            'store_refund_is_processed' => 0,
                                            'store_refund_last_modified' => date('Y-m-d H:i:s')
                                        );
                                        $store_refund->load($refund_update_sql_data_array, '');
                                        $store_refund->save();

                                        $comments = "Alipay Refund Receive - failed<BR>";
                                        $comments .= "Notify ID : " . ($pgRawData['notify_id'] ?? 'n/a') . "<BR>";
                                        $comments .= "Trade No.: " . (!empty($trade_no) ? $trade_no : 'n/a') . "<BR>";
                                        $comments .= "Refund Amount: " . (!empty($refund_amt) ? $refund_amt : 'n/a') . "<BR>";
                                        $comments .= "Status: " . (!empty($status_flag) ? $status_flag : 'n/a');

                                        $refund_history_sql_data_array['comments'] = $comments;
                                        $insert_history = true;
                                    }
                                }
                                break;
                            case 'bibit':
                                if (isset($data['reverse_status']) && ($data['reverse_status'] == 10)) {
                                    $refund_update_sql_data_array = array(
                                        'store_refund_status' => 3,
                                        'store_refund_is_processed' => 0,
                                        'store_refund_payments_reference' => $data['reverse_pw_id'],
                                        'store_refund_last_modified' => date('Y-m-d H:i:s')
                                    );
                                    $store_refund->load($refund_update_sql_data_array, '');
                                    $store_refund->save();

                                    $refund_history_sql_data_array['store_refund_status'] = 3;
                                    $refund_history_sql_data_array['comments'] = 'Success';
                                    $insert_history = true;
                                } else {
                                    if (isset($data['reverse_status']) && ($data['reverse_status'] == 5)) {
                                        $refund_history_sql_data_array['store_refund_status'] = 3;
                                        $refund_history_sql_data_array['comments'] = 'Failed';
                                        $insert_history = true;
                                    }
                                }
                        }

                        if ($insert_history) {
                            $store_refund_history = new StoreRefundHistory;
                            $store_refund_history->load($refund_history_sql_data_array, '');
                            $store_refund_history->save();
                        }
                    }
                }
            }
        }
    }

    private function updatePaymentInfo($orders_id, $pwPaymentInfo, $data)
    {
        if (isset($data['payment_method_code'])) {
            if (isset($pwPaymentInfo['pg_code'])) {
                $pgRawData = (!empty($data['pg_raw_data']) ? Json::decode($data['pg_raw_data']) : []);
                switch ($pwPaymentInfo['pg_code']) {
                    //payer email + payer id
                    case 'paypalEC':
                    case 'paypal':
                        if (isset($pgRawData['payer_id']) || isset($pgRawData['payer']['payer_id'])) {
                            $payer_id = ($pgRawData['payer_id'] ?? $pgRawData['payer']['payer_id']);
                            AnalysisPgInfo::updatePgInfo($orders_id, 'payer_id', $payer_id);
                        }

                        if (isset($pgRawData['payer_email']) || isset($pgRawData['payer']['email_address'])) {
                            $payer_email = ($pgRawData['payer_email'] ?? $pgRawData['payer']['email_address']);
                            AnalysisPgInfo::updatePgInfo($orders_id, 'payer_email', $payer_email, true);
                        }

                        if(isset($pgRawData['requery']['payer'])) {
                            if (isset($pgRawData['requery']['payer']['payer_id'])){
                                AnalysisPgInfo::updatePgInfo($orders_id, 'payer_id', $pgRawData['requery']['payer']['payer_id']);
                            }
                            if (isset($pgRawData['requery']['payer']['email_address'])){
                                AnalysisPgInfo::updatePgInfo($orders_id, 'payer_email', $pgRawData['requery']['payer']['email_address'], true);
                            }
                        }
                        break;
                    //moneybookers email
                    case 'moneybookers':
                        if (isset($pgRawData['pay_from_email'])) {
                            AnalysisPgInfo::updatePgInfo($orders_id, 'moneybooker_email', $pgRawData['pay_from_email'], true);
                        }
                        break;
                    //cc
                    case 'adyen':
                        if (isset($pgRawData['eventCode']) && $pgRawData['eventCode'] == 'AUTHORISATION') {
                            if (isset($pgRawData['additionalData_cardBin']) && isset($pgRawData['additionalData_cardSummary'])) {
                                $pan = $pgRawData['additionalData_cardBin'] . '******' . $pgRawData['additionalData_cardSummary'];
                            }
                            $ccData = array(
                                'fomatted_pan' => ($pan ?? ''),
                                'bin_number' => ($pgRawData['additionalData_cardBin'] ?? ''),
                                'card_summary' => ($pgRawData['additionalData_cardSummary'] ?? ''),
                                'expiry' => ($pgRawData['additionalData_expiryDate'] ?? ''),
                                'three_d_offered' => ($pgRawData['additionalData_threeDOffered'] ?? ''),
                                'three_d_result' => ($pgRawData['additionalData_threeDAuthenticated'] ?? ''),
                                'issuer_country' => ($pgRawData['additionalData_issuerCountry'] ?? ''),
                            );
                            AnalysisCreditCardInfo::updateCCInfo($orders_id, $ccData);
                        }
                        break;
                    case 'pipwavePG':
                        if (!empty($pwPaymentInfo['pg_display_name'])) {
                            switch ($pwPaymentInfo['pg_display_name']) {
                                case 'SafeCharge':
                                    if (isset($pgRawData['transactionType']) && ($pgRawData['transactionType'] == 'Auth' || $pgRawData['transactionType'] == 'Sale3D')) {
                                        $summary = '';
                                        $expiry = '';
                                        if (isset($pgRawData['cardNumber'])) {
                                            $cardDetails = preg_split('/[\*]+/', $pgRawData['cardNumber']);
                                            if (isset($cardDetails[1])) {
                                                $summary = $cardDetails[1];
                                            }
                                        }
                                        if (isset($pgRawData['bin'])) {
                                            $pan = $pgRawData['bin'] . '******' . $summary;
                                        }
                                        if (isset($pgRawData['expYear']) && isset($pgRawData['expMonth'])) {
                                            if (strlen($pgRawData['expYear']) == 2) {
                                                $pgRawData['expYear'] = '20' . $pgRawData['expYear'];
                                            }
                                            $expiry = $pgRawData['expMonth'] . '/' . $pgRawData['expYear'];
                                        }
                                        if (isset($pgRawData['eci'])) {
                                            switch ($pgRawData['eci']) {
                                                case '5': //visa
                                                case '2': //mc
                                                    //the cardholder was successfully authenticated
                                                    $threedAuth = 'true';
                                                    break;
                                                case '6': //visa
                                                case '1': //mc
                                                    //the issuer or cardholder does not participate in a 3D Secure program
                                                    $threedAuth = 'false';
                                                    break;
                                                case '7': //payment authentication was not performed
                                                    $threedAuth = 'false';
                                                    break;
                                            }
                                        }
                                        if ($pgRawData['transactionType'] == 'Auth') {
                                            $threedOffer = 'true';
                                        } else {
                                            if ($pgRawData['transactionType'] == 'Sale3D') {
                                                $threedOffer = 'false';
                                            }
                                        }
                                        $ccData = array(
                                            'fomatted_pan' => ($pan ?? ''),
                                            'bin_number' => ($pgRawData['bin'] ?? ''),
                                            'card_summary' => $summary,
                                            'expiry' => $expiry,
                                            'three_d_offered' => ($threedOffer ?? ''),
                                            'three_d_result' => ($threedAuth ?? ''),
                                            'issuer_country' => ($pgRawData['issuer_country'] ?? ''),
                                        );
                                        AnalysisCreditCardInfo::updateCCInfo($orders_id, $ccData);
                                    }
                                    break;
                                default:
                                    if (isset($data['payment_data']['credit_card'])) {
                                        $pmCcInfo = $data['payment_data']['credit_card'];
                                        $bin = '';
                                        $summary = '';
                                        $pan = '';
                                        $expiry = '';
                                        // Card No format
                                        if (isset($pmCcInfo['formatten_pan'])) {
                                            $cardDetails = preg_split('/[\*]+/', $pmCcInfo['formatten_pan']);
                                            if (isset($cardDetails[1])) {
                                                $bin = $cardDetails[0];
                                                $summary = $cardDetails[1];
                                                $pan = $bin . '******' . $summary;
                                            }
                                        }
                                        // Card Expiry format
                                        if (isset($pmCcInfo['card_expiry_month']) && isset($pmCcInfo['card_expiry_year'])) {
                                            $expiry = $pmCcInfo['card_expiry_month'] . '/' . $pmCcInfo['card_expiry_year'];
                                        }
                                        // 3d checking
                                        if (isset($pmCcInfo['threeds'])) {
                                            switch ($pmCcInfo['threeds']) {
                                                case '10': // Sucess
                                                    $threedOffer = 'true';
                                                    $threedAuth = 'true';
                                                    break;
                                                case '0': // Failed
                                                    $threedOffer = 'true';
                                                    $threedAuth = 'false';
                                                    break;
                                                case '5': // Bank/Card not enrolled
                                                    $threedOffer = 'false';
                                                    $threedAuth = 'false';
                                                    break;
                                            }
                                        }
                                        // Save data
                                        $ccData = array(
                                            'fomatted_pan' => $pan,
                                            'bin_number' => $bin,
                                            'card_summary' => $summary,
                                            'expiry' => $expiry,
                                            'three_d_offered' => ($threedOffer ?? ''),
                                            'three_d_result' => ($threedAuth ?? ''),
                                            'issuer_country' => ($pmCcInfo['issuer_country'] ?? ''),
                                        );
                                        AnalysisCreditCardInfo::updateCCInfo($orders_id, $ccData);
                                    }
                                    break;
                            }
                        }
                        break;
                    case 'global_collect':
                        $ccData = array(
                            'card_summary' => ($pgRawData['CCLASTFOURDIGITS'] ?? ''),
                            'expiry' => ($pgRawData['EXPIRYDATE'] ?? ''),
                            'three_d_result' => ($pgRawData['CVVRESULT'] ?? ''),
                        );
                        AnalysisCreditCardInfo::updateCCInfo($orders_id, $ccData);
                        break;
                }
            }
        }
    }

    public function updateKount($data, $customer_id)
    {
        if (isset($data['risk_management_data']) && !empty($data['risk_management_data'])) {
            $CustomersLoginIpHistory = new CustomersLoginIpHistory();
            $loginHistory = $CustomersLoginIpHistory->getLoginHistory($customer_id);
            $kountData = $data['risk_management_data'];
            $ApiTmTransactionIdentifier = new ApiTmTransactionIdentifier();
            if (isset($kountData['device_id'])) {
                //update kount data
                $kountMain = array(
                    'customers_id' => $customer_id,
                    'customers_login_ip' => ($loginHistory['customers_login_ip'] ?? ''),
                    'customers_login_date' => ($loginHistory['customers_login_date'] ?? date('Y-m-d H:i:s')),
                    'request_result' => (!empty($kountData) ? 'success' : 'na'),
                    'request_id' => $data['pw_id'],
                    'transaction_type' => 'CO',
                    'device_id' => $kountData['device_id'],
                    'create_datetime' => date('Y-m-d H:i:s'),
                    'transaction_id' => $data['txn_id']
                );
                $ApiTmTransactionIdentifier->updateKountInfo($data['txn_id'], $kountMain);
                $kountId = $ApiTmTransactionIdentifier->getKountId($data['txn_id']);
                $kountProxy = array(
                    'proxy_ip' => ($kountData['proxy_ip'] ?? ''),
                    'proxy_ip_city' => ($kountData['proxy_ip_city'] ?? ''),
                    'proxy_ip_geo' => ($kountData['proxy_ip_country'] ?? ''),
                    'proxy_ip_isp' => ($kountData['proxy_ip_organisation'] ?? ''),
                );
                $this->kountDataInsertion('proxy', $kountProxy, $kountId);
                $kountTrueIp = array(
                    'true_ip' => ($kountData['ip'] ?? ''),
                    'true_ip_city' => ($kountData['ip_city'] ?? ''),
                    'true_ip_geo' => ($kountData['ip_country'] ?? ''),
                    'true_ip_organization' => ($kountData['ip_organisation'] ?? ''),
                );
                $this->kountDataInsertion('true_ip', $kountTrueIp, $kountId);
                $kountDevice = array(
                    'device_id' => ($kountData['device_id'] ?? ''),
                    'screen_res' => ($kountData['screen_resolution'] ?? ''),
                    'device_first_seen' => ($kountData['device_first_seen'] ?? ''),
                );
                $this->kountDataInsertion('device', $kountDevice, $kountId);
                $kountBrowser = array(
                    'browser_language' => ($kountData['browser_language'] ?? ''),
                );
                $this->kountDataInsertion('browser', $kountBrowser, $kountId);
            }
        }
    }

    private function updateOrdersData($order_id, $customer_id, $data)
    {
        $classLogFilesComponent = new LogFilesComponent($customer_id);
        $customers_default_address_id = 0;
        $allCustomersInfoChangesMade = '';
        $customerChangesArray = array();
        $customerAddressChangesArray = array();
        $customerChangesFormattedArray = array();
        $customerAddressChangesFormattedArray = array();
        $oldLogInfo = Customers::getCustomerCurrentInfo($customer_id);

        if (!is_null($oldLogInfo)) {
            $customers_default_address_id = $oldLogInfo['customers_default_address_id'];
            $allCustomersInfoChangesMade = $oldLogInfo['customers_info_changes_made'];
        }

        //update customer billing (orders + profile)
        if (isset($data['billing_info']) && !empty($data['billing_info'])) {
            $billingInfo = $data['billing_info'];
            $country = null;
            if (isset($billingInfo['country_iso2']) && $billingInfo['country_iso2'] != '') {
                $country = Countries::getCountrybyISO2($billingInfo['country_iso2']);
                $orderBilling = array(
                    'customers_street_address' => ($billingInfo['address1'] ?? ''),
                    'customers_suburb' => ($billingInfo['address2'] ?? ''),
                    'customers_city' => ($billingInfo['city'] ?? ''),
                    'customers_postcode' => ($billingInfo['zip'] ?? ''),
                    'customers_state' => ($billingInfo['state'] ?? ''),
                    'customers_country' => $country->countries_name ?? '',
                    'customers_address_format_id' => $country->address_format_id ?? '',
                    'billing_name' => ($billingInfo['name'] ?? ''),
                    'billing_street_address' => ($billingInfo['address1'] ?? ''),
                    'billing_suburb' => ($billingInfo['address2'] ?? ''),
                    'billing_city' => ($billingInfo['city'] ?? ''),
                    'billing_postcode' => ($billingInfo['zip'] ?? ''),
                    'billing_state' => ($billingInfo['state'] ?? ''),
                    'billing_country' => $country->countries_name ?? '',
                    'billing_address_format_id' => $country->address_format_id ?? '',
                );

                $telephoneData = [];
                //update order table for mobile number
                if (isset($data['mobile_number']) && !empty($data['mobile_number'])) {
                    if (isset($data['mobile_number_country_iso2']) && !empty($data['mobile_number_country_iso2'])) {
                        $country = Countries::getCountryByISO2($data['mobile_number_country_iso2']);
                    }
                    $telephoneData = array(
                        'customers_country_international_dialing_code' => ($data['mobile_number_country_code'] ?? ''),
                        'customers_telephone' => $data['mobile_number'],
                        'customers_telephone_country' => $country->countries_name ?? '',
                    );
                }
                $orders = Orders::findOne($order_id);
                $orders->load(array_merge($orderBilling, $telephoneData), '');
                $orders->save();
            }
            $country_id = $country->countries_id ?? 0;

            $stateId = 0;
            if (isset($billingInfo['state']) && !empty($billingInfo['state'])) {
                $stateId = Zones::getStateIdByName($billingInfo['state']);
            }
            $new_billing_country = $country_id;
            $current_dialing_code_id = $oldLogInfo['customers_country_dialing_code_id'] ?? 0;
            if ($new_billing_country == $current_dialing_code_id) {
                $sql_address_data_array = array(
                    'entry_gender' => '',
                    'entry_firstname' => ($billingInfo['name'] ?? ''),
                    'entry_lastname' => '',
                    'entry_street_address' => ($billingInfo['address1'] ?? ''),
                    'entry_suburb' => ($billingInfo['address2'] ?? ''),
                    'entry_postcode' => ($billingInfo['zip'] ?? ''),
                    'entry_city' => ($billingInfo['city'] ?? ''),
                    'entry_state' => ($billingInfo['state'] ?? ''),
                    'entry_country_id' => $country_id,
                    'entry_zone_id' => $stateId,
                );

                $oldAddress = AddressBook::getAddressInfo($customers_default_address_id, $customer_id);

                if ($oldAddress) {
                    $oldAddressInfo['entry_street_address'] = $oldAddress->entry_street_address;
                    $oldAddressInfo['entry_suburb'] = $oldAddress->entry_suburb;
                    $oldAddressInfo['entry_postcode'] = $oldAddress->entry_postcode;
                    $oldAddressInfo['entry_city'] = $oldAddress->entry_city;
                    $oldAddressInfo['entry_state'] = $oldAddress->entry_state;
                    $oldAddressInfo['entry_country_id'] = $oldAddress->entry_country_id;
                    $oldAddressInfo['entry_zone_id'] = $oldAddress->entry_zone_id;

                    $customers_default_address_id = $oldAddress->address_book_id;
                    $oldAddress->load($sql_address_data_array, '');
                    $oldAddress->save();

                    if ((int)$oldAddress["entry_zone_id"] > 0) {
                        if ($oldAddress['entry_zone_id'] > 0) {
                            $oldAddressInfo['entry_state'] = Zones::getStateName($oldAddress['entry_country_id'], $oldAddress['entry_zone_id']);
                        }
                    }

                    $newAddress = AddressBook::getAddressInfo($customers_default_address_id, $customer_id);
                    $newAddressInfo['entry_street_address'] = $newAddress->entry_street_address;
                    $newAddressInfo['entry_suburb'] = $newAddress->entry_suburb;
                    $newAddressInfo['entry_postcode'] = $newAddress->entry_postcode;
                    $newAddressInfo['entry_city'] = $newAddress->entry_city;
                    $newAddressInfo['entry_state'] = $newAddress->entry_state;
                    $newAddressInfo['entry_country_id'] = $newAddress->entry_country_id;
                    $newAddressInfo['entry_zone_id'] = $newAddress->entry_zone_id;

                    if ((int)$newAddress["entry_zone_id"] > 0) {
                        $newAddressInfo['entry_state'] = Zones::getStateName($newAddressInfo['entry_country_id'], $newAddressInfo['entry_zone_id']);
                    }

                    $customerAddressChangesArray = $classLogFilesComponent->detectChanges($oldAddressInfo, $newAddressInfo);
                    $customerAddressChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerAddressChangesArray);
                }
            }
        }

        //update customer phone verification - customer_info_verification & order table ONLY (not profile)
        // rule 1: if the phone is not own by any customers (customers table), and it's verified, then update both customer_info_verification and customer profile table
        // rule 2: it the phone is own by a customers and VERIFIED in customers_info_verification, DO NOT update customers_info_verification table and profile table
        // rule 3: if the phone is own by a customers and NOT VERIFIED in customers_info_verification, update customers_info_verification and profile
        // eg. if customer A has phone no A in profile, customer B don have phone no A in profile but its verified before, customer c with phone no A can be update in both verification and customers table

        if (!empty($data['mobile_number_verification']) && ($data['mobile_number_verification'] == 'verified' || $data['mobile_number_verification'] == 'verified previously')) {
            if (isset($data['mobile_number']) && !empty($data['mobile_number'])) {
                if (isset($data['mobile_number_country_code'])) {
                    $phoneNumber = $data['mobile_number_country_code'] . $data['mobile_number'];
                } else {
                    $phoneNumber = $data['mobile_number'];
                }
                if (isset($data['mobile_number_country_iso2']) && !empty($data['mobile_number_country_iso2'])) {
                    $country = Countries::getCountryByISO2($data['mobile_number_country_iso2']);
                }
                $dialingId = 0;
                if (isset($country->countries_id)) {
                    $dialingId = $country->countries_id;
                }
                $updateProfile = true;
                $verifyPhone = true;
                $phoneVerifiedInfo = CustomersInfoVerification::getPhoneVerificationDetails($phoneNumber);
                if (!empty($phoneVerifiedInfo)) {
                    //exist in verification table
                    foreach ($phoneVerifiedInfo as $customerId => $phoneInfo) {
                        //other customers
                        if ($customerId != $customer_id) {
                            //others customers have this phone in profile already
                            if ($phoneInfo['customers_telephone'] == $data['mobile_number'] && $phoneInfo['customers_country_dialing_code_id'] == $dialingId) {
                                //and they have already verified the phone
                                if ($phoneInfo['info_verified'] == 1) {
                                    //do not update profile & do not verify the phone
                                    $updateProfile = false;
                                    $verifyPhone = false;
                                }
                            } else {
                                //do not exist in any other customer profile, default update both profile and verification table
                            }
                        } else {
                            //this customer
                            //already having this phone in profile, no need update again
                            if ($phoneInfo['customers_telephone'] == $data['mobile_number'] &&
                                $phoneInfo['customers_country_dialing_code_id'] == $dialingId) {
                                $updateProfile = false;
                            }
                            //already verified the phone, no need verify again
                            if (isset($phoneInfo['info_verified']) && $phoneInfo['info_verified'] == 1) {
                                //phone not yet verified but inserted into verification table
                                $verifyPhone = false;
                            }
                        }
                    }
                }

                if ($verifyPhone) {
                    CustomersInfoVerification::setInfoVerified($customer_id, $phoneNumber, 'telephone');
                }
                if ($updateProfile) {
                    $telephoneData = array(
                        'customers_country_dialing_code_id' => $country->countries_id ?? 0,
                        'customers_telephone' => $data['mobile_number']
                    );
                    Customers::updateCustomerPhone($customer_id, $telephoneData);
                    CustomersInfo::updateLastModify($customer_id);
                    $newLogInfo = Customers::getCustomerCurrentInfo($customer_id);
                    $customerChangesArray = $classLogFilesComponent->detectChanges($oldLogInfo, $newLogInfo);
                    $customerChangesFormattedArray = $classLogFilesComponent->constructLogMessage($customerChangesArray);
                }
            }
        }
        if (count($customerChangesArray) > 0 || count($customerAddressChangesArray) > 0) {
            $customerChangesArray = array_merge($customerChangesArray, $customerAddressChangesArray);
            $customerChangesFormattedArray = array_merge($customerChangesFormattedArray, $customerAddressChangesFormattedArray);

            $allCustomersInfoChangesMade = $classLogFilesComponent->contructChangesString($customerChangesArray, $allCustomersInfoChangesMade);

            if (count($customerChangesFormattedArray)) {
                $changesStr = 'Changes made:' . "\n";

                for ($i = 0; $i < count($customerChangesFormattedArray); $i++) {
                    if (count($customerChangesFormattedArray[$i])) {
                        foreach ($customerChangesFormattedArray[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $changesStr .= $res['text'] . "\n";
                            } else {
                                $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }
                if (!isset($newLogInfo)) {
                    $newLogInfo = Customers::getCustomerCurrentInfo($customer_id);
                }
                $classLogFilesComponent->insertCustomerHistoryLog($customer_id, $newLogInfo['customers_email_address'], $changesStr);
            }
            CustomersInfo::updateChangeMade($allCustomersInfoChangesMade, $customer_id);
        }
    }

    private function kountDataInsertion($type, $data, $kountId)
    {
        $data['api_tm_query_id'] = $kountId;
        switch ($type) {
            case 'proxy':
                $model = new ApiTmProxyIp();
                break;
            case 'true_ip':
                $model = new ApiTmTrueIp();
                break;
            case 'device':
                $model = new ApiTmDevice();
                break;
            case 'browser':
                $model = new ApiTmBrowser();
                break;
        }
        $model->updateKountInfo($kountId, $data);
    }

    private function paypalVerification($orders_id, $data, $payment_mapper, $customers_id, $full_name, $payment_methods_id)
    {
        if ($payment_mapper['pg_code'] == 'paypalEC' || $payment_mapper['pg_code'] == 'paypal') {
            if (isset($data['pg_reason'])) {
                $reason = '';
                if ($data['pg_reason'] == 'paymentreview') {
                    $reason = 'Review';
                } else {
                    if ($data['pg_reason'] == 'echeck') {
                        $reason = 'Echeck';
                    }
                }
                if ($reason) {
                    \micro\components\Order::customerInfoVerification($orders_id, $customers_id, $full_name, $data['shipping_info']['email'], $payment_methods_id, $reason);
                }
            }
        }
    }

    /**
     * @throws \Throwable
     */
    protected function obtainLock($orders_id)
    {
        $mutex_lock = sprintf(self::MUTEX_LOCK_NAME, $orders_id);
        $mutex_timeout = 15;
        if (!Yii::$app->mutex->acquire($mutex_lock, $mutex_timeout)) {
            throw new QueueRetryableException('Has not waited the lock.');
        }
        return true;
    }

    /**
     * @throws \Throwable
     */
    protected function releaseLock($orders_id)
    {
        $mutex_lock = sprintf(self::MUTEX_LOCK_NAME, $orders_id);
        Yii::$app->mutex->release($mutex_lock);
    }
}

