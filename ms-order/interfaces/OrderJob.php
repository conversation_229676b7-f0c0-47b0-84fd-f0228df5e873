<?php

namespace micro\interfaces;

use micro\components\Currency;
use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use offgamers\base\models\ms\Inventory;
use offgamers\base\traits\GuzzleTrait;
use micro\models\Orders;
use micro\models\OrdersProducts;
use micro\models\OrdersProductsExtraInfo;
use micro\models\OrdersTopUp;
use micro\models\OrdersCompensateProducts;
use offgamers\base\models\MsStoreCreditModel;
use offgamers\base\models\Currencies;
use micro\models\OrdersCustomProducts;
use micro\models\OrderVerifyQueueLog;
use micro\models\OrdersStatusHistory;

class OrderJob extends BaseObject implements JobInterface
{
    use GuzzleTrait;

    public $data;
    public $start_time;
    public $debug = false;
    private $raw_data = "";
    private $subject = "";
    private $text = "";
    private $param;
    private $sendAlert = false;
    private $sc_threshold;
    private $delay;
    private $reason;
    private $channel = 'MYCET';

    public function execute($queue)
    {
        /* @var $model bool|\offgamers\base\models\ms\BaseMsTrait */

        $this->param = $this->data['params'];
        if (isset($this->param['order_id']) && !empty($this->param['order_id'])) {
            $this->subject = 'Order Verification Alert : #' . $this->param['order_id'];
            $type = 'warning';
            $config = Yii::$app->params['orderjob'];
            $this->delay = $config['sqs_delay'];
            $this->sc_threshold = $config['sc_threshold'];
            $long_process_threshold = $config['long_process_threshold'];
            $this->text = "Order Id: " . $this->param['order_id'] . "\n";
            $sc_check = false;
            $this->raw_data = array();
            $orders = Orders::find()
                ->where(['orders_id' => $this->param['order_id']])->one();
            $this->raw_data += $this->amendRawData('orders_id', $this->param['order_id']);
            if (isset($orders)) {
                $this->raw_data += $this->amendRawData('orders_status', $orders->orders_status);

                switch ($orders->orders_status) {
                    case '3':
                        $this->text .= "Order status: Completed\n";
                        //check if this is bundle order
                        //check orders_product where custom_products_type_id <> 3 (not store credit) and products_bundle_id = 0 order by products_bundle_id
                        $op = OrdersProducts::find()
                            ->where(['orders_id' => $orders->orders_id])
                            ->andWhere(['=', 'products_bundle_id', '0'])
                            ->orderBy('orders_products_id')
                            ->all();
                        foreach ($op as $op_row) {
                            $this->raw_data += $this->amendRawData('orders_products_id', $op_row['orders_products_id']);
                            $op_bundle = OrdersProducts::find()
                                ->where(['orders_id' => $op_row['orders_id']])
                                ->andWhere(['parent_orders_products_id' => $op_row['orders_products_id']])
                                ->all();
                            if (!empty($op_bundle)) {
                                //loop bundle child items
                                foreach ($op_bundle as $op_bundle_row) {
                                    $this->raw_data += $this->amendRawData('orders_products_id', $op_bundle_row['orders_products_id']);
                                    $this->checkStockDelivery($orders, $op_bundle_row);
                                }
                            } else {
                                //check delivery type (DTU, SC or others)
                                //SC and others using same verification
                                $this->raw_data += $this->amendRawData('products_good_delivered_quantity', $op_row['products_good_delivered_quantity']);
                                $op_extra_info = OrdersProductsExtraInfo::find()
                                    ->where(['orders_products_id' => $op_row['orders_products_id']])
                                    ->andWhere(['=', 'orders_products_extra_info_key', 'delivery_mode'])
                                    ->one();

                                $products_type_extra_info = OrdersProductsExtraInfo::find()
                                    ->where(['orders_products_id' => $op_row['orders_products_id']])
                                    ->andWhere(['=', 'orders_products_extra_info_key', 'products_type'])
                                    ->one();

                                if (!empty($op_extra_info) && $op_extra_info->orders_products_extra_info_value == '6') {
                                    $delivery_mode = 'DTU';
                                } elseif ($op_row['custom_products_type_id'] == '3' || ($op_row['orders_products_is_compensate'] == '1' && $op_row['products_id'] == '-1' && $op_row['custom_products_type_id'] == '0')) {
                                    //Delivery type is store credit or compensate SC
                                    $delivery_mode = 'SC';
                                } elseif (!empty($products_type_extra_info) && $products_type_extra_info->orders_products_extra_info_value == '4') {
                                    $delivery_mode = 'PHYSICAL';
                                } else {
                                    $delivery_mode = 'Stock';
                                }
                                switch ($delivery_mode) {
                                    //DTU
                                    case 'DTU':
                                        $last_status_timestamp = OrdersStatusHistory::getLastStatusTimeByStatusId($this->param['order_id'], $orders->orders_status);
                                        $current_timestamp = time();
                                        $diff_timestamp = $current_timestamp - $last_status_timestamp;
                                        if ($diff_timestamp >= $long_process_threshold) {
                                            $this->checkDTUDelivery($orders, $op_row);
                                        } else {
                                            $this->pushSqsQueue($this->param, $this->delay);
                                        }
                                        break;
                                    case 'SC':
                                        $sc_check = true;
                                        $sc_order = array(
                                            'orders_products_id' => $op_row['orders_products_id'],
                                            'products_good_delivered_price' => $op_row['products_good_delivered_price']
                                        );
                                        $sc_order_list[] = $sc_order;
                                        break;
                                    case 'Stock':
                                        $this->checkStockDelivery($orders, $op_row);
                                        break;
                                    case 'PHYSICAL':
                                        break;
                                    default:
                                        $this->checkStockDelivery($orders, $op_row);
                                }
                            }
                        }
                        if ($sc_check) {
                            $this->checkSCDelivery($orders, $sc_order_list);
                        }
                        break;
                    case '1':
                        $queue_timestamp = $this->param['timestamp'];
                        $current_timestamp = time();
                        $diff_timestamp = $current_timestamp - $queue_timestamp;
                        //if order is pending more than 1 day then no more sqs queue
                        if ($diff_timestamp <= 86400) {
                            $this->pushSqsQueue($this->param, $this->delay);
                        }
                        break;
                    case '5':
                        break;
                    case '7': //verifying
                    case '8': //on-hold
                        $this->pushSqsQueue($this->param, $this->delay);
                        break;
                    default:
                        //check long process
                        $last_status_timestamp = OrdersStatusHistory::getLastStatusTimeByStatusId($this->param['order_id'], $orders->orders_status);
                        $current_timestamp = time();
                        $diff_timestamp = $current_timestamp - $last_status_timestamp;
                        if ($diff_timestamp >= $long_process_threshold and !isset($this->param['long_process_notified'])) {
                            $this->subject = 'Out of stock Notification : #' . $this->param['order_id'];
                            $this->param['long_process_notified'] = '1';
                            $this->reason = "Order pending process too long. " . $long_process_threshold . " Seconds.";
                            $this->text .= "Reason: " . $this->reason . "\n";
                            $this->sendAlert = true;
                            $this->channel = 'CNCET';
                        }
                        $this->pushSqsQueue($this->param, $this->delay);
                }
            } else {
                $this->reason = 'Order not found';
                $this->text .= $this->reason;
                $this->channel = 'DEFAULT';
                $this->sendAlert = false;
            }
            if ($this->sendAlert) {
                $this->sendMessage($this->subject, $type, $this->text);
                $orders_id = $op_row['orders_id'] ?? $this->param['order_id'];
                $date_purchased = $orders->date_purchased ?? '';
                $orders_products_id = $op_row['orders_products_id'] ?? '';
                $delivery_mode = $delivery_mode ?? '';
                $this->reason = $this->reason ?? '';
                $this->saveLog($orders_id, $orders_products_id, $date_purchased, $delivery_mode, $this->raw_data, $this->sendAlert, $this->reason);
            }
        }
    }

    private function checkStockDelivery($orders, $op_row)
    {
        $products_quantity = $op_row['products_good_delivered_quantity'];

        if (in_array($orders->orders_cb_status, [1, 2, 3])) {
            $products_reverse_quantity = $op_row['products_reversed_quantity'];
            $products_quantity += $products_reverse_quantity;
        }
        $result = $this->checkInventory($op_row['orders_products_id'], $products_quantity);
        $this->raw_data += $this->amendRawData('products_good_delivered_quantity', $op_row['products_good_delivered_quantity']);
        $this->raw_data += $this->amendRawData('products_reversed_quantity', $op_row['products_reversed_quantity']);
        $this->raw_data += $this->amendRawData('inventory_quantity', $result['count']);
        $this->raw_data += $this->amendRawData('cpc_id', $result['custom_products_code_id']);
        if ($result['reason'] != "") {
            $this->text .= "Order Product Id: " . $op_row['orders_products_id'] . "\n";
            $this->text .= "Product Name: " . $op_row['products_name'] . "\n";
            $this->text .= "Product Delivered/Reversed Quantity: " . $products_quantity . "\n";
            $this->text .= "Inventory Quantity: " . $result['count'] . "\n";
            $this->reason = $result['reason'];
            $this->text .= "Reason: " . $this->reason . "\n";
            $this->sendAlert = true;
        } else {
            $this->saveLog($op_row['orders_id'], $op_row['orders_products_id'], $orders->date_purchased, 'Stock', $this->raw_data, '', '');
        }
        return true;
    }

    private function checkDTUDelivery($orders, $op_row)
    {
        $products_quantity = $op_row['products_good_delivered_quantity'];
        $orders_topup = OrdersTopUp::find()
            ->where(['orders_products_id' => $op_row['orders_products_id']])
            ->one();
        if (!empty($orders_topup)) {
            $this->raw_data += $this->amendRawData('orders_topup_status', $orders_topup->top_up_status);
            $this->raw_data += $this->amendRawData('orders_topup_process_flag', $orders_topup->top_up_process_flag);
            switch ($orders_topup->top_up_status) {
                case '1': // Processing, resend to queue
                    $this->pushSqsQueue($this->param, $this->delay);
                    break;
                case '3':
                    if ($orders_topup->top_up_process_flag != '2' && $products_quantity > 0) {
                        $this->subject = 'Failed DTU Notification : #' . $op_row['orders_id'];
                        $this->text .= "Order Product Id: " . $op_row['orders_products_id'] . "\n";
                        $this->text .= "Top Up Id: " . $orders_topup['top_up_id'] . "\n";
                        $this->text .= "Product Name: " . $op_row['products_name'] . "\n";
                        $this->text .= "Product Delivered Quantity: " . $products_quantity . "\n";
                        $this->reason = "Top up process flag != 2 (Not completed).";
                        $this->text .= "Reason: " . $this->reason . "\n";
                        $this->sendAlert = true;
                    } else {
                        $this->saveLog($op_row['orders_id'], $op_row['orders_products_id'], $orders->date_purchased, 'DTU', $this->raw_data, '', '');
                    }
                    break;
                default:
                    if ($products_quantity > 0) {
                        $this->subject = 'Failed DTU Notification : #' . $op_row['orders_id'];
                        $this->text .= "Order Product Id: " . $op_row['orders_products_id'] . "\n";
                        $this->text .= "Top Up Id: " . $orders_topup['top_up_id'] . "\n";
                        $this->text .= "Product Name: " . $op_row['products_name'] . "\n";
                        $this->text .= "Product Delivered Quantity: " . $products_quantity . "\n";
                        $this->reason = "Top up status is not completed.";
                        $this->text .= "Reason: " . $this->reason . "\n";
                        $this->sendAlert = true;
                    } else {
                        $this->saveLog($op_row['orders_id'], $op_row['orders_products_id'], $orders->date_purchased, 'DTU', $this->raw_data, '', '');
                    }
            }
        } else {
            if ($products_quantity > 0) {
                $this->text .= "Order Product Id: " . $op_row['orders_products_id'] . "\n";
                $this->text .= "Product Name: " . $op_row['products_name'] . "\n";
                $this->text .= "Product Quantity: " . $products_quantity . "\n";
                $this->reason = "Top up record not found (orders_top_up).";
                $this->text .= "Reason: " . $this->reason . "\n";
                $this->sendAlert = true;
            }
        }
        return true;
    }

    private function checkSCDelivery($orders, $sc_order_list)
    {
        $sc = new MsStoreCreditModel();
        $cust_sc_currency = $sc->getStoreCreditCurrency($orders->customers_id);
        $sc_list = $sc->getStoreCreditTrans($orders->orders_id, $orders->date_purchased, $orders->last_modified);
        $currency = new Currency();
        $sc_order_total = 0;
        $sc_extra = 0;
        $sc_extra_perc = 0;
        $sc_delivered_total = 0;
        $sc_delivered_array = array();
        $conversion_rate = array();
        $sc_order_currency = "";
        foreach ($sc_order_list as $sc_order_row) {
            //convert sc_order to sc_currency
            $order_compensate = OrdersCompensateProducts::find()
                ->where(['=', 'orders_products_id', $sc_order_row['orders_products_id']])
                ->one();
            if (isset($order_compensate)) {
                //this sc is compensation
                $sc_order_price = $sc_order_row['products_good_delivered_price'];
                $sc_compensate_order_currency = $order_compensate->compensate_order_currency;
                $sc_extra = 0;
            } else {
                $sc_order_price = $sc_order_row['products_good_delivered_price'];
                $sc_order_currency = $this->getSCOrderCurrency($sc_order_row['orders_products_id']);
                $sc_extra_perc = $this->getSCPromotionPercentage($sc_order_row['orders_products_id']);
                $sc_extra = $sc_order_price * ($sc_extra_perc / 100);
            }
            $sc_order_total = bcadd($sc_order_total, $sc_order_price, 8);
            $sc_order_total = bcadd($sc_order_total, $sc_extra, 8);
        }

        if ($sc_order_total == 0 && is_null($cust_sc_currency)) {
            $this->saveLog($orders->orders_id, '', $orders->date_purchased, 'SC', $this->raw_data, '', '');
            return $this->sendAlert;
        }

        foreach ($sc_list as $sc_list_row) {
            if (in_array($sc_list_row['activity'], ["S", "C", "XS"])) {
                if ($sc_list_row['allow_negative'] == 1) {
                    $sc_list_row['transaction_amount'] = $sc_list_row['transaction_amount'] * -1;
                }
                if (isset($sc_delivered_array[$sc_list_row['transaction_currency']])) {
                    if (isset($sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']])) {
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['transaction_amount'] += $sc_list_row['transaction_amount'];
                    } else {
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['transaction_amount'] = $sc_list_row['transaction_amount'];
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['created_date'] = array();
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['raw_data'] = array();
                    }
                } else {
                    if (isset($sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']])) {
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['transaction_amount'] += $sc_list_row['transaction_amount'];
                    } else {
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['transaction_amount'] = $sc_list_row['transaction_amount'];
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['created_date'] = array();
                        $sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['raw_data'] = array();
                    }
                }
                array_push($sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['created_date'], $sc_list_row['created_date']);
                array_push($sc_delivered_array[$sc_list_row['transaction_currency']][$sc_list_row['activity']]['raw_data'], $sc_list_row);
            }
        }

        foreach ($sc_delivered_array as $sc_delivered_currency => $sc_delivered_activity_row) {
            $conv_rate_date = $orders->date_purchased;
            foreach ($sc_delivered_activity_row as $sc_activity => $sc_delivered_row) {
                if ($sc_activity == "C") {
                    $sc_currency = $sc_compensate_order_currency;
                } else {
                    $sc_currency = $sc_order_currency;
                }
                if ($sc_currency == $orders->currency) {
                    $type = 'sell';
                } else {
                    $type = 'buy';
                }
                if ($sc_delivered_row['transaction_amount'] != 0) {
                    if ($sc_currency != $sc_delivered_currency) {
                        $array = array();
                        foreach ($sc_delivered_row['raw_data'] as $sc_delivered_row_raw) {
                            $conv_rate_date = date("Y-m-d H:i:s", $sc_delivered_row_raw['created_date']);
                            $conversion = $currency->advanceCurrencyConversionHist($sc_delivered_row_raw['transaction_amount'], $conv_rate_date, $sc_delivered_currency, $sc_currency, false, 'sell', true);
                            $sc_delivered_amount_conv = $conversion['new_number'];
                            array_push($conversion_rate, $conversion);
                            $conversion = $currency->advanceCurrencyConversionHist($sc_delivered_amount_conv, $orders->date_purchased, $sc_currency, 'USD', false, $type);
                            $sc_delivered_amount = $conversion['new_number'];
                            array_push($conversion_rate, $conversion);
                            $sc_delivered_total = bcadd($sc_delivered_total, $sc_delivered_amount, 8);
                        }
                    } else {
                        $conversion = $currency->advanceCurrencyConversionHist($sc_delivered_row['transaction_amount'], $conv_rate_date, $sc_delivered_currency, 'USD', false, $type);
                        $sc_delivered_amount = $conversion['new_number'];
                        array_push($conversion_rate, $conversion);
                        $sc_delivered_total = bcadd($sc_delivered_total, $sc_delivered_amount, 8);
                    }
                }
            }
        }

        $this->raw_data += $this->amendRawData('sc_order_currency', $sc_order_currency);
        $this->raw_data += $this->amendRawData('sc_extra_perc', $sc_extra_perc);
        $this->raw_data += $this->amendRawData('sc_extra', $sc_extra);
        $this->raw_data += $this->amendRawData('sc_order_total', $sc_order_total);
        $this->raw_data += $this->amendRawData('sc_delivered_total', $sc_delivered_total);
        $this->raw_data += $this->amendRawData('sc_order_list', $sc_order_list);
        $this->raw_data += $this->amendRawData('ms_sc_list', $sc_list);
        $this->raw_data += $this->amendRawData('conversion_rate', $conversion_rate);

        if ($sc_order_total > 0 && $sc_delivered_total > 0) {
            $sc_diff_perc = (abs($sc_order_total - $sc_delivered_total) / (($sc_order_total + $sc_delivered_total) / 2)) * 100;
        } elseif (($sc_order_total == 0 && $sc_delivered_total != 0) || $sc_order_total > 0 && $sc_delivered_total == 0) {
            $sc_diff_perc = 100;
            $this->channel = 'DEFAULT';
        } else {
            $sc_diff_perc = 0;
        }

        if ($sc_diff_perc > $this->sc_threshold) {
            $this->text .= "Product Name: Store Credit\n";
            $this->text .= 'SC Order Currency: ' . $sc_currency . "\n";
            $this->text .= 'SC Currency: ' . $cust_sc_currency . "\n";
            $this->text .= "SC Order In Total (USD): " . $sc_order_total . "\n";
            $this->text .= "SC Delivered In Total (USD): " . $sc_delivered_total . "\n";
            $this->text .= "SC Difference in Percentage: " . round($sc_diff_perc, 4) . "\n";
            $this->reason = "Differences exceed threshold " . $this->sc_threshold . "%.";
            $this->text .= "Reason: " . $this->reason . "\n";
            $this->sendAlert = true;
        } else {
            $this->saveLog($orders->orders_id, '', $orders->date_purchased, 'SC', $this->raw_data, '', '');
        }
        return $this->sendAlert;
    }


    private function sendMessage($subject, $type, $text)
    {
        Yii::$app->mailer->compose()
            ->setFrom(Yii::$app->params['noreply.email'])
            ->setReplyTo(Yii::$app->params['noreply.email'])
            ->setTo(Yii::$app->params['cet.email'])
            ->setSubject($subject)
            ->setHtmlBody(nl2br($text))
            ->send();

        return true;
    }

    private function pushSqsQueue($param, $delay)
    {
        $payload = new \micro\interfaces\OrderJob(['data' => ['params' => $param]]);
        $sqs = Yii::$app->aws->getSQS('ORDER_QUEUE');
        $result = $sqs->pushMessage($payload, $delay);
        return $result;
    }

    private function checkInventory($orders_products_id, $products_quantity)
    {
        $result = array('count' => 0, 'custom_products_code_id' => "", 'reason' => "");
        $inventory = new Inventory();
        $response = $inventory->customRequest('inventory', 'check-custom-products-code-by-orders-products-id', $orders_products_id);
        if (empty($response)) {
            if ($products_quantity > 0) {
                $result['reason'] = "Custom products code not found.";
            }
        } else {
            $result['custom_products_code_id'] = array_column($response, 'custom_products_code_id');
            $result['count'] = count($response);
            if ($result['count'] != $products_quantity) {
                $result['reason'] = "Custom products code count is not tally.";
            } else {
                $result['reason'] = "";
            }
        }
        return $result;
    }

    private function amendRawData($label, $value)
    {
        return [$label => $value];
    }

    private function getSCPromotionPercentage($orders_products_id)
    {
        $orders_custom_products = OrdersCustomProducts::find()
            ->where(['orders_products_id' => $orders_products_id])
            ->andWhere(['orders_custom_products_key' => 'store_credit_promotion_percentage'])
            ->one();
        if (!empty($orders_custom_products)) {
            return $orders_custom_products->orders_custom_products_value;
        } else {
            return 0;
        }
    }

    private function getSCOrderCurrency($orders_products_id)
    {
        $orders_custom_products = OrdersCustomProducts::find()
            ->where(['orders_products_id' => $orders_products_id])
            ->andWhere(['orders_custom_products_key' => 'store_credit_currency'])
            ->one();


        if (!empty($orders_custom_products)) {
            $currency_code = Currencies::find()
                ->where(['currencies_id' => $orders_custom_products->orders_custom_products_value])
                ->one();
            if (!empty($currency_code)) {
                return $currency_code->code;
            }
        }
        return 0;
    }

    private function saveLog($orders_id, $orders_products_id, $orders_datetime, $orders_type, $raw_data, $status = false, $reason = '')
    {
        $order_verify_log = new OrderVerifyQueueLog();
        $order_verify_log->orders_id = $orders_id;
        $order_verify_log->orders_products_id = $orders_products_id;
        $order_verify_log->orders_datetime = $orders_datetime;
        switch ($orders_type)//1-DTU, 2-SC, 3-Stock, 4-others
        {
            case "DTU":
                $orders_type_id = 1;
                break;
            case "SC":
                $orders_type_id = 2;
                break;
            case "Stock":
                $orders_type_id = 3;
                break;
            default:
                $orders_type_id = 4;
        }
        $order_verify_log->orders_type = $orders_type_id;
        $status_id = 1;
        if ($status) {
            $status_id = 2;
        }
        $order_verify_log->verify_status = $status_id;
        $order_verify_log->reason = $reason;
        $order_verify_log->raw_data = json_encode($raw_data);
        $order_verify_log->save();
        return true;
    }
}