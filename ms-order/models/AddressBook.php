<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "address_book".
 *
 * @property int $address_book_id
 * @property int $customers_id
 * @property string $entry_gender
 * @property string|null $entry_company
 * @property string $entry_firstname
 * @property string $entry_lastname
 * @property string $entry_street_address
 * @property string|null $entry_suburb
 * @property string $entry_postcode
 * @property string $entry_city
 * @property string|null $entry_state
 * @property int $entry_country_id
 * @property int $entry_zone_id
 */
class AddressBook extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'address_book';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'entry_country_id', 'entry_zone_id'], 'integer'],
            [['entry_gender'], 'string', 'max' => 1],
            [['entry_company', 'entry_firstname', 'entry_lastname', 'entry_suburb', 'entry_city', 'entry_state'], 'string', 'max' => 32],
            [['entry_street_address'], 'string', 'max' => 64],
            [['entry_postcode'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'address_book_id' => 'Address Book ID',
            'customers_id' => 'Customers ID',
            'entry_gender' => 'Entry Gender',
            'entry_company' => 'Entry Company',
            'entry_firstname' => 'Entry Firstname',
            'entry_lastname' => 'Entry Lastname',
            'entry_street_address' => 'Entry Street Address',
            'entry_suburb' => 'Entry Suburb',
            'entry_postcode' => 'Entry Postcode',
            'entry_city' => 'Entry City',
            'entry_state' => 'Entry State',
            'entry_country_id' => 'Entry Country ID',
            'entry_zone_id' => 'Entry Zone ID',
        ];
    }

    public function updateAddressInfo($customerId, $data) {
        $params = array(':customers_id' => $customerId);
        $condition = 'customers_id =:customers_id';
        self::updateAll($data, $condition, $params);
    }

    public static function getAddressInfo($addressBookId, $customerId) {
        $customerData = '';
//        if (!$addressBookId) {
//            $customerData = Customers::findOne($customerId);
//            if ($customerData) {
//                $addressBookId = $customerData->customers_default_address_id;
//            }
//        }
        $addressBookData = (self::findOne(['address_book_id' => $addressBookId, 'customers_id' => $customerId]) ?? new self);
        if ($addressBookData->isNewRecord) {
            if (!$customerData) {
                $customerData = Customers::findOne($customerId);
                $data = [
                    'customers_id' => $customerId,
                    'entry_gender' => $customerData->customers_gender,
                    'entry_firstname' => $customerData->customers_firstname,
                    'entry_lastname' => $customerData->customers_lastname
                ];
                $addressBookData->load($data,'');
                if($addressBookData->save()){
                    $customerData->customers_default_address_id = $addressBookData->address_book_id;
                    $customerData->save();
                }
            }
        }
        return $addressBookData;
    }
}
