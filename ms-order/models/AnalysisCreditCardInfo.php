<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "analysis_credit_card_info".
 *
 * @property int $orders_id
 * @property string|null $fomatted_pan
 * @property string|null $bin_number
 * @property string|null $card_summary
 * @property string|null $expiry
 * @property string|null $three_d_offered
 * @property string|null $three_d_result
 * @property string|null $issuer_country
 * @property string $created_at
 */
class AnalysisCreditCardInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'analysis_credit_card_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id'], 'integer'],
            [['created_at'], 'safe'],
            [['fomatted_pan'], 'string', 'max' => 32],
            [['bin_number'], 'string', 'max' => 6],
            [['card_summary'], 'string', 'max' => 4],
            [['expiry'], 'string', 'max' => 7],
            [['three_d_offered'], 'string', 'max' => 5],
            [['three_d_result'], 'string', 'max' => 11],
            [['issuer_country'], 'string', 'max' => 2],
            [['orders_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'fomatted_pan' => 'Fomatted Pan',
            'bin_number' => 'Bin Number',
            'card_summary' => 'Card Summary',
            'expiry' => 'Expiry',
            'three_d_offered' => 'Three D Offered',
            'three_d_result' => 'Three D Result',
            'issuer_country' => 'Issuer Country',
            'created_at' => 'Created At',
        ];
    }

    public static function updateCCInfo($orders_id, $data)
    {
        $model = (self::findOne(['orders_id' => $orders_id]) ?? new self);

        if ($model->isNewRecord) {
            $model->orders_id = $orders_id;
            $model->created_at = date('Y-m-d H:i:s');
        }
        
        $model->load($data, '');
        if($model->validate()){
            $model->save();
        } else {
            $model->save(false);
        }
    }

}
