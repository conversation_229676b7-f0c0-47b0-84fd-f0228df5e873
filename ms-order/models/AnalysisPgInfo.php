<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "analysis_pg_info".
 *
 * @property int $orders_id
 * @property string $info_key
 * @property string|null $info_value
 * @property string $created_at
 */
class AnalysisPgInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'analysis_pg_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id'], 'integer'],
            [['created_at'], 'safe'],
            [['info_key'], 'string', 'max' => 128],
            [['info_value'], 'string', 'max' => 255],
            [['orders_id', 'info_key'], 'unique', 'targetAttribute' => ['orders_id', 'info_key']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'info_key' => 'Info Key',
            'info_value' => 'Info Value',
            'created_at' => 'Created At',
        ];
    }

    public static function updatePgInfo($orders_id, $key, $value, $skip_update = false)
    {
        $model = (self::findOne(['orders_id' => $orders_id, 'info_key' => $key]) ?? new self);

        if ($model->isNewRecord) {
            $model->load([
                'orders_id' => $orders_id,
                'info_key' => $key,
                'created_at' => date('Y-m-d H:i:s'),
            ], '');
        } elseif ($skip_update) {
            return false;
        }

        $model->info_value = $value;
        $model->save();
    }
}
