<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_tm_browser".
 *
 * @property int $api_tm_query_id
 * @property string $browser_language
 * @property string $browser_string
 * @property string $enabled_js
 * @property string $enabled_fl
 * @property string $enabled_ck
 * @property string $enabled_im
 * @property string $css_image_loaded
 * @property string $flash_version
 * @property string $flash_lang
 * @property string $flash_os
 * @property string $headers_name_value_hash
 * @property string $headers_order_string_hash
 * @property string $http_os_signature
 * @property string $http_referer
 * @property string $plugin_adobe_acrobat
 * @property string $plugin_flash
 * @property string $plugin_hash
 * @property string $plugin_silverlight
 */
class ApiTmBrowser extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_tm_browser';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['api_tm_query_id'], 'integer'],
            [['browser_language', 'css_image_loaded', 'flash_version', 'flash_os', 'http_os_signature', 'plugin_adobe_acrobat', 'plugin_flash', 'plugin_silverlight'], 'string', 'max' => 32],
            [['browser_string'], 'string', 'max' => 200],
            [['enabled_js', 'enabled_fl', 'enabled_ck', 'enabled_im'], 'string', 'max' => 3],
            [['flash_lang'], 'string', 'max' => 10],
            [['headers_name_value_hash', 'headers_order_string_hash', 'http_referer', 'plugin_hash'], 'string', 'max' => 36],
            [['api_tm_query_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_tm_query_id' => 'Api Tm Query ID',
            'browser_language' => 'Browser Language',
            'browser_string' => 'Browser String',
            'enabled_js' => 'Enabled Js',
            'enabled_fl' => 'Enabled Fl',
            'enabled_ck' => 'Enabled Ck',
            'enabled_im' => 'Enabled Im',
            'css_image_loaded' => 'Css Image Loaded',
            'flash_version' => 'Flash Version',
            'flash_lang' => 'Flash Lang',
            'flash_os' => 'Flash Os',
            'headers_name_value_hash' => 'Headers Name Value Hash',
            'headers_order_string_hash' => 'Headers Order String Hash',
            'http_os_signature' => 'Http Os Signature',
            'http_referer' => 'Http Referer',
            'plugin_adobe_acrobat' => 'Plugin Adobe Acrobat',
            'plugin_flash' => 'Plugin Flash',
            'plugin_hash' => 'Plugin Hash',
            'plugin_silverlight' => 'Plugin Silverlight',
        ];
    }

    public static function updateKountInfo($kountId, $data)
    {
        $model = (self::findOne(['api_tm_query_id' => $kountId]) ?? new self);
        $model->load($data, '');
        $model->save();
    }
}
