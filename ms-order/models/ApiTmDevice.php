<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_tm_device".
 *
 * @property int $api_tm_query_id
 * @property string $device_id
 * @property string $device_result
 * @property string $os
 * @property string $screen_res
 * @property float $local_time_offset
 * @property float $local_time_offset_range
 * @property int $time_zone
 * @property int $device_score
 * @property string $device_attributes
 * @property string $device_activities
 * @property string $device_assert_history
 * @property string|null $device_last_update
 * @property int $device_worst_score
 * @property int $profiling_datetime
 * @property string|null $device_first_seen
 * @property string|null $device_last_event
 * @property string $device_match_result
 * @property int $offset_measure_time
 * @property string $os_anomaly
 * @property string $os_fonts_hash
 * @property string $os_fonts_number
 */
class ApiTmDevice extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_tm_device';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['api_tm_query_id', 'time_zone', 'device_score', 'device_worst_score', 'profiling_datetime', 'offset_measure_time'], 'integer'],
            [['local_time_offset', 'local_time_offset_range'], 'number'],
            [['device_last_update', 'device_first_seen', 'device_last_event'], 'safe'],
            [['device_id', 'os_fonts_hash'], 'string', 'max' => 36],
            [['device_result', 'device_match_result', 'os_fonts_number'], 'string', 'max' => 10],
            [['os'], 'string', 'max' => 32],
            [['screen_res'], 'string', 'max' => 12],
            [['device_attributes', 'device_activities', 'device_assert_history'], 'string', 'max' => 64],
            [['os_anomaly'], 'string', 'max' => 3],
            [['api_tm_query_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_tm_query_id' => 'Api Tm Query ID',
            'device_id' => 'Device ID',
            'device_result' => 'Device Result',
            'os' => 'Os',
            'screen_res' => 'Screen Res',
            'local_time_offset' => 'Local Time Offset',
            'local_time_offset_range' => 'Local Time Offset Range',
            'time_zone' => 'Time Zone',
            'device_score' => 'Device Score',
            'device_attributes' => 'Device Attributes',
            'device_activities' => 'Device Activities',
            'device_assert_history' => 'Device Assert History',
            'device_last_update' => 'Device Last Update',
            'device_worst_score' => 'Device Worst Score',
            'profiling_datetime' => 'Profiling Datetime',
            'device_first_seen' => 'Device First Seen',
            'device_last_event' => 'Device Last Event',
            'device_match_result' => 'Device Match Result',
            'offset_measure_time' => 'Offset Measure Time',
            'os_anomaly' => 'Os Anomaly',
            'os_fonts_hash' => 'Os Fonts Hash',
            'os_fonts_number' => 'Os Fonts Number',
        ];
    }

    public static function updateKountInfo($kountId, $data)
    {
        $model = (self::findOne(['api_tm_query_id' => $kountId]) ?? new self);
        $model->load($data, '');
        $model->save();
    }
}
