<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_tm_proxy_ip".
 *
 * @property int $api_tm_query_id
 * @property string $proxy_ip
 * @property int $proxy_ip_score
 * @property string $proxy_ip_attributes
 * @property string $proxy_ip_activities
 * @property string $proxy_ip_assert_history
 * @property string|null $proxy_ip_last_update
 * @property int $proxy_ip_worst_score
 * @property string $proxy_ip_city
 * @property string $proxy_ip_geo
 * @property string $proxy_ip_isp
 * @property float $proxy_ip_latitude
 * @property float $proxy_ip_longitude
 * @property string $proxy_type
 * @property string|null $proxy_ip_first_seen
 * @property string|null $proxy_ip_last_event
 * @property string $proxy_ip_organization
 * @property string $proxy_ip_region
 * @property string $proxy_ip_result
 */
class ApiTmProxyIp extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_tm_proxy_ip';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['api_tm_query_id', 'proxy_ip_score', 'proxy_ip_worst_score'], 'integer'],
            [['proxy_ip_last_update', 'proxy_ip_first_seen', 'proxy_ip_last_event'], 'safe'],
            [['proxy_ip_latitude', 'proxy_ip_longitude'], 'number'],
            [['proxy_ip', 'proxy_ip_attributes', 'proxy_ip_activities'], 'string', 'max' => 64],
            [['proxy_ip_assert_history', 'proxy_ip_city', 'proxy_ip_isp'], 'string', 'max' => 50],
            [['proxy_ip_geo'], 'string', 'max' => 2],
            [['proxy_type', 'proxy_ip_region'], 'string', 'max' => 32],
            [['proxy_ip_organization'], 'string', 'max' => 100],
            [['proxy_ip_result'], 'string', 'max' => 10],
            [['api_tm_query_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_tm_query_id' => 'Api Tm Query ID',
            'proxy_ip' => 'Proxy Ip',
            'proxy_ip_score' => 'Proxy Ip Score',
            'proxy_ip_attributes' => 'Proxy Ip Attributes',
            'proxy_ip_activities' => 'Proxy Ip Activities',
            'proxy_ip_assert_history' => 'Proxy Ip Assert History',
            'proxy_ip_last_update' => 'Proxy Ip Last Update',
            'proxy_ip_worst_score' => 'Proxy Ip Worst Score',
            'proxy_ip_city' => 'Proxy Ip City',
            'proxy_ip_geo' => 'Proxy Ip Geo',
            'proxy_ip_isp' => 'Proxy Ip Isp',
            'proxy_ip_latitude' => 'Proxy Ip Latitude',
            'proxy_ip_longitude' => 'Proxy Ip Longitude',
            'proxy_type' => 'Proxy Type',
            'proxy_ip_first_seen' => 'Proxy Ip First Seen',
            'proxy_ip_last_event' => 'Proxy Ip Last Event',
            'proxy_ip_organization' => 'Proxy Ip Organization',
            'proxy_ip_region' => 'Proxy Ip Region',
            'proxy_ip_result' => 'Proxy Ip Result',
        ];
    }

    public static function updateKountInfo($kountId, $data)
    {
        $model = (self::findOne(['api_tm_query_id' => $kountId]) ?? new self);
        $model->load($data, '');
        $model->save();
    }
}
