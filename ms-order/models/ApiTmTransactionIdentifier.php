<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_tm_transaction_identifier".
 *
 * @property int $api_tm_query_id
 * @property int $transaction_id
 * @property int $customers_id
 * @property string $customers_login_ip
 * @property string $customers_login_date
 * @property string $request_result
 * @property string $request_id
 * @property string $transaction_type
 * @property string $device_id
 * @property string $create_datetime
 * @property string $query_string
 * @property string $missing_field_bk
 */
class ApiTmTransactionIdentifier extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_tm_transaction_identifier';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['transaction_id', 'customers_id'], 'integer'],
            [['customers_login_date', 'create_datetime'], 'safe'],
            [['query_string', 'missing_field_bk'], 'string'],
            [['customers_login_ip'], 'string', 'max' => 128],
            [['request_result', 'request_id'], 'string', 'max' => 64],
            [['transaction_type'], 'string', 'max' => 2],
            [['device_id'], 'string', 'max' => 36],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_tm_query_id' => 'Api Tm Query ID',
            'transaction_id' => 'Transaction ID',
            'customers_id' => 'Customers ID',
            'customers_login_ip' => 'Customers Login Ip',
            'customers_login_date' => 'Customers Login Date',
            'request_result' => 'Request Result',
            'request_id' => 'Request ID',
            'transaction_type' => 'Transaction Type',
            'device_id' => 'Device ID',
            'create_datetime' => 'Create Datetime',
            'query_string' => 'Query String',
            'missing_field_bk' => 'Missing Field Bk',
        ];
    }

    public function saveKountInfo($data)
    {
        self::load($data, '');
        if (self::validate()) {
            return self::save();
        } else {
            var_dump(self::getErrors());
        }
        return self::save();
    }

    public static function updateKountInfo($kountId, $data)
    {
        $model = (self::findOne(['transaction_id' => $kountId]) ?? new self);
        $model->load($data, '');
//        $model->customers_login_date =  '0000-00-00 00:00:00';
        $model->save();
    }

    public function getKountId($orderId)
    {
        return self::find()->select(['api_tm_query_id'])
            ->where(['transaction_id' => $orderId])
            ->asArray()
            ->scalar();
    }
}
