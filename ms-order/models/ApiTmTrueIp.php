<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "api_tm_true_ip".
 *
 * @property int $api_tm_query_id
 * @property string $true_ip
 * @property string $true_ip_activities
 * @property string $true_ip_assert_history
 * @property string $true_ip_attributes
 * @property string $true_ip_city
 * @property string $true_ip_geo
 * @property string $true_ip_isp
 * @property string|null $true_ip_last_update
 * @property float $true_ip_latitude
 * @property float $true_ip_longitude
 * @property int $true_ip_worst_score
 * @property int $true_ip_score
 * @property string|null $true_ip_first_seen
 * @property string|null $true_ip_last_event
 * @property string $true_ip_organization
 * @property string $true_ip_region
 * @property string $true_ip_result
 */
class ApiTmTrueIp extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api_tm_true_ip';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['api_tm_query_id', 'true_ip_worst_score', 'true_ip_score'], 'integer'],
            [['true_ip_last_update', 'true_ip_first_seen', 'true_ip_last_event'], 'safe'],
            [['true_ip_latitude', 'true_ip_longitude'], 'number'],
            [['true_ip'], 'string', 'max' => 128],
            [['true_ip', 'true_ip_activities', 'true_ip_attributes'], 'string', 'max' => 64],
            [['true_ip_assert_history'], 'string', 'max' => 3],
            [['true_ip_city', 'true_ip_isp'], 'string', 'max' => 50],
            [['true_ip_geo'], 'string', 'max' => 2],
            [['true_ip_organization'], 'string', 'max' => 100],
            [['true_ip_region'], 'string', 'max' => 32],
            [['true_ip_result'], 'string', 'max' => 10],
            [['api_tm_query_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'api_tm_query_id' => 'Api Tm Query ID',
            'true_ip' => 'True Ip',
            'true_ip_activities' => 'True Ip Activities',
            'true_ip_assert_history' => 'True Ip Assert History',
            'true_ip_attributes' => 'True Ip Attributes',
            'true_ip_city' => 'True Ip City',
            'true_ip_geo' => 'True Ip Geo',
            'true_ip_isp' => 'True Ip Isp',
            'true_ip_last_update' => 'True Ip Last Update',
            'true_ip_latitude' => 'True Ip Latitude',
            'true_ip_longitude' => 'True Ip Longitude',
            'true_ip_worst_score' => 'True Ip Worst Score',
            'true_ip_score' => 'True Ip Score',
            'true_ip_first_seen' => 'True Ip First Seen',
            'true_ip_last_event' => 'True Ip Last Event',
            'true_ip_organization' => 'True Ip Organization',
            'true_ip_region' => 'True Ip Region',
            'true_ip_result' => 'True Ip Result',
        ];
    }

    public static function updateKountInfo($kountId, $data)
    {
        $model = (self::findOne(['api_tm_query_id' => $kountId]) ?? new self);
        $model->load($data, '');
        $model->save();
    }
}
