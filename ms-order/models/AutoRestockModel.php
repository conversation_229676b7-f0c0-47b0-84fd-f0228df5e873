<?php

namespace micro\models;

use Yii;
use offgamers\base\models\ms\Inventory;
use offgamers\base\models\ms\Product;
use offgamers\base\models\Countries;

class AutoRestockModel extends \yii\base\Model
{
    public $orders_id, $orders_products_id, $quantity, $sku, $orders_products_price, $customers_id;

    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'quantity', 'customers_id'], 'integer'],
            [['sku'], 'string'],
            [['orders_products_price'], 'number']
        ];
    }

    public function processRestock()
    {
        $restock_model = new Inventory();

        $data = $this->getOrderDetail();

        $data['quantity'] = $this->quantity;

        return $restock_model->restock($data);
    }

    public function getRestockItemDetail()
    {
        $data = $this->getOrderDetail();
        $data['quantity'] = 1;
        return $data;
    }

    private function getOrderDetail()
    {
        $product_model = new Product();

        $op_model = OrdersProducts::findOne($this->orders_products_id);
        $this->orders_id = $op_model->orders_id;
        $o_model = Orders::findOne($this->orders_id);

        if ($op_model) {
            $products_id = $op_model->products_id;

            $product_selling_price = $op_model->final_price;

            if ($op_model->parent_orders_products_id) {
                $parent_op_model = OrdersProducts::findOne($op_model->parent_orders_products_id);
                if ($parent_op_model) {
                    // Temporary Fix to deliver product
                    $info = $product_model->getProductInfoByOrdersId(['products_id' => $products_id, 'parent_products_id' => $parent_op_model->products_id]);
                    $product_selling_price = $parent_op_model->final_price * $info['bundle_info']['price_ratio'];
                } else {
                    throw new \Exception('Missing Parent Orders Products Id ' . $this->orders_products_id);
                }
            }
            else{
                $info = $product_model->getProductInfoByOrdersId(['products_id' => $products_id]);
            }

            $data = [
                'orders_id' => $op_model->orders_id,
                'orders_currency' => $o_model->currency,
                'orders_products_id' => $op_model->orders_products_id,
                'order_quantity' => $op_model->products_quantity,
                'sku' => (!empty($this->sku) ? $this->sku : $op_model->products_model),
                'orders_products_currency_price' => $product_selling_price * $o_model->currency_value,
                'orders_products_price' => Yii::$app->currency->advanceCurrencyConversion($product_selling_price * $o_model->currency_value, $o_model->currency, 'USD', false, 'spot'),
                'products_id' => $products_id,
                'products_name' => $op_model->products_name,
                'publisher_id' => ($info['products_type'] == 2 ? $info['publisher_id'] : null),
                'product_cost' => (!empty($info['product_cost']) ? $info['product_cost'] : $info['product_original_cost']),
                'product_cost_currency' => (!empty($info['product_cost_currency']) ? $info['product_cost_currency'] : $info['product_original_currency']),
                'customers_id' => $o_model->customers_id,
                'phone_country_code' => (Countries::find()->select(['countries_iso_code_2'])->where(['countries_international_dialing_code' => $o_model->customers_country_international_dialing_code])->scalar() ?? 'US'),
                'customers_state' => (!empty($o_model->customers_state) ? $o_model->customers_state : 'N/A'),
            ];
        } else {
            throw new \Exception('Missing Orders Products ' . $this->orders_products_id);
        }

        return $data;
    }

}