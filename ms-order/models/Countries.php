<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "countries".
 *
 * @property int $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property int|null $countries_currencies_id
 * @property string|null $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property int $address_format_id
 * @property int $maxmind_support
 * @property string $aft_risk_type
 * @property int $countries_display
 * @property int $telesign_support
 */
class Countries extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'countries';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['countries_currencies_id', 'address_format_id', 'maxmind_support', 'countries_display', 'telesign_support'], 'integer'],
            [['countries_name', 'countries_website_domain'], 'string', 'max' => 64],
            [['countries_iso_code_2'], 'string', 'max' => 2],
            [['countries_iso_code_3'], 'string', 'max' => 3],
            [['countries_international_dialing_code'], 'string', 'max' => 5],
            [['aft_risk_type'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'countries_id' => 'Countries ID',
            'countries_name' => 'Countries Name',
            'countries_iso_code_2' => 'Countries Iso Code 2',
            'countries_iso_code_3' => 'Countries Iso Code 3',
            'countries_currencies_id' => 'Countries Currencies ID',
            'countries_international_dialing_code' => 'Countries International Dialing Code',
            'countries_website_domain' => 'Countries Website Domain',
            'address_format_id' => 'Address Format ID',
            'maxmind_support' => 'Maxmind Support',
            'aft_risk_type' => 'Aft Risk Type',
            'countries_display' => 'Countries Display',
            'telesign_support' => 'Telesign Support',
        ];
    }

    public static function getCountryDialingCode($countryId)
    {
        $result = self::find()->select('countries_id, countries_international_dialing_code, countries_iso_code_2')->where(['countries_id' => $countryId])->asArray()->one();

        return $result;
    }

    public static function getCountryNamebyISO2($countryISO2)
    {
        $result = self::find()->select('countries_name, countries_id')->where(['countries_iso_code_2' => $countryISO2])->asArray()->one();

        return $result;
    }

    public static function getCountryByISO2($countryISO2)
    {
        $result = self::find()->where(['countries_iso_code_2' => $countryISO2])->one();

        return $result;
    }

    public static function getCountryName($countryId) {
        $result = self::find()->select('countries_name')->where($countryId);

        return $result;
    }
}
