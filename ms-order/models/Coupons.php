<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "coupons".
 *
 * @property int $coupon_id
 * @property int $coupon_generation_id
 * @property string $coupon_type
 * @property string $coupon_code
 * @property string $coupon_amount
 * @property string $coupon_minimum_order
 * @property string $max_cap
 * @property string $coupon_start_date
 * @property string $coupon_expire_date
 * @property int $uses_per_coupon
 * @property string $uses_per_coupon_unlimited
 * @property int $uses_per_user
 * @property string $uses_per_user_unlimited
 * @property string $restrict_to_products
 * @property string $restrict_to_categories
 * @property string $restrict_to_customers
 * @property string $restrict_to_customers_groups
 * @property string $restrict_to_currency_id
 * @property string $restrict_to_payment_id
 * @property string $coupon_active
 * @property int $mobile_only
 * @property string $date_created
 * @property string $date_modified
 */
class Coupons extends \yii\db\ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'coupons';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'date_created',
                'updatedAtAttribute' => 'date_modified',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['coupon_generation_id', 'uses_per_coupon', 'uses_per_user'], 'integer'],
            [['coupon_amount', 'coupon_minimum_order', 'max_cap'], 'number'],
            [['coupon_start_date', 'coupon_expire_date', 'date_created', 'date_modified'], 'safe'],
            [['restrict_to_products', 'restrict_to_categories', 'restrict_to_customers', 'restrict_to_currency_id', 'restrict_to_payment_id'], 'string'],
            [['coupon_type', 'uses_per_coupon_unlimited', 'uses_per_user_unlimited', 'coupon_active'], 'string', 'max' => 1],
            [['coupon_code'], 'string', 'max' => 32],
            [['restrict_to_customers_groups'], 'string', 'max' => 64],
            [['mobile_only'], 'integer', 'min' => 0, 'max' => 1],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'coupon_id' => 'Coupon ID',
            'coupon_generation_id' => 'Coupon Generation ID',
            'coupon_type' => 'Coupon Type',
            'coupon_code' => 'Coupon Code',
            'coupon_amount' => 'Coupon Amount',
            'coupon_minimum_order' => 'Coupon Minimum Order',
            'max_cap' => 'Max Cap',
            'coupon_start_date' => 'Coupon Start Date',
            'coupon_expire_date' => 'Coupon Expire Date',
            'uses_per_coupon' => 'Uses Per Coupon',
            'uses_per_coupon_unlimited' => 'Uses Per Coupon Unlimited',
            'uses_per_user' => 'Uses Per User',
            'uses_per_user_unlimited' => 'Uses Per User Unlimited',
            'restrict_to_products' => 'Restrict To Products',
            'restrict_to_categories' => 'Restrict To Categories',
            'restrict_to_customers' => 'Restrict To Customers',
            'restrict_to_customers_groups' => 'Restrict To Customers Groups',
            'restrict_to_currency_id' => 'Restrict To Currency ID',
            'restrict_to_payment_id' => 'Restrict To Payment ID',
            'coupon_active' => 'Coupon Active',
            'mobile_only' => 'Mobile Only',
            'date_created' => 'Date Created',
            'date_modified' => 'Date Modified',
        ];
    }
}
