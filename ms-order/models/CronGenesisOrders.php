<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "cron_genesis_orders".
 *
 * @property int $orders_id
 * @property int $flag
 * @property int|null $re_run
 * @property string|null $last_modified
 */
class CronGenesisOrders extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cron_genesis_orders';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id', 'flag', 're_run'], 'integer'],
            [['last_modified'], 'safe'],
            [['orders_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'flag' => 'Flag',
            're_run' => 'Re Run',
            'last_modified' => 'Last Modified',
        ];
    }
}
