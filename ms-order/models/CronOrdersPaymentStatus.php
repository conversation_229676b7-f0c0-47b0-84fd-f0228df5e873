<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "cron_orders_payment_status".
 *
 * @property int $orders_id
 * @property int $counter
 * @property string $check_date
 */
class CronOrdersPaymentStatus extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cron_orders_payment_status';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id', 'counter'], 'integer'],
            [['check_date'], 'safe'],
            [['orders_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'counter' => 'Counter',
            'check_date' => 'Check Date',
        ];
    }
}
