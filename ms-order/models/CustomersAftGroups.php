<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "customers_aft_groups".
 *
 * @property int $customers_aft_groups_id
 * @property string $customers_aft_groups_name
 * @property int $sort_order
 */
class CustomersAftGroups extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_aft_groups';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sort_order'], 'integer'],
            [['customers_aft_groups_name'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_aft_groups_id' => 'Customers Aft Groups ID',
            'customers_aft_groups_name' => 'Customers Aft Groups Name',
            'sort_order' => 'Sort Order',
        ];
    }
}
