<?php

namespace micro\models;

use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "customers_info".
 *
 * @property int $customers_info_id
 * @property string|null $customers_info_date_of_last_logon
 * @property int|null $customer_info_account_dormant
 * @property int|null $customers_info_number_of_logons
 * @property string|null $customers_info_date_account_created
 * @property string|null $customers_info_account_created_ip
 * @property string|null $account_created_country
 * @property int $customers_info_account_created_from
 * @property string|null $account_created_site
 * @property string|null $customers_info_date_account_last_modified
 * @property string $customers_info_changes_made
 * @property int|null $global_product_notifications
 * @property int $customer_info_selected_country
 * @property int $customer_info_selected_language_id
 */
class CustomersInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_info_id', 'customers_info_changes_made'], 'required'],
            [
                [
                    'customers_info_id',
                    'customer_info_account_dormant',
                    'customers_info_number_of_logons',
                    'customers_info_account_created_from',
                    'global_product_notifications',
                    'customer_info_selected_country',
                    'customer_info_selected_language_id'
                ],
                'integer'
            ],
            [['customers_info_date_of_last_logon', 'customers_info_date_account_created', 'customers_info_date_account_last_modified'], 'safe'],
            [['customers_info_changes_made'], 'string'],
            [['customers_info_account_created_ip'], 'string', 'max' => 128],
            [['account_created_country'], 'string', 'max' => 2],
            [['account_created_site'], 'string', 'max' => 32],
            [['customers_info_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_info_id' => 'Customers Info ID',
            'customers_info_date_of_last_logon' => 'Customers Info Date Of Last Logon',
            'customer_info_account_dormant' => 'Customer Info Account Dormant',
            'customers_info_number_of_logons' => 'Customers Info Number Of Logons',
            'customers_info_date_account_created' => 'Customers Info Date Account Created',
            'customers_info_account_created_ip' => 'Customers Info Account Created Ip',
            'account_created_country' => 'Account Created Country',
            'customers_info_account_created_from' => 'Customers Info Account Created From',
            'account_created_site' => 'Account Created Site',
            'customers_info_date_account_last_modified' => 'Customers Info Date Account Last Modified',
            'customers_info_changes_made' => 'Customers Info Changes Made',
            'global_product_notifications' => 'Global Product Notifications',
            'customer_info_selected_country' => 'Customer Info Selected Country',
            'customer_info_selected_language_id' => 'Customer Info Selected Language ID',
        ];
    }

    public static function updateChangeMade($change_log, $uid) {
        $m_ci = self::findOne($uid);
        if ($m_ci) {
            $m_ci->customers_info_changes_made = $change_log;
            $m_ci->save();
        }
    }

    public static function getSignupInfo($customerId)
    {
        $result = self::find()->select('customers_info_account_created_ip, customers_info_date_account_created, account_created_country')->where(['customers_info_id' => $customerId])->asArray()->one();
        if (empty($result)) {
            return [];
        }
        return array(
            'signup_ip' => $result['customers_info_account_created_ip'],
            'signup_date' => $result['customers_info_date_account_created'],
            'signup_country' => $result['account_created_country'],
        );
    }

    public static function updateLastModify($customers_id)
    {
        $params = array(':customers_id' => $customers_id);
        $condition = 'customers_info_id =:customers_id';
        self::updateAll(['customers_info_date_account_last_modified' => new Expression('NOW()')], $condition, $params);
    }
}
