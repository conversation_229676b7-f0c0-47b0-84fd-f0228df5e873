<?php

namespace micro\models;

use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "customers_info_verification".
 *
 * @property int $customers_id
 * @property string $customers_info_value
 * @property string|null $serial_number
 * @property int $verify_try_turns
 * @property int $info_verified
 * @property string $info_verification_type
 * @property string $customers_info_verification_mode
 * @property string|null $customers_info_verification_date
 * @property string|null $call_language
 */
class CustomersInfoVerification extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_info_verification';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_info_value', 'info_verification_type'], 'required'],
            [['customers_id', 'verify_try_turns', 'info_verified'], 'integer'],
            [['customers_info_verification_date'], 'safe'],
            [['customers_info_value'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['info_verification_type'], 'string', 'max' => 32],
            [['customers_info_verification_mode'], 'string', 'max' => 1],
            [['call_language'], 'string', 'max' => 40],
            [['customers_id', 'customers_info_value', 'info_verification_type'], 'unique', 'targetAttribute' => ['customers_id', 'customers_info_value', 'info_verification_type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'customers_info_value' => 'Customers Info Value',
            'serial_number' => 'Serial Number',
            'verify_try_turns' => 'Verify Try Turns',
            'info_verified' => 'Info Verified',
            'info_verification_type' => 'Info Verification Type',
            'customers_info_verification_mode' => 'Customers Info Verification Mode',
            'customers_info_verification_date' => 'Customers Info Verification Date',
            'call_language' => 'Call Language',
        ];
    }


    public static function isInfoVerified($customerId, $verifyInfo, $type)
    {
        $result = self::find()->select('info_verified, customers_info_verification_mode')->where([
            'customers_id' => $customerId,
            'info_verification_type' => $type,
            'customers_info_value' => $verifyInfo
        ])->asArray()->one();

        return $result;
    }

    public static function setInfoVerified($customerId, $verifyInfo, $type)
    {
        $model = (self::findOne(['customers_id' => $customerId, 'customers_info_value' => $verifyInfo, 'info_verification_type' => $type]) ?? new self);

        $model->load([
            'customers_id' => $customerId,
            'customers_info_value' => (string)$verifyInfo,
            'serial_number' => '',
            'info_verified' => 1,
            'info_verification_type' => 'telephone',
            'customers_info_verification_date' => date('Y-m-d H:i:s'),
            'customers_info_verification_mode' => 'A'
        ], '');

        $model->save();
    }

    public static function getPhoneVerificationDetails($phoneNo)
    {
        $query = new Query;
        $result = $query->select([
            'c.customers_id',
            'customers_telephone',
            'info_verified',
            'customers_phone_verified',
            'customers_country_dialing_code_id'
        ])
            ->from(Customers::tableName() . ' c')
            ->innerJoin(self::tableName() . ' ci', 'c.customers_id = ci.customers_id')
            ->where(['ci.customers_info_value' => $phoneNo, 'ci.info_verification_type' => 'telephone'])
            ->all();

        return ArrayHelper::index($result, 'customers_id');
    }

}
