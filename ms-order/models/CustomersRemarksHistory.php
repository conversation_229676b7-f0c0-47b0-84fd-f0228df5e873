<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "customers_remarks_history".
 *
 * @property int $customers_remarks_history_id
 * @property int $customers_id
 * @property string $date_remarks_added
 * @property string $remarks
 * @property string $remarks_added_by
 */
class CustomersRemarksHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_remarks_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id'], 'integer'],
            [['date_remarks_added'], 'safe'],
            [['remarks'], 'string'],
            [['remarks_added_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_remarks_history_id' => 'Customers Remarks History ID',
            'customers_id' => 'Customers ID',
            'date_remarks_added' => 'Date Remarks Added',
            'remarks' => 'Remarks',
            'remarks_added_by' => 'Remarks Added By',
        ];
    }
}
