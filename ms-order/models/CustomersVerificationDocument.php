<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "customers_verification_document".
 *
 * @property int $customers_id
 * @property string|null $files_001
 * @property int $files_001_locked
 * @property int $files_001_status
 * @property string|null $files_002
 * @property int $files_002_locked
 * @property int $files_002_status
 * @property string|null $files_003
 * @property int $files_003_locked
 * @property int $files_003_status 0 = Pending, 1 = Approved, 2 = Denied
 * @property string|null $files_004
 * @property int $files_004_locked
 * @property int $files_004_status
 * @property string|null $files_005
 * @property int $files_005_locked
 * @property int $files_005_status
 */
class CustomersVerificationDocument extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_verification_document';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id'], 'required'],
            [
                [
                    'customers_id',
                    'files_001_locked',
                    'files_001_status',
                    'files_002_locked',
                    'files_002_status',
                    'files_003_locked',
                    'files_003_status',
                    'files_004_locked',
                    'files_004_status',
                    'files_005_locked',
                    'files_005_status'
                ],
                'integer'
            ],
            [['files_001', 'files_002', 'files_003', 'files_004', 'files_005'], 'string', 'max' => 32],
            [['customers_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'files_001' => 'Files 001',
            'files_001_locked' => 'Files 001 Locked',
            'files_001_status' => 'Files 001 Status',
            'files_002' => 'Files 002',
            'files_002_locked' => 'Files 002 Locked',
            'files_002_status' => 'Files 002 Status',
            'files_003' => 'Files 003',
            'files_003_locked' => 'Files 003 Locked',
            'files_003_status' => 'Files 003 Status',
            'files_004' => 'Files 004',
            'files_004_locked' => 'Files 004 Locked',
            'files_004_status' => 'Files 004 Status',
            'files_005' => 'Files 005',
            'files_005_locked' => 'Files 005 Locked',
            'files_005_status' => 'Files 005 Status',
        ];
    }

    public static function getKycStatus($customerId)
    {
        $result = self::findOne($customerId);

        if ($result) {
            switch ($result->files_003_status) {
                case 1:
                    return 'approved';
                case 2:
                    return 'denied';
                case 0:
                default:
                    return 'pending';
            }
        }

        return 'new';
    }
}
