<?php

namespace micro\models;

use Yii;
use offgamers\base\models\ms\Inventory;
use offgamers\base\models\ms\Product;
use yii\base\Exception;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class DirectTopUpModel extends \yii\base\Model
{
    public $orders_id, $orders_products_id, $quantity, $orders_products_price, $game_account_info;

    public function rules()
    {
        return [
            [['orders_id', 'orders_products_id', 'quantity'], 'integer'],
            [['orders_products_price'], 'number'],
            [['game_account_info'], 'safe']
        ];
    }

    public function processOrder()
    {
        $data = $this->getOrderDetail();
        return $data;
    }

    private function getOrderDetail()
    {
        $product_model = new Product();

        $op_model = OrdersProducts::findOne($this->orders_products_id);

        $otu_model = OrdersTopUp::findOne(['orders_products_id' => $this->orders_products_id]);
        $return_array = [];
        $response = [];

        if ($otu_model && $op_model) {
            $o_model = Orders::findOne($op_model->orders_id);
            $op_extra_info = ArrayHelper::map(OrdersProductsExtraInfo::find()->where(['orders_products_id' => $op_model->orders_products_id])->asArray()->all(), 'orders_products_extra_info_key',
                'orders_products_extra_info_value');
            $otu_model->updateProductCost($op_model, $op_extra_info);

            $this->createSubOrder($otu_model->top_up_id, $op_model->products_quantity);

            if ($op_model) {
                $product_selling_price = $op_model->final_price;
                if ($op_model->parent_orders_products_id) {
                    $parent_op_model = OrdersProducts::findOne($op_model->parent_orders_products_id);
                    if ($parent_op_model) {
                        // Temporary Fix to deliver product
                        $product_selling_price = $parent_op_model->final_price;
                    } else {
                        throw new Exception('Missing Parent Orders Products Id ' . $this->orders_products_id);
                    }
                }

                $product_info = $product_model->customRequest('product', 'get-dtu-product-info', [
                    'products_id' => $op_model->products_id,
                    'products_type' => $op_extra_info['products_type'],
                    'sub_products_id' => ($op_extra_info['sub_products_id'] ?? 0)
                ]);

                if ($op_extra_info['products_type'] == 3) {
                    list($prefix, $phone) = explode(" ", $this->game_account_info['account']);
                    $this->addMobileRechargeRecord([
                        'customer_id' => $o_model->customers_id,
                        'prefix' => $prefix,
                        'phone_no' => $phone,
                        'deno_id' => $op_extra_info['sub_products_id'],
                        'orders_id' => $op_model->orders_id,
                        'orders_products_id' => $op_model->orders_products_id,
                        'status' => 0,
                        'hide_from_listing' => 0
                    ]);
                }

                foreach (OrdersTopUpSubOrder::find()->where(['top_up_id' => $otu_model->top_up_id])->andWhere(['!=', 'status', 1])->andWhere(['!=', 'status', 3])->andWhere([
                    '!=',
                    'status',
                    5
                ])->all() as $sub_order) {
                    $data = [
                        'customer_id' => $o_model->customers_id,
                        'orders_id' => $op_model->orders_id,
                        'orders_currency' => $o_model->currency,
                        'orders_products_id' => $op_model->orders_products_id,
                        'products_id' => $op_model->products_id,
                        'sub_products_id' => (!empty($op_extra_info['sub_products_id']) ? $op_extra_info['sub_products_id'] : 0),
                        'orders_top_up_id' => $otu_model->top_up_id,
                        'sub_orders_top_up_id' => $sub_order->id,
                        'sub_orders_top_up_status' => $sub_order->status,
                        'publisher_order_id' => $sub_order->publisher_ref_id,
                        'quantity' => $op_model->products_quantity,
                        'orders_products_price' => Yii::$app->currency->advanceCurrencyConversion($product_selling_price * $o_model->currency_value, $o_model->currency, 'USD', false, 'spot'),
                        'orders_products_currency_price' => $product_selling_price * $o_model->currency_value,
                        'products_name' => $op_model->products_name,
                        'game_account_info' => $this->game_account_info,
                        'product_cost' => $otu_model->currency_settle_amount,
                        'product_cost_currency' => $otu_model->currency_code,
                        'publisher_id' => $product_info['publisher_id'],
                        'publisher_product_id' => (isset($product_info['publisher_product_id']) ? $product_info['publisher_product_id'] : '')
                    ];

                    if ($op_extra_info['products_type'] == 3) {
                        $data['game_account_info']['operator_id'] = $product_info['operator_id'];
                        $data['game_account_info']['deno_id'] = $product_info['deno_id'];
                        $data['game_account_info']['type'] = $product_info['bundle_type'];
                    }

                    // Set Sub Order Status to processing before sending, to prevent duplicate order case
                    $sub_order->status = '3';
                    $sub_order->save();

                    $restock_model = new Inventory();
                    $response = $restock_model->customRequest('direct-top-up', 'process-order', $data);

                    if (isset($response['status']) && $response['status'] == true & $response['error_code'] == "0" && isset($response['publisher_ref_id'])) {
                        $sub_order->status = 1;
                        $sub_order->publisher_ref_id = $response['publisher_ref_id'];
                        $sub_order->save();

                    } else {
                        // Set order status to fail, when error code is returned
                        if (!empty($response['error_code']) && $response['error_code'] != '9999') {
                            if (!empty($response['new_request_id'])) {
                                $sub_order->status = '5';
                            } else {
                                $sub_order->status = '2';
                            }
                            if (!empty($response['publisher_ref_id'])) {
                                $sub_order->publisher_ref_id = $response['publisher_ref_id'];
                            }
                            $sub_order->save();
                        }

                        $return_array['top_up_status'] = ($response['top_up_status'] ?? 'failed');
                        $return_array['error_msg'] = ($response['error_msg'] ?? '');
                        $return_array['error_code'] = ($response['error_code'] ?? 1001);

                        break;
                    }
                }

                $time = new Expression('NOW()');
                $otu_model->refresh();
                $otu_model->publishers_response_time = $time;
                $otu_model->top_up_timestamp = $time;
                $otu_model->top_up_process_flag = '2';

                if (OrdersTopUpSubOrder::find()->where(['top_up_id' => $otu_model->top_up_id])->andWhere(['status' => 1])->count() == $op_model->products_quantity) {
                    $otu_model->top_up_status = '3';
                    $otu_model->publishers_ref_id = $otu_model->top_up_id;
                    $otu_model->result_code = 2000;
                    $otu_model->save();

                    if ($op_extra_info['products_type'] == 3) {
                        $this->updateMobileRechargeRecord([
                            'customer_id' => $o_model->customers_id,
                            'orders_products_id' => $op_model->orders_products_id,
                            'publisher_order_id' => ($response['publisher_ref_id'] ?? ''),
                            'status' => 1
                        ]);
                    }

                    $return_array = ['result_code' => 2000, 'top_up_status' => 'reloaded', 'publisher_ref_id' => $otu_model->top_up_id];
                } else {
                    $otu_model->top_up_status = '10';
                    $otu_model->result_code = ($return_array['error_code'] ?? 1001);
                    $otu_model->save();
                    $return_array = array_merge($return_array, ['result_code' => $otu_model->result_code]);

                    if ($op_extra_info['products_type'] == 3) {
                        $this->updateMobileRechargeRecord([
                            'customer_id' => $o_model->customers_id,
                            'orders_products_id' => $op_model->orders_products_id,
                            'publisher_order_id' => ($response['publisher_ref_id'] ?? ''),
                            'status' => 2
                        ]);
                    }
                }

                $remark_model = new OrdersTopUpRemark();
                $remark_model->top_up_id = $otu_model->top_up_id;
                $remark_model->data_added = $time;
                $remark_model->changed_by = 'system';
                $remark_model->remark = ($otu_model->top_up_status == 3 ? 'Top-up: Reloaded' : 'Top-up: Failed ' . ($return_array['error_msg'] ?? ''));
                $remark_model->save();
            }

            return $return_array;
        }
    }

    private function addMobileRechargeRecord($data)
    {
        $model = MobileRechargeHistory::find()->where(['orders_products_id' => $data['orders_products_id']])->one();
        if (!$model) {
            $model = new MobileRechargeHistory;
            $model->load($data, '');
            $model->save();
            Yii::$app->cache->delete(Yii::$app->params['frontend.memcache.prefix'] . 'mobile-recharge/get-recharge-history/' . $data['customer_id']);
        }
        return $model;
    }

    private function updateMobileRechargeRecord($data)
    {
        $model = MobileRechargeHistory::find()->where(['orders_products_id' => $data['orders_products_id']])->one();
        $model->load($data, '');
        $model->save();
    }

    private function createSubOrder($top_up_id, $quantity)
    {
        //TODO some dtu orders process quantity * face value in single order
        $sub_order_qty = OrdersTopUpSubOrder::find()->where(['top_up_id' => $top_up_id])->andWhere(['!=', 'status', 5])->count();

        if ($quantity > $sub_order_qty) {
            $timestamp = time();
            for ($i = 0; $i < $quantity - $sub_order_qty; $i++) {
                $insert_array[] = [$top_up_id, 0, $timestamp, $timestamp];
            }

            if ($insert_array) {
                Yii::$app->db->createCommand()->batchInsert(OrdersTopUpSubOrder::tableName(), ['top_up_id', 'status', 'created_at', 'updated_at'], $insert_array)->execute();
            }

            $sub_order_qty = OrdersTopUpSubOrder::find()->where(['top_up_id' => $top_up_id])->andWhere(['!=', 'status', 5])->count();
        }

        if ($sub_order_qty > $quantity) {
            throw new \Exception('Sub Order ID More than Ordered Quantity');
        }
    }

    public static function resetTopUpStatus($data)
    {
        if (isset($data['sub_order_id'])) {
            $sub_order = OrdersTopUpSubOrder::findOne(['id' => $data['sub_order_id']]);
            $otu_model = OrdersTopUp::findOne(['top_up_id' => $sub_order->top_up_id]);
            if ($otu_model->top_up_status != '3') {
                $otu_model->top_up_status = '1';
                $otu_model->top_up_process_flag = '0';
                $otu_model->top_up_last_processed_time = new Expression('NOW()');
                $otu_model->save();

                $remark_model = new OrdersTopUpRemark();
                $remark_model->top_up_id = $otu_model->top_up_id;
                $remark_model->data_added = new Expression('NOW()');
                $remark_model->changed_by = 'system';
                $remark_model->remark = 'Publisher Postback';
                $remark_model->save();
            }
        }
    }

}