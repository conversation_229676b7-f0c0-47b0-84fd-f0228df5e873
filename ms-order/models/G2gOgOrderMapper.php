<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "g2g_og_order_mapper".
 *
 * @property int $id
 * @property int $og_order_id
 * @property string $g2g_order_id
 * @property string $delivery_id
 * @property string $extra_info
 * @property int $status
 * @property int $error
 * @property int $created_at
 * @property int $updated_at
 */
class G2gOgOrderMapper extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'g2g_og_order_mapper';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['og_order_id', 'g2g_order_id', 'delivery_id', 'created_at', 'updated_at'], 'required'],
            [['og_order_id', 'status', 'error', 'created_at', 'updated_at'], 'integer'],
            [['extra_info'], 'string'],
            [['g2g_order_id', 'delivery_id'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'og_order_id' => 'Og Order ID',
            'g2g_order_id' => 'G2g Order ID',
            'delivery_id' => 'Delivery ID',
            'extra_info' => 'Extra Info',
            'status' => 'Status',
            'error' => 'Error',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
