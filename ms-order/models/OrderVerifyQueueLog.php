<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\Json;

/**
 * This is the model class for table "order_verify_queue_log".
 *
 * @property int $orders_id
 * @property int $orders_products_id
 * @property string $orders_datetime
 * @property int $orders_type
 * @property int $verify_tatus
 * @property string $reason
 * @property string $raw_data
 */
class OrderVerifyQueueLog extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_verify_queue_log';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => false,
                'updatedAtAttribute' => false,
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id', 'orders_products_id'], 'integer'],
            [['orders_type', 'verify_status'], 'integer', 'max' => 4],
            [['orders_datetime'], 'safe'],
            [['reason'], 'string', 'max' => 255],
            [['raw_data'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders Id',
            'orders_products_id' => 'Orders Products Id',
            'orders_datetime' => 'Orders Datetime',
            'orders_type' => 'Orders Type',
            'verify_status' => 'Status',
            'reason' => 'Reason',
            'raw_data' => "Raw Data",
        ];
    }
}
