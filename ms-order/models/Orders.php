<?php

namespace micro\models;

use Yii;
use yii\db\Exception;
use yii\db\Query;
use yii\helpers\Json;

class Orders extends OrdersBase
{
    public function getTotalOrderAmount($customers_id, $start_time, $end_time)
    {
        $start_time = date('Y-m-d H:i:s', $start_time);
        $end_time = date('Y-m-d H:i:s', $end_time);
        $total = 0;

        $data_row = (new Query())
            ->select(['SUM(op.products_good_delivered_price) as total', 'ot.value as ot_value', 'gv.value as gv_value'])
            ->from(Orders::tableName() . ' o')
            ->join('LEFT JOIN', OrdersProducts::tableName() . ' op', 'o.orders_id = op.orders_id')
            ->join('LEFT JOIN', OrdersTotal::tableName() . ' ot', 'o.orders_id = ot.orders_id AND ot.class = "ot_total"')
            ->join('LEFT JOIN', OrdersTotal::tableName() . ' gv', 'o.orders_id = gv.orders_id AND gv.class = "ot_gv"')
            ->where(['o.customers_id' => $customers_id])
            ->andWhere(['o.orders_status' => 3])
            ->andWhere(['IS', 'o.orders_cb_status', null])
            ->andWhere(['>=', 'o.date_purchased', $start_time])
            ->andWhere(['<=', 'o.date_purchased', $end_time])
            ->groupBy(['o.orders_id'])
            ->all(Yii::$app->db_offgamers);

        foreach ($data_row as $row) {
            $checkout_total = $row['ot_value'] + $row['gv_value'];
            $total += ($checkout_total > $row['total'] ? $row['total'] : $checkout_total);
        }

        return $total;
    }


    public function setOrdersPaymentMethod($payment_mapper)
    {
        $pm_data = array(
            'payment_methods_id' => $payment_mapper['pm_id'],
            'payment_methods_parent_id' => $payment_mapper['payment_methods_parent_id'],
            'payment_method' => $payment_mapper['pm_display_name'],
        );

        $orders = Orders::findOne(['orders_id' => $this->orders_id]);
        $orders->load($pm_data, '');
        if (!$orders->save()) {
            throw new Exception(Json::encode($orders->getErrors()));
        }
    }

    /**
     * @param array $data
     */
    public function updateSurchargeAmount($data)
    {
        $update_array = [];
        if (isset($data['processing_fee_amount'])) {
            $update_array['processing_fee_amount'] = $data['processing_fee_amount'];
        } else {
            $update_array['processing_fee_amount'] = 0;
        }

        if (!empty($data['tax_amount']) && (float)$data['tax_amount'] > 0) {
            $update_array['tax_amount'] = [
                'title' => $data['tax_info']['tax_name'] . ' (' . round($data['tax_info']['tax_rate']) . '%)',
                'value' => $data['tax_amount'],
            ];
        }

        if (!empty($update_array)) {
            OrdersTotal::updatePipwaveSurcharge($this->orders_id, $this->currency, $this->currency_value, $update_array, $data);
        }
    }

    public static function addPayPalTagging($order_id, $tag_id)
    {
        $is_tagging_exists = self::find()
            ->where(['orders_id' => $order_id])
            ->andWhere('FIND_IN_SET("' . (int)$tag_id . '", orders_tag_ids)')
            ->count();

        if (!$is_tagging_exists) {
            $model = self::findOne(['orders_id' => $order_id]);
            if (!empty($model->orders_tag_ids)) {
                $model->orders_tag_ids .= ',';
            }
            $model->orders_tag_ids .= (string) $tag_id;
            $model->save();
        }
    }
}