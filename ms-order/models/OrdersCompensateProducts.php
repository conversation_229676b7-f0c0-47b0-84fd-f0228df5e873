<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_compensate_products".
 *
 * @property int $orders_products_id
 * @property int $orders_id Top-up ID
 * @property int $compensate_by_supplier_id
 * @property string $compensate_for_orders_products_id
 * @property string $compensate_entered_currency
 * @property string $compensate_order_currency
 * @property string $compensate_by_supplier_firstname
 * @property string $compensate_by_supplier_lastname
 * @property string $compensate_by_supplier_code
 * @property string $compensate_by_supplier_email_address
 * @property string $orders_compensate_products_added_by
 * @property string $orders_compensate_products_messages
 * @property number $compensate_entered_currency_value
 * @property number $compensate_order_currency_value
 * @property number $compensate_accident_amount
 * @property number $compensate_non_accident_amount
 * @property number $compensate_supplier_amount
 *
 */
class OrdersCompensateProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_compensate_products';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id','orders_id','compensate_by_supplier_id'], 'integer'],
            [['compensate_for_orders_products_id','compensate_entered_currency','compensate_order_currency','compensate_by_supplier_firstname','compensate_by_supplier_lastname','compensate_by_supplier_code','compensate_by_supplier_email_address','orders_compensate_products_added_by','orders_compensate_products_messages'],'string'],
            [['compensate_entered_currency_value','compensate_order_currency_value','compensate_accident_amount','compensate_non_accident_amount','compensate_supplier_amount'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products Id',
            'orders_id' => 'Orders Id',
            'compensate_by_supplier_id' => 'Compensate By Supplier Id',
            'compensate_for_orders_products_id' => 'Compensate For Orders Products Id',
            'compensate_entered_currency' => 'Compensate Entered Currency',
            'compensate_order_currency' => 'Compensate Order Currency',
            'compensate_by_supplier_firstname' => 'Compensate By Supplier Firstname',
            'compensate_by_supplier_lastname' => 'Compensate By Supplier Lastname',
            'compensate_by_supplier_code' => 'Compensate By Supplier Code',
            'compensate_by_supplier_email_address' => 'Compensate By Supplier Email Address',
            'orders_compensate_products_added_by' => 'Orders Compensate Products Added By',
            'orders_compensate_products_messages' => 'Orders Compensate Products Messages',
            'compensate_entered_currency_value' => 'Compensate Entered Currency Value',
            'compensate_order_currency_value' => 'Compensate Order Currency Value',
            'compensate_accident_amount' => 'Compensate Accident Amount',
            'compensate_non_accident_amount' => 'Compensate Non Accident Amount',
            'compensate_supplier_amount' => 'Compensate Supplier Amount',
        ];
    }
}
