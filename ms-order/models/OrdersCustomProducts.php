<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_custom_products".
 *
 * @property int $orders_custom_products_id
 * @property int $product_id
 * @property int $orders_products_id
 * @property string $orders_custom_products_key
 * @property string $orders_custom_products_value
 * @property string $orders_custom_products_number
 *
 */
class OrdersCustomProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_custom_products';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_custom_products_id', 'products_id', 'orders_products_id'], 'integer'],
            [['orders_custom_products_key', 'orders_custom_products_value', 'orders_custom_products_number'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_custom_products_id' => 'Orders Custom Products Id',
            'products_id' => 'Product Id',
            'orders_products_id' => 'Orders Products Id',
            'orders_custom_products_key' => 'Orders Custom Products Key',
            'orders_custom_products_value' => 'Orders Custom Products Value',
            'orders_custom_products_number' => 'Orders Custom Products Number',
        ];
    }

    public static function insertOrdersCustomProduct($data)
    {
        $model = new self;
        $model->load($data, '');
        if(!$model->save()){
            throw new \Exception(json_encode($model->getErrors()));
        }
    }
}
