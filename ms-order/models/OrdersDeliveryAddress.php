<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_delivery_address".
 *
 * @property int $orders_products_id
 * @property string|null $recipient_name
 * @property string|null $contact_number
 * @property string|null $addr_1
 * @property string|null $addr_2
 * @property string|null $city
 * @property string|null $country_name
 * @property string|null $state
 * @property string|null $postcode
 */
class OrdersDeliveryAddress extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_delivery_address';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id'], 'integer'],
            [['recipient_name', 'contact_number', 'addr_1', 'addr_2', 'city', 'country_name', 'state', 'postcode'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products ID',
            'recipient_name' => 'Recipient Name',
            'contact_number' => 'Contact Number',
            'addr_1' => 'Addr 1',
            'addr_2' => 'Addr 2',
            'city' => 'City',
            'country_name' => 'Country Name',
            'state' => 'State',
            'postcode' => 'Postcode',
        ];
    }

    public static function saveDeliveryAddress($data)
    {
        $model = new self;
        $model->load($data, '');
        if (!$model->save()) {
            throw new \Exception(json_encode($model->getErrors()));
        }
    }
}
