<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_delivery_queue".
 *
 * @property string $orders_products_id
 * @property string $orders_id
 * @property string $extra_info Data store in json format
 * @property string $created_datetime
 */
class OrdersDeliveryQueue extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'created_datetime',
                'updatedAtAttribute' => false,
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_delivery_queue';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id', 'orders_id', 'extra_info'], 'required'],
            [['orders_products_id', 'orders_id'], 'integer'],
            [['extra_info'], 'string'],
            [['created_datetime'], 'safe'],
            [['orders_products_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products ID',
            'orders_id' => 'Orders ID',
            'extra_info' => 'Extra Info',
            'created_datetime' => 'Created Datetime',
        ];
    }
}
