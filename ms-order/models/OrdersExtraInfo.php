<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_extra_info".
 *
 * @property int $orders_id
 * @property string $orders_extra_info_key
 * @property string $orders_extra_info_value
 */
class OrdersExtraInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_extra_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'orders_extra_info_key'], 'required'],
            [['orders_id'], 'integer'],
            [['orders_extra_info_key'], 'string', 'max' => 32],
            [['orders_extra_info_value'], 'string', 'max' => 128],
            [['orders_id', 'orders_extra_info_key'], 'unique', 'targetAttribute' => ['orders_id', 'orders_extra_info_key']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'orders_extra_info_key' => 'Orders Extra Info Key',
            'orders_extra_info_value' => 'Orders Extra Info Value',
        ];
    }

    public static function insertOrdersExtraInfo($orders_id, $key, $value)
    {
        $model = new self();
        $model->load([
            'orders_id' => $orders_id,
            'orders_extra_info_key' => $key,
            'orders_extra_info_value' => (string)$value,
        ], '');
        if(!$model->save()){
            throw new \Exception(json_encode($model->getErrors()));
        }
    }
}
