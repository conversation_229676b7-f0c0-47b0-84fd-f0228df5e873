<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_log_table".
 *
 * @property int $orders_log_id
 * @property string $orders_log_admin_id
 * @property string|null $orders_log_ip
 * @property string|null $orders_log_time
 * @property int $orders_log_orders_id
 * @property string $orders_log_system_messages
 * @property string $orders_log_filename
 */
class OrdersLogTable extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_log_table';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_log_time'], 'safe'],
            [['orders_log_orders_id'], 'integer'],
            [['orders_log_system_messages'], 'required'],
            [['orders_log_system_messages'], 'string'],
            [['orders_log_admin_id', 'orders_log_filename'], 'string', 'max' => 255],
            [['orders_log_ip'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_log_id' => 'Orders Log ID',
            'orders_log_admin_id' => 'Orders Log Admin ID',
            'orders_log_ip' => 'Orders Log Ip',
            'orders_log_time' => 'Orders Log Time',
            'orders_log_orders_id' => 'Orders Log Orders ID',
            'orders_log_system_messages' => 'Orders Log System Messages',
            'orders_log_filename' => 'Orders Log Filename',
        ];
    }
}
