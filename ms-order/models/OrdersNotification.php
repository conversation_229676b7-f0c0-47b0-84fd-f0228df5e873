<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_notification".
 *
 * @property int $customers_id
 * @property int $orders_id
 * @property string $orders_type CO, BO
 * @property int $site_id
 */
class OrdersNotification extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_notification';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'orders_id', 'orders_type', 'site_id'], 'required'],
            [['customers_id', 'orders_id', 'site_id'], 'integer'],
            [['orders_type'], 'string', 'max' => 3],
            [['orders_id', 'orders_type', 'site_id'], 'unique', 'targetAttribute' => ['orders_id', 'orders_type', 'site_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'orders_id' => 'Orders ID',
            'orders_type' => 'Orders Type',
            'site_id' => 'Site ID',
        ];
    }


    public static function insertOrdersNotification($data)
    {
        $model = new self;
        $model->load($data, '');
        $model->save();
    }
}
