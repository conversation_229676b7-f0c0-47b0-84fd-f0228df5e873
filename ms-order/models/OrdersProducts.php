<?php

namespace micro\models;

use Yii;
use yii\helpers\Json;

class OrdersProducts extends OrdersProductsBase
{
    public static function setDeliveryQueue($data)
    {
        $orders_products_id = $data['orders_products_id'];
        $api_provider = $data['api_provider'];

        $op_model = OrdersProducts::findOne(['orders_products_id' => $orders_products_id]);

        if ($op_model) {

            $delivery_queue_model = new OrdersDeliveryQueue();
            $delivery_queue_model->load([
                'orders_products_id' => $orders_products_id,
                'orders_id' => $op_model->orders_id,
                'extra_info' => Json::encode(['api_provider' => $api_provider])
            ], "");
            $delivery_queue_model->save();

            $temp_process_model = new TempProcess();
            $temp_process_data = [
                'page_name' => 'PROCESS_MOVING_ORDER',
                'match_case' => (string)$orders_products_id,
                'extra_info' => Json::encode(
                    [
                        'request_by' => 'system',
                        'locked_by' => 'system',
                        'locked_email' => 'system',
                        'locked_ip' => (!empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : '127.0.0.1')
                    ]
                )
            ];

            $temp_process_model->load($temp_process_data, "");
            $temp_process_model->save();

        }
    }

    public static function createOrdersProducts($data): int
    {
        $model = new self;
        $model->load($data, '');
        if (!$model->save()) {
            throw new \Exception(json_encode($model->getErrors()));
        }
        return $model->orders_products_id;
    }

    public static function isDirectTopUp($orders_products_id)
    {
        return OrdersProductsExtraInfo::find()->where(['orders_products_id' => $orders_products_id, 'orders_products_extra_info_key' => 'DELIVERY_MODE', 'orders_products_extra_info_value' => '6'])->exists();
    }
}