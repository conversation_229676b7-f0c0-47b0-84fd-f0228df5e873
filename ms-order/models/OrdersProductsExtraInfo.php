<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_products_extra_info".
 *
 * @property int $orders_products_id
 * @property string $orders_products_extra_info_key
 * @property string $orders_products_extra_info_value
 */
class OrdersProductsExtraInfo extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_products_extra_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_products_id', 'orders_products_extra_info_key', 'orders_products_extra_info_value'], 'required'],
            [['orders_products_id'], 'integer'],
            [['orders_products_extra_info_value'], 'string'],
            [['orders_products_extra_info_key'], 'string', 'max' => 100],
            [['orders_products_id', 'orders_products_extra_info_key'], 'unique', 'targetAttribute' => ['orders_products_id', 'orders_products_extra_info_key']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_products_id' => 'Orders Products ID',
            'orders_products_extra_info_key' => 'Orders Products Extra Info Key',
            'orders_products_extra_info_value' => 'Orders Products Extra Info Value',
        ];
    }

    public static function insertExtraInfo($op_id, $key, $value)
    {
        $model = new self;
        $model->load([
            'orders_products_id' => $op_id,
            'orders_products_extra_info_key' => $key,
            'orders_products_extra_info_value' => $value
        ], '');
        $model->save();
    }
}
