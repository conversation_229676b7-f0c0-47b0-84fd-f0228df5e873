<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_status_history".
 *
 * @property int $orders_status_history_id
 * @property int $orders_id
 * @property int $orders_status_id
 * @property string $date_added
 * @property int $customer_notified
 * @property string $comments
 * @property int $comments_type
 * @property int $set_as_order_remarks
 * @property string $changed_by
 */
class OrdersStatusHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_status_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */

    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'orders_status_id', 'customer_notified', 'comments_type', 'set_as_order_remarks'], 'integer'],
            [['date_added'], 'safe'],
            [['comments'], 'string'],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_status_history_id' => 'Orders Status History ID',
            'orders_id' => 'Orders ID',
            'orders_status_id' => 'Orders Status ID',
            'date_added' => 'Date Added',
            'customer_notified' => 'Customer Notified',
            'comments' => 'Comments',
            'comments_type' => 'Comments Type',
            'set_as_order_remarks' => 'Set As Order Remarks',
            'changed_by' => 'Changed By',
        ];
    }

    public static function getLastStatusTimeByStatusId($orders_id, $status_id)
    {
        $query = self::find()->select('orders_status_id,date_added')
            ->where(['orders_id' => $orders_id, 'orders_status_id' => $status_id])
            ->orderBy(['date_added' => SORT_DESC, 'orders_status_history_id' => SORT_DESC])
            ->limit(1)
            ->asArray()
            ->one();

        return strtotime($query['date_added']);
    }

    public static function insertHistory($data)
    {
        $model = new self();
        $model->load($data, '');
        if(!$model->save()){
            throw new \Exception(json_encode($model->getErrors()));
        }
    }
}