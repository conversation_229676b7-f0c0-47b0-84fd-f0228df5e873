<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_status_stat".
 *
 * @property int $orders_id
 * @property int $orders_status_id
 * @property int $occurrence
 * @property string $first_date
 * @property string $latest_date
 * @property string $changed_by
 */
class OrdersStatusStat extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_status_stat';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'orders_status_id'], 'required'],
            [['orders_id', 'orders_status_id', 'occurrence'], 'integer'],
            [['first_date', 'latest_date'], 'safe'],
            [['changed_by'], 'string', 'max' => 128],
            [['orders_id', 'orders_status_id'], 'unique', 'targetAttribute' => ['orders_id', 'orders_status_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'orders_status_id' => 'Orders Status ID',
            'occurrence' => 'Occurrence',
            'first_date' => 'First Date',
            'latest_date' => 'Latest Date',
            'changed_by' => 'Changed By',
        ];
    }
}
