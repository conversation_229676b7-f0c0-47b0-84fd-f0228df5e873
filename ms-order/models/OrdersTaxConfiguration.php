<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_tax_configuration".
 *
 * @property int $orders_tax_id
 * @property string $country_code countries_iso_code_2
 * @property string $country_name
 * @property string $currency
 * @property float $orders_tax_percentage
 * @property float $business_tax_percentage
 * @property string|null $tax_item
 * @property int|null $tax_group_id
 * @property string $orders_tax_status
 * @property int $orders_provide_invoice_status
 * @property int $business_tax_status
 * @property int $orders_include_reverse_charge
 * @property string $start_datetime
 * @property string $address_1
 * @property string $address_2
 * @property string $address_3
 * @property string $contact
 * @property string $website
 * @property string $tax_invoice_title
 * @property string $tax_registration_name
 * @property string $gst_registration_no
 * @property string $company_name
 * @property string $company_logo
 * @property string|null $business_tax_form
 */
class OrdersTaxConfiguration extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_tax_configuration';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['country_name', 'start_datetime', 'address_1', 'address_2', 'address_3', 'contact', 'website', 'gst_registration_no', 'company_name', 'company_logo'], 'required'],
            [['orders_tax_percentage', 'business_tax_percentage'], 'number'],
            [['tax_group_id', 'orders_provide_invoice_status', 'business_tax_status', 'orders_include_reverse_charge'], 'integer'],
            [['orders_tax_status', 'business_tax_form'], 'string'],
            [['start_datetime'], 'safe'],
            [['country_code'], 'string', 'max' => 2],
            [['country_name', 'address_1', 'address_2', 'address_3', 'tax_invoice_title', 'tax_registration_name'], 'string', 'max' => 64],
            [['currency'], 'string', 'max' => 3],
            [['tax_item'], 'string', 'max' => 200],
            [['contact', 'website', 'gst_registration_no', 'company_name'], 'string', 'max' => 32],
            [['company_logo'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_tax_id' => 'Orders Tax ID',
            'country_code' => 'Country Code',
            'country_name' => 'Country Name',
            'currency' => 'Currency',
            'orders_tax_percentage' => 'Orders Tax Percentage',
            'business_tax_percentage' => 'Business Tax Percentage',
            'tax_item' => 'Tax Item',
            'tax_group_id' => 'Tax Group ID',
            'orders_tax_status' => 'Orders Tax Status',
            'orders_provide_invoice_status' => 'Orders Provide Invoice Status',
            'business_tax_status' => 'Business Tax Status',
            'orders_include_reverse_charge' => 'Orders Include Reverse Charge',
            'start_datetime' => 'Start Datetime',
            'address_1' => 'Address 1',
            'address_2' => 'Address 2',
            'address_3' => 'Address 3',
            'contact' => 'Contact',
            'website' => 'Website',
            'tax_invoice_title' => 'Tax Invoice Title',
            'tax_registration_name' => 'Tax Registration Name',
            'gst_registration_no' => 'Gst Registration No',
            'company_name' => 'Company Name',
            'company_logo' => 'Company Logo',
            'business_tax_form' => 'Business Tax Form',
        ];
    }

    public static function getNormalTaxPercentage($country_code)
    {
        return self::findOne(['country_code' => $country_code]);
    }
}
