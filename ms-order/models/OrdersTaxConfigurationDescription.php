<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_tax_configuration_description".
 *
 * @property int $orders_tax_id
 * @property int $language_id
 * @property string $orders_tax_title
 * @property string $orders_tax_title_short
 * @property string $orders_tax_message
 */
class OrdersTaxConfigurationDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_tax_configuration_description';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_tax_id', 'language_id', 'orders_tax_title_short', 'orders_tax_message'], 'required'],
            [['orders_tax_id', 'language_id'], 'integer'],
            [['orders_tax_message'], 'string'],
            [['orders_tax_title'], 'string', 'max' => 32],
            [['orders_tax_title_short'], 'string', 'max' => 10],
            [['orders_tax_id', 'language_id'], 'unique', 'targetAttribute' => ['orders_tax_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_tax_id' => 'Orders Tax ID',
            'language_id' => 'Language ID',
            'orders_tax_title' => 'Orders Tax Title',
            'orders_tax_title_short' => 'Orders Tax Title Short',
            'orders_tax_message' => 'Orders Tax Message',
        ];
    }

    public static function getTaxDescription($tax_id, $language_id = 1)
    {
        return self::findOne(['orders_tax_id' => $tax_id, $language_id]);
    }
}
