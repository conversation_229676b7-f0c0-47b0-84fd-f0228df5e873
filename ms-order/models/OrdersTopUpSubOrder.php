<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_top_up_sub_order".
 *
 * @property int $id
 * @property int $top_up_id
 * @property int $status
 * @property string $publisher_ref_id
 * @property int $created_at
 * @property int $updated_at
 */
class OrdersTopUpSubOrder extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_top_up_sub_order';
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            \yii\behaviors\TimestampBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['top_up_id', 'status'], 'required'],
            [['top_up_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['publisher_ref_id'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'top_up_id' => 'Top Up ID',
            'status' => 'Status',
            'publisher_ref_id' => 'Publisher Ref ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
