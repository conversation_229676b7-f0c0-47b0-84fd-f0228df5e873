<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "orders_total".
 *
 * @property int $orders_total_id
 * @property int $orders_id
 * @property string $title
 * @property string $text
 * @property string $value
 * @property string $class
 * @property int $sort_order
 */
class OrdersTotal extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'orders_total';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'sort_order'], 'integer'],
            [['value'], 'number'],
            [['title', 'text'], 'string', 'max' => 255],
            [['class'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_total_id' => 'Orders Total ID',
            'orders_id' => 'Orders ID',
            'title' => 'Title',
            'text' => 'Text',
            'value' => 'Value',
            'class' => 'Class',
            'sort_order' => 'Sort Order',
        ];
    }

    public static function revertPipwaveSurcharge($orders_id, $currency, $rate, $data)
    {
        $surcharge_model = OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_surcharge']);
        $tax_surcharge_model = OrdersExtraInfo::findOne(['orders_id' => $orders_id, 'orders_extra_info_key' => 'tax_surcharge_amount']);
        $surcharge_tax_amount = (float)($tax_surcharge_model ? $tax_surcharge_model->orders_extra_info_value : 0);
        $surcharge_amount = Yii::$app->currency->parseStringToNumber($currency, $surcharge_model->text);
        $new_processing_tax_amount = ($data['tax_amount']['value'] ?? 0);

        if ($surcharge_amount == $data['processing_fee_amount'] && $surcharge_tax_amount == $new_processing_tax_amount) {
            return false;
        }

        if ($tax_surcharge_model) {
            $tax_surcharge_model->delete();
            OrdersExtraInfo::deleteAll(['orders_id' => $orders_id, 'orders_extra_info_key' => 'tax_surcharge_usd_amount']);
        }

        if ($surcharge_tax_amount > 0) {
            $ot = OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_gst']);
            $current_tax_amount = Yii::$app->currency->parseStringToNumber($currency, $ot->text);
            $total_tax_amount = bcsub($current_tax_amount, $surcharge_tax_amount, 8);

            $ot->load([
                'text' => '<b>' . Yii::$app->currency->format($currency, $total_tax_amount) . '</b>',
                'value' => (string)(bcdiv($total_tax_amount, $rate, 8)),
            ], '');

            $ot->save();

            $oei = OrdersExtraInfo::findOne(['orders_id' => $orders_id, 'orders_extra_info_key' => 'tax_amount']);
            $total_tax_amount = bcsub((float)$oei->orders_extra_info_value, $surcharge_tax_amount, 8);
            $oei->orders_extra_info_value = (string)$total_tax_amount;
            $oei->save();
        }

        $ot = OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_total']);
        $current_total_amount = Yii::$app->currency->parseStringToNumber($currency, $ot->text);
        $total_tax_amount = bcsub(bcsub($current_total_amount, $surcharge_tax_amount, 8), $surcharge_amount, 8);

        $ot->load([
            'text' => '<b>' . Yii::$app->currency->format($currency, $total_tax_amount) . '</b>',
            'value' => (string)(bcdiv($total_tax_amount, $rate, 8)),
        ], '');

        $ot->save();
        return true;
    }

    public static function updatePipwaveSurcharge($orders_id, $currency, $rate, $data, $pipwave_data)
    {
        $surcharge = 0;
        $surcharge_tax_amount = 0;

        if (OrdersTotal::find()->where(['orders_id' => $orders_id, 'class' => 'ot_surcharge'])->exists()) {
            if(!self::revertPipwaveSurcharge($orders_id, $currency, $rate, $data)){
                return;
            }
        }

        foreach ($data as $key => $value) {
            switch ($key) {
                case 'processing_fee_amount':
                    $ot = (OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_surcharge']) ?? new OrdersTotal);
                    $surcharge = $value;

                    if ($value == 0 && !$ot->isNewRecord) {
                        $ot->delete();
                    } elseif ($value > 0) {
                        $ot->load([
                            'orders_id' => $orders_id,
                            'title' => 'Handling Fee:',
                            'text' => '<b>' . Yii::$app->currency->format($currency, $value) . '</b>',
                            'class' => 'ot_surcharge',
                            'value' => (string)(bcdiv($value, $rate, 8)),
                            'sort_order' => 850,
                        ], '');
                        $ot->save();
                    }
                    break;

                case 'tax_amount':
                    $ot = (OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_gst']) ?? new OrdersTotal);
                    $surcharge_tax_amount = $value['value'];
                    $current_tax_amount = Yii::$app->currency->parseStringToNumber($currency, $ot->text);
                    $total_tax_amount = bcadd($surcharge_tax_amount, $current_tax_amount, 8);;
                    $ot->load([
                        'orders_id' => $orders_id,
                        'title' => $value['title'],
                        'text' => '<b>' . Yii::$app->currency->format($currency, $total_tax_amount) . '</b>',
                        'class' => 'ot_gst',
                        'value' => (string)(bcdiv($total_tax_amount, $rate, 8)),
                        'sort_order' => 800,
                    ], '');

                    $ot->save();

                    $oei = (OrdersExtraInfo::findOne(['orders_id' => $orders_id, 'orders_extra_info_key' => 'tax_amount']) ?? new OrdersExtraInfo);
                    $total_tax_amount = bcadd((float)$oei->orders_extra_info_value, $value['value'], 8);
                    $oei->load([
                        'orders_id' => $orders_id,
                        'orders_extra_info_key' => 'tax_amount',
                        'orders_extra_info_value' => (string)$total_tax_amount,
                    ], '');
                    $oei->save();
                    break;
            }

        }

        if ($surcharge >= 0 || $surcharge_tax_amount >= 0) {
            $ot = (OrdersTotal::findOne(['orders_id' => $orders_id, 'class' => 'ot_total']) ?? new OrdersTotal);
            $current_value = Yii::$app->currency->parseStringToNumber($currency, $ot->text);
            $new_total = $current_value + $surcharge + $surcharge_tax_amount;
            $ot->text = '<b>' . Yii::$app->currency->format($currency, $new_total) . '</b>';
            $ot->value = $new_total / $rate;
            $ot->save();

            $oei = new OrdersExtraInfo;
            $oei->load([
                'orders_id' => $orders_id,
                'orders_extra_info_key' => 'tax_surcharge_amount',
                'orders_extra_info_value' => (string)$surcharge_tax_amount,
            ], '');
            $oei->save();

            $oei = new OrdersExtraInfo;
            $oei->load([
                'orders_id' => $orders_id,
                'orders_extra_info_key' => 'tax_surcharge_usd_amount',
                'orders_extra_info_value' => (string)(bcdiv($surcharge_tax_amount, $rate, 8)),
            ], '');
            $oei->save();

            if (OrdersExtraInfo::find()->where(['orders_id' => $orders_id, 'orders_extra_info_key' => 'tax_country'])->exists() === false) {
                $tax_country = ($pipwave_data['tax_info']['tax_country'] ?? '');
                if ($tax_country) {
                    $oei = new OrdersExtraInfo;
                    $oei->load([
                        'orders_id' => $orders_id,
                        'orders_extra_info_key' => 'tax_country',
                        'orders_extra_info_value' => $tax_country,
                    ], '');
                    $oei->save();

                    $orders_tax = OrdersTaxConfiguration::find()->where(['country_code' => $tax_country])->one();

                    if ($orders_tax) {
                        $tax_invoice_description = OrdersTaxConfigurationDescription::find()->where(['orders_tax_id' => $orders_tax->orders_tax_id])->one();
                        $oei = new OrdersExtraInfo;
                        $oei->load([
                            'orders_id' => $orders_id,
                            'orders_extra_info_key' => 'tax_short_title',
                            'orders_extra_info_value' => $tax_invoice_description->orders_tax_title_short,
                        ], '');
                        $oei->save();

                        $oei = new OrdersExtraInfo;
                        $oei->load([
                            'orders_id' => $orders_id,
                            'orders_extra_info_key' => 'tax_invoice_title',
                            'orders_extra_info_value' => $orders_tax->tax_invoice_title,
                        ], '');
                        $oei->save();
                    }
                }
            }
        }
    }
}
