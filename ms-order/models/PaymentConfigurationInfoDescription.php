<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "payment_configuration_info_description".
 *
 * @property int $payment_configuration_info_id
 * @property int $languages_id
 * @property string $payment_configuration_info_value
 */
class PaymentConfigurationInfoDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'payment_configuration_info_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['payment_configuration_info_id', 'languages_id', 'payment_configuration_info_value'], 'required'],
            [['payment_configuration_info_id', 'languages_id'], 'integer'],
            [['payment_configuration_info_value'], 'string'],
            [['payment_configuration_info_id', 'languages_id'], 'unique', 'targetAttribute' => ['payment_configuration_info_id', 'languages_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'payment_configuration_info_id' => 'Payment Configuration Info ID',
            'languages_id' => 'Languages ID',
            'payment_configuration_info_value' => 'Payment Configuration Info Value',
        ];
    }
}
