<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "payment_extra_info".
 *
 * @property int $orders_id
 * @property string $transaction_id
 * @property int $transaction_status
 * @property float $transaction_amount
 * @property string|null $transaction_currency
 * @property string|null $transaction_text_amount
 * @property string|null $credit_card_type
 * @property string|null $credit_card_owner
 * @property string|null $cardholder_aut_result
 * @property string|null $email_address
 * @property string|null $billing_address
 * @property string|null $country
 * @property string|null $country_code
 * @property string|null $ip_address
 * @property string|null $tel
 * @property string|null $fax
 * @property string $check_result
 * @property string $alert_message
 * @property string $authorisation_mode
 * @property string|null $authorisation_result
 * @property float|null $transaction_net_amount
 */
class PaymentExtraInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'payment_extra_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id', 'transaction_status'], 'integer'],
            [['transaction_amount', 'transaction_net_amount'], 'number'],
            [['authorisation_result'], 'string'],
            [['transaction_id', 'country_code'], 'string', 'max' => 15],
            [['transaction_currency'], 'string', 'max' => 10],
            [['transaction_text_amount', 'tel', 'fax'], 'string', 'max' => 32],
            [['credit_card_type', 'cardholder_aut_result', 'ip_address'], 'string', 'max' => 64],
            [['credit_card_owner', 'email_address', 'billing_address', 'country', 'check_result', 'alert_message'], 'string', 'max' => 255],
            [['authorisation_mode'], 'string', 'max' => 1],
            [['orders_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'transaction_id' => 'Transaction ID',
            'transaction_status' => 'Transaction Status',
            'transaction_amount' => 'Transaction Amount',
            'transaction_currency' => 'Transaction Currency',
            'transaction_text_amount' => 'Transaction Text Amount',
            'credit_card_type' => 'Credit Card Type',
            'credit_card_owner' => 'Credit Card Owner',
            'cardholder_aut_result' => 'Cardholder Aut Result',
            'email_address' => 'Email Address',
            'billing_address' => 'Billing Address',
            'country' => 'Country',
            'country_code' => 'Country Code',
            'ip_address' => 'Ip Address',
            'tel' => 'Tel',
            'fax' => 'Fax',
            'check_result' => 'Check Result',
            'alert_message' => 'Alert Message',
            'authorisation_mode' => 'Authorisation Mode',
            'authorisation_result' => 'Authorisation Result',
            'transaction_net_amount' => 'Transaction Net Amount',
        ];
    }

    public static function updateAttachmentDetail($orders_id, $authorisation_result)
    {
        $model = (self::findOne(['orders_id' => $orders_id]) ?? new self);
        $model->authorisation_result = $authorisation_result;
        $model->save();
        return $model;
    }
}
