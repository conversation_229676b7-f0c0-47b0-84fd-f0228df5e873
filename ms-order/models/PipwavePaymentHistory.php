<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "pipwave_payment_history".
 *
 * @property int $id
 * @property int $orders_id
 * @property string|null $status
 * @property string|null $message
 * @property string|null $notification_date
 * @property string|null $changed_by
 */
class PipwavePaymentHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pipwave_payment_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id'], 'required'],
            [['orders_id'], 'integer'],
            [['notification_date'], 'safe'],
            [['status', 'message', 'changed_by'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'orders_id' => 'Orders ID',
            'status' => 'Status',
            'message' => 'Message',
            'notification_date' => 'Notification Date',
            'changed_by' => 'Changed By',
        ];
    }


    public static function paymentStatusMapper($code)
    {
        $status = '';
        switch ($code) {
            case 0:
                $status = 'Pending';
                break;
            case 1:
                $status = 'Failed';
                break;
            case 2:
                $status = 'Cancelled';
                break;
            case 5:
                $status = 'Processing';
                break;
            case 10:
                $status = 'Paid';
                break;
            case 13:
                $status = 'Disputed';
                break;
            case 15:
                $status = 'Chargeback';
                break;
            case 20:
                $status = 'Full refund';
                break;
            case 25:
                $status = 'Partial refund';
                break;
        }

        return $status;
    }

    public static function paymentSubStatusMapper($code)
    {
        $status = '';
        switch ($code) {
            case 501:
                $status = 'Required capture';
                break;
            case 502:
                $status = 'Payment confirmed';
                break;
        }

        return $status;
    }

    public static function pipwaveNotificationStatusParser($code)
    {
        $status = '';
        switch ($code) {
            case 200:
                $status = 'OK';
                break;
            case 400:
                $status = 'Failed to fulfill request, see message';
                break;
            case 401:
                $status = 'Invalid API signature';
                break;
            case 403:
                $status = 'Invalid API key';
                break;
            case 404:
                $status = 'Invalid action';
                break;
            case 500:
                $status = 'Generic server error, see message';
                break;
            case 1001:
                $status = 'Currency not offered by Merchant';
                break;
            case 1002:
                $status = 'Transaction ID (txn_id) duplicate';
                break;
            case 2001:
                $status = 'Unsupported payment method, see message';
                break;
            case 2002:
                $status = 'Token has expired';
                break;
            case 3001:
                $status = 'Notification with warning (see pw_reason data)';
                break;
            case 4001:
                $status = 'Payment is already fully refunded';
                break;
            case 4002:
                $status = 'Payment not finalized yet';
                break;
            case 4003:
                $status = 'Refunded amount is more than current amount';
                break;
            case 4004:
                $status = 'Refund not supported';
                break;
            case 4005:
                $status = 'Partial refund not supported';
                break;
            case 4006:
                $status = 'The transaction already has a reversal in process. You must wait for the process to complete first.';
                break;
            case 4501:
                $status = 'No disputed chargeback transaction to reversed';
                break;
            case 9001:
                $status = 'Parameter error, see message';
                break;
        }
        return $status;
    }

    public static function savePipwavePaymentHistory($data)
    {
        $message = strip_tags($data['message']);
        if (empty($message)) {
            $message = self::paymentStatusMapper($data['transaction_status']);
            if (isset($data['txn_sub_status']) && !empty($data['txn_sub_status'])) {
                $message .= ' - ' . self::paymentSubStatusMapper($data['txn_sub_status']);
            }
        } else {
            if (isset($data['pg_reason']) && !empty($data['pg_reason'])) {
                $message .= ' - ' . $data['pg_reason'];
            }
        }
        $pwPaymentHistoryDataArray = array(
            'orders_id' => $data['txn_id'],
            'status' => self::pipwaveNotificationStatusParser($data['status']),
            'message' => $message,
            'notification_date' => (!empty($data['notification_date']) ? date('Y-m-d H:i:s', $data['notification_date']) : date('Y-m-d H:i:s')),
            'changed_by' => 'PGS'
        );
        $payment_history = (new self);
        $payment_history->load($pwPaymentHistoryDataArray, '');
        $payment_history->save();
    }
}
