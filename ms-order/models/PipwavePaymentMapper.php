<?php

namespace micro\models;

use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "pipwave_payment_mapper".
 *
 * @property int $id
 * @property int $pm_id
 * @property string $pipwave_payment_code
 * @property string $pm_display_name
 * @property string $pg_display_name
 * @property int $pg_id
 * @property string $pg_code
 * @property int $is_rp
 * @property int $site_id
 * @property int|null $is_refundable
 */
class PipwavePaymentMapper extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pipwave_payment_mapper';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'pm_id',
                    'pipwave_payment_code',
                    'pm_display_name',
                    'pg_display_name',
                    'pg_id',
                    'pg_code',
                    'is_rp',
                ],
                'required',
            ],
            [['pm_id', 'pg_id', 'is_rp', 'site_id', 'is_refundable'], 'integer'],
            [
                ['pipwave_payment_code', 'pm_display_name', 'pg_display_name', 'pg_code'],
                'string',
                'max' => 255,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'pm_id' => 'Pm ID',
            'pipwave_payment_code' => 'Pipwave Payment Code',
            'pm_display_name' => 'Pm Display Name',
            'pg_display_name' => 'Pg Display Name',
            'pg_id' => 'Pg ID',
            'pg_code' => 'Pg Code',
            'is_rp' => 'Is Rp',
            'site_id' => 'Site ID',
            'is_refundable' => 'Is Refundable',
        ];
    }

    public static function getPaymentInfo($payment_code)
    {
        $query = new Query;
        $query->select([
            'pm_id',
            'pm_display_name',
            'pg_display_name',
            'is_rp',
            'pg_code',
            'payment_methods_id',
            'payment_methods_title',
            'payment_methods_parent_id',
            'payment_methods_code',
            'payment_methods_filename',
        ])
            ->from(self::tableName() . ' ppm')
            ->innerJoin(PaymentMethods::tableName() . ' pm', 'pm.payment_methods_id = ppm.pm_id')
            ->where(['pipwave_payment_code' => $payment_code, 'site_id' => 0]);

        return $query->one();
    }

    public static function getRpPayment()
    {
        $result = self::find()->select('pipwave_payment_code')
            ->where(['site_id' => 0, 'is_rp' => 1])
            ->asArray()
            ->all();

        return ArrayHelper::getColumn($result, 'pipwave_payment_code');
    }
}
