<?php

namespace micro\models;

use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\Url;

/**
 * This is the model class for table "reseller".
 *
 * @property int $reseller_id
 * @property string $title
 * @property string $profile
 * @property int $last_sync
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class Reseller extends \yii\db\ActiveRecord
{
    const RESELLER_LIST = [
        'G2G' => '\micro\models\resellers\G2G'
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'reseller';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'profile', 'status'], 'required', 'on' => 'save'],
            [['last_sync', 'status', 'created_at', 'updated_at'], 'integer'],
            [['title', 'profile'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'reseller_id' => 'Reseller ID',
            'title' => 'Title',
            'profile' => 'Profile',
            'last_sync' => 'Last Sync',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
