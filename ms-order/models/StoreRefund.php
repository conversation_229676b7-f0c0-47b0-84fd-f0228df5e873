<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "store_refund".
 *
 * @property int $store_refund_id
 * @property int $user_id
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $store_refund_date
 * @property string $store_refund_trans_id
 * @property int $store_refund_status
 * @property float $store_refund_trans_total_amount
 * @property float $store_refund_amount
 * @property string|null $store_refund_payments_reference
 * @property string|null $store_refund_payments_methods_name
 * @property string|null $store_refund_last_modified
 * @property int $store_refund_is_processed
 */
class StoreRefund extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_refund';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'store_refund_status', 'store_refund_is_processed'], 'integer'],
            [['store_refund_date', 'store_refund_last_modified'], 'safe'],
            [['store_refund_trans_total_amount', 'store_refund_amount'], 'number'],
            [['user_firstname', 'user_lastname', 'store_refund_payments_reference'], 'string', 'max' => 32],
            [['user_email_address'], 'string', 'max' => 96],
            [['store_refund_trans_id'], 'string', 'max' => 255],
            [['store_refund_payments_methods_name'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'store_refund_id' => 'Store Refund ID',
            'user_id' => 'User ID',
            'user_firstname' => 'User Firstname',
            'user_lastname' => 'User Lastname',
            'user_email_address' => 'User Email Address',
            'store_refund_date' => 'Store Refund Date',
            'store_refund_trans_id' => 'Store Refund Trans ID',
            'store_refund_status' => 'Store Refund Status',
            'store_refund_trans_total_amount' => 'Store Refund Trans Total Amount',
            'store_refund_amount' => 'Store Refund Amount',
            'store_refund_payments_reference' => 'Store Refund Payments Reference',
            'store_refund_payments_methods_name' => 'Store Refund Payments Methods Name',
            'store_refund_last_modified' => 'Store Refund Last Modified',
            'store_refund_is_processed' => 'Store Refund Is Processed',
        ];
    }
}
