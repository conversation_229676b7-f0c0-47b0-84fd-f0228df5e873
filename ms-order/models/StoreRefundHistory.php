<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "store_refund_history".
 *
 * @property int $store_refund_history_id
 * @property int $store_refund_id
 * @property int $store_refund_status
 * @property string $date_added
 * @property int $payee_notified
 * @property string|null $comments
 * @property string $changed_by
 * @property string $changed_by_role
 */
class StoreRefundHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_refund_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['store_refund_id', 'store_refund_status', 'payee_notified'], 'integer'],
            [['date_added'], 'safe'],
            [['comments'], 'string'],
            [['changed_by'], 'string', 'max' => 128],
            [['changed_by_role'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'store_refund_history_id' => 'Store Refund History ID',
            'store_refund_id' => 'Store Refund ID',
            'store_refund_status' => 'Store Refund Status',
            'date_added' => 'Date Added',
            'payee_notified' => 'Payee Notified',
            'comments' => 'Comments',
            'changed_by' => 'Changed By',
            'changed_by_role' => 'Changed By Role',
        ];
    }
}
