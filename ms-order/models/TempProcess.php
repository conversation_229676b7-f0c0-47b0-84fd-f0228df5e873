<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "temp_process".
 *
 * @property int $temp_id
 * @property string $page_name
 * @property string $match_case
 * @property string $extra_info
 * @property string $created_date
 */
class TempProcess extends \yii\db\ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'temp_process';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'created_date',
                'updatedAtAttribute' => false,
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_date'], 'safe'],
            [['page_name', 'match_case', 'extra_info'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'temp_id' => 'Temp ID',
            'page_name' => 'Page Name',
            'match_case' => 'Match Case',
            'extra_info' => 'Extra Info',
            'created_date' => 'Created Date',
        ];
    }
}
