<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "zones".
 *
 * @property int $zone_id
 * @property int $zone_country_id
 * @property string $zone_code
 * @property string $zone_name
 */
class Zones extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'zones';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['zone_country_id'], 'integer'],
            [['zone_code'], 'string', 'max' => 32],
            [['zone_name'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'zone_id' => 'Zone ID',
            'zone_country_id' => 'Zone Country ID',
            'zone_code' => 'Zone Code',
            'zone_name' => 'Zone Name',
        ];
    }

    public static function getState($zoneId) {
        $result = self::find()->select('zone_code, zone_name')->where(['zone_id' => $zoneId])->asArray()->one();

        return $result;
    }

    public static function getStateIdByName($zone_name) {
        $result = self::find()->select('zone_id')->where(['zone_name' => $zone_name])->scalar();

        return ($result ?? 0);
    }

    public static function getStateName($ctry_id, $state_id) {
        $result = self::find()->select('zone_name')->where(["zone_id" => $state_id, "zone_country_id" => $ctry_id])->scalar();

        return ($result ?? '');
    }

}
