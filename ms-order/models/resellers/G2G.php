<?php

namespace micro\models\resellers;

use micro\models\G2gOgOrderMapper;
use micro\models\Products;
use micro\models\G2gProductsMapping;
use micro\models\G2gStockMovementHistory;
use micro\models\ResellerSetting;
use offgamers\base\traits\GuzzleTrait;
use Yii;
use offgamers\base\models\ms\Inventory;
use yii\db\Exception;
use yii\helpers\Json;
use micro\components\Order;

class G2G
{

    use GuzzleTrait;

    const API_VERSION = 'v2';
    const G2G_ORDER_MUTEX_LOCK = 'g2g_order/%s';

    public $reseller;

    public $order_id;

    public $reprocess_restock_request = false;
    protected $configuration_data;
    protected $base_url;
    protected $api_key;
    protected $api_secret;
    protected $user_id;
    protected $webhook_token;

    protected $error_code;
    protected $error_message;

    const API_G2G_GET_ORDER = 'orders/%s';

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->reseller)) {
                $config_list = ResellerSetting::findAll(['reseller_id' => $this->reseller]);

                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new Exception('Reseller Id is empty');
            }
        }
    }

    public function getConfig()
    {
        $this->getExistingConfig();
        $this->api_key = $this->configuration_data['API_KEY'];
        $this->api_secret = $this->configuration_data['API_SECRET'];
        $this->user_id = $this->configuration_data['USER_ID'];
        $this->base_url = $this->configuration_data['BASE_URL'];
        $this->webhook_token = $this->configuration_data['WEBHOOK_TOKEN'];
    }

    /**
     * @todo Match total delivered quantity against database value
     */
    public function processDelivery()
    {
        // Obtain mutex lock
        $mutex_lock = sprintf(self::G2G_ORDER_MUTEX_LOCK, $this->order_id);
        if (Yii::$app->mutex->acquire($mutex_lock, 30)) {
            try {
                if ($delivery_details = $this->getLastDeliveryId($this->order_id)) {
                    // Pending Delivery
                    if ($delivery_details['status'] === 1) {
                        $products_id = $this->getProductIdFromOfferId($delivery_details['offer_id']);
                        $order_details = $this->getG2GOrderDetail($this->order_id);
                        if ($products_id && $order_details) {
                            $product_data = Products::getProducts($products_id);
                            if($product_data){
                                //pull pin
                                $g2g_og_order_model = new G2gOgOrderMapper();
                                $g2gOgOrder = G2gOgOrderMapper::find()->where(['g2g_order_id' => $this->order_id])->one();
                                if($g2gOgOrder){
                                    $g2gOgOrder->delivery_id = $delivery_details['delivery_id'];
                                    $g2gOgOrder->update(false, ['delivery_id']);
                                } else {
                                    $data = $this->_getOrderInput($product_data, $order_details);
                                    $transaction = Yii::$app->db_offgamers->beginTransaction();
                                    $og_transaction = Yii::$app->db_og->beginTransaction();
                                    $error = '';
                                    $orders_id = 0;
                                    $time = time();
                                    try {
                                        $orders_id = Order::createOrder($data);
                                        $transaction->commit();
                                        $og_transaction->commit();
        
                                        $g2g_og_order_model = new G2gOgOrderMapper();
                                        $g2g_og_order_model->setAttributes([
                                            'g2g_order_id' => $this->order_id,
                                            'og_order_id' => $orders_id,
                                            'delivery_id' => $delivery_details['delivery_id'],
                                            'extra_info' => "",
                                            'status' => 0,
                                            'error' => 0,
                                            'created_at' => $time,
                                            'updated_at' => $time
                                        ]);
                                        $g2g_og_order_model->save();
        
                                    } catch (\Exception $e) {
                                        $transaction->rollBack();
                                        $og_transaction->rollBack();
                                        $error = $e->getMessage();
                                        Yii::$app->slack->send('G2G Order Failed to create g2g_og_order_mapper: ' . $this->order_id, [
                                            [
                                                'color' => 'warning',
                                                'text' => Json::encode($error)
                                            ]
                                        ]);
                                    }
                                }
                            } else {
                                //inventory

                                $codes_list = (new Inventory)->customRequest('inventory', 'g2g-order-delivery', [
                                    'reseller_id' => $this->reseller,
                                    'order_id' => $this->order_id,
                                    'products_id' => $products_id,
                                    'qty' => $order_details['undelivered_qty'],
                                    'pending_qty' => $delivery_details['pending_qty'],
                                    'order_currency' => $order_details['currency'] ?? '',
                                    'product_price_in_currency' => $order_details['unit_price'] ?? '',
                                    'product_price_in_usd' => Yii::$app->currency->advanceCurrencyConversion($order_details['unit_price'], 'USD', $order_details['currency'], 'spot'),
                                    'reprocess_restock_request' => $this->reprocess_restock_request,
                                ]);
    
                                // If got available codes, perform delivery
                                if (!empty($codes_list['delivery_pin'])) {
                                    $this->deliverOrder($this->order_id, $delivery_details['delivery_id'], $this->parseSoftPinToDelivery($codes_list['delivery_pin']));
                                    foreach ($codes_list['delivery_pin'] as $codes) {
                                        $stock_movement_model = new G2gStockMovementHistory();
                                        $stock_movement_model->setAttributes([
                                            'g2g_order_id' => $this->order_id,
                                            'custom_products_code_id' => $codes['id'],
                                            'status' => 1
                                        ]);
                                        $stock_movement_model->save();
                                    }
                                } elseif (isset($codes_list['status_message'])) {
                                    // if ($codes_list['status_message'] == 'OUT_OF_STOCK') {
                                    //     Yii::$app->slack->send('G2G Order Out Of Stock ({' . $this->order_id . '})', [], 'BDT_G2G_ORDER');
                                    // }
                                    if ($codes_list['status_message'] == 'OUT_OF_STOCK') {
                                        Yii::$app->slack->send('G2G Order Out Of Stock ({' . $this->order_id . '})', [], 'DEBUG');
                                    } else {
                                        Yii::$app->slack->send('G2G Order Others status message ({' . $codes_list['status_message'] . '}} - ({' . $this->order_id . '})', [], 'DEBUG');
                                    }
                                } else {
                                    // Yii::$app->slack->send('G2G Order Unknown Error ({' . $this->order_id . '})', [], 'BDT_G2G_ORDER');
                                    Yii::$app->slack->send('G2G Order Unknown Error ({' . $this->order_id . '})', [], 'DEBUG');
                                }
                            }
                        }
                    } elseif ($delivery_details['status'] != 2) {
                        Yii::$app->slack->send('G2G Order Delivery Issues : ' . $this->order_id, [
                            [
                                'color' => 'warning',
                                'text' => Json::encode($delivery_details)
                            ]
                        ]);
                    }
                }
            } finally {
                Yii::$app->mutex->release($mutex_lock);
            }
        } else {
            throw new \Exception('Failed to obtain lock');
        }
    }

    private function getProductIdFromOfferId($offer_id)
    {
        $product_id = G2gProductsMapping::find()->select('products_id')->where(['g2g_offer_id' => $offer_id])->scalar();

        if (!$product_id) {
            Yii::$app->slack->send('Missing Product Mapping for Order (' . $this->order_id . ')', array(
                array(
                    'color' => 'warning',
                    'text' => ''
                ),
            ));
        }

        return $product_id;
    }

    private function parseSoftPinToDelivery($code_list)
    {
        $return_arr = [];
        foreach ($code_list as $code) {
            switch ($code['type']) {
                case 'soft':
                    $return_arr[] = [
                        'content' => 'GameKongs Reference : ' . $code['id'] . ',' . str_replace('<br>', ',', $code['content']),
                        'content_type' => 'text/plain',
                        'reference_id' => $code['id']
                    ];
                    break;

                default:
                    break;
            }
        }

        return $return_arr;
    }

    public function getLastDeliveryId($g2g_order_id)
    {
        $return_array = [
            'status' => 0,
            'status_message' => '',
            'delivery_id' => '',
            'offer_id' => '',
            'pending_qty' => 0
        ];

        $data = $this->sendRequest('GET', 'orders/' . $g2g_order_id . '/delivery');

        if (isset($data['delivery_list'])) {
            $last_delivery = end($data['delivery_list']);
            if (isset($last_delivery['offer_id']) && isset($last_delivery['delivery_summary'])) {
                $offer_id = $last_delivery['offer_id'];
                $last_delivery = $last_delivery['delivery_summary'];
                if (isset($last_delivery['delivery_status'])) {
                    if ($last_delivery['delivery_status'] == 'pending') {
                        $return_array = [
                            'offer_id' => $offer_id,
                            'status' => 1,
                            'delivery_id' => $last_delivery['delivery_id'],
                            'pending_qty' => $last_delivery['requested_qty']
                        ];
                    } elseif (in_array($last_delivery['delivery_status'], ['delivered', 'cancelled'])) {
                        $return_array = [
                            'status' => 2,
                            'status_message' => $last_delivery['delivery_status']
                        ];
                    }
                }
            }
        } else {
            //TODO::Throw Exception
            throw new \Exception('Failed to obtain delivery info');
        }

        return $return_array;
    }

    public function deliverOrder($g2g_order_id, $delivery_id, $codes_list)
    {
        $data = [
            'ignore_label_format' => true,
            'delivery_id' => $delivery_id,
            'codes' => $codes_list
        ];

        $this->sendRequest('POST', 'orders/' . $g2g_order_id . '/delivery', $data);
    }

    private function parseResponse($response, $throw_exception = true)
    {
        if ($data = $response->getBody()) {
            $parsed_data = Json::decode($data);

            if (isset($parsed_data['code']) && $parsed_data['code'] === 20000001) {
                return $parsed_data['payload'];
            } elseif (isset($parsed_data['code']) && isset($parsed_data['message'])) {
                $this->error_code = $parsed_data['code'];
                $this->error_message = $parsed_data['message'];
            }
        }

        if (!$throw_exception) {
            return $response;
        } else {
            Yii::$app->slack->send('Invalid Response from G2G API', array(
                array(
                    'color' => 'warning',
                    'text' => $this->error_code . ' : ' . $this->error_message
                ),
            ));
            throw new \Exception('Invalid Response from G2G API');
        }
    }

    private function sendRequest($method, $path, $body = [], $throw_exception = true)
    {
        $time = time();
        $this->getConfig();
        $this->initClient();

        $signature = $this->generateSignature($path, $time);

        $options = [
            'headers' => [
                'g2g-api-key' => $this->api_key,
                'g2g-userid' => $this->user_id,
                'g2g-signature' => $signature,
                'g2g-timestamp' => $time,
                'Content-Type' => 'application/json'
            ],
            'http_errors' => false
        ];


        if ($body) {
            $options['json'] = $body;
        }

        return $this->parseResponse($this->client->request($method, $this->base_url . '/' . $path, $options), $throw_exception);
    }

    public function generateSignature($path, $timestamp)
    {
        $string = '/' . self::API_VERSION . '/' . $path . $this->api_key . $this->user_id . $timestamp;
        return hash_hmac('sha256', $string, $this->api_secret);
    }

    protected function getG2GOrderDetail($order_id)
    {
        $data = $this->sendRequest('GET', sprintf(self::API_G2G_GET_ORDER, $order_id));
        if ($data) {
            $data["undelivered_qty"] = $data["purchased_qty"] - $data["delivered_qty"];
            return $data;
        } else {
            Yii::$app->slack->send("Failed to get order details for G2G Order ID: " . $order_id);
            return false;
        }
    }

    private function _getOrderInput($product_data, $order_details){
        $product_name = $product_data["products_alt_name"];
        if(!$product_name){
            $product_name = ($product_data["products_name"]) ? $product_data["products_name"] : $product_data["products_cat_path"] ;
        }
        $data = Yii::$app->params['g2g.og.order.mapper'];
        $undelivered_qty = $order_details['undelivered_qty'];
        // MYR
        $unit_price_myr = $order_details['unit_price'];
        $price_myr = $unit_price_myr * $undelivered_qty;
        $format_price_myr = Yii::$app->currency->format($order_details['currency'], $price_myr);
        $format_price_myr_0 = Yii::$app->currency->format($order_details['currency'], 0);
        // USD
        $unit_price_usd = Yii::$app->currency->advanceCurrencyConversion($unit_price_myr, $order_details['currency'], 'USD', false, 'spot');
        $price_usd = $unit_price_usd * $undelivered_qty;
        $format_price_usd = Yii::$app->currency->format('USD', $price_usd);
        $format_price_usd_0 = Yii::$app->currency->format('USD', 0);
        
        $data["currency_value"] = Yii::$app->currency->advanceCurrencyConversionRate('USD', $order_details['currency'], 'spot');
        $data['products'] = [
            [
                "id" => $product_data["products_id"],
                "name" => $product_name,
                "price" => $unit_price_myr, // in MYR
                "base_currency" => $order_details['currency'], // in MYR
                "quantity" => $undelivered_qty,
                "final_price"=> $unit_price_myr, // in MYR
                "normal_price"=> $unit_price_myr, // in MYR
                "products_categories_id" => $product_data["products_main_cat_id"],
                "products_package" => [],
                "cat_id_path" => $product_data["products_cat_id_path"],
                "discounts" => [0],
                "custom_products_type_id" => $product_data["custom_products_type_id"],
                "custom_products_type_child_id" => $product_data["custom_products_type_child_id"],
                "custom_content" => [
                    "delivery_info" => [
                        "delivery_mode" => 5
                    ]
                ],
                "model" => $product_data["products_model"],
                "sub_products_id" => "",
                "products_type" =>$product_data["products_type"],
                // storage_price in USD
                "storage_price" => [
                    "normal_price" => $unit_price_usd,
                    "price" => $unit_price_usd,
                    "final_price" => $unit_price_usd
                ],
                "op_info" => [
                    "rebate_point" => 0,
                    "rebate_point_extra" => 0,
                    "rebate_point_formula" => "-"
                ]
            ]
        ];
        $data['ot'] = [
            [
                "code" => "ot_subtotal",
                "title" => "Sub-Total:",
                "display_title" => "Order Amount",
                "checkout_value" => $price_myr, // in MYR
                "storage_text" => $format_price_myr, // in MYR
                "storage_value" => $price_usd, // in USD
                "sort_order" => 1,
                "input" => [],
                "output" => []
            ],
            [
                "code" => "ot_coupon",
                "title" => "Discount Coupons: ",
                "display_title" => "Discount Coupons",
                "checkout_value" => "0.00",
                "storage_text" => $format_price_myr_0, // format in MYR
                "storage_value" => 0,
                "sort_order" => 9,
                "input" => [
                    "coupon_code" => ""
                ],
                "output" => []
            ],
            [
                "code" => "ot_gv",
                "title" => "Store Credit:",
                "display_title" => "Store Credit Deduction",
                "checkout_value" => $price_myr, // in MYR
                "storage_text" => $format_price_myr, // in MYR
                "storage_value" => $price_usd, // in USD
                "sort_order" => 740,
                "input" => [],
            ],
            [
                "code" => "ot_total",
                "title" => "Total:",
                "display_title" => "Payable Amount",
                "checkout_value" => "0.00",
                "storage_text" => $format_price_myr_0, // in MYR
                "storage_value" => 0,
                "sort_order" => 900,
                "input" => [],
                "output" => []
            ]
        ];
        return $data;
    }
}