<?php

namespace micro\components;

use Yii;
use yii\helpers\Json;

class Algolia extends Yii\Base\Component
{
    public $key, $secret, $indices, $batchUpdate = array(), $batchDelete = array();

    private $client, $index;

    public function getClient()
    {
        if (!$this->client) {
            $params = Yii::$app->params;
            if (!empty($params['proxy'])) {
                $proxy = new \GuzzleHttp\Client(['proxy' => $params['proxy']]);
                $httpClient = new \Algolia\AlgoliaSearch\Http\Guzzle6HttpClient($proxy);
                \Algolia\AlgoliaSearch\Algolia::setHttpClient($httpClient);
            }

            $this->client = \Algolia\AlgoliaSearch\SearchClient::create(
                $this->key,
                $this->secret
            );
        }
        return $this->client;
    }

    public function setIndex($name)
    {
        $client = $this->getClient();
        $index_name = $this->indices[$name];
        $this->index = $client->initIndex($index_name);
        return $this->index;
    }

    public function setBatchQuery($type, $index, $value)
    {
        switch ($type) {
            case 'update':
                $data = &$this->batchUpdate;
                if (!isset($data[$index])) {
                    $data[$index] = array($value);
                } else {
                    array_push($data[$index], $value);
                }
                break;

            case 'delete':
                $data = &$this->batchDelete;
                if (!isset($data[$index])) {
                    $data[$index] = array($value);
                } else {
                    array_push($data[$index], $value);
                }
                break;
        }

    }

    public function execBatchQuery($count = 1)
    {
        try {
            foreach ($this->batchUpdate as $index => $data) {
                $client = $this->setIndex($index);
                $client->saveObjects($data);
                unset($this->batchUpdate[$index]);
            }

            foreach ($this->batchDelete as $index => $data) {
                $client = $this->setIndex($index);
                $client->deleteObjects($data);
                unset($this->batchDelete[$index]);
            }

        } catch (\Exception $e) {
            if ($count <= 3) {
                $count++;
                $this->execBatchQuery($count);
            } else {
                GeneralCom::slackStackNotification('debug', 'Posting to Algolia Failed with exception : ' . $e->getMessage(), 'danger');
            }
        }
    }

}