<?php

namespace micro\components;

use Yii;

class GeneralCom extends \offgamers\base\components\GeneralCom
{
    public static function getArrayData($key, $data, $default_data)
    {
        return !empty($data[$key]) ? $data[$key] : ($default_data[$key] ?? '');
    }

    public static function getColumnValueWithDefault($data, $default_data, $key_list)
    {
        $return_arr = [];

        foreach ($key_list as $key) {
            $return_arr[$key] = self::getArrayData($key, $data, $default_data);
        }

        return $return_arr;
    }

    public static function getFirstParagraph($content, $html_tag)
    {
        $content = str_replace("\n", "<br>", $content);
        $content = preg_replace('/<h1[^>]*>([\s\S]*?)<\/h1[^>]*>/', '', $content);
        $parts = explode($html_tag, $content);
        foreach ($parts as $part) {
            $string = trim(strip_tags($part));
            if ($string !== "") {
                return $string;
            }
        }
        $result = trim(strip_tags(implode("", $parts)));
        return $result;
    }

}
