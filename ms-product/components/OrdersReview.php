<?php

namespace micro\components;

use micro\models\ProductsReviewsBlacklist;
use Yii;
use micro\models\Products;
use micro\models\ProductsToCategories;
use micro\models\OrdersProductsReviews;
use micro\models\OrdersProductsReviewsLog;
use micro\models\OrdersProductsReviewsRemarks;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class OrdersReview
{
    public function getAllReviewRecords($params, $page = 1, $limit = 10)
    {
        $model = new OrdersProductsReviews();
        $query = $model::find()
            ->select(['{{orders_products_reviews}}.*'])
            ->orderBy(['updated_at' => SORT_DESC]);

        $filter = false;

        if (!empty($params)) {
            foreach ($params as $key => $value) {
                if (!empty($value)) {
                    switch ($key) {
                        case 'created_at':
                            $date_fr = strtotime(substr($value, 0, strpos($value, '/')));
                            $date_to = strtotime(substr($value, strpos($value, '/') + 1));
                            $query->andWhere(['>=', 'created_at', $date_fr]);
                            $query->andWhere(['<=', 'created_at', $date_to]);
                            $filter = true;
                            break;
                        case 'products_name':
                            $products_ids = Products::getProductIds($value, 1);
                            $query->andWhere(['products_id' => $products_ids]);
                            $filter = true;
                            break;
                        case 'review_score':
                            if ($value == '0') {
                                $query->andWhere(['>=', 'review_score', '1']);
                                $query->andWhere(['<=', 'review_score', '2']);
                            } elseif ($value == '6') {
                                $query->andWhere(['>=', 'review_score', '1']);
                            } else {
                                $query->andWhere(['=', 'review_score', $value]);
                            }
                            $filter = true;
                            break;
                        case 'page':
                        case 'pageSize':
                            break;
                        default:
                            $query->andWhere(['like', $key, $value]);
                            $filter = true;
                            break;
                    }
                }
            }
            if (!$filter) {
                $query->andWhere(['>=', 'review_score', '1']);
                $query->andWhere(['<=', 'review_score', '2']);
            }
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [];
            
            foreach ($obj as $key => $value) {
                $item[$key] = $value;
            }
            $item['products_name'] = Products::getProductName($item['products_id'], 1);
            $item['updated_by'] = ($item['updated_at'] > $item['created_at'] && $item['updated_by'] != $item['created_by']) ? $item['updated_by'] : '';
            $item['created_at'] = date("Y-m-d H:i:s", $item['created_at']);
            $item['date_fr'] = (isset($param['date_fr']) ? $param['date_fr'] : '');
            $item['date_to'] = (isset($param['date_to']) ? $param['date_to'] : '');
            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];
        
        return ($return_array);
    }

    public static function get($id)
    {
        $model = OrdersProductsReviews::find()
            ->select(['{{orders_products_reviews}}.*'])
            ->where(['review_id' => $id])->asArray()->one();

        foreach ($model as $key => $value) {
            $return_array[$key] = $value;
        }
        $return_array['products_name'] = Products::getProductName($return_array['products_id'], 1);
        $remarks = OrdersProductsReviewsRemarks::find()->where(['review_id' => $id])->orderBy('created_at DESC')->asArray()->all();

        $return_array['Remarks'] = $remarks;

        return $return_array;
    }

    public static function update($params)
    {
        $model = OrdersProductsReviews::findOne($params['review_id']);

        if (isset($params['status'])) {
            $model->status = $params['status'];
        } else {
            $model->status = '2';
        }

        $model->visibility = $params['visibility'];
        $model->updated_by = $params['user'];

        $model->update();

        $Remark = $params['OrderReviewRemarks'];

        if (!empty($Remark['remarks'])) {
            self::insertRemarks($params['review_id'], $Remark['remarks'], $params['user'], $Remark['remarks_status']);
        }
        
        foreach ($params['OrderReviewRemarksHist'] as $RemarkHist) {
            self::updateRemarks($RemarkHist['remarks_id'], $params['user'], $RemarkHist['remarks_status']);

        }

        return true;
    }

    public static function insertRemarks($review_id, $remarks, $user, $status)
    {
        $model = new OrdersProductsReviewsRemarks();
        $params = ['review_id' => $review_id, 'remarks' => $remarks, 'user' => $user, 'status' => $status];

        return $model->saveRemarksRecords($params);
    }

    public static function updateRemarks($remarks_id, $user, $status)
    {
        $model = OrdersProductsReviewsRemarks::findOne($remarks_id);
        if ($model->status == $status) {
            return true;
        }
        $model->status = $status;
        $model->updated_by = $user;

        return $model->update();
    }

    public function getReviewStatusByOid($orders_id)
    {
        $review_records = OrdersProductsReviews::find()
            ->select('orders_id, status, count(*) as cnt')
            ->where(['=', 'orders_id', $orders_id])
            ->asArray()
            ->groupBy('orders_id, status')
            ->all();
        if (empty($review_records)) {
            $return_status = '1';
        } else {
            foreach ($review_records as $row) {
                if ($row['status'] == '3') {
                    return '3';
                } else {
                    $return_status = '2';
                }
            }
        }
        return $return_status;
    }

    public function getEditFlagByOpid($orders_products_id)
    {
        $review_records = OrdersProductsReviews::find()
            ->select('orders_products_id, status')
            ->where(['=', 'orders_products_id', $orders_products_id])
            ->asArray()
            ->one();
        if (empty($review_records)) {
            $return_status = true;
        } else {
            if ($review_records['status'] == '3') {
                $return_status = true;
            } else {
                $return_status = false;
            }
        }
        return $return_status;
    }

    public function getReviewIdsByOid($orders_id)
    {
        $review_records = OrdersProductsReviews::find()
            ->where(['=', 'orders_id', $orders_id])
            ->all();

        return $review_records;
    }

    public function getReviewByOPid($orders_products_id)
    {
        $review_records = OrdersProductsReviews::find()
            ->where(['=', 'orders_products_id', $orders_products_id])
            ->all();

        return $review_records;
    }

    public function getLatestReviewByOPid($orders_products_id)
    {
        $review_records = OrdersProductsReviews::find()
            ->where(['=', 'orders_products_id', $orders_products_id])
            ->orderBy(['created_at' => SORT_DESC])
            ->one();


        return $review_records;
    }

    public function saveReviewRecords($params)
    {
        if (!empty($params['review_id'])) {
            $model = OrdersProductsReviews::findOne($params['review_id']);

            $model->status = '2';
            $model->review_score = $params['review_score'];
            $model->comment = $params['comment'];
            $model->updated_by = $params['user_id'];

            $model->update();
            $this->saveLog($params['review_id'], 'Customer Review updated', $params['user_id']);
            return true;
        } else {
            $OrdersProductsReviews = new OrdersProductsReviews();

            $result = $OrdersProductsReviews->saveReviewRecords($params);
            $this->saveLog($OrdersProductsReviews->review_id, 'Customer Review inserted', $params['user_id']);
            return $result;
        }
    }

    public function getReviewRemarksByRid($review_id, $view_all = false)
    {
        $query = OrdersProductsReviewsRemarks::find()->Where(['=', 'review_id', $review_id]);
        if (!$view_all) {
            $query->andWhere(['=', 'status', '1']);
        }
        $query->orderBy('created_at');

        return $query->asArray()->all();
    }

    public function saveLog($review_id, $activity, $user)
    {
        $model = new OrdersProductsReviewsLog();
        $params = ['review_id' => $review_id, 'user' => $user, 'activity' => $activity];

        return $model->saveLog($params);
    }

    public function getReviewsSummaryByCid($categories_id, $blacklist)
    {
        $query = OrdersProductsReviews::find()
            ->select(['round(sum(review_score)/count(*),1) avg_score', 'count(*) total'])
            ->Where(['categories_id' => $categories_id])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2']);

        $result = $query->asArray()->one();

        if (empty($result['avg_score']) || (float)$result['avg_score'] <= (float)'0') {
            $result['avg_score'] = '0';
        }
        $result['label'] = 'TEXT_RATING_' . floor($result['avg_score']);

        $rating_bd_query = OrdersProductsReviews::find()
            ->select(['review_score', 'count(*) cnt'])
            ->Where(['categories_id' => $categories_id])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2'])
            ->groupBy('review_score')
            ->asArray()
            ->all();

        $breakdown = [
            'TEXT_RATING_1' => '0',
            'TEXT_RATING_2' => '0',
            'TEXT_RATING_3' => '0',
            'TEXT_RATING_4' => '0',
            'TEXT_RATING_5' => '0',
        ];

        foreach ($rating_bd_query as $row) {
            $key = 'TEXT_RATING_' . floor($row['review_score']);
            $breakdown[$key] = $row['cnt'];
        }

        $result['breakdown'] = $breakdown;

        return $result;
    }

    public function getReviewsByCid($categories_ids, $page = 1, $limit = 6, $sort = '0')
    {
        $blacklist = ProductsReviewsBlacklist::getAllBlacklistItem();
        $query = OrdersProductsReviews::find()
            ->select(['review_id', 'created_by', 'created_at', 'review_score', 'comment'])
            ->Where(['categories_id' => $categories_ids])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2']);

        switch ($sort) {
            case '0': //date: latest
                $query->orderBy(['created_at' => SORT_DESC]);
                break;
            case '1': //date: oldest
                $query->orderBy(['created_at' => SORT_ASC]);
                break;
            case '2': //score: highest
                $query->orderBy(['review_score' => SORT_DESC]);
                break;
            case '3': //score: lowest
                $query->orderBy(['review_score' => SORT_ASC]);
                break;
            default:
                $query->orderBy(['created_at' => SORT_DESC]);
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $review = $result;

        foreach ($result as $idx => $review_row) {
            $remarks_query = OrdersProductsReviewsRemarks::find()
                ->select('remarks')
                ->orderBy('created_at')
                ->where(['status' => '1', 'review_id' => $review_row['review_id']])
                ->asArray()
                ->all();
            $review[$idx]['remark'] = false;
            if (!empty($remarks_query)) {
                $review[$idx]['remark'] = true;
            }
        }
        $review_summary = $this->getReviewsSummaryByCid($categories_ids, $blacklist);


        $data = [];

        $data['summary'] = $review_summary;
        $data['reviews'] = $review;
        $data['blacklist'] = false;

        $pagination['page'] = $page;
        $pagination['total_pages'] = ceil($review_summary['total'] / $limit);
        $pagination['sort'] = $sort;

        $data['pagination'] = $pagination;


        return $data;
    }

    public function getReviewsSummaryByPid($products_id, $blacklist)
    {
        $query = OrdersProductsReviews::find()
            ->select(['round(sum(review_score)/count(*),1) avg_score', 'count(*) total'])
            ->Where(['products_id' => $products_id])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2']);

        $result = $query->asArray()->one();

        if (empty($result['avg_score']) || (float)$result['avg_score'] <= (float)'0') {
            $result['avg_score'] = '0';
        }
        $result['label'] = 'TEXT_RATING_' . floor($result['avg_score']);

        $rating_bd_query = OrdersProductsReviews::find()
            ->select(['review_score', 'count(*) cnt'])
            ->Where(['products_id' => $products_id])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2'])
            ->groupBy('review_score')
            ->asArray()
            ->all();

        $breakdown = [
            'TEXT_RATING_1' => '0',
            'TEXT_RATING_2' => '0',
            'TEXT_RATING_3' => '0',
            'TEXT_RATING_4' => '0',
            'TEXT_RATING_5' => '0',
        ];

        foreach ($rating_bd_query as $row) {
            $key = 'TEXT_RATING_' . floor($row['review_score']);
            $breakdown[$key] = $row['cnt'];
        }

        $result['breakdown'] = $breakdown;

        return $result;
    }

    public function getReviewsByPid($products_id, $page = 1, $limit = 6, $sort = '0')
    {
        $blacklist = ProductsReviewsBlacklist::getAllBlacklistItem();
        $query = OrdersProductsReviews::find()
            ->select(['review_id', 'created_by', 'created_at', 'review_score', 'comment'])
            ->Where(['products_id' => $products_id])
            ->andWhere(['NOT', ['products_id' => $blacklist['products']]])
            ->andWhere(['NOT', ['categories_id' => $blacklist['category']]])
            ->andWhere(['visibility' => '1'])
            ->andWhere(['status' => '2']);

        switch ($sort) {
            case '0': //date: latest
                $query->orderBy(['created_at' => SORT_DESC]);
                break;
            case '1': //date: oldest
                $query->orderBy(['created_at' => SORT_ASC]);
                break;
            case '2': //score: highest
                $query->orderBy(['review_score' => SORT_DESC]);
                break;
            case '3': //score: lowest
                $query->orderBy(['review_score' => SORT_ASC]);
                break;
            default:
                $query->orderBy(['created_at' => SORT_DESC]);
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $review = $result;

        foreach ($result as $idx => $review_row) {
            $remarks_query = OrdersProductsReviewsRemarks::find()
                ->select('remarks')
                ->orderBy('created_at')
                ->where(['status' => '1', 'review_id' => $review_row['review_id']])
                ->asArray()
                ->all();
            $review[$idx]['remark'] = false;
            if (!empty($remarks_query)) {
                $review[$idx]['remark'] = true;
            }
        }
        $review_summary = $this->getReviewsSummaryByPid($products_id, $blacklist);

        $data = [];

        $data['summary'] = $review_summary;
        $data['reviews'] = $review;
        $data['blacklist'] = false;

        $pagination['page'] = $page;
        $pagination['total_pages'] = ceil($review_summary['total'] / $limit);
        $pagination['sort'] = $sort;

        $data['pagination'] = $pagination;


        return $data;
    }
}