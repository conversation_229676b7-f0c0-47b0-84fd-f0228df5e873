<?php

namespace micro\components;

use micro\models\CategoriesDescription;
use micro\models\CategoriesStructures;
use micro\models\GameBlogDescription;
use Yii;
use micro\models\Products;
use micro\models\ProductsToCategories;
use micro\models\Categories;
use micro\models\ProductsReviewsBlacklist;
use micro\models\OrdersProductsReviewsLog;
use micro\models\OrdersProductsReviewsRemarks;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class ProductReview
{
    public function getAllBlacklistItems($params, $page = 1, $limit = 10)
    {

        $query = ProductsReviewsBlacklist::find()
            ->select(['*'])
            ->orderBy(['created_at' => SORT_DESC])
            ->where(['status'=>'1']);


        if (!empty($params)) {
            foreach ($params as $key => $value) {
                if (!empty($value)) {
                    switch ($key) {
                        case 'item_name':
                            $products_ids = Products::getProductIds($value, 1);
                            $categories_ids = Categories::getCategoriesIds($value, 1);

                            $query->andWhere(['OR',['AND',['item_id' => $products_ids],['item_type' => 'Products']],['AND',['item_id' => $categories_ids],['item_type' => 'Category']]]);
                            break;
                        case 'item_type':
                            $query->andWhere(['like', 'item_type', $value]);
                            break;
                        case 'page':
                        case 'pageSize':
                            break;
                        default:
                            $query->andWhere(['like', $key, $value]);
                            break;
                    }
                }
            }
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [];

            foreach ($obj as $key => $value) {
                $item[$key] = $value;
            }
            switch ($obj['item_type'])
            {
                case 'Category':
                    $item['item_name'] = Categories::getCategoriesName($item['item_id'], 1);
                    break;
                case 'Products':
                    $item['item_name'] = Products::getProductName($item['item_id'], 1);
                    break;
                default:
                    break;
            }
            $item['created_at'] = date("Y-m-d H:i:s", $item['created_at']);

            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];

        return ($return_array);
    }

    public static function deleteByBlacklistId($params)
    {
        $model = ProductsReviewsBlacklist::findOne($params['blacklist_id']);
        $model->status = 0;
        $model->updated_by = $params['user'];

        return $model->update();
    }

    public static function addBlacklist($params)
    {
        $model = new ProductsReviewsBlacklist();
        $model->item_id = $params['id'];
        $model->item_type = $params['type'];
        $model->status = 1;
        $model->updated_by = $params['user'];
        $model->created_by = $params['user'];

        return $model->save();
    }

    public static function getItemList($input)
    {
        $output = [];

        if (empty($input['list']) && strtolower($input['type']) == 'products') {
            $input['type'] = 'category';
        }


        switch (strtolower($input['type'])) {
            case 'category':
                $active_category = CategoriesStructures::find()->select('categories_structures_value')->where(['categories_structures_key' => 'games'])->asArray()->one();
                $category_list = explode(",", $active_category['categories_structures_value']);
                $list = ProductsReviewsBlacklist::getAllBlacklistItem();
                $data = CategoriesDescription::find()->select(['categories_id', 'categories_name'])->where(['categories_id' => $category_list, 'language_id' => 1])->andWhere(['NOT',['categories_id' => $list['category']]])->asArray()->all();
                $output = ArrayHelper::map($data, 'categories_id', 'categories_name');
                break;

            case 'products':
                $category_list = Categories::getAllSubCategoryByCategoryId([$input['list']]);
                $list = ProductsReviewsBlacklist::getAllBlacklistItem();
                $data = Products::getAllProductByCategoryId($category_list, $list['products']);
                $output = $data;
                break;
        }
        return $output;
    }

    public static function isProductReviewable($id, $type)
    {
        $result = ProductsReviewsBlacklist::find()->select(['item_id'])->where(['status'=>'1', 'item_type'=>$type, 'item_id' => $id])->asArray()->one();
        if(!empty($result)){
            $output = false;
        }else{
            $output = true;
        }
        return $output;
    }

}