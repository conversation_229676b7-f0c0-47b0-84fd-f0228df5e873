<?php

namespace micro\components;

use micro\models\CategoriesDescription;
use micro\models\CategoriesStructures;
use micro\models\GameBlogDescription;
use offgamers\base\models\ProductsTaxCategory;
use Yii;
use micro\models\Products;
use micro\models\ProductsToCategories;
use micro\models\Categories;
use micro\models\ProductsReviewsBlacklist;
use micro\models\OrdersProductsReviewsLog;
use micro\models\OrdersProductsReviewsRemarks;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class ProductsConfig
{
    public static function getItemList($input)
    {
        $output = [];

        if (empty($input['list']) && strtolower($input['type']) == 'products') {
            $input['type'] = 'category';
        }


        switch (strtolower($input['type'])) {
            case 'category':
                $active_category = CategoriesStructures::find()->select('categories_structures_value')->where(['categories_structures_key' => 'games'])->asArray()->one();
                $category_list = explode(",", $active_category['categories_structures_value']);
                $list = ProductsReviewsBlacklist::getAllBlacklistItem();
                $data = CategoriesDescription::find()->select(['categories_id', 'categories_name'])->where(['categories_id' => $category_list, 'language_id' => 1])->andWhere(['NOT',['categories_id' => $list['category']]])->asArray()->all();
                $output = ArrayHelper::map($data, 'categories_id', 'categories_name');
                break;

            case 'products':
                $category_list = Categories::getAllSubCategoryByCategoryId([$input['list']]);
                $data = Products::getAllProductByCategoryId($category_list);
                $output = $data;
                $pids = array_keys($output);
                $products_tax_cat = new ProductsTaxCategory();
                $result = $products_tax_cat->find()->select('products_id')->where(['products_id' => $pids])->asArray()->all();
                $result = ArrayHelper::getColumn($result,'products_id');
                if (!empty($input['assign'])) {
                    if($input['assign']=='true'){
                        foreach($pids as $pid){
                            if(!in_array($pid,$result)){
                                unset($output[$pid]);
                            }
                        }
                    }else{
                        foreach($result as $row)
                        {
                            if(in_array($row, $pids))
                            {
                                unset($output[$row]);
                            }
                        }
                    }
                }
                break;
        }
        return $output;
    }

}