<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
    require(__DIR__ . '/params-encoded.php')
);

return [
    'id' => 'ms-product',

    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'params' => $params,
    'bootstrap' => ['log'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'DEBUG' => $params['slack.webhook.debug'],
                'BDT_INFO' => $params['slack.webhook.bdt.info']
            ]
        ],
        'algolia' => [
            'class' => 'micro\components\Algolia',
            'key' => $params['algolia.application'],
            'secret' => $params['algolia.admin.key'],
            'indices' => [
                'product' => $params['algolia.index.product'],
                'category' => $params['algolia.index.category'],
                'brand' => $params['algolia.index.brand']
            ]
        ],
        'currency' => 'offgamers\base\components\Currency',
        'enum' => [
            'class' => 'offgamers\base\components\Enum'
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'request' => [
            'enableCookieValidation' => false,
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ],
        ],
        'i18n' => [
            'translations' => [
                'seo' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    //'basePath' => '@app/messages',
                    'sourceLanguage' => 'en',
                    'fileMap' => [
                        'app' => 'seo.php',
                    ],
                ],
            ],
        ],
        'response' => [
            'class' => 'offgamers\base\components\Response',
            'on beforeSend' => function ($event) {
                $response = $event->sender;
                if ($response->data !== null) {
                    Yii::$app->algolia->execBatchQuery();
                    Yii::$app->slack->sendMessageStack('bdt_notify', 'Product Update Notification', 'BDT_INFO');
                    Yii::$app->slack->sendMessageStack('debug', 'Error Notification', 'DEBUG');
                }
            },
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => $params['aws.key'],
            'secret' => $params['aws.secret'],
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_LOG' => [
                    'key' => '********************',
                    'secret' => 'q4Wn1ixUaXW1wliS2AcE398vRxJ9YG1GdEJsdNHI',
                    'region' => 'ap-southeast-1',
                    'bucket_key' => 'bucket-db-archive',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                ],
            ],
        ],
        'pinyin' => [
            'class' => 'Overtrue\Pinyin\Pinyin'
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'errorHandler' => [
            'class' => 'offgamers\base\components\ErrorHandler'
        ]
    ]

];


