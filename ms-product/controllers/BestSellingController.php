<?php

namespace micro\controllers;

use micro\models\BestSelling;
use offgamers\base\controllers\RestController;

class BestSellingController extends RestController
{
    public function actionIndex()
    {
        $country_code = $this->input['country_code'] ?? '';
        $language_id = $this->input['language_id'] ?? '';
        $ip_country = $this->input['ip_country'] ?? $country_code;

        return BestSelling::getUrlList($country_code, $language_id, $ip_country);
    }
}
