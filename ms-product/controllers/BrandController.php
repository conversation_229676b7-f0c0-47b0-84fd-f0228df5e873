<?php

namespace micro\controllers;

use micro\models\{Brand, BrandSearch, Category};
use offgamers\base\controllers\RestController;
use Throwable;
use Yii;
use yii\web\HttpException;

class BrandController extends RestController
{
    public function actionIndex()
    {
        $getAll = $this->input['all'] ?? 0;

        $query = [];
        $query['name_search'] = $this->input['name_search'] ?? '';
        $query['status'] = $this->input['status'] ?? '';
        $query['all'] = $getAll == 1;

        $page = $this->input['page'] ?? 1;
        $pageSize = $this->input['pageSize'] ?? 20;

        return (new BrandSearch())->search(
            $query,
            $page,
            $pageSize
        );
    }

    /**
     * @throws HttpException
     */
    public function actionView()
    {
        $brandSearch = new BrandSearch();

        return $brandSearch->searchByBrandId($this->input);
    }

    public function actionCreate()
    {
        $brandModel = new Brand();

        return $brandModel->saveBrand($this->input);
    }

    /**
     * @throws HttpException|Throwable
     */
    public function actionUpdate()
    {
        return (new Brand())->updateBrand($this->input);
    }

    /**
     * @throws Throwable
     * @throws HttpException
     */
    public function actionDelete()
    {
        return (new Brand())->deleteBrand($this->input);
    }

    public function actionCheckSeoUrlExists()
    {
        return (new Brand())->isSeoUrlExists($this->input);
    }

    public function actionList()
    {
        return (new BrandSearch())->getBrandList();
    }

    /**
     * @throws HttpException
     */
    public function actionListing()
    {
        $request = $this->input;

        return (new BrandSearch())->getListing($request);
    }

    public function actionGetBrandInfo()
    {
        return Brand::getBrandInfo(
            $this->input['url_alias'],
            $this->input['language_id'],
            $this->input['country_code'],
            $this->input['ip_country']
        );
    }

    public function actionGetSubBrandInfo()
    {
        if(!isset($this->input['main_url_alias'])){
            $this->input['main_url_alias'] = $this->input['url_alias'];
        }
        return Brand::getSubBrandInfo(
            $this->input['main_url_alias'],
            $this->input['url_alias'],
            $this->input['language_id'],
            $this->input['country_code'],
            $this->input['ip_country']
        );
    }

    public function actionGetCategoryInfo(){
        return Category::getCategoryInfo(
            $this->input['url_alias'],
            $this->input['language_id'],
            $this->input['country_code'],
            $this->input['ip_country']
        );
    }

    public function actionGetReview(){
        $review = [];
        switch($this->input['type']){
            case 'brand':
                $review = Brand::getBrandReview($this->input['url_alias'], $this->input['page'], $this->input['limit']);
                break;
            case 'category':
                $review = Brand::getCategoryReview($this->input['url_alias'], $this->input['page'], $this->input['limit']);
                break;
            case 'product':
                $review = Brand::getProductsReview($this->input['url_alias'], $this->input['page'], $this->input['limit']);
        }
        return $review;
    }

    public function actionSyncAlgolia(){
        ini_set("memory_limit", "-1");
        Brand::getFullBrandList();
        return true;
    }

    public function actionSyncBrandType(){
        Brand::getBrandSupportedType();
        return true;
    }
}
