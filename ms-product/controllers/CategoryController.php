<?php

namespace micro\controllers;

use micro\models\Categories;
use micro\models\Category;
use micro\models\CategoryPromotion;

use offgamers\base\controllers\RestController;

class CategoryController extends RestController
{
    public function actionIndex()
    {
        $page = $this->input['page'] ?? 1;
        $pageSize = $this->input['pageSize'] ?? 10;

        return Category::search($this->input, $page, $pageSize);
    }

    public function actionView()
    {
        return Category::get($this->input);
    }

    public function actionSave()
    {
        return Category::saveCategory($this->input);
    }

    public function actionDelete()
    {
        return Category::deleteCategory($this->input);
    }

    public function actionCheckDuplicateSeoUrl()
    {
        return Category::actionCheckDuplicateSeoURL($this->input);
    }

    public function actionSavePromotion()
    {
        return CategoryPromotion::saveCategoryPromotion($this->input);
    }

    public function actionGetPromotionList()
    {
        return CategoryPromotion::getCategoryPromotionList($this->input);
    }

    public function actionViewPromotion()
    {
        return CategoryPromotion::getCategoryPromotion($this->input);
    }

    public function actionDeletePromotion()
    {
        return CategoryPromotion::deleteCategoryPromotion($this->input);
    }
}