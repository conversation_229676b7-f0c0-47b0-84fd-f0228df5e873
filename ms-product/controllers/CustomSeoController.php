<?php

namespace micro\controllers;

use Yii;
use micro\models\CustomSeo;
use micro\models\CategoriesDescription;
use micro\models\CategoriesStructures;
use micro\models\GameBlogDescription;
use micro\models\Categories;
use micro\models\Products;

class CustomSeoController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        return CustomSeo::getCustomSeoList($this->input, $this->input['page'],
            $this->input['pageSize']);
    }
    
    public function actionGetItemList(){
        return CustomSeo::getItemList($this->input);
    }

    public function actionGetDefaultMeta(){
        return CustomSeo::getMetaTag($this->input);
    }

    public function actionSave(){
        return CustomSeo::saveCustomSeo($this->input);
    }

    public function actionClone(){
        return CustomSeo::cloneCustomSeo($this->input);
    }

    public function actionGetSeoContent(){
        return CustomSeo::getSeoContent($this->input);
    }

    public function actionGenerateDefaultEntry(){
        return CustomSeo::generateDefaultEntry();
    }

}