<?php

namespace micro\controllers;

use Yii;
use micro\models\GameProductAttributeSearch;
use micro\models\GameProductAttribute;
use micro\models\GameProductAttributeTranslation;

class GameProductAttributeController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        return GameProductAttribute::getAttributeWithDefaultMessage($this->input, $this->input['page'],
            $this->input['pageSize']);
    }

    public function actionView()
    {
        return GameProductAttribute::get($this->input);
    }

    public function actionCreate()
    {

        $model = new GameProductAttribute();

        $model->type = $this->input['type'];
        $model->sort_order = $this->input['sort_order'];
        if ($model->save()) {
            foreach ($this->input['description'] as $key => $val) {
                if (!empty($val)) {
                    $model_trans = new GameProductAttributeTranslation();
                    $model_trans->game_product_attribute_id = $model->game_product_attribute_id;
                    $model_trans->language_id = $key;
                    $model_trans->value = $val;
                    $model_trans->save();
                }
            }
            return GameProductAttribute::get($model->game_product_attribute_id);
        }
    }

    public function actionUpdate()
    {
        $model = GameProductAttribute::findOne($this->input['id']);
        $model->type = $this->input['type'];
        $model->sort_order = $this->input['sort_order'];
        $id = $model->game_product_attribute_id;

        if ($id && $model->save()) {
            foreach ($this->input['description'] as $key => $val) {
                if (!empty($val)) {
                    $model_trans = GameProductAttributeTranslation::findOne([
                        'game_product_attribute_id' => $id,
                        'language_id' => $key
                    ]);
                    if(!empty($model_trans)){
                        $model_trans->value = $val;
                        $model_trans->save();
                    }
                    else{
                        $model_trans = new GameProductAttributeTranslation();
                        $model_trans->game_product_attribute_id = $id;
                        $model_trans->language_id = $key;
                        $model_trans->value = $val;
                        $model_trans->save();
                    }
                }
            }
        }
        return GameProductAttribute::get($model->game_product_attribute_id);
    }

    public function actionNextSortOrder(){
        return GameProductAttribute::getNextSortOrder($this->input['type']);
    }

}