<?php

namespace micro\controllers;

use offgamers\base\components\LogComponent;
use Yii;
use micro\models\GamePublisherProduct;
use micro\models\GameProduct;
use micro\models\GameProductLanguage;
use micro\models\GameProductGenre;
use micro\models\GameProductFeature;
use micro\models\GameProductRegion;
use micro\models\GameProductAttributeTranslation;
use yii\helpers\ArrayHelper;

class GamePublisherProductController extends \offgamers\base\controllers\RestController
{
    public function actionIndex()
    {
        return GamePublisherProduct::getGamePublisherProduct($this->input, $this->input['page'],
            $this->input['pageSize']);
    }

    public function actionBatchCreate()
    {
        ini_set("memory_limit", "-1");
        return GamePublisherProduct::batchCreate($this->input);
    }

    public function actionDisableProduct()
    {
        return GamePublisherProduct::disableProduct($this->input);
    }

    public function actionBatchUpdateProduct()
    {
        foreach ($this->input['items'] as $gp_id) {
            GamePublisherProduct::updateProduct($gp_id, $this->input);
        }
        return true;
    }

    public function actionUpdateGameProduct()
    {
        return GameProduct::updateGameProduct($this->input);
    }

    public function actionView()
    {
        return GamePublisherProduct::get($this->input);
    }

    public function actionViewGameProduct()
    {
        return GameProduct::get($this->input);
    }

    public function actionViewGameProductByProductId()
    {
        $product_id = GameProduct::getGameProductIdByProductId($this->input['id']);
        return GameProduct::get($product_id);
    }

    public function actionCheckDuplicateUrlAlias()
    {
        return GamePublisherProduct::checkDuplicateUrlAlias($this->input);
    }

    public function actionUpdateTitleUrlAlias()
    {
        $data = [
            'title' => $this->input['title'],
            'url_alias' => $this->input['url_alias'],
        ];
        return GamePublisherProduct::set($this->input['id'], $data);
    }

    public function actionGetActiveAttributes()
    {
        $language = GameProductLanguage::find()->select('value')->distinct()->asArray()->all();
        $genre = GameProductGenre::find()->select('value')->distinct()->asArray()->all();
        $feature = GameProductFeature::find()->select('value')->distinct()->asArray()->all();
        $region = GameProductRegion::find()->select('value')->distinct()->asArray()->all();

        $all_value = GameProductAttributeTranslation::getProductAttributeWithTranslation();
        $mapped_value = ArrayHelper::map($all_value, 'language_id', 'value', 'game_product_attribute_id');
        $item = [];

        $data = [
            'language' => $language,
            'genre' => $genre,
            'feature' => $feature,
            'region' => $region,
        ];

        foreach ($data as $index => $value) {
            $active_value = ArrayHelper::getColumn($value, 'value');
            $item[$index] = [];
            foreach ($active_value as $val) {
                $item[$index] [$val] = [];
                foreach (Yii::$app->params['supported.language'] as $l_key => $l_val) {
                    $item[$index][$val][$l_key] = ArrayHelper::getValue($mapped_value[$val], $l_key,
                        $mapped_value[$val][1]);
                }
            }
        }

        return $item;
    }

    public function actionBatchUpdateAlgolia()
    {
        ini_set("memory_limit", "-1");
        $game_publisher_product = GamePublisherProduct::find()->select('game_product_id')->where(['!=', 'status', 0])->andWhere(['not', ['game_product_id' => null]])->asArray()->all();
        foreach (GameProduct::findAll(['game_product_id' => ArrayHelper::getColumn($game_publisher_product, 'game_product_id')]) as $game_product) {
            $game_product->updateAlgolia();
        }
        return $game_publisher_product;
    }

    public function actionPatchGameProductCategoryPath()
    {
        GameProduct::patchGameProductCategoryPath();
        return true;
    }

    public function actionPatchReleaseDate()
    {
        GameProduct::patchReleaseDate();
        return true;
    }

    public function actionPatchSellingPrice()
    {
        GameProduct::patchSellingPrice();
        return true;
    }
}