<?php

namespace micro\controllers;

use micro\components\GeneralCom;
use micro\models\DtoneOgMapping;
use micro\models\MobileRechargeDenoDescription;
use micro\models\MobileRechargeOperatorDescription;
use offgamers\publisher\models\profile\DTOne;
use offgamers\publisher\models\profile\DTOneDVS;
use offgamers\publisher\models\Publisher;
use Yii;
use micro\models\MobileRechargeRegion;
use micro\models\MobileRechargeOperator;
use micro\models\MobileRechargeDeno;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class MobileRechargeController extends \offgamers\base\controllers\RestController
{
    public function actionGetRegionList()
    {
        return MobileRechargeRegion::search($this->input, $this->input['page'], $this->input['pageSize']);
    }

    public function actionGetRegion()
    {
        return MobileRechargeRegion::get($this->input);
    }

    public function actionGetAllRegion()
    {
        return ArrayHelper::map(MobileRechargeRegion::find()->select(['region_id', 'name'])->asArray()->all(), 'region_id', 'name');
    }

    public function actionGetActiveRegion()
    {
        return MobileRechargeRegion::find()->select(['iso3', 'prefix', 'name', 'flag'])->where(['status' => 1])->asArray()->all();
    }

    public function actionUpdateRegion()
    {
        if ($this->input['id']) {
            $model = MobileRechargeRegion::findOne($this->input['id']);
        } else {
            $model = new MobileRechargeRegion();
        }

        $model->load($this->input, '');
        return $model->save();
    }

    public function actionBatchCreateRegion()
    {
        return MobileRechargeRegion::batchCreate($this->input['list']);
    }

    public function actionGetOperatorList()
    {
        return MobileRechargeOperator::search($this->input, $this->input['page'], $this->input['pageSize']);
    }

    public function actionGetAllOperator()
    {
        $operator_list = MobileRechargeOperatorDescription::find()->select(['operator_id', 'title'])->where(['language_id' => 1])->orderBy('title')->asArray()->all();

        return ArrayHelper::map($operator_list, 'operator_id', 'title');
    }

    public function actionGetOperator()
    {
        return MobileRechargeOperator::get($this->input);
    }

    public function actionGetDenominationDetail($id, $language_id = 1)
    {
        $model = MobileRechargeDeno::findOne($id);
        $model_description = MobileRechargeDenoDescription::find()->where(['deno_id' => $model->deno_id, 'language_id' => $language_id])->one();
        $default_model_description = MobileRechargeDenoDescription::find()->where(['deno_id' => $model->deno_id, 'language_id' => 1])->one();
        if (!$model_description) {
            $model_description = $default_model_description;
        }
        $operator_description = MobileRechargeOperatorDescription::find()->where(['operator_id' => $model->operator_id, 'language_id' => $language_id])->one();
        $default_operator_description = MobileRechargeOperatorDescription::find()->where(['operator_id' => $model->operator_id, 'language_id' => 1])->one();
        if (!$operator_description) {
            $operator_description = $default_operator_description;
        }
        $data = [
            'sales_price' => $model->sales_price,
            'sales_currency' => $model->sales_currency,
            'title' => GeneralCom::checkEmpty($operator_description->title, $default_operator_description->title) . ' - ' . GeneralCom::checkEmpty($model_description->title, $default_model_description->title)
        ];

        return $data;
    }


    public function actionUpdateOperator()
    {
        if ($this->input['id']) {
            $model = MobileRechargeOperator::findOne($this->input['id']);
        } else {
            $model = new MobileRechargeOperator();
        }

        $model->load($this->input, '');
        if ($model->save()) {
            $description_list = MobileRechargeOperatorDescription::findAll(['operator_id' => $model->operator_id]);
            $description_list = ArrayHelper::index($description_list, 'language_id');

            $removed_item = array_diff(array_keys($model->description), array_keys($description_list));
            if ($removed_item) {
                MobileRechargeOperatorDescription::deleteAll(['operator_id' => $model->operator_id, 'language_id' => $removed_item]);
            }

            foreach ($model->description as $language => $description) {
                if (isset($description_list[$language])) {
                    $description_model = $description_list[$language];
                } else {
                    $description_model = new MobileRechargeOperatorDescription;
                    $description_model->operator_id = $model->operator_id;
                    $description_model->language_id = $language;
                }
                $description_model->load($description, '');
                $description_model->save();
            }
        }

        return true;
    }

    public function actionBatchCreateOperator()
    {
        return MobileRechargeOperator::batchCreate($this->input['list']);
    }

    public function actionBatchCreateDvsOperator()
    {
        return MobileRechargeOperator::batchProcessOperatorList($this->input['list']);
    }

    public function actionBatchCreateDvsDeno()
    {
        return MobileRechargeDeno::batchProcessDvsList($this->input['publisher_id'], $this->input['currency'], $this->input['mark_up'], $this->input['list'], $this->input['products_id']);
    }

    public function actionBatchCreateDeno()
    {
        return MobileRechargeDeno::batchCreate($this->input['publisher_id'], $this->input['currency'], $this->input['mark_up'], $this->input['list'], $this->input['products_id']);
    }

    public function actionBatchCreateBundle()
    {
        return MobileRechargeDeno::batchCreateBundle($this->input['publisher_id'], $this->input['currency'], $this->input['mark_up'], $this->input['list'], $this->input['products_id']);
    }

    public function actionGetDenoList()
    {
        return MobileRechargeDeno::search($this->input, $this->input['page'], $this->input['pageSize']);
    }

    public function actionGetDeno()
    {
        return MobileRechargeDeno::get($this->input);
    }

    public function actionGetOperatorByPhone($prefix, $phone, $language_id = 1)
    {
        if ($publisher = Publisher::findOne(['profile' => 'DTOneDVS', 'status' => 1])) {
            $dtone = new DTOneDVS;
            $dtone->publisher = $publisher->publisher_id;
            $operator_id = $dtone->getOperatorIdByPhoneNumber($prefix . $phone);
        } else {
            $publisher = Publisher::findOne(['profile' => 'DTOne', 'status' => 1]);
            $dtone = new DTOne;
            $dtone->publisher = $publisher->publisher_id;
            $data = $dtone->getOperatorIdByPhoneNumber($prefix, $phone);
            $operator_id = (string)$data->operatorid;
        }
        $og_id = DtoneOgMapping::find()->select('og_id')->where(['dtone_id' => $operator_id, 'type' => 2])->asArray()->one();
        if ($og_id) {
            $id = $og_id['og_id'];
            $model = MobileRechargeOperator::findOne(['operator_id' => $id, 'status' => 1]);
            if ($model) {
                $operator_description = MobileRechargeOperatorDescription::find()->where(['operator_id' => $id, 'language_id' => $language_id])->one();
                $default_operator_description = MobileRechargeOperatorDescription::find()->where(['operator_id' => $id, 'language_id' => 1])->one();
                if (!$operator_description) {
                    $operator_description = $default_operator_description;
                }
                $data = [
                    'logo' => $model->logo_path,
                    'title' => GeneralCom::checkEmpty($operator_description->title, $default_operator_description->title),
                    'description' => GeneralCom::checkEmpty($operator_description->description, $default_operator_description->description),
                    'terms' => $operator_description->terms,
                    'deno' => $model->getDenominationList($publisher->publisher_id, $language_id, 1),
                    'bundle' => $model->getDenominationList($publisher->publisher_id, $language_id, 2),
                    'pin' => $model->getDenominationList($publisher->publisher_id, $language_id, 3)
                ];

                $data['status'] = (!empty($data['deno']) || !empty($data['bundle']) || !empty($data['pin']));
                return $data;
            }
        }
        return ['status' => false];
    }

    public function actionUpdateDeno()
    {
        if ($this->input['id']) {
            $model = MobileRechargeDeno::findOne($this->input['id']);
        } else {
            $model = new MobileRechargeDeno();
        }

        $model->load($this->input, '');

        if ($model->save()) {
            $description_list = MobileRechargeDenoDescription::findAll(['deno_id' => $model->deno_id]);
            $description_list = ArrayHelper::index($description_list, 'language_id');

            $removed_item = array_diff(array_keys($model->description), array_keys($description_list));
            if ($removed_item) {
                MobileRechargeDenoDescription::deleteAll(['deno_id' => $model->deno_id, 'language_id' => $removed_item]);
            }

            foreach ($model->description as $language => $description) {
                if (isset($description_list[$language])) {
                    $description_model = $description_list[$language];
                } else {
                    $description_model = new MobileRechargeDenoDescription;
                    $description_model->deno_id = $model->deno_id;
                    $description_model->language_id = $language;
                }
                $description_model->load($description, '');
                $description_model->save();
            }
        }
        return true;
    }

    public function actionBatchUpdateDeno()
    {
        MobileRechargeDeno::batchUpdate($this->input);
        return true;
    }

}