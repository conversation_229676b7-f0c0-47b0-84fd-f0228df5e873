<?php

namespace micro\controllers;

use micro\models\Categories;
use micro\models\ProductsToCategories;
use Yii;
use micro\components\OrdersReview;
use micro\components\ProductReview;
use yii\helpers\Json;

class OrderReviewController extends \offgamers\base\controllers\RestController
{
    public function actionGetAllOrderReview()
    {
        $result = new OrdersReview();
        return $result->getAllReviewRecords($this->input, $this->input['page'], $this->input['pageSize']);
    }

    public function actionView()
    {
        $result = new OrdersReview();

        return $result->get($this->input);
    }

    public function actionUpdate()
    {
        $result = new OrdersReview();

        return $result->update($this->input);
    }

    public function actionUpdateRemarks()
    {
        $result = new OrdersReview();

        return $result->get($this->input);
    }


    public function actionGetOrdersReviewByOid()
    {
        $OrdersReview = new OrdersReview();

        return $OrdersReview->getReviewByOPid($this->input['orders_products_id']);
    }
    public function actionGetOrdersReviewByOpid()
    {
        $OrdersReview = new OrdersReview();

        return $OrdersReview->getReviewByOPid($this->input['orders_products_id']);
    }

    public function actionGetReviewRemarksByRid()
    {
        $OrdersReview = new OrdersReview();

        return $OrdersReview->getReviewRemarksByRid($this->input['review_id']);
    }


    public function actionGetLatestOrdersReviewByOpid()
    {
        $OrdersReview = new OrdersReview();
        $review_records = $OrdersReview->getLatestReviewByOPid($this->input['orders_products_id']);
        if(!isset($review_records)){
            $review_records = array();
        }

        return $review_records;
    }

    public function actionSaveOrdersReview()
    {
        $OrdersReview = new OrdersReview();
        $params = $this->input['params'];

        return $OrdersReview->saveReviewRecords($params);
    }

    public function actionGetOrderReviewStatusByOid()
    {
        $OrdersReview = new OrdersReview();
        $orders_id = $this->input['orders_id'];

        return $OrdersReview->getReviewStatusByOid($orders_id);
    }

    public function actionGetOrderReviewEditFlagByOpid()
    {
        $OrdersReview = new OrdersReview();
        $orders_products_id = $this->input['orders_products_id'];

        return $OrdersReview->getEditFlagByOpid($orders_products_id);
    }

    public function actionSaveLog()
    {
        $OrdersReview = new OrdersReview();
        $review_id = $this->input['review_id'];
        $user = $this->input['user'];
        $activity = $this->input['activity'];

        return $OrdersReview->saveLog($review_id, $user, $activity);
    }

    public function actionGetReviewsByProductsId()
    {
        $OrdersReview = new OrdersReview();
        $products_id = $this->input['products_id'];
        $limit = $this->input['limit'];
        $page = $this->input['page'];
        $sort = $this->input['sort'];

        $categories_id = Categories::getSecondLayerCategoryIdByProductId($products_id);
        $categories_ids = Categories::getAllSubCategoryByCategoryId($categories_id);
        $cid_list = Categories::getParentCatIds($categories_id);

        if(ProductReview::isProductReviewable($products_id,'Products') && ProductReview::isProductReviewable($cid_list,'Category')) {
            return $OrdersReview->getReviewsByCid($categories_ids, $page, $limit, $sort);
        }else{
            return ['blacklist' => true];
        }
    }

    public function actionGetReviewsByCategoriesId()
    {
        $OrdersReview = new OrdersReview();
        $categories_id = $this->input['categories_id'];
        $limit = $this->input['limit'];
        $page = $this->input['page'];
        $sort = $this->input['sort'];

        $cid_list = Categories::getParentCatIds($categories_id);
        $categories_ids = Categories::getAllSubCategoryByCategoryId($categories_id);
        $categories_ids[] = $categories_id;

        if(ProductReview::isProductReviewable($cid_list,'Category')) {
            return $OrdersReview->getReviewsByCid($categories_ids, $page, $limit, $sort);
        }else{
            return ['blacklist' => true];
        }
    }

    public function actionIsProductReviewable()
    {
        $id = $this->input['id'];
        $type = $this->input['type'];

        return ProductReview::isProductReviewable($id,$type);
    }

}