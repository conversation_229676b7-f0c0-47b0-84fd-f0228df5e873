<?php

namespace micro\controllers;

use micro\models\Categories;
use micro\models\Category;
use micro\models\DtoneOgMapping;
use micro\models\MobileRechargeDeno;
use micro\models\Products;
use micro\models\ProductsCost;
use micro\models\ProductsToCategories;
use micro\models\PublishersConfiguration;
use micro\models\PublishersGames;
use micro\models\PublishersProducts;
use micro\models\TopUpInfo;

class ProductController extends \offgamers\base\controllers\RestController
{
    public function actionGetRestockProductInfo()
    {
        return Products::getRestockProductInfo($this->input);
    }

    public function actionVerifyingStockMovement()
    {
        $data = $this->input;
        $qty = $data['quantity'] * -1;
        Products::verifyingStockMovement($data['orders_id'], $data['products_id'], $qty);
        return true;
    }

    public function actionUpdateProductsQty()
    {
        $products_list = $this->input['products_list'];
        foreach ($products_list as $data) {
            $cpc_list = ($data['custom_products_code'] ?? []);
            Products::updateProductQty((string)$data['products_id'], $data['qty'], $cpc_list);
        }
        return true;
    }

    public function actionG2gStockMovement(){
        Products::disableG2GInventory($this->input['products_id'], $this->input['qty'], $this->input['custom_products_code']);
        return true;
    }

    public function actionGetProductUrl()
    {
        $products_list = $this->input['products_id'];
        $language_id = $this->input['language_id'];
        return Products::getProductsBrandUrl($products_list, $language_id);
    }

    public function actionGetMobileRechargeProductCost()
    {
        $data = $this->input;
        $return_arr = [];
        switch ($data['products_type']) {
            case 3:
                $deno_model = MobileRechargeDeno::find()->select([
                    'cost_price',
                    'cost_currency'
                ])->where([
                    'products_id' => $data['products_id'],
                    'deno_id' => $data['sub_products_id']
                ])->asArray()->one();
                if ($deno_model) {
                    $return_arr = [
                        'products_cost' => $deno_model['cost_price'],
                        'products_cost_currency' => $deno_model['cost_currency']
                    ];
                }
                break;
            default:
                $products_cost_model = ProductsCost::find()->select([
                    'products_cost',
                    'products_currency'
                ])->where(['products_id' => $data['products_id']])->asArray()->one();
                if ($products_cost_model) {
                    $return_arr = [
                        'products_cost' => $products_cost_model['products_cost'],
                        'products_cost_currency' => $products_cost_model['products_currency']
                    ];
                }
                break;
        }

        return $return_arr;
    }

    public function actionGetDtuProductInfo()
    {
        $data = $this->input;
        $return_arr = [];
        switch ($data['products_type']) {
            case 3:
                $deno_model = MobileRechargeDeno::find()->select([
                    'publisher_id',
                    'type',
                    'publisher_ref_id'
                ])->where([
                    'products_id' => $data['products_id'],
                    'deno_id' => $data['sub_products_id'],
                ])->asArray()->one();
                if ($deno_model) {
                    list($operator_id, $dtone_id) = explode("_", $deno_model['publisher_ref_id']);
                    $return_arr = [
                        'publisher_id' => $deno_model['publisher_id'],
                        'operator_id' => $operator_id,
                        'deno_id' => $dtone_id,
                        'bundle_type' => $deno_model['type']
                    ];
                }
                break;
            default:
                $publisher_products = PublishersProducts::find()->where(['products_id' => $data['products_id']]
                )->asArray()->one();
                if ($publisher_products) {
                    $publisher_games = PublishersGames::find()->select(['publishers_id'])->where(
                        ['publishers_games_id' => $publisher_products['publishers_games_id']]
                    )->asArray()->one();
                    if ($publisher_games) {
                        $oc_publishers = PublishersConfiguration::find()->select(['publishers_configuration_value']
                        )->where(['publishers_configuration_key' => 'OGM_MERCHANT_ID'])->andWhere(
                            ['publishers_id' => $publisher_games['publishers_id']]
                        )->asArray()->one();
                        $top_up_info = TopUpInfo::find()->select(['top_up_info_value'])->where(
                            ['top_up_info_key' => 'product_code']
                        )->andWhere(['products_id' => $data['products_id']])->asArray()->one();
                        $return_arr = [
                            'publisher_id' => $oc_publishers['publishers_configuration_value'],
                            'publisher_product_id' => $top_up_info['top_up_info_value'],
                        ];
                    }
                }
                break;
        }

        return $return_arr;
    }

    public function actionGetProductCheckoutPermission()
    {
        $allow_to_checkout = false;
        $products_id = $this->input['products_id'];
        $ip_country = $this->input['ip_country'];
        $country_code = $this->input['country_code'];

        $categories_id = ProductsToCategories::find()
            ->select('categories_id')
            ->where(['products_id' => $products_id])
            ->scalar();

        if ($categories_id) {
            $c_path = Categories::find()
                ->select('categories_parent_path')
                ->where(['categories_id' => $categories_id])
                ->scalar();
            if ($c_path) {
                $categories_list = array_reverse(explode('_', trim($c_path, '_')));
                $allow_to_checkout = Category::find()
                    ->select(['c.*', 'csb.*'])
                    ->alias('c')
                    ->where(['categories_id' => $categories_list, 'status' => 1])
                    ->leftJoin(
                        'category_hard_block chb',
                        'c.category_id = chb.category_id AND chb.country_code = :ip_country'
                    )
                    ->leftJoin(
                        'category_soft_block csb',
                        'c.category_id = csb.category_id AND csb.country_code = :country_code'
                    )
                    ->andWhere(['IS', 'chb.category_id', null])
                    ->andWhere(['IS', 'csb.category_id', null])
                    ->params([':ip_country' => $ip_country, ':country_code' => $country_code])
                    ->exists();
            }
        }
        return $allow_to_checkout;
    }
}