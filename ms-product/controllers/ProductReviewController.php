<?php

namespace micro\controllers;

use Yii;
use micro\components\ProductReview;

class ProductReviewController extends \offgamers\base\controllers\RestController
{
    public function actionGetAllBlacklistItems()
    {
        $result = new ProductReview();
        return $result->getAllBlacklistItems($this->input, $this->input['page'], $this->input['pageSize']);
    }

    public function actionDeleteByBlacklistId()
    {
        return ProductReview::deleteByBlacklistId($this->input);
    }

    public function actionGetItemList(){
        return ProductReview::getItemList($this->input);
    }

    public function actionAddBlacklist(){
        return ProductReview::addBlacklist($this->input);
    }

}