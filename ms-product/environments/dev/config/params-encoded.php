<?php

return [
    // Main offgamers db conection
    'db.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // slave db connection
    'db.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',
    'db.slave.username' => '',
    'db.slave.password' => '',

    // offgamers db connection for utf8mb4
    'db.og.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.og.username' => '',
    'db.og.password' => '',

    // log module
    'db.log.dsn' => 'mysql:host=localhost;dbname=offgamers_log',
    'db.log.username' => '',
    'db.log.password' => '',

    // slave db connection
    'db.log.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers_log',
    'db.log.slave.username' => '',
    'db.log.slave.password' => '',

    'slack.webhook.default' => '',
    'slack.webhook.debug' => '',
    'slack.webhook.bdt.info' =>  '',

    'algolia.application' => '',
    'algolia.admin.key' => '',

    'api.credential' => [
        'backend' => '123456'
    ],

    // AWS
    'aws.key' => '',
    'aws.secret' => '',
    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',
];
