<?php

return [
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.slave.username'],
                'password' => $params['db.slave.password'],
                'charset' => 'utf8',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.dsn'],
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.slave.username'],
                'password' => $params['db.slave.password'],
                'charset' => 'latin1',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.slave.dsn']],
            ],
        ],
        'db_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.og.dsn'],
            'username' => $params['db.og.username'],
            'password' => $params['db.og.password'],
            'charset' => 'utf8mb4',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_log' => [
            'class' => 'yii\db\Connection',
            'dsn' => $params['db.log.dsn'],
            'username' => $params['db.log.username'],
            'password' => $params['db.log.password'],
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => false,
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.log.slave.username'],
                'password' => $params['db.log.slave.password'],
                'charset' => 'utf8',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => $params['db.log.slave.dsn']],
            ],
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "",
            'servers' => [
                [
                    'host' => $params['cache.host'],
                    'port' => $params['cache.port'],
                    'weight' => 50,
                ],
            ],
        ],
        'frontend_cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "ogm/OffGamers/",
            'servers' => [
                [
                    'host' => $params['cache.host'],
                    'port' => $params['cache.port'],
                    'weight' => 50,
                ],
            ],
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => $params['aws.key'],
            'secret' => $params['aws.secret'],
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_ENCRYPT_LOG' => [
                    'acl' => 'private',
                    'prefix_path' => 'log',
                    'storage' => 'STANDARD',
                    'bucket_key' => '',
                    'sse_kms_key' => ''
                ],
                'BUCKET_IMAGE' => [
                    'acl' => '',
                    'prefix_path' => '',
                    'storage' => '',
                    'bucket_key' => '',
                ],
            ],
        ]
    ],
];
