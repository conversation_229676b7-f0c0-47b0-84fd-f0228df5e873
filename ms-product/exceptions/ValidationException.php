<?php

namespace micro\exceptions;

use Exception;

class ValidationException extends Exception
{
    public function __construct(array $errors, string $context)
    {
        $message = '';

        foreach ($errors as $property => $errorMessages) {
            foreach ($errorMessages as $errorMessage) {
                $message .= sprintf("[%s] - [%s] - $errorMessage" . PHP_EOL, $context, $property);
            }
        }
        parent::__construct($message);
    }
}
