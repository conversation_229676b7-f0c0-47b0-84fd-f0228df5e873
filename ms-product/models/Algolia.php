<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "algolia".
 *
 * @property int $algolia_id
 * @property string $object_id
 * @property string $hash
 */
class Algolia extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'algolia';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['object_id', 'hash'], 'required'],
            [['object_id', 'hash'], 'string', 'max' => 255],
            [['object_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'algolia_id' => 'Algolia ID',
            'object_id' => 'Object ID',
            'hash' => 'Hash',
        ];
    }
}
