<?php

namespace micro\models;

use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "best_selling".
 *
 * @property int $best_selling_country_id
 * @property string $country_code
 * @property string $info
 */
class BestSelling extends \yii\db\ActiveRecord
{
    private const CACHE_KEY_BESTSELLING = 'bestselling';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'best_selling';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['best_selling_id'], 'integer'],
            [['country_code', 'info'], 'string']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'best_selling_id' => 'Best Selling ID',
            'country_code' => 'Country Code',
            'info' => 'Info',
        ];
    }

    public static function getUrlList($country_code, $language_id, $ip_country)
    {
        return Yii::$app->cache->getOrSet(static::CACHE_KEY_BESTSELLING . "/$country_code/$ip_country/$language_id", function () use ($country_code, $language_id, $ip_country) {
            $result = [];
            $info = false;
            $best_selling = self::findOne(["country_code" => $country_code]);
            if ($best_selling && !empty($best_selling->info)) {
                $info = Json::decode($best_selling->info);
            }

            if ($info) {
                $brand_id_list = [];
                if (!empty($info['general'])) {
                    $brand_id_list = ArrayHelper::merge($brand_id_list, $info['general']);
                }
                if (!empty($info['voucher'])) {
                    $brand_id_list = ArrayHelper::merge($brand_id_list, $info['voucher']);
                }
                if (!empty($info['dtu'])) {
                    $brand_id_list = ArrayHelper::merge($brand_id_list, $info['dtu']);
                }
                if (!empty($info['physical_goods'])) {
                    $brand_id_list = ArrayHelper::merge($brand_id_list, $info['physical_goods']);
                }

                // Filter By region setting for hard block
                $hard_block_list = BrandHardBlock::getBrandIdList($ip_country);
                $brand_id_list = array_diff($brand_id_list, $hard_block_list);

                // Filter By region setting for soft block
                $soft_block_list = BrandSoftBlock::getBrandIdList($country_code);
                $brand_id_list = array_diff($brand_id_list, $soft_block_list);

                $brand_detail_list = self::getBrandWithLanguage($brand_id_list, $language_id);

                $result['general'] = self::getResult($info['general'], $brand_detail_list);
                $result['voucher'] = self::getResult($info['voucher'], $brand_detail_list);
                $result['dtu'] = self::getResult($info['dtu'], $brand_detail_list);
                $result['physical_goods'] = self::getResult($info['physical_goods'], $brand_detail_list);
            }

            return $result;
        });
    }

    private static function getBrandWithLanguage($brand_id_list, $language_id)
    {
        $brand_detail_list = self::getBrandDetailByIdList($brand_id_list);
        foreach($brand_detail_list as $key => $brand_detail) {
            $brand_detail_list[$key]['name'] = BrandBase::getBrandName($key, $language_id);
        }

        return $brand_detail_list;
    }

    private static function getBrandDetailByIdList($brand_id_list)
    {
        return ArrayHelper::index((new Query())
            ->select(["b.brand_id", "b.seo_url", 'b.image_url'])
            ->from('brand b')
            ->where(["IN", "b.brand_id", $brand_id_list])
            ->andWhere(['b.status' => 1])
            ->all(Yii::$app->db_og), 'brand_id');
    }

    private static function getResult($brand_id_list, $brand_detail_list)
    {
        $result = [];
        if (!empty($brand_id_list)) {
            foreach ($brand_id_list as $brandId) {
                if (isset($brand_detail_list[$brandId])) {
                    $result[] = $brand_detail_list[$brandId];
                }
            }
        }
        return $result;
    }

    private static function checkHardBlockList($brand_list, $hard_block_list)
    {
        if (!empty($brand_list)) {
            foreach($brand_list as $key => $brand) {
                if (in_array($brand['brand_id'], $hard_block_list)) {
                    unset($brand_list[$key]);
                }
            }
        }
        return $brand_list;
    }

}
