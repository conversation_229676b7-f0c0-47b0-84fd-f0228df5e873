<?php

namespace micro\models;

use Exception;
use micro\components\GeneralCom;
use micro\components\OrdersReview;
use Throwable;
use Yii;
use micro\exceptions\{MissingArgumentException, NotFoundException, ValidationException};
use offgamers\base\components\AWSS3;
use yii\db\{ActiveRecord, StaleObjectException};
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class Brand extends BrandBase
{
    private const S3_BRAND_IMAGE_FOLDER = 'brand';
    private const BRAND_DEPENDENCIES_TABLE_NO_UPDATE = [
        'brand_category',
    ];

    public const ACTION_CREATE = 'create';
    public const ACTION_UPDATE = 'update';
    private const VALID_ACTIONS = [
        self::ACTION_CREATE,
        self::ACTION_UPDATE
    ];

    public $brand = [];
    public $brandImage = [];
    public $brandDescriptions = [];
    public $brandMetadatas = [];
    public $brandGameInfos = [];
    public $brandGameInfosRelatedLinkDeleteBucket = [];
    public $brandCategories = [];
    public $brandCategoriesDeleteBucket = [];
    public $brandSoftBlocks = [];
    public $brandHardBlocks = [];
    public $gameRegions = [];
    public $gameLanguages = [];
    public $gamePlatforms = [];
    public $gameGenres = [];

    public function saveBrand(array $params): array
    {
        try {
            $this->constructRequest($params);

            $this->load($this->brand, '');

            if (!$this->validate()) {
                throw new ValidationException(
                    $this->getErrors(),
                    self::tableName()
                );
            }

            if ($this->save() && $this->brand_id) {
                $imageUrl = $this->saveAndGetUrlImageToS3(
                    $this->brandImage,
                    $this->brand_id
                );

                $this->image_url = $imageUrl;
                $this->save();

                $this->initSaveBrandDependencies();
                $this->invalidateBrandListing();
            }
        } catch (Exception $exception) {
            $this->sendToSlack($exception, "Error saving brand due to");
            return $this->makeResponse(400, $exception->getMessage());
        }

        return $this->makeResponse(201, "Brand has been successfully created.");
    }

    /**
     * @throws NotFoundException
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function updateBrand(array $params): array
    {
        try {
            $this->constructRequest($params, self::ACTION_UPDATE);

            $brandId = $this->brand['brand_id'];

            $brandModel = self::findOne($brandId);

            if (is_null($brandModel)) {
                throw new NotFoundException($brandId, 'brand');
            }

            $brandModel->load($this->brand, "");

            if (!$brandModel->validate()) {
                throw new ValidationException(
                    $brandModel->getErrors(),
                    $brandModel::tableName()
                );
            }

            if (
                empty($this->brandImage['temp_file_image'])
                && !empty($this->brandImage['image_base64'])
                && !empty($this->brandImage['image_extension'])
            ) {
                try {
                    $this->deleteS3Image($brandModel->image_url);
                } catch (\Exception $e) {
                }
                $imageUrl = $this->saveAndGetUrlImageToS3(
                    $this->brandImage,
                    $brandId
                );

                $brandModel->image_url = $imageUrl;
            }

            if ($brandModel->save()) {
                $this->initDeleteBrandDependencies();
                $this->initSaveOrUpdateBrandDependencies();

                $this->invalidateBrandListingDetails($this->brand['brand_id']);
                $this->invalidateBrandListing();
            }
        } catch (NotFoundException $exception) {
            $this->sendToSlack($exception, "Brand not found when updating");
            return $this->makeResponse(404, $exception->getMessage());
        } catch (Exception $exception) {
            $this->sendToSlack($exception, "Error updating brand due to");
            return $this->makeResponse(400, $exception->getMessage());
        }

        return $this->makeResponse(
            200,
            "Brand id : [$brandId] has been successfully updated."
        );
    }

    /**
     * @throws Throwable
     * @throws StaleObjectException
     * @throws NotFoundException
     */
    public function deleteBrand(array $input): array
    {
        if (!isset($input['brand_id'])) {
            return $this->makeBrandArgumentMissingBrandId();
        }

        $brandId = $input['brand_id'];

        try {
            $brandModel = self::findOne($brandId);

            if (is_null($brandModel)) {
                throw new NotFoundException($brandId, 'brand');
            }

            $imageS3Path = $brandModel->image_url;

            $response = $brandModel->delete();

            // remove brand from brand_category if associated by other brand
            BrandCategory::deleteAll(
                [
                    'sub_id' => $brandId,
                    'type' => BrandCategory::BRAND_STATUS,
                ]
            );

            if ($response) {
                $this->deleteS3Image($imageS3Path);
            }

            if (!$response) {
                return $this->makeResponse(
                    400,
                    "Brand [$brandId] failed to be deleted."
                );
            }

            $this->invalidateBrandListingDetails($brandId);
            $this->invalidateBrandListing();
        } catch (NotFoundException $exception) {
            $this->sendToSlack($exception, "Brand not found when deleting");
            return $this->makeResponse(
                404,
                $exception->getMessage()
            );
        } catch (Exception $exception) {
            $this->sendToSlack($exception, "Error deleting brand due to");
            return $this->makeResponse(400, $exception->getMessage());
        }

        return $this->makeResponse(
            204,
            "Brand [$brandId] has been successfully deleted."
        );
    }

    public function isSeoUrlExists(array $input): array
    {
        if (empty($input['seo_url'])) {
            return $this->makeResponse(
                400,
                "Missing seo_url."
            );
        }

        $seoUrl = $input['seo_url'];

        $isSeoUrlExists = self::find()->where(
            [
                'seo_url' => trim($seoUrl)
            ]
        )->exists();

        if ($isSeoUrlExists) {
            return $this->makeResponse(
                200,
                "$seoUrl exists."
            );
        }

        return $this->makeResponse(
            404,
            "$seoUrl does not exists"
        );
    }

    /**
     * @throws ValidationException
     */
    protected function saveBrandDependencies(
        string $brandDependencyStringModel,
        array $brandDependencies,
        int $brandId
    ): void {
        if (empty($brandDependencies)) {
            return;
        }

        foreach ($brandDependencies as $brandDependency) {
            $brandDependencyModel = $this->getBrandDependenciesModel(
                $brandDependencyStringModel
            );

            $brandDependency['brand_id'] = $brandId;

            $this->saveBrandDependency($brandDependencyModel, $brandDependency);
        }
    }

    /**
     * @throws ValidationException
     */
    protected function saveOrUpdateBrandDependencies(
        string $brandDependencyStringModel,
        array $brandDependencies
    ): void {
        if (empty($brandDependencies)) {
            return;
        }

        foreach ($brandDependencies as $brandDependency) {
            $brandDependencyModel = $this->getBrandDependenciesModel(
                $brandDependencyStringModel
            );

            $record = null;
            if (!empty($brandDependency['id'])) {
                $record = $brandDependencyModel::findOne($brandDependency['id']);
            }

            // No record found means New record, else update existing record
            if (is_null($record)) {
                $record = $brandDependencyModel;
            } else {
                // If record found for data from the
                // tables in BRAND_DEPENDENCIES_TABLE_NO_UPDATE,
                // skip the update operation as
                // no update process for these tables
                if (
                    in_array(
                        $record::tableName(),
                        self::BRAND_DEPENDENCIES_TABLE_NO_UPDATE
                    )
                ) {
                    continue;
                }
            }

            $this->saveBrandDependency($record, $brandDependency);
        }
    }

    /**
     * @throws StaleObjectException
     * @throws Throwable
     */
    protected function deleteBrandDependencies(
        string $brandDependencyStringModel,
        array $brandDependenciesDeleteBucket
    ): void {
        if (empty($brandDependenciesDeleteBucket)) {
            return;
        }

        foreach ($brandDependenciesDeleteBucket as $id) {
            $brandDependencyModel = $this->getBrandDependenciesModel(
                $brandDependencyStringModel
            );

            $model = $brandDependencyModel::findOne($id);

            if (is_null($model)) {
                continue;
            }

            $model->delete();
        }
    }

    protected function saveAndGetUrlImageToS3(
        array $brandImage,
        int $brandId
    ) {
        /**
         * @var AWSS3 $s3
         */
        $s3 = Yii::$app->aws->getS3('BUCKET_IMAGE');

        $filename = sprintf(
            "%s/%d_%d.%s",
            self::S3_BRAND_IMAGE_FOLDER,
            $brandId,
            time(),
            $brandImage['image_extension']
        );

        $arrayBase64 = explode(",", $brandImage['image_base64']);
        $imageData = base64_decode(end($arrayBase64));

        $s3->saveContent($filename, $imageData, AWSS3::S3_ACL_PUBLIC_READ);

        return $s3->getObjectUrl($filename);
    }

    protected function deleteS3Image(string $imageS3Path): void
    {
        /**
         * @var AWSS3 $s3
         */
        $s3 = Yii::$app->aws->getS3('BUCKET_IMAGE');

        $pathInfo = pathinfo($imageS3Path);

        if (!isset($pathInfo['basename'])) {
            return;
        }

        $absoluteImagePath = sprintf(
            "%s/%s",
            self::S3_BRAND_IMAGE_FOLDER,
            $pathInfo['basename']
        );

        $s3->deleteContent($absoluteImagePath);
    }

    /**
     * @throws ValidationException
     */
    private function initSaveBrandDependencies(): void
    {
        $this->saveBrandDependencies(
            self::BRAND_DESCRIPTION,
            $this->brandDescriptions,
            $this->brand_id
        );

        $this->saveBrandDependencies(
            self::BRAND_METADATA,
            $this->brandMetadatas,
            $this->brand_id
        );

        $this->saveBrandDependencies(
            self::BRAND_GAME_INFO,
            $this->brandGameInfos,
            $this->brand_id
        );

        $this->saveBrandDependencies(
            self::BRAND_CATEGORY,
            $this->brandCategories,
            $this->brand_id
        );

        $this->saveBrandDependencies(
            self::BRAND_SOFT_BLOCK,
            $this->brandSoftBlocks,
            $this->brand_id
        );

        $this->saveBrandDependencies(
            self::BRAND_HARD_BLOCK,
            $this->brandHardBlocks,
            $this->brand_id
        );
    }

    /**
     * @throws ValidationException
     */
    private function initSaveOrUpdateBrandDependencies(): void
    {
        $this->saveOrUpdateBrandDependencies(
            self::BRAND_DESCRIPTION,
            $this->brandDescriptions
        );

        $this->saveOrUpdateBrandDependencies(
            self::BRAND_METADATA,
            $this->brandMetadatas
        );

        $this->saveOrUpdateBrandDependencies(
            self::BRAND_GAME_INFO,
            $this->brandGameInfos
        );

        $this->saveOrRemoveBrandGameInfos(
            $this->gameGenres,
            $this->brand['brand_id'],
            'game_genre'
        );

        $this->saveOrRemoveBrandGameInfos(
            $this->gamePlatforms,
            $this->brand['brand_id'],
            'game_platform'
        );

        $this->saveOrRemoveBrandGameInfos(
            $this->gameRegions,
            $this->brand['brand_id'],
            'game_region'
        );

        $this->saveOrRemoveBrandGameInfos(
            $this->gameLanguages,
            $this->brand['brand_id'],
            'game_language'
        );

        $this->saveOrUpdateBrandDependencies(
            self::BRAND_CATEGORY,
            $this->brandCategories
        );

        $this->saveOrRemoveHardOrSoftBlocks(
            self::BRAND_SOFT_BLOCK,
            $this->brandSoftBlocks,
            $this->brand['brand_id']
        );

        $this->saveOrRemoveHardOrSoftBlocks(
            self::BRAND_HARD_BLOCK,
            $this->brandHardBlocks,
            $this->brand['brand_id']
        );
    }

    /**
     * @throws Throwable
     * @throws StaleObjectException
     */
    private function initDeleteBrandDependencies(): void
    {
        $this->deleteBrandDependencies(
            self::BRAND_CATEGORY,
            $this->brandCategoriesDeleteBucket
        );

        $this->deleteBrandDependencies(
            self::BRAND_GAME_INFO,
            $this->brandGameInfosRelatedLinkDeleteBucket
        );
    }

    /**
     * @throws ValidationException
     */
    private function saveBrandDependency(
        ActiveRecord $brandDependencyModel,
        array $brandDependency
    ): void {
        $brandDependencyModel->load($brandDependency, '');

        if (!$brandDependencyModel->validate()) {
            throw new ValidationException(
                $brandDependencyModel->getErrors(),
                $brandDependencyModel::tableName()
            );
        }

        $brandDependencyModel->save();
    }

    /**
     * @throws ValidationException
     */
    private function saveOrRemoveBrandGameInfos(
        array $gameInfoList,
        int $brandId,
        string $key
    ): void {
        $brandDependencyModel = $this->getBrandDependenciesModel(
            self::BRAND_GAME_INFO
        );

        if (empty($gameInfoList)) {
            $brandDependencyModel::deleteAll(
                [
                    'and',
                    ['brand_id' => $brandId, 'key' => $key]
                ]
            );
            return;
        }

        // get existing list
        $results = $brandDependencyModel::find()->where(
            [
                'brand_id' => $brandId
                ,
                'key' => $key
            ]
        )->asArray()->all();

        $existingGameInfos = array_map(
            function (array $result) {
                return $result['value'];
            },
            $results
        );

        $newGameInfos = array_map(
            function (array $result) {
                return $result['value'];
            },
            $gameInfoList
        );

        $gameInfosToBeDeleted = array_diff($existingGameInfos, $newGameInfos);
        if (!empty($gameInfosToBeDeleted)) {
            $brandDependencyModel::deleteAll(
                [
                    'and',
                    ['brand_id' => $brandId, 'key' => $key],
                    ['in', 'value', $gameInfosToBeDeleted]
                ]
            );
        }

        $gameInfosToBeAdded = array_diff($newGameInfos, $existingGameInfos);

        foreach ($gameInfosToBeAdded as $value) {
            $model = $this->getBrandDependenciesModel(self::BRAND_GAME_INFO);
            $this->saveBrandDependency(
                $model,
                [
                    'key' => $key,
                    'value' => $value,
                    'brand_id' => $brandId
                ]
            );
        }
    }

    /**
     * @throws ValidationException
     */
    private function saveOrRemoveHardOrSoftBlocks(
        string $brandDependencyModelString,
        array $hardOrSoftBlocksList,
        int $brandId
    ): void {
        $brandDependencyModel = $this->getBrandDependenciesModel(
            $brandDependencyModelString
        );

        if (empty($hardOrSoftBlocksList)) {
            $brandDependencyModel::deleteAll(
                [
                    'and',
                    ['brand_id' => $brandId]
                ]
            );
            return;
        }

        // get existing list
        $results = $brandDependencyModel::find()->where(
            [
                'brand_id' => $brandId
            ]
        )->asArray()->all();

        $existingCountryCodes = array_map(
            function (array $result) {
                return $result['country_code'];
            },
            $results
        );

        $newCountryCodes = array_map(
            function (array $result) {
                return $result['country_code'];
            },
            $hardOrSoftBlocksList
        );

        // check what needs to be deleted and delete it
        $countryCodesToBeDeleted = array_diff(
            $existingCountryCodes,
            $newCountryCodes
        );
        if (!empty($countryCodesToBeDeleted)) {
            $brandDependencyModel::deleteAll(
                [
                    'and',
                    ['brand_id' => $brandId],
                    ['in', 'country_code', $countryCodesToBeDeleted]
                ]
            );
        }

        // check what needs to be added and add it
        $countryCodesToBeAdded = array_unique(array_diff($newCountryCodes, $existingCountryCodes));
        foreach ($countryCodesToBeAdded as $countryCode) {
            $model = $this->getBrandDependenciesModel($brandDependencyModelString);
            $this->saveBrandDependency(
                $model,
                [
                    'country_code' => $countryCode,
                    'brand_id' => $brandId
                ]
            );
        }
    }

    /**
     * @throws MissingArgumentException
     */
    private function constructRequest(
        array $params,
        string $action = self::ACTION_CREATE
    ): void {
        if (
            !in_array(
                $action,
                self::VALID_ACTIONS
            )
        ) {
            throw new MissingArgumentException(
                "Invalid action given : [$action]"
            );
        }

        if ($action == self::ACTION_UPDATE && empty($params['brand_id'])) {
            throw new MissingArgumentException(
                "Missing brand id for update operation"
            );
        }

        if (
            ($action == self::ACTION_UPDATE
                && empty($params['temp_file_image']))
            || $action == self::ACTION_CREATE
        ) {
            if (empty($params['image_base64'])) {
                throw new MissingArgumentException('brand: image_base64');
            }

            if (empty($params['image_extension'])) {
                throw new MissingArgumentException('brand: image_extension');
            }

            $this->loadBrandImage($params);
        }

        if ($action == self::ACTION_UPDATE && !empty($params['temp_file_image'])) {
            $this->brandImage['temp_file_image'] = $params['temp_file_image'];
        }

        if (empty($params['seo_url'])) {
            throw new MissingArgumentException('brand: seo_url');
        }

        if (!isset($params['show_in_search_result'])) {
            throw new MissingArgumentException('brand: show_in_search_result');
        }

        if (!isset($params['status'])) {
            throw new MissingArgumentException('brand: status');
        }

        $this->loadBrand($params);
        $this->loadBrandDescriptions($params);
        $this->loadBrandMetadatas($params);
        $this->loadBrandGameInfos($params, $action);
        $this->loadBrandCategories($params);
        $this->loadBrandSoftBlocks($params);
        $this->loadBrandHardBlocks($params);
        $this->loadBrandCategoriesDeleteBucket($params);
        $this->loadBrandGameInfosRelatedLinksDeleteBucket($params);

        unset($params);
    }

    private function loadBrand(array $params): void
    {
        $this->brand['brand_id'] = $params['brand_id'] ?? null;
        $this->brand['seo_url'] = $params['seo_url'];
        $this->brand['show_in_search_result'] = $params['show_in_search_result'];
        $this->brand['status'] = $params['status'];
        $this->brand['search_keyword'] = $params['search_keyword'] ?? null;
        $this->brand['sort_order'] = $params['sort_order'] ?? 0;
    }

    private function loadBrandImage(array $params): void
    {
        $this->brandImage['image_base64'] = $params['image_base64'];
        $this->brandImage['image_extension'] = $params['image_extension'];
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandDescriptions(array $params): void
    {
        if (empty($params['brand_descriptions'])) {
            return;
        }

        foreach ($params['brand_descriptions'] as $brandDescription) {
            if (empty($brandDescription['language_id'])) {
                throw new MissingArgumentException(
                    'brand_description: language_id'
                );
            }

            if (
                $brandDescription['language_id'] == self::LANGUAGE_ID_ENGLISH
                && empty($brandDescription['name'])
            ) {
                throw new MissingArgumentException('brand: name for English');
            }

            if (!empty($brandDescription['brand_description_id'])) {
                $brandDescription['id'] = $brandDescription['brand_description_id'];
                unset($brandDescription['brand_description_id']);
            }

            $this->brandDescriptions[] = $brandDescription;
        }
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandMetadatas(array $params): void
    {
        if (empty($params['brand_metadatas'])) {
            return;
        }

        foreach ($params['brand_metadatas'] as $brandMetadata) {
            if (empty($brandMetadata['language_id'])) {
                throw new MissingArgumentException(
                    'brand_metadata: language_id'
                );
            }

            if (!empty($brandMetadata['brand_metadata_id'])) {
                $brandMetadata['id'] = $brandMetadata['brand_metadata_id'];
                unset($brandMetadata['brand_metadata_id']);
            }

            $this->brandMetadatas[] = $brandMetadata;
        }
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandGameInfos(array $params, string $action): void
    {
        if (empty($params['brand_game_infos'])) {
            return;
        }

        foreach ($params['brand_game_infos'] as $brandGameInfo) {
            if (empty($brandGameInfo['key'])) {
                throw new MissingArgumentException('brand_game_info: key');
            }

            if (!empty($brandGameInfo['brand_game_info_id'])) {
                $brandGameInfo['id'] = $brandGameInfo['brand_game_info_id'];
                unset($brandGameInfo['brand_game_info_id']);
            }

            if ($action == self::ACTION_UPDATE) {
                switch ($brandGameInfo['key']) {
                    case 'game_language':
                        $this->gameLanguages[] = $brandGameInfo;
                        break;
                    case 'game_region':
                        $this->gameRegions[] = $brandGameInfo;
                        break;
                    case 'game_platform':
                        $this->gamePlatforms[] = $brandGameInfo;
                        break;
                    case 'game_genre':
                        $this->gameGenres[] = $brandGameInfo;
                        break;
                }

                if (
                    $brandGameInfo['key'] == 'game_language'
                    || $brandGameInfo['key'] == 'game_region'
                    || $brandGameInfo['key'] == 'game_platform'
                    || $brandGameInfo['key'] == 'game_genre'
                ) {
                    continue;
                }
            }

            $this->brandGameInfos[] = $brandGameInfo;
        }
    }

    private function loadBrandGameInfosRelatedLinksDeleteBucket(array $params): void
    {
        if (empty($params['related_links_id_delete_bucket'])) {
            return;
        }

        $this->brandGameInfosRelatedLinkDeleteBucket = $params['related_links_id_delete_bucket'];
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandCategories(array $params): void
    {
        if (empty($params['brand_categories'])) {
            return;
        }

        foreach ($params['brand_categories'] as $brandCategory) {
            if (empty($brandCategory['sub_id'])) {
                throw new MissingArgumentException('brand_category: sub_id');
            }

            if (empty($brandCategory['type'])) {
                throw new MissingArgumentException('brand_category: type');
            }

            if (!empty($brandCategory['brand_category_id'])) {
                $brandCategory['id'] = $brandCategory['brand_category_id'];
                unset($brandCategory['brand_category_id']);
            }

            $this->brandCategories[] = $brandCategory;
        }
    }

    private function loadBrandCategoriesDeleteBucket(array $params): void
    {
        if (empty($params['brand_categories_id_delete_bucket'])) {
            return;
        }

        $this->brandCategoriesDeleteBucket = $params['brand_categories_id_delete_bucket'];
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandSoftBlocks(array $params): void
    {
        if (empty($params['brand_soft_blocks'])) {
            return;
        }

        foreach ($params['brand_soft_blocks'] as $brandSoftBlock) {
            if (empty($brandSoftBlock['country_code'])) {
                throw new MissingArgumentException(
                    'brand_soft_block: country_code'
                );
            }

            if (!empty($brandSoftBlock['brand_soft_block_id'])) {
                $brandSoftBlock['id'] = $brandSoftBlock['brand_soft_block_id'];
                unset($brandSoftBlock['brand_soft_block_id']);
            }

            $this->brandSoftBlocks[] = $brandSoftBlock;
        }
    }

    /**
     * @throws MissingArgumentException
     */
    private function loadBrandHardBlocks(array $params): void
    {
        if (empty($params['brand_hard_blocks'])) {
            return;
        }

        foreach ($params['brand_hard_blocks'] as $brandHardBlock) {
            if (empty($brandHardBlock['country_code'])) {
                throw new MissingArgumentException(
                    'brand_hard_block: country_code'
                );
            }

            if (!empty($brandHardBlock['brand_hard_block_id'])) {
                $brandHardBlock['id'] = $brandHardBlock['brand_hard_block_id'];
                unset($brandHardBlock['brand_hard_block_id']);
            }

            $this->brandHardBlocks[] = $brandHardBlock;
        }
    }

    public static function getBrandMetaData()
    {
        $column = [];
    }

    public static function getBrandInfo($url_alias, $language_id, $country_code, $ip_country)
    {
        $brand = Brand::find()->where(['seo_url' => $url_alias, 'status' => 1])->asArray()->limit(1)->one();
        if ($brand) {
            $brand_id = $brand['brand_id'];

            $soft_block = BrandSoftBlock::find()->where(
                ['brand_id' => $brand_id, 'country_code' => $country_code]
            )->asArray()->exists();

            $hard_block = BrandHardBlock::find()->where(
                ['brand_id' => $brand_id, 'country_code' => $ip_country]
            )->exists();

            $brand_description = BrandDescription::getBrandDescription($brand_id, $language_id);
            $brand_metadata = BrandMetadata::getMetaData($brand_id, $language_id, $brand_description['name'], $brand_description['description']);

            $brand_supported_item = BrandCategory::getSupportedItemByBrandId(
                $brand_id,
                $language_id,
                $country_code,
                $ip_country
            );

            $supported_category = [];

            if (count($brand_supported_item) == 1) {
                $supported_category = BrandCategory::getSupportedCategoryByBrandId(
                    $brand_id,
                    $brand_id,
                    $language_id,
                    $country_code,
                    $ip_country
                );
            }

            $brand_info = BrandGameInfo::getBrandInfoByBrandId($brand_id, $language_id);

            $supported_type = BrandType::find()
                ->select(['GROUP_CONCAT(type)'])
                ->where(['brand_id' => $brand_id])
                ->asArray()
                ->scalar();

            return [
                'title' => $brand_description['name'],
                'supported_type' => explode(',', $supported_type),
                'soft_block_country' => $soft_block,
                'hard_block_country' => $hard_block,
                'image_url' => $brand['image_url'],
                'url' => $brand['seo_url'],
                'short_description' => $brand_description['short_description'],
                'description' => $brand_description['description'],
                'notice' => $brand_description['notice'],
                'meta_title' => $brand_metadata['meta_title'],
                'meta_keyword' => $brand_metadata['meta_keyword'],
                'meta_description' => $brand_metadata['meta_description'],
                'supported_item' => $brand_supported_item,
                'supported_category' => $supported_category,
                'info' => $brand_info
            ];
        }

        return null;
    }

    public static function getSubBrandInfo($main_url_alias, $url_alias, $language_id, $country_code, $ip_country)
    {
        $brand = Brand::find()->where(['seo_url' => $url_alias, 'status' => 1])->asArray()->limit(1)->one();

        if ($brand) {
            $brand_id = $brand['brand_id'];

            $soft_block = ArrayHelper::getColumn(
                BrandSoftBlock::find()->where(
                    ['brand_id' => $brand_id]
                )->asArray()->all(),
                'country_code'
            );

            $brand_description = BrandDescription::getBrandDescription($brand_id, $language_id);
            $brand_metadata = BrandMetadata::getMetaData($brand_id, $language_id, $brand_description['name'], $brand_description['description']);

            $main_brand = Brand::find()->where(['seo_url' => $main_url_alias])->asArray()->limit(1)->one();

            $brand_supported_item = BrandCategory::getSupportedCategoryByBrandId(
                $main_brand['brand_id'],
                $brand_id,
                $language_id,
                $country_code,
                $ip_country
            );

            return [
                'title' => $brand_description['name'],
                'soft_block_country' => $soft_block,
                'url' => $brand['seo_url'],
                'description' => $brand_description['description'],
                'short_description' => $brand_description['short_description'],
                'notice' => $brand_description['notice'],
                'meta_title' => $brand_metadata['meta_title'],
                'meta_keyword' => $brand_metadata['meta_keyword'],
                'meta_description' => $brand_metadata['meta_description'],
                'supported_item' => $brand_supported_item,
            ];
        }
        return null;
    }

    public function afterSave($insert, $changedAttribute)
    {
        $key = 'brand/list';
        Yii::$app->cache->delete($key);
        parent::afterSave($insert, $changedAttribute);
    }

    public static function generateAlgoliaBrandType($type_list)
    {
        $return_arr = [];
        $type = ['voucher', 'dtu', 'game_key', 'mobile_recharge', 'physical'];
        foreach ($type_list as $type_id) {
            $return_arr[] = $type[$type_id - 1];
        }
        return $return_arr;
    }

    public static function getFullBrandList()
    {
        $all_list = [];

        $brand_list = self::find()
            ->where(['show_in_search_result' => 1, 'status' => 1])
            ->asArray()
            ->indexBy('brand_id')
            ->all();

        $id_list = ArrayHelper::getColumn($brand_list, 'brand_id');

        $brand_description = BrandDescription::find()
            ->where(['brand_id' => $id_list])
            ->asArray()
            ->all();

        $brand_description = ArrayHelper::index($brand_description, null, 'brand_id');

        $brand_block = ArrayHelper::index(
            BrandSoftBlock::find()
                ->where(['brand_id' => $id_list])
                ->asArray()
                ->all(),
            null,
            'brand_id'
        );

        $brand_hard_block = ArrayHelper::index(
            BrandHardBlock::find()
                ->where(['brand_id' => $id_list])
                ->asArray()
                ->all(),
            null,
            'brand_id'
        );

        $brand_type_list = ArrayHelper::map(
            BrandType::find()
                ->select(['brand_id', 'GROUP_CONCAT(type) as supported_type'])
                ->groupBy('brand_id')
                ->asArray()
                ->all(),
            'brand_id',
            function ($ele) {
                return explode(',', $ele['supported_type']);
            }
        );

        foreach ($brand_list as $brand) {
            $data = [
                'objectID' => 'b-' . $brand['brand_id'],
                'url_alias' => $brand['seo_url'],
                'type' => self::generateAlgoliaBrandType($brand_type_list[$brand['brand_id']] ?? []),
                'keyword' => $brand['search_keyword'],
                'image_url' => $brand['image_url'],
                'blocked_country' => ArrayHelper::getColumn($brand_block[$brand['brand_id']] ?? [], 'country_code'),
                'ip_blocked_country' => ArrayHelper::getColumn($brand_hard_block[$brand['brand_id']] ?? [], 'country_code'),
                'sort_order' => (int)(!empty($brand['sort_order']) ? $brand['sort_order'] : 9999)
            ];

            foreach ($brand_description[$brand['brand_id']] as $description) {
                $name = $description['name'];
                switch ($description['language_id']) {
                    case 1:
                        $data['name'] = $name;
                        break;

                    case 2:
                        $data['name_cn'] = $name;
                        break;

                    case 4:
                        $data['name_id'] = $name;
                        break;
                }
            }

            $all_list[] = $data;
        }

        $existing_list = ArrayHelper::index(Algolia::find()->where(['LIKE', 'object_id', 'b%', false])->all(), 'object_id');

        foreach ($all_list as $item) {
            $hash = md5(Json::encode($item));
            if (!isset($existing_list[$item['objectID']])) {
                $model = new Algolia();
                $model->object_id = $item['objectID'];
            } else {
                $model = $existing_list[$item['objectID']];
            }

            if ($model->hash != $hash) {
                $model->hash = $hash;
                Yii::$app->algolia->setBatchQuery('update', 'brand', $item);
                $model->save();
            }

            unset($existing_list[$item['objectID']]);
        }

        foreach ($existing_list as $id => $deleted_item) {
            Yii::$app->algolia->setBatchQuery('delete', 'brand', $id);
            $deleted_item->delete();
        }
    }

    public static function getSupportedSubCategory($parent_id)
    {
        return ArrayHelper::getColumn(
            Categories::find()
                ->select(['categories_id'])
                ->where(['parent_id' => $parent_id, 'categories_status' => 1])
                ->asArray()
                ->all()
            ,
            'categories_id'
        );
    }

    public static function getAllSupportedSubCategory($categories_id)
    {
        $return_arr = $category_list = self::getSupportedSubCategory($categories_id);
        foreach ($category_list as $sub_categories_id) {
            $return_arr = array_merge($return_arr, self::getSupportedSubCategory($sub_categories_id));
        }
        return $return_arr;
    }

    public static function getSupportedProductsByCategoriesId($categories_list)
    {
        $supported_type = [];

        $supported_products = ProductsToCategories::find()
            ->select(['p.products_id', 'p.products_type', 'pd.products_delivery_mode_id'])
            ->alias('pc')
            ->innerJoin('products p', 'pc.products_id = p.products_id AND p.products_status = 1')
            ->leftJoin('products_delivery_info pd', 'p.products_id = pd.products_id')
            ->where(['pc.categories_id' => $categories_list])
            ->asArray()
            ->all();

        foreach ($supported_products as $product) {
            switch ($product['products_type']) {
                case 2:
                case 3:
                case 4:
                    $type = (int)$product['products_type'] + 1;
                    break;
                default:
                    if ($product['products_delivery_mode_id'] == 6) {
                        $type = 2;
                    } else {
                        $type = 1;
                    }
                    break;
            }
            if (!in_array($type, $supported_type)) {
                $supported_type[] = $type;
            }
        }

        return $supported_type;
    }

    public static function getBrandReview($url_alias, $page, $limit)
    {
        $brand_id = Brand::find()->select('brand_id')->where(['seo_url' => $url_alias])->scalar();
        $category_list = self::getBrandSupportedCategory($brand_id);
        $supported_category = self::getAllSupportedSubCategory($category_list);
        $review_com = new OrdersReview();
        return $review_com->getReviewsByCid($supported_category, $page, $limit);
    }

    public static function getCategoryReview($url_alias, $page, $limit)
    {
        $category_list = Category::find()->select('categories_id')->where(['seo_url' => $url_alias])->scalar();
        $supported_category = self::getAllSupportedSubCategory($category_list);
        $review_com = new OrdersReview();
        return $review_com->getReviewsByCid($supported_category, $page, $limit);
    }

    public static function getProductsReview($url_alias, $page, $limit)
    {
        $products_id = Products::find()->select('products_id')->where(['products_url_alias' => $url_alias])->scalar();
        $review_com = new OrdersReview();
        return $review_com->getReviewsByPid($products_id, $page, $limit);
    }

    public static function getBrandSupportedType()
    {
        $insert_arr = [];

        $category_supported_item = [];

        $brand_list = Category::find()
            ->select(['brand_id'])
            ->asArray()
            ->distinct()
            ->all();

        foreach ($brand_list as &$brand) {
            $brand['supported_type'] = [];
            $brand['supported_category'] = self::getBrandSupportedCategory($brand['brand_id']);

            foreach ($brand['supported_category'] as $categories_id) {
                if (!isset($category_supported_item[$categories_id])) {
                    $supported_category = self::getAllSupportedSubCategory($categories_id);
                    $supported_type = self::getSupportedProductsByCategoriesId($supported_category);
                    $category_supported_item[$categories_id] = $supported_type;
                } else {
                    $supported_type = $category_supported_item[$categories_id];
                }
                $brand['supported_type'] = array_unique(array_merge($brand['supported_type'], $supported_type));
            }
        }

        $brand_type_list = ArrayHelper::map(
            BrandType::find()
                ->select(['brand_id', 'GROUP_CONCAT(type) as supported_type'])
                ->groupBy('brand_id')
                ->asArray()
                ->all(),
            'brand_id',
            function ($ele) {
                return explode(',', $ele['supported_type']);
            }
        );

        unset($brand);

        foreach ($brand_list as $brand) {
            $brand_id = $brand['brand_id'];
            $supported_type = $brand['supported_type'];
            $existing_item = ($brand_type_list[$brand_id] ?? []);

            $delete_list = array_diff($existing_item, $supported_type);
            $insert_list = array_diff($supported_type, $existing_item);

            foreach ($insert_list as $type) {
                $insert_arr[] = [$brand_id, $type];
            }

            foreach ($delete_list as $type) {
                BrandType::deleteAll(['brand_id' => $brand_id, 'type' => $type]);
            }
        }

        Yii::$app->db_og->createCommand()->batchInsert(
            BrandType::tableName(),
            ['brand_id', 'type'],
            $insert_arr
        )->execute();
    }

    public static function getBrandSupportedCategory($brand_id)
    {
        $return_arr = [];

        $supported_item = BrandCategory::find()
            ->where(['brand_id' => $brand_id])
            ->asArray()
            ->all();

        foreach ($supported_item as $item) {
            if ($item['type'] == 1) {
                $sub_brand_category = BrandCategory::find()
                    ->where(['brand_id' => $item['sub_id']])
                    ->andWhere(['type' => 2])
                    ->asArray()
                    ->all();

                foreach ($sub_brand_category as $sub_item) {
                    $return_arr[] = $sub_item['sub_id'];
                }
            } else {
                $return_arr[] = $item['sub_id'];
            }
        }

        return $return_arr;
    }
}
