<?php

namespace micro\models;

use Exception;
use micro\components\GeneralCom;
use Yii;
use yii\caching\TagDependency;
use yii\db\ActiveRecord;
use yii\caching\Cache;

/**
 * This is the model class for table "brand".
 *
 * @property int $brand_id
 * @property string $name
 * @property string $seo_url
 * @property string $image_url
 * @property int $show_in_search_result
 * @property string $search_keyword
 * @property int $status
 * @property int $sort_order
 */
class BrandBase extends ActiveRecord
{
    protected const BRAND_LISTING_TAG = 'brand-listing';
    protected const BRAND_DESCRIPTION = 'BrandDescription';
    protected const BRAND_METADATA = 'BrandMetadata';
    protected const BRAND_GAME_INFO = 'BrandGameInfo';
    protected const BRAND_CATEGORY = 'BrandCategory';
    protected const BRAND_SOFT_BLOCK = 'BrandSoftBlock';
    protected const BRAND_HARD_BLOCK = 'BrandHardBlock';
    protected const VALID_STATUSES = [0, 1];
    protected const LANGUAGE_ID_ENGLISH = 1;

    /**
     * @var Cache
     */
    protected $cache;

    protected const CACHE_KEY_BRAND_NAME = 'brand_name';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public function __construct($config = [])
    {
        $this->cache = Yii::$app->cache;
        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            ['seo_url', 'required'],
            [['seo_url', 'image_url'], 'trim'],
            [
                'seo_url',
                'unique',
                'targetAttribute' => ['seo_url'],
                'message' => 'This seo url exists'
            ],
            [['show_in_search_result', 'status'], 'integer'],
            ['sort_order', 'integer'],
            [['image_url'], 'string', 'max' => 1024],
            [['seo_url'], 'string', 'max' => 255],
            [['search_keyword'], 'string'],
            [['show_in_search_result', 'status'], 'validateStatus'],
        ];
    }

    public static function getBrandName($brand_id, $language_id)
    {
        return Yii::$app->cache->getOrSet(static::CACHE_KEY_BRAND_NAME . "/$brand_id/$language_id",
            function () use ($brand_id, $language_id) {
                $result = self::find()
                    ->alias('b')
                    ->select(['bd.name'])
                    ->where(['b.brand_id' => $brand_id, 'bd.language_id' => $language_id])
                    ->joinWith('brandDescriptions bd', false)
                    ->asArray()
                    ->one();

                // if name is empty, pass the english version
                if (empty($result['name']) && $language_id != 1) {
                    return self::getBrandName($brand_id, 1);
                }
                return $result['name'];
            });
    }

    public function validateStatus($attribute)
    {
        if (!in_array($this->$attribute, self::VALID_STATUSES)) {
            $this->addError($attribute, "Status must be either 0 or 1");
        }
    }

    public function getBrandDescriptions()
    {
        return $this->hasMany(BrandDescription::class, ['brand_id' => 'brand_id']);
    }

    protected function getBrandDependenciesModel(string $stringModel): ActiveRecord
    {
        switch ($stringModel) {
            case self::BRAND_DESCRIPTION:
                return new BrandDescription();
            case self::BRAND_METADATA:
                return new BrandMetadata();
            case self::BRAND_GAME_INFO:
                return new BrandGameInfo();
            case self::BRAND_CATEGORY:
                return new BrandCategory();
            case self::BRAND_SOFT_BLOCK:
                return new BrandSoftBlock();
            case self::BRAND_HARD_BLOCK:
                return new BrandHardBlock();
        }
    }

    protected function makeBrandArgumentMissingBrandId(): array
    {
        return $this->makeResponse(400, "Missing brand_id.");
    }

    protected function makeResponse(int $statusCode, string $message): array
    {
        return [
            "status" => $statusCode,
            "message" => $message
        ];
    }

    protected function sendToSlack(Exception $exception, string $message): void
    {
        GeneralCom::slackStackNotification(
            'debug',
            sprintf(
                "$message : %s - %s - %s",
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine()
            )
        );
    }

    protected function getBrandListingDetailsTag(int $brandId): string
    {
        return sprintf("brand-listing/brand/%d", $brandId);
    }

    protected function getLanguagesId(): array
    {
        $enum = Yii::$app->enum;
        return array_keys($enum->getLanguage('listData'));
    }

    protected function invalidateBrandListing(): void
    {
        TagDependency::invalidate(
            $this->cache,
            self::BRAND_LISTING_TAG
        );
    }

    protected function invalidateBrandListingDetails(int $brandId): void
    {
        TagDependency::invalidate(
            $this->cache,
            $this->getBrandListingDetailsTag($brandId)
        );
    }
}
