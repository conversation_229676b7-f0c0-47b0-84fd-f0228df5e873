<?php

namespace micro\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "brand_category".
 *
 * @property int $brand_category_id
 * @property int $brand_id
 * @property int $sub_id
 * @property int $type
 */
class BrandCategory extends ActiveRecord
{
    public const BRAND_STATUS = 1;
    private const VALID_TYPES = [
        1,
        2
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_category';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'type', 'sub_id'], 'required'],
            [['brand_id', 'sub_id'], 'integer'],
            ['type', 'integer'],
            ['type', 'validateType'],
            ['sub_id', 'validateBrand'],
        ];
    }

    public function validateType($attribute)
    {
        if (!in_array($this->$attribute, self::VALID_TYPES)) {
            $this->addError(
                $attribute,
                "Type must be either brand (1) or category (2)"
            );
        }
    }

    public function validateBrand($attribute)
    {
        if (
            ($this->type == self::BRAND_STATUS)
            && ($this->brand_id == $this->$attribute)
        ) {
            $this->addError(
                $attribute,
                "Unable to associate with the same brand"
            );
        }
    }

    public static function getSupportedItemByBrandId($brand_id, $language_id, $country_code, $ip_country)
    {
        $return_arr = [];

        $supported_brand = self::getSupportedBrandByBrand($brand_id, $country_code, $ip_country);

        $categories_id = self::find()
            ->alias('t')
            ->select(['c.brand_id'])
            ->innerJoin('category c', 't.sub_id = c.categories_id AND c.status = 1')
            ->leftJoin(
                'category_hard_block chb',
                'chb.category_id = c.category_id AND chb.country_code = :ip_country'
            )
            ->leftJoin(
                'brand_hard_block bhb',
                'bhb.brand_id = c.brand_id AND bhb.country_code = :ip_country'
            )
            ->where(['t.brand_id' => $brand_id, 'type' => 2])
            ->andWhere(['IS', 'bhb.brand_id', null])
            ->andWhere(['IS', 'chb.category_id', null])
            ->params([':ip_country' => $ip_country])
            ->distinct()
            ->asArray()
            ->all();

        $brand_list = array_merge($supported_brand, ArrayHelper::getColumn($categories_id, 'brand_id'));

        foreach ($brand_list as $brand_id) {
            $brand = Brand::findOne(['brand_id' => $brand_id, 'status' => 1]);
            if ($brand) {
                $return_arr[$brand->seo_url] = [
                    'type' => 'brand',
                    'soft_block' => BrandSoftBlock::find()->where(
                        ['brand_id' => $brand_id, 'country_code' => $country_code]
                    )->exists(),
                    'sort_order' => $brand->sort_order,
                    'title' => BrandDescription::getBrandName($brand_id, $language_id)['name']
                ];
            }
        }

        uasort($return_arr, function ($a, $b) {
            $retval = $a['soft_block'] <=> $b['soft_block'];
            if ($retval == 0) {
                $retval = $a['sort_order'] <=> $b['sort_order'];
            }
            if ($retval == 0) {
                $retval = $a['title'] <=> $b['title'];
            }
            return $retval;
        });

        return $return_arr;
    }

    public static function getSupportedBrandByBrand($brand_id, $country_code, $ip_country)
    {
        $list = self::find()
            ->alias('t')
            ->select(['b.brand_id'])
            ->innerJoin('brand b', 't.sub_id = b.brand_id AND b.status = 1')
            ->leftJoin(
                'brand_hard_block bhb',
                'bhb.brand_id = b.brand_id AND bhb.country_code = :ip_country'
            )
            ->where(['t.brand_id' => $brand_id, 't.type' => 1])
            ->andWhere(['IS', 'bhb.brand_id', null])
            ->params([':ip_country' => $ip_country])
            ->asArray()
            ->all();

        return ArrayHelper::getColumn($list, ['brand_id']);
    }

    public static function getSupportedCategoryByBrand($brand_id, $ip_country)
    {
        $list = self::find()
            ->alias('t')
            ->select(['c.category_id'])
            ->innerJoin('category c', 't.sub_id = c.categories_id AND c.status = 1')
            ->leftJoin(
                'category_hard_block chb',
                'chb.category_id = c.category_id AND chb.country_code = :ip_country'
            )
            ->where(['t.brand_id' => $brand_id, 'type' => 2])
            ->andWhere(['IS', 'chb.category_id', null])
            ->params([':ip_country' => $ip_country])
            ->asArray()
            ->all();

        return ArrayHelper::getColumn($list, ['category_id']);
    }

    public static function getSupportedCategoryByBrandId($brand_id, $sub_brand_id, $language_id, $country_code, $ip_country)
    {
        $return_arr = [];

        $supported_brand = self::find()
            ->select(['t.sub_id'])
            ->alias('t')
            ->leftJoin('brand b', 'b.brand_id = t.brand_id AND b.status = 1')
            ->leftJoin(
                'brand_hard_block bhb',
                'b.brand_id = bhb.brand_id AND bhb.country_code = :ip_country'
            )
            ->where(['t.brand_id' => $brand_id, 't.sub_id' => $sub_brand_id, 't.type' => 1])
            ->andWhere(['IS', 'bhb.brand_id', null])
            ->params([':ip_country' => $ip_country])
            ->asArray()
            ->scalar();

        if ($supported_brand) {
            $categories_id = self::getSupportedCategoryByBrand($supported_brand, $ip_country);
        } else {
            $categories_id = ArrayHelper::getColumn(
                self::find()
                    ->alias('t')
                    ->select(['c.category_id'])
                    ->innerJoin('category c', 't.sub_id = c.categories_id AND c.status = 1')
                    ->innerJoin('brand b', 'c.brand_id = b.brand_id AND b.status = 1')
                    ->leftJoin(
                        'category_hard_block chb',
                        'chb.category_id = c.category_id AND chb.country_code = :ip_country'
                    )
                    ->leftJoin(
                        'brand_hard_block bhb',
                        'bhb.brand_id = c.brand_id AND bhb.country_code = :ip_country'
                    )
                    ->where(['t.brand_id' => $brand_id, 'type' => 2])
                    ->andWhere(['IS', 'bhb.brand_id', null])
                    ->andWhere(['IS', 'chb.category_id', null])
                    ->andWhere(['c.brand_id' => $sub_brand_id])
                    ->params([':ip_country' => $ip_country])
                    ->distinct()
                    ->asArray()
                    ->all()
                ,
                'category_id'
            );
        }

        $category_list = array_unique($categories_id);

        foreach ($category_list as $category_id) {
            $category = Category::findOne(['category_id' => $category_id]);
            $return_arr[$category->seo_url] = [
                'type' => 'category',
                'soft_block' => CategorySoftBlock::find()->where(
                    ['category_id' => $category_id, 'country_code' => $country_code]
                )->exists(),
                'sort_order' => $category->sort_order,
                'title' => CategoryDescription::getCategoryName($category_id, $language_id)['name']
            ];
        }

        uasort($return_arr, function ($a, $b) {
            $retval = $a['soft_block'] <=> $b['soft_block'];
            if ($retval == 0) {
                $retval = $a['sort_order'] <=> $b['sort_order'];
            }
            if ($retval == 0) {
                $retval = $a['title'] <=> $b['title'];
            }
            return $retval;
        });

        return $return_arr;
    }

    public static function deleteSupportedItem($brand_id, $type, $sub_id)
    {
        self::deleteAll(['brand_id' => $brand_id, 'type' => $type, 'sub_id' => $sub_id]);
    }
}
