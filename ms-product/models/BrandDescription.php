<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "brand_description".
 *
 * @property int $brand_description_id
 * @property int $brand_id
 * @property int $language_id
 * @property string $name
 * @property string $notice
 * @property string $short_description
 * @property string $description
 */
class BrandDescription extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_description';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'language_id'], 'required'],
            [['name', 'notice', 'short_description', 'description'], 'trim'],
            ['brand_id', 'integer'],
            ['language_id', 'integer'],
            ['name', 'string', 'max' => 255],
        ];
    }

    public static function getBrandDescriptionByLanguageId($brand_id, $language_id, $column)
    {
        return self::find()
            ->select($column)
            ->where(['brand_id' => $brand_id, 'language_id' => $language_id])
            ->limit(1)
            ->asArray()
            ->one();
    }

    public static function getBrandName($brand_id, $language_id)
    {
        $column = ['name'];
        $data = self::getBrandDescriptionByLanguageId($brand_id, $language_id, $column);
        $default_data = self::getBrandDescriptionByLanguageId($brand_id, 1, $column);

        return GeneralCom::getColumnValueWithDefault($data, $default_data, $column);
    }

    public static function getBrandDescription($brand_id, $language_id)
    {
        $column = ['name', 'notice', 'short_description', 'description'];
        $data = self::getBrandDescriptionByLanguageId($brand_id, $language_id, $column);
        $default_data = self::getBrandDescriptionByLanguageId($brand_id, 1, $column);

        return GeneralCom::getColumnValueWithDefault($data, $default_data, $column);
    }
}
