<?php

namespace micro\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "brand_game_info".
 *
 * @property int $brand_game_info_id
 * @property int $brand_id
 * @property string $key
 * @property string $value
 */
class BrandGameInfo extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_game_info';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'key'], 'required'],
            [['key', 'value'], 'trim'],
            ['brand_id', 'integer'],
            ['key', 'string', 'max' => 255],
            ['value', 'string', 'max' => 1024],
        ];
    }

    public static function getGameGenreTitle($id, $language_id)
    {
        return GameGenreDescription::find()
            ->select(['game_genre_description'])
            ->where(['game_genre_id' => $id, 'language_id' => $language_id])
            ->limit(1)
            ->scalar();
    }

    public static function getGameLanguageTitle($id, $language_id)
    {
        return GameLanguageDescription::find()
            ->select(['game_language_description'])
            ->where(['game_language_id' => $id, 'language_id' => $language_id])
            ->limit(1)
            ->scalar();
    }

    public static function getGamePlatformTitle($id, $language_id)
    {
        return GamePlatformDescription::find()
            ->select(['game_platform_description'])
            ->where(['game_platform_id' => $id, 'language_id' => $language_id])
            ->limit(1)
            ->scalar();
    }

    public static function getGameRegionTitle($id, $language_id)
    {
        return GameRegionDescription::find()
            ->select(['game_region_description'])
            ->where(['game_region_id' => $id, 'language_id' => $language_id])
            ->limit(1)
            ->scalar();
    }

    public static function getBrandInfoByBrandId($brand_id, $language_id)
    {
        $list = self::find()
            ->select(['key', 'value'])
            ->where(['brand_id' => $brand_id])
            ->asArray()
            ->all();

        foreach ($list as &$info) {
            if (empty($info['key'])) {
                continue;
            }
            switch ($info['key']) {
                case 'game_region':
                    $info['value'] = self::getGameRegionTitle($info['value'], $language_id);
                    break;
                case 'game_language':
                    $info['value'] = self::getGameLanguageTitle($info['value'], $language_id);
                    break;
                case 'game_genre':
                    $info['value'] = self::getGameGenreTitle($info['value'], $language_id);
                    break;
                case 'game_platform':
                    $info['value'] = self::getGamePlatformTitle($info['value'], $language_id);
                    break;
            }
        }

        $list = ArrayHelper::index($list, null, 'key');

        return $list;
    }
}
