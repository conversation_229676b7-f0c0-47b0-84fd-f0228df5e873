<?php

namespace micro\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "brand_hard_block".
 *
 * @property int $brand_hard_block_id
 * @property int $brand_id
 * @property string $country_code
 */
class BrandHardBlock extends ActiveRecord
{
    private const CACHE_KEY_BRAND_HARD_BLOCK = 'brand/hard_block';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_hard_block';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'country_code'], 'required'],
            ['brand_id', 'integer'],
            ['country_code', 'string', 'max' => 2],
        ];
    }

    public static function getBrandIdList($country_code)
    {
        return Yii::$app->cache->getOrSet(static::CACHE_KEY_BRAND_HARD_BLOCK . "/$country_code", function () use ($country_code) {
            return ArrayHelper::getColumn(self::find()
                ->select(['brand_id'])
                ->where(["country_code" => $country_code])
                ->asArray()
                ->all(), 'brand_id');
        });
    }
}
