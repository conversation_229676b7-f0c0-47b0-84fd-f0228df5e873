<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "brand_metadata".
 *
 * @property int $brand_metadata_id
 * @property int $brand_id
 * @property int $language_id
 * @property string $meta_title
 * @property string $meta_keyword
 * @property string $meta_description
 */
class BrandMetadata extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_metadata';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'language_id'], 'required'],
            [['meta_title', 'meta_keyword', 'meta_description'], 'trim'],
            ['brand_id', 'integer'],
            ['language_id', 'integer'],
            ['meta_title', 'string', 'max' => 255],
        ];
    }

    public static function getBrandMetaDataByLanguageId($brand_id, $language_id, $column)
    {
        return self::find()
            ->select($column)
            ->where(['brand_id' => $brand_id, 'language_id' => $language_id])
            ->limit(1)
            ->asArray()
            ->one();
    }

    public static function getMetaData($brand_id, $language_id, $title, $description)
    {
        $language_code = Yii::$app->enum->getLanguage('getLanguageCode');
        $column = ['meta_title', 'meta_keyword', 'meta_description'];
        $data = self::getBrandMetaDataByLanguageId($brand_id, $language_id, $column);
        $default_data = self::getBrandMetaDataByLanguageId($brand_id, 1, $column);
        $return_arr = GeneralCom::getColumnValueWithDefault($data, $default_data, $column);

        if (empty($return_arr['meta_title'])) {
            $return_arr['meta_title'] = '%s - ' . Yii::t(
                    'seo',
                    'TEXT_DEFAULT_TITLE',
                    [],
                    $language_code[$language_id]
                );
        }

        if (empty($return_arr['meta_description'])){
            $return_arr['meta_description'] = GeneralCom::getFirstParagraph($description, '<br>');
        }

        $return_arr['meta_title'] = sprintf($return_arr['meta_title'], $title);

        return $return_arr;
    }
}
