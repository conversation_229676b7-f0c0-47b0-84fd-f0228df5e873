<?php

namespace micro\models;

use Exception;
use micro\exceptions\{InvalidArgumentException, MissingArgumentException, NotFoundException};
use Yii;
use yii\caching\TagDependency;
use yii\db\Query;
use yii\web\HttpException;

class BrandSearch extends BrandBase
{
    private const CACHE_EXPIRATION_DURATION = 86400;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['seo_url', 'image_url', 'show_in_search_result', 'status'], 'safe'],
        ];
    }

    public function search(array $params, int $page = 1, int $limit = 10): array
    {
        $name = $params['name_search'];
        $getAll = $params['all'];

        $this->load($params, '');

        $query = self::find();

        $query->alias('b')
            ->select(
                [
                    'b.brand_id',
                    'b.seo_url',
                    'b.image_url',
                    'b.show_in_search_result',
                    'b.search_keyword',
                    'b.status',
                    'b.sort_order',
                ]
            )
            ->joinWith('brandDescriptions bd', false);

        $query->where(['bd.language_id' => 1]);

        $query->andFilterWhere(['b.status' => $this->status]);

        $query->andFilterWhere(['like', 'bd.name', $name]);

        $query->orderBy('b.sort_order');

        if (!$getAll) {
            $limit = min($limit, 200);
            $query->limit($limit);
            $query->offset(($page - 1) * $query->limit);
        }

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $data[] = $this->getBrandAndItsDependencies($obj);
        }

        return [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];
    }

    public function getBrandList():array
    {
        return self::find()
            ->alias('b')
            ->select(['b.brand_id', 'bd.name'])
            ->joinWith('brandDescriptions bd', false)
            ->where(['bd.language_id' => 1])
            ->orderBy('b.sort_order, bd.name')
            ->asArray()->all();
    }

    /**
     * @throws HttpException
     */
    public function searchByBrandId(array $input): array
    {
        if (!isset($input['brand_id'])) {
            return $this->makeBrandArgumentMissingBrandId();
        }

        $brandId = $input['brand_id'];

        try {
            $brand = self::findOne($brandId);

            if (is_null($brand)) {
                throw new NotFoundException($brandId, 'brand');
            }

            return $this->getBrandAndItsDependencies($brand->toArray());
        } catch (NotFoundException $exception) {
            return $this->makeResponse(404, $exception->getMessage());
        } catch (Exception $exception) {
            throw new HttpException(500, $exception->getMessage());
        }
    }

    /**
     * @throws HttpException
     */
    public function getListing(array $params): array
    {
        try {
            $this->validateListingParams($params);

            $pageSize = intval($params['pageSize']);
            $page = intval($params['page']);
            $languageId = intval($params['language_id']);
            $country = $params['country'];
            $ipCountry = $params['ip_country'];

            $limit = min($pageSize, 200);
            $offset = ($page - 1) * $limit;

            $brandIdsInfo = $this->getListingBrandIdsInfo($country, $ipCountry, $page, $limit, $offset, $languageId);

            $brandIds = $brandIdsInfo['results'] ?? [];

            $data = [];
            if (!empty($brandIds)) {
                $data = $this->getListingsDetails($brandIds, $languageId);
            }

            $count = $brandIdsInfo['total_count'] ?? 0;

            return [
                'page' => $page,
                'count' => $count,
                'results' => $data
            ];
        } catch (MissingArgumentException | InvalidArgumentException $exception) {
            return $this->makeResponse(400, $exception->getMessage());
        } catch (Exception $exception) {
            $this->sendToSlack(
                $exception,
                sprintf(
                    "Error getting brand listing : %s params : [%s]",
                    $exception->getMessage(),
                    print_r($params, true)
                )
            );
            throw new HttpException(500, "Error getting best selling.");
        }
    }

    private function getBrandDependencies(
        string $brandDependencyStringModel,
        int $brandId
    ): array {
        $brandDependenciesModel = $this->getBrandDependenciesModel(
            $brandDependencyStringModel
        );

        $brandDependencies = $brandDependenciesModel::findAll(
            [
                'brand_id' => $brandId,
            ]
        );

        $data = [];
        foreach ($brandDependencies as $brandDependency) {
            $data[] = $brandDependency->toArray();
        }

        return $data;
    }

    private function getBrandAndItsDependencies(array $obj): array
    {
        $brand = [];

        $brand['brand_id'] = $obj['brand_id'];
        $brand['seo_url'] = $obj['seo_url'];
        $brand['image_url'] = $obj['image_url'];
        $brand['show_in_search_result'] = $obj['show_in_search_result'];
        $brand['search_keyword'] = $obj['search_keyword'] ?? null;
        $brand['status'] = $obj['status'];
        $brand['sort_order'] = $obj['sort_order'] ?? null;
        $brand['short_description'] = $obj['short_description'] ?? null;

        $brand['brand_descriptions'] = $this->getBrandDependencies(
            self::BRAND_DESCRIPTION,
            $brand['brand_id']
        );
        $brand['brand_metadatas'] = $this->getBrandDependencies(
            self::BRAND_METADATA,
            $brand['brand_id']
        );
        $brand['brand_game_infos'] = $this->getBrandDependencies(
            self::BRAND_GAME_INFO,
            $brand['brand_id']
        );
        $brand['brand_categories'] = $this->getBrandDependencies(
            self::BRAND_CATEGORY,
            $brand['brand_id']
        );
        $brand['brand_soft_blocks'] = $this->getBrandDependencies(
            self::BRAND_SOFT_BLOCK,
            $brand['brand_id']
        );
        $brand['brand_hard_blocks'] = $this->getBrandDependencies(
            self::BRAND_HARD_BLOCK,
            $brand['brand_id']
        );

        $languagesBd = array_column($brand['brand_descriptions'], 'language_id');
        $brandDescriptionsEnKey = array_search(1, $languagesBd);

        $brand['name_search'] = $brand['brand_descriptions'][$brandDescriptionsEnKey]['name'];

        return $brand;
    }

    /**
     * @throws MissingArgumentException|InvalidArgumentException
     */
    private function validateListingParams(array $params): void
    {
        if (empty($params['page'])) {
            throw new MissingArgumentException('page');
        }

        $page = intval($params['page']);

        if (empty($params['pageSize'])) {
            throw new MissingArgumentException('pageSize');
        }

        $pageSize = intval($params['pageSize']);

        if ($page < 1) {
            throw new InvalidArgumentException('page');
        }

        if ($pageSize < 1) {
            throw new InvalidArgumentException('pageSize');
        }

        if (empty($params['country'])) {
            throw new MissingArgumentException('country');
        }

        $countries = Countries::getCountriesIsoCodeTwoList();

        $country = $params['country'];
        if (!in_array($country, $countries)) {
            throw new InvalidArgumentException('country');
        }

        if (empty($params['ip_country'])) {
            throw new MissingArgumentException('ip_country');
        }

        $ipCountry = $params['ip_country'];
        if (!in_array($ipCountry, $countries)) {
            throw new InvalidArgumentException('ip_country');
        }

        if (empty($params['language_id'])) {
            throw new MissingArgumentException('language_id');
        }

        $languageId = intval($params['language_id']);
        if (!in_array($languageId, $this->getLanguagesId())) {
            throw new InvalidArgumentException('language_id');
        }
    }

    private function getListingBrandIdsInfo(
        string $country,
        string $ipCountry,
        int $page,
        int $limit,
        int $offset,
        int $languageId
    ): array {
        $key = sprintf(
            "brand-listing/brand-ids/%d-%d-%s-%s-%d",
            $page,
            $limit,
            $country,
            $ipCountry,
            $languageId
        );

        $dependency = new TagDependency(['tags' => self::BRAND_LISTING_TAG]);


        return $this->cache->getOrSet(
            $key,
            function () use ($country, $ipCountry, $limit, $offset) {
                $restrictedBrandIds = $this->getRestrictedBrandIds($country, $ipCountry);

                $query = (new Query())
                    ->select(['brand_id'])
                    ->from('brand');

                if ($restrictedBrandIds) {
                    $query->andWhere(['NOT IN', 'brand_id', $restrictedBrandIds]);
                }

                $query->orderBy(['brand_id' => SORT_ASC]);

                $results = $query->all(Yii::$app->db_og);

                $results = array_column($results, 'brand_id');

                $listingInfo['total_count'] = count($results);
                $listingInfo['results'] = array_slice($results, $offset, $limit);

                return $listingInfo;
            },
            self::CACHE_EXPIRATION_DURATION,
            $dependency
        );
    }

    private function getListingsDetails(
        array $brandIds,
        int $languageId
    ): array {
        $data = [];
        foreach ($brandIds as $brandId) {
            $brand = $this->getBrandListingDetailsByBrandIdAndLanguage($brandId, $languageId);

            if ($this->isBrandDescriptionNotExists($languageId, $brand)) {
                $brandEnglish = $this->getBrandListingDetailsByBrandIdAndLanguage(
                    $brandId,
                    self::LANGUAGE_ID_ENGLISH
                );

                if (is_array($brand)) {
                    $brand['name'] = $brandEnglish['name'];
                } else {
                    $brand = $brandEnglish;
                }
            }

            $data[] = $brand;
        }

        return $data;
    }

    private function getRestrictedBrandIds(string $country_code, string $ip_country_code): array
    {
        $softBlockBrandIds = (new Query())
            ->select("brand_id")
            ->from('brand_soft_block')
            ->where(['country_code' => $country_code]);

        $hardBlockBrandIds = (new Query())
            ->select('brand_id')
            ->from('brand_hard_block')
            ->where(['country_code' => $ip_country_code]);

        $results = $softBlockBrandIds->union($hardBlockBrandIds)->all(Yii::$app->db_og);

        return array_column($results, 'brand_id');
    }

    private function getBrandListingDetailsByBrandIdAndLanguage(int $brandId, int $languageId)
    {
        $key = sprintf("brand-listing/brand/%d/lang/%d", $brandId, $languageId);
        $tag = $this->getBrandListingDetailsTag($brandId);
        $dependency = new TagDependency(['tags' => $tag]);

        return $this->cache->getOrSet($key, function () use ($brandId, $languageId) {
            $query = (new Query())
                ->select([
                    'b.brand_id',
                    'bd.name',
                    'b.seo_url AS url',
                    'b.image_url',
                ])
                ->from('brand b')
                ->innerJoin(
                    'brand_description bd',
                    'b.brand_id = bd.brand_id AND bd.language_id = :language_id'
                )
                ->where(['b.brand_id' => $brandId]);

            $query->params([':language_id' => $languageId]);

            return $query->one(Yii::$app->db_og);
        }, self::CACHE_EXPIRATION_DURATION, $dependency);
    }

    private function isBrandDescriptionNotExists(int $languageId, $brand): bool
    {
        return ($languageId != self::LANGUAGE_ID_ENGLISH && (isset($brand['name']) && trim($brand['name']) == ''))
            || $brand === false;
    }
}
