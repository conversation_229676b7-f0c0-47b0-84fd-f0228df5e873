<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "brand_type".
 *
 * @property int $brand_type_id
 * @property int $brand_id
 * @property int $type 1=voucher,2=dtu,3=game_key,4=mobile_recharge,5=physical_goods
 */
class BrandType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'brand_type';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'type'], 'required'],
            [['brand_id', 'type'], 'integer'],
            [['brand_id', 'type'], 'unique', 'targetAttribute' => ['brand_id', 'type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'brand_type_id' => 'Brand Type ID',
            'brand_id' => 'Brand ID',
            'type' => 'Type',
        ];
    }
}
