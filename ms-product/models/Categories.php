<?php

namespace micro\models;

use offgamers\base\components\AWSS3;
use Yii;
use yii\helpers\ArrayHelper;

class Categories extends CategoriesBase
{
    public static function getCatPathByCatId($cid)
    {
        $category_str = [];

        $model = self::findOne($cid);
        $categories_path = explode('_', trim($model->categories_parent_path, '_ '));
        $categories_path[] = $model->categories_id;

        $parent_category = Categories::find()->alias('c')->select(array('c.categories_id', 'cd.categories_name'))->joinWith('categoriesDescriptions cd', false)->where(array('c.categories_id' => $categories_path))->asArray()->all();

        foreach ($parent_category as $cat) {
            $category_str[] = $cat['categories_name'];
        }

        return [$categories_path, implode(" > ", $category_str)];
    }

    public static function getCategoryPathByCategoryId($category_list)
    {
        $output = [];

        $path_list = self::find()
            ->select(['categories_id', 'categories_parent_path'])
            ->where(['categories_id' => $category_list])
            ->asArray()->all();

        foreach ($path_list as $item) {
            $output[] = $item['categories_parent_path'] . $item['categories_id'] . '_';
        }

        return $output;
    }

    public static function getAllSubCategoryByCategoryId($category_list)
    {
        $output = self::getThirdLayerCategory($category_list);
        if ($output) {
            $fourth_layer = self::getFourthLayerCategory($output);
            if ($fourth_layer) {
                $output = array_merge($output, $fourth_layer);
            }
        }
        return $output;
    }

    public static function getSecondLayerCategoryIdByProductId($product_id)
    {
        $active_category = CategoriesStructures::find()->select('categories_structures_value')->where(['categories_structures_key' => 'games'])->asArray()->one();
        $category_list = explode(",", $active_category['categories_structures_value']);

        $category = Products::find()->alias('p')
            ->select(array('c.categories_id', 'c.categories_parent_path'))
            ->joinWith('categories c', false)
            ->joinWith('productsFollowPrice pfp', false)
            ->where(array('p.products_status' => 1, 'pfp.products_id' => null, 'c.categories_status' => 1, 'p.products_id' => $product_id))
            ->andWhere(array('<>', 'p.custom_products_type_id', 3))
            ->asArray()->one();

        if ($category) {
            $list = explode("_", trim($category['categories_parent_path'], " _"));
            $list[] = $category['categories_id'];
            $result = array_intersect($category_list, $list);
            return array_shift($result);
        }
    }

    public static function getThirdLayerCategory($category_list)
    {
        $path_list = self::getCategoryPathByCategoryId($category_list);

        $data = self::find()
            ->select('categories_id')
            ->where(['custom_products_type_id' => 2])
            ->andWhere(['categories_status' => 1])
            ->andWhere(['REGEXP', 'categories_parent_path', implode('|', $path_list)])
            ->asArray()->all();

        return ArrayHelper::getColumn($data, 'categories_id');
    }

    public static function getFourthLayerCategory($category_list)
    {
        $path_list = self::getCategoryPathByCategoryId($category_list);

        $data = self::find()
            ->select('categories_id')
            ->where(['categories_status' => 1])
            ->andWhere(['REGEXP', 'categories_parent_path', implode('|', $path_list)])
            ->asArray()->all();

        return ArrayHelper::getColumn($data, 'categories_id');
    }

    public static function getCategoriesName($categories_id, $language_id){
        $result = self::find()
            ->alias('c')
            ->select(['c.categories_id', 'cd.categories_name'])
            ->where(['c.categories_id' => $categories_id, 'cd.language_id' => $language_id])
            ->joinWith('categoriesDescriptions cd', false)
            ->asArray()
            ->one();

        return $result['categories_name'];
    }

    public static function getCategoriesIds($categories_name, $language_id){
        $result = self::find()
            ->alias('c')
            ->select(['cd.categories_name', 'c.categories_id'])
            ->where(['cd.language_id' => $language_id])
            ->andWhere(['like','cd.categories_name',$categories_name])
            ->joinWith('categoriesDescriptions cd', false)
            ->asArray()
            ->all();
        $cat_ids = array();

        foreach($result as $row){
            $cat_ids[] = $row['categories_id'];
        }

        return $cat_ids;
    }

    public static function getMetaKeyword($category_id, $language_id = false, $default = false)
    {
        $output = SearchKeywords::getMetaKeyword($category_id, 0, $language_id, $default);
        return $output;
    }

    public static function getMetaDescription($category_id, $language_id = false, $default = false)
    {
        return FrontendTemplate::getTemplateDescription($category_id, 0, $language_id, $default);
    }

    public static function getParentCatIds($cid)
    {
        $model = self::findOne($cid);
        $categories_path = explode('_', trim($model->categories_parent_path, '_ '));
        $categories_path[] = $model->categories_id;
        return $categories_path;
    }

    public static function search($params, int $page = 1, int $limit = 10): array
    {
        $categoriesParentIdResult = self::find()->select(['parent_id'])
            ->where(['custom_products_type_id' => 2])
            ->andWhere(['categories_status' => 1])->asArray()->all();
        $categoriesParentArray = ArrayHelper::getColumn($categoriesParentIdResult, 'parent_id');

        $query = self::find();

        $query->alias('c')
            ->select([
                'c.categories_id AS id',
                'c.categories_status AS status',
                'cd.categories_name AS name'
            ])->joinWith('categoriesDescription cd', false);
        $query->where(['cd.language_id' => 1]);

        $query->andWhere(['in', ['c.categories_id'], $categoriesParentArray]);

        if (!empty($params['name'])) {
            $query->andWhere(['LIKE', 'cd.categories_name', $params['name']]);
        }
        if (isset($params['status']) && is_numeric($params['status'])) {
            $query->andWhere(['c.categories_status' => $params['status']]);
        }

        $query->orderBy('c.sort_order');

        $query->offset(($page - 1) * $limit);
        $query->limit($limit);

        $result = $query->asArray()->all();

        $data = [];
        foreach ($result as $obj) {
            $data[] = $obj;
        }

        return [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];
    }

    public function getCategoriesDescription()
    {
        return $this->hasMany(CategoriesDescription::class, ['categories_id' => 'categories_id']);
    }
}