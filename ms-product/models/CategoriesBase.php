<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "categories".
 *
 * @property int $categories_id
 * @property int $parent_id
 * @property string $categories_parent_path
 * @property int $sort_order
 * @property string $date_added
 * @property string $last_modified
 * @property int $categories_pinned
 * @property int $categories_status
 * @property int $c2c_categories_status
 * @property string $categories_url_alias
 * @property int $categories_auto_seo
 * @property string $categories_auto_seo_type
 * @property string $products_count
 * @property int $custom_products_type_id
 * @property int $custom_products_type_child_id
 * @property int $categories_types_groups_id
 * @property int $categories_buyback_main_cat
 */
class CategoriesBase extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'date_added',
                'updatedAtAttribute' => 'last_modified',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['parent_id', 'sort_order', 'categories_pinned', 'categories_status', 'c2c_categories_status', 'categories_auto_seo', 'products_count', 'custom_products_type_id', 'custom_products_type_child_id', 'categories_types_groups_id', 'categories_buyback_main_cat'], 'integer'],
            [['date_added', 'last_modified'], 'safe'],
            [['categories_parent_path', 'categories_url_alias'], 'string', 'max' => 255],
            [['categories_auto_seo_type'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories ID',
            'parent_id' => 'Parent ID',
            'categories_parent_path' => 'Categories Parent Path',
            'sort_order' => 'Sort Order',
            'date_added' => 'Date Added',
            'last_modified' => 'Last Modified',
            'categories_pinned' => 'Categories Pinned',
            'categories_status' => 'Categories Status',
            'c2c_categories_status' => 'C2c Categories Status',
            'categories_url_alias' => 'Categories Url Alias',
            'categories_auto_seo' => 'Categories Auto Seo',
            'categories_auto_seo_type' => 'Categories Auto Seo Type',
            'products_count' => 'Products Count',
            'custom_products_type_id' => 'Custom Products Type ID',
            'custom_products_type_child_id' => 'Custom Products Type Child ID',
            'categories_types_groups_id' => 'Categories Types Groups ID',
            'categories_buyback_main_cat' => 'Categories Buyback Main Cat',
        ];
    }

    public function getCategoriesDescriptions(){
        return $this->hasMany(CategoriesDescription::class,['categories_id' => 'categories_id']);
    }
}
