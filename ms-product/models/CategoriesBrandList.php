<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "categories_brand_list".
 *
 * @property int $categories_brand_list_id
 * @property int $categories_id
 * @property int $brand_id
 */
class CategoriesBrandList extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_brand_list';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_brand_list_id', 'categories_id', 'brand_id'], 'integer'],
            [['categories_id', 'brand_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_brand_list_id' => 'Category Brand List ID',
            'categories_id' => 'Category ID',
            'brand_id' => 'Brand ID',
        ];
    }
}
