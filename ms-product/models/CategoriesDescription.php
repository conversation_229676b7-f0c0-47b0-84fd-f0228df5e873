<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;

/**
 * This is the model class for table "categories_description".
 *
 * @property int $categories_id
 * @property int $language_id
 * @property string $categories_name
 * @property string $categories_short_name
 * @property string $categories_pin_yin
 * @property string $categories_heading_title
 * @property string $categories_description
 * @property string $categories_image
 * @property string $categories_image_title
 */
class CategoriesDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_id', 'language_id'], 'required'],
            [['categories_id', 'language_id'], 'integer'],
            [['categories_description'], 'string'],
            [['categories_name', 'categories_short_name', 'categories_pin_yin', 'categories_heading_title', 'categories_image', 'categories_image_title'], 'string', 'max' => 64],
            [['categories_id', 'language_id'], 'unique', 'targetAttribute' => ['categories_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'categories_id' => 'Categories ID',
            'language_id' => 'Language ID',
            'categories_name' => 'Categories Name',
            'categories_short_name' => 'Categories Short Name',
            'categories_pin_yin' => 'Categories Pin Yin',
            'categories_heading_title' => 'Categories Heading Title',
            'categories_description' => 'Categories Description',
            'categories_image' => 'Categories Image',
            'categories_image_title' => 'Categories Image Title',
        ];
    }

    public static function getCategoriesDescriptionByLanguageId($categories_id, $language_id, $column)
    {
        return self::find()
            ->select($column)
            ->where(['categories_id' => $categories_id, 'language_id' => $language_id])
            ->limit(1)
            ->asArray()
            ->one();
    }

    public static function getCategoriesName($categories_id, $language_id)
    {
        $column = ['categories_name'];
        $data = self::getCategoriesDescriptionByLanguageId($categories_id, $language_id, $column);
        if ($language_id == 1) {
            $default_data = $data;
        } else {
            $default_data = self::getCategoriesDescriptionByLanguageId($categories_id, 1, $column);
        }

        return GeneralCom::getColumnValueWithDefault($data, $default_data, $column);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCategories()
    {
        return $this->hasOne(Categories::className(), ['categories_id' => 'categories_id']);
    }
}
