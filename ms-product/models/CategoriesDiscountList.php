<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "categories_discount_list".
 *
 * @property int $cdl_id
 * @property int $cdrules_id
 * @property int $cdl_category_id
 * @property string|null $cdl_datetime
 */
class CategoriesDiscountList extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_discount_list';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['cdl_id', 'cdrules_id', 'cdl_category_id'], 'integer'],
            [['cdl_datetime'], 'safe']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'cdl_id' => 'Categories Discount List ID',
            'cdrules_id' => 'Categories Discount Rules ID',
            'cdl_category_id' => 'Customer Discount Rules Category ID'
        ];
    }
}
