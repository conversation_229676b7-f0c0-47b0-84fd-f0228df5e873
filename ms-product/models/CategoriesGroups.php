<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "categories_groups".
 *
 * @property int $linkid
 * @property int $categories_id
 * @property int $groups_id
 */
class CategoriesGroups extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_groups';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['categories_id', 'groups_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'linkid' => 'Linkid',
            'categories_id' => 'Categories ID',
            'groups_id' => 'Groups ID',
        ];
    }

    public static function saveCategoriesGroups($categories_id, $groups_id = 0)
    {
        $model = new self;
        $model->categories_id = $categories_id;
        $model->groups_id = 0;
        $model->save();
    }
}
