<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "categories_tag".
 *
 * @property int $tag_id
 * @property string $tag_key
 * @property int $tag_lft
 * @property int $tag_rgt
 * @property int $tag_status
 */
class CategoriesTag extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'categories_tag';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tag_key', 'tag_lft', 'tag_rgt'], 'required'],
            [['tag_lft', 'tag_rgt', 'tag_status'], 'integer'],
            [['tag_key'], 'string', 'max' => 125],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'tag_id' => 'Tag ID',
            'tag_key' => 'Tag Key',
            'tag_lft' => 'Tag Lft',
            'tag_rgt' => 'Tag Rgt',
            'tag_status' => 'Tag Status',
        ];
    }

    public function getCategoriesTagMap()
    {
        return $this->hasMany(CategoriesTagmap::class, ['tag_id' => 'tag_id']);
    }
}
