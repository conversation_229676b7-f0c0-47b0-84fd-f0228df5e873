<?php

namespace micro\models;

use micro\exceptions\NotFoundException;
use offgamers\base\components\AWSS3;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "category".
 *
 * @property int $category_id
 * @property int $categories_id
 * @property int $brand_id
 * @property string $seo_url
 * @property string $image_url
 * @property int $sort_order
 * @property int $status
 */
class Category extends \yii\db\ActiveRecord
{
    private const S3_CATEGORIES_IMAGE_FOLDER = 'categories';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id', 'categories_id', 'brand_id', 'sort_order', 'status'], 'integer'],
            [['seo_url', 'image_url'], 'string'],
            [['categories_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_id' => 'Category ID',
            'categories_id' => 'Categories ID',
            'brand_id' => 'Brand ID',
            'seo_url' => 'SEO URL',
            'image_url' => 'Image URL',
            'sort_order' => 'Sort Order',
            'status' => 'Status'
        ];
    }

    public static function search($params, int $page = 1, int $limit = 10): array
    {
        $query = self::find();

        $query->alias('c')
            ->select([
                'c.category_id AS id',
                'c.status',
                'cd.name'
            ])->joinWith('categoryDescription cd', false);
        $query->where(['cd.language_id' => 1]);

        if (!empty($params['name'])) {
            $query->andWhere(['LIKE', 'cd.name', $params['name']]);
        }
        if (isset($params['status']) && is_numeric($params['status'])) {
            $query->andWhere(['c.status' => $params['status']]);
        }

        $query->orderBy('c.sort_order');

        $query->offset(($page - 1) * $limit);
        $query->limit($limit);

        $result = $query->asArray()->all();

        $data = [];
        foreach ($result as $obj) {
            $data[] = $obj;
        }

        return [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];
    }

    public static function getAllCategory()
    {
        return ArrayHelper::map(
            Category::find()
                ->alias('c')
                ->select('c.categories_id, cd.name')
                ->innerJoin('categories_description cd', 'c.category_id = cd.category_id')
                ->asArray()
                ->all()
            ,
            'categories_id',
            'name'
        );
    }

    public static function saveCategory($input)
    {
        $transaction = Yii::$app->db_offgamers->beginTransaction();
        $og_transaction = Yii::$app->db_og->beginTransaction();
        $error = '';

        try {
            if (empty($input['category_id'])) {
                $categories_model = self::createCategoriesList($input);
            } else {
                $category = self::findOne(['category_id' => $input['category_id']]);
                if (!$category) {
                    throw new NotFoundException($input['category_id'], "Category");
                }
                $categories_model = self::updateCategoriesList($input, $category);
            }

            if (!$categories_model) {
                throw new NotFoundException($input['category_id'], "Category");
            }

            $category = self::saveCategoryModel($input, $categories_model->categories_id);
            self::saveBrandCategory($category->brand_id, $category->categories_id);
            self::saveCategoryDescription($input, $category->category_id);
            self::saveMetadataList($input, $category);
            self::saveCategoryDiscountList($input, $category);
            self::saveCategoryGroupPurchaseLimit($input, $category->category_id);
            self::saveCategoryCustomerGroupRestriction($input, $category->category_id);
            self::saveCategorySoftBlock($input, $category->category_id);
            self::saveCategoryHardBlock($input, $category->category_id);
            self::saveCategoryPaymentType($input, $category->category_id);
            self::saveCategoryPaymentRestriction($input, $category->category_id);

            $transaction->commit();
            $og_transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $og_transaction->rollBack();
            $error = $e->getMessage();
        }

        if ($error) {
            return self::makeResponse(false, $error);
        }

        return self::makeResponse(true, "Success");
    }

    public static function actionCheckDuplicateSeoURL($input)
    {
        $category_id = $input['category_id'] ?? 0;
        $result = self::find()
            ->select(['category_id'])
            ->where(['seo_url' => $input['seo_url']])
            ->one();
        return $result && $result->category_id != $category_id;
    }

    public function getCategoryDescription()
    {
        return $this->hasMany(CategoryDescription::class, ['category_id' => 'category_id']);
    }

    public static function get($id)
    {
        $category = self::find()
            ->where(['category_id' => $id])
            ->asArray()->one();

        if (!empty($category)) {
            $category['category_description'] = self::getCategoryDescriptionList($category['category_id']);
            $category['category_metadata'] = self::getCategoryMetadata($category['category_id']);
            $category['category_discount_list'] = self::getCategoryDiscountList($category['category_id']);
            $category['category_group_purchase_limit'] = self::getCategoryGroupPurchaseLimit($category['category_id']);
            $category['category_customer_group_restriction'] = self::getCategoryCustomerGroupRestriction(
                $category['category_id']
            );
            $category['category_hard_block'] = self::getCategoryHardBlock($category['category_id']);
            $category['category_soft_block'] = self::getCategorySoftBlock($category['category_id']);
            $category['category_payment_type'] = self::getCategoryPaymentType($category['category_id']);
            $category['category_payment_restriction'] = self::getCategoryPaymentRestriction($category['category_id']);
        }

        return $category;
    }

    public static function deleteCategory($category_id)
    {
        $category = self::findOne(['category_id' => $category_id]);
        if ($category) {
            self::deleteSEO($category->categories_id);
            self::deleteCategoriesAndDescription($category->categories_id);
            self::deleteAll(['category_id' => $category->category_id]);
        }
        return true;
    }

    private static function saveAndGetCategoriesLayer1($brand_id)
    {
        $current_brand_list = CategoriesBrandList::findOne(['brand_id' => $brand_id]);

        if ($current_brand_list) {
            // use existing
            return CategoriesBase::findOne(['categories_id' => $current_brand_list->categories_id]);
        }

        // create new
        $brand = BrandBase::findOne(['brand_id' => $brand_id]);
        if (!$brand) {
            return null;
        }

        $categories_layer_1 = new CategoriesBase();
        $categories_layer_1->categories_status = 1;
        $categories_layer_1->parent_id = 0;
        $categories_layer_1->sort_order = $brand->sort_order ?? 5000;
        $categories_layer_1->save();

        CategoriesGroups::saveCategoriesGroups($categories_layer_1->categories_id);

        $brand_description_list = BrandDescription::find()
            ->select(['language_id', 'name', 'description'])
            ->where(['brand_id' => $brand->brand_id ?? 0])
            ->asArray()
            ->all();

        foreach ($brand_description_list as $brand_description_row) {
            $categories_input = [
                "name" => $brand_description_row['name'],
                "description" => $brand_description_row['description'],
            ];
            self::saveCategoriesDescription(
                $categories_input,
                $categories_layer_1->categories_id,
                $brand_description_row['language_id']
            );
        }

        $current_brand_list = new CategoriesBrandList();
        $current_brand_list->categories_id = $categories_layer_1->categories_id;
        $current_brand_list->brand_id = $brand_id;
        $current_brand_list->save();

        $brand_model = Brand::findOne($brand_id);

        if ($brand_model->sort_order === 500000) {
            $brand_model->sort_order = 50000;
            $brand_model->save();
        }

        return $categories_layer_1;
    }

    private static function createCategoriesList($input)
    {
        // Save Layer 1
        $categories_layer_1 = self::saveAndGetCategoriesLayer1($input['brand_id']);

        if (!$categories_layer_1) {
            return null;
        }

        // Save Layer 2
        $categories_layer_2 = new CategoriesBase();
        $categories_layer_2->parent_id = $categories_layer_1->categories_id;
        $categories_layer_2->categories_parent_path = "_" . $categories_layer_1->categories_id . "_";
        $categories_layer_2->categories_status = $input['status'];
        $categories_layer_2->sort_order = $input['sort_order'];
        $categories_layer_2->save();

        CategoriesGroups::saveCategoriesGroups($categories_layer_2->categories_id);

        // Save Layer 3
        $categories_layer_3 = new CategoriesBase();
        $categories_layer_3->custom_products_type_id = 2;
        $categories_layer_3->custom_products_type_child_id = 3;
        $categories_layer_3->parent_id = $categories_layer_2->categories_id;
        $categories_layer_3->categories_parent_path = $categories_layer_2->categories_parent_path . $categories_layer_2->categories_id . "_";
        $categories_layer_3->sort_order = $input['sort_order'];
        $categories_layer_3->categories_status = 1;
        $categories_layer_3->save();

        CategoriesGroups::saveCategoriesGroups($categories_layer_3->categories_id);

        if (isset($input['category_description'])) {
            $categories_description_list = $input['category_description'];
            foreach ($categories_description_list as $categories_description) {
                self::saveCategoriesDescription(
                    $categories_description,
                    $categories_layer_2->categories_id
                    ,
                    $categories_description['language_id']
                );

                self::saveCategoriesDescription(
                    $categories_description,
                    $categories_layer_3->categories_id
                    ,
                    $categories_description['language_id']
                );
            }
        }

        return $categories_layer_2;
    }

    private static function updateCategoriesList($input, Category $category)
    {
        $categories_layer_1 = self::saveAndGetCategoriesLayer1($input['brand_id']);

        if (!$categories_layer_1) {
            return null;
        }

        $categories_layer_2 = CategoriesBase::find()
            ->where(['categories_id' => $input['categories_id']])
            ->one();
        if ($categories_layer_2) {
            $primary_brand_changed = false;

            if ($categories_layer_2->parent_id != $categories_layer_1->categories_id) {
                $primary_brand_changed = true;
            }

            $categories_layer_2->parent_id = $categories_layer_1->categories_id;

            $categories_layer_2->categories_parent_path = "_" . $categories_layer_1->categories_id . "_";
            $categories_layer_2->categories_status = $input['status'];
            $categories_layer_2->sort_order = $input['sort_order'];
            $categories_layer_2->save();

            $categories_layer_3 = CategoriesBase::find()
                ->where(['parent_id' => $categories_layer_2->categories_id])
                ->one();
            $categories_layer_3->categories_parent_path = $categories_layer_2->categories_parent_path . $categories_layer_2->categories_id . "_";
            $categories_layer_3->sort_order = $input['sort_order'];
            $categories_layer_3->save();

            if ($primary_brand_changed) {
                self::updateCategoriesSubProductsPath($categories_layer_3->categories_id, $categories_layer_3->categories_parent_path);
                BrandCategory::deleteSupportedItem($category->brand_id, 2, $categories_layer_2->categories_id);
            }

            $categories_layer_4_list = CategoriesBase::find()
                ->where(['parent_id' => $categories_layer_3->categories_id])
                ->all();

            foreach ($categories_layer_4_list as $categories_layer_4) {
                $categories_layer_4->categories_parent_path = $categories_layer_3->categories_parent_path . $categories_layer_3->categories_id . "_";
                $categories_layer_4->sort_order = $input['sort_order'];
                $categories_layer_4->save();
                if ($primary_brand_changed) {
                    self::updateCategoriesSubProductsPath($categories_layer_4->categories_id, $categories_layer_4->categories_parent_path);
                }
            }


            if (isset($input['categories_description'])) {
                $categories_id_layer_2 = $categories_layer_2->categories_id ?? 0;
                $categories_id_layer_3 = $categories_layer_3->categories_id ?? 0;

                $categories_description_list = $input['categories_description'];
                foreach ($categories_description_list as $categories_description) {
                    self::saveCategoriesDescription(
                        $categories_description,
                        $categories_id_layer_2
                        ,
                        $categories_description['language_id']
                    );

                    self::saveCategoriesDescription(
                        $categories_description,
                        $categories_id_layer_3
                        ,
                        $categories_description['language_id']
                    );
                }
            }
        }
        return $categories_layer_2;
    }

    private static function updateCategoriesSubProductsPath($categories_id, $categories_parent_path)
    {
        $ptc_list = ArrayHelper::getColumn(
            ProductsToCategories::find()
                ->select(['products_id'])
                ->where(['categories_id' => $categories_id])
                ->asArray()
                ->all()
            ,
            'products_id'
        );

        $name_list = [];

        $categories_parent_path = $categories_parent_path . $categories_id . '_';

        $category_list = explode('_', trim($categories_parent_path, '_'));

        foreach ($category_list as $cat_id) {
            $name = CategoriesDescription::getCategoriesName($cat_id, 1)['categories_name'];
            $name_list[] = $name;
        }

        Products::updateAll(['products_main_cat_id' => (int)$category_list[0], 'products_cat_id_path' => $categories_parent_path, 'products_cat_path' => implode(' > ', $name_list)], ['products_id' => $ptc_list]);
    }

    private static function saveBrandCategory($brand_id, $categories_id)
    {
        if (!BrandCategory::find()->where(['brand_id' => $brand_id, 'sub_id' => $categories_id, 'type' => 2])->exists()) {
            $model = (new BrandCategory);
            $model->setAttributes(['brand_id' => $brand_id, 'sub_id' => $categories_id, 'type' => 2]);
            $model->save();
        }
    }

    private static function saveCategoriesDescription($description_input, $categories_id, $language_id)
    {
        $categories_description = null;
        if ($categories_id && $language_id) {
            $categories_description = CategoriesDescription::find()
                ->where(['categories_id' => $categories_id])
                ->andWhere(['language_id' => $language_id])
                ->one();
        }

        if (empty($categories_description)) {
            $categories_description = new CategoriesDescription();
            $categories_description->language_id = $language_id;
            $categories_description->categories_id = $categories_id;
        }
        $categories_description->categories_name = $description_input['name'];
        $categories_description->categories_description = $description_input['description'];
        $categories_description->save();
    }

    private static function saveCategoryModel($input, $categories_id)
    {
        $image_url = null;

        if ($input['image_base64'] && $input['image_extension']) {
            $image = [
                'image_base64' => $input['image_base64'],
                'image_extension' => $input['image_extension']
            ];
            $image_url = self::saveS3Image($image, $categories_id);
        }

        $category = self::find()
            ->where(['categories_id' => $categories_id])
            ->one();
        if (empty($category)) {
            $category = new Category();
            $category->categories_id = $categories_id;
        }
        $category->brand_id = $input['brand_id'];
        $category->sort_order = $input['sort_order'];
        $category->seo_url = $input['seo_url'];
        if (!empty($image_url)) {
            if (!empty($category->image_url)) {
                self::deleteS3Image($category->image_url);
            }
            $category->image_url = $image_url;
        }
        $category->status = $input['status'];
        $category->save();

        return $category;
    }

    private static function saveCategoryDescription($input, $categories_id)
    {
        if (isset($input['category_description'])) {
            $category_description_list = $input['category_description'];

            foreach ($category_description_list as $category_description) {
                $description = CategoryDescription::find()
                    ->where(['category_id' => $categories_id])
                    ->andWhere(['language_id' => $category_description['language_id']])
                    ->one();

                if (empty($description)) {
                    $description = new CategoryDescription();
                    $description->category_id = $categories_id;
                    $description->language_id = $category_description['language_id'];
                }
                $description->name = $category_description['name'];
                $description->notice = $category_description['notice'];
                $description->short_description = $category_description['short_description'];
                $description->description = $category_description['description'];
                $description->save();
            }
        }
    }

    private static function saveMetadataList($input, $category)
    {
        if (isset($input['category_metadata']) && !empty($input['category_metadata'])) {
            // offgamers DB
            $custom_seo_model = CustomSeoBase::find()
                ->where(['reference_data_id' => $category->categories_id])
                ->andWhere(['type' => 0])
                ->one();
            if (empty($custom_seo_model)) {
                $custom_seo_model = new CustomSeoBase();
                $custom_seo_model->reference_data_id = $category->categories_id;
                $custom_seo_model->type = 5;
                $custom_seo_model->save();
            }

            foreach ($input['category_metadata'] as $category_metadata_row) {
                $category_metadata = CategoryMetadata::find()
                    ->where(['category_id' => $category->category_id])
                    ->andWhere(['language_id' => $category_metadata_row['language_id']])
                    ->one();
                if (empty($category_metadata)) {
                    $category_metadata = new CategoryMetadata();
                    $category_metadata->category_id = $category->category_id;
                    $category_metadata->language_id = $category_metadata_row['language_id'];
                }
                $category_metadata->title = $category_metadata_row['title'];
                $category_metadata->keyword = $category_metadata_row['keyword'];
                $category_metadata->description = $category_metadata_row['description'];
                $category_metadata->save();

                // offgamers DB
                $custom_seo_translation = CustomSeoTranslation::find()
                    ->where(['custom_seo_id' => $custom_seo_model->custom_seo_id])
                    ->andWhere(['language_id' => $category_metadata_row['language_id']])
                    ->one();
                if (empty($custom_seo_translation)) {
                    $custom_seo_translation = new CustomSeoTranslation();
                    $custom_seo_translation->custom_seo_id = $custom_seo_model->custom_seo_id;
                    $custom_seo_translation->language_id = $category_metadata_row['language_id'];
                }
                $custom_seo_translation->meta_title = $category_metadata_row['title'];
                $custom_seo_translation->meta_keyword = $category_metadata_row['keyword'];
                $custom_seo_translation->meta_description = $category_metadata_row['description'];
                $custom_seo_translation->status = 1;
                $custom_seo_translation->save();
            }
        }
    }

    private static function saveS3Image($categories_image, int $categories_id): string
    {
        /**
         * @var AWSS3 $s3
         */
        $s3 = Yii::$app->aws->getS3('BUCKET_IMAGE');

        $filename = sprintf(
            "%s/%d_%d.%s",
            self::S3_CATEGORIES_IMAGE_FOLDER,
            $categories_id,
            time(),
            $categories_image['image_extension']
        );

        $arrayBase64 = explode(",", $categories_image['image_base64']);
        $imageData = base64_decode(end($arrayBase64));

        return $s3->saveContent($filename, $imageData, AWSS3::S3_ACL_PUBLIC_READ);
    }

    private static function deleteS3Image(string $imageS3Path): void
    {
        /**
         * @var AWSS3 $s3
         */
        $s3 = Yii::$app->aws->getS3('BUCKET_IMAGE');

        $pathInfo = pathinfo($imageS3Path);

        if (!isset($pathInfo['basename'])) {
            return;
        }

        $absoluteImagePath = sprintf(
            "%s/%s",
            self::S3_CATEGORIES_IMAGE_FOLDER,
            $pathInfo['basename']
        );

        $s3->deleteContent($absoluteImagePath);
    }

    private static function saveCategoryDiscountList($input, $category)
    {
        if (isset($input['cdrules_id']) && !empty($input['cdrules_id'])) {
            $discount_list = CategoryDiscountList::find()
                ->where(['category_id' => $category->category_id])
                ->one();
            if (empty($discount_list)) {
                $discount_list = new CategoryDiscountList();
                $discount_list->category_id = $category->category_id;
            }
            $discount_list->cdrules_id = $input['cdrules_id'];
            $discount_list->save();

            // offgamers DB
            $categories_discount_list = CategoriesDiscountList::find()
                ->where(['cdl_category_id' => $category->categories_id])
                ->one();
            if (empty($categories_discount_list)) {
                $categories_discount_list = new CategoriesDiscountList();
                $categories_discount_list->cdl_category_id = $category->categories_id;
            }
            $categories_discount_list->cdrules_id = $input['cdrules_id'];
            $categories_discount_list->save();
        } else {
            CategoryDiscountList::deleteAll(['category_id' => $category->category_id]);
            CategoriesDiscountList::deleteAll(['cdl_category_id' => $category->categories_id]);
        }
    }

    private static function saveCategoryGroupPurchaseLimit($input, $category_id)
    {
        if (isset($input['category_group_purchase_limit'])) {
            $category_group_purchase_list = CategoryGroupPurchaseLimit::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($category_group_purchase_list) > 0) {
                // make customers_group_id for the key in array
                $customers_group_list = ArrayHelper::index(
                    $input['category_group_purchase_limit'],
                    'customers_group_id'
                );
                foreach ($category_group_purchase_list as $categories_group_purchase_row) {
                    if (empty($customers_group_list[$categories_group_purchase_row['customers_group_id']])) {
                        CategoryGroupPurchaseLimit::deleteAll([
                            'category_id' => $category_id,
                            'customers_group_id' => $categories_group_purchase_row['customers_group_id']
                        ]);
                    }
                }
            }

            foreach ($input['category_group_purchase_limit'] as $categories_group_purchase_row) {
                if ($categories_group_purchase_row['amount'] || $categories_group_purchase_row['x_minute']) {
                    $group_purchase_limit_model = CategoryGroupPurchaseLimit::find()
                        ->where(['category_id' => $category_id])
                        ->andWhere(['customers_group_id' => $categories_group_purchase_row['customers_group_id']])
                        ->one();

                    if (empty($group_purchase_limit_model)) {
                        $group_purchase_limit_model = new CategoryGroupPurchaseLimit();
                        $group_purchase_limit_model->customers_group_id = $categories_group_purchase_row['customers_group_id'];
                        $group_purchase_limit_model->category_id = $category_id;
                    }
                    $group_purchase_limit_model->amount = $categories_group_purchase_row['amount'] ?: 0;
                    $group_purchase_limit_model->x_minute = $categories_group_purchase_row['x_minute'] ?: 0;
                    $group_purchase_limit_model->save();
                }
            }
        }
    }

    private static function saveCategoryCustomerGroupRestriction($input, $category_id)
    {
        if (isset($input['category_customer_group_restriction'])) {
            $customer_group_restriction_list = CategoryCustomerGroupRestriction::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($customer_group_restriction_list) > 0) {
                foreach ($customer_group_restriction_list as $category_customer_group_row) {
                    if (!in_array(
                        $category_customer_group_row['customers_group_id'],
                        $input['category_customer_group_restriction']
                    )) {
                        CategoryCustomerGroupRestriction::deleteAll([
                            'category_id' => $category_id,
                            'customers_group_id' => $category_customer_group_row['customers_group_id']
                        ]);
                    }
                }
            }

            foreach ($input['category_customer_group_restriction'] as $row) {
                $categories_customer_group_restriction = CategoryCustomerGroupRestriction::find()
                    ->where(['category_id' => $category_id])
                    ->andWhere(['customers_group_id' => $row])
                    ->one();

                if (empty($categories_customer_group_restriction)) {
                    $categories_customer_group_restriction = new CategoryCustomerGroupRestriction();
                    $categories_customer_group_restriction->category_id = $category_id;
                    $categories_customer_group_restriction->customers_group_id = $row;
                    $categories_customer_group_restriction->save();
                }
            }
        } else {
            CategoryCustomerGroupRestriction::deleteAll(['category_id' => $category_id]);
        }
    }

    private static function saveCategorySoftBlock($input, $category_id)
    {
        if (isset($input['category_soft_block'])) {
            $category_soft_block = CategorySoftBlock::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($category_soft_block) > 0) {
                foreach ($category_soft_block as $category_soft_block_row) {
                    if (!in_array($category_soft_block_row['country_code'], $input['category_soft_block'])) {
                        CategorySoftBlock::deleteAll([
                            'category_id' => $category_id,
                            'country_code' => $category_soft_block_row['country_code']
                        ]);
                    }
                }
            }

            foreach ($input['category_soft_block'] as $row) {
                $category_soft_block = CategorySoftBlock::find()
                    ->where(['category_id' => $category_id])
                    ->andWhere(['country_code' => $row])
                    ->one();

                if (empty($category_soft_block)) {
                    $category_soft_block = new CategorySoftBlock();
                    $category_soft_block->category_id = $category_id;
                    $category_soft_block->country_code = $row;
                    $category_soft_block->save();
                }
            }
        } else {
            CategorySoftBlock::deleteAll(['category_id' => $category_id]);
        }
    }

    private static function saveCategoryHardBlock($input, $category_id)
    {
        if (isset($input['category_hard_block'])) {
            $category_hard_block = CategoryHardBlock::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($category_hard_block) > 0) {
                foreach ($category_hard_block as $category_block_block_row) {
                    if (!in_array($category_block_block_row['country_code'], $input['category_hard_block'])) {
                        CategoryHardBlock::deleteAll([
                            'category_id' => $category_id,
                            'country_code' => $category_block_block_row['country_code']
                        ]);
                    }
                }
            }

            foreach ($input['category_hard_block'] as $row) {
                $category_hard_block = CategoryHardBlock::find()
                    ->where(['category_id' => $category_id])
                    ->andWhere(['country_code' => $row])
                    ->one();

                if (empty($category_hard_block)) {
                    $category_hard_block = new CategoryHardBlock();
                    $category_hard_block->category_id = $category_id;
                    $category_hard_block->country_code = $row;
                    $category_hard_block->save();
                }
            }
        } else {
            CategoryHardBlock::deleteAll(['category_id' => $category_id]);
        }
    }

    private static function saveCategoryPaymentType($input, $category_id)
    {
        if (isset($input['category_payment_type'])) {
            $category_payment_type = CategoryPaymentType::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($category_payment_type) > 0) {
                foreach ($category_payment_type as $category_payment_type_row) {
                    if (!in_array($category_payment_type_row['type'], $input['category_payment_type'])) {
                        CategoryPaymentType::deleteAll([
                            'category_id' => $category_id,
                            'type' => $category_payment_type_row['type']
                        ]);
                    }
                }
            }

            foreach ($input['category_payment_type'] as $row) {
                $category_payment_type = CategoryPaymentType::find()
                    ->where(['category_id' => $category_id])
                    ->andWhere(['type' => $row])
                    ->one();

                if (empty($category_payment_type)) {
                    $category_payment_type = new CategoryPaymentType();
                    $category_payment_type->category_id = $category_id;
                    $category_payment_type->type = $row;
                    $category_payment_type->save();
                }
            }
        } else {
            CategoryPaymentType::deleteAll(['category_id' => $category_id]);
        }
    }

    private static function saveCategoryPaymentRestriction($input, $category_id)
    {
        if (isset($input['category_payment_restriction'])) {
            $category_payment_restriction = CategoryPaymentRestriction::find()
                ->where(['category_id' => $category_id])
                ->asArray()
                ->all();
            if (count($category_payment_restriction) > 0) {
                foreach ($category_payment_restriction as $category_payment_restriction_row) {
                    if (!in_array(
                        $category_payment_restriction_row['payment_methods_id'],
                        $input['category_payment_restriction']
                    )) {
                        CategoryPaymentRestriction::deleteAll([
                            'category_id' => $category_id,
                            'payment_methods_id' => $category_payment_restriction_row['payment_methods_id']
                        ]);
                    }
                }
            }

            foreach ($input['category_payment_restriction'] as $row) {
                $category_payment_restriction = CategoryPaymentRestriction::find()
                    ->where(['category_id' => $category_id])
                    ->andWhere(['payment_methods_id' => $row])
                    ->one();
                if (empty($category_payment_restriction)) {
                    $category_payment_restriction = new CategoryPaymentRestriction();
                    $category_payment_restriction->category_id = $category_id;
                    $category_payment_restriction->payment_methods_id = $row;
                    $category_payment_restriction->save();
                }
            }
        } else {
            CategoryPaymentRestriction::deleteAll(['category_id' => $category_id]);
        }
    }

    private static function getCategoryDescriptionList($category_id)
    {
        return CategoryDescription::find()
            ->select(['language_id', 'name', 'notice', 'short_description', 'description'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryMetadata($category_id)
    {
        return CategoryMetadata::find()
            ->select(['language_id', 'title', 'keyword', 'description'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryDiscountList($category_id)
    {
        return CategoryDiscountList::find()->select(['cdrules_id'])
            ->where(['category_id' => $category_id])
            ->asArray()->one();
    }

    private static function getCategoryGroupPurchaseLimit($category_id)
    {
        return CategoryGroupPurchaseLimit::find()
            ->select(['amount', 'x_minute', 'customers_group_id'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryCustomerGroupRestriction($category_id)
    {
        return CategoryCustomerGroupRestriction::find()
            ->select(['customers_group_id'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategorySoftBlock($category_id)
    {
        return CategorySoftBlock::find()->select(['country_code'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryHardBlock($category_id)
    {
        return CategoryHardBlock::find()->select(['country_code'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryPaymentType($category_id)
    {
        return CategoryPaymentType::find()->select(['type'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function getCategoryPaymentRestriction($category_id)
    {
        return CategoryPaymentRestriction::find()->select(['payment_methods_id'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();
    }

    private static function deleteSEO($categories_id)
    {
        $custom_seo_model = CustomSeoBase::find()
            ->where(['reference_data_id' => $categories_id])
            ->andWhere(['type' => 5])
            ->one();
        if (!empty($custom_seo_model)) {
            CustomSeoTranslation::deleteAll(['custom_seo_id' => $custom_seo_model->custom_seo_id]);
        }
        CustomSeoBase::deleteAll(['reference_data_id' => $categories_id, 'type' => 5]);
    }

    private static function deleteCategoriesAndDescription($categories_id)
    {
        $categories_layer_2 = CategoriesBase::find()
            ->where(['categories_id' => $categories_id])
            ->one();
        if (!empty($categories_layer_2)) {
            // Layer 3
            $categories_layer3_list = CategoriesBase::find()
                ->select(['categories_id'])
                ->where(['parent_id' => $categories_id])
                ->asArray()->all();
            foreach ($categories_layer3_list as $categories_layer_row) {
                CategoriesDescription::deleteAll(['categories_id' => $categories_layer_row['categories_id']]);
            }
            CategoriesBase::deleteAll(['parent_id' => $categories_id]);
            CategoriesDescription::deleteAll(['categories_id' => $categories_id]);
            CategoriesBase::deleteAll(['categories_id' => $categories_id]);
        }
    }

    private static function makeResponse(bool $statusCode, string $message): array
    {
        return [
            "status" => $statusCode,
            "message" => $message
        ];
    }

    public static function getCategoryInfo($url_alias, $language_id, $country_code, $ip_country)
    {
        $category = Category::find()->where(['seo_url' => $url_alias, 'status' => 1])->asArray()->limit(1)->one();
        if ($category) {
            $category_id = $category['category_id'];

            $soft_block = CategorySoftBlock::find()->where(
                ['category_id' => $category_id, 'country_code' => $country_code]
            )->exists();

            $hard_block = CategoryHardBlock::find()->where(
                ['category_id' => $category_id, 'country_code' => $ip_country]
            )->exists();

            $category_description = CategoryDescription::getCategoryDescription($category_id, $language_id);
            $category_metadata = CategoryMetadata::getMetaData($category_id, $language_id, $category_description['name'], $category_description['description']);

            return [
                'title' => $category_description['name'],
                'categories_id' => $category['categories_id'],
                'soft_block_country' => $soft_block,
                'hard_block_country' => $hard_block,
                'image_url' => $category['image_url'],
                'url' => $category['seo_url'],
                'short_description' => $category_description['short_description'],
                'description' => $category_description['description'],
                'notice' => $category_description['notice'],
                'meta_title' => $category_metadata['meta_title'],
                'meta_keyword' => $category_metadata['meta_keyword'],
                'meta_description' => $category_metadata['meta_description'],
            ];
        }
        return null;
    }
}
