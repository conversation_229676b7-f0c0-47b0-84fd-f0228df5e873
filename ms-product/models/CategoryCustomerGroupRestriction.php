<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_customer_group_restriction".
 *
 * @property int $category_customer_group_restriction_id
 * @property int $category_id
 * @property int $customers_group_id
 */
class CategoryCustomerGroupRestriction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_customer_group_restriction';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id', 'customers_group_id'], 'required'],
            [['category_customer_group_restriction_id', 'category_id', 'customers_group_id'], 'integer'],
            [['category_id', 'customers_group_id'], 'unique', 'targetAttribute' => ['category_id', 'customers_group_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_customer_group_restriction_id' => 'Category Customer Group Restriction ID',
            'category_id' => 'Category ID',
            'customers_group_id' => 'Customers Group ID',
        ];
    }
}
