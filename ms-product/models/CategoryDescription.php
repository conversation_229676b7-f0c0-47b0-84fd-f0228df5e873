<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;

/**
 * This is the model class for table "OG.categories_description".
 *
 * @property int $category_description_id
 * @property int $category_id
 * @property int $language_id
 * @property string $name
 * @property string $notice
 * @property string $short_description
 * @property string $description
 */
class CategoryDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id', 'language_id'], 'required'],
            [['category_description_id', 'category_id', 'language_id'], 'integer'],
            [['name', 'notice', 'short_description', 'description'], 'string'],
            [['category_id', 'language_id'], 'unique', 'targetAttribute' => ['category_id', 'language_id']]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_description_id' => 'Category Description ID',
            'category_id' => 'Category ID',
            'language_id' => 'Language ID',
            'name' => 'Categories Name',
            'notice' => 'Categories Notice',
            'short_description' => 'Categories Short Description',
            'description' => 'Categories Description'
        ];
    }

    public function getCategory() {
        return $this->hasOne(Category::class, ['category_id' => 'category_id']);
    }


    public static function getCategoryDescriptionByLanguageId($category_id, $language_id, $column)
    {
        return self::find()
            ->select($column)
            ->where(['category_id' => $category_id, 'language_id' => $language_id])
            ->limit(1)
            ->asArray()
            ->one();
    }

    public static function getCategoryName($category_id, $language_id)
    {
        $column = ['name'];
        $data = self::getCategoryDescriptionByLanguageId($category_id, $language_id, $column);
        if ($language_id == 1) {
            $default_data = $data;
        } else {
            $default_data = self::getCategoryDescriptionByLanguageId($category_id, 1, $column);
        }

        return GeneralCom::getColumnValueWithDefault($data, $default_data, $column);
    }

    public static function getCategoryDescription($category_id, $language_id)
    {
        $column = ['name', 'notice', 'short_description', 'description'];
        $data = self::getCategoryDescriptionByLanguageId($category_id, $language_id, $column);
        if ($language_id == 1) {
            $default_data = $data;
        } else {
            $default_data = self::getCategoryDescriptionByLanguageId($category_id, 1, $column);
        }

        return GeneralCom::getColumnValueWithDefault($data, $default_data, $column);
    }
}
