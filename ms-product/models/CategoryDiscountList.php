<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_discount_list".
 *
 * @property int $category_discount_list_id
 * @property int $category_id
 * @property int $cdrules_id
 */
class CategoryDiscountList extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_discount_list';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_discount_list_id', 'category_id', 'cdrules_id'], 'integer'],
            [['category_id', 'cdrules_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_discount_list_id' => 'Category Discount List ID',
            'category_id' => 'Category ID',
            'cdrules_id' => 'CD Rules ID',
        ];
    }
}
