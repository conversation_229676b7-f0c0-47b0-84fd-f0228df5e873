<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_group_purchase_limit".
 *
 * @property int $category_group_purchase_limit_id
 * @property int $category_id
 * @property int $customers_group_id
 * @property double $amount
 * @property double $x_minute
 */
class CategoryGroupPurchaseLimit extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_group_purchase_limit';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id'], 'required'],
            [['category_group_purchase_limit_id', 'category_id', 'customers_group_id', 'x_minute'], 'integer'],
            [['amount'], 'number'],
            [['category_id', 'customers_group_id'], 'unique', 'targetAttribute' => ['category_id', 'customers_group_id']]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_group_purchase_limit_id' => 'Category Group Purchase Limit ID',
            'category_id' => 'Category ID',
            'customers_group_id' => 'Customers Group ID',
            'amount' => 'Amount',
            'x_minute' => 'X Minute'
        ];
    }
}
