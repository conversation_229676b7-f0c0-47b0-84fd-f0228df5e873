<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;

/**
 * This is the model class for table "category_metadata".
 *
 * @property int $category_metadata_id
 * @property int $category_id
 * @property int $language_id
 * @property string $title
 * @property string $keyword
 * @property string $description
 */
class CategoryMetadata extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_metadata';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id', 'language_id'], 'integer'],
            [['title', 'keyword', 'description'], 'string'],
            [['category_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_metadata_id' => 'Category Metadata ID',
            'category_id' => 'Category ID',
            'brand_id' => 'Brand ID',
            'seo_url' => 'SEO URL',
            'image_url' => 'Image URL',
            'sort_order' => 'Sort Order',
            'status' => 'Status'
        ];
    }

    public static function getCategoryMetaDataByLanguageId($category_id, $language_id, $column)
    {
        return self::find()
            ->select($column)
            ->where(['category_id' => $category_id, 'language_id' => $language_id])
            ->limit(1)
            ->asArray()
            ->one();
    }

    public static function getMetaData($category_id, $language_id, $title, $description)
    {
        $language_code = Yii::$app->enum->getLanguage('getLanguageCode');
        $column = ['title', 'keyword', 'description'];
        $data = self::getCategoryMetaDataByLanguageId($category_id, $language_id, $column);
        $default_data = self::getCategoryMetaDataByLanguageId($category_id, 1, $column);
        $return_arr = GeneralCom::getColumnValueWithDefault($data, $default_data, $column);

        if (empty($return_arr['title'])) {
            $return_arr['title'] = Yii::t(
                    'seo',
                    'TEXT_BUY_TITLE',
                    [],
                    $language_code[$language_id]
                ) . ' %s - ' . Yii::t(
                    'seo',
                    'TEXT_DEFAULT_TITLE',
                    [],
                    $language_code[$language_id]
                );
        }

        $return_arr['title'] = sprintf($return_arr['title'], $title);

        if (empty($return_arr['description'])) {
            $return_arr['description'] = GeneralCom::getFirstParagraph($description, '<br>');
        }

        foreach ($column as $col) {
            $return_arr['meta_' . $col] = $return_arr[$col];
            unset($return_arr[$col]);
        }

        return $return_arr;
    }
}
