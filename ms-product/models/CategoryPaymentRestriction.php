<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_payment_restriction".
 *
 * @property int $category_payment_restriction_id
 * @property int $category_id
 * @property int $payment_methods_id
 */
class CategoryPaymentRestriction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_payment_restriction';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id'], 'required'],
            [['category_payment_restriction_id', 'category_id', 'payment_methods_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_payment_restriction_id' => 'Category Payment Restriction ID',
            'category_id' => 'Category ID',
            'payment_methods_id' => 'Payment Method ID'
        ];
    }
}
