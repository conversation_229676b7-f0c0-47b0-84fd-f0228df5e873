<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_payment_type".
 *
 * @property int $category_payment_type_id
 * @property int $category_id
 * @property int $type
 */
class CategoryPaymentType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_payment_type';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id'], 'required'],
            [['category_payment_type_id', 'category_id', 'type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_payment_type_id' => 'Category Type ID',
            'category_id' => 'Category ID',
            'type' => 'Type'
        ];
    }
}
