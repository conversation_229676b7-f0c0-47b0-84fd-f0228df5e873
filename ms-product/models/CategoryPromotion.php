<?php

namespace micro\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "category_promotion".
 *
 * @property int $category_promotion_id
 * @property int $category_id
 * @property string $start_date
 * @property string $end_date
 * @property int $unlimited_purchase_per_user
 * @property int $max_purchase_per_user
 * @property int $total_promo_quantity
 */
class CategoryPromotion extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_promotion';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id'], 'required'],
            [['category_promotion_id', 'category_id', 'unlimited_purchase_per_user', 'max_purchase_per_user',
                'total_promo_quantity'], 'integer'],
            [['start_date', 'end_date'], 'string'],
            [['start_date', 'end_date', 'total_promo_quantity', 'category_id'], 'required']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_promotion_id' => 'Category Promotion ID',
            'category_id' => 'Category ID',
            'unlimited_purchase_per_user' => 'Unlimited Purchase per User',
            'max_purchase_per_user' => 'Max Purchase per User',
            'start_date' => 'Start Date',
            'end_date' => 'End Date'
        ];
    }

    public static function saveCategoryPromotion($input) {
        $category_promotion = self::saveCategoryPromotionDetail($input);
        self::saveCategoryPromotionCustomerGroup($input, $category_promotion['category_promotion_id']);
        return $category_promotion;
    }

    public static function saveCategoryPromotionDetail($input) {
        $category_promotion = null;
        if (!empty($input['category_promotion_id'])) {
            $category_promotion = self::find()
                ->where(['category_promotion_id' => $input['category_promotion_id']])
                ->one();
        }
        if (empty($category_promotion)) {
            $category_promotion = new CategoryPromotion();
        }
        $category_promotion->category_id = $input['category_id'];
        $category_promotion->unlimited_purchase_per_user = $input['unlimited_purchase_per_user'] ?: 0;
        $category_promotion->max_purchase_per_user = $input['max_purchase_per_user'] ?: 0;
        $category_promotion->total_promo_quantity = $input['total_promo_quantity'] ?: 0;
        $category_promotion->start_date = date("Y-m-d H:i:00", strtotime($input['start_date']));
        $category_promotion->end_date = date("Y-m-d H:i:00", strtotime($input['end_date']));
        $category_promotion->save();

        $result = ArrayHelper::toArray($category_promotion);
        $result['status'] = self::getStatusByCompareDate($category_promotion->end_date);
        return $result;
    }

    private static function saveCategoryPromotionCustomerGroup($input, $category_promotion_id)
    {
        if (isset($input['category_promotion_customer_group'])) {
            foreach($input['category_promotion_customer_group'] as $category_promotion_customer_row) {
                $customer_group = CategoryPromotionCustomerGroup::find()
                    ->where(['category_promotion_id' => $category_promotion_id])
                    ->andWhere(['customers_group_id' => $category_promotion_customer_row['customers_group_id']])
                    ->one();

                if (empty($customer_group)) {
                    $customer_group = new CategoryPromotionCustomerGroup();
                    $customer_group->customers_group_id = $category_promotion_customer_row['customers_group_id'];
                    $customer_group->category_promotion_id = $category_promotion_id;
                }
                $customer_group->promo_quantity = $category_promotion_customer_row['promo_quantity'] ?: 0;
                $customer_group->discount_percentage = $category_promotion_customer_row['discount_percentage'] ?: 0;
                $customer_group->op_reward = $category_promotion_customer_row['op_reward'] ?: 0;
                $customer_group->save();
            }
        }
    }

    public static function getCategoryPromotion($category_promotion_id)
    {
        $category_promotion = CategoryPromotion::find()
            ->where(['category_promotion_id' => $category_promotion_id])
            ->asArray()
            ->one();
        if ($category_promotion) {
            $category_promotion['category_promotion_customer_group' ] = self::getCategoryPromotionCustomerGroup($category_promotion_id);
        }
        return $category_promotion;
    }

    public static function getCategoryPromotionList($category_id)
    {
        $category_promotion_list = self::find()
            ->select(['category_promotion_id','start_date', 'end_date'])
            ->where(['category_id' => $category_id])
            ->asArray()->all();

        // compare date and set status
        if (!empty($category_promotion_list)) {
            $currentTime = time();
            foreach($category_promotion_list as $key => $row) {
                $category_promotion_list[$key]['status'] = self::getStatusByCompareDate($row['end_date'], $currentTime);
            }
        }
        return $category_promotion_list;
    }

    private static function getStatusByCompareDate($date, $currentTime = null) {
        if ($currentTime === null) {
            $currentTime = time();
        }
        return strtotime($date) > $currentTime ? 'Active' : 'Inactive';
    }

    private static function getCategoryPromotionCustomerGroup($category_promotion_id)
    {
        return CategoryPromotionCustomerGroup::find()
            ->where(['category_promotion_id' => $category_promotion_id])
            ->asArray()->all();
    }

    public static function deleteCategoryPromotion($category_promotion_id) {
        CategoryPromotionCustomerGroup::deleteAll(['category_promotion_id' => $category_promotion_id]);
        self::deleteAll(['category_promotion_id' => $category_promotion_id]);
        return true;
    }
}
