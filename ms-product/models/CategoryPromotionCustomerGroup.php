<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_promotion_customer_group".
 *
 * @property int $category_promotion_customer_group_id
 * @property int $category_promotion_id
 * @property int $customers_group_id
 * @property int $promo_quantity
 * @property int $discount_percentage
 * @property int $op_reward
 */
class CategoryPromotionCustomerGroup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_promotion_customer_group';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_promotion_id'], 'required'],
            [['category_promotion_customer_group_id', 'category_promotion_id', 'customers_group_id',
                'promo_quantity'], 'integer'],
            [['discount_percentage', 'op_reward'], 'number']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_promotion_customer_group_id' => 'Category Promotion Customer Group ID',
            'category_promotion_id' => 'Category Promotion ID',
            'customers_group_id' => 'Customer Group ID',
            'promo_quantity' => 'Promo Quantity',
            'discount_percentage' => 'Discount Percentage',
            'op_reward' => 'OP Reward'
        ];
    }
}
