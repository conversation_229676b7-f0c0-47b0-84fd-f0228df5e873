<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "category_soft_block".
 *
 * @property int $category_soft_block_id
 * @property int $category_id
 * @property string $country_code
 */
class CategorySoftBlock extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'category_soft_block';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id'], 'required'],
            [['category_soft_block_id', 'category_id'], 'integer'],
            [['country_code'], 'string']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'category_soft_block_id' => 'Category Soft Block ID',
            'category_id' => 'Category ID',
            'country_code' => 'Country Code'
        ];
    }
}
