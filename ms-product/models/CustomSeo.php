<?php

namespace micro\models;

use Yii;
use yii\helpers\ArrayHelper;
use micro\models\enum\CustomSeoTypeEnum;

class CustomSeo extends CustomSeoBase
{

    public static function getCustomSeoList($params, $page = 1, $limit = 10)
    {
        
        $searchModel = new self();

        $searchModel->load($params, '');

        $query = self::find()->alias('cs')->select(['cs.*', 'cst.*'])->joinWith('customSeoTranslation cst', false);

        $query->filterWhere(
            ['type' => $searchModel->type]
        );

        $query->andFilterWhere([
            'LIKE',
            'cst.title',
            $searchModel->title,
        ]);

        if (!empty($params['is_general_seo'])) {
            $query->andFilterWhere(
                ['cs.reference_data_id' => 0]
            );
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [];
            $item['id'] = $obj['custom_seo_id'];
            $item['type'] = CustomSeoTypeEnum::getLabel($obj['type']);
            $item['reference_data_id'] = $obj['reference_data_id'];
            $item['title'] = $obj['title'];
            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];

        return ($return_array);
    }

    public static function getItemList($input)
    {
        $output = [];

        if (empty($input['list']) && strtolower($input['type']) == 'products') {
            $input['type'] = 'category';
        }

        switch (strtolower($input['type'])) {
            case 'category':
                $active_category = CategoriesStructures::find()->select('categories_structures_value')->where(['categories_structures_key' => 'games'])->asArray()->one();
                $category_list = explode(",", $active_category['categories_structures_value']);
                $data = CategoriesDescription::find()->alias('cd')
                ->select(['cd.categories_id', 'cd.categories_name'])
                ->joinwith('categories c', false)
                ->where(['cd.categories_id' => $category_list, 'cd.language_id' => 1, 'c.categories_status' => 1])
                ->asArray()->all();
                $output = ArrayHelper::map($data, 'categories_id', 'categories_name');
                break;

            case 'game blog':
                $data = GameBlogDescription::getAllGameBlog();
                $output = ArrayHelper::map($data, 'game_blog_id', 'game_blog_description');
                break;

            case 'products':
                $category_list = Categories::getAllSubCategoryByCategoryId([$input['list']]);
                $data = Products::getAllProductByCategoryId($category_list);
                $output = $data;
                break;
        }
        return $output;
    }

    public static function getMetaTag($input)
    {
        $enum = Yii::$app->enum;
        $language = $enum->getLanguage('listData');

        $output = [
            'id' => $input['id'],
            'type' => ucwords(strtolower($input['type']))
        ];

        $type = enum\CustomSeoTypeEnum::getValueByName($output['type']);

        $force_default = empty($input['force_default']) ? false : $input['force_default'];

        $model_data = (!$force_default ? self::find()->where(['type' => $type, 'reference_data_id' => $output['id']])->with('customSeoTranslations')->asArray()->one() : null);

        if ($model_data) {
            $trans_data = ArrayHelper::index($model_data['customSeoTranslations'], 'language_id');
        } else {
            $trans_data = [];
        }

        extract(self::getDefaultMeta($output['type'], $input['id']));

        $field_list = ['meta_title', 'meta_keyword', 'meta_description'];

        foreach ($language as $k => $v) {
            foreach ($field_list as $field_name) {
                $data = $$field_name;
                if (!empty($trans_data[$k])) {
                    $output[$k][$field_name] = $trans_data[$k][$field_name];
                } elseif (!empty($data[$k])) {
                    $output[$k][$field_name] = $data[$k];
                } else {
                    $output[$k][$field_name] = (!empty($data[1]) ? $data[1] : '');
                }
            }

            if (!empty($trans_data[$k])) {
                $output[$k]['status'] = $trans_data[$k]['status'];
            }

            if (in_array($type, [1, 2, 3, 4]) && !$force_default) {
                $output[$k]['title'] = $trans_data[$k]['title'];
            } elseif (!empty($title[$k])) {
                $output[$k]['title'] = $title[$k];
            } else {
                $output[$k]['title'] = (!empty($title[1]) ? $title[1] : '');
            }
        }

        return $output;
    }

    public static function getDefaultMeta($type, $id)
    {
        $enum = Yii::$app->enum;
        $language = $enum->getLanguage('listData');
        $language_code = $enum->getLanguage('getLanguageCode');

        switch (strtolower($type)) {
            case 'category':
                $title = ArrayHelper::map(CategoriesDescription::find()->where(['categories_id' => $id, 'language_id' => array_keys($language)])->asArray()->all(), 'language_id', 'categories_name');
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                $meta_keyword = ArrayHelper::map(Categories::getMetaKeyword($id), 'language_id', 'search_value');
                $meta_description = Categories::getMetaDescription($id);
                break;

            case 'products':
                $product_type = Products::find()->select('products_type')->where(['products_id' => $id])->one();
                $title = ArrayHelper::map(ProductsDescription::find()->where(['products_id' => $id])->asArray()->all(), 'language_id', 'products_name');
                $category_id = Categories::getSecondLayerCategoryIdByProductId($id);
                $meta_keyword = ArrayHelper::map(Categories::getMetaKeyword($category_id), 'language_id', 'search_value');
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_CHEAP_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                if($product_type['products_type'] == 2){
                    $meta_description = GameProduct::getMetaDescription($id);

                }
                else{
                    $meta_description = Categories::getMetaDescription($category_id);
                }

                break;

            case 'game blog':
                $title = ArrayHelper::map(GameBlogDescription::find()->where(['game_blog_id' => $id])->asArray()->all(), 'language_id', 'game_blog_description');
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                $meta_keyword = ArrayHelper::map(GameBlog::getMetaKeyword($id), 'language_id', 'search_value');
                $meta_description = ArrayHelper::map(GameBlog::getMetaDescription($id), 'language_id', 'description');
                break;

            case 'home':
                $title = [];
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $title[$lang_id] = '';
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_MAIN_TITLE', [], $language_code[$lang_id]) . ' - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_KEYWORDS', [], $language_code[$lang_id]);
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;

            case 'browse':
            case 'search':
            case 'game key':
                $title = [];
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $title[$lang_id] = '';
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_BUY_KEYWORD', [], $language_code[$lang_id]) . ' %s';
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;

            default:
                $title = [];
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $title[$lang_id] = '';
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_KEYWORDS', [], $language_code[$lang_id]);
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;
        }
        return [
            'title' => $title,
            'meta_title' => $meta_title,
            'meta_keyword' => $meta_keyword,
            'meta_description' => $meta_description,
        ];
    }


    public static function saveCustomSeo($input)
    {
        $type = enum\CustomSeoTypeEnum::getValueByName(ucwords(strtolower($input['type'])));

        $model = self::findOne(['reference_data_id' => $input['id'], 'type' => $type]);

        if (!$model) {
            $model = new self();
            $model->type = $type;
            $model->reference_data_id = $input['id'];
            $model->save();
        }

        $language = Yii::$app->enum->getLanguage('listData');

        $translation_model = ArrayHelper::index(CustomSeoTranslation::find()->where(['custom_seo_id' => $model->custom_seo_id])->all(), 'language_id');

        foreach ($language as $l_id => $l_val) {
            if (!empty($translation_model[$l_id])) {
                $t_model = $translation_model[$l_id];
            } else {
                $t_model = new CustomSeoTranslation();
            }
            extract(self::getDefaultMeta($input['type'], $input['id']));
            $t_model->load($input['translation'][$l_id], '');
            $t_model->language_id = $l_id;
            $t_model->custom_seo_id = $model->custom_seo_id;
            if (!in_array($type, [1, 2, 3, 4])) {
                $t_model->title = (!empty($title[$l_id]) ? $title[$l_id] : $title[1]);
            }
            $t_model->save();
        }

        return true;
    }

    public static function cloneCustomSeo($input)
    {
        $model = self::find()->where(['custom_seo_id' => $input['id']])->with('customSeoTranslations')->asArray()->one();

        $translation_data = ArrayHelper::index($model['customSeoTranslations'], 'language_id');

        foreach ($input['clone_id'] as $item_id) {
            self::saveCustomSeo(['type' => $input['type'], 'id' => $item_id, 'translation' => $translation_data]);
        }

        return true;
    }

    public static function generateDefaultEntry()
    {
        foreach (enum\CustomSeoTypeEnum::listData() as $key => $val) {
            switch ($key) {
                case 1:
                case 2:
                case 3:
                case 4:
                    extract(self::getDefaultMeta($val, 0));
                    $data = [];
                    $language = Yii::$app->enum->getLanguage('listData');
                    foreach ($language as $l_key => $l_val) {
                        $data[$l_key] = [
                            'title' => $val,
                            'meta_title' => $meta_title[$l_key],
                            'meta_description' => $meta_description[$l_key],
                            'meta_keyword' => $meta_keyword[$l_key],
                            'status' => 1
                        ];
                    }
                    self::saveCustomSeo(['id' => 0, 'type' => $val, 'translation' => $data]);
                    break;
            }
        }
        return true;
    }

    public static function getSeoContent($input)
    {
        $enum = Yii::$app->enum;
        $language_code = $enum->getLanguage('getLanguageCode');
        $language_id = $input['language_id'];
        $language = [$language_id => (isset($language_code[$language_id]) ? $language_code[$language_id] : $language_code[1])];

        $output = [
            'id' => $input['id'],
            'type' => ucwords(strtolower($input['type']))
        ];

        $type = enum\CustomSeoTypeEnum::getValueByName($output['type']);

        $model_data = self::find()->select(['cs.*', 'cst.*'])->alias('cs')->where(['cs.type' => $type, 'cs.reference_data_id' => $output['id']])->joinWith('activeCustomSeoTranslations cst', 1)->asArray()->one();

        if ($model_data) {
            $trans_data = ArrayHelper::index($model_data['activeCustomSeoTranslations'], 'language_id');
        } else {
            $trans_data = [];
        }

        switch (strtolower($output['type'])) {
            case 'category':
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                $meta_keyword = ArrayHelper::map(Categories::getMetaKeyword($output['id'], $language_id, true), 'language_id', 'search_value');
                $meta_description = Categories::getMetaDescription($output['id'], $language_id, true);
                break;

            case 'products':
                $product_type = Products::find()->select('products_type')->where(['products_id' => $output['id']])->one();
                $title = ArrayHelper::map(ProductsDescription::find()->where(['products_id' => $output['id']])->asArray()->all(), 'language_id', 'products_name');
                $category_id = Categories::getSecondLayerCategoryIdByProductId($output['id']);
                $meta_keyword = ArrayHelper::map(Categories::getMetaKeyword($category_id), 'language_id', 'search_value');
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_CHEAP_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                if($product_type['products_type'] == 2){
                    $meta_description = GameProduct::getMetaDescription($output['id']);
                }
                else{
                    $meta_description = Categories::getMetaDescription($category_id);
                }
                break;

            case 'game blog':
                $meta_title = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                }
                $meta_keyword = ArrayHelper::map(GameBlog::getMetaKeyword($output['id'], $language_id, true), 'language_id', 'search_value');
                $meta_description = ArrayHelper::map(GameBlog::getMetaDescription($output['id'], $language_id, true), 'language_id', 'description');
                break;

            case 'home':
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $title[$lang_id] = '';
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_MAIN_TITLE', [], $language_code[$lang_id]) . ' - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_KEYWORDS', [], $language_code[$lang_id]);
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;

            case 'browse':
            case 'search':
            case 'game key':
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $title[$lang_id] = '';
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_BUY_TITLE', [], $language_code[$lang_id]) . ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_BUY_KEYWORD', [], $language_code[$lang_id]) . ' %s';
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;

            case 'promo':
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = ' %s - ' . Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_KEYWORDS', [], $language_code[$lang_id]);
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;

            default:
                $meta_title = [];
                $meta_keyword = [];
                $meta_description = [];
                foreach ($language as $lang_id => $lang_desc) {
                    $meta_title[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_TITLE', [], $language_code[$lang_id]);
                    $meta_keyword[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_KEYWORDS', [], $language_code[$lang_id]);
                    $meta_description[$lang_id] = Yii::t('seo', 'TEXT_DEFAULT_DESCRIPTION', [], $language_code[$lang_id]);
                }
                break;
        }

        $field_list = ['meta_title', 'meta_keyword', 'meta_description'];


        foreach ($field_list as $field_name) {
            $data = $$field_name;
            if (!empty($trans_data[$language_id])) {
                $output[$language_id][$field_name] = $trans_data[$language_id][$field_name];
            } elseif (!empty($data[$language_id])) {
                $output[$language_id][$field_name] = $data[$language_id];
            } else {
                $output[$language_id][$field_name] = (!empty($data[1]) ? $data[1] : '');
            }
        }

        $data = $output;

        return [
            'title' => $data[$input['language_id']]['meta_title'],
            'keyword' => $data[$input['language_id']]['meta_keyword'],
            'description' => $data[$input['language_id']]['meta_description']
        ];
    }

}