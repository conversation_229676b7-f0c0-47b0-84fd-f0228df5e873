<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "custom_seo".
 *
 * @property int $custom_seo_id
 * @property int $type
 * @property int $reference_data_id
 *
 * @property CustomSeoTranslation[] $customSeoTranslations
 */
class CustomSeoBase extends \yii\db\ActiveRecord
{
    public $title;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'custom_seo';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'type'], 'safe'],
            [['type', 'reference_data_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'custom_seo_id' => 'Custom Seo ID',
            'type' => 'Type',
            'reference_data_id' => 'Reference Data ID',
        ];
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomSeoTranslation()
    {
        return $this->hasOne(CustomSeoTranslation::className(), ['custom_seo_id' => 'custom_seo_id'])->andOnCondition(['language_id' => 1]);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomSeoTranslations()
    {
        return $this->hasMany(CustomSeoTranslation::className(), ['custom_seo_id' => 'custom_seo_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getActiveCustomSeoTranslations()
    {
        return $this->hasMany(CustomSeoTranslation::className(), ['custom_seo_id' => 'custom_seo_id'])->andWhere(['status' => 1]);
    }
}
