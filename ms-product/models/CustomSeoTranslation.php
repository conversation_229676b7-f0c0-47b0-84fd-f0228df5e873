<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "custom_seo_translation".
 *
 * @property int $custom_seo_id
 * @property int $custom_seo_translation_id
 * @property int $language_id
 * @property string $title
 * @property string $meta_title
 * @property string $meta_keyword
 * @property string $meta_description
 * @property int $status
 * @property string $created_at
 * @property string $updated_at
 *
 * @property CustomSeo $customSeo
 */
class CustomSeoTranslation extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'custom_seo_translation';
    }


    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['custom_seo_id', 'language_id', 'status'], 'integer'],
            [['meta_keyword', 'meta_description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['title', 'meta_title'], 'string', 'max' => 255],
            [['custom_seo_id'], 'exist', 'skipOnError' => true, 'targetClass' => CustomSeo::className(), 'targetAttribute' => ['custom_seo_id' => 'custom_seo_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'custom_seo_translation_id' => 'Custom Seo Translation ID',
            'custom_seo_id' => 'Custom Seo ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'meta_title' => 'Meta Title',
            'meta_keyword' => 'Meta Keyword',
            'meta_description' => 'Meta Description',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomSeo()
    {
        return $this->hasOne(CustomSeo::className(), ['custom_seo_id' => 'custom_seo_id']);
    }
}
