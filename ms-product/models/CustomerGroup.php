<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "customer_groups".
 *
 * @property int $customers_group_id
 * @property string $customers_group_name
 *
 * @property CustomSeoTranslation[] $customSeoTranslations
 */
class CustomerGroup extends \yii\db\ActiveRecord
{
    public $customers_group_id;
    public $customers_group_name;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customer_groups';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_group_name'], 'safe'],
            [['customers_group_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_group_id' => 'Customer Group ID',
            'customers_group_name' => 'Customer Group Name',
        ];
    }
    
}
