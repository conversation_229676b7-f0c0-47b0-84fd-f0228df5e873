<?php

namespace micro\models;

use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "customers_groups_purchase_control".
 *
 * @property int $products_id
 * @property int $customers_groups_id
 * @property int $purchase_limit
 * @property int $out_of_stock_flag 0: available, 1: out-of-stock
 * @property string|null $out_of_stock_datetime
 */
class CustomersGroupsPurchaseControl extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_groups_purchase_control';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'customers_groups_id', 'purchase_limit', 'out_of_stock_flag'], 'required'],
            [['products_id', 'customers_groups_id', 'purchase_limit', 'out_of_stock_flag'], 'integer'],
            [['out_of_stock_datetime'], 'safe'],
            [['products_id', 'customers_groups_id'], 'unique', 'targetAttribute' => ['products_id', 'customers_groups_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'customers_groups_id' => 'Customers Groups ID',
            'purchase_limit' => 'Purchase Limit',
            'out_of_stock_flag' => 'Out Of Stock Flag',
            'out_of_stock_datetime' => 'Out Of Stock Datetime',
        ];
    }

    public static function updateOutOfStockRule($products_id, $quantity)
    {
        return self::updateAll(
            ['out_of_stock_flag' => 1, 'out_of_stock_datetime' => new Expression('NOW()')],
            ['AND', ['products_id' => $products_id], ['>', 'purchase_limit', $quantity]]
        );
    }

    public static function getAllOutOfStockData($products_id)
    {
        return self::find()
            ->select(['customers_groups_id'])
            ->where(['products_id' => $products_id, 'out_of_stock_flag' => 1])
            ->asArray()
            ->all();
    }
}
