<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "dtone_og_mapping".
 *
 * @property int $mapping_id
 * @property int $type
 * @property string $og_id
 * @property string $dtone_id
 * @property string $description
 */
class DtoneOgMapping extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'dtone_og_mapping';
    }

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'og_id', 'dtone_id'], 'required'],
            [['og_id', 'type'], 'integer'],
            [['dtone_id', 'description'], 'string', 'max' => 255],
            [['description'], 'default', 'value' => '']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'mapping_id' => 'Mapping ID',
            'type' => 'Type',
            'og_id' => 'Og ID',
            'dtone_id' => 'Dtone ID',
            'description' => 'Description',
        ];
    }
}
