<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "frontend_template_lang".
 *
 * @property string $tpl_id
 * @property int $language_id
 * @property string $description
 * @property string $notice
 * @property string $system_requirements
 * @property string $remark
 * @property string $gallery_info
 * @property string $game_title
 * @property string $game_publisher
 * @property string $game_developer
 * @property string $game_release_date
 * @property string $game_keyword
 * @property string $related_link_info
 */
class FrontendTemplateLang extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'frontend_template_lang';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tpl_id', 'language_id'], 'required'],
            [['description', 'notice', 'system_requirements', 'remark', 'gallery_info', 'game_title', 'game_publisher', 'game_developer', 'game_release_date', 'related_link_info'], 'safe'],
            [['tpl_id', 'language_id'], 'integer'],
            [['description', 'system_requirements', 'remark', 'gallery_info', 'game_keyword', 'related_link_info'], 'string'],
            [['notice', 'game_title', 'game_publisher', 'game_developer', 'game_release_date'], 'string', 'max' => 255],
            [['tpl_id', 'language_id'], 'unique', 'targetAttribute' => ['tpl_id', 'language_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'tpl_id' => 'Tpl ID',
            'language_id' => 'Language ID',
            'description' => 'Description',
            'notice' => 'Notice',
            'system_requirements' => 'System Requirements',
            'remark' => 'Remark',
            'gallery_info' => 'Gallery Info',
            'game_title' => 'Game Title',
            'game_publisher' => 'Game Publisher',
            'game_developer' => 'Game Developer',
            'game_release_date' => 'Game Release Date',
            'game_keyword' => 'Game Keyword',
            'related_link_info' => 'Related Link Info',
        ];
    }
}
