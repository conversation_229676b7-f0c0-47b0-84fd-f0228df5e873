<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "frontend_template_lang".
 *
 * @property int $tpl_id
 * @property int $game_info_id
 * @property string $game_info_type
 */
class FrontendTemplateToGameInfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'frontend_template_to_game_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tpl_id', 'game_info_id', 'game_info_type'], 'required'],
            [['game_info_type'], 'string'],
            [['tpl_id', 'game_info_id'], 'integer']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'tpl_id' => 'Tpl ID',
            'game_info_id' => 'Game Info ID',
            'game_info_type' => 'Game Info Type'
        ];
    }
}
