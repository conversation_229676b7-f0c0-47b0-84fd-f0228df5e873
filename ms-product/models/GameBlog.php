<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_blog".
 *
 * @property string $game_blog_id
 * @property int $sort_order
 */
class GameBlog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_blog';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sort_order'], 'required'],
            [['sort_order'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_blog_id' => 'Game Blog ID',
            'sort_order' => 'Sort Order',
        ];
    }

    public function getGameBlogDescription()
    {
        return $this->hasMany(GameBlogDescription::class, ['game_blog_id' => 'game_blog_id']);
    }

    public function getDefaultGameBlogDescription()
    {
        return $this->getGameBlogDescription()->where(['language_id' => 1]);
    }


    public static function getMetaKeyword($game_blog_id, $language_id = false, $default = false)
    {
        $output = SearchKeywords::getMetaKeyword($game_blog_id, 2, $language_id, $default);
        return $output;
    }

    public static function getMetaDescription($game_blog_id, $language_id = false, $default = false)
    {
        return FrontendTemplate::getTemplateDescription($game_blog_id, 2, $language_id = false, $default = false);
    }

}
