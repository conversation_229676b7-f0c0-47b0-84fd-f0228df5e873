<?php

namespace micro\models;

use micro\components\GeneralCom;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class GameProduct extends GameProductBase
{
    const PRODUCT_ATTRIBUTE_FIELD = array('language', 'genre', 'feature', 'platform', 'region');

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    public static function getProductAttributeList()
    {
        $data = GameProductAttributeTranslation::find()->alias('gpat')->select('gpat.game_product_attribute_id, gpat.value as value, gpa.type')->joinWith('gameProductAttribute gpa')->where(['language_id' => 1])->asArray()->all();
        return ArrayHelper::map($data, 'game_product_attribute_id', 'value', 'type');
    }

    public static function getGameProductIdByProductId($id)
    {
        return self::findOne(['products_id' => $id])->game_product_id;
    }

    public static function get($id)
    {
        $model = self::find()->select([
            self::tableName() . ".*",
            'p.products_url_alias',
            'p.products_date_available',
            'p.products_price',
            'p.products_base_currency',
            'gpp.status',
            'gpp.game_publisher_id',
            'gpp.cost_price'
        ])->where([self::tableName() . '.game_product_id' => $id])
            ->joinWith('products p', false)
            ->joinWith('gamePublisherProduct gpp', false)
            ->joinWith('gameProductDescriptions')
            ->joinWith('productsDescriptions')
            ->asArray()->one();

        $model['gameProductLanguages'] = GameProductLanguage::find()->where(['game_product_id' => $model['game_product_id']])->joinWith(
            'gameProductAttribute',
            false
        )->orderBy('game_product_attribute.sort_order')->asArray()->all();

        $model['gameProductGenres'] = GameProductGenre::find()->where(['game_product_id' => $model['game_product_id']])->joinWith(
            'gameProductAttribute',
            false
        )->orderBy('game_product_attribute.sort_order')->asArray()->all();

        $model['gameProductFeatures'] = GameProductFeature::find()->where(['game_product_id' => $model['game_product_id']])->joinWith(
            'gameProductAttribute',
            false
        )->orderBy('game_product_attribute.sort_order')->asArray()->all();

        $model['gameProductPlatforms'] = GameProductPlatform::find()->where(['game_product_id' => $model['game_product_id']])->joinWith(
            'gameProductAttribute',
            false
        )->orderBy('game_product_attribute.sort_order')->asArray()->all();

        $model['gameProductRegions'] = GameProductRegion::find()->where(['game_product_id' => $model['game_product_id']])->joinWith(
            'gameProductAttribute',
            false
        )->orderBy('game_product_attribute.sort_order')->asArray()->all();

        $return_array = [
            'id' => $model['game_product_id'],
            'game_publisher_id' => $model['game_publisher_id'],
            'release_date' => date_format(date_create($model['products_date_available']), "Y-m-d"),
            'product_price' => $model['products_price'],
            'product_currency' => $model['products_base_currency'],
            'products_id' => $model['products_id'],
            'url_alias' => $model['products_url_alias'],
            'status' => $model['status'],
            'cost_price' => $model['cost_price'],
            'genre' => [],
            'feature' => [],
            'language' => [],
            'platform' => [],
            'region' => [],
            'attribute' => self::getProductAttributeList()
        ];

        foreach ($model['gameProductDescriptions'] as $description) {
            $item = array();
            $item['description'] = $description['description'];
            $item['notice'] = $description['notice'];
            $item['header_image_url'] = $description['header_image_url'];
            $item['header_image_title'] = $description['header_image_title'];
            $item['background_image_url'] = $description['background_image_url'];
            $return_array[$description['language_id']] = $item;
        }

        foreach ($model['productsDescriptions'] as $description) {
            if (isset($return_array[$description['language_id']])) {
                $item = $return_array[$description['language_id']];
            } else {
                $item = array();
            }
            $item['title'] = $description['products_name'];
            $item['product_image_url'] = $description['products_image'];
            $item['product_image_title'] = utf8_encode($description['products_image_title']);
            $return_array[$description['language_id']] = $item;
        }

        foreach ($model['gameProductLanguages'] as $description) {
            $return_array['language'][] = $description['value'];
        }

        foreach ($model['gameProductPlatforms'] as $description) {
            $return_array['platform'][] = $description['value'];
        }

        foreach ($model['gameProductGenres'] as $description) {
            $return_array['genre'][] = $description['value'];
        }

        foreach ($model['gameProductRegions'] as $description) {
            $return_array['region'][] = $description['value'];
        }

        foreach ($model['gameProductFeatures'] as $description) {
            $return_array['feature'][] = $description['value'];
        }

        return $return_array;
    }

    private static function checkEmpty($value, $default = '')
    {
        return (!empty($value) ? $value : $default);
    }

    protected static function findModel($className, $filter)
    {
        if (($model = $className::findOne($filter)) !== null) {
            return $model;
        } else {
            return (new $className());
        }
    }

    public static function updateGameProduct($input)
    {
        $error_array = [];
        $dbTransaction = Yii::$app->db->beginTransaction();

        $data = self::get($input['id']);
        $attribute_field = self::PRODUCT_ATTRIBUTE_FIELD;

        $model = self::findOne(['game_product_id' => $input['id']]);
        $model->updated_at = date('Y-m-d H:i:s');

        $gpp_model = $model->gamePublisherProduct;

        $p_model = Products::findOne(['products_id' => $model->products_id]);

        $p_model->products_date_available = $input['release_date'];

        if (!empty($input['price'])) {
            $p_model->products_price = (float)$input['price'];
            $gpp_model->setSellingPrice($input['price']);
            $gpp_model->mark_up = $gpp_model->actual_mark_up;
        }

        if (!empty($input['status'])) {
            $gpp_model->status = (int)$input['status'];
        }

        $status = ($gpp_model->status == 1 ? 1 : 0);
        $p_model->products_status = $status;
        $p_model->products_display = $status;

        foreach (Yii::$app->params['supported.language'] as $l_index => $l_value) {
            $gpd_model = self::findModel(GameProductDescription::class, [
                'game_product_id' => $input['id'],
                'language_id' => $l_index
            ]);

            $pd_model = self::findModel(ProductsDescription::class, [
                'products_id' => $data['products_id'],
                'language_id' => $l_index
            ]);

            $game_description_field = array(
                'description',
                'notice',
                'header_image_url',
                'header_image_title',
                'background_image_url'
            );

            foreach ($game_description_field as $field_name) {
                if (!empty($input[$field_name][$l_index]) || !empty($gpd_model->$field_name)) {
                    $gpd_model->$field_name = self::checkEmpty($input[$field_name][$l_index], $input[$field_name][1]);
                }
            }
            $gpd_model->language_id = $l_index;
            $gpd_model->game_product_id = $input['id'];
            $gpd_model->save();

            if (!empty($input['title']) || !empty($input['product_image_url']) && !empty($input['product_image_title'])) {
                $pd_model->products_image = self::checkEmpty(
                    $input['product_image_url'][$l_index],
                    $input['product_image_url'][1]
                );
                $pd_model->products_image_title = self::checkEmpty(
                    $input['product_image_title'][$l_index],
                    $input['product_image_title'][1]
                );
                $pd_model->products_id = $data['products_id'];
                $pd_model->products_name = self::checkEmpty(
                    $input['title'][$l_index],
                    $input['title'][1]
                );
                $pd_model->language_id = $l_index;
                $pd_model->save();
            }
        }

        foreach ($attribute_field as $attribute) {
            $_class = 'micro\models\GameProduct' . ucfirst($attribute);

            if (empty($input[$attribute])) {
                $input[$attribute] = array();
            }
            $new_attribute = $input[$attribute];
            $insert_val = array_diff($new_attribute, $data[$attribute]);
            $del_val = array_diff($data[$attribute], $new_attribute);

            if (!empty($del_val)) {
                $_class::deleteAll(['and', ['game_product_id' => $data['id']], ['in', 'value', $del_val]]);
            }

            if (!empty($insert_val)) {
                foreach ($insert_val as $val) {
                    $gpa_model = new $_class();
                    $gpa_model->value = $val;
                    $gpa_model->game_product_id = $input['id'];
                    $gpa_model->save();
                }
            }
        }

        $gpp_model->save();
        $p_model->save();
        $model->save();

        $model_list = [
            $gpp_model,
            $model,
            $p_model,
        ];

        foreach ($model_list as $m) {
            $err = $m->getErrors();
            if ($err) {
                $error_array[] = $m->getErrors();
            }
        }

        if (!empty($error_array)) {
            $dbTransaction->rollBack();
            Yii::$app->slack->send('Game Product Creation Error', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($error_array)
                )
            ), 'DEBUG');
            return false;
        }
        $dbTransaction->commit();
        $model->updateAlgolia();
        return ['game_publisher_id' => $gpp_model->game_publisher_id, 'status' => $gpp_model->status];
    }

    public static function patchGameProductCategoryPath()
    {
        $g_product_list = self::find()->select('products_id')->where(['>', 'products_id', 0])->asArray()->all();

        $product_list = Products::find()->select(['products_id', 'products_cat_id_path'])->where(['products_id' => ArrayHelper::getColumn($g_product_list, 'products_id')])->asArray()->all();

        foreach ($product_list as $product) {
            $cat_path = explode("_", trim($product['products_cat_id_path'], ' _'));
            $cat_id = end($cat_path);
            list($categories_path, $category_str) = Categories::getCatPathByCatId($cat_id);
            Products::updateAll(['products_cat_path' => $category_str], ['products_id' => $product['products_id']]);
        }
    }

    public static function patchReleaseDate()
    {
        $g_product_list = self::find()->select([self::tableName() . '.products_id', GamePublisherProduct::tableName() . '.json_raw'])->where(['>', self::tableName() . '.products_id', 0])->joinWith('gamePublisherProduct', false)->asArray(
        )->all();

        $map_value = ArrayHelper::map($g_product_list, 'products_id', 'json_raw');

        $product_list = Products::find()->select(['products_id'])->where(['products_id' => ArrayHelper::getColumn($g_product_list, 'products_id')])->asArray()->all();

        foreach ($product_list as $product) {
            $raw_data = Json::decode($map_value[$product['products_id']]);
            Products::updateAll(['products_date_available' => $raw_data['release_date']], ['products_id' => $product['products_id']]);
        }
    }

    public static function patchSellingPrice()
    {
        $g_product_list = self::find()->select([self::tableName() . '.products_id', GamePublisherProduct::tableName() . '.json_raw', GamePublisherProduct::tableName() . '.game_publisher_product_id'])->where(
            ['>', self::tableName() . '.products_id', 0]
        )->joinWith('gamePublisherProduct', false)->asArray()->all();

        $map_value = ArrayHelper::index($g_product_list, 'products_id');

        $product_list = Products::find()->select(['products_id', 'products_price'])->where(['products_id' => ArrayHelper::getColumn($g_product_list, 'products_id')])->asArray()->all();

        foreach ($product_list as $product) {
            $raw_data = Json::decode($map_value[$product['products_id']]['json_raw']);
            $margin = ($raw_data['cost_price'] > 0 ? number_format(($product['products_price'] / $raw_data['cost_price'] * 100) - 100, 2) : 0);
            GamePublisherProduct::updateAll(['selling_price' => $product['products_price'], 'selling_currency' => $raw_data['cost_currency'], 'actual_mark_up' => $margin],
                ['game_publisher_product_id' => $map_value[$product['products_id']]['game_publisher_product_id']]);
        }
    }

    public static function getMetaDescription($products_id)
    {
        $data = self::find()->alias('gp')->select(['gp.game_product_id', 'gpd.language_id', 'gpd.description'])->where(['products_id' => $products_id])->joinWith('gameProductDescriptions gpd')->asArray()->one();
        $return_arr = [];
        if (!empty($data['gameProductDescriptions'])) {
            foreach ($data['gameProductDescriptions'] as $description) {
                $return_arr[$description['language_id']] = FrontendTemplate::getFirstParagraph($description['description'], '<br>');
            }
        }

        return $return_arr;
    }


    public function getFirstChar($str)
    {
        $str = mb_substr($str, 0, 1);

        if (mb_strlen($str) == strlen($str)) {
        } else {
            $str = mb_substr(Yii::$app->pinyin->abbr($str), 0, 1);
        }
        if (ctype_alpha($str)) {
            $return_str = $str;
        } else {
            $return_str = '#';
        }

        return strtolower($return_str);
    }


    public function updateAlgoliaContent()
    {
        $data = self::get($this->game_product_id);

        $url = '/buynow/' . $data['url_alias'] . '.html';

        $title = (!empty($data[1]['title']) ? $data[1]['title'] : '');

        $output = [
            'name' => $title,
            'name_cn' => (!empty($data[2]['title']) ? $data[2]['title'] : $title),
            'name_id' => (!empty($data[4]['title']) ? $data[4]['title'] : $title),
            'type' => 'game_product',
            'url' => $url,
            'allowed_country' => ['*'],
            'ip_allowed_country' => ['*'],
            'objectID' => 'p-' . $data['products_id'],
        ];

        $output = array_merge($output, [
            'sort_en' => $this->getFirstChar($output['name']),
            'sort_id' => $this->getFirstChar($output['name_id']),
            'sort_sc' => $this->getFirstChar($output['name_cn']),
            'pic_url' => (!empty($data[1]['header_image_url']) ? $data[1]['header_image_url'] : ''),
        ]);

        $p_output = array_merge($output, [
            'price' => (is_numeric($data['product_price']) ? (float)$data['product_price'] : $data['product_price']),
            'genre' => $data['genre'],
            'feature' => $data['feature'],
            'language' => $data['language'],
            'platform' => $data['platform'],
            'region' => $data['region'],
            'release_date' => strtotime($data['release_date'])
        ]);

        $data = [
            'objectID' => 'p-' . $data['products_id'],
            'name' => $title,
            'name_cn' => (!empty($data[2]['title']) ? $data[2]['title'] : $title),
            'name_id' => (!empty($data[4]['title']) ? $data[4]['title'] : $title),
            'type' => ['game_key'],
            'image_url' => (!empty($data[1]['header_image_url']) ? $data[1]['header_image_url'] : ''),
            'url_alias' => $url,
            'blocked_country' => [],
            'ip_blocked_country' => []
        ];

        $algolia = Yii::$app->algolia;

        $hash = md5(Json::encode($p_output));

        if ($hash !== $this->algolia_hash) {
            $algolia->setBatchQuery('update', 'product', $p_output);
            $this->algolia_hash = $hash;
            $this->save();
        }

        $hash = md5(Json::encode($data));
        $model = Algolia::findOne(['object_id' => 'p-' . $this->products_id]);

        if (!$model) {
            $model = new Algolia();
            $model->object_id = 'p-' . $this->products_id;
        }

        if ($model->hash != $hash) {
            $algolia->setBatchQuery('update', 'brand', $data);
            $model->hash = $hash;
            $model->save();
        }
    }


    public function deleteFromAlgolia($id)
    {
        $algolia = Yii::$app->algolia;
        $algolia->setBatchQuery('delete', 'brand', $id);
        $algolia->setBatchQuery('delete', 'product', $id);

        if ($model = Algolia::findOne(['object_id' => 'p-' . $id])) {
            $model->delete();
        }

        $this->algolia_hash = '';
        $this->save();
    }

    public function updateAlgolia()
    {
        try {
            if ($this->products->products_status != 1) {
                if (!empty($this->algolia_hash)) {
                    $this->deleteFromAlgolia('p-' . $this->products_id);
                }
            } else {
                $this->updateAlgoliaContent();
            }
        } catch (\Exception $e) {
            //TODO Temporary Solution, Should have proper handler when algolia update / delete failed
            GeneralCom::slackStackNotification('debug', 'Products ID : ' . $this->products->products_id . ' - Posting to Algolia Failed with exception : ' . $e->getMessage(), 'warning');
        }
    }

}
