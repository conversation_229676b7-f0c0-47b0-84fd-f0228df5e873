<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_product_attribute".
 *
 * @property int $game_product_attribute_id
 * @property int $sort_order
 * @property int $type
 *
 * @property GameProductAttributeTranslation[] $gameProductAttributeTranslations
 */
class GameProductAttribute extends GameProductAttributeBase
{
    const TYPE_LANGUAGE = 1;
    const TYPE_GENRE = 2;
    const TYPE_FEATURE = 3;
    const TYPE_PLATFORM = 4;
    const TYPE_REGION = 5;

    const TYPE = array(
        1 => 'Language',
        2 => 'Genre',
        3 => 'Feature',
        4 => 'Platform',
        5 => 'Region'
    );

    public function rules()
    {
        return [
            [['type'], 'integer'],
        ];
    }

    public static function getAttributeWithDefaultMessage($params, $page = 1, $limit = 10)
    {
        $searchModel = new self();

        $t_name = self::tableName();
        $searchModel->load($params, '');
        $query = self::find();

        $query->andFilterWhere([
            $t_name . '.game_product_attribute_id' => $searchModel->game_product_attribute_id,
            $t_name . '.type' => $searchModel->type,
        ]);

        $query->joinWith('defaultMessage');

        $query->andFilterWhere([
            'LIKE',
            GameProductAttributeTranslation::tableName() . '.value',
            (isset($params['description']) ? $params['description'] : null)
        ]);

        $query->orderBy($t_name.'.type');

        $query->addOrderBy($t_name.'.sort_order');

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [];
            $item['id'] = $obj['game_product_attribute_id'];
            $item['type'] = (self::TYPE[$obj['type']] ?? 0);
            $item['description'] = (isset($obj['defaultMessage']['value']) ? $obj['defaultMessage']['value'] : '');
            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];
        return ($return_array);
    }

    public static function get($id)
    {
        $model = self::find()->where([self::tableName() . '.game_product_attribute_id' => $id])->joinWith('allMessage')->asArray()->one();

        $return_array = [
            'id' => $model['game_product_attribute_id'],
            'type' => self::TYPE[$model['type']],
            'type_id' => $model['type'],
            'sort_order' => $model['sort_order'],
            'description' => []
        ];

        foreach ($model['allMessage'] as $message) {
            $return_array['description'][$message['language_id']] = $message['value'];
        }

        return $return_array;
    }

    public static function getNextSortOrder($type)
    {
        $query = new \yii\db\Query;
        $query->select('max(sort_order)')
            ->from(self::tableName())
            ->where(['type' => $type]);
        $command = $query->createCommand();
        $num = (floor($command->queryScalar() / 100) * 100) + 100;
        return $num;
    }

    public function getAllMessage()
    {
        return $this->hasMany(GameProductAttributeTranslation::class,
            ['game_product_attribute_id' => 'game_product_attribute_id']);
    }

    public function getDefaultMessage()
    {
        return $this->hasOne(GameProductAttributeTranslation::class,
            ['game_product_attribute_id' => 'game_product_attribute_id'])->andOnCondition(['language_id' => 1]);
    }

}
