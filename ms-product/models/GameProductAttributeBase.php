<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_product_attribute".
 *
 * @property int $game_product_attribute_id
 * @property int $sort_order
 * @property int $type
 *
 * @property GameProductAttributeTranslation[] $gameProductAttributeTranslations
 */
class GameProductAttributeBase extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_product_attribute';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type','sort_order'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_product_attribute_id' => 'Game Product Attribute ID',
            'type' => 'Type',
            'sort_order' => 'Sort Order',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductAttributeTranslations()
    {
        return $this->hasMany(GameProductAttributeTranslation::className(),
            ['game_product_attribute_id' => 'game_product_attribute_id']);
    }

}
