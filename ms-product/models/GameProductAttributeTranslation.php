<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_product_attribute_translation".
 *
 * @property int $id
 * @property int $game_product_attribute_id
 * @property int $language_id
 * @property string $value
 *
 * @property GameProductAttribute $gameProductAttribute
 */
class GameProductAttributeTranslation extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_product_attribute_translation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_product_attribute_id', 'language_id'], 'integer'],
            [['value'], 'string', 'max' => 255],
            [
                ['game_product_attribute_id'],
                'exist',
                'skipOnError' => true,
                'targetClass' => GameProductAttribute::className(),
                'targetAttribute' => ['game_product_attribute_id' => 'game_product_attribute_id']
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_product_attribute_id' => 'Game Product Attribute ID',
            'language_id' => 'Language ID',
            'value' => 'Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductAttribute()
    {
        return $this->hasOne(GameProductAttribute::className(),
            ['game_product_attribute_id' => 'game_product_attribute_id']);
    }

    public static function getProductAttributeList()
    {
        $data = GameProductAttributeTranslation::find()->alias('gpat')->select('gpat.game_product_attribute_id, gpat.value as value, gpa.type')->joinWith('gameProductAttribute gpa')->where(['language_id' => 1])->orderBy('gpa.sort_order')->asArray()->all();
        return $data;
    }

    public static function getProductAttributeWithTranslation()
    {
        $data = GameProductAttributeTranslation::find()->alias('gpat')->select('gpat.game_product_attribute_id, gpat.value as value, gpa.type,gpat.language_id')->joinWith('gameProductAttribute gpa')->orderBy('gpa.sort_order')->asArray()->all();
        return $data;
    }

}
