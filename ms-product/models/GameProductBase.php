<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_product".
 *
 * @property int $game_product_id
 * @property int $products_id
 * @property double $product_price
 * @property string $product_currency
 * @property string $created_at
 * @property string $updated_at
 * @property string $algolia_hash
 *
 * @property Products $products
 * @property GameProductDescription[] $gameProductDescriptions
 * @property GameProductFeature[] $gameProductFeatures
 * @property GameProductGenre[] $gameProductGenres
 * @property GameProductLanguage[] $gameProductLanguages
 * @property GameProductPlatform[] $gameProductPlatforms
 * @property GameProductRegion[] $gameProductRegions
 * @property GamePublisherProduct $gamePublisherProduct
 */
class GameProductBase extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_product';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['products_id'], 'exist', 'skipOnError' => true, 'targetClass' => Products::className(), 'targetAttribute' => ['products_id' => 'products_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_product_id' => 'Game Product ID',
            'products_id' => 'Products ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducts()
    {
        return $this->hasOne(Products::className(), ['products_id' => 'products_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProductsDescriptions()
    {
        return $this->hasMany(ProductsDescription::className(), ['products_id' => 'products_id'])->viaTable(Products::tableName(), ['products_id' => 'products_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductDescriptions()
    {
        return $this->hasMany(GameProductDescription::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductFeatures()
    {
        return $this->hasMany(GameProductFeature::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductGenres()
    {
        return $this->hasMany(GameProductGenre::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductLanguages()
    {
        return $this->hasMany(GameProductLanguage::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductPlatforms()
    {
        return $this->hasMany(GameProductPlatform::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductRegions()
    {
        return $this->hasMany(GameProductRegion::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGamePublisherProduct()
    {
        return $this->hasOne(GamePublisherProduct::className(), ['game_product_id' => 'game_product_id']);
    }
}
