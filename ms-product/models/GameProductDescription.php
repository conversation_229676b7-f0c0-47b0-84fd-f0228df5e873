<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use offgamers\base\models\enum\ModelAuditActionEnum;

/**
 * This is the model class for table "game_product_description".
 *
 * @property int $id
 * @property int $game_product_id
 * @property int $language_id
 * @property string $description
 * @property string $notice
 * @property string $created_at
 * @property string $updated_at
 * @property string $header_image_url
 * @property string $header_image_title
 * @property string $background_image_url
 *
 * @property GameProduct $gameProduct
 */
class GameProductDescription extends \offgamers\base\models\ActiveRecord
{
    protected $log_data_id;

    const LOG_TABLE = 'game_publisher_product';
    const INSERT_LOG_DESCRIPTION = '%s';
    const UPDATE_LOG_DESCRIPTION = '%s';
    const DELETE_LOG_DESCRIPTION = '%s';

    /**
     * {@inheritdoc}
     */

    public static function tableName()
    {
        return 'game_product_description';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_product_id', 'language_id'], 'integer'],
            [['description', 'notice'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['header_image_url', 'header_image_title', 'background_image_url'], 'string', 'max' => 255],
            [['game_product_id'], 'exist', 'skipOnError' => true, 'targetClass' => GameProduct::className(), 'targetAttribute' => ['game_product_id' => 'game_product_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_product_id' => 'Game Product ID',
            'language_id' => 'Language ID',
            'description' => 'Description',
            'notice' => 'Notice',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'header_image_url' => 'Header Image Url',
            'header_image_title' => 'Header Image Title',
            'background_image_url' => 'Background Image Url',
        ];
    }

    protected function getDataId()
    {
        if (!$this->log_data_id) {
            $gp_model = GamePublisherProduct::find()->select(['game_publisher_product_id'])->where(['game_product_id' => $this->game_product_id])->one();
            if ($gp_model) {
                $this->log_data_id = $gp_model->game_publisher_product_id;
            }
        }
        return $this->log_data_id;
    }

    protected function getRequiredField()
    {
        $this->getDataId();
        return ['language_id' => $this->language_id, 'game_publisher_product_id' => $this->log_data_id];
    }

    protected function getUpdateField()
    {
        return ['description','notice'];
    }

    protected function isLogRequired($action)
    {
        $ret = false;

        $this->getDataId();

        switch ($action) {
            case ModelAuditActionEnum::UPDATE:
                $ret = true;
                break;

            default:
                break;
        }

        return $ret;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProduct()
    {
        return $this->hasOne(GameProduct::className(), ['game_product_id' => 'game_product_id']);
    }
}
