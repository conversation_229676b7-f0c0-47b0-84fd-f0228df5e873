<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "game_product_language".
 *
 * @property int $id
 * @property int $game_product_id
 * @property int $value
 *
 * @property GameProduct $gameProduct
 * @property GameProductAttribute $gameProductAttribute
 */
class GameProductLanguage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_product_language';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_product_id', 'value'], 'integer'],
            [['game_product_id'], 'exist', 'skipOnError' => true, 'targetClass' => GameProduct::className(), 'targetAttribute' => ['game_product_id' => 'game_product_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_product_id' => 'Game Product ID',
            'value' => 'Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProduct()
    {
        return $this->hasOne(GameProduct::className(), ['game_product_id' => 'game_product_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProductAttribute()
    {
        return $this->hasOne(GameProductAttribute::className(), ['game_product_attribute_id' => 'value']);
    }
}
