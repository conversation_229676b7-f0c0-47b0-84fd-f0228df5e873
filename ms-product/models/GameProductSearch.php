<?php

namespace micro\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * GameProductSearch represents the model behind the search form of `micro\models;GameProduct`.
 */
class GameProductSearch extends GameProduct
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_product_id', 'products_id', 'out_of_stock'], 'integer'],
            [['product_currency', 'created_at', 'updated_at'], 'safe'],
            [['product_price'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = GameProduct::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'game_product_id' => $this->game_product_id,
            'products_id' => $this->products_id,
            'out_of_stock' => $this->out_of_stock,
            'product_price' => $this->product_price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'product_currency', $this->product_currency]);

        return $dataProvider;
    }
}
