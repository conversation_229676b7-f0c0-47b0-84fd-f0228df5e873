<?php

namespace micro\models;

use http\Exception\InvalidArgumentException;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use micro\components\GeneralCom;

class GamePublisherProduct extends GamePublisherProductBase
{
    const STATUS = array(
        1 => 'Active',
        0 => 'Pending',
        3 => 'Inactive',
        4 => 'Modified',
        5 => 'Excluded',
        6 => 'REMOVED'
    );

    const PRODUCT_TYPE = array('2' => 'Game Product', '3' => 'Mobile Reload', '1' => 'CD Key');

    public $changed_by = '';

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    public function __construct()
    {
        $data = Yii::$app->getRequest()->getBodyParam('data');
        if (isset($data['changed_by'])) {
            $this->changed_by = $data['changed_by'];
            Yii::$app->params['_user_identifier'] = $this->changed_by;
        }
    }

    public function getCategories()
    {
        return $this->hasMany(Categories::class, ['categories_id' => 'categories_id'])
            ->viaTable(ProductsToCategories::tableName(), ['products_id' => 'products_id']);
    }

    public function getProduct()
    {
        return $this->hasOne(Products::class, ['products_id' => 'products_id'])
            ->viaTable(GameProduct::tableName(), ['game_product_id' => 'game_product_id']);
    }

    public static function getGamePublisherProduct($params, $page = 1, $limit = 10)
    {
        $searchModel = new self();

        $searchModel->load($params, '');
        $query = self::find()->alias('gpp')->select(['p.*', 'gpp.*', 'gp.*'])->joinWith('gameProduct gp',
            false)->joinWith('product p', false);

        $query->andFilterWhere([
            'game_publisher_product_id' => $searchModel->game_publisher_product_id,
            'game_publisher_id' => $searchModel->game_publisher_id,
            'game_product_id' => $searchModel->game_product_id,
            'status' => $searchModel->status,
            'cost_price' => $searchModel->cost_price,
            'created_at' => $searchModel->created_at,
            'updated_at' => $searchModel->updated_at,
        ]);

        $query->andFilterWhere(['like', 'title', $searchModel->title])
            ->andFilterWhere(['like', 'publisher_reference_id', $searchModel->publisher_reference_id])
            ->andFilterWhere(['like', 'cost_currency', $searchModel->cost_currency]);

        if (!empty($params['flag'])) {
            switch ($params['flag']) {
                case 'normal':
                    $query->andFilterWhere(['out_of_stock_flag' => 0, 'pre_order_flag' => 0, 'is_duplicate_flag' => 0]);
                    break;
                case 'out_of_stock':
                    $query->andFilterWhere(['out_of_stock_flag' => 1]);
                    break;

                case 'pre_order':
                    $query->andFilterWhere(['pre_order_flag' => 1]);
                    break;

                case 'is_duplicate':
                    $query->andFilterWhere(['is_duplicate_flag' => 1]);
                    break;
            }
        }

        if (isset($params['actual_mark_up']) && $params['actual_mark_up'] !== "") {
            switch ($params['actual_mark_up']) {
                //<3%
                case 0:
                    $query->andFilterWhere(['<', 'actual_mark_up', 3]);
                    break;
                //>=3% & < 9%
                case 1:
                    $query->andFilterWhere(['>=', 'actual_mark_up', 3]);
                    $query->andFilterWhere(['<', 'actual_mark_up', 9]);
                    break;
                //>=9% & < 12 %
                case 2:
                    $query->andFilterWhere(['>=', 'actual_mark_up', 9]);
                    $query->andFilterWhere(['<', 'actual_mark_up', 12]);
                    break;
                //>=12% & < 15%
                case 3:
                    $query->andFilterWhere(['>=', 'actual_mark_up', 12]);
                    $query->andFilterWhere(['<', 'actual_mark_up', 15]);
                    break;
                //>=15%
                case 4:
                    $query->andFilterWhere(['>=', 'actual_mark_up', 15]);
                    break;

            }
        }

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [];
            $item['id'] = $obj['game_publisher_product_id'];
            $item['title'] = $obj['title'];
            $item['status'] = self::STATUS[$obj['status']];
            $item['cost_currency'] = $obj['cost_currency'];
            $item['cost_price'] = $obj['cost_price'];
            $item['product_currency'] = $obj['products_base_currency'];
            $item['product_price'] = $obj['products_price'];
            $item['game_product_id'] = $obj['game_product_id'];
            $item['is_duplicate_flag'] = $obj['is_duplicate_flag'];
            $item['out_of_stock_flag'] = $obj['out_of_stock_flag'];
            $item['pre_order_flag'] = $obj['pre_order_flag'];
            $item['actual_mark_up'] = $obj['actual_mark_up'];
            $item['url_alias'] = $obj['url_alias'];
            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data
        ];

        return ($return_array);
    }


    public static function create($data)
    {
        $model = new self();
        $model->title = $data['title'];
        $model->cost_currency = $data['cost_currency'];
        $model->cost_price = $data['cost_price'];
        $model->game_publisher_id = $data['game_publisher_id'];
        $model->publisher_reference_id = $data['publisher_reference_id'];
        $model->url_alias = $data['url_alias'];
        $model->status = 0;
        $model->pre_order_flag = ($data['pre_order_flag'] ? 1 : 0);
        $model->out_of_stock_flag = ($data['out_of_stock_flag'] ? 1 : 0);
        $model->stock_quantity = $data['stock_quantity'];
        $model->auto_markup = (int)(isset($data['auto_markup']) ? $data['auto_markup'] : 0);
        $find_duplicate = Products::find()->where(['products_url_alias' => $data['url_alias']])->one();
        if (!empty($find_duplicate->products_id)) {
            $model->is_duplicate_flag = 1;
        }
        $model->json_raw = Json::encode($data);
        $model->save();
        if (!empty($model->getErrors())) {
            Yii::$app->slack->send('DEBUG MESSAGE', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($model->getErrors())
                )
            ), 'DEBUG');
        }
        return $model->game_publisher_product_id;
    }

    public static function edit($data)
    {
        $model = self::findOne([
            'publisher_reference_id' => $data['publisher_reference_id'],
            'game_publisher_id' => $data['game_publisher_id']
        ]);

        // Temporary Variable for Missing Postback tracking of codeswholesales
        $is_changed = false;
        $changed_attribute = [];

        try {
            $model->pre_order_flag = ($data['pre_order_flag'] ? 1 : 0);
            $model->out_of_stock_flag = ($data['out_of_stock_flag'] ? 1 : 0);
            $model->stock_quantity = $data['stock_quantity'];
            $model->json_raw = Json::encode($data);
            $model->auto_markup = (int)(isset($data['auto_markup']) ? $data['auto_markup'] : 0);
            $old_cost = $model->cost_price;
            $model->cost_price = $data['cost_price'];

            if ($model->status !== 0 && $model->status !== 5) {
                $p_model = $model->product;
                if ($model->status == 6 && empty($p_model)) {
                    $model->status = 0;
                } else {
                    if ($model->pre_order_flag == 1) {
                        $p_model->products_purchase_mode = 2;
                    } elseif ($model->out_of_stock_flag == 1) {
                        $p_model->products_purchase_mode = 3;
                    } else {
                        $p_model->products_purchase_mode = 1;
                    }

                    // Change restored product status to modified
                    if ($model->status == 6) {
                        $model->status = 4;
                    }

                    if ($model->cost_price != $old_cost) {
                        if (!empty($p_model->products_id)) {
                            $pc_model = $p_model->productsCost;
                            $pc_model->products_cost = $data['cost_price'];
                            $pc_model->save();
                        }

                        if ($model->cost_price > 0 && !empty($model->auto_markup)) {
                            if (empty($model->mark_up)) {
                                $model->mark_up = 10;
                            }
                            $p_model->products_price = (float)round($model->cost_price * (100 + $model->mark_up) / 100, 2);
                            $model->setSellingPrice($p_model->products_price);
                        }
                        elseif($model->out_of_stock_flag == 1){
                            // Keep Products Price & Status, Set to No Stock in frontend
                        }
                        else {
                            $p_model->products_status = 0;
                            $p_model->products_display = 0;
                            $model->status = 4;
                        }
                    }
                    $p_model->save();
                }
            }

            // Temporary Code for Missing Postback tracking of codeswholesales
            $changed_data = $model->getDirtyAttributes();
            $tracked_field = ['out_of_stock_flag', 'cost_price'];
            foreach ($changed_data as $name => $value) {
                if (in_array($name, $tracked_field)) {
                    $changed_attribute['publisher_reference_id'] = $model->publisher_reference_id;
                    $changed_attribute['game_publisher_product_id'] = $model->game_publisher_product_id;
                    if ($model->isAttributeChanged($name, false)) {
                        $is_changed = true;
                        $changed_attribute[$name] = $value . "=>" . $model->getOldAttribute($name);
                    }
                }
            }

            $model->save();
            if (isset($p_model) && !empty($p_model)) {
                $p_model->gameProduct->updateAlgolia();
            }
            if (!empty($model->getErrors())) {
                Yii::$app->slack->send('DEBUG MESSAGE', array(
                    array(
                        'color' => 'warning',
                        'text' => Json::encode(['error' => $model->getErrors(), 'source' => $data])
                    )
                ), 'DEBUG');
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send('DEBUG MESSAGE', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode(['error' => $e->getMessage(), 'source' => $data])
                )
            ), 'DEBUG');
        }
        if ($is_changed) {
            return $changed_attribute;
        }
    }

    public static function disableProduct($data)
    {
        $model = self::findOne([
            'publisher_reference_id' => $data['publisher_reference_id'],
            'game_publisher_id' => $data['game_publisher_id']
        ]);

        $model->markAsDisabled();

        return true;
    }

    public static function disableProductById($id)
    {
        $model = self::findOne([
            'game_publisher_product_id' => $id,
        ]);

        $model->markAsDisabled();

        return true;
    }

    public function markAsDisabled()
    {
        if ($this->status !== 0 && $this->status !== 5) {
            $p_model = $this->product;

            if (!empty($p_model->products_id)) {
                $p_model->products_status = 0;
                $p_model->products_display = 0;
                $p_model->save();
            }

            $gp_model = $this->gameProduct;
            if ($gp_model) {
                $gp_model->updateAlgolia();
            }
        }

        $this->status = 6;
        $this->save();
    }

    public static function batchCreate($data)
    {
        $_product_list = [];
        $_changed_list = [];
        $publisher_id = '';
        foreach ($data['item_list'] as $obj) {
            $model = GamePublisherProduct::findOne([
                'publisher_reference_id' => $obj['publisher_reference_id'],
                'game_publisher_id' => $obj['game_publisher_id']
            ]);
            $publisher_id = $obj['game_publisher_id'];
            if (!empty($model->game_publisher_product_id)) {
                $game_product_id = $model->game_publisher_product_id;
                $changed_item = self::edit($obj);
                if (!empty($data['full_sync']) && $data['full_sync'] == true) {
                    if ($changed_item) {
                        $_changed_list[] = $changed_item;
                    }
                }
            } else {
                $game_product_id = self::create($obj);
            }

            if (!empty($data['full_sync']) && $data['full_sync'] == true) {
                $_product_list[] = $game_product_id;
            }
        }

        if (!empty($data['full_sync']) && $data['full_sync'] == true) {
            $all_product_list = ArrayHelper::getColumn(GamePublisherProduct::find()->select('game_publisher_product_id')->where(['game_publisher_id' => $publisher_id])->andWhere(['!=', 'status', 6])->asArray()->all(), 'game_publisher_product_id');
            $disabled_product_list = array_diff($all_product_list, $_product_list);
            foreach ($disabled_product_list as $id) {
                self::disableProductById($id);
            }
            if (!empty($_changed_list)) {
                \offgamers\base\models\DevDebugLog::generateDebugLog('MISSING POSTBACK', $_changed_list);
            }
        }

        return true;
    }

    public static function updateProduct($gp_id, $input)
    {
        $model = self::findOne($gp_id);
        if (isset($input['status']) && $input['status'] == 5) {
            $model->status = 5;
            $model->save();
        } else {
            if ($model->game_product_id) {
                $model = self::findOne($gp_id);
                $game_product = GameProduct::findOne(['game_product_id' => $model->game_product_id]);
                $product = Products::findOne(['products_id' => $game_product->products_id]);

                $dbTransaction = Yii::$app->db->beginTransaction();
                $error_array = [];
                $product->products_last_modified = date('Y-m-d H:i:s');

                if (!empty($input['mark_up'])) {
                    $product->products_price = (float)round($model->cost_price * (100 + $input['mark_up']) / 100, 2);
                    $model->mark_up = (double)$input['mark_up'];
                    $model->setSellingPrice($product->products_price);
                }

                if (!empty($input['status'])) {
                    $model->status = (int)$input['status'];
                    $status = ($input['status'] == 1 ? 1 : 0);
                    $product->products_status = $status;
                    $product->products_display = $status;
                }

                $product_description = ProductsDescription::findOne([
                    'products_id' => $product->products_id,
                    'language_id' => 1
                ]);

                if (!empty($input['product_image_url'])) {
                    $product_description->products_image = $input['product_image_url'];
                    $product_description->save();
                }

                $game_product_description = GameProductDescription::findOne([
                    'game_product_id' => $game_product->game_product_id,
                    'language_id' => 1
                ]);

                if (!empty($input['header_image_url'])) {
                    $game_product_description->header_image_url = $input['header_image_url'];
                }

                if (!empty($input['background_image_url'])) {
                    $game_product_description->background_image_url = $input['background_image_url'];
                }

                if (!empty($input['description'])) {
                    $game_product_description->description = $input['description'];
                }

                if ($model->pre_order_flag == 1) {
                    $product->products_purchase_mode = 2;
                } elseif ($model->out_of_stock_flag == 1) {
                    $product->products_purchase_mode = 3;
                } else {
                    $product->products_purchase_mode = 1;
                }

                $game_product_description->save();
                $product->save();
                $game_product->save();
                $model->save();

                $model_list = [
                    $model,
                    $game_product,
                    $product,
                    $game_product_description,
                    $product_description,

                ];

                foreach ($model_list as $m) {
                    $err = $m->getErrors();
                    if ($err) {
                        $error_array[] = $m->getErrors();
                    }
                }

                if (!empty($error_array)) {
                    $dbTransaction->rollBack();
                    Yii::$app->slack->send('Game Product Creation Error', array(
                        array(
                            'color' => 'warning',
                            'text' => Json::encode($error_array)
                        )
                    ), 'DEBUG');
                    return;
                }
                $dbTransaction->commit();
                $game_product->updateAlgolia();

            } else {
                $model = self::findOne($gp_id);

                $data = Json::decode($model->json_raw, false);

                $dbTransaction = Yii::$app->db->beginTransaction();
                $error_array = [];
                if (strlen($model->title) <= 64) {
                    $default_title = $model->title;
                } else {
                    $default_title = '';
                    $array = explode(" ", $model->title);
                    foreach ($array as $text) {
                        if (strlen($default_title . $text) > 64) {
                            break;
                        } else {
                            $default_title .= $text;
                        }
                    }
                    $default_title = trim($default_title);
                }

                $category = Categories::findOne(['categories_id' => $input['category_id']]);

                if (!$category->categories_id) {
                    $dbTransaction->rollBack();
                    throw new InvalidArgumentException('Invalid Categories Id');
                }

                $product = new Products();
                $product->products_quantity = null;
                $product->products_actual_quantity = null;
                $product->products_bundle = '';
                $product->products_bundle_dynamic = '';
                $product->products_date_available = $data->release_date;
                $product->products_weight = 0;
                $product->products_skip_inventory = 0;
                $product->products_tax_class_id = 0;
                $product->manufacturers_id = 0;
                $product->products_ordered = 0;
                $product->products_ship_price = 0;
                $product->products_auto_seo = 0;
                $product->products_flag_id = '';
                $product->products_sort_order = 50000;
                $product->custom_products_type_id = 2;
                $product->custom_products_type_child_id = 3;
                $product->products_buyback_price = 0;
                $product->products_buyback_quantity = '';
                $product->products_buyback_price = 0;
                $product->products_quantity_fifo_cost = 0;
                $product->products_actual_quantity_fifo_cost = 0;

                $product->products_model = (!empty($data->model) ? $data->model : '');
                $product->products_quantity_order = -100;

                if ($model->pre_order_flag == 1) {
                    $product->products_purchase_mode = 2;
                } elseif ($model->out_of_stock_flag == 1) {
                    $product->products_purchase_mode = 3;
                } else {
                    $product->products_purchase_mode = 1;
                }

                $product->products_eta = 72;
                $product->products_add_to_cart_msg = 'Instant';
                $product->products_preorder_msg = ($model->pre_order_flag ? $data->release_date : '24 to 72 hrs');
                $product->products_pre_order_level = 0;

                // Mark Up 10% when no mark up data provided (without price adjustment permission)

                if (empty($input['mark_up'])) {
                    $input['mark_up'] = 10;
                }

                $model->mark_up = (int)$input['mark_up'];

                $product->products_price = (float)round($model->cost_price * (100 + $input['mark_up']) / 100, 2);
                $product->products_base_currency = $model->cost_currency;

                $model->setSellingPrice($product->products_price);
                $product->products_out_of_stock_msg = 'Restock In Progress';
                $product->products_out_of_stock_level = -100;

                list($categories_path, $category_str) = Categories::getCatPathByCatId($category->categories_id);

                $product->products_main_cat_id = $categories_path[0];
                $product->products_cat_id_path = '_' . implode('_', $categories_path) . '_';
                $product->products_cat_path = $category_str;
                $product->products_url_alias = $data->url_alias;

                $product->products_type = 2;

                $status = ($input['status'] == 1 ? 1 : 0);
                $product->products_status = $status;
                $product->products_display = $status;

                $product->save();

                if ($product->products_id) {
                    $game_product = new GameProduct();
                    $game_product->products_id = $product->products_id;

                    if ($model->pre_order_flag == 1) {
                        $product->products_purchase_mode = 3;
                    } elseif ($model->out_of_stock_flag == 1) {
                        $product->products_purchase_mode = 4;
                    } else {
                        $product->products_purchase_mode = 2;
                    }

                    $game_product->save();

                    $product_to_categories = new ProductsToCategories();
                    $product_to_categories->products_id = $product->products_id;
                    $product_to_categories->categories_id = $input['category_id'];
                    $product_to_categories->products_is_link = 0;
                    $product_to_categories->save();

                    $product_cost = new ProductsCost();
                    $product_cost->products_id = $product->products_id;
                    $product_cost->products_model = '';
                    $product_cost->products_forex_status = 'off';
                    $product_cost->products_original_cost = $model->cost_price;
                    $product_cost->products_original_currency = $model->cost_currency;
                    $product_cost->products_cost = $model->cost_price;
                    $product_cost->products_currency = $model->cost_currency;
                    $product_cost->products_last_modified = new Expression('NOW()');
                    $product_cost->products_cost_company_code = $input['cost_company'];
                    $product_cost->products_cost_company_name = $input['cost_company_name'];
                    $product_cost->save();

                    $allow_payment_restriction = new ProductsPaymentMethodsRestrictions();
                    $allow_payment_restriction->products_id = $product->products_id;
                    $allow_payment_restriction->created_date = new Expression('NOW()');
                    $allow_payment_restriction->updated_date = new Expression('NOW()');
                    $allow_payment_restriction->restriction_info = '';
                    $allow_payment_restriction->restriction_mode = 'Disallow';
                    $allow_payment_restriction->save();

                    $disallow_payment_restriction = new ProductsPaymentMethodsRestrictions();
                    $disallow_payment_restriction->products_id = $product->products_id;
                    $disallow_payment_restriction->created_date = new Expression('NOW()');
                    $disallow_payment_restriction->updated_date = new Expression('NOW()');
                    $disallow_payment_restriction->restriction_info = '';
                    $disallow_payment_restriction->restriction_mode = 'Disallow';
                    $disallow_payment_restriction->save();

                    $product_description = new ProductsDescription();
                    $product_description->products_id = $product->products_id;
                    $product_description->products_name = $model->title;
                    $product_description->language_id = 1;
                    $product_description->products_dtu_extra_info_1 = '';
                    $product_description->products_dtu_extra_info_2 = '';
                    $product_description->products_dtu_extra_info_3 = '';
                    $product_description->products_image = $input['product_image_url'];
                    $product_description->products_image_title = $default_title;
                    $product_description->save();

                    $attribute = GameProductAttributeTranslation::getProductAttributeList();
                    $attribute_list = ArrayHelper::map($attribute, 'value', 'game_product_attribute_id', 'type');

                    $game_product_description = new GameProductDescription();
                    $game_product_description->game_product_id = $game_product->game_product_id;
                    $game_product_description->header_image_title = $default_title;
                    $game_product_description->header_image_url = $input['header_image_url'];
                    $game_product_description->background_image_url = $input['background_image_url'];
                    $game_product_description->description = $input['description'];
                    $game_product_description->language_id = 1;
                    $game_product_description->save();

                    if (isset($data->language)) {
                        foreach ($data->language as $language) {
                            if (isset($attribute_list[1][$language])) {
                                $game_product_language = new GameProductLanguage();
                                $game_product_language->game_product_id = $game_product->game_product_id;
                                $game_product_language->value = $attribute_list[1][$language];
                                $game_product_language->save();
                            }
                        }
                    }

                    if (isset($data->platform)) {
                        foreach ($data->platform as $platform) {
                            if (isset($attribute_list[4][$platform])) {
                                $game_product_platform = new GameProductPlatform();
                                $game_product_platform->game_product_id = $game_product->game_product_id;
                                $game_product_platform->value = $attribute_list[4][$platform];
                                $game_product_platform->save();
                            }
                        }
                    }

                    if (isset($data->region)) {
                        foreach ($data->region as $region) {
                            if (isset($attribute_list[5][$region])) {
                                $game_product_platform = new GameProductRegion();
                                $game_product_platform->game_product_id = $game_product->game_product_id;
                                $game_product_platform->value = $attribute_list[5][$region];
                                $game_product_platform->save();
                            }
                        }
                    }

                    $model->game_product_id = $game_product->game_product_id;
                    $model->status = (int)$input['status'];
                    $model->save();

                    $model_list = [
                        $model,
                        $game_product,
                        $product_cost,
                        $product_to_categories,
                        $game_product_description,
                        $product_description,
                        $disallow_payment_restriction,
                        $allow_payment_restriction,

                    ];

                    foreach ($model_list as $m) {
                        $err = $m->getErrors();
                        if ($err) {
                            $error_array[] = $m->getErrors();
                        }
                    }
                } else {
                    $error_array[] = $product->getErrors();
                }
                if (!empty($error_array)) {
                    $dbTransaction->rollBack();
                    Yii::$app->slack->send('Game Product Creation Error', array(
                        array(
                            'color' => 'warning',
                            'text' => Json::encode($error_array)
                        )
                    ), 'DEBUG');
                    return;
                }
                $dbTransaction->commit();
                $game_product->updateAlgolia();
            }
        }
    }

    public static function checkDuplicateUrlAlias($url_alias)
    {
        if (!empty(Products::findOne(['products_url_alias' => $url_alias])->products_id)) {
            return false;
        }
        return true;
    }

    public static function set($id, $params)
    {
        $response = [
            'success' => false,
            'message' => ''
        ];

        $model = self::findOne(['game_publisher_product_id' => $id]);
        if (empty($model->game_publisher_product_id)) {
            throw new InvalidArgumentException('Invalid Game Product ID');
        } else {
            foreach ($params as $key => $value) {
                if ($key == 'url_alias') {
                    if (self::checkDuplicateUrlAlias($value)) {
                        $model->is_duplicate_flag = 0;
                    }
                }
                $model->$key = $value;
                $raw = Json::decode($model->json_raw);
                $raw[$key] = $value;
                $model->json_raw = Json::encode($raw);
                $model->save();
            }

            if (empty($model->getErrors())) {
                $response['success'] = true;
                return $response;
            }
        }

        foreach ($model->getErrors() as $key => $error) {
            $response['message'] .= $key . ' => ' . implode(",", $error) . '<br>';
        }
        return $response;
    }

    public static function get($id)
    {
        $model = self::find()->alias('gpp')
            ->select(array('p.*', 'gp.*', 'gpp.*'))
            ->joinWith('gameProduct gp', false)
            ->joinWith('product p', false)
            ->where(array('game_publisher_product_id' => $id))
            ->asArray()->one();

        $return_array = [
            'id' => $model['game_publisher_product_id'],
            'game_publisher_id' => $model['game_publisher_id'],
            'status' => self::STATUS[$model['status']],
            'title' => $model['title'],
            'products_id' => $model['products_id'],
            'url_alias' => $model['url_alias'],
            'publisher_reference_id' => $model['publisher_reference_id'],
            'game_product_id' => $model['game_product_id'],
            'mark_up' => (!empty($model['mark_up']) ? $model['mark_up'] . "%" : null),
            'actual_mark_up' => (!empty($model['actual_mark_up']) ? $model['actual_mark_up'] . "%" : null),
            'cost_price' => $model['cost_price'],
            'cost_currency' => $model['cost_currency'],
            'product_price' => $model['products_price'],
            'product_currency' => $model['products_base_currency'],
            'created_at' => $model['created_at'],
            'updated_at' => $model['updated_at'],
            'json_raw' => $model['json_raw']
        ];


        return $return_array;
    }


    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        $changed_by = $this->changed_by;
        $title = htmlspecialchars($this->title);
        if (!$insert) {
            if (isset($changedAttributes['cost_price']) && $this->status != 0 && $this->status != 5) {
                if ($changedAttributes['cost_price'] != $this->cost_price) {
                    if ($this->cost_price > 0) {
                        if ($changed_by != 'CodesWholeSale') {
                            $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->game_product_id;
                            GeneralCom::slackStackNotification('bdt_notify', "*Product Cost Changed By $changed_by* \n Product : <$url|$title> \n Cost Change : " . $changedAttributes['cost_price'] . " => " . $this->cost_price);
                        }
                    }
                }
                // Doesn't need to report status change
                return;
            }

            if (isset($changedAttributes['out_of_stock_flag']) && $changed_by != 'CodesWholeSale') {
                if ($changedAttributes['out_of_stock_flag'] != $this->out_of_stock_flag && $this->status != 0 && $this->status != 5) {
                    $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->game_product_id;
                    GeneralCom::slackStackNotification('bdt_notify', "*Product Stock Status Changed By $changed_by* \n Product : <$url|$title> \n Stock Change : " . ($changedAttributes['out_of_stock_flag'] == 0 ? 'Available' : 'Out Of Stock') . " => " . ($this->out_of_stock_flag == 0 ? 'Available' : 'Out Of Stock'));
                }
                // Doesn't need to report status change
                return;
            }

            if (isset($changedAttributes['status'])) {
                if ($this->status == 6 && ($changedAttributes['status'] != 0 && $changedAttributes['status'] != 5)) {
                    $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->game_product_id;
                    GeneralCom::slackStackNotification('bdt_notify', "*$changed_by Remove Product From System* \n Product : <$url|$title>");
                }

                if ($changedAttributes['status'] == 1 || $this->status == 1) {
                    $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->game_product_id;
                    GeneralCom::slackStackNotification('bdt_notify', "*Active Product Status Changed By $changed_by* \n Product : <$url|$title> \n Status Change : " . self::STATUS[$changedAttributes['status']] . " => " . self::STATUS[$this->status]);
                }

                if ($changedAttributes['status'] == 6 && $changedAttributes['status'] != $this->status && $this->status != 0) {
                    $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->game_product_id;
                    GeneralCom::slackStackNotification('bdt_notify', "*$changed_by Restore Product To System* \n Product : <$url|$title>");
                }
            }
        }
    }

    public function setSellingPrice($selling_price)
    {
        $this->selling_price = $selling_price;
        $this->actual_mark_up = ($this->cost_price > 0 ? ($selling_price / $this->cost_price * 100) - 100 : 0);;
    }

}