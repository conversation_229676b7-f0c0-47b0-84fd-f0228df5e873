<?php

namespace micro\models;

use Yii;
use offgamers\base\models\enum\ModelAuditActionEnum;

/**
 * This is the model class for table "game_publisher_product".
 *
 * @property int $game_publisher_product_id
 * @property string $title
 * @property int $game_publisher_id
 * @property string $publisher_reference_id
 * @property int $game_product_id
 * @property int $status
 * @property int $out_of_stock_flag
 * @property int $pre_order_flag
 * @property int $stock_quantity
 * @property double $cost_price
 * @property string $cost_currency
 * @property string $created_at
 * @property string $updated_at
 * @property string $json_raw
 * @property int $is_duplicate_flag
 * @property double $mark_up
 * @property int $auto_markup
 * @property string $url_alias
 * @property double $selling_price
 * @property string $selling_currency
 * @property double $actual_mark_up
 *
 * @property GameProduct $gameProduct
 */
class GamePublisherProductBase extends \offgamers\base\models\ActiveRecord
{
    const LOG_TABLE = 'game_publisher_product';
    const INSERT_LOG_DESCRIPTION = '%s';
    const UPDATE_LOG_DESCRIPTION = '%s';
    const DELETE_LOG_DESCRIPTION = '%s';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_publisher_product';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_publisher_id', 'game_product_id', 'status', 'out_of_stock_flag', 'pre_order_flag', 'stock_quantity', 'is_duplicate_flag', 'auto_markup'], 'integer'],
            [['cost_price', 'selling_price', 'mark_up', 'actual_mark_up'], 'number'],
            [['created_at', 'updated_at'], 'safe'],
            [['json_raw'], 'string'],
            [['title', 'publisher_reference_id', 'cost_currency', 'selling_currency', 'url_alias'], 'string', 'max' => 255],
            [['game_product_id'], 'exist', 'skipOnError' => true, 'targetClass' => GameProduct::className(), 'targetAttribute' => ['game_product_id' => 'game_product_id']]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_publisher_product_id' => 'Game Publisher Product ID',
            'title' => 'Title',
            'game_publisher_id' => 'Game Publisher ID',
            'publisher_reference_id' => 'Publisher Reference ID',
            'game_product_id' => 'Game Product ID',
            'status' => 'Status',
            'out_of_stock_flag' => 'Out Of Stock Flag',
            'pre_order_flag' => 'Pre Order Flag',
            'stock_quantity' => 'Stock Quantity',
            'cost_price' => 'Cost Price',
            'cost_currency' => 'Cost Currency',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'json_raw' => 'Json Raw',
            'is_duplicate_flag' => 'Is Duplicate',
            'url_alias' => 'Url Alias',
        ];
    }

    protected function getRequiredField()
    {
        return ['game_publisher_product_id' => $this->game_publisher_product_id];
    }

    protected function getUpdateField()
    {
        return ['status', 'selling_price', 'cost_price'];
    }

    protected function isLogRequired($action)
    {
        $ret = false;
        $old_attributes = $this->getOldAttributes();
        switch ($action) {
            case ModelAuditActionEnum::UPDATE:
                if (($this->status > 0 && $this->status < 5) || (!empty($old_attributes['status']) && $old_attributes['status'] > 0 && $old_attributes['status'] < 5)) {
                    $ret = true;
                }
                break;
            default:
                break;
        }
        return $ret;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProduct()
    {
        return $this->hasOne(GameProduct::className(), ['game_product_id' => 'game_product_id']);
    }

}
