<?php

namespace micro\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * GamePublisherProductSearch represents the model behind the search form of `micro\models;GamePublisherProductBase`.
 */
class GamePublisherProductSearch extends GamePublisherProduct
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_publisher_product_id', 'game_publisher_id', 'game_product_id', 'status'], 'integer'],
            [['title', 'publisher_reference_id', 'cost_currency', 'created_at', 'updated_at'], 'safe'],
            [['cost_price'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = GamePublisherProductBase::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'game_publisher_product_id' => $this->game_publisher_product_id,
            'game_publisher_id' => $this->game_publisher_id,
            'game_product_id' => $this->game_product_id,
            'status' => $this->status,
            'cost_price' => $this->cost_price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        if (!is_null($this->updated_at) && strpos($this->updated_at, ' to ') !== false) {
            list($start_date, $end_date) = explode(' - ', $this->updated_at);
            $query->andFilterWhere(['between', 'updated_at', $start_date, $end_date]);
        }

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'publisher_reference_id', $this->publisher_reference_id])
            ->andFilterWhere(['like', 'cost_currency', $this->cost_currency]);

        return $dataProvider;
    }
}
