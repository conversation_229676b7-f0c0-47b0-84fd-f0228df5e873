<?php

namespace micro\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "game_region_description".
 *
 * @property int $game_region_id
 * @property int $language_id
 * @property string $game_region_description
 */
class GameRegionDescription extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_region_description';
    }

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    public static function getGameRegionDescId(): array
    {
        return Yii::$app->cache->getOrSet('game-region/desc-id', function () {
            return ArrayHelper::map(
                self::find()
                    ->select(['game_region_id', 'game_region_description'])
                    ->where(['language_id' => 1])
                    ->orderBy(['game_region_id' => SORT_ASC])
                    ->asArray()->all(),
                'game_region_id',
                'game_region_description'
            );
        }, 86400);
    }
}
