<?php

namespace micro\models;

use offgamers\base\components\Currency;
use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "mobile_recharge_deno".
 *
 * @property int $deno_id
 * @property int $type
 * @property int $operator_id
 * @property int $publisher_id
 * @property string $cost_currency
 * @property string $cost_price
 * @property string $sales_currency
 * @property string $sales_price
 * @property int $status
 * @property string $publisher_ref_id
 * @property int $mark_up
 * @property int $products_id
 * @property int $created_at
 * @property int $updated_at
 *
 * @property MobileRechargeOperator $operator
 * @property MobileRechargeDenoDescription[] $mobileRechargeDenoDescriptions
 */
class MobileRechargeDeno extends \yii\db\ActiveRecord
{
    public $operator;
    public $title;
    public $description;

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_deno';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }


    public function attributes()
    {
        // add related fields to searchable attributes
        return array_merge(parent::attributes(), ['mobileRechargeDenoDescription.title', 'mobileRechargeOperatorDescription.title', 'mobileRechargePublisher.title']);
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'operator_id', 'publisher_id', 'status', 'created_at', 'updated_at', 'products_id', 'mark_up'], 'integer'],
            [['cost_price', 'sales_price'], 'number'],
            [['cost_currency', 'sales_currency'], 'string', 'max' => 3],
            [['publisher_ref_id'], 'string', 'max' => 255],
            [['mobileRechargeDenoDescription.title', 'mobileRechargeOperatorDescription.title', 'mobileRechargePublisher.title', 'description', 'title', 'operator'], 'safe'],
            [['operator_id'], 'exist', 'skipOnError' => true, 'targetClass' => MobileRechargeOperator::className(), 'targetAttribute' => ['operator_id' => 'operator_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'mobileRechargeDenoDescription.title' => 'Title',
            'mobileRechargeOperatorDescription.title' => 'Operator',
            'mobileRechargePublisher.title' => 'Publisher',
            'deno_id' => 'Deno ID',
            'type' => 'Type',
            'operator_id' => 'Operator ID',
            'publisher_id' => 'Publisher ID',
            'cost_currency' => 'Cost Currency',
            'cost_price' => 'Cost Price',
            'sales_currency' => 'Sales Currency',
            'sales_price' => 'Sales Price',
            'status' => 'Status',
            'publisher_ref_id' => 'Publisher Ref ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public static function get($data)
    {
        $model = self::find()->select(['deno_id', 'type', 'operator_id', 'publisher_id', 'cost_currency', 'cost_price', 'sales_currency', 'sales_price', 'status', 'publisher_ref_id', 'products_id', 'mark_up'])->where(
            ['deno_id' => $data['id']]
        )->asArray()->one();
        if ($model) {
            $description = \micro\models\MobileRechargeDenoDescription::find()->select(['title', 'description', 'language_id'])->where(['deno_id' => $data['id']])->asArray()->all();
            $model['description'] = ArrayHelper::index($description, 'language_id');
        }
        return $model;
    }

    public static function search($params, $page = 1, $limit = 10)
    {
        $searchModel = new self();

        $query = self::find();

        $searchModel->load($params, '');

        $query->joinWith(['mobileRechargeDenoDescription']);

        $query->andFilterWhere(
            ['LIKE', MobileRechargeDenoDescription::tableName() . '.title', $searchModel->title]
        );

        // grid filtering conditions
        $query->andFilterWhere([
            self::tableName() . '.deno_id' => $searchModel->deno_id,
            self::tableName() . '.type' => $searchModel->type,
            self::tableName() . '.operator_id' => $searchModel->operator_id,
            self::tableName() . '.publisher_id' => $searchModel->publisher_id,
            self::tableName() . '.status' => $searchModel->status,
        ]);

        if ($searchModel->operator) {
            $operator_id = MobileRechargeOperatorDescription::find()->select('operator_id')->filterWhere(['LIKE', 'title', $searchModel->operator])->asArray()->all();
            $query->andFilterWhere([
                self::tableName() . '.operator_id' => ArrayHelper::getColumn($operator_id, 'operator_id')
            ]);
        }


        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [
                'id' => $obj['deno_id'],
                'title' => $obj['mobileRechargeDenoDescription']['title'],
                'status' => $obj['status'],
                'type' => $obj['type'],
                'operator' => MobileRechargeOperator::getTitle($obj['operator_id'])
            ];

            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data,
            'params' => $params
        ];

        return ($return_array);
    }

    public function getMobileRechargeDenoDescription()
    {
        return $this->hasOne(MobileRechargeDenoDescription::class, ['deno_id' => 'deno_id'])->andWhere([MobileRechargeDenoDescription::tableName() . '.language_id' => 1]);
    }

    public function getMobileRechargeOperatorDescription()
    {
        return $this->hasOne(MobileRechargeOperatorDescription::class, ['operator_id' => 'operator_id'])->andWhere([MobileRechargeOperatorDescription::tableName() . '.language_id' => 1]);
    }

    public function getOperator()
    {
        return $this->hasOne(MobileRechargeOperator::className(), ['operator_id' => 'operator_id']);
    }

    public function getMobileRechargeDenoDescriptions()
    {
        return $this->hasMany(MobileRechargeDenoDescription::className(), ['deno_id' => 'deno_id']);
    }

    public static function batchProcessDvsList($publisher_id, $currency, $mark_up, $deno_list, $products_id)
    {
        $deno_data = self::find()->select(['publisher_ref_id', 'mark_up', 'cost_price', 'status'])->where(['publisher_id' => $publisher_id])->asArray()->all();

        $data_list = DtoneOgMapping::find()->select(['og_id', 'dtone_id'])->where(['dtone_id' => ArrayHelper::getColumn($deno_data, 'publisher_ref_id'), 'type' => [5]])->asArray()->all();
        $dtone_og_mapping = ArrayHelper::map($data_list, 'dtone_id', 'og_id');
        $data_list = ArrayHelper::getColumn($data_list, 'dtone_id');
        //Clone of data list for checking purpose, to prevent same deno id on multiple operator
        $deno_check = $data_list;

        $operator_mapping = DtoneOgMapping::find()->select(['og_id', 'dtone_id'])->where(['type' => 2])->asArray()->all();
        $operator_mapping = ArrayHelper::map($operator_mapping, 'dtone_id', 'og_id');

        $markup_mapping = ArrayHelper::map($deno_data, 'publisher_ref_id', 'mark_up');
        $cost_mapping = ArrayHelper::map($deno_data, 'publisher_ref_id', 'cost_price');
        $deno_status = ArrayHelper::map($deno_data, 'publisher_ref_id', 'status');

        $deno_title_description = ArrayHelper::index(
            MobileRechargeDenoDescription::find()
                ->select(['deno_id', 'title', 'description'])
                ->where(['deno_id' => array_values($dtone_og_mapping), 'language_id' => 1])
                ->asArray()
                ->all(),
            'deno_id'
        );

        $dtone_mapping_insert = [];
        $deno_insert = [];
        $deno_description_insert = [];
        $reactivate_deno = [];
        $deno_id_to_update_sql = [];
        $title_sql = '';
        $description_sql = '';

        $unsupported_operator_list = [];

        foreach ($deno_list as $deno_data) {
            $time = time();
            $operator_id = (int)$deno_data['operator_id'];
            $deno = (int)$deno_data['deno_id'];

            $publisher_ref_id = $operator_id . '_' . $deno;

            $ids = array_search($publisher_ref_id, $data_list);

            $cost_price = (double)$deno_data['cost_price'];

            if (isset($deno_status[$publisher_ref_id]) && $deno_status[$publisher_ref_id] == 2) {
                $reactivate_deno[] = $publisher_ref_id;
            }

            if (isset($markup_mapping[$publisher_ref_id]) && $markup_mapping[$publisher_ref_id] > 0) {
                $selling_price = $cost_price + round($cost_price * $markup_mapping[$publisher_ref_id] / 100, 2);
            } else {
                $selling_price = $cost_price + round($cost_price * $mark_up / 100, 2);
            }

            $title = $deno_data['name'];
            $description = (empty($deno_data['description']) ? '' : '<ul><li>' . implode('</li><li>', $deno_data['description']) . '</li></ul>');

            if ($ids === false) {
                if (!isset($operator_mapping[$operator_id])) {
                    $unsupported_operator_list[] = $deno_data;
                    continue;
                }

                $deno_insert[] = [$deno_data['type'], $operator_mapping[$operator_id], $publisher_id, $currency, $cost_price, $currency, $selling_price, $publisher_ref_id, 1, $products_id, $time, $time];
                $deno_description_insert[] = [1, html_entity_decode($title), html_entity_decode($description)];
                $dtone_mapping_insert[] = [(string)$publisher_ref_id, 5, ''];
                $data_list[] = $publisher_ref_id;
            } else {
                if (isset($dtone_og_mapping[$publisher_ref_id])) {
                    if ($deno_title_description[$dtone_og_mapping[$publisher_ref_id]]['title'] != $title || $deno_title_description[$dtone_og_mapping[$publisher_ref_id]]['description'] != $description) {
                        $title_sql .= 'WHEN deno_id = ' . $dtone_og_mapping[$publisher_ref_id] . ' THEN "' . $title . '" ';
                        $description_sql .= 'WHEN deno_id = ' . $dtone_og_mapping[$publisher_ref_id] . ' THEN "' . $description . '" ';
                        $deno_id_to_update_sql[] = $dtone_og_mapping[$publisher_ref_id];
                    }
                }

                if (isset($cost_mapping[$publisher_ref_id])) {
                    if($cost_mapping[$publisher_ref_id] != $cost_price){
                        $sql = 'UPDATE ' . MobileRechargeDeno::tableName() . ' SET cost_price = ' . $cost_price . ', sales_price = ' . $selling_price . ', updated_at=' . time() . ' WHERE publisher_ref_id = "' . $publisher_ref_id . '"';
                        Yii::$app->db_og->createCommand($sql)->execute();
                    }
                    unset($deno_check[$ids]);
                }
            }
        }

        if ($unsupported_operator_list) {
            Yii::$app->slack->send('Missing Operator For DTOne Product', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($unsupported_operator_list)
                )
            ), 'DEBUG');
        }

        if ($deno_check) {
            $disabled_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => [5], 'dtone_id' => $deno_check])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 2, 'updated_at' => time()], ['deno_id' => $disabled_deno]);
        }

        if ($reactivate_deno) {
            $reactivate_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => [5], 'dtone_id' => $reactivate_deno])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 1, 'updated_at' => time()], ['deno_id' => $reactivate_deno]);
        }

        if (!empty($deno_insert)) {
            Yii::$app->db_og->createCommand()->batchInsert(
                MobileRechargeDeno::tableName(),
                ['type', 'operator_id', 'publisher_id', 'cost_currency', 'cost_price', 'sales_currency', 'sales_price', 'publisher_ref_id', 'status', 'products_id', 'created_at', 'updated_at'],
                $deno_insert
            )->execute();

            $deno_id_list = ArrayHelper::getColumn($deno_insert, 7);
            $data_list = MobileRechargeDeno::find()->select(['deno_id'])->where(['publisher_id' => $publisher_id, 'publisher_ref_id' => $deno_id_list])->asArray()->all();
            $data_list = ArrayHelper::getColumn($data_list, 'deno_id');

            foreach ($data_list as $index => $og_id) {
                $deno_description_insert[$index][] = $og_id;
                $dtone_mapping_insert[$index][] = $og_id;
            }

            Yii::$app->db_og->createCommand()->batchInsert(MobileRechargeDenoDescription::tableName(), ['language_id', 'title', 'description', 'deno_id'], $deno_description_insert)->execute();
            Yii::$app->db_og->createCommand()->batchInsert(DtoneOgMapping::tableName(), ['dtone_id', 'type', 'description', 'og_id'], $dtone_mapping_insert)->execute();
        }
        
        // if ($id_list) {
        //     $sql = 'UPDATE ' . MobileRechargeDeno::tableName() . ' SET cost_price = (CASE ' . $cost_sql . ' END) ,sales_price = (CASE ' . $sales_sql . ' END), updated_at=' . time() . ' WHERE publisher_ref_id IN (' . implode(
        //             ",",
        //             $id_list
        //         ) . ')';
        //     Yii::$app->db_og->createCommand($sql)->execute();
        // }

        if ($deno_id_to_update_sql) {
            $sql = 'UPDATE ' . MobileRechargeDenoDescription::tableName() . ' SET title = (CASE ' . $title_sql . ' END) ,description = (CASE ' . $description_sql . ' END) WHERE deno_id IN (' . implode(
                    ",",
                    $deno_id_to_update_sql
                ) . ')';
            Yii::$app->db_og->createCommand($sql)->execute();
        }

        return true;
    }

    public static function batchCreate($publisher_id, $currency, $mark_up, $deno_list, $products_id)
    {
        $deno_data = self::find()->select(['publisher_ref_id', 'mark_up', 'cost_price', 'status'])->where(['publisher_id' => $publisher_id])->asArray()->all();
        $data_list = DtoneOgMapping::find()->select(['dtone_id'])->where(['dtone_id' => ArrayHelper::getColumn($deno_data, 'publisher_ref_id'), 'type' => [3]])->asArray()->all();
        $data_list = ArrayHelper::getColumn($data_list, 'dtone_id');
        //Clone of data list for checking purpose, to prevent same deno id on multiple operator
        $deno_check = $data_list;

        $operator_mapping = DtoneOgMapping::find()->select(['og_id', 'dtone_id'])->where(['type' => 2])->asArray()->all();
        $operator_mapping = ArrayHelper::map($operator_mapping, 'dtone_id', 'og_id');

        $deno_data = self::find()->select(['publisher_ref_id', 'mark_up', 'cost_price', 'status'])->where(['publisher_ref_id' => $data_list])->asArray()->all();
        $markup_mapping = ArrayHelper::map($deno_data, 'publisher_ref_id', 'mark_up');
        $cost_mapping = ArrayHelper::map($deno_data, 'publisher_ref_id', 'cost_price');
        $deno_status = ArrayHelper::map($deno_data, 'publisher_ref_id', 'status');

        $dtone_mapping_insert = [];
        $deno_insert = [];
        $deno_description_insert = [];
        $cost_sql = '';
        $sales_sql = '';
        $id_list = [];
        $reactivate_deno = [];

        $formatter = (new \NumberFormatter('en_US', \NumberFormatter::CURRENCY));
        $currencies_obj = new Currency;
        $og_currencies_list = $currencies_obj->getCurrencyList();

        foreach ($deno_list as $deno_data) {
            $denomination_list = explode(",", $deno_data['product_list']);
            $cost_price_list = explode(",", $deno_data['wholesale_price_list']);

            $time = time();
            array_unique($denomination_list);

            foreach ($denomination_list as $index => $deno) {
                $operator_id = (int)$deno_data['operatorid'];
                $publisher_ref_id = $operator_id . '_' . $deno;
                $ids = array_search($publisher_ref_id, $data_list);

                $cost_price = (double)$cost_price_list[$index];

                if (isset($deno_status[$publisher_ref_id]) && $deno_status[$publisher_ref_id] == 2) {
                    $reactivate_deno[] = $publisher_ref_id;
                }

                if (isset($cost_mapping[$publisher_ref_id]) && $cost_mapping[$publisher_ref_id] == $cost_price) {
                    // Skip update statement when no changes
                    unset($deno_check[$ids]);
                    continue;
                }

                if (isset($markup_mapping[$publisher_ref_id]) && $markup_mapping[$publisher_ref_id] > 0) {
                    $selling_price = $cost_price + round($cost_price * $markup_mapping[$publisher_ref_id] / 100, 2);
                } else {
                    $selling_price = $cost_price + round($cost_price * $mark_up / 100, 2);
                }

                if ($ids === false) {
                    if (gettype($deno_data['destination_currency']) == "string" && isset($og_currencies_list[$deno_data['destination_currency']])) {
                        $title = $currencies_obj->format($deno_data['destination_currency'], $deno, ' ');
                    } elseif (gettype($deno_data['destination_currency']) != "string") {
                        // Error with DTone Data, Temporary Skip
                        Yii::$app->slack->send('DTONE Missing Destination Currency', array(
                            array(
                                'color' => 'warning',
                                'text' => Json::encode($deno_data)
                            )
                        ), 'DEBUG');
                        continue;
                    } else {
                        $title = $formatter->formatCurrency($deno, $deno_data['destination_currency']);
                    }

                    $deno_insert[] = [1, $operator_mapping[$operator_id], $publisher_id, $currency, $cost_price, $currency, $selling_price, $publisher_ref_id, 1, $products_id, $time, $time];
                    $deno_description_insert[] = [1, html_entity_decode($title)];
                    $dtone_mapping_insert[] = [(string)$publisher_ref_id, 3, ''];
                    $data_list[] = $publisher_ref_id;
                } else {
                    $cost_sql .= 'WHEN publisher_ref_id="' . $publisher_ref_id . '" THEN "' . $cost_price . '" ';
                    $sales_sql .= 'WHEN publisher_ref_id="' . $publisher_ref_id . '" THEN "' . $selling_price . '" ';
                    $id_list[] = "'" . $publisher_ref_id . "'";
                    unset($deno_check[$ids]);
                }
            }
        }

        if ($deno_check) {
            $disabled_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 3, 'dtone_id' => $deno_check])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 2, 'updated_at' => time()], ['deno_id' => $disabled_deno]);
        }

        if ($reactivate_deno) {
            $reactivate_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 3, 'dtone_id' => $reactivate_deno])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 1, 'updated_at' => time()], ['deno_id' => $reactivate_deno]);
        }

        if (!empty($deno_insert)) {
            Yii::$app->db_og->createCommand()->batchInsert(
                MobileRechargeDeno::tableName(),
                ['type', 'operator_id', 'publisher_id', 'cost_currency', 'cost_price', 'sales_currency', 'sales_price', 'publisher_ref_id', 'status', 'products_id', 'created_at', 'updated_at'],
                $deno_insert
            )->execute();

            $deno_id_list = ArrayHelper::getColumn($deno_insert, 7);
            $data_list = MobileRechargeDeno::find()->select(['deno_id'])->where(['publisher_ref_id' => $deno_id_list])->asArray()->all();
            $data_list = ArrayHelper::getColumn($data_list, 'deno_id');

            foreach ($data_list as $index => $og_id) {
                $deno_description_insert[$index][] = $og_id;
                $dtone_mapping_insert[$index][] = $og_id;
            }

            Yii::$app->db_og->createCommand()->batchInsert(MobileRechargeDenoDescription::tableName(), ['language_id', 'title', 'deno_id'], $deno_description_insert)->execute();
            Yii::$app->db_og->createCommand()->batchInsert(DtoneOgMapping::tableName(), ['dtone_id', 'type', 'description', 'og_id'], $dtone_mapping_insert)->execute();
        }

        if (!empty($id_list)) {
            $sql = 'UPDATE ' . MobileRechargeDeno::tableName() . ' SET cost_price = (CASE ' . $cost_sql . ' END) ,sales_price = (CASE ' . $sales_sql . ' END), updated_at =' . time() . ' WHERE publisher_ref_id IN (' . implode(
                    ",",
                    $id_list
                ) . ')';
            Yii::$app->db_og->createCommand($sql)->execute();
        }

        return true;
    }

    public static function batchCreateBundle($publisher_id, $currency, $mark_up, $deno_list, $products_id)
    {
        $deno_data = self::find()->select(['publisher_ref_id', 'mark_up', 'cost_price', 'status'])->where(['publisher_id' => $publisher_id])->asArray()->all();
        $data_list = DtoneOgMapping::find()->select(['dtone_id'])->where(['dtone_id' => ArrayHelper::getColumn($deno_data, 'publisher_ref_id'), 'type' => [4]])->asArray()->all();
        $data_list = ArrayHelper::getColumn($data_list, 'dtone_id');
        //Clone of data list for checking purpose, to prevent same deno id on multiple operator
        $deno_check = $data_list;

        $operator_mapping = DtoneOgMapping::find()->select(['og_id', 'dtone_id'])->where(['type' => 2])->asArray()->all();
        $operator_mapping = ArrayHelper::map($operator_mapping, 'dtone_id', 'og_id');

        $region_mapping = DtoneOgMapping::find()->select(['dtone_id', 'og_id'])->where(['type' => 1])->asArray()->all();
        $region_mapping = ArrayHelper::map($region_mapping, 'og_id', 'dtone_id');

        $operator_country = MobileRechargeOperator::find()->select(['region_id', 'operator_id'])->asArray()->all();
        $operator_country = ArrayHelper::map($operator_country, 'operator_id', 'region_id');

        $special_markup = self::find()->select(['publisher_ref_id', 'mark_up', 'cost_price', 'status'])->where(['publisher_ref_id' => $data_list])->asArray()->all();
        $markup_mapping = ArrayHelper::map($special_markup, 'publisher_ref_id', 'mark_up');
        $cost_mapping = ArrayHelper::map($special_markup, 'publisher_ref_id', 'cost_price');
        $deno_status = ArrayHelper::map($special_markup, 'publisher_ref_id', 'status');

        $dtone_mapping_insert = [];
        $deno_insert = [];
        $deno_description_insert = [];
        $cost_sql = '';
        $sales_sql = '';
        $id_list = [];
        $missing_operator_id = [];
        $reactivate_deno = [];

        $time = time();

        foreach ($deno_list as $index => $deno) {
            $operator_id = self::legacyOperatorMapping($deno['operator_id']);
            if ($operator_id == 0) {
                continue;
            }

            $publisher_ref_id = $operator_id . '_' . $deno['product_id'];
            $cost_price = (double)$deno['wholesale_price'];
            $ids = array_search($publisher_ref_id, $data_list);

            if (isset($deno_status[$publisher_ref_id]) && $deno_status[$publisher_ref_id] == 3) {
                $reactivate_deno[] = $publisher_ref_id;
            }

            if (isset($cost_mapping[$publisher_ref_id]) && $cost_mapping[$publisher_ref_id] == $cost_price) {
                // Skip update statement when no changes
                unset($deno_check[$ids]);
                continue;
            }

            if (!isset($operator_mapping[$operator_id]) || $deno['country_id'] != $region_mapping[$operator_country[$operator_mapping[$operator_id]]]) {
                if (array_search($operator_id, $missing_operator_id) === false) {
                    // Mismatch from DTONE, mapping required
                    $missing_operator_id[] = $operator_id;
                }
                continue;
            }

            if (isset($markup_mapping[$publisher_ref_id]) && $markup_mapping[$publisher_ref_id] > 0) {
                $selling_price = $cost_price + round($cost_price * $markup_mapping[$publisher_ref_id] / 100, 2);
            } else {
                $selling_price = $cost_price + round($cost_price * $mark_up / 100, 2);
            }

            $title = $deno['product_name'];
            $description = '<ul><li>' . implode('</li><li>', explode(', ', $deno['product_short_desc'])) . '</li></ul>';

            if ($ids === false) {
                $deno_insert[] = [2, $operator_mapping[$operator_id], $publisher_id, $currency, $cost_price, $currency, $selling_price, $publisher_ref_id, 1, $products_id, $time, $time];
                $deno_description_insert[] = [1, html_entity_decode($title), html_entity_decode($description)];
                $dtone_mapping_insert[] = [(string)$publisher_ref_id, 4, ''];
                $data_list[] = $publisher_ref_id;
            } else {
                $cost_sql .= 'WHEN publisher_ref_id="' . $publisher_ref_id . '" THEN "' . $cost_price . '" ';
                $sales_sql .= 'WHEN publisher_ref_id="' . $publisher_ref_id . '" THEN "' . $selling_price . '" ';
                $id_list[] = "'" . $publisher_ref_id . "'";
                unset($deno_check[$ids]);
            }
        }

        if ($deno_check) {
            $disabled_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 4, 'dtone_id' => $deno_check])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 2, 'updated_at' => time()], ['deno_id' => $disabled_deno]);
        }

        if ($reactivate_deno) {
            $reactivate_deno = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 4, 'dtone_id' => $reactivate_deno])->asArray()->all(), 'og_id');
            MobileRechargeDeno::updateAll(['status' => 1, 'updated_at' => time()], ['deno_id' => $reactivate_deno]);
        }

        if (!empty($deno_insert)) {
            Yii::$app->db_og->createCommand()->batchInsert(
                MobileRechargeDeno::tableName(),
                ['type', 'operator_id', 'publisher_id', 'cost_currency', 'cost_price', 'sales_currency', 'sales_price', 'publisher_ref_id', 'status', 'products_id', 'created_at', 'updated_at'],
                $deno_insert
            )->execute();

            $deno_id_list = ArrayHelper::getColumn($deno_insert, 7);
            $data_list = MobileRechargeDeno::find()->select(['deno_id'])->where(['publisher_ref_id' => $deno_id_list])->asArray()->all();
            $data_list = ArrayHelper::getColumn($data_list, 'deno_id');

            foreach ($data_list as $index => $og_id) {
                $deno_description_insert[$index][] = $og_id;
                $dtone_mapping_insert[$index][] = $og_id;
            }

            Yii::$app->db_og->createCommand()->batchInsert(MobileRechargeDenoDescription::tableName(), ['language_id', 'title', 'description', 'deno_id'], $deno_description_insert)->execute();
            Yii::$app->db_og->createCommand()->batchInsert(DtoneOgMapping::tableName(), ['dtone_id', 'type', 'description', 'og_id'], $dtone_mapping_insert)->execute();
        }

        if (!empty($id_list)) {
            $sql = 'UPDATE ' . MobileRechargeDeno::tableName() . ' SET cost_price = (CASE ' . $cost_sql . ' END) ,sales_price = (CASE ' . $sales_sql . ' END), updated_at=' . time() . ' WHERE publisher_ref_id IN (' . implode(
                    ",",
                    $id_list
                ) . ')';
            Yii::$app->db_og->createCommand($sql)->execute();
        }

        if (!empty($missing_operator_id)) {
            Yii::$app->slack->send('Missing Operator in Data Bundle', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($missing_operator_id)
                )
            ), 'DEBUG');
        }

        return true;
    }

    public static function batchUpdate($data)
    {
        $update_data = [];
        if ($data['status'] !== "") {
            $update_data['status'] = $data['status'];
        }

        if ($data['products_id'] !== "") {
            $p_model = Products::find()->where(['products_id' => $data['products_id'], 'products_type' => 3])->one();
            if ($p_model) {
                $update_data['products_id'] = $data['products_id'];
            }
        }

        if ($data['mark_up'] !== "" && ((integer)$data['mark_up'] > 0)) {
            $update_data['mark_up'] = (integer)$data['mark_up'];
        }

        if ($update_data) {
            self::updateAll($update_data, ['deno_id' => $data['items']]);
        }
    }

    public static function legacyOperatorMapping($operator_id)
    {
        $operator_id = (int)$operator_id;
        switch ($operator_id) {
            case 110:
                $operator_id = 1585;
                break;
            case 178:
                $operator_id = 1566;
                break;
            case 553:
                $operator_id = 1590;
                break;
            case 1512:
                $operator_id = 3055;
                break;
            case 1518:
                $operator_id = 1584;
                break;
            case 1521:
                $operator_id = 1581;
                break;
            case 1928:
                $operator_id = 1929;
                break;
            // Telco N/A in system
            case 2337:
            case 2326:
            case 3227:
                $operator_id = 0;
                break;
        }

        return $operator_id;
    }

}
