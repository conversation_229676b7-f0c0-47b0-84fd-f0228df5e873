<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "mobile_recharge_deno_description".
 *
 * @property int $deno_description_id
 * @property int $deno_id
 * @property int $language_id
 * @property string $title
 * @property string $description
 *
 * @property MobileRechargeDeno $deno
 */
class MobileRechargeDenoDescription extends \yii\db\ActiveRecord
{

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_deno_description';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['deno_id', 'language_id'], 'required'],
            [['deno_id', 'language_id'], 'integer'],
            [['description'], 'string'],
            [['title'], 'string', 'max' => 255],
            [['deno_id'], 'exist', 'skipOnError' => true, 'targetClass' => MobileRechargeDeno::className(), 'targetAttribute' => ['deno_id' => 'deno_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'deno_description_id' => 'Deno Description ID',
            'deno_id' => 'Deno ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'description' => 'Description',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDeno()
    {
        return $this->hasOne(MobileRechargeDeno::className(), ['deno_id' => 'deno_id']);
    }
}
