<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "mobile_recharge_history".
 *
 * @property int $history_id
 * @property int $customer_id
 * @property int $prefix
 * @property string $phone_no
 * @property int $deno_id
 * @property int $orders_id
 * @property int $orders_products_id
 * @property string $publisher_order_id
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class MobileRechargeHistory extends \yii\db\ActiveRecord
{
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_id', 'prefix', 'phone_no', 'deno_id', 'orders_id', 'orders_products_id', 'publisher_order_id', 'status', 'created_at', 'updated_at'], 'required'],
            [['customer_id', 'prefix', 'deno_id', 'orders_id', 'orders_products_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['phone_no', 'publisher_order_id'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'history_id' => 'History ID',
            'customer_id' => 'Customer ID',
            'prefix' => 'Prefix',
            'phone_no' => 'Phone No',
            'deno_id' => 'Deno ID',
            'orders_id' => 'Orders ID',
            'orders_products_id' => 'Orders Products ID',
            'publisher_order_id' => 'Publisher Order ID',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
