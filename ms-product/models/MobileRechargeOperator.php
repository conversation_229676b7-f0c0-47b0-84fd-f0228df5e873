<?php

namespace micro\models;

use Yii;
use yii\helpers\ArrayHelper;
use micro\components\GeneralCom;
use yii\helpers\Json;

/**
 * This is the model class for table "mobile_recharge_operator".
 *
 * @property int $operator_id
 * @property int $region_id
 * @property string $logo_path
 * @property int $status
 * @property int $parent_operator_id
 * @property int $created_at
 * @property int $updated_at
 *
 * @property MobileRechargeDeno[] $mobileRechargeDenos
 * @property MobileRechargeOperatorDescription[] $mobileRechargeOperatorDescriptions
 */
class MobileRechargeOperator extends \yii\db\ActiveRecord
{

    const STATUS = array(
        0 => 'Inactive',
        1 => 'Active',
        2 => 'Removed'
    );

    public $description;

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_operator';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }

    public function attributes()
    {
        // add related fields to searchable attributes
        return array_merge(parent::attributes(), ['title']);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['status', 'parent_operator_id', 'region_id', 'created_at', 'updated_at'], 'integer'],
            [['logo_path'], 'string', 'max' => 2048],
            [['title', 'description'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'operator_id' => 'Operator ID',
            'region_id' => 'Region',
            'logo_path' => 'Logo Path',
            'status' => 'Status',
            'parent_operator_id' => 'Parent Operator ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }


    public static function get($data)
    {
        $model = self::find()->select(['operator_id', 'region_id', 'logo_path', 'status', 'parent_operator_id'])->where(['operator_id' => $data['id']])->asArray()->one();
        if ($model) {
            $description = \micro\models\MobileRechargeOperatorDescription::find()->select(['title', 'description', 'terms', 'language_id'])->where(['operator_id' => $data['id']])->asArray()->all();
            $model['description'] = ArrayHelper::index($description, 'language_id');
        }
        return $model;
    }

    public function search($params, $page = 1, $limit = 10)
    {
        $searchModel = new self;

        // add conditions that should always apply here

        $searchModel->load($params, '');

        $query = self::find();

        $query->joinWith(['mobileRechargeOperatorDescription']);

        $query->andFilterWhere(
            ['LIKE', MobileRechargeOperatorDescription::tableName() . '.title', $searchModel->getAttribute('title')]
        );

        // grid filtering conditions
        $query->andFilterWhere([
            'operator_id' => $searchModel->operator_id,
            'status' => $searchModel->status
        ]);

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [
                'id' => $obj['operator_id'],
                'title' => $obj['mobileRechargeOperatorDescription']['title'],
                'status' => $obj['status']
            ];

            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data,
            'params' => $params
        ];

        return ($return_array);
    }

    public function getMobileRechargeOperatorDescription()
    {
        return $this->hasOne(MobileRechargeOperatorDescription::class, ['operator_id' => 'operator_id'])->andWhere(['language_id' => 1]);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMobileRechargeDenos()
    {
        return $this->hasMany(MobileRechargeDeno::className(), ['operator_id' => 'operator_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMobileRechargeOperatorDescriptions()
    {
        return $this->hasMany(MobileRechargeOperatorDescription::className(), ['operator_id' => 'operator_id']);
    }

    public function getDenominationList($publisher_id, $language_id = 1, $type = 1)
    {
        $return_arr = [];
        $operator_list = ArrayHelper::getColumn(
            MobileRechargeOperator::find()->select('operator_id')
                ->where(['operator_id' => $this->operator_id])
                ->orWhere(['parent_operator_id' => $this->operator_id, 'status' => 1])
                ->asArray()
                ->all(),
            'operator_id'
        );

        $language_list = [$language_id];

        if ($language_id != 1) {
            $language_list[] = 1;
        }

        $data = MobileRechargeDeno::find()
            ->alias('d')
            ->joinWith('mobileRechargeDenoDescriptions dd')
            ->where(['d.operator_id' => $operator_list, 'd.type' => $type, 'language_id' => $language_list])
            ->andWhere(['publisher_id' => $publisher_id, 'status' => 1])
            ->andWhere(['!=', 'products_id', 0])
            ->orderBy('sales_price DESC')
            ->asArray()
            ->all();

        foreach ($data as $item) {
            $deno_description_list = ArrayHelper::index($item['mobileRechargeDenoDescriptions'], 'language_id');
            $deno_description = (!empty($deno_description_list[$language_id]) ? $deno_description_list[$language_id] : $deno_description_list[1]);

            $obj = [];
            $obj['title'] = GeneralCom::checkEmpty($deno_description['title'], $deno_description_list[1]['title']);
            $obj['description'] = GeneralCom::checkEmpty($deno_description['description'], $deno_description_list[1]['description']);
            $obj['products_id'] = $item['products_id'];
            $obj['deno_id'] = $item['deno_id'];
            $obj['sales_price'] = $item['sales_price'];
            $obj['sales_currency'] = $item['sales_currency'];
            $return_arr[] = $obj;
        }

        return $return_arr;
    }

    public static function batchCreate($list)
    {
        $operator_list = DtoneOgMapping::find()->select(['dtone_id'])->where(['type' => 2])->asArray()->all();
        $existing_data = ArrayHelper::getColumn($operator_list, 'dtone_id');

        $countries_list = ArrayHelper::map(DtoneOgMapping::find()->select(['dtone_id', 'og_id'])->where(['type' => 1])->asArray()->all(), 'dtone_id', 'og_id');
        foreach ($list as $operator_data) {
            $o_title_list = explode(",", $operator_data['operator']);
            $o_id_list = explode(",", $operator_data['operatorid']);

            foreach ($o_id_list as $index => $value) {
                $ids = array_search($value, $existing_data);
                if ($ids === false) {
                    $og_operator = new \micro\models\MobileRechargeOperator();
                    $og_operator->region_id = $countries_list[$operator_data['countryid']];
                    $og_operator->status = 1;

                    if ($og_operator->save()) {
                        $og_operator_description = new MobileRechargeOperatorDescription();
                        $og_operator_description->operator_id = $og_operator->operator_id;
                        $og_operator_description->language_id = 1;
                        $og_operator_description->title = $o_title_list[$index];
                        $og_operator_description->save();

                        $dtone_operator = new DtoneOgMapping();
                        $dtone_operator->dtone_id = $value;
                        $dtone_operator->type = 2;
                        $dtone_operator->og_id = $og_operator->operator_id;
                        $dtone_operator->save();
                    }
                } else {
                    unset($existing_data[$ids]);
                }
            }
        }

        $disabled_operator = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 2, 'dtone_id' => $existing_data])->asArray()->all(), 'og_id');

        MobileRechargeOperator::updateAll(['status' => 1, 'updated_at' => time()], ['and', ['status' => 2], ['NOT IN', 'operator_id', $disabled_operator]]);

        return true;
    }

    public static function batchProcessOperatorList($list)
    {
        $operator_list = DtoneOgMapping::find()->select(['dtone_id'])->where(['type' => 2])->asArray()->all();
        $existing_data = ArrayHelper::getColumn($operator_list, 'dtone_id');

        $countries_list = ArrayHelper::map(MobileRechargeRegion::find()->select(['region_id', 'iso3'])->asArray()->all(), 'iso3', 'region_id');
        $unsupported_region_list = [];

        foreach ($list as $operator) {
            $operator_id = $operator['operator_id'];
            $name = $operator['name'];

            $ids = array_search($operator_id, $existing_data);
            if ($ids === false) {
                if (isset($countries_list[strtoupper($operator['region_code'])])) {
                    $og_operator = new \micro\models\MobileRechargeOperator();
                    $og_operator->region_id = $countries_list[strtoupper($operator['region_code'])];
                    $og_operator->status = 1;

                    if ($og_operator->save()) {
                        $og_operator_description = new MobileRechargeOperatorDescription();
                        $og_operator_description->operator_id = $og_operator->operator_id;
                        $og_operator_description->language_id = 1;
                        $og_operator_description->title = $name;
                        $og_operator_description->save();

                        $dtone_operator = new DtoneOgMapping();
                        $dtone_operator->dtone_id = (string)$operator_id;
                        $dtone_operator->type = 2;
                        $dtone_operator->og_id = $og_operator->operator_id;
                        $dtone_operator->save();
                        if ($dtone_operator->getErrors()) {
                            Yii::$app->slack->send('Unable to save operator', array(
                                array(
                                    'color' => 'warning',
                                    'text' => Json::encode($dtone_operator->getErrors())
                                )
                            ), 'DEBUG');
                        }
                    }
                } else {
                    $unsupported_region_list[] = $operator;
                }
            } else {
                unset($existing_data[$ids]);
            }
        }

        if ($unsupported_region_list) {
            Yii::$app->slack->send('Missing Region For DTOne Operator', array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode($unsupported_region_list)
                )
            ), 'DEBUG');
        }

        $disabled_operator = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 2, 'dtone_id' => $existing_data])->asArray()->all(), 'og_id');
        MobileRechargeOperator::updateAll(['status' => 1, 'updated_at' => time()], ['and', ['status' => 2], ['NOT IN', 'operator_id', $disabled_operator]]);

        return true;
    }

    public static function getTitle($operator_id)
    {
        $key = self::tableName() . '/' . 'title' . '/' . $operator_id;
        $data = Yii::$app->cache->getOrSet($key, function () use ($operator_id) {
            $model = MobileRechargeOperatorDescription::find()->select('title')->where(['operator_id' => $operator_id, 'language_id' => 1])->one();
            return $model->title;
        });
        return $data;
    }

    public function afterSave($insert, $changedAttribute)
    {
        $key = 'mobile-recharge/operator-list';
        Yii::$app->cache->delete($key);
        return parent::afterSave($insert, $changedAttribute);
    }
}
