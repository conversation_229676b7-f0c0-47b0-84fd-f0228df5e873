<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "mobile_recharge_operator_description".
 *
 * @property int $operator_description_id
 * @property int $operator_id
 * @property int $language_id
 * @property string $title
 * @property string $description
 * @property string $terms
 *
 * @property MobileRechargeOperator $operator
 */
class MobileRechargeOperatorDescription extends \yii\db\ActiveRecord
{

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_operator_description';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['operator_id', 'language_id', 'title'], 'required'],
            [['operator_id', 'language_id'], 'integer'],
            [['description', 'terms'], 'string'],
            [['title'], 'string', 'max' => 255],
            [['operator_id'], 'exist', 'skipOnError' => true, 'targetClass' => MobileRechargeOperator::className(), 'targetAttribute' => ['operator_id' => 'operator_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'operator_description_id' => 'Operator Description ID',
            'operator_id' => 'Operator ID',
            'language_id' => 'Language ID',
            'title' => 'Title',
            'description' => 'Description',
            'terms' => 'Terms',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOperator()
    {
        return $this->hasOne(MobileRechargeOperator::className(), ['operator_id' => 'operator_id']);
    }
}
