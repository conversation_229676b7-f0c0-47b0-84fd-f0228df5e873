<?php

namespace micro\models;

use micro\models\DtoneOgMapping;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use offgamers\base\models\Countries;

/**
 * This is the model class for table "mobile_recharge_region".
 *
 * @property int $region_id
 * @property string $flag
 * @property string $iso3
 * @property string $name
 * @property int $prefix
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class MobileRechargeRegion extends \yii\db\ActiveRecord
{
    public $limit = 10;
    public $row = 10;

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mobile_recharge_region';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['prefix', 'status', 'created_at', 'updated_at'], 'integer'],
            [['flag'], 'string', 'max' => 5],
            [['iso3'], 'string', 'max' => 3],
            [['name'], 'string', 'max' => 255],
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'region_id' => 'Region ID',
            'flag' => 'Flag',
            'iso3' => 'Iso3',
            'name' => 'Name',
            'prefix' => 'Prefix',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public static function search($params, $page = 1, $limit = 10)
    {
        $searchModel = new self();

        $searchModel->load($params, '');
        $query = self::find();

        $query->andFilterWhere(['like', 'name', $searchModel->name])
            ->andFilterWhere(['like', 'iso3', $searchModel->iso3])
            ->andFilterWhere(['=', 'status', $searchModel->status])
            ->andFilterWhere(['=', 'prefix', $searchModel->prefix]);

        $query->orderBy('name');

        $query->limit($limit);

        $query->offset(($page - 1) * $query->limit);

        $result = $query->asArray()->all();

        $data = [];

        foreach ($result as $obj) {
            $item = [
                'id' => $obj['region_id'],
                'name' => $obj['name'],
                'iso3' => $obj['iso3'],
                'prefix' => $obj['prefix'],
                'status' => $obj['status']
            ];

            $data[] = $item;
        }

        $return_array = [
            'totalCount' => $query->count(),
            'page' => $page,
            'pageSize' => count($data),
            'results' => $data,
            'params' => $params
        ];
        return ($return_array);
    }

    public static function get($data)
    {
        $model = self::find()->select(['region_id', 'name', 'iso3', 'status', 'prefix', 'flag'])->where(['region_id' => $data['id']])->asArray()->one();
        return $model;
    }

    public static function batchCreate($list)
    {
        $list = array_unique($list);

        $countries_list = Countries::find()->select(['countries_name', 'countries_iso_code_3', 'countries_international_dialing_code'])->asArray()->all();
        $countries_list = ArrayHelper::index($countries_list, 'countries_name');

        $countries_data = DtoneOgMapping::find()->select(['dtone_id', 'og_id'])->where(['type' => 1])->asArray()->all();
        $existing_countries = ArrayHelper::getColumn($countries_data, 'dtone_id');

        foreach ($list as $id => $value) {
            $ids = array_search($id, $existing_countries);
            if ($ids === false) {
                $region_model = new MobileRechargeRegion();

                if (isset($countries_list[$value])) {
                    $region_model->iso3 = $countries_list[$value]['countries_iso_code_3'];
                    $region_model->flag = $countries_list[$value]['countries_iso_code_3'];
                    $region_model->prefix = $countries_list[$value]['countries_international_dialing_code'];
                    $region_model->status = 1;
                } else {
                    $region_model->status = 0;
                }

                $region_model->name = $value;

                if ($region_model->save()) {
                    $dtone_mapping = new DtoneOgMapping();
                    $dtone_mapping->type = 1;
                    $dtone_mapping->dtone_id = (string)$id;
                    $dtone_mapping->og_id = ($region_model->region_id ?? 1);
                    $dtone_mapping->save();
                }
            } else {
                unset($existing_countries[$ids]);
            }
        }
        $disabled_region = [];
        if ($existing_countries) {
            $disabled_region = ArrayHelper::getColumn(DtoneOgMapping::find()->select(['og_id'])->where(['type' => 1, 'dtone_id' => $existing_countries])->asArray()->all(), 'og_id');
            MobileRechargeRegion::updateAll(['status' => 2], ['region_id' => $disabled_region]);
        }
        MobileRechargeRegion::updateAll(['status' => 1], ['and', ['status' => 2], ['NOT IN', 'region_id', $disabled_region]]);

        return true;
    }


    public function afterSave($insert, $changedAttribute)
    {
        $key = 'mobile-recharge/region-list';
        Yii::$app->cache->delete($key);
        return parent::afterSave($insert, $changedAttribute);
    }

}
