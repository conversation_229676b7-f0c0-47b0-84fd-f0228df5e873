<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "OrdersProductsReviews".
 *
 * @property int $review_id
 * @property int $orders_id
 * @property int $orders_products_id
 * @property int $categories_id
 * @property int $products_id
 * @property int $review_score
 * @property string $comment
 * @property int $status
 * @property int $visibility
 * @property string $created_at
 * @property string $created_by
 * @property string $updated_at
 * @property string $updated_by
 *
 */
class OrdersProductsReviews extends \yii\db\ActiveRecord
{
    public $_updated_by;
    /**
     * {@inheritdoc}
     */

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public static function tableName()
    {
        return 'orders_products_reviews';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
            ],
        ];
    }

    public function rules()
    {
        return [
            [
                [
                    'review_id',
                    'orders_id',
                    'orders_products_id',
                    'products_id',
                    'categories_id',
                    'review_score',
                    'status',
                    'visibility'
                ],
                'integer'
            ],
            [['created_at', 'updated_at'], 'safe'],
            [['comment', 'created_by', 'updated_by'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'review_id' => 'Review Id',
            'orders_id' => 'Orders Id',
            'orders_products_id' => 'Orders Products Id',
            'products_id' => 'Products Id',
            'categories_id' => 'Categories Id',
            'review_score' => 'Review Score',
            'comment' => 'Comment',
            'status' => 'Status',
            'visibility' => 'Visibility',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
            'updated_at' => 'updated_at',
            'updated_by' => 'updated_by',
        ];
    }

    public function saveReviewRecords($params)
    {
        $this->orders_id = $params['orders_id'];
        $this->orders_products_id = $params['orders_products_id'];
        $this->products_id = $params['products_id'];
        $this->categories_id = $params['categories_id'];
        $this->review_score = $params['review_score'];
        $this->comment = $params['comment'];
        $this->status = 2;
        $this->visibility = 1;
        $this->created_by = $params['user_id'];
        $this->updated_by = $params['user_id'];

        try {
            $this->save();
            return true;
        }catch (Exception $e) {
            return $e;
        }
    }

    protected function getModifiedBy()
    {
        if (empty($this->_updated_by)) {
            if (!empty(Yii::$app->params['_user_identifier'])) {
                $this->_updated_by = Yii::$app->params['_user_identifier'];
            } else {
                $this->_updated_by = 'system';
            }
        }

        return $this->_updated_by;
    }

}
