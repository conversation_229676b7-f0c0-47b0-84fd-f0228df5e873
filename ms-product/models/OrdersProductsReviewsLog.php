<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "OrdersProductsReviewsLog".
 *
 * @property int $log_id
 * @property string $created_at
 * @property string $created_by
 * @property string $activities
 * @property int $review_id
 *
 */
class OrdersProductsReviewsLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public static function tableName()
    {
        return 'orders_products_reviews_logs';
    }

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'updatedAtAttribute' => null,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['log_id', 'review_id',], 'integer'],
            [['created_at'], 'safe'],
            [['created_by','activities'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'log_id' => 'Log Id',
            'created_at' => 'created_at',
            'created_by' => 'created_by',
            'activities' => 'Activities',
            'review_id' => 'Review Id',
        ];
    }

    public function saveLog($params){
        $this->review_id = $params['review_id'];
        $this->created_by = $params['user'];
        $this->activities = $params['activity'];

        try {
            $this->save();
            return true;
        }catch (Exception $e) {
            return $e;
        }
    }
}
