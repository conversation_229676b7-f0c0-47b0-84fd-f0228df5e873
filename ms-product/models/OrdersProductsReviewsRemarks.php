<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "OrdersProductsReviewsRemarks".
 *
 * @property int $remark_id
 * @property int $review_id
 * @property string $remarks
 * @property string $internal_remarks
 * @property int $status
 * @property string $created_at
 * @property string $created_by
 * @property string $updated_at
 * @property string $updated_by
 *
 */
class OrdersProductsReviewsRemarks extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public static function tableName()
    {
        return 'orders_products_reviews_remarks';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
            ],
        ];
    }

    public function rules()
    {
        return [
            [['remark_id', 'review_id', 'status',], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['remarks', 'created_by', 'updated_by'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'remark_id' => 'Remark Id',
            'review_id' => 'Review Id',
            'remarks' => 'Remarks',
            'status' => 'Status',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
            'updated_at' => 'updated_at',
            'updated_by' => 'updated_by',
        ];
    }

    public function saveRemarksRecords($params)
    {
        $this->review_id = $params['review_id'];
        $this->remarks = $params['remarks'];
        $this->status = $params['status'];
        $this->created_by = $params['user'];
        $this->updated_by = $params['user'];

        try {
            $this->save();
            return true;
        }catch (Exception $e) {
            return $e;
        }
    }
}
