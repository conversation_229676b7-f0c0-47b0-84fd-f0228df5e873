<?php

namespace micro\models;

use Yii;
use micro\components\GeneralCom;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use offgamers\base\models\LogTable;

class Products extends ProductsBase
{
    public static function getAllProductByCategoryId($category_list, $list = array())
    {
        $product_list = self::find()->alias('p')
            ->select(array('p.products_id', 'pd.products_name'))
            ->joinWith('categories c', false)
            ->joinWith('productsFollowPrice pfp', false)
            ->joinWith('productsToGameBlog ptgb', false)
            ->joinWith('productsDescription pd', false)
            ->where(array('p.products_status' => 1, 'pfp.products_id' => null, 'c.categories_status' => 1, 'c.categories_id' => $category_list, 'pd.language_id' => 1))
            ->andWhere(array('<>', 'p.custom_products_type_id', 3))
            ->andWhere(['NOT', ['p.products_id' => $list]])
            ->asArray()->all();

        return ArrayHelper::map($product_list, 'products_id', 'products_name');
    }

    public static function getRestockProductInfo($input)
    {
        $products_id = $input['products_id'];

        $result = self::find()
            ->alias('p')
            ->select([
                'p.products_id',
                'p.products_type',
                'p.products_model',
                'pc.products_original_cost as product_original_cost',
                'pc.products_original_currency as product_original_currency',
                'pc.products_cost as product_cost',
                'pc.products_currency as product_cost_currency'
            ])
            ->where(['p.products_id' => $products_id])
            ->joinWith('productsCost pc', false)
            ->asArray()
            ->one();

        $products_name = ProductsDescription::find()->select('products_name')->where(['products_id' => $products_id, 'language_id' => 1])->scalar();

        $result['products_name'] = ($products_name ?: '');

        if ($result['products_type'] == 2) {
            $game_product_info = GameProduct::find()
                ->select('gpp.game_publisher_id')
                ->alias('gp')
                ->joinWith('gamePublisherProduct gpp', false)
                ->where(['gp.products_id' => $products_id])
                ->asArray()
                ->one();

            $result['publisher_id'] = $game_product_info['game_publisher_id'];
        }

        if (isset($input['parent_products_id'])) {
            $bundle_item = ProductsBundles::find()
                ->select(['pb.subproduct_id', 'pb.subproduct_qty', 'p.products_price', 'p.products_base_currency'])
                ->alias('pb')
                ->joinWith('products p', false)
                ->where(['pb.bundle_id' => $input['parent_products_id']])
                ->asArray()
                ->all();

            $total = 0;
            $products_unit_price = 0;
            foreach ($bundle_item as $item) {
                $unit_price_in_usd = self::getUSDPrice($item['products_price'], $item['products_base_currency']);
                $total += $unit_price_in_usd * $item['subproduct_qty'];
                if ($item['subproduct_id'] == $products_id) {
                    $products_unit_price = $unit_price_in_usd;
                }
            }

            $result['bundle_info']['price_ratio'] = $products_unit_price / $total;
        }

        return $result;
    }

    public static function getUSDPrice($price, $base_currency, $rate = 'spot')
    {
        return Yii::$app->currency->advanceCurrencyConversion($price, $base_currency, 'USD', false, $rate);
    }

    public static function getProductsQuantity($products_id)
    {
        $product = self::find()->select(['products_quantity', 'products_actual_quantity'])->where(['products_id' => $products_id])->one();
        return $product;
    }

    public static function verifyingStockMovement($orders_id, $products_id, $qty)
    {
        $connection = self::getDb();
        $product = self::getProductsQuantity($products_id);
        if ($product) {
            $connection->createCommand()
                ->update(
                    self::tableName(), [
                    'products_quantity' => new \yii\db\Expression("COALESCE(products_quantity + $qty,$qty)"),
                ], ['products_id' => $products_id]
                )
                ->execute();

            $msg = strtr('##{ORDER_ID}##: ##{SYS_STATUS_FROM}## ##{SYS_STATUS_TO}##', [
                '{ORDER_ID}' => $orders_id,
                '{SYS_STATUS_FROM}' => 1,
                '{SYS_STATUS_TO}' => 7,
            ]);

            (new LogTable())->createLog(
                $products_id,
                'products_quantity',
                $product->products_quantity . ':~:' . $product->products_actual_quantity,
                ($product->products_quantity + $qty) . ':~:' . ($product->products_actual_quantity),
                $msg,
                ''
            );

            self::updateOutOfStockFlag($products_id, $product->products_quantity + $qty);
        }
    }

    public static function disableG2GInventory($products_id, $qty, $cpc_list)
    {
        $connection = self::getDb();
        $product = self::getProductsQuantity($products_id);
        if ($product) {
            $connection->createCommand()
                ->update(
                    self::tableName(), [
                    'products_quantity' => new \yii\db\Expression("COALESCE(products_quantity - $qty,$qty)"),
                    'products_actual_quantity' => new \yii\db\Expression("COALESCE(products_actual_quantity - $qty,$qty)"),
                ], ['products_id' => $products_id]
                )
                ->execute();

            (new LogTable())->createLog(
                (string) $products_id,
                'products_quantity',
                $product->products_quantity . ':~:' . $product->products_actual_quantity,
                ($product->products_quantity) - $qty . ':~:' . ($product->products_actual_quantity - $qty),
                'G2G Pull Pin',
                'CD Key ID:' . implode(",", $cpc_list)
            );

            self::updateOutOfStockFlag($products_id, $product->products_quantity + $qty);
        }
    }

    public static function updateProductQty($products_id, $qty, $cpc_list)
    {
        $connection = self::getDb();
        $product = self::getProductsQuantity($products_id);
        if ($product) {
            $connection->createCommand()
                ->update(self::tableName(), [
                    'products_quantity' => new \yii\db\Expression("COALESCE(products_quantity + $qty,$qty)"),
                    'products_actual_quantity' => new \yii\db\Expression("COALESCE(products_actual_quantity + $qty,$qty)")
                ], ['products_id' => $products_id]
                )
                ->execute();

            (new LogTable())->createLog(
                $products_id,
                'products_quantity',
                $product->products_quantity . ':~:' . $product->products_actual_quantity,
                ($product->products_quantity + $qty) . ':~:' . ($product->products_actual_quantity + $qty),
                'Quantity Adjustment',
                'CD Key ID:' . implode(",", $cpc_list)
            );
        }
    }

    public static function updateOutOfStockFlag($products_id, $quantity)
    {
        $updated_row = CustomersGroupsPurchaseControl::updateOutOfStockRule($products_id, $quantity);

        if ($updated_row) {
            $clear_array = CustomersGroupsPurchaseControl::getAllOutOfStockData($products_id);

            foreach ($clear_array as $data) {
                $cache_key = CustomersGroupsPurchaseControl::tableName() . '/products_id/' . $products_id . '/customers_groups_id/' . $data['customers_groups_id'];
                Yii::$app->frontend_cache->delete($cache_key);
            }
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        $changed_by = '';
        $data = Yii::$app->getRequest()->getBodyParam('data');
        if (isset($data['changed_by'])) {
            $changed_by = $data['changed_by'];
            Yii::$app->params['_user_identifier'] = $changed_by;
        }
        parent::afterSave($insert, $changedAttributes);
        if (!$insert) {
            if (!empty($changedAttributes['products_price']) && $this->products_status == 1) {
                //TODO : Temporary Additional checking for dirty attribute due to AR parse products_price column as string
                if ($changedAttributes['products_price'] != $this->products_price && $changed_by != 'CodesWholeSale') {
                    $url = Yii::$app->params['yii2.crew.href'] . '/game-publisher-product/update?id=' . $this->gameProduct->game_product_id;
                    GeneralCom::slackStackNotification(
                        'bdt_notify',
                        "*Active Product Price Changed By $changed_by* \n Product : <$url|$this->products_id> \n Price Update : " . $changedAttributes['products_price'] . " => $this->products_price \n"
                    );
                }
            }
        }
    }

    public static function getProductName($products_id, $language_id)
    {
        $result = self::find()
            ->alias('p')
            ->select(['p.products_id', 'pd.products_name'])
            ->where(['p.products_id' => $products_id, 'pd.language_id' => $language_id])
            ->joinWith('productsDescription pd', false)
            ->asArray()
            ->one();

        return $result['products_name'];
    }

    public static function getProductIds($products_name, $language_id)
    {
        $result = self::find()
            ->alias('p')
            ->select(['pd.products_name', 'p.products_id'])
            ->where(['pd.language_id' => $language_id])
            ->andWhere(['like', 'pd.products_name', $products_name])
            ->joinWith('productsDescription pd', false)
            ->asArray()
            ->all();
        $products_ids = array();

        foreach ($result as $row) {
            $products_ids[] = $row['products_id'];
        }

        return $products_ids;
    }

    public static function getProductsNameByLanguageId($products_id, $language_id)
    {
        return ProductsDescription::find()
            ->select(['products_name'])
            ->where(['products_id' => $products_id, 'language_id' => $language_id])
            ->limit(1)
            ->scalar();
    }

    public static function getProductsName($products_id, $language_id)
    {
        $products_name = self::getProductsNameByLanguageId($products_id, $language_id);

        if (empty($products_name)) {
            $products_name = self::getProductsNameByLanguageId($products_id, 1);
        }

        return $products_name;
    }

    public static function getProductsBrandUrl($products_list, $language_id)
    {
        $array = [];

        foreach ($products_list as $products_id) {
            $data = null;
            $url_image_data = self::getProductFrontendUrl($products_id);
            if ($url_image_data) {
                $data = [
                    'image_url' => $url_image_data['image_url'],
                    'seo_url' => $url_image_data['seo_url'],
                    'products_name' => self::getProductsName($products_id, $language_id)
                ];
            }

            $array[$products_id] = $data;
        }

        return $array;
    }

    public static function getProductFrontendUrl($products_id)
    {
        $data = null;

        $products = Products::find()
            ->select(['c.categories_parent_path', 'p.products_url_alias', 'ptc.categories_id'])
            ->alias('p')
            ->innerJoin('products_to_categories ptc', 'p.products_id = ptc.products_id')
            ->innerJoin('categories c', 'ptc.categories_id = c.categories_id')
            ->where(['p.products_id' => $products_id])
            ->limit(1)
            ->asArray()
            ->one();

        if ($products) {
            $categories_path = explode('_', trim($products['categories_parent_path']));

            $brand_category = Category::find()
                ->alias('c')
                ->select(['b.image_url', 'b.brand_id', 'b.seo_url AS brand_url', 'c.seo_url AS category_url'])
                ->innerJoin('brand b', 'b.brand_id = c.brand_id')
                ->where(['c.categories_id' => $categories_path])
                ->limit(1)
                ->asArray()
                ->one();

            if ($brand_category) {
                $brand_id = $brand_category['brand_id'];
                $count = (new Query)
                    ->from(['brand_category bc'])
                    ->leftJoin('category c', 'c.categories_id = bc.sub_id')
                    ->leftJoin('brand b', 'b.brand_id = c.brand_id')
                    ->where(['bc.brand_id' => $brand_id, 'bc.type' => 2])
                    ->count('DISTINCT(b.brand_id)', Yii::$app->db_og);

                $image_path = $brand_category['image_url'];
                $url_path = $brand_category['brand_url'] . '/' . ($count > 1 ? $brand_category['brand_url'] . '/' : '') . $brand_category['category_url'] . '/' . $products['products_url_alias'];
                $data = [
                    'image_url' => $image_path,
                    'seo_url' => $url_path,
                ];
            }
        }

        return $data;
    }
}