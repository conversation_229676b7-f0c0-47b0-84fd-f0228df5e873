<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "products".
 *
 * @property int $products_id
 * @property int $products_quantity
 * @property int $products_actual_quantity
 * @property string $products_model
 * @property string $products_price
 * @property string $products_base_currency
 * @property string $products_date_added
 * @property string $products_last_modified
 * @property string $products_date_available
 * @property string $products_weight
 * @property int $products_status
 * @property int $products_display
 * @property int $products_skip_inventory
 * @property int $products_purchase_mode
 * @property int $products_tax_class_id
 * @property int $manufacturers_id
 * @property int $products_ordered
 * @property string $products_ship_price
 * @property int $products_quantity_order
 * @property int $products_pre_order_level
 * @property double $products_eta
 * @property string $products_add_to_cart_msg
 * @property string $products_preorder_msg
 * @property string $products_out_of_stock_msg
 * @property int $products_out_of_stock_level
 * @property string $products_bundle
 * @property string $products_bundle_dynamic
 * @property int $products_bundle_dynamic_qty
 * @property int $products_main_cat_id
 * @property string $products_cat_id_path
 * @property string $products_cat_path
 * @property string $products_url_alias
 * @property int $products_auto_seo
 * @property string $products_flag_id
 * @property int $products_sort_order
 * @property int $custom_products_type_id
 * @property int $custom_products_type_child_id
 * @property string $products_buyback_quantity
 * @property string $products_buyback_price
 * @property string $products_quantity_fifo_cost
 * @property string $products_actual_quantity_fifo_cost
 * @property string $products_payment_mature_period
 * @property int $products_type
 *
 * @property GameProduct $gameProduct
 */
class ProductsBase extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'products_date_added',
                'updatedAtAttribute' => 'products_last_modified',
                'value' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'products_quantity',
                    'products_actual_quantity',
                    'products_status',
                    'products_display',
                    'products_skip_inventory',
                    'products_purchase_mode',
                    'products_tax_class_id',
                    'manufacturers_id',
                    'products_ordered',
                    'products_quantity_order',
                    'products_pre_order_level',
                    'products_out_of_stock_level',
                    'products_bundle_dynamic_qty',
                    'products_main_cat_id',
                    'products_auto_seo',
                    'products_sort_order',
                    'custom_products_type_id',
                    'custom_products_type_child_id',
                    'products_payment_mature_period',
                    'products_type'
                ],
                'integer'
            ],
            [
                [
                    'products_price',
                    'products_weight',
                    'products_ship_price',
                    'products_eta',
                    'products_buyback_price',
                    'products_quantity_fifo_cost',
                    'products_actual_quantity_fifo_cost'
                ],
                'number'
            ],
            [['products_date_added', 'products_last_modified', 'products_date_available'], 'safe'],
            [['products_bundle', 'products_bundle_dynamic'], 'string'],
            [
                [
                    'products_model',
                    'products_cat_id_path',
                    'products_cat_path',
                    'products_url_alias',
                    'products_flag_id',
                    'products_buyback_quantity'
                ],
                'string',
                'max' => 255
            ],
            [['products_base_currency'], 'string', 'max' => 3],
            [
                ['products_add_to_cart_msg', 'products_preorder_msg', 'products_out_of_stock_msg'],
                'string',
                'max' => 128
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'products_quantity' => 'Products Quantity',
            'products_actual_quantity' => 'Products Actual Quantity',
            'products_model' => 'Products Model',
            'products_price' => 'Products Price',
            'products_base_currency' => 'Products Base Currency',
            'products_date_added' => 'Products Date Added',
            'products_last_modified' => 'Products Last Modified',
            'products_date_available' => 'Products Date Available',
            'products_weight' => 'Products Weight',
            'products_status' => 'Products Status',
            'products_display' => 'Products Display',
            'products_skip_inventory' => 'Products Skip Inventory',
            'products_purchase_mode' => 'Products Purchase Mode',
            'products_tax_class_id' => 'Products Tax Class ID',
            'manufacturers_id' => 'Manufacturers ID',
            'products_ordered' => 'Products Ordered',
            'products_ship_price' => 'Products Ship Price',
            'products_quantity_order' => 'Products Quantity Order',
            'products_pre_order_level' => 'Products Pre Order Level',
            'products_eta' => 'Products Eta',
            'products_add_to_cart_msg' => 'Products Add To Cart Msg',
            'products_preorder_msg' => 'Products Preorder Msg',
            'products_out_of_stock_msg' => 'Products Out Of Stock Msg',
            'products_out_of_stock_level' => 'Products Out Of Stock Level',
            'products_bundle' => 'Products Bundle',
            'products_bundle_dynamic' => 'Products Bundle Dynamic',
            'products_bundle_dynamic_qty' => 'Products Bundle Dynamic Qty',
            'products_main_cat_id' => 'Products Main Cat ID',
            'products_cat_id_path' => 'Products Cat Id Path',
            'products_cat_path' => 'Products Cat Path',
            'products_url_alias' => 'Products Url Alias',
            'products_auto_seo' => 'Products Auto Seo',
            'products_flag_id' => 'Products Flag ID',
            'products_sort_order' => 'Products Sort Order',
            'custom_products_type_id' => 'Custom Products Type ID',
            'custom_products_type_child_id' => 'Custom Products Type Child ID',
            'products_buyback_quantity' => 'Products Buyback Quantity',
            'products_buyback_price' => 'Products Buyback Price',
            'products_quantity_fifo_cost' => 'Products Quantity Fifo Cost',
            'products_actual_quantity_fifo_cost' => 'Products Actual Quantity Fifo Cost',
            'products_payment_mature_period' => 'Products Payment Mature Period',
            'products_type' => 'Products Type',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGameProduct()
    {
        return $this->hasOne(GameProduct::class, ['products_id' => 'products_id']);
    }

    public function getProductsCost()
    {
        return $this->hasOne(ProductsCost::class, ['products_id' => 'products_id']);
    }

    public function getCategories()
    {
        return $this->hasMany(Categories::class, ['categories_id' => 'categories_id'])
            ->viaTable(ProductsToCategories::tableName(), ['products_id' => 'products_id']);
    }

    public function getProductsFollowPrice()
    {
        return $this->hasMany(ProductsFollowPrice::class, ['products_id' => 'products_id']);
    }

    public function getProductsToGameBlog()
    {
        return $this->hasMany(ProductsToGameBlog::class, ['products_id' => 'products_id']);
    }

    public function getProductsToCategories()
    {
        return $this->hasOne(ProductsToCategories::class, ['products_id' => 'products_id']);
    }

    public function getProductsDescription()
    {
        return $this->hasMany(ProductsDescription::class, ['products_id' => 'products_id']);
    }

    public function getGamePublisherProduct(){

        return $this->hasOne(GamePublisherProduct::class, ['game_product_id' => 'game_product_id'])->viaTable(GameProduct::tableName(), ['products_id' => 'products_id']);
    }

}
