<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "products_bundles".
 *
 * @property int $bundle_id
 * @property int $subproduct_id
 * @property int $subproduct_qty
 * @property int $subproduct_weight
 */
class ProductsBundles extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_bundles';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['bundle_id', 'subproduct_id'], 'required'],
            [['bundle_id', 'subproduct_id', 'subproduct_qty', 'subproduct_weight'], 'integer'],
            [['bundle_id', 'subproduct_id'], 'unique', 'targetAttribute' => ['bundle_id', 'subproduct_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'bundle_id' => 'Bundle ID',
            'subproduct_id' => 'Subproduct ID',
            'subproduct_qty' => 'Subproduct Qty',
            'subproduct_weight' => 'Subproduct Weight',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducts()
    {
        return $this->hasOne(Products::class, ['products_id' => 'subproduct_id']);
    }
}
