<?php

namespace micro\models;

use Yii;
use offgamers\base\models\enum\ModelAuditActionEnum;

/**
 * This is the model class for table "products_description".
 *
 * @property int $products_id
 * @property int $language_id
 * @property string $products_name
 * @property string $products_alt_name
 * @property string $products_keyword
 * @property string $products_description
 * @property string $products_image
 * @property string $products_image_title
 * @property string $products_description_image
 * @property string $products_description_image_title
 * @property string $products_url
 * @property int $products_viewed
 * @property string $products_location
 * @property string $products_dtu_extra_info_1 DTU deno label
 * @property string $products_dtu_extra_info_2 DTU Deno calculation
 * @property string $products_dtu_extra_info_3 DTU deno label
 */
class ProductsDescription extends \offgamers\base\models\ActiveRecord
{
    protected $log_data_id;

    const LOG_TABLE = 'game_publisher_product';
    const INSERT_LOG_DESCRIPTION = '%s';
    const UPDATE_LOG_DESCRIPTION = '%s';
    const DELETE_LOG_DESCRIPTION = '%s';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['language_id'], 'required'],
            [['language_id', 'products_viewed'], 'integer'],
            [['products_description', 'products_location'], 'string'],
            [['products_name', 'products_alt_name', 'products_url'], 'string', 'max' => 255],
            [['products_keyword', 'products_image', 'products_image_title', 'products_description_image', 'products_description_image_title'], 'string', 'max' => 64],
            [['products_dtu_extra_info_1', 'products_dtu_extra_info_2', 'products_dtu_extra_info_3'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'language_id' => 'Language ID',
            'products_name' => 'Products Name',
            'products_alt_name' => 'Products Alt Name',
            'products_keyword' => 'Products Keyword',
            'products_description' => 'Products Description',
            'products_image' => 'Products Image',
            'products_image_title' => 'Products Image Title',
            'products_description_image' => 'Products Description Image',
            'products_description_image_title' => 'Products Description Image Title',
            'products_url' => 'Products Url',
            'products_viewed' => 'Products Viewed',
            'products_location' => 'Products Location',
            'products_dtu_extra_info_1' => 'Products Dtu Extra Info 1',
            'products_dtu_extra_info_2' => 'Products Dtu Extra Info 2',
            'products_dtu_extra_info_3' => 'Products Dtu Extra Info 3',
        ];
    }

    protected function getDataId()
    {
        if (!$this->log_data_id) {
            $gp_model = GameProduct::find()->select(['game_product_id'])->where(['products_id' => $this->products_id])->one();
            $gpp_model = GamePublisherProduct::find()->select(['game_publisher_product_id'])->where(['game_product_id' => $gp_model->game_product_id])->one();
            if ($gpp_model) {
                $this->log_data_id = $gpp_model->game_publisher_product_id;
            }
        }
        return $this->log_data_id;
    }

    protected function getRequiredField()
    {
        $this->getDataId();
        return ['language_id' => $this->language_id, 'game_publisher_product_id' => $this->log_data_id];
    }

    protected function getUpdateField()
    {
        return ['products_name'];
    }

    protected function isLogRequired($action)
    {
        $ret = false;

        $this->getDataId();

        switch ($action) {
            case ModelAuditActionEnum::UPDATE:
                $ret = true;
                break;

            default:
                break;
        }

        return $ret;
    }
}
