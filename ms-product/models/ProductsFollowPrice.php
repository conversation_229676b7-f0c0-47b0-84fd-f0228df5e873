<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "products_follow_price".
 *
 * @property int $products_id
 * @property int $follow_products_id
 */
class ProductsFollowPrice extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_follow_price';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'follow_products_id'], 'required'],
            [['products_id', 'follow_products_id'], 'integer'],
            [['products_id', 'follow_products_id'], 'unique', 'targetAttribute' => ['products_id', 'follow_products_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'follow_products_id' => 'Follow Products ID',
        ];
    }
}
