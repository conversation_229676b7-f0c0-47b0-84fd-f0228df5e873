<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "products_payment_methods_restrictions".
 *
 * @property string $id
 * @property int $products_id
 * @property string $restriction_mode
 * @property string $restriction_info
 * @property string $created_date
 * @property string $updated_date
 * @property string $changed_by
 */
class ProductsPaymentMethodsRestrictions extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_payment_methods_restrictions';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'restriction_mode'], 'required'],
            [['products_id'], 'integer'],
            [['restriction_info'], 'string'],
            [['created_date', 'updated_date'], 'safe'],
            [['restriction_mode'], 'string', 'max' => 255],
            [['changed_by'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'products_id' => 'Products ID',
            'restriction_mode' => 'Restriction Mode',
            'restriction_info' => 'Restriction Info',
            'created_date' => 'Created Date',
            'updated_date' => 'Updated Date',
            'changed_by' => 'Changed By',
        ];
    }
}
