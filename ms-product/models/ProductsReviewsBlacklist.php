<?php

namespace micro\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "OrdersProductsReviews".
 *
 * @property int $item_id
 * @property int $item_type
 * @property int $blacklist_id
 * @property int $status
 * @property string $created_at
 * @property string $created_by
 * @property string $updated_at
 * @property string $updated_by
 *
 */
class ProductsReviewsBlacklist extends \yii\db\ActiveRecord
{
    public $_updated_by;
    /**
     * {@inheritdoc}
     */

    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public static function tableName()
    {
        return 'products_reviews_blacklist';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
            ],
        ];
    }

    public function rules()
    {
        return [
            [
                [
                    'blacklist_id',
                    'item_id',
                    'status',
                ],
                'integer'
            ],
            [['created_at', 'updated_at'], 'safe'],
            [['item_type', 'created_by', 'updated_by'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'blacklist_id'=>'Blacklist Id',
            'item_id'=>'Item Id',
            'item_type'=>'Item Type',
            'status' => 'Status',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
            'updated_at' => 'updated_at',
            'updated_by' => 'updated_by',
        ];
    }

    public function saveReviewRecords($params)
    {
        $this->item_id = $params['item_id'];
        $this->item_type = $params['item_type'];
        $this->status = 1;
        $this->created_by = $params['user_id'];
        $this->updated_by = $params['user_id'];

        try {
            $this->save();
            return true;
        }catch (Exception $e) {
            return $e;
        }
    }

    public static function getAllBlacklistItem(){
        $category_list = self::find()->select(['item_id'])->where(['status'=>'1','item_type' =>'Category'])->asArray()->all();
        $product_list = self::find()->select(['item_id'])->where(['status'=>'1','item_type' =>'Products'])->asArray()->all();
        $result['category'] = ArrayHelper::getColumn($category_list, 'item_id');
        $result['products'] = ArrayHelper::getColumn($product_list, 'item_id');
        return $result;
    }


}
