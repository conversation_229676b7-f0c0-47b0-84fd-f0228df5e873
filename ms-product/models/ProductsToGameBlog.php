<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "products_to_game_blog".
 *
 * @property int $products_id
 * @property int $game_blog_id
 */
class ProductsToGameBlog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'products_to_game_blog';
    }


    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['products_id', 'game_blog_id'], 'required'],
            [['products_id', 'game_blog_id'], 'integer'],
            [['products_id', 'game_blog_id'], 'unique', 'targetAttribute' => ['products_id', 'game_blog_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'products_id' => 'Products ID',
            'game_blog_id' => 'Game Blog ID',
        ];
    }
}
