<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "publishers_configuration".
 *
 * @property int $publishers_configuration_id ID
 * @property int $publishers_id Publisher ID
 * @property string $publishers_configuration_title Configuration Title, eg. Reminder Amount
 * @property string $publishers_configuration_key Configuration Key
 * @property string $publishers_configuration_value Configuration Value, eg. 5000
 * @property string $publishers_configuration_description Configuration Description, Min Amount for email notification
 * @property int $sort_order sorting
 * @property string|null $last_modified Last update time
 * @property string $date_added Record added time
 * @property int $last_modified_by
 * @property string $use_function function to integrate
 * @property string $set_function Input type, textbox, select box or ...
 */
class PublishersConfiguration extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publishers_configuration';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publishers_id', 'sort_order', 'last_modified_by'], 'integer'],
            [['publishers_configuration_value', 'set_function'], 'required'],
            [['publishers_configuration_value', 'set_function'], 'string'],
            [['last_modified', 'date_added'], 'safe'],
            [['publishers_configuration_title', 'publishers_configuration_key'], 'string', 'max' => 64],
            [['publishers_configuration_description', 'use_function'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publishers_configuration_id' => 'Publishers Configuration ID',
            'publishers_id' => 'Publishers ID',
            'publishers_configuration_title' => 'Publishers Configuration Title',
            'publishers_configuration_key' => 'Publishers Configuration Key',
            'publishers_configuration_value' => 'Publishers Configuration Value',
            'publishers_configuration_description' => 'Publishers Configuration Description',
            'sort_order' => 'Sort Order',
            'last_modified' => 'Last Modified',
            'date_added' => 'Date Added',
            'last_modified_by' => 'Last Modified By',
            'use_function' => 'Use Function',
            'set_function' => 'Set Function',
        ];
    }
}
