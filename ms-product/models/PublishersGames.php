<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "publishers_games".
 *
 * @property int $publishers_games_id
 * @property int $publishers_id Publisher ID
 * @property string $publishers_game Publisher Game
 * @property int $categories_id
 * @property int $publishers_games_status
 * @property float $publishers_games_daily_limit Daily Top-up Limit
 * @property float $publishers_games_today_topped_amount Today Top-up Amount
 * @property string|null $publishers_games_pending_message
 * @property string|null $publishers_games_reloaded_message
 * @property string|null $publishers_games_failed_message
 * @property string|null $publishers_games_remark
 * @property string $publishers_server Publisher Servers in array (json)
 * @property float $publishers_games_daily_topped_amount
 */
class PublishersGames extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publishers_games';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publishers_id', 'categories_id', 'publishers_games_status'], 'integer'],
            [['publishers_games_daily_limit', 'publishers_games_today_topped_amount', 'publishers_games_daily_topped_amount'], 'number'],
            [['publishers_server'], 'required'],
            [['publishers_server'], 'string'],
            [['publishers_game'], 'string', 'max' => 64],
            [['publishers_games_pending_message', 'publishers_games_reloaded_message', 'publishers_games_failed_message', 'publishers_games_remark'], 'string', 'max' => 255],
            [['publishers_id', 'publishers_game'], 'unique', 'targetAttribute' => ['publishers_id', 'publishers_game']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publishers_games_id' => 'Publishers Games ID',
            'publishers_id' => 'Publishers ID',
            'publishers_game' => 'Publishers Game',
            'categories_id' => 'Categories ID',
            'publishers_games_status' => 'Publishers Games Status',
            'publishers_games_daily_limit' => 'Publishers Games Daily Limit',
            'publishers_games_today_topped_amount' => 'Publishers Games Today Topped Amount',
            'publishers_games_pending_message' => 'Publishers Games Pending Message',
            'publishers_games_reloaded_message' => 'Publishers Games Reloaded Message',
            'publishers_games_failed_message' => 'Publishers Games Failed Message',
            'publishers_games_remark' => 'Publishers Games Remark',
            'publishers_server' => 'Publishers Server',
            'publishers_games_daily_topped_amount' => 'Publishers Games Daily Topped Amount',
        ];
    }
}
