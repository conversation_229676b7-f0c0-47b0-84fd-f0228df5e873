<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "publishers_products".
 *
 * @property int $publishers_games_id
 * @property int $products_id
 */
class PublishersProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publishers_products';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publishers_games_id', 'products_id'], 'required'],
            [['publishers_games_id', 'products_id'], 'integer'],
            [['publishers_games_id', 'products_id'], 'unique', 'targetAttribute' => ['publishers_games_id', 'products_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publishers_games_id' => 'Publishers Games ID',
            'products_id' => 'Products ID',
        ];
    }
}
