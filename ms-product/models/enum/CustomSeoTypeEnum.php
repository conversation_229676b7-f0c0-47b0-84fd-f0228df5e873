<?php

namespace micro\models\enum;

class CustomSeoTypeEnum extends \offgamers\base\models\enum\BaseEnum
{
    const HOME_PAGE = 1;
    const BROWSE_PAGE = 2;
    const SEARCH_PAGE = 3;
    const GAME_KEY_PAGE = 4;
    const CATEGORY_PAGE = 5;
    const PRODUCT_PAGE = 6;
    const GAME_BLOG_PAGE = 7;
    const MOBILE_RECHARGE_PAGE = 8;
    const PROMOTION_PAGE = 9;
    const TNC_PAGE = 10;
    const PRIVACY_POLICY_PAGE = 11;

    public static $messageCategory = 'app';

    public static $list = [
        self::HOME_PAGE => 'Home',
        self::BROWSE_PAGE => 'Browse',
        self::SEARCH_PAGE => 'Search',
        self::GAME_KEY_PAGE => 'Game Key',
        self::CATEGORY_PAGE => 'Category',
        self::PRODUCT_PAGE => 'Products',
        self::GAME_BLOG_PAGE => 'Game Blog',
        self::MOBILE_RECHARGE_PAGE => 'Mobile Recharge',
        self::PROMOTION_PAGE => 'Promotion',
        self::TNC_PAGE => 'Terms & Conditions',
        self::PRIVACY_POLICY_PAGE => 'Privacy Policy'
    ];

}