{"name": "offgamers/microservices-publisher", "description": "Yii2 Publisher Shared Components", "type": "yii2-extension", "version": "1.0.0", "minimum-stability": "dev", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "Glendon <PERSON>o", "email": "<EMAIL>"}], "require": {"php": ">=7.4.0", "yiisoft/yii2": "*", "guzzlehttp/guzzle": "^6.3@dev", "offgamers/microservices-multiservice": "*", "ext-json": "*", "yiisoft/yii2-composer": "~2.0.4", "phpseclib/phpseclib": "~3.0.0"}, "autoload": {"psr-4": {"offgamers\\publisher\\": ""}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}, {"type": "vcs", "url": "**************:tech-ogm/toolbox-multiservice.git"}], "config": {"allow-plugins": {"yiisoft/yii2-composer": true}}}