<?php

namespace offgamers\publisher\models;

use Yii;

/**
 * This is the model class for table "game_publisher".
 *
 * @property int $game_publisher_id
 * @property string $title
 * @property string $profile
 * @property int $status
 * @property string $last_sync
 * @property string $created_at
 * @property string $updated_at
 *
 * @property GamePublisherSetting[] $gamePublisherSettings
 */
class GamePublisher extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'game_publisher';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        $enum = Yii::$app->enum->getStatus();

        return [
            [['status'], 'integer'],
            ['status', 'in', 'range' => $enum::getConstantsByName()],
            [['title','profile'],'required'],
            [['last_sync', 'created_at', 'updated_at'], 'safe'],
            [['title', 'profile'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'game_publisher_id' => 'Game Publisher ID',
            'title' => 'Title',
            'profile' => 'Profile',
            'status' => 'Status',
            'last_sync' => 'Last Sync',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGamePublisherSettings()
    {
        return $this->hasMany(GamePublisherSetting::className(), ['game_publisher_id' => 'game_publisher_id']);
    }
}
