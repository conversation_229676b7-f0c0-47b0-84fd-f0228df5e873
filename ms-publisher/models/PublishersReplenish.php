<?php

namespace offgamers\publisher\models;

use Yii;

/**
 * This is the model class for table "publishers_replenish".
 *
 * @property string $publishers_replenish_id
 * @property string $publishers_name
 * @property int $publishers_status
 * @property string $publishers_remark
 * @property string $publishers_supplier_id
 * @property int $publishers_payment_term
 * @property string $publishers_sku_header
 * @property string $publishers_api_provider
 * @property string $publishers_api_method
 * @property string $last_modified
 * @property string $date_added
 * @property int $last_modified_by
 * @property int $sort_order
 */
class PublishersReplenish extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'publishers_replenish';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publishers_name', 'publishers_remark', 'publishers_sku_header', 'publishers_api_provider', 'publishers_api_method'], 'required'],
            [['publishers_status', 'publishers_supplier_id', 'publishers_payment_term', 'last_modified_by', 'sort_order'], 'integer'],
            [['publishers_remark'], 'string'],
            [['last_modified', 'date_added'], 'safe'],
            [['publishers_name', 'publishers_sku_header', 'publishers_api_provider'], 'string', 'max' => 32],
            [['publishers_api_method'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'publishers_replenish_id' => 'Publishers Replenish ID',
            'publishers_name' => 'Publishers Name',
            'publishers_status' => 'Publishers Status',
            'publishers_remark' => 'Publishers Remark',
            'publishers_supplier_id' => 'Publishers Supplier ID',
            'publishers_payment_term' => 'Publishers Payment Term',
            'publishers_sku_header' => 'Publishers Sku Header',
            'publishers_api_provider' => 'Publishers Api Provider',
            'publishers_api_method' => 'Publishers Api Method',
            'last_modified' => 'Last Modified',
            'date_added' => 'Date Added',
            'last_modified_by' => 'Last Modified By',
            'sort_order' => 'Sort Order',
        ];
    }
}
