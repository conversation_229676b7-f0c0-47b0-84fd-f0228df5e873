<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Acepayz extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher,$error_code;

    const cache_token_acepayz = 'cache_token_acepayz_dtu';

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getOperatorListByCountryId($country_id)
    {
        $request_data = [
            'action' => 'pricelist',
            'info_type' => 'country',
            'content' => $country_id
        ];
        $data = $this->sendRequest($request_data);

        return $data;
    }

    public function getDenominationListByOperatorId($operator_id)
    {
        $request_data = [
            'action' => 'pricelist',
            'info_type' => 'operator',
            'content' => $operator_id
        ];
        $data = $this->sendRequest($request_data);

        return $data;
    }

    protected function getToken()
    {
        // Get token cache if exist
        $this->getExistingConfig();
        if ($token = Yii::$app->cache->get(static::cache_token_acepayz)) {
            return $token;
        }

        $params = [
            "Username" => $this->configuration_data['API_LOGIN'],
            "Password" => $this->configuration_data['API_SECRET'],
        ];

        $params['Signature'] = $this->generateSignature($params, 'init');

        $response = $this->sendRequest($params, $this->configuration_data['API_ENDPOINT_AUTH']);

        $data = $this->checkError($response);

        if (!empty($data['Token'])) {
            // cache token last 20hours
            Yii::$app->cache->set(static::cache_token_acepayz, $data['Token'], 72000);
            return $data['Token'];
        }

        return false;
    }

    protected function generateSignature($body, $type)
    {
        $string = '';
        switch ($type) {
            case 'init':
                $params = ['Username', 'Password'];
                break;
            case 'order':
                $body['MerchantKey'] = $this->configuration_data['API_KEY'];
                $params = ['Username', 'Password', 'ReferenceCode', 'ProductCode', 'ClientID', 'MerchantKey'];
                break;
            case 'query':
                $body['MerchantKey'] = $this->configuration_data['API_KEY'];
                $params = ['Username', 'Password', 'ReferenceCode', 'MerchantKey'];
                break;
        }
        foreach ($params as $key) {
            if (isset($body[$key])) {
                $string .= $body[$key];
            }
        }
        return md5($string);
    }

    protected function sendRequest($params, $url, $token = '')
    {
        $this->initClient();

        $baseUrl = $this->configuration_data['API_URL'];

        $headers['Accept'] = 'application/json';
        if ($token) {
            $headers['Authorization'] = 'Bearer ' . $token;
        }

        $options = array(
            'headers' => $headers,
            'json' => $params,
            'http_errors' => false
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->client->request('POST', $baseUrl . '/' . $url, $options);
    }

    protected function checkError($response)
    {
        try {
            $data = Json::decode($response->getBody());
            $status = $response->getStatusCode();
            if ($status == 200) {
                if (!empty($data['Token'])) {
                    return $data;
                } elseif (isset($data['StatusCode'])) {
                    if ($data['StatusCode'] == 000) {
                        return $data;
                    } elseif ($data['StatusCode'] == 001) {
                        $this->status_flag = -1;
                        $this->error_code = $data['StatusCode'];
                        $this->error_msg = $this->checkStatusMessage($data['StatusCode']) . "\n" . 'ReferenceCode : ' . $data['ReferenceCode'];
                        $this->error = ['message' => $this->error_msg];
                    } else {
                        $this->error_code = $data['StatusCode'];
                        $this->error_msg = $this->checkStatusMessage($data['StatusCode']). "\n" . 'ReferenceCode : ' . $data['ReferenceCode'];
                        $this->error = ['message' => $this->error_msg];
                    }
                } else {
                    $this->error = [
                        'data' => $data
                    ];
                }
            } else {
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => ($data['message'] ?? $data)
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        $exception_array = [002, 003, 004];

        if (!empty($this->error_code) && !in_array($this->error_code,$exception_array)) {
            $this->errorReporter($this->checkStatusMessage($this->error_code));
        }

        return false;
    }

    protected function checkStatusMessage($code)
    {
        $message = '';
        switch ($code) {
            case 001:
                $message = 'Fail';
                break;
            case 002:
                $message = 'Pending';
                break;
            case 003:
                $message = 'Duplicate ID';
                break;
            case 004:
                $message = 'Transaction not found';
                break;
            case 005:
                $message = 'Invalid Account / Phone No';
                break;
            case 101:
                $message = 'Invalid Login';
                break;
            case 102:
                $message = 'Invalid Signature';
                break;
            case 103:
                $message = 'Insufficient Credit';
                break;
            case 201:
                $message = 'Invalid Product Code';
                break;
            case 202:
                $message = 'Invalid Denomination';
                break;
            case 203:
                $message = 'Invalid Price';
                break;
            case 204:
                $message = 'Invalid Cost';
                break;
            case 205:
                $message = 'Insufficient Inventory';
                break;
        }

        return "($code) " . $message;
    }

    public function errorReporter($error)
    {
        Yii::$app->slack->send('Error fetching Acepayz api', array(
            array(
                'color' => 'warning',
                'text' => Json::encode(
                    $error
                )
            )
        ));
    }


}