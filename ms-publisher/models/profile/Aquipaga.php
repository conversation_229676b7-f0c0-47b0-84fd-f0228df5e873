<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class Aquipaga extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $username;
    protected $password;
    protected $terminal_id;
    protected $currency_code;
    protected $low_margin;
    protected $min_margin;
    protected $token = null;
    protected $token_expiry = '';

    public $error_code;
    public $error_msg;
    public $error;
    protected $max_connection;

    protected $account;

    protected $request_id;

    const create_order_url = '/DoTransaction';
    const check_order_url = '/GetTransaction';
    const authenticate_url = '/Authenticate';

    const catalog_url = '/GetAvailableProduct';

    const PRODUCT_TYPE_CODES = [
        'Voucher',
    ];

    public function getConfig($reload_config = false)
    {
        if ($reload_config || empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->username = $config_list['USERNAME'];
            $this->password = $config_list['PASSWORD'];
            $this->terminal_id = $config_list['TERMINAL_ID'];
            $this->currency_code = $config_list['CURRENCY_CODE'];
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];
            $this->token = $config_list['TOKEN'];
            $this->token_expiry = $config_list['TOKEN_EXPIRY'];
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'Aquipaga ' . $this->getAccount(),
            'profile' => 'Aquipaga',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Aquipaga Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Aquipaga Publisher Not Found'
                )
            ));
        }
    }

    protected function getToken($force_refresh = false, $attempt = 0) {
        $token = null;
        if (!$force_refresh) {
            $token = $this->token;
        }
        if (!$token || time() > $this->token_expiry) {
            if ($this->obtainLock()) {
                $token = $this->apiAuthenticate();
                if ($token) {
                    $this->saveTokenToDb($token);
                }
            } else {
                if ($attempt < 3) {
                    sleep(3);
                    $this->getConfig(true);
                    return $this->getToken($force_refresh, $attempt + 1);
                } else {
                    throw new \Exception('Failed to get token from Aquipaga (' . $this->publisher_id . ')');
                }
            }
        }
        return $token;
    }

    protected function obtainLock()
    {
        $token_lock = PublisherSetting::find()->where(['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = PublisherSetting::find()->where(['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token_lock && empty($token_lock->value)) {
            $result = PublisherSetting::updateAll(['value' => '1'], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK', 'value' => $token_lock->value]);
            if ($result > 0) {
                PublisherSetting::updateAll(['value' => (string)time()], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token_lock && $token_lock->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    protected function releaseLock()
    {
        PublisherSetting::updateAll(['value' => '0'], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK']);
    }

    protected function saveTokenToDb($token)
    {
        $this->token = $token;
        // Document indicate 59 min expiry, we deduct 1 min out for safety
        $this->token_expiry = time() + (58 * 60);

        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher_id, 'key' => 'TOKEN']);
        PublisherSetting::updateAll(['value' => (string)$this->token_expiry], ['publisher_id' => $this->publisher_id, 'key' => 'TOKEN_EXPIRY']);
        $this->releaseLock();
    }

    protected function apiAuthenticate()
    {
        $data = [
            "Username" => $this->username,
            "Password" => $this->password,
            "TerminalKey" => $this->terminal_id,
        ];
        $headers = [
            'Content-Type' => 'application/json',
        ];

        $url = $this->base_url . static::authenticate_url;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
            'json' => $data,
        ];

        $response = $this->client->request('POST', $url, $options);
        if (($response_body = $response->getBody()) && $response_array = JSON::decode($response_body)) {
            if ($response_array && !empty($response_array['Token'])) {
                return $response_array['Token'];
            }
        }
        $this->checkError($response);
    }

    protected function sendRequest($path, $data, $is_retry = false)
    {
        $token = $this->getToken($is_retry);
        if (!$token) {
            return;
        }
        $data = array_merge($data, [
            'token' => $token,
        ]);
        $headers = [
            'Content-Type' => 'application/json',
        ];

        $url = $this->base_url . $path;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
            'json' => $data,
        ];

        $response = $this->client->request('POST', $url, $options);
        if (($response_body = $response->getBody()) && $response_array = JSON::decode($response_body)) {
            if ($response_array && !isset($response_array['OperationSucceeded']) || !$response_array['OperationSucceeded']) {
                if (isset($response_array['Error']) && in_array($response_array['Error'], [13, 14])) {
                    //13 = InvalidCredentials
                    //14 = TokenExpired
                    return $this->api($path, $data, true);
                } else if (isset($response_array['ErrorText']) && in_array($response_array['ErrorText'], ['Token expired', 'Invalid Credentials'])) {
                    return $this->api($path, $data, true);
                }
            }
        }
        return $response;
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $data = Json::decode($response->getBody());
            if (isset($data['OperationSucceeded']) && $data['OperationSucceeded']) {
                return $data;
            } else if (!$data || !isset($data['Error']) || !isset($data['ErrorText'])) {
                $error_msg = $data;
            } else {
                $error_msg = $data['Error'] . ' : ' . $data['ErrorText'];
                $this->error_code = $data['Error'];
                $this->error_msg = $data['ErrorText'];
            }
            $this->error = [
                'http_status' => $response->getStatusCode(),
                'error' => $error_msg
            ];
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
