<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Astropay extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $error, $configuration_data, $token = null, $token_expiry, $headers, $error_code;

    const AUTH_API_URL = "api-auth";

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
                $this->token = $this->configuration_data['TOKEN'];
                $this->token_expiry = $this->configuration_data['TOKEN_EXPIRY'];
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $path, $headers, $data = null, $tag = null)
    {
        $this->initClient(1, isset($tag) ? $tag : "");

        $options = array(
            'headers' => $headers,
            'http_errors' => false
        );

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $path, $options);
    }

    public function getPublisher()
    {
        $publisher = Publisher::findOne([
            'title' => 'Astropay',
            'profile' => 'Astropay',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Astropay Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Astropay Publisher Not Found'
                )
            ));
        }
    }

    /**
     * Get Token Lock Status
     * @return bool
     */
    protected function obtainLock(): bool
    {
        $token_lock = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token_lock && empty($token_lock->value)) {
            $result = PublisherSetting::updateAll(['value' => '1'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK', 'value' => $token_lock->value]);
            if ($result > 0) {
                PublisherSetting::updateAll(['value' => (string)time()], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token_lock && $token_lock->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    /**
     * Reset Token
     * @return void
     */
    protected function resetToken()
    {
        $this->token = '';
        $this->token_expiry = 0;
    }

    /**
     * Save token to database
     * @return void
     */
    protected function saveTokenToDb()
    {
        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher, 'key' => 'TOKEN']);
        PublisherSetting::updateAll(['value' => (string)$this->token_expiry], ['publisher_id' => $this->publisher, 'key' => 'TOKEN_EXPIRY']);
        $this->releaseLock();   
    }

    /**
     * @return void
     */
    protected function releaseLock()
    {
        PublisherSetting::updateAll(['value' => '0'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK']);
    }

    /**
     * Get Token Code And Set Header For Astropay
     * @return void
     * @throws \Exception
     */
    protected function getTokenFromPublisher()
    {
        $params = [
            "api_key" => $this->configuration_data["API_KEY"],
            "api_secret" => $this->configuration_data["API_SECRET"]
        ];

        $response = $this->sendRequest('POST', static::AUTH_API_URL, array(),  $params);
        $status = $response->getStatusCode();
        $data = Json::decode($response->getBody());

        if ($status == 200) {
            if (!empty($data)) {
                if (isset($data['data']) && isset($data['data']['access_token']) && isset($data['data']['expires_in'])) {
                    $this->token = $data['data']['access_token'];
                    #Expiry Time
                    $this->token_expiry = time() + $data['data']['expires_in'];
                    $this->saveTokenToDb();
                } else {
                    if (isset($data['error']) && isset($data['message']) && isset($data['internal_term'])) {
                        $this->error_msg = $data['internal_term'] . ' - ' . $data['message'];
                    }
                }
            }
        }

        if (!$this->token) {
            throw new \Exception($this->error_msg ?: "Failed to get token from Astropay");
        }
    }

    protected function setHeader()
    {
        if ($this->token) 
        {
            $this->headers = [
                "Content-Type" => "application/json",
                "Authorization" => "bearer " . $this->token
            ];
        }

    }

    protected function checkError($response)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data)) {
                    if (isset($data['uuid']) && isset($data['cards']) && !isset($data['error'])) {
                        return $data;
                    } 
                }
            } else {
                if (isset($data['error']) && isset($data['internal_term'])) {
                    if ($data['internal_term'] == 'error_jwt_exception') 
                    {
                        //Reset Token Due to Token expired before Expiry Time, Token not valid
                        $this->resetToken();
                        $this->saveTokenToDb();
                    }
                    $this->error_code = $data['error'];
                    $this->error_msg = $data['internal_term'] . ' - ' . ($data['message'] ?? "") . (isset($data['detail']) ? Json::encode($data['detail']) : "") ;
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (Astropay) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }
}