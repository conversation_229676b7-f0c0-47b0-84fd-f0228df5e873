<?php

namespace offgamers\publisher\models\profile;

use offgamers\base\traits\GuzzleTrait;
use Psr\Http\Message\ResponseInterface;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;
use yii\helpers\ArrayHelper;
use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;

class Bamboo extends \yii\base\Model {
    use GuzzleTrait;

    /**
     * @var int publisher id
     */
    protected int $publisher_id;
    protected int $log_publisher_id;
    /**
     * @var string base url
     */
    protected string $base_url;
    /**
     * @var string username
     */
    protected string $username;
    /**
     * @var string secret
     */
    protected string $secret;
    protected string $notification_url;
    protected string $notification_secret_key;
    /**
     * @var int error code from publisher
     */
    protected $error_code;
    /**
     * @var string error message for error handler
     */
    public $error_msg;
    protected const API_PATH = 'api/integration/v1.0/';
    /**
     *  URL to retrieve product list
     */
    protected const CATEGORY_URL = 'api/integration/v2.0/catalog';
    /**
     * URL to retrieve account list
     */
    protected const ACCOUNT_URL = self::API_PATH.'accounts';
    /**
     * URL to retrieve order list
     */
    protected const ORDER_URL = self::API_PATH.'orders/checkout';
    /**
     * URL to check order
     */
    protected const CHECK_ORDER_URL = self::API_PATH.'orders/';

    /**
     *  URL to set/retrieve notification url and secret key
     */
    protected const NOTIFICATION_URL = self::API_PATH.'notification';

    public function getConfig($reload_config = false)
    {
        if (empty($this->base_url) || $reload_config) {
            $publisher = Publisher::find()->where(['profile' => 'Bamboo', 'publisher_id' => $this->publisher_id])->exists();
            if ($publisher) {
                $config_list = ArrayHelper::map(PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(), 'key', 'value');
                if (count($config_list) === 6) {
                    $this->log_publisher_id = $config_list['PUBLISHER_ID'];
                    $this->base_url = rtrim($config_list['BASE_URL'], '/');
                    $this->username = $config_list['USERNAME'];
                    $this->secret = $config_list['SECRET'];
                    $this->notification_url = $config_list['NOTIFICATION_URL'];
                    $this->notification_secret_key = $config_list['NOTIFICATION_SECRET_KEY'];
                } else {
                    throw new InvalidArgumentException('Invalid Publisher Configuration' . count($config_list));
                }
            } else {
                throw new InvalidArgumentException('Invalid Publisher');
            }
        }
    }

    /**
     * @return array
     */
    protected function getAuthParams()
    {
        return [$this->username, $this->secret];
    }

    /**
     * @param ResponseInterface $response
     * @return array|null
     * @throws \Exception
     */
    protected function verifyResponse(ResponseInterface $response): ?array
    {
        $this->error_msg = '';

        $status_code = $response->getStatusCode();
        $response_body = $response->getBody();
        try {
            $data = Json::decode($response_body);
            if ($status_code < 300) {
                return $data;
            } elseif (isset($data['error'])) {
                $this->error_msg = $data['error'];
            } else {
                $this->error_msg = $status_code . ' - ' . $response->getReasonPhrase();
            }
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage() . ' - ' . (string)$response_body;
            throw new \Exception($this->error_msg);
        }

        return null;
    }

    /**
     * @param string $method
     * @param string $path
     * @param array $headers
     * @param array $params
     * @param array $auth
     * @return ResponseInterface|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function sendRequest(string $method, string $path, array $data): ?ResponseInterface
    {
        $auth_params = $this->getAuthParams();
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode("{$auth_params[0]}:{$auth_params[1]}"),
        ];
        $url = $this->base_url . '/' . $path;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
        ];

        if (strtoupper($method) === 'GET') {
            if (!empty($data)) {
                $url .= '?' . http_build_query($data);
            }
        } else {
            if (!empty($data)) {
                $options['json'] = $data;
            }
        }

        $this->initClient();

        return $this->client->request($method, $url, $options);
    }
}
