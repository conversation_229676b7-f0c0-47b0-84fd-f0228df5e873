<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;


/**
 * Summary of Bitnovo
 */
class Bitnovo extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $configuration_data;

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $url, $data = null)
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));

        $headers = [
            'security-api-username' => $this->configuration_data['USERNAME'],
            'security-api-key' => $this->configuration_data['API_KEY']
        ];

        $options = array(
            'headers' => $headers,
            'http_errors' => false
        );

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $url, $options);
    }

    public function getPublisher()
    {
        $publisher = Publisher::findOne([
            'title' => 'Bitnovo',
            'profile' => 'Bitnovo',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Bitnovo Publisher Details', array(
                    array(
                        'color' => 'warning',
                        'text' => 'Bitnovo Publisher Not Found'
                    )
                )
            );
        }
    }
}