<?php

namespace offgamers\publisher\models\profile;

use offgamers\base\models\OutgoingRequestLog;
use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\FileHelper;
use yii\helpers\Json;

class BlackHawkNetworkV2 extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $cert_file;

    protected $cert_secret;
    protected $mid;
    protected $low_margin;
    protected $min_margin;

    protected $apple_product_configuration_id_list;

    public $error_code;
    public $error_msg;
    public $error;
    public $status_flag = 0;
    protected $max_connection;

    protected $cert_path;

    const order_url_prefix = 'eGiftProcessing/v1';
    const create_order_url = 'generateEGift';
    const void_order_url = 'voidEGift';
    const reverse_order_url = 'reverseEGift';

    const catalog_url = 'productCatalogManagement/v1/productCatalogs';

    public function __construct($config = [])
    {
        $this->cert_path = Yii::getAlias('@runtime');
        parent::__construct($config);
    }

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = $config_list['BASE_URL'];
            $this->cert_file = $config_list['CERT_FILE']; // Filename
            $this->cert_secret = $config_list['CERT_SECRET'];
            $this->mid = $config_list['MID'];
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];
            $this->apple_product_configuration_id_list = (!empty($config_list['APPLE_PRODUCT_CONFIGURATION_ID']) ? explode(',', $config_list['APPLE_PRODUCT_CONFIGURATION_ID']) : []);

            // Set the local directory for the certificate file
            $cert_path = $this->cert_path . '/' . $this->cert_file;

            // Check the certificate files exist or not
            if (!file_exists($cert_path)) {
                $s3 = Yii::$app->aws->getS3('BUCKET_ENCRYPT_KEY');
                $content = $s3->getContent($this->cert_file);
                if ($content !== null) {
                    $path = dirname($cert_path);
                    FileHelper::createDirectory($path, 755);
                    file_put_contents($cert_path, $content);
                } else {
                    throw new Exception('Failed to download certificate file');
                }
            }
        }
    }

    protected function getProductListByCatalog($catalog_url)
    {
        $response = $this->sendRequest('GET', $catalog_url);
        $product_list = [];
        if ($data = $this->checkError($response)) {
            if (isset($data['details']['productIds'])) {
                foreach ($data['details']['productIds'] as $product) {
                    $product_list[] = $product;
                }
            }
        }
        return $product_list;
    }

    protected function getProductCatalog()
    {
        $response = $this->sendRequest('GET', $this->base_url . '/' . self::catalog_url);
        $catalog_list = [];
        if ($data = $this->checkError($response)) {
            if (isset($data['results'])) {
                foreach ($data['results'] as $catalog) {
                    $catalog_list[] = $catalog['entityId'];
                }
            }
        }
        return $catalog_list;
    }

    protected function sendRequest($request_type, $url, $request_id = null, $data = [], $headers = [])
    {
        if ($request_id) {
            $headers = array_merge(['requestId' => $request_id], $headers);
        }

        $params = array(
            'http_errors' => false,
            'headers' => $headers,
            'cert' => [
                $this->cert_path . '/' . $this->cert_file,
                $this->cert_secret
            ],
            'curl' => [CURLOPT_SSLCERTTYPE => 'p12'],
        );

        if ($data) {
            $params['json'] = $data;
        }

        if (!empty(Yii::$app->params['proxy'])) {
            $params['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request($request_type, $url, $params);
    }

    protected function getFakeRequestId()
    {
        // Generate Fake System Trace Audit Number
        return Yii::$app->db_log->createCommand('select max(outgoing_request_log_id) as max from ' . OutgoingRequestLog::tableName())->queryScalar();
    }

    protected function reverseTransaction($request_id)
    {
        $data = [
            'reversalEGiftRequestId' => $request_id
        ];

        $request_id = 'OG_R_' . $this->getFakeRequestId();

        $response = $this->sendRequest('POST', $this->base_url . '/' . self::order_url_prefix . '/' . self::reverse_order_url, $request_id, $data);

        if ($data = $this->checkError($response)) {
            if ($data['isReversal'] && $data['transactionStatus'] == "APPROVED") {
                $this->status_flag = -1;
                return true;
            }
        }
        elseif($this->error_code === 'original.transaction.not.found'){
            $this->status_flag = -1;
            return true;
        }

        return false;
    }

    protected function checkError($response, $request_id = 0, $reverse_on_timeout = false)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                return $data;
            } elseif ($status == 504 && $reverse_on_timeout) {
                $count = 0;
                while ($count < 3) {
                    if ($this->reverseTransaction($request_id)) {
                        break;
                    } else {
                        $count++;
                        sleep(30);
                    }
                }
            } else {
                if (isset($data['errorCode']) && isset($data['message'])) {
                    $error_msg = $data['errorCode'] . ' : ' . $data['message'];
                    $this->error_code = $data['errorCode'];
                    $this->error_msg = $data['message'];
                } else {
                    $error_msg = $data;
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }

    protected function request($method, $url, $options)
    {
        $result = $this->client->request($method, $url, $options);
        return $result;
    }
}