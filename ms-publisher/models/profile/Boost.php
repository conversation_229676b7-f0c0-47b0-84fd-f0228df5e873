<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Boost extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $error, $configuration_data;

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $path, $data = null)
    {
        $this->initClient();

        $data['apiKey'] = $this->configuration_data['API_KEY'];

        $options = array(
            'http_errors' => false
        );

        if (!empty($data)) {
            $options['form_params'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $path, $options);
    }

    public function getPublisher()
    {
        $publisher = Publisher::findOne([
            'title' => 'Boost',
            'profile' => 'Boost',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Boost Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Boost Publisher Not Found'
                )
            ));
        }
    }
}