<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\GamePublisherSetting;
use Yii;
use yii\helpers\Json;

abstract class CodesWholeSale extends \yii\base\Model
{
    use GamePublisherTrait;

    protected $token;
    protected $token_expiry;

    static $token_url = '/oauth/token';
    static $product_url = '/v2/products';

    protected function getToken($retry_attempt = 0)
    {
        $refresh_config = ($retry_attempt > 0);
        $this->getExistingConfig($refresh_config);

        if ($retry_attempt > 3) {
            throw new \Exception("Fail to get token from Codeswholesales");
        }

        $this->token = $this->configuration_data['API_TOKEN'];
        $this->token_expiry = $this->configuration_data['API_TOKEN_EXPIRY'];

        if (empty($this->token) || time() > $this->token_expiry) {
            $this->getTokenFromApi($retry_attempt);
        } elseif (time() + 3 >= $this->token_expiry) {
            sleep(3);
            $retry_attempt++;
            $this->token = $this->getToken($retry_attempt);
        }

        return $this->token;
    }

    protected function getTokenFromApi($retry_attempt)
    {
        if ($this->obtainLock()) {
            $this->initClient();
            $url = $this->configuration_data['API_ENDPOINT'] . static::$token_url;

            $body = [
                'grant_type' => 'client_credentials',
                'client_id' => $this->configuration_data['API_KEY'],
                'client_secret' => $this->configuration_data['API_SECRET']
            ];

            $options = array(
                'form_params' => $body,
            );

            if (!empty(Yii::$app->params['proxy'])) {
                $options['proxy'] = Yii::$app->params['proxy'];
            }

            $response = $this->client->request('POST', $url, $options, 3)->getBody();
            try {
                if ($response = Json::decode($response)) {
                    if (isset($response['access_token']) && isset($response['expires_in'])) {
                        if ($response['expires_in'] > 3) {
                            $this->token = $response['access_token'];
                            $this->token_expiry = time() + $response['expires_in'];
                            $this->setToken($this->token, $this->token_expiry);
                            $this->releaseLock();
                            return;
                        }
                    }
                }
            } catch (\Exception $e) {
                // Do nothing, retry will handle
            }

            $this->releaseLock();
        }

        sleep(3);
        $retry_attempt++;
        $this->getToken($retry_attempt);
    }

    protected function setToken($token, $expiry)
    {
        GamePublisherSetting::updateAll(['value' => $token], ['game_publisher_id' => $this->publisher, 'key' => 'API_TOKEN']);
        GamePublisherSetting::updateAll(['value' => $expiry], ['game_publisher_id' => $this->publisher, 'key' => 'API_TOKEN_EXPIRY']);
    }

    protected function clearToken()
    {
        $this->token = null;
        $this->token_expiry = null;
        $this->setToken('', '');
    }

    protected function obtainLock()
    {
        $token = GamePublisherSetting::find()->where(['game_publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = GamePublisherSetting::find()->where(['game_publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token && $token->value == '0') {
            $result = GamePublisherSetting::updateAll(['value' => '1'], ['game_publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK', 'value' => '0']);

            if ($result > 0) {
                GamePublisherSetting::updateAll(['value' => (string)time()], ['game_publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token && $token->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    protected function releaseLock()
    {
        GamePublisherSetting::updateAll(['value' => '0'], ['game_publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK']);
    }

    public function getAPIProvider()
    {
        return 'CODESWHOLESALE';
    }

    public function getProductCatalog()
    {
        $this->initClient();
        $this->getToken();

        $url = $this->configuration_data['API_ENDPOINT'] . static::$product_url;

        $options = array(
            'http_errors' => false,
            'headers' => ['Authorization' => 'bearer ' . $this->token],
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        $response = $this->client->request('GET', $url, $options, 3)->getBody();

        return $response;
    }

    protected function getProductByPublisherId($product_id)
    {
        $this->initClient();
        $this->getToken();

        $url = $this->configuration_data['API_ENDPOINT'] . static::$product_url . '/' . $product_id;

        $options = array(
            'http_errors' => false,
            'headers' => ['Authorization' => 'bearer ' . $this->token],
        );

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        $response = $this->client->request('GET', $url, $options, 3);

        return $this->checkError($response);
    }

    protected function filter_special_char($str)
    {
        $RegExp1 = "@%[\dA-F]{2}@";
        $RegExp2 = "/\$|,|@|#|~|`|\%|\*|\^|\&|\(|\)|\+|\=|\[|\]|\[|\}|\{|\;|\:|\"|\<|\>|\?|\||\|\!|\$|\./";
        $RegExp3 = "/'|\_|-{1,}| {1,}|\/{1,}/";
        $RegExp4 = "/\ {1,}/";

        $str = urlencode($str);
        $str = preg_replace($RegExp1, ' ', $str);
        $str = preg_replace($RegExp2, ' ', $str);
        $str = preg_replace($RegExp3, ' ', $str);
        $str = trim($str);
        $str = preg_replace($RegExp4, '-', $str);
        return strtolower($str);
    }

    protected function findCostPrice($data)
    {
        foreach ($data as $val) {
            if ($val['to'] == null) {
                return $val['value'];
            }
        }

        return 0;
    }

    protected function isPreOrder($date)
    {
        return (date_create($date) > date_create());
    }

    public function checkError($response)
    {
        $status = $response->getStatusCode();
        if ($status === 401) {
            $this->clearToken();
        }
        return Json::decode($response->getBody());
    }

}