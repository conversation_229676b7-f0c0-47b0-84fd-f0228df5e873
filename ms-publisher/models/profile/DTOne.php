<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class DTOne extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher;

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getOperatorIdByPhoneNumber($prefix, $phone)
    {
        $this->initClient();
        $request_data = [
            'action' => 'msisdn_info',
            'destination_msisdn' => '+' . $prefix . $phone
        ];
        $data = $this->sendRequest($request_data);
        return simplexml_load_string((string)$data->getBody());
    }

    public function getCountryList()
    {
        $request_data = [
            'action' => 'pricelist',
            'info_type' => 'countries'
        ];
        $data = $this->sendRequest($request_data);

        return $data;
    }

    public function getGNSOperator($service_id)
    {
        $data = $this->sendGNSRequest('GET', 'operators?service_id='.$service_id);

        return $data;
    }

    public function getOperatorListByCountryId($country_id)
    {
        $request_data = [
            'action' => 'pricelist',
            'info_type' => 'country',
            'content' => $country_id
        ];
        $data = $this->sendRequest($request_data);

        return $data;
    }

    public function getDenominationListByOperatorId($operator_id)
    {
        $request_data = [
            'action' => 'pricelist',
            'info_type' => 'operator',
            'content' => $operator_id
        ];
        $data = $this->sendRequest($request_data);

        return $data;
    }

    protected function generateSignature($key)
    {
        return md5($this->configuration_data['API_LOGIN'] . $this->configuration_data['API_TOKEN'] . $key);
    }

    protected function generateXmlFromArray($array)
    {
        $xml = new \SimpleXMLElement('<xml/>');
        foreach ($array as $key => $value) {
            $xml->addChild($key, $value);
        }
        return $xml->asXML();
    }

    public function sendRequest($data)
    {
        $this->getExistingConfig();
        $key = (int)(microtime(true) * 10000);
        $default_array = [
            'login' => $this->configuration_data['API_LOGIN'],
            'key' => $key,
            'md5' => $this->generateSignature($key)
        ];

        $url = $this->configuration_data['API_ENDPOINT'];
        $request_data = array_merge($default_array, $data);

        $options = [
            'headers' => [
                'Content-Type' => 'text/xml'
            ],
            'body' => $this->generateXmlFromArray($request_data)
        ];

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->client->request('POST', $url, $options, 3);
    }

    public function sendGNSRequest($method, $path, $data = [], $ext_options = [])
    {
        $this->getExistingConfig();
        $nonce = (int)(microtime(true) * 10000);

        $url = $this->configuration_data['GNS_API_ENDPOINT'] . '/' . $path;

        $api_key = $this->configuration_data['API_KEY'];
        $api_secret = $this->configuration_data['API_SECRET'];

        $hmac = base64_encode(hash_hmac('sha256', $api_key . $nonce, $api_secret, true));

        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                "X-TransferTo-apikey" => $api_key,
                "X-TransferTo-nonce" => $nonce,
                "X-TransferTo-hmac" => $hmac,
            ],
            'body' => JSON::encode($data)
        ];

        $options = array_merge($ext_options, $options);

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->client->request($method, $url, $options, 3);
    }

    public function errorReporter($error)
    {
        Yii::$app->slack->send('Error fetching DTOne api', array(
            array(
                'color' => 'warning',
                'text' => Json::encode(
                    $error
                )
            )
        ));
    }


}