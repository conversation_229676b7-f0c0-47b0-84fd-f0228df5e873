<?php

namespace offgamers\publisher\models\profile;

use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class DTOneDVS extends \yii\base\Model
{
    use GuzzleTrait;

    public $configuration_data, $publisher, $error;

    public function getExistingConfig()
    {
        if (empty($configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getOperatorIdByPhoneNumber($number)
    {
        $path = 'lookup/mobile-number/+' . $number;
        $response = $this->sendRequest('GET', $path);
        if ($data = $this->validateResponse($response)) {
            if(!empty($data)){
                return $data[0]['id'];
            }
        }
    }

    protected function sendRequest($method, $path, $data = null)
    {
        $this->initClient();
        $this->getExistingConfig();
        $options = [
            'http_errors' => false,
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->configuration_data['API_KEY'] . ':' . $this->configuration_data['API_SECRET'])
            ]
        ];

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['API_ENDPOINT'] . '/' . $path, $options);
    }

    protected function validateResponse($response)
    {
        $status_code = $response->getStatusCode();

        if ($status_code < 300) {
            return Json::decode($response->getBody());
        } else {
            try {
                $data = Json::decode($response->getBody());
                if (isset($data['errors'][0]['code']) && isset($data['errors'][0]['message'])) {
                    $this->error = $data['errors'][0]['code'] . ' : ' . $data['errors'][0]['message'];
                }
            } catch (\Exception $e) {
                $this->error = $e->getMessage();
            }
        }
        return false;
    }
}