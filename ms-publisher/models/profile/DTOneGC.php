<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class DTOneGC extends \yii\base\Model
{
    const TYPE_FIXED_VALUE_PIN_PURCHASE = 'FIXED_VALUE_PIN_PURCHASE',
        TYPE_RANGED_VALUE_PIN_PURCHASE = 'RANGED_VALUE_PIN_PURCHASE',
        CALCULATION_MODE_DESTINATION_AMOUNT = 'DESTINATION_AMOUNT';

    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $api_key;
    protected $api_secret;
    protected $check_order_initial_delay;
    protected $check_order_subsequent_delay;
    protected $check_order_max_retry_attempts;
    protected $low_margin;
    protected $min_margin;
    protected $max_connection;

    public $error_code;
    public $error_msg;
    public $error;

    protected $account;

    const create_order_url = '/async/transactions';
    const check_order_url = '/transactions';

    const catalog_url = '/products';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->api_key = $config_list['API_KEY'];
            $this->api_secret = $config_list['API_SECRET'];
            $this->check_order_initial_delay = isset($config_list['CHECK_ORDER_INITIAL_DELAY']) ? $config_list['CHECK_ORDER_INITIAL_DELAY'] : "";
            $this->check_order_subsequent_delay = isset($config_list['CHECK_ORDER_SUBS_DELAY']) ? $config_list['CHECK_ORDER_SUBS_DELAY'] : "";
            $this->check_order_max_retry_attempts = isset($config_list['CHECK_ORDER_MAX_ATTEMPTS']) ? $config_list['CHECK_ORDER_MAX_ATTEMPTS'] : "";
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];

            $this->check_order_initial_delay = is_numeric($this->check_order_initial_delay) ? ($this->check_order_initial_delay * 1) : 0;
            $this->check_order_subsequent_delay = is_numeric($this->check_order_subsequent_delay) ? ($this->check_order_subsequent_delay * 1) : 0;
            $this->check_order_max_retry_attempts = is_numeric($this->check_order_max_retry_attempts) ? ($this->check_order_max_retry_attempts * 1) : 0;
            //System safety check, in case admin accidentally fill in bad data or did not fill in any, we set to default
            if ($this->check_order_initial_delay <= 0 || $this->check_order_initial_delay > 10) {
                $this->check_order_initial_delay = 0.5;
            }
            if ($this->check_order_subsequent_delay <= 0 || $this->check_order_subsequent_delay > 10) {
                $this->check_order_subsequent_delay = 0.5;
            }
            if ($this->check_order_max_retry_attempts <= 0 || $this->check_order_max_retry_attempts > 10) {
                $this->check_order_max_retry_attempts = 1;
            }
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'DTOneGC ' . $this->getAccount(),
            'profile' => 'DTOneGC',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching DTOneGC Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'DTOneGC Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($method, $path, $data)
    {
        $url = $this->base_url . $path;
        $headers = [
            'Authorization' => 'Basic ' . base64_encode($this->api_key . ":" . $this->api_secret),
        ];
        if ($method != 'GET') {
            $headers['Content-Type'] = 'application/json';
        } else if ($data) {
            $url .= '?' . http_build_query($data);
            $data = null;
        }

        $options = [
            'http_errors' => false,
            'headers' => $headers,
        ];

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $url, $options);
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200 || $status == 201) {
                return $data;
            } else {
                if (isset($data['errors'][0]['code']) && isset($data['errors'][0]['message'])) {
                    $error_msg = $data['errors'][0]['code'] . ' : ' . $data['errors'][0]['message'];
                    $this->error_code = $data['errors'][0]['code'];
                    $this->error_msg = $data['errors'][0]['message'];
                } else {
                    $error_msg = $data;
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
