<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;


/**
 * Summary of Devco
 */
class Devco extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $error, $configuration_data, $iv, $error_code;
    private $hash_mac_enc = 'sha256';
    private $cap_iv = '2.2.0';

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
                $this->iv = rand(1000000000000000, 9999999999999999);
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $activity, $data = null, $tag = null)
    {
        $this->initClient(1, isset($tag) ? $tag : "");

        $param = [
            'API_VERSION' => $this->cap_iv,
            'SERVER_URI' => $this->configuration_data['BASE_URL'],
            'SERVER_TIMESTAMP' => date('Y-m-d H:i:s'),
            'SERVER_TIMEZONE' => date_default_timezone_get(),
            'SOURCE_IP' => $this->configuration_data['SOURCE_IP'],
            'PUBLIC_KEY' => $this->configuration_data['PUBLIC_KEY'],
            'USERNAME' => $this->configuration_data['USERNAME'],
            'PASSWORD' => $this->configuration_data['PASSWORD'],
            'HASH_STUB' => rand(1111111111, 9999999999),
            'DEBUG_OUTPUT' => true,
            'FAULTY_PROXY' => false,
            'ENCRYPT_RESPONSE' => false,
            'SERVER_DEBUG' => false,
            'FUNCTION' => $activity
        ];

        if (isset($data)) {
            $param['PARAMETERS'] = $data;
        }

        $content = JSON::encode($param);

        $hash = hash_hmac($this->hash_mac_enc, $content, $this->configuration_data['PRIVATE_KEY']);

        $headers = [
            'X-Public-Key' => $this->configuration_data['PUBLIC_KEY'],
            'X-Hash' => $hash,
            'X-Sourceip' => $this->configuration_data['SOURCE_IP']
        ];

        $options = array(
            'http_errors' => false,
            'headers' => $headers,
        );

        $encrypted_content = $this->doEncrypt($content);
        if (!empty($content)) {
            $options['form_params'] = $encrypted_content;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'], $options);
    }

    protected function checkError($response)
    {
        try {
            $status = $response->getStatusCode();

            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data)) {
                    if (isset($data['CONTENT']) && ($data['CONTENT'] != 'ERROR')) {
                        $result = JSON::decode($data['CONTENT']);
                        return $result;
                    } else {
                        if (!empty($data['ERROR_DESCRIPTION']) && !empty($data['RESULT'])) {
                            $this->error_code = $data['RESULT'];
                            $this->error_msg = $data['RESULT'] . ' - ' . $data['ERROR_DESCRIPTION'];
                        }
                    }
                }
            }
            throw new \Exception($this->error_msg ?: "No Output For Publisher (DEVCO) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }

    public function getPublisher()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);

        $publisher = Publisher::findOne([
            'title' => 'Devco ' . $region,
            'profile' => 'Devco',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Devco Publisher Details', array(
                    array(
                        'color' => 'warning',
                        'text' => 'Devco Publisher Not Found'
                    )
                )
            );
        }
    }

    private function doEncrypt($content)
    {
        // Check the IV and openSSL setup/config.
        $encrypted_content = array();
        $encrypted_content['PUBLIC_KEY'] = $this->configuration_data['PUBLIC_KEY'];
        if ($this->iv != '') {
            $encrypted_content['ENC_METHOD'] = 'AES-CBC-256-OPENSSL';
            $encrypted_content['ZAPI'] = $this->iv;
            $encrypted_content['CONTENT'] = base64_encode(openssl_encrypt($content, 'aes-256-cbc', $this->configuration_data['PRIVATE_KEY'], OPENSSL_RAW_DATA, $this->iv));
            return $encrypted_content;
        }
    }
}