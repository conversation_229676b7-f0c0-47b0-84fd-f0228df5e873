<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class EZCards extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $api_key;
    protected $access_token;
    protected $check_order_initial_delay;
    protected $check_order_subsequent_delay;
    protected $check_order_max_retry_attempts;
    protected $low_margin;
    protected $min_margin;

    public $error_code;
    public $error_msg;
    public $error;
    protected $max_connection;

    protected $account;

    protected $request_id;

    const create_order_url = '/v2/orders/instant';
    const check_order_url = '/v2/orders';
    const get_codes_url = '/v2/orders/{transactionId}/codes';

    const catalog_url = '/v2/products';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->api_key = $config_list['API_KEY'];
            $this->access_token = $config_list['ACCESS_TOKEN'];
            $this->check_order_initial_delay = isset($config_list['CHECK_ORDER_INITIAL_DELAY']) ? $config_list['CHECK_ORDER_INITIAL_DELAY'] : "";
            $this->check_order_subsequent_delay = isset($config_list['CHECK_ORDER_SUBS_DELAY']) ? $config_list['CHECK_ORDER_SUBS_DELAY'] : "";
            $this->check_order_max_retry_attempts = isset($config_list['CHECK_ORDER_MAX_ATTEMPTS']) ? $config_list['CHECK_ORDER_MAX_ATTEMPTS'] : "";
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];

            $this->check_order_initial_delay = is_numeric($this->check_order_initial_delay) ? ($this->check_order_initial_delay * 1) : 0;
            $this->check_order_subsequent_delay = is_numeric($this->check_order_subsequent_delay) ? ($this->check_order_subsequent_delay * 1) : 0;
            $this->check_order_max_retry_attempts = is_numeric($this->check_order_max_retry_attempts) ? ($this->check_order_max_retry_attempts * 1) : 0;
            //System safety check, in case admin accidentally fill in bad data or did not fill in any, we set to default
            if ($this->check_order_initial_delay <= 0 || $this->check_order_initial_delay > 10) {
                $this->check_order_initial_delay = 0.5;
            }
            if ($this->check_order_subsequent_delay <= 0 || $this->check_order_subsequent_delay > 10) {
                $this->check_order_subsequent_delay = 0.5;
            }
            if ($this->check_order_max_retry_attempts <= 0 || $this->check_order_max_retry_attempts > 10) {
                $this->check_order_max_retry_attempts = 1;
            }
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'EZCards ' . $this->getAccount(),
            'profile' => 'EZCards',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching EZCards Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'EZCards Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($method, $path, $data)
    {
        $headers = $this->generateApiHeader();
        $url = $this->base_url . $path;
        if ($method != 'GET') {
            $headers['Content-Type'] = 'application/json';
        } else if ($data) {
            $url .= '?' . http_build_query($data);
            $data = null;
        }

        $options = [
            'http_errors' => false,
            'headers' => $headers,
        ];

        if ($method != 'GET') {
            $headers['Content-Type'] = 'application/json';
            if (!empty($data)) {
                $options['json'] = $data;
            }
        }
        
        return $this->client->request($method, $url, $options);
    }

    protected function generateApiHeader() {
        return [
            'x-api-key' => $this->api_key,
            'Authorization' => 'Bearer ' . $this->access_token,
        ];
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                return $data;
            } else {
                if (isset($data['errors'][0]['code']) && isset($data['errors'][0]['description'])) {
                    $error_msg = $data['errors'][0]['code'] . ' : ' . $data['errors'][0]['description'];
                    $this->error_code = $data['errors'][0]['code'];
                    $this->error_msg = $data['errors'][0]['description'];
                } else {
                    $error_msg = $data;
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
