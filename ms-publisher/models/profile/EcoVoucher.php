<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class EcoVoucher extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $distributor_code;
    protected $distributor_secret;
    protected $low_margin;
    protected $min_margin;

    public $error_code;
    public $error_msg;
    public $error;

    protected $account;

    protected $request_id;

    const create_order_url = '/API/Channel/REST/v2/issue';
    const check_order_url = '/API/Channel/REST/v2/issue/details';
    const refund_url = '/API/Channel/REST/v2/refund';

    const catalog_url = '/API/Channel/REST/v2/products/{distributorCode}';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->distributor_code = $config_list['DISTRIBUTOR_CODE'];
            $this->distributor_secret = $config_list['DISTRIBUTOR_SECRET'];
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'EcoVoucher ' . $this->getAccount(),
            'profile' => 'EcoVoucher',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching EcoVoucher Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'EcoVoucher Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($method, $path, $data, $hash_params)
    {
        $headers = [
            'timestamp' => date('c'),
        ];
        $headers['hashCode'] = $this->request_id = $this->generateHashCode($data, $headers, $hash_params);
        if ($method != 'GET') {
            $headers['Content-Type'] = 'application/json';
        }
        $url = $this->base_url . $path;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
        ];

        if (!empty($data)) {
            $options['json'] = $data;
        }

        return $this->client->request($method, $url, $options);
    }

    protected function generateHashCode($payload, $headers, $hash_params)
    {
        $plain_str = '';
        foreach ($hash_params as $param) {
            if ($param == 'distributorCode') {
                $plain_str .= $this->distributor_code;
            } else if (isset($headers[$param])) {
                $plain_str .= $headers[$param];
            } else if (isset($payload[$param])) {
                $plain_str .= $payload[$param];
            }
        }
        $plain_str .= $this->distributor_secret;
        return hash('sha256', $plain_str);
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                return $data;
            } else {
                if (isset($data['code']) && isset($data['message'])) {
                    $error_msg = $data['code'] . ' : ' . $data['message'];
                    $this->error_code = $data['code'];
                    $this->error_msg = $data['message'];
                } else {
                    $error_msg = $data;
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
