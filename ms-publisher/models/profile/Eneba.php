<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

class Eneba extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $error, $configuration_data, $token = null, $token_expiry, $headers, $error_code;

    const AUTH_API_URL = "oauth/token";

    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
                $this->token = $this->configuration_data['TOKEN'];
                $this->token_expiry = $this->configuration_data['TOKEN_EXPIRY'];
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $headers, $data = null, $body = "json", $path = '')
    {
        $this->initClient(1, $this->getAPIProvider() . '_' . ($this->orders_id ?: $this->api_restock_request_id));

        if (empty($path)) {
            $path = 'graphql/';
        }

        $options = array(
            'headers' => $headers,
            'http_errors' => false
        );

        if (!empty($data)) {
            $options[$body] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $path, $options);
    }

    public function getPublisher()
    {
        $publisher = Publisher::findOne([
            'title' => 'Eneba',
            'profile' => 'Eneba',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Eneba Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Eneba Publisher Not Found'
                )
            ));
        }
    }

    /**
     * Get Token Lock Status
     * @return bool
     */
    protected function obtainLock(): bool
    {
        $token_lock = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token_lock && empty($token_lock->value)) {
            $result = PublisherSetting::updateAll(['value' => '1'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK', 'value' => $token_lock->value]);
            if ($result > 0) {
                PublisherSetting::updateAll(['value' => (string)time()], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token_lock && $token_lock->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    /**
     * Reset Token
     * @return void
     */
    protected function resetToken()
    {
        $this->token = '';
        $this->token_expiry = 0;
    }

    /**
     * Save token to database
     * @return void
     */
    protected function saveTokenToDb()
    {
        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher, 'key' => 'TOKEN']);
        PublisherSetting::updateAll(['value' => (string)$this->token_expiry], ['publisher_id' => $this->publisher, 'key' => 'TOKEN_EXPIRY']);
        $this->releaseLock();
    }

    /**
     * @return void
     */
    protected function releaseLock()
    {
        PublisherSetting::updateAll(['value' => '0'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK']);
    }

    /**
     * Get Token Code And Set Header For Eneba
     * @return void
     * @throws \Exception
     */
    protected function getTokenFromPublisher()
    {
        $params = [
            "grant_type" => "api_consumer",
            "client_id" => $this->configuration_data["CLIENT_ID"],
            "id" => $this->configuration_data["AUTH_ID"],
            "secret" => $this->configuration_data["AUTH_SECRET"]
        ];
        $response = $this->sendRequest('POST', array(), $params, "form_params", self::AUTH_API_URL);
        $status = $response->getStatusCode();
        $data = Json::decode($response->getBody());

        if ($status == 200) {
            if (!empty($data)) {
                if (isset($data['access_token']) && isset($data['expires_in'])) {
                    $this->token = $data['access_token'];
                    #Expiry Time
                    $this->token_expiry = time() + $data['expires_in'];
                    $this->saveTokenToDb();
                }
            }
        } else {
            if (isset($data['error'])) {
                $this->error_msg = $data['error'] . ' - ' . $data['message'];
            }
        }

        if (!$this->token) {
            throw new \Exception($this->error_msg ?: "Failed to get token from Eneba");
        }
    }

    protected function setHeader()
    {
        if ($this->token) {
            $this->headers = [
                "Content-Type" => "application/json",
                "Authorization" => "Bearer " . $this->token
            ];
        }
    }

    protected function checkError($response, $action)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data)) {
                    if (isset($data['data']) && !empty($data['data'][$action]) && !isset($data['errors'])) {
                        return $data;
                    } elseif (isset($data['errors'])) {
                        $this->error_msg = ($data['errors'][0]['message'] ?? "");
                    } else {
                        $this->error_msg = "No Record Found For Publisher (Eneba)";
                    }
                }
            } else {
                if (isset($data['message'])) {
                    if ($data['message'] == 'Access token is expired, please refresh') //get error for expired token
                    {
                        //Reset Token Due to Token expired before Expiry Time, Token not valid
                        $this->resetToken();
                        $this->saveTokenToDb();
                    }
                    $this->error_msg = $data['message'];
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (Eneba) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }
}