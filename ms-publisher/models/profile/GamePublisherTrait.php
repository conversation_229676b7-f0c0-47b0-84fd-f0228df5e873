<?php

namespace offgamers\publisher\models\profile;

use Yii;
use offgamers\publisher\models\GamePublisherSetting;
use yii\base\InvalidArgumentException;

trait GamePublisherTrait
{
    use \offgamers\base\traits\GuzzleTrait;

    public $configuration_data, $publisher;

    public function getExistingConfig($reload_config = false)
    {
        if (empty($configuration_data) || $reload_config) {
            if (!empty($this->publisher)) {
                $config_list = GamePublisherSetting::findAll(['game_publisher_id' => $this->publisher]);

                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher id is empty');
            }
        }
    }

}