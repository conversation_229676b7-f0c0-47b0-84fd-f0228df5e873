<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class LikeCard extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $device_id;
    protected $hash_key;
    protected $secret_iv;
    protected $secret_key;
    protected $security_code;
    protected $email;
    protected $password;
    protected $phone;
    protected $low_margin;
    protected $min_margin;

    public $error_code;
    public $error_msg;
    public $error;
    protected $max_connection;

    protected $account;

    protected $request_id;

    const create_order_url = '/create_order';
    const check_order_url = '/orders/details';

    const categories_url = '/categories';
    const products_url = '/products';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->device_id = $config_list['DEVICE_ID'];
            $this->hash_key = $config_list['HASH_KEY'];
            $this->secret_iv = $config_list['SECRET_IV'];
            $this->secret_key = $config_list['SECRET_KEY'];
            $this->security_code = $config_list['SECURITY_CODE'];
            $this->email = $config_list['EMAIL'];
            $this->password = $config_list['PASSWORD'];
            $this->phone = $config_list['PHONE'];
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'LikeCard ' . $this->getAccount(),
            'profile' => 'LikeCard',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching LikeCard Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'LikeCard Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($path, $data)
    {
        $data = array_merge($data, [
            'deviceId' => $this->device_id,
            'email' => $this->email,
            'password' => $this->password,
            'securityCode' => $this->security_code,
            'langId' => 1,
        ]);

        $headers = [];
        $url = $this->base_url . $path;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
            'form_params' => $data,
        ];

        return $this->client->request('POST', $url, $options);
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = $response->getBody();
            $error_msg = $data;

            if ($status == 200) {
                $data = Json::decode($data);
                if (isset($data['response']) && $data['response'] === 1) {
                    return $data;
                } else if (!isset($data['message'])) {
                    ; // worse case scenario no message
                } else if (isset($data['errorCode'])) {
                    $error_msg = $data['errorCode'] . ' : ' . $data['message'];
                    $this->error_code = $data['errorCode'];
                    $this->error_msg = $data['message'];
                } else {
                    $error_msg = $this->error_code = $this->error_msg = $data['message'];
                }
            }
            $this->error = [
                'http_status' => $response->getStatusCode(),
                'error' => $error_msg
            ];
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
