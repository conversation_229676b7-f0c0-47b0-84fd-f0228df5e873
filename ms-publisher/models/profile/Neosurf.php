<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;

abstract class Neosurf extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $id_user;
    protected $secret_key;
    protected $id_reseller_prefix;
    protected $low_margin;
    protected $min_margin;

    public $error_code;
    public $error_msg;
    public $error;

    protected $account;

    protected $request_id;

    const create_order_method = 'get_dtickets';
    const check_order_method = 'get_dtickets';

    const refund_method = 'get_dtickets_void2';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->id_user = $config_list['ID_USER'];
            $this->secret_key = $config_list['SECRET_KEY'];
            $this->id_reseller_prefix = $config_list['ID_RESELLER_PREFIX'];
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'Neosurf ' . $this->getAccount(),
            'profile' => 'Neosurf',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Neosurf Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Neosurf Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($method_name, $data)
    {
        $data['IDUser'] = $this->id_user * 1;
        $data['hash'] = $this->generateHash($data);
        ksort($data, SORT_STRING | SORT_FLAG_CASE);

        $payload_xml = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"',
                    'xmlns:ns1="https://www.neosurf.info/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"',
                    'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
                    'xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"',
                    'SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">',
                '<SOAP-ENV:Body>',
                    '<ns1:' . $method_name . '>'];
                    foreach ($data as $key => $value) {
                        $payload_xml[] = "<$key xsi:type=\"xsd:" . (is_int($value) ? 'int' : 'string') . "\">$value</$key>";
                    }
                    $payload_xml = array_merge($payload_xml, [
                    '</ns1:' . $method_name . '>',
                '</SOAP-ENV:Body>',
            '</SOAP-ENV:Envelope>']);
        $payload_str = implode("\n", $payload_xml);

        $url = $this->base_url . '#' . $method_name;

        $options = [
            'http_errors' => false,
            'headers' => ['Content-Type' => 'text/xml; charset=utf-8'],
            'body' => $payload_str,
        ];

        return $this->client->request('POST', $url, $options);
    }

    protected function generateHash($payload) {
        ksort($payload, SORT_STRING | SORT_FLAG_CASE);
        $hash_str_arr = [];
        foreach ($payload as $str) {
            $hash_str_arr[] = $str;
        }
        $hash_str_arr[] = $this->secret_key;
        $hash_str = implode('', $hash_str_arr);
        return sha1($hash_str);
    }

    protected function checkError($method_name, $response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = $this->parseXml($method_name, $response->getBody());

            if ($status == 200) {
                return $data;
            } else {
                if (isset($data['faultcode']) && isset($data['faultstring'])) {
                    $error_msg = $data['faultcode'] . ' : ' . $data['faultstring'];
                    $this->error_code = $data['faultcode'];
                    $this->error_msg = $data['faultstring'];
                } else {
                    $error_msg = $response->getBody();
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }

    protected function parseXml($method_name, $str) {
        $xml = simplexml_load_string($str);
        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $response = $xml->xpath('//soap:Body');
        if ($response) {
            $xml = $response[0];
            $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
            $fault_response = $xml->xpath('//soap:Fault');
            if ($fault_response) {
                return json_decode(json_encode($fault_response[0]), true);
            } else {
                $xml->registerXPathNamespace('ns1', 'https://www.neosurf.info/soap/');
                $response = $xml->xpath('//ns1:' . $method_name . 'Response');
                return json_decode(json_encode($response[0]), true);
            }
        } else {
            return null;
        }
    }
}
