<?php

namespace offgamers\publisher\models\profile;

use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

abstract class Pro extends \yii\base\Model
{
    use \offgamers\base\traits\GuzzleTrait;

    protected $publisher_id;
    protected $base_url;
    protected $client_id;
    protected $client_secret;
    protected $check_order_initial_delay;
    protected $check_order_subsequent_delay;
    protected $check_order_max_retry_attempts;
    protected $low_margin;
    protected $min_margin;

    public $error_code;
    public $error_msg;
    public $error;
    protected $max_connection;

    protected $account;

    protected $request_id;

    const create_order_url = '/api/v1/submitOrder';
    const check_order_url = '/api/v1/getOrderDetail';

    const catalog_url = '/api/v1/getGoodsList/{pageNum}/{pageSize}';

    public function getConfig()
    {
        if (empty($this->base_url)) {
            $config_list = ArrayHelper::map(
                PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(),
                'key',
                'value'
            );

            $this->base_url = rtrim($config_list['BASE_URL'], '/');
            $this->client_id = $config_list['CLIENT_ID'];
            $this->client_secret = $config_list['CLIENT_SECRET'];
            $this->check_order_initial_delay = isset($config_list['CHECK_ORDER_INITIAL_DELAY']) ? $config_list['CHECK_ORDER_INITIAL_DELAY'] : "";
            $this->check_order_subsequent_delay = isset($config_list['CHECK_ORDER_SUBS_DELAY']) ? $config_list['CHECK_ORDER_SUBS_DELAY'] : "";
            $this->check_order_max_retry_attempts = isset($config_list['CHECK_ORDER_MAX_ATTEMPTS']) ? $config_list['CHECK_ORDER_MAX_ATTEMPTS'] : "";
            $this->low_margin = $config_list['LOW_MARGIN'];
            $this->min_margin = $config_list['MIN_MARGIN'];
            $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];

            $this->check_order_initial_delay = is_numeric($this->check_order_initial_delay) ? ($this->check_order_initial_delay * 1) : 0;
            $this->check_order_subsequent_delay = is_numeric($this->check_order_subsequent_delay) ? ($this->check_order_subsequent_delay * 1) : 0;
            $this->check_order_max_retry_attempts = is_numeric($this->check_order_max_retry_attempts) ? ($this->check_order_max_retry_attempts * 1) : 0;
            //System safety check, in case admin accidentally fill in bad data or did not fill in any, we set to default
            if ($this->check_order_initial_delay <= 0 || $this->check_order_initial_delay > 10) {
                $this->check_order_initial_delay = 5;
            }
            if ($this->check_order_subsequent_delay <= 0 || $this->check_order_subsequent_delay > 10) {
                $this->check_order_subsequent_delay = 1;
            }
            if ($this->check_order_max_retry_attempts <= 0 || $this->check_order_max_retry_attempts > 10) {
                $this->check_order_max_retry_attempts = 5;
            }
        }
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'PRO ' . $this->getAccount(),
            'profile' => 'PRO',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher_id = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching PRO Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'PRO Publisher Not Found'
                )
            ));
        }
    }

    protected function sendRequest($path, $data)
    {
        $payload = json_encode($data);
        $headers = $this->generateApiHeader($payload);
        $url = $this->base_url . $path;

        $options = [
            'http_errors' => false,
            'headers' => $headers,
            'body' => $payload,
        ];

        return $this->client->request('POST', $url, $options);
    }

    protected function generateApiHeader($payload) {
        return [
            'ClientId' => $this->client_id,
            'AuthSign' => md5($payload . $this->client_secret),
            'Content-Type' => 'application/json',
        ];
    }

    protected function checkError($response)
    {
        $this->error_code = $this->error_msg = $this->error = null;
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200 && isset($data['result']) && $data['result'] === true) {
                return $data;
            } else {
                if (isset($data['error']['code']) && isset($data['error']['message'])) {
                    $error_msg = $data['error']['code'] . ' : ' . $data['error']['message'];
                    $this->error_code = $data['error']['code'];
                    $this->error_msg = $data['error']['message'];
                } else {
                    $error_msg = $response->getBody();
                }
                $this->error = [
                    'http_status' => $response->getStatusCode(),
                    'error' => $error_msg
                ];
            }
        } catch (\Exception $e) {
            $this->error = [
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ];

            if (!empty($response->getBody())) {
                $this->error['response'] = (string)$response->getBody();
            }
        }

        return false;
    }
}
