<?php

namespace offgamers\publisher\models\profile;

use offgamers\base\traits\GuzzleTrait;
use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Psr\Http\Message\ResponseInterface;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class QwikCilver extends \yii\base\Model
{
    use GuzzleTrait;

    /**
     * @var int publisher id
     */
    protected int $publisher_id;
    /**
     * @var string base url
     */
    protected string $base_url;
    /**
     * @var string username
     */
    protected string $username;
    /**
     * @var string password
     */
    protected string $password;
    /**
     * @var string client id
     */
    protected string $client_id;
    /**
     * @var string client secret code
     */
    protected string $client_secret;
    /**
     * @var string|null authorization code
     */
    protected ?string $authorization_code = null;
    /**
     * @var string|null token
     */
    protected ?string $token = null;
    /**
     * @var string|null token expiry timestamp
     */
    protected ?string $token_expiry = '';
    /**
     * @var string billing name
     */
    protected string $billing_name;
    /**
     * @var string billing postcode
     */
    protected string $billing_postcode;
    /**
     * @var string billing country
     */
    protected string $billing_country;
    /**
     * @var string
     */
    protected string $billing_email;
    /**
     * @var int minimum margin alert percentage
     */
    protected $min_margin;
    /**
     * @var int low margin alert percentage
     */
    protected $low_margin;
    /**
     * @var int maximum supported concurrent connection
     */
    protected int $max_connection;
    /**
     * @var int error code from publisher
     */
    protected int $error_code;
    /**
     * @var string error message for error handler
     */
    public $error_msg;

    /**
     * Get Access Token before authentication token
     */
    protected const CREATE_ACCESS_TOKEN_URL = "oauth2/token";
    /**
     * Get OAuth Token endpoint
     */
    protected const GET_AUTHORIZATION_CODE_URL = "oauth2/verify";
    /**
     * Create Order Endpoint
     */
    protected const PLACE_ORDER_URL = "rest/v3/orders";
    /**
     * Staging Root Category List Endpoint
     */
    protected const CATEGORY_URL = "rest/v3/category";
    /**
     * Production Root Category List Endpoint
     */
    protected const PRODUCTION_CATEGORY_URL = "rest/v3/category/categories";
    /**
     * Category List Endpoint
     */
    protected const PRODUCT_LIST_URL = "rest/v3/catalog/categories";
    /**
     * Products Details Endpoint
     */
    protected const PRODUCT_DETAILS_URL = "rest/v3/catalog/products";
    /**
     * Order Endpoint
     */
    protected const ORDER_URL = "rest/v3/order";

    // Not In Use
    /**
     * Reverse Order Endpoint
     */
    protected const ORDER_REVERSAL_URL = "rest/v3/orders/reverse";

    /**
     * Get Config From DB
     * @param $reload_config
     * @return void
     */
    public function getConfig($reload_config = false)
    {
        if (empty($this->base_url) || $reload_config) {
            $publisher = Publisher::find()->where(['profile' => 'QwikCilver', 'publisher_id' => $this->publisher_id])->exists();
            if ($publisher) {
                $config_list = ArrayHelper::map(PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(), 'key', 'value');
                if (count($config_list) === 16) {
                    $this->base_url = $config_list['BASE_URL'];
                    $this->client_id = $config_list['CLIENT_ID'];
                    $this->client_secret = $config_list['CLIENT_SECRET'];
                    $this->username = $config_list['USERNAME'];
                    $this->password = $config_list['PASSWORD'];
                    $this->billing_name = $config_list['BILLING_NAME'];
                    $this->billing_email = $config_list['BILLING_EMAIL'];
                    $this->billing_postcode = $config_list['BILLING_POSTCODE'];
                    $this->billing_country = $config_list['BILLING_COUNTRY_CODE'];
                    $this->token = $config_list['TOKEN'];
                    $this->token_expiry = $config_list['TOKEN_EXPIRY'];
                    $this->max_connection = $config_list['MAX_CONCURRENT_CONNECTION'];
                    $this->min_margin = $config_list['MIN_MARGIN'];
                    $this->low_margin = $config_list['LOW_MARGIN'];
                } else {
                    throw new InvalidArgumentException('Invalid Publisher Configuration' . count($config_list));
                }
            } else {
                throw new InvalidArgumentException('Invalid Publisher');
            }
        }
    }

    /**
     * Get Authorization Code From QwikCilver
     * @return void
     * @throws \Exception
     */
    protected function getAuthorizationCode()
    {
        $params = [
            "clientId" => $this->client_id,
            "username" => $this->username,
            "password" => $this->password
        ];

        $response = $this->sendRequest("POST", self::GET_AUTHORIZATION_CODE_URL, [], $params);

        if ($data = $this->verifyResponse($response)) {
            if (isset($data['authorizationCode'])) {
                $this->authorization_code = $data['authorizationCode'];
            }
        }

        if (!$this->authorization_code) {
            throw new \Exception('Failed to get authorization code from QwikCilver (' . $this->publisher_id . ')');
        }
    }

    /**
     * Function to get request header for authenticated endpoint
     * @param $path
     * @param $method
     * @param $body
     * @return array
     */
    protected function getHeaders($path, $method = 'GET', $body = '')
    {
        return [
            'Content-Type' => 'application/json',
            "Authorization" => "Bearer " . $this->token,
            "Accept" => '*/*',
            "dateAtClient" => date("c"),
            "signature" => $this->getSignature($this->base_url . '/' . $path, $method, $body)
        ];
    }

    /**
     * Generate Token Process
     * @param $attempt
     * @return string|void|null
     * @throws \Exception
     */
    protected function getToken($attempt = 0)
    {
        # Renew token when token expiry less than one hour
        try {
            if (empty($this->token) || time() + 3600 > $this->token_expiry) {
                if ($this->obtainLock()) {
                    $this->getAuthorizationCode();
                    $this->getTokenFromPublisher();
                } else {
                    sleep(3);
                    if ($attempt < 3) {
                        $this->getConfig(true);
                        return $this->getToken($attempt + 1);
                    } else {
                        throw new \Exception('Failed to get token from QwikCilver (' . $this->publisher_id . ')');
                    }
                }
            } else {
                return $this->token;
            }
        } catch (\Exception $e) {
            $this->releaseLock();
            throw ($e);
        }
    }

    /**
     * Get Token Code From Qwikcilver
     * @return void
     * @throws \Exception
     */
    protected function getTokenFromPublisher()
    {
        $params = [
            "clientId" => $this->client_id,
            "clientSecret" => $this->client_secret,
            "authorizationCode" => $this->authorization_code
        ];

        $response = $this->sendRequest("POST", self::CREATE_ACCESS_TOKEN_URL, [], $params);

        if ($data = $this->verifyResponse($response)) {
            if (isset($data['token'])) {
                $this->token = $data['token'];
                # One Week Expiry Time
                $this->token_expiry = time() + 604800;
                $this->saveTokenToDb();
            }
        }
        if (!$this->token) {
            throw new \Exception('Failed to get token from QwikCilver (' . $this->publisher_id . ')');
        }
    }

    /**
     * Check Token Lock Status
     * @return bool
     */
    protected function getTokenLockStatus(): bool
    {
        $token = PublisherSetting::find()->where(['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK'])->one();
        return ($token && $token->value === '1');
    }

    /**
     * Get Token Lock Status
     * @return bool
     */
    protected function obtainLock(): bool
    {
        $token_lock = PublisherSetting::find()->where(['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = PublisherSetting::find()->where(['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token_lock && empty($token_lock->value)) {
            $result = PublisherSetting::updateAll(['value' => '1'], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK', 'value' => $token_lock->value]);
            if ($result > 0) {
                PublisherSetting::updateAll(['value' => (string)time()], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token_lock && $token_lock->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    /**
     * Reset Token
     * @return void
     */
    protected function resetToken()
    {
        $this->token = '';
        $this->token_expiry = 0;
    }

    /**
     * Save token to database
     * @return void
     */
    protected function saveTokenToDb()
    {
        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher_id, 'key' => 'TOKEN']);
        PublisherSetting::updateAll(['value' => (string)$this->token_expiry], ['publisher_id' => $this->publisher_id, 'key' => 'TOKEN_EXPIRY']);
        $this->releaseLock();
    }

    /**
     * @param ResponseInterface $response
     * @return array|null
     */
    protected function verifyResponse(ResponseInterface $response): ?array
    {
        $this->error_msg = '';

        $status_code = $response->getStatusCode();
        $response_body = $response->getBody();
        try {
            $data = Json::decode($response_body);
            if ($status_code < 300) {
                return $data;
            } elseif (isset($data['code'])) {
                if ($status_code == 401) {
                    $this->resetToken();
                    $this->saveTokenToDb();
                }
                $this->error_code = (int)$data['code'];
                $this->error_msg = $this->getErrorFromResponse($data);
            } else {
                $this->error_msg = $status_code . ' - ' . $response->getReasonPhrase();
            }
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage() . ' - ' . (string)$response_body;
        }

        return null;
    }

    /**
     * @param $data
     * @return string
     */
    protected function getErrorFromResponse($data)
    {
        return ($data['code'] ?? '') . ' : ' . ($data['message'] ?? '');
    }

    /**
     * @param string $method
     * @param string $path
     * @param array $headers
     * @param array $params
     * @return ResponseInterface|null
     */
    protected function sendRequest(string $method, string $path, array $headers = [], array $params = []): ?ResponseInterface
    {
        $options = ['http_errors' => false];

        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        if ($params) {
            $options['json'] = $params;
        }

        return $this->client->request($method, $this->base_url . '/' . $path, $options);
    }

    /**
     * @param $endpoint
     * @param $method
     * @param $body
     * @return string
     */
    protected function getSignature($endpoint, $method, $body): string
    {
        if (!is_string($body)) {
            $body = Json::encode($body);
        }
        return hash_hmac('sha512', $this->getConcatenateBaseString($endpoint, $method, $body), $this->client_secret);
    }


    /**
     * @return void
     */
    protected function releaseLock()
    {
        PublisherSetting::updateAll(['value' => '0'], ['publisher_id' => $this->publisher_id, 'key' => 'GET_TOKEN_LOCK']);
    }

    /**
     * @param array $params
     * @return void
     */
    protected function sortParams(array &$params)
    {
        ksort($params);
        foreach ($params as $key => &$value) {
            $value = is_object($value) ? (array)$value : $value;
            if (is_array($value)) {
                $this->sortParams($value);
            }
        }
    }

    /**
     * Sort all query parameters in the request according to the parameter name in ASCII table.
     *
     * @param string $queryParam
     * @return string
     */
    protected function sortQueryParams(string $queryParam): string
    {
        $query = explode('&', $queryParam);
        asort($query, SORT_STRING);
        return implode('&', $query);
    }

    /**
     * Concat the (request method(upper case), request host, request URL), encoded response parameters and encoded query parameters using & as the separator.
     *
     * @param string $absApiUrl
     * @param string $requestHttpMethod
     * @param string $responseBody
     *
     * @return string
     */
    protected function getConcatenateBaseString(string $absApiUrl, string $requestHttpMethod, string $responseBody): string
    {
        $baseStrings = [];

        $baseStrings[] = strtoupper($requestHttpMethod);
        $url = explode('?', $absApiUrl);
        $apiUrl = $url[0];

        if (isset($url[1])) {
            $baseStrings[] = rawurlencode($apiUrl . '?' . $this->sortQueryParams($url[1]));
        } else {
            $baseStrings[] = rawurlencode($apiUrl);
        }

        if ($responseBody) {
            $jsonDecodedResponseBody = Json::decode($responseBody);
            $this->sortParams($jsonDecodedResponseBody);
            $baseStrings[] = rawurlencode(
                Json::encode($jsonDecodedResponseBody, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
            );
        }

        return implode('&', $baseStrings);
    }
}