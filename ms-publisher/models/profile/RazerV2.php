<?php

namespace offgamers\publisher\models\profile;

use Yii;
use offgamers\base\traits\GuzzleTrait;
use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;

abstract class RazerV2 extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher;

    public $error_msg;

    public $error_code;

    public $configuration_data;

    public $base_url;

    public $user_name;

    public $secret;

    protected $token;

    protected $token_expiry;

    protected $account;

    const AUTH_URL = 'auth/token/login';

    const ORDER_PIN = 'pinsupply/order';


    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getConfig()
    {
        $this->getExistingConfig();
        $this->base_url = $this->configuration_data['BASE_URL'];
        $this->user_name = $this->configuration_data['USER_NAME'];
        $this->secret = $this->configuration_data['SECRET'];
        $this->token = $this->configuration_data['TOKEN'];
        $this->token_expiry = $this->configuration_data['TOKEN_EXPIRY'];
    }

    protected function getAccount()
    {
        return $this->account;
    }

    public function getPublisher($status = 1)
    {
        $publisher = Publisher::findOne([
            'title' => 'Razer ' . $this->getAccount(),
            'profile' => 'Razer',
            'status' => $status
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Razer Publisher Details', array(
                array(
                    'color' => 'warning',
                    'text' => 'Razer Publisher Not Found'
                )
            ));
        }
    }

    /**
     * @throws \Exception
     */
    protected function getToken($attempt = 0)
    {
        # Renew token when token expiry less than 5 min
        try {
            if (empty($this->token) || time() + 300 > $this->token_expiry) {
                if ($this->obtainLock()) {
                    $this->getTokenFromPublisher();
                } else {
                    sleep(3);
                    if ($attempt < 3) {
                        $this->getExistingConfig();
                        $this->getToken($attempt + 1);
                    } else {
                        throw new \Exception('Failed to get token from Razer (ID : ' . $this->publisher . ')');
                    }
                }
            } else {
                return $this->token;
            }
        } catch (\Exception $e) {
            $this->releaseLock();
            throw ($e);
        }
    }

    protected function getTokenFromPublisher()
    {
        $params = [
            "grant_type" => "distributor_credentials",
            "distributor_username" => $this->user_name,
            "distributor_secret" => $this->secret,
        ];

        $response = $this->sendRequest('POST', self::AUTH_URL, $params);

        if ($data = $this->parseResponse($response)) {
            if (!empty($data['access_token'])) {
                $this->token = $data['access_token'];
                $this->token_expiry = time() + 3600;
                $this->saveTokenToDb();
            }
        }

        if (!$this->token) {
            throw new \Exception($this->error_msg ?: "Failed to get token from Razer");
        }
    }

    protected function parseResponse($response)
    {
        $status = $response->getStatusCode();
        $json = Json::decode($response->getBody());

        if ($status == 200) {
            try {
                if (!empty($json['data'])) {
                    return $json['data'];
                }
            } catch (\Exception $e) {
                $this->error_msg = $e->getMessage();
            }
        }

        if ($status == 401) {
            $this->resetToken();
        }

        if (!empty($json['code']) || !empty($json['message'])) {
            $this->error_code = ($json['code'] ?? '');
            $this->error_msg = ($json['message'] ?? '');
        } elseif (!empty($this->error_msg)) {
            $this->error_msg = 'Invalid Response from publisher';
        }

        return false;
    }


    protected function sendRequest($method, $url, $params = [])
    {
        $headers = [];

        if ($url != self::AUTH_URL) {
            $headers['Authorization'] = 'Bearer ' . $this->token;
        }

        $options = array(
            'headers' => $headers,
            'json' => $params,
            'http_errors' => false
        );

        if (!empty($params)) {
            $options['json'] = $params;
        }

        if (!empty(Yii::$app->params['proxy'])) {
            $options['proxy'] = Yii::$app->params['proxy'];
        }

        return $this->request($method, $this->base_url . '/' . $url, $options);
    }

    abstract function request($method, $url, $options);

    /**
     * Get Token Lock Status
     * @return bool
     */
    protected function obtainLock(): bool
    {
        $token_lock = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK'])->one();
        $locked_time = PublisherSetting::find()->where(['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP'])->one();

        if ($token_lock && empty($token_lock->value)) {
            $result = PublisherSetting::updateAll(['value' => '1'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK', 'value' => $token_lock->value]);
            if ($result > 0) {
                PublisherSetting::updateAll(['value' => (string)time()], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK_TIMESTAMP']);
                return true;
            }
        } elseif ($token_lock && $token_lock->value == '1' && $locked_time && time() - $locked_time->value > 10) {
            $this->releaseLock();
            return $this->obtainLock();
        }

        return false;
    }

    /**
     * Reset Token
     * @return void
     */
    protected function resetToken()
    {
        $this->token = '';
        $this->token_expiry = 0;
    }

    /**
     * Save token to database
     * @return void
     */
    protected function saveTokenToDb()
    {
        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher, 'key' => 'TOKEN']);
        PublisherSetting::updateAll(['value' => (string)$this->token_expiry], ['publisher_id' => $this->publisher, 'key' => 'TOKEN_EXPIRY']);
        $this->releaseLock();
    }

    /**
     * @return void
     */
    protected function releaseLock()
    {
        PublisherSetting::updateAll(['value' => '0'], ['publisher_id' => $this->publisher, 'key' => 'GET_TOKEN_LOCK']);
    }
}