<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use yii\base\InvalidArgumentException;
use yii\helpers\FileHelper;
use yii\helpers\Json;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\TripleDES;

/**
 * Summary of Vtc
 */
class VTC extends \yii\base\Model
{
    use GuzzleTrait;

    public $publisher, $error, $configuration_data, $error_msg, $error_code, $base_url, $partner_code, $private_key, $public_key, $triple_des_key, $tag = 'VTC';

    public function getExistingConfig()
    {
        if (empty($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    public function getConfig()
    {
        $this->getExistingConfig();
        $this->base_url = $this->configuration_data['BASE_URL'];
        $this->triple_des_key = $this->configuration_data['TRIPLE_DES_KEY'];
        $this->partner_code = $this->configuration_data['PARTNER_CODE'];
        $private_key_content = $this->getCertificateFile($this->configuration_data['PRIVATE_KEY']);
        $public_key_content = $this->getCertificateFile($this->configuration_data['PUBLIC_KEY']);
        if ($private_key_content && $public_key_content) {
            $this->private_key = $private_key_content;
            $this->public_key = $public_key_content;
        } else {
            throw new \Exception('Failed to get key content');
        }
    }

    public function getCertificateFile($file)
    {
        // Set the local directory for the certificate file
        $cert_path = Yii::getAlias('@runtime') . '/' . $file;

        // Check the certificate files exist or not
        if (!file_exists($cert_path)) {
            $s3 = Yii::$app->aws->getS3('BUCKET_ENCRYPT_KEY');
            $content = $s3->getContent($file);
            if ($content !== null) {
                $path = dirname($cert_path);
                FileHelper::createDirectory($path, 755);
                file_put_contents($cert_path, $content);
            }
        }

        return file_get_contents($cert_path);
    }

    protected function sendRequest($method, $url, $data = null)
    {
        $this->initClient(1, $this->tag);

        $options = array(
            'http_errors' => false,
        );

        if (!empty($data)) {
            $options['json'] = $data;
        }

        $response = $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $url, $options);

        return $response;
    }

    protected function checkError($response)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data) && isset($data['responseCode']) && isset($data['dataSign'])) {
                    if ($this->verifyDatasign($data, $data['dataSign'])) {
                        if (($data['responseCode'] == '1')) {
                            return $data;
                        } else {
                            // get Error from the response code
                            $this->error_code = $data['responseCode'];
                            $this->error_msg = $this->getStatusMessage($data['responseCode'], ($data['description'] ?? ''));
                        }
                    } else {
                        $this->error_code = '';
                        $this->error_msg = 'Unable to verify signature';
                    }
                }
            } else {
                if (isset($data['errors']) && isset($data['status'])) {
                    // get Error from the response code
                    $this->error_code = $data['status'];
                    $this->error_msg = $data['status'] . " - " . $data['errors'];
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (VTC) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }

        return false;
    }

    public function getStatusMessage($code, $description = '')
    {
        switch ($code) {
            case '1':
                $message = "Successful";
                break;
            case '-1':
                $message = "Failed";
                break;
            case '-34':
                $message = "Invalid or invalid mobile phone number";
                break;
            case '-35':
                $message = "11-digit phone number has been converted to a 10-digit phone number";
                break;
            case '-50':
                $message = "E-wallet does not exist";
                break;
            case '-100':
                $message = "Service is not valid";
                break;
            case '-301':
                $message = "Wrong format of partnerTransDate";
                break;
            case '-302':
                $message = "PartnerCode does not exist on system or Wrong signature";
                break;
            case '-303':
                $message = "False Authen failed transaction";
                break;
            case '-306':
                $message = "Quantity is not valid";
                break;
            case '-305':
                $message = "Unable get the data or dont have issued invoice";
                break;
            case '-307':
                $message = "Incorrect productAmount (temporarily expired product in cache)";
                break;
            case '-600':
                $message = "Blank or invalid Required input data";
                break;
            case '-1002':
                $message = "Invalid or expired Signature";
                break;
            case '-1003':
                $message = "Expired data";
                break;
            case '-5501':
                $message = "Invalid partner code";
                break;
            case '-5502':
                $message = "The product is temporarily out of service";
                break;
            case '-5503':
                $message = "Invalid quantity or exceeding the allowed purchase quantity";
                break;
            case '-5601':
                $message = "Invalid account code";
                break;
            case '-5506':
                $message = "Product does not exist";
                break;
            case '-5507':
                $message = "Product category does not exist";
                break;
            case '-5508':
                $message = "customer does not exist or is not valid";
                break;
            case '-5509':
                $message = "locked partner";
                break;
            case '-5510':
                $message = "Invalid partner code to query invoice payment";
                break;
            case '-5511':
                $message = "Error in query sales policy";
                break;
            case '-5512':
                $message = "The number of cards in stock is not enough";
                break;
            case '-5513':
                $message = "Exceeded your daily or monthly purchase limit";
                break;
            case '-5515':
            case '-5517':
                $message = "Invalid mobile phone number";
                break;
            case '-5516':
                $message = "Invoice not found (no debt or paid)";
                break;
            case '-5518':
                $message = "Mobile user is yet registered to use the telco services";
                break;
            case '-5519':
                $message = "Mobile user has not been activated";
                break;
            case '-5520':
                $message = "Mobile user is locked or not allowed to recharge";
                break;
            case '-5521':
                $message = "The subscription does not exist or has switched networks. Please check the subscriber number or the selected carrier";
                break;
            case '-5522':
                $message = "Postpaid subscribers cannot use prepaid payment services";
                break;
            case '-5523':
                $message = "Prepaid subscribers cannot use postpaid payment services";
                break;
            case '-5550':
                $message = "Order does not exist or is invalid";
                break;
            case '-5551':
                $message = "Invalid order due to change of information";
                break;
            case '-5552':
                $message = "Partner order already exists";
                break;
            case '-999':
                $message = "The system is busy. The transaction is being processed, please check the transaction history";
                break;
            default:
                $message = 'Unspecified error codes';
                break;
        }

        return "($code) $message - $description";
    }

    public function decryptResponse($response)
    {
        $tripleDesKey = md5($this->triple_des_key);
        $tripleDesKey = str_ireplace('-', '', $tripleDesKey);
        $tripleDesKey = strtolower(str_ireplace(' ', '+', $tripleDesKey));
        $tripleDesKey = substr($tripleDesKey, 0, 24);
        $decrypt = new TripleDES('ecb');
        $decrypt->setKey($tripleDesKey);
        return Json::decode($decrypt->decrypt(base64_decode($response)));
    }

    public function generateSignature($body)
    {
        $params = [
            'partnerCode' => ($body['partnerCode'] ?? ''),
            'categoryID' => ($body['categoryID'] ?? ''),
            'productID' => ($body['productID'] ?? ''),
            'productAmount' => ($body['productAmount'] ?? ''),
            'customerID' => ($body['customerID'] ?? ''),
            'partnerTransID' => ($body['partnerTransID'] ?? ''),
            'partnerTransDate' => ($body['partnerTransDate'] ?? ''),
            'data' => ($body['data'] ?? ''),
        ];
        $message = implode('|', $params);
        $key = (RSA::load($this->private_key))->withPadding(RSA::ENCRYPTION_PKCS1 | RSA::SIGNATURE_PKCS1)->withHash('sha256')->withMGFHash('sha256');
        $data_sign = $key->sign($message);

        return base64_encode($data_sign);
    }

    public function verifyDatasign($body, $data_sign)
    {
        $params = [
            'responseCode' => ($body['responseCode'] ?? ''),
            'status' => ($body['status'] ?? ''),
            'partnerTransID' => ($body['partnerTransID'] ?? ''),
            'description' => ($body['description'] ?? ''),
            'dataInfo' => ($body['dataInfo'] ?? ''),
        ];
        $message = implode('|', $params);
        $public_key = (RSA::load($this->public_key))->withPadding(RSA::ENCRYPTION_PKCS1 | RSA::SIGNATURE_PKCS1)->withHash('sha256')->withMGFHash('sha256');
        return $public_key->verify($message, base64_decode($data_sign));
    }

    public function decodeDataInfo($data)
    {
        if (isset($data['dataInfo'])) {
            $decoded_data = JSON::decode(base64_decode($data['dataInfo']));
            return $decoded_data;
        }
        return false;
    }
}