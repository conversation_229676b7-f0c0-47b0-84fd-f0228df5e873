<?php

namespace offgamers\publisher\models\profile;

use Yii;
use \offgamers\base\traits\GuzzleTrait;
use \offgamers\publisher\models\PublisherSetting;
use \offgamers\publisher\models\Publisher;
use yii\base\InvalidArgumentException;
use yii\helpers\Json;


/**
 * Summary of Wogi
 */
class Wogi extends \yii\base\Model
{
    use GuzzleTrait;
    public $publisher, $error, $configuration_data, $error_code;
    public function getExistingConfig()
    {
        if (!isset($this->configuration_data)) {
            if (!empty($this->publisher)) {
                $config_list = PublisherSetting::findAll(['publisher_id' => $this->publisher]);
                foreach ($config_list as $config) {
                    $this->configuration_data[$config->key] = $config->value;
                }
            } else {
                throw new InvalidArgumentException('Publisher Id is empty');
            }
        }
    }

    protected function sendRequest($method, $url, $data = null,$tag = null)
    {
        $this->initClient(1, isset($tag) ? $tag : "");

        $auth = [
            $this->configuration_data['USERID'],
            $this->configuration_data['SECRET']
        ];

        $options = array(
            'auth' => $auth,
            'http_errors' => false
        );

        if (!empty($data)) {
            $options['form_params'] = $data;
        }

        return $this->client->request($method, $this->configuration_data['BASE_URL'] . '/' . $url, $options);
    }
    protected function checkError($response)
    {
        try {
            $status = $response->getStatusCode();
            $data = Json::decode($response->getBody());

            if ($status == 200) {
                if (!empty($data)) {
                    return $data;
                }
            } else {
                if (!empty($data['errorCode']) && !empty($data['description'])) {
                    $this->error_code = $data['errorCode'];
                    $this->error_msg = $data['errorCode'] . ' - ' . $data['description'];
                }
            }

            throw new \Exception($this->error_msg ?: "No Output For Publisher (WOGI) Response");
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
        }
        return false;
    }

    public function getPublisher()
    {
        $region = strtoupper(explode("_", $this->sku)[1]);

        $publisher = Publisher::findOne([
            'title' => 'Wogi ' . $region,
            'profile' => 'Wogi',
            'status' => 1
        ]);

        if (isset($publisher->publisher_id)) {
            $this->publisher = $publisher->publisher_id;
        } else {
            Yii::$app->slack->send('Error fetching Wogi Publisher Details', array(
                    array(
                        'color' => 'warning',
                        'text' => 'Wogi Publisher Not Found'
                    )
                )
            );
        }
    }
}