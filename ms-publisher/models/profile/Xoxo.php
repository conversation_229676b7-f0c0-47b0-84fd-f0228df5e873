<?php

namespace offgamers\publisher\models\profile;

use offgamers\base\traits\GuzzleTrait;
use offgamers\publisher\models\Publisher;
use offgamers\publisher\models\PublisherSetting;
use Psr\Http\Message\ResponseInterface;
use yii\base\InvalidArgumentException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class Xoxo extends \yii\base\Model
{
    use GuzzleTrait;

    /**
     * @var int publisher id
     */
    protected int $publisher_id;
    /**
     * @var string base url
     */
    protected string $base_url;
    /**
     * @var string client id
     */
    protected string $client_id;
    /**
     * @var string client secret code
     */
    protected string $client_secret;
    /**
     * @var string|null token
     */
    protected ?string $token = null;
    /**
     * @var string|null token expiry timestamp
     */
    protected ?string $refresh_token = null;
    /**
     * @var int error code from publisher
     */
    protected int $error_code;
    /**
     * @var string error message for error handler
     */
    public $error_msg;

    private const VALIDATE_ACCESS_TOKEN_URL = "v1/oauth/token";
    /**
     * Get Access Token before authentication token
     */
    protected const GENERATE_ACCESS_TOKEN_URL = "v1/oauth/token/user";
    /**
     * API URL for all query
     */
    protected const API_PATH = "v1/oauth/api";
    /**
     * URL to get list of vouchers
     */
    protected const API_VOUCHERS_QUERY = 'plumProAPI.mutation.getVouchers';

    /**
     * Get Config From DB
     * @param $reload_config
     * @return void
     */
    public function getConfig($reload_config = false)
    {
        if (empty($this->base_url) || $reload_config) {
            $publisher = Publisher::find()->where(['profile' => 'Xoxo', 'publisher_id' => $this->publisher_id])->exists();
            if ($publisher) {
                $config_list = ArrayHelper::map(PublisherSetting::find()->where(['publisher_id' => $this->publisher_id])->asArray()->all(), 'key', 'value');

                if (count($config_list) === 5) {
                    $this->base_url = $config_list['XOXO_BASE_API_URL'];
                    $this->client_id = $config_list['XOXO_CLIENT_ID'];
                    $this->client_secret = $config_list['XOXO_CLIENT_SECRET'];
                    $this->token = $config_list['XOXO_ACCESS_TOKEN'];
                    $this->refresh_token = $config_list['XOXO_REFRESH_TOKEN'];
                } else {
                    throw new InvalidArgumentException('Invalid Publisher Configuration' . count($config_list));
                }
            } else {
                throw new InvalidArgumentException('Invalid Publisher');
            }
        }
    }

    /**
     * Function to get request header for authenticated endpoint
     * @param $path
     * @param $method
     * @param $body
     * @return array
     */
    protected function getHeaders()
    {
        return [
            'Content-Type' => 'application/json',
            "Authorization" => "Bearer " . $this->token,
            "Accept" => '*/*',
        ];
    }

    /**
     * Generate Token Process
     * @param $attempt
     * @return string|void|null
     * @throws \Exception
     */
    protected function getToken() {
        if(!$this->checkTokenValidity()) { // If token expired or about to expired
            $params = [
                "grant_type" => "refresh_token",
                "refresh_token" => $this->refresh_token,
                "client_id" => $this->client_id,
                "client_secret" => $this->client_secret
            ];

            $response = $this->sendRequest("POST", self::GENERATE_ACCESS_TOKEN_URL, [], $params);

            if($data = $this->verifyResponse($response)) {
                $this->token = $data['access_token'];
                $this->refresh_token = $data['refresh_token'];
                $this->saveTokenToDb();
            }
            if(!$this->token) {
                throw new \Exception('Failed to get token from Xoxo (' . $this->publisher_id . ')');
            }
        }
    }

    private function checkTokenValidity() : bool {
        $response = $this->sendRequest("GET", self::VALIDATE_ACCESS_TOKEN_URL, $this->getHeaders());

        if($data = $this->verifyResponse($response)) {
            if($data['expires_in'] >= 3600) { // If token expires after one hour, no need to refresh
                return true;
            }
        }

        return false;
    }

    /**
     * Reset Token
     * @return void
     */
    protected function resetToken()
    {
        $this->token = '';
        $this->token_expiry = 0;
    }

    /**
     * Save token to database
     * @return void
     */
    protected function saveTokenToDb()
    {
        PublisherSetting::updateAll(['value' => $this->token], ['publisher_id' => $this->publisher_id, 'key' => 'XOXO_ACCESS_TOKEN']);
        PublisherSetting::updateAll(['value' => $this->refresh_token], ['publisher_id' => $this->publisher_id, 'key' => 'XOXO_REFRESH_TOKEN']);
    }

    /**
     * @param ResponseInterface $response
     * @return array|null
     */
    protected function verifyResponse(ResponseInterface $response): ?array
    {
        $this->error_msg = '';

        $status_code = $response->getStatusCode();
        $response_body = $response->getBody();
        try {
            $data = Json::decode($response_body);
            if ($status_code < 300) {
                return $data;
            } elseif (isset($data['error'])) {
                $data['code'] = $status_code;
                $this->error_code = $status_code;
                $this->error_msg = $this->getErrorFromResponse($data);
            } else {
                $this->error_msg = $status_code . ' - ' . $response->getReasonPhrase();
            }
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage() . ' - ' . (string)$response_body;
        }

        return null;
    }

    /**
     * @param $data
     * @return string
     */
    protected function getErrorFromResponse($data)
    {
        return ($data['code'] ?? '') . ' : ' . ($data['error_description'] ?? '');
    }

    /**
     * @param string $method
     * @param string $path
     * @param array $headers
     * @param array $params
     * @return ResponseInterface|null
     */
    protected function sendRequest(string $method, string $path, array $headers = [], array $params = []): ?ResponseInterface
    {
        $options = ['http_errors' => false];

        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        if ($params) {
            $options['json'] = $params;
        }

        return $this->client->request($method, $this->base_url . '/' . $path, $options);
    }
}