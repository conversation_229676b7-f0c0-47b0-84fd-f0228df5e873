<?php

namespace offgamers\publisher\models\profile\library;

class FuluAes
{
    private $key;
    private $cipher;

    /**
     * Aes constructor.
     * @param $key
     */
    function __construct($key, $cipher = 'aes-256-cbc')
    {
        $this->key = $key;
        $this->cipher = $cipher;
    }

    /**
     * @param $value
     * @return string
     */
    public function encrypt($value)
    {
        if (is_array($value)) {
            $value = json_encode($value);
        }

        $iv = $this->strRandom(openssl_cipher_iv_length($this->cipher));
        $value = openssl_encrypt($value, $this->cipher, $this->key, OPENSSL_RAW_DATA, $iv);
        if ($value === false) {
            throw new Exception('加密失败');
        }

        $json = json_encode(['iv' => base64_encode($iv), 'value' => base64_encode($value)]);

        return base64_encode($json);
    }

    /**
     * @param $str
     * @return string | array
     */
    public function decrypt($str, $jsonDecode = true, $jsonToArray = true)
    {
        $obj = json_decode(base64_decode($str));
        if (empty($obj)) {
            throw new \Exception('获取密文失败');
        }

        if (!isset($obj->iv) || !isset($obj->value)) {
            throw new \Exception('解密参数失败');
        }

        $iv = base64_decode($obj->iv);
        $value = base64_decode($obj->value);
        $decrypted = openssl_decrypt($value, $this->cipher, $this->key, OPENSSL_RAW_DATA, $iv);
        if ($decrypted === false) {
            throw new \Exception('解密失败');
        }

        if ($jsonDecode) {
            $decrypted = json_decode($decrypted, $jsonToArray);
        }

        return $decrypted;
    }

    public function strRandom($length = 16)
    {
        $string = '';

        while (($len = strlen($string)) < $length) {
            $size = $length - $len;

            $bytes = random_bytes($size);

            $string .= substr(str_replace(['/', '+', '='], '', base64_encode($bytes)), 0, $size);
        }

        return $string;
    }
}