<?php

namespace micro\components;

use Exception;
use Yii;
use yii\helpers\Json;

class ApiGeneral
{

    public $purifier;

    public function _isSerialize($data)
    {
        return (@unserialize($data) != null) ? true : false;
    }

    public function _isJson($data)
    {
        try {
            return (Json::decode($data) != null);
        } catch (Exception $ex) {
            return false;
        }
    }

    public function _purify($data)
    {
        if (is_array($data)) {
            $data = array_map([get_called_class(), '_purify'], $data);
        } else {
            $data = strip_tags(\yii\helpers\HtmlPurifier::process($data));
        }

        return $data;
    }

    public function _readInput()
    {
        $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : $_GET;

        if (isset($_SERVER['CONTENT_TYPE'])) {
            if ($_SERVER['CONTENT_TYPE'] == 'application/json') {
                $_data = file_get_contents("php://input");
            }
        }

        if (!empty($_data)) {
            if ($this->_isJson($_data)) {
                $_data = Json::decode($_data);
            } else if ($this->_isSerialize($_data)) {
                $_data = unserialize($_data);
            } else if (is_object($_data)) {
                $_data = (array) $_data;
            }
        }

        return $this->_purify($_data);
    }

    public function _sendResponse($result)
    {
        header("Cache-Control: no-store");
        header("Access-Control-Allow-Origin: *");
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        Yii::$app->response->data = $this->removeInvalidCharsFromArray($result);
        Yii::$app->end();
    }

    public function _validateSignature($input)
    {
        if (isset($input['time']) && isset($input['source']) && isset($input['signature'])) {
            if (isset(Yii::$app->params['identification'][$input['source']]['secret'])) {
                if ($input['signature'] == md5($input['source'] . $input['time'] . "|" . Yii::$app->params['identification'][$input['source']]['secret'])) {
                    return true;
                }
            }
        }

        $this->_errorReport('Invalid signature');
        return false;
    }

    public function _errorReport($error)
    {
        $message = "API Response : " . $error . "\n";
        $message .= "Request URI : " . getenv('REQUEST_URI') . "\n";
        $message .= "Request Variables : ";

        $_data = $this->_readInput();
        if (!empty($_data)) {
            $message .= json_encode($_data);
        } else {
            $message .= "- empty -";
        }

        $subject = Yii::$app->name . ' API Error from ' . (!empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'unknown') . ' - ' . date("F j, Y H:i");
        
        Yii::$app->slack->send(
            $subject,
            [[
                'color' => 'danger',
                'title' => $error,
                'text' => $message,
                'ts' => time()
            ]],
            'DEFAULT'
        );
    }

    public static function removeInvalidChars($text)
    {
        $regex = '/( [\x00-\x7F] | [\xC0-\xDF][\x80-\xBF] | [\xE0-\xEF][\x80-\xBF]{2} | [\xF0-\xF7][\x80-\xBF]{3} ) | ./x';
        return preg_replace($regex, '$1', $text);
    }

    public static function removeInvalidCharsFromArray($array)
    {
        foreach ($array as &$data) {
            if (is_array($data)) {
                $data = self::removeInvalidCharsFromArray($data);
            } else if (is_string($data)) {
                $data = self::removeInvalidChars($data);
            }
        }
        return $array;
    }
}
