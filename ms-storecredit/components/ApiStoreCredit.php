<?php

namespace micro\components;

use Yii;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use yii\helpers\Json;
use micro\models\StoreCreditRequest;

class ApiStoreCredit
{
    public $id, $requestId, $ordersId, $referenceId, $transactionType, $requestingId,
        $requestingRole, $totalAmount, $amount, $currency, $listType, $activity, $param1,
        $startDate, $endDate, $page, $limit, $sort, $snapshotDate, $showCustomer, $allowNegative;

    public $source, $checkingType, $conversionRate, $role, $message, $currentCurrency, $error_response,
        $scenario, $discrepancy;

    public $errors = false;

    const   BALANCE_CACHE_KEY = 'store_credit_balance/';

    use \offgamers\base\traits\GuzzleTrait;

    public function __construct($scenario, $input, $id = '')
    {
        $model = new \micro\models\ApiStoreCreditForm;
        $model->scenario = $scenario;
        $this->scenario = $scenario;

        try {
            if (!empty($id)) {
                $model->customers_id = $id;
            }

            foreach ($input as $key => $value) {
                $model->$key = $value;
            }

            // validate input
            if ($model->validate()) {
                $this->source = $model->source;
                $this->requestId = (!empty($model->request_id)) ? $model->request_id : '';
                $this->id = (!empty($model->customers_id)) ? $model->customers_id : '';
                $this->listType = (!empty($model->list_type)) ? $model->list_type : '';
                $this->startDate = (!empty($model->start_date)) ? $model->start_date : '';
                $this->endDate = (!empty($model->end_date)) ? $model->end_date : '';
                $this->snapshotDate = (!empty($model->snapshot_date)) ? $model->snapshot_date : '';
                $this->page = (!empty($model->page)) ? $model->page : 1;
                $this->limit = (!empty($model->limit)) ? $model->limit : 20;
                $this->sort = (!empty($model->sort)) ? $model->sort : '';

                $this->ordersId = (!empty($model->orders_id)) ? $model->orders_id : '';
                $this->referenceId = (!empty($model->reference_id)) ? $model->reference_id : '';
                $this->role = (!empty($model->customers_role)) ? $model->customers_role : '';
                $this->transactionType = (!empty($model->transaction_type)) ? $model->transaction_type : '';
                $this->requestingId = (!empty($model->requesting_id)) ? $model->requesting_id : '';
                $this->requestingRole = (!empty($model->requesting_role)) ? $model->requesting_role : '';
                $this->amount = (!empty($model->amount)) ? $model->amount : '';
                $this->totalAmount = (!empty($model->total_amount)) ? $model->total_amount : '';
                $this->currency = (!empty($model->currency)) ? $model->currency : '';
                $this->activity = (!empty($model->activity)) ? $model->activity : '';
                $this->message = (!empty($model->message)) ? $model->message : '';
                $this->showCustomer = (!empty($model->show_customer)) ? $model->show_customer : '0';
                $this->allowNegative = (!empty($model->allow_negative)) ? $model->allow_negative : 0;

                $this->checkingType = (!empty($model->checking_type)) ? $model->checking_type : 'BALANCE';
                $this->conversionRate = (!empty($model->conversion_rate)) ? $model->conversion_rate : '';
            } else {
                $this->errors['code'] = 98;
                $this->errors['message'] = 'Invalid Data : ' . json_encode($model->getErrors());
            }
        } catch (\Exception $exception) {
            $this->errors['code'] = 98;
            $this->errors['message'] = 'Invalid Data';
        }

        if ($this->errors) {
            $this->error_response = [
                'code' => $this->errors['code'],
                'message' => $this->errors['message']
            ];
        }
    }

    private function convertDate($datetime)
    {
        // create a $dt object with the Asia/Kuala_Lumpur timezone
        $dt = new \DateTime(date('Y-m-d H:i:s', $datetime), new \DateTimeZone('Asia/Kuala_Lumpur'));
        // change the timezone of the object without changing it's time
        $dt->setTimezone(new \DateTimeZone('UTC'));
        return $dt->getTimestamp();
    }

    private function getJwtToken($time)
    {
        // Check if request from backend or frontend
        $tokenSubject = (Yii::$app->params['identification'][$this->source]['role'] == 'admin') ? 'system' : (string)$this->id;
        $tokenSubType = Yii::$app->params['identification'][$this->source]['role'];
        $tokenSubRole = [Yii::$app->params['identification'][$this->source]['role']];

        $signer = new \Lcobucci\JWT\Signer\Rsa\Sha256();

        $token = Yii::$app->jwt->getBuilder()
            ->setIssuer(Yii::$app->params['sc.serverless.issuer'])
            ->setSubject($tokenSubject)
            ->setAudience(Yii::$app->params['sc.serverless.audience'])
            ->setExpiration($time + 900)
            ->setIssuedAt($time)
            ->set('subType', $tokenSubType)
            ->set('subRole', $tokenSubRole)
            ->sign($signer, new Key('file://' . Yii::getAlias('@privateKeySls')))
            ->getToken();

        return $token;
    }

    private function generateRequestId()
    {
        $createNew = false;
        // Check for orders_products_id / payment_id
        $whereOptions['activity_type'] = $this->activity;
        $whereOptions['reference_id'] = $this->referenceId;
        switch ($this->activity) {
            case 'C':
            case 'S':
            case 'XS':
                $whereOptions['reference_key'] = 'orders_products_id';
                break;
            case 'D': // OP Redemption to SC
                $whereOptions['reference_key'] = 'redemption_id';
                break;
            case 'GC':
                $whereOptions['reference_key'] = 'ogc_id';
                break;
            case 'PW':
                $whereOptions['reference_key'] = 'payment_id';
                break;
            case 'X':
            case 'R':
            case 'P':
            case 'RP': // Order move from cancel to pending status
                $whereOptions['reference_key'] = 'orders_id';
                break;
            default:
                $whereOptions['reference_key'] = 'customers_id';
                break;
        }

        // Check for manual transaction by admin
        if ($this->activity == 'MX') {
            $scRequest = false;
        } else {
            $scRequest = StoreCreditRequest::find()->where($whereOptions)->orderBy(['id' => SORT_DESC])->one();
        }

        // exist and its manual transaction
        if ($scRequest && ($this->activity == 'MI' || $this->activity == 'MR')) {
            // check if the request within 30 sec and same amount
            if ($this->amount == $scRequest->currency_from_amount && $this->currency == $scRequest->currency_from && time() <= ($scRequest->updated_at + 30)) {
                $this->errors = [
                    'code' => 18,
                    'message' => 'Duplicate Request or Wait 30 Sec'
                ];
                $errorRequestId = $scRequest->request_id;
            } else {
                $createNew = true;
            }
        } elseif ($scRequest) { // Transaction exist
            if ($scRequest->status == 1) { // Processing
                // Check serverless on the transaction status
                $this->requestId = $scRequest->request_id;
                $this->start_date = $scRequest->created_at;
                $this->end_date = time();
                if ($result = $this->scApi()) {
                    $scRequest->status = 2;
                    $scRequest->update();
                    return [
                        'request_id' => $scRequest->request_id,
                        'message' => 'Processed Successfully',
                    ];
                }
            } elseif ($scRequest->status == 2) { // Completed
                // Check duplicates
                $duplicates = $this->checkDuplicateTransaction($scRequest);
                // Check transaction allowed amount
                $allowedAmount = $this->transactionAllowedAmount($whereOptions);

                if ($duplicates || $allowedAmount) {
                    $this->errors = [
                        'code' => 18,
                        'message' => 'Duplicate Request'
                    ];
                    $errorRequestId = $scRequest->request_id;
                } else {
                    $createNew = true;
                }
            } elseif ($scRequest->status == 3) {
                $createNew = true;
            } else { // Re-use request_id from before as it failed to send to serverless
                $this->requestId = $scRequest->request_id;
            }
        } else { // Without orders_products_id / payments_id (Manual / Conversion)
            $createNew = true;
        }

        if ($createNew) {
            $newData = new StoreCreditRequest();
            $newData->reference_key = $whereOptions['reference_key'];
            $newData->reference_id = $this->referenceId;
            $newData->transaction_type = $this->transactionType;
            $newData->activity_type = $this->activity;
            $newData->currency_from = $this->currency;
            $newData->currency_from_amount = $this->amount;
            $newData->currency_to = $this->currency;
            $newData->currency_to_amount = $this->amount;
            $newData->currency_rate = 1;
            $newData->save();

            // Generte request_id
            $newData->request_id = 'OG-' . $this->activity . '-' . $newData->id;
            $newData->update();

            $this->requestId = $newData->request_id;

            // re-check the row entry for double action
            $recheckWhere = [
                'reference_key' => $newData->reference_key,
                'reference_id' => $newData->reference_id,
                'transaction_type' => $newData->transaction_type,
                'activity_type' => $newData->activity_type,
                'status' => 0
            ];

            $requestTotal = StoreCreditRequest::find()->where($recheckWhere)->count();
            if ($requestTotal > 1) {
                $this->errors = [
                    'code' => 18,
                    'message' => 'Duplicate Request'
                ];
                $errorRequestId = $newData->request_id;
            }
        }

        if ($this->errors) {
            $this->error_response = [
                'code' => $this->errors['code'],
                'message' => $this->errors['message']
            ];
            // Send slack error to do manual check and failed the process.
            $this->reportErrorDiscrepancy($errorRequestId);
        }
    }

    private function checkDuplicateTransaction($request)
    {
        if ($request->reference_key == 'redemption_id' || $request->reference_key == 'ogc_id' || $request->reference_key == 'payment_id') {
            return true;
        }

        return false;
    }

    private function transactionAllowedAmount($whereOptions)
    {
        $processedAmount = 0;
        $amountUsd = 0;
        $totalAmountUsd = 0;
        $margin = 0;
        $calAmount = 0;
        $sendNotice = false;
        $error = false;

        if ($whereOptions['reference_key'] == 'orders_id' && ($whereOptions['activity_type'] == 'P' || $whereOptions['activity_type'] == 'RP')) {
            $whereQuery = ['reference_id' => $whereOptions['reference_id'], 'reference_key' => $whereOptions['reference_key'], 'status' => 2];
        } else {
            $whereQuery = $whereOptions;
            $whereQuery['status'] = 2;
        }
        // get all related transaction
        $scRequestAll = StoreCreditRequest::find()->where($whereQuery)->all();
        foreach ($scRequestAll as $key => $request) {
            switch ($request->transaction_type) {
                case 'ADD_CREDIT':
                    $op = 'bcadd';
                    break;
                case 'DEDUCT_CREDIT':
                    $op = 'bcsub';
                    break;
            }
            // Change to USD for calculations
            $prevAmountUsd = Yii::$app->currency->advanceCurrencyConversion($request->currency_from_amount, $request->currency_from, 'USD', true, 'sell');

            $processedAmount = $op($processedAmount, $prevAmountUsd, Yii::$app->currency->currency_list['USD']['decimal_places']);
        }

        // Change to USD for calculations
        $amountUsd = Yii::$app->currency->advanceCurrencyConversion($this->amount, $this->currency, 'USD', true, 'sell');
        $totalAmountUsd = Yii::$app->currency->advanceCurrencyConversion($this->totalAmount, $this->currency, 'USD', true, 'sell');

        // Calculation to determine the limit for order refund or cancel
        if ($whereOptions['reference_key'] == 'orders_id' && ($whereOptions['activity_type'] == 'R' || $whereOptions['activity_type'] == 'X')) {
            $processedAmount = $totalAmountUsd - abs($processedAmount);
        }

        // check orders purchase related transaction
        if (($this->transactionType == 'ADD_CREDIT' && $whereOptions['reference_key'] == 'orders_id') || ($this->transactionType == 'DEDUCT_CREDIT' && $whereOptions['reference_key'] == 'orders_products_id')) {

            if (abs($processedAmount) == 0) {
                $margin = 10;
            } else {
                if ($amountUsd <= abs($processedAmount)) {
                    // do nothing let it pass
                } else {
                    // Calculate margin for conversion differences to accomodate disparity
                    $marginDiff = ($amountUsd - abs($processedAmount));
                    $margin = $marginDiff / abs($processedAmount) * 100;
                    // Margin less than certain % or the amount is too small
                    if ($margin > 1.5 || $amountUsd < 0.03) {
                        $sendNotice = true;
                    }
                }
            }
        } elseif (($this->transactionType == 'ADD_CREDIT' && $whereOptions['reference_key'] == 'orders_products_id') || ($this->transactionType == 'DEDUCT_CREDIT' && $whereOptions['reference_key'] == 'orders_id')) {

            $marginCal = false;

            $calAmount = $totalAmountUsd - abs($processedAmount);
            if ($amountUsd <= $calAmount) {
                $marginCal = true;
            } else {
                if (abs($calAmount) != 0) {
                    // Calculate margin for conversion differences to accomodate disparity
                    $marginDiff = ($amountUsd - abs($calAmount));
                    $margin = $marginDiff / abs($calAmount) * 100;
                    // Margin less than certain % or the amount is too small
                    $marginCal = ($margin <= 1.5 || $amountUsd < 0.03) ? true : false;
                } else {
                    $margin = 10;
                }
            }
            if ($calAmount <= 0 && !$marginCal && $amountUsd > $totalAmountUsd) {
                $sendNotice = true;
            }
        }

        if ($sendNotice) {
            $this->discrepancy = ['margin' => $margin, 'processedAmount' => $processedAmount, 'callAmount' => $calAmount, 'amountUsd' => $amountUsd, 'totalAmountUsd' => $totalAmountUsd];
            $this->errors = [
                'code' => 30,
                'message' => 'Store Credit Discrepancy Checking Notice'
            ];
            $this->reportErrorDiscrepancy('- empty -');
            $this->errors = null;
            $this->discrepancy = null;
        }

        if ($margin > 5) {
            $error = true;
        }

        return $error;
    }

    private function scApi($requestType = '')
    {
        $method = 'POST';
        $apiEndpoint = '';
        $microtime = microtime(true);
        $log_level = 0;
        $tag = '';

        switch (strtolower($requestType)) {
            case 'view':
                $apiEndpoint = 'get-balance';
                $extraParams = ['user_id' => $this->id];
                if ($this->checkingType == 'CONVERSION') {
                    $apiEndpoint = 'check-convert';
                    $extraParams['conversion_rate'] = floatval($this->conversionRate);
                    $extraParams['currency'] = $this->currency;
                }
                break;
            case 'post':
                switch ($this->transactionType) {
                    case 'ADD_CREDIT':
                        $apiEndpoint = 'add';
                        break;
                    case 'DEDUCT_CREDIT':
                        $apiEndpoint = 'subtract';
                        break;
                    case 'CONVERT_CREDIT':
                        $apiEndpoint = 'convert';
                        break;
                }

                $log_level = 1;
                $tag = $this->transactionType . '/' . $this->id . '/' . $this->ordersId;

                $this->requestId = (!empty($this->requestId)) ? $this->requestId : 'OG' . $this->id . str_replace('.', '', $microtime);

                $extraParams = [
                    'request_id' => $this->requestId,
                    'order_id' => $this->ordersId,
                    'user_id' => $this->id,
                    'user_role' => $this->role,
                    'requesting_id' => $this->requestingId,
                    'requesting_role' => $this->requestingRole,
                    'amount' => (!empty($this->amount)) ? floatval($this->amount) : '',
                    'currency' => $this->currency,
                    'current_currency' => $this->currentCurrency,
                    'conversion_rate' => (!empty($this->conversionRate)) ? floatval($this->conversionRate) : '',
                    'activity' => $this->activity,
                    'activity_title' => Yii::$app->params['sc.activity.code'][$this->activity],
                    'activity_description' => $this->message,
                    'param_1' => $this->showCustomer,
                ];
                // check if deduct need to send allow_negative or not
                if ($this->allowNegative > 0) {
                    $extraParams['allow_negative'] = 1;
                }
                break;
            case 'status':
                $apiEndpoint = 'set-user-status';
                $extraParams = [
                    'user_id' => $this->id,
                    'status' => ($this->transactionType == 'SC_ACTIVATE') ? 'ACTIVE' : 'SUSPENDED'
                ];
                $log_level = 1;
                break;
            default:
                if ($this->listType == 'OPENBALANCE' || $this->listType == 'CLOSEBALANCE') {
                    $apiEndpoint = 'currency-snapshot';

                    $openCloseDate = ($this->listType == 'OPENBALANCE') ? date('Y-m-d', $this->snapshotDate) : date('Y-m-d', strtotime('+1 day', $this->snapshotDate));

                    $extraParams = ['snapshot_date' => $openCloseDate];
                } else {
                    $apiEndpoint = ($this->listType == 'TRANSACTION') ? 'get-transaction' : 'get-request';
                    $extraParams = [
                        'request_id' => $this->requestId,
                        'order_id' => $this->ordersId,
                        'user_id' => $this->id,
                        'start_date' => ($this->startDate) ? $this->convertDate($this->startDate) : '',
                        'end_date' => ($this->endDate) ? $this->convertDate($this->endDate) : '',
                        'activity' => $this->activity,
                        'page' => intval($this->page),
                        'limit' => intval($this->limit),
                        'sort' => $this->sort,
                    ];
                }
                break;
        }

        // Clean data params if empty
        $extraParams = array_filter($extraParams, function ($value) {
            return ($value !== null && $value !== false && $value !== '');
        });

        // Outgoing Log
        $this->initClient($log_level, $tag);

        // Use retry method for curl to microservice
        $i = 0;
        while ($i++ < 3) {
            // reset errors
            $this->errors = null;
            $this->error_response = null;

            $start_time = time();
            $start_time_m = microtime(true);

            $token = $this->getJwtToken($start_time);

            $authorization = ['Authorization' => (string)$token];

            $options = array(
                'verify' => (!empty(Yii::$app->params['guzzle.verifySsl']) ? Yii::$app->params['guzzle.verifySsl'] : false),
                'headers' => $authorization,
                'json' => $extraParams,
                'http_errors' => false
            );

            if (!empty(Yii::$app->params['proxy'])) {
                $options['proxy'] = Yii::$app->params['proxy'];
            }

            try {
                $request_url = Yii::$app->params['sc.serverless.baseUrl'] . '/' . $apiEndpoint;
                $response = $this->client->request($method, $request_url, $options);
                $statusCode = $response->getStatusCode();
                $statusText = $response->getReasonPhrase();
                $body = $response->getBody();
                $result = Json::decode($body);

                if ($statusCode == 200) {
                    if ($requestType == 'post') {
                        $this->clearAccountBalanceCache();
                        return [
                            'request_id' => $this->requestId,
                            'amount' => $this->amount,
                            'currency' => $this->currency,
                            'message' => $result['messages'],
                        ];
                    }

                    // clear cache if update status
                    if ($requestType == 'status') {
                        $this->clearAccountBalanceCache();
                        return [
                            'message' => $result['messages'],
                        ];
                    }

                    // return full array if Item code found
                    if (isset($result['payload']['Items'])) {
                        return $result['payload'];
                    } elseif (isset($result['payload'])) {
                        $payload['Items'] = $result['payload'];
                        return $payload;
                    }
                } elseif ($statusCode > 200) {
                    // No Store Credit Account, No Action Required
                    if (isset($result['code']) && $result['code'] == 14) {
                        $this->error_response = [
                            'code' => $result['code'],
                            'message' => $result['messages']
                        ];
                        return;
                    }
                    $this->errors['code'] = (!empty($result['code']) ? $result['code'] : $statusCode);
                    $this->errors['message'] = (!empty($result['messages']) ? $result['messages'] : $statusText);
                    $this->errors['aws_request_id'] = (!empty($result['aws_request_id']) ? $result['aws_request_id'] : '');
                    $this->errors['raw_response'] = $body;
                } else {
                    // shouldn't appear, minimize script
                    $this->errors['code'] = 99;
                    $this->errors['message'] = 'Unknown Error';
                }
            } catch (\Exception $e) {
                $this->errors['code'] = 99;
                $this->errors['message'] = $e->getMessage();
            }

            $this->errors['http_code'] = (isset($statusCode) && !empty($statusCode)) ? $statusCode : 500;
            $this->errors['request_url'] = $request_url;
            $this->errors['token'] = (string)$token;
            $this->errors['extra_params'] = Json::encode($extraParams);
            $this->errors['start_time'] = $start_time;
            $this->errors['total_time'] = (int)((microtime(true) - $start_time_m) * 1000);
            $this->errors['total_try'] = $i;
            $this->error_response = [
                'code' => $this->errors['code'],
                'message' => $this->errors['message']
            ];
            $this->reportError();

            // Check for error code that dont need retry
            if (in_array($this->errors['code'], range(13,25))) {
                return;
            }

            // Delays the program execution
            if ($i < 3) {
                sleep($i);
            }
        }
    }

    public function getStoreCreditList()
    {
        return ($this->errors) ? $this->errors : $this->scApi();
    }

    public function getStoreCredit($input)
    {
        return ($this->errors) ? $this->errors : $this->scApi('view');
    }

    public function setStoreCredit()
    {
        if ($this->errors) {
            return $this->errors;
        }

        // Generate Request ID
        $result = $this->generateRequestId();
        if ($this->errors) {
            return $this->errors;
        } elseif (isset($result['request_id']) && !empty($result['request_id'])) {
            return $result;
        }

        $scRequest = StoreCreditRequest::find()->where(['request_id' => $this->requestId])->one();
        // Check currency and account status
        $userBalance = $this->scApi('view');

        if (isset($this->error_response['code']) && $this->error_response['code'] == 14) {
            // means new user, amount and currency follow from the new selections
        } elseif (isset($userBalance['Items']['currency'])) {
            // Check currency matching?
            if ($userBalance['Items']['currency'] != $this->currency) {
                // Check if from frontend use BUY
                $useRate = ($this->source == 'frontend') ? 'buy' : 'sell';
                $currencyRate = Yii::$app->currency->advanceCurrencyConversionRate($this->currency, $userBalance['Items']['currency'], $useRate);

                $this->amount = number_format(($this->amount * $currencyRate), Yii::$app->currency->currency_list[$userBalance['Items']['currency']]['decimal_places'], '.', '');
                $this->currency = $userBalance['Items']['currency'];

                $scRequest->currency_to = $this->currency;
                $scRequest->currency_to_amount = $this->amount;
                $scRequest->currency_rate = $currencyRate;
                $scRequest->update();
            }
        }
        // Set processing status
        $scRequest->status = 1;
        $scRequest->update();

        $postResult = $this->scApi('post');

        // Update request status 2-completed / 3-failed
        if (isset($this->error_response) && !empty($this->error_response)) {
            $scRequest->status = 3;
        } else {
            $scRequest->status = 2;
        }
        $scRequest->update();

        return $postResult;
    }

    public function convertStoreCredit($input)
    {
        if ($this->errors) {
            return $this->errors;
        }

        // Generate Request ID
        $result = $this->generateRequestId();
        if ($this->errors) {
            return $this->errors;
        } elseif (isset($result['request_id']) && !empty($result['request_id'])) {
            return $result;
        }

        $scRequest = StoreCreditRequest::find()->where(['request_id' => $this->requestId])->one();
        $scBalance = $this->scApi('view');

        if ($scBalance['Items']['currency'] == $this->currency) {
            $this->errors['code'] = 16;
            $this->errors['message'] = 'Same Currency Conversion';
        } else {
            $this->currentCurrency = (isset($scBalance['Items']['currency']) && !empty($scBalance['Items']['currency'])) ? $scBalance['Items']['currency'] : '';

            $scRequest->currency_from = $this->currentCurrency;
            $scRequest->currency_from_amount = $scBalance['Items']['balance'];
            $scRequest->currency_to = $this->currency;
            $scRequest->currency_rate = $this->conversionRate;
            $scRequest->status = 1;
            $scRequest->update();

            // Do conversion
            $convertResult = $this->scApi('post');

            if (isset($this->error_response) && !empty($this->error_response)) {
                $scRequest->currency_to_amount = 0;
                $scRequest->status = 3;
            } else {
                // Check new balance
                $newScBalance = $this->scApi('view');
                $scRequest->currency_to_amount = $newScBalance['Items']['balance'];
                $scRequest->status = 2;
            }
            $scRequest->update();

            return $convertResult;
        }
    }

    public function updateAccountStatus($input)
    {
        return ($this->errors) ? $this->errors : $this->scApi('status');
    }

    public function getScToken()
    {
        if (Yii::$app->params['sc.serverless.get.token']) {
            $time = time();

            $token = $this->getJwtToken($time);

            return ['token' => (string)$token];
        } else {
            $this->error_response = [
                'code' => 99,
                'message' => 'Invalid Access'
            ];
        }
    }

    public function clearAccountBalanceCache()
    {
        $cache_key = self::BALANCE_CACHE_KEY . $this->id;
        for ($i = 0; $i < 3; $i++) {
            Yii::$app->cache->delete($cache_key);
            //check cache status to prevent cache available after clearing
            if (Yii::$app->cache->get($cache_key) == false) {
                break;
            }
        }

        for ($i = 0; $i < 3; $i++) {
            Yii::$app->frontend_cache->delete($cache_key);
            //check cache status to prevent cache available after clearing
            if (Yii::$app->frontend_cache->get($cache_key) == false) {
                break;
            }
        }
    }

    private function reportError()
    {
        $message = "API Response : " . $this->errors['message'] . "\n";
        $message .= "Request URI : " . $this->errors['request_url'] . "\n";
        $message .= "Requester : " . (!empty($this->requestingId) ? $this->requestingId : $this->source) . "\n";
        $message .= "AWS Request ID : " . (!empty($this->errors['aws_request_id']) ? $this->errors['aws_request_id'] : 'Not found') . "\n";
        $message .= "JWT Token : " . (!empty($this->errors['token']) ? $this->errors['token'] : 'Not found') . "\n";
        $message .= "Request Variables : " . (!empty($this->errors['extra_params']) ? $this->errors['extra_params'] : '- empty -') . "\n";
        $message .= "Server Response : " . (!empty($this->errors['raw_response']) ? $this->errors['raw_response'] : 'Empty Response From Server') . "\n";
        $message .= "Request Timestamp : " . $this->errors['start_time'] . "\n";
        $message .= "Total Time(ms) : " . $this->errors['total_time'] . "\n";
        $message .= "Try # : " . $this->errors['total_try'];

        $subject = Yii::$app->name . ' API Error from ' . (!empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'unknown') . ' - ' . date("F j, Y H:i");

        \offgamers\base\models\DevDebugLog::generateDebugLog('Store Credit Serverless Error : ' . $this->errors['http_code'], explode("\n", $message));

        Yii::$app->slack->send(
            $subject,
            [
                [
                    'color' => 'danger',
                    'title' => $this->errors['http_code'] . (($this->errors['code'] != $this->errors['http_code']) ? '-' . $this->errors['code'] : '') . '-' . $this->errors['message'],
                    'text' => $message,
                    'ts' => time()
                ]
            ],
            'DEFAULT'
        );
    }

    private function reportErrorDiscrepancy($requestId)
    {
        $message = "API Response : Duplicate Request\n";
        $message .= "Requester : " . (!empty($this->requestingId) ? $this->requestingId : $this->source) . "\n";
        $message .= (!empty($this->id)) ? "Customers ID : " . $this->id . "\n" : '';
        $message .= (!empty($this->ordersId)) ? "Orders ID : " . $this->ordersId . "\n" : '';
        $message .= "Request ID : " . $requestId . "\n";
        $message .= "Request Timestamp : " . microtime(true) . "\n";
        $message .= (!empty($this->discrepancy)) ? "Discrepancy Info : " . json_encode($this->discrepancy) . "\n" : '';

        Yii::$app->slack->send(
            Yii::$app->name . ' API Error from ' . (!empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'unknown') . ' - ' . date("F j, Y H:i"),
            [
                [
                    'color' => 'danger',
                    'title' => $this->errors['code'] . '-' . $this->errors['message'],
                    'text' => $message,
                    'ts' => time()
                ]
            ],
            'ANB'
        );

        $this->discrepancy = null;
    }
}
