<?php

Yii::set<PERSON><PERSON><PERSON>('@micro', dirname(__DIR__));
Yii::set<PERSON>lias('@privateKeySls', '@micro/config/sc_php_private.pem');

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
    require(__DIR__ . '/params-encoded.php')
);

return [
    'id' => 'ms-storecredit',
    'name' => 'Store Credit Microservice',
    // the basePath of the application will be the `micro-app` directory
    'basePath' => dirname(__DIR__),
    // this is where the application will find all controllers
    'controllerNamespace' => 'micro\controllers',
    'bootstrap' => ['log'],
    'runtimePath' => '@micro/runtime',
    'components' => [
        'jwt' => [
            'class' => 'sizeg\jwt\Jwt',
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => $params['slack.webhook.default'],
                'ANB' => $params['slack.webhook.anb']
            ]
        ],
        'currency' => [
            'class' => 'offgamers\base\components\Currency',
        ],
        'response' => [
            'format' => \yii\web\Response::FORMAT_JSON,
        ],
    	'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                ['class' => 'yii\rest\UrlRule', 'controller' => 'store-credit']
            ],
        ],
    ],
    'params' => $params,
];