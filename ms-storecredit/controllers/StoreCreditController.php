<?php

namespace micro\controllers;

use micro\components\ApiGeneral;
use micro\components\ApiStoreCredit;
use Yii;
use yii\helpers\BaseJson;

class StoreCreditController extends \yii\web\Controller
{
    public $input = array();
    public $result = array();
    public $status = false;
    public $error;

    public function beforeAction($action)
    {
        $apiObj = new ApiGeneral;

        $this->input = $apiObj->_readInput();
        if ($apiObj->_validateSignature($this->input)) {
            return true;
        } else {
            $this->error = 'Invalid signature';
        }

        $apiObj->_sendResponse(array(
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ));

        return false;
    }

    public function afterAction($action, $result)
    {
        $apiObj = new ApiGeneral;

        $apiObj->_sendResponse(array(
            'status' => $this->status,
            'result' => $this->result,
            'error' => $this->error
        ));

        return true;
    }

    public function actionGetToken()
    {
        $objSc = new ApiStoreCredit('get_token', $this->input);
        $data = $objSc->getScToken();

        $this->returnResponse($objSc, $data);
    }

    public function actionIndex()
    {
        if ($this->input['list_type'] == 'OPENBALANCE' || $this->input['list_type'] == 'CLOSEBALANCE') {
            $objSc = new ApiStoreCredit('sc_openclose', $this->input);
        } else {
            $objSc = new ApiStoreCredit('sc_list', $this->input);
        }

        $data = $objSc->getStoreCreditList();
        $this->returnResponse($objSc, $data);
    }

    public function actionView($id)
    {
        if (isset($this->input['checking_type']) && !empty($this->input['checking_type']) && $this->input['checking_type'] == 'CONVERSION') {
            $objSc = new ApiStoreCredit('sc_info_conversion', $this->input, $id);
        } else {
            $objSc = new ApiStoreCredit('sc_info_balance', $this->input, $id);
        }

        $cache_key = $objSc::BALANCE_CACHE_KEY.$id;
        $data = Yii::$app->cache->get($cache_key);

        if($data == false){
            $data = $objSc->getStoreCredit($this->input);
            $this->returnResponse($objSc, $data);
            if($objSc->scenario == 'sc_info_balance' && $this->status == true){
                // Cache Content For One Hour
                Yii::$app->cache->set($cache_key,$data,3600);
            }
        }
        else{
            $this->returnResponse($objSc,$data);
        }
    }

    public function actionCreate()
    {
        $objSc = new ApiStoreCredit('sc_transaction', $this->input);
        $data = $objSc->setStoreCredit();
        $this->returnResponse($objSc, $data);
    }

    public function actionUpdate($id)
    {
        if ($this->input['transaction_type'] == 'SC_ACTIVATE' || $this->input['transaction_type'] == 'SC_SUSPEND') {
            $objSc = new ApiStoreCredit('sc_status', $this->input, $id);
            $data = $objSc->updateAccountStatus($this->input);
        } else {
            $objSc = new ApiStoreCredit('sc_convert', $this->input, $id);
            $data = $objSc->convertStoreCredit($this->input);
        }
        
        $this->returnResponse($objSc, $data);
    }

    private function returnResponse($objSc, $data)
    {
        $this->status = ($objSc->error_response) ? false : true;
        $this->result = ($objSc->error_response) ? $this->input : $data;
        $this->error = $objSc->error_response;
    }
}