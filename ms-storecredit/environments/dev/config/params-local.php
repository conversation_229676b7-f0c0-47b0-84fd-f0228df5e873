<?php
return [
	// Set proxy if needed
    'proxy' => 'my-proxy.offgamers.lan:3128',

    //Cache Server
    'cache.host' => 'localhost',
    'cache.port' => 11211,

    // if not using ssl = false
    'guzzle.verifySsl' => false,

    // Store Credit Serverless base URL
    'sc.serverless.baseUrl' => 'https://tfh88eno27.execute-api.us-east-1.amazonaws.com/staging',

    // Store Credit Activity code
    'sc.activity.code' => [
    	'X' => 'Cancel',
    	'C' => 'Compensate',
    	'MI' => 'Manual Addition',
    	'MR' => 'Manual Deduction',
        'MX' => 'Manual Conversion',
    	'P' => 'Purchase',
    	'R' => 'Refund',
    	'B' => 'Bonus',
    	'D' => 'Redeem',
    	'S' => 'Store Credit Top Up',
    	'XS' => 'Extra Store Credit',
    	'GC' => 'Gift Card Redemption',
    	'PW' => 'OffGamers Payment',
    	'RP' => 'Re-Purchase',
    	'V' => 'Currency Conversion'
    ],
];