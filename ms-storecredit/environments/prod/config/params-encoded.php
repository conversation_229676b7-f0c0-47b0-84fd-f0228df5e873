<?php
return [
    // offgamers db conection
    'db.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.username' => '',
    'db.password' => '',

    // log db connection
    'db.log.dsn' => 'mysql:host=localhost;dbname=offgamers_log',
    'db.log.username' => 'root',
    'db.log.password' => 'root',

    // og db connection
    'db.og.dsn' => 'mysql:host=localhost;dbname=offgamers',
    'db.og.username' => '',
    'db.og.password' => '',

    // slave db connection
    'db.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',
    'db.log.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers_log',
    'db.og.slave.dsn' => 'mysql:host=rr-localhost;dbname=offgamers',

    // Slack default usage
    'slack.webhook.default' => '',
    'slack.webhook.anb' => '',

    // Identification for each portal accessing
    'identification' => [
        'og-crew' => ['secret' => '', 'role' => 'admin'],
        'crew2' => ['secret' => '', 'role' => 'admin'],
        'frontend' => ['secret' => '', 'role' => 'admin'],
        'shasso' => ['secret' => '', 'role' => 'admin'],
        'ms-order' => ['secret' => '', 'role' => 'admin'],
    ],

    // Store Credit Serverless JWT AUTH
    'sc.serverless.issuer' => 'OGPHP',
    'sc.serverless.audience' => 'https://ms-storecredit.offgamers.com',
    'sc.serverless.get.token' => false,

    // AWS
    'aws.key' => '',
    'aws.secret' => '',

    'aws.encrypt.log.key' => '',
    'aws.encrypt.log.secret' => '',
    'aws.encrypt.log.bucket_key' => '',
    'aws.encrypt.log.kms_key' => '',

];