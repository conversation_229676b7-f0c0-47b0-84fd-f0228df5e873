<?php

namespace micro\models;

class ApiStoreCreditForm extends \yii\base\Model
{
	public $source, $signature, $time, $request_id, $customers_id, $list_type, $start_date, $end_date, $sort, $page, $limit;
	public $orders_id, $reference_id, $customers_role, $transaction_type, $requesting_id, $requesting_role, $amount, $total_amount, $currency, $activity, $message;
	public $checking_type, $conversion_rate, $show_customer, $snapshot_date, $allow_negative;

	public function rules()
    {
        return [
            [['source', 'signature', 'time'], 'string', 'max' => 255],
            [['request_id', 'customers_id', 'list_type', 'start_date', 'end_date', 'snapshot_date', 'sort'], 'string', 'max' => 255],
            [['orders_id', 'reference_id', 'customers_role', 'transaction_type', 'requesting_id', 'requesting_role', 'currency', 'activity'], 'string', 'max' => 255],
            [['checking_type'], 'string', 'max' => 255],
            [['message'], 'string'],
            [['page', 'limit', 'show_customer', 'allow_negative'], 'integer'],
            [['amount', 'total_amount'], 'double'],
            [['conversion_rate'], 'double'],
            [['source', 'signature', 'time'], 'required'],
            [['source', 'signature', 'time'], 'required', 'on' => 'get_token'],
            [['customers_id', 'checking_type'], 'required', 'on' => 'sc_info_balance'],
            [['customers_id', 'checking_type', 'conversion_rate', 'currency', 'requesting_id', 'requesting_role'], 'required', 'on' => 'sc_info_conversion'],
            [['list_type'], 'required', 'on' => 'sc_list'],
            [['list_type','snapshot_date'], 'required', 'on' => 'sc_openclose'],
            [['customers_id', 'transaction_type', 'amount', 'total_amount', 'currency', 'activity', 'requesting_id', 'requesting_role'], 'required', 'on' => 'sc_transaction'],
            [['transaction_type', 'currency', 'conversion_rate', 'activity'], 'required', 'on' => 'sc_convert'],
            [['customers_id', 'transaction_type'], 'required', 'on' => 'sc_status'],
            ['page', 'default', 'value' => 1],
            ['limit', 'default', 'value' => 20],
            ['transaction_type', 'in', 'range' => ['ADD_CREDIT', 'DEDUCT_CREDIT', 'CONVERT_CREDIT', 'SC_ACTIVATE', 'SC_SUSPEND']],
            ['checking_type', 'in', 'range' => ['BALANCE', 'CONVERSION']],
            ['list_type', 'in', 'range' => ['TRANSACTION', 'REQUEST', 'OPENBALANCE', 'CLOSEBALANCE']],
        ];
    }
}