<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "store_credit_request".
 *
 * @property int $id
 * @property string $request_id
 * @property string $reference_key
 * @property string $reference_id
 * @property string $activity_type
 * @property string $transaction_type
 * @property string $currency_from
 * @property string $currency_from_amount
 * @property string $currency_to
 * @property string $currency_to_amount
 * @property string $currency_rate
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class StoreCreditRequest extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'store_credit_request';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['activity_type'], 'required'],
            [['currency_from_amount', 'currency_to_amount', 'currency_rate'], 'number'],
            [['status', 'created_at', 'updated_at'], 'integer'],
            [['request_id', 'reference_key', 'transaction_type', 'reference_id', 'currency_from', 'currency_to'], 'string', 'max' => 64],
            [['activity_type'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'request_id' => 'Request ID',
            'reference_key' => 'Reference Key',
            'reference_id' => 'Reference ID',
            'activity_type' => 'Activity Type',
            'transaction_type' => 'Transaction Type',
            'currency_from' => 'Currency From',
            'currency_from_amount' => 'Currency From Amount',
            'currency_to' => 'Currency To',
            'currency_to_amount' => 'Currency To Amount',
            'currency_rate' => 'Currency Rate',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
