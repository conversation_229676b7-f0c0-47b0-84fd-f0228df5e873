* {
    padding: 0;
    margin: 0;
} 

body {
background: #555;
font-family: Verdana, Arial, Helvetica, sans-serif;
font-size: 11px;
line-height: 18px;
color: #333333;;
}

img { border: none; }
a { color: #3E92EA;  }
a:hover { text-decoration: none; color : #000; }

#wrap {
margin: 40px auto 0 auto;
width: 800px;
}

#header { 
padding-bottom:10px;
}
#header h1 {
font-size: 26px;
font-weight: bold;
letter-spacing: -3px;
padding: 12px 0 5px 10px;
}
#header h1 a {
color: #fff;
text-decoration: none;
}
#header h1 a:hover {
color: #fff;
text-decoration: none;
}
#header h2 {
color: #ccc;
font-size: 15px;
font-weight: 100;
padding: 0 0 0 11px;
letter-spacing: -1px;
line-height: 24px;
}

#menu {
height: 30px;
line-height: 30px;
background: #222 url(../img/menu.jpg) no-repeat;
}
#menu ul {
padding-left: 10px;
list-style-type: none;
}
#menu ul li {
display: block;
float: left;
}
#menu ul li a {
color: #eee;
padding: 0 10px;
text-decoration: none;
font-weight: 600;
}
#menu ul li a:hover {
color: #fff;
text-decoration: underline;
}

#content {
background: #555 url(../img/content.jpg) repeat-y;
padding: 10px 20px;
}

.left {
width: 568px;
float: left;
text-align: justify;
}
.left h1 {
    font-size: 20px;
}
.left h2 {
color: #FF4800;
font-size: 24px;
letter-spacing: -3px;
font-weight: 100;
padding : 10px 0 15px 0;
}

.right {
width: 150px;
float: right;
padding: 10px;
border-left: 1px solid #bbb;
font-size: 12px;
}
.right ul {
list-style-type: none;
padding: 5px 10px 10px 10px;
}
.right h2 {
height: 30px;
font-size: 12px;
color: #666;
line-height: 30px;
}
.right a { text-decoration: none }

#top {
background: #555 url(../img/top.jpg) no-repeat;
padding: 5px 0;
}
#bottom {
background: #555 url(../img/bottom.jpg) no-repeat;
padding: 10px 0;
}

#footer {
text-align: center;
color: #eee;
font-size: 11px;
padding: 0 0 10px 0;
}

#footer a{
    color:yellow;
}

span.tag{
    font-size: 9pt;
    padding: 0 6px 0 0;
}

hr.divider{
    margin-top:5px;
    margin-bottom:20px;
    border:1px solid #E3EFFF;
}

.datePosted{
    background-color:#E3EFFF;
    display:block;
    width:100%
}

.articles p{
    margin-top:10px;
    margin-bottom:15px;
}

.tagContainer{
    margin-top:10px;
    margin-bottom:8px;
}

.commentInput{
    float:left;
    width:120px;
}

.field{
    display: block;
    margin-top:10px;
    margin-bottom:8px;
}

.commentItem{
    margin-left:10px;
    margin-top:5px;
    padding-left:10px;
    padding-top:5px;
    padding-right:10px;
    padding-bottom:5px;
    border:1px solid #E3E0DF;
    background-color:#EFEFF0;
}