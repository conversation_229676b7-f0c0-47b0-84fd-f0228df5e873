<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DooPHP Blog Admin</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="Shortcut Icon" href="http://doophp.com/favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/style.css" media="screen" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/demo.css" media="screen" />


</head>
<body>

<div id="wrap">

<!-- include 'top' -->

<div id="content">
    <div class="left">

        <h1>Manage Blog Posts</h1><br/>
        <p>You can sort by the fields' value. Click the title to edit.</p><br/>
        <div class="articles">
            <table>
              <tbody><tr>
                <th width="150"><a href="{{rootUrl}}admin/post/sort/status/{{order}}">Status</a></th>
                <th width="500"><a href="{{rootUrl}}admin/post/sort/title/{{order}}">Title</a></th>
                <th width="360"><a href="{{rootUrl}}admin/post/sort/createtime/{{order}}">Create Time</a></th>
              </tr>
              <!-- loop posts -->
              <tr class="trecord">
                <td><!-- if {{posts' value.@status}}==1 -->Published<!-- else -->Draft<!-- endif --></td>
                <td><a href="{{rootUrl}}admin/post/edit/{{posts' value.@id}}">{{posts' value.@title}}</a></td>
                <td>{{formatDate(posts' value.@createtime)}}</td>
              </tr>
              <!-- endloop -->
            </tbody></table>
        </div>

        <hr class="divider"/>
        {{pager}}
    </div>

    <div class="right">
        <!-- include 'admin_sidebar' -->
    </div>

    <div style="clear: both;"> </div>
</div>

<div id="bottom"> </div>

    <div id="footer">
        Powered by <a href="http://www.doophp.com/">DooPHP framework</a>, for educational purpose.
    </div>
</div>

</body>
</html>