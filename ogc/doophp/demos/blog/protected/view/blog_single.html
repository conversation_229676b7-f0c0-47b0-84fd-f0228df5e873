<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DooPHP Blog Demo - {{post.@title}}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="Shortcut Icon" href="http://doophp.com/favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/style.css" media="screen" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/demo.css" media="screen" />


</head>
<body>

<div id="wrap">

<!-- include 'top' -->

<div id="content">
    <div class="left">

        <h2><a href="{{rootUrl}}article/{{post.@id}}">{{post.@title}}</a></h2>
        <div class="articles">
            {{post.@content}}
            <div class="tagContainer">
                <strong>Tags: </strong>
                <!-- loop post.@Tag -->
                <span class="tag"><a href="{{rootUrl}}tag/{{l' v.@name}}">{{l' v.@name}}</a></span>
                <!-- endloop -->
            </div>
            <em class="datePosted">Posted on {{formatDate(post.@createtime)}}</em>
        </div>

        <hr class="divider"/>
        <div id="comments" name="comments">
          <!-- if {{isset(comments)}} -->
            <strong>Total Comments ({{post.@totalcomment}})</strong><br/><br/>
              <!-- loop comments -->

                  <span id="comment{{comments' value.@id}}" name="comment{{comments' value.@id}}" style="font-weight:bold;">
                  <!-- if !{{empty(comments' value.@url)}} -->
                      <a href="{{comments' value.@url}}">{{comments' value.@author}}</a>
                  <!-- else -->
                      {{comments' value.@author}}
                  <!-- endif -->
                  </span> said on <em>{{formatDate(comments' value.@createtime, 'd M, y h:i:s A')}}</em>,<br/>

                  <div class="commentItem">{{comments' value.@content}}</div><br/>

              <!-- endloop -->
            <hr class="divider"/>
          <!-- endif -->
        </div>

        <p><strong>Leave a comment :)</strong></p>
        <form method="POST" action="{{rootUrl}}comment/submit">
            <input type="hidden" name="post_id" value="{{post.@id}}"/>
            <span class="field"><span class="commentInput">Name*:</span><input type="text" name="author" size="32"/></span>
            <span class="field"><span class="commentInput">Email*:</span><input type="text" name="email" size="32"/></span>
            <span class="field"><span class="commentInput">Website:</span><input type="text" name="url" value="http://" size="32"/></span>
            <span class="field"><span class="commentInput">Content*:</span><textarea cols="45" rows="6" name="content"></textarea></span>
            <span class="field"><span class="commentInput">&nbsp;</span><input type="submit" value="Send Comment"/></span>
        </form>
    </div>

    <div class="right">
        <!-- include 'blog_sidebar' -->
    </div>

    <div style="clear: both;"> </div>
</div>

<div id="bottom"> </div>

    <div id="footer">
        Powered by <a href="http://www.doophp.com/">DooPHP framework</a>, for educational purpose.
    </div>
</div>

</body>
</html>