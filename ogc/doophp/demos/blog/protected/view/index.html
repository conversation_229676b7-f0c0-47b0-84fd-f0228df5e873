<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DooPHP Blog Demo</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="Shortcut Icon" href="http://doophp.com/favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/style.css" media="screen" />
<link rel="stylesheet" type="text/css" href="{{rootUrl}}global/css/demo.css" media="screen" />


</head>
<body>

<div id="wrap">

<!-- include 'top' -->

<div id="content">
    <div class="left">

     <!-- loop posts -->
        <h2><a href="{{rootUrl}}article/{{posts' value.@id}}">{{posts' value.@title}}</a></h2>
        <div class="articles">
            {{shorten(posts' value.@content)}}
            <div class="tagContainer">
                <strong>Tags: </strong>
                <!-- loop posts' value.@Tag -->
                <span class="tag"><a href="{{rootUrl}}tag/{{posts' v' v.@name}}">{{posts' v' v.@name}}</a></span>
                <!-- endloop -->
            </div>
            <em class="datePosted">&nbsp;<a href="{{rootUrl}}article/{{posts' value.@id}}#comments" style="text-decoration:none;">Comments ({{posts' value.@totalcomment}})</a> | Posted on {{formatDate(posts' value.@createtime)}}</em>
        </div>
        <hr class="divider"/>
    <!-- endloop -->
    <div>{{pager}}</div>

    </div>

    <div class="right">
        <!-- include 'blog_sidebar' -->
    </div>

    <div style="clear: both;"> </div>
</div>

<div id="bottom"> </div>

    <div id="footer">
        Powered by <a href="http://www.doophp.com/">DooPHP framework</a>, for educational purpose.
    </div>
</div>

</body>
</html>