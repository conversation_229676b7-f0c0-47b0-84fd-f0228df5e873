
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
        <!-- include 'header' -->
    </head>
	<body>
      <!-- include 'nav' -->
	  <div class="content">
	  	<h1>Welcome to Database ORM Demo</h1>
		<p class="normal">Here you can learn about how to do database Operation with DooPHP.</p>
		<p class="normal">Please refer to the API for DooSqlMagic, DooDbExpression and DooModelGen class. 
        Models are generated by accessing <em>http://yourappurl.com/gen_model</em> using DooModelGen. The Model class doesn't need to extend any parent class.
        You can download the backup SQL for the MySQL database <a class="file" href="{{baseurl}}foodexample.sql">here</a>.        </p>

        <p class="normal">For debugging purpose, it is recommended that you use the Database profiling tools with your data centric app. 
        A profiler viewer tool is under development currently which gives you a better view of your log files.</p>
        <br/>The database consist of 6 tables as below:<br/><br/>
        <img src="{{baseurl}}global/img/food.png" alt="Food database Example" />
		<p class="boldy">The Tables Relationship</p>
        <p>Tables relationship are defined in a single file, <em>db.conf.php</em></p>
        <p style="font-size:70%">$dbmap[Table A]['has_one'][Table B] = array('foreign_key'=> Table B's column that links to Table A )<br/><br/>
 $dbmap[Table B]['belongs_to'][Table A] = array('foreign_key'=> Table A's column where Table B links to )</p>
        <pre>
<span style="color:yellow">//Food relationship</span>
$dbmap['Food']['belongs_to']['FoodType'] = array('foreign_key'=>'id');
$dbmap['Food']['has_many']['Article'] = array('foreign_key'=>'food_id');
$dbmap['Food']['has_one']['Recipe'] = array('foreign_key'=>'food_id');
$dbmap['Food']['has_many']['Ingredient'] = array('foreign_key'=>'food_id', 'through'=>'food_has_ingredient');

<span style="color:yellow">//Food Type</span>
$dbmap['FoodType']['has_many']['Food'] = array('foreign_key'=>'food_type_id');

<span style="color:yellow">//Article</span>
$dbmap['Article']['belongs_to']['Food'] = array('foreign_key'=>'id');

<span style="color:yellow">//Recipe</span>
$dbmap['Recipe']['belongs_to']['Food'] = array('foreign_key'=>'id');

<span style="color:yellow">//Ingredient</span>
$dbmap['Ingredient']['has_many']['Food'] = array('foreign_key'=>'ingredient_id', 'through'=>'food_has_ingredient');

        </pre>

        <p class="boldy"><a name="database_orm" id="database_orm"></a>Test drive ORM Find/Relate operations</p>
        <pre>
//list all food
<a href="{{baseurl}}index.php/food/all">/food/all</a>

//Each food belongs to a type
<a href="{{baseurl}}index.php/food_with_type">/food_with_type</a>

//Same as above but only type with at least a matched food is return
<a href="{{baseurl}}index.php/food_with_type/matched">/food_with_type/matched</a>

//The reversed of the link above, 1 Type has many food
<a href="{{baseurl}}index.php/type_with_food">/type_with_food</a>


//Many to many relationship, food & ingredient is link with table food_has_ingredient
<a href="{{baseurl}}index.php/food_with_ingredients">/food_with_ingredients</a>

//The reversed of the link above
<a href="{{baseurl}}index.php/ingredients_with_food">/ingredients_with_food</a>

//An advanced query with 3 related Models linked, a Food is return with its Type & Articles
<a href="{{baseurl}}index.php/nasilemak_type_&_article">/nasilemak_type_&_article</a>

//Search for a food and return the food with its Type
<a href="{{baseurl}}index.php/food_&_type_by_name/Nasi Lemak">/food_&_type_by_name/Nasi Lemak</a>

//Search for a food and return the food with its Type
<a href="{{baseurl}}index.php/recipe/Nasi Lemak">/recipe/Nasi Lemak</a>

//Search for a food by its Id
<a href="{{baseurl}}index.php/food_by_id/7">/food_by_id/7</a>

//get articles by food name
<a href="{{baseurl}}index.php/article_by_food/Nasi Lemak">/article_by_food/Nasi Lemak</a>

//get articles by food name and sort it by creation date Descendingly
<a href="{{baseurl}}index.php/article_by_food_desc/Nasi Lemak">/article_by_food_desc/Nasi Lemak</a>

//get articles (not drafts) by food name and sort it by creation date Descendingly
<a href="{{baseurl}}index.php/article_by_food_desc_published/Nasi Lemak">/article_by_food_desc_published/Nasi Lemak</a>


         </pre>

        <p class="boldy"><a name="database_orm" id="database_orm"></a>Test drive ORM Insert/Relate Insert operations</p>
        <pre>
//add a new Food, replace the params with your values
<a href="{{baseurl}}index.php/food/insert/:foodtype/:foodname/:location/:desc">/food/insert/:foodtype/:foodname/:location/:desc</a>

//add a new Food, replace the params with your values
<a href="{{baseurl}}index.php/food/insert/:foodtype/:foodname/:location/:desc">/food/insert/:foodtype/:foodname/:location/:desc</a>

//add a new Food along with an associated Ingredient, many to many insert (no repeats of value)
<a href="{{baseurl}}index.php/food/insert/:foodtype/:foodname/:location/:desc/:ingredient">/food/insert/:foodtype/:foodname/:location/:desc/:ingredient</a>
</pre>

<p>There's more! However I leave it to you to discover.</p>
       <span class="totop"><a href="#top">BACK TO TOP</a></span>
	  </div>
	</body>
</html>