<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<title>DooPHP demos</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="description" content="DooPHP framework URI routing demo">
		<meta name="keywords" content="DooPHP, php framework, MVC, Web 2.0, design, xhtml, css, template">
		<!--<link rel="stylesheet" type="text/css" href="global/css/screen.css" media="screen" />-->
	<style type="text/css">
<!--
.boldy {
	color: #e7c118;
	font-weight: bold;
}
.file{
	color:#729fbe;
}
body{
	margin:0px 0px 0px;
	color:#fff;
	background-color:#2e3436;
	font-size:190%;
	font-family: 'Courier New', Courier, monospace;
}

.nav{
float:left;clear:right;width:230px;display:block;
}
.nav li{
	list-style-type:none;
	text-align:right;
	font-size:60%;
	display:block;
	height:30px;
	line-height:30px;
}

.nav li a{
	display:block;
	padding-right:10px;
	color:#FFFFFF;
	text-decoration:none;
	background-color:#333333;
}
.nav li a:hover{
	color:#e7c118;
	text-decoration:none;
	background-color:#242424;
}

.header{
background-color:#222222;height:60px;line-height:60px;padding-left:10px;
}

.top, .top a{
background-color:#151515;height:20px;padding-left:10px;text-align:right;color:#00FF99;font-size:70%;text-decoration:none;padding-right:5px;
}

.content{
float:right;text-align:left;width:75%;display:block;
}

h1{
font-size:120%;
}

pre{
	font-size:50%;
	width:620px;
	background-color:#121212;
	padding:10px 15px 10px;
	border:1px solid #555;
}
-->
</style></head>
	<body>
		<div class="top"><a href="http://doophp.com/">demo@doophp</a></div>
		<div class="header"><strong>Doophp </strong><span class="boldy">URI routing </span> demo. Class used - <strong><span class="file">DooURIRouter</span></strong><br/>
      </div>
	  ...<br/>
	  <div class="nav">
	  	<ul>
			<li><a href="">About@</a></li>
			<li><a href="">URL Links@</a></li>
			<li><a href="">Example Usage@</a></li>
		</ul>
	  </div>
	  <div class="content">
	  	<h1>Uri Routing Example</h1>
		<p><span class="boldy">Result: </span>This is a hello world test</p>
		<p><pre>
Array(0) {
	[Username] => 'Leng Sheng Hong',
	[Food] => 102233
	[Location] => 'ioek902'
}		
		</pre>
<pre>
Array(0) {
	[Username] => 'Leng Sheng Hong',
	[Food] => 102233
	[Location] => 'ioek902'
}		
		</pre>
		</p>
	  </div>
	</body>
</html>
