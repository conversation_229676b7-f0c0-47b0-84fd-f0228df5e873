
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
        <!-- include 'header' -->
    </head>
	<body>
      <!-- include 'nav' -->
	  <div class="content">
	  	<h1>Welcome to Template Engine Demo</h1>
		<p class="normal">Here you can learn about how to do the view part in MVC with DooPHP using its template engine.</p>
		<p class="normal">IF statement is SUPPORTED now. You can use partial caching mechanism with the tempalte engine too!</p>
        <p class="normal">View the <a class="file" href="{{baseurl}}index.php/template.html">template source</a> (html).</p>
		<p class="boldy"><a name="template" id="template"></a>Test drive Template Engine</p>

<hr/>
    <!-- include "{{file}}" -->

    <!-- include 'nested/hi' -->
<hr/>
    <p>View test, variable, tags, loop, loop assoc array, double loop:</p>

    Username: {{username}} <br/>

    Username upper case: {{upper(username)}}
	
	<!-- if {{upper(username)}}=='ADMIN' -->
		<h3>Hi admin! Please administer.</h3>
	<!-- else -->
		<h3>Hi {{username}}! welcome back.</h3>
	<!-- endif -->
	
    <hr/>

    <p>Using a function in <em>template_tags.php</em> to print_r. You control what you want to be available in the template.
    {{DeBuG(messages)}}</p>

    <hr/>
    <h2>Messages list:</h2>
    <p>Just a simple loop</p>
    <ol>
    <!-- loop messages -->
        <li>{{messages' value}}</li>
    <!-- endloop -->
    </ol>

    <hr/>
    <h2>User name list:</h2>
    <p>Functions can be used in loop</p>
    <ol>
    <!-- loop user -->
        <li>{{upper(user' key)}} : {{upper(user' value)}}</li>
    <!-- endloop -->
    </ol>

    <hr/>
    <h2>Full name for user <b>john</b>:</h2>
    {{user.john}}

    <hr/>
    <h2>Full name for user <b>john</b> UPPER case:</h2>
    {{upper(user.john)}}

    <hr/>
    <h2>Total user:</h2>
    <p>Associative array usage</p>
    Male = {{tofloat(member.total.male)}} <br/>
    Female = {{member.total.female}}<br/>
    Female = {{sample_with_args(member.total.female, 'we female')}}<br/>
    Female = {{TRIPLE(member.total.female, ' x3 female + ', 1000)}}<br/>
    <br/>
    Kids Male = {{member.totalKids.male}} <br/>
    Kids Female = {{member.totalKids.female}} <br/>
    <br/>
    Teen Male = {{member.totalTeen.male}} <br/>
    Teen Female = {{member.totalTeen.female}} <br/>

    <hr/>
    <h2>User's messages list:</h2>
    <p>Nested loop example</p> 
    <ul>
	
	<!-- cache('messages', 3600) -->
    <!-- loop usermsg -->
        <strong>{{usermsg' key}} :</strong> <br/>
        <!-- loop usermsg' value -->
            <li>({{usermsg' value' key}})  {{usermsg' value' value}}</li>
            <!-- or you can use <li>({usermsg' v' k})  {usermsg' v' v}</li> -->
            <!-- or <li>({loop' v' k})  {{loop' v' v}</li> -->
            <!-- or even <li>({l' v' k})  {{l' v' v}</li> -->
        <!-- endloop -->
        <br/>
    <!-- endloop -->
    <!-- endcache -->
    </ul>

    <hr/>
    <h2>Messages with detail:</h2>
    <p>Nested loop with Assoc array example</p>
    <ol>
    <!-- loop msgdetails -->
        <li>{{upper(msgdetails' value.subject)}} <b>ATTACH: </b> {{msgdetails' value.attachment.pdf}}</li>
    <!-- endloop -->
    </ol> 

    <hr/>
    <h2>The Winner is:</h2>
    <p>Using objects</p>
    Winner name: {{winner.@fullname}}  <br/>
    Gender: {{winner.@gender}}  <br/>
    <br/><strong>Winner's Physical Profile:</strong><br/>
    Weight: {{winner.@Physical.@weight}}  <br/>
    Height: {{winner.@Physical.@height}}  <br/>

    <hr/>
    <h2>Winners list:</h2>
    <p>Using objects in loop</p>
    <ul>
    <!-- loop winners -->
        <li>{{upper(winners' value.@fullname)}}
        <br/>Gender: {{loop' value.@gender}}
        <br/>Weight: {{loop' v.@Physical.@weight}}
        <br/>Height: {{l' v.@Physical.@height}}
        </li>
        <br/><br/>
    <!-- endloop -->
    </ul>
    <p>winners' value,  loop' value, loop' v, l' v, somename' v are the same</p>
    <hr/>
    
    <h2>Loop Blog post with tags</h2>
    <!-- loop posts -->        
        <p class="normal" style="background-color:#222;padding:20px;">
        <strong>{{posts' value.@title}}</strong><br/>
        {{posts' value.@content}}<br/>
        <!-- loop posts' value.@Tag -->
            <a href="#" class="file">{{posts' value' value.@name}}</a>, 
        <!-- endloop -->
        </p>
    <!-- endloop -->

    
       <span class="totop"><a href="#top">BACK TO TOP</a></span>
	  </div>
	</body>
</html>