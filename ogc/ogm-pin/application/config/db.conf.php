<?php
$dbmap['GenRequestRemark']['belongs_to']['User'] = array('foreign_key' => 'id');
$dbmap['User']['has_many']['GenRequestRemark'] = array('foreign_key' => 'create_by');

$dbmap['PinRemark']['belongs_to']['User'] = array('foreign_key' => 'id');
$dbmap['User']['has_many']['PinRemark'] = array('foreign_key' => 'create_by');

$dbmap['Pin']['belongs_to']['GenRequest'] = array('foreign_key' => 'id');
$dbmap['GenRequest']['has_many']['Pin'] = array('foreign_key' => 'batch_id');

$dbmap['ResellerPinSetting']['belongs_to']['Product'] = array('foreign_key' => 'id');
$dbmap['Product']['has_many']['ResellerPinSetting'] = array('foreign_key' => 'product_id');

$dbmap['PinRequestDelivered']['belongs_to']['PinRequest'] = array('foreign_key' => 'id');
$dbmap['PinRequest']['has_many']['PinRequestDelivered'] = array('foreign_key' => 'pin_request_id');

//$dbconfig[ Environment or connection name] = array(Host, Database, User, Password, DB Driver, Make Persistent Connection?);
/**
 * Database settings are case sensitive.
 * To set collation and charset of the db connection, use the key 'collate' and 'charset'
 * array('localhost', 'database', 'root', '1234', 'mysql', true, 'collate'=>'utf8_unicode_ci', 'charset'=>'utf8'); 
 */
?>