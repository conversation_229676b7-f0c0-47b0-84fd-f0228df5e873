<?php
/**
 * Define your URI routes here.
 *
 * $route[Request Method][Uri] = array( Controller class, action method, other options, etc. )
 *
 * RESTful api support, *=any request method, GET PUT POST DELETE
 * POST 	Create
 * GET      Read
 * PUT      Update, Create
 * DELETE 	Delete
 *
 * Use lowercase for Request Method
 *
 * If you have your controller file name different from its class name, eg. home.php HomeController
 * $route['*']['/'] = array('home', 'index', 'className'=>'HomeController');
 * 
 * If you need to reverse generate URL based on route ID with DooUrlBuilder in template view, please defined the id along with the routes
 * $route['*']['/'] = array('HomeController', 'index', 'id'=>'home');
 *
 * If you need dynamic routes on root domain, such as http://facebook.com/username
 * Use the key 'root':  $route['*']['root']['/:username'] = array('UserController', 'showProfile');
 *
 * If you need to catch unlimited parameters at the end of the url, eg. http://localhost/paramA/paramB/param1/param2/param.../.../..
 * Use the key 'catchall': $route['*']['catchall']['/:first'] = array('TestController', 'showAllParams');
 * 
 * If you have placed your controllers in a sub folder, eg. /protected/admin/EditStuffController.php
 * $route['*']['/'] = array('admin/EditStuffController', 'action');
 * 
 * If you want a module to be publicly accessed (without using Doo::app()->getModule() ) , use [module name] ,   eg. /protected/module/forum/PostController.php
 * $route['*']['/'] = array('[forum]PostController', 'action');
 * 
 * If you create subfolders in a module,  eg. /protected/module/forum/post/ListController.php, the module here is forum, subfolder is post
 * $route['*']['/'] = array('[forum]post/PostController', 'action');
 *
 * Aliasing give you an option to access the action method/controller through a different URL. This is useful when you need a different url than the controller class name.
 * For instance, you have a ClientController::new() . By default, you can access via http://localhost/client/new
 * 
 * $route['autoroute_alias']['/customer'] = 'ClientController';
 * $route['autoroute_alias']['/company/client'] = 'ClientController';
 * 
 * With the definition above, it allows user to access the same controller::method with the following URLs:
 * http://localhost/company/client/new
 *
 * To define alias for a Controller inside a module, you may use an array:
 * $route['autoroute_alias']['/customer'] = array('controller'=>'ClientController', 'module'=>'example');
 * $route['autoroute_alias']['/company/client'] = array('controller'=>'ClientController', 'module'=>'example');
 *
 * Auto routes can be accessed via URL pattern: http://domain.com/controller/method
 * If you have a camel case method listAllUser(), it can be accessed via http://domain.com/controller/listAllUser or http://domain.com/controller/list-all-user
 * In any case you want to control auto route to be accessed ONLY via dashed URL (list-all-user)
 *
 * $route['autoroute_force_dash'] = true;	//setting this to false or not defining it will keep auto routes accessible with the 2 URLs.
 *
 */
 
$route['*']['/'] = array('AppController', 'index');
$route['*']['/error'] = array('ErrorController', 'index');

$route['*']['/login'] = array('AppController', 'login');
$route['post']['/login'] = array('LoginController', 'login');
$route['*']['/logout'] = array('LoginController', 'logout');
$route['get']['/islogin'] = array('LoginController', 'isLogin');

$route['*']['/forgot_password'] = array('LoginController', 'forgotPassword');
$route['*']['/forgot_password/sendlink'] = array('LoginController', 'sendPasswordResetLink');
$route['get']['/forgot_password/:token'] = array('LoginController', 'fromEmailLinkChangePwd');
$route['post']['/forgot_password/:token'] = array('LoginController', 'changePwdFromToken');

// user
$route['get']['/api/user/list_user'] = array('UserController', 'getListUser');
$route['get']['/api/user/list_user/page/:page'] = array('UserController', 'getListUser');
$route['get']['/api/user/list_all_user'] = array('UserController', 'listAllUser');

// user group
$route['get']['/api/user-group/list_user_group'] = array('UserGroupController', 'getListUserGroup');
$route['get']['/api/user-group/list_user_group/page/:page'] = array('UserGroupController', 'getListUserGroup');
$route['get']['/api/user-group/list_all_user_group'] = array('UserGroupController', 'listAllUserGroup');

$route['post']['/api/user-group/update'] = array('UserGroupController', 'update');
$route['post']['/api/user-group/create'] = array('UserGroupController', 'create');
$route['post']['/api/user-group/permission'] = array('UserGroupController', 'setPermission');

// country
$route['get']['/api/country/list_country'] = array('CountryController', 'getListCountry');
$route['get']['/api/country/list_country/page/:page'] = array('CountryController', 'getListCountry');
$route['get']['/api/country/list_all_country'] = array('CountryController', 'listAllCountry');

$route['post']['/api/country/update'] = array('CountryController', 'update');
$route['post']['/api/country/create'] = array('CountryController', 'create');

// product
$route['get']['/api/product/list_product'] = array('ProductController', 'getListProduct');
$route['get']['/api/product/list_product/page/:page'] = array('ProductController', 'getListProduct');
$route['get']['/api/product/list_all_product'] = array('ProductController', 'listAllProduct');

$route['post']['/api/product/update'] = array('ProductController', 'update');
$route['post']['/api/product/create'] = array('ProductController', 'create');

// gen request
$route['get']['/api/request/list_request'] = array('RequestController', 'getListRequest');
$route['get']['/api/request/list_request/page/:page'] = array('RequestController', 'getListRequest');
$route['get']['/api/request/list_request/page/:page/status/:status'] = array('RequestController', 'getListRequest');
$route['get']['/api/request/list_all_request'] = array('RequestController', 'listAllRequest');
$route['get']['/api/request/list_remarks/:id'] = array('RequestController', 'getListRemarks');
$route['get']['/api/request/list_remarks/:id/page/:page'] = array('RequestController', 'getListRemarks');

$route['post']['/api/request/update'] = array('RequestController', 'update');
$route['post']['/api/request/update_self'] = array('RequestController', 'updateSelf');
$route['post']['/api/request/create'] = array('RequestController', 'create');
$route['post']['/api/request/approve/:id'] = array('RequestController', 'approveRequest');
$route['post']['/api/request/reject/:id'] = array('RequestController', 'rejectRequest');
$route['post']['/api/request/delete/:id'] = array('RequestController', 'delete');
$route['post']['/api/request/delete_self/:id'] = array('RequestController', 'deleteSelf');

// reseller
$route['get']['/api/reseller/list_reseller'] = array('ResellerController', 'getListReseller');
$route['get']['/api/reseller/list_reseller/page/:page'] = array('ResellerController', 'getListReseller');
$route['get']['/api/reseller/list_all_reseller'] = array('ResellerController', 'listAllReseller');

$route['post']['/api/reseller/update'] = array('ResellerController', 'update');
$route['post']['/api/reseller/create'] = array('ResellerController', 'create');

$route['post']['/api/reseller/create_notification'] = array('ResellerController', 'createNotification');
$route['post']['/api/reseller/delete_notification/:id'] = array('ResellerController', 'deleteNotification');
$route['get']['/api/reseller/list_notification/:id'] = array('ResellerController', 'getListNotification');
$route['get']['/api/reseller/list_notification/:id/page/:page'] = array('ResellerController', 'getListNotification');
$route['get']['/api/reseller/get_notification_by_id/:id'] = array('ResellerController', 'getNotificationById');
$route['get']['/api/reseller/get_notification_by_product_id/:reseller_id/product_id/:product_id'] = array('ResellerController', 'getNotificationByProductId');
$route['get']['/api/reseller/list_all_user'] = array('ResellerController', 'getAllUser');

// client
$route['get']['/api/client/list_client'] = array('ClientController', 'getListClient');
$route['get']['/api/client/list_client/page/:page'] = array('ClientController', 'getListClient');
$route['get']['/api/client/list_all_client'] = array('ClientController', 'listAllClient');

$route['post']['/api/client/update'] = array('ClientController', 'update');
$route['post']['/api/client/create'] = array('ClientController', 'create');

// pin
$route['get']['/api/pin/list_pin'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/product_id/:product_id'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/product_id/:product_id/status/:status'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/product_id/:product_id/redeem/:redeem'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/product_id/:product_id/status/:status/redeem/:redeem'] = array('PinController', 'getListPin');

$route['get']['/api/pin/list_pin/page/:page/batch_id/:batch_id'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/batch_id/:batch_id/status/:status'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/batch_id/:batch_id/redeem/:redeem'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/batch_id/:batch_id/status/:status/redeem/:redeem'] = array('PinController', 'getListPin');

$route['get']['/api/pin/list_pin/page/:page/status/:status'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/redeem/:redeem'] = array('PinController', 'getListPin');
$route['get']['/api/pin/list_pin/page/:page/status/:status/redeem/:redeem'] = array('PinController', 'getListPin');

$route['get']['/api/pin/list_pin/page/:page/serial/:serial'] = array('PinController', 'getListPin');

$route['get']['/api/pin/list_all_pin'] = array('PinController', 'listAllPin');
$route['get']['/api/pin/export'] = array('PinController', 'exportAll');
$route['get']['/api/pin/export/batch_id/:batch_id'] = array('PinController', 'exportByBatch');
$route['get']['/api/pin/export/product_id/:product_id'] = array('PinController', 'exportByProduct');
$route['get']['/api/pin/export/status/:status'] = array('PinController', 'exportAll');
$route['get']['/api/pin/export/redeem/:redeem'] = array('PinController', 'exportAll');
$route['get']['/api/pin/export/status/:status/redeem/:redeem'] = array('PinController', 'exportAll');

$route['get']['/api/pin/export/batch_id/:batch_id/status/:status'] = array('PinController', 'exportByBatch');
$route['get']['/api/pin/export/product_id/:product_id/status/:status'] = array('PinController', 'exportByProduct');
$route['get']['/api/pin/export/batch_id/:batch_id/redeem/:redeem'] = array('PinController', 'exportByBatch');
$route['get']['/api/pin/export/product_id/:product_id/redeem/:redeem'] = array('PinController', 'exportByProduct');
$route['get']['/api/pin/export/batch_id/:batch_id/status/:status/redeem/:redeem'] = array('PinController', 'exportByBatch');
$route['get']['/api/pin/export/product_id/:product_id/status/:status/redeem/:redeem'] = array('PinController', 'exportByProduct');
$route['get']['/api/pin/export/serial/:serial'] = array('PinController', 'exportBySerial');

$route['get']['/api/pin/export_self/batch_id/:batch_id'] = array('PinController', 'exportByBatchSelf');
$route['get']['/api/pin/export_self/batch_id/:batch_id/status/:status'] = array('PinController', 'exportByBatchSelf');
$route['get']['/api/pin/export_self/batch_id/:batch_id/redeem/:redeem'] = array('PinController', 'exportByBatchSelf');
$route['get']['/api/pin/export_self/batch_id/:batch_id/status/:status/redeem/:redeem'] = array('PinController', 'exportByBatchSelfRequest');

$route['post']['/api/pin/update'] = array('PinController', 'update');
$route['post']['/api/pin/activate'] = array('PinController', 'activate');
$route['post']['/api/pin/deactivate'] = array('PinController', 'deactivate');

$route['get']['/api/pin/list_remarks/:id'] = array('PinController', 'getListRemarks');
$route['get']['/api/pin/list_remarks/:id/page/:page'] = array('PinController', 'getListRemarks');

// cronjob
$route['*']['/api/cronjob_available_qty'] = array('MainController', 'cronjob_available_qty');

// Report
//$route['get']['/api/report/start_date/:start_date/end_date/:end_date'] = array('PinController', 'getReport');
//$route['get']['/api/report/start_date/:start_date/end_date/:end_date/batch_id/:batch_id'] = array('PinController', 'getReport');

// API for external redeem and stuffs
$route['post']['/api/authenticate'] = array('ApiController', 'authenticate');
$route['post']['/api/redeem'] = array('ApiController', 'redeem');
$route['post']['/api/get_pin_info'] = array('ApiController', 'getPinInfo');

$route['post']['/api/pin_request'] = array('ApiController', 'pinRequest');

//---------- Delete if not needed ------------
$admin = array('admin' => '1234');

//view the logs and profiles XML, filename = db.profile, log, trace.log, profile
$route['*']['/debug/:filename'] = array('MainController', 'debug', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

//show all urls in app
$route['*']['/allurl'] = array('MainController', 'allurl', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

//generate routes file. This replace the current routes.conf.php. Use with the sitemap tool.
$route['post']['/gen_sitemap'] = array('MainController', 'gen_sitemap', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

//generate routes & controllers. Use with the sitemap tool.
$route['post']['/gen_sitemap_controller'] = array('MainController', 'gen_sitemap_controller', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

//generate Controllers automatically
$route['*']['/gen_site'] = array('MainController', 'gen_site', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

//generate Models automatically
$route['*']['/gen_model'] = array('MainController', 'gen_model', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');

$route['*']['/setup'] = array('MainController', 'setup', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
$route['*']['/setup_root'] = array('MainController', 'setup_root', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
$route['*']['/upgrade_setup'] = array('MainController', 'upgrade_setup', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
$route['*']['/upgrade_db'] = array('MainController', 'upgrade_db', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
$route['*']['/upgrade_db2'] = array('MainController', 'upgrade_db2', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
$route['*']['/upgrade_db_exclusive'] = array('MainController', 'upgrade_db_exclusive', 'authName'=>'DooPHP Admin', 'auth'=>$admin, 'authFail'=>'Unauthorized!');
?>