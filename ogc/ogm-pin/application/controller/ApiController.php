<?php

class ApiController extends AppController {

    public $autorender = false;
    public $autoroute = false;
    public $scopeList = array('redeem', 'getPinInfo');
    public $scope = '';
    public $data;

    public function authenticate() {
        if (empty($_POST['data'])) {
            $this->toJSON(array('error' => 'JSON data no specified'), true);
            return 400;
        }

        $data = (array) json_decode(stripslashes($_POST['data']));

        if ($data === null) {
            $this->toJSON(array('error' => 'Invalid JSON format'), true);
            return 400;
        }

        if (empty($data['merchant_code'])) {
            $this->toJSON(array('error' => 'Merchant Code needs to be specified in JSON'), true);
            return 400;
        }

        if (empty($data['auth'])) {
            $this->toJSON(array('error' => 'Secret needs to be specified in JSON'), true);
            return 400;
        }

        if (empty($data['hash'])) {
            $this->toJSON(array('error' => 'Hash needs to be specified in JSON'), true);
            return 400;
        }

        if (empty($data['scope'])) {
            $this->toJSON(array('error' => 'Scope needs to be specified in JSON'), true);
            return 400;
        }

        if (!in_array($data['scope'], $this->scopeList)) {
            $this->toJSON(array('error' => 'Invalid Scope'), true);
            return 400;
        }

        $c = new Client();
        $c->api_key = $data['merchant_code'];

        if ($c->count() < 1) {
            $this->toJSON(array('error' => 'Authentication error'), true);
            return 401;
        }

        $c = $c->getOne(array('select' => 'secret_key'));

        if (md5($data['merchant_code'] . $c->secret_key . $data['hash']) != $data['auth']) {
            $this->toJSON(array('error' => 'Authentication error'), true);
            return 401;
        }

        $t = new Token();

        $t->merchant_code = $data['merchant_code'];
        $t->ip = $this->clientIP();
        $t->create_date = new DooDbExpression('NOW()');
        $t->scope = $data['scope'];
        $t->expires = 15 * 60;

        $token = md5($t->merchant_code . $t->ip . $c->secret_key . time() . mt_rand(1000, 9999));
        $t->oauth_token = $token;

        $t->insert();

        $t = new Token;
        $t->oauth_token = $token;
        $t = $t->getOne();

        if ($t->expires > 0) {
            $expireTS = strtotime($t->create_date) + $t->expires;
            $this->toJSON(array('token' => $token, 'expires' => $expireTS), true);
        } else {
            $this->toJSON(array('token' => $token), true);
        }

        Doo::logger()->log('API authentication success - ' . json_encode($t->getDataArray()), 0, 'api_auth');
    }

    public function beforeRun($resource, $action) {
        $this->scope = $action;
        return parent::beforeRun($resource, $action);
    }

    private function checkValid() {
        if (empty($_POST['data'])) {
            $this->toJSON(array('error' => 'JSON data no specified'), true);
            return 400;
        }

        $data = (array) json_decode(stripslashes($_POST['data']));

        if ($data === null) {
            $this->toJSON(array('error' => 'Invalid JSON format'), true);
            return 400;
        }

        if (empty($data['token'])) {
            $this->toJSON(array('error' => 'Token needs to be specified in JSON'), true);
            return 400;
        }

        $t = new Token();
        $t->oauth_token = $data['token'];
        $t->scope = $this->scope;

        $t = $t->getOne();

        if (empty($t)) {
            $this->toJSON(array('error' => 'Invalid token'), true);
            return 403;
        }

        if ($t->expires > 0) {
            $expireTS = strtotime($t->create_date) + $t->expires;

            if (time() > $expireTS) {
                $this->toJSON(array('error' => 'Token already expired'), true);
                return 403;
            }
        }

        $c = new Client();
        $c->api_key = $t->merchant_code;

        $c = $c->getOne(array('select' => 'id', 'asArray' => true));

        if (empty($c)) {
            $this->toJSON(array('error' => 'Client not exist'), true);
            return 400;
        }

        $data['client_id'] = $c['id'];

        $this->data = $data;
    }

    public function redeem() {
        if ($err = $this->checkValid()) {
            return $err;
        }

        if (empty($this->data['pin'])) {
            Doo::logger()->log('Pin needs to be specified in JSON', 0, 'api_redeem');

            $this->toJSON(array('error' => 'Pin needs to be specified in JSON'), true);
            return 400;
        }

        if (empty($this->data['serial'])) {
            Doo::logger()->log('Serial needs to be specified in JSON', 0, 'api_redeem');

            $this->toJSON(array('error' => 'Serial needs to be specified in JSON'), true);
            return 400;
        }
        //Do redeem
        $p = new Pin;
        $rs = $p->redeem($this->data['pin'], $this->data['serial'], $this->data['client_id'], isset($this->data['merchant_id']) ? $this->data['merchant_id'] : null);

        if($rs['code'] == 200) {
            $this->toJson($rs['msg'], true);
            return;
        }

        $this->toJSON($rs['msg'], true);

        return $rs['code'];
    }

    public function getPinInfo() {
        if ($err = $this->checkValid()) {
            return $err;
        }

        $p = new Pin;
        $rs = $p->getPinInfo($this->data['pin'], $this->data['serial'], $this->data['client_id'], $this->data['flag_redeem'], isset($this->data['merchant_id']) ? $this->data['merchant_id'] : null);

        if ($rs['code'] == 200) {
            $this->toJSON($rs['result'], true);

            return;
        }

        $this->toJSON($rs['msg'], true);

        return $rs['code'];
    }

    public function pinRequest() {
        ini_set('max_execution_time', 200);

        $error_array = array('tran_status' => 2, 'process_date' => date('Y-m-d H:i:s'));

        Doo::logger()->log('Pin Request API - ' . json_encode($_POST), 0, 'api_pin_request');

        if (empty($_POST['GP_REQUEST'])) {
            $this->toJSON(array('tran_errcode' => 'JSON GP_REQUEST no specified'), true);
            return 400;
        }

        $data = (array) json_decode(stripslashes($_POST['GP_REQUEST']));

        if ($data === null) {
            $error_array['tran_errcode'] = 'Invalid JSON format';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['mm_cmd'])) {
            $error_array['tran_errcode'] = 'mm_cmd needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        $error_array['mm_cmd'] = $data['mm_cmd'];

        if ($data['mm_cmd'] != 'GP_REQ') {
            $error_array['tran_errcode'] = 'Invalid mm_cmd';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['tran_id'])) {
            $error_array['tran_errcode'] = 'Invalid mm_cmd';

            $this->toJSON(array('tran_errcode' => 'tran_id needs to be specified in JSON'), true);
            return 400;
        }

        if (intval($data['tran_id']) < 1) {
            $error_array['tran_errcode'] = 'Invalid tran_id - ' . $data['tran_id'] . ' - ' . intval($data['tran_id']);

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'])) {
            $error_array['tran_errcode'] = 'req needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (!is_array($data['req']) || sizeof($data['req']) < 1) {
            $error_array['tran_errcode'] = 'req needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        // Only Read the 1st request
        if (empty($data['req'][0]->reseller_id)) {
            $error_array['tran_errcode'] = 'reseller_id needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->hash_data)) {
            $error_array['tran_errcode'] = 'hash_data needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->softpin_currency)) {
            $error_array['tran_errcode'] = 'softpin_currency needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->softpin_amt)) {
            $error_array['tran_errcode'] = 'softpin_amt needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->quantity)) {
            $error_array['tran_errcode'] = 'quantity needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        $request_qty = intval($data['req'][0]->quantity);

        if ($request_qty < 1) {
            $error_array['tran_errcode'] = 'quantity needs to be Integer';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->sub_date)) {
            $error_array['tran_errcode'] = 'sub_date needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        if (empty($data['req'][0]->description)) {
            $error_array['tran_errcode'] = 'description needs to be specified in JSON';

            $this->toJSON($error_array, true);
            return 400;
        }

        $r = new Reseller();

        $r->reseller_code = $data['req'][0]->reseller_id;

        if ($r->count(array('limit' => 1)) < 1) {
            $error_array['tran_errcode'] = 'Invalid Reseller';

            $this->toJSON($error_array, true);
            return 400;
        }

        $r = $r->getOne(array('select' => 'id, verification_url, secret_key'));

        $hashdata = base64_encode(sha1($r->secret_key . $data['tran_id'] . $data['req'][0]->softpin_amt, true));

        if ($hashdata != $data['req'][0]->hash_data) {
            $error_array['tran_errcode'] = 'Validation Fail';

            $this->toJSON($error_array, true);
            return 403;
        }

        $pr = new PinRequest();
        $pr->trans_id = $data['tran_id'];
        $pr->reseller_id = $r->id;

        if ($pr->count() < 1) {
            $data_array = array('command' => $data['mm_cmd'],
                'trans_id' => $data['tran_id'],
                'reseller_id' => $r->id,
                'currency' => $data['req'][0]->softpin_currency,
                'deno' => $data['req'][0]->softpin_amt,
                'qty' => $request_qty,
                'description' => $data['req'][0]->description,
                'reseller_request_date' => $data['req'][0]->sub_date,
                'status' => 0
            );

            $p = new PinRequest($data_array);
            $p->create_date = new DooDbExpression('NOW()');

            $id = $p->insert();
        } else {
            $pr = $pr->getOne(array('select' => 'id, status', 'asArray' => true));

            if ($pr['status'] == 1) {
                $error_array['tran_errcode'] = 'Pins already delivered';

                $this->toJSON($error_array, true);
                return 400;
            }

            $id = $pr['id'];
        }

        //Doo::logger()->log('Added new Pin Request - ' . json_encode($p->getDataArray()), $id, 'api_pin_request');

        $exclusive = $data['req'][0]->exclusive;
        
        $rs = $this->pinRequestVefication($id, $data['tran_id'], $r->verification_url, $data['req'][0]->softpin_currency, $data['req'][0]->softpin_amt, $request_qty, $exclusive);

        if ($rs['code'] == 200) {
            $this->toJSON($rs['result'], true);

            return;
        }

        $error_array['tran_errcode'] = $rs['msg'];

        $this->toJSON($error_array, true);

        return $rs['code'];
    }

    function pinRequestVefication($id, $tran_id, $verify_url, $currency, $deno, $qty, $excl) {
        Doo::logger()->log('Pin Request Verification - Transaction ID #' . $tran_id, $id, 'api_pin_request');

        $pr = new PinRequest();
        $pr->id = $id;
        $pr->trans_id = $tran_id;
        $pr->status = 0;

        if ($pr->count() > 0) {
            $p = new Pin();
            $rs = $p->deliverPin($id, $excl);

            if ($rs['code'] == 200) {
                $pr = new PinRequest(array('id' => $id, 'status' => 1));
                $pr->update();
            }

            return $rs;
        } else {
            $rs = array('code' => 400, 'msg' => 'Pins already delivered');

            return $rs;
        }
    }

}

