<?php
Doo::loadCore('auth/DooAuth');
Doo::loadModel('User');

class AppController extends DooController {
	public $autorender = false;
	/**
	 * Auth session object of the application
	 * @var DooAuth
	 */
	public $auth;

	/**
	 * Session object of the application
	 * @var DooSession
	 */
	public $session;
	
	public function afterRun($routeResult) {
		parent::afterRun($routeResult);
		
		$logs = Doo::logger()->getLogs();
		
		if (!empty($logs)) {
			foreach($logs as $l){
				$lmodel = new Log;
				$lmodel->logtime = new DooDbExpression('NOW()');
				$lmodel->msg = $l[0];
				$lmodel->item_id = (int)$l[1];
				$lmodel->module = $l[2];

				if (isset(Doo::session()->user->username)) {
					$lmodel->act_by = Doo::session()->user->username;
				}

				$lmodel->ip = Doo::session()->clientIP;
				$lmodel->insert();
			}
		}		
	}

	public function __construct()  {
		$this->auth = new DooAuth('pin'); //constructor with namespace (can also be obtained from external source, example common.conf.php :-) )
		$this->auth->setSalt(Doo::conf()->salt); //Intialize salt
		$this->auth->start(); //Start auth component
		$this->session = Doo::session();

		if ($this->auth->isValid()) { //success User authentication
			$this->data['username'] = $this->auth->username;
			$this->data['group'] = $this->auth->group;
		} else if(isset($_COOKIE['pinnm'])) {
			$this->loginCookie();
		}

		Doo::conf()->auth = $this->auth;
		Doo::db()->query("SET time_zone = 'Asia/Kuala_Lumpur'");
		$this->session->clientIP = $this->clientIP();

		$this->setContentType('html');
	}

	public static function _checkPermit($controller, $action){
		Doo::acl()->rules = Doo::session()->acl;
		
		if (Doo::acl()->process(Doo::conf()->auth->group, $controller, $action)) {
			return false;
		}
		
		return true;
	}

	public function checkPermit($controller, $action){
		return self::_checkPermit($controller, $action);
	}
	
	public function index(){
		$this->autorender = true;

		$this->vdata['nav'] = 'home';
		$this->vdata['subnav'] = 'dashboard';
	}
	
	public function login(){
		$this->autorender = true;

		if($this->auth->isValid())
			return '/';
	}
	
	public function loginCookie(){
		if (!empty($_COOKIE['pinhash']) && !empty($_COOKIE['pinhs'])){
			//user agent hash must be the same
			if ($_COOKIE['pinhash'] == md5($this->auth->getSalt() . $_SERVER['HTTP_USER_AGENT'])){
				$u = new User;
				$u->username = $_COOKIE['pinnm'];
				$u = $u->getOne();
				
				if (isset($u->pwd)){
					$pwd = explode(':', $u->pwd);
					
					if($_COOKIE['pinhs'] == md5($pwd[1] . $_COOKIE['pinnm'] . $_SERVER['HTTP_USER_AGENT']. $pwd[0]) ){
						$this->auth->setSecurityLevel(DooAuth::LEVEL_HIGH); //Needed high security level
						$this->auth->setData($u->username, 'member');
						unset($u->pwd);
						$this->session->user = $u;
						//use to popup news in future
						setcookie('lastlogin', time(), time()+31536000, '/', Doo::conf()->cookie_domain);
						DooUriRouter::redirect($_SERVER['REQUEST_URI']);
					}
				}
			}
		}
	}
	
	public function debug($var){
		Doo::app()->debug($var);
	}
	
	public function beforeRun($resource, $action) {
		if ($action != 'login' && $resource != 'ApiController')
			return $this->mustLogin($resource, $action);
	}
	
	public function mustLogin($controller, $action){
		//if not login!
		if (!$this->auth->isValid()) {
			//$this->session->after_login = (empty($afterLoginUrl))?$_SERVER['REQUEST_URI']:$afterLoginUrl;
			return '/login';
		}
		
		$this->acl()->rules = $this->session->acl;
		
		//if role have no access!
		$this->acl()->defaultFailedRoute = '/login';
		
		if ($rs = $this->acl()->process($this->auth->group, $controller, $action)) {
			if($this->isAjax())
				return 403;
			return $rs;
		}
	}
	
	public function updateUserSession(){
		$user = new User;
		$user->username = $this->auth->username;
		$user = $user->getOne();
		unset($user->pwd);
		$this->session->user = $user;
	}
}