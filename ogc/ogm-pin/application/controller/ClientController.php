<?php
class ClientController extends AppController{
	public $autorender = false;
	
	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'client';
		Doo::loadModel('Client');
	}
	
	public function listClient() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'client';
		$this->vdata['subnav'] = 'manage_client';
	}
	
	public function getListClient() {
		$item_per_pg = 10;
		
		$c = new Client;
		
		$opt['where'] = 'active IN (0,1)';
		$totalPage = $c->count($opt);
		$totalPage = ceil($totalPage / $item_per_pg);

		$pg = 0;
		
		if (isset($this->params['page'])){
			$pg = intval($this->params['page']);
			
			if ($pg <= 0){
				return 404;
			}
			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$clients = $c->limit("$pg,$item_per_pg", 'name', null, $opt);
		
		$this->toJSON(array('total_page' => intval($totalPage), 'clients' => $clients), true);
	}
	
	public function listAllClient() {
		if (empty($_GET['q'])) return;

		$c = new Client;

		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY name ASC LIMIT 50";
		$opt['select'] = 'id, name';
		$opt['where'] = ' active IN (0, 1) AND name LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');

		$rs = $c->find($opt);

		if (is_null($rs)) {
			$rs = array();
		}

		$this->toJSON($rs, true, true);
	}

	public function clientInfo(){
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_client';

		if (intval($this->params[0]) < 1) {
			return 404;
		}

		$c = new Client;

		$opt['where'] = "id = '" . (int)$this->params[0] . "'";
		$this->vdata['rs'] = $c->getOne($opt);
	}
	
	public function newClient() {
		$this->vdata['subnav'] = 'new_client';

		$this->renderc('client/client-info', $this->vdata);
	}

	public function create() {
		foreach($_POST as $k=>$v){
			$_POST[$k] = trim($v);
		}

		$rules = array(
					'name' => array(
									array('dbNotExist', 'client', 'name', 'Client name already exists. Please use another name.'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Client name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Client name cannot be longer than 65 characters'),
									array('minlength', 2, 'Client name cannot be shorter than 2 characters')
								),
					'url' => array(
									array('url'),
									array('maxlength', 255, 'URL cannot be longer than 255 characters'),
									array('minlength', 5)
								),
					'api_key' => array(
									array('dbNotExist', 'client', 'api_key', 'Merchant Code already exist. Please use another Merchant Code'),
									array('alphaNumeric', 'Merchant Code can only consist letters & number'),
									array('maxlength', 16, 'Merchant Code cannot be longer than 16 characters'),
									array('minlength', 3, 'Merchant Code cannot be shorter than 3 characters')
								),
					'secret_key' => array(
									array('alphaNumeric', 'Secret Key can only consist letters & number'),
									array('maxlength', 65, 'Secret Key cannot be longer than 45 characters'),
									array('minlength', 6, 'Secret Key cannot be shorter than 6 characters')
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}

		if (isset($_POST['id']))
			unset($_POST['id']);

		$c = new Client($_POST);
		$c->create_date = new DooDbExpression('NOW()');
		$c->create_by = $this->auth->userID;

		$id = $c->insert();

		Doo::logger()->log('Added new client - ' . json_encode($c->getDataArray()), $id, 'client');

		return 201;
	}
	
	public function update() {
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}

		$rules = array(
					'id' => array('dbExist', 'client', 'id', 'Client does not exist. It might been deleted.'),
					'name' => array(
									//array('dbNotExist', 'client', 'api_key', 'Merchant Code already exist. Please use another Merchant Code'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Client name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Client name cannot be longer than 65 characters'),
									array('minlength', 2, 'Client name cannot be shorter than 2 characters')
								),
					'url' => array(
									array('url'),
									array('maxlength', 255, 'Url cannot be longer than 255 characters'),
									array('minlength', 5)
								),
					'api_key' => array(
									//array('dbNotExist', 'client', 'api_key', 'Client does not exist. It might been deleted.'),
									array('alphaNumeric', 'Merchant Code  can only consist letters & number'),
									array('maxlength', 16, 'Merchant Code cannot be longer than 16 characters'),
									array('minlength', 3, 'Merchant Code cannot be shorter than 3 characters')
								),
					'secret_key' => array(
									array('alphaNumeric', 'Secret Key can only consist letters & number'),
									array('maxlength', 65, 'Secret Key cannot be longer than 45 characters'),
									array('minlength', 6, 'Secret Key cannot be shorter than 6 characters')
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$c = new Client($_POST);
		
		$c->update();
		
		Doo::logger()->log('Update details of client (ID '. $c->id .') - ' . json_encode($c->getDataArray()), $c->id, 'client');
	}
	
	public function delete() {
		if (intval($this->params[0]) < 1) {
			return 404;
		}

		$c = new Client;
		$c->id = (int)$this->params[0];
		$c->active = 2;

		$c->update();
		
		Doo::logger()->log('Delete client - ' . json_encode($c->getDataArray()), $c->id, 'client');

		return '/client/list-client';
	}
}