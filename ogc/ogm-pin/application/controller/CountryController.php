<?php
class CountryController extends AppController {
	public $autorender = false;
	
	public function __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'setting';
		Doo::loadModel('Country');
	}
	
	public function listCountry() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'setting';
		$this->vdata['subnav'] = 'manage_country';
	}
	
	public function getListCountry() {
		$item_per_pg = 10;
		
		$c = new Country();
		
		$totalPage = $c->count();
		$totalPage = ceil($totalPage / $item_per_pg);
		
		$pg = 0;
		
		if (isset($this->params['page'])) {
			$pg = intval($this->params['page']);
			
			if ($pg <= 0) {
				return 404;
			}
			
			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$countries = $c->limit("$pg,$item_per_pg", 'name');
		
		$this->toJSON(array('total_page' => intval($totalPage), 'countries' => $countries), true);
	}
	
	public function listAllCountry() {
		if (empty($_GET['q']))
			return;
		
		$c = new Country();
		
		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY name ASC LIMIT 50";
		$opt['select'] = 'id, name';
		$opt['where'] = ' name LIKE ?';
		$opt['param'] = array('%' . $_GET['q'] . '%');
		
		$rs = $c->find($opt);
		
		if (is_null($rs)) {
			$rs = array();
		}
		
		$this->toJSON($rs, true, true);
	}
	
	public function countryInfo() {
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_country';
		
		if (intval($this->params[0]) < 1) {
			return 404;
		}
		
		$c = new Country();
		$c->id = $this->params[0];
		
		$this->vdata['rs'] = $c->getOne();
	}
	
	public function newCountry() {
		$this->vdata['subnav'] = 'new_country';
		
		$this->renderc('country/country-info', $this->vdata);
	}
	
	public function create() {
		foreach ($_POST as $k => $v) {
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
			'id' => array(
				array('dbNotExist', 'country', 'id', 'ID already exists. Please use another ID.'),
				array('regex', '/^[0-9]+$/', 'ID can only consist of numbers.'),
				array('maxlength', 3, 'ID can only be 3 characters'),
				array('minlength', 3, 'ID can only be 3 characters')
			),
			'name' => array(
				array('dbNotExist', 'country', 'name', 'Country name already exists. Please use another name.'),
				array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Country name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
				array('maxlength', 128, 'Country name cannot be longer than 128 characters'),
				array('minlength', 2, 'Country name cannot be shorter than 2 characters')
			)
		);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$c = new Country($_POST);
		$c->insert();
		
		Doo::logger()->log('Added new country - ' . json_encode($c->getDataArray()), $_POST['id'], 'country');
		
		return 201;
	}
	
	public function update() {
		foreach ($_POST as $k => $v) {
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
			'id' => array(
				//array('dbNotExist', 'country', 'id', 'ID already exists. Please use another ID.'),
				array('regex', '/^[0-9]+$/', 'ID can only consist of numbers.'),
				array('maxlength', 3, 'ID can only be 3 characters'),
				array('minlength', 3, 'ID can only be 3 characters')
			),
			'name' => array(
				//array('dbNotExist', 'country', 'name', 'Country name already exist. Please use another name.'),
				array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Country name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
				array('maxlength', 32, 'Country name cannot be longer than 32 characters'),
				array('minlength', 2, 'Country name cannot be shorter than 2 characters')
			)
		);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		if ($_POST['id'] !== $_POST['old_id']) {
			$c = new Country;
			$c->id = $_POST['id'];
			$c = $c->count();
			
			if ($c > 0) {
				$this->toJSON('ID already exists. Please use another ID.', true);
				unset($c);

				return 400;
			}
		}
		
		$c = new Country($_POST);
		$c->update();
		
		Doo::logger()->log('Update country - ' . json_encode($c->getDataArray()), $c->id, 'country');
	}
}