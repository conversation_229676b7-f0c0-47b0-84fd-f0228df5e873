<?php
class LogController extends AppController{
	public $autorender = true;
	public $pageurl;

	public function index(){
		$log = new Log();

		if (!empty($_GET['module'])) {
			if($_GET['module']!='all')
			   $log->module = $_GET['module'];
		}

		$start_date = $end_date = null;

		if (!empty($_GET['start_date'])) {
			$start_date = strtotime($_GET['start_date']);
			if($start_date!==-1){
				$start_date = date('Y-m-d', $start_date);
			}
		}

		if (!empty($_GET['end_date'])){
			$end_date = strtotime($_GET['end_date']);

			if ($end_date !== -1) {
				$end_date = date('Y-m-d 23:59:59', $end_date);
			}
		}

		if ($start_date != null && $end_date != null){
			$log->logtime = new DooDbExpression("<= '$end_date' AND logtime >= '$start_date'", false, true);
		} else if($start_date != null) {
			$log->logtime = new DooDbExpression(">= '$start_date'", false, true);            
		} else if($end_date != null) {
			$log->logtime = new DooDbExpression("<= '$end_date'", false, true);            
		}

		$totalPage = $log->count();
		$itmPerPage = 10;

		$page = intval($this->getKeyParam('page'));

		$this->pageurl = '/log/index/page';
		$pager = new DooPager($this->pageurl, $totalPage, $itmPerPage, 6);

		if ($page > 0){
			$pager->paginate($page);
		} else {
			$pager->paginate(1);
			$page = 1;
		}        

		if ($totalPage > 0) {
			$logs = $log->limit($pager->limit, null, 'id');
		} else {
			$logs = array();
		}

		$_SERVER['QUERY_STRING'] = filter_var($_SERVER['QUERY_STRING'], FILTER_SANITIZE_STRING);
		$this->vdata['pager'] = preg_replace('/'. preg_quote($this->pageurl,'/'). '\/(\d+)/', 
											$this->pageurl. '/$1/?' . $_SERVER['QUERY_STRING'], 
											$pager->output);

		$this->vdata['pageurl'] = $this->pageurl . '/' . $page .'/?'. $_SERVER['QUERY_STRING'];
		$this->vdata['filter'] = '/log/index/?'. $_SERVER['QUERY_STRING'];
		$this->vdata['logs'] = $logs;
		$this->vdata['start_date'] = $start_date;
		$this->vdata['end_date'] = substr($end_date,0,10);
		$this->vdata['module'] = (!empty($log->module))? $log->module : 'all';
		$this->vdata['module_list'] = array('all','api_auth','api_redeem','api_getpininfo','api_pin_request','client','country','login','pin','pinrequest','pinrequest_generatepin','pinrequest_export','product', 'reseller','usergroup','usergroup_permission','user','user_myaccount');
	}
}