<?php
class LoginController extends AppController {
	public $autorender = false;
	
	public function beforeRun($resource, $action) {
		//if already login, redirect back to home if user/hacker try do relogin while session valid. Must logout first
		if ($action=='login' && $this->auth->isValid()) {
			return;
		}
	}
	
	public function loginFirst() {
		//$this->data['error'] = 'Please login with your account details.';
		return 401;
	}

	/**
	 * Check if client login session is still valid.
	 * @return int Status code 200 or 401
	 */
	public function isLogin() {
		if ($this->auth->isValid()) {
			$this->toJSON(array('role' => $this->auth->group, 'username' => $this->auth->username, 'redirect' => '/'), true);
			return;
		}
		return 401;
	}

	/**
	 * Login for client
	 * @return int Status code 200 or 401(with errors)
	 */
	public function login() {
		if (isset($_POST['login']) && isset($_POST['pass'])) {
			$_POST['login'] = trim($_POST['login']);
			$_POST['pass'] = $_POST['pass'];

			if (empty($_POST['login']) || empty($_POST['pass'])){
				$this->showError('Pleased fill in both username and password.');
				return 401;
			}

			Doo::loadModel('User');

			$u = new User;
			$u->username = $_POST['login'];

			$opt['select'] = 'username, id, user_type, user_group, password, email';
			$opt['where'] = 'active = 1';
			$user_result = $u->getOne($opt);

			if (empty($user_result)){
				$this->showError('Failed to login! Wrong username and password combination.');
				return 401;
			}

			$user_password = $user_result->password;

			if ($user_password != '') {
				$password_array = explode(':', $user_password);

				if (md5($_POST['pass'] . $password_array[1]) == $password_array[0]) {
					$role = $user_result->user_group;

					if ($user_result->user_type == 'super_admin') {
						$role = $user_result->user_type;

						$this->session->acl = include(Doo::conf()->SITE_PATH .'/'. Doo::conf()->PROTECTED_FOLDER . '/config/acl.conf.php');
					} else {
						$ug = new UserGroup();
						$ug->name = $user_result->user_group;

						$pm = $ug->getOne(array('select' => 'permission', 'asArray' => true));

						$temp_array = array();

						$permit = (array)json_decode($pm['permission']);

						$this->session->acl = array(
							strtolower($user_result->user_group) => array('allow' => $permit)
						);
					}
					
					$this->auth->setSecurityLevel(DooAuth::LEVEL_HIGH); //Needed high security level
					$this->auth->setData($_POST['login'], strtolower($role), $user_result->id);

					$user_result = $user_result->getDataObject();

					unset($user_result->password);
					$this->session->user = $user_result;

					//$this->setRememberMe($pwd[0], $pwd[1]);
					//use to popup news in future
					//setcookie('lastlogin', time(), time()+31536000, '/', Doo::conf()->cookie_domain);

					$this->toJSON(array('role' => $this->auth->group, 'username' => $this->auth->username, 'redirect'=>'/'), true);

					Doo::logger()->log('Logined as '. $user_result->username, $user_result->id, 'login');                             

					return;
				} else {
					Doo::logger()->log('Fail to login as '. $u->username, '0', 'login');                             
					$this->showError('Failed to login! Wrong username and password combination.');
					return 401;
				}
			} else {
				Doo::logger()->log('Fail to login as '. $u->username, '0', 'login');                             
				$this->showError('Failed to login! Wrong username and password combination.');
				return 401;
			}
		}
		
		$this->showError('Please fill in both nick name and password.');
		return 401;
	}

	public function setRememberMe($pwd, $salt) {
		if (!empty($_POST['autologin'])) {
			setcookie('pinnm', $this->auth->username , time()+31536000, '/', Doo::conf()->cookie_domain);
			setcookie('pinhash', md5($this->auth->getSalt() . $_SERVER['HTTP_USER_AGENT']) , time()+31536000, '/', Doo::conf()->cookie_domain);
			setcookie('pinhs', md5($salt . $this->auth->username . $_SERVER['HTTP_USER_AGENT'] . $pwd) , time()+31536000, '/', Doo::conf()->cookie_domain);
		}
	}

	public function showError($errorMsg){
		echo json_encode($errorMsg);
	}

	/**
	 * Logout for client
	 * @return int Status code 200
	 */
	public function logout() {
		if ($this->auth->isValid()){
			$this->auth->finalize();
			setcookie('pinnm', '', 0, '/', Doo::conf()->cookie_domain);
			setcookie('pinhash', '', 0, '/', Doo::conf()->cookie_domain);
			setcookie('pinhs', '', 0, '/', Doo::conf()->cookie_domain);
		}
		//return 200;
		DooUriRouter::redirect(Doo::conf()->APP_URL);
	}

	/**
	 * Send an activation link to login and change password (this is for change password, forgot the current password)
	 */
	public function sendPasswordResetLink(){
		Doo::loadModel('User');
		$u = new User;
		
		$u->username = $_POST['username'];
		$u->active = 1;
		
		$u = $u->getOne();

        if (empty($u)) {
            return 404;
        } else {
			$pr = new UserPwdrecall;
			$pr->user_id = $u->id;

			if ($pr->count()) {
				$pr->delete();
			}
			
			$pr->token = md5($u->id . time() . Doo::conf()->salt);
			$pr->create_date = new DooDbExpression("NOW()");

			if ($pr->insert()) {
				$headers = "From: <EMAIL>";
				$headers .= "\nReply-To: <EMAIL>";
				
				$email = $u->email;
				
				$urlReset = Doo::conf()->APP_URL .= "forgot_password/{$pr->token}";
				
				$mail = new DooMailer();
				$mail->addTo($email);
				$mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
				$mail->setSubject('Password reset for your OffGamers Pin Generation account');
				
				$str = "
Hi {$u->username},

Can't remember your password, huh? It happens to the best of us too.

Please click on the link below or copy and paste the URL into your browser:
{$urlReset}

This will reset your password. You can then login and change it to something you can remember.


------------------------------------------------------

Customer Support
OffGamers Pin Generation
                ";
				$mail->setBodyText($str);
				$mail->send();
			} else {
				return 500;
			}
		}
	}
	
	/**
	 * Login to change password. Click from the pwd activation link sent to email.
	 */
	public function fromEmailLinkChangePwd() {
		Doo::loadModel('UserPwdrecall');
		$pr = new UserPwdrecall;
		$pr->token = $this->params['token'];
		$pr = $pr->getOne();
		
		if ($pr) {
			//activation link only valid for 24
			$time = strtotime($pr->create_date);
			if ((time() - $time)/(3600*24) > 1) {
				$this->vdata['rs'] = array('error' => 'This activation token has expired!');
            } else {
				//display change password page without the Current Password field.
				$this->session->pwdtoken = $this->params['token'];
				
				$this->vdata['rs'] = array('token' => $this->params['token']);
			}
		} else {
			$this->vdata['rs'] = array('error' => 'Invalid token for password modification!');
		}
		
		$this->renderc('forgot-password', $this->vdata);
	}

	public function changePwdFromToken() {
		$token = $this->session->pwdtoken;
		
		if (!empty($token) && $this->params['token'] == $token) {
			if ($_POST['new_pwd'] != $_POST['confirm_pwd']){
				$arr = 'Please confirm your new password.';
				echo json_encode($arr);
				return 400;
			} else {
				$pwdrecall = new UserPwdrecall;
				$pwdrecall->token = $token;
				$pwdrecall = $pwdrecall->getOne();
				
				$u = new User;
				$u->id = $pwdrecall->user_id;
				$u = $u->getOne();
				$pwd = explode(':', $u->password);
				$u->password = md5($_POST['new_pwd'] . $pwd[1]) .':'. $pwd[1];
				$u->update(array('field' => 'password'));
				
				//delete this token.
				$pwdrecall->delete();
				
				$this->session->pwdtoken = null;
			}
		} else {
			$arr = 'Invalid token for password modification!';
			echo json_encode($arr);
			return 400;
		}
	}
	
	public function forgotPassword() {
		$this->renderc('forgot-password');
	}
}