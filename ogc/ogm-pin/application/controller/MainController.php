<?php
/**
 * MainController
 * Feel free to delete the methods and replace them with your own code.
 *
 * <AUTHOR>
 */
class MainController extends DooController{
	public function index(){
		//Just replace these
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::displayHome();
	}

	public function allurl(){	
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::showAllUrl();	
	}

	public function debug(){
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::showDebug($this->params['filename']);
	}

	public function gen_sitemap_controller(){
		//This will replace the routes.conf.php file
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::buildSitemap(true);		
		DooSiteMagic::buildSite();
	}
	
	public function gen_sitemap(){
		//This will write a new file,  routes2.conf.php file
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::buildSitemap();		
	}

	public function gen_site(){
		Doo::loadCore('app/DooSiteMagic');
		DooSiteMagic::buildSite();
	}

	public function gen_model(){
		Doo::loadCore('db/DooModelGen');
		DooModelGen::genMySQL();
	}

	public function setup(){
		$sql = "SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;";
		$this->db()->query($sql);
		$sql = "SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;";
		$this->db()->query($sql);
		$sql = "SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL';";
		$this->db()->query($sql);
		
		$sql = "DROP SCHEMA IF EXISTS `".Doo::conf()->db_name."` ;
				CREATE SCHEMA IF NOT EXISTS `".Doo::conf()->db_name."` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci ;
				USE `".Doo::conf()->db_name."` ;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`user_group` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `name` VARCHAR(32) NOT NULL,
				  `permission` TEXT NULL,
				  `create_date` DATETIME NOT NULL,
				  PRIMARY KEY (`id`) ,
				  INDEX `name` (`name` ASC) )
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`user` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `first_name` VARCHAR(45) NOT NULL,
				  `last_name` VARCHAR(45) NOT NULL,
				  `username` VARCHAR(45) NOT NULL,
				  `password` VARCHAR(65) NOT NULL,
				  `user_type` ENUM('super_admin','admin') NOT NULL,
				  `user_group` VARCHAR(32) NOT NULL,
				  `email` VARCHAR(65) NOT NULL,
				  `active` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1,
				  PRIMARY KEY (`id`),
				  UNIQUE INDEX `username_UNIQUE` (`username` ASC),
				  INDEX `fk_user_user_group1` (`user_group` ASC),
				  CONSTRAINT `fk_user_user_group1`
					FOREIGN KEY (`user_group` )
					REFERENCES `".Doo::conf()->db_name."`.`user_group` (`name`)
					ON DELETE NO ACTION
					ON UPDATE CASCADE)
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`reseller` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
				  `name` VARCHAR(65) NOT NULL ,
				  `email` VARCHAR(65) NOT NULL ,
				  `tel` VARCHAR(65) NOT NULL ,
				  `reseller_code` VARCHAR(16) NOT NULL ,
				  `secret_key` VARCHAR(45) NOT NULL ,
				  `verification_url` VARCHAR(255) NOT NULL ,
				  `description` TINYTEXT NULL ,
				  `active` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1 ,
				  `create_date` DATETIME NOT NULL ,
				  `create_by` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  PRIMARY KEY (`id`) ,
				  INDEX `fk_reseller_user1` (`create_by` ASC) ,
				  CONSTRAINT `fk_reseller_user1`
					FOREIGN KEY (`create_by` )
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`client` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `name` VARCHAR(65) NOT NULL,
				  `url` VARCHAR(255) NOT NULL,
				  `description` MEDIUMTEXT NULL,
				  `api_key` VARCHAR(16) NOT NULL,
				  `secret_key` VARCHAR(45) NOT NULL,
				  `active` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1,
				  `create_date` DATETIME NOT NULL ,
				  `create_by` MEDIUMINT(8) UNSIGNED NOT NULL,
				  PRIMARY KEY (`id`),
				  UNIQUE INDEX `code_UNIQUE` (`api_key` ASC),
				  INDEX `fk_client_user1` (`create_by` ASC),
				  CONSTRAINT `fk_client_user1`
					FOREIGN KEY (`create_by`)
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`product` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `name` VARCHAR(65) NOT NULL,
				  `currency` VARCHAR(3) NOT NULL,
				  `deno` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00,
				  `barcode` VARCHAR(45) NULL,
				  `description` TINYTEXT NULL,
				  `active` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1,
				  `create_date` DATETIME NOT NULL,
				  `create_by` MEDIUMINT(8) UNSIGNED NOT NULL,
				  PRIMARY KEY (`id`),
				  INDEX `fk_product_user1` (`create_by` ASC),
				  CONSTRAINT `fk_product_user1`
					FOREIGN KEY (`create_by` )
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`gen_request` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
				  `title` VARCHAR(65) NOT NULL,
				  `status` ENUM('Pending','Approved','Cancelled','Deleted') NOT NULL,
				  `quantity` INT(5) UNSIGNED NOT NULL,
				  `product_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `reseller_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `client_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `country_id` VARCHAR(3) NOT NULL,
				  `pin_type` TINYINT(1) UNSIGNED NOT NULL,
				  `start_date` DATE NOT NULL,
				  `end_date` DATE NOT NULL,
				  `description` TINYTEXT NOT NULL,
				  `request_date` DATETIME NOT NULL,
				  `request_by` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `approve_by` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `product_name` VARCHAR(65) NOT NULL,
				  `currency` VARCHAR(3) NOT NULL,
				  `deno` DECIMAL(10,2) UNSIGNED NOT NULL,
				  `barcode` VARCHAR(45) NULL,
				  `product_description` TINYTEXT NULL,
				  PRIMARY KEY (`id`) ,
				  INDEX `fk_generate_request_client1` (`client_id` ASC),
				  INDEX `fk_generate_request_reseller1` (`reseller_id` ASC),
				  INDEX `fk_gen_request_user1` (`request_by` ASC) ,
				  INDEX `fk_gen_request_product1` (`product_id` ASC) ,
				  CONSTRAINT `fk_generate_request_client1`
					FOREIGN KEY (`client_id`)
					REFERENCES `".Doo::conf()->db_name."`.`client` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION,
				  CONSTRAINT `fk_generate_request_reseller1`
					FOREIGN KEY (`reseller_id`)
					REFERENCES `".Doo::conf()->db_name."`.`reseller` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION,
				  CONSTRAINT `fk_gen_request_user1`
					FOREIGN KEY (`request_by`)
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION,
				  CONSTRAINT `fk_gen_request_product1`
					FOREIGN KEY (`product_id`)
					REFERENCES `".Doo::conf()->db_name."`.`product` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB
				AUTO_INCREMENT = 10001;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`pin` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `batch_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `pin` VARCHAR(16) NOT NULL,
				  `serial` VARCHAR(18) NOT NULL,
				  `status` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1,
				  `redeem` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
				  `currency` VARCHAR(3) NOT NULL,
				  `deno` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00,
				  `start_date` DATE NOT NULL,
				  `end_date` DATE NOT NULL,
				  `extend` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 ,
				  PRIMARY KEY (`id`),
				  UNIQUE INDEX `serial_UNIQUE` (`serial` ASC),
				  INDEX `fk_pin_generate_request1` (`batch_id` ASC),
				  UNIQUE INDEX `index_serial` (`serial` ASC),
				  INDEX `index_pin` (`pin` ASC),
				  INDEX `index_status` (`status` ASC),
				  INDEX `index_redeem` (`redeem` ASC),
				  CONSTRAINT `fk_pin_generate_request1`
					FOREIGN KEY (`batch_id` )
					REFERENCES `".Doo::conf()->db_name."`.`gen_request` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`country` (
				  `id` VARCHAR(3) NOT NULL,
				  `name` VARCHAR(128) NOT NULL,
				  PRIMARY KEY (`id`),
				  UNIQUE INDEX `id_UNIQUE` (`id` ASC),
				  UNIQUE INDEX `name_UNIQUE` (`name` ASC) )
				ENGINE = InnoDB;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`log` (
				  `id` BIGINT(19) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `module` VARCHAR(25) NULL DEFAULT NULL,
				  `msg` MEDIUMTEXT NULL DEFAULT NULL,
				  `ip` VARCHAR(19) NULL DEFAULT NULL,
				  `item_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL,
				  `act_by` VARCHAR(45) NULL DEFAULT NULL,
				  `logtime` DATETIME NULL,
				  PRIMARY KEY (`id`),
				  INDEX `index_module` (`module` ASC),
				  INDEX `index_act_by` (`act_by` ASC) )
				ENGINE = InnoDB
				DEFAULT CHARACTER SET = utf8
				COLLATE = utf8_general_ci;";

		$this->db()->query($sql);

		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`gen_request_remark` (
				  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `create_date` DATETIME NOT NULL,
				  `remarks` TINYTEXT NULL DEFAULT NULL,
				  `create_by` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `gen_request_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  PRIMARY KEY (`id`),
				  INDEX `fk_generate_request_remark_user1` (`create_by` ASC),
				  INDEX `fk_gen_request_remark_gen_request1` (`gen_request_id` ASC),
				  CONSTRAINT `fk_generate_request_remark_user1`
					FOREIGN KEY (`create_by` )
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION,
				  CONSTRAINT `fk_gen_request_remark_gen_request1`
					FOREIGN KEY (`gen_request_id` )
					REFERENCES `".Doo::conf()->db_name."`.`gen_request` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB
				DEFAULT CHARACTER SET = utf8
				COLLATE = utf8_general_ci;";

		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`token` (
				  `oauth_token` VARCHAR(40) NOT NULL,
				  `merchant_code` VARCHAR(16) NOT NULL,
				  `expires` INT(11) UNSIGNED NOT NULL,
				  `scope` VARCHAR(200) NOT NULL,
				  `create_date` DATETIME NOT NULL,
				  `ip` VARCHAR(15) NOT NULL,
				  PRIMARY KEY (`oauth_token`) )
				ENGINE = InnoDB;";
		
		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`pin_remark` (
				  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
				  `create_date` DATETIME NOT NULL,
				  `remarks` TINYTEXT NULL,
				  `create_by` MEDIUMINT(8) NOT NULL,
				  `pin_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  PRIMARY KEY (`id`),
				  INDEX `fk_pin_remark_pin1` (`pin_id` ASC),
				  CONSTRAINT `fk_pin_remark_pin1`
					FOREIGN KEY (`pin_id`)
					REFERENCES `".Doo::conf()->db_name."`.`pin` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";
		
		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`user_pwdrecall` (
				  `id` SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT,
				  `user_id` MEDIUMINT(8) UNSIGNED NOT NULL,
				  `token` VARCHAR(65) NULL,
				  `create_date` DATETIME NOT NULL,
				  PRIMARY KEY (`id`),
				  INDEX `fk_user_pwdrecall_user1` (`user_id` ASC),
				  CONSTRAINT `fk_user_pwdrecall_user1`
					FOREIGN KEY (`user_id`)
					REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";
		
		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`pin_request` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
				  `reseller_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `command` VARCHAR(45) NOT NULL ,
				  `trans_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `currency` VARCHAR(3) NOT NULL ,
				  `deno` DECIMAL(10,2) UNSIGNED NOT NULL ,
				  `qty` SMALLINT UNSIGNED NOT NULL ,
				  `description` TINYTEXT NULL ,
				  `status` TINYINT UNSIGNED NOT NULL ,
				  `reseller_request_date` DATETIME NOT NULL ,
				  `create_date` DATETIME NOT NULL ,
				  PRIMARY KEY (`id`) ,
				  INDEX `fk_pin_request_reseller1` (`reseller_id` ASC) ,
				  CONSTRAINT `fk_pin_request_reseller1`
					FOREIGN KEY (`reseller_id`)
					REFERENCES `".Doo::conf()->db_name."`.`reseller` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB;";
		
		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`pin_request_delivered` (
				  `pin_request_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `pins` LONGTEXT NOT NULL ,
				  PRIMARY KEY (`pin_request_id`) ,
				  INDEX `fk_pin_request_delivered_pin_request1` (`pin_request_id` ASC) ,
				  CONSTRAINT `fk_pin_request_delivered_pin_request1`
					FOREIGN KEY (`pin_request_id`)
					REFERENCES `".Doo::conf()->db_name."`.`pin_request` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB
				DEFAULT CHARACTER SET = utf8
				COLLATE = utf8_general_ci;";
		
		$this->db()->query($sql);
		
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`pin_status_log` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
				  `pin_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `status` TINYINT(1) UNSIGNED NOT NULL ,
				  `redeem` TINYINT(1) UNSIGNED NOT NULL ,
				  `create_date` DATETIME NOT NULL ,
				  PRIMARY KEY (`id`) ,
				  INDEX `fk_pin_status_log_pin1` (`pin_id` ASC) ,
				  CONSTRAINT `fk_pin_status_log_pin1`
					FOREIGN KEY (`pin_id`)
					REFERENCES `".Doo::conf()->db_name."`.`pin` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB
				DEFAULT CHARACTER SET = utf8
				COLLATE = utf8_general_ci;";
		
		$this->db()->query($sql);
		
		$sql = "SET SQL_MODE=@OLD_SQL_MODE;";
		$this->db()->query($sql);
		
		$sql = "SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;";
		$this->db()->query($sql);
		
		$sql = "SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;";
		$this->db()->query($sql);
		
		$list = array(
		"AALAND ISLANDS                                   " => "248",
		"AFGHANISTAN                                      " => "004",
		"ALBANIA                                          " => "008",
		"ALGERIA                                          " => "012",
		"AMERICAN SAMOA                                   " => "016",
		"ANDORRA                                          " => "020",
		"ANGOLA                                           " => "024",
		"ANGUILLA                                         " => "660",
		"ANTARCTICA                                       " => "010",
		"ANTIGUA AND BARBUDA                              " => "028",
		"ARGENTINA                                        " => "032",
		"ARMENIA                                          " => "051",
		"ARUBA                                            " => "533",
		"AUSTRALIA                                        " => "036",
		"AUSTRIA                                          " => "040",
		"AZERBAIJAN                                       " => "031",
		"BAHAMAS                                          " => "044",
		"BAHRAIN                                          " => "048",
		"BANGLADESH                                       " => "050",
		"BARBADOS                                         " => "052",
		"BELARUS                                          " => "112",
		"BELGIUM                                          " => "056",
		"BELIZE                                           " => "084",
		"BENIN                                            " => "204",
		"BERMUDA                                          " => "060",
		"BHUTAN                                           " => "064",
		"BOLIVIA                                          " => "068",
		"BOSNIA AND HERZEGOWINA                           " => "070",
		"BOTSWANA                                         " => "072",
		"BOUVET ISLAND                                    " => "074",
		"BRAZIL                                           " => "076",
		"BRITISH INDIAN OCEAN TERRITORY                   " => "086",
		"BRUNEI DARUSSALAM                                " => "096",
		"BULGARIA                                         " => "100",
		"BURKINA FASO                                     " => "854",
		"BURUNDI                                          " => "108",
		"CAMBODIA                                         " => "116",
		"CAMEROON                                         " => "120",
		"CANADA                                           " => "124",
		"CAPE VERDE                                       " => "132",
		"CAYMAN ISLANDS                                   " => "136",
		"CENTRAL AFRICAN REPUBLIC                         " => "140",
		"CHAD                                             " => "148",
		"CHILE                                            " => "152",
		"CHINA                                            " => "156",
		"CHRISTMAS ISLAND                                 " => "162",
		"COCOS (KEELING) ISLANDS                          " => "166",
		"COLOMBIA                                         " => "170",
		"COMOROS                                          " => "174",
		"CONGO, Democratic Republic                       " => "180",
		"CONGO, Republic                                  " => "178",
		"COOK ISLANDS                                     " => "184",
		"COSTA RICA                                       " => "188",
		"COTE D'IVOIRE                                    " => "384",
		"CROATIA                                          " => "191",
		"CUBA                                             " => "192",
		"CYPRUS                                           " => "196",
		"CZECH REPUBLIC                                   " => "203",
		"DENMARK                                          " => "208",
		"DJIBOUTI                                         " => "262",
		"DOMINICA                                         " => "212",
		"DOMINICAN REPUBLIC                               " => "214",
		"ECUADOR                                          " => "218",
		"EGYPT                                            " => "818",
		"EL SALVADOR                                      " => "222",
		"EQUATORIAL GUINEA                                " => "226",
		"ERITREA                                          " => "232",
		"ESTONIA                                          " => "233",
		"ETHIOPIA                                         " => "231",
		"FALKLAND ISLANDS                                 " => "238",
		"FAROE ISLANDS                                    " => "234",
		"FIJI                                             " => "242",
		"FINLAND                                          " => "246",
		"FRANCE                                           " => "250",
		"FRENCH GUIANA                                    " => "254",
		"FRENCH POLYNESIA                                 " => "258",
		"FRENCH SOUTHERN TERRITORIES                      " => "260",
		"GABON                                            " => "266",
		"GAMBIA                                           " => "270",
		"GEORGIA                                          " => "268",
		"GERMANY                                          " => "276",
		"GHANA                                            " => "288",
		"GIBRALTAR                                        " => "292",
		"GREECE                                           " => "300",
		"GREENLAND                                        " => "304",
		"GRENADA                                          " => "308",
		"GUADELOUPE                                       " => "312",
		"GUAM                                             " => "316",
		"GUATEMALA                                        " => "320",
		"GUINEA                                           " => "324",
		"GUINEA-BISSAU                                    " => "624",
		"GUYANA                                           " => "328",
		"HAITI                                            " => "332",
		"HEARD AND MC DONALD ISLANDS                      " => "334",
		"HONDURAS                                         " => "340",
		"HONG KONG                                        " => "344",
		"HUNGARY                                          " => "348",
		"ICELAND                                          " => "352",
		"INDIA                                            " => "356",
		"INDONESIA                                        " => "360",
		"IRAN                                             " => "364",
		"IRAQ                                             " => "368",
		"IRELAND                                          " => "372",
		"ISRAEL                                           " => "376",
		"ITALY                                            " => "380",
		"JAMAICA                                          " => "388",
		"JAPAN                                            " => "392",
		"JORDAN                                           " => "400",
		"KAZAKHSTAN                                       " => "398",
		"KENYA                                            " => "404",
		"KIRIBATI                                         " => "296",
		"SOUTH KOREA                                      " => "408",
		"NORTH KOREA                                      " => "410",
		"KUWAIT                                           " => "414",
		"KYRGYZSTAN                                       " => "417",
		"LAO PEOPLE'S DEMOCRATIC REPUBLIC                 " => "418",
		"LATVIA                                           " => "428",
		"LEBANON                                          " => "422",
		"LESOTHO                                          " => "426",
		"LIBERIA                                          " => "430",
		"LIBYAN ARAB JAMAHIRIYA                           " => "434",
		"LIECHTENSTEIN                                    " => "438",
		"LITHUANIA                                        " => "440",
		"LUXEMBOURG                                       " => "442",
		"MACAU                                            " => "446",
		"MACEDONIA                                        " => "807",
		"MADAGASCAR                                       " => "450",
		"MALAWI                                           " => "454",
		"MALAYSIA                                         " => "458",
		"MALDIVES                                         " => "462",
		"MALI                                             " => "466",
		"MALTA                                            " => "470",
		"MARSHALL ISLANDS                                 " => "584",
		"MARTINIQUE                                       " => "474",
		"MAURITANIA                                       " => "478",
		"MAURITIUS                                        " => "480",
		"MAYOTTE                                          " => "175",
		"MEXICO                                           " => "484",
		"MICRONESIA                                       " => "583",
		"MOLDOVA                                          " => "498",
		"MONACO                                           " => "492",
		"MONGOLIA                                         " => "496",
		"MONTSERRAT                                       " => "500",
		"MOROCCO                                          " => "504",
		"MOZAMBIQUE                                       " => "508",
		"MYANMAR                                          " => "104",
		"NAMIBIA                                          " => "516",
		"NAURU                                            " => "520",
		"NEPAL                                            " => "524",
		"NETHERLANDS                                      " => "528",
		"NETHERLANDS ANTILLES                             " => "530",
		"NEW CALEDONIA                                    " => "540",
		"NEW ZEALAND                                      " => "554",
		"NICARAGUA                                        " => "558",
		"NIGER                                            " => "562",
		"NIGERIA                                          " => "566",
		"NIUE                                             " => "570",
		"NORFOLK ISLAND                                   " => "574",
		"NORTHERN MARIANA ISLANDS                         " => "580",
		"NORWAY                                           " => "578",
		"OMAN                                             " => "512",
		"PAKISTAN                                         " => "586",
		"PALAU                                            " => "585",
		"PALESTINIAN TERRITORY, Occupied                  " => "275",
		"PANAMA                                           " => "591",
		"PAPUA NEW GUINEA                                 " => "598",
		"PARAGUAY                                         " => "600",
		"PERU                                             " => "604",
		"PHILIPPINES                                      " => "608",
		"PITCAIRN                                         " => "612",
		"POLAND                                           " => "616",
		"PORTUGAL                                         " => "620",
		"PUERTO RICO                                      " => "630",
		"QATAR                                            " => "634",
		"REUNION                                          " => "638",
		"ROMANIA                                          " => "642",
		"RUSSIAN FEDERATION                               " => "643",
		"RWANDA                                           " => "646",
		"SAINT HELENA                                     " => "654",
		"SAINT KITTS AND NEVIS                            " => "659",
		"SAINT LUCIA                                      " => "662",
		"SAINT PIERRE AND MIQUELON                        " => "666",
		"SAINT VINCENT AND THE GRENADINES                 " => "670",
		"SAMOA                                            " => "882",
		"SAN MARINO                                       " => "674",
		"SAO TOME AND PRINCIPE                            " => "678",
		"SAUDI ARABIA                                     " => "682",
		"SENEGAL                                          " => "686",
		"SERBIA AND MONTENEGRO                            " => "891",
		"SEYCHELLES                                       " => "690",
		"SIERRA LEONE                                     " => "694",
		"SINGAPORE                                        " => "702",
		"SLOVAKIA                                         " => "703",
		"SLOVENIA                                         " => "705",
		"SOLOMON ISLANDS                                  " => "090",
		"SOMALIA                                          " => "706",
		"SOUTH AFRICA                                     " => "710",
		"SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS     " => "239",
		"SPAIN                                            " => "724",
		"SRI LANKA                                        " => "144",
		"SUDAN                                            " => "736",
		"SURINAME                                         " => "740",
		"SVALBARD AND JAN MAYEN ISLANDS                   " => "744",
		"SWAZILAND                                        " => "748",
		"SWEDEN                                           " => "752",
		"SWITZERLAND                                      " => "756",
		"SYRIAN ARAB REPUBLIC                             " => "760",
		"TAIWAN                                           " => "158",
		"TAJIKISTAN                                       " => "762",
		"TANZANIA                                         " => "834",
		"THAILAND                                         " => "764",
		"TIMOR-LESTE                                      " => "626",
		"TOGO                                             " => "768",
		"TOKELAU                                          " => "772",
		"TONGA                                            " => "776",
		"TRINIDAD AND TOBAGO                              " => "780",
		"TUNISIA                                          " => "788",
		"TURKEY                                           " => "792",
		"TURKMENISTAN                                     " => "795",
		"TURKS AND CAICOS ISLANDS                         " => "796",
		"TUVALU                                           " => "798",
		"UGANDA                                           " => "800",
		"UKRAINE                                          " => "804",
		"UNITED ARAB EMIRATES                             " => "784",
		"UNITED KINGDOM                                   " => "826",
		"UNITED STATES                                    " => "840",
		"UNITED STATES MINOR OUTLYING ISLANDS             " => "581",
		"URUGUAY                                          " => "858",
		"UZBEKISTAN                                       " => "860",
		"VANUATU                                          " => "548",
		"VATICAN CITY STATE (HOLY SEE)                    " => "336",
		"VENEZUELA                                        " => "862",
		"VIET NAM                                         " => "704",
		"VIRGIN ISLANDS (BRITISH)                         " => "092",
		"VIRGIN ISLANDS (U.S.)                            " => "850",
		"WALLIS AND FUTUNA ISLANDS                        " => "876",
		"WESTERN SAHARA                                   " => "732",
		"YEMEN                                            " => "887",
		"ZAMBIA                                           " => "894",
		"ZIMBABWE                                         " => "716",
		);

		$newlist = array();
		foreach($list as $k=>$v){
			$k = trim($k);
			$newlist[ $k ] = $v;

			$c = new Country(array('id' => $v, 'name' => $k));

			$c->insert();

			unset($c);
		}


		$ug = new UserGroup();
		$ug->name = 'Manager';
		$ug->create_date = new DooDbExpression('NOW()');

		$ug->insert();

		echo 'success';
	}

	public function setup_root() {
		$u =  new User();
		
		$u->first_name = 'root';
		$u->last_name = 'root';
		$u->email = 'root@localhost';
		$u->username = 'admin';
		$u->password = '146dd4702a79b2f744ca434760bdf05a:03b5df464e6047f617ef5bad1af95331';
		$u->user_type = 'super_admin';
		$u->user_group = 'Manager';

		$u->insert();

		echo 'success';
	}
	
	public function upgrade_db() {
		try {
			$sql = "ALTER TABLE `".Doo::conf()->db_name."`.`gen_request` ADD COLUMN `fee_deno` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00  AFTER `product_description`,
					  ADD COLUMN `rate` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0  AFTER `fee_deno` , DROP FOREIGN KEY `fk_gen_request_user1`;";

			$this->db()->query($sql);
		} catch(PDOException $e) {
			//echo 'free_deno & rate fields already exist';
		}
		
		try {
			$sql = "ALTER TABLE `".Doo::conf()->db_name."`.`gen_request` DROP FOREIGN KEY `fk_gen_request_user1`";
			
			$this->db()->query($sql);
			
			$sql = "ALTER TABLE `".Doo::conf()->db_name."`.`gen_request` 
					  ADD CONSTRAINT `fk_gen_request_user1`
					  FOREIGN KEY (`request_by`)
					  REFERENCES `".Doo::conf()->db_name."`.`user` (`id`)
					  ON DELETE NO ACTION
					  ON UPDATE NO ACTION;";

			$this->db()->query($sql);
		} catch(PDOException $e) {
			//echo $e;
		}
		
		echo 'success upgrade_db';
	}
	
	public function upgrade_db2() {
		$pr = new PinRequest;
		$pr->status = 1;
		
		$pr = $pr->relate('PinRequestDelivered', array('select' => 'pin_request.create_date, pin_request_delivered.pins', 'asArray' => true));
		
		if (!is_null($pr)) {
			foreach ($pr as $i => $value) {
				$pins_array = explode(',', $value['pins']);
				
				for ($j = 0; $j < sizeof($pins_array); $j++) {
					$ps = new PinStatusLog();
					$ps->pin_id = $pins_array[$j];
					$ps->status = 1;
					$ps->redeem = 0;
					
					if ($ps->count() < 1) {
						try {
							$data_array = array('pin_id' => $pins_array[$j],
												'status' => 1,
												'redeem' => 0,
												'create_date' => $value['create_date']
												);

							$psl = new PinStatusLog($data_array);
							$psl->insert();

							unset($data_array);
							unset($psl);
						} catch(PDOException $e) {
							//echo $e;
						}
					}
					
					unset($ps);
				}
			}
		}
		
		echo 'success upgrade_db 2';
	}
	public function upgrade_db_exclusive() {
		try {
			$sql = "ALTER TABLE `".Doo::conf()->db_name."`.`gen_request` ADD COLUMN `exclusive` varchar(20);";

			$this->db()->query($sql);

			echo "Success upgrade exclusive field";
		} catch(PDOException $e) {
			echo $e->getMessage();
		}
	}
	
	public function upgrade_setup() {
		$sql = "CREATE TABLE IF NOT EXISTS `".Doo::conf()->db_name."`.`reseller_pin_setting` (
				  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
				  `reseller_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `product_id` MEDIUMINT(8) UNSIGNED NOT NULL ,
				  `qty_of_notify` INT(10) UNSIGNED NOT NULL DEFAULT 0 ,
				  `fee_deno` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 ,
				  `rate` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 ,
				  `notification_list` TINYTEXT NULL DEFAULT NULL ,
				  PRIMARY KEY (`id`) ,
				  INDEX `fk_reseller_pin_setting_reseller1` (`reseller_id` ASC) ,
				  INDEX `fk_reseller_pin_setting_product1` (`product_id` ASC) ,
				  CONSTRAINT `fk_reseller_pin_setting_reseller1`
					FOREIGN KEY (`reseller_id`)
					REFERENCES `".Doo::conf()->db_name."`.`reseller` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION,
				  CONSTRAINT `fk_reseller_pin_setting_product1`
					FOREIGN KEY (`product_id`)
					REFERENCES `".Doo::conf()->db_name."`.`product` (`id`)
					ON DELETE NO ACTION
					ON UPDATE NO ACTION)
				ENGINE = InnoDB
				DEFAULT CHARACTER SET = utf8
				COLLATE = utf8_general_ci;";
		
		$this->db()->query($sql);
		
		$l = new Log();
		$l->module = 'pinrequest_generatepin';
		
		$opt = array();
		$opt['select'] = 'item_id, logtime';
		$opt['asArray'] = true;
		
		$l = $l->find($opt);
		
		for ($i = 0; $i < sizeof($l); $i++) {
			$gr = new GenRequest();
			$gr->id = $l[$i]['item_id'];
			
			$gr = $gr->getOne(array('select' => 'pin_type', 'asArray' => true));
			
			if (isset($gr['pin_type'])) {
				$p = new Pin();
				$p->batch_id = $l[$i]['item_id'];
				
				$p = $p->find(array('select' => 'id', 'asArray' => true));
				
				for ($j = 0;  $j < sizeof($p); $j++) {
					$psl = new PinStatusLog();
					
					$psl->pin_id = $p[$j]['id'];
					$psl->redeem = 0;
					
					if ($gr['pin_type'] == 8) {
						$psl->status = 2;
					} else {
						$psl->status = 1;
					}
					
					if ($psl->count() < 1) {
						$data_array = array('pin_id' => $p[$j]['id'],
											'status' => $psl->status,
											'redeem' => 0,
											'create_date' => $l[$i]['logtime']
											);
						
						$ps = new PinStatusLog($data_array);
						$ps->insert();
						
						unset($data_array);
						unset($ps);
					}
					
					unset($psl);
				}
			}
			
			unset($gr);
		}
	
		echo 'upgrade_setup';
	}
}
?>