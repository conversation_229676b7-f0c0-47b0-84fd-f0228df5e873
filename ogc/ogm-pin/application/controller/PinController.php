<?php

class PinController extends AppController {

    public $autorender = false;
    public $status = array('active' => 1, 'inactive' => 0);
    public $redeem = array('yes' => 1, 'no' => 0);

    public function __construct() {
        parent::__construct();
        $this->vdata['nav'] = 'request';
        Doo::loadModel('Pin');
    }

    public function searchPin() {
        $this->autorender = true;

        $p = new Pin;

        $this->vdata['subnav'] = 'manage_pin';
        $this->vdata['status_list'] = $p->getStatusList();
    }

    public function listPin() {
        $this->autorender = true;

        $this->vdata['options'] = '';
        $this->vdata['export_options'] = '';
        $this->vdata['batch_id'] = '';

        if ($product_id = $this->getKeyParam('product-id')) {
            $product_id = intval($product_id);
            if ($product_id > 0) {
                $this->vdata['options'] = "+ '/product_id/$product_id'";
                $this->vdata['export_options'] = "/product_id/$product_id";
            }
        }

        if ($batch_id = $this->getKeyParam('batch-id')) {
            $batch_id = intval($batch_id);

            if ($batch_id > 0) {
                $this->vdata['options'] = "+ '/batch_id/$batch_id'";
                $this->vdata['batch_id'] = $batch_id;
                $this->vdata['export_options'] = "/batch_id/$batch_id";
            }
        }

        if ($status = $this->getKeyParam('status')) {
            $this->vdata['options'] .= "+ '/status/$status'";
            $this->vdata['export_options'] .= "/status/$status";
        }

        if ($redeem = $this->getKeyParam('redeem')) {
            $this->vdata['options'] .= "+ '/redeem/$redeem'";
            $this->vdata['export_options'] .= "/redeem/$redeem";
        }

        if ($serial = $this->getKeyParam('serial')) {
            $this->vdata['options'] .= "+ '/serial/$serial'";
            $this->vdata['export_options'] .= "/serial/$serial";
        }

        $this->vdata['subnav'] = 'manage_pin';
    }

    public function getListPin() {
        $p = new Pin;
        $pin = new Pin;
        $with_product_id = false;
        $export = false;
        $item_per_pg = 10;

        $opt = array();

        if (!empty($this->params['batch_id'])) {
            $id = intval($this->params['batch_id']);

            if ($id > 0) {
                $p->batch_id = $id;
                $pin->batch_id = $id;

                $gr = new GenRequest();
                $gr->id = $id;

                $gr = $gr->getOne(array('select' => 'request_by'));

                if ($gr->request_by == $this->auth->userID) {
                    $export = true;
                }
            }
        }

        if (!empty($this->params['product_id'])) {
            $id = intval($this->params['product_id']);
            if ($id > 0) {
                $opt['custom'] = " INNER JOIN gen_request AS gr ON (gr.id = pin.batch_id AND gr.product_id = '" . $id . "') ";

                $with_product_id = true;
            }
        }

        if (!empty($this->params['status'])) {
            $status = $this->params['status'];

            if (isset($this->status[$status])) {
                $p->status = $this->status[$status];
                $pin->status = $this->status[$status];
            }
        }

        if (!empty($this->params['redeem'])) {
            $redeem = $this->params['redeem'];

            if (isset($this->redeem[$redeem])) {
                $p->redeem = $this->redeem[$redeem];
                $pin->redeem = $this->redeem[$redeem];
            }
        }

        if (!empty($this->params['serial'])) {
            $serial = $this->params['serial'];
            $p->serial = $serial;
            $pin->serial = $serial;
        }

        $totalPage = $p->count($opt);
        $totalPage = ceil($totalPage / $item_per_pg);

        $pg = 0;

        if (isset($this->params['page'])) {
            $pg = intval($this->params['page']);

            if ($pg <= 0) {
                return 404;
            }

            $pg = ($pg - 1) * $item_per_pg;
        }

        $opt['select'] = 'pin.id, pin.batch_id, pin.serial, pin.pin, pin.status, pin.start_date, pin.end_date, pin.redeem';

        if ($with_product_id) {
            $opt['custom'] .= " ORDER BY pin.id DESC LIMIT $pg,$item_per_pg";
        } else {
            $opt['custom'] = " ORDER BY pin.id DESC LIMIT $pg,$item_per_pg";
        }

        $pin = $pin->find($opt);

        $request_by_array = array();

        for ($i = 0; $i < sizeof($pin); $i++) {
            if (!$this->checkPermit('PinController', 'viewPinNumber')) {
                if (isset($request_by_array[$pin[$i]->batch_id])) {
                    if ($this->auth->userID != $request_by_array[$pin[$i]->batch_id]) {
                        $pin[$i]->pin = '******';
                    }
                } else {
                    $gr = new GenRequest();
                    $gr->id = $pin[$i]->batch_id;

                    $gr = $gr->getOne(array('select' => 'request_by', 'asArray' => true));

                    $request_by_array[$pin[$i]->batch_id] = $gr['request_by'];

                    if ($this->auth->userID != $gr['request_by']) {
                        $pin[$i]->pin = '******';
                    }

                    unset($gr);
                }
            }
        }

        $this->toJSON(array('total_page' => intval($totalPage), 'pins' => $pin, 'export_allow' => $export), true);
    }

    public function pinInfo() {
        $this->autorender = true;
        $this->vdata['subnav'] = 'manage_pin';

        if (intval($this->params[0]) < 1) {
            return 404;
        }

        $p = new Pin;
        $p->id = (int) $this->params[0];

        $opt['select'] = 'pin.id, pin.batch_id, pin.serial, pin.pin, pin.status, pin.start_date, pin.end_date, pin.redeem, gen_request.request_by';

        $rs = $p->relate('GenRequest', $opt);
        $rs = $rs[0];

        if ($this->auth->userID != $rs->GenRequest->request_by) {
            if (!$this->checkPermit('PinController', 'viewPinNumber')) {
                $rs->pin = '******';
            }
        }

        $this->vdata['rs'] = $rs;
    }

    public function update() {
        if (!isset($_POST))
            return 404;

        $rules = array(
            'id' => array('dbExist', 'pin', 'id', 'Pin does not exist.'),
            'end_date' => array('date', 'yyyy/mm/dd', 'Invalid Expiry Date')
        );

        $v = new DooValidator;
        $v->checkMode = DooValidator::CHECK_SKIP;

        if ($err = $v->validate($_POST, $rules)) {
            $this->toJSON($err, true);

            return 400;
        }

        $p = new Pin();
        $p->id = $_POST['id'];

        $p = $p->getOne(array('select' => 'end_date'));

        if (strtotime($p->end_date . ' 23:59:59') >= strtotime($_POST['end_date'] . ' 23:59:59')) {
            $this->toJSON('Expiry Date has to been later than ' . $p->end_date, true);

            return 400;
        }

        unset($p);

        $_POST['extend'] = 1;
        
        // Do not allow change of Serial & Pin to prevent Pin value become ***** when expiry date is extended by Admin with no permission to view Pin
        unset($_POST['serial']);
        unset($_POST['pin']);
        
        $p = new Pin($_POST);
        $p->update();

        Doo::logger()->log('Update details of pin (ID ' . $_POST['id'] . ') - ' . json_encode($p->getDataArray()), $_POST['id'], 'pin');
    }

    public function activate() {
        if (isset($_POST['pins'])) {
            if (sizeof($_POST['pins']) > 0) {
                foreach ($_POST['pins'] as $id) {
                    $p = new Pin();
                    $p->id = $id;

                    $rs = $p->getOne(array('select' => 'status'));

                    if ($rs->status != 1) {
                        $p->status = 1;
                        $p->update();

                        $pin = new Pin();
                        $pin->id = $id;
                        $pin = $pin->getOne(array('select' => 'redeem', 'asArray' => true));

                        $psl = new PinStatusLog(array('pin_id' => $id, 'status' => $p->status, 'redeem' => $pin['redeem'], 'create_date' => new DooDbExpression('NOW()')));
                        $psl->insert();

                        $pr = new PinRemark();

                        $pr->remarks = $_POST['remarks'];
                        $pr->pin_id = $id;
                        $pr->create_by = $this->auth->userID;
                        $pr->create_date = new DooDbExpression('NOW()');
                        $pr->insert();

                        unset($pin);
                        unset($psl);
                        unset($pr);

                        Doo::logger()->log('Activated pin (ID ' . $id . ') - ' . json_encode($p->getDataArray()), $id, 'pin');
                    }

                    unset($p);
                }
            } else {
                $this->toJSON('Please select pin', true);

                return 400;
            }
        } else {
            $this->toJSON('Please select pin', true);

            return 400;
        }
    }

    public function deactivate() {
        if (isset($_POST['pins'])) {
            if (sizeof($_POST['pins']) > 0) {
                foreach ($_POST['pins'] as $id) {
                    $p = new Pin();
                    $p->id = $id;

                    $rs = $p->getOne(array('select' => 'status'));

                    if ($rs->status != 0) {
                        $p->status = 0;
                        $p->update();

                        $pin = new Pin();
                        $pin->id = $id;
                        $pin = $pin->getOne(array('select' => 'redeem', 'asArray' => true));

                        $psl = new PinStatusLog(array('pin_id' => $id, 'status' => $p->status, 'redeem' => $pin['redeem'], 'create_date' => new DooDbExpression('NOW()')));
                        $psl->insert();

                        $pr = new PinRemark();

                        $pr->remarks = $_POST['remarks'];
                        $pr->pin_id = $id;
                        $pr->create_by = $this->auth->userID;
                        $pr->create_date = new DooDbExpression('NOW()');
                        $pr->insert();

                        unset($pin);
                        unset($psl);
                        unset($pr);

                        Doo::logger()->log('Deactivated pin (ID ' . $id . ') - ' . json_encode($p->getDataArray()), $id, 'pin');
                    }

                    unset($p);
                }
            } else {
                $this->toJSON('Please select pin', true);

                return 400;
            }
        } else {
            $this->toJSON('Please select pin', true);

            return 400;
        }
    }

    public function exportAll() {
        ini_set('max_execution_time', 200);

        $status = null;
        $redeem = null;

        if (!empty($this->params['status'])) {
            $status = $this->params['status'];

            if (isset($this->status[$status])) {
                $status = $this->redeem[$status];
            } else {
                $status = null;
            }
        }

        if (!empty($this->params['redeem'])) {
            $redeem = $this->params['redeem'];

            if (isset($this->redeem[$redeem])) {
                $redeem = $this->redeem[$redeem];
            } else {
                $redeem = null;
            }
        }

        $p = new Pin;

        $file = $p->exportCSV(null, null, $status, $redeem, $this->auth->userID);

        $this->load()->download($file);

        unlink($file);

        Doo::logger()->log('Exported all pin', '0', 'pinrequest_export');
    }

    public function exportByBatch() {
        ini_set('max_execution_time', 200);

        $status = null;
        $redeem = null;

        $p = new Pin;

        if (!empty($this->params['batch_id'])) {
            $id = intval($this->params['batch_id']);

            if ($id < 1) {
                return 400;
            }
        }

        if (!empty($this->params['status'])) {
            $status = $this->params['status'];

            if (isset($this->status[$status])) {
                $status = $this->redeem[$status];
            } else {
                $status = null;
            }
        }

        if (!empty($this->params['redeem'])) {
            $redeem = $this->params['redeem'];

            if (isset($this->redeem[$redeem])) {
                $redeem = $this->redeem[$redeem];
            } else {
                $redeem = null;
            }
        }

        $file = $p->exportCSV($id, 'batch_id', $status, $redeem, $this->auth->userID);

        $this->load()->download($file);

        unlink($file);
        Doo::logger()->log('Exported pin of Batch - ' . $id, $id, 'pinrequest_export');
    }

    public function exportByProduct() {
        ini_set('max_execution_time', 200);

        $status = null;
        $redeem = null;

        $p = new Pin;

        if (!empty($this->params['product_id'])) {
            $id = intval($this->params['product_id']);

            if ($id < 1) {
                return 400;
            }
        }

        if (!empty($this->params['status'])) {
            $status = $this->params['status'];

            if (isset($this->status[$status])) {
                $status = $this->redeem[$status];
            } else {
                $status = null;
            }
        }

        if (!empty($this->params['redeem'])) {
            $redeem = $this->params['redeem'];

            if (isset($this->redeem[$redeem])) {
                $redeem = $this->redeem[$redeem];
            } else {
                $redeem = null;
            }
        }

        $file = $p->exportCSV($id, 'product_id', $status, $redeem, $this->auth->userID);

        $this->load()->download($file);

        unlink($file);
        Doo::logger()->log('Exported pin of Product - ' . $id, $id, 'pinrequest_export');
    }

    public function exportBySerial() {
        $status = null;
        $redeem = null;

        $p = new Pin;

        if (!empty($this->params['serial'])) {
            $id = $this->params['serial'];

            if (strlen($id) < 18) {
                return 400;
            }
        }

        $file = $p->exportCSV($id, 'serial', $status, $redeem, $this->auth->userID);

        $this->load()->download($file);

        unlink($file);
        Doo::logger()->log('Exported pin of serial - ' . $id, $id, 'pinrequest_export');
    }

    public function exportByBatchSelf() {
        ini_set('max_execution_time', 200);

        $status = null;
        $redeem = null;

        if (!empty($this->params['batch_id'])) {
            $id = intval($this->params['batch_id']);

            if ($id < 1) {
                return 400;
            }

            $gr = new GenRequest;
            $gr->request_by = $this->auth->userID;
            $gr->id = $id;

            if ($gr->count() < 1) {
                return 403;
            }
        }

        if (!empty($this->params['status'])) {
            $status = $this->params['status'];

            if (isset($this->status[$status])) {
                $status = $this->redeem[$status];
            } else {
                $status = null;
            }
        }

        if (!empty($this->params['redeem'])) {
            $redeem = $this->params['redeem'];

            if (isset($this->redeem[$redeem])) {
                $redeem = $this->redeem[$redeem];
            } else {
                $redeem = null;
            }
        }

        $p = new Pin;

        $file = $p->exportCSV($id, 'batch_id', $status, $redeem, $this->auth->userID);

        $this->load()->download($file);

        unlink($file);
        Doo::logger()->log('Exported pin of Batch - ' . $id, $id, 'pinrequest_export');
    }

    public function getListRemarks() {
        if (intval($this->params['id']) < 1) {
            return 404;
        }

        $pr = new PinRemark();

        $pr->pin_id = $this->params['id'];

        $numPerPage = 5;

        $totalPage = $pr->count();
        $totalPage = ceil($totalPage / $numPerPage);

        $pg = 0;

        if (isset($this->params['page'])) {
            $pg = intval($this->params['page']);

            if ($pg <= 0) {
                return 404;
            }

            $pg = ($pg - 1) * $numPerPage;
        }

        $opt['select'] = 'pin_remark.id, pin_remark.create_date, pin_remark.remarks, pin_remark.create_by, user.first_name, user.last_name';
        $opt['custom'] = "ORDER BY create_date DESC LIMIT $pg,$numPerPage";

        $remarks_result = $pr->relate('User', $opt);

        if (is_null($remarks_result)) {
            $remarks_result = array();
        }

        foreach ($remarks_result as $key => $remarks_value) {
            $remarks_result[$key]->create_date = date('M j, Y', strtotime($remarks_result[$key]->create_date));
        }

        $this->toJSON(array('total_page' => intval($totalPage), 'remarks' => $remarks_result), true, false, null);
    }

}
