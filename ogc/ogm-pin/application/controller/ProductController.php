<?php
class ProductController extends AppController{
	public $autorender = false;
	public $currency_list = array(	'AED' => 'AED - Emirati <PERSON>',
									'AFN' => 'AFN - Afghan Afghani',
									'ALL' => 'ALL - Albanian Lek',
									'AMD' => 'AMD - Armenian Dram',
									'ANG' => 'ANG - Dutch Guilder',
									'AOA' => 'AOA - Angolan Kwanza',
									'ARS' => 'ARS - Argentine Peso',
									'AUD' => 'AUD - Australian Dollar',
									'AWG' => 'AUD - Aruban or Dutch Guilder',
									'AZN' => 'AZN - Azerbaijani New Manat',
									'BAM' => 'BAM - Bosnian Convertible Marka',
									'BBD' => 'BBD - Barbadian or Bajan Dollar',
									'BDT' => 'BDT - Bangladeshi Taka',
									'BGN' => 'BGN - Bulgarian Lev',
									'BHD' => 'BHD - Bahraini Dinar',
									'BIF' => 'BIF - Burundian Franc',
									'BMD' => 'BMD - Bermudian Dollar',
									'BND' => 'BND - Bruneian Dollar',
									'BOB' => 'BOB - Bolivian Boliviano',
									'BRL' => 'BRL - Brazilian Real',
									'BSD' => 'BSD - Bahamian Dollar',
									'BTN' => 'BTN - Bhutanese Ngultrum',
									'BWP' => 'BWP - Botswana Pula',
									'BYR' => 'BYR - Belarusian Ruble',
									'BZD' => 'BZD - Belizean Dollar',
									'CAD' => 'CAD - Canadian Dollar',
									'CDF' => 'CDF - Congolese Franc',
									'CHF' => 'CHF - Swiss Franc',
									'CLP' => 'CLP - Chilean Peso',
									'CNY' => 'CNY - Chinese Yuan Renminbi',
									'COP' => 'COP - Colombian Peso',
									'CRC' => 'CRC - Costa Rican Colon',
									'CUC' => 'CUC - Cuban Convertible Peso',
									'CUP' => 'CUP - Cuban Peso',
									'CVE' => 'CVE - Cape Verdean Escudo',
									'CZK' => 'CZK - Czech Koruna',
									'DJF' => 'DJF - Djiboutian Franc',
									'DKK' => 'DKK - Danish Krone',
									'DOP' => 'DOP - Dominican Peso',
									'DZD' => 'DZD - Algerian Dinar',
									'EGP' => 'EGP - Egyptian Pound',
									'ERN' => 'ERN - Eritrean Nakfa',
									'ETB' => 'ETB - Ethiopian Birr',
									'EUR' => 'EUR - Euro',
									'FJD' => 'FJD - Fijian Dollar',
									'FKP' => 'FKP - Falkland Island Pound',
									'GBP' => 'GBP - British Pound',
									'GEL' => 'GEL - Georgian Lari',
									'GGP' => 'GGP - Guernsey Pound',
									'GHS' => 'GHS - Ghanaian Cedi',
									'GIP' => 'GIP - Gibraltar Pound',
									'GMD' => 'GMD - Gambian Dalasi',
									'GNF' => 'GNF - Guinean Franc',
									'GTQ' => 'GTQ - Guatemalan Quetzal',
									'GYD' => 'GYD - Guyanese Dollar',
									'HKD' => 'HKD - Hong Kong Dollar',
									'HNL' => 'HNL - Honduran Lempira',
									'HRK' => 'HRK - Croatian Kuna',
									'HTG' => 'HTG - Haitian Gourde',
									'HUF' => 'HUF - Hungarian Forint',
									'IDR' => 'IDR - Indonesian Rupiah',
									'ILS' => 'ILS - Israeli Shekel',
									'IMP' => 'IMP - Isle of Man Pound',
									'INR' => 'INR - Indian Rupee',
									'IQD' => 'IQD - Iraqi Dinar',
									'IRR' => 'IRR - Iranian Rial',
									'ISK' => 'ISK - Icelandic Krona',
									'JEP' => 'JEP - Jersey Pound',
									'JMD' => 'JMD - Jamaican Dollar',
									'JOD' => 'JOD - Jordanian Dinar',
									'JPY' => 'JPY - Japanese Yen',
									'KES' => 'KES - Kenyan Shilling',
									'KGS' => 'KGS - Kyrgyzstani Som',
									'KHR' => 'KHR - Cambodian Riel',
									'KMF' => 'KMF - Comoran Franc',
									'KPW' => 'KPW - North Korean Won',
									'KRW' => 'KRW - South Korean Won',
									'KWD' => 'KWD - Kuwaiti Dinar',
									'KYD' => 'KYD - Caymanian Dollar',
									'KZT' => 'KZT - Kazakhstani Tenge',
									'LAK' => 'LAK - Lao or Laotian Kip',
									'LBP' => 'LBP - Lebanese Pound',
									'LKR' => 'LKR - Sri Lankan Rupee',
									'LRD' => 'LRD - Liberian Dollar',
									'LSL' => 'LSL - Basotho Loti',
									'LTL' => 'LTL - Lithuanian Litas',
									'LVL' => 'LVL - Latvian Lat',
									'LYD' => 'LYD - Libyan Dinar',
									'MAD' => 'MAD - Moroccan Dirham',
									'MDL' => 'MDL - Moldovan Leu',
									'MGA' => 'MGA - Malagasy Ariary',
									'MKD' => 'MKD - Macedonian Denar',
									'MMK' => 'MMK - Burmese Kyat',
									'MNT' => 'MNT - Mongolian Tughrik',
									'MOP' => 'MOP - Macau Pataca',
									'MRO' => 'MRO - Mauritanian Ouguiya',
									'MUR' => 'MUR - Mauritian Rupee',
									'MVR' => 'MVR - Maldivian Rufiyaa',
									'MWK' => 'MWK - Malawian Kwacha',
									'MXN' => 'MXN - Mexican Peso',
									'MYR' => 'MYR - Malaysian Ringgit',
									'MZN' => 'MZN - Mozambican Metical',
									'NAD' => 'NAD - Namibian Dollar',
									'NGN' => 'NGN - Nigerian Naira',
									'NIO' => 'NIO - Nicaraguan Cordoba',
									'NOK' => 'NOK - Norwegian Krone',
									'NPR' => 'NPR - Nepalese Rupee',
									'NZD' => 'NZD - New Zealand Dollar',
									'OMR' => 'OMR - Omani Rial',
									'PAB' => 'PAB - Panamanian Balboa',
									'PEN' => 'PEN - Peruvian Nuevo Sol',
									'PGK' => 'PGK - Papua New Guinean Kina',
									'PHP' => 'PHP - Philippine Peso',
									'PKR' => 'PKR - Pakistani Rupee',
									'PLN' => 'PLN - Polish Zloty',
									'PYG' => 'PYG - Paraguayan Guarani',
									'QAR' => 'QAR - Qatari Riyal',
									'RON' => 'RON - Romanian New Leu',
									'RSD' => 'RSD - Serbian Dinar',
									'RUB' => 'RUB - Russian Ruble',
									'RWF' => 'RWF - Rwandan Franc',
									'SAR' => 'SAR - Saudi or Saudi Arabian Riyal',
									'SBD' => 'SBD - Solomon Islander Dollar',
									'SCR' => 'SCR - Seychellois Rupee',
									'SDG' => 'SDG - Sudanese Pound',
									'SEK' => 'SEK - Swedish Krona',
									'SGD' => 'SGD - Singapore Dollar',
									'SHP' => 'SHP - Saint Helenian Pound',
									'SLL' => 'SLL - Sierra Leonean Leone',
									'SOS' => 'SOS - Somali Shilling',
									'SPL' => 'SPL - Seborgan Luigino',
									'SRD' => 'SRD - Surinamese Dollar',
									'STD' => 'STD - Sao Tomean Dobra',
									'SVC' => 'SVC - Salvadoran Colon',
									'SYP' => 'SYP - Syrian Pound',
									'SZL' => 'SZL - Swazi Lilangeni',
									'THB' => 'THB - Thai Baht',
									'TJS' => 'TJS - Tajikistani Somoni',
									'TMT' => 'TMT - Turkmenistani Manat',
									'TND' => 'TND - Tunisian Dinar',
									'TOP' => 'TOP - Tongan Pa\'anga',
									'TRY' => 'TRY - Turkish Lira',
									'TTD' => 'TTD - Trinidadian Dollar',
									'TVD' => 'TVD - Tuvaluan Dollar',
									'TWD' => 'TWD - Taiwan New Dollar',
									'TZS' => 'TZS - Tanzanian Shilling',
									'UAH' => 'UAH - Ukrainian Hryvna',
									'UGX' => 'UGX - Ugandan Shilling',
									'USD' => 'USD - US Dollar',
									'UYU' => 'UYU - Uruguayan Peso',
									'UZS' => 'UZS - Uzbekistani Som',
									'VEF' => 'VEF - Venezuelan Bolivar Fuerte',
									'VND' => 'VND - Vietnamese Dong',
									'VUV' => 'VUV - Ni-Vanuatu Vatu',
									'WST' => 'WST - Samoan Tala',
									'XAF' => 'XAF - Central African CFA Franc BEAC',
									'XAG' => 'XAG - Silver Ounce',
									'XAU' => 'XAU - Gold Ounce',
									'XCD' => 'XCD - East Caribbean Dollar',
									'XDR' => 'XDR - IMF Special Drawing Rights',
									'XOF' => 'XOF - CFA Franc',
									'XPD' => 'XPD - Palladium Ounce',
									'XPF' => 'XPF - CFP Franc',
									'XPT' => 'XPT - Platinum Ounce',
									'YER' => 'YER - Yemeni Rial',
									'ZAR' => 'ZAR - South African Rand',
									'ZMK' => 'ZMK - Zambian Kwacha',
									'ZWD' => 'ZWD - Zimbabwean Dollar'
								);

	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'product';
		Doo::loadModel('Product');
	}
/*
	public function index() {
        $this->vdata['subnav'] = 'manage_product';
		$this->renderc($this->auth->group.'/product/list-all-product', $this->vdata);
	}
*/
	public function listProduct() {
		$this->autorender = true;

		$this->vdata['nav'] = 'product';
		$this->vdata['subnav'] = 'manage_product';
	}

	public function getListProduct() {
		$item_per_pg = 10;

		$p = new Product;
		$opt['where'] = 'active IN (0,1)';
		$totalPage = $p->count($opt);
		$totalPage = ceil($totalPage / $item_per_pg);

		$pg = 0;

		if (isset($this->params['page'])){
			$pg = intval($this->params['page']);
			if($pg <= 0){
				return 404;
			}
			$pg = ($pg - 1) * $item_per_pg;
		}

		$products = $p->limit("$pg,$item_per_pg", 'name', null, $opt);

		$this->toJSON(array('total_page' => intval($totalPage), 'products' => $products) , true);
	}

	public function listAllProduct() {
		if (empty($_GET['q'])) return;

		$c = new Product;

		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY name ASC LIMIT 50";
		$opt['select'] = 'id, name';
		$opt['where'] = ' active IN (0, 1) AND name LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');

		$rs = $c->find($opt);

		if (is_null($rs)) {
			$rs = array();
		}

		$this->toJSON($rs, true, true);
	}

	public function productInfo(){
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_product';

		if (intval($this->params[0]) < 1){
			return 404;
		}

		$p = new Product;

		$opt['where'] = "id = '" . (int)$this->params[0] . "'";
		$this->vdata['rs'] = $p->getOne($opt);
		$this->vdata['currency_list'] = $this->currency_list;
	}

	public function newProduct() {
		$this->vdata['subnav'] = 'new_product';
		$this->vdata['currency_list'] = $this->currency_list;

		$this->renderc('product/product-info', $this->vdata);
	}

	public function create() {
		$aFilter = array(
			'id' => FILTER_SANITIZE_NUMBER_INT,
			'name' => FILTER_SANITIZE_STRING,
			'active' => FILTER_SANITIZE_NUMBER_INT,
			'currency' => FILTER_SANITIZE_STRING,
			'deno' => array(
                'filter' => FILTER_SANITIZE_NUMBER_FLOAT,
                'flags' => FILTER_FLAG_ALLOW_FRACTION
            ),
			'barcode' => FILTER_SANITIZE_STRING,
			'description' => FILTER_SANITIZE_STRING,
		);

		$_POST = filter_var_array($_POST, $aFilter);
		foreach($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}

		$rules = array(
					'name' => array(
									array('dbNotExist', 'product', 'name', 'Product name already exists. Please use another name.'),
									array('maxlength', 45, 'Product name cannot be longer than 45 characters'),
									array('minlength', 2, 'Product name cannot be shorter than 2 characters')
								),
					'currency' => array(
									array('maxlength', 3, 'Currency must be 3 characters'),
									array('minlength', 3, 'Currency must be 3 characters')
								),
					'deno' => array(
									array('float', 2, 'Deno value is invalid'),
									array('lessThan', 999999999, 'Item value is out of range')
								),
					'barcode' => array(
									array('optional'),
									array('maxlength', 45, 'Bar Code cannot be longer than 45 characters')
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);

		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;

		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}

		if (isset($_POST['id']))
			unset($_POST['id']);

		$p = new Product($_POST);

		$p->create_date = new DooDbExpression('NOW()');
		$p->create_by = $this->auth->userID;

		$id = $p->insert();

		Doo::logger()->log('Added new product - ' . json_encode($p->getDataArray()), $id, 'product');

		return 201;
	}

	public function update() {
		$aFilter = array(
			'id' => FILTER_SANITIZE_NUMBER_INT,
			'name' => FILTER_SANITIZE_STRING,
			'active' => FILTER_SANITIZE_NUMBER_INT,
			'currency' => FILTER_SANITIZE_STRING,
            'deno' => array(
                'filter' => FILTER_SANITIZE_NUMBER_FLOAT,
                'flags' => FILTER_FLAG_ALLOW_FRACTION
            ),
			'barcode' => FILTER_SANITIZE_STRING,
			'description' => FILTER_SANITIZE_STRING,
		);

		$_POST = filter_var_array($_POST, $aFilter);
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}

		$rules = array(
					'id' => array('dbExist', 'product', 'id', 'Product does not exist. It might been deleted.'),
					'name' => array(
									//array('dbNotExist', 'product', 'name', 'Product name already exists. Please use another name.'),
									array('maxlength', 45, 'Product name cannot be longer than 45 characters'),
									array('minlength', 2, 'Product name cannot be shorter than 2 characters')
								),
					'currency' => array(
									array('maxlength', 3, 'Currency must be 3 characters'),
									array('minlength', 3, 'Currency must be 3 characters')
								),
					'deno' => array(
									array('float', 2, 'Item value is invalid'),
									array('lessThan', 999999999, 'Item value is out of range')
								),
					'barcode' => array(
									array('optional'),
									array('maxlength', 45, 'Bar Code cannot be longer than 45 characters')
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);

		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;

		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}

		$p = new Product($_POST);

		$p->update();

		Doo::logger()->log('Update product - ' . json_encode($p->getDataArray()), $p->id, 'product');
	}

	public function delete(){
		if (intval($this->params[0]) < 1) {
			return 404;
		}

		$p = new Product;
		$p->id = (int)$this->params[0];
		$p->active = 2;

		$p->update();

		$p = $p->getOne();
		Doo::logger()->log('Delete product - ' . json_encode($p->getDataArray()), $p->id, 'product');

		return '/product/list-product';
	}
}