<?php
class ReportController extends AppController{
	public $autorender = false;
	
	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'report';
	}
	
	public function searchReport() {
		$this->autorender = true;
		
		$p = new Product;
		$r = new Reseller;
		$c = new Client;
		
		$this->vdata['subnav'] = 'performance_report';
		
		$this->vdata['product_list'] = $p->getList();
		$this->vdata['product_list'][''] = 'Please Select';
		ksort($this->vdata['product_list']);
		
		$this->vdata['reseller_list'] = $r->getList();
		$this->vdata['reseller_list'][''] = 'Please Select';
		ksort($this->vdata['reseller_list']);
		
		$this->vdata['client_list'] = $c->getList();
		$this->vdata['client_list'][''] = 'Please Select';
		ksort($this->vdata['client_list']);
		
		$this->vdata['date_list'] = array('request_date' => 'Request Date');
	}
	
	public function getReport() {
		$this->autorender = true;
		
		$report_type = $this->getKeyParam('type');
		$render = $this->getKeyParam('view');
		
		switch($report_type) {
			case 'performance':
				$this->vdata['options'] = '';
				$this->vdata['export_options'] = '';
				$this->vdata['batch_id'] = '';
				
				$start_date = $this->getKeyParam('start-date') . ' 00:00:00';
				$end_date = $this->getKeyParam('end-date') . ' 23:59:59';
				
				$gr = new GenRequest();
				
				if ($product_id = $this->getKeyParam('product-id')) {
					$product_id = intval($product_id);
					
					if ($product_id > 0) {
						$gr->product_id = $product_id;
					}
				}
				
				if ($client_id = $this->getKeyParam('client-id')) {
					$client_id = intval($client_id);
					
					if ($client_id > 0) {
						$gr->client_id = $client_id;
					}
				}
				
				if ($reseller_id = $this->getKeyParam('reseller-id')) {
					$reseller_id = intval($reseller_id);

					if ($reseller_id > 0) {
						$gr->reseller_id = $reseller_id;
					}
				}
				
				$opt = array();
				$opt['select'] = 'id, client_id, reseller_id, product_id, pin_type';
				$opt['where'] = "request_date <= ? AND request_date >= ?";
				$opt['param'] = array($end_date, $start_date);
				$opt['asArray'] = true;
				
				$gr = $gr->find($opt);
				
				$c = new Client();
				$r = new Reseller();
				$rq = new RequestController();
				
				$pin_type_list = $rq->pintype_list;
				
				for ($cnt = 0; $cnt < sizeof($gr); $cnt++) {
					$gr[$cnt]['client_id'] = $c->getName($gr[$cnt]['client_id']);
					$gr[$cnt]['reseller_id'] = $r->getName($gr[$cnt]['reseller_id']);
					
					$p = new Product();
					$p->id = $gr[$cnt]['product_id'];
					
					$gr[$cnt]['product'] = $p->getOne(array('select' => 'name, currency, deno', 'asArray' => true));
					
					$pin = new Pin();
					$pin->batch_id = $gr[$cnt]['id'];
					
					$gr[$cnt]['pin_type'] = $pin_type_list[$gr[$cnt]['pin_type']];
					$gr[$cnt]['total_pin'] = $pin->count();
					$gr[$cnt]['sold'] = $pin->count(array('where' => 'status = 1'));
					$gr[$cnt]['used'] = $pin->count(array('where' => 'status = 1 AND redeem = 1'));
					$gr[$cnt]['available'] = $pin->count(array('where' => '((status = 2) OR (status = 1 AND redeem = 0))'));
					$gr[$cnt]['void'] = $pin->count(array('where' => 'status = 0 AND redeem = 0'));
					$gr[$cnt]['loss'] = $pin->count(array('where' => 'status = 0 AND redeem = 1'));

					unset($p);
					unset($pin);
				}
				
				$content = '<table class="report">
								<thead>
									<tr>
										<th>Client</th>
										<th>Reseller</th>
										<th align="right">Batch ID</th>
										<th>Pin Type</th>
										<th>Product</th>
										<th>Product Currency</th>
										<th>Product Deno</th>
										<th align="right">Total Pins</th>
										<th align="right">Sold</th>
										<th align="right">Used</th>
										<th align="right">Void</th>
										<th align="right">Loss</th>
									</tr>
								</thead>
								<tbody>';
				
				
				for ($i = 0; $i < sizeof($gr); $i++) {
					$content .= '	<tr class="report-row link" data-id="' . $gr[$i]['id'] .'">
										<td>' . $gr[$i]['client_id'] . '</td>
										<td>' . $gr[$i]['reseller_id'] . '</td>
										<td align="right">' . $gr[$i]['id'] . '</td>
										<td>' . $gr[$i]['pin_type'] . '</td>
										<td>' . $gr[$i]['product']['name'] . '</td>
										<td>' . $gr[$i]['product']['currency'] . '</td>
										<td align="right">' . $gr[$i]['product']['deno'] . '</td>
										<td align="right">' . $gr[$i]['total_pin'] . '</td>
										<td align="right">' . $gr[$i]['sold'] . '</td>
										<td align="right">' . $gr[$i]['used'] . '</td>
										<td align="right">' . $gr[$i]['void'] . '</td>
										<td align="right">' . $gr[$i]['loss'] . '</td>
									</tr>';
				}
				
				$content .=	'	</tbody>
							</table>';
				
				break;
			case 'batch':
				if ($batch_id = $this->getKeyParam('batch-id')) {
					$batch_id = intval($batch_id);
					
					if ($batch_id > 0) {
						$p = new Pin;
						$p->batch_id = $batch_id;

						$p = $p->find(array('asArray' => true));

						$array_size = sizeof($p);

						for ($i = 0; $i < $array_size; $i++) {
							if (!$this->checkPermit('PinController', 'viewPinNumber')) {
								$p[$i]['pin'] = '******';
							}

							if ($p[$i]['status'] == 0) {
								$p[$i]['status'] = 'In Active';
							} else if ($p[$i]['status'] == 1) {
								$p[$i]['status'] = 'Active';
							} else {
								$p[$i]['status'] = 'Pending';
							}

							if ($p[$i]['redeem'] == 1) {
								$p[$i]['redeem'] = 'true';
							} else {
								$p[$i]['redeem'] = 'false';
							}
						}
						
						$content = '<table class="report">
							<thead>
								<tr>
									<th align="right">Batch ID</th>
									<th align="right">Serial</th>
									<th align="right">Pin</th>
									<th align="center">Status</th>
									<th align="left">Start Date</th>
									<th align="left">Expiry Date</th>
									<th align="center">Redeem</th>
								</tr>
							</thead>
							<tbody>';

						for ($i = 0; $i < sizeof($p); $i++) {
							$content .= '<tr class="report-row">
											<td align="right">' . $p[$i]['batch_id'] . '</td>
											<td align="right">' . $p[$i]['serial'] . '</td>
											<td align="right">' . $p[$i]['pin'] . '</td>
											<td align="center">' . $p[$i]['status'] . '</td>
											<td>' . $p[$i]['start_date'] . '</td>
											<td>' . $p[$i]['end_date'] . '</td>
											<td align="center">' . $p[$i]['redeem'] . '</td>
										</tr>';
						}

						$content .= '</thead>
								</table>';
					}
				}
				
				break;
		}
		
		if ($render == 'no') {
			$this->autorender = false;
			
			echo $content;
			
			return;
		}
		
		$this->vdata['report_data'] = $content;
		$this->vdata['subnav'] = 'performance_report';
	}
	
	public function getReportPin() {
		$this->autorender = true;
		
		$render = $this->getKeyParam('view');
		$report_type = $this->getKeyParam('type');
		/*
		if ($render == 'no') {
			$this->autorender = false;
			
			echo 'abc';
			return;
		}
		*/
		if ($batch_id = $this->getKeyParam('batch-id')) {
			$batch_id = intval($batch_id);
			
			if ($batch_id > 0) {
				$p = new Pin;
				$p->batch_id = $batch_id;
				
				$p = $p->find(array('asArray' => true));
				
				$array_size = sizeof($p);
				
				for ($i = 0; $i < $array_size; $i++) {
					if (!$this->checkPermit('PinController', 'viewPinNumber')) {
						$p[$i]['pin'] = '******';
					}
					
					if ($p[$i]['status'] == 0) {
						$p[$i]['status'] = 'In Active';
					} else if ($p[$i]['status'] == 1) {
						$p[$i]['status'] = 'Active';
					} else {
						$p[$i]['status'] = 'Pending';
					}
					
					if ($p[$i]['redeem'] == 1) {
						$p[$i]['redeem'] = 'true';
					} else {
						$p[$i]['redeem'] = 'false';
					}
				}
				
				$this->vdata['report_data'] = $p;
			}
		}
		
		$this->vdata['subnav'] = 'performance_report';
	}
}