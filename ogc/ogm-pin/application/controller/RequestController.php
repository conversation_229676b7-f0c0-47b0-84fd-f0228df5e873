<?php
class RequestController extends AppController{
	public $autorender = false;
	public $pintype_list = array(	'0' => 'Direct Selling (0)',
									'1' => 'Consignment (1)',
									'3' => 'Replacement (3)',
									'6' => 'COD (6)',
									'8' => 'API (8)',
									'9' => 'Free Promo (9)'
								);

	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'request';
		Doo::loadModel('GenRequest');
	}
	
	public function listRequest() {
		$this->autorender = true;
		$this->vdata['nav'] = 'request';
		$this->vdata['subnav'] = 'manage_request';
		
		$gr = new GenRequest();
		
		$this->vdata['status_list'] = $gr->getStatusList();
	}
	
	public function getListRequest() {
		$item_per_pg = 10;
		$g = new GenRequest();

		$status = 'All';

		if (isset($this->params['status']) && $this->params['status'] != 'All') {
			$status = $this->params['status'];
			$g->status = $status;
		}

		$totalPage = $g->count();
		$totalPage = ceil($totalPage / $item_per_pg);

		$pg = 0;
		
		if (isset($this->params['page'])) {
			$pg = intval($this->params['page']);
			
			if ($pg <= 0) {
				return 404;
			}

			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$requests = $g->limit("$pg,$item_per_pg", '', 'request_date');
		
		if (sizeof($requests) > 0) {
			$p = new Product;
			$r = new Reseller;
			$c = new Client;
			
			foreach ($requests as &$v) {
				$v->product = $p->getName($v->product_id);
				$v->reseller = $r->getName($v->reseller_id);
				$v->client = $c->getName($v->client_id);
				
				if ($v->status == 'Approved') {
					$pi = new Pin();
					$pi->batch_id = $v->id;
					$pi->status = 1;

					$v->active_pins = $pi->count();

					unset($pi);
					
					$pi = new Pin();
					$pi->batch_id = $v->id;
					$pi->status = 2;
					
					$v->pending_pins = $pi->count();

					unset($pi);
					
					$pi = new Pin();
					$pi->batch_id = $v->id;
					$pi->redeem = 1;
					
					$v->redeem_pins = $pi->count();
					unset($pi);
				}
			}
		}
		
		$this->toJSON(array('total_page' => intval($totalPage), 'requests' => $requests, 'auth' => $this->auth->userID) , true);
	}
	
	public function listAllRequest() {
		if (empty($_GET['q'])) return;

		$r = new GenRequest;

		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY title ASC LIMIT 50";
		$opt['select'] = 'id, title';
		$opt['where'] = ' title LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');

		$rs = $r->find($opt);

		if (is_null($rs)) {
			$rs = array();
		}

		$this->toJSON($rs, true, true);
	}
	
	public function requestInfo() {
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_request';
		
		if (intval($this->params[0]) < 1){
			return 404;
		}
		
		$g = new GenRequest;
		$opt['where'] = "id = '" . $this->params[0] . "'";
		$this->vdata['rs'] = $g->getOne($opt);
		
		$p = new Product;
		$r = new Reseller;
		$c = new Client;
		$ct = new Country;
		
		$this->vdata['auth'] = $this->auth->userID;
		$this->vdata['product_list'] = $p->getList();
		$this->vdata['reseller_list'] = $r->getList();
		$this->vdata['client_list'] = $c->getList();
		$this->vdata['country_list'] = $ct->getList();
		$this->vdata['pintype_list'] = $this->pintype_list;
	}
	
	public function newRequest() {
		$this->vdata['subnav'] = 'new_request';
		
		$p = new Product;
		$r = new Reseller;
		$c = new Client;
		$ct = new Country;

		$this->vdata['product_list'] = $p->getList(1);
		$this->vdata['reseller_list'] = $r->getList(1);
		$this->vdata['client_list'] = $c->getList(1);
		$this->vdata['country_list'] = $ct->getList();
		$this->vdata['pintype_list'] = $this->pintype_list;
		$this->renderc('request/request-info', $this->vdata);
	}

	public function create() {
		foreach($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
					'title' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Title can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Title cannot be longer than 65 characters'),
									array('minlength', 2, 'Title cannot be shorter than 2 characters')
								),
					'quantity' => array(
									array('max', 99999, 'Pin Request quantity cannot be more than 99999'),
									array('min', 1, 'Pin Request quantity cannot be less than 1')
								),
					'product_id' => array('dbExist', 'product', 'id', 'Please select a Product.'),
					'reseller_id' => array('dbExist', 'reseller', 'id', 'Please select a Reseller.'),
					'client_id' => array('dbExist', 'client', 'id', 'Please select a Client.'),
					'start_date' => array('date', 'yyyy/mm/dd', 'Invalid Start Date'),
					'end_date' => array('date', 'yyyy/mm/dd', 'Invalid Expiry Date'),
					'description' => array('maxlength', 1000, 'Description cannot be longer than 1000 characters'),
					'exclusive' => array(array('maxlength', 20, 'Exclusive cannot be longer than 20 characters'), array('optional'))
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);
			
			return 400;
		}
		
		if (isset($_POST['id']))
			unset($_POST['id']);
		
		$p = new Product();
		$p->id = $_POST['product_id'];
		$p = $p->getOne(array('select' => 'name, currency, deno, barcode, description'));

		$_POST['product_name'] = $p->name;
		$_POST['currency'] = $p->currency;
		$_POST['deno'] = $p->deno;
		$_POST['barcode'] = $p->barcode;
		$_POST['product_description'] = $p->description;
		$_POST['fee_deno'] = 0.00;
		$_POST['rate'] = 0;
		
		unset($p);
		
		$rps = new ResellerPinSetting;
		$rps->reseller_id = $_POST['reseller_id'];
		$rps->product_id = $_POST['product_id'];
		
		if ($rps->count() > 0) {
			$rps = $rps->getOne(array('select' => 'fee_deno, rate', 'asArray' => true));
			
			$_POST['fee_deno'] = $rps['fee_deno'];
			$_POST['rate'] = $rps['rate'];
		}
		
		unset($rps);
		
		$g = new GenRequest($_POST);
		
		$g->request_date = new DooDbExpression('NOW()');
		$g->status = 'Pending';
		$g->request_by = $this->auth->userID;
		$id = $g->insert();

		Doo::logger()->log('Added new pin request - ' . json_encode($g->getDataArray()), $id, 'pinrequest');                     

		$u = new User();
		$u->id = $this->auth->userID;

		$u = $u->getOne(array('select' => 'email'));
		
		$mail = new DooMailer();
		$mail->addTo($u->email);
		$mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
		$mail->setSubject('Request PIN');
		$mail->setBodyText('Your Pin request (' . $_POST['title'] . ') have been successfully created. You will receive a notification on the result of your request.');
		$mail->send();
		
		return 201;
	}
	
	public function updateSelf() {
		return $this->updateRequest();
	}
	
	public function update() {
		return $this->updateRequest();
	}
	
	private function updateRequest() {
		if (!isset($_POST))
			return 404;
		
		$aFilter = array(
			'id' => FILTER_SANITIZE_NUMBER_INT,
			'title' => FILTER_SANITIZE_STRING,
			'quantity' => FILTER_SANITIZE_NUMBER_FLOAT,
			'product_id' => FILTER_SANITIZE_NUMBER_INT,
			'reseller_id' => FILTER_SANITIZE_NUMBER_INT,
			'country_id' => FILTER_SANITIZE_NUMBER_INT,
			'client_id' => FILTER_SANITIZE_NUMBER_INT,
			'pin_type' => FILTER_SANITIZE_NUMBER_INT,
			'description' => FILTER_SANITIZE_STRING,
			'start_date' => FILTER_SANITIZE_NUMBER_FLOAT,
			'end_date' => FILTER_SANITIZE_NUMBER_FLOAT,
			'exclusive' => FILTER_SANITIZE_STRING
		);

		$_POST = filter_var_array($_POST, $aFilter);
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
					'id' => array('dbExist', 'gen_request', 'id', 'Pin Request does not exist. It might been deleted.'),
					'title' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Title can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Title cannot be longer than 65 characters'),
									array('minlength', 2, 'Title cannot be shorter than 2 characters')
								),
					'quantity' => array(
									array('max', 99999, 'Pin Request quantity cannot be more than 99999'),
									array('min', 1, 'Pin Request quantity cannot be less than 1')
								),
					'product_id' => array('dbExist', 'product', 'id', 'Please select a Product.'),
					'reseller_id' => array('dbExist', 'reseller', 'id', 'Please select a Reseller.'),
					'client_id' => array('dbExist', 'client', 'id', 'Please select a Client.'),
					'start_date' => array('date', 'yyyy/mm/dd', 'Invalid Start Date'),
					'end_date' => array('date', 'yyyy/mm/dd', 'Invalid Expiry Date'),
					'description' => array('maxlength', 1000, 'Description cannot be longer than 1000 characters'),
					'exclusive' => array(array('maxlength', 20, 'Exclusive cannot be longer than 20 characters'), array('optional'))
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);
			
			return 400;
		}
		
		$g = new GenRequest();
		$g->id = $_POST['id'];

		$rs = $g->getOne(array('asArray' => true, 'select' => 'status, request_by'));

		unset($g);
		
		if ($rs['status'] == 'Pending') {
			$p = new Product();
			$p->id = $_POST['product_id'];

			$p = $p->getOne(array('select' => 'name, currency, deno, barcode, description'));

			$_POST['product_name'] = $p->name;
			$_POST['currency'] = $p->currency;
			$_POST['deno'] = $p->deno;
			$_POST['barcode'] = $p->barcode;
			$_POST['product_description'] = $p->description;
			$_POST['fee_deno'] = 0.00;
			$_POST['rate'] = 0;
			
			unset($p);
			
			$rps = new ResellerPinSetting;
			$rps->reseller_id = $_POST['reseller_id'];
			$rps->product_id = $_POST['product_id'];
			
			if ($rps->count() > 0) {
				$rps = $rps->getOne(array('select' => 'fee_deno, rate', 'asArray' => true));
				
				$_POST['fee_deno'] = $rps['fee_deno'];
				$_POST['rate'] = $rps['rate'];
			}
			
			unset($rps);
			
			$g = new GenRequest($_POST);
			$g->update();
			
			Doo::logger()->log('Update pin request - ' . json_encode($g->getDataArray()), $g->id, 'pinrequest');
			
			$u = new User();
			$u->id = $rs['request_by'];

			$u = $u->getOne(array('select' => 'email'));
			
			$mail = new DooMailer();
			$mail->addTo($u->email);
			$mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
			$mail->setSubject('Request PIN');
			$mail->setBodyText('Please note that your Pin request (' . $_POST['title'] . ') information has been altered.');
			$mail->send();
			
			return;
		} else {
			$this->toJSON('Pin Request cannot be edit', true);
			
			return 400;
		}
	}
	
	public function approveRequest() {
		$id = intval($this->params['id']);
		
		if ($id < 1) return 404;
		
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array('remarks' => array(
									array('optional'),
									array('maxlength', 1000, 'Remarks cannot be longer than 1000 characters')
								)
					);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$g = new GenRequest();
		$g->id = $id;
		
		$g = $g->getOne(array('select' => 'request_by'));
		$request_by = $g->request_by;
		
		unset($g);
		
		$g = new GenRequest();
		$p = new Pin();
		$gr = new GenRequestRemark();

		ini_set('max_execution_time', 200);

		$this->db()->beginTransaction();
		
		try {
			$quantity = $p->generatePin($id);

			$g->id = $id;
			$g->status = 'Approved';
			$g->approve_by = $this->auth->userID;
			$g->update();

			$gr->remarks = $_POST['remarks'];
			$gr->gen_request_id = $id;
			$gr->create_by = $this->auth->userID;
			$gr->create_date = new DooDbExpression('NOW()');
			$gr->insert();

			$this->db()->commit();

			Doo::logger()->log('Approve pin request - ' . json_encode($g->getDataArray()), $g->id, 'pinrequest');
			Doo::logger()->log('Added '. $quantity .' pins for batch '. $g->id, $g->id, 'pinrequest_generatepin');

			$u = new User();
			$u->id = $request_by;
			$u = $u->getOne(array('select' => 'email'));
			
			unset($g);
			unset($gr);
			unset($p);
			
			$g = new GenRequest();
			$g->id = $id;
			
			$g = $g->getOne(array('select' => 'title'));
			
			$mail = new DooMailer();
			$mail->addTo($u->email);
			$mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
			$mail->setSubject('PIN Request Approved');
			$mail->setBodyText('Your Pin request (' . $g->title . ') has been approved. Thank you.');
			$mail->send();
		} catch(PDOException $e) {
			$this->db()->rollBack();
			$this->toJSON("Pin Generation Error", true);
			return 400;
		}
	}
	
	public function rejectRequest() {
		$id = intval($this->params['id']);
		if ($id <= 0) return 404;

		foreach ($_POST as $k => $v) {
			$_POST[$k] = trim($v);
		}
		
		$rules = array('remarks' => array(
									array('optional'),
									array('maxlength', 1000, 'Remarks cannot be longer than 1000 characters')
								)
					);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$g = new GenRequest();
		$g->id = $id;
		
		$g = $g->getOne(array('select' => 'request_by'));
		$request_by = $g->request_by;
		
		unset($g);
		
		$g = new GenRequest();
		$gr = new GenRequestRemark();
		
		$this->db()->beginTransaction();
		
		try {
			$g->id = $id;
			$g->status = 'Cancelled';
			$g->update();
			
			$gr->remarks = $_POST['remarks'];
			$gr->gen_request_id = $id;
			$gr->create_by = $this->auth->userID;
			$gr->create_date = new DooDbExpression('NOW()');
			$gr->insert();
			
			$this->db()->commit();
			
			Doo::logger()->log('Reject pin request - ' . json_encode($g->getDataArray()), $g->id, 'pinrequest');
			
			$u = new User();
			$u->id = $request_by;
			$u = $u->getOne(array('select' => 'email'));
			
			unset($g);
			unset($gr);
			
			$g = new GenRequest();
			$g->id = $id;
			
			$g = $g->getOne(array('select' => 'title'));
			
			$mail = new DooMailer();
			$mail->addTo($u->email);
			$mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
			$mail->setSubject('PIN Request Rejected');
			$mail->setBodyText('Your Pin request (' . $g->title . ') has been rejected. Thank you.');
			$mail->send();
		} catch(PDOException $e) {
			$this->db()->rollBack();
			$this->toJSON("Pin Generation Reject Error", true);
			return 400;
		}
	}
	
	public function deleteSelf() {
		return $this->deleteRequest();
	}
	
	public function delete() {
		return $this->deleteRequest();
	}
	
	private function deleteRequest() {
		if (intval($this->params['id']) < 1) {
			return 404;
		}
		
		$p = new GenRequest();
		$p->status = 'Pending';
		$p->id = (int)$this->params['id'];
		
		if ($p->count() < 1) return 404;
		
		unset($p);
		
		$p = new GenRequest();
		$gr = new GenRequestRemark();
		
		$this->db()->beginTransaction();
		
		try {
			$p->id = (int)$this->params['id'];
			$p->status = 'Deleted';
			
			$p->update();
			
			$gr->remark = $_POST['remarks'];
			$gr->gen_request_id = $p->id;
			$gr->create_by = $this->auth->userID;
			$gr->create_date = new DooDbExpression('NOW()');
			$gr->insert();

			$this->db()->commit();
			
			$p = $p->getOne();
			Doo::logger()->log('Delete pin request - ' . json_encode($p->getDataArray()), $p->id, 'pinrequest');
		} catch(PDOException $e) {
			$this->db()->rollBack();
			$this->toJSON("Delete Pin Generation Error", true);
			
			return 400;
		}
	}
	
	public function getListRemarks() {
		if (intval($this->params['id']) < 1) {
			return 404;
		}
		
		$grr = new GenRequestRemark();
		$grr->gen_request_id = $this->params['id'];
		
		$numPerPage = 5;
		
		$totalPage = $grr->count();
		$totalPage = ceil($totalPage / $numPerPage);
		
		$pg = 0;
		
		if (isset($this->params['page'])){
			$pg = intval($this->params['page']);
			
			if ($pg <= 0) {
				return 404;
			}
			
			$pg = ($pg - 1) * $numPerPage;
		}
		
		$opt['select'] = 'gen_request_remark.id, gen_request_remark.create_date, gen_request_remark.remarks, gen_request_remark.create_by, user.first_name, user.last_name';
		$opt['custom'] = "ORDER BY create_date DESC LIMIT $pg,$numPerPage";
		
		$remarks_result = $grr->relate('User', $opt);
		
		if (is_null($remarks_result)) {
			$remarks_result = array();
		}
		
		foreach ($remarks_result as $key => $remarks_value) {
			$remarks_result[$key]->create_date = date('M j, Y', strtotime($remarks_result[$key]->create_date));
		}
		
		$this->toJSON(array('total_page' => intval($totalPage), 'remarks' => $remarks_result) , true, false, null);
	}
}