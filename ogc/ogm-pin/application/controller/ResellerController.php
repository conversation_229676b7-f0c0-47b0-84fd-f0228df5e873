<?php
class ResellerController extends AppController{
	public $autorender = false;
	
	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'reseller';
		Doo::loadModel('Reseller');
	}
	
	public function listReseller() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'reseller';
		$this->vdata['subnav'] = 'manage_reseller';
	}
	
	public function getListReseller() {
		$item_per_pg = 10;
		
		$r = new Reseller;
		
		$opt['where'] = 'active IN (0,1)';
		$totalPage = $r->count($opt);
		$totalPage = ceil($totalPage / $item_per_pg);
		
		$pg = 0;
		
		if (isset($this->params['page'])) {
			$pg = intval($this->params['page']);
			
			if ($pg <= 0){
				return 404;
			}
			
			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$resellers = $r->limit("$pg,$item_per_pg", 'name', null, $opt);
		
		$this->toJSON(array('total_page' => intval($totalPage), 'resellers' => $resellers) , true);
	}
	
	public function listAllReseller() {
		if (empty($_GET['q'])) return;
		
		$c = new Reseller;
		
		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY name ASC LIMIT 50";
		$opt['select'] = 'reseller.id, name';
		$opt['where'] = ' active IN (0, 1) AND name LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');
		
		$rs = $c->find($opt);
		
		if (is_null($rs)) {
			$rs = array();
		}
		
		$this->toJSON($rs, true, true);
	}
	
	public function resellerInfo() {
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_reseller';
		
		if (intval($this->params[0]) < 1){
			return 404;
		}
		
		$r = new Reseller;
		$r->id = $this->params[0];
		
		$p = new Product;
		$opt =  array();
		$opt['select'] = 'id, name';
		$opt['where'] = 'active IN (0,1)';
		$opt['asArray'] = true;
		$opt['order'] = 'name';
		
		$p = $p->find($opt);
		
		$this->vdata['product_list'] = array('' => 'Please Select');
		
		foreach($p as $num => $product_info) {
			$this->vdata['product_list'][$product_info['id']] = $product_info['name'];
		}
		
		$this->vdata['rs'] = $r->getOne();
	}
	
	public function newReseller() {
		$this->vdata['subnav'] = 'new_reseller';
		
		$this->renderc('reseller/reseller-info', $this->vdata);
	}
	
	public function create() {
		foreach($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
					'name' => array(
									array('dbNotExist', 'reseller', 'name', 'Reseller name already exists. Please use another name.'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Reseller name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Reseller name cannot be longer than 65 characters'),
									array('minlength', 2, 'Reseller name cannot be shorter than 2 characters')
								),
					'email' => array(
									array('email'),
									array('maxlength', 65, 'Email cannot be longer than 65 characters'),
									array('minlength', 3)
								),
					'tel' => array(
									array('maxlength', 30, 'Telephone cannot be longer than 30 characters'),
									array('minlength', 3, 'Telephone cannot be shorter than 3 characters')
								),
					'reseller_code' => array(
									array('alphaNumeric', 'Reseller Code  can only consist letters & number'),
									array('maxlength', 16, 'Reseller Code cannot be longer than 16 characters'),
									array('minlength', 3, 'Reseller Code cannot be shorter than 3 characters')
								),
					'verification_url' => array(
									array('url', 'Invalid Verification URL'),
									array('maxlength', 255, 'Verification URL cannot be longer than 255 characters'),
									array('minlength', 5)
								),
					'secret_key' => array(
									array('alphaNumeric', 'Secret Key can only consist letters & number'),
									array('maxlength', 65, 'Secret Key cannot be longer than 45 characters'),
									array('minlength', 6, 'Secret Key cannot be shorter than 6 characters')
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);            
			return 400;
		}
		
		if (isset($_POST['id']))
			unset($_POST['id']);
		
		$r = new Reseller($_POST);
		$r->create_date = new DooDbExpression('NOW()');
		$r->create_by = $this->auth->userID;
		
		$id = $r->insert();
		
		Doo::logger()->log('Added new reseller - ' . json_encode($r->getDataArray()), $id, 'reseller');
		
		return 201;
	}
	
	public function update() {
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
					'id' => array('dbExist', 'reseller', 'id', 'Reseller does not exist. It might been deleted.'),
					'name' => array(
									//array('dbNotExist', 'reseller', 'name', 'Reseller name already exist. Please use another name.'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Reseller name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'Reseller name cannot be longer than 10 characters'),
									array('minlength', 2, 'Reseller name cannot be shorter than 2 characters')
								),
					'email' => array(
									array('email'),
									array('maxlength', 65, 'Email cannot be longer than 65 characters'),
									array('minlength', 3)
								),
					'tel' => array(
									array('maxlength', 30, 'Telephone cannot be longer than 30 characters'),
									array('minlength', 3, 'Telephone cannot be shorter than 3 characters')
								),
					'reseller_code' => array(
									array('alphaNumeric', 'Reseller Code  can only consist letters & number'),
									array('maxlength', 16, 'Reseller Code cannot be longer than 16 characters'),
									array('minlength', 3, 'Reseller Code cannot be shorter than 3 characters')
								),
					'secret_key' => array(
									array('alphaNumeric', 'Secret Key can only consist letters & number'),
									array('maxlength', 65, 'Secret Key cannot be longer than 45 characters'),
									array('minlength', 6, 'Secret Key cannot be shorter than 6 characters')
								),
					'verification_url' => array(
									array('url', 'Invalid Verification URL'),
									array('maxlength', 255, 'Verification URL cannot be longer than 255 characters'),
									array('minlength', 5)
								),
					'description' => array(
									array('optional'),
									array('maxlength', 1000, 'Description cannot be longer than 1000 characters')
								)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$r = new Reseller($_POST);
		
		$r->update();
		
		Doo::logger()->log('Update details of reseller (ID '. $r->id .') - ' . json_encode($r->getDataArray()), $r->id, 'reseller');
	}
    
	public function delete() {
		if (intval($this->params[0]) < 1) {
			return 404;
		}
		
		$r = new Reseller;
		$r->id = (int)$this->params[0];
		$r->active = 2;
		
		$r->update();
		
		Doo::logger()->log('Delete reseller - ' . json_encode($r->getDataArray()), $r->id, 'reseller');
		
		return '/reseller/list-reseller';
	}
	
	public function getListNotification() {
		if (intval($this->params['id']) < 1) {
			return 404;
		}
		
		$rps = new ResellerPinSetting();
		$rps->reseller_id = $this->params['id'];
		
		$numPerPage = 5;
		
		$totalPage = $rps->count();
		$totalPage = ceil($totalPage / $numPerPage);
		
		$pg = 0;
		
		if (isset($this->params['page'])){
			$pg = intval($this->params['page']);
			
			if ($pg <= 0) {
				return 404;
			}
			
			$pg = ($pg - 1) * $numPerPage;
		}
		
		$opt['select'] = 'reseller_pin_setting.*, product.name';
		$opt['custom'] = "ORDER BY product.name DESC LIMIT $pg,$numPerPage";
		
		$rps = $rps->relate('Product', $opt);
		
		if (is_null($rps)) {
			$rps = array();
		}
		
		$this->toJSON(array('total_page' => intval($totalPage), 'notify' => $rps) , true, false, null);
	}
	
	public function createNotification() {
		$rules = array(
					'reseller_id' => array('dbExist', 'reseller', 'id', 'Reseller does not exist. It might been deleted.'),
					'product_id' => array('dbExist', 'product', 'id', 'Product does not exist. It might been deleted.'),
					'fee_deno' => array(
									array('float', 2, 'Fee value is invalid'),
									array('lessThan', 999999999, 'Fee value is out of range')
								),
					'qty_of_notify' => array(
									array('integer', 'Quantity value is invalid'),
									array('lessThan', 99999999, 'Item value is out of range'),
									array('greaterThanOrEqual', 0, 'Item value is out of range')
								),
					'rate' => array(
									array('integer', 'Quantity value is invalid'),
									array('greaterThanOrEqual', 0, 'Item value is out of range')
								)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		if (isset($_POST['notify']) && sizeof($_POST['notify']) > 0) {
			$_POST['notification_list'] = implode(',', $_POST['notify']);
		}
		
		$rps = new ResellerPinSetting();
		$rps->reseller_id = $_POST['reseller_id'];
		$rps->product_id = $_POST['product_id'];
		
		$rps->delete();
		
		$rps = new ResellerPinSetting($_POST);
		$id = $rps->insert();
		
		Doo::logger()->log('Added/Update reseller notification - ' . json_encode($rps->getDataArray()), $id, 'reseller_notification');
		
		return;
	}
	
	public function getNotificationByProductId() {
		if (intval($this->params['product_id']) < 0 || intval($this->params['reseller_id']) < 1) {
			return 400;
		}
		
		$rps = new ResellerPinSetting;
		
		$rps->reseller_id = $this->params['reseller_id'];
		$rps->product_id = $this->params['product_id'];
		
		$rps = $rps->getOne();
		
		$this->toJSON($rps, true, true);
	
	}
	
	public function deleteNotification() {
		if (intval($this->params['id']) < 0) {
			return 400;
		}
		
		$rps = new ResellerPinSetting;
		$rps->id = $this->params['id'];
		
		$rps->delete();
		
		Doo::logger()->log('Delete reseller notification - ' . json_encode($rps->getDataArray()), $rps->id, 'reseller_notification');
		
		return;
	}
	
	public function getNotificationById() {
		if (intval($this->params['id']) < 1) {
			return 400;
		}
		
		$rps = new ResellerPinSetting;
		$rps->id = $this->params['id'];
		
		$rps = $rps->getOne(array('asArray' => true));
		
		$notify = array();
		$unnotify = array();
		
		$opt = array(	'select' => 'id, first_name, last_name', 
						'where' => 'active IN (0, 1)'
					);
		
		if (!is_null($rps['notification_list'])) {
			$u = new User;
			$notify = $u->find(array('select' => 'id, first_name, last_name', 'where' => 'active IN (0, 1) AND id IN ('.$rps['notification_list'].')'));

			if (is_null($notify)) {
				$notify = array();
			}
			
			$opt['where'] .= ' AND id NOT IN ('.$rps['notification_list'].')';
		}
		
		$u = new User;
		$unnotify = $u->find($opt);
		
		if (is_null($unnotify)) {
			$unnotify = array();
		}
		
		$this->toJSON(array('data' => $rps, 'notify' => $notify, 'unnotify' => $unnotify), true, true);
	}
	
	public function getAllUser() {
		$u = new User();
		
		$u = $u->find(array('select' => 'id, first_name, last_name', 'where' => 'active IN (0,1)'));
		
		$this->toJSON($u, true, true);
	}
}