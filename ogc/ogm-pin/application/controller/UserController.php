<?php
class UserController extends AppController{
	public $autorender = false;
	
	public function listUser() {
		$this->vdata['nav'] = 'user';
		$this->vdata['subnav'] = 'manage_user';
		
		$this->autorender = true;
	}
	
	public function listAllUser() {
		if (empty($_GET['q'])) return;
		
		$u = new User();
		
		//show all for admin, need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY first_name ASC LIMIT 50";
		$opt['select'] = 'id, first_name, last_name';
		$opt['where'] = ' active IN (0,1) AND CONCAT(first_name, \' \', last_name) LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');
		
		$rs = $u->find($opt);
		
		if (is_null($rs)) {
			$rs = array();
		}
		
		$this->toJSON($rs, true, true);
	}
	
	public function getListUser() {
		$item_per_pg = 10;
		
		$u = new User;
		
		$opt['where'] = 'active IN (0,1)';
		$totalPage = $u->count($opt);
		$totalPage = ceil($totalPage / $item_per_pg);
		
		$opt['select'] = "user_group, id, first_name, last_name, user.email, active";
		
		$pg = 0;
		
		if (isset($this->params['page'])){
			$pg = intval($this->params['page']);
			if ($pg <= 0){
				return 404;
			}
			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$opt['custom'] = "ORDER BY first_name, last_name ASC, id DESC LIMIT $pg,$item_per_pg";
		
		$crew_result = $u->find($opt);
		
		$this->toJSON(array('total_page' => intval($totalPage), 'users' => $crew_result) , true, false, null, array('password', 'username', 'active'));
	}
	
	public function userInfo() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'user';
		$this->vdata['subnav'] = 'manage_user';
		
		if (intval($this->params[0]) < 1) {
			return 404;
		}
		
		$u = new User;
		
		$opt['select'] = 'id, user_group, first_name, last_name, username, active, email';
		$opt['where'] = "id = '" . (int)$this->params[0] . "'";
		$this->vdata['rs'] = $u->getOne($opt);
		
		$ug = new UserGroup();
		
		$this->vdata['user_group_list'] = $ug->getList();
	}
	
	public function newUser() {
		$this->vdata['nav'] = 'user';
		$this->vdata['subnav'] = 'new_user';
		
		$ug = new UserGroup();
		$this->vdata['user_group_list'] = $ug->getList();
		$this->renderc('user/user-info', $this->vdata);
	}
	
	public function create() {
		$rules = array(
					'first_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'First name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 50, 'First name cannot be longer than 50 characters'),
									array('minlength', 2, 'First name cannot be shorter than 2 characters')
								),
					'last_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Last name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 50, 'Last name cannot be longer than 50 characters'),
									array('minlength', 2, 'Last name cannot be shorter than 2 characters')
								),
					'user_group' => array(
									array('dbExist', 'user_group', 'name', 'User Group not available')
								),
					'username' => array(
									array('username', 3, 45),
									array('dbNotExist', 'user', 'username', 'Username not available, please choose another Username')
								),
					'email' => array(
									array('email'),
									array('maxlength', 50, 'Email cannot be longer than 50 characters'),
									array('minlength', 3, 'Email cannot be shorter than 3 characters')
								),
					'password' => array(
									array('maxlength', 20, 'Password cannot be longer than 20 characters'),
									array('minlength', 6, 'Password cannot be shorter than 6 characters')
								),
					'password2' => array(
							array('maxlength', 20, 'Password cannot be longer than 20 characters'),
							array('minlength', 6, 'Password cannot be shorter than 6 characters')
					)
				);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$salt = md5(Doo::conf()->salt . $_POST['username'] . time());
		$_POST['password'] = md5($_POST['password'] . $salt) . ':' . $salt;
		
		unset($_POST['id']);
		
		$_POST['user_type'] = 'admin';
		
		$u = new User($_POST);
		
		try {
			$id = $u->insert();
			Doo::logger()->log('Added new user - ' . json_encode($u->getDataArray()), $id, 'user');            
		} catch(PDOException $e) {
			if (strpos($e->getMessage(), 'SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate') === 0) {
				$this->toJSON('User already exists in the database. Duplication is not allowed.', true);				
				return 500;
			}
			throw $e;            
		}
		
		return 201;
	}
	
	public function update() {
		$rules = array(
					'id' => array('dbExist', 'user', 'id', 'User does not exist. It might been deleted.'),
					'first_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'First name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 50, 'First name cannot be longer than 50 characters'),
									array('minlength', 2, 'First name cannot be shorter than 2 characters')
								),
					'last_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Last name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 50, 'Last name cannot be longer than 50 characters'),
									array('minlength', 2, 'Last name cannot be shorter than 2 characters')
								),
					'username' => array(
									array('username', 3, 45),
								),
					'user_group' => array(
									array('dbExist', 'user_group', 'name', 'User Group not available')
								),
					'email' => array(
									array('email'),
									array('maxlength', 50, 'Email cannot be longer than 50 characters'),
									array('minlength', 3)
								),
					'password' => array(
									array('optional'),
									array('maxlength', 20, 'Password cannot be longer than 20 characters'),
									array('minlength', 6, 'Password cannot be shorter than 6 characters')
								),
					'password2' => array(
							array('optional'),
							array('maxlength', 20, 'Password cannot be longer than 20 characters'),
							array('minlength', 6, 'Password cannot be shorter than 6 characters')
					)
				);
		
		$u = new User();
		$u->id = $_POST['id'];
		
		$rs = $u->getOne(array('select' => 'username'));
		
		if ($rs->username != $_POST['username']) {
			$rules['username'][] = array('dbNotExist', 'user', 'username', 'Username not available, please choose another Username');
		}
		
		unset($u);
		unset($rs);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		if (empty($_POST['password'])){
			unset($_POST['password']);
		} else {
			if ($_POST['password'] == $_POST['password2']) {
				$salt = md5(Doo::conf()->salt . $_POST['username'] . time());
				$_POST['password'] = md5($_POST['password'] . $salt) . ':' . $salt;
			} else {
				$this->toJSON('Password and Confirm Password not match', true);
				return 400;
			}
		}
		
		$u = new User($_POST);
		
		try {
			$u->update();
			Doo::logger()->log('Update user - ' . json_encode($u->getDataArray()), $u->id, 'user');                        
		} catch(PDOException $e) {
			if (strpos($e->getMessage(), 'SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate') === 0) {
				$this->toJSON('User already exists in the database. Duplication is not allowed.', true);				
				return 500;
			}
			throw $e;            
		}
		
		return 200;
	}
	
	public function delete(){
		if (intval($this->params[0]) < 1){
			return 404;
		}
		
		$p = new User;
		$p->id = (int)$this->params[0];
		$p->active = 2;
		
		$p->update();
		
		$p = $p->getOne();
		Doo::logger()->log('Delete user - ' . json_encode($p->getDataArray()), $p->id, 'user');

		return '/user/list-user';
	}
	
	public function myAccount() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'home';
		$this->vdata['subnav'] = 'my_account';
		
		$u = new User;
		$u->id = (int)$this->auth->userID;
		
		$opt['select'] = 'id, first_name, last_name, username, email';
		
		$u = $u->getOne($opt);
		
		$this->vdata['rs'] = $u;
	}
	
	public function updateMyAccount() {
		if ($_POST['id'] != $this->auth->userID) {
			return 404;
		}
		
		$rules = array(
					'first_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'First name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 45, 'First name cannot be longer than 45 characters'),
									array('minlength', 2, 'First name cannot be shorter than 2 characters')
								),
					'last_name' => array(
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'Last name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 45, 'Last name cannot be longer than 45 characters'),
									array('minlength', 2, 'Last name cannot be shorter than 2 characters')
								),
					'email' => array(
									array('email'),
									array('maxlength', 65, 'Email cannot be longer than 65 characters'),
									array('minlength', 3)
								),
					'username' => array(
									array('username', 3, 45),
								),
					'password' => array(
									array('optional'),
									array('maxlength', 20, 'Password cannot be longer than 20 characters'),
									array('minlength', 6, 'Password cannot be shorter than 6 characters')
								),
					'password2' => array(
							array('optional'),
							array('maxlength', 20, 'Password cannot be longer than 20 characters'),
							array('minlength', 6, 'Password cannot be shorter than 6 characters')
					)
				);
		
		$u = new User();
		$u->id = $_POST['id'];
		
		$rs = $u->getOne(array('select' => 'username'));
		
		if ($rs->username != $_POST['username']) {
			$rules['username'][] = array('dbNotExist', 'user', 'username', 'Username not available, please choose another Username');
		}
		
		unset($u);
		unset($rs);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);
			
			return 400;
		}
		
		if (empty($_POST['password'])){
			unset($_POST['password']);
		} else {
			if ($_POST['password'] == $_POST['password2']) {
				$salt = md5(Doo::conf()->salt . $this->auth->username . time());
				$_POST['password'] = md5($_POST['password'] . $salt) . ':' . $salt;
			} else {
				$this->toJSON('Password and Confirm Password not match', true);
				return 400;
			}
		}
		
		$u = new User($_POST);
		
		$u->update();
		
		Doo::logger()->log('Update user account - ' . json_encode($u->getDataArray()), $u->id, 'user_myaccount');     
		
		return 200;
	}
}