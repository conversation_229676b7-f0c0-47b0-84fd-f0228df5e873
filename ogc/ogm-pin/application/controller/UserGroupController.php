<?php
class UserGroupController extends AppController{
	public $autorender = false;
	
	public function  __construct() {
		parent::__construct();
		$this->vdata['nav'] = 'setting';
		Doo::loadModel('UserGroup');
	}
	
	public function listUserGroup() {
		$this->autorender = true;
		
		$this->vdata['nav'] = 'setting';
		$this->vdata['subnav'] = 'manage_user_group';
	}
	
	public function getListUserGroup() {
		$item_per_pg = 10;
		
		$ug = new UserGroup;
		
		$totalPage = $ug->count();
		$totalPage = ceil($totalPage / $item_per_pg);
		
		$pg = 0;
		
		if (isset($this->params['page'])) {
			$pg = intval($this->params['page']);
			
			if ($pg < 1){
				return 404;
			}
			
			$pg = ($pg - 1) * $item_per_pg;
		}
		
		$usergroups = $ug->limit("$pg,$item_per_pg", 'name');
		
		$this->toJSON(array('total_page' => intval($totalPage), 'usergroups' => $usergroups) , true);
	}
	
	public function listAllUserGroup() {
		if (empty($_GET['q'])) return;
		
		$ug = new UserGroup;
		
		//need caching or live search autocomplete
		//$opt['match'] = false;
		$opt['custom'] = "ORDER BY name ASC LIMIT 50";
		$opt['select'] = 'id, name';
		$opt['where'] = ' name LIKE ?';
		$opt['param'] = array('%'.$_GET['q'].'%');
		
		$rs = $ug->find($opt);
		
		if (is_null($rs)) {
			$rs = array();
		}
		
		$this->toJSON($rs, true, true);
	}
	
	public function userGroupInfo() {
		$this->autorender = true;
		$this->vdata['subnav'] = 'manage_user_group';
		
		if (intval($this->params[0]) < 1) {
			return 404;
		}
		
		$ug = new UserGroup();
		$ug->id = (int)$this->params[0];
		$rs = $ug->getOne();
		$rs->permission = (Array)(json_decode($rs->permission));

		$this->vdata['rs'] = $rs;
	}
	
	public function newUserGroup() {
		$this->vdata['subnav'] = 'new_user_group';
		
		$this->renderc('user-group/user-group-info', $this->vdata);
	}
	
	public function create() {
		foreach ($_POST as $k => $v) {
			$_POST[$k] = trim($v);
		}
		
		$rules = array('name' => array(
									array('dbNotExist', 'user_group', 'name', 'User group name already exists. Please use another name.'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'User group name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 65, 'User group name cannot be longer than 65 characters'),
									array('minlength', 2, 'User group name cannot be shorter than 2 characters')
								)
					);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);
			
			return 400;
		}
		
		if (isset($_POST['id']))
			unset($_POST['id']);
		
		$ug = new UserGroup($_POST);
		
		$id = $ug->insert();
		
		Doo::logger()->log('Added new user group - ' . json_encode($ug->getDataArray()), $id, 'usergroup');     
		
		return 201;
	}
	
	public function update() {
		foreach ($_POST as $k => $v){
			$_POST[$k] = trim($v);
		}
		
		$rules = array(
					'id' => array('dbExist', 'user_group', 'id', 'User group does not exist. It might been deleted.'),
					'name' => array(
									//array('dbNotExist', 'user_group', 'name', 'User group name already exist. Please use another name.'),
									array('regex', '/^[a-zA-Z0-9,\ \'\.\-\(\)\&\#]+$/', 'User group name can only consist of letters, numbers, dashes, dots, brackets and commas.'),
									array('maxlength', 32, 'User group name cannot be longer than 32 characters'),
									array('minlength', 2, 'User group name cannot be shorter than 2 characters')
								)
					);
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$ug = new UserGroup($_POST);
		
		$ug->update();
		
		Doo::logger()->log('Update user group - ' . json_encode($ug->getDataArray()), $ug->id, 'usergroup');             
	}
	
	public function setPermission() {
		$rules = array('id' => array('dbExist', 'user_group', 'id', 'User group does not exist. It might been deleted.'));
		
		$v = new DooValidator;
		$v->checkMode = DooValidator::CHECK_SKIP;
		
		if ($err = $v->validate($_POST, $rules)) {
			$this->toJSON($err, true);

			return 400;
		}
		
		$permission_array = array();
		$permission_array['AppController'] = '*';
		
		foreach ($_POST as $controller => $action_array) {
			if ($controller != 'id') {
				$permission_array[$controller] = array();
				
				foreach ($action_array as $num => $action) {
					//TODO: Use switch case
					if ($action == 'view') {
						if ($controller == 'LogController' || $controller == 'ReportController') {
							$permission_array[$controller] = '*';
						} else {
							$temp = substr($controller, 0, -10);
							
							$permission_array[$controller][] = 'list' . $temp;
							$permission_array[$controller][] = 'getList' . $temp;
							$permission_array[$controller][] = 'listAll' . $temp;
							
							if ($controller == 'UserGroupController') {
								$permission_array[$controller][] = 'userGroupInfo';
							} else {
								if ($controller == 'RequestController' || $controller == 'PinController') {
									$permission_array[$controller][] = 'getListRemarks';

									if ($controller == 'PinController') {
										$permission_array[$controller][] = 'searchPin';
									}
								}

								$permission_array[$controller][] = strtolower($temp) . 'Info';
							}
							
							unset($temp);
						}
					} else if ($action == 'create') {
						$temp = substr($controller, 0, -10);
						
						$permission_array[$controller][] = 'new' . $temp;
						$permission_array[$controller][] = $action;

						unset($temp);
					} else if ($action == 'export') {
						$permission_array[$controller][] = 'exportBySerial';
						$permission_array[$controller][] = 'exportByProduct';
						$permission_array[$controller][] = 'exportByBatch';
						$permission_array[$controller][] = 'exportAll';
					} else if ($action == 'setNotification') {
						if ($controller == 'ResellerController') {
							$permission_array[$controller][] = 'getListNotification';
							$permission_array[$controller][] = 'createNotification';
							$permission_array[$controller][] = 'deleteNotification';
							$permission_array[$controller][] = 'getNotificationById';
							$permission_array[$controller][] = 'getNotificationByProductId';
							$permission_array[$controller][] = 'getAllUser';
						} else {
							$permission_array[$controller][] = $action;
						}
					} else {
						$permission_array[$controller][] = $action;
					}
				}
			}
		}
		
		$permission_array['UserController'][] = 'myAccount';
		$permission_array['UserController'][] = 'updateMyAccount';
		$permission_array['RequestController'][] = 'updateSelf';
		$permission_array['RequestController'][] = 'deleteSelf';
		$permission_array['PinController'][] = 'exportBySerialSelf';
		$permission_array['PinController'][] = 'exportByBatchSelf';
		
		$ug = new UserGroup();
		$ug->id = $_POST['id'];
		$ug->permission = json_encode($permission_array);
		$ug->update();
		
		$ug = $ug->getOne();
		Doo::logger()->log('Update user group permission - ' . json_encode($ug->getDataArray()), $ug->id, 'usergroup_permission');
	}
}