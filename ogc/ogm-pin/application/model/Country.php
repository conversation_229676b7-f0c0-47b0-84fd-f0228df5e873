<?php
Doo::loadModel('base/CountryBase');

class Country extends CountryBase{
    public function getName($id){
        $this->id = $id;
        $return = $this->getOne(array('select'=>'name'));
        return $return->name;
    }
    
    public function getList(){
        $return = array();
		$opt = array('select' => 'id, name', 'asArray' => true);
		
        if ($result = $this->find($opt)) {
            foreach($result as $v){
                $return[$v['id']] = $v['name'];
            }
        }
		
        return $return;
    }
}