<?php

Doo::loadModel('base/PinBase');

class Pin extends PinBase {

    public function getStatusList() {
        $return = array('' => 'Please Select', 'active' => 'Active', 'inactive' => 'Inactive');

        return $return;
    }

    public function redeem($pin, $serial, $client_id, $merchant_id = null) {
        $p = new Pin();

        $p->pin = $pin;
        $p->serial = $serial;
        $p->status = 1;

        $opt['select'] = 'pin.*';
        $opt['asArray'] = true;

        $p = $p->getOne($opt);

        if (empty($p)) {
            Doo::logger()->log('Pin does not exist - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), 0, 'api_redeem');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin does not exist'));
            return $rs;
        }

        $gr = new GenRequest();
        $gr->id = $p['batch_id'];
        $gr->client_id = $client_id;

        $gr = $gr->getOne(array('asArray' => true));

        if (empty($gr)) {
            Doo::logger()->log('Pin does not belong to client - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), 0, 'api_redeem');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin does not exist'));
            return $rs;
        }

        if(!empty($gr['exclusive'])) {
            if($merchant_id !== $gr['exclusive']) {
                $rs = array('code' => 400, 'msg' => array('error' => 'Exclusive pin doesn\'t belong to this merchant'));
                return $rs;
            }
        }

        unset($gr);

        if ($p['redeem'] == 1) {
            Doo::logger()->log('Pin has already redempted - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), $p['id'], 'api_redeem');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin has already redempted'));
            return $rs;
        }

        $start_date = strtotime($p['start_date'] . ' 00:00:00');
        $end_date = strtotime($p['end_date'] . ' 23:59:59');

        if (time() < $start_date) {
            Doo::logger()->log('Pin is not available for redeem at this time - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), $p['id'], 'api_redeem');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin is not available for redeem at this time'));
            return $rs;
        }

        if (time() > $end_date) {
            Doo::logger()->log('Pin Expired - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), $p['id'], 'api_redeem');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin Expired'));
            return $rs;
        }

        $pin = new Pin(array('id' => $p['id'], 'redeem' => 1, 'pin' => $pin, 'serial' => $serial));
        $pin->update();

        $pObj = new Pin();
        $pObj->id = $p['id'];
        $pObj = $pObj->getOne(array('select' => 'status, redeem', 'asArray' => true));

        $psl = new PinStatusLog(array('pin_id' => $p['id'], 'status' => $pObj['status'], 'redeem' => $pObj['redeem'], 'create_date' => new DooDbExpression('NOW()')));
        $psl->insert();

        $pr = new PinRemark();

        $pr->remarks = 'Pin Redeemed';
        $pr->pin_id = $p['id'];
        $pr->create_by = 0;
        $pr->create_date = new DooDbExpression('NOW()');
        $pr->insert();

        Doo::logger()->log('Pin Redeem Successfully - ' . json_encode($pin->getDataArray()), $p['id'], 'api_redeem');

        unset($pin);
        unset($psl);
        unset($pr);

        $gr = new GenRequest();
        $gr->id = $p['batch_id'];

        $gr = $gr->getOne(array('select' => 'product_id, reseller_id', 'asArray' => true));

        // Low Stock Notification
        $rps = new ResellerPinSetting;

        $rps->reseller_id = $gr['reseller_id'];
        $rps->product_id = $gr['product_id'];

        $rps = $rps->getOne(array('select' => 'qty_of_notify, notification_list', 'asArray' => true));

        if (!is_null($rps)) {
            if (!is_null($rps['notification_list'])) {
                $pin_sql = "	SELECT p.id 
								FROM pin AS p 
								INNER JOIN gen_request AS gr 
									ON (gr.id = p.batch_id AND gr.product_id = '" . $gr['product_id'] . "' AND gr.reseller_id = '" . $gr['reseller_id'] . "' AND gr.status = 'Approved') 
								WHERE p.status IN (1,2) 
									AND p.redeem = 0";
                $pin_rs = $this->db()->fetchAll($pin_sql);

                if (sizeof($pin_rs) <= $rps['qty_of_notify']) {
                    $u = new User();

                    $op = array();
                    $op['select'] = 'first_name, last_name, email';
                    $op['where'] = 'id IN (' . $rps['notification_list'] . ') AND active IN (0,1)';
                    $op['asArray'] = true;

                    $u = $u->find($op);

                    if (!is_null($u)) {
                        $mail = new DooMailer();

                        $u_size = sizeof($u);

                        for ($i = 0; $i < $u_size; $i++) {
                            $mail->addTo($u[$i]['email']);
                        }

                        $pro = new Product();
                        $pro->id = $gr['product_id'];
                        $pro = $pro->getOne(array('select' => 'name', 'asArray' => true));

                        $res = new Reseller();
                        $res->id = $gr['reseller_id'];
                        $res = $res->getOne(array('select' => 'name', 'asArray' => true));

                        $mail->setFrom('<EMAIL>', 'OffGamers Pin Generation');
                        $mail->setSubject('Low Stock Notification');
                        $mail->setBodyText('The available pins of product (' . $pro['name'] . ') for ' . $res['name'] . ' are currently lower than ' . $rps['qty_of_notify']);
                        $mail->send();

                        unset($pro);
                        unset($res);
                    }
                }
            }
        }

        $rs = array('code' => 200, 'msg' => array('success' => 'Redeem Success'));
        return $rs;
    }

    public function getPinInfo($pin, $serial, $client_id, $flag_redeem = false, $merchant_id = null) {
        $p = new Pin();

        if ($pin) {
            $p->pin = $pin;
        }
        $p->serial = $serial;

        $p = $p->getOne(array('select' => 'id, batch_id, pin, status, redeem, currency, deno, start_date, end_date, extend'));

        if (empty($p)) {
            Doo::logger()->log('Pin does not exist - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), 0, 'api_getpininfo');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin does not exist'));
            return $rs;
        }
        
        $gr = new GenRequest();
        $gr->id = $p->batch_id;
        $gr->client_id = $client_id;
        
        $gr = $gr->getOne(array('select' => 'reseller_id,exclusive'));

        if (empty($gr)) {
            Doo::logger()->log('Pin does not belong to client - ' . json_encode(array('pin' => $pin, 'serial' => $serial)), 0, 'api_getpininfo');

            $rs = array('code' => 400, 'msg' => array('error' => 'Pin does not exist'));
            return $rs;
        }

        $p->exclusive = $gr->exclusive;
        
        $r = new Reseller();
        $r->id = $gr->reseller_id;

        $r = $r->getOne(array('select' => 'id, name, reseller_code'));

        if (empty($r)) {
            Doo::logger()->log('Reseller does not exist - ' . json_encode(array('pin' => $pin, 'serial' => $serial, 'reseller_id' => $gr->reseller_id)), 0, 'api_getpininfo');

            $rs = array('code' => 400, 'msg' => array('error' => 'Reseller does not exist'));
            return $rs;
        }

        unset($gr);

        Doo::logger()->log('Get pin information Successfully - ' . json_encode(array('id' => $p->id, 'batch_id' => $p->batch_id, 'status' => $p->status, 'redeem' => $p->redeem, 'currency' => $p->currency, 'deno' => $p->deno, 'start_date' => $p->start_date, 'end_date' => $p->end_date)), $p->id, 'api_getpininfo');

        if ($flag_redeem) {
            $pObj = new Pin(array('id' => $p->id, 'redeem' => 1, 'pin' => $p->pin, 'serial' => $p->serial));
            $pObj->update();

            $psl = new PinStatusLog(array('pin_id' => $p->id, 'status' => $p->status, 'redeem' => 1, 'create_date' => new DooDbExpression('NOW()')));
            $psl->insert();

            $pr = new PinRemark();
            $pr->remarks = 'Pin Redeemed';
            $pr->pin_id = $p->id;
            $pr->create_by = 0;
            $pr->create_date = new DooDbExpression('NOW()');
            $pr->insert();

            Doo::logger()->log('Pin flag as Redeem Successfully - ' . json_encode($pObj->getDataArray()), $p->id, 'api_redeem');

            unset($pObj);
            unset($psl);
            unset($pr);
        }

        $rs = array('code' => 200, 'result' => array('id' => $p->id, 'batch_id' => $p->batch_id, 'pin' => $p->pin, 'status' => $p->status, 'redeem' => $p->redeem, 'currency' => $p->currency, 'deno' => $p->deno, 'start_date' => $p->start_date, 'end_date' => $p->end_date, 'exclusive' => $p->exclusive, 'reseller_id' => $r->id, 'reseller_name' => $r->name, 'reseller_code' => $r->reseller_code));
        return $rs;
    }

    public function generatePin($id) {
        if (empty($id))
            return false;

        $g = new GenRequest;
        $g->id = $id;
        $g->status = 'Pending';

        $opt = array();
        $opt['select'] = 'quantity, currency, deno, pin_type, start_date, end_date, fee_deno, rate';

        $request = $g->getOne();

        if ($request->quantity < 1)
            return false;

        $pin_status = 1;

        if ($request->pin_type == 8) {
            $pin_status = 2;
        }

        $list = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'R', 'S', 'T', 'U', 'V', 'X', 'Y', 'Z');
        $length = sizeof($list);
        $sql_query = array();

        $str = "INSERT INTO pin (batch_id,pin,serial,status,currency,deno,start_date,end_date,fee_deno,rate) VALUES ";

        for ($k = 1; $k <= $request->quantity; $k++) {
            if ($k % 5000 == 1 && $k != 1) { // split into chunks
                $str = substr($str, 0, -1);
                $sql_query[] = $str;
                $str = "INSERT INTO pin (batch_id,pin,serial,status,currency,deno,start_date,end_date,fee_deno,rate) VALUES ";
            }

            $serial = $list[rand(0, $length - 1)] . $list[rand(0, $length - 1)] . $list[rand(0, $length - 1)] . $list[rand(0, $length - 1)] . $request->country_id . $request->id . str_pad($k, 5, "0", STR_PAD_LEFT) . $request->pin_type;
            $pin = chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57)) . chr(rand(48, 57));
            $str .= "('" . $g->id . "','" . $pin . "','" . $serial . "','" . $pin_status . "','" . $request->currency . "','" . $request->deno . "','" . $request->start_date . "','" . $request->end_date . "','" . $request->fee_deno . "','" . $request->rate . "'),";
        }

        $str = substr($str, 0, -1);
        $sql_query[] = $str;

        foreach ($sql_query as $sql) {
            $this->db()->query($sql);
        }

        $p = new Pin();
        $p->batch_id = $g->id;
        $p = $p->find(array('select' => 'id', 'asArray' => true));

        $pin_amt = sizeof($p);

        for ($i = 0; $i < $pin_amt; $i++) {
            $psl = new PinStatusLog(array('pin_id' => $p[$i]['id'], 'status' => $pin_status, 'redeem' => 0, 'create_date' => new DooDbExpression('NOW()')));
            $psl->insert();

            unset($psl);
        }

        return $request->quantity;
    }

    public function deliverPin($pin_request_id, $excl) {
        $r = new PinRequest();
        $r->id = $pin_request_id;
        $r->status = 0;

        $r = $r->getOne(array('select' => 'reseller_id, trans_id, currency, deno, qty'));
        /*
          $g = new GenRequest();
          $g->reseller_id = $r->reseller_id;
          $g->status = 'Approved';
          $g->pin_type = 8;

          $opt = array('select' => 'COUNT(pin.id) AS total_pin', 'where' => "pin.status = 2 AND pin.currency = '" . $r->currency . "' AND pin.deno = '" . $r->deno . "'");

          $total_pins = $g->relate('Pin', $opt);
          $total_pins = $total_pins[0]->total_pin;

          if ($r->qty > $total_pins) {
          $rs = array('code' => 400, 'msg' => 'The quantity of pins requested is more than the amount available, please reduce the number of quantity request');
          return $rs;
          }
         */
        $sql = "SELECT p.id, p.pin AS softpin_number, p.serial AS serial_number, p.end_date AS Info 
				FROM gen_request AS gr 
				INNER JOIN pin AS p 
					ON (p.batch_id = gr.id AND p.status = 2 AND p.currency = '" . $r->currency . "' AND p.deno = '" . $r->deno . "') 
				WHERE gr.reseller_id = '" . $r->reseller_id . "' 
					AND gr.status = 'Approved' 
					AND gr.pin_type = 8 
                                        AND gr.exclusive = '" . $excl . "' 
					ORDER BY p.id 
				LIMIT " . $r->qty;
        $pin_array = $this->db()->fetchAll($sql);

        if (sizeof($pin_array) < $r->qty) {
            $rs = array('code' => 400, 'msg' => 'The quantity of pins requested is more than the amount available, please reduce the number of quantity request');
            return $rs;
        } else {
            $pin_id_array = array();
            $sql_str = "INSERT INTO pin_status_log (pin_id,status,redeem,create_date) VALUES ";

            for ($i = 0; $i < $r->qty; $i++) {
                $pin_array[$i]['Info'] = $pin_array[$i]['Info'] . ' 23:59:59';
                $pin_id_array[] = $pin_array[$i]['id'];

                $sql_str .= "('" . $pin_array[$i]['id'] . "','1','0','" . new DooDbExpression('NOW()') . "'),";
            }

            $sql_str = substr($sql_str, 0, -1);

            $this->db()->query("LOCK TABLES pin WRITE;");

            $update_sql = "UPDATE pin SET status = 1 WHERE id IN (" . implode(',', $pin_id_array) . ")";
            $this->db()->query($update_sql);

            $data_array = array('pin_request_id' => $pin_request_id,
                'pins' => implode(',', $pin_id_array)
            );

            $this->db()->query("UNLOCK TABLES;");

            $prd = new PinRequestDelivered($data_array);
            $prd->insert();

            $this->db()->query($sql_str);

            Doo::logger()->log('Pin Request Delivered - ' . json_encode($pin_array), $pin_request_id, 'api_pin_request');

            $rs = array('code' => 200, 'result' => array('mm_cmd' => 'GP_SSS', 'tran_id' => $r->trans_id, 'offgamers_tran_id' => $pin_request_id, 'tran_status' => 1, 'tran_errcode' => 'Success', 'process_date' => date('Y-m-d H:i:s'), 'req' => $pin_array));
            return $rs;
        }
    }

    public function exportCSV($id = null, $type = null, $status = null, $redeem = null, $auth_id) {
            if($type == 'product_id') {
                $g = new GenRequest();
                $opt['select'] = '*';
                $opt['custom'] = "INNER JOIN pin ON gen_request.id = pin.batch_id WHERE product_id = '". $id. "'";
                $pins = $g->find($opt);

            } else {
                $p = new Pin;
                $where = 'WHERE 1';
    
                if ($type !== null) {
                    $where .= " AND " . $type . " = '" . $id . "'";
                }
    
                if ($status !== null) {
                    $where .= " AND pin.status = " . (int) $status;
                }
    
                if ($redeem !== null) {
                    $where .= " AND pin.redeem = " . (int) $redeem;
                }
    
                $opt['select'] = 'pin.batch_id, pin.serial, pin.pin, pin.status, pin.redeem, pin.start_date, pin.end_date';
                $opt['custom'] = " $where ORDER BY pin.batch_id DESC";
    
                $pins = $p->find($opt);
            }

            $pin = '******';

            if (is_null($pins)) {
                $pins = array();
            }

            if (sizeof($pins) > 0) {
                $filename = Doo::conf()->auth->securityToken() . '.csv';
                $path = Doo::getAppPath() . 'cache/' . $filename;
                $fp = fopen(Doo::getAppPath() . 'cache/' . $filename, 'w+');

                $field_array = explode(',', 'Batch ID,Serial,Pin,Start Date,End Date,Status,Redeem');

                fputcsv($fp, $field_array);

                $batch_id_array = array();

                foreach ($pins as $pin_info) {
                    if ($pin_info->status == 1) {
                        $pin_info->status = 'Active';
                    } else if ($pin_info->status == 2) {
                        $pin_info->status = 'Pending';
                    } else {
                        $pin_info->status = 'In Active';
                    }

                    if ($pin_info->redeem == 1) {
                        $pin_info->redeem = 'Yes';
                    } else {
                        $pin_info->redeem = 'No';
                    }

                    if (AppController::_checkPermit('PinController', 'viewPinNumber')) {
                        $pin = $pin_info->pin;
                    } else {
                        if (!isset($batch_id_array[$pin_info->batch_id])) {
                            $gr = new GenRequest();
                            $gr->id = $pin_info->batch_id;

                            $gr = $gr->getOne(array('select' => 'request_by', 'asArray' => true));

                            $batch_id_array[$pin_info->batch_id] = $gr['request_by'];
                        }

                        if ($batch_id_array[$pin_info->batch_id] == $auth_id) {
                            $pin = $pin_info->pin;
                        }
                    }

                    $pin_info_array = array("'" . $pin_info->batch_id,
                        $pin_info->serial,
                        "'" . $pin,
                        $pin_info->start_date,
                        $pin_info->end_date,
                        $pin_info->status,
                        $pin_info->redeem
                    );

                    fputcsv($fp, $pin_info_array);
                }

                fclose($fp);

                return $path;
            }
    }

}
