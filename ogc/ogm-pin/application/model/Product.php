<?php
Doo::loadModel('base/ProductBase');

class Product extends ProductBase{
    public function getName($id) {
        $this->id = $id;
        $return = $this->getOne(array('select'=>'name'));
        return $return->name;
    }
    
    public function getList($active = '') {
        $return = array();
		$opt = array('select' => 'id, name', 'asArray' => true);

		if (intval($active) != '') {
			$opt['where'] = "active = " . (int)$active;
		}

        if ($result = $this->find($opt)) {
            foreach($result as $v){
                $return[$v['id']] = $v['name'];
            }
        }
        return $return;
    }
}