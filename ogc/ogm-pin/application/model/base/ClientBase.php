<?php
Doo::loadCore('db/DooModel');

class ClientBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 65.
     */
    public $name;

    /**
     * @var varchar Max length is 255.
     */
    public $url;

    /**
     * @var mediumtext
     */
    public $description;

    /**
     * @var varchar Max length is 16.
     */
    public $api_key;

    /**
     * @var varchar Max length is 45.
     */
    public $secret_key;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $active;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $create_by;

    public $_table = 'client';
    public $_primarykey = 'id';
    public $_fields = array('id','name','url','description','api_key','secret_key','active','create_date','create_by');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'name' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'url' => array(
                        array( 'maxlength', 255 ),
                        array( 'notnull' ),
                ),

                'description' => array(
                        array( 'optional' ),
                ),

                'api_key' => array(
                        array( 'maxlength', 16 ),
                        array( 'notnull' ),
                ),

                'secret_key' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'active' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'create_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                )
            );
    }

}