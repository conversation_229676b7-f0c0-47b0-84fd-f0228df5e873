<?php
Doo::loadCore('db/DooModel');

class CountryBase extends DooModel{

    /**
     * @var varchar Max length is 3.
     */
    public $id;

    /**
     * @var varchar Max length is 128.
     */
    public $name;

    public $_table = 'country';
    public $_primarykey = 'id';
    public $_fields = array('id','name');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'name' => array(
                        array( 'maxlength', 128 ),
                        array( 'notnull' ),
                )
            );
    }

}