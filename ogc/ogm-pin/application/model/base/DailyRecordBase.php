<?php
Doo::loadCore('db/DooModel');

class DailyRecordBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $reseller_id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $product_id;

    /**
     * @var int Max length is 8.  unsigned.
     */
    public $pending;

    /**
     * @var int Max length is 8.  unsigned.
     */
    public $active;

    /**
     * @var datetime
     */
    public $create_date;

    public $_table = 'daily_record';
    public $_primarykey = 'id';
    public $_fields = array('id','reseller_id','product_id','pending','active','create_date');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'reseller_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'product_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'pending' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'active' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                )
            );
    }

}