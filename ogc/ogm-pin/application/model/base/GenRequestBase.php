<?php
Doo::loadCore('db/DooModel');

class GenRequestBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 65.
     */
    public $title;

    /**
     * @var enum 'Pending','Approved','Cancelled','Deleted').
     */
    public $status;

    /**
     * @var int Max length is 5.  unsigned.
     */
    public $quantity;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $product_id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $reseller_id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $client_id;

    /**
     * @var varchar Max length is 3.
     */
    public $country_id;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $pin_type;

    /**
     * @var date
     */
    public $start_date;

    /**
     * @var date
     */
    public $end_date;

    /**
     * @var tinytext
     */
    public $description;

    /**
     * @var datetime
     */
    public $request_date;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $request_by;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $approve_by;

    /**
     * @var varchar Max length is 65.
     */
    public $product_name;

    /**
     * @var varchar Max length is 3.
     */
    public $currency;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $deno;

    /**
     * @var varchar Max length is 45.
     */
    public $barcode;

    /**
     * @var tinytext
     */
    public $product_description;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $fee_deno;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $rate;
    
    /**
     * @var varchar Max length is 20.
     */
    public $exclusive;

    public $_table = 'gen_request';
    public $_primarykey = 'id';
    public $_fields = array('id','title','status','quantity','product_id','reseller_id','client_id','country_id','pin_type','start_date','end_date','description','request_date','request_by','approve_by','product_name','currency','deno','barcode','product_description','fee_deno','rate', 'exclusive');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'title' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'status' => array(
                        array( 'notnull' ),
                ),

                'quantity' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 5 ),
                        array( 'notnull' ),
                ),

                'product_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'reseller_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'client_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'country_id' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'pin_type' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'start_date' => array(
                        array( 'date' ),
                        array( 'notnull' ),
                ),

                'end_date' => array(
                        array( 'date' ),
                        array( 'notnull' ),
                ),

                'description' => array(
                        array( 'notnull' ),
                ),

                'request_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'request_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'approve_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'product_name' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'currency' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'barcode' => array(
                        array( 'maxlength', 45 ),
                        array( 'optional' ),
                ),

                'product_description' => array(
                        array( 'optional' ),
                ),

                'fee_deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'rate' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),
                'exclusive' => array(
                        array('maxlength', 20),
                        array('optional')
                )
            );
    }

}