<?php
Doo::loadCore('db/DooModel');

class GenRequestRemarkBase extends DooModel{

    /**
     * @var int Max length is 11.  unsigned.
     */
    public $id;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var tinytext
     */
    public $remarks;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $create_by;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $gen_request_id;

    public $_table = 'gen_request_remark';
    public $_primarykey = 'id';
    public $_fields = array('id','create_date','remarks','create_by','gen_request_id');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 11 ),
                        array( 'optional' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'remarks' => array(
                        array( 'optional' ),
                ),

                'create_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'gen_request_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                )
            );
    }

}