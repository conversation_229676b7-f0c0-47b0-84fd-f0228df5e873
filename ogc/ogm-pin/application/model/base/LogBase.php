<?php
Doo::loadCore('db/DooModel');

class LogBase extends DooModel{

    /**
     * @var bigint Max length is 19.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 25.
     */
    public $module;

    /**
     * @var mediumtext
     */
    public $msg;

    /**
     * @var varchar Max length is 19.
     */
    public $ip;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $item_id;

    /**
     * @var varchar Max length is 45.
     */
    public $act_by;

    /**
     * @var datetime
     */
    public $logtime;

    public $_table = 'log';
    public $_primarykey = 'id';
    public $_fields = array('id','module','msg','ip','item_id','act_by','logtime');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 19 ),
                        array( 'optional' ),
                ),

                'module' => array(
                        array( 'maxlength', 25 ),
                        array( 'optional' ),
                ),

                'msg' => array(
                        array( 'optional' ),
                ),

                'ip' => array(
                        array( 'maxlength', 19 ),
                        array( 'optional' ),
                ),

                'item_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'act_by' => array(
                        array( 'maxlength', 45 ),
                        array( 'optional' ),
                ),

                'logtime' => array(
                        array( 'datetime' ),
                        array( 'optional' ),
                )
            );
    }

}