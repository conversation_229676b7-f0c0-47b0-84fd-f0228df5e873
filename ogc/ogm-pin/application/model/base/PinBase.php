<?php
Doo::loadCore('db/DooModel');

class PinBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $batch_id;

    /**
     * @var varchar Max length is 16.
     */
    public $pin;

    /**
     * @var varchar Max length is 18.
     */
    public $serial;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $status;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $redeem;

    /**
     * @var varchar Max length is 3.
     */
    public $currency;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $deno;

    /**
     * @var date
     */
    public $start_date;

    /**
     * @var date
     */
    public $end_date;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $extend;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $fee_deno;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $rate;

    public $_table = 'pin';
    public $_primarykey = 'id';
    public $_fields = array('id','batch_id','pin','serial','status','redeem','currency','deno','start_date','end_date','extend','fee_deno','rate');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'batch_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'pin' => array(
                        array( 'maxlength', 16 ),
                        array( 'notnull' ),
                ),

                'serial' => array(
                        array( 'maxlength', 18 ),
                        array( 'notnull' ),
                ),

                'status' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'redeem' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'currency' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'start_date' => array(
                        array( 'date' ),
                        array( 'notnull' ),
                ),

                'end_date' => array(
                        array( 'date' ),
                        array( 'notnull' ),
                ),

                'extend' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'fee_deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'rate' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                )
            );
    }

}