<?php
Doo::loadCore('db/DooModel');

class PinRemarkBase extends DooModel{

    /**
     * @var int Max length is 11.  unsigned.
     */
    public $id;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var tinytext
     */
    public $remarks;

    /**
     * @var mediumint Max length is 8.
     */
    public $create_by;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $pin_id;

    public $_table = 'pin_remark';
    public $_primarykey = 'id';
    public $_fields = array('id','create_date','remarks','create_by','pin_id');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 11 ),
                        array( 'optional' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'remarks' => array(
                        array( 'optional' ),
                ),

                'create_by' => array(
                        array( 'integer' ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'pin_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                )
            );
    }

}