<?php
Doo::loadCore('db/DooModel');

class PinRequestBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $reseller_id;

    /**
     * @var varchar Max length is 45.
     */
    public $command;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $trans_id;

    /**
     * @var varchar Max length is 3.
     */
    public $currency;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $deno;

    /**
     * @var smallint Max length is 5.  unsigned.
     */
    public $qty;

    /**
     * @var tinytext
     */
    public $description;

    /**
     * @var tinyint Max length is 3.  unsigned.
     */
    public $status;

    /**
     * @var datetime
     */
    public $reseller_request_date;

    /**
     * @var datetime
     */
    public $create_date;

    public $_table = 'pin_request';
    public $_primarykey = 'id';
    public $_fields = array('id','reseller_id','command','trans_id','currency','deno','qty','description','status','reseller_request_date','create_date');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'reseller_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'command' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'trans_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'currency' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'qty' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 5 ),
                        array( 'notnull' ),
                ),

                'description' => array(
                        array( 'optional' ),
                ),

                'status' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'reseller_request_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                )
            );
    }

}