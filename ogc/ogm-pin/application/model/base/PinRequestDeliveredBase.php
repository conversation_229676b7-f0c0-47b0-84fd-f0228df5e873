<?php
Doo::loadCore('db/DooModel');

class PinRequestDeliveredBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $pin_request_id;

    /**
     * @var longtext
     */
    public $pins;

    public $_table = 'pin_request_delivered';
    public $_primarykey = 'pin_request_id';
    public $_fields = array('pin_request_id','pins');

    public function getVRules() {
        return array(
                'pin_request_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'pins' => array(
                        array( 'notnull' ),
                )
            );
    }

}