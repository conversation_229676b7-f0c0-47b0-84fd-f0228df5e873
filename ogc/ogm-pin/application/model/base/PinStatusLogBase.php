<?php
Doo::loadCore('db/DooModel');

class PinStatusLogBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $pin_id;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $status;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $redeem;

    /**
     * @var datetime
     */
    public $create_date;

    public $_table = 'pin_status_log';
    public $_primarykey = 'id';
    public $_fields = array('id','pin_id','status','redeem','create_date');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'pin_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'status' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'redeem' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                )
            );
    }

}