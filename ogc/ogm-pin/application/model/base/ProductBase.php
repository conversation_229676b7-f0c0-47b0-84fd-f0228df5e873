<?php
Doo::loadCore('db/DooModel');

class ProductBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 65.
     */
    public $name;

    /**
     * @var varchar Max length is 3.
     */
    public $currency;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $deno;

    /**
     * @var varchar Max length is 45.
     */
    public $barcode;

    /**
     * @var tinytext
     */
    public $description;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $active;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $create_by;

    public $_table = 'product';
    public $_primarykey = 'id';
    public $_fields = array('id','name','currency','deno','barcode','description','active','create_date','create_by');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'name' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'currency' => array(
                        array( 'maxlength', 3 ),
                        array( 'notnull' ),
                ),

                'deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'barcode' => array(
                        array( 'maxlength', 45 ),
                        array( 'optional' ),
                ),

                'description' => array(
                        array( 'optional' ),
                ),

                'active' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'create_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                )
            );
    }

}