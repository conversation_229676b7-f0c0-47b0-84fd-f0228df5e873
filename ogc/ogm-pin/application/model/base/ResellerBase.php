<?php
Doo::loadCore('db/DooModel');

class ResellerBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 65.
     */
    public $name;

    /**
     * @var varchar Max length is 65.
     */
    public $email;

    /**
     * @var varchar Max length is 65.
     */
    public $tel;

    /**
     * @var varchar Max length is 16.
     */
    public $reseller_code;

    /**
     * @var varchar Max length is 45.
     */
    public $secret_key;

    /**
     * @var varchar Max length is 255.
     */
    public $verification_url;

    /**
     * @var tinytext
     */
    public $description;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $active;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $create_by;

    public $_table = 'reseller';
    public $_primarykey = 'id';
    public $_fields = array('id','name','email','tel','reseller_code','secret_key','verification_url','description','active','create_date','create_by');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'name' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'email' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'tel' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'reseller_code' => array(
                        array( 'maxlength', 16 ),
                        array( 'notnull' ),
                ),

                'secret_key' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'verification_url' => array(
                        array( 'maxlength', 255 ),
                        array( 'notnull' ),
                ),

                'description' => array(
                        array( 'optional' ),
                ),

                'active' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'create_by' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                )
            );
    }

}