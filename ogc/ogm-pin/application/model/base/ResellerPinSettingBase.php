<?php
Doo::loadCore('db/DooModel');

class ResellerPinSettingBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $reseller_id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $product_id;

    /**
     * @var int Max length is 10.  unsigned.
     */
    public $qty_of_notify;

    /**
     * @var decimal Max length is 10. ,2) unsigned.
     */
    public $fee_deno;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $rate;

    /**
     * @var tinytext
     */
    public $notification_list;

    public $_table = 'reseller_pin_setting';
    public $_primarykey = 'id';
    public $_fields = array('id','reseller_id','product_id','qty_of_notify','fee_deno','rate','notification_list');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'reseller_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'product_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'qty_of_notify' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 10 ),
                        array( 'notnull' ),
                ),

                'fee_deno' => array(
                        array( 'float' ),
                        array( 'notnull' ),
                ),

                'rate' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                ),

                'notification_list' => array(
                        array( 'optional' ),
                )
            );
    }

}