<?php
Doo::loadCore('db/DooModel');

class TokenBase extends DooModel{

    /**
     * @var varchar Max length is 40.
     */
    public $oauth_token;

    /**
     * @var varchar Max length is 16.
     */
    public $merchant_code;

    /**
     * @var int Max length is 11.  unsigned.
     */
    public $expires;

    /**
     * @var varchar Max length is 200.
     */
    public $scope;

    /**
     * @var datetime
     */
    public $create_date;

    /**
     * @var varchar Max length is 15.
     */
    public $ip;

    public $_table = 'token';
    public $_primarykey = 'oauth_token';
    public $_fields = array('oauth_token','merchant_code','expires','scope','create_date','ip');

    public function getVRules() {
        return array(
                'oauth_token' => array(
                        array( 'maxlength', 40 ),
                        array( 'notnull' ),
                ),

                'merchant_code' => array(
                        array( 'maxlength', 16 ),
                        array( 'notnull' ),
                ),

                'expires' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 11 ),
                        array( 'notnull' ),
                ),

                'scope' => array(
                        array( 'maxlength', 200 ),
                        array( 'notnull' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                ),

                'ip' => array(
                        array( 'maxlength', 15 ),
                        array( 'notnull' ),
                )
            );
    }

}