<?php
Doo::loadCore('db/DooModel');

class UserBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 45.
     */
    public $first_name;

    /**
     * @var varchar Max length is 45.
     */
    public $last_name;

    /**
     * @var varchar Max length is 45.
     */
    public $username;

    /**
     * @var varchar Max length is 65.
     */
    public $password;

    /**
     * @var enum 'super_admin','admin').
     */
    public $user_type;

    /**
     * @var varchar Max length is 32.
     */
    public $user_group;

    /**
     * @var varchar Max length is 65.
     */
    public $email;

    /**
     * @var tinyint Max length is 1.  unsigned.
     */
    public $active;

    public $_table = 'user';
    public $_primarykey = 'id';
    public $_fields = array('id','first_name','last_name','username','password','user_type','user_group','email','active');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'first_name' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'last_name' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'username' => array(
                        array( 'maxlength', 45 ),
                        array( 'notnull' ),
                ),

                'password' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'user_type' => array(
                        array( 'notnull' ),
                ),

                'user_group' => array(
                        array( 'maxlength', 32 ),
                        array( 'notnull' ),
                ),

                'email' => array(
                        array( 'maxlength', 65 ),
                        array( 'notnull' ),
                ),

                'active' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 1 ),
                        array( 'notnull' ),
                )
            );
    }

}