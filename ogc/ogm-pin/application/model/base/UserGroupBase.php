<?php
Doo::loadCore('db/DooModel');

class UserGroupBase extends DooModel{

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $id;

    /**
     * @var varchar Max length is 32.
     */
    public $name;

    /**
     * @var text
     */
    public $permission;

    /**
     * @var datetime
     */
    public $create_date;

    public $_table = 'user_group';
    public $_primarykey = 'id';
    public $_fields = array('id','name','permission','create_date');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'optional' ),
                ),

                'name' => array(
                        array( 'maxlength', 32 ),
                        array( 'notnull' ),
                ),

                'permission' => array(
                        array( 'optional' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                )
            );
    }

}