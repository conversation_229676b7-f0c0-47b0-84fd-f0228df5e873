<?php
Doo::loadCore('db/DooModel');

class UserPwdrecallBase extends DooModel{

    /**
     * @var smallint Max length is 5.  unsigned.
     */
    public $id;

    /**
     * @var mediumint Max length is 8.  unsigned.
     */
    public $user_id;

    /**
     * @var varchar Max length is 65.
     */
    public $token;

    /**
     * @var datetime
     */
    public $create_date;

    public $_table = 'user_pwdrecall';
    public $_primarykey = 'id';
    public $_fields = array('id','user_id','token','create_date');

    public function getVRules() {
        return array(
                'id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 5 ),
                        array( 'optional' ),
                ),

                'user_id' => array(
                        array( 'integer' ),
                        array( 'min', 0 ),
                        array( 'maxlength', 8 ),
                        array( 'notnull' ),
                ),

                'token' => array(
                        array( 'maxlength', 65 ),
                        array( 'optional' ),
                ),

                'create_date' => array(
                        array( 'datetime' ),
                        array( 'notnull' ),
                )
            );
    }

}