<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Country Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		
		// Example context menu
		$(document).ready(function() {
			$('#tab-relations').css('height','371px');
			$('#country_form').submit(function(e) {
				var action = 'update';
				
<?php	if ($this->data['subnav'] == 'new_country') { ?>
				action = 'create';
<?php	} ?>

				$.ajax({
					type: "POST",
					url: '/api/country/' + action + '/',
					data: $('#country_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!');
						} else if (XMLHttpRequest.status == 201) {
							alert('Country Created');
							window.location = '/country/list-country';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				})

				return false;
			});
		});
		
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
	<?php $this->inc('parts/head'); ?>
	<?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
		<?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Setting">Setting</a></li>
			<li><a href="javascript:;" title="Country Details">Country Details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onclick="document.location.href = '/country/list-country'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
<?php
		$disabled = '';
		
		if (isset($this->data['rs']->id)) {
			if (!checkPermit('CountryController', 'update')) {
				$disabled = 'disabled="disabled"';
			}
		} else {
			if (!checkPermit('CountryController', 'create')) {
				$disabled = 'disabled="disabled"';
			}
		}
		
		if ($disabled == '') {
?>
			<div class="float-right">
				<button type="button" onClick="$('#country_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
<?php	} ?>
		</div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="country_form" class="block-content form">
					<h1>Country Details</h1>
					
					<div class="columns">
						<div class="col200pxL-left">
							<h2>Side tabs</h2>
							
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Country Details" href="#tab-global">Country Details</a></li>
							</ul>
						</div>
						<div class="col200pxL-right">
							<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>
								
								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
										<p>Edit information as needed below</p>
											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												
												<div class="colx3-left-double required">
													<label for="complex-title">ID</label>
													<span class="relative">
														<input type="hidden" name="old_id" id="old_id" size="3" maxlength="3" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" name="id" id="country_id" size="3" maxlength="3" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" <?php echo $disabled?> />
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Name</label>
													<span class="relative">
														<input type="text" class="full-width" id="country_name" name="name" value="<?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?>" <?php echo $disabled?> />
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
											</fieldset>
										</div>
									</div>																
								</div>
							</div>
						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>
		
		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>