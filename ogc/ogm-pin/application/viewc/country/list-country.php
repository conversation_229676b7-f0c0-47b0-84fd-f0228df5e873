<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Manage Country</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.autocomplete"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<!-- Charts library -->
	<script type="text/javascript">
        var page = 1;
        var lastpage = 1;
        var loading = true;
		// Example context menu
		$(document).ready(function() {
            loadCountries();
			loadCountriesForSearch();
		});
		
		function first() {
			if (loading) return;
			page = 1;
			loadCountries();
		}
		
		function last() {
			if (loading) return;
			page = lastpage;
			loadCountries();
		}
		
        function prev() {
            if (loading) return;
            if (page > 1) {
                page--;
                loadCountries();
            }
        }

        function next() {
            if (loading) return;
            if (page < lastpage) {
                page++;
    			loadCountries();
            }
        }
		
        function refresh() {
            if (loading) return;
            loadCountries();
        }

        function gotoPage(index) {
            if (loading) return;
            page = index;
            loadCountries();
        }
		
        function loadCountries(){
            $.get('/api/country/list_country/page/' + page, null, listCountriesSuccess);
        }

        function listCountriesSuccess(data, textStatus, XMLHttpRequest) {
            var country_list_html = '';
            var d = new Date();
			
            $('#listBottomMessage').text('Last loaded at ' + d.toLocaleTimeString());
            loading = false;
            
            if (data) {
                var pagerStr = '<li class="sep"></li>';
				pagerStr +='<li><a title="First" href="javascript:first();"><img height="16" width="16" src="/images/icons/fugue/control-double-180.png"></a></li>';
                pagerStr +='<li><a title="Previous" href="javascript:prev()"><img height="16" width="16" src="/images/icons/fugue/control-180.png"></a></li>';
				
				var maxpage = data.total_page;
				lastpage = data.total_page;

                if (maxpage > 5){
                    maxpage = 5;
                }

                var offset = 0;
                if (page >= maxpage) {
                    offset = page-3;
                }

                for (var i = 1; i <= maxpage; i++) {
                    if (offset < 0) offset = 0; var pnum = offset + i;
					
					if (pnum <= lastpage) {
						if (page == pnum)
							pagerStr += '<li><a class="current" title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
						else
							pagerStr += '<li><a title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
					}
				}
				
				pagerStr += '<li><a title="Next" href="javascript:next();"><img height="16" width="16" src="/images/icons/fugue/control.png"></a></li>';
				pagerStr += '<li><a title="Last" href="javascript:last();"><img height="16" width="16" src="/images/icons/fugue/control-double.png"></a></li>';
                pagerStr += '<li><div id="totalPageTxt"></div></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><input id="pageNumTxt" type="text" style="width:30px;"></li>';
				pagerStr += '<li class="sep"></li>';
                pagerStr += '<li><a href="javascript:refresh();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a></li>';
				
                $('#pager').html(pagerStr);
				$('#totalPageTxt').html(' of ' + lastpage);
				
				$('#pageNumTxt').bind('blur', function() {
					var no = parseInt($('#pageNumTxt').val());
					
					if (no > 0 && no <= lastpage) {
						gotoPage(no);
					}
					
					$('#pageNumTxt').val('');
				});
				
				$('#pageNumTxt').bind('keyup', function(event){
					if (event.keyCode == 13)
						$('#pageNumTxt').blur();
				});

                if (data.countries) {
                    data = data.countries;
                    var ll = data.length;

                    for (var i = 0; i < ll; i++) {
                        country_list_html += 	'<li>';
                        country_list_html +=		'<a href="/country/country-info/'+data[i].id+'">';
                        country_list_html +=			'<span class="icon" style="background-image: url(/images/icons/web-app/48/Search.png);"></span>';
                        country_list_html +=			'<div style="display: block; height: 40px;padding-top:8px;">'+data[i].name +'</div>';
                        country_list_html +=			'<div class="clear" style="padding-top:5px;"></div>';
                        country_list_html +=			'<small><span style="font-size:9px;">ID:</span> <span style="font-size:11px;"><strong>'+data[i].id+'</strong></span></small>';
                        country_list_html +=		'</a>';

						country_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
                        country_list_html +=			'<li><a title="Edit" href="/country/country-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/pencil.png"></a></li>';
                        country_list_html +=		'</ul>';
                        country_list_html +=	'</li>';
                    }
					
                    $('#list_country_ul').html(country_list_html);
                } else {
                    $('#list_country_ul').html('');
                }
            }
        }

		function loadCountriesForSearch() {
            $('#quick_search').autocomplete('/api/country/list_all_country/', {
               dataType: 'json',
               parse: function(data) {
					var rows = [];

					for (var i = 0; i < data.length; i++){
						rows[i] = { data:data[i], value:data[i].name, result:data[i].name };
					}

					return rows;
                },
                formatItem: function(item){
                    return item.name;
                }
                ,matchContains: true, minChars: 2, max:50
            }).result(function(event, item) {
                location.href = '/country/country-info/' + item.id;
            });
		}
	</script>
</head>

<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		
		<ul id="breadcrumb">
			<li><a href="javascript:void(0);" title="Setting">Setting</a></li>
			<li><a href="javascript:void(0);" title="Manage Countries">Manage Countries</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_12">
			<div class="block-border"><div class="block-content">
				<h1>Countries List</h1>

				<div class="block-controls">
					<input id="quick_search" />
					<ul id="pager" class="controls-buttons"></ul>
				</div>
				
				<ul class="extended-list no-margin icon-user" id="list_country_ul"></ul>
				
				<ul class="message no-margin">
					<li id="listBottomMessage"></li>
				</ul>
			</div></div>
		</section>		
		
		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>