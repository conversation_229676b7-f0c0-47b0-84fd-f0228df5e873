<!DOCTYPE html>
<html lang="en">
<head>
	<title>Pin Generate Login</title>
	<meta charset="utf-8"/>

	<!-- Global stylesheets -->
	<link href="/css/reset.css" rel="stylesheet" type="text/css">
	<link href="/css/common.css" rel="stylesheet" type="text/css">
	<link href="/css/form.css" rel="stylesheet" type="text/css">
	<link href="/css/standard.css" rel="stylesheet" type="text/css">
	<link href="/css/special-pages.css" rel="stylesheet" type="text/css">

	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
	<link rel="icon" type="image/png" href="favicon-large.png">

	<!-- Generic libs -->
	<script type="text/javascript" src="/js/html5.js"></script><!-- this has to be loaded before anything else -->
	<script type="text/javascript" src="/js/jquery-1.12.4.min.js"></script>
	<script type="text/javascript" src="/js/common.js"></script>
	<script type="text/javascript" src="/js/old-browsers.js"></script>		<!-- remove if you do not need older browsers detection -->

	<!-- Template core functions -->
	<script type="text/javascript" src="/js/standard.js"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript" src="/js/jquery.tip.js"></script>
	<script type="text/javascript" src="/js/restaddon.js"></script>

	<!-- example login script -->
	<script type="text/javascript">
		$(document).ready(function() {
			$('#new_pwd').focus();
			$('#forgotUsername').focus();
		});
		
		function submitForgotPwd() {
			if ($('#forgotUsername').val() == '') {
				$('#forgotUsername').focus();
				return;
			}
			
			$.ajax({
				type: "POST",
				url: '/forgot_password/sendlink',
				data: {username: $('#forgotUsername').val()},
				success: function( data, textStatus, XMLHttpRequest ) {
					if (XMLHttpRequest.status == 200) {
						$('#forgot_password_div').hide();
						$('#forgot_password_success_div').show();
					}
				},
				error: function(err) {
					if (err.status == 404) {
						alert('Username not found! Please try again.');
					} else {
						alert('Connection failed! Please try again.');
					}
				}
			});
		}
		
		function resetPassword(token) {
			var newPass = $('#new_pwd').val();
			var confirmPass = $('#confirm_pwd').val();
			
			if (!newPass || newPass.length < 1) {
				$('#new_pwd').focus();
				$('#login-block').removeBlockMessages().blockMessage('Please enter new password', {type: 'warning'});
			} else if (!confirmPass || confirmPass.length == 0) {
				$('#confirm_pwd').focus();
				$('#login-block').removeBlockMessages().blockMessage('Please enter confirm password', {type: 'warning'});
			} else {
				$.ajax({
					type: "POST",
					url: '/forgot_password/'+token,
					data: $('#forgot-form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							$('#reset_password_div').hide();
							$('#reset_password_success_div').show();
						} 
					},
					error: function(err) {
						if (err.status == 404) {
							alert(eval(err.responseText));
						} else {
							alert('Connection failed! Please try again.');
						}
					}
				});
			}
		}
	</script>
</head>

<!-- the 'special-page' class is only an identifier for scripts -->
<body class="special-page login-bg dark">
<!-- The template uses conditional comments to add wrappers div for ie8 and ie7 - just add .ie, .ie7 or .ie6 prefix to your css selectors when needed -->
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->

	<!--section id="message">
		<div class="block-border"><div class="block-content no-title dark-bg">
			<p class="mini-infos">Reset your password</p>
		</div></div>
	</section-->

	<section id="login-block">
		<div class="block-border"><div class="block-content">
			<h1>Admin</h1>
			<div class="block-header">Reset your password</div>

			<form class="form with-margin" name="forgot-form" id="forgot-form" method="POST">
<?php
		if (isset($this->data['rs'])) {
			if (isset($this->data['rs']['token'])) {
?>
				<div id="reset_password_div">
					<p class="inline-small-label">
						<label for="login"><span>New password</span></label>
						<input type="password" name="new_pwd" id="new_pwd" class="full-width" value="">
					</p>
					<p class="inline-small-label">
						<label for="login"><span>Confirm password</span></label>
						<input type="password" name="confirm_pwd" id="confirm_pwd" class="full-width" value="">
					</p>
					<button type="button" class="float-right" onClick="resetPassword('<?php echo $this->data['rs']['token'];?>');">Submit</button>
					<p class="input-height"></p>
				</div>
				<div id="reset_password_success_div" style="display:none;">Your password is reset successfully! Please use the new password to login.</div>
<?php	} else { ?>
				<div><?php echo $this->data['rs']['error']?></div>
<?php
			}
		} else {
?>
				<div id="forgot_password_div">
					<p class="inline-small-label">
						<label for="login"><span class="big">User name</span></label>
						<input type="text" name="forgotUsername" id="forgotUsername" class="full-width" value="">
					</p>
					<button type="button" class="float-right" onClick="submitForgotPwd();">Submit</button>
				</div>
				<div id="forgot_password_success_div" style="display:none;">Please check you email to reset your password.</div>
				<br/>
<?php	} ?>
			</form>
		</div></div>
	</section>
<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>