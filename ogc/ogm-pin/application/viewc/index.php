<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Dashboard</title>
	<meta charset="utf-8">
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->

	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="/js/standard.ie.js"></script><![endif]-->
</head>

<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->

	<!-- Header -->
	<?php $this->inc('parts/head'); ?>
	<?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0)" title="Home">Home</a></li>
			<li><a href="javascript:void(0)" title="Dashboard">Dashboard</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
<?php	global $user_module, $client_module, $reseller_module, $product_module, $pin_module, $log_module, $setting_module; ?>
	<article class="container_12">
		<section class="grid_4">
			<!--<div class="block-border"><div class="block-content">-->
				<h1>Quick Tools</h1>
				
				<ul class="favorites no-margin with-tip" title="Click to enter">
<?php	if ($reseller_module) { ?>
					<li>
						<img src="/images/icons/web-app/48/reseller.png" width="48" height="48">
						<a href="/reseller/list-reseller">Manage Resellers<br>
						<small>Manage Resellers Info</small></a>
					</li>
<?php
		}

		if ($client_module) {
?>
					<li>
						<img src="/images/icons/web-app/48/Modify.png" width="48" height="48">
						<a href="/client/list-client">Manage Clients<br>
						<small>Manage Client Info</small></a>
					</li>
<?php	}

		if ($user_module) {
?>
					<li>
						<img src="/images/icons/web-app/48/Profile.png" width="48" height="48">
						<a href="/user/list-user">Manage Users<br>
						<small>Manage Users</small></a>
					</li>
<?php
		}
		
		if ($product_module) {
?>
					<li>
						<img src="/images/icons/web-app/48/Picture.png" width="48" height="48">
						<a href="/product/list-product">Manage Products<br>
						<small>Manage Products</small></a>
					</li>
<?php
		}

		if (checkPermit('RequestController', 'listRequest')) {
?>
					<li>
						<img src="/images/icons/namecards.png" width="48" height="48">
						<a href="/request/list-request">Manage Pin Requests<br>
						<small>Manage Pin Requests</small></a>
					</li>
<?php
		}

		if (checkPermit('PinController', 'listPin')) {
?>
					<li>
						<img src="/images/icons/namecards.png" width="48" height="48">
						<a href="/pin/search-pin">Manage Pins<br>
						<small>Manage Pins</small></a>
					</li>
<?php
		}

		if (checkPermit('LogController', '*')) {
?>
					<li>
						<img src="/images/icons/web-app/48/Info.png" width="48" height="48">
						<a href="/log" target="_blank">Logs<br>
						<small>View Logs</small></a>
					</li>
<?php
		}

		if ($setting_module) {
?>
					<li>
						<img src="/images/icons/web-app/48/Save.png" width="48" height="48">
<?php		if (checkPermit('UserGroupController', 'listUserGroup')) { ?>
						<a href="/user-group/list-user-group">Setting<br>
<?php		} else { ?>
						<a href="/user-group/list-country">Setting<br>
<?php		} ?>
						<small>Manage Settings</small></a>
					</li>
<?php	} ?>
				</ul>
		</section>
		
		<section class="grid_8" style="display: none;">
			<div class="block-border"><div class="block-content">

				<!-- We could put the menu inside a H1, but to get valid syntax we'll use a wrapper -->
				<!--div class="h1 with-menu"-->
				<div class="h1">
					<h1>Top Products by Client</h1>
					<div class="menu">
					</div>

				</div>
			
				<div class="block-controls">
					<ul class="controls-tabs js-tabs same-height with-children-tip">
						<li><a href="#tab-stats" title="Clients"><img src="/images/icons/web-app/24/Bar-Chart.png" width="24" height="24"></a></li>
					</ul>
				</div>
				
				<form class="form" id="tab-stats" method="post" action="">
					<div id="chart_div" style="height:360px;"></div>
				</form>
				
				<div id="tab-comments" class="with-margin"></div>
				
				
				<ul class="message no-margin">
                    <li>Data last updated <strong><span id="listBottomMessage"></span></strong></li>
				</ul>
			</div></div>
		</section>
		
		<div class="clear"></div>
		
	</article>
	
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>