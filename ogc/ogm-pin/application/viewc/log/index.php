<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Activity logs</title>
        <link href="/css/mini.php?files=jquery.datepicker" rel="stylesheet" type="text/css">
        
        <script type="text/javascript" src="/js/jquery-1.12.4.min.js"></script>
        <script type="text/javascript" src="/js/jquery.datepicker.js"></script>
        
        <script>
            jQuery.browser = {};
            (function () {
                jQuery.browser.msie = false;
                jQuery.browser.version = 0;
                if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
                    jQuery.browser.msie = true;
                    jQuery.browser.version = RegExp.$1;
                }
            })();
            $(function(){
               $('.rstrigger').click(function(evt){
                   $(this).parent().find('.jsonrs').toggle();
               })
               
                $('#start_date').datepicker({dateFormat: 'yy-mm-dd'});
                $('#end_date').datepicker({dateFormat: 'yy-mm-dd'});               
            });
        </script>
        <style>
            body{
                font-family: Arial,verdana;
                font-size:12px;
                margin: auto;
                width:100%;
            }
            #mainframe{
                margin: auto;
                width:100%;
                padding:0;
            }
            #main{
                margin: auto;            
                width:950px;height:1000px;display:block;
                position: relative;
            }
            table thead td{
                background-color: #000;color:#fff;
                font-weight:bold;
                border:1px #000 solid;
            }

			.paginate, .current, .inactivePrev, .inactiveNext, .prev, .next {
				font-family: Verdana,Helvetica,Arial,sans-serif;
				font-size:12px;
				border:1px solid #D8D8D8;
				float:left;
				height:20px;
				line-height:20px;
				margin-right:2px;
				overflow:hidden;
				padding:0 6px;
				text-decoration:none;
			}

			a:hover .paginate{
				border-color:#006699;
			}

			.current {
				border-color:#006699;
				background-color:#006699;
				color:#fff;
				font-weight:bold;
			}

			.inactivePrev, .inactiveNext{
				color:#999;
			}

			.rstrigger{
				cursor: pointer
			}

			.src-code {
				background-color: #FFFFC5;
				border: 1px dashed #55AED0;
				font-family: 'Courier New',Courier,monospace;
				font-weight: normal;
				line-height: 1.1em;
				padding: 1em;
			}
        </style>
    </head>
    <body>
        <div id="mainframe">
            <div id="main">
                <br/>
                <form action="<?php echo $this->data['filter'] ?>" method="get" >
                    <div>
                        <div style="display:inline-block;margin-right:40px">
                            <span>Category </span>
                            <select name="module">
                                <?php 
                                    genOption($this->data['module_list'], $this->data['module'])
                                ?>
                            </select>                        
                        </div>
                        <div style="display:inline-block;margin-right:40px">
                            <span>From </span>
                            <input id="start_date" name="start_date" type="text" value="<?php echo $this->data['start_date'] ?>"/>                    
                        </div>
                        <div style="display:inline-block;margin-right:40px">
                            <span>To </span>
                            <input id="end_date" name="end_date" type="text" value="<?php echo $this->data['end_date'] ?>"/>                                         
                        </div>
                        <input type="submit" value="filter"/>
                    </div>
                </form>
                <br/>
                <hr style="border:1px #999 dashed;border-bottom: none; border-left: none; border-right: none;"/>
                <br/>
                <table style="width:100%;border:1px #000 solid;" border="1" cellpadding="6" cellspacing="0">
                    <thead>
                        <tr>
                            <td class="time" style="width: 65px;">Time</td>
                            <td class="category" style="width: 90px;">Category</td>
                            <td class="description">Description</td>
                            <td class="ip" style="width: 64px;">User</td>
                            <td class="ip" style="width: 70px;">IP</td>
                        </tr>
                    </thead>         
                    <?php if(empty($this->data['logs'])): ?>
                        <tr valign="middle"><td colspan="5" style="text-align:center"><h3>No activity log found.</h3></td></tr>
                    <?php else: ?>
                    <?php foreach($this->data['logs'] as $l): ?>
                    <?php $msg = explode(' - ', $l->msg); ?>
                    <tr valign="top">
                        <td class="time" style="font-size:11px;"><?php echo $l->logtime ?></td>
                        <td class="category"><?php echo $l->module ?></td>
                        <td class="description">
                            <div class="rstrigger"><a href="javascript:void(0);"><?php echo $msg[0]?></a></div>
                            <div class="jsonrs" style="display:none;">
                            <?php
                                if(!empty($msg[1])){
                                    $j = (array)json_decode($msg[1]);                                            
                                    foreach($j as $k=>$v){
                                        if(empty($v)){
                                            unset($j[$k]);
                                        }
                                    }
                                     echo '<pre class="src-code">'. str_replace('array (', "\n(", htmlentities(var_export($j, true))) .'<br/></pre>';
                                }
                            ?>
                            </div>
                        </td>
                        <td class="ip"><?php echo $l->act_by ?></td>
                        <td class="ip"><?php echo $l->ip ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </table>
                <br/>
                <div style="display:block;width:100%;">
                    <?php echo $this->data['pager'] ?>
                </div>
            </div>
        </div>

    </body>
</html>
