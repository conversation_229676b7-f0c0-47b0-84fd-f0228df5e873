<!DOCTYPE html>
<html lang="en">
<head>
	<title>Pin Generate Login</title>
	<meta charset="utf-8"/>
	
	<!-- Global stylesheets -->
	<link href="css/reset.css" rel="stylesheet" type="text/css">
	<link href="css/common.css" rel="stylesheet" type="text/css">
	<link href="css/form.css" rel="stylesheet" type="text/css">
	<link href="css/standard.css" rel="stylesheet" type="text/css">
	<link href="css/special-pages.css" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
	<link rel="icon" type="image/png" href="favicon-large.png">
	
	<!-- Generic libs -->
	<script type="text/javascript" src="js/html5.js"></script><!-- this has to be loaded before anything else -->
	<script type="text/javascript" src="js/jquery-1.12.4.min.js"></script>
	<!-- Template core functions -->
	<script type="text/javascript" src="js/common.js"></script>
	<!-- remove if you do not need older browsers detection -->
	<script type="text/javascript" src="js/old-browsers.js"></script>		
	
	<script type="text/javascript" src="js/standard.js"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript" src="js/jquery.tip.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			// We'll catch form submission to do it in AJAX, but this works also with JS disabled
			$('#login-form').submit(function(event) {
				// Stop full page load
				event.preventDefault();
				
				// Check fields
				var login = $('#login').val();
				var pass = $('#pass').val();
				
				if (!login || login.length == 0) {
					$('#login-block').removeBlockMessages().blockMessage('Please enter your user name', {type: 'warning'});
				} else if (!pass || pass.length == 0) {
					$('#login-block').removeBlockMessages().blockMessage('Please enter your password', {type: 'warning'});
				} else {
					var submitBt = $(this).find('button[type=submit]');
					submitBt.disableBt();
					
					// Request
					var data = {
						login: login,
						pass: pass
					};
					
					// Start timer
					var sendTimer = new Date().getTime();
					
					// Send
					$.ajax({
						url: '/login',
						dataType: 'json',
						type: 'POST',
						data: data,
						success: function(data, textStatus, XMLHttpRequest) {
							if (data) {
								if (data.redirect) {
									document.location.href = data.redirect;
									return;
								} else {
									// Message
									$('#login-block').removeBlockMessages().blockMessage(data.error || 'An unexpected error occured, please try again', {type: 'error'});

									submitBt.enableBt();
								}
							}
						},
						error: function(XMLHttpRequest, textStatus, errorThrown) {
							// Message
							$('#login-block').removeBlockMessages().blockMessage(eval(XMLHttpRequest.responseText), {type: 'error'});
							
							submitBt.enableBt();
						}
					});
					
					// Message
					$('#login-block').removeBlockMessages().blockMessage('Please wait, checking login...', {type: 'loading'});
				}
			});
		});
	</script>
</head>

<!-- the 'special-page' class is only an identifier for scripts -->
<body class="special-page login-bg dark">
<!-- The template uses conditional comments to add wrappers div for ie8 and ie7 - just add .ie, .ie7 or .ie6 prefix to your css selectors when needed -->
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->

	<!--section id="message">
		<div class="block-border"><div class="block-content no-title dark-bg">
			<p class="mini-infos">Please login with your <b>username</b> &amp; <b>password</b></p>
		</div></div>
	</section-->
	
	<section id="login-block">
		<div class="block-border"><div class="block-content">
				
			<h1>Pin Generation</h1>
			<div class="block-header">Please login</div>
				
			<!--p class="message error no-margin">Error message</p-->
			
			<form class="form with-margin" name="login-form" id="login-form" method="POST">
				<p class="inline-small-label">
					<label for="login"><span class="big">User name</span></label>
					<input type="text" name="login" id="login" class="full-width" value="">
				</p>
				<p class="inline-small-label">
					<label for="pass"><span class="big">Password</span></label>
					<input type="password" name="pass" id="pass" class="full-width" value="" autocomplete="off">
				</p>
				
				<button type="submit" class="float-right">Login</button>
				<p class="input-height">
					<!--input type="checkbox" name="keep-logged" id="keep-logged" value="1" class="mini-switch" checked="checked"/>
					<label for="keep-logged" class="inline">Keep me logged in</label-->
				</p>
			</form>
			
			<legend><a href="/forgot_password">Lost password?</a></legend>
		</div></div>
	</section>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>