<!-- Main nav -->
<?php
	global $user_module, $client_module, $reseller_module, $product_module, $pin_module, $log_module, $setting_module;
	
	$user_module = checkPermit('UserController', 'listUser');
	$client_module = checkPermit('ClientController', 'listClient');
	$reseller_module = checkPermit('ResellerController', 'listReseller');
	$product_module = checkPermit('ProductController', 'listProduct');
	$log_module = checkPermit('LogController', '*');
	$report_module = checkPermit('ReportController', '*');
	$setting_module = false;
	$pin_module = false;
	
	if (checkPermit('RequestController', 'listRequest') == true || checkPermit('PinController', 'listPin') == true) {
		$pin_module = true;
	}
	
	if (checkPermit('UserGroupController', 'listUserGroup') == true || checkPermit('CountryController', 'listCountry') == true) {
		$setting_module = true;
	}
?>
<nav id="main-nav">
	<ul class="container_12">
		<li class="home<?php echo ($this->data['nav'] == 'home')?' current':'' ?>"><a href="/" title="Home">Home</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'dashboard')?' class="current"':'' ?>><a href="/" title="Dashboard">Dashboard</a></li>
				<li<?php echo ($this->data['subnav'] == 'my_account')?' class="current"':'' ?>><a href="/user/my-account" title="My profile">My Account</a></li>
			</ul>
		</li>
<?php	if ($user_module) { ?>
		<li class="users<?php echo ($this->data['nav'] == 'user')?' current':'' ?>"><a href="/user/list-user" title="Users">Users</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'manage_user')?' class="current"':'' ?>><a href="/user/list-user">Manage Users</a></li>
<?php		if (checkPermit('UserController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_user')?' class="current"':'' ?>><a href="/user/new-user">Add User</a></li>
<?php			} ?>
			</ul>
		</li>
<?php
		}
		
		if ($reseller_module) {
?>
		<li class="reseller<?php echo ($this->data['nav'] == 'reseller')?' current':'' ?>"><a href="/reseller/list-reseller" title="Reseller">Resellers</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'manage_reseller')?' class="current"':'' ?>><a href="/reseller/list-reseller">Manage Reseller</a></li>
<?php		if (checkPermit('ResellerController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_reseller')?' class="current"':'' ?>><a href="/reseller/new-reseller">Add Reseller</a></li>
<?php		} ?>
			</ul>
		</li>
<?php	}
		
		if ($client_module) {
?>
		<li class="write<?php echo ($this->data['nav'] == 'client')?' current':'' ?>"><a href="/client/list-client" title="Clients">Clients</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'manage_client')?' class="current"':'' ?>><a href="/client/list-client">Manage Clients</a></li>
<?php		if (checkPermit('ClientController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_client')?' class="current"':'' ?>><a href="/client/new-client">Add Client</a></li>
<?php		} ?>
			</ul>
		</li>
<?php	}
		
		if ($product_module) {
?>
		<li class="medias<?php echo ($this->data['nav'] == 'product')?' current':'' ?>"><a href="/product/list-product" title="Products">Products</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'manage_product')?' class="current"':'' ?>><a href="/product/list-product">Manage Product</a></li>
<?php		if (checkPermit('ProductController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_product')?' class="current"':'' ?>><a href="/product/new-product">Add Product</a></li>
<?php		} ?>
			</ul>
		</li>
<?php	}
		
		if ($pin_module) {
?>
		<li class="pin<?php echo ($this->data['nav'] == 'request')?' current':'' ?>"><a href="/request/list-request" title="Pin">Pin</a>
			<ul>
				<li<?php echo ($this->data['subnav'] == 'manage_request')?' class="current"':'' ?>><a href="/request/list-request">Manage Pin Request</a></li>
<?php		if (checkPermit('RequestController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_request')?' class="current"':'' ?>><a href="/request/new-request">Request Pin</a></li>
<?php
			}
			
			if (checkPermit('PinController', 'listPin')) {
?>
				<li<?php echo ($this->data['subnav'] == 'manage_pin')?' class="current"':'' ?>><a href="/pin/search-pin">Manage Pin</a></li>
<?php		} ?>
			</ul>
		</li>
<?php	}
		
		if ($log_module) {
?>
		<li class="logs">
			<a href="/log" target="_blank" title="Logs">Logs</a>
		</li>

<?php	}
		
		if ($report_module) {
?>
		<li class="stats<?php echo ($this->data['nav']=='report')?' current':'' ?>"><a href="javascript:void(0)" title="Reports">Reports</a>
			<ul>
				<li<?php echo ($this->data['subnav']=='performance_report')?' class="current"':'' ?>><a href="/report/search-report">Performance Report</a></li>
				<!--li<?php echo ($this->data['subnav']=='movement_report')?' class="current"':'' ?>><a href="/report/search-movement-report">Movement Report</a></li-->
			</ul>
		</li>
<?php	}
		
		if ($setting_module) {
?>
		<li class="backup<?php echo ($this->data['nav'] == 'setting')?' current':'' ?>"><a href="/user-group/list-user-group" title="Setting">Setting</a>
			<ul>
<?php		if (checkPermit('UserGroupController', 'listUserGroup')) { ?>
				<li<?php echo ($this->data['subnav'] == 'manage_user_group')?' class="current"':'' ?>><a href="/user-group/list-user-group">Manage User Group</a></li>
<?php
			}
			
			if (checkPermit('UserGroupController', 'create')) {
?>
				<li<?php echo ($this->data['subnav'] == 'new_user_group')?' class="current"':'' ?>><a href="/user-group/new-user-group">Add User Group</a></li>
<?php
			}

			if (checkPermit('CountryController', 'listCountry')) {
?>
				<li<?php echo ($this->data['subnav'] == 'manage_country')?' class="current"':'' ?>><a href="/country/list-country">Manage Country</a></li>
<?php			if (checkPermit('CountryController', 'create')) { ?>
				<li<?php echo ($this->data['subnav'] == 'new_country')?' class="current"':'' ?>><a href="/country/new-country">Add Country</a></li>
<?php
				}
			}
?>
			</ul>
		</li>
<?php
		}
?>

	</ul>
</nav>
<!-- End main nav -->

<!-- Sub nav -->
<div id="sub-nav">
	<!--div class="container_12">
		<a href="javascript:void(0)" title="Help" class="nav-button"><b>Help</b></a>
	</div-->
</div>
<!-- End sub nav -->