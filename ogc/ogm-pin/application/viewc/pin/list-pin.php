<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Manage Pins</title>
	<meta charset="utf-8"/>
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.autocomplete"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<script type="text/javascript">
        var page = 1;
        var lastpage = 1;
        var loading = true;
		
		$(document).ready(function() {
            loadPins();

			$('#paradigm_all').click(function() {
				var checked_status = this.checked;
				
				$('input[name="pin[]"]').each(function() {
					this.checked = checked_status;
				});
			});
		});
		
		function first() {
			if (loading) return;
			page = 1;
			loadPins();
		}
		
		function last() {
			if (loading) return;
			page = lastpage;
			loadPins();
		}
		
		function prev() {
			if (loading) return;
			if (page > 1) {
				page--;
				loadPins();
			}
		}
		
		function next() {
			if (loading) return;
			if (page < lastpage) {
				page++;
				loadPins();
			}
		}
		
		function refresh() {
			if (loading) return;
			loadPins();
			$('#paradigm_all').removeAttr('checked');
		}
		
		function gotoPage(index) {
			if (loading) return;
			page = index;
			loadPins();
		}
		
		function loadPins() {
			$.get('/api/pin/list_pin/page/' + page <?php echo $this->data['options'] ?>, null, listPinsSuccess);
			$('#paradigm_all').removeAttr('checked');
		}
		
		function activate() {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					var pins = new Array();

					$('input[name="pin[]"]:checked').each(function() {
						pins.push($(this).val());
					});

					if (pins.length > 0) {
						$.ajax({
							type: "POST",
							url: '/api/pin/activate/',
							data: {pins: pins, remarks: remarks},
							success: function(data, textStatus, XMLHttpRequest) {
								refresh();
							},
							error: function(XMLHttpRequest, textStatus, errorThrown) {
								alert(XMLHttpRequest.responseText);
							}
						});

					} else {
						alert('Please select pin!');
					}
				} else {
					alert('Please enter your remarks!');

					return false;
				}
			}
		}
		
		function deactivate() {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					var pins = new Array();

					$('input[name="pin[]"]:checked').each(function() {
						pins.push($(this).val());
					});

					if (pins.length > 0) {
						$.ajax({
							type: "POST",
							url: '/api/pin/deactivate/',
							data: {pins: pins, remarks: remarks},
							success: function(data, textStatus, XMLHttpRequest) {
								refresh();
							},
							error: function(XMLHttpRequest, textStatus, errorThrown) {
								alert(XMLHttpRequest.responseText);
							}
						});

					} else {
						alert('Please select pin!');
					}
				} else {
					alert('Please enter your remarks!');

					return false;
				}
			}
		}
		
        function listPinsSuccess(data, textStatus, XMLHttpRequest) {
            var pin_list_html = '';
            var d = new Date();
			
            $('#listBottomMessage').text('Last loaded at ' + d.toLocaleTimeString());
            loading = false;
            
            if (data) {
				var pagerStr = '<li class="sep"></li>';
                pagerStr +='<li><a title="First" href="javascript:first();"><img height="16" width="16" src="/images/icons/fugue/control-double-180.png"></a></li>';
				pagerStr += '<li><a title="Previous" href="javascript:prev();"><img height="16" width="16" src="/images/icons/fugue/control-180.png"></a></li>';
                
				var maxpage = data.total_page;
				lastpage = data.total_page;
				
                if (maxpage > 5){
                    maxpage = 5;
                }
				
                var offset = 0;
                if (page >= maxpage) {
                    offset = page-3;
                }
				
                for (var i = 1; i <= maxpage; i++) {
                    if (offset < 0) offset = 0; var pnum = offset + i;
					
					if (pnum <= lastpage) {
						if (page == pnum)
							pagerStr += '<li><a class="current" title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
						else
							pagerStr += '<li><a title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
					}
				}
				
				pagerStr += '<li><a href="javascript:next();" title="Next"><img height="16" width="16" src="/images/icons/fugue/control.png"></a></li>';
				pagerStr += '<li><a href="javascript:last();" title="Last"><img height="16" width="16" src="/images/icons/fugue/control-double.png"></a></li>';
				pagerStr += '<li><div id="totalPageTxt"></div></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><input id="pageNumTxt" type="text" style="width:30px;"></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><a href="javascript:refresh();" title="Refresh"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a></li>';
				
				if (data.total_page > 0) {
<?php	if (checkPermit('PinController', 'exportAll')) { ?>
					pagerStr += '<li class="sep"></li>';
					pagerStr += '<li><a title="Export CSV" href="/api/pin/export<?php echo $this->data['export_options']?>"><b>Export CSV</b></a></li>';
<?php	} else { ?>
				if (data.export_allow) {
					pagerStr += '<li class="sep"></li>';
					pagerStr += '<li><a title="Export CSV" href="/api/pin/export_self<?php echo $this->data['export_options']?>"><b>Export CSV</b></a></li>';
				}
<?php	} ?>
				}
                
                $('#pager').html(pagerStr);
				$('#totalPageTxt').html(' of ' + lastpage);
				
				$('#pageNumTxt').bind('blur', function() {
					var no = parseInt($('#pageNumTxt').val());
					
					if (no > 0 && no <= lastpage) {
						gotoPage(no);
					}
					
					$('#pageNumTxt').val('');
				});
				
				$('#pageNumTxt').bind('keyup', function(event){
					if (event.keyCode == 13)
						$('#pageNumTxt').blur();
				});
				
                if (data.pins) {
                    data = data.pins;
                    var ll = data.length;
					var pinNumber = '******';
					
                    for (var i = 0; i < ll; i++) {
                        pin_list_html += 	'<li><span style="padding-left:5px;padding-right:5px;padding-top:15px;">';
						
<?php	if (checkPermit('PinController', 'deactivate') || checkPermit('PinController', 'activate')) { ?>
						pin_list_html +=		'<input type="checkbox" name="pin[]" value="'+data[i].id+'" id="request_'+data[i].id+'">';
<?php	} ?>
						pin_list_html +=		'</span>';
                        pin_list_html +=		'<a href="/pin/pin-info/'+data[i].id+'">';
                        pin_list_html +=			'<span class="icon" style="background-image: url(/images/icons/namecards.png);"></span>';
						pin_list_html +=			'<div style="display: block; height: 40px;padding-top:8px;">'+data[i].serial+'</div>';
						pin_list_html +=			'<div class="clear" style="padding-top:5px;"></div>';
                        pin_list_html +=			'<small>';
						pin_list_html +=				'<span style="font-size:9px;">Status:</span> ';
						
						var statusImg = 'status';
						
						if (data[i].status == 0) {
							statusImg = 'status-busy';
						} else if (data[i].status == 2) {
							statusImg = 'status-away';
						}
						
						pin_list_html +=				'<img style="width:16px;height:16px;position:absolute;" src="/images/icons/fugue/'+statusImg+'.png" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
						pin_list_html +=				'<span style="font-size:9px;">Batch ID:</span> ';
						pin_list_html +=				'<span style="font-size:11px;"><strong>'+data[i].batch_id+'</strong></span>&nbsp;&nbsp;&nbsp;';
						
						if (data[i].pin !== null) {
							pinNumber = data[i].pin;
						}
						
						pin_list_html +=				'<span style="font-size:9px;">Pin:</span> <span style="font-size:11px;"><strong>'+pinNumber+'</strong></span>&nbsp;&nbsp;&nbsp;';
						pin_list_html +=				'<span style="font-size:9px;">Start Date:</span> <span style="font-size:11px;"><strong>'+data[i].start_date+'</strong></span>&nbsp;&nbsp;&nbsp;';
						pin_list_html +=				'<span style="font-size:9px;">Expiry Date:</span> <span style="font-size:11px;"><strong>'+data[i].end_date+'</strong></span>&nbsp;&nbsp;&nbsp;';
						pin_list_html +=				'<span style="font-size:9px;">Redeem:</span> <span style="font-size:11px;"><strong>'+((data[i].redeem == 1) ? 'Yes' : 'No')+'</strong></span>';
						pin_list_html +=			'</small>';
                        pin_list_html +=		'</a>';
						
						pin_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
                        pin_list_html +=			'<li><a title="Edit" href="/pin/pin-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/magnifier.png"></a></li>';
                        pin_list_html +=		'</ul>';
                        pin_list_html +=	'</li>';
                    }
					
                    $('#list_pin_ul').html(pin_list_html);
                } else {
                    $('#list_pin_ul').html('');
                }
            }
            
            $('#quick_search').keyup(function(evt) {
                if (evt.keyCode == 13) {
                    document.location.href = '/pin/list-pin/batch-id/' + $(this).val();
                }
            });            
        }
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Pins">Pins</a></li>
			<li><a href="javascript:;" title="Manage Pins">Manage Pins</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12">
			<div class="float-left">
				<button onClick="document.location.href = '/pin/search-pin'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to Search</button>
			</div>
			<div class="float-right">
<?php	if (checkPermit('PinController', 'activate')) { ?>
				<button type="button" id="activate" onClick="activate(); return false;"><img style="height:16px;width:16px;" src="/images/icons/fugue/status.png" /> Activate</button>
<?php
		}
		
		if (checkPermit('PinController', 'activate')) {
?>
				<button type="button" id="deactivate" class="red" onClick="deactivate(); return false;"><img style="height:16px;width:16px;" src="/images/icons/fugue/status-busy.png" /> Deactivate</button>
<?php	} ?>
			</div>
		</div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_12">
			<div class="block-border"><div class="block-content">
				<h1>Pins List</h1>
				
				<div class="block-controls">
<?php	if (checkPermit('PinController', 'deactivate') || checkPermit('PinController', 'activate')) { ?>
					<div style="float:left;margin-left:13px;margin-top:15px;position:absolute;"><input type="checkbox" id="paradigm_all" /></div>
					<div class="clear"></div>
<?php	} ?>
					<input id="quick_search" value="<?php echo $this->data['batch_id']?>" />
					<ul id="pager" class="controls-buttons"></ul>
				</div>
				
				<ul class="extended-list no-margin icon-user" id="list_pin_ul"></ul>
				
				<ul class="message no-margin">
					<li id="listBottomMessage"></li>
				</ul>
			</div></div>
		</section>
		
		<div class="clear"></div>
	</article>
	<!-- End content -->
    <?php $this->inc('parts/footer'); ?>
	<!--[if lt IE 8]></div><![endif]-->
	<!--[if lt IE 9]></div><![endif]-->
</body>
</html>