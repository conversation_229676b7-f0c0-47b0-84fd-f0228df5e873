<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Pin Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.datepicker" rel="stylesheet" type="text/css">
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.datepicker"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		var remarks_page = 1;
		var remarks_lastpage = 1;
		var remarks_loading = true;
		
		$(document).ready(function() {
			$('#tab-relations').css('height','371px');
			
<?php	if (checkPermit('PinController', 'update')) { ?>
			$('#end_date').datepicker({dateFormat: 'yy-mm-dd'});
<?php	} else { ?>
			$('#end_date').attr('disabled', 'disabled');
<?php	} ?>
			
			if (window.location.hash == '#&tab-remarks') {
				loadRemarks();
			}
			
			$('#pin_form').submit(function(e) {
				$.ajax({
					type: "POST",
					url: '/api/pin/update/',
					data: $('#pin_form').serialize(),
					success: function(data, textStatus, XMLHttpRequest) {
						if (XMLHttpRequest.status == 200) {
							alert("Update Pin's Expiry Date Success!");
						}
					},
					error: function(XMLHttpRequest, textStatus, errorThrown) {
						alert(eval(XMLHttpRequest.responseText));
					}
				});

				
                return false;
			});
		});
		
		function activate(id) {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					$('#activate-btn').attr('disabled', 'disabled');

					var pins = new Array();

					pins.push(id);

					$.ajax({
						type: "POST",
						url: '/api/pin/activate/',
						data: {pins: pins, remarks: remarks},
						success: function( data, textStatus, XMLHttpRequest ) {
							alert('Pin has been activated');
							window.location = '/pin/pin-info/'+id;
						},
						error: function(err) {
							alert(eval(err.responseText));
						}
					});

				} else {
					alert('Please enter your remarks!');
				}
			}
			
			return false;
		}

		function deactivate(id) {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
			
				if (remarks != '') {
					$('#deactivate-btn').attr('disabled', 'disabled');

					var pins = new Array();

					pins.push(id);
					$.ajax({
						type: "POST",
						url: '/api/pin/deactivate/',
						data: {pins: pins, remarks: remarks},
						success: function( data, textStatus, XMLHttpRequest ) {
							alert('Pin has been deactivated');
							window.location = '/pin/pin-info/'+id;
						},
						error: function(err) {
							alert(eval(err.responseText));
						}
					});

				} else {
					alert('Please enter your remarks!');
				}
			}
			
			return false;
		}
		
		function prevRemarks() {
			if (remarks_loading) return;
			if (remarks_page > 1) {
				remarks_page--;
				loadRemarks();
			}
		}

		function nextRemarks() {
			if (remarks_loading) return;
			if (remarks_page < remarks_lastpage){
				remarks_page++;
				loadRemarks();
			}
		}

		function refreshRemarks() {
			if (remarks_loading) return;
			loadRemarks();
		}

		function gotoPageRemarks(index) {
			if (remarks_loading) return;
			remarks_page = index;
			loadRemarks();
		}
		
		function loadRemarks() {
			$('#list_remarks_ul').html('');

			$.get('/api/pin/list_remarks/' + $('#id').val()+'/page/' + remarks_page, null,
			function(data, textStatus, XMLHttpRequest) {
				var list_remarks_html = '';
				var dd = new Date();

				$('#listRemarksBottomMessage').text('Last loaded at ' + dd.toLocaleTimeString());
				remarks_loading = false;

				if (data) {
					var remarksPagerStr ='<li><a title="Previous" href="javascript:prevRemarks();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a></li>';

					for (var j = 1; j <= data.total_page; j++) {
						if (remarks_page == j)
							remarksPagerStr += '<li><a class="current" title="Page '+j+'" href="javascript:gotoPageRemarks('+j+');"><b>'+j+'</b></a></li>';
						else
							remarksPagerStr += '<li><a title="Page '+j+'" href="javascript:gotoPageRemarks('+j+');"><b>'+j+'</b></a></li>';
					}

					remarks_lastpage = data.total_page;

					remarksPagerStr += '<li><a title="Next" href="javascript:nextRemarks();">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a></li>';
					remarksPagerStr += '<li class="sep"></li>';
					remarksPagerStr += '<li><a href="javascript:refreshRemarks();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"/></a></li>';

					$('#remarks_pager').html(remarksPagerStr);

					if (data.remarks) {
						data = data.remarks;
						var ll = data.length;

						for (var i = 0; i < ll; i++) {
							list_remarks_html += 	'<li>';
							list_remarks_html +=		'<span class="icon" style="background-image:url(/images/icons/write_2states.png)"></span>';
							list_remarks_html +=		data[i].remarks ;
							list_remarks_html +=		'<div class="" style="padding-top:5px;"></div>';
							list_remarks_html +=		'<small>';
							
							if (data[i].User != null) {
								list_remarks_html +=		'	<span style="font-size:9px;">From:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].User.first_name+' '+data[i].User.last_name+'</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							} else {
								list_remarks_html +=		'	<span style="font-size:9px;">From:</span> <span style="font-size:11px;text-transform:none;"><strong>System API</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							}
							
							list_remarks_html +=		'	<span style="font-size:9px;">Date:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].create_date+'</strong></span>';
							list_remarks_html +=		'</small>';
							list_remarks_html +=	'</li>';
						}

						$('#list_remarks_ul').html(list_remarks_html);
					} else {
						$('#list_remarks_ul').html('');
					}
				}
			});
		}
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Client">Pin</a></li>
			<li><a href="javascript:;" title="Client Details">Pin Details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onClick="document.location.href = '/pin/list-pin/batch-id/' + <?php echo $this->data['rs']->batch_id; ?>" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
			<div class="float-right">
<?php	if (checkPermit('PinController', 'update')) { ?>
				<button type="button" id="save-btn" onClick="$('#pin_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
<?php	} ?>
			</div>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<h1>Pin Details</h1>

				<div class="columns">
					<div class="col200pxL-left">
						<h2>Side tabs</h2>

						<ul class="side-tabs js-tabs same-height">
							<li class="current"><a title="Pin Details" href="#tab-global">Pin Details</a></li>
<?php	if (isset($this->data['rs']->id)) { ?>
							<li><a href="#tab-remarks" title="Remarks" onClick="loadRemarks();">Remarks</a></li>
<?php	} ?>
						</ul>
					</div>
					<div class="col200pxL-right">
						<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
							<form id="pin_form" action="" method="post" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
										<p>Pin information</p>

											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Batch ID</label>
													<span class="relative">
														<input type="text" class="full-width" id="batch_id" name="batch_id" value="<?php echo $this->data['rs']->batch_id?>" disabled="disabled" />
													</span>
												</div>
												
												<div class="colx3-right">
													<span class="label required">Status</span>
													<p class="input-height grey-bg" align="center">
														<input type="radio" <?php echo (isset($this->data['rs']->status) ? (($this->data['rs']->status == 2) ? 'checked="checked"' : '') : '') ?> value="2" name="pending" disabled="disabled" />&nbsp;<label for="complex-en-active-0">Pending</label></br>
														<input type="radio" <?php echo (isset($this->data['rs']->status) ? (($this->data['rs']->status == 1) ? 'checked="checked"' : '') : 'checked="checked"') ?> value="1" name="active" disabled="disabled" />&nbsp;<label for="complex-en-active-1">Active</label></br>
														<input type="radio" <?php echo (isset($this->data['rs']->status) ? (($this->data['rs']->status == 0) ? 'checked="checked"' : '') : '') ?> value="0" name="active" disabled="disabled" />&nbsp;<label for="complex-en-active-0">Inactive</label>
<?php
		if (isset($this->data['rs']->id)) {
			if ($this->data['rs']->status == 1) {
				if (checkPermit('PinController', 'deactivate')) {
?>
														<br><br><button class="red" id="deactivate-btn" onClick="deactivate('<?php echo $this->data['rs']->id ?>'); return false;">Deactivate</button>
<?php
				}
			} else {
				if (checkPermit('PinController', 'activate')) {
?>
														<br><br><button class="active" id="activate-btn" onClick="activate('<?php echo $this->data['rs']->id ?>'); return false;">Activate</button>
<?php
				}
			}
		}
?>
													</p>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Serial</label>
													<span class="relative">
														<input type="text" class="full-width" id="serial" name="serial" value="<?php echo $this->data['rs']->serial?>" readonly="readonly" />
													</span>
												</div>

												<div class="colx3-right">
													<span class="label required">Redeem</span>
													<p class="input-height grey-bg" align="center">
														<input type="radio" <?php echo (isset($this->data['rs']->redeem) ? (($this->data['rs']->redeem == 1) ? 'checked="checked"' : '') : 'checked="checked"') ?> value="1" name="redeem" disabled="disabled" />&nbsp;<label for="complex-en-active-1">Yes</label>
														<input type="radio" <?php echo (isset($this->data['rs']->redeem) ? (($this->data['rs']->redeem == 0) ? 'checked="checked"' : '') : '') ?> value="0" name="unredeem" disabled="disabled" />&nbsp;<label for="complex-en-active-0">No</label>
													</p>
												</div>

												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Pin</label>
													<span class="relative">
														<input type="hidden" name="id" id="id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" class="full-width" id="pin" name="pin" value="<?php echo $this->data['rs']->pin?>" readonly="readonly" />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Start Date</label>
													<span class="relative">
														<input type="text" id="start_date" name="start_date" value="<?php echo $this->data['rs']->start_date?>" disabled="disabled" />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Expired Date</label>
													<span class="relative">
														<input type="text" id="end_date" name="end_date" value="<?php echo $this->data['rs']->end_date?>" />
													</span>
												</div>
											</fieldset>
										</div>
									</div>																
								</div>
							</form>	
						</div>
<?php	if (isset($this->data['rs']->id)) { ?>
						<div class="tabs-content" id="tab-remarks" style="min-height: 483px; display: block;">
							<form action="" method="post" id="remarks_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Remarks" href="#tab-details">Remarks</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
											<h3>Remarks</h3>
											<p>View remarks below</p>

											<fieldset class="grey-bg">
												<legend>Remarks</legend>

												<div class="block-border"><div class="block-content">
													<h1>Remarks list</h1>

													<div class="block-controls">
														<ul id="remarks_pager" class="controls-buttons">
															<li>
																<a title="Previous" href="javascript:prevRemarks();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a>
															</li>
															<li>
																<a class="current" title="Page 1" href="javascript:gotoPageRemarks(1);"><b>1</b></a>
															</li>
															<li>
																<a title="Next" href="javascript:nextRemarks();">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a>
															</li>
															<li class="sep"></li>
															<li>
																<a href="javascript:refreshRemarks();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a>
															</li>
														</ul>
													</div>

													<ul class="extended-list no-margin icon-user" id="list_remarks_ul"></ul>

													<ul class="message no-margin">
														<li id="listRemarksBottomMessage"></li>
													</ul>
												</div></div>
											</fieldset>
										</div>
									</div>
								</div>
							</form>
						</div>
<?php	} ?>
					</div>
				</div>
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

	<!--[if lt IE 8]></div><![endif]-->
	<!--[if lt IE 9]></div><![endif]-->
</body>
</html>