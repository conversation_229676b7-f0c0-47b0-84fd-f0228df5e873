<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Pin Search Criteria</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<script>
		$(document).ready(function() {
			$('#batch_id').keyup(function() {
				var value = $(this).val();
				var orignalValue = value;
				value = value.replace(/\d*/g, '');

				if (value != '') {
					orignalValue=orignalValue.replace(/([^0-9].*)/g, '')
					$(this).val(orignalValue);
				}
				
				$('#serial').val('');
			});
			
			$('#serial').keyup(function() {
				var value = $(this).val();
				var orignalValue = value;
				value = value.replace(/[0-9a-zA-Z]*/g, '');

				if (value != '') {
					orignalValue=orignalValue.replace(/([^0-9a-zA-Z].*)/g, '')
					$(this).val(orignalValue);
				}
				
				$('#batch_id').val('');
				$('#status').val('');
				$('#redeem').val('');
			});
		});
		
		function searchPin() {
			var search_str = '';
			var batch_id = $.trim($('#batch_id').val());
			var serial = $.trim($('#serial').val());
			
			if (batch_id == '' && serial == '') {
				alert('Please Enter Batch ID or Serial');
				return;
			}
			
			if (serial == '') {
				var status = $.trim($('#status').val());
				var redeem = $.trim($('#redeem').val());
				
				if (batch_id != '') {
					search_str = '/batch-id/'+batch_id;
				}

				if (status != '') {
					search_str += '/status/'+status;
				}

				if (redeem != '') {
					search_str += '/redeem/'+redeem;
				}
			} else {
				search_str = '/serial/'+serial;
			}
			
			window.location = '/pin/list-pin' + search_str;
		}
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0);" title="Client">Pin</a></li>
			<li><a href="javascript:void(0);" title="Client Details">Pin Search Criteria</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="client_form" class="block-content form">
					<h1>Pin Search Critiria</h1>
					
					<div class="columns">
						<div class="col200pxL-left">
							<h2>Side tabs</h2>
							
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Search Critiria" href="#tab-global">Search Critiria</a></li>
							</ul>
						</div>
						<div class="col200pxL-right">
							<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Search Critiria</a></li>
								</ul>
								
								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<p>Pin Search Criteria</p>
										
											<fieldset>
												<legend>Pin Search Criteria</legend>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Serial</label>
													<span class="relative">
														<input type="text" id="serial" name="serial" value="" size="18" maxlength="18" />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Batch ID</label>
													<span class="relative">
														<input type="text" id="batch_id" name="batch_id" size="5" maxlength="5" value="" />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Status</label>
													<span class="relative">
														<select name="status" id="status">
															<?php genOption($this->data['status_list']);?>
														</select>
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Redeem</label>
													<span class="relative">
														<select name="status" id="redeem">
															<option value="">Please Select</option>
															<option value="yes">Yes</option>
															<option value="no">No</option>
														</select>
													</span>
												</div>
											</fieldset>
										
											<button type="button" id="search" onClick="searchPin(); return false;">
												<img style="height:16px;width:16px;" src="/images/icons/fugue/magnifier.png" /> Search
											</button>
										</div>
									</div>																
								</div>
							</div>
						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

	<!--[if lt IE 8]></div><![endif]-->
	<!--[if lt IE 9]></div><![endif]-->
</body>
</html>