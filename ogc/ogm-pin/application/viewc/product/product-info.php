<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Product Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		$(document).ready(function() {
			$('#tab-relations').css('height','371px');
			$('#product_form').submit(function(e) {
				//post to ajax
				var action = 'update';
				
				if ($('#product_id').val() == '') {
					action = 'create';
				}
				
				$.ajax({
					type: "POST",
					url: '/api/product/' + action + '/',
					data: $('#product_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!');
						} else if (XMLHttpRequest.status == 201) {
							alert('Product Created');
							window.location = '/product/list-product';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});

                return false;
            });
		});
		
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Product">Product</a></li>
			<li><a href="javascript:;" title="Product details">Product details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onclick="document.location.href = '/product/list-product'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
<?php
		$disabled = '';
		
		if (isset($this->data['rs']->id)) {
			if (!checkPermit('ProductController', 'update')) {
				$disabled = 'disabled="disabled"';
			}
		} else {
			if (!checkPermit('ProductController', 'create')) {
				$disabled = 'disabled="disabled"';
			}
		}
		
		if ($disabled == '') {
?>
			<div class="float-right">
				<button type="button" onClick="$('#product_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
<?php	} ?>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="product_form" class="block-content form">
					<h1>Product Details</h1>
					
					<div class="columns">
						<div class="col200pxL-left">
							<h2>Side tabs</h2>
							
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Product Details" href="#tab-global">Product Details</a></li>
							</ul>
						</div>
						<div class="col200pxL-right">
							<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>
								
								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
<?php
		if (isset($this->data['rs']->id)) {
			if (checkPermit('PinController', 'getListPin')) {
?>
										<p>
											<a href="/pin/list-pin/product-id/<?php echo $this->data['rs']->id;?>">
												View all pins for this product <img height="16" width="16" src="/images/icons/fugue/cards-address.png" />
											</a>
										</p>
<?php
			}
		}
?>
										<p>Edit information as needed below</p>
										
											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Product name</label>
													<span class="relative">
														<input type="hidden" name="id" id="product_id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" class="full-width" id="product_name" name="name" value="<?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?>" <?php echo $disabled?> />
													</span>
												</div>
												<div class="colx3-right">
													<span class="label required">Active</span>
													<p class="input-height grey-bg">
														<input type="radio" <?php echo (isset($this->data['rs']->active) ? (($this->data['rs']->active == 1) ? 'checked="checked"' : '') : 'checked="checked"') ?> value="1" id="active" name="active" <?php echo $disabled?> />&nbsp;<label for="complex-en-active-1">Yes</label>
														<input type="radio" <?php echo (isset($this->data['rs']->active) ? (($this->data['rs']->active == 0) ? 'checked="checked"' : '') : '') ?> value="0" id="inactive" name="active" <?php echo $disabled?> />&nbsp;<label for="complex-en-active-0">No</label>
													</p>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Currency</label>
													<span class="relative">
														<select id="currency" name="currency" <?php echo $disabled?>><?php genOption($this->data['currency_list'], (isset($this->data['rs']->currency) ? $this->data['rs']->currency : null) );?></select>
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Deno</label>
													<span class="relative">
														<input type="text" id="deno" name="deno" value="<?php echo (isset($this->data['rs']->deno) ? $this->data['rs']->deno : '')?>" <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double">
													<label for="complex-title">Bar Code (Only for Physical Card)</label>
													<span class="relative">
														<input type="text" class="full-width" id="barcode" name="barcode" value="<?php echo (isset($this->data['rs']->barcode) ? $this->data['rs']->barcode : '')?>" <?php echo $disabled?> />
													</span>
												</div>
											</fieldset>
											
											<fieldset>
												<legend>Optional details</legend>
												
                                                <div class="colx3-left-double">
													<label for="complex-title">Enter the product description below</label>
													<span class="relative">
														<textarea style="width: 150%;" rows="5" id="description" name="description" <?php echo $disabled?>><?php echo (isset($this->data['rs']->description) ? $this->data['rs']->description : '')?></textarea>
													</span>
												</div>
											</fieldset>
										</div>
									</div>																
								</div>
							</div>

						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>