<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Performance Report - Pin</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<style type='text/css'>
		.left-text {
			font-weight: bold;
			text-align: left;
		}
	</style>
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.autocomplete"></script>
	<script type='text/javascript' src='https://www.google.com/jsapi'></script>
	
	<script type='text/javascript'>
		google.load('visualization', '1', {packages:['table']});
		google.setOnLoadCallback(drawTable);
		
		function drawTable() {
			var data = new google.visualization.DataTable();
			var cssClassNames = {'headerRow': 'left-text'};
			var options = {'allowHtml': true, 'cssClassNames': cssClassNames};
			
			data.addColumn('string', 'Batch ID');
			data.addColumn('string', 'Serial');
			data.addColumn('string', 'Pin');
			data.addColumn('string', 'Status');
			data.addColumn('string', 'Start Date');
			data.addColumn('string', 'Expiry Date');
			data.addColumn('boolean', 'Redeem');
			
			data.addRows(<?php echo sizeof($this->data['report_data'])?>);
<?php
	for ($i = 0; $i < sizeof($this->data['report_data']); $i++) {
		echo 'data.setCell('.$i.',0, "'.$this->data['report_data'][$i]['batch_id'].'");';
		echo 'data.setCell('.$i.',1, "'.$this->data['report_data'][$i]['serial'].'");';
		echo 'data.setCell('.$i.',2, "'.$this->data['report_data'][$i]['pin'].'");';
		echo 'data.setCell('.$i.',3, "'.$this->data['report_data'][$i]['status'].'");';
		echo 'data.setCell('.$i.',4, "'.$this->data['report_data'][$i]['start_date'].'");';
		echo 'data.setCell('.$i.',5, "'.$this->data['report_data'][$i]['end_date'].'");';
		echo 'data.setCell('.$i.',6, '.$this->data['report_data'][$i]['redeem'].');';
	}
?>
			var table = new google.visualization.Table(document.getElementById('tab-details'));
			table.draw(data, options);
		}
	</script>
</head>

<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Requests">Reports</a></li>
			<li><a href="javascript:;" title="Manage Request">Performance Report</a></li>
			<li><a href="javascript:;" title="Manage Request">Performance Report - Pins</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="client_form" class="block-content form">
					<h1>Report</h1>
					
					<div class="columns">
						<!--div class="col200pxL-left">
							<h2>Side tabs</h2>
							
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Report" href="#tab-global">Report</a></li>
							</ul>
						</div-->
						<!--div class="col200pxL-right"-->
							<div class="tabs-content">
								<div id="tab-details" style="min-height: 418px; width:100%;"></div>																
							</div>
						<!--/div-->
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>