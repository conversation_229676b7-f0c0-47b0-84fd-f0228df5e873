<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Performance Report</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<style type='text/css'>
		.left-text {
			font-weight: bold;
			text-align: left;
		}
		
		.report {
			width:100%;
		}
		
		th {
			border: 1px solid #EEEEEE;
			margin: 0;
			padding: 6px;
		}
		
		td {
			border: 1px solid #EEEEEE;
			margin: 0;
			padding: 6px;
		}
		
		tr.report-row:hover {
			background-color:#DFE8E4;
		}
		
		tr.link {
			cursor: pointer;
		}
	</style>
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,jquery.autocomplete"></script>
	<script type='text/javascript'>
		$.fn.slideTo = function(data) {
			var width = parseInt($('#slider').css('width'));
			var height = parseInt($('#slider').css('height'));
			var transfer = $('<div class="transfer"></div>').css({ 'width': (2 * width) + 'px' , 'height': height + 'px'});
			var current = $('<div class="current"></div>').css({ 'width': width + 'px', 'left': '0', 'float': 'left' }).html($('#slider').html());
			var next = $('<div class="next"></div>').css({ 'width': width + 'px', 'left': width + 'px', 'float': 'left' }).html(data);
			
			transfer.append(current).append(next);
			
			$('#slider').html('').append(transfer);
			transfer.animate({ 'margin-left': '-' + width + 'px' }, 600, function () {
				$('#slider').html(data);
			});
		}
		
		$.fn.slideFrom = function(data) {
			var width = parseInt($('#slider').css('width'));
			var height = parseInt($('#slider').css('height'));
			var transfer = $('<div class="transfer"></div>').css({ 'width': (2 * width) + 'px' , 'height': height + 'px'});
			var current = $('<div class="current"></div>').css({ 'width': width + 'px', 'left': '0', 'float': 'right' }).html($('#slider').html());
			var next = $('<div class="next"></div>').css({ 'width': width + 'px', 'left': width + 'px', 'float': 'right' }).html(data);
			
			transfer.append(current).append(next);
			
			$('#slider').html('').append(transfer);
			transfer.animate({ 'margin-right': width + 'px' }, 600, function () {
				$('#slider').html(data);
			});
		}

		$(function() {
			$('.report-row').on('click', function() {
				if ('undefined' !== typeof $(this).attr('data-id')) {
					var url = '/report/get-report/type/batch/batch-id/'+$(this).attr('data-id');

					history.pushState({ path: url }, '', url);

					$.get(url+'/view/no', function(data) {
						$('#slider').slideTo(data);
					});
				}
			});
			
			$(window).on('popstate', function() {
				$.get(location.href+'/view/no', function(data) {
					$('#slider').slideFrom(data);
				});
			});
		});
	</script>
</head>

<body>
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Report">Reports</a></li>
			<li><a href="javascript:;" title="Performance Reports">Performance Reports</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_12">
			<div class="block-border">
				<form action="" method="post" id="client_form" class="block-content form">
					<h1>Report</h1>
					
					<div class="columns">
						<div class="tabs-content">
							<div id="slider" style="min-height: 483px; width:100%;overflow: hidden;">
								<?php echo $this->data['report_data'] ?>
							</div>
						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>
</body>
</html>