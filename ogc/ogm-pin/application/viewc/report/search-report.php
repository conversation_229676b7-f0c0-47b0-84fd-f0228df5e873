<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Performance Report Generate Criteria</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.datepicker" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.datepicker"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<script>
		$(document).ready(function() {
			$('#start_date').datepicker({dateFormat: 'yy-mm-dd'});
            $('#end_date').datepicker({dateFormat: 'yy-mm-dd'});
		});
		
		function searchReport() {
			var search_str = '';
			
			var start_date = $.trim($('#start_date').val());
			var end_date = $.trim($('#end_date').val());
			
			if (start_date == '' || end_date == '') {
				alert('Please Select Start Date & End Date');
				return;
			}
			
			search_str = '/start-date/'+start_date+'/end-date/'+end_date;
			
			$('#product_id option:selected').each(function () {
				if ($(this).val() != '') {
					search_str += '/product-id/'+$(this).val();
				}
			});
			
			$('#client_id option:selected').each(function () {
				if ($(this).val() != '') {
					search_str += '/client-id/'+$(this).val();
				}
			});
			
			$('#reseller_id option:selected').each(function () {
				if ($(this).val() != '') {
					search_str += '/reseller-id/'+$(this).val();
				}
			});
			
			window.location = '/report/get-report/type/performance' + search_str;
		}
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Client">Report</a></li>
			<li><a href="javascript:;" title="Client Details">Performance Report Criteria</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="client_form" class="block-content form">
					<h1>Performance Report Criteria</h1>
					
					<div class="columns">
						<div class="col200pxL-left">
							<h2>Side tabs</h2>
							
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Performance Report Criteria" href="#tab-global">Performance Report Criteria</a></li>
							</ul>
						</div>
						<div class="col200pxL-right">
							<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Performance Report</a></li>
								</ul>
								
								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<p>Performance Report</p>
										
											<fieldset>
												<legend>Report Criteria</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Start Date (YYYY-MM-DD)</label>
													<span class="relative">
														<input type="text" name="start_date" id="start_date" value="" />
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">End Date (YYYY-MM-DD)</label>
													<span class="relative">
														<input type="text" name="end_date" id="end_date" value="" />
													</span>
												</div>
												
												<div class="clear" style="padding-top:15px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Date Type</label>
													<span class="relative">
														<select name="date_id" id="date_id">
															<?php genOption($this->data['date_list']);?>
														</select>
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Product</label>
													<span class="relative">
														<select name="product_id" id="product_id">
															<?php genOption($this->data['product_list']);?>
														</select>
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Client</label>
													<span class="relative">
														<select name="client_id" id="client_id">
															<?php genOption($this->data['client_list']);?>
														</select>
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double">
													<label for="complex-title">Reseller</label>
													<span class="relative">
														<select name="reseller_id" id="reseller_id">
															<?php genOption($this->data['reseller_list']);?>
														</select>
													</span>
												</div>
											</fieldset>
										
											<button type="button" id="search" onClick="searchReport(); return false;">
												<img style="height:16px;width:16px;" src="/images/icons/fugue/magnifier.png" /> Generate
											</button>
										</div>
									</div>																
								</div>
							</div>
						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

	<!--[if lt IE 8]></div><![endif]-->
	<!--[if lt IE 9]></div><![endif]-->
</body>
</html>