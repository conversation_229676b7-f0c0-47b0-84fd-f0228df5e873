<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Manage Pin Request</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.autocomplete"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<!-- Charts library -->
	<script type="text/javascript">
        var page = 1;
        var lastpage = 1;
        var loading = true;
		// Example context menu
		$(document).ready(function() {
            loadRequests();
			loadRequestsForSearch();
		});

<?php	if (checkPermit('RequestController', 'approveRequest')) { ?>
        function approve(id) {
			var remarks = $('#remarks_'+id).val();
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');

				if (remarks != '') {
					if (confirm('Confirm Approve this request?')) {
						$('#remarks_'+id).parent().fadeOut();
						$('#remarks_'+id).parent().prev().fadeOut();

						$.ajax({
							type: "POST",
							url: '/api/request/approve/' + id,
							data: {remarks: $('#remarks_'+id).val()},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Approve Success!', 'success');
									location.href = location.href;
								}							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#remarks_'+id).parent().show();
								$('#remarks_'+id).parent().prev().show();
							}
						});

					}
				} else {
					alert('Please insert remarks!');
				}
			}
        }
<?php
		}
		
		if (checkPermit('RequestController', 'rejectRequest')) {
?>
        function reject(id) {
			var remarks = $('#remarks_'+id).val();
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');

				if (remarks != '') {
					if (confirm('Confirm Reject this request?')) {
						$('#remarks_'+id).parent().fadeOut();
						$('#remarks_'+id).parent().prev().fadeOut();

						$.ajax({
							type: "POST",
							url: '/api/request/reject/' + id,
							data: {remarks: $('#remarks_'+id).val()},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Reject Success!', 'success');
									location.href = location.href;
								}							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#remarks_'+id).parent().show();
								$('#remarks_'+id).parent().prev().show();
							}
						});

					}
				} else {
					alert('Please insert remarks!');
				}
			}
        }
<?php
		}
		
		if (checkPermit('RequestController', 'delete')) {
?>

		function deleteRequest(id) {
			var remarks = $('#remarks_'+id).val();
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					if (confirm('Confirm Delete this request?')) {
						$('#remarks_'+id).parent().fadeOut();
						$('#remarks_'+id).parent().prev().fadeOut();
						
						$.ajax({
							type: "POST",
							url: '/api/request/delete/' + id,
							data: {remarks: remarks},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Delete Success!', 'success');
									location.href = location.href;
								}
							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#remarks_'+id).parent().show();
								$('#remarks_'+id).parent().prev().show();    
							}
						});

					}
				} else {
					alert('Please insert remarks!');
				}
			}
		}
<?php	} ?>

		function deleteSelfRequest(id) {
			var remarks = $('#remarks_'+id).val();
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					if (confirm('Confirm Delete this request?')) {
						$('#remarks_'+id).parent().fadeOut();
						$('#remarks_'+id).parent().prev().fadeOut();
						
						$.ajax({
							type: "POST",
							url: '/api/request/delete_self/' + id,
							data: {remarks: remarks},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Delete Success!', 'success');
									location.href = location.href;
								}
							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#remarks_'+id).parent().show();
								$('#remarks_'+id).parent().prev().show();    
							}
						});

					}
				} else {
					alert('Please insert remarks!');
				}
			}
		}
		
		function first() {
			if (loading) return;
			page = 1;
			loadRequests();
		}
		
		function last() {
			if (loading) return;
			page = lastpage;
			loadRequests();
		}
		
		function prev() {
			if (loading) return;
			if (page > 1) {
				page--;
				loadRequests();
			}
		}
		
		function next() {
			if (loading) return;
			if (page < lastpage) {
				page++;
				loadRequests();
			}
		}
		
		function refresh() {
			if (loading) return;
			loadRequests();
		}
		
		function gotoPage(index) {
			if (loading) return;
			page = index;
			loadRequests();
		}
		
        function loadRequests() {
			var status = $('#status').val();
			$('#list-title').html(status);
            $.get('/api/request/list_request/page/' + page + '/status/' + status, null, listRequestSuccess);
        }

        function listRequestSuccess(data, textStatus, XMLHttpRequest) {
            var request_list_html = '';
            var d = new Date();
			
            $('#listBottomMessage').text('Last loaded at ' + d.toLocaleTimeString());
            loading = false;
            
            if (data) {
				var pagerStr = '<li class="sep"></li>';
				pagerStr +='<li><a title="First" href="javascript:first();"><img height="16" width="16" src="/images/icons/fugue/control-double-180.png"></a></li>';
                pagerStr +='<li><a title="Previous" href="javascript:prev()"><img height="16" width="16" src="/images/icons/fugue/control-180.png"></a></li>';
                
				var maxpage = data.total_page;
				var authUserId = data.auth;
				lastpage = data.total_page;
				
				if (maxpage > 5) {
					maxpage = 5;
				}
				
                var offset = 0;
				
				if (page >= maxpage) {
					offset = page - 3;
				}
				
                for (var i = 1; i <= maxpage; i++) {
                    if (offset < 0) offset = 0; var pnum = offset + i;
					
					if (pnum <= lastpage) {
						if (page == pnum)
							pagerStr += '<li><a class="current" title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
						else
							pagerStr += '<li><a title="Page '+pnum+'" href="javascript:gotoPage('+pnum+');"><b>'+pnum+'</b></a></li>';
					}
                }
				
                pagerStr += '<li><a title="Next" href="javascript:next();"><img height="16" width="16" src="/images/icons/fugue/control.png"></a></li>';
				pagerStr += '<li><a title="Last" href="javascript:last();"><img height="16" width="16" src="/images/icons/fugue/control-double.png"></a></li>';
                pagerStr += '<li><div id="totalPageTxt"></div></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><input id="pageNumTxt" type="text" style="width:30px;"></li>';
				pagerStr += '<li class="sep"></li>';
                pagerStr += '<li><a href="javascript:refresh();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a></li>';
                
                $('#pager').html(pagerStr);
				$('#totalPageTxt').html(' of ' + lastpage);
				
				$('#pageNumTxt').bind('blur', function() {
					var no = parseInt($('#pageNumTxt').val());
					
					if (no > 0 && no <= lastpage) {
						gotoPage(no);
					}
					
					$('#pageNumTxt').val('');
				});
				
				$('#pageNumTxt').bind('keyup', function(event){
					if (event.keyCode == 13)
						$('#pageNumTxt').blur();
				});
				
                if (data.requests) {
                    data = data.requests;
                    var ll = data.length;
					
                    for (var i = 0; i < ll; i++) {
                        switch(data[i].status){
                            case 'Approved': 
                                var color = 'green'; break;
                            case 'Cancelled': 
                                var color = 'red'; break;
                            case 'Pending': 
                                var color = 'blue'; break;
							case 'Deleted':
								var color = 'grey'; break;
                        }
                        
                        request_list_html += 	'<li>';
                        request_list_html +=		'<a href="/request/request-info/'+data[i].id+'">';
                        request_list_html +=			'<span class="icon" style="background-image: url(/images/icons/namecards.png);"></span>';
                        request_list_html +=			'<div style="display: block; height: 40px;padding-top:8px;">'+data[i].title + ' -  (' + data[i].id + ')' +'</div>';
                        request_list_html +=			'<div class="clear" style="padding-top:5px;"></div>';
                        request_list_html +=            '<small>';
                        request_list_html +=				'<span style="font-size:9px;">Status:</span> <span style="font-size:11px;"><strong style="color: '+color+'">'+data[i].status+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=				'<span style="font-size:9px;">Quantity:</span> <span style="font-size:11px;"><strong>'+data[i].quantity+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=				'<span style="font-size:9px;">Product:</span> <span style="font-size:11px;"><strong>'+data[i].product_name+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=				'<span style="font-size:9px;">PIN Type:</span> <span style="font-size:11px;"><strong>'+data[i].pin_type+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=				'<span style="font-size:9px;">Reseller:</span> <span style="font-size:11px;"><strong>'+data[i].reseller+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=				'<span style="font-size:9px;">Client:</span> <span style="font-size:11px;"><strong>'+data[i].client+'</strong></span>&nbsp;&nbsp;&nbsp;';
                        request_list_html +=            '</small>';
                        request_list_html +=		'</a>';
						
                        if (data[i].status == 'Pending') {
							request_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
							
							if (data[i].request_by == authUserId) {
								request_list_html +=		'<li><a title="Edit" href="/request/request-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/pencil.png"></a></li>';
							} else {
								request_list_html +=		'<li><a title="Edit" href="/request/request-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/<?php echo (checkPermit('RequestController', 'update') ? 'pencil' : 'magnifier') ?>.png"></a></li>';
							}
							
							if (data[i].request_by == authUserId) {
								request_list_html +=		'<li><a href="javascript:;" title="Delete" onClick="deleteSelfRequest('+data[i].id+');"><img height="16" width="16" src="/images/icons/fugue/trash.png"></a></li>';
							} else {
<?php	if (checkPermit('RequestController', 'delete')) { ?>
								request_list_html +=		'<li><a href="javascript:;" title="Delete" onClick="deleteRequest('+data[i].id+');"><img height="16" width="16" src="/images/icons/fugue/trash.png"></a></li>';
<?php	} ?>
							}
<?php	if (checkPermit('RequestController', 'approveRequest')) { ?>
							request_list_html +=			'<li><a title="Approve" href="javascript:approve('+data[i].id+')"><img height="16" width="16" src="/images/icons/fugue/tick-circle.png"></a></li>';
<?php
		}

		if (checkPermit('RequestController', 'rejectRequest')) {
?>

							request_list_html +=			'<li><a title="Reject" href="javascript:reject('+data[i].id+')"><img height="16" width="16" src="/images/icons/fugue/cross-circle.png"></a></li>';
<?php	} ?>
							request_list_html +=		'</ul>';

							var remarks = (data[i].remarks) ? data[i].remarks : '';
							
							request_list_html +=		'<div class="mini-menu" style="min-height:80px; margin-top: 15px;opacity: 1;">';
							request_list_html +=            '<textarea id="remarks_'+data[i].id+'" name="remarks_'+data[i].id+'" rows="4" style="width: 200px;">'+remarks+'</textarea>';
							request_list_html +=		'</div>';
                        } else if (data[i].status == 'Approved') {
							request_list_html +=		'<div style="margin-top:34px;right:-1px;position:absolute;border:dotted 1px #999999;padding:3px;">';
							request_list_html +=			'<table border="0" cellpadding="5">';
							request_list_html +=				'<tr><td align="center" colspan="3"><small>PINS INFORMATION</small></td></tr>';
							request_list_html +=				'<tr>';
							request_list_html +=					'<td align="center">';
							request_list_html +=						'<img style="width:16px;height:16px;" src="/images/icons/fugue/status-away.png" alt="Pending" title="Pending" />';
							request_list_html +=					'</td>';
							request_list_html +=					'<td align="center">';
							request_list_html +=						'<img style="width:16px;height:16px;" src="/images/icons/fugue/status.png" alt="Active" title="Active" />';
							request_list_html +=					'</td>';
							request_list_html +=					'<td align="center">';
							request_list_html +=						'<img style="width:16px;height:16px;" src="/images/icons/fugue/status-busy.png" alt="Inactive" title="Inactive" />';
							request_list_html +=					'</td>';
							request_list_html +=					'<td align="center">';
							request_list_html +=						'<img style="width:16px;height:16px;" src="/images/icons/fugue/tags-label.png" alt="Redeemed" title="Redeemed" />';
							request_list_html +=					'</td>';
							request_list_html +=				'</tr>';
							request_list_html +=				'<tr>';
							request_list_html +=					'<td align="center"><span style="font-size:11px;"><strong>'+data[i].pending_pins+'</strong></span></td>';
							request_list_html +=					'<td align="center"><span style="font-size:11px;"><strong>'+data[i].active_pins+'</strong></span></td>';
							request_list_html +=					'<td align="center"><span style="font-size:11px;"><strong>'+(data[i].quantity - data[i].active_pins - data[i].pending_pins)+'</strong></span></td>';
							request_list_html +=					'<td align="center"><span style="font-size:11px;"><strong>'+(data[i].redeem_pins)+'</strong></span></td>';
							request_list_html +=				'</tr>';
							request_list_html +=			'</table>';
							request_list_html +=		'</div>';
							request_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
							request_list_html +=			'<li><a title="View" href="/request/request-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/magnifier.png"></a></li>';
							
							if (data[i].pin_type != 8) {
<?php	if (checkPermit('PinController', 'exportByBatch')) { ?>
								request_list_html +=			'<li><a title="Export" href="/api/pin/export/batch_id/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/document-excel-csv.png"></a></li>';
<?php	} else { ?>
								if (data[i].request_by == authUserId) {
									request_list_html +=		'<li><a title="Export" href="/api/pin/export_self/batch_id/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/document-excel-csv.png"></a></li>';
								}
<?php	} ?>
							}
							
							request_list_html +=		'</ul>';
						} else {
							request_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
							request_list_html +=			'<li><a title="View" href="/request/request-info/'+data[i].id+'"><img height="16" width="16" src="/images/icons/fugue/magnifier.png"></a></li>';
							request_list_html +=		'</ul>';
						}
                        
                        request_list_html +=	'</li>';
                    }
					
                    $('#list_request_ul').html(request_list_html);
                } else {
                    $('#list_request_ul').html('');
                }
            }
        }
		
		function loadRequestsForSearch() {
            $('#quick_search').autocomplete('/api/request/list_all_request/', {
               dataType: 'json',
               parse: function(data) {
					var rows = [];

					for (var i = 0; i < data.length; i++){
						rows[i] = { data:data[i], value:data[i].title, result:data[i].title };
					}

					return rows;
                },
                formatItem: function(item){
                    return item.title;
                }
                ,matchContains: true, minChars: 2, max:50
            }).result(function(event, item) {
                location.href = '/request/request-info/' + item.id;
            });
		}
	</script>
</head>

<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0);" title="Requests">Pin Requests</a></li>
			<li><a href="javascript:void(0);" title="Manage Request">Manage Pin Requests</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_12">
			<div class="block-border">
				<div class="block-content">
					<h1><span id="list-title"></span> List</h1>
                    
                    <div class="block-controls">
						<input id="quick_search" />
						<select id="status" name="status" onChange="loadRequests();"><?php genOption($this->data['status_list'], (isset($this->data['selected_status']) ? $this->data['selected_status'] : null) );?></select>
                        <ul id="pager" class="controls-buttons"></ul>
                    </div>
					
                    <ul class="extended-list no-margin icon-user" id="list_request_ul"></ul>

					<ul class="message no-margin">
						<li id="listBottomMessage"></li>
					</ul>
				</div>
			</div>
		</section>		
		
		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>