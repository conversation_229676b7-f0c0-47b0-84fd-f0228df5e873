<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Pin Request Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.datepicker" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.datepicker"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		var remarks_page = 1;
		var remarks_lastpage = 1;
		var remarks_loading = true;
		
<?php	if (checkPermit('RequestController', 'approveRequest')) { ?>
        function approve(id) {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					if (confirm('Confirm Approve this request?')) {
						$('#approve-btn').attr('disabled', 'disabled');
						$('#reject-btn').attr('disabled', 'disabled');

						$.ajax({
							type: "POST",
							url: '/api/request/approve/' + id,
							data: {remarks: remarks},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Approve Success!');
									location.href = '/request/request-info/'+id;
								}
							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#approve-btn').removeAttr('disabled');
								$('#reject-btn').removeAttr('disabled');  
							}
						});

					}
				} else {
					alert('Please enter your remarks!');
				}
			}
        }
<?php
		}
		
		if (checkPermit('RequestController', 'rejectRequest')) {
?>
        function reject(id) {
			var remarks = prompt('Please enter your remarks', '');
			
			if (remarks != null) {
				remarks = remarks.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
				
				if (remarks != '') {
					if (confirm('Confirm Reject this request?')) {
						$('#approve-btn').attr('disabled', 'disabled');
						$('#reject-btn').attr('disabled', 'disabled');

						$.ajax({
							type: "POST",
							url: '/api/request/reject/' + id,
							data: {remarks: remarks},
							success: function( data, textStatus, XMLHttpRequest ) {
								if (XMLHttpRequest.status == 200) {
									alert('Request been rejected!');
									location.href = '/request/request-info/'+id;
								}
							},
							error: function(err) {
								alert(eval(err.responseText));
								$('#approve-btn').removeAttr('disabled');
								$('#reject-btn').removeAttr('disabled');  
							}
						});

					}
				} else {
					alert('Please enter your remarks!');
				}
			}
        }
<?php	} ?>
		
		function showBtn() {
			$('#save-div').show();
			$('#approve-div').show();
			$('#reject-div').show();
			$('#export-div').show();
		}
		
		function hideBtn() {
			$('#save-div').hide();
			$('#approve-div').hide();
			$('#reject-div').hide();
			$('#export-div').hide();
		}
        
		// Example context menu
		$(document).ready(function() {
            $('#start_date').datepicker({dateFormat: 'yy-mm-dd'});
            $('#end_date').datepicker({dateFormat: 'yy-mm-dd'});
            
			$('#tab-relations').css('height','371px');
			
			$('#request_form').submit(function(e) {
<?php	if (isset($this->data['rs']->id) && $this->data['auth'] == $this->data['rs']->request_by) { ?>
				var action = 'update_self';
<?php	} else { ?>
				var action = 'update';
<?php	} ?>
				if ($('#id').val() == '') {
					action = 'create';
				}
				
				$.ajax({
					type: "POST",
					url: '/api/request/'+ action + '/',
					data: $('#request_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!', 'success');
						} else if (XMLHttpRequest.status == 201) {
							alert('Request Created', 'success');
							window.location = '/request/list-request';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});

				return false;
			});

			if (window.location.hash == '#&tab-remarks') {
				loadRemarks();
				hideBtn();
			} else {
				showBtn();
			}
		});

		function prevRemarks() {
			if (remarks_loading) return;
			if (remarks_page > 1){
				remarks_page--;
				loadRemarkss();
			}
		}

		function nextRemarks() {
			if (remarks_loading) return;
			if (remarks_page < remarks_lastpage){
				remarks_page++;
				loadRemarks();
			}
		}

		function refreshRemarks() {
			if (remarks_loading) return;
			loadRemarks();
		}

		function gotoPageRemarks(index) {
			if (remarks_loading) return;
			remarks_page = index;
			loadRemarks();
		}

		function loadRemarks() {
			$('#list_remarks_ul').html('');

			$.get('/api/request/list_remarks/' + $('#id').val()+'/page/' + remarks_page, null,
			function(data, textStatus, XMLHttpRequest) {
				var list_remarks_html = '';
				var dd = new Date();

				$('#listRemarksBottomMessage').text('Last loaded at ' + dd.toLocaleTimeString());
				remarks_loading = false;

				if (data) {
					var remarksPagerStr ='<li><a title="Previous" href="javascript:prevRemarks();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a></li>';

					for (var j = 1; j <= data.total_page; j++) {
						if (remarks_page == j)
							remarksPagerStr += '<li><a href="javascript:gotoPageRemarks('+j+');" class="current" title="Page '+j+'"><b>'+j+'</b></a></li>';
						else
							remarksPagerStr += '<li><a href="javascript:gotoPageRemarks('+j+');" title="Page '+j+'"><b>'+j+'</b></a></li>';
					}

					remarks_lastpage = data.total_page;

					remarksPagerStr += '<li><a href="javascript:nextRemarks();" title="Next">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a></li>';
					remarksPagerStr += '<li class="sep"></li>';
					remarksPagerStr += '<li><a href="javascript:refreshRemarks();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"/></a></li>';

					$('#remarks_pager').html(remarksPagerStr);
					if (data.remarks) {
						data = data.remarks;
						var ll = data.length;
						
						var sanitizeHTML = function (str) {
							var temp = document.createElement('div');
							temp.textContent = str;
							return temp.innerHTML;
						};
						for (var i = 0; i < ll; i++) {
							list_remarks_html += 	'<li>';
							list_remarks_html +=		'<span class="icon" style="background-image:url(/images/icons/write_2states.png)"></span>';
							list_remarks_html +=		sanitizeHTML(data[i].remarks);
							list_remarks_html +=		'<div class="" style="padding-top:5px;"></div>';
							list_remarks_html +=		'<small>';
							list_remarks_html +=		'	<span style="font-size:9px;">From:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].User.first_name+' '+data[i].User.last_name+'</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							list_remarks_html +=		'	<span style="font-size:9px;">Date:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].create_date+'</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							list_remarks_html +=		'</small>';
							list_remarks_html +=	'</li>';
						}

						$('#list_remarks_ul').html(list_remarks_html);
					} else {
						$('#list_remarks_ul').html('');
					}
				}
			});
		}
		
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>

<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0)" title="Pin Request">Pin Requests</a></li>
			<li><a href="javascript:void(0)" title="Pin Request details">Pin Request details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onClick="document.location.href = '/request/list-request'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
<?php
    $disable = '';
    $status = '';
	$btn_enable = false;
            
	if (isset($this->data['rs']->id)) {
		switch(trim($this->data['rs']->status)) {
			case 'Pending':
				$color = 'blue';
				$disable = '';
				
				if ($this->data['auth'] != $this->data['rs']->request_by) {
					if (!checkPermit('RequestController', 'update') || $this->data['auth'] != $this->data['rs']->request_by) {
						$disable = 'disabled="disabled"';
					}
				}
				
				$btn_enable = true;
				break;
			case 'Approved':
				$color = 'green';
				$disable = 'disabled="disabled"';
				break;
			case 'Cancelled':
				$color = 'red';
				$disable = 'disabled="disabled"';
				break;
            case 'Deleted':
				$color = 'grey';
				$disable = 'disabled="disabled"';                
				break;
		}

		$status = '<span style="color:'.$color.'"> - ('.$this->data['rs']->status.')</span>';

		if (trim($this->data['rs']->status) == 'Approved') {
			if ($this->data['rs']->pin_type != 8) {
				if (checkPermit('PinController', 'exportByBatch')) {
?>
			<div id="export-div" class="float-right">
				<a href="/api/pin/export/batch_id/<?php echo $this->data['rs']->id?>"><button type="button" id="export-btn"><img height="16" width="16" src="/images/icons/fugue/document-excel-csv.png"> Export</button></a>
			</div>
<?php
				} else {
					if ($this->data['auth'] == $this->data['rs']->request_by) {
?>
			<div id="export-div" class="float-right">
				<a href="/api/pin/export_self/batch_id/<?php echo $this->data['rs']->id?>"><button type="button" id="export-btn"><img height="16" width="16" src="/images/icons/fugue/document-excel-csv.png"> Export</button></a>
			</div>
<?php
					}
				}
			}
		}

		if ($btn_enable) {
			if (checkPermit('RequestController', 'approveRequest')) {
?>
			<div id="reject-div" class="float-right">
				<button type="button" id="reject-btn" class="red" onClick="reject('<?php echo $this->data['rs']->id?>');"><img height="16" width="16" src="/images/icons/fugue/cross-circle.png"> Reject</button>
			</div>
<?php
			}

			if (checkPermit('RequestController', 'rejectRequest')) {
?>
			<div id="approve-div" class="float-right">
				<button type="button" id="approve-btn" class="green" onClick="approve('<?php echo $this->data['rs']->id?>');"><img height="16" width="16" src="/images/icons/fugue/tick-circle.png"> Approve</button>
			</div>
<?php
			}
		}
	}
	
	if ($disable == '') {
?>
			<div id="save-div" class="float-right">
				<button type="button" id="save-btn" onClick="$('#request_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
<?php
	}
?>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<h1>Request Details</h1>

				<div class="columns">
					<div class="col200pxL-left">
						<h2>Side tabs</h2>

						<ul class="side-tabs js-tabs same-height">
							<li class="current"><a href="#tab-global" title="Request Details" onClick="showBtn();">Request Details</a></li>
<?php	if (isset($this->data['rs']->id)) { ?>
							<li><a href="#tab-remarks" title="Remarks" onClick="loadRemarks();hideBtn();">Remarks</a></li>
<?php	} ?>
						</ul>
					</div>
					<div class="col200pxL-right">
						<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
							<form action="" method="post" id="request_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
											<h3>
<?php
												echo (isset($this->data['rs']->title) ? $this->data['rs']->title : '');
												echo $status;
?>
											</h3>
<?php
		if (isset($this->data['rs']->id) && $this->data['rs']->status == 'Approved') {
			if (checkPermit('PinController', 'getListPin')) {
?>
											<p>
												<a href="/pin/list-pin/batch-id/<?php echo $this->data['rs']->id;?>">
													View all pins for this batch <img height="16" width="16" src="/images/icons/fugue/cards-address.png" />
												</a>
											</p>
<?php
			}
		}
?>
											<p><?php echo (isset($this->data['rs']->id) ? 'Edit' : 'Insert')?> information as needed below</p>
											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Title</label>
													<span class="relative">
														<input type="hidden" name="id" id="id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" id="title" name="title" value="<?php echo (isset($this->data['rs']->title) ? $this->data['rs']->title : '')?>" <?php echo $disable?> />
													</span>
												</div>
												
												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Quantity</label>
													<span class="relative">
														<input type="text" id="quantity" name="quantity" value="<?php echo (isset($this->data['rs']->quantity) ? $this->data['rs']->quantity : '')?>" <?php echo $disable?> />
													</span>
												</div>
												
												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Product</label>
													<span class="relative">
<?php	if ($disable == '') { ?>
														<select id="product_id" name="product_id" <?php echo $disable?>><?php genOption($this->data['product_list'], (isset($this->data['rs']->product_id) ? $this->data['rs']->product_id : null) );?></select>
<?php	} else { ?>
														<input type="text" id="product_id" name="product_id" value="<?php echo $this->data['rs']->product_name?>" <?php echo $disable?> />
<?php	} ?>
													</span>
												</div>
												
												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Reseller</label>
													<span class="relative">
														<select id="reseller_id" name="reseller_id" <?php echo $disable?>><?php genOption($this->data['reseller_list'], (isset($this->data['rs']->reseller_id) ? $this->data['rs']->reseller_id : null) );?></select>
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Client</label>
													<span class="relative">
														<select id="client_id" name="client_id" <?php echo $disable?>><?php genOption($this->data['client_list'], (isset($this->data['rs']->client_id) ? $this->data['rs']->client_id : null) );?></select>
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Country</label>
													<span class="relative">
														<select id="country_id" name="country_id" <?php echo $disable?>><?php genOption($this->data['country_list'], (isset($this->data['rs']->country_id) ? $this->data['rs']->country_id : 458) );?></select>
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">PIN Type</label>
													<span class="relative">
														<select id="pin_type" name="pin_type" <?php echo $disable?>><?php genOption($this->data['pintype_list'], (isset($this->data['rs']->pin_type) ? $this->data['rs']->pin_type : null) );?></select>
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Start Date (YYYY-MM-DD)</label>
													<span class="relative">
														<input type="text" name="start_date" id="start_date" value="<?php echo (isset($this->data['rs']->start_date) ? $this->data['rs']->start_date : date('Y-m-d'))?>" <?php echo $disable?> />
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Expiry Date (YYYY-MM-DD)</label>
													<span class="relative">
														<input type="text" name="end_date" id="end_date" value="<?php echo (isset($this->data['rs']->end_date) ? $this->data['rs']->end_date : date('Y-m-d', strtotime("+1 year")))?>" <?php echo $disable?> />
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>

												<div class="colx3-left-double">
													<label for="complex-title">Exclusive</label>
													<span class="relative">
														<input type="text" name="exclusive" id="exclusive" value="<?php echo (isset($this->data['rs']->exclusive) ? $this->data['rs']->exclusive : '') ?>" />
													</span>
												</div>

												<div class="clear" style="padding-top:15px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Enter the description below</label>
													<span class="relative">
														<textarea style="width: 150%;" rows="5" id="description" name="description"><?php echo (isset($this->data['rs']->description) ? $this->data['rs']->description : '')?></textarea>
													</span>
												</div>
											</fieldset>
										</div>
									</div>
								</div>
							</form>
						</div>
<?php	if (isset($this->data['rs']->id)) { ?>
						<div class="tabs-content" id="tab-remarks" style="min-height: 483px; display: block;">
							<form action="" method="post" id="remarks_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Remarks" href="#tab-details">Remarks</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
											<h3>Remarks</h3>
											<p>View remarks below</p>

											<fieldset class="grey-bg">
												<legend>Remarks</legend>

												<div class="block-border"><div class="block-content">
													<h1>Remarks list</h1>

													<div class="block-controls">
														<ul id="remarks_pager" class="controls-buttons">
															<li>
																<a title="Previous" href="javascript:prevRemarks();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a>
															</li>
															<li>
																<a class="current" title="Page 1" href="javascript:gotoPageRemarks(1);"><b>1</b></a>
															</li>
															<li>
																<a title="Next" href="javascript:nextRemarks();">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a>
															</li>
															<li class="sep"></li>
															<li>
																<a href="javascript:refreshRemarks()"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a>
															</li>
														</ul>
													</div>

													<ul class="extended-list no-margin icon-user" id="list_remarks_ul"></ul>

													<ul class="message no-margin">
														<li id="listRemarksBottomMessage"></li>
													</ul>
												</div></div>
											</fieldset>
										</div>
									</div>
								</div>
							</form>
						</div>
<?php	} ?>
					</div>
				</div>
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>