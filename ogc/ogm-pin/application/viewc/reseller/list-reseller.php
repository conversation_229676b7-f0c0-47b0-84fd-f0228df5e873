<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Manage Reseller</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery,jquery.autocomplete" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon,jquery.autocomplete"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	
	<!-- Charts library -->
		<script type="text/javascript">
		var page = 1;
		var lastpage = 1;
		var loading = true;
		
		$(document).ready(function() {
			loadResellers();
			loadResellersForSearch();
		});
		
		function first() {
			if (loading) return;
			page = 1;
			loadResellers();
		}
		
		function last() {
			if (loading) return;
			page = lastpage;
			loadResellers();
		}
		
		function prev() {
			if (loading) return;
			if (page > 1) {
				page--;
				loadResellers();
			}
		}
		
		function next() {
			if (loading) return;
			if (page < lastpage) {
				page++;
				loadResellers();
			}
		}
		
		function refresh() {
			if (loading) return;
			loadResellers();
		}
		
		function gotoPage(index) {
			if (loading) return;
			page = index;
			loadResellers();
		}
		
		function loadResellers() {
			$.get('/api/reseller/list_reseller/page/' + page, null, listResellerSuccess);
		}
		
		function deleteReseller(id) {
			var answer = confirm('Are you sure you want to delete this reseller?');
			
			if (answer) {
				location.href = '/reseller/delete/' + id;
			}
		}

			function listResellerSuccess(data, textStatus, XMLHttpRequest) {
			var reseller_list_html = '';
			var d = new Date();

			$('#listBottomMessage').text('Last loaded at ' + d.toLocaleTimeString());
			loading = false;

			if (data) {
				var pagerStr = '<li class="sep"></li>';
				pagerStr += '<li><a title="First" href="javascript:first();"><img height="16" width="16" src="/images/icons/fugue/control-double-180.png"></a></li>';
				pagerStr +='<li><a title="Previous" href="javascript:prev()"><img height="16" width="16" src="/images/icons/fugue/control-180.png"></a></li>';
				var maxpage = data.total_page;
				lastpage = data.total_page;

				if (maxpage > 5){
					maxpage = 5;
				}
				
				var offset = 0;
				if (page >= maxpage) {
					offset = page-3;
				}

				for (var i = 1; i <= maxpage; i++) {
					if (offset < 0) offset = 0; var pnum = offset + i;

					if (pnum <= lastpage) {
						if (page == pnum)
							pagerStr += '<li><a href="javascript:gotoPage('+pnum+');" class="current" title="Page '+pnum+'"><b>'+pnum+'</b></a></li>';
						else
							pagerStr += '<li><a href="javascript:gotoPage('+pnum+');" title="Page '+pnum+'"><b>'+pnum+'</b></a></li>';
					}
				}

				pagerStr += '<li><a href="javascript:next();" title="Next"><img height="16" width="16" src="/images/icons/fugue/control.png"></a></li>';
				pagerStr += '<li><a href="javascript:last();" title="Last"><img height="16" width="16" src="/images/icons/fugue/control-double.png"></a></li>';
				pagerStr += '<li><div id="totalPageTxt"></div></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><input id="pageNumTxt" type="text" style="width:30px;"></li>';
				pagerStr += '<li class="sep"></li>';
				pagerStr += '<li><a href="javascript:refresh();" title="Refresh"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a></li>';
				
				$('#pager').html(pagerStr);
				$('#totalPageTxt').html(' of ' + lastpage);
				
				$('#pageNumTxt').bind('blur', function() {
					var no = parseInt($('#pageNumTxt').val());
					
					if (no > 0 && no <= lastpage) {
						gotoPage(no);
					}
					
					$('#pageNumTxt').val('');
				});
				
				$('#pageNumTxt').bind('keyup', function(event){
					if (event.keyCode == 13)
						$('#pageNumTxt').blur();
				});
				
				$('#pageNumTxt').bind('blur', function() {
					var no = parseInt($('#pageNumTxt').val());
					
					if (no > 0 && no <= lastpage) {
						gotoPage(no);
					}
					
					$('#pageNumTxt').val('');
				});
				
				$('#pageNumTxt').bind('keyup', function(event){
					if (event.keyCode == 13)
						$('#pageNumTxt').blur();
				});

				if (data.resellers) {
					data = data.resellers;
					var ll = data.length;

					for (var i = 0; i < ll; i++) {
						reseller_list_html += 	'<li>';
						reseller_list_html +=		'<a href="/reseller/reseller-info/'+data[i].id+'">';
						reseller_list_html +=			'<span class="icon" style="background-image: url(/images/icons/web-app/48/reseller.png);"></span>';
						reseller_list_html +=			'<div style="display: block; height: 40px;padding-top:8px;">'+data[i].name +'</div>';
						reseller_list_html +=			'<div class="clear" style="padding-top:5px;"></div>';
						reseller_list_html +=			'<small><span style="font-size:9px;">Email:</span> <span style="font-size:11px;"><strong>'+data[i].email+'</strong></span>&nbsp;&nbsp;&nbsp;<span style="font-size:9px;">Tel:</span><span style="font-size:11px;"><strong>'+data[i].tel+'</strong></span></small>';
						reseller_list_html +=		'</a>';

						reseller_list_html +=		'<ul class="mini-menu" style="opacity: 1;">';
						reseller_list_html +=			'<li><a href="/reseller/reseller-info/'+data[i].id+'" title="Edit"><img height="16" width="16" src="/images/icons/fugue/<?php echo (checkPermit('ResellerController', 'update') ? 'pencil' : 'magnifier') ?>.png"></a></li>';
<?php	if (checkPermit('ResellerController', 'delete')) { ?>
						reseller_list_html +=			'<li><a href="javascript:;" title="Delete" onClick="deleteReseller('+data[i].id+');"><img height="16" width="16" src="/images/icons/fugue/trash.png"></a></li>';
<?php	} ?>
						reseller_list_html +=			'<li><a href="mailto:'+data[i].email+'" title="Send mail"><img height="16" width="16" src="/images/icons/fugue/mail.png"></a></li>';
						reseller_list_html +=		'</ul>';
						reseller_list_html +=	'</li>';
					}

					$('#list_reseller_ul').html(reseller_list_html);
				} else {
					$('#list_reseller_ul').html('');
				}
			}
		}

		function loadResellersForSearch() {
			$('#quick_search').autocomplete('/api/reseller/list_all_reseller/', {
				dataType: 'json',
				parse: function(data) {
					var rows = [];
					
					for (var i = 0; i < data.length; i++){
						rows[i] = { data:data[i], value:data[i].name, result:data[i].name };
					}
					
					return rows;
				},
				formatItem: function(item){
					return item.name;
				}
				,matchContains: true, minChars: 2, max:50
			}).result(function(event, item) {
				location.href = '/reseller/reseller-info/' + item.id;
			});
		}
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>

	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		
		<ul id="breadcrumb">
			<li><a href="javascript:void(0)" title="Resellers">Resellers</a></li>
			<li><a href="javascript:void(0)" title="Manage Resellers">Manage Resellers</a></li>
		</ul>
	
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div id="control-bar" class="grey-bg clearfix">
		<div class="container_12"></div>
	</div>
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_12">
			<div class="block-border"><div class="block-content">
				<h1>Resellers List</h1>

				<div class="block-controls">
					<input id="quick_search" />
					<ul id="pager" class="controls-buttons"></ul>
				</div>
				
				<ul class="extended-list no-margin icon-user" id="list_reseller_ul"></ul>
				
				<ul class="message no-margin">
					<li id="listBottomMessage"></li>
				</ul>
			</div></div>
		</section>		
		
		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	
    <?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>