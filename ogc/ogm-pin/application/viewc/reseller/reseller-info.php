<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - Reseller Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		var notify_page = 1;
		var notify_lastpage = 1;
		var notify_loading = true;
		
		$(document).ready(function() {
			$('.tabs-content').height('');
			$('#tab-relations').css('height','371px');
			
			$('#reseller_form').submit(function(e) {
				//post to ajax
				var action = 'update';
				
				if ($('#reseller_id').val() == '') {
					action = 'create';
				}
				
				$.ajax({
					type: "POST",
					url: '/api/reseller/' + action + '/',
					data: $('#reseller_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!');
						} else if (XMLHttpRequest.status == 201) {
							alert('Reseller Created');
							window.location = '/reseller/list-reseller';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});

				return false;
			});
<?php	if (isset($this->data['rs']->id) && (checkPermit('ResellerController', 'getListNotification'))) { ?>
			$('#notify_form').submit(function(e) {
				var notified = [];
				
				$('#from_select_list').find('option').each( function(){ notified.push($(this).attr('value')) } );
				
				var notifyData = {product_id: $('#product_id').val(), reseller_id : $('#reseller_id').val(), fee_deno : $('#fee_deno').val(), rate: $('#rate').val(), qty_of_notify: $('#qty_of_notify').val(), notify : notified};
				
				$.ajax({
					type: "POST",
					url: '/api/reseller/create_notification/',
					data: notifyData,
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							loadNotify();
							
							$('#product_id option:first').attr('selected', 'selected');
							$('#qty_of_notify').val(0);
							$('#fee_deno').val('0.00');
							listAllUsers();
							$('#from_select_list').html('');
							$('#notification_check').html('');
							
							alert('Notifcation Successfully Added');
						} 
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});
				return false;
			});
			
			if (window.location.hash == '#&tab-notification') {
				loadNotify();
				$('#save-btn').hide();
			}
			
			listAllUsers();
			
			$('#product_id').change(function () {
				$('#notification_check').html('');
				
				if ($(this).val() != '') {
					notificationExist($('#reseller_id').val(), $(this).val());
				}
			});
<?php	} ?>
		});

<?php	if (isset($this->data['rs']->id) && (checkPermit('ResellerController', 'getListNotification'))) { ?>
		function notificationExist(resellerId, productId) {
			$.get('/api/reseller/get_notification_by_product_id/'+resellerId+'/product_id/'+ productId, null,
			function(data, textStatus, XMLHttpRequest) {
				if (data) {
					$('#notification_check').html('Notification Exist');
				}
			});
		}
		
		function prevNotify() {
			if (notify_loading) return;
			if (notify_page > 1) {
				notify_page--;
				loadNotify();
			}
		}

		function nextNotify() {
			if (notify_loading) return;
			if (notify_page < notify_lastpage){
				notify_page++;
				loadNotify();
			}
		}

		function refreshNotify() {
			if (notify_loading) return;
			loadNotify();
		}

		function gotoPageNotify(index) {
			if (notify_loading) return;
			notify_page = index;
			loadNotify();
		}
		
		function loadNotify() {
			$('#list_notify_ul').html('');

			$.get('/api/reseller/list_notification/' + $('#reseller_id').val()+'/page/' + notify_page, null,
			function(data, textStatus, XMLHttpRequest) {
				var list_notify_html = '';
				var dd = new Date();

				$('#listNotifyBottomMessage').text('Last loaded at ' + dd.toLocaleTimeString());
				notify_loading = false;

				if (data) {
					var notifyPagerStr ='<li><a title="Previous" href="javascript:prevNotify();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a></li>';

					for (var j = 1; j <= data.total_page; j++) {
						if (notify_page == j)
							notifyPagerStr += '<li><a class="current" title="Page '+j+'" href="javascript:gotoPageNotify('+j+');"><b>'+j+'</b></a></li>';
						else
							notifyPagerStr += '<li><a title="Page '+j+'" href="javascript:gotoPageNotify('+j+');"><b>'+j+'</b></a></li>';
					}

					notify_lastpage = data.total_page;

					notifyPagerStr += '<li><a title="Next" href="javascript:nextNotify();">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a></li>';
					notifyPagerStr += '<li class="sep"></li>';
					notifyPagerStr += '<li><a href="javascript:refreshNotify();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"/></a></li>';

					$('#notify_pager').html(notifyPagerStr);
					
					if (data.notify) {
						data = data.notify;
						var ll = data.length;
						
						for (var i = 0; i < ll; i++) {
							list_notify_html += 	'<li>';
							list_notify_html +=		'<span class="icon" style="background-image:url(/images/icons/write_2states.png)"></span>';
							list_notify_html +=		data[i].Product.name ;
							list_notify_html +=		'<div class="" style="padding-top:5px;"></div>';
							list_notify_html +=		'<small>';
							
							list_notify_html +=		'	<span style="font-size:9px;">Quantity to be notifiy:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].qty_of_notify+'</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							list_notify_html +=		'	<span style="font-size:9px;">Fee:</span> <span style="font-size:11px;text-transform:none;"><strong>'+data[i].fee_deno+'</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
							list_notify_html +=		'	<span style="font-size:9px;">Rate:</span> <span style="font-size:11px;text-transform:none;"><strong>';
							
							if (data[i].rate == 0) {
								list_notify_html += 'fixed';
							} else {
								list_notify_html += 'percentage';
							}
							
							list_notify_html += '</strong></span>';
							list_notify_html +=	'</small>';
							list_notify_html +=		'<ul class="mini-menu" style="opacity: 1;">';
							list_notify_html +=			'<li><a title="Edit" href="javascript:;" onClick="getNotify('+data[i].id+')"><img height="16" width="16" src="/images/icons/fugue/pencil.png"></a></li>';
							list_notify_html +=			'<li><a title="Delete" href="javascript:;" onClick="deleteNotify('+data[i].id+')"><img height="16" width="16" src="/images/icons/fugue/cross-circle.png"></a></li>';
							list_notify_html +=		'</ul>';
							
							list_notify_html +=	'</li>';
						}
						
						$('#list_notify_ul').html(list_notify_html);
					} else {
						$('#list_notify_ul').html('');
					}
				}
			});
		}
		
		function listAllUsers() {
			$.get('/api/reseller/list_all_user', null,
			function(data, textStatus, XMLHttpRequest) {
				var ul = data.length;
				var optionHTML = '';
				
				for (var i = 0; i < ul; i++) {
					optionHTML += '<option value='+data[i].id+'>'+data[i].first_name+' '+data[i].last_name+'</option>';
				}
				
				$('#to_select_list').html(optionHTML);
			});
		}
		
		function getNotify(id) {
			$.get('/api/reseller/get_notification_by_id/'+id, null,
			function(data, textStatus, XMLHttpRequest) {
				$('#product_id option[value="'+data.data.product_id+'"]').attr("selected", "selected");
				$('#rate option[value="'+data.data.rate+'"]').attr("selected", "selected");
				$('#qty_of_notify').val(data.data.qty_of_notify);
				$('#fee_deno').val(data.data.fee_deno);
				
				var notifyHTML = '';
				var unnotifyHTML = '';
				
				for (var a in data.unnotify) {
					unnotifyHTML += '<option value="'+data.unnotify[a].id+'">'+data.unnotify[a].first_name+' '+data.unnotify[a].last_name+'</option>';
				}
				
				for (var b in data.notify) {
					notifyHTML += '<option value="'+data.notify[b].id+'">'+data.notify[b].first_name+' '+data.notify[b].last_name+'</option>';
				}
				
				$('#to_select_list').html(unnotifyHTML);
				$('#from_select_list').html(notifyHTML);
				
				notificationExist($('#reseller_id').val(), data.data.product_id);
			});
		}
		
		function deleteNotify(id) {
			if (confirm('Are you sure you want to delete this notification?')) {
				$.ajax({
					type: "POST",
					url: '/api/reseller/delete_notification/'+id,
					data: {},
					success: function( data, textStatus, XMLHttpRequest ) {
						loadNotify();
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});
			}
		}
		
<?php	} ?>
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>

<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
	<?php $this->inc('parts/head'); ?>
	<?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
		<?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:;" title="Reseller">Reseller</a></li>
			<li><a href="javascript:;" title="Reseller Details">Reseller Details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onclick="document.location.href = '/reseller/list-reseller'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
<?php
	$disabled = '';

	if (isset($this->data['rs']->id)) {
		if (!checkPermit('ResellerController', 'update')) {
			$disabled = 'disabled="disabled"';
		}
	} else {
		if (!checkPermit('ResellerController', 'create')) {
			$disabled = 'disabled="disabled"';
		}
	}

	if ($disabled == '') {
?>
			<div class="float-right"> 
				<button id="save-btn" type="button" onClick="$('#reseller_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
<?php
	}
?>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<h1>Reseller Details</h1>

				<div class="columns">
					<div class="col200pxL-left">
						<h2>Side tabs</h2>

						<ul class="side-tabs js-tabs same-height">
							<li class="current"><a title="Reseller Details" href="#tab-global" onClick="$('#save-btn').show();">Reseller Details</a></li>
<?php	if (isset($this->data['rs']->id) && (checkPermit('ResellerController', 'getListNotification'))) { ?>
							<li><a title="Notification" href="#tab-notification" onClick="loadNotify();$('#save-btn').hide();">Notification</a></li>
<?php	} ?>
						</ul>
					</div>
					<div class="col200pxL-right">
						<div class="tabs-content" id="tab-global" style="min-height: 413px; display: block;">
							<form action="" method="post" id="reseller_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="javascript:;">Details</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
										<p>Edit information as needed below</p>

											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Reseller name</label>
													<span class="relative">
														<input type="hidden" name="id" id="reseller_id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" class="full-width" id="reseller_name" name="name" value="<?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?>" <?php echo $disabled?> />
													</span>
												</div>
												<div class="colx3-right">
													<span class="label required">Active</span>
													<p class="input-height grey-bg">
														<input type="radio" <?php echo (isset($this->data['rs']->active) ? (($this->data['rs']->active == 1) ? 'checked="checked"' : '') : 'checked="checked"') ?> value="1" id="active" name="active" <?php echo $disabled?> />&nbsp;<label for="complex-en-active-1">Yes</label>
														<input type="radio" <?php echo (isset($this->data['rs']->active) ? (($this->data['rs']->active == 0) ? 'checked="checked"' : '') : '') ?> value="0" id="inactive" name="active" <?php echo $disabled?> />&nbsp;<label for="complex-en-active-0">No</label>
													</p>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Email</label>
													<span class="relative">
														<input type="text" class="full-width" id="email" name="email" value="<?php echo (isset($this->data['rs']->email) ? $this->data['rs']->email : '')?>" <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Telephone</label>
													<span class="relative">
														<input type="text" id="tel" name="tel" value="<?php echo (isset($this->data['rs']->tel) ? $this->data['rs']->tel : '')?>" <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Reseller ID</label>
													<span class="relative">
														<input type="text" class="full-width" id="reseller_code" name="reseller_code" value="<?php echo (isset($this->data['rs']->reseller_code) ? $this->data['rs']->reseller_code : '')?>" <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Secret Key</label>
													<span class="relative">
														<input type="text" class="full-width" id="secret_key" name="secret_key" value="<?php echo (isset($this->data['rs']->secret_key) ? $this->data['rs']->secret_key : '')?>" <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Verification URL</label>
													<span class="relative">
														<input type="text" id="verification_url" class="full-width" name="verification_url" value="<?php echo (isset($this->data['rs']->verification_url) ? $this->data['rs']->verification_url : '')?>" <?php echo $disabled?> />
													</span>
												</div>
											</fieldset>

											<fieldset>
												<legend>Optional details</legend>

												<div class="colx3-left-double">
													<label for="complex-title">Enter the reseller description below</label>
													<span class="relative">
														<textarea style="width: 150%;" rows="5" id="description" name="description" <?php echo $disabled?>><?php echo (isset($this->data['rs']->description) ? $this->data['rs']->description : '')?></textarea>
													</span>
												</div>
											</fieldset>
										</div>
									</div>																
								</div>
							</form>
						</div>
<?php	if (isset($this->data['rs']->id) && (checkPermit('ResellerController', 'getListNotification'))) { ?>
						<div class="tabs-content" id="tab-notification" style="min-height: 413px;">
							<form action="" method="post" id="notify_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Notification" href="javascript:;">Notification</a></li>
								</ul>

								<div class="tabs-content" style="position: relative;">
									<div id="tab-details" style="min-height: 452px;">
										<div class="infos">
											<h3>Notification</h3>
											<p>Add or view a Notification below</p>

											<fieldset class="grey-bg">
												<legend>Notification</legend>
												
												<div class="block-border"><div class="block-content">
													<h1>Products Notification List</h1>

													<div class="block-controls">
														<ul id="notify_pager" class="controls-buttons">
															<li>
																<a title="Previous" href="javascript:prevNotify();"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"> Prev</a>
															</li>
															<li>
																<a class="current" title="Page 1" href="javascript:gotoPageNotify(1);"><b>1</b></a>
															</li>
															<li>
																<a title="Next" href="javascript:nextNotify();">Next <img height="16" width="16" src="/images/icons/fugue/navigation.png"></a>
															</li>
															<li class="sep"></li>
															<li>
																<a href="javascript:refreshNotify();"><img height="16" width="16" src="/images/icons/fugue/arrow-circle.png"></a>
															</li>
														</ul>
													</div>

													<ul class="extended-list no-margin icon-user" id="list_notify_ul"></ul>

													<ul class="message no-margin">
														<li id="listNotifyBottomMessage"></li>
													</ul>
												</div></div>
											</fieldset>

											<fieldset>
												<legend>Add Product Notification</legend>

												<div class="colx3-left-double required">
													<label for="complex-title">Product</label>
													<span class="relative">
														<input type="hidden" name="reseller_id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<select id="product_id" name="product_id">
															<?php genOption($this->data['product_list']);?>
														</select>
													</span>
													<span id="notification_check" style="color:red"></span>
												</div>

												<div class="clear" style="padding-top: 13px"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Quantity to be notify</label>
													<span class="relative">
														<input type="text" id="qty_of_notify" name="qty_of_notify" value="0" class="min-width" />
													</span>
												</div>
												
												<div class="clear" style="padding-top: 13px"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Fee</label>
													<span class="relative">
														<input type="text" id="fee_deno" name="fee_deno" value="0.00" class="min-width" />
														<select name="rate" id="rate">
															<option value="0">Fixed</option>
															<option value="1">%</option>
														</select>
													</span>
												</div>
												
												<div class="clear" style="padding-top: 20px"></div>
												
												<table border="1" width="100%">
													<tr>
														<td align="center">
															<span style="font-weight:bold">Users List</span><br/><br/>
															<select id="to_select_list" multiple="multiple" name="to_select_list" style="width:200px;min-height:180px;"></select>
														</td>
														<td align="center" valign="top" style="vertical-align: middle;">
															<input id="moveright" type="button" value="<" onClick="move_list_items('from_select_list','to_select_list');" style="width:50px;" /><br/>
															<input id="moverightall" type="button" value="<<" onClick="move_list_items_all('from_select_list','to_select_list');" style="width:50px;" /><br/>
															<input id="moveleft" type="button" value=">" onClick="move_list_items('to_select_list','from_select_list');" style="width:50px;" /><br/>
															<input id="moveleftall" type="button" value=">>" onClick="move_list_items_all('to_select_list','from_select_list');" style="width:50px;" />
														</td>
														<td align="center">
															<span style="font-weight:bold">Notify List</span><br/><br/>
															<select id="from_select_list" multiple="multiple" name="from_select_list" style="width:200px;min-height:180px;"></select>
														</td>
													</tr>
													<tr>
														<td colspan="3" height="20px;"></td>
													</tr>
													<tr>
														<td colspan="3" align="center">
															<button type="submit">Submit</button>
														</td>
													</tr>
												</table>
											</fieldset>
										</div>
									</div>																
								</div>
							</form>
						</div>
<?php	} ?>
					</div>
				</div>
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>