<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - User Group Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		// Example context menu
		$(document).ready(function() {
			$('#tab-relations').css('height','371px');
			
			$('#user_group_form').submit(function(e) {
				//post to ajax
				var action = 'update';
				
				if ($('#user_group_id').val() == '') {
					action = 'create';
				}
				
				$.ajax({
					type: "POST",
					url: '/api/user-group/' + action + '/',
					data: $('#user_group_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!');
						} else if (XMLHttpRequest.status == 201) {
							alert('User Group Created');
							window.location = '/user-group/list-user-group';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});
	
				return false;
			});
			
			$('#user_group_permission_form').submit(function(e) {
				//post to ajax
				var action = 'update';
				
				if ($('#user_group_id').val() == '') {
					action = 'create';
				}
				
				$.ajax({
					type: "POST",
					url: '/api/user-group/permission',
					data: $('#user_group_permission_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!');
						} else if (XMLHttpRequest.status == 201) {
							alert('User Group Created');
							window.location = '/user-group/list-user-group';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});
	
                return false;
            });
			
			swapButton();
		});
		
		function swapButton() {
			if (window.location.hash == '#&tab-permission') {
				$('#user_group').hide();
				$('#user_group_permission').show();
			} else {
				$('#user_group').show();
				$('#user_group_permission').hide();
			}
		}
		
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>

<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	<?php $this->inc('parts/head'); ?>
	<?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
		<?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0);" title="Setting">Setting</a></li>
			<li><a href="javascript:void(0);" title="User Group Details">User Group Details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onClick="document.location.href = '/user-group/list-user-group'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
<?php
		$disabled = '';
		
		if (isset($this->data['rs']->id)) {
			if (!checkPermit('UserGroupController', 'update')) {
				$disabled = 'disabled="disabled"';
			}
		} else {
			if (!checkPermit('UserGroupController', 'create')) {
				$disabled = 'disabled="disabled"';
			}
		}
		
		if ($disabled == '') {
?>
			<div class="float-right"> 
				<button type="button" id="user_group" onClick="$('#user_group_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
				<button type="button" id="user_group_permission" style="display:none;" onClick="$('#user_group_permission_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
<?php	} ?>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<h1>User Group Details</h1>
				<div class="columns">
					<div class="col200pxL-left">
						<h2>Side tabs</h2>
						<ul class="side-tabs js-tabs same-height">
							<li class="current"><a title="User Group Details" href="#tab-global" onClick="$('#user_group').show();$('#user_group_permission').hide();">User Group Details</a></li>
<?php	if (isset($this->data['rs']->id) && checkPermit('UserGroupController', 'setPermission')) { ?>
							<li><a title="Permissions" href="#tab-permission" onClick="$('#user_group').hide();$('#user_group_permission').show();">Permissions</a></li>
<?php	} ?>
						</ul>
					</div>
					<div class="col200pxL-right">
						<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
							<form action="" method="post" id="user_group_form" class="block-content form">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
										<p>Edit information as needed below</p>
											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">Name</label>
													<span class="relative">
														<input type="hidden" name="id" id="user_group_id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" class="full-width" id="user_group_name" name="name" value="<?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?>" <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear" style="padding-top:10px;"></div>
											</fieldset>
										</div>
									</div>
								</div>
							</form>
						</div>
<?php	if (isset($this->data['rs']->id) && checkPermit('UserGroupController', 'setPermission')) { ?>
						<div class="tabs-content" id="tab-permission" style="min-height: 483px; display: none;">
							<form action="" method="post" id="user_group_permission_form" class="block-content form">
								<input type="hidden" name="id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Permission</a></li>
								</ul>

								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->name) ? $this->data['rs']->name : '')?></h3>
										<p>Edit permission as needed below</p>
											<fieldset>
												<legend>User</legend>
												<div class="colx3-left-double">
													<span class="relative">
														<input type="hidden" name="id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														View <input type="checkbox" name="UserController[]" value="view" <?php echo (isset($this->data['rs']->permission['UserController']) ? (in_array('listUser', $this->data['rs']->permission['UserController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="UserController[]" value="create" <?php echo (isset($this->data['rs']->permission['UserController']) ? (in_array('create', $this->data['rs']->permission['UserController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="UserController[]" value="update" <?php echo (isset($this->data['rs']->permission['UserController']) ? (in_array('update', $this->data['rs']->permission['UserController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Delete <input type="checkbox" name="UserController[]" value="delete" <?php echo (isset($this->data['rs']->permission['UserController']) ? (in_array('delete', $this->data['rs']->permission['UserController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear"></div>
											</fieldset>
										
											<fieldset>
												<legend>User Group</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="UserGroupController[]" value="view" <?php echo (isset($this->data['rs']->permission['UserGroupController']) ? (in_array('listUserGroup', $this->data['rs']->permission['UserGroupController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="UserGroupController[]" value="create" <?php echo (isset($this->data['rs']->permission['UserGroupController']) ? (in_array('create', $this->data['rs']->permission['UserGroupController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="UserGroupController[]" value="update" <?php echo (isset($this->data['rs']->permission['UserGroupController']) ? (in_array('update', $this->data['rs']->permission['UserGroupController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Permission <input type="checkbox" name="UserGroupController[]" value="setPermission" <?php echo (isset($this->data['rs']->permission['UserGroupController']) ? (in_array('setPermission', $this->data['rs']->permission['UserGroupController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear"></div>
											</fieldset>
												
											<fieldset>
												<legend>Reseller</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="ResellerController[]" value="view" <?php echo (isset($this->data['rs']->permission['ResellerController']) ? (in_array('listReseller', $this->data['rs']->permission['ResellerController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="ResellerController[]" value="create" <?php echo (isset($this->data['rs']->permission['ResellerController']) ? (in_array('create', $this->data['rs']->permission['ResellerController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="ResellerController[]" value="update" <?php echo (isset($this->data['rs']->permission['ResellerController']) ? (in_array('update', $this->data['rs']->permission['ResellerController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Delete <input type="checkbox" name="ResellerController[]" value="delete" <?php echo (isset($this->data['rs']->permission['ResellerController']) ? (in_array('delete', $this->data['rs']->permission['ResellerController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Notification <input type="checkbox" name="ResellerController[]" value="setNotification" <?php echo (isset($this->data['rs']->permission['ResellerController']) ? (in_array('createNotification', $this->data['rs']->permission['ResellerController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear"></div>
											</fieldset>

											<fieldset>
												<legend>Client</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="ClientController[]" value="view" <?php echo (isset($this->data['rs']->permission['ClientController']) ? (in_array('listClient', $this->data['rs']->permission['ClientController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="ClientController[]" value="create" <?php echo (isset($this->data['rs']->permission['ClientController']) ? (in_array('create', $this->data['rs']->permission['ClientController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="ClientController[]" value="update" <?php echo (isset($this->data['rs']->permission['ClientController']) ? (in_array('update', $this->data['rs']->permission['ClientController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Delete <input type="checkbox" name="ClientController[]" value="delete" <?php echo (isset($this->data['rs']->permission['ClientController']) ? (in_array('delete', $this->data['rs']->permission['ClientController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear"></div>
											</fieldset>

											<fieldset>
												<legend>Pin Request</legend>
													<div class="colx3-left-double">
														<span class="relative">
															View <input type="checkbox" name="RequestController[]" value="view" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('listRequest', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
															&nbsp;&nbsp;Add <input type="checkbox" name="RequestController[]" value="create" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('create', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
															&nbsp;&nbsp;Edit <input type="checkbox" name="RequestController[]" value="update" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('update', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
															&nbsp;&nbsp;Delete <input type="checkbox" name="RequestController[]" value="delete" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('delete', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
															&nbsp;&nbsp;Approve <input type="checkbox" name="RequestController[]" value="approveRequest" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('approveRequest', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
															&nbsp;&nbsp;Reject <input type="checkbox" name="RequestController[]" value="rejectRequest" <?php echo (isset($this->data['rs']->permission['RequestController']) ? (in_array('rejectRequest', $this->data['rs']->permission['RequestController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														</span>
													</div>
												<div class="clear"></div>
											</fieldset>

											<fieldset>
												<legend>Pin</legend>
												<div class="colx3-left-double" style="width:85%">
													<span class="relative">
														View <input type="checkbox" name="PinController[]" value="view" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('listPin', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;View Pin Number <input type="checkbox" name="PinController[]" value="viewPinNumber" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('viewPinNumber', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Activate <input type="checkbox" name="PinController[]" value="activate" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('activate', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Deactivate <input type="checkbox" name="PinController[]" value="deactivate" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('deactivate', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Extend <input type="checkbox" name="PinController[]" value="update" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('update', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Export <input type="checkbox" name="PinController[]" value="export" <?php echo (isset($this->data['rs']->permission['PinController']) ? (in_array('exportAll', $this->data['rs']->permission['PinController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear"></div>
											</fieldset>
											
											<fieldset>
												<legend>Product</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="ProductController[]" value="view" <?php echo (isset($this->data['rs']->permission['ProductController']) ? (in_array('listProduct', $this->data['rs']->permission['ProductController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="ProductController[]" value="create" <?php echo (isset($this->data['rs']->permission['ProductController']) ? (in_array('create', $this->data['rs']->permission['ProductController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="ProductController[]" value="update" <?php echo (isset($this->data['rs']->permission['ProductController']) ? (in_array('update', $this->data['rs']->permission['ProductController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Delete <input type="checkbox" name="ProductController[]" value="delete" <?php echo (isset($this->data['rs']->permission['ProductController']) ? (in_array('delete', $this->data['rs']->permission['ProductController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>

												<div class="clear"></div>
											</fieldset>
											
											<fieldset>
												<legend>Country</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="CountryController[]" value="view" <?php echo (isset($this->data['rs']->permission['CountryController']) ? (in_array('listCountry', $this->data['rs']->permission['CountryController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Add <input type="checkbox" name="CountryController[]" value="create" <?php echo (isset($this->data['rs']->permission['CountryController']) ? (in_array('create', $this->data['rs']->permission['CountryController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
														&nbsp;&nbsp;Edit <input type="checkbox" name="CountryController[]" value="update" <?php echo (isset($this->data['rs']->permission['CountryController']) ? (in_array('update', $this->data['rs']->permission['CountryController']) ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear"></div>
											</fieldset>
											
											<fieldset>
												<legend>Log</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="LogController[]" value="view" <?php echo (isset($this->data['rs']->permission['LogController']) ? (($this->data['rs']->permission['LogController'] == '*') ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear"></div>
											</fieldset>
											
											<fieldset>
												<legend>Report</legend>
												<div class="colx3-left-double">
													<span class="relative">
														View <input type="checkbox" name="ReportController[]" value="view" <?php echo (isset($this->data['rs']->permission['ReportController']) ? (($this->data['rs']->permission['ReportController'] == '*') ? 'checked="checked"' : '') : '') ?> <?php echo $disabled?> />
													</span>
												</div>
												<div class="clear"></div>
											</fieldset>
										</div>
									</div>
								</div>
							</form>
						</div>
<?php	} ?>
					</div>
				</div>
			</div>			
		</section>
		
		<section class="grid_1"></section>
		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>