<!DOCTYPE html>
<html lang="en">
<head>
	<title>OffGamers Pin Generation - My Account Info</title>
	<meta charset="utf-8"/>
	
	<!-- Combined stylesheets load -->
	<!-- Load either 960.gs.fluid or 960.gs to toggle between fixed and fluid layout -->
	<link href="/css/mini.php?files=reset,common,form,standard,960.gs.fluid,simple-lists,block-lists,planning,table,calendars,wizard,gallery" rel="stylesheet" type="text/css">
	
	<!-- Favicon -->
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<link rel="icon" type="image/png" href="/favicon-large.png">
	
	<!-- Combined JS load -->
	<!-- html5.js has to be loaded before anything else -->
	<script type="text/javascript" src="/js/mini.php?files=html5,jquery-1.12.4.min,common,old-browsers,jquery.accessibleList,searchField,standard,jquery.tip,jquery.hashchange,jquery.contextMenu,list,restaddon"></script>
	<!--[if lte IE 8]><script type="text/javascript" src="js/standard.ie.js"></script><![endif]-->
	<script type="text/javascript">
		$(document).ready(function() {
			$('#tab-relations').css('height','548px');
			$('#user_form').submit(function(e) {
				$.ajax({
					type: "POST",
					url: '/user/update-my-account/',
					data: $('#user_form').serialize(),
					success: function( data, textStatus, XMLHttpRequest ) {
						if (XMLHttpRequest.status == 200) {
							alert('Update Success!'), 'success';
						}
					},
					error: function(err) {
						alert(eval(err.responseText));
					}
				});


                return false;
            });
		});
		
		window.onbeforeunload = confirmExit;
	</script>
</head>
<body>
<!--[if lt IE 9]><div class="ie"><![endif]-->
<!--[if lt IE 8]><div class="ie7"><![endif]-->
	
    <?php $this->inc('parts/head'); ?>
    <?php $this->inc('parts/nav'); ?>
	
	<!-- Status bar -->
	<div id="status-bar"><div class="container_12">
        <?php $this->inc('parts/logout'); ?>
		<ul id="breadcrumb">
			<li><a href="javascript:void(0);" title="Home">Users</a></li>
			<li><a href="javascript:void(0);" title="Clients">User details</a></li>
		</ul>
	</div></div>
	<!-- End status bar -->
	
	<div id="header-shadow"></div>
	<!-- End header -->
	
	<!-- Always visible control bar -->
	<div class="grey-bg clearfix" id="control-bar" style="opacity: 1;">
		<div class="container_12">
			<div class="float-left">
				<button onClick="document.location.href = '/user/list-user'" type="button"><img height="16" width="16" src="/images/icons/fugue/navigation-180.png"/> Back to list</button>
			</div>
			
			<div class="float-right">
				<button type="button" onClick="$('#user_form').submit();"><img height="16" width="16" src="/images/icons/fugue/disk.png"> Save</button>
			</div>
		</div>
	</div>	
	<!-- End control bar -->
	
	<!-- Content -->
	<article class="container_12">
		<section class="grid_1"></section>
		<section class="grid_10">
			<div class="block-border">
				<form action="" method="post" id="user_form" class="block-content form">
					<h1>User Details</h1>
					
					<div class="columns">
						<div class="col200pxL-left">
							<h2>Side tabs</h2>
							<ul class="side-tabs js-tabs same-height">
								<li class="current"><a title="Global properties" href="#tab-global">User Details</a></li>
							</ul>
						</div>
						<div class="col200pxL-right">
							<div class="tabs-content" id="tab-global" style="min-height: 483px; display: block;">
								<ul class="tabs js-tabs same-height">
									<li class="current"><a title="Details" href="#tab-details">Details</a></li>
								</ul>
								
								<div class="tabs-content">
									<div id="tab-details" style="min-height: 412px;">
										<div class="infos">
										<h3><?php echo (isset($this->data['rs']->first_name) ? $this->data['rs']->first_name : '')?> <?php echo (isset($this->data['rs']->last_name) ? $this->data['rs']->last_name : '')?></h3>
										<p>Edit informations as needed below</p>
										
											<fieldset class="grey-bg">
												<legend>Required fields</legend>
												<div class="colx3-left-double required">
													<label for="complex-title">First Name</label>
													<span class="relative">
														<input type="hidden" name="id" id="user_id" value="<?php echo (isset($this->data['rs']->id) ? $this->data['rs']->id : '')?>" />
														<input type="text" class="full-width" id="first_name" name="first_name" value="<?php echo (isset($this->data['rs']->first_name) ? $this->data['rs']->first_name : '')?>" />
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>

												<div class="colx3-left-double required">
													<label for="complex-title">Last Name</label>
													<span class="relative">
														<input type="text" class="full-width" id="last_name" name="last_name" value="<?php echo (isset($this->data['rs']->last_name) ? $this->data['rs']->last_name : '')?>" />
													</span>
												</div>
												
												<div class="clear" style="padding-top:10px;"></div>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Email</label>
													<span class="relative">
														<input type="text" class="full-width" id="email" name="email" value="<?php echo (isset($this->data['rs']->email) ? $this->data['rs']->email : '')?>" />
													</span>
												</div>
											</fieldset>
											
											<fieldset>
												<legend>Account details</legend>
												
												<div class="colx3-left-double required">
													<label for="complex-title">Username</label>
													<span class="relative">
														<input type="text" class="full-width" id="username" name="username" value="<?php echo (isset($this->data['rs']->username) ? $this->data['rs']->username : '')?>" />
													</span>
												</div>
												<div class="clear" style="padding-top:10px;"></div>
												<div class="columns">
													<p class="colx2-left">
														<label for="complex-en-url">Password</label>
														<span class="relative">
															<input type="password" class="full-width" value="" id="password" name="password" autocomplete="off"/>
														</span>
													</p>
													<p class="colx2-right">
														<label for="complex-en-subtitle">Confirm Password</label>
														<input type="password" class="full-width" value="" id="password2" name="password2" autocomplete="off"/>
													</p>
												</div>
											</fieldset>
										</div>
									</div>																
								</div>
							</div>
						</div>
					</div>
				</form>	
			</div>			
		</section>
		
		<section class="grid_1"></section>

		<div class="clear"></div>
	</article>
	
	<!-- End content -->
	<?php $this->inc('parts/footer'); ?>

<!--[if lt IE 8]></div><![endif]-->
<!--[if lt IE 9]></div><![endif]-->
</body>
</html>