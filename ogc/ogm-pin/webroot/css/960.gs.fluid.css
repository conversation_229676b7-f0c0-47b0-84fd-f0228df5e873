/*
	Variable Grid System (Fluid Version).
	Learn more ~ http://www.spry-soft.com/grids/
	Based on 960 Grid System - http://960.gs/ & 960 Fluid - http://www.designinfluences.com/

	Licensed under GPL and MIT.
*/


/* Containers
----------------------------------------------------------------------------------------------------*/
.container_12 {
	width: 98%;
	margin-left: 1%;
	margin-right: 1%;
}

/* Grid >> Global
----------------------------------------------------------------------------------------------------*/

.grid_1,
.grid_2,
.grid_3,
.grid_4,
.grid_5,
.grid_6,
.grid_7,
.grid_8,
.grid_9,
.grid_10,
.grid_11,
.grid_12 {
	display:inline;
	float: left;
	margin-left: 0.99%;
	margin-right: 0.99%;
}

/* Grid >> Children (Alpha ~ First, Omega ~ Last)
----------------------------------------------------------------------------------------------------*/

.alpha {
	margin-left: 0;
}

.omega {
	margin-right: 0;
}

/* Grid >> 12 Columns
----------------------------------------------------------------------------------------------------*/


.container_12 .grid_1 {
	width:6.333%;
}

.container_12 .grid_2 {
	width:14.667%;
}

.container_12 .grid_3 {
	width:23.0%;
}

.container_12 .grid_4 {
	width:31.333%;
}

.container_12 .grid_5 {
	width:39.667%;
}

.container_12 .grid_6 {
	width:48.0%;
}

.container_12 .grid_7 {
	width:56.333%;
}

.container_12 .grid_8 {
	width:64.667%;
}

.container_12 .grid_9 {
	width:73.0%;
}

.container_12 .grid_10 {
	width:81.333%;
}

.container_12 .grid_11 {
	width:89.667%;
}

.container_12 .grid_12 {
	width:98.0%;
}



/* Prefix Extra Space >> 12 Columns
----------------------------------------------------------------------------------------------------*/


.container_12 .prefix_1 {
	padding-left:8.333%;
}

.container_12 .prefix_2 {
	padding-left:16.667%;
}

.container_12 .prefix_3 {
	padding-left:25.0%;
}

.container_12 .prefix_4 {
	padding-left:33.333%;
}

.container_12 .prefix_5 {
	padding-left:41.667%;
}

.container_12 .prefix_6 {
	padding-left:50.0%;
}

.container_12 .prefix_7 {
	padding-left:58.333%;
}

.container_12 .prefix_8 {
	padding-left:66.667%;
}

.container_12 .prefix_9 {
	padding-left:75.0%;
}

.container_12 .prefix_10 {
	padding-left:83.333%;
}

.container_12 .prefix_11 {
	padding-left:91.667%;
}



/* Suffix Extra Space >> 12 Columns
----------------------------------------------------------------------------------------------------*/


.container_12 .suffix_1 {
	padding-right:8.333%;
}

.container_12 .suffix_2 {
	padding-right:16.667%;
}

.container_12 .suffix_3 {
	padding-right:25.0%;
}

.container_12 .suffix_4 {
	padding-right:33.333%;
}

.container_12 .suffix_5 {
	padding-right:41.667%;
}

.container_12 .suffix_6 {
	padding-right:50.0%;
}

.container_12 .suffix_7 {
	padding-right:58.333%;
}

.container_12 .suffix_8 {
	padding-right:66.667%;
}

.container_12 .suffix_9 {
	padding-right:75.0%;
}

.container_12 .suffix_10 {
	padding-right:83.333%;
}

.container_12 .suffix_11 {
	padding-right:91.667%;
}



/* Push Space >> 12 Columns
----------------------------------------------------------------------------------------------------*/


.container_12 .push_1 {
	left:8.333%;
}

.container_12 .push_2 {
	left:16.667%;
}

.container_12 .push_3 {
	left:25.0%;
}

.container_12 .push_4 {
	left:33.333%;
}

.container_12 .push_5 {
	left:41.667%;
}

.container_12 .push_6 {
	left:50.0%;
}

.container_12 .push_7 {
	left:58.333%;
}

.container_12 .push_8 {
	left:66.667%;
}

.container_12 .push_9 {
	left:75.0%;
}

.container_12 .push_10 {
	left:83.333%;
}

.container_12 .push_11 {
	left:91.667%;
}



/* Pull Space >> 12 Columns
----------------------------------------------------------------------------------------------------*/


.container_12 .pull_1 {
	left:-8.333%;
}

.container_12 .pull_2 {
	left:-16.667%;
}

.container_12 .pull_3 {
	left:-25.0%;
}

.container_12 .pull_4 {
	left:-33.333%;
}

.container_12 .pull_5 {
	left:-41.667%;
}

.container_12 .pull_6 {
	left:-50.0%;
}

.container_12 .pull_7 {
	left:-58.333%;
}

.container_12 .pull_8 {
	left:-66.667%;
}

.container_12 .pull_9 {
	left:-75.0%;
}

.container_12 .pull_10 {
	left:-83.333%;
}

.container_12 .pull_11 {
	left:-91.667%;
}