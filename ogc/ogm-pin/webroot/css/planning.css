/**
 * Styles for the planning block
 */
 
.planning {
	margin-bottom: 1.667em;
	border: 1px solid #999999;
	}
	.planning:last-child {
		margin-bottom: 0;
	}
	/* IE class */
	.planning.last-child {
		margin-bottom: 0;
	}
	.planning.no-margin,
	.content-columns .planning {
		border: none;
	}
	.content-columns .planning {
		margin-bottom: 0;
	}
	.planning > li {
		height: 2.5em;
		line-height: 2.5em;
		padding-left: 15em;
		background: #f2f2f2;
		vertical-align: bottom; /* IE7 list gap fix */
	}
	.planning > li:nth-child(odd) {
		background: #e6e6e6;
	}
	/* IE class */
	.planning > li.odd {
		background: #e6e6e6;
	}
	.planning > li.planning-header {
		background: #a4a4a4 url(../images/old-browsers-bg/planning-header-bg.png) repeat-x top;
		-webkit-background-size: 100% 100%;
		-moz-background-size: 100% 100%;
		-o-background-size: 100% 100%;
		background-size: 100% 100%;
		background: -moz-linear-gradient(
			top,
			#cccccc,
			#a4a4a4
		);
		background: -webkit-gradient(
			linear,
			left top, left bottom,
			from(#cccccc),
			to(#a4a4a4)
		);
		border-top: 1px solid white;
		border-bottom: 1px solid #828282;
		color: white;
	}
	.planning > li > span,
	.planning > li > a {
		display: block;
		height: 2.5em;
		line-height: 2.5em;
		float: left;
		margin-left: -15em;
		width: 14em;
		padding: 0 0.5em;
		color: #333333;
		}
		.planning > li > span {
			color: #999999;
		}
		.planning > li > a:hover {
			background: #CCCCCC;
		}
		.planning > li > span img,
		.planning > li > a img {
			margin-bottom: -3px;
		}
		.planning > li.planning-header > span {
			width: 13.5em;
			padding: 0 0.75em;
			-moz-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			-webkit-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			color: white;
			}
			.planning > li.planning-header > span b {
				display: block;
				height: 2.5em;
				margin: 0 -0.75em;
				padding: 0 0.75em;
				border-left: 1px solid #dddddd;
				border-right: 1px solid #999999;
			}
	.planning > li > ul {
		position: relative;
		height: 2.5em;
		border-left: 1px dotted #808080;
		background: white;
		}
		.planning > li:nth-child(odd) > ul {
			background: #f2f2f2;
		}
		/* IE class */
		.planning > li.odd > ul {
			background: #f2f2f2;
		}
		.planning > li.planning-header > ul {
			border-left: 1px solid white;
			background: none;
		}
		.planning > li > ul > li {
			position: absolute;
			top: 0.5em;
			height: 1.5em;
			-moz-border-radius: 0.167em;
			-webkit-border-radius: 0.167em;
			border-radius: 0.167em;
			background: #e5e5e5 url(../images/old-browsers-bg/planning-bar-bg.png) repeat-x top;
			-webkit-background-size: 100% 100%;
			-moz-background-size: 100% 100%;
			-o-background-size: 100% 100%;
			background-size: 100% 100%;
			background: -moz-linear-gradient(
				top,
				#ffffff,
				#eeeeee 15%,
				#c2c2c2 73%,
				#e5e5e5
			);
			background: -webkit-gradient(
				linear,
				left top, left bottom,
				from(#ffffff),
				to(#e5e5e5),
				color-stop(0.15, #eeeeee),
				color-stop(0.73, #c2c2c2)
			);
			}
			.planning > li.planning-header > ul > li {
				width: 2em;
				top: auto;
				height: auto;
				text-align: center;
				margin-left: -1em;
				-moz-text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.25);
				-webkit-text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.25);
				text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.25);
				background: none;
			}
			.planning > li.planning-header > ul > li:nth-child(even) {
				font-size: 0.75em;
			}
			/* IE class */
			.planning > li.planning-header > ul > li.even {
				font-size: 0.75em;
			}
			.planning > li > ul > li.lunch,
			.planning > li > ul > li.zebras {
				-webkit-background-size: auto;
				-moz-background-size: auto;
				-o-background-size: auto;
				background-size: auto;
				background: #f2f2f2;
				top: 0;
				height: 2.5em;
				-moz-border-radius: 0;
				-webkit-border-radius: 0;
				border-radius: 0;
			}
			.planning > li:nth-child(odd) > ul > li.lunch {
				background: #e6e6e6;
			}
			/* IE class */
			.planning > li.odd > ul > li.lunch {
				background: #e6e6e6;
			}
			.planning > li > ul > li.zebras {
				border-left: 1px solid #ccc;
				border-right: 1px solid #ccc;
			}
			.planning > li > ul.zebras,
			.planning > li > ul > li.zebras {
				background: white url(../images/zebras.png);
			}
			.planning > li > ul > li.current-time {
				background: none;
				top: 0;
				height: 2.5em;
				-moz-border-radius: 0;
				-webkit-border-radius: 0;
				border-radius: 0;
				width: 0;
				border-left: 1px solid red;
			}
			.planning > li > ul > li > a,
			.planning > li > ul > li > span {
				display: block;
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				color: #666666;
				text-indent: 0.333em;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 0.92em;
				line-height: 1.45em;
				border: 1px solid #666666;
				-moz-border-radius: 0.25em;
				-webkit-border-radius: 0.25em;
				border-radius: 0.25em;
				-moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
				-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
				white-space: nowrap;
				overflow: hidden;
				}
				.planning > li > ul > li > a span,
				.planning > li > ul > li > span span {
					display: block;
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 100%;
					white-space: nowrap;
					overflow: hidden;
				}
			
			.planning > li > ul > li.milestone {
				background: #333;
				top: 0.75em;
				height: 0.833em;
				width: 0.833em;
				margin-left: -0.5em;
				border: 0.083em solid;
				border-color: #999 #000 #000 #999;
				-webkit-transform: rotate(45deg);
				-moz-transform: rotate(45deg);
				-o-transform: rotate(45deg);
				transform: rotate(45deg);
				}
				.planning > li > ul > li.milestone > a,
				.planning > li > ul > li.milestone > span {
					border: 0;
				}
			

.planning > li > ul .event-blue,
.planning > li > ul .event-green,
.planning > li > ul .event-orange,
.planning > li > ul .event-purple,
.planning > li > ul > li.event-blue a,
.planning > li > ul > li.event-green a,
.planning > li > ul > li.event-orange a,
.planning > li > ul > li.event-purple a,
.planning > li > ul > li.event-blue span,
.planning > li > ul > li.event-green span,
.planning > li > ul > li.event-orange span,
.planning > li > ul > li.event-purple span {
	color: white;
	-webkit-background-size: 100% 100%;
	-moz-background-size: 100% 100%;
	-o-background-size: 100% 100%;
	background-size: 100% 100%;
	-moz-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
	-webkit-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
.planning > li > ul .event-blue {
	background: #4398c9 url(../images/old-browsers-bg/planning-bar-blue-bg.png) repeat-x top;
	background: -moz-linear-gradient(
		top,
		#b0cde5,
		#6ec3e3 15%,
		#0e62a8 73%,
		#4398c9
	);
	background: -webkit-gradient(
		linear,
		left top, left bottom,
		from(#b0cde5),
		to(#4398c9),
		color-stop(0.15, #6ec3e3),
		color-stop(0.73, #0e62a8)
	);
}
.planning > li > ul .event-green {
	background: #56c943 url(../images/old-browsers-bg/planning-bar-green-bg.png) repeat-x top;
	background: -moz-linear-gradient(
		top,
		#b3e6b1,
		#8ae46f 15%,
		#15a80e 73%,
		#56c943
	);
	background: -webkit-gradient(
		linear,
		left top, left bottom,
		from(#b3e6b1),
		to(#56c943),
		color-stop(0.15, #8ae46f),
		color-stop(0.73, #15a80e)
	);
}
.planning > li > ul .event-orange {
	background: #c99c43 url(../images/old-browsers-bg/planning-bar-orange-bg.png) repeat-x top;
	background: -moz-linear-gradient(
		top,
		#e6d4b1,
		#e4bd6f 15%,
		#a8750e 73%,
		#c99c43
	);
	background: -webkit-gradient(
		linear,
		left top, left bottom,
		from(#e6d4b1),
		to(#c99c43),
		color-stop(0.15, #e4bd6f),
		color-stop(0.73, #a8750e)
	);
}
.planning > li > ul .event-purple {
	background: #b543c9 url(../images/old-browsers-bg/planning-bar-purple-bg.png) repeat-x top;
	background: -moz-linear-gradient(
		top,
		#e3b1e6,
		#c86fe4 15%,
		#a10ea8 73%,
		#b543c9
	);
	background: -webkit-gradient(
		linear,
		left top, left bottom,
		from(#e3b1e6),
		to(#b543c9),
		color-stop(0.15, #c86fe4),
		color-stop(0.73, #a10ea8)
	);
}

.from-7-30, .at-7-30 { left: 0; }
.from-7-45, .at-7-45 { left: 1.92%; }
.from-8, .at-8, .from-8-00, .at-8-00 { left: 3.85%; }
.from-8-15, .at-8-15 { left: 5.77%; }
.from-8-30, .at-8-30 { left: 7.69%; }
.from-8-45, .at-8-45 { left: 9.62%; }
.from-9, .at-9, .from-9-00, .at-9-00 { left: 11.54%; }
.from-9-15, .at-9-15 { left: 13.46%; }
.from-9-30, .at-9-30 { left: 15.38%; }
.from-9-45, .at-9-45 { left: 17.31%; }
.from-10, .at-10, .from-10-00, .at-10-00 { left: 19.23%; }
.from-10-15, .at-10-15 { left: 21.15%; }
.from-10-30, .at-10-30 { left: 23.08%; }
.from-10-45, .at-10-45 { left: 25%; }
.from-11, .at-11, .from-11-00, .at-11-00 { left: 26.92%; }
.from-11-15, .at-11-15 { left: 28.85%; }
.from-11-30, .at-11-30 { left: 30.77%; }
.from-11-45, .at-11-45 { left: 32.69%; }
.from-12, .at-12, .from-12-00, .at-12-00 { left: 34.62%; }
.from-12-15, .at-12-15 { left: 36.54%; }
.from-12-30, .at-12-30 { left: 38.46%; }
.from-12-45, .at-12-45 { left: 40.38%; }
.from-13, .at-13, .from-13-00, .at-13-00 { left: 42.31%; }
.from-13-15, .at-13-15 { left: 44.23%; }
.from-13-30, .at-13-30 { left: 46.15%; }
.from-13-45, .at-13-45 { left: 48.08%; }
.from-14, .at-14, .from-14-00, .at-14-00 { left: 50%; }
.from-14-15, .at-14-15 { left: 51.92%; }
.from-14-30, .at-14-30 { left: 53.85%; }
.from-14-45, .at-14-45 { left: 55.77%; }
.from-15, .at-15, .from-15-00, .at-15-00 { left: 57.69%; }
.from-15-15, .at-15-15 { left: 59.62%; }
.from-15-30, .at-15-30 { left: 61.54%; }
.from-15-45, .at-15-45 { left: 63.46%; }
.from-16, .at-16, .from-16-00, .at-16-00 { left: 65.38%; }
.from-16-15, .at-16-15 { left: 67.31%; }
.from-16-30, .at-16-30 { left: 69.23%; }
.from-16-45, .at-16-45 { left: 71.15%; }
.from-17, .at-17, .from-17-00, .at-17-00 { left: 73.08%; }
.from-17-15, .at-17-15 { left: 75%; }
.from-17-30, .at-17-30 { left: 76.92%; }
.from-17-45, .at-17-45 { left: 78.85%; }
.from-18, .at-18, .from-18-00, .at-18-00 { left: 80.77%; }
.from-18-15, .at-18-15 { left: 82.69%; }
.from-18-30, .at-18-30 { left: 84.62%; }
.from-18-45, .at-18-45 { left: 86.54%; }
.from-19, .at-19, .from-19-00, .at-19-00 { left: 88.46%; }
.from-19-15, .at-19-15 { left: 90.38%; }
.from-19-30, .at-19-30 { left: 92.31%; }
.from-19-45, .at-19-45 { left: 94.23%; }
.from-20, .at-20, .from-20-00, .at-20-00 { left: 96.15%; }
.from-20-15, .at-20-15 { left: 98.08%; }

.to-7-30 { right: 100%; }
.to-7-45 { right: 98.08%; }
.to-8, .to-8-00 { right: 96.15%; }
.to-8-15 { right: 94.23%; }
.to-8-30 { right: 92.31%; }
.to-8-45 { right: 90.38%; }
.to-9, .to-9-00 { right: 88.46%; }
.to-9-15 { right: 86.54%; }
.to-9-30 { right: 84.62%; }
.to-9-45 { right: 82.69%; }
.to-10, .to-10-00 { right: 80.77%; }
.to-10-15 { right: 78.85%; }
.to-10-30 { right: 76.92%; }
.to-10-45 { right: 75%; }
.to-11, .to-11-00 { right: 73.08%; }
.to-11-15 { right: 71.15%; }
.to-11-30 { right: 69.23%; }
.to-11-45 { right: 67.31%; }
.to-12, .to-12-00 { right: 65.38%; }
.to-12-15 { right: 63.46%; }
.to-12-30 { right: 61.54%; }
.to-12-45 { right: 59.62%; }
.to-13, .to-13-00 { right: 57.69%; }
.to-13-15 { right: 55.77%; }
.to-13-30 { right: 53.85%; }
.to-13-45 { right: 51.92%; }
.to-14, .to-14-00 { right: 50%; }
.to-14-15 { right: 48.08%; }
.to-14-30 { right: 46.15%; }
.to-14-45 { right: 44.23%; }
.to-15, .to-15-00 { right: 42.31%; }
.to-15-15 { right: 40.38%; }
.to-15-30 { right: 38.46%; }
.to-15-45 { right: 36.54%; }
.to-16, .to-16-00 { right: 34.62%; }
.to-16-15 { right: 32.69%; }
.to-16-30 { right: 30.77%; }
.to-16-45 { right: 28.85%; }
.to-17, .to-17-00 { right: 26.92%; }
.to-17-15 { right: 25%; }
.to-17-30 { right: 23.08%; }
.to-17-45 { right: 21.15%; }
.to-18, .to-18-00 { right: 19.23%; }
.to-18-15 { right: 17.31%; }
.to-18-30 { right: 15.38%; }
.to-18-45 { right: 13.46%; }
.to-19, .to-19-00 { right: 11.54%; }
.to-19-15 { right: 9.62%; }
.to-19-30 { right: 7.69%; }
.to-19-45 { right: 5.77%; }
.to-20, .to-20-00 { right: 3.85%; }
.to-20-15 { right: 1.92%; }