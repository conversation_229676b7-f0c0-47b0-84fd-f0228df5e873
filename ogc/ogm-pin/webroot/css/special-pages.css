/* Custom styles for the special pages */

html {
	height: 100%;
}
.login-bg,
.wizard-bg {
	background: url(../images/bg.png) no-repeat center -200px;
	min-height: 100%;
}
.error-bg,
.code-page {
	background: url(../images/old-browsers-bg/login-radial-bg.png) no-repeat center center;
	-webkit-background-size: 100% 100%;
	-moz-background-size: 100% 100%;
	-o-background-size: 100% 100%;
	background-size: 100% 100%;
	background: -moz-radial-gradient(center, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0)), #70828f;
	background: -webkit-gradient(radial, 50% 50%, 10, 50% 50%, 500, from(rgba(255, 255, 255, 0.8)), to(rgba(255, 255, 255, 0))), #70828f;
	min-height: 100%;
	}
	.login-bg section,
	.wizard-bg section,
	.error-bg section,
	.code-page section {
		position: absolute;
		z-index: 89;
		left: 50%;
		top: 50%;
		}
		.login-bg section,
		.error-bg section {
			width: 34em;
			margin-left: -17em;
			margin-top: -15em;
			}
			.login-bg section#message {
				margin-top: -23.5em;
			}
			.error-bg section {
				-moz-transition: all 200ms;
				-webkit-transition: all 200ms;
				-o-transition: all 200ms;
				transition: all 200ms;
			}
			.error-bg section#error-log {
				z-index: 88;
				opacity: 0;
				filter: alpha(opacity=0);
			}
			.error-bg.with-log section {
				margin-top: -19em;
				}
				.error-bg.with-log section#error-desc {
					margin-left: -36em;
				}
				.error-bg.with-log section#error-log {
					margin-left: 2em;
					opacity: 100;
					filter: none;
				}
			
		.wizard-bg section {
			width: 64em;
			margin-left: -32em;
			margin-top: -23em;
		}
	.login-bg .block-content,
	.wizard-bg .block-content,
	.error-bg .block-content {
		-moz-box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
		-webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
		box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
	}
	.login-bg .block-border,
	.wizard-bg .block-border,
	.error-bg .block-border {
		-moz-box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
		-webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
		box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
		}
		.login-bg .block-border .block-content,
		.wizard-bg .block-border .block-content,
		.error-bg .block-border .block-content {
			-moz-box-shadow: 0 0 0.8em rgba(255, 255, 255, 0.5);
			-webkit-box-shadow: 0 0 0.8em rgba(255, 255, 255, 0.5);
			box-shadow: 0 0 0.8em rgba(255, 255, 255, 0.5);
		}
		
	.error-bg #send-report p {
		padding-left: 10em;
		}
		.error-bg #send-report p .float-left {
			margin-left: -10em;
			width: 8em;
		}

/******** Page for code errors : 404... *********/
.code-page {
	text-align: center;
	}
	.code-page h1 {
		position: absolute;
		left: 0;
		right: 0;
		top: 50%;
		margin-top: -1em;
		text-align: center;
		font-size: 16em;
		color: #A1A8AB;
		-moz-text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
		-webkit-text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
		text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
	}
	.code-page p {
		position: absolute;
		left: 0;
		right: 0;
		top: 50%;
		text-align: center;
		font-size: 3em;
		font-weight: bold;
		text-transform: uppercase;
		-moz-text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
		-webkit-text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
		text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.35), 1px 1px 1px rgba(255, 255, 255, 0.75);
	}
	.code-page section {
		margin-top: 5em;
		width: 28em;
		margin-left: -14em;
		}
		.code-page section .action-tabs {
			padding-top: 1em;
			}
			.code-page section .action-tabs.on-form {
				padding-top: 1.75em;
			}
		.code-page section form.block-content {
			padding-left: 0;
			padding-right: 0;
			}
			.code-page section form.block-content #s {
				width: 15em;
			}

/************* Mobile customization *************/
@media only screen and (max-device-width: 480px) {
	
	.login-bg,
	.wizard-bg {
		background: #70828f url(../images/bg-mobile.png) no-repeat center -200px;
		}
		.login-bg,
		.error-bg {
			padding: 1em;
		}
		.login-bg section,
		.wizard-bg section,
		.error-bg section {
			position: static;
			width: auto;
			margin-top: 0;
			margin-left: 0;
			left: auto;
			top: auto;
			}
			.error-bg section#error-log {
				opacity: 100;
				filter: none;
			}
	.error-bg,
	.code-page {
		background: #70828f url(../images/old-browsers-bg/login-radial-bg-mobile.png) no-repeat center center;
		background: -moz-radial-gradient(center, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0)), #70828f;
		background: -webkit-gradient(radial, 50% 50%, 10, 50% 50%, 240, from(rgba(255, 255, 255, 0.8)), to(rgba(255, 255, 255, 0))), #70828f;
	}
	
	.error-bg #send-report p {
		padding: 0;
		}
		.error-bg #send-report p .float-left {
			margin-left: 0;
			width: auto;
		}
		.error-bg #send-report p span.float-left {
			float: right;
			width: 40%;
		}
		.error-bg #send-report p #sender {
			width: 50%;
		}
	
	/******** Page for code errors : 404... *********/
	.code-page h1 {
		font-size: 10em;
	}
	.code-page p {
		font-size: 1.75em;
	}
	.code-page section {
		width: auto;
		top: auto;
		left: 0;
		right: 0;
		bottom: 0;
		margin: 0;
		}
		.code-page section .action-tabs {
			display: none;
		}
	
}