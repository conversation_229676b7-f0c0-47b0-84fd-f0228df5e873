/**
 * Styles for tables and grid view
 */
 
.table, .with-head {
	margin-bottom: 1.667em;
	border: 1px solid #999999;
	}
	.table {
		border-collapse: separate;
	}
	.table:last-child,
	.with-head:last-child {
		margin-bottom: 0;
	}
	/* IE class */
	.table.last-child,
	.with-head.last-child {
		margin-bottom: 0;
	}
	.no-margin .table,
	.content-columns .table,
	.with-head.no-margin,
	.content-columns .with-head {
		border: none;
	}
	.no-margin .table + .no-margin,
	.with-head.no-margin + .no-margin {
		margin-top: -1.667em;
	}
	.content-columns .table:first-child,
	.content-columns .with-head:first-child {
		border: none;
	}
	/* IE class */
	.content-columns .table.first-child,
	.content-columns .with-head.first-child {
		border: none;
	}
	.content-columns .table,
	.content-columns .with-head {
		margin-bottom: 0;
	}
	.table thead th,
	.table thead td,
	.head {
		background: #a4a4a4 url(../images/old-browsers-bg/planning-header-bg.png) repeat-x top;
		-webkit-background-size: 100% 100%;
		-moz-background-size: 100% 100%;
		-o-background-size: 100% 100%;
		background-size: 100% 100%;
		background: -moz-linear-gradient(
			top,
			#cccccc,
			#a4a4a4
		);
		background: -webkit-gradient(
			linear,
			left top, left bottom,
			from(#cccccc),
			to(#a4a4a4)
		);
		color: white;
		-moz-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
		-webkit-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
		border-top: 1px solid white;
		border-left: 1px solid #dddddd;
		border-right: 1px solid #999999;
		border-bottom: 1px solid #828282;
	}
	.table thead th,
	.table thead td {
		vertical-align: middle;
		text-align: left;
		padding: 0.5em 0.75em;
	}
	.head {
		font-weight: bold;
		line-height: 1.5em;
		}
		.head > div {
			float: left;
			padding: 0.5em 2em 0.5em 0.75em;
			border-left: 1px solid #dddddd;
			border-right: 1px solid #999999;
			color: white;
			margin: -1px 0 0 0;
			-moz-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			-webkit-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
			}
			.head > div:first-child {
				margin-left: -1px;
			}
			/* IE class */
			.head > div.first-child {
				margin-left: -1px;
			}
			.head > div:last-of-type {
				border-right: none;
			}
			/* IE class */
			.head > div.last-of-type {
				border-right: none;
			}
	
	.head .button {
		float: right;
		margin: 0.25em 0.5em 0 0;
	}
	.head > div .button {
		float: left;
		margin: -0.167em 0.5em -0.333em 0;
		}
		.head > div .button:last-child {
			margin-right: 0;
		}
		/* IE class */
		.head > div .button.last-child {
			margin-right: 0;
		}
	
	.table tbody th,
	.table tbody td,
	.table tfoot th,
	.table tfoot td {
		vertical-align: top;
		text-align: left;
		padding: 0.75em;
		border-left: 1px dotted #333333;
		}
		.table tbody th {
			background: #e6e6e6;
		}
		.table tbody td {
			background: #f2f2f2;
		}
		.table tfoot th,
		.table tfoot td {
			border-top: 1px solid #FF9900;
			background: #999999 url(../images/old-browsers-bg/tfoot-bg.png) repeat-x top;
			-o-background-size: 100% 100%;
			-moz-background-size: 100% 100%;
			-webkit-background-size: 100% 100%;
			background-size: 100% 100%;
			background: -moz-linear-gradient(
				top,
				#333333,
				#999999
			);
			background: -webkit-gradient(
				linear,
				left top, left bottom,
				from(#333333),
				to(#999999)
			);
			color: white;
		}
		.table tbody th:first-child,
		.table tbody td:first-child,
		.table tfoot th:first-child,
		.table tfoot td:first-child {
			border-left: none;
		}
		/* IE class */
		.table tbody th.first-child,
		.table tbody td.first-child,
		.table tfoot th.first-child,
		.table tfoot td.first-child {
			border-left: none;
		}
		.table tbody tr:nth-child(even) th {
			background: #d9d9d9;
		}
		/* IE class */
		.table tbody tr.even th {
			background: #d9d9d9;
		}
		.table tbody tr:nth-child(even) td {
			background: #e6e6e6;
		}
		/* IE class */
		.table tbody tr.even td {
			background: #e6e6e6;
		}
		.table tbody tr:hover th,
		.table tbody tr:hover td {
			background: #d1e5ef;
		}
	
	.table .black-cell,
	.head .black-cell {
		background: #242424 url(../images/old-browsers-bg/black-cell-bg.png) repeat-x top;
		-webkit-background-size: 100% 100%;
		-moz-background-size: 100% 100%;
		-o-background-size: 100% 100%;
		background-size: 100% 100%;
		background: -moz-linear-gradient(
			top,
			#4c4c4c,
			#242424
		);
		background: -webkit-gradient(
			linear,
			left top, left bottom,
			from(#4c4c4c),
			to(#242424)
		);
		border-top-color: #7f7f7f;
		border-left: none;
		border-right-color: #191919;
		min-width: 1.333em;
		padding: 0.5em 0.583em;
		}
		/* IE class */
		.ie7 .head .black-cell {
			height: 1.5em;
			position: relative;
			z-index: 89;
		}
		.head .black-cell.with-gap {
			border-right-color: white;
			margin-right: 0.25em
			}
			.head .black-cell.with-gap + .black-cell {
				border-left: 1px solid #999999;
			}
		.table .black-cell span,
		.head .black-cell span {
			display: block;
			height: 2.5em;
			background-repeat: no-repeat;
			background-position: center;
			margin: -0.5em -0.75em;
			}
			/* IE class */
			.ie7 .head .black-cell span {
				position: absolute;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				height: auto;
				padding: 0;
			}
			.table .black-cell span.loading, .with-head .black-cell span.loading { background-image: url(../images/table-loader.gif); }
			.table .black-cell span.error, .with-head .black-cell span.error { background-image: url(../images/icons/fugue/cross-circle.png); }
			.table .black-cell span.success, .with-head .black-cell span.success { background-image: url(../images/icons/fugue/tick-circle-blue.png); }
	
	.table-actions a img {
		margin: -2px 0;
	}
	
/************ Sort arrows ************/
.column-sort {
	display: block;
	float: left;
	width: 14px;
	margin: -0.583em 0.5em -0.583em -0.75em;
	border-right: 1px solid #dddddd;
	}
	.head .column-sort {
		margin: -0.5em 0.5em -0.5em -0.75em;
	}
	.column-sort .sort-up,
	.column-sort .sort-down {
		display: block;
		width: 13px;
		height: 14px;
		background: url(../images/table-sort-arrows.png) no-repeat;
		border-right: 1px solid #999999;
	}
	.column-sort .sort-up {
		background-position: 0 1px;
		border-bottom: 1px solid #828282;
	}
	.column-sort .sort-down {
		background-position: 0 bottom;
		border-top: 1px solid white;
	}
	.column-sort .sort-up:hover { background-position: -15px 1px; }
	.column-sort .sort-down:hover { background-position: -15px bottom; }
	.column-sort .sort-up:active, .column-sort .sort-up.active { background-position: -30px 1px; }
	.column-sort .sort-down:active, .column-sort .sort-down.active { background-position: -30px bottom; }

/************ Cell styles ************/
.table-check-cell {
	width: 1em;
}

/* http://perishablepress.com/press/2008/02/05/lessons-learned-concerning-the-clearfix-css-hack */
.head:after,
ul.grid:after {
	clear: both;
	content: ' ';
	display: block;
	font-size: 0;
	line-height: 0;
	visibility: hidden;
	width: 0;
	height: 0;
}

.head,
ul.grid {
	display: inline-block;
}

* html .head,
* html ul.grid {
	height: 1%;
}

.head,
ul.grid {
	display: block;
}