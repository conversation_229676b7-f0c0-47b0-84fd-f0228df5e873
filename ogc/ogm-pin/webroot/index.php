<?php
include '../application/config/common.conf.php';
include '../application/config/routes.conf.php';
include '../application/config/db.conf.php';

#Just include this for production mode
//include $config['BASE_PATH'].'deployment/deploy.php';
include $config['BASE_PATH'].'Doo.php';
include $config['BASE_PATH'].'app/DooConfig.php';

# Uncomment for auto loading the framework classes.
spl_autoload_register('Doo::autoload');

Doo::conf()->set($config);

# remove this if you wish to see the normal PHP error view.
if($config['APP_MODE']=='dev')
    include $config['BASE_PATH'].'diagnostic/debug.php';
//else
//    include '../application/config/debug.php';


# database usage
//Doo::useDbReplicate();	#for db replication master-slave usage
Doo::db()->setMap($dbmap);
Doo::db()->setDb($dbconfig, $config['APP_MODE']);

//if($config['APP_MODE']=='dev')
    Doo::db()->sql_tracking = true;	#for debugging/profiling purpose

Doo::loadController('AppController');
Doo::app()->route = $route;

Doo::app()->run();

