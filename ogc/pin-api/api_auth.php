<?php
require_once 'config.php';
require_once 'DooRestClient.php';

$jsondata = json_encode(array('merchant_code' => $_POST['merchant_code'], 'auth' => $_POST['auth'], 'hash' => $_POST['hash'], 'scope' => $_POST['scope']));
$data = array('data' => $jsondata);

$c = new DooRestClient;
$c->connectTo($host . 'authenticate')
  ->data($data)
  ->post();

if ($c->isSuccess()) {
	header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
	echo $c->result();
} else {
	header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
	echo $c->result();
}