<?php

require_once 'config.php';
require_once 'DooRestClient.php';

//token is get from the API result auth

$jsondata = json_encode(array(
    'pin' => (isset($_POST['pin']) ? $_POST['pin'] : ""),
    'serial' => $_POST['serial'],
    'token' => $_POST['token'],
    'flag_redeem' => (isset($_POST['flag_redeem']) ? $_POST['flag_redeem'] : false),
    'merchant_id' => isset($_POST['merchant_id']) ? $_POST['merchant_id'] : null
        ));
$data = array('data' => $jsondata);

$c = new DooRestClient;
$c->connectTo($host . 'get_pin_info')
        ->data($data)
        ->post();

if ($c->isSuccess()) {
    header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
    echo $c->result();
} else {
    header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
    echo $c->result();
}