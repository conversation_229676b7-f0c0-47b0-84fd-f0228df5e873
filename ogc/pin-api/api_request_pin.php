<?php

require_once 'config.php';
require_once 'DooRestClient.php';

$raw_data = file_get_contents("php://input");
$data_array = json_decode($raw_data, true);

$data = array('GP_REQUEST' => json_encode($data_array['GP_REQUEST']));

$c = new DooRestClient;
$c->connectTo($host . 'pin_request')
        ->data($data)
        ->post();

if ($c->isSuccess()) {
    header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
    echo $c->result();
} else {
    header('Content-Type: application/json; charset=utf8', true, $c->resultCode());
    echo $c->result();
}