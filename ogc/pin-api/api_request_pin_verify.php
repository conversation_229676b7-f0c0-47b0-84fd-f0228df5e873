<?php
require_once 'config.php';
require_once 'DooRestClient.php';

$raw_data = json_decode(stripslashes($_POST['GP_REQUEST']), true);

$data = array('GP_REQUEST' => $raw_data);
$json_string = json_encode($data);

$ch = curl_init($_POST['VERIFY_URL']);

$agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $json_string);
curl_setopt($ch, CURLOPT_HTTPHEADER, Array("Expect:"));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
curl_setopt($ch, CURLOPT_VERBOSE, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_TIMEOUT, 120);
curl_setopt($ch, CURLOPT_USERAGENT, $agent);
curl_setopt($ch, CURLOPT_HEADER, 0);

$response = curl_exec($ch);
curl_close($ch);