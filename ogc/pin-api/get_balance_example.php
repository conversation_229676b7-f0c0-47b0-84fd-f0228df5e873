<html>
	<head>
		<title>Get Balance</title>
		<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.6.2/jquery.min.js" type="text/javascript"></script>
		<script>
			function auth() {
				$.ajax({
					type: 'POST',
					url: 'https://api.offgamers.biz/ws/pin/api_auth.php',
					data: { merchant_code: 'OGM', auth: '37a5a646bce0d80eb743497c531f4043', hash: '1234', scope: 'getPinInfo' },
					success: function(data, textStatus, XMLHttpRequest) {
						if (XMLHttpRequest.status == 200) {
							getBalance(data.token);
						}
					},
					error: function(XMLHttpRequest, textStatus, errorThrown){
						var obj = jQuery.parseJSON(XMLHttpRequest.responseText);
						alert(obj.error);
					},
					dataType: 'json'
				});
			}
			
			function getBalance(token) {
				$.ajax({
					type: 'POST',
					url: 'https://api.offgamers.biz/ws/pin/api_get_balance.php',
					data: {token: token, serial: $('#serial').val(), pin: $('#pin').val() },
					success: function(data, textStatus, XMLHttpRequest) {
						if (XMLHttpRequest.status == 200) {
							alert(data.currency + data.deno);
						}
					},
					error: function(XMLHttpRequest, textStatus, errorThrown){
						var obj = jQuery.parseJSON(XMLHttpRequest.responseText);
						alert(obj.error);
					},
					dataType: 'json'
				});
			}
		</script>
	</head>
	<body>
		<table align="center">
			<tr>
				<td>Serial</td>
				<td><input type="text" name="serial" id="serial"></td>
			</tr>
			<tr>
				<td>Pin</td>
				<td><input type="text" name="pin" id="pin"></td>
			</tr>
			<tr>
				<td align="right" colspan="2"><button name="redeem" title="redeem" onClick="auth();">Redeem</button></td>
			</tr>
		</table>
	</body>
</html>