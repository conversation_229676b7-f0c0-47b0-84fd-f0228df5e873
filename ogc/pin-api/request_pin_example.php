<html>
	<head>
		<title>Request Pin</title>
		<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.6.2/jquery.min.js" type="text/javascript"></script>
		<script>
			function request() {
				$.ajax({
					type: 'POST',
					url: 'https://api.offgamers.biz/ws/pin/api_request_pin.php',
					data: { 'GP_REQUEST' : {"mm_cmd":"GP_REQ","tran_id":"687","req":[{"softpin_amt":"50.00","quantity":"5","reseller_id":"MobileMoney","description":"OffGamers Restock - RM50.00","sub_date":"2011/12/16 10:30:33","hash_data":"c48a78f29318d3e4012197b12586f3c58ef2ea04"}]}},
					success: function(data, textStatus, XMLHttpRequest) {
						if (XMLHttpRequest.status == 200) {
							console.log(data);
						}
					},
					error: function(XMLHttpRequest, textStatus, errorThrown){
						var obj = $.parseJSON(XMLHttpRequest.responseText);
						alert(obj.tran_errcode);
					},
					dataType: 'json'
				});
			}
		</script>
	</head>
	<body>
		<table align="center" border="1" cellpadding="2" cellspacing="0">
			<tr>
				<td>Secret key:</td><td>axkuy87</td>
			</tr>
			<tr>
				<td>tran_id:</td><td>687</td>
			</tr>
			<tr>
				<td>softpin_amt:</td><td>50.00</td>
			</tr>
			<tr>
				<td>Before encrypt:</td><td>axkuy8768750.00</td>
			</tr>
			<tr>
				<td>After encrypt (hash):</td><td>c48a78f29318d3e4012197b12586f3c58ef2ea04</td>
			</tr>
			</tr>
			<tr>
				<td align="center" colspan="2"><button name="redeem" title="redeem" onClick="request();">Request Pin</button></td>
			</tr>
		</table>
	</body>
</html>
