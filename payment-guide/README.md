Yii 2 Advanced Project Template
===============================

Yii 2 Advanced Project Template is a skeleton [Yii 2](http://www.yiiframework.com/) application best for
developing complex Web applications with multiple tiers.

The template includes three tiers: front end, back end, and console, each of which
is a separate Yii application.

The template is designed to work in a team development environment. It supports
deploying the application in different environments.

Documentation is at [docs/guide/README.md](docs/guide/README.md).

[![Latest Stable Version](https://poser.pugx.org/yiisoft/yii2-app-advanced/v/stable.png)](https://packagist.org/packages/yiisoft/yii2-app-advanced)
[![Total Downloads](https://poser.pugx.org/yiisoft/yii2-app-advanced/downloads.png)](https://packagist.org/packages/yiisoft/yii2-app-advanced)
[![Build Status](https://travis-ci.org/yiisoft/yii2-app-advanced.svg?branch=master)](https://travis-ci.org/yiisoft/yii2-app-advanced)

DIRECTORY STRUCTURE
-------------------

```
common
    config/              contains shared configurations
    mail/                contains view files for e-mails
    models/              contains model classes used in both backend and frontend
    tests/               contains tests for common classes    
console
    config/              contains console configurations
    controllers/         contains console controllers (commands)
    migrations/          contains database migrations
    models/              contains console-specific model classes
    runtime/             contains files generated during runtime
backend
    assets/              contains application assets such as JavaScript and CSS
    config/              contains backend configurations
    controllers/         contains Web controller classes
    models/              contains backend-specific model classes
    runtime/             contains files generated during runtime
    tests/               contains tests for backend application    
    views/               contains view files for the Web application
    web/                 contains the entry script and Web resources
frontend
    assets/              contains application assets such as JavaScript and CSS
    config/              contains frontend configurations
    controllers/         contains Web controller classes
    models/              contains frontend-specific model classes
    runtime/             contains files generated during runtime
    tests/               contains tests for frontend application
    views/               contains view files for the Web application
    web/                 contains the entry script and Web resources
    widgets/             contains frontend widgets
vendor/                  contains dependent 3rd-party packages
environments/            contains environment-based overrides
```

FRONTEND
-------------------

### SCSS STRUCTURE
```
frontend/themes/common/assets/scss
    _base.scss           contains basic common classes and styles
    _browser.scss        contains in-use browser detection
    _colors.scss         contains definition of basic theme color
    _grids.scss          contains theme gridding structures and settings

frontend/themes/{{custom}}/assets/scss
    _main.scss           contains import and basic CSS settings for each theme

```
**compile _main.scss to generate full CSS file.*

### GRID SYSTEM

```
class for column size        
    basic                col-1 to col-12

class with media rules
    small                col-sm-1 to col-sm-12   (max 576px)
    medium               col-md-1 to col-md-12   (max 768px)
    large                col-lg-1 to col-lg-12   (max 992px)
    extra large          col-xl-1 to col-xl-12   (max 1200px)
    double extra large   col-xxl-1 to col-xxl-12 (max 1588px)
```

### Deployment with Asset change

When there is an asset (css / js / img) change, during deployment, there is an additional process to refresh the asset path. To refresh the asset path, edit `frontend/config/main-local.php` and change the sub-path of `components.assetManager.basePath` and `components.assetManager.baseUrl` to something new. Example:

from:
```
'assetManager' => [
    'basePath' => 's3://{s3-path}/payment-guide/g2g-asset1102',
    'baseUrl' => 'https://{s3-url}/payment-guide/g2g-asset1102',
],
```

to:
```
'assetManager' => [
    'basePath' => 's3://{s3-path}/payment-guide/g2g-asset1225',
    'baseUrl' => 'https://{s3-url}/payment-guide/g2g-asset1225',
],
