<?php

namespace backend\components;

use Yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\helpers\Json;
use yii\validators\Validator;

class Recaptcha extends \himiklab\yii2\recaptcha\ReCaptchaValidator{

  protected function validateValue($value)
  {
      if(empty(Yii::$app->params["captchaLoginAttempt"]) || $_SESSION['loginAttempt'] < Yii::$app->params["captchaLoginAttempt"]){
        return null;
      }
      if (empty($value)) {
          if (!($value = Yii::$app->request->post(self::CAPTCHA_RESPONSE_FIELD))) {
              return [$this->message, []];
          }
      }

      $request = self::SITE_VERIFY_URL . '?' . http_build_query([
              'secret' => $this->secret,
              'response' => $value,
              'remoteip' => Yii::$app->request->userIP
          ]);
      $response = $this->getResponse($request);
      if (!isset($response['success'])) {
          throw new Exception('Invalid recaptcha verify response.');
      }
      return $response['success'] ? null : [$this->message, []];
  }

}
