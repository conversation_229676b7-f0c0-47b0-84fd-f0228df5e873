<?php

namespace backend\components;

use Yii;
use common\models\Language;
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use common\models\PaymentCategory;
use common\models\PaymentGateway;
use common\models\PaymentMethod;


class SharedComponent
{
  /**
   * Generate bootstrap tab nav bar
   */
    public static function createNavBar()
    {
        $navTab = "<ul class='nav nav-tabs'>";
        $language = Language::getActiveLanguage();
        foreach ($language as $value) {
            $isDefaultLang = ($value->iso_code === "en-US");
            $navTab .= Html::tag('li', "<a data-toggle='tab' href='#{$value->iso_code}'>{$value->name}</a>", ['class' => ($isDefaultLang ? 'active' : '')]);
        }
        $navTab .= "</ul>";
        return $navTab;
    }

    /**
     * Generate bootstrap tab content
     */
    public static function createContentTab($content, $translation, $isNewRecord)
    {
        $language = Language::getActiveLanguage();
        $html = "<div id='translation' class='tab-content'>";
        if (count($translation))
            $isNewRecord = false;
        foreach ($language as $value) {
            $iso_code = $value->iso_code;
            $string = str_replace("%iso_code%", $iso_code, $content);
            $string = str_replace("%required%", ($iso_code == "en-US" ? 'required' : ''), $string);
            if (!$isNewRecord) {
                preg_match_all("/(?<=%value%-)[a-z0-9_]*/", $string, $preg);
                for ($i = 0; $i < count($preg[0]); $i++) {
                    $expression = "%value%-" . $preg[0][$i] . "(?=\"|\<)";
                    $string = preg_replace("/" . $expression . "/", htmlentities($translation[$preg[0][$i]][$iso_code]), $string);
                }
            } else {
                $string = preg_replace("/%value%-[a-z0-9_]*(?=\"|\<)/", "", $string);
            }
            $html .= "<div id='{$iso_code}' class='tab-pane fade" . ($iso_code === "en-US" ? " in active" : "") . "'><div style='padding:0px 15px 15px 20px;margin-bottom:15px;'><br>" .
                $string .
                "</div></div>";
        }
        $html .= "</div>";
        return $html;
    }

    /**
     * Generate status for content
     */
    public static function createStatus($form, $model)
    {
        return $form->field($model, 'status')->dropDownList(['1' => 'Active', '0' => 'Inactive']);
    }

    /**
     * Get Translated Items
     * @param type $model
     * @return type
     */
    public static function getTranslatedItemList($model)
    {
        switch ($model) {
            case 'PaymentCategory':
                $dataList = PaymentCategory::find()->where(['status' => 1])->asArray()->all();
                $translate_category = (new PaymentCategory)->translate_category;
                break;
            case 'PaymentGateway':
                $dataList = PaymentGateway::find()->where(['status' => 1])->asArray()->all();
                $translate_category = (new PaymentGateway)->translate_category;
                break;
            case 'PaymentMethod':
                $dataList = PaymentMethod::find()->where(['status' => 1])->asArray()->all();
                $translate_category = (new PaymentMethod)->translate_category;
                break;
        }
        foreach ($dataList as $index => $data) {
          $dataList[$index]['name'] = Yii::t($translate_category, $data['name'], '', 'en-US');
        }
        ArrayHelper::multisort($dataList,'name');
        return ArrayHelper::map($dataList,'id','name');
    }

    public static function getDropdownOption($data)
    {
        $languageList = Language::getActiveLanguage();
        return ArrayHelper::map($data, 'id', function ($model, $defaultValue) use (&$languageList) {
            $array = [];
            foreach ($languageList as $language) {
                $array[$language["iso_code"]] = $model[$language["iso_code"]];
            }
            return $array;
        });
    }

    /**
     * Function to generate error message
     * @param type $errors
     * @return type
     */
    public static function generateErrorMessage($errors)
    {
        $string = "";
        foreach ($errors as $key => $value) {
            $string .= $key . "<br>" . self::generateHtmlList($value);
        }
        return "<div class='alert alert-danger'><a href='#' class='close' data-dismiss='alert' aria-label='close'>&times;</a>$string</div>";
    }

    /**
     * Function to generate HTML list for error messages
     * @param type $arr
     * @return string
     */
    public static function generateHtmlList($arr)
    {
        $string = "<ul>";
        foreach ($arr as $data) {
            $string .= "<li>" . $data . "</li>";
        }
        $string .= "<ul>";
        return $string;
    }

}
