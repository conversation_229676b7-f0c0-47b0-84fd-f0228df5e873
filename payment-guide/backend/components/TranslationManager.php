<?php

namespace backend\components;

use Yii;
use common\models\SourceMessage;
use common\models\Message;
use common\models\Language;
use yii\caching\TagDependency;

class TranslationManager
{

    public static function save($model, callable $success = null)
    {
        $isNewRecord = $model->isNewRecord;
        $dbTrans = Yii::$app->db->beginTransaction();
        $post = Yii::$app->request->post();
        $error = [];
        // Create Model With Translation Key
        if ($model->isNewRecord) {
            foreach ($model->translate_col as $value) {
                if (self::isValue($post[$value])) {
                    $model->$value = $model->translate_prefix . $value;
                }
            }
            //Fallback on error
            if (!$model->save()) {
                $error = $model->getErrors();
                $dbTrans->rollback();
            }
        }
        if ($dbTrans->getIsActive()) {
            foreach ($model->translate_col as $value) {
                $isValue = self::isValue($post[$value]);
                if ($isValue || !empty($model->$value)) {
                    if ($isNewRecord && $isValue) {
                        // Add Id to translation key for new model
                        $model->$value .= "-" . $model->id;
                        $sourceMessage = null;
                    } else {
                        // Insert Missing Translation key for existing Model (For Optional Column)
                        if ($isValue && empty($model->$value)) {
                            $model->$value = $model->translate_prefix . $value . "-" . $model->id;
                        }
                        // Check for existing Translation Key Record and create new if not exists
                        $sourceMessage = SourceMessage::findOne(['category' => $model->translate_category, 'message' => $model->$value]);
                    }
                    if (!$sourceMessage) {
                        $sourceMessage = new SourceMessage();
                        $sourceMessage->category = $model->translate_category;
                        $sourceMessage->message = $model->$value;
                    }
                    if ($sourceMessage->save()) {
                        foreach ($post[$value] as $key => $msg) {
                            if ($isNewRecord) {
                                $message = null;
                            } else {
                                // Check For Existing Translated Message Record and Create new if not exists
                                $message = Message::findOne(['language' => $key, 'id' => $sourceMessage->id]);
                            }
                            // Create new Record if record not exists
                            if (!$message) {
                                $message = new Message();
                                $message->id = $sourceMessage->id;
                                $message->language = $key;
                            }
                            // Create new record if value is not empty
                            if (!$message->isNewRecord || !empty($msg)) {
                                // Prevent Existing Record show as key when english value is empty
                                if(!$message->isNewRecord && empty($msg) && $message->language == "en-US"){
                                  $msg = " ";
                                }
                                $message->translation = $msg;
                                if (!$message->save()) {
                                    $error = $message->getErrors();
                                    $dbTrans->rollBack();
                                    break;
                                }
                            }
                        }
                    } else {
                        $error = $sourceMessage->getErrors();
                        $dbTrans->rollback();
                    }
                }
            }
        }
        if ($dbTrans->getIsActive()) {
            if (!$model->save()) {
                $error = $message->getErrors();
                $dbTrans->rollback();
            }
        }
        // Run Success Function if Exists
        if ($success) {
            $success($model, $dbTrans);
        }
        // Commit DB Translation, or return to view if errors
        if ($dbTrans->getIsActive()) {

            TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.translation');
            $dbTrans->commit();
            return true;
        } else {
            return ['error' => $error, 'translation' => self::fallbackData($model, $post)];
        }
    }

    //Check If Value Exists In Translation
    protected static function isValue($fields)
    {
        foreach ($fields as $field) {
            if (!empty($field))
                return true;
        }
        return false;
    }

    //Return Translation Message to Views
    protected static function fallbackData($model, $post)
    {
        $data = [];
        foreach ($model->translate_col as $value) {
            $data[$value] = $post[$value];
        }
        return $data;
    }


    public static function onFind($model)
    {
        $translation = array();
        foreach ($model->translate_col as $value) {
            $sourceMessage = SourceMessage::findOne(['message' => $model->$value]);
            $languages = Language::getActiveLanguage();
            $messages = array();
            if ($sourceMessage) {
                $messages = Message::find()->where(['id' => $sourceMessage->id])->all();
            }
            $arr = array();
            foreach ($messages as $message) {
                $arr = array_merge($arr, array($message->language => $message->translation));
            }
            foreach ($languages as $language) {
                if (!isset($arr[$language->iso_code])) {
                    $arr[$language->iso_code] = '';
                }
            }
            $translation = array_merge($translation, [$value => $arr]);
        }
        return $translation;
    }

    public static function onDelete($id)
    {
        $sourceMessage = SourceMessage::deleteAll(['message' => $id]);
    }

}
