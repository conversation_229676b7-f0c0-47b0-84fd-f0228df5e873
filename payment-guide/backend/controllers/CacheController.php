<?php
namespace backend\controllers;

use Yii;
use yii\web\Controller;
use yii\filters\VerbFilter;
use yii\caching\TagDependency;
use common\components\MemCache;
use yii\data\ArrayDataProvider;


/**
 * CountryController implements the CRUD actions for Country model.
 */
class CacheController extends Controller
{

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists available cache list
     * @return mixed
     */
    public function actionIndex()
    {
        $cache_list = MemCache::getCacheTagConstants();

        $filteredresultData = array_filter($cache_list, function ($item) {
            $namefilter = Yii::$app->request->getQueryParam('filtername', '');
            if (strlen($namefilter) > 0) {
                if (strpos($item['name'], $namefilter) != false) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return true;
            }
        });

        $namefilter = Yii::$app->request->getQueryParam('filtername', '');

        $searchModel = ['id' => null, 'name' => $namefilter];

        $dataProvider = new ArrayDataProvider([
            'allModels' => $filteredresultData,
            'sort' => [
                'attributes' => ['name'],
            ],
        ]);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);


    }

    /**
     * Function to invalidate cache based on tag
     */
    public function actionClear($name)
    {
        TagDependency::invalidate(Yii::$app->cache, $name);
        return $this->redirect(['index']);
    }

    /**
     * Function to invalidate all cache based on tag
     */
    public function actionClearall()
    {
        $cache_list = MemCache::getCacheTagConstants();
        foreach ($cache_list as $k => $v) {
            TagDependency::invalidate(Yii::$app->cache, $v['key']);
        }
        return $this->redirect(['index']);
    }

}

?>
