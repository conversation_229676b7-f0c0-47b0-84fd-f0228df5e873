<?php

namespace backend\controllers;

use Yii;
use common\models\GiftCardMerchant;
use common\models\GiftCardMerchantSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\GiftCardCountryRelationship;
use common\models\Country;
use backend\components\TranslationManager;

/**
 * GiftCardMerchantController implements the CRUD actions for GiftCardMerchant model.
 */
class GiftCardMerchantController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all GiftCardMerchant models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new GiftCardMerchantSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        foreach ($dataProvider->models as $k => $v) {
            $v->displayValue();
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single GiftCardMerchant model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $model->displayValue();
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Get a list of Countries name
     * @param string id(GiftCatdMerchantId)
     */
    public static function getRelatedCountry($id)
    {
        $country_realtionship = GiftCardCountryRelationship::find()->where(['gift_card_merchant_id' => $id])->asArray()->all();
        $country_name = array();
        foreach ($country_realtionship as $k => $v) {
            $name = Country::findOne($v['country_code'])->name;
            array_push($country_name, $name);
        }
        return implode("; ", $country_name);
    }

    /**
     * Country Relationship function to be used in create and update function
     */
    public function countryRelationship($model, $dbTrans)
    {
        $ori = array_filter(explode(",", $_POST["GiftCardCountryRelationship"]["ori-val"]));
        $pass = (isset(Yii::$app->request->post()["GiftCardCountryRelationship"]["country_code"]) ? Yii::$app->request->post()["GiftCardCountryRelationship"]["country_code"] : []);
        $diff1 = array_diff($pass, $ori);
        $diff2 = array_diff($ori, $pass);
        foreach ($diff1 as $val) {
            $relationship = new GiftCardCountryRelationship();
            $relationship->gift_card_merchant_id = $model->id;
            $relationship->country_code = $val;
            if (!$relationship->save()) {
                $dbTrans->rollback();
                return;
            }
        }
        foreach ($diff2 as $val) {
            $relationship = GiftCardCountryRelationship::find()->where(["gift_card_merchant_id" => $model->id, "country_code" => $val])->one();
            if (!$relationship->delete()) {
                $dbTrans->rollback();
                return;
            }
        }
    }

    /**
     * Creates a new GiftCardMerchant model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new GiftCardMerchant();
        $translation = [];
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            $success = function ($model, $dbTrans) {
                //calling above function to save country relationship with gift card
                $this->countryRelationship($model, $dbTrans);
            };
            //Save translated message for multi language
            $status = TranslationManager::save($model, $success);
            if ($status === true) {
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        return $this->render('create', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);
    }

    /**
     * Updates an existing GiftCardMerchant model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            $success = function ($model, $dbTrans) {
                $this->countryRelationship($model, $dbTrans);
            };
            $status = TranslationManager::save($model, $success);
            if ($status === true) {
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        } else {
            $translation = TranslationManager::onFind($model);
        }
        return $this->render('update', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);
    }

    /**
     * Deletes an existing GiftCardMerchant model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the GiftCardMerchant model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return GiftCardMerchant the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = GiftCardMerchant::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
