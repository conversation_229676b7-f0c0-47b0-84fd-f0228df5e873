<?php

namespace backend\controllers;

use Yii;
use common\models\GiftCardSubMerchant;
use common\models\GiftCardSubMerchantSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\GiftCardMerchant;
use dpodium\filemanager\components\FilemanagerHelper;
use dpodium\filemanager\components\Filemanager;
use backend\components\TranslationManager;

/**
 * GiftCardSubMerchantController implements the CRUD actions for GiftCardSubMerchant model.
 */
class GiftCardSubMerchantController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all GiftCardSubMerchant models.
     * @return mixed
     */
    public function actionIndex($id)
    {
        $searchModel = new GiftCardSubMerchantSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        foreach ($dataProvider->models as $k => $v) {
            $v->displayValue();
        }

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single GiftCardSubMerchant model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $model->status = ($model->status === 1) ? 'Published' : 'Draft';
        $gift_merchant_id = $model->gift_card_merchant_id;

        $model->gift_card_merchant_id = GiftCardMerchant::findOne($model->gift_card_merchant_id)->name;
        //Get Image to display from s3 using filemanager
        $model->icon_url = FilemanagerHelper::getFile($model->icon_url, 'file_identifier');
        $model->icon_url = $model->icon_url['img_thumb_src'];
        $model->icon_url = Filemanager::getThumbnail('image', $model->icon_url);

        return $this->render('view', [
            'model' => $model,
            'gift_merchant_id' => $gift_merchant_id,
        ]);
    }

    /**
     * Creates a new GiftCardSubMerchant model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new GiftCardSubMerchant();
        $translation = [];
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Save content for multi-language
            $status = TranslationManager::save($model);
            if ($status === true) {
                return $this->redirect(['index', 'id' => $model->gift_card_merchant_id]);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }

        }
        return $this->render('create', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);

    }

    /**
     * Updates an existing GiftCardSubMerchant model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Update content for multi-language
            $status = TranslationManager::save($model);
            if ($status === true) {
                return $this->redirect(['index', 'id' => $model->gift_card_merchant_id]);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        else {
            //Find content of current model
            $translation = TranslationManager::onFind($model);
        }
        return $this->render('update', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);

    }

    /**
     * Deletes an existing GiftCardSubMerchant model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $gift_card_merchant_id = GiftCardSubMerchant::findOne($id)->gift_card_merchant_id;
        $this->findModel($id)->delete();

        return $this->redirect(['index', 'id' => $gift_card_merchant_id]);
    }

    /**
     * Finds the GiftCardSubMerchant model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return GiftCardSubMerchant the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = GiftCardSubMerchant::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
