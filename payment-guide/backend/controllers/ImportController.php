<?php
namespace backend\controllers;

use Yii;
use yii\web\Controller;
use yii\filters\VerbFilter;
use common\models\Country;
use common\models\Region;
use common\models\Currency;

class ImportController extends Controller {

  public function behaviors()
  {
      return [
          'verbs' => [
              'class' => VerbFilter::className(),
              'actions' => [
                  'delete' => ['POST'],
              ],
          ],
      ];
  }

  /**
  * Index Page
  */
  public function actionIndex(){
    return $this->render('index');
  }

  /**
  * Get Items List from Rest API
  */
  public function getItemLists(){
    $params = Yii::$app->params;
    $proxy_host = $params['PROXY_HOST'];
    $proxy_port = $params['PROXY_PORT'];
    if (isset($proxy_host)) {
       $proxy_enabled = true;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FAILONERROR,    true);
    curl_setopt($ch, CURLOPT_URL, 'https://restcountries.eu/rest/v2/all');
    
    if ($proxy_enabled && !empty($proxy_host)) {
        curl_setopt($ch, CURLOPT_PROXY, $proxy_host);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
    }
    
    
    $response = curl_exec($ch);
    if(curl_errno($ch)){
      echo curl_error($ch);
      exit;
    }
    curl_close($ch);
    $response = ($response ? json_decode($response) : $response);
    return $response;
  }

  /**
  * Function to Import Currency
  */
  public function actionCurrency($response = null){
    //Calling getItenList to get data from API
    if(!$response) $response = self::getItemLists();
    $dbTransaction = Yii::$app->db->beginTransaction();
    $count = 0;
    $errorlog = "";
    foreach ($response as $item){
        if(isset($item->currencies) && $item->currencies[0]->code !== "" && $item->currencies[0]->code !== null && $item->currencies[0]->code !== "(none)"){
          foreach($item->currencies as $importCur){
            $currency = Currency::findOne(['currency_code'=>$importCur->code]);
            if(!$currency){
              $currency = new Currency();
              $currency->name = $importCur->name;
              $currency->currency_code = strtolower($importCur->code);
              $currency->status = 0;
              $currency->save();
              if(!$currency->save()) $errorlog .= $item->name. "\n";
              $count++;
            }
          }
        }
    }
    $dbTransaction->commit();
    return json_encode(array("Added $count Currency to Database."));
  }

  /**
  * Function to Import Region
  */
  public function actionRegion($response = null){
    //Calling getItenList to get data from API
    if(!$response) $response = self::getItemLists();
    $dbTransaction = Yii::$app->db->beginTransaction();
    $count = 0;
    foreach ($response as $item){
      if(isset($item->region) && $item->region != ""){
        $region = Region::findOne(['name'=>$item->region]);
        if(!$region){
          $region = new Region();
          $region->name = $item->region;
          $region->status = 1;
          $region->save();
          $count++;
        }
      }
    }
    $dbTransaction->commit();
    return json_encode(array("Added $count Region to Database."));
  }

  /**
  * Function to Import Country
  */
  public function actionCountry($response = null){
    //Calling getItenList to get data from API
    if(!$response) $response = self::getItemLists();
    $dbTransaction = Yii::$app->db->beginTransaction();
    $countryCount = $regionCount = $currencyCount = 0;
    $errorlog = "";
    foreach ($response as $item){
      $country = Country::findOne(['name'=>$item->name]);
      if(!$country){
        $country = new Country();
        $country->name = $item->name;
        $country->country_code = strtolower($item->alpha2Code);
        if(isset($item->region) && $item->region != ""){
          $region = Region::findOne(['name'=>$item->region]);
          if(!$region){
            $region = new Region();
            $region->name = $item->region;
            $region->status = 1;
            $region->save();
            $regionCount++;
          }
          $country->default_region = $region->id;
        }
        if(isset($item->currencies) && $item->currencies[0]->code !== "" && $item->currencies[0]->code !== null && $item->currencies[0]->code !== "(none)"){
          $currency = Currency::findOne(['currency_code'=>$item->currencies[0]->code]);
          if(!$currency){
            $currency = new Currency();
            $currency->name = $item->currencies[0]->name;
            $currency->currency_code = strtolower($item->currencies[0]->code);
            $currency->status = 0;
            $currency->save();
            $currencyCount++;
          }
          $country->default_currency = $currency->currency_code;
        }
        else{
          $currency = Currency::findOne(['currency_code'=> 'USD']);
          $country->default_currency = $currency->currency_code;
        }
        $country->status = 1;
        $country->save();
        if(!$country->save()) $errorlog .= $item->alpha2Code. "\n";
        $countryCount++;
      }
    }
    $dbTransaction->commit();
    return json_encode(array("Added $currencyCount Currency to Database.","Added $regionCount Region to Database.","Added $countryCount Country to Database."));
  }

  /**
  * Function to Delete All Country
  */
  public function actionDeleteAllCountry(){
    Country::deleteAll();
    return json_encode(array("All Country Removed From DB"));
  }

  /**
  * Function to Delete All Region
  */
  public function actionDeleteAllRegion(){
    Region::deleteAll();
    return json_encode(array("All Region Removed From DB"));
  }

  /**
  * Function to Delete All Region
  */
  public function actionDeleteAllCurrency(){
    Currency::deleteAll();
    return json_encode(array("All Currency Removed From DB"));
  }

  /**
  * Function to Delete All
  */
  public function actionDeleteAll(){
    Country::deleteAll();
    Currency::deleteAll();
    Region::deleteAll();
    return json_encode(array("All Country Removed From DB","All Country Removed From DB","All Currency Removed From DB"));
  }
}
