<?php

namespace backend\controllers;

use Yii;
use common\models\PaymentCategory;
use common\models\PaymentCategorySearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\caching\TagDependency;
use backend\components\TranslationManager;

/**
 * PaymentCategoryController implements the CRUD actions for PaymentCategory model.
 */
class PaymentCategoryController extends Controller
{
    /**
     * @inheritdoc
     */

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PaymentCategory models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new PaymentCategorySearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        //Exclude Payment Category where status is 5. As this is being used by Gift Card.
        $dataProvider->query->where('status <> \'5\'');
        foreach ($dataProvider->models as $k => $v) {
            //To get content for multi language
            $v->displayValue();
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PaymentCategory model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $model->displayValue();
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new PaymentCategory model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new PaymentCategory();
        $translation = [];
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Save multi language content
            $status = TranslationManager::save($model);
            if ($status === true) {
                //Invalidate menu cache if new payment category is added
                TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.menu');
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        return $this->render('create', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);
    }

    /**
     * Updates an existing PaymentCategory model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Update multi language content
            $status = TranslationManager::save($model);
            if ($status === true) {
                //Invalidate menu cache if any payment category is updated
                TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.menu');
                if ($model->status == '5') {
                    return $this->redirect(['gift-card-merchant/index']);
                } else {
                    return $this->redirect(['index']);
                }
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        else{
            //Get model with translation content
            $translation = TranslationManager::onFind($model);
        }
        return $this->render('update', [
            'translation' => $translation,
            'error' => $error,
            'model' => $model
        ]);
    }

    /**
     * Deletes an existing PaymentCategory model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $deleteMessageId = array();
        foreach ($model->translate_col as $value) {
            array_push($deleteMessageId, $model->$value);
        }
        //Delete multi language content and invalidate menu cache
        TranslationManager::onDelete($deleteMessageId);
        TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.menu');
        $model->delete();
        return $this->redirect(['index']);
    }

    /**
     * Finds the PaymentCategory model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return PaymentCategory the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = PaymentCategory::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
