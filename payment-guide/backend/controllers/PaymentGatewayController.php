<?php

namespace backend\controllers;

use Yii;
use common\models\PaymentGateway;
use common\models\PaymentGatewaySearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use backend\components\TranslationManager;

/**
 * PaymentGatewayController implements the CRUD actions for PaymentGateway model.
 */
class PaymentGatewayController extends Controller
{
    /**
     * @inheritdoc
     */

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PaymentGateway models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new PaymentGatewaySearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        foreach ($dataProvider->models as $key => $value) {
            $value->displayValue();
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PaymentGateway model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new PaymentGateway model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new PaymentGateway();
        $translation = [];
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Save multi language content
            $status = TranslationManager::save($model);
            if ($status === true) {
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        return $this->render('create', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation,
        ]);
    }

    /**
     * Updates an existing PaymentGateway model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            //Save multi language content
            $status = TranslationManager::save($model);
            if ($status === true) {
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        } else {
            //Get multi language content for that particular model
            $translation = TranslationManager::onFind($model);
        }
        return $this->render('update', [
            'translation' => $translation,
            'error' => $error,
            'model' => $model
        ]);
    }

    /**
     * Deletes an existing PaymentGateway model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $deleteMessageId = array();
        foreach ($model->translate_col as $value) {
            array_push($deleteMessageId, $model->$value);
        }
        //Delete content from multi language table
        TranslationManager::onDelete($deleteMessageId);
        $model->delete();
        return $this->redirect(['index']);
    }

    /**
     * Finds the PaymentGateway model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return PaymentGateway the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = PaymentGateway::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
