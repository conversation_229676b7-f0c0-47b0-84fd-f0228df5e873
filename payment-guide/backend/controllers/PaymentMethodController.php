<?php

namespace backend\controllers;

use Yii;
use common\models\PaymentMethod;
use common\models\PaymentMethodSearch;
use common\models\PipwaveRequest;
use common\models\PaymentMethodLocalization;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\caching\TagDependency;
use backend\components\TranslationManager;

/**
 * PaymentMethodController implements the CRUD actions for PaymentMethod model.
 */
class PaymentMethodController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PaymentMethod models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new PaymentMethodSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        foreach ($dataProvider->models as $key => $value) {
            $value->displayValue();
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PaymentMethod model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id)->displayValue(),
        ]);
    }

    /**
     * Creates a new PaymentMethod model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new PaymentMethod();
        $translation = [];
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            $status = TranslationManager::save($model);
            if ($status === true) {
                //Call function to update payment method localization
                self::updatePaymentMethodLocalization($model, true);
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        }
        return $this->render('create', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);

    }

    /**
     * Updates an existing PaymentMethod model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $error = [];
        if ($model->load(Yii::$app->request->post())) {
            $status = TranslationManager::save($model);
            if ($status === true) {
                //Call function to update payment method localization
                self::updatePaymentMethodLocalization($model, false);
                return $this->redirect(['index']);
            } else {
                $error = $status['error'];
                $translation = $status['translation'];
            }
        } else {
            //Get multi language content for a particular model
            $translation = TranslationManager::onFind($model);
        }
        return $this->render('update', [
            'model' => $model,
            'error' => $error,
            'translation' => $translation
        ]);

    }

    /**
     *
     * Function to update payment method localization
     * @param Payment Method
     */
    private function updatePaymentMethodLocalization($payment_method, $isNew)
    {
        if ($isNew) {
            //Update pipwave request with new expiry time
            PipwaveRequest::updateAll(['expiry' => time()]);
        } else {
            //Delete a particular payment method from payment method localization
            PaymentMethodLocalization::deleteAll(['payment_method_id' => $payment_method->id]);
            //Search in pipwave request DB for pipwave_id
            $localizeRecord = PipwaveRequest::find()->where(['like', 'results', '"'.$payment_method->pipwave_payment_id.'"'])->andWhere([">", "expiry", time()])->all();
            foreach ($localizeRecord as $data) {
                //Create new pyament method relationship with  pipwave id
                $model = new PaymentMethodLocalization();
                $model->country_code = $data->country_code;
                $model->currency_code = $data->currency_code;
                $model->payment_method_id = $payment_method->id;
                $model->save();
            }
        }
        //Invalidate pipwaveAPI cache
        TagDependency::invalidate(Yii::$app->cache, "com.payment-guide.pipwaveAPI");
    }

    /**
     * Deletes an existing PaymentMethod model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the PaymentMethod model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return PaymentMethod the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = PaymentMethod::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
