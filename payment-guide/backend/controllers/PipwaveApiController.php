<?php

namespace backend\controllers;

use Yii;
use common\models\PipwaveRequest;
use common\models\PipwaveRequestSearch;
use common\models\PipwaveRequestLog;
use common\models\PipwaveRequestLogSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\caching\TagDependency;

/**
 * PipwaveApiController implements the CRUD actions for PipwaveRequest model.
 */
class PipwaveApiController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PipwaveRequest models.
     * @return mixed
     */
    public function actionIndex()
    {
        //pipwave request search
        $searchModel = new PipwaveRequestSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->setSort([
            'defaultOrder' => [
                'expiry' => SORT_DESC
            ]
        ]);
        //pipwave request error log Search
        $logSearchModel = new PipwaveRequestLogSearch();
        $logDataProvider = $logSearchModel->search(Yii::$app->request->queryParams);
        $logDataProvider->setSort([
            'defaultOrder' => [
                'created_at' => SORT_DESC
            ]
        ]);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'logSearchModel' => $logSearchModel,
            'logDataProvider' => $logDataProvider,
        ]);
    }

    /**
    * Get a list of pipwave request error log
    */
     public function actionView($currency_code, $country_code)
     {
         $model =  $this->findModel($currency_code, $country_code);
         return $this->render('view',[
           'model' => $model
         ]);
     }

     /**
     * Function to expire a particular pipwave request
     * @param country code, currency code
     */
     public function actionViewLog($id)
     {
         $model =  PipwaveRequestLog::findOne($id);
         return $this->render('view-log',[
           'model' => $model
         ]);
     }

    /**
     * Function to expire a particular pipwave request
     * @return mixed
     */
    public function actionSetPartialExpired($currency_code, $country_code)
    {
        if(!empty($currency_code) || !empty($country_code)){
          $filter = [];
          if(!empty($country_code)){
            $filter['country_code'] = $country_code;
          }
          if(!empty($currency_code)){
            $filter['currency_code'] = $currency_code;
          }
          PipwaveRequest::updateAll(['expiry'=>time()],$filter);
          TagDependency::invalidate(Yii::$app->cache, "com.payment-guide.pipwaveAPI");
        }
        return $this->redirect(['index']);
    }


    /**
     * Function to expire a particular pipwave request
     * @return mixed
     */
    public function actionSetExpired($currency_code, $country_code)
    {
        $model = $this->findModel($currency_code, $country_code);
        $model->expiry = time();
        $model->save();
        //Invalidate pipwave API cache call
        TagDependency::invalidate(Yii::$app->cache, "com.payment-guide.pipwaveAPI");
        return $this->redirect(['index']);
    }

    /**
     * Function to expire all pipwave request
     */
    // public function actionSetAllExpired()
    // {
    //     PipwaveRequest::updateAll(['expiry'=>time()]);
    //     TagDependency::invalidate(Yii::$app->cache, "com.payment-guide.pipwaveAPI");
    //     return $this->redirect(['index']);
    // }

    /**
     * Function to delete a particular error log
     */

    public function actionDelete($currency_code, $country_code)
    {
        $this->findModel($currency_code, $country_code)->delete();
        return $this->redirect(['index']);
    }

    public function actionDeleteLog($id)
    {
        PipwaveRequestLog::findOne($id)->delete();
        return $this->redirect(['index']);
    }

    /**
     * Function to delete all error log
     */
    public function actionDeleteAllLog()
    {
        PipwaveRequestLog::deleteAll();
        return $this->redirect(['index']);
    }

    /**
     * Finds the PipwaveRequest model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $currency_code
     * @param string $country_code
     * @return PipwaveRequest the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($currency_code, $country_code)
    {
        if (($model = PipwaveRequest::findOne(['currency_code' => $currency_code, 'country_code' => $country_code])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
