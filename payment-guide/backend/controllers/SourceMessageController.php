<?php

namespace backend\controllers;

use Yii;
use common\models\SourceMessage;
use common\models\Message;
use common\models\SourceMessageSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\Language;
use yii\helpers\ArrayHelper;
use yii\caching\TagDependency;

/**
 * SourceMessageController implements the CRUD actions for SourceMessage model.
 */
class SourceMessageController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all SourceMessage models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SourceMessageSearch();
        //Translation category name
        $searchModel->category = 'app';
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SourceMessage model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SourceMessage model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SourceMessage();
        if ($model->load(Yii::$app->request->post())) {
            $dbTransaction = Yii::$app->db->beginTransaction();
            $model->category = 'app';
            if ($model->save()) {
                if (isset($_POST['Message'])) {
                    foreach ($_POST['Message'] as $item) {
                        if ($item["id"] == "") {
                            $message = new Message();
                            $message->id = $model->id;
                            $message->language = $item["language"];
                        }
                        $message->translation = $item["translation"];
                        $message->save();
                    }
                }
                $dbTransaction->commit();
            }
            return $this->redirect('index');
        } else {
            return $this->render('create', [
                'model' => $model,
                'translation' => array()
            ]);
        }
    }

    /**
     *
     * To Update Translation Message
     */
    public function actionUpdate($id = null)
    {
        // $model = $this->findModel($id);
        if (empty($id)) {
            $model = new SourceMessage();
            $message = [];
        } else {
            $model = $this->findModel($id);
            $message = $this->getMessageList($id);
        }
        $data = Yii::$app->request->post();
        if ($data) {
            if ($model->isNewRecord) {
                $model->category = 'app';
            }
            $model->load(Yii::$app->request->post());
            $model->save();
            $this->saveModel($data, $model->id);
            return $this->redirect('index');
        } else {
            return $this->render('update', [
                'model' => $model,
                'message' => $message
            ]);
        }
    }

    /**
     *
     * To Save Multi Language content
     */
    protected function saveModel($data, $id)
    {
        //Get a list of active Languages
        $languageList = Language::getActiveLanguage();
        foreach ($languageList as $language) {
            $iso_code = $language->iso_code;
            if ($data['id'][$iso_code] !== "") {
                $messageModel = Message::find()->where(['id' => $id, 'language' => $iso_code])->one();
            } else {
                $messageModel = new Message();
                $messageModel->id = $id;
                $messageModel->language = $iso_code;
            }
            if ($data['translation'][$iso_code] !== "") {
                $messageModel->translation = $data['translation'][$iso_code];
                $messageModel->save();
            }
        }
        //Invalidate translation cache is translation messages are updated
        TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.translation');
    }

    /**
     * Get a particular message
     */
    protected function getMessageList($id)
    {
        $model = Message::find()->where(['id' => $id])->asArray()->all();
        return ArrayHelper::index($model, 'language');
    }

    /**
     * Deletes an existing SourceMessage model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();
        return $this->redirect(['index']);
    }

    // public function actionAjaxDeleteTranslate(){
    //     if(Yii::$app->request->isAjax){
    //       Message::findOne(['id'=>$_POST['id'], 'language'=> $_POST['language']])->delete();
    //       return "success";
    //     }
    // }
    /**
     * Finds the SourceMessage model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SourceMessage the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SourceMessage::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
