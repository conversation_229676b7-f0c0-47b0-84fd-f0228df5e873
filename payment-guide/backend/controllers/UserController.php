<?php

namespace backend\controllers;

use Yii;
use common\models\User;
use common\models\UserSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CountryController implements the CRUD actions for Country model.
 */
class UserController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Country models.
     * @return mixed
     */
    public function actionIndex()
    {
        if (self::checkAuthorization()) {
            $searchModel = new UserSearch();
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
            foreach ($dataProvider->models as $k => $v) {
                $v->status = ($v->status === 10) ? 'Active' : 'Not Active';
                $v->updated_at = \Yii::$app->formatter->asDatetime($v->updated_at, "php:Y-m-d H:i:s");
            }
            return $this->render('index', [
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
            ]);
        }
        return $this->render('restricted');
    }

    /**
     * Displays a single Country model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        if (self::checkAuthorization()) {
            $model = $this->findModel($id);
            $model->status = ($model->status === 10) ? 'Active' : 'Not Active';
            $model->updated_at = \Yii::$app->formatter->asDatetime($model->updated_at, "php:Y-m-d H:i:s");
            $model->created_at = \Yii::$app->formatter->asDatetime($model->created_at, "php:Y-m-d H:i:s");
            return $this->render('view', [
                'model' => $model,
            ]);
        }
        return $this->render('restricted');
    }


    /**
     * Creates a new USer model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new User();
        $post = Yii::$app->request->post();
        if (self::checkAuthorization()) {
            if ($model->load($post)) {
                $model->password_hash = $model->setPassword($model->password_hash);
                $model->auth_key = $model->generateAuthKey();
                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
            return $this->render('create', [
                'model' => $model,
            ]);
        }
        return $this->redirect('index');
    }

    /**
     * Updates an existing Country model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $post = Yii::$app->request->post();

        //Check if there is data from post and user is logged in as superuser
        if ($post && (self::checkAuthorization() || Yii::$app->user->identity->id == $id)) {
            if ($model->load($post)) {
                $model->password_hash = $model->setPassword($model->password_hash);
                $model->auth_key = $model->generateAuthKey();
                if ($model->save() && self::checkAuthorization()) {
                    return $this->redirect('index');
                } else {
                    return $this->redirect('/site/index');
                }
            }
        } elseif (!self::checkAuthorization() && Yii::$app->user->identity->id != $id) {
            return $this->render('restricted');
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Country model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        if (self::checkAuthorization()) {
            $this->findModel($id)->delete();
        }
        return $this->redirect(['index']);
    }

    /**
     * Function to check if user is logged in as superuser
     * @return boolean
     */
    private function checkAuthorization()
    {
        return \backend\components\SuperAdminAccessControl::checkAccess();
    }

    /**
     * Finds the Country model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return User the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
