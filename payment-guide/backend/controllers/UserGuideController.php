<?php

namespace backend\controllers;

use Yii;
use common\models\UserGuide;
use common\models\UserGuideSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\Language;
use yii\helpers\ArrayHelper;
use dpodium\filemanager\components\FilemanagerHelper;

/**
 * UserGuideController implements the CRUD actions for UserGuide model.
 */
class UserGuideController extends Controller
{
    /**
     * @inheritdoc
     */
    private $multi_column = ['name','content','language_code','status','meta_description'];
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all UserGuide models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new UserGuideSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        foreach($dataProvider->models as $v){
          $v->displayValue();
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single UserGuide model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $model->displayValue();

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Function to update User Guide
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id = null)
    {
        $model = $this->getUserGuide($id);
             
        $data = Yii::$app->request->post();
        if ($data) {
            $this->saveModel($data,$id);
           return $this->redirect('index');
        } else {
            return $this->render('update', [
                'model' => $model
            ]);
        }
    }

    public function actionImageSource(){
      $thumb_url = Yii::$app->request->post('thumb_url');
      $model = \dpodium\filemanager\models\Files::findOne(['thumbnail_name'=>$thumb_url]);
      if($model){
         $file = FilemanagerHelper::getFile($model->file_identifier, 'file_identifier')['img_src'];
         if (!preg_match("~^(?:f|ht)tps?://~i", $file)) {
                $file = "https://" . $file;
         }
         $result = array(
             'file' => $file,
             'alt' => $model->src_file_name,
         ); 
         
         return json_encode($result);
      }
      else {
        return '';
      }
    }

    /**
     * Deletes an existing UserGuide model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();
        return $this->redirect(['index']);
    }

    /**
     * Finds the UserGuide model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return UserGuide the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
     protected function findModel($id)
     {
       return UserGuide::findOne($id);
     }

     /**
      * To save content for multi language
      */
     protected function saveModel($data,$id){
       $languageList = Language::getActiveLanguage();
       foreach($languageList as $language){
         $iso_code = $language->iso_code;
         if($data['id'][$iso_code] !== ""){
           $userGuide = $this->findModel($data['id'][$iso_code]);
         }
         else{
           $userGuide = new UserGuide();
         }

        if($data['name'][$iso_code] !== "" && $data['content'][$iso_code] !== ""){

           $userGuide->payment_method_id = $id;
           foreach($this->multi_column as $field){
             $userGuide->$field = $data[$field][$language->iso_code];
           }
           $userGuide->save();
         }
       }
     }

     /**
       * Get User Guide for a particular Payment Method
     */
    protected function getUserGuide($id)
    {
      $model = UserGuide::find()->where(['payment_method_id' => $id])->asArray()->all();
      return ArrayHelper::index($model,'language_code');
    }
}
