<?php
namespace backend\models;

use Yii;
use yii\base\Model;
use common\models\User;
use yii\helpers\Html;

/**
 * Password reset request form
 */
class PasswordResetRequestForm extends Model
{
    public $email;


    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            ['email', 'trim'],
            ['email', 'required'],
            ['email', 'email'],
            ['email', 'exist',
                'targetClass' => '\common\models\User',
                'filter' => ['status' => User::STATUS_ACTIVE],
                'message' => 'There is no user with this email address.'
            ],
        ];
    }

    /**
     * Sends an email with a link, for resetting the password.
     *
     * @return bool whether the email was send
     */
    public function sendEmail()
    {
        /* @var $user User */
        $user = User::findOne([
            'status' => User::STATUS_ACTIVE,
            'email' => $this->email,
        ]);

        if (!$user) {
            return false;
        }

        if (!User::isPasswordResetTokenValid($user->password_reset_token)) {
            $user->generatePasswordResetToken();
            if (!$user->save()) {
                return false;
            }
        }

        $resetLink = Yii::$app->urlManager->createAbsoluteUrl(['site/reset-password', 'token' => $user->password_reset_token]);

        $content = "<div class='password-reset'><p>Hello ".Html::encode($user->username) .",</p>
        <p>Follow the link below to reset your password:</p>
        <p>".Html::a(Html::encode($resetLink), $resetLink)."</p>
        </div>";

        $mailer = Yii::$app->aws->getSes();
        $email_content = [
          'Destination' => [
              'ToAddresses' => [
                  $this->email,
              ],
          ],
          'Message' => [
              'Body' => [
                  'Html' => [
                      'Charset' => "utf-8",
                      'Data' => $content,
                  ],
              ],
              'Subject' => [
                  'Charset' => "utf-8",
                  'Data' => "Payment Guide Password Reset",
              ],
          ],
          'Source' => Yii::$app->params["emailSender"],
        ];
        return $mailer->sendEmail($email_content);
    }
}
