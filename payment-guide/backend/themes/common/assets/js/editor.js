$(document).ready(function() {

    tinymce.init({
  selector: ".editor",
	// theme: "modern",
	// paste_data_images: true,
	plugins: [
	  "advlist autolink lists link image charmap print preview hr anchor pagebreak",
	  "searchreplace wordcount visualblocks visualchars code fullscreen",
	  "insertdatetime media nonbreaking save table contextmenu directionality",
	  "emoticons template paste textcolor colorpicker textpattern"
	],
	toolbar1: "mybutton2 mybutton mybutton3 insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image",
	toolbar2: "print preview media | forecolor backcolor emoticons",
	image_advtab: true,
        content_css: [
            // '../css/site.css'
        ],
        content_style: ".step{ position: relative; padding: 10px; width: 26px; margin-right: 10px; top: -10px; border-radius: 25px; float: left; font-family: Helvetica, Arial; color: #ffffff; text-align: center; font-size: 20px; font-weight: 600; background-color: #428bca;}",
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
            editor.addButton('mybutton', {
              text: 'Insert Image',
              icon: false,
              onclick: function () {
                var image = $("#w1 .fm-section-item img");
                if(image){

                  /* Ajax Call to get image src */
                   var source = image.attr('src').split("/");
                   var source = source[source.length-1];
                   var ajax = postAjax(source,editor);
                  /* Hard Coded Thumbsize */

                 // var source = image.attr('src').replace('/thumb_','/').replace('_120X120','');
                 // editor.insertContent('<img src="'+source+'" />');
                }
            }
          });
          editor.addButton('mybutton2', {
            text: 'Browse Image',
            icon: false,
            onclick: function () {
              $('label[data-target="#fm-modal"]').click();
              }
            });
            editor.addButton('mybutton3', {
                text: 'Add Background',
                icon: false,
                onclick: function () {
                    var ed = tinyMCE.activeEditor;
                    var content = ed.selection.getContent({'format':'html'});
                    var new_selection_content = '<span class="step">' + content + '</span>';
                    ed.execCommand('mceInsertContent', false, new_selection_content);

                    //ed.execCommand('insertHTML', false, new_selection_content);

                 }
            });
    }
    });
});

function postAjax(thumb_url,editor){
     $.ajax({
              type: "POST",
              url: "/user-guide/image-source",
              data: {
                  thumb_url: thumb_url
              },
              success: function (data){
                  value = JSON.parse(data);

                   if(value !== "")
                    tinymce.get(editor.id).execCommand('mceInsertContent', false, '<img src=\"'+value.file+'\" alt=\"'+value.alt+'\">');
              }
     });

//    $.ajax({
//      url: '/user-guide/image-source',
//      type : 'post',
//      data : {'thumb_url':thumb_url},
//      success : function(data){
//          console.log(data);
//        if(data !== "")
//        tinymce.get(editor.id).execCommand('mceInsertContent', false, '<img src=\"'+data+'\">');
//      }
//    });
}
