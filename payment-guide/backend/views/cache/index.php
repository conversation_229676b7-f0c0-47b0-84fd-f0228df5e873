<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;

/* @var $this yii\web\View */
/* @var $searchModel common\models\LanguageSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Cache List';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="cache-index">

<div>
  <p>
    <?php
        echo Html::a('Clear All Cache', ['/cache/clearall'], ['class'=>'btn btn-danger']);
    ?>
  </p>


      <?php echo DataTables::widget([
            'dataProvider' => $dataProvider,
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'name',
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{delete}',
                    'buttons' => [
                        'delete' => function ($url, $model) {
                            return Html::a('<span class="glyphicon glyphicon-trash"></span>', $url, [
                                        'title' => 'lead-delete',
                            ]);
                        }

                    ],
                    'urlCreator' => function ($action, $model, $key, $index) {
                        if ($action === 'delete') {
                            $url ='/cache/clear?name='.$model['key'];
                            return $url;
                        }
                    }

                ],


            ],
        ]); ?>
</div>
