<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use backend\components\SharedComponent;
use yii\helpers\ArrayHelper;
use common\models\Region;
use common\models\Currency;
/* @var $this yii\web\View */
/* @var $model common\models\Country */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="country-form">

    <?php $form = ActiveForm::begin(); ?>

    <?php
      echo ($model->isNewRecord ? $form->field($model, 'country_code')->textInput(['maxlength' => true]) : '');
    ?>

    <?php echo $form->field($model, 'name')->textInput(['maxlength' => true]); ?>

    <?php echo $form->field($model, 'default_region')->dropDownList(ArrayHelper::map(Region::find()->all(),'id','name'),['prompt'=>'']); ?>

    <?php echo $form->field($model, 'default_currency')->dropDownList(ArrayHelper::map(Currency::find()->all(),'currency_code','name'),['prompt'=>'']); ?>

    <?php
    if(strcasecmp($model->country_code,'us') !== 0)
      echo SharedComponent::createStatus($form,$model);
    ?>

    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
