<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use dpodium\filemanager\widgets\FileBrowse;
use yii\helpers\ArrayHelper;
use common\models\Country;
use yii\web\view;
use backend\components\SharedComponent;
use common\models\Language;
?>

<div id="button-template" style="display:none">
  <div class="form-group" index="{index}">
      <div class="row">
        <div class="col-md-2"><?php echo Html::input('text', 'additional_button_label[]',"{label}", ['class' => "form-control",'placeholder' => "Label"]); ?></div>
        <div class="col-md-4"><?php echo Html::input('text', 'additional_button_url[]',"{url}", ['class' => "form-control",'placeholder' => "Url"]); ?></div>
        <button class="btn glyphicon glyphicon-trash" onclick="deleteButtonLine(this);return false;"></button>
    </div>
  </div>
</div>

<div class="gift-card-merchant-form">

    <?php $form = ActiveForm::begin(); ?>

    <?php
      if(count($error)){
        echo SharedComponent::generateErrorMessage($error);
      }
      echo $form->field($model, 'is_sub_merchant')->dropDownList(['1' => 'Yes', '0' => 'No'],['prompt'=>'']);

      echo SharedComponent::createNavBar();

      $content =
        "<div class='form-group'>".
          Html::Label('Name','name').
          Html::input('text', 'name[%iso_code%]', '%value%-name' , ['class' => "form-control", '%required%' => true]).
        "</div>".
        "<div class='form-group'>".
          Html::Label('Description','description').
          Html::input('text', 'description[%iso_code%]', '%value%-description' , ['class' => "form-control"]).
        "</div>".
        "<div class='form-group sub_merchant_description' style='display:none;'>".
          Html::Label('Sub Merchant Description','sub_merchant_description').
          Html::input('text', 'sub_merchant_description[%iso_code%]', '%value%-sub_merchant_description' , ['class' => "form-control"]).
        "</div>".
          Html::input('text', 'additional_button[%iso_code%]', '%value%-additional_button' , ['class' => "form-control", 'style' => 'display:none;']).
        "<div class='form-group'>".
          Html::button('Add Button', ['class' => 'btn btn-success','onclick' => 'generateButtonField();']).
        "</div><div class='additional_button'>".
        Html::Label('Button','description').
        "</div><hr style='border-color:black;'>"
        ;

      echo SharedComponent::createContentTab($content,$translation,$model->isNewRecord);

      echo SharedComponent::createStatus($form,$model);
    ?>
    <?php
    echo $form->field($model, 'icon_url')->widget(FileBrowse::className(), [
            'multiple' => false,
            'folderId' => 3
    ]);
    ?>

    <div class="form-group">
        <label>Gift Card Country List</label>
        <?php echo Html::dropDownList('GiftCardCountryRelationship[country_code]',ArrayHelper::getColumn($model->countryRelationship, 'country_code'),
            ArrayHelper::map(Country::find()->all(),'country_code','name'),["multiple"=>true,"class"=>"form-control select-duallist","id"=>"countryrelationship-country_code"]);
        ?>
    </div>

    <?php echo Html::input('text', 'GiftCardCountryRelationship[ori-val]', implode(",",ArrayHelper::getColumn($model->countryRelationship, 'country_code')), ['class' => "form-control","style"=>"display:none;"]); ?>

     <?php echo $form->field($model, 'status')->dropDownList(['1' => 'Publish', '0' => 'Draft']); ?>

    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
    </div>

    <?php ActiveForm::end(); ?>
    <?php echo FileBrowse::renderModal(); ?>

</div>


<?php
\backend\assets\BootstrapDualListBoxAsset::register($this);
$this->registerJS(<<<EOT_JS
  $(document).ready(function(){
    $(".select-duallist").bootstrapDualListbox();
  });

  function generateButtonField(){
    var index = $(".additional_button:eq(0) .form-group").length;
    $(".additional_button").append(
      $("#button-template").html().replace("\{label\}","").replace("\{url\}","").replace("\{index\}",index)
    );
  }

  function deleteButtonLine(btn){
    var index = $(btn).closest($('.form-group')).attr("index");
    $(".additional_button").each(function(i,e){
      $(e).find($(".form-group"))[index].remove();
    });
    resetIndex("additional_button");
  }

  function resetIndex(div){
    $("."+div+"").each(function(i,e){
      $(e).find($(".form-group")).each(function(f,g){
        $(g).attr("index",f);
      });
    });
  }

  $("#giftcardmerchant-is_sub_merchant").change(function(){
    switch($("#giftcardmerchant-is_sub_merchant").val()){
      case "0":
        $(".sub_merchant_description").hide();
        $("#en-US .sub_merchant_description input").attr('required',false);
        break;
      case "1":
        $(".sub_merchant_description").show();
        $("#en-US .sub_merchant_description input").attr('required',true);
        break;
    }
  });

  $('form').on('beforeSubmit', function(event) {
      var validate = true;
      $("#translation > div").each(function(i,e){
        var btn_field = $(e).find($(".additional_button > .form-group"));
        var btn_arr = {};
        var id = $(e).attr('id');
        $(btn_field).each(function(f,g){
          var label = $(g).find($("input[name='additional_button_label[]")).val();
          var value = $(g).find($("input[name='additional_button_url[]")).val();
          var name = $(e).find($("input[name='name["+id+"]']")).val();
          if(label !== "" && value !== "" && name !== ""){
            btn_arr[label] = value;
          }
          else if((label == "" || value == "") && name !== "" && validate){
            alert('Missing Label / Url on Extra Button Section');
            validate = false;
          }
        });
        if(Object.keys(btn_arr).length > 0)
          $(e).find($("input[name='additional_button["+id+"]']")).val(JSON.stringify(btn_arr));
      });
      if(validate == false){
        return false;
      }
  });

EOT_JS
,View::POS_END, 'my-options');

if(!$model->isNewRecord){
  $this->registerJS(<<<EOT_JS

  $("#translation > div").each(function(i,e){
    var id = $(e).attr('id');
    var value = $("input[name='additional_button["+id+"]']").val();
    if(value == "") value = $("input[name='additional_button[en-US]']").val();
    if(value !== ""){
      var data = JSON.parse(value);
      $.each(data,function(label,url){
        var template = $("#button-template").html()
                      .replace("{label}",label)
                      .replace("{url}",url);
        $(e).find($(".additional_button")).append(template);
      });
    }
  });

EOT_JS
);
}
?>
