<?php

use yii\helpers\Html;
use common\models\PaymentCategory;
use fedemotta\datatables\DataTables;
/* @var $this yii\web\View */
/* @var $searchModel common\models\GiftCardMerchantSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Gift Card Merchants';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="gift-card-merchant-index">

    <p>
        <?php echo Yii::$app->params['giftCardPage']; ?>
    </p>

    <p>
        <?php echo Html::a('Create Gift Card Merchant', ['create'], ['class' => 'btn btn-success']); ?>
        <?php echo Html::a('Edit Description', ['/payment-category/update?id='.PaymentCategory::findOne(['status'=>5])->id], ['class' => 'btn btn-default']); ?>
    </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'name',
            [
              'attribute' => 'icon_url',
              'format' => 'html',
              'label' => 'Icon',
              'value' => function ($data) {
                  return Html::img('' . $data['icon_url'],
                      ['width' => '150px']);
              },
            ],
            'description:ntext',
            'status',
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {update} {delete} {myButton}',  // the default buttons + your custom button
                'buttons' => [
                    'myButton' => function($url, $model, $key) {     // render your custom button
                        return ($model->is_sub_merchant ? Html::a('<span class="glyphicon glyphicon-list-alt"></span>', ['gift-card-sub-merchant/index', 'id' => $model->id]) : '');
                    }
                ]
            ],

        ],
    ]); ?>
</div>
