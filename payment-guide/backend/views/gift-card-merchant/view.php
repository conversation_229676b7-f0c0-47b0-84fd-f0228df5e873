<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\GiftCardMerchant */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Gift Card Merchants', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="gift-card-merchant-view">

    <p>
        <?php echo Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']); ?>
        <?php echo Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            [
                'label' => 'Country',
                'value' => \backend\controllers\GiftCardMerchantController::getRelatedCountry($model->id)
            ],
            'name',
            'description:ntext',
            [
                'label' => 'Icon',
                'format' => 'raw',
                'value' => "<img src='" . $model->icon_url . "'"
            ],
            [
                'label' => 'Sub Merchant',
                'format' => 'raw',
                'value' => ($model->is_sub_merchant == 'Yes') ? Html::a('Sub Merchant', ['/gift-card-sub-merchant/index', 'id' => $model->id], ['class' => 'btn btn-primary']) : 'No Sub Merchant'
            ],
            [
              'label' => 'Additional Button',
              'value' => function($model){
                $additional_button = "";
                if(!empty($model->additional_button)){
                  foreach($model->additional_button as $key => $value){
                    $additional_button .= $key . " => " . $value;
                  }
                }
                return $additional_button;
              }
            ],
            'status',
            'updated_at',
            'created_at',
        ],
    ]); ?>


</div>
