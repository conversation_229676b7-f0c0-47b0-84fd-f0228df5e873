<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use dpodium\filemanager\widgets\FileBrowse;
use yii\helpers\ArrayHelper;
use backend\components\SharedComponent;
/* @var $this yii\web\View */
/* @var $model common\models\GiftCardSubMerchant */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="gift-card-sub-merchant-form">

    <?php $form = ActiveForm::begin(); ?>

    <?php
    if(count($error)){
      echo SharedComponent::generateErrorMessage($error);
    }
    echo SharedComponent::createNavBar();

    $content =
      "<div class='form-group'>".
        Html::Label('Name','name').
        Html::input('text', 'name[%iso_code%]', '%value%-name' , ['class' => "form-control", '%required%' => true]).
      "</div>";

    echo SharedComponent::createContentTab($content,$translation,$model->isNewRecord);

    ?>

    <?php
    echo $form->field($model, 'icon_url')->widget(FileBrowse::className(), [
            'multiple' => false,
            'folderId' => 3
    ]);
    ?>

    <?php echo $form->field($model, 'status')->dropDownList(['1' => 'Publish', '0' => 'Draft']); ?>

    <?php
        if (strpos($this->title, 'Update') !== false) {
            echo $form->field($model, 'gift_card_merchant_id')->dropDownList(
               ArrayHelper::map(common\models\GiftCardMerchant::find()->where(['id' => $model->gift_card_merchant_id])->all(), 'id', 'name'),['style'=>'display:none;'])->label(false) ;
        }
        else{
           echo $form->field($model, 'gift_card_merchant_id')->dropDownList(
               ArrayHelper::map(common\models\GiftCardMerchant::find()->where(['id' => $_GET['id']])->all(), 'id', 'name'),['style'=>'display:none;'])->label(false) ;
        }
    ?>


    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
    </div>

    <?php ActiveForm::end(); ?>
    <?php echo FileBrowse::renderModal(); ?>

</div>
