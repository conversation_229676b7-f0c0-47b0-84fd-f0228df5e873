<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;

/* @var $this yii\web\View */
/* @var $searchModel common\models\GiftCardSubMerchantSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Gift Card Sub Merchants';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="gift-card-sub-merchant-index">

    <p>
        <?php echo Html::a('Create Gift Card Sub Merchant', ['create', 'id' => $_GET['id']], ['class' => 'btn btn-success']); ?>
    </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'name',
            [
                'attribute' => 'icon_url',
                'format' => 'html',
                'label' => 'Icon',
                'value' => function ($data) {
                    return Html::img('' . $data['icon_url'],
                        ['width' => '150px']);
                },
            ],
            'gift_card_merchant_id',
            'status',
            'updated_at',


            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
