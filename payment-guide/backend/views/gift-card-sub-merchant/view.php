<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\GiftCardSubMerchant */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Gift Card Sub Merchants', 'url' => ['index', 'id'=>$gift_merchant_id]];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="gift-card-sub-merchant-view">

    <p>
        <?php echo Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']); ?>
        <?php echo Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'name',
            'gift_card_merchant_id',
            [
                'label' => 'Icon',
                'format' => 'raw',
                'value' => $model->icon_url
            ],
            'status',
            'updated_at',
            'created_at',
        ],
    ]); ?>

</div>
