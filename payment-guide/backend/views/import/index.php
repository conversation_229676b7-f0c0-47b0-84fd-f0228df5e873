<?php

use yii\helpers\Html;

$this->title = 'Data Import';
$this->params['breadcrumbs'][] = $this->title;
$baseUrl = Yii::$app->request->baseUrl;
?>

<button type="button" class="btn btn-default" onclick="postAjax('country')">Import Country From Rest API</button>
<button type="button" class="btn btn-default" onclick="postAjax('region')">Import Region From Rest API</button>
<button type="button" class="btn btn-default" onclick="postAjax('currency')">Import Currency From Rest API</button>
<br>
<br>
<br>
<!-- <button type="button" class="btn btn-warning" onclick="postAjax('delete-all-country')">Delete All Country</button>
<button type="button" class="btn btn-warning" onclick="postAjax('delete-all-region')">Delete All Region</button>
<button type="button" class="btn btn-warning" onclick="postAjax('delete-all-currency')">Delete All Currency</button> -->
<button type="button" class="btn btn-danger" onclick="postAjax('delete-all')">Delete All Country, Region and Currency</button>
<br>
<br>
<br>
<p id="result"></p>

<script>

  function postAjax(action){
      waitingDialog.show();
      $.ajax({
        url: '<?php echo $baseUrl ?>/import/'+action,
        type : 'post',
        success : function(data){
          var data = JSON.parse(data);
          var string = "";
          // waitingDialog.hide();
          for (var i = 0; i < data.length; i++){
            string += data[i] + "<br>";
          }
          $("#result").append(string);
        }

      });
  }

</script>
<?php
// $this->registerJSFile("/js/bootstrap-waitingfor.min.js", ['depends' => [yii\web\JqueryAsset::className()]]);
?>
