<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Language */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Languages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="language-view">

    

    <p>
        <?php echo Html::a('Update', ['update', 'id' => $model->iso_code], ['class' => 'btn btn-primary']); ?>
        <?php echo Html::a('Delete', ['delete', 'id' => $model->iso_code], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'iso_code',
            'name',
            'status',
            'updated_at',
            'created_at',
        ],
    ]); ?>

</div>
