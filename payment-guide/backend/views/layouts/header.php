<?php
use yii\helpers\Html;

/* @var $this \yii\web\View */
/* @var $content string */
?>

<header class="main-header">

    <?php echo Html::a('<span class="logo-mini">PG</span><span class="logo-lg">' . Yii::$app->name . '</span>', Yii::$app->homeUrl, ['class' => 'logo']); ?>

    <nav class="navbar navbar-static-top" role="navigation">

        <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
            <span class="sr-only">Toggle navigation</span>
        </a>
        <div class="navbar-custom-menu">

          <ul class="nav navbar-nav">
            <li class="dropdown messages-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <i class="fa fa-user-o"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a href="/user/update?id=<?php echo Yii::$app->user->identity->id ?>">Change Password</a></li>
                  <li><a href="/site/logout" data-method="post">Log Out</a></li>
                </ul>
            </li>
          </ul>

        </div>
    </nav>



</header>

<?php
$this->registerCss("
  .dropdown-menu{
    min-width: 0px !important;
    width: 150px !important;
  }
");
?>
