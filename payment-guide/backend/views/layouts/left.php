<aside class="main-sidebar">

    <section class="sidebar">
        <?php echo dmstr\widgets\Menu::widget(
            [
                'options' => ['class' => 'sidebar-menu'],
                'items' => [
                    ['label' => 'Payment Category', 'icon' => 'hashtag', 'url' => ['/payment-category'], 'active' => (\Yii::$app->controller->id == 'payment-category')],
                    ['label' => 'Payment Gateway', 'icon' => 'group', 'url' => ['/payment-gateway'], 'active' => (\Yii::$app->controller->id == 'payment-gateway')],
                    ['label' => 'Payment Method', 'icon' => 'cc-paypal', 'url' => ['/payment-method'], 'active' => (\Yii::$app->controller->id == 'payment-method')],
                    ['label' => 'User Guide', 'icon' => 'file-pdf-o', 'url' => ['/user-guide'], 'active' => (\Yii::$app->controller->id == 'user-guide')],
                    ['label' => 'Gift Card Merchant', 'icon' => 'shopping-cart', 'url' => ['/gift-card-merchant'], 'active' => in_array(\Yii::$app->controller->id,['gift-card-merchant','gift-card-sub-merchant'])],
                    ['label' => 'File Manager', 'icon' => 'file-image-o ', 'url' => ['/filemanager/files'], 'active' => (\Yii::$app->controller->id == 'files')],
                    [
                        'label' => 'Localization',
                        'icon' => 'share',
                        'url' => '#',
                        'items' => [
                            ['label' => 'Country', 'icon' => 'flag', 'url' => ['/country'], 'active' => (\Yii::$app->controller->id == 'country')],
                            ['label' => 'Region', 'icon' => 'map-marker', 'url' => ['/region'], 'active' => (\Yii::$app->controller->id == 'region')],
                            ['label' => 'Language', 'icon' => 'language', 'url' => ['/language'], 'active' => (\Yii::$app->controller->id == 'language')],
                            ['label' => 'Currency', 'icon' => 'dollar', 'url' => ['/currency'], 'active' => (\Yii::$app->controller->id == 'currency')],
                        ],
                    ],
                    [
                        'label' => 'Setting',
                        'icon' => 'gear',
                        'url' => '#',
                        'items' => [
                            // ['label' => 'File Manager - Folder', 'icon' => 'folder-open-o ', 'url' => ['/filemanager/folders']],
                          //  ['label' => 'Import Data', 'icon' => 'magic', 'url' => ['/import']],
                            ['label' => 'User', 'icon' => 'user', 'url' => ['/user'], 'active' => (\Yii::$app->controller->id == 'user'), 'visible' => \backend\components\SuperAdminAccessControl::checkAccess(),],
                            ['label' => 'Translation', 'icon' => 'language', 'url' => ['/source-message'], 'active' => (\Yii::$app->controller->id == 'source-message')],
                    
                        ],
                    ],
                    [
                        'label' => 'Administrator',
                        'icon' => 'user-circle',
                        'url' => '#',
                        'items' => [
                          ['label' => 'Cache', 'icon' => 'database', 'url' => ['/cache/index'], 'active' => (\Yii::$app->controller->id == 'cache')],
                          ['label' => 'pipwave API', 'icon' => 'history ', 'url' => ['/pipwave-api/index'], 'active' => (\Yii::$app->controller->id == 'pipwave-api')],
                    
                        ],
                    ],
                ],
                                    
            ]
        ); ?>

    </section>
    <section class="sidebar">
         <?php echo dmstr\widgets\Menu::widget(
            [
                'options' => ['class' => 'sidebar-menu'],
                'items' => [
                    ['label' => 'Visit Site', 'icon' => 'external-link', 'url' => Yii::$app->params['visitSite'],
                        'template' => '<a href="{url}" target="_blank">{icon} {label}</a>'],
                ],                                    
            ]
        ); ?>
    </section>

</aside>
