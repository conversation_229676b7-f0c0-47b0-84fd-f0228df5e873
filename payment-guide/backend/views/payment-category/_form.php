<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use backend\components\SharedComponent;
/* @var $this yii\web\View */
/* @var $model common\models\PaymentCategory */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="payment-category-form">

    <?php

      if(count($error)){
        echo SharedComponent::generateErrorMessage($error);
      }

      $form = ActiveForm::begin();
      echo SharedComponent::createNavBar();
      $content =
        "<div class='form-group'>".
          Html::Label('Name','name').
          Html::input('text', 'name[%iso_code%]', '%value%-name' , ['class' => "form-control", '%required%' => true]).
        "</div>".
        "<div class='form-group'>".
          Html::Label('Description','description').
          Html::textarea('description[%iso_code%]', '%value%-description' , ['class' => "form-control",'rows' => 6]).
        "</div>";
        echo SharedComponent::createContentTab($content,$translation,$model->isNewRecord);

        if($model->status !== 5){
          echo SharedComponent::createStatus($form,$model);
          echo $form->field($model, 'url_name')->textInput(['maxlength' => true]);
          echo $form->field($model, 'order_no')->textInput();
        }

    ?>

    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<?php
$this->registerJs(
  <<< EOT_JS
  $("#translation input[name='name[en-US]']").change(function(){
    var string = $.grep($("#translation input[name='name[en-US]']").val().toLowerCase().split(/(?:\@|#|\$|\.|%|\^|&|\*|,| |\/)+/), Boolean).join("-");
    $("#paymentcategory-url_name").val(string);
  })
EOT_JS
);
?>
