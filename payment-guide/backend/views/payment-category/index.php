<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;
/* @var $this yii\web\View */
/* @var $searchModel common\models\PaymentCategorySearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Payment Categories';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="payment-category-index">

    <p>
        <?php echo Html::a('Create Payment Category', ['create'], ['class' => 'btn btn-success']); ?>
    </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'name',
            'url_name',
            'order_no',
            'status',
            'updated_at',
            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
