<?php

use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentCategory */

$name = Yii::t($model->translate_category,$model->name);
if($model->status == '5')  {

    $this->title = 'Update Gift Card Description';
}
else{

    $this->title = 'Update Payment Category : '. $name;
}


$this->params['breadcrumbs'][] = ['label' => 'Payment Categories', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => $name, 'url' => ['view', 'id' => $model->id]];
$this->params['breadcrumbs'][] = 'Update';
?>
<div class="payment-category-update">

    <?php echo $this->render('_form', [
        'model' => $model,
        'error' => $error,
        'translation' => $translation
    ]); ?>

</div>
