<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\Language;
use backend\components\SharedComponent;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentGateway */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="payment-gateway-form">

    <?php $form = ActiveForm::begin();

      if(count($error)){
        echo SharedComponent::generateErrorMessage($error);
      }

      echo SharedComponent::createNavBar();

      $content =
        "<div class='form-group'>".
          Html::Label('Name','name').
          Html::input('text', 'name[%iso_code%]', '%value%-name' , ['class' => "form-control", '%required%' => true]).
        "</div>";

      echo SharedComponent::createContentTab($content,$translation,$model->isNewRecord);

      echo SharedComponent::createStatus($form,$model);

      echo "<div class=\"form-group\">";
      echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']);
      echo "</div>";

      ActiveForm::end(); ?>

</div>
