<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentGatewaySearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="payment-gateway-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?php

    echo $form->field($model, 'id');

    echo $form->field($model, 'name');

    echo $form->field($model, 'status');

    echo $form->field($model, 'updated_at');

    echo $form->field($model, 'created_at');

    ?>

    <div class="form-group">
        <?php
        echo Html::submitButton('Search', ['class' => 'btn btn-primary']);
        echo Html::resetButton('Reset', ['class' => 'btn btn-default']);
        ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
