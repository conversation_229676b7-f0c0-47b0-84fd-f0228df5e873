<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;

/* @var $this yii\web\View */
/* @var $searchModel common\models\PaymentGatewaySearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Payment Gateways';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="payment-gateway-index">
    <p>
        <?php
          echo Html::a('Create Payment Gateway', ['create'], ['class' => 'btn btn-success'])
        ?>
    </p>
    <?php
    echo DataTables::widget([
      'dataProvider' => $dataProvider,
      'columns' => [
          ['class' => 'yii\grid\SerialColumn'],
          'name',
          'status',
          ['class' => 'yii\grid\ActionColumn'],
      ],
    ]);
    ?>

</div>
