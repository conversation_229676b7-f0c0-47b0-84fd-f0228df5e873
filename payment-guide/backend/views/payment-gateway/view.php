<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentGateway */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Payment Gateways', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="payment-gateway-view">

    <p>
        <?php
          echo Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']);
          echo Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ])
        ?>
    </p>

    <?php
      echo DetailView::widget([
          'model' => $model,
          'attributes' => [
              'id',
              [
                'label' => 'Name',
                'value' => $model->name
              ],
              'status',
              'updated_at',
              'created_at',
          ],
      ]);
    ?>

</div>
