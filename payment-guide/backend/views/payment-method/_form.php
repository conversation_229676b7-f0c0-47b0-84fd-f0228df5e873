<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use yii\helpers\ArrayHelper;
use common\models\PaymentCategory;
use common\models\PaymentGateway;
use common\models\Currency;
use dpodium\filemanager\widgets\FileBrowse;
use yii\web\view;
use backend\components\SharedComponent;
use common\models\Language;
use kartik\select2\Select2;

?>

<div id="field-template" style="display:none">
  <div class="form-group" index="{index}">
      <div class="row">
        <div class="col-md-2"><?php echo Html::input('text', 'additional_info_label[]',"{label}", ['class' => "form-control",'placeholder' => "Label"]); ?></div>
        <div class="col-md-4"><?php echo Html::input('text', 'additional_info_word[]',"{word}", ['class' => "form-control",'placeholder' => "Value"]); ?></div>
        <button class="btn glyphicon glyphicon-trash" onclick="deleteInfoLine(this);return false;"></button>
    </div>
  </div>
</div>

<div id="button-template" style="display:none">
  <div class="form-group" index="{index}">
      <div class="row">
        <div class="col-md-2"><?php echo Html::input('text', 'additional_button_label[]',"{label}", ['class' => "form-control",'placeholder' => "Label"]); ?></div>
        <div class="col-md-4"><?php echo Html::input('text', 'additional_button_url[]',"{url}", ['class' => "form-control",'placeholder' => "Url"]); ?></div>
        <button class="btn glyphicon glyphicon-trash" onclick="deleteButtonLine(this);return false;"></button>
    </div>
  </div>
</div>

<div id="processing_fee-template" style="display:none;">
  <div class="form-group" index="{index}">
      <div class="row">
        <div class="col-md-2"><?php echo Html::dropDownList('processing_fee_currency[]',null,array_merge(["Default"=>"Default"],ArrayHelper::map(Currency::find()->asArray()->all(),'currency_code','currency_code')),["class"=>"form-control","disabled"=>true]); ?></div>
        <div class="col-md-4"><?php echo Html::input('text', 'processing_fee_text[]',"{text}", ['class' => "form-control",'placeholder' => "Value"]); ?></div>
        <button class="btn glyphicon glyphicon-trash" onclick="deleteProcessingFeeLine(this);return false;"></button>
    </div>
  </div>
</div>

<div class="payment-method-form">

    <?php $form = ActiveForm::begin(); ?>

    <?php
    if(count($error)){
      echo SharedComponent::generateErrorMessage($error);
    }
    $paymentCategoryList = SharedComponent::getTranslatedItemList("PaymentCategory");
    $paymentGatewayList = SharedComponent::getTranslatedItemList("PaymentGateway");

    echo $form->field($model, 'payment_category_id')->widget(Select2::classname(), [
      'data' => $paymentCategoryList,
      'options' => ['placeholder' => ''],
    ]);

    echo $form->field($model, 'payment_gateway_id')->widget(Select2::classname(), [
      'data' => $paymentGatewayList,
      'options' => ['placeholder' => ''],
    ]);
    // echo $form->field($model, 'payment_category_id')->dropDownList(
    //   ArrayHelper::map($paymentCategoryList,'id','name'),
    //   [
    //     'prompt' => '',
    //     'options' => SharedComponent::getDropdownOption($paymentCategoryList)
    //   ]
    // );

    // echo $form->field($model, 'payment_gateway_id')->dropDownList(
    //   ArrayHelper::map($paymentGatewayList,'id','name'),
    //   [
    //     'prompt' => '',
    //     'options' => SharedComponent::getDropdownOption($paymentGatewayList)
    //   ]
    // );
    ?>

    <?php

      echo SharedComponent::createNavBar();

      $content =
        "<div class='form-group'>".
          Html::Label('Name','name').
          Html::input('text', 'name[%iso_code%]', '%value%-name' , ['class' => "form-control", '%required%' => true]).
        "</div>".
        "<div class='form-group'>".
          Html::Label('Processing Time','processing_time').
          Html::input('text', 'processing_time[%iso_code%]', '%value%-processing_time' , ['class' => "form-control", '%required%' => true]).
        "</div>".
        "<div class='form-group'>".
          Html::button('Add Multi-Currency Surcharge', ['class' => 'btn btn-success','onclick' => 'generateProcessingFeeField();']).
          Html::input('text', 'processing_fee[%iso_code%]', '%value%-processing_fee' , ['class' => "form-control", 'style' => 'display:none;']).
        "</div><div class='processing_fee'>".
          Html::Label('Processing Fee','processing_fee').

        "</div><div class='form-group'>".
          Html::Label('Description','description').
          Html::input('text', 'description[%iso_code%]', '%value%-description' , ['class' => "form-control"]).
        "</div>".
          Html::input('text', 'additional_info[%iso_code%]', '%value%-additional_info' , ['class' => "form-control", 'style' => 'display:none;']).
          Html::input('text', 'additional_button[%iso_code%]', '%value%-additional_button' , ['class' => "form-control", 'style' => 'display:none;']).
        "<div class='form-group'>".
          Html::button('Add Info', ['class' => 'btn btn-success','onclick' => 'generateField();']).
        "</div><div class='additional_info'>".
        Html::Label('Extra Info','description').
        "</div>".
        "<hr style='border-color:black;'><div class='form-group'>".
          Html::button('Add Button', ['class' => 'btn btn-success','onclick' => 'generateButtonField();']).
        "</div><div class='additional_button'>".
        Html::Label('Extra Button','description').
        "</div><hr style='border-color:black;'>"
        ;

        echo SharedComponent::createContentTab($content,$translation,$model->isNewRecord);

        echo SharedComponent::createStatus($form,$model);
    ?>


    <?php
      echo $form->field($model, 'support_credit')->checkbox();
      echo $form->field($model, 'support_direct')->checkbox();
      echo $form->field($model, 'url_name')->textInput(['maxlength' => true]);
      echo $form->field($model, 'pipwave_payment_id')->textInput();
      echo $form->field($model, 'logo_url')->widget(FileBrowse::className(), [
                'multiple' => false,
                'folderId' => 1
                  ]);
    ?>

    <div class="form-group">
        <?php
          echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']);
        ?>
    </div>

    <?php ActiveForm::end(); ?>
    <?php echo FileBrowse::renderModal(); ?>
</div>

<?php
$this->registerJS(<<<EOT_JS
  function generateField(){
    var index = $(".additional_info:eq(0) .form-group").length;
    $(".additional_info").append(
      $("#field-template").html().replace("\{label\}","").replace("\{word\}","").replace("\{index\}",index)
    );
  }

  function generateButtonField(){
    var index = $(".additional_button:eq(0) .form-group").length;
    $(".additional_button").append(
      $("#button-template").html().replace("\{label\}","").replace("\{url\}","").replace("\{index\}",index)
    );
  }

  function generateProcessingFeeField(){
    var index = $(".processing_fee:eq(0) .form-group").length;
    var template = $("#processing_fee-template").html().replace("\{text\}","").replace("\{index\}",index);
    if($(".processing_fee select[name='processing_fee_currency[]']").length >0){
      template = template.replace("disabled=\"\"","").replace("disabled","");
    }
    $(".processing_fee").append(template);
    bindProcessingFeeEventListener();
  }

  function deleteInfoLine(btn){
    var index = $(btn).closest($('.form-group')).attr("index");
    $(".additional_info").each(function(i,e){
      $(e).find($(".form-group"))[index].remove();
    });
    resetIndex("additional_info");
  }

  function deleteButtonLine(btn){
    var index = $(btn).closest($('.form-group')).attr("index");
    $(".additional_button").each(function(i,e){
      $(e).find($(".form-group"))[index].remove();
    });
    resetIndex("additional_button");
  }

  function deleteProcessingFeeLine(btn){
    var index = $(btn).closest($('.form-group')).attr("index");
    if(index == 0){
      alert("Default line cannot be deleted");
      return false;
    }
    $(".processing_fee").each(function(i,e){
      $(e).find($(".form-group"))[index].remove();
    });
    resetIndex("processing_fee");
  }

  function resetIndex(div){
    $("."+div+"").each(function(i,e){
      $(e).find($(".form-group")).each(function(f,g){
        $(g).attr("index",f);
      });
    });
  }

  function syncProcessingFeeCurrencyCode(btn){
    var index = $(btn).closest($('.form-group')).attr("index");
    var value = $(btn).val();

    $(".processing_fee").each(function(i,e){
      $($(e).find($('select[name="processing_fee_currency[]"'))[index]).val(value);
    });
  }

  function bindProcessingFeeEventListener(){
    $('select[name="processing_fee_currency[]"').change(function(){
      syncProcessingFeeCurrencyCode(this);
    });
  }

  $('#paymentmethod-payment_gateway_id').change(function(){
    $("input[name='description[en-US]']").val("via "+ $('#paymentmethod-payment_gateway_id option:selected').text());
    setUrlName();
  });

  $('input[name="name[en-US]"').change(function(){
    setUrlName();
  });

  function setUrlName(){
    if($('#paymentmethod-payment_gateway_id').val() && $('input[name="name[en-US]"').val()){
      var string = $('#paymentmethod-payment_gateway_id option:selected').text() + "-" + $('input[name="name[en-US]"').val();
      string = $.grep($("#translation input[name='name[en-US]']").val().toLowerCase().split(/(?:\@|#|\$|\.|%|\^|&|\*|,| |\/)+/), Boolean).join("-");
      $("#paymentmethod-url_name").val(string);
    }
  }

  $('form').on('beforeSubmit', function(event) {
      var validate = true;
      $("#translation > div").each(function(i,e){
          var info_field = $(e).find($(".additional_info > .form-group"));
          var info_arr = {};
          var id = $(e).attr('id');
          $(info_field).each(function(f,g){
            var label = $(g).find($("input[name='additional_info_label[]")).val();
            var value = $(g).find($("input[name='additional_info_word[]")).val();
            var name = $(e).find($("input[name='name["+id+"]']")).val();
            if(label !== "" && value !== "" && name !== ""){
              info_arr[label] = value;
            }
            else if((label == "" || value == "") && name !== "" && validate){
              alert('Missing Label / Url on Extra Info Section');
              validate = false;
            }
          });
          if(Object.keys(info_arr).length > 0)
          $(e).find($("input[name='additional_info["+id+"]']")).val(JSON.stringify(info_arr));

          var btn_field = $(e).find($(".additional_button > .form-group"));
          var btn_arr = {};
          var id = $(e).attr('id');
          $(btn_field).each(function(f,g){
            var label = $(g).find($("input[name='additional_button_label[]")).val();
            var value = $(g).find($("input[name='additional_button_url[]")).val();
            var name = $(e).find($("input[name='name["+id+"]']")).val();
            if(label !== "" && value !== "" && name !== ""){
              btn_arr[label] = value;
            }
            else if((label == "" || value == "") && name !== "" && validate){
              alert('Missing Label / Url on Extra Button Section');
              validate = false;
            }
          });
          if(Object.keys(btn_arr).length > 0)
            $(e).find($("input[name='additional_button["+id+"]']")).val(JSON.stringify(btn_arr));

          var processing_fee_field = $(e).find($(".processing_fee > .form-group"));
          var processing_fee_arr = {};
          var id = $(e).attr('id');
          $(processing_fee_field).each(function(f,g){
            var label = $(g).find($("select[name='processing_fee_currency[]")).val();
            var value = $(g).find($("input[name='processing_fee_text[]")).val();
            var name = $(e).find($("input[name='name["+id+"]']")).val();
            if(label !== "" && value !== "" && name !== ""){
              if(processing_fee_arr[label]){
                alert('Duplicate Currency for Processing Fee');
                validate = false;
              }
              else{
                processing_fee_arr[(label == "Default" ? "Default" : label.toLowerCase())] = value;
              }
            }
            else if((label == "" || value == "") && name !== "" && validate){
              alert('Missing Value on Processing Fee Section');
              validate = false;
            }
          });
          if(Object.keys(processing_fee_arr).length > 0){
            $(e).find($("input[name='processing_fee["+id+"]']")).val(JSON.stringify(processing_fee_arr));
          }
      });
      if(validate == false){
        return false;
      }
  });

EOT_JS
,View::POS_END, 'my-options');

if(!$model->isNewRecord){
  $this->registerJS(<<<EOT_JS
    $("#translation > div").each(function(i,e){
        var id = $(e).attr('id');
        var value = $("input[name='additional_info["+id+"]']").val();
        if(value == "") value = $("input[name='additional_info[en-US]']").val();
          if(value !== ""){
            var data = JSON.parse(value);
            var count = 0;
            $.each(data,function(label,value){
              var template = $("#field-template").html()
                            .replace("{label}",label)
                            .replace("{word}",value)
                            .replace("\{index\}",count);

              $(e).find($(".additional_info")).append(template);
              count += 1;
            });
          }

        var value = $("input[name='additional_button["+id+"]']").val();
        if(value == "") value = $("input[name='additional_button[en-US]']").val();
        if(value !== ""){
          var data = JSON.parse(value);
          var count = 0;
          $.each(data,function(label,url){
            var template = $("#button-template").html()
                          .replace("{label}",label)
                          .replace("{url}",url)
                          .replace("\{index\}",count);
            $(e).find($(".additional_button")).append(template);
            count += 1;
          });
          }

        var value = $("input[name='processing_fee["+id+"]']").val();
        if(value == "") value = $("input[name='processing_fee[en-US]']").val();
        if(value !== ""){
          var data = JSON.parse(value);
          var count = 0;
          $.each(data,function(label,text){

            var template = $("#processing_fee-template").html()
                          .replace("{text}",text)
                          .replace("\{index\}",count);
            if(label !== "Default"){
              template = template.replace("disabled=\"\"","").replace("disabled","");
            }

            $(e).find($(".processing_fee")).append(template);
            $($(e).find($("select[name='processing_fee_currency[]']"))[count]).val(label);
            count += 1;
          });
        }
    });
    bindProcessingFeeEventListener();
EOT_JS
    );
}
else{
  $this->registerJS("generateProcessingFeeField()");
}
?>
