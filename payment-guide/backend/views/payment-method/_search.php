<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentMethodSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="payment-method-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?php echo $form->field($model, 'id'); ?>

    <?php echo $form->field($model, 'name'); ?>

    <?php echo $form->field($model, 'description'); ?>

    <?php echo $form->field($model, 'processing_fee'); ?>

    <?php echo $form->field($model, 'processing_time'); ?>

    <?php // echo $form->field($model, 'logo_url'); ?>

    <?php // echo $form->field($model, 'support_credit'); ?>

    <?php // echo $form->field($model, 'support_direct'); ?>

    <?php // echo $form->field($model, 'additional_info'); ?>

    <?php // echo $form->field($model, 'order_no'); ?>

    <?php // echo $form->field($model, 'status'); ?>

    <?php // echo $form->field($model, 'is_global'); ?>

    <?php // echo $form->field($model, 'support_all_currency'); ?>

    <?php // echo $form->field($model, 'updated_at'); ?>

    <?php // echo $form->field($model, 'created_at'); ?>

    <?php // echo $form->field($model, 'payment_gateway_id'); ?>

    <?php // echo $form->field($model, 'payment_category_id'); ?>

    <div class="form-group">
        <?php echo Html::submitButton('Search', ['class' => 'btn btn-primary']); ?>
        <?php echo Html::resetButton('Reset', ['class' => 'btn btn-default']); ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
