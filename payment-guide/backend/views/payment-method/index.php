<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;
/* @var $this yii\web\View */
/* @var $searchModel common\models\PaymentMethodSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Payment Methods';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="payment-method-index">
    <p>
         <?php echo Html::a('Create Payment Method', ['create'], ['class' => 'btn btn-success']); ?>
     </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'name',
            [
                'label' => 'Logo',
                'format' => 'raw',
                'value' => function ($model){
                  return "<img style='max-width: 150px;' src='" . $model->logo_url . "'";
                }
            ],
            [
                'label' => 'Processing Fee',
                'value' => function ($model){
                  $returnVal = [];
                  $processing_fee = json_decode($model->processing_fee);
                  foreach($processing_fee as $i => $v){
                    array_push($returnVal,"$i : $v");
                  }
                  return implode(",",$returnVal);
                }
            ],
            'processing_time',
            [
                'label' => '',
                'format' =>'html',
                'value' => function ($model){
                  return "<span class='fa fa-circle' style='color:".($model->status === "Active" ? 'green' : 'red')."'></span>";
                }
            ],
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {update} {delete} {myButton}',
                'buttons' => [
                    'myButton' => function($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-file"></span>', ['user-guide/update', 'id' => $model->id]);
                    }
                ]
            ],
        ],
    ]); ?>
</div>
