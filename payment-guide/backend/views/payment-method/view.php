<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use dpodium\filemanager\Files;

/* @var $this yii\web\View */
/* @var $model common\models\PaymentMethod */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Payment Methods', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="payment-method-view">

    <p>
        <?php echo Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']); ?>
        <?php echo Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'name',
            'description',
            [
                'label' => 'Processing Fee',
                'value' => function ($model){
                  $returnVal = [];
                  $processing_fee = json_decode($model->processing_fee);
                  foreach($processing_fee as $i => $v){
                    array_push($returnVal,"$i : $v");
                  }
                  return implode(",",$returnVal);
                }
            ],
            'processing_time',
            [
                'label' => 'Logo',
                'format' => 'raw',
                'value' => "<img style='max-width: 150px;' src='" . $model->logo_url . "'"
            ],
            'support_credit',
            'support_direct',
            [
              'label' => 'Additional Info',
              'value' => function($model){
                $additional_info = "";
                if(!empty($model->additional_info)){
                  foreach($model->additional_info as $key => $value){
                    $additional_info .= $key . " => " . $value;
                  }
                }
                return $additional_info;
              }
            ],
            [
              'label' => 'Additional Button',
              'value' => function($model){
                $additional_button = "";
                if(!empty($model->additional_button)){
                  foreach($model->additional_button as $key => $value){
                    $additional_button .= $key . " => " . $value;
                  }
                }
                return $additional_button;
              }
            ],
            'order_no',
            'status',
            'updated_at',
            'created_at',
            'payment_gateway_id',
            'payment_category_id',
        ],
    ]); ?>

</div>
