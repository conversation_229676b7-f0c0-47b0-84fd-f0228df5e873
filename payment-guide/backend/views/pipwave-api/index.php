<?php

use yii\helpers\Html;
use yii\bootstrap\Modal;
use fedemotta\datatables\DataTables;
use common\models\Country;
use common\models\Currency;
use yii\helpers\ArrayHelper;
use kartik\select2\Select2;

/* @var $this yii\web\View */
/* @var $searchModel common\models\PipwaveRequestSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'pipwave';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="pipwave-request-index">

  <h3>Request History</h3>
    <?php
    echo "<p>";
    // echo Html::a('Mark all as expired', ['/pipwave-api/set-all-expired'], ['class'=>'btn btn-danger', ["data-confirm" => "Are you sure you want to mark all as expired?"]]);
    Modal::begin([
    'header' => '<h4>Set API Request as Expired</h4>',
    'toggleButton' => ['label' => 'Mark as Expired','class'=>'btn btn-success'],
    ]);
    $countrylist = ArrayHelper::map(Country::find()->where(['status' => 1])->asArray()->all(),'country_code',function($model){
      return $model["name"]. " (" . strtoupper($model["country_code"]) . ")";
    });
    $currencylist =  ArrayHelper::map(Currency::find()->asArray()->all(),'currency_code',function($model){
      return $model["name"]. " (" . strtoupper($model["currency_code"]) . ")";
    });

    echo Html::beginForm('/pipwave-api/set-partial-expired','get');
    echo "<div class='form-group'>".Html::Label('Country','').
      Select2::widget([
          'name' => 'country_code',
          'value' => '',
          'data' => $countrylist,
          'options' => ['placeholder' => '','class'=>'form-control']
      ]).
    "</div>";

    echo "<div class='form-group'>".Html::Label('Currency','').
      Select2::widget([
          'name' => 'currency_code',
          'value' => '',
          'data' => $currencylist,
          'options' => ['placeholder' => '','class'=>'form-control']
      ]).
    "</div>";
    echo "<div class='form-group'>".
          Html::submitButton('Submit', ['class' => 'btn btn-primary']).
    "</div>";
    echo Html::endForm();
    Modal::end();

    echo "</p>";
    echo \yii\grid\GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'currency_code',
            'country_code',
            [
              'attribute'=>'expiry',
              'format' => ['date', 'php:Y-m-d H:i']
            ],
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {delete}',
                'buttons' => [
                  'delete' => function($url, $model, $key) {
                      return Html::a('<span class="glyphicon glyphicon-refresh"></span>', ['pipwave-api/set-expired', 'currency_code' => $model->currency_code, 'country_code' => $model->country_code], ["data-confirm" => "Are you sure you want to set this item as expired?"]);
                  }
                ]
            ],
        ],
    ]); ?>

  <h3>Request Error Log</h3>
    <?php
    echo "<p>";
    echo Html::a('Delete All Log', ['/pipwave-api/delete-all-log'], ['class'=>'btn btn-danger', ["data-confirm" => "Are you sure you want to remove all log?"]]);
    echo "</p>";
    echo \yii\grid\GridView::widget([
        'dataProvider' => $logDataProvider,
        'filterModel' => $logSearchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'currency_code',
            'country_code',
            [
              'attribute'=>'created_at',
              'format' => ['date', 'php:Y-m-d H:i']
            ],
            [
                'header' => 'Action',
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {delete}',
                'buttons' => [
                    'view' => function($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', ['pipwave-api/view-log', 'id' => $model->id]);
                    },
                    'delete' => function($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-trash"></span>', ['pipwave-api/delete-log', 'id' => $model->id], ["data-confirm" => "Are you sure you want to delete this item?"]);
                    }
                ]
            ],
        ],
    ]); ?>
    <?php
    // Fix for select2 search input box not working in modal
    $this->registerJs('$.fn.modal.Constructor.prototype.enforceFocus = function() {};');
    ?>
</div>
