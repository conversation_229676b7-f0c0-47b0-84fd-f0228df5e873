<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\PipwaveRequest */

$this->title = $model->currency_code;
$this->params['breadcrumbs'][] = ['label' => 'Pipwave Requests', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="pipwave-request-view">

    <p>
        <?php echo Html::a('Delete', ['delete-log', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'currency_code',
            'country_code',
            [
                'label' => 'Results',
                'format' => 'html',
                'value' => "<pre class='prettyprint'>$model->results</pre>",
                'options' => [
   'style'=>'max-width:150px; min-height:100px; overflow: auto; word-wrap: break-word;'
],
            ],
            [
              'attribute'=>'created_at',
              'format' => ['date', 'php:Y-m-d H:i']
            ],
        ],
    ]); ?>

</div>
<?php
  \backend\assets\CodePrettifyAsset::register($this);
  $this->registerJs('
    var data = $(".prettyprint").text();
    if(IsJsonString(data)){
      $(".prettyprint").text(JSON.stringify(JSON.parse($(".prettyprint").text()), null, 4));
    }
  ');
  $this->registerJs('
  function IsJsonString(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
  }
',\yii\web\view::POS_END);

  ?>
