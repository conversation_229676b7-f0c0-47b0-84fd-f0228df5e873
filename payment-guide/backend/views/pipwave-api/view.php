<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\PipwaveRequest */

$this->title = $model->currency_code;
$this->params['breadcrumbs'][] = ['label' => 'Pipwave Requests', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="pipwave-request-view">

    <p>
        <?php echo Html::a('Delete', ['delete', 'currency_code' => $model->currency_code, 'country_code' => $model->country_code], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            'currency_code',
            'country_code',
            [
                'label' => 'Results',
                'format' => 'html',
                'value' => "<pre class='prettyprint'>$model->results</pre>",
            ],
            [
              'attribute'=>'expiry',
              'format' => ['date', 'php:Y-m-d H:i']
            ],
        ],
    ]); ?>

</div>
<?php
  \backend\assets\CodePrettifyAsset::register($this);
  $this->registerJs('
    $(".prettyprint").text(JSON.stringify(JSON.parse($(".prettyprint").text()), null, 4));
  ');

  ?>
