<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use yii\helpers\ArrayHelper;
use common\models\Language;
use yii\web\view;

/* @var $this yii\web\View */
/* @var $model common\models\SourceMessage */
/* @var $form yii\widgets\ActiveForm */
?>
<div id="field-template" style='display:none'>
    <?php echo generateFieldTemplate(); ?>
</div>
<div class="source-message-form">
    <?php $form = ActiveForm::begin(); ?>

    <?php echo $form->field($model, 'message')->input(['text']); ?>

    <?php echo Html::label("Translated Message"); ?>
    <br>
    <div id="translated_lang">
      <?php
        $index = 0;
        foreach($translation as $t){
          echo generateFieldTemplate($t->id,$t->language,$t->translation,$index);
          $index++;
        }
      ?>
    </div>
    <button type="button" class="btn btn-info" style="margin-bottom:10px;" onclick="generateField();"><i class="glyphicon glyphicon-plus"></i></button>
    <div class="form-group" style="margin-top:100px;">
        <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<?php

  $baseUrl = Yii::$app->request->baseUrl;
  function generateFieldTemplate($id='',$lang='',$message='',$count="{count}"){
    $template =
    "<div class='form-group'><div class='input-group'>".
    Html::input('text','Message['.$count.'][id]',$id,['style'=>'display:none;','itemType'=>'id']).
    "<div class='input-group-btn'>" .
    Html::dropDownList('Message['.$count.'][language]',$lang, ArrayHelper::map(Language::find()->all(),'iso_code','name'),['class'=>'form-control','style'=>'min-width:200px','prompt'=>'','itemType'=>'language'])
    . "</div>".
    Html::input('text','Message['.$count.'][translation]',$message, ['class' => "form-control",'placeholder' => "Url"]).
    "<span class='input-group-btn'><button type='button' class='btn btn-default' onclick='removeTranslation(this);'><i class='glyphicon glyphicon-trash'></i></button></span></div></div>"
    ;

    return $template;
  }

$this->registerJs(<<<EOT_JS
  function generateField(){
    var count = $('#translated_lang .form-group').length;
    console.log(count);
    $("#translated_lang").append($("#field-template").html().split("\{count\}").join(count));
  }

  function removeTranslation(btn){
    var item = $(btn).closest($('.form-group'));
    if($(item).find($('input[itemType=\'id\']')).val() == ""){
      $(item).remove()
    }
    else{
      $.ajax({
        url: '$baseUrl/source-message/ajax-delete-translate',
        type : 'post',
        data : {
          id : $(item).find($('input[itemType=\'id\']')).val(),
          language : $(item).find($('select[itemType=\'language\']')).val()
        },
        success : function(data){
          $(item).remove();
          console.log(data);
        }
      })
    }
  }

EOT_JS
,View::POS_END, 'my-options')
?>
