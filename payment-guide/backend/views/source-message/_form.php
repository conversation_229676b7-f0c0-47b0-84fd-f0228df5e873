<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use dpodium\filemanager\widgets\FileBrowse;
use yii\helpers\ArrayHelper;
use backend\components\SharedComponent;
use common\models\Language;

/* @var $this yii\web\View */
/* @var $model common\models\UserGuide */
/* @var $form yii\widgets\ActiveForm */


  function setValue($model, $language, $field){
    if(isset($model[$language->iso_code])){
      return $model[$language->iso_code][$field];
    }
    else{
      return '';
    }
  }
?>

<!--style>
	.hidden{display:none;}
</style-->

<div class="source-message-form">

    <?php

    $form = ActiveForm::begin();

    // echo $form->field($model, 'message')->textInput(['maxlength' => true]);

    echo SharedComponent::createNavBar();

    $languageList = Language::getActiveLanguage();
    $html = '';
    foreach($languageList as $language){
      $iso_code = $language->iso_code;
      $html .=
      "<div id='{$iso_code}' class='tab-pane fade".($iso_code === "en-US" ? " in active":"")."'><div style='padding:0px 15px 15px 20px;margin-bottom:15px;'><br>".
          Html::input('text', "id[$iso_code]", setValue($message,$language,'id') , ['class' => "form-control",'style' => "display:none;"]).
        "<div class='form-group'>".
          Html::Label('Message','message').
          Html::textarea("translation[$iso_code]", setValue($message,$language,'translation') , ['class' => "form-control editor",'rows' => 6]).
        "</div>".
          Html::input('text',"language_code[$iso_code]", $language->iso_code , ['class' => "form-control","style" => "display:none;"]).
      "</div></div>";
    }
    ?>

    <div class="tab-content">
      <?php echo $html; ?>
    </div>

    <div class="form-group">
        <?php
        echo Html::submitButton('Update', ['class' => 'btn btn-primary']);
        ?>
    </div>

    <?php ActiveForm::end(); ?>


</div>

<?php
    backend\assets\TinyMceAsset::register($this);
    $this->registerJs(<<<JS
    tinyMCE.init({
        selector : ".editor",
        menubar: false,
        force_br_newlines : false,
        force_p_newlines : false,
        forced_root_block : '',
        plugins: [
          'advlist autolink lists link image charmap print preview anchor',
          'searchreplace visualblocks code fullscreen',
          'insertdatetime media table contextmenu paste code'
        ],
        toolbar: 'undo redo | insert | styleselect | bold italic | bullist numlist | link',
    });
JS
);
?>
