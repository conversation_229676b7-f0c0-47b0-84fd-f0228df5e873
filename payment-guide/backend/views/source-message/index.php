<?php

use yii\helpers\Html;
use fedemotta\datatables\DataTables;
/* @var $this yii\web\View */
/* @var $searchModel common\models\SourceMessageSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Translation';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="source-message-index">
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?php
          // echo Html::a('Create Source Message', ['update'], ['class' => 'btn btn-success']);
        ?>
    </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'category',
            [
              'label' => 'Tag',
              'value' => function ($model){
                return $model->message;
              }
            ],
            [
              'label' => 'Message',
              'format' => 'raw',
              'value' => function ($model){
                return Yii::t('app',$model->message);
              }
            ],
            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>

</div>
