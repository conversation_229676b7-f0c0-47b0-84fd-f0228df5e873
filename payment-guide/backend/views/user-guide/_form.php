<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use dpodium\filemanager\widgets\FileBrowse;
use yii\helpers\ArrayHelper;
use backend\components\SharedComponent;
use common\models\Language;
use common\models\PaymentMethod;
use yii\web\view;

/* @var $this yii\web\View */
/* @var $model common\models\UserGuide */
/* @var $form yii\widgets\ActiveForm */


  function setValue($model, $language, $field){
    if(isset($model[$language->iso_code])){
      return $model[$language->iso_code][$field];
    }
    else{
      return '';
    }
  }
?>

<div class="user-guide-form">

    <?php $form = ActiveForm::begin(['enableClientValidation'=>false]);

    echo $form->field((new common\models\UserGuide()), 'name')->widget(FileBrowse::className(), [
            'multiple' => false,
            'folderId' => 2
    ])->label(false);

    echo SharedComponent::createNavBar();

    $languageList = Language::getActiveLanguage();
    $html = '';
    foreach($languageList as $language){
      $iso_code = $language->iso_code;
      $html .=
      "<div id='{$iso_code}' class='tab-pane fade".($iso_code === "en-US" ? " in active":"")."'><div style='padding:0px 15px 15px 20px;margin-bottom:15px;'><br>".
          Html::input('text', "id[$iso_code]", setValue($model,$language,'id') , ['class' => "form-control",'style' => "display:none;"]).
        "<div class='form-group'>".
          Html::Label('Name','name').
          Html::input('text', "name[$iso_code]", setValue($model,$language,'name') , ['class' => "form-control"]).
        "</div>".
        "<div class='form-group'>".
          Html::Label('Content','content').
          Html::textarea("content[$iso_code]", setValue($model,$language,'content') , ['class' => "form-control editor","id" => "tinymce-$iso_code", "style"=>"height: 400px"]).
        "</div>".
        "<div class='form-group'>".
          Html::Label('Meta Desription','meta_description').
          Html::textarea("meta_description[$iso_code]", setValue($model,$language,'meta_description') , ['class' => "form-control",'rows' => 6]).
        "</div>".
          Html::input('text',"language_code[$iso_code]", $language->iso_code , ['class' => "form-control","style" => "display:none;"]).
        "<div class='form-group'>".
          Html::Label('Status','status').
          Html::dropDownList("status[$iso_code]", setValue($model,$language,'status'), [ 0 => 'Inactive' , 1 => 'Active'], ['class' => "form-control"]).
        "</div>".
      "</div></div>";
    }
    ?>

    <div id="translation" class="tab-content">
      <?php echo $html; ?>
    </div>

    <div class="form-group">
        <?php
        echo Html::submitButton('Update', ['class' => 'btn btn-primary']);
        ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
<?php echo FileBrowse::renderModal(); ?>
<?php
    $bundle = Yii::$app->assetManager->getBundle('backend\assets\AppAsset');
    backend\assets\TinyMceAsset::register($this);
    $this->registerJSFile(Yii::$app->assetManager->getAssetUrl($bundle,"js/editor.js"), ['depends' => [yii\web\JqueryAsset::className()]]);
    $this->registerCss(<<<CSS
      #w1 {
        display:none;
      }
CSS
);

$baseUrl = Yii::$app->request->baseUrl;
$this->registerJs(<<<JS
    $('form').on('beforeSubmit', function(e) {
        var validate = true;
        $('.field-userguide-name').remove();
        $("#translation > div").each(function(i,e){
            var id = $(e).attr('id');
            var name = $("input[name='name["+id+"]']").val();
            var content = $("textarea[name='content["+id+"]']").val();
            if((!name && content) || (name && !content)){
              validate = false;
              alert('Please Fill Out Title & Content Field.')
              return;
            }
        });
        return validate;
    });

    $("#translation > div").each(function(i,e){
        var id = $(e).attr('id');
        $("input[name='name["+id+"]']").change(function(){
          if($("textarea[name='meta_description["+id+"]']").val() == "")
          $("textarea[name='meta_description["+id+"]']").val($("input[name='name["+id+"]']").val());
        });
    });
JS
);

  // Fix for select2 search input box not working in modal
  $this->registerJs('$.fn.modal.Constructor.prototype.enforceFocus = function() {};');
?>
