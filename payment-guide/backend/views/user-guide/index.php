<?php

use yii\helpers\Html;
use yii\bootstrap\Modal;
use yii\helpers\ArrayHelper;
use common\models\PaymentMethod;
use yii\web\view;
use fedemotta\datatables\DataTables;
use backend\components\SharedComponent;
use kartik\select2\Select2;

/* @var $this yii\web\View */
/* @var $searchModel common\models\UserGuideSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'User Guides';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="user-guide-index">

    <p>
        <?php
        Modal::begin([
        'header' => '<h4>Create User Guide</h4>',
        'toggleButton' => ['label' => 'Create User Guide','class'=>'btn btn-success'],
        ]);
        $paymentmethod = SharedComponent::getTranslatedItemList('PaymentMethod');
        echo "<div class='form-group'>".Html::Label('Payment Method','payment_method_id').
          Select2::widget([
              'name' => 'payment_method_id',
              'value' => '',
              'data' => $paymentmethod,
              'options' => ['placeholder' => '','class'=>'form-control']
          ]).
        "</div>";

        echo "<div class='form-group'>".
              Html::button('Create', ['class' => 'btn btn-primary', 'onclick' =>'createUserGuide();']).
        "</div>";

    Modal::end();
        Html::a('Create User Guide', ['update'], ['class' => 'btn btn-success']); ?>
    </p>
    <?php echo DataTables::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'name',
            [
              'label' => 'Payment Method',
              'value' => function($model){
                return $model->payment_method_id["name"];
              }
            ],
            [
              'label' => 'Visitors',
               'value' => function($model){
                    return isset($model->userGuideVisitors->visit_count) ? $model->userGuideVisitors->visit_count : 0;
               }
            ],
            'language_code',
            'status',
            'updated_at',
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {update} {delete}',  // the default buttons + your custom button
                'buttons' => [
                    'update' => function($url, $model, $key) {     // render your custom button
                        return Html::a('<span class="glyphicon glyphicon-edit"></span>', ['user-guide/update', 'id' => $model->payment_method_id['id']]);
                    }
                ]
            ],
        ],
    ]); ?>

</div>

<?php
$this->registerJS(<<<EOT_JS
  function createUserGuide(){
    window.location.replace("/user-guide/update?id="+$("select[name=payment_method_id]").val());
  }
EOT_JS
,View::POS_END, 'my-options');
?>
