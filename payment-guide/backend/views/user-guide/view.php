<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\UserGuide */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'User Guides', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<style>
    .step{    
        width: 50px;
        top: 2px;
    }
</style>
<div class="user-guide-view">

    <p>
        <?php echo Html::a('Update', ['update', 'id' => $model->payment_method_id['id']], ['class' => 'btn btn-primary']); ?>
        <?php echo Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]); ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
        //    'id',
            'name',
            'content:html',
            'status',
            'updated_at',
            'created_at',
            [
              'label' => 'Payment Method',
              'value' => function($model){
                return $model['name'];
              }
            ],
            'language_code',
        ],
    ]); ?>

</div>
