<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\web\View;


/* @var $this yii\web\View */
/* @var $model common\models\User */
/* @var $form ActiveForm */
?>
<div class="form">

    <?php $form = ActiveForm::begin(); ?>

        <?php
             if((Yii::$app->controller->action->id != 'update') || \backend\components\SuperAdminAccessControl::checkAccess()){
                echo $form->field($model, 'username')->textInput(['maxlength' => 50]) ;
             }
        ?>

        <?php echo $form->field($model, 'password_hash')->passwordInput(['maxlength' => 50,'value'=>''])->label('Password'); ?>

        <?php
             if((Yii::$app->controller->action->id != 'update') || \backend\components\SuperAdminAccessControl::checkAccess()){
               echo $form->field($model, 'email');
               echo $form->field($model, 'status')->dropDownList(['10' => 'Active', '0' => 'Inactive']);
             }
         ?>

        <div class="form-group">
            <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']); ?>
        </div>
    <?php ActiveForm::end(); ?>

</div><!-- _form -->
