<?php

namespace common\components;

use Yii;
use yii\base\InvalidConfigException;
use yii\base\InvalidParamException;
use yii\helpers\FileHelper;
use yii\caching\TagDependency;
use \Aws\CacheInterface;
use \Aws\S3\S3ClientInterface;
use yii\helpers\Url;

class AssetManager extends \yii\web\AssetManager
{
    public $s3BucketTag, $s3, $baseUrl, $basePath, $cacheTime;

    public function init()
    {
        if (empty($this->cacheTime)) {
            $this->cacheTime = '86400';
        }
        self::init_s3();
        $this->baseUrl = Yii::getAlias($this->baseUrl);
        $this->basePath = Yii::getAlias($this->basePath);
        if (empty($this->s3BucketTag) && is_dir(Yii::getAlias($this->basePath))) {
            mkdir(Yii::getAlias($this->basePath));
        }
    }

    protected function init_s3()
    {
        if (!empty($this->s3BucketTag)) {
            $this->s3 = Yii::$app->aws->getS3($this->s3BucketTag);
            StreamWrapper::register($this->s3->getInstance());
        } else {
            throw new InvalidConfigException("S3 bucket tag cannot be empty!");
        }
    }

    public function getAssetUrl($bundle, $asset)
    {
        if (($actualAsset = $this->resolveAsset($bundle, $asset)) !== false) {
            if (strncmp($actualAsset, '@web/', 5) === 0) {
                $asset = substr($actualAsset, 5);
                $basePath = Yii::getAlias('@webroot');
                $baseUrl = Yii::getAlias('@web');
            } else {
                $asset = Yii::getAlias($actualAsset);
                $basePath = $this->basePath;
                $baseUrl = $this->baseUrl;
            }
        } else {
            $basePath = $bundle->basePath;
            $baseUrl = $bundle->baseUrl;
        }

        if (!Url::isRelative($asset) || strncmp($asset, '/', 1) === 0) {
            return $asset;
        }
        $cache_key = 'Performance/' . \Yii::$app->id . '/publish_check/' . md5("$baseUrl/$asset") . '/boolean';
        $url = Yii::$app->cache->getOrSet($cache_key, function ($cache) use ($baseUrl, $asset, $basePath) {
            if (($timestamp = @filemtime(self::replacePathSeperator("$basePath/$asset"))) > 0) {
                return "$baseUrl/$asset?v=$timestamp";
            } else {
                return "$baseUrl/$asset";
            }
        }, $this->cacheTime, new TagDependency(['tags' => 'com.payment-guide.asset-manager']));
        return $url;
    }

    protected function replacePathSeperator($path)
    {
        return str_replace("\\", "/", $path);
    }

    /**
     * Publishes a file to S3.
     * @param string $src the asset file to be published
     * @return array the path and the URL that the asset is published as.
     * @throws InvalidParamException if the asset to be published does not exist.
     */
    protected function publishFile($src)
    {
        if ($val = $this->publishCheck($src)) {
            return $val;
        }
        $dir = $this->hash($src);
        $fileName = basename($src);
        $dstDir = $this->basePath . DIRECTORY_SEPARATOR . $dir;
        $dstFile = $dstDir . DIRECTORY_SEPARATOR . $fileName;

        if ($this->linkAssets) {
            if (!is_file($dstFile)) {
                symlink($src, $dstFile);
            }
        } elseif (@filemtime($dstFile) < @filemtime($src)) {
            $context = stream_context_create([
                's3' => ['ACL' => 'public-read', 'CacheControl' => 'max-age:' . $this->cacheTime]
            ]);
            copy($src, $dstFile, $context);
        }
        return $this->publishCheck($src, [$dstFile, $this->baseUrl . "/$dir/$fileName"]);
    }

    /**
     * Publishes a directory to S3.
     * @param string $src the asset directory to be published
     * @param array $options the options to be applied when publishing a directory.
     * The following options are supported:
     *
     * - only: array, list of patterns that the file paths should match if they want to be copied.
     * - except: array, list of patterns that the files or directories should match if they want to be excluded from being copied.
     * - caseSensitive: boolean, whether patterns specified at "only" or "except" should be case sensitive. Defaults to true.
     * - beforeCopy: callback, a PHP callback that is called before copying each sub-directory or file.
     *   This overrides [[beforeCopy]] if set.
     * - afterCopy: callback, a PHP callback that is called after a sub-directory or file is successfully copied.
     *   This overrides [[afterCopy]] if set.
     * - forceCopy: boolean, whether the directory being published should be copied even if
     *   it is found in the target directory. This option is used only when publishing a directory.
     *   This overrides [[forceCopy]] if set.
     *
     * @return array the path directory and the URL that the asset is published as.
     * @throws InvalidParamException if the asset to be published does not exist.
     */
    protected function publishDirectory($src, $options)
    {
        if ($val = $this->publishCheck($src)) {
            return $val;
        }
        $dir = $this->hash($src);
        $dstDir = $this->basePath . DIRECTORY_SEPARATOR . $dir;
        $opts = array_merge($options, ['dirMode' => $this->dirMode, 'fileMode' => $this->fileMode]);
        if (!isset($opts['beforeCopy'])) {
            if ($this->beforeCopy !== null) {
                $opts['beforeCopy'] = $this->beforeCopy;
            } else {
                $opts['beforeCopy'] = function ($from, $to) {
                    return strncmp(basename($from), '.', 1) !== 0;
                };
            }
        }
        if (!isset($opts['afterCopy']) && $this->afterCopy !== null) {
            $opts['afterCopy'] = $this->afterCopy;
        }
        $this->_copyDirectory($src, $dstDir, $opts);
        return $this->publishCheck($src, [$dstDir, $this->baseUrl . '/' . $dir]);
    }

    /**
     * Copies a whole directory as another one in S3
     * Duplicate from FileHelper::copyDirectory($src, $dst, $options = [])
     * @param string $src the source directory
     * @param string $dst the destination directory
     * @param array $options reference FileHelper::copyDirectory
     * @throws InvalidParamException
     */
    public function _copyDirectory($src, $dst, $options = [])
    {
        if (empty($this->s3BucketTag) && !is_dir($dst)) {
            mkdir($dst, isset($options['dirMode']) ? $options['dirMode'] : 0775, true);
        }
        $handle = opendir($src);
        if ($handle === false) {
            throw new InvalidParamException("Unable to open directory: $src");
        }
        while (($file = readdir($handle)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $from = $src . DIRECTORY_SEPARATOR . $file;
            $to = $dst . DIRECTORY_SEPARATOR . $file;
            $to = str_replace("\\", "/", $to);
            if (FileHelper::filterPath($from, $options)) {
                if (isset($options['beforeCopy']) && !call_user_func($options['beforeCopy'], $from, $to)) {
                    continue;
                }
                if (is_file($from) && (!is_file($to) || (is_file($to) && (@filemtime($from) > @filemtime($to))))) {
                    $context = stream_context_create([
                        's3' => ['ACL' => 'public-read', 'CacheControl' => 'max-age:' . $this->cacheTime]
                    ]);
                    copy($from, $to, $context);
                } elseif (is_file($from)) {
                    continue;
                } else {
                    static::_copyDirectory($from, $to, $options);
                }
                if (isset($options['afterCopy'])) {
                    call_user_func($options['afterCopy'], $from, $to);
                }
            }
        }
        closedir($handle);
    }

    protected function publishCheck($src, $returns = null)
    {
        $cache_key = 'Performance/' . \Yii::$app->id . '/publish_check/' . md5($this->basePath . $src) . '/boolean';
        if (isset($returns)) {
            Yii::$app->cache->set($cache_key, $returns, $this->cacheTime, new TagDependency(['tags' => "com.payment-guide.asset-manager"]));
            return $returns;
        } else {
            return Yii::$app->cache->get($cache_key);
        }
    }

}

class StreamWrapper extends \Aws\S3\StreamWrapper
{
    /**
     * Register the 's3://' stream wrapper, copied from aws-sdk-php/src/S3/StreamWrapper
     *
     * @param S3ClientInterface $client Client to use with the stream wrapper
     * @param string $protocol Protocol to register as.
     * @param CacheInterface $cache Default cache for the protocol.
     */
    public static function register(S3ClientInterface $client, $protocol = 's3', CacheInterface $cache = null)
    {
        if (in_array($protocol, stream_get_wrappers())) {
            stream_wrapper_unregister($protocol);
        }

        // Set the client passed in as the default stream context client
        // Do not register stream wrapper with STREAM_IS_URL
        stream_wrapper_register($protocol, get_called_class());
        $default = stream_context_get_options(stream_context_get_default());
        $default[$protocol]['client'] = $client;

        if ($cache) {
            $default[$protocol]['cache'] = $cache;
        } elseif (!isset($default[$protocol]['cache'])) {
            // Set a default cache adapter.
            $default[$protocol]['cache'] = new \Aws\LruArrayCache();
        }

        stream_context_set_default($default);
    }
}
