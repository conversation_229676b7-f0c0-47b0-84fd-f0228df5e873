<?php

namespace common\components;

use Yii;
use yii\caching\TagDependency;
use yii\base\Exception;

class MemCache {

    const CACHE_MENU = 'com.payment-guide.menu';
    const CACHE_PIPWAVEAPI = 'com.payment-guide.pipwaveAPI';
    const CACHE_TRANSLATION = 'com.payment-guide.translation';
    const CACHE_CURRENCY = 'com.payment-guide.currency';
    const CACHE_COUNTRY = 'com.payment-guide.country';
    const CACHE_LANGUAGE = 'com.payment-guide.language';
    const CACHE_ASSETMANAGER = "com.payment-guide.asset-manager";
    
    public static function getCacheTagConstants() {
        return [
            ['key' => self::CACHE_MENU, 'name' => 'Menu'],
            ['key' => self::CACHE_PIPWAVEAPI, 'name' => 'Pipwave API'],
            ['key' => self::CACHE_TRANSLATION, 'name' => 'Translation'],
            ['key' => self::CACHE_CURRENCY, 'name' => 'Currency'],
            ['key' => self::CACHE_COUNTRY, 'name' => 'Country'],
            ['key' => self::CACHE_LANGUAGE, 'name' => 'Language'],
            ['key' => self::CACHE_ASSETMANAGER, 'name' => 'Asset Manager']
        ];
    }

    public static function set($key, $value, $duration = 0, $cacheTag = null) {
        if (Yii::$app->cache) {
            $availableTags = \yii\helpers\ArrayHelper::getColumn(self::getCacheTagConstants(), 'key');
            if (!in_array($cacheTag, $availableTags)) {
                throw new Exception('Invalid cache tag.');
            }
            $dependency = is_null($cacheTag) ? $cacheTag : new TagDependency(['tags' => $cacheTag]);

            return Yii::$app->cache->set($key, $value, $duration, $dependency);
        }

        return false;
    }

    public static function get($key) {
        if (Yii::$app->cache) {
            return Yii::$app->cache->get($key);
        }

        return false;
    }

    public static function delete($key) {
        if (Yii::$app->cache) {
            return Yii::$app->cache->delete($key);
        }

        return false;
    }

    public static function invalidate($tags) {
        if (Yii::$app->cache) {
            TagDependency::invalidate(Yii::$app->cache, $tags);
        }
    }

}
