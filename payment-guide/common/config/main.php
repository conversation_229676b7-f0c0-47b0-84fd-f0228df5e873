<?php
$params = array_merge(
        require(__DIR__ . '/params.php'),
        require(__DIR__ . '/params-local.php')
);

return [
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'components' => [
        'aws' => [
          'class' => 'common\components\AWS',
        ],
        'geoip' => [
                    'class' => 'dpodium\yii2\geoip\components\CGeoIP',
                    'mode' => 'STANDARD',  // Choose MEMORY_CACHE or STANDARD mode
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                        'sourceLanguage' => 'sys',
                        'class' => 'common\components\DbMessageSource',
                        'on missingTranslation' => ['frontend\components\TranslationEventHandler', 'handleMissingTranslation']
                ],
                'app' => [
                        'sourceLanguage' => 'sys',
                        'class' => 'common\components\DbMessageSource',
                        'on missingTranslation' => ['frontend\components\TranslationEventHandler', 'handleMissingTranslation']
                ],
            ],
        ],
    ],
    'timeZone' => 'Asia/Kuala_Lumpur',
];
