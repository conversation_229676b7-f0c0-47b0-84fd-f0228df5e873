<?php


namespace common\models;

use Yii;

class BaseModel extends \yii\db\ActiveRecord
{
  const STATUS_DELETED = 0;
  const STATUS_ACTIVE = 1;

  public function beforeSave($insert)
  {
      if (!parent::beforeSave($insert)) {
          return false;
      }
      if($this->isNewRecord){
        $this->created_at = date('Y-m-d H:i:s', time());
      }
      $this->updated_at = date('Y-m-d H:i:s', time());
      return true;
  }

  public function displayValue(){
    $this->status = ($this->status === 1 ? "Active" : "Inactive");
  }
  
  /**
   * Get Translated Message
   */
  public function translate($field){
    return Yii::t($this->translate_category,$this->$field);
  }

}
