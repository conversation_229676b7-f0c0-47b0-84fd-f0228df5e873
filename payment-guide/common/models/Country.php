<?php

namespace common\models;



/**
 * This is the model class for table "country".
 *
 * @property string $country_code
 * @property string $name
 * @property string $default_currency
 * @property string $default_region
 * @property integer $status
 * @property string $updated_at
 * @property string $created_at
 *
 * @property Currency $defaultCurrency
 * @property Region $defaultRegion
 * @property GiftCardCountryRelationship[] $giftCardCountryRelationships
 */
class Country extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'country';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['country_code', 'name'], 'required'],
            [['default_region', 'status'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['country_code', 'default_currency'], 'string', 'max' => 5],
            [['name'], 'string', 'max' => 255],
            [['default_currency'], 'exist', 'skipOnError' => true, 'targetClass' => Currency::className(), 'targetAttribute' => ['default_currency' => 'currency_code']],
            [['default_region'], 'exist', 'skipOnError' => true, 'targetClass' => Region::className(), 'targetAttribute' => ['default_region' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'country_code' => 'Country Code',
            'name' => 'Name',
            'default_currency' => 'Default Currency',
            'default_region' => 'Default Region',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }

    public function displayValue(){
      parent::displayValue();
      $region = Region::findOne(['id' => $this->default_region]);
      $this->default_region = ($region ? $region->name : 'Not Found');
      return $this;
    }


  /**
   * To Get Default Currency
   * @return \yii\db\ActiveQuery
   */
    public function getDefaultCurrency()
    {
        return $this->hasOne(Currency::className(), ['currency_code' => 'default_currency']);
    }

    /**
     * To Get Default Region
     * @return \yii\db\ActiveQuery
     */
    public function getDefaultRegion()
    {
        return $this->hasOne(Region::className(), ['id' => 'default_region']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGiftCardCountryRelationships()
    {
        return $this->hasMany(GiftCardCountryRelationship::className(), ['country_code' => 'country_code']);
    }
}
