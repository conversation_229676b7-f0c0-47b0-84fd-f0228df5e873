<?php

namespace common\models;



/**
 * This is the model class for table "currency".
 *
 * @property string $currency_code
 * @property string $name
 * @property integer $status
 * @property string $updated_at
 * @property string $created_at
 *
 * @property Country[] $countries
 */
class Currency extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'currency';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['currency_code', 'name'], 'required'],
            [['status'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['currency_code'], 'string', 'max' => 5],
            [['name'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'currency_code' => 'Currency Code',
            'name' => 'Name',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }

    public function displayValue(){
      parent::displayValue();
      return $this;
    }

    /**
     * @return \yii\db\ActiveQuery
     * @return \yii\db\ActiveQuery
     */
    public function getCountries()
    {
        return $this->hasMany(Country::className(), ['default_currency' => 'currency_code']);
    }
}
