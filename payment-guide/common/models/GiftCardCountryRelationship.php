<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "gift_card_merchant".
 *
 * @property string $id
 * @property string $country_id
 * @property string $gift_card_merchant_id
 * @property string $created_at
 * @property string $updated_at
 */
class GiftCardCountryRelationship extends \yii\db\ActiveRecord
{
    const STATUS_DELETED = 0;
    const STATUS_ACTIVE = 1;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gift_card_country_relationship';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['gift_card_merchant_id'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['country_code'],'string']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'country_id' =>'Country ID',
            'gift_card_merchant_id' => 'Gift Card Merchant ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGiftCardMerchant()
    {
        return $this->hasOne(GiftCardMerchant::className(), ['id' => 'gift_card_merchant_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCountry()
    {
        return $this->hasOne(Country::className(), ['id' => 'country_id']);
    }
}
