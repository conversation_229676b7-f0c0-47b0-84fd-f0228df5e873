<?php

namespace common\models;


use yii\base\Model;
use yii\data\ActiveDataProvider;


/**
 * GiftCardRegionRelationshipSearch represents the model behind the search form about `common\models\GiftCardRegionRelationship`.
 */
class GiftCardCountryRelationshipSearch extends GiftCardCountryRelationship
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'gift_card_merchant_id'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['country_code'],'string']
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = GiftCardCountryRelationship::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 1000,
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'country_id' => $this->country_id,
            'gift_card_merchant_id' => $this->gift_card_merchant_id,
            'updated_at' => $this->updated_at,
            'created_at' => $this->created_at,
        ]);

        return $dataProvider;
    }
}
