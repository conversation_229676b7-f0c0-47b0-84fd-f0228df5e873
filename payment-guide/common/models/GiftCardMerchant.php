<?php

namespace common\models;

use Yii;
use dpodium\filemanager\components\FilemanagerHelper;

/**
 * This is the model class for table "gift_card_merchant".
 *
 * @property string $id
 * @property string $name
 * @property string $description
 * @property string $icon_url
 * @property integer $sub_merchant
 * @property integer $additional_button
 * @property string $status
 * @property string $created_at
 * @property string $updated_at
 */
class GiftCardMerchant extends BaseModel
{
    //Variables to be used to get translated content
    public $translate_col = array('name','description','additional_button','sub_merchant_description');
    public $translate_prefix = 'gift-card-merchant-';
    public $translate_category = 'gift-card-merchant';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gift_card_merchant';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'description', 'icon_url','is_sub_merchant'], 'required'],
            [['description', 'icon_url', 'additional_button'], 'string'],
            [['is_sub_merchant', 'status'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 30],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'description' => 'Description',
            'icon_url' => 'Icon Url',
            'is_sub_merchant' => 'Is Sub Merchant',
            'additional_button' => 'Additional Button',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Get Translated Content
     */
    public function displayValue(){
      parent::displayValue();
      $this->name = $this->translate('name');
      $this->description = $this->translate('description');
      if(!empty($this->additional_button)){
        $btn = json_decode($this->translate('additional_button'));
        $this->additional_button = $btn;
      }
      // $this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['img_src'];
      if(Yii::$app->id === 'app-backend'){
           $this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['backend_img_src'];
      }
      else{
          $this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['img_src'];
      }
      return $this;
    }

    /**
     * Get Countries which are available for a particular gift card
     */
    public function getCountryRelationship()
    {
        return $this->hasMany(GiftCardCountryRelationship::className(), ['gift_card_merchant_id' => 'id']);
    }
}
