<?php

namespace common\models;


use yii\base\Model;
use yii\data\ActiveDataProvider;


/**
 * GiftCardMerchantSearch represents the model behind the search form about `common\models\GiftCardMerchant`.
 */
class GiftCardMerchantSearch extends GiftCardMerchant
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'is_sub_merchant', 'additional_button', 'status'], 'integer'],
            [['name', 'description', 'icon_url', 'updated_at', 'created_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = GiftCardMerchant::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 1000,
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'is_sub_merchant' => $this->is_sub_merchant,
            'status' => $this->status,
            'updated_at' => $this->updated_at,
            'created_at' => $this->created_at,
        ]);

        $query->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'icon_url', $this->icon_url]);

        return $dataProvider;
    }
}
