<?php

namespace common\models;

use Yii;
use dpodium\filemanager\components\FilemanagerHelper;


/**
 * This is the model class for table "gift_card_sub_merchant".
 *
 * @property string $id
 * @property string $name
 * @property string $icon_url
 * @property string $status
 * @property string $created_at
 * @property string $updated_at
 * @property string $gift_card_merchant_id
 */
class GiftCardSubMerchant extends BaseModel
{
    //Variables to be used to get translated content
    public $translate_col = array('name');
    public $translate_prefix = 'gift-card-sub-merchant-';
    public $translate_category = 'gift-card-sub-merchant';
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gift_card_sub_merchant';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'icon_url', 'gift_card_merchant_id'], 'required'],
            [['icon_url'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['gift_card_merchant_id'], 'integer'],
            [['name'], 'string', 'max' => 30],
            [['status'], 'string', 'max' => 1],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'icon_url' => 'Icon Url',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'gift_card_merchant_id' => 'Gift Card Merchant',
        ];
    }

    public function displayValue(){
      parent::displayValue();
      $this->name = $this->translate('name');
      //$this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['img_src'];
      if(Yii::$app->id === 'app-backend'){
           $this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['backend_img_src'];
      }
      else{
          $this->icon_url = FilemanagerHelper::getFile($this->icon_url, 'file_identifier')['img_src'];
      }
      return $this;
    }
}
