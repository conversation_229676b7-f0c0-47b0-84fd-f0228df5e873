<?php

namespace common\models;



/**
 * This is the model class for table "language".
 *
 * @property string $iso_code
 * @property string $name
 * @property integer $status
 * @property string $updated_at
 * @property string $created_at
 *
 * @property UserGuide[] $userGuides
 */
class Language extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'language';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['iso_code', 'name'], 'required'],
            [['status'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['iso_code'], 'string', 'max' => 10],
            [['name'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'iso_code' => 'Iso Code',
            'name' => 'Name',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Get translated Content
     */
    public function displayValue(){
      parent::displayValue();
      return $this;
    }

    /**
   * Get a list of active Languages
   */
    public static function getActiveLanguage(){
      return Language::find()->where(['Status' => 1])->all();
    }

    /**
     * Get User Guide based on language
     * @return \yii\db\ActiveQuery
     */
    public function getUserGuides()
    {
        return $this->hasMany(UserGuide::className(), ['language_code' => 'iso_code']);
    }
}
