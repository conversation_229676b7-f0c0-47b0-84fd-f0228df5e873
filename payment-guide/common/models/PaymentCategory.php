<?php

namespace common\models;


/**
 * This is the model class for table "payment_category".
 *
 * @property integer $id
 * @property string $name
 * @property string $url_name
 * @property string $description
 * @property integer $order_no
 * @property string $status
 * @property string $created_at
 * @property string $updated_at
 * @property PaymentMethod[] $paymentMethods
 */
class PaymentCategory extends BaseModel
{
    public $translate_col = array('name','description');
    public $translate_prefix = 'payment-category-';
    public $translate_category = 'payment-category';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'payment_category';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'url_name', 'order_no'], 'required'],
            [['status', 'order_no'], 'integer'],
            [['url_name'], 'unique'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
     public function attributeLabels()
     {
         return [
             'id' => 'ID',
             'name' => 'Name',
             'url_name' => 'URL Name',
             'description' => 'Description',
             'order_no' => 'Sort Order',
             'status' => 'Status',
             'updated_at' => 'Updated At',
             'created_at' => 'Created At',
         ];
     }

     /**
      * Get translated content
      */
     public function displayValue(){
       parent::displayValue();
       $this->name = $this->translate('name');
       $this->description = $this->translate('description');
       return $this;
     }

     /**
      * Get Payment Method based on Payment Category
      * @return \yii\db\ActiveQuery
      */
    public function getPaymentMethods()
    {
        return $this->hasMany(PaymentMethod::className(), ['payment_category_id' => 'id']);
    }

    public function getPaymentCategoryNames($id = '')
    {
        $paymentCategory = PaymentCategory::findOne($id)->name;
        $sourceMessage = SourceMessage::find()->where(['category' => $this->translate_category, 'message' => $paymentCategory])->one();
        $message = Message::find()->where(['id' => $sourceMessage->id])->all();
        return $message;
    }
}
