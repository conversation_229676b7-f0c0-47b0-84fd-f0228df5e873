<?php

namespace common\models;


use Yii;

/**
 * This is the model class for table "payment_gateway".
 *
 * @property string $id
 * @property string $name
 * @property integer $status
 * @property string $updated_at
 * @property string $created_at
 *
 * @property PaymentMethod[] $paymentMethods
 */
class PaymentGateway extends BaseModel
{
    public $translate_col = array('name');
    public $translate_prefix = 'payment-gateway-';
    public $translate_category = 'payment-gateway';

    /**
     * @inheritdoc
     */

    public static function tableName()
    {
        return 'payment_gateway';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['status'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['name'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Get translated content
     */
    public function displayValue(){
      parent::displayValue();
      $this->name = $this->translate('name');
      return $this;
    }

    /**
     * Get Payment Method based on Payment Gateway
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentMethods()
    {
        return $this->hasMany(PaymentMethod::className(), ['payment_gateway_id' => 'id']);
    }
}
