<?php

namespace common\models;

use Yii;


use dpodium\filemanager\components\FilemanagerHelper;
use frontend\components\PipwaveApiManager;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/**
 * This is the model class for table "payment_method".
 *
 * @property string $id
 * @property string $name
 * @property string $description
 * @property string $processing_fee
 * @property string $processing_time
 * @property string $logo_url
 * @property integer $support_credit
 * @property integer $support_direct
 * @property string $additional_info
 * @property integer $order_no
 * @property integer $status
 * @property integer $is_global
 * @property integer $support_all_currency
 * @property string $updated_at
 * @property string $created_at
 * @property string $payment_gateway_id
 * @property string $payment_category_id
 * @property PaymentCategory $paymentCategory
 * @property PaymentGateway $paymentGateway
 * @property UserGuide[] $userGuides
 */
class PaymentMethod extends BaseModel
{
    public $translate_col = array('name', 'description', 'processing_fee', 'processing_time', 'additional_info', 'additional_button');
    public $translate_prefix = 'payment-method-';
    public $translate_category = 'payment-method';
    public $payment_category_name;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'payment_method';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'support_credit', 'support_direct', 'payment_category_id', 'payment_gateway_id', 'url_name', 'pipwave_payment_id'], 'required'],
            [['logo_url', 'additional_info', 'additional_button', 'url_name'], 'string'],
            [['support_credit', 'support_direct', 'order_no', 'status', 'payment_gateway_id', 'payment_category_id'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['url_name', 'pipwave_payment_id'], 'unique'],
            [['name', 'description'], 'string', 'max' => 255],
            [['processing_fee', 'processing_time'], 'string', 'max' => 50],
            [['payment_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => PaymentCategory::className(), 'targetAttribute' => ['payment_category_id' => 'id']],
            [['payment_gateway_id'], 'exist', 'skipOnError' => true, 'targetClass' => PaymentGateway::className(), 'targetAttribute' => ['payment_gateway_id' => 'id']],
            ['logo_url', 'required', 'when' => function ($model) {
                return $model->status == 1;
            }, 'whenClient' => "function (attribute, value) { return $('#paymentmethod-status').val() == 1; }"]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'description' => 'Description',
            'processing_fee' => 'Processing Fee',
            'processing_time' => 'Processing Time',
            'url_name' => 'Url Name',
            'logo_url' => 'Logo Url',
            'support_credit' => 'Support Store Credit Top-up',
            'support_direct' => 'Support Direct Checkout',
            'additional_info' => 'Additional Info',
            'additional_button' => 'Additional Button',
            'pipwave_payment_id' => 'Pipwave Payment ID',
            'order_no' => 'Sort Order',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
            'payment_gateway_id' => 'Payment Gateway',
            'payment_category_id' => 'Payment Category',
        ];
    }

    /**
     * Get Translated content
     */
    public function displayValue($display_externally = false)
    {
        parent::displayValue();
        if (!empty($this->name)) {
            $this->name = $this->translate('name');
        }
        if (!empty($this->description)) {
            $this->description = $this->translate('description');
        }
        if (!empty($this->processing_fee)) {
            $this->processing_fee = $this->translate('processing_fee');
        }
        if (!empty($this->processing_time)) {
            $this->processing_time = $this->translate('processing_time');
        }
        if (!empty($this->additional_info)) {
            $info = json_decode($this->translate('additional_info'));
            $this->additional_info = $info;
        }
        if (!empty($this->additional_button)) {
            $btn = json_decode($this->translate('additional_button'));
            $this->additional_button = $btn;
        }
        if (!empty($this->payment_category_id)) {
            if ($display_externally) {
                $this->payment_category_id = PaymentCategory::findOne($this->payment_category_id)->displayValue()->url_name;
            } else {
                $this->payment_category_id = PaymentCategory::findOne($this->payment_category_id)->displayValue()->name;
            }
        }
        if (!empty($this->payment_gateway_id)) {
            $this->payment_gateway_id = PaymentGateway::findOne($this->payment_gateway_id)->displayValue()->name;
        }

        if (Yii::$app->id === 'app-backend') {
            $this->logo_url = FilemanagerHelper::getFile($this->logo_url, 'file_identifier')['backend_img_src'];
        } else {
            $this->logo_url = FilemanagerHelper::getFile($this->logo_url, 'file_identifier')['img_src'];
        }

        // Build URL for call
        if ($display_externally) {
            if (!empty($this->url_name) && !empty($this->payment_category_id)) {
                $this->url_name = Url::base(true) . '/pg/' . $this->payment_category_id . '/' . $this->url_name;
            }
        }
        return $this;
    }

    /**
     * @return \yii\db\ActiveQuery
     * Get Payment Category
     */
    public function getPaymentCategory()
    {
        return $this->hasOne(PaymentCategory::className(), ['id' => 'payment_category_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     * Get Payment Gateway
     */
    public function getPaymentGateway()
    {
        return $this->hasOne(PaymentGateway::className(), ['id' => 'payment_gateway_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     * Get User Guide Based in Payment Method
     */
    public function getUserGuides()
    {
        return $this->hasMany(UserGuide::className(), ['payment_method_id' => 'id']);
    }

    public function checkLogoUrl($attributes, $params)
    {
        if ($this->status === 1) {
            if (!$this->validate_logo_url($this->logo_url)) {
                $this->addError('logo_url', 'Empty logo');
            }
        }
    }

    public static function getPaymentMethodsUrlAndImage($country, $currency)
    {
        $filterData = PipwaveApiManager::getFilterData($country, $currency);
        $model = PaymentMethod::find()->select('name, logo_url, url_name, payment_category_id, status')->where(array('id' => $filterData))->andWhere(['status' => 1])->all();

        $category = new PaymentCategory();
        foreach ($model as $data) {
            $data->payment_category_name = $category->getPaymentCategoryNames($data->payment_category_id);
            $data->displayValue(true);
        }

        return ArrayHelper::toArray($model);
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields['payment_category_name'] = 'payment_category_name';
        return $fields;
    }
}