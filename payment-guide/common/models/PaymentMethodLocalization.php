<?php

namespace common\models;



/**
 * This is the model class for table "payment_method_localization".
 *
 * @property string $currency_code
 * @property string $country_code
 * @property string $payment_method_id
 *
 * @property PaymentMethod $paymentMethod
 */
class PaymentMethodLocalization extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'payment_method_localization';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['currency_code', 'country_code', 'payment_method_id'], 'required'],
            [['payment_method_id'], 'integer'],
            [['currency_code', 'country_code'], 'string', 'max' => 5],
            [['payment_method_id'], 'exist', 'skipOnError' => true, 'targetClass' => PaymentMethod::className(), 'targetAttribute' => ['payment_method_id' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'currency_code' => 'Currency Code',
            'country_code' => 'Country Code',
            'payment_method_id' => 'Payment Method ID',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentMethod()
    {
        return $this->hasOne(PaymentMethod::className(), ['id' => 'payment_method_id']);
    }
}
