<?php

namespace common\models;


use yii\base\Model;
use yii\data\ActiveDataProvider;


/**
 * PaymentMethodSearch represents the model behind the search form about `common\models\PaymentMethod`.
 */
class PaymentMethodSearch extends PaymentMethod
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'support_credit', 'support_direct', 'order_no', 'status', 'payment_gateway_id', 'payment_category_id'], 'integer'],
            [['name', 'description', 'processing_fee', 'processing_time', 'logo_url', 'additional_info', 'updated_at', 'created_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = PaymentMethod::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 1000,
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'support_credit' => $this->support_credit,
            'support_direct' => $this->support_direct,
            'order_no' => $this->order_no,
            'status' => $this->status,
            'updated_at' => $this->updated_at,
            'created_at' => $this->created_at,
            'payment_gateway_id' => $this->payment_gateway_id,
            'payment_category_id' => $this->payment_category_id,
        ]);

        $query->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'url_name', $this->url_name])
            ->andFilterWhere(['like', 'pipwave_payment_id', $this->pipwave_payment_id])
            ->andFilterWhere(['like', 'processing_fee', $this->processing_fee])
            ->andFilterWhere(['like', 'processing_time', $this->processing_time])
            ->andFilterWhere(['like', 'logo_url', $this->logo_url])
            ->andFilterWhere(['like', 'additional_info', $this->additional_info]);

        return $dataProvider;
    }
}
