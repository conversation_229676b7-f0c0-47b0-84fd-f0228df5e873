<?php

namespace common\models;



/**
 * This is the model class for table "pipwave_request".
 *
 * @property string $currency_code
 * @property string $country_code
 * @property string $results
 * @property string $expiry
 */
class PipwaveRequest extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'pipwave_request';
    }

    public static function primaryKey(){
       return array('currency_code', 'country_code');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['currency_code', 'country_code', 'expiry'], 'required'],
            [['results'], 'string'],
            [['expiry'], 'integer'],
            [['currency_code', 'country_code'], 'string', 'max' => 5],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'currency_code' => 'Currency Code',
            'country_code' => 'Country Code',
            'results' => 'Results',
            'expiry' => 'Expiry',
        ];
    }
}
