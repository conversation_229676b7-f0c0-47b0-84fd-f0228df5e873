<?php

namespace common\models;



/**
 * This is the model class for table "pipwave_request_log".
 *
 * @property string $id
 * @property string $currency_code
 * @property string $country_code
 * @property string $results
 * @property string $created_at
 */
class PipwaveRequestLog extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'pipwave_request_log';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['results','error_code'], 'string'],
            [['created_at'], 'integer'],
            [['currency_code', 'country_code'], 'string', 'max' => 5],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'error_code' => 'Error Code',
            'currency_code' => 'Currency Code',
            'country_code' => 'Country Code',
            'results' => 'Results',
            'created_at' => 'Created At',
        ];
    }
}
