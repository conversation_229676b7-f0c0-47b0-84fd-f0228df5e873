<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "region".
 *
 * @property string $id
 * @property string $name
 * @property integer $status
 * @property string $updated_at
 * @property string $created_at
 *
 * @property GiftCardRegionRelationship[] $giftCardRegionRelationships
 */
class Region extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'region';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['status'], 'integer'],
            [['updated_at', 'created_at'], 'safe'],
            [['name'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' =>'Name',
            'status' => 'Status',
            'updated_at' => 'Updated At',
            'created_at' => 'Created At',
        ];
    }

    public function displayValue(){
      parent::displayValue();
      return $this;
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGiftCardRegionRelationships()
    {
        return $this->hasMany(GiftCardRegionRelationship::className(), ['region_id' => 'id']);
    }

}
