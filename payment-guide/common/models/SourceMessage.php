<?php

namespace common\models;

use Yii;
/**
 * This is the model class for table "source_message".
 *
 * @property integer $id
 * @property string $category
 * @property text $message
 */
class SourceMessage extends \yii\db\ActiveRecord
{

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'source_message';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['category'], 'string'],
            [['message'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'category' => 'Category',
            'message' => 'Message',
        ];
    }

    public function displayValue(){
      $this->message = Yii::t('app',$this->message);
      return $this;
    }
    
    /**
     * Function to get Translated message for multi language
     */
    public function getTranslatedMessage(){
      $message = Message::find()->where(['id'=>$this->id])->all();
      $string = '';
      foreach($message as $m){
          $string .= $m->language . " => " . $m->translation ."<br>";
      }
      return $string;
    }

    public function getMessageRelationship()
    {
        return $this->hasMany(Message::className(), ['id' => 'id']);
    }
}
