<?php

namespace common\models;

use Yii;


/**
 * This is the model class for table "user_guide".
 *
 * @property string $id
 * @property string $name
 * @property string $content
 * @property string $status
 * @property string $created_at
 * @property string $updated_at
 * @property string $payment_method_id
 * @property string $language_code
 *
 * @property Language $language
 * @property PaymentMethod $paymentMethod
 */
class UserGuide extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_guide';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'content', 'payment_method_id', 'language_code'], 'required'],
            [['content','meta_description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['payment_method_id', 'status'], 'integer'],
            [['name', 'language_code'], 'string', 'max' => 255],
            [['language_code'], 'exist', 'skipOnError' => true, 'targetClass' => Language::className(), 'targetAttribute' => ['language_code' => 'iso_code']],
            [['payment_method_id'], 'exist', 'skipOnError' => true, 'targetClass' => PaymentMethod::className(), 'targetAttribute' => ['payment_method_id' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'content' => 'Content',
            'meta_description' => 'Meta Description',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'payment_method_id' => 'Payment Method',
            'language_code' => 'Language',
        ];
    }

    public function displayValue(){
      parent::displayValue();
      $payment_method = PaymentMethod::findOne($this->payment_method_id)->displayValue();
      $this->payment_method_id = array('name'=> $payment_method->name, 'id'=> $payment_method->id);
      $this->language_code = Language::findOne($this->language_code)->name;
      return $this;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getLanguage()
    {
        return $this->hasOne(Language::className(), ['id' => 'language_code']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentMethod()
    {
        return $this->hasOne(PaymentMethod::className(), ['id' => 'payment_method_id']);
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getUserGuideVisitors()
    {
        return $this->hasMany(UserGuideVisitors::className(), ['user_guide_id' => 'id'])->one();
        
    }

}
