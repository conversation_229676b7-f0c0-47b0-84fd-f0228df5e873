<?php

namespace common\models;

use Yii;


/**
 * This is the model class for table "user_guide".
 *
 * @property string $id
 * @property string $visit_count
 * @property string $status
 * @property string $created_at
 * @property string $updated_at
 * @property string $user_guide_id
 *
 * @property UserGuide $UserGuide
 */
class UserGuideVisitors extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_guide_visitors';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['visit_count', 'user_guide_id'], 'required'],
            [['visit_count', 'user_guide_id', 'status'], 'integer'],
            [['user_guide_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserGuide::className(), 'targetAttribute' => ['user_guide_id' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'visit_count' => 'Visit Count',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'user_guide_id' => 'User Guide',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getUserGuide()
    {
        return $this->hasOne(UserGuide::className(), ['id' => 'user_guide_id']);
    }
}
