{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=5.4.0", "yiisoft/yii2": "~2.0.6", "yiisoft/yii2-bootstrap": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.0.0", "dpodium/yii2-filemanager": "dev-master", "kartik-v/yii2-grid": "dev-master", "dmstr/yii2-adminlte-asset": "2.*", "dpodium/yii2-geoip": "*", "fedemotta/yii2-widget-datatables": "*", "aws/aws-sdk-php": "^3.36", "tinymce/tinymce": ">=4", "bower-asset/google-code-prettify": "^1.0", "istvan-ujjmeszaros/bootstrap-duallistbox": "^3.0", "himiklab/yii2-recaptcha-widget": "*", "kartik-v/yii2-widget-select2": "@dev"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0", "yiisoft/yii2-gii": "~2.0.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/base": "^2.2.3", "codeception/verify": "~0.3.1"}, "config": {"fxp-asset": {"installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "process-timeout": 1800}, "extra": {}, "scripts": {"post-install-cmd": "php init --env=Development --overwrite=n"}}