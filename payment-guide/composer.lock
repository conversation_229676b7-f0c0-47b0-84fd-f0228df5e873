{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "content-hash": "6e363b6b5de5c5daa73725453873f5ae", "packages": [{"name": "almasaeed2010/adminlte", "version": "v2.3.11", "source": {"type": "git", "url": "https://github.com/almasaeed2010/AdminLTE.git", "reference": "2be703222af2edcb87e562d3da2299e4352bff8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/almasaeed2010/AdminLTE/zipball/2be703222af2edcb87e562d3da2299e4352bff8a", "reference": "2be703222af2edcb87e562d3da2299e4352bff8a", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "AdminLTE - admin control panel and dashboard that's based on Bootstrap 3", "homepage": "http://almsaeedstudio.com/", "keywords": ["JS", "admin", "back-end", "css", "less", "responsive", "template", "theme", "web"], "time": "2017-01-08T21:03:57+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.36.6", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "a7cd486139971d66387a4938f5a0a2a95e50d678"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/a7cd486139971d66387a4938f5a0a2a95e50d678", "reference": "a7cd486139971d66387a4938f5a0a2a95e50d678", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "guzzlehttp/guzzle": "^5.3.1|^6.2.1", "guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "^1.4.1", "mtdowling/jmespath.php": "~2.2", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "nette/neon": "^2.3", "phpunit/phpunit": "^4.8.35|^5.4.0", "psr/cache": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2017-09-12T17:09:10+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.3.7", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "0b9c4a4007c44201dce9a6cc1a38407005c26c86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/0b9c4a4007c44201dce9a6cc1a38407005c26c86", "reference": "0b9c4a4007c44201dce9a6cc1a38407005c26c86", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["less/bootstrap.less", "dist/js/bootstrap.js"], "bower-asset-ignore": ["/.*", "_config.yml", "CNAME", "composer.json", "CONTRIBUTING.md", "docs", "js/tests", "test-infra"]}, "license": ["MIT"], "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "keywords": ["css", "framework", "front-end", "js", "less", "mobile-first", "responsive", "web"]}, {"name": "bower-asset/bootstrap3-dialog", "version": "v1.35.4", "source": {"type": "git", "url": "https://github.com/nakupanda/bootstrap3-dialog.git", "reference": "3dd11d586f78de75356af418907ec6e3b347377c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nakupanda/bootstrap3-dialog/zipball/3dd11d586f78de75356af418907ec6e3b347377c", "reference": "3dd11d586f78de75356af418907ec6e3b347377c", "shasum": ""}, "require": {"bower-asset/bootstrap": ">=3.1.0", "bower-asset/jquery": ">=1.9.0"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["dist/less/bootstrap-dialog.less", "dist/css/bootstrap-dialog.min.css", "dist/js/bootstrap-dialog.min.js"], "bower-asset-ignore": ["source", "spec", ".bower<PERSON>", ".giti<PERSON>re", ".jshintignore", ".j<PERSON>trc", "bower.json", "gruntfile.js", "package.json", "README.md"]}, "license": ["MIT"], "description": "Make use of Bootstrap Modal more monkey-friendly. http://nakupanda.github.io/bootstrap3-dialog/", "keywords": ["css", "framework", "front-end", "js", "less", "mobile-first", "responsive", "web"]}, {"name": "bower-asset/datatables", "version": "1.10.16", "source": {"type": "git", "url": "https://github.com/DataTables/DataTables.git", "reference": "75a665f64f02982c0f4666b15a25c4670e5e6b18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DataTables/DataTables/zipball/75a665f64f02982c0f4666b15a25c4670e5e6b18", "reference": "75a665f64f02982c0f4666b15a25c4670e5e6b18", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.7.0"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["media/js/jquery.dataTables.js", "media/css/jquery.dataTables.css", "media/images/sort_asc.png", "media/images/sort_asc_disabled.png", "media/images/sort_both.png", "media/images/sort_desc.png", "media/images/sort_desc_disabled.png"], "bower-asset-ignore": ["/.*", "examples", "media/unit_testing", "composer.json", "dataTables.jquery.json", "package.json"]}, "license": ["MIT"], "keywords": ["datatables", "javascript", "j<PERSON>y", "library", "table"]}, {"name": "bower-asset/datatables-bootstrap3", "version": "0.1", "source": {"type": "git", "url": "https://github.com/Jowin/Datatables-Bootstrap3.git", "reference": "06d78a732dfd1da928ff118dd26249ae94021495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jowin/Datatables-Bootstrap3/zipball/06d78a732dfd1da928ff118dd26249ae94021495", "reference": "06d78a732dfd1da928ff118dd26249ae94021495", "shasum": ""}, "type": "bower-asset-library"}, {"name": "bower-asset/datatables-tabletools", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/DataTables/TableTools.git", "reference": "5fc9d457b673fe3f2d84f32660ee74e29ce6f84e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DataTables/TableTools/zipball/5fc9d457b673fe3f2d84f32660ee74e29ce6f84e", "reference": "5fc9d457b673fe3f2d84f32660ee74e29ce6f84e", "shasum": ""}, "require": {"bower-asset/datatables": ">=1.9.0", "bower-asset/jquery": ">=1.7.0"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["js/dataTables.tableTools.js", "css/dataTables.tableTools.css"]}}, {"name": "bower-asset/google-code-prettify", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/spencewood/google-code-prettify.git", "reference": "5e0b27bba2583a386c08ead942bbf25418c28784"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spencewood/google-code-prettify/zipball/5e0b27bba2583a386c08ead942bbf25418c28784", "reference": "5e0b27bba2583a386c08ead942bbf25418c28784", "shasum": ""}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["./bin/prettify.min.css", "./bin/prettify.min.js"], "bower-asset-ignore": ["closure-compiler", "js-modules", "tests", "yui-compressor", "<PERSON><PERSON><PERSON>"]}}, {"name": "bower-asset/jquery", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/c0185ab7c75aab88762c5aae780b9d83b80eda72", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72", "shasum": ""}, "type": "bower-asset-library", "extra": {"bower-asset-main": "dist/jquery.js", "bower-asset-ignore": ["package.json"]}, "license": ["MIT"], "keywords": ["browser", "javascript", "j<PERSON>y", "library"]}, {"name": "bower-asset/jquery.inputmask", "version": "3.3.8", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "791d84990c4a98df1597e9d155be53a3725805dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/791d84990c4a98df1597e9d155be53a3725805dd", "reference": "791d84990c4a98df1597e9d155be53a3725805dd", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["./dist/inputmask/inputmask.js", "./dist/inputmask/inputmask.extensions.js", "./dist/inputmask/inputmask.date.extensions.js", "./dist/inputmask/inputmask.numeric.extensions.js", "./dist/inputmask/inputmask.phone.extensions.js", "./dist/inputmask/jquery.inputmask.js", "./dist/inputmask/global/document.js", "./dist/inputmask/global/window.js", "./dist/inputmask/phone-codes/phone.js", "./dist/inputmask/phone-codes/phone-be.js", "./dist/inputmask/phone-codes/phone-nl.js", "./dist/inputmask/phone-codes/phone-ru.js", "./dist/inputmask/phone-codes/phone-uk.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jqlite.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jquery.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.js", "./dist/inputmask/bindings/inputmask.binding.js"], "bower-asset-ignore": ["**/*", "!dist/*", "!dist/inputmask/*", "!dist/min/*", "!dist/min/inputmask/*"]}, "license": ["http://opensource.org/licenses/mit-license.php"], "description": "Inputmask is a javascript library which creates an input mask.  Inputmask can run against vanilla javascript, jQuery and jqlite.", "keywords": ["form", "input", "inputmask", "j<PERSON>y", "mask", "plugins"]}, {"name": "bower-asset/punycode", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bestiejs/punycode.js/zipball/38c8d3131a82567bfef18da09f7f4db68c84f8a3", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3", "shasum": ""}, "type": "bower-asset-library", "extra": {"bower-asset-main": "punycode.js", "bower-asset-ignore": ["coverage", "tests", ".*", "component.json", "Gruntfile.js", "node_modules", "package.json"]}}, {"name": "bower-asset/yii2-pjax", "version": "v2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/jquery-pjax.git", "reference": "60728da6ade5879e807a49ce59ef9a72039b8978"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/60728da6ade5879e807a49ce59ef9a72039b8978", "reference": "60728da6ade5879e807a49ce59ef9a72039b8978", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset-library", "extra": {"bower-asset-main": "./jquery.pjax.js", "bower-asset-ignore": [".travis.yml", "Gem<PERSON>le", "Gemfile.lock", "CONTRIBUTING.md", "vendor/", "script/", "test/"]}, "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "25b28bae8a6f185b5030673af77b32e1163d5c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/25b28bae8a6f185b5030673af77b32e1163d5c6e", "reference": "25b28bae8a6f185b5030673af77b32e1163d5c6e", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2017-07-16T21:13:23+00:00"}, {"name": "cebe/yii2-gravatar", "version": "1.1", "target-dir": "cebe/gravatar", "source": {"type": "git", "url": "https://github.com/cebe/yii2-gravatar.git", "reference": "c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/yii2-gravatar/zipball/c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057", "reference": "c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-0": {"cebe\\gravatar\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}], "description": "<PERSON><PERSON><PERSON><PERSON> Widget for <PERSON><PERSON> 2", "keywords": ["gravatar", "yii"], "time": "2013-12-10T17:49:58+00:00"}, {"name": "dmstr/yii2-adminlte-asset", "version": "2.4.3", "source": {"type": "git", "url": "https://github.com/dmstr/yii2-adminlte-asset.git", "reference": "2cff40e951c47d02667617833465e09abdac45da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dmstr/yii2-adminlte-asset/zipball/2cff40e951c47d02667617833465e09abdac45da", "reference": "2cff40e951c47d02667617833465e09abdac45da", "shasum": ""}, "require": {"almasaeed2010/adminlte": "~2.0", "cebe/yii2-gravatar": "1.*", "rmrevin/yii2-fontawesome": "~2.9", "yiisoft/yii2": "2.*", "yiisoft/yii2-bootstrap": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"dmstr\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "AdminLTE backend theme asset bundle for Yii 2.0 Framework", "keywords": ["AdminLTE", "admin", "asset", "backend", "css", "extension", "less", "theme", "yii2"], "time": "2017-08-31T16:21:44+00:00"}, {"name": "dpodium/yii2-filemanager", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/dpodium/yii2-filemanager.git", "reference": "83a24944aaef6c6ea068399c304ce8cdcd8f9173"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dpodium/yii2-filemanager/zipball/83a24944aaef6c6ea068399c304ce8cdcd8f9173", "reference": "83a24944aaef6c6ea068399c304ce8cdcd8f9173", "shasum": ""}, "require": {"aws/aws-sdk-php": "3.*", "fortawesome/font-awesome": "*", "kartik-v/yii2-editable": "*", "kartik-v/yii2-grid": "dev-master", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widgets": "*", "php": ">=5.4.0", "yiisoft/yii2": "2.0.*", "yiisoft/yii2-imagine": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"dpodium\\filemanager\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "June See", "email": "<EMAIL>", "homepage": "http://www.dpodium.com"}], "description": "A file manager for Yii2. Allow user to manage files from any location as well as browsing files within the application.", "keywords": ["browse", "file", "filemanager", "manager", "widget", "yii2"], "time": "2017-09-12 11:28:49"}, {"name": "dpodium/yii2-geoip", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/dpodium/yii2-geoip.git", "reference": "f4ef56259676177125a7e1876a0d4c31888354df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dpodium/yii2-geoip/zipball/f4ef56259676177125a7e1876a0d4c31888354df", "reference": "f4ef56259676177125a7e1876a0d4c31888354df", "shasum": ""}, "require": {"yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "autoload": {"psr-4": {"dpodium\\yii2\\geoip\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sionghuat Chng", "email": "<EMAIL>", "homepage": "http://www.dpodium.com"}], "description": "Yii2 Component to allow for easy usage of the MaxMind Free dbs.", "keywords": ["extension", "geoip", "maxmind", "yii2"], "time": "2017-07-04T02:41:34+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.9.3", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "95e1bae3182efc0f3422896a3236e991049dac69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/95e1bae3182efc0f3422896a3236e991049dac69", "reference": "95e1bae3182efc0f3422896a3236e991049dac69", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "^1.1"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2017-06-03T02:28:16+00:00"}, {"name": "fedemotta/yii2-widget-datatables", "version": "v1.3", "source": {"type": "git", "url": "https://github.com/fedemotta/yii2-widget-datatables.git", "reference": "ee0c70a038302ca414fe01d51732ed217efdc772"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fedemotta/yii2-widget-datatables/zipball/ee0c70a038302ca414fe01d51732ed217efdc772", "reference": "ee0c70a038302ca414fe01d51732ed217efdc772", "shasum": ""}, "require": {"bower-asset/datatables": ">= 1.9.4", "bower-asset/datatables-bootstrap3": "*", "bower-asset/datatables-tabletools": "*", "bower-asset/jquery": ">= 1.7.0", "yiisoft/yii2": "*"}, "type": "yii2-extension", "extra": {"asset-installer-paths": {"bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"fedemotta\\datatables\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DataTables widget for Yii2", "keywords": ["datatables", "extension", "javascript", "j<PERSON>y", "library", "table", "yii2"], "time": "2015-06-08T16:07:23+00:00"}, {"name": "fortawesome/font-awesome", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/a8386aae19e200ddb0f6845b5feeee5eb7013687", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687", "shasum": ""}, "require-dev": {"jekyll": "1.0.2", "lessc": "1.4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.6.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OFL-1.1", "MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://twitter.com/davegandy", "role": "Developer"}], "description": "The iconic font and CSS framework", "homepage": "http://fontawesome.io/", "keywords": ["FontAwesome", "awesome", "bootstrap", "font", "icon"], "time": "2016-10-24T15:52:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4db5a78a5ea468d4831de7f0bf9d9415e348699", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2017-06-22T18:50:49+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"], "time": "2017-03-20T17:10:46+00:00"}, {"name": "himiklab/yii2-recaptcha-widget", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/himiklab/yii2-recaptcha-widget.git", "reference": "6c7c6c41a595f8fdcc5ec54e5f09a332ef0358cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/himiklab/yii2-recaptcha-widget/zipball/6c7c6c41a595f8fdcc5ec54e5f09a332ef0358cc", "reference": "6c7c6c41a595f8fdcc5ec54e5f09a332ef0358cc", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "require-dev": {"phpunit/dbunit": ">=1.2", "phpunit/phpunit": ">=3.7", "phpunit/phpunit-selenium": ">=1.2"}, "type": "yii2-extension", "autoload": {"psr-4": {"himiklab\\yii2\\recaptcha\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/himiklab/"}], "description": "Yii2 Google reCAPTCHA widget", "keywords": ["<PERSON><PERSON>a", "google", "recaptcha", "widget", "yii2"], "time": "2017-01-10T15:46:29+00:00"}, {"name": "imagine/imagine", "version": "v0.6.3", "source": {"type": "git", "url": "https://github.com/avalanche123/Imagine.git", "reference": "149041d2a1b517107bfe270ca2b1a17aa341715d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/avalanche123/Imagine/zipball/149041d2a1b517107bfe270ca2b1a17aa341715d", "reference": "149041d2a1b517107bfe270ca2b1a17aa341715d", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"sami/sami": "dev-master"}, "suggest": {"ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-0": {"Imagine": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "time": "2015-09-19T16:54:05+00:00"}, {"name": "istvan-ujjmeszaros/bootstrap-duallistbox", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/istvan-ujjmeszaros/bootstrap-duallistbox.git", "reference": "4ebd0727b27425b96204cb0dde1d8ce564676403"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/istvan-ujjmeszaros/bootstrap-duallistbox/zipball/4ebd0727b27425b96204cb0dde1d8ce564676403", "reference": "4ebd0727b27425b96204cb0dde1d8ce564676403", "shasum": ""}, "suggest": {"components/jquery": "2.0.*", "twbs/bootstrap": "3.*"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/istvan-ujjmeszaros"}], "description": "A responsive dual listbox widget optimized for Twitter Bootstrap. It works on all modern browsers and on touch devices.", "homepage": "http://www.virtuosoft.eu/code/bootstrap-duallistbox/", "keywords": ["bootstrap", "bootstrap duallistbox", "bootstrap select", "duallistbox", "select"], "time": "2016-09-06T13:18:25+00:00"}, {"name": "kartik-v/bootstrap-fileinput", "version": "v4.4.3", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git", "reference": "e35391eb28e6c8d2f280d5d2b025437733058131"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-fileinput/zipball/e35391eb28e6c8d2f280d5d2b025437733058131", "reference": "e35391eb28e6c8d2f280d5d2b025437733058131", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\fileinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced HTML 5 file input for Bootstrap 3.x with features for file preview for many file types, multiple selection, ajax uploads, and more.", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "keywords": ["ajax", "bootstrap", "delete", "file", "image", "input", "j<PERSON>y", "multiple", "preview", "progress", "upload"], "time": "2017-08-27T18:51:18+00:00"}, {"name": "kartik-v/bootstrap-popover-x", "version": "v1.4.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-popover-x.git", "reference": "16704c0a6a7d2960b9a35dfc03b54211d642e43c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-popover-x/zipball/16704c0a6a7d2960b9a35dfc03b54211d642e43c", "reference": "16704c0a6a7d2960b9a35dfc03b54211d642e43c", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Bootstrap Popover Extended - Popover with modal behavior, styling enhancements and more.", "homepage": "https://github.com/kartik-v/bootstrap-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "time": "2017-09-07T13:05:26+00:00"}, {"name": "kartik-v/bootstrap-star-rating", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-star-rating.git", "reference": "599c10e2456bc2215da7c6337d6b1c65892bff72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-star-rating/zipball/599c10e2456bc2215da7c6337d6b1c65892bff72", "reference": "599c10e2456bc2215da7c6337d6b1c65892bff72", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A simple yet powerful JQuery star rating plugin for Bootstrap.", "homepage": "https://github.com/kartik-v/bootstrap-star-rating", "keywords": ["Rating", "awesome", "bootstrap", "font", "glyphicon", "star", "svg"], "time": "2017-08-27T08:23:59+00:00"}, {"name": "kartik-v/dependent-dropdown", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/dependent-dropdown.git", "reference": "1349e7f5816a65e89bc227098386aac5b2c89363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/dependent-dropdown/zipball/1349e7f5816a65e89bc227098386aac5b2c89363", "reference": "1349e7f5816a65e89bc227098386aac5b2c89363", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A multi level dependent dropdown JQuery plugin that allows nested dependencies.", "homepage": "https://github.com/kartik-v/dependent-dropdown", "keywords": ["dependent", "dropdown", "j<PERSON>y", "option", "select"], "time": "2017-08-01T11:43:55+00:00"}, {"name": "kartik-v/yii2-dialog", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dialog.git", "reference": "4aaf8918c6dbd90218b6ad9036b1aae211480716"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dialog/zipball/4aaf8918c6dbd90218b6ad9036b1aae211480716", "reference": "4aaf8918c6dbd90218b6ad9036b1aae211480716", "shasum": ""}, "require": {"bower-asset/bootstrap3-dialog": "~1.34", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\dialog\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An asset bundle for bootstrap3-dialog for Yii 2.0 framework.", "homepage": "https://github.com/kartik-v/yii2-dialog", "keywords": ["alert", "bootstrap", "dialog", "extension", "modal", "yii2"], "time": "2016-09-13T18:15:26+00:00"}, {"name": "kartik-v/yii2-editable", "version": "v1.7.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-editable.git", "reference": "e07d78660e1d74cf831824855c46742d7ff784da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-editable/zipball/e07d78660e1d74cf831824855c46742d7ff784da", "reference": "e07d78660e1d74cf831824855c46742d7ff784da", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.7", "kartik-v/yii2-popover-x": "~1.3"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"kartik\\editable\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced editable widget for Yii 2.0 that allows easy editing of displayed data with numerous configuration possibilities.", "homepage": "https://github.com/kartik-v/yii2-editable", "keywords": ["bootstrap", "editable", "input", "j<PERSON>y", "popover", "popover-x", "widget"], "time": "2017-06-28T05:11:45+00:00"}, {"name": "kartik-v/yii2-grid", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-grid.git", "reference": "dc04339fb3fd66af283ca63f5dcb6da085a6a333"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-grid/zipball/dc04339fb3fd66af283ca63f5dcb6da085a6a333", "reference": "dc04339fb3fd66af283ca63f5dcb6da085a6a333", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": "~1.7"}, "suggest": {"kartik-v/yii2-mpdf": "For exporting grids to PDF"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\grid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Yii 2 GridView on steroids. Various enhancements and utilities for the Yii 2.0 GridView widget.", "homepage": "https://github.com/kartik-v/yii2-grid", "keywords": ["extension", "grid", "widget", "yii2"], "time": "2017-09-01T12:21:36+00:00"}, {"name": "kartik-v/yii2-krajee-base", "version": "v1.8.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-krajee-base.git", "reference": "2479241c03c87995cfc528ae7b297f5ae9e733cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-krajee-base/zipball/2479241c03c87995cfc528ae7b297f5ae9e733cb", "reference": "2479241c03c87995cfc528ae7b297f5ae9e733cb", "shasum": ""}, "require": {"yiisoft/yii2-bootstrap": "@dev"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\base\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Base library and foundation components for all Yii2 Krajee extensions.", "homepage": "https://github.com/kartik-v/yii2-krajee-base", "keywords": ["base", "extension", "foundation", "krajee", "widget", "yii2"], "time": "2017-02-22T05:58:53+00:00"}, {"name": "kartik-v/yii2-popover-x", "version": "v1.3.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-popover-x.git", "reference": "58837d63e65caa41f0c13e671d1e2abeec17887d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-popover-x/zipball/58837d63e65caa41f0c13e671d1e2abeec17887d", "reference": "58837d63e65caa41f0c13e671d1e2abeec17887d", "shasum": ""}, "require": {"kartik-v/bootstrap-popover-x": "~1.4", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.0 popover widget which combines both the bootstrap popover and modal features and includes various new styling enhancements.", "homepage": "https://github.com/kartik-v/yii2-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "time": "2017-09-08T04:30:42+00:00"}, {"name": "kartik-v/yii2-widget-activeform", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-activeform.git", "reference": "53c2f877f12ba0b79e8346b6cae50cbba2bcfc69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-activeform/zipball/53c2f877f12ba0b79e8346b6cae50cbba2bcfc69", "reference": "53c2f877f12ba0b79e8346b6cae50cbba2bcfc69", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\form\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 active-form and active-field with full bootstrap styling support (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-activeform", "keywords": ["activefield", "activeform", "extension", "field", "form", "widget", "yii2"], "time": "2016-04-27T18:38:05+00:00"}, {"name": "kartik-v/yii2-widget-affix", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-affix.git", "reference": "2184119bfa518c285406156f744769b13b861712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-affix/zipball/2184119bfa518c285406156f744769b13b861712", "reference": "2184119bfa518c285406156f744769b13b861712", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\affix\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A scrollspy and affixed enhanced navigation to highlight page sections (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-affix", "keywords": ["affix", "bootstrap", "extension", "j<PERSON>y", "navigation", "plugin", "scrollspy", "widget", "yii2"], "time": "2014-11-09T04:56:27+00:00"}, {"name": "kartik-v/yii2-widget-alert", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-alert.git", "reference": "5b312eeaf429c2affe6a96217d79f0c761159643"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-alert/zipball/5b312eeaf429c2affe6a96217d79f0c761159643", "reference": "5b312eeaf429c2affe6a96217d79f0c761159643", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\alert\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate alert based notifications using bootstrap-alert plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-alert", "keywords": ["alert", "block", "bootstrap", "extension", "flash", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "time": "2014-11-19T06:44:12+00:00"}, {"name": "kartik-v/yii2-widget-colorinput", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-colorinput.git", "reference": "3f6e847ef72cf6e27e4d3b4870b00b8f80d51752"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-colorinput/zipball/3f6e847ef72cf6e27e4d3b4870b00b8f80d51752", "reference": "3f6e847ef72cf6e27e4d3b4870b00b8f80d51752", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\color\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 color input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-colorinput", "keywords": ["HTML5", "color", "extension", "form", "input", "j<PERSON>y", "plugin", "widget", "yii2"], "time": "2016-01-14T11:15:49+00:00"}, {"name": "kartik-v/yii2-widget-datepicker", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datepicker.git", "reference": "b793655b63e77e5598ed232cf887cc640c30420f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datepicker/zipball/b793655b63e77e5598ed232cf887cc640c30420f", "reference": "b793655b63e77e5598ed232cf887cc640c30420f", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\date\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datepicker plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-datepicker", "keywords": ["date", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "time": "2017-09-04T03:28:47+00:00"}, {"name": "kartik-v/yii2-widget-datetimepicker", "version": "v1.4.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datetimepicker.git", "reference": "e843520aca008dc0807aa7ea99bfab2cc03c9a3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datetimepicker/zipball/e843520aca008dc0807aa7ea99bfab2cc03c9a3c", "reference": "e843520aca008dc0807aa7ea99bfab2cc03c9a3c", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\datetime\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datetimepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-datetimepicker", "keywords": ["datetime", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "time": "2017-06-08T05:53:28+00:00"}, {"name": "kartik-v/yii2-widget-depdrop", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-depdrop.git", "reference": "6918ca6f7d7be153c80f6aa9c261f9b333293e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-depdrop/zipball/6918ca6f7d7be153c80f6aa9c261f9b333293e9c", "reference": "6918ca6f7d7be153c80f6aa9c261f9b333293e9c", "shasum": ""}, "require": {"kartik-v/dependent-dropdown": "~1.4", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Widget that enables setting up dependent dropdowns with nested dependencies (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-depdrop", "keywords": ["dependent", "dropdown", "extension", "form", "j<PERSON>y", "plugin", "widget", "yii2"], "time": "2016-01-10T17:30:48+00:00"}, {"name": "kartik-v/yii2-widget-fileinput", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-fileinput.git", "reference": "d2c8dcde1aa69ac0c4a0e3b3cfc0d79b3cc2a550"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-fileinput/zipball/d2c8dcde1aa69ac0c4a0e3b3cfc0d79b3cc2a550", "reference": "d2c8dcde1aa69ac0c4a0e3b3cfc0d79b3cc2a550", "shasum": ""}, "require": {"kartik-v/bootstrap-fileinput": "~4.4", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\file\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced FileInput widget for Bootstrap 3.x with file preview, multiple selection, and more features (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-fileinput", "keywords": ["extension", "file", "form", "input", "j<PERSON>y", "plugin", "upload", "widget", "yii2"], "time": "2017-05-25T20:12:30+00:00"}, {"name": "kartik-v/yii2-widget-growl", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-growl.git", "reference": "c79abaa47e9103e93345cd1eca7bc75e17e9a92e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-growl/zipball/c79abaa47e9103e93345cd1eca7bc75e17e9a92e", "reference": "c79abaa47e9103e93345cd1eca7bc75e17e9a92e", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\growl\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate growl based notifications using bootstrap-growl plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-growl", "keywords": ["alert", "bootstrap", "extension", "growl", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "time": "2015-05-03T08:23:04+00:00"}, {"name": "kartik-v/yii2-widget-rangeinput", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rangeinput.git", "reference": "ad82cf956b95555d39dbb534587c7827b1dc7bdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rangeinput/zipball/ad82cf956b95555d39dbb534587c7827b1dc7bdf", "reference": "ad82cf956b95555d39dbb534587c7827b1dc7bdf", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\range\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 range input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rangeinput", "keywords": ["HTML5", "extension", "form", "input", "j<PERSON>y", "plugin", "range", "widget", "yii2"], "time": "2015-11-22T06:52:44+00:00"}, {"name": "kartik-v/yii2-widget-rating", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rating.git", "reference": "69b192bc2b26a435618e17eed7c56294ef805fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rating/zipball/69b192bc2b26a435618e17eed7c56294ef805fab", "reference": "69b192bc2b26a435618e17eed7c56294ef805fab", "shasum": ""}, "require": {"kartik-v/bootstrap-star-rating": "*", "kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\rating\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 widget for the simple yet powerful bootstrap-star-rating plugin with fractional rating support (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rating", "keywords": ["Rating", "bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "star", "widget", "yii2"], "time": "2016-02-17T19:13:26+00:00"}, {"name": "kartik-v/yii2-widget-select2", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-select2.git", "reference": "32761540146d1d5b65c383730a9c9c78ee449069"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-select2/zipball/32761540146d1d5b65c383730a9c9c78ee449069", "reference": "32761540146d1d5b65c383730a9c9c78ee449069", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\select2\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Select2 jQuery plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-select2", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "select2", "widget", "yii2"], "time": "2017-12-03 08:02:21"}, {"name": "kartik-v/yii2-widget-sidenav", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-sidenav.git", "reference": "02ee4f142d7dfbb316f878e538cc7b946f4502d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-sidenav/zipball/02ee4f142d7dfbb316f878e538cc7b946f4502d2", "reference": "02ee4f142d7dfbb316f878e538cc7b946f4502d2", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\sidenav\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced side navigation menu styled for bootstrap (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-sidenav", "keywords": ["bootstrap", "extension", "j<PERSON>y", "menu", "navigation", "plugin", "sidenav", "widget", "yii2"], "time": "2014-11-09T08:07:23+00:00"}, {"name": "kartik-v/yii2-widget-spinner", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-spinner.git", "reference": "3132ba14d58e0564d17f3b846b04c42aa72bdde3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-spinner/zipball/3132ba14d58e0564d17f3b846b04c42aa72bdde3", "reference": "3132ba14d58e0564d17f3b846b04c42aa72bdde3", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\spinner\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to render animated CSS3 loading spinners with VML fallback for IE (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-spinner", "keywords": ["CSS3", "extension", "j<PERSON>y", "loading", "plugin", "spinner", "widget", "yii2"], "time": "2014-11-09T05:02:05+00:00"}, {"name": "kartik-v/yii2-widget-switchinput", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-switchinput.git", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-switchinput/zipball/7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\switchinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle switchinputes (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-switchinput", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "switchinput", "toggle", "widget", "yii2"], "time": "2016-01-10T16:47:35+00:00"}, {"name": "kartik-v/yii2-widget-timepicker", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-timepicker.git", "reference": "203aeb9123e7fa9a3c584d206793d82abe96d469"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-timepicker/zipball/203aeb9123e7fa9a3c584d206793d82abe96d469", "reference": "203aeb9123e7fa9a3c584d206793d82abe96d469", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\time\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap timepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-timepicker", "keywords": ["bootstrap", "extension", "form", "j<PERSON>y", "picker", "plugin", "time", "widget", "yii2"], "time": "2017-01-08T06:36:24+00:00"}, {"name": "kartik-v/yii2-widget-touchspin", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-touchspin.git", "reference": "afc56f68d87c65c9659b76ac82ae4ae8160634c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-touchspin/zipball/afc56f68d87c65c9659b76ac82ae4ae8160634c6", "reference": "afc56f68d87c65c9659b76ac82ae4ae8160634c6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\touchspin\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle touchspines (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-touchspin", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "spinner", "touch", "widget", "yii2"], "time": "2016-01-10T17:10:39+00:00"}, {"name": "kartik-v/yii2-widget-typeahead", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-typeahead.git", "reference": "e369cd55cb2fb9d3a82b57ea6c9a62d69f14fb94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-typeahead/zipball/e369cd55cb2fb9d3a82b57ea6c9a62d69f14fb94", "reference": "e369cd55cb2fb9d3a82b57ea6c9a62d69f14fb94", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\typeahead\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Twitter Typeahead plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-typeahead", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "typeahead", "widget", "yii2"], "time": "2015-06-28T18:05:41+00:00"}, {"name": "kartik-v/yii2-widgets", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widgets.git", "reference": "f435d5e96c4848844bdfa9cee58249ccfb3e2dd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widgets/zipball/f435d5e96c4848844bdfa9cee58249ccfb3e2dd2", "reference": "f435d5e96c4848844bdfa9cee58249ccfb3e2dd2", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-affix": "*", "kartik-v/yii2-widget-alert": "*", "kartik-v/yii2-widget-colorinput": "*", "kartik-v/yii2-widget-datepicker": "*", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widget-growl": "*", "kartik-v/yii2-widget-rangeinput": "*", "kartik-v/yii2-widget-rating": "*", "kartik-v/yii2-widget-select2": "*", "kartik-v/yii2-widget-sidenav": "*", "kartik-v/yii2-widget-spinner": "*", "kartik-v/yii2-widget-switchinput": "*", "kartik-v/yii2-widget-timepicker": "*", "kartik-v/yii2-widget-touchspin": "*", "kartik-v/yii2-widget-typeahead": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\widgets\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Collection of useful widgets for Yii Framework 2.0 extending functionalities for Bootstrap", "homepage": "https://github.com/kartik-v/yii2-widgets", "keywords": ["extension", "form", "widget", "yii2"], "time": "2014-11-09T19:54:17+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2016-12-03T22:08:25+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "rmrevin/yii2-fontawesome", "version": "2.17.1", "source": {"type": "git", "url": "https://github.com/rmrevin/yii2-fontawesome.git", "reference": "65ce306da864f4d558348aeba040ed7876878090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmrevin/yii2-fontawesome/zipball/65ce306da864f4d558348aeba040ed7876878090", "reference": "65ce306da864f4d558348aeba040ed7876878090", "shasum": ""}, "require": {"fortawesome/font-awesome": "~4.7", "php": ">=5.4.0", "yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "extra": {"asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"rmrevin\\yii\\fontawesome\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>in <PERSON>", "email": "<EMAIL>", "homepage": "https://rmrevin.com/"}], "description": "Asset Bundle for Yii2 with Font Awesome", "keywords": ["asset", "awesome", "bundle", "font", "yii"], "time": "2017-01-11T14:05:47+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.8", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "9a06dc570a0367850280eefd3f1dc2da45aef517"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/9a06dc570a0367850280eefd3f1dc2da45aef517", "reference": "9a06dc570a0367850280eefd3f1dc2da45aef517", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "http://swiftmailer.org", "keywords": ["email", "mail", "mailer"], "time": "2017-05-01T15:54:03+00:00"}, {"name": "tinymce/tinymce", "version": "4.6.6", "source": {"type": "git", "url": "https://github.com/tinymce/tinymce-dist.git", "reference": "50b5a77af8ef1a1da362e60ad32ace4131e2dbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tinymce/tinymce-dist/zipball/50b5a77af8ef1a1da362e60ad32ace4131e2dbf0", "reference": "50b5a77af8ef1a1da362e60ad32ace4131e2dbf0", "shasum": ""}, "type": "component", "extra": {"component": {"scripts": ["tinymce.js", "plugins/*/plugin.js", "themes/*/theme.js"], "files": ["tinymce.min.js", "plugins/*/plugin.min.js", "themes/*/theme.min.js", "skins/**"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "http://www.tinymce.com", "keywords": ["editor", "html", "javascript", "richtext", "<PERSON><PERSON><PERSON>", "wysiwyg"], "time": "2017-08-30T12:11:58+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.12", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "70acbecc75cb26b6cd66d16be0b06e4b73db190d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/70acbecc75cb26b6cd66d16be0b06e4b73db190d", "reference": "70acbecc75cb26b6cd66d16be0b06e4b73db190d", "shasum": ""}, "require": {"bower-asset/jquery": "2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/jquery.inputmask": "~3.2.2 | ~3.3.5", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "http://www.yiiframework.com/", "keywords": ["framework", "yii2"], "time": "2017-06-05T14:33:41+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5", "reference": "3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": ">=2.0.6"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"yii\\bootstrap\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "time": "2016-03-17T03:29:28+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "3f4923c2bde6caf3f5b88cc22fdd5770f52f8df2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/3f4923c2bde6caf3f5b88cc22fdd5770f52f8df2", "reference": "3f4923c2bde6caf3f5b88cc22fdd5770f52f8df2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0"}, "require-dev": {"composer/composer": "^1.0"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "time": "2016-12-20T13:26:02+00:00"}, {"name": "yiisoft/yii2-imagine", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-imagine.git", "reference": "59dcd0b43c2b0e5495c7e5c0320e2cbc1cd57411"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-imagine/zipball/59dcd0b43c2b0e5495c7e5c0320e2cbc1cd57411", "reference": "59dcd0b43c2b0e5495c7e5c0320e2cbc1cd57411", "shasum": ""}, "require": {"imagine/imagine": "~0.6.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\imagine\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Imagine integration for the Yii framework", "keywords": ["helper", "image", "imagine", "yii2"], "time": "2016-11-03T19:28:39+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-swiftmailer.git", "reference": "8a03a62cbcb82e7697d3002eb43a8d2637f566ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/8a03a62cbcb82e7697d3002eb43a8d2637f566ec", "reference": "8a03a62cbcb82e7697d3002eb43a8d2637f566ec", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~5.0", "yiisoft/yii2": "~2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "time": "2017-05-01T08:29:00+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.4.5", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "5c14cff4f955b17d20d088dec1bde61c0539ec74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/5c14cff4f955b17d20d088dec1bde61c0539ec74", "reference": "5c14cff4f955b17d20d088dec1bde61c0539ec74", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "~4.5|~5", "symfony/phpunit-bridge": "~2.7|~3", "symfony/yaml": "~2.3|~3"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "time": "2016-10-30T11:50:56+00:00"}, {"name": "bower-asset/typeahead.js", "version": "v0.11.1", "source": {"type": "git", "url": "https://github.com/twitter/typeahead.js.git", "reference": "588440f66559714280628a4f9799f0c4eb880a4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twitter/typeahead.js/zipball/588440f66559714280628a4f9799f0c4eb880a4a", "reference": "588440f66559714280628a4f9799f0c4eb880a4a", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.7"}, "require-dev": {"bower-asset/jasmine-ajax": "~1.3.1", "bower-asset/jasmine-jquery": "~1.5.2", "bower-asset/jquery": "~1.7"}, "type": "bower-asset-library", "extra": {"bower-asset-main": "dist/typeahead.bundle.js"}}, {"name": "codeception/base", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/Codeception/base.git", "reference": "eb3274a5dddeef8d8ca64b0414c0bf3c75bc9be9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/base/zipball/eb3274a5dddeef8d8ca64b0414c0bf3c75bc9be9", "reference": "eb3274a5dddeef8d8ca64b0414c0bf3c75bc9be9", "shasum": ""}, "require": {"behat/gherkin": "~4.4.0", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "~1.0", "php": ">=5.4.0 <8.0", "phpunit/php-code-coverage": ">=2.2.4 <6.0", "phpunit/phpunit": ">4.8.20 <7.0", "phpunit/phpunit-mock-objects": ">2.3 <5.0", "sebastian/comparator": ">1.1 <3.0", "sebastian/diff": "^1.4", "stecman/symfony-console-completion": "^0.7.0", "symfony/browser-kit": ">=2.7 <4.0", "symfony/console": ">=2.7 <4.0", "symfony/css-selector": ">=2.7 <4.0", "symfony/dom-crawler": ">=2.7.5 <4.0", "symfony/event-dispatcher": ">=2.7 <4.0", "symfony/finder": ">=2.7 <4.0", "symfony/yaml": ">=2.7 <4.0"}, "require-dev": {"codeception/specify": "~0.3", "facebook/graph-sdk": "~5.3", "flow/jsonpath": "~0.2", "league/factory-muffin": "^3.0", "league/factory-muffin-faker": "^1.0", "mongodb/mongodb": "^1.0", "monolog/monolog": "~1.8", "pda/pheanstalk": "~3.0", "php-amqplib/php-amqplib": "~2.4", "predis/predis": "^1.0", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <4.0", "vlucas/phpdotenv": "^2.4.0"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "flow/jsonpath": "For using JSONPath in REST module", "league/factory-muffin": "For DataFactory module", "league/factory-muffin-faker": "For Faker support in DataFactory module", "phpseclib/phpseclib": "for SFTP option in FTP Module", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Codeception\\": "src\\Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "http://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "time": "2017-08-10T20:41:08+00:00"}, {"name": "codeception/verify", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "shasum": ""}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"files": ["src/Codeception/function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD assertion library for PHPUnit", "time": "2017-01-09T10:58:51+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14T21:17:01+00:00"}, {"name": "fzaninotto/faker", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "squizlabs/php_codesniffer": "^1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2017-08-15T16:48:10+00:00"}, {"name": "myclabs/deep-copy", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "8e6e04167378abf1ddb4d3522d8755c5fd90d102"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/8e6e04167378abf1ddb4d3522d8755c5fd90d102", "reference": "8e6e04167378abf1ddb4d3522d8755c5fd90d102", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"doctrine/collections": "1.*", "phpunit/phpunit": "~4.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "homepage": "https://github.com/myclabs/DeepCopy", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2017-04-12T18:52:22+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.2.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/4aada1f93c72c35e22fb1383b47fee43b8f1d157", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.3.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-08-08T06:39:58+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/fb3933512008d8162b3cdf9e18dba9309b7c3773", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-06-03T08:32:36+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/0464787bfa7cd13576c5a1e318709768798bec6a", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "time": "2016-04-07T12:29:16+00:00"}, {"name": "phpspec/prophecy", "version": "v1.7.2", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6", "reference": "c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8 || ^5.6.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2017-09-04T11:05:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/3cc8f69b3028d0f96a9078e6295d86e9bf019be5", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2016-10-03T07:40:28+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.11", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/e03f8f67534427a787e21a385a67ec3ca6978ea7", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2017-02-27T10:12:30+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.21", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "3b91adfb64264ddec5a2dee9851f354aa66327db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/3b91adfb64264ddec5a2dee9851f354aa66327db", "reference": "3b91adfb64264ddec5a2dee9851f354aa66327db", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "~1.0.3|~2.0", "symfony/yaml": "~2.1|~3.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2017-06-21T08:11:54+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2017-06-30T09:13:00+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "stecman/symfony-console-completion", "version": "0.7.0", "source": {"type": "git", "url": "https://github.com/stecman/symfony-console-completion.git", "reference": "5461d43e53092b3d3b9dbd9d999f2054730f4bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stecman/symfony-console-completion/zipball/5461d43e53092b3d3b9dbd9d999f2054730f4bbb", "reference": "5461d43e53092b3d3b9dbd9d999f2054730f4bbb", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/console": "~2.3 || ~3.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Stecman\\Component\\Symfony\\Console\\BashCompletion\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Automatic BASH completion for Symfony Console Component based applications.", "time": "2016-02-24T05:08:54+00:00"}, {"name": "symfony/browser-kit", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "aee7120b058c268363e606ff5fe8271da849a1b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/aee7120b058c268363e606ff5fe8271da849a1b5", "reference": "aee7120b058c268363e606ff5fe8271da849a1b5", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/dom-crawler": "~2.8|~3.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0", "symfony/process": "~2.8|~3.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:54:42+00:00"}, {"name": "symfony/console", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf", "reference": "a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3", "symfony/dependency-injection": "~3.3", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/filesystem": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2017-09-06T16:40:18+00:00"}, {"name": "symfony/css-selector", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "c5f5263ed231f164c58368efbce959137c7d9488"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/c5f5263ed231f164c58368efbce959137c7d9488", "reference": "c5f5263ed231f164c58368efbce959137c7d9488", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:54:42+00:00"}, {"name": "symfony/debug", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "8beb24eec70b345c313640962df933499373a944"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/8beb24eec70b345c313640962df933499373a944", "reference": "8beb24eec70b345c313640962df933499373a944", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2017-09-01T13:23:39+00:00"}, {"name": "symfony/dom-crawler", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "6b511d7329b203a620f09a2288818d27dcc915ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/6b511d7329b203a620f09a2288818d27dcc915ae", "reference": "6b511d7329b203a620f09a2288818d27dcc915ae", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2017-09-11T15:55:22+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "54ca9520a00386f83bca145819ad3b619aaa2485"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/54ca9520a00386f83bca145819ad3b619aaa2485", "reference": "54ca9520a00386f83bca145819ad3b619aaa2485", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/expression-language": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:54:42+00:00"}, {"name": "symfony/finder", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "b2260dbc80f3c4198f903215f91a1ac7fe9fe09e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/b2260dbc80f3c4198f903215f91a1ac7fe9fe09e", "reference": "b2260dbc80f3c4198f903215f91a1ac7fe9fe09e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:54:42+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "7c8fae0ac1d216eb54349e6a8baa57d515fe8803"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/7c8fae0ac1d216eb54349e6a8baa57d515fe8803", "reference": "7c8fae0ac1d216eb54349e6a8baa57d515fe8803", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2017-06-14T15:44:48+00:00"}, {"name": "symfony/yaml", "version": "v3.3.9", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "1d8c2a99c80862bdc3af94c1781bf70f86bccac0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/1d8c2a99c80862bdc3af94c1781bf70f86bccac0", "reference": "1d8c2a99c80862bdc3af94c1781bf70f86bccac0", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/console": "~2.8|~3.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:54:42+00:00"}, {"name": "webmozart/assert", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/2db61e59ff05fe5126d152bd0655c9ea113e550f", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2016-11-23T20:04:58+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "372fbf0d47303b7d68275d6720935e5116915418"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/372fbf0d47303b7d68275d6720935e5116915418", "reference": "372fbf0d47303b7d68275d6720935e5116915418", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.11", "yiisoft/yii2-bootstrap": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\debug\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2017-09-05T23:21:05+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "b88ca69ee226a3610b2c26c026c3203d7ac50f6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/b88ca69ee226a3610b2c26c026c3203d7ac50f6c", "reference": "b88ca69ee226a3610b2c26c026c3203d7ac50f6c", "shasum": ""}, "require": {"fzaninotto/faker": "*", "yiisoft/yii2": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\faker\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "time": "2015-03-01T06:22:44+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/1bd6df6804ca077ec022587905a0d43eb286f507", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507", "shasum": ""}, "require": {"bower-asset/typeahead.js": "0.10.* | ~0.11.0", "phpspec/php-diff": ">=1.0.2", "yiisoft/yii2": ">=2.0.4", "yiisoft/yii2-bootstrap": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"yii\\gii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "gii", "yii2"], "time": "2016-03-18T14:09:46+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"dpodium/yii2-filemanager": 20, "kartik-v/yii2-grid": 20, "kartik-v/yii2-widget-select2": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": []}