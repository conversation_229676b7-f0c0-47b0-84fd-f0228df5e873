<?php

namespace console\controllers;

use \yii\console\Controller;
use backend\controllers\ImportController;


class DataImportController extends Controller
{
    public function actionImport(){
      $response = ImportController::getItemLists();
      if($response){
        ImportController::actionCurrency($response);
        ImportController::actionCountry($response);
        ImportController::actionRegion($response);
        echo "Import Completed";
      }
      else {
        echo "Unable to connect to remote server";
      }

    }

}
?>
