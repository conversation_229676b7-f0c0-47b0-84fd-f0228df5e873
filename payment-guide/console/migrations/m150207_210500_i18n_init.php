<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

use yii\db\Migration;

/**
 * Initializes i18n messages tables.
 *
 *
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0.7
 */
class m150207_210500_i18n_init extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%source_message}}', [
            'id' => $this->primaryKey(),
            'category' => $this->string(),
            'message' => $this->text(),
        ], $tableOptions);

        $this->createTable('{{%message}}', [
            'id' => $this->integer()->notNull(),
            'language' => $this->string(16)->notNull(),
            'translation' => $this->text(),
        ], $tableOptions);

        $this->insert('source_message',array(
          'id' => 1,
          'category' => 'app',
          'message' => 'site-title'
        ));
        $this->insert('message',array(
          'id' => 1,
          'language' => 'en-US',
          'translation' => 'Offgamers Payment Guide'
        ));

        $this->insert('source_message',array(
          'id' => 2,
          'category' => 'app',
          'message' => 'theme-footer-copyright'
        ));
        $this->insert('message',array(
          'id' => 2,
          'language' => 'en-US',
          'translation' => 'Copyright &copy; 2017 <a title="Buy gift cards, game cards and pc game cd keys from OffGamers, your trusted online digital games store. online securely with PayPal, credit cards, store credit and more." href="http://www.offgamers.com">OffGamers</a>. All rights reserved.<br />All product names are trademarks of their respective companies.'
        ));

        $this->insert('source_message',array(
          'id' => 3,
          'category' => 'app',
          'message' => 'theme-sidebar-header'
        ));
        $this->insert('message',array(
          'id' => 3,
          'language' => 'en-US',
          'translation' => 'PAYMENT CATEGORIES'
        ));

        $this->insert('source_message',array(
          'id' => 4,
          'category' => 'app',
          'message' => 'theme-country-header'
        ));
        $this->insert('message',array(
          'id' => 4,
          'language' => 'en-US',
          'translation' => 'Choose your country or region'
        ));

        $this->insert('source_message',array(
          'id' => 5,
          'category' => 'app',
          'message' => 'theme-currency-header'
        ));
        $this->insert('message',array(
          'id' => 5,
          'language' => 'en-US',
          'translation' => 'Choose your currency'
        ));

        $this->insert('source_message',array(
          'id' => 6,
          'category' => 'app',
          'message' => 'payment-list-label1'
        ));
        $this->insert('message',array(
          'id' => 6,
          'language' => 'en-US',
          'translation' => 'Support Store Credit Top-up'
        ));

        $this->insert('source_message',array(
          'id' => 7,
          'category' => 'app',
          'message' => 'payment-list-label2'
        ));
        $this->insert('message',array(
          'id' => 7,
          'language' => 'en-US',
          'translation' => 'Support Direct Checkout'
        ));

        $this->insert('source_message',array(
          'id' => 8,
          'category' => 'app',
          'message' => 'home-description'
        ));

        $this->insert('message',array(
          'id' => 8,
          'language' => 'en-US',
          'translation' => 'Learn how to pay in the most convenient way for all payment methods via our payment guides here.'
        ));

        $this->insert('source_message',array(
          'id' => 9,
          'category' => 'app',
          'message' => 'global-meta'
        ));
        $this->insert('message',array(
          'id' => 9,
          'language' => 'en-US',
          'translation' => 'gift cards, cd key, game card, mmo game, pc game, digital games store, online games store, game top up, digital gift cards, gifts, gift card code'
        ));

        $this->insert('source_message',array(
          'id' => 10,
          'category' => 'app',
          'message' => 'method-no-content'
        ));
        $this->insert('message',array(
          'id' => 10,
          'language' => 'en-US',
          'translation' => '<b>No Payment Method Found</b> Try to check for other payment category or contact our <a href="http://support.offgamers.com/support/tickets/new" title="support">customer service</a>.'
        ));

        $this->insert('source_message',array(
          'id' => 11,
          'category' => 'app',
          'message' => 'guide-no-content'
        ));
        $this->insert('message',array(
          'id' => 11,
          'language' => 'en-US',
          'translation' => '<b>Payment Guide Not Found</b> Try to insert another keyword or contact our <a href="http://support.offgamers.com/support/tickets/new" title="support">customer service</a>.'
        ));

        $this->insert('source_message',array(
          'id' => 12,
          'category' => 'app',
          'message' => 'gift-card-no-content'
        ));
        $this->insert('message',array(
          'id' => 12,
          'language' => 'en-US',
          'translation' => '<h2>No Gift Cards available in your country</h2>'
        ));

        $this->insert('source_message',array(
          'id' => 13,
          'category' => 'app',
          'message' => 'method-not-found'
        ));
        $this->insert('message',array(
          'id' => 13,
          'language' => 'en-US',
          'translation' => '<b>Search result not found!</b> Try to insert another keyword or contact our <a href="http://support.offgamers.com/support/tickets/new" title="support">customer service</a>.'
        ));

        $this->insert('source_message',array(
          'id' => 14,
          'category' => 'payment-category',
          'message' => 'payment-category-name-1'
        ));
        $this->insert('message',array(
          'id' => 14,
          'language' => 'en-US',
          'translation' => 'Where to buy OffGamers Gift Card'
        ));

        $this->insert('source_message',array(
          'id' => 15,
          'category' => 'app',
          'message' => 'search-prefix-title'
        ));
        $this->insert('message',array(
          'id' => 15,
          'language' => 'en-US',
          'translation' => 'Search Results'
        ));

        $this->insert('source_message',array(
          'id' => 16,
          'category' => 'app',
          'message' => 'search-placeholder'
        ));
        $this->insert('message',array(
          'id' => 16,
          'language' => 'en-US',
          'translation' => 'Search for Payment Method'
        ));

        $this->insert('source_message',array(
          'id' => 17,
          'category' => 'app',
          'message' => '404-message'
        ));
        $this->insert('message',array(
          'id' => 17,
          'language' => 'en-US',
          'translation' => '<b>404 Error!!!</b><br><br><ul><li>Check the Web address you entered to make sure if it’s correct.</li><li>Try to access the page directly from the <a href="http://www.offgamers.com" title="OffGamers">OffGamers Home Page</a> instead of using a bookmark. If the page has moved, please reset your bookmark.</li><li>Contact our <a href="http://support.offgamers.com/support/tickets/new" title="support">customer support</a> for help.</li></ul>',
        ));

        $this->insert('source_message',array(
          'id' => 18,
          'category' => 'app',
          'message' => 'payment-list-processing-time'
        ));
        $this->insert('message',array(
          'id' => 18,
          'language' => 'en-US',
          'translation' => 'Processing Time'
        ));

        $this->insert('source_message',array(
          'id' => 19,
          'category' => 'app',
          'message' => 'payment-list-processing-fee'
        ));
        $this->insert('message',array(
          'id' => 19,
          'language' => 'en-US',
          'translation' => 'Processing Fee'
        ));

        $this->addPrimaryKey('pk_message_id_language', '{{%message}}', ['id', 'language']);
        $this->addForeignKey('fk_message_source_message', '{{%message}}', 'id', '{{%source_message}}', 'id', 'CASCADE', 'RESTRICT');
        $this->createIndex('idx_source_message_category', '{{%source_message}}', 'category');
        $this->createIndex('idx_message_language', '{{%message}}', 'language');
    }

    public function down()
    {
        $this->dropTable('message');
        $this->dropTable('source_message');
    }
}
