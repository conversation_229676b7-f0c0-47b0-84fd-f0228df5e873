<?php

use yii\db\Migration;

class m170607_064142_currency extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%currency}}', [
           'currency_code' => $this->string(5)->notNull(),
           'name' => $this->string(255)->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->insert('currency',array(
          'currency_code' => 'usd',
          'name' => 'United State Dollar',
          'status' => 1
        ));

        $this->addPrimaryKey('pk_currency_currency_code', 'currency' , 'currency_code' );

        $this->createTable('{{%region}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(255)->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->insert('region',array(
          'id' => 1,
          'name' => '	Americas',
          'status' => 1
        ));

        $this->createTable('{{%country}}', [
           'country_code' => $this->string(5)->notNull(),
           'name' => $this->string(255)->notNull(),
           'default_currency' => $this->string(5),
           'default_region' => $this->bigInteger(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->insert('country',array(
          'country_code' => 'us',
          'name' => 'United States of America',
          'default_currency' => 'usd',
          'default_region' => 1,
          'status' => 1
        ));

        $this->addPrimaryKey('pk_country_country_code', 'country' , 'country_code' );
        $this->addForeignKey ("fk_default_region", "country", "default_region", "region", "id",'CASCADE');
        $this->addForeignKey ("fk_default_currency", "country", "default_currency", "currency", "currency_code",'CASCADE');
    }

    public function down()
    {
      $this->dropTable('country');
      $this->dropTable('region');
      $this->dropTable('currency');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
