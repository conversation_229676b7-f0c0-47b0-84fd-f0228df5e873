<?php

use yii\db\Migration;

class m170607_064516_language extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('language', [
           'iso_code' => $this->string(10)->notNull(),
           'name' => $this->string(255)->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->addPrimaryKey ('pk_language_iso_code', 'language' , 'iso_code' );

        $this->insert('{{%language}}',array(
          'name'=> 'English',
          'iso_code'=> 'en-US',
          'status' => '1',
          'created_at'=>'1392559490',
          'updated_at'=>'1392559490',
        ));

        $this->insert('{{%language}}',array(
          'name'=> '简体中文',
          'iso_code'=> 'zh-CN',
          'status' => '1',
          'created_at'=>'1392559490',
          'updated_at'=>'1392559490',
        ));
    }

    public function down()
    {
      $this->dropTable('language');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
