<?php

use yii\db\Migration;

class m170607_074615_gift_card_merchant extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%gift_card_merchant}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(50)->notNull(),
           'description' => $this->text()->notNull(),
           'icon_url' => $this->text()->notNull(),
           'is_sub_merchant' => $this->boolean()->notNull(),
           'sub_merchant_description' => $this->text(),
           'additional_button' => $this->text(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->createTable('{{%gift_card_sub_merchant}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(30)->notNull(),
           'icon_url' => $this->text()->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
           'gift_card_merchant_id' => $this->bigInteger()->notNull(),
        ], $tableOptions);

        $this->addForeignKey ("fk_gift_card_merchant", "gift_card_sub_merchant", "gift_card_merchant_id", "gift_card_merchant", "id",'CASCADE');

        $this->createTable('{{%gift_card_country_relationship}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'country_code' => $this->string(5)->notNull(),
           'gift_card_merchant_id' => $this->bigInteger()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);
        $this->addForeignKey ("fk_gift_card_country_code", "gift_card_country_relationship", "country_code", "country", "country_code",'CASCADE');
        $this->addForeignKey ("fk_gift_card_method_id", "gift_card_country_relationship", "gift_card_merchant_id", "gift_card_merchant", "id",'CASCADE');

    }

    public function down()
    {
      $this->dropTable('gift_card_country_relationship');
      $this->dropTable('gift_card_sub_merchant');
      $this->dropTable('gift_card_merchant');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
