<?php

use yii\db\Migration;

class m170607_075456_payment extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%payment_category}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(50)->notNull(),
           'url_name' => $this->string(50)->notNull(),
           'description' => $this->text()->notNull(),
           'order_no' => $this->integer()->notNull()->defaultValue(0),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->insert('{{%payment_category}}',array(
          'id' => '1',
          'name'=>'payment-category-name-1',
          'url_name'=>'where-to-buy',
          'description'=> '',
          'order_no' => '0',
          'status' => '5',
          'created_at'=>'1392559490',
          'updated_at'=>'1392559490',
        ));

        $this->createTable('{{%payment_gateway}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(50)->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
        ], $tableOptions);

        $this->createTable('{{%payment_method}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(255)->notNull(),
           'url_name' => $this->string(255),
           'description'=> $this->string(255),
           'processing_fee' => $this->string(50),
           'processing_time' => $this->string(50),
           'logo_url' => $this->text()->notNull(),
           'support_credit' => $this->boolean()->notNull(),
           'support_direct' => $this->boolean()->notNull(),
           'additional_info' => $this->text(),
           'additional_button' => $this->text(),
           'order_no' => $this->integer(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
           'payment_gateway_id' => $this->bigInteger(),
           'payment_category_id' => $this->bigInteger()->notNull(),
           'pipwave_payment_id' => $this->string(255)->notNull()
        ], $tableOptions);

        $this->addForeignKey ("fk_payment_method_gateway_id", "payment_method", "payment_gateway_id", "payment_gateway", "id",'CASCADE');
        $this->addForeignKey ("fk_payment_method_category_id", "payment_method", "payment_category_id", "payment_category", "id",'CASCADE');
        $this->createIndex('pipwave_payment_id', 'payment_method', 'pipwave_payment_id', $unique = true );

        $this->createTable('payment_method_localization',[
          'currency_code' => $this->string(5)->notNull(),
          'country_code' => $this->string(5)->notNull(),
          'payment_method_id' => $this->bigInteger()->notNull(),
        ], $tableOptions);

        $this->createTable('pipwave_request',[
          'currency_code' => $this->string(5)->notNull(),
          'country_code' => $this->string(5)->notNull(),
          'results' => $this->text(),
          'expiry' => $this->bigInteger()->notNull()
        ],$tableOptions);

        $this->createTable('pipwave_request_log',[
          'id' => $this->bigPrimaryKey(),
          'error_code' => $this->string(255),
          'currency_code' => $this->string(5),
          'country_code' => $this->string(5),
          'results' => $this->text(),
          'created_at' => $this->bigInteger()
        ]);

        $this->createIndex('idx_pipwave_request_currency', 'pipwave_request', 'currency_code');
        $this->createIndex('idx_pipwave_request_country', 'pipwave_request', 'country_code');
        $this->createIndex('idx_localization_currency', 'payment_method_localization', 'currency_code');
        $this->createIndex('idx_localization_country', 'payment_method_localization', 'country_code');
        $this->addForeignKey ("fk_payment_method_localization_id", "payment_method_localization", "payment_method_id", "payment_method", "id",'CASCADE');
    }

    public function down()
    {
      $this->dropTable('pipwave_request_log');
      $this->dropTable('payment_method_localization');
      $this->dropTable('pipwave_request');
      $this->dropTable('payment_method');
      $this->dropTable('payment_gateway');
      $this->dropTable('payment_category');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
