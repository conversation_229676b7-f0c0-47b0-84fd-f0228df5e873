<?php

use yii\db\Migration;

class m170607_075729_user_guide extends Migration
{
    public function up()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%user_guide}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'name' => $this->string(255)->notNull(),
           'content' => $this->text()->notNull(),
           'meta_description' => $this->text(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
           'payment_method_id' => $this->bigInteger()->notNull(),
           'language_code' => $this->string(10)->notNull(),
        ], $tableOptions);

        $this->createTable('{{%user_guide_visitors}}', [
           'id' => $this->bigPrimaryKey()->notNull(),
           'visit_count' => $this->bigInteger()->notNull(),
           'status' => $this->integer()->notNull(),
           'updated_at' => $this->timestamp()->notNull(),
           'created_at' => $this->timestamp()->notNull()->defaultValue(0),
           'user_guide_id' => $this->bigInteger()->notNull(),
        ], $tableOptions);

        $this -> addForeignKey ("fk_user_guide_language", "user_guide", "language_code", "language", "iso_code",'CASCADE');
        $this -> addForeignKey ("fk_user_guide_payment_method", "user_guide", "payment_method_id", "payment_method", "id",'CASCADE');
    }

    public function down()
    {
        $this->dropTable('user_guide_visitors');
        $this->dropTable('user_guide');
    }

    /*
    // Use safeUp/safeDown to run migration code within a transaction
    public function safeUp()
    {
    }

    public function safeDown()
    {
    }
    */
}
