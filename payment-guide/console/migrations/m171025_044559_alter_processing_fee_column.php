<?php

use yii\db\Migration;
use common\models\PaymentMethod;
use common\models\SourceMessage;
use common\models\Message;

class m171025_044559_alter_processing_fee_column extends Migration
{
    public function safeUp()
    {
      $tableOptions = null;
      if ($this->db->driverName === 'mysql') {
          // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
          $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
      }
      $models = PaymentMethod::find()->all();
      foreach($models as $model){
        if(!empty(trim($model->processing_fee))){
          $source = SourceMessage::find()->where(["category"=>"payment-method","message"=>$model->processing_fee])->one();
          if(!empty($source->id)){
            $message = Message::find()->where(["id"=>$source->id])->all();
            foreach($message as $msg){
              $msg->translation = json_encode(["Default" => $msg->translation]);
              $msg->save();
            }
          }
        }
      }
      // return false;
    }

    function isJson($string) {
     json_decode($string);
     return (json_last_error() == JSON_ERROR_NONE);
    }

    public function safeDown()
    {
        echo "m171025_044559_alter_processing_fee_column cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m171025_044559_alter_processing_fee_column cannot be reverted.\n";

        return false;
    }
    */
}
