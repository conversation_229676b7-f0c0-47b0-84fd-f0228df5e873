<?php

use yii\db\Migration;
use yii\caching\TagDependency;

class m171206_035139_add_placeholder_translation extends Migration
{
    public function safeUp()
    {
      $this->insert('source_message',array(
        'category' => 'app',
        'message' => 'search-country-placeholder'
      ));

      $this->insert('message',array(
        'id' => $this->getDb()->getLastInsertID(),
        'language' => 'en-US',
        'translation' => 'Search by Country Name'
      ));

      $this->insert('source_message',array(
        'category' => 'app',
        'message' => 'search-currency-placeholder'
      ));

      $this->insert('message',array(
        'id' => $this->getDb()->getLastInsertID(),
        'language' => 'en-US',
        'translation' => 'Search by Currency Name or ISO-3 Code'
      ));
      TagDependency::invalidate(Yii::$app->cache, 'com.payment-guide.translation');
    }

    public function safeDown()
    {
        echo "m171206_035139_add_placeholder_translation cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m171206_035139_add_placeholder_translation cannot be reverted.\n";

        return false;
    }
    */
}
