<?php

use yii\db\Migration;

class m180130_033323_hotfix_guide_localization extends Migration
{
    public function safeUp()
    {
      $this->insert('source_message',array(
        'category' => 'app',
        'message' => 'guide-localization'
      ));

      $this->insert('message',array(
        'id' => $this->getDb()->getLastInsertID(),
        'language' => 'en-US',
        'translation' => 'is not available in your country / currency. Try to check for other payment method or contact our customer service.'
      ));
    }

    public function safeDown()
    {
        echo "m180130_033323_hotfix_guide_localization cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m180130_033323_hotfix_guide_localization cannot be reverted.\n";

        return false;
    }
    */
}
