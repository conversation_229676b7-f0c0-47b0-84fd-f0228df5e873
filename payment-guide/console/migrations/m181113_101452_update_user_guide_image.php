<?php

use yii\db\Migration;

use common\models\UserGuide;
use dpodium\filemanager\components\FilemanagerHelper;
use dpodium\filemanager\models\Files;

class m181113_101452_update_user_guide_image extends Migration
{
    public function safeUp()
    {
        $user_guide = UserGuide::find()->all();
        foreach($user_guide as $key => $value){
            if(isset($value->content) && ($value->content != '')){
                  preg_match_all('/<File-identifier>(.*)<\/File-identifier><extra>(.*)<\/extra>/', $value->content, $result);

                  foreach($result[1] as $k => $v) {
                      $img = FilemanagerHelper::getFile($v, 'file_identifier')['img_src'];
                      if (!preg_match("~^(?:f|ht)tps?://~i", $img)) {
                              $img = "https://" . $img;
                      }
                      $name_file = Files::find()->select('src_file_name')->where(['file_identifier' => $v])->one();
                      if($name_file){
                          $name = $name_file->src_file_name;
                          $value->content = str_replace('<File-identifier>'.$v.'</File-identifier><extra> width="700" </extra>', '<img src="'.$img.'" alt="'.$name.'">', $value->content);
                      }
                      else{
                          $value->content = str_replace('<File-identifier>'.$v.'</File-identifier><extra> width="700" </extra>', '<img src="'.$img.'" alt="">', $value->content);
                      }
                      $value->save();
                  }

            }
        }
    }

    public function safeDown()
    {
//        echo "m181113_101452_update_user_guide_image cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m181113_101452_update_user_guide_image cannot be reverted.\n";

        return false;
    }
    */
}
