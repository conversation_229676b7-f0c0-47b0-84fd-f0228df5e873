<?php

use yii\db\Migration;

use common\models\UserGuide;
use dpodium\filemanager\components\FilemanagerHelper;
use dpodium\filemanager\models\Files;

class m211202_155615_user_guide_visitors_indexing extends Migration
{
    public function safeUp()
    {
        $this->createIndex('idx_user_guide_id', 'user_guide_visitors', ['user_guide_id']);
    }

    public function safeDown()
    {
        $this->dropIndex('idx_user_guide_id', 'user_guide_visitors');
    }
}
