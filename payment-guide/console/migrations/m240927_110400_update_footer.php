<?php

use yii\db\Migration;

use common\models\UserGuide;
use dpodium\filemanager\components\FilemanagerHelper;
use dpodium\filemanager\models\Files;

class m240927_110400_update_footer extends Migration
{
    public function up()
    {
        //legacy-footer-copyright
        $this->insert('source_message', [
            'category' => 'app',
            'message' => 'legacy-footer-copyright'
        ]);
        $id = $this->getDb()->getLastInsertID();
        $this->insert('message', [
            'id' => $id,
            'language' => 'en-US',
            'translation' => 'OffGamers is a global digital product and service retailer with a 20+ years track record. We prioritize delivering value and satisfaction to partners and customers.'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'zh-CN',
            'translation' => 'OffGamers 是一家拥有超过 20 年历史的全球数字产品和服务零售商。我们致力于为合作伙伴和客户提供价值和满意度'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'id',
            'translation' => 'OffGamers adalah pengecer produk dan layanan digital global dengan pengalaman lebih dari 20 tahun. Kami berkomitmen untuk memberikan nilai dan kepuasan kepada mitra dan pelanggan.'
        ]);
    
        //legacy-about-us
        $this->insert('source_message', [
            'category' => 'app',
            'message' => 'legacy-about-us'
        ]);
        $id = $this->getDb()->getLastInsertID();
        $this->insert('message', [
            'id' => $id,
            'language' => 'en-US',
            'translation' => 'About Us'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'zh-CN',
            'translation' => '关于我们'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'id',
            'translation' => 'Tentang Kami'
        ]);
    
        //legacy-terms-of-service
        $this->insert('source_message', [
            'category' => 'app',
            'message' => 'legacy-terms-of-service'
        ]);
        $id = $this->getDb()->getLastInsertID();
        $this->insert('message', [
            'id' => $id,
            'language' => 'en-US',
            'translation' => 'Terms of service'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'zh-CN',
            'translation' => '服务条款'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'id',
            'translation' => 'Syarat layanan'
        ]);
    
        //legacy-privacy-policy
        $this->insert('source_message', [
            'category' => 'app',
            'message' => 'legacy-privacy-policy'
        ]);
        $id = $this->getDb()->getLastInsertID();
        $this->insert('message', [
            'id' => $id,
            'language' => 'en-US',
            'translation' => 'Privacy policy'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'zh-CN',
            'translation' => '隐私政策'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'id',
            'translation' => 'Kebijakan privasi'
        ]);
    
        //legacy-help-center
        $this->insert('source_message', [
            'category' => 'app',
            'message' => 'legacy-help-center'
        ]);
        $id = $this->getDb()->getLastInsertID();
        $this->insert('message', [
            'id' => $id,
            'language' => 'en-US',
            'translation' => 'Help center'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'zh-CN',
            'translation' => '帮助中心'
        ]);
        $this->insert('message', [
            'id' => $id,
            'language' => 'id',
            'translation' => 'Pusat bantuan'
        ]);
    }
    

    public function down()
    {
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-footer-copyright'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-footer-copyright']);
        
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-about-us'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-about-us']);
        
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-terms-of-service'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-terms-of-service']);
        
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-legal'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-legal']);
        
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-privacy-policy'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-privacy-policy']);
        
        $this->delete('message', [
            'id' => (new \yii\db\Query())
            ->select('id')->from('source_message')
            ->where(['category' => 'app', 'message' => 'legacy-help-center'])
        ]);
        $this->delete('source_message', ['category' => 'app', 'message' => 'legacy-help-center']);
    }
}