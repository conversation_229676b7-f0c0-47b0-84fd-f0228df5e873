<?php

return [
    'components' => [
        'cache' => [
            'class' => 'yii\caching\MemCache',
            'keyPrefix' => "payment-guide/",
            'useMemcached' => true,
            'servers' => [
                [
                    'host' => 'localhost',
                    'port' => 11211,
                    'weight' => 100,
                ],
            ],
        ],
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=payment-guide-yii2',
            'username' => $params['db.username'],
            'password' => $params['db.password'],
            'charset' => 'utf8mb4',
            'emulatePrepare' => true,
            'enableSchemaCache' => true,
            'schemaCache' => 'cache',
            'schemaCacheDuration' => 604800, // 7 days
            // Configure the slave server
            'slaveConfig' => [
                'username' => $params['db.username'],
                'password' => $params['db.password'],
                'charset' => 'utf8mb4',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => 'mysql:host=rds-kb.offgamers.com;dbname=payment_guide_og'],
            ],
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => true,
        ],
        'aws' => [
            'key' => $params['s3.key'],
            'secret' => $params['s3.secret'],
            'region' => '',
            'version' => 'latest',
            's3' => [
                'asset' => [
                    'acl' => 'public-read',
                    'bucket_key' => '',
                    'prefix_path' => '',
                ]
            ]
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'enableCaching' => true,
                    'cachingDuration' => 0
                ],
                'app' => [
                    'enableCaching' => true,
                    'cachingDuration' => 0
                ],
            ],
        ],
    ],
    'modules' => [
        'gridview' => [
            'class' => '\kartik\grid\Module'
        ],
        'filemanager' => [
            'class' => 'dpodium\filemanager\Module',
            'filesUpload' => [
                'multiple' => true,
                'maxFileCount' => 30
            ],
            'storage' => [
                's3' => [
                    'key' => $params['s3.key'],
                    'secret' => $params['s3.secret'],
                    'bucket' => '',
                    'region' => 'us-east-1',
                    'proxy' => '',
                    'cdnDomain' => '',
                    'prefixPath' => '',
                    'cacheTime' => ''
                ]
            ],
            'acceptedFilesType' => [
                'image/jpeg',
                'image/png',
                'image/gif',
            ],
            'maxFileSize' => 8,
            'thumbnailSize' => [120, 120]
        ]
    ]
];