<?php

$config = [
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'enableCookieValidation' => false,
        ],
        'assetManager' => [
            'appendTimestamp' => true,
            's3BucketTag' => 'asset',
            'basePath' => '', //s3://bucket_name/folder_path
            'baseUrl' => '', //https://bucket_name.s3.amazonaws.com/folder_path
            'cacheTime' => '',
            'bundles' => [
                'yii\web\JqueryAsset' => [
                    'js' => [
                        'jquery.min.js'
                    ]
                ],
            ]
        ],
    ],
    'aliases' => [
        '@assets' => '@app/themes/' . $params['theme'] . '/assets',
    ],
];

if (!YII_ENV_TEST) {
    //configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
