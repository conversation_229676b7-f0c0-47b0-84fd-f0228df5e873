<?php

namespace frontend\components;

use Yii;
use common\models\Country;
use frontend\controllers\BaseController;

class CookieManager
{

    public static function getCookie()
    {
        $cookies = Yii::$app->response->cookies;
        $session = Yii::$app->session;
        // Detect Payment Guide Cookies
        if ((($cookie = Yii::$app->request->cookies->get('pg_regional')) == null) && !self::checkCookies($cookie)) {
            // Detect G2G Regional Cookies
            if (($cookie = Yii::$app->request->cookies->get('g2g_regional')) !== null && self::checkCookies($cookie)) {
                $cookie = self::checkRegionCookies($cookie);
            // Detect OGM Regional Cookies
            } elseif (($cookie = Yii::$app->request->cookies->get('ogm_regional')) !== null && self::checkCookies($cookie)) {
                $cookie = self::checkRegionCookies($cookie);
            // Initialize regional by GeoIP Extension when cookies not found
            } else {
                $location = Yii::$app->geoip->lookupLocation();
                if ($location && !empty($location->countryCode) && BaseController::validateLocaleData(array('country' => $location->countryCode))) {
                    // $countryCode = $location->countryCode;
                    $countryCode = Yii::$app->params['defaultCountry'];
                } else {
                    $countryCode = Yii::$app->params['defaultCountry'];
                }
                $default_currency = Country::find()->where(['country_code' => $location->countryCode])->asArray()->one();
                if ($default_currency && BaseController::validateLocaleData(array('currency' => $default_currency['default_currency']))) {
                    $currency = $default_currency['default_currency'];
                } else {
                    $currency = Yii::$app->params['defaultCurrency'];
                }
                $cookie = array(
                    'country' => $countryCode,
                    'currency' => $currency,
                    'language' => 'en-US',
                );
            }
            $cookie = json_encode($cookie);
            $cookies->add(new \yii\web\Cookie([
                'name' => 'pg_regional',
                'value' => $cookie,
            ]));
            $session->set('pg_regional', $cookie);
        } else {
            $cookie = self::checkRegionCookies($cookie);
            $cookie = json_encode($cookie);
        }
        $session->set('pg_regional', $cookie);
    }

    public static function checkCookies($cookie){
      return $cookie instanceof \yii\web\Cookie;
    }

    private static function checkRegionCookies($regionalData)
    {
        $data = json_decode(urldecode($regionalData->value), true);
        if(json_last_error() !== JSON_ERROR_NONE){
          return [
              'country' => Yii::$app->params['defaultCountry'],
              'currency' => Yii::$app->params['defaultCurrency'],
              'language' => 'en-US'
          ];
        }
        return [
            // 'country' => (BaseController::validateLocaleData(array('country' => $data['country'])) ? $data['country'] : Yii::$app->params['defaultCountry']),
            'country' => Yii::$app->params['defaultCountry'],
            'currency' => (BaseController::validateLocaleData(array('currency' => $data['currency'])) ? $data['currency'] : Yii::$app->params['defaultCurrency']),
            'language' => (BaseController::validateLocaleData(array('language' => $data['language'])) ? $data['language'] : 'en-US')
        ];
    }
}
