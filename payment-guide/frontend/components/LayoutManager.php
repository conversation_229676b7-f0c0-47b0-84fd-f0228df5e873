<?php

namespace frontend\components;

use Yii;
use yii\caching\TagDependency;
use common\models\PaymentCategory;
use common\models\PaymentMethod;
use common\models\Country;
use common\models\Currency;
use common\models\Language;
use yii\helpers\ArrayHelper;
use frontend\components\PipwaveApiManager;
use yii\helpers\Url;

class LayoutManager
{
    /**
     * Function to get sidebar Menu
     */
    public static function getSidebar()
    {
        $key = 'payment_category/allMenu/name/' . Yii::$app->session->get('currencycode') . '/' . Yii::$app->session->get('language') . '/array';

        //Get Menu from MemCache
        $data = Yii::$app->cache->get($key);
        $session = Yii::$app->session;

        //If Menu is not available in MemCache query from DB
        $records = array();
        if ($data === false) {
            $record = PaymentCategory::find()->where(['status' => PaymentCategory::STATUS_ACTIVE])->orderBy('order_no')->all();
            foreach ($record as $v) {
                //Get translated menu based on language selected
                $v->displayValue();
            }
            $record = ArrayHelper::toArray($record);
            $filterData = PipwaveApiManager::getFilterData();
            //Get data from pipwave API
            $pipwave_data = json_decode(PipwaveApiManager::getPipwaveRequestResults());
            //Convert return data into array
            $pipwave = ArrayHelper::toArray($pipwave_data->message);
            $recommended = false;
            foreach($record as $k=>$v){
                //To define to display the particular category or not
                $record[$k]["display"] = 0;
                //To define the default sorting order for the particular category
                $record[$k]["sorting"] = 10000;
                //Find existing data in DB if match
                $methods[$v['id']] = PaymentMethod::find()->where([
                    'payment_category_id' => $v['id'],
                    'id' => $filterData,
                    'status' => 1
                ])->all();

                //Replace display value for the particular category if having active payment methods
                $record[$k]["display"] = !empty($methods[$v['id']]) ? 1 : 0;

                foreach ($methods[$v['id']] as $method_k => $method_v) {
                    //Find if this method exist in Pipwave API data
                    $this_pipwave = ArrayHelper::toArray(self::search_array($pipwave, "code", $method_v->pipwave_payment_id));
                    if($this_pipwave && count($this_pipwave)>=1){
                        //Replace the category name by using API return data for ENGLISH
                        if(Yii::$app->session->get('language') == "en-US"){
                            $record[$k]["name"] = $this_pipwave["category"];
                        }
                        //Replace sorting ordering for the category
                        $record[$k]["sorting"] = $record[$k]["sorting"] == 10000 ? $this_pipwave["sorting"] : $record[$k]["sorting"];
                        if(array_key_exists("preferred", $this_pipwave)){
                            if(!$recommended){
                                $recommended = $this_pipwave["preferred"];
                            }
                        }
                    }
                }
            }
            //Sort the array by using "display" and "sorting" value in the array
            ArrayHelper::multisort($record, ['display', 'sorting'], [SORT_DESC, SORT_ASC]);
            $record = ArrayHelper::index($record, 'id', [], true);
            $records["data"] = $record;
            if($recommended){
                $records["preferred"] = true;
            }
            Yii::$app->cache->set($key, $records, 604800, new TagDependency(
                ['tags' => 'com.payment-guide.menu']
            ));
            $data = $records;
        }
        return $data;
    }

    //To find and return the entire array in multidimension array if key and value exist
    public static function search_array($array, $key, $value) {
        $return = array();
        foreach ($array as $k=>$subarray){     
            if (isset($subarray[$key]) && $subarray[$key] == $value) {
                $return = $subarray;
                //Return it origin index in the main array
                $return["sorting"] = $k;
                return $return;
            } 
        }
        return $return;
    }

    //Get icons for UI display purpose by using url_name
    public static function getIcon($target)
    {
        $icons = [
            "recommended"=>"fa fa-star",
            "credit-card"=>"fa fa-credit-card-alt",
            "e-wallet"=>"fa fa-qrcode",
            "online-banking"=>"fa fa-bank",
            "atm-bank-transfer"=>"fa fa-money",
            "mobile-payment"=>"fa fa-mobile",
            "over-the-counter-non-bank"=>"fa fa-usd",
            "store-credit-gift-card"=>"fa fa-gift",
            "gift-card"=>"fa fa-gift",
            "bill-payment-online-atm"=>"fa fa-exchange",
        ];

        //Check if the url_name existed in the array
        if(array_key_exists($target, $icons)){
            return $icons[$target];
        } else {
            return "";
        }
    }

    /**
     * Function to get Country List
     */
    public static function getCountryList()
    {
        //Setting MemCache key for Country and getting data from Memcache
        $key = "CACHE_COUNTRY";
        $data = Yii::$app->cache->get($key);
        //If Country list is not available in MemCache query from DB
        if ($data === false) {
            $record = Country::find()->where(['status' => Country::STATUS_ACTIVE])->andWhere(['not', ['default_region' => NULL]])->all();
            foreach ($record as $v) {
                $v->displayValue();
            }
            $record = ArrayHelper::toArray($record);
            Yii::$app->cache->set($key, $record, 604800, new TagDependency(['tags' => 'com.payment-guide.country']));
            $data = $record;
        }
        return $data;
    }

    /**
     * Function to get Currency List
     */
    public static function getCurrencyList()
    {
        //Setting MemCache key for Currency and getting data from Memcache
        $key = "CACHE_CURRENCY";
        $data = Yii::$app->cache->get($key);
        //If Currency list is not available in MemCache query from DB
        if ($data === false) {
            $record = Currency::find()->where(['status' => Currency::STATUS_ACTIVE])->asArray()->all();
            Yii::$app->cache->set($key, $record, 604800, new TagDependency(['tags' => 'com.payment-guide.currency']));
            $data = $record;
        }
        return $data;
    }

    /**
     * Function to generate redirect url
     */
    public static function getRedirectUrl($localeData = [])
    {
        $controller = Yii::$app->controller->id . "/" . Yii::$app->controller->action->id;
        $language = (!empty($localeData['language']) ? $localeData['language'] : $_SESSION['language']);
        $country = (!empty($localeData['country']) ? $localeData['country'] : $_SESSION['countrycode']);
        $currency = (!empty($localeData['currency']) ? $localeData['currency'] : $_SESSION['currencycode']);
        $request = Yii::$app->request;
        $url = $request->getHostInfo() . "/payment-guide/";
        $is_en = ($language === 'en-US');
        switch ($controller) {
            case "payment-category/index":
                $url = $url . ($is_en ? "" : $language);
                break;

            case "payment-method/list":
                $cat = $request->get('cat');
                $url = $url . ($is_en ? "" : $language . "/") . "$cat/$country/$currency";
                break;

            case "payment-method/search":
                $url = $url . ($is_en ? "" : $language . "/") . "search?q=" . $request->get('q');
                break;

            case "user-guide/index":
                $url = $url ."pg/" . ($is_en ? "" : $language . "/") . $request->get('cat') . "/" . $request->get('name');
                break;

            case "gift-card-merchant/index":
                $url = $url . ($is_en ? "" : $language . "/") . "buy-offgamers-gift-card/$country";
                break;

            default:
                $url = $url . ($is_en ? "" : $language);
                break;

        }
        
        return \yii\helpers\Html::encode($url);
    }

    /**
     * Function to get Language List
     */
    public static function getLanguageList()
    {
        //Setting MemCache key for Language and getting data from Memcache
        $key = "CACHE_LANGUAGE";
        $data = Yii::$app->cache->get($key);
        //If Language list is not available in MemCache query from DB
        if ($data === false) {
            $record = Language::find()->where(['status' => Language::STATUS_ACTIVE])->asArray()->all();
            Yii::$app->cache->set($key, $record, 604800, new TagDependency(['tags' => 'com.payment-guide.language']));
            $data = $record;
        }
        return $data;
    }

    /**
     * Function to detect bot
     */
    public static function _bot_detected()
    {

        return (
            isset($_SERVER['HTTP_USER_AGENT'])
            && preg_match('/bot|crawl|slurp|spider|mediapartners/i', $_SERVER['HTTP_USER_AGENT'])
        );
    }

    /**
     * Generate Javascript To Redirect When Changing JS/CSS
     */
    public static function generateRedirectScript()
    {
        if (self::_bot_detected()) {
            return "";
        }
        $session = Yii::$app->session;
        $baseUrl = Url::base(true) . '/';
        $currency_code = $session->get('currencycode');
        $country_code = $session->get('countrycode');
        $language_code = $session->get('language');
        //JS to get session value and redirect user to domain based on selected value
        $returnVal = <<<JS
        document.documentElement.setAttribute("data-browser", navigator.userAgent);
        var localeData = {
          currency : "$currency_code",
          country : "$country_code",
          language : "$language_code"
        };

        function setLocaleParameter(key, value){
          localeData[key] = value;
          redirectUrl();
        }

        function redirectUrl(){
          var q = findGetParameter('q');
          var resetURL = '$baseUrl';
          var string = '';
          if(q){
            string = '<input type="text" name="q" value=\'' + q + '\' />';
          }
          var form = $('<form action="'+resetURL+'" method="get" style="display:none;">' + string + '<input type="text" name="regionalData" value=\'' + encodeURIComponent(JSON.stringify(localeData)) + '\' />' +'</form>');
          $('body').append(form);
          $(form).submit();
          return false;
        }

JS;
        return $returnVal;
    }

}
