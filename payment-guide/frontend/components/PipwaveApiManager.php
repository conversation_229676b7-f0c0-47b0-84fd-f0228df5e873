<?php

namespace frontend\components;

use Yii;
use yii\helpers\ArrayHelper;
use common\models\PaymentMethod;
use common\models\PaymentMethodLocalization;
use common\models\PipwaveRequest;
use common\models\PipwaveRequestLog;
use yii\caching\TagDependency;

class PipwaveApiManager {

  /**
  * Function to get pipwave data from DB
  */
  public static function queryDb($country_code,$currency_code,$required_filter){
    $isRecord = PipwaveRequest::find()->where(['currency_code'=>$currency_code,'country_code'=>$country_code])->andWhere(['>','expiry',time()])->one();
    if(empty($isRecord)){
      //If requested data is not available in DB, call pipwave API from this function
      $isRecord = self::queryPipwaveApi($country_code,$currency_code);
    }

    //To check if current request required to do DB filtering (only list of "id" will return if TRUE)
    if($required_filter){
      return ($isRecord ? ArrayHelper::getColumn(PaymentMethodLocalization::find()->where(['currency_code'=>$currency_code,'country_code'=>$country_code])->all(),'payment_method_id') : false);
    } else {
      return $isRecord;
    }
  }

  /**
  * Function to get pipwave data from pipwave API
  */
  private static function queryPipwaveApi($country_code,$currency_code){
    $proxy_enabled = false;
    $timestamp = time();
    $params = Yii::$app->params;

    $proxy_host = $params['PROXY_HOST'];
    $proxy_port = $params['PROXY_PORT'];
    if (isset($proxy_host)) {
       $proxy_enabled = true;
    }

    $data = array(
          "action"=>"get-payment-method-list",
          "api_key"=>$params['pipwave_api_key'],
          "country_code"=>$country_code,
          "currency_code"=>$currency_code,
          "timestamp"=>$timestamp
          );
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL,            $params['pipwave_api_url'] );
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1 );
    curl_setopt($ch, CURLOPT_POST,           1 );
    curl_setopt($ch, CURLOPT_POSTFIELDS,     json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER,     array('Content-Type: application/json','x-api-key:'.$params['pipwave_api_key']));
    curl_setopt($ch, CURLOPT_FAILONERROR,    true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT ,5);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);

    if ($proxy_enabled && !empty($proxy_host)) {
        curl_setopt($ch, CURLOPT_PROXY, $proxy_host);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
    }

    $source = curl_exec($ch);
    $result = json_decode($source);
    if(curl_errno($ch)){
      $expiry = time() + 86400;
      self::generateErrorLog($country_code,$currency_code,strval(curl_errno($ch)),curl_error($ch));
      $isRecord = self::updatePipwaveRequest($country_code,$currency_code,$expiry);
    }
    else {
      if($result->status === 200){
        $expiry = time() + 604800;
        //If pipwave API call was successful, update pipwave request with new expiry time of 1 week
        $isRecord = self::updatePipwaveRequest($country_code,$currency_code,$expiry,$source);
        if($isRecord){
          //Create payment method relationship with local DB data
          self::createPaymentMethodRelationship($country_code,$currency_code,$result->message);
        }

      }
      else{
        $expiry = time() + 86400;
        //If pipwave API call was not successful, update pipwave request with new expiry time of 1 day and generate error log
        $isRecord = self::updatePipwaveRequest($country_code,$currency_code,$expiry);
        self::generateErrorLog($country_code,$currency_code,strval($result->status),$source);
      }
    }
    curl_close($ch);
    return $isRecord;
  }

  /**
  * Function to update pipwave request call
  */
  private static function updatePipwaveRequest($country_code,$currency_code,$expiry,$result=null){
    $model = PipwaveRequest::findOne(['country_code'=>$country_code,'currency_code'=>$currency_code]);
    if($model && $result){
      $model->results = $result;
      $model->expiry = $expiry;
      $model->save();
    }
    else if($model && !$result){
      $model->expiry = $expiry;
      $model->save();
    }
    else if(!$model && $result){
      $model = new PipwaveRequest();
      $model->country_code = $country_code;
      $model->currency_code = $currency_code;
      $model->results = $result;
      $model->expiry = $expiry;
      $model->save();
    }
    else{
      return false;
    }
    return true;
  }


  /**
  * Function to create payment method relationship
  */
  private static function createPaymentMethodRelationship($country_code,$currency_code,$dataArray){
    PaymentMethodLocalization::deleteAll(['country_code'=>$country_code,'currency_code'=>$currency_code]);
    foreach($dataArray as $data){
      $payment_method = PaymentMethod::findOne(["pipwave_payment_id" => $data->code]);
      if($payment_method){
        $model = new PaymentMethodLocalization();
        $model->country_code = $country_code;
        $model->currency_code = $currency_code;
        $model->payment_method_id = $payment_method->id;
        $model->save();
      }
    }
  }

  /**
  * Function to generate error log during pipwave API call if any
  */
  private static function generateErrorLog($country_code,$currency_code,$error_code,$error_msg){
    $params = Yii::$app->params;
    $model = new PipwaveRequestLog();
    $model->country_code = $country_code;
    $model->currency_code = $currency_code;
    $model->results = $error_code;
    $model->results = $error_msg;
    $model->created_at = time();
    $model->save();
  }



  /**
  * Get cache data for payment method
  */
  public static function getFilterData($country_code='', $currency_code=''){

    $country_code = strtoupper(empty($country_code) ? Yii::$app->session->get('countrycode') : $country_code);
    $currency_code = strtoupper(empty($currency_code) ? Yii::$app->session->get('currencycode') : $currency_code);

    $key = 'payment_category/pipwaveAPI/'.$country_code. '/' .$currency_code . '/array';

    return Yii::$app->cache->getOrSet($key, function ($cache) use ($country_code, $currency_code) {
        return self::queryDb($country_code, $currency_code, true);
    },86400 , new TagDependency(['tags' => 'com.payment-guide.pipwaveAPI']));
  }

  /**
  * Get list of pipwave request data 
  */
  public static function getPipwaveRequestResults($country_code='', $currency_code=''){

    $country_code = strtoupper(empty($country_code) ? Yii::$app->session->get('countrycode') : $country_code);
    $currency_code = strtoupper(empty($currency_code) ? Yii::$app->session->get('currencycode') : $currency_code);

    $key = 'pipwaveAPI/pipwave_request/'.$country_code. '/' .$currency_code . '/array';

    return Yii::$app->cache->getOrSet($key, function ($cache) use ($country_code, $currency_code) {
        $data = self::queryDb($country_code, $currency_code, false);
        return $data->results;
    },86400 , new TagDependency(['tags' => 'com.payment-guide.pipwaveAPI']));
  }


}
