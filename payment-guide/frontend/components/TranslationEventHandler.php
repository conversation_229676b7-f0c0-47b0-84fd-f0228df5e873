<?php

namespace frontend\components;

use Yii;
use yii\i18n\MissingTranslationEvent;

// Custom Missing Translation Event Class
class TranslationEventHandler
{
    public static function handleMissingTranslation(MissingTranslationEvent $event)
    {
        // Check Current Translation Language, return english wording for other language, or return key if existing language is english

        if($event->language == "en-US"){
          $event->translatedMessage = Yii::t($event->category, $event->message,$event->data,'sys');
        }
        else{
          $event->translatedMessage = Yii::t($event->category, $event->message,$event->data,'en-US');
        }
    }
}
