<?php
$params = array_merge(
    require(__DIR__ . '/../../common/config/params.php'),
    require(__DIR__ . '/../../common/config/params-local.php'),
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php')
);

return [
    'id' => 'app-frontend',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'frontend\controllers',
    'components' => [
        'request' => [
            'baseUrl' => '/payment-guide',
            'csrfParam' => '_csrf-frontend',
        ],
        'user' => [
            'identityClass' => 'common\models\User',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-frontend', 'httpOnly' => true],
        ],
        'session' => [
            // this is the name of the session cookie used for login on the frontend
            'name' => 'advanced-frontend',
            'cookieParams' => ['lifetime' => 7200]
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'assetManager' => [
            'class' => 'common\components\AssetManager',
        ],
        'urlManager' => [
            'baseUrl' => '/payment-guide',
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [

                '<language:zh-CN|en-US|ID>/search' => 'payment-method/search',
                'search' => 'payment-method/search',
                'pg/<language:zh-CN|en-US|ID>/<cat:(.+)>/<name:(.+)>' => 'user-guide/index',
                'pg/<cat:(.+)>/<name:(.+)>' => 'user-guide/index',
                'local-payment-method/<country:\w{2}>/<currency:\w{3}>' => 'local-payment-method/list',
                '<language:zh-CN|en-US|ID>/<cat:(.+)>/<country:\w{2}>/<currency:\w{3}>' => 'local-payment-method/list',
                '<cat:(.+)>/<country:\w{2}>/<currency:\w{3}>' => 'payment-method/list',
                '<language:zh-CN|en-US|ID>/buy-offgamers-gift-card/<country:\w{2}>' => 'gift-card-merchant/index',
                'buy-offgamers-gift-card/<country:\w{2}>' => 'gift-card-merchant/index',
                'pipwave-query' => 'payment-method/pipwave-query',
                '<language:zh-CN|en-US|ID>' => 'payment-category/index',
                '<language:zh-CN|en-US|ID>/<cat:(.+)>' => 'payment-method/list',
                '<cat:(.+)>' => 'payment-method/list',
                '' => 'payment-category/index',
            ],
        ],
    ],
    'params' => $params,
    

];
