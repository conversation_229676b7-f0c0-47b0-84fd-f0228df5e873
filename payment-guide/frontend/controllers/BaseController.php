<?php

namespace frontend\controllers;

use Yii;
use frontend\components\CookieManager;
use frontend\components\LayoutManager;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;

class BaseController extends \yii\web\Controller
{
    public function init()
    {
        parent::init();
    }

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    '*' => ['GET','HEAD'],
                ],
            ],
        ];
    }

    /**
     * Function to execute before main action
     * @param type $action
     * @return boolean
     */
    public function beforeAction($action)
    {
        $http_host = Yii::$app->params["HTTP_HOST"];
        if(!empty($http_host)){
          Yii::$app->request->setHostInfo($http_host);
        }
        $session = Yii::$app->session;
        $request = Yii::$app->request;
        $session->open();
        // Set Site Title
        Yii::$app->params['site_title'] = Yii::t('app', 'site-title');
        // Get Locale Data From URL
        $url_value = [
            // "country" => $request->get('country'),
            "country" => Yii::$app->params["defaultCountry"],
            "currency" => $request->get('currency'),
            "language" => ($request->get('language') ? $request->get('language') : 'en-US')
        ];
        // Redirect URL If Language is en-US
        if (strcasecmp($request->get('language'), 'en-US') === 0) {
            $url = Yii::$app->request->absoluteUrl;
            $url = preg_replace('/en-US$|en-US\//i', '', $url);
            $this->redirect($url);
            return;
        }

        // Get Locale Data from Cookies If Session not Exists
        if (!$session->get('pg_regional')) {
            CookieManager::getCookie();
            $cookie = $session->get('pg_regional');
            $cookie = json_decode(urldecode($cookie), true);
            self::setLocaleSession($cookie, true);
        }
        // Update Session For Locale Data Change Request (From Button)
        if (!empty($data = $request->get('regionalData'))) {
            $data = json_decode(urldecode($data), true);
            if(json_last_error() === JSON_ERROR_NONE){
              if (!self::setLocaleSession($data, false)) {
                  return $this->redirect(['site/error']);
              }
              // Refresh page to remove locale value from url
              $url = LayoutManager::getRedirectUrl();
              $this->redirect($url);
            }
            else{
              return $this->redirect(['site/error']);
            }
        } else {
            // Validate Locale Data From URL
            if (!self::setLocaleSession($url_value, false)) {
                return $this->redirect(['site/error']);
            }
        }
        Yii::$app->language = $session->get('language');
        parent::beforeAction($action);
        return true;
    }

    /**
     * Validate Locale Parameter Exists & Active in DB
     */
    public static function validateLocaleData($data)
    {
        foreach ($data as $key => $value) {
            switch ($key) {
                case "language":
                     $languageList = ArrayHelper::getColumn((LayoutManager::getLanguageList()), 'iso_code');
                    $languageInput = preg_quote($data['language'], "/");
                    if (count(preg_grep("/^{$languageInput}$/i", $languageList)) === 0) {
                        return false;
                    }
                    break;

                case "country":
                    $countryList = ArrayHelper::getColumn((LayoutManager::getCountryList()), 'country_code');
                    $countryInput = preg_quote($data['country'], "/");
                    if (count(preg_grep("/^{$countryInput}$/i", $countryList)) === 0) {
                        return false;
                    }
                    break;

                case "currency":
                    $currencyList = ArrayHelper::getColumn((LayoutManager::getCurrencyList()), 'currency_code');
                    $currencyInput = preg_quote($data['currency'], "/");
                    if (count(preg_grep("/^{$currencyInput}$/i", $currencyList)) === 0) {
                        return false;
                    }
                    break;
            }
        }
        return true;
    }

    /**
     * Set Session for Language, Country Code , Country Name , Currency Code & Currency Name
     * Check for Changes, and only run script if locale data is change
     */
    private function setLocaleSession($data, $validated)
    {
        $session = Yii::$app->session;
        $isChange = false;
        if ($data['country'] && (!$session->get('countrycode') || $session->get('countrycode') !== $data['country'])) {
            $params = Yii::$app->params;
            $country_code = $params["defaultCountry"];
            $session->set('countrycode', strtoupper($country_code));
            // if ($validated || self::validateLocaleData(array('country' => $data['country']))) {
            //     $session->set('countrycode', strtoupper($data['country']));
            //     $isChange = true;
            // } else {
            //     return false;
            // }
        }
        if ($data['currency'] && (!$session->get('currencycode') || $session->get('currencycode') !== $data['currency'])) {
            if ($validated || self::validateLocaleData(array('currency' => $data['currency']))) {
                $session->set('currencycode', strtoupper($data['currency']));
                $isChange = true;
            } else {
                return false;
            }
        }
        if ($data['language'] && (!$session->get('language') || $session->get('language') !== $data['language'])) {
            if ($validated || self::validateLocaleData(array('language' => $data['language']))) {
                $session->set('language', $data['language']);
                $isChange = true;
            } else {
                return false;
            }
        }
        if ($isChange && !$validated) {
            self::setCookies();
        }
        return true;
    }


    /**
     * Append Changes to Cookies
     */
    private static function setCookies()
    {
        $cookie = Yii::$app->response->cookies;
        $session = Yii::$app->session;
        $params = Yii::$app->params;
        $country_code = $params["defaultCountry"];
        $data = [
            // "country" => $session->get('countrycode'),
            "country" => $country_code,
            "currency" => $session->get('currencycode'),
            "language" => $session->get('language')
        ];
        $data = json_encode($data);
        $session->set('pg_regional', $data);
        $cookie->add(new \yii\web\Cookie([
            'name' => 'pg_regional',
            'value' => $data,
        ]));
    }
}
