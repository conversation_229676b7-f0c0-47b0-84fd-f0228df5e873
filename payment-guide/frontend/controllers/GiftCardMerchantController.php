<?php

namespace frontend\controllers;

use Yii;
use common\models\GiftCardMerchant;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \common\models\GiftCardSubMerchant;
use common\models\GiftCardCountryRelationship;
use common\models\PaymentCategory;

/**
 * GiftCardMerchantController implements the CRUD actions for GiftCardMerchant model.
 */
class GiftCardMerchantController extends BaseController
{

    /**
     * Lists all GiftCardMerchant models.
     * @return mixed
     */
    public function actionIndex()
    {
        $country_code = Yii::$app->session->get('countrycode');

        $country_code = \common\models\Country::find()->where(['country_code' => $country_code])->one();
        //Get Gift Card title and description, which is stored in PaymentCategory with status 5
        $description = PaymentCategory::find()->where(['status' => '5'])->one()->displayValue();
        $description = \yii\helpers\ArrayHelper::toArray($description);

        $gift_card_merchant_ids = GiftCardCountryRelationship::find()
                ->select('gift_card_merchant_id')
                ->where(['country_code' => $country_code])
                ->asArray()
                ->all();
        $ids = array();
        foreach($gift_card_merchant_ids as $k=> $v){
            $ids[] = $v['gift_card_merchant_id'];
        }


        $models = GiftCardMerchant::find()
                ->where(array('id' => $ids))
                ->andWhere(['status' => GiftCardMerchant::STATUS_ACTIVE])
                ->all();

        foreach($models as $v){
          $v->displayValue();
        }
        $models = \yii\helpers\ArrayHelper::toArray($models);

        foreach($models as $k=> $v){
            if($v['is_sub_merchant'] === 1){
                $sub_model = GiftCardSubMerchant::find()
                    ->where(['gift_card_merchant_id' => $v['id'], 'status' => GiftCardSubMerchant::STATUS_ACTIVE])
                    ->all();

                foreach($sub_model as $key => $value){
                    $value->displayValue();
                }
                $sub_model = \yii\helpers\ArrayHelper::toArray($sub_model);
                $models[$k]['merchant-sub'] = $sub_model;
            }
        }


        if ($models === null) {
            throw new NotFoundHttpException;
        }

        return $this->render('index', [
            'models' => $models,
            'description' => $description,
        ]);

    }
}
