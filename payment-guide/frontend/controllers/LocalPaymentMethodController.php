<?php

namespace frontend\controllers;

use Yii;
use yii\web\NotFoundHttpException;
use common\models\PaymentMethod;
use yii\web\Controller;

class LocalPaymentMethodController extends Controller
{
    public function beforeAction($action)
    {
        $http_host = Yii::$app->params["HTTP_HOST"];
        if (!empty($http_host)) {
            Yii::$app->request->setHostInfo($http_host);
        }
        return parent::beforeAction($action);
    }

    public function actionList()
    {
        $request = Yii::$app->request;
        $response = Yii::$app->response;
        $response->format = \yii\web\Response::FORMAT_JSON;
        $country_code = $request->get('country');
        $currency_code = $request->get('currency');

        if (empty($country_code) || empty($currency_code)) {
            throw new NotFoundHttpException;
        }

        $payment_methods = PaymentMethod::getPaymentMethodsUrlAndImage(
            $request->get('country'),
            $request->get('currency')
        );

        $response->headers->add('Pragma', 'no-cache');
        $response->data = array('pm_data' => $payment_methods);

        return $response;
    }
}
