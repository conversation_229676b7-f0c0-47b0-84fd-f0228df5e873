<?php

namespace frontend\controllers;

use Yii;
use yii\caching\TagDependency;
use yii\filters\VerbFilter;
use common\models\PaymentCategory;
use common\models\PaymentMethod;
use yii\helpers\ArrayHelper;
use frontend\components\LayoutManager;
use frontend\components\PipwaveApiManager;

/**
 * PaymentCategoryController implements the CRUD actions for PaymentCategory model.
 */
class PaymentCategoryController extends BaseController
{
    /**
     * Renders the index view for the module
     * @return string
     */
    public function actionIndex()
    {
        //Setting MemCache key for multi-language Menu
        $key = 'payment_category/allMenu/name/' . Yii::$app->session->get('language') . '/array';

        //Get Menu from MemCache
        $data = Yii::$app->cache->get($key);
        $session = Yii::$app->session;

        //If Menu is not available in MemCache query from DB
        if ($data === false) {
            $record = PaymentCategory::find()->where(['status' => PaymentCategory::STATUS_ACTIVE])->orderBy('order_no')->all();
            foreach ($record as $v) {
                //Get translated menu based on language selected
                $v->displayValue();
            }
            $record = ArrayHelper::toArray($record);
            $filterData = PipwaveApiManager::getFilterData();
            //Get data from pipwave API
            $pipwave_data = json_decode(PipwaveApiManager::getPipwaveRequestResults());
            //Convert return data into array
            $pipwave = ArrayHelper::toArray($pipwave_data->message);
            foreach($record as $k=>$v){
                //To define to display the particular category or not
                $record[$k]["display"] = 0;
                //To define the default sorting order for the particular category
                $record[$k]["sorting"] = 10000;
                //Find existing data in DB if match
                $methods[$v['id']] = PaymentMethod::find()->where([
                    'payment_category_id' => $v['id'],
                    'id' => $filterData,
                    'status' => 1
                ])->all();

                //Replace display value for the particular category if having active payment methods
                $record[$k]["display"] = !empty($methods[$v['id']]) ? 1 : 0;

                foreach ($methods[$v['id']] as $method_k => $method_v) {
                    $method_v->displayValue();
                    //Find if this method exist in Pipwave API data
                    $this_pipwave = ArrayHelper::toArray(LayoutManager::search_array($pipwave, "code", $method_v->pipwave_payment_id));
                    if(!empty($this_pipwave)){
                        //Replace the category name by using API return data for ENGLISH
                        if(Yii::$app->session->get('language') == "en-US"){
                            $record[$k]["name"] = $this_pipwave["category"];
                        }
                        //Replace sorting ordering for the category
                        $record[$k]["sorting"] = $record[$k]["sorting"] == 10000 ? $this_pipwave["sorting"] : $record[$k]["sorting"];
                    }
                }
            }

            //Sort the array by using "display" and "sorting" value in the array
            ArrayHelper::multisort($record, ['display', 'sorting'], [SORT_DESC, SORT_ASC]);
            $record = ArrayHelper::index($record, 'id', [], true);
            Yii::$app->cache->set($key, $record, 604800, new TagDependency(
                ['tags' => 'com.payment-guide.menu']
            ));
            $data = $record;
        }
        $filterData = PipwaveApiManager::getFilterData();
        //Get data from pipwave API
        $pipwave_data = json_decode(PipwaveApiManager::getPipwaveRequestResults());
        //Convert return data into array
        $pipwave = ArrayHelper::toArray($pipwave_data->message);
        //New array for "Recommended" payment method
        $recommended_array = array();
        //Index for "Recommended" array
        $recommended_i = 0;
        //Use $data to find payment methods
        foreach($data as $k => $v){
            //To define to display the particular category or not
            $data[$k]["display"] = 0;
            //To define the default sorting order for the particular category
            $data[$k]["sorting"] = 10000;
            //Get category icons for UI display purpose
            $data[$k]["icons"] = LayoutManager::getIcon($data[$k]["url_name"]);
            //For category other than 'store-credit-gift-card' as it doesn't required to compare with data from API
            if($data[$k]['url_name'] != 'store-credit-gift-card'){
                $methods[$v['id']] = PaymentMethod::find()->where([
                    'payment_category_id' => $v['id'],
                    'id' => $filterData,
                    'status' => 1
                ])->all();

                //Replace display value for the particular category if having active payment methods
                $data[$k]["display"] = !empty($methods[$v['id']]) ? 1 : 0;

                foreach ($methods[$v['id']] as $method_k => $method_v) {
                    //Get translated method based on language selected
                    $method_v->displayValue();
                    //Convert into array
                    $method_v = ArrayHelper::toArray($method_v);
                    //To define the default sorting order for the particular method
                    $method_v["sorting"] = 10000;
                    //Find if this method exist in Pipwave API data
                    $this_pipwave = ArrayHelper::toArray(LayoutManager::search_array($pipwave, "code", $method_v["pipwave_payment_id"]));
                    if(!empty($this_pipwave)){
                        
                        //Replace the category name by using API return data for ENGLISH
                        if(Yii::$app->session->get('language') == "en-US"){
                            $method_v["payment_category_id"] = $this_pipwave["category"];
                            $data[$k]["name"] = $this_pipwave["category"];
                        }
                        //Replace sorting ordering for the category
                        $data[$k]["sorting"] = $data[$k]["sorting"] == 10000 ? $this_pipwave["sorting"] : $data[$k]["sorting"];
                        //Replace sorting ordering for the method
                        $method_v["sorting"] = $method_v["sorting"] == 10000 ? $this_pipwave["sorting"] : $method_v["sorting"];

                        //Check if this method is set to "preferred" (Recommended if TRUE)
                        $is_preferred = array_key_exists("preferred", $this_pipwave) ? $this_pipwave["preferred"] : false;
                        if($is_preferred && $is_preferred == true){
                            //Append the method into "Recommended" array
                            $recommended_array[$recommended_i] = ArrayHelper::toArray($method_v);
                            //Define category url name for the method
                            $recommended_array[$recommended_i]["category_url_name"] = $data[$k]["url_name"];
                            //Increase the indexing
                            $recommended_i++;
                        }
                    }
                }
            } else {
                //Find all the method for category 'store-credit-gift-card'
                $methods[$v['id']] = PaymentMethod::find()->where([
                    'payment_category_id' => $v['id'],
                    'status' => 1
                ])->all();

                //Set this category to "display"
                $data[$k]["display"] = 1;
                //Set sorting value for this category to make sure it will always be at bottom of active payment category
                $data[$k]["sorting"] = 10001;

                foreach ($methods[$v['id']] as $method_k => $method_v) {
                    //Get translated method based on language selected
                    $method_v->displayValue();
                }
            }

            $methods[$v['id']] = ArrayHelper::toArray($methods[$v['id']]);
            //Sort the methods array by using "name" value in the array
            ArrayHelper::multisort($methods[$v['id']], ['name'], [SORT_ASC], [SORT_NATURAL | SORT_FLAG_CASE]);
        }

        $data = ArrayHelper::toArray($data);
        //Sort the array by using "display" and "sorting" value in the array
        ArrayHelper::multisort($data, ['display','sorting'], [SORT_DESC, SORT_ASC]);
        
        //Define and reconvert the "Recommended" list into array
        $recommended = ArrayHelper::toArray($recommended_array);
        
        $methods = ArrayHelper::toArray($methods);
        return $this->render('index', [
            'paymentCategory' => $data,
            'paymentMethods' => $methods,
            'recommendeds' => $recommended
        ]);
    }

}