<?php

namespace frontend\controllers;

use Yii;
use yii\web\NotFoundHttpException;
use common\models\PaymentMethod;
use common\models\PaymentCategory;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use frontend\components\PipwaveApiManager;
use yii\helpers\Url;

/**
 * PaymentMethodController implements the CRUD actions for PaymentMethod model.
 */
class PaymentMethodController extends BaseController
{

    public function actionList()
    {
        $request = Yii::$app->request;
        $cat = $request->get('cat');

        if(empty($request->get('country')) || empty($request->get('currency'))){
            $this->redirect(Url::to([(Yii::$app->session->get('language') === 'en-US' ? '/' : Yii::$app->session->get('language') . '/') . $cat . '/' . Yii::$app->session->get('countrycode') . '/' . Yii::$app->session->get('currencycode')],true));
        }

        $payment_category = PaymentCategory::findOne(['url_name' => $cat]);
        //If payment category exist, check for payment method
        if ($payment_category) {
            $payment_category = $payment_category->displayValue();
            $title = $payment_category->name;
            $description = $payment_category->description;
            //Get Payment method id from getFilterData
            $filterData = PipwaveApiManager::getFilterData();
            $model = PaymentMethod::find()->where([
                'payment_category_id' => $payment_category->id,
                'id' => $filterData,
                'status' => 1
            ])->all();

            foreach ($model as $k => $v) {
                $v->displayValue();
            }

            $model = ArrayHelper::toArray($model);
        } else {
            throw new NotFoundHttpException;
        }
        return $this->render('index', [
            'title' => $title,
            'model' => $model,
            'cat' => $cat,
            'description' => $description
        ]);
    }

    public function actionSearch($q = null)
    {
        $q = \yii\helpers\Html::encode($q);
        $q=trim($q);
        if($q!=='') {
            $query = (new \yii\db\Query())->select([
                    'category',
                    'message'
                ])->from('source_message')->innerJoin('message', 'source_message.id = message.id')->where([
                    'or',
                    [
                        'and',
                        [
                            'source_message.category' => [
                                'payment-category',
                                'payment-gateway'
                            ]
                        ],
                        [
                            'like',
                            'source_message.message',
                            'name'
                        ],
                        [
                            'like',
                            'message.translation',
                            $q
                        ]
                    ],
                    [
                        'and',
                        ['source_message.category' => 'payment-method'],
                        [
                            'or',
                            [
                                'like',
                                'source_message.message',
                                'name'
                            ],
                            [
                                'like',
                                'source_message.message',
                                'description'
                            ]
                        ],
                        [
                            'like',
                            'message.translation',
                            $q
                        ]
                    ]
                ])->distinct()->all();
        }
        if (!empty($query)) {
            $array = array(
                'payment_category' => [],
                'payment_gateway' => [],
                'payment_method-name' => [],
                'payment_method-description' => []
            );
            foreach ($query as $data) {
                switch ($data["category"]) {
                    case 'payment-category':
                        array_push($array['payment_category'], $data['message']);
                        break;
                    case 'payment-gateway':
                        array_push($array['payment_gateway'], $data['message']);
                        break;
                    case 'payment-method':
                        if (strpos($data["message"], 'name')) {
                            array_push($array['payment_method-name'], $data['message']);
                        } else {
                            array_push($array['payment_method-description'], $data['message']);
                        }
                        break;
                }
            }

            $query2 = (new \yii\db\Query())->select(['payment_method.id'])->from('payment_method')->innerJoin('payment_category', 'payment_method.payment_category_id = payment_category.id')->innerJoin('payment_gateway', 'payment_method.payment_gateway_id = payment_gateway.id')->where([
                        'or',
                        ['payment_method.name' => $array['payment_method-name']],
                        ['payment_method.description' => $array['payment_method-description']],
                        ['payment_category.name' => $array['payment_category']],
                        ['payment_gateway.name' => $array['payment_gateway']]
                    ])->distinct()->all();
            $filterData = PipwaveApiManager::getFilterData();
            $model = PaymentMethod::find()->where(array('id' => $query2))->andWhere(['id' => $filterData])->andWhere(['status' => 1])->all();

            foreach ($model as $data) {
                $data->displayValue();
            }
            $model = ArrayHelper::toArray($model);

            return $this->render('index', [
                'title' => Yii::t('app', 'search-prefix-title') . ": " . $q,
                'model' => $model,
                'cat' => 'e-wallet',
                'description' => ''
            ]);
        }
        return $this->render('index', [
            'title' => "Search Result: " . $q,
            'description' => ''
        ]);

    }
}
