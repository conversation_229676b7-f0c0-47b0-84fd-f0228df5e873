<?php

namespace frontend\controllers;

use Yii;
use common\models\UserGuide;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \common\models\PaymentMethod;
use \common\models\PaymentCategory;
use \common\models\UserGuideVisitors;
use yii\caching\TagDependency;
use yii\db\Expression;
use dpodium\filemanager\components\FilemanagerHelper;
use dpodium\filemanager\models\Files;
use frontend\components\PipwaveApiManager;
use frontend\components\LayoutManager;
use yii\helpers\ArrayHelper;

/**
 * UserGuideController implements the CRUD actions for UserGuide model.
 */
class UserGuideController extends BaseController
{
    /**
     * Renders the index view for the module
     * @return string
     */
    public function actionIndex()
    {
        $name = Yii::$app->request->get('name');

        $language_code = Yii::$app->session->get('language');

        $payment_method_id = PaymentMethod::find()
            ->where(['url_name' => $name, 'status' => PaymentMethod::STATUS_ACTIVE])
            ->one();

        if ($payment_method_id) {
            //Get User Guide Data from actionGuide Function
            $model = $this->actionGuide($payment_method_id->id, $language_code);
            if ($model === null) {
                $model = $this->actionGuide($payment_method_id->id, 'en-US');
                return $this->render('index', ['model' => $model,]);
            }

            //User Visit Count
            $retrun = LayoutManager::_bot_detected();
            if ($retrun == false) {
                $visit_count = Yii::$app->session->get('visit_count_' . $model->id);
                if (!$visit_count) {
                    $model_visit = UserGuideVisitors::find()->where(['user_guide_id' => $model->id])->one();
                    if ($model_visit) {
                        UserGuideVisitors::updateAll(['visit_count' => new Expression('visit_count+1')], 'user_guide_id = ' . $model->id);
                    } else {
                        $model_visit = new UserGuideVisitors;
                        $model_visit->user_guide_id = $model->id;
                        $model_visit->visit_count = 1;
                        $model_visit->status = 1;
                        $model_visit->save();
                    }
                    Yii::$app->session->set('visit_count_' . $model->id, 1);
                }
            }

        } else {
            throw new NotFoundHttpException;
        }

        return $this->render('index', [
            'model' => isset($model) ? $model : null,
        ]);
    }

    /**
     *Get User Guide Content
     */
    public function actionGuide($id = '', $language_code = '')
    {
        $model = UserGuide::findOne(['payment_method_id' => $id, 'language_code' => $language_code, 'status' => UserGuide::STATUS_ACTIVE]);

        if ($model) {
            $model->content = preg_replace('#(<img.*?src="(.*?)"[^\>]+>)#', '<div class="imgbox"><a href="$2" target="_blank">$1</a></div>', $model->content);


        }
        return $model;
    }

    /**
     * Get List for UserGuide right bar
     */
    public static function actionList($id = '')
    {

        if (!isset($id)) {
            $id = 0;
        }

        $cat = Yii::$app->request->get('cat');
        $name = Yii::$app->request->get('name');
        $country_code = Yii::$app->session->get('countrycode');
        $currency_code = Yii::$app->session->get('currencycode');
        $language_code = Yii::$app->session->get('language');

        $models = array();

        $payment_category_id = PaymentMethod::find(['payment_category_id', 'id'])
            ->where(['url_name' => $name, 'status' => PaymentMethod::STATUS_ACTIVE])
            ->one();

        if ($payment_category_id) {
            $payment_category_id = $payment_category_id->payment_category_id;

            $payment_category_name = PaymentCategory::findOne($payment_category_id)->displayValue();
            $payment_category_name = $payment_category_name->name;


            $payment_method_ids = PaymentMethod::find()
                ->select(['id'])
                ->where(['payment_category_id' => $payment_category_id, 'status' => PaymentMethod::STATUS_ACTIVE])
                ->asArray()
                ->all();

            $ids = array();
            foreach ($payment_method_ids as $k => $v) {
                $ids[] = $v['id'];
            }
            
            $models = UserGuide::find()
                ->select(['user_guide.id', 'user_guide.name', 'user_guide.payment_method_id'])
                ->join('LEFT JOIN', 'user_guide_visitors', 'user_guide.id = user_guide_visitors.user_guide_id')
                ->join('LEFT JOIN', 'payment_method_localization', 'user_guide.payment_method_id = payment_method_localization.payment_method_id')
                ->where(array('user_guide.payment_method_id' => $ids))
                ->andWhere(['user_guide.language_code' => $language_code, 'user_guide.status' => UserGuide::STATUS_ACTIVE])
                ->andWhere(['<>', 'user_guide.id', $id])
                ->andWhere(['payment_method_localization.currency_code' => $currency_code, 'payment_method_localization.country_code' => $country_code])
                ->orderBy('user_guide_visitors.visit_count DESC')
                ->limit(20)
                ->asArray()
                ->all();

            foreach ($models as $k => $v) {
                $payment_method_name = PaymentMethod::find('name', 'url_name')
                    ->where(['id' => $v['payment_method_id'], 'status' => PaymentMethod::STATUS_ACTIVE])
                    ->one();

                $models[$k]['payment_method_url_name'] = $payment_method_name->url_name;

                $payment_method_name = str_replace(' ', '-', strtolower($payment_method_name->name));
                $models[$k]['payment_method_name'] = $payment_method_name;

            }
        }

        if ($models === null) {
            throw new NotFoundHttpException;
        }

        $list = [
            'models' => $models,
            'payment_category_name' => $payment_category_name,
            'cat' => $cat,
        ];
        return $list;
    }

    public static function actionLocalization()
    {

        $name = Yii::$app->request->get('name');

        $payment_method_name = PaymentMethod::find()
            ->where(['url_name' => $name, 'status' => PaymentMethod::STATUS_ACTIVE])
            ->one();

        $filterData = PipwaveApiManager::getFilterData();
        $payment_method_id_localization = PaymentMethod::find()
            ->where(['url_name' => $name, 'id' => $filterData, 'status' => PaymentMethod::STATUS_ACTIVE])
            ->one();

        $payment_method_name = $payment_method_name->displayValue();

        if (!$payment_method_id_localization) {
            return $payment_method_name->name;
        } else {
            return $payment_method_id_localization;
        }

        return false;
    }

}
