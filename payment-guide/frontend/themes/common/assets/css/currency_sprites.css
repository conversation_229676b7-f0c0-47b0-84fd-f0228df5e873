.cur {
    background: url(currency_sprites.png) no-repeat;
    width: 32px;
    height: 32px;
    background-position: -285px -75px;
}

.cur-afn {
    background-position: -5px -5px;
}

.cur-all {
    background-position: -40px -5px;
}

.cur-alq {
    background-position: -75px -5px;
}

.cur-dzd {
    background-position: -110px -5px;
}

.cur-aoa {
    background-position: -145px -5px;
}

.cur-awg {
    background-position: -180px -5px;
}

.cur-aud {
    background-position: -215px -5px;
}

.cur-bsd {
    background-position: -250px -5px;
}

.cur-bhd {
    background-position: -285px -5px;
}

.cur-bdt {
    background-position: -320px -5px;
}

.cur-bbd {
    background-position: -355px -5px;
}

.cur-byr {
    background-position: -390px -5px;
}

.cur-bzd {
    background-position: -425px -5px;
}

.cur-bmd {
    background-position: -460px -5px;
}

.cur-btn_che {
    background-position: -495px -5px;
}

.cur-btn {
    background-position: -530px -5px;
}

.cur-bam {
    background-position: -565px -5px;
}

.cur-bwp {
    background-position: -600px -5px;
}

.cur-none {
    background-position: -5px -40px;
}

.cur-brl {
    background-position: -40px -40px;
}

.cur-bpennies {
    background-position: -75px -40px;
}

.cur-gbp {
    background-position: -110px -40px;
}

.cur-bnd {
    background-position: -145px -40px;
}

.cur-bgn {
    background-position: -180px -40px;
}

.cur-bif {
    background-position: -215px -40px;
}

.cur-khr {
    background-position: -250px -40px;
}

.cur-cad {
    background-position: -285px -40px;
}

.cur-cve {
    background-position: -320px -40px;
}

.cur-kyd {
    background-position: -355px -40px;
}

.cur-cent {
    background-position: -390px -40px;
}

.cur-xaf {
    background-position: -425px -40px;
}

.cur-clp {
    background-position: -460px -40px;
}

.cur-cny {
    background-position: -495px -40px;
}

.cur-cop {
    background-position: -530px -40px;
}

.cur-kmf {
    background-position: -565px -40px;
}

.cur-crc {
    background-position: -600px -40px;
}

.cur-hrk {
    background-position: -5px -75px;
}

.cur-hrk_2 {
    background-position: -40px -75px;
}

.cur-cup_convertiblepeso {
    background-position: -75px -75px;
}

.cur-cup {
    background-position: -110px -75px;
}

.cur-czk_haler {
    background-position: -145px -75px;
}

.cur-czk {
    background-position: -180px -75px;
}

.cur-djf {
    background-position: -215px -75px;
}

.cur-std {
    background-position: -250px -75px;
}

.cur-usd {
    background-position: -285px -75px;
}

.cur-dop {
    background-position: -320px -75px;
}

.cur-xcd {
    background-position: -355px -75px;
}

.cur-egp_piastre {
    background-position: -390px -75px;
}

.cur-egp {
    background-position: -425px -75px;
}

.cur-ern {
    background-position: -460px -75px;
}

.cur-etb {
    background-position: -495px -75px;
}

.cur-eur {
    background-position: -530px -75px;
}

.cur-eur_2 {
    background-position: -565px -75px;
}

.cur-exchange {
    background-position: -600px -75px;
}

.cur-fjd {
    background-position: -5px -110px;
}

.cur-frf {
    background-position: -40px -110px;
}

.cur-gmd {
    background-position: -75px -110px;
}

.cur-gel {
    background-position: -110px -110px;
}

.cur-ghc {
    background-position: -145px -110px;
}

.cur-gtq {
    background-position: -180px -110px;
}

.cur-gnf {
    background-position: -215px -110px;
}

.cur-gyd {
    background-position: -250px -110px;
}

.cur-htg {
    background-position: -285px -110px;
}

.cur-hkd {
    background-position: -320px -110px;
}

.cur-huf {
    background-position: -355px -110px;
}

.cur-isk {
    background-position: -390px -110px;
}

.cur-inr {
    background-position: -425px -110px;
}

.cur-idr {
    background-position: -460px -110px;
}

.cur-irr {
    background-position: -495px -110px;
}

.cur-iqd {
    background-position: -530px -110px;
}

.cur-ils {
    background-position: -565px -110px;
}

.cur-jmd {
    background-position: -600px -110px;
}

.cur-jpy {
    background-position: -5px -145px;
}

.cur-jod {
    background-position: -40px -145px;
}

.cur-kes {
    background-position: -75px -145px;
}

.cur-kwd {
    background-position: -110px -145px;
}

.cur-kgs {
    background-position: -145px -145px;
}

.cur-lak {
    background-position: -180px -145px;
}

.cur-lat {
    background-position: -215px -145px;
}

.cur-lat_2 {
    background-position: -250px -145px;
}

.cur-lsl {
    background-position: -285px -145px;
}

.cur-lrd {
    background-position: -320px -145px;
}

.cur-lyd {
    background-position: -355px -145px;
}

.cur-ltl {
    background-position: -390px -145px;
}

.cur-ltl_2 {
    background-position: -425px -145px;
}

.cur-mop {
    background-position: -460px -145px;
}

.cur-mkd {
    background-position: -495px -145px;
}

.cur-mgf {
    background-position: -530px -145px;
}

.cur-mwk {
    background-position: -565px -145px;
}

.cur-myr {
    background-position: -600px -145px;
}

.cur-mvr {
    background-position: -5px -180px;
}

.cur-mro {
    background-position: -40px -180px;
}

.cur-mxn {
    background-position: -75px -180px;
}

.cur-mill {
    background-position: -110px -180px;
}

.cur-mnt {
    background-position: -145px -180px;
}

.cur-mad {
    background-position: -180px -180px;
}

.cur-mzm {
    background-position: -215px -180px;
}

.cur-mmk {
    background-position: -250px -180px;
}

.cur-nad {
    background-position: -285px -180px;
}

.cur-ang {
    background-position: -320px -180px;
}

.cur-nzd {
    background-position: -355px -180px;
}

.cur-nio {
    background-position: -390px -180px;
}

.cur-ngn {
    background-position: -425px -180px;
}

.cur-omr {
    background-position: -460px -180px;
}

.cur-pab {
    background-position: -495px -180px;
}

.cur-pyg {
    background-position: -530px -180px;
}

.cur-pen {
    background-position: -565px -180px;
}

.cur-php {
    background-position: -600px -180px;
}

.cur-grosz {
    background-position: -5px -215px;
}

.cur-pln {
    background-position: -40px -215px;
}

.cur-gbp_2 {
    background-position: -75px -215px;
}

.cur-qar {
    background-position: -110px -215px;
}

.cur-rupee {
    background-position: -145px -215px;
}

.cur-rwf {
    background-position: -180px -215px;
}

.cur-wst {
    background-position: -215px -215px;
}

.cur-sar {
    background-position: -250px -215px;
}

.cur-csd {
    background-position: -285px -215px;
}

.cur-scr {
    background-position: -320px -215px;
}

.cur-sll {
    background-position: -355px -215px;
}

.cur-sgd {
    background-position: -390px -215px;
}

.cur-sbd {
    background-position: -425px -215px;
}

.cur-sos {
    background-position: -460px -215px;
}

.cur-zar {
    background-position: -495px -215px;
}

.cur-special_drawing {
    background-position: -530px -215px;
}

.cur-lkr {
    background-position: -565px -215px;
}

.cur-stock_exchange {
    background-position: -600px -215px;
}

.cur-srg {
    background-position: -5px -250px;
}

.cur-szl {
    background-position: -40px -250px;
}

.cur-sek {
    background-position: -75px -250px;
}

.cur-chf {
    background-position: -110px -250px;
}

.cur-twd {
    background-position: -145px -250px;
}

.cur-thb {
    background-position: -180px -250px;
}

.cur-transaction_fee {
    background-position: -215px -250px;
}

.cur-ttd {
    background-position: -250px -250px;
}

.cur-tnd {
    background-position: -285px -250px;
}

.cur-ugx {
    background-position: -320px -250px;
}

.cur-uah {
    background-position: -355px -250px;
}

.cur-aed {
    background-position: -390px -250px;
}

.cur-uss {
    background-position: -425px -250px;
}

.cur-uyu {
    background-position: -460px -250px;
}

.cur-vuv {
    background-position: -495px -250px;
}

.cur-vef {
    background-position: -530px -250px;
}

.cur-vnd {
    background-position: -565px -250px;
}

.cur-bceao {
    background-position: -600px -250px;
}

.cur-krw {
    background-position: -5px -285px;
}

.cur-yen_2 {
    background-position: -40px -285px;
}

.cur-zmk {
    background-position: -75px -285px;
}

.cur-zwd {
    background-position: -110px -285px;
}

.cur-ars {
    background-position: -145px -285px;
}

.cur-amd {
    background-position: -180px -285px;
}

.cur-azn {
    background-position: -215px -285px;
}

.cur-byn {
    background-position: -250px -285px;
}

.cur-xof {
    background-position: -285px -285px;
}

.cur-bob {
    background-position: -320px -285px;
}

.cur-nok {
    background-position: -355px -285px;
}

.cur-cdf {
    background-position: -390px -285px;
}

.cur-cuc {
    background-position: -425px -285px;
}

.cur-dkk {
    background-position: -460px -285px;
}

.cur-fkp {
    background-position: -495px -285px;
}

.cur-xpf {
    background-position: -530px -285px;
}

.cur-ghs {
    background-position: -565px -285px;
}

.cur-gip {
    background-position: -600px -285px;
}

.cur-hnl {
    background-position: -5px -320px;
}

.cur-kzt {
    background-position: -40px -320px;
}

.cur-lbp {
    background-position: -75px -320px;
}

.cur-mga {
    background-position: -110px -320px;
}

.cur-mur {
    background-position: -145px -320px;
}

.cur-mdl {
    background-position: -180px -320px;
}

.cur-mzn {
    background-position: -215px -320px;
}

.cur-npr {
    background-position: -250px -320px;
}

.cur-kpw {
    background-position: -285px -320px;
}

.cur-pkr {
    background-position: -320px -320px;
}

.cur-pgk {
    background-position: -355px -320px;
}

.cur-ron {
    background-position: -390px -320px;
}

.cur-rub {
    background-position: -425px -320px;
}

.cur-shp {
    background-position: -460px -320px;
}

.cur-rsd {
    background-position: -495px -320px;
}

.cur-ssp {
    background-position: -530px -320px;
}

.cur-sdg {
    background-position: -565px -320px;
}

.cur-srd {
    background-position: -600px -320px;
}

.cur-syp {
    background-position: -5px -355px;
}

.cur-tjs {
    background-position: -40px -355px;
}

.cur-tzs {
    background-position: -75px -355px;
}

.cur-top {
    background-position: -110px -355px;
}

.cur-try {
    background-position: -145px -355px;
}

.cur-tmt {
    background-position: -180px -355px;
}

.cur-uzs {
    background-position: -215px -355px;
}

.cur-yer {
    background-position: -250px -355px;
}

.cur-zmw {
    background-position: -285px -355px;
}