var globalTimeout = null;
var navRegion = false;
var navCurrency = false;
// Open Side Navigation Menu
function openNav() {
  document.getElementById("mySidenav").style.width = "350px";
  // document.getElementById("mainWrapper").style.marginLeft = "0";
  //document.body.style.backgroundColor = "#f9f9f9";
}
// Close Side Navigation Menu
function closeNav() {
  document.getElementById("mySidenav").style.width = "0";
  //  document.getElementById("mainWrapper").style.marginLeft= "0";
  //	document.body.style.backgroundColor = "#f9f9f9";
}

// Open Searchbar
function openSearch() {
  document.getElementById("mySearch").style.width = "350px";
  document.getElementById("mainWrapper").style.marginRight = "0";
}
// Close Searchbar
function closeSearch() {
  document.getElementById("mySearch").style.width = "0";
  document.getElementById("mainWrapper").style.marginRight = "0";
}

// Localization
function openNavRegion() {
  document.getElementById("myRegion").style.width = "100%";
}

function closeNavRegion() {
  document.getElementById("myRegion").style.width = "0%";
}

function openNavCurrency() {
  document.getElementById("myCurrency").style.width = "100%";
}

function closeNavCurrency() {
  document.getElementById("myCurrency").style.width = "0%";
}

// When the user scrolls down 20px from the top of the document, show the button
window.onscroll = function() {
  scrollFunction()
};

function scrollFunction() {
  var btn = document.getElementById("btn-gototop");
  if (btn) {
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
      btn.style.display = "block";
    } else {
      btn.style.display = "none";
    }
  }
}
// When the user clicks on the button, scroll to the top of the document
function topFunction() {
  document.body.scrollTop = 0;
  document.documentElement.scrollTop = 0;
}

// Expand extra info
function toggleItem(button) {

  var id = button.id;
  id = id.replace("Content-pg-expand-", "");

  if ($(button).attr('toggle') != 'true') {
    $(button).parentsUntil($(".Content-pg")).find($("#expand-" + id)).addClass("expand-show");
    $(button).removeClass("fa-chevron-circle-down").addClass('fa-chevron-circle-up').attr('toggle', 'true');
  } else {
    $(button).parentsUntil($(".Content-pg")).find($("#expand-" + id)).removeClass("expand-show");
    $(button).removeClass("fa-chevron-circle-up").addClass('fa-chevron-circle-down').attr('toggle', 'false');
  }
}

function findGetParameter(parameterName) {
    var result = null,
        tmp = [];
    var items = location.search.substr(1).split("&");
    for (var index = 0; index < items.length; index++) {
        tmp = items[index].split("=");
        if (tmp[0] === parameterName) result = decodeURIComponent(tmp[1]);
    }
    return result;
}

$(document).ready(function() {
  $('#country-search').keyup(function() {
    if (globalTimeout != null) {
      clearTimeout(globalTimeout);
    }
    globalTimeout = setTimeout(function() {
      var searchString = $('#country-search').val();
      if (searchString === "") {
        $("#myRegion > .overlay-content > h2").show();
        $("#myRegion > .overlay-content > .overlayInfo").show();
      } else {
        $("#myRegion > .overlay-content > h2").hide();
        $("#myRegion > .overlay-content > .overlayInfo").hide();
        var $el = $(".flag[title*=" + searchString + " i]").parentsUntil($(".overlayInfo"));
        $el.parent().fadeIn(450);
      }
    }, 500);
  });

  $('#currency-search').keyup(function() {
    if (globalTimeout != null) {
      clearTimeout(globalTimeout);
    }
    globalTimeout = setTimeout(function() {
      var searchString = $('#currency-search').val();
      if (searchString === "") {
        $("#myCurrency > .overlay-content > .overlayInfo").show();
      } else {
        $("#myCurrency > .overlay-content > .overlayInfo").hide();
        var $el = $(".cur[title*=" + searchString + " i]").parentsUntil($(".overlayInfo"));
        $el.parent().fadeIn(450);
      }
    }, 500);
  });
});
