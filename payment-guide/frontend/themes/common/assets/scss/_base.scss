html {
    // scroll-behavior: smooth !important;
}

body.v2 {

    background-color: $color-skin;
    margin: 0px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-family: Helvetica, Arial;
    font-size: $font-size;
    position: relative;
    scroll-behavior: smooth !important;

    * {
        box-sizing: border-box;
    }

    main {
        display: flex;
        flex-wrap: wrap;

        .container {
            box-sizing: border-box;
            margin: 0 auto;
            padding: 32px;
        }

        .content-page {
            display: flex;
            flex-wrap: wrap;
            min-height: 100vh !important;
            height: fit-content;
            width: 100%;
            background-color: $color-white;
            @media (min-width: (($grid__bp-lg * 1px))) {
                margin-left: 300px;
                width: calc(100% - 300px) !important;
            }
        }
    
        .side-bar {
            bottom: 0;
            top: auto;
            width: 100%;
            position: relative;
            background-color: $color-skin;
            margin-inline-start: auto;
            
            &-desktop {
                position: fixed;
                min-width: 300px !important;
                max-width: 300px !important;
                max-height: 100vh;
                padding: $font-size;
                overflow: auto !important;
               
                @media (min-width: (($grid__bp-lg * 1px))) {
                    min-height: 100vh;
                    height: 100%;
                }
                
                .header{
                    background-color: $color-pale-skin;
                    margin: $font-size * (-1);
                    padding-bottom: $font-size;
    
                    &-img {
                        max-width: 300px;
                        padding-top: 35px;
                        padding-bottom: $font-size;
                        padding-left: 32px;
                        padding-right: 16px;
                        margin-left: $font-size;
                        margin-right: $font-size;
                        cursor: pointer;

                    }

                    .v2-select {
                        position: relative;
                        width: inherit;
                        height: 40px;
                        border-radius: 100px;
                        outline: none;
                        border: 1px solid $color-bold-skin;
                        background-color: $main-color !important;
                        font-size: 14px;
                        margin-left: 48px;
                        margin-right: 32px;
        
                        &:hover{
                            border: 1px solid $color-pale-skin;
                        }
                        
                        &:focus{
                            border: 1px solid $color-pale-skin;
                        }
    
                        &-selected-text {
                            color: $color-skin;
                            height: inherit;
                            padding-top: 12px;
                            padding-left: 15px;
                            padding-bottom: 11px;
                            padding-right: $font-size !important;
                            background: url('../images/select_arrow.png')no-repeat 100%;
                            background-origin: content-box;
                            font-size: 14px;
                            max-width: 100%;
                            overflow: hidden !important;
                            cursor: pointer;
                            filter: brightness(0) invert(1) ;
    
                            div {
                                width: 90%;
                                overflow: hidden;
                                text-overflow: ellipsis !important;
                                white-space: nowrap;
                            }
                        }
    
                        &-content {
                            display: none;
                            position: absolute;
                            z-index: 1000;
                            min-height: 300px;
                            max-height: 300px;
                            outline: none;
                            left: 0px !important;
                            right: 0px !important;
                            background-color: $color-white !important;
                            border: 1px solid $color-bold-skin;
                            overflow: hidden;
    
                            &.isShow{
                                display: block !important;
                            }

                            &-header {
                              display: none;
                            }
    
                            &-search {
                                width: 100%;
                                height: 41px;
                                outline: none;
                                border: none;
                                padding-top: 12px;
                                padding-left: 15px;
                                padding-bottom: 11px;
                                padding-right: $font-size !important;
                                color: $color-dark-grey;
                                border-bottom: 1px solid $color-bold-skin;
                                font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                                background: url('../images/search_logo.png')no-repeat 100%;
                                background-color: $color-white !important;
                                background-origin: content-box;
                                font-size: 14px;
    
                                &:placeholder {
                                    color: $color-dark-grey;
                                    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
                                }
                
                                &:hover{
                                    border: 1px solid $color-pale-skin;
                                }
                
                                &:focus{
                                    border: 1px solid $color-pale-skin;
                                }
                            }
    
                            &-options {
                                max-height: 259px;
                                overflow: auto !important;
    
                                &-item {
                                    color: $color-dark-grey;
                                    padding-left: 15px;
                                    padding-top: 5px;
                                    padding-bottom: 5px;
                                    line-height: 20px;
                                    left:0;
                                    font-size: 14px;
                                    cursor: pointer;
                                    
                                    &:hover {
                                        background-color: $color-pale-skin;
                                        color: $color-dark-grey;
                                    }
    
                                    &-selected {
                                        background-color: $color-pale-skin;
                                        color: $color-dark-grey;
                                    }
                                }
                            }
                        }
                    }
                }

                & > * {
                    margin-left: auto;
                    max-width: 300px;
                }
            }
    
            &-mobile {
                padding: 15px;
                min-height: 0 !important;
                height: auto;

                .header{
                    background-color: $color-pale-skin;
                    margin: $font-size * (-1);
                    padding-bottom: $font-size;
                    padding-top: inherit;
    
                    &-img {
                        width: 100px;
                        display: block;
                        margin-top: 20px;
                        margin-bottom: 19px;
                        margin-left: auto !important;
                        margin-right: auto !important;
                        cursor: pointer;

                        img {
                            width: 100px !important;
                        }
                    }

                    .v2-select {

                        @media (max-width: (($grid__bp-lg * 1px) - 1px)){
                            position: relative;
                            width: inherit;
                            height: 40px;
                            border-radius: 100px;
                            outline: none;
                            border: 1px solid $color-bold-skin;
                            background-color: $main-color !important;
                            font-size: 14px;
                            margin-left: $font-size;
                            margin-right: $font-size;
                        
                            &-selected-text {
                                color: $color-skin;
                                height: inherit;
                                padding-top: 12px;
                                padding-left: 15px;
                                padding-bottom: 11px;
                                padding-right: $font-size !important;
                                background: url('../images/select_arrow.png')no-repeat 100%;
                                background-origin: content-box;
                                font-size: 14px;
                                max-width: 100%;
                                overflow: hidden !important;
                                cursor: pointer;
                                filter: brightness(0) invert(1) ;
                                transition: all 0.3s;
        
                                div {
                                    width: 90%;
                                    overflow: hidden;
                                    text-overflow: ellipsis !important;
                                    white-space: nowrap;
                                }
                        
                                &:active {
                                    position: relative;
                                    top: 1px;
                                }
                            }
                        
                            &-content {
                                opacity: 0;
                                visibility: hidden;
                                visibility: 0s 0.5s;
                                z-index: 5;
                                position: fixed;
                                width: 100%;
                                height: 100%;
                                top: 0;
                                left: 0;
                                background: $color-white;
                                transform: translateY(200px);
	                            transition: opacity 0.5s, transform 0.5s, z-index 0s 0.5s;
                                transition: all 0.3s;
                                overflow-y: scroll;
                                min-height:100% !important;

                                &-header {
                                    width: 100%;
                                    height: 41px;
                                    padding-top: 12px;
                                    display: flex;
                                    justify-content: center;
                                    border-bottom: 1px solid $color-bold-skin;
                                    background-color: $color-white;
                                    position: sticky;
                                    top: 0;
                                    z-index: 1;

                                    &-title {
                                        font-size: 16px;
                                        text-align: center;
                                        font-weight: 600;
                                        color: $color-dark-grey;

                                    }
                                }

                                &-search {
                                    width: 100%;
                                    height: 41px;
                                    outline: none;
                                    border: none;
                                    padding-top: 12px;
                                    padding-left: 15px;
                                    padding-bottom: 11px;
                                    padding-right: $font-size !important;
                                    color: $color-dark-grey;
                                    border-bottom: 1px solid $color-bold-skin;
                                    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                                    background: url('../images/search_logo.png')no-repeat 100%;
                                    background-color: $color-white !important;
                                    background-origin: content-box;
                                    font-size: 14px;
                                    position: sticky;
                                    top: 41px;
                                    z-index: 1;
                                }
                        
                                &-options {
                                    position: relative;
                                    height: 100%;
                                    font-size: 14px;
                            
                                    &-item {
                                        font-weight: 300;
                                        text-decoration: none;
                                        display: block;
                                        color: #1d1f20;
                                        padding-top: 10px;
                                        padding-bottom: 10px;
                                        padding-left: 20px;

                                        &-selected {
                                            background-color: $color-pale-skin;
                                        }
                                    }
                                }
                            }
                        
                            .overlay-open {
                                opacity: 1;
                                visibility: visible;
                                z-index: 15;
                                transform: translateY(0);
		                        transition: opacity 0.5s, transform 0.5s, z-index 0s;
                            }
                        
                            .overlay-close {
                                position: absolute;
                                right: 16px;
                                top: 12px;
                                cursor: pointer;
                                transition: all 0.3s;

                                &:after {
                                    font-family: "FontAwesome";
                                    content: '\f00d';
                                    font-size: 14px;                               
                                }
                            }
                        } 
                    }
                }

                .back {
                    display: block;
                    margin-top: auto;
                    margin-bottom: auto;
                    height: 100%;
                    padding: $font-size;

                    img {
                        height: 16px;
                        width: 9.6px;
                    }
                }

                .no-x-margin {
                    margin-top: -15px;
                    margin-bottom: -15px;
                }

                &.side-bar-sticky-top {
                    background-color: $color-skin;
                    z-index: 1;
                    min-height: 70px !important;
                    height: 70px;
                    position: fixed !important;
                    top: 0;

                    &-large {
                        @extend.side-bar-sticky-top;
                        height: 270px !important;
                    }
                }
            }
    
            .search-form {
                padding-top: 32px;

                @media (min-width: (($grid__bp-lg * 1px))) {
                    padding: 32px $font-size 0 32px;
                }
    
                .input-icons {
                    width: 100%;
                    height: 40px;
                    border-radius: 100px;
                    outline: none;
                    padding-top: 12px;
                    padding-left: 15px;
                    padding-bottom: 11px;
                    padding-right: $font-size !important;
                    margin-bottom: $font-size;
                    color: $color-dark-grey;
                    border: 1px solid $color-bold-skin;
                    background: url('../images/search_logo.png')no-repeat 100%;
                    background-color: $color-white !important;
                    background-origin: content-box;
                    font-size: 14px;
    
                    &:hover{
                        border: 1px solid $main-color;
                    }
    
                    &:focus{
                        border: 1px solid $main-color;
                    }
                }
    
               
            }
    
            .nav-bar {
                top: 0;
                //position: relative;
                
                @media (min-width: (($grid__bp-lg * 1px))) {
                    padding-top: $font-size;
                    padding-bottom: $font-size;
                    padding-left: $font-size;
                    padding-right: $font-size;
                    position: relative;
                }
                
    
                &-category {
                    padding-top: $font-size;
                    padding-bottom: 19px;
                    padding-left: $font-size;
                    padding-right: $font-size;
                    color: $color-light-grey;
                    font-size: 12px;
                    text-transform: uppercase;
                }
    
                &-list {
                    padding-bottom: $font-size;
                    padding-left: $font-size;
                    padding-right: $font-size;
                    font-size: $font-size;
                    height: 100%;
                    // overflow: auto !important;
    
                    &-item {
                        color: $color-dark-grey;
                        line-height: 20px;
                        display: flex;
                        flex-direction: row;
                        justify-content: start;
                        align-items: center;
                        // white-space: nowrap;
                        cursor: pointer;
                        text-decoration: none;
                        font-size: 14px;
    
                        img, i {
                            margin-right: $font-size;
                            width: 18px;
                        }

                        &:first-child {
                            padding-top: 19px;
                        }

                        &:not(:first-child){
                            padding-top: 30px;
                        }
                        @media(hover:hover){
                            &:hover {
                                color: $main-color;
                                text-decoration: none;

                                img, i {
                                    filter: $main-filter-color;
                                }
                            }
                        }
                        &.isActive {
                            color: $main-color;
                            text-decoration: none;
                        }
                    }
                }
    
                &-horizontal {
                    &-list {
                        padding-bottom: 10px;
                        display: flex;
                        flex-direction: row;
                        justify-content: start;
                        align-items: center;
                        white-space: nowrap;
                        cursor: pointer;
                        overflow-x: scroll;
            
                        &-item {
                            line-height: 17px;
                            border-radius: 60px;
                            padding: 11px 20px;
                            color: $color-dark-grey;
                            background-color: $color-white;
                            box-shadow: 0px 4px 7px rgba(169, 169, 169, 0.25);
                            font-size: 14px;
                            border: 1px solid transparent;

                            margin-left: $grid_spacing / 2;
                            margin-right: $grid_spacing / 2;
                            text-decoration: none;
    
                            &:first-child {
                                margin-left: 0px;
                            }
    
                            &:last-child {
                                margin-right: 0px;
                            }

                            @media( hover: hover) {
                                &:hover {
                                    border: 1px solid $main-color;
                                    color: $main-color;
                                    background-color: rgba($main-color, 0.1);
                                }
                             }
            
                            &.isActive {
                                border: 1px solid $main-color;
                                color: $main-color;
                                text-decoration: none;
                                background-color: rgba($main-color, 0.1);
                            }
                        }
                    }
                }
    
            }
        }
    
        .main-page {
            background-color: $color-white;
            height: 100%;

            @media (max-width: (($grid__bp-lg * 1px) - 1px)) {
                padding: 0 15px 2% 15px !important;

                &.has-sticky-top {
                    margin-top: 70px;

                    &-large {
                        margin-top: 270px !important;
                    }
                }
            }

            @media (min-width: (($grid__bp-lg * 1px))) {
                @include browser(UBrowser) {
                    min-height: 100%!important;
                    height: auto !important;
                }
                padding: 0 30px 2% 30px !important;
            }
    
            .page-section {
                width: 100%;
                height: fit-content;
                height: auto;
                padding-top: 10px;
                padding-bottom: 10px;

                @media (min-width: ($grid__bp-sm * 1px)) {
                    border-top: 1px solid $color-pale-skin;  
                    padding-top: 20px;
                    padding-bottom: 40px;
                }

                @media (min-width: (($grid__bp-lg * 1px))) {
                    min-height: 30%!important;
                }

                &:first-of-type {
                    border-top: none !important;
                }
    
                &-title {
                    font-size: 20px;
                    margin-top: $font-size;
                    margin-bottom: 20px;
                    font-weight: 500;
                    line-height: 29px;
    
                    img {
                        width:26px;
                        height: 26px;
                        margin-right: $font-size;
                        float: left;
                    }
                }

                .payment-section-anchor {
                    position: relative;
                    // top: -55px; // offset page-section-title

                    @media (max-width: 820px) {
                        top: -260px !important;    // offset nav-bar-horizontal
                    }
                }
                
                &-card {
                    min-height: 100% !important;
                    box-sizing:border-box !important;
                    background-color: $color-white;
                    box-shadow: 0px 3px 5px 1px rgba(0, 0, 0, 0.1); 
                    cursor: pointer;
                    position: relative;
                    flex-direction: row;
                    
                    @media (max-width: ($grid__bp-lg * 1px)) { 
                        margin-inline-end: $grid-spacing * (-1);
                    }

                    @media (max-width: ($grid__bp-sm * 1px)) {
                        box-shadow: none; 
                    }

                    a {
                        text-decoration: none;
                        display: flex;
                        height: 100%;

                        @media (min-width: ($grid__bp-sm * 1px)) {
                            flex-direction: column;  
                        }

                        @media (max-width: ($grid__bp-sm * 1px)) {
                            margin: 0 8px 0 8px;
                        }
                        &:hover {
                            text-decoration: none;
                        }
                    }

                    &:not([class*="-notfound"]){
                        outline: 1px solid $color-pale-skin;
                        border-radius: 6px;

                        @media (max-width: ($grid__bp-sm * 1px)) {
                            outline: none;
                            border-bottom: 1px solid $color-pale-skin;
                            border-radius: 0;
                            margin-left: $grid-spacing * (-1);
                            margin-right: $grid-spacing * (-1.5);
                        }

                         &:after {
                                @media (max-width: ($grid__bp-sm * 1px)) {
                                    font-family: 'FontAwesome';
                                    content: "\f054";
                                    float: right;
                                    margin-right: 15px;
                                    margin-top: -16%;
                                    font-size: $font-size;
                                 
                                }
                            }
                    }

                    &[class*="-notfound"] {
                        border-radius: $font-size;
                        border: 1px dashed $main-color !important;
                        margin-inline-end: $grid-spacing;
                        margin-inline-start: $grid-spacing;
                        margin-top: $grid-spacing;

                        .page-section-card-content-title {
                            color: $color-light-grey;
                        }
                    }
    
                    &-header {
                        text-align: center;
                        margin: auto;
                        padding-right: $grid-spacing / 2;

                        @media (min-width: ($grid__bp-sm * 1px)) {
                            width: 100% !important;
                            border-bottom: 1px solid $color-pale-skin;
                        }
    
                        img {
                            margin-top: $font-size;
                            margin-bottom: $font-size;
                            height: 70px !important;

                            @media (max-width: ($grid__bp-sm * 1px)) {
                                width: 80px !important;
                                object-fit: contain;
                            }
                        }
                    }
    
                    &-content {
                        width: 100% !important;
                        height: initial;
                        flex-grow: 1;
                        margin: 20px 0px 15px 0px ;
                        padding: 0px 15px 40px 15px;

                        @media (max-width: ($grid__bp-sm * 1px)) {
                            padding: 0;
                        }
                        
                       
                        &-title {
                            color: $color-dark-grey;
                            font-weight: 500;
                            display: inline;
                            font-size: 14px;

                            b {
                                color: $color-dark-grey;
                                margin-left: $font-size * 2;

                                &:after {
                                    content: "\a \a ";
                                    white-space: pre;
                                    margin-bottom: $font-size;
                                }
                            }
                            
                            a {
                                margin-top: $font-size;
                                color: $color-light-grey;
                                display: inline;

                                &[href]{
                                    color: $main-color;
                                    text-decoration: none;
                                }
                            }
                        }
    
                        &-subtitle {
                            margin-top: 5px;
                            font-size: 12px;
                            font-weight: 400;
                            color: $color-light-grey;

                            &-link {
                                color: $main-color;
                                text-decoration: none;
                            }
                        }
    
                        &-info {
                            margin-top: 0px !important;
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                            justify-content: start;
                            vertical-align:middle;
                            display: flex;
                            row-gap: 5px;
                            font-size: 12px;
                            font-weight: 400;

                            &-time, &-fee {
                                margin-top: 19.5px !important;
                                display: inline-block;
                                justify-content: start;
                                vertical-align:middle;
                                row-gap: $font-size;
                                // white-space: nowrap;
    
                                p {
                                    padding-left: $font-size;
                                }
                            }
                        }
                    }
    
                    &-footer {
                        width: 100% !important;
                        outline: 1px solid $color-pale-skin;
                        background-color: $color-skin;
                        text-align: center;
                        margin: auto;
                        line-height: 36px;
                        bottom: 0;
                        font-size: 14px;
                        font-weight: 500;
                        position: absolute;
                        border-bottom-left-radius: 6px;
                        border-bottom-right-radius: 6px;
                        
                        @media (max-width: (($grid__bp-sm * 1px))) { 
                            display: none !important;
                        }

                        span {
                            color: $color-dark-grey;
                            text-decoration: none;
                        }
                    }
                }
            }
    
            .page-header {
                color: $color-dark-grey;
                font-size: 18px;
                line-height: 30px;
                top:0;
                display: flex;
                flex-direction: row;
                justify-content: start;
                align-items: center;
                white-space: nowrap;
                margin-top: 30px;
    
                img, i {
                    padding-right: $font-size;
                    cursor: pointer;
                }
    
                &:hover{
                    color: $main-color;
                    cursor: pointer;
                }
                
            }
    
            .page-guide-section {
                display: flex;
                flex-wrap: wrap;
                flex-direction: row;
                width: 100%;
                min-height: 0;
                bottom: 0;
                padding-bottom: 40px;
                // min-height: 100vh;
                
                @include browser(UBrowser) {
                    height: auto !important;
                    &.col-12 {
                        flex-basis: auto !important;
                    }
                }
    
                &-title {
                    padding-top: 30px;
                    padding-bottom: 30px;
                    font-size: 20px;
                    font-weight: 500;
                }
    
                &-step {
                    height: inherit;
                    top:0;
                    // display: table;
                    flex-direction: column;

                    @include browser(UBrowser) {
                        &.col-12 {
                            flex-basis: auto !important;
                        }
                    }

                    p {
                        display: table-row;
                        width: 100%;
                        line-height: 22px;
                        font-size: 16px;
                        color: $color-dark-grey;
                        margin-bottom: 40px;

                        @media (max-width: (($grid__bp-lg * 1px) + 1px)) {
                            line-height: 20px;
                            font-size: 14px;
                            margin-bottom: 30px;
                            @include browser(UBrowser) {
                                max-width: calc(100% - 15px) !important;
                            }
                        }

                        @media (min-width: (($grid__bp-lg * 1px) + 2px)) {
                            line-height: 20px;
                            font-size: 14px;
                            margin-bottom: 30px;
                            @include browser(UBrowser) {
                                max-width: calc(100% - 15px) !important;
                            }
                        }

                        em {
                            display: table-cell;
                            padding-right: 15px;
                            @include browser(UBrowser) {
                                @media (max-width: (($grid__bp-lg * 1px) + 1px)) {
                                    max-width: calc(100% - 15px) !important;
                                }
                                @media (min-width: (($grid__bp-lg * 1px) + 2px)) {
                                    max-width: calc(100% - 15px) !important;
                                }
                            }
                        }
                    }

                    .step {
                        display: table-cell;
                        position: relative;
                        padding: 14px 0 14px 0;
                        line-height: 0;
                        width: 28px;
                        height: 28px;
                        top: 0px;
                        margin-right: 18px;
                        border-radius: 50%;
                        float: left;
                        color: #ffffff;
                        text-align: center;
                        font-size: 14px;
                        font-weight: 500;
                        background-color: $main-color;

                        @media (max-width: (($grid__bp-lg * 1px)- 1px)) {
                            top: -5px;
                        }
                    }

                    .st {
                        width: 100%;
                        font-weight: normal;
                        color: $color-dark-grey;
                        line-height: normal;
                        text-align: left;
                    }

                    strong {
                        color: $main-color;
                        font-size: 14px;
                        font-weight: 500;
                    }

                    .imgbox {
                        width: inherit;
                        object-fit: fill;
                        border: 1px solid $color-pale-skin;
                        margin-top: 15px;
                        margin-bottom: 60px;
                        border-radius: 10px;
                        max-width: 800px;
                        margin-left: auto;
                        margin-right: auto;

                        @media (max-width: (($grid__bp-lg * 1px) - 1px) ) {
                            width: 100vw;
                            margin-left: -15px;
                            border-radius: 0px;
                        }

                        img {
                            width:100%;
                            object-fit: fill;
                            border-radius: 10px;

                            @media (max-width: (($grid__bp-lg * 1px) - 1px)) {
                                border-radius: 0px;
                            }
                        }
                    }


                }
            }
    
            &-footer {
                min-height: 10% !important;
                text-align: center;
                padding: $font-size;
                margin-bottom: 32px;
                border-top: 1px solid $color-pale-skin;

                @media  (max-width: (($grid__bp-lg * 1px) -1px)) {
                    padding: 0px;
                }

                @media (max-width: (($grid__bp-lg * 1px) - 1px)) {
                    bottom: 0px;
                    background-color: $color-skin;
                    margin-bottom: 0px !important;
                }

                &.atBottom {
                    width: 100%;
                    position: relative;
                    top: 20%;
                    transform: translateY(-100%) translateY(100%);    
                }
    
                &-actions {
                    width: 100%;
                    margin-left: auto !important;
                    margin-right: auto !important;
                    display: table;
                    line-height: 32px;
                    margin-top: 32px;
                    margin-bottom: 25px;
                    border-spacing: 10px;

                    @media (max-width: (($grid__bp-lg * 1px)- 1px)) {
                        // margin: 0px;
                        border-spacing: 10px 10px;

                        .btn {
                            font-size: 12px !important;
                        }
                    }

                    @include browser(UBrowser) {
                        &.col-10 {
                            flex-basis: auto     !important;
                        }

                        &.col-12 {
                            flex-basis: auto     !important;
                        }
                    }
    
                    .btn {
                        height: 36px;
                        width: 25%;
                        display: table-cell;
                        vertical-align: middle;
                        max-height: 64px;
                        background-color: $color-white; 
                        border: 1px solid $color-bold-skin;
                        color: $color-dark-grey;
                        padding: 9px 20px;
                        text-decoration: none;
                        touch-action: manipulation;
                        cursor: pointer;
                        border-radius: 5px;
                        font-size: 14px;
                        line-height: 17px;
                        margin: 0px;
                        white-space: normal;
                        word-break: keep-all;

                        @media (max-width: (($grid__bp-lg * 1px)- 1px)) {
                            height: 24px !important;
                            font-size: 11px;
                        }
    
                        &:hover {
                            color: $color-white;
                            background-color: $main-color;
                        }
    
                        &-active {
                            color: $color-white;
                            background-color: $main-color;
    
                            &:hover {
                                cursor:not-allowed;
                            }
                        }
                        
                    }
                }
    
                &-content{
                    color: $color-light-grey;
                    font-size: 14px;
                    font-weight: 400;
                    padding-bottom:14px;

                    b {
                        color: $color-dark-grey;
                        font-weight: 400;
                    }

                    a {
                        text-decoration: none;
                        color: $main-color;
                        font-weight: 400;

                        &:hover{
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    
    }

    @media (max-width: (($grid__bp-lg * 1px) - 1px)) {
        .spinner-main {
            z-index: 1;
            position: fixed;
            top: 55%;
            left: 43%;

            .spinner-general {
                border-top-color: #FF4130;
                border-left-color: #FF4130;
            }
            .spinner-slow {
                animation: spinner 2s linear infinite;
            }
            .spinner-large {
                width: 5rem;
                height: 5rem;
                border-width: 6px;
            }
            .spinner {
                width: 70px;
                height: 70px;
                border-top-color: #444;
                border-left-color: #444;
                animation: spinner 1s linear infinite;
                border-bottom-color: transparent;
                border-right-color: transparent;
                border-style: solid;
                border-width: 2px;
                border-radius: 50%;
                box-sizing: border-box;
                display: inline-block;
                vertical-align: middle;
            }
        }
    }

    @keyframes spinner {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

    .text-blue {
        color: $color-blue;
    }
    .text-green {
        color: $color-green;
    }
    .text-red {
        color: $color-red;
    }
}

