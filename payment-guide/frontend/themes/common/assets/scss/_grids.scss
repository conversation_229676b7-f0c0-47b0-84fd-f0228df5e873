$grid__bp-xs: 0;
$grid__bp-sm: 451;
$grid__bp-md: 601;
$grid__bp-lg: 821;
$grid__bp-xl: 1201;
$grid__bp-xxl: 1441;
$grid__cols: 12;

$map-grid-props: (
    '': 0,
    '-xs': $grid__bp-xs,
    '-sm': $grid__bp-sm,
    '-md': $grid__bp-md,
    '-lg': $grid__bp-lg,
    '-xl': $grid__bp-xl,
    '-xxl': $grid__bp-xxl
);

.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: $grid_spacing / (-2);
   
    @media (min-width: (($grid__bp-lg * 1px))) {
        margin-right: $grid_spacing / (-2);
    }
  
    &[class^="col-"] {
        margin-right: $grid_spacing;
        margin-top: $grid_spacing;
    }
}

@mixin create-mq($breakpoint, $min-or-max) {
    @if($breakpoint == 0) {
        @content;
    } @else {
        @media screen and (#{$min-or-max}-width: ($breakpoint *1px)) {
            @content;
        }
    }
}

@mixin create-col-classes($modifier, $grid-cols, $breakpoint) {
    @include create-mq($breakpoint, 'min') {
        .col#{$modifier}-offset-0 {
            margin-left: 0;
        }
        @for $i from 1 through $grid-cols {
            .col#{$modifier}-#{$i} {
                $this_width: ((100 / ($grid-cols / $i) ) * 1%);
                flex-basis: $this_width;
                max-width: $this_width;

                .row & {
                    padding-left: 0;
                    padding-right: 0;
                    
                    @media (min-width: ($grid__bp-sm * 1px)) {
                        margin-top: 10px;
                        margin-bottom: 10px;
                        padding-left: $grid-spacing /2 ;
                        padding-right: $grid-spacing /2 ;
                    }


                    @include browser(UBrowser) {
                        
                        &:first-child {
                            margin-left: 0px;
                        }

                        &:last-child {
                            margin-right: 0px;
                        }
                    }
                }
            }
            .col#{$modifier}-offset-#{$i} {
                margin-left: (100 / ($grid-cols / $i) ) * 1%;
            }
        }
    }
}

@each $modifier , $breakpoint in $map-grid-props {
    @if($modifier == '') {
        $modifier: '-xs';
    }
    @include create-mq($breakpoint - 1, 'max') {
        .hidden#{$modifier}-down {
            display: none !important;
        }
    }
    @include create-mq($breakpoint, 'min') {
        .hidden#{$modifier}-up {
            display: none !important;
        }
    }
}

@each $modifier , $breakpoint in $map-grid-props {
    @include create-col-classes($modifier, $grid__cols, $breakpoint);
}