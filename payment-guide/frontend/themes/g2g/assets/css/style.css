@charset "utf-8";
/* Global Styles */

p.solid {
  border-top: 1px solid #d6d6d6;
}

p.solidblue {
  border-top: 1px solid #ea5734;
}

a {
  text-decoration: none;
  color: #f03726;
}

a:hover {
  color: #999999;
  text-decoration: underline;
}

body {
  margin: 0;
  transition: background-color .5s;
  font-family: Helvetica, Arial;
  background-color: #f9f9f9;
}

h1 {
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 20px;
  text-align: left;
  font-weight: 400;
}

h2 {
  font-family: Helvetica, Arial;
}

.btn a {
  display: block;
  width: auto;
  height: 30px;
  padding: 0px 10px;
  border: 1px solid #d6d6d6;
  border-radius: 20px 20px 20px 20px;
  background: #FFFFFF;
  font-family: Helvetica, Arial;
  font-size: 10px;
  font-weight: 400;
  color: #999999;
  text-decoration: none;
  text-transform: uppercase;
  line-height: 32px;
  transition: 0.5s;
  margin-bottom: 10px;
}

.btn a:hover {
  border: 1px solid #f03726;
  background: #f03726;
  color: #FFFFFF;
}

.btn-selected a {
  pointer-events: none;
  cursor: default;
  border: 1px solid #f03726;
  background: #f03726;
  color: #FFFFFF;
}

#btn-gototop {
  display: none;
  width: 40px;
  position: fixed;
  bottom: 20px;
  right: 10px;
  z-index: 99;
  background-color: #f03726;
  font-size: 30px;
  color: #FFFFFF;
  cursor: pointer;
  padding: 2px;
  border-radius: 6px;
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.5);
  border: none;
  outline: none;
  transition: 0.5s;
}

#btn-gototop:hover {
  color: #FFFFFF;
  background-color: #555555;
}

/* Localization */

.overlay {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1;
  top: 0;
  right: 0;
  background-color: #232323;
  overflow-x: hidden;
}

.overlay-content {
  position: relative;
  top: 10%;
  width: 90%;
  margin-left: 5%;
  text-align: center;
  margin-top: 30px;
  font-size: 30px;
  font-weight: 400;
  color: #FFFFFF;
}

.overlay h1 {
  width: 70%;
  float: left;
  color: #999999;
  margin-top: -50px;
}

.overlay h2 {
  font-size: 11px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  float: left;
  width: 100%;
  text-align: left;
  border-bottom: 1px solid #666666;
  height: 20px;
  margin-top: 2%;
  margin-bottom: 1%;
}

.overlayInfo {
  float: left;
  width: calc(100%/6);
  display: inline-block;
  text-align: left;
  height: 60px;
}

.overlayIcon {
  float: left;
  position: relative;
  top: -8px;
  margin-right: 10px;
  /*padding-right: 10px;*/
}

.overlay .overlayContainer {
  display: block;
  padding-top: 20px;
  padding-bottom: 30px;
  transition: 0.3s;
  font-size: 12px;
  font-weight: 400;
  text-decoration: none;
  color: #b3d1e9;
  cursor:pointer;
}

.overlay .overlayContainer:hover, .overlay .overlayContainer:focus {
  color: #999999;
}

.overlay a {
  display: block;
  padding-top: 20px;
  padding-bottom: 30px;
  transition: 0.3s;
  font-size: 12px;
  font-weight: 400;
  text-decoration: none;
  color: #FFFFFF;
}

.overlay a:hover, .overlay a:focus {
  color: #999999;
}

.overlay .closebtn {
  position: absolute;
  top: 0px;
  right: 20px;
  font-size: 30px;
}

.localization-search {
  float: right;
  width: 30%;
  height: 20px;
  border-style : solid;
	border-width : 1px;
	border-color : #666666;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 200;
  color: #fff;
  padding: 10px 5px 8px 5px;
  background-color: #232323;
  margin-top: -50px;
  background-image: url("../images/icon_search.png");
  background-position: right;
  background-repeat: no-repeat;
  cursor: pointer;
}

/*.localization-search::placeholder{
  color : #fff;
}*/

/* Side Navigation Menu */

.myMenu {
  position: relative;
  top: 10px;
  width: 30px;
  height: 35px;
  font-family: Helvetica, Arial;
  font-size: 12px;
  padding: 10px 10px 8px 10px;
  background-color: #f03726;
  border-radius: 0px 6px 6px 0px;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.3);
  color: #FFFFFF;
  cursor: pointer;
  transition: 0.3s;
  background-image: url("../images/icon.png");
  background-position: left;
  background-repeat: no-repeat;
}

.myMenu:hover {
  color: #FFFFFF;
  width: 50px;
  background-color: #f03726;
}

.sidenav {
  font-family: Helvetica, Arial;
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: #f03726;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
  overflow-x: hidden;
  transition: 0.5s;
}

.sidenav a {
  padding: 20px 10px 20px 40px;
  font-size: 16px;
  font-weight: 400;
  text-decoration: none;
  color: #ffaaa2;
  display: block;
  transition: 0.3s;
}

.sidenav a:hover, .offcanvas a:focus {
  color: #ffffff;
}

.sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 30px;
  margin-left: 50px;
}

.sidenav .logo {
  padding: 50px 10px 0px 0px;
}

.sidenav h2 {
  font-size: 11px;
  font-weight: 600;
  color: #ffaaa2;
  text-transform: uppercase;
  padding: 30px 10px 20px 40px;
}

/* Search bar */

#searchbar {
  position: absolute;
  right: 0;
  top: 10px;
  width: 30px;
  height: 33px;
  border: none;
  border-radius: 6px 0px 0px 6px;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.3);
  font-size: 14px;
  font-weight: 200;
  color: #fff;
  padding: 10px 0px 8px 20px;
  background-color: #000;
  background-image: url("../images/icon_search.png");
  background-position: right;
  background-repeat: no-repeat;
  -webkit-transition: width 0.3s ease-in-out;
  transition: width 0.3s ease-in-out;
  cursor: pointer;
}

#searchbar:hover {
  width: 50px;
}

#searchbar:focus {
  width: 30%;
  padding-right: 50px;
  cursor: auto;
}

/* Main */

#mainWrapper {
  width: 100%;
  max-width: 1280px;
  background-color: #f9f9f9;
  transition: margin-left .5s;
  margin: 0 auto;
}

#mainWrapper-Home {
  width: 100%;
  background-color: #f9f9f9;
  transition: margin-left .5s;
  margin: 0 auto;
}

#mainWrapper .mainContent, #mainWrapper-Home .mainContent {
  float: left;
  width: 98%;
  text-align: center;
  padding-left: 2%;
  margin-bottom: 50px;
  padding-top: 20px;
  min-height: 400px;
}

.mainContent .head-Row, .mainContent .legend-Row, .mainContent .description-Row, .mainContent .payment-Row, .mainContent .giftcard-Row, .mainContent .footer-Row {
  overflow: auto;
}

/* Homepage */

.Content-home {
  float: left;
  width: 60%;
  margin-left: 20%;
  margin-top: 5%;
  margin-bottom: 5%;
  text-align: left;
}

.Content-home .logo {
  margin-bottom: 40px;
}

.Content-home h1 {
  color: #ffaaa2;
  font-size: 30px;
  line-height: 40px;
  padding: 20px 0 20px 0;
}

.Content-home-Nav {
  float: left;
  width: calc(100%/3);
  display: inline-block;
  text-align: left;
  height: 60px;
}

.Content-home-Nav a {
  display: block;
  margin: 20px 10px 20px 0;
  transition: 0.3s;
  color: #ffaaa2;
}

.Content-home-Nav a:hover, .offcanvas a:focus {
  color: #ffffff;
}

.fixed-bg {
  background-image: url("../images/bgimg_1500x450.jpg");
  height: 650px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Header */

.head-Row-info {
  float: left;
  margin: 1%;
  width: 95%;
}

.head-Row-info .localization {
  width: 140px;
  position: relative;
  text-align: right;
}

/* Payment Categories */

.description-Row {
  margin: 1%;
  width: 95%;
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 16px;
  text-align: left;
  font-weight: 400;
  line-height: 26px;
  padding-bottom: 20px;
}

.legend-Row {
  margin: 1%;
  width: 95%;
  height: 30px;
}

.legend-Row .indicator {
  float: left;
  font-family: Helvetica, Arial;
  color: #999999;
  font-size: 12px;
  position: relative;
  text-align: left;
  padding-right: 20px;
  padding-top: 10px;
}

.pg-info-box {
  position: relative;
  float: left;
  margin: 1%;
  width: 22%;
  min-height: 360px;
  background-color: #f9f9f9;
  box-shadow: 0px 3px 5px 1px rgba(0, 0, 0, 0.1);
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #d6d6d6;
}

.pg-logo-box {
  width: 100%;
  border-bottom: 1px solid #d6d6d6;
  background-color: #FFFFFF;
  border-radius: 6px 6px 0px 0px;
}

.pg-logo-box img {
  width: 130px;
  height: 70px;
  padding: 10px 0px;
}

.supporticon {
  font-size: 11px;
  text-align: right;
  position: relative;
  top: 10px;
}

.Content-pg {
  position: relative;
  left: 15px;
  font-family: Helvetica, Arial;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  text-align: left;
  line-height: 14px;
  width: 90%;
  padding-bottom: 70px;
}

.Content-pg h1 {
  color: #333333;
  font-size: 14px;
  font-weight: 600;
  position: relative;
  top: -14px;
  width: 80%;
}

.Content-pg .via {
  position: relative;
  top: -24px;
}

.Content-pg .label_1 {
  position: relative;
  top: -14px;
}

.Content-pg .label_2 {
  color: #333333;
  position: relative;
  top: -22px;
}

.Content-pg-footer {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
}

.Content-pg-btn {
  position: relative;
  top: -10px;
  left: 15px;
  height: 30px;
  margin-bottom: 10px;
  display: inline-block;
  float: left;
  margin-right: 5px;
}

.Content-pg-expand {
  position: relative;
  top: -10px;
  left: 15px;
  height: 30px;
  margin-bottom: 10px;
  display: inline-block;
  float: right;
  margin-right: 30px;
}

section {
  padding: 0px 0px;
}

.expand {
  display: none;
  overflow: hidden;
  -webkit-transition: height 0.5s;
  transition: height 0.5s;
}

.expand-show {
  display: block;
}

/* Userguide */

.col-content {
  margin-left: 1%;
  margin-bottom: 10%;
  padding-left: 5px;
  float: left;
  width: 65%;
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 16px;
  text-align: left;
  font-weight: 400;
  line-height: 26px;
  display: inline-block;
}

.col-content .step {
  position: relative;
  padding: 10px;
  width: 26px;
  margin-right: 10px;
  top: -10px;
  border-radius: 25px;
  float: left;
  font-family: Helvetica, Arial;
  color: #ffffff;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  background-color: #f03726;
}

.col-content .imgbox {
  width: 100%;
  margin: 20px 0px 60px 0px;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #d6d6d6;
  text-align: center;
  background-color: #ffffff;
}

.imgbox img {
  width: 700px;
  padding: 20px;
}

.col-listing {
  margin-left: 5%;
  float: left;
  width: 24%;
  display: inline-block;
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 14px;
  text-align: left;
  font-weight: 400;
  line-height: 20px;
}

.col-listing a {
  width: 100%;
  padding: 10px 0px 10px 0px;
  text-decoration: none;
  color: #f03726;
  display: block;
  float: left;
  border-bottom: 1px dotted #d6d6d6;
}

.col-listing a:hover {
  color: #999999;
}

/* Where to buy */

.giftcard-Row {
  margin-left: 1%;
  width: 92%;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0px 3px 5px 1px rgba(0, 0, 0, 0.1);
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #d6d6d6;
  margin-bottom: 20px;
}

.Content-gc-logo-big {
  width: 130px;
  height: 70px;
  float: left;
  margin-right: 20px;
}

.Content-gc-logo-small {
  float: left;
  position: relative;
  margin-bottom: 20px;
  width: 70px;
  height: 38px;
}

.Content-gc {
  float: left;
  font-family: Helvetica, Arial;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  text-align: left;
}

.Content-gc h1 {
  color: #333333;
  font-size: 14px;
  font-weight: 600;
}

.Content-gc .description {
  position: relative;
  top: -5px;
}

.Content-gc-btn {
  float: right;
}

.Content-gc-extra {
  position: relative;
  float: left;
  top: 10px;
  width: 100%;
  border-top: 1px solid #d6d6d6;
}

.Content-gc-extra-info {
  padding-top: 20px;
  font-family: Helvetica, Arial;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  text-align: left;
}

.Content-gc-extra-logo {
  float: left;
  width: calc(100%/10);
  display: inline-block;
  text-align: left;
}

/* Search Not Found */

.col-content-notfound {
  margin: 10% 5% 10% 1%;
  padding: 30px 30px 20px 30px;
  width: auto;
  display: inline-block;
  border: 1px dashed #f00000;
  border-radius: 6px 6px 6px 6px;
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
}

.col-content-notfound-icon {
  position: relative;
  top: -5px;
  float: left;
  padding-right: 10px;
}

.col-pg-content-notfound {
    margin: 20px 0px 20px 0px;
    padding: 30px 0px 20px 0px;
    width: 100%;
    display: inline-block;
    border: 1px dashed #f00000;
    border-radius: 6px 6px 6px 6px;
    font-family: Helvetica, Arial;
    color: #333333;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
}

.pg-notfound-wrapper {
    padding: 0px 20px;
}

/* footer */

.footer-Row-info {
  float: left;
  width: 100%;
  border-top: 1px solid #d6d6d6;
}

.Content-footer {
  font-family: Helvetica, Arial;
  color: #333333;
  font-size: 12px;
  font-weight: 200;
  line-height: 1.6;
  text-align: center;
  padding: 30px 10px;
}

@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav .logo {
    top: 10%;
    padding-bottom: 10px;
  }
}

/* Media query for tablets */

@media screen and (max-width: 1024px) {
  .overlayInfo {
    width: calc(100%/4);
  }
  .overlay-content {
    width: 90%;
    margin-left: 5%;
  }
  .col-content img {
    width: 90%;
    height: 90%;
    padding: 0;
  }
  .mainContent .giftcard-Row {
    width: 91%;
  }
  .btn a {
    font-size: 9px;
  }
}

/* Media query for oldskool */

@media screen and (max-width: 800px) {
  .overlayInfo {
    width: calc(100%/2);
  }
  .overlay-content {
    text-align: left;
  }
  .pg-info-box {
    margin: 2%;
    width: 44%;
  }
  .Content-pg {
    width: 92%;
  }
  .Content-pg h1 {
    width: 80%;
  }
  .Content-gc-extra-logo {
    width: calc(100%/6);
  }
  .mainContent .giftcard-Row {
    margin-left: 1%;
    width: 89%;
  }
  .Content-home {
    width: 80%;
    margin-left: 10%;
  }
  .col-content {
    margin-left: 2%;
    width: 90%;
  }
  .col-listing {
    margin-left: 2%;
    padding-left: 5px;
    width: 90%;
    margin-top: 5%;
    margin-bottom: 5%;
  }
  .head-Row-info, .legend-Row, .description-Row {
    margin-left: 3%;
    width: 92%;
  }
  #searchbar:focus {
    width: 50%;
  }
	.overlay h1 {
		float: left;
		text-align: left;
		margin-top: 10px;
		width: 100%;
	}
	.overlay h2 {
		margin-top: 10%;
	}
	.localization-search {
		width: 100%;
		margin-top: 10px;
		float: left;
	}
}

/* Media query for mobilephone */

@media screen and (max-width: 760px) {
  /* search field in sidebar */
}

/* Media query for mobilephone */

@media screen and (max-width: 480px) {
  /* search field in sidebar */
  .head-Row-info, .legend-Row, .description-Row {
    margin-left: 4%;
    width: 92%;
  }
  .overlay-content {
    text-align: left;
    top: 6%;
  }
  .overlayInfo {
    width: 100%;
  }
  .pg-info-box {
    display: block;
    width: 91%;
    padding-left: 0px;
    padding-right: 0px;
    margin-top: 20px;
    position: relative;
    left: 2%;
  }
  .Content-gc-extra-logo {
    width: calc(100%/5);
  }
  .mainContent .giftcard-Row {
    margin-left: 3%;
    margin-right: 2%;
    width: 82%;
  }
  .Content-gc-btn {
    float: left;
  }
  .Content-gc {
    width: 200px;
  }
  .Content-gc-logo-small {
    margin-bottom: 20px;
    width: 50px;
    height: 27px;
  }
  .Content-home {
    width: 90%;
    margin-left: 5%;
  }
  .Content-home .logo {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .Content-home h1 {
    font-size: 24px;
    line-height: 30px;
    padding: 5px 0 5px 0;
  }
  .Content-home-Nav {
    width: 100%;
    height: auto;
  }
  .Content-home-Nav a {
    margin: 5px 10px 20px 0;
  }
  .col-listing {
    margin-top: 20%;
  }
  .col-content-notfound {
    margin: 10% 5% 10% 5%;
  }
}

/* Media query for tablets */

@media screen and (max-width: 360px) {
  .legend-Row {
    height: 50px;
  }
}
