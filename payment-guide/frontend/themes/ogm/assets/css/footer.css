/* Styling for OG payment guide new footer */
.main-page-footer.hidden-lg-down+div {
    margin-left: -30px;
    margin-right: -30px;
    margin-bottom: -100px;
    background: #2f3dbb;
}

.main-page-footer.hidden-lg-up+div {
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: -100px;
    background: #2f3dbb;
}

.legacy__footer__container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: white;
    gap: 24px;
}

@media (max-width: 1100px) {
    .legacy__footer__container {
        flex-direction: column;
        gap: 20px;
        flex-wrap: wrap;
    }

    .legacy__footer__left,
    .legacy__footer__right {
        width: 100%;
    }

}

@media screen and (max-width: 820px) {
    .main-page-footer.hidden-lg-down+div {
        display: none;
    }

    .main-page-footer.hidden-lg-up+div {
        display: block;
    }

    .legacy__footer__right {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .container {
        padding: 24px 16px !important;
    }
}

@media screen and (min-width: 821px) {
    .main-page-footer.hidden-lg-down+div {
        display: block;
    }

    .main-page-footer.hidden-lg-up+div {
        display: none;
    }

    .container {
        padding: 24px 40px !important;
    }
}

.legacy__footer__left {
    font-family: Helvetica Neue, Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-align: left;
    gap: 5px;
}

@media (max-width: 820px) {
    .legacy__footer__left {
        align-items: center;
        text-align: center;
        gap: 20px;
    }
}

.legacy__footer__links {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

@media (max-width: 820px) {
    .legacy__footer__links {
        justify-content: center;
    }
}

.legacy__footer__links-item {
    text-wrap: nowrap;
    display: flex;
    align-items: center;
    gap: 5px;
}

.legacy__footer__links-item img {
    width: 20px;
    height: 20px;
}

@media (min-width: 1024px) {
    .legacy__footer__links-item-support-desktop {
        display: initial;
    }
}

@media (max-width: 1023.99px) {
    .legacy__footer__links-item-support-desktop {
        display: none;
    }
}

@media (min-width: 1024px) {
    .legacy__footer__links-item-support-mobile {
        display: none;
    }
}

@media (max-width: 1023.99px) {
    .legacy__footer__links-item-support-mobile {
        display: initial;
    }
}

.legacy__footer__links-item a {
    height: 20px;
    line-height: 20px;
    color: #fff;
    display: flex;
    align-items: center;
}

.legacy__footer__separator {
    margin: 0 10px;
}

.legacy__footer__right {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.legacy__footer__social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.legacy__footer__social-icon a {
    height: 20px;
    width: 20px;
}

.h-hyperlink {
    cursor: pointer
}