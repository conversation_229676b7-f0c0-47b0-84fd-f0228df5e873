.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -1.5%;
}

@media (min-width: 821px) {
  .row {
    margin-right: -1.5%;
  }
}

.row[class^="col-"] {
  margin-right: 3%;
  margin-top: 3%;
}

@media screen and (max-width: -1px) {
  .hidden-xs-down {
    display: none !important;
  }
}

.hidden-xs-up {
  display: none !important;
}

@media screen and (max-width: -1px) {
  .hidden-xs-down {
    display: none !important;
  }
}

.hidden-xs-up {
  display: none !important;
}

@media screen and (max-width: 450px) {
  .hidden-sm-down {
    display: none !important;
  }
}

@media screen and (min-width: 451px) {
  .hidden-sm-up {
    display: none !important;
  }
}

@media screen and (max-width: 600px) {
  .hidden-md-down {
    display: none !important;
  }
}

@media screen and (min-width: 601px) {
  .hidden-md-up {
    display: none !important;
  }
}

@media screen and (max-width: 820px) {
  .hidden-lg-down {
    display: none !important;
  }
}

@media screen and (min-width: 821px) {
  .hidden-lg-up {
    display: none !important;
  }
}

@media screen and (max-width: 1200px) {
  .hidden-xl-down {
    display: none !important;
  }
}

@media screen and (min-width: 1201px) {
  .hidden-xl-up {
    display: none !important;
  }
}

@media screen and (max-width: 1440px) {
  .hidden-xxl-down {
    display: none !important;
  }
}

@media screen and (min-width: 1441px) {
  .hidden-xxl-up {
    display: none !important;
  }
}

.col-offset-0 {
  margin-left: 0;
}

.col-1 {
  flex-basis: 8.33333%;
  max-width: 8.33333%;
}

.row .col-1 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-1:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-1:last-child {
  margin-right: 0px;
}

.col-offset-1 {
  margin-left: 8.33333%;
}

.col-2 {
  flex-basis: 16.66667%;
  max-width: 16.66667%;
}

.row .col-2 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-2:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-2:last-child {
  margin-right: 0px;
}

.col-offset-2 {
  margin-left: 16.66667%;
}

.col-3 {
  flex-basis: 25%;
  max-width: 25%;
}

.row .col-3 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-3:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-3:last-child {
  margin-right: 0px;
}

.col-offset-3 {
  margin-left: 25%;
}

.col-4 {
  flex-basis: 33.33333%;
  max-width: 33.33333%;
}

.row .col-4 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-4:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-4:last-child {
  margin-right: 0px;
}

.col-offset-4 {
  margin-left: 33.33333%;
}

.col-5 {
  flex-basis: 41.66667%;
  max-width: 41.66667%;
}

.row .col-5 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-5:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-5:last-child {
  margin-right: 0px;
}

.col-offset-5 {
  margin-left: 41.66667%;
}

.col-6 {
  flex-basis: 50%;
  max-width: 50%;
}

.row .col-6 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-6:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-6:last-child {
  margin-right: 0px;
}

.col-offset-6 {
  margin-left: 50%;
}

.col-7 {
  flex-basis: 58.33333%;
  max-width: 58.33333%;
}

.row .col-7 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-7:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-7:last-child {
  margin-right: 0px;
}

.col-offset-7 {
  margin-left: 58.33333%;
}

.col-8 {
  flex-basis: 66.66667%;
  max-width: 66.66667%;
}

.row .col-8 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-8:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-8:last-child {
  margin-right: 0px;
}

.col-offset-8 {
  margin-left: 66.66667%;
}

.col-9 {
  flex-basis: 75%;
  max-width: 75%;
}

.row .col-9 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-9:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-9:last-child {
  margin-right: 0px;
}

.col-offset-9 {
  margin-left: 75%;
}

.col-10 {
  flex-basis: 83.33333%;
  max-width: 83.33333%;
}

.row .col-10 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-10:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-10:last-child {
  margin-right: 0px;
}

.col-offset-10 {
  margin-left: 83.33333%;
}

.col-11 {
  flex-basis: 91.66667%;
  max-width: 91.66667%;
}

.row .col-11 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-11:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-11:last-child {
  margin-right: 0px;
}

.col-offset-11 {
  margin-left: 91.66667%;
}

.col-12 {
  flex-basis: 100%;
  max-width: 100%;
}

.row .col-12 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-12:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-12:last-child {
  margin-right: 0px;
}

.col-offset-12 {
  margin-left: 100%;
}

.col-xs-offset-0 {
  margin-left: 0;
}

.col-xs-1 {
  flex-basis: 8.33333%;
  max-width: 8.33333%;
}

.row .col-xs-1 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-1:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-1:last-child {
  margin-right: 0px;
}

.col-xs-offset-1 {
  margin-left: 8.33333%;
}

.col-xs-2 {
  flex-basis: 16.66667%;
  max-width: 16.66667%;
}

.row .col-xs-2 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-2:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-2:last-child {
  margin-right: 0px;
}

.col-xs-offset-2 {
  margin-left: 16.66667%;
}

.col-xs-3 {
  flex-basis: 25%;
  max-width: 25%;
}

.row .col-xs-3 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-3:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-3:last-child {
  margin-right: 0px;
}

.col-xs-offset-3 {
  margin-left: 25%;
}

.col-xs-4 {
  flex-basis: 33.33333%;
  max-width: 33.33333%;
}

.row .col-xs-4 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-4:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-4:last-child {
  margin-right: 0px;
}

.col-xs-offset-4 {
  margin-left: 33.33333%;
}

.col-xs-5 {
  flex-basis: 41.66667%;
  max-width: 41.66667%;
}

.row .col-xs-5 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-5:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-5:last-child {
  margin-right: 0px;
}

.col-xs-offset-5 {
  margin-left: 41.66667%;
}

.col-xs-6 {
  flex-basis: 50%;
  max-width: 50%;
}

.row .col-xs-6 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-6:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-6:last-child {
  margin-right: 0px;
}

.col-xs-offset-6 {
  margin-left: 50%;
}

.col-xs-7 {
  flex-basis: 58.33333%;
  max-width: 58.33333%;
}

.row .col-xs-7 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-7:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-7:last-child {
  margin-right: 0px;
}

.col-xs-offset-7 {
  margin-left: 58.33333%;
}

.col-xs-8 {
  flex-basis: 66.66667%;
  max-width: 66.66667%;
}

.row .col-xs-8 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-8:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-8:last-child {
  margin-right: 0px;
}

.col-xs-offset-8 {
  margin-left: 66.66667%;
}

.col-xs-9 {
  flex-basis: 75%;
  max-width: 75%;
}

.row .col-xs-9 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-9:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-9:last-child {
  margin-right: 0px;
}

.col-xs-offset-9 {
  margin-left: 75%;
}

.col-xs-10 {
  flex-basis: 83.33333%;
  max-width: 83.33333%;
}

.row .col-xs-10 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-10:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-10:last-child {
  margin-right: 0px;
}

.col-xs-offset-10 {
  margin-left: 83.33333%;
}

.col-xs-11 {
  flex-basis: 91.66667%;
  max-width: 91.66667%;
}

.row .col-xs-11 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-11:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-11:last-child {
  margin-right: 0px;
}

.col-xs-offset-11 {
  margin-left: 91.66667%;
}

.col-xs-12 {
  flex-basis: 100%;
  max-width: 100%;
}

.row .col-xs-12 {
  padding-left: 0;
  padding-right: 0;
}

@media (min-width: 451px) {
  .row .col-xs-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

html[data-browser*="UBrowser"] .row .col-xs-12:first-child {
  margin-left: 0px;
}

html[data-browser*="UBrowser"] .row .col-xs-12:last-child {
  margin-right: 0px;
}

.col-xs-offset-12 {
  margin-left: 100%;
}

@media screen and (min-width: 451px) {
  .col-sm-offset-0 {
    margin-left: 0;
  }

  .col-sm-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;
  }

  .row .col-sm-1 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-1:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-1:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-1 {
    margin-left: 8.33333%;
  }

  .col-sm-2 {
    flex-basis: 16.66667%;
    max-width: 16.66667%;
  }

  .row .col-sm-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-2:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-2:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-2 {
    margin-left: 16.66667%;
  }

  .col-sm-3 {
    flex-basis: 25%;
    max-width: 25%;
  }

  .row .col-sm-3 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-3:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-3:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-3 {
    margin-left: 25%;
  }

  .col-sm-4 {
    flex-basis: 33.33333%;
    max-width: 33.33333%;
  }

  .row .col-sm-4 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-4:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-4:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-4 {
    margin-left: 33.33333%;
  }

  .col-sm-5 {
    flex-basis: 41.66667%;
    max-width: 41.66667%;
  }

  .row .col-sm-5 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-5:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-5:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-5 {
    margin-left: 41.66667%;
  }

  .col-sm-6 {
    flex-basis: 50%;
    max-width: 50%;
  }

  .row .col-sm-6 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-6:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-6:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-6 {
    margin-left: 50%;
  }

  .col-sm-7 {
    flex-basis: 58.33333%;
    max-width: 58.33333%;
  }

  .row .col-sm-7 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-7:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-7:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-7 {
    margin-left: 58.33333%;
  }

  .col-sm-8 {
    flex-basis: 66.66667%;
    max-width: 66.66667%;
  }

  .row .col-sm-8 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-8:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-8:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-8 {
    margin-left: 66.66667%;
  }

  .col-sm-9 {
    flex-basis: 75%;
    max-width: 75%;
  }

  .row .col-sm-9 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-9:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-9:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-9 {
    margin-left: 75%;
  }

  .col-sm-10 {
    flex-basis: 83.33333%;
    max-width: 83.33333%;
  }

  .row .col-sm-10 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-10:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-10:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-10 {
    margin-left: 83.33333%;
  }

  .col-sm-11 {
    flex-basis: 91.66667%;
    max-width: 91.66667%;
  }

  .row .col-sm-11 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-11:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-11:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-11 {
    margin-left: 91.66667%;
  }

  .col-sm-12 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .row .col-sm-12 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 451px) and (min-width: 451px) {
  .row .col-sm-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 451px) {
  html[data-browser*="UBrowser"] .row .col-sm-12:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-sm-12:last-child {
    margin-right: 0px;
  }

  .col-sm-offset-12 {
    margin-left: 100%;
  }
}

@media screen and (min-width: 601px) {
  .col-md-offset-0 {
    margin-left: 0;
  }

  .col-md-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;
  }

  .row .col-md-1 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-1:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-1:last-child {
    margin-right: 0px;
  }

  .col-md-offset-1 {
    margin-left: 8.33333%;
  }

  .col-md-2 {
    flex-basis: 16.66667%;
    max-width: 16.66667%;
  }

  .row .col-md-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-2:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-2:last-child {
    margin-right: 0px;
  }

  .col-md-offset-2 {
    margin-left: 16.66667%;
  }

  .col-md-3 {
    flex-basis: 25%;
    max-width: 25%;
  }

  .row .col-md-3 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-3:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-3:last-child {
    margin-right: 0px;
  }

  .col-md-offset-3 {
    margin-left: 25%;
  }

  .col-md-4 {
    flex-basis: 33.33333%;
    max-width: 33.33333%;
  }

  .row .col-md-4 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-4:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-4:last-child {
    margin-right: 0px;
  }

  .col-md-offset-4 {
    margin-left: 33.33333%;
  }

  .col-md-5 {
    flex-basis: 41.66667%;
    max-width: 41.66667%;
  }

  .row .col-md-5 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-5:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-5:last-child {
    margin-right: 0px;
  }

  .col-md-offset-5 {
    margin-left: 41.66667%;
  }

  .col-md-6 {
    flex-basis: 50%;
    max-width: 50%;
  }

  .row .col-md-6 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-6:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-6:last-child {
    margin-right: 0px;
  }

  .col-md-offset-6 {
    margin-left: 50%;
  }

  .col-md-7 {
    flex-basis: 58.33333%;
    max-width: 58.33333%;
  }

  .row .col-md-7 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-7:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-7:last-child {
    margin-right: 0px;
  }

  .col-md-offset-7 {
    margin-left: 58.33333%;
  }

  .col-md-8 {
    flex-basis: 66.66667%;
    max-width: 66.66667%;
  }

  .row .col-md-8 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-8:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-8:last-child {
    margin-right: 0px;
  }

  .col-md-offset-8 {
    margin-left: 66.66667%;
  }

  .col-md-9 {
    flex-basis: 75%;
    max-width: 75%;
  }

  .row .col-md-9 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-9:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-9:last-child {
    margin-right: 0px;
  }

  .col-md-offset-9 {
    margin-left: 75%;
  }

  .col-md-10 {
    flex-basis: 83.33333%;
    max-width: 83.33333%;
  }

  .row .col-md-10 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-10:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-10:last-child {
    margin-right: 0px;
  }

  .col-md-offset-10 {
    margin-left: 83.33333%;
  }

  .col-md-11 {
    flex-basis: 91.66667%;
    max-width: 91.66667%;
  }

  .row .col-md-11 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-11:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-11:last-child {
    margin-right: 0px;
  }

  .col-md-offset-11 {
    margin-left: 91.66667%;
  }

  .col-md-12 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .row .col-md-12 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 601px) and (min-width: 451px) {
  .row .col-md-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 601px) {
  html[data-browser*="UBrowser"] .row .col-md-12:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-md-12:last-child {
    margin-right: 0px;
  }

  .col-md-offset-12 {
    margin-left: 100%;
  }
}

@media screen and (min-width: 821px) {
  .col-lg-offset-0 {
    margin-left: 0;
  }

  .col-lg-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;
  }

  .row .col-lg-1 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-1:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-1:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-1 {
    margin-left: 8.33333%;
  }

  .col-lg-2 {
    flex-basis: 16.66667%;
    max-width: 16.66667%;
  }

  .row .col-lg-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-2:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-2:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-2 {
    margin-left: 16.66667%;
  }

  .col-lg-3 {
    flex-basis: 25%;
    max-width: 25%;
  }

  .row .col-lg-3 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-3:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-3:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-3 {
    margin-left: 25%;
  }

  .col-lg-4 {
    flex-basis: 33.33333%;
    max-width: 33.33333%;
  }

  .row .col-lg-4 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-4:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-4:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-4 {
    margin-left: 33.33333%;
  }

  .col-lg-5 {
    flex-basis: 41.66667%;
    max-width: 41.66667%;
  }

  .row .col-lg-5 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-5:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-5:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-5 {
    margin-left: 41.66667%;
  }

  .col-lg-6 {
    flex-basis: 50%;
    max-width: 50%;
  }

  .row .col-lg-6 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-6:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-6:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-6 {
    margin-left: 50%;
  }

  .col-lg-7 {
    flex-basis: 58.33333%;
    max-width: 58.33333%;
  }

  .row .col-lg-7 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-7:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-7:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-7 {
    margin-left: 58.33333%;
  }

  .col-lg-8 {
    flex-basis: 66.66667%;
    max-width: 66.66667%;
  }

  .row .col-lg-8 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-8:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-8:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-8 {
    margin-left: 66.66667%;
  }

  .col-lg-9 {
    flex-basis: 75%;
    max-width: 75%;
  }

  .row .col-lg-9 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-9:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-9:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-9 {
    margin-left: 75%;
  }

  .col-lg-10 {
    flex-basis: 83.33333%;
    max-width: 83.33333%;
  }

  .row .col-lg-10 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-10:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-10:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-10 {
    margin-left: 83.33333%;
  }

  .col-lg-11 {
    flex-basis: 91.66667%;
    max-width: 91.66667%;
  }

  .row .col-lg-11 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-11:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-11:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-11 {
    margin-left: 91.66667%;
  }

  .col-lg-12 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .row .col-lg-12 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 821px) and (min-width: 451px) {
  .row .col-lg-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 821px) {
  html[data-browser*="UBrowser"] .row .col-lg-12:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-lg-12:last-child {
    margin-right: 0px;
  }

  .col-lg-offset-12 {
    margin-left: 100%;
  }
}

@media screen and (min-width: 1201px) {
  .col-xl-offset-0 {
    margin-left: 0;
  }

  .col-xl-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;
  }

  .row .col-xl-1 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-1:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-1:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-1 {
    margin-left: 8.33333%;
  }

  .col-xl-2 {
    flex-basis: 16.66667%;
    max-width: 16.66667%;
  }

  .row .col-xl-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-2:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-2:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-2 {
    margin-left: 16.66667%;
  }

  .col-xl-3 {
    flex-basis: 25%;
    max-width: 25%;
  }

  .row .col-xl-3 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-3:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-3:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-3 {
    margin-left: 25%;
  }

  .col-xl-4 {
    flex-basis: 33.33333%;
    max-width: 33.33333%;
  }

  .row .col-xl-4 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-4:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-4:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-4 {
    margin-left: 33.33333%;
  }

  .col-xl-5 {
    flex-basis: 41.66667%;
    max-width: 41.66667%;
  }

  .row .col-xl-5 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-5:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-5:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-5 {
    margin-left: 41.66667%;
  }

  .col-xl-6 {
    flex-basis: 50%;
    max-width: 50%;
  }

  .row .col-xl-6 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-6:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-6:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-6 {
    margin-left: 50%;
  }

  .col-xl-7 {
    flex-basis: 58.33333%;
    max-width: 58.33333%;
  }

  .row .col-xl-7 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-7:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-7:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-7 {
    margin-left: 58.33333%;
  }

  .col-xl-8 {
    flex-basis: 66.66667%;
    max-width: 66.66667%;
  }

  .row .col-xl-8 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-8:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-8:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-8 {
    margin-left: 66.66667%;
  }

  .col-xl-9 {
    flex-basis: 75%;
    max-width: 75%;
  }

  .row .col-xl-9 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-9:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-9:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-9 {
    margin-left: 75%;
  }

  .col-xl-10 {
    flex-basis: 83.33333%;
    max-width: 83.33333%;
  }

  .row .col-xl-10 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-10:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-10:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-10 {
    margin-left: 83.33333%;
  }

  .col-xl-11 {
    flex-basis: 91.66667%;
    max-width: 91.66667%;
  }

  .row .col-xl-11 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-11:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-11:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-11 {
    margin-left: 91.66667%;
  }

  .col-xl-12 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .row .col-xl-12 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1201px) and (min-width: 451px) {
  .row .col-xl-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1201px) {
  html[data-browser*="UBrowser"] .row .col-xl-12:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xl-12:last-child {
    margin-right: 0px;
  }

  .col-xl-offset-12 {
    margin-left: 100%;
  }
}

@media screen and (min-width: 1441px) {
  .col-xxl-offset-0 {
    margin-left: 0;
  }

  .col-xxl-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;
  }

  .row .col-xxl-1 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-1:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-1:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-1 {
    margin-left: 8.33333%;
  }

  .col-xxl-2 {
    flex-basis: 16.66667%;
    max-width: 16.66667%;
  }

  .row .col-xxl-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-2 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-2:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-2:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-2 {
    margin-left: 16.66667%;
  }

  .col-xxl-3 {
    flex-basis: 25%;
    max-width: 25%;
  }

  .row .col-xxl-3 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-3 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-3:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-3:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-3 {
    margin-left: 25%;
  }

  .col-xxl-4 {
    flex-basis: 33.33333%;
    max-width: 33.33333%;
  }

  .row .col-xxl-4 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-4 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-4:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-4:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-4 {
    margin-left: 33.33333%;
  }

  .col-xxl-5 {
    flex-basis: 41.66667%;
    max-width: 41.66667%;
  }

  .row .col-xxl-5 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-5 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-5:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-5:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-5 {
    margin-left: 41.66667%;
  }

  .col-xxl-6 {
    flex-basis: 50%;
    max-width: 50%;
  }

  .row .col-xxl-6 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-6 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-6:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-6:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-6 {
    margin-left: 50%;
  }

  .col-xxl-7 {
    flex-basis: 58.33333%;
    max-width: 58.33333%;
  }

  .row .col-xxl-7 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-7 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-7:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-7:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-7 {
    margin-left: 58.33333%;
  }

  .col-xxl-8 {
    flex-basis: 66.66667%;
    max-width: 66.66667%;
  }

  .row .col-xxl-8 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-8 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-8:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-8:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-8 {
    margin-left: 66.66667%;
  }

  .col-xxl-9 {
    flex-basis: 75%;
    max-width: 75%;
  }

  .row .col-xxl-9 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-9 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-9:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-9:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-9 {
    margin-left: 75%;
  }

  .col-xxl-10 {
    flex-basis: 83.33333%;
    max-width: 83.33333%;
  }

  .row .col-xxl-10 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-10 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-10:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-10:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-10 {
    margin-left: 83.33333%;
  }

  .col-xxl-11 {
    flex-basis: 91.66667%;
    max-width: 91.66667%;
  }

  .row .col-xxl-11 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-11 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-11:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-11:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-11 {
    margin-left: 91.66667%;
  }

  .col-xxl-12 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .row .col-xxl-12 {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1441px) and (min-width: 451px) {
  .row .col-xxl-12 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 1.5%;
    padding-right: 1.5%;
  }
}

@media screen and (min-width: 1441px) {
  html[data-browser*="UBrowser"] .row .col-xxl-12:first-child {
    margin-left: 0px;
  }

  html[data-browser*="UBrowser"] .row .col-xxl-12:last-child {
    margin-right: 0px;
  }

  .col-xxl-offset-12 {
    margin-left: 100%;
  }
}

body.v2 {
  background-color: #F7F7F9;
  margin: 0px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-family: Helvetica, Arial;
  font-size: 16px;
  position: relative;
  scroll-behavior: smooth !important;
}

body.v2 * {
  box-sizing: border-box;
}

body.v2 main {
  display: flex;
  flex-wrap: wrap;
}

body.v2 main .container {
  box-sizing: border-box;
  margin: 0 auto;
  padding: 32px;
}

body.v2 main .content-page {
  display: flex;
  flex-wrap: wrap;
  min-height: 100vh !important;
  height: fit-content;
  width: 100%;
  background-color: #FFFFFF;
}

@media (min-width: 821px) {
  body.v2 main .content-page {
    margin-left: 300px;
    width: calc(100% - 300px) !important;
  }
}

body.v2 main .side-bar {
  bottom: 0;
  top: auto;
  width: 100%;
  position: relative;
  background-color: #F7F7F9;
  margin-inline-start: auto;
}

body.v2 main .side-bar-desktop {
  position: fixed;
  min-width: 300px !important;
  max-width: 300px !important;
  max-height: 100vh;
  padding: 16px;
  overflow: auto !important;
}

@media (min-width: 821px) {
  body.v2 main .side-bar-desktop {
    min-height: 100vh;
    height: 100%;
  }
}

body.v2 main .side-bar-desktop .header {
  background-color: #E9E9EE;
  margin: -16px;
  padding-bottom: 16px;
}

body.v2 main .side-bar-desktop .header-img {
  max-width: 300px;
  padding-top: 35px;
  padding-bottom: 16px;
  padding-left: 32px;
  padding-right: 16px;
  margin-left: 16px;
  margin-right: 16px;
  cursor: pointer;
}

body.v2 main .side-bar-desktop .header .v2-select {
  position: relative;
  width: inherit;
  height: 40px;
  border-radius: 100px;
  outline: none;
  border: 1px solid #D2D2DA;
  background-color: #548AF7 !important;
  font-size: 14px;
  margin-left: 48px;
  margin-right: 32px;
}

body.v2 main .side-bar-desktop .header .v2-select:hover {
  border: 1px solid #E9E9EE;
}

body.v2 main .side-bar-desktop .header .v2-select:focus {
  border: 1px solid #E9E9EE;
}

body.v2 main .side-bar-desktop .header .v2-select-selected-text {
  color: #F7F7F9;
  height: inherit;
  padding-top: 12px;
  padding-left: 15px;
  padding-bottom: 11px;
  padding-right: 16px !important;
  background: url("../images/select_arrow.png") no-repeat 100%;
  background-origin: content-box;
  font-size: 14px;
  max-width: 100%;
  overflow: hidden !important;
  cursor: pointer;
  filter: brightness(0) invert(1);
}

body.v2 main .side-bar-desktop .header .v2-select-selected-text div {
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis !important;
  white-space: nowrap;
}

body.v2 main .side-bar-desktop .header .v2-select-content {
  display: none;
  position: absolute;
  z-index: 1000;
  min-height: 300px;
  max-height: 300px;
  outline: none;
  left: 0px !important;
  right: 0px !important;
  background-color: #FFFFFF !important;
  border: 1px solid #D2D2DA;
  overflow: hidden;
}

body.v2 main .side-bar-desktop .header .v2-select-content.isShow {
  display: block !important;
}

body.v2 main .side-bar-desktop .header .v2-select-content-header {
  display: none;
}

body.v2 main .side-bar-desktop .header .v2-select-content-search {
  width: 100%;
  height: 41px;
  outline: none;
  border: none;
  padding-top: 12px;
  padding-left: 15px;
  padding-bottom: 11px;
  padding-right: 16px !important;
  color: #303030;
  border-bottom: 1px solid #D2D2DA;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  background: url("../images/search_logo.png") no-repeat 100%;
  background-color: #FFFFFF !important;
  background-origin: content-box;
  font-size: 14px;
}

body.v2 main .side-bar-desktop .header .v2-select-content-search:placeholder {
  color: #303030;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

body.v2 main .side-bar-desktop .header .v2-select-content-search:hover {
  border: 1px solid #E9E9EE;
}

body.v2 main .side-bar-desktop .header .v2-select-content-search:focus {
  border: 1px solid #E9E9EE;
}

body.v2 main .side-bar-desktop .header .v2-select-content-options {
  max-height: 259px;
  overflow: auto !important;
}

body.v2 main .side-bar-desktop .header .v2-select-content-options-item {
  color: #303030;
  padding-left: 15px;
  padding-top: 5px;
  padding-bottom: 5px;
  line-height: 20px;
  left: 0;
  font-size: 14px;
  cursor: pointer;
}

body.v2 main .side-bar-desktop .header .v2-select-content-options-item:hover {
  background-color: #E9E9EE;
  color: #303030;
}

body.v2 main .side-bar-desktop .header .v2-select-content-options-item-selected {
  background-color: #E9E9EE;
  color: #303030;
}

body.v2 main .side-bar-desktop>* {
  margin-left: auto;
  max-width: 300px;
}

body.v2 main .side-bar-mobile {
  padding: 15px;
  min-height: 0 !important;
  height: auto;
}

body.v2 main .side-bar-mobile .header {
  background-color: #E9E9EE;
  margin: -16px;
  padding-bottom: 16px;
  padding-top: inherit;
}

body.v2 main .side-bar-mobile .header-img {
  width: 100px;
  display: block;
  margin-top: 20px;
  margin-bottom: 19px;
  margin-left: auto !important;
  margin-right: auto !important;
  cursor: pointer;
}

body.v2 main .side-bar-mobile .header-img img {
  width: 100px !important;
}

@media (max-width: 820px) {
  body.v2 main .side-bar-mobile .header .v2-select {
    position: relative;
    width: inherit;
    height: 40px;
    border-radius: 100px;
    outline: none;
    border: 1px solid #D2D2DA;
    background-color: #548AF7 !important;
    font-size: 14px;
    margin-left: 16px;
    margin-right: 16px;
  }

  body.v2 main .side-bar-mobile .header .v2-select-selected-text {
    color: #F7F7F9;
    height: inherit;
    padding-top: 12px;
    padding-left: 15px;
    padding-bottom: 11px;
    padding-right: 16px !important;
    background: url("../images/select_arrow.png") no-repeat 100%;
    background-origin: content-box;
    font-size: 14px;
    max-width: 100%;
    overflow: hidden !important;
    cursor: pointer;
    filter: brightness(0) invert(1);
    transition: all 0.3s;
  }

  body.v2 main .side-bar-mobile .header .v2-select-selected-text div {
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis !important;
    white-space: nowrap;
  }

  body.v2 main .side-bar-mobile .header .v2-select-selected-text:active {
    position: relative;
    top: 1px;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content {
    opacity: 0;
    visibility: hidden;
    visibility: 0s 0.5s;
    z-index: 5;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #FFFFFF;
    transform: translateY(200px);
    transition: opacity 0.5s, transform 0.5s, z-index 0s 0.5s;
    transition: all 0.3s;
    overflow-y: scroll;
    min-height: 100% !important;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-header {
    width: 100%;
    height: 41px;
    padding-top: 12px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #D2D2DA;
    background-color: #FFFFFF;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-header-title {
    font-size: 16px;
    text-align: center;
    font-weight: 600;
    color: #303030;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-search {
    width: 100%;
    height: 41px;
    outline: none;
    border: none;
    padding-top: 12px;
    padding-left: 15px;
    padding-bottom: 11px;
    padding-right: 16px !important;
    color: #303030;
    border-bottom: 1px solid #D2D2DA;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    background: url("../images/search_logo.png") no-repeat 100%;
    background-color: #FFFFFF !important;
    background-origin: content-box;
    font-size: 14px;
    position: sticky;
    top: 41px;
    z-index: 1;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-options {
    position: relative;
    height: 100%;
    font-size: 14px;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-options-item {
    font-weight: 300;
    text-decoration: none;
    display: block;
    color: #1d1f20;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 20px;
  }

  body.v2 main .side-bar-mobile .header .v2-select-content-options-item-selected {
    background-color: #E9E9EE;
  }

  body.v2 main .side-bar-mobile .header .v2-select .overlay-open {
    opacity: 1;
    visibility: visible;
    z-index: 15;
    transform: translateY(0);
    transition: opacity 0.5s, transform 0.5s, z-index 0s;
  }

  body.v2 main .side-bar-mobile .header .v2-select .overlay-close {
    position: absolute;
    right: 16px;
    top: 12px;
    cursor: pointer;
    transition: all 0.3s;
  }

  body.v2 main .side-bar-mobile .header .v2-select .overlay-close:after {
    font-family: "FontAwesome";
    content: '\f00d';
    font-size: 14px;
  }
}

body.v2 main .side-bar-mobile .back {
  display: block;
  margin-top: auto;
  margin-bottom: auto;
  height: 100%;
  padding: 16px;
}

body.v2 main .side-bar-mobile .back img {
  height: 16px;
  width: 9.6px;
}

body.v2 main .side-bar-mobile .no-x-margin {
  margin-top: -15px;
  margin-bottom: -15px;
}

body.v2 main .side-bar-mobile.side-bar-sticky-top,
body.v2 main .side-bar-mobile.side-bar-sticky-top-large {
  background-color: #F7F7F9;
  z-index: 1;
  min-height: 70px !important;
  height: 70px;
  position: fixed !important;
  top: 0;
}

body.v2 main .side-bar-mobile.side-bar-sticky-top-large {
  height: 270px !important;
}

body.v2 main .side-bar .search-form {
  padding-top: 32px;
}

@media (min-width: 821px) {
  body.v2 main .side-bar .search-form {
    padding: 32px 16px 0 32px;
  }
}

body.v2 main .side-bar .search-form .input-icons {
  width: 100%;
  height: 40px;
  border-radius: 100px;
  outline: none;
  padding-top: 12px;
  padding-left: 15px;
  padding-bottom: 11px;
  padding-right: 16px !important;
  margin-bottom: 16px;
  color: #303030;
  border: 1px solid #D2D2DA;
  background: url("../images/search_logo.png") no-repeat 100%;
  background-color: #FFFFFF !important;
  background-origin: content-box;
  font-size: 14px;
}

body.v2 main .side-bar .search-form .input-icons:hover {
  border: 1px solid #548AF7;
}

body.v2 main .side-bar .search-form .input-icons:focus {
  border: 1px solid #548AF7;
}

body.v2 main .side-bar .nav-bar {
  top: 0;
}

@media (min-width: 821px) {
  body.v2 main .side-bar .nav-bar {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
    position: relative;
  }
}

body.v2 main .side-bar .nav-bar-category {
  padding-top: 16px;
  padding-bottom: 19px;
  padding-left: 16px;
  padding-right: 16px;
  color: #9292A2;
  font-size: 12px;
  text-transform: uppercase;
}

body.v2 main .side-bar .nav-bar-list {
  padding-bottom: 16px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 16px;
  height: 100%;
}

body.v2 main .side-bar .nav-bar-list-item {
  color: #303030;
  line-height: 20px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

body.v2 main .side-bar .nav-bar-list-item img,
body.v2 main .side-bar .nav-bar-list-item i {
  margin-right: 16px;
  width: 18px;
}

body.v2 main .side-bar .nav-bar-list-item:first-child {
  padding-top: 19px;
}

body.v2 main .side-bar .nav-bar-list-item:not(:first-child) {
  padding-top: 30px;
}

@media (hover: hover) {
  body.v2 main .side-bar .nav-bar-list-item:hover {
    color: #548AF7;
    text-decoration: none;
  }

  body.v2 main .side-bar .nav-bar-list-item:hover img,
  body.v2 main .side-bar .nav-bar-list-item:hover i {
    filter: invert(50%) sepia(22%) saturate(4437%) hue-rotate(201deg) brightness(99%) contrast(96%);
  }
}

body.v2 main .side-bar .nav-bar-list-item.isActive {
  color: #548AF7;
  text-decoration: none;
}

body.v2 main .side-bar .nav-bar-horizontal-list {
  padding-bottom: 10px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  white-space: nowrap;
  cursor: pointer;
  overflow-x: scroll;
}

body.v2 main .side-bar .nav-bar-horizontal-list-item {
  line-height: 17px;
  border-radius: 60px;
  padding: 11px 20px;
  color: #303030;
  background-color: #FFFFFF;
  box-shadow: 0px 4px 7px rgba(169, 169, 169, 0.25);
  font-size: 14px;
  border: 1px solid transparent;
  margin-left: 1.5%;
  margin-right: 1.5%;
  text-decoration: none;
}

body.v2 main .side-bar .nav-bar-horizontal-list-item:first-child {
  margin-left: 0px;
}

body.v2 main .side-bar .nav-bar-horizontal-list-item:last-child {
  margin-right: 0px;
}

@media (hover: hover) {
  body.v2 main .side-bar .nav-bar-horizontal-list-item:hover {
    border: 1px solid #548AF7;
    color: #548AF7;
    background-color: rgba(84, 138, 247, 0.1);
  }
}

body.v2 main .side-bar .nav-bar-horizontal-list-item.isActive {
  border: 1px solid #548AF7;
  color: #548AF7;
  text-decoration: none;
  background-color: rgba(84, 138, 247, 0.1);
}

body.v2 main .main-page {
  background-color: #FFFFFF;
  height: 100%;
}

@media (max-width: 820px) {
  body.v2 main .main-page {
    padding: 0 15px 2% 15px !important;
  }

  body.v2 main .main-page.has-sticky-top {
    margin-top: 70px;
  }

  body.v2 main .main-page.has-sticky-top-large {
    margin-top: 270px !important;
  }
}

@media (min-width: 821px) {
  body.v2 main .main-page {
    padding: 0 30px 2% 30px !important;
  }

  html[data-browser*="UBrowser"] body.v2 main .main-page {
    min-height: 100% !important;
    height: auto !important;
  }
}

body.v2 main .main-page .page-section {
  width: 100%;
  height: fit-content;
  height: auto;
  padding-top: 10px;
  padding-bottom: 10px;
}

@media (min-width: 451px) {
  body.v2 main .main-page .page-section {
    border-top: 1px solid #E9E9EE;
    padding-top: 20px;
    padding-bottom: 40px;
  }
}

@media (min-width: 821px) {
  body.v2 main .main-page .page-section {
    min-height: 30% !important;
  }
}

body.v2 main .main-page .page-section:first-of-type {
  border-top: none !important;
}

body.v2 main .main-page .page-section-title {
  font-size: 20px;
  margin-top: 16px;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 29px;
}

body.v2 main .main-page .page-section-title img {
  width: 26px;
  height: 26px;
  margin-right: 16px;
  float: left;
}

body.v2 main .main-page .page-section .payment-section-anchor {
  position: relative;
}

@media (max-width: 820px) {
  body.v2 main .main-page .page-section .payment-section-anchor {
    top: -260px !important;
  }
}

body.v2 main .main-page .page-section-card {
  min-height: 100% !important;
  box-sizing: border-box !important;
  background-color: #FFFFFF;
  box-shadow: 0px 3px 5px 1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  position: relative;
  flex-direction: row;
}

@media (max-width: 821px) {
  body.v2 main .main-page .page-section-card {
    margin-inline-end: -3%;
  }
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card {
    box-shadow: none;
  }
}

body.v2 main .main-page .page-section-card a {
  text-decoration: none;
  display: flex;
  height: 100%;
}

@media (min-width: 451px) {
  body.v2 main .main-page .page-section-card a {
    flex-direction: column;
  }
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card a {
    margin: 0 8px 0 8px;
  }
}

body.v2 main .main-page .page-section-card a:hover {
  text-decoration: none;
}

body.v2 main .main-page .page-section-card:not([class*="-notfound"]) {
  outline: 1px solid #E9E9EE;
  border-radius: 6px;
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card:not([class*="-notfound"]) {
    outline: none;
    border-bottom: 1px solid #E9E9EE;
    border-radius: 0;
    margin-left: -3%;
    margin-right: -4.5%;
  }
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card:not([class*="-notfound"]):after {
    font-family: 'FontAwesome';
    content: "\f054";
    float: right;
    margin-right: 15px;
    margin-top: -16%;
    font-size: 16px;
  }
}

body.v2 main .main-page .page-section-card[class*="-notfound"] {
  border-radius: 16px;
  border: 1px dashed #548AF7 !important;
  margin-inline-end: 3%;
  margin-inline-start: 3%;
  margin-top: 3%;
}

body.v2 main .main-page .page-section-card[class*="-notfound"] .page-section-card-content-title {
  color: #9292A2;
}

body.v2 main .main-page .page-section-card-header {
  text-align: center;
  margin: auto;
  padding-right: 1.5%;
}

@media (min-width: 451px) {
  body.v2 main .main-page .page-section-card-header {
    width: 100% !important;
    border-bottom: 1px solid #E9E9EE;
  }
}

body.v2 main .main-page .page-section-card-header img {
  margin-top: 16px;
  margin-bottom: 16px;
  height: 70px !important;
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card-header img {
    width: 80px !important;
    object-fit: contain;
  }
}

body.v2 main .main-page .page-section-card-content {
  width: 100% !important;
  height: initial;
  flex-grow: 1;
  margin: 20px 0px 15px 0px;
  padding: 0px 15px 40px 15px;
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card-content {
    padding: 0;
  }
}

body.v2 main .main-page .page-section-card-content-title {
  color: #303030;
  font-weight: 500;
  display: inline;
  font-size: 14px;
}

body.v2 main .main-page .page-section-card-content-title b {
  color: #303030;
  margin-left: 32px;
}

body.v2 main .main-page .page-section-card-content-title b:after {
  content: "\a \a ";
  white-space: pre;
  margin-bottom: 16px;
}

body.v2 main .main-page .page-section-card-content-title a {
  margin-top: 16px;
  color: #9292A2;
  display: inline;
}

body.v2 main .main-page .page-section-card-content-title a[href] {
  color: #548AF7;
  text-decoration: none;
}

body.v2 main .main-page .page-section-card-content-subtitle {
  margin-top: 5px;
  font-size: 12px;
  font-weight: 400;
  color: #9292A2;
}

body.v2 main .main-page .page-section-card-content-subtitle-link {
  color: #548AF7;
  text-decoration: none;
}

body.v2 main .main-page .page-section-card-content-info {
  margin-top: 0px !important;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: start;
  vertical-align: middle;
  display: flex;
  row-gap: 5px;
  font-size: 12px;
  font-weight: 400;
}

body.v2 main .main-page .page-section-card-content-info-time,
body.v2 main .main-page .page-section-card-content-info-fee {
  margin-top: 19.5px !important;
  display: inline-block;
  justify-content: start;
  vertical-align: middle;
  row-gap: 16px;
}

body.v2 main .main-page .page-section-card-content-info-time p,
body.v2 main .main-page .page-section-card-content-info-fee p {
  padding-left: 16px;
}

body.v2 main .main-page .page-section-card-footer {
  width: 100% !important;
  outline: 1px solid #E9E9EE;
  background-color: #F7F7F9;
  text-align: center;
  margin: auto;
  line-height: 36px;
  bottom: 0;
  font-size: 14px;
  font-weight: 500;
  position: absolute;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

@media (max-width: 451px) {
  body.v2 main .main-page .page-section-card-footer {
    display: none !important;
  }
}

body.v2 main .main-page .page-section-card-footer span {
  color: #303030;
  text-decoration: none;
}

body.v2 main .main-page .page-header {
  color: #303030;
  font-size: 18px;
  line-height: 30px;
  top: 0;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  white-space: nowrap;
  margin-top: 30px;
}

body.v2 main .main-page .page-header img,
body.v2 main .main-page .page-header i {
  padding-right: 16px;
  cursor: pointer;
}

body.v2 main .main-page .page-header:hover {
  color: #548AF7;
  cursor: pointer;
}

body.v2 main .main-page .page-guide-section {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  width: 100%;
  min-height: 0;
  bottom: 0;
  padding-bottom: 40px;
}

html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section {
  height: auto !important;
}

html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section.col-12 {
  flex-basis: auto !important;
}

body.v2 main .main-page .page-guide-section-title {
  padding-top: 30px;
  padding-bottom: 30px;
  font-size: 20px;
  font-weight: 500;
}

body.v2 main .main-page .page-guide-section-step {
  height: inherit;
  top: 0;
  flex-direction: column;
}

html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section-step.col-12 {
  flex-basis: auto !important;
}

body.v2 main .main-page .page-guide-section-step p {
  display: table-row;
  width: 100%;
  line-height: 22px;
  font-size: 16px;
  color: #303030;
  margin-bottom: 40px;
}

@media (max-width: 822px) {
  body.v2 main .main-page .page-guide-section-step p {
    line-height: 20px;
    font-size: 14px;
    margin-bottom: 30px;
  }

  html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section-step p {
    max-width: calc(100% - 15px) !important;
  }
}

@media (min-width: 823px) {
  body.v2 main .main-page .page-guide-section-step p {
    line-height: 20px;
    font-size: 14px;
    margin-bottom: 30px;
  }

  html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section-step p {
    max-width: calc(100% - 15px) !important;
  }
}

body.v2 main .main-page .page-guide-section-step p em {
  display: table-cell;
  padding-right: 15px;
}

@media (max-width: 822px) {
  html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section-step p em {
    max-width: calc(100% - 15px) !important;
  }
}

@media (min-width: 823px) {
  html[data-browser*="UBrowser"] body.v2 main .main-page .page-guide-section-step p em {
    max-width: calc(100% - 15px) !important;
  }
}

body.v2 main .main-page .page-guide-section-step .step {
  display: table-cell;
  position: relative;
  padding: 14px 0 14px 0;
  line-height: 0;
  width: 28px;
  height: 28px;
  top: 0px;
  margin-right: 18px;
  border-radius: 50%;
  float: left;
  color: #ffffff;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  background-color: #548AF7;
}

@media (max-width: 820px) {
  body.v2 main .main-page .page-guide-section-step .step {
    top: -5px;
  }
}

body.v2 main .main-page .page-guide-section-step .st {
  width: 100%;
  font-weight: normal;
  color: #303030;
  line-height: normal;
  text-align: left;
}

body.v2 main .main-page .page-guide-section-step strong {
  color: #548AF7;
  font-size: 14px;
  font-weight: 500;
}

body.v2 main .main-page .page-guide-section-step .imgbox {
  width: inherit;
  object-fit: fill;
  border: 1px solid #E9E9EE;
  margin-top: 15px;
  margin-bottom: 60px;
  border-radius: 10px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 820px) {
  body.v2 main .main-page .page-guide-section-step .imgbox {
    width: 100vw;
    margin-left: -15px;
    border-radius: 0px;
  }
}

body.v2 main .main-page .page-guide-section-step .imgbox img {
  width: 100%;
  object-fit: fill;
  border-radius: 10px;
}

@media (max-width: 820px) {
  body.v2 main .main-page .page-guide-section-step .imgbox img {
    border-radius: 0px;
  }
}

body.v2 main .main-page-footer {
  min-height: 10% !important;
  text-align: center;
  padding: 16px;
  margin-bottom: 32px;
  border-top: 1px solid #E9E9EE;
}

@media (max-width: 821px -1px) {
  body.v2 main .main-page-footer {
    padding: 0px;
  }
}

@media (max-width: 820px) {
  body.v2 main .main-page-footer {
    bottom: 0px;
    background-color: #F7F7F9;
    margin-bottom: 0px !important;
  }
}

body.v2 main .main-page-footer.atBottom {
  width: 100%;
  position: relative;
  top: 20%;
  transform: translateY(-100%) translateY(100%);
}

body.v2 main .main-page-footer-actions {
  width: 100%;
  margin-left: auto !important;
  margin-right: auto !important;
  display: table;
  line-height: 32px;
  margin-top: 32px;
  margin-bottom: 25px;
  border-spacing: 10px;
}

@media (max-width: 820px) {
  body.v2 main .main-page-footer-actions {
    border-spacing: 10px 10px;
  }

  body.v2 main .main-page-footer-actions .btn {
    font-size: 12px !important;
  }
}

html[data-browser*="UBrowser"] body.v2 main .main-page-footer-actions.col-10 {
  flex-basis: auto !important;
}

html[data-browser*="UBrowser"] body.v2 main .main-page-footer-actions.col-12 {
  flex-basis: auto !important;
}

body.v2 main .main-page-footer-actions .btn {
  height: 36px;
  width: 25%;
  display: table-cell;
  vertical-align: middle;
  max-height: 64px;
  background-color: #FFFFFF;
  border: 1px solid #D2D2DA;
  color: #303030;
  padding: 9px 20px;
  text-decoration: none;
  touch-action: manipulation;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
  line-height: 17px;
  margin: 0px;
  white-space: normal;
  word-break: keep-all;
}

@media (max-width: 820px) {
  body.v2 main .main-page-footer-actions .btn {
    height: 24px !important;
    font-size: 11px;
  }
}

body.v2 main .main-page-footer-actions .btn:hover {
  color: #FFFFFF;
  background-color: #548AF7;
}

body.v2 main .main-page-footer-actions .btn-active {
  color: #FFFFFF;
  background-color: #548AF7;
}

body.v2 main .main-page-footer-actions .btn-active:hover {
  cursor: not-allowed;
}

body.v2 main .main-page-footer-content {
  color: #9292A2;
  font-size: 14px;
  font-weight: 400;
  padding-bottom: 14px;
}

body.v2 main .main-page-footer-content b {
  color: #303030;
  font-weight: 400;
}

body.v2 main .main-page-footer-content a {
  text-decoration: none;
  color: #fff;
  font-weight: 400;
}

body.v2 main .main-page-footer-content a:hover {
  cursor: pointer;
}

@media (max-width: 820px) {
  body.v2 .spinner-main {
    z-index: 1;
    position: fixed;
    top: 55%;
    left: 43%;
  }

  body.v2 .spinner-main .spinner-general {
    border-top-color: #FF4130;
    border-left-color: #FF4130;
  }

  body.v2 .spinner-main .spinner-slow {
    animation: spinner 2s linear infinite;
  }

  body.v2 .spinner-main .spinner-large {
    width: 5rem;
    height: 5rem;
    border-width: 6px;
  }

  body.v2 .spinner-main .spinner {
    width: 70px;
    height: 70px;
    border-top-color: #444;
    border-left-color: #444;
    animation: spinner 1s linear infinite;
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-style: solid;
    border-width: 2px;
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: middle;
  }
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

body.v2 .text-blue {
  color: #548AF7;
}

body.v2 .text-green {
  color: #1DBF73;
}

body.v2 .text-red {
  color: #FF4130;
}