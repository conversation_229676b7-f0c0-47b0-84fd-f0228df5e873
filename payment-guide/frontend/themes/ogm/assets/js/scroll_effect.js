// var $window = $(window);

// $('.nav-bar-list-item, .nav-bar-horizontal-list-item').not('[href="#"]').not('[href="#0"]').click(function(event) {
//     if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
//         var target = $(this.hash);
//         var $mobileNav = target.offset().top - 10 - $('.side-bar-mobile:visible').height();
//         target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
//         $window.off('scroll.scroll_effect');
//         $(document.body).off('touchmove.scroll_effect');
//         if (target.length) {
//             event.preventDefault();
//             $('html, body').animate({
//                 scrollTop: $window.width() <= 768 ? $mobileNav : target.offset().top
//             }, 800).promise().then(function(){
//                 // Animation complete
//                 // Need a timeout because this handler is fired before scrollTop reach the final position
//                 window.setTimeout(function(){
//                     $(document.body).on('touchmove.scroll_effect', onScroll); // for mobile view need to use $(document.body) and 'touchmove'
//                     $(window).on('scroll.scroll_effect', onScroll);  // for not mobile view 
//                    }, 100);
//             })
//         }
//     }
//     // $('nav-bar-horizontal-list-item isActive').removeClass('isActive');
//     $('.nav-bar-horizontal-list-item').not('[href="#"]').not('[href="#0"]').removeClass("isActive");
//     $('.nav-bar-list-item').removeClass("isActive");
//     // add class to the one we clicked
//     $(this).addClass("isActive");
// });

// function onScroll() {
//     $('.nav-bar-horizontal-list-item').removeClass('isActive');
//     $('.nav-bar-list-item').removeClass("isActive");
// }

// //add margin-top to push footer to bottom
// function adjustFooter(){
// $('.main-page-footer').css('margin-top',
//     $window.width() >768 ?
//      $(document).height() - ($('.main-page').height()): $(document).height() - ($('.main-page').height() + $('.side-bar-mobile')));

// };

// $window.on('resize.footer', function(){
//     adjustFooter();
// });

// $(document).ready(function(){
//     adjustFooter();
// });

(function($, window, document, undefined){
	
	var OnePageNav = function(elem, options){
		this.elem = elem;
		this.$elem = $(elem);
		this.options = options;
		this.metadata = this.$elem.data('plugin-options');
		this.$win = $(window);
		this.sections = {};
		this.didScroll = false;
		this.$doc = $(document);
		this.docHeight = this.$doc.height();
	};

	// the plugin prototype
	OnePageNav.prototype = {
		defaults: {
			navItems: 'a',
			currentClass: 'isActive',
			changeHash: true,
			easing: 'swing',
			filter: '',
			scrollSpeed: 700,
			scrollThreshold: 0.5,
			begin: true,
			end: true,
			scrollChange: false,
		},

		init: function() {
			this.config = $.extend({}, this.defaults, this.options, this.metadata);

			this.$nav = this.$elem.find(this.config.navItems);

			//Filter any links out of the nav
			if(this.config.filter !== '') {
				this.$nav = this.$nav.filter(this.config.filter);
			}

			//Handle clicks on the nav
			this.$nav.on('click.onePageNav', $.proxy(this.handleClick, this));

			//Get the section positions
			this.getPositions();

			//Handle scroll changes
			this.bindInterval();

			//Update the positions on resize too
			this.$win.on('resize.onePageNav', $.proxy(this.getPositions, this));
			this.$doc.on('resize.onePageNav', $.proxy(this.getPositions, this));
		
			// add isActive class to the first link in the nav if there is no isActive class
			if(!this.$nav.hasClass(this.config.currentClass) && window.location.hash === '') {
				this.$nav.first().addClass(this.config.currentClass);
			}

			if(!this.$nav.hasClass(this.config.currentClass) && window.location.hash !== '') {
				var position = this.getSection(this.$win.scrollTop());
				$parent = this.$elem.find('a[href$="#' + position + '"]');
				this.adjustNav(this, $parent);
			}

			return this;
		},

		adjustNav: function(self, $parent) {
			self.$elem.find('.' + self.config.currentClass).removeClass(self.config.currentClass);
			$parent.addClass(self.config.currentClass);

			// nav-bar-horizontal-list animate scrolling
			self.$elem.animate({
				scrollLeft: $parent.offset().left + self.$elem.scrollLeft() - 15
			})
		},

		bindInterval: function() {
			var self = this;
			var docHeight;

			self.$win.on('scroll.onePageNav touchmove.onePageNav', function() {
				self.didScroll = true;
			});

			self.t = setInterval(function() {
				docHeight = self.$doc.height();

				//If it was scrolled
				if(self.didScroll) {
					self.didScroll = false;
					self.scrollChange();
				}

				//If the document height changes
				if(docHeight !== self.docHeight) {
					self.docHeight = docHeight;
					self.getPositions();
				}
			}, 250);
		},

		getHash: function($link) {
			return $link.attr('href').split('#')[1];
		},

		getPositions: function() {
			var self = this;
			var linkHref;
			var topPos;
			var $target;

			self.$nav.each(function() {
				linkHref = self.getHash($(this));
				$target = $('#' + linkHref);

				if($target.length) {
					topPos = $target.offset().top;
					self.sections[linkHref] = Math.round(topPos);
				}
			});
		},

		getSection: function(windowPos) {
			var currentSection = null;
			var lastSection = null;
			var windowHeight = Math.round(this.$win.height() * this.config.scrollThreshold);
			var atTheBottom = windowPos >= this.docHeight - this.$win.height();
			var currentPosition = windowPos + windowHeight;
			var atTheTop = this.$win.scrollTop() <= this.$win.height() * 0.1;
			
			for(var section in this.sections) {
				if(this.sections[section] >= this.sections[lastSection || section]) {
					lastSection = section;
				}
				if(this.sections[section] < currentPosition && this.sections[section] >= this.sections[currentSection || section]) {
					currentSection = section;
				}
				if (atTheTop) {
					currentSection = Object.keys(this.sections)[0];
				}
			}
			
			return atTheBottom ? lastSection : currentSection;
		},

		handleClick: function(e) {
			var self = this;
			var $link = $(e.currentTarget);
			var $parent = $link;
			var newLoc = '#' + self.getHash($link);

			if(!$parent.hasClass(self.config.currentClass)) {
				//Start callback
				if(self.config.begin) {
					self.config.begin();
				}

				//Change the highlighted nav item
				self.adjustNav(self, $parent);

				//Removing the auto-adjust on scroll
				self.unbindInterval();

				//Scroll to the correct position
				self.scrollTo(newLoc, function() {
					//Do we need to change the hash?
					if(self.config.changeHash) {
						e.preventDefault();
					 	history.pushState(null, null, newLoc);
					}

					//Add the auto-adjust on scroll back in
					self.bindInterval();

					//End callback
					if(self.config.end) {
						self.config.end();
					}
				});
			}

			e.preventDefault();
		},

		scrollChange: function() {
			var windowTop;
			
			if	( $(window).width() == 1366) {
				windowTop = this.$win.scrollTop() - 150;
			}
			else {
				windowTop = this.$win.scrollTop();
			}
		
			var position = this.getSection(windowTop);
			var $parent;

			//If the position is set
			if(position !== null) {
				$parent = this.$elem.find('a[href$="#' + position + '"]');

				//If it's not already the current section
				if(!$parent.hasClass(this.config.currentClass)) {
					//Change the highlighted nav item
					this.adjustNav(this, $parent);

					//If there is a scrollChange callback
					if(this.config.scrollChange) {
						this.config.scrollChange($parent);
					}
				}
			}
		},

		scrollTo: function(target, callback) {
			var offset =  $(target).offset().top;
			
			$('html, body').animate({
				scrollTop: offset
			}, this.config.scrollSpeed, this.config.easing, callback);
		},

		unbindInterval: function() {
			clearInterval(this.t);
			this.$win.unbind('scroll.onePageNav touchmove.onePageNav');
			
		}
	};

	OnePageNav.defaults = OnePageNav.prototype.defaults;

	$.fn.onePageNav = function(options) {
		return this.each(function() {
			new OnePageNav(this, options).init();
		});
	};

})( jQuery, window , document );

$(document).ready(function(){
    adjustFooter();
	$('.nav-bar-list, .nav-bar-horizontal-list').onePageNav({
		begin: function() {
			$('body').append('<div id="device-dummy" style="height: 1px;"></div>');
		},
		end: function() {
			$('#device-dummy').remove();
		}
	});
	$(window).on('resize', adjustFooter);
	//$(window).on('resize', 	function() {document.title = "X: " +$(window).width() + "	Y:" + $(window).height()});
});

//add margin-top to push footer to bottom
function adjustFooter(){
$('.main-page-footer').css('margin-top',
    $(window).width() > 820 ?
    $(document).height() - ($('.main-page').height()): 
	$(document).height() - ($('.main-page').height() + $('.side-bar-mobile')));
};
