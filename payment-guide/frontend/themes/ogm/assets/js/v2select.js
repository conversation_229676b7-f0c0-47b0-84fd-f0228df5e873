var v2select, v2select_i, v2select_select_i, v2select_l, v2select_select_l, v2select_select, v2select_selected_text, v2select_content, v2select_content_options, v2select_content_options_item, v2select_search;
/*look for any elements with the class "custom-select":*/
v2select = document.getElementsByClassName("v2-select");
v2select_l = v2select.length;
for (v2select_i = 0; v2select_i < v2select_l; v2select_i++) {
    v2select_select = v2select[v2select_i].getElementsByTagName("select")[0];
    v2select_select_l = v2select_select.length;
    /*for each element, create a new DIV that will act as the selected item:*/
    v2select_selected_text = document.createElement("DIV");
    v2select_selected_text.setAttribute("class", "v2-select-selected-text");
    v2select_selected_text_content = document.createElement("DIV");
    v2select_selected_text_content.innerHTML = v2select_select.options[v2select_select.selectedIndex].innerHTML;
    v2select_selected_text.appendChild(v2select_selected_text_content);
    v2select[v2select_i].appendChild(v2select_selected_text);

     // create overlay menu close button (only for mobile view)
     let v2select_content_header = document.createElement("DIV");
     v2select_content_header.setAttribute("class", "v2-select-content-header")
     let v2select_content_header_title = document.createElement("v2-select-content-header-title");
     v2select_content_header_title.innerHTML = "Select currency";
     v2select_content_header_title.setAttribute("class", "v2-select-content-header-title");
     let overlay_close = document.createElement("DIV");
     overlay_close.setAttribute("class", "overlay-close");
     v2select_content_header.appendChild(v2select_content_header_title);
     v2select_content_header.appendChild(overlay_close);
    /*for each element, create a new DIV that will contain the option list:*/
    v2select_search = document.createElement("input");
    v2select_search.setAttribute("class", "v2-select-content-search");
    v2select_search.setAttribute("name", "v2_select_content_search");
    v2select_search.setAttribute("placeholder", "Type to search...");
    v2select_search.setAttribute("autocomplete", "off");
    v2select_content = document.createElement("DIV");
    v2select_content.setAttribute("class", "v2-select-content");
    v2select_content_options = document.createElement("DIV");
    v2select_content_options.setAttribute("class", "v2-select-content-options");
    for (v2select_select_i = 1; v2select_select_i < v2select_select_l; v2select_select_i++) {
        /*for each option in the original select element,
        create a new DIV that will act as an option item:*/
        v2select_content_options_item = document.createElement("DIV");
        v2select_content_options_item.setAttribute("class", "v2-select-content-options-item");
        v2select_content_options_item.innerHTML = v2select_select.options[v2select_select_i].innerHTML;
        v2select_content_options_item.setAttribute("data-text", v2select_select.options[v2select_select_i].innerHTML);
        v2select_content_options_item.addEventListener("click", function(e) {
            /*when an item is clicked, update the original select box,
            and the selected item:*/
            var selected_item, select_i, selected_item_i, select, selected_text, select_l, selected_item_l;
            select = this.parentNode.parentNode.parentNode.getElementsByTagName("select")[0];
            select_l = select.length;
            selected_text = this.parentNode.parentNode.previousSibling;
            for (select_i = 0; select_i < select_l; select_i++) {
                if (select.options[select_i].innerHTML == this.innerHTML) {
                    select.selectedIndex = select_i;
                    selected_text.innerHTML = this.innerHTML;
                    selected_item = this.parentNode.getElementsByClassName("v2-select-content-options-item-selected");
                    console.log(selected_item);
                    selected_item_l = selected_item.length;
                    for (selected_item_i = 0; selected_item_i < selected_item_l; selected_item_i++) {
                        selected_item[selected_item_i].setAttribute("class", "v2-select-content-options-item");
                    }
                    this.classList.add("v2-select-content-options-item-selected");
                    setLocaleParameter("currency",(select.options[select_i].value.toUpperCase()));
                    break;
                }
            }
            selected_text.parentNode.click();
        });
        v2select_content_options.appendChild(v2select_content_options_item);
    }

    v2select_search.addEventListener("keyup", function(evt){
        let type_value = evt.currentTarget.value;
        filterSearchOptions(evt, type_value);
    })

    v2select_content.appendChild(v2select_content_header);
    v2select_content.appendChild(v2select_search); 
    v2select_content.appendChild(v2select_content_options);
    v2select[v2select_i].appendChild(v2select_content);
    v2select[v2select_i].targetChild = v2select_content;
    v2select_selected_text.addEventListener("click", function(evt) {
        /*when the select box is clicked, close any other select boxes,
        and open/close the current select box:*/
        evt.stopPropagation();
        closeAllSelect(this);
        evt.currentTarget.nextSibling.classList.toggle("isShow")
    });
}

function closeAllSelect(evt) {
    /*a function that will close all select boxes in the document,
    except the current select box:*/
    let close_able = false
    if(evt.srcElement){
        if(evt.srcElement.nodeName != "INPUT"){
            close_able = true
        } else {
            if (evt.srcElement.name != "v2_select_content_search"){
                close_able = true
            }
        }
    } else {
        close_able = true
    }

    if(close_able){
        var select_content, selected_text, i, select_content_l, selected_text_l, selectedNote = [];
        select_content = document.getElementsByClassName("v2-select-content");
        selected_text = document.getElementsByClassName("v2-select-selected-text");
        select_content_l = select_content.length;
        selected_text_l = selected_text.length;
        for (i = 0; i < selected_text_l; i++) {
            if (evt == selected_text[i]) {
                selectedNote.push(i)
            }
        }
        for (i = 0; i < select_content_l; i++) {
            if (selectedNote.indexOf(i)) {
                select_content[i].classList.remove("isShow");
            }
        }
    }
}

function filterSearchOptions(evt, type_value){
    let target_search = evt.target
    let target_options = target_search.nextSibling
    let option_items = target_options.getElementsByClassName("v2-select-content-options-item");
    let target_value = type_value.toUpperCase()

    for (item_i = 0; item_i < option_items.length; item_i++) {
        txtValue = option_items[item_i].textContent || option_items[item_i].innerText;
        if (txtValue.toUpperCase().indexOf(target_value) > -1) {
        // if (txtValue.indexOf(type_value) > -1) {
            option_items[item_i].style.display = "";
        } else {
            option_items[item_i].style.display = "none";
        }
    }
}

document.addEventListener("click", closeAllSelect);

// highlight selected currency in overlay menu
$(document).ready(function() {
    let index = $('.select')[1].selectedIndex -1;
    for (let i = 0; i < $(".v2-select-content-options-item:visible").length; i++) {
        if (i == index) {
            $(".v2-select-content-options-item:visible")[i].className += " v2-select-content-options-item-selected";
        }
    }
});

$(".v2-select-selected-text").click(function () {
    if ($(window).width() <= 820) {
        $(".v2-select-content").addClass("overlay-open");
        $("body").css({"overflow": "hidden", "position" : "fixed" });
    }
  });

    
$(".v2-select-content-options-item").click(function () {
    $(".v2-select-content").removeClass("overlay-open");
    $('body.v2').append('<div class="spinner-main spinner"><span class="spinner spinner-slow spinner-large spinner-general"></span></div>');
    $(window).width() <= 820 ? $('.content-page').hide() :  $('.content-page').show();
    $('spinner-main spinner').show()
});
  

  
$(".overlay-close, .v2-select-content-options-item").click(function () {
    $(".v2-select-content").removeClass("overlay-open");
    $("body").css({ "overflow" : "", "position" : ""});
});
  
