<?php

use frontend\components\LayoutManager;

$this->beginBlock('sideBar');
echo $this->context->renderPartial('/layouts/_sidebar.php');
$this->endBlock();

$title = $description['name'];

$this->title = Yii::t('app', $title . " - " . Yii::$app->params['site_title']);
Yii::$app->params["meta-description"] = $description['description'];
Yii::$app->params["meta-keywords"] = $this->title . "," . Yii::t('app', 'global-meta');
?>

<div id="mainWrapper">
    <div class="mainContent">

        <!-- Header -->
        <?php echo $this->context->renderPartial('/layouts/_headerrow.php', ['title' => $title]); ?>


        <!-- Page Description -->
        <div class="description-Row">
            <?php echo $description['description']; ?>
        </div>


        <!-- Where to buy -->
        <?php
        if ($models) {
            foreach ($models as $k => $v) {
                echo "<div class='giftcard-Row'>"
                    . "<img  alt='{$v['name']}' class='Content-gc-logo-big' src='" . $v['icon_url'] . "'>"
                    . "<div class='Content-gc'>"
                    . "<h1>" . $v['name'] . "</h1>"
                    . "<p class='description'>" . $v['description'] . "</p>"
                    . "</div>"
                    . "<div class='Content-gc-btn'>";
                if (!empty($v['additional_button'])) {
                    foreach ($v['additional_button'] as $key => $value) {
                        echo "<div class='btn'><a href='" . $value . "' target='_blank'>" . $key . "</a></div>";
                    };
                }
                echo "</div>";
                if ($v['is_sub_merchant'] === 1) {
                    echo "<div class='Content-gc-extra'>"
                        . "<div class='Content-gc-extra-info'>"
                        . "Store list via " . $v['name'] . " terminal<br><br>";
                    foreach ($v['merchant-sub'] as $key => $value) {
                        echo "<div class='Content-gc-extra-logo'>"
                            . "<img src='" . $value['icon_url'] . "' alt='{$value['name']}' width='70' height='38' class='Content-gc-logo-small'>
                                 </div>";
                    }

                    echo "</div>"
                        . "</div>";

                }

                echo "</div>";
            }
        } else {
            echo Yii::t('app','gift-card-no-content');
        }

        ?>

    </div>

</div>
