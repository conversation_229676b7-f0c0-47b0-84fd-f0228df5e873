<?php

use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use frontend\components\LayoutManager;


$publishedUrl = Yii::$app->assetManager->getPublishedUrl('@assets');
$request = Yii::$app->request;
$cat = $request->get('cat');

$controller = Yii::$app->controller->id . "/" . Yii::$app->controller->action->id;
?>

<?php
    if($types == "left"){
?>
    <section class="side-bar side-bar-desktop hidden-lg-down">
       <div class="header">
            <div class="header-img" onclick="window.location='/payment-guide/<?php echo ($_SESSION['language'] === 'en-US' ? '' : $_SESSION['language']) ?>';">
                <img alt="logo" src="
                    <?php
                        echo $publishedUrl . '/images/logo_big.png';
                    ?>
                "/>
            </div>
            <div class="v2-select">
                    <select class="select" style="display: none!important;">
                        <option value="0" disabled>Currency Options</option>
                        <?php
                            $currencyList = LayoutManager::getCurrencyList();
                            ArrayHelper::multisort($currencyList, ['name'], [SORT_ASC]);
                            $selected_currency = Yii::$app->session->get('currencycode');
                            foreach ($currencyList as $currency) {
                                if(strtoupper($currency['currency_code']) == $selected_currency){
                                    $selected = " selected";
                                } else {
                                    $selected = "";
                                } ?>
                                <option value="<?php echo strtoupper($currency['currency_code']); ?>"<?php echo $selected; ?>><?php echo $currency['name'] . " ("  . strtoupper($currency['currency_code']) . ")"; ?></option>
                        <?php } ?>
                    </select>
                </div>
        </div>
        
        <div class="search-form">
            <form action="/payment-guide<?php echo ($_SESSION['language'] === 'en-US' ? '' : "/".$_SESSION['language']."") ?>/search" method="get">
                <input type="text" class="input-icons" name="q" title="Search" value="" autocomplete="off" placeholder="Search payment method"/>
            </form>          
        </div>
        <div class="nav-bar">
            <div class="nav-bar-category">
                <?php echo Yii::t('app', 'theme-sidebar-header'); ?>
            </div>
            <div class="nav-bar-list">
                <?php
                    $categories = LayoutManager::getSidebar();
                    if($categories){
                        if(isset($categories["preferred"]) && $categories["preferred"]){
                ?>
                    <a class="nav-bar-list-item<?php echo (!is_null($cat)) ? ($cat == 'recommended') ? ' isActive' : '' : ''; ?>" href="/payment-guide<?php echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . 'recommended';?>">
                        <!-- <i class="fa <?php // echo LayoutManager::getIcon("recommended");?> text-red" aria-hidden="true"></i> -->
                        <img alt="logo" src="
                            <?php
                                echo $publishedUrl . '/images/recommended.svg';
                            ?>
                        "/>
                        <?php echo Yii::t('app', 'payment-recommended-1');  ?>
                    </a>
                <?php
                        }
                        foreach ($categories["data"] as $k => $v) { 
                            if($v["display"]){?>
                    
                                <a class="nav-bar-list-item<?php echo (!is_null($cat)) ? ($cat == $v['url_name']) ? '' : ''  :  ''; ?>" href="/payment-guide<?php echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . $v['url_name']?>">
                                    <!-- <i class="fa <?php // echo LayoutManager::getIcon($v['url_name']);?>" aria-hidden="true"></i> -->
                                    <img alt="logo" src="
                                        <?php
                                            echo $publishedUrl . "/images/".$v['url_name'].".svg";
                                        ?>
                                    "/>
                                    <?php echo Html::encode($v['name']); ?>
                                </a>
                            
                <?php       }
                        }
                    } ?>
                
                <!-- <a class="nav-bar-list-item<?php //echo (!is_null($cat)) ? ($cat == 'store-credit-gift-card') ? '' : '' : ''; ?>" href="/payment-guide<?php //echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . 'store-credit-gift-card';?>">
                    <i class="fa <?php // echo LayoutManager::getIcon("store-credit-gift-card");?>" aria-hidden="true"></i>
                    <img alt="logo" src="
                        <?php
                           // echo $publishedUrl . "/images/store-credit-gift-card.svg";
                        ?>
                    "/>
                    <?php // echo Yii::t('payment-category', 'payment-category-name-8');  ?>
                </a> -->
            </div>
        </div>         
    </section>
    <?php if(isset($controller) && !in_array($controller, array("user-guide/index"))){?>
        <section class="side-bar side-bar-mobile side-bar-sticky-top-large col-sm-12 col-xs-12 hidden-lg-up">
        <div class="header">   
            <div class="header-img" onclick="window.location='/payment-guide/<?php echo ($_SESSION['language'] === 'en-US' ? '' : $_SESSION['language']) ?>';">
                    <img alt="logo" src="
                        <?php
                            echo $publishedUrl . '/images/logo_big.png';
                        ?>
                    "/>
            </div>
            <div class="v2-select">
                    <select class="select" style="display: none;">
                        <option value="0" disabled>Currency Options</option>
                        <?php
                            $currencyList = LayoutManager::getCurrencyList();
                            ArrayHelper::multisort($currencyList, ['name'], [SORT_ASC]);
                            foreach ($currencyList as $currency) { 
                                if(strtoupper($currency['currency_code']) == $selected_currency){
                                    $selected = " selected";
                                } else {
                                    $selected = "";
                                } ?>
                                <option value="<?php echo strtoupper($currency['currency_code']); ?>"<?php echo $selected; ?>><?php echo $currency['name'] . " ("  . strtoupper($currency['currency_code']) . ")"; ?></option>
                        <?php } ?>
                    </select>
            </div>
        </div>
            <div class="search-form">
                <form action="/payment-guide<?php echo ($_SESSION['language'] === 'en-US' ? '' : "/".$_SESSION['language']."") ?>/search" method="get">
                    <input type="text" class="input-icons" name="q" title="Search" value="" autocomplete="off" placeholder="Search payment method"/>
                </form>
            </div>
            <div class="nav-bar nav-bar-horizontal">
                <div class="nav-bar-horizontal-list">
                    
                    <?php
                        $categories = LayoutManager::getSidebar();
                        if($categories){
                            if(isset($categories["preferred"]) && $categories["preferred"]){
                    ?>
                        <a class="nav-bar-horizontal-list-item<?php echo (!is_null($cat)) ? ($cat == 'recommended') ? ' isActive' : '' : ''; ?>" href="/payment-guide<?php echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . 'recommended';?>">
                            <?php echo Yii::t('app', 'payment-recommended-1');  ?>
                        </a>
                    <?php
                            }
                            foreach ($categories["data"] as $k => $v) { 
                                if($v["display"]){ ?>
                                    <a class="nav-bar-horizontal-list-item" href="/payment-guide<?php echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . $v['url_name']?>">
                                        <?php echo Html::encode($v['name']); ?>
                                    </a>
                                
                    <?php       }
                            }
                        } ?>
                    <!-- <a class="nav-bar-horizontal-list-item<?php //echo (!is_null($cat)) ? ($cat == 'store-credit-gift-card') ? ' isActive' : '' : ''; ?>" href="/payment-guide<?php //echo (Yii::$app->session->get('language') === 'en-US' ? '/#' : "/" . Yii::$app->session->get('language') . '#') . 'store-credit-gift-card';?>">
                        <?php //echo Yii::t('payment-category', 'payment-category-name-8');  ?>
                    </a> -->
                </div>
            </div>
        </section>
    <?php } else { ?>
        <section class="side-bar side-bar-mobile side-bar-sticky-top col-xxl-3 col-xl-3 col-lg-3 col-md-12 col-sm-12 col-xs-12 hidden-lg-up">
            <div class="row no-x-margin">
                <a class="back" style="float:left" <?php echo " href='/payment-guide/".($_SESSION['language'] == "en-US" ? '' : $_SESSION['language'])."'" ?>>
                    <img src="
                        <?php
                            echo $publishedUrl . '/images/back_arrow.png';
                        ?>
                    "/>
                </a>
                <div class="header-img" onclick="window.location='/payment-guide/<?php echo ($_SESSION['language'] === 'en-US' ? '' : $_SESSION['language']) ?>';">
                    <img alt="logo" src="
                        <?php
                            echo $publishedUrl . '/images/logo_big.png';
                        ?>
                    "/>
                </div>
            </div>
        </section>
    <?php } ?>
<?php } elseif ($types == "right") { ?>

    <?php if($list){ ?>

        <section class="side-bar col-xxl-4 col-xl-4 col-lg-4 col-md-12 col-sm-12 col-xs-12">
            <div class="nav-bar">
                <div class="nav-bar-category">
                    Popular Guide in <?php echo $list['payment_category_name'];?>
                </div>
                <div class="nav-bar-list">
                    <?php foreach ($list['models'] as $k => $v) { ?>
                        <a class="nav-bar-list-item" href="<?php echo '/payment-guide/pg/' . ($_SESSION['language'] == 'en-US' ? "" : $_SESSION['language'] . "/" ). $list['cat'] . '/' . $v['payment_method_url_name'] ?>"><?php echo $v['name']; ?></a>
                    <?php } ?>
                </div>
            </div>
        </section>

    <?php } ?>

<?php } else { ?>
    <p></p>
<?php } ?>