<?php

/* @var $this \yii\web\View */

/* @var $content string */

use yii\helpers\Html;


use frontend\assets\AppAsset;

use frontend\components\LayoutManager;

AppAsset::register($this);
$publishedUrl = Yii::$app->assetManager->getPublishedUrl('@assets');

$this->registerJS(
    LayoutManager::generateRedirectScript()
    , \yii\web\view::POS_END);

?>

<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?php echo Yii::$app->language; ?>">
    <head>
        <meta charset="<?php echo Yii::$app->charset; ?>">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="<?php echo Yii::$app->params["meta-description"]; ?>">
        <meta name="keywords" content="<?php echo Yii::$app->params["meta-keywords"]; ?>">
        <link rel="shortcut icon" href="<?php echo $publishedUrl . '/images/favicon.ico'; ?>" type="image/x-icon"/>
        <?php echo Html::csrfMetaTags() ?>
        <title><?php echo Html::encode($this->title); ?></title>
        <?php $this->head() ?>
    </head>
    <body class="v2">
        <?php $this->beginBody() ?>
            <main class="col-12">

                <?php echo $this->context->renderPartial('/layouts/_sidebar.php', ['types'=>'left']); ?>

                <div class="content-page">
                    <?php echo $content ?>

                    <?php
                        if (isset($this->blocks['rightSideBar'])) {
                            echo $this->blocks['rightSideBar'];
                        }
                    ?>
                    <?php
                        if (isset($this->blocks['outerFooter'])) {
                            echo $this->blocks['outerFooter'];
                        }
                    ?>
                </div>

            </main>
        <?php $this->endBody() ?>
    </body>
</html>
<?php $this->endPage() ?>
