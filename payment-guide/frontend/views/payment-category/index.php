<?php


use frontend\components\LayoutManager;

/* @var $this yii\web\View */
/* @var $searchModel common\models\PaymentCategorySearch */
/* @var $dataProvider yii\data\ActiveDataProvider */
$this->title = "Home Page - " . Yii::$app->params['site_title'];
Yii::$app->params["meta-description"] = Yii::t('app', 'home-description');
Yii::$app->params["meta-keywords"] = Yii::t('app', 'global-meta');
$publishedUrl = Yii::$app->assetManager->getPublishedUrl('@assets');
$has_category = false;
?>

<section class="main-page has-sticky-top-large col-xxl-12 col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
    <!-- Beginning of "Recommended" category -->
    <?php if(isset($recommendeds) && (count($recommendeds) >= 1)){?>
    <div class="page-section col-12">
        <div class="payment-section-anchor" id="recommended"></div>
        <div class="page-section-title col-12">
            <!-- <i class="fa fa-star text-red" aria-hidden="true"></i> -->
            <img alt="logo" src="
                    <?php
                        echo $publishedUrl . '/images/recommended.svg';
                    ?>
                " />
            <?php echo Yii::t('app', 'payment-recommended-1');  ?>
        </div>
        <div class="col-12">
            <div class="row">
                <?php 
                        foreach($recommendeds as $recommended){ ?>
                <div class=" col-xxl-3 col-xl-3 col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="page-section-card">
                        <a
                            <?php echo " href='/payment-guide/pg/".($_SESSION['language'] == "en-US" ? '' : $_SESSION['language'] . "/")."{$recommended['category_url_name']}/{$recommended['url_name']}'" ?>>
                            <div class="page-section-card-header">
                                <img src="<?php echo $recommended['logo_url']; ?>" />
                            </div>
                            <div class="page-section-card-content">
                                <div class="page-section-card-content-title">
                                    <?php echo $recommended['name']; ?>
                                    <?php if(!is_null($recommended['description'])){ ?>
                                    <div class="page-section-card-content-subtitle">
                                        <?php echo $recommended['description']; ?><nobr style="visibility: hidden;">.
                                        </nobr>
                                    </div>
                                    <?php } ?>
                                </div>
                                <!-- Changes start here -->
                                <div class="page-section-card-content-info">
                                    <?php if(!is_null($recommended['processing_time'])){ ?>
                                    <span class="page-section-card-content-info-time text-blue">
                                        <svg width="20" height="20" style="vertical-align: middle; margin-right: 5px;"
                                            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10 4.28571C6.84409 4.28571 4.28571 6.84409 4.28571 10C4.28571 13.1559 6.84409 15.7143 10 15.7143C13.1559 15.7143 15.7143 13.1559 15.7143 10C15.7143 6.84409 13.1559 4.28571 10 4.28571ZM2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10Z"
                                                fill="#548AF7" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M9.77425 6.5714H10.9171V11.3714H7.71429V10.2285H9.77425V6.5714Z"
                                                fill="#548AF7" />
                                        </svg>
                                        <?php echo $recommended['processing_time']; ?>&nbsp;&nbsp;
                                    </span>
                                    <?php } ?>
                                    <?php if(!is_null($recommended['processing_fee'])){ ?>
                                    <span class="page-section-card-content-info-fee text-green">
                                        <svg width="20" height="20" style="vertical-align: middle; margin-right: 5px;"
                                            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10 4.28571C6.84409 4.28571 4.28571 6.84409 4.28571 10C4.28571 13.1559 6.84409 15.7143 10 15.7143C13.1559 15.7143 15.7143 13.1559 15.7143 10C15.7143 6.84409 13.1559 4.28571 10 4.28571ZM2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10Z"
                                                fill="#1DBF73" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.4999 6V6.6018C10.8882 6.66766 11.2585 6.81061 11.5759 7.02596C12.0287 7.33317 12.3575 7.77861 12.4647 8.29914L11.4852 8.50077C11.4382 8.2724 11.2852 8.03716 11.0144 7.85347C10.7427 7.66908 10.3833 7.55972 10.0003 7.55996C9.5321 7.55996 9.13761 7.68426 8.87511 7.86891C8.62066 8.0479 8.49994 8.27111 8.49994 8.51995C8.49994 8.71624 8.61274 8.91587 8.93689 9.11636C9.24183 9.30497 9.66232 9.44386 10.1003 9.52995C10.6207 9.62445 11.1879 9.78313 11.6384 10.0625C12.1048 10.3517 12.4999 10.8102 12.4999 11.4799C12.4999 12.1111 12.1707 12.6179 11.7001 12.9489C11.3615 13.187 10.9461 13.3396 10.4999 13.4043V14H9.49994V13.3978C9.04767 13.3208 8.62043 13.1393 8.27338 12.8617C7.7974 12.4809 7.49994 11.9375 7.49994 11.3399H8.49994C8.49994 11.591 8.62391 11.8615 8.89807 12.0808C9.17513 12.3025 9.57045 12.4399 9.99994 12.4399C10.4681 12.4399 10.8623 12.3156 11.1248 12.1309C11.3792 11.952 11.4999 11.7287 11.4999 11.4799C11.4999 11.2696 11.395 11.0882 11.1115 10.9124C10.835 10.741 10.4438 10.6153 10.0053 10.5296C9.9754 10.5242 9.94528 10.5186 9.915 10.5127C9.91379 10.5124 9.91258 10.5122 9.91136 10.512C9.40596 10.413 8.85603 10.2422 8.41087 9.96684C7.93608 9.67318 7.49994 9.20366 7.49994 8.51995C7.49994 7.88879 7.82922 7.38201 8.29976 7.051C8.63835 6.81283 9.05375 6.66021 9.49994 6.59553V6H10.4999Z"
                                                fill="#1DBF73" />
                                        </svg>
                                        <!-- Until here -->
                                        <?php
                                                            $processing_fee_arr = json_decode($recommended['processing_fee'],true);
                                                            if(json_last_error() == JSON_ERROR_NONE){
                                                                $curcode = strtolower($_SESSION['currencycode']);
                                                                echo (!empty($processing_fee_arr[$curcode]) ?  $processing_fee_arr[$curcode] : $processing_fee_arr["Default"]);
                                                            }
                                                            else{
                                                                echo "";
                                                            }
                                                        ?>
                                    </span>
                                    <?php } ?>
                                    <!-- <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-xs-6 page-section-card-content-info-time text-blue">
                                                    <div class="col-xl-2 col-lg-2 col-md-2 col-sm-1 col-xs-2"><i class="fa fa-clock-o"></i></div>
                                                    <span class="col-10"><?php // echo $recommended['processing_time']; ?></span>
                                                </div>
                                                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-xs-6 page-section-card-content-info-fee text-green">
                                                    <div class="col-xl-2 col-lg-2 col-md-2 col-sm-1 col-xs-2"><i class="fa fa-usd"></i></div>
                                                    <span class="col-10">
                                                        <?php /*
                                                            $processing_fee_arr = json_decode($recommended['processing_fee'],true);
                                                            if(json_last_error() == JSON_ERROR_NONE){
                                                                $curcode = strtolower($_SESSION['currencycode']);
                                                                echo (!empty($processing_fee_arr[$curcode]) ?  $processing_fee_arr[$curcode] : $processing_fee_arr["Default"]);
                                                            }
                                                            else{
                                                                echo "";
                                                            }
                                                        */ ?>
                                                    </span>
                                                </div> -->
                                </div>
                            </div>
                            <div class="page-section-card-footer">
                                <span>User guide</span>
                            </div>
                        </a>
                    </div>
                </div>
                <?php 
                        }
                    ?>
            </div>
        </div>
    </div>
    <?php } ?>
    <!-- End of "Recommended" category -->

    <!-- Beginning of all category -->
    <?php foreach ($paymentCategory as $k => $v) { 
        if($v["display"] && count($paymentMethods[$v['id']]) > 0){
            $has_category = true; ?>
    <div class="page-section col-12">
        <div class="payment-section-anchor" id="<?php echo $v['url_name'] ?>"></div>
        <div class="page-section-title col-12">
            <!-- <i class="fa <?php // echo $v["icons"];?>" aria-hidden="true"></i> -->
            <img alt="logo" src="
                        <?php
                            echo $publishedUrl . '/images/'.$v["url_name"].'.svg';
                        ?>
                    " />
            <?php echo $v['name']; ?>
        </div>
        <div class="col-12">
            <div class="row">
                <?php 
                            if($paymentMethods[$v['id']]){
                                foreach($paymentMethods[$v['id']] as $method){ ?>
                <div class="col-xxl-3 col-xl-3 col-lg-4 col-md-4 col-sm-6 col-xs-12">
                    <div class="page-section-card">
                        <a
                            <?php echo " href='/payment-guide/pg/".($_SESSION['language'] == "en-US" ? '' : $_SESSION['language'] . "/")."{$v['url_name']}/{$method['url_name']}'" ?>>
                            <div class="page-section-card-header">
                                <img src="<?php echo $method['logo_url']; ?>" />
                            </div>
                            <div class="page-section-card-content">
                                <div class="page-section-card-content-title">
                                    <?php echo $method['name']; ?>
                                    <?php if(!is_null($method['description'])){ ?>
                                    <div class="page-section-card-content-subtitle">
                                        <?php echo $method['description']; ?><nobr style="visibility: hidden;">.</nobr>
                                    </div>
                                    <?php } ?>
                                </div>
                                <!-- Changes start here -->
                                <div class="page-section-card-content-info">
                                    <?php if(!is_null($method['processing_time'])){ ?>
                                    <span class="page-section-card-content-info-time text-blue">
                                        <svg width="20" height="20" style="vertical-align: middle; margin-right: 5px;"
                                            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10 4.28571C6.84409 4.28571 4.28571 6.84409 4.28571 10C4.28571 13.1559 6.84409 15.7143 10 15.7143C13.1559 15.7143 15.7143 13.1559 15.7143 10C15.7143 6.84409 13.1559 4.28571 10 4.28571ZM2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10Z"
                                                fill="#548AF7" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M9.77425 6.5714H10.9171V11.3714H7.71429V10.2285H9.77425V6.5714Z"
                                                fill="#548AF7" />
                                        </svg>
                                        <?php echo $method['processing_time']; ?>&nbsp;&nbsp;</span>
                                    <?php } ?>
                                    <?php if(!is_null($method['processing_fee'])){ ?>
                                    <span class="page-section-card-content-info-fee text-green">
                                        <svg width="20" height="20" style="vertical-align: middle; margin-right: 5px;"
                                            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10 4.28571C6.84409 4.28571 4.28571 6.84409 4.28571 10C4.28571 13.1559 6.84409 15.7143 10 15.7143C13.1559 15.7143 15.7143 13.1559 15.7143 10C15.7143 6.84409 13.1559 4.28571 10 4.28571ZM2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10Z"
                                                fill="#1DBF73" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.4999 6V6.6018C10.8882 6.66766 11.2585 6.81061 11.5759 7.02596C12.0287 7.33317 12.3575 7.77861 12.4647 8.29914L11.4852 8.50077C11.4382 8.2724 11.2852 8.03716 11.0144 7.85347C10.7427 7.66908 10.3833 7.55972 10.0003 7.55996C9.5321 7.55996 9.13761 7.68426 8.87511 7.86891C8.62066 8.0479 8.49994 8.27111 8.49994 8.51995C8.49994 8.71624 8.61274 8.91587 8.93689 9.11636C9.24183 9.30497 9.66232 9.44386 10.1003 9.52995C10.6207 9.62445 11.1879 9.78313 11.6384 10.0625C12.1048 10.3517 12.4999 10.8102 12.4999 11.4799C12.4999 12.1111 12.1707 12.6179 11.7001 12.9489C11.3615 13.187 10.9461 13.3396 10.4999 13.4043V14H9.49994V13.3978C9.04767 13.3208 8.62043 13.1393 8.27338 12.8617C7.7974 12.4809 7.49994 11.9375 7.49994 11.3399H8.49994C8.49994 11.591 8.62391 11.8615 8.89807 12.0808C9.17513 12.3025 9.57045 12.4399 9.99994 12.4399C10.4681 12.4399 10.8623 12.3156 11.1248 12.1309C11.3792 11.952 11.4999 11.7287 11.4999 11.4799C11.4999 11.2696 11.395 11.0882 11.1115 10.9124C10.835 10.741 10.4438 10.6153 10.0053 10.5296C9.9754 10.5242 9.94528 10.5186 9.915 10.5127C9.91379 10.5124 9.91258 10.5122 9.91136 10.512C9.40596 10.413 8.85603 10.2422 8.41087 9.96684C7.93608 9.67318 7.49994 9.20366 7.49994 8.51995C7.49994 7.88879 7.82922 7.38201 8.29976 7.051C8.63835 6.81283 9.05375 6.66021 9.49994 6.59553V6H10.4999Z"
                                                fill="#1DBF73" />
                                        </svg>
                                        <!-- Until here -->
                                        <?php
                                                                    $processing_fee_arr = json_decode($method['processing_fee'],true);
                                                                    if(json_last_error() == JSON_ERROR_NONE){
                                                                        $curcode = strtolower($_SESSION['currencycode']);
                                                                        echo (!empty($processing_fee_arr[$curcode]) ?  $processing_fee_arr[$curcode] : $processing_fee_arr["Default"]);
                                                                    }
                                                                    else{
                                                                        echo "";
                                                                    }
                                                                ?>
                                    </span>
                                    <?php } ?>
                                    <!-- <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-xs-6 page-section-card-content-info-time text-blue">
                                                            <div class="col-xl-2 col-lg-2 col-md-2 col-sm-1 col-xs-2"><i class="fa fa-clock-o"></i></div>
                                                            <span class="col-10"><?php // echo $method['processing_time']; ?></span>
                                                        </div>
                                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-xs-6 page-section-card-content-info-fee text-green">
                                                            <div class="col-xl-2 col-lg-2 col-md-2 col-sm-1 col-xs-2"><i class="fa fa-usd"></i></div>
                                                            <span class="col-10">
                                                            <?php /*
                                                                    $processing_fee_arr = json_decode($method['processing_fee'],true);
                                                                    if(json_last_error() == JSON_ERROR_NONE){
                                                                        $curcode = strtolower($_SESSION['currencycode']);
                                                                        echo (!empty($processing_fee_arr[$curcode]) ?  $processing_fee_arr[$curcode] : $processing_fee_arr["Default"]);
                                                                    }
                                                                    else{
                                                                        echo "";
                                                                    }
                                                                */ ?>
                                                            </span>
                                                        </div> -->
                                </div>
                            </div>
                            <div class="page-section-card-footer">
                                <span>User guide</span>
                            </div>
                        </a>
                    </div>
                </div>
                <?php 
                                }
                            } else { 
                            ?>
                <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="page-section-card page-section-card-notfound">
                        <div class="page-section-card-content">
                            <div class="page-section-card-content-title">
                                <?php echo Yii::t('app', 'method-no-content'); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php 
                            } 
                        ?>
            </div>
        </div>
    </div>
    <?php } else { ?>
    <!-- Uncommand this section (line 154 until line 170) to display "No payment methods" message on front end view -->

    <!-- <div class="page-section col-12" id="<?php //echo $v['url_name'] ?>">
                <div class="page-section-title col-12">
                    <i class="fa <?php //echo $v["icons"];?>" aria-hidden="true"></i>
                    <?php //echo $v['name']; ?>
                </div>
                <div class="col-12">
                    <div class="row">
                        <div class="page-section-card page-section-card-notfound col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="page-section-card-content">
                                <div class="page-section-card-content-title">
                                    <?php //echo Yii::t('app', 'method-no-content'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
    <?php }
    } ?>

    <?php
     if(!$has_category) {?>
    <div class="col-12">
        <div class="row">
            <div class="page-section-card page-section-card-notfound col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="page-section-card-content">
                    <div class="page-section-card-content-title">
                        <?php echo Yii::t('app', 'method-no-content'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php } ?>

    <?php echo $this->context->renderPartial('/layouts/_footer.php', ['class'=>'atBottom hidden-lg-down']); ?>
</section>

<?php
    $this->beginBlock('outerFooter');
        echo $this->context->renderPartial('/layouts/_footer.php', ['class'=>'hidden-lg-up']); 
    $this->endBlock();
?>