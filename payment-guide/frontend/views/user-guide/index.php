<?php

use yii\helpers\Html;

use frontend\controllers\UserGuideController;
use frontend\components\LayoutManager;

/* @var $this yii\web\View */
/* @var $searchModel common\models\UserGuideSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */


Yii::$app->params["meta-description"] = Yii::t('app', 'home-description');
Yii::$app->params["meta-keywords"] = Yii::t('app', 'global-meta');
?>

<section class="main-page has-sticky-top col-xxl-8 col-xl-8 col-lg-8 col-md-8 col-sm-12 col-xs-12" style="min-height: 100vh !important;">
    <div class="page-header col-12 hidden-lg-down" <?php echo " onclick='window.location=\"/payment-guide/".($_SESSION['language'] == "en-US" ? '' : $_SESSION['language'])."\";'" ?>>
        <i class="fa fa-arrow-left" aria-hidden="true"></i>
        Back
    </div>
    <div class="page-guide-section col-12" <?php if(!$model){echo " style='min-height: 60vh !important;'";} ?>>
        <?php
            if ($model) {
                $title = $model->name;
            } else {
                $title = "";
            }
            echo "
                <div class='page-guide-section-title col-12'>
                    {$title}
                </div>
            ";
            $this->title = $title . " - " . Yii::$app->params['site_title'];
        ?>
        
        <div class="page-guide-section-step col-12">
            <?php
                if ($model) {
                    echo $model->content;
                } else { ?>
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="page-section-card page-section-card-notfound">
                            <div class="page-section-card-content">
                                <div class="page-section-card-content-title">
                                    <?php echo Yii::t('app', 'method-no-content'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                }
            ?>
        </div>
    </div>
    <?php echo $this->context->renderPartial('/layouts/_footer.php', ['class'=>'atBottom hidden-lg-down']); ?>
</section>

<?php
    if($model){
        $model_id = isset($model->id) ? $model->id : 0;
        $list = UserGuideController::actionList($model_id);
        $this->beginBlock('rightSideBar');
            echo $this->context->renderPartial('/layouts/_sidebar.php', ['types'=>'right', 'list'=>$list]); 
        $this->endBlock();
    }
?>

<?php
    $this->beginBlock('outerFooter');
        echo $this->context->renderPartial('/layouts/_footer.php', ['class'=>'hidden-lg-up']); 
    $this->endBlock();
?>