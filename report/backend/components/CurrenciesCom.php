<?php

namespace backend\components;

use Yii;

class CurrenciesCom {

    public $currencies = null;
    public $currency_list = [];

    public function advanceCurrencyConversion($number, $from_cur_type = '', $to_cur_type = '', $order_date = '') {
        $from_cur_type = $from_cur_type == '' ? 'USD' : $from_cur_type;
        $to_cur_type = $to_cur_type == '' ? 'USD' : $to_cur_type;
        $rate = $this->advanceCurrencyConversionRate($from_cur_type, $to_cur_type, $order_date);
        return $number * $rate;
    }

    public function advanceCurrencyConversionRate($from_cur_type = '', $to_cur_type = '', $order_date = '') {
        $from_cur_type = $from_cur_type == '' ? 'USD' : $from_cur_type;
        $to_cur_type = $to_cur_type == '' ? 'USD' : $to_cur_type;

        if ($from_cur_type == $to_cur_type) {
            return number_format(1, 8, '.', '');
        }

        $this->getSpotValue($from_cur_type, $order_date);
        $this->getSpotValue($to_cur_type, $order_date);

        $rate = isset($this->currencies[$from_cur_type]['spot_value']) ? bcdiv($this->currencies[$to_cur_type]['spot_value'], $this->currencies[$from_cur_type]['spot_value'], 8) : 1;

        return number_format($rate, 8, '.', '');
    }

    public function getSpotValue($code, $order_date) {
        if ($code != 'USD') {
            $spot_value_select_sql = "SELECT code, spot_value FROM currencies_history WHERE code ='" . $code . "' AND date_from <='" . $order_date . "'  ORDER BY version DESC LIMIT 1";

            $spot_value = Yii::$app->get('db_offgamers')->createCommand($spot_value_select_sql)->queryAll();
            
            if ($spot_value) {
                foreach ($spot_value as $spot_value_row) {
                    $this->currencies[$spot_value_row['code']] = array(
                        'spot_value' => $spot_value_row['spot_value'],
                    );
                }
            }
            else {
                $spot_value_reselect_sql = "SELECT code, spot_value FROM currencies_history WHERE code ='" . $code . "' ORDER BY version ASC LIMIT 1";
                $spot_value_reselect = Yii::$app->get('db_offgamers')->createCommand($spot_value_reselect_sql)->queryAll();
                foreach ($spot_value_reselect as $spot_value_reselect_row) {
                    $this->currencies[$spot_value_reselect_row['code']] = array(
                        'spot_value' => $spot_value_reselect_row['spot_value'],
                    );
                }
            }
        }

        $this->currencies['USD'] = array(
            'spot_value' => 1.00000000,
        );
    }

    public function displayFormat($number, $code, $symbol = true) {
        $currency_obj = new \backend\models\Currencies();
        $currencies_row = $currency_obj->getCurrencyInfo($code);
        if ($symbol) {
            $format_string = html_entity_decode($currencies_row['symbol_left'] . number_format($this->tep_round($number, $currencies_row['decimal_places']), $currencies_row['decimal_places'], $currencies_row['decimal_point'], $currencies_row['thousands_point']) . $currencies_row['symbol_right']);
        } else {
            $format_string = number_format($this->tep_round($number, $currencies_row['decimal_places']), $currencies_row['decimal_places'], $currencies_row['decimal_point'], '');
        }
        return $format_string;
    }

    private function tep_round($number, $precision) {
        return number_format($number, $precision, '.', '');
    }

}
