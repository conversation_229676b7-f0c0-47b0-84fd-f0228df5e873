<?php

namespace backend\components;

class GeneralCom {

    public function hasAnyEmptyValueInArray($array, $return_array = []) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $return_array = $this->hasAnyEmptyValueInArray($value, $return_array);
            } else {
                if ($value === '' || is_null($value)) {
                    $return_array[$key] = $value;
                }
            }
        }
        return $return_array;
    }

}
