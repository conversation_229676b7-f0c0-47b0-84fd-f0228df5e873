<?php

namespace backend\components;

use Yii;

class GpReportCom {
    
    public function getEntityOption() {
        $entity_obj = new \backend\models\DimGpEntity();
        $entities_row = $entity_obj->getEntityInfo();
        
        foreach ($entities_row as $entity) {
            $entities[$entity['gpe_id']] = $entity['id']." - ".$entity['company_name'];
        }
        
        return $entities;
    }
    
}