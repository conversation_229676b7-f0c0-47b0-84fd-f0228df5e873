<?php

namespace backend\components;
use backend\models\DimPinClient;
use backend\models\DimOgcCrew;
use backend\models\DimPinProduct;
use backend\models\DimPinReseller;
use backend\models\DimDate;
use backend\models\DimPinMgcMerchant;

class MgcReportCom {
    private $id_mapping_array = array();
    
    public function getDimOgcCrewOCID($id, $params) {
       
        if (isset($this->id_mapping_array['DimOgcCrew'][$id])) {
           $return_id = $this->id_mapping_array['DimOgcCrew'][$id];
        } else {
            $obj=new DimOgcCrew();
            $return_id = $obj->checkRecordAndUpdateVersion($id,'oc_id',$params);
            $this->id_mapping_array['DimOgcCrew'][$id] = $return_id;
        }
        
        return $return_id;
    }
    
    public function getDimPinClientPCID($id, $params) {
        if (isset($this->id_mapping_array['DimPinClient'][$id])) {
           $return_id = $this->id_mapping_array['DimPinClient'][$id];
        } else {
            $obj=new DimPinClient();
            $return_id = $obj->checkRecordAndUpdateVersion($id,'pc_id', $params);
            $this->id_mapping_array['DimPinClient'][$id] = $return_id;
        }
        
        return $return_id;
    }
    
    public function getDimPinProductPPID($id, $params) {
        if (isset($this->id_mapping_array['DimPinProduct'][$id])) {
           $return_id = $this->id_mapping_array['DimPinProduct'][$id];
        } else {
            $obj=new DimPinProduct();
            $return_id = $obj->checkRecordAndUpdateVersion($id,'pp_id', $params);
            $this->id_mapping_array['DimPinProduct'][$id] = $return_id;
        }
        
        return $return_id;
    }
    
    public function getDimPinResellerPRID($id, $params) {
        if (isset($this->id_mapping_array['DimPinReseller'][$id])) {
           $return_id = $this->id_mapping_array['DimPinReseller'][$id];
        } else {
            $obj=new DimPinReseller();
            $return_id = $obj->checkRecordAndUpdateVersion($id,'pr_id', $params);
            $this->id_mapping_array['DimPinReseller'][$id] = $return_id;
        }
        
        return $return_id;
    }
    
    public function getDimDateDATEID($id, $params) {
        if (isset($this->id_mapping_array['DimDate'][$id])) {
           $return_id = $this->id_mapping_array['DimDate'][$id];
        } else {
            $obj=new DimDate();
            $return_id = $obj->checkDateAndInsertIfNotExists($id, $params);
            $this->id_mapping_array['DimDate'][$id] = $return_id;
        }
        
        return $return_id;
    }
    
    public function getDimPinMgcMerchantPMMID($id, $params) {
        if (isset($this->id_mapping_array['DimPinMgcMerchant'][$id])) {
           $return_id = $this->id_mapping_array['DimPinMgcMerchant'][$id];
        } else {
            $obj=new DimPinMgcMerchant();
            $return_id = $obj->checkRecordAndUpdateVersion($id,'pmm_id', $params);
            $this->id_mapping_array['DimPinMgcMerchant'][$id] = $return_id;
        }
        
        return $return_id;
    }
}


