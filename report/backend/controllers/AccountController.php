<?php

namespace backend\controllers;

use Yii;
use backend\models\User;
use yii\web\NotFoundHttpException;

//use mdm\admin\components\AccessChecking;

/**
 * UserController implements the CRUD actions for User model.
 */
class AccountController extends \backend\components\Controller {

    public function init() {
        parent::init();
        $this->defaultAction = 'set-password';
    }

    /**
     * Lists all User models.
     * @return mixed
     */
    public function actionIndex() {
        
    }

    public function actionSetPassword() {
        //remove access checking, allow all users to change their own password
//        if (AccessChecking::AccessChecking("/account.set-password") === true) {
        $user = User::findone(Yii::$app->user->id);
        $user->scenario = "setPassword";

        if ($user->load($_POST) && $user->validate()) {
            $user->password_hash = Yii::$app->getSecurity()->generatePasswordHash($user->password);
            $user->save(false);
            Yii::$app->session->setFlash('BackendSuccess', Yii::t('alert', 'Password Settings Saved!'));
            return $this->refresh();
//            return $this->redirect(['set-password']);
        }

        return $this->render('setPassword', [
                    'model' => $user,
        ]);
//        }
//        AccessChecking::noPermissionAction();
    }

    /**
     * Finds the User model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return User the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id) {
        if ($id !== null && ($model = User::find($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException(Yii::t('alert', 'The requested page does not exist.'));
        }
    }

}
