<?php

namespace backend\controllers;

use Yii;
use yii\filters\AccessControl;

class GpReportController extends \yii\web\Controller {

    public function behaviors() {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => false,
                        'roles' => ['?'],
                    ],
                ],
            ],
        ];
    }

    public function actionIndex() {
        return $this->render('index');
    }
    
    private function exportGPToCSV($model, $dataProvider) {
        if (!get_cfg_var('safe_mode')) {
            set_time_limit(1500);
        }
        
        $order_results = $dataProvider->query->asArray()->all();
        $cur_obj = new \backend\components\CurrenciesCom();

        $sequence = [
            'row_numbering' => '#',
            'delivery_datetime' => 'Delivery Date',
            'order_id' => 'Order ID',
            'product_name' => 'Product Name',
            'order_currency' => 'Order Currency',
            'payment_method' => 'Payment Method',
            'unit_cost_in_purchase_currency' => 'PO Cost In Original Price',
            'company_name' => 'Stock Purchase',
            'delivered_quantity' => 'Quantity Sold',
            'purchase_conversion_rate_in_order_currency' => 'Conversion Rate',
            'unit_cost_in_order_currency' => 'PO Cost Follow CO Currency',
            'total_cost_in_order_currency' => 'Total Cost In Order Currency',
            'total_order_in_order_currency' => 'Total Order In Order Currency',
            'total_sc_in_order_currency' => 'Total Store Credit In Order Currency',
            'total_payment_in_order_currency' => 'Total Payment In Order Currency',
            'margin_in_order_currency' => 'Margin In CO Currency',
            'margin_percentage_in_order_currency' => 'Margin In Percentage',
            'order_conversion_rate_in_usd' => 'Conversion Rate To Usd',
            'total_purchase_in_usd' => 'PO Cost In Usd',
            'total_order_in_usd' => 'CO Price In Usd',
            'total_sc_in_usd' => 'Total Store Credit In Usd',
            'total_payment_in_usd' => 'Total Payment In Usd',
            'margin_in_usd' => 'Margin In Usd',
            'margin_percentage_in_usd' => 'Margin Percentage In Usd',
        ];
        
        $csv_body = [];
        
        $attr_arr = $dataProvider->sort->attributes;
        
        # header
        foreach ($sequence as $att_key => $custom_label) {
            if (isset($attr_arr[$att_key]['label'])) {
                $head[] = $attr_arr[$att_key]['label'];
            } else {
                $head[] = $custom_label;
            }
        }
        
        $csv_body[] = '"' . implode('","', $head) . '"' . "\n";

        $numbering = 0;
        foreach ($order_results as $data) {
            $row_arr = [];

            $numbering++;
            $row_arr[] = $numbering;
            $row_arr[] = $data['gpOrder']['delivery_datetime'];
            $row_arr[] = $data['gpOrder']['order_id'];
            $row_arr[] = $data['gpOrder']['product_name'];
            $row_arr[] = $data['gpOrder']['order_currency'];
            $row_arr[] = $data['gpOrder']['payment_method'];
            $row_arr[] = $cur_obj->displayFormat($data['unit_cost_in_purchase_currency'], $data['purchase_currency']);
            $row_arr[] = $data['gpEntity']['id'] . " - " . $data['gpEntity']['company_name'];
            $row_arr[] = $model->getSumDeliveredQuantity($data['gp_order_id'], $data['id']);
            $row_arr[] = isset($data['purchase_conversion_rate_in_order_currency']) ? $data['purchase_conversion_rate_in_order_currency'] : '';
            $row_arr[] = $cur_obj->displayFormat($data['unit_cost_in_order_currency'], $data['gpOrder']['order_currency']);
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_cost_in_order_currency']), $data['gpOrder']['order_currency']);
            $row_arr[] = $cur_obj->displayFormat($model->getPoTotal($data['gp_order_id'], $data['id'], 'order_currency'), $data['gpOrder']['order_currency']);
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_sc_in_order_currency']), $data['gpOrder']['order_currency']);
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_payment_in_order_currency']), $data['gpOrder']['order_currency']);
            $row_arr[] = $cur_obj->displayFormat($model->getPoMargin($data['gp_order_id'], $data['id'], 'order_currency'), $data['gpOrder']['order_currency']);
            $row_arr[] = number_format($model->getPoMarginPercentage($data['gp_order_id'], $data['id'], 'order_currency'), 2) . '%';
            $row_arr[] = $data['gpOrder']['order_conversion_rate_in_usd'];
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_cost_in_usd']), 'USD');
            $row_arr[] = $cur_obj->displayFormat($model->getPoTotal($data['gp_order_id'], $data['id'], 'order_currency_usd'), 'USD');
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_sc_in_usd']), 'USD');
            $row_arr[] = $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($data['gp_order_id'], $data['id']) * $data['total_payment_in_usd']), 'USD');
            $row_arr[] = $cur_obj->displayFormat($model->getPoMargin($data['gp_order_id'], $data['id'], 'order_currency_usd'), 'USD');
            $row_arr[] = number_format($model->getPoMarginPercentage($data['gp_order_id'], $data['id'], 'order_currency_usd'), 2) . '%';
            
            $csv_body[] = '"' . implode('","', $row_arr) . '"' . "\n";
        }

        // Download
        header('Content-Type: text/x-csv');
        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Content-Disposition: attachment; filename="GP_report_' . date('YmdHis') . '.csv"');
        header('Pragma: no-cache');
        echo implode('', $csv_body);
        exit;
    }
    
    public function actionReport() {
        set_time_limit(0);
        $model = new \backend\models\GpCost();
        $page = Yii::$app->request->get('pageSize');
        $pageSize = !empty($page) ? $page : 20;
        $params = Yii::$app->request->queryParams;
        $dataProvider = $model->getGpDataProvider($params);
        
        if (isset($params['submit']) && $params['submit'] == 'export') {
            $this->exportGPToCSV($model, $dataProvider);
        }
        
        $dataProvider->pagination->pageSize = $pageSize;
        return $this->render('gp_report', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
                    'pageSize' => $pageSize,
                    'delivery_start_date' => isset($params['GpCost']['delivery_start_date']) ? $params['GpCost']['delivery_start_date'] : date("Y-m-d"),
                    'delivery_end_date' => isset($params['GpCost']['delivery_end_date']) ? $params['GpCost']['delivery_end_date'] : date("Y-m-d"),
        ]);
    }
    
    public function actionReleased() {
        set_time_limit(0);
        $model = new \backend\models\GpCostReleased();
        $page = Yii::$app->request->get('pageSize');
        $pageSize = !empty($page) ? $page : 20;
        $params = Yii::$app->request->queryParams;
        $dataProvider = $model->getGpDataProvider($params);
        $dataProvider->pagination->pageSize = $pageSize;
        return $this->render('gp_released', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
                    'pageSize' => $pageSize,
                    'released_start_date' => isset($params['GpCostReleased']['released_start_date']) ? $params['GpCostReleased']['released_start_date'] : date("Y-m-d"),
                    'released_end_date' => isset($params['GpCostReleased']['released_end_date']) ? $params['GpCostReleased']['released_end_date'] : date("Y-m-d"),
        ]);
    }

}