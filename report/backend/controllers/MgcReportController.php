<?php

namespace backend\controllers;
use Yii;
use backend\models\FactPinDistribution;
use yii\filters\AccessControl;


class MgcReportController extends \yii\web\Controller {
    
   public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                       
                        'allow' => true, 
                        'roles' => ['@'], 
                    ],
                    [
                        'allow' => false, 
                        'roles'=>['?'],
                    ],
                ],
            ],
        ];
    }

    public function actionIndex() {
        return $this->render('index');
    }

    public function actionRedeemReport() {
        $model=new FactPinDistribution();
        $pageSize = Yii::$app->request->get('pageSize');
        $pageSize = !empty($pageSize) ? $pageSize : 20;
        $params=\Yii::$app->request->queryParams;
        $dataProvider=$model->getPinDistributionDataProvider($params);
        $dataProvider->pagination->pageSize = $pageSize;
         return $this->render('redeem-report', [
                    'model' => $model,
                    'dataProvider' => $dataProvider,
                    'pageSize' => $pageSize,
                    'request_start_date' => isset($params['FactPinDistribution']['PinRequest_start_date'])?$params['FactPinDistribution']['PinRequest_start_date']:'',
                    'request_end_date' => isset($params['FactPinDistribution']['PinRequest_end_date'])?$params['FactPinDistribution']['PinRequest_end_date']:'',
                    'redeem_start_date' => isset($params['FactPinDistribution']['PinRedeem_start_date'])?$params['FactPinDistribution']['PinRedeem_start_date']:'',
                    'redeem_end_date' => isset($params['FactPinDistribution']['PinRedeem_end_date'])?$params['FactPinDistribution']['PinRedeem_end_date']:'',
        ]);
    }
 

}
