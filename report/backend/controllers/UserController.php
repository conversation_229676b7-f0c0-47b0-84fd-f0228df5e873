<?php

namespace backend\controllers;

use Yii;
use backend\models\User;
use backend\models\UserSearch;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;
use yii\filters\AccessControl;


//use yii\helpers\Url;
//use yii\web\Controller;

/**
 * UserController implements the CRUD actions for User model.
 */
class UserController extends \backend\components\Controller {
    
    
    
     public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                       
                        'allow' => true, 
                        'roles' => ['@'], 
                    ],
                    [
                        'allow' => false, 
                        'roles'=>['?'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all User models.
     * @return mixed
     */
    public function actionIndex() {
        //if (AccessChecking::AccessChecking("/user.index.list") === true) {
            $searchModel = new UserSearch;
            $dataProvider = $searchModel->search($_GET);
            $dataProvider->pagination->pageSize = 20;
            
            $userModel = new User();

            return $this->render('index', [
                        'dataProvider' => $dataProvider,
                        'searchModel' => $searchModel,
                        'userModel' => $userModel,
            ]);
        //}

        //AccessChecking::noPermissionAction();
    }

    /**
     * Displays a single User model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id) {
        //if (AccessChecking::AccessChecking("/user.view") === true) {
            return $this->render('view', [
                        'model' => $this->findModel($id),
            ]);
       // }
       // AccessChecking::noPermissionAction();
    }

    /**
     * Creates a new User model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate() {
        //if (AccessChecking::AccessChecking("/user.create") === true) {
            $model = new User(['scenario' => 'create']);

            if ($model->load($_POST)) {
                $user = $model->signup();
                if ($user) {
                    Yii::$app->session->setFlash("success.", Yii::t('alert','Create Success'));
                    return $this->redirect(['view', 'id' => $user->id]);
                } else {
                    Yii::$app->session->setFlash("danger.", Yii::t('alert','Failed to create'));
                }
            }

            return $this->render('create', [
                        'model' => $model,
            ]);
        //}

        //AccessChecking::noPermissionAction();
    }

    /**
     * Updates an existing User model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id) {
        //if (AccessChecking::AccessChecking("/user.update") === true) {
            
            $userGroup = User::getUserGroup(Yii::$app->user->id);
            $updateUserGroup = User::getUserGroup($id);
            
            switch($userGroup){
                case 'superadmin';
                    //can update all
                    break;
                case 'admin';
                    if($updateUserGroup == 'superadmin' || ($updateUserGroup == 'admin' && Yii::$app->user->id != $id)){
                        //AccessChecking::noPermissionAction();
                    }
                    break;
                default;
                    if(Yii::$app->user->id != $id){
                       // AccessChecking::noPermissionAction();
                    }
            }
            
            $model = $this->findModel($id);

            if ($model->load($_POST)) {
                $status = $model->updateProfile();
                if ($status) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
            return $this->render('update', [
                        'model' => $model,
            ]);
        //}

        //AccessChecking::noPermissionAction();
    }

    /**
     * Deletes an existing User model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id) {
       // if (AccessChecking::AccessChecking("/user.delete") === true) {
            
            $userGroup = User::getUserGroup(Yii::$app->user->id);
            $updateUserGroup = User::getUserGroup($id);
            
            switch($userGroup){
                case 'superadmin';
                    //can update all
                    break;
                case 'admin';
                    if($updateUserGroup == 'superadmin' || ($updateUserGroup == 'admin' && Yii::$app->user->id != $id)){
                       // AccessChecking::noPermissionAction();
                    }
                    break;
                default;
                    if(Yii::$app->user->id != $id){
                        //AccessChecking::noPermissionAction();
                    }
            }
            
            $userData = $this->findModel($id);
            $userData->deleteUser();
            return $this->redirect(['index']);
       // }

        //AccessChecking::noPermissionAction();
    }

    /**
     * Finds the User model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return User the loaded model
     * @throws HttpException if the model cannot be found
     */
    protected function findModel($id) {
        if (($model = User::findone($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException(Yii::t('alert','The requested page does not exist.'));
        }
    }

    public function actionResetPassword($id) {
        $status = false;
        $msg = Yii::t('email','You do not have permission');
        //if (AccessChecking::AccessChecking("/user.reset-password") === true) {
            $user = $this->findModel($id);
            /* @var \mdm\admin\models\User $user */
            if ($user) {
                if ($user->checkSuperAdmin()) {
                    $msg = Yii::t('email','You are not allow to reset superadmin password!');
                } else {
                    $user->password_hash = Yii::$app->getSecurity()->generatePasswordHash($user->username);
                    $user->save(false);
                    $status = true;
                    $msg = Yii::t('email',"Successfully reset password to:") . $user->username;
                }
            } else {
                $msg = Yii::t('email','Invalid user');
            }
        //}

        echo json_encode(array(
            'result' => $status,
            'msg' => $msg,
        ));
    }
}
