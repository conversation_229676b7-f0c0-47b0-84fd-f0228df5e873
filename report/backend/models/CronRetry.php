<?php

namespace backend\models;

use Yii;

/**
 * This is the model class for table "cron_retry".
 *
 * @property integer $id
 * @property string $action
 * @property string $task
 * @property integer $key_id
 * @property string $key_str
 * @property string $val_1
 * @property string $val_2
 * @property string $txt
 */
class CronRetry extends \common\models\CronRetry
{
    public function getDataByTask($task, $limit = 10, $retry_limit = 3) {
        $return_array = [];
        
        $query = (new \yii\db\Query())
                ->select(['id', 'json_str'])
                ->from(static::tableName())
                ->where(['task' => $task])
                ->andWhere(['<', 'val_2', $retry_limit])
                ->limit($limit)
                ->orderBy('val_2 ASC');
        
        foreach ($query->each() as $data) {
            $return_array[$data['id']] = json_decode($data['json_str'], true);
        }

        return $return_array;
    }
    
    public static function updateRerunCounter($id, $increment = 1) {
        return static::updateAllCounters(['val_2' => $increment], 'id=:id', [':id' => $id]);
    }
    
    public static function deleteRerunByID($id) {
        return static::deleteAll(['id' => $id]);
    }
}
