<?php

namespace backend\models;

use Yii;

class Currencies extends \common\models\Currencies {

    public function getCurrencyInfo($code) {
        $currencies = $this->find()->where(['code' => $code])->exists();
        if (!$currencies) {
            $ogm_db = Yii::$app->get('db_offgamers');
            $currency_sql = "SELECT title,code,symbol_left,symbol_right,decimal_point,thousands_point,decimal_places FROM currencies WHERE  code='" . $code . "' ";
            $currencies = $ogm_db->createCommand($currency_sql)->queryOne();
            $this->title = $currencies['title'];
            $this->code = $currencies['code'];
            $this->symbol_left= $currencies['symbol_left'];
            $this->symbol_right= $currencies['symbol_right'];
            $this->decimal_point= $currencies['decimal_point'];
            $this->thousands_point= $currencies['thousands_point'];	
            $this->decimal_places = $currencies['decimal_places'];
            $this->save();
        }
        $currencies_row = $this->find()->where(['code' => $code])->asArray()->one();
        return $currencies_row;
    }

}
