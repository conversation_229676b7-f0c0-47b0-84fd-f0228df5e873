<?php

namespace backend\models;

use yii\data\ActiveDataProvider;
use Yii;
use yii\db\Query;

class FactPinDistribution extends \common\models\FactPinDistribution {

    public $DimPin_serialno,$DimPin_currency,$DimPin_deno;
    public $DimPinReseller_id,$DimPinReseller_name;
    public $DimPinProduct_name;
    public $DimPinBatchRequest_id,$DimPinBatchRequest_datetime;
    public $DimPinMgcMerchant_id,$DimPinMgcMerchant_name;
    public $DimGeoInfo_ip,$DimGeoInfo_ip_country_iso2;
    public $DimPinMgcTransaction_id,$DimPinMgcTransaction_ref_id;
    public $PinRequest_period,$PinRequest_start_date,$PinRequest_end_date;
    public $PinRedeem_period,$PinRedeem_start_date,$PinRedeem_end_date;
    public $FactPinMgcRedemptions_pin_value;
    public $DimPinMgcTransaction_datetime;

    public function rules() {
        return [
            [['DimPinReseller_name', 'DimPinProduct_name'], 'string', 'max' => 65],
            [['DimPin_serialno','DimPinMgcTransaction_id'], 'string', 'max' => 18],
            [['DimPinBatchRequest_id','DimPinReseller_id'], 'integer'],
            [['DimPin_currency'], 'string', 'max' => 3],
            [['DimPinMgcMerchant_id','PinRequest_period'], 'string', 'max' => 14],
            [['DimPinMgcMerchant_name'], 'string', 'max' => 64],
            [['DimPin_deno','FactPinMgcRedemptions_pin_value'], 'number'],
            [['DimGeoInfo_ip','DimPinMgcTransaction_ref_id'], 'string', 'max' => 32],
            [['DimGeoInfo_ip_country_iso2'], 'string', 'max' => 2],
            [['PinRequest_start_date','PinRequest_end_date','PinRedeem_start_date','PinRedeem_end_date'], 'date', 'format' => 'yyyy-M-d'],
            [['PinRequest_period','PinRedeem_period', 'DimPinMgcTransaction_datetime'], 'safe']
        ];
    }

    public function attributeLabels() {
        return [
            'DimPinReseller_id' => 'Reseller ID',
            'DimPinReseller_name' => 'Reseller Name',
            'DimPin_serialno' => 'Serial No',
            'DimPinProduct_name' => 'Product Name',
            'DimPin_currency' => 'Currency',
            'DimPin_deno' => 'Value(Amount)',
            'DimPinBatchRequest_id' => 'Batch ID',
            'DimPinMgcMerchant_id' => 'Merchant ID',
            'DimPinMgcMerchant_name' => 'Merchant Name',
            'DimGeoInfo_ip' => 'IP',
            'DimGeoInfo_ip_country_iso2' => 'Country',
            'DimPinMgcTransaction_id' => 'Trans ID',
            'DimPinMgcTransaction_ref_id' => 'Merchant Ref ID',
            'FactPinMgcRedemptions_pin_value' => 'Redeem Amount',
            'DimPinMgcTransaction_datetime'  => 'Redeem Date'
            
        ];
    }

    public function getPinDistributionDataProvider($filterParams) {
        $obj = new DimPinReseller();
        $dim_pin_table = DimPin::tableName();
        $dim_pin_reseller_table = DimPinReseller::tableName();
        $dim_pin_batch_request_table = DimPinBatchRequest::tableName();
        $dim_pin_product_table = DimPinProduct::tableName();
        $fact_pin_mgc_redemptions_table = FactPinMgcRedemption::tableName();
        $dim_pin_mgc_merchant_table = DimPinMgcMerchant::tableName();
        $dim_geo_info_table = DimGeoInfo::tableName();
        $dim_date_table=DimDate::tableName();
        $dim_pin_mgc_transaction_table =DimPinMgcTransaction::tableName();
        
        $query = $this::find()->select([$this->tableName() . '.id' ,
                            $dim_pin_reseller_table . '.id AS DimPinReseller_id',
                            $dim_pin_reseller_table . '.name AS DimPinReseller_name',
                            $dim_pin_table . '.serialno AS DimPin_serialno',
                            $dim_pin_table . '.currency AS DimPin_currency',
                            $dim_pin_table . '.deno AS DimPin_deno',
                            $dim_pin_product_table . '.name AS DimPinProduct_name',
                            $dim_pin_batch_request_table . '.id AS DimPinBatchRequest_id',
                            $dim_pin_batch_request_table . '.datetime AS DimPinBatchRequest_datetime',
                            $dim_pin_mgc_merchant_table. '.id AS DimPinMgcMerchant_id',
                            $dim_pin_mgc_merchant_table. '.name AS DimPinMgcMerchant_name',
                            $dim_geo_info_table. '.ip AS DimGeoInfo_ip',
                            $dim_geo_info_table. '.ip_country_iso2 AS DimGeoInfo_ip_country_iso2',
                            $dim_pin_mgc_transaction_table. '.id AS DimPinMgcTransaction_id',
                            $dim_pin_mgc_transaction_table. '.ref_id AS DimPinMgcTransaction_ref_id',
                            $dim_pin_mgc_transaction_table.'.datetime AS DimPinMgcTransaction_datetime',
                            $fact_pin_mgc_redemptions_table.'.pin_value AS FactPinMgcRedemptions_pin_value'])
                        ->joinWith(['fpdPin'=>function($q){ 
                        $q->joinWith(['factPinMgcRedemptions' => function($q) {
                        $q->joinWith('fpmrPmm')->joinWith('fpmrGi')->joinWith('fpmrPmt')->joinWith(['fpmrDate'=> function ($q) {
        $q->from('dim_date du');
         }]);
                       
                    }
                ]);
                            
                            }]);
         $query->joinWith('fpdPr')->joinWith('fpdPp')->joinWith('fpdPbr')->joinWith('fpdDate');
        
         //echo $query->createCommand()->rawSql;exit;
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $dataProvider->setSort([
            'attributes' => [

                'DimPinReseller_id' => [
                    'asc' => ["{$dim_pin_reseller_table}.id" => SORT_ASC],
                    'desc' => ["{$dim_pin_reseller_table}.id" => SORT_DESC],
                ],
                            
                'DimPinReseller_name' => [
                    'asc' => ["{$dim_pin_reseller_table}.name" => SORT_ASC],
                    'desc' => ["{$dim_pin_reseller_table}.name" => SORT_DESC],
                ],
                            
                'DimPin_serialno' => [
                    'asc' => ["{$dim_pin_table}.serialno" => SORT_ASC],
                    'desc' => ["{$dim_pin_table}.serialno" => SORT_DESC],
                ],
                            
                'DimPin_currency' => [
                    'asc' => ["{$dim_pin_table}.currency" => SORT_ASC],
                    'desc' => ["{$dim_pin_table}.currency" => SORT_DESC],
                ],
                            
                'DimPin_deno' => [
                    'asc' => ["{$dim_pin_table}.deno" => SORT_ASC],
                    'desc' => ["{$dim_pin_table}.deno" => SORT_DESC],
                ], 
                            
                'DimPinProduct_name' => [
                    'asc' => ["{$dim_pin_product_table}.name" => SORT_ASC],
                    'desc' => ["{$dim_pin_product_table}.name" => SORT_DESC],
                ],
                
               'DimPinBatchRequest_id' => [
                    'asc' => ["{$dim_pin_batch_request_table}.id" => SORT_ASC],
                    'desc' => ["{$dim_pin_batch_request_table}.id" => SORT_DESC],
                ],             
                                 
               'DimPinMgcMerchant_id'   => [
                    'asc' => ["{$dim_pin_mgc_merchant_table}.id" => SORT_ASC],
                    'desc' => ["{$dim_pin_mgc_merchant_table}.id" => SORT_DESC],
                ], 
                            
               'DimPinMgcMerchant_name'   => [
                    'asc' => ["{$dim_pin_mgc_merchant_table}.name" => SORT_ASC],
                    'desc' => ["{$dim_pin_mgc_merchant_table}.name" => SORT_DESC],
                ], 
               'DimGeoInfo_ip'   => [
                    'asc' => ["{$dim_geo_info_table}.ip" => SORT_ASC],
                    'desc' => ["{$dim_geo_info_table}.ip" => SORT_DESC],
                ], 
                            
                'DimGeoInfo_ip_country_iso2'   => [
                    'asc' => ["{$dim_geo_info_table}.ip_country_iso2" => SORT_ASC],
                    'desc' => ["{$dim_geo_info_table}.ip_country_iso2" => SORT_DESC],
                ], 
                            
                'DimPinMgcTransaction_id'   => [
                    'asc' => ["{$dim_pin_mgc_transaction_table}.id" => SORT_ASC],
                    'desc' => ["{$dim_pin_mgc_transaction_table}.id" => SORT_DESC],
                ], 
                            
                'DimPinMgcTransaction_datetime'   => [
                       'asc' => ["{$dim_pin_mgc_transaction_table}.datetime" => SORT_ASC],
                       'desc' => ["{$dim_pin_mgc_transaction_table}.datetime" => SORT_DESC],
                ],                
            
                'FactPinMgcRedemptions_pin_value'=> [
                    'asc' => ["{$fact_pin_mgc_redemptions_table}.pin_value" => SORT_ASC],
                    'desc' => ["{$fact_pin_mgc_redemptions_table}.pin_value" => SORT_DESC],
                ], 
                
            ]
        ]);


        if (isset($filterParams[$this->formName()]) && !empty($filterParams[$this->formName()])) {
            $filterWhere = [];
            $this->load($filterParams);
            if (!$this->validate()) {
                return $dataProvider;
            }
        }
       
        $query->andFilterWhere([$this->tableName() . '.id' => $this->DimPinReseller_id]);
        $query->andFilterWhere(['like', "{$dim_pin_reseller_table}.name", $this->DimPinReseller_name]);
        $query->andFilterWhere(['like', "{$dim_pin_table}.serialno", $this->DimPin_serialno]);
        $query->andFilterWhere(['like', "{$dim_pin_table}.currency", $this->DimPin_currency]);
        $query->andFilterWhere(['like', "{$dim_pin_table}.deno", $this->DimPin_deno]);
        $query->andFilterWhere(['like', "{$dim_pin_product_table}.name", $this->DimPinProduct_name]);
        $query->andFilterWhere(["{$dim_pin_batch_request_table}.id"=> $this->DimPinBatchRequest_id]);
        $query->andFilterWhere([$dim_pin_mgc_merchant_table . '.id' => $this->DimPinMgcMerchant_id]);
        $query->andFilterWhere(['like', "{$dim_pin_mgc_merchant_table}.name", $this->DimPinMgcMerchant_name]);
        $query->andFilterWhere(['like', "{$dim_pin_mgc_transaction_table}.id", $this->DimPinMgcTransaction_id]);
        $query->andFilterWhere(['like', "{$dim_pin_mgc_transaction_table}.datetime", $this->DimPinMgcTransaction_datetime]);
        $query->andFilterWhere(["{$fact_pin_mgc_redemptions_table}.pin_value"=> $this->FactPinMgcRedemptions_pin_value]);
        
        if(isset($filterParams[$this->formName()]['PinRequest_period'])){
        switch ($filterParams[$this->formName()]['PinRequest_period']) {
                  case 'custom':
                        $query->andFilterWhere(['between', "{$dim_date_table}.date", $this->PinRequest_start_date, $this->PinRequest_end_date]);
                        break;
                    case "yesterday":
                         $query->andFilterWhere(['=', "{$dim_date_table}.date", date('Y-m-d', strtotime('yesterday'))]);
                        break;
                    case "thisweek":
                       $query->andFilterWhere(['=', "{$dim_date_table}.year_week", date('YW')]);
                        break;
                    case "lastweek":
                        $query->andFilterWhere(['=', "{$dim_date_table}.year_week",  date('YW',strtotime('last week'))]);
                        break;
                    case "thismonth":
                        $query->andFilterWhere(['=', "{$dim_date_table}.year_month", date('Ym')]);
                        break;
                    case "thisyear":
                        $query->andFilterWhere(['=', "{$dim_date_table}.year", date('Y')]);
                        break;
                    case "lastyear":
                        $query->andFilterWhere(['=', "{$dim_date_table}.year", date('Y')-1]);
                        break;
                    case "last7days":
                        $query->andFilterWhere(['between', "{$dim_date_table}.date", date("Y-m-d", (time() - (60 * 60 * 24 * 7))), date("Y-m-d")]);
                        break;
                    case "last30days":
                        $query->andFilterWhere(['between', "{$dim_date_table}.date", date("Y-m-d", (time() - (60 * 60 * 24 * 30))), date("Y-m-d")]);
                        break;
                }
        }
        
        
       if(isset($filterParams[$this->formName()]['PinRedeem_period'])){
        switch ($filterParams[$this->formName()]['PinRedeem_period']) {
                  case 'custom':
                        $query->andFilterWhere(['between', "du.date", $this->PinRedeem_start_date, $this->PinRedeem_end_date]);
                        break;
                    case "yesterday":
                         $query->andFilterWhere (['=', "du.date", date('Y-m-d', strtotime('yesterday'))]);
                        break;
                    case "thisweek":
                       $query->andFilterWhere (['=', "du.year_week", date('YW')]);
                        break;
                    case "lastweek":
                        $query->andFilterWhere (['=', "du.year_week",  date('YW',strtotime('last week'))]);
                        break;
                    case "thismonth":
                        $query->andFilterWhere (['=', "du.year_month", date('Ym')]);
                        break;
                    case "thisyear":
                        $query->andFilterWhere (['=', "du.year", date('Y')]);
                        break;
                    case "lastyear":
                        $query->andFilterWhere (['=', "du.year", date('Y')-1]);
                        break;
                    case "last7days":
                        $query->andFilterWhere (['between', "du.date", date("Y-m-d", (time() - (60 * 60 * 24 * 7))), date("Y-m-d")]);
                        break;
                    case "last30days":
                        $query->andFilterWhere (['between', "du.date", date("Y-m-d", (time() - (60 * 60 * 24 * 30))), date("Y-m-d")]);
                        break;
                }
        }
        
        return $dataProvider;
    }

}
