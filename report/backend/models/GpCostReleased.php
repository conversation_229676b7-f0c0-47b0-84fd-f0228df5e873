<?php

namespace backend\models;

use Yii;
use yii\data\ActiveDataProvider;

/**
 * This is the model class for table "gp_cost_released".
 *
 * @property integer $id
 * @property integer $gpo_released_id
 * @property string $purchase_type
 * @property integer $purchase_id
 * @property string $purchase_currency
 * @property string $released_quantity
 * @property string $unit_cost_in_order_currency
 * @property string $unit_cost_in_purchase_currency
 * @property string $unit_cost_in_usd
 * @property string $total_cost_in_usd
 * @property string $purchase_conversion_rate_in_order_currency
 * @property string $purchase_conversion_rate_in_usd
 * @property string $total_cost_in_purchase_currency
 * @property string $total_cost_in_order_currency
 * @property string $extra_info
 */
class GpCostReleased extends \common\models\GpCostReleased {

    public  $released_period,
            $released_start_datetime,
            $released_end_datetime,
            $order_id,
            $unit_price_in_order_currency,
            $order_conversion_rate_in_usd,
            $margin_in_order_currency,
            $released_start_date,
            $released_end_date,
            $margin_cur,
            $margin_usd,
            $margin_cur_input,
            $margin_usd_input,
            $margin_percentage_in_order_currency,
            $order_currency,
            $payment_method,
            $product_name,
            $total_released_in_order_currency,
            $total_purchase_in_purchase_currency,
            $total_purchase_in_usd,
            $total_released_in_usd,
            $margin_in_usd,
            $margin_percentage_in_usd,
            $company_name;

    public function rules() {
        return yii\helpers\ArrayHelper::merge(
            parent::rules(), [
            [['released_start_datetime', 'released_end_datetime'], 'safe'],
            [['order_id', 'gpo_released_id', 'margin_cur_input', 'margin_usd_input'], 'integer'],
            [['unit_price_in_order_currency', 'order_conversion_rate_in_usd', 'total_purchase_in_usd', 'total_released_in_usd', 'order_conversion_rate_in_usd', 'margin_in_order_currency', 'margin_percentage_in_order_currency', 'total_released_in_order_currency', 'total_purchase_in_purchase_currency', 'total_purchase_in_usd', 'margin_in_usd', 'margin_percentage_in_usd'], 'number'],
            [['order_currency', 'margin_cur', 'margin_usd'], 'string', 'max' => 3],
            [['payment_method'], 'string', 'max' => 255],
            [['company_name'], 'string', 'max' => 32],    
            [['product_name'], 'string', 'max' => 255],
            [['released_start_date', 'released_end_date'], 'date', 'format' => 'yyyy-M-d']
        ]);
    }

    public function attributeLabels() {
        return [
            'id' => 'ID',
            'released_start_datetime' => 'Date',
            'released_end_datetime' => 'Released Date',
            'order_id' => 'Order ID',
            'order_currency' => 'Order Currency',
            'payment_method' => 'Payment Method',
            'product_name' => 'Product Name',
            'unit_price_in_order_currency' => 'CO Selling Price',
            'margin_in_order_currency' => 'Margin In CO Currency',
            'margin_percentage_in_order_currency' => 'Margin In Percentage',
            'order_conversion_rate_in_usd' => 'Conversion Rate To Usd',
            'total_purchase_in_usd' => 'PO Cost In Usd',
            'total_released_in_usd' => 'CO Price In Usd',
            'margin_in_usd' => 'Margin In Usd',
            'margin_percentage_in_usd' => 'Margin Percentage In Usd',
            'gpo_released_id' => 'Gp Order ID',
            'purchase_type' => 'Type',
            'purchase_id' => 'Po ID',
            'unit_cost_in_purchase_currency' => 'PO Cost In Original Price',
            'company_name' => 'Stock Purchase',
            'released_quantity' => 'Quantity Released',
            'purchase_conversion_rate_in_order_currency' => 'Conversion Rate',
            'unit_cost_in_order_currency' => 'PO Cost Follow CO Currency',
            'total_sc_in_order_currency' => 'Total Store Credit In Order Currency',
            'total_payment_in_order_currency' => 'Total Payment In Order Currency',
            'total_sc_in_usd' => 'Total Store Credit In Usd',
            'total_payment_in_usd' => 'Total Payment In Usd',
            'extra_info' => 'Extra Info',
        ];
    }

    public function getGpDataProvider($filterParams) {
        $gp_table = GpOrderReleased::tableName();
        $gp_entity = DimGpEntity::tableName();
        $default_order = Yii::$app->request->get('sort');
        $query = $this::find()->joinWith('gpOrderReleased')->joinWith('gpEntity');
        if (empty($default_order)) {
            $query->orderBy("{$gp_table}.released_end_datetime DESC, {$gp_table}.order_id");
            $query->groupBy("
                (CASE {$this->tableName()}.purchase_type 
                    WHEN 'API' THEN {$this->tableName()}.gpo_released_id
                    WHEN 'DTU' THEN {$this->tableName()}.gpo_released_id 
                    ELSE {$this->tableName()}.id
                END)");
        }
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $dataProvider->setSort([
            'attributes' => [
                'product_unit_price', 'company_name', 'unit_cost_in_purchase_currency', 'unit_price_in_order_currency', 'product_name',
                'released_start_datetime' => [
                    'asc' => ["{$gp_table}.released_start_datetime" => SORT_ASC],
                    'desc' => ["{$gp_table}.released_start_datetime" => SORT_DESC],
                ],
                'released_end_datetime' => [
                    'asc' => ["{$gp_table}.released_end_datetime" => SORT_ASC],
                    'desc' => ["{$gp_table}.released_end_datetime" => SORT_DESC],
                ],
                'order_id' => [
                    'asc' => ["{$gp_table}.order_id" => SORT_ASC],
                    'desc' => ["{$gp_table}.order_id" => SORT_DESC],
                ],
                'order_currency' => [
                    'asc' => ["{$gp_table}.order_currency" => SORT_ASC],
                    'desc' => ["{$gp_table}.order_currency" => SORT_DESC],
                ],
                'payment_method' => [
                    'asc' => ["{$gp_table}.payment_method" => SORT_ASC],
                    'desc' => ["{$gp_table}.payment_method" => SORT_DESC],
                ],
            ]
        ]);

        if (isset($filterParams[$this->formName()]) && !empty($filterParams[$this->formName()])) {
            $this->load($filterParams);
            if (!$this->validate()) {
                return $dataProvider;
            }
        }

        // $query->andFilterWhere(['LIKE', $gp_table . '.released_start_datetime', trim($this->released_start_datetime)]);
        $query->andFilterWhere(['LIKE', $gp_table . '.released_end_datetime', trim($this->released_end_datetime)]);
        $query->andFilterWhere(['LIKE', $gp_table . '.order_id', trim($this->order_id)]);
        $query->andFilterWhere(['LIKE', $gp_table . '.order_currency', trim($this->order_currency)]);
        $query->andFilterWhere(['LIKE', $gp_table . '.payment_method', trim($this->payment_method)]);
        $query->andFilterWhere(['LIKE', $gp_table . '.product_name', trim($this->product_name)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.unit_price_in_order_currency', trim($this->unit_price_in_order_currency)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.total_released_in_order_currency', trim($this->total_released_in_order_currency)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.margin_in_order_currency', trim($this->margin_in_order_currency)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.margin_percentage_in_order_currency', trim($this->margin_percentage_in_order_currency)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.order_conversion_rate_in_usd', trim($this->order_conversion_rate_in_usd)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.total_purchase_in_usd', trim($this->total_purchase_in_usd)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.total_released_in_usd', trim($this->total_released_in_usd)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.margin_in_usd', trim($this->margin_in_usd)]);
        // $query->andFilterWhere(['LIKE', $gp_table . '.margin_percentage_in_usd', trim($this->margin_percentage_in_usd)]);
        $query->andFilterWhere(['LIKE', $gp_entity . '.gpe_id', trim($this->company_name)]);

        if (!empty($this->unit_cost_in_purchase_currency)) {
            $query->andFilterWhere(['LIKE', $this->tableName() . '.unit_cost_in_purchase_currency', trim($this->unit_cost_in_purchase_currency)]);
        }
        // if (!empty($this->released_quantity)) {
        //     $query->andFilterWhere(['LIKE', $this->tableName() . '.released_quantity', trim($this->released_quantity)]);
        // }
        // if (!empty($this->purchase_conversion_rate_in_order_currency)) {
        //     $query->andFilterWhere(['LIKE', $this->tableName() . '.purchase_conversion_rate_in_order_currency', trim($this->purchase_conversion_rate_in_order_currency)]);
        // }
        // if (!empty($this->unit_cost_in_order_currency)) {
        //     $query->andFilterWhere(['LIKE', $this->tableName() . '.unit_cost_in_order_currency', trim($this->unit_cost_in_order_currency)]);
        // }

        if (isset($filterParams[$this->formName()]['released_period'])) {
            switch ($filterParams[$this->formName()]['released_period']) {
                case 'custom':
                    $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", $this->released_start_date]);
                    $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", $this->released_end_date]);
                    break;
                case "yesterday":
                    $query->andFilterWhere(['=', "DATE({$gp_table}.released_end_datetime)", date('Y-m-d', strtotime('yesterday'))]);
                    break;
                case "thisweek":
                    $mday = strtotime("last monday");
                    $monday = date('w', $mday) == date('w') ? $mday + 7 * 86400 : $mday;
                    $sunday = strtotime(date("Y-m-d", $monday) . " +6 days");
                    $this_week_sd = date("Y-m-d", $monday);
                    $this_week_ed = date("Y-m-d", $sunday);
                    $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", $this_week_sd]);
                    $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", $this_week_ed]);
                    break;
                case "lastweek":
                    $mday = strtotime("last monday");
                    $monday = date('W', $mday) == date('W') ? $mday - 7 * 86400 : $mday;
                    $sunday = strtotime(date("Y-m-d", $monday) . " +6 days");
                    $this_week_sd = date("Y-m-d", $monday);
                    $this_week_ed = date("Y-m-d", $sunday);
                    $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", $this_week_sd]);
                    $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", $this_week_ed]);
                    break;
                case "thismonth":
                    $query->andFilterWhere(['=', "MONTH({$gp_table}.released_end_datetime)", date('m')]);
                    break;
                case "thisyear":
                    $query->andFilterWhere(['=', "YEAR({$gp_table}.released_end_datetime)", date('Y')]);
                    break;
                case "lastyear":
                    $query->andFilterWhere(['=', "YEAR({$gp_table}.released_end_datetime)", date('Y') - 1]);
                    break;
                case "last7days":
                    $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d", (time() - (60 * 60 * 24 * 7)))]);
                    $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d")]);
                    break;
                case "last30days":
                    $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d", (time() - (60 * 60 * 24 * 30)))]);
                    $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d")]);
                    break;
            }
        } else {
            // Current date
            $query->andFilterWhere(['>=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d")]);
            $query->andFilterWhere(['<=', "DATE({$gp_table}.released_end_datetime)", date("Y-m-d")]);
        }

        // if (isset($filterParams[$this->formName()]['margin_cur_input'])) {
        //     $query->andFilterWhere([$this->margin_cur, "{$gp_table}.margin_percentage_in_order_currency", trim($this->margin_cur_input)]);
        // }
        // if (isset($filterParams[$this->formName()]['margin_usd_input'])) {
        //     $query->andFilterWhere([$this->margin_usd, "{$gp_table}.margin_percentage_in_usd", trim($this->margin_usd_input)]);
        // }

        // echo $query->createCommand()->rawSql;

        return $dataProvider;
    }

    public function getPoTotal($gpo_released_id, $id, $cost_type) {
        $total = 0;
        
        $po_released_quantity = $this->getSumDeliveredQuantity($gpo_released_id, $id);

        $provider_gp = GpOrderReleased::find()->where(['id' => $gpo_released_id])->one();
        $total_released_quantity = $provider_gp->total_released_quantity;
        $total_order_in_order_currency = ($cost_type == 'order_currency') ? $provider_gp->total_released_in_order_currency : $provider_gp->total_released_in_usd;

        return $total = ($total_order_in_order_currency / $total_released_quantity) * $po_released_quantity;
    }

    public function getPoMargin($gpo_released_id, $id, $cost_type) {
        $total = 0;
        
        $po_released_quantity = $this->getSumDeliveredQuantityCalculation($gpo_released_id, $id);
        $total_order_in_order_currency = $this->getPoTotal($gpo_released_id, $id, $cost_type);

        $query = $this::find()->where(['id' => $id])->one();
        $total_cost_in_order_currency = ($cost_type == 'order_currency') ? $query->total_cost_in_order_currency : $query->total_cost_in_usd;

        return $total_order_in_order_currency - ($total_cost_in_order_currency * $po_released_quantity);
    }

    public function getPoMarginPercentage($gpo_released_id, $id, $cost_type) {
        $po_total = $this->getPoTotal($gpo_released_id, $id, $cost_type);
        $po_margin = $this->getPoMargin($gpo_released_id, $id, $cost_type);

        if ($po_total == 0 || $po_margin == 0) {
            return 0;
        }

        return $percentage = ($po_margin / $po_total) * 100;
    }

    public function getSumDeliveredQuantity($gpo_released_id, $id) {
        $purchase_type = "";
        $released_quantity = 0;
        $query = $this::find()->where(['id' => $id])->one();
        $purchase_type = $query->purchase_type;
        $released_quantity = $query->released_quantity;
        
        if ($purchase_type == 'PO' || $purchase_type == 'DTU' || $purchase_type == 'PIN' || $purchase_type == 'PHYSICAL') {
            return $released_quantity;
        } else {
            $query = $this::find()->where(['gpo_released_id' => $gpo_released_id]);
            return $query->sum('released_quantity');
        }
    }

    public function getSumDeliveredQuantityCalculation($gpo_released_id, $id) {
        $purchase_type = "";
        $released_quantity = 0;
        $query = $this::find()->where(['id' => $id])->one();
        $purchase_type = $query->purchase_type;
        $released_quantity = $query->released_quantity;
        
        if ($purchase_type == 'PO' || $purchase_type == 'DTU' || $purchase_type == 'PIN' || $purchase_type == 'PHYSICAL') {
            return 1;
        } else {
            $query = $this::find()->where(['gpo_released_id' => $gpo_released_id]);
            return $query->sum('released_quantity');
        }
    }

    public function getSumUnitCostInPurchaseCurrency($gp_order_id) {
        $query = $this::find()->where(['gpo_released_id' => $gp_order_id]);
        return $query->sum('unit_cost_in_purchase_currency');
    }

}
