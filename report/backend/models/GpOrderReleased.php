<?php

namespace backend\models;

class GpOrderReleased extends \common\models\GpOrderReleased
{
    public function isOrderExists($order_id, $save = false, $attributes = []) {
        $current_row = $this->find()
                ->select(['id', 'order_id', 'order_conversion_rate','unit_price_in_order_currency','total_released_quantity'])
                ->where(['order_id' => $order_id])
                ->asArray()
                ->one();
        if ($current_row['id']) {
            $id = $current_row['id'];
            if ($save) {
                $update_status = $this->updateAll($attributes, "id =$id");
                return isset($update_status) ? $id : 0;
            }
        }
        return $current_row;
    }
}
