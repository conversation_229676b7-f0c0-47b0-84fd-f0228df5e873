<?php

namespace backend\models;

use Yii;
use yii\db\ActiveRecord;
use yii\helpers\Security;
use yii\web\IdentityInterface;

/**
 * This is the model class for table "tbl_user".
 *
 * @property integer $id
 * @property string $username
 * @property string $password
 * @property string $password_hash
 * @property string $email
 */
class User extends \common\models\User {

    public $oldPassword;
    public $password;
    public $retypePassword;
    public $userMainGroup;

    /**
     * @inheritdoc
     */
    public function rules() {
        return [
            ['username', 'filter', 'filter' => 'trim'],
            ['username', 'required', 'on' => 'create'],
            ['username', 'unique', 'on' => 'create'],
            ['username', 'string', 'min' => 2, 'max' => 255],
            ['email', 'filter', 'filter' => 'trim'],
            ['email', 'required', 'on' => ['create', 'update']],
            ['email', 'email'],
            ['email', 'unique', 'targetClass' => '\common\models\User', 'message' => 'This {attribute} address has already been taken.', 'on' => ['create', 'update']],
            [['password', 'retypePassword'], 'required', 'on' => ['create', 'setPassword']],
            [['password', 'retypePassword'], 'string', 'min' => 6],
            ['retypePassword', 'compare', 'compareAttribute' => 'password', 'on' => ['create', 'setPassword']],
            ['oldPassword', 'required', 'on' => 'setPassword'],
            ['oldPassword', 'checkValidPassword', 'on' => 'setPassword'],
            ['status', 'required', 'on' => 'update'],
            ['status', 'default', 'value' => self::STATUS_ACTIVE],
            ['status', 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_DELETED]],
        ];
    }

    public function attributeLabels() {
        if ($this->scenario == "setPassword") {
            return [
                'password' => 'New Password',
                'retypePassword' => 'Re-Enter New password',
                'oldPassword' => 'Old Password',
            ];
        } else {
            return [
                'username' => 'Username',
                'email' => 'E-mail',
                'password' => 'Password',
                'retypePassword' => 'Confirm Password',
                'status' => 'Status',
                'assignedRoles' => 'Roles'
            ];
        }
    }

    public function signup() {
        if ($this->validate()) {
            $attributes = $this->attributes;
            $attributes['password'] = $this->password;
            return User::create($attributes);
        }
        return null;
    }

    public function updateProfile() {
        $this->scenario = 'update';
        if ($this->validate()) {
            if ($this->checkSuperAdmin()) {
                if ($this->isSuperAdmin()) {
                    Yii::$app->session->setFlash('success', 'Update Success');
                    $this->update(false, ['email']);
                } else {
                    Yii::$app->session->setFlash('danger', 'You are not allow to edit superadmin details!');
                }
            } else {
                Yii::$app->session->setFlash('success', 'Update Success');
                $this->update(false, ['email', 'status']);
            }
            return true;
        }
        return null;
    }

    public function deleteUser() {
        if ($this->checkSuperAdmin()) {
            Yii::$app->session->setFlash("danger.", 'You are not allow to delete superadmin!');
        } else {
            if ($this->delete()) {
                Yii::$app->session->setFlash("success.", 'Delete success');
            } else {
                Yii::$app->session->setFlash("danger.", 'Failed to delete');
            }
        }
    }

    public function getStatusList() {
        return [10 => 'Active', 0 => 'In-active'];
    }

    public function checkValidPassword($attribute, $params) {
        if (!$this->validatePassword($this->oldPassword)) {
            $this->addError('oldPassword', 'The password entered is incorrect.');
        }
    }

    public function getUserRoles() {
        
        return 'admin';
        /*return $this->hasMany(\common\models\AuthItem::classname(), ['name' => 'item_name'])
                        ->viaTable(\common\models\AuthAssignment::tableName(), ['user_id' => 'id']);*/
    }

}
