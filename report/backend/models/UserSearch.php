<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use backend\models\User;

/**
 * UserSearch represents the model behind the search form about `backend\models\User`.
 */
class UserSearch extends User {

    public $id;
    public $username;
    public $auth_key;
    public $password_hash;
    public $password_reset_token;
    public $email;
    public $role;
    public $status;
    public $created_at;
    public $updated_at;
    public $userRoles;
    public $userMainGroup;

    public function rules() {
        return [
            [['id', 'role', 'status', 'created_at', 'updated_at'], 'integer'],
            [['username', 'auth_key', 'password_hash', 'password_reset_token', 'email'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels() {
        return [
            'id' => 'ID',
            'username' => 'Username',
            'auth_key' => 'Auth Key',
            'password_hash' => 'Password Hash',
            'password_reset_token' => 'Password Reset Token',
            'email' => 'Email',
            'role' => 'Role',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'userRoles' => 'Roles',
            'userMainGroup' => 'User Group'
        ];
    }

    public function search($params) {

        $currentUserId = (isset(Yii::$app->user->id) ? Yii::$app->user->id : 0);
        $currentUserGroup = User::getUserGroup($currentUserId);

        $query = User::find();

        $query->select("user.id,
    user.username,
    (CASE
        WHEN user.id = 1 THEN 'superadmin'
        WHEN c.item_name LIKE 'admin_%' THEN 'admin'
        WHEN d.item_name LIKE 'merchant_%' THEN 'merchant'
        ELSE NULL
        END) as 'userMainGroup',
    user.status");

        $query->leftJoin('auth_assignment as c', "c.user_id = user.id AND c.item_name LIKE 'admin_%'");

        $query->leftJoin('auth_assignment as d', "d.user_id = user.id AND d.item_name LIKE 'merchant_%'");

        $query->leftJoin('auth_assignment as f', "f.user_id = user.id AND f.item_name IS NULL AND user.id != 1");

        if($currentUserGroup == 'admin'){
            $query->where("user.id != 1 AND c.item_name IS NULL AND d.item_name LIKE 'merchant_%' OR (user.id !=1 AND c.item_name IS NULL AND f.item_name IS NULL)");
        }

        $query->groupBy('user.id');

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $dataProvider->setSort([
            'attributes' => [
                'id',
                'username',
                'status',
                'userRoles' => [
                    'asc' => ['user.id' => SORT_ASC, 'c.item_name' => SORT_ASC, 'd.item_name' => SORT_ASC, 'f.item_name' => SORT_ASC],
                    'desc' => ['user.id' => SORT_DESC, 'c.item_name' => SORT_DESC, 'd.item_name' => SORT_DESC, 'f.item_name' => SORT_DESC],
                    'label' => 'Roles',
                    'default' => SORT_ASC
                ]
            ],
            'defaultOrder' => ['id' => SORT_ASC, 'userRoles' => SORT_ASC]
        ]);

        if (!($this->load($params) && $this->validate())) {
            return $dataProvider;
        }
        
//        $command = $dataProvider->query->createCommand();
//        print_r($command->sql);

        $this->addCondition($query, 'id');
        $this->addCondition($query, 'username', true);
        $this->addCondition($query, 'auth_key', true);
        $this->addCondition($query, 'password_hash', true);
        $this->addCondition($query, 'password_reset_token', true);
        $this->addCondition($query, 'email', true);
        $this->addCondition($query, 'role');
        $this->addCondition($query, 'status');
        $this->addCondition($query, 'created_at');
        $this->addCondition($query, 'updated_at');
        return $dataProvider;
    }

    protected function addCondition($query, $attribute, $partialMatch = false) {
        if (($pos = strrpos($attribute, '.')) !== false) {
            $modelAttribute = substr($attribute, $pos + 1);
        } else {
            $modelAttribute = $attribute;
        }

        $value = $this->$modelAttribute;
        if (trim($value) === '') {
            return;
        }
        if ($partialMatch) {
            $query->andWhere(['like', $attribute, $value]);
        } else {
            $query->andWhere([$attribute => $value]);
        }
    }

}
