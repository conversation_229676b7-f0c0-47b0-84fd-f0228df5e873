<?php

namespace backend\modules\cronjob\components;

use backend\modules\cronjob\models\CronLogs;
use yii\db\Expression;

class CronLogCom {

    public function createLog($log_data, $log_user_msg, $type = '', $from_id = 0, $to_id = 0, $report_type = '') {

        if ($report_type == 'released') {
            $gp_task = 'GpReleasedCronJob::id';
        }
        else {
            $gp_task = 'GpCronJob::id';
        }
        
        if ($type == 'po_id' || $type == 'final_price') {
            ob_start();
            echo "<pre>";
            print_r($log_data);
            $log_str = ob_get_contents();
            ob_end_clean();

            $this->saveCronLog([
                'log_time' => new Expression('NOW()'),
                'log_user_messages' => $log_user_msg,
                'log_system_messages' => $log_str,
                'log_field_name' => $gp_task,
                'log_from_value' => (string) reset($log_data),
                'log_to_value' => (string) end($log_data),
            ]);
        } else if (empty($type)) {
            $from_id = 0;
            $to_id = 0;

            ob_start();
            echo "<pre>";
            print_r($log_data);
            $log_str = ob_get_contents();
            ob_end_clean();

//            if ($log_id_array = array_keys($log_data)) {
//                $from_id = array_shift($log_id_array);
//                $to_id = array_pop($log_id_array);
//            }

            $this->saveCronLog([
                'log_time' => new Expression('NOW()'),
                'log_user_messages' => $log_user_msg,
                'log_system_messages' => $log_str,
                'log_field_name' => $gp_task,
                'log_from_value' => (string) $from_id,
                'log_to_value' => (string) $to_id,
            ]);
        }
    }

    public function saveCronLog($input_array) {
        $model = new CronLogs();
        $model->setAttributes($input_array);
        return $model->save();
    }

}
