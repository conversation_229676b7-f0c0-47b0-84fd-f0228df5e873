<?php

namespace backend\modules\cronjob\components;

use Yii;
use backend\components\CurrenciesCom;

class NotificationCom
{
    private $container = [],
        $cur_obj;
    private $alert_setting = [
        'alert_1' => [
            'config_params_key' => 'low_margin',
            'setting' => [
                'icon_emoji' => ':inbox_tray:',
                'color' => 'warning',
                'text' => 'Alert!',
                'pretext' => 'The Below Delivery has Low GP:',
                'fields' => []
            ],
            'field_pattern' => [
                'title' => 'Order Number : #{order_id}',
                'value' => "Product ID : {products_id} \n Product Name : {products_name} \n GP in Order : {margin_percentage_in_order_currency}%",
                'short' => false
            ]
        ],
        'alert_2' => [
            'config_params_key' => 'negative_margin',
            'setting' => [
                'icon_emoji' => ':inbox_tray:',
                'color' => 'danger',
                'text' => 'Alert!',
                'pretext' => 'The Below Delivery has Negative GP:',
                'fields' => []
            ],
            'field_pattern' => [
                'title' => 'Order Number : #{order_id}',
                'value' => "Product ID : {products_id} \n Product Name : {products_name} \n Qty Delivered : {total_delivered_quantity} \n Delivered Cost : {total_purchase_in_usd_formatted} \n Product Selling Price : {total_order_in_usd_formatted} \n GP in Order : {margin_percentage_in_order_currency}%",
                'short' => false
            ]
        ],
        'alert_3' => [
            'config_params_key' => 'missing_gp_data',
            'setting' => [
                'icon_emoji' => ':inbox_tray:',
                'color' => 'danger',
                'pretext' => 'Data missing as below:',
                'fields' => []
            ],
            'field_pattern' => [
                'title' => 'Order Number : #{order_id}',
                'value' => "Report Type: {report_type} \n Product ID : {products_id} \n Product Name : {products_name} \n Missing Data : {missing_data_str}",
                'short' => false
            ]
        ],
    ];

    public function __construct()
    {
        $this->cur_obj = new CurrenciesCom();
    }


    public function scanMargin($input_data)
    {
        $margin = isset($input_data['margin_percentage_in_order_currency']) ? $input_data['margin_percentage_in_order_currency'] : null;
        $total_purchase_in_order_currency = isset($input_data['total_purchase_in_order_currency']) ? $input_data['total_purchase_in_order_currency'] : null;

        if ($margin && $total_purchase_in_order_currency > 0) {
            $data = [
                'order_id' => $input_data['order_id'],
                'products_id' => $input_data['products_id'],
                'products_name' => $input_data['products_name'],
                'total_delivered_quantity' => $input_data['total_delivered_quantity'],

                'total_order_in_usd' => $input_data['total_order_in_usd'],
                'total_purchase_in_usd' => $input_data['total_purchase_in_usd'],

                'margin_percentage_in_order_currency' => $input_data['margin_percentage_in_order_currency'],

                'total_order_in_usd_formatted' => $this->cur_obj->displayFormat($input_data['total_order_in_usd'], 'USD'),
                'total_purchase_in_usd_formatted' => $this->cur_obj->displayFormat($input_data['total_purchase_in_usd'], 'USD'),
            ];

            # temporary added on 2017-04-19 and request by Alfred
            if ($margin >= 0 && $margin <= 3) {
                $this->container['alert_1'][] = $data;
            } else if ($margin < 0) {
                $this->container['alert_2'][] = $data;
            }
        }
    }

    public function scanMissingData($input_data)
    {
        $data = [
            'order_id' => $input_data['order_id'],
            'products_id' => $input_data['products_id'],
            'products_name' => $input_data['products_name'],
            'report_type' => $input_data['report_type'],
            'missing_data_str' => $input_data['missing_data_str'],
        ];

        $this->container['alert_3'][] = $data;
    }

    public function fireNotification()
    {
        if ($this->container) {
            $curl_obj = new \common\components\CurlCom();

            foreach ($this->container as $alert_type => $alert_data_array) {
                $alert_info = $this->alert_setting[$alert_type];
                $url = isset(Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['url']) ? Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['url'] : '';

                $curl_obj->ssl_verification = isset(Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['ssl_verification']) ? Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['ssl_verification'] : '';

                if ($url) {
                    $alert_setting = $alert_info['setting'];
                    $alert_field = $alert_info['field_pattern'];

                    foreach ($alert_data_array as $data) {
                        $field = $alert_field;
                        $field['title'] = $this->t($alert_field['title'], $data);
                        $field['value'] = $this->t($alert_field['value'], $data);

                        $alert_setting['fields'][] = $field;
                    }

                    if (isset(Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['legacy']) && Yii::$app->params['slack_channel'][$alert_info['config_params_key']]['legacy'] == false) {
                        $attachment = $alert_setting['fields'];
                        $attachment_data = array();
                        foreach ($attachment as $item) {
                            $attachment_data[] = [
                                'color' => $alert_setting['color'],
                                'text' => ((isset($alert_setting['text'])) ? ($alert_setting['text'] . "\n") : "") . '*' . $item['title'] . '*' . "\n" . $item['value']
                            ];
                        }
                        $slack_body = [
                            'text' => $alert_setting['pretext'],
                            'attachments' => $attachment_data
                        ];
                        $curl_obj->sendPost($url, json_encode($slack_body));

                    } else {
                        $curl_obj->sendPost($url, json_encode($alert_setting));
                    }


                }
            }

            unset($curl_obj);
        }
    }

    public function t($message, $params = [])
    {
        $p = [];
        foreach ((array)$params as $name => $value) {
            $p['{' . $name . '}'] = $value;
        }

        return ($p === []) ? $message : strtr($message, $p);
    }

}
