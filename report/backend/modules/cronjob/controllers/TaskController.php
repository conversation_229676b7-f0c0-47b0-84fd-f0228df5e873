<?php

namespace backend\modules\cronjob\controllers;

use Yii;
use backend\modules\cronjob\components\DefaultController;
use backend\modules\cronjob\models\CronProcessTrack;
use backend\modules\cronjob\models\CronPointer;
use backend\components\CurrenciesCom;
use backend\components\GeneralCom;
use backend\modules\cronjob\components\CronLogCom;
use yii\db\Expression;

class TaskController extends DefaultController {

    private function ogcPinInsert($ogc_pins, $retry_mode = false) {
        $dim_pin_client = [];
        $dim_ogc_crew = [];
        $dim_pin_product = [];
        $dim_pin_reseller = [];
        $dim_pin_batch_request = [];
        $dim_pin = [];
        $dim_date = [];
        $fact_pin_distribution = [];

        $last_pin_id = 0;
        $ogc_process_count = 0;

        $og_model = new \backend\modules\cronjob\models\OgcModel();
        $mgc_com_obj = new \backend\components\MgcReportCom();
        $dim_pin_batch_request_obj = new \backend\models\DimPinBatchRequest();
        $dim_pin_obj = new \backend\models\DimPin();
        $fact_pin_distribution_obj = new \common\models\FactPinDistribution();

        foreach ($ogc_pins as $idx => $ogc_pin) {
            # dim pin client
            $dim_pin_client['id'] = $ogc_pin['client_id'];
            $dim_pin_client['name'] = $ogc_pin['client_name'];
            $dim_pin_client['url'] = $ogc_pin['url'];
            $dim_pin_client['description'] = $ogc_pin['client_description'];

            $pc_id = $mgc_com_obj->getDimPinClientPCID($dim_pin_client['id'], $dim_pin_client);

            # dim ogc crew
            list($dim_ogc_crew['id'], $dim_ogc_crew['first_name'], $dim_ogc_crew['last_name'], $dim_ogc_crew['email'], $dim_ogc_crew['type']) = array_values($og_model->getOgcCrewInfo($ogc_pin['request_by']));
            $oc_id = $mgc_com_obj->getDimOgcCrewOCID($dim_ogc_crew['id'], array_reverse($dim_ogc_crew));

            # dim pin product
            $dim_pin_product['id'] = $ogc_pin['product_id'];
            $dim_pin_product['name'] = $ogc_pin['product_name'];
            $dim_pin_product['description'] = $ogc_pin['product_description'];

            $pp_id = $mgc_com_obj->getDimPinProductPPID($dim_pin_product['id'], $dim_pin_product);

            # dim pin reseller
            $dim_pin_reseller['id'] = $ogc_pin['reseller_id'];
            $dim_pin_reseller['name'] = $ogc_pin['reseller_name'];
            $dim_pin_reseller['email'] = $ogc_pin['email'];
            $dim_pin_reseller['tel'] = $ogc_pin['tel'];
            $dim_pin_reseller['code'] = empty($ogc_pin['reseller_code']) ? '-' : $ogc_pin['reseller_code'];

            $pr_id = $mgc_com_obj->getDimPinResellerPRID($dim_pin_reseller['id'], $dim_pin_reseller);

            # dim pin batch request
            $dim_pin_batch_request['id'] = $ogc_pin['batch_id'];
            $dim_pin_batch_request['description'] = $ogc_pin['gr_description'];
            $dim_pin_batch_request['title'] = $ogc_pin['title'];
            $dim_pin_batch_request['datetime'] = $ogc_pin['request_date'];
            $dim_pin_batch_request['approved_by'] = $oc_id;
            $dim_pin_batch_request['request_by'] = $oc_id;

            $pbr_id = $dim_pin_batch_request_obj->doUpdateIfExists('pbr_id', $dim_pin_batch_request['id'], $dim_pin_batch_request);

            # dim pin 
            $dim_pin['id'] = $ogc_pin['pin_id'];
            $dim_pin['serialno'] = $ogc_pin['serial'];
            $dim_pin['currency'] = $ogc_pin['currency'];
            $dim_pin['deno'] = $ogc_pin['deno'];
            $dim_pin['valid_start'] = $ogc_pin['start_date'];
            $dim_pin['valid_end'] = $ogc_pin['end_date'];

            $pin_id = $dim_pin_obj->doUpdateIfExists('pin_id', $dim_pin['id'], $dim_pin);

            # dim date 
            $ts = strtotime(date($ogc_pin['request_date']));
            $year = date('Y', $ts);
            $dim_date['year_month'] = $year . date('m', $ts);
            $dim_date['year_week'] = $year . date('W', $ts);
            $dim_date['year_day'] = $year . date('z', $ts);
            $dim_date['year_quarter'] = $year . ceil(date('n', $ts) / 3);
            $dim_date['year'] = $year;
            $dim_date['date'] = date('Y-m-d', $ts);

            $date_id = $mgc_com_obj->getDimDateDATEID($dim_date['date'], $dim_date);

            $ogc_process_count += 1;
            $last_pin_id = $ogc_pin['pin_id'];

            if ($pin_id) {
                if(\common\models\FactPinDistribution::find()->where('fpd_pin_id=:fpd_pin_id', [
                    ':fpd_pin_id' => $pin_id,
                    ])->exists() === false) {
                    
                    # fact pin distribution
                    $fact_pin_distribution['fpd_pin_id'] = $pin_id;
                    $fact_pin_distribution['fpd_pbr_id'] = $pbr_id;
                    $fact_pin_distribution['fpd_pr_id'] = $pr_id;
                    $fact_pin_distribution['fpd_date_id'] = $date_id;
                    $fact_pin_distribution['fpd_pp_id'] = $pp_id;
                    $fact_pin_distribution['fpd_pc_id'] = $pc_id;
                    $fact_pin_distribution['pin_value'] = $ogc_pin['deno'];

                    $fact_pin_distribution_obj->saveNewRecord($fact_pin_distribution, true);

                    if (!$fact_pin_distribution_obj->primaryKey) {
                        if ($retry_mode) {
                            \backend\models\CronRetry::updateRerunCounter($idx);
                        } else {
                            $retry_array = [
                                'action' => 'RERUN',
                                'task' => 'MGC_PIN',
                                'key_1' => 'error-type',
                                'val_1' => 1,
                                'key_2' => 'rerun-counter',
                                'val_2' => 1,
                                'json_str' => json_encode($ogc_pin),
                                'create_datetime' => new Expression('NOW()')
                            ];

                            $cron_retry_obj = new \backend\models\CronRetry();
                            $cron_retry_obj->saveNewRecord($retry_array, true);
                            
                            if ($cron_retry_obj->primaryKey) {
                                $this->createCronLogs([
                                    'log_time' => date('Y-m-d H:i:s'),
                                    'log_user_messages' => 'Error: Insert DB',
                                    'log_system_messages' => json_encode($fact_pin_distribution),
                                    'log_field_name' => 'ogcPinInsert:rerun-id',
                                    'log_from_value' => (string) $cron_retry_obj->primaryKey,
                                    'log_to_value' => '1'
                                ]);
                            }
                            
                            unset($cron_retry_obj);
                        }
                    } else if ($retry_mode) {
                        \backend\models\CronRetry::deleteRerunByID($idx);
                    }
                } else if ($retry_mode) {
                    \backend\models\CronRetry::deleteRerunByID($idx);
                }
            } else {
                if ($retry_mode) {
                    \backend\models\CronRetry::updateRerunCounter($idx);
                } else {
                    $retry_array = [
                        'action' => 'RERUN',
                        'task' => 'MGC_PIN',
                        'key_1' => 'error-type',
                        'val_1' => 2,
                        'key_2' => 'rerun-counter',
                        'val_2' => 1,
                        'json_str' => json_encode($ogc_pin),
                        'create_datetime' => new Expression('NOW()')
                    ];

                    $cron_retry_obj = new \backend\models\CronRetry();
                    $cron_retry_obj->saveNewRecord($retry_array);
                    unset($cron_retry_obj);
                }
            }
        }

        return [
            'process_count' => $ogc_process_count,
            'last_pin_id' => $last_pin_id,
        ];
    }

    private function mgcPinInsert($mgc_pins, $retry_mode = false) {
        $mgc_process_count = 0;
        
        $main_model = new \backend\models\DimPin();
        $mgc_com_obj = new \backend\components\MgcReportCom();
        $mgc_model = new \backend\modules\cronjob\models\MgcModel();

        foreach ($mgc_pins as $idx => $mgc_pin) {
            $dim_pin_mgc_transaction_obj = new \backend\models\DimPinMgcTransaction();
            $dim_geo_info_obj = new \backend\models\DimGeoInfo();
            $fact_pin_mgc_redemption_obj = new \backend\models\FactPinMgcRedemption();

            $dim_pin_mgc_merchant = [];
            $dim_pin_mgc_transaction = [];
            $dim_geo_info = [];
            $fact_pin_mgc_redemption = [];

            $dim_pin_mgc_merchant['id'] = $mgc_pin['merchant_id'];
            $dim_pin_mgc_merchant['name'] = $mgc_pin['merchant_name'];

            $pmm_id = $mgc_com_obj->getDimPinMgcMerchantPMMID($dim_pin_mgc_merchant['id'], $dim_pin_mgc_merchant);
            $dim_date = $this->getDimDateFromTimeStamp(date('Y-m-d', strtotime($mgc_pin['trans_complete_ts'])));
            $date_id = $mgc_com_obj->getDimDateDATEID($dim_date['date'], $dim_date);

            $dim_pin_mgc_transaction['id'] = $mgc_pin['trans_id'];
            $dim_pin_mgc_transaction['type'] = $mgc_pin['trans_method'];
            $dim_pin_mgc_transaction['ref_id'] = $mgc_pin['merchant_ref_id'];
            $dim_pin_mgc_transaction['currency'] = $mgc_pin['trans_currency'];
            $dim_pin_mgc_transaction['datetime'] = $mgc_pin['trans_complete_ts'];

            $pmt_id = $dim_pin_mgc_transaction_obj->doUpdateIfExists('pmt_id', $dim_pin_mgc_transaction['id'], $dim_pin_mgc_transaction);

            if ($dim_geo_info['ip'] = $mgc_model->getRequestLogIP($mgc_pin['trans_id'], $mgc_pin['game_card_serial'])) {
                $dim_geo_info['ip_country_iso2'] = '-';
                
                if ($ip_country_iso2 = Yii::$app->geoip->lookupCountryCode($dim_geo_info['ip'])) {
                    $dim_geo_info['ip_country_iso2'] = $ip_country_iso2;
                }
            } else {
                $dim_geo_info['ip'] = '-';
                $dim_geo_info['ip_country_iso2'] = '-';
            }
            
            $gi_id = $dim_geo_info_obj->doUpdateIfExists('gi_id', ['ip' => $dim_geo_info['ip']], $dim_geo_info);

            $mgc_process_count += 1;
            $fpmr_pin_id = $main_model->getPinIdBySerialNo($mgc_pin['game_card_serial']);
            
            if ($fpmr_pin_id) {
                if(\backend\models\FactPinMgcRedemption::find()->where('fpmr_pin_id=:fpmr_pin_id', [
                        ':fpmr_pin_id' => $fpmr_pin_id,
                        ])->exists() === false) {

                    $fact_pin_mgc_redemption['fpmr_pin_id'] = $fpmr_pin_id;
                    $fact_pin_mgc_redemption['fpmr_pmm_id'] = $pmm_id;
                    $fact_pin_mgc_redemption['fpmr_gi_id'] = $gi_id;
                    $fact_pin_mgc_redemption['fpmr_pmt_id'] = $pmt_id;
                    $fact_pin_mgc_redemption['fpmr_date_id'] = $date_id;
                    $fact_pin_mgc_redemption['pin_value'] = $mgc_pin['redeem_amount'];
                    $fact_pin_mgc_redemption_obj->saveNewRecord($fact_pin_mgc_redemption, true);

                    if (!$fact_pin_mgc_redemption_obj->primaryKey) {
                        if ($retry_mode) {
                            \backend\models\CronRetry::updateRerunCounter($idx);
                        } else {
                            $retry_array = [
                                'action' => 'RERUN',
                                'task' => 'MGC_REDEEM',
                                'key_1' => 'error-type',
                                'val_1' => 1,
                                'key_2' => 'rerun-counter',
                                'val_2' => 1,
                                'json_str' => json_encode($mgc_pin),
                                'create_datetime' => new Expression('NOW()')
                            ];

                            $cron_retry_obj = new \backend\models\CronRetry();
                            $cron_retry_obj->saveNewRecord($retry_array, true);

                            if ($cron_retry_obj->primaryKey) {
                                $this->createCronLogs([
                                    'log_time' => date('Y-m-d H:i:s'),
                                    'log_user_messages' => 'Error: Insert DB',
                                    'log_system_messages' => json_encode($fact_pin_mgc_redemption),
                                    'log_field_name' => 'mgcPinInsert:rerun-id',
                                    'log_from_value' => (string) $cron_retry_obj->primaryKey,
                                    'log_to_value' => '1'
                                ]);
                            }

                            unset($cron_retry_obj);
                        }
                    } else if ($retry_mode) {
                        \backend\models\CronRetry::deleteRerunByID($idx);
                    }
                } else if ($retry_mode) {
                    \backend\models\CronRetry::deleteRerunByID($idx);
                }
            } else {
                if ($retry_mode) {
                    \backend\models\CronRetry::updateRerunCounter($idx);
                } else {
                    $retry_array = [
                        'action' => 'RERUN',
                        'task' => 'MGC_REDEEM',
                        'key_1' => 'error-type',
                        'val_1' => 2,
                        'key_2' => 'rerun-counter',
                        'val_2' => 1,
                        'json_str' => json_encode($mgc_pin),
                        'create_datetime' => new Expression('NOW()')
                    ];

                    $cron_retry_obj = new \backend\models\CronRetry();
                    $cron_retry_obj->saveNewRecord($retry_array);
                    unset($cron_retry_obj);
                }
            }
        }

        return [
            'process_count' => $mgc_process_count,
        ];
    }

    public function actionMgcCronJob() {
        $cron_process_track_filename = 'cronjob/task/redeemreport';
        $cron_process_checking_row = $this->cronProcessCheck($cron_process_track_filename);

        if ($cron_process_checking_row->cron_process_track_in_action == '0') {
            $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
            $cron_process_track_obj->cron_process_track_in_action = 1;
            $cron_process_track_obj->cron_process_track_start_date = new Expression('NOW()');
            $cron_process_track_obj->cron_process_track_failed_attempt = 0;
            $cron_process_track_obj->save();

            $cronRetry_obj = new \backend\models\CronRetry();
            
            # ============================================  OGC ============================================
            # OGC retry
            if ($get_retry_array = $cronRetry_obj->getDataByTask('MGC_PIN', 3)) {
                $this->ogcPinInsert($get_retry_array, true);
            }
            
            # OGC
            $cron_pin_record_row = $this->getCronPointer('pin');
            $new_ogc_start_id = $cron_pin_record_row->new_start_id;
            $last_ogc_pointer = $next_ogc_pointer = (string) $new_ogc_start_id;
            $total_ogc_process_count = 0;

            $og_model = new \backend\modules\cronjob\models\OgcModel();
            $ogc_pins = $og_model->getOgcPins($new_ogc_start_id);
            $return_array = $this->ogcPinInsert($ogc_pins);

            if ($return_array['last_pin_id']) {
                $last_ogc_pointer = (string) $return_array['last_pin_id'];
                $next_ogc_pointer = (string) ($return_array['last_pin_id'] + 1);

                $total_ogc_process_count = $return_array['process_count'];
            }

            if ($total_ogc_process_count) {
                $cron_pointer_obj = CronPointer::findOne($cron_pin_record_row->id);
                $cron_pointer_obj->new_start_id = (string) $next_ogc_pointer;
                $cron_pointer_obj->cron_last_process_date = new Expression('NOW()');
                $cron_pointer_obj->cron_last_processed_count = $total_ogc_process_count;
                $cron_pointer_obj->save();
                unset($cron_pointer_obj);
            }

            # ============================================  MGC ============================================
            # MGC retry
            if ($get_retry_array = $cronRetry_obj->getDataByTask('MGC_REDEEM', 10)) {
                $this->mgcPinInsert($get_retry_array, true);
            }
            
            # MGC
            $cron_redeem_transaction_record_row = $this->getCronPointer('redeem_transaction');
            $new_mgc_start_id = $cron_redeem_transaction_record_row->new_start_id;
            $last_mgc_pointer = $next_mgc_pointer = $new_mgc_start_id;
            $total_mgc_process_count = 0;

            if ($new_mgc_start_id != date('Ymd' . '0000000001')) {
                $last_mgc_pointer = $this->transIdToDateTime($new_mgc_start_id);
                $next_mgc_pointer = bcadd($last_mgc_pointer, 1);

                $mgc_model = new \backend\modules\cronjob\models\MgcModel();
                $mgc_pins = $mgc_model->getMgcPins($new_mgc_start_id, $last_mgc_pointer);
        
                $return_array = $this->mgcPinInsert($mgc_pins);
                $total_mgc_process_count = $return_array['process_count'];
            }

            # no need to depend on total data process count
            $cron_pointer_obj = CronPointer::findOne($cron_redeem_transaction_record_row->id);
            $cron_pointer_obj->new_start_id = (string) $next_mgc_pointer;
            $cron_pointer_obj->cron_last_process_date = new Expression('NOW()');
            $cron_pointer_obj->cron_last_processed_count = $total_mgc_process_count;
            $cron_pointer_obj->save();

            $cron_process_track_obj->cron_process_track_in_action = 0;
            $cron_process_track_obj->save();
            
            unset($cronRetry_obj, $cron_pointer_obj, $cron_process_track_obj);
        } else {
            if ($cron_process_checking_row->overdue_process == '1') {
                $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
                $cron_process_track_obj->cron_process_track_failed_attempt = $cron_process_checking_row->cron_process_track_failed_attempt + 1;
                $cron_process_track_obj->cron_process_track_start_date = new Expression('NOW()');
                $cron_process_track_obj->save();
                unset($cron_process_track_obj);
                $this->cronFailedMaxAttempt('[OFFGAMERS-REPORT-MODULE] Cronjob Failed', 'MGC cronjob failed at ' . $cron_process_checking_row->cron_process_track_start_date, $cron_process_checking_row);
            }
        }
    }

    private function cronProcessCheck($cron_process_track_filename) {
        $cron_process_checking_row = CronProcessTrack::find()
                ->select(['id', 'cron_process_track_in_action', 'cron_process_track_start_date', 'cron_process_track_failed_attempt', 'cron_process_track_filename',
                    'cron_process_track_start_date< DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS overdue_process'
                ])
                ->where('cron_process_track_filename=:cron_process_track_filename', [':cron_process_track_filename' => $cron_process_track_filename])
                ->one();
        return $cron_process_checking_row;
    }

    public function actionGpCronJob() {
        $cron_process_track_filename = 'cronjob/task/gpcronjob';
        $cron_process_checking_row = $this->cronProcessCheck($cron_process_track_filename);

        if ($cron_process_checking_row->cron_process_track_in_action == '0') {
            $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
            $cron_process_track_obj->cron_process_track_in_action = 1;
            $cron_process_track_obj->cron_process_track_start_date = new Expression('NOW()');
            $cron_process_track_obj->cron_process_track_failed_attempt = 0;
            $cron_process_track_obj->save();

            $cronRetry_obj = new \backend\models\CronRetry();
            
            # ============================================  GP ============================================
            # GP retry
            if ($get_retry_array = $cronRetry_obj->getDataByTask('GP', 5, 3)) {
                $this->gpInsert($get_retry_array, true);
            }
            
            $gp_cron_pointer = $this->getCronPointer('log_delivered_products');
            $new_gp_start_id = $gp_cron_pointer->new_start_id;
            $next_gp_pointer = $new_gp_start_id;

            $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
            $gp_list = $ogm_model->getPoList($new_gp_start_id);
            $return_array = $this->gpInsert($gp_list);

            if ($return_array['last_id'] != '') {
                $next_gp_pointer = (string) ($return_array['last_id'] + 1);
            }

            $cron_pointer_obj = CronPointer::findOne($gp_cron_pointer->id);
            $cron_pointer_obj->new_start_id = (string) $next_gp_pointer;
            $cron_pointer_obj->cron_last_process_date = new Expression('NOW()');
            $cron_pointer_obj->cron_last_processed_count = $return_array['gp_process_count'];
            $cron_pointer_obj->save();
            
            $cron_process_track_obj->cron_process_track_in_action = 0;
            $cron_process_track_obj->save();
            
            unset($cronRetry_obj, $cron_pointer_obj, $cron_process_track_obj);
        } else {
            if ($cron_process_checking_row->overdue_process == '1') {
                $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
                $cron_process_track_obj->cron_process_track_failed_attempt = $cron_process_checking_row->cron_process_track_failed_attempt + 1;
                $cron_process_track_obj->cron_process_track_start_date = new Expression('NOW()');
                $cron_process_track_obj->save();
                $this->cronFailedMaxAttempt('[OFFGAMERS-REPORT-MODULE] Cronjob Failed', 'GP cronjob failed at ' . $cron_process_checking_row->cron_process_track_start_date, $cron_process_checking_row);
            }
        }
    }
    
    public function actionGpReleasedCronJob() {
        $cron_process_track_filename = 'cronjob/task/gpreleasedcronjob';
        $cron_process_checking_row = $this->cronProcessCheck($cron_process_track_filename);

        if ($cron_process_checking_row->cron_process_track_in_action == '0') {
            $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
            $cron_process_track_obj->cron_process_track_in_action = 1;
            $cron_process_track_obj->cron_process_track_start_date = new Expression('NOW()');
            $cron_process_track_obj->cron_process_track_failed_attempt = 0;
            $cron_process_track_obj->save();

            $cronRetry_obj = new \backend\models\CronRetry();
            
            # ============================================  GP ============================================
            # GP retry
            if ($get_retry_array = $cronRetry_obj->getDataByTask('GP_RELEASE', 1, 3)) {
                $this->gpInsertReleased($get_retry_array, true);
            }
            
            $gp_cron_pointer = $this->getCronPointer('log_delivered_released');
            $new_gp_start_id = $gp_cron_pointer->new_start_id;
            $next_gp_pointer = $new_gp_start_id;

            $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
            $gp_list = $ogm_model->getPoListReleased($new_gp_start_id);
            $return_array = $this->gpInsertReleased($gp_list);
            
            if ($return_array['last_id'] != '') {
                $next_gp_pointer = (string) ($return_array['last_id'] + 1);
            }

            # gp logs
            $cron_pointer_obj = CronPointer::findOne($gp_cron_pointer->id);
            $cron_pointer_obj->new_start_id = (string) $next_gp_pointer;
            $cron_pointer_obj->cron_last_process_date = new Expression('NOW()');
            $cron_pointer_obj->cron_last_processed_count = $return_array['gp_process_count'];
            $cron_pointer_obj->save();

            $cron_process_track_obj->cron_process_track_in_action = 0;
            $cron_process_track_obj->save();
            
            unset($cronRetry_obj, $cron_pointer_obj, $cron_process_track_obj);
        } else {
            if ($cron_process_checking_row->overdue_process == '1') {
                $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
                $cron_process_track_obj->cron_process_track_failed_attempt = $cron_process_checking_row->cron_process_track_failed_attempt + 1;
                $cron_process_track_obj->save();
                $this->cronFailedMaxAttempt('[OFFGAMERS-REPORT-MODULE] Cronjob Failed', 'GP Released cronjob failed at ' . $cron_process_checking_row->cron_process_track_start_date, $cron_process_checking_row);
            }
        }
    }

    private function capture4rerun($type, $idx, $retry_mode, $rerunData, $LogData, $notification_com = null) {
        $sendNotification = false;
        list($error_id, $error_msg, $json) = $LogData;
        
        if ($retry_mode) {
            $rerun_obj = \backend\models\CronRetry::findOne($idx);
            
            if (isset($rerun_obj->val_2)) {
                \backend\models\CronRetry::updateRerunCounter($idx);
                
               if ($rerun_obj->val_2 > 0) {
                    $notification_com = null;
               }
            }
            // set send notification to true is retry more or equal 3 times
            $sendNotification = ($rerun_obj->val_2 >= 3) ? true : false;
        } else {
            $retry_array = [
                'action' => 'RERUN',
                'task' => $type,
                'key_1' => 'error-type',
                'val_1' => (int) $error_id,
                'key_2' => 'rerun-counter',
                'val_2' => 1,
                'json_str' => json_encode($rerunData),
                'create_datetime' => new Expression('NOW()')
            ];

            $cron_retry_obj = new \backend\models\CronRetry();
            $cron_retry_obj->saveNewRecord($retry_array, true);
            $rerun_id = $cron_retry_obj->primaryKey ? $cron_retry_obj->primaryKey : '-';
            
            $this->createCronLogs([
                'log_time' => date('Y-m-d H:i:s'),
                'log_user_messages' => 'Error: ' .$type . ' - ' . $error_msg,
                'log_system_messages' => json_encode($json),
                'log_field_name' => 'CronRetry:rerun-id',
                'log_from_value' => (string) $rerun_id,
                'log_to_value' => (string) $rerunData['id']
            ]);
            
            unset($retry_array, $cron_retry_obj);
        }
        
        if ($notification_com && $sendNotification) {
            $missing_data_str = '';

            switch ($error_id) {
                case 1001:
                    // Check if order data missing?
                    if (isset($json[$rerunData['orders_id']]['order'])) {
                        $missing_data_str .= "\n";
                        foreach ($json[$rerunData['orders_id']]['order'] as $key => $value) {
                            $missing_data_str .= '- order > ' . str_replace("_", " ", $key) . "\n";
                        }
                    }
                    // Check if cost data missing?
                    if (isset($json[$rerunData['orders_id']]['cost'])) {
                        $missing_data_str .= "\n";
                        foreach ($json[$rerunData['orders_id']]['cost'] as $key => $value) {
                            $missing_data_str .= '- cost > ' . str_replace("_", " ", $key) . "\n";
                        }
                    }
                    break;
                case 1002:
                case 1003:
                    $missing_data_str = $error_msg;
                    break;
            }

            if ($missing_data_str) {
                $notification_com->scanMissingData([
                    'order_id' => isset($rerunData['orders_id']) ? $rerunData['orders_id'] : '-',
                    'products_id' => isset($rerunData['products_id']) ? $rerunData['products_id'] : '-',
                    'products_name' => isset($rerunData['products_name']) ? $rerunData['products_name'] : '-',
                    'missing_data_str' => $missing_data_str,
                    'report_type' => $type,
                ]);
            }
        }
    }

    private function cronFailedMaxAttempt($subject, $response_data, $cron_process_checking_row) {
        if ($cron_process_checking_row->cron_process_track_failed_attempt < 3) {
            //send email
            $this->errorReport($subject, $response_data);
        } else {
            $cron_process_track_obj = CronProcessTrack::findOne($cron_process_checking_row->id);
            $cron_process_track_obj->cron_process_track_in_action = 0;
            $cron_process_track_obj->cron_process_track_failed_attempt = 0;
            $cron_process_track_obj->save();
            unset($cron_process_track_obj);
        }
    }

    private function getCronPointer($system_table) {
        $cron_pointer = CronPointer::find()
                ->select(['id', 'new_start_id'])
                ->where('system_table=:system_table', [':system_table' => $system_table])
                ->one();
        return $cron_pointer;
    }

    private function getCostInfo($extra_info, $delivery_log_data, $order_currency) {
        $currency_com = new CurrenciesCom();
        $softpin_array = [];
        $total_purchase_in_purchase_currency = 0;
        $total_purchase_in_order_currency = 0;
        $total_purchase_in_usd = 0;

        $order_pending_date = $delivery_log_data['first_date'];
        $order_delivery_date = $delivery_log_data['log_date_time'];
        $gp_row_id = $delivery_log_data['id'];
        $products_id = $delivery_log_data['products_id'];
        $order_id = $delivery_log_data['orders_id'];
        $order_quantity = (isset($delivery_log_data['products_quantity'])) ? $delivery_log_data['products_quantity'] : $delivery_log_data['released_quantity'];

        if (isset($extra_info['SOFTPIN'])) {
            $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
            $gp_obj_entity = new \backend\models\DimGpEntity();

            $temp_array = $extra_info['SOFTPIN']['po_id'];
            $is_pin = false;

            if (isset($delivery_log_data['products_flag_id']) && $delivery_log_data['products_flag_id']) {
                $product_flag_arr = explode(',', $delivery_log_data['products_flag_id']);

                foreach ($product_flag_arr as $key) {
                    if (trim($key) == '4') {
                        $is_pin = true;
                        break;
                    }
                }
            } else if (isset($delivery_log_data['products_model']) && strpos($delivery_log_data['products_model'], 'MGC_') === 0) {
                $is_pin = true;
            }

            if ($is_pin) {
                $temp_array2 = [];

                foreach ($temp_array as $cd_key_array) {
                    foreach ($cd_key_array['cdkey_id'] as $cd_key_id) {
                        $temp_array2[] = $cd_key_id;
                    }
                }

                $return_array = $this->processFlatCostInfo('PIN', $products_id, $temp_array2, $order_pending_date, $order_currency);
            } else {
                foreach ($temp_array as $po_id => $cd_key) {
                    if (!empty($po_id)) {
                        $total_cost_in_purchase_currency = 0;
                        $total_cost_in_order_currency = 0;
                        $total_cost_in_usd = 0;

                        $po_quantity_delivered = count($cd_key['cdkey_id']);
                        $purchase_currency = $ogm_model->getPoCurrency($po_id);

                        // Get PO Order Original Entity
                        $purchase_entity = $ogm_model->getPoEntity($po_id);
                        // Check if PO is from 2017
                        $moved_entity = $ogm_model->checkEntityMovement($po_id, $order_delivery_date);
                        if ($moved_entity) {
                            $moved_entity_sg = \backend\models\DimGpEntity::find()->select('gpe_id')->where(['id' => 'SG'])->orderBy('gpe_id DESC')->one();
                            $gp_entity = $moved_entity_sg->gpe_id;
                        } else {
                            $gp_entity = $gp_obj_entity->checkRecordAndUpdateVersionNotCaseSensitive($purchase_entity['id'], 'gpe_id', $purchase_entity);
                        }
                        
                        $purchase_conversion_rate_in_order_currency = $currency_com->advanceCurrencyConversionRate($purchase_currency, $order_currency, $order_pending_date);
                        $purchase_unit_price_in_purchase_currency = $ogm_model->getPoUnitPrice($po_id, $products_id);

                        // get PO Cost Follow CO Currency
                        $purchase_unit_price_in_order_currency = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_purchase_currency, $purchase_currency, $order_currency, $order_pending_date);

                        $unit_cost_in_usd = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_order_currency, $order_currency, 'USD', $order_pending_date);

                        $total_cost_in_purchase_currency = $currency_com->displayFormat($purchase_unit_price_in_purchase_currency * $po_quantity_delivered, $purchase_currency, false);
                        $total_cost_in_order_currency = ($purchase_unit_price_in_order_currency * $po_quantity_delivered);
                        $total_cost_in_usd = ($unit_cost_in_usd * $po_quantity_delivered);

                        $total_purchase_in_purchase_currency += $total_cost_in_purchase_currency;
                        $total_purchase_in_order_currency += $total_cost_in_order_currency;
                        $total_purchase_in_usd +=$total_cost_in_usd;

                        $softpin_array[] = [
                            'purchase_id' => $po_id,
                            'purchase_type' => 'PO',
                            'po_entity' => $gp_entity,
                            'delivered_quantity' => $po_quantity_delivered,
                            'po_currency' => $purchase_currency,
                            'po_spot_rate' => $purchase_conversion_rate_in_order_currency,
                            'unit_cost_in_purchase_currency' => $purchase_unit_price_in_purchase_currency,
                            'unit_cost_in_order_currency' => $purchase_unit_price_in_order_currency,
                            'unit_cost_in_usd' => $unit_cost_in_usd,
                            'total_cost_in_purchase_currency' => $total_cost_in_purchase_currency,
                            'total_cost_in_order_currency' => $total_cost_in_order_currency,
                            'total_cost_in_usd' => $total_cost_in_usd,
                            'cdkey_id' => $cd_key['cdkey_id']
                        ];
                    } else {
                        # failed to retrieve PO data
                        $softpin_array = [];
                        break;
                    }
                }

                $return_array = [
                    $softpin_array,
                    $total_purchase_in_purchase_currency,
                    $total_purchase_in_order_currency,
                    $total_purchase_in_usd
                ];
            }
        }

        if (isset($extra_info['API'])) {
            $temp_array = $extra_info['API']['custom_products_code_id'];
            $return_array = $this->processCostInfo('API', 'id', $temp_array, $order_pending_date, $order_currency, $products_id, $order_quantity);
        }

        if (isset($extra_info['DTU'])) {
            $temp_array = $extra_info['DTU']['top_up_id'];
            $return_array = $this->processCostInfo('DTU', 'top_up_id', $temp_array, $order_pending_date, $order_currency, $products_id, $order_quantity);
        }

        if (isset($extra_info['PHYSICAL_GOODS'])) {
            $temp_array = $extra_info['PHYSICAL_GOODS']['id'];
            $return_array = $this->processCostInfo('PHY', 'id', $temp_array, $order_pending_date, $order_currency, $products_id, $order_quantity);
        }

        return $return_array;
    }

    private function processCostInfo($type, $key, $extra_info, $order_pending_date, $order_currency, $products_id, $product_quantity) {
        $currency_com = new CurrenciesCom();
        $softpin_array = [];
        $total_purchase_in_purchase_currency = 0;
        $total_purchase_in_order_currency = 0;
        $total_purchase_in_usd = 0;
        $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
        $gp_obj_entity = new \backend\models\DimGpEntity();

        // Get API provider as PO Entity
        $tmp_entity = $ogm_model->getEntity($products_id);
        $gp_entity = $gp_obj_entity->checkRecordAndUpdateVersionNotCaseSensitive($tmp_entity['id'], 'gpe_id', $tmp_entity);
        
        $method = 'get' . $type . 'CurrencyCost';
        foreach ($extra_info as $cd_key_id) {
            $total_cost_in_purchase_currency = 0;
            $total_cost_in_order_currency = 0;
            $total_cost_in_usd = 0;

            $api_quantity_delivered = ($type == 'API') ? 1 : $product_quantity;

            $currency_cost = $ogm_model->$method($cd_key_id);

            $purchase_currency = isset($currency_cost['currency_code']) ? $currency_cost['currency_code'] : '';
            $purchase_conversion_rate_in_order_currency = $currency_com->advanceCurrencyConversionRate($purchase_currency, $order_currency, $order_pending_date);
            $purchase_unit_price_in_purchase_currency = isset($currency_cost['currency_settle_amount']) ? $currency_cost['currency_settle_amount'] : 0;

            $purchase_unit_price_in_order_currency = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_purchase_currency, $purchase_currency, $order_currency, $order_pending_date);

            $unit_cost_in_usd = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_order_currency, $order_currency, 'USD', $order_pending_date);

            $total_cost_in_purchase_currency = ($purchase_unit_price_in_purchase_currency * $api_quantity_delivered);
            $total_cost_in_order_currency = ($purchase_unit_price_in_order_currency * $api_quantity_delivered);
            $total_cost_in_usd = ($unit_cost_in_usd * $api_quantity_delivered);

            $total_purchase_in_purchase_currency += $total_cost_in_purchase_currency;
            $total_purchase_in_order_currency += $total_cost_in_order_currency;
            $total_purchase_in_usd += $total_cost_in_usd;

            $softpin_array[] = [
                'purchase_id' => isset($currency_cost[$key]) ? $currency_cost[$key] : 0,
                'purchase_type' => $type,
                'po_entity' => $gp_entity,
                'delivered_quantity' => $api_quantity_delivered,
                'po_currency' => $purchase_currency,
                'po_spot_rate' => $purchase_conversion_rate_in_order_currency,
                'unit_cost_in_purchase_currency' => $purchase_unit_price_in_purchase_currency,
                'unit_cost_in_order_currency' => $purchase_unit_price_in_order_currency,
                'unit_cost_in_usd' => $unit_cost_in_usd,
                'total_cost_in_purchase_currency' => $total_cost_in_purchase_currency,
                'total_cost_in_order_currency' => $total_cost_in_order_currency,
                'total_cost_in_usd' => $total_cost_in_usd,
                'cdkey_id' => $cd_key_id
            ];
        }

        return [
            $softpin_array,
            $total_purchase_in_purchase_currency,
            $total_purchase_in_order_currency,
            $total_purchase_in_usd
        ];
    }

    private function processFlatCostInfo($type, $id, $cd_key_array, $order_pending_date, $order_currency) {
        $currency_com = new CurrenciesCom();
        $softpin_array = [];
        $total_purchase_in_purchase_currency = 0;
        $total_purchase_in_order_currency = 0;
        $total_purchase_in_usd = 0;
        $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
        $gp_obj_entity = new \backend\models\DimGpEntity();

        $method = 'get' . $type . 'CurrencyCost';

        $api_quantity_delivered = count($cd_key_array);
        $currency_cost = $ogm_model->$method($id);

        // Get PIN provider as PO Entity
        $tmp_entity = $ogm_model->getEntity($id);
        $gp_entity = $gp_obj_entity->checkRecordAndUpdateVersionNotCaseSensitive($tmp_entity['id'], 'gpe_id', $tmp_entity);

        $purchase_currency = isset($currency_cost['currency_code']) ? $currency_cost['currency_code'] : '';
        $purchase_conversion_rate_in_order_currency = $currency_com->advanceCurrencyConversionRate($purchase_currency, $order_currency, $order_pending_date);
        $purchase_unit_price_in_purchase_currency = isset($currency_cost['currency_settle_amount']) ? $currency_cost['currency_settle_amount'] : 0;

        $purchase_unit_price_in_order_currency = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_purchase_currency, $purchase_currency, $order_currency, $order_pending_date);

        $unit_cost_in_usd = $currency_com->advanceCurrencyConversion($purchase_unit_price_in_order_currency, $order_currency, 'USD', $order_pending_date);

        $total_cost_in_purchase_currency = ($purchase_unit_price_in_purchase_currency * $api_quantity_delivered);
        $total_cost_in_order_currency = ($purchase_unit_price_in_order_currency * $api_quantity_delivered);
        $total_cost_in_usd = ($unit_cost_in_usd * $api_quantity_delivered);

        $total_purchase_in_purchase_currency += $total_cost_in_purchase_currency;
        $total_purchase_in_order_currency += $total_cost_in_order_currency;
        $total_purchase_in_usd += $total_cost_in_usd;

        $softpin_array[] = [
            'purchase_id' => $id,
            'purchase_type' => $type,
            'po_entity' => $gp_entity,
            'delivered_quantity' => $api_quantity_delivered,
            'po_currency' => $purchase_currency,
            'po_spot_rate' => $purchase_conversion_rate_in_order_currency,
            'unit_cost_in_purchase_currency' => $purchase_unit_price_in_purchase_currency,
            'unit_cost_in_order_currency' => $purchase_unit_price_in_order_currency,
            'unit_cost_in_usd' => $unit_cost_in_usd,
            'total_cost_in_purchase_currency' => $total_cost_in_purchase_currency,
            'total_cost_in_order_currency' => $total_cost_in_order_currency,
            'total_cost_in_usd' => $total_cost_in_usd,
            'cdkey_id' => $cd_key_array
        ];

        return [
            $softpin_array,
            $total_purchase_in_purchase_currency,
            $total_purchase_in_order_currency,
            $total_purchase_in_usd
        ];
    }

    private function gpInsert($gp_list, $retry_mode = false) {
        $gp_obj = new \backend\models\GpOrder();
        $po_obj = new \backend\models\GpCost();
        $currency_com = new CurrenciesCom();
        $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
        $general_com = new GeneralCom();
        $notification_com = new \backend\modules\cronjob\components\NotificationCom();
        $gp_process_count = 0;

        foreach ($gp_list as $idx => $gp_row) {
            $gp_array = [];
            $extra_info = json_decode($gp_row['extra_info'], true);
            $order_unit_price_in_usd_sell_rate = isset($extra_info['final_price']) ? $extra_info['final_price'] : 0;

            // get Order Currency
            $order_currency = $gp_row['currency'];

            $order_conversion_rate_in_usd = $currency_com->advanceCurrencyConversionRate('USD', $order_currency, $gp_row['first_date']);
            $order_unit_price_in_order_currency = $order_unit_price_in_usd_sell_rate * $gp_row['currency_value'];
            $total_delivered_quantity = $gp_row['products_quantity'];
            $total_order_in_order_currency = $order_unit_price_in_order_currency * $total_delivered_quantity;
            $total_order_in_usd = $currency_com->advanceCurrencyConversion($total_order_in_order_currency, $order_currency, 'USD', $gp_row['first_date']);

            list($softpin_array, $total_purchase_in_purchase_currency, $total_purchase_in_order_currency, $total_purchase_in_usd) = $this->getCostInfo($extra_info, $gp_row, $order_currency);

            if ($softpin_array) {
                $order_margin_in_order_currency = $total_order_in_order_currency - $total_purchase_in_order_currency;

                if ($total_purchase_in_order_currency > 0) {
                    $margin_percentage_in_order_currency = $total_order_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_order_in_order_currency * 100) : -100;
                } else {
                    $margin_percentage_in_order_currency = $total_order_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_order_in_order_currency * 100) : 0;
                }

                // Get Payment methods (parent - pm) title
                $payment_methods_title = '';
                if (!empty($gp_row['payment_method'])) {
                    if (isset($gp_row['payment_methods_parent_id']) && !empty($gp_row['payment_methods_parent_id'])) {
                        $payment_methods_title = $ogm_model->getPaymentMethodsTitle($gp_row['payment_methods_parent_id'], $gp_row['payment_methods_id']);
                    }
                } else {
                    $payment_methods_title = 'Store Credit';
                }

                $gp_array['order_pending_datetime'] = $gp_row['first_date'];
                $gp_array['delivery_datetime'] = $gp_row['log_date_time'];
                $gp_array['order_id'] = $gp_row['orders_id'];
                $gp_array['payment_method'] = $payment_methods_title;
                $gp_array['products_id'] = $gp_row['products_id'];
                $gp_array['product_name'] = $gp_row['products_name'];

                # rename to order_currency
                $gp_array['order_currency'] = $order_currency;
                # rename to order_conversion_rate_in_usd
                $gp_array['order_conversion_rate_in_usd'] = $order_conversion_rate_in_usd;

                $gp_array['unit_price_in_order_currency'] = $order_unit_price_in_order_currency;
                $gp_array['total_delivered_quantity'] = $total_delivered_quantity;


                $gp_array['total_order_in_order_currency'] = $total_order_in_order_currency;

                $gp_array['total_purchase_in_purchase_currency'] = $total_purchase_in_purchase_currency;
                # new add total_purchase_in_order_currency
                $gp_array['total_purchase_in_order_currency'] = $total_purchase_in_order_currency;

                $gp_array['margin_in_order_currency'] = $order_margin_in_order_currency;
                $gp_array['margin_percentage_in_order_currency'] = $margin_percentage_in_order_currency;


                $gp_array['total_order_in_usd'] = $currency_com->displayFormat($total_order_in_usd, 'USD', false);
                $gp_array['total_purchase_in_usd'] = $currency_com->displayFormat($total_purchase_in_usd, 'USD', false);

                $gp_array['margin_in_usd'] = $total_order_in_usd - $total_purchase_in_usd;

                if ($total_purchase_in_usd > 0) {
                    $gp_array['margin_percentage_in_usd'] = $gp_array['total_order_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_order_in_usd'] * 100) : -100;
                } else {
                    $gp_array['margin_percentage_in_usd'] = $gp_array['total_order_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_order_in_usd'] * 100) : 0;
                }

                $check_gp_array_all_values = $general_com->hasAnyEmptyValueInArray($gp_array);
                $check_po_array_all_values = $general_com->hasAnyEmptyValueInArray($softpin_array);

                if (count($check_gp_array_all_values) || count($check_po_array_all_values)) {
                    $failed_gp_order_array = [];
                    
                    if (count($check_gp_array_all_values)) {
                        $failed_gp_order_array[$gp_row['orders_id']]['order'] = $check_gp_array_all_values;
                    }

                    if (count($check_po_array_all_values)) {
                        $failed_gp_order_array[$gp_row['orders_id']]['cost'] = $check_po_array_all_values;
                    }
                    
                    # create re-run
                    $this->capture4rerun('GP', $idx, $retry_mode, $gp_row, [1001, 'Missing Data', $failed_gp_order_array], $notification_com);
                } else {
                    $gp_record_new = $gp_obj->saveNewRecord($gp_array, true);
                    $gp_order_id = isset($gp_record_new->id) ? $gp_record_new->id : (is_object($gp_record_new) ? 0 : $gp_record_new);
                    
                    if ($gp_order_id) {
                        foreach ($softpin_array as $cd_key2) {
                            $po_array = [];
                            $po_array['gp_order_id'] = $gp_order_id;

                            // purchase_order_entitiy
                            $po_array['gpc_gpe_id'] = $cd_key2['po_entity'];

                            $po_array['delivered_quantity'] = $cd_key2['delivered_quantity'];
                            $po_array['purchase_type'] = $cd_key2['purchase_type'];
                            $po_array['purchase_id'] = $cd_key2['purchase_id'];
                            $po_array['purchase_currency'] = $cd_key2['po_currency'];

                            # purchase_conversion_rate_in_order_currency
                            $po_array['purchase_conversion_rate_in_order_currency'] = $cd_key2['po_spot_rate'];
                            # purchase_conversion_rate_in_usd
                            $po_array['purchase_conversion_rate_in_usd'] = $currency_com->advanceCurrencyConversionRate($cd_key2['po_currency'], 'USD', $gp_row['first_date']);

                            $po_array['unit_cost_in_purchase_currency'] = $cd_key2['unit_cost_in_purchase_currency'];
                            $po_array['unit_cost_in_order_currency'] = $cd_key2['unit_cost_in_order_currency'];
                            # unit_cost_in_usd
                            $po_array['unit_cost_in_usd'] = $cd_key2['unit_cost_in_usd'];

                            # total_cost_in_purchase_currency
                            $po_array['total_cost_in_purchase_currency'] = $cd_key2['total_cost_in_purchase_currency'];
                            # total_cost_in_order_currency
                            $po_array['total_cost_in_order_currency'] = $cd_key2['total_cost_in_order_currency'];
                            # total_cost_in_usd
                            $po_array['total_cost_in_usd'] = $cd_key2['total_cost_in_usd'];

                            $po_array['extra_info'] = json_encode($cd_key2['cdkey_id']);

                            // Additional checking for partial store credit payment
                            $sc_per_cost = 0;
                            $sc_in_usd_per_cost = 0;
                            $pay_amount_in_usd_per_cost = 0;

                            // Calculate price per item
                            $pay_amount_per_cost = ($total_delivered_quantity > 0 ? $total_order_in_order_currency / $total_delivered_quantity : 0);
                            $pay_amount_per_cost = $pay_amount_per_cost * $cd_key2['delivered_quantity'];

                            // get store credit amount
                            $sc_amount = $ogm_model->getScAmount($gp_row['orders_id']);
                            
                            if ($sc_amount > 0) {
                                $sc_amount_ratio = $ogm_model->getBundleRatio($gp_array['order_id'], $gp_array['products_id'], $gp_row['first_date'], $currency_com);
                                $sc_per_quantity = ($sc_amount_ratio * $sc_amount) / $total_delivered_quantity;
                                $sc_per_cost = $sc_per_quantity * $cd_key2['delivered_quantity'];
                                $pay_amount_per_cost = $pay_amount_per_cost - $sc_per_cost;
                                // amount in USD
                                $sc_in_usd_per_cost = $currency_com->advanceCurrencyConversion($sc_per_cost, $order_currency, 'USD', $gp_row['first_date']);
                            }

                            $pay_amount_in_usd_per_cost = $currency_com->advanceCurrencyConversion($pay_amount_per_cost, $order_currency, 'USD', $gp_row['first_date']);

                            // Set partial store credit payment
                            $po_array['total_sc_in_order_currency'] = $sc_per_cost;
                            $po_array['total_payment_in_order_currency'] = $pay_amount_per_cost;
                            $po_array['total_sc_in_usd'] = $sc_in_usd_per_cost;
                            $po_array['total_payment_in_usd'] = $pay_amount_in_usd_per_cost;

                            $po_obj->saveNewRecord($po_array, true);
                        }
                        
                        $notification_com->scanMargin([
                            'order_id' => $gp_array['order_id'],
                            'products_id' => $gp_array['products_id'],
                            'products_name' => $gp_row['products_name'],
                            'total_delivered_quantity' => $gp_array['total_delivered_quantity'],
                            'total_order_in_usd' => $total_order_in_usd,
                            'total_purchase_in_usd' => $total_purchase_in_usd,
                            'total_purchase_in_order_currency' => $total_purchase_in_order_currency,
                            'margin_percentage_in_order_currency' => number_format($gp_array['margin_percentage_in_order_currency'], '2', '.', ''),
                        ]);
                        
                        $gp_process_count += 1;
                        
                        if ($retry_mode) {
                            \backend\models\CronRetry::deleteRerunByID($idx);
                        }
                    } else {
                        # failed to insert db
                        # create re-run
                        $this->capture4rerun('GP', $idx, $retry_mode, $gp_row, [1003, 'Issue with Data', $gp_array], $notification_com);
                    }
                }
            } else {
                # Missing GP Cost Data
                # create re-run
                $this->capture4rerun('GP', $idx, $retry_mode, $gp_row, [1002, 'Missing GP Cost Data', $gp_row], $notification_com);
            }
        }

        $notification_com->fireNotification();

        return [
            'gp_process_count' => $gp_process_count,
            'last_id' => isset($gp_row['id']) ? $gp_row['id'] : ''
        ];
    }
    
    private function gpInsertReleased($gp_list, $retry_mode = false) {
        $gp_obj = new \backend\models\GpOrderReleased();
        $po_obj = new \backend\models\GpCostReleased();
        $currency_com = new CurrenciesCom();
        $ogm_model = new \backend\modules\cronjob\models\OffgamersModel();
        $general_com = new GeneralCom();
        $gp_process_count = 0;
        
        foreach ($gp_list as $idx => $gp_row) {
            $gp_array = [];
            $extra_info = json_decode($gp_row['extra_info'], true);
            $order_unit_price_in_usd_sell_rate = isset($extra_info['final_price']) ? $extra_info['final_price'] : 0;
            
            // get Order Currency
            $order_currency = $gp_row['currency'];

            $order_conversion_rate_in_usd = $currency_com->advanceCurrencyConversionRate('USD', $order_currency, $gp_row['first_date']);
            $order_unit_price_in_order_currency = $order_unit_price_in_usd_sell_rate * $gp_row['currency_value'];
            $total_released_quantity = $gp_row['released_quantity'];
            $total_released_in_order_currency =  $order_unit_price_in_order_currency * $total_released_quantity;
            $total_released_in_usd = $currency_com->advanceCurrencyConversion($total_released_in_order_currency, $order_currency, 'USD', $gp_row['first_date']);

            list($softpin_array, $total_purchase_in_purchase_currency, $total_purchase_in_order_currency, $total_purchase_in_usd) = $this->getCostInfo($extra_info, $gp_row, $order_currency);
            
            if ($softpin_array) {
                $order_margin_in_order_currency = $total_released_in_order_currency - $total_purchase_in_order_currency;
                
                if ($total_purchase_in_order_currency > 0) {
                    $margin_percentage_in_order_currency = $total_released_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_released_in_order_currency * 100) : -100;
                } else {
                    $margin_percentage_in_order_currency = $total_released_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_released_in_order_currency * 100) : 0;
                }

                // Get Payment methods (parent - pm) title
                $payment_methods_title = '';
                if (!empty($gp_row['payment_method'])) {
                    if (isset($gp_row['payment_methods_parent_id']) && !empty($gp_row['payment_methods_parent_id'])) {
                        $payment_methods_title = $ogm_model->getPaymentMethodsTitle($gp_row['payment_methods_parent_id'], $gp_row['payment_methods_id']);
                    }
                } else {
                    $payment_methods_title = 'Store Credit';
                }
                
                $gp_array['released_start_datetime'] = $gp_row['first_date'];
                $gp_array['released_end_datetime'] = $gp_row['log_date_time'];
                $gp_array['order_id'] = $gp_row['orders_id'];
                $gp_array['order_currency'] = $order_currency;
                $gp_array['payment_method'] = $payment_methods_title;
                $gp_array['products_id'] = $gp_row['products_id'];
                $gp_array['product_name'] = $gp_row['products_name'];
                $gp_array['unit_price_in_order_currency'] = $order_unit_price_in_order_currency;
                $gp_array['margin_in_order_currency'] = $order_margin_in_order_currency;
                $gp_array['margin_percentage_in_order_currency'] = $margin_percentage_in_order_currency;
                $gp_array['order_conversion_rate_in_usd'] = $order_conversion_rate_in_usd;
                $gp_array['total_released_in_order_currency'] = $total_released_in_order_currency;
                $gp_array['total_purchase_in_purchase_currency'] = $total_purchase_in_purchase_currency;
                $gp_array['total_purchase_in_usd'] = $currency_com->displayFormat($total_purchase_in_usd, 'USD', false);
                $gp_array['total_released_in_usd'] = $currency_com->displayFormat($total_released_in_usd, 'USD', false);
                $gp_array['total_released_quantity'] = $total_released_quantity;
                $gp_array['margin_in_usd'] = $total_released_in_usd - $total_purchase_in_usd;
                
                if ($total_purchase_in_usd > 0) {
                    $gp_array['margin_percentage_in_usd'] = $gp_array['total_released_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_released_in_usd'] * 100) : -100;
                } else {
                    $gp_array['margin_percentage_in_usd'] = $gp_array['total_released_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_released_in_usd'] * 100) : 0;
                }
                
                $check_gp_array_all_values = $general_com->hasAnyEmptyValueInArray($gp_array);
                $check_po_array_all_values = $general_com->hasAnyEmptyValueInArray($softpin_array);

                if (count($check_gp_array_all_values) || count($check_po_array_all_values)) {
                    $failed_gp_order_array = [];
                    
                    if (count($check_gp_array_all_values)) {
                        $failed_gp_order_array[$gp_row['orders_id']]['order'] = $check_gp_array_all_values;
                    }

                    if (count($check_po_array_all_values)) {
                        $failed_gp_order_array[$gp_row['orders_id']]['cost'] = $check_po_array_all_values;
                    }
                    
                    # create re-run
                    $this->capture4rerun('GP_RELEASE', $idx, $retry_mode, $gp_row, [1001, 'Missing Data', $failed_gp_order_array]);
                    unset($failed_gp_order_array);
                } else {
                    $gp_record_new = $gp_obj->saveNewRecord($gp_array, true);
                    $gp_order_id = isset($gp_record_new->id) ? $gp_record_new->id : (is_object($gp_record_new) ? 0 : $gp_record_new);
                    
                    if ($gp_order_id) {
                        foreach ($softpin_array as $cd_key2) {
                            $po_array = [];
                            $po_array['gpo_released_id'] = $gp_order_id;
                            $po_array['purchase_type'] = $cd_key2['purchase_type'];
                            $po_array['purchase_id'] = $cd_key2['purchase_id'];
                            $po_array['purchase_currency'] = $cd_key2['po_currency'];
                            $po_array['gpcr_gpe_id'] = $cd_key2['po_entity'];
                            $po_array['released_quantity'] = $cd_key2['delivered_quantity'];
                            $po_array['unit_cost_in_order_currency'] = $cd_key2['unit_cost_in_order_currency'];
                            $po_array['unit_cost_in_purchase_currency'] = $cd_key2['unit_cost_in_purchase_currency'];
                            $po_array['unit_cost_in_usd'] = $cd_key2['unit_cost_in_usd'];
                            $po_array['total_cost_in_usd'] = $cd_key2['total_cost_in_usd'];
                            $po_array['purchase_conversion_rate_in_order_currency'] = $cd_key2['po_spot_rate'];
                            $po_array['purchase_conversion_rate_in_usd'] = $currency_com->advanceCurrencyConversionRate($cd_key2['po_currency'], 'USD', $gp_row['first_date']);
                            $po_array['total_cost_in_purchase_currency'] = $cd_key2['total_cost_in_purchase_currency'];
                            $po_array['total_cost_in_order_currency'] = $cd_key2['total_cost_in_order_currency'];
                            $po_array['extra_info'] = json_encode($cd_key2['cdkey_id']);

                            // Additional checking for partial store credit payment
                            $sc_per_cost = 0;
                            $sc_in_usd_per_cost = 0;
                            $pay_amount_in_usd_per_cost = 0;

                            // Calculate price per item
                            $pay_amount_per_cost = $total_released_in_order_currency / $total_released_quantity;
                            $pay_amount_per_cost = $pay_amount_per_cost * $cd_key2['delivered_quantity'];

                            // get store credit amount
                            $sc_amount = $ogm_model->getScAmount($gp_row['orders_id']);
                            $original_total_delivered = $ogm_model->getSumOriginalQuantity($gp_row['orders_id']);
                            
                            if ($sc_amount > 0) {
                                $sc_amount_ratio = $ogm_model->getBundleRatio($gp_array['order_id'], $gp_array['products_id'], $gp_row['first_date'], $currency_com);
                                $sc_per_quantity = ($sc_amount_ratio * $sc_amount) / $original_total_delivered;
                                $sc_per_cost = $sc_per_quantity * $cd_key2['delivered_quantity'];
                                $pay_amount_per_cost = $pay_amount_per_cost - $sc_per_cost;
                                // amount in USD
                                $sc_in_usd_per_cost = $currency_com->advanceCurrencyConversion($sc_per_cost, $order_currency, 'USD', $gp_row['first_date']);
                            }

                            $pay_amount_in_usd_per_cost = $currency_com->advanceCurrencyConversion($pay_amount_per_cost, $order_currency, 'USD', $gp_row['first_date']);

                            // Set partial store credit payment
                            $po_array['total_sc_in_order_currency'] = $sc_per_cost;
                            $po_array['total_payment_in_order_currency'] = $pay_amount_per_cost;
                            $po_array['total_sc_in_usd'] = $sc_in_usd_per_cost;
                            $po_array['total_payment_in_usd'] = $pay_amount_in_usd_per_cost;

                            $po_obj->saveNewRecord($po_array, true);
                        }
                        
                        $gp_process_count += 1;
                        
                        if ($retry_mode) {
                            \backend\models\CronRetry::deleteRerunByID($idx);
                        }
                    } else {
                        # failed to insert db
                        # create re-run
                        $this->capture4rerun('GP_RELEASE', $idx, $retry_mode, $gp_row, [1003, 'Issue with Data', $gp_array]);
                    }
                }
            } else {
                # Missing GP Cost Data
                # create re-run
                $this->capture4rerun('GP_RELEASE', $idx, $retry_mode, $gp_row, [1002, 'Missing GP Cost Data', $gp_row]);
            }
        }
        
        return ['gp_process_count' => $gp_process_count, 'last_id' => isset($gp_row['id']) ? $gp_row['id'] : ''];
    }

    private function createCronLogs($input_array) {
        $cron_log_com = new CronLogCom();
        $cron_log_com->saveCronLog($input_array);
    }

    private function transIdToDateTime($param) {
        if (strlen($param) == 18) {
            $substr = substr($param, 0, 14);
            return date('Ymd' . '0000000000', strtotime($substr . '+ 1 day'));
        }
        return null;
    }

    private function getDimDateFromTimeStamp($ts) {
        $ts = strtotime($ts);
        $year = date('Y', $ts);

        $dim_date['year_month'] = $year . date('m', $ts);
        $dim_date['year_week'] = $year . date('W', $ts);
        $dim_date['year_day'] = $year . date('z', $ts);
        $dim_date['year_quarter'] = $year . ceil(date('n', $ts) / 3);
        $dim_date['year'] = $year;
        $dim_date['date'] = date('Y-m-d', $ts);

        return $dim_date;
    }
    
    private function errorReport($subject, $response_data) {
        ob_start();
        echo "========================RESPONSE=========================<BR><pre>";
        if (is_array($response_data)) {
            print_r($response_data);
        } else {
            echo $response_data;
        }
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = $subject . ' - ' . date("F j, Y H:i");
        $headers = 'MIME-Version: 1.0' . "\r\n";
        $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
        $headers .= 'From: OG-CREW-REPORT-MODULE REPORTER <<EMAIL>>' . "\r\n";
        @mail(Yii::$app->params['dev_debug_email']['recipient'], $subject, $response_data, $headers);
    }

}