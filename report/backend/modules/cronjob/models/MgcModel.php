<?php

namespace backend\modules\cronjob\models;
use Yii;

class MgcModel extends \yii\db\ActiveRecord {

    public static function getDb() {
        return Yii::$app->get('db_mgc');
    }

    public function getMgcPins($offset1, $offset2) {
        $mgc_sql = "SELECT rgc.trans_id,rgc.game_card_serial,rgc.redeem_amount,m.merchant_id, m.merchant_name, rt.trans_method, 
                        rt.merchant_ref_id, 
                        rt.trans_currency, DATE_FORMAT( FROM_UNIXTIME( `trans_complete_ts` ) , '%Y-%m-%d %H:%i:%s' ) AS trans_complete_ts FROM redeem_game_card rgc
                        LEFT JOIN redeem_transaction rt ON rgc.trans_id=rt.trans_id
                        LEFT JOIN merchant m ON m.merchant_id = rt.merchant_id WHERE rt.trans_id BETWEEN $offset1 AND $offset2 AND rt.trans_status=1";
        $mgc_report = self::getdb()->createCommand($mgc_sql)->queryAll();

        return $mgc_report;
    }

    public function getRequestLogIP($trans_id, $serial, $default = '') {
        $request_log_sql = "SELECT request_ip FROM request_log rl WHERE trans_id='$trans_id' AND  serial='$serial' ORDER BY request_log_id DESC LIMIT 1";
        $request_log = self::getdb()->createCommand($request_log_sql)->queryOne();
        
        return isset($request_log['request_ip']) ? $request_log['request_ip'] : $default;
    }

}
