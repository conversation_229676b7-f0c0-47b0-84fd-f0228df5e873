<?php

namespace backend\modules\cronjob\models;

use Yii;

class OffgamersModel extends \yii\db\ActiveRecord {

    public $cron_limit_record = 500;

    public static function getDb() {
        return Yii::$app->get('db_offgamers');
    }

    public function getPoList($new_gp_start_id) {
        $po_sql = " SELECT o.orders_id, o.customers_id, o.currency, o.payment_method, o.currency_value, o.payment_methods_parent_id, o.payment_methods_id,
                        ldp.id, ldp.products_quantity, ldp.extra_info, ldp.log_date_time, ldp.products_id, oss.first_date,
                       CONCAT(p.products_cat_path, ' > ', op.products_name) AS products_name, 
                       p.products_flag_id, p.products_model
                    FROM log_delivered_products AS ldp
                    INNER JOIN  orders AS o ON o.orders_id = ldp.orders_id
                    INNER JOIN orders_status_stat AS oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 1
                    INNER JOIN products AS p ON p.products_id= ldp.products_id
                    LEFT JOIN orders_products AS op ON op.products_id= ldp.products_id AND o.orders_id = op.orders_id WHERE ldp.id >= $new_gp_start_id
                    ORDER BY ldp.id ASC
                    LIMIT $this->cron_limit_record
                    ";

        $gp_report = self::getdb()->createCommand($po_sql)->queryAll();

        return $gp_report;
    }
    
    public function getPoListReleased($new_gp_start_id) {
        $po_sql = "SELECT o.orders_id, o.customers_id, o.currency, o.payment_method, o.payment_methods_parent_id, o.payment_methods_id,
                    ldp.id, ldp.released_quantity, ldp.extra_info, ldp.products_id,
                    CONCAT(p.products_cat_path, ' > ', op.products_name) AS products_name, 
                    p.products_flag_id, p.products_model, o.currency_value, oss.first_date, ldp.log_date_time
                    FROM log_delivered_released AS ldp
                    INNER JOIN  orders AS o ON o.orders_id = ldp.orders_id
                    INNER JOIN orders_status_stat AS oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 1
                    INNER JOIN products AS p ON p.products_id= ldp.products_id
                    LEFT JOIN orders_products AS op ON op.products_id= ldp.products_id AND o.orders_id = op.orders_id WHERE ldp.id >= $new_gp_start_id
                    ORDER BY ldp.id ASC
                    LIMIT $this->cron_limit_record
                    ";

        $gp_report = self::getdb()->createCommand($po_sql)->queryAll();

        return $gp_report;
    }
    
    public function getPoUnitPrice($purchase_orders_id, $products_id) {
        $cost = 0;

        if ($purchase_orders_id) {
            $po_unit_price_select_sql = "   SELECT products_usd_unit_price, products_unit_price
                                            FROM purchase_orders_products
                                            WHERE purchase_orders_id = '" . $purchase_orders_id . "' AND  products_id = '" . $products_id . "'";
            $po_unit_price_result = self::getdb()->createCommand($po_unit_price_select_sql)->queryOne();
            $cost = $po_unit_price_result['products_unit_price'];
        }
        return $cost;
    }

    public function getPoCurrency($po_id) {
        $po_currency = '';

        if (!empty($po_id)) {
            $sql = "SELECT currency FROM purchase_orders
                    WHERE purchase_orders_id = $po_id";
            $result = self::getdb()->createCommand($sql)->queryOne();
            $po_currency = $result['currency'];
        }

        return $po_currency;
    }
    
    // Function will retrieve Purchase Order Entity for particular PO
    public function getPoEntity($po_id) {
        if (!empty($po_id)) {
            $sql = "SELECT delivery_location, delivery_name FROM purchase_orders WHERE purchase_orders_id = $po_id";
            $result = self::getdb()->createCommand($sql)->queryOne();
        }

        return [
            'id' => isset($result['delivery_location']) ? $result['delivery_location'] : '',
            'company_name' => isset($result['delivery_name']) ? $result['delivery_name'] : '',
        ];
    }
    
    // Function will retrieve Purchase Order Entity for particular API, DTU & PIN
    public function getEntity($products_id) {
        $sql = "SELECT products_cost_company_code, products_cost_company_name FROM  products_cost  WHERE products_id = '" . $products_id . "' ";
        $result = self::getdb()->createCommand($sql)->queryOne();
        
        return [
            'id' => isset($result['products_cost_company_code']) ? $result['products_cost_company_code'] : '',
            'company_name' => isset($result['products_cost_company_name']) ? $result['products_cost_company_name'] : '',
        ];
    }

    public function getCurrencyRate($code, $order_date) {
        $sql = "SELECT spot_value FROM  currencies_history  WHERE code = '" . $code . "' AND date_from <='" . $order_date . "'  ORDER BY version DESC LIMIT 1 ";
        $sql_result = self::getdb()->createCommand($sql)->queryOne();
        return $sql_result['spot_value'];
    }

    public function getAPICurrencyCost($cd_key_id) {
        $sql = "SELECT custom_products_code_id, id, currency_settle_amount, currency_code FROM  log_api_restock  WHERE custom_products_code_id = '" . $cd_key_id . "' ";
        $sql_result = self::getdb()->createCommand($sql)->queryOne();

        // Check if empty
        if (empty($sql_result['currency_code'])) {
            $op_sql = "SELECT products_id FROM  custom_products_code  WHERE custom_products_code_id = '" . $sql_result['custom_products_code_id'] . "' ";
            $op_sql_result = self::getdb()->createCommand($op_sql)->queryOne();

            $pc_sql = "SELECT products_cost, products_currency FROM  products_cost  WHERE products_id = '" . $op_sql_result['products_id'] . "' ";
            $pc_sql_result = self::getdb()->createCommand($pc_sql)->queryOne();

            return [
                'custom_products_code_id' => isset($sql_result['custom_products_code_id']) ? $sql_result['custom_products_code_id'] : '',
                'id' => isset($sql_result['id']) ? $sql_result['id'] : '',
                'currency_code' => isset($pc_sql_result['products_currency']) ? $pc_sql_result['products_currency'] : '',
                'currency_settle_amount' => isset($pc_sql_result['products_cost']) ? $pc_sql_result['products_cost'] : '',
            ];            
        }

        return $sql_result;
    }

    public function getDTUCurrencyCost($top_up_id) {
        $sql = "SELECT orders_products_id, top_up_id, currency_settle_amount, currency_code FROM  orders_top_up  WHERE top_up_id = '" . $top_up_id . "' ";
        $sql_result = self::getdb()->createCommand($sql)->queryOne();

        // Check if empty
        if (empty($sql_result['currency_code'])) {
            $op_sql = "SELECT products_id FROM  orders_products  WHERE orders_products_id = '" . $sql_result['orders_products_id'] . "' ";
            $op_sql_result = self::getdb()->createCommand($op_sql)->queryOne();

            $pc_sql = "SELECT products_cost, products_currency FROM  products_cost  WHERE products_id = '" . $op_sql_result['products_id'] . "' ";
            $pc_sql_result = self::getdb()->createCommand($pc_sql)->queryOne();

            return [
                'orders_products_id' => isset($sql_result['orders_products_id']) ? $sql_result['orders_products_id'] : '',
                'top_up_id' => isset($sql_result['top_up_id']) ? $sql_result['top_up_id'] : '',
                'currency_code' => isset($pc_sql_result['products_currency']) ? $pc_sql_result['products_currency'] : '',
                'currency_settle_amount' => isset($pc_sql_result['products_cost']) ? $pc_sql_result['products_cost'] : '',
            ];            
        }

        return $sql_result;
    }

    public function getPINCurrencyCost($products_id) {
        $sql = "SELECT products_cost, products_currency FROM  products_cost  WHERE products_id = '" . $products_id . "' ";
        $sql_result = self::getdb()->createCommand($sql)->queryOne();
        
        return [
            'currency_code' => isset($sql_result['products_currency']) ? $sql_result['products_currency'] : '',
            'currency_settle_amount' => isset($sql_result['products_cost']) ? $sql_result['products_cost'] : '',
        ];
    }

    public function getPHYCurrencyCost($products_id) {
        $sql = "SELECT products_cost, products_currency FROM  products_cost  WHERE products_id = '" . $products_id . "' ";
        $sql_result = self::getdb()->createCommand($sql)->queryOne();

        return [
            'id' => $products_id,
            'currency_code' => isset($sql_result['products_currency']) ? $sql_result['products_currency'] : '',
            'currency_settle_amount' => isset($sql_result['products_cost']) ? $sql_result['products_cost'] : '',
        ];
    }

    public function checkEntityMovement($po_id, $order_delivery_date) {
        // Get PO 1st upload date in offgamers db from custom_products_code table
        $upload_date_sql = "SELECT code_date_added 
                            FROM custom_products_code 
                            WHERE purchase_orders_id = '" . $po_id . "'
                            ORDER BY custom_products_code_id ASC
        ";
        $upload_date_result = self::getdb()->createCommand($upload_date_sql)->queryOne();
        $upload_date = ($upload_date_result) ? $upload_date_result['code_date_added'] : 0;

        if ($upload_date == 0) {
            // skip if upload date unvailable for this particular PO
            return false;
        } else {
            // compare delivery months vs 1st upload date
            $date1 = date('Y-m', strtotime($order_delivery_date));
            $date2 = date('Y-m', strtotime($upload_date));
            if ($date1 === $date2) {
                // year and month match so do nothing
                return false;
            } else {
                // Check if 2018 orders
                $order_years = date('Y', strtotime($order_delivery_date));
                $po_years = date('Y', strtotime($upload_date));
                if ($order_years >= '2018' && $po_years <= '2017') {
                    // no stock movement for 2018
                    return true;
                } else {
                    // Update Entity to SG as the date not match
                    return false;
                }
            }
        }
    }

    // Get Store Credit amount for each order if available
    public function getScAmount($order_id) {
        $sc_sql = "SELECT value, text FROM orders_total WHERE class = 'ot_gv' AND orders_id = '" . $order_id . "'";
        $sc_result = self::getdb()->createCommand($sc_sql)->queryOne();
        
        if (isset($sc_result['value'])) {
            // Clean result and get actual amount from text
            $ot_gv = strip_tags($sc_result['text']);
            $ot_gv = preg_replace('(&#[0-9]+[;])', "", $ot_gv);
            return preg_replace("/[^0-9.]/", "", str_replace(',', '', $ot_gv));
        } else {
            return 0;
        }
    }

    // Get sum of original quantity for store credit calculation in refund process
    public function getSumOriginalQuantity($order_id) {
        $sc_sql = "SELECT sum(products_quantity) FROM orders_products WHERE orders_id = '" . $order_id . "'";
        $sc_result = self::getdb()->createCommand($sc_sql)->queryScalar();
        
        return $sc_result;
    }

    public function getPaymentMethodsTitle($payment_methods_parent_id, $payment_methods_id) {
        if ($payment_methods_parent_id > 0) {
            $pm_parent_sql = "SELECT payment_methods_title FROM payment_methods WHERE payment_methods_id = '" . $payment_methods_parent_id . "'";
            $pm_parent_result = self::getdb()->createCommand($pm_parent_sql)->queryOne();
        }

        $pm_sql = "SELECT payment_methods_title FROM payment_methods WHERE payment_methods_id = '" . $payment_methods_id . "'";
        $pm_result = self::getdb()->createCommand($pm_sql)->queryOne();

        return (isset($pm_parent_result['payment_methods_title'])) ? $pm_parent_result['payment_methods_title'].' - '.$pm_result['payment_methods_title'] : $pm_result['payment_methods_title'];
    }

    public function getBundleRatio($orders_id, $products_id, $first_date, $currencyObj) {
        $result = 1;
        $parent_sql = "SELECT products_bundle_id FROM orders_products WHERE orders_id = $orders_id AND products_id = $products_id";
        $parent_info = self::getdb()->createCommand($parent_sql)->queryOne();

        if (isset($parent_info['products_bundle_id']) && !empty($parent_info['products_bundle_id'])) {
            $parent_bundle_id = $parent_info['products_bundle_id'];
            $bundle_item_sql = " SELECT pb.subproduct_id, pb.subproduct_qty, p.products_price, p.products_base_currency
                        FROM products_bundles AS pb
                        LEFT JOIN products AS p ON pb.subproduct_id = p.products_id
                        WHERE pb.bundle_id = $parent_bundle_id";
            $bundle_item = self::getdb()->createCommand($bundle_item_sql)->queryAll();

            $total = 0;
            $products_unit_price = 0;
            foreach ($bundle_item as $item) {
                $unit_price_in_usd = $currencyObj->advanceCurrencyConversion($item['products_price'], $item['products_base_currency'], 'USD', $first_date);
                $total += $unit_price_in_usd;
                if ($item['subproduct_id'] == $products_id) {
                    $products_unit_price = $unit_price_in_usd;
                }
            }

            $result = $products_unit_price / $total;
        }

        return $result;
    }
}
