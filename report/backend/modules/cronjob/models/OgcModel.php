<?php

namespace backend\modules\cronjob\models;

use Yii;

class OgcModel extends \yii\db\ActiveRecord {
   
    private $cron_limit_record=5000;
    
    public static function getDb() {
        return Yii::$app->get('db_ogc');
    }

    public function getOgcPins($offset) {
        $ogc_sql = "SELECT gr.id AS batch_id,gr.description AS gr_description,gr.title,gr.request_date,gr.product_id,gr.reseller_id,
                        gr.client_id,gr.request_by, gr.approve_by,gr.product_name,gr.product_description,c.name AS client_name,
                        c.url,c.description AS client_description,
                        rs.name AS reseller_name,rs.email,rs.tel,rs.reseller_code,pn.id as pin_id, 
                        pn.serial,pn.currency,pn.deno,pn.start_date,pn.end_date
                    FROM gen_request gr 
                    LEFT JOIN  client c  ON gr.client_id=c.id 
                    LEFT JOIN  reseller rs ON gr.reseller_id=rs.id
                    LEFT JOIN  pin pn ON gr.id=pn.batch_id 
                    WHERE pn.id IS NOT NULL AND pn.id >= $offset 
                    ORDER BY pn.id ASC 
                    LIMIT $this->cron_limit_record";
       
        $ogc_report = self::getdb()->createCommand($ogc_sql)->queryAll();

        return $ogc_report;
    }

    public static function getOgcCrewInfo($user_id) {
            $sql = "SELECT id,first_name,last_name,email,user_type AS type FROM user WHERE id=:id";
            $ogc_crew = self::getdb()->createCommand($sql);
            $ogc_crew->bindValue(':id', $user_id);
            $crew_data = $ogc_crew->queryOne();
            return $crew_data;
    }

}
