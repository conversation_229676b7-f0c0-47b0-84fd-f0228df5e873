<?php

use kartik\widgets\Growl;

$allFlashMsg = Yii::$app->session->getAllFlashes();
$delay = 0;
foreach ($allFlashMsg as $key => $value) {
    $typeData = explode(".", $key);
    if (is_array($typeData) && isset($typeData[0])) {
        switch ($typeData[0]) {
            case Growl::TYPE_SUCCESS:
                $type = Growl::TYPE_SUCCESS;
                break;
            case Growl::TYPE_DANGER:
                $type = Growl::TYPE_DANGER;
                break;
            default:
                $type = Growl::TYPE_SUCCESS;
        }

        echo Growl::widget([
            'type' => $type,
            'icon' => 'glyphicon glyphicon-ok-sign',
            'body' => Yii::$app->session->getFlash($key, '-', true),
            'showSeparator' => false,
            'delay' => false,
            'pluginOptions' => [
                'position' => [
                    'from' => 'bottom',
                    'align' => 'right',
                ]
            ]
        ]);
        $delay += 300;
    }
}
?>



