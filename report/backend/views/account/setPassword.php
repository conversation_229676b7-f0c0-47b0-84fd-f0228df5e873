<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use mdm\admin\models\User;

/**
 * @var yii\base\View $this
 * @var mdm\admin\models\User $model
 */
$this->title = 'Password Setting';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="user-create">

    <h1><?php echo Html::encode($this->title) ?></h1>

    <div class="user-form">

        <?php $form = ActiveForm::begin(['id' => 'form-signup']); ?>

        <?php
        echo $form->field($model, 'oldPassword')->passwordInput(['maxlength' => 128]);
        echo $form->field($model, 'password')->passwordInput(['maxlength' => 128]);
        echo $form->field($model, 'retypePassword')->passwordInput(['maxlength' => 128]);
        ?>


        <div class="form-group">
            <?php
            echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', [
                'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary',
                'name' => 'Submit',
                'value' => 'update',
            ])
            ?>
        </div>

        <?php ActiveForm::end(); ?>

    </div>


</div>
