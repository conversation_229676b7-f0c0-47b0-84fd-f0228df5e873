
<?php

use yii\helpers\Html;
use kartik\widgets\AlertBlock;
use kartik\grid\GridView;
use kartik\widgets\ActiveForm;
use yii\helpers\Url;
use kartik\widgets\DatePicker;
use yii\helpers\ArrayHelper;

$get_params = Yii::$app->request->get('GpCostReleased');

$cur_obj = new \backend\components\CurrenciesCom();
$entity_obj = new \backend\components\GpReportCom();

$this->title = 'GP Released Report';
$this->params['breadcrumbs'][] = ['label' => 'Site Report Index', 'url' => ['site/index']];
$this->params['breadcrumbs'][] = ['label' => 'GP Report Index', 'url' => ['gp-report/index']];
$this->params['breadcrumbs'][] = $this->title;
$gridColumns = [
    ['class' => 'kartik\grid\SerialColumn'],
    [
        'attribute' => 'released_end_datetime',
        'format' => 'raw',
        'value' => function($model) {
            return $model->gpOrderReleased->released_end_datetime;
        },
        'filterWidgetOptions' => [
            'pluginOptions' => ['allowClear' => true],
        ],
        'filterInputOptions' => ['placeholder' => 'Released Date Time'],
    ],
    [
        'attribute' => 'order_id',
        'value' => function($model) {
            return $model->gpOrderReleased->order_id;
        },
    ],
    [
        'attribute' => 'product_name',
        'value' => function($model) {
            return $model->gpOrderReleased->product_name;
        },
    ],
    [
        'attribute' => 'order_currency',
        'value' => function($model) {
            return $model->gpOrderReleased->order_currency;
        },
    ],
    [
        'attribute' => 'payment_method',
        'value' => function($model) {
            return $model->gpOrderReleased->payment_method;
        },
    ],
    [
        'attribute' => 'unit_cost_in_purchase_currency',
        'value' => function ($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->unit_cost_in_purchase_currency, $model->purchase_currency);
        },
        'filterInputOptions' => [
            'value' =>  isset($get_params['unit_cost_in_purchase_currency']) ? $get_params['unit_cost_in_purchase_currency'] : '', 
            'class' => 'form-control'
        ],
    ],
    [
        'attribute' => 'company_name',
        'headerOptions' => ['style' => 'width:7%'],
        'value' => function($model) {
            return $model->gpEntity->id." - ".$model->gpEntity->company_name;
        },
        'filter' => Html::activeDropDownList($model, 'company_name', $entity_obj->getEntityOption(),[
            'class'=>'form-control',
            'prompt' => 'Select Entity'
        ]),
    ],            
    [
        'attribute' => 'released_quantity',
        'filter' => false,
        'contentOptions' => ['class' => 'text-center'],
        'value' => function ($model) {
            return $model->getSumDeliveredQuantity($model->gpo_released_id, $model->id);
        },
    ],
    [
        'attribute' => 'purchase_conversion_rate_in_order_currency',
        'filter' => false,
        'format' => 'raw',
        'contentOptions' => ['class' => 'text-center'],
    ],
    [
        'attribute' => 'unit_cost_in_order_currency',
        'filter' => false,
        'format' => 'raw',
        'value' => function ($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->unit_cost_in_order_currency, $model->gpOrderReleased->order_currency);
        },
        'contentOptions' => ['class' => 'text-center'],
    ],
    [
        'attribute' => 'total_cost_in_order_currency',
        'filter' => false,
        'value' => function ($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_cost_in_order_currency), $model->gpOrderReleased->order_currency);
        },
    ],
    [
        'attribute' => 'total_released_in_order_currency',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->getPoTotal($model->gpo_released_id, $model->id, 'order_currency'), $model->gpOrderReleased->order_currency);
        },
    ],
    [
        'attribute' => 'total_sc_in_order_currency',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_sc_in_order_currency), $model->gpOrderReleased->order_currency);
        },
    ],
    [
        'attribute' => 'total_payment_in_order_currency',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_payment_in_order_currency), $model->gpOrderReleased->order_currency);
        },
    ],
    [
        'attribute' => 'margin_in_order_currency',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->getPoMargin($model->gpo_released_id, $model->id, 'order_currency'), $model->gpOrderReleased->order_currency);
        },
    ],
    [
        'attribute' => 'margin_percentage_in_order_currency',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return number_format($model->getPoMarginPercentage($model->gpo_released_id, $model->id, 'order_currency'),  2) . '%';
        },
    ],
    [
        'attribute' => 'order_conversion_rate_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $model->gpOrderReleased->order_conversion_rate_in_usd;
        },
    ],
    [
        'attribute' => 'total_purchase_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_cost_in_usd), 'USD');
        },
    ],
    [
        'attribute' => 'total_released_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->getPoTotal($model->gpo_released_id, $model->id, 'order_currency_usd'), 'USD');
        },
    ],
    [
        'attribute' => 'total_sc_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_sc_in_usd), 'USD');
        },
    ],
    [
        'attribute' => 'total_payment_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat(($model->getSumDeliveredQuantityCalculation($model->gpo_released_id, $model->id) * $model->total_payment_in_usd), 'USD');
        },
    ],
    [
        'attribute' => 'margin_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return $cur_obj->displayFormat($model->getPoMargin($model->gpo_released_id, $model->id, 'order_currency_usd'), 'USD');
        },
    ],
    [
        'attribute' => 'margin_percentage_in_usd',
        'filter' => false,
        'value' => function($model) use ($cur_obj) {
            return number_format($model->getPoMarginPercentage($model->gpo_released_id, $model->id, 'order_currency_usd'),  2) . '%';
        },
    ]
];
$form = ActiveForm::begin([
    'type' => ActiveForm::TYPE_HORIZONTAL, 
    'method' => 'get', 
    'id' => 'gp_search_form', 
    'action' => Url::to(['gp-report/released'])
]);
?>

<h2></h2>
<div class="well"><legend>Search</legend>
    <!-- START DELIVERY PERIOD -->
    <div class="form-group">
        <?php 
            echo Html::activeLabel($model, 'released_period', ['label' => 'Released Period', 'class' => 'col-sm-2 control-label']) 
        ?>
        <div class="col-sm-2">
            <?php
                echo $form->field($model, 'released_period', ['showLabels' => false])->dropDownList([
                    'custom' => 'Custom Date', 
                    'yesterday' => 'Yesterday', 
                    'thisweek' => 'This Week', 
                    'lastweek' => 'Last Week', 
                    'thismonth' => 'This Month',
                    'thisyear' => 'This Year', 
                    'lastyear' => 'Last Year', 
                    'last7days' => 'Last 7 Days', 
                    'last30days' => 'Last 30 Days'],
                    ['id' => 'delivery', 'onchange' => 'javascript:periodSelectHide(this);']
                );
            ?>
        </div>
        <div class="col-sm-3">
            <?php
            echo $form->field($model, 'released_start_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                'options' => ['placeholder' => '', 'label' => '', 'value' => $released_start_date, 'id' => 'released_start_date'],
                'pluginOptions' => [
                    'format' => 'yyyy-mm-dd',
                    'todayBtn' => 'linked',
                    'clearBtn' => true,
                    'autoclose' => true,
                    'todayHighlight' => true,
                ]
            ])->label('');
            ?>
        </div>
        <div class="col-sm-3">
            <?php
            echo $form->field($model, 'released_end_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                'options' => ['placeholder' => '', 'label' => '', 'value' => $released_end_date, 'id' => 'released_end_date'],
                'pluginOptions' => [
                    'format' => 'yyyy-mm-dd',
                    'todayBtn' => 'linked',
                    'clearBtn' => true,
                    'autoclose' => true,
                    'todayHighlight' => true,
                ]
            ])->label('');
            ?>
        </div>
    </div>
    <!-- START MARGIN % IN CUR -->
    <!-- <div class="form-group"> -->
        <?php 
            // echo Html::activeLabel($model, 'margin_cur', ['label' => 'Margin % In CUR', 'class' => 'col-sm-2 col-xs-3 control-label']) 
        ?>
        <!-- <div class="col-sm-2 col-xs-2"> -->
            <?php
                // echo $form->field($model, 'margin_cur', ['showLabels' => false])->dropDownList([
                //     '<=' => '<=', 
                //     '<' => '<', 
                //     '>=' => '>=', 
                //     '>' => '>', 
                //     '=' => '=='], 
                //     ['id' => 'margin_cur']
                // );
            ?>
        <!-- </div> -->
        <!-- <div class="col-sm-3 col-xs-7"> -->
            <?php 
                // echo $form->field($model, 'margin_cur_input')->textInput()->label(false); 
            ?>
        <!-- </div> -->
    <!-- </div> -->
    <!-- START MARGIN % IN USD -->
    <!-- <div class="form-group"> -->
        <?php 
            // echo Html::activeLabel($model, 'margin_usd', ['label' => 'Margin % In USD', 'class' => 'col-sm-2 col-xs-3 control-label']) 
        ?>
        <!-- <div class="col-sm-2 col-xs-2"> -->
            <?php
                // echo $form->field($model, 'margin_usd', ['showLabels' => false])->dropDownList([
                //     '<=' => '<=', 
                //     '<' => '<', 
                //     '>=' => '>=', 
                //     '>' => '>', 
                //     '=' => '=='], 
                //     ['id' => 'margin_usd']
                // );
            ?>
        <!-- </div> -->
        <!-- <div class="col-sm-3 col-xs-7"> -->
            <?php 
                // echo $form->field($model, 'margin_usd_input')->textInput()->label(false); 
            ?>
        <!-- </div> -->
    <!-- </div> -->
    <?php
        echo '<div class="text-right">' . 
                Html::submitButton('Search', ['type' => 'button', 'class' => 'btn btn-primary']) . '  ' . 
                Html::resetButton('Reset', ['class' => 'btn btn-default', 'id' => 'reset_button', 'onclick' => 'clearForm()']) . '</div>';
        ActiveForm::end();
    ?>   
</div>

<?php
    $currentUrl = Url::current(['pageSize' => null]);
    $referer = Yii::$app->request->referrer;

    echo GridView::widget([
        'dataProvider' => $dataProvider,
        'id' => 'file-grid',
        'filterModel' => $model,
        'pjax' => true,
        'options' => ['style' => 'width:2000px;'],
        'filterSelector' => 'select[name="per-page"]',
        'columns' => $gridColumns,
        'export' => ['target' => GridView::TARGET_SELF, 'icon' => 'glyphicon glyphicon-download'],
        'exportConfig' => [
            GridView::CSV => [
                'filename' => date('YmdHis') . 'GP_Released_Report',
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                ],
            ],
            GridView::EXCEL => ['filename' => date('YmdHis') . 'GP_Released_Report'],
            GridView::PDF => [
                'filename' => date('YmdHis') . 'GP_Released',
                'showHeader' => true,
                'showPageSummary' => true,
                'showFooter' => true,
                'showCaption' => true,
                'config' => [
                    'mode' => 'UTF-8',
                    'methods' => [
                        'SetHeader' => 'GP Released Report',
                        'SetFooter' => 'GP Released Report',
                    ],
                    'options' => [
                        'title' => 'GP Released Report Export',
                        'subject' => 'PDF export generated by Offgamers DEV Team',
                        'keywords' => 'GP, Report, Released',
                        'mode' => 'UTF-8',
                    ],
                ],],
            GridView::TEXT => ['filename' => date('YmdHis') . 'GP_Released_Report'],
            GridView::HTML => ['filename' => date('YmdHis') . 'GP_Released_Report'],
            GridView::JSON => ['filename' => date('YmdHis') . 'GP_Released_Report'],],
        'persistResize' => false,
        'showPageSummary' => false,
        'responsive' => true,
        'panel' => [
            'before' => ''
        ],
        'toolbar' => [
            [
                'content' => 'Records Per Page: ' . 
                    Html::dropDownList(
                            'pageSize', 
                            $currentUrl . '&pageSize=' . $pageSize, [
                                $currentUrl . '&pageSize=20' => 20, 
                                $currentUrl . '&pageSize=50' => 50, 
                                $currentUrl . '&pageSize=100' => 100, 
                                $currentUrl . '&pageSize=200' => 200, 
                                $currentUrl . '&pageSize=500' => 500], 
                            [
                                'style' => 'height:38px;padding:10px;margin:2px;', 
                                'onchange' => "window.location=jQuery(this).val()",
                            ]
                    ),
            ],
            "{export}",
        ]
    ]);

    $js = "jQuery('.dropdown-toggle').dropdown();";
    $js.= "jQuery('li a.dropdown-toggle').click(function(){
                 jQuery('.submenu').toggle();
        });";
    $this->registerJs($js . 'jQuery(window).load(function () {
                  periodSelectHide(jQuery("#delivery"));

                });'
    );
    $this->registerJs(
            'jQuery("document").ready(function(){ 
                    jQuery("#file-grid-pjax").on("pjax:end", function() {
                    jQuery(".dropdown-toggle").dropdown(); //Reload export dropdown
                    });
                    });'
    );
    $this->registerJsFile(Yii::$app->request->baseUrl . '/js/mgc.js');
?>

