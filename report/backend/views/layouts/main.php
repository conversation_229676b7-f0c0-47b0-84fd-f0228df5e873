<?php

use backend\assets\AppAsset;
use yii\widgets\Breadcrumbs;
use yii\helpers\Html;
use dpodiumdev\widgets\AceNavBar;
use dpodiumdev\widgets\AceNav;
use dpodiumdev\widgets\AceSideNav;

AppAsset::register($this);

$this->beginPage()
?>

<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
    <head>
        <meta charset="<?= Yii::$app->charset ?>"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <?= Html::csrfMetaTags() ?>
        <title><?php echo Html::encode($this->title); ?></title>
        <?php $this->head() ?>
    </head>

    <body class="skin-blue">
        <?php
        $this->beginBody();
        $this->render('/_flash');

        AceNavBar::begin([
            'brandLabel' => 'OGM Report Module Crew',
            'brandUrl' => Yii::$app->homeUrl,
            'options' => [
                'class' => 'navbar-inverse navbar-fixed-top',
            ],
        ]);
        $menuItems = [
            ['label' => 'Home', 'url' => ['/site/index']],
        ];
        if (Yii::$app->user->isGuest) {
            $menuItems[] = ['label' => 'Login', 'url' => ['/site/login']];
        } else {
            $menuItems[] = ['label' => Yii::$app->user->identity->username,
                'items' => [
                    ['label' => 'Change Password', 'url' => ['/account/set-password']],
                    ['label' => 'Logout', 'url' => ['/site/logout'], 'linkOptions' => ['data-method' => 'post']],
                ]
            ];
        }
        echo AceNav::widget([
            'options' => ['class' => 'navbar-nav navbar-right'],
            'items' => $menuItems,
        ]);
        AceNavBar::end();
        ?>
        <div id="main-container" class="main-container">
            <div class="main-container-inner">
                <?php
                $userManagement =
                        [
                            'url' => ['#'],
                            'label' => 'User Management',
                            'icon' => 'icon-group',
                            'items' => [
                                ['url' => ['/user/index'], 'label' => 'User List', 'icon' => 'icon-leaf'],
                            ],
                ];
                
                

                $userGroup = backend\models\User::getUserGroup(Yii::$app->user->id);

                $items = [];

                if (isset(Yii::$app->user->id)) {
                    $currentUser = new backend\models\User;
                    $userGroup = $currentUser->getUserGroup(Yii::$app->user->id);
                    
                    $items = [
                        [
                            'url' => ['/site/index'],
                            'label' => 'Dashboard',
                            'icon' => 'icon-dashboard',
                        ],
                    ];

                    switch ($userGroup) {
                        case 'superadmin';
                            $items[] = $userManagement;
                            break;
                        case 'admin';
                            //$items[] = $userManagement;
                            break;
                        
                        default;
                            $items = [];
                    }
                }
               
                echo AceSideNav::widget(['items' => $items]);
                ?>

                <div class="main-content">
                    <div id="breadcrumbs" class="breadcrumbs">
                        <?php
                        echo Breadcrumbs::widget([
                            'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                        ]);
                        ?>
                    </div>
                    <div class="page-content">
                        <?php
                        echo $content;
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <?php $this->endBody() ?>
    </body>
</html>
<?php $this->endPage() ?>
