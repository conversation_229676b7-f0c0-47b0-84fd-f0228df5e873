
<?php

use yii\helpers\Html;
//use yii\grid\GridView;
use kartik\widgets\AlertBlock;
use kartik\grid\GridView;
use kartik\grid\ExpandRowColumn;
use kartik\widgets\ActiveForm;
use kartik\builder\Form;
use yii\helpers\Url;
use kartik\widgets\DatePicker;

echo AlertBlock::widget([
    'useSessionFlash' => true,
    'type' => AlertBlock::TYPE_GROWL,
    'delay' => 100
]);

$this->title = 'MGC Report';
$this->params['breadcrumbs'][] = ['label' => 'Site Report Index', 'url' => ['site/index']];
$this->params['breadcrumbs'][] = ['label' => 'MGC Report Index', 'url' => ['mgc-report/index']];
$this->params['breadcrumbs'][] = $this->title;
$gridColumns = [
    ['class' => 'kartik\grid\SerialColumn'],
            [
                'class' => 'kartik\grid\ExpandRowColumn',
                'width' => '50px',
                'value' => function ($model, $key, $index, $column) {
                    return GridView::ROW_COLLAPSED;
                },
                'detail' => function ($model, $key, $index, $column) {
                    return Yii::$app->controller->renderPartial('_mgc', ['model' => $model]);
                },
                        'headerOptions' => ['class' => 'kartik-sheet-style']
            ],
            [
             'attribute' => 'DimPinBatchRequest_id',
            ],
            [
              'attribute' => 'DimPinReseller_id',
            ],
            [
              'attribute' => 'DimPinReseller_name',
            ],
            [
              'attribute' => 'DimPinProduct_name',
            ],
            [
              'attribute' => 'DimPin_serialno',
            ],
            [
              'attribute' => 'DimPin_currency',
            ],
            [
             'attribute' => 'DimPin_deno',
            ],
            [
                'attribute' => 'DimPinMgcMerchant_name',
                'content' => function ($model) {
                    return call_user_func_array('mb_convert_encoding', array(&$model->DimPinMgcMerchant_name, 'HTML-ENTITIES', 'UTF-8'));
                },
             ],
                        
            [
                'attribute' => 'DimPinMgcTransaction_datetime',
             ],
                        
             [
                'attribute' => 'DimPinMgcTransaction_id',
             ],
             [
               'attribute' => 'FactPinMgcRedemptions_pin_value',
             ],
                ];
                $form = ActiveForm::begin(['type' => ActiveForm::TYPE_HORIZONTAL, 'method' => 'get', 'id' => 'mgc_search_form', 'action' => Url::to(['mgc-report/redeem-report'])]);
                ?>

                <h2></h2>
                <div class="well"><legend>Search</legend>
                    <div class="form-group">
                <?php echo Html::activeLabel($model, 'PinRequest_period', ['label' => 'Pin Request Period', 'class' => 'col-sm-2 control-label']) ?>
                        <div class="col-sm-2">
                        <?php
                        echo $form->field($model, 'PinRequest_period', ['showLabels' => false])->dropDownList(['custom' => 'Custom Date', 'yesterday' => 'Yesterday', 'thisweek' => 'This Week', 'lastweek' => 'Last Week', 'thismonth' => 'This Month',
                            'thisyear' => 'This Year', 'lastyear' => 'Last Year', 'last7days' => 'Last 7 Days', 'last30days' => 'Last 30 Days'], ['id' => 'pin_request', 'onchange' => 'javascript:periodSelectHide(this);']
                        );
                        ?>
                        </div>

                        <div class="col-sm-3">
                <?php
                echo $form->field($model, 'PinRequest_start_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                    'options' => ['placeholder' => '', 'label' => '', 'value' => $request_start_date, 'id' => 'pin_request_start_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd',
                        'todayBtn' => 'linked',
                        'clearBtn' => true,
                        'autoclose' => true,
                        'todayHighlight' => true,
                    ]
                ])->label('');
                ?>
                        </div>
                        <div class="col-sm-3">
                            <?php
                            echo $form->field($model, 'PinRequest_end_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                                'options' => ['placeholder' => '', 'label' => '', 'value' => $request_end_date, 'id' => 'pin_request_end_date'],
                                'pluginOptions' => [
                                    'format' => 'yyyy-mm-dd',
                                    'todayBtn' => 'linked',
                                    'clearBtn' => true,
                                    'autoclose' => true,
                                    'todayHighlight' => true,
                                ]
                            ])->label('');
                            ?>
                        </div>



                    </div>

                    <div class="form-group">
                <?php echo Html::activeLabel($model, 'PinRedeem_period', ['label' => 'Pin Redeem Period', 'class' => 'col-sm-2 control-label']) ?>
                        <div class="col-sm-2">
                        <?php
                        echo $form->field($model, 'PinRedeem_period', ['showLabels' => false])->dropDownList(['custom' => 'Custom Date', 'yesterday' => 'Yesterday', 'thisweek' => 'This Week', 'lastweek' => 'Last Week', 'thismonth' => 'This Month',
                            'thisyear' => 'This Year', 'lastyear' => 'Last Year', 'last7days' => 'Last 7 Days', 'last30days' => 'Last 30 Days'], ['id' => 'pin_redeem', 'onchange' => 'javascript:periodSelectHide(this);']
                        );
                        ?>
                        </div>

                        <div class="col-sm-3">
                <?php
                echo $form->field($model, 'PinRedeem_start_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                    'options' => ['placeholder' => '', 'label' => '', 'value' => $redeem_start_date, 'id' => 'pin_redeem_start_date'],
                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd',
                        'todayBtn' => 'linked',
                        'clearBtn' => true,
                        'autoclose' => true,
                        'todayHighlight' => true,
                    ]
                ])->label('');
                ?>
                        </div>
                        <div class="col-sm-3">
                            <?php
                            echo $form->field($model, 'PinRedeem_end_date', ['showLabels' => false])->widget(DatePicker::classname(), [
                                'options' => ['placeholder' => '', 'label' => '', 'value' => $redeem_end_date, 'id' => 'pin_redeem_end_date'],
                                'pluginOptions' => [
                                    'format' => 'yyyy-mm-dd',
                                    'todayBtn' => 'linked',
                                    'clearBtn' => true,
                                    'autoclose' => true,
                                    'todayHighlight' => true,
                                ]
                            ])->label('');
                            ?>
                        </div>



                    </div>


                <?php
                echo '<div class="text-right">' . Html::submitButton('Search', ['type' => 'button', 'class' => 'btn btn-primary']) . '  ' . Html::resetButton('Reset', ['class' => 'btn btn-default', 'id' => 'reset_button', 'onclick' => 'clearForm()']) . '</div>';
                ActiveForm::end();
                ?>   

                </div>

                <?php
                $currentUrl = Url::current(['pageSize' => null]);
                $referer = Yii::$app->request->referrer;

                echo GridView::widget([
                    'dataProvider' => $dataProvider,
                    'id' => 'file-grid',
                    'filterModel' => $model,
                    'pjax' => true,
                    'filterSelector' => 'select[name="per-page"]',
                    'columns' => $gridColumns,
                    'export' => ['target' => GridView::TARGET_SELF, 'icon' => 'glyphicon glyphicon-download'],
                    'exportConfig' => [
                        GridView::CSV => [
                            'filename' => date('YmdHis') . 'MGC_Redeem_Report',
                            'columns' => [
                                ['class' => 'yii\grid\SerialColumn'],
                                'fpd_pbr_id',
                            ],
                        ],
                        GridView::EXCEL => ['filename' => date('YmdHis') . 'MGC_Redeem_Report'],
                        GridView::PDF => [
                            'filename' => date('YmdHis') . 'MGC_Redeem_Report',
                            'showHeader' => true,
                            'showPageSummary' => true,
                            'showFooter' => true,
                            'showCaption' => true,
                            'config' => [
                                'mode' => 'UTF-8',
                                'methods' => [
                                    'SetHeader' => 'MGC Redeem Report',
                                    'SetFooter' => 'MGC Redeem Report',
                                ],
                                'options' => [
                                    'title' => 'MGC Redeem Report Export',
                                    'subject' => 'PDF export generated by Offgamers DEV Team',
                                    'keywords' => 'MGC, Redeem, Report',
                                    'mode' => 'UTF-8',
                                    'autoScriptToLang' => true,
                                    'ignore_invalid_utf8' => true,
                                    'tabSpaces' => true,
                                ],
                            ],],
                        GridView::TEXT => ['filename' => date('YmdHis') . 'MGC_Redeem_Report'],
                        GridView::HTML => ['filename' => date('YmdHis') . 'MGC_Redeem_Report'],
                        GridView::JSON => ['filename' => date('YmdHis') . 'MGC_Redeem_Report'],],
                    'persistResize' => false,
                    'showPageSummary' => false,
                    'responsive' => true,
                    'panel' => [
                        'before' => ''
                    ],
                    'toolbar' => [
                        [
                            'content' => 'Records Per Page: ' . Html::dropDownList('pageSize', $currentUrl . '&pageSize=' . $pageSize, [$currentUrl . '&pageSize=20' => 20, $currentUrl . '&pageSize=50' => 50, $currentUrl . '&pageSize=100' => 100, $currentUrl . '&pageSize=200' => 200, $currentUrl . '&pageSize=500' => 500], [
                                'style' => 'height:38px;padding:10px;margin:2px;', 'onchange' => "window.location=jQuery(this).val()",
                            ]),
                        ],
                        "{export}",
                    ]
                ]);
                ?>


                <?php
                $js = "jQuery('.dropdown-toggle').dropdown();";
                $this->registerJs($js . 'jQuery(window).load(function () {
              periodSelectHide(jQuery("#pin_request"));
              periodSelectHide(jQuery("#pin_redeem"));
            });');

                $this->registerJs(
                        'jQuery("document").ready(function(){ 
                jQuery("#file-grid-pjax").on("pjax:end", function() {
                jQuery(".dropdown-toggle").dropdown(); //Reload export dropdown
                });
                });');

                $this->registerJsFile(Yii::$app->request->baseUrl . '/js/mgc.js');
                ?>

