<script type="text/javascript">
    var URL_RESET_PASSWORD = "<?php echo \Yii::$app->urlManager->createUrl("/user/reset-password"); ?>";
</script>

<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use mdm\admin\models\User;

/**
 * @var yii\base\View $this
 * @var mdm\admin\models\User $model
 * @var yii\widgets\ActiveForm $form
 */
?>

<div class="user-form">

    <?php $form = ActiveForm::begin(['id' => 'form-signup']); ?>

    <?php
    if ($model->isNewRecord) {
        echo $form->field($model, 'username')->textInput(['maxlength' => 128]);
        echo $form->field($model, 'password')->passwordInput(['maxlength' => 128]);
        echo $form->field($model, 'retypePassword')->passwordInput(['maxlength' => 128]);
    }
    ?>

    <?php echo $form->field($model, 'email')->textInput(['maxlength' => 128]) ?>

    <?php
    if (!$model->isNewRecord) {
        $list = $model->getStatusList();
        echo $form->field($model, 'status')->radioList($list, ['inline' => true]);
        echo Html::button('Password Reset', [
            'class' => 'btn btn-link',
            'onClick' => 'resetPassword("' . $model->id . '")',
            'type' => 'button',
            'value' => 'Password Reset',
            'id' => 'btn-reset-pw'
        ]);
    }
    ?>

    <div class="form-group">
        <?php
        echo Html::submitButton($model->isNewRecord ? '<i class="icon-plus align-top bigger-125"></i> Create' : '<i class="icon-pencil align-top bigger-125"></i> Update', [
            'class' => 'btn btn-primary',
            'name' => 'Submit',
            'value' => 'update',
        ])
        ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
