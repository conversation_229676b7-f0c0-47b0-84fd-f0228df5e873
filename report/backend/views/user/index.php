<?php

use yii\helpers\Html;
use yii\grid\GridView;

/**
 * @var yii\base\View $this
 * @var yii\data\ActiveDataProvider $dataProvider
 * @var mdm\admin\models\UserSearch $searchModel
 */
$this->title = 'Users';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="user-index">
    <div class="page-header">
        <h1>User </h1>
    </div>

    <div class="row">
        <div class="col-xs-12">

            <?php echo Html::a('<i class="icon-plus align-top bigger-125"></i> Create User', ['create'], ['class' => 'btn btn-primary']) ?>

            <div class="hr hr-18 dotted hr-double"></div>
            <?php
            echo GridView::widget([
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'columns' => [
                    [
                        'class' => 'yii\grid\SerialColumn',
                        'headerOptions' => ['width' => '24px']
                    ],
                    [
                        'class' => 'yii\grid\DataColumn',
                        'attribute' => 'username',
                        'headerOptions' => ['width' => '50%']
                    ],
                    [
                        'format' => 'html',
                        'attribute' => 'status',
                        'value' => function ($searchModel) {
                            if ($searchModel->status == "10") {
                                return "<span class='label label-success'>Active</span>";
                            } else {
                                return "<span class='label label-danger'>In-active</span>";
                            }
                        },
                        'filter' => $userModel->getStatusList(),
                    ],
                   
                    [
//                'header' => 'Action',
                        'class' => 'yii\grid\ActionColumn',
                        'headerOptions' => ['width' => '100px'],
                        'template' => '{view} {update} {delete} {merchant}',
                        'buttons' => [
                            'view' => function ($url, $model) {
                                return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, [
                                            'title' => "View",
                                            'data-pjax' => '0',
                                ]);
                            },
                            'update' => function ($url, $model) {
                                return Html::a('<span class="glyphicon glyphicon-pencil"></span>', $url, [
                                            'title' => "Edit",
                                            'data-pjax' => '0',
                                ]);
                            },
                            'delete' => function ($url, $model) {
                                if (!$model->checkSuperAdmin()) {
                                    return Html::a('<span class="glyphicon glyphicon-trash"></span>', $url, [
                                                'title' => Yii::t('yii', 'Delete'),
                                                'data-pjax' => '0',
                                                'onclick' => 'return confirm("Confirm delete user with id ' . $model['id'] . '?");'
                                    ]);
                                }
                            },
                            'merchant' => function ($url, $model) {

                                if ($model->userMainGroup == 'merchant') {

                                    if (isset($model->merchant)) {
                                        $action = 'Edit';
                                        $url = \Yii::$app->urlManager->createUrl(['merchant/update', 'id' => $model->merchant->merchant_id]);
                                    } else {
                                        $action = 'Create';
                                        $url = \Yii::$app->urlManager->createUrl(['merchant/create', 'user_id' => $model['id']]);
                                    }

                                    return Html::a('<span class="glyphicon glyphicon-briefcase"></span>', $url, [
                                                'title' => $action . ' ' . Yii::t('app', 'Merchant'),
                                    ]);
                                }
                            },
                        ],
                    ]
                ]
            ]);
            ?>
        </div>
    </div>

    <div class="hr hr-18 dotted hr-double"></div>

    <div class="row">
        <div class="col-xs-7">
        </div>
    </div>
</div>
