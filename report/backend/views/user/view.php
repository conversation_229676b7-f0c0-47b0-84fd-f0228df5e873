<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use mdm\admin\components\AccessChecking;

/**
 * @var yii\base\View $this
 * @var mdm\admin\models\User $model
 */
$this->title = $model->username;
$this->params['breadcrumbs'][] = ['label' => 'Users', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="user-view">
    <div class="page-header">
        <h1><?php echo Html::encode($this->title) ?></h1>
    </div>

    <div class="row">
        <div class="col-xs-12">

            <p>
                <?php
                //if (AccessChecking::AccessChecking("/user.update") === true) {
                    echo Html::a('<i class="icon-pencil align-top bigger-125"></i> Edit', ['update', 'id' => $model->id], [
                        'class' => 'btn btn-primary'
                    ]);
                //}
                ?>
                <?php
                //if (AccessChecking::AccessChecking("/user.delete") === true && $model->id != 1) {
                    echo Html::a('<i class="icon-trash align-top bigger-125"></i> Delete', ['delete', 'id' => $model->id], [
                        'class' => 'btn btn-danger',
                        'data-confirm' => Yii::t('app', 'Are you sure to delete this item?'),
                        'data-method' => 'post'
                    ]);
                //}
                ?>
            </p>

            <?php
            if ($model->status == "10") {
                $model->status = "<span class='label label-success'>Active</span>";
            } else {
                $model->status = "<span class='label label-danger'>In-active</span>";
            }


            echo DetailView::widget([
                'model' => $model,
                'attributes' => [
                    'id',
                    'username',
                    'email:email',
                    [
                        'format' => 'html',
                        'attribute' => 'status',
                    ],
                ],
            ]);
            ?>
        </div>
    </div>
</div>