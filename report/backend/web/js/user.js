function resetPassword(id) {
    if (confirm('Password will reset to same as username, confirm to reset?')) {
        jQuery("#btn-reset-pw").attr("disabled", true);
        jQuery("#btn-reset-pw").text("resetting...");
        jQuery.ajax({
            type: 'GET',
            dataType: 'json',
            url: URL_RESET_PASSWORD,
            data: {
                'id': id
            },
            success: function(data) {
                jQuery("#btn-reset-pw").attr("disabled", false);
                jQuery("#btn-reset-pw").text("Password Reset");
                alert(data.msg);
            }
        });
    }
}