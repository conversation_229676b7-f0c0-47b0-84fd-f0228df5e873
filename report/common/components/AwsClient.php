<?php

namespace common\components;

use Yii;
use Aws\Common\Aws;
use Aws\S3\Exception\S3Exception;
use yii\swiftmailer\Message;

class AwsClient {

    public $aws;
    public $_model = null;
    public $config = [];
    
    public function __construct() {
        $config = [
            'key' => Yii::$app->params['AWS_CONFIG']['AWS_APP_KEY'],
            'secret' => Yii::$app->params['AWS_CONFIG']['AWS_APP_SECRET'],
            'region' => Yii::$app->params['AWS_CONFIG']['region'],
        ];
        
        if (isset(Yii::$app->params['proxy'])) {
            $config['request.options'] = [
                'proxy' => Yii::$app->params['proxy']
            ];
        }

        $this->aws = Aws::factory($config);
    }

//    public function getS3PublicDomain() {
//        return Yii::$app->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['AWS_S3_PUBLIC_DOMAIN'];
//    }

//    public function getS3UserProfile() {
//        return Yii::$app->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['AWS_S3_BUCKET_MGC_USER_FILE'];
//    }

    public function getS3BucketDomain() {
        return Yii::$app->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['AWS_S3_BUCKET_MGC_DOMAIN'];
    }

    public function getS3Object() {
        return $this->aws->get('s3');
    }

    public function getSource() {
        return Yii::$app->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['AWS_S3_PUBLIC_DOMAIN'] . '/';
    }

    public function getMaxAge() {
        return Yii::$app->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['MAX_AGE'];
    }

    public function uploadImageToS3($name, $resource) {
        $s3Client = $this->getS3Object();
        $result['status'] = false;

        try {
            $uploadResult = $s3Client->putObject(array(
                'Bucket' => $this->getS3BucketDomain(),
                'Key' => $name,
                'Body' => fopen($resource->tempName, 'r'),
                'ACL' => 'public-read',
                'CacheControl' => $this->getMaxAge(),
            ));
            $result['status'] = true;
            $result['objectUrl'] = $uploadResult['ObjectURL'];
        } catch (S3Exception $e) {
            echo Yii::t('alert', 'There was an error uploading the file') . "\n";
        }

        return $result;
    }

    public function deleteImageFromS3($name) {
        $s3Client = $this->getS3Object();
        $result['status'] = false;

        try {
            $deleteResult = $s3Client->deleteObject(array(
                'Bucket' => $this->getS3BucketDomain(),
                'Key' => $name
            ));
            $result['status'] = true;
            $result['data'] = $deleteResult;
        } catch (S3Exception $e) {
            echo Yii::t('alert', 'There was an error uploading the file') . "\n";
        }

        return $result;
    }

    public function updateS3ObjectsCacheControl() {
        $s3Client = $this->getS3Object();
        $iterator = $s3Client->getIterator('ListObjects', array(
            'Bucket' => $this->getS3BucketDomain()
        ));
        foreach ($iterator as $object) {
            $key = $object['Key'];
            if (strpos($key, 'gallery/') !== false) {
                $result = $s3Client->copyObject(array(
                    'ACL' => 'public-read',
                    'Bucket' => $this->getS3BucketDomain(), 
                    'CacheControl' => $this->getMaxAge(),
                    //'ContentType' => 'image/jpeg', 
                    'CopySource' => urlencode($this->getS3BucketDomain() . '/' . $key),
                    'Key' => $key,
                    'MetadataDirective' => 'REPLACE'
                ));
                
            }
        }
        
    }
    
    public function sendEmail($emailInfo) {
        $msg = new yii\swiftmailer\Message();

        if (isset($emailInfo['file'])) {
            $msg->attach($emailInfo['file']['path'], ['fileName' => $emailInfo['file']['name']]);
        }
        
        $msg_content = $msg->setFrom($emailInfo['from'])
                            ->setTo($emailInfo['to'])
                            ->setSubject($emailInfo['subject'])
                            ->setHtmlBody($emailInfo['content'])
                            ->toString();
        
        $ses = $this->aws->get('ses');
        
        try {
            $ses->getCommand('sendRawEmail', array(
                'Source' => $emailInfo['from'],
//                'Destinations' => [
//                   'Addresses' => $emailInfo['to'],
//                ],
                'Destinations' => $emailInfo['to'],
                'RawMessage' => [
                    'Data' => base64_encode($msg_content)
                ],
            ))->execute();

            return true;
        } catch (S3Exception $e) {
            // The bucket couldn't be created
//            echo $e->getMessage();
        }
        
        return false;
    }

}
