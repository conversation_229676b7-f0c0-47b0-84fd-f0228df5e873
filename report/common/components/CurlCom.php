<?php

namespace common\components;

class CurlCom {
    const HTTP_POST = 'POST';

    public $ssl_verification = false;
    public $debug_mode = false;
    public $proxy = false;
    public $method, $request_url, $request_body, $response;

    /**
     * Custom CURLOPT settings.
     */
    public $curlopts = null;

    /**
     * Default useragent string to use.
     */
    public $useragent = 'CrewReportRequest/1.0';
    public $request_header = true;

    /**
     * The headers being sent in the request.
     */
    public $request_headers;

    /**
     * Default ERROR array to response.
     */
    private $error_array = array(
        'error_code' => 0,
        'error_message' => ''
    );

    public function readRSS($url) {
        $this->request_header = 0;

        return $this->sendRequest($url, '', 'GET', false);
    }

    public function sendGet($url, $data, $parse = false) {
        return $this->sendRequest($url, $data, 'GET', $parse);
    }

    public function sendPost($url, $data, $parse = false) {
        return $this->sendRequest($url, $data, 'POST', $parse);
    }

    private function sendRequest($url, $data, $method = 'POST', $parse = false) {
        $this->setRequestUrl($url);
        $this->setBody($data);
        $this->setMethod($method);

        $curl_handle = $this->prepRequest();
        $this->response = curl_exec($curl_handle);

        if ($this->response === false) {
            $curl_errno = curl_errno($curl_handle);
            $this->setError($curl_errno, 'cURL resource: ' . (string) $curl_handle . '; cURL error: ' . curl_error($curl_handle) . ' (cURL error code ' . $curl_errno . ')');
        }

        if ($parse) {
            $parsed_response = $this->processResponse($curl_handle, $this->response);
            curl_close($curl_handle);

            return $parsed_response;
        } else {
            curl_close($curl_handle);
        }

        return $this->response;
    }

    private function prepRequest() {
        $curl_handle = curl_init();

        // Set default options.
        curl_setopt($curl_handle, CURLOPT_URL, $this->request_url);
        curl_setopt($curl_handle, CURLOPT_FRESH_CONNECT, false);    //  TRUE to force the use of a new connection instead of a cached one. 
        curl_setopt($curl_handle, CURLOPT_HEADER, $this->request_header);
        curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl_handle, CURLOPT_TIMEOUT, 600);
        curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl_handle, CURLOPT_NOSIGNAL, true);
        curl_setopt($curl_handle, CURLOPT_USERAGENT, $this->useragent);

        // Verification of the SSL cert
        if ($this->ssl_verification) {
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, 2);
        } else {
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, 0);   // should be false
        }

        // Enable a proxy connection if requested.
        if ($this->proxy) {
            curl_setopt($curl_handle, CURLOPT_PROXY, $this->proxy);
        }

        // Debug mode
        if ($this->debug_mode === true) {
            curl_setopt($curl_handle, CURLOPT_VERBOSE, true);
        }

        // Process custom headers
        if ($this->request_header) {
            if (isset($this->request_headers) && count($this->request_headers)) {
                $temp_headers = array();

                if (!array_key_exists('Expect', $this->request_headers)) {
                    $this->request_headers['Expect'] = '';
                }

                foreach ($this->request_headers as $k => $v) {
                    $temp_headers[] = $k . ': ' . $v;
                }

                curl_setopt($curl_handle, CURLOPT_HTTPHEADER, $temp_headers);
            }
        }

        switch ($this->method) {
            case self::HTTP_POST:
                curl_setopt($curl_handle, CURLOPT_POST, true);
                curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $this->request_body);
                break;
            default: // Assumed GET
                curl_setopt($curl_handle, CURLOPT_CUSTOMREQUEST, $this->method);
                curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $this->request_body);
                break;
        }

        // Merge in the CURLOPTs
        if (isset($this->curlopts) && sizeof($this->curlopts) > 0) {
            foreach ($this->curlopts as $k => $v) {
                curl_setopt($curl_handle, $k, $v);
            }
        }

        return $curl_handle;
    }

    private function processResponse($curl_handle, $response_raw) {
        $response_excl_header = explode("\r\n\r\n", $response_raw);
        $response = end($response_excl_header);

        if ($this->isSerialize($response)) {
            $response = unserialize($response);
        } else if ($this->isJson($response)) {
            $response = json_decode($response);
        }

        return $response;
    }

    public function addHeader($key, $value) {
        $this->request_headers[$key] = $value;
        return $this;
    }
    
    function isSerialize($params) {
        return (@unserialize($params) !== FALSE) ? true : false;
    }
    
    function isJson($params) {
        return (@json_decode($params) !== NULL) ? true : false;
    }

    public function setBody($request_param) {
        if (!empty($request_param)) {
            if ($this->isJson($request_param)) {
                $this->addHeader('Accept', 'application/json');
                $this->addHeader('Content-Type', 'application/json');
            } else if (is_array($request_param)) {
                $request_param = http_build_query($request_param);
            }

            $this->addHeader('Content-Length', strlen($request_param));
        } else {
            $this->addHeader('Content-Length', '10');
        }

        $this->request_body = $request_param;
    }

    public function setCurlopts($curlopts) {
        $this->curlopts = $curlopts;
    }

    private function setError($errorCode, $errorMessage = '') {
        $this->error_array = array(
            'request_url' => $this->request_url,
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        );
    }

    public function getError() {
        return $this->error_array;
    }

    private function setMethod($method) {
        $this->method = strtoupper($method);
    }

    private function setRequestUrl($url) {
        $this->request_url = $url;
    }

    // found none is using this
    public function setProxy($bool) {
        $this->proxy = $bool;
    }

    public function getProxy() {
        return $this->proxy;
    }

}
