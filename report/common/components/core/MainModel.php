<?php

namespace common\components\core;

use Yii;

class MainModel extends \yii\db\ActiveRecord {

    protected $conn;

    public function __construct() {
        $this->conn = Yii::$app->get('db');
    }

    public function saveNewRecord($data, $returnPk = false) {
        // Making sure our PK is null before inserting it
        $table = $this->getTableSchema();
        $primaryKey = $table->primaryKey;

        if (is_array($primaryKey)) {
            foreach ($primaryKey as $primaryKeyValue) {
                $this->unsetAttributes(array($primaryKeyValue));
            }
        } else {
            $this->unsetAttributes(array($primaryKey));
        }

        foreach ($data as $field => $value) {
            //Checks whether this AR has the named attribute
            if ($this->hasAttribute($field)) {
                if ($value == null) {
                    $this->unsetAttributes(array($field));
                } else {
                    $this->setAttribute($field, $value);
                }
            }
        }

        // Insert requires it to be flagged as new record
        $this->isNewRecord = true;
        $status = $this->insert();

        if ($returnPk) {
            if (is_array($primaryKey)) {
                return $this;
            } else {
                return $this->$primaryKey;
            }
        } else {
            return $status;
        }
    }

    public function unsetAttributes($names = null) {
        if ($names === null)
            $names = $this->attributeNames();
        foreach ($names as $name)
            $this->$name = null;
    }
    
    public function checkRecordAndUpdateVersion($id, $pk, $params = []) {
        # is ID exist
        $current_data = $this->find()
                ->select('*')
                ->where(['id' => $id])
                ->orderBy(['version' => SORT_DESC])
                ->asArray()
                ->one();

        if ($current_data) {
            # is the content matched? (dim_pin_client's name, url and description)
            $matched_flag = true;
            foreach ($params as $key => $value) {
                if ($current_data[$key] == $value) {
                    // do nothing
                } else {
                    $matched_flag = false;

                    break;
                }
            }
            if ($matched_flag) {

                return $current_data[$pk];
                # return 'pc_id';
            } else {
                # new_version = current_version + 1;
                # update current pc_id's date_to to current date
                # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
                # return new_pc_id;
                $update_status = $this->updateAll(['date_to' => date('Y-m-d')], "$pk =$current_data[$pk] AND id= '" . $id . "'");
                if ($update_status) {
                    $pk_id = $this->saveNewRecord(array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => $current_data['version'] + 1]), true);
                    return $pk_id->primaryKey;
                }
            }
        } else {
            # new_version = 1;
            # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
            # return new_pc_id
            $pk_id = $this->saveNewRecord(array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => 1]), true);
            return $pk_id->primaryKey;
        }
    }

    public function checkRecordAndUpdateVersionNotCaseSensitive($id, $pk, $params = []) {
        # is ID exist
        $current_data = $this->find()
                ->select('*')
                ->where(['id' => $id])
                ->orderBy(['version' => SORT_DESC])
                ->asArray()
                ->one();

        if ($current_data) {
            # is the content matched? (dim_pin_client's name, url and description)
            $matched_flag = true;
            foreach ($params as $key => $value) {
                if (ctype_digit($value)) {
                    $current_data_key = $current_data[$key];
                    $value_from = $value;
                } else {
                    $current_data_key = strtolower($current_data[$key]);
                    $value_from = strtolower($value);
                }
                
                if ($current_data_key == $value_from) {
                    // do nothing
                } else {
                    $matched_flag = false;

                    break;
                }
            }
            if ($matched_flag) {

                return $current_data[$pk];
                # return 'pc_id';
            } else {
                # new_version = current_version + 1;
                # update current pc_id's date_to to current date
                # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
                # return new_pc_id;
                $update_status = $this->updateAll(['date_to' => date('Y-m-d')], "$pk =$current_data[$pk] AND id= '" . $id . "'");
                if ($update_status) {
                    $pk_id = $this->saveNewRecord(array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => $current_data['version'] + 1]), true);
                    return $pk_id->primaryKey;
                }
            }
        } else {
            # new_version = 1;
            # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
            # return new_pc_id
            $pk_id = $this->saveNewRecord(array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => 1]), true);
            return $pk_id->primaryKey;
        }
    }

    public function checkDateAndInsertIfNotExists($date_param, $params) {

        $current_row = $this->find()
                ->select('date_id')
                ->where(['date' => $date_param])
                ->asArray()
                ->one();

        if ($current_row) {
            return $current_row['date_id'];
        } else {

            $pk_id = $this->saveNewRecord($params, true);
            return $pk_id->primaryKey;
        }
    }

    public function doUpdateIfExists($pk_column, $id, $attributes) {
        $condition = (!is_array($id)) ? ['id' => $id] : $id;
        $current_row = $this->find()
                ->select($pk_column)
                ->where($condition)
                ->asArray()
                ->one();
        if ($current_row[$pk_column]) {
            $update_status = $this->updateAll($attributes, "$pk_column =$current_row[$pk_column]");
            return $current_row[$pk_column];
        } else {
            $pk_id = $this->saveNewRecord($attributes, true);
            return $pk_id->primaryKey;
        }
    }

}
