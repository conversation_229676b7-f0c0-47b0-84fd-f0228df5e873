<?php
return [
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'components' => [
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'forceTranslation' => true,
                    'basePath' => '@common/messages',
                ],
            ],
        ],
        'geoip' => [
            'class' => 'dpodium\yii2\geoip\components\CGeoIP',
            'mode' => 'STANDARD',
        ],
    ],
];
