<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "cron_logs".
 *
 * @property integer $log_id
 * @property string $log_time
 * @property string $log_user_messages
 * @property string $log_system_messages
 * @property string $log_field_name
 * @property string $log_from_value
 * @property string $log_to_value
 */
class CronLogs extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'cron_logs';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['log_time'], 'safe'],
            [['log_user_messages', 'log_system_messages', 'log_field_name', 'log_from_value', 'log_to_value'], 'required'],
            [['log_user_messages', 'log_system_messages'], 'string'],
            [['log_field_name', 'log_from_value', 'log_to_value'], 'string', 'max' => 255]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'log_id' => 'Log ID',
            'log_time' => 'Log Time',
            'log_user_messages' => 'Log User Messages',
            'log_system_messages' => 'Log System Messages',
            'log_field_name' => 'Log Field Name',
            'log_from_value' => 'Log From Value',
            'log_to_value' => 'Log To Value',
        ];
    }
}
