<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "cron_pointer".
 *
 * @property integer $last_end_id
 * @property integer $new_start_id
 * @property string $cron_last_process_date
 * @property integer $cron_last_processed_count
 * @property string $system_table
 */
class CronPointer extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'cron_pointer';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['cron_last_process_date', 'cron_last_processed_count', 'system_table'], 'required'],
            [['cron_last_processed_count'], 'integer'],
            [['cron_last_process_date'], 'safe'],
            [['new_start_id'], 'string', 'max' => 18],
            [['system_table'], 'string', 'max' => 32]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'new_start_id' => 'New Start ID',
            'cron_last_process_date' => 'Cron Last Process Date',
            'cron_last_processed_count' => 'Cron Last Processed Count',
            'system_table' => 'System Table',
        ];
    }
}
