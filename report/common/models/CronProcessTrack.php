<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "cron_process_track".
 *
 * @property integer $cron_process_track_in_action
 * @property string $cron_process_track_start_date
 * @property integer $cron_process_track_failed_attempt
 * @property string $cron_process_track_filename
 */
class CronProcessTrack extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'cron_process_track';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['cron_process_track_in_action', 'cron_process_track_failed_attempt'], 'integer'],
            [['cron_process_track_start_date'], 'safe'],
            [['cron_process_track_filename'], 'required'],
            [['cron_process_track_filename'], 'string', 'max' => 255]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'cron_process_track_in_action' => 'Cron Process Track In Action',
            'cron_process_track_start_date' => 'Cron Process Track Start Date',
            'cron_process_track_failed_attempt' => 'Cron Process Track Failed Attempt',
            'cron_process_track_filename' => 'Cron Process Track Filename',
        ];
    }
}
