<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "cron_retry".
 *
 * @property integer $id
 * @property string $action
 * @property string $task
 * @property string $key_1
 * @property integer $val_1
 * @property string $key_2
 * @property integer $val_2
 * @property string $json_str
 * @property string $create_datetime
 */
class CronRetry extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'cron_retry';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['val_1', 'val_2', 'json_str'], 'required'],
            [['val_1', 'val_2'], 'integer'],
            [['json_str'], 'string'],
            [['create_datetime'], 'safe'],
            [['action', 'task'], 'string', 'max' => 64],
            [['key_1', 'key_2'], 'string', 'max' => 255]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'action' => 'Action',
            'task' => 'Task',
            'key_1' => 'Key 1',
            'val_1' => 'Val 1',
            'key_2' => 'Key 2',
            'val_2' => 'Val 2',
            'json_str' => 'Json Str',
            'create_datetime' => 'Create Datetime',
        ];
    }
}
