<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_date".
 *
 * @property string $date_id
 * @property integer $year_month
 * @property integer $year_week
 * @property integer $year_day
 * @property integer $year_quarter
 * @property integer $year
 * @property string $date
 *
 * @property FactPinDistribution[] $factPinDistributions
 * @property FactPinMgcRedemption[] $factPinMgcRedemptions
 */
class DimDate extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_date';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['year_month', 'year_week', 'year_day', 'year_quarter', 'year', 'date'], 'required'],
            [['year_month', 'year_week', 'year_day', 'year_quarter', 'year'], 'integer'],
            [['date'], 'safe']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'date_id' => 'Date ID',
            'year_month' => 'Year Month',
            'year_week' => 'Year Week',
            'year_day' => 'Year Day',
            'year_quarter' => 'Year Quarter',
            'year' => 'Year',
            'date' => 'Date',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasMany(FactPinDistribution::className(), ['fpd_date_id' => 'date_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinMgcRedemptions()
    {
        return $this->hasMany(FactPinMgcRedemption::className(), ['fpmr_date_id' => 'date_id']);
    }
}
