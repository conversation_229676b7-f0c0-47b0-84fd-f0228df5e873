<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_geo_info".
 *
 * @property string $gi_id
 * @property string $ip
 * @property string $ip_country_iso2
 *
 * @property FactPinMgcRedemption[] $factPinMgcRedemptions
 */
class DimGeoInfo extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_geo_info';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['ip', 'ip_country_iso2'], 'required'],
            [['ip'], 'string', 'max' => 32],
            [['ip_country_iso2'], 'string', 'max' => 2]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'gi_id' => 'Gi ID',
            'ip' => 'Ip',
            'ip_country_iso2' => 'Ip Country Iso2',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinMgcRedemptions()
    {
        return $this->hasMany(FactPinMgcRedemption::className(), ['fpmr_gi_id' => 'gi_id']);
    }
}
