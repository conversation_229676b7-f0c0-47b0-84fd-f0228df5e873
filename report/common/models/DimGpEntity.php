<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_gp_entity".
 *
 * @property integer $gp_entity_id
 * @property string $company_code
 * @property string $company_name
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 */
class DimGpEntity extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_gp_entity';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'company_name', 'date_from', 'date_to', 'version'], 'required'],
            [['date_from', 'date_to'], 'safe'],
            [['version'], 'integer'],
            [['id'], 'string', 'max' => 2],
            [['company_name'], 'string', 'max' => 32]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'gpe_id' => 'Gp Entity ID',
            'id' => 'Company Code',
            'company_name' => 'Company Name',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGpCosts()
    {
        return $this->hasMany(GpCost::className(), ['gpc_gpe_id' => 'gpe_id']);
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGpCostsReleased()
    {
        return $this->hasMany(GpCostReleased::className(), ['gpcr_gpe_id' => 'gpe_id']);
    }
}
