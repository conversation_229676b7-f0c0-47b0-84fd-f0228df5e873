<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_ogc_crew".
 *
 * @property string $oc_id
 * @property integer $id
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $type
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 *
 * @property DimPinBatchRequest[] $dimPinBatchRequests
 */
class DimOgcCrew extends  \common\components\core\MainModel
{
    public $dimogccrew_id_mapping_array = array();
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_ogc_crew';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'first_name', 'last_name', 'email', 'type', 'date_from', 'date_to', 'version'], 'required'],
            [['id', 'version'], 'integer'],
            [['date_from', 'date_to'], 'safe'],
            [['first_name', 'last_name'], 'string', 'max' => 45],
            [['email'], 'string', 'max' => 65],
            [['type'], 'string', 'max' => 16]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'oc_id' => 'Oc ID',
            'id' => 'ID',
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'email' => 'Email',
            'type' => 'Type',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDimPinBatchRequests()
    {
        return $this->hasMany(DimPinBatchRequest::className(), ['request_by' => 'oc_id']);
    }
    
}
