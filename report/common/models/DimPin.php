<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin".
 *
 * @property string $pin_id
 * @property integer $id
 * @property string $serialno
 * @property string $currency
 * @property string $deno
 * @property string $valid_start
 * @property string $valid_end
 *
 * @property FactPinDistribution[] $factPinDistributions
 * @property FactPinMgcRedemption[] $factPinMgcRedemptions
 */
class DimPin extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'serialno', 'currency', 'deno', 'valid_start', 'valid_end'], 'required'],
            [['id'], 'integer'],
            [['deno'], 'number'],
            [['valid_start', 'valid_end'], 'safe'],
            [['serialno'], 'string', 'max' => 18],
            [['currency'], 'string', 'max' => 3]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pin_id' => 'Pin ID',
            'id' => 'ID',
            'serialno' => 'Serialno',
            'currency' => 'Currency',
            'deno' => 'Deno',
            'valid_start' => 'Valid Start',
            'valid_end' => 'Valid End',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasOne(FactPinDistribution::className(), ['fpd_pin_id' => 'pin_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinMgcRedemptions()
    {
        return $this->hasMany(FactPinMgcRedemption::className(), ['fpmr_pin_id' => 'pin_id']);
    }
}
