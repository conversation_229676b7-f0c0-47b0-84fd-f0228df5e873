<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin_batch_request".
 *
 * @property string $pbr_id
 * @property integer $id
 * @property string $description
 * @property string $title
 * @property string $datetime
 * @property string $approved_by
 * @property string $request_by
 *
 * @property DimOgcCrew $approvedBy
 * @property DimOgcCrew $requestBy
 * @property FactPinDistribution[] $factPinDistributions
 */
class DimPinBatchRequest extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_batch_request';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'description', 'title', 'datetime', 'approved_by', 'request_by'], 'required'],
            [['id', 'approved_by', 'request_by'], 'integer'],
            [['description'], 'string'],
            [['datetime'], 'safe'],
            [['title'], 'string', 'max' => 65]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pbr_id' => 'Pbr ID',
            'id' => 'ID',
            'description' => 'Description',
            'title' => 'Title',
            'datetime' => 'Datetime',
            'approved_by' => 'Approved By',
            'request_by' => 'Request By',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getApprovedBy()
    {
        return $this->hasOne(DimOgcCrew::className(), ['oc_id' => 'approved_by']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRequestBy()
    {
        return $this->hasOne(DimOgcCrew::className(), ['oc_id' => 'request_by']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasMany(FactPinDistribution::className(), ['fpd_pbr_id' => 'pbr_id']);
    }
}
