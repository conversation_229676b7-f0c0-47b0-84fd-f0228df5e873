<?php

namespace common\models;
use common\components\core\MainModel;
use Yii;

/**
 * This is the model class for table "dim_pin_client".
 *
 * @property string $pc_id
 * @property integer $id
 * @property string $name
 * @property string $url
 * @property string $description
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 *
 * @property FactPinDistribution[] $factPinDistributions
 */
class DimPinClient extends  \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_client';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'name', 'url', 'description', 'date_from', 'date_to', 'version'], 'required'],
            [['id', 'version'], 'integer'],
            [['description'], 'string'],
            [['date_from', 'date_to'], 'safe'],
            [['name'], 'string', 'max' => 65],
            [['url'], 'string', 'max' => 255]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pc_id' => 'Pc ID',
            'id' => 'ID',
            'name' => 'Name',
            'url' => 'Url',
            'description' => 'Description',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasMany(FactPinDistribution::className(), ['fpd_pc_id' => 'pc_id']);
    }
}
