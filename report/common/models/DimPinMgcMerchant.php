<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin_mgc_merchant".
 *
 * @property string $pmm_id
 * @property string $id
 * @property string $name
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 *
 * @property FactPinMgcRedemption[] $factPinMgcRedemptions
 */
class DimPinMgcMerchant extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_mgc_merchant';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'name', 'date_from', 'date_to', 'version'], 'required'],
            [['date_from', 'date_to'], 'safe'],
            [['version'], 'integer'],
            [['id'], 'string', 'max' => 14],
            [['name'], 'string', 'max' => 64]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pmm_id' => 'Pmm ID',
            'id' => 'ID',
            'name' => 'Name',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinMgcRedemptions()
    {
        return $this->hasMany(FactPinMgcRedemption::className(), ['fpmr_pmm_id' => 'pmm_id']);
    }
}
