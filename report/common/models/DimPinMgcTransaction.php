<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin_mgc_transaction".
 *
 * @property string $pmt_id
 * @property string $id
 * @property string $type
 * @property string $ref_id
 * @property string $currency
 * @property string $datetime
 *
 * @property FactPinMgcRedemption[] $factPinMgcRedemptions
 */
class DimPinMgcTransaction extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_mgc_transaction';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'type', 'ref_id', 'currency', 'datetime'], 'required'],
            [['datetime'], 'safe'],
            [['id'], 'string', 'max' => 18],
            [['type'], 'string', 'max' => 16],
            [['ref_id'], 'string', 'max' => 32],
            [['currency'], 'string', 'max' => 3]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pmt_id' => 'Pmt ID',
            'id' => 'ID',
            'type' => 'Type',
            'ref_id' => 'Ref ID',
            'currency' => 'Currency',
            'datetime' => 'Datetime',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinMgcRedemptions()
    {
        return $this->hasMany(FactPinMgcRedemption::className(), ['fpmr_pmt_id' => 'pmt_id']);
    }
}
