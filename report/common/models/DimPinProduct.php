<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin_product".
 *
 * @property string $pp_id
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 *
 * @property FactPinDistribution[] $factPinDistributions
 */
class DimPinProduct extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_product';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'name', 'date_from', 'date_to', 'version'], 'required'],
            [['id', 'version'], 'integer'],
            [['description'], 'string'],
            [['date_from', 'date_to'], 'safe'],
            [['name'], 'string', 'max' => 65]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pp_id' => 'Pp ID',
            'id' => 'ID',
            'name' => 'Name',
            'description' => 'Description',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasMany(FactPinDistribution::className(), ['fpd_pp_id' => 'pp_id']);
    }
}
