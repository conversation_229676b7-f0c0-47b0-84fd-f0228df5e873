<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "dim_pin_reseller".
 *
 * @property string $pr_id
 * @property integer $id
 * @property string $name
 * @property string $email
 * @property string $tel
 * @property string $code
 * @property string $date_from
 * @property string $date_to
 * @property integer $version
 *
 * @property FactPinDistribution[] $factPinDistributions
 */
class DimPinReseller extends \common\components\core\MainModel
{
        //public $DimPinReseller_name;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dim_pin_reseller';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'name', 'email', 'tel', 'code', 'date_from', 'date_to', 'version'], 'required'],
            [['id', 'version'], 'integer'],
            [['date_from', 'date_to'], 'safe'],
            [['name', 'email', 'tel'], 'string', 'max' => 65],
            [['code'], 'string', 'max' => 16]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'pr_id' => 'Pr ID',
            'id' => 'ID',
            'name' => 'Name',
            'email' => 'Email',
            'tel' => 'Tel',
            'code' => 'Code',
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'version' => 'Version',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactPinDistributions()
    {
        return $this->hasMany(FactPinDistribution::className(), ['fpd_pr_id' => 'pr_id']);
    }
}
