<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "fact_pin_distribution".
 *
 * @property string $fpd_pin_id
 * @property string $fpd_pbr_id
 * @property string $fpd_pr_id
 * @property string $fpd_date_id
 * @property string $fpd_pp_id
 * @property string $fpd_pc_id
 * @property string $pin_value
 *
 * @property DimPin $fpdPin
 * @property DimPinReseller $fpdPr
 * @property DimDate $fpdDate
 * @property DimPinProduct $fpdPp
 * @property DimPinClient $fpdPc
 * @property DimPinBatchRequest $fpdPbr
 */
class FactPinDistribution extends \common\components\core\MainModel
{
    
   
   
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'fact_pin_distribution';
    }

    
    /**
     * @inheritdoc
     */
    public function rules() {
        return [
            [['fpd_pin_id', 'fpd_pbr_id', 'fpd_pr_id', 'fpd_date_id', 'fpd_pp_id', 'fpd_pc_id', 'pin_value'], 'required'],
            [['fpd_pin_id', 'fpd_pbr_id', 'fpd_pr_id', 'fpd_date_id', 'fpd_pp_id', 'fpd_pc_id'], 'integer'],
            [['pin_value'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'fpd_pin_id' => 'Fpd Pin ID',
            'fpd_pbr_id' => 'Fpd Pbr ID',
            'fpd_pr_id' => 'Fpd Pr ID',
            'fpd_date_id' => 'Fpd Date ID',
            'fpd_pp_id' => 'Fpd Pp ID',
            'fpd_pc_id' => 'Fpd Pc ID',
            'pin_value' => 'Pin Value',
           
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdPin()
    {
        return $this->hasOne(DimPin::className(), ['pin_id' => 'fpd_pin_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdPr()
    {
        return $this->hasOne(DimPinReseller::className(), ['pr_id' => 'fpd_pr_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdDate()
    {
        return $this->hasOne(DimDate::className(), ['date_id' => 'fpd_date_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdPp()
    {
        return $this->hasOne(DimPinProduct::className(), ['pp_id' => 'fpd_pp_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdPc()
    {
        return $this->hasOne(DimPinClient::className(), ['pc_id' => 'fpd_pc_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpdPbr()
    {
        return $this->hasOne(DimPinBatchRequest::className(), ['pbr_id' => 'fpd_pbr_id']);
    }
}
