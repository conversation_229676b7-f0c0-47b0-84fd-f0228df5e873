<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "fact_pin_mgc_redemption".
 *
 * @property string $fpmr_pin_id
 * @property string $fpmr_pmm_id
 * @property string $fpmr_gi_id
 * @property string $fpmr_pmt_id
 * @property string $fpmr_date_id
 * @property string $pin_value
 *
 * @property DimPin $fpmrPin
 * @property DimPinMgcMerchant $fpmrPmm
 * @property DimGeoInfo $fpmrGi
 * @property DimPinMgcTransaction $fpmrPmt
 * @property DimDate $fpmrDate
 */
class FactPinMgcRedemption extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'fact_pin_mgc_redemption';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['fpmr_pin_id', 'fpmr_pmm_id', 'fpmr_gi_id', 'fpmr_pmt_id', 'fpmr_date_id', 'pin_value'], 'required'],
            [['fpmr_pin_id', 'fpmr_pmm_id', 'fpmr_gi_id', 'fpmr_pmt_id', 'fpmr_date_id'], 'integer'],
            [['pin_value'], 'number']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'fpmr_pin_id' => 'Fpmr Pin ID',
            'fpmr_pmm_id' => 'Fpmr Pmm ID',
            'fpmr_gi_id' => 'Fpmr Gi ID',
            'fpmr_pmt_id' => 'Fpmr Pmt ID',
            'fpmr_date_id' => 'Fpmr Date ID',
            'pin_value' => 'Pin Value',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpmrPin()
    {
        return $this->hasOne(DimPin::className(), ['pin_id' => 'fpmr_pin_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpmrPmm()
    {
        return $this->hasOne(DimPinMgcMerchant::className(), ['pmm_id' => 'fpmr_pmm_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpmrGi()
    {
        return $this->hasOne(DimGeoInfo::className(), ['gi_id' => 'fpmr_gi_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpmrPmt()
    {
        return $this->hasOne(DimPinMgcTransaction::className(), ['pmt_id' => 'fpmr_pmt_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFpmrDate()
    {
        return $this->hasOne(DimDate::className(), ['date_id' => 'fpmr_date_id']);
    }
}
