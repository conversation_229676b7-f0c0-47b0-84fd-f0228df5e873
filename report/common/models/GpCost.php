<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "gp_cost".
 *
 * @property integer $id
 * @property integer $gp_order_id
 * @property string $purchase_type
 * @property integer $purchase_id
 * @property string $purchase_currency
 * @property string $delivered_quantity
 * @property string $unit_cost_in_order_currency
 * @property string $unit_cost_in_purchase_currency
 * @property string $unit_cost_in_usd
 * @property string $total_cost_in_usd
 * @property string $purchase_conversion_rate_in_order_currency
 * @property string $purchase_conversion_rate_in_usd
 * @property string $total_cost_in_purchase_currency
 * @property string $total_cost_in_order_currency
 * @property string $extra_info
 *
 * @property GpOrder $gpOrder
 */
class GpCost extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gp_cost';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['gp_order_id', 'purchase_type', 'purchase_id', 'purchase_currency', 'gpc_gpe_id', 'delivered_quantity', 'unit_cost_in_order_currency', 'unit_cost_in_purchase_currency', 'unit_cost_in_usd', 'total_cost_in_usd', 'purchase_conversion_rate_in_order_currency', 'purchase_conversion_rate_in_usd', 'total_cost_in_purchase_currency', 'total_cost_in_order_currency', 'total_sc_in_order_currency', 'total_payment_in_order_currency', 'total_sc_in_usd', 'total_payment_in_usd', 'extra_info'], 'required', 'skipOnEmpty' => true],
            [['gp_order_id', 'purchase_id', 'delivered_quantity'], 'integer'],
            [['unit_cost_in_order_currency', 'unit_cost_in_purchase_currency', 'unit_cost_in_usd', 'total_cost_in_usd', 'purchase_conversion_rate_in_order_currency', 'purchase_conversion_rate_in_usd', 'total_cost_in_purchase_currency', 'total_cost_in_order_currency', 'total_sc_in_order_currency', 'total_payment_in_order_currency', 'total_sc_in_usd', 'total_payment_in_usd'], 'default', 'value' => 0],
            [['unit_cost_in_order_currency', 'unit_cost_in_purchase_currency', 'unit_cost_in_usd', 'total_cost_in_usd', 'purchase_conversion_rate_in_order_currency', 'purchase_conversion_rate_in_usd', 'total_cost_in_purchase_currency', 'total_cost_in_order_currency', 'total_sc_in_order_currency', 'total_payment_in_order_currency', 'total_sc_in_usd', 'total_payment_in_usd'], 'number'],
            [['extra_info'], 'string'],
            [['purchase_type'], 'string', 'max' => 4],
            [['purchase_currency'], 'string', 'max' => 3]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'gp_order_id' => 'Gp Order ID',
            'purchase_type' => 'Purchase Type',
            'purchase_id' => 'Purchase ID',
            'purchase_currency' => 'Purchase Currency',
            'gpc_gpe_id' => 'PO Order Original Entity',
            'delivered_quantity' => 'Delivered Quantity',
            'unit_cost_in_order_currency' => 'Unit Cost In Order Currency',
            'unit_cost_in_purchase_currency' => 'Unit Cost In Purchase Currency',
            'unit_cost_in_usd' => 'Unit Cost In Usd',
            'total_cost_in_usd' => 'Total Cost In Usd',
            'purchase_conversion_rate_in_order_currency' => 'Purchase Conversion Rate In Order Currency',
            'purchase_conversion_rate_in_usd' => 'Purchase Conversion Rate In Usd',
            'total_cost_in_purchase_currency' => 'Total Cost In Purchase Currency',
            'total_cost_in_order_currency' => 'Total Cost In Order Currency',
            'total_sc_in_order_currency' => 'Total Store Credit In Order Currency',
            'total_payment_in_order_currency' => 'Total Payment In Order Currency',
            'total_sc_in_usd' => 'Total Store Credit In Usd',
            'total_payment_in_usd' => 'Total Payment In Usd',
            'extra_info' => 'Extra Info',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGpOrder()
    {
        return $this->hasOne(GpOrder::className(), ['id' => 'gp_order_id']);
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGpEntity()
    {
        return $this->hasOne(DimGpEntity::className(), ['gpe_id' => 'gpc_gpe_id']);
    }
}
