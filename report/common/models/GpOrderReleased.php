<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "gp_order_released".
 *
 * @property integer $id
 * @property string $released_start_datetime
 * @property string $released_end_datetime
 * @property string $order_id
 * @property string $order_currency
 * @property string $payment_method
 * @property string $products_id
 * @property string $product_name
 * @property string $unit_price_in_order_currency
 * @property string $margin_in_order_currency
 * @property string $margin_percentage_in_order_currency
 * @property string $order_conversion_rate_in_usd
 * @property string $total_released_in_order_currency
 * @property string $total_purchase_in_purchase_currency
 * @property string $total_purchase_in_usd
 * @property string $total_released_in_usd
 * @property string $total_released_quantity
 * @property string $margin_in_usd
 * @property string $margin_percentage_in_usd
 */
class GpOrderReleased extends \common\components\core\MainModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gp_order_released';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['released_start_datetime', 'released_end_datetime'], 'safe'],
            [['order_id', 'order_currency', 'products_id', 'product_name', 'margin_percentage_in_order_currency', 'order_conversion_rate_in_usd', 'total_released_in_order_currency', 'total_purchase_in_purchase_currency', 'total_released_quantity', 'margin_percentage_in_usd'], 'required', 'skipOnEmpty' => true],
            [['order_id', 'products_id', 'total_released_quantity'], 'integer'],
            [['unit_price_in_order_currency', 'margin_in_order_currency', 'margin_percentage_in_order_currency', 'order_conversion_rate_in_usd', 'total_released_in_order_currency', 'total_purchase_in_purchase_currency', 'total_purchase_in_usd', 'total_released_in_usd', 'margin_in_usd', 'margin_percentage_in_usd'], 'default', 'value' => 0],
            [['unit_price_in_order_currency', 'margin_in_order_currency', 'margin_percentage_in_order_currency', 'order_conversion_rate_in_usd', 'total_released_in_order_currency', 'total_purchase_in_purchase_currency', 'total_purchase_in_usd', 'total_released_in_usd', 'margin_in_usd', 'margin_percentage_in_usd'], 'number'],
            [['order_currency'], 'string', 'max' => 3],
            [['payment_method'], 'string', 'max' => 255],
            [['product_name'], 'string', 'max' => 255]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'released_start_datetime' => 'Released Start Datetime',
            'released_end_datetime' => 'Released End Datetime',
            'order_id' => 'Order ID',
            'order_currency' => 'Order Currency',
            'payment_method' => 'Payment Method',
            'products_id' => 'Products ID',
            'product_name' => 'Product Name',
            'unit_price_in_order_currency' => 'Unit Price In Order Currency',
            'margin_in_order_currency' => 'Margin In Order Currency',
            'margin_percentage_in_order_currency' => 'Margin Percentage In Order Currency',
            'order_conversion_rate_in_usd' => 'Order Conversion Rate In Usd',
            'total_released_in_order_currency' => 'Total Released In Order Currency',
            'total_purchase_in_purchase_currency' => 'Total Purchase In Purchase Currency',
            'total_purchase_in_usd' => 'Total Purchase In Usd',
            'total_released_in_usd' => 'Total Released In Usd',
            'total_released_quantity' => 'Total Released Quantity',
            'margin_in_usd' => 'Margin In Usd',
            'margin_percentage_in_usd' => 'Margin Percentage In Usd',
        ];
    }
    
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getGpCostsReleased()
    {
        return $this->hasMany(GpCostReleased::className(), ['gpo_released_id' => 'id']);
    }
}
