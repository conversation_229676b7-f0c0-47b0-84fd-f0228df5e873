<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "mgc_redeem_history".
 *
 * @property string $trans_id
 * @property string $pin_request_date
 * @property string $merchant_id
 * @property string $merchant_name
 * @property string $merchant_ref_id
 * @property string $serial
 * @property string $currency
 * @property string $trans_amount
 * @property string $ip
 * @property string $batch_id
 * @property string $reseller_id
 * @property string $reseller_name
 * @property string $reseller_code
 * @property string $start_date
 * @property string $end_date
 */
class MgcRedeemHistory extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'mgc_redeem_history1';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['trans_id', 'pin_request_date', 'merchant_id', 'merchant_name', 'merchant_ref_id', 'serial_number', 'currency', 'ip', 'batch_id', 'reseller_name', 'reseller_code','pin_used_date'], 'required'],
            [['pin_request_date','pin_used_date', 'start_date', 'end_date'], 'safe'],
            [['trans_amount'], 'number'],
            [['batch_id', 'reseller_id'], 'integer'],
            [['trans_id', 'serial_number'], 'string', 'max' => 18],
            [['merchant_id'], 'string', 'max' => 14],
            [['merchant_name'], 'string', 'max' => 64],
            [['merchant_ref_id'], 'string', 'max' => 32],
            [['currency'], 'string', 'max' => 3],
            [['ip'], 'string', 'max' => 15],
            [['reseller_name'], 'string', 'max' => 65],
            [['reseller_code'], 'string', 'max' => 16]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'trans_id' => 'Trans ID',
            'pin_request_date' => 'Pin Request Date',
            'pin_used_date'=>'Pin Used Date',
            'merchant_id' => 'Merchant ID',
            'merchant_name' => 'Merchant Name',
            'merchant_ref_id' => 'Merchant Ref ID',
            'serial_number' => 'Serial',
            'currency' => 'Currency',
            'trans_amount' => 'Trans Amount',
            'ip' => 'Ip',
            'batch_id' => 'Batch ID',
            'reseller_id' => 'Reseller ID',
            'reseller_name' => 'Reseller Name',
            'reseller_code' => 'Reseller Code',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            
        ];
    }
    
    
    
     
}
