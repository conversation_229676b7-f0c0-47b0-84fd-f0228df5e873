<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "redeem_game_card".
 *
 * @property string $trans_id
 * @property string $game_card_serial
 * @property string $game_card_currency
 * @property string $game_card_amount
 * @property string $redeem_currency
 * @property string $redeem_amount
 * @property string $redeem_ts
 */
class RedeemGameCard extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    
     public static function getDb()
    {
        return Yii::$app->get('db_mgc');
    }
    
    public static function tableName()
    {
        return 'redeem_game_card';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['trans_id', 'game_card_serial', 'game_card_currency', 'game_card_amount', 'redeem_currency', 'redeem_amount', 'redeem_ts'], 'required'],
            [['game_card_amount', 'redeem_amount'], 'number'],
            [['redeem_ts'], 'integer'],
            [['trans_id'], 'string', 'max' => 18],
            [['game_card_serial'], 'string', 'max' => 32],
            [['game_card_currency', 'redeem_currency'], 'string', 'max' => 3]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'trans_id' => Yii::t('app', 'Trans ID'),
            'game_card_serial' => Yii::t('app', 'Game Card Serial'),
            'game_card_currency' => Yii::t('app', 'Game Card Currency'),
            'game_card_amount' => Yii::t('app', 'Game Card Amount'),
            'redeem_currency' => Yii::t('app', 'Redeem Currency'),
            'redeem_amount' => Yii::t('app', 'Redeem Amount'),
            'redeem_ts' => Yii::t('app', 'Redeem Ts'),
        ];
    }
}
