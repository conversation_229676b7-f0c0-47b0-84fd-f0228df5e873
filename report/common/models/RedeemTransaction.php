<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "redeem_transaction".
 *
 * @property string $trans_id
 * @property string $trans_method
 * @property string $trans_currency
 * @property string $trans_amount
 * @property string $trans_create_ts
 * @property string $trans_complete_ts
 * @property integer $trans_status
 * @property string $merchant_id
 * @property string $merchant_name
 * @property string $merchant_ref_id
 */
class RedeemTransaction extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    
    public static function getDb()
    {
        return Yii::$app->get('db_mgc');
    }
    
    public static function tableName()
    {
        return 'redeem_transaction';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['trans_id', 'trans_method', 'trans_currency', 'trans_create_ts', 'merchant_id', 'merchant_name', 'merchant_ref_id'], 'required'],
            [['trans_amount'], 'number'],
            [['trans_create_ts', 'trans_complete_ts', 'trans_status'], 'integer'],
            [['trans_id'], 'string', 'max' => 18],
            [['trans_method'], 'string', 'max' => 16],
            [['trans_currency'], 'string', 'max' => 3],
            [['merchant_id'], 'string', 'max' => 14],
            [['merchant_name'], 'string', 'max' => 64],
            [['merchant_ref_id'], 'string', 'max' => 32]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'trans_id' => Yii::t('app', 'Trans ID'),
            'trans_method' => Yii::t('app', 'Trans Method'),
            'trans_currency' => Yii::t('app', 'Trans Currency'),
            'trans_amount' => Yii::t('app', 'Trans Amount'),
            'trans_create_ts' => Yii::t('app', 'Trans Create Ts'),
            'trans_complete_ts' => Yii::t('app', 'Trans Complete Ts'),
            'trans_status' => Yii::t('app', 'Trans Status'),
            'merchant_id' => Yii::t('app', 'Merchant ID'),
            'merchant_name' => Yii::t('app', 'Merchant Name'),
            'merchant_ref_id' => Yii::t('app', 'Merchant Ref ID'),
        ];
    }
}
