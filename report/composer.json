{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "repositories": [{"type": "composer", "url": "https://packagist.org"}, {"type": "composer", "url": "http://satis.offgamers.biz"}], "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=5.4.0", "yiisoft/yii2": "2.0.4", "yiisoft/yii2-bootstrap": "2.0.3", "yiisoft/yii2-swiftmailer": "2.0.3", "kartik-v/yii2-widgets": "dev-master", "aws/aws-sdk-php": "2.6.16", "dpodiumdev/yii2-widgets": "*", "dpodiumdev/yii2-admin": "*", "recaptcha/php5": "dev-master", "kartik-v/yii2-grid": "dev-master", "kartik-v/yii2-mpdf": "@dev", "kartik-v/yii2-export": "@dev", "kartik-v/yii2-builder": "@dev", "kartik-v/yii2-datecontrol": "@dev", "nterms/yii2-pagesize-widget": "*", "kartik-v/yii2-ipinfo": "@dev", "dpodium/yii2-geoip": "*", "c006/yii2-migration-utility": "dev-master"}, "require-dev": {"yiisoft/yii2-codeception": "2.0.3", "yiisoft/yii2-debug": "2.0.3", "yiisoft/yii2-gii": "2.0.3", "yiisoft/yii2-faker": "*"}, "config": {"process-timeout": 1800}, "extra": {"asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}}