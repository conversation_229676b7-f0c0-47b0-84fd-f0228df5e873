<?php

use yii\db\Schema;
use yii\db\Migration;

class m151119_080627_upgrade extends Migration {

    public function up() {

        $tables = Yii::$app->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";
        $tableOptions_mssql = "";
        $tableOptions_pgsql = "";
        $tableOptions_sqlite = "";
        /* MYSQL */
        if (!in_array('cron_pointer', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_pointer}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'new_start_id' => 'VARCHAR(18) NOT NULL',
                    'cron_last_process_date' => 'DATETIME NOT NULL',
                    'cron_last_processed_count' => 'INT(11) NOT NULL',
                    'system_table' => 'VARCHAR(32) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('cron_process_track', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_process_track}}', [
                    'id' => 'MEDIUMINT(4) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'cron_process_track_in_action' => 'TINYINT(4) NOT NULL',
                    'cron_process_track_start_date' => 'DATETIME NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                    'cron_process_track_failed_attempt' => 'INT(11) NOT NULL',
                    'cron_process_track_filename' => 'VARCHAR(255) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('cron_logs', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_logs}}', [
                    'log_id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`log_id`)',
                    'log_time' => 'DATETIME NULL',
                    'log_user_messages' => 'TEXT NOT NULL',
                    'log_system_messages' => 'TEXT NOT NULL',
                    'log_field_name' => 'VARCHAR(255) NOT NULL',
                    'log_from_value' => 'VARCHAR(255) NOT NULL',
                    'log_to_value' => 'VARCHAR(255) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }



        if (!in_array('dim_date', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_date}}', [
                    'date_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`date_id`)',
                    'year_month' => 'MEDIUMINT(6) NOT NULL',
                    'year_week' => 'MEDIUMINT(6) NOT NULL',
                    'year_day' => 'MEDIUMINT(7) NOT NULL',
                    'year_quarter' => 'MEDIUMINT(5) NOT NULL',
                    'year' => 'SMALLINT(4) NOT NULL',
                    'date' => 'DATE NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_geo_info', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_geo_info}}', [
                    'gi_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`gi_id`)',
                    'ip' => 'VARCHAR(32) NOT NULL',
                    'ip_country_iso2' => 'CHAR(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_ogc_crew', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_ogc_crew}}', [
                    'oc_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`oc_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'first_name' => 'VARCHAR(45) NOT NULL',
                    'last_name' => 'VARCHAR(45) NOT NULL',
                    'email' => 'VARCHAR(65) NOT NULL',
                    'type' => 'VARCHAR(16) NOT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin}}', [
                    'pin_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pin_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'serialno' => 'VARCHAR(18) NOT NULL',
                    'currency' => 'CHAR(3) NOT NULL',
                    'deno' => 'DECIMAL(10,2) NOT NULL',
                    'valid_start' => 'DATE NOT NULL',
                    'valid_end' => 'DATE NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_batch_request', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_batch_request}}', [
                    'pbr_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pbr_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'description' => 'TINYTEXT NOT NULL',
                    'title' => 'VARCHAR(65) NOT NULL',
                    'datetime' => 'DATETIME NOT NULL',
                    'approved_by' => 'INT(11) UNSIGNED NOT NULL',
                    'request_by' => 'INT(11) UNSIGNED NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_client', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_client}}', [
                    'pc_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pc_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'name' => 'VARCHAR(65) NOT NULL',
                    'url' => 'VARCHAR(255) NOT NULL',
                    'description' => 'MEDIUMTEXT NOT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_mgc_merchant', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_mgc_merchant}}', [
                    'pmm_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pmm_id`)',
                    'id' => 'VARCHAR(14) NOT NULL',
                    'name' => 'VARCHAR(64) NOT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_mgc_transaction', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_mgc_transaction}}', [
                    'pmt_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pmt_id`)',
                    'id' => 'VARCHAR(18) NOT NULL',
                    'type' => 'VARCHAR(16) NOT NULL',
                    'ref_id' => 'VARCHAR(32) NOT NULL',
                    'currency' => 'CHAR(3) NOT NULL',
                    'datetime' => 'DATETIME NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_product', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_product}}', [
                    'pp_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pp_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'name' => 'VARCHAR(65) NOT NULL',
                    'description' => 'TINYTEXT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('dim_pin_reseller', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_pin_reseller}}', [
                    'pr_id' => 'INT(11) UNSIGNED NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`pr_id`)',
                    'id' => 'MEDIUMINT(8) NOT NULL',
                    'name' => 'VARCHAR(65) NOT NULL',
                    'email' => 'VARCHAR(65) NOT NULL',
                    'tel' => 'VARCHAR(65) NOT NULL',
                    'code' => 'VARCHAR(16) NOT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('fact_pin_distribution', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%fact_pin_distribution}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'fpd_pin_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpd_pbr_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpd_pr_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpd_date_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpd_pp_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpd_pc_id' => 'INT(11) UNSIGNED NOT NULL',
                    'pin_value' => 'DECIMAL(15,4) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('fact_pin_mgc_redemption', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%fact_pin_mgc_redemption}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'fpmr_pin_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpmr_pmm_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpmr_gi_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpmr_pmt_id' => 'INT(11) UNSIGNED NOT NULL',
                    'fpmr_date_id' => 'INT(11) UNSIGNED NOT NULL',
                    'pin_value' => 'DECIMAL(15,4) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }



        $this->execute('SET foreign_key_checks = 0');
        $this->insert('{{%cron_pointer}}', ['id' => '1', 'new_start_id' => '201410040000000000', 'cron_last_process_date' => '2015-11-19 11:54:28', 'cron_last_processed_count' => '4', 'system_table' => 'redeem_transaction']);
        $this->insert('{{%cron_pointer}}', ['id' => '2', 'new_start_id' => '201', 'cron_last_process_date' => '2015-11-19 11:54:28', 'cron_last_processed_count' => '200', 'system_table' => 'pin']);
        $this->insert('{{%cron_process_track}}', ['id' => '1', 'cron_process_track_in_action' => '0', 'cron_process_track_start_date' => '2015-11-19 11:53:25', 'cron_process_track_failed_attempt' => '0', 'cron_process_track_filename' => 'cronjob/task/redeemreport']);
        $this->execute('SET foreign_key_checks = 1;');
        


        $this->createIndex('idx_approved_by_177_00', 'dim_pin_batch_request', 'approved_by', 0);
        $this->createIndex('idx_request_by_177_01', 'dim_pin_batch_request', 'request_by', 0);
        $this->createIndex('idx_fpd_pin_id_2253_02', 'fact_pin_distribution', 'fpd_pin_id', 0);
        $this->createIndex('idx_fpd_pbr_id_2253_03', 'fact_pin_distribution', 'fpd_pbr_id', 0);
        $this->createIndex('idx_fpd_pr_id_2253_04', 'fact_pin_distribution', 'fpd_pr_id', 0);
        $this->createIndex('idx_fpd_pc_id_2253_05', 'fact_pin_distribution', 'fpd_pc_id', 0);
        $this->createIndex('idx_fpd_pp_id_2253_06', 'fact_pin_distribution', 'fpd_pp_id', 0);
        $this->createIndex('idx_fpd_date_id_2253_07', 'fact_pin_distribution', 'fpd_date_id', 0);
        $this->createIndex('idx_fpmr_pin_id_2362_08', 'fact_pin_mgc_redemption', 'fpmr_pin_id', 0);
        $this->createIndex('idx_fpmr_gi_id_2363_09', 'fact_pin_mgc_redemption', 'fpmr_gi_id', 0);
        $this->createIndex('idx_fpmr_date_id_2363_10', 'fact_pin_mgc_redemption', 'fpmr_date_id', 0);
        $this->createIndex('idx_fpmr_pmt_id_2363_11', 'fact_pin_mgc_redemption', 'fpmr_pmt_id', 0);
        $this->createIndex('idx_fpmr_pmm_id_2363_12', 'fact_pin_mgc_redemption', 'fpmr_pmm_id', 0);

        $this->execute('SET foreign_key_checks = 0');
        $this->addForeignKey('fk_dim_ogc_crew_1764_00', '{{%dim_pin_batch_request}}', 'approved_by', '{{%dim_ogc_crew}}', 'oc_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_ogc_crew_1765_01', '{{%dim_pin_batch_request}}', 'request_by', '{{%dim_ogc_crew}}', 'oc_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_2248_02', '{{%fact_pin_distribution}}', 'fpd_pin_id', '{{%dim_pin}}', 'pin_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_reseller_2248_03', '{{%fact_pin_distribution}}', 'fpd_pr_id', '{{%dim_pin_reseller}}', 'pr_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_date_2248_04', '{{%fact_pin_distribution}}', 'fpd_date_id', '{{%dim_date}}', 'date_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_product_2248_05', '{{%fact_pin_distribution}}', 'fpd_pp_id', '{{%dim_pin_product}}', 'pp_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_client_2248_06', '{{%fact_pin_distribution}}', 'fpd_pc_id', '{{%dim_pin_client}}', 'pc_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_batch_request_2248_07', '{{%fact_pin_distribution}}', 'fpd_pbr_id', '{{%dim_pin_batch_request}}', 'pbr_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_2357_08', '{{%fact_pin_mgc_redemption}}', 'fpmr_pin_id', '{{%dim_pin}}', 'pin_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_mgc_merchant_2357_09', '{{%fact_pin_mgc_redemption}}', 'fpmr_pmm_id', '{{%dim_pin_mgc_merchant}}', 'pmm_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_geo_info_2357_010', '{{%fact_pin_mgc_redemption}}', 'fpmr_gi_id', '{{%dim_geo_info}}', 'gi_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_pin_mgc_transaction_2357_011', '{{%fact_pin_mgc_redemption}}', 'fpmr_pmt_id', '{{%dim_pin_mgc_transaction}}', 'pmt_id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_dim_date_2357_012', '{{%fact_pin_mgc_redemption}}', 'fpmr_date_id', '{{%dim_date}}', 'date_id', 'CASCADE', 'CASCADE');
        $this->execute('SET foreign_key_checks = 1;');




        if (!in_array('migration', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%migration}}', [
                    'version' => 'VARCHAR(180) NOT NULL',
                    0 => 'PRIMARY KEY (`version`)',
                    'apply_time' => 'INT(11) NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('user', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%user}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'username' => 'VARCHAR(255) NOT NULL',
                    'auth_key' => 'VARCHAR(32) NOT NULL',
                    'password_hash' => 'VARCHAR(255) NOT NULL',
                    'password_reset_token' => 'VARCHAR(255) NULL',
                    'email' => 'VARCHAR(255) NOT NULL',
                    'status' => 'SMALLINT(6) NOT NULL DEFAULT \'10\'',
                    'created_at' => 'INT(11) NOT NULL',
                    'updated_at' => 'INT(11) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }


        $this->execute('SET foreign_key_checks = 0');
        $this->insert('{{%user}}', ['id' => '1', 'username' => 'superadmin', 'auth_key' => 'rdv1D7PyjSyZuGx9rtw88k0o_Y8XGyy8', 'password_hash' => '$2y$13$iZImBL2e0RaSqQpNqHHpl.68uJJMTsMnywWw/zd.w/iSjHUGVb1.2', 'password_reset_token' => '', 'email' => '<EMAIL>', 'status' => '10', 'created_at' => '1388534400', 'updated_at' => '1388534400']);
        $this->execute('SET foreign_key_checks = 1;');
        
        /* MYSQL */
        if (!in_array('auth_assignment', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%auth_assignment}}', [
                    'item_name' => 'VARCHAR(64) NOT NULL',
                    'user_id' => 'VARCHAR(64) NOT NULL',
                    'created_at' => 'INT(11) NULL',
                        ], $tableOptions_mysql);
            }
        }
        
        $this->execute('ALTER TABLE `auth_assignment` ADD PRIMARY KEY (`item_name`, `user_id` )');
        
        
        
    }

    public function down() {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `cron_pointer`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `cron_process_track`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `cron_logs`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_date`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_geo_info`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_ogc_crew`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_batch_request`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_client`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_mgc_merchant`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_mgc_transaction`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_product`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_pin_reseller`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `fact_pin_distribution`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `fact_pin_mgc_redemption`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `user`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `auth_assignment`');
        $this->execute('SET foreign_key_checks = 1;');
        
        echo "m151119_080627_upgrade reverted\n";
    }

    /*
      // Use safeUp/safeDown to run migration code within a transaction
      public function safeUp()
      {
      }

      public function safeDown()
      {
      }
     */
}
