<?php

use yii\db\Schema;
use yii\db\Migration;

class m160225_101546_upgrade extends Migration {

    public function up() {
        $tables = Yii::$app->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";
        $tableOptions_mssql = "";
        $tableOptions_pgsql = "";
        $tableOptions_sqlite = "";
        /* MYSQL */
        if (!in_array('gp_order', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%gp_order}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'delivery_datetime' => 'TIMESTAMP NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                    'order_pending_datetime' => 'TIMESTAMP NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                    'order_id' => 'INT(11) UNSIGNED NOT NULL',
                    'order_currency' => 'CHAR(3) NOT NULL',
                    'payment_method' => 'VARCHAR(64) NULL',
                    'products_id' => 'INT(11) UNSIGNED NOT NULL',
                    'product_name' => 'VARCHAR(255) NOT NULL',
                    'unit_price_in_order_currency' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_in_order_currency' => 'DECIMAL(12,4) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_percentage_in_order_currency' => 'DECIMAL(12,4) NOT NULL',
                    'order_conversion_rate_in_usd' => 'DECIMAL(13,8) NOT NULL',
                    'total_order_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_purchase_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_purchase_in_usd' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'total_order_in_usd' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'total_delivered_quantity' => 'INT(11) UNSIGNED NOT NULL',
                    'margin_in_usd' => 'DECIMAL(12,4) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_percentage_in_usd' => 'DECIMAL(12,4) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('gp_cost', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%gp_cost}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'gp_order_id' => 'INT(11) NOT NULL',
                    'purchase_type' => 'VARCHAR(4) NOT NULL',
                    'purchase_id' => 'INT(11) NOT NULL',
                    'purchase_currency' => 'CHAR(3) NOT NULL',
                    'delivered_quantity' => 'INT(2) UNSIGNED NOT NULL',
                    'unit_cost_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'unit_cost_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'unit_cost_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'purchase_conversion_rate_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'purchase_conversion_rate_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'extra_info' => 'TEXT NOT NULL',
                        ], $tableOptions_mysql);
            }
        }


        $this->createIndex('idx_order_id_2448_00', 'gp_order', 'order_id', 0);
        $this->createIndex('idx_gp_order_tbl_id', 'gp_cost', 'gp_order_id', 0);

        $this->execute('SET foreign_key_checks = 0');
        $this->addForeignKey('fk_gp_order_2483_00', '{{%gp_cost}}', 'gp_order_id', '{{%gp_order}}', 'id', 'CASCADE', 'CASCADE');
        $this->execute('SET foreign_key_checks = 1;');
       

        $this->insert('{{%cron_pointer}}', ['id' => '3', 'new_start_id' => '0', 'cron_last_process_date' => '0000-00-00 00:00:00', 'cron_last_processed_count' => '0', 'system_table' => 'log_delivered_products']);
        $this->insert('{{%cron_process_track}}', ['id' => '2', 'cron_process_track_in_action' => '0', 'cron_process_track_start_date' => '0000-00-00 00:00:00', 'cron_process_track_failed_attempt' => '0', 'cron_process_track_filename' => 'cronjob/task/gpcronjob']);
    }

    public function down() {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `gp_order`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `gp_cost`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->delete('{{%cron_pointer}}', ['id' => '3']);
        $this->delete('{{%cron_process_track}}', ['id' => '2']);
        echo "m151119_080627_upgrade reverted\n";
    }

}
