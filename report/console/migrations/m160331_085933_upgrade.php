<?php

use yii\db\Schema;
use yii\db\Migration;

class m160331_085933_upgrade extends Migration {

    public function up() {

        $tables = Yii::$app->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";
        $tableOptions_mssql = "";
        $tableOptions_pgsql = "";
        $tableOptions_sqlite = "";
        /* MYSQL */
        if (!in_array('currencies', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%currencies}}', [
                    'currencies_id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`currencies_id`)',
                    'title' => 'VARCHAR(32) NOT NULL',
                    'code' => 'CHAR(3) NOT NULL',
                    'symbol_left' => 'VARCHAR(12) NULL',
                    'symbol_right' => 'VARCHAR(12) NULL',
                    'decimal_point' => 'CHAR(1) NULL',
                    'thousands_point' => 'CHAR(1) NULL',
                    'decimal_places' => 'CHAR(1) NULL',
                        ], $tableOptions_mysql);
            }
        }


        $this->createIndex('idx_code_8019_00', 'currencies', 'code', 0);
        
        $select_sql = "SELECT currencies_id,title,code,symbol_left,symbol_right,decimal_point,thousands_point,decimal_places FROM currencies ";
        $results = Yii::$app->get('db_offgamers')->createCommand($select_sql)->queryAll();
        
        $this->execute('SET foreign_key_checks = 0');
        
        foreach ($results as $curr_row) {
            $insert_array = ['currencies_id' => $curr_row['currencies_id'],
                'title' => $curr_row['title'],
                'code' => $curr_row['code'],
                'symbol_left' => $curr_row['symbol_left'],
                'symbol_right' => $curr_row['symbol_right'],
                'decimal_point' => $curr_row['decimal_point'],
                'thousands_point' => $curr_row['thousands_point'],
                'decimal_places' => $curr_row['decimal_places']];
            $this->insert('{{%currencies}}', $insert_array);
        }
        
        $this->execute('SET foreign_key_checks = 1;');
        
        if (!in_array('cron_retry', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%cron_retry}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'action' => 'VARCHAR(64) NULL',
                    'task' => 'VARCHAR(64) NULL',
                    'key_1' => 'VARCHAR(255) NULL',
                    'val_1' => 'INT(11) UNSIGNED NOT NULL',
                    'key_2' => 'VARCHAR(255) NULL',
                    'val_2' => 'INT(11) UNSIGNED NOT NULL',
                    'json_str' => 'TEXT NOT NULL',
                    'create_datetime' => 'TIMESTAMP NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                ], $tableOptions_mysql);
            }
        }
    }

    public function down() {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `currencies`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('DROP TABLE IF EXISTS `cron_retry`');
        echo "m160331_085933_upgrade reverted\n";
    }

}
