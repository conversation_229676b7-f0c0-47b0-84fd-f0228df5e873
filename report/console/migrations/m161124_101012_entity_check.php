<?php

use yii\db\Schema;
use yii\db\Migration;

class m161124_101012_entity_check extends Migration {

    public function up() {
        $tables = Yii::$app->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";

        //Create dim_gp_entity table. GP report will be able to list normaly even with entity name change.
        if (!in_array('dim_gp_entity', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%dim_gp_entity}}', [
                    'gpe_id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`gpe_id`)',
                    'id' => 'VARCHAR(2) NOT NULL',
                    'company_name' => 'VARCHAR(65) NOT NULL',
                    'date_from' => 'DATE NOT NULL',
                    'date_to' => 'DATE NOT NULL',
                    'version' => 'TINYINT(2) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }
        // Fetch the table schema
        $table = Yii::$app->db->schema->getTableSchema('gp_cost');
        if (!isset($table->columns['gpc_gpe_id'])) {
            // Alter table gp_cost to add entity id
            $this->addColumn('{{%gp_cost}}', 'gpc_gpe_id', 'INT(11) NOT NULL AFTER purchase_currency');
        }
        // Start data checking for db:offgamers
        $products_id = array();
        // Get product_id for API, DTU, PIN
        $others_gpcost_sql = "SELECT O.products_id "
                . "FROM gp_cost AS C "
                . "LEFT JOIN gp_order AS O "
                . "ON O.id = C.gp_order_id "
                . "WHERE C.purchase_type != 'PO' "
                . "GROUP BY O.products_id";
        $others_results = Yii::$app->get('db')->createCommand($others_gpcost_sql)->queryAll();
        // Start loop purchase_id (API, DTU, PIN)
        echo "    > Start (API, DTU, PIN) Entity Check\n";
        foreach ($others_results as $row_pid) {
            // Get Entity for each purchase_id
            $others_entity_sql = "SELECT P.products_name, C.products_cost_company_code, C.products_cost_company_name "
                    . "FROM products_cost AS C "
                    . "LEFT JOIN products_description AS P "
                    . "ON P.products_id = C.products_id "
                    . "AND P.language_id = 1 "
                    . "WHERE C.products_id = " . $row_pid['products_id'];
            $others_entity_result = Yii::$app->get('db_offgamers')->createCommand($others_entity_sql)->queryOne();
            // Check missing company_code or company_name
            if (empty($others_entity_result['products_cost_company_name']) || empty($others_entity_result['products_cost_company_code'])) {
                // if not available in tbl:products_cost
                if (empty($others_entity_result['products_name'])) {
                    $products_name_sql = "SELECT products_name "
                            . "FROM products_description "
                            . "WHERE products_id = " . $row_pid['products_id'] . " "
                            . "AND language_id = 1";
                    $products_name_result = Yii::$app->get('db_offgamers')->createCommand($products_name_sql)->queryOne();
                    $products_id[] = $row_pid['products_id'] . " - " . $products_name_result['products_name'];
                } else {
                    $products_id[] = $row_pid['products_id'] . " - " . $others_entity_result['products_name'];
                }
            }
        } // End loop purchase_id (API, DTU, PIN)
        echo "    > End (API, DTU, PIN) Entity Check\n\n";
        echo "\n";
        echo implode(", ", $products_id);
        echo "\n\n";
        echo "    > Please redo this migration (m161124_101012_entity_check) once all the PO Entity for the above list has been set\n";
    }

    public function down() {

        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `dim_gp_entity`');
        $this->execute('ALTER TABLE gp_cost DROP gpc_gpe_id');
        $this->execute('SET foreign_key_checks = 1;');

        echo "m161124_101012_entity_check reverted\n";
    }

}
