<?php

use yii\db\Schema;
use yii\db\Migration;

class m161125_024547_upgrade extends Migration {

    public function up() {

        // Set cron pointers for migration limit.
        $gp_pointer_sql = "SELECT id, new_start_id FROM cron_pointer WHERE id = 4";
        $gp_pointer_result = Yii::$app->get('db')->createCommand($gp_pointer_sql)->queryOne();
        if (empty($gp_pointer_result)) {
            $this->insert('{{%cron_pointer}}', [
                'id' => '4',
                'new_start_id' => '0',
                'cron_last_process_date' => '0000-00-00 00:00:00',
                'cron_last_processed_count' => '0',
                'system_table' => 'migrations'
            ]);
        }
        $limit = 100000; // CHANGE VALUE FOR LIVE
        $offset = (empty($gp_pointer_result['id']) || $gp_pointer_result['new_start_id'] == 0) ? 0 : ($gp_pointer_result['new_start_id'] - 1);
        // Start data migration from db:offgamers => db:ogm_report
        $params_entity = [];
        $cron_pointer = 0;
        // Get product_id from gp_cost + gp_order
        $gpcost_sql = "SELECT C.id, C.purchase_id, C.purchase_type, O.products_id "
                . "FROM gp_cost AS C "
                . "LEFT JOIN gp_order AS O "
                . "ON O.id = C.gp_order_id "
                . "LIMIT " . $limit . " "
                . "OFFSET " . $offset . " ";
        $gpcost_results = Yii::$app->get('db')->createCommand($gpcost_sql)->queryAll();
        // Start loop purchase_id
        echo "    > Start data migration";
        $this->execute('SET foreign_key_checks = 0;');
        foreach ($gpcost_results as $row_po) {
            if ($row_po['purchase_type'] === 'PO') {
                // Get Entity for each purchase_id
                $entity_sql = "SELECT delivery_location, delivery_name "
                        . "FROM purchase_orders "
                        . "WHERE purchase_orders_id = " . $row_po['purchase_id'];
                $entity_result = Yii::$app->get('db_offgamers')->createCommand($entity_sql)->queryOne();
                $params_entity['id'] = isset($entity_result['delivery_location']) ? $entity_result['delivery_location'] : '';
                $params_entity['company_name'] = isset($entity_result['delivery_name']) ? $entity_result['delivery_name'] : '';
            } else {
                // Get Entity for each purchase_id
                $entity_sql = "SELECT products_cost_company_code, products_cost_company_name "
                        . "FROM products_cost "
                        . "WHERE products_id = " . $row_po['products_id'];
                $entity_result = Yii::$app->get('db_offgamers')->createCommand($entity_sql)->queryOne();
                $params_entity['id'] = isset($entity_result['products_cost_company_code']) ? $entity_result['products_cost_company_code'] : '';
                $params_entity['company_name'] = isset($entity_result['products_cost_company_name']) ? $entity_result['products_cost_company_name'] : '';
            }
            // Check Entity version
            $gpe_id = $this->checkEntityVersion($params_entity['id'], 'gpe_id', $params_entity);
            // Update gp_cost with entity_entity code
            $this->update('{{%gp_cost}}', ['gpc_gpe_id' => $gpe_id], 'id =' . $row_po['id']);
            $cron_pointer++;
        } // End loop purchase_id (PO)
        $this->update('{{%cron_pointer}}', [
            'new_start_id' => ($cron_pointer + $offset + 1),
            'cron_last_process_date' => date('Y-m-d H:i:s'),
            'cron_last_processed_count' => ($cron_pointer + $offset)
                ], 'id = 4');
        echo "    > End data migration\n";
        $this->execute('SET foreign_key_checks = 1;');
    }

    public function down() {

        echo "m161125_024547_upgrade not allow to be reverted\n";
        false;
    }

    private function checkEntityVersion($code, $pk, $params = []) {
        // Get entity info from dim_gp_entity
        $current_data_sql = "SELECT * FROM dim_gp_entity "
                . "WHERE id = '" . $code . "' "
                . "ORDER BY version DESC";
        $current_data = Yii::$app->get('db')->createCommand($current_data_sql)->queryOne();
        // is ID exist
        if ($current_data) {
            // is the content matched? (with table column)
            $matched_flag = true;
            foreach ($params as $key => $value) {
                if ($current_data[$key] == $value) {
                    // do nothing
                } else {
                    $matched_flag = false;
                    break;
                }
            }
            // is $matched_flag == true
            if ($matched_flag) {
                return $current_data[$pk];
            } else {
                // new_version = current_version + 1;
                // update current primary key date_to to current date
                // insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
                // Get entity info from dim_gp_entity
                $update_status_sql = "SELECT * FROM dim_gp_entity "
                        . "WHERE " . $pk . " = " . $current_data[$pk] . " "
                        . "AND id = '" . $code . "'";
                $update_status = Yii::$app->get('db')->createCommand($update_status_sql)->queryAll();
                if ($update_status) {
                    $this->update('{{%dim_gp_entity}}', ['date_to' => date('Y-m-d')], "$pk =$current_data[$pk] AND id='" . $code . "'");
                    $this->insert('{{%dim_gp_entity}}', array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => $current_data['version'] + 1]));
                    $current_gpe_id_sql = "SELECT gpe_id FROM dim_gp_entity "
                            . "WHERE id = '" . $code . "' "
                            . "ORDER BY version DESC";
                    $current_gpe_id = Yii::$app->get('db')->createCommand($current_gpe_id_sql)->queryOne();
                    return $current_gpe_id[$pk];
                }
            }
        } else {
            # new_version = 1;
            # insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
            $this->insert('{{%dim_gp_entity}}', array_merge($params, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => 1]));
            $current_gpe_id_sql = "SELECT gpe_id FROM dim_gp_entity "
                    . "WHERE id = '" . $code . "' "
                    . "ORDER BY version DESC";
            $current_gpe_id = Yii::$app->get('db')->createCommand($current_gpe_id_sql)->queryOne();
            return $current_gpe_id[$pk];
        }
    }

}
