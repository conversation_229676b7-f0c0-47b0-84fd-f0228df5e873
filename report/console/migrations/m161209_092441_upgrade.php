<?php

use yii\db\Schema;
use yii\db\Migration;

class m161209_092441_upgrade extends Migration
{
    public function up()
    {
        $tables = Yii::$app->db->schema->getTableNames();
        $dbType = $this->db->driverName;
        $tableOptions_mysql = "CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB";
        $tableOptions_mssql = "";
        $tableOptions_pgsql = "";
        $tableOptions_sqlite = "";
        /* MYSQL */
        if (!in_array('gp_order_released', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%gp_order_released}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'released_start_datetime' => 'TIMESTAMP NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                    'released_end_datetime' => 'TIMESTAMP NOT NULL DEFAULT \'0000-00-00 00:00:00\'',
                    'order_id' => 'INT(11) UNSIGNED NOT NULL',
                    'order_currency' => 'CHAR(3) NOT NULL',
                    'payment_method' => 'VARCHAR(64) NULL',
                    'products_id' => 'INT(11) UNSIGNED NOT NULL',
                    'product_name' => 'VARCHAR(255) NOT NULL',
                    'unit_price_in_order_currency' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_in_order_currency' => 'DECIMAL(12,4) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_percentage_in_order_currency' => 'DECIMAL(12,4) NOT NULL',
                    'order_conversion_rate_in_usd' => 'DECIMAL(13,8) NOT NULL',
                    'total_released_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_purchase_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_purchase_in_usd' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'total_released_in_usd' => 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\'',
                    'total_released_quantity' => 'INT(11) UNSIGNED NOT NULL',
                    'margin_in_usd' => 'DECIMAL(12,4) NOT NULL DEFAULT \'0.00000000\'',
                    'margin_percentage_in_usd' => 'DECIMAL(12,4) NOT NULL',
                        ], $tableOptions_mysql);
            }
        }

        /* MYSQL */
        if (!in_array('gp_cost_released', $tables)) {
            if ($dbType == "mysql") {
                $this->createTable('{{%gp_cost_released}}', [
                    'id' => 'INT(11) NOT NULL AUTO_INCREMENT',
                    0 => 'PRIMARY KEY (`id`)',
                    'gpo_released_id' => 'INT(11) NOT NULL',
                    'purchase_type' => 'VARCHAR(4) NOT NULL',
                    'purchase_id' => 'INT(11) NOT NULL',
                    'purchase_currency' => 'CHAR(3) NOT NULL',
                    'gpcr_gpe_id' => 'INT(11) UNSIGNED NOT NULL',
                    'released_quantity' => 'INT(2) UNSIGNED NOT NULL',
                    'unit_cost_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'unit_cost_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'unit_cost_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'purchase_conversion_rate_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'purchase_conversion_rate_in_usd' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_purchase_currency' => 'DECIMAL(20,8) NOT NULL',
                    'total_cost_in_order_currency' => 'DECIMAL(20,8) NOT NULL',
                    'extra_info' => 'TEXT NOT NULL',
                        ], $tableOptions_mysql);
            }
        }
        
        $this->createIndex('idx_order_id', 'gp_order_released', 'order_id', 0);
        $this->createIndex('idx_gp_order_tbl_id', 'gp_cost_released', 'gpo_released_id', 0);

        $this->execute('SET foreign_key_checks = 0');
        $this->addForeignKey('fk_gp_order_released', '{{%gp_cost_released}}', 'gpo_released_id', '{{%gp_order_released}}', 'id', 'CASCADE', 'CASCADE');
        $this->execute('SET foreign_key_checks = 1;');

        $this->insert('{{%cron_pointer}}', ['id' => '5', 'new_start_id' => '0', 'cron_last_process_date' => '0000-00-00 00:00:00', 'cron_last_processed_count' => '0', 'system_table' => 'log_delivered_released']);
        $this->insert('{{%cron_process_track}}', ['id' => '3', 'cron_process_track_in_action' => '0', 'cron_process_track_start_date' => '0000-00-00 00:00:00', 'cron_process_track_failed_attempt' => '0', 'cron_process_track_filename' => 'cronjob/task/gpreleasedcronjob']);
        
    }

    public function down()
    {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `gp_order_released`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('DROP TABLE IF EXISTS `gp_cost_released`');
        $this->execute('SET foreign_key_checks = 1;');
        $this->delete('{{%cron_pointer}}', ['id' => '5']);
        $this->delete('{{%cron_process_track}}', ['id' => '3']);
        echo "m161209_092441_upgrade reverted.\n";

    }
    
}
