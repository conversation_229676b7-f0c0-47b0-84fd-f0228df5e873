<?php

use yii\db\Schema;
use yii\db\Migration;

class m170404_030309_upgrade extends Migration {

    public function up() {
        #Update Start - End Date for GP Released Report
        /*
         * Steps:
         * 1. Get all orders_id from gp_order_released (group by orders_id)
         * 2. Get first order date from table: orders_status_stat
         * 3. Update start date for each order
         * 
         * Database:Tables
         * 1. offgamers:log_delivered_released
         * 2. offgamers:orders_status_stat
         * 3. ogm_report:gp_order_released
         */

        $order_sql = "SELECT order_id FROM gp_order_released GROUP BY order_id";
        $order_results = Yii::$app->get('db')->createCommand($order_sql)->queryAll();

        foreach ($order_results as $orders_id) {

            $first_date_sql = "SELECT first_date FROM orders_status_stat WHERE orders_id = '" . $orders_id['order_id'] . "' AND orders_status_id = 1";
            $first_date_result = Yii::$app->get('db_offgamers')->createCommand($first_date_sql)->queryOne();
            
            // Start updating start-date for order
            $this->update('{{%gp_order_released}}', ['released_start_datetime' => $first_date_result['first_date']], 'order_id =' . $orders_id['order_id']);
            
        }
        
        /*
         * 1. Check all gp_cost:gpc_gpe_id
         * 2. Set gp_cost:gpc_gpe_id
         *      1 => MY
         *      2 => SG
         *      3 => HK
         * 3. Store temp data from dim_gp_entity - final version
         * 4. Truncate dim_gp_entity
         * 5. Insert temp data back with new dim_gp_entity:gpe_id
         * 6. Map gp_cost:gpc_gpe_id with new dim_gp_entity:gpe_id
         */
        
        // Get gp_cost info
        $gpc_gpe_id_sql = "SELECT gpc.gpc_gpe_id AS gpcgpeid, gpe.id AS gpeid "
                . "FROM gp_cost AS gpc "
                . "INNER JOIN dim_gp_entity AS gpe "
                . "ON gpe.gpe_id = gpc.gpc_gpe_id "
                . "GROUP BY gpc.gpc_gpe_id";
        $gpc_gpe_id_data = Yii::$app->get('db')->createCommand($gpc_gpe_id_sql)->queryAll();
        // Start loop gp_cost
        $this->execute('SET foreign_key_checks = 0;');
        foreach ($gpc_gpe_id_data as $row_gpc) {
            switch($row_gpc['gpeid']) {
                case "MY":
                    $gpe_id = 4;
                    break;
                case "SG":
                    $gpe_id = 5;
                    break;
                case "HK":
                    $gpe_id = 6;
                    break;
            }
            // Update gp_cost with temp
            $this->update('{{%gp_cost}}', ['gpc_gpe_id' => $gpe_id], 'gpc_gpe_id =' . $row_gpc['gpcgpeid']);
        }
        // Get entity info from dim_gp_entity
        $current_data_sql = "SELECT id, company_name FROM dim_gp_entity ";
        $current_data = Yii::$app->get('db')->createCommand($current_data_sql)->queryAll();
        $entities = array();
        foreach ($current_data as $entity) {
            $entities[$entity['id']]['id'] = $entity['id'];
            $entities[$entity['id']]['company_name'] = $entity['company_name'];
        }
        // Truncate table dim_gp_entity
        $this->truncateTable('{{%dim_gp_entity}}');
        // insert new record with new_version (date_from is current_date, date_to is 2999-12-31)
        foreach ($entities as $entity_array) {
            $this->insert('{{%dim_gp_entity}}', array_merge($entity_array, ['date_from' => date('Y-m-d'), 'date_to' => '2999-12-31', 'version' => 1]));
        }
        // Map gp_cost:gpc_gpe_id to the new dim_gp_entity:gpe_id
        // Get new entity info from dim_gp_entity
        $new_data_sql = "SELECT gpe_id, id FROM dim_gp_entity ";
        $new_data = Yii::$app->get('db')->createCommand($new_data_sql)->queryAll();
        foreach ($new_data as $new_entity) {
            switch ($new_entity['id']) {
                case "MY":
                    $new_gpe_id = 4;
                    break;
                case "SG":
                    $new_gpe_id = 5;
                    break;
                case "HK":
                    $new_gpe_id = 6;
                    break;
            }
            $this->update('{{%gp_cost}}', ['gpc_gpe_id' => $new_entity['gpe_id']], 'gpc_gpe_id =' . $new_gpe_id);
        }
        $this->execute('SET foreign_key_checks = 1;');
    }

    public function down() {
        echo "m170404_030309_upgrade cannot be reverted.\n";

        return false;
    }

}
