<?php

use yii\db\Schema;
use yii\db\Migration;
use backend\models\CronRetry;
use yii\db\Expression;
use backend\modules\cronjob\components\CronLogCom;

class m170418_094904_hotfix extends Migration
{
    public function up() {
        $retry_sql = "SELECT * FROM cron_retry WHERE task IN ('GP_REPORT', 'GP_REPORT_RELEASED') LIMIT 2000";
        $retry_results = Yii::$app->get('db')->createCommand($retry_sql)->queryAll();

        foreach ($retry_results as $retry_info) {
            if ($retry_info['json_str']) {
                $LogData = [];
                
                $idx = $retry_info['id'];
                $_id = $retry_info['val_1'];
                $type = $retry_info['task'] == 'GP_REPORT' ? 'GP' : 'GP_RELEASE';
                
                $json_array = json_decode($retry_info['json_str'], true);
                $rerunData = $type == 'GP' ? $this->getPoList($_id) : $this->getPoListReleased($_id);
                
                if ($rerunData) {
                    foreach ($json_array as $err_code_info) {
                        $ecode = is_array($err_code_info) ? 'missingData' : $err_code_info;

                        switch ($ecode) {
                            case 'x_po':
                            case 'x_cost':
                                $LogData = [1002, 'Missing GP Cost Data', $rerunData];
                                break 2;
                            case 'missingData':
                                $errData = [
                                    $retry_info['val_2'] => $json_array
                                ];
                                $LogData = [1001, 'Missing Data', $errData];
                                break 2;
                            case 'x_final_price':
                                // do nothing
                                break;
                        }
                    }
                }
                
                if ($LogData) {
                    $this->capture4rerun($idx, $type, $rerunData, $LogData);
                } else {
                    CronRetry::deleteRerunByID($idx);
                }
            }
        }
        
        echo "Total Done : " . count($retry_results) . ".\n";
    }

    public function down() {
        echo "m170418_094904_hotfix cannot be reverted.\n";

        return true;
    }

    private function capture4rerun($idx, $type, $rerunData, $LogData) {
        list($error_id, $error_msg, $json) = $LogData;

        $retry_array = [
            'action' => 'RERUN',
            'task' => $type,
            'key_1' => 'error-type',
            'val_1' => (int) $error_id,
            'key_2' => 'rerun-counter',
            'val_2' => 1,
            'json_str' => json_encode($rerunData),
            'create_datetime' => new Expression('NOW()')
        ];

        $rerun_obj = CronRetry::findOne($idx);
        $rerun_obj->task = $type;
        $rerun_obj->key_1 = 'error-type';
        $rerun_obj->val_1 = (int) $error_id;
        $rerun_obj->key_2 = 'rerun-counter';
        $rerun_obj->val_2 = 0;
        $rerun_obj->json_str = json_encode($rerunData);
        $rerun_obj->save();
        
        $this->createCronLogs([
            'log_time' => date('Y-m-d H:i:s'),
            'log_user_messages' => 'Error: ' .$type . ' - ' . $error_msg,
            'log_system_messages' => json_encode($json),
            'log_field_name' => 'CronRetry:rerun-id',
            'log_from_value' => (string) $idx,
            'log_to_value' => (string) $rerunData['id']
        ]);

        unset($retry_array);
    }

    private function createCronLogs($input_array) {
        $cron_log_com = new CronLogCom();
        $cron_log_com->saveCronLog($input_array);
    }

    private function getPoList($_id) {
        $po_sql = " SELECT o.orders_id, o.customers_id, o.currency, o.payment_method, o.currency_value,
                        ldp.id, ldp.products_quantity, ldp.extra_info, ldp.log_date_time, ldp.products_id, oss.first_date,
                       CONCAT(p.products_cat_path, ' > ', pd.products_name) AS products_name, 
                       p.products_flag_id, p.products_model
                    FROM log_delivered_products AS ldp
                    INNER JOIN  orders AS o ON o.orders_id = ldp.orders_id
                    INNER JOIN orders_status_stat AS oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 1
                    INNER JOIN products AS p ON p.products_id= ldp.products_id
                    LEFT JOIN products_description AS pd ON pd.products_id= ldp.products_id AND pd.language_id = 1 
                    WHERE ldp.id = $_id
                    ";

        $gp_report = Yii::$app->get('db_offgamers')->createCommand($po_sql)->queryOne();

        return $gp_report;
    }
    
    private function getPoListReleased($_id) {
        $po_sql = "SELECT o.orders_id, o.customers_id, o.currency, o.payment_method, 
                    ldp.id, ldp.released_quantity, ldp.extra_info, ldp.products_id,
                    CONCAT(p.products_cat_path, ' > ', pd.products_name) AS products_name, 
                    p.products_flag_id, p.products_model, o.currency_value, oss.first_date, ldp.log_date_time
                    FROM log_delivered_released AS ldp
                    INNER JOIN  orders AS o ON o.orders_id = ldp.orders_id
                    INNER JOIN orders_status_stat AS oss ON o.orders_id = oss.orders_id AND oss.orders_status_id = 1
                    INNER JOIN products AS p ON p.products_id= ldp.products_id
                    LEFT JOIN products_description AS pd ON pd.products_id= ldp.products_id AND pd.language_id = 1 
                    WHERE ldp.id = $_id
                    ";

        $gp_report = Yii::$app->get('db_offgamers')->createCommand($po_sql)->queryOne();

        return $gp_report;
    }
}
