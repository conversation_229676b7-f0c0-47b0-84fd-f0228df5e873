<?php

use yii\db\Schema;
use yii\db\Migration;

class m180213_023917_update extends Migration
{
    public function up()
    {
        /*
            Migration steps for entity update year 2017
            1. Change star-end date according to each months in 2017
            2. Run migration
            3. start #1 again and just redo the migration
         */
        
        // Date settings for monthly processing
        $start_date = '2010-01-01';
        $end_date = '2018-02-13';

        // Get SG entity id from dim_gp_entity table
        $entity_sql = "SELECT gpe_id FROM dim_gp_entity
                        WHERE id = 'SG'
                        ORDER BY gpe_id DESC
        ";
        $entity_results = Yii::$app->get('db')->createCommand($entity_sql)->queryOne();
        $entity_id = ($entity_results) ? $entity_results['gpe_id'] : 0;

        // Get cost orders result from gp_cost & gp_order filter by date range
        $cost_sql = "SELECT gpc.id AS gpc_id, gpc.purchase_type, gpc.purchase_id, gpo.id AS gpo_id, gpo.order_id, gpo.delivery_datetime 
                        FROM gp_cost AS gpc
                        INNER JOIN gp_order AS gpo
                            ON gpc.gp_order_id = gpo.id
                        WHERE gpo.delivery_datetime >= '" . $start_date . " 00:00:00'
                            AND gpo.delivery_datetime <= '" . $end_date . " 23:59:59'
                            AND gpc.purchase_type = 'PO'
                        ORDER BY gpo.delivery_datetime ASC
        ";
        $cost_result = Yii::$app->get('db')->createCommand($cost_sql)->queryAll();

        foreach ($cost_result as $cost) {
            // Get PO 1st upload date in offgamers db from custom_products_code table
            $upload_date_sql = "SELECT code_date_added 
                                FROM custom_products_code 
                                WHERE purchase_orders_id = '" . $cost['purchase_id'] . "'
                                ORDER BY custom_products_code_id ASC
            ";
            $upload_date_result = Yii::$app->get('db_offgamers')->createCommand($upload_date_sql)->queryOne();
            $upload_date = ($upload_date_result) ? $upload_date_result['code_date_added'] : 0;

            if ($upload_date == 0) {
                // skip if upload date unvailable for this particular PO
                continue;
            } else {
                // compare delivery months vs 1st upload date
                $date1 = date('Y-m', strtotime($cost['delivery_datetime']));
                $date2 = date('Y-m', strtotime($upload_date));
                if ($date1 === $date2) {
                    // year and month match so do nothing
                } else {
                    // Check if 2018 orders
                    $order_years = date('Y', strtotime($cost['delivery_datetime']));
                    $po_years = date('Y', strtotime($upload_date));
                    if ($order_years === '2018' && $po_years === '2018') {
                        // no stock movement for 2018
                    } else {
                        // Update Entity to SG as the date not match
                        $this->update('{{%gp_cost}}', ['gpc_gpe_id' => $entity_id], 'id =' . $cost['gpc_id']);

                        // Check if order has released key?
                        $released_cost_sql = "SELECT gpc.id, gpo.order_id, gpc.purchase_id 
                                            FROM gp_cost_released AS gpc 
                                            INNER JOIN gp_order_released AS gpo 
                                                ON gpc.gpo_released_id = gpo.id 
                                            WHERE gpo.order_id = '" . $cost['order_id'] . "' 
                                                AND gpc.purchase_id = '" . $cost['purchase_id'] . "'
                        ";
                        $released_cost_result = Yii::$app->get('db')->createCommand($released_cost_sql)->queryAll();

                        if ($released_cost_result) {
                            foreach ($released_cost_result as $released_cost) {
                                // Update Entity to SG as the date not match
                                $this->update('{{%gp_cost_released}}', ['gpcr_gpe_id' => $entity_id], 'id =' . $released_cost['id']);
                            }
                        }
                    }
                }
            }
        }
    }

    public function down()
    {
        echo "m180213_023917_update cannot be reverted.\n";
    }
}
