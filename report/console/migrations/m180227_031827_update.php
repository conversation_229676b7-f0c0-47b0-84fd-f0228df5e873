<?php

use yii\db\Schema;
use yii\db\Migration;
use backend\components\CurrenciesCom;

class m180227_031827_update extends Migration
{
    public function up()
    {
        // Fetch the table schema
        $table_cost = Yii::$app->db->schema->getTableSchema('gp_cost');
        if (!isset($table_cost->columns['total_sc_in_order_currency'])) {
            // Alter table gp_cost to add store credit amount
            $this->addColumn('{{%gp_cost}}', 'total_sc_in_order_currency', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_cost->columns['total_payment_in_order_currency'])) {
            // Alter table gp_cost to add payment amount
            $this->addColumn('{{%gp_cost}}', 'total_payment_in_order_currency', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_cost->columns['total_sc_in_usd'])) {
            // Alter table gp_cost to add store credit amount
            $this->addColumn('{{%gp_cost}}', 'total_sc_in_usd', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_cost->columns['total_payment_in_usd'])) {
            // Alter table gp_cost to add payment amount
            $this->addColumn('{{%gp_cost}}', 'total_payment_in_usd', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }

        // Fetch the table schema
        $table_released = Yii::$app->db->schema->getTableSchema('gp_cost_released');
        if (!isset($table_released->columns['total_sc_in_order_currency'])) {
            // Alter table gp_cost to add store credit amount
            $this->addColumn('{{%gp_cost_released}}', 'total_sc_in_order_currency', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_released->columns['total_payment_in_order_currency'])) {
            // Alter table gp_cost to add payment amount
            $this->addColumn('{{%gp_cost_released}}', 'total_payment_in_order_currency', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_released->columns['total_sc_in_usd'])) {
            // Alter table gp_cost to add store credit amount
            $this->addColumn('{{%gp_cost_released}}', 'total_sc_in_usd', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }
        if (!isset($table_released->columns['total_payment_in_usd'])) {
            // Alter table gp_cost to add payment amount
            $this->addColumn('{{%gp_cost_released}}', 'total_payment_in_usd', 'DECIMAL(20,8) NOT NULL DEFAULT \'0.00000000\' AFTER total_cost_in_order_currency');
        }

        // Check offgamers db for orders with store credit partial payment (2018 only)
        // Date settings for monthly processing
        $start_date = '2018-01-01';
        $end_date = '2018-03-31';
        $currency_com = new CurrenciesCom();

        // Get SG entity id from dim_gp_entity table
        $entity_sql = "SELECT gpe_id FROM dim_gp_entity
                        WHERE id = 'SG'
                        ORDER BY gpe_id DESC
        ";
        $entity_results = Yii::$app->get('db')->createCommand($entity_sql)->queryOne();
        $entity_id = ($entity_results) ? $entity_results['gpe_id'] : 0;

        // Get cost orders result from gp_cost & gp_order filter by date range
        $cost_sql = "SELECT gpc.id AS gpc_id, gpc.purchase_type, gpc.purchase_id, gpo.id AS gpo_id, gpo.order_id, gpc.gpc_gpe_id,
                            gpo.total_order_in_order_currency, gpo.delivery_datetime, gpo.total_delivered_quantity, gpo.payment_method,
                            gpc.delivered_quantity, gpo.order_conversion_rate_in_usd, gpo.order_currency, gpo.order_pending_datetime
                        FROM gp_cost AS gpc
                        INNER JOIN gp_order AS gpo
                            ON gpc.gp_order_id = gpo.id
                        WHERE gpo.delivery_datetime >= '" . $start_date . " 00:00:00'
                            AND gpo.delivery_datetime <= '" . $end_date . " 23:59:59'
                        ORDER BY gpo.delivery_datetime ASC
        ";
        $cost_result = Yii::$app->get('db')->createCommand($cost_sql)->queryAll();

        foreach ($cost_result as $cost) {
            // Check for order with entity HK and move to entity SG
            $cost_entity_sql = "SELECT id FROM dim_gp_entity WHERE gpe_id = '" . $cost['gpc_gpe_id'] . "'";
            $cost_entity_result = Yii::$app->get('db')->createCommand($cost_entity_sql)->queryOne();

            $order_years = date('Y', strtotime($cost['delivery_datetime']));
            if ($cost_entity_result['id'] == 'HK' && $order_years == '2018') {
                // Update Entity to SG as the date not match
                echo "HK => SG";
                $this->update('{{%gp_cost}}', ['gpc_gpe_id' => $entity_id], 'id =' . $cost['gpc_id']);
            }

            // Update Payment Methods title to new format (PG - PM)
            if ($cost['payment_method'] != 'Store Credit') {
                $payment_methods_title = $this->getPaymentMethodsTitle($cost['order_id']);
                echo "Payment Methods: ";
                $this->update('{{%gp_order}}', ['payment_method' => $payment_methods_title], 'order_id =' . $cost['order_id']);
            }

            // Start calculating for Store Credit Partial Payment
            $order_currency = $cost['order_currency'];
            $order_pending_date = $cost['order_pending_datetime'];
            $sc_per_cost = 0;
            $sc_in_usd_per_cost = 0;
            $pay_amount_in_usd_per_cost = 0;

            // Calculate price per item
            $pay_amount_per_cost = $cost['total_order_in_order_currency'];
            $pay_amount_per_cost = $pay_amount_per_cost / $cost['total_delivered_quantity'];
            $pay_amount_per_cost = $pay_amount_per_cost * $cost['delivered_quantity'];

            $sc_sql = "SELECT value FROM orders_total WHERE class = 'ot_gv' AND orders_id = '" . $cost['order_id'] . "'";
            $sc_result = Yii::$app->get('db_offgamers')->createCommand($sc_sql)->queryOne();
            
            if (isset($sc_result['value']) && $sc_result['value'] > 0) {
                // get currency_value from original order
                $curr_value_sql = "SELECT currency_value FROM orders WHERE orders_id = '" . $cost['order_id'] . "'";
                $curr_value_result = Yii::$app->get('db_offgamers')->createCommand($curr_value_sql)->queryOne();
                // convert SC to order currency
                $sc_amount = $currency_com->displayFormat($sc_result['value'] * $curr_value_result['currency_value'], $order_currency, false);

                $sc_per_quantity = $sc_amount / $cost['total_delivered_quantity'];
                $sc_per_cost = $sc_per_quantity * $cost['delivered_quantity'];
                $pay_amount_per_cost = $pay_amount_per_cost - $sc_per_cost;
                // amount in USD
                $sc_in_usd_per_cost = $currency_com->advanceCurrencyConversion($sc_per_cost, $order_currency, 'USD', $order_pending_date);
            }

            $pay_amount_in_usd_per_cost = $currency_com->advanceCurrencyConversion($pay_amount_per_cost, $order_currency, 'USD', $order_pending_date);
            // Update gp_cost with new total
            $update_cost = array(
                'total_sc_in_order_currency' => $sc_per_cost,
                'total_payment_in_order_currency' => $pay_amount_per_cost,
                'total_sc_in_usd' => $sc_in_usd_per_cost,
                'total_payment_in_usd' => $pay_amount_in_usd_per_cost
            );
            $this->update('{{%gp_cost}}', $update_cost, 'id =' . $cost['gpc_id']);
        }

        // Get cost orders released result from gp_cost_released & gp_order_released filter by date range
        $cost_released_sql = "SELECT gpc.id AS gpc_id, gpc.purchase_type, gpc.purchase_id, gpo.id AS gpo_id, gpo.order_id, gpc.gpcr_gpe_id,
                                gpo.total_released_in_order_currency, gpo.released_end_datetime, gpo.total_released_quantity, gpo.payment_method,
                                gpc.released_quantity, gpo.order_conversion_rate_in_usd, gpo.order_currency, gpo.released_start_datetime
                        FROM gp_cost_released AS gpc
                        INNER JOIN gp_order_released AS gpo
                            ON gpc.gpo_released_id = gpo.id
                        WHERE gpo.released_end_datetime >= '" . $start_date . " 00:00:00'
                            AND gpo.released_end_datetime <= '" . $end_date . " 23:59:59'
                        ORDER BY gpo.released_end_datetime ASC
        ";
        $cost_released_result = Yii::$app->get('db')->createCommand($cost_released_sql)->queryAll();

        foreach ($cost_released_result as $cost_released) {
            // Check for order with entity HK and move to entity SG
            $cost_entity_sql = "SELECT id FROM dim_gp_entity WHERE gpe_id = '" . $cost_released['gpcr_gpe_id'] . "'";
            $cost_entity_result = Yii::$app->get('db')->createCommand($cost_entity_sql)->queryOne();

            $order_years = date('Y', strtotime($cost_released['released_end_datetime']));
            if ($cost_entity_result['id'] == 'HK' && $order_years == '2018') {
                // Update Entity to SG as the date not match
                echo "HK => SG";
                $this->update('{{%gp_cost_released}}', ['gpcr_gpe_id' => $entity_id], 'id =' . $cost_released['gpc_id']);
            }

            // Update Payment Methods title to new format (PG - PM)
            if ($cost_released['payment_method'] != 'Store Credit') {
                $payment_methods_title = $this->getPaymentMethodsTitle($cost_released['order_id']);
                echo "Payment Methods: ";
                $this->update('{{%gp_order_released}}', ['payment_method' => $payment_methods_title], 'order_id =' . $cost_released['order_id']);   
            }

            $order_currency = $cost_released['order_currency'];
            $order_pending_date = $cost_released['released_start_datetime'];
            $sc_per_cost = 0;
            $sc_in_usd_per_cost = 0;
            $pay_amount_in_usd_per_cost = 0;

            // Calculate price per item
            $pay_amount_per_cost = $cost_released['total_released_in_order_currency'];
            $pay_amount_per_cost = $pay_amount_per_cost / $cost_released['total_released_quantity'];
            $pay_amount_per_cost = $pay_amount_per_cost * $cost_released['released_quantity'];

            $sc_sql = "SELECT value FROM orders_total WHERE class = 'ot_gv' AND orders_id = '" . $cost_released['order_id'] . "'";
            $sc_result = Yii::$app->get('db_offgamers')->createCommand($sc_sql)->queryOne();

            // get original sum delivered quantity
            $sum_quantity_sql = "SELECT sum(products_quantity) FROM orders_products WHERE orders_id = '" . $cost_released['order_id'] . "'";
            echo $sum_quantity_result = Yii::$app->get('db_offgamers')->createCommand($sum_quantity_sql)->queryScalar();
            echo $cost_released['order_id']."-Test\n";
            if (isset($sc_result['value']) && $sc_result['value'] > 0) {
                // get currency_value from original order
                $curr_value_sql = "SELECT currency_value FROM orders WHERE orders_id = '" . $cost_released['order_id'] . "'";
                $curr_value_result = Yii::$app->get('db_offgamers')->createCommand($curr_value_sql)->queryOne();
                // convert SC to order currency
                $sc_amount = $currency_com->displayFormat($sc_result['value'] * $curr_value_result['currency_value'], $order_currency, false);

                $sc_per_quantity = $sc_amount / $sum_quantity_result;
                $sc_per_cost = $sc_per_quantity * $cost_released['released_quantity'];
                $pay_amount_per_cost = $pay_amount_per_cost - $sc_per_cost;
                // amount in USD
                $sc_in_usd_per_cost = $currency_com->advanceCurrencyConversion($sc_per_cost, $order_currency, 'USD', $order_pending_date);
            }

            $pay_amount_in_usd_per_cost = $currency_com->advanceCurrencyConversion($pay_amount_per_cost, $order_currency, 'USD', $order_pending_date);
            // Update gp_cost with new total
            $update_cost = array(
                'total_sc_in_order_currency' => $sc_per_cost,
                'total_payment_in_order_currency' => $pay_amount_per_cost,
                'total_sc_in_usd' => $sc_in_usd_per_cost,
                'total_payment_in_usd' => $pay_amount_in_usd_per_cost
            );
            $this->update('{{%gp_cost_released}}', $update_cost, 'id =' . $cost_released['gpc_id']);
        }
    }

    public function down()
    {
        $this->execute('SET foreign_key_checks = 0');
        $this->execute('ALTER TABLE gp_cost DROP total_sc_in_order_currency');
        $this->execute('ALTER TABLE gp_cost DROP total_payment_in_order_currency');
        $this->execute('ALTER TABLE gp_cost DROP total_sc_in_usd');
        $this->execute('ALTER TABLE gp_cost DROP total_payment_in_usd');
        $this->execute('ALTER TABLE gp_cost_released DROP total_sc_in_order_currency');
        $this->execute('ALTER TABLE gp_cost_released DROP total_payment_in_order_currency');
        $this->execute('ALTER TABLE gp_cost_released DROP total_sc_in_usd');
        $this->execute('ALTER TABLE gp_cost_released DROP total_payment_in_usd');
        $this->execute('SET foreign_key_checks = 1;');

        echo "m180227_031827_update reverted\n";
    }

    private function getPaymentMethodsTitle($order_id) {
        $pm_order_sql = "SELECT payment_methods_parent_id, payment_methods_id FROM orders WHERE orders_id = '" . $order_id . "'";
        $pm_order_result = Yii::$app->get('db_offgamers')->createCommand($pm_order_sql)->queryOne();

        if ($pm_order_result['payment_methods_parent_id'] > 0) {
            $pm_parent_sql = "SELECT payment_methods_title FROM payment_methods WHERE payment_methods_id = '" . $pm_order_result['payment_methods_parent_id'] . "'";
            $pm_parent_result = Yii::$app->get('db_offgamers')->createCommand($pm_parent_sql)->queryOne();
        }

        $pm_sql = "SELECT payment_methods_title FROM payment_methods WHERE payment_methods_id = '" . $pm_order_result['payment_methods_id'] . "'";
        $pm_result = Yii::$app->get('db_offgamers')->createCommand($pm_sql)->queryOne();

        return (isset($pm_parent_result['payment_methods_title'])) ? $pm_parent_result['payment_methods_title'].' - '.$pm_result['payment_methods_title'] : $pm_result['payment_methods_title'];
    }
}
