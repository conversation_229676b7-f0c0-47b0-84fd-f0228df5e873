<?php

use yii\db\Schema;
use yii\db\Migration;

class m180309_071415_hotfix extends Migration
{
    public function up()
    {
        $this->execute('SET foreign_key_checks = 0;');

        // Alter column for table gp_cost & gp_cost_released
        $this->alterColumn('{{%gp_order}}', 'payment_method', 'VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL');
        $this->alterColumn('{{%gp_order_released}}', 'payment_method', 'VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL');

        $this->execute('SET foreign_key_checks = 1;');
    }

    public function down()
    {
        echo "m180309_071415_hotfix cannot be reverted.\n";

        // return false;
    }
}
