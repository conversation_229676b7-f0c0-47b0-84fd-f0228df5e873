<?php

use yii\db\Schema;
use yii\db\Migration;

class m180312_073639_hotfix extends Migration
{
    public function up()
    {
        // Date settings for monthly processing
        $start_date = '2018-01-01';
        $end_date = '2018-01-31';

        // Get cost orders result from gp_cost & gp_order filter by date range
        $cost_sql = "SELECT gpc.id AS gpc_id, gpc.purchase_type, gpc.purchase_id,
                            gpo.id AS gpo_id, gpo.order_id, gpo.delivery_datetime 
                    FROM gp_cost AS gpc
                    INNER JOIN gp_order AS gpo
                        ON gpc.gp_order_id = gpo.id
                    WHERE gpo.delivery_datetime >= '" . $start_date . " 00:00:00'
                        AND gpo.delivery_datetime <= '" . $end_date . " 23:59:59'
                        AND gpc.purchase_type = 'DTU'
                    ORDER BY gpo.delivery_datetime ASC
        ";
        $cost_result = Yii::$app->get('db')->createCommand($cost_sql)->queryAll();

        foreach ($cost_result as $cost) {
            $order_activities_sql = "SELECT sales_activities_date 
                                FROM sales_activities 
                                WHERE sales_activities_code = 'D' 
                                    AND sales_activities_operator = '+'
                                    AND sales_activities_orders_id = '" . $cost['order_id'] . "'
                                ORDER BY sales_activities_id DESC
            ";
            $order_activities = Yii::$app->get('db_offgamers')->createCommand($order_activities_sql)->queryOne();

            if ($order_activities) {
                $this->update('{{%gp_order}}', ['delivery_datetime' => $order_activities['sales_activities_date']], 'id =' . $cost['gpo_id']);
            }
        }
    }

    public function down()
    {
        echo "m180312_073639_hotfix cannot be reverted.\n";
    }
}
