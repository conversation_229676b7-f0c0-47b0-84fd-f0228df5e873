<?php

use yii\db\Schema;
use yii\db\Migration;
use backend\components\CurrenciesCom;

class m190423_072406_hotfix_dtu_qty extends Migration
{
    public function up()
    {
        // Date settings for monthly processing
        $start_date = '2018-01-01';
        $end_date = '2018-01-31';

        // Get all DTU related records from gp_cost
        // Get cost orders result from gp_cost & gp_order filter by date range
        $cost_sql = "SELECT gpc.id AS gpc_id, gpc.delivered_quantity, gpc.extra_info, gpc.unit_cost_in_purchase_currency,
                    gpc.purchase_conversion_rate_in_order_currency, gpc.unit_cost_in_order_currency, gpc.unit_cost_in_usd,
                    gpo.id AS gpo_id, gpo.order_id, gpo.order_currency, gpo.unit_price_in_order_currency, gpo.order_pending_datetime
                    FROM gp_cost AS gpc
                    INNER JOIN gp_order AS gpo
                        ON gpc.gp_order_id = gpo.id
                    WHERE gpo.delivery_datetime >= '" . $start_date . " 00:00:00'
                        AND gpo.delivery_datetime <= '" . $end_date . " 23:59:59'
                        AND gpc.purchase_type = 'DTU'
                    ORDER BY gpo.delivery_datetime ASC";
        $cost_result = Yii::$app->get('db')->createCommand($cost_sql)->queryAll();
        // Loop trough and get topup info from og db based on top_up_id
        if ($cost_result) {
            foreach ($cost_result as $cost) {
                $gp_array = [];
                // Get delivered quantity from orders_products_id from orders_top_up
                $topup_sql = "  SELECT orders_products_id
                                FROM orders_top_up
                                WHERE top_up_id = '" . json_decode($cost['extra_info']) . "'";
                $orders_topup = Yii::$app->get('db_offgamers')->createCommand($topup_sql)->queryOne();
                
                if ($orders_topup) {
                    $orders_products_sql = "SELECT products_good_delivered_quantity
                                            FROM orders_products
                                            WHERE orders_products_id = '" . $orders_topup['orders_products_id'] . "'";
                    $orders_products = Yii::$app->get('db_offgamers')->createCommand($orders_products_sql)->queryOne();
                    // if delivered quantity = gp_report delivered quantity skip update
                    if ($orders_products['products_good_delivered_quantity'] != $cost['delivered_quantity']) {
                        $currency_com = new CurrenciesCom();

                        $order_currency = $cost['order_currency'];
                        $delivered_quantity = $orders_products['products_good_delivered_quantity'];
                        $order_unit_price_in_order_currency = $cost['unit_price_in_order_currency'];
                        $order_pending_datetime = $cost['order_pending_datetime'];
                        $unit_cost_in_purchase_currency = $cost['unit_cost_in_purchase_currency'];
                        $unit_cost_in_order_currency = $cost['unit_cost_in_order_currency'];
                        $unit_cost_in_usd = $cost['unit_cost_in_usd'];

                        // Re-calculate the cost
                        $total_order_in_order_currency = $currency_com->displayFormat($order_unit_price_in_order_currency * $delivered_quantity, $order_currency, false);
                        $total_order_in_usd = $currency_com->advanceCurrencyConversion($total_order_in_order_currency, $order_currency, 'USD', $order_pending_datetime);

                        list($softpin_array, $total_purchase_in_purchase_currency, $total_purchase_in_order_currency, $total_purchase_in_usd) = $this->processCostInfo($delivered_quantity, $unit_cost_in_purchase_currency, $unit_cost_in_order_currency, $unit_cost_in_usd);

                        if ($softpin_array) {
                            $order_margin_in_order_currency = $total_order_in_order_currency - $total_purchase_in_order_currency;

                            if ($total_purchase_in_order_currency > 0) {
                                $margin_percentage_in_order_currency = $total_order_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_order_in_order_currency * 100) : -100;
                            } else {
                                $margin_percentage_in_order_currency = $total_order_in_order_currency > 0 ? ($order_margin_in_order_currency / $total_order_in_order_currency * 100) : 0;
                            }

                            $gp_array['total_delivered_quantity'] = $delivered_quantity;
                            $gp_array['total_order_in_order_currency'] = $total_order_in_order_currency;
                            $gp_array['total_purchase_in_purchase_currency'] = $total_purchase_in_purchase_currency;
                            // $gp_array['total_purchase_in_order_currency'] = $total_purchase_in_order_currency;
                            $gp_array['margin_in_order_currency'] = $order_margin_in_order_currency;
                            $gp_array['margin_percentage_in_order_currency'] = $margin_percentage_in_order_currency;
                            $gp_array['total_order_in_usd'] = $currency_com->displayFormat($total_order_in_usd, 'USD', false);
                            $gp_array['total_purchase_in_usd'] = $currency_com->displayFormat($total_purchase_in_usd, 'USD', false);
                            $gp_array['margin_in_usd'] = $total_order_in_usd - $total_purchase_in_usd;

                            if ($total_purchase_in_usd > 0) {
                                $gp_array['margin_percentage_in_usd'] = $gp_array['total_order_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_order_in_usd'] * 100) : -100;
                            } else {
                                $gp_array['margin_percentage_in_usd'] = $gp_array['total_order_in_usd'] > 0 ? ($gp_array['margin_in_usd'] / $gp_array['total_order_in_usd'] * 100) : 0;
                            }

                            // Update gp_order table using gp_array
                            $this->update('{{%gp_order}}', $gp_array, 'id =' . $cost['gpo_id']);
                                
                            foreach ($softpin_array as $cd_key2) {
                                $po_array = [];

                                $po_array['delivered_quantity'] = $delivered_quantity;
                                $po_array['total_cost_in_purchase_currency'] = $cd_key2['total_cost_in_purchase_currency'];
                                $po_array['total_cost_in_order_currency'] = $cd_key2['total_cost_in_order_currency'];
                                $po_array['total_cost_in_usd'] = $cd_key2['total_cost_in_usd'];

                                // Additional checking for partial store credit payment
                                $sc_per_cost = 0;
                                $sc_in_usd_per_cost = 0;
                                $pay_amount_in_usd_per_cost = 0;
                                $sc_amount = 0;

                                // Calculate price per item
                                $pay_amount_per_cost = $total_order_in_order_currency;

                                // get store credit amount
                                $sc_sql = "SELECT value FROM orders_total WHERE class = 'ot_gv' AND orders_id = '" . $cost['order_id'] . "'";
                                $sc_result = Yii::$app->get('db_offgamers')->createCommand($sc_sql)->queryOne();
                                
                                if (isset($sc_result['value'])) {
                                    $sc_amount = $sc_result['value'];
                                }
                                
                                if ($sc_amount > 0) {
                                    // get currency_value from orders table
                                    $currency_value_sql = "SELECT currency_value FROM orders WHERE orders_id = '" . $cost['order_id'] . "'";
                                    $currency_value_result = Yii::$app->get('db_offgamers')->createCommand($currency_value_sql)->queryOne();

                                    $sc_amount = $currency_com->displayFormat($sc_amount * $currency_value_result['currency_value'], $order_currency, false);
                                    $sc_per_quantity = $sc_amount / $delivered_quantity;
                                    $sc_per_cost = $sc_per_quantity * $delivered_quantity;
                                    $pay_amount_per_cost = $pay_amount_per_cost - $sc_per_cost;
                                    // amount in USD
                                    $sc_in_usd_per_cost = $currency_com->advanceCurrencyConversion($sc_per_cost, $order_currency, 'USD', $order_pending_datetime);
                                }

                                $pay_amount_in_usd_per_cost = $currency_com->advanceCurrencyConversion($pay_amount_per_cost, $order_currency, 'USD', $order_pending_datetime);

                                // Set partial store credit payment
                                $po_array['total_sc_in_order_currency'] = $sc_per_cost;
                                $po_array['total_payment_in_order_currency'] = $pay_amount_per_cost;
                                $po_array['total_sc_in_usd'] = $sc_in_usd_per_cost;
                                $po_array['total_payment_in_usd'] = $pay_amount_in_usd_per_cost;

                                // Update gp_cost table using po_array
                                $this->update('{{%gp_cost}}', $po_array, 'id =' . $cost['gpc_id']);
                            }
                        }
                    }
                }
            }
        }
    }

    public function down()
    {
        echo "m190423_072406_hotfix_dtu_qty cannot be reverted.\n";
    }

    private function processCostInfo($delivered_quantity, $unit_cost_in_purchase_currency, $unit_cost_in_order_currency, $unit_cost_in_usd)
    {
        $softpin_array = [];
        $total_purchase_in_purchase_currency = 0;
        $total_purchase_in_order_currency = 0;
        $total_purchase_in_usd = 0;
        
        $total_cost_in_purchase_currency = 0;
        $total_cost_in_order_currency = 0;
        $total_cost_in_usd = 0;

        $total_cost_in_purchase_currency = ($unit_cost_in_purchase_currency * $delivered_quantity);
        $total_cost_in_order_currency = ($unit_cost_in_order_currency * $delivered_quantity);
        $total_cost_in_usd = ($unit_cost_in_usd * $delivered_quantity);

        $total_purchase_in_purchase_currency += $total_cost_in_purchase_currency;
        $total_purchase_in_order_currency += $total_cost_in_order_currency;
        $total_purchase_in_usd += $total_cost_in_usd;

        $softpin_array[] = [
            'total_cost_in_purchase_currency' => $total_cost_in_purchase_currency,
            'total_cost_in_order_currency' => $total_cost_in_order_currency,
            'total_cost_in_usd' => $total_cost_in_usd
        ];

        return [
            $softpin_array,
            $total_purchase_in_purchase_currency,
            $total_purchase_in_order_currency,
            $total_purchase_in_usd
        ];
    }
}
