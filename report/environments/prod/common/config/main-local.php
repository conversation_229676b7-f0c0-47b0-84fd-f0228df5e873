<?php

return [
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=ogm_report',
            'username' => 'root',
            'password' => 'root',
            'charset' => 'utf8',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => true,
            // Configure the slave server
            'slaveConfig' => [
                'username' => 'root',
                'password' => 'root',
                'charset' => 'utf8',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => 'mysql:host=localhost;dbname=ogm_report'],
            ],
        ],
        'db_mgc' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=multigamecard', //maybe other dbms such as psql,...
            'username' => 'root',
            'password' => 'root',
            'charset' => 'utf8',
        ],
        'db_ogc' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=ogc', //maybe other dbms such as psql,...
            'username' => 'root',
            'password' => 'root',
            'charset' => 'utf8',
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=offgamers', //maybe other dbms such as psql,...
            'username' => 'root',
            'password' => 'root',
            'charset' => 'latin1',
            'enableSchemaCache' => (YII_DEBUG ? false : true),
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
            'enableSlaves' => true,
            // Configure the slave server
            'slaveConfig' => [
                'username' => 'root',
                'password' => 'root',
                'charset' => 'latin1',
            ],
            // Configure the slave server
            'slaves' => [
                ['dsn' => 'mysql:host=rds-localhost;dbname=offgamers'],
            ],
        ],
    ],
];