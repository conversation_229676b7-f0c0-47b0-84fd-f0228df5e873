# Codeception Test Suite Configuration

# suite for acceptance tests.
# perform tests in browser using the Selenium-like tools.
# powered by Mink (http://mink.behat.org).
# (tip: that's what your customer will see).
# (tip: test your ajax and javascript by one of Mink drivers).

# RUN `build` COMMAND AFTER ADDING/REMOVING MODULES.

class_name: AcceptanceTester
modules:
    enabled:
        - PhpBrowser
        - tests\codeception\common\_support\FixtureHelper
# you can use WebDriver instead of PhpBrowser to test javascript and ajax.
# This will require you to install selenium. See http://codeception.com/docs/04-AcceptanceTests#Selenium
# "restart" option is used by the WebDriver to start each time per test-file new session and cookies,
# it is useful if you want to login in your app in each test.
#        - WebDriver
    config:
        PhpBrowser:
# PLEASE ADJUST IT TO THE ACTUAL ENTRY POINT WITHOUT PATH INFO
            url: http://localhost:8080
#        WebDriver:
#            url: http://localhost:8080
#            browser: firefox
#            restart: true
