#!/usr/bin/env php
<?php
/**
 * Yii console bootstrap file.
 *
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

require_once __DIR__ . '/_bootstrap.php';

$config = yii\helpers\ArrayHelper::merge(
    require(YII_APP_BASE_PATH . '/common/config/main.php'),
    require(YII_APP_BASE_PATH . '/common/config/main-local.php'),
    require(YII_APP_BASE_PATH . '/console/config/main.php'),
    require(YII_APP_BASE_PATH . '/console/config/main-local.php'),
    require(dirname(__DIR__) . '/config/config.php')
);

$application = new yii\console\Application($config);
$exitCode = $application->run();
exit($exitCode);
