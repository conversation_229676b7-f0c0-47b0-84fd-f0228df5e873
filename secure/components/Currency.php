<?php

namespace micro\components;

class Currency extends \offgamers\base\components\Currency
{
    public function parseStringToNumber($currency, $value){
        $value = str_replace($this->currency_list[$currency]['symbol_left'], '', $value);
        $value = str_replace($this->currency_list[$currency]['symbol_right'], '', $value);
        $value = trim(strip_tags($value));
        $value = str_replace($this->currency_list[$currency]['thousands_point'], '', $value);
        return (float) str_replace($this->currency_list[$currency]['decimal_point'], '.', $value);
    }
}