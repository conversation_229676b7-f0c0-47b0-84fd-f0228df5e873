<?php

namespace micro\components;

use Yii;
use yii\base\Component;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;
use micro\models\Countries;
use micro\models\Customers;
use micro\models\CustomersInfo;
use micro\models\CustomersInfoVerification;
use micro\models\CustomersLoginIpHistory;
use micro\models\CustomersVerificationDocument;
use micro\models\Orders;
use micro\models\PipwavePaymentMapper;
use yii\web\HttpException;

class Pipwave extends Component
{
    public $merchant_id;
    public $key;
    public $secret;
    public $server_url;
    public $sdk_url;
    private $prefered_payment;
    private $exclude_payment;

    public function __construct()
    {
        $params = Yii::$app->params['pipwave'];
        $this->key = $params['key'];
        $this->secret = $params['secret'];
        $this->server_url = $params['url'];
        $this->sdk_url = $params['sdk'];
        parent::__construct();
    }

    public function validateIp($ip)
    {
        if ($ip) {
            if (filter_var(
                    $ip,
                    FILTER_VALIDATE_IP,
                    FILTER_FLAG_IPV4 | FILTER_FLAG_IPV6 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
                ) === false) {
                return false;
            }
            return true;
        }
        return false;
    }

    public function getUserIp()
    {
        if (isset($_SERVER['HTTP_TRUE_CLIENT_IP']) && $this->validateIp($_SERVER['HTTP_TRUE_CLIENT_IP'])) {
            return $_SERVER['HTTP_TRUE_CLIENT_IP'];
        }

        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && ($_SERVER['HTTP_X_FORWARDED_FOR'] != '')) {
            $ip_array = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            foreach ($ip_array as $ip) {
                // trim for safety measures
                $ip = trim($ip);
                // attempt to validate IP
                if ($this->validateIp($ip)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    public function initiatePayment($data, $trans_id)
    {
        $country_code = null;
        $country_ISO_code = '';
        $telWithoutCountry = '';
        $skipTelVerify = false;
        $countryName = array();
        $customer_id = $data['customer_id'];
        $customer_group_id = $data['customer_info']['customer_group'];
        $data_version = ($data['version'] ?? 1);

        $customerContactInfo = Customers::getCustomerContactInfo($data['customer_id']);
        if (isset($data['customer_info']['country_ISO'])) {
            $countryName = Countries::getCountryNamebyISO2($data['customer_info']['country_ISO']);
        }
        if (isset($customerContactInfo['customers_telephone']) && !empty($customerContactInfo['customers_telephone'])) {
            $telephone = $customerContactInfo['customers_telephone'];
            if (isset($customerContactInfo['customers_country_dialing_code_id']) && !empty($customerContactInfo['customers_country_dialing_code_id'])) {
                $telephoneCountryCode = Countries::getCountryDialingCode(
                    $customerContactInfo['customers_country_dialing_code_id']
                );
                if (isset($telephoneCountryCode['countries_international_dialing_code'])) {
                    $country_code = $telephoneCountryCode['countries_international_dialing_code'];
                    $country_ISO_code = $telephoneCountryCode['countries_iso_code_2'];
                    $telWithoutCountry = $customerContactInfo['customers_telephone'];
                }
            }
            if ($country_code) {
                $telephone = $country_code . $customerContactInfo['customers_telephone'];
            }
            $telInfo = CustomersInfoVerification::isInfoVerified($data['customer_id'], $telephone, 'telephone');
            if (isset($telInfo['info_verified']) && $telInfo['info_verified'] == '1') {
                $skipTelVerify = true;
            }
        }
        $customerFlag = '';
        $customer_flag = Customers::getCustomerFlag($data['customer_id']);
        $customerFlagRemark = '';
        $is_dispute = false;
        $is_fraudster = false;

        if (!empty($customer_flag)) {
            $customerFlagArr = explode(',', $customer_flag);
            asort($customerFlagArr);
            $customerFlag = implode(',', $customerFlagArr);
            if (in_array('1', $customerFlagArr)) {
                // get customers flag message by raw sql to avoid importing another model for one use
                $sql = "SELECT `message` FROM `customers_flag_message` WHERE customers_id = " . (int)$data['customer_id'] . " AND flag_id = 1";
                $customerFlagRemark = Yii::$app->db->createCommand($sql)->queryScalar();
            }
            if (in_array('2', $customerFlagArr)) {
                $is_dispute = true;
            }
            if (in_array('4', $customerFlagArr)) {
                $is_fraudster = true;
            }
        }
        $signUpInfo = [];
        if (!empty($data['customer_info']['signup_detail']['country'])) {
            $api_signup_info = $data['customer_info']['signup_detail'];
            $signUpInfo['signup_country'] = $api_signup_info['country'];
            if (!empty($api_signup_info['ip'])) {
                $signUpInfo['signup_ip'] = $api_signup_info['ip'];
            }
            if (!empty($api_signup_info['date'])) {
                $signUpInfo['signup_date'] = $api_signup_info['date'];
            }
        } else {
            $signUpInfo = CustomersInfo::getSignupInfo($data['customer_id']);
        }
        $login_info = [];
        if (!empty($data['customer_info']['login_detail']['ip'])) {
            $api_login_info = $data['customer_info']['login_detail'];
            $login_info['customers_login_ip'] = $api_login_info['ip'];
        } else {
            $login_info = CustomersLoginIpHistory::getLatestLoginHistory($data['customer_id']);
        }

        $default_ip = $this->getUserIP();

        $request = array(
            'action' => 'initiate-payment',
            'timestamp' => time(),
            'api_key' => $this->key,
            'txn_id' => (string)$trans_id,
            'amount' => $data['amount'],
            'tax_exempted_amount' => $data['amount'],
            'currency_code' => $data['currency_code'],
            'short_description' => 'Purchase from OffGamers',
            'session_info' => array(
                'ip_address' => $default_ip,
                'language' => Yii::$app->language,
            ),
            'buyer_info' => array(
                'id' => $data['customer_id'],
                'email' => ($data['customer_info']['email_address'] ?? ""),
                'first_name' => $data['customer_info']['firstname'],
                'last_name' => $data['customer_info']['lastname'],
                'contact_no' => $telWithoutCountry,
                'contact_no_country_iso2' => $country_ISO_code,
                'contact_no_country_code' => $country_code,
                'country_code' => $country_ISO_code,
                'processing_fee_group' => 1,
                'login_ip_address' => $login_info['customers_login_ip'] ?? $default_ip,
                'signup_ip_address' => $signUpInfo['signup_ip'] ?? $default_ip,
                'signup_ip_country' => $signUpInfo['signup_country'] ?? null,
                'signup_date' => (isset($signUpInfo['signup_date']) ? strtotime($signUpInfo['signup_date']) : time()),
                'kyc_status' => CustomersVerificationDocument::getKycStatus($data['customer_id'])
            ),
            'billing_info' => array(
                'name' => $data['customer_info']['firstname'] . ' ' . $data['customer_info']['lastname'],
                'address1' => ($data['customer_info']['street_address'] ?? ''),
                'address2' => ($data['customer_info']['suburb'] ?? ''),
                'city' => ($data['customer_info']['city'] ?? ''),
                'state' => ($data['customer_info']['state'] ?? ''),
                'zip' => ($data['customer_info']['postcode'] ?? ''),
                'country_iso2' => ($data['customer_info']['country_ISO'] ?? ''),
                'country' => ($countryName['countries_name'] ?? ''),
                'contact_no' => $telWithoutCountry,
                'contact_no_country_iso2' => $country_ISO_code,
                'email' => ($data['customer_info']['email_address'] ?? ''),
            ),
            'init_setting' => array(
                'skip_phone_verification' => $skipTelVerify,
                'txn_count_1_hr' => Orders::getCompletedOrderCount($customer_id, '1hour'),
                'txn_count_24_hr' => Orders::getCompletedOrderCount($customer_id, '24hour'),
                'txn_count_3_month' => Orders::getCompletedOrderCount($customer_id, '3month'),
                'txn_count_total' => Orders::getCompletedOrderCount($customer_id, 'all'),
                'tax_override' => ($data['tax_override'] ?? ''),
            ),
            'api_override' => array(
                'success_url' => $this->getSuccessUrl(),
                'fail_url' => $this->getFailUrl(),
                'notification_url' => $this->getNotificationUrl(),
                //for risk management trust level group usage
                'notification_extra_param1' => Customers::getCustomerAftGroup($data['customer_id']),
                'notification_extra_param2' => $customerFlag,
                'redirect_extra_param1' => Customers::getCustomerGroupId($data['customer_id']),
                'redirect_extra_param3' => $customerFlagRemark,
            ),
        );

        $request['shipping_info'] = $request['billing_info'];

        $products = [];

        foreach ($data['products_info'] as $_product) {
            $_product_data = [
                'name' => (string)($_product['name'] ?? ''),
                'sku' => (string)($_product['id'] ?? ''),
                'category' => (string)($_product['category'] ?? ''),
                'quantity' => ($_product['quantity'] ?? ''),
                'currency_code' => $data['currency_code'],
            ];

            if ($data_version > 1 && $price = ($_product['final_price'] ?? 0)) {
                $_product_data['amount'] = $price;
            }

            $products[] = $_product_data;
        }

        if ($data_version > 1 && isset($data['tax_amount'])) {
            $products[] = [
                'name' => 'tax_' . $trans_id,
                'sku' => 'tax_' . $trans_id,
                'category' => 'tax_' . $trans_id,
                'quantity' => 1,
                'currency_code' => $data['currency_code'],
                'amount' => $data['tax_amount']
            ];
        }

        $request['item_info'] = $products;

        $this->prefered_payment = (!empty($data['prefered_payment']) ? explode(',', $data['prefered_payment']) : []);
        $this->exclude_payment = (!empty($data['exclude_payment']) ? explode(',', $data['exclude_payment']) : []);

        if (!in_array($customer_group_id, [8, 31])) {
            $this->processPaymentMethodExclusion('offline_wiretransfer.wiretransfer');
        }

        if (!in_array($customer_group_id, [2, 3, 4, 5, 6, 8, 12, 15, 29, 31])) {
            $this->processPaymentMethodExclusion('offline_wiretransfer.wise');
            $this->processPaymentMethodExclusion('offline_wiretransfer.wise.local');
        }

        if (!in_array($customer_group_id, [8])) {
            $this->processPaymentMethodExclusion('offline_cimb.cimbsg');
        }

        // Exclude Public Bank from customers group other than distributors and Reseller
        if (!in_array($customer_group_id, [6, 8])) {
            $this->processPaymentMethodExclusion('offline_pbbank.publicbank');
        }

        if (in_array($customer_group_id, [2, 3, 12])) {
            $this->processPaymentMethodExclusion('offline_uob.uobsg');
            $pm_list = explode(',', 'offline_ambank.ambank,offline_cimb.cimbmy,offline_hlb.hongleongbank,offline_maybank.maybank,offline_ocbc.ocbcmy,offline_rhb.rhb,offline_uob.uobmy,jompay.jompay,offline_maybank_billpayment.billpayment,offline_pbbank_billpayment.pbe_billpayment');
            $this->processPaymentMethodExclusion($pm_list);
        }

        if ($is_dispute || $is_fraudster) {
            if ($is_fraudster) {
                $restrict_pm_code_list = PipwavePaymentMapper::getRpAndSemiRpPayment();
            } else {
                $restrict_pm_code_list = PipwavePaymentMapper::getRpPayment();
            }
            $this->processPaymentMethodExclusion($restrict_pm_code_list);
        }

        $visa_exclusive_list = [];

        // Adyen Disabled on SGD, no rules execution required
        if (isset($data['is_crypto_voucher']) && strtoupper($data['currency_code']) !== 'SGD') {
            if ($data['is_crypto_voucher']) {
                $visa_exclusive_list = ["adyen.api.jcb", "adyen.api.mc", "adyen.api.visa", "adyen.hpp.applepay", "adyen.hpp.bankTransfer_AT", "adyen.hpp.bankTransfer_BE", "adyen.hpp.bankTransfer_DE", "adyen.hpp.bankTransfer_DK", "adyen.hpp.bankTransfer_ES", "adyen.hpp.bankTransfer_FI", "adyen.hpp.bankTransfer_FR", "adyen.hpp.bankTransfer_IE", "adyen.hpp.bankTransfer_NL", "adyen.hpp.bankTransfer_NO", "adyen.hpp.bankTransfer_SE", "adyen.hpp.bankTransfer_GB", "adyen.hpp.unionpay", "adyen.hpp.giropay", "adyen.hpp.googlepay", "adyen.hpp.ideal::0031", "adyen.hpp.ideal::0761", "adyen.hpp.ideal::0802", "adyen.hpp.ideal::0804", "adyen.hpp.ideal::0721", "adyen.hpp.ideal::0801", "adyen.hpp.ideal::0803", "adyen.hpp.ideal::0021        ", "adyen.hpp.ideal::0771", "adyen.hpp.ideal::0751        ", "adyen.hpp.ideal::0511", "adyen.hpp.ideal::0161", "adyen.hpp.paysafecard", "adyen.hpp.payshop", "adyen.hpp.bankTransfer_IBAN", "adyen.api.sepadirectdebit", "adyen.hpp.directEbanking"];
//            } else {
//                $visa_exclusive_list = ["stripe.visa", "stripe.mastercard", "stripe.applepay", "stripe.googlepay"];
            }
        }

        $this->processPaymentMethodExclusion($visa_exclusive_list);

        if ($this->prefered_payment) {
            $request['prefered_payment'] = implode(',', $this->prefered_payment);
        }

        if ($this->exclude_payment) {
            $request['exclude_payment'] = implode(',', $this->exclude_payment);
        }

        $signatureParam = array(
            'api_key' => $this->key,
            'api_secret' => $this->secret,
            'txn_id' => $request['txn_id'],
            'amount' => $request['amount'],
            'currency_code' => $request['currency_code'],
            'action' => $request['action'],
            'timestamp' => $request['timestamp'],
        );

        $request['signature'] = $this->generate_pw_signature($signatureParam);

        $response = $this->send_request_to_pw($request, $this->key);

        // return API data to your view to render payment selection page powered by pipwave
        if (isset($response['token']) && !empty($response['token'])) {
            return $response['token'];
        } elseif (($response['status'] ?? '') == 1002) {
            // Order Already Paid, Redirect User Back to Order Page
            $order_url = Yii::$app->params['frontend']['baseUrl'] . '/account/purchase/order/' . $request['txn_id'];
            Yii::$app->response->redirect($order_url)->send();
            Yii::$app->end();
        } else {
            Yii::$app->slack->send('Failed to get token from Pipwave : ' . $trans_id, array(
                array(
                    'color' => 'warning',
                    'text' => Json::encode([$request, $response]),
                ),
            ));
            throw new HttpException(500);
        }
    }

    public function checkOrder($orders_id)
    {
        $data = array(
            'action' => 'requery',
            'timestamp' => time(),
            'api_key' => $this->key,
            'txn_id' => (string)$orders_id,
        );

        $signatureParam = array(
            'api_key' => $this->key,
            'api_secret' => $this->secret,
            'txn_id' => $data['txn_id'],
            'action' => $data['action'],
            'timestamp' => $data['timestamp']
        );

        $data['signature'] = $this->generate_pw_signature($signatureParam);
        $response = $this->send_request_to_pw($data, $this->key);

        return $response;
    }


    public function generate_pw_signature($signatureParam)
    {
        ksort($signatureParam);
        $signature = "";
        foreach ($signatureParam as $key => $value) {
            $signature .= $key . ':' . $value;
        }
        return sha1($signature);
    }

    public function verifyNotificationSignature($data)
    {
        $signatureParams = [
            'api_key' => $this->key,
            'api_secret' => $this->secret,
            'txn_id' => $data['txn_id'],
            'amount' => $data['amount'],
            'currency_code' => $data['currency_code'],
            'pw_id' => $data['pw_id'],
            'transaction_status' => $data['transaction_status'],
            'timestamp' => $data['timestamp']
        ];

        if ($data['signature'] == $this->generate_pw_signature($signatureParams)) {
            return true;
        }
    }

    function send_request_to_pw($data, $pw_api_key)
    {
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('x-api-key:' . $pw_api_key));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_URL, $this->server_url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if ($response == false) {
            echo "<pre>";
            echo 'CURL ERROR: ' . curl_errno($ch) . '::' . curl_error($ch);
            die;
        }
        curl_close($ch);
        return json_decode($response, true);
    }

    public function getSuccessUrl()
    {
        return Url::toRoute('payment/checkout-success', true);
    }

    public function getFailUrl()
    {
        return Url::toRoute('site/error', true);
    }

    public function getNotificationUrl()
    {
        return Url::toRoute(['pipwave/notification', 'dp_payment' => 'pipwave'], true);
    }

    private function processPaymentMethodExclusion($pm_code)
    {
        if (is_array($pm_code)) {
            foreach ($pm_code as $pm) {
                $this->processPaymentMethodExclusion($pm);
            }
        } elseif (!in_array($pm_code, $this->exclude_payment)) {
            $this->exclude_payment[] = $pm_code;
        }
    }

}