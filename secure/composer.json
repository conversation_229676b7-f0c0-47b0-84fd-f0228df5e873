{"name": "yiisoft/yii2-app-basic", "description": "Yii 2 Basic Project Template", "keywords": ["yii2", "framework", "basic", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.3.0", "yiisoft/yii2": "~2.0.14", "yiisoft/yii2-bootstrap": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.0.0 || ~2.1.0", "offgamers/microservices-multiservice": "dev-main", "ext-dom": "*", "ext-bcmath": "*", "ext-fileinfo": "*", "ext-curl": "*"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.1.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^4.0", "codeception/verify": "~0.5.0 || ~1.1.0", "codeception/specify": "~0.4.6", "symfony/browser-kit": ">=2.7 <=4.2.4", "codeception/module-filesystem": "^1.0.0", "codeception/module-yii2": "^1.0.0", "codeception/module-asserts": "^1.0.0"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}, {"type": "vcs", "url": "**************:tech-ogm/toolbox-multiservice.git"}]}