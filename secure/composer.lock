{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "987828caf4b4db6545913b4df9627dc9", "packages": [{"name": "aws/aws-sdk-php", "version": "3.183.9", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "3b3aafdceac4cb820e2ae65a8785e4d07db471a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/3b3aafdceac4cb820e2ae65a8785e4d07db471a7", "reference": "3b3aafdceac4cb820e2ae65a8785e4d07db471a7", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^5.3.3|^6.2.1|^7.0", "guzzlehttp/promises": "^1.4.0", "guzzlehttp/psr7": "^1.7.0", "mtdowling/jmespath.php": "^2.6", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.183.9"}, "time": "2021-05-28T18:28:19+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/68b0d231a13201eb14acd3dc84e51543d16e5f7e", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/inputmask", "version": "3.3.11", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/5e670ad62f50c738388d4dcec78d2888505ad77b", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "3711efedf0ca2e998cd0417324f717f2e0b828ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/3711efedf0ca2e998cd0417324f717f2e0b828ec", "reference": "3711efedf0ca2e998cd0417324f717f2e0b828ec"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/mathiasbynens/punycode.js.git", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mathiasbynens/punycode.js/zipball/38c8d3131a82567bfef18da09f7f4db68c84f8a3", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "type": "bower-asset"}, {"name": "bower-asset/yii2-pjax", "version": "*******", "source": {"type": "git", "url": "https://github.com/yiisoft/jquery-pjax.git", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/aef7b953107264f00234902a3880eb50dafc48be", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "egulias/email-validator", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f88dcf4b14af14a98ad96b14b2b317969eab6715", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.1"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2022-06-18T20:57:19+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.16.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/523407fb06eb9e5f3d59889b3978d5bfe94299c8", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.16.0"}, "time": "2022-09-18T07:06:19+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-06-20T21:43:03+00:00"}, {"name": "mpdf/mpdf", "version": "v8.1.2", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "a8a22f4874157e490d41b486053a20bec42e182c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/a8a22f4874157e490d41b486053a20bec42e182c", "reference": "a8a22f4874157e490d41b486053a20bec42e182c", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0", "php-http/message-factory": "^1.0", "psr/http-message": "^1.0", "psr/log": "^1.0 || ^2.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "^2.4", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2022-08-15T08:15:09+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.1"}, "time": "2021-06-14T00:11:39+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "offgamers/microservices-multiservice", "version": "dev-main", "source": {"type": "git", "url": "**************:tech-ogm/toolbox-multiservice.git", "reference": "6191ccbabc79c6f640b4fbf19bc378add6475dd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tech-ogm/toolbox-multiservice/zipball/6191ccbabc79c6f640b4fbf19bc378add6475dd7", "reference": "6191ccbabc79c6f640b4fbf19bc378add6475dd7", "shasum": ""}, "require": {"aws/aws-sdk-php": "3.183.9", "ext-bcmath": "*", "ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.3@dev", "mpdf/mpdf": "~8.0", "true/punycode": "~2.0", "yiisoft/yii2": "*", "yiisoft/yii2-queue": "^2.0@dev"}, "default-branch": true, "type": "yii2-extension", "autoload": {"psr-4": {"offgamers\\base\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Glendon <PERSON>o", "email": "<EMAIL>"}], "description": "Yii2 Microservices Shared Component", "support": {"source": "https://github.com/tech-ogm/toolbox-multiservice/tree/1.4.5"}, "time": "2021-09-29T02:17:41+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "setasign/fpdi", "version": "v2.3.6", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2021-02-11T11:37:01+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "143f1881e655bebca1312722af8068de235ae5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/143f1881e655bebca1312722af8068de235ae5dc", "reference": "143f1881e655bebca1312722af8068de235ae5dc", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "reference": "219aa369ceff116e673852dce47c3a41794c14bd", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/bf44a9fd41feaac72b074de600314a93e2ae78e2", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/cfa0ae98841b9e461207c13ab093d76b0fa7bace", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-10T07:21:04+00:00"}, {"name": "symfony/process", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "6e75fe6874cbc7e4773d049616ab450eff537bf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/6e75fe6874cbc7e4773d049616ab450eff537bf1", "reference": "6e75fe6874cbc7e4773d049616ab450eff537bf1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "true/punycode", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/true/php-punycode.git", "reference": "a4d0c11a36dd7f4e7cd7096076cab6d3378a071e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/true/php-punycode/zipball/a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "reference": "a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpunit/phpunit": "~4.7", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"TrueBV\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>an <PERSON>", "email": "<EMAIL>"}], "description": "A Bootstring encoding of Unicode for Internationalized Domain Names in Applications (IDNA)", "homepage": "https://github.com/true/php-punycode", "keywords": ["idna", "punycode"], "support": {"issues": "https://github.com/true/php-punycode/issues", "source": "https://github.com/true/php-punycode/tree/master"}, "abandoned": true, "time": "2016-11-16T10:37:54+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.46", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "d73259c3bc886648a6875109f9f09cddeff03708"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/d73259c3bc886648a6875109f9f09cddeff03708", "reference": "d73259c3bc886648a6875109f9f09cddeff03708", "shasum": ""}, "require": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "paragonie/random_compat": ">=1", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2022-08-18T22:18:45+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/83d144f4089adaa7064ad60dc4c1436daa2eb30e", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.4.* | 3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": "~2.0.6"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\bootstrap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-bootstrap/issues", "source": "https://github.com/yiisoft/yii2-bootstrap", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap", "type": "tidelift"}], "time": "2021-08-09T20:54:06+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-queue.git", "reference": "ed30b5f46ddadd62587a4963dec35f9b756c408b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/ed30b5f46ddadd62587a4963dec35f9b756c408b", "reference": "ed30b5f46ddadd62587a4963dec35f9b756c408b", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"aws/aws-sdk-php": ">=2.4", "enqueue/amqp-lib": "^0.8||^0.9.10", "enqueue/stomp": "^0.8.39", "jeremeamia/superclosure": "*", "pda/pheanstalk": "v3.*", "php-amqplib/php-amqplib": "*", "phpunit/phpunit": "~4.4", "yiisoft/yii2-debug": "*", "yiisoft/yii2-gii": "*", "yiisoft/yii2-redis": "*"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "support": {"docs": "https://github.com/yiisoft/yii2-queue/blob/master/docs/guide", "issues": "https://github.com/yiisoft/yii2-queue/issues", "source": "https://github.com/yiisoft/yii2-queue"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-queue", "type": "tidelift"}], "time": "2022-03-31T07:41:51+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-swiftmailer.git", "reference": "7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340", "reference": "7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~6.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-swiftmailer/issues", "source": "https://github.com/yiisoft/yii2-swiftmailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-swiftmailer", "type": "tidelift"}], "time": "2021-12-30T08:48:48+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.9.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/0bc8d1e30e96183e4f36db9dc79caead300beff4", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4", "shasum": ""}, "require": {"php": "~7.2|~8.0"}, "require-dev": {"cucumber/cucumber": "dev-gherkin-22.0.0", "phpunit/phpunit": "~8|~9", "symfony/yaml": "~3|~4|~5"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.9.0"}, "time": "2021-10-12T13:05:09+00:00"}, {"name": "codeception/codeception", "version": "4.2.2", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "b88014f3348c93f3df99dc6d0967b0dbfa804474"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/b88014f3348c93f3df99dc6d0967b0dbfa804474", "reference": "b88014f3348c93f3df99dc6d0967b0dbfa804474", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/lib-asserts": "^1.0 | 2.0.*@dev", "codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.1.1 | ^9.0", "codeception/stub": "^2.0 | ^3.0 | ^4.0", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "^1.4 | ^2.0", "php": ">=5.6.0 <9.0", "symfony/console": ">=2.7 <6.0", "symfony/css-selector": ">=2.7 <6.0", "symfony/event-dispatcher": ">=2.7 <6.0", "symfony/finder": ">=2.7 <6.0", "symfony/yaml": ">=2.7 <6.0"}, "require-dev": {"codeception/module-asserts": "^1.0 | 2.0.*@dev", "codeception/module-cli": "^1.0 | 2.0.*@dev", "codeception/module-db": "^1.0 | 2.0.*@dev", "codeception/module-filesystem": "^1.0 | 2.0.*@dev", "codeception/module-phpbrowser": "^1.0 | 2.0.*@dev", "codeception/specify": "~0.3", "codeception/util-universalframework": "*@dev", "monolog/monolog": "~1.8", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <6.0", "vlucas/phpdotenv": "^2.0 | ^3.0 | ^4.0 | ^5.0"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "hoa/console": "For interactive console functionality", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"files": ["functions.php"], "psr-4": {"Codeception\\": "src/Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "https://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "support": {"issues": "https://github.com/Codeception/Codeception/issues", "source": "https://github.com/Codeception/Codeception/tree/4.2.2"}, "funding": [{"url": "https://opencollective.com/codeception", "type": "open_collective"}], "time": "2022-08-13T13:28:25+00:00"}, {"name": "codeception/lib-asserts", "version": "1.13.2", "source": {"type": "git", "url": "https://github.com/Codeception/lib-asserts.git", "reference": "184231d5eab66bc69afd6b9429344d80c67a33b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-asserts/zipball/184231d5eab66bc69afd6b9429344d80c67a33b6", "reference": "184231d5eab66bc69afd6b9429344d80c67a33b6", "shasum": ""}, "require": {"codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.0.3 | ^9.0", "ext-dom": "*", "php": ">=5.6.0 <9.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Assertion methods used by Codeception core and Asserts module", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-asserts/issues", "source": "https://github.com/Codeception/lib-asserts/tree/1.13.2"}, "time": "2020-10-21T16:26:20+00:00"}, {"name": "codeception/lib-innerbrowser", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/Codeception/lib-innerbrowser.git", "reference": "31b4b56ad53c3464fcb2c0a14d55a51a201bd3c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-innerbrowser/zipball/31b4b56ad53c3464fcb2c0a14d55a51a201bd3c2", "reference": "31b4b56ad53c3464fcb2c0a14d55a51a201bd3c2", "shasum": ""}, "require": {"codeception/codeception": "4.*@dev", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0 <9.0", "symfony/browser-kit": ">=2.7 <6.0", "symfony/dom-crawler": ">=2.7 <6.0"}, "conflict": {"codeception/codeception": "<4.0"}, "require-dev": {"codeception/util-universalframework": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Parent library for all Codeception framework modules and PhpBrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-innerbrowser/issues", "source": "https://github.com/Codeception/lib-innerbrowser/tree/1.5.1"}, "time": "2021-08-30T15:21:42+00:00"}, {"name": "codeception/module-asserts", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-asserts.git", "reference": "59374f2fef0cabb9e8ddb53277e85cdca74328de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-asserts/zipball/59374f2fef0cabb9e8ddb53277e85cdca74328de", "reference": "59374f2fef0cabb9e8ddb53277e85cdca74328de", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "codeception/lib-asserts": "^1.13.1", "php": ">=5.6.0 <9.0"}, "conflict": {"codeception/codeception": "<4.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Codeception module containing various assertions", "homepage": "https://codeception.com/", "keywords": ["assertions", "asserts", "codeception"], "support": {"issues": "https://github.com/Codeception/module-asserts/issues", "source": "https://github.com/Codeception/module-asserts/tree/1.3.1"}, "time": "2020-10-21T16:48:15+00:00"}, {"name": "codeception/module-filesystem", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/Codeception/module-filesystem.git", "reference": "781be167fb1557bfc9b61e0a4eac60a32c534ec1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-filesystem/zipball/781be167fb1557bfc9b61e0a4eac60a32c534ec1", "reference": "781be167fb1557bfc9b61e0a4eac60a32c534ec1", "shasum": ""}, "require": {"codeception/codeception": "^4.0", "php": ">=5.6.0 <9.0", "symfony/finder": ">=2.7 <6.0"}, "conflict": {"codeception/codeception": "<4.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module for testing local filesystem", "homepage": "http://codeception.com/", "keywords": ["codeception", "filesystem"], "support": {"issues": "https://github.com/Codeception/module-filesystem/issues", "source": "https://github.com/Codeception/module-filesystem/tree/1.0.3"}, "time": "2020-10-24T14:46:40+00:00"}, {"name": "codeception/module-yii2", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/Codeception/module-yii2.git", "reference": "14269d059b8eaedf3d414a673907bd874cd4ed04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-yii2/zipball/14269d059b8eaedf3d414a673907bd874cd4ed04", "reference": "14269d059b8eaedf3d414a673907bd874cd4ed04", "shasum": ""}, "require": {"codeception/codeception": "^4.0", "codeception/lib-innerbrowser": "^1.0", "php": ">=5.6.0 <=8.1 | ~8.1.0"}, "require-dev": {"codeception/module-asserts": "^1.3", "codeception/module-filesystem": "^1.0", "codeception/verify": "<2", "codemix/yii2-localeurls": "^1.7", "yiisoft/yii2": "dev-master", "yiisoft/yii2-app-advanced": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Codeception module for Yii2 framework", "homepage": "http://codeception.com/", "keywords": ["codeception", "yii2"], "support": {"issues": "https://github.com/Codeception/module-yii2/issues", "source": "https://github.com/Codeception/module-yii2/tree/1.1.5"}, "time": "2021-12-30T09:16:01+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "8.1.6", "source": {"type": "git", "url": "https://github.com/Codeception/phpunit-wrapper.git", "reference": "7d3479bab7e2b6349044db8af11cd05d57809f9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/phpunit-wrapper/zipball/7d3479bab7e2b6349044db8af11cd05d57809f9c", "reference": "7d3479bab7e2b6349044db8af11cd05d57809f9c", "shasum": ""}, "require": {"php": ">=7.2", "phpunit/php-code-coverage": "^7.0", "phpunit/phpunit": "^8.0", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0"}, "require-dev": {"codeception/specify": "*", "vlucas/phpdotenv": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHPUnit classes used by Codeception", "support": {"issues": "https://github.com/Codeception/phpunit-wrapper/issues", "source": "https://github.com/Codeception/phpunit-wrapper/tree/8.1.6"}, "time": "2022-05-23T06:22:33+00:00"}, {"name": "codeception/specify", "version": "0.4.6", "source": {"type": "git", "url": "https://github.com/Codeception/Specify.git", "reference": "21b586f503ca444aa519dd9cafb32f113a05f286"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Specify/zipball/21b586f503ca444aa519dd9cafb32f113a05f286", "reference": "21b586f503ca444aa519dd9cafb32f113a05f286", "shasum": ""}, "require": {"myclabs/deep-copy": "~1.1", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-0": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD code blocks for PHPUnit and Codeception", "support": {"issues": "https://github.com/Codeception/Specify/issues", "source": "https://github.com/Codeception/Specify/tree/master"}, "time": "2016-10-21T09:42:00+00:00"}, {"name": "codeception/stub", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "18a148dacd293fc7b044042f5aa63a82b08bff5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/18a148dacd293fc7b044042f5aa63a82b08bff5d", "reference": "18a148dacd293fc7b044042f5aa63a82b08bff5d", "shasum": ""}, "require": {"php": "^7.4 | ^8.0", "phpunit/phpunit": "^8.4 | ^9.0 | ^10.0 | 10.0.x-dev"}, "require-dev": {"consolidation/robo": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "support": {"issues": "https://github.com/Codeception/Stub/issues", "source": "https://github.com/Codeception/Stub/tree/4.0.2"}, "time": "2022-01-31T19:25:15+00:00"}, {"name": "codeception/verify", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "fa0bb946b6d61279f461bcc5a677ac0ed5eab9b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/fa0bb946b6d61279f461bcc5a677ac0ed5eab9b3", "reference": "fa0bb946b6d61279f461bcc5a677ac0ed5eab9b3", "shasum": ""}, "require": {"codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.0.4", "php": ">= 7.0", "phpunit/phpunit": "> 6.0"}, "type": "library", "autoload": {"files": ["src/Codeception/function.php"], "psr-4": {"Codeception\\": "src\\Codeception"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD assertion library for PHPUnit", "support": {"issues": "https://github.com/Codeception/Verify/issues", "source": "https://github.com/Codeception/Verify/tree/master"}, "time": "2019-07-30T18:21:46+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-03-03T08:28:38+00:00"}, {"name": "fakerphp/faker", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/37f751c67a5372d4e26353bd9384bc03744ec77b", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "symfony/phpunit-bridge": "^4.4 || ^5.2"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "extra": {"branch-alias": {"dev-main": "v1.20-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.20.0"}, "time": "2022-07-20T13:12:54+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/fc1156187f9f6c8395886fe85ed88a0a245d72e9", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "support": {"source": "https://github.com/phpspec/php-diff/tree/v1.1.3"}, "time": "2020-09-18T13:47:07+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.15", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "819f92bba8b001d4363065928088de22f25a3a48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/819f92bba8b001d4363065928088de22f25a3a48", "reference": "819f92bba8b001d4363065928088de22f25a3a48", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": ">=7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.3 || ^4.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.15"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-07-26T12:20:09+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:42:26+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:20:02+00:00"}, {"name": "phpunit/php-token-stream", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/a853a0e183b9db7eed023d7933a858fa1c8d25a3", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/master"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2020-08-04T08:28:15+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.29", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "e8c563c47a9a303662955518ca532b022b337f4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/e8c563c47a9a303662955518ca532b022b337f4d", "reference": "e8c563c47a9a303662955518ca532b022b337f4d", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.0", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.2", "phpunit/php-code-coverage": "^7.0.12", "phpunit/php-file-iterator": "^2.0.4", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.3", "sebastian/exporter": "^3.1.2", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/8.5.29"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-08-22T13:59:39+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:31:48+00:00"}, {"name": "sebastian/diff", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:59:04+00:00"}, {"name": "sebastian/environment", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:53:42+00:00"}, {"name": "sebastian/exporter", "version": "3.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "73a9676f2833b9a7c36968f9d882589cd75511e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/73a9676f2833b9a7c36968f9d882589cd75511e6", "reference": "73a9676f2833b9a7c36968f9d882589cd75511e6", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T06:00:17+00:00"}, {"name": "sebastian/global-state", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/de036ec91d55d2a9e0db2ba975b512cdb1c23921", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-02-10T06:55:38+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:30:19+00:00"}, {"name": "sebastian/type", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/0150cfbc4495ed2df3872fb31b26781e4e077eb4", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/1.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:25:11+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v4.2.4", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "61d85c5af2fc058014c7c89504c3944e73a086f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/61d85c5af2fc058014c7c89504c3944e73a086f0", "reference": "61d85c5af2fc058014c7c89504c3944e73a086f0", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "~3.4|~4.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0", "symfony/process": "~3.4|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/4.2"}, "time": "2019-02-23T15:17:42+00:00"}, {"name": "symfony/console", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "c072aa8f724c3af64e2c7a96b796a4863d24dba1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c072aa8f724c3af64e2c7a96b796a4863d24dba1", "reference": "c072aa8f724c3af64e2c7a96b796a4863d24dba1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.12"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-17T13:18:05+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "c1681789f059ab756001052164726ae88512ae3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/c1681789f059ab756001052164726ae88512ae3d", "reference": "c1681789f059ab756001052164726ae88512ae3d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.4.45", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "4b8daf6c56801e6d664224261cb100b73edc78a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/4b8daf6c56801e6d664224261cb100b73edc78a5", "reference": "4b8daf6c56801e6d664224261cb100b73edc78a5", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v4.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-03T12:57:57+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-05T16:45:39+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f98b54df6ad059855739db6fcbc2d36995283fe1", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/finder", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/7872a66f57caffa2916a584db1aa7f12adc76f8c", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:37:50+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/433d05519ce6990bf3530fba6957499d327395c2", "reference": "433d05519ce6990bf3530fba6957499d327395c2", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/e440d35fa0286f77fb45b79a03fedbeda9307e85", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/191afdcb5804db960d26d8566b7e9a2843cab3a0", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/container": "", "symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.2"}, "time": "2019-05-28T07:50:59+00:00"}, {"name": "symfony/string", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "2fc515e512d721bf31ea76bd02fe23ada4640058"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/2fc515e512d721bf31ea76bd02fe23ada4640058", "reference": "2fc515e512d721bf31ea76bd02fe23ada4640058", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.12"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-12T17:03:11+00:00"}, {"name": "symfony/yaml", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.12"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:52:22+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.21", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "fa9fa02e242c1b744eb68045ee8e9d698134836d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/fa9fa02e242c1b744eb68045ee8e9d698134836d", "reference": "fa9fa02e242c1b744eb68045ee8e9d698134836d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}}}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2022-09-06T17:40:08+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "shasum": ""}, "require": {"fakerphp/faker": "~1.9|~1.10", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\faker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-faker/issues", "source": "https://github.com/yiisoft/yii2-faker", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-faker", "type": "tidelift"}], "time": "2020-11-10T12:27:35+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "d879cb186361fbc6f71a2d994d580b5a071a5642"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/d879cb186361fbc6f71a2d994d580b5a071a5642", "reference": "d879cb186361fbc6f71a2d994d580b5a071a5642", "shasum": ""}, "require": {"phpspec/php-diff": "^1.1.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"phpunit/phpunit": "<7", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\gii\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "gii", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-gii/issues", "source": "https://github.com/yiisoft/yii2-gii", "wiki": "http://www.yiiframework.com/wiki/"}, "time": "2020-01-17T13:33:30+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"offgamers/microservices-multiservice": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.3.0", "ext-dom": "*", "ext-bcmath": "*", "ext-fileinfo": "*", "ext-curl": "*"}, "platform-dev": [], "plugin-api-version": "2.1.0"}