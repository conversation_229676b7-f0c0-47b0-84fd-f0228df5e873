<?php

Yii::setAlias('@micro', dirname(__DIR__));

$params = array_merge(
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php'),
);

return [
    'id' => 'ms-checkout',
    'name' => 'OffGamers Secure Checkout',
    'language' => 'en',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'micro\controllers',
    'components' => [
        'currency' => 'micro\components\Currency',
        'request' => [
            'csrfParam' => '_csrf-checkout',
        ],
        'mailer' => [
            'class' => 'offgamers\base\mail\AWSSESMailer',
            'mailQueueName' => 'MAIL_QUEUE',
        ],
        'user' => [
            'identityClass' => 'micro\models\User',
            'enableAutoLogin' => false,
            'enableSession' => false,
            'loginUrl' => null,
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                'IPN/HandleIPN' => 'pipwave/notification',
                'IPN/HandleIPN/<dp_payment:\w+>' => 'pipwave/notification',
                'Pay/post' => 'payment/handle-pg-redirect'
            ],
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/messages',
                ],
            ],
        ],
    ],
    'params' => $params,
];
