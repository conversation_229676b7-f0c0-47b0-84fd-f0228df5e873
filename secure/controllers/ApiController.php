<?php

namespace micro\controllers;

use micro\components\Pipwave;
use micro\models\Orders;
use micro\models\PipwavePayment;
use micro\models\PipwavePaymentHistory;
use micro\models\PipwavePaymentMapper;
use Yii;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\HttpException;
use micro\models\CheckoutTransaction;

class ApiController extends \yii\web\Controller
{
    public $input;

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        Yii::$app->response->format = 'json';
        $input_json = Yii::$app->getRequest()->getRawBody();
        $input = Json::decode($input_json);
        $headers = Yii::$app->request->headers;

        $api_credential = Yii::$app->params['api.credential'];

        $this->input = $input['data'];

        if (isset($input['api_key']) && isset($api_credential[$input['api_key']])) {
            $secret = $api_credential[$input['api_key']];
            if (isset($headers['signature'])) {
                if ($headers['signature'] == hash_hmac('sha256', $input_json, $secret)) {
                    if (!empty($input['data'])) {
                        $this->input = $input['data'];
                    }
                    return parent::beforeAction($action);
                }
            }
        }

        throw new HttpException(401, 'NOT AUTHORIZED');
    }

    public function actionCreateTransaction()
    {
        $code = CheckoutTransaction::saveTransactionData($this->input);

        $response_data = array(
            'result_code' => '2000',
            'access_token' => $code->access_code,
            'signature' => $code->generateSignature(),
            'url' => Url::toRoute(['payment/index'], true),
        );

        return ['response' => $response_data];
    }

    public function actionCheckPaymentMethod()
    {
        try {
            $orders_id = $this->input['orders_id'];
            if ($orders = Orders::findOne($orders_id)) {
                if ($orders->payment_methods_id == 0) {
                    $pipwave = new Pipwave;
                    $data = $pipwave->checkOrder($this->input['orders_id']);
                    if(isset($data['status']) && $data['status'] == 9001){
                        return ;
                    }
                    $data['api_key'] = $pipwave->key;
                    $data['type'] = 'requery';
                    $data['pg_status'] = ($data['pg_status'] ?? '');
                    $data['txn_sub_status'] = ($data['txn_sub_status'] ?? '');

                    $payment_mapper = PipwavePaymentMapper::getPaymentInfo($data['payment_method_code']);
                    PipwavePayment::updatePipwaveData($orders_id, $data, $payment_mapper);
                    $orders->setOrdersPaymentMethod($payment_mapper);
                    $orders->updateSurchargeAmount($data);
                    PipwavePaymentHistory::savePipwavePaymentHistory($data);
                }
            }
        } catch (\Exception $e) {
            $this->reportError('Fail to update Pipwave Payment', $e);
            throw $e;
        }

    }

    public function reportError($title, $exception)
    {
        Yii::$app->slack->send($title, array(
            array(
                'color' => 'warning',
                'text' => $exception->getTraceAsString(),
            ),
        ));
    }

}