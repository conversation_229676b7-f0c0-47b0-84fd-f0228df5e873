<?php

namespace micro\controllers;

use Yii;
use yii\helpers\Json;
use yii\web\Controller;
use micro\models\CheckoutTransaction;
use micro\components\Pipwave;
use yii\web\HttpException;

class PaymentController extends Controller
{
    public function beforeAction($action)
    {
        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionIndex()
    {
        $request = Yii::$app->request;

        $access_token = $request->post('access_token', '');
        $signature = $request->post('s', '');

        if ($access_token && $signature && $transaction = CheckoutTransaction::getCheckoutTransaction($access_token, $signature)) {
            $pipwave = new Pipwave();
            $data = Json::decode($transaction->transaction_detail);
            if ($data['language_id'] != 1) {
                Yii::$app->language = ($data['language_id'] == 2 ? 'zh-CN' : 'id');
            }
            $token = $pipwave->initiatePayment($data, $data['orders_id']);
            return $this->render('pipwave', ['token' => $token, 'api_key' => $pipwave->key, 'sdk_url' => $pipwave->sdk_url, 'data' => $data]);
        } elseif (isset($_GET['pipwaveTxn'])) {
            $this->redirect(Yii::$app->params['frontend']['baseUrl'] . '/account/purchase/order/' . $_GET['pipwaveTxn']);
        } elseif ($access_token && $signature) {
            $sig = explode('-', $signature);
            $orders_id = end($sig);
            $this->redirect(Yii::$app->params['frontend']['baseUrl'] . '/account/purchase/order/' . $orders_id);
        } else {
            throw new HttpException(401, 'Unauthorized');
        }
    }

    public function actionCheckoutSuccess()
    {
        if (isset($_GET['pipwaveTxn'])) {
            $this->redirect(Yii::$app->params['frontend']['baseUrl'] . '/checkout/buyNow/success?oid=' . $_GET['pipwaveTxn']);
        } else {
            throw new HttpException(300,'Order ID not found');
        }
    }

    public function actionHandlePgRedirect()
    {
        $request = Yii::$app->getRequest();
        if ($request->isPost) {
            if ($request->post('actionUrl')) {
                return $this->renderPartial('post', ['data' => $request->post()]);
            }
        }
    }
}