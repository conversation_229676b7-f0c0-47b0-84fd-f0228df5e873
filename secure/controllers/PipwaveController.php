<?php

namespace micro\controllers;

use offgamers\base\models\IncomingRequestLog;
use Yii;
use yii\helpers\Json;
use yii\web\Controller;
use micro\components\Pipwave;

class PipwaveController extends Controller
{
    public function beforeAction($action)
    {
        $headers = Yii::$app->response->headers;
        $headers->add('Pragma', 'no-cache');
        $headers->add('Cache-Control', 'no-cache');
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionNotification()
    {
        $input_json = Yii::$app->getRequest()->getRawBody();
        if (Yii::$app->getRequest()->get('dp_payment', '') == 'pipwave') {
            $data = Json::decode($input_json);
            $pipwave = new Pipwave;
            if ($pipwave->verifyNotificationSignature($data)) {
                $orderId = $data['txn_id'];
                (new IncomingRequestLog())->createRequestLog(['tag' => 'IPN_' . $orderId]);
                $mail_sqs = Yii::$app->aws->getSQS('PIPWAVE_QUEUE');
                $mail_sqs->pushMessage(['data' => $data]);
                echo 'OK';
                Yii::$app->end();
            }
        }
    }
}
