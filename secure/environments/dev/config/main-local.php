<?php

$config = [
    'components' => [
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['warning', 'error'],
                ],
            ],
        ],
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '',
        ],
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=offgamers',
            'username' => '',
            'password' => '',
            'charset' => 'utf8',
            'enableSchemaCache' => !YII_DEBUG,
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_offgamers' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=offgamers',
            'username' => '',
            'password' => '',
            'charset' => 'latin1',
            'enableSchemaCache' => !YII_DEBUG,
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_og' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=og',
            'username' => '',
            'password' => '',
            'charset' => 'utf8',
            'enableSchemaCache' => !YII_DEBUG,
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'db_log' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=localhost;dbname=offgamers_log',
            'username' => '',
            'password' => '',
            'charset' => 'utf8',
            'enableSchemaCache' => YII_DEBUG,
            'schemaCacheDuration' => 3600,
            'schemaCache' => 'cache',
        ],
        'aws' => [
            'class' => '\offgamers\base\components\AWS',
            'key' => '',
            'secret' => '',
            'version' => 'latest',
            'region' => 'us-east-1',
            's3' => [
                'BUCKET_ENCRYPT_LOG' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD',
                    'sse_kms_key' => ''
                ],
                'BUCKET_UPLOAD' => [
                    'bucket_key' => '',
                    'acl' => 'private',
                    'prefix_path' => '',
                    'storage' => 'STANDARD'
                ],
                'BUCKET_TOOLBOX' => [
                    'prefix_path' => '',
                    'bucket_key' => '',
                    'acl' => 'private',
                    'storage' => 'STANDARD',
                ],
            ],
            'sqs' => [
                'MAIL_QUEUE' => [
                    'queue_url' => '',
                ],
            ],
        ],
        'slack' => [
            'class' => 'offgamers\base\components\Slack',
            'webhook' => [
                'DEFAULT' => '',
            ],
        ],
        'cache' => [
            'class' => '\offgamers\base\components\MemCache',
            'keyPrefix' => "",
            'servers' => [
                [
                    'host' => 'localhost',
                    'port' => '11211',
                    'weight' => 50,
                ],
            ],
        ],
    ],
];

return $config;