<?php
return array (
    'HTML_PARAMS' => 'dir="ltr" lang="en"',
    'ENTRY_PAYMENT_SURCHARGE' => 'Processing Fee',
    'MODULE_ORDER_TOTAL_SURCHARGE_DISPLAY_TITLE' => 'Processing Fee(<a href="http://kb.offgamers.com/en/category/payment-option/" target="_blank">?</a>)',
    'MODULE_ORDER_TOTAL_SURCHARGE_DESCRIPTION' => 'Processing Fee' , 
    'MODULE_ORDER_TOTAL_SURCHARGE_USER_PROMPT' => '&nbsp;%s',
    'TEXT_ENTER_SURCHARGE_CODE' => '<span class="inputLabel">Processing Fee&nbsp;&nbsp;</span>',
    'HEADING_SUBTITLE' => 'SELECT PAYMENT METHOD IN %s',
    'TEXT_INFO_PAYMENT_IN_USD_OR' => 'OR',
    'TEXT_INFO_CHANGE_CHECKOUT_CURRENCY' => 'To view other payment methods, you will have to change your checkout currency.',
    'TEXT_INFO_CURRENT_CHECKOUT_CURRENCY' => 'Current Checkout Currency',
    'TEXT_INFO_PAYMENT_METHOD_OFFLINE' => 'Remark: Pricing may differ from prices available at the time of ordering if you selected this payment method.',
    'TEXT_INFO_PAYMENT_MANUAL_CHECK' => '<b>Manual checking method</b><br />In the event of out of stock / price fluctuations that resulted unmatched amount, will be credited to user\'s Store Credit.',
    'HEADING_TITLE' => 'Payment Information',
    'TEXT_INFO_PAYMENT_SURCHARGE_DESC' => '<b>Processing Fee</b><br />Please take note that processing fee is applicable per transaction, for selected payment methods only. <a href="http://kb.offgamers.com/en/category/payment-option/" target="_blank">More Info</a>.',
    'TEXT_ORDER_FULLY_COVERED_BY_CREDITS' => 'You do not need to select a payment method since the total amount can be fully debited from your store credit.',
    'TABLE_HEADING_PRODUCT' => 'PRODUCT',
    'TABLE_HEADING_AMOUNT' => 'AMOUNT',
    'TABLE_HEADING_QUANTITY' => 'QTY',
    'ERROR_NO_PAYMENT_MODULE_SELECTED' => 'No Payment Selected',
    'BUTTON_CHECKOUT' => 'CHECKOUT',
    'ERROR_NO_PAYMENT_CURRENCY_SELECTED' => 'No Currency Selected',
    'ERROR_NO_AMOUNT_FOUND' => 'No Amount Found',
    'ERROR_NO_TRANSACTION_ID_FOUND' => 'No Transaction ID Found',
    'ERROR_CURRENCY_NOT_SUPPORTED' => 'Currency not supported',
    'ERROR_COUNTRY_NOT_SUPPORTED' => 'Country not supported',
//    'ERROR_NO_LANGUAGE_FOUND' => 'Language Not Found',
    'ERROR_NO_ORDER_ID_FOUND' => 'Order ID Not Found',
    'ERROR_AMOUNT_MISMATCH' => 'Amount Mismatch',
    'ERROR_CURRENCY_MISMATCH' => 'Currency Mismatch',
    'ERROR_TRANSACTION_ID_MISMATCH' => 'Transaction ID Mismatch',
    'ERROR_NO_PG_URL_FOUND' => 'PG ERROR - NO URL',
    'ERROR_NO_FORCE_CHECKOUT_FOUND' => 'PG ERROR - NO FORCE TO CHECKOUT',
    'ERROR_ORDER_ID_ERROR' => 'PG ERROR - ORDER ID ERROR',
    'ERROR_ACCESS_CODE_EXPIRED' => 'PG ERROR - ACCESS CODE EXPIRED', 
    'ERROR_ACCESS_CODE_NOT_FOUND' => 'PG ERROR - ACCESS CODE NOT FOUND',
    'ERROR_MISSING_CHECKOUT_DATA' => 'PG ERROR - MISSING CHECKOUT DATA',
    'ERROR_NO_ORDER_SUCCESS_CHECKOUT' => 'Something wrong with the checkout success page, please check your order status from your account.',
    'ERROR_MISSING_PG_INFO' => 'PG ERROR - MISSING PG INFO',
    'ERROR_UNSUPPORTED_CURRENCY_FOUND' => 'PG ERROR - UNSUPPORTED CURRENCY FOUND, PLEASE SELECT NEW CURRENCY TO CHECKOUT OR RETURN TO MERCHANT SITE',
    'ERROR_MISSING_MERCHANT_URL' => 'PG ERROR - MISSING MERCHANT SUCCESS URL',
    'ERROR_PAYPAL_IP_COUNTRY_RESTRICTION' => 'The IP detected has restriction upon checkout with PayPal.',
    'TEXT_EDIT_ADDRESS' => '<b>Edit Address?</b>',
    'LINK_EDIT_TELEPHONE' => 'Edit Contact Number?',
    'LINK_UPDATE_CUSTOMER_PAYMENT_INFO' => 'Confirm',
    'ENTRY_PAYMENT_CONTACT_NUMBER' => 'Contact Number:',
    'ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER' => 'IC number:<BR>(Last 4 Digit)',
    'TEXT_FEATURED_PAYMENT_METHODS' => 'FEATURED PAYMENT METHODS',
    'TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE' => '(123456-14-<font color="red">XXXX</font>)',
    'LINK_EDIT_SHOPPING_CART' => 'Edit Cart',
    'TEXT_TOTAL' => 'Total: ',
    'ENTRY_PAYMENT_CUSTOMER_SURNAME' => 'Surname',
    'ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER' => 'House Number',
    'ENTRY_PAYMENT_CUSTOMER_STREET' => 'Street',
    'ENTRY_PAYMENT_CUSTOMER_ZIP' => 'Zip',
    'ENTRY_PAYMENT_CUSTOMER_CITY' => 'City',
    'ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME' => 'Account Name',
    'ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER' => 'Account Number',
    'ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT' => 'Direct Debit Text',
    'ENTRY_PAYMENT_CUSTOMER_BANKCODE' => 'Bank Code',
    'ENTRY_PAYMENT_CUSTOMER_BRANCHCODE' => 'Branch Code',
    'ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER' => 'Voucher Number',
    'ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE' => 'Voucher Value',
    'ENTRY_TELEPHONE_NUMBER_ERROR' => 'The telephone number must be min %s chars',
    'IMAGE_BUTTON_REDEEM_VOUCHER' => 'Redeem',
    'LINK_COUPON_CODE' => 'Redeem Discount Coupon',
    'AJAX_ERROR_COUPON_MISSING' => 'Please enter a valid Coupon',
    'TEXT_INFO_PAYMENT_CONFIRM_CAPTION' => 'Checkout will proceed to %s',
    'CHARSET' => 'utf-8',
	'HEADING_CONFIRM_PAYMENT' => 'CONFIRM YOUR PAYMENT',
	'TITLE_PRODUCT_TITLE' => 'Product',
	'TITLE_QUANTITY' => 'Quantity',
	'TITLE_ORDER_AMOUNT' => 'Unit Price',
	'TITLE_PAY_TO' => 'Pay to',
	'TITLE_PROCESSING_FEE' => 'Processing fee',
	'TITLE_TOTAL_AMOUNT' => 'Total amount',
	'TITLE_BUTTON_CANCEL' => 'Cancel',
	'TITLE_BUTTON_CONFIRM_PAYMENT' => '1 Click Paypal Express Checkout',
	'TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE' => 'Please try again, and if the issue persists, <a href=\'http://support.offgamers.com/support/home\'>contact us</a>.',
	'TITLE_DEVICEPIN_MESSAGE' => 'Security PIN send to your email',
	'TITLE_DEVICEPIN' => 'Security PIN',
	'ERROR_DEVICEPIN_MISSING' => 'Security PIN is Missing',
	'ERROR_DEVICEPIN_INCORRECT' => 'Security PIN is Incorrect',
	'EMAIL_DEVICE_TITLE' => '[{SYS_STORENAME}] Your are accessing from different device / browser',
	'EMAIL_DEVICE_HEADER'=> 'Dear %s,',
	'EMAIL_DEVICE_CONTENT_1' => 'Are you checking out with <b>Payment method: %s</b> from a new browser or new device? Here is the Security PIN to complete the checkout:',
	'EMAIL_DEVICE_CONTENT_2' => 'If you suspect someone else may be attempting to access your account, please change your password immediately.',
	'EMAIL_DEVICE_CONTENT_3' => 'Thank you for maintaining the security of your account.',
	'LINK_RESEND_SECURITY_PIN' => 'Resend Security Pin',
	'ERROR_MAXIMUN_SECURITY_PIN_TRIAL' => 'Maximum {SYS_MAX_TRIAL} attempts occured, please try again in an hour time.',
	'FOOTER_COPYRIGHT_MESSAGE' => 'Copyright © {SYS_YEAR} OffGamers. All rights reserved.</br>All product names are trademarks of their respective companies.',
	'FOOTER_G2G_COPYRIGHT_MESSAGE' => 'Copyright © {SYS_YEAR} G2G. All rights reserved.</br>All product names are trademarks of their respective companies.',
	'TEXT_PAY_IN' => 'Pay in',
	'TEXT_TOTAL_AMOUNT' => 'Payable Amount',
	'TEXT_ORDER_AMOUNT' => 'Order Amount',
	'TEXT_PRECHECKOUT' => 'Cart',
	'TEXT_PAYMENT' => 'Payment',
	'TEXT_ORDER_SUCCESS' => 'Order success',
    'TABLE_HEADING_YOUR_ORDER' => 'YOUR ORDER',
    'FRESHDESK_CHAT' => "<script
        src='//fw-cdn.com/2623928/3076726.js'
        chat='true'
        widgetId='0d3106b6-e631-4f70-a3d7-2db7d9e40cb6'>
        </script> ",
)
?>