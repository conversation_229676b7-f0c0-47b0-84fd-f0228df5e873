<?php
return array (
    'HTML_PARAMS' => 'dir="LTR" lang="cn" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://www.facebook.com/2008/fbml',
    'ENTRY_PAYMENT_SURCHARGE' => '处理费',
    'MODULE_ORDER_TOTAL_SURCHARGE_DISPLAY_TITLE' => '处理费(<a href="http://kb.offgamers.com/zhcn/category/payment-option/" target="_blank">?</a>)',
    'MODULE_ORDER_TOTAL_SURCHARGE_DESCRIPTION' => '付款方式处理费' , 
    'MODULE_ORDER_TOTAL_SURCHARGE_USER_PROMPT' => '&nbsp;%s',
    'TEXT_ENTER_SURCHARGE_CODE' => '<span class="inputLabel">付款方式处理费&nbsp;&nbsp;</span>',
    'HEADING_SUBTITLE' => '请用%s进行支付',
    'TEXT_INFO_PAYMENT_IN_USD_OR' => '或',
    'TEXT_INFO_CHANGE_CHECKOUT_CURRENCY' => '如要查看其它付款方式，请先更换结账货币。',
    'TEXT_INFO_CURRENT_CHECKOUT_CURRENCY' => '当前结账货币',
    'TEXT_INFO_PAYMENT_METHOD_OFFLINE' => '备注：如果选择此付款方式，定价可能与订购时的价格不同。',
    'TEXT_INFO_PAYMENT_MANUAL_CHECK' => '<b>手动检查方法</b><br />因库存不足或价格有波动导致金额无法匹配，款项会被打到用户SC里面',
    'HEADING_TITLE' => '付款情况',
    'TEXT_INFO_PAYMENT_SURCHARGE_DESC' => '<b>处理费</b><br />请注意，处理费适用于特定付款方式的每一笔交易。 <a href="http://kb.offgamers.com/zhcn/category/payment-option/" target="_blank">更多详情</a>',
    'TEXT_ORDER_FULLY_COVERED_BY_CREDITS' => '因为可以从您的商店信用中扣除总金额，所以您不必选择付款方式.',
    'TABLE_HEADING_PRODUCT' => '产品',
    'TABLE_HEADING_AMOUNT' => '总额',
    'TABLE_HEADING_QUANTITY' => '数量.',
    'ERROR_NO_PAYMENT_MODULE_SELECTED' => 'No Payment Selected',
    'BUTTON_CHECKOUT' => '结账',
    'ERROR_NO_PAYMENT_CURRENCY_SELECTED' => 'No Currency Selected',
    'ERROR_NO_AMOUNT_FOUND' => 'No Amount Found',
    'ERROR_NO_TRANSACTION_ID_FOUND' => 'No Transaction ID Found',
    'ERROR_CURRENCY_NOT_SUPPORTED' => '货币不被支持',
    'ERROR_COUNTRY_NOT_SUPPORTED' => 'Country not supported',
//    'ERROR_NO_LANGUAGE_FOUND' => 'Language Not Found',
    'ERROR_NO_ORDER_ID_FOUND' => 'Order ID Not Found',
    'ERROR_AMOUNT_MISMATCH' => 'Amount Mismatch',
    'ERROR_CURRENCY_MISMATCH' => 'Currency Mismatch',
    'ERROR_TRANSACTION_ID_MISMATCH' => 'Transaction ID Mismatch',
    'ERROR_NO_PG_URL_FOUND' => 'PG ERROR - NO URL',
    'ERROR_NO_FORCE_CHECKOUT_FOUND' => 'PG ERROR - NO FORCE TO CHECKOUT',
    'ERROR_ORDER_ID_ERROR' => 'PG ERROR - ORDER ID ERROR',
    'ERROR_ACCESS_CODE_EXPIRED' => 'PG ERROR - ACCESS CODE EXPIRED', 
    'ERROR_ACCESS_CODE_NOT_FOUND' => 'PG ERROR - ACCESS CODE NOT FOUND',
    'ERROR_MISSING_CHECKOUT_DATA' => 'PG ERROR - MISSING CHECKOUT DATA',
    'ERROR_NO_ORDER_SUCCESS_CHECKOUT' => 'Something wrong with the checkout success page, please check your order status from your account.',
    'ERROR_MISSING_PG_INFO' => 'PG ERROR - MISSING PG INFO',
    'ERROR_UNSUPPORTED_CURRENCY_FOUND' => 'PG ERROR - UNSUPPORTED CURRENCY FOUND, PLEASE SELECT NEW CURRENCY TO CHECKOUT OR RETURN TO MERCHANT SITE',
    'ERROR_MISSING_MERCHANT_URL' => 'PG ERROR - MISSING MERCHANT SUCCESS URL',
    'ERROR_PAYPAL_IP_COUNTRY_RESTRICTION' => 'The IP detected has restriction upon checkout with PayPal.',
    'TEXT_EDIT_ADDRESS' => '编辑地址？',
    'LINK_EDIT_TELEPHONE' => '编辑联系电话？',
    'LINK_UPDATE_CUSTOMER_PAYMENT_INFO' => '确定',
    'ENTRY_PAYMENT_CONTACT_NUMBER' => '联系电话：',
    'ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER' => '身份证号码：<BR>（最后4个号码）',
    'TEXT_FEATURED_PAYMENT_METHODS' => '推荐付款方式',
    'TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE' => '（123456-14-<font color="red">XXXX</font>）',
    'LINK_EDIT_SHOPPING_CART' => '编辑购物车',
    'TEXT_TOTAL' => '共计: ',
    'ENTRY_PAYMENT_CUSTOMER_SURNAME' => 'Surname',
    'ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER' => 'House Number',
    'ENTRY_PAYMENT_CUSTOMER_STREET' => 'Street',
    'ENTRY_PAYMENT_CUSTOMER_ZIP' => 'Zip',
    'ENTRY_PAYMENT_CUSTOMER_CITY' => 'City',
    'ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME' => 'Account Name',
    'ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER' => 'Account Number',
    'ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT' => 'Direct Debit Text',
    'ENTRY_PAYMENT_CUSTOMER_BANKCODE' => 'Bank Code',
    'ENTRY_PAYMENT_CUSTOMER_BRANCHCODE' => 'Branch Code',
    'ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER' => 'Voucher Number',
    'ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE' => 'Voucher Value',
    'ENTRY_TELEPHONE_NUMBER_ERROR' => 'The telephone number must be min %s chars',
    'IMAGE_BUTTON_REDEEM_VOUCHER' => '兑换',
    'LINK_COUPON_CODE' => '折扣优惠卷',
    'AJAX_ERROR_COUPON_MISSING' => 'Please enter a valid Coupon',
    'TEXT_INFO_PAYMENT_CONFIRM_CAPTION' => '通过%s结账',
    'CHARSET' => 'UTF-8',
	'HEADING_CONFIRM_PAYMENT' => '请确认您支付的交易款项',
	'CONTENT_PAYMENT_REVIEW' => '检查款项细节如下，然后点击<b>确认付款</b>以完成您的安全支付',
	'TITLE_PRODUCT_TITLE' => '产品',
	'TITLE_QUANTITY' => '数量',
	'TITLE_ORDER_AMOUNT' => '单价',
	'TITLE_PAY_TO' => '收款人',
	'TITLE_PROCESSING_FEE' => '手续费/处理费',
	'TITLE_TOTAL_AMOUNT' => '总金额',
	'TITLE_BUTTON_CANCEL' => '取消',
	'TITLE_BUTTON_CONFIRM_PAYMENT' => '确认付款',
	'TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE' => '如果错误仍然存在，请点击<a href=\'http://support.offgamers.com/support/home\'>这里</a>联系我们。',
	'TITLE_BUTTON_CONFIRM_PAYMENT' => '点击PayPal快速结帐',
	'TITLE_DEVICEPIN_MESSAGE' => '安全码会发送到您的邮箱',
	'TITLE_DEVICEPIN' => '安全码',
	'ERROR_DEVICEPIN_MISSING' => '请输入安全码/安全码不能为空',
	'ERROR_DEVICEPIN_INCORRECT' => '安全码不正确',
	'EMAIL_DEVICE_TITLE' => '[{SYS_STORENAME}] 您正在从不同的设备或浏览器登录',
	'EMAIL_DEVICE_HEADER' => '亲爱的 %s,',
	'EMAIL_DEVICE_CONTENT_1' => '请确认您是否正在使用新的浏览器页面或设备来完成您的使用<b>%s</b>作为支付方式的结账请求? 您的交易安全码为：',
	'EMAIL_DEVICE_CONTENT_2' => '如果您怀疑有其他人正在尝试登录或使用您的帐号, 请立即修改您的密码。',
	'EMAIL_DEVICE_CONTENT_3' => '感谢您对您的账户安全所做的保障措施。',
	'LINK_RESEND_SECURITY_PIN' => '重新发送安全码',
	'ERROR_MAXIMUN_SECURITY_PIN_TRIAL' => '已达 {SYS_MAX_TRIAL}次重试上限,请在1小时后重试。',
	'FOOTER_COPYRIGHT_MESSAGE' => 'Copyright ©{SYS_YEAR} OffGamers。版权所有。</br>本文提及的所有商标均为其各自所有者的财产。',
	'FOOTER_G2G_COPYRIGHT_MESSAGE' => 'Copyright ©{SYS_YEAR} G2G。版权所有。</br>本文提及的所有商标均为其各自所有者的财产。',
    'TEXT_PAY_IN' => '货币',
    'TEXT_TOTAL_AMOUNT' => '应付金额',
    'TEXT_ORDER_AMOUNT' => '订单总额',
    'TEXT_PRECHECKOUT' => '购物车',
    'TEXT_PAYMENT' => '付款',
    'TEXT_ORDER_SUCCESS' => '订单成功',
    'TABLE_HEADING_YOUR_ORDER' => '您的订单',
    'FRESHDESK_CHAT' => "<script
        src='//fw-cdn.com/2623928/3076726.js'
        chat='true'
        widgetId='0d3106b6-e631-4f70-a3d7-2db7d9e40cb6'>
        </script> ",
)
?>