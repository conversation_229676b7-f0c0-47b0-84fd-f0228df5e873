<?php

namespace micro\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\Json;

/**
 * This is the model class for table "checkout_transaction".
 *
 * @property int $id
 * @property int $merchant_id
 * @property int $customers_id
 * @property int $orders_id
 * @property int $expiry
 * @property string $access_code
 * @property string $transaction_detail
 * @property int $status
 * @property int $created_at
 * @property int $updated_at
 */
class CheckoutTransaction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'checkout_transaction';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_og');
    }

    public function behaviors()
    {
        return [
            TimestampBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'orders_id', 'access_code', 'transaction_detail', 'expiry'], 'required'],
            [['merchant_id', 'customers_id', 'orders_id', 'expiry', 'created_at', 'updated_at'], 'integer'],
            [['transaction_detail'], 'string'],
            [['access_code'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customers_id' => 'Customers ID',
            'orders_id' => 'Orders ID',
            'access_code' => 'Access Code',
            'expiry' => 'Expiry',
            'transaction_detail' => 'Transaction Detail',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public static function getCheckoutTransaction($access_token, $signature)
    {
        $model = self::find()->where(['access_code' => $access_token])->andWhere(['>=', 'expiry', time()])->one();

        if ($model && $model->validateSignature($signature)) {
            return $model;
        }
    }

    public static function saveTransactionData($data)
    {
        $model = self::findOne(['orders_id' => $data['orders_id']]);

        if (!$model) {
            $model = new self;
            $model->load([
                'merchant_id' => 1,
                'customers_id' => $data['customer_id'] ?? 0,
                'orders_id' => $data['orders_id'] ?? 0,
                'expiry' => time() + 1800,
                'access_code' => Yii::$app->security->generateRandomString(),
            ], '');
        }
        //extend expiry and generate new access token if expiry less than 5 minutes
        elseif(time() + 300 > $model->expiry){
            $model->load([
                'expiry' => time() + 1800,
                'access_code' => Yii::$app->security->generateRandomString(),
            ], '');
        }

        $model->transaction_detail = Json::encode($data);

        $model->save();
        return $model;
    }

    private function getHashKey()
    {
        return Yii::$app->params['hashKey'];
    }

    public function getHashString()
    {
        return $this->merchant_id . '-' . $this->customers_id . '-' . $this->orders_id;
    }

    public function generateSignature()
    {
        return Yii::$app->security->hashData($this->getHashString(), $this->getHashKey());
    }

    public function validateSignature($signature)
    {
        return Yii::$app->security->validateData($signature, $this->getHashKey());
    }
}
