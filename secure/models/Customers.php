<?php

namespace micro\models;

use Yii;
use yii\db\Query;

/**
 * This is the model class for table "customers".
 *
 * @property int $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string|null $customers_dob
 * @property int $account_activated
 * @property string $customers_email_address
 * @property int $email_verified
 * @property string $serial_number
 * @property int $customers_default_address_id
 * @property int|null $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string|null $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string|null $customers_newsletter
 * @property string $customers_group_name
 * @property int $customers_group_id
 * @property float $customers_discount
 * @property int $customers_groups_id
 * @property int $customers_aft_groups_id
 * @property int $customers_status
 * @property string $customers_flag
 * @property int $customers_phone_verified
 * @property string|null $customers_phone_verified_by
 * @property string|null $customers_phone_verified_datetime
 * @property int $affiliate_ref_id
 * @property int $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property float $customers_reserve_amount
 * @property string|null $customers_security_start_time
 * @property int $customers_disable_withdrawal
 * @property string $customers_relationship
 *
 */
class Customers extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_dob', 'customers_phone_verified_datetime', 'customers_security_start_time'], 'safe'],
            [
                [
                    'account_activated',
                    'email_verified',
                    'customers_default_address_id',
                    'customers_country_dialing_code_id',
                    'customers_group_id',
                    'customers_groups_id',
                    'customers_aft_groups_id',
                    'customers_status',
                    'customers_phone_verified',
                    'affiliate_ref_id',
                    'ref_id',
                    'customers_disable_withdrawal'
                ],
                'integer'
            ],
            [['customers_discount', 'customers_reserve_amount'], 'number'],
            [['customers_gender'], 'string', 'max' => 1],
            [['customers_firstname', 'customers_lastname', 'customers_telephone', 'customers_mobile', 'customers_fax', 'customers_login_sites'], 'string', 'max' => 32],
            [['customers_email_address', 'customers_msn', 'customers_yahoo'], 'string', 'max' => 96],
            [['serial_number'], 'string', 'max' => 12],
            [['customers_qq', 'customers_icq'], 'string', 'max' => 20],
            [['customers_password', 'customers_pin_number'], 'string', 'max' => 40],
            [['customers_newsletter', 'customers_phone_verified_by', 'customers_merged_profile', 'customers_relationship'], 'string', 'max' => 255],
            [['customers_group_name'], 'string', 'max' => 27],
            [['customers_flag'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'customers_gender' => 'Customers Gender',
            'customers_firstname' => 'Customers Firstname',
            'customers_lastname' => 'Customers Lastname',
            'customers_dob' => 'Customers Dob',
            'account_activated' => 'Account Activated',
            'customers_email_address' => 'Customers Email Address',
            'email_verified' => 'Email Verified',
            'serial_number' => 'Serial Number',
            'customers_default_address_id' => 'Customers Default Address ID',
            'customers_country_dialing_code_id' => 'Customers Country Dialing Code ID',
            'customers_telephone' => 'Customers Telephone',
            'customers_mobile' => 'Customers Mobile',
            'customers_fax' => 'Customers Fax',
            'customers_msn' => 'Customers Msn',
            'customers_qq' => 'Customers Qq',
            'customers_yahoo' => 'Customers Yahoo',
            'customers_icq' => 'Customers Icq',
            'customers_password' => 'Customers Password',
            'customers_pin_number' => 'Customers Pin Number',
            'customers_newsletter' => 'Customers Newsletter',
            'customers_group_name' => 'Customers Group Name',
            'customers_group_id' => 'Customers Group ID',
            'customers_discount' => 'Customers Discount',
            'customers_groups_id' => 'Customers Groups ID',
            'customers_aft_groups_id' => 'Customers Aft Groups ID',
            'customers_status' => 'Customers Status',
            'customers_flag' => 'Customers Flag',
            'customers_phone_verified' => 'Customers Phone Verified',
            'customers_phone_verified_by' => 'Customers Phone Verified By',
            'customers_phone_verified_datetime' => 'Customers Phone Verified Datetime',
            'affiliate_ref_id' => 'Affiliate Ref ID',
            'ref_id' => 'Ref ID',
            'customers_merged_profile' => 'Customers Merged Profile',
            'customers_login_sites' => 'Customers Login Sites',
            'customers_reserve_amount' => 'Customers Reserve Amount',
            'customers_security_start_time' => 'Customers Security Start Time',
            'customers_disable_withdrawal' => 'Customers Disable Withdrawal',
            'customers_relationship' => 'Customers Relationship',
        ];
    }

    public static function getCustomerFlag($customerId)
    {
        $result = self::find()->select('customers_flag')->where(['customers_id' => $customerId])->scalar();

        return $result;
    }

    public static function getCustomerContactInfo($customerId)
    {
        $result = self::find()->select('customers_telephone, customers_default_address_id, customers_lastname, customers_country_dialing_code_id')->where(['customers_id' => $customerId])->asArray()->one();

        return $result;
    }

    public static function getCustomerGroupId($customerId)
    {
        $result = self::find()->select('customers_groups_id')->where(['customers_id' => $customerId])->asArray()->one();

        return $result['customers_groups_id'];
    }

    public static function getCustomerAftGroup($customerId)
    {
        $result = self::find()->select('customers_aft_groups_id')->where(['customers_id' => $customerId])->asArray()->one();

        $aft_group_name = CustomersAftGroups::find()->select('customers_aft_groups_name')->where(['customers_aft_groups_id' => $result['customers_aft_groups_id']])->asArray()->one();

        return $aft_group_name['customers_aft_groups_name'];
    }

    public static function getCustomerCurrentInfo($customerId)
    {
        $query = new Query;
        $query->select([
            'customers_firstname',
            'customers_lastname',
            'customers_gender',
            'customers_email_address',
            'customers_country_dialing_code_id',
            'customers_telephone',
            'customers_fax',
            'customers_dob',
            'customers_login_sites',
            'customers_default_address_id',
            'customers_info_changes_made'
        ])
            ->from(self::tableName() . ' c')
            ->innerJoin(CustomersInfo::tableName() . ' ci', 'c.customers_id = ci.customers_info_id')
            ->where(['c.customers_id' => $customerId]);

        return $query->one();
    }

    public static function updateCustomerPhone($customers_id, $data)
    {
        $params = array(':customersId' => $customers_id);
        $condition = 'customers_id = :customersId';
        self::updateAll($data, $condition, $params);
    }

}
