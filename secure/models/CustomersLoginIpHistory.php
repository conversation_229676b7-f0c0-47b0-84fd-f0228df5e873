<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "customers_login_ip_history".
 *
 * @property int $customers_id
 * @property string $customers_login_date
 * @property string $customers_login_ip
 * @property string $customers_login_ua_info
 * @property string|null $login_method
 */
class CustomersLoginIpHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers_login_ip_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customers_id', 'customers_login_date'], 'required'],
            [['customers_id'], 'integer'],
            [['customers_login_date'], 'safe'],
            [['customers_login_ip', 'login_method'], 'string', 'max' => 128],
            [['customers_login_ua_info'], 'string', 'max' => 255],
            [['customers_id', 'customers_login_date'], 'unique', 'targetAttribute' => ['customers_id', 'customers_login_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'customers_id' => 'Customers ID',
            'customers_login_date' => 'Customers Login Date',
            'customers_login_ip' => 'Customers Login Ip',
            'customers_login_ua_info' => 'Customers Login Ua Info',
            'login_method' => 'Login Method',
        ];
    }

    public static function getLatestLoginHistory($customerId)
    {
        $result = self::find()->select('customers_login_date, customers_login_ip')->where(['customers_id' => $customerId])->orderBy('customers_login_date DESC')->asArray()->one();

        return $result;
    }

    public function getLoginHistory($customerId){
        return self::find()->select(['customers_login_date','customers_login_ip'])
            ->where(['customers_id' => $customerId])
            ->asArray()
            ->one();
    }

}
