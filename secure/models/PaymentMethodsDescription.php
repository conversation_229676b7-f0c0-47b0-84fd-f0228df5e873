<?php

namespace micro\models;

use Yii;

/**
 * This is the model class for table "payment_methods_description".
 *
 * @property int $payment_methods_id
 * @property int $languages_id
 * @property string $payment_methods_description_title
 */
class PaymentMethodsDescription extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'payment_methods_description';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['payment_methods_id', 'languages_id', 'payment_methods_description_title'], 'required'],
            [['payment_methods_id', 'languages_id'], 'integer'],
            [['payment_methods_description_title'], 'string'],
            [['payment_methods_id', 'languages_id'], 'unique', 'targetAttribute' => ['payment_methods_id', 'languages_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'payment_methods_id' => 'Payment Methods ID',
            'languages_id' => 'Languages ID',
            'payment_methods_description_title' => 'Payment Methods Description Title',
        ];
    }
}
