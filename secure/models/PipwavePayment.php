<?php

namespace micro\models;

use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "pipwave_payment".
 *
 * @property int $orders_id
 * @property string $status
 * @property string $api_key
 * @property string|null $notification_id
 * @property string|null $notification_date
 * @property string $pw_id
 * @property float $amount
 * @property float|null $tax_exempted_amount
 * @property float|null $processing_fee_amount
 * @property float|null $tax_amount
 * @property float $total_amount
 * @property float $final_amount
 * @property string $currency_code
 * @property string $transaction_status
 * @property string|null $txn_sub_status
 * @property string $type
 * @property string|null $subscription_token
 * @property int|null $charge_index
 * @property string|null $payment_method_code
 * @property int|null $reversible_payment
 * @property string|null $settlement_account
 * @property int|null $require_capture
 * @property int|null $require_upload
 * @property string|null $mobile_number
 * @property int|null $mobile_number_verification
 * @property string|null $pg_status
 * @property string|null $pg_reason
 * @property string|null $pg_date
 * @property string|null $pg_txn_id
 * @property string|null $pg_raw_data
 * @property string|null $risk_management_data
 * @property string|null $matched_rules
 * @property int|null $pipwave_score
 * @property string|null $rules_action
 * @property string|null $extra_param1
 * @property string|null $extra_param2
 * @property string|null $extra_param3
 * @property string|null $risk_service_type
 */
class PipwavePayment extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pipwave_payment';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db_offgamers');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'api_key', 'pw_id', 'amount', 'total_amount', 'final_amount', 'currency_code', 'type'], 'required'],
            [['orders_id', 'charge_index', 'reversible_payment', 'require_capture', 'require_upload', 'mobile_number_verification', 'pipwave_score'], 'integer'],
            [['notification_date', 'pg_date'], 'safe'],
            [['amount', 'tax_exempted_amount', 'processing_fee_amount', 'tax_amount', 'total_amount', 'final_amount'], 'number'],
            [['pg_raw_data', 'risk_management_data', 'matched_rules'], 'string'],
            [['status', 'transaction_status', 'pg_status'], 'string', 'max' => 5],
            [['api_key', 'pw_id', 'payment_method_code', 'settlement_account', 'mobile_number', 'pg_reason', 'extra_param1', 'extra_param2', 'extra_param3'], 'string', 'max' => 255],
            [['notification_id', 'type', 'subscription_token'], 'string', 'max' => 32],
            [['currency_code'], 'string', 'max' => 3],
            [['txn_sub_status', 'pg_txn_id'], 'string', 'max' => 64],
            [['rules_action'], 'string', 'max' => 16],
            [['risk_service_type'], 'string', 'max' => 128],
            [['orders_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'Orders ID',
            'status' => 'Status',
            'api_key' => 'Api Key',
            'notification_id' => 'Notification ID',
            'notification_date' => 'Notification Date',
            'pw_id' => 'Pw ID',
            'amount' => 'Amount',
            'tax_exempted_amount' => 'Tax Exempted Amount',
            'processing_fee_amount' => 'Handling Fee Amount',
            'tax_amount' => 'Tax Amount',
            'total_amount' => 'Total Amount',
            'final_amount' => 'Final Amount',
            'currency_code' => 'Currency Code',
            'transaction_status' => 'Transaction Status',
            'txn_sub_status' => 'Txn Sub Status',
            'type' => 'Type',
            'subscription_token' => 'Subscription Token',
            'charge_index' => 'Charge Index',
            'payment_method_code' => 'Payment Method Code',
            'reversible_payment' => 'Reversible Payment',
            'settlement_account' => 'Settlement Account',
            'require_capture' => 'Require Capture',
            'require_upload' => 'Require Upload',
            'mobile_number' => 'Mobile Number',
            'mobile_number_verification' => 'Mobile Number Verification',
            'pg_status' => 'Pg Status',
            'pg_reason' => 'Pg Reason',
            'pg_date' => 'Pg Date',
            'pg_txn_id' => 'Pg Txn ID',
            'pg_raw_data' => 'Pg Raw Data',
            'risk_management_data' => 'Risk Management Data',
            'matched_rules' => 'Matched Rules',
            'pipwave_score' => 'Pipwave Score',
            'rules_action' => 'Rules Action',
            'extra_param1' => 'Extra Param1',
            'extra_param2' => 'Extra Param2',
            'extra_param3' => 'Extra Param3',
            'risk_service_type' => 'Risk Service Type',
        ];
    }

    public static function updatePipwaveData($orders_id, $data, $pwPaymentInfo)
    {
        //only save if got data
        if (isset($data['pg_raw_data']) && $data['pg_raw_data'] == '[]') {
            unset($data['pg_raw_data']);
        }

        $model = (self::findOne(['orders_id' => $orders_id]) ?? new self);

        $model->orders_id = $orders_id;

        $date_data = ['notification_date', 'pg_date'];

        foreach ($date_data as $key) {
            $data[$key] = (isset($data[$key])&& !empty($data[$key]) ? date('Y-m-d H:i:s', $data[$key]) : date('Y-m-d H:i:s'));
        }

        $string_data = ['notification_id', 'status', 'transaction_status', 'pg_status', 'txn_sub_status'];

        foreach ($string_data as $key) {
            if (isset($data[$key])) {
                $data[$key] = (string)$data[$key];
            }
        }

        $boolean_data = ['require_capture', 'reversible_payment', 'require_upload'];

        foreach ($boolean_data as $key) {
            $data[$key] = ($data[$key] == 'true' || $data[$key] === true ? 1 : 0);
        }

        $json_data = ['risk_management_data', 'matched_rules'];

        foreach ($json_data as $key) {
            if (isset($data[$key])) {
                $data[$key] = Json::encode($data[$key]);
            }
        }

        if (isset($data['payment_method_code'])) {
            if (isset($pwPaymentInfo['pg_code'])) {
                if (isset($data['pg_raw_data'])) {
                    $pgRawData = Json::decode($data['pg_raw_data']);
                }
                switch ($pwPaymentInfo['pg_code']) {
                    case 'paypalEC':
                    case 'paypal':
                        if (isset($pgRawData['reason_code']) && ($pgRawData['reason_code'] == 'chargeback_settlement' || $pgRawData['reason_code'] == 'chargeback_reimbursement')) {
                            //do not update the payer_id & payer_email in chargeback settlement as paypal will send merchant payer email and id (instead of payer) in this case
                            unset($data['pg_raw_data']);
                            break;
                        }
                }
            }
        }

        if (!empty($data['mobile_number_verification'])) {
            switch ($data['mobile_number_verification']) {
                case 'verified':
                    $data['mobile_number_verification'] = 1;
                    break;
                case 'verified previously':
                    $data['mobile_number_verification'] = 2;
                    break;
                case 'unverified':
                    $data['mobile_number_verification'] = 3;
                    break;
                case 'skipped':
                    $data['mobile_number_verification'] = 0;
                    break;
            }
        }

        $model->load($data, '');
        try {
            if (!$model->save()) {
                throw new \Exception('Failed to save data to Pipwave Payment');
            }
        } catch (\Exception $e) {
            Yii::$app->slack->send('Pipwave API Failed', array(
                array(
                    'color' => 'warning',
                    'text' => json_encode($model->getAttributes()) . json_encode($model->getErrors()) . $e->getTraceAsString(),
                ),
            ));
            throw $e;
        }
    }


}
