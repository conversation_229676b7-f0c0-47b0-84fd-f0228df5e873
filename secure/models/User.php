<?php

namespace micro\models;

use Yii;
use yii\web\IdentityInterface;

class User implements IdentityInterface
{
    public $id;

    public function getId()
    {
        return $this->id;
    }

    public static function findIdentity($id)
    {
        return null;
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        $model = CheckoutTransaction::find()->select('customers_id')->where(['access_code' => $token])->one();
        if ($model) {
            $user = new self;
            $user->id = $model->customers_id;
            return $user;
        }
        return null;
    }

    public function getAuthKey()
    {
        return Yii::$app->security->generateRandomString();
    }

    public function validateAuthKey($authKey)
    {
        return null;
    }
}