<?php
/* @var $this View */

/* @var $content string */

use micro\assets\AppAsset;
use micro\components\GoogleTagManager;
use yii\helpers\Html;
use yii\web\View;

AppAsset::register($this);

$this->registerMetaTag([
    'name' => 'robots',
    'content' => 'noindex,noarchive.'
]);

?>
<?php $this->beginPage() ?>
    <!DOCTYPE html>
    <html lang="<?= Yii::$app->language ?>" class="h-100">
    <head>
        <meta charset="<?= Yii::$app->charset ?>">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <?php $this->registerCsrfMetaTags() ?>
        <title><?= Html::encode(Yii::$app->name) ?></title>
        <?php (new GoogleTagManager())->webSDK(); ?>
        <?php $this->head() ?>
    </head>
    <body class="d-flex flex-column h-100">
    <?php $this->beginBody() ?>
    <?php (new GoogleTagManager())->bodySDK(); ?>

    <main role="main" class="flex-shrink-0">
        <div id="pgsHeader" class="pgsContainer"></div>
        <div id="pgsMain">
            <?php echo $content; ?>
        </div>
        <div class="clear"></div>

        <div id="pgsFooter" class="pgsContainer">
            <div id="footerDesc">
                <a href="http://corp.offgamers.com/">OffGamers Corporate</a> | <a
                    href="http://www.offgamers.com/terms-of-service#tos">Terms & Conditions</a> | <a
                    href="http://www.offgamers.com/terms-of-service#privacy">Privacy Policy</a> </br>
                &nbsp;&nbsp;&nbsp; <?php echo Yii::t('payment', 'FOOTER_COPYRIGHT_MESSAGE',
                    array("SYS_YEAR" => date("Y"))) ?>
            </div>
        </div>
    </main>
    <?php  echo Yii::t('payment', "FRESHDESK_CHAT"); ?>

    <?php $this->endBody() ?>
    </body>
    </html>
<?php $this->endPage(); ?>