<?php

use yii\web\View;

?>
    <div class="steps">
        <div class="steps__holder">
            <div class="steps__item ">
                <span class="steps__item-text"><?php echo Yii::t('payment', 'TEXT_PRECHECKOUT') ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line"></span>
            <div class="steps__item is-active">
                <span class="steps__item-text"><?php echo Yii::t('payment', 'TEXT_PAYMENT') ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line steps__line--last"></span>
            <div class="steps__item ">
                <span class="steps__item-text"><?php echo Yii::t('payment', 'TEXT_ORDER_SUCCESS') ?><span class="steps__item-point"></span></span>
            </div>
        </div>
    </div>


    <div class="container" id="main-container">
        <?php
        if (isset($_GET['pw_message']) && !empty($_GET['pw_message'])) {
            $error = strip_tags($_GET['pw_message']);
        }
        if (!empty($error)) {
            $errorMsg = (isset($error) && !empty($error) ? $error : '');
            echo '<div id="error-message">' . \yii\helpers\Html::encode($errorMsg) . '</br>' . Yii::t('payment', 'TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE') . '</div>';
        }
        ?>
        <div class="top-container row">
            <div class="cart-info">
                <table width="100%" class="cart-table" id="product-info-desktop">
                    <thead>
                    <tr>
                        <th style="font-weight: normal;" align="left"><?php echo Yii::t('payment', 'TABLE_HEADING_YOUR_ORDER') ?></th>
                        <th></th>
                        <th></th>
                    </tr>
                    </thead>
                    <?php
                    foreach ($data['products_info'] as $viewData) {
                        $strlen = strlen($viewData['quantity']);
                        $padding = '';
                        if ($strlen <= 1) {
                            $padding = 'padding-left:15px;';
                        } else {
                            if ($strlen == 2) {
                                $padding = 'padding-left:10px';
                            }
                        }
                        echo '	<tr>
                    <td width="85%">' . $viewData['name'] . '</td>
                    <td width="5%" style="' . $padding . '"><span class="order-basket__val">' . $viewData['quantity'] . '</span></td>
                    <td nowrap align="right" width="10%" class="right-most-td">' . Yii::$app->currency->format($data['currency_code'], $viewData['sub_total']) . '</td>
                </tr>';
                    }
                    ?>
                </table>
                <div class="wrapper">
                    <table width="100%" class="cart-table" id="product-info-mobile">
                        <thead>
                        <tr>
                            <th style="font-weight: normal;" align="left"><?php echo Yii::t('payment', 'TABLE_HEADING_YOUR_ORDER') ?></th>
                            <th></th>
                            <th></th>
                        </tr>
                        </thead>
                        <?php
                        foreach ($data['products_info'] as $viewData) {
                            echo '	<tr style="line-height:25px">
                        <td width="65%">' . $viewData['name'] . '</td>
                        <td width="5%"><span class="order-basket__val">' . $viewData['quantity'] . '</span></td>
                        <td nowrap align="right" width="30%" class="right-most-td">' . Yii::$app->currency->format($data['currency_code'], $viewData['sub_total']) . '</td>
                    </tr>
                    ';
                        }
                        ?>
                    </table>
                    <table width="100%" id="discount-info-mobile">
                        <?php
                        foreach ($data['additional_desc'] as $addData) {
                            $title = $addData['title'];
                            if (strtoupper($data['currency_code']) == 'IDR' && $addData['class'] == 'ot_gst') {
                                $title = preg_replace('/\s*\(\d+%\)/', '', $title);
                            }
                            echo '  <tr>
                                    <td width="70%" align="right">' . $title . '</td>
                                    <td nowrap align="right" width="30%" class="right-most-td" style="color:#a2a6be">' . $addData['text'] . '</td>
                                </tr>
                        ';


                        }
                        ?>
                    </table>
                </div>
                <table width="100%" id="discount-info">
                    <?php
                    foreach ($data['additional_desc'] as $addData) {
                        $title = $addData['title'];
                        if (strtoupper($data['currency_code']) == 'IDR' && $addData['class'] == 'ot_gst') {
                            $title = preg_replace('/\s*\(\d+%\)/', '', $title);
                        }
                        echo '  <tr>
                                        <td width="70%"></td>
                                        <td width="20%" align="right">' . ($title ?? '') . '</td>
                                        <td nowrap align="right" width="10%" class="right-most-td" style="color:#a2a6be">' . $addData['text'] . '</td>
                                    </tr>
                            ';

                    }
                    ?>
                </table>
                <table width="100%" id="final-info">
                    <?php
                    $amount = Yii::$app->currency->format($data['currency_code'], $data['amount'], ' ');
                    echo '  <tr>
                <td width="70%"></td>
                <td width="20%" align="right" style="font-size: 15px;font-weight:500">' . Yii::t('payment', 'TEXT_TOTAL_AMOUNT') . ':</td>
                <td nowrap align="right" width="10%" class="right-most-td" id="total-amount-column">' . $amount . '</td>
            </tr>';
                    $doc = new \DOMDocument();
                    foreach ($data['extra_total_info'] as $extraData) {
                        echo '  <tr>
                    <td width="70%"></td>
                    <td width="20%" align="right">' . $extraData['name'] . ':</td>
                    <td nowrap align="right" width="10%" class="right-most-td" id="wor-token">
                        ' . $extraData['value'] . '
                    </td>
                </tr>';
                    }
                    ?>
                </table>
                <table width="100%" id="final-info-mobile" class="toggle">
                    <?php
                    $amount = Yii::$app->currency->format($data['currency_code'], $data['amount'], ' ');
                    echo '  <tr>
                <td width="65%" align="right" style="font-weight:500">' . Yii::t('payment', 'TEXT_TOTAL_AMOUNT') . ':</td>
                <td nowrap align="right" width="25%" class="right-most-td" id="total-amount-column">' . $amount . '</td>
                <td nowrap align="left" width="5%"><a href="javascript:void(0);" class="arrow-down arrow" id="hide">&nbsp</a><a href="javascript:void(0);" class="arrow-up arrow" id="show" style="display:none">&nbsp</a></td>
            </tr>';
                    $doc = new DOMDocument();
                    foreach ($data['extra_total_info'] as $extraData) {
                        echo '  <tr>
                    <td width="65%" align="right">' . $extraData['name'] . ':</td>
                    <td nowrap align="right" width="25%" class="right-most-td" id="wor-token">
                        ' . $extraData['value'] . '
                    </td>
                    <td nowrap align="left" width="5%"</td>
                </tr>';
                    }
                    ?>
                </table>
            </div>
        </div>
    </div>


    <div class="row bottom-container">
        <div class="pg-area">
            <?php
            if (!isset($error)) {
                ?>
                <div id='pwloading' class='text-center'>
                    <div class="loader"></div>
                </div>
                <?php
            }
            ?>
            <div id='pwscript'></div>
            <div id='pwarea' class='row-fluid clearfix'></div>
            <script>
                var pwconfig = {
                    'api_key': "<?php echo $api_key; ?>",
                    'token': "<?php echo $token; ?>",
                };
                (function (_, p, w, s, d, k) {
                    var a = _.createElement('script')
                    a.setAttribute('src', w + d)
                    a.setAttribute('id', k)
                    setTimeout(function () {
                        var reqPwInit = (typeof reqPipwave != 'undefined')
                        if (reqPwInit) {
                            reqPipwave.require(['pw'], function (pw) {
                                pw.setOpt(pwconfig)
                                pw.startLoad()
                            })
                        } else {
                            _.getElementById(k).parentNode.replaceChild(a, _.getElementById(k))
                        }
                    }, 800)
                })(document, 'script', "<?php echo $sdk_url; ?>", 'pw.sdk.min.js', 'pw.sdk.min.js', 'pwscript')

            </script>
        </div>
    </div>

<?php

$this->registerJs(<<<JS
          $(document).ready(function(){
            $(function() {
              $('.toggle').click(function() {
                $('.wrapper').toggle();
                $('#hide').toggle();
                $('#show').toggle();
              });
            });
          })

JS
    , View::POS_END);