<?php

/* @var $this yii\web\View */
/* @var $name string */
/* @var $message string */

/* @var $exception Exception */

use yii\helpers\Html;

?>
<div class="container" id="main-container">
    <div class="site-error" style="text-align:center">

        <h1><?= Html::encode($this->title) ?></h1>

        <div class="alert alert-danger">
            <?php
            $error_msg = Yii::$app->getRequest()->get('pw_message', '');
            $output_msg = '';
            if ($error_msg) {
                $output_msg = strip_tags($error_msg);
            } elseif (Yii::$app->errorHandler->exception) {
                $output_msg = Yii::$app->errorHandler->exception->getMessage();
            }
            ?>
            <?= nl2br(Html::encode($output_msg)) ?>
        </div>

        <p>
            The above error occurred while the Web server was processing your request. <a></a>
        </p>

        <p>
            Please contact us if you think this is a server error. Thank you.
        </p>

        <?php
            if($order_id = Yii::$app->getRequest()->get('pipwaveTxn')){
                $url = Yii::$app->params['frontend']['baseUrl'] . '/account/purchase/order/' . (integer) $order_id;
                echo <<<BUTTON
<a href="$url" class="back-btn">Back to view order page</a>
BUTTON;

            }
        ?>

    </div>
</div>