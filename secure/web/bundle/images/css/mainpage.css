body {

    height: auto;

}

#pgsHeader {
    height: 95px;
    background-repeat: no-repeat;
    background-position: 10px 50%;
}

html, body {

    font-family: Arial, Tahoma, Verdana, Helvetica, sans-serif;

    background-color: #C4BEB0;

    color: #666666;

}

.main, .system_cust_pm_info {

    text-align: left;

    font-size: 11px;

}

table.main div {

    color: red !important;

}

.hide {

    display: none;

}

TD {

    font-family: Arial, Verdana, sans-serif;

}

a:link {

    color: #004B91;

    text-decoration: none;

}

body, td, div, span, p, font {

    font-family: Arial, Tahoma, Verdana, Helvetica, sans-serif;

    font-size: 11px;

}

.cart_item {

    border-bottom: 1px solid rgb(204, 204, 204);

}

.pgsContentClass {

    float: left;

    width: 473px;

    margin: 10px;

    font-size: 11px;

}

#pgsMain {

    margin: 0 auto;

    width: 1000px;

    background-color: #FFF;

    min-height: 600px;

    border-radius: 5px 5px 0 0;

}

#pgsSeperator {

    width: 20px;

    float: left;

}

#cartbox_div {

    position: relative;

    width: 470px;

    float: left;

    z-index: 99;

}


#pgsSurchargeDesc {

    padding-top: 10px;

}

#pgsFooter {

    min-height: 200px;

    background-color: #999999;

}

.pgsContainer {

    margin: 0 auto;

    width: 1000px;

}

#footerDesc {

    color: #E2E2E2;

    padding: 30px 0 0 30px;

}

#footerDesc > a {

    color: #FFFFFF;

    padding: 0 10px

}

#footerDesc > a:hover {

    text-decoration: underline;

}

#final_price {

    font-weight: bold;

    color: #333333;

    font-size: 14px;

}

#errorMessage {

    color: #FF0000;

    padding: 20px 0 0 20px;

}

li.mm-item a.mm-item-link span.arrow,
li.vm_header > a > span.arrow, li.vm_header_selected > a > span.arrow,
li.vm_header > a > span.arrow2, li.vm_header_selected > a > span.arrow2,
li.vm_header, li.vm_header_selected,
.ibrc_btm_lf, .ibrc_btm_rg, .ibrc_btm_ct,
.rc_top_lf, .rc_top_rg, .rc_btm_lf, .rc_btm_rg, .tabItem > .ct_selected,
.brc_top_lf, .brc_top_rg, .brc_btm_lf, .brc_btm_rg,
.lgrc_top_lf, .lgrc_top_rg, .lgrc_btm_lf, .lgrc_btm_rg,
.ylrc_top_lf, .ylrc_top_rg, .ylrc_btm_lf, .ylrc_btm_rg,
.grc_top_lf, .grc_top_rg, .grc_btm_lf, .grc_btm_rg,
.sdrc_top_lf, .sdrc_top_rg, .sdrc_btm_lf, .sdrc_btm_rg, .sdrc_top_ct,
.mm-item-content-table td.btm_lf, .mm-item-content-table td.btm_rg, .mm-item-content-table td.btm_ct,
.tabItem > .lf_selected, .tabItem > .rg_selected,
li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link,
li.mm-item:hover div.lf, li.mm-item-hover div.lf, li.mm-item:hover div.rg, li.mm-item-hover div.rg,
.tabTop, a.linkHolder > span.linkIcon, span.fbIcon, a.snsLink > span.tweetIcon, a.snsLink > span.youtubeIcon, a.snsLink > span.nlIcon, a.snsLink > span.forumIcon, span.cartIcon,
ul.ogmMenu li.partition {

    background-image: url("../box_and_tab.png");

}


.vspacing {
    clear: both;
    height: 20px;
}


.dotborder {
    border-top: 1px dotted #cecece;
}

.dotborderBtm {
    border-bottom: 1px dotted #cecece;
}


.dotborderLG {
    border-color: #CECECE;
    border-style: dotted;
}


.solborder, .solborderb, .solborderc, .solborderd,
.solborder2s, .solborder2bs,
.solborder3s, .solborder3bs {
    border-color: #CECECE;
    border-style: solid;
}

.solborder {
    border-width: 1px 0px 0px;
}

.solborderb {
    border-width: 0px 1px 0px;
}

.solborderc {
    border-width: 0px 0px 1px;
}

.solborderd {
    border-width: 0px 0px 0px 1px;
}

.solborder2s {
    border-width: 0px 1px;
}

.solborder2bs {
    border-width: 1px 0px;
}

.solborder3s {
    border-width: 0px 1px 1px;
}

.solborder3bs {
    border-width: 1px 1px 0px;
}


.solborderG, .solborderGb {
    border-color: #9D9D9D;
    border-style: solid;
}

.solborderGb {
    border-right-width: 1px;
}


.solborderLG, .solborderLGd {
    border-color: #E7E7E7;
    border-style: solid;
}

.solborderLGd {
    border-left-width: 1px;
}


.shw {
    display: block;
}

.hdn {
    display: none;
}

.clrFx {
    clear: both;
}


/* 	font setting

	{X} : text-transform

	{N} : N-style

 	hd{X}{N} : size 12px

	hd{s}{X}{N} : size 11px 

*/

span.hd1, span.hdC1, span.hdU1, span.hdL1,
span.hd2,
span.hd3,
span.hd4, span.hdsU4,
span.hd5, span.hds5,
span.hd6 {

    font-weight: bold;

    font-size: 12px;

    line-height: 21px;

}


span.hds1, span.hdsC1, span.hdsU1, span.hdsL1,
span.hds2,
span.hds3,
span.hds4, span.hdsU4,
span.hds5, span.hds5,
span.hds6 {

    font-weight: bold;

    font-size: 11px;

    line-height: 14px;

}


/* black */

span.hd1, span.hdC1, span.hdU1, span.hdL1,
span.hds1, span.hdsC1, span.hdsU1, span.hdsL1 {
    color: #000;
}

span.hdC1, span.hdsC1 {
    text-transform: capitalize;
}

span.hdU1, span.hdsU1 {
    text-transform: uppercase;
}

span.hdL1, span.hdsL1 {
    text-transform: lowercase;
}


/* White */

span.hd2,
span.hds2 {
    color: #FFF;
}


/* #545454 */

span.hd3,
span.hds3 {
    color: #545454;
}


span.hd5, span.hdC5, span.hdU5, span.hdL5,
span.hds5, span.hdsC5, span.hdsU5, span.hdsL5 {
    font-weight: normal;
    color: #545454;
}

span.hdC5, span.hdsC5 {
    text-transform: capitalize;
}

span.hdU5, span.hdsU5 {
    text-transform: uppercase;
}

span.hdL5, span.hdsL5 {
    text-transform: lowercase;
}


/* #999 */

span.hd4,
span.hds4, span.hdsU4 {
    color: #999;
    font-weight: normal;
}

span.hdsU4 {
    text-transform: uppercase;
}


/* content description */

span.cd1 {

    color: #545454;

    font-size: 12px;

}

span.op {

}

span.date {

}

span.cdd_price { /*custom dropdown*/

    font-size: 11px;

    font-weight: bold;

    line-height: 16px;

}


/* menu */

/* font setting */

li.mm-item a.mm-item-link, li.mm-item a.mm-item-link span {

    color: #FFF;

    font-size: 12px;

    font-weight: bold;

    line-height: 18px;

    text-decoration: none;

}

li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link,
li.mm-item:hover a.mm-item-link span, li.mm-item-hover a.mm-item-link span {

    color: #444;

}

/* font setting */


ul.ogmMenu {
    margin: 0px auto;
    padding: 1px 10px 0;
    border: 0px;
    list-style: none;
    display: none;
}

ul.ogmMenu li {
    margin: 0 2px;
    padding: 0px;
    float: left;
}

ul.ogmMenu li.partition {
    background-image: url("../head_line.gif");
    height: 10px;
    width: 2px;
    margin-top: 15px;
}

ul.ogmMenu li.clear-fix {
    float: none;
    clear: both;
    margin: 0px;
    padding: 0px;
    height: 0px;
    font-size: 0px;
    line-height: 0px;
    border: 0px;
}

ul.ogmMenu li.mm-item > a.dropdown {
    padding-right: 30px;
}


li.mm-item div.lf, li.mm-item div.rg,
li.mm-item:hover div.lf, li.mm-item:hover div.rg,
li.mm-item-hover div.lf, li.mm-item-hover div.rg {

    background-repeat: no-repeat;

    float: left;

    position: relative;

    height: 42px;

    width: 6px;

}

li.mm-item:hover div.lf, li.mm-item-hover div.lf {
    background-position: 0 -326px;
}

li.mm-item:hover div.rg, li.mm-item-hover div.rg {
    background-position: -12px -326px;
}


li.mm-item a.mm-item-link {
    float: left;
    padding: 12px 6px;
}

li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link {

    background-position: 0 -368px;

    background-repeat: repeat-x;

    /*    cursor: pointer;*/

    display: block;

    float: left;

    margin: 0;

    position: relative;

}


li.mm-item a.mm-item-link span.arrow {

    background-position: -25px -20px;

    background-repeat: no-repeat;

    height: 18px;

    margin: 0 6px 0 3px;

    position: absolute;

    width: 18px;

}

li.mm-item:hover a.mm-item-link span.arrow,
li.mm-item-hover a.mm-item-link span.arrow {

    background-position: -45px -20px;

}


ul.ogmMenu div.mm-item-content {

    padding: 0px;

    position: absolute;

}


.mm-content-base {

    padding-top: 5px;

    position: relative;

}

#sub-menu-1 {

    border-right: 1px solid #FFF;

    float: left;

    position: relative;

    right: 1px;

    width: 100%;

}

#sub-menu-2 {

    border-right: 1px solid #C7C7C7;

    clear: left;

    float: left;

    position: relative;

    right: 197px;

    width: 100%;

}

#sub-menu-3 {

    clear: left;

    float: left;

    overflow: hidden;

    width: 100%;

}

.mm-sub-menu-1, .mm-sub-menu-2 {

    float: left;

    left: 198px;

    overflow: hidden;

    position: relative;

    padding: 0 10px;

}

.mm-sub-menu-2 {

    left: 199px;

}

.mm-sub-menu-1 > div, .mm-sub-menu-2 > div {

    padding: 5px;

}


/* Simple/Expandable VMenu Round Corner Box */

.vmContent {
    background-color: #FFF;
    clear: both;
}

.vmContent > .header {
    padding: 6px 15px 10px;
}

.vmContent > .content {
    position: relative;
}

ul.vmenu, ul.vmenu2 {
    background-color: #888;
    border: 0px;
    margin: 0px auto;
    padding: 0px 0px;
    list-style: none;
}

li.vm_header {
    background-repeat: repeat-x;
    background-position: 0 -624px;
    cursor: pointer;
    position: relative;
}

li.vm_header:hover, li.vm_header_selected {
    background-repeat: repeat-x;
    background-position: 0 -741px;
}

li.vm_header > a {

    color: #545454;
    font-weight: bold;
    text-decoration: none;
    display: block;
    position: relative;
}

li.vm_header > a > span.lbl {
    padding: 9px 15px;
    display: inline-block;
}

li.vm_header:hover > a, li.vm_header_selected > a {
    color: #FFF;
}

li.vm_content {
    width: 100%;
    float: left;
    clear: both;
}

li.vm_header > a > span.arrow, li.vm_header > a > span.arrow2 {
    background-repeat: no-repeat;
    position: absolute;
    right: 0px;
    bottom: 0;
}

li.vm_header > a > span.arrow {
    background-position: -24px -19px;
    height: 20px;
    margin: 5px 15px;
    width: 20px;
}

li.vm_header_selected > a > span.arrow {
    background-position: -44px -19px;
}

li.vm_header > a > span.arrow2 {
    background-position: -24px 0;
    height: 8px;
    margin: 12px 15px;
    width: 14px;
}

li.vm_header > a:hover > span.arrow2 {
    background-position: -37px 0;
}

li.vm_header_selected > a > span.arrow2, li.vm_header_selected > a:hover > span.arrow2 {
    background-position: -50px 0;
}

li.vm_content {
    width: 100%;
    float: left;
    clear: both;
}

/* Simple/Expandable VMenu Round Corner Box */


/* Simple Inner White Round Corner Box */

.srcContent {
    background-color: #FFF;
}

.srcContent > .header {
    border-bottom: 1px solid #CECECE;
    padding: 6px 15px 10px;
}

/* Simple Inner White Round Corner Box */


.moduleRow {
    cursor: pointer;
}

/* Drop Down Listing */

ul.dd_listing {

    background-color: #FFF;

    border: 0px;

    margin: 0px auto;

    padding: 0px 0px;

    list-style: none;

}

ul.dd_listing > li {

    padding: 9px 5px 9px 25px;

    display: block;

    /*	cursor: pointer;*/

}

ul.dd_listing > li:hover {

    background-color: #E9E9E9;

}

ul.dd_listing > li > a {

    display: block;

    text-decoration: none;

    color: #545454;

}

ul.dd_listing2 > li {

    background-image: url("../pointer_blue_light.gif");

    background-repeat: no-repeat;

    background-position: 15px 8px;
}

/* Drop Down Listing */


/* Custom Drop Down Box */

.customdropdown dd, .customdropdown dt, .customdropdown ul,
.customdropdown2 dd, .customdropdown2 dt, .customdropdown2 ul {
    margin: 0px;
    padding: 0px;
}

.customdropdown dd,
.customdropdown2 dd {
    position: relative;
}

.customdropdown a, .customdropdown2 a {
    color: #545454;
    font-weight: normal;
    text-decoration: none;
    outline: none;
    vertical-align: top;
}

.customdropdown dt a,
.customdropdown2 dt a {
    background-color: #FFFFFF;
    display: block;
    padding-right: 20px;
    border: 1px solid #CCCCCC;
}

.customdropdown dt a span.arrow {
}

.customdropdown dd ul,
.customdropdown2 dd ul {
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC;
    color: #C5C0B0;
    display: none;
    left: 0px;
    padding: 0px;
    position: absolute;
    top: -1px;
    list-style: none;
}

.customdropdown span.value, .customdropdown span.ext,
.customdropdown2 span.value, .customdropdown2 span.ext {
    display: none;
}

.customdropdown dd ul li a,
.customdropdown2 dd ul li a {
    display: block;
    line-height: 16px;
    padding: 6px 5px;
}

.customdropdown dd ul li a:hover,
.customdropdown2 dd ul li a:hover {
    background-color: #e9e9e9;
}

.customdropdown > dt > a > span.sel,
.customdropdown2 > dt > a > span.sel {
    cursor: pointer;
    color: #999;
    display: inline-block;
    line-height: 16px;
    padding: 6px 5px;
}

/* IE need cursor */

.customdropdown > dt > a.selected > span.sel,
.customdropdown2 > dt > a.selected > span.sel {
    color: #545454;
}


/* Custom Drop Down Box [A] - only for width:270px */

.customdropdown {
    margin: 5px 0px 0px;
}

.customdropdown dd, .customdropdown dt, .customdropdown ul {
    width: 270px;
}

.customdropdown dt a {
    background: url("../icons/dropdown.gif") no-repeat scroll 97% center #FFFFFF;
    width: 100%;
}

/*257px center;width:250px;*/

.customdropdown dd ul {
    width: 270px;
}

.customdropdown > dt > a.selected {
    color: #545454
}

/* Custom Drop Down Box [A] */


/* Custom Drop Down Box [B] */

.customdropdown2 {
    margin: 0;
}

.customdropdown2 dt a {
    position: relative;
}

.customdropdown2 dt a span {
    width: 100%
}

.customdropdown2 > dt > a.selected {
    background-color: #e9e9e9;
}

.customdropdown2 > dt > a > span.arrow {

    background-image: url("../icons/dropdown.gif");

    background-position: 8px 12px;

    background-repeat: no-repeat;

    cursor: pointer;

    height: 4px;

    padding: 12px 8px;

    position: absolute;

    right: 0;

    top: 0;

    width: 7px;

}

.customdropdown2 > dt > a.selected > span.arrow {

    background-image: url("../icons/cancel-round.png");

    background-position: 4px 6px;

    height: 16px;

    padding: 6px 4px;

    width: 16px;

}

/* Custom Drop Down Box [B] */


/* [Round corner][Inner White][Code 1] */

.rc_top_lf, .rc_top_rg, .rc_btm_lf, .rc_btm_rg {
    background-repeat: no-repeat;
    height: 8px;
    width: 8px;
}

.rc_top_lf {
    background-position: -1px -1px;
    float: left;
}

.rc_top_rg {
    background-position: -9px -1px;
    float: right;
}

.rc_btm_lf {
    background-position: -1px -9px;
    float: left;
}

.rc_btm_rg {
    background-position: -9px -9px;
    float: right;
}

.rc_mdl {
    border-color: #CECECE;
    border-style: solid;
    border-width: 0 1px;
}

.rc_top_ct, .rc_btm_ct {
    background-color: #FFF;
    height: 7px;
    margin: 0 8px;
}

.rc_top_ct {
    border-top: 1px solid #CECECE;
}

.rc_btm_ct {
    border-bottom: 1px solid #CECECE;
}

/* [Round corner][Inner White][Code 1] */


/* [Round corner][Inner Black][Code 0] */

.brc_top_lf, .brc_top_rg, .brc_btm_lf, .brc_btm_rg {
    background-repeat: no-repeat;
    height: 8px;
    width: 8px;
}

.brc_top_lf {
    background-position: -1px -18px;
    float: left;
}

.brc_top_rg {
    background-position: -9px -18px;
    float: right;
}

.brc_btm_lf {
    background-position: -1px -26px;
    float: left;
}

.brc_btm_rg {
    background-position: -9px -26px;
    float: right;
}

.brc_mdl {
    border-color: #000;
    border-style: solid;
    border-width: 0 1px;
    background-color: #000;
}

.brc_top_ct, .brc_btm_ct {
    background-color: #000;
    height: 8px;
    margin-left: 8px;
    margin-right: 8px;
}

/* [Round corner][Inner Black][Code 0] */


/* [Round corner][Inner Light Gray][Code 2] */

.lgrc_top_lf, .lgrc_top_rg, .lgrc_btm_lf, .lgrc_btm_rg {
    background-repeat: no-repeat;
    height: 8px;
    width: 8px;
}

.lgrc_top_lf {
    background-position: -1px -35px;
    float: left;
}

.lgrc_top_rg {
    background-position: -9px -35px;
    float: right;
}

.lgrc_btm_lf {
    background-position: -1px -44px;
    float: left;
}

.lgrc_btm_rg {
    background-position: -9px -44px;
    float: right;
}

.lgrc_mdl {
    border-color: #e3e3e3;
    border-style: solid;
    border-width: 0 1px;
    background-color: #e3e3e3;
}

.lgrc_top_ct, .lgrc_btm_ct {
    background-color: #e3e3e3;
    height: 8px;
    margin-left: 8px;
    margin-right: 8px;
}

/* [Round corner][Inner Light Gray][Code 2] */


/* [Round corner][Inner Yellow][Code 3] */

.ylrc_top_lf, .ylrc_top_rg, .ylrc_btm_lf, .ylrc_btm_rg {
    background-repeat: no-repeat;
    height: 8px;
    width: 8px;
}

.ylrc_top_lf {
    background-position: -64px -1px;
    float: left;
}

.ylrc_top_rg {
    background-position: -72px -1px;
    float: right;
}

.ylrc_btm_lf {
    background-position: -64px -9px;
    float: left;
}

.ylrc_btm_rg {
    background-position: -72px -9px;
    float: right;
}

.ylrc_mdl {
    border-color: #ffffcc;
    border-style: solid;
    border-width: 0 1px;
    background-color: #ffffcc;
}

.ylrc_top_ct, .ylrc_btm_ct {
    background-color: #ffffcc;
    height: 8px;
    margin-left: 8px;
    margin-right: 8px;
}

/* [Round corner][Inner Yellow][Code 3] */


/* [Round corner][Inner Gray][Code 4] */

.grc_top_lf, .grc_top_rg, .grc_btm_lf, .grc_btm_rg {
    background-repeat: no-repeat;
    height: 4px;
    width: 4px;
}

.grc_top_lf {
    background-position: -64px -18px;
    float: left;
}

.grc_top_rg {
    background-position: -76px -18px;
    float: right;
}

.grc_btm_lf {
    background-position: -64px -30px;
    float: left;
}

.grc_btm_rg {
    background-position: -76px -30px;
    float: right;
}

.grc_mdl {
    border-color: #dedede;
    border-style: solid;
    border-width: 0 1px;
    background-color: #dedede;
}

.grc_top_ct, .grc_btm_ct {
    background-color: #dedede;
    height: 4px;
    margin: 0 4px;
}

/* [Round corner][Inner Gray][Code 4] */


/* [Round corner][Inner Gray][Code 5] - Default width: 450px */

.sdrc_top_lf, .sdrc_top_rg, .sdrc_btm_lf, .sdrc_btm_rg {

    background-repeat: no-repeat;

    height: 10px;

    width: 10px;

}

.sdrc_top_lf {
    background-position: -82px -4px;
    float: left;
}

.sdrc_top_rg {
    background-position: -96px -4px;
    float: right;
}

.sdrc_btm_lf {
    background-position: -64px -30px;
    float: left;
}

.sdrc_btm_rg {
    background-position: -76px -30px;
    float: right;
}

.sdrc_mdl {

    background-position: 425px 0;

    background-repeat: repeat-y;

    float: right;

}

.sdrc_mdl2 {

    background-position: -1px 0;

    background-repeat: repeat-y;

    clear: both;

}

.sdrc_top_ct {

    background-position: 0 -600px;

    background-repeat: repeat-x;

    height: 10px;

    margin: 0px 10px;

}

/* [Round corner][Inner Gray][Code 5] */


/* [Round corner][Image background] */

.ibrc_btm_lf, .ibrc_btm_rg {

    background-repeat: no-repeat;

    height: 20px;

    width: 6px;

}

.ibrc_btm_lf {
    background-position: -1px -79px;
    float: left;
}

.ibrc_btm_rg {
    background-position: -12px -79px;
    float: right;
}

.ibrc_btm_ct {

    background-repeat: repeat-x;

    background-position: 0 -101px;

    height: 20px;

    margin: 0 6px;

}

/* [Round corner][Image background] */


/* All Button Style Section */

.main_btn > a > span, .main_btn a,
.connect_with_fb_btn a {

    background: url("../buttons/button_main.png") no-repeat scroll 0 0 transparent;

    text-decoration: none;

    float: left;

}


.main_btn, .main_btn > a > span, .main_btn a {

    cursor: pointer;

    display: inline-block;

    margin: 0;

}

.main_btn a {

    position: relative;

    margin-right: 6px;

}

.main_btn > a > font {

    display: inline-block;

    font-size: 11px;

    font-weight: bold;

    padding: 6px 10px 0 30px;

    text-align: center;

}

.main_btn > a > span {

    position: absolute;

    right: -6px;

    top: 0;

    width: 6px;

}

.red_btn > a > font {

    color: #FFF !important;

}

/* button height */

.red_btn a, .red_btn span {

    height: 34px;

}

.gray_tall_btn a, .gray_tall_btn span {

    height: 38px;

}

.gray_short_btn a, .gray_short_btn > a > span {

    height: 30px;

}

.gray_short_btn > a > font, .gray_tall_btn > a > font {

    color: #000;

    padding: 6px 4px 6px 10px;

}

.gray_short_btn > a, .gray_tall_btn > a {

    text-align: center;

}

.gray_short_btn > a > span {
    background-position: -300px -436px;
}

.gray_short_btn a {
    background-position: 0 -408px;
}

.gray_short_btn a:hover {
    background-position: 0 -464px;
}

.gray_short_btn a:hover span {
    background-position: -300px -492px;
}

.red_btn > a > font {
    padding: 9px 10px;
    width: 100%;
}

.red_btn > a > span {
    background-position: -275px -310px;
    width: 29px !important;
    right: -29px !important;
}

.red_btn a {
    background-position: 0 -278px;
    margin-right: 29px;
!important
}

.red_btn a:hover {
    background-position: 0 -342px;
}

.red_btn a:hover span {
    background-position: -275px -374px;
}

.gray_tall_btn > a > font {
    padding-top: 10px;
}

.gray_tall_btn > a > span {
    background-position: -300px -556px;
}

.gray_tall_btn a {
    background-position: 0 -520px;
}

.gray_tall_btn a:hover {
    background-position: 0 -592px;
}

.gray_tall_btn a:hover span {
    background-position: -300px -628px;
}


/* All Table Header Section */

div.h1_tbl {

    background-color: #F4F4F4;

    min-height: 15px;

    padding: 5px 15px;

}


/* PG selection page pm offline info message box */


#warning_pm {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: black;
    opacity: 0.8;
    z-index: 9999;
}

#warning_pm .warning_area {
    padding: 40px 0px;
    width: 1000px;
    margin: 0px auto;
    color: white;
}

#warning_pm .warning_icon {
    float: left;
    font-size: 16px;
    margin-right: 10px;
    margin-top: -2px;
}

#warning_pm .warning_content {
    float: left;
    font-size: 16px;
}

#warning_pm .warning_close {
    float: right;
}

#warning_pm .warning_close a {
    font-size: 16px;
    color: white;
}

.clearfix {
    clear: both;
}

.site-error body, .site-error td, .site-error div, .site-error span, .site-error p, .site-error font {
    font-size: 18px;
}

.back-btn {
    padding: 14px 41px 14px;
    background-color: #3bb3ff;
    border-radius: 28px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: .7px;
    color: #fff !important;
    fill: #fff;
    border: 0;
    display: inline-block;
}