@font-face{
    font-family:'FuturaPTCond';
    src:url("../fonts/futura/FuturaPTCond-bold.eot?#iefix")
    format("embedded-opentype"),url("../fonts/futura/FuturaPTCond-bold.woff")
    format("woff"),url("../fonts/futura/FuturaPTCond-bold.ttf")
    format("truetype"),url("../fonts/futura/FuturaPTCond-bold.svg#FuturaPTCond-bold")
    format("svg");font-weight:700;font-style:normal
}
@font-face{font-family:'montserrat';
    src:url("../fonts/montserrat/500/subset-Montserrat-Medium.eot");
    src:url("../fonts/montserrat/500/subset-Montserrat-Medium.eot?#iefix")
    format("embedded-opentype"),url("../fonts/montserrat/500/subset-Montserrat-Medium.woff")
    format("woff"),url("../fonts/montserrat/500/subset-Montserrat-Medium.ttf")
    format("truetype"),url("../fonts/montserrat/500/subset-Montserrat-Medium.svg#Montserrat-Medium")
    format("svg");font-weight:500;font-style:normal
}
@font-face{
    font-family:'montserrat';
    src:url("../fonts/montserrat/400/subset-Montserrat-Regular.eot");
    src:url("../fonts/montserrat/400/subset-Montserrat-Regular.eot?#iefix")
    format("embedded-opentype"),url("../fonts/montserrat/400/subset-Montserrat-Regular.woff")
    format("woff"),url("../fonts/montserrat/400/subset-Montserrat-Regular.ttf")
    format("truetype"),url("../fonts/montserrat/400/subset-Montserrat-Regular.svg#Montserrat-Regular")
    format("svg");font-weight:normal;font-style:normal
}
html, body {
    font-family:'Helvetica Neue', Helvetica, sans-serif, Verdana !important;
    background-color: rgba(247,247,250,1);
    color: rgb(46, 49, 65);
    margin: 0;
}
@media screen and (min-width: 1200px) {
    #normal-size{
        display:inline-block;
    }
    #mobile-size{
        display:none;
    }
    #product-info-mobile, #discount-info-mobile, #final-info-mobile{
        display:none;
    }
    #product-info-desktop, #discount-info, #final-info {
        display:inline-table;
        padding-top: 10px;
    }
    #pgsHeader {
        padding-left:10px;
    }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
    #normal-size{
        display:inline-block;
    }
    #mobile-size{
        display:none;
    }
    #product-info-mobile, #discount-info-mobile, #final-info-mobile{
        display:none;
    }
    #product-info-desktop, #discount-info, #final-info {
        display:inline-table;
        padding-top: 10px;
    }
    #pgsHeader {
        padding-left:10px;
    }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
    #normal-size{
        display:inline-block;
    }
    #mobile-size{
        display:none;
    }
    #product-info-mobile, #discount-info-mobile, #final-info-mobile{
        display:none;
    }
    #product-info-desktop, #discount-info, #final-info {
        display:inline-table;
        padding-top: 10px;
    }
    #pgsHeader {
        padding-left:10px;
    }
}
@media screen and (min-width: 331px) and (max-width: 767px) {
    #normal-size{
        display:none;
    }
    #mobile-size{
        display:inline-block;
        text-align: center;
        width: 20%;
        margin-left: 10px;
    }
    .currency-option a {
        font-weight: normal;
    }
    #main-container {
        width:95%;
    }
    #currency-select {
        width: 85%;
    }
    #product-info-mobile, #discount-info-mobile, #final-info-mobile{
        display:inline-table;
    }
    #product-info-desktop, #discount-info, #final-info {
        display:none;
    }
    #pgsHeader {
        padding-left:40px;
    }
}
@media screen and (max-width: 330px) {
    #normal-size{
        display:none;
    }
    #mobile-size{
        display:inline-block;
        text-align: center;
        width: 20%;
    }
    .currency-option a {
        font-weight: normal;
    }
    #main-container {
        width:95%;
    }
    #currency-select {
        width: 83%;
    }
    #product-info-mobile, #discount-info-mobile, #final-info-mobile{
        display:inline-table;
    }
    #product-info-desktop, #discount-info, #final-info {
        display:none;
    }
    #pgsHeader {
        padding-left:30px;
    }
}
#pgsHeader {
    height: 95px;
    background-image: url("../logo_black.png?v=1683107745");
    background-size: 200px 50px;
    background-repeat: no-repeat;
    background-position: 0px 50%;
}
#pgsMain {
    background-color: rgba(247,247,250,1);
}
#pgsFooter {
    background-color: rgba(247,247,250,1);
    text-align: center;
}
#footerDesc{
    color: #666;
    padding: 30px 0 0 30px;
    font-size: 14px;
    line-height: 30px;
}
#footerDesc > a {
    color: #666;
    text-decoration:underline;
}

.currency-select {
    /*height: 50px;*/
    /*line-height: 50px;*/
    background: #7e2199;
    border-radius: 3px;
    box-shadow: 1px 2px 1px grey;
    padding: 10px;
    display: block;
    margin: 0 auto;
    text-align: left;
    color: white;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}
.top-container {
    width: 100%;
    margin-bottom: 15px;
}
.currency-option {
    font-size: 16px;
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
    padding: 15px;
}
.currency-option.active {
    border-radius: 3px;
    color: rgba(202,165,213,1);
    background: rgba(152,78,174,1);
}
.currency-option.active a {
    color: #FFF;
}
.currency-option a{
    color: rgba(202,165,213,1);
    font-weight: bold;
}
.cart-info {
    min-height: 60px;
    margin: 0 auto;
    padding-top: 20px;
    line-height: 25px;
}
table.cart-table tr, table.cart-table td, table.cart-table th{
    font-family:'Helvetica Neue', Helvetica, sans-serif, Verdana;
    font-size: 10px;
    padding: 5px;
}
table.cart-table {
    border-spacing:0;
}
table.cart-table tr {
    line-height: 30px;
}
table.cart-table th {
    border-bottom:1px solid #cccccc;
}
table.cart-table td {
    font-size: 13px;
    border-bottom:1px solid #cccccc;
}
table#discount-info td, table#final-info td, table#discount-info-mobile td, table#final-info-mobile td {
    font-size: 13px;
    font-family:'Helvetica Neue', Helvetica, sans-serif, Verdana;
}
#discount-info {
    padding-top:5px;
}
.partition-line {
    border-bottom:1px solid #cccccc;
    width: 99%;
    margin: 15px auto;
    padding-top: 5px;
}
.right-most-td {
    text-align: right;
}
#error-message {
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    color: #ff0000;
    min-height: 10px;
    margin-bottom: 10px;
    padding: 20px;
    font-size: 12px;
}
.container {
    width: 80%;
    max-width: 1040px;
    margin: 0 auto;
}
#pgsMain{
    width: 100%;
}
.pgsContainer {
    width: 80%;
    max-width: 1040px;
}
#pgsFooter {
    width: 100%;
    max-width: 1024px;
}
#footerDesc {
    padding: 0;
}
#wor-token {
    font-size: 13px !important;
}
#total-amount-column {
    font-weight: 500;
    color: #333;
    font-size: 15px !important;
}
.loader,
.loader:before,
.loader:after {
    background: #4A246C;
    -webkit-animation: load1 1s infinite ease-in-out;
    animation: load1 1s infinite ease-in-out;
    width: 1em;
    height: 4em;
    border-radius: 4px;
}
.loader {
    color: #4A246C;
    font-size: 8px;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
    margin: 0 auto;
}
.loader:before,
.loader:after {
    position: absolute;
    top: 0;
    content: '';
}
.loader:before {
    left: -1.5em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}
.loader:after {
    left: 1.5em;
}
@-webkit-keyframes load1 {
    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }
    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}
@keyframes load1 {
    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }
    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}
#pwloading {
    height: 60px;
    padding-top: 40px;
}
#note {
    position: relative;
    z-index: 101;
    top: 0;
    left: 0;
    right: 0;
    background: #631878;
    text-align: center;
    line-height: 1.5;
    padding: 10px;
    color: rgb(46, 49, 65);
    font-size: 13px;
    font-family:'Helvetica Neue', Helvetica, sans-serif, Verdana !important;
}
#note > a {
    color: #FFF;
    text-decoration:underline;
}
#note::after {
    content: '';
    position: absolute;
    top: 100%;
    left: calc(50% - 10px);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid #631878;
}
.steps {
    margin-bottom:50px;
    background:#f6f7f9;
    padding-bottom:38px;
    border-bottom:1px solid #eef0f4
}

@media (max-width: 1024px) {
    .steps {
        margin-bottom:45px
    }
}
@media (max-width: 739px) {
    .steps {
        padding:0px 30px 23px;
        margin-bottom:10px;
        position:relative
    }
    .steps:after {
        content:'';
        position:absolute;
        top:100%;
        left:0;
        margin-top:1px;
        width:100%;
        height:1px;
    }
}
@media (max-width: 360px) {
    .steps {
        padding:0px 23px 23px;
        margin-bottom:10px;
        position:relative
    }
}
.steps__title {
    font-size:28px;
    padding-bottom:32px;
    width: 80%;
    max-width: 1040px;
    margin: 0 auto;
}
@media (max-width: 739px) {
    .steps__title {
        padding-bottom:25px;
        font-size:22px;
        width: 100%;
        margin: 0;
    }
}
.steps__holder {
    max-width:570px;
    margin:0 auto;
    position:relative;
    z-index:0
}
@media (max-width: 1024px) {
    .steps__holder {
        max-width:426px;
    }
}
.steps__line {
    position:absolute;
    top:4px;
    left:15px;
    right:50%;
    height:3px;
    background:#d7e6f0;
    z-index:-1
}
.steps__line--last {
    left:50%;
    right:45px
}
.steps__item {
    display:inline-block;
    vertical-align:top;
    width:32.5%;
    text-align:center;
    padding-top:19px
}
.steps__item.is-active .steps__item-text {
    color:#2e3141
}
.steps__item.is-active ~ .steps__line {
    background:#e8eaef
}
.steps__item.is-active ~ .steps__item .steps__item-point {
    background:#e8eaef
}
.steps__item.is-active ~ .steps__item .steps__item-point:after {
    background:#bfc4ce
}
.steps__item:last-child {
    text-align:right
}
.steps__item:first-child {
    text-align:left
}
.steps__item-text {
    display:inline-block;
    position:relative;
    font-size:13px;
    color:#acb2bf
}
.steps__item-point {
    position:absolute;
    bottom:100%;
    left:50%;
    -webkit-transform:translateX(-50%);
    -ms-transform:translateX(-50%);
    transform:translateX(-50%);
    width:16px;
    height:16px;
    border-radius:50%;
    background:#d7e6f0;
    margin-bottom:5px
}
.steps__item-point:after {
    content:'';
    position:absolute;
    top:50%;
    left:50%;
    -webkit-transform:translate(-50%,
    -50%);
    -ms-transform:translate(-50%,
    -50%);
    transform:translate(-50%,
    -50%);
    width:8px;
    height:8px;
    border-radius:50%;
    background:#3bb3ff
}
.order-basket__val {
    display:block;
    width:20px;
    height:20px;
    background:#fff;
    border:1px solid #e6e9ee;
    border-radius:50px;
    text-align:center;
    line-height:19px;
    font-size:10px;
    color:#2e3141
}
.arrow-up {
    background-image: url("../tri-up.png");
    background-repeat: no-repeat;
    background-position: 0px 50%;
    display: block;
    cursor: pointer;
}
.arrow-down {
    background-image: url("../tri-down.png");
    background-repeat: no-repeat;
    background-position: 0px 50%;
    display: block;
    cursor: pointer;
}
#show
{
    display:block;
}
#hide
{
    display:block;
}
#hide:target {
    display:none;
}
.toggle {
    cursor: pointer;
}
.wrapper {
    display: none;
}
.bottom-container {
    border-top:1px solid #eef0f4;
    background: #fff;
    border-bottom:1px solid #eef0f4;
}
.pg-area {
    width: 80%;
    max-width: 1040px;
    margin: 0 auto;
}
h1, h2, h3, h4, h5, h6 {
    font-family: "FuturaPTCond", Verdana;
    margin: 0;
    padding: 0;
    padding-bottom: 0px;
    color: #55575f;
    text-transform: uppercase;
}