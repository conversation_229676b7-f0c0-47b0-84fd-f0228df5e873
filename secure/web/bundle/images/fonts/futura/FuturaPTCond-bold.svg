<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Tue Aug 22 12:30:21 2017
 By deploy user
Copyright &#194;&#169; 1995 ParaGraph Intl., &#194;&#169; 1998 ParaType Inc., ParaType Ltd. All rights reserved.
</metadata>
<defs>
<font id="FuturaPTCond-Bold" horiz-adv-x="500" >
  <font-face 
    font-family="Futura PT Cond Bold"
    font-weight="700"
    font-stretch="condensed"
    units-per-em="1000"
    panose-1="2 11 8 6 2 2 4 2 2 3"
    ascent="763"
    descent="-237"
    x-height="447"
    cap-height="667"
    bbox="-279 -250 1020 911"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="233" 
 />
    <glyph glyph-name="afii10017.var.accented" unicode="&#x410;`" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM358 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10022.var.accented" unicode="&#x415;`" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM305 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10026.var.accented" unicode="&#x418;`" horiz-adv-x="487" 
d="M47 0v667h130v-290q0 -59 -11 -120l152 410h122v-667h-130v292q0 60 9 123l-150 -415h-122zM371 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10032.var.accented" unicode="&#x41e;`" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM271 556q-27 0 -48 -22q-46 -46 -46 -198t46 -198q21 -22 48 -22t48 22q46 46 46 198t-46 198q-21 22 -48 22zM399 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10037.var.accented" unicode="&#x423;`" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM356 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10045.var.accented" unicode="&#x42b;`" horiz-adv-x="631" 
d="M579 0h-132v667h132v-667zM52 667h132v-261h4q107 0 163 -55q57 -55 57 -156q0 -94 -50 -143q-53 -52 -162 -52h-144v667zM184 292v-178h4q51 0 72 20q22 22 22 66q0 45 -25 69q-25 23 -70 23h-3zM443 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10047.var.accented" unicode="&#x42d;`" horiz-adv-x="421" 
d="M45 14v138q36 -32 82 -32q42 0 71 29q20 20 33 58t13 74h-159v118h155q-1 31 -12 66.5t-30 54.5q-26 28 -71 28q-46 0 -82 -29v136q45 19 100 19q94 0 153 -59q86 -85 86 -279q0 -196 -86 -283q-60 -60 -159 -60q-56 0 -94 21zM292 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10048.var.accented" unicode="&#x42e;`" horiz-adv-x="751" 
d="M714 334q0 -195 -82 -281q-61 -64 -150 -64q-94 0 -153 59q-72 74 -76 233h-69v-281h-132v667h132v-261h71q5 140 78 212q59 59 147 59q91 0 152 -66q50 -53 66 -121.5t16 -155.5zM480 556q-27 0 -48 -22q-46 -46 -46 -198t46 -198q21 -22 48 -22t48 22q46 46 46 198
t-46 198q-21 22 -48 22zM503 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10049.var.accented" unicode="&#x42f;`" horiz-adv-x="465" 
d="M33 0l121 284q-41 12 -68 41q-44 46 -44 129q0 105 55 160q53 53 173 53h143v-667h-132v276l-106 -276h-142zM281 559h-25q-39 0 -59 -21q-23 -25 -23 -73q0 -47 25 -70q23 -21 68 -21h14v185zM360 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10023.var.accented" unicode="&#x401;`" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM262 858l-107 -122l-39 24l77 128zM119 806q0 -21 -15 -36t-35 -15q-22 0 -37 15t-15 36q0 20 15 35t37 15q20 0 35 -15t15 -35zM352 806q0 -21 -15 -36t-37 -15q-20 0 -35 15t-15 36q0 20 15 35t35 15
q22 0 37 -15t15 -35z" />
    <glyph glyph-name="afii10065.var.accented" unicode="&#x430;`" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM326 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10070.var.accented" unicode="&#x435;`" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM314 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10074.var.accented" unicode="&#x438;`" horiz-adv-x="405" 
d="M40 447h118v-159q0 -52 -9 -117q13 47 32 94l73 182h111v-447h-118v166q0 44 8 114q-14 -59 -35 -111l-69 -169h-111v447zM330 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10080.var.accented" unicode="&#x43e;`" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM196 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107q-9 13 -23 13zM324 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10085.var.accented" unicode="&#x443;`" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM311 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10093.var.accented" unicode="&#x44b;`" horiz-adv-x="547" 
d="M507 0h-125v447h125v-447zM40 447h125v-164h28q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-158v447zM165 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107zM401 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10095.var.accented" unicode="&#x44d;`" horiz-adv-x="313" 
d="M30 12v118q24 -27 63 -27q33 0 51 18q21 21 23 61h-102v92h102q-2 32 -20 53t-53 21q-36 0 -64 -30v115q39 23 88 23q70 0 114 -42q61 -59 61 -187q0 -135 -61 -196q-40 -40 -111 -40q-53 0 -91 21zM258 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10096.var.accented" unicode="&#x44e;`" horiz-adv-x="572" 
d="M545 223q0 -134 -61 -193q-41 -39 -111 -39q-66 0 -105 37q-54 50 -56 148h-47v-176h-125v447h125v-167h48q4 88 54 136q40 39 106 39q70 0 111 -39q61 -59 61 -193zM376 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107
q-9 13 -23 13zM414 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10097.var.accented" unicode="&#x44f;`" horiz-adv-x="386" 
d="M20 0l85 167q-34 12 -55 40q-23 31 -23 82q0 75 43 116q44 42 142 42h134v-447h-125v169l-71 -169h-130zM221 238v115h-4q-32 0 -48 -14q-18 -16 -18 -47q0 -29 17 -42q15 -12 50 -12h3zM321 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10071.var.accented" unicode="&#x451;`" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-7 -77 17 -104q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q2 20 -4 42t-16 31t-23 9q-16 0 -28 -12
q-21 -23 -19 -70zM257 641l-108 -118l-33 30l85 128zM26 601q0 20 14 34t34 14t34 -14t14 -34t-14 -34t-34 -14t-34 14t-14 34zM251 601q0 20 14 34t34 14t34 -14t14 -34t-14 -34t-34 -14t-34 14t-14 34z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="448" 
d="M236 343h-57v-343h-125v343h-46v104h46v151q0 54 13 81q15 30 47 45t68 15q24 0 54 -10v-120q-12 7 -22 7q-35 0 -35 -55v-114h57v-104zM425 593q0 -33 -23 -56t-56 -23t-56 23t-23 56t23 56t56 23t56 -23t23 -56zM408 0h-125v447h125v-447z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="447" 
d="M236 343h-57v-343h-125v343h-46v104h46v151q0 54 13 81q15 30 47 45t68 15q24 0 54 -10v-120q-12 7 -22 7q-35 0 -35 -55v-114h57v-104zM406 0h-125v729h125v-729z" />
    <glyph glyph-name=".notdef" horiz-adv-x="233" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="233" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="288" 
d="M210 200h-131v467h131v-467zM228 73q0 -35 -24.5 -59.5t-59.5 -24.5q-34 0 -59 24.5t-25 59.5t25 59.5t59 24.5q35 0 59.5 -24.5t24.5 -59.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="405" 
d="M364 667l-24 -268h-92l-23 268h139zM180 667l-23 -268h-92l-23 268h138z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="600" 
d="M603 496l-17 -112h-100l-14 -88h102l-17 -112h-103l-29 -184h-128l29 184h-88l-30 -184h-127l30 184h-103l18 112h102l14 88h-102l18 112h101l27 171h127l-27 -171h89l27 171h128l-27 -171h100zM357 384h-88l-14 -88h89z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="433" 
d="M398 599l-73 -79q-47 50 -101 50q-34 0 -54.5 -17.5t-20.5 -51.5t17 -51t52 -32l46 -20t41 -19.5t40 -24l30.5 -27.5t27.5 -35t14.5 -41.5t6.5 -52.5q0 -77 -45 -135.5t-118 -73.5v-80h-72v80q-53 9 -90.5 26t-74.5 52l62 98q70 -66 130 -66q38 0 61 23t23 61
q0 36 -22.5 55.5t-72.5 42.5q-47 22 -70 35t-54 38.5t-44.5 58.5t-13.5 78q0 78 44.5 125t121.5 61v74h72v-74q45 -9 76.5 -26t60.5 -52z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="731" 
d="M556 667l-289 -667h-87l291 667h85zM303 504q0 -44 -10.5 -80.5t-42.5 -65.5t-82 -29q-38 0 -65.5 16t-42 43t-21 55.5t-6.5 58.5q0 40 10.5 75.5t42.5 64.5t82 29q38 0 66 -16t42 -42.5t20.5 -53.5t6.5 -55zM202 505q0 76 -32 76q-31 0 -31 -76q0 -85 31 -85q32 0 32 85
zM597 169q0 76 -32 76q-31 0 -31 -76q0 -85 31 -85q32 0 32 85zM698 168q0 -44 -10.5 -80.5t-42.5 -65.5t-82 -29q-38 0 -65.5 16t-42 43t-21 55.5t-6.5 58.5q0 40 10.5 75.5t42.5 64.5t82 29q38 0 66 -16t42 -42.5t20.5 -53.5t6.5 -55z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="538" 
d="M449 276l69 -100q-34 -41 -73 -70l70 -106h-140l-26 39q-54 -48 -124 -48q-75 0 -126 54q-56 59 -56 142q0 107 96 187q-20 32 -31 64q-16 46 -16 90q0 67 42 106q45 43 114 43q71 0 115 -42q38 -37 38 -94q0 -63 -33 -114q-30 -46 -77 -77l88 -144q37 29 70 70zM235 449
q23 17 34 37q12 21 12 46q0 24 -11 34q-9 9 -23 9q-20 0 -29 -11q-11 -11 -11 -35q0 -41 28 -80zM287 134l-84 140q-32 -31 -32 -73q0 -40 27 -65q19 -19 47 -19q22 0 42 17z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="222" 
d="M180 667l-23 -268h-92l-23 268h138z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="307" 
d="M274 -171l-123 -30q-98 222 -98 465q0 258 98 473l123 -27q-89 -222 -89 -446q0 -215 89 -435z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="307" 
d="M156 -201l-123 30q89 220 89 435q0 224 -89 446l123 27q98 -215 98 -473q0 -243 -98 -465z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="435" 
d="M374 595l-108 -31l68 -91l-50 -36l-63 91l-65 -92l-51 37l69 90l-108 32l19 60l106 -37l-3 111h64l-4 -111l107 37z" />
    <glyph glyph-name="plus" unicode="+" 
d="M467 282h-154v-155h-125v155h-155v115h155v155h125v-155h154v-115z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="250" 
d="M228 121l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="268" 
d="M218 191h-168v108h168v-108z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="250" 
d="M209 73q0 -35 -24.5 -60t-59.5 -25q-34 0 -59 25t-25 60t24.5 59.5t59.5 24.5t59.5 -24.5t24.5 -59.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="472" 
d="M449 729l-367 -922h-112l370 922h109z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="433" 
d="M404 336q0 -55 -7 -107t-25.5 -110.5t-58.5 -94t-98 -35.5q-46 0 -81 25.5t-54 62.5t-31 87t-15.5 91t-3.5 83q0 54 7.5 105.5t26.5 108t59 91t97 34.5q55 0 94 -35.5t57 -92.5t25.5 -108.5t7.5 -104.5zM276 300v60q0 199 -60 199q-59 0 -59 -194v-70q0 -23 1.5 -46.5
t6.5 -58.5t18.5 -57t32.5 -22q60 0 60 189z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="433" 
d="M295 0h-128v551h-64v116h192v-667z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="433" 
d="M167 435h-121q-2 24 -2 36q0 101 38.5 153.5t136.5 52.5q84 0 131.5 -43t47.5 -127q0 -13 -1 -25.5t-4 -25.5t-5 -22.5t-7.5 -23.5t-8 -21t-10.5 -22.5l-10.5 -20.5l-12 -23l-11.5 -22l-97 -186h155v-115h-350l155 285q81 149 81 216q0 33 -11.5 58.5t-40.5 25.5
q-54 0 -54 -109q0 -27 1 -41z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="433" 
d="M58 479v7q0 72 18 113q35 78 139 78q79 0 123.5 -46.5t44.5 -126.5q0 -122 -83 -159q103 -33 103 -160q0 -91 -52.5 -143.5t-143.5 -52.5q-86 0 -131.5 49.5t-45.5 136.5h124q0 -3 -0.5 -9.5t-0.5 -9.5q0 -26 13 -44.5t38 -18.5q56 0 56 104q0 58 -20.5 77t-79.5 19v112
q56 0 79 17t23 71q0 75 -46 75q-44 0 -44 -89h-114z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="433" 
d="M404 157h-54v-157h-128v157h-193v103l177 407h144v-396h54v-114zM222 271v244l-98 -244h98z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="433" 
d="M209 553l-18 -109q61 -6 94 -21q106 -48 106 -199q0 -110 -56 -172.5t-164 -62.5q-61 0 -128 31l22 115q40 -32 82 -32q50 0 76.5 35.5t26.5 86.5q0 60 -36 89t-97 29q-24 0 -52 -5l52 329h255v-114h-163z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="433" 
d="M308 667l-126 -263q39 37 83 37q37 0 65 -20t42.5 -52.5t21.5 -66.5t7 -68q0 -46 -10 -87.5t-30.5 -78t-57 -58t-85.5 -21.5q-95 0 -140.5 67.5t-45.5 167.5q0 39 7.5 81.5t15 71t28.5 78.5l29 68t36.5 76l33.5 68h126zM272 220q0 15 -2 30.5t-7 33t-16.5 29t-26.5 11.5
q-22 0 -36 -22t-18 -45.5t-4 -46.5q0 -20 4 -42t17 -43.5t32 -21.5q57 0 57 117z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="433" 
d="M410 667l-250 -667h-137l207 552h-179v115h359z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="433" 
d="M408 184q0 -84 -54 -139.5t-137 -55.5t-137 55.5t-54 139.5q0 80 38 126q22 27 77 49q-91 39 -91 142q0 74 46 125t120 51t120 -50.5t46 -125.5q0 -104 -90 -142q54 -22 77 -49q39 -46 39 -126zM252 502q0 71 -38 71t-38 -70q0 -76 38 -76t38 75zM272 199
q0 33 -13.5 64.5t-42.5 31.5q-21 0 -34.5 -17.5t-17.5 -37.5t-4 -41t4 -41t17.5 -37.5t34.5 -17.5q29 0 42.5 31.5t13.5 64.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="433" 
d="M124 0l127 262q-42 -37 -83 -37q-37 0 -65 20t-42.5 53t-21.5 67t-7 68q0 46 10 87.5t30.5 77.5t57.5 57.5t86 21.5q95 0 140 -67t45 -167q0 -130 -71 -279l-10 -20l-10 -20l-59 -124h-127zM270 457q0 14 -2.5 30t-7.5 34t-16 30t-26 12q-56 0 -56 -116q0 -22 3 -43
t15.5 -41.5t33.5 -20.5q30 0 43 38.5t13 76.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="250" 
d="M209 371q0 -35 -24.5 -59.5t-59.5 -24.5t-59.5 24.5t-24.5 59.5t24.5 59.5t59.5 24.5t59.5 -24.5t24.5 -59.5zM209 76q0 -35 -24.5 -60t-59.5 -25q-34 0 -59 25t-25 60q0 34 24.5 59t59.5 25t59.5 -25t24.5 -59z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="250" 
d="M229 374q0 -35 -25 -60t-60 -25q-34 0 -59 25t-25 60q0 34 25 59t59 25q35 0 60 -25t25 -59zM228 121l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M467 62l-434 219v105l434 219v-123l-302 -149l301 -148z" />
    <glyph glyph-name="equal" unicode="=" 
d="M467 371h-434v109h434v-109zM467 200h-434v109h434v-109z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M467 281l-434 -219v123l302 149l-301 148l-1 123l434 -219v-105z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="415" 
d="M154 482h-124q0 57 10 88q16 51 59.5 78.5t99.5 27.5q85 0 135.5 -55.5t50.5 -141.5q0 -73 -33 -127q-25 -41 -75 -58v-86h-130v164q14 -2 20 -2q40 0 58 31t18 74q0 19 -3 36.5t-14.5 33.5t-29.5 16q-45 0 -42 -79zM296 73q0 -35 -25 -59.5t-59 -24.5q-35 0 -60 24.5
t-25 59.5q0 34 25 59t60 25q34 0 59 -24.5t25 -59.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="733" 
d="M572 97l20 -78q-89 -31 -205 -31q-77 0 -137 21q-98 34 -157.5 123.5t-59.5 195.5q0 147 102 248.5t249 101.5q132 0 224 -82t92 -212q0 -50 -17 -99q-20 -53 -58.5 -92.5t-88.5 -39.5q-27 0 -49 14.5t-29 40.5q-21 -30 -54.5 -46.5t-69.5 -16.5q-62 0 -101.5 47
t-39.5 110q0 76 50.5 138t124.5 62q66 0 98 -50l8 40l86 -1l-37 -191q-7 -33 -7 -43q0 -34 24 -34q11 0 25 10q29 22 44 65.5t15 85.5q0 99 -67.5 158t-168.5 59q-115 0 -193 -79.5t-78 -194.5t74 -191q70 -71 196 -71q95 0 185 32zM444 345q0 28 -18 48t-46 20
q-38 0 -63.5 -33.5t-25.5 -73.5q0 -31 18 -52.5t48 -21.5q40 0 63.5 35.5t23.5 77.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="435" 
d="M52 667h141q92 0 140 -47q55 -53 55 -144q0 -58 -31 -95q-23 -27 -53 -39q46 -17 72 -59q23 -39 23 -94q0 -82 -50 -132q-57 -57 -157 -57h-140v667zM184 558v-165q37 2 58 23t21 62q0 39 -21 60q-20 20 -58 20zM184 291v-177q38 0 60 21q25 23 25 65q0 46 -26 70
q-24 21 -59 21z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="488" 
d="M52 667h111q86 0 135 -23q56 -25 92 -79.5t48.5 -111.5t12.5 -123q0 -71 -14 -128t-44.5 -104t-84.5 -72.5t-127 -25.5h-129v667zM184 122h4q37 0 64.5 20t41 53.5t19.5 67t6 68.5q0 140 -68 196q-25 21 -67 23v-428z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="348" 
d="M318 544h-134v-142h121v-127h-121v-275h-132v667h266v-123z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="501" 
d="M449 0h-132v295h-133v-295h-132v667h132v-247h133v247h132v-667z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="310" 
d="M258 667v-469q0 -68 -14 -109q-16 -47 -56 -73.5t-90 -26.5q-59 0 -98 21v132q30 -37 70 -37q20 0 32.5 8.5t17 27t5.5 30t1 34.5v462h132z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="452" 
d="M445 0h-138l-123 339v-339h-132v667h132v-298l111 298h135l-123 -308z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="329" 
d="M322 0h-270v667h132v-542h138v-125z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="646" 
d="M624 0h-135l-33 480q-3 -60 -20 -154l-59 -326h-101l-62 302q-14 66 -17 100l-8 78l-21 -480h-141l58 667h174l65 -394l53 394h178z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="517" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="435" 
d="M52 667h160q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -102 -64 -152.5t-167 -42.5v-274h-132v667zM184 385h8q47 0 69.5 19.5t22.5 65.5q0 90 -77 90h-23v-175z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="544" 
d="M431 -57l-54 75q-38 -29 -93 -29q-97 0 -162 66q-85 86 -85 281q0 193 85 278q61 63 148 63t151 -66q85 -87 85 -284q0 -148 -61 -241l63 -88zM319 256l36 -54q12 56 12 131q0 146 -48 201q-21 23 -46 23q-26 0 -45 -19q-31 -30 -40.5 -79.5t-9.5 -118.5q0 -151 46 -202
q20 -22 49 -22q16 0 28 9l-55 77z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="473" 
d="M440 0h-139l-117 307v-307h-132v667h143q97 0 145 -32q38 -25 58.5 -69t20.5 -93q0 -112 -95 -170zM184 563v-183q7 -1 22 -1q41 0 63 25.5t22 67.5q0 91 -82 91h-25z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="338" 
d="M338 545h-103v-545h-132v545h-103v122h338v-122z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="486" 
d="M486 667l-185 -667h-108l-193 667h142l71 -258q21 -78 33 -181q9 112 29 181l73 258h138z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="694" 
d="M691 667l-138 -667h-115l-65 277q-17 70 -23 197q-9 -110 -34 -212l-63 -262h-115l-135 667h135l54 -293q17 -94 17 -168q10 87 25 161l62 300h104l68 -335q14 -73 19 -126q0 98 18 194l48 267h138z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="443" 
d="M443 0h-153q-6 16 -20.5 49.5l-22.5 53t-17 49t-13 54.5q-12 -57 -69 -206h-148l141 344l-141 323h148q4 -10 19 -44.5t22.5 -54t16.5 -48t11 -49.5q5 24 14.5 52.5t16.5 46t22 51.5l20 46h142l-142 -323z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="447" 
d="M447 667l-155 -382v-285h-132v285l-160 382h145l49 -145q18 -54 30 -99q9 37 29 100l46 144h148z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="456" 
d="M438 667l-228 -542h209v-125h-401l222 541h-194v126h392z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="367" 
d="M333 -193h-266v922h265v-123h-133v-677h134v-122z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="472" 
d="M23 729h109l370 -922h-112z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="366" 
d="M33 -193v122h134v677h-133v123h265v-922h-266z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M494 334h-131l-113 234l-113 -234h-131l162 333h164z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M500 -148h-500v100h500v-100z" />
    <glyph glyph-name="grave" unicode="`" 
d="M298 553l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="396" 
d="M165 729v-337q29 63 84 63q37 0 62.5 -24t37 -63t16 -73t4.5 -70q0 -34 -5 -69.5t-16.5 -75t-37.5 -64.5t-62 -25q-50 0 -83 60v-51h-125v729h125zM243 223q0 120 -45 120q-42 0 -42 -120t43 -120q14 0 23.5 14t13.5 36t5.5 38.5t1.5 31.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v337h125v-729zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="241" 
d="M236 343h-57v-343h-125v343h-46v104h46v151q0 54 13 81q15 30 47 45t68 15q24 0 54 -10v-120q-12 7 -22 7q-35 0 -35 -55v-114h57v-104z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v729h125v-352q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="206" 
d="M184 593q0 -33 -23 -56t-56 -23t-56 23t-23 56t23 56t56 23t56 -23t23 -56zM167 0h-125v447h125v-447z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="206" 
d="M184 593q0 -33 -23 -56t-56 -23t-56 23t-23 56t23 56t56 23t56 -23t23 -56zM167 -206h-125v653h125v-653z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="389" 
d="M392 0h-132l-95 231v-231h-125v729h125v-454l74 172h130l-98 -187z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="205" 
d="M165 0h-125v729h125v-729z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="582" 
d="M543 0h-125v268q0 63 -32 63q-8 0 -13.5 -2.5t-9 -9t-5.5 -11t-3 -14.5t-1 -14v-15v-12v-253h-125v277q0 52 -29 52q-35 0 -35 -68v-261h-125v447h125v-72q47 80 111 80q60 0 78 -76q17 32 45 54t61 22q83 0 83 -123v-332z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v447h125v-70q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="396" 
d="M165 447v-55q29 63 84 63q37 0 62.5 -24t37 -63t16 -73t4.5 -70q0 -34 -5 -69.5t-16.5 -75t-37.5 -64.5t-62 -25q-50 0 -83 60v-257h-125v653h125zM243 223q0 120 -45 120q-42 0 -42 -120t43 -120q14 0 23.5 14t13.5 36t5.5 38.5t1.5 31.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="396" 
d="M356 -206h-125v257q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-653zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="278" 
d="M268 463v-121q-12 2 -18 2q-52 0 -68.5 -26t-16.5 -81v-237h-125v447h125v-55q23 33 45 49t58 22z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="230" 
d="M228 343h-51v-343h-125v343h-43v104h43v127h125v-127h51v-104z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="366" 
d="M370 447l-130 -447h-115l-128 447h123q52 -208 61 -253q2 11 7 30.5t6 24.5l46 198h130z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="580" 
d="M583 447l-123 -447h-103l-18.5 67l-20.5 74.5t-15.5 65.5t-13.5 73q-10 -78 -62 -280h-104l-126 447h121q50 -198 61 -263l59 263h107q47 -207 57 -263q12 61 63 263h118z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="385" 
d="M389 0h-132l-41 78q-16 30 -24 57q-13 -36 -26 -60l-41 -75h-128l128 240l-111 207h129l24 -47q15 -28 25 -65q7 30 27 68l23 44h129l-114 -207z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="341" 
d="M327 447l-138 -344h124v-103h-300l142 343h-127v104h299z" />
    <glyph glyph-name="braceleft" unicode="{" 
d="M401 -80v-110q-72 -3 -73 -3q-80 0 -115.5 32.5t-35.5 112.5v180q0 36 -11.5 58t-43.5 28v106q55 12 55 77v184q0 83 37.5 113.5t122.5 30.5q11 0 32 -1t32 -1v-110h-10q-60 0 -75 -13.5t-15 -77.5q0 -20 0.5 -54.5t0.5 -56.5q0 -67 -12 -98q-10 -25 -49 -51
q37 -23 48.5 -50.5t11.5 -75.5q0 -16 -0.5 -56t-0.5 -68t1.5 -42t7.5 -29t20.5 -21t38.5 -6q11 0 33 2z" />
    <glyph glyph-name="bar" unicode="|" 
d="M312 -250h-125v1000h125v-1000z" />
    <glyph glyph-name="braceright" unicode="}" 
d="M121 -190v110q22 -2 33 -2q25 0 39.5 6t20.5 22t7 29.5t1 43.5q0 23 -0.5 61t-0.5 60q0 48 11.5 75.5t48.5 50.5q-39 26 -49 51q-12 31 -12 98q0 22 0.5 56.5t0.5 54.5q0 64 -15 77.5t-75 13.5h-10v110q11 0 32.5 1t31.5 1q85 0 122.5 -30.5t37.5 -113.5v-184
q0 -65 55 -77v-106q-32 -6 -43.5 -28t-11.5 -58v-180q0 -80 -35.5 -112.5t-115.5 -32.5q-1 0 -73 3z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="667" 
d="M505 746l71 -41q-30 -64 -64.5 -98t-93.5 -34q-33 0 -93 25.5t-92 25.5q-28 0 -43.5 -16t-30.5 -46l-69 45q4 8 10.5 23.5t9.5 20.5q45 93 131 93q36 0 99 -25.5t92 -25.5q26 0 42 13t31 40z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="233" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="288" 
d="M228 370q0 -34 -25 -59t-59 -25t-59 25t-25 59q0 35 25 60t59 25q35 0 59.5 -25t24.5 -60zM210 -223h-131v466h131v-466z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="433" 
d="M333 228v-112q-18 -8 -48 -17v-76h-72v80q-112 41 -112 215q0 83 25.5 152t86.5 91v92h72v-86q16 0 48 -13v-112q-19 13 -33 13q-26 0 -43 -24t-22.5 -51t-5.5 -53q0 -42 15 -76t52 -34q20 0 37 11z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="433" 
d="M413 514l-112 -33q-3 53 -15 69q-13 19 -40 19q-28 0 -44 -18q-18 -20 -18 -66q0 -38 17 -117h112v-80h-102q7 -30 7 -49q0 -59 -41 -108q28 0 69 -23q30 -16 59 -16q36 0 56 35l68 -55q-13 -38 -45 -62q-28 -21 -73 -21q-44 0 -91 18q-53 21 -88 21q-38 0 -76 -28
l-53 72q39 30 78 42q18 37 18 86q0 51 -15 88h-75v80h60q-17 58 -17 117q0 88 51 140t142 52q80 0 123 -47q40 -45 45 -116z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="667" 
d="M644 137l-67 -69l-102 102q-67 -45 -142 -45q-71 0 -140 45l-102 -101l-68 67l102 102q-44 67 -44 138q0 74 45 138l-104 103l68 68l104 -103q67 44 140 44q74 0 138 -43l102 101l68 -67l-102 -102q46 -67 46 -139q0 -69 -44 -137zM472 376q0 57 -40.5 98t-97.5 41
q-58 0 -98.5 -40.5t-40.5 -98.5t40.5 -99t98.5 -41q57 0 97.5 41.5t40.5 98.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="433" 
d="M440 667l-101 -228h81v-68h-111l-24 -55h135v-68h-135v-248h-132v248h-140v68h140l-24 55h-116v68h84l-104 228h145l67 -175q7 -20 12 -39q16 51 76 214h147z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" 
d="M312 335h-125v415h125v-415zM312 -250h-125v415h125v-415z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="433" 
d="M375 562h-114q-6 79 -43 79q-20 0 -30 -18t-10 -39q0 -44 33 -76t79.5 -61t62.5 -52q33 -46 33 -134q0 -114 -70 -167q54 -45 54 -142q0 -68 -42.5 -113t-110.5 -45q-43 0 -80 22t-56 61q-16 31 -22 105h116l1 -18q0 -3 1 -13t1.5 -14.5t2 -13t4.5 -12.5l7 -9t10.5 -7
t14.5 -2q20 0 30 17.5t10 34.5q0 35 -15.5 59.5t-39 37.5t-50.5 31t-50.5 38t-39 60t-15.5 96q0 112 77 169q-32 31 -45 63t-13 87q0 67 43 110.5t110 43.5q68 0 108.5 -41t45.5 -110zM273 253q0 117 -57 117q-56 0 -56 -106q0 -107 56 -107q57 0 57 96z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="475" 
d="M392 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM215 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="762" 
d="M728 333q0 -144 -101 -246t-245 -102t-246 102t-102 246t102 246t246 102t245 -102t101 -246zM660 333q0 118 -80.5 203t-197.5 85q-118 0 -199 -84.5t-81 -202.5t81.5 -203t198.5 -85q116 0 197 85t81 202zM459 219v-82q-22 -8 -44 -8q-85 0 -138.5 60.5t-53.5 146.5
q0 81 53.5 141.5t133.5 60.5q23 0 49 -8v-80q-20 5 -36 5q-49 0 -76 -34.5t-27 -84.5q0 -48 27.5 -86t73.5 -38q17 0 38 7z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="289" 
d="M245 372h-87v20q-20 -25 -48 -25q-44 0 -62.5 46.5t-18.5 108.5q0 151 80 151q28 0 49 -26v20h87v-295zM245 297h-212v44h212v-44zM164 521q0 80 -24 80t-24 -80t25 -80q23 0 23 80z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="396" 
d="M366 66l-67 -38l-116 206l116 206l67 -34l-90 -172zM213 66l-68 -38l-115 206l115 206l68 -34l-91 -172z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M467 0h-125v154h-309v113h434v-267z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="268" 
d="M218 191h-168v108h168v-108z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="775" 
d="M525 145h-97l-64 169v-169h-90v390h94q62 0 102 -26t40 -86q0 -72 -60 -101zM364 370h6q27 0 38 12.5t11 41.5q0 22 -10 32.5t-37 10.5h-8v-97zM666 333q0 118 -80.5 203t-197.5 85q-118 0 -199 -84.5t-81 -202.5t81.5 -203t198.5 -85q116 0 197 85t81 202zM734 333
q0 -144 -101 -246t-245 -102t-246 102t-102 246t102 246t246 102t245 -102t101 -246z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M393 531h-286v80h286v-80z" />
    <glyph glyph-name="degree" unicode="&#xb0;" 
d="M403 511q0 -63 -45 -107t-108 -44t-108 44t-45 107t45 106.5t108 43.5t108 -43.5t45 -106.5zM328 510q0 32 -23 54.5t-55 22.5t-55 -22t-23 -54t23 -55t55 -23t55 23t23 54z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M467 356h-154v-155h-125v155h-155v115h155v155h125v-155h154v-115zM467 51h-434v116h434v-116z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="286" 
d="M112 523h-83q-1 9 -1 26q0 65 26.5 95t90.5 30q51 0 81.5 -27t30.5 -78q0 -10 -1 -19.5t-4.5 -20t-5 -16t-7.5 -18l-7.5 -15.5t-9.5 -18t-9 -16l-58 -105h95v-74h-229l100 173q51 87 51 126q0 46 -30 46q-37 0 -30 -89z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="287" 
d="M120 549h-80q0 62 22.5 93t82.5 31q48 0 77.5 -29t29.5 -77q0 -70 -46 -93q59 -20 59 -98q0 -53 -35.5 -84t-88.5 -31q-118 0 -118 116h85q0 -3 -0.5 -8t-0.5 -7q0 -34 28 -34q33 0 33 59q0 35 -13 44t-50 9v72q25 0 36.5 2t19.5 13t8 34q0 41 -25 41q-17 0 -22 -17.5
t-2 -35.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M378 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="mu" unicode="&#xb5;" 
d="M473 0h-124v58q-34 -68 -99 -68q-62 0 -97 62v-258h-125v653h125v-227q0 -61 17 -88q28 -44 83 -44q30 0 55 19q41 31 41 122v218h124v-447z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" 
d="M473 0h-124v58q-34 -68 -99 -68q-62 0 -97 62v-258h-125v653h125v-227q0 -61 17 -88q28 -44 83 -44q30 0 55 19q41 31 41 122v218h124v-447z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="659" 
d="M586 570h-72v-670h-106v670h-85v-670h-106v421q-4 -1 -11 -1q-78 0 -128.5 46.5t-50.5 123.5q0 177 230 177h329v-97z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="284" 
d="M243 334q0 -42 -29.5 -71.5t-71.5 -29.5t-71.5 29.5t-29.5 71.5t29.5 71.5t71.5 29.5t71.5 -29.5t29.5 -71.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M332 -78l-97 -123l-59 33l73 126z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="286" 
d="M192 267h-87v325h-40v75h127v-400z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="289" 
d="M253 522q0 -155 -110 -155q-57 0 -84.5 42.5t-27.5 109.5q0 69 27.5 111.5t83.5 42.5q111 0 111 -151zM249 297h-215v44h215v-44zM166 517q0 82 -24 82q-25 0 -25 -84q0 -74 25 -74q24 0 24 76z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="396" 
d="M366 234l-115 -206l-68 38l91 168l-91 172l68 34zM213 234l-116 -206l-67 38l91 168l-91 172l67 34z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="720" 
d="M695 92h-34v-92h-87v92h-122v65l113 243h96v-234h34v-74zM550 667l-317 -667h-77l317 667h77zM192 267h-87v325h-40v75h127v-400zM574 166v125l-54 -125h54z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="719" 
d="M545 257h-83q-1 9 -1 26q0 65 26.5 94.5t90.5 29.5q52 0 82 -26.5t30 -77.5q0 -46 -44 -124l-58 -105h95v-74h-229l100 173q51 87 51 127q0 46 -30 46q-37 0 -30 -89zM550 667l-317 -667h-77l317 667h77zM192 267h-87v325h-40v75h127v-400z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="720" 
d="M696 92h-34v-92h-87v92h-122v65l113 243h96v-234h34v-74zM551 667l-317 -667h-77l317 667h77zM120 549h-80q0 62 22.5 93t82.5 31q48 0 77.5 -29t29.5 -77q0 -70 -46 -93q59 -20 59 -98q0 -53 -35.5 -84t-88.5 -31q-118 0 -118 116h85q0 -3 -0.5 -8t-0.5 -7q0 -34 28 -34
q33 0 33 59q0 35 -13 44t-50 9v72q25 0 36.5 2t19.5 13t8 34q0 41 -25 41q-17 0 -22 -17.5t-2 -35.5zM575 166v125l-54 -125h54z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="414" 
d="M287 370q0 -34 -25 -59t-60 -25q-34 0 -59 25t-25 59q0 35 25 60t59 25q35 0 60 -25t25 -60zM260 -39h124q0 -57 -10 -88q-15 -51 -59 -78.5t-100 -27.5q-85 0 -135 56t-50 142q0 72 32 127q24 40 75 57v86h130v-163q-7 1 -20 1q-40 0 -58 -30.5t-18 -73.5q0 -19 3 -36.5
t14.5 -33.5t29.5 -16q42 0 42 78z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM278 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM353 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM358 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM308 846l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20
q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM380 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM210 790q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM129 810q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28t-28 68zM184 810q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5
t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="592" 
d="M585 0h-267l-25 111h-138l-20 -111h-132l152 667h272l28 -124h-132l34 -138h132l30 -126h-130l38 -154h127zM177 216h94l-31 141q-12 52 -19 137q-6 -86 -44 -278z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32zM338 -80l-100 -125l-61 35l76 128z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM248 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM298 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM318 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM335 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM165 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5
t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM188 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM238 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM246 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM270 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM100 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="488" 
d="M52 667h111q69 0 116 -15t88 -58q85 -89 85 -264q0 -96 -24.5 -167t-87 -117t-159.5 -46h-129v283h-53v107h53v277zM184 283v-162q53 1 84 35q51 55 51 173q0 127 -54 185q-31 33 -81 36v-160h67v-107h-67z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="517" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667zM336 846l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17
q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM328 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM399 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM399 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM343 846l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM420 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM250 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5
t19.5 -46.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M363 145l-113 113l-113 -113l-81 82l113 113l-113 112l81 82l113 -113l113 113l81 -82l-113 -112l113 -113z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="542" 
d="M503 641l-47 -75q49 -85 49 -236t-51 -235q-29 -47 -78 -76.5t-104 -29.5q-73 0 -144 56l-33 -51l-50 33l43 68q-51 91 -51 238q0 156 56 244q29 46 77.5 73t102.5 27q82 0 144 -60l36 54zM183 243l158 256q-26 59 -70 59q-92 0 -92 -222q0 -52 4 -93zM361 414l-156 -252
q27 -49 68 -49q29 0 49 23t28.5 60t11.5 67t3 58q0 31 -4 93z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM298 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM378 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM378 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM405 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM235 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="447" 
d="M447 667l-155 -382v-285h-132v285l-160 382h145l49 -145q18 -54 30 -99q9 37 29 100l46 144h148zM348 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="435" 
d="M184 667v-94h18h18.5t17 -0.5t18.5 -1t17 -2.5t18 -4t16 -5.5t17 -8.5q44 -25 67.5 -73t23.5 -103q0 -91 -54.5 -143.5t-146.5 -52.5q-20 0 -30 2v-181h-132v667h132zM184 467v-176q52 0 76 18.5t24 68.5q0 43 -19 67q-18 22 -61 22h-20z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="429" 
d="M66 0v343h-56v104h56v132q0 37 16 74q36 87 149 87q83 0 120.5 -54t37.5 -141q0 -55 -13.5 -91t-55.5 -54q48 -28 65.5 -73t17.5 -110q0 -222 -144 -222q-18 0 -40 5v112q28 3 37 24q11 23 11 89q0 64 -15 94q-11 20 -33 24v111q24 4 32 25q7 19 7 63q0 87 -35 87
q-13 0 -20.5 -7.5t-9 -14t-2 -22t-0.5 -16.5v-569h-125z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM278 553l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM313 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM326 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM283 639l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5
t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM353 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM176 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM102 619q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28
t-28 68zM157 619q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="570" 
d="M544 192h-205q3 -109 56 -109q40 0 40 54h102q-2 -67 -38 -106.5t-102 -39.5q-68 0 -111 55q-52 -55 -114 -55q-64 0 -104.5 38t-40.5 102q0 61 36.5 99t96.5 38q41 0 66 -16v2q0 25 -2 40t-9 30t-22 21.5t-40 6.5q-38 0 -88 -23v107q60 20 101 20q45 0 74 -14.5
t50 -51.5q38 65 110 65q33 0 58 -12t39.5 -35t24.5 -46.5t14.5 -58.5t5.5 -56.5t2 -54.5zM340 269h93q0 95 -46 95q-47 0 -47 -95zM234 131q0 19 -14 33t-33 14t-33 -14t-14 -33t14 -33t33 -14t33 14t14 33z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13zM245 -78l-97 -123l-59 33l73 126z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM255 553l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM315 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM315 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM341 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM164 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM163 553l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM223 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM231 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM257 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM80 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="393" 
d="M324 673l-77 -54q119 -146 119 -396q0 -44 -8 -82.5t-26 -73.5t-51.5 -55.5t-80.5 -20.5q-48 0 -83 20t-54 54t-27.5 73.5t-8.5 84.5q0 98 37 161q20 32 54.5 52t71.5 20q2 0 7 -0.5t8 -0.5q-14 63 -40 103l-84 -58l-46 68l75 56q-33 29 -73 50l58 66q62 -32 93 -62
l88 63zM239 223q0 13 -1 29t-5 39t-13 37.5t-22 14.5q-44 0 -44 -120t44 -120q13 0 22 14.5t13 37t5 38.5t1 30z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v447h125v-70q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323zM280 639l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5
q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM268 553l-61 -36l-114 122l90 42
z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM318 639l-115 -122l-60 36
l85 128z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM328 572l-54 -55l-75 62l-78 -62
l-49 62l127 100z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM274 639l66 -4
q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM351 583q0 -27 -19.5 -46.5
t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM174 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M322 510q0 -29 -21.5 -50.5t-50.5 -21.5q-30 0 -51 21t-21 51t21 51t51 21t51 -21t21 -51zM467 282h-434v115h434v-115zM322 168q0 -29 -21.5 -50.5t-50.5 -21.5q-30 0 -51 21t-21 51t21 51t51 21t51 -21t21 -51z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="393" 
d="M359 441l-32 -51q39 -71 39 -165q0 -45 -8 -84t-26.5 -74t-53 -55.5t-81.5 -20.5q-50 0 -96 32l-24 -39l-46 30l32 53q-36 68 -36 159q0 43 8.5 82.5t27 74.5t53 56t80.5 21q55 0 94 -29l24 38zM239 223q0 22 -2 45t-12.5 49.5t-28.5 26.5q-43 0 -43 -119q0 -122 45 -122
q13 0 22 14.5t13 37t5 38t1 30.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM258 553l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM313 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM325 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM351 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5
t46.5 -19.5t19.5 -46.5zM174 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM311 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="396" 
d="M165 729v-337q29 64 85 64q37 0 62.5 -24.5t37 -64.5t15.5 -74t4 -69q0 -33 -5 -68t-17 -74.5t-37.5 -65t-60.5 -25.5q-50 0 -84 60v-257h-125v935h125zM242 224q0 120 -44 120q-42 0 -42 -121q0 -14 1.5 -30t5 -38.5t12.5 -37t23 -14.5q44 0 44 121z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM342 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM165 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5
t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM368 738h-286v80h286v-80z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM348 531h-286v80h286v-80z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM289 838h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM262 637h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88
h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="460" 
d="M159 667h138l160 -667h-77q-10 -9 -24 -29q-12 -20 -12 -34t9 -23t26 -9q18 0 34 10l-14 -75q-20 -10 -48 -10q-43 0 -66 22q-22 22 -22 55q0 32 17 56q14 20 37 37l-25 112h-133l-24 -112h-132zM179 215h93l-2 9q-21 96 -25 120q-19 120 -22 151q-3 -53 -17 -137
q-4 -25 -25 -133z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="396" 
d="M231 447h125v-447h-38q-16 -18 -24 -30q-12 -17 -12 -35q0 -16 10 -25q9 -9 26 -9t33 10l-14 -71q-22 -10 -45 -10q-41 0 -65 22q-22 22 -22 56q0 27 17 51q16 22 38 41h-29v51q-14 -26 -32 -41q-22 -19 -50 -19q-38 0 -66 29q-56 60 -56 205q0 148 55 203q27 27 64 27
q31 0 55 -21q17 -15 30 -42v55zM153 225q0 -86 24 -112q9 -10 21 -10q10 0 18 8q24 25 24 112q0 88 -26 113q-7 7 -17 7q-11 0 -20 -9q-24 -24 -24 -109z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32zM408 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13zM308 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32zM448 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13zM328 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32zM360 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5
q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13zM265 583q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5
q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32zM448 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13zM328 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="488" 
d="M52 667h111q86 0 135 -23q56 -25 92 -79.5t48.5 -111.5t12.5 -123q0 -71 -14 -128t-44.5 -104t-84.5 -72.5t-127 -25.5h-129v667zM184 122h4q37 0 64.5 20t41 53.5t19.5 67t6 68.5q0 140 -68 196q-25 21 -67 23v-428zM348 824l-127 -100l-129 107l55 55l74 -62l78 62z
" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="535" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v337h125v-729zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM545 667l-85 -204h-70l52 204h103z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="488" 
d="M52 667h111q69 0 116 -15t88 -58q85 -89 85 -264q0 -96 -24.5 -167t-87 -117t-159.5 -46h-129v283h-53v107h53v277zM184 283v-162q53 1 84 35q51 55 51 173q0 127 -54 185q-31 33 -81 36v-160h67v-107h-67z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="416" 
d="M356 507v-507h-125v51q-33 -60 -83 -60q-36 0 -62 24.5t-37.5 62.5t-16.5 69t-5 58q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v135h-108v88h108v134h125v-134h50v-88h-50zM240 204q0 119 -43 119q-44 0 -44 -121q0 -8 1.5 -20t5 -32t13.5 -33.5t25 -13.5
q13 0 22.5 14t13 34.5t5 33t1.5 19.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM328 738h-286v80h286v-80z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM330 531h-286v80h286v-80z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM252 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM254 583q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="355" 
d="M52 667h265v-124h-133v-144h117v-126h-117v-150h135v-123h-33q-10 -9 -24 -29q-12 -20 -12 -34t9 -23t26 -9q18 0 34 10l-14 -75q-20 -10 -48 -10q-43 0 -66 22q-22 22 -22 55q0 32 17 56q14 20 37 37h-171v667z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="373" 
d="M233 138h106q-9 -66 -53 -118q-9 -11 -22 -25t-17 -20q-16 -21 -16 -40q0 -16 10 -25q9 -9 24 -9q21 0 35 10l-14 -71q-20 -10 -45 -10q-39 0 -65 24q-22 22 -22 52q0 28 18 54q12 18 31 31q-76 0 -120 45q-57 59 -57 177q0 136 65 201q41 41 103 41q58 0 95 -36
q58 -58 58 -177q0 -28 -3 -52h-198v-9q0 -65 23 -87q11 -9 25 -9q16 0 25 9q14 13 14 37v7zM148 267h90q0 62 -18 81q-12 12 -27 12t-25 -10q-20 -19 -20 -71v-12z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM318 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM315 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118zM403 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44zM333 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118zM339 838h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44zM265 637h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118zM341 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44zM261 583q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118zM333 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44zM123 495l92 182h70l-59 -182h-103z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="501" 
d="M449 0h-132v295h-133v-295h-132v667h132v-247h133v247h132v-667zM378 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v729h125v-352q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323zM233 792l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="501" 
d="M449 0h-132v295h-133v-295h-132v517h-50v97h50v53h132v-53h133v53h132v-53h50v-97h-50v-517zM317 517h-133v-97h133v97z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="419" 
d="M379 0h-125v254q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-239h-125v507h-55v88h55v134h125v-134h108v-88h-108v-150q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-303z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM261 738h-286v80h286v-80z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM246 531h-286v80h286v-80z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="236" 
d="M52 667h132v-667q-10 -9 -24 -29q-12 -20 -12 -34t9 -23t26 -9q18 0 34 10l-14 -75q-20 -10 -48 -10q-43 0 -66 22q-22 22 -22 55q0 32 17 56q14 20 37 37h-69v667z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="206" 
d="M39 447h125v-447q-19 -19 -27 -30q-14 -19 -14 -37q0 -13 8 -21q9 -11 27 -11q14 0 32 10l-14 -71q-20 -10 -46 -10q-40 0 -63 23q-21 21 -21 55q0 26 20 54q15 20 39 38h-66v447zM25 579q0 32 23.5 55.5t55.5 23.5t55.5 -23.5t23.5 -55.5t-23.5 -55.5t-55.5 -23.5
t-55.5 23.5t-23.5 55.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM185 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="310" 
d="M258 667v-469q0 -68 -14 -109q-16 -47 -56 -73.5t-90 -26.5q-59 0 -98 21v132q30 -37 70 -37q20 0 32.5 8.5t17 27t5.5 30t1 34.5v462h132zM318 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="206" 
d="M165 -215h-125v662h125v-662zM231 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="452" 
d="M445 0h-138l-123 339v-339h-132v667h132v-298l111 298h135l-123 -308zM303 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="389" 
d="M392 0h-132l-95 231v-231h-125v729h125v-454l74 172h130l-98 -187zM263 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="329" 
d="M322 0h-270v667h132v-542h138v-125zM248 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="205" 
d="M165 0h-125v729h125v-729zM223 869l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="329" 
d="M322 0h-270v667h132v-542h138v-125zM252 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="205" 
d="M165 0h-125v729h125v-729zM158 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="378" 
d="M322 0h-270v667h132v-542h138v-125zM388 667l-85 -198h-70l51 198h104z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="345" 
d="M165 0h-125v729h125v-729zM355 667l-85 -204h-70l52 204h103z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="329" 
d="M322 0h-270v667h132v-542h138v-125zM347 334q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="322" 
d="M165 0h-125v729h125v-729zM330 365q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="329" 
d="M322 0h-270v202l-52 -58v112l52 58v353h132v-240l118 131v-112l-118 -131v-190h138v-125z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="205" 
d="M165 729v-240l48 49v-100l-48 -49v-389h-125v277l-49 -50v100l49 50v352h125z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="517" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667zM378 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v447h125v-70q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323zM318 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="517" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667zM323 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v447h125v-70q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323zM253 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="517" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667zM386 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v447h125v-70q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323zM330 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM414 738h-286v80h286v-80z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM339 531h-286v80h286v-80z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM335 838h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM260 637h66q-4 -54 -38 -88
q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="542" 
d="M505 333q0 -165 -60 -250q-30 -42 -76.5 -68t-97.5 -26q-50 0 -96.5 26t-77.5 68q-60 82 -60 250q0 166 60 251q29 42 76 67.5t98 25.5t97 -25.5t77 -67.5q60 -82 60 -251zM365 336q0 32 -3 62t-11 69.5t-29 64t-51 24.5t-51 -24.5t-29 -64t-11 -69.5t-3 -62t3 -62
t11 -69.5t29 -64t51 -24.5t51 24.5t29 64t11 69.5t3 62zM433 846l-85 -122l-57 36l60 128zM296 846l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="392" 
d="M365 223q0 -45 -8 -83.5t-26 -73.5t-52.5 -55t-81.5 -20q-46 0 -80.5 20.5t-53 55.5t-27.5 74t-9 82t9 82t27.5 74t53 55.5t80.5 20.5q48 0 82 -20t52 -55t26 -73.5t8 -83.5zM239 223q0 120 -42 120q-44 0 -44 -120t44 -120q42 0 42 120zM359 639l-85 -122l-57 36l60 128
zM222 639l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="675" 
d="M638 0h-274v52q-46 -63 -118 -63q-61 0 -104.5 33.5t-65 88t-30.5 108t-9 110.5q0 58 9.5 113.5t32 111t67.5 89.5t107 34q70 0 111 -48v38h273v-123h-143v-139h128v-128h-128v-152h144v-125zM364 335q0 226 -92 226q-31 0 -51.5 -25t-29 -66t-11 -71t-2.5 -63
q0 -32 3 -62.5t11 -71t29 -65.5t51 -25q92 0 92 223z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="583" 
d="M444 139h106q-5 -69 -42.5 -108.5t-105.5 -39.5q-67 0 -116 49q-38 -49 -98 -49q-57 0 -95.5 36t-52.5 84t-14 104q0 58 15.5 110t55.5 91t100 39q56 0 98 -44q45 44 103 44q57 0 94.5 -32.5t51 -78t13.5 -101.5q0 -18 -2 -54h-200v-10q0 -93 49 -93q20 0 30 16t10 37z
M444 266v2q0 89 -43 89q-44 0 -44 -91h87zM239 223q0 120 -42 120q-44 0 -44 -119q0 -121 44 -121q42 0 42 120z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="473" 
d="M440 0h-139l-117 307v-307h-132v667h143q97 0 145 -32q38 -25 58.5 -69t20.5 -93q0 -112 -95 -170zM184 563v-183q7 -1 22 -1q41 0 63 25.5t22 67.5q0 91 -82 91h-25zM348 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="278" 
d="M268 463v-121q-12 2 -18 2q-52 0 -68.5 -26t-16.5 -81v-237h-125v447h125v-55q23 33 45 49t58 22zM278 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="473" 
d="M440 0h-139l-117 307v-307h-132v667h143q97 0 145 -32q38 -25 58.5 -69t20.5 -93q0 -112 -95 -170zM184 563v-183q7 -1 22 -1q41 0 63 25.5t22 67.5q0 91 -82 91h-25zM298 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="278" 
d="M268 463v-121q-12 2 -18 2q-52 0 -68.5 -26t-16.5 -81v-237h-125v447h125v-55q23 33 45 49t58 22zM216 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="473" 
d="M440 0h-139l-117 307v-307h-132v667h143q97 0 145 -32q38 -25 58.5 -69t20.5 -93q0 -112 -95 -170zM184 563v-183q7 -1 22 -1q41 0 63 25.5t22 67.5q0 91 -82 91h-25zM348 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="278" 
d="M268 463v-121q-12 2 -18 2q-52 0 -68.5 -26t-16.5 -81v-237h-125v447h125v-55q23 33 45 49t58 22zM280 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32zM338 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38zM308 639
l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32zM338 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38zM308 572
l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32zM262 -80l-100 -125l-61 35l76 128z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38zM234 -78
l-97 -123l-59 33l73 126z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32zM338 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38zM308 617
l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="Tcedilla" unicode="&#x162;" horiz-adv-x="338" 
d="M338 545h-103v-545h-132v545h-103v122h338v-122zM246 -80l-100 -125l-61 35l76 128z" />
    <glyph glyph-name="tcedilla" unicode="&#x163;" horiz-adv-x="230" 
d="M228 343h-51v-343h-125v343h-43v104h43v127h125v-127h51v-104zM197 -78l-97 -123l-59 33l73 126z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="338" 
d="M235 545h103v122h-338v-122h103v-545h132v545zM298 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="355" 
d="M228 343h-51v-343h-125v343h-43v104h43v127h125v-127h51v-104zM365 702l-85 -204h-70l52 204h103z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM327 846l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5
t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM274 639l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5
t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM388 738h-286v80h286v-80z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM340 531h-286v80h286v-80z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM314 838h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66
q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM261 637h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29
q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM154 810q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28
t-28 68zM209 810q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM101 619q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28t-28 68zM156 619
q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="499" 
d="M447 667v-497q0 -65 -22 -102q-47 -79 -177 -79q-87 0 -141.5 43t-54.5 128v507h132v-443q0 -23 0.5 -32.5t3.5 -29t9.5 -28.5t19.5 -17t33 -8q19 0 32.5 7.5t19.5 15.5t9 27t3.5 28t0.5 31v449h132zM403 846l-85 -122l-57 36l60 128zM266 846l-86 -122l-57 36l60 128z
" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="393" 
d="M353 447v-302q0 -52 -21 -85t-58.5 -51t-78.5 -18t-79 18.5t-58 51.5q-18 30 -18 77v309h125v-275q0 -65 31 -65q8 0 13.5 3t9 9.5t5.5 13t2.5 16t1 15.5t0 15t-0.5 12v256h126zM353 639l-85 -122l-57 36l60 128zM216 639l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="499" 
d="M447 667v-487q0 -90 -48 -138q-15 -16 -54 -40q-27 -17 -39 -31q-12 -15 -12 -33q0 -15 9 -24t26 -9q18 0 34 10l-14 -75q-20 -10 -48 -10q-43 0 -66 22q-22 22 -22 55q0 49 42 82q-108 0 -161 55q-42 44 -42 116v507h132v-454q0 -65 18 -85q17 -19 48 -19q33 0 48 19
q17 20 17 76v463h132z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="393" 
d="M353 447v-295q0 -83 -39 -124q-12 -12 -32 -28t-24 -19q-24 -22 -24 -49q0 -12 10 -22q9 -9 24 -9q21 0 35 10l-14 -71q-20 -10 -45 -10q-39 0 -65 24q-22 22 -22 53q0 27 18 53q12 18 31 31q-83 0 -129 46q-37 37 -37 101v309h125v-275q0 -42 11 -55q7 -10 20 -10
q12 0 18 6q13 11 13 53v281h126z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="447" 
d="M447 667l-155 -382v-285h-132v285l-160 382h145l49 -145q18 -54 30 -99q9 37 29 100l46 144h148zM375 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM205 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5
t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="456" 
d="M438 667l-228 -542h209v-125h-401l222 541h-194v126h392zM356 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="341" 
d="M327 447l-138 -344h124v-103h-300l142 343h-127v104h299zM299 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="456" 
d="M438 667l-228 -542h209v-125h-401l222 541h-194v126h392zM308 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="341" 
d="M327 447l-138 -344h124v-103h-300l142 343h-127v104h299zM246 583q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="456" 
d="M438 667l-228 -542h209v-125h-401l222 541h-194v126h392zM368 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="341" 
d="M327 447l-138 -344h124v-103h-300l142 343h-127v104h299zM299 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="uni018F" unicode="&#x18f;" horiz-adv-x="524" 
d="M38 371h308q-1 48 -13 98t-36 72q-21 20 -50 20q-25 0 -42 -17q-25 -24 -29 -75l-128 50q17 62 57 102q55 56 144 56q93 0 152 -59q87 -87 87 -282q0 -92 -24 -167t-66 -119q-58 -61 -147 -61q-81 0 -134 55q-80 83 -80 276q0 31 1 51zM168 253v-5q0 -85 32 -118
q20 -21 49 -21q30 0 49 19q19 17 31.5 55t13.5 70h-175z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="433" 
d="M422 728l-23 -118q-12 6 -25 6q-7 0 -13 -2t-10.5 -7t-7.5 -9.5t-5.5 -13t-3.5 -12.5t-2.5 -14t-2.5 -13l-19 -98h70l-13 -68h-70l-53 -262l-8 -40.5t-9 -44.5t-10.5 -40.5t-14.5 -40t-20 -32.5t-28 -28.5t-36.5 -16.5t-47.5 -7q-25 0 -61 9l23 118q14 -7 27 -7
q7 0 12.5 2t10 6.5t7.5 8t5.5 11.5t4 11.5t4 12.5t2.5 11q5 20 16 76l50 253h-66l13 68h66l26 128q16 89 46 121q41 44 106 44q28 0 60 -12z" />
    <glyph glyph-name="uni01CF" unicode="&#x1cf;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM246 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="uni01D0" unicode="&#x1d0;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM230 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="uni01DD" unicode="&#x1dd;" horiz-adv-x="372" 
d="M135 308l-102 20q8 55 43 90q37 37 102 37q70 0 113 -43q55 -55 55 -175q0 -142 -65 -207q-38 -39 -98 -39q-63 0 -100 36q-57 56 -57 178q0 9 1 28.5t1 22.5h198v4q0 73 -23 92q-10 9 -28 9q-16 0 -26 -10q-14 -16 -14 -43zM224 179h-90q-3 -19 1.5 -44t16.5 -37t27 -12
t25 10q20 19 20 71v12z" />
    <glyph glyph-name="Gcaron" unicode="&#x1e6;" horiz-adv-x="524" 
d="M273 371h214q1 -17 1 -51q0 -86 -12 -141q-18 -83 -70 -136.5t-132 -53.5q-64 0 -112.5 32t-74 84.5t-38 109t-12.5 116.5q0 49 6.5 95t23.5 92.5t42.5 81t68 56t96.5 21.5q74 0 124 -42.5t72 -115.5l-128 -50q-3 19 -6.5 31.5t-10.5 28.5t-19.5 24t-30.5 8
q-31 0 -52.5 -25.5t-30 -66.5t-11.5 -72t-3 -64q0 -224 98 -224q78 0 81 144h-84v118zM403 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="gcaron" unicode="&#x1e7;" horiz-adv-x="401" 
d="M361 447v-475q0 -54 -13 -85q-20 -47 -63 -70t-97 -23q-144 0 -157 155h110q5 -54 46 -54q48 0 48 95v66q-13 -21 -21.5 -32.5t-25.5 -22t-37 -10.5q-36 0 -62 24t-39 62t-18.5 73.5t-5.5 68.5q0 36 4.5 70.5t16 75t37 65.5t62.5 25q54 0 89 -62v54h126zM241 224
q0 21 -2 44t-12.5 49t-28.5 26q-45 0 -45 -119t45 -119q18 0 28.5 26t12.5 49t2 44zM327 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="592" 
d="M585 0h-267l-25 111h-138l-20 -111h-132l152 667h272l28 -124h-132l34 -138h132l30 -126h-130l38 -154h127zM177 216h94l-31 141q-12 52 -19 137q-6 -86 -44 -278zM424 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="570" 
d="M544 192h-205q3 -109 56 -109q40 0 40 54h102q-2 -67 -38 -106.5t-102 -39.5q-68 0 -111 55q-52 -55 -114 -55q-64 0 -104.5 38t-40.5 102q0 61 36.5 99t96.5 38q41 0 66 -16v2q0 25 -2 40t-9 30t-22 21.5t-40 6.5q-38 0 -88 -23v107q60 20 101 20q45 0 74 -14.5
t50 -51.5q38 65 110 65q33 0 58 -12t39.5 -35t24.5 -46.5t14.5 -58.5t5.5 -56.5t2 -54.5zM340 269h93q0 95 -46 95q-47 0 -47 -95zM234 131q0 19 -14 33t-33 14t-33 -14t-14 -33t14 -33t33 -14t33 14t14 33zM413 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32zM258 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38zM218 -46
l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="338" 
d="M338 545h-103v-545h-132v545h-103v122h338v-122zM228 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="230" 
d="M228 343h-51v-343h-125v343h-43v104h43v127h125v-127h51v-104zM168 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="uni0228" unicode="&#x228;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM263 -80l-100 -125l-61 35l76 128z" />
    <glyph glyph-name="uni0229" unicode="&#x229;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM263 -78l-97 -123l-59 33l73 126z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="206" 
d="M165 -215h-125v662h125v-662z" />
    <glyph glyph-name="uni0259" unicode="&#x259;" horiz-adv-x="372" 
d="M135 308l-102 20q8 55 43 90q37 37 102 37q70 0 113 -43q55 -55 55 -175q0 -142 -65 -207q-38 -39 -98 -39q-63 0 -100 36q-57 56 -57 178q0 9 1 28.5t1 22.5h198v4q0 73 -23 92q-10 9 -28 9q-16 0 -26 -10q-14 -16 -14 -43zM224 179h-90q-3 -19 1.5 -44t16.5 -37t27 -12
t25 10q20 19 20 71v12z" />
    <glyph glyph-name="afii57929" unicode="&#x2bc;" horiz-adv-x="218" 
d="M195 667l-85 -204h-70l52 204h103z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M378 572l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M378 617l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" 
d="M393 531h-286v80h286v-80z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M314 637h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M317 583q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M154 619q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28t-28 68zM209 619q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M324 -97l-14 -71q-19 -17 -46 -17q-40 0 -66 26q-22 21 -22 53q0 31 18 60q18 28 36 46h59q0 -1 -4 -6t-9.5 -13.5t-9.5 -15.5q-13 -22 -13 -39t11 -28q12 -12 29 -12t31 17z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M328 639l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M423 639l-85 -122l-57 36l60 128zM286 639l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="2" 
d="M-79 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="667" 
d="M649 0h-632l246 667h136zM468 113l-136 401l-136 -401h272z" />
    <glyph glyph-name="uni0394" unicode="&#x2206;" horiz-adv-x="667" 
d="M649 0h-632l246 667h136zM468 113l-136 401l-136 -401h272z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="667" 
d="M638 0h-269v100q139 76 139 252q0 86 -46 152.5t-128 66.5t-128.5 -66.5t-46.5 -152.5q0 -177 139 -252v-100h-269v112h140q-73 46 -107 107t-34 147q0 135 85.5 226t219.5 91t219.5 -91t85.5 -226q0 -87 -33 -147.5t-106 -106.5h139v-112z" />
    <glyph glyph-name="uni03A9" unicode="&#x2126;" horiz-adv-x="667" 
d="M638 0h-269v100q139 76 139 252q0 86 -46 152.5t-128 66.5t-128.5 -66.5t-46.5 -152.5q0 -177 139 -252v-100h-269v112h140q-73 46 -107 107t-34 147q0 135 85.5 226t219.5 91t219.5 -91t85.5 -226q0 -87 -33 -147.5t-106 -106.5h139v-112z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="659" 
d="M622 335h-75v-335h-125v335h-184v-335h-125v335h-76v112h585v-112z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="365" 
d="M325 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM174 775q0 -26 -18 -44t-43 -18t-43 18t-18 44q0 25 18 43t43 18t43 -18t18 -43zM329 775q0 -26 -18 -44t-43 -18t-43 18.5t-18 43.5t18 43t43 18t43 -18t18 -43z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="555" 
d="M404 544h-154v-147q45 10 81 10q89 0 138 -47q56 -55 56 -167q0 -109 -55 -162q-42 -42 -107 -42q-46 0 -88 22l29 112q14 -12 33 -12q26 0 41 19q15 20 15 60q0 44 -20 69q-21 26 -61 26q-27 0 -62 -11v-274h-132v545h-103v122h389v-123z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="348" 
d="M318 544h-134v-544h-132v667h266v-123zM302 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="421" 
d="M376 152v-138q-39 -21 -89 -21q-99 0 -160 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-33 29 -78 29q-50 0 -81 -39q-36 -45 -41 -110h172v-118h-177q0 -89 44 -132q29 -29 77 -29q50 0 84 32z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="391" 
d="M327 645v-129q-50 43 -95 43q-29 0 -47.5 -18t-18.5 -47q0 -27 15 -49.5t37 -38l48 -35.5t48 -41t37 -56t15 -79q0 -89 -51 -147.5t-139 -58.5q-65 0 -115 34v126q54 -36 98 -36q34 0 56 20t22 54q0 19 -7 34q-3 6 -8.5 12.5t-10.5 11t-13.5 11l-13 10t-14 9.5t-11.5 8
q-63 44 -93 86.5t-30 110.5q0 83 46.5 140t127.5 57q60 0 117 -32z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="236" 
d="M184 0h-132v667h132v-667zM222 790q0 -27 -13.5 -46.5t-32.5 -19.5t-32 19.5t-13 46.5t13 46.5t32 19.5t32.5 -19.5t13.5 -46.5zM106 790q0 -27 -13.5 -46.5t-32.5 -19.5t-32.5 19.5t-13.5 46.5t13.5 46.5t32.5 19.5t32.5 -19.5t13.5 -46.5z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="310" 
d="M258 667v-469q0 -68 -14 -109q-16 -47 -56 -73.5t-90 -26.5q-59 0 -98 21v132q30 -37 70 -37q20 0 32.5 8.5t17 27t5.5 30t1 34.5v462h132z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="720" 
d="M462 406h7q109 0 165 -54t56 -155q0 -92 -53 -144q-54 -53 -162 -53h-143v542h-104v-276q0 -53 -2.5 -87t-16 -79.5t-39.5 -70.5q-39 -38 -103 -38q-30 0 -63 8v134q14 -14 36 -14q24 0 38 14q24 24 24 120v414h360v-261zM462 292v-178h4q49 0 71 21q23 23 23 66
q0 46 -25 69q-24 22 -66 22h-7z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="703" 
d="M313 667h132v-261h7q109 0 165 -54t56 -155q0 -92 -53 -144q-54 -53 -162 -53h-145v292h-129v-292h-132v667h132v-261h129v261zM445 292v-178h4q49 0 71 21q23 23 23 66q0 46 -25 69q-24 22 -66 22h-7z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="544" 
d="M118 0v545h-103v122h389v-123h-154v-149q41 11 86 11q69 0 110 -29q36 -25 47 -62.5t11 -91.5v-223h-132v211q0 39 -16 55q-15 15 -51 15q-31 0 -55 -9v-272h-132z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="452" 
d="M445 0h-138l-123 339v-339h-132v667h132v-298l111 298h135l-123 -308zM354 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM294 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="493" 
d="M52 667h132v-542h125v542h132v-667h-134v-130h-120v130h-135v667z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="434" 
d="M52 667h303v-123h-171v-138h3q103 0 161 -56q56 -55 56 -156q0 -91 -52 -141q-56 -53 -164 -53h-136v667zM184 292v-178h4q39 0 62 20q24 20 24 66q0 45 -25 70q-22 22 -62 22h-3z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="435" 
d="M52 667h141q92 0 140 -47q55 -53 55 -144q0 -58 -31 -95q-23 -27 -53 -39q46 -17 72 -59q23 -39 23 -94q0 -82 -50 -132q-57 -57 -157 -57h-140v667zM184 558v-165q37 2 58 23t21 62q0 39 -21 60q-20 20 -58 20zM184 291v-177q38 0 60 21q25 23 25 65q0 46 -26 70
q-24 21 -59 21z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="348" 
d="M318 544h-134v-544h-132v667h266v-123z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="513" 
d="M89 667h360v-542h54v-255h-120v130h-257v-130h-120v255q34 12 53 39q20 26 25 65.5t5 109.5v328zM217 542v-189q0 -118 -16 -165q-13 -37 -44 -63h160v417h-100z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="616" 
d="M608 0h-134l-103 335v-335h-126v335l-103 -335h-134l120 362l-121 305h131l107 -302v302h126v-302l107 302h131l-121 -305z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="419" 
d="M47 496v137q58 44 132 44q87 0 136 -51q48 -49 48 -130q0 -57 -23 -95t-63 -52q43 -12 71 -45q35 -42 35 -106q0 -99 -61 -158q-53 -51 -143 -51q-80 0 -134 42v136q22 -26 47 -39q27 -15 59 -15q41 0 63 22q24 23 24 63t-21 61q-25 24 -82 24h-18v118h14q51 0 74 19
q24 20 24 60q0 33 -22 55q-23 20 -56 20q-32 0 -60 -18q-26 -17 -44 -41z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="487" 
d="M47 0v667h130v-290q0 -59 -11 -120l152 410h122v-667h-130v292q0 60 9 123l-150 -415h-122z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="487" 
d="M47 0v667h130v-290q0 -59 -11 -120l152 410h122v-667h-130v292q0 60 9 123l-150 -415h-122zM310 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="452" 
d="M445 0h-138l-123 339v-339h-132v667h132v-298l111 298h135l-123 -308z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="516" 
d="M102 667h362v-667h-132v542h-104v-276q0 -53 -2.5 -87t-16 -79.5t-39.5 -70.5q-39 -38 -103 -38q-30 0 -63 8v134q14 -14 36 -14q24 0 38 14q24 24 24 120v414z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="646" 
d="M624 0h-135l-33 480q-3 -60 -20 -154l-59 -326h-101l-62 302q-14 66 -17 100l-8 78l-21 -480h-141l58 667h174l65 -394l53 394h178z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="501" 
d="M449 0h-132v295h-133v-295h-132v667h132v-247h133v247h132v-667z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM271 556q-27 0 -48 -22q-46 -46 -46 -198t46 -198q21 -22 48 -22t48 22q46 46 46 198t-46 198q-21 22 -48 22z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="493" 
d="M441 0h-132v542h-125v-542h-132v667h389v-667z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="435" 
d="M52 667h160q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -102 -64 -152.5t-167 -42.5v-274h-132v667zM184 385h8q47 0 69.5 19.5t22.5 65.5q0 90 -77 90h-23v-175z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="409" 
d="M376 152v-138q-38 -21 -91 -21q-98 0 -158 60q-90 90 -90 270q0 187 96 284q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="338" 
d="M338 545h-103v-545h-132v545h-103v122h338v-122z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="620" 
d="M244 0v78q-84 4 -141 58q-73 68 -73 216q0 140 69 210q59 59 145 64v70h132v-70q88 -6 146 -64q68 -68 68 -210q0 -148 -72 -216q-8 -8 -21 -17.5t-48 -23.5t-73 -17v-78h-132zM366 508v-312q37 3 59 25q35 35 35 131q0 99 -37 134q-24 22 -57 22zM254 196v312
q-33 0 -57 -22q-37 -35 -37 -134q0 -96 35 -131q22 -22 59 -25z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="443" 
d="M443 0h-153q-6 16 -20.5 49.5l-22.5 53t-17 49t-13 54.5q-12 -57 -69 -206h-148l141 344l-141 323h148q4 -10 19 -44.5t22.5 -54t16.5 -48t11 -49.5q5 24 14.5 52.5t16.5 46t22 51.5l20 46h142l-142 -323z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="524" 
d="M52 667h132v-542h125v542h132v-542h69v-255h-120v130h-338v667z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="483" 
d="M299 0v292q-41 -11 -86 -11q-69 0 -110 29q-36 25 -47 62.5t-11 91.5v203h132v-191q0 -39 16 -55q15 -15 51 -15q31 0 55 9v252h132v-667h-132z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="724" 
d="M52 667h130v-542h115v542h130v-542h115v542h130v-667h-620v667z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="761" 
d="M52 667h130v-542h115v542h130v-542h115v542h130v-542h75v-255h-120v130h-575v667z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="483" 
d="M93 0v545h-83v122h215v-261h7q109 0 165 -54t56 -155q0 -92 -53 -144q-54 -53 -162 -53h-145zM225 292v-178h4q49 0 71 21q23 23 23 66q0 46 -25 69q-24 22 -66 22h-7z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="631" 
d="M579 0h-132v667h132v-667zM52 667h132v-261h4q107 0 163 -55q57 -55 57 -156q0 -94 -50 -143q-53 -52 -162 -52h-144v667zM184 292v-178h4q51 0 72 20q22 22 22 66q0 45 -25 69q-25 23 -70 23h-3z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="442" 
d="M52 667h132v-261h7q109 0 165 -54t56 -155q0 -92 -53 -144q-54 -53 -162 -53h-145v667zM184 292v-178h4q49 0 71 21q23 23 23 66q0 46 -25 69q-24 22 -66 22h-7z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="421" 
d="M45 14v138q36 -32 82 -32q42 0 71 29q20 20 33 58t13 74h-159v118h155q-1 31 -12 66.5t-30 54.5q-26 28 -71 28q-46 0 -82 -29v136q45 19 100 19q94 0 153 -59q86 -85 86 -279q0 -196 -86 -283q-60 -60 -159 -60q-56 0 -94 21z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="751" 
d="M714 334q0 -195 -82 -281q-61 -64 -150 -64q-94 0 -153 59q-72 74 -76 233h-69v-281h-132v667h132v-261h71q5 140 78 212q59 59 147 59q91 0 152 -66q50 -53 66 -121.5t16 -155.5zM480 556q-27 0 -48 -22q-46 -46 -46 -198t46 -198q21 -22 48 -22t48 22q46 46 46 198
t-46 198q-21 22 -48 22z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="465" 
d="M33 0l121 284q-41 12 -68 41q-44 46 -44 129q0 105 55 160q53 53 173 53h143v-667h-132v276l-106 -276h-142zM281 559h-25q-39 0 -59 -21q-23 -25 -23 -73q0 -47 25 -70q23 -21 68 -21h14v185z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="400" 
d="M262 548l-31 -13q-37 -15 -57 -34q-40 -40 -40 -116q9 28 31 48q26 22 65 22q53 0 87 -33q56 -55 56 -196q0 -136 -63 -196q-41 -39 -107 -39q-67 0 -106 39q-27 26 -41.5 72.5t-17.5 84t-3 90.5q0 122 15 195t56 114q32 32 84 52l23 8.5t21.5 8.5t14 7.5t12.5 10.5
q11 13 9 27h99q0 -70 -37 -109q-20 -22 -70 -43zM204 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107q-9 13 -23 13z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="383" 
d="M40 447h151q76 0 114 -37q33 -33 33 -89q0 -38 -17 -60q-17 -23 -45 -31q31 -7 49 -27q28 -28 28 -75q0 -58 -37 -92q-39 -36 -113 -36h-163v447zM165 266q30 0 46 13q15 13 15 37q0 21 -14 34q-13 12 -41 12h-6v-96zM165 85q32 0 47 12q17 13 17 40q0 26 -17 40
q-16 13 -47 13v-105z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="285" 
d="M271 343h-106v-343h-125v447h231v-104z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="419" 
d="M74 447h292v-343h45v-208h-102v104h-203v-104h-102v208q22 8 38 23q32 31 32 121v199zM246 104v239h-62v-104q0 -50 -8 -79q-9 -31 -34 -56h104z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="509" 
d="M506 0h-127l-66 206v-206h-117v206l-66 -206h-127l94 234l-95 213h128l66 -206v206h117v-206l66 206h128l-94 -213z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="360" 
d="M256 234q34 -11 52 -30q27 -27 27 -69q0 -61 -43 -102q-43 -42 -117 -42q-50 0 -88 15q-27 10 -62 37l54 93q14 -16 33 -26q16 -9 42 -9q25 0 38 11q13 12 13 29q0 18 -13 27q-16 11 -55 11h-22v94h19q27 0 42 7q21 10 21 32q0 16 -13 27q-13 10 -36 10q-24 0 -43 -13
q-16 -10 -29 -27l-46 98q26 18 53 31q37 17 86 17q80 0 121 -43q31 -33 31 -83q0 -37 -19 -62q-17 -22 -46 -33z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="405" 
d="M40 447h118v-159q0 -52 -9 -117q13 47 32 94l73 182h111v-447h-118v166q0 44 8 114q-14 -59 -35 -111l-69 -169h-111v447z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="405" 
d="M40 447h118v-159q0 -52 -9 -117q13 47 32 94l73 182h111v-447h-118v166q0 44 8 114q-14 -59 -35 -111l-69 -169h-111v447zM260 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="370" 
d="M373 0h-132l-76 204v-204h-125v447h125v-204l66 204h128l-91 -212z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="418" 
d="M378 447v-447h-125v343h-62v-134q0 -142 -45 -187q-31 -31 -91 -31q-27 0 -51 8v106q10 -7 25 -7q17 0 28 11q20 20 20 95v243h301z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="514" 
d="M301 0h-87l-37 161q-15 63 -22 131q-1 -8 -4.5 -60t-4.5 -71l-10 -161h-122l54 447h130l42 -189q8 -37 17 -106q9 69 17 106l42 189h130l54 -447h-121l-11 161q-1 14 -8 131q-4 -46 -23 -132z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="398" 
d="M165 447v-157h68v157h125v-447h-125v186h-68v-186h-125v447h125z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM196 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107q-9 13 -23 13z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="390" 
d="M40 447h310v-447h-125v343h-60v-343h-125v447z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="396" 
d="M165 447v-55q29 63 84 63q37 0 62.5 -24t37 -63t16 -73t4.5 -70q0 -34 -5 -69.5t-16.5 -75t-37.5 -64.5t-62 -25q-50 0 -83 60v-257h-125v653h125zM243 223q0 120 -45 120q-42 0 -42 -120t43 -120q14 0 23.5 14t13.5 36t5.5 38.5t1.5 31.5z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="281" 
d="M261 121v-114q-36 -16 -71 -16q-43 0 -76 21t-51 55t-27 72.5t-9 78.5q0 60 14.5 111t55 89t101.5 38q31 0 63 -13v-110q-15 15 -35 15q-28 0 -45.5 -22t-22.5 -46.5t-5 -52.5q0 -119 71 -119q19 0 37 13z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="301" 
d="M293 343h-80v-343h-125v343h-80v104h285v-104z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="528" 
d="M202 -206v200q-74 6 -121 55q-56 58 -56 180q0 112 58 170q48 47 119 54v214h124v-214q73 -9 119 -55q58 -58 58 -172q0 -119 -56 -179q-48 -50 -121 -53v-200h-124zM315 347v-247q29 2 46 20q26 26 26 103q0 82 -29 108q-18 16 -43 16zM213 100v247q-25 0 -43 -16
q-29 -27 -29 -108q0 -77 26 -103q17 -18 46 -20z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="385" 
d="M389 0h-132l-41 78q-16 30 -24 57q-13 -36 -26 -60l-41 -75h-128l128 240l-111 207h129l24 -47q15 -28 25 -65q7 30 27 68l23 44h129l-114 -207z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="414" 
d="M302 0h-262v447h125v-343h68v343h125v-343h48v-208h-104v104z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="387" 
d="M222 0v184q-30 -10 -63 -10q-63 0 -95 32q-34 34 -34 106v135h122v-126q0 -24 10 -34q11 -9 29 -9q14 0 31 6v163h125v-447h-125z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="568" 
d="M344 104h64v343h120v-447h-488v447h120v-343h64v343h120v-343z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="594" 
d="M344 104h64v343h120v-343h58v-208h-104v104h-442v447h120v-343h64v343h120v-343z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="426" 
d="M209 283h33q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-163v343h-70v104h195v-164zM209 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="547" 
d="M507 0h-125v447h125v-447zM40 447h125v-164h28q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-158v447zM165 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="382" 
d="M40 447h125v-164h33q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-163v447zM165 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="313" 
d="M30 12v118q24 -27 63 -27q33 0 51 18q21 21 23 61h-102v92h102q-2 32 -20 53t-53 21q-36 0 -64 -30v115q39 23 88 23q70 0 114 -42q61 -59 61 -187q0 -135 -61 -196q-40 -40 -111 -40q-53 0 -91 21z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="572" 
d="M545 223q0 -134 -61 -193q-41 -39 -111 -39q-66 0 -105 37q-54 50 -56 148h-47v-176h-125v447h125v-167h48q4 88 54 136q40 39 106 39q70 0 111 -39q61 -59 61 -193zM376 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107
q-9 13 -23 13z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="386" 
d="M20 0l85 167q-34 12 -55 40q-23 31 -23 82q0 75 43 116q44 42 142 42h134v-447h-125v169l-71 -169h-130zM221 238v115h-4q-32 0 -48 -14q-18 -16 -18 -47q0 -29 17 -42q15 -12 50 -12h3z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM211 571q0 27 18 45t45 18t45 -18t18 -45t-18 -45t-45 -18t-45 18t-18 45zM48 571q0 27 18 45t45 18t45 -18t18 -45t-18 -45t-45 -18t-45 18t-18 45z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="419" 
d="M190 729v-134h108v-88h-108v-150q14 28 32 46q32 32 74 32q35 0 56 -21q27 -27 27 -111v-361q0 -80 -39 -117q-33 -31 -92 -31q-36 0 -68 11v110q12 -9 28 -9q18 0 29 10q17 15 17 51v287q0 36 -11 48q-7 8 -19 8q-13 0 -21 -8q-13 -12 -13 -59v-243h-125v507h-55v88h55
v134h125z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="285" 
d="M271 343h-106v-343h-125v447h231v-104zM271 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="313" 
d="M283 130v-118q-38 -21 -91 -21q-71 0 -111 40q-61 61 -61 196q0 128 61 187q44 42 114 42q49 0 88 -23v-115q-27 30 -66 30q-34 0 -51 -21q-18 -21 -20 -53h102v-92h-102q2 -39 24 -61q19 -18 51 -18q38 0 62 27z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="315" 
d="M299 417l-50 -91q-32 30 -59 30q-14 0 -23 -9t-9 -23q0 -17 22 -32t48 -26.5t48 -42.5t22 -77q0 -66 -38 -110.5t-103 -44.5q-70 0 -136 51l55 89q34 -28 61 -28q17 0 28.5 10t11.5 27q0 18 -23 33.5t-50 28t-50 44t-23 77.5q0 62 43 97t106 35q66 0 119 -38z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="206" 
d="M184 593q0 -33 -23 -56t-56 -23t-56 23t-23 56t23 56t56 23t56 -23t23 -56zM167 0h-125v447h125v-447z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447zM196 583q0 -27 -11.5 -46.5t-27.5 -19.5t-28 19.5t-12 46.5t12 46.5t28 19.5t27.5 -19.5t11.5 -46.5zM90 583q0 -27 -12 -46.5t-28 -19.5t-28 19.5t-12 46.5t12 46.5t28 19.5t28 -19.5t12 -46.5z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="206" 
d="M184 593q0 -33 -23 -56t-56 -23t-56 23t-23 56t23 56t56 23t56 -23t23 -56zM167 -206h-125v653h125v-653z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="591" 
d="M374 447v-164h33q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-159v343h-62v-134q0 -142 -45 -187q-31 -31 -91 -31q-27 0 -51 8v106q10 -7 25 -7q17 0 28 11q20 20 20 95v243h297zM374 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="573" 
d="M233 447h123v-164h33q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-161v189h-68v-189h-125v447h125v-157h68v157zM356 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -52 15v-107z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="419" 
d="M379 0h-125v254q0 56 -30 56q-34 0 -34 -71v-239h-125v507h-55v88h55v134h125v-134h108v-88h-108v-150q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-303z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="370" 
d="M373 0h-132l-76 204v-204h-125v447h125v-204l66 204h128l-91 -212zM328 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM240 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="398" 
d="M147 0h-107v447h125v-343h68v343h125v-447h-107v-104h-104v104z" />
    <glyph glyph-name="afii10146" unicode="&#x462;" horiz-adv-x="487" 
d="M97 0v498h-77v114h77v84h132v-84h114v-114h-114v-92h7q109 0 165 -54t56 -155q0 -92 -53 -144q-54 -53 -162 -53h-145zM229 292v-178h4q49 0 71 21q23 23 23 66q0 46 -25 69q-24 22 -66 22h-7z" />
    <glyph glyph-name="afii10194" unicode="&#x463;" horiz-adv-x="411" 
d="M69 0v343h-54v104h54v220h125v-220h90v-104h-90v-60h33q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-163zM194 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -52 15v-107z" />
    <glyph glyph-name="afii10147" unicode="&#x472;" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM181 400h180q0 36 -11 75.5t-31 59.5q-21 21 -48 21t-48 -21q-42 -41 -42 -135zM363 283h-184q3 -104 44 -146q20 -21 48 -21t48 21q40 43 44 146z" />
    <glyph glyph-name="afii10195" unicode="&#x473;" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM157 268h78q0 45 -16 68q-9 13 -23 13t-23 -13q-16 -23 -16 -68zM156 184q0 -55 20 -78q8 -9 20 -9t20 9q20 23 20 78h-80z" />
    <glyph glyph-name="afii10148" unicode="&#x474;" horiz-adv-x="486" 
d="M486 667l-185 -667h-108l-193 667h142l71 -258q21 -78 33 -181q9 112 29 181l73 258h138z" />
    <glyph glyph-name="afii10196" unicode="&#x475;" horiz-adv-x="366" 
d="M370 447l-130 -447h-115l-128 447h123q52 -208 61 -253q2 11 7 30.5t6 24.5l46 198h130z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="377" 
d="M52 667h180v125h120v-248h-168v-544h-132v667z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="324" 
d="M165 0h-125v447h166v100h104v-204h-145v-343z" />
    <glyph glyph-name="uni0492" unicode="&#x492;" horiz-adv-x="381" 
d="M351 544h-134v-142h121v-127h-121v-275h-132v275h-65v127h65v265h266v-123z" />
    <glyph glyph-name="uni0493" unicode="&#x493;" horiz-adv-x="311" 
d="M297 343h-106v-83h84v-104h-84v-156h-125v156h-51v104h51v187h231v-104z" />
    <glyph glyph-name="uni0494" unicode="&#x494;" horiz-adv-x="489" 
d="M338 544h-154v-147q45 10 81 10q89 0 138 -47q56 -55 56 -167q0 -109 -55 -162q-42 -42 -107 -42q-46 0 -88 22l29 112q14 -12 33 -12q26 0 41 19q15 20 15 60q0 44 -20 69q-21 26 -61 26q-27 0 -62 -11v-274h-132v667h286v-123z" />
    <glyph glyph-name="uni0495" unicode="&#x495;" horiz-adv-x="385" 
d="M281 343h-116v-83h9q89 0 136 -44q55 -51 55 -164q0 -154 -87 -218q-49 -36 -113 -40v108q24 4 40 20q34 33 34 122q0 72 -28 97q-17 15 -42 15h-4v-156h-125v447h241v-104z" />
    <glyph glyph-name="uni0496" unicode="&#x496;" horiz-adv-x="636" 
d="M7 667h131l107 -302v302h126v-302l107 302h131l-121 -305l75 -237h69v-255h-120v130h-42l-99 335v-335h-126v335l-103 -335h-134l120 362z" />
    <glyph glyph-name="uni0497" unicode="&#x497;" horiz-adv-x="517" 
d="M2 447h128l66 -206v206h117v-206l66 206h128l-94 -213l52 -130h48v-208h-104v104h-30l-66 206v-206h-117v206l-66 -206h-127l94 234z" />
    <glyph glyph-name="uni0498" unicode="&#x498;" horiz-adv-x="419" 
d="M47 496v137q58 44 132 44q87 0 136 -51q48 -49 48 -130q0 -57 -23 -95t-63 -52q42 -12 71 -45q35 -42 35 -102q0 -99 -61 -162q-35 -36 -78 -42v-128h-120v126q-44 6 -79 35v136q22 -26 47 -39q27 -15 59 -15q41 0 63 22q24 23 24 63t-21 61q-25 24 -82 24h-18v118h14
q51 0 74 19q24 20 24 60q0 33 -22 55q-23 20 -56 20q-32 0 -60 -18q-26 -17 -44 -41z" />
    <glyph glyph-name="uni0499" unicode="&#x499;" horiz-adv-x="360" 
d="M262 234q31 -8 50 -31q23 -28 23 -66q0 -62 -37 -101q-26 -28 -69 -37v-103h-104v100q-50 8 -100 47l54 93q14 -16 33 -26q16 -9 42 -9q25 0 38 11q13 12 13 29q0 18 -13 27q-16 11 -55 11h-22v94h19q27 0 42 7q21 10 21 32q0 16 -13 27q-13 10 -36 10q-24 0 -43 -13
q-16 -10 -29 -27l-46 98q26 18 53 31q37 17 86 17q80 0 121 -43q31 -33 31 -84q0 -37 -18 -63q-16 -22 -41 -31z" />
    <glyph glyph-name="uni049A" unicode="&#x49a;" horiz-adv-x="466" 
d="M52 667h132v-298l111 298h135l-123 -308l86 -234h69v-255h-120v130h-39l-119 339v-339h-132v667z" />
    <glyph glyph-name="uni049B" unicode="&#x49b;" horiz-adv-x="379" 
d="M40 447h125v-204l66 204h128l-91 -212l59 -131h48v-208h-104v104h-30l-76 204v-204h-125v447z" />
    <glyph glyph-name="uni049C" unicode="&#x49c;" horiz-adv-x="554" 
d="M184 281v-281h-132v667h132v-261h50v153h76v-192l97 300h135l-114 -309l119 -358h-138l-99 337v-208h-76v152h-50z" />
    <glyph glyph-name="uni049D" unicode="&#x49d;" horiz-adv-x="472" 
d="M165 182v-182h-125v447h125v-182h32v111h70v-132l62 203h130l-84 -215l93 -232h-133l-68 205v-127h-70v104h-32z" />
    <glyph glyph-name="uni04A0" unicode="&#x4a0;" horiz-adv-x="529" 
d="M522 0h-138l-123 339v-339h-132v544h-114v123h246v-298l111 298h135l-123 -308z" />
    <glyph glyph-name="uni04A1" unicode="&#x4a1;" horiz-adv-x="430" 
d="M433 0h-132l-76 204v-204h-125v343h-90v104h215v-204l66 204h128l-91 -212z" />
    <glyph glyph-name="uni04A2" unicode="&#x4a2;" horiz-adv-x="532" 
d="M317 667h132v-542h69v-255h-120v130h-81v295h-133v-295h-132v667h132v-247h133v247z" />
    <glyph glyph-name="uni04A3" unicode="&#x4a3;" horiz-adv-x="414" 
d="M165 447v-157h68v157h125v-343h48v-208h-104v104h-69v186h-68v-186h-125v447h125z" />
    <glyph glyph-name="uni04A4" unicode="&#x4a4;" horiz-adv-x="599" 
d="M569 544h-120v-544h-132v295h-133v-295h-132v667h132v-247h133v247h252v-123z" />
    <glyph glyph-name="uni04A5" unicode="&#x4a5;" horiz-adv-x="466" 
d="M165 447v-157h68v157h219v-104h-94v-343h-125v186h-68v-186h-125v447h125z" />
    <glyph glyph-name="uni04AA" unicode="&#x4aa;" horiz-adv-x="409" 
d="M376 152v-138q-19 -10 -38 -16v-128h-120v132q-60 12 -102 62q-79 93 -79 258q0 189 96 285q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-55 -55 -55 -191q0 -123 47 -175q28 -32 75 -32q44 0 79 32z" />
    <glyph glyph-name="uni04AB" unicode="&#x4ab;" horiz-adv-x="281" 
d="M261 121v-114q-12 -7 -22 -7v-104h-104v106q-29 12 -49 32q-59 59 -59 187q0 59 16.5 110.5t46.5 81.5q43 43 108 43q30 0 63 -13v-110q-14 15 -35 15q-25 0 -42 -17q-31 -29 -31 -104q0 -73 29 -102q17 -17 42 -17q11 0 37 13z" />
    <glyph glyph-name="uni04AE" unicode="&#x4ae;" horiz-adv-x="447" 
d="M447 667l-155 -382v-285h-132v285l-160 382h145l49 -145q18 -54 30 -99q9 37 29 100l46 144h148z" />
    <glyph glyph-name="uni04AF" unicode="&#x4af;" horiz-adv-x="377" 
d="M-3 453h127l36 -129q21 -73 29 -132q8 54 30 134l34 127h127l-131 -414v-245h-120v245z" />
    <glyph glyph-name="uni04B0" unicode="&#x4b0;" horiz-adv-x="447" 
d="M447 667l-155 -382v-15h106v-120h-106v-150h-132v150h-106v120h106v15l-160 382h145l49 -145q18 -54 30 -99q9 37 29 100l46 144h148z" />
    <glyph glyph-name="uni04B1" unicode="&#x4b1;" horiz-adv-x="377" 
d="M-2 453h126l36 -129q21 -73 29 -132q8 54 30 134l34 127h127l-131 -394v-17h108v-88h-108v-160h-120v160h-108v88h108v17z" />
    <glyph glyph-name="uni04B2" unicode="&#x4b2;" horiz-adv-x="453" 
d="M148 667q4 -10 19 -44.5t22.5 -54t16.5 -48t11 -49.5q5 24 14.5 52.5t16.5 46t22 51.5l20 46h142l-142 -323l96 -219h65v-255h-120v130h-43q-6 17 -25 62.5t-30 78.5t-16 65q-12 -57 -69 -206h-148l141 344l-141 323h148z" />
    <glyph glyph-name="uni04B3" unicode="&#x4b3;" horiz-adv-x="390" 
d="M14 447h129l24 -47q15 -28 25 -65q7 30 27 68l23 44h129l-114 -207l75 -136h54v-208h-104v104h-25l-41 78q-16 30 -24 57q-13 -36 -26 -60l-41 -75h-128l128 240z" />
    <glyph glyph-name="uni04B6" unicode="&#x4b6;" horiz-adv-x="514" 
d="M380 0h-81v292q-41 -11 -86 -11q-69 0 -110 29q-36 25 -47 62.5t-11 91.5v203h132v-191q0 -39 16 -55q15 -15 51 -15q31 0 55 9v252h132v-542h69v-255h-120v130z" />
    <glyph glyph-name="uni04B7" unicode="&#x4b7;" horiz-adv-x="403" 
d="M30 447h122v-126q0 -24 10 -34q11 -9 29 -9q14 0 31 6v163h125v-343h48v-208h-104v104h-69v184q-30 -10 -63 -10q-63 0 -95 32q-34 34 -34 106v135z" />
    <glyph glyph-name="uni04B8" unicode="&#x4b8;" horiz-adv-x="543" 
d="M359 0v294q-24 -8 -50 -10v-147h-76v144h-5q-78 0 -119 26q-28 18 -43.5 47.5t-18 52.5t-2.5 57v203h132v-191q0 -40 16 -55q14 -14 40 -14v156h76v-157q26 0 50 11v250h132v-667h-132z" />
    <glyph glyph-name="uni04B9" unicode="&#x4b9;" horiz-adv-x="441" 
d="M246 373v-100q12 0 30 7v167h125v-447h-125v184q-2 0 -13 -3t-17 -4v-91h-58v87q-88 0 -124 36q-34 34 -34 101v137h120v-120q0 -31 10 -41q9 -10 28 -12v99h58z" />
    <glyph glyph-name="uni04BA" unicode="&#x4ba;" horiz-adv-x="478" 
d="M184 667v-272q41 11 86 11q69 0 110 -29q36 -25 47 -62.5t11 -91.5v-223h-132v211q0 39 -16 55q-15 15 -51 15q-31 0 -55 -9v-272h-132v667h132z" />
    <glyph glyph-name="uni04BB" unicode="&#x4bb;" horiz-adv-x="394" 
d="M354 0h-125v274q0 56 -30 56q-13 0 -20.5 -6.5t-10 -21t-3 -21t-0.5 -22.5v-259h-125v729h125v-352q40 78 106 78q27 0 45 -11.5t25.5 -33t10 -40.5t2.5 -47v-323z" />
    <glyph glyph-name="uni04C0" unicode="&#x4c0;" horiz-adv-x="205" 
d="M165 0h-125v667h125v-667z" />
    <glyph glyph-name="uni04C1" unicode="&#x4c1;" horiz-adv-x="616" 
d="M608 0h-134l-103 335v-335h-126v335l-103 -335h-134l120 362l-121 305h131l107 -302v302h126v-302l107 302h131l-121 -305zM374 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="uni04C2" unicode="&#x4c2;" horiz-adv-x="509" 
d="M506 0h-127l-66 206v-206h-117v206l-66 -206h-127l94 234l-95 213h128l66 -206v206h117v-206l66 206h128l-94 -213zM308 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="uni04C3" unicode="&#x4c3;" horiz-adv-x="487" 
d="M52 667h132v-298l111 298h135l-111 -275q67 -8 104 -69q34 -56 34 -154v-62q0 -166 -58 -225q-42 -42 -105 -42q-44 0 -83 22l29 112q14 -12 30 -12q22 0 36 19q19 29 19 123v50q0 71 -24 101q-23 28 -64 28q-24 0 -53 -9v-274h-132v667z" />
    <glyph glyph-name="uni04C4" unicode="&#x4c4;" horiz-adv-x="378" 
d="M40 447h125v-182l66 182h134l-92 -212q45 -17 69 -57q26 -43 26 -127q0 -138 -67 -204q-55 -53 -136 -53v108q23 0 41 17q33 32 33 123q0 73 -28 98q-18 16 -41 16h-5v-156h-125v447z" />
    <glyph glyph-name="uni04C7" unicode="&#x4c7;" horiz-adv-x="501" 
d="M52 667h132v-247h133v247h132v-664q0 -68 -14 -109q-16 -47 -55.5 -73.5t-90.5 -26.5q-59 0 -98 21v132q30 -37 70 -37q16 0 27 7t16.5 15.5t8.5 24.5t3.5 26t0.5 27v285h-133v-295h-132v667z" />
    <glyph glyph-name="uni04C8" unicode="&#x4c8;" horiz-adv-x="398" 
d="M165 447v-157h68v157h125v-505q0 -80 -39 -117q-33 -31 -92 -31q-36 0 -68 11v110q12 -9 28 -9q18 0 29 10q17 15 17 51v219h-68v-186h-125v447h125z" />
    <glyph glyph-name="uni04D0" unicode="&#x4d0;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM292 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="uni04D1" unicode="&#x4d1;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM256 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25
q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="uni04D2" unicode="&#x4d2;" horiz-adv-x="460" 
d="M457 0h-140l-25 112h-133l-24 -112h-132l156 667h138zM179 215h93q-29 130 -38 192q-8 55 -11 88q-6 -94 -44 -280zM373 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM203 790q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04D3" unicode="&#x4d3;" horiz-adv-x="396" 
d="M356 0h-125v51q-33 -60 -83 -60q-36 0 -62 25t-37.5 64.5t-16.5 75t-5 69.5q0 36 4.5 70t16 73t37 63t62.5 24q55 0 84 -63v55h125v-447zM240 223q0 120 -43 120q-44 0 -44 -120t45 -120q42 0 42 120zM352 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM175 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04D4" unicode="&#x4d4;" horiz-adv-x="592" 
d="M585 0h-267l-25 111h-138l-20 -111h-132l152 667h272l28 -124h-132l34 -138h132l30 -126h-130l38 -154h127zM177 216h94l-31 141q-12 52 -19 137q-6 -86 -44 -278z" />
    <glyph glyph-name="uni04D5" unicode="&#x4d5;" horiz-adv-x="570" 
d="M544 192h-205q3 -109 56 -109q40 0 40 54h102q-2 -67 -38 -106.5t-102 -39.5q-68 0 -111 55q-52 -55 -114 -55q-64 0 -104.5 38t-40.5 102q0 61 36.5 99t96.5 38q41 0 66 -16v2q0 25 -2 40t-9 30t-22 21.5t-40 6.5q-38 0 -88 -23v107q60 20 101 20q45 0 74 -14.5
t50 -51.5q38 65 110 65q33 0 58 -12t39.5 -35t24.5 -46.5t14.5 -58.5t5.5 -56.5t2 -54.5zM340 269h93q0 95 -46 95q-47 0 -47 -95zM234 131q0 19 -14 33t-33 14t-33 -14t-14 -33t14 -33t33 -14t33 14t14 33z" />
    <glyph glyph-name="uni04D6" unicode="&#x4d6;" horiz-adv-x="355" 
d="M319 0h-267v667h265v-124h-133v-144h117v-126h-117v-150h135v-123zM244 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="uni04D7" unicode="&#x4d7;" horiz-adv-x="373" 
d="M233 154h106q-2 -80 -48 -128q-35 -35 -97 -35q-67 0 -107 40q-61 61 -61 181q0 137 65 202q41 41 102 41q58 0 96 -36q58 -55 58 -174q0 -21 -3 -44h-198q-1 -7 -1 -25q0 -60 18 -79q10 -12 26 -12q15 0 25 8q16 13 19 61zM148 278h90q0 56 -20 73q-10 9 -23 9
q-16 0 -28 -12q-19 -20 -19 -70zM249 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="uni04D8" unicode="&#x4d8;" horiz-adv-x="524" 
d="M38 371h308q-1 48 -13 98t-36 72q-21 20 -50 20q-25 0 -42 -17q-25 -24 -29 -75l-128 50q17 62 57 102q55 56 144 56q93 0 152 -59q87 -87 87 -282q0 -92 -24 -167t-66 -119q-58 -61 -147 -61q-81 0 -134 55q-80 83 -80 276q0 31 1 51zM168 253v-5q0 -85 32 -118
q20 -21 49 -21q30 0 49 19q19 17 31.5 55t13.5 70h-175z" />
    <glyph glyph-name="afii10846" unicode="&#x4d9;" horiz-adv-x="372" 
d="M135 308l-102 20q8 55 43 90q37 37 102 37q70 0 113 -43q55 -55 55 -175q0 -142 -65 -207q-38 -39 -98 -39q-63 0 -100 36q-57 56 -57 178q0 9 1 28.5t1 22.5h198v4q0 73 -23 92q-10 9 -28 9q-16 0 -26 -10q-14 -16 -14 -43zM224 179h-90q-3 -19 1.5 -44t16.5 -37t27 -12
t25 10q20 19 20 71v12z" />
    <glyph glyph-name="uni04DA" unicode="&#x4da;" horiz-adv-x="524" 
d="M38 371h308q-1 48 -13 98t-36 72q-21 20 -50 20q-25 0 -42 -17q-25 -24 -29 -75l-128 50q17 62 57 102q55 56 144 56q93 0 152 -59q87 -87 87 -282q0 -92 -24 -167t-66 -119q-58 -61 -147 -61q-81 0 -134 55q-80 83 -80 276q0 31 1 51zM168 253v-5q0 -85 32 -118
q20 -21 49 -21q30 0 49 19q19 17 31.5 55t13.5 70h-175zM399 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM229 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5
t19.5 -46.5z" />
    <glyph glyph-name="uni04DB" unicode="&#x4db;" horiz-adv-x="372" 
d="M135 308l-102 20q8 55 43 90q37 37 102 37q70 0 113 -43q55 -55 55 -175q0 -142 -65 -207q-38 -39 -98 -39q-63 0 -100 36q-57 56 -57 178q0 9 1 28.5t1 22.5h198v4q0 73 -23 92q-10 9 -28 9q-16 0 -26 -10q-14 -16 -14 -43zM224 179h-90q-3 -19 1.5 -44t16.5 -37t27 -12
t25 10q20 19 20 71v12zM341 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM164 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04DC" unicode="&#x4dc;" horiz-adv-x="616" 
d="M608 0h-134l-103 335v-335h-126v335l-103 -335h-134l120 362l-121 305h131l107 -302v302h126v-302l107 302h131l-121 -305zM455 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM285 790q0 -27 -19.5 -46.5
t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04DD" unicode="&#x4dd;" horiz-adv-x="509" 
d="M506 0h-127l-66 206v-206h-117v206l-66 -206h-127l94 234l-95 213h128l66 -206v206h117v-206l66 206h128l-94 -213zM404 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM227 583q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04DE" unicode="&#x4de;" horiz-adv-x="419" 
d="M47 496v137q58 44 132 44q87 0 136 -51q48 -49 48 -130q0 -57 -23 -95t-63 -52q43 -12 71 -45q35 -42 35 -106q0 -99 -61 -158q-53 -51 -143 -51q-80 0 -134 42v136q22 -26 47 -39q27 -15 59 -15q41 0 63 22q24 23 24 63t-21 61q-25 24 -82 24h-18v118h14q51 0 74 19
q24 20 24 60q0 33 -22 55q-23 20 -56 20q-32 0 -60 -18q-26 -17 -44 -41zM339 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM169 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5
t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04DF" unicode="&#x4df;" horiz-adv-x="360" 
d="M256 234q34 -11 52 -30q27 -27 27 -69q0 -61 -43 -102q-43 -42 -117 -42q-50 0 -88 15q-27 10 -62 37l54 93q14 -16 33 -26q16 -9 42 -9q25 0 38 11q13 12 13 29q0 18 -13 27q-16 11 -55 11h-22v94h19q27 0 42 7q21 10 21 32q0 16 -13 27q-13 10 -36 10q-24 0 -43 -13
q-16 -10 -29 -27l-46 98q26 18 53 31q37 17 86 17q80 0 121 -43q31 -33 31 -83q0 -37 -19 -62q-17 -22 -46 -33zM331 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM154 583q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04E2" unicode="&#x4e2;" horiz-adv-x="487" 
d="M47 0v667h130v-290q0 -59 -11 -120l152 410h122v-667h-130v292q0 60 9 123l-150 -415h-122zM383 738h-286v80h286v-80z" />
    <glyph glyph-name="uni04E3" unicode="&#x4e3;" horiz-adv-x="405" 
d="M40 447h118v-159q0 -52 -9 -117q13 47 32 94l73 182h111v-447h-118v166q0 44 8 114q-14 -59 -35 -111l-69 -169h-111v447zM346 531h-286v80h286v-80z" />
    <glyph glyph-name="uni04E4" unicode="&#x4e4;" horiz-adv-x="487" 
d="M47 0v667h130v-290q0 -59 -11 -120l152 410h122v-667h-130v292q0 60 9 123l-150 -415h-122zM391 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM221 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04E5" unicode="&#x4e5;" horiz-adv-x="405" 
d="M40 447h118v-159q0 -52 -9 -117q13 47 32 94l73 182h111v-447h-118v166q0 44 8 114q-14 -59 -35 -111l-69 -169h-111v447zM357 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM180 583q0 -27 -19.5 -46.5
t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04E6" unicode="&#x4e6;" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM271 556q-27 0 -48 -22q-46 -46 -46 -198t46 -198q21 -22 48 -22t48 22q46 46 46 198t-46 198q-21 22 -48 22zM420 790q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM250 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04E7" unicode="&#x4e7;" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM196 343q-14 0 -23 -13q-20 -29 -20 -107t20 -107q9 -13 23 -13t23 13q20 29 20 107t-20 107q-9 13 -23 13zM346 583q0 -27 -19.5 -46.5t-46.5 -19.5
t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM169 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04E8" unicode="&#x4e8;" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM181 400h180q0 36 -11 75.5t-31 59.5q-21 21 -48 21t-48 -21q-42 -41 -42 -135zM363 283h-184q3 -104 44 -146q20 -21 48 -21t48 21q40 43 44 146z" />
    <glyph glyph-name="uni04E9" unicode="&#x4e9;" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM157 268h78q0 45 -16 68q-9 13 -23 13t-23 -13q-16 -23 -16 -68zM156 184q0 -55 20 -78q8 -9 20 -9t20 9q20 23 20 78h-80z" />
    <glyph glyph-name="uni04EA" unicode="&#x4ea;" horiz-adv-x="542" 
d="M505 333q0 -191 -82 -277q-63 -67 -152 -67t-152 67q-82 86 -82 277t82 277q63 67 152 67t152 -67q82 -86 82 -277zM181 400h180q0 36 -11 75.5t-31 59.5q-21 21 -48 21t-48 -21q-42 -41 -42 -135zM363 283h-184q3 -104 44 -146q20 -21 48 -21t48 21q40 43 44 146z
M422 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM252 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04EB" unicode="&#x4eb;" horiz-adv-x="392" 
d="M365 223q0 -134 -61 -193q-41 -39 -108 -39t-108 39q-61 59 -61 193t61 193q41 39 108 39t108 -39q61 -59 61 -193zM157 268h78q0 45 -16 68q-9 13 -23 13t-23 -13q-16 -23 -16 -68zM156 184q0 -55 20 -78q8 -9 20 -9t20 9q20 23 20 78h-80zM351 583q0 -27 -19.5 -46.5
t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM174 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04EE" unicode="&#x4ee;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM372 738h-286v80h286v-80z" />
    <glyph glyph-name="uni04EF" unicode="&#x4ef;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM326 531h-286v80h286v-80z" />
    <glyph glyph-name="uni04F0" unicode="&#x4f0;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM380 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM210 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04F1" unicode="&#x4f1;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM339 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM162 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5
t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04F2" unicode="&#x4f2;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM395 846l-85 -122l-57 36l60 128zM258 846l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="uni04F3" unicode="&#x4f3;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM333 639l-85 -122l-57 36l60 128zM196 639l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="uni04F4" unicode="&#x4f4;" horiz-adv-x="483" 
d="M299 0v292q-41 -11 -86 -11q-69 0 -110 29q-36 25 -47 62.5t-11 91.5v203h132v-191q0 -39 16 -55q15 -15 51 -15q31 0 55 9v252h132v-667h-132zM390 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM220 790
q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04F5" unicode="&#x4f5;" horiz-adv-x="387" 
d="M222 0v184q-30 -10 -63 -10q-63 0 -95 32q-34 34 -34 106v135h122v-126q0 -24 10 -34q11 -9 29 -9q14 0 31 6v163h125v-447h-125zM343 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM166 583q0 -27 -19.5 -46.5
t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04F8" unicode="&#x4f8;" horiz-adv-x="631" 
d="M579 0h-132v667h132v-667zM52 667h132v-261h4q107 0 163 -55q57 -55 57 -156q0 -94 -50 -143q-53 -52 -162 -52h-144v667zM184 292v-178h4q51 0 72 20q22 22 22 66q0 45 -25 69q-25 23 -70 23h-3zM460 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5
t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM290 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni04F9" unicode="&#x4f9;" horiz-adv-x="547" 
d="M507 0h-125v447h125v-447zM40 447h125v-164h28q78 0 119 -41q39 -37 39 -103t-39 -103q-38 -36 -114 -36h-158v447zM165 88h6q29 0 43 11q17 13 17 42q0 25 -14 39q-15 15 -50 15h-2v-107zM423 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5
t46.5 19.5t46.5 -19.5t19.5 -46.5zM246 583q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uni1E9E" unicode="&#x1e9e;" horiz-adv-x="440" 
d="M184 507v-507h-132v491q0 90 51 140q47 46 118 46q79 0 124 -42q47 -45 49 -132q0 -22 -1 -31l-72 -91q40 -18 61 -57q22 -39 22 -105q0 -110 -45 -173q-6 -8 -12.5 -15t-12 -12t-12 -9t-11.5 -6.5t-12 -4t-10.5 -2.5t-10 -1.5t-9 -0.5h-8.5h-7q-22 0 -37 5v112
q25 0 37 13q20 21 20 95q0 52 -15 75q-13 18 -42 20v88l51 71q1 4 1 23q0 37 -8 52q-11 19 -34 19q-22 0 -33 -16q-10 -15 -10 -45z" />
    <glyph glyph-name="uni2010" unicode="&#x2010;" horiz-adv-x="268" 
d="M218 191h-168v108h168v-108z" />
    <glyph glyph-name="uni2011" unicode="&#x2011;" horiz-adv-x="268" 
d="M218 191h-168v108h168v-108z" />
    <glyph glyph-name="figuredash" unicode="&#x2012;" horiz-adv-x="574" 
d="M504 288h-434v100h434v-100z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="574" 
d="M504 195h-434v100h434v-100z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="820" 
d="M750 295v-100h-680v100h680z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="820" 
d="M750 295v-100h-680v100h680z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="274" 
d="M30 443l133 270h88l-88 -270h-133z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="274" 
d="M251 713l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="250" 
d="M228 121l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="441" 
d="M418 713l-88 -270h-133l133 270h88zM251 713l-88 -270h-133l133 270h88z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="441" 
d="M418 713l-133 -270h-88l88 270h133zM251 713l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="441" 
d="M244 121l-133 -270h-88l88 270h133zM417 121l-133 -270h-88l88 270h133z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="433" 
d="M390 439h-111v-632h-125v632h-111v117h111v173h125v-173h111v-117z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="433" 
d="M390 100h-111v-293h-125v293h-111v117h111v222h-111v117h111v173h125v-173h111v-117h-111v-222h111v-117z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="1000" 
d="M638 340q0 -57 -40 -98.5t-97 -41.5t-97.5 41.5t-40.5 98.5t40.5 98.5t97.5 41.5t97 -41.5t40 -98.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="750" 
d="M703 79q0 -32 -23 -55.5t-55 -23.5t-55.5 23.5t-23.5 55.5t23.5 55t55.5 23t55 -23t23 -55zM453 79q0 -32 -23 -55.5t-55 -23.5t-55.5 23.5t-23.5 55.5t23.5 55t55.5 23t55 -23t23 -55zM203 79q0 -32 -23 -55.5t-55 -23.5t-55.5 23.5t-23.5 55.5t23.5 55t55.5 23t55 -23
t23 -55z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1053" 
d="M556 667l-289 -667h-87l291 667h85zM303 504q0 -44 -10.5 -80.5t-42.5 -65.5t-82 -29q-38 0 -65.5 16t-42 43t-21 55.5t-6.5 58.5q0 40 10.5 75.5t42.5 64.5t82 29q38 0 66 -16t42 -42.5t20.5 -53.5t6.5 -55zM202 505q0 76 -32 76q-31 0 -31 -76q0 -85 31 -85q32 0 32 85
zM597 169q0 76 -32 76q-31 0 -31 -76q0 -85 31 -85q32 0 32 85zM698 168q0 -44 -10.5 -80.5t-42.5 -65.5t-82 -29q-38 0 -65.5 16t-42 43t-21 55.5t-6.5 58.5q0 40 10.5 75.5t42.5 64.5t82 29q38 0 66 -16t42 -42.5t20.5 -53.5t6.5 -55zM919 169q0 76 -32 76q-31 0 -31 -76
q0 -85 31 -85q32 0 32 85zM1020 168q0 -44 -10.5 -80.5t-42.5 -65.5t-82 -29q-38 0 -65.5 16t-42 43t-21 55.5t-6.5 58.5q0 40 10.5 75.5t42.5 64.5t82 29q38 0 66 -16t42 -42.5t20.5 -53.5t6.5 -55z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="243" 
d="M213 66l-68 -38l-115 206l115 206l68 -34l-91 -172z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="243" 
d="M213 234l-116 -206l-67 38l91 168l-91 172l67 34z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="146" 
d="M264 667l-318 -667h-77l318 667h77z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="433" 
d="M407 152v-138q-38 -21 -91 -21q-98 0 -158 60q-69 68 -86 195h-83l29 68h52v55h-81l29 68h61q18 99 85 168q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-28 -28 -41 -79h193l-29 -68h-177q-1 -9 -1 -27q0 -19 1 -28h157l-29 -68h-123q12 -63 41 -96
q28 -32 75 -32q44 0 79 32z" />
    <glyph glyph-name="uni20B4" unicode="&#x20b4;" horiz-adv-x="433" 
d="M92 516v129q32 14 54 20q39 11 85 11q70 0 109 -33q43 -37 43 -102q0 -60 -34 -102h70v-68h-105l-46 -55h151v-68h-198q-34 -39 -34 -77q0 -31 22 -46q18 -12 51 -12q29 0 58 11q24 10 45 25v-126q-9 -9 -57 -22q-38 -11 -77 -11q-74 0 -121 37q-50 39 -50 114
q0 61 35 107h-68v68h109l46 55h-155v68h200q28 27 28 59q0 29 -17 45t-48 16q-28 0 -56 -15q-26 -14 -40 -28z" />
    <glyph glyph-name="uni20B6" unicode="&#x20b6;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20cf;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20ce;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20cd;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20cc;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20cb;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20ca;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c9;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c8;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c7;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c6;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c5;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c4;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c3;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c2;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c1;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20c0;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20bf;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20be;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20bd;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20bc;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20bb;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20ba;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20b9;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B6" unicode="&#x20b7;" horiz-adv-x="433" 
d="M227 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM199 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B8" unicode="&#x20b8;" horiz-adv-x="433" 
d="M386 415h-104v-415h-132v415h-103v112h339v-112zM386 575h-339v92h339v-92z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="797" 
d="M465 0h-124l-164 402q7 -35 7 -76v-326h-132v667h125l164 -403q-10 50 -10 90v313h134v-667zM761 520q0 -68 -30 -112.5t-95 -44.5q-64 0 -95.5 45.5t-31.5 111.5t31.5 111.5t94.5 45.5q66 0 96 -44t30 -113zM750 227h-223v80h223v-80zM662 520q0 74 -27 74
q-28 0 -28 -74t28 -74q27 0 27 74z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="775" 
d="M734 333q0 -143 -101 -245q-101 -103 -246 -103t-246 103q-101 102 -101 245t101 245q101 103 246 103t246 -103q101 -102 101 -245zM387 621q-110 0 -189 -76q-90 -87 -90 -211q0 -125 90 -212q79 -76 189 -76t189 76q90 87 90 212q0 124 -90 211q-79 76 -189 76z
M291 535h98q73 0 105 -31q31 -31 31 -87q0 -57 -30 -85q-30 -30 -95 -30h-19v-157h-90v390zM381 370h6q29 0 40 12q13 13 13 38t-11 36t-34 11h-14v-97z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="602" 
d="M592 287h-89l-17 230l-45 -230h-68l-49 225l-11 -225h-93l36 380h112l37 -203l30 203h114zM211 591h-64v-304h-87v304h-63v76h214v-76z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M57 556l16 109q185 -20 285 -106q127 -109 127 -292q0 -119 -63.5 -197.5t-180.5 -78.5q-96 0 -163.5 66t-67.5 162q0 89 59 151t148 62q76 0 124 -47q-71 153 -284 171zM350 217q0 47 -30 79t-77 32q-45 0 -74.5 -33t-29.5 -78q0 -47 29 -80.5t76 -33.5q46 0 76 33.5
t30 80.5z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="730" 
d="M691 -250h-289v112h80v776h-234v-776h79v-112h-287v112h83v776h-83v112h651v-112h-82v-776h82v-112z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="667" 
d="M627 -250h-606v125l244 392l-244 358v125h597v-245h-112l-13 133h-331l250 -371l-250 -405h340l8 136h117v-248z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M467 282h-434v115h434v-115z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="472" 
d="M449 729l-367 -922h-112l370 922h109z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="284" 
d="M243 334q0 -42 -29.5 -71.5t-71.5 -29.5t-71.5 29.5t-29.5 71.5t29.5 71.5t71.5 29.5t71.5 -29.5t29.5 -71.5z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="667" 
d="M667 714h-180l-170 -714h-165l-59 273h-93v112h197l19 -88q11 -54 15 -145h10q10 138 47 295l90 380h289v-113z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="667" 
d="M650 340q0 -71 -45.5 -117t-115.5 -46q-85 0 -153 81q-71 -81 -157 -81q-71 0 -116.5 46t-45.5 117q0 70 45.5 115.5t115.5 45.5q83 0 153 -80q66 80 156 80q71 0 117 -45t46 -116zM521 340q0 20 -16 34.5t-37 14.5q-39 0 -73 -50q37 -50 73 -50q22 0 37.5 14.5
t15.5 36.5zM272 339q-37 50 -73 50q-21 0 -37 -14.5t-16 -35.5t16 -35.5t37 -14.5q38 0 73 50z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M449 740l-23 -112q-33 11 -55 11q-57 0 -57 -71v-550q0 -10 0.5 -29.5t0.5 -28.5q0 -106 -36 -152q-45 -58 -137 -58q-41 0 -91 10l22 112q33 -11 56 -11q20 0 32 7t17 22t6.5 27.5t1.5 32.5q0 99 -0.5 297t-0.5 298q0 65 11 100q16 53 60 79t103 26q40 0 90 -10z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M422 524l71 -41q-29 -63 -64 -97.5t-94 -34.5q-33 0 -93 25.5t-92 25.5q-28 0 -43.5 -15.5t-30.5 -46.5l-69 45q27 66 59 101.5t92 35.5q35 0 96.5 -25t93.5 -25q28 0 43 12t31 40zM422 324l71 -41q-29 -63 -64 -97.5t-94 -34.5q-33 0 -93 25.5t-92 25.5
q-28 0 -43.5 -15.5t-30.5 -46.5l-69 45q27 67 59 102t91 35q34 0 96.5 -25t94.5 -25q28 0 43 12t31 40z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M467 176h-241l-49 -98h-99l48 98h-92v112h143l35 69h-178v112h229l55 108h98l-54 -108h105v-112h-155l-35 -69h190v-112z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M467 145l-434 207v105l434 207v-117l-302 -143l301 -142zM467 5h-434v112h434v-112z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M467 352l-434 -207v117l302 143l-301 142l-1 117l434 -207v-105zM467 5h-434v112h434v-112z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="284" 
d="M243 334q0 -42 -29.5 -71.5t-71.5 -29.5t-71.5 29.5t-29.5 71.5t29.5 71.5t71.5 29.5t71.5 -29.5t29.5 -71.5z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="667" 
d="M593 334l-260 -360l-259 360l259 359zM447 334l-114 156l-113 -156l113 -156z" />
    <glyph glyph-name="openbullet" unicode="&#x25e6;" 
d="M403 511q0 -63 -45 -107t-108 -44t-108 44t-45 107t45 106.5t108 43.5t108 -43.5t45 -106.5zM328 510q0 32 -23 54.5t-55 22.5t-55 -22t-23 -54t23 -55t55 -23t55 23t23 54z" />
    <glyph glyph-name="uniF400" unicode="&#xf400;" horiz-adv-x="2" 
d="M-79 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="uniF401" unicode="&#xf401;" horiz-adv-x="2" 
d="M-104 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="uniF406" unicode="&#xf406;" 
d="M314 838h66q-4 -54 -38 -88q-32 -32 -92 -32t-92 32q-33 33 -38 88h66q3 -18 16 -29q15 -13 48 -13t48 13q13 11 16 29z" />
    <glyph glyph-name="uniF407" unicode="&#xf407;" 
d="M335 -80l-100 -125l-61 35l76 128z" />
    <glyph glyph-name="uniF408" unicode="&#xf408;" 
d="M378 779l-54 -55l-75 62l-78 -62l-49 62l127 100z" />
    <glyph glyph-name="uniF409" unicode="&#xf409;" horiz-adv-x="221" 
d="M198 -46l-84 -168h-70l51 168h103z" />
    <glyph glyph-name="uniF40A" unicode="&#xf40a;" horiz-adv-x="216" 
d="M193 667l-85 -198h-70l51 198h104z" />
    <glyph glyph-name="uniF40B" unicode="&#xf40b;" 
d="M317 790q0 -27 -20 -46.5t-47 -19.5t-46.5 19.5t-19.5 46.5q0 28 19 47.5t47 19.5t47.5 -19.5t19.5 -47.5z" />
    <glyph glyph-name="uniF40C" unicode="&#xf40c;" 
d="M154 810q0 40 28 68t68 28t68 -28t28 -68t-28 -68t-68 -28t-68 28t-28 68zM209 810q0 -18 11.5 -29.5t29.5 -11.5t29.5 11.5t11.5 29.5t-11.5 29.5t-29.5 11.5t-29.5 -11.5t-11.5 -29.5z" />
    <glyph glyph-name="uniF40D" unicode="&#xf40d;" 
d="M328 846l66 -4q-4 -24 -8.5 -40.5t-13.5 -36.5t-25.5 -30.5t-39.5 -10.5q-21 0 -57 15.5t-51 15.5q-23 0 -28 -28h-65q4 35 9.5 56.5t24.5 41.5t51 20q22 0 55 -17t50 -17q10 0 16 3.5t8 6.5t4.5 13t3.5 12z" />
    <glyph glyph-name="uniF40E" unicode="&#xf40e;" 
d="M324 -97l-14 -71q-18 -17 -47 -17q-34 0 -60.5 22t-26.5 55q0 59 74 108h59q-56 -38 -56 -74q0 -16 11.5 -28t28.5 -12t31 17z" />
    <glyph glyph-name="uniF4C6" unicode="&#xf4c6;" horiz-adv-x="483" 
d="M299 667h132v-720q0 -13 -14 -53q-4 -11 -10 -29.5t-7 -23.5t-7.5 -16.5t-8 -13.5t-11.5 -7.5t-15 -5.5t-21.5 -2t-28 -2h-37.5h-23.5t-18 1.5t-13 2t-11.5 3t-9 3.5l-10.5 5l-12.5 6v132q2 -2 7.5 -9t7 -8.5t7 -6t9 -6l9.5 -4t13.5 -3t16.5 -0.5h57q0 5 -0.5 14.5
t-0.5 14.5v353q-41 -11 -86 -11q-69 0 -110 29q-36 25 -47 62.5t-11 91.5v203h132v-191q0 -39 16 -55q15 -15 51 -15q31 0 55 9v252z" />
    <glyph glyph-name="uniF4C7" unicode="&#xf4c7;" horiz-adv-x="387" 
d="M30 447h122v-126q0 -24 10 -34q11 -9 29 -9q14 0 31 6v163h125v-505q0 -80 -39 -117q-33 -31 -92 -31q-36 0 -68 11v110q12 -9 28 -9q18 0 29 10q17 15 17 51v217q-30 -10 -63 -10q-63 0 -95 32q-34 34 -34 106v135z" />
    <glyph glyph-name="uniF4CC" unicode="&#xf4cc;" horiz-adv-x="457" 
d="M8 667h142l52 -162q19 -57 28 -115q19 88 29 118l50 159h140l-232 -667h-132l76 230zM357 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="uniF4CD" unicode="&#xf4cd;" horiz-adv-x="366" 
d="M369 447l-188 -653h-123l69 215l-130 438h125l43 -163q16 -59 20 -92l59 255h125zM311 639l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="dollar.fitted" unicode="&#xf5f5;" horiz-adv-x="428" 
d="M389 599l-73 -79q-47 50 -101 50q-34 0 -54.5 -17.5t-20.5 -51.5t17 -51t52 -32l46 -20t41 -19.5t40 -24l30.5 -27.5t27.5 -35t14.5 -41.5t6.5 -52.5q0 -77 -45 -135.5t-118 -73.5v-80h-72v80q-53 9 -90.5 26t-74.5 52l62 98q70 -66 130 -66q38 0 61 23t23 61
q0 36 -22.5 55.5t-72.5 42.5q-47 22 -70 35t-54 38.5t-44.5 58.5t-13.5 78q0 78 44.5 125t121.5 61v74h72v-74q45 -9 76.5 -26t60.5 -52z" />
    <glyph glyph-name="Euro.fitted" unicode="&#xf5f6;" horiz-adv-x="461" 
d="M427 152v-138q-38 -21 -91 -21q-98 0 -158 60q-69 68 -86 195h-83l29 68h52v55h-81l29 68h61q18 99 85 168q67 67 160 67q42 0 83 -19v-136q-32 29 -73 29q-43 0 -73 -30q-28 -28 -41 -79h193l-29 -68h-177q-1 -9 -1 -27q0 -19 1 -28h157l-29 -68h-123q12 -63 41 -96
q28 -32 75 -32q44 0 79 32z" />
    <glyph glyph-name="cent.fitted" unicode="&#xf5f7;" horiz-adv-x="263" 
d="M243 228v-112q-18 -8 -48 -17v-76h-72v80q-112 41 -112 215q0 83 25.5 152t86.5 91v92h72v-86q16 0 48 -13v-112q-19 13 -33 13q-26 0 -43 -24t-22.5 -51t-5.5 -53q0 -42 15 -76t52 -34q20 0 37 11z" />
    <glyph glyph-name="sterling.fitted" unicode="&#xf5f8;" horiz-adv-x="465" 
d="M428 514l-112 -33q-3 53 -15 69q-13 19 -40 19q-28 0 -44 -18q-18 -20 -18 -66q0 -38 17 -117h112v-80h-102q7 -30 7 -49q0 -59 -41 -108q28 0 69 -23q30 -16 59 -16q36 0 56 35l68 -55q-13 -38 -45 -62q-28 -21 -73 -21q-44 0 -91 18q-53 21 -88 21q-38 0 -76 -28
l-53 72q39 30 78 42q18 37 18 86q0 51 -15 88h-75v80h60q-17 58 -17 117q0 88 51 140t142 52q80 0 123 -47q40 -45 45 -116z" />
    <glyph glyph-name="yen.fitted" unicode="&#xf5f9;" horiz-adv-x="476" 
d="M461 667l-101 -228h81v-68h-111l-24 -55h135v-68h-135v-248h-132v248h-140v68h140l-24 55h-116v68h84l-104 228h145l67 -175q8 -20 12 -39q16 51 76 214h147z" />
    <glyph glyph-name="florin.fitted" unicode="&#xf5fa;" horiz-adv-x="427" 
d="M420 728l-23 -118q-12 6 -25 6q-7 0 -13 -2t-10.5 -7t-7.5 -9.5t-5.5 -13t-3.5 -12.5t-2.5 -14t-2.5 -13l-19 -98h70l-13 -68h-70l-53 -262l-8 -40.5t-9 -44.5t-10.5 -40.5t-14.5 -40t-20 -32.5t-28 -28.5t-36.5 -16.5t-47.5 -7q-25 0 -61 9l23 118q14 -7 27 -7
q7 0 12.5 2t10 6.5t7.5 8t5.5 11.5t4 11.5t4 12.5t2.5 11q5 20 16 76l50 253h-66l13 68h66l26 128q16 89 46 121q41 44 106 44q28 0 60 -12z" />
    <glyph glyph-name="uni20B4.fitted" unicode="&#xf5fb;" horiz-adv-x="435" 
d="M87 516v129q32 14 54 20q38 11 85 11q70 0 109 -33q43 -37 43 -102q0 -60 -34 -102h70v-68h-105l-46 -55h151v-68h-198q-34 -39 -34 -77q0 -31 22 -46q18 -12 51 -12q29 0 58 11q24 10 45 25v-126q-9 -9 -57 -22q-38 -11 -77 -11q-74 0 -121 37q-50 39 -50 114
q0 61 35 107h-68v68h109l46 55h-155v68h200q28 27 28 59q0 29 -17 45t-48 16q-27 0 -56 -15q-25 -14 -40 -28z" />
    <glyph glyph-name="uni20B6.fitted" unicode="&#xf5fc;" horiz-adv-x="460" 
d="M245 667q73 0 112 -22q44 -25 67.5 -73t23.5 -103q0 -98 -56.5 -142.5t-174.5 -44.5v-55h110v-68h-110v-159h-132v159h-64v68h64v55h-64v90h64v295h160zM217 372h8q47 0 69.5 22t22.5 76q0 90 -77 90h-23v-188z" />
    <glyph glyph-name="uni20B8.fitted" unicode="&#xf5fd;" horiz-adv-x="371" 
d="M355 415h-104v-415h-132v415h-103v112h339v-112zM355 575h-339v92h339v-92z" />
    <glyph glyph-name="uniF639" unicode="&#xf639;" horiz-adv-x="418" 
d="M396 336q0 -55 -7 -107t-25.5 -110.5t-58.5 -94t-98 -35.5q-46 0 -81 25.5t-54 62.5t-31 87t-15.5 91t-3.5 83q0 54 7.5 105.5t26.5 108t59 91t97 34.5q55 0 94 -35.5t57 -92.5t25.5 -108.5t7.5 -104.5zM268 300v60q0 199 -60 199q-59 0 -59 -194v-70q0 -23 1.5 -46.5
t6.5 -58.5t18.5 -57t32.5 -22q60 0 60 189z" />
    <glyph glyph-name="uniF63A" unicode="&#xf63a;" horiz-adv-x="409" 
d="M153 435h-121q-2 24 -2 36q0 101 38.5 153.5t136.5 52.5q84 0 131.5 -43t47.5 -127q0 -13 -1 -25.5t-4 -25.5t-5 -22.5t-7.5 -23.5t-8 -21t-10.5 -22.5l-10.5 -20.5l-12 -23l-11.5 -22l-97 -186h155v-115h-350l155 285q81 149 81 216q0 33 -11.5 58.5t-40.5 25.5
q-54 0 -54 -109q0 -27 1 -41z" />
    <glyph glyph-name="uniF63B" unicode="&#xf63b;" horiz-adv-x="413" 
d="M47 479v7q0 72 18 113q35 78 139 78q79 0 123.5 -46.5t44.5 -126.5q0 -122 -83 -159q103 -33 103 -160q0 -91 -52.5 -143.5t-143.5 -52.5q-86 0 -131.5 49.5t-45.5 136.5h124q0 -3 -0.5 -9.5t-0.5 -9.5q0 -26 13 -44.5t38 -18.5q56 0 56 104q0 58 -20.5 77t-79.5 19v112
q56 0 79 17t23 71q0 75 -46 75q-44 0 -44 -89h-114z" />
    <glyph glyph-name="uniF63C" unicode="&#xf63c;" horiz-adv-x="402" 
d="M383 157h-54v-157h-128v157h-193v103l177 407h144v-396h54v-114zM201 271v244l-98 -244h98z" />
    <glyph glyph-name="uniF63D" unicode="&#xf63d;" horiz-adv-x="391" 
d="M186 553l-18 -109q61 -6 94 -21q106 -48 106 -199q0 -110 -56 -172.5t-164 -62.5q-61 0 -128 31l22 115q40 -32 82 -32q50 0 76.5 35.5t26.5 86.5q0 60 -36 89t-97 29q-24 0 -52 -5l52 329h255v-114h-163z" />
    <glyph glyph-name="uniF63E" unicode="&#xf63e;" horiz-adv-x="403" 
d="M294 667l-126 -263q39 37 83 37q37 0 65 -20t42.5 -52.5t21.5 -66.5t7 -68q0 -46 -10 -87.5t-30.5 -78t-57 -58t-85.5 -21.5q-95 0 -140.5 67.5t-45.5 167.5q0 39 7.5 81.5t15 71t28.5 78.5l29 68t36.5 76l33.5 68h126zM258 220q0 15 -2 30.5t-7 33t-16.5 29t-26.5 11.5
q-22 0 -36 -22t-18 -45.5t-4 -46.5q0 -20 4 -42t17 -43.5t32 -21.5q57 0 57 117z" />
    <glyph glyph-name="uniF63F" unicode="&#xf63f;" horiz-adv-x="417" 
d="M411 667l-250 -667h-137l207 552h-179v115h359z" />
    <glyph glyph-name="uniF640" unicode="&#xf640;" horiz-adv-x="423" 
d="M403 184q0 -84 -54 -139.5t-137 -55.5t-137 55.5t-54 139.5q0 80 38 126q22 27 77 49q-91 39 -91 142q0 74 46 125t120 51t120 -50.5t46 -125.5q0 -104 -90 -142q54 -22 77 -49q39 -46 39 -126zM247 502q0 71 -38 71t-38 -70q0 -76 38 -76t38 75zM267 199
q0 33 -13.5 64.5t-42.5 31.5q-21 0 -34.5 -17.5t-17.5 -37.5t-4 -41t4 -41t17.5 -37.5t34.5 -17.5q29 0 42.5 31.5t13.5 64.5z" />
    <glyph glyph-name="uniF641" unicode="&#xf641;" horiz-adv-x="403" 
d="M108 0l127 262q-42 -37 -83 -37q-37 0 -65 20t-42.5 53t-21.5 67t-7 68q0 46 10 87.5t30.5 77.5t57.5 57.5t86 21.5q95 0 140 -67t45 -167q0 -130 -71 -279l-10 -20l-10 -20l-59 -124h-127zM254 457q0 14 -2.5 30t-7.5 34t-16 30t-26 12q-56 0 -56 -116q0 -22 3 -43
t15.5 -41.5t33.5 -20.5q30 0 43 38.5t13 76.5z" />
    <glyph glyph-name="uniF6BA" unicode="&#xf6ba;" horiz-adv-x="268" 
d="M218 284h-168v108h168v-108z" />
    <glyph glyph-name="uniF6BB" unicode="&#xf6bb;" horiz-adv-x="574" 
d="M504 288h-434v100h434v-100z" />
    <glyph glyph-name="uniF6BC" unicode="&#xf6bc;" horiz-adv-x="820" 
d="M750 388v-100h-680v100h680z" />
    <glyph glyph-name="uniF6C3" unicode="&#xf6c3;" horiz-adv-x="221" 
d="M198 -46l-92 -182h-70l59 182h103z" />
    <glyph glyph-name="uniF6C9" unicode="&#xf6c9;" 
d="M378 846l-115 -122l-60 36l85 128z" />
    <glyph glyph-name="uniF6CA" unicode="&#xf6ca;" 
d="M378 824l-127 -100l-129 107l55 55l74 -62l78 62z" />
    <glyph glyph-name="uniF6CB" unicode="&#xf6cb;" horiz-adv-x="468" 
d="M385 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5zM215 790q0 -27 -19.5 -46.5t-46.5 -19.5t-46.5 19.5t-19.5 46.5t19.5 46.5t46.5 19.5t46.5 -19.5t19.5 -46.5z" />
    <glyph glyph-name="uniF6CE" unicode="&#xf6ce;" 
d="M298 760l-61 -36l-114 122l90 42z" />
    <glyph glyph-name="uniF6CF" unicode="&#xf6cf;" 
d="M423 846l-85 -122l-57 36l60 128zM286 846l-86 -122l-57 36l60 128z" />
    <glyph glyph-name="uniF6D0" unicode="&#xf6d0;" 
d="M393 738h-286v80h286v-80z" />
    <glyph glyph-name="uniF6D1" unicode="&#xf6d1;" horiz-adv-x="424" 
d="M278 852l99 -32q-14 -55 -58 -85q-41 -27 -107 -27t-107 27q-44 30 -58 85l99 32q6 -28 22 -41q16 -14 44 -14t44 14q16 13 22 41z" />
    <glyph glyph-name="uniF6D4" unicode="&#xf6d4;" horiz-adv-x="380" 
d="M244 642l89 -29q-11 -49 -51 -78q-35 -25 -92 -25t-92 25q-40 29 -51 78l89 29q2 -27 17 -40q12 -12 37 -12t37 12q15 13 17 40z" />
    <glyph glyph-name="uniF6DC" unicode="&#xf6dc;" horiz-adv-x="238" 
d="M207 0h-128v551h-64v116h192v-667z" />
    <glyph glyph-name="uniF6DE" unicode="&#xf6de;" horiz-adv-x="615" 
d="M562 295v-100h-510v100h510z" />
    <glyph glyph-name="nbspace" horiz-adv-x="233" 
 />
    <glyph glyph-name="hyphenminus" horiz-adv-x="268" 
d="M218 191h-168v108h168v-108z" />
    <glyph glyph-name="dotlessi1" horiz-adv-x="206" 
d="M165 0h-125v447h125v-447z" />
    <hkern u1="&#x2c;" u2="&#x31;" k="37" />
    <hkern u1="&#x2d;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2d;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2d;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2d;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2d;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2d;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2d;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2d;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2d;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2d;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2d;" u2="&#x31;" k="21" />
    <hkern u1="&#x2e;" u2="&#x31;" k="37" />
    <hkern u1="&#x30;" u2="&#x31;" k="41" />
    <hkern u1="&#x31;" u2="&#x39;" k="44" />
    <hkern u1="&#x31;" u2="&#x38;" k="34" />
    <hkern u1="&#x31;" u2="&#x37;" k="43" />
    <hkern u1="&#x31;" u2="&#x36;" k="39" />
    <hkern u1="&#x31;" u2="&#x35;" k="47" />
    <hkern u1="&#x31;" u2="&#x34;" k="47" />
    <hkern u1="&#x31;" u2="&#x33;" k="44" />
    <hkern u1="&#x31;" u2="&#x32;" k="48" />
    <hkern u1="&#x31;" u2="&#x31;" k="77" />
    <hkern u1="&#x31;" u2="&#x30;" k="31" />
    <hkern u1="&#x31;" u2="&#x2e;" k="30" />
    <hkern u1="&#x31;" u2="&#x2c;" k="31" />
    <hkern u1="&#x32;" u2="&#x31;" k="34" />
    <hkern u1="&#x33;" u2="&#x31;" k="41" />
    <hkern u1="&#x34;" u2="&#x31;" k="46" />
    <hkern u1="&#x35;" u2="&#x37;" k="32" />
    <hkern u1="&#x35;" u2="&#x31;" k="45" />
    <hkern u1="&#x36;" u2="&#x37;" k="21" />
    <hkern u1="&#x36;" u2="&#x31;" k="58" />
    <hkern u1="&#x37;" u2="&#x3a;" k="33" />
    <hkern u1="&#x37;" u2="&#x38;" k="31" />
    <hkern u1="&#x37;" u2="&#x37;" k="32" />
    <hkern u1="&#x37;" u2="&#x36;" k="49" />
    <hkern u1="&#x37;" u2="&#x35;" k="42" />
    <hkern u1="&#x37;" u2="&#x34;" k="59" />
    <hkern u1="&#x37;" u2="&#x33;" k="42" />
    <hkern u1="&#x37;" u2="&#x32;" k="42" />
    <hkern u1="&#x37;" u2="&#x31;" k="26" />
    <hkern u1="&#x37;" u2="&#x2e;" k="79" />
    <hkern u1="&#x37;" u2="&#x2c;" k="80" />
    <hkern u1="&#x38;" u2="&#x31;" k="42" />
    <hkern u1="&#x39;" u2="&#x31;" k="36" />
    <hkern u1="A" g2="hyphenminus" k="-73" />
    <hkern u1="A" u2="&#x2dd;" k="11" />
    <hkern u1="A" u2="&#xe6;" k="11" />
    <hkern u1="A" u2="&#xb8;" k="-33" />
    <hkern u1="A" u2="v" k="4" />
    <hkern u1="A" u2="Q" k="11" />
    <hkern u1="A" u2="J" k="-21" />
    <hkern u1="A" u2="&#x2e;" k="-42" />
    <hkern u1="B" u2="&#x2122;" k="-52" />
    <hkern u1="B" u2="&#x1fc;" k="1" />
    <hkern u1="B" u2="&#x178;" k="17" />
    <hkern u1="B" u2="&#x104;" k="1" />
    <hkern u1="B" u2="&#x102;" k="1" />
    <hkern u1="B" u2="&#x100;" k="1" />
    <hkern u1="B" u2="&#xdd;" k="17" />
    <hkern u1="B" u2="&#xc6;" k="1" />
    <hkern u1="B" u2="&#xc5;" k="1" />
    <hkern u1="B" u2="&#xc4;" k="1" />
    <hkern u1="B" u2="&#xc3;" k="1" />
    <hkern u1="B" u2="&#xc2;" k="1" />
    <hkern u1="B" u2="&#xc1;" k="1" />
    <hkern u1="B" u2="&#xc0;" k="1" />
    <hkern u1="B" u2="&#xb8;" k="1" />
    <hkern u1="B" u2="Y" k="17" />
    <hkern u1="B" u2="A" k="1" />
    <hkern u1="C" u2="&#x2122;" k="-73" />
    <hkern u1="C" u2="&#x201d;" k="-52" />
    <hkern u1="C" u2="&#x2dd;" k="13" />
    <hkern u1="C" u2="&#xe6;" k="13" />
    <hkern u1="C" u2="&#xb8;" k="-18" />
    <hkern u1="C" u2="Q" k="31" />
    <hkern u1="D" u2="&#x2122;" k="-42" />
    <hkern u1="D" u2="&#xb8;" k="13" />
    <hkern u1="E" u2="&#x2122;" k="-63" />
    <hkern u1="F" g2="hyphenminus" k="-63" />
    <hkern u1="F" u2="&#xf6bc;" k="-29" />
    <hkern u1="F" u2="&#xf6bb;" k="-29" />
    <hkern u1="F" u2="&#xf6ba;" k="-29" />
    <hkern u1="F" u2="&#x2122;" k="-73" />
    <hkern u1="F" u2="&#x2014;" k="-29" />
    <hkern u1="F" u2="&#x2013;" k="-29" />
    <hkern u1="F" u2="&#x2012;" k="-29" />
    <hkern u1="F" u2="&#x2011;" k="-29" />
    <hkern u1="F" u2="&#x2010;" k="-29" />
    <hkern u1="F" u2="&#x259;" k="5" />
    <hkern u1="F" u2="&#x1fd;" k="5" />
    <hkern u1="F" u2="&#x1fc;" k="17" />
    <hkern u1="F" u2="&#x1dd;" k="5" />
    <hkern u1="F" u2="&#x1d0;" k="-20" />
    <hkern u1="F" u2="&#x173;" k="11" />
    <hkern u1="F" u2="&#x171;" k="11" />
    <hkern u1="F" u2="&#x16f;" k="11" />
    <hkern u1="F" u2="&#x16d;" k="11" />
    <hkern u1="F" u2="&#x16b;" k="11" />
    <hkern u1="F" u2="&#x169;" k="11" />
    <hkern u1="F" u2="&#x159;" k="11" />
    <hkern u1="F" u2="&#x157;" k="11" />
    <hkern u1="F" u2="&#x155;" k="11" />
    <hkern u1="F" u2="&#x153;" k="5" />
    <hkern u1="F" u2="&#x151;" k="5" />
    <hkern u1="F" u2="&#x14f;" k="5" />
    <hkern u1="F" u2="&#x14d;" k="5" />
    <hkern u1="F" u2="&#x12b;" k="-40" />
    <hkern u1="F" u2="&#x122;" k="21" />
    <hkern u1="F" u2="&#x120;" k="21" />
    <hkern u1="F" u2="&#x11e;" k="21" />
    <hkern u1="F" u2="&#x11c;" k="21" />
    <hkern u1="F" u2="&#x11b;" k="6" />
    <hkern u1="F" u2="&#x119;" k="6" />
    <hkern u1="F" u2="&#x117;" k="6" />
    <hkern u1="F" u2="&#x113;" k="6" />
    <hkern u1="F" u2="&#x10c;" k="21" />
    <hkern u1="F" u2="&#x10a;" k="21" />
    <hkern u1="F" u2="&#x108;" k="21" />
    <hkern u1="F" u2="&#x106;" k="21" />
    <hkern u1="F" u2="&#x105;" k="5" />
    <hkern u1="F" u2="&#x104;" k="17" />
    <hkern u1="F" u2="&#x103;" k="5" />
    <hkern u1="F" u2="&#x102;" k="17" />
    <hkern u1="F" u2="&#x101;" k="5" />
    <hkern u1="F" u2="&#x100;" k="17" />
    <hkern u1="F" u2="&#xfc;" k="11" />
    <hkern u1="F" u2="&#xfb;" k="11" />
    <hkern u1="F" u2="&#xfa;" k="11" />
    <hkern u1="F" u2="&#xf9;" k="11" />
    <hkern u1="F" u2="&#xf8;" k="5" />
    <hkern u1="F" u2="&#xf6;" k="5" />
    <hkern u1="F" u2="&#xf5;" k="5" />
    <hkern u1="F" u2="&#xf4;" k="5" />
    <hkern u1="F" u2="&#xf3;" k="5" />
    <hkern u1="F" u2="&#xf2;" k="5" />
    <hkern u1="F" u2="&#xef;" k="-60" />
    <hkern u1="F" u2="&#xee;" k="-30" />
    <hkern u1="F" u2="&#xec;" k="-10" />
    <hkern u1="F" u2="&#xeb;" k="6" />
    <hkern u1="F" u2="&#xea;" k="6" />
    <hkern u1="F" u2="&#xe9;" k="6" />
    <hkern u1="F" u2="&#xe8;" k="6" />
    <hkern u1="F" u2="&#xe6;" k="5" />
    <hkern u1="F" u2="&#xe5;" k="5" />
    <hkern u1="F" u2="&#xe4;" k="5" />
    <hkern u1="F" u2="&#xe3;" k="5" />
    <hkern u1="F" u2="&#xe2;" k="5" />
    <hkern u1="F" u2="&#xe1;" k="5" />
    <hkern u1="F" u2="&#xe0;" k="5" />
    <hkern u1="F" u2="&#xc7;" k="21" />
    <hkern u1="F" u2="&#xc6;" k="17" />
    <hkern u1="F" u2="&#xc5;" k="17" />
    <hkern u1="F" u2="&#xc4;" k="17" />
    <hkern u1="F" u2="&#xc3;" k="17" />
    <hkern u1="F" u2="&#xc2;" k="17" />
    <hkern u1="F" u2="&#xc1;" k="17" />
    <hkern u1="F" u2="&#xc0;" k="17" />
    <hkern u1="F" u2="&#xb8;" k="17" />
    <hkern u1="F" u2="&#xad;" k="-29" />
    <hkern u1="F" u2="u" k="11" />
    <hkern u1="F" u2="r" k="11" />
    <hkern u1="F" u2="o" k="5" />
    <hkern u1="F" u2="e" k="6" />
    <hkern u1="F" u2="a" k="5" />
    <hkern u1="F" u2="J" k="42" />
    <hkern u1="F" u2="G" k="21" />
    <hkern u1="F" u2="C" k="21" />
    <hkern u1="F" u2="A" k="17" />
    <hkern u1="F" u2="&#x2e;" k="37" />
    <hkern u1="F" u2="&#x2d;" k="-29" />
    <hkern u1="F" u2="&#x2c;" k="60" />
    <hkern u1="G" u2="&#x2122;" k="-52" />
    <hkern u1="G" u2="&#xb8;" k="9" />
    <hkern u1="H" u2="&#x2122;" k="-52" />
    <hkern u1="I" u2="&#x2122;" k="-73" />
    <hkern u1="J" u2="&#x2122;" k="-52" />
    <hkern u1="K" u2="&#x2122;" k="-52" />
    <hkern u1="K" u2="&#x2dd;" k="12" />
    <hkern u1="K" u2="&#xe6;" k="12" />
    <hkern u1="L" u2="&#x2dd;" k="3" />
    <hkern u1="L" u2="&#xe6;" k="3" />
    <hkern u1="L" u2="&#xb8;" k="-31" />
    <hkern u1="M" u2="&#x2122;" k="-73" />
    <hkern u1="M" u2="&#x201d;" k="-41" />
    <hkern u1="M" u2="&#x2019;" k="-41" />
    <hkern u1="N" u2="&#x2122;" k="-73" />
    <hkern u1="O" u2="&#x2122;" k="-42" />
    <hkern u1="O" u2="&#x201d;" k="-41" />
    <hkern u1="O" u2="&#xb8;" k="11" />
    <hkern u1="O" u2="X" k="9" />
    <hkern u1="P" g2="hyphenminus" k="-53" />
    <hkern u1="P" u2="&#x2122;" k="-62" />
    <hkern u1="P" u2="&#x2026;" k="83" />
    <hkern u1="P" u2="&#x259;" k="16" />
    <hkern u1="P" u2="&#x1fd;" k="11" />
    <hkern u1="P" u2="&#x1fc;" k="25" />
    <hkern u1="P" u2="&#x1dd;" k="16" />
    <hkern u1="P" u2="&#x17d;" k="32" />
    <hkern u1="P" u2="&#x17b;" k="32" />
    <hkern u1="P" u2="&#x179;" k="32" />
    <hkern u1="P" u2="&#x178;" k="11" />
    <hkern u1="P" u2="&#x153;" k="16" />
    <hkern u1="P" u2="&#x151;" k="16" />
    <hkern u1="P" u2="&#x14f;" k="16" />
    <hkern u1="P" u2="&#x14d;" k="16" />
    <hkern u1="P" u2="&#x12b;" k="-50" />
    <hkern u1="P" u2="&#x123;" k="11" />
    <hkern u1="P" u2="&#x121;" k="11" />
    <hkern u1="P" u2="&#x11f;" k="11" />
    <hkern u1="P" u2="&#x11d;" k="11" />
    <hkern u1="P" u2="&#x11b;" k="17" />
    <hkern u1="P" u2="&#x119;" k="17" />
    <hkern u1="P" u2="&#x117;" k="17" />
    <hkern u1="P" u2="&#x113;" k="17" />
    <hkern u1="P" u2="&#x111;" k="21" />
    <hkern u1="P" u2="&#x10f;" k="21" />
    <hkern u1="P" u2="&#x10d;" k="32" />
    <hkern u1="P" u2="&#x10b;" k="32" />
    <hkern u1="P" u2="&#x109;" k="32" />
    <hkern u1="P" u2="&#x107;" k="32" />
    <hkern u1="P" u2="&#x105;" k="11" />
    <hkern u1="P" u2="&#x104;" k="25" />
    <hkern u1="P" u2="&#x103;" k="11" />
    <hkern u1="P" u2="&#x102;" k="25" />
    <hkern u1="P" u2="&#x101;" k="11" />
    <hkern u1="P" u2="&#x100;" k="25" />
    <hkern u1="P" u2="&#xf8;" k="16" />
    <hkern u1="P" u2="&#xf6;" k="16" />
    <hkern u1="P" u2="&#xf5;" k="16" />
    <hkern u1="P" u2="&#xf4;" k="16" />
    <hkern u1="P" u2="&#xf3;" k="16" />
    <hkern u1="P" u2="&#xf2;" k="16" />
    <hkern u1="P" u2="&#xef;" k="-69" />
    <hkern u1="P" u2="&#xee;" k="-20" />
    <hkern u1="P" u2="&#xeb;" k="17" />
    <hkern u1="P" u2="&#xea;" k="17" />
    <hkern u1="P" u2="&#xe9;" k="17" />
    <hkern u1="P" u2="&#xe8;" k="17" />
    <hkern u1="P" u2="&#xe7;" k="32" />
    <hkern u1="P" u2="&#xe6;" k="11" />
    <hkern u1="P" u2="&#xe5;" k="11" />
    <hkern u1="P" u2="&#xe4;" k="11" />
    <hkern u1="P" u2="&#xe3;" k="11" />
    <hkern u1="P" u2="&#xe2;" k="11" />
    <hkern u1="P" u2="&#xe1;" k="11" />
    <hkern u1="P" u2="&#xe0;" k="11" />
    <hkern u1="P" u2="&#xdd;" k="11" />
    <hkern u1="P" u2="&#xc6;" k="25" />
    <hkern u1="P" u2="&#xc5;" k="25" />
    <hkern u1="P" u2="&#xc4;" k="25" />
    <hkern u1="P" u2="&#xc3;" k="25" />
    <hkern u1="P" u2="&#xc2;" k="25" />
    <hkern u1="P" u2="&#xc1;" k="25" />
    <hkern u1="P" u2="&#xc0;" k="25" />
    <hkern u1="P" u2="&#xb8;" k="25" />
    <hkern u1="P" u2="q" k="10" />
    <hkern u1="P" u2="o" k="16" />
    <hkern u1="P" u2="g" k="11" />
    <hkern u1="P" u2="e" k="17" />
    <hkern u1="P" u2="d" k="21" />
    <hkern u1="P" u2="c" k="32" />
    <hkern u1="P" u2="a" k="11" />
    <hkern u1="P" u2="Z" k="32" />
    <hkern u1="P" u2="Y" k="11" />
    <hkern u1="P" u2="J" k="42" />
    <hkern u1="P" u2="A" k="25" />
    <hkern u1="P" u2="&#x2e;" k="78" />
    <hkern u1="P" u2="&#x2c;" k="100" />
    <hkern u1="Q" u2="&#x2122;" k="-42" />
    <hkern u1="Q" u2="&#x201d;" k="-32" />
    <hkern u1="Q" u2="&#x2019;" k="-41" />
    <hkern u1="Q" u2="&#x178;" k="21" />
    <hkern u1="Q" u2="&#xdd;" k="21" />
    <hkern u1="Q" u2="Y" k="21" />
    <hkern u1="R" u2="&#x2122;" k="-52" />
    <hkern u1="R" u2="&#x2dd;" k="13" />
    <hkern u1="R" u2="&#xe6;" k="13" />
    <hkern u1="R" u2="&#xb8;" k="-18" />
    <hkern u1="R" u2="J" k="11" />
    <hkern u1="S" u2="&#x2122;" k="-31" />
    <hkern u1="T" g2="hyphenminus" k="-42" />
    <hkern u1="T" u2="&#x2122;" k="-52" />
    <hkern u1="T" u2="&#x2018;" k="-30" />
    <hkern u1="T" u2="&#x1d0;" k="-60" />
    <hkern u1="T" u2="&#x17e;" k="10" />
    <hkern u1="T" u2="&#x161;" k="26" />
    <hkern u1="T" u2="&#x15d;" k="26" />
    <hkern u1="T" u2="&#x159;" k="-10" />
    <hkern u1="T" u2="&#x12b;" k="-87" />
    <hkern u1="T" u2="&#x113;" k="-1" />
    <hkern u1="T" u2="&#xff;" k="1" />
    <hkern u1="T" u2="&#xef;" k="-107" />
    <hkern u1="T" u2="&#xee;" k="-57" />
    <hkern u1="T" u2="&#xed;" k="-17" />
    <hkern u1="T" u2="&#xec;" k="-47" />
    <hkern u1="T" u2="&#xeb;" k="-1" />
    <hkern u1="T" u2="&#xb8;" k="14" />
    <hkern u1="T" u2="x" k="50" />
    <hkern u1="T" u2="v" k="20" />
    <hkern u1="T" u2="q" k="40" />
    <hkern u1="T" u2="p" k="20" />
    <hkern u1="T" u2="m" k="30" />
    <hkern u1="T" u2="f" k="20" />
    <hkern u1="T" u2="J" k="31" />
    <hkern u1="T" u2="&#x2e;" k="14" />
    <hkern u1="U" u2="&#x2122;" k="-62" />
    <hkern u1="U" u2="&#xb8;" k="5" />
    <hkern u1="V" g2="hyphenminus" k="-53" />
    <hkern u1="V" u2="&#x2122;" k="-31" />
    <hkern u1="V" u2="&#x2dd;" k="13" />
    <hkern u1="V" u2="&#x1d0;" k="-50" />
    <hkern u1="V" u2="&#x17e;" k="20" />
    <hkern u1="V" u2="&#x161;" k="30" />
    <hkern u1="V" u2="&#x15d;" k="40" />
    <hkern u1="V" u2="&#x159;" k="1" />
    <hkern u1="V" u2="&#x12b;" k="-60" />
    <hkern u1="V" u2="&#xef;" k="-79" />
    <hkern u1="V" u2="&#xee;" k="-30" />
    <hkern u1="V" u2="&#xec;" k="-50" />
    <hkern u1="V" u2="&#xe6;" k="13" />
    <hkern u1="V" u2="&#xb8;" k="23" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="q" k="50" />
    <hkern u1="V" u2="p" k="19" />
    <hkern u1="V" u2="m" k="19" />
    <hkern u1="V" u2="J" k="42" />
    <hkern u1="V" u2="&#x3a;" k="1" />
    <hkern u1="W" g2="hyphenminus" k="-62" />
    <hkern u1="W" u2="&#x2122;" k="-53" />
    <hkern u1="W" u2="&#x2dd;" k="7" />
    <hkern u1="W" u2="&#x1d0;" k="-50" />
    <hkern u1="W" u2="&#x159;" k="1" />
    <hkern u1="W" u2="&#x12b;" k="-60" />
    <hkern u1="W" u2="&#xef;" k="-90" />
    <hkern u1="W" u2="&#xee;" k="-40" />
    <hkern u1="W" u2="&#xec;" k="-40" />
    <hkern u1="W" u2="&#xe6;" k="7" />
    <hkern u1="W" u2="&#xb8;" k="13" />
    <hkern u1="W" u2="x" k="20" />
    <hkern u1="W" u2="q" k="20" />
    <hkern u1="W" u2="p" k="12" />
    <hkern u1="W" u2="m" k="12" />
    <hkern u1="X" g2="hyphenminus" k="-41" />
    <hkern u1="X" u2="&#xf6bc;" k="-41" />
    <hkern u1="X" u2="&#xf6bb;" k="-41" />
    <hkern u1="X" u2="&#xf6ba;" k="-41" />
    <hkern u1="X" u2="&#x2122;" k="-31" />
    <hkern u1="X" u2="&#x2014;" k="-41" />
    <hkern u1="X" u2="&#x2013;" k="-41" />
    <hkern u1="X" u2="&#x2012;" k="-41" />
    <hkern u1="X" u2="&#x2011;" k="-41" />
    <hkern u1="X" u2="&#x2010;" k="-41" />
    <hkern u1="X" u2="&#x2dd;" k="12" />
    <hkern u1="X" u2="&#x259;" k="12" />
    <hkern u1="X" u2="&#x218;" k="21" />
    <hkern u1="X" u2="&#x1fd;" k="9" />
    <hkern u1="X" u2="&#x1dd;" k="12" />
    <hkern u1="X" u2="&#x18f;" k="12" />
    <hkern u1="X" u2="&#x173;" k="9" />
    <hkern u1="X" u2="&#x171;" k="9" />
    <hkern u1="X" u2="&#x16f;" k="9" />
    <hkern u1="X" u2="&#x16d;" k="9" />
    <hkern u1="X" u2="&#x16b;" k="9" />
    <hkern u1="X" u2="&#x169;" k="9" />
    <hkern u1="X" u2="&#x160;" k="21" />
    <hkern u1="X" u2="&#x15e;" k="21" />
    <hkern u1="X" u2="&#x15c;" k="21" />
    <hkern u1="X" u2="&#x15a;" k="21" />
    <hkern u1="X" u2="&#x153;" k="12" />
    <hkern u1="X" u2="&#x152;" k="12" />
    <hkern u1="X" u2="&#x151;" k="12" />
    <hkern u1="X" u2="&#x150;" k="12" />
    <hkern u1="X" u2="&#x14f;" k="12" />
    <hkern u1="X" u2="&#x14e;" k="12" />
    <hkern u1="X" u2="&#x14d;" k="12" />
    <hkern u1="X" u2="&#x14c;" k="12" />
    <hkern u1="X" u2="&#x122;" k="31" />
    <hkern u1="X" u2="&#x120;" k="31" />
    <hkern u1="X" u2="&#x11e;" k="31" />
    <hkern u1="X" u2="&#x11c;" k="31" />
    <hkern u1="X" u2="&#x11b;" k="11" />
    <hkern u1="X" u2="&#x119;" k="11" />
    <hkern u1="X" u2="&#x117;" k="11" />
    <hkern u1="X" u2="&#x113;" k="11" />
    <hkern u1="X" u2="&#x10c;" k="20" />
    <hkern u1="X" u2="&#x10a;" k="20" />
    <hkern u1="X" u2="&#x108;" k="20" />
    <hkern u1="X" u2="&#x106;" k="20" />
    <hkern u1="X" u2="&#x105;" k="9" />
    <hkern u1="X" u2="&#x103;" k="9" />
    <hkern u1="X" u2="&#x101;" k="9" />
    <hkern u1="X" u2="&#xff;" k="20" />
    <hkern u1="X" u2="&#xfd;" k="20" />
    <hkern u1="X" u2="&#xfc;" k="9" />
    <hkern u1="X" u2="&#xfb;" k="9" />
    <hkern u1="X" u2="&#xfa;" k="9" />
    <hkern u1="X" u2="&#xf9;" k="9" />
    <hkern u1="X" u2="&#xf8;" k="12" />
    <hkern u1="X" u2="&#xf6;" k="12" />
    <hkern u1="X" u2="&#xf5;" k="12" />
    <hkern u1="X" u2="&#xf4;" k="12" />
    <hkern u1="X" u2="&#xf3;" k="12" />
    <hkern u1="X" u2="&#xf2;" k="12" />
    <hkern u1="X" u2="&#xeb;" k="11" />
    <hkern u1="X" u2="&#xea;" k="11" />
    <hkern u1="X" u2="&#xe9;" k="11" />
    <hkern u1="X" u2="&#xe8;" k="11" />
    <hkern u1="X" u2="&#xe6;" k="12" />
    <hkern u1="X" u2="&#xe5;" k="9" />
    <hkern u1="X" u2="&#xe4;" k="9" />
    <hkern u1="X" u2="&#xe3;" k="9" />
    <hkern u1="X" u2="&#xe2;" k="9" />
    <hkern u1="X" u2="&#xe1;" k="9" />
    <hkern u1="X" u2="&#xe0;" k="9" />
    <hkern u1="X" u2="&#xd8;" k="12" />
    <hkern u1="X" u2="&#xd6;" k="12" />
    <hkern u1="X" u2="&#xd5;" k="12" />
    <hkern u1="X" u2="&#xd4;" k="12" />
    <hkern u1="X" u2="&#xd3;" k="12" />
    <hkern u1="X" u2="&#xd2;" k="12" />
    <hkern u1="X" u2="&#xc7;" k="20" />
    <hkern u1="X" u2="&#xad;" k="-41" />
    <hkern u1="X" u2="y" k="20" />
    <hkern u1="X" u2="u" k="9" />
    <hkern u1="X" u2="o" k="12" />
    <hkern u1="X" u2="e" k="11" />
    <hkern u1="X" u2="a" k="9" />
    <hkern u1="X" u2="S" k="21" />
    <hkern u1="X" u2="O" k="12" />
    <hkern u1="X" u2="G" k="31" />
    <hkern u1="X" u2="C" k="20" />
    <hkern u1="X" u2="&#x2d;" k="-41" />
    <hkern u1="Y" u2="&#x2122;" k="-41" />
    <hkern u1="Y" u2="&#x2dd;" k="25" />
    <hkern u1="Y" u2="&#x1d0;" k="-40" />
    <hkern u1="Y" u2="&#x17e;" k="30" />
    <hkern u1="Y" u2="&#x161;" k="40" />
    <hkern u1="Y" u2="&#x159;" k="20" />
    <hkern u1="Y" u2="&#x12b;" k="-50" />
    <hkern u1="Y" u2="&#xff;" k="30" />
    <hkern u1="Y" u2="&#xef;" k="-70" />
    <hkern u1="Y" u2="&#xee;" k="-20" />
    <hkern u1="Y" u2="&#xed;" k="10" />
    <hkern u1="Y" u2="&#xec;" k="-40" />
    <hkern u1="Y" u2="&#xe6;" k="25" />
    <hkern u1="Y" u2="&#xb8;" k="34" />
    <hkern u1="Y" u2="x" k="70" />
    <hkern u1="Y" u2="v" k="40" />
    <hkern u1="Y" u2="q" k="50" />
    <hkern u1="Y" u2="p" k="38" />
    <hkern u1="Y" u2="m" k="40" />
    <hkern u1="Y" u2="b" k="10" />
    <hkern u1="Y" u2="Q" k="31" />
    <hkern u1="Y" u2="J" k="52" />
    <hkern u1="Y" u2="&#x3a;" k="13" />
    <hkern u1="Z" u2="&#x2122;" k="-20" />
    <hkern u1="Z" u2="Q" k="31" />
    <hkern u1="a" u2="&#x201d;" k="-52" />
    <hkern u1="a" u2="&#x2018;" k="-73" />
    <hkern u1="c" u2="J" k="-21" />
    <hkern u1="e" u2="v" k="7" />
    <hkern u1="f" u2="&#x1d0;" k="-58" />
    <hkern u1="f" u2="&#x12b;" k="-66" />
    <hkern u1="f" u2="&#xef;" k="-81" />
    <hkern u1="f" u2="&#xee;" k="-41" />
    <hkern u1="f" u2="&#xed;" k="-31" />
    <hkern u1="f" u2="&#xec;" k="-51" />
    <hkern u1="f" u2="v" k="-21" />
    <hkern u1="f" u2="f" k="-25" />
    <hkern u1="f" u2="X" k="-31" />
    <hkern u1="h" u2="&#x201d;" k="-41" />
    <hkern u1="h" u2="&#x2019;" k="-41" />
    <hkern u1="h" u2="&#xff;" k="3" />
    <hkern u1="h" u2="&#xfd;" k="3" />
    <hkern u1="h" u2="y" k="3" />
    <hkern u1="m" u2="&#x178;" k="41" />
    <hkern u1="m" u2="&#xff;" k="3" />
    <hkern u1="m" u2="&#xfd;" k="3" />
    <hkern u1="m" u2="&#xdd;" k="41" />
    <hkern u1="m" u2="y" k="3" />
    <hkern u1="m" u2="Y" k="41" />
    <hkern u1="o" u2="&#x2018;" k="-31" />
    <hkern u1="o" u2="x" k="11" />
    <hkern u1="o" u2="v" k="9" />
    <hkern u1="p" u2="&#x178;" k="31" />
    <hkern u1="p" u2="&#xff;" k="6" />
    <hkern u1="p" u2="&#xfd;" k="6" />
    <hkern u1="p" u2="&#xdd;" k="31" />
    <hkern u1="p" u2="y" k="6" />
    <hkern u1="p" u2="Y" k="31" />
    <hkern u1="q" u2="&#x178;" k="31" />
    <hkern u1="q" u2="&#xdd;" k="31" />
    <hkern u1="q" u2="Y" k="31" />
    <hkern u1="r" u2="x" k="-13" />
    <hkern u1="r" u2="v" k="-11" />
    <hkern u1="r" u2="f" k="-16" />
    <hkern u1="t" u2="v" k="-32" />
    <hkern u1="t" u2="h" k="-11" />
    <hkern u1="t" u2="f" k="-32" />
    <hkern u1="v" g2="hyphenminus" k="-73" />
    <hkern u1="v" u2="&#xf6bc;" k="-26" />
    <hkern u1="v" u2="&#xf6bb;" k="-26" />
    <hkern u1="v" u2="&#xf6ba;" k="-26" />
    <hkern u1="v" u2="&#x2014;" k="-26" />
    <hkern u1="v" u2="&#x2013;" k="-26" />
    <hkern u1="v" u2="&#x2012;" k="-26" />
    <hkern u1="v" u2="&#x2011;" k="-26" />
    <hkern u1="v" u2="&#x2010;" k="-26" />
    <hkern u1="v" u2="&#x259;" k="10" />
    <hkern u1="v" u2="&#x1fd;" k="6" />
    <hkern u1="v" u2="&#x1dd;" k="10" />
    <hkern u1="v" u2="&#x178;" k="32" />
    <hkern u1="v" u2="&#x153;" k="10" />
    <hkern u1="v" u2="&#x151;" k="10" />
    <hkern u1="v" u2="&#x14f;" k="10" />
    <hkern u1="v" u2="&#x14d;" k="10" />
    <hkern u1="v" u2="&#x11b;" k="10" />
    <hkern u1="v" u2="&#x119;" k="10" />
    <hkern u1="v" u2="&#x117;" k="10" />
    <hkern u1="v" u2="&#x113;" k="10" />
    <hkern u1="v" u2="&#x105;" k="6" />
    <hkern u1="v" u2="&#x103;" k="6" />
    <hkern u1="v" u2="&#x101;" k="6" />
    <hkern u1="v" u2="&#xf8;" k="10" />
    <hkern u1="v" u2="&#xf6;" k="10" />
    <hkern u1="v" u2="&#xf5;" k="10" />
    <hkern u1="v" u2="&#xf4;" k="10" />
    <hkern u1="v" u2="&#xf3;" k="10" />
    <hkern u1="v" u2="&#xf2;" k="10" />
    <hkern u1="v" u2="&#xeb;" k="10" />
    <hkern u1="v" u2="&#xea;" k="10" />
    <hkern u1="v" u2="&#xe9;" k="10" />
    <hkern u1="v" u2="&#xe8;" k="10" />
    <hkern u1="v" u2="&#xe6;" k="6" />
    <hkern u1="v" u2="&#xe5;" k="6" />
    <hkern u1="v" u2="&#xe4;" k="6" />
    <hkern u1="v" u2="&#xe3;" k="6" />
    <hkern u1="v" u2="&#xe2;" k="6" />
    <hkern u1="v" u2="&#xe1;" k="6" />
    <hkern u1="v" u2="&#xe0;" k="6" />
    <hkern u1="v" u2="&#xdd;" k="32" />
    <hkern u1="v" u2="&#xad;" k="-26" />
    <hkern u1="v" u2="o" k="10" />
    <hkern u1="v" u2="e" k="10" />
    <hkern u1="v" u2="a" k="6" />
    <hkern u1="v" u2="Y" k="32" />
    <hkern u1="v" u2="&#x2e;" k="20" />
    <hkern u1="v" u2="&#x2d;" k="-26" />
    <hkern u1="v" u2="&#x2c;" k="21" />
    <hkern u1="w" g2="hyphenminus" k="-83" />
    <hkern u1="w" u2="f" k="-11" />
    <hkern u1="w" u2="&#x2e;" k="18" />
    <hkern u1="x" u2="&#x259;" k="13" />
    <hkern u1="x" u2="&#x21a;" k="21" />
    <hkern u1="x" u2="&#x1fd;" k="9" />
    <hkern u1="x" u2="&#x1dd;" k="13" />
    <hkern u1="x" u2="&#x178;" k="42" />
    <hkern u1="x" u2="&#x164;" k="21" />
    <hkern u1="x" u2="&#x162;" k="21" />
    <hkern u1="x" u2="&#x153;" k="13" />
    <hkern u1="x" u2="&#x151;" k="13" />
    <hkern u1="x" u2="&#x14f;" k="13" />
    <hkern u1="x" u2="&#x14d;" k="13" />
    <hkern u1="x" u2="&#x11b;" k="13" />
    <hkern u1="x" u2="&#x119;" k="13" />
    <hkern u1="x" u2="&#x117;" k="13" />
    <hkern u1="x" u2="&#x113;" k="13" />
    <hkern u1="x" u2="&#x10d;" k="13" />
    <hkern u1="x" u2="&#x10b;" k="13" />
    <hkern u1="x" u2="&#x109;" k="13" />
    <hkern u1="x" u2="&#x107;" k="13" />
    <hkern u1="x" u2="&#x105;" k="9" />
    <hkern u1="x" u2="&#x103;" k="9" />
    <hkern u1="x" u2="&#x101;" k="9" />
    <hkern u1="x" u2="&#xf8;" k="13" />
    <hkern u1="x" u2="&#xf6;" k="13" />
    <hkern u1="x" u2="&#xf5;" k="13" />
    <hkern u1="x" u2="&#xf4;" k="13" />
    <hkern u1="x" u2="&#xf3;" k="13" />
    <hkern u1="x" u2="&#xf2;" k="13" />
    <hkern u1="x" u2="&#xeb;" k="13" />
    <hkern u1="x" u2="&#xea;" k="13" />
    <hkern u1="x" u2="&#xe9;" k="13" />
    <hkern u1="x" u2="&#xe8;" k="13" />
    <hkern u1="x" u2="&#xe7;" k="13" />
    <hkern u1="x" u2="&#xe6;" k="9" />
    <hkern u1="x" u2="&#xe5;" k="9" />
    <hkern u1="x" u2="&#xe4;" k="9" />
    <hkern u1="x" u2="&#xe3;" k="9" />
    <hkern u1="x" u2="&#xe2;" k="9" />
    <hkern u1="x" u2="&#xe1;" k="9" />
    <hkern u1="x" u2="&#xe0;" k="9" />
    <hkern u1="x" u2="&#xdd;" k="42" />
    <hkern u1="x" u2="x" k="-20" />
    <hkern u1="x" u2="o" k="13" />
    <hkern u1="x" u2="e" k="13" />
    <hkern u1="x" u2="c" k="13" />
    <hkern u1="x" u2="a" k="9" />
    <hkern u1="x" u2="Y" k="42" />
    <hkern u1="x" u2="V" k="11" />
    <hkern u1="x" u2="T" k="21" />
    <hkern u1="y" g2="hyphenminus" k="-83" />
    <hkern u1="y" u2="&#x2018;" k="-30" />
    <hkern u1="y" u2="f" k="-10" />
    <hkern u1="&#xad;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#xad;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#xad;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#xad;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#xad;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#xad;" u2="&#x4af;" k="-33" />
    <hkern u1="&#xad;" u2="&#x4ae;" k="20" />
    <hkern u1="&#xad;" u2="&#x497;" k="-52" />
    <hkern u1="&#xad;" u2="&#x496;" k="-41" />
    <hkern u1="&#xad;" u2="&#x459;" k="-52" />
    <hkern u1="&#xad;" u2="&#x442;" k="-52" />
    <hkern u1="&#xad;" u2="&#x436;" k="-52" />
    <hkern u1="&#xad;" u2="&#x434;" k="-41" />
    <hkern u1="&#xad;" u2="&#x42a;" k="-41" />
    <hkern u1="&#xad;" u2="&#x425;" k="-31" />
    <hkern u1="&#xad;" u2="&#x416;" k="-41" />
    <hkern u1="&#xad;" u2="&#x414;" k="-41" />
    <hkern u1="&#xad;" u2="&#x40b;" k="-52" />
    <hkern u1="&#xad;" u2="&#x409;" k="-52" />
    <hkern u1="&#xad;" u2="&#x402;" k="-41" />
    <hkern u1="&#xad;" u2="&#x21a;" k="-41" />
    <hkern u1="&#xad;" u2="&#x178;" k="-21" />
    <hkern u1="&#xad;" u2="&#x164;" k="-41" />
    <hkern u1="&#xad;" u2="&#x104;" k="-62" />
    <hkern u1="&#xad;" u2="&#x102;" k="-62" />
    <hkern u1="&#xad;" u2="&#x100;" k="-62" />
    <hkern u1="&#xad;" u2="&#xdd;" k="-21" />
    <hkern u1="&#xad;" u2="&#xc5;" k="-62" />
    <hkern u1="&#xad;" u2="&#xc4;" k="-62" />
    <hkern u1="&#xad;" u2="&#xc3;" k="-62" />
    <hkern u1="&#xad;" u2="&#xc2;" k="-62" />
    <hkern u1="&#xad;" u2="&#xc1;" k="-62" />
    <hkern u1="&#xad;" u2="&#xc0;" k="-62" />
    <hkern u1="&#xad;" u2="&#x31;" k="21" />
    <hkern u1="&#xb8;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xb8;" u2="&#x21a;" k="14" />
    <hkern u1="&#xb8;" u2="&#x1fc;" k="-33" />
    <hkern u1="&#xb8;" u2="&#x18f;" k="11" />
    <hkern u1="&#xb8;" u2="&#x178;" k="37" />
    <hkern u1="&#xb8;" u2="&#x172;" k="6" />
    <hkern u1="&#xb8;" u2="&#x170;" k="6" />
    <hkern u1="&#xb8;" u2="&#x16e;" k="6" />
    <hkern u1="&#xb8;" u2="&#x16c;" k="6" />
    <hkern u1="&#xb8;" u2="&#x16a;" k="6" />
    <hkern u1="&#xb8;" u2="&#x168;" k="6" />
    <hkern u1="&#xb8;" u2="&#x164;" k="14" />
    <hkern u1="&#xb8;" u2="&#x162;" k="14" />
    <hkern u1="&#xb8;" u2="&#x152;" k="11" />
    <hkern u1="&#xb8;" u2="&#x150;" k="11" />
    <hkern u1="&#xb8;" u2="&#x14e;" k="11" />
    <hkern u1="&#xb8;" u2="&#x14c;" k="11" />
    <hkern u1="&#xb8;" u2="&#x122;" k="12" />
    <hkern u1="&#xb8;" u2="&#x120;" k="12" />
    <hkern u1="&#xb8;" u2="&#x11e;" k="12" />
    <hkern u1="&#xb8;" u2="&#x11c;" k="12" />
    <hkern u1="&#xb8;" u2="&#x10c;" k="16" />
    <hkern u1="&#xb8;" u2="&#x10a;" k="16" />
    <hkern u1="&#xb8;" u2="&#x108;" k="16" />
    <hkern u1="&#xb8;" u2="&#x106;" k="16" />
    <hkern u1="&#xb8;" u2="&#x104;" k="-33" />
    <hkern u1="&#xb8;" u2="&#x102;" k="-33" />
    <hkern u1="&#xb8;" u2="&#x100;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xff;" k="5" />
    <hkern u1="&#xb8;" u2="&#xfd;" k="5" />
    <hkern u1="&#xb8;" u2="&#xe6;" k="11" />
    <hkern u1="&#xb8;" u2="&#xdd;" k="37" />
    <hkern u1="&#xb8;" u2="&#xdc;" k="6" />
    <hkern u1="&#xb8;" u2="&#xdb;" k="6" />
    <hkern u1="&#xb8;" u2="&#xda;" k="6" />
    <hkern u1="&#xb8;" u2="&#xd9;" k="6" />
    <hkern u1="&#xb8;" u2="&#xd8;" k="11" />
    <hkern u1="&#xb8;" u2="&#xd6;" k="11" />
    <hkern u1="&#xb8;" u2="&#xd5;" k="11" />
    <hkern u1="&#xb8;" u2="&#xd4;" k="11" />
    <hkern u1="&#xb8;" u2="&#xd3;" k="11" />
    <hkern u1="&#xb8;" u2="&#xd2;" k="11" />
    <hkern u1="&#xb8;" u2="&#xc7;" k="16" />
    <hkern u1="&#xb8;" u2="&#xc6;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc5;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc4;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc3;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc2;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc1;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xc0;" k="-33" />
    <hkern u1="&#xb8;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xb8;" u2="y" k="5" />
    <hkern u1="&#xb8;" u2="w" k="4" />
    <hkern u1="&#xb8;" u2="v" k="4" />
    <hkern u1="&#xb8;" u2="Y" k="37" />
    <hkern u1="&#xb8;" u2="W" k="14" />
    <hkern u1="&#xb8;" u2="V" k="27" />
    <hkern u1="&#xb8;" u2="U" k="6" />
    <hkern u1="&#xb8;" u2="T" k="14" />
    <hkern u1="&#xb8;" u2="Q" k="11" />
    <hkern u1="&#xb8;" u2="O" k="11" />
    <hkern u1="&#xb8;" u2="G" k="12" />
    <hkern u1="&#xb8;" u2="C" k="16" />
    <hkern u1="&#xb8;" u2="A" k="-33" />
    <hkern u1="&#xbb;" u2="&#x178;" k="31" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="31" />
    <hkern u1="&#xbb;" u2="Y" k="31" />
    <hkern u1="&#xc0;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc0;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc0;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc0;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc0;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc0;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc0;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc0;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc0;" u2="v" k="4" />
    <hkern u1="&#xc0;" u2="Q" k="11" />
    <hkern u1="&#xc0;" u2="J" k="-21" />
    <hkern u1="&#xc0;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc1;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc1;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc1;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc1;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc1;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc1;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc1;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc1;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc1;" u2="v" k="4" />
    <hkern u1="&#xc1;" u2="Q" k="11" />
    <hkern u1="&#xc1;" u2="J" k="-21" />
    <hkern u1="&#xc1;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc2;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc2;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc2;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc2;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc2;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc2;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc2;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc2;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc2;" u2="v" k="4" />
    <hkern u1="&#xc2;" u2="Q" k="11" />
    <hkern u1="&#xc2;" u2="J" k="-21" />
    <hkern u1="&#xc2;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc3;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc3;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc3;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc3;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc3;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc3;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc3;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc3;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc3;" u2="v" k="4" />
    <hkern u1="&#xc3;" u2="Q" k="11" />
    <hkern u1="&#xc3;" u2="J" k="-21" />
    <hkern u1="&#xc3;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc4;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc4;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc4;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc4;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc4;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc4;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc4;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc4;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc4;" u2="v" k="4" />
    <hkern u1="&#xc4;" u2="Q" k="11" />
    <hkern u1="&#xc4;" u2="J" k="-21" />
    <hkern u1="&#xc4;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc5;" g2="hyphenminus" k="-30" />
    <hkern u1="&#xc5;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#xc5;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#xc5;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2012;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2011;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2010;" k="-73" />
    <hkern u1="&#xc5;" u2="&#x2dd;" k="11" />
    <hkern u1="&#xc5;" u2="&#xe6;" k="11" />
    <hkern u1="&#xc5;" u2="&#xb8;" k="-33" />
    <hkern u1="&#xc5;" u2="&#xad;" k="-73" />
    <hkern u1="&#xc5;" u2="v" k="4" />
    <hkern u1="&#xc5;" u2="Q" k="11" />
    <hkern u1="&#xc5;" u2="J" k="-21" />
    <hkern u1="&#xc5;" u2="&#x2e;" k="-42" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="-73" />
    <hkern u1="&#xc6;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xc7;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xc7;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xc7;" u2="&#x2dd;" k="13" />
    <hkern u1="&#xc7;" u2="&#xe6;" k="13" />
    <hkern u1="&#xc7;" u2="&#xb8;" k="-18" />
    <hkern u1="&#xc7;" u2="Q" k="31" />
    <hkern u1="&#xc8;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xc9;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xca;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xcb;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xcc;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xcd;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xce;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xcf;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd0;" u2="&#xb8;" k="13" />
    <hkern u1="&#xd1;" u2="&#x2122;" k="-73" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd2;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd2;" u2="X" k="9" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd3;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd3;" u2="X" k="9" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd4;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd4;" u2="X" k="9" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd5;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd5;" u2="X" k="9" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd6;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd6;" u2="X" k="9" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="-42" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="-41" />
    <hkern u1="&#xd8;" u2="&#xb8;" k="11" />
    <hkern u1="&#xd8;" u2="X" k="9" />
    <hkern u1="&#xd9;" u2="&#x2122;" k="-62" />
    <hkern u1="&#xd9;" u2="&#xb8;" k="5" />
    <hkern u1="&#xda;" u2="&#x2122;" k="-62" />
    <hkern u1="&#xda;" u2="&#xb8;" k="5" />
    <hkern u1="&#xdb;" u2="&#x2122;" k="-62" />
    <hkern u1="&#xdb;" u2="&#xb8;" k="5" />
    <hkern u1="&#xdc;" u2="&#x2122;" k="-62" />
    <hkern u1="&#xdc;" u2="&#xb8;" k="5" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-41" />
    <hkern u1="&#xdd;" u2="&#x2dd;" k="25" />
    <hkern u1="&#xdd;" u2="&#x1d0;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x17e;" k="30" />
    <hkern u1="&#xdd;" u2="&#x161;" k="40" />
    <hkern u1="&#xdd;" u2="&#x159;" k="20" />
    <hkern u1="&#xdd;" u2="&#x12b;" k="-50" />
    <hkern u1="&#xdd;" u2="&#xff;" k="30" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-70" />
    <hkern u1="&#xdd;" u2="&#xee;" k="-20" />
    <hkern u1="&#xdd;" u2="&#xed;" k="10" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-40" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="25" />
    <hkern u1="&#xdd;" u2="&#xb8;" k="34" />
    <hkern u1="&#xdd;" u2="x" k="70" />
    <hkern u1="&#xdd;" u2="v" k="40" />
    <hkern u1="&#xdd;" u2="q" k="50" />
    <hkern u1="&#xdd;" u2="p" k="38" />
    <hkern u1="&#xdd;" u2="m" k="40" />
    <hkern u1="&#xdd;" u2="b" k="10" />
    <hkern u1="&#xdd;" u2="Q" k="31" />
    <hkern u1="&#xdd;" u2="J" k="52" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="13" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="-52" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="-73" />
    <hkern u1="&#xe6;" u2="&#x1fc;" k="11" />
    <hkern u1="&#xe6;" u2="&#x178;" k="26" />
    <hkern u1="&#xe6;" u2="&#x104;" k="11" />
    <hkern u1="&#xe6;" u2="&#x102;" k="11" />
    <hkern u1="&#xe6;" u2="&#x100;" k="11" />
    <hkern u1="&#xe6;" u2="&#xdd;" k="26" />
    <hkern u1="&#xe6;" u2="&#xc6;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc5;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc4;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc3;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc2;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc1;" k="11" />
    <hkern u1="&#xe6;" u2="&#xc0;" k="11" />
    <hkern u1="&#xe6;" u2="&#xb8;" k="11" />
    <hkern u1="&#xe6;" u2="v" k="7" />
    <hkern u1="&#xe6;" u2="Y" k="26" />
    <hkern u1="&#xe6;" u2="X" k="9" />
    <hkern u1="&#xe6;" u2="W" k="7" />
    <hkern u1="&#xe6;" u2="V" k="14" />
    <hkern u1="&#xe6;" u2="A" k="11" />
    <hkern u1="&#xe7;" u2="J" k="-21" />
    <hkern u1="&#xe8;" u2="v" k="7" />
    <hkern u1="&#xe9;" u2="v" k="7" />
    <hkern u1="&#xea;" u2="v" k="7" />
    <hkern u1="&#xeb;" u2="v" k="7" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf2;" u2="x" k="11" />
    <hkern u1="&#xf2;" u2="v" k="9" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf3;" u2="x" k="11" />
    <hkern u1="&#xf3;" u2="v" k="9" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf4;" u2="x" k="11" />
    <hkern u1="&#xf4;" u2="v" k="9" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf5;" u2="x" k="11" />
    <hkern u1="&#xf5;" u2="v" k="9" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf6;" u2="x" k="11" />
    <hkern u1="&#xf6;" u2="v" k="9" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="-31" />
    <hkern u1="&#xf8;" u2="x" k="11" />
    <hkern u1="&#xf8;" u2="v" k="9" />
    <hkern u1="&#xfd;" g2="hyphenminus" k="-26" />
    <hkern u1="&#xfd;" u2="&#xf6bc;" k="-83" />
    <hkern u1="&#xfd;" u2="&#xf6bb;" k="-83" />
    <hkern u1="&#xfd;" u2="&#xf6ba;" k="-83" />
    <hkern u1="&#xfd;" u2="&#x2018;" k="-30" />
    <hkern u1="&#xfd;" u2="&#x2014;" k="-83" />
    <hkern u1="&#xfd;" u2="&#x2013;" k="-83" />
    <hkern u1="&#xfd;" u2="&#x2012;" k="-83" />
    <hkern u1="&#xfd;" u2="&#x2011;" k="-83" />
    <hkern u1="&#xfd;" u2="&#x2010;" k="-83" />
    <hkern u1="&#xfd;" u2="&#xad;" k="-83" />
    <hkern u1="&#xfd;" u2="f" k="-10" />
    <hkern u1="&#xfd;" u2="&#x2d;" k="-83" />
    <hkern u1="&#xff;" g2="hyphenminus" k="-26" />
    <hkern u1="&#xff;" u2="&#xf6bc;" k="-83" />
    <hkern u1="&#xff;" u2="&#xf6bb;" k="-83" />
    <hkern u1="&#xff;" u2="&#xf6ba;" k="-83" />
    <hkern u1="&#xff;" u2="&#x2018;" k="-30" />
    <hkern u1="&#xff;" u2="&#x2014;" k="-83" />
    <hkern u1="&#xff;" u2="&#x2013;" k="-83" />
    <hkern u1="&#xff;" u2="&#x2012;" k="-83" />
    <hkern u1="&#xff;" u2="&#x2011;" k="-83" />
    <hkern u1="&#xff;" u2="&#x2010;" k="-83" />
    <hkern u1="&#xff;" u2="&#xad;" k="-83" />
    <hkern u1="&#xff;" u2="f" k="-10" />
    <hkern u1="&#xff;" u2="&#x2d;" k="-83" />
    <hkern u1="&#x100;" g2="hyphenminus" k="-30" />
    <hkern u1="&#x100;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x100;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x100;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x100;" u2="&#x2dd;" k="11" />
    <hkern u1="&#x100;" u2="&#xe6;" k="11" />
    <hkern u1="&#x100;" u2="&#xb8;" k="-33" />
    <hkern u1="&#x100;" u2="&#xad;" k="-73" />
    <hkern u1="&#x100;" u2="v" k="4" />
    <hkern u1="&#x100;" u2="Q" k="11" />
    <hkern u1="&#x100;" u2="J" k="-21" />
    <hkern u1="&#x100;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x100;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x101;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x101;" u2="&#x2018;" k="-73" />
    <hkern u1="&#x102;" g2="hyphenminus" k="-30" />
    <hkern u1="&#x102;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x102;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x102;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x102;" u2="&#x2dd;" k="11" />
    <hkern u1="&#x102;" u2="&#xe6;" k="11" />
    <hkern u1="&#x102;" u2="&#xb8;" k="-33" />
    <hkern u1="&#x102;" u2="&#xad;" k="-73" />
    <hkern u1="&#x102;" u2="v" k="4" />
    <hkern u1="&#x102;" u2="Q" k="11" />
    <hkern u1="&#x102;" u2="J" k="-21" />
    <hkern u1="&#x102;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x102;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x103;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x103;" u2="&#x2018;" k="-73" />
    <hkern u1="&#x104;" g2="hyphenminus" k="-30" />
    <hkern u1="&#x104;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x104;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x104;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x104;" u2="&#x2dd;" k="11" />
    <hkern u1="&#x104;" u2="&#xe6;" k="11" />
    <hkern u1="&#x104;" u2="&#xb8;" k="-33" />
    <hkern u1="&#x104;" u2="&#xad;" k="-73" />
    <hkern u1="&#x104;" u2="v" k="4" />
    <hkern u1="&#x104;" u2="Q" k="11" />
    <hkern u1="&#x104;" u2="J" k="-21" />
    <hkern u1="&#x104;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x104;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x105;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x105;" u2="&#x2018;" k="-73" />
    <hkern u1="&#x106;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x106;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x106;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x106;" u2="&#xe6;" k="13" />
    <hkern u1="&#x106;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x106;" u2="Q" k="31" />
    <hkern u1="&#x107;" u2="J" k="-21" />
    <hkern u1="&#x108;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x108;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x108;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x108;" u2="&#xe6;" k="13" />
    <hkern u1="&#x108;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x108;" u2="Q" k="31" />
    <hkern u1="&#x109;" u2="J" k="-21" />
    <hkern u1="&#x10a;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x10a;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x10a;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x10a;" u2="&#xe6;" k="13" />
    <hkern u1="&#x10a;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x10a;" u2="Q" k="31" />
    <hkern u1="&#x10b;" u2="J" k="-21" />
    <hkern u1="&#x10c;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x10c;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x10c;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x10c;" u2="&#xe6;" k="13" />
    <hkern u1="&#x10c;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x10c;" u2="Q" k="31" />
    <hkern u1="&#x10d;" u2="&#x13e;" k="-30" />
    <hkern u1="&#x10d;" u2="J" k="-21" />
    <hkern u1="&#x10e;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x10e;" u2="&#xb8;" k="13" />
    <hkern u1="&#x10f;" u2="&#x259;" k="110" />
    <hkern u1="&#x10f;" u2="&#x21b;" k="40" />
    <hkern u1="&#x10f;" u2="&#x219;" k="120" />
    <hkern u1="&#x10f;" u2="&#x1fd;" k="110" />
    <hkern u1="&#x10f;" u2="&#x1dd;" k="110" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="20" />
    <hkern u1="&#x10f;" u2="&#x17c;" k="90" />
    <hkern u1="&#x10f;" u2="&#x17a;" k="90" />
    <hkern u1="&#x10f;" u2="&#x173;" k="90" />
    <hkern u1="&#x10f;" u2="&#x171;" k="90" />
    <hkern u1="&#x10f;" u2="&#x16f;" k="64" />
    <hkern u1="&#x10f;" u2="&#x16d;" k="90" />
    <hkern u1="&#x10f;" u2="&#x16b;" k="90" />
    <hkern u1="&#x10f;" u2="&#x169;" k="90" />
    <hkern u1="&#x10f;" u2="&#x165;" k="40" />
    <hkern u1="&#x10f;" u2="&#x163;" k="40" />
    <hkern u1="&#x10f;" u2="&#x161;" k="40" />
    <hkern u1="&#x10f;" u2="&#x15f;" k="120" />
    <hkern u1="&#x10f;" u2="&#x15d;" k="120" />
    <hkern u1="&#x10f;" u2="&#x15b;" k="120" />
    <hkern u1="&#x10f;" u2="&#x159;" k="20" />
    <hkern u1="&#x10f;" u2="&#x157;" k="90" />
    <hkern u1="&#x10f;" u2="&#x155;" k="90" />
    <hkern u1="&#x10f;" u2="&#x153;" k="110" />
    <hkern u1="&#x10f;" u2="&#x151;" k="110" />
    <hkern u1="&#x10f;" u2="&#x14f;" k="110" />
    <hkern u1="&#x10f;" u2="&#x14d;" k="110" />
    <hkern u1="&#x10f;" u2="&#x148;" k="50" />
    <hkern u1="&#x10f;" u2="&#x146;" k="90" />
    <hkern u1="&#x10f;" u2="&#x144;" k="90" />
    <hkern u1="&#x10f;" u2="&#x127;" k="90" />
    <hkern u1="&#x10f;" u2="&#x125;" k="90" />
    <hkern u1="&#x10f;" u2="&#x123;" k="110" />
    <hkern u1="&#x10f;" u2="&#x121;" k="110" />
    <hkern u1="&#x10f;" u2="&#x11f;" k="110" />
    <hkern u1="&#x10f;" u2="&#x11d;" k="110" />
    <hkern u1="&#x10f;" u2="&#x11b;" k="44" />
    <hkern u1="&#x10f;" u2="&#x119;" k="120" />
    <hkern u1="&#x10f;" u2="&#x117;" k="120" />
    <hkern u1="&#x10f;" u2="&#x113;" k="120" />
    <hkern u1="&#x10f;" u2="&#x111;" k="110" />
    <hkern u1="&#x10f;" u2="&#x10f;" k="110" />
    <hkern u1="&#x10f;" u2="&#x10d;" k="60" />
    <hkern u1="&#x10f;" u2="&#x10b;" k="120" />
    <hkern u1="&#x10f;" u2="&#x109;" k="120" />
    <hkern u1="&#x10f;" u2="&#x107;" k="120" />
    <hkern u1="&#x10f;" u2="&#x105;" k="110" />
    <hkern u1="&#x10f;" u2="&#x103;" k="110" />
    <hkern u1="&#x10f;" u2="&#x101;" k="110" />
    <hkern u1="&#x10f;" u2="&#xff;" k="60" />
    <hkern u1="&#x10f;" u2="&#xfd;" k="60" />
    <hkern u1="&#x10f;" u2="&#xfc;" k="90" />
    <hkern u1="&#x10f;" u2="&#xfb;" k="90" />
    <hkern u1="&#x10f;" u2="&#xfa;" k="90" />
    <hkern u1="&#x10f;" u2="&#xf9;" k="90" />
    <hkern u1="&#x10f;" u2="&#xf8;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf6;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf5;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf4;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf3;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf2;" k="110" />
    <hkern u1="&#x10f;" u2="&#xf1;" k="90" />
    <hkern u1="&#x10f;" u2="&#xeb;" k="120" />
    <hkern u1="&#x10f;" u2="&#xea;" k="120" />
    <hkern u1="&#x10f;" u2="&#xe9;" k="120" />
    <hkern u1="&#x10f;" u2="&#xe8;" k="120" />
    <hkern u1="&#x10f;" u2="&#xe7;" k="120" />
    <hkern u1="&#x10f;" u2="&#xe6;" k="110" />
    <hkern u1="&#x10f;" u2="&#xe5;" k="110" />
    <hkern u1="&#x10f;" u2="&#xe4;" k="40" />
    <hkern u1="&#x10f;" u2="&#xe3;" k="110" />
    <hkern u1="&#x10f;" u2="&#xe2;" k="110" />
    <hkern u1="&#x10f;" u2="&#xe1;" k="110" />
    <hkern u1="&#x10f;" u2="&#xe0;" k="110" />
    <hkern u1="&#x10f;" u2="z" k="90" />
    <hkern u1="&#x10f;" u2="y" k="60" />
    <hkern u1="&#x10f;" u2="w" k="60" />
    <hkern u1="&#x10f;" u2="u" k="90" />
    <hkern u1="&#x10f;" u2="t" k="40" />
    <hkern u1="&#x10f;" u2="s" k="120" />
    <hkern u1="&#x10f;" u2="r" k="90" />
    <hkern u1="&#x10f;" u2="o" k="110" />
    <hkern u1="&#x10f;" u2="n" k="90" />
    <hkern u1="&#x10f;" u2="h" k="10" />
    <hkern u1="&#x10f;" u2="g" k="110" />
    <hkern u1="&#x10f;" u2="f" k="20" />
    <hkern u1="&#x10f;" u2="e" k="120" />
    <hkern u1="&#x10f;" u2="d" k="110" />
    <hkern u1="&#x10f;" u2="c" k="120" />
    <hkern u1="&#x10f;" u2="a" k="110" />
    <hkern u1="&#x110;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x110;" u2="&#xb8;" k="13" />
    <hkern u1="&#x112;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x113;" u2="v" k="7" />
    <hkern u1="&#x116;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x117;" u2="v" k="7" />
    <hkern u1="&#x118;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x119;" u2="v" k="7" />
    <hkern u1="&#x11a;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x11b;" u2="v" k="7" />
    <hkern u1="&#x11c;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x11c;" u2="&#xb8;" k="9" />
    <hkern u1="&#x11e;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x11e;" u2="&#xb8;" k="9" />
    <hkern u1="&#x120;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x120;" u2="&#xb8;" k="9" />
    <hkern u1="&#x122;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x122;" u2="&#xb8;" k="9" />
    <hkern u1="&#x124;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x126;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x12a;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x12e;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x130;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x134;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x136;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x136;" u2="&#x2dd;" k="12" />
    <hkern u1="&#x136;" u2="&#xe6;" k="12" />
    <hkern u1="&#x139;" u2="&#x2dd;" k="3" />
    <hkern u1="&#x139;" u2="&#xe6;" k="3" />
    <hkern u1="&#x139;" u2="&#xb8;" k="-31" />
    <hkern u1="&#x13b;" u2="&#x2dd;" k="3" />
    <hkern u1="&#x13b;" u2="&#xe6;" k="3" />
    <hkern u1="&#x13b;" u2="&#xb8;" k="-31" />
    <hkern u1="&#x13d;" u2="&#x2dd;" k="3" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="-32" />
    <hkern u1="&#x13d;" u2="&#x178;" k="-34" />
    <hkern u1="&#x13d;" u2="&#x164;" k="-32" />
    <hkern u1="&#x13d;" u2="&#x162;" k="-32" />
    <hkern u1="&#x13d;" u2="&#xe6;" k="3" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="-34" />
    <hkern u1="&#x13d;" u2="&#xb8;" k="-31" />
    <hkern u1="&#x13d;" u2="Y" k="-34" />
    <hkern u1="&#x13d;" u2="W" k="-28" />
    <hkern u1="&#x13d;" u2="V" k="-25" />
    <hkern u1="&#x13d;" u2="T" k="-32" />
    <hkern u1="&#x13e;" u2="&#x259;" k="110" />
    <hkern u1="&#x13e;" u2="&#x21b;" k="30" />
    <hkern u1="&#x13e;" u2="&#x219;" k="110" />
    <hkern u1="&#x13e;" u2="&#x1fd;" k="110" />
    <hkern u1="&#x13e;" u2="&#x1dd;" k="110" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="20" />
    <hkern u1="&#x13e;" u2="&#x17c;" k="80" />
    <hkern u1="&#x13e;" u2="&#x17a;" k="80" />
    <hkern u1="&#x13e;" u2="&#x173;" k="90" />
    <hkern u1="&#x13e;" u2="&#x171;" k="90" />
    <hkern u1="&#x13e;" u2="&#x16f;" k="60" />
    <hkern u1="&#x13e;" u2="&#x16d;" k="90" />
    <hkern u1="&#x13e;" u2="&#x16b;" k="90" />
    <hkern u1="&#x13e;" u2="&#x169;" k="90" />
    <hkern u1="&#x13e;" u2="&#x165;" k="30" />
    <hkern u1="&#x13e;" u2="&#x163;" k="30" />
    <hkern u1="&#x13e;" u2="&#x161;" k="30" />
    <hkern u1="&#x13e;" u2="&#x15f;" k="110" />
    <hkern u1="&#x13e;" u2="&#x15d;" k="110" />
    <hkern u1="&#x13e;" u2="&#x15b;" k="110" />
    <hkern u1="&#x13e;" u2="&#x159;" k="90" />
    <hkern u1="&#x13e;" u2="&#x157;" k="90" />
    <hkern u1="&#x13e;" u2="&#x155;" k="90" />
    <hkern u1="&#x13e;" u2="&#x153;" k="110" />
    <hkern u1="&#x13e;" u2="&#x151;" k="110" />
    <hkern u1="&#x13e;" u2="&#x14f;" k="110" />
    <hkern u1="&#x13e;" u2="&#x14d;" k="110" />
    <hkern u1="&#x13e;" u2="&#x148;" k="50" />
    <hkern u1="&#x13e;" u2="&#x146;" k="90" />
    <hkern u1="&#x13e;" u2="&#x144;" k="90" />
    <hkern u1="&#x13e;" u2="&#x142;" k="4" />
    <hkern u1="&#x13e;" u2="&#x13e;" k="4" />
    <hkern u1="&#x13e;" u2="&#x13c;" k="4" />
    <hkern u1="&#x13e;" u2="&#x13a;" k="4" />
    <hkern u1="&#x13e;" u2="&#x127;" k="90" />
    <hkern u1="&#x13e;" u2="&#x125;" k="90" />
    <hkern u1="&#x13e;" u2="&#x123;" k="110" />
    <hkern u1="&#x13e;" u2="&#x121;" k="110" />
    <hkern u1="&#x13e;" u2="&#x11f;" k="110" />
    <hkern u1="&#x13e;" u2="&#x11d;" k="110" />
    <hkern u1="&#x13e;" u2="&#x11b;" k="44" />
    <hkern u1="&#x13e;" u2="&#x119;" k="120" />
    <hkern u1="&#x13e;" u2="&#x117;" k="120" />
    <hkern u1="&#x13e;" u2="&#x113;" k="120" />
    <hkern u1="&#x13e;" u2="&#x111;" k="110" />
    <hkern u1="&#x13e;" u2="&#x10f;" k="110" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="50" />
    <hkern u1="&#x13e;" u2="&#x10b;" k="110" />
    <hkern u1="&#x13e;" u2="&#x109;" k="110" />
    <hkern u1="&#x13e;" u2="&#x107;" k="110" />
    <hkern u1="&#x13e;" u2="&#x105;" k="110" />
    <hkern u1="&#x13e;" u2="&#x103;" k="110" />
    <hkern u1="&#x13e;" u2="&#x101;" k="110" />
    <hkern u1="&#x13e;" u2="&#xff;" k="60" />
    <hkern u1="&#x13e;" u2="&#xfd;" k="60" />
    <hkern u1="&#x13e;" u2="&#xfc;" k="90" />
    <hkern u1="&#x13e;" u2="&#xfb;" k="90" />
    <hkern u1="&#x13e;" u2="&#xfa;" k="90" />
    <hkern u1="&#x13e;" u2="&#xf9;" k="90" />
    <hkern u1="&#x13e;" u2="&#xf8;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf6;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf5;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf4;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf3;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf2;" k="110" />
    <hkern u1="&#x13e;" u2="&#xf1;" k="90" />
    <hkern u1="&#x13e;" u2="&#xeb;" k="120" />
    <hkern u1="&#x13e;" u2="&#xea;" k="120" />
    <hkern u1="&#x13e;" u2="&#xe9;" k="120" />
    <hkern u1="&#x13e;" u2="&#xe8;" k="120" />
    <hkern u1="&#x13e;" u2="&#xe7;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe6;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe5;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe4;" k="30" />
    <hkern u1="&#x13e;" u2="&#xe3;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe2;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe1;" k="110" />
    <hkern u1="&#x13e;" u2="&#xe0;" k="110" />
    <hkern u1="&#x13e;" u2="z" k="80" />
    <hkern u1="&#x13e;" u2="y" k="60" />
    <hkern u1="&#x13e;" u2="w" k="60" />
    <hkern u1="&#x13e;" u2="u" k="90" />
    <hkern u1="&#x13e;" u2="t" k="30" />
    <hkern u1="&#x13e;" u2="s" k="110" />
    <hkern u1="&#x13e;" u2="r" k="90" />
    <hkern u1="&#x13e;" u2="o" k="110" />
    <hkern u1="&#x13e;" u2="n" k="90" />
    <hkern u1="&#x13e;" u2="l" k="4" />
    <hkern u1="&#x13e;" u2="k" k="4" />
    <hkern u1="&#x13e;" u2="h" k="4" />
    <hkern u1="&#x13e;" u2="g" k="110" />
    <hkern u1="&#x13e;" u2="f" k="20" />
    <hkern u1="&#x13e;" u2="e" k="120" />
    <hkern u1="&#x13e;" u2="d" k="110" />
    <hkern u1="&#x13e;" u2="c" k="110" />
    <hkern u1="&#x13e;" u2="b" k="4" />
    <hkern u1="&#x13e;" u2="a" k="110" />
    <hkern u1="&#x13f;" u2="&#x2dd;" k="3" />
    <hkern u1="&#x13f;" u2="&#xe6;" k="3" />
    <hkern u1="&#x13f;" u2="&#xb8;" k="-31" />
    <hkern u1="&#x141;" u2="&#x2dd;" k="3" />
    <hkern u1="&#x141;" u2="&#xe6;" k="3" />
    <hkern u1="&#x141;" u2="&#xb8;" k="-31" />
    <hkern u1="&#x143;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x145;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x147;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x14c;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x14c;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x14c;" u2="&#xb8;" k="11" />
    <hkern u1="&#x14c;" u2="X" k="9" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x14d;" u2="x" k="11" />
    <hkern u1="&#x14d;" u2="v" k="9" />
    <hkern u1="&#x14e;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x14e;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x14e;" u2="&#xb8;" k="11" />
    <hkern u1="&#x14e;" u2="X" k="9" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x14f;" u2="x" k="11" />
    <hkern u1="&#x14f;" u2="v" k="9" />
    <hkern u1="&#x150;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x150;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x150;" u2="&#xb8;" k="11" />
    <hkern u1="&#x150;" u2="X" k="9" />
    <hkern u1="&#x151;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x151;" u2="x" k="11" />
    <hkern u1="&#x151;" u2="v" k="9" />
    <hkern u1="&#x152;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x153;" u2="v" k="7" />
    <hkern u1="&#x154;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x154;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x154;" u2="&#xe6;" k="13" />
    <hkern u1="&#x154;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x154;" u2="J" k="11" />
    <hkern u1="&#x155;" u2="x" k="-13" />
    <hkern u1="&#x155;" u2="v" k="-11" />
    <hkern u1="&#x155;" u2="f" k="-16" />
    <hkern u1="&#x156;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x156;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x156;" u2="&#xe6;" k="13" />
    <hkern u1="&#x156;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x156;" u2="J" k="11" />
    <hkern u1="&#x157;" u2="x" k="-13" />
    <hkern u1="&#x157;" u2="v" k="-11" />
    <hkern u1="&#x157;" u2="f" k="-16" />
    <hkern u1="&#x158;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x158;" u2="&#x2dd;" k="13" />
    <hkern u1="&#x158;" u2="&#xe6;" k="13" />
    <hkern u1="&#x158;" u2="&#xb8;" k="-18" />
    <hkern u1="&#x158;" u2="J" k="11" />
    <hkern u1="&#x159;" u2="x" k="-13" />
    <hkern u1="&#x159;" u2="v" k="-11" />
    <hkern u1="&#x159;" u2="f" k="-16" />
    <hkern u1="&#x15a;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x15c;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x15e;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x160;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x162;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x162;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x162;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x162;" u2="&#x1d0;" k="-60" />
    <hkern u1="&#x162;" u2="&#x17e;" k="10" />
    <hkern u1="&#x162;" u2="&#x161;" k="26" />
    <hkern u1="&#x162;" u2="&#x15d;" k="26" />
    <hkern u1="&#x162;" u2="&#x159;" k="-10" />
    <hkern u1="&#x162;" u2="&#x12b;" k="-87" />
    <hkern u1="&#x162;" u2="&#x113;" k="-1" />
    <hkern u1="&#x162;" u2="&#xff;" k="1" />
    <hkern u1="&#x162;" u2="&#xef;" k="-107" />
    <hkern u1="&#x162;" u2="&#xee;" k="-57" />
    <hkern u1="&#x162;" u2="&#xed;" k="-17" />
    <hkern u1="&#x162;" u2="&#xec;" k="-47" />
    <hkern u1="&#x162;" u2="&#xeb;" k="-1" />
    <hkern u1="&#x162;" u2="&#xb8;" k="14" />
    <hkern u1="&#x162;" u2="x" k="50" />
    <hkern u1="&#x162;" u2="v" k="20" />
    <hkern u1="&#x162;" u2="q" k="40" />
    <hkern u1="&#x162;" u2="p" k="20" />
    <hkern u1="&#x162;" u2="m" k="30" />
    <hkern u1="&#x162;" u2="f" k="20" />
    <hkern u1="&#x162;" u2="J" k="31" />
    <hkern u1="&#x162;" u2="&#x2e;" k="14" />
    <hkern u1="&#x163;" u2="v" k="-32" />
    <hkern u1="&#x163;" u2="h" k="-11" />
    <hkern u1="&#x163;" u2="f" k="-32" />
    <hkern u1="&#x164;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x164;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x164;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x164;" u2="&#x1d0;" k="-60" />
    <hkern u1="&#x164;" u2="&#x17e;" k="10" />
    <hkern u1="&#x164;" u2="&#x161;" k="26" />
    <hkern u1="&#x164;" u2="&#x15d;" k="26" />
    <hkern u1="&#x164;" u2="&#x159;" k="-10" />
    <hkern u1="&#x164;" u2="&#x12b;" k="-87" />
    <hkern u1="&#x164;" u2="&#x113;" k="-1" />
    <hkern u1="&#x164;" u2="&#xff;" k="1" />
    <hkern u1="&#x164;" u2="&#xef;" k="-107" />
    <hkern u1="&#x164;" u2="&#xee;" k="-57" />
    <hkern u1="&#x164;" u2="&#xed;" k="-17" />
    <hkern u1="&#x164;" u2="&#xec;" k="-47" />
    <hkern u1="&#x164;" u2="&#xeb;" k="-1" />
    <hkern u1="&#x164;" u2="&#xb8;" k="14" />
    <hkern u1="&#x164;" u2="x" k="50" />
    <hkern u1="&#x164;" u2="v" k="20" />
    <hkern u1="&#x164;" u2="q" k="40" />
    <hkern u1="&#x164;" u2="p" k="20" />
    <hkern u1="&#x164;" u2="m" k="30" />
    <hkern u1="&#x164;" u2="f" k="20" />
    <hkern u1="&#x164;" u2="J" k="31" />
    <hkern u1="&#x164;" u2="&#x2e;" k="14" />
    <hkern u1="&#x165;" u2="&#x259;" k="110" />
    <hkern u1="&#x165;" u2="&#x21b;" k="50" />
    <hkern u1="&#x165;" u2="&#x219;" k="110" />
    <hkern u1="&#x165;" u2="&#x1fd;" k="110" />
    <hkern u1="&#x165;" u2="&#x1dd;" k="110" />
    <hkern u1="&#x165;" u2="&#x17e;" k="40" />
    <hkern u1="&#x165;" u2="&#x17c;" k="110" />
    <hkern u1="&#x165;" u2="&#x17a;" k="110" />
    <hkern u1="&#x165;" u2="&#x173;" k="110" />
    <hkern u1="&#x165;" u2="&#x171;" k="110" />
    <hkern u1="&#x165;" u2="&#x16f;" k="74" />
    <hkern u1="&#x165;" u2="&#x16d;" k="110" />
    <hkern u1="&#x165;" u2="&#x16b;" k="110" />
    <hkern u1="&#x165;" u2="&#x169;" k="110" />
    <hkern u1="&#x165;" u2="&#x165;" k="50" />
    <hkern u1="&#x165;" u2="&#x163;" k="50" />
    <hkern u1="&#x165;" u2="&#x161;" k="50" />
    <hkern u1="&#x165;" u2="&#x15f;" k="110" />
    <hkern u1="&#x165;" u2="&#x15d;" k="110" />
    <hkern u1="&#x165;" u2="&#x15b;" k="110" />
    <hkern u1="&#x165;" u2="&#x159;" k="18" />
    <hkern u1="&#x165;" u2="&#x157;" k="100" />
    <hkern u1="&#x165;" u2="&#x155;" k="100" />
    <hkern u1="&#x165;" u2="&#x153;" k="110" />
    <hkern u1="&#x165;" u2="&#x151;" k="110" />
    <hkern u1="&#x165;" u2="&#x14f;" k="110" />
    <hkern u1="&#x165;" u2="&#x14d;" k="110" />
    <hkern u1="&#x165;" u2="&#x148;" k="75" />
    <hkern u1="&#x165;" u2="&#x146;" k="90" />
    <hkern u1="&#x165;" u2="&#x144;" k="90" />
    <hkern u1="&#x165;" u2="&#x142;" k="8" />
    <hkern u1="&#x165;" u2="&#x13e;" k="8" />
    <hkern u1="&#x165;" u2="&#x13c;" k="8" />
    <hkern u1="&#x165;" u2="&#x13a;" k="8" />
    <hkern u1="&#x165;" u2="&#x131;" k="12" />
    <hkern u1="&#x165;" u2="&#x12f;" k="12" />
    <hkern u1="&#x165;" u2="&#x12b;" k="12" />
    <hkern u1="&#x165;" u2="&#x127;" k="90" />
    <hkern u1="&#x165;" u2="&#x125;" k="90" />
    <hkern u1="&#x165;" u2="&#x123;" k="110" />
    <hkern u1="&#x165;" u2="&#x121;" k="110" />
    <hkern u1="&#x165;" u2="&#x11f;" k="110" />
    <hkern u1="&#x165;" u2="&#x11d;" k="110" />
    <hkern u1="&#x165;" u2="&#x11b;" k="54" />
    <hkern u1="&#x165;" u2="&#x119;" k="110" />
    <hkern u1="&#x165;" u2="&#x117;" k="110" />
    <hkern u1="&#x165;" u2="&#x113;" k="110" />
    <hkern u1="&#x165;" u2="&#x111;" k="104" />
    <hkern u1="&#x165;" u2="&#x10f;" k="104" />
    <hkern u1="&#x165;" u2="&#x10d;" k="70" />
    <hkern u1="&#x165;" u2="&#x10b;" k="110" />
    <hkern u1="&#x165;" u2="&#x109;" k="110" />
    <hkern u1="&#x165;" u2="&#x107;" k="110" />
    <hkern u1="&#x165;" u2="&#x105;" k="110" />
    <hkern u1="&#x165;" u2="&#x103;" k="110" />
    <hkern u1="&#x165;" u2="&#x101;" k="110" />
    <hkern u1="&#x165;" u2="&#xff;" k="80" />
    <hkern u1="&#x165;" u2="&#xfd;" k="80" />
    <hkern u1="&#x165;" u2="&#xfc;" k="110" />
    <hkern u1="&#x165;" u2="&#xfb;" k="110" />
    <hkern u1="&#x165;" u2="&#xfa;" k="110" />
    <hkern u1="&#x165;" u2="&#xf9;" k="110" />
    <hkern u1="&#x165;" u2="&#xf8;" k="110" />
    <hkern u1="&#x165;" u2="&#xf6;" k="110" />
    <hkern u1="&#x165;" u2="&#xf5;" k="110" />
    <hkern u1="&#x165;" u2="&#xf4;" k="110" />
    <hkern u1="&#x165;" u2="&#xf3;" k="110" />
    <hkern u1="&#x165;" u2="&#xf2;" k="110" />
    <hkern u1="&#x165;" u2="&#xf1;" k="90" />
    <hkern u1="&#x165;" u2="&#xeb;" k="110" />
    <hkern u1="&#x165;" u2="&#xea;" k="110" />
    <hkern u1="&#x165;" u2="&#xe9;" k="110" />
    <hkern u1="&#x165;" u2="&#xe8;" k="110" />
    <hkern u1="&#x165;" u2="&#xe7;" k="110" />
    <hkern u1="&#x165;" u2="&#xe6;" k="110" />
    <hkern u1="&#x165;" u2="&#xe5;" k="110" />
    <hkern u1="&#x165;" u2="&#xe4;" k="44" />
    <hkern u1="&#x165;" u2="&#xe3;" k="110" />
    <hkern u1="&#x165;" u2="&#xe2;" k="110" />
    <hkern u1="&#x165;" u2="&#xe1;" k="110" />
    <hkern u1="&#x165;" u2="&#xe0;" k="110" />
    <hkern u1="&#x165;" u2="z" k="110" />
    <hkern u1="&#x165;" u2="y" k="80" />
    <hkern u1="&#x165;" u2="w" k="80" />
    <hkern u1="&#x165;" u2="v" k="-32" />
    <hkern u1="&#x165;" u2="u" k="110" />
    <hkern u1="&#x165;" u2="t" k="50" />
    <hkern u1="&#x165;" u2="s" k="110" />
    <hkern u1="&#x165;" u2="r" k="100" />
    <hkern u1="&#x165;" u2="o" k="110" />
    <hkern u1="&#x165;" u2="n" k="90" />
    <hkern u1="&#x165;" u2="l" k="8" />
    <hkern u1="&#x165;" u2="k" k="8" />
    <hkern u1="&#x165;" u2="i" k="12" />
    <hkern u1="&#x165;" u2="h" k="10" />
    <hkern u1="&#x165;" u2="g" k="110" />
    <hkern u1="&#x165;" u2="f" k="30" />
    <hkern u1="&#x165;" u2="e" k="110" />
    <hkern u1="&#x165;" u2="d" k="104" />
    <hkern u1="&#x165;" u2="c" k="110" />
    <hkern u1="&#x165;" u2="b" k="8" />
    <hkern u1="&#x165;" u2="a" k="110" />
    <hkern u1="&#x168;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x168;" u2="&#xb8;" k="5" />
    <hkern u1="&#x16a;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x16a;" u2="&#xb8;" k="5" />
    <hkern u1="&#x16c;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x16c;" u2="&#xb8;" k="5" />
    <hkern u1="&#x16e;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x16e;" u2="&#xb8;" k="5" />
    <hkern u1="&#x170;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x170;" u2="&#xb8;" k="5" />
    <hkern u1="&#x172;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x172;" u2="&#xb8;" k="5" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x178;" u2="&#x2dd;" k="25" />
    <hkern u1="&#x178;" u2="&#x1d0;" k="-40" />
    <hkern u1="&#x178;" u2="&#x17e;" k="30" />
    <hkern u1="&#x178;" u2="&#x161;" k="40" />
    <hkern u1="&#x178;" u2="&#x159;" k="20" />
    <hkern u1="&#x178;" u2="&#x12b;" k="-50" />
    <hkern u1="&#x178;" u2="&#xff;" k="30" />
    <hkern u1="&#x178;" u2="&#xef;" k="-70" />
    <hkern u1="&#x178;" u2="&#xee;" k="-20" />
    <hkern u1="&#x178;" u2="&#xed;" k="10" />
    <hkern u1="&#x178;" u2="&#xec;" k="-40" />
    <hkern u1="&#x178;" u2="&#xe6;" k="25" />
    <hkern u1="&#x178;" u2="&#xb8;" k="34" />
    <hkern u1="&#x178;" u2="x" k="70" />
    <hkern u1="&#x178;" u2="v" k="40" />
    <hkern u1="&#x178;" u2="q" k="50" />
    <hkern u1="&#x178;" u2="p" k="38" />
    <hkern u1="&#x178;" u2="m" k="40" />
    <hkern u1="&#x178;" u2="b" k="10" />
    <hkern u1="&#x178;" u2="Q" k="31" />
    <hkern u1="&#x178;" u2="J" k="52" />
    <hkern u1="&#x178;" u2="&#x3a;" k="13" />
    <hkern u1="&#x179;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x179;" u2="Q" k="31" />
    <hkern u1="&#x17b;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x17b;" u2="Q" k="31" />
    <hkern u1="&#x17d;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x17d;" u2="Q" k="31" />
    <hkern u1="&#x18f;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x18f;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x18f;" u2="&#xb8;" k="11" />
    <hkern u1="&#x18f;" u2="X" k="9" />
    <hkern u1="&#x1dd;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x1dd;" u2="x" k="11" />
    <hkern u1="&#x1dd;" u2="v" k="9" />
    <hkern u1="&#x1fc;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x1fd;" u2="v" k="7" />
    <hkern u1="&#x218;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x21a;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x21a;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x21a;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x21a;" u2="&#x1d0;" k="-60" />
    <hkern u1="&#x21a;" u2="&#x17e;" k="10" />
    <hkern u1="&#x21a;" u2="&#x161;" k="26" />
    <hkern u1="&#x21a;" u2="&#x15d;" k="26" />
    <hkern u1="&#x21a;" u2="&#x159;" k="-10" />
    <hkern u1="&#x21a;" u2="&#x12b;" k="-87" />
    <hkern u1="&#x21a;" u2="&#x113;" k="-1" />
    <hkern u1="&#x21a;" u2="&#xff;" k="1" />
    <hkern u1="&#x21a;" u2="&#xef;" k="-107" />
    <hkern u1="&#x21a;" u2="&#xee;" k="-57" />
    <hkern u1="&#x21a;" u2="&#xed;" k="-17" />
    <hkern u1="&#x21a;" u2="&#xec;" k="-47" />
    <hkern u1="&#x21a;" u2="&#xeb;" k="-1" />
    <hkern u1="&#x21a;" u2="&#xb8;" k="14" />
    <hkern u1="&#x21a;" u2="x" k="50" />
    <hkern u1="&#x21a;" u2="v" k="20" />
    <hkern u1="&#x21a;" u2="q" k="40" />
    <hkern u1="&#x21a;" u2="p" k="20" />
    <hkern u1="&#x21a;" u2="m" k="30" />
    <hkern u1="&#x21a;" u2="f" k="20" />
    <hkern u1="&#x21a;" u2="J" k="31" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="14" />
    <hkern u1="&#x21b;" u2="v" k="-32" />
    <hkern u1="&#x21b;" u2="h" k="-11" />
    <hkern u1="&#x21b;" u2="f" k="-32" />
    <hkern u1="&#x259;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x259;" u2="x" k="11" />
    <hkern u1="&#x259;" u2="v" k="9" />
    <hkern u1="&#x2dd;" u2="&#x1fc;" k="11" />
    <hkern u1="&#x2dd;" u2="&#x178;" k="26" />
    <hkern u1="&#x2dd;" u2="&#x104;" k="11" />
    <hkern u1="&#x2dd;" u2="&#x102;" k="11" />
    <hkern u1="&#x2dd;" u2="&#x100;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xdd;" k="26" />
    <hkern u1="&#x2dd;" u2="&#xc6;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc5;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc4;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc3;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc2;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc1;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xc0;" k="11" />
    <hkern u1="&#x2dd;" u2="&#xb8;" k="11" />
    <hkern u1="&#x2dd;" u2="Y" k="26" />
    <hkern u1="&#x2dd;" u2="X" k="9" />
    <hkern u1="&#x2dd;" u2="W" k="7" />
    <hkern u1="&#x2dd;" u2="V" k="14" />
    <hkern u1="&#x2dd;" u2="A" k="11" />
    <hkern u1="&#x401;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x401;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x401;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x401;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x401;" u2="&#x447;" k="21" />
    <hkern u1="&#x402;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x402;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x402;" u2="&#x4f4;" k="52" />
    <hkern u1="&#x402;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x402;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x402;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x402;" u2="&#x4b8;" k="52" />
    <hkern u1="&#x402;" u2="&#x4b6;" k="52" />
    <hkern u1="&#x402;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x402;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x402;" u2="&#x42f;" k="21" />
    <hkern u1="&#x402;" u2="&#x42a;" k="41" />
    <hkern u1="&#x402;" u2="&#x427;" k="52" />
    <hkern u1="&#x402;" u2="&#x424;" k="21" />
    <hkern u1="&#x402;" u2="&#x423;" k="52" />
    <hkern u1="&#x402;" u2="&#x422;" k="41" />
    <hkern u1="&#x402;" u2="&#x421;" k="21" />
    <hkern u1="&#x402;" u2="&#x40e;" k="52" />
    <hkern u1="&#x402;" u2="&#x40b;" k="41" />
    <hkern u1="&#x402;" u2="&#x402;" k="41" />
    <hkern u1="&#x403;" g2="afii10071.var.accented" k="33" />
    <hkern u1="&#x403;" g2="afii10097.var.accented" k="73" />
    <hkern u1="&#x403;" g2="afii10095.var.accented" k="63" />
    <hkern u1="&#x403;" g2="afii10049.var.accented" k="42" />
    <hkern u1="&#x403;" g2="afii10037.var.accented" k="11" />
    <hkern u1="&#x403;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x403;" u2="&#x2026;" k="52" />
    <hkern u1="&#x403;" u2="&#x201d;" k="-31" />
    <hkern u1="&#x403;" u2="&#x2019;" k="-31" />
    <hkern u1="&#x403;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x403;" u2="&#x4f5;" k="73" />
    <hkern u1="&#x403;" u2="&#x4f2;" k="11" />
    <hkern u1="&#x403;" u2="&#x4f0;" k="11" />
    <hkern u1="&#x403;" u2="&#x4ee;" k="11" />
    <hkern u1="&#x403;" u2="&#x4e5;" k="52" />
    <hkern u1="&#x403;" u2="&#x4e3;" k="52" />
    <hkern u1="&#x403;" u2="&#x4dd;" k="73" />
    <hkern u1="&#x403;" u2="&#x4dc;" k="11" />
    <hkern u1="&#x403;" u2="&#x4c4;" k="52" />
    <hkern u1="&#x403;" u2="&#x4c2;" k="73" />
    <hkern u1="&#x403;" u2="&#x4c1;" k="11" />
    <hkern u1="&#x403;" u2="&#x4b9;" k="73" />
    <hkern u1="&#x403;" u2="&#x4b7;" k="73" />
    <hkern u1="&#x403;" u2="&#x4b3;" k="94" />
    <hkern u1="&#x403;" u2="&#x4ae;" k="11" />
    <hkern u1="&#x403;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x403;" u2="&#x49d;" k="52" />
    <hkern u1="&#x403;" u2="&#x49b;" k="52" />
    <hkern u1="&#x403;" u2="&#x497;" k="73" />
    <hkern u1="&#x403;" u2="&#x496;" k="11" />
    <hkern u1="&#x403;" u2="&#x473;" k="73" />
    <hkern u1="&#x403;" u2="&#x472;" k="42" />
    <hkern u1="&#x403;" u2="&#x45e;" k="23" />
    <hkern u1="&#x403;" u2="&#x458;" k="1" />
    <hkern u1="&#x403;" u2="&#x457;" k="2" />
    <hkern u1="&#x403;" u2="&#x456;" k="1" />
    <hkern u1="&#x403;" u2="&#x455;" k="90" />
    <hkern u1="&#x403;" u2="&#x451;" k="33" />
    <hkern u1="&#x403;" u2="&#x44f;" k="83" />
    <hkern u1="&#x403;" u2="&#x44d;" k="73" />
    <hkern u1="&#x403;" u2="&#x44a;" k="83" />
    <hkern u1="&#x403;" u2="&#x447;" k="93" />
    <hkern u1="&#x403;" u2="&#x445;" k="94" />
    <hkern u1="&#x403;" u2="&#x444;" k="83" />
    <hkern u1="&#x403;" u2="&#x442;" k="73" />
    <hkern u1="&#x403;" u2="&#x43c;" k="52" />
    <hkern u1="&#x403;" u2="&#x436;" k="73" />
    <hkern u1="&#x403;" u2="&#x434;" k="73" />
    <hkern u1="&#x403;" u2="&#x431;" k="52" />
    <hkern u1="&#x403;" u2="&#x42f;" k="42" />
    <hkern u1="&#x403;" u2="&#x424;" k="31" />
    <hkern u1="&#x403;" u2="&#x423;" k="11" />
    <hkern u1="&#x403;" u2="&#x421;" k="52" />
    <hkern u1="&#x403;" u2="&#x41c;" k="31" />
    <hkern u1="&#x403;" u2="&#x416;" k="11" />
    <hkern u1="&#x403;" u2="&#x414;" k="42" />
    <hkern u1="&#x403;" u2="&#x40e;" k="11" />
    <hkern u1="&#x403;" u2="&#x404;" k="52" />
    <hkern u1="&#x403;" u2="&#xae;" k="31" />
    <hkern u1="&#x403;" u2="&#xa9;" k="21" />
    <hkern u1="&#x403;" u2="&#x2e;" k="62" />
    <hkern u1="&#x404;" g2="afii10097.var.accented" k="21" />
    <hkern u1="&#x404;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x404;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x404;" u2="&#x201d;" k="-42" />
    <hkern u1="&#x404;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x404;" u2="&#x2019;" k="-42" />
    <hkern u1="&#x404;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x404;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x404;" u2="&#x4f4;" k="20" />
    <hkern u1="&#x404;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x404;" u2="&#x4b8;" k="20" />
    <hkern u1="&#x404;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x404;" u2="&#x4b6;" k="20" />
    <hkern u1="&#x404;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x404;" u2="&#x473;" k="21" />
    <hkern u1="&#x404;" u2="&#x472;" k="10" />
    <hkern u1="&#x404;" u2="&#x454;" k="10" />
    <hkern u1="&#x404;" u2="&#x44f;" k="21" />
    <hkern u1="&#x404;" u2="&#x44a;" k="31" />
    <hkern u1="&#x404;" u2="&#x447;" k="41" />
    <hkern u1="&#x404;" u2="&#x442;" k="31" />
    <hkern u1="&#x404;" u2="&#x431;" k="21" />
    <hkern u1="&#x404;" u2="&#x42f;" k="21" />
    <hkern u1="&#x404;" u2="&#x42a;" k="-21" />
    <hkern u1="&#x404;" u2="&#x427;" k="20" />
    <hkern u1="&#x404;" u2="&#x424;" k="21" />
    <hkern u1="&#x404;" u2="&#x421;" k="52" />
    <hkern u1="&#x404;" u2="&#x41b;" k="-20" />
    <hkern u1="&#x404;" u2="&#x409;" k="-20" />
    <hkern u1="&#x404;" u2="&#x404;" k="31" />
    <hkern u1="&#x406;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x407;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x407;" u2="&#x407;" k="-21" />
    <hkern u1="&#x409;" g2="afii10037.var.accented" k="63" />
    <hkern u1="&#x409;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x409;" g2="afii10022.var.accented" k="21" />
    <hkern u1="&#x409;" g2="afii10017.var.accented" k="21" />
    <hkern u1="&#x409;" u2="&#x2122;" k="20" />
    <hkern u1="&#x409;" u2="&#x201c;" k="62" />
    <hkern u1="&#x409;" u2="&#x2018;" k="72" />
    <hkern u1="&#x409;" u2="&#x4f4;" k="52" />
    <hkern u1="&#x409;" u2="&#x4f2;" k="63" />
    <hkern u1="&#x409;" u2="&#x4f0;" k="63" />
    <hkern u1="&#x409;" u2="&#x4ee;" k="63" />
    <hkern u1="&#x409;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x409;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x409;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x409;" u2="&#x4dc;" k="31" />
    <hkern u1="&#x409;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x409;" u2="&#x4d6;" k="21" />
    <hkern u1="&#x409;" u2="&#x4d2;" k="21" />
    <hkern u1="&#x409;" u2="&#x4d0;" k="21" />
    <hkern u1="&#x409;" u2="&#x4c1;" k="31" />
    <hkern u1="&#x409;" u2="&#x4b8;" k="52" />
    <hkern u1="&#x409;" u2="&#x4b6;" k="52" />
    <hkern u1="&#x409;" u2="&#x4b2;" k="21" />
    <hkern u1="&#x409;" u2="&#x4ae;" k="63" />
    <hkern u1="&#x409;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x409;" u2="&#x496;" k="31" />
    <hkern u1="&#x409;" u2="&#x472;" k="21" />
    <hkern u1="&#x409;" u2="&#x42a;" k="42" />
    <hkern u1="&#x409;" u2="&#x427;" k="52" />
    <hkern u1="&#x409;" u2="&#x425;" k="21" />
    <hkern u1="&#x409;" u2="&#x424;" k="21" />
    <hkern u1="&#x409;" u2="&#x423;" k="63" />
    <hkern u1="&#x409;" u2="&#x421;" k="21" />
    <hkern u1="&#x409;" u2="&#x41e;" k="21" />
    <hkern u1="&#x409;" u2="&#x41c;" k="10" />
    <hkern u1="&#x409;" u2="&#x416;" k="31" />
    <hkern u1="&#x409;" u2="&#x415;" k="21" />
    <hkern u1="&#x409;" u2="&#x410;" k="21" />
    <hkern u1="&#x409;" u2="&#x40e;" k="63" />
    <hkern u1="&#x409;" u2="&#x40b;" k="73" />
    <hkern u1="&#x409;" u2="&#x404;" k="21" />
    <hkern u1="&#x409;" u2="&#x402;" k="63" />
    <hkern u1="&#x409;" u2="&#x401;" k="21" />
    <hkern u1="&#x40a;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x40a;" g2="afii10022.var.accented" k="21" />
    <hkern u1="&#x40a;" u2="&#x2122;" k="20" />
    <hkern u1="&#x40a;" u2="&#x201d;" k="42" />
    <hkern u1="&#x40a;" u2="&#x201c;" k="53" />
    <hkern u1="&#x40a;" u2="&#x2019;" k="42" />
    <hkern u1="&#x40a;" u2="&#x2018;" k="41" />
    <hkern u1="&#x40a;" u2="&#x4f4;" k="62" />
    <hkern u1="&#x40a;" u2="&#x4de;" k="21" />
    <hkern u1="&#x40a;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x40a;" u2="&#x4d6;" k="21" />
    <hkern u1="&#x40a;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x40a;" u2="&#x4b8;" k="62" />
    <hkern u1="&#x40a;" u2="&#x4b6;" k="62" />
    <hkern u1="&#x40a;" u2="&#x4b2;" k="31" />
    <hkern u1="&#x40a;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x40a;" u2="&#x498;" k="21" />
    <hkern u1="&#x40a;" u2="&#x496;" k="21" />
    <hkern u1="&#x40a;" u2="&#x472;" k="10" />
    <hkern u1="&#x40a;" u2="&#x42d;" k="21" />
    <hkern u1="&#x40a;" u2="&#x42a;" k="42" />
    <hkern u1="&#x40a;" u2="&#x427;" k="62" />
    <hkern u1="&#x40a;" u2="&#x425;" k="31" />
    <hkern u1="&#x40a;" u2="&#x424;" k="10" />
    <hkern u1="&#x40a;" u2="&#x422;" k="42" />
    <hkern u1="&#x40a;" u2="&#x421;" k="10" />
    <hkern u1="&#x40a;" u2="&#x41c;" k="10" />
    <hkern u1="&#x40a;" u2="&#x417;" k="21" />
    <hkern u1="&#x40a;" u2="&#x416;" k="21" />
    <hkern u1="&#x40a;" u2="&#x415;" k="21" />
    <hkern u1="&#x40a;" u2="&#x40b;" k="73" />
    <hkern u1="&#x40a;" u2="&#x404;" k="21" />
    <hkern u1="&#x40a;" u2="&#x402;" k="52" />
    <hkern u1="&#x40a;" u2="&#x401;" k="21" />
    <hkern u1="&#x40b;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x40b;" u2="&#x4f4;" k="42" />
    <hkern u1="&#x40b;" u2="&#x4b8;" k="42" />
    <hkern u1="&#x40b;" u2="&#x4b6;" k="42" />
    <hkern u1="&#x40b;" u2="&#x4aa;" k="11" />
    <hkern u1="&#x40b;" u2="&#x427;" k="42" />
    <hkern u1="&#x40b;" u2="&#x421;" k="11" />
    <hkern u1="&#x40b;" u2="&#x40b;" k="31" />
    <hkern u1="&#x40b;" u2="&#x402;" k="31" />
    <hkern u1="&#x40c;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x40c;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10085.var.accented" k="21" />
    <hkern u1="&#x40c;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x40c;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10037.var.accented" k="10" />
    <hkern u1="&#x40c;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x40c;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x40c;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x40c;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4f3;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4f2;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4f1;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4f0;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4ef;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4ee;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4de;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x40c;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x40c;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4b2;" k="-21" />
    <hkern u1="&#x40c;" u2="&#x4af;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4ae;" k="10" />
    <hkern u1="&#x40c;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x40c;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x40c;" u2="&#x49d;" k="10" />
    <hkern u1="&#x40c;" u2="&#x49b;" k="10" />
    <hkern u1="&#x40c;" u2="&#x498;" k="21" />
    <hkern u1="&#x40c;" u2="&#x493;" k="10" />
    <hkern u1="&#x40c;" u2="&#x491;" k="10" />
    <hkern u1="&#x40c;" u2="&#x472;" k="21" />
    <hkern u1="&#x40c;" u2="&#x45e;" k="21" />
    <hkern u1="&#x40c;" u2="&#x45c;" k="10" />
    <hkern u1="&#x40c;" u2="&#x454;" k="10" />
    <hkern u1="&#x40c;" u2="&#x453;" k="10" />
    <hkern u1="&#x40c;" u2="&#x451;" k="10" />
    <hkern u1="&#x40c;" u2="&#x44f;" k="10" />
    <hkern u1="&#x40c;" u2="&#x44a;" k="31" />
    <hkern u1="&#x40c;" u2="&#x447;" k="21" />
    <hkern u1="&#x40c;" u2="&#x444;" k="21" />
    <hkern u1="&#x40c;" u2="&#x443;" k="21" />
    <hkern u1="&#x40c;" u2="&#x442;" k="31" />
    <hkern u1="&#x40c;" u2="&#x441;" k="21" />
    <hkern u1="&#x40c;" u2="&#x43e;" k="10" />
    <hkern u1="&#x40c;" u2="&#x43a;" k="10" />
    <hkern u1="&#x40c;" u2="&#x435;" k="10" />
    <hkern u1="&#x40c;" u2="&#x433;" k="10" />
    <hkern u1="&#x40c;" u2="&#x42f;" k="21" />
    <hkern u1="&#x40c;" u2="&#x42d;" k="10" />
    <hkern u1="&#x40c;" u2="&#x427;" k="21" />
    <hkern u1="&#x40c;" u2="&#x425;" k="-21" />
    <hkern u1="&#x40c;" u2="&#x424;" k="12" />
    <hkern u1="&#x40c;" u2="&#x423;" k="10" />
    <hkern u1="&#x40c;" u2="&#x421;" k="10" />
    <hkern u1="&#x40c;" u2="&#x41e;" k="21" />
    <hkern u1="&#x40c;" u2="&#x417;" k="21" />
    <hkern u1="&#x40c;" u2="&#x414;" k="-20" />
    <hkern u1="&#x40c;" u2="&#x410;" k="-10" />
    <hkern u1="&#x40c;" u2="&#x40e;" k="10" />
    <hkern u1="&#x40c;" u2="&#x40b;" k="10" />
    <hkern u1="&#x40c;" u2="&#x404;" k="21" />
    <hkern u1="&#x40c;" u2="&#x402;" k="10" />
    <hkern u1="&#x40e;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x40e;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x40e;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x40e;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x40e;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x40e;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x40e;" u2="&#x2026;" k="52" />
    <hkern u1="&#x40e;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x40e;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x40e;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x40e;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x40e;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x40e;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x40e;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x40e;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x40e;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x40e;" u2="&#x49d;" k="31" />
    <hkern u1="&#x40e;" u2="&#x49b;" k="31" />
    <hkern u1="&#x40e;" u2="&#x497;" k="41" />
    <hkern u1="&#x40e;" u2="&#x473;" k="62" />
    <hkern u1="&#x40e;" u2="&#x472;" k="31" />
    <hkern u1="&#x40e;" u2="&#x458;" k="1" />
    <hkern u1="&#x40e;" u2="&#x457;" k="1" />
    <hkern u1="&#x40e;" u2="&#x456;" k="11" />
    <hkern u1="&#x40e;" u2="&#x455;" k="70" />
    <hkern u1="&#x40e;" u2="&#x451;" k="42" />
    <hkern u1="&#x40e;" u2="&#x44f;" k="62" />
    <hkern u1="&#x40e;" u2="&#x44d;" k="62" />
    <hkern u1="&#x40e;" u2="&#x44a;" k="31" />
    <hkern u1="&#x40e;" u2="&#x447;" k="41" />
    <hkern u1="&#x40e;" u2="&#x445;" k="52" />
    <hkern u1="&#x40e;" u2="&#x444;" k="52" />
    <hkern u1="&#x40e;" u2="&#x442;" k="41" />
    <hkern u1="&#x40e;" u2="&#x43c;" k="52" />
    <hkern u1="&#x40e;" u2="&#x436;" k="50" />
    <hkern u1="&#x40e;" u2="&#x434;" k="31" />
    <hkern u1="&#x40e;" u2="&#x431;" k="51" />
    <hkern u1="&#x40e;" u2="&#x42f;" k="31" />
    <hkern u1="&#x40e;" u2="&#x42d;" k="20" />
    <hkern u1="&#x40e;" u2="&#x424;" k="31" />
    <hkern u1="&#x40e;" u2="&#x421;" k="52" />
    <hkern u1="&#x40e;" u2="&#x41c;" k="31" />
    <hkern u1="&#x40e;" u2="&#x414;" k="28" />
    <hkern u1="&#x40e;" u2="&#x409;" k="41" />
    <hkern u1="&#x40e;" u2="&#x404;" k="41" />
    <hkern u1="&#x40e;" u2="&#x3a;" k="31" />
    <hkern u1="&#x40e;" u2="&#x2e;" k="83" />
    <hkern u1="&#x40f;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x410;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x410;" g2="afii10049.var.accented" k="10" />
    <hkern u1="&#x410;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x410;" u2="&#x2026;" k="-21" />
    <hkern u1="&#x410;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x410;" u2="&#x4f4;" k="20" />
    <hkern u1="&#x410;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x410;" u2="&#x4b8;" k="20" />
    <hkern u1="&#x410;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x410;" u2="&#x4b6;" k="20" />
    <hkern u1="&#x410;" u2="&#x4b3;" k="-11" />
    <hkern u1="&#x410;" u2="&#x4b2;" k="-10" />
    <hkern u1="&#x410;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x410;" u2="&#x4ab;" k="10" />
    <hkern u1="&#x410;" u2="&#x4aa;" k="20" />
    <hkern u1="&#x410;" u2="&#x459;" k="-20" />
    <hkern u1="&#x410;" u2="&#x447;" k="10" />
    <hkern u1="&#x410;" u2="&#x445;" k="-11" />
    <hkern u1="&#x410;" u2="&#x442;" k="20" />
    <hkern u1="&#x410;" u2="&#x441;" k="10" />
    <hkern u1="&#x410;" u2="&#x434;" k="-21" />
    <hkern u1="&#x410;" u2="&#x42f;" k="10" />
    <hkern u1="&#x410;" u2="&#x427;" k="20" />
    <hkern u1="&#x410;" u2="&#x425;" k="-10" />
    <hkern u1="&#x410;" u2="&#x424;" k="20" />
    <hkern u1="&#x410;" u2="&#x421;" k="20" />
    <hkern u1="&#x410;" u2="&#x414;" k="-15" />
    <hkern u1="&#x410;" u2="&#x40b;" k="31" />
    <hkern u1="&#x410;" u2="&#x409;" k="-32" />
    <hkern u1="&#x410;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x410;" u2="&#x2a;" k="32" />
    <hkern u1="&#x411;" g2="afii10049.var.accented" k="32" />
    <hkern u1="&#x411;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x411;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x411;" u2="&#x4f4;" k="52" />
    <hkern u1="&#x411;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x411;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x411;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x411;" u2="&#x4de;" k="32" />
    <hkern u1="&#x411;" u2="&#x4b8;" k="52" />
    <hkern u1="&#x411;" u2="&#x4b6;" k="52" />
    <hkern u1="&#x411;" u2="&#x4b2;" k="32" />
    <hkern u1="&#x411;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x411;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x411;" u2="&#x498;" k="32" />
    <hkern u1="&#x411;" u2="&#x42f;" k="32" />
    <hkern u1="&#x411;" u2="&#x42d;" k="21" />
    <hkern u1="&#x411;" u2="&#x42a;" k="32" />
    <hkern u1="&#x411;" u2="&#x427;" k="52" />
    <hkern u1="&#x411;" u2="&#x425;" k="32" />
    <hkern u1="&#x411;" u2="&#x424;" k="21" />
    <hkern u1="&#x411;" u2="&#x423;" k="52" />
    <hkern u1="&#x411;" u2="&#x422;" k="32" />
    <hkern u1="&#x411;" u2="&#x421;" k="21" />
    <hkern u1="&#x411;" u2="&#x417;" k="32" />
    <hkern u1="&#x411;" u2="&#x40e;" k="52" />
    <hkern u1="&#x411;" u2="&#x40b;" k="32" />
    <hkern u1="&#x411;" u2="&#x404;" k="21" />
    <hkern u1="&#x411;" u2="&#x402;" k="42" />
    <hkern u1="&#x412;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x413;" g2="afii10071.var.accented" k="33" />
    <hkern u1="&#x413;" g2="afii10097.var.accented" k="73" />
    <hkern u1="&#x413;" g2="afii10095.var.accented" k="63" />
    <hkern u1="&#x413;" g2="afii10049.var.accented" k="42" />
    <hkern u1="&#x413;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x413;" u2="&#x2026;" k="52" />
    <hkern u1="&#x413;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x413;" u2="&#x4f5;" k="73" />
    <hkern u1="&#x413;" u2="&#x4e5;" k="52" />
    <hkern u1="&#x413;" u2="&#x4e3;" k="52" />
    <hkern u1="&#x413;" u2="&#x4dd;" k="73" />
    <hkern u1="&#x413;" u2="&#x4dc;" k="11" />
    <hkern u1="&#x413;" u2="&#x4c4;" k="52" />
    <hkern u1="&#x413;" u2="&#x4c2;" k="73" />
    <hkern u1="&#x413;" u2="&#x4c1;" k="11" />
    <hkern u1="&#x413;" u2="&#x4b9;" k="73" />
    <hkern u1="&#x413;" u2="&#x4b7;" k="73" />
    <hkern u1="&#x413;" u2="&#x4b3;" k="94" />
    <hkern u1="&#x413;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x413;" u2="&#x49d;" k="52" />
    <hkern u1="&#x413;" u2="&#x49b;" k="52" />
    <hkern u1="&#x413;" u2="&#x497;" k="73" />
    <hkern u1="&#x413;" u2="&#x496;" k="11" />
    <hkern u1="&#x413;" u2="&#x473;" k="73" />
    <hkern u1="&#x413;" u2="&#x472;" k="42" />
    <hkern u1="&#x413;" u2="&#x45e;" k="23" />
    <hkern u1="&#x413;" u2="&#x458;" k="1" />
    <hkern u1="&#x413;" u2="&#x457;" k="2" />
    <hkern u1="&#x413;" u2="&#x456;" k="1" />
    <hkern u1="&#x413;" u2="&#x455;" k="90" />
    <hkern u1="&#x413;" u2="&#x451;" k="33" />
    <hkern u1="&#x413;" u2="&#x44f;" k="83" />
    <hkern u1="&#x413;" u2="&#x44d;" k="73" />
    <hkern u1="&#x413;" u2="&#x44a;" k="83" />
    <hkern u1="&#x413;" u2="&#x447;" k="93" />
    <hkern u1="&#x413;" u2="&#x445;" k="94" />
    <hkern u1="&#x413;" u2="&#x444;" k="83" />
    <hkern u1="&#x413;" u2="&#x442;" k="73" />
    <hkern u1="&#x413;" u2="&#x43c;" k="52" />
    <hkern u1="&#x413;" u2="&#x436;" k="73" />
    <hkern u1="&#x413;" u2="&#x434;" k="73" />
    <hkern u1="&#x413;" u2="&#x431;" k="52" />
    <hkern u1="&#x413;" u2="&#x42f;" k="42" />
    <hkern u1="&#x413;" u2="&#x424;" k="31" />
    <hkern u1="&#x413;" u2="&#x421;" k="52" />
    <hkern u1="&#x413;" u2="&#x41c;" k="31" />
    <hkern u1="&#x413;" u2="&#x416;" k="11" />
    <hkern u1="&#x413;" u2="&#x414;" k="42" />
    <hkern u1="&#x413;" u2="&#x404;" k="52" />
    <hkern u1="&#x413;" u2="&#xae;" k="31" />
    <hkern u1="&#x413;" u2="&#xa9;" k="21" />
    <hkern u1="&#x413;" u2="&#x2e;" k="62" />
    <hkern u1="&#x414;" g2="hyphenminus" k="-63" />
    <hkern u1="&#x414;" u2="&#xf6bc;" k="-63" />
    <hkern u1="&#x414;" u2="&#xf6bb;" k="-63" />
    <hkern u1="&#x414;" u2="&#xf6ba;" k="-63" />
    <hkern u1="&#x414;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x414;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x414;" g2="afii10017.var.accented" k="-21" />
    <hkern u1="&#x414;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x414;" u2="&#x201d;" k="-31" />
    <hkern u1="&#x414;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x414;" u2="&#x2019;" k="-31" />
    <hkern u1="&#x414;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x414;" u2="&#x2014;" k="-63" />
    <hkern u1="&#x414;" u2="&#x2013;" k="-63" />
    <hkern u1="&#x414;" u2="&#x2012;" k="-63" />
    <hkern u1="&#x414;" u2="&#x2011;" k="-63" />
    <hkern u1="&#x414;" u2="&#x2010;" k="-63" />
    <hkern u1="&#x414;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x414;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x414;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x414;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x414;" u2="&#x4de;" k="21" />
    <hkern u1="&#x414;" u2="&#x4d2;" k="-21" />
    <hkern u1="&#x414;" u2="&#x4d0;" k="-21" />
    <hkern u1="&#x414;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x414;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x414;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x414;" u2="&#x498;" k="21" />
    <hkern u1="&#x414;" u2="&#x434;" k="-31" />
    <hkern u1="&#x414;" u2="&#x42d;" k="21" />
    <hkern u1="&#x414;" u2="&#x427;" k="21" />
    <hkern u1="&#x414;" u2="&#x423;" k="21" />
    <hkern u1="&#x414;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x414;" u2="&#x417;" k="21" />
    <hkern u1="&#x414;" u2="&#x414;" k="-31" />
    <hkern u1="&#x414;" u2="&#x410;" k="-21" />
    <hkern u1="&#x414;" u2="&#x40e;" k="21" />
    <hkern u1="&#x414;" u2="&#x409;" k="-31" />
    <hkern u1="&#x414;" u2="&#xad;" k="-63" />
    <hkern u1="&#x414;" u2="&#x2d;" k="-63" />
    <hkern u1="&#x415;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x415;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x415;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x415;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x415;" u2="&#x447;" k="21" />
    <hkern u1="&#x416;" g2="afii10049.var.accented" k="11" />
    <hkern u1="&#x416;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x416;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x416;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x416;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x416;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x416;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x416;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x416;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x416;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x416;" u2="&#x44a;" k="21" />
    <hkern u1="&#x416;" u2="&#x447;" k="21" />
    <hkern u1="&#x416;" u2="&#x444;" k="11" />
    <hkern u1="&#x416;" u2="&#x442;" k="32" />
    <hkern u1="&#x416;" u2="&#x441;" k="21" />
    <hkern u1="&#x416;" u2="&#x42f;" k="11" />
    <hkern u1="&#x416;" u2="&#x427;" k="21" />
    <hkern u1="&#x416;" u2="&#x424;" k="12" />
    <hkern u1="&#x416;" u2="&#x421;" k="21" />
    <hkern u1="&#x416;" u2="&#x414;" k="-20" />
    <hkern u1="&#x417;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x417;" g2="afii10047.var.accented" k="11" />
    <hkern u1="&#x417;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x417;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x417;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x417;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x417;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x417;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x417;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x417;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x417;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x417;" u2="&#x4de;" k="21" />
    <hkern u1="&#x417;" u2="&#x4b2;" k="11" />
    <hkern u1="&#x417;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x417;" u2="&#x498;" k="21" />
    <hkern u1="&#x417;" u2="&#x42f;" k="21" />
    <hkern u1="&#x417;" u2="&#x42d;" k="11" />
    <hkern u1="&#x417;" u2="&#x425;" k="11" />
    <hkern u1="&#x417;" u2="&#x423;" k="31" />
    <hkern u1="&#x417;" u2="&#x417;" k="21" />
    <hkern u1="&#x417;" u2="&#x40e;" k="31" />
    <hkern u1="&#x417;" u2="&#x402;" k="21" />
    <hkern u1="&#x418;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x419;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x41a;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x41a;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x41a;" g2="afii10085.var.accented" k="21" />
    <hkern u1="&#x41a;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x41a;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x41a;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x41a;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x41a;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x41a;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x41a;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x41a;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4f3;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4f1;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4ef;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4de;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x41a;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x41a;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x41a;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4b2;" k="-21" />
    <hkern u1="&#x41a;" u2="&#x4af;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x41a;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x41a;" u2="&#x49d;" k="10" />
    <hkern u1="&#x41a;" u2="&#x49b;" k="10" />
    <hkern u1="&#x41a;" u2="&#x498;" k="21" />
    <hkern u1="&#x41a;" u2="&#x493;" k="10" />
    <hkern u1="&#x41a;" u2="&#x491;" k="10" />
    <hkern u1="&#x41a;" u2="&#x472;" k="21" />
    <hkern u1="&#x41a;" u2="&#x45e;" k="21" />
    <hkern u1="&#x41a;" u2="&#x45c;" k="10" />
    <hkern u1="&#x41a;" u2="&#x454;" k="10" />
    <hkern u1="&#x41a;" u2="&#x453;" k="10" />
    <hkern u1="&#x41a;" u2="&#x451;" k="10" />
    <hkern u1="&#x41a;" u2="&#x44f;" k="10" />
    <hkern u1="&#x41a;" u2="&#x44a;" k="31" />
    <hkern u1="&#x41a;" u2="&#x447;" k="21" />
    <hkern u1="&#x41a;" u2="&#x444;" k="21" />
    <hkern u1="&#x41a;" u2="&#x443;" k="21" />
    <hkern u1="&#x41a;" u2="&#x442;" k="31" />
    <hkern u1="&#x41a;" u2="&#x441;" k="21" />
    <hkern u1="&#x41a;" u2="&#x43e;" k="10" />
    <hkern u1="&#x41a;" u2="&#x43a;" k="10" />
    <hkern u1="&#x41a;" u2="&#x435;" k="10" />
    <hkern u1="&#x41a;" u2="&#x433;" k="10" />
    <hkern u1="&#x41a;" u2="&#x42f;" k="21" />
    <hkern u1="&#x41a;" u2="&#x42d;" k="10" />
    <hkern u1="&#x41a;" u2="&#x427;" k="21" />
    <hkern u1="&#x41a;" u2="&#x425;" k="-21" />
    <hkern u1="&#x41a;" u2="&#x424;" k="12" />
    <hkern u1="&#x41a;" u2="&#x421;" k="10" />
    <hkern u1="&#x41a;" u2="&#x41e;" k="21" />
    <hkern u1="&#x41a;" u2="&#x417;" k="21" />
    <hkern u1="&#x41a;" u2="&#x414;" k="-20" />
    <hkern u1="&#x41a;" u2="&#x410;" k="-10" />
    <hkern u1="&#x41a;" u2="&#x40b;" k="10" />
    <hkern u1="&#x41a;" u2="&#x404;" k="21" />
    <hkern u1="&#x41a;" u2="&#x402;" k="10" />
    <hkern u1="&#x41b;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x41b;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x41b;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x41b;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x41b;" u2="&#x201d;" k="-62" />
    <hkern u1="&#x41b;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x41b;" u2="&#x2019;" k="-62" />
    <hkern u1="&#x41b;" u2="&#x2018;" k="-52" />
    <hkern u1="&#x41b;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x41b;" u2="&#xad;" k="-73" />
    <hkern u1="&#x41b;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x41c;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x41c;" u2="&#x201d;" k="-31" />
    <hkern u1="&#x41c;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x41c;" u2="&#x2019;" k="-31" />
    <hkern u1="&#x41c;" u2="&#x2018;" k="-21" />
    <hkern u1="&#x41d;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x41e;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x41e;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x41e;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x41e;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x41e;" u2="&#x425;" k="12" />
    <hkern u1="&#x41e;" u2="&#x416;" k="12" />
    <hkern u1="&#x41f;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x420;" g2="hyphenminus" k="-63" />
    <hkern u1="&#x420;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x420;" g2="afii10097.var.accented" k="21" />
    <hkern u1="&#x420;" g2="afii10095.var.accented" k="21" />
    <hkern u1="&#x420;" g2="afii10085.var.accented" k="-10" />
    <hkern u1="&#x420;" g2="afii10080.var.accented" k="21" />
    <hkern u1="&#x420;" g2="afii10070.var.accented" k="32" />
    <hkern u1="&#x420;" g2="afii10065.var.accented" k="32" />
    <hkern u1="&#x420;" g2="afii10047.var.accented" k="11" />
    <hkern u1="&#x420;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x420;" g2="afii10017.var.accented" k="42" />
    <hkern u1="&#x420;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x420;" u2="&#x2026;" k="83" />
    <hkern u1="&#x420;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x420;" u2="&#x4f3;" k="-10" />
    <hkern u1="&#x420;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x420;" u2="&#x4f1;" k="-10" />
    <hkern u1="&#x420;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x420;" u2="&#x4ef;" k="-10" />
    <hkern u1="&#x420;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x420;" u2="&#x4eb;" k="21" />
    <hkern u1="&#x420;" u2="&#x4e9;" k="21" />
    <hkern u1="&#x420;" u2="&#x4e7;" k="21" />
    <hkern u1="&#x420;" u2="&#x4df;" k="21" />
    <hkern u1="&#x420;" u2="&#x4de;" k="11" />
    <hkern u1="&#x420;" u2="&#x4d9;" k="32" />
    <hkern u1="&#x420;" u2="&#x4d7;" k="32" />
    <hkern u1="&#x420;" u2="&#x4d3;" k="32" />
    <hkern u1="&#x420;" u2="&#x4d2;" k="42" />
    <hkern u1="&#x420;" u2="&#x4d1;" k="32" />
    <hkern u1="&#x420;" u2="&#x4d0;" k="42" />
    <hkern u1="&#x420;" u2="&#x4bb;" k="-10" />
    <hkern u1="&#x420;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x420;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x420;" u2="&#x4b2;" k="21" />
    <hkern u1="&#x420;" u2="&#x4af;" k="-10" />
    <hkern u1="&#x420;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x420;" u2="&#x4ab;" k="32" />
    <hkern u1="&#x420;" u2="&#x4aa;" k="11" />
    <hkern u1="&#x420;" u2="&#x499;" k="21" />
    <hkern u1="&#x420;" u2="&#x498;" k="11" />
    <hkern u1="&#x420;" u2="&#x473;" k="21" />
    <hkern u1="&#x420;" u2="&#x45e;" k="-10" />
    <hkern u1="&#x420;" u2="&#x45b;" k="-10" />
    <hkern u1="&#x420;" u2="&#x459;" k="32" />
    <hkern u1="&#x420;" u2="&#x454;" k="32" />
    <hkern u1="&#x420;" u2="&#x452;" k="-10" />
    <hkern u1="&#x420;" u2="&#x451;" k="32" />
    <hkern u1="&#x420;" u2="&#x44f;" k="21" />
    <hkern u1="&#x420;" u2="&#x44d;" k="21" />
    <hkern u1="&#x420;" u2="&#x447;" k="10" />
    <hkern u1="&#x420;" u2="&#x444;" k="42" />
    <hkern u1="&#x420;" u2="&#x443;" k="-10" />
    <hkern u1="&#x420;" u2="&#x442;" k="-10" />
    <hkern u1="&#x420;" u2="&#x441;" k="32" />
    <hkern u1="&#x420;" u2="&#x43e;" k="21" />
    <hkern u1="&#x420;" u2="&#x43b;" k="42" />
    <hkern u1="&#x420;" u2="&#x437;" k="21" />
    <hkern u1="&#x420;" u2="&#x435;" k="32" />
    <hkern u1="&#x420;" u2="&#x434;" k="52" />
    <hkern u1="&#x420;" u2="&#x430;" k="32" />
    <hkern u1="&#x420;" u2="&#x42d;" k="11" />
    <hkern u1="&#x420;" u2="&#x425;" k="21" />
    <hkern u1="&#x420;" u2="&#x423;" k="21" />
    <hkern u1="&#x420;" u2="&#x422;" k="-31" />
    <hkern u1="&#x420;" u2="&#x421;" k="11" />
    <hkern u1="&#x420;" u2="&#x41b;" k="32" />
    <hkern u1="&#x420;" u2="&#x417;" k="11" />
    <hkern u1="&#x420;" u2="&#x414;" k="21" />
    <hkern u1="&#x420;" u2="&#x410;" k="42" />
    <hkern u1="&#x420;" u2="&#x40e;" k="21" />
    <hkern u1="&#x420;" u2="&#x40b;" k="-31" />
    <hkern u1="&#x420;" u2="&#x409;" k="32" />
    <hkern u1="&#x420;" u2="&#x402;" k="-31" />
    <hkern u1="&#x420;" u2="&#x2e;" k="73" />
    <hkern u1="&#x420;" u2="&#x2c;" k="104" />
    <hkern u1="&#x421;" g2="afii10097.var.accented" k="21" />
    <hkern u1="&#x421;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x421;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x421;" u2="&#x201c;" k="-41" />
    <hkern u1="&#x421;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x421;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x421;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x421;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x421;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x421;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x421;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x421;" u2="&#x4aa;" k="41" />
    <hkern u1="&#x421;" u2="&#x473;" k="21" />
    <hkern u1="&#x421;" u2="&#x472;" k="10" />
    <hkern u1="&#x421;" u2="&#x454;" k="10" />
    <hkern u1="&#x421;" u2="&#x44f;" k="21" />
    <hkern u1="&#x421;" u2="&#x44a;" k="31" />
    <hkern u1="&#x421;" u2="&#x447;" k="41" />
    <hkern u1="&#x421;" u2="&#x442;" k="31" />
    <hkern u1="&#x421;" u2="&#x431;" k="21" />
    <hkern u1="&#x421;" u2="&#x42f;" k="21" />
    <hkern u1="&#x421;" u2="&#x42a;" k="-21" />
    <hkern u1="&#x421;" u2="&#x427;" k="21" />
    <hkern u1="&#x421;" u2="&#x424;" k="21" />
    <hkern u1="&#x421;" u2="&#x421;" k="41" />
    <hkern u1="&#x421;" u2="&#x404;" k="31" />
    <hkern u1="&#x422;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x422;" u2="&#xf6bc;" k="-11" />
    <hkern u1="&#x422;" u2="&#xf6bb;" k="-11" />
    <hkern u1="&#x422;" u2="&#xf6ba;" k="-11" />
    <hkern u1="&#x422;" g2="afii10071.var.accented" k="1" />
    <hkern u1="&#x422;" g2="afii10097.var.accented" k="42" />
    <hkern u1="&#x422;" g2="afii10096.var.accented" k="30" />
    <hkern u1="&#x422;" g2="afii10095.var.accented" k="21" />
    <hkern u1="&#x422;" g2="afii10093.var.accented" k="30" />
    <hkern u1="&#x422;" g2="afii10085.var.accented" k="52" />
    <hkern u1="&#x422;" g2="afii10080.var.accented" k="41" />
    <hkern u1="&#x422;" g2="afii10074.var.accented" k="30" />
    <hkern u1="&#x422;" g2="afii10070.var.accented" k="41" />
    <hkern u1="&#x422;" g2="afii10065.var.accented" k="30" />
    <hkern u1="&#x422;" g2="afii10037.var.accented" k="-21" />
    <hkern u1="&#x422;" g2="afii10017.var.accented" k="14" />
    <hkern u1="&#x422;" u2="&#x2122;" k="-62" />
    <hkern u1="&#x422;" u2="&#x201d;" k="-53" />
    <hkern u1="&#x422;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x422;" u2="&#x2019;" k="-30" />
    <hkern u1="&#x422;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x422;" u2="&#x2014;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2013;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2012;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2011;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2010;" k="-11" />
    <hkern u1="&#x422;" u2="&#x4f9;" k="30" />
    <hkern u1="&#x422;" u2="&#x4f5;" k="42" />
    <hkern u1="&#x422;" u2="&#x4f3;" k="52" />
    <hkern u1="&#x422;" u2="&#x4f2;" k="-21" />
    <hkern u1="&#x422;" u2="&#x4f1;" k="52" />
    <hkern u1="&#x422;" u2="&#x4f0;" k="-21" />
    <hkern u1="&#x422;" u2="&#x4ef;" k="52" />
    <hkern u1="&#x422;" u2="&#x4ee;" k="-21" />
    <hkern u1="&#x422;" u2="&#x4eb;" k="21" />
    <hkern u1="&#x422;" u2="&#x4e9;" k="21" />
    <hkern u1="&#x422;" u2="&#x4e7;" k="21" />
    <hkern u1="&#x422;" u2="&#x4df;" k="72" />
    <hkern u1="&#x422;" u2="&#x4dd;" k="31" />
    <hkern u1="&#x422;" u2="&#x4d9;" k="41" />
    <hkern u1="&#x422;" u2="&#x4d7;" k="41" />
    <hkern u1="&#x422;" u2="&#x4d3;" k="30" />
    <hkern u1="&#x422;" u2="&#x4d2;" k="14" />
    <hkern u1="&#x422;" u2="&#x4d1;" k="30" />
    <hkern u1="&#x422;" u2="&#x4d0;" k="14" />
    <hkern u1="&#x422;" u2="&#x4c8;" k="30" />
    <hkern u1="&#x422;" u2="&#x4c2;" k="31" />
    <hkern u1="&#x422;" u2="&#x4bb;" k="-31" />
    <hkern u1="&#x422;" u2="&#x4b9;" k="42" />
    <hkern u1="&#x422;" u2="&#x4b7;" k="42" />
    <hkern u1="&#x422;" u2="&#x4b3;" k="42" />
    <hkern u1="&#x422;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x422;" u2="&#x4af;" k="52" />
    <hkern u1="&#x422;" u2="&#x4ae;" k="-21" />
    <hkern u1="&#x422;" u2="&#x4ab;" k="41" />
    <hkern u1="&#x422;" u2="&#x4a3;" k="30" />
    <hkern u1="&#x422;" u2="&#x499;" k="72" />
    <hkern u1="&#x422;" u2="&#x497;" k="31" />
    <hkern u1="&#x422;" u2="&#x493;" k="30" />
    <hkern u1="&#x422;" u2="&#x491;" k="30" />
    <hkern u1="&#x422;" u2="&#x473;" k="21" />
    <hkern u1="&#x422;" u2="&#x45f;" k="30" />
    <hkern u1="&#x422;" u2="&#x45e;" k="12" />
    <hkern u1="&#x422;" u2="&#x45c;" k="30" />
    <hkern u1="&#x422;" u2="&#x45b;" k="-21" />
    <hkern u1="&#x422;" u2="&#x45a;" k="30" />
    <hkern u1="&#x422;" u2="&#x459;" k="41" />
    <hkern u1="&#x422;" u2="&#x458;" k="-17" />
    <hkern u1="&#x422;" u2="&#x457;" k="-30" />
    <hkern u1="&#x422;" u2="&#x456;" k="-17" />
    <hkern u1="&#x422;" u2="&#x455;" k="40" />
    <hkern u1="&#x422;" u2="&#x454;" k="41" />
    <hkern u1="&#x422;" u2="&#x453;" k="30" />
    <hkern u1="&#x422;" u2="&#x452;" k="-31" />
    <hkern u1="&#x422;" u2="&#x451;" k="13" />
    <hkern u1="&#x422;" u2="&#x44f;" k="42" />
    <hkern u1="&#x422;" u2="&#x44e;" k="30" />
    <hkern u1="&#x422;" u2="&#x44d;" k="51" />
    <hkern u1="&#x422;" u2="&#x44c;" k="30" />
    <hkern u1="&#x422;" u2="&#x44b;" k="30" />
    <hkern u1="&#x422;" u2="&#x44a;" k="31" />
    <hkern u1="&#x422;" u2="&#x449;" k="30" />
    <hkern u1="&#x422;" u2="&#x448;" k="30" />
    <hkern u1="&#x422;" u2="&#x447;" k="42" />
    <hkern u1="&#x422;" u2="&#x446;" k="30" />
    <hkern u1="&#x422;" u2="&#x445;" k="42" />
    <hkern u1="&#x422;" u2="&#x444;" k="41" />
    <hkern u1="&#x422;" u2="&#x443;" k="52" />
    <hkern u1="&#x422;" u2="&#x442;" k="31" />
    <hkern u1="&#x422;" u2="&#x441;" k="41" />
    <hkern u1="&#x422;" u2="&#x440;" k="30" />
    <hkern u1="&#x422;" u2="&#x43f;" k="30" />
    <hkern u1="&#x422;" u2="&#x43e;" k="21" />
    <hkern u1="&#x422;" u2="&#x43d;" k="30" />
    <hkern u1="&#x422;" u2="&#x43c;" k="20" />
    <hkern u1="&#x422;" u2="&#x43b;" k="41" />
    <hkern u1="&#x422;" u2="&#x43a;" k="30" />
    <hkern u1="&#x422;" u2="&#x439;" k="30" />
    <hkern u1="&#x422;" u2="&#x438;" k="30" />
    <hkern u1="&#x422;" u2="&#x437;" k="72" />
    <hkern u1="&#x422;" u2="&#x436;" k="31" />
    <hkern u1="&#x422;" u2="&#x435;" k="41" />
    <hkern u1="&#x422;" u2="&#x434;" k="31" />
    <hkern u1="&#x422;" u2="&#x433;" k="30" />
    <hkern u1="&#x422;" u2="&#x432;" k="30" />
    <hkern u1="&#x422;" u2="&#x431;" k="30" />
    <hkern u1="&#x422;" u2="&#x430;" k="30" />
    <hkern u1="&#x422;" u2="&#x42a;" k="-31" />
    <hkern u1="&#x422;" u2="&#x425;" k="-31" />
    <hkern u1="&#x422;" u2="&#x423;" k="-21" />
    <hkern u1="&#x422;" u2="&#x422;" k="-52" />
    <hkern u1="&#x422;" u2="&#x410;" k="14" />
    <hkern u1="&#x422;" u2="&#x40e;" k="-21" />
    <hkern u1="&#x422;" u2="&#x40b;" k="-31" />
    <hkern u1="&#x422;" u2="&#x404;" k="21" />
    <hkern u1="&#x422;" u2="&#x402;" k="-31" />
    <hkern u1="&#x422;" u2="&#xad;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2e;" k="16" />
    <hkern u1="&#x422;" u2="&#x2d;" k="-11" />
    <hkern u1="&#x422;" u2="&#x2c;" k="16" />
    <hkern u1="&#x423;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x423;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x423;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x423;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x423;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x423;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x423;" u2="&#x2026;" k="52" />
    <hkern u1="&#x423;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x423;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x423;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x423;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x423;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x423;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x423;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x423;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x423;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x423;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x423;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x423;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x423;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x423;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x423;" u2="&#x49d;" k="31" />
    <hkern u1="&#x423;" u2="&#x49b;" k="31" />
    <hkern u1="&#x423;" u2="&#x497;" k="41" />
    <hkern u1="&#x423;" u2="&#x473;" k="62" />
    <hkern u1="&#x423;" u2="&#x472;" k="31" />
    <hkern u1="&#x423;" u2="&#x458;" k="1" />
    <hkern u1="&#x423;" u2="&#x457;" k="1" />
    <hkern u1="&#x423;" u2="&#x456;" k="11" />
    <hkern u1="&#x423;" u2="&#x455;" k="70" />
    <hkern u1="&#x423;" u2="&#x451;" k="42" />
    <hkern u1="&#x423;" u2="&#x44f;" k="62" />
    <hkern u1="&#x423;" u2="&#x44d;" k="62" />
    <hkern u1="&#x423;" u2="&#x44a;" k="31" />
    <hkern u1="&#x423;" u2="&#x447;" k="41" />
    <hkern u1="&#x423;" u2="&#x445;" k="52" />
    <hkern u1="&#x423;" u2="&#x444;" k="52" />
    <hkern u1="&#x423;" u2="&#x442;" k="41" />
    <hkern u1="&#x423;" u2="&#x43c;" k="52" />
    <hkern u1="&#x423;" u2="&#x436;" k="50" />
    <hkern u1="&#x423;" u2="&#x434;" k="31" />
    <hkern u1="&#x423;" u2="&#x431;" k="51" />
    <hkern u1="&#x423;" u2="&#x42f;" k="31" />
    <hkern u1="&#x423;" u2="&#x42d;" k="20" />
    <hkern u1="&#x423;" u2="&#x424;" k="31" />
    <hkern u1="&#x423;" u2="&#x421;" k="52" />
    <hkern u1="&#x423;" u2="&#x41c;" k="31" />
    <hkern u1="&#x423;" u2="&#x414;" k="28" />
    <hkern u1="&#x423;" u2="&#x409;" k="41" />
    <hkern u1="&#x423;" u2="&#x404;" k="41" />
    <hkern u1="&#x423;" u2="&#x3a;" k="31" />
    <hkern u1="&#x423;" u2="&#x2e;" k="83" />
    <hkern u1="&#x424;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x424;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x424;" g2="afii10017.var.accented" k="20" />
    <hkern u1="&#x424;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x424;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x424;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x424;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x424;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x424;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x424;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x424;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x424;" u2="&#x4d2;" k="20" />
    <hkern u1="&#x424;" u2="&#x4d0;" k="20" />
    <hkern u1="&#x424;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x424;" u2="&#x42d;" k="21" />
    <hkern u1="&#x424;" u2="&#x425;" k="21" />
    <hkern u1="&#x424;" u2="&#x423;" k="21" />
    <hkern u1="&#x424;" u2="&#x416;" k="12" />
    <hkern u1="&#x424;" u2="&#x410;" k="20" />
    <hkern u1="&#x424;" u2="&#x40e;" k="21" />
    <hkern u1="&#x425;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x425;" u2="&#xf6bc;" k="-52" />
    <hkern u1="&#x425;" u2="&#xf6bb;" k="-52" />
    <hkern u1="&#x425;" u2="&#xf6ba;" k="-52" />
    <hkern u1="&#x425;" g2="afii10095.var.accented" k="21" />
    <hkern u1="&#x425;" g2="afii10049.var.accented" k="10" />
    <hkern u1="&#x425;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x425;" g2="afii10032.var.accented" k="12" />
    <hkern u1="&#x425;" g2="afii10017.var.accented" k="-21" />
    <hkern u1="&#x425;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x425;" u2="&#x2014;" k="-52" />
    <hkern u1="&#x425;" u2="&#x2013;" k="-52" />
    <hkern u1="&#x425;" u2="&#x2012;" k="-52" />
    <hkern u1="&#x425;" u2="&#x2011;" k="-52" />
    <hkern u1="&#x425;" u2="&#x2010;" k="-52" />
    <hkern u1="&#x425;" u2="&#x4f5;" k="42" />
    <hkern u1="&#x425;" u2="&#x4f4;" k="31" />
    <hkern u1="&#x425;" u2="&#x4ea;" k="12" />
    <hkern u1="&#x425;" u2="&#x4e8;" k="12" />
    <hkern u1="&#x425;" u2="&#x4e6;" k="12" />
    <hkern u1="&#x425;" u2="&#x4df;" k="21" />
    <hkern u1="&#x425;" u2="&#x4de;" k="10" />
    <hkern u1="&#x425;" u2="&#x4dd;" k="-21" />
    <hkern u1="&#x425;" u2="&#x4d8;" k="12" />
    <hkern u1="&#x425;" u2="&#x4d2;" k="-21" />
    <hkern u1="&#x425;" u2="&#x4d0;" k="-21" />
    <hkern u1="&#x425;" u2="&#x4c2;" k="-21" />
    <hkern u1="&#x425;" u2="&#x4b9;" k="42" />
    <hkern u1="&#x425;" u2="&#x4b8;" k="31" />
    <hkern u1="&#x425;" u2="&#x4b7;" k="42" />
    <hkern u1="&#x425;" u2="&#x4b6;" k="31" />
    <hkern u1="&#x425;" u2="&#x4b2;" k="-10" />
    <hkern u1="&#x425;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x425;" u2="&#x499;" k="21" />
    <hkern u1="&#x425;" u2="&#x498;" k="10" />
    <hkern u1="&#x425;" u2="&#x497;" k="-21" />
    <hkern u1="&#x425;" u2="&#x459;" k="-21" />
    <hkern u1="&#x425;" u2="&#x44d;" k="21" />
    <hkern u1="&#x425;" u2="&#x44a;" k="21" />
    <hkern u1="&#x425;" u2="&#x447;" k="42" />
    <hkern u1="&#x425;" u2="&#x442;" k="21" />
    <hkern u1="&#x425;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x425;" u2="&#x437;" k="21" />
    <hkern u1="&#x425;" u2="&#x436;" k="-21" />
    <hkern u1="&#x425;" u2="&#x434;" k="-10" />
    <hkern u1="&#x425;" u2="&#x42f;" k="10" />
    <hkern u1="&#x425;" u2="&#x42d;" k="10" />
    <hkern u1="&#x425;" u2="&#x427;" k="31" />
    <hkern u1="&#x425;" u2="&#x425;" k="-10" />
    <hkern u1="&#x425;" u2="&#x424;" k="21" />
    <hkern u1="&#x425;" u2="&#x422;" k="-21" />
    <hkern u1="&#x425;" u2="&#x421;" k="21" />
    <hkern u1="&#x425;" u2="&#x41e;" k="12" />
    <hkern u1="&#x425;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x425;" u2="&#x417;" k="10" />
    <hkern u1="&#x425;" u2="&#x414;" k="-21" />
    <hkern u1="&#x425;" u2="&#x410;" k="-21" />
    <hkern u1="&#x425;" u2="&#x40b;" k="-21" />
    <hkern u1="&#x425;" u2="&#x409;" k="-21" />
    <hkern u1="&#x425;" u2="&#x404;" k="20" />
    <hkern u1="&#x425;" u2="&#x402;" k="-21" />
    <hkern u1="&#x425;" u2="&#xad;" k="-52" />
    <hkern u1="&#x425;" u2="&#x2d;" k="-52" />
    <hkern u1="&#x426;" g2="hyphenminus" k="-63" />
    <hkern u1="&#x426;" u2="&#xf6bc;" k="-63" />
    <hkern u1="&#x426;" u2="&#xf6bb;" k="-63" />
    <hkern u1="&#x426;" u2="&#xf6ba;" k="-63" />
    <hkern u1="&#x426;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x426;" u2="&#x2122;" k="-21" />
    <hkern u1="&#x426;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x426;" u2="&#x201c;" k="-21" />
    <hkern u1="&#x426;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x426;" u2="&#x2018;" k="-21" />
    <hkern u1="&#x426;" u2="&#x2014;" k="-63" />
    <hkern u1="&#x426;" u2="&#x2013;" k="-63" />
    <hkern u1="&#x426;" u2="&#x2012;" k="-63" />
    <hkern u1="&#x426;" u2="&#x2011;" k="-63" />
    <hkern u1="&#x426;" u2="&#x2010;" k="-63" />
    <hkern u1="&#x426;" u2="&#x4f4;" k="31" />
    <hkern u1="&#x426;" u2="&#x4de;" k="10" />
    <hkern u1="&#x426;" u2="&#x4b8;" k="31" />
    <hkern u1="&#x426;" u2="&#x4b6;" k="31" />
    <hkern u1="&#x426;" u2="&#x498;" k="10" />
    <hkern u1="&#x426;" u2="&#x434;" k="-21" />
    <hkern u1="&#x426;" u2="&#x42d;" k="10" />
    <hkern u1="&#x426;" u2="&#x427;" k="31" />
    <hkern u1="&#x426;" u2="&#x417;" k="10" />
    <hkern u1="&#x426;" u2="&#x414;" k="-21" />
    <hkern u1="&#x426;" u2="&#x40b;" k="21" />
    <hkern u1="&#x426;" u2="&#x402;" k="31" />
    <hkern u1="&#x426;" u2="&#xad;" k="-63" />
    <hkern u1="&#x426;" u2="&#x2d;" k="-63" />
    <hkern u1="&#x427;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x428;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x429;" g2="hyphenminus" k="-62" />
    <hkern u1="&#x429;" u2="&#xf6bc;" k="-62" />
    <hkern u1="&#x429;" u2="&#xf6bb;" k="-62" />
    <hkern u1="&#x429;" u2="&#xf6ba;" k="-62" />
    <hkern u1="&#x429;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x429;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x429;" u2="&#x201d;" k="-31" />
    <hkern u1="&#x429;" u2="&#x2019;" k="-31" />
    <hkern u1="&#x429;" u2="&#x2018;" k="-32" />
    <hkern u1="&#x429;" u2="&#x2014;" k="-62" />
    <hkern u1="&#x429;" u2="&#x2013;" k="-62" />
    <hkern u1="&#x429;" u2="&#x2012;" k="-62" />
    <hkern u1="&#x429;" u2="&#x2011;" k="-62" />
    <hkern u1="&#x429;" u2="&#x2010;" k="-62" />
    <hkern u1="&#x429;" u2="&#x4f4;" k="32" />
    <hkern u1="&#x429;" u2="&#x4de;" k="21" />
    <hkern u1="&#x429;" u2="&#x4b8;" k="32" />
    <hkern u1="&#x429;" u2="&#x4b6;" k="32" />
    <hkern u1="&#x429;" u2="&#x498;" k="21" />
    <hkern u1="&#x429;" u2="&#x434;" k="-10" />
    <hkern u1="&#x429;" u2="&#x42d;" k="21" />
    <hkern u1="&#x429;" u2="&#x427;" k="32" />
    <hkern u1="&#x429;" u2="&#x417;" k="21" />
    <hkern u1="&#x429;" u2="&#x414;" k="-31" />
    <hkern u1="&#x429;" u2="&#xad;" k="-62" />
    <hkern u1="&#x429;" u2="&#x2d;" k="-62" />
    <hkern u1="&#x42a;" g2="afii10022.var.accented" k="21" />
    <hkern u1="&#x42a;" u2="&#x2122;" k="20" />
    <hkern u1="&#x42a;" u2="&#x201c;" k="31" />
    <hkern u1="&#x42a;" u2="&#x2018;" k="42" />
    <hkern u1="&#x42a;" u2="&#x4f4;" k="62" />
    <hkern u1="&#x42a;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x42a;" u2="&#x4d6;" k="21" />
    <hkern u1="&#x42a;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x42a;" u2="&#x4b8;" k="62" />
    <hkern u1="&#x42a;" u2="&#x4b6;" k="62" />
    <hkern u1="&#x42a;" u2="&#x4b2;" k="31" />
    <hkern u1="&#x42a;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x42a;" u2="&#x496;" k="21" />
    <hkern u1="&#x42a;" u2="&#x472;" k="10" />
    <hkern u1="&#x42a;" u2="&#x42a;" k="52" />
    <hkern u1="&#x42a;" u2="&#x427;" k="62" />
    <hkern u1="&#x42a;" u2="&#x425;" k="31" />
    <hkern u1="&#x42a;" u2="&#x424;" k="21" />
    <hkern u1="&#x42a;" u2="&#x421;" k="21" />
    <hkern u1="&#x42a;" u2="&#x41c;" k="10" />
    <hkern u1="&#x42a;" u2="&#x416;" k="21" />
    <hkern u1="&#x42a;" u2="&#x415;" k="21" />
    <hkern u1="&#x42a;" u2="&#x40b;" k="62" />
    <hkern u1="&#x42a;" u2="&#x404;" k="21" />
    <hkern u1="&#x42a;" u2="&#x402;" k="73" />
    <hkern u1="&#x42a;" u2="&#x401;" k="21" />
    <hkern u1="&#x42b;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x42c;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x42c;" g2="afii10037.var.accented" k="84" />
    <hkern u1="&#x42c;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x42c;" g2="afii10022.var.accented" k="21" />
    <hkern u1="&#x42c;" g2="afii10017.var.accented" k="21" />
    <hkern u1="&#x42c;" u2="&#x2122;" k="31" />
    <hkern u1="&#x42c;" u2="&#x201d;" k="52" />
    <hkern u1="&#x42c;" u2="&#x201c;" k="73" />
    <hkern u1="&#x42c;" u2="&#x2019;" k="52" />
    <hkern u1="&#x42c;" u2="&#x2018;" k="42" />
    <hkern u1="&#x42c;" u2="&#x4f4;" k="63" />
    <hkern u1="&#x42c;" u2="&#x4f2;" k="84" />
    <hkern u1="&#x42c;" u2="&#x4f0;" k="84" />
    <hkern u1="&#x42c;" u2="&#x4ee;" k="84" />
    <hkern u1="&#x42c;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4de;" k="32" />
    <hkern u1="&#x42c;" u2="&#x4dc;" k="32" />
    <hkern u1="&#x42c;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4d6;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4d2;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4d0;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4c1;" k="32" />
    <hkern u1="&#x42c;" u2="&#x4b8;" k="63" />
    <hkern u1="&#x42c;" u2="&#x4b6;" k="63" />
    <hkern u1="&#x42c;" u2="&#x4b2;" k="21" />
    <hkern u1="&#x42c;" u2="&#x4ae;" k="84" />
    <hkern u1="&#x42c;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x42c;" u2="&#x498;" k="32" />
    <hkern u1="&#x42c;" u2="&#x496;" k="32" />
    <hkern u1="&#x42c;" u2="&#x472;" k="21" />
    <hkern u1="&#x42c;" u2="&#x42d;" k="21" />
    <hkern u1="&#x42c;" u2="&#x42a;" k="63" />
    <hkern u1="&#x42c;" u2="&#x427;" k="63" />
    <hkern u1="&#x42c;" u2="&#x425;" k="21" />
    <hkern u1="&#x42c;" u2="&#x424;" k="11" />
    <hkern u1="&#x42c;" u2="&#x423;" k="84" />
    <hkern u1="&#x42c;" u2="&#x421;" k="21" />
    <hkern u1="&#x42c;" u2="&#x41e;" k="21" />
    <hkern u1="&#x42c;" u2="&#x41c;" k="10" />
    <hkern u1="&#x42c;" u2="&#x417;" k="32" />
    <hkern u1="&#x42c;" u2="&#x416;" k="32" />
    <hkern u1="&#x42c;" u2="&#x415;" k="21" />
    <hkern u1="&#x42c;" u2="&#x410;" k="21" />
    <hkern u1="&#x42c;" u2="&#x40e;" k="84" />
    <hkern u1="&#x42c;" u2="&#x40b;" k="63" />
    <hkern u1="&#x42c;" u2="&#x404;" k="21" />
    <hkern u1="&#x42c;" u2="&#x402;" k="73" />
    <hkern u1="&#x42c;" u2="&#x401;" k="21" />
    <hkern u1="&#x42d;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x42d;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x42d;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x42d;" u2="&#x2019;" k="-52" />
    <hkern u1="&#x42d;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x42d;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x42d;" u2="&#x425;" k="12" />
    <hkern u1="&#x42d;" u2="&#x416;" k="12" />
    <hkern u1="&#x42e;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x42e;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x42e;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x42e;" u2="&#x2019;" k="-52" />
    <hkern u1="&#x42e;" u2="&#x2018;" k="-52" />
    <hkern u1="&#x42e;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x42e;" u2="&#x425;" k="12" />
    <hkern u1="&#x42e;" u2="&#x416;" k="12" />
    <hkern u1="&#x42f;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x430;" u2="&#x201c;" k="-32" />
    <hkern u1="&#x430;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x431;" u2="&#x4af;" k="10" />
    <hkern u1="&#x432;" u2="&#x4f5;" k="32" />
    <hkern u1="&#x432;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x432;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x432;" u2="&#x4b9;" k="32" />
    <hkern u1="&#x432;" u2="&#x4b7;" k="32" />
    <hkern u1="&#x432;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x432;" u2="&#x496;" k="21" />
    <hkern u1="&#x432;" u2="&#x447;" k="32" />
    <hkern u1="&#x432;" u2="&#x445;" k="21" />
    <hkern u1="&#x432;" u2="&#x416;" k="21" />
    <hkern u1="&#x433;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x433;" g2="afii10080.var.accented" k="11" />
    <hkern u1="&#x433;" u2="&#x2026;" k="52" />
    <hkern u1="&#x433;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x433;" u2="&#x4eb;" k="11" />
    <hkern u1="&#x433;" u2="&#x4e9;" k="11" />
    <hkern u1="&#x433;" u2="&#x4e7;" k="11" />
    <hkern u1="&#x433;" u2="&#x4dc;" k="20" />
    <hkern u1="&#x433;" u2="&#x4c1;" k="20" />
    <hkern u1="&#x433;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x433;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x433;" u2="&#x4b3;" k="10" />
    <hkern u1="&#x433;" u2="&#x4b2;" k="20" />
    <hkern u1="&#x433;" u2="&#x496;" k="20" />
    <hkern u1="&#x433;" u2="&#x493;" k="10" />
    <hkern u1="&#x433;" u2="&#x491;" k="10" />
    <hkern u1="&#x433;" u2="&#x473;" k="11" />
    <hkern u1="&#x433;" u2="&#x454;" k="10" />
    <hkern u1="&#x433;" u2="&#x453;" k="10" />
    <hkern u1="&#x433;" u2="&#x44f;" k="10" />
    <hkern u1="&#x433;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x433;" u2="&#x447;" k="10" />
    <hkern u1="&#x433;" u2="&#x445;" k="10" />
    <hkern u1="&#x433;" u2="&#x442;" k="-21" />
    <hkern u1="&#x433;" u2="&#x43e;" k="11" />
    <hkern u1="&#x433;" u2="&#x43c;" k="20" />
    <hkern u1="&#x433;" u2="&#x434;" k="31" />
    <hkern u1="&#x433;" u2="&#x433;" k="10" />
    <hkern u1="&#x433;" u2="&#x425;" k="20" />
    <hkern u1="&#x433;" u2="&#x41c;" k="20" />
    <hkern u1="&#x433;" u2="&#x416;" k="20" />
    <hkern u1="&#x433;" u2="&#x414;" k="20" />
    <hkern u1="&#x433;" u2="&#x2e;" k="42" />
    <hkern u1="&#x434;" g2="hyphenminus" k="-62" />
    <hkern u1="&#x434;" u2="&#xf6bc;" k="-62" />
    <hkern u1="&#x434;" u2="&#xf6bb;" k="-62" />
    <hkern u1="&#x434;" u2="&#xf6ba;" k="-62" />
    <hkern u1="&#x434;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x434;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x434;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x434;" u2="&#x2014;" k="-62" />
    <hkern u1="&#x434;" u2="&#x2013;" k="-62" />
    <hkern u1="&#x434;" u2="&#x2012;" k="-62" />
    <hkern u1="&#x434;" u2="&#x2011;" k="-62" />
    <hkern u1="&#x434;" u2="&#x2010;" k="-62" />
    <hkern u1="&#x434;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x434;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x434;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x434;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x434;" u2="&#x459;" k="-10" />
    <hkern u1="&#x434;" u2="&#x43b;" k="-21" />
    <hkern u1="&#x434;" u2="&#x434;" k="-20" />
    <hkern u1="&#x434;" u2="&#x423;" k="21" />
    <hkern u1="&#x434;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x434;" u2="&#x414;" k="-21" />
    <hkern u1="&#x434;" u2="&#x40e;" k="21" />
    <hkern u1="&#x434;" u2="&#x409;" k="-21" />
    <hkern u1="&#x434;" u2="&#xad;" k="-62" />
    <hkern u1="&#x434;" u2="&#x2d;" k="-62" />
    <hkern u1="&#x436;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x436;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x436;" u2="&#x497;" k="-20" />
    <hkern u1="&#x436;" u2="&#x459;" k="-20" />
    <hkern u1="&#x436;" u2="&#x442;" k="-10" />
    <hkern u1="&#x436;" u2="&#x436;" k="-20" />
    <hkern u1="&#x436;" u2="&#x434;" k="-10" />
    <hkern u1="&#x436;" u2="&#x414;" k="-20" />
    <hkern u1="&#x437;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x437;" u2="&#x4f5;" k="32" />
    <hkern u1="&#x437;" u2="&#x4df;" k="10" />
    <hkern u1="&#x437;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x437;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x437;" u2="&#x4b9;" k="32" />
    <hkern u1="&#x437;" u2="&#x4b7;" k="32" />
    <hkern u1="&#x437;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x437;" u2="&#x499;" k="10" />
    <hkern u1="&#x437;" u2="&#x496;" k="21" />
    <hkern u1="&#x437;" u2="&#x44d;" k="10" />
    <hkern u1="&#x437;" u2="&#x447;" k="32" />
    <hkern u1="&#x437;" u2="&#x445;" k="21" />
    <hkern u1="&#x437;" u2="&#x437;" k="10" />
    <hkern u1="&#x437;" u2="&#x416;" k="21" />
    <hkern u1="&#x43a;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x43a;" u2="&#xf6bc;" k="-42" />
    <hkern u1="&#x43a;" u2="&#xf6bb;" k="-42" />
    <hkern u1="&#x43a;" u2="&#xf6ba;" k="-42" />
    <hkern u1="&#x43a;" g2="afii10085.var.accented" k="-11" />
    <hkern u1="&#x43a;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x43a;" u2="&#x2014;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x2013;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x2012;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x2011;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x2010;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x4f3;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x43a;" u2="&#x4f1;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x43a;" u2="&#x4ef;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x43a;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x43a;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x43a;" u2="&#x4af;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x43a;" u2="&#x497;" k="-20" />
    <hkern u1="&#x43a;" u2="&#x45e;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x459;" k="-10" />
    <hkern u1="&#x43a;" u2="&#x443;" k="-11" />
    <hkern u1="&#x43a;" u2="&#x442;" k="-10" />
    <hkern u1="&#x43a;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x43a;" u2="&#x436;" k="-20" />
    <hkern u1="&#x43a;" u2="&#x434;" k="-10" />
    <hkern u1="&#x43a;" u2="&#x423;" k="31" />
    <hkern u1="&#x43a;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x43a;" u2="&#x414;" k="-21" />
    <hkern u1="&#x43a;" u2="&#x40e;" k="31" />
    <hkern u1="&#x43a;" u2="&#x409;" k="-21" />
    <hkern u1="&#x43a;" u2="&#xad;" k="-42" />
    <hkern u1="&#x43a;" u2="&#x2d;" k="-42" />
    <hkern u1="&#x43b;" g2="hyphenminus" k="-63" />
    <hkern u1="&#x43b;" u2="&#xf6bc;" k="-63" />
    <hkern u1="&#x43b;" u2="&#xf6bb;" k="-63" />
    <hkern u1="&#x43b;" u2="&#xf6ba;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x43b;" u2="&#x2019;" k="-52" />
    <hkern u1="&#x43b;" u2="&#x2018;" k="-32" />
    <hkern u1="&#x43b;" u2="&#x2014;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x2013;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x2012;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x2011;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x2010;" k="-63" />
    <hkern u1="&#x43b;" u2="&#xad;" k="-63" />
    <hkern u1="&#x43b;" u2="&#x2d;" k="-63" />
    <hkern u1="&#x43c;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x43c;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x43c;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x43c;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x43c;" u2="&#x2018;" k="-42" />
    <hkern u1="&#x43c;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x43c;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x43c;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x43c;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x43c;" u2="&#x459;" k="-20" />
    <hkern u1="&#x43c;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x43c;" u2="&#x423;" k="21" />
    <hkern u1="&#x43c;" u2="&#x40e;" k="21" />
    <hkern u1="&#x43d;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x43d;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x43d;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x43d;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x43d;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x43d;" u2="&#x423;" k="31" />
    <hkern u1="&#x43d;" u2="&#x40e;" k="31" />
    <hkern u1="&#x43e;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x43e;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x43e;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x43e;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x43e;" u2="&#x4af;" k="10" />
    <hkern u1="&#x43e;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x43e;" u2="&#x445;" k="10" />
    <hkern u1="&#x43e;" u2="&#x423;" k="52" />
    <hkern u1="&#x43e;" u2="&#x40e;" k="52" />
    <hkern u1="&#x43f;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x43f;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x43f;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x43f;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x43f;" u2="&#xad;" k="-73" />
    <hkern u1="&#x43f;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x440;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x440;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x440;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x440;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x440;" u2="&#x4af;" k="10" />
    <hkern u1="&#x440;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x440;" u2="&#x423;" k="31" />
    <hkern u1="&#x440;" u2="&#x40e;" k="31" />
    <hkern u1="&#x441;" u2="&#x459;" k="-10" />
    <hkern u1="&#x441;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x441;" u2="&#x442;" k="-11" />
    <hkern u1="&#x441;" u2="&#x434;" k="-10" />
    <hkern u1="&#x441;" u2="&#x414;" k="-11" />
    <hkern u1="&#x442;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x442;" u2="&#xf6bc;" k="-52" />
    <hkern u1="&#x442;" u2="&#xf6bb;" k="-52" />
    <hkern u1="&#x442;" u2="&#xf6ba;" k="-52" />
    <hkern u1="&#x442;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x442;" g2="afii10085.var.accented" k="-21" />
    <hkern u1="&#x442;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x442;" u2="&#x2014;" k="-52" />
    <hkern u1="&#x442;" u2="&#x2013;" k="-52" />
    <hkern u1="&#x442;" u2="&#x2012;" k="-52" />
    <hkern u1="&#x442;" u2="&#x2011;" k="-52" />
    <hkern u1="&#x442;" u2="&#x2010;" k="-52" />
    <hkern u1="&#x442;" u2="&#x4f3;" k="-21" />
    <hkern u1="&#x442;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x442;" u2="&#x4f1;" k="-21" />
    <hkern u1="&#x442;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x442;" u2="&#x4ef;" k="-21" />
    <hkern u1="&#x442;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x442;" u2="&#x4df;" k="10" />
    <hkern u1="&#x442;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x442;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x442;" u2="&#x4af;" k="-21" />
    <hkern u1="&#x442;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x442;" u2="&#x499;" k="10" />
    <hkern u1="&#x442;" u2="&#x496;" k="21" />
    <hkern u1="&#x442;" u2="&#x45e;" k="-21" />
    <hkern u1="&#x442;" u2="&#x459;" k="10" />
    <hkern u1="&#x442;" u2="&#x44d;" k="10" />
    <hkern u1="&#x442;" u2="&#x44a;" k="-21" />
    <hkern u1="&#x442;" u2="&#x443;" k="-21" />
    <hkern u1="&#x442;" u2="&#x442;" k="-20" />
    <hkern u1="&#x442;" u2="&#x43b;" k="10" />
    <hkern u1="&#x442;" u2="&#x437;" k="10" />
    <hkern u1="&#x442;" u2="&#x423;" k="31" />
    <hkern u1="&#x442;" u2="&#x41b;" k="21" />
    <hkern u1="&#x442;" u2="&#x416;" k="21" />
    <hkern u1="&#x442;" u2="&#x40e;" k="31" />
    <hkern u1="&#x442;" u2="&#x409;" k="21" />
    <hkern u1="&#x442;" u2="&#xad;" k="-52" />
    <hkern u1="&#x442;" u2="&#x3b;" k="-11" />
    <hkern u1="&#x442;" u2="&#x3a;" k="-11" />
    <hkern u1="&#x442;" u2="&#x2e;" k="20" />
    <hkern u1="&#x442;" u2="&#x2d;" k="-52" />
    <hkern u1="&#x442;" u2="&#x2c;" k="20" />
    <hkern u1="&#x443;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x443;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x443;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x443;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x443;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x443;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x443;" u2="&#x497;" k="-20" />
    <hkern u1="&#x443;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x443;" u2="&#x445;" k="-10" />
    <hkern u1="&#x443;" u2="&#x442;" k="-21" />
    <hkern u1="&#x443;" u2="&#x436;" k="-20" />
    <hkern u1="&#x444;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x444;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x444;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x444;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x444;" u2="&#x4af;" k="10" />
    <hkern u1="&#x444;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x444;" u2="&#x445;" k="10" />
    <hkern u1="&#x444;" u2="&#x423;" k="31" />
    <hkern u1="&#x444;" u2="&#x40e;" k="31" />
    <hkern u1="&#x445;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x445;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x445;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x445;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x445;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x445;" g2="afii10017.var.accented" k="-20" />
    <hkern u1="&#x445;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x445;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x445;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x445;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x445;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x445;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x445;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x445;" u2="&#x4df;" k="10" />
    <hkern u1="&#x445;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x445;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x445;" u2="&#x4d2;" k="-20" />
    <hkern u1="&#x445;" u2="&#x4d0;" k="-20" />
    <hkern u1="&#x445;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x445;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x445;" u2="&#x4b3;" k="-32" />
    <hkern u1="&#x445;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x445;" u2="&#x4ab;" k="10" />
    <hkern u1="&#x445;" u2="&#x499;" k="10" />
    <hkern u1="&#x445;" u2="&#x459;" k="-10" />
    <hkern u1="&#x445;" u2="&#x454;" k="10" />
    <hkern u1="&#x445;" u2="&#x451;" k="10" />
    <hkern u1="&#x445;" u2="&#x44d;" k="10" />
    <hkern u1="&#x445;" u2="&#x447;" k="21" />
    <hkern u1="&#x445;" u2="&#x445;" k="-32" />
    <hkern u1="&#x445;" u2="&#x444;" k="10" />
    <hkern u1="&#x445;" u2="&#x441;" k="10" />
    <hkern u1="&#x445;" u2="&#x43e;" k="10" />
    <hkern u1="&#x445;" u2="&#x43b;" k="-21" />
    <hkern u1="&#x445;" u2="&#x437;" k="10" />
    <hkern u1="&#x445;" u2="&#x435;" k="10" />
    <hkern u1="&#x445;" u2="&#x434;" k="-20" />
    <hkern u1="&#x445;" u2="&#x423;" k="32" />
    <hkern u1="&#x445;" u2="&#x422;" k="32" />
    <hkern u1="&#x445;" u2="&#x41b;" k="-20" />
    <hkern u1="&#x445;" u2="&#x414;" k="-20" />
    <hkern u1="&#x445;" u2="&#x410;" k="-20" />
    <hkern u1="&#x445;" u2="&#x40e;" k="32" />
    <hkern u1="&#x445;" u2="&#x40b;" k="32" />
    <hkern u1="&#x445;" u2="&#x409;" k="-20" />
    <hkern u1="&#x445;" u2="&#x402;" k="32" />
    <hkern u1="&#x446;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x446;" u2="&#xf6bc;" k="-73" />
    <hkern u1="&#x446;" u2="&#xf6bb;" k="-73" />
    <hkern u1="&#x446;" u2="&#xf6ba;" k="-73" />
    <hkern u1="&#x446;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x446;" u2="&#x201d;" k="-42" />
    <hkern u1="&#x446;" u2="&#x201c;" k="-42" />
    <hkern u1="&#x446;" u2="&#x2019;" k="-42" />
    <hkern u1="&#x446;" u2="&#x2018;" k="-32" />
    <hkern u1="&#x446;" u2="&#x2014;" k="-73" />
    <hkern u1="&#x446;" u2="&#x2013;" k="-73" />
    <hkern u1="&#x446;" u2="&#x2012;" k="-73" />
    <hkern u1="&#x446;" u2="&#x2011;" k="-73" />
    <hkern u1="&#x446;" u2="&#x2010;" k="-73" />
    <hkern u1="&#x446;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x446;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x446;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x446;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x446;" u2="&#x459;" k="-21" />
    <hkern u1="&#x446;" u2="&#x43b;" k="-21" />
    <hkern u1="&#x446;" u2="&#x434;" k="-21" />
    <hkern u1="&#x446;" u2="&#x423;" k="21" />
    <hkern u1="&#x446;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x446;" u2="&#x414;" k="-21" />
    <hkern u1="&#x446;" u2="&#x40e;" k="21" />
    <hkern u1="&#x446;" u2="&#x409;" k="-21" />
    <hkern u1="&#x446;" u2="&#xad;" k="-73" />
    <hkern u1="&#x446;" u2="&#x2d;" k="-73" />
    <hkern u1="&#x447;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x447;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x447;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x447;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x447;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x447;" u2="&#x423;" k="32" />
    <hkern u1="&#x447;" u2="&#x40e;" k="32" />
    <hkern u1="&#x449;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x449;" u2="&#xf6bc;" k="-52" />
    <hkern u1="&#x449;" u2="&#xf6bb;" k="-52" />
    <hkern u1="&#x449;" u2="&#xf6ba;" k="-52" />
    <hkern u1="&#x449;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x449;" u2="&#x2122;" k="-32" />
    <hkern u1="&#x449;" u2="&#x201d;" k="-52" />
    <hkern u1="&#x449;" u2="&#x201c;" k="-41" />
    <hkern u1="&#x449;" u2="&#x2019;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x449;" u2="&#x2014;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2013;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2012;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2011;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2010;" k="-52" />
    <hkern u1="&#x449;" u2="&#x4dd;" k="-10" />
    <hkern u1="&#x449;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x449;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x449;" u2="&#x4c2;" k="-10" />
    <hkern u1="&#x449;" u2="&#x497;" k="-10" />
    <hkern u1="&#x449;" u2="&#x459;" k="-20" />
    <hkern u1="&#x449;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x449;" u2="&#x436;" k="-10" />
    <hkern u1="&#x449;" u2="&#x434;" k="-31" />
    <hkern u1="&#x449;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x449;" u2="&#x414;" k="-21" />
    <hkern u1="&#x449;" u2="&#x410;" k="-10" />
    <hkern u1="&#x449;" u2="&#x409;" k="-21" />
    <hkern u1="&#x449;" u2="&#xad;" k="-52" />
    <hkern u1="&#x449;" u2="&#x2d;" k="-52" />
    <hkern u1="&#x44a;" g2="afii10097.var.accented" k="32" />
    <hkern u1="&#x44a;" u2="&#x2122;" k="52" />
    <hkern u1="&#x44a;" u2="&#x201c;" k="73" />
    <hkern u1="&#x44a;" u2="&#x2018;" k="52" />
    <hkern u1="&#x44a;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x44a;" u2="&#x4dd;" k="21" />
    <hkern u1="&#x44a;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x44a;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x44a;" u2="&#x4c2;" k="21" />
    <hkern u1="&#x44a;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x44a;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x44a;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x44a;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x44a;" u2="&#x4ab;" k="11" />
    <hkern u1="&#x44a;" u2="&#x4aa;" k="41" />
    <hkern u1="&#x44a;" u2="&#x49d;" k="10" />
    <hkern u1="&#x44a;" u2="&#x49b;" k="10" />
    <hkern u1="&#x44a;" u2="&#x497;" k="21" />
    <hkern u1="&#x44a;" u2="&#x496;" k="21" />
    <hkern u1="&#x44a;" u2="&#x45c;" k="10" />
    <hkern u1="&#x44a;" u2="&#x45b;" k="41" />
    <hkern u1="&#x44a;" u2="&#x44f;" k="32" />
    <hkern u1="&#x44a;" u2="&#x44a;" k="41" />
    <hkern u1="&#x44a;" u2="&#x447;" k="41" />
    <hkern u1="&#x44a;" u2="&#x445;" k="21" />
    <hkern u1="&#x44a;" u2="&#x442;" k="32" />
    <hkern u1="&#x44a;" u2="&#x441;" k="11" />
    <hkern u1="&#x44a;" u2="&#x43a;" k="10" />
    <hkern u1="&#x44a;" u2="&#x436;" k="21" />
    <hkern u1="&#x44a;" u2="&#x431;" k="21" />
    <hkern u1="&#x44a;" u2="&#x421;" k="41" />
    <hkern u1="&#x44a;" u2="&#x416;" k="21" />
    <hkern u1="&#x44c;" g2="afii10097.var.accented" k="21" />
    <hkern u1="&#x44c;" g2="afii10085.var.accented" k="42" />
    <hkern u1="&#x44c;" u2="&#x2122;" k="31" />
    <hkern u1="&#x44c;" u2="&#x201c;" k="63" />
    <hkern u1="&#x44c;" u2="&#x2018;" k="83" />
    <hkern u1="&#x44c;" u2="&#x4f5;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4f3;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4f1;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4ef;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4dd;" k="21" />
    <hkern u1="&#x44c;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x44c;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x44c;" u2="&#x4c2;" k="21" />
    <hkern u1="&#x44c;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x44c;" u2="&#x4bb;" k="32" />
    <hkern u1="&#x44c;" u2="&#x4b9;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4b7;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4b3;" k="32" />
    <hkern u1="&#x44c;" u2="&#x4af;" k="42" />
    <hkern u1="&#x44c;" u2="&#x4ab;" k="11" />
    <hkern u1="&#x44c;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x44c;" u2="&#x49d;" k="10" />
    <hkern u1="&#x44c;" u2="&#x49b;" k="10" />
    <hkern u1="&#x44c;" u2="&#x497;" k="21" />
    <hkern u1="&#x44c;" u2="&#x496;" k="21" />
    <hkern u1="&#x44c;" u2="&#x45e;" k="42" />
    <hkern u1="&#x44c;" u2="&#x45c;" k="10" />
    <hkern u1="&#x44c;" u2="&#x45b;" k="42" />
    <hkern u1="&#x44c;" u2="&#x452;" k="32" />
    <hkern u1="&#x44c;" u2="&#x44f;" k="21" />
    <hkern u1="&#x44c;" u2="&#x44a;" k="41" />
    <hkern u1="&#x44c;" u2="&#x447;" k="42" />
    <hkern u1="&#x44c;" u2="&#x445;" k="32" />
    <hkern u1="&#x44c;" u2="&#x443;" k="42" />
    <hkern u1="&#x44c;" u2="&#x442;" k="42" />
    <hkern u1="&#x44c;" u2="&#x441;" k="11" />
    <hkern u1="&#x44c;" u2="&#x43a;" k="10" />
    <hkern u1="&#x44c;" u2="&#x436;" k="21" />
    <hkern u1="&#x44c;" u2="&#x431;" k="11" />
    <hkern u1="&#x44c;" u2="&#x421;" k="21" />
    <hkern u1="&#x44c;" u2="&#x416;" k="21" />
    <hkern u1="&#x44d;" u2="&#x4af;" k="10" />
    <hkern u1="&#x44d;" u2="&#x422;" k="32" />
    <hkern u1="&#x44d;" u2="&#x40b;" k="32" />
    <hkern u1="&#x44d;" u2="&#x402;" k="32" />
    <hkern u1="&#x44e;" u2="&#x4af;" k="10" />
    <hkern u1="&#x44e;" u2="&#x422;" k="42" />
    <hkern u1="&#x44e;" u2="&#x40b;" k="42" />
    <hkern u1="&#x44e;" u2="&#x402;" k="42" />
    <hkern u1="&#x453;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x453;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x453;" g2="afii10080.var.accented" k="11" />
    <hkern u1="&#x453;" u2="&#x2026;" k="52" />
    <hkern u1="&#x453;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x453;" u2="&#x4eb;" k="11" />
    <hkern u1="&#x453;" u2="&#x4e9;" k="11" />
    <hkern u1="&#x453;" u2="&#x4e7;" k="11" />
    <hkern u1="&#x453;" u2="&#x4dc;" k="20" />
    <hkern u1="&#x453;" u2="&#x4c1;" k="20" />
    <hkern u1="&#x453;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x453;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x453;" u2="&#x4b3;" k="10" />
    <hkern u1="&#x453;" u2="&#x4b2;" k="20" />
    <hkern u1="&#x453;" u2="&#x496;" k="20" />
    <hkern u1="&#x453;" u2="&#x493;" k="10" />
    <hkern u1="&#x453;" u2="&#x491;" k="10" />
    <hkern u1="&#x453;" u2="&#x473;" k="11" />
    <hkern u1="&#x453;" u2="&#x454;" k="10" />
    <hkern u1="&#x453;" u2="&#x453;" k="10" />
    <hkern u1="&#x453;" u2="&#x44f;" k="10" />
    <hkern u1="&#x453;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x453;" u2="&#x447;" k="10" />
    <hkern u1="&#x453;" u2="&#x445;" k="10" />
    <hkern u1="&#x453;" u2="&#x442;" k="-21" />
    <hkern u1="&#x453;" u2="&#x43e;" k="11" />
    <hkern u1="&#x453;" u2="&#x43c;" k="20" />
    <hkern u1="&#x453;" u2="&#x434;" k="31" />
    <hkern u1="&#x453;" u2="&#x433;" k="10" />
    <hkern u1="&#x453;" u2="&#x431;" k="10" />
    <hkern u1="&#x453;" u2="&#x425;" k="20" />
    <hkern u1="&#x453;" u2="&#x41c;" k="20" />
    <hkern u1="&#x453;" u2="&#x416;" k="20" />
    <hkern u1="&#x453;" u2="&#x414;" k="20" />
    <hkern u1="&#x453;" u2="&#x3a;" k="-21" />
    <hkern u1="&#x453;" u2="&#x2e;" k="42" />
    <hkern u1="&#x454;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x454;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x454;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x454;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x454;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x454;" u2="&#x459;" k="-10" />
    <hkern u1="&#x454;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x454;" u2="&#x442;" k="-11" />
    <hkern u1="&#x454;" u2="&#x434;" k="-10" />
    <hkern u1="&#x454;" u2="&#x423;" k="21" />
    <hkern u1="&#x454;" u2="&#x414;" k="-11" />
    <hkern u1="&#x454;" u2="&#x40e;" k="21" />
    <hkern u1="&#x455;" u2="&#x4af;" k="-10" />
    <hkern u1="&#x457;" u2="&#x457;" k="-21" />
    <hkern u1="&#x459;" g2="afii10097.var.accented" k="32" />
    <hkern u1="&#x459;" g2="afii10065.var.accented" k="10" />
    <hkern u1="&#x459;" g2="afii10032.var.accented" k="31" />
    <hkern u1="&#x459;" u2="&#x2122;" k="52" />
    <hkern u1="&#x459;" u2="&#x201c;" k="52" />
    <hkern u1="&#x459;" u2="&#x2018;" k="63" />
    <hkern u1="&#x459;" u2="&#x4f5;" k="31" />
    <hkern u1="&#x459;" u2="&#x4ea;" k="31" />
    <hkern u1="&#x459;" u2="&#x4e8;" k="31" />
    <hkern u1="&#x459;" u2="&#x4e6;" k="31" />
    <hkern u1="&#x459;" u2="&#x4dd;" k="21" />
    <hkern u1="&#x459;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x459;" u2="&#x4d8;" k="31" />
    <hkern u1="&#x459;" u2="&#x4d3;" k="10" />
    <hkern u1="&#x459;" u2="&#x4d1;" k="10" />
    <hkern u1="&#x459;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x459;" u2="&#x4c2;" k="21" />
    <hkern u1="&#x459;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x459;" u2="&#x4bb;" k="42" />
    <hkern u1="&#x459;" u2="&#x4b9;" k="31" />
    <hkern u1="&#x459;" u2="&#x4b7;" k="31" />
    <hkern u1="&#x459;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x459;" u2="&#x4ab;" k="11" />
    <hkern u1="&#x459;" u2="&#x4aa;" k="41" />
    <hkern u1="&#x459;" u2="&#x49d;" k="10" />
    <hkern u1="&#x459;" u2="&#x49b;" k="10" />
    <hkern u1="&#x459;" u2="&#x497;" k="21" />
    <hkern u1="&#x459;" u2="&#x496;" k="21" />
    <hkern u1="&#x459;" u2="&#x493;" k="21" />
    <hkern u1="&#x459;" u2="&#x491;" k="21" />
    <hkern u1="&#x459;" u2="&#x472;" k="31" />
    <hkern u1="&#x459;" u2="&#x45c;" k="10" />
    <hkern u1="&#x459;" u2="&#x45b;" k="41" />
    <hkern u1="&#x459;" u2="&#x453;" k="21" />
    <hkern u1="&#x459;" u2="&#x452;" k="42" />
    <hkern u1="&#x459;" u2="&#x44f;" k="32" />
    <hkern u1="&#x459;" u2="&#x44a;" k="41" />
    <hkern u1="&#x459;" u2="&#x447;" k="31" />
    <hkern u1="&#x459;" u2="&#x445;" k="21" />
    <hkern u1="&#x459;" u2="&#x442;" k="42" />
    <hkern u1="&#x459;" u2="&#x441;" k="11" />
    <hkern u1="&#x459;" u2="&#x43a;" k="10" />
    <hkern u1="&#x459;" u2="&#x436;" k="21" />
    <hkern u1="&#x459;" u2="&#x433;" k="21" />
    <hkern u1="&#x459;" u2="&#x431;" k="21" />
    <hkern u1="&#x459;" u2="&#x430;" k="10" />
    <hkern u1="&#x459;" u2="&#x424;" k="31" />
    <hkern u1="&#x459;" u2="&#x421;" k="41" />
    <hkern u1="&#x459;" u2="&#x41e;" k="31" />
    <hkern u1="&#x459;" u2="&#x41c;" k="11" />
    <hkern u1="&#x459;" u2="&#x416;" k="21" />
    <hkern u1="&#x459;" u2="&#x404;" k="31" />
    <hkern u1="&#x45a;" g2="afii10097.var.accented" k="32" />
    <hkern u1="&#x45a;" u2="&#x2122;" k="42" />
    <hkern u1="&#x45a;" u2="&#x201c;" k="21" />
    <hkern u1="&#x45a;" u2="&#x2018;" k="42" />
    <hkern u1="&#x45a;" u2="&#x4f5;" k="31" />
    <hkern u1="&#x45a;" u2="&#x4dd;" k="21" />
    <hkern u1="&#x45a;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x45a;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x45a;" u2="&#x4c2;" k="21" />
    <hkern u1="&#x45a;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x45a;" u2="&#x4bb;" k="21" />
    <hkern u1="&#x45a;" u2="&#x4b9;" k="31" />
    <hkern u1="&#x45a;" u2="&#x4b7;" k="31" />
    <hkern u1="&#x45a;" u2="&#x4b3;" k="10" />
    <hkern u1="&#x45a;" u2="&#x4ab;" k="11" />
    <hkern u1="&#x45a;" u2="&#x4aa;" k="41" />
    <hkern u1="&#x45a;" u2="&#x49d;" k="10" />
    <hkern u1="&#x45a;" u2="&#x49b;" k="10" />
    <hkern u1="&#x45a;" u2="&#x497;" k="21" />
    <hkern u1="&#x45a;" u2="&#x496;" k="21" />
    <hkern u1="&#x45a;" u2="&#x45c;" k="10" />
    <hkern u1="&#x45a;" u2="&#x45b;" k="31" />
    <hkern u1="&#x45a;" u2="&#x452;" k="21" />
    <hkern u1="&#x45a;" u2="&#x44f;" k="32" />
    <hkern u1="&#x45a;" u2="&#x44a;" k="41" />
    <hkern u1="&#x45a;" u2="&#x447;" k="31" />
    <hkern u1="&#x45a;" u2="&#x445;" k="10" />
    <hkern u1="&#x45a;" u2="&#x442;" k="31" />
    <hkern u1="&#x45a;" u2="&#x441;" k="11" />
    <hkern u1="&#x45a;" u2="&#x43a;" k="10" />
    <hkern u1="&#x45a;" u2="&#x436;" k="21" />
    <hkern u1="&#x45a;" u2="&#x431;" k="21" />
    <hkern u1="&#x45a;" u2="&#x421;" k="41" />
    <hkern u1="&#x45a;" u2="&#x41c;" k="10" />
    <hkern u1="&#x45a;" u2="&#x416;" k="21" />
    <hkern u1="&#x45c;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x45c;" u2="&#xf6bc;" k="-42" />
    <hkern u1="&#x45c;" u2="&#xf6bb;" k="-42" />
    <hkern u1="&#x45c;" u2="&#xf6ba;" k="-42" />
    <hkern u1="&#x45c;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x45c;" g2="afii10085.var.accented" k="-11" />
    <hkern u1="&#x45c;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x45c;" u2="&#x2014;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x2013;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x2012;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x2011;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x2010;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x45c;" u2="&#x4f3;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x45c;" u2="&#x4f1;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x45c;" u2="&#x4ef;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x45c;" u2="&#x4df;" k="10" />
    <hkern u1="&#x45c;" u2="&#x4dd;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x4c2;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x45c;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x45c;" u2="&#x4af;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x45c;" u2="&#x499;" k="10" />
    <hkern u1="&#x45c;" u2="&#x497;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x45e;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x459;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x44d;" k="10" />
    <hkern u1="&#x45c;" u2="&#x447;" k="10" />
    <hkern u1="&#x45c;" u2="&#x443;" k="-11" />
    <hkern u1="&#x45c;" u2="&#x442;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x45c;" u2="&#x437;" k="10" />
    <hkern u1="&#x45c;" u2="&#x436;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x434;" k="-10" />
    <hkern u1="&#x45c;" u2="&#x423;" k="31" />
    <hkern u1="&#x45c;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x45c;" u2="&#x414;" k="-21" />
    <hkern u1="&#x45c;" u2="&#x40e;" k="31" />
    <hkern u1="&#x45c;" u2="&#x409;" k="-21" />
    <hkern u1="&#x45c;" u2="&#xad;" k="-42" />
    <hkern u1="&#x45c;" u2="&#x2d;" k="-42" />
    <hkern u1="&#x45e;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x45e;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x45e;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x45e;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x497;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x45e;" u2="&#x445;" k="-10" />
    <hkern u1="&#x45e;" u2="&#x442;" k="-21" />
    <hkern u1="&#x45e;" u2="&#x436;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x3b;" k="-20" />
    <hkern u1="&#x45e;" u2="&#x3a;" k="-20" />
    <hkern u1="&#x45f;" g2="afii10037.var.accented" k="21" />
    <hkern u1="&#x45f;" u2="&#x4f2;" k="21" />
    <hkern u1="&#x45f;" u2="&#x4f0;" k="21" />
    <hkern u1="&#x45f;" u2="&#x4ee;" k="21" />
    <hkern u1="&#x45f;" u2="&#x4ae;" k="21" />
    <hkern u1="&#x45f;" u2="&#x423;" k="21" />
    <hkern u1="&#x45f;" u2="&#x40e;" k="21" />
    <hkern u1="&#x472;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x472;" u2="&#x201d;" k="-32" />
    <hkern u1="&#x472;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x472;" u2="&#x2019;" k="-32" />
    <hkern u1="&#x472;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x473;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x473;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x473;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x473;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x473;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x473;" u2="&#x423;" k="52" />
    <hkern u1="&#x473;" u2="&#x40e;" k="52" />
    <hkern u1="&#x490;" g2="afii10071.var.accented" k="34" />
    <hkern u1="&#x490;" g2="afii10097.var.accented" k="114" />
    <hkern u1="&#x490;" g2="afii10096.var.accented" k="42" />
    <hkern u1="&#x490;" g2="afii10095.var.accented" k="74" />
    <hkern u1="&#x490;" g2="afii10093.var.accented" k="42" />
    <hkern u1="&#x490;" g2="afii10085.var.accented" k="83" />
    <hkern u1="&#x490;" g2="afii10080.var.accented" k="114" />
    <hkern u1="&#x490;" g2="afii10074.var.accented" k="42" />
    <hkern u1="&#x490;" g2="afii10070.var.accented" k="114" />
    <hkern u1="&#x490;" g2="afii10065.var.accented" k="104" />
    <hkern u1="&#x490;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x490;" g2="afii10047.var.accented" k="21" />
    <hkern u1="&#x490;" g2="afii10032.var.accented" k="31" />
    <hkern u1="&#x490;" g2="afii10017.var.accented" k="83" />
    <hkern u1="&#x490;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x490;" u2="&#x2026;" k="84" />
    <hkern u1="&#x490;" u2="&#x201d;" k="-31" />
    <hkern u1="&#x490;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x490;" u2="&#x2019;" k="-31" />
    <hkern u1="&#x490;" u2="&#x2018;" k="-42" />
    <hkern u1="&#x490;" u2="&#x4f9;" k="52" />
    <hkern u1="&#x490;" u2="&#x4f5;" k="94" />
    <hkern u1="&#x490;" u2="&#x4f3;" k="83" />
    <hkern u1="&#x490;" u2="&#x4f1;" k="83" />
    <hkern u1="&#x490;" u2="&#x4ef;" k="83" />
    <hkern u1="&#x490;" u2="&#x4eb;" k="83" />
    <hkern u1="&#x490;" u2="&#x4ea;" k="31" />
    <hkern u1="&#x490;" u2="&#x4e9;" k="83" />
    <hkern u1="&#x490;" u2="&#x4e8;" k="31" />
    <hkern u1="&#x490;" u2="&#x4e7;" k="83" />
    <hkern u1="&#x490;" u2="&#x4e6;" k="31" />
    <hkern u1="&#x490;" u2="&#x4e5;" k="62" />
    <hkern u1="&#x490;" u2="&#x4e3;" k="62" />
    <hkern u1="&#x490;" u2="&#x4df;" k="104" />
    <hkern u1="&#x490;" u2="&#x4de;" k="31" />
    <hkern u1="&#x490;" u2="&#x4dd;" k="94" />
    <hkern u1="&#x490;" u2="&#x4d9;" k="114" />
    <hkern u1="&#x490;" u2="&#x4d8;" k="31" />
    <hkern u1="&#x490;" u2="&#x4d7;" k="114" />
    <hkern u1="&#x490;" u2="&#x4d3;" k="104" />
    <hkern u1="&#x490;" u2="&#x4d2;" k="83" />
    <hkern u1="&#x490;" u2="&#x4d1;" k="104" />
    <hkern u1="&#x490;" u2="&#x4d0;" k="83" />
    <hkern u1="&#x490;" u2="&#x4c8;" k="62" />
    <hkern u1="&#x490;" u2="&#x4c4;" k="73" />
    <hkern u1="&#x490;" u2="&#x4c2;" k="94" />
    <hkern u1="&#x490;" u2="&#x4b9;" k="94" />
    <hkern u1="&#x490;" u2="&#x4b7;" k="94" />
    <hkern u1="&#x490;" u2="&#x4b3;" k="104" />
    <hkern u1="&#x490;" u2="&#x4af;" k="83" />
    <hkern u1="&#x490;" u2="&#x4ab;" k="114" />
    <hkern u1="&#x490;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x490;" u2="&#x4a3;" k="62" />
    <hkern u1="&#x490;" u2="&#x49d;" k="73" />
    <hkern u1="&#x490;" u2="&#x49b;" k="73" />
    <hkern u1="&#x490;" u2="&#x499;" k="104" />
    <hkern u1="&#x490;" u2="&#x498;" k="31" />
    <hkern u1="&#x490;" u2="&#x497;" k="94" />
    <hkern u1="&#x490;" u2="&#x493;" k="52" />
    <hkern u1="&#x490;" u2="&#x491;" k="52" />
    <hkern u1="&#x490;" u2="&#x473;" k="83" />
    <hkern u1="&#x490;" u2="&#x472;" k="31" />
    <hkern u1="&#x490;" u2="&#x45f;" k="42" />
    <hkern u1="&#x490;" u2="&#x45e;" k="43" />
    <hkern u1="&#x490;" u2="&#x45c;" k="42" />
    <hkern u1="&#x490;" u2="&#x45a;" k="42" />
    <hkern u1="&#x490;" u2="&#x459;" k="94" />
    <hkern u1="&#x490;" u2="&#x458;" k="62" />
    <hkern u1="&#x490;" u2="&#x457;" k="2" />
    <hkern u1="&#x490;" u2="&#x456;" k="12" />
    <hkern u1="&#x490;" u2="&#x454;" k="114" />
    <hkern u1="&#x490;" u2="&#x453;" k="42" />
    <hkern u1="&#x490;" u2="&#x451;" k="44" />
    <hkern u1="&#x490;" u2="&#x44f;" k="114" />
    <hkern u1="&#x490;" u2="&#x44e;" k="62" />
    <hkern u1="&#x490;" u2="&#x44d;" k="104" />
    <hkern u1="&#x490;" u2="&#x44c;" k="52" />
    <hkern u1="&#x490;" u2="&#x44b;" k="52" />
    <hkern u1="&#x490;" u2="&#x44a;" k="104" />
    <hkern u1="&#x490;" u2="&#x449;" k="52" />
    <hkern u1="&#x490;" u2="&#x448;" k="62" />
    <hkern u1="&#x490;" u2="&#x447;" k="94" />
    <hkern u1="&#x490;" u2="&#x446;" k="62" />
    <hkern u1="&#x490;" u2="&#x445;" k="104" />
    <hkern u1="&#x490;" u2="&#x444;" k="104" />
    <hkern u1="&#x490;" u2="&#x443;" k="83" />
    <hkern u1="&#x490;" u2="&#x442;" k="83" />
    <hkern u1="&#x490;" u2="&#x441;" k="114" />
    <hkern u1="&#x490;" u2="&#x440;" k="62" />
    <hkern u1="&#x490;" u2="&#x43f;" k="62" />
    <hkern u1="&#x490;" u2="&#x43e;" k="83" />
    <hkern u1="&#x490;" u2="&#x43d;" k="62" />
    <hkern u1="&#x490;" u2="&#x43c;" k="83" />
    <hkern u1="&#x490;" u2="&#x43b;" k="94" />
    <hkern u1="&#x490;" u2="&#x43a;" k="73" />
    <hkern u1="&#x490;" u2="&#x439;" k="62" />
    <hkern u1="&#x490;" u2="&#x438;" k="62" />
    <hkern u1="&#x490;" u2="&#x437;" k="104" />
    <hkern u1="&#x490;" u2="&#x436;" k="94" />
    <hkern u1="&#x490;" u2="&#x435;" k="114" />
    <hkern u1="&#x490;" u2="&#x434;" k="94" />
    <hkern u1="&#x490;" u2="&#x433;" k="52" />
    <hkern u1="&#x490;" u2="&#x432;" k="42" />
    <hkern u1="&#x490;" u2="&#x431;" k="42" />
    <hkern u1="&#x490;" u2="&#x430;" k="104" />
    <hkern u1="&#x490;" u2="&#x42f;" k="31" />
    <hkern u1="&#x490;" u2="&#x42d;" k="21" />
    <hkern u1="&#x490;" u2="&#x424;" k="42" />
    <hkern u1="&#x490;" u2="&#x422;" k="-21" />
    <hkern u1="&#x490;" u2="&#x421;" k="52" />
    <hkern u1="&#x490;" u2="&#x41e;" k="31" />
    <hkern u1="&#x490;" u2="&#x41c;" k="31" />
    <hkern u1="&#x490;" u2="&#x41b;" k="62" />
    <hkern u1="&#x490;" u2="&#x417;" k="31" />
    <hkern u1="&#x490;" u2="&#x414;" k="52" />
    <hkern u1="&#x490;" u2="&#x410;" k="83" />
    <hkern u1="&#x490;" u2="&#x40b;" k="-21" />
    <hkern u1="&#x490;" u2="&#x409;" k="62" />
    <hkern u1="&#x490;" u2="&#x404;" k="52" />
    <hkern u1="&#x490;" u2="&#x402;" k="-21" />
    <hkern u1="&#x490;" u2="&#xae;" k="21" />
    <hkern u1="&#x490;" u2="&#x3b;" k="21" />
    <hkern u1="&#x490;" u2="&#x3a;" k="21" />
    <hkern u1="&#x490;" u2="&#x2e;" k="52" />
    <hkern u1="&#x490;" u2="&#x2c;" k="62" />
    <hkern u1="&#x491;" g2="hyphenminus" k="-63" />
    <hkern u1="&#x491;" u2="&#xf6bc;" k="-62" />
    <hkern u1="&#x491;" u2="&#xf6bb;" k="-62" />
    <hkern u1="&#x491;" u2="&#xf6ba;" k="-62" />
    <hkern u1="&#x491;" g2="afii10071.var.accented" k="20" />
    <hkern u1="&#x491;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x491;" g2="afii10095.var.accented" k="20" />
    <hkern u1="&#x491;" g2="afii10085.var.accented" k="-10" />
    <hkern u1="&#x491;" g2="afii10080.var.accented" k="11" />
    <hkern u1="&#x491;" g2="afii10070.var.accented" k="20" />
    <hkern u1="&#x491;" g2="afii10065.var.accented" k="10" />
    <hkern u1="&#x491;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x491;" g2="afii10017.var.accented" k="20" />
    <hkern u1="&#x491;" u2="&#x2026;" k="52" />
    <hkern u1="&#x491;" u2="&#x2014;" k="-62" />
    <hkern u1="&#x491;" u2="&#x2013;" k="-62" />
    <hkern u1="&#x491;" u2="&#x2012;" k="-62" />
    <hkern u1="&#x491;" u2="&#x2011;" k="-62" />
    <hkern u1="&#x491;" u2="&#x2010;" k="-62" />
    <hkern u1="&#x491;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x491;" u2="&#x4f3;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x491;" u2="&#x4f1;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x491;" u2="&#x4ef;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x491;" u2="&#x4eb;" k="11" />
    <hkern u1="&#x491;" u2="&#x4e9;" k="11" />
    <hkern u1="&#x491;" u2="&#x4e7;" k="11" />
    <hkern u1="&#x491;" u2="&#x4df;" k="20" />
    <hkern u1="&#x491;" u2="&#x4dd;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4dc;" k="20" />
    <hkern u1="&#x491;" u2="&#x4d9;" k="20" />
    <hkern u1="&#x491;" u2="&#x4d7;" k="20" />
    <hkern u1="&#x491;" u2="&#x4d3;" k="10" />
    <hkern u1="&#x491;" u2="&#x4d2;" k="20" />
    <hkern u1="&#x491;" u2="&#x4d1;" k="10" />
    <hkern u1="&#x491;" u2="&#x4d0;" k="20" />
    <hkern u1="&#x491;" u2="&#x4c2;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4c1;" k="20" />
    <hkern u1="&#x491;" u2="&#x4bb;" k="10" />
    <hkern u1="&#x491;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x491;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x491;" u2="&#x4b3;" k="10" />
    <hkern u1="&#x491;" u2="&#x4b2;" k="20" />
    <hkern u1="&#x491;" u2="&#x4af;" k="-10" />
    <hkern u1="&#x491;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x491;" u2="&#x4ab;" k="20" />
    <hkern u1="&#x491;" u2="&#x499;" k="20" />
    <hkern u1="&#x491;" u2="&#x497;" k="-10" />
    <hkern u1="&#x491;" u2="&#x496;" k="20" />
    <hkern u1="&#x491;" u2="&#x493;" k="10" />
    <hkern u1="&#x491;" u2="&#x491;" k="10" />
    <hkern u1="&#x491;" u2="&#x473;" k="11" />
    <hkern u1="&#x491;" u2="&#x45e;" k="-10" />
    <hkern u1="&#x491;" u2="&#x45b;" k="10" />
    <hkern u1="&#x491;" u2="&#x459;" k="30" />
    <hkern u1="&#x491;" u2="&#x454;" k="10" />
    <hkern u1="&#x491;" u2="&#x453;" k="10" />
    <hkern u1="&#x491;" u2="&#x452;" k="10" />
    <hkern u1="&#x491;" u2="&#x451;" k="20" />
    <hkern u1="&#x491;" u2="&#x44f;" k="10" />
    <hkern u1="&#x491;" u2="&#x44d;" k="20" />
    <hkern u1="&#x491;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x491;" u2="&#x447;" k="10" />
    <hkern u1="&#x491;" u2="&#x445;" k="10" />
    <hkern u1="&#x491;" u2="&#x444;" k="20" />
    <hkern u1="&#x491;" u2="&#x443;" k="-10" />
    <hkern u1="&#x491;" u2="&#x442;" k="-21" />
    <hkern u1="&#x491;" u2="&#x441;" k="20" />
    <hkern u1="&#x491;" u2="&#x43e;" k="11" />
    <hkern u1="&#x491;" u2="&#x43c;" k="20" />
    <hkern u1="&#x491;" u2="&#x43b;" k="30" />
    <hkern u1="&#x491;" u2="&#x437;" k="20" />
    <hkern u1="&#x491;" u2="&#x436;" k="-10" />
    <hkern u1="&#x491;" u2="&#x435;" k="20" />
    <hkern u1="&#x491;" u2="&#x434;" k="31" />
    <hkern u1="&#x491;" u2="&#x433;" k="10" />
    <hkern u1="&#x491;" u2="&#x430;" k="10" />
    <hkern u1="&#x491;" u2="&#x425;" k="20" />
    <hkern u1="&#x491;" u2="&#x423;" k="31" />
    <hkern u1="&#x491;" u2="&#x422;" k="31" />
    <hkern u1="&#x491;" u2="&#x41c;" k="20" />
    <hkern u1="&#x491;" u2="&#x41b;" k="31" />
    <hkern u1="&#x491;" u2="&#x416;" k="20" />
    <hkern u1="&#x491;" u2="&#x414;" k="20" />
    <hkern u1="&#x491;" u2="&#x410;" k="20" />
    <hkern u1="&#x491;" u2="&#x40e;" k="31" />
    <hkern u1="&#x491;" u2="&#x40b;" k="31" />
    <hkern u1="&#x491;" u2="&#x409;" k="31" />
    <hkern u1="&#x491;" u2="&#x402;" k="31" />
    <hkern u1="&#x491;" u2="&#xad;" k="-62" />
    <hkern u1="&#x491;" u2="&#x3a;" k="-21" />
    <hkern u1="&#x491;" u2="&#x2e;" k="42" />
    <hkern u1="&#x491;" u2="&#x2d;" k="-62" />
    <hkern u1="&#x491;" u2="&#x2c;" k="62" />
    <hkern u1="&#x492;" g2="afii10071.var.accented" k="33" />
    <hkern u1="&#x492;" g2="afii10097.var.accented" k="73" />
    <hkern u1="&#x492;" g2="afii10095.var.accented" k="63" />
    <hkern u1="&#x492;" g2="afii10049.var.accented" k="42" />
    <hkern u1="&#x492;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x492;" u2="&#x2026;" k="52" />
    <hkern u1="&#x492;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x492;" u2="&#x4f5;" k="73" />
    <hkern u1="&#x492;" u2="&#x4e5;" k="52" />
    <hkern u1="&#x492;" u2="&#x4e3;" k="52" />
    <hkern u1="&#x492;" u2="&#x4dd;" k="73" />
    <hkern u1="&#x492;" u2="&#x4dc;" k="11" />
    <hkern u1="&#x492;" u2="&#x4c4;" k="52" />
    <hkern u1="&#x492;" u2="&#x4c2;" k="73" />
    <hkern u1="&#x492;" u2="&#x4c1;" k="11" />
    <hkern u1="&#x492;" u2="&#x4b9;" k="73" />
    <hkern u1="&#x492;" u2="&#x4b7;" k="73" />
    <hkern u1="&#x492;" u2="&#x4b3;" k="94" />
    <hkern u1="&#x492;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x492;" u2="&#x49d;" k="52" />
    <hkern u1="&#x492;" u2="&#x49b;" k="52" />
    <hkern u1="&#x492;" u2="&#x497;" k="73" />
    <hkern u1="&#x492;" u2="&#x496;" k="11" />
    <hkern u1="&#x492;" u2="&#x473;" k="73" />
    <hkern u1="&#x492;" u2="&#x472;" k="42" />
    <hkern u1="&#x492;" u2="&#x45e;" k="23" />
    <hkern u1="&#x492;" u2="&#x458;" k="1" />
    <hkern u1="&#x492;" u2="&#x457;" k="2" />
    <hkern u1="&#x492;" u2="&#x456;" k="1" />
    <hkern u1="&#x492;" u2="&#x455;" k="90" />
    <hkern u1="&#x492;" u2="&#x451;" k="33" />
    <hkern u1="&#x492;" u2="&#x44f;" k="83" />
    <hkern u1="&#x492;" u2="&#x44d;" k="73" />
    <hkern u1="&#x492;" u2="&#x44a;" k="83" />
    <hkern u1="&#x492;" u2="&#x447;" k="93" />
    <hkern u1="&#x492;" u2="&#x445;" k="94" />
    <hkern u1="&#x492;" u2="&#x444;" k="83" />
    <hkern u1="&#x492;" u2="&#x442;" k="73" />
    <hkern u1="&#x492;" u2="&#x43c;" k="52" />
    <hkern u1="&#x492;" u2="&#x436;" k="73" />
    <hkern u1="&#x492;" u2="&#x434;" k="73" />
    <hkern u1="&#x492;" u2="&#x431;" k="52" />
    <hkern u1="&#x492;" u2="&#x42f;" k="42" />
    <hkern u1="&#x492;" u2="&#x424;" k="31" />
    <hkern u1="&#x492;" u2="&#x421;" k="52" />
    <hkern u1="&#x492;" u2="&#x41c;" k="31" />
    <hkern u1="&#x492;" u2="&#x416;" k="11" />
    <hkern u1="&#x492;" u2="&#x414;" k="42" />
    <hkern u1="&#x492;" u2="&#x404;" k="52" />
    <hkern u1="&#x492;" u2="&#xae;" k="31" />
    <hkern u1="&#x492;" u2="&#xa9;" k="21" />
    <hkern u1="&#x492;" u2="&#x2e;" k="62" />
    <hkern u1="&#x493;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x493;" g2="afii10080.var.accented" k="11" />
    <hkern u1="&#x493;" u2="&#x2026;" k="52" />
    <hkern u1="&#x493;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x493;" u2="&#x4eb;" k="11" />
    <hkern u1="&#x493;" u2="&#x4e9;" k="11" />
    <hkern u1="&#x493;" u2="&#x4e7;" k="11" />
    <hkern u1="&#x493;" u2="&#x4dc;" k="20" />
    <hkern u1="&#x493;" u2="&#x4c1;" k="20" />
    <hkern u1="&#x493;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x493;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x493;" u2="&#x4b3;" k="10" />
    <hkern u1="&#x493;" u2="&#x4b2;" k="20" />
    <hkern u1="&#x493;" u2="&#x496;" k="20" />
    <hkern u1="&#x493;" u2="&#x493;" k="10" />
    <hkern u1="&#x493;" u2="&#x491;" k="10" />
    <hkern u1="&#x493;" u2="&#x473;" k="11" />
    <hkern u1="&#x493;" u2="&#x454;" k="10" />
    <hkern u1="&#x493;" u2="&#x453;" k="10" />
    <hkern u1="&#x493;" u2="&#x44f;" k="10" />
    <hkern u1="&#x493;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x493;" u2="&#x447;" k="10" />
    <hkern u1="&#x493;" u2="&#x445;" k="10" />
    <hkern u1="&#x493;" u2="&#x442;" k="-21" />
    <hkern u1="&#x493;" u2="&#x43e;" k="11" />
    <hkern u1="&#x493;" u2="&#x43c;" k="20" />
    <hkern u1="&#x493;" u2="&#x434;" k="31" />
    <hkern u1="&#x493;" u2="&#x433;" k="10" />
    <hkern u1="&#x493;" u2="&#x425;" k="20" />
    <hkern u1="&#x493;" u2="&#x41c;" k="20" />
    <hkern u1="&#x493;" u2="&#x416;" k="20" />
    <hkern u1="&#x493;" u2="&#x414;" k="20" />
    <hkern u1="&#x493;" u2="&#x2e;" k="42" />
    <hkern u1="&#x496;" g2="afii10049.var.accented" k="11" />
    <hkern u1="&#x496;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x496;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x496;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x496;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x496;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x496;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x496;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x496;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x496;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x496;" u2="&#x44a;" k="21" />
    <hkern u1="&#x496;" u2="&#x447;" k="21" />
    <hkern u1="&#x496;" u2="&#x444;" k="11" />
    <hkern u1="&#x496;" u2="&#x442;" k="32" />
    <hkern u1="&#x496;" u2="&#x441;" k="21" />
    <hkern u1="&#x496;" u2="&#x42f;" k="11" />
    <hkern u1="&#x496;" u2="&#x427;" k="21" />
    <hkern u1="&#x496;" u2="&#x424;" k="12" />
    <hkern u1="&#x496;" u2="&#x421;" k="21" />
    <hkern u1="&#x496;" u2="&#x414;" k="-20" />
    <hkern u1="&#x497;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x497;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x497;" u2="&#x497;" k="-20" />
    <hkern u1="&#x497;" u2="&#x459;" k="-20" />
    <hkern u1="&#x497;" u2="&#x442;" k="-10" />
    <hkern u1="&#x497;" u2="&#x436;" k="-20" />
    <hkern u1="&#x497;" u2="&#x434;" k="-10" />
    <hkern u1="&#x497;" u2="&#x414;" k="-20" />
    <hkern u1="&#x498;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x498;" g2="afii10047.var.accented" k="11" />
    <hkern u1="&#x498;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x498;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x498;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x498;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x498;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x498;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x498;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x498;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x498;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x498;" u2="&#x4de;" k="21" />
    <hkern u1="&#x498;" u2="&#x4b2;" k="11" />
    <hkern u1="&#x498;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x498;" u2="&#x498;" k="21" />
    <hkern u1="&#x498;" u2="&#x42f;" k="21" />
    <hkern u1="&#x498;" u2="&#x42d;" k="11" />
    <hkern u1="&#x498;" u2="&#x425;" k="11" />
    <hkern u1="&#x498;" u2="&#x423;" k="31" />
    <hkern u1="&#x498;" u2="&#x417;" k="21" />
    <hkern u1="&#x498;" u2="&#x40e;" k="31" />
    <hkern u1="&#x498;" u2="&#x402;" k="21" />
    <hkern u1="&#x499;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x499;" g2="afii10037.var.accented" k="42" />
    <hkern u1="&#x499;" u2="&#x4f5;" k="32" />
    <hkern u1="&#x499;" u2="&#x4f2;" k="42" />
    <hkern u1="&#x499;" u2="&#x4f0;" k="42" />
    <hkern u1="&#x499;" u2="&#x4ee;" k="42" />
    <hkern u1="&#x499;" u2="&#x4df;" k="10" />
    <hkern u1="&#x499;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x499;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x499;" u2="&#x4b9;" k="32" />
    <hkern u1="&#x499;" u2="&#x4b7;" k="32" />
    <hkern u1="&#x499;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x499;" u2="&#x4ae;" k="42" />
    <hkern u1="&#x499;" u2="&#x499;" k="10" />
    <hkern u1="&#x499;" u2="&#x496;" k="21" />
    <hkern u1="&#x499;" u2="&#x44d;" k="10" />
    <hkern u1="&#x499;" u2="&#x447;" k="32" />
    <hkern u1="&#x499;" u2="&#x445;" k="21" />
    <hkern u1="&#x499;" u2="&#x437;" k="10" />
    <hkern u1="&#x499;" u2="&#x423;" k="42" />
    <hkern u1="&#x499;" u2="&#x416;" k="21" />
    <hkern u1="&#x499;" u2="&#x40e;" k="42" />
    <hkern u1="&#x49a;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x49a;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x49a;" g2="afii10085.var.accented" k="21" />
    <hkern u1="&#x49a;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x49a;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x49a;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x49a;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x49a;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x49a;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x49a;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x49a;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4f3;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4f1;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4ef;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4de;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x49a;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x49a;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x49a;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4b2;" k="-21" />
    <hkern u1="&#x49a;" u2="&#x4af;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x49a;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x49a;" u2="&#x49d;" k="10" />
    <hkern u1="&#x49a;" u2="&#x49b;" k="10" />
    <hkern u1="&#x49a;" u2="&#x498;" k="21" />
    <hkern u1="&#x49a;" u2="&#x493;" k="10" />
    <hkern u1="&#x49a;" u2="&#x491;" k="10" />
    <hkern u1="&#x49a;" u2="&#x472;" k="21" />
    <hkern u1="&#x49a;" u2="&#x45e;" k="21" />
    <hkern u1="&#x49a;" u2="&#x45c;" k="10" />
    <hkern u1="&#x49a;" u2="&#x454;" k="10" />
    <hkern u1="&#x49a;" u2="&#x453;" k="10" />
    <hkern u1="&#x49a;" u2="&#x451;" k="10" />
    <hkern u1="&#x49a;" u2="&#x44f;" k="10" />
    <hkern u1="&#x49a;" u2="&#x44a;" k="31" />
    <hkern u1="&#x49a;" u2="&#x447;" k="21" />
    <hkern u1="&#x49a;" u2="&#x444;" k="21" />
    <hkern u1="&#x49a;" u2="&#x443;" k="21" />
    <hkern u1="&#x49a;" u2="&#x442;" k="31" />
    <hkern u1="&#x49a;" u2="&#x441;" k="21" />
    <hkern u1="&#x49a;" u2="&#x43e;" k="10" />
    <hkern u1="&#x49a;" u2="&#x43a;" k="10" />
    <hkern u1="&#x49a;" u2="&#x435;" k="10" />
    <hkern u1="&#x49a;" u2="&#x433;" k="10" />
    <hkern u1="&#x49a;" u2="&#x42f;" k="21" />
    <hkern u1="&#x49a;" u2="&#x42d;" k="10" />
    <hkern u1="&#x49a;" u2="&#x427;" k="21" />
    <hkern u1="&#x49a;" u2="&#x425;" k="-21" />
    <hkern u1="&#x49a;" u2="&#x424;" k="12" />
    <hkern u1="&#x49a;" u2="&#x421;" k="10" />
    <hkern u1="&#x49a;" u2="&#x41e;" k="21" />
    <hkern u1="&#x49a;" u2="&#x417;" k="21" />
    <hkern u1="&#x49a;" u2="&#x414;" k="-20" />
    <hkern u1="&#x49a;" u2="&#x410;" k="-10" />
    <hkern u1="&#x49a;" u2="&#x40b;" k="10" />
    <hkern u1="&#x49a;" u2="&#x404;" k="21" />
    <hkern u1="&#x49a;" u2="&#x402;" k="10" />
    <hkern u1="&#x49b;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x49b;" u2="&#xf6bc;" k="-42" />
    <hkern u1="&#x49b;" u2="&#xf6bb;" k="-42" />
    <hkern u1="&#x49b;" u2="&#xf6ba;" k="-42" />
    <hkern u1="&#x49b;" g2="afii10085.var.accented" k="-11" />
    <hkern u1="&#x49b;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x49b;" u2="&#x2014;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x2013;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x2012;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x2011;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x2010;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x4f3;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x49b;" u2="&#x4f1;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x49b;" u2="&#x4ef;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x49b;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x49b;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x49b;" u2="&#x4af;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x49b;" u2="&#x497;" k="-20" />
    <hkern u1="&#x49b;" u2="&#x45e;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x459;" k="-10" />
    <hkern u1="&#x49b;" u2="&#x443;" k="-11" />
    <hkern u1="&#x49b;" u2="&#x442;" k="-10" />
    <hkern u1="&#x49b;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x49b;" u2="&#x436;" k="-20" />
    <hkern u1="&#x49b;" u2="&#x434;" k="-10" />
    <hkern u1="&#x49b;" u2="&#x423;" k="31" />
    <hkern u1="&#x49b;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x49b;" u2="&#x414;" k="-21" />
    <hkern u1="&#x49b;" u2="&#x40e;" k="31" />
    <hkern u1="&#x49b;" u2="&#x409;" k="-21" />
    <hkern u1="&#x49b;" u2="&#xad;" k="-42" />
    <hkern u1="&#x49b;" u2="&#x2d;" k="-42" />
    <hkern u1="&#x49c;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x49c;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x49c;" g2="afii10085.var.accented" k="21" />
    <hkern u1="&#x49c;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x49c;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x49c;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x49c;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x49c;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x49c;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x49c;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x49c;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4f3;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4f1;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4ef;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4de;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x49c;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x49c;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x49c;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4b2;" k="-21" />
    <hkern u1="&#x49c;" u2="&#x4af;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x49c;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x49c;" u2="&#x49d;" k="10" />
    <hkern u1="&#x49c;" u2="&#x49b;" k="10" />
    <hkern u1="&#x49c;" u2="&#x498;" k="21" />
    <hkern u1="&#x49c;" u2="&#x493;" k="10" />
    <hkern u1="&#x49c;" u2="&#x491;" k="10" />
    <hkern u1="&#x49c;" u2="&#x472;" k="21" />
    <hkern u1="&#x49c;" u2="&#x45e;" k="21" />
    <hkern u1="&#x49c;" u2="&#x45c;" k="10" />
    <hkern u1="&#x49c;" u2="&#x454;" k="10" />
    <hkern u1="&#x49c;" u2="&#x453;" k="10" />
    <hkern u1="&#x49c;" u2="&#x451;" k="10" />
    <hkern u1="&#x49c;" u2="&#x44f;" k="10" />
    <hkern u1="&#x49c;" u2="&#x44a;" k="31" />
    <hkern u1="&#x49c;" u2="&#x447;" k="21" />
    <hkern u1="&#x49c;" u2="&#x444;" k="21" />
    <hkern u1="&#x49c;" u2="&#x443;" k="21" />
    <hkern u1="&#x49c;" u2="&#x442;" k="31" />
    <hkern u1="&#x49c;" u2="&#x441;" k="21" />
    <hkern u1="&#x49c;" u2="&#x43e;" k="10" />
    <hkern u1="&#x49c;" u2="&#x43a;" k="10" />
    <hkern u1="&#x49c;" u2="&#x435;" k="10" />
    <hkern u1="&#x49c;" u2="&#x433;" k="10" />
    <hkern u1="&#x49c;" u2="&#x42f;" k="21" />
    <hkern u1="&#x49c;" u2="&#x42d;" k="10" />
    <hkern u1="&#x49c;" u2="&#x427;" k="21" />
    <hkern u1="&#x49c;" u2="&#x425;" k="-21" />
    <hkern u1="&#x49c;" u2="&#x424;" k="12" />
    <hkern u1="&#x49c;" u2="&#x421;" k="10" />
    <hkern u1="&#x49c;" u2="&#x41e;" k="21" />
    <hkern u1="&#x49c;" u2="&#x417;" k="21" />
    <hkern u1="&#x49c;" u2="&#x414;" k="-20" />
    <hkern u1="&#x49c;" u2="&#x410;" k="-10" />
    <hkern u1="&#x49c;" u2="&#x40b;" k="10" />
    <hkern u1="&#x49c;" u2="&#x404;" k="21" />
    <hkern u1="&#x49c;" u2="&#x402;" k="10" />
    <hkern u1="&#x49d;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x49d;" u2="&#xf6bc;" k="-42" />
    <hkern u1="&#x49d;" u2="&#xf6bb;" k="-42" />
    <hkern u1="&#x49d;" u2="&#xf6ba;" k="-42" />
    <hkern u1="&#x49d;" g2="afii10085.var.accented" k="-11" />
    <hkern u1="&#x49d;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x49d;" u2="&#x2014;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x2013;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x2012;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x2011;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x2010;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x4f3;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x49d;" u2="&#x4f1;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x49d;" u2="&#x4ef;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x49d;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x49d;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x49d;" u2="&#x4af;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x49d;" u2="&#x497;" k="-20" />
    <hkern u1="&#x49d;" u2="&#x45e;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x459;" k="-10" />
    <hkern u1="&#x49d;" u2="&#x443;" k="-11" />
    <hkern u1="&#x49d;" u2="&#x442;" k="-10" />
    <hkern u1="&#x49d;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x49d;" u2="&#x436;" k="-20" />
    <hkern u1="&#x49d;" u2="&#x434;" k="-10" />
    <hkern u1="&#x49d;" u2="&#x423;" k="31" />
    <hkern u1="&#x49d;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x49d;" u2="&#x414;" k="-21" />
    <hkern u1="&#x49d;" u2="&#x40e;" k="31" />
    <hkern u1="&#x49d;" u2="&#x409;" k="-21" />
    <hkern u1="&#x49d;" u2="&#xad;" k="-42" />
    <hkern u1="&#x49d;" u2="&#x2d;" k="-42" />
    <hkern u1="&#x4a2;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x4a3;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x4a3;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x4a3;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x4a3;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x4a3;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x4a3;" u2="&#x423;" k="31" />
    <hkern u1="&#x4a3;" u2="&#x40e;" k="31" />
    <hkern u1="&#x4aa;" g2="afii10097.var.accented" k="21" />
    <hkern u1="&#x4aa;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x4aa;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4aa;" u2="&#x201c;" k="-41" />
    <hkern u1="&#x4aa;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x4aa;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x4aa;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x473;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x472;" k="10" />
    <hkern u1="&#x4aa;" u2="&#x454;" k="10" />
    <hkern u1="&#x4aa;" u2="&#x44f;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4aa;" u2="&#x447;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x442;" k="31" />
    <hkern u1="&#x4aa;" u2="&#x431;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x42f;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x42a;" k="-21" />
    <hkern u1="&#x4aa;" u2="&#x427;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x424;" k="21" />
    <hkern u1="&#x4aa;" u2="&#x421;" k="41" />
    <hkern u1="&#x4aa;" u2="&#x404;" k="31" />
    <hkern u1="&#x4ab;" u2="&#x459;" k="-10" />
    <hkern u1="&#x4ab;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x4ab;" u2="&#x442;" k="-11" />
    <hkern u1="&#x4ab;" u2="&#x434;" k="-10" />
    <hkern u1="&#x4ab;" u2="&#x414;" k="-11" />
    <hkern u1="&#x4ae;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x4ae;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x4ae;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x4ae;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x4ae;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x4ae;" g2="afii10017.var.accented" k="36" />
    <hkern u1="&#x4ae;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4ae;" u2="&#x2026;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x4ae;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x4ae;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4d2;" k="36" />
    <hkern u1="&#x4ae;" u2="&#x4d0;" k="36" />
    <hkern u1="&#x4ae;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x49d;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x49b;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x497;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x473;" k="62" />
    <hkern u1="&#x4ae;" u2="&#x472;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x458;" k="1" />
    <hkern u1="&#x4ae;" u2="&#x457;" k="1" />
    <hkern u1="&#x4ae;" u2="&#x456;" k="11" />
    <hkern u1="&#x4ae;" u2="&#x455;" k="70" />
    <hkern u1="&#x4ae;" u2="&#x451;" k="42" />
    <hkern u1="&#x4ae;" u2="&#x44f;" k="62" />
    <hkern u1="&#x4ae;" u2="&#x44d;" k="62" />
    <hkern u1="&#x4ae;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x447;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x445;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x444;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x442;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x43c;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x436;" k="50" />
    <hkern u1="&#x4ae;" u2="&#x434;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x431;" k="51" />
    <hkern u1="&#x4ae;" u2="&#x42f;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x42d;" k="20" />
    <hkern u1="&#x4ae;" u2="&#x424;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x421;" k="52" />
    <hkern u1="&#x4ae;" u2="&#x41c;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x414;" k="28" />
    <hkern u1="&#x4ae;" u2="&#x410;" k="36" />
    <hkern u1="&#x4ae;" u2="&#x409;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x404;" k="41" />
    <hkern u1="&#x4ae;" u2="&#x3a;" k="31" />
    <hkern u1="&#x4ae;" u2="&#x2e;" k="83" />
    <hkern u1="&#x4af;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4af;" u2="&#xf6bc;" k="-33" />
    <hkern u1="&#x4af;" u2="&#xf6bb;" k="-33" />
    <hkern u1="&#x4af;" u2="&#xf6ba;" k="-33" />
    <hkern u1="&#x4af;" g2="afii10085.var.accented" k="-20" />
    <hkern u1="&#x4af;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x4af;" u2="&#x2014;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x2013;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x2012;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x2011;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x2010;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x4f3;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x4f1;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x4ef;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x4af;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x45e;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x4af;" u2="&#x445;" k="-10" />
    <hkern u1="&#x4af;" u2="&#x443;" k="-20" />
    <hkern u1="&#x4af;" u2="&#x442;" k="-21" />
    <hkern u1="&#x4af;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4af;" u2="&#xad;" k="-33" />
    <hkern u1="&#x4af;" u2="&#x2d;" k="-33" />
    <hkern u1="&#x4b2;" g2="hyphenminus" k="-52" />
    <hkern u1="&#x4b2;" u2="&#xf6bc;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#xf6bb;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#xf6ba;" k="-52" />
    <hkern u1="&#x4b2;" g2="afii10095.var.accented" k="21" />
    <hkern u1="&#x4b2;" g2="afii10049.var.accented" k="10" />
    <hkern u1="&#x4b2;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x4b2;" g2="afii10017.var.accented" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x2122;" k="-73" />
    <hkern u1="&#x4b2;" u2="&#x2014;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x2013;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x2012;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x2011;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x2010;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x4f5;" k="42" />
    <hkern u1="&#x4b2;" u2="&#x4f4;" k="31" />
    <hkern u1="&#x4b2;" u2="&#x4df;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x4de;" k="10" />
    <hkern u1="&#x4b2;" u2="&#x4dd;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x4d2;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x4d0;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x4c2;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x4b9;" k="42" />
    <hkern u1="&#x4b2;" u2="&#x4b8;" k="31" />
    <hkern u1="&#x4b2;" u2="&#x4b7;" k="42" />
    <hkern u1="&#x4b2;" u2="&#x4b6;" k="31" />
    <hkern u1="&#x4b2;" u2="&#x4b2;" k="-10" />
    <hkern u1="&#x4b2;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x499;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x498;" k="10" />
    <hkern u1="&#x4b2;" u2="&#x497;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x459;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x44d;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x44a;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x447;" k="42" />
    <hkern u1="&#x4b2;" u2="&#x442;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x4b2;" u2="&#x437;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x436;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x434;" k="-10" />
    <hkern u1="&#x4b2;" u2="&#x42f;" k="10" />
    <hkern u1="&#x4b2;" u2="&#x42d;" k="10" />
    <hkern u1="&#x4b2;" u2="&#x427;" k="31" />
    <hkern u1="&#x4b2;" u2="&#x425;" k="-10" />
    <hkern u1="&#x4b2;" u2="&#x424;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x422;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x421;" k="21" />
    <hkern u1="&#x4b2;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x417;" k="10" />
    <hkern u1="&#x4b2;" u2="&#x414;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x410;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x40b;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x409;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#x404;" k="20" />
    <hkern u1="&#x4b2;" u2="&#x402;" k="-21" />
    <hkern u1="&#x4b2;" u2="&#xad;" k="-52" />
    <hkern u1="&#x4b2;" u2="&#x2d;" k="-52" />
    <hkern u1="&#x4b3;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x4b3;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x4b3;" g2="afii10017.var.accented" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x4b3;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x4df;" k="10" />
    <hkern u1="&#x4b3;" u2="&#x4d2;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x4d0;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x4b3;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x4b3;" u2="&#x4b3;" k="-32" />
    <hkern u1="&#x4b3;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x499;" k="10" />
    <hkern u1="&#x4b3;" u2="&#x459;" k="-10" />
    <hkern u1="&#x4b3;" u2="&#x44d;" k="10" />
    <hkern u1="&#x4b3;" u2="&#x447;" k="21" />
    <hkern u1="&#x4b3;" u2="&#x445;" k="-32" />
    <hkern u1="&#x4b3;" u2="&#x43b;" k="-21" />
    <hkern u1="&#x4b3;" u2="&#x437;" k="10" />
    <hkern u1="&#x4b3;" u2="&#x434;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x423;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x422;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x41b;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x410;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x40e;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x40b;" k="32" />
    <hkern u1="&#x4b3;" u2="&#x409;" k="-20" />
    <hkern u1="&#x4b3;" u2="&#x402;" k="32" />
    <hkern u1="&#x4b6;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4b7;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x4b7;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x4b7;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x4b7;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x4b7;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x4b7;" u2="&#x423;" k="32" />
    <hkern u1="&#x4b7;" u2="&#x40e;" k="32" />
    <hkern u1="&#x4b8;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4b9;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x4b9;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x4b9;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x4b9;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x4b9;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x4b9;" u2="&#x423;" k="32" />
    <hkern u1="&#x4b9;" u2="&#x40e;" k="32" />
    <hkern u1="&#x4ba;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4ba;" u2="&#x4f4;" k="42" />
    <hkern u1="&#x4ba;" u2="&#x4b8;" k="42" />
    <hkern u1="&#x4ba;" u2="&#x4b6;" k="42" />
    <hkern u1="&#x4ba;" u2="&#x4ae;" k="61" />
    <hkern u1="&#x4ba;" u2="&#x4aa;" k="11" />
    <hkern u1="&#x4ba;" u2="&#x427;" k="42" />
    <hkern u1="&#x4ba;" u2="&#x421;" k="11" />
    <hkern u1="&#x4ba;" u2="&#x40b;" k="31" />
    <hkern u1="&#x4ba;" u2="&#x402;" k="31" />
    <hkern u1="&#x4c1;" g2="afii10049.var.accented" k="11" />
    <hkern u1="&#x4c1;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4c1;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x44a;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x447;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x444;" k="11" />
    <hkern u1="&#x4c1;" u2="&#x442;" k="32" />
    <hkern u1="&#x4c1;" u2="&#x441;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x42f;" k="11" />
    <hkern u1="&#x4c1;" u2="&#x427;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x424;" k="12" />
    <hkern u1="&#x4c1;" u2="&#x421;" k="21" />
    <hkern u1="&#x4c1;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x459;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x442;" k="-10" />
    <hkern u1="&#x4c2;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4c2;" u2="&#x434;" k="-10" />
    <hkern u1="&#x4c2;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4c3;" g2="afii10071.var.accented" k="10" />
    <hkern u1="&#x4c3;" g2="afii10097.var.accented" k="10" />
    <hkern u1="&#x4c3;" g2="afii10085.var.accented" k="21" />
    <hkern u1="&#x4c3;" g2="afii10080.var.accented" k="10" />
    <hkern u1="&#x4c3;" g2="afii10070.var.accented" k="10" />
    <hkern u1="&#x4c3;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x4c3;" g2="afii10047.var.accented" k="10" />
    <hkern u1="&#x4c3;" g2="afii10032.var.accented" k="21" />
    <hkern u1="&#x4c3;" g2="afii10017.var.accented" k="-10" />
    <hkern u1="&#x4c3;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x4c3;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4f3;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4f1;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4ef;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4eb;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4ea;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4e9;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4e8;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4e7;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4e6;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4de;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4d9;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4d8;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4d7;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4d2;" k="-10" />
    <hkern u1="&#x4c3;" u2="&#x4d0;" k="-10" />
    <hkern u1="&#x4c3;" u2="&#x4c4;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4b2;" k="-21" />
    <hkern u1="&#x4c3;" u2="&#x4af;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x4aa;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x49d;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x49b;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x498;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x493;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x491;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x472;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x45e;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x45c;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x454;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x453;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x451;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x44f;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4c3;" u2="&#x447;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x444;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x443;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x442;" k="31" />
    <hkern u1="&#x4c3;" u2="&#x441;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x43e;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x43a;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x435;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x433;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x42f;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x42d;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x427;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x425;" k="-21" />
    <hkern u1="&#x4c3;" u2="&#x424;" k="12" />
    <hkern u1="&#x4c3;" u2="&#x421;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x41e;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x417;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4c3;" u2="&#x410;" k="-10" />
    <hkern u1="&#x4c3;" u2="&#x40b;" k="10" />
    <hkern u1="&#x4c3;" u2="&#x404;" k="21" />
    <hkern u1="&#x4c3;" u2="&#x402;" k="10" />
    <hkern u1="&#x4c4;" g2="hyphenminus" k="-42" />
    <hkern u1="&#x4c4;" u2="&#xf6bc;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#xf6bb;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#xf6ba;" k="-42" />
    <hkern u1="&#x4c4;" g2="afii10085.var.accented" k="-11" />
    <hkern u1="&#x4c4;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x4c4;" u2="&#x2014;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x2013;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x2012;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x2011;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x2010;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x4f3;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x4f1;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x4ef;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4c4;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4c4;" u2="&#x4af;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4c4;" u2="&#x45e;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x459;" k="-10" />
    <hkern u1="&#x4c4;" u2="&#x443;" k="-11" />
    <hkern u1="&#x4c4;" u2="&#x442;" k="-10" />
    <hkern u1="&#x4c4;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x4c4;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4c4;" u2="&#x434;" k="-10" />
    <hkern u1="&#x4c4;" u2="&#x423;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x41b;" k="-21" />
    <hkern u1="&#x4c4;" u2="&#x414;" k="-21" />
    <hkern u1="&#x4c4;" u2="&#x40e;" k="31" />
    <hkern u1="&#x4c4;" u2="&#x409;" k="-21" />
    <hkern u1="&#x4c4;" u2="&#xad;" k="-42" />
    <hkern u1="&#x4c4;" u2="&#x2d;" k="-42" />
    <hkern u1="&#x4c7;" u2="&#x2122;" k="-63" />
    <hkern u1="&#x4c8;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x4c8;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x4c8;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x4c8;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x4c8;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x4c8;" u2="&#x423;" k="31" />
    <hkern u1="&#x4c8;" u2="&#x40e;" k="31" />
    <hkern u1="&#x4d0;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4d0;" g2="afii10049.var.accented" k="10" />
    <hkern u1="&#x4d0;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x4d0;" u2="&#x2026;" k="-21" />
    <hkern u1="&#x4d0;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x4f4;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x4b8;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x4b6;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x4b3;" k="-11" />
    <hkern u1="&#x4d0;" u2="&#x4b2;" k="-10" />
    <hkern u1="&#x4d0;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x4d0;" u2="&#x4ab;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x4aa;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x459;" k="-20" />
    <hkern u1="&#x4d0;" u2="&#x447;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x445;" k="-11" />
    <hkern u1="&#x4d0;" u2="&#x442;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x441;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x434;" k="-21" />
    <hkern u1="&#x4d0;" u2="&#x42f;" k="10" />
    <hkern u1="&#x4d0;" u2="&#x427;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x425;" k="-10" />
    <hkern u1="&#x4d0;" u2="&#x424;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x421;" k="20" />
    <hkern u1="&#x4d0;" u2="&#x414;" k="-15" />
    <hkern u1="&#x4d0;" u2="&#x40b;" k="31" />
    <hkern u1="&#x4d0;" u2="&#x409;" k="-32" />
    <hkern u1="&#x4d0;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x4d0;" u2="&#x2a;" k="32" />
    <hkern u1="&#x4d1;" u2="&#x201c;" k="-32" />
    <hkern u1="&#x4d1;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x4d2;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4d2;" g2="afii10049.var.accented" k="10" />
    <hkern u1="&#x4d2;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x4d2;" u2="&#x2026;" k="-21" />
    <hkern u1="&#x4d2;" u2="&#x4f5;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x4f4;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x4b9;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x4b8;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x4b7;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x4b6;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x4b3;" k="-11" />
    <hkern u1="&#x4d2;" u2="&#x4b2;" k="-10" />
    <hkern u1="&#x4d2;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x4d2;" u2="&#x4ab;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x4aa;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x459;" k="-20" />
    <hkern u1="&#x4d2;" u2="&#x447;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x445;" k="-11" />
    <hkern u1="&#x4d2;" u2="&#x442;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x441;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x434;" k="-21" />
    <hkern u1="&#x4d2;" u2="&#x42f;" k="10" />
    <hkern u1="&#x4d2;" u2="&#x427;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x425;" k="-10" />
    <hkern u1="&#x4d2;" u2="&#x424;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x421;" k="20" />
    <hkern u1="&#x4d2;" u2="&#x414;" k="-15" />
    <hkern u1="&#x4d2;" u2="&#x40b;" k="31" />
    <hkern u1="&#x4d2;" u2="&#x409;" k="-32" />
    <hkern u1="&#x4d2;" u2="&#x2e;" k="-42" />
    <hkern u1="&#x4d2;" u2="&#x2a;" k="32" />
    <hkern u1="&#x4d3;" u2="&#x201c;" k="-32" />
    <hkern u1="&#x4d3;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x4d6;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4d6;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x4d6;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x4d6;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x4d6;" u2="&#x447;" k="21" />
    <hkern u1="&#x4d8;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4d8;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x4d8;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x4d8;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x4d8;" u2="&#x425;" k="12" />
    <hkern u1="&#x4d8;" u2="&#x416;" k="12" />
    <hkern u1="&#x4d9;" u2="&#x4af;" k="10" />
    <hkern u1="&#x4dc;" g2="afii10049.var.accented" k="11" />
    <hkern u1="&#x4dc;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4dc;" u2="&#x4f5;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4f4;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4b9;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4b8;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4b7;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4b6;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4ab;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x4aa;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x44a;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x447;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x444;" k="11" />
    <hkern u1="&#x4dc;" u2="&#x442;" k="32" />
    <hkern u1="&#x4dc;" u2="&#x441;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x42f;" k="11" />
    <hkern u1="&#x4dc;" u2="&#x427;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x424;" k="12" />
    <hkern u1="&#x4dc;" u2="&#x421;" k="21" />
    <hkern u1="&#x4dc;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x459;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x442;" k="-10" />
    <hkern u1="&#x4dd;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4dd;" u2="&#x434;" k="-10" />
    <hkern u1="&#x4dd;" u2="&#x414;" k="-20" />
    <hkern u1="&#x4de;" g2="afii10049.var.accented" k="21" />
    <hkern u1="&#x4de;" g2="afii10047.var.accented" k="11" />
    <hkern u1="&#x4de;" g2="afii10037.var.accented" k="31" />
    <hkern u1="&#x4de;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4de;" u2="&#x201d;" k="-41" />
    <hkern u1="&#x4de;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x4de;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x4de;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x4de;" u2="&#x4f2;" k="31" />
    <hkern u1="&#x4de;" u2="&#x4f0;" k="31" />
    <hkern u1="&#x4de;" u2="&#x4ee;" k="31" />
    <hkern u1="&#x4de;" u2="&#x4de;" k="21" />
    <hkern u1="&#x4de;" u2="&#x4b2;" k="11" />
    <hkern u1="&#x4de;" u2="&#x4ae;" k="31" />
    <hkern u1="&#x4de;" u2="&#x498;" k="21" />
    <hkern u1="&#x4de;" u2="&#x42f;" k="21" />
    <hkern u1="&#x4de;" u2="&#x42d;" k="11" />
    <hkern u1="&#x4de;" u2="&#x425;" k="11" />
    <hkern u1="&#x4de;" u2="&#x423;" k="31" />
    <hkern u1="&#x4de;" u2="&#x417;" k="21" />
    <hkern u1="&#x4de;" u2="&#x40e;" k="31" />
    <hkern u1="&#x4de;" u2="&#x402;" k="21" />
    <hkern u1="&#x4df;" g2="afii10095.var.accented" k="10" />
    <hkern u1="&#x4df;" g2="afii10037.var.accented" k="42" />
    <hkern u1="&#x4df;" u2="&#x4f5;" k="32" />
    <hkern u1="&#x4df;" u2="&#x4f2;" k="42" />
    <hkern u1="&#x4df;" u2="&#x4f0;" k="42" />
    <hkern u1="&#x4df;" u2="&#x4ee;" k="42" />
    <hkern u1="&#x4df;" u2="&#x4df;" k="10" />
    <hkern u1="&#x4df;" u2="&#x4dc;" k="21" />
    <hkern u1="&#x4df;" u2="&#x4c1;" k="21" />
    <hkern u1="&#x4df;" u2="&#x4b9;" k="32" />
    <hkern u1="&#x4df;" u2="&#x4b7;" k="32" />
    <hkern u1="&#x4df;" u2="&#x4b3;" k="21" />
    <hkern u1="&#x4df;" u2="&#x4ae;" k="42" />
    <hkern u1="&#x4df;" u2="&#x499;" k="10" />
    <hkern u1="&#x4df;" u2="&#x496;" k="21" />
    <hkern u1="&#x4df;" u2="&#x44d;" k="10" />
    <hkern u1="&#x4df;" u2="&#x447;" k="32" />
    <hkern u1="&#x4df;" u2="&#x445;" k="21" />
    <hkern u1="&#x4df;" u2="&#x437;" k="10" />
    <hkern u1="&#x4df;" u2="&#x423;" k="42" />
    <hkern u1="&#x4df;" u2="&#x416;" k="21" />
    <hkern u1="&#x4df;" u2="&#x40e;" k="42" />
    <hkern u1="&#x4e2;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x4e4;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x4e6;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4e6;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x4e6;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x4e6;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x4e6;" u2="&#x425;" k="12" />
    <hkern u1="&#x4e6;" u2="&#x416;" k="12" />
    <hkern u1="&#x4e7;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x4e7;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x4e7;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x4e7;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x4e7;" u2="&#x4af;" k="10" />
    <hkern u1="&#x4e7;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x4e7;" u2="&#x423;" k="52" />
    <hkern u1="&#x4e7;" u2="&#x40e;" k="52" />
    <hkern u1="&#x4e8;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4e8;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x4e8;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x4e8;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x4e8;" u2="&#x425;" k="12" />
    <hkern u1="&#x4e8;" u2="&#x416;" k="12" />
    <hkern u1="&#x4e9;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x4e9;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x4e9;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x4e9;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x4e9;" u2="&#x4af;" k="10" />
    <hkern u1="&#x4e9;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x4e9;" u2="&#x423;" k="52" />
    <hkern u1="&#x4e9;" u2="&#x40e;" k="52" />
    <hkern u1="&#x4ea;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4ea;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x4ea;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x4ea;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x4ea;" u2="&#x425;" k="12" />
    <hkern u1="&#x4ea;" u2="&#x416;" k="12" />
    <hkern u1="&#x4eb;" g2="afii10037.var.accented" k="52" />
    <hkern u1="&#x4eb;" u2="&#x4f2;" k="52" />
    <hkern u1="&#x4eb;" u2="&#x4f0;" k="52" />
    <hkern u1="&#x4eb;" u2="&#x4ee;" k="52" />
    <hkern u1="&#x4eb;" u2="&#x4af;" k="10" />
    <hkern u1="&#x4eb;" u2="&#x4ae;" k="52" />
    <hkern u1="&#x4eb;" u2="&#x423;" k="52" />
    <hkern u1="&#x4eb;" u2="&#x40e;" k="52" />
    <hkern u1="&#x4ee;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x4ee;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x4ee;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x4ee;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x4ee;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x4ee;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4ee;" u2="&#x2026;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x4ee;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x4ee;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x49d;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x49b;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x497;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x473;" k="62" />
    <hkern u1="&#x4ee;" u2="&#x472;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x458;" k="1" />
    <hkern u1="&#x4ee;" u2="&#x457;" k="1" />
    <hkern u1="&#x4ee;" u2="&#x456;" k="11" />
    <hkern u1="&#x4ee;" u2="&#x455;" k="70" />
    <hkern u1="&#x4ee;" u2="&#x451;" k="42" />
    <hkern u1="&#x4ee;" u2="&#x44f;" k="62" />
    <hkern u1="&#x4ee;" u2="&#x44d;" k="62" />
    <hkern u1="&#x4ee;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x447;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x445;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x444;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x442;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x43c;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x436;" k="50" />
    <hkern u1="&#x4ee;" u2="&#x434;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x431;" k="51" />
    <hkern u1="&#x4ee;" u2="&#x42f;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x42d;" k="20" />
    <hkern u1="&#x4ee;" u2="&#x424;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x421;" k="52" />
    <hkern u1="&#x4ee;" u2="&#x41c;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x414;" k="28" />
    <hkern u1="&#x4ee;" u2="&#x409;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x404;" k="41" />
    <hkern u1="&#x4ee;" u2="&#x3a;" k="31" />
    <hkern u1="&#x4ee;" u2="&#x2e;" k="83" />
    <hkern u1="&#x4ef;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4ef;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x4ef;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4ef;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4ef;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x4ef;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x4ef;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4ef;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x4ef;" u2="&#x445;" k="-10" />
    <hkern u1="&#x4ef;" u2="&#x442;" k="-21" />
    <hkern u1="&#x4ef;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4f0;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x4f0;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x4f0;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x4f0;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x4f0;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x4f0;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4f0;" u2="&#x2026;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x4f0;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x4f0;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x49d;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x49b;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x497;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x473;" k="62" />
    <hkern u1="&#x4f0;" u2="&#x472;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x458;" k="1" />
    <hkern u1="&#x4f0;" u2="&#x457;" k="1" />
    <hkern u1="&#x4f0;" u2="&#x456;" k="11" />
    <hkern u1="&#x4f0;" u2="&#x455;" k="70" />
    <hkern u1="&#x4f0;" u2="&#x451;" k="42" />
    <hkern u1="&#x4f0;" u2="&#x44f;" k="62" />
    <hkern u1="&#x4f0;" u2="&#x44d;" k="62" />
    <hkern u1="&#x4f0;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x447;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x445;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x444;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x442;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x43c;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x436;" k="50" />
    <hkern u1="&#x4f0;" u2="&#x434;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x431;" k="51" />
    <hkern u1="&#x4f0;" u2="&#x42f;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x42d;" k="20" />
    <hkern u1="&#x4f0;" u2="&#x424;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x421;" k="52" />
    <hkern u1="&#x4f0;" u2="&#x41c;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x414;" k="28" />
    <hkern u1="&#x4f0;" u2="&#x409;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x404;" k="41" />
    <hkern u1="&#x4f0;" u2="&#x3a;" k="31" />
    <hkern u1="&#x4f0;" u2="&#x2e;" k="83" />
    <hkern u1="&#x4f1;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4f1;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x4f1;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4f1;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4f1;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x4f1;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x4f1;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4f1;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x4f1;" u2="&#x445;" k="-10" />
    <hkern u1="&#x4f1;" u2="&#x442;" k="-21" />
    <hkern u1="&#x4f1;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4f2;" g2="afii10071.var.accented" k="32" />
    <hkern u1="&#x4f2;" g2="afii10097.var.accented" k="62" />
    <hkern u1="&#x4f2;" g2="afii10095.var.accented" k="52" />
    <hkern u1="&#x4f2;" g2="afii10049.var.accented" k="31" />
    <hkern u1="&#x4f2;" g2="afii10047.var.accented" k="20" />
    <hkern u1="&#x4f2;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x4f2;" u2="&#x2026;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x201c;" k="-52" />
    <hkern u1="&#x4f2;" u2="&#x2018;" k="-22" />
    <hkern u1="&#x4f2;" u2="&#x4f5;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4e5;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x4e3;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x4dd;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4c8;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4c4;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x4c2;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4b9;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4b7;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x4b3;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x4aa;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x4a3;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x49d;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x49b;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x497;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x473;" k="62" />
    <hkern u1="&#x4f2;" u2="&#x472;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x458;" k="1" />
    <hkern u1="&#x4f2;" u2="&#x457;" k="1" />
    <hkern u1="&#x4f2;" u2="&#x456;" k="11" />
    <hkern u1="&#x4f2;" u2="&#x455;" k="70" />
    <hkern u1="&#x4f2;" u2="&#x451;" k="42" />
    <hkern u1="&#x4f2;" u2="&#x44f;" k="62" />
    <hkern u1="&#x4f2;" u2="&#x44d;" k="62" />
    <hkern u1="&#x4f2;" u2="&#x44a;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x447;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x445;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x444;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x442;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x43c;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x436;" k="50" />
    <hkern u1="&#x4f2;" u2="&#x434;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x431;" k="51" />
    <hkern u1="&#x4f2;" u2="&#x42f;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x42d;" k="20" />
    <hkern u1="&#x4f2;" u2="&#x424;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x421;" k="52" />
    <hkern u1="&#x4f2;" u2="&#x41c;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x414;" k="28" />
    <hkern u1="&#x4f2;" u2="&#x409;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x404;" k="41" />
    <hkern u1="&#x4f2;" u2="&#x3a;" k="31" />
    <hkern u1="&#x4f2;" u2="&#x2e;" k="83" />
    <hkern u1="&#x4f3;" g2="hyphenminus" k="-73" />
    <hkern u1="&#x4f3;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x4f3;" u2="&#x4dd;" k="-20" />
    <hkern u1="&#x4f3;" u2="&#x4c2;" k="-20" />
    <hkern u1="&#x4f3;" u2="&#x4b3;" k="-10" />
    <hkern u1="&#x4f3;" u2="&#x4af;" k="-20" />
    <hkern u1="&#x4f3;" u2="&#x497;" k="-20" />
    <hkern u1="&#x4f3;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x4f3;" u2="&#x445;" k="-10" />
    <hkern u1="&#x4f3;" u2="&#x442;" k="-21" />
    <hkern u1="&#x4f3;" u2="&#x436;" k="-20" />
    <hkern u1="&#x4f4;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x4f5;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x4f5;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x4f5;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x4f5;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x4f5;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x4f5;" u2="&#x423;" k="32" />
    <hkern u1="&#x4f5;" u2="&#x40e;" k="32" />
    <hkern u1="&#x4f8;" u2="&#x2122;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2010;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2010;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2010;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2010;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2010;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2010;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2010;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2010;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2010;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2010;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2010;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2010;" u2="&#x31;" k="21" />
    <hkern u1="&#x2011;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2011;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2011;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2011;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2011;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2011;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2011;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2011;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2011;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2011;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2011;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2011;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2011;" u2="&#x31;" k="21" />
    <hkern u1="&#x2012;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2012;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2012;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2012;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2012;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2012;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2012;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2012;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2012;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2012;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2012;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2012;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2012;" u2="&#x31;" k="21" />
    <hkern u1="&#x2013;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2013;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2013;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2013;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2013;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2013;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2013;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2013;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2013;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2013;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2013;" u2="&#x31;" k="21" />
    <hkern u1="&#x2014;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#x2014;" u2="&#x4af;" k="-33" />
    <hkern u1="&#x2014;" u2="&#x4ae;" k="20" />
    <hkern u1="&#x2014;" u2="&#x497;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x496;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x459;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x442;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x436;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x434;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x42a;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x425;" k="-31" />
    <hkern u1="&#x2014;" u2="&#x416;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x414;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x40b;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x409;" k="-52" />
    <hkern u1="&#x2014;" u2="&#x402;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x178;" k="-21" />
    <hkern u1="&#x2014;" u2="&#x164;" k="-41" />
    <hkern u1="&#x2014;" u2="&#x104;" k="-62" />
    <hkern u1="&#x2014;" u2="&#x102;" k="-62" />
    <hkern u1="&#x2014;" u2="&#x100;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="-21" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="-62" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="-62" />
    <hkern u1="&#x2014;" u2="&#x31;" k="21" />
    <hkern u1="&#x2018;" u2="q" k="52" />
    <hkern u1="&#x2018;" u2="J" k="31" />
    <hkern u1="&#x2019;" g2="afii10071.var.accented" k="83" />
    <hkern u1="&#x2019;" g2="afii10096.var.accented" k="63" />
    <hkern u1="&#x2019;" g2="afii10093.var.accented" k="63" />
    <hkern u1="&#x2019;" g2="afii10085.var.accented" k="42" />
    <hkern u1="&#x2019;" g2="afii10080.var.accented" k="73" />
    <hkern u1="&#x2019;" g2="afii10074.var.accented" k="63" />
    <hkern u1="&#x2019;" g2="afii10070.var.accented" k="83" />
    <hkern u1="&#x2019;" g2="afii10065.var.accented" k="94" />
    <hkern u1="&#x2019;" g2="afii10037.var.accented" k="-42" />
    <hkern u1="&#x2019;" g2="afii10017.var.accented" k="52" />
    <hkern u1="&#x2019;" u2="&#x4f9;" k="63" />
    <hkern u1="&#x2019;" u2="&#x4f3;" k="42" />
    <hkern u1="&#x2019;" u2="&#x4f2;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x4f1;" k="42" />
    <hkern u1="&#x2019;" u2="&#x4f0;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x4ef;" k="42" />
    <hkern u1="&#x2019;" u2="&#x4ee;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x4eb;" k="73" />
    <hkern u1="&#x2019;" u2="&#x4e9;" k="73" />
    <hkern u1="&#x2019;" u2="&#x4e7;" k="73" />
    <hkern u1="&#x2019;" u2="&#x4d9;" k="83" />
    <hkern u1="&#x2019;" u2="&#x4d7;" k="83" />
    <hkern u1="&#x2019;" u2="&#x4d3;" k="94" />
    <hkern u1="&#x2019;" u2="&#x4d2;" k="52" />
    <hkern u1="&#x2019;" u2="&#x4d1;" k="94" />
    <hkern u1="&#x2019;" u2="&#x4d0;" k="52" />
    <hkern u1="&#x2019;" u2="&#x4c8;" k="63" />
    <hkern u1="&#x2019;" u2="&#x4af;" k="42" />
    <hkern u1="&#x2019;" u2="&#x4ae;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x4ab;" k="104" />
    <hkern u1="&#x2019;" u2="&#x4aa;" k="42" />
    <hkern u1="&#x2019;" u2="&#x4a3;" k="63" />
    <hkern u1="&#x2019;" u2="&#x493;" k="63" />
    <hkern u1="&#x2019;" u2="&#x491;" k="63" />
    <hkern u1="&#x2019;" u2="&#x473;" k="73" />
    <hkern u1="&#x2019;" u2="&#x45f;" k="63" />
    <hkern u1="&#x2019;" u2="&#x45e;" k="42" />
    <hkern u1="&#x2019;" u2="&#x45c;" k="63" />
    <hkern u1="&#x2019;" u2="&#x45a;" k="63" />
    <hkern u1="&#x2019;" u2="&#x459;" k="52" />
    <hkern u1="&#x2019;" u2="&#x457;" k="63" />
    <hkern u1="&#x2019;" u2="&#x456;" k="63" />
    <hkern u1="&#x2019;" u2="&#x454;" k="94" />
    <hkern u1="&#x2019;" u2="&#x453;" k="63" />
    <hkern u1="&#x2019;" u2="&#x451;" k="83" />
    <hkern u1="&#x2019;" u2="&#x44e;" k="63" />
    <hkern u1="&#x2019;" u2="&#x44c;" k="63" />
    <hkern u1="&#x2019;" u2="&#x44b;" k="63" />
    <hkern u1="&#x2019;" u2="&#x449;" k="63" />
    <hkern u1="&#x2019;" u2="&#x448;" k="63" />
    <hkern u1="&#x2019;" u2="&#x446;" k="63" />
    <hkern u1="&#x2019;" u2="&#x444;" k="42" />
    <hkern u1="&#x2019;" u2="&#x443;" k="42" />
    <hkern u1="&#x2019;" u2="&#x441;" k="104" />
    <hkern u1="&#x2019;" u2="&#x43f;" k="63" />
    <hkern u1="&#x2019;" u2="&#x43e;" k="73" />
    <hkern u1="&#x2019;" u2="&#x43d;" k="63" />
    <hkern u1="&#x2019;" u2="&#x43c;" k="73" />
    <hkern u1="&#x2019;" u2="&#x43b;" k="94" />
    <hkern u1="&#x2019;" u2="&#x43a;" k="63" />
    <hkern u1="&#x2019;" u2="&#x439;" k="63" />
    <hkern u1="&#x2019;" u2="&#x438;" k="63" />
    <hkern u1="&#x2019;" u2="&#x435;" k="83" />
    <hkern u1="&#x2019;" u2="&#x434;" k="94" />
    <hkern u1="&#x2019;" u2="&#x433;" k="63" />
    <hkern u1="&#x2019;" u2="&#x432;" k="63" />
    <hkern u1="&#x2019;" u2="&#x430;" k="94" />
    <hkern u1="&#x2019;" u2="&#x424;" k="42" />
    <hkern u1="&#x2019;" u2="&#x423;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x422;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x421;" k="42" />
    <hkern u1="&#x2019;" u2="&#x41c;" k="21" />
    <hkern u1="&#x2019;" u2="&#x41b;" k="10" />
    <hkern u1="&#x2019;" u2="&#x414;" k="21" />
    <hkern u1="&#x2019;" u2="&#x410;" k="52" />
    <hkern u1="&#x2019;" u2="&#x40e;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x40b;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x409;" k="42" />
    <hkern u1="&#x2019;" u2="&#x404;" k="21" />
    <hkern u1="&#x2019;" u2="&#x402;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x259;" k="73" />
    <hkern u1="&#x2019;" u2="&#x21a;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x219;" k="42" />
    <hkern u1="&#x2019;" u2="&#x1fd;" k="73" />
    <hkern u1="&#x2019;" u2="&#x1fc;" k="21" />
    <hkern u1="&#x2019;" u2="&#x1dd;" k="73" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-42" />
    <hkern u1="&#x2019;" u2="&#x164;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x162;" k="-50" />
    <hkern u1="&#x2019;" u2="&#x161;" k="42" />
    <hkern u1="&#x2019;" u2="&#x15f;" k="42" />
    <hkern u1="&#x2019;" u2="&#x15d;" k="42" />
    <hkern u1="&#x2019;" u2="&#x15b;" k="42" />
    <hkern u1="&#x2019;" u2="&#x159;" k="31" />
    <hkern u1="&#x2019;" u2="&#x157;" k="31" />
    <hkern u1="&#x2019;" u2="&#x155;" k="31" />
    <hkern u1="&#x2019;" u2="&#x153;" k="73" />
    <hkern u1="&#x2019;" u2="&#x151;" k="73" />
    <hkern u1="&#x2019;" u2="&#x14f;" k="73" />
    <hkern u1="&#x2019;" u2="&#x14d;" k="73" />
    <hkern u1="&#x2019;" u2="&#x11b;" k="42" />
    <hkern u1="&#x2019;" u2="&#x119;" k="42" />
    <hkern u1="&#x2019;" u2="&#x117;" k="42" />
    <hkern u1="&#x2019;" u2="&#x113;" k="42" />
    <hkern u1="&#x2019;" u2="&#x111;" k="31" />
    <hkern u1="&#x2019;" u2="&#x10f;" k="31" />
    <hkern u1="&#x2019;" u2="&#x10d;" k="52" />
    <hkern u1="&#x2019;" u2="&#x10b;" k="52" />
    <hkern u1="&#x2019;" u2="&#x109;" k="52" />
    <hkern u1="&#x2019;" u2="&#x107;" k="52" />
    <hkern u1="&#x2019;" u2="&#x105;" k="73" />
    <hkern u1="&#x2019;" u2="&#x104;" k="21" />
    <hkern u1="&#x2019;" u2="&#x103;" k="73" />
    <hkern u1="&#x2019;" u2="&#x102;" k="21" />
    <hkern u1="&#x2019;" u2="&#x101;" k="73" />
    <hkern u1="&#x2019;" u2="&#x100;" k="21" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="73" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="73" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="73" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="73" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="73" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="73" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="42" />
    <hkern u1="&#x2019;" u2="&#xea;" k="42" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="42" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="42" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="52" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="73" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="73" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-42" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="21" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="21" />
    <hkern u1="&#x2019;" u2="s" k="42" />
    <hkern u1="&#x2019;" u2="r" k="31" />
    <hkern u1="&#x2019;" u2="q" k="42" />
    <hkern u1="&#x2019;" u2="o" k="73" />
    <hkern u1="&#x2019;" u2="e" k="42" />
    <hkern u1="&#x2019;" u2="d" k="31" />
    <hkern u1="&#x2019;" u2="c" k="52" />
    <hkern u1="&#x2019;" u2="a" k="73" />
    <hkern u1="&#x2019;" u2="Y" k="-42" />
    <hkern u1="&#x2019;" u2="W" k="-42" />
    <hkern u1="&#x2019;" u2="V" k="-31" />
    <hkern u1="&#x2019;" u2="T" k="-50" />
    <hkern u1="&#x2019;" u2="A" k="21" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="83" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="115" />
    <hkern u1="&#x201c;" g2="afii10017.var.accented" k="11" />
    <hkern u1="&#x201c;" u2="&#x4d2;" k="11" />
    <hkern u1="&#x201c;" u2="&#x4d0;" k="11" />
    <hkern u1="&#x201c;" u2="&#x410;" k="11" />
    <hkern u1="&#x201c;" u2="&#x21a;" k="-41" />
    <hkern u1="&#x201c;" u2="&#x1fd;" k="63" />
    <hkern u1="&#x201c;" u2="&#x1fc;" k="32" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-41" />
    <hkern u1="&#x201c;" u2="&#x164;" k="-41" />
    <hkern u1="&#x201c;" u2="&#x162;" k="-41" />
    <hkern u1="&#x201c;" u2="&#x123;" k="32" />
    <hkern u1="&#x201c;" u2="&#x121;" k="32" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="32" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="32" />
    <hkern u1="&#x201c;" u2="&#x11b;" k="21" />
    <hkern u1="&#x201c;" u2="&#x119;" k="21" />
    <hkern u1="&#x201c;" u2="&#x117;" k="21" />
    <hkern u1="&#x201c;" u2="&#x113;" k="21" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="52" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="52" />
    <hkern u1="&#x201c;" u2="&#x109;" k="52" />
    <hkern u1="&#x201c;" u2="&#x107;" k="52" />
    <hkern u1="&#x201c;" u2="&#x105;" k="63" />
    <hkern u1="&#x201c;" u2="&#x104;" k="32" />
    <hkern u1="&#x201c;" u2="&#x103;" k="63" />
    <hkern u1="&#x201c;" u2="&#x102;" k="32" />
    <hkern u1="&#x201c;" u2="&#x101;" k="63" />
    <hkern u1="&#x201c;" u2="&#x100;" k="32" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="21" />
    <hkern u1="&#x201c;" u2="&#xea;" k="21" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="21" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="21" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="52" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="63" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="63" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-41" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="32" />
    <hkern u1="&#x201c;" u2="q" k="52" />
    <hkern u1="&#x201c;" u2="g" k="32" />
    <hkern u1="&#x201c;" u2="e" k="21" />
    <hkern u1="&#x201c;" u2="c" k="52" />
    <hkern u1="&#x201c;" u2="a" k="63" />
    <hkern u1="&#x201c;" u2="Y" k="-41" />
    <hkern u1="&#x201c;" u2="W" k="-41" />
    <hkern u1="&#x201c;" u2="V" k="-41" />
    <hkern u1="&#x201c;" u2="T" k="-41" />
    <hkern u1="&#x201c;" u2="J" k="21" />
    <hkern u1="&#x201c;" u2="A" k="32" />
    <hkern u1="&#x201d;" u2="&#x21a;" k="-52" />
    <hkern u1="&#x201d;" u2="&#x1fc;" k="52" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-31" />
    <hkern u1="&#x201d;" u2="&#x164;" k="-52" />
    <hkern u1="&#x201d;" u2="&#x162;" k="-52" />
    <hkern u1="&#x201d;" u2="&#x104;" k="52" />
    <hkern u1="&#x201d;" u2="&#x102;" k="52" />
    <hkern u1="&#x201d;" u2="&#x100;" k="52" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-31" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="52" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="52" />
    <hkern u1="&#x201d;" u2="Y" k="-31" />
    <hkern u1="&#x201d;" u2="W" k="-41" />
    <hkern u1="&#x201d;" u2="V" k="-41" />
    <hkern u1="&#x201d;" u2="T" k="-52" />
    <hkern u1="&#x201d;" u2="A" k="52" />
    <hkern u1="&#x201e;" g2="afii10037.var.accented" k="32" />
    <hkern u1="&#x201e;" g2="afii10017.var.accented" k="-41" />
    <hkern u1="&#x201e;" u2="&#x4f4;" k="84" />
    <hkern u1="&#x201e;" u2="&#x4f2;" k="32" />
    <hkern u1="&#x201e;" u2="&#x4f0;" k="32" />
    <hkern u1="&#x201e;" u2="&#x4ee;" k="32" />
    <hkern u1="&#x201e;" u2="&#x4d2;" k="-41" />
    <hkern u1="&#x201e;" u2="&#x4d0;" k="-41" />
    <hkern u1="&#x201e;" u2="&#x4b8;" k="84" />
    <hkern u1="&#x201e;" u2="&#x4b6;" k="84" />
    <hkern u1="&#x201e;" u2="&#x4ae;" k="32" />
    <hkern u1="&#x201e;" u2="&#x427;" k="84" />
    <hkern u1="&#x201e;" u2="&#x423;" k="32" />
    <hkern u1="&#x201e;" u2="&#x410;" k="-41" />
    <hkern u1="&#x201e;" u2="&#x40e;" k="32" />
    <hkern u1="&#x201e;" u2="&#x40b;" k="42" />
    <hkern u1="&#x201e;" u2="&#x21a;" k="21" />
    <hkern u1="&#x201e;" u2="&#x1fc;" k="-52" />
    <hkern u1="&#x201e;" u2="&#x178;" k="52" />
    <hkern u1="&#x201e;" u2="&#x164;" k="21" />
    <hkern u1="&#x201e;" u2="&#x162;" k="21" />
    <hkern u1="&#x201e;" u2="&#x104;" k="-52" />
    <hkern u1="&#x201e;" u2="&#x102;" k="-52" />
    <hkern u1="&#x201e;" u2="&#x100;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="52" />
    <hkern u1="&#x201e;" u2="&#xc6;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc5;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc4;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc3;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc2;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc1;" k="-52" />
    <hkern u1="&#x201e;" u2="&#xc0;" k="-52" />
    <hkern u1="&#x201e;" u2="Y" k="52" />
    <hkern u1="&#x201e;" u2="V" k="63" />
    <hkern u1="&#x201e;" u2="T" k="21" />
    <hkern u1="&#x201e;" u2="A" k="-52" />
    <hkern u1="&#x203a;" u2="&#x178;" k="32" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="32" />
    <hkern u1="&#x203a;" u2="Y" k="32" />
    <hkern g1="afii10017.var.accented" g2="hyphenminus" k="-73" />
    <hkern g1="afii10017.var.accented" g2="afii10049.var.accented" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x2122;" k="-31" />
    <hkern g1="afii10017.var.accented" u2="&#x2026;" k="-21" />
    <hkern g1="afii10017.var.accented" u2="&#x4f5;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x4f4;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x4b9;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x4b8;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x4b7;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x4b6;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x4b3;" k="-11" />
    <hkern g1="afii10017.var.accented" u2="&#x4b2;" k="-10" />
    <hkern g1="afii10017.var.accented" u2="&#x4ae;" k="52" />
    <hkern g1="afii10017.var.accented" u2="&#x4ab;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x4aa;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x459;" k="-20" />
    <hkern g1="afii10017.var.accented" u2="&#x447;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x445;" k="-11" />
    <hkern g1="afii10017.var.accented" u2="&#x442;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x441;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x434;" k="-21" />
    <hkern g1="afii10017.var.accented" u2="&#x42f;" k="10" />
    <hkern g1="afii10017.var.accented" u2="&#x427;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x425;" k="-10" />
    <hkern g1="afii10017.var.accented" u2="&#x424;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x421;" k="20" />
    <hkern g1="afii10017.var.accented" u2="&#x414;" k="-15" />
    <hkern g1="afii10017.var.accented" u2="&#x40b;" k="31" />
    <hkern g1="afii10017.var.accented" u2="&#x409;" k="-32" />
    <hkern g1="afii10017.var.accented" u2="&#x2e;" k="-42" />
    <hkern g1="afii10017.var.accented" u2="&#x2a;" k="32" />
    <hkern g1="afii10022.var.accented" u2="&#x2122;" k="-52" />
    <hkern g1="afii10022.var.accented" u2="&#x4f5;" k="21" />
    <hkern g1="afii10022.var.accented" u2="&#x4b9;" k="21" />
    <hkern g1="afii10022.var.accented" u2="&#x4b7;" k="21" />
    <hkern g1="afii10022.var.accented" u2="&#x447;" k="21" />
    <hkern g1="afii10026.var.accented" u2="&#x2122;" k="-53" />
    <hkern g1="afii10032.var.accented" u2="&#x2122;" k="-52" />
    <hkern g1="afii10032.var.accented" u2="&#x201c;" k="-31" />
    <hkern g1="afii10032.var.accented" u2="&#x2018;" k="-41" />
    <hkern g1="afii10032.var.accented" u2="&#x4ae;" k="20" />
    <hkern g1="afii10032.var.accented" u2="&#x425;" k="12" />
    <hkern g1="afii10032.var.accented" u2="&#x416;" k="12" />
    <hkern g1="afii10037.var.accented" g2="afii10071.var.accented" k="32" />
    <hkern g1="afii10037.var.accented" g2="afii10097.var.accented" k="62" />
    <hkern g1="afii10037.var.accented" g2="afii10095.var.accented" k="52" />
    <hkern g1="afii10037.var.accented" g2="afii10049.var.accented" k="31" />
    <hkern g1="afii10037.var.accented" g2="afii10047.var.accented" k="20" />
    <hkern g1="afii10037.var.accented" u2="&#x2122;" k="-42" />
    <hkern g1="afii10037.var.accented" u2="&#x2026;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x201c;" k="-52" />
    <hkern g1="afii10037.var.accented" u2="&#x2018;" k="-22" />
    <hkern g1="afii10037.var.accented" u2="&#x4f5;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4e5;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x4e3;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x4dd;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4c8;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4c4;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x4c2;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4b9;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4b7;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x4b3;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x4aa;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x4a3;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x49d;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x49b;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x497;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x473;" k="62" />
    <hkern g1="afii10037.var.accented" u2="&#x472;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x458;" k="1" />
    <hkern g1="afii10037.var.accented" u2="&#x457;" k="1" />
    <hkern g1="afii10037.var.accented" u2="&#x456;" k="11" />
    <hkern g1="afii10037.var.accented" u2="&#x455;" k="70" />
    <hkern g1="afii10037.var.accented" u2="&#x451;" k="42" />
    <hkern g1="afii10037.var.accented" u2="&#x44f;" k="62" />
    <hkern g1="afii10037.var.accented" u2="&#x44d;" k="62" />
    <hkern g1="afii10037.var.accented" u2="&#x44a;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x447;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x445;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x444;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x442;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x43c;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x436;" k="50" />
    <hkern g1="afii10037.var.accented" u2="&#x434;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x431;" k="51" />
    <hkern g1="afii10037.var.accented" u2="&#x42f;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x42d;" k="20" />
    <hkern g1="afii10037.var.accented" u2="&#x424;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x421;" k="52" />
    <hkern g1="afii10037.var.accented" u2="&#x41c;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x414;" k="28" />
    <hkern g1="afii10037.var.accented" u2="&#x409;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x404;" k="41" />
    <hkern g1="afii10037.var.accented" u2="&#x3a;" k="31" />
    <hkern g1="afii10037.var.accented" u2="&#x2e;" k="83" />
    <hkern g1="afii10045.var.accented" u2="&#x2122;" k="-52" />
    <hkern g1="afii10047.var.accented" u2="&#x2122;" k="-42" />
    <hkern g1="afii10047.var.accented" u2="&#x201d;" k="-52" />
    <hkern g1="afii10047.var.accented" u2="&#x201c;" k="-42" />
    <hkern g1="afii10047.var.accented" u2="&#x2019;" k="-52" />
    <hkern g1="afii10047.var.accented" u2="&#x2018;" k="-31" />
    <hkern g1="afii10047.var.accented" u2="&#x4ae;" k="20" />
    <hkern g1="afii10047.var.accented" u2="&#x425;" k="12" />
    <hkern g1="afii10047.var.accented" u2="&#x416;" k="12" />
    <hkern g1="afii10048.var.accented" u2="&#x2122;" k="-52" />
    <hkern g1="afii10048.var.accented" u2="&#x201d;" k="-52" />
    <hkern g1="afii10048.var.accented" u2="&#x201c;" k="-42" />
    <hkern g1="afii10048.var.accented" u2="&#x2019;" k="-52" />
    <hkern g1="afii10048.var.accented" u2="&#x2018;" k="-52" />
    <hkern g1="afii10048.var.accented" u2="&#x4ae;" k="20" />
    <hkern g1="afii10048.var.accented" u2="&#x425;" k="12" />
    <hkern g1="afii10048.var.accented" u2="&#x416;" k="12" />
    <hkern g1="afii10049.var.accented" u2="&#x2122;" k="-41" />
    <hkern g1="afii10023.var.accented" u2="&#x2122;" k="-52" />
    <hkern g1="afii10023.var.accented" u2="&#x4f5;" k="21" />
    <hkern g1="afii10023.var.accented" u2="&#x4b9;" k="21" />
    <hkern g1="afii10023.var.accented" u2="&#x4b7;" k="21" />
    <hkern g1="afii10023.var.accented" u2="&#x447;" k="21" />
    <hkern g1="afii10065.var.accented" u2="&#x201c;" k="-32" />
    <hkern g1="afii10065.var.accented" u2="&#x2018;" k="-31" />
    <hkern g1="afii10080.var.accented" g2="afii10037.var.accented" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x4f2;" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x4f0;" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x4ee;" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x4af;" k="10" />
    <hkern g1="afii10080.var.accented" u2="&#x4ae;" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x423;" k="52" />
    <hkern g1="afii10080.var.accented" u2="&#x40e;" k="52" />
    <hkern g1="afii10085.var.accented" g2="hyphenminus" k="-73" />
    <hkern g1="afii10085.var.accented" u2="&#x2018;" k="-30" />
    <hkern g1="afii10085.var.accented" u2="&#x4dd;" k="-20" />
    <hkern g1="afii10085.var.accented" u2="&#x4c2;" k="-20" />
    <hkern g1="afii10085.var.accented" u2="&#x4b3;" k="-10" />
    <hkern g1="afii10085.var.accented" u2="&#x4af;" k="-20" />
    <hkern g1="afii10085.var.accented" u2="&#x497;" k="-20" />
    <hkern g1="afii10085.var.accented" u2="&#x44a;" k="-10" />
    <hkern g1="afii10085.var.accented" u2="&#x445;" k="-10" />
    <hkern g1="afii10085.var.accented" u2="&#x442;" k="-21" />
    <hkern g1="afii10085.var.accented" u2="&#x436;" k="-20" />
    <hkern g1="afii10095.var.accented" u2="&#x4af;" k="10" />
    <hkern g1="afii10095.var.accented" u2="&#x422;" k="32" />
    <hkern g1="afii10095.var.accented" u2="&#x40b;" k="32" />
    <hkern g1="afii10095.var.accented" u2="&#x402;" k="32" />
    <hkern g1="afii10096.var.accented" u2="&#x4af;" k="10" />
    <hkern g1="afii10096.var.accented" u2="&#x422;" k="42" />
    <hkern g1="afii10096.var.accented" u2="&#x40b;" k="42" />
    <hkern g1="afii10096.var.accented" u2="&#x402;" k="42" />
    <hkern u1="&#xf6ba;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#xf6ba;" u2="&#x4af;" k="-33" />
    <hkern u1="&#xf6ba;" u2="&#x4ae;" k="20" />
    <hkern u1="&#xf6ba;" u2="&#x497;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x496;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x459;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x442;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x436;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x434;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x42a;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x425;" k="-31" />
    <hkern u1="&#xf6ba;" u2="&#x416;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x414;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x40b;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x409;" k="-52" />
    <hkern u1="&#xf6ba;" u2="&#x402;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x21a;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x178;" k="-21" />
    <hkern u1="&#xf6ba;" u2="&#x164;" k="-41" />
    <hkern u1="&#xf6ba;" u2="&#x104;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#x102;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#x100;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xdd;" k="-21" />
    <hkern u1="&#xf6ba;" u2="&#xc5;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xc4;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xc3;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xc2;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xc1;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#xc0;" k="-62" />
    <hkern u1="&#xf6ba;" u2="&#x31;" k="21" />
    <hkern u1="&#xf6bb;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#xf6bb;" u2="&#x4af;" k="-33" />
    <hkern u1="&#xf6bb;" u2="&#x4ae;" k="20" />
    <hkern u1="&#xf6bb;" u2="&#x497;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x496;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x459;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x442;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x436;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x434;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x42a;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x425;" k="-31" />
    <hkern u1="&#xf6bb;" u2="&#x416;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x414;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x40b;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x409;" k="-52" />
    <hkern u1="&#xf6bb;" u2="&#x402;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x21a;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x178;" k="-21" />
    <hkern u1="&#xf6bb;" u2="&#x164;" k="-41" />
    <hkern u1="&#xf6bb;" u2="&#x104;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#x102;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#x100;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xdd;" k="-21" />
    <hkern u1="&#xf6bb;" u2="&#xc5;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xc4;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xc3;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xc2;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xc1;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#xc0;" k="-62" />
    <hkern u1="&#xf6bb;" u2="&#x31;" k="21" />
    <hkern u1="&#xf6bc;" u2="&#x4dd;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x4dc;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x4c2;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x4c1;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x4b2;" k="-31" />
    <hkern u1="&#xf6bc;" u2="&#x4af;" k="-33" />
    <hkern u1="&#xf6bc;" u2="&#x4ae;" k="20" />
    <hkern u1="&#xf6bc;" u2="&#x497;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x496;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x459;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x442;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x436;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x434;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x42a;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x425;" k="-31" />
    <hkern u1="&#xf6bc;" u2="&#x416;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x414;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x40b;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x409;" k="-52" />
    <hkern u1="&#xf6bc;" u2="&#x402;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x21a;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x178;" k="-21" />
    <hkern u1="&#xf6bc;" u2="&#x164;" k="-41" />
    <hkern u1="&#xf6bc;" u2="&#x104;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#x102;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#x100;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xdd;" k="-21" />
    <hkern u1="&#xf6bc;" u2="&#xc5;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xc4;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xc3;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xc2;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xc1;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#xc0;" k="-62" />
    <hkern u1="&#xf6bc;" u2="&#x31;" k="21" />
    <hkern g1="hyphenminus" g2="afii10085.var.accented" k="-73" />
    <hkern g1="hyphenminus" g2="afii10017.var.accented" k="-62" />
    <hkern g1="hyphenminus" u2="&#x4f3;" k="-73" />
    <hkern g1="hyphenminus" u2="&#x4f1;" k="-73" />
    <hkern g1="hyphenminus" u2="&#x4ef;" k="-73" />
    <hkern g1="hyphenminus" u2="&#x4dd;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x4dc;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x4d2;" k="-62" />
    <hkern g1="hyphenminus" u2="&#x4d0;" k="-62" />
    <hkern g1="hyphenminus" u2="&#x4c2;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x4c1;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x4b2;" k="-31" />
    <hkern g1="hyphenminus" u2="&#x4af;" k="-33" />
    <hkern g1="hyphenminus" u2="&#x4ae;" k="20" />
    <hkern g1="hyphenminus" u2="&#x497;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x496;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x45e;" k="-73" />
    <hkern g1="hyphenminus" u2="&#x459;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x443;" k="-73" />
    <hkern g1="hyphenminus" u2="&#x442;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x436;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x434;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x42a;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x425;" k="-31" />
    <hkern g1="hyphenminus" u2="&#x422;" k="-31" />
    <hkern g1="hyphenminus" u2="&#x416;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x414;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x410;" k="-62" />
    <hkern g1="hyphenminus" u2="&#x40b;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x409;" k="-52" />
    <hkern g1="hyphenminus" u2="&#x402;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x21a;" k="-11" />
    <hkern g1="hyphenminus" u2="&#x1fc;" k="-62" />
    <hkern g1="hyphenminus" u2="&#x178;" k="17" />
    <hkern g1="hyphenminus" u2="&#x164;" k="-11" />
    <hkern g1="hyphenminus" u2="&#x162;" k="-41" />
    <hkern g1="hyphenminus" u2="&#x104;" k="-31" />
    <hkern g1="hyphenminus" u2="&#x102;" k="-31" />
    <hkern g1="hyphenminus" u2="&#x100;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xdd;" k="17" />
    <hkern g1="hyphenminus" u2="&#xc6;" k="-62" />
    <hkern g1="hyphenminus" u2="&#xc5;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xc4;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xc3;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xc2;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xc1;" k="-31" />
    <hkern g1="hyphenminus" u2="&#xc0;" k="-31" />
    <hkern g1="hyphenminus" u2="Y" k="-21" />
    <hkern g1="hyphenminus" u2="W" k="-62" />
    <hkern g1="hyphenminus" u2="V" k="-52" />
    <hkern g1="hyphenminus" u2="T" k="-41" />
    <hkern g1="hyphenminus" u2="A" k="-62" />
    <hkern g1="hyphenminus" u2="&#x31;" k="21" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="-21" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="21" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="V"
	k="-42" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="afii10082,afii10105"
	k="13" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="-42" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="-21" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="-41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="colon,semicolon"
	k="-52" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-21" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="i,imacron,iogonek,dotlessi"
	k="21" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="r,racute,rcommaaccent,rcaron"
	k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="-25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="afii10077,afii10106"
	k="19" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="11" />
    <hkern g1="K,Kcommaaccent"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="-22" />
    <hkern g1="K,Kcommaaccent"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="-31" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="3" />
    <hkern g1="K,Kcommaaccent"
	g2="d,dcaron,dcroat"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="i,imacron,iogonek,dotlessi"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="22" />
    <hkern g1="K,Kcommaaccent"
	g2="r,racute,rcommaaccent,rcaron"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="afii10025,afii10047,uni0498,uni04DE,afii10047.var.accented"
	k="-31" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="y,yacute,ydieresis"
	k="1" />
    <hkern g1="N,Ntilde,Nacute,Ncommaaccent,Ncaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="11" />
    <hkern g1="N,Ntilde,Nacute,Ncommaaccent,Ncaron"
	g2="y,yacute,ydieresis"
	k="14" />
    <hkern g1="N,Ntilde,Nacute,Ncommaaccent,Ncaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="7" />
    <hkern g1="N,Ntilde,Nacute,Ncommaaccent,Ncaron"
	g2="quoteright,quotedblright"
	k="26" />
    <hkern g1="N,Ntilde,Nacute,Ncommaaccent,Ncaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-32" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="comma,period"
	k="-18" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="15" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="13" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="13" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="21" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="21" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="31" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="V"
	k="21" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="y,yacute,ydieresis"
	k="-21" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="12" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="quoteright,quotedblright"
	k="16" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="17" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,uni018F"
	g2="guillemotright,guilsinglright"
	k="21" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="-11" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="afii10099,afii10108,uni04BB"
	k="-16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="-30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="21" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="34" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="V"
	k="1" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="W"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ydieresis"
	k="-17" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="w"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis"
	k="17" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="56" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="40" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="afii10058,afii10029"
	k="5" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="50" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="r,racute,rcommaaccent,rcaron"
	k="-22" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="23" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="13" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="afii10082,afii10105"
	k="29" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="33" />
    <hkern g1="T,Tcedilla,Tcaron,Tcommaaccent"
	g2="afii10099,afii10108,uni04BB"
	k="33" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period"
	k="19" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="19" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="50" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="40" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="32" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="-18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="d,dcaron,dcroat"
	k="13" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="colon,semicolon"
	k="12" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="i,imacron,iogonek,dotlessi"
	k="7" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="afii10025,afii10047,uni0498,uni04DE,afii10047.var.accented"
	k="15" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="19" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="19" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="14" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="30" />
    <hkern g1="V"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="V"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="34" />
    <hkern g1="V"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="32" />
    <hkern g1="V"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="26" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="25" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="9" />
    <hkern g1="V"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="-32" />
    <hkern g1="V"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-20" />
    <hkern g1="V"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="40" />
    <hkern g1="V"
	g2="r,racute,rcommaaccent,rcaron"
	k="50" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="52" />
    <hkern g1="V"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="50" />
    <hkern g1="V"
	g2="afii10051,afii10060,afii10036"
	k="46" />
    <hkern g1="V"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="50" />
    <hkern g1="V"
	g2="afii10077,afii10106"
	k="40" />
    <hkern g1="V"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="22" />
    <hkern g1="V"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="42" />
    <hkern g1="V"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="40" />
    <hkern g1="V"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="50" />
    <hkern g1="V"
	g2="afii10082,afii10105"
	k="50" />
    <hkern g1="V"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="50" />
    <hkern g1="V"
	g2="afii10099,afii10108,uni04BB"
	k="50" />
    <hkern g1="W"
	g2="Y,Yacute,Ydieresis"
	k="52" />
    <hkern g1="W"
	g2="w"
	k="31" />
    <hkern g1="W"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="W"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="31" />
    <hkern g1="W"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="24" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="V"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="-83" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="V"
	k="6" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="-10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-21" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="-63" />
    <hkern g1="b,thorn"
	g2="afii10082,afii10105"
	k="-21" />
    <hkern g1="b,thorn"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="-21" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="-31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="-31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="i,imacron,iogonek,dotlessi"
	k="-52" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="32" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="21" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10077,afii10106"
	k="53" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="11" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="afii10082,afii10105"
	k="11" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,period"
	k="11" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="42" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="-32" />
    <hkern g1="f"
	g2="colon,semicolon"
	k="42" />
    <hkern g1="f"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="f"
	g2="r,racute,rcommaaccent,rcaron"
	k="-21" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="38" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="-63" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="31" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="-22" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-22" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="colon,semicolon"
	k="2" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="1" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="r,racute,rcommaaccent,rcaron"
	k="2" />
    <hkern g1="h,k,hcircumflex,hbar,kcommaaccent"
	g2="afii10077,afii10106"
	k="-15" />
    <hkern g1="n,ntilde,nacute,ncommaaccent,ncaron"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="21" />
    <hkern g1="n,ntilde,nacute,ncommaaccent,ncaron"
	g2="V"
	k="21" />
    <hkern g1="n,ntilde,nacute,ncommaaccent,ncaron"
	g2="Y,Yacute,Ydieresis"
	k="42" />
    <hkern g1="n,ntilde,nacute,ncommaaccent,ncaron"
	g2="quoteright,quotedblright"
	k="-63" />
    <hkern g1="n,ntilde,nacute,ncommaaccent,ncaron"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="-13" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,uni01DD,uni0259"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,uni01DD,uni0259"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-21" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,uni01DD,uni0259"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="-21" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,uni01DD,uni0259"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="-21" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,uni01DD,uni0259"
	g2="colon,semicolon"
	k="-21" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="afii10058,afii10029"
	k="19" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="afii10077,afii10106"
	k="-27" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="comma,period"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="W"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="21" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="32" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="-10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="-30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="afii10099,afii10108,uni04BB"
	k="5" />
    <hkern g1="t,tcedilla,tcaron,tcommaaccent"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="2" />
    <hkern g1="t,tcedilla,tcaron,tcommaaccent"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="-21" />
    <hkern g1="t,tcedilla,tcaron,tcommaaccent"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="52" />
    <hkern g1="t,tcedilla,tcaron,tcommaaccent"
	g2="afii10077,afii10106"
	k="-10" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="31" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="colon,semicolon"
	k="-40" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="i,imacron,iogonek,dotlessi"
	k="-42" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="-42" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="r,racute,rcommaaccent,rcaron"
	k="-31" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="afii10051,afii10060,afii10036"
	k="42" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="31" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="afii10058,afii10029"
	k="63" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="31" />
    <hkern g1="w"
	g2="w"
	k="-30" />
    <hkern g1="w"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="-11" />
    <hkern g1="w"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-22" />
    <hkern g1="w"
	g2="guillemotright,guilsinglright"
	k="-19" />
    <hkern g1="w"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="17" />
    <hkern g1="w"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="-30" />
    <hkern g1="w"
	g2="afii10082,afii10105"
	k="-11" />
    <hkern g1="w"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="14" />
    <hkern g1="w"
	g2="afii10099,afii10108,uni04BB"
	k="-42" />
    <hkern g1="y,yacute,ydieresis"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="-32" />
    <hkern g1="y,yacute,ydieresis"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="-30" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10025,afii10047,uni0498,uni04DE,afii10047.var.accented"
	k="14" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="-21" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="-21" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="11" />
    <hkern g1="y,yacute,ydieresis"
	g2="afii10082,afii10105"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="y,yacute,ydieresis"
	k="-31" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="32" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="afii10099,afii10108,uni04BB"
	k="73" />
    <hkern g1="quoteleft,quotedblleft"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="colon,semicolon"
	k="42" />
    <hkern g1="quoteleft,quotedblleft"
	g2="h,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron"
	k="52" />
    <hkern g1="quoteleft,quotedblleft"
	g2="r,racute,rcommaaccent,rcaron"
	k="83" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="21" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="42" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="104" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="72" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10051,afii10060,afii10036"
	k="52" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="73" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10058,afii10029"
	k="42" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10077,afii10106"
	k="73" />
    <hkern g1="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="-31" />
    <hkern g1="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	g2="guillemotright,guilsinglright"
	k="-21" />
    <hkern g1="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="-42" />
    <hkern g1="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="12" />
    <hkern g1="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="21" />
    <hkern g1="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="-32" />
    <hkern g1="afii10019,afii10025,uni0498,uni04DE"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="11" />
    <hkern g1="afii10019,afii10025,uni0498,uni04DE"
	g2="afii10077,afii10106"
	k="-41" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="comma,period"
	k="-21" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-11" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="10" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="21" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="V"
	k="21" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="W"
	k="10" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="Y,Yacute,Ydieresis"
	k="21" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="104" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="-41" />
    <hkern g1="afii10052,afii10020,uni0492"
	g2="afii10053,afii10032,afii10035,uni04AA,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented"
	k="42" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="comma,period"
	k="72" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="hyphen,uni00AD,uni2010,uni2011,figuredash,endash,emdash,uniF6BA,uniF6BB,uniF6BC,hyphenminus"
	k="72" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="50" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="72" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="51" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="31" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="10" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10103,afii10104,afii10107,afii10109,afii10193,afii10098,uni0493,uni04A3,uni04C8,uni04F9,afii10074.var.accented,afii10093.var.accented,afii10096.var.accented"
	k="52" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="afii10070,afii10080,afii10083,afii10086,afii10071,afii10101,uni04AB,uni04D7,afii10846,uni04E7,uni04E9,uni04EB,afii10070.var.accented,afii10080.var.accented,afii10071.var.accented"
	k="73" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="afii10082,afii10105"
	k="10" />
    <hkern g1="afii10023,afii10022,uni04D6,afii10022.var.accented,afii10023.var.accented"
	g2="afii10099,afii10108,uni04BB"
	k="31" />
    <hkern g1="afii10061,afii10024,afii10028,uni0496,uni049A,uni049C,uni04C1,uni04C3,uni04DC"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="-63" />
    <hkern g1="afii10061,afii10024,afii10028,uni0496,uni049A,uni049C,uni04C1,uni04C3,uni04DC"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="-52" />
    <hkern g1="afii10061,afii10024,afii10028,uni0496,uni049A,uni049C,uni04C1,uni04C3,uni04DC"
	g2="afii10058,afii10029"
	k="20" />
    <hkern g1="afii10061,afii10024,afii10028,uni0496,uni049A,uni049C,uni04C1,uni04C3,uni04DC"
	g2="afii10077,afii10106"
	k="21" />
    <hkern g1="afii10032,afii10047,afii10048,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented,afii10047.var.accented,afii10048.var.accented"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="53" />
    <hkern g1="afii10032,afii10047,afii10048,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented,afii10047.var.accented,afii10048.var.accented"
	g2="afii10058,afii10029"
	k="21" />
    <hkern g1="afii10032,afii10047,afii10048,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented,afii10047.var.accented,afii10048.var.accented"
	g2="afii10082,afii10105"
	k="62" />
    <hkern g1="afii10032,afii10047,afii10048,uni04D8,uni04E6,uni04E8,uni04EA,afii10032.var.accented,afii10047.var.accented,afii10048.var.accented"
	g2="afii10085,afii10110,uni04AF,uni04EF,uni04F1,uni04F3,afii10085.var.accented"
	k="-62" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="20" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="d,dcaron,dcroat"
	k="31" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="colon,semicolon"
	k="31" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="31" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="i,imacron,iogonek,dotlessi"
	k="30" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="t,tcedilla,tcaron,tcommaaccent"
	k="10" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="20" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="-10" />
    <hkern g1="afii10053,afii10035,uni04AA"
	g2="afii10058,afii10029"
	k="10" />
    <hkern g1="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	g2="e,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron"
	k="31" />
    <hkern g1="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	g2="afii10017,uni04D0,uni04D2,afii10017.var.accented"
	k="-53" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="Y,Yacute,Ydieresis"
	k="21" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="w"
	k="-20" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="-21" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,OE,uni018F"
	k="31" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-21" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="T,Tcedilla,Tcaron,Tcommaaccent"
	k="-11" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oe,uni01DD,uni0259"
	k="21" />
    <hkern g1="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	g2="afii10062,afii10037,uni04AE,uni04EE,uni04F0,uni04F2,afii10037.var.accented"
	k="-30" />
    <hkern g1="afii10067,afii10073,uni0499,uni04DF"
	g2="r,racute,rcommaaccent,rcaron"
	k="52" />
    <hkern g1="afii10067,afii10073,uni0499,uni04DF"
	g2="afii10099,afii10108,uni04BB"
	k="52" />
    <hkern g1="afii10068,afii10100,uni0493"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="31" />
    <hkern g1="afii10068,afii10100,uni0493"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	k="31" />
    <hkern g1="afii10068,afii10100,uni0493"
	g2="afii10073,afii10095,uni0499,uni04DF,afii10095.var.accented"
	k="21" />
    <hkern g1="afii10068,afii10100,uni0493"
	g2="afii10065,uni04D1,uni04D3,afii10065.var.accented"
	k="31" />
    <hkern g1="afii10070,afii10071,uni04D7,afii10070.var.accented,afii10071.var.accented"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="27247" />
  </font>
</defs></svg>
