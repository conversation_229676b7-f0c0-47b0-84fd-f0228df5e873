<?php
# This is the global bootstrap file containing code which should be run for *every* entry point.

# Path to the real root of project
define('ROOT_DIR', realpath(dirname(__FILE__) . '/../'));
define('COMMON_DIR', ROOT_DIR .'/common');

# ==================================================================================
# NOTE that you must declare `YII_DEBUG` and `YII_TRACE_LEVEL`
# BEFORE loading the framework or it will have no effect on Yii!
defined('YII_DEBUG') or define('YII_DEBUG',false);
# Stacktraces 3 level deep
define('YII_TRACE_LEVEL', 3);
# ==================================================================================

# Include our own helper class
require_once COMMON_DIR . '/components/core/Helper.php';
# Launching the Yii framework.
require_once ROOT_DIR . '/../framework/yii.php';

# Some global aliases
Yii::setPathOfAlias('root', ROOT_DIR);
Yii::setPathOfAlias('common', COMMON_DIR);
