<?php

class AccessTokenCom extends MainCom {

    const algo = 'md5';

    public function cleanupAccessToken() {
        AuthorizedTokenBase::model()->cleanupAccessToken();
    }

    public static function generateAccessToken($dataStr = NULL, $algo = '') {
        $algo = $algo !== '' ? $algo : self::algo;
        $raw_str = self::generateUniqueId($dataStr);

        return hash($algo, $raw_str);
    }

    public static function generateUniqueId($rawStr = NULL, $unique = TRUE) {
        if (!is_null($rawStr)) {
            if ($unique) {
                $raw_str = $rawStr . uniqid(mt_rand(), TRUE);
            } else {
                $raw_str = $rawStr;
            }
        } else {
            $raw_str = uniqid(mt_rand(), TRUE);
        }

        return $raw_str;
    }

}