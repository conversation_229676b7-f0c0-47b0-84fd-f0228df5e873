<?php

class CategoriesTagCom extends MainCom 
{
    public function install_layer1() {
        $layer_1 = array(
            'ENTERTAINMENT' => array(
                1 => array('tag_title' => 'Entertainment', 'tag_description' => ''), 
                2 => array('tag_title' => '娱乐', 'tag_description' => ''), 
                3 => array('tag_title' => '娛樂', 'tag_description' => ''), 
                4 => array('tag_title' => 'Hiburan', 'tag_description' => '')
            ),
            'MOBILE' => array(
                1 => array('tag_title' => 'Mobile', 'tag_description' => ''), 
                2 => array('tag_title' => '手机', 'tag_description' => ''), 
                3 => array('tag_title' => '手機', 'tag_description' => ''), 
                4 => array('tag_title' => 'Ponsel', 'tag_description' => '')
            ), 
            'TOOLS' => array(
                1 => array('tag_title' => 'Tools', 'tag_description' => ''), 
                2 => array('tag_title' => '游戏工具', 'tag_description' => ''), 
                3 => array('tag_title' => '遊戲工具', 'tag_description' => ''), 
                4 => array('tag_title' => 'Alat-alat', 'tag_description' => '')
            ),
            'PC_GAMES' => array(
                1 => array('tag_title' => 'PC Games', 'tag_description' => ''), 
                2 => array('tag_title' => '电脑游戏', 'tag_description' => ''), 
                3 => array('tag_title' => '電腦遊戲', 'tag_description' => ''), 
                4 => array('tag_title' => 'Permainan PC', 'tag_description' => '')
            ),
            'GIFT_CARDS' => array(
                1 => array('tag_title' => 'Gift Cards', 'tag_description' => ''), 
                2 => array('tag_title' => '礼品卡', 'tag_description' => ''), 
                3 => array('tag_title' => '禮品卡', 'tag_description' => ''), 
                4 => array('tag_title' => 'Kartu Hadiah', 'tag_description' => '')
            ),
            'GAME_CARD' => array(
                1 => array('tag_title' => 'Game Cards', 'tag_description' => ''), 
                2 => array('tag_title' => '游戏点卡', 'tag_description' => ''), 
                3 => array('tag_title' => '遊戲點卡', 'tag_description' => ''), 
                4 => array('tag_title' => 'Kartu Permainan', 'tag_description' => '')
            ),
            'DIRECT_TOP_UP' => array(
                1 => array('tag_title' => 'Direct Top Up', 'tag_description' => ''), 
                2 => array('tag_title' => '直充', 'tag_description' => ''), 
                3 => array('tag_title' => '直充', 'tag_description' => ''), 
                4 => array('tag_title' => 'Top Up Langsung', 'tag_description' => '') 
            )
        );
        
        foreach ($layer_1 as $key => $layer_arr) {
            $tag_id = CategoriesTagBase::model()->addNewTag('ROOT', $key);
            
            if ($tag_id) {
                foreach ($layer_arr as $lang_id => $lang_arr) {
                    $lang_arr['tag_id'] = $tag_id;
                    $lang_arr['language_id'] = $lang_id;
                    
                    CategoriesTagDescriptionBase::model()->saveNewRecord($lang_arr);
                }
            }
        }
        
        echo 'done 1';
    }
    
    public function install_layer2() {
        $layer_2 = array(
            'DIRECT_TOP_UP' => array(
                'DIRECT_TOP_UP_CHILD' => array(
                    1 => array('tag_title' => 'Direct Top Up', 'tag_description' => '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)'), 
                    2 => array('tag_title' => '直充', 'tag_description' => '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)'), 
                    3 => array('tag_title' => '直充', 'tag_description' => '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)'), 
                    4 => array('tag_title' => 'Top Up Langsung', 'tag_description' => '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)') 
                ),
            ),
            'GAME_CARD' => array(
                'GAME_POINT' => array(
                    1 => array('tag_title' => 'Game Point', 'tag_description' => '(League of Legends, Minecraft)'), 
                    2 => array('tag_title' => '游戏点数', 'tag_description' => '(League of Legends, Minecraft)'), 
                    3 => array('tag_title' => '遊戲點數', 'tag_description' => '(League of Legends, Minecraft)'), 
                    4 => array('tag_title' => 'Poin Permainan', 'tag_description' => '(League of Legends, Minecraft)')
                ),
                'UNIVERSAL_GAME_CARDS' => array(
                    1 => array('tag_title' => 'Universal Game Cards', 'tag_description' => '(GoCash, Rixty, Cherry Credits, Karma Koin)'), 
                    2 => array('tag_title' => '通用游戏卡', 'tag_description' => '(GoCash, Rixty, Cherry Credits, Karma Koin)'), 
                    3 => array('tag_title' => '通用遊戲卡', 'tag_description' => '(GoCash, Rixty, Cherry Credits, Karma Koin)'), 
                    4 => array('tag_title' => 'Kartu Permainan Universal', 'tag_description' => '(GoCash, Rixty, Cherry Credits, Karma Koin)') 
                ),
                'CONSOLES' => array(
                    1 => array('tag_title' => 'Consoles', 'tag_description' => '(Xbox Live, Playstation Network Card, Nintendo)'), 
                    2 => array('tag_title' => '游戏机', 'tag_description' => '(Xbox Live, Playstation Network Card, Nintendo)'), 
                    3 => array('tag_title' => '遊戲機', 'tag_description' => '(Xbox Live, Playstation Network Card, Nintendo)'), 
                    4 => array('tag_title' => 'Konsol', 'tag_description' => '(Xbox Live, Playstation Network Card, Nintendo)')
                ),
            ),
            'GIFT_CARDS' => array(
                'GIFT_CARDS_CHILD' => array(
                    1 => array('tag_title' => 'Gift Cards', 'tag_description' => '(iTunes, Amazon, GameStop)'), 
                    2 => array('tag_title' => '礼品卡', 'tag_description' => '(iTunes, Amazon, GameStop)'), 
                    3 => array('tag_title' => '禮品卡', 'tag_description' => '(iTunes, Amazon, GameStop)'), 
                    4 => array('tag_title' => 'Kartu Hadiah', 'tag_description' => '(iTunes, Amazon, GameStop)')
                ),
            ),
            'PC_GAMES' => array(
                'PC_GAMES_OTHERS' => array(
                    1 => array('tag_title' => 'Others', 'tag_description' => '(Battlefield 4, Call of Duty, Brink, Left4Dead)'), 
                    2 => array('tag_title' => '其它', 'tag_description' => '(Battlefield 4, Call of Duty, Brink, Left4Dead)'), 
                    3 => array('tag_title' => '其它', 'tag_description' => '(Battlefield 4, Call of Duty, Brink, Left4Dead)'), 
                    4 => array('tag_title' => 'Lain-lain', 'tag_description' => '(Battlefield 4, Call of Duty, Brink, Left4Dead)')
                ),
                'MASSIVELY_MULTIPLAYER' => array(
                    1 => array('tag_title' => 'Massively Multiplayer', 'tag_description' => '(World of Warcraft, WildStar, SWTOR)'), 
                    2 => array('tag_title' => '大型多人在线游戏', 'tag_description' => '(World of Warcraft, WildStar, SWTOR)'), 
                    3 => array('tag_title' => '大型多人在線遊戲', 'tag_description' => '(World of Warcraft, WildStar, SWTOR)'), 
                    4 => array('tag_title' => 'Pemain Multiplayer Masal', 'tag_description' => '(World of Warcraft, WildStar, SWTOR)')
                ),
            ),
            'TOOLS' => array(
                'HIGH_LEVEL_ACCOUNT' => array(
                    1 => array('tag_title' => 'High Level Account', 'tag_description' => '(World of Warcraft, Aion, League of Legends)'), 
                    2 => array('tag_title' => '高级账号', 'tag_description' => '(World of Warcraft, Aion, League of Legends)'), 
                    3 => array('tag_title' => '高級賬號', 'tag_description' => '(World of Warcraft, Aion, League of Legends)'), 
                    4 => array('tag_title' => 'Akun Tingkat Tinggi', 'tag_description' => '(World of Warcraft, Aion, League of Legends)')
                ),
                'HARDWARE' => array(
                    1 => array('tag_title' => 'Hardware', 'tag_description' => '(Gaming Keyboard, Gaming Mouse, Mousepad)'), 
                    2 => array('tag_title' => '硬件', 'tag_description' => '(游戏键盘，游戏鼠标，鼠标垫)'), 
                    3 => array('tag_title' => '硬件', 'tag_description' => '(遊戲鍵盤，遊戲鼠標，鼠標墊)'), 
                    4 => array('tag_title' => 'Perangkat Keras', 'tag_description' => '(Gaming Keyboard, Gaming Mouse, Mousepad)')
                ),
                'SOFTWARE' => array(
                    1 => array('tag_title' => 'Software', 'tag_description' => '(Anti Virus)'), 
                    2 => array('tag_title' => '软件', 'tag_description' => '(Anti Virus)'), 
                    3 => array('tag_title' => '軟件', 'tag_description' => '(Anti Virus)'), 
                    4 => array('tag_title' => 'Perangkat Lunak', 'tag_description' => '(Anti Virus)')
                ),
                'VPN' => array(
                    1 => array('tag_title' => 'VPN', 'tag_description' => '(Battleping, WTFast, Ultima Proxy)'), 
                    2 => array('tag_title' => 'VPN', 'tag_description' => '(Battleping, WTFast, Ultima Proxy)'), 
                    3 => array('tag_title' => 'VPN', 'tag_description' => '(Battleping, WTFast, Ultima Proxy)'), 
                    4 => array('tag_title' => 'VPN', 'tag_description' => '(Battleping, WTFast, Ultima Proxy)')
                ),
            ),
            'MOBILE' => array(
                'MOBILE_RELOAD' => array(
                    1 => array('tag_title' => 'Mobile Reload', 'tag_description' => '(XOX, YES)'), 
                    2 => array('tag_title' => '手机充值', 'tag_description' => '(XOX, YES)'), 
                    3 => array('tag_title' => '手機充值', 'tag_description' => '(XOX, YES)'), 
                    4 => array('tag_title' => 'Pengisian Pulsa', 'tag_description' => '(XOX, YES)')
                ),
                'MOBILE_GAMES' => array(
                    1 => array('tag_title' => 'Mobile Games', 'tag_description' => ''), 
                    2 => array('tag_title' => '手机游戏', 'tag_description' => ''), 
                    3 => array('tag_title' => '手機遊戲', 'tag_description' => ''), 
                    4 => array('tag_title' => 'Permainan Ponsel', 'tag_description' => '')
                ),
            ),
            'ENTERTAINMENT' => array(
                'COLLECTIBLES' => array(
                    1 => array('tag_title' => 'Collectibles', 'tag_description' => '(Toys, Figurines, Miniatures)'), 
                    2 => array('tag_title' => '游戏周边', 'tag_description' => '(公仔， 手办， 模型)'), 
                    3 => array('tag_title' => '遊戲周邊', 'tag_description' => '(公仔，手辦，模型)'), 
                    4 => array('tag_title' => 'Koleksi', 'tag_description' => '(Mainan, Patung-patung, Miniatur)')
                ),
                'MOVIES_AND_MUSIC' => array(
                    1 => array('tag_title' => 'Movies & Music', 'tag_description' => '(Channel Subscription, Movies, Music)'), 
                    2 => array('tag_title' => '电影＆音乐', 'tag_description' => '(频道订阅，电影，音乐)'), 
                    3 => array('tag_title' => '音樂＆電影', 'tag_description' => '(頻道訂閱，電影，音樂)'), 
                    4 => array('tag_title' => 'Film & Musik', 'tag_description' => '(Channel berlangganan, Film, Musik)')
                ),
            ),
        );
        
        foreach ($layer_2 as $root_key => $layer2_arr) {
            foreach ($layer2_arr as $key => $layer_arr) {
                $tag_id = CategoriesTagBase::model()->addNewTag($root_key, $key);

                if ($tag_id) {
                    foreach ($layer_arr as $lang_id => $lang_arr) {
                        $lang_arr['tag_id'] = $tag_id;
                        $lang_arr['language_id'] = $lang_id;

                        CategoriesTagDescriptionBase::model()->saveNewRecord($lang_arr);
                    }
                }
            }
        }
        
        echo 'done 2';
    }
    
    public function bindWithGameIDByTagKey($game_id, $tag_key) {
        $return_int = 0;
        
        if ($tag_id = CategoriesTagBase::model()->getTagByKey($tag_key)) {
            $return_int = CategoriesTagmapBase::model()->addNewbinding($game_id, $tag_id);
        }
        
        return $return_int;
    }
    
    public function bindWithGameIDByTagID($game_id, $tag_id) {
        return CategoriesTagmapBase::model()->addNewbinding($game_id, $tag_id);
    }
    
    protected function getGameIDByTagID($tag_id) {
        return CategoriesTagmapBase::model()->getCagetoryIDByTagID($tag_id);
    }
    
    public function getActiveTagIDByTagKey($tag_key) {
        $return_int = '';
        
        if ($tag_array = CategoriesTagBase::model()->getTagByKey($tag_key)) {
            $return_int = (isset($tag_array['tag_status']) && $tag_array['tag_status'] == 1) ? $tag_array['tag_id'] : $return_int;
        }
        
        return $return_int;
    }
    
    protected function getTagIDByGameID($game_id) {
        return CategoriesTagmapBase::model()->getTagIDByGameID($game_id);
    }
    
    protected function getGameIDFilteredByTagIDAndChild($tag_id) {
        $result = CategoriesTagBase::model()->getTreeInfoByTagID($tag_id);
        return CategoriesTagmapBase::model()->getGameIDsByTagIDs($result);
    }
    
    public function getSinglePathByGameID($game_id) {
        $return_array = array();
        
        if ($tag_id = $this->getTagIDByGameID($game_id)) {
            $path_array = $this->getSinglePathByTagID($tag_id);

            foreach ($path_array as $tag_id => $tag_key) {
                $return_array[$tag_id] = $this->getDescriptionByTagID($tag_id, NULL, NULL, 'tag_title');
            }
        }
        return $return_array;
    }
    
    public function getSinglePathTagKeyByGameID($game_id) {
        $return_array = array();
        
        if ($tag_id = $this->getTagIDByGameID($game_id)) {
            $return_array = $this->getSinglePathTagKeyByTagID($tag_id);
        }
        
        return $return_array;
    }
    
    public function getSinglePathTagKeyByTagID($tag_id) {
        return $this->getSinglePathByTagID($tag_id);
    }
    
    protected function getSinglePathByTagID($tag_id) {
        $return_array = array();
        
        $path_array = CategoriesTagBase::model()->getSinglePathByTagID($tag_id);

        foreach ($path_array as $p_array) {
            $return_array[$p_array['tag_id']] = $p_array['tag_key'];
        }

        return $return_array;
    }
    
    public function getSinglePathDescriptionByTagID($tag_id, $language_id = NULL, $default_language_id = NULL) {
        $return_array = array();
        $path_array = $this->getSinglePathByTagID($tag_id);
        
        foreach ($path_array as $tag_id => $tag_key) {
            $return_array[$tag_id] = $this->getDescriptionByTagID($tag_id, $language_id, $default_language_id, 'tag_title');
        }
        
        return $return_array;
    }
    
    protected function getDescriptionByTagID($tag_id, $language_id, $default_language_id) {
        return CategoriesTagDescriptionBase::model()->getTagInfo($tag_id, $language_id, $default_language_id);
    }
    
    public function getFullTree() {
        $return_array = $this->toHierarchy(CategoriesTagBase::model()->getFullPathByLabel());
        return (isset($return_array[0]['children']) ? $return_array[0]['children'] : array());
    }
    
    public function isRecordExist($game_id, $tag_id) {
        return CategoriesTagmapBase::model()->exists(array(
            'condition' => 'game_id = :game_id AND tag_id = :tag_id',
            'params' => array(':game_id' => $game_id, ':tag_id' => $tag_id)
        ));
    }
    
    private function toHierarchy($collection) {
        $l = 0;
        $trees = array();

        if (count($collection) > 0) {
            // Node Stack. Used to help building the hierarchy
            $stack = array();

            foreach ($collection as $node) {
                $item = $node;
                $item['children'] = array();

                // Number of stack items
                $l = count($stack);

                // Check if we're dealing with different levels
                while($l > 0 && $stack[$l - 1]['depth'] >= $item['depth']) {
                    array_pop($stack);
                    $l--;
                }

                // Stack is empty (we are inspecting the root)
                if ($l == 0) {
                    // Assigning the root node
                    $i = count($trees);
                    $trees[$i] = $item;
                    $stack[] = & $trees[$i];
                } else {
                    // Add node to parent
                    $i = count($stack[$l - 1]['children']);
                    $stack[$l - 1]['children'][$i] = $item;
                    $stack[] = & $stack[$l - 1]['children'][$i];
                }
            }
        }

        return $trees;
    }
}