<?php

class CategoryCom extends MainCom 
{
    protected function isCategoryIDActive($category_id) {
        return CategoriesBase::model()->isCategoryExistByStatus($category_id, 1);
    }
    
    protected function isCustomerGroupCategoryID($category_id, $customers_groups_id) {
        $result_array = $this->getCustomerGroupCategoryIDs($customers_groups_id);
		
        return isset($result_array[$category_id]);
    }
    
    protected function getCustomerGroupCategoryIDs($customers_groups_id) {
        return CategoriesGroupsBase::model()->getGameIDsByGroupID($customers_groups_id);
    }
    
    public function getCategoryIDsBySearchKeyword($keyword) {
        if (!$return_array = CategoriesSearchBase::model()->getCategoryIDsByMatchingAgainstScore($keyword)) {
            $return_array = CategoriesSearchBase::model()->getCategoryIDsByKeyword($keyword);
        }
        
        return $return_array;
    }
    
    public function getCategoryIDsBySearchKeywordSelective($keyword, $cat_ids) {
        if (!$return_array = CategoriesSearchBase::model()->getCategoryIDsByMatchingAgainstScoreSelective($keyword, $cat_ids)) {
            $return_array = CategoriesSearchBase::model()->getCategoryIDsByKeywordSelective($keyword, $cat_ids);
        }
        
        return $return_array;
    }
    
    public function getCategoryIDsFromCategoryStructure() {
        $return_array = array();
        
        if ($ids_str = CategoriesStructuresBase::model()->getIDs()) {
            $return_array = explode(",", $ids_str);
        }
        
        return $return_array;
    }
    
    public function getGameIDByCPathArrayFilterByCategoryStructure($cPath_array) {
        $return_int = 0;
        
        if (is_array($cPath_array)) {
            if ($game_id_array = array_intersect($this->getCategoryIDsFromCategoryStructure(), $cPath_array)) {
                $return_int = array_shift($game_id_array);
            }
        }
        
        return $return_int;
    }
    
	public function getCategoriesInfoFromAllCPTC($pass_path, $category_id) {
		$return_array = array();
        $pass_product_child_types_array = $this->getCPTCIDs($category_id);
        
		foreach ($pass_product_child_types_array as $product_child_types_id) {
            if ($response_array = $this->getCategoriesInfoByCPTC($pass_path, $product_child_types_id)) {
                $return_array[] = $response_array;
            }
		}
		
		return $return_array;
	}
	
    protected function getCPathByParentID($parent_id) {
        return CategoriesBase::model()->getCPathByParentID($parent_id);
    }
    
    public function getCPathByID($category_id) {
        return CategoriesBase::model()->getCPathByCategoryID($category_id);
    }
    
    // function get_available_product_child_type
    protected function getCPTCIDs($category_id) {
            return CategoriesProductTypesBase::model()->getCustomProductsTypeChildID($category_id);
    }
	
    protected function getHeadingTitle($category_id, $language_id, $default_language_id) {
        return CategoriesDescriptionBase::model()->getCategoriesHeadingTitle($category_id, $language_id, $default_language_id);
    }

    protected function getName($category_id, $language_id, $default_language_id) {
        return CategoriesDescriptionBase::model()->getCategoriesName($category_id, $language_id, $default_language_id);
    }
    
    protected function getCategoriesImage($category_id, $language_id, $default_languages_id) {
        return CategoriesDescriptionBase::model()->getCategoriesImage($category_id, $language_id, $default_languages_id);
    }
    
    protected function getCategoriesImageTitle($category_id, $language_id, $default_languages_id) {
        return CategoriesDescriptionBase::model()->getCategoriesImageTitle($category_id, $language_id, $default_languages_id);
    }
    
    // not in used yet
    public function getImageSrc($category_id, $language_id, $default_languages_id) {
        $img_arr = $this->getCategoriesImage($category_id, $language_id, $default_languages_id);
        list($img_src, ,) = $img_arr['dim'];
        
        return $img_src;
    }
    
    protected function getPinYin($category_id, $language_id, $default_language_id) {
        return CategoriesDescriptionBase::model()->getCategoriesPinYin($category_id, $language_id, $default_language_id);
    }
    
    protected function getCategoriesInfoByCPT($pass_path, $product_types_id) {
        return CategoriesBase::model()->getSubCategoryInfoByCustomProductTypeID($pass_path, $product_types_id);
    }
    
    protected function getCategoriesInfoByCPTC($pass_path, $product_child_types_id) {
        return CategoriesBase::model()->getSubCategoryInfoByCustomProductTypeChildID($pass_path, $product_child_types_id);
    }
    
    // get 3rd layer id
    protected function getCategoryInfoByGameID($game_id, $product_types_id) {
        $return_array = array();
        $result_array = CategoriesBase::model()->getSubCategoriesInfoByGameID($game_id, $product_types_id, 1);

        foreach ($result_array as $subcategory_array) {
            $subcategory_array['cpath'] = $subcategory_array['categories_parent_path'] ? substr($subcategory_array['categories_parent_path'], 1) . $subcategory_array['categories_id'] : $subcategory_array['categories_id'];
            $return_array = $this->getSubcategoriesByCPath($subcategory_array, $return_array);
        }

        return $return_array;
    }
    
    public function getAllCategoryInfoByGameIDs($game_id_array, $product_types_id) {
        $return_array = array();
        
        foreach ($game_id_array as $game_id) {
            $return_array[$game_id] = $this->getCategoryInfoByGameID($game_id, $product_types_id);
        }

        return $return_array;
    }
    
    // get 4th layer id
    protected function getSubcategoriesByCPath($category_info_array, $return_array = array()) {
        $return_array[] = $category_info_array;
        $forth_layer_id_array = CategoriesBase::model()->getSubcategoriesByCPath($category_info_array['cpath'], 1);

        foreach ($forth_layer_id_array as $category_info) {
            $return_array[] = $category_info;
        }
        
        return $return_array;
    }

    public function getTotalActiveProductsByCategoryID($category_id, $customers_groups_id, $include_subcategories = TRUE) {
        $products_count = 0;
        
        $products_count += CategoriesBase::model()->getTotalActiveProducts($category_id, $customers_groups_id, self::ALL_USER_ACCESS_GROUP_ID);
        
        if ($include_subcategories) {
            $sub_categories_array = CategoriesBase::model()->getSubCategoryIDByParentID($category_id, $customers_groups_id, self::ALL_USER_ACCESS_GROUP_ID);
            
            foreach ($sub_categories_array as $c_id) {
                $products_count += CategoriesBase::model()->getTotalActiveProducts($c_id, $customers_groups_id, self::ALL_USER_ACCESS_GROUP_ID);
            }
        }

        return $products_count;
    }

    public function filterEnabledCagetoriesID($category_id_array) {
        return array_intersect($category_id_array, CategoriesBase::model()->getAllEnabledCagetoriesID());
    }
}