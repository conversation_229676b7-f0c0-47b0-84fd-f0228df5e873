<?php

class ConfigurationCom extends MainCom 
{
    public  $category_id,
            $language_id,
            $default_language_id,
            $category_config_array;
	
    // prevent nested loop
    public function __construct() {}
    
    public function _init($language_id = NULL, $default_language_id = NULL) {
        $this->language_id = notNull($language_id) ? $language_id : $this->getRegional('language_id');
        $this->default_language_id = notNull($default_language_id) ? $default_language_id : $this->getRegional('default_language_id');
    }
    
    public static function getValue($key, $cache = true) {
        if($cache){
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ConfigurationBase::model()->tableName() . '/key/' . $key;
            $result = Yii::app()->cache->get($cache_key);

            if ($result === false) {
                $result = ConfigurationBase::model()->getConfigValue($key);
                Yii::app()->cache->set($cache_key, $result, 86400);
            }
        } else {
            $result = ConfigurationBase::model()->getConfigValue($key);
        }
        
        return $result;
    }
    
    public static function getCategoryConfigValueByProductID($product_id, $cfg_key='', $key_type='configuration_key') {
        $return_array = array();
        
        $category_id = ProductsToCategoriesBase::model()->getCategoryID($product_id);
        
        if (notNull($cfg_key)) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesConfigurationBase::model()->tableName() . '/categories_id/' . $category_id . '/cfg_key/' . $cfg_key . '/key_type' . $key_type . '/cfg_setting';
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== FALSE) {
                $return_array = $cache_result;
            } else {
                $category_obj = new CategoryCom();
                $cat_path = $category_obj->getCPathByID($category_id);
                
                $cat_path_array = explode('_', $cat_path);
                $cat_path_array = array_merge(array(0), $cat_path_array);

                if ($key_type == 'configuration_key') {
                    $ttl_itm = count($cat_path_array)-1;
                    
                    for ($i=$ttl_itm; $i >= 0; $i--) {
                        if ($result = CategoriesConfigurationBase::model()->getValueByKey($cat_path_array[$i], $cfg_key)) {
                            $return_array = $result;
                            break;
                        }
                    }
                } else if ($key_type == 'group_id') {
                    $ttl_itm = count($cat_path_array);
                    
                    for ($i=0; $i < $ttl_itm; $i++) {
                        if ($result = CategoriesConfigurationBase::model()->getValueByCategoryIDByGroupID($cat_path_array[$i], $cfg_key)) {
                            $return_array = array_merge($return_array, $result);
                        }
                    }
                }

                Yii::app()->cache->set($cache_key, $return_array, 7200);
            }
        } else {
            $return_array = CategoriesConfigurationBase::model()->getValueByCategoryID('0');
            
            $category_obj = new CategoryCom();
            $cat_path = $category_obj->getCPathByID($category_id);
            
            if (notNull($cat_path)) {
                $cat_path_array = explode('_', $cat_path);
                $ttl_itm = count($cat_path_array);
                
                for ($i=0; $i < $ttl_itm; $i++) {
                    if ($result = CategoriesConfigurationBase::model()->getValueByCategoryID($cat_path_array[$i])) {
                        $return_array = array_merge($return_array, $result);
                    }
                }
            }
        }

        return $return_array;
    }
    
    public function getCategoryConfigValueByCPath($cPath, $cfg_key='', $key_type='configuration_key') {
        $return_array = array();
        $cat_path = $cPath;

        if (isset($this->category_config_array[$key_type][$cPath][$cfg_key])) {
            $return_array = $this->category_config_array[$key_type][$cPath][$cfg_key];
        } else {
            if (notNull($cfg_key)) {
                $cat_path_array = array_merge(array(0), explode('_', $cat_path));

                if ($key_type == 'configuration_key') {
                    $ttl_itm = count($cat_path_array)-1;
                    
                    for ($i=$ttl_itm; $i >= 0; $i--) {
                        if ($result = CategoriesConfigurationBase::model()->getValueByKey($cat_path_array[$i], $cfg_key)) {
                            $return_array = $result;
                            break;
                        }
                    }
                } else if ($key_type == 'group_id') {
                    $ttl_itm = count($cat_path_array);
                    
                    for ($i=0; $i < $ttl_itm; $i++) {
                        if ($result = CategoriesConfigurationBase::model()->getValueByCategoryIDByGroupID($cat_path_array[$i], $cfg_key)) {
                            $return_array = array_merge($return_array, $result);
                        }
                    }
                }
            } else {
                $return_array = CategoriesConfigurationBase::model()->getValueByCategoryID('0');

                if (notNull($cat_path)) {
                    $cat_path_array = explode('_', $cat_path);
                    $ttl_itm = count($cat_path_array);
                    
                    for ($i=0; $i < $ttl_itm; $i++) {
                        if ($result = CategoriesConfigurationBase::model()->getValueByCategoryID($cat_path_array[$i])) {
                            $return_array = array_merge($return_array, $result);
                        }
                    }
                }
            }
            
            $this->category_config_array[$key_type][$cPath][$cfg_key] = $return_array;
        }
        
        return $return_array;
    }
}