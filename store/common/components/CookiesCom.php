<?php

class CookiesCom extends CApplicationComponent {

    const SAMESITE_STRICT = 'Strict';
    /**
     * Set and update a cookie which its value is a string
     * @param type $name 
     * @param type $value 
     * @return boolean
     */
    public static function storeCookie($name, $value, $expireTime = '', $path = '/', $sameSite = self::SAMESITE_STRICT) {
        if (is_string($value)) {
            $cookie = new CHttpCookie($name, $value);
            $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];

            if (!empty($expireTime)) {
                $cookie->expire = $expireTime;
            }
            $cookie->path = $path."; SameSite=".$sameSite;

            Yii::app()->request->cookies[$name] = $cookie;
            return true;
        }
        return false;
    }

    /**
     * 
     * @param string $name
     * @param string $value
     * @param boolean $append -- 0 if update the cookie array, 1 if append the cookie array
     * @return boolean
     */
    public static function storeCookieArray($name, $value, $append = 1, $expireTime = '', $path = '/', $sameSite = self::SAMESITE_STRICT) {
        if (is_null(self::readCookie($name))) {
            $cookie = is_array($value) ? new CHttpCookie($name,  CJSON::encode($value)) : new CHttpCookie($name, $value);
            $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
            
            if (!empty($expireTime)) {
                $cookie->expire = $expireTime;
            }
            $cookie->path = $path;

            Yii::app()->request->cookies[$name] = $cookie;
        } else {
            if ($append) {
                self::_appendCookieArray($name, $value);
            } else {
                $cookie = new CHttpCookie($name,  CJSON::encode($value));
                $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
                
                if (!empty($expireTime)) {
                    $cookie->expire = $expireTime;
                }
                $cookie->path = $path;

                Yii::app()->request->cookies[$name] = $cookie;
            }
        }
        return true;
    }

    public static function readCookie($name) {
        if (!empty(Yii::app()->request->cookies[$name])) {
            return Yii::app()->request->cookies[$name]->value;
        }

        return null;
    }

    public static function deleteCookie($name = NULL) {
        unset(Yii::app()->request->cookies[$name]);
        return true;
    }

    private static function _appendCookieArray($name, $value) {
        $cookieArray =  CJSON::decode(self::readCookie($name), true);
        $key = array_keys($value);

        if (is_array($cookieArray)) {
            if (array_key_exists($key[0], $cookieArray)) {
                unset($cookieArray[$key[0]]);
            }
        }

        $newCookieArray = array_merge($cookieArray, $value);
        Yii::app()->request->cookies[$name] = new CHttpCookie($name,  CJSON::encode($newCookieArray));
    }

}
