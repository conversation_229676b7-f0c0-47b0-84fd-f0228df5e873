<?php

class CurlCom extends MainCom
{

    const HTTP_POST = 'POST';

    public $ssl_verification = false;
    public $debug_mode = false;
    public $method, $request_url, $request_body, $response, $timeout = 600;

    /**
     * Custom CURLOPT settings.
     */
    public $curlopts = null;

    /**
     * Default useragent string to use.
     */
    public $useragent = 'OGMRequest/1.0';
    public $request_header = true;

    /**
     * The headers being sent in the request.
     */
    public $request_headers = array();

    /**
     * Default ERROR array to response.
     */
    private $error_array = array(
        'error_code' => 0,
        'error_message' => ''
    );

    private $tmp;

    public function readRSS($url, $reset = true)
    {
        $this->request_header = 0;

        return $this->sendRequest($url, '', 'GET', false, $reset);
    }

    public function sendGet($url, $data, $parse = false, $reset = true)
    {
        return $this->sendRequest($url, $data, 'GET', $parse, $reset);
    }

    public function sendPost($url, $data, $parse = false, $reset = true)
    {
        return $this->sendRequest($url, $data, 'POST', $parse, $reset);
    }

    public function sendRequest($url, $data, $method = 'POST', $parse = false, $reset = true)
    {
        $this->setRequestUrl($url);
        $this->setBody($data);
        $this->setMethod($method);

        $curl_handle = $this->prepRequest();
        $this->response = curl_exec($curl_handle);

        if ($this->response === false) {
            $debug = '';
            if ($this->debug_mode) {
                rewind($this->tmp);
                $debug = stream_get_contents($this->tmp);
            }
            $curl_errno = curl_errno($curl_handle);
            $this->setError($curl_errno, 'cURL resource: ' . (string)$curl_handle . '; cURL error: ' . curl_error($curl_handle) . ' (cURL error code ' . $curl_errno . ')' . $debug);
        }

        # This feature will reset next request string parameters.
        if ($reset) {
            $this->reset();
        }

        if ($parse) {
            $parsed_response = $this->processResponse($curl_handle, $this->response);
            curl_close($curl_handle);

            return $parsed_response;
        } else {
            curl_close($curl_handle);
        }

        return $this->response;
    }

    private function prepRequest()
    {
        $curl_handle = curl_init();

        $this->error_array = array(
            'error_code' => 0,
            'error_message' => ''
        );

        // Set default options.
        curl_setopt($curl_handle, CURLOPT_URL, $this->request_url);
        curl_setopt($curl_handle, CURLOPT_FRESH_CONNECT, false);    //  TRUE to force the use of a new connection instead of a cached one. 
        curl_setopt($curl_handle, CURLOPT_HEADER, $this->request_header);
        curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl_handle, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl_handle, CURLOPT_NOSIGNAL, true);
        curl_setopt($curl_handle, CURLOPT_USERAGENT, $this->useragent);

        // Verification of the SSL cert
        if ($this->ssl_verification) {
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($curl_handle, CURLOPT_SSLVERSION, 6);
        } else {
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, 0);   // should be false
            curl_setopt($curl_handle, CURLOPT_SSLVERSION, 6);
        }

        // Enable a proxy connection if requested.
        if ($this->proxy) {
            curl_setopt($curl_handle, CURLOPT_PROXY, $this->proxy);
        }

        // Debug mode
        if ($this->debug_mode === true) {
            $this->tmp = fopen('php://temp', 'w+');
            curl_setopt($curl_handle, CURLOPT_VERBOSE, true);
            curl_setopt($curl_handle, CURLOPT_STDERR, $this->tmp);
        }

        // Process custom headers
        if ($this->request_header) {
            if (isset($this->request_headers) && count($this->request_headers)) {
                $temp_headers = array();

                if (!array_key_exists('Expect', $this->request_headers)) {
                    $this->request_headers['Expect'] = '';
                }

                foreach ($this->request_headers as $k => $v) {
                    $temp_headers[] = $k . ': ' . $v;
                }

                curl_setopt($curl_handle, CURLOPT_HTTPHEADER, $temp_headers);
            }
        }

        switch ($this->method) {
            case self::HTTP_POST:
                curl_setopt($curl_handle, CURLOPT_POST, true);
                curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $this->request_body);
                break;
            default: // Assumed GET
                curl_setopt($curl_handle, CURLOPT_CUSTOMREQUEST, $this->method);
                if (!empty($this->request_body)) {
                    curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $this->request_body);
                }
                break;
        }

        // Merge in the CURLOPTs
        if (isset($this->curlopts) && sizeof($this->curlopts) > 0) {
            foreach ($this->curlopts as $k => $v) {
                curl_setopt($curl_handle, $k, $v);
            }
        }

        return $curl_handle;
    }

    private function processResponse($curl_handle, $response_raw)
    {
        $response_excl_header = explode("\r\n\r\n", $response_raw);
        $response = end($response_excl_header);

        if (isSerialize($response)) {
            $response = unserialize($response);
        } else {
            if (isJson($response)) {
                $response = CJSON::decode($response);
            }
        }

        return $response;
    }

    public function addHeader($key, $value)
    {
        $this->request_headers[$key] = $value;
        return $this;
    }

    public function setBody($request_param)
    {
        if (!empty($request_param)) {
            if (isJson($request_param)) {
                $this->addHeader('Accept', 'application/json');
                $this->addHeader('Content-Type', 'application/json');
            } else {
                if (is_array($request_param)) {
                    $request_param = http_build_query($request_param);
                }
            }

            $this->addHeader('Content-Length', strlen($request_param));
        }

        $this->request_body = $request_param;
    }

    public function setCurlopts($curlopts)
    {
        $this->curlopts = $curlopts;
    }

    private function setError($errorCode, $errorMessage = '')
    {
        $this->error_array = array(
            'request_url' => $this->request_url,
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        );
    }

    public function getError()
    {
        return $this->error_array;
    }

    private function setMethod($method)
    {
        $this->method = strtoupper($method);
    }

    private function setRequestUrl($url)
    {
        $this->request_url = $url;
    }

    // found none is using this
    public function setProxy($bool)
    {
        $this->proxy = $bool;
    }

    public function getProxy()
    {
        return $this->proxy;
    }

    private function reset()
    {
        $this->ssl_verification = false;
        $this->curlopts = null;
        $this->useragent = 'OGMRequest/1.0';
        $this->request_header = true;
        $this->request_headers = array();
        $this->debug_mode = false;
        if ($this->tmp) {
            fclose($this->tmp);
            $this->tmp = null;
        }
    }

}
