<?php

class CurrenciesCom extends MainCom
{
    public $customer_id,
        $default_checkout_currency = '',
        $checkout_currency = '',
        $currencies = null,
        $internal_currencies = array(),
        $decimal_places = null,
        $rebate_point = 0,
        $rebate_point_extra = 0,
        $rebate_point_formula = '',
        $currencies_list,
        $deprecated_currencies;

    public function _init($default_checkout_currency = null, $checkout_currency = null)
    {
        $this->default_checkout_currency = notNull($default_checkout_currency) ? $default_checkout_currency : $this->getRegional('default_currency');
        $this->checkout_currency = notNull($checkout_currency) ? $checkout_currency : $this->getRegional('currency');

        $this->getAllSellTypeCurrencies();
    }

    protected function getAllSellTypeCurrencies()
    {
        $cache_result = false;
        if (is_null($this->currencies)) {
            if (empty(Yii::app()->controller->module)) {
                $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/sales_type_currencies';
                $cache_result = Yii::app()->cache->get($cache_key);
            }
            if ($cache_result === false) {
                $cache_result = CurrenciesBase::model()->getAllByUsedFor();
                Yii::app()->cache->set($cache_key, $cache_result, 300);
            }
            list($this->currencies, $this->deprecated_currencies, $this->internal_currencies) = $cache_result;
        }
        return $this->currencies;
    }

    public function getAllCurrencies()
    {
        if (is_null($this->currencies_list)) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/currencies_list';
            $cache_result = Yii::app()->cache->get($cache_key);
            if ($cache_result === false) {
                $cache_result = CurrenciesBase::model()->getAllCurrencies();
                Yii::app()->cache->set($cache_key, $cache_result, 604800);
            }
            $this->currencies_list = $cache_result;
        }

        return $this->currencies_list;
    }

    public function setCustomerID($cid)
    {
        $this->customer_id = $cid;
    }

    public function advanceCurrencyConversion($number, $from_cur_type = '', $to_cur_type = '', $round = true, $type = 'buy')
    {
        $from_cur_type = $from_cur_type == '' ? ConfigurationCom::getValue('DEFAULT_CURRENCY') : $from_cur_type;
        $to_cur_type = $to_cur_type == '' ? ConfigurationCom::getValue('DEFAULT_CURRENCY') : $to_cur_type;
        $rate = $this->advanceCurrencyConversionRate($from_cur_type, $to_cur_type, $type);

        if ($round) {
            return number_format($number * $rate, is_null($this->decimal_places) ? $this->currencies[$to_cur_type]['decimal_places'] : $this->decimal_places, '.', '');
        } else {
            return $number * $rate;
        }
    }

    public function advanceCurrencyConversionRate($from_cur_type = '', $to_cur_type = '', $type = 'sell')
    {
        $from_cur_type = $from_cur_type == '' ? ConfigurationCom::getValue('DEFAULT_CURRENCY') : $from_cur_type;
        $to_cur_type = $to_cur_type == '' ? ConfigurationCom::getValue('DEFAULT_CURRENCY') : $to_cur_type;

        if ($from_cur_type == $to_cur_type) {
            return number_format(1, 8, '.', '');
        }

        $to_currencies = (isset($this->currencies[$to_cur_type]) ? $this->currencies[$to_cur_type] : $this->deprecated_currencies[$to_cur_type]);
        $from_currencies = (isset($this->currencies[$from_cur_type]) ? $this->currencies[$from_cur_type] : $this->deprecated_currencies[$from_cur_type]);
        if ($type == 'sell') {
            if ($to_cur_type == ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                $rate = ($from_currencies['sell_value'] > 0) ? bcdiv($to_currencies['buy_value'], $from_currencies['sell_value'], 8) : 0;
            } else {
                $rate = ($from_currencies['sell_value'] > 0) ? bcdiv($to_currencies['buy_value'], $from_currencies['value'], 8) : 0;
            }
        } elseif ($type == 'spot') {
            $rate = ($from_currencies['value'] > 0) ? bcdiv($to_currencies['value'], $from_currencies['value'], 8) : 0;
        } else {
            if ($to_cur_type == ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                $rate = ($from_currencies['sell_value'] > 0) ? bcdiv($to_currencies['sell_value'], $from_currencies['buy_value'], 8) : 0;
            } else {
                $rate = ($from_currencies['sell_value'] > 0) ? bcdiv($to_currencies['sell_value'], $from_currencies['value'], 8) : 0;
            }
        }

        return number_format($rate, 8, '.', '');
    }

    public function display_price($product_price_array, $customer_discount_array, $quantity = 1, $separator = '')
    {
        $cust_group_extra_rebate = 0;
        $price_convert = 0;
        $calculate_currency_value = true;
        $currency = $this->checkout_currency;
        $products_price = $product_price_array['price'];

        $customers_groups_rebate = 0;
        $customer_discount = 0;

        if ($customer_discount_array) {
            $customers_groups_discount = $customer_discount_array['group_discount']['cust_group_discount'];
            $customers_groups_rebate = abs($customer_discount_array['group_discount']['cust_group_rebate']);
            $customer_discount = $customer_discount_array['discount'] + $customers_groups_discount;
        }

        // Get percentage if price value <>
        if (isset($this->currencies[$product_price_array['base_cur']])) {
            if ($product_price_array['base_cur'] == $currency) {
                $calculate_currency_value = false;
            } else {
                if (isset($product_price_array['defined_price'][$currency])) {
                    $products_price = $product_price_array['defined_price'][$currency];
                    $calculate_currency_value = false;
                } else {
                    if ($currency == $this->default_checkout_currency) {
                        $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['buy_value'];
                    } else {
                        $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                    }
                }
            }
        } else {
            if ($currency == $this->default_checkout_currency) {
                $products_price = $products_price / $this->deprecated_currencies[$product_price_array['base_cur']]['buy_value'];
            } else {
                $products_price = $products_price / $this->deprecated_currencies[$product_price_array['base_cur']]['value'];
            }
        }

        if ($customer_discount >= 0) {
            $products_price = $products_price + $products_price * abs($customer_discount) / 100;
        } else {
            $products_price = $products_price - $products_price * abs($customer_discount) / 100;
        }

        $currency_obj = (isset($this->currencies[$currency]) ? $this->currencies[$currency] : $this->deprecated_currencies[$currency]);

        if ($calculate_currency_value) { // This price is in USD (Default Currency)
            $price_convert = number_format((($products_price * $quantity) * $currency_obj['sell_value'] / $currency_obj['buy_value']), 2, '.', '');
            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ') * ' . $currency_obj['sell_value'] . ' / ' . $currency_obj['buy_value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        } else {
            // use spot rate to have better selling price (business decision)
//            $price_convert = number_format(($products_price * $quantity) / $this->currencies[$currency]['value'], 2, '.', '');
            // 2016-03-09 revert and use buy rate
            $price_convert = number_format(($products_price * $quantity) / $currency_obj['buy_value'], 2, '.', '');

            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ') / ' . $currency_obj['buy_value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        }

        return $this->format($products_price * $quantity, $calculate_currency_value, '', '', 'sell', $separator);
    }

    public function format($number, $calculate_currency_value = true, $currency_type = '', $currency_value = '', $type = 'sell', $separator = '')
    {
        $this->getAllSellTypeCurrencies();
        $currency = $this->checkout_currency;
        if (empty($currency_type)) {
            $currency_type = $currency;
        }
        if (!empty($this->currencies[$currency_type]['symbol_left'])) {
            $this->currencies[$currency_type]['symbol_left'] = trim($this->currencies[$currency_type]['symbol_left']) . $separator;
        } else {
            if (!empty($this->currencies[$currency_type]['symbol_right'])) {
                $this->currencies[$currency_type]['symbol_right'] = $separator . trim($this->currencies[$currency_type]['symbol_right']);
            }
        }
        if ($calculate_currency_value == true) {
            if (notNull($currency_value)) {
                $rate = $currency_value;
            } else {
                $rate = (notNull($type) ? $this->currencies[$currency_type][$type . '_value'] : $this->currencies[$currency_type]['value']);
            }

            // added by subrat
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->tep_round($number * $rate, $this->currencies[$currency_type]['decimal_places']),
                        $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'],
                        $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->tep_round($number * $rate, $this->decimal_places), $this->decimal_places,
                        $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            }

            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if (($this->default_checkout_currency == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->tep_round($number, $this->currencies[$currency_type]['decimal_places']),
                        $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'],
                        $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->tep_round($number, $this->decimal_places), $this->decimal_places,
                        $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            }
        }

        return $format_string;
    }

    public function format_with_all_currencies($number, $calculate_currency_value = true, $currency_type = '', $currency_value = '', $type = 'sell', $separator = '')
    {
        $this->getAllSellTypeCurrencies();
        $currency = $this->checkout_currency;
        if (empty($currency_type)) {
            $currency_type = $currency;
        }
        
        $formatter = ($this->currencies[$currency_type] ?? $this->deprecated_currencies[$currency_type]);
        
        if (!empty($formatter['symbol_left'])) {
            $formatter['symbol_left'] = trim($formatter['symbol_left']) . $separator;
        } else {
            if (!empty($formatter['symbol_right'])) {
                $formatter['symbol_right'] = $separator . trim($formatter['symbol_right']);
            }
        }
        if ($calculate_currency_value == true) {
            if (notNull($currency_value)) {
                $rate = $currency_value;
            } else {
                $rate = (notNull($type) ? $formatter[$type . '_value'] : $formatter['value']);
            }

            // added by subrat
            if (is_null($this->decimal_places)) {
                $format_string = $formatter['symbol_left'] . number_format($this->tep_round($number * $rate, $formatter['decimal_places']),
                        $formatter['decimal_places'], $formatter['decimal_point'],
                        $formatter['thousands_point']) . $formatter['symbol_right'];
            } else {
                $format_string = $formatter['symbol_left'] . number_format($this->tep_round($number * $rate, $this->decimal_places), $this->decimal_places,
                        $formatter['decimal_point'], $formatter['thousands_point']) . $formatter['symbol_right'];
            }

            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if (($this->default_checkout_currency == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places)) {
                $format_string = $formatter['symbol_left'] . number_format($this->tep_round($number, $formatter['decimal_places']),
                        $formatter['decimal_places'], $formatter['decimal_point'],
                        $formatter['thousands_point']) . $formatter['symbol_right'];
            } else {
                $format_string = $formatter['symbol_left'] . number_format($this->tep_round($number, $this->decimal_places), $this->decimal_places,
                        $formatter['decimal_point'], $formatter['thousands_point']) . $formatter['symbol_right'];
            }
        }

        return $format_string;
    }
    
    public function display_format($number, $calculate_currency_value = true, $currency_type = '', $currency_value = '', $type = 'sell', $separator = '')
    {
        $this->getAllCurrencies();

        if (!empty($this->currencies_list[$currency_type]['symbol_left'])) {
            $this->currencies_list[$currency_type]['symbol_left'] = trim($this->currencies_list[$currency_type]['symbol_left']) . $separator;
        } elseif (!empty($this->currencies_list[$currency_type]['symbol_right'])) {
            $this->currencies_list[$currency_type]['symbol_right'] = $separator . trim($this->currencies_list[$currency_type]['symbol_right']);
        }
        if ($calculate_currency_value == true) {
            $rate = $currency_value;

            // added by subrat
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies_list[$currency_type]['symbol_left'] . number_format($this->tep_round($number * $rate, $this->currencies_list[$currency_type]['decimal_places']),
                        $this->currencies_list[$currency_type]['decimal_places'], $this->currencies_list[$currency_type]['decimal_point'],
                        $this->currencies_list[$currency_type]['thousands_point']) . $this->currencies_list[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies_list[$currency_type]['symbol_left'] . number_format($this->tep_round($number * $rate, $this->decimal_places), $this->decimal_places,
                        $this->currencies_list[$currency_type]['decimal_point'], $this->currencies_list[$currency_type]['thousands_point']) . $this->currencies_list[$currency_type]['symbol_right'];
            }

            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if (($this->default_checkout_currency == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies_list[$currency_type]['symbol_left'] . number_format($this->tep_round($number, $this->currencies_list[$currency_type]['decimal_places']),
                        $this->currencies_list[$currency_type]['decimal_places'], $this->currencies_list[$currency_type]['decimal_point'],
                        $this->currencies_list[$currency_type]['thousands_point']) . $this->currencies_list[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies_list[$currency_type]['symbol_left'] . number_format($this->tep_round($number, $this->decimal_places), $this->decimal_places,
                        $this->currencies_list[$currency_type]['decimal_point'], $this->currencies_list[$currency_type]['thousands_point']) . $this->currencies_list[$currency_type]['symbol_right'];
            }
        }

        return $format_string;
    }

    function display_price_original($product_price_array, $products_tax, $quantity = 1)
    {
        $currency = $this->checkout_currency;
        $products_price = $product_price_array['price'];
        $calculate_currency_value = true;

        // Get percentage if price value <>
        if (isset($this->currencies[$product_price_array['base_cur']])) {
            if ($product_price_array['base_cur'] == $currency) {
                $calculate_currency_value = false;
            } else {
                if (isset($product_price_array['defined_price'][$currency])) {
                    $products_price = $product_price_array['defined_price'][$currency];
                    $calculate_currency_value = false;
                } else {
                    if ($currency == $this->default_checkout_currency) {
                        // use spot rate to have better selling price (business decision)
//                    $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        // 2016-03-09 revert and use buy rate
                        $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['buy_value'];
                    } else {
                        $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                    }
                }
            }
        } else {
            if ($currency == $this->default_checkout_currency) {
                $products_price = $products_price / $this->deprecated_currencies[$product_price_array['base_cur']]['buy_value'];
            } else {
                $products_price = $products_price / $this->deprecated_currencies[$product_price_array['base_cur']]['value'];
            }
        }

        return $this->format($this->addTax($products_price, $products_tax) * $quantity, $calculate_currency_value, '', '', '');
    }

    private function addTax($price, $tax)
    {
        if ((ConfigurationCom::getValue('DISPLAY_PRICE_WITH_TAX') == 'true') && ($tax > 0)) {
            return $this->tep_round($price, $this->currencies[$this->default_checkout_currency]['decimal_places']) + $this->getTax($price, $tax);
        } else {
            return $this->tep_round($price, $this->currencies[$this->default_checkout_currency]['decimal_places']);
        }
    }

    // Calculates Tax rounding the result
    function getTax($price, $tax)
    {
        return $this->tep_round($price * $tax / 100, $this->currencies[$this->default_checkout_currency]['decimal_places']);
    }

    function getCodeById($cur_id)
    {
        return isset($this->internal_currencies[$cur_id]) ? $this->internal_currencies[$cur_id] : '';
    }

    function getIdByCode($cur_code)
    {
        return (int)array_search($cur_code, $this->internal_currencies);
    }

    public function getProductPrice($product_id, $products_price_array, $from_currency = '', $to_currency = '', $to_decimal = '', $thousand_sep = '', $rebate_info = array())
    {
        $to_decimal = $to_decimal == '' ? $this->currencies[$to_currency]['decimal_places'] : $to_decimal;
        $do_conversion = false;

        if ($from_currency == $to_currency) {
            $products_price = $products_price_array['price'];
        } else {
            if (isset($products_price_array['defined_price'][$to_currency])) {
                $products_price = $products_price_array['defined_price'][$to_currency];
            } else {
                $products_price = $products_price_array['price'];
                $do_conversion = true;
            }
        }

        // Merging store changes: Get the customer group discount
        $customer_discount = Yii::app()->customerCom->getDiscountInfo($product_id, 'total_discount');

        if ($customer_discount >= 0) {
            $products_price = $products_price + $products_price * abs($customer_discount) / 100;
        } else {
            $products_price = $products_price - $products_price * abs($customer_discount) / 100;
        }

        if ($do_conversion) {
            $currencies = (isset($this->currencies[$from_currency]) ? $this->currencies[$from_currency] : $this->deprecated_currencies[$from_currency]);
            if ($to_currency == ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                $products_price = $products_price / $currencies['buy_value'];
            } else {
                $products_price = $products_price / $currencies['value'];
            }
            $currencies = (isset($this->currencies[$to_currency]) ? $this->currencies[$to_currency] : $this->deprecated_currencies[$to_currency]);
            $products_price = $this->tep_round($products_price * $currencies['sell_value'], $currencies['decimal_places']);
        }

        return number_format($products_price, $to_decimal, '.', $thousand_sep);
    }

    public function getProductNoDiscountPrice($products_price, $from_currency, $to_currency, $products_price_array)
    {
        if ($from_currency == $to_currency) {
            // do nothing
        } else {
            if (isset($products_price_array['defined_price'][$to_currency])) {
                $products_price = $products_price_array['defined_price'][$to_currency];
            } else {
                # product used currency != checkout currency
                $currencies = (isset($this->currencies[$from_currency]) ? $this->currencies[$from_currency] : $this->deprecated_currencies[$from_currency]);
                if ($to_currency == ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                    $products_price = $products_price / $currencies['buy_value'];
                } else {
                    $products_price = $products_price / $currencies['value'];
                }

                $currencies = (isset($this->currencies[$to_currency]) ? $this->currencies[$to_currency] : $this->deprecated_currencies[$to_currency]);

                $products_price = $products_price * $currencies['sell_value'];
            }
        }

        return $products_price;
    }

    public function getInfo($currency_code, $key, $default = '')
    {
        return isset($this->currencies[$currency_code][$key]) ? $this->currencies[$currency_code][$key] : $default;
    }

    function getValue($code, $type = 'sell')
    {
        if ($type != '') {
            if (isset($this->currencies[$code])) {
                return $this->currencies[$code][$type . '_value'];
            } else {
                return $this->deprecated_currencies[$code][$type . '_value'];
            }
        } else {
            return $this->currencies[$code]['value'];
        }
    }

    public function rebatePoint($product_id, $product_price_array, $from_currency = '', $to_currency = '', $quantity, $payment_methods_id = 0, $customer_id = 0)
    {
        $calculate_currency_value = true;
        $cust_group_extra_rebate = 0;

        $products_price = $product_price_array['price'];

        if ($from_currency == $to_currency) {
            $calculate_currency_value = false;
        } else {
            if (isset($product_price_array['defined_price'][$to_currency])) {
                $products_price = $product_price_array['defined_price'][$to_currency];
                $calculate_currency_value = false;
            } else {
                $currencies = (isset($this->currencies[$from_currency]) ? $this->currencies[$from_currency] : $this->deprecated_currencies[$from_currency]);
                if ($to_currency == ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                    $products_price = $products_price / $currencies['buy_value'];
                } else {
                    $products_price = $products_price / $currencies['value'];
                }
            }
        }

        // Merging store changes: Get the customer group discount
        $customer_discount = Yii::app()->customerCom->getDiscountInfo($product_id, 'total_discount');

        if ($customer_discount >= 0) {
            $products_price = $products_price + $products_price * abs($customer_discount) / 100;
        } else {
            $products_price = $products_price - $products_price * abs($customer_discount) / 100;
        }

        $customers_groups_discount_id = Yii::app()->customerCom->getDiscountInfo($product_id, 'cust_group_discount_id');
        $customers_groups_rebate = Yii::app()->customerCom->getDiscountInfo($product_id, 'cust_group_rebate');

        if ($payment_methods_id > 0 && $customers_groups_discount_id > 0) {
            if ($customers_groups_extra_op = CustomersGroupsExtraOpBase::model()->getTplIDsByMatchingAgainstScore($customers_groups_discount_id, $payment_methods_id, $to_currency)) {
                $cust_group_extra_rebate = abs($customers_groups_extra_op);
            }
        }

        $currencies = (isset($this->currencies[$to_currency]) ? $this->currencies[$to_currency] : $this->deprecated_currencies[$to_currency]);

        if ($calculate_currency_value) { // This price is in USD (Default Currency)
            $price_convert = number_format((($products_price * $quantity) * $currencies['sell_value'] / $currencies['buy_value']), 2, '.', '');

            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ')' . ' * ' . $currencies['sell_value'] . ' / ' . $currencies['buy_value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        } else {
            $price_convert = number_format(($products_price * $quantity) / $currencies['buy_value'], 2, '.', '');

            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ') / ' . $currencies['buy_value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        }
    }

    public function reset()
    {
        $this->rebate_point = 0;
        $this->rebate_point_extra = 0;
        $this->rebate_point_formula = '';
        $this->decimal_places = null;
    }

    private function tep_round($number, $precision)
    {
        return number_format($number, $precision, '.', '');
    }

    public function addSeparator($value, $separator = ' ')
    {
        preg_match('/^(\&#[0-9]+\;|[^;]*?)([0-9\.\,]+)(.*?)$/', strip_tags($value), $matches);
        return trim($matches[1] . $separator . $matches[2] . $separator . $matches[3]);
    }

    public function parseStringToNumber($currency, $value){
        $formatter = ($this->currencies[$currency] ?? $this->deprecated_currencies[$currency]);
        $value = str_replace($formatter['symbol_left'], '', $value);
        $value = str_replace($formatter['symbol_right'], '', $value);
        $value = trim(strip_tags($value));
        $value = str_replace($formatter['thousands_point'], '', $value);
        return (float)str_replace($formatter['decimal_point'], '.', $value);
    }

    public function reformat($currency, $value)
    {
        $value = $this->parseStringToNumber($currency, $value);
        return $this->display_format($value, false, $currency, '', 'sell', ' ');
    }

    // Rounding for small value currency
    // Glendon : Do rounding when currency rate > 10000
    public function rounding($currency, $value, $rate)
    {
        if ($rate > 10000) {
            return $this->tep_round($value * $rate, $this->currencies[$currency]['decimal_places']);
        }
        return $value * $rate;
    }

    public function roundByCurrency($currency, $amount)
    {
        $formatter = ($this->currencies[$currency] ?? $this->deprecated_currencies[$currency]);
        return $this->tep_round($amount, $formatter['decimal_places']);
    }

}