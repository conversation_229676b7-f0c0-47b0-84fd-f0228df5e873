<?php

class CustomerCom extends MainCom
{

    protected
        //            $oauth_obj,
        $customer_id,
        $customer_groups_id,
        $discount_info_array = array(),
        $discount,
        $is_cb_customer = null,
        $is_active_customer = null,
        $is_from_sanction_country;

    public function __construct()
    {
        //        $this->oauth_obj = new OAuthClientCom();
        //        $this->oauth_obj->setReusableToken($session_token_prefix = 'RCI');    // turn to <PERSON>alse will enabled request new token for each request.
        //        $this->oauth_obj->setScope('request_customer_info');
        //        $this->oauth_obj->setClientInfo(Yii::app()->params['OAUTH_CLIENT']['DEFAULT_CLIENT'], Yii::app()->params['OAUTH_DOMAIN']);
    }

    public function _init($data_array = array())
    {
        $this->customer_id = isset($data_array['customer_id']) && notNull($data_array['customer_id']) ? $data_array['customer_id'] : $this->getUser('id');
        $this->customer_groups_id = isset($data_array['customer_groups_id']) && notNull($data_array['customer_groups_id']) ? $data_array['customer_groups_id'] : $this->getUser('customers_groups_id');
    }

    public function setCustomerID($id)
    {
        if (notNull($id)) {
            $this->customer_id = $id;
        }
    }

    public function isCbCustomer()
    {
        $customer_id = (isset($GLOBALS['cart']['customer']['id']) ? $GLOBALS['cart']['customer']['id'] : Yii::app()->user->id);
        if (is_null($this->is_cb_customer)) {
            if (empty($customer_id)) {
                $this->is_cb_customer = false;
            } else {
                $this->is_cb_customer = (new Customers())->isCbCustomer($customer_id);
            }
        }
        return $this->is_cb_customer;
    }

    public function isActiveCustomer()
    {
        $customer_id = (isset($GLOBALS['cart']['customer']['id']) ? $GLOBALS['cart']['customer']['id'] : Yii::app()->user->id);
        if (is_null($this->is_active_customer)) {
            if (empty($customer_id)) {
                $this->is_active_customer = false;
            } else {
                $this->is_active_customer = (new Customers())->isActiveCustomer($customer_id);
            }
        }
        return $this->is_active_customer;
    }

    public function isPhoneSanctionCountry($ignore_cache = false)
    {
        if (is_null($this->is_from_sanction_country)) {
            $customer_id = (isset($GLOBALS['cart']['customer']['id']) ? $GLOBALS['cart']['customer']['id'] : Yii::app()->user->id);
            $this->is_from_sanction_country = (new Customers())->isSanctionCountry($customer_id, $ignore_cache);
        }
        return $this->is_from_sanction_country;
    }

    public function getCustomerID($customer_id = 0)
    {
        if (empty($customer_id)) {
            $customer_id = !empty($this->customer_id) ? $this->customer_id : Yii::app()->user->id;
        }

        return $customer_id;
    }

    public function getCustomerInfoVerifyStatus($cid, $info_value, $req_token = false, $type = 'email')
    {
        $return_array = array();

        $extraParams = array(
            'info_value' => $info_value,
            'req_token' => $req_token,
            'type' => $type
        );

        $api_response = self::_callAPI('profile/checkInfoVerified', $cid, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    /*
     * customers_firstname
     * customers_lastname
     * customers_groups_id
     * customers_groups_name
     * customers_email_address
     * customers_login_ip
     * customers_login_timestamp
     * customers_default_address_id
     */

    public function getValue($key, $default = '')
    {
        return $this->getUser($key, $default);
    }

    public function getDiscountInfo($product_id = null, $field = 'discount')
    {
        $return_int = 0;

        $this->getDiscount();

        if ($product_id) {
            $this->getGroupDiscountByProductID($product_id);

            if ($field == 'total_discount') {
                $return_int = $this->discount[$this->customer_id]['discount'] + $this->discount[$this->customer_id]['group_discount'][$product_id]['cust_group_discount'];
            } else {
                $return_int = $this->discount[$this->customer_id]['group_discount'][$product_id][$field];
            }
        } else {
            $return_int = isset($this->discount[$this->customer_id][$field]) ? $this->discount[$this->customer_id][$field] : $return_int;
        }

        return $return_int;
    }

    public function getDiscount($customer_id = null)
    {
        $customer_id = notNull($customer_id) ? $customer_id : $this->customer_id;

        if (!isset($this->discount[$customer_id]['discount'])) {
            $this->discount[$customer_id]['discount'] = CustomersBase::model()->getDiscount($customer_id);
        }

        return $this->discount[$customer_id]['discount'];
    }

    public function getGroupDiscountByProductID($product_id, $category_info = array(), $customer_groups_id = null, $customer_id = null)
    {
        $customer_id = notNull($customer_id) ? $customer_id : $this->customer_id;
        $customer_groups_id = $customer_id > 0 ? (notNull($customer_groups_id) ? $customer_groups_id : $this->customer_groups_id) : '1';

        if (!isset($this->discount[$customer_id]['group_discount'][$product_id])) {
            $cust_group_discount = 0;
            $cust_group_rebate = 0;
            $cust_group_discount_id = 0;

            if (isset($category_info['cpath'])) {
                $cat_path = '0_' . $category_info['cpath'];
                $cat_path_array = explode('_', $cat_path);
            } else {
                $cid = ProductsToCategoriesBase::model()->getCategoryID($product_id);
                $cat_path = CategoriesBase::model()->getCPathByCategoryID($cid);

                $cat_path_array = explode('_', $cat_path);
                $cat_path_array = array_merge(array(0), $cat_path_array);
            }

            $ttl_itm = count($cat_path_array) - 1;

            # Discount Rules method
            if ($rule_ids = CategoriesDiscountListBase::model()->getDiscountRuleByCategoryIDs($cat_path_array)) {
                for ($i = $ttl_itm; $i >= 0; $i--) {
                    if (isset($rule_ids[$cat_path_array[$i]])) {
                        $rule_id = $rule_ids[$cat_path_array[$i]];

                        #key:categories_discount_group_rules/rule_id/xxx/customers_groups_id/xxx
                        $cache_key = CategoriesDiscountGroupRulesBase::model()->tableName() . '/rule_id/' . $rule_id . '/customers_groups_id/' . $customer_groups_id;
                        $cache_result = Yii::app()->cache->get($cache_key);

                        if ($cache_result !== false) {
                            list($cust_group_discount, $cust_group_rebate, $cust_group_discount_id) = $cache_result;
                        } else {
                            if ($grp_discount_row = CategoriesDiscountGroupRulesBase::model()->getDiscountInfoByRuleIDByGroupID($rule_id, $customer_groups_id)) {
                                $cust_group_discount = $grp_discount_row['cdgr_discount'];
                                $cust_group_rebate = abs($grp_discount_row['cdgr_wor']);
                                $cust_group_discount_id = 0; // do not support extra OP
                            }

                            Yii::app()->cache->set($cache_key, array($cust_group_discount, $cust_group_rebate, $cust_group_discount_id), 86400);
                        }
                        break;
                    }
                }
            } else {
                # Old Discount method
                for ($i = $ttl_itm; $i >= 0; $i--) {
                    $grp_discount_row = CustomersGroupsDiscountBase::model()->getDiscountInfoByCategoryIDByGroupID($cat_path_array[$i], $customer_groups_id);

                    if ($grp_discount_row != array()) {
                        $cust_group_discount = $grp_discount_row['customers_groups_discount'];
                        $cust_group_rebate = abs($grp_discount_row['customers_groups_rebate']);
                        $cust_group_discount_id = $grp_discount_row['customers_groups_discount_id'];
                        break;
                    }
                }
            }

            $this->discount[$customer_id]['group_discount'][$product_id] = array(
                'cust_group_discount' => $cust_group_discount,
                'cust_group_rebate' => $cust_group_rebate,
                'cust_group_discount_id' => $cust_group_discount_id
            );
        }

        return $this->discount[$customer_id]['group_discount'][$product_id];
    }

    public function getZoneID($address_id = null, $customer_id = null)
    {
        $ret_int = null;
        $address_id = notNull($address_id) ? $address_id : $this->getValue('customers_default_address_id');
        $customer_id = notNull($customer_id) ? $customer_id : $this->customer_id;

        if ($customer_id && $address_id != '') {
            $return_int = AddressBookBase::model()->getZoneIDByIDByCustomerID($address_id, $customer_id);
        } else {
            $return_int = ConfigurationCom::getValue('STORE_ZONE');
        }

        return $return_int;
    }

    public static function getCustomerData($customer_id, $requestField = 'firstname,lastname,email')
    {
        $return_array = array();

        if ($customer_array = self::_callAPI('profile/userInfo', $customer_id, $requestField)) {
            if (isset($customer_array['status']) && $customer_array['status']) {
                $return_array = $customer_array['result'];
            }
        }

        return $return_array;
    }

    public function getCustomerData2($with_address_book = 0, $customer_id = 0)
    {
        $data_array = array();
        $customer_id = $this->getCustomerID($customer_id);

        $extraParams = array(
            'address_book_flag' => $with_address_book    // 1: get address book info as well
        );

        $api_response = self::_callAPI('profile/getCustomerData', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $data_array = $api_response['result'];
        }

        return $data_array;
    }

    public static function getCustomerSC($customer_id)
    {
        $responseArray = array(
            'error' => false,
            'val' => '-',
        );

        if (Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'] == 0) {
            $responseArray['val'] = Yii::t('storeCredit', 'TEXT_SC_OFFLINE');
        } else {
            $scAccount = StoreCreditCom::storeCreditAccount();
            if (!empty($scAccount)) {
                if (isset($scAccount['result']['Items'])) {
                    $result = $scAccount['result']['Items'];
                    if ($result['user_status'] == 'ACTIVE') {
                        $responseArray['val'] = Yii::app()->currency->format_with_all_currencies($result['balance'], false, $result['currency'], '', 'sell', ' ');
                        $responseArray['balance'] = $result['balance'];
                        $responseArray['currency'] = $result['currency'];
                    } else {
                        $responseArray['val'] = Yii::t('storeCredit', 'TEXT_SC_OFFLINE');
                    }
                } elseif (isset($scAccount['error'])) {
                    if ($scAccount['error']['code'] == '14') {
                        $responseArray['error'] = 404;
                        $responseArray['val'] = Yii::app()->currency->format(0, false, Yii::app()->session['currency'], '', 'sell', ' ');
                    }
                }
            }
        }
        return $responseArray;
    }

    public static function getCustomerWor($customer_id)
    {
        if (Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'] == 0) {
            return $responseArray = array(
                'error' => false,
                'val' => Yii::t('storeCredit', 'TEXT_SC_OFFLINE'),
            );
        }

        $worAmount = 0;
        $responseArray = array(
            'error' => false,
            'val' => '-',
        );

        $wor = self::_callAPI('account/balance', $customer_id, "token");

        if (isset($wor['status']) && $wor['status'] === true) {
            if (isset($wor['code']) && $wor['code'] == 200) {
                if (isset($wor['result']) && !empty($wor['result'])) {
                    foreach ($wor['result'] as $value) {
                        if (isset($value['amount'])) {
                            $worAmount += $value['amount'];
                        }
                    }
                }
                $responseArray['val'] = number_format($worAmount);
            } else {
                $responseArray['val'] = 0;
            }
        } else {
            if (isset($wor['error']) && !empty($wor['error'])) {
                $responseArray['error'] = true;
            } else {
                $responseArray['val'] = 0;
            }
        }

        return $responseArray;
    }

    public static function getCustomerGroupID($id)
    {
        $return_int = 0;

        $select = " SELECT customers_groups_id
                    FROM " . CustomersBase::model()->tableName() . "
                    WHERE customers_id = :customers_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":customers_id", $id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_int = $value;
        }

        return $return_int;
    }

    public static function getCustomerGroupExtraSCByID($id)
    {
        $return_int = 0;

        $select = " SELECT customers_groups_extra_sc
                    FROM " . CustomersGroupsBase::model()->tableName() . "
                    WHERE customers_groups_id = :customers_groups_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":customers_groups_id", $id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_int = $value;
        }

        return $return_int;
    }

    protected static function _callAPI($action, $customer_id, $requestField = "", $extraParams = array())
    {
        $url = Yii::app()->params['SHASSO_CONFIG']['API_URI'] . "/" . $action;
        $params = array(
            'merchant' => Yii::app()->params['SHASSO_CONFIG']['MERCHANT_API_ACC'],
            'signature' => md5($customer_id . '|' . Yii::app()->params['SHASSO_CONFIG']['MERCHANT_API_SECRET']),
            'cid' => $customer_id,
            'request' => $requestField,
            'extra' => $extraParams,
        );

        $proxy_setting_holder = Yii::app()->curl->getProxy();
        Yii::app()->curl->setProxy(false);
        Yii::app()->curl->debug_mode = true;
        $response = Yii::app()->curl->sendPost($url, json_encode($params), true);
        Yii::app()->curl->setProxy($proxy_setting_holder);

        $error = Yii::app()->curl->getError();

        if (isset($error['error_code']) && $error['error_code']) {
            self::reportError('CustomerCom:_callAPI', array('error' => $error));
        }

        unset($error);

        return $response;
    }

}
