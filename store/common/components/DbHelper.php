<?php

class DbHelper {
    public static function insertIgnore(string $table_name, array $params) {
        try {
            $column_names = array_keys($params);

            $insert_column_names = [];
            $param_column_names = [];
            foreach($column_names as $column_name) {
                $insert_column_names[] = "`$column_name`";
                $param_column_names[] = ":$column_name";
                $params[":$column_name"] = $params[$column_name];
                unset($params[$column_name]);
            }

            $insert_column_names = "(" . implode(', ', $insert_column_names). ")";
            $param_column_names = "(" . implode(', ', $param_column_names). ")";

            $sql = "INSERT IGNORE INTO `$table_name` " . $insert_column_names . " VALUES ". $param_column_names;

            Yii::app()->db->createCommand($sql)->bindValues($params)->execute();
        } catch (\Exception $e) {
            $errors = [
                [
                    'color' => 'errors',
                    'text' => json_encode($e->getMessage())
                ]
            ];
            Yii::app()->slack->send('DbHelper.php: Insert ignore error', $errors, 'DEV_DEBUG');
        }

    }
}