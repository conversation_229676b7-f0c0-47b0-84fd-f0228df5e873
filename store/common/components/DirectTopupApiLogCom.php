<?php

class DirectTopupApiLogCom extends MainCom {

    var $api_log_id;

    function __construct($pass_action, $request_log, $extra_param = '') {
        $this->api_log_id = 0;

        $api_log_data_sql = array();
        $api_log_data_sql['ip_address'] = getIPAddress();
        $api_log_data_sql['action'] = $pass_action;
        $api_log_data_sql['request_log'] = $request_log;
        $api_log_data_sql['request_start'] = new CDbExpression('NOW()');

        if (notNull($extra_param)) {
            $api_log_data_sql = array_merge($api_log_data_sql, $extra_param);
        }
        
        $this->api_log_id = ApiLogBase::model()->saveNewRecord($api_log_data_sql, true);
    }

    function end_log($pass_result_code, $response_log, $extra_param) {
        if ((int) $this->api_log_id == 0)
            return;
        
        $api_log_data_sql = array();
        $api_log_data_sql['result_code'] = $pass_result_code;
        $api_log_data_sql['response_log'] = $response_log;
        $api_log_data_sql['request_end'] = new CDbExpression('NOW()');

        if (notNull($extra_param)) {
            $api_log_data_sql = array_merge($api_log_data_sql, $extra_param);
        }
        
        ApiLogBase::model()->updateByPk($this->api_log_id, $api_log_data_sql);
    }

}