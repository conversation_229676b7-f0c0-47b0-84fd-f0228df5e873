<?php

class DirectTopupCom extends PublishersCom {

    public $language_id,
            $default_language_id;

    function __construct($data_array = array()) {
        parent::__construct($data_array);

        $this->language_id = isset($data_array['language_id']) ? $data_array['language_id'] : $this->getRegional('language_id');
        $this->default_language_id = isset($data_array['default_language_id']) ? $data_array['default_language_id'] : $this->getRegional('default_language_id');
    }

    function get_product_publisher($pass_id) {
        $publishers_id = 0;
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/publishers_products/' . (int) $pass_id . '/publisher';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $publishers_id = $cache_result;
        } else {
            $select = " SELECT pg.publishers_id
                        FROM " . PublishersProductsBase::model()->tableName() . " AS pp 
                        INNER JOIN " . PublishersGamesBase::model()->tableName() . " AS pg
                            ON pp.publishers_games_id = pg.publishers_games_id
                        WHERE pp.products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $pass_id, PDO::PARAM_INT);
            if ($value = $command->queryScalar()) {
                $publishers_id = $value;
            }

            Yii::app()->cache->set($cache_key, $publishers_id, 86400);
        }

        return $publishers_id;
    }

    function get_product_info($pass_id, $info_key = 'GAME') {
        $return_info = null;
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/product/' . (int) $pass_id . '/' . $info_key;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_info = $cache_result;
        } else {
            $select = " SELECT tui.top_up_info_value 
                        FROM " . TopUpInfoBase::model()->tableName() . " AS tui  
                        WHERE tui.products_id = :products_id
                            AND tui.top_up_info_key = :top_up_info_key
                            AND tui.top_up_info_type_id = '1'";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $pass_id, PDO::PARAM_INT);
            $command->bindParam(":top_up_info_key", $info_key, PDO::PARAM_STR);

            if ($value = $command->queryScalar()) {
                $return_info = $value;
            }

            Yii::app()->cache->set($cache_key, $return_info, 86400);
        }
        
        return $return_info;
    }

    function get_supported_direct_top_up_product() {
        $supported_games = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/supported_direct_top_up_product/products_id';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $supported_games = $cache_result;
        } else {
            $select = " SELECT pp.products_id, pg.publishers_id 
                        FROM " . PublishersProductsBase::model()->tableName() . " AS pp 
                        INNER JOIN " . PublishersGamesBase::model()->tableName() . " AS pg 
                            ON pg.publishers_games_id = pp.publishers_games_id
                        GROUP BY pp.products_id";
            $command = Yii::app()->db->createCommand($select);

            if ($dataset = $command->queryAll()) {
                foreach ($dataset as $row) {
                    $supported_games[$row['products_id']] = $row['publishers_id'];
                }
            }

            Yii::app()->cache->set($cache_key, $supported_games, 86400);
        }
        
        return $supported_games;
    }

    function get_servers($products_id) {
        $servers_array = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersGamesBase::model()->tableName() . '/products_id/' . $products_id . '/publishers_server';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $servers_array = $cache_result;
        } else {
            $select = " SELECT pg.publishers_server
                        FROM " . PublishersProductsBase::model()->tableName() . " AS pp 
                        INNER JOIN " . PublishersGamesBase::model()->tableName() . " AS pg
                            ON pp.publishers_games_id = pg.publishers_games_id
                        WHERE pp.products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $servers_array = json_decode($value, true);
                Yii::app()->cache->set($cache_key, $servers_array, 86400);
            }
        }
        
        return $servers_array;
    }

    function conf_get_servers($products_id) {
        $sel_servers_array = array();
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersGamesBase::model()->tableName() . '/products_id/' . $products_id . '/publishers_server';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $servers_array = $cache_result;
        } else {
            $select = " SELECT pg.publishers_server
                        FROM " . PublishersProductsBase::model()->tableName() . " AS pp 
                        INNER JOIN " . PublishersGamesBase::model()->tableName() . " AS pg
                            ON pp.publishers_games_id = pg.publishers_games_id
                        WHERE pp.products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $servers_array = json_decode($value, true);
                Yii::app()->cache->set($cache_key, $servers_array, 86400);
            }
        }
        
        if (isset($servers_array['servers']) && count($servers_array['servers'])) {
            foreach ($servers_array['servers'] as $servers_id_loop => $servers_data_loop) {
                $sel_servers_array[] = array(
                    'id' => $servers_id_loop, 
                    'text' => $servers_data_loop
                );
            }
        }
        
        return $sel_servers_array;
    }

    function check_is_supported_by_direct_top_up($products_id) {
        $publishers_id = 0;
        
        #key:top_up_info/products_id/xxx/is_supported_by_direct_top_up/
        $cache_key = TopUpInfoBase::model()->tableName() . '/products_id/' . $products_id . '/is_supported_by_direct_top_up/';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $publishers_id = $cache_result;
        } else {
            $m_ot = ProductsBase::model()->find(array(
                'condition' => 'products_id = :products_id',
                'params' => array(
                    ':products_id' => $products_id
                )
            ));

            if ($m_ot) {
                if ($m_ot->products_bundle == 'yes' || $m_ot->products_bundle_dynamic == 'yes') {
                    $select = " SELECT pg.publishers_id
                                FROM " . PublishersGamesBase::model()->tableName() . " AS pg
                                INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp 
                                    ON pg.publishers_games_id = pp.publishers_games_id
                                INNER JOIN " . ProductsDeliveryInfoBase::model()->tableName() . " AS pdi
                                    ON pdi.products_id = pp.products_id 
                                INNER JOIN " . ProductsBundlesBase::model()->tableName() . " AS pb
                                    ON pp.products_id=pb.subproduct_id
                                WHERE pb.bundle_id = :bundle_id
                                    AND pdi.products_delivery_mode_id = '6'
                                LIMIT 1";
                    $command = Yii::app()->db->createCommand($select);
                    $command->bindParam(":bundle_id", $products_id, PDO::PARAM_INT);

                    if ($value = $command->queryScalar()) {
                        $publishers_id = $value;
                    }
                } else {
                    $select = " SELECT pg.publishers_id
                                FROM " . PublishersGamesBase::model()->tableName() . " AS pg
                                INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp 
                                    ON pg.publishers_games_id = pp.publishers_games_id
                                INNER JOIN " . ProductsDeliveryInfoBase::model()->tableName() . " AS pdi
                                    ON pdi.products_id = pp.products_id
                                WHERE pdi.products_id = :products_id
                                    AND pdi.products_delivery_mode_id = '6'
                                LIMIT 1";
                    $command = Yii::app()->db->createCommand($select);
                    $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

                    if ($value = $command->queryScalar()) {
                        $publishers_id = $value;
                    }
                }
            }
            
            Yii::app()->cache->set($cache_key, $publishers_id, 86400);
        }

        return $publishers_id;
    }

    function get_game_input($products_id, $language_id = NULL, $default_languages_id = NULL) {
        $game_input_array = array();
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/products_id/' . $products_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $game_input_array = $cache_result;
        } else {
            $select = " SELECT tui.top_up_info_id, tuil.top_up_info_display, tui.top_up_info_key, tui.use_function, tui.set_function 
                        FROM " . TopUpInfoBase::model()->tableName() . " AS tui 
                        INNER JOIN " . TopUpInfoLangBase::model()->tableName() . " AS tuil
                            ON tui.top_up_info_id = tuil.top_up_info_id
                        WHERE tui.products_id  = :products_id
                            AND tui.top_up_info_type_id = 2
                            AND IF(tuil.languages_id = :languages_id , 1, IF (( SELECT COUNT(tuil2.top_up_info_id) > 0 
                                 FROM " . TopUpInfoLangBase::model()->tableName() . " AS tuil2
                                 WHERE tuil2.top_up_info_id = tui.top_up_info_id
                                    AND tuil2.languages_id = :languages_id), 0, tuil.languages_id = :default_languages_id))
                        ORDER BY tui.sort_order";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
            $command->bindParam(":languages_id", $language_id, PDO::PARAM_INT);
            $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_INT);

            if ($dataset = $command->queryAll()) {
                foreach ($dataset as $row) {
                    $game_input_array[$row['top_up_info_key']] = $row;
                }
            }
            
            Yii::app()->cache->set($cache_key, $game_input_array, 86400);
        }
        
        return $game_input_array;
    }

    /*
     * wee siong comment : 2015-03-11 [Checked, it's not in used in V2]
     */
    function get_admin_game_input($products_id, $languages_id) {
        $game_input_array = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/products_id/' . $products_id . '/language/' . $languages_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $game_input_array = $cache_result;
        } else {
            $select = " SELECT tui.top_up_info_id, tuil.top_up_info_display, tui.top_up_info_key, tui.use_function, tui.set_function 
                        FROM " . TopUpInfoBase::model()->tableName() . " AS tui 
                        INNER JOIN " . TopUpInfoLangBase::model()->tableName() . " AS tuil
                            ON tui.top_up_info_id = tuil.top_up_info_id
                        WHERE tui.products_id  = :products_id
                            AND tui.top_up_info_type_id = 2
                            AND tuil.languages_id = 1 
                        ORDER BY tui.sort_order";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($dataset = $command->queryAll()) {
                foreach ($dataset as $row) {
                    $game_input_array[$row['top_up_info_key']] = $row;
                }
            }
            
            Yii::app()->cache->set($cache_key, $game_input_array, 86400);
        }
        
        return $game_input_array;
    }

    function get_top_up_info($products_id) {
        $top_up_info_array = array();

        #key:top_up_info/products_id/xxx
        $cache_key = TopUpInfoBase::model()->tableName() . '/products_id/' . $products_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $top_up_info_array = $cache_result;
        } else {
            $select = " SELECT tui.* 
                        FROM " . TopUpInfoBase::model()->tableName() . " AS tui 
                        WHERE tui.products_id  = :products_id
                        ORDER BY tui.sort_order";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($dataset = $command->queryAll()) {
                foreach ($dataset as $row) {
                    $top_up_info_array[$row['top_up_info_key']] = $row;
                }
            }
            
            Yii::app()->cache->set($cache_key, $top_up_info_array, 86400);
        }
        
        return $top_up_info_array;
    }

    function get_customer_input_value($orders_products_id) {
        $customer_input_value = array();

        $select = " SELECT ctui.top_up_info_id, ctui.top_up_value 
                    FROM " . CustomersTopUpInfoBase::model()->tableName() . " AS ctui 
                    WHERE ctui.orders_products_id  = :orders_products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":orders_products_id", $orders_products_id, PDO::PARAM_INT);

        if ($dataset = $command->queryAll()) {
            foreach ($dataset as $row) {
                $customer_input_value[$row['top_up_info_id']] = $row['top_up_value'];
            }
        }

        return $customer_input_value;
    }

    function add_customers_top_up_info($orders_products_id, $extra_param) {
        $added_info = 0;

        if (count($extra_param)) {
            $top_up_info_key_array = array();

            $criteria = new CDbCriteria;
            $criteria->select = 'top_up_info_id, top_up_info_key';
            $criteria->addInCondition('top_up_info_key', array_keys($extra_param));

            if ($results = $this->model()->findAll($criteria)) {
                foreach ($results as $result) {
                    $top_up_info_key_array[$result->top_up_info_key] = $result->top_up_info_id;
                }
            }

            foreach ($extra_param as $extra_key_loop => $extra_data_loop) {
                $customers_top_up_info_data_sql = array(
                    'top_up_info_id' => (int) $top_up_info_key_array[$extra_key_loop],
                    'orders_products_id' => $orders_products_id,
                    'top_up_value' => $extra_data_loop
                );

                CustomersTopUpInfoBase::model()->saveNewRecord($customers_top_up_info_data_sql);
                $added_info++;
            }
        }
        return $added_info;
    }

    function add_top_up($orders_products_id, $publishers_id) {
        $return_array = array();
        $return_array['top_up_id'] = 0;

        $select = " SELECT orders_products_id
                    FROM " . OrdersTopUpBase::model()->tableName() . " 
                    WHERE orders_products_id = :orders_products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":orders_products_id", $orders_products_id, PDO::PARAM_INT);

        if ($value = $command->queryScalar()) {
            $return_array['top_up_id'] = $value;
        } else {
            $top_up_data_sql = array(
                'orders_products_id' => (int) $orders_products_id,
                'top_up_status' => 1,
                'top_up_process_flag' => 0,
                'publishers_id' => (int) $publishers_id,
                'top_up_created_date' => new CDbExpression('NOW()')
            );
            $return_array['top_up_id'] = OrdersTopUpBase::model()->saveNewRecord($top_up_data_sql, true);
        }

        return $return_array;
    }

    /* function to check OffGamers' orders status */

    function get_customer_top_up_status($orders_products_id) {
        $return_status = 0;

        $select = " SELECT top_up_status
                    FROM " . OrdersTopUpBase::model()->tableName() . " 
                    WHERE orders_products_id = :orders_products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":orders_products_id", $orders_products_id, PDO::PARAM_INT);

        if ($value = $command->queryScalar()) {
            $return_status = $value;
        }

        return $return_status;
    }

    /* function to check customer top-up info */

    function get_customer_top_up_info($orders_products_id) {
        $customer_top_up_info_array = array();

        $select = " SELECT otu.*  
                    FROM " . OrdersTopUpBase::model()->tableName() . " AS otu 
                    WHERE orders_products_id = :orders_products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":orders_products_id", $orders_products_id, PDO::PARAM_INT);

        if ($value = $command->queryRow()) {
            $customer_top_up_info_array = $value;
        }

        return $customer_top_up_info_array;
    }

    /* function return top-up status defination in array */

    function top_up_status($pass_id = '') {
        $top_up_status_array = array();
        $top_up_status_array[0] = 'Unknown';
        $top_up_status_array[1] = 'Pending';
        $top_up_status_array[3] = 'Reloaded';
        $top_up_status_array[10] = 'Failed';
        $top_up_status_array[11] = 'Not Found';

        return (isset($top_up_status_array[(int) $pass_id]) ? $top_up_status_array[(int) $pass_id] : $top_up_status_array[0]);
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $class = $this->void_include_class($publisher_id);

        if (notNull($class) && class_exists($class)) {
            $direct_topup_class_obj = new $class();
        } else {
            $direct_topup_class_obj = new dtu_offgamers();
        }

        if (method_exists($direct_topup_class_obj, 'validate_game_acc')) {
            return $direct_topup_class_obj->validate_game_acc($publisher_id, $games_acc_array, $curl_response_array);
        } else {
            return false;
        }
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $class = $this->void_include_class($publisher_id);

        if (notNull($class) && class_exists($class)) {
            $direct_topup_class_obj = new $class();
        } else {
            $direct_topup_class_obj = new dtu_offgamers();
        }

        if (method_exists($direct_topup_class_obj, 'get_character_list')) {
            return $direct_topup_class_obj->get_character_list($publisher_id, $games_acc_array, $curl_response_array);
        } else {
            return array();
        }
    }

    /*
     * wee siong comment : 2015-03-11 [Checked, it's not in used in V2]
     */

    function void_include_all_classes() {
        
    }

    function get_include_class_name($publisher_id) {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersConfigurationBase::model()->tableName() . '/publishers_id/' . $publisher_id . '/key/top_up_mode';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $class_name = $cache_result;
        } else {
            $select = " SELECT publishers_configuration_value
                        FROM " . PublishersConfigurationBase::model()->tableName() . " 
                        WHERE publishers_id = :publishers_id
                            AND publishers_configuration_key = 'TOP_UP_MODE'";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":publishers_id", $publisher_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $class_name = $value;
            } else {
                $class_name = 'offgamers';
            }
            
            Yii::app()->cache->set($cache_key, $class_name, 86400);
        }
        
        return 'dtu_' . $class_name;
    }
    
    function void_include_class($publisher_id) {
        $class_name = $this->get_include_class_name($publisher_id);

        if (notNull($class_name)) {
            $module_directory = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'extensions' . DIRECTORY_SEPARATOR . 'DirectTopup' . DIRECTORY_SEPARATOR;

            if ($dir = @dir($module_directory)) {
                while ($file = $dir->read()) {
                    if (!is_dir($module_directory . $file)) {
                        if ($file == $class_name . '.php') {
                            Yii::import('common.extensions.DirectTopup.' . $class_name);
                        }
                    }
                }
                $dir->close();
            }
        }

        return $class_name;
    }

    function get_result_code_description($pass_code) {
        switch ($pass_code) {
            case '2000':
                return 'Success';

            case '1000':
                return 'Unknown action';
            case '1001':
                return 'Incomplete request';
            case '1002':
                return 'Invalid signature';
            case '1003':
                return 'IP access denied';
            case '1004':
                return 'Publisher does not exists';
            case '1005':
                return 'Publisher inactive';
            case '1006':
                return 'Game character doest not exist';
            case '1007':
                return 'Game account doest not exist';
            case '1008':
                return 'Game does not exists';
            case '1009':
                return 'Inactive Game';
            case '1010':
                return 'Server does not exists';
            case '1011':
                return 'Inactive Server';
            case '1012':
                return 'Merchant does not exist';
            case '1013':
                return 'Inactive merchant';
            case '1014':
                return 'Publisher Reference ID expired';
            case '1015':
                return 'Publisher Reference ID does not exists';
            case '1016':
                return 'Duplicate Publisher Reference ID';
            case '1017':
                return 'Server not available';

            case '1300':
                return 'Permission denied (Unable to check reference status)';

            case '1500':
                return 'Out of credit';
            case '1501':
                return 'Top-up amount less than minimum amount';
            case '1502':
                return 'Top-up amount exceed than maximum amount';
            case '1503':
                return 'Permission denied (Unable to top-up  other transaction)';
            case '1504':
                return 'Invalid top-up amount';
            case '1505':
                return 'Invalid top-up currency';
            case '1506':
                return 'Game account suspended';
            case '1507':
                return 'Game account closed';
            case '1508':
                return 'Exceed player top-up limit';
            case '1509':
                return 'Invalid payment status at OffGamers Side';

            case '3000':
                return 'Please contact publisher';

                break;
            default:
                return $pass_code;
        }
    }

    function get_publishers_games_conf($pass_publishers_games_id = '', $pass_key = '') {
        $publishers_games_configuration_array = array();

        if (notNull($pass_key)) {
            $select = " SELECT publishers_games_configuration_value
                        FROM " . PublishersGamesConfigurationBase::model()->tableName() . "
                        WHERE publishers_games_id = :publishers_games_id
                            AND publishers_games_configuration_key = :publishers_games_configuration_key";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":publishers_games_id", $pass_publishers_games_id, PDO::PARAM_INT);
            $command->bindParam(":publishers_games_configuration_key", $pass_key, PDO::PARAM_STR);

            if ($value = $command->queryScalar()) {
                return $value;
            }

            return '';
        } else {
            $publishers_games_configuration_array = array();

            $select = " SELECT * 
                        FROM " . PublishersGamesConfigurationBase::model()->tableName() . "
                        WHERE publishers_games_id = :publishers_games_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":publishers_games_id", $pass_publishers_games_id, PDO::PARAM_INT);

            if ($dataset = $command->queryAll()) {
                foreach ($dataset as $row) {
                    $publishers_games_configuration_array[$row['publishers_games_configuration_key']] = $row;
                }
            }

            return $publishers_games_configuration_array;
        }
    }

    function character_is_sync($products_id) {
        $sync_publisher_character_flag = false;
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/' . (int) $products_id . '/sync_publisher_character_flag';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $sync_publisher_character_flag = $cache_result;
        } else {
            $select = " SELECT top_up_info_value
                        FROM " . TopUpInfoBase::model()->tableName() . "
                        WHERE top_up_info_key = 'sync_publisher_character_flag'
                            AND products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $sync_publisher_character_flag = $value == 1 ? true : false;
            }
            
            Yii::app()->cache->set($cache_key, $sync_publisher_character_flag, 86400);
        }
        
        return $sync_publisher_character_flag;
    }

    function used_as_text_field($products_id) {
        $used_as_text_field_flag = false;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/' . (int) $products_id . '/used_as_text_field_flag';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $used_as_text_field_flag = $cache_result;
        } else {
            $select = " SELECT top_up_info_value
                        FROM " . TopUpInfoBase::model()->tableName() . "
                        WHERE top_up_info_key = 'used_as_text_field_flag'
                            AND products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $used_as_text_field_flag = $value == 1 ? true : false;
            }

            Yii::app()->cache->set($cache_key, $used_as_text_field_flag, 86400);
        }

        return $used_as_text_field_flag;
    }

    function retype_account($products_id) {
        $retype_account_flag = false;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . TopUpInfoBase::model()->tableName() . '/' . (int) $products_id . '/retype_account_flag';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $retype_account_flag = $cache_result;
        } else {
            $select = " SELECT top_up_info_value
                        FROM " . TopUpInfoBase::model()->tableName() . "
                        WHERE top_up_info_key = 'retype_account_flag'
                            AND products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $retype_account_flag = $value == 1 ? true : false;
            }
            
            Yii::app()->cache->set($cache_key, $retype_account_flag, 86400);
        }
        
        return $retype_account_flag;
    }

    function get_account_platform($products_id) {
        $account_platform_array = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersGamesConfigurationBase::model()->tableName() . '/products_id/' . (int) $products_id . '/account_platform_list';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $account_platform_array = $cache_result;
        } else {
            $select = " SELECT pgc.publishers_games_configuration_value 
                        FROM " . PublishersGamesConfigurationBase::model()->tableName() . " AS pgc
                        INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp
                            ON pgc.publishers_games_id = pp.publishers_games_id
                                AND pgc.publishers_games_configuration_key = 'ACCOUNT_PLATFORM_LIST' 
                        WHERE pp.products_id = :products_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $account_platform_array = json_decode($value, 1);
                Yii::app()->cache->set($cache_key, $account_platform_array, 86400);
            }
        }
        
        return $account_platform_array;
    }
    
    function is_customer_id_to_email_conversion_needed($cat_id) {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersGamesConfigurationBase::model()->tableName() . '/catagories_id/' . (int) $cat_id . '/convert_customer_id_to_email_flag';
        $cache_result = Yii::app()->cache->get($cache_key);
        $convert_customer_id_to_email_flag = '';
        
        if ($cache_result !== FALSE) {
            $convert_customer_id_to_email_flag = $cache_result;
        } else {
            $select = " SELECT publishers_games_id 	 
                        FROM " . PublishersGamesBase::model()->tableName() . " 
                        WHERE categories_id  = :categories_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":categories_id", $cat_id, PDO::PARAM_INT);

            if ($value = $command->queryScalar()) {
                $convert_customer_id_to_email_flag = $this->get_publishers_games_conf($value, 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
                Yii::app()->cache->set($cache_key, $convert_customer_id_to_email_flag, 86400);
            }
        }
        
        return ($convert_customer_id_to_email_flag == '1') ? true : false;
    }

    function convert_customer_email_to_id($customer_email) {
        $customer_id = '';

        $select = " SELECT customers_id 
                    FROM " . CustomersBase::model()->tableName() . "
                    WHERE customers_email_address = :customers_email_address";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":customers_email_address", $customer_email, PDO::PARAM_STR);

        if ($value = $command->queryScalar()) {
            $customer_id = $value;
        }

        return $customer_id;
    }

}