<?php

class ImageCom extends MainCom 
{
    const DEFAULT_NO_PRODUCT_IMAGE_FILENAME = 'no_product_image.gif';
    const AWS_CATEGRORY_IMAGE_FILEPATH = 'images/category/';
    const AWS_PRODUCT_IMAGE_FILEPATH = 'images/products/';
    
    public  $DIR_FS_IMAGES,
            $DIR_WS_IMAGES,
            $DEFAULT_IMAGE;
    
    protected $aws_obj;

    public function __construct() {
        parent::__construct();
        $main_ui = Yii::app()->assetManager->publish(ROOT_DIR . '/frontend/packages/theme', $hashByName = false, $level = -1, Yii::app()->debug_mode);
        $this->DEFAULT_IMAGE = "$main_ui/img/no_product_image.png";
        $this->aws_obj = new AmazonWsCom();
    }
    
    public function getCategoryDimension($filename, $default = self::DEFAULT_NO_PRODUCT_IMAGE_FILENAME) {
        $img_src = $img_w = $img_h = '';

        $this->aws_obj->set_bucket_key('BUCKET_STATIC');
        $this->aws_obj->set_filepath(self::AWS_CATEGRORY_IMAGE_FILEPATH);
        $image_info_array = $this->aws_obj->get_image_info($filename);

        if (notNull($image_info_array)) {
            $img_src = $image_info_array['src'];
        } else if (notNull($filename) && file_exists($this->DIR_FS_IMAGES . 'category/' . $filename)) {
            $img_src = $this->DIR_WS_IMAGES . 'category/' . $filename;
        } else if ($default) {
            //no_product_image.gif
            $img_src = $this->DEFAULT_IMAGE;
        }
        return $img_src != '' ? array($img_src, $img_w, $img_h) : '';
    }
    
    public function getProductDimension($filename, $default = self::DEFAULT_NO_PRODUCT_IMAGE_FILENAME) {
        $img_src = $img_w = $img_h = '';

        $this->aws_obj->set_bucket_key('BUCKET_STATIC');
        $this->aws_obj->set_filepath(self::AWS_PRODUCT_IMAGE_FILEPATH);
        $products_image_info_array = $this->aws_obj->get_image_info($filename);

        if (notNull($products_image_info_array)) {
            $img_src = $products_image_info_array['src'];
        } else if (notNull($filename) && file_exists($this->DIR_FS_IMAGES . 'products/' . $filename)) {
            $img_src = $this->DIR_WS_IMAGES . 'products/' . $filename;
        } else if ($default) {
            $img_src = $this->DEFAULT_IMAGE;
        }

        return $img_src != '' ? array($img_src, $img_w, $img_h) : '';
    }

}