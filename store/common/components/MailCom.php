<?php

class MailCom extends MainCom {

    public static function addressLabel($cid, $address_id = 1, $html = false, $boln = '', $eoln = "\n") {
        $m_ab = AddressBookBase::model()->findByAttributes(array('customers_id' => $cid, 'address_book_id' => $address_id));
        if (isset($m_ab->entry_country_id)) {
            $format_id = self::getAddressFormatId($m_ab->entry_country_id);
        } else {
            $format_id = 1;
        }

        return self::addressFormat($format_id, $m_ab, $html, $boln, $eoln);
    }

    public static function getAddressFormatId($country_id) {
        $m_c = CountriesBase::model()->findByPk($country_id);
        if (isset($m_c->address_format_id)) {
            return $m_c->address_format_id;
        } else {
            return 1;
        }
    }

    public static function addressFormat($address_format_id, $m_ab, $html, $boln, $eoln) {
        $address = '';
        $country = '';
        $firstname = '';
        $lastname = '';

        $m_af = AddressFormatBase::model()->findByPk($address_format_id);

        if (isset($m_ab->entry_firstname) && notNull($m_ab->entry_firstname)) {
            $firstname = validateRequest($m_ab->entry_firstname);
        }

        if (isset($m_ab->entry_lastname) && notNull($m_ab->entry_lastname)) {
            $lastname = validateRequest($m_ab->entry_lastname);
        }

        $company = (isset($m_ab->entry_company) ? validateRequest($m_ab->entry_company) : '');
        $street = (isset($m_ab->entry_street_address) ? validateRequest($m_ab->entry_street_address) : '');
        $suburb = (isset($m_ab->entry_suburb) ? validateRequest($m_ab->entry_suburb) : '');
        $city = (isset($m_ab->entry_city) ? validateRequest($m_ab->entry_city) : '');
        $state = (isset($m_ab->entry_state) ? validateRequest($m_ab->entry_state) : '');
        if (isset($m_ab->entry_country_id) && notNull($m_ab->entry_country_id)) {
            $m_c = CountriesBase::model()->findByPk($m_ab->entry_country_id);
            if (isset($m_c->countries_name)) {
                $country = $m_c->countries_name;
            }

            if (isset($m_ab->entry_zone_id) && notNull($m_ab->entry_zone_id)) {
                $m_z = ZonesBase::model()->findByAttributes(array('zone_country_id' => $m_ab->entry_country_id, 'zone_id' => $m_ab->entry_zone_id));
                if (isset($m_z->zone_code)) {
                    $state = $m_z->zone_code;
                }
            }
        }
        $postcode = (isset($m_ab->entry_postcode) ? validateRequest($m_ab->entry_postcode) : '');
        $zip = $postcode;

        if ($html) {
            // HTML Mode
            $hr = '<hr />';
            if (($boln == '') && ($eoln == "\n")) { // Values not specified, use rational defaults
                $cr = '<br />';
                $eoln = $cr;
            } else { // Use values supplied
                $cr = $eoln . $boln;
            }
        } else {
            // Text Mode
            $cr = $eoln;
            $hr = '----------------------------------------';
        }

        $statecomma = '';
        $streets = $street;
        if ($suburb != '') {
            $streets = $street . $cr . $suburb;
        }

        if ($state != '') {
            $statecomma = $state . ', ';
        }

        $fmt = $m_af->address_format;
        $empty_val = true;

        preg_match_all('/\\$(\w+)/', $fmt, $af_var);
        foreach ($af_var[0] as $_num => $_var) {
            $_param = str_replace('$', '', $_var);

            if (notNull(${$_param})) {
                $empty_val = false;
            }

            $fmt = preg_replace('/\\' . $_var . '/', ${$_param}, $fmt);
        }
        if (!$empty_val) {
            $address = $fmt;
        }

        if ((ConfigurationCom::getValue('ACCOUNT_COMPANY') == 'true') && (notNull($company))) {
            $address = $company . $cr . $address;
        }

        return $address;
    }

    public static function send($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address, $bcc_email_address = '') {
        if (ConfigurationCom::getValue('SEND_EMAILS') != 'true') {
            return false;
        }

        $sent_status = false;
        $extra_header = '';

        $letter = array();
        $letter['message']['subject'] = $email_subject;
        $letter['message']['body'] = $email_text;
        $letter['envelope']['to'] = array('name' => $to_email_address, 'address' => $to_email_address);
        $letter['envelope']['from'] = array('name' => $from_email_name, 'address' => $from_email_address);
        $letter['envelope']['bcc'] = $bcc_email_address;

        $aws_obj = new AmazonWsCom();

        if ($aws_obj->send_mail_by_ses_controller($letter)) {
            $sent_status = $aws_obj->send_mail_by_ses($letter);
        }

        if (!$sent_status) {
            // Instantiate a new mail object
            $message = new EmailCom(array('X-Mailer: php',
                'Reply-To: ' . $from_email_address
            ));

            // Build the text version
            $text = strip_tags($email_text);
            if (ConfigurationCom::getValue('EMAIL_USE_HTML') == 'true') {
                $message->addHTML($email_text, $text);
            } else {
                $message->addText($text);
            }

            if ($bcc_email_address)
                $extra_header.="Bcc: $bcc_email_address\r\n";
            // Send message
            $message->buildMessage();
            $message->send($to_name, $to_email_address, $from_email_name, $from_email_address, $email_subject, $extra_header);
        }

        unset($aws_obj, $letter);
    }

}