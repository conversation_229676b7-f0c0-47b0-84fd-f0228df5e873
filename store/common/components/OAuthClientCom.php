<?php

class OAuthClientCom extends MainCom 
{
    private $state = '',
            $scope = '',
            $access_token = NULL,
            $refresh_token = NULL,
            $reusable_token = NULL,
            $json_mode = FALSE;
    public $client_id,
            $client_secret,
            $oauth_domain,
            $redirect_uri;
    
    private function compileRequestParams($request_params) {
        if ($this->isJsonMode()) {
            $request_params = CJSON::encode($request_params);
        }

        return $request_params;
    }

    public function enabledJsonMode() {
        $this->json_mode = TRUE;
    }

    public function setAccessToken($token, $refresh_token = '') {
        if (!empty($token)) {
            if ($this->isReusableToken()) {
                Yii::app()->session->add($this->getReusableTokenKey('oauth_accesstoken'), $token);
            }

            $this->access_token = $token;
        }

        if (!empty($refresh_token)) {
            if ($this->isReusableToken()) {
                Yii::app()->session->add($this->getReusableTokenKey('oauth_refreshtoken'), $refresh_token);
            }

            $this->refresh_token = $refresh_token;
        }
    }

    public function setClientInfo($client_info_array, $oauth_domain, $type = 's2s') {
        if (is_array($client_info_array)) {
            $this->client_id = ($type == 's2s') ? $client_info_array['CLIENT_ID_S2S'] : $client_info_array['CLIENT_ID'];
            $this->client_secret = $client_info_array['CLIENT_SECRET'];
            $this->redirect_uri = $client_info_array['REDIRECT_URI'];
            $this->oauth_domain = $oauth_domain;
        }
    }

    public function setReusableToken($key) {
        $this->reusable_token = $key;
    }

    public function setScope($scope) {
        if (!empty($scope)) {
            $this->scope = $scope;
        }
    }

    private function isJsonMode() {
        return $this->json_mode;
    }

    public function isReusableToken() {
        return Yii::app()->session->getIsStarted() ? !empty($this->reusable_token) : FALSE;
    }

    public function getAccessTokenByRefreshToken() {
        if ($this->isReusableToken() && Yii::app()->session->contains($this->getReusableTokenKey('oauth_refreshtoken'))) {
            $post_array = array(
                'grant_type' => 'refresh_token',
                'refresh_token' => Yii::app()->session->get($this->getReusableTokenKey('oauth_refreshtoken')),
                'client_id' => $this->client_id,
                'client_secret' => $this->client_secret
            );

            $this->unsetRefreshAccessToken();
            $response = $this->curl($this->oauth_domain . 'token/RequestAccessToken/', $post_array, TRUE);

            if (is_array($response) && isset($response['access_token'])) {
                $this->setAccessToken($response['access_token'], $response['refresh_token']);
                return TRUE;
            }
        }

        return FALSE;
    }

    public function getAccessToken() {
        $response = '';
        $post_array = array();

        if (is_null($this->access_token)) {
            try {
                if ($this->isReusableToken() && Yii::app()->session->contains($this->getReusableTokenKey('oauth_accesstoken'))) {
                    $this->access_token = Yii::app()->session->get($this->getReusableTokenKey('oauth_accesstoken'));
                } else if (!is_null($this->client_id)) {
                    $post_array = array(
                        'client_id' => $this->client_id,
                        'response_type' => 'code',
                        'redirect_uri' => $this->redirect_uri,
                        'state' => $this->getState(),
                        'scope' => $this->getScope(),
                        'user_id' => 0
                    );

                    $response = $this->curl($this->oauth_domain . 'authorize/requestcode/', $post_array, TRUE);
                    
                    if (is_array($response) && isset($response['query']['code'])) {
                        $response_code = $response['query']['code'];

                        if ($response_code && $this->getState() == $response['query']['state']) {
                            $post_array = array(
                                'grant_type' => 'authorization_code',
                                'code' => $response_code,
                                'client_id' => $this->client_id,
                                'client_secret' => $this->client_secret
                            );

                            $response = $this->curl($this->oauth_domain . 'token/RequestAccessToken/', $post_array, true);

                            if (is_array($response) && isset($response['access_token'])) {
                                $this->setAccessToken($response['access_token'], $response['refresh_token']);
                            } else {
                                throw new Exception('ERROR_FAILED_TO_GET_ACCESS_TOKEN');
                            }
                        } else {
                            throw new Exception('ERROR_FAILED_TO_GET_CODE_OR_INVALID_STATE');
                        }
                    } else if (isset($response['error'])) {
                        throw new Exception($response['error'] . ' : ' . $response['error_description']);
                    } else {
                        throw new Exception('ERROR_MISSING_CODE_WITH_UNKNOWN_REASON');
                    }
                } else {
                    throw new Exception('ERROR_MISSING_CLIENT_INFO');
                }
            } catch (Exception $e) {
                $this->reportError('[Method] getAccessToken', array('response' => $response, 'cURL_error' => Yii::app()->curl->getError(), 'error' => $e->getMessage(), 'post' => $post_array));
            }
        }

        return !is_null($this->access_token) ? $this->access_token : '';
    }

    private function getReusableTokenKey($key) {
        if ($this->isReusableToken()) {
            return $this->reusable_token . '_' . $key;
        }

        return $key;
    }

    private function getScope() {
        if (empty($this->scope)) {
            $this->scope = 'request_login';
        }

        return $this->scope;
    }

    public function getState() {
        if (empty($this->state)) {
            $this->state = md5($this->client_id . time() . '123456');
        }

        return $this->state;
    }

    private function callAPI($request_path, &$request_param_array, &$response, $try_count = 0, $parse = true) {
        $access_token = $this->getAccessToken();

        if ($access_token) {
            $request_param_array['access_token'] = $access_token;
            $request_data_str = $this->compileRequestParams($request_param_array);
            $response = $this->curl($request_path, $request_data_str, $parse);
            
            if ($this->renewAccessToken($response)) {
                $this->unsetAccessToken();

                if ($try_count < 1) {
                    // use refresh token to try again, if failed it will try to get from new start.
                    if (!$this->getAccessTokenByRefreshToken()) {
                        $this->resetAcessToken();
                    }

                    $this->callAPI($request_path, $request_param_array, $response, $try_count++, $parse);
                }
            } else if (($parse && !is_array($response)) || (isset($response['error']) && isset($response['code']))) {
                $this->reportError('[Method] callAPI - ' . $request_path, array('response' => $response, 'cURL_error' => Yii::app()->curl->getError(), 'request' => $request_param_array));
                $response = '';
            }
        }
    }

    private function renewAccessToken($response) {
        $return_bool = FALSE;

        if (isset($response['code']) && $response['code'] == '401') {   // The access token provided has expired.
            // oauth return info
            $return_bool = TRUE;
        }

        return $return_bool;
    }

    public function request($request_path, $request_param_array, $try_count = 0) {
        $response = '';
        $this->callAPI($request_path, $request_param_array, $response);

        return array('request' => $request_param_array, 'response' => $response);
    }

    public function resetAcessToken() {
        $this->unsetAccessToken();
        $this->unsetRefreshAccessToken();
    }

    private function unsetAccessToken() {
        if (Yii::app()->session->contains($this->getReusableTokenKey('oauth_accesstoken'))) {
            Yii::app()->session->offsetUnset($this->getReusableTokenKey('oauth_accesstoken'));
        }

        $this->access_token = null;
    }

    private function unsetRefreshAccessToken() {
        if (Yii::app()->session->contains($this->getReusableTokenKey('oauth_refreshtoken'))) {
            Yii::app()->session->offsetUnset($this->getReusableTokenKey('oauth_refreshtoken'));
        }

        $this->refresh_token = null;
    }
    
    public function curl($url, $data, $parse = false) {
        $proxy_setting_holder = Yii::app()->curl->getProxy();
        
        Yii::app()->curl->setProxy(false);
        $return = Yii::app()->curl->sendPost($url, $data, $parse);
        Yii::app()->curl->setProxy($proxy_setting_holder);

        return $return;
    }
}