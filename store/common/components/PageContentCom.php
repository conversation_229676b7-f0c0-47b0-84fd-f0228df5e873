<?php

class PageContentCom extends MainCom {

    protected function getAllCategoryIDByGameBlogID($game_blog_id) {
        return GameBlogCategoriesBase::model()->getAllCategoryByGamesBlogID($game_blog_id);
    }

    protected function getAllGamesByCategoryID($category_id) {
        return GameBlogCategoriesBase::model()->getAllGamesByCategoryID($category_id);
    }

    public function getGameBlogSupportedCategory($game_id = NULL){
        $return_array = array();

        $game_id = $game_id !== NULL ? $game_id : $this->id;

        $language_id = Yii::app()->frontPageCom->language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'game_supported_category/' . $game_id . '/language/' . $language_id;

        $cache_result = Yii::app()->cache->get($cache_key);
        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            if ($categories_array = $this->getAllCategoryIDByGameBlogID($game_id)) {
                foreach ($categories_array as $category_id) {
                    if ($category_info_array = $this->getTemplateInfoByIDByType($category_id, 0)) {
                        if (!empty($category_info_array['logo_source'])) {
                            $category_info_array['title'] = Yii::app()->frontPageCom->getPageContentObj()->getCategoriesDescription($category_id);
                            $return_array[] = $category_info_array;
                        }
                    }
                }
            }
            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        $title  = array_column($return_array, 'title');
        $title_lowercase = array_map('strtolower', $title);
        array_multisort($title_lowercase, SORT_ASC, SORT_STRING, $return_array);

        return $return_array;
    }

    public function getCategorySupportedGames($category_id = NULL) {
        $return_array = array();
        $category_id = $category_id !== NULL ? $category_id : $this->id;
        $language_id = Yii::app()->frontPageCom->language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'category_supported_game/' . $category_id . '/language/' . $language_id;

        $cache_result = Yii::app()->cache->get($cache_key);
        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            if ($games_array = $this->getAllGamesByCategoryID($category_id)) {
                foreach ($games_array as $game_id) {
                    if ($game_info_array = $this->getTemplateInfoByIDByType($game_id, 2)) {
                        if (!empty($game_info_array['logo_source'])) {
                            $game_info_array['title'] = Yii::app()->frontPageCom->getPageContentObj()->getGameBlogDescription($game_id);
                            $return_array[] = $game_info_array;
                        }
                    }
                }
            }
            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        return $return_array;
    }

    protected function getGenreDescription($game_genre_id, $language_id, $default_language_id) {
        return GameGenreDescriptionBase::model()->getGenreDescription($game_genre_id, $language_id, $default_language_id);
    }

    protected function getLanguageDescription($game_language_id, $language_id, $default_language_id) {
        return GameLanguageDescriptionBase::model()->getLanguageDescription($game_language_id, $language_id, $default_language_id);
    }

    protected function getPlatformDescription($game_platform_id, $language_id, $default_language_id) {
        return GamePlatformDescriptionBase::model()->getPlatformDescription($game_platform_id, $language_id, $default_language_id);
    }

    protected function getRegionDescription($game_region_id, $language_id, $default_language_id) {
        return GameRegionDescriptionBase::model()->getRegionDescription($game_region_id, $language_id, $default_language_id);
    }

    protected function getTemplateInfoByIDByType($id, $id_type) {
        return FrontendTemplateBase::model()->getRowByIDByType($id, $id_type);
    }

    protected function getTemplateLangInfoByTplIDByLanguageID($tpl_id, $language_id) {
        return FrontendTemplateLangBase::model()->getRowByIDByLanguage($tpl_id, $language_id);
    }

    public function getTplIDsBySearchKeyword($keyword) {
        if (!$return_array = SearchKeywordsBase::model()->getTplIDsByMatchingAgainstScore($keyword)) {
            $return_array = SearchKeywordsBase::model()->getTplIDsByKeyword($keyword);
        }
        return $return_array;
    }

    protected function getAllGameInfoByTplID($tpl_id) {
        return FrontendTemplateToGameInfoBase::model()->getRowsByIDByLanguage($tpl_id);
    }

    protected function getAllGameInfoDetail($type, $ids, $language_id, $default_language_id, $return_array = array()) {
        if (is_array($ids)) {
            foreach ($ids as $id) {
                $return_array = $this->getAllGameInfoDetail($type, $id, $language_id, $default_language_id, $return_array);
            }
        } else {
            switch ($type) {
                case 'game_genre':
                    $return_array[] = $this->getGenreDescription($ids, $language_id, $default_language_id);
                    break;
                case 'game_platform':
                    $return_array[] = $this->getPlatformDescription($ids, $language_id, $default_language_id);
                    break;
                case 'game_region':
                    $return_array[] = $this->getRegionDescription($ids, $language_id, $default_language_id);
                    break;
                case 'game_language':
                    $return_array[] = $this->getLanguageDescription($ids, $language_id, $default_language_id);
                    break;
            }
        }

        return $return_array;
    }

    protected function getGameBlogDescription($game_blog_id, $language_id, $default_language_id) {
        return GameBlogDescriptionBase::model()->getDescription($game_blog_id, $language_id, $default_language_id);
    }

    protected function getCategoriesDescription($category_id, $language_id, $default_language_id) {
        return CategoriesDescription::model()->getCategoriesName($category_id, $language_id, $default_language_id);
    }

    protected function getGameBlogCustomUrl($game_blog_id) {
        return GameBlogBase::model()->getCustomUrl($game_blog_id);
    }

}