<?php

class ProductCom extends MainCom
{
    const PRODUCT_BUNDLE = 'products_bundle';
    const PRODUCT_BUNDLE_DYNAMIC = 'products_bundle_dynamic';
    const PRODUCT_SINGLE = 'single';

    public $category_obj, $product_id, $language_id, $default_language_id, $product_info, $sub_product_info;

    public function _init($extended_param = [])
    {
        $this->language_id = isset($extended_param['language_id']) ? $extended_param['language_id'] : $this->getRegional('language_id');
        $this->default_language_id = isset($extended_param['default_language_id']) ? $extended_param['default_language_id'] : $this->getRegional('default_language_id');
    }

    protected function deactivateProduct($product_id)
    {
        return ProductsBase::model()->deactivateProductStatus($product_id);
    }

    protected function isProductZeroPriceEnabled($products_id)
    {
        $return_bool = false;

        if (ProductsExtraInfoBase::model()->getProductsZeroPriceSetting($products_id) === '1') {
            $return_bool = true;
        }

        return $return_bool;
    }

    public function setProductID($id)
    {
        $this->product_id = $id;
    }

    protected function setActiveProductByIDCache($products_id, $groups_id, $cache_array, $category_array = [])
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsBase::model()->tableName() . '/get/product_info/products_id/' . $products_id . '/groups_id/' . $groups_id;

        if ($category_array) {
            $cache_array['category'] = $category_array;
        }

        Yii::app()->cache->set($cache_key, $cache_array, 915);
    }

    public function getActiveProductByID($products_id, $groups_id, $show_virtual_products)
    {
        $return_array = [];

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsBase::model()->tableName() . '/get/product_info/products_id/' . $products_id . '/groups_id/' . $groups_id . '/' . $show_virtual_products;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_array = $cache_result;
        } else {
            $return_array = ProductsBase::model()->getActiveProductByID($products_id, $groups_id, self::ALL_USER_ACCESS_GROUP_ID, $show_virtual_products);
            $this->setActiveProductByIDCache($products_id, $groups_id, $return_array);
        }

        return $return_array;
    }

    protected function getAllProductsByCategoryID($category_id, $status, $display_status, $group_id = 0)
    {
        return ProductsBase::model()->getAllProductByCategoryID($category_id, $status, $display_status, $group_id, self::ALL_USER_ACCESS_GROUP_ID);
    }

    protected function getAllProductsByGameBlogID($game_blog_id, $category_id, $status, $group_id = 0)
    {
        return ProductsBase::model()->getAllProductByGameBlogID($game_blog_id, $category_id, $status, $group_id, self::ALL_USER_ACCESS_GROUP_ID);
    }

    public function getGameIDByID($product_id)
    {
        $return_int = 0;

        if ($category_id = ProductsBase::model()->getCategoryIDByActiveID($product_id)) {
            if ($cpath = $this->getCategoryObj()->getCPathByID($category_id)) {
                $return_int = $this->getCategoryObj()->getGameIDByCPathArrayFilterByCategoryStructure(explode('_', $cpath));
            }
        }

        return $return_int;
    }

    /*
     * 0: game_currency
     * 1: power_leveling
     * 2: cdkey
     * 3: store_credit
     * 4: hla
     */
    public function getProductBundleInfoByID($products_id = '')
    {
        $products_id = $products_id != '' ? $products_id : $this->product_id;

        if ($product_type_row = $this->getProductInfoByID($products_id)) {
            if ($product_type_row['products_bundle'] == 'yes' || $product_type_row['products_bundle_dynamic'] == 'yes') {
                // Package (Static or Dynamic)
                // NOTE: Assum package can only contain ONE product type
                if (!isset($this->product_info[$products_id]['bundle'])) {
                    if ($product_type_row = ProductsBase::model()->getProductsBundleInfoByBundleID($products_id)) {
                        foreach ($product_type_row as $subproduct_type_row) {
                            $this->product_info[$products_id]['bundle'][] = $subproduct_type_row;
                        }
                    }
                }
            } else {
                // Single Product
            }
        }

        return isset($this->product_info[$products_id]) ? $this->product_info[$products_id] : '';
    }

    protected function getCategoryObj()
    {
        if (is_null($this->category_obj)) {
            $this->category_obj = new CategoryCom();
        }

        return $this->category_obj;
    }

    public function getCPathByID($product_id)
    {
        $return_array = [];

        if ($category_id = ProductsBase::model()->getCategoryIDByActiveID($product_id)) {
            if ($cpath = $this->getCategoryObj()->getCPathByID($category_id)) {
                $return_array = explode('_', $cpath);
            }
        }
        return $return_array;
    }

    public static function getProductPackageType($product_info)
    {
        if (isset($product_info['products_bundle']) && $product_info['products_bundle'] == 'yes') {
            return self::PRODUCT_BUNDLE;
        } elseif (isset($product_info['products_bundle_dynamic']) && $product_info['products_bundle_dynamic'] == 'yes') {
            return self::PRODUCT_BUNDLE_DYNAMIC;
        } else {
            return self::PRODUCT_SINGLE;
        }
    }

    public function getProductsName($pid = null, $language_id = null, $default_languages_id = null)
    {
        $return_string = '';
        $pid = notNull($pid) ? $pid : $this->product_id;
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDescriptionBase::model()->tableName() . '/get/products_name/products_id/' . $pid . '/language/' . $language_id;
        if (isset($_REQUEST['sub_products_id'])) {
            $cache_key .= $_REQUEST['sub_products_id'];
        }
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getProductsName($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    public function getAltProductsName($pid = null, $language_id = null, $default_languages_id = null)
    {
        $return_string = '';
        $pid = notNull($pid) ? $pid : $this->product_id;
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDescriptionBase::model()->tableName() . '/get/alt_products_name/products_id/' . $pid . '/language/' . $language_id;
        if (isset($_REQUEST['sub_products_id'])) {
            $cache_key .= $_REQUEST['sub_products_id'];
        }
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getAltProductsName($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }  else if($return_string = $this->getProductsName($pid, $language_id, $default_languages_id)){
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    public function getProductsRedemptionGuide($pid = null, $language_id = null, $default_languages_id = null)
    {
        $pid = notNull($pid) ? $pid : $this->product_id;
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDescriptionBase::model()->tableName() . '/get/products_redemption/products_id/' . $pid . '/language/' . $language_id;

        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getProductsRedemptionGuide($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    public function getProductsDescription($pid, $language_id = null, $default_languages_id = null)
    {
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDescriptionBase::model()->tableName() . '/get/products_description/products_id/' . $pid . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getProductsDescription($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    protected function getProductsImage($pid, $language_id = null, $default_languages_id = null)
    {
        $return_string = '';
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        #key:products_description/get/products_image/products_id/xxx/language/xxx
        $cache_key = ProductsDescriptionBase::model()->tableName() . '/get/products_image/products_id/' . $pid . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getProductsImage($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    protected function getProductsImageTitle($pid, $language_id = null, $default_languages_id = null)
    {
        $return_string = '';
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDescriptionBase::model()->tableName() . '/get/products_image_title/products_id/' . $pid . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = ProductsDescriptionBase::model()->getProductsImageTitle($pid, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    protected function getProductsPromotionImage($pid, $language_id = null, $default_languages_id = null)
    {
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        return ProductsPromotionDescriptionBase::model()->getPromotionImage($pid, $language_id, $default_languages_id);
    }

    protected function getProductsPromotionImageTitle($pid, $language_id = null, $default_languages_id = null)
    {
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        return ProductsPromotionDescriptionBase::model()->getPromotionImageTitle($pid, $language_id, $default_languages_id);
    }

    public function getProductInfoByID($product_id = '', $field = '', $default = '')
    {
        $return_array = [];
        $product_id = $product_id != '' ? $product_id : $this->product_id;

        if (isset($this->product_info[$product_id]['actual'])) {
            $return_array = $this->product_info[$product_id]['actual'];
        } else {
            if ($return_array = ProductsBase::model()->getInfoByID($product_id)) {
                $this->product_info[$product_id]['actual'] = $return_array;
            }
        }

        return $field !== '' ? (isset($return_array[$field]) ? $return_array[$field] : $default) : $return_array;
    }

    public function getSubProductInfoByID($product_id, $sub_products_id)
    {
        $language_id = $this->getRegional('language_id', 1);
        if (!isset($this->sub_product_info[$product_id][$sub_products_id][$language_id])) {
            $data = (new MsProductModel)->getDenomination($sub_products_id, $language_id);
            //TODO Validate Return Data
            $this->sub_product_info[$product_id][$sub_products_id][$language_id] = $data;
        }
        return $this->sub_product_info[$product_id][$sub_products_id][$language_id];
    }

    public function getProductCurrencyPriceInfoByID($product_id)
    {
        return ProductsCurrencyPricesBase::model()->getInfoWithKeyValueByID($product_id);
    }

    protected function getProductsBundleByBundleID($bundle_id, $language_id = null, $default_languages_id = null)
    {
        return ProductsBase::model()->getProductsBundleInfoByBundleID($bundle_id);//, $language_id, $default_languages_id
    }

    protected function getEtaString($eta)
    {
        if(empty($eta)) return "";
        if (preg_match("/(?:#DATE#)([^#]+)(?:#DATE#)?/is", $eta, $regs)) {
            return $regs[1];
        } else {
            $hours = (int)$eta;
            $minutes = ($eta * 60) - ($hours * 60);

            return ($hours >= 1 ? $hours . " hr" . ($hours > 1 ? 's' : '') : '') . ($minutes > 0 ? ' ' . $minutes . " min" . ($minutes > 1 ? 's' : '') : '');
        }
    }

    public function getProductPricesInfoByID($products_id, $product_info_array = [])
    {
        if ($product_info_array === []) {
            $product_info_array = $this->getProductInfoByID($products_id);
        }

        $product_price_array = [
            'products_id' => $products_id,
            'price' => $product_info_array['products_price'],
            'base_cur' => $product_info_array['products_base_currency'],
            'custom_products_type_id' => $product_info_array['custom_products_type_id'],
            'products_bundle' => $product_info_array['products_bundle'],
            'defined_price' => $this->getProductCurrencyPriceInfoByID($products_id),
        ];

        return $product_price_array;
    }

    public function getProductInfo($field, $return_string = '') {
        if ($product = $this->getProductBundleInfoByID()) {
            switch ($field) {
                case 'custom_products_type_id':
                    if ($product['actual']['products_bundle'] != 'yes' && $product['actual']['products_bundle_dynamic'] != 'yes') {	// Single Product
                        $return_string = $product['actual']['custom_products_type_id'];
                    } else if ($product['bundle']) {
                        foreach ($product['bundle'] as $subproduct_type_row) {
                            $return_string = $subproduct_type_row['custom_products_type_id'];
                            break;
                        }
                    }
                    break;
                default:
                    if (isset($product['actual'][$field])) {
                        $return_string = $product['actual'][$field];
                    }
            }
        }

        return $return_string;
    }

    public function getProductDeliveryMode($products_id, $product_info_row = [])
    {
        $return_array = [];

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsDeliveryInfoBase::model()->tableName() . '/products_delivery_mode_id/array/products_id/' . $products_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_array = $cache_result;
        } else {
            $product_info_row = $product_info_row === [] ? $this->getProductInfoByID($products_id) : $product_info_row;

            if ($product_info_row) {
                if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
                    // Assume package ONLY consists of ONE product type
                    $custom_products_type_id = $product_info_row["custom_products_type_id"];
                    $sub_products_row = $this->getProductsBundleByBundleID($products_id);

                    foreach ($sub_products_row as $sub_product_row) {
                        $custom_products_type_id = $sub_product_row['custom_products_type_id'];
                        $sub_product_delivery_mode_array = $this->getProductDeliveryMode($sub_product_row['products_id']);

                        if (count($return_array)) {
                            $return_array = array_intersect($return_array, $sub_product_delivery_mode_array);
                        } else {
                            $return_array = $sub_product_delivery_mode_array;
                        }
                    }

                    if (count($return_array) == 0) {
                        if ($custom_products_type_id == 2) {
                            $return_array[] = 5; // force all to 5 if empty
                        } elseif ($custom_products_type_id == 0) {
                            $return_array[] = 1; // force all to 1 if empty
                        }
                    }
                } else {
                    $return_array = ProductsDeliveryInfoBase::model()->getProductDeliverModeIDByProductID($products_id);

                    if (count($return_array) == 0) {
                        if ($product_info_row["custom_products_type_id"] == 2) {
                            $return_array[] = 5; // force all to 5 if empty
                        } elseif ($product_info_row["custom_products_type_id"] == 0) {
                            $return_array[] = 1; // force all to 1 if empty
                        }
                    }
                }

                $return_array = array_unique($return_array, SORT_NUMERIC);

                Yii::app()->cache->set($cache_key, $return_array, 86400);
            }
        }

        return $return_array;
    }

    public function getProductDeliveryModeTitleByArrayID($dm_array)
    {
        return ProductsDeliveryModeBase::model()->getTitleByArrayID($dm_array);
    }

    protected function getPromotionProducts($products_id)
    {
        $cached_duration = 86400; // 1 day
        $return_array = [];
        $ttl_seconds = 0;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ProductsPromotionBase::model()->tableName() . '/promotion_product_info/array/products_id/' . $products_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_array = $cache_result;

            if (isset($return_array['ts'])) {
                $return_array['ttl_seconds'] = (int)$return_array['ts'] - time();
            }
        } else {
            if ($cache_array = ProductsPromotionBase::model()->getActivePromotionProductInfoByID($products_id)) {
                if ($cache_array['promotion_end_date'] != '0000-00-00 00:00:00') {
                    $temp_date = str_ireplace([' ', ':'], '-', $cache_array['promotion_end_date']);
                    list($yr, $mth, $day, $hr, $mn, $sc) = explode("-", $temp_date);
                    $cache_array['ts'] = mktime($hr, $mn, $sc, $mth, $day, $yr);
                    $ttl_seconds = (int)$cache_array['ts'] - time();

                    if ($ttl_seconds < 86400) {
                        $cached_duration = $ttl_seconds;
                    }
                }

                $return_array = $cache_array;
                $return_array['ttl_seconds'] = $ttl_seconds;
            }

            Yii::app()->cache->set($cache_key, $cache_array, $cached_duration);
        }

        return $return_array;
    }

    protected function validateCustomOutOfStockRules($products_id, $products_quantity, $purchase_qty, &$available_qty = 0, $customer_group_id = 0, $skip_cache = false)
    {
        $status = '';

        if ($skip_cache) {
            $purchase_limit_info = CustomersGroupsPurchaseControlBase::model()->getPurchaseQtyLimitByCustomerGroupAndProductId($customer_group_id, $products_id);
        } else {
            #key:customers_groups_purchase_control/products_id/xxx/customers_groups_id/xxx
            $cache_key = CustomersGroupsPurchaseControlBase::model()->tableName() . '/products_id/' . $products_id . '/customers_groups_id/' . $customer_group_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== false) {
                $purchase_limit_info = $cache_result;
            } else {
                $purchase_limit_info = CustomersGroupsPurchaseControlBase::model()->getPurchaseQtyLimitByCustomerGroupAndProductId($customer_group_id, $products_id);

                Yii::app()->cache->set($cache_key, $purchase_limit_info, 86400);
            }
        }

        if ($purchase_limit_info) {
            if ($purchase_limit_info['out_of_stock_flag'] === 1) {
                $status = 'OUT_OF_STOCK';
            } else {
                $available_qty = bcsub($products_quantity, $purchase_limit_info['purchase_limit']);

                if (bcsub($products_quantity, $purchase_qty) < $purchase_limit_info['purchase_limit']) {
                    $status = 'OUT_OF_STOCK';
                }
            }
        }

        return $status;
    }

    protected function updateCustomOutOfStockRuleOutOfStock($products_id, $current_available_quantity)
    {
        $return_bool = false;

        if ($products_id) {
            # Update out of stock flag if purchase limit fall under or equal this limit
            $return_bool = CustomersGroupsPurchaseControlBase::model()->updateOutOfStockFlag($products_id, $current_available_quantity);

            if ($return_bool) {
                # Clear all the out of stock cache
                $clear_array = CustomersGroupsPurchaseControlBase::model()->getAllOutOfStockData($products_id);

                foreach ($clear_array as $data) {
                    #key:customers_groups_purchase_control/products_id/xxx/customers_groups_id/xxx
                    $cache_key = CustomersGroupsPurchaseControlBase::model()->tableName() . '/products_id/' . $products_id . '/customers_groups_id/' . $data['customers_groups_id'];
                    Yii::app()->cache->delete($cache_key);
                }
            }
        }

        return $return_bool;
    }

    public function getProductsPaymentMethodsRestrictions($product_id = '')
    {
        $product_id = $product_id != '' ? $product_id : $this->product_id;
        $restriction_mode = 'No Restrictions';
        $restriction_info = [];

        // Check if Allow payment methods is set or not
        $restriction_info_allow = ProductsPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsAllow($product_id);
        $restriction_info_disallow = ProductsPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsDisallow($product_id);

        // get products information
        $product_info_row = $this->getProductInfoByID($product_id);

        // start checking for restrictions
        if ($restriction_info_allow != '') {
            $restriction_mode = 'Allow';
            $restriction_info = $restriction_info_allow;
        } elseif ($restriction_info_disallow != '') {
            $restriction_mode = 'Disallow';
            $restriction_info = $restriction_info_disallow;
        } else {
            // do additional checking for bundle products
            if ($product_info_row['products_bundle'] == 'yes') {
                $sub_products_row = $this->getProductsBundleByBundleID($product_id);

                // check each base products
                foreach ($sub_products_row as $sub_product_row) {
                    // Check if Allow payment methods is set or not
                    $pm_allow = ProductsPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsAllow($sub_product_row['products_id']);
                    $pm_disallow = ProductsPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsDisallow($sub_product_row['products_id']);

                    if ($pm_allow != '') {
                        list($restriction_mode, $restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $restriction_mode, 'restriction_info' => $restriction_info], ['restriction_mode' => 'Allow', 'restriction_info' => $pm_allow]);
                    } elseif ($pm_disallow != '') {
                        list($restriction_mode, $restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $restriction_mode, 'restriction_info' => $restriction_info], ['restriction_mode' => 'Disallow', 'restriction_info' => $pm_disallow]);
                    }

                }
            }
        }

        // categories checking for pm rule if above empty
        if ($restriction_mode == 'No Restrictions') {
            $product_cat_path = explode("_", substr($product_info_row['products_cat_id_path'], 1, -1));
            if (count($product_cat_path)) {
                $product_cat_path = array_reverse($product_cat_path);
                foreach ($product_cat_path as $cat_id) {
                    // Check if Allow payment methods is set or not
                    $restriction_info_allow = CategoriesPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsAllow($cat_id);
                    $restriction_info_disallow = CategoriesPaymentMethodsRestrictionsBase::model()->getProductsPmRestrictionsDisallow($cat_id);

                    // start checking for restrictions
                    if ($restriction_info_allow != '') {
                        $restriction_mode = 'Allow';
                        $restriction_info = $restriction_info_allow;
                    } elseif ($restriction_info_disallow != '') {
                        $restriction_mode = 'Disallow';
                        $restriction_info = $restriction_info_disallow;
                    }
                    if ($restriction_mode !== 'No Restrictions') {
                        break;
                    }
                }
            }
        }

        return [
            'restriction_mode' => $restriction_mode,
            'restriction_info' => (is_array($restriction_info) ? implode(",",$restriction_info) : $restriction_info),
        ];
    }

}