<?php

class PublishersCom extends MainCom {

    var $pass_id, $publishers_id;

    function __construct($data_array = array()) {
        $this->publishers_id = isset($data_array['publishers_id']) ? $data_array['publishers_id'] : 0;
    }

    function get_publishers() {
        $publishers_array = array();

        $m_ot = PublishersBase::model()->findAll(array(
            'condition' => 'publishers_id = :publishers_id',
            'params' => array(':publishers_id' => $this->publishers_id)
        ));

        foreach ($m_ot as $_val) {
            $publishers_array[$_val->publishers_id] = $_val->getAttributes(NULL);
        }

        return $publishers_array;
    }

    function get_all_publishers() {
        $publishers_array = array();

        $m_ot = PublishersBase::model()->findAll();
        foreach ($m_ot as $_val) {
            $publishers_array[$_val->publishers_id] = $_val->getAttributes(NULL);
        }

        return $publishers_array;
    }

    function set_publishers_conf($pass_key, $pass_value = '') {
        $publishers_configuration_array = array();
        $publishers_configuration_array[$pass_key] = tep_db_prepare_input($pass_value);
        $publishers_configuration_array['last_modified'] = new CDbExpression('NOW()');

        if (isset(Yii::app()->session['customers_email_address'])) {
            $publishers_configuration_array['last_modified_by'] = Yii::app()->session['customers_email_address'];
        }

        $m_cond = 'publishers_id = :publishers_id AND publishers_configuration_key = :publishers_configuration_key';
        $m_params = array(
            ':publishers_id' => $this->publishers_id,
            ':publishers_configuration_key' => $pass_key,
        );
        PublishersConfigurationBase::model()->updateAll($publishers_configuration_array, $m_cond, $m_params);
    }

    function get_publishers_conf($pass_key = '') {
        $publishers_configuration_array = array();

        if (notNull($pass_key)) {
            $publishers_configuration_row = PublishersConfigurationBase::model()->findAll(array(
                'condition' => 'publishers_id = :publishers_id AND publishers_configuration_key = :publishers_configuration_key',
                'params' => array(
                    ':publishers_id' => $this->publishers_id,
                    ':publishers_configuration_key' => $pass_key
                )
            ));

            return isset($publishers_configuration_row->publishers_configuration_value) ? $publishers_configuration_row->publishers_configuration_value : '';
        } else {
            $publishers_configuration_row = PublishersConfigurationBase::model()->findAll(array(
                'condition' => 'publishers_id = :publishers_id',
                'params' => array(
                    ':publishers_id' => $this->publishers_id,
                )
            ));

            foreach ($publishers_configuration_row as $_val) {
                $publishers_configuration_array[$_val->publishers_configuration_key] = $_val->getAttributes(NULL);
            }

            return $publishers_configuration_array;
        }
    }

    function activate_publishers() {
        $publishers_data_sql = array(
            'publishers_status' => 1,
            'last_modified' => new CDbExpression('NOW()')
        );

        if (isset(Yii::app()->session['customers_email_address'])) {
            $publishers_data_sql['last_modified_by'] = Yii::app()->session['customers_email_address'];
        }

        PublishersBase::model()->updateByPk($this->publishers_id, $publishers_data_sql);
    }

    function deactivate_publishers() {
        $publishers_data_sql = array(
            "publishers_status" => 0,
            "last_modified" => new CDbExpression('NOW()')
        );

        if (isset(Yii::app()->session['customers_email_address'])) {
            $publishers_data_sql['last_modified_by'] = Yii::app()->session['customers_email_address'];
        }

        PublishersBase::model()->updateByPk($this->publishers_id, $publishers_data_sql);
    }

    function set_publishers_game($pass_id, $pass_game) {
        $update_publishers_game_data_sql = array(
            'publishers_game' => $pass_game
        );

        if (PublishersGamesBase::model()->exists(array(
                    'condition' => 'publishers_id = :publishers_id AND products_id = :products_id',
                    'params' => array(':publishers_id' => $this->publishers_id, ':products_id' => $pass_id)
                ))) {
            #exist
            $m_cond = 'publishers_id = :publishers_id AND products_id = :products_id';
            $m_params = array(
                ':publishers_id' => $this->publishers_id,
                ':publishers_configuration_key' => $pass_id,
            );

            PublishersGamesBase::model()->updateAll($update_publishers_game_data_sql, $m_cond, $m_params);
        } else {
            $update_publishers_game_data_sql['publishers_id'] = $this->publishers_id;
            $update_publishers_game_data_sql['products_id'] = (int) $pass_id;

            PublishersGamesBase::model()->saveNewRecord($update_publishers_game_data_sql);
        }
    }

    function get_publishers_game($publishers_id = '') {
        $publishers_games_array = array();

        if (notNull($publishers_id)) {
            $m_ot = PublishersBase::model()->findAll(array(
                'condition' => 'publishers_id = :publishers_id',
                'params' => array(
                    ':publishers_id' => $publishers_id
                )
            ));
        } else {
            $m_ot = PublishersBase::model()->findAll();
        }

        foreach ($m_ot as $_val) {
            $publishers_games_array[$_val->publishers_id][$_val->publishers_games_id] = $_val->publishers_game;
        }

        return $publishers_games_array;
    }

    function set_publishers_game_server($publishers_games_id, $publishers_server) {
        if ((int) $publishers_games_id) {
            $game_array = array(
                'publishers_server' => json_encode($publishers_server)
            );

            if (PublishersGamesBase::model()->exists(array(
                        'condition' => 'publishers_games_id = :publishers_games_id',
                        'params' => array(':publishers_games_id' => $publishers_games_id)
                    ))) {
                #exist
                PublishersBase::model()->updateByPk($publishers_games_id, $game_array);
            }
        }
    }

    function get_publishers_game_server($pass_match, $pass_type = 'products_id') {
        $servers_array = array();

        if ($pass_type == 'products_id') {
            $m_ot = PublishersBase::model()->find(array(
                'condition' => 'products_id = :products_id',
                'params' => array(
                    ':products_id' => $pass_match
                )
            ));
        } else {
            $m_ot = PublishersBase::model()->find(array(
                'condition' => 'game = :game',
                'params' => array(
                    ':game' => $pass_match
                )
            ));
        }

        if (isset($m_ot->publishers_servers) && notNull($m_ot->publishers_servers)) {
            $servers_array = json_decode($m_ot->publishers_servers);
        }

        return $servers_array;
    }

}