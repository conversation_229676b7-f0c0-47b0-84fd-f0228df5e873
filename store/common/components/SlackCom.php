<?php

class SlackCom extends MainCom
{

    public $webhook = array();
    private $client, $header, $title, $basePath, $channel, $body, $attachment = array();

    public function init()
    {
        $this->client = new CurlCom();
        $this->header = array('Content-Type' => 'application/json');
        $this->basePath = 'https://hooks.slack.com/services/';
    }

    public function reset()
    {
        $this->title = '';
        $this->attachment = array();
    }

    public function setChannel($channel)
    {
        if (isset($this->webhook[$channel])) {
            $this->channel = $this->webhook[$channel];
        } else {
            $this->channel = $this->webhook['DEFAULT'];
        }
    }

    public function logError($subject, $message, $channel = 'DEV_DEBUG')
    {
        if(!is_string($message)){
            $message = CJSON::encode($message);
        }
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $message,
            )
        );
        Yii::app()->slack->send($subject, $attachments, $channel);
    }

    public function send($title, $attachments = [], $channel = 'DEFAULT')
    {
        $this->title = $title;

        $this->body = array('text' => $this->title);

        $this->setChannel($channel);
        $url = $this->basePath . $this->channel;

        foreach ($attachments as $attachment) {
            array_push($this->attachment, $attachment);
        }

        if (!empty($this->attachment)) {
            $this->body['attachments'] = $this->attachment;
        }

        $options = json_encode($this->body);

        $this->client->setProxy(!empty(Yii::app()->params['PROXY']) ? Yii::app()->params['PROXY'] : false);

        $this->client->sendPost($url, $options);

        $this->reset();
    }

}
