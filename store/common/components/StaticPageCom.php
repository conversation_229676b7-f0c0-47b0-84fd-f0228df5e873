<?php

class StaticPageCom extends MainCom
{
    public $languageId;

    public function _init($extended_param = [])
    {
        $this->languageId = isset($extended_param['language_id']) ? $extended_param['language_id'] : $this->getRegional('language_id');
    }

    public function getStaticPageContent($staticPageTypeCode, $languageId = null)
    {
        $return_string = '';
        $languageId = notNull($languageId) ? $languageId : $this->languageId;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . StaticPageContentBase::model()->tableName() . '/get/content/id/' . $staticPageTypeCode . '/language/' . $languageId;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = StaticPageContentBase::model()->getStaticPageContent($staticPageTypeCode, $languageId)) {
                Yii::app()->cache->set($cache_key, $return_string, 2592000); // 30 Days cache
            }
        }

        return $return_string;
    }
}