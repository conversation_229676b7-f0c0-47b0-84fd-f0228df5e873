<?php

class StoreCreditCom extends MainCom
{

    public $credit_accounts = array(), $user_id;
    public static $stmt_filt_month = 12;

    const PRODUCT_TYPE_ID_STORE_CREDIT = 3;

    public function __construct($user_id = null)
    {
        $this->user_id = notNull($user_id) ? $user_id : $this->getUser('id');

        $this->_get_current_credits_balance();
    }

    public function _get_current_credits_balance($return_result = false)
    {
        if ($this->credit_accounts == array()) {
            $arrayResult = self::_callAPI($this->user_id, 'view');

            if (empty($arrayResult) && !isset($arrayResult['status']) || !isset($arrayResult['result'])) {
                $this->credit_accounts = false;
                return false;
            }

            if ($arrayResult['status'] && $arrayResult['result']['Items']['user_status'] == 'ACTIVE' && isset(Yii::app()->currency->currencies[$arrayResult['result']['Items']['currency']])) {
                $this->credit_accounts = array(
                    'sc_balance' => $arrayResult['result']['Items']['balance'],
                    'sc_currency_id' => Yii::app()->currency->getIdByCode($arrayResult['result']['Items']['currency'])
                );
            }
        }

        if ($return_result) {
            return ($this->credit_accounts ? $this->credit_accounts : false);
        }
    }


    public static function get_sc_promotion_percentage($customer_id)
    {
        $percentage = 0;

        if ($customer_id) {
            $customers_groups_id = CustomerCom::getCustomerGroupID($customer_id);
            $customers_groups_extra_sc = CustomerCom::getCustomerGroupExtraSCByID($customers_groups_id);

            if ($customers_groups_extra_sc > 0) {
                $percentage = $customers_groups_extra_sc;
            }
        }

        return $percentage;
    }

    public static function accountCurrency($selected_currency = null)
    {
        $result = array(
            'status' => false,
            'result' => [
                'cur_code' => '',
                'sc_set' => false,
                'cur_symbol_left' => '',
                'cur_symbol_right' => '',
                'cur_title' => '',
            ],
        );

        $cur_obj = Yii::app()->currency;
        $cur_obj->_init();
        $arrayResult = self::_callAPI(Yii::app()->user->id, 'view');
        if ($arrayResult['status']) {
            $sc_currency_id = Yii::app()->currency->getIdByCode($arrayResult['result']['Items']['currency']);
            if (isset($cur_obj->internal_currencies[$sc_currency_id])) {
                $result['status'] = true;
                $result['result'] = array(
                    'cur_id' => $sc_currency_id,
                    'cur_code' => $cur_obj->internal_currencies[$sc_currency_id]
                );
            } else {
                $result['result'] = array(
                    'cur_id' => $sc_currency_id,
                    'cur_code' => $cur_obj->format_id[$sc_currency_id]
                );
            }
            $result['result']['sc_set'] = true;
        } else {
            if ($selected_currency) {
                $cur_code = (isset($cur_obj->currencies[$selected_currency]) ? $selected_currency : Yii::app()->params['REGIONAL_SETTING']['DEFAULT_CURRENCY']);
            } else {
                $cur_code = (isset($cur_obj->currencies[Yii::app()->session['currency']]) ? Yii::app()->session['currency'] : Yii::app()->params['REGIONAL_SETTING']['DEFAULT_CURRENCY']);
            }
            $result['status'] = true;
            $result['result'] = array(
                'cur_id' => $cur_obj->currencies[$cur_code],
                'cur_code' => $cur_code
            );
            $result['result']['sc_set'] = false;
        }

        $cur_code = $result['result']['cur_code'];
        if (!empty($cur_code) && isset($cur_obj->currencies[$cur_code])) {
            $result['result']["cur_symbol_left"] = $cur_obj->currencies[$cur_code]['symbol_left'];
            $result['result']["cur_symbol_right"] = $cur_obj->currencies[$cur_code]['symbol_right'];
            $result['result']["cur_title"] = $cur_obj->currencies[$cur_code]['title'];
        }

        return $result;
    }

    public function getCurrentCreditsBalance($cid)
    {
        $creditAccounts = array();
        $arrayResult = self::_callAPI($cid, 'view');

        if (isset($arrayResult['status']) && $arrayResult['status'] && $arrayResult['result']['Items']['user_status'] == 'ACTIVE' && isset(Yii::app()->currency->currencies[$arrayResult['result']['Items']['currency']])) {
            $creditAccounts = array(
                'sc_balance' => $arrayResult['result']['Items']['balance'],
                'sc_currency_id' => Yii::app()->currency->getIdByCode($arrayResult['result']['Items']['currency'])
            );
        } elseif (isset($arrayResult['error']) && $arrayResult['error']['code'] == 14) {
            $creditAccounts = array(
                'sc_balance' => 0,
                'sc_currency_id' => null
            );
        }

        return $creditAccounts;
    }

    public function scMiscellaneousAddAmount($transArr, $actionMsg = '', $actionDesc = '', $sendResponse = false)
    {
        $cur_obj = Yii::app()->currency;
        $cur_obj->_init();

        // get default currency id
        if (!isset($transArr['currency_id'])) {
            $transArr['currency_id'] = array_search(
                Yii::app()->params["REGIONAL_SETTING"]["DEFAULT_CURRENCY"], $cur_obj->internal_currencies
            );
        }

        $addAmount = trim($transArr['amount']);
        if (is_numeric($addAmount) && $addAmount >= 0) {
            $transArr['transaction_type'] = 'ADD_CREDIT';
            $transArr['message'] = ($actionMsg) ? $actionMsg . "\n" . $actionDesc : '';

            $result = $this->setStoreCreditBalance($transArr, $sendResponse);
            if ($result) {
                if ($sendResponse) {
                    $resultArray = [
                        'sc_trans_status' => $result['status'],
                        'sc_trans_id' => $result['result']['request_id'],
                        'sc_amount' => $result['result']['amount'],
                        'sc_currency_id' => $cur_obj->getIdByCode($result['result']['currency']),
                        'sc_currency' => $result['result']['currency'],
                    ];

                    // Handle error code 19-Order is already processed
                    if (isset($result['error']['code']) && $result['error']['code'] == 19) {
                        $resultArray['sc_trans_status'] = true;
                    }

                    return $resultArray;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        }
    }

    public function set_store_credit_balance($sc_array, $admin_msg = '', $user_msg = '')
    {
        if (count($sc_array)) {
            $m_customer = CustomersBase::model()->findByPk($this->user_id);

            if (isset($m_customer->customers_email_address)) {
                $sc_array['requesting_id'] = $m_customer->customers_email_address;
                $sc_array['requesting_role'] = 'customer';

                if ($this->setStoreCreditBalance($sc_array)) {
                    return true;
                }
            }
        }
        return false;
    }

    public function setStoreCreditBalance($updateInfo, $sendResponse = false)
    {
        $cur_obj = Yii::app()->currency;
        $cur_obj->_init();
        $updateInfo['currency'] = $cur_obj->internal_currencies[$updateInfo['currency_id']];
        unset($updateInfo['currency_id']);
        
        return $this->scUpdateInfo($updateInfo, $sendResponse);
    }

    public function scUpdateInfo($updateInfo, $sendResponse = false)
    {
        $arrayResult = self::_callAPI($this->user_id, 'post', $updateInfo);

        if ($sendResponse) {
            return $arrayResult;
        } else {
            return (isset($arrayResult['status']) && isset($arrayResult['result'])) ? $arrayResult['status'] : false;
        }
    }

    public function getMinimumStoreCredit($toCurrency, $minValue = 10, $minCurrency = 'USD', $roundToNearest = 10)
    {

        if ($toCurrency == $minCurrency) {
            $newMinValue = $minValue;
        } else {
            $newMinValue = $this->getStoreCreditConversion($minValue, $minCurrency, $toCurrency, 'buy');
        }

        $newMinValue = ceil($newMinValue / $roundToNearest) * $roundToNearest;

        return $newMinValue;
    }

    public function getMaximumStoreCredit($toCurrency, $maxValue = 2000, $maxCurrency = 'USD', $roundToNearest = 10)
    {

        if ($toCurrency == $maxCurrency) {
            $newMaxValue = $maxValue;
        } else {
            $newMaxValue = $this->getStoreCreditConversion($maxValue, $maxCurrency, $toCurrency, 'sell');
        }

        $newMaxValue = floor($newMaxValue / $roundToNearest) * $roundToNearest;

        return $newMaxValue;
    }

    function getStoreCreditConversion($scAmount = 0, $scFromCurrency, $scToCurrency, $type = 'sell')
    {
        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();

        if ($scFromCurrency == '') {
            $scFromCurrency = Yii::app()->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if ($scToCurrency == '') {
            $scToCurrency = Yii::app()->params["REG_CONFIG"]["DEF_CURRENCY_CODE"];
        }

        if (trim($scFromCurrency) == trim($scToCurrency)) {
            return $scAmount;
        }

        $returnValue = $cur_obj->advanceCurrencyConversion($scAmount, $scFromCurrency, $scToCurrency, true, $type);

        return $returnValue;
    }

    public static function getBlockedCountry($ipAddress)
    {
        $country = Yii::app()->geoip->countryCodeByIP($ipAddress);
        if (!isset(Yii::app()->params['BLOCKED_COUNTRY'])) {
            return false;
        }
        return in_array($country, Yii::app()->params['BLOCKED_COUNTRY']);
    }

    public static function storeCreditBalance($curFormat = false)
    {
        $result = array();

        $arrayResult = self::_callAPI(Yii::app()->user->id, 'view');
        $ScStatus = self::storeCreditStatus($arrayResult);

        if ($arrayResult['status'] && $arrayResult['result']['Items']['user_status'] == 'ACTIVE') {
            $result = array(
                'amount' => $arrayResult['result']['Items']['balance'],
                'currency' => $arrayResult['result']['Items']['currency'],
                'status' => $ScStatus
            );

            if(!isset(Yii::app()->currency->currencies[$result['currency']])){
                $result = array(
                    'amount' => 0,
                    'status' => false
                );
            }

            if ($curFormat && $result) {
                $cur_obj = new CurrenciesCom();
                $cur_obj->_init();
                $result['amount'] = $cur_obj->format($result['amount'], false, $arrayResult['result']['Items']['currency'],'','',' ');
            }
        } else {
            $result = array(
                'amount' => 0,
                'status' => $ScStatus
            );            
        }

        return $result;
    }

    public static function storeCreditStatus($scAccount)
    {
        $status = false;
        
        if (!empty($scAccount)) {
            if (isset($scAccount['result']['Items'])) {
                $result = $scAccount['result']['Items'];
                if ($result['user_status'] == 'ACTIVE') {
                    $status = true;
                }
            } elseif (isset($scAccount['error'])) {
                if ($scAccount['error']['code'] == '14') {
                    // User Without Store Credit Account
                    $status = true;
                }
            }
        }
        return $status;
    }

    public static function storeCreditAccount()
    {
        return self::_callAPI(Yii::app()->user->id, 'view');
    }

    public static function storeCreditStatement($extraParams)
    {
        $arrayResult = self::_callAPI(Yii::app()->user->id, '', $extraParams);

        if (empty($arrayResult) || !isset($arrayResult['status']) || !is_bool($arrayResult['status'])) {
            return false;
        }

        // Custom row numbering
        $scCount = $arrayResult['result']['Page']['total_record'];
        $scTotalPage = $arrayResult['result']['Page']['total_page'];
        $scPageSize = $arrayResult['result']['Page']['limit'];
        $scPage = $arrayResult['result']['Page']['page'];

        $startAt = $scPageSize * ($scPage - 1) + 1;
        if ($scCount < $scPageSize) {
            $endAt = $scCount;
        } elseif (($scCount / $scPageSize) < $scPage) {
            $endAt = $scCount - $scPageSize * $scPage + $scPageSize + $startAt - 1;
        } else {
            $endAt = ($scPageSize) + $startAt - 1;
        }

        // create custom array to complement data
        $result = array();
        $_res = $arrayResult['result']['Items'];
        $prev_v = 0;

        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();

        $i = 0;
        foreach ($_res as $value) {
            $cur_code = (!empty($value['transaction_currency']) ? $value['transaction_currency'] : '');
            
            $result[$i] = array(
                'desc' => Yii::t('storeCredit', 'TEXT_STATEMENT_TYPE_' . $value['activity']),
                'comment' => (isset($value['param_1']) && $value['param_1'] == 0) ? '' : "\n" . $value['activity_description'],
                'amount' => (($value['transaction_amount'] != '') ? $cur_obj->format_with_all_currencies($value['transaction_amount'], false, $cur_code) : $cur_obj->format('0.00', false, $cur_code)),
                'balance' => (($value['new_amount'] != '') ? $cur_obj->format_with_all_currencies($value['new_amount'], false, $cur_code) : $cur_obj->format('0.00', false, $cur_code)),
                'request_id' => (($value['request_id'] != '') ? $value['request_id'] : ''),
                'created_date' => (($value['created_date'] != '') ? $value['created_date'] : ''),
            );

            switch ($value['activity']) {
                case 'D':
                    if (!empty($value['order_id'])) {
                        $result[$i]['desc'] .= ' ( <a href="' . Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/wor/statement' . '">#' . $value['order_id'] . '</a> ) ';
                    }
                    break;
                case 'P':
                case 'R':
                case 'S':
                case 'C':
                    if (!empty($value['order_id'])) {
                        $result[$i]['desc'] .= ' ( <a href="/account/purchase/order/' . $value['order_id'] . '">#' . $value['order_id'] . '</a> ) ';
                    }
                    break;
                case 'MX':
                case 'V':
                    if ($prev_v % 2 == 0) {
                        $result[$i]['desc'] .= Yii::t('storeCredit', 'TEXT_SC_CONVERT_FROM_TO', array('{SYS_FROM_CUR}' => $value['previous_currency'], '{SYS_TO_CUR}' => $cur_code));
                    } else {
                        $result[$i]['desc'] .= Yii::t('storeCredit', 'TEXT_SC_CONVERT_FROM_TO', array('{SYS_FROM_CUR}' => $cur_code, '{SYS_TO_CUR}' => $value['previous_currency']));
                    }
                    $prev_v++;
                    break;
            }

            $i++;
        }

        return array(
            'list' => $arrayResult['result']['Items'],
            'list_customs' => $result,
            'pagination' => array(
                'display_start' => $startAt,
                'display_end' => $endAt,
                'total' => $scCount,
                'page' => $scPage,
                'total_pages' => $scTotalPage,
            ),
        );
    }

    public static function getAllStoreCreditStatement($extraParams)
    {
        $arrayResult = self::_callAPI(Yii::app()->user->id, '', $extraParams);

        $allListArray = ($arrayResult['result']['Items'] ?? []);

        if (isset($arrayResult['result']['Page'])) {
            if ($arrayResult['result']['Page']['total_page'] > 1) {
                for ($i = 2; $i <= $arrayResult['result']['Page']['total_page']; $i++) {
                    $arrayResult = self::_callAPI(Yii::app()->user->id, '', $extraParams);
                    $allListArray = array_push($allListArray, $arrayResult['result']['Items']);
                }
            }
        }

        return $allListArray;
    }

    public static function getOrderScPaidAmount($orders_id)
    {
        $used_amount = 0;

        // get all transaction related to this order
        $statementArray = self::getAllStoreCreditStatement(array(
            'orders_id' => $orders_id,
            'activity' => 'P'
        ));

        foreach ($statementArray as $sc_history_info) {
            $used_amount += $sc_history_info['transaction_amount'];
        }

        return $used_amount;
    }

    public function redeemGiftCard($input)
    {
        $response = ['status' => false, 'error_code' => 1];
        $newGiftCard = true;
        $ogcRequestId = 'OG-GC-' . $input["serial"];
        // Create Gift Card entry
        if ($gcInfo = GiftCardRedemptionBase::model()->findByAttributes(array('serial_number' => $input["serial"]), array('order' => 'gift_card_redemption_id DESC', 'limit' => 1))) {
            if (empty($gcInfo->transaction_id) && $gcInfo->customers_id == Yii::app()->user->id) {
                $newGiftCard = false;
                $gc_redeem_track_array = [
                    'transaction_id' => $ogcRequestId,
                    'redeem_date' => date('Y-m-d H:i:s'),
                    'redeem_ip' => getIPAddress(),
                ];
                $return_bool = GiftCardRedemptionBase::model()->updateGiftCardRedemption($gcInfo->gift_card_redemption_id, $gc_redeem_track_array);
                $giftCardRedemptionId = $gcInfo->gift_card_redemption_id;
            }
        }

        if ($newGiftCard) {
            $gc_redeem_track_array = [
                'transaction_id' => $ogcRequestId,
                'customers_id' => Yii::app()->user->id,
                'serial_number' => $input["serial"],
                'pin_number' => $input["pin"],
                'gift_card_deno' => $input["orig_deno"],
                'gift_card_currency_id' => $input["orig_cur_id"],
                'gift_card_currency_code' => $input["orig_cur_code"],
                'gift_card_redeem_amount' => $input["orig_deno"],
                'transaction_type' => 'SC',
                'redeem_date' => date('Y-m-d H:i:s'),
                'redeem_ip' => getIPAddress(),
            ];
            $giftCardRedemptionId = GiftCardRedemptionBase::model()->saveNewRecord($gc_redeem_track_array, true);
        }

        // re-check duplicates
        $countGiftCardRow = GiftCardRedemptionBase::model()->countByAttributes(array('serial_number' => $input["serial"]));
        if ($countGiftCardRow > 1) {
            // Remove request_id from row
            $gc_redeem_track_array = [
                'transaction_id' => NULL
            ];
            $return_bool = GiftCardRedemptionBase::model()->updateGiftCardRedemption($giftCardRedemptionId, $gc_redeem_track_array);

            $mail_content = '   Gift Card Pin Number : ' . $input["pin"] . ' 
                                Gift Card Serial Number : ' . $input["serial"] . ' 
                                Gift Card Value : ' . $input["redeem_cur_code"] . ' ' . $input["redeem_deno"] . '
                                Customer ID :' . Yii::app()->user->id . '
                                DateTime : ' . time();
            $subject = Yii::app()->params["DEV_DEBUG_EMAIL_SUBJECT_PREFIX"] . "GiftCard TopUp Duplicate";
            $attachments = array(
                array(
                    'color' => 'danger',
                    'text' => $mail_content
                )
            );
            Yii::app()->slack->send($subject, $attachments, 'DEFAULT');

            return [
                'status' => false,
                'error_code' => 2
            ];
        }

        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();
        $def_cur = ConfigurationBase::model()->getConfigValue('DEFAULT_CURRENCY');

        // Prepare to add SC amount back to user account
        $m_customer = CustomersBase::model()->findByPk(Yii::app()->user->id);
        $trans_array = array(
            'reference_id' => $input["serial"],
            'request_id' => $ogcRequestId,
            'requesting_id' => (isset($m_customer->customers_email_address) ? $m_customer->customers_email_address : Yii::app()->user->id),
            'requesting_role' => 'customer',
            'amount' => $input["redeem_deno"],
            'total_amount' => $input["redeem_deno"],
            'currency_id' => $input["redeem_cur_id"],
            'activity' => 'GC',
            'show_customer' => 0
        );

        $sc_top_up_info = $this->scMiscellaneousAddAmount($trans_array, 'Gift Card Redemption', '', true);
        // Check if topup success or not
        if (!empty($sc_top_up_info) && isset($sc_top_up_info['sc_trans_id'])) {
            $gc_redeem_track_array = [
                'transaction_id' => $sc_top_up_info['sc_trans_id'],
                'issued_amount' => $sc_top_up_info['sc_amount'],
                'issued_currency_id' => $sc_top_up_info['sc_currency_id'],
                'issued_currency_code' => $sc_top_up_info['sc_currency']
            ];

            $response = [
                'status' => $sc_top_up_info['sc_trans_status'],
                'error_code' => 2
            ];
        } else {
            $gc_redeem_track_array = [
                'transaction_id' => NULL
            ];
        }

        $return_bool = GiftCardRedemptionBase::model()->updateGiftCardRedemption($giftCardRedemptionId, $gc_redeem_track_array);

        return $response;
    }

    protected static function _callAPI($customer_id, $action = '', $extraParams = array())
    {
        $response = false;
        $method = 'sendPost';
        $cache_key = false;
        switch (strtolower($action)) {
            case 'view':
                // Check if SC disabled?
                $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'store_credit_balance/' . $customer_id;
                $cache_result = Yii::app()->cache->get($cache_key);

                if (Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'] == 0) {
                    return array('status' => false);
                }

                if ($cache_result !== FALSE) {
                    return $cache_result;
                }

                $apiEndpoint = 'store-credits/' . $customer_id;
                $method = 'sendGet';
                $extraParams = [
                    'checking_type' => 'BALANCE',
                ];
                break;
            case 'post':
                $apiEndpoint = 'store-credits';
                $extraParams['customers_id'] = $customer_id;
                $extraParams['customers_role'] = 'customers';
                break;
            default:
                $apiEndpoint = 'store-credits';
                $method = 'sendGet';

                $extraParams['customers_id'] = $customer_id;
                $extraParams['list_type'] = 'TRANSACTION';
                $extraParams['sort'] = 'DESC';
                break;
        }

        // Clean data params if empty
        $extraParams = array_filter($extraParams, function ($value) {
            return ($value !== null && $value !== false && $value !== '');
        });

        $url = Yii::app()->params['MICROSERVICE_STORECREDIT']['BASEURL'] . "/" . $apiEndpoint;

        $extraParams['source'] = Yii::app()->params['MICROSERVICE_STORECREDIT']['KEY'];
        $extraParams['time'] = time();
        $extraParams['signature'] = md5(Yii::app()->params['MICROSERVICE_STORECREDIT']['KEY'] . $extraParams['time'] . "|" . Yii::app()->params['MICROSERVICE_STORECREDIT']['SECRET']);

        $proxy_setting_holder = Yii::app()->curl->getProxy();
        Yii::app()->curl->setProxy($proxy_setting_holder);

        $start_time = time();
        $start_time_m = microtime(true);

        // Use retry method for curl to microservice
        $i = 0;

        while ($i++ < 3) {
            try {
                $response = Yii::app()->curl->$method($url, json_encode($extraParams), true);
                if($cache_key && !empty($response) && isset($response['status']) && isset($response['result'])){
                    Yii::app()->cache->set($cache_key, $response, 3600);
                }
                if (!isset($response['status']) || !isset($response['result'])) {
                    throw new Exception("Error Connecting to store credit micro-service");
                }
            } catch (Exception $e) {
                $message = "Error Message : " . (($e->getMessage()) ? $e->getMessage() : 'Empty Response From Server') . "\n";
                $message .= "Endpoints : " . $url . "\n";
                $message .= "Params : " . json_encode($extraParams) . "\n";
                $message .= "Response Body : " . json_encode($response) . "\n";
                $message .= "Request Timestamp : " . date('Y-m-d H:i:s', $start_time) . "\n";
                $message .= "Total Time(ms) : " . (int)((microtime(true) - $start_time_m) * 1000);

                $subject = "StoreCreditCom : _callAPI";
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => $message,
                    )
                );
                Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG');
                $response = false;
            }

            // If response not false break the loop
            if ($response) {
                break;
            }
            // Delays the program execution
            if ($i < 3) {
                usleep(500);
            }
        }

        return $response;
    }

}
