<?php

class TaxModuleCom extends MainCom {

    public $customerId, $taxInfo = array();

    public function __construct($customerId = null) {
        $this->customerId = notNull($customerId) ? $customerId : $this->getUser('id');
        $this->taxInfo = Yii::app()->localizationCom->showGSTAlertBar(false);
    }

    public function getOrdersTaxCustomers() {
        $otcModel = OrdersTaxCustomers::model()->findByAttributes(array('customers_id' => $this->customerId));
        if (!$otcModel) {
            $otcModel = new OrdersTaxCustomers();
        }

        return $otcModel;
    }

    public static function getFailedMessage($jsonData) {
        $data = json_decode($jsonData, true);
        if (isset($data['show_customer']) && $data['show_customer'] == 1) {
            return $data['remarks'];
        }
        return '';
    }

    public function save($input) {
        $result = false;
        $response = array('status' => false, 'error_message' => '');
        $fieldData = array();
        $arrayExistingData = array();
        $time = time();
        if (isset($this->taxInfo['business_status']) && $this->taxInfo['business_status'] == 1) {
            // Check tax number exist?
            $otcModel = OrdersTaxCustomers::model()->findByAttributes(
                    array('business_tax_number' => $input['input_tax_number']), 'customers_id != :customers_id', array(':customers_id' => $this->customerId)
            );
            if (!$otcModel) {
                $fieldArray = json_decode($this->taxInfo['field_data'], true);
                $fieldDataArray = [];
                foreach ($input as $key => $value) {
                    foreach ($fieldArray[1] as $fields) {
                        // Cross check the input field name
                        if ($key == $fields['name']) {
                            $fields['value'] = strip_tags($value);
                            $fieldDataArray[] = $fields;
                        }
                    }
                }
                // Check for display label or information
                foreach ($fieldArray[1] as $fields) {
                    if ($fields['type'] == 'display_label' || $fields['type'] == 'information_text') {
                        $fieldDataArray[] = $fields;
                    }
                }
                // Set json array
                if ($fieldDataArray) {
                    $fieldData['created_at'] = $time;
                    $fieldData['field_data'] = $fieldDataArray;
                }

                $data = [
                    'orders_tax_id' => $this->taxInfo['id'],
                    'country_name' => $this->taxInfo['country_name'],
                    'business_name' => $input['input_business_name'],
                    'business_tax_number' => $input['input_tax_number'],
                    'orders_tax_customers_data' => json_encode($fieldData),
                    'orders_tax_customers_status' => 0, // 0-pending, 1=verified, 2-rejected, 3-canceled/removed by customer
                    'updated_at' => $time,
                ];

                $otcObj = OrdersTaxCustomers::model()->findByAttributes(array('customers_id' => $this->customerId));
                if ($otcObj) {
                    // Prep old data move to history
                    $oldData = [
                        'customers_id' => $otcObj->customers_id,
                        'orders_tax_customers_id' => $otcObj->id,
                        'orders_tax_customers_data' => $otcObj->orders_tax_customers_data,
                        'orders_tax_customers_status' => $otcObj->orders_tax_customers_status,
                        'created_at' => $time,
                        'updated_at' => $time,
                    ];
                    // Check if the same tax country?
                    if ($otcObj->orders_tax_id != $this->taxInfo['id']) {
                        // update json string
                        $dataArray = json_decode($otcObj->orders_tax_customers_data, true);
                        $dataArray['prev_orders_tax_id'] = $otcObj->orders_tax_id;
                        $dataArray['prev_country_name'] = $otcObj->country_name;
                        $oldData['orders_tax_customers_data'] = json_encode($dataArray);
                    }
                    OrdersTaxCustomersHistory::model()->saveNewRecord($oldData);
                    // Save new data
                    $result = OrdersTaxCustomers::model()->updateByPk($otcObj->id, $data);
                    $saveMessage = 'TEXT_TAX_SUCCESS_UPDATE';
                    $saveErrorMessage = 'TEXT_TAX_ERROR_UPDATE';
                } else {
                    $data['customers_id'] = Yii::app()->user->id;
                    $data['orders_tax_customers_data'] = json_encode($fieldData);
                    $data['created_at'] = $time;
                    $result = OrdersTaxCustomers::model()->saveNewRecord($data);
                    $saveMessage = 'TEXT_TAX_SUCCESS_ADD';
                    $saveErrorMessage = 'TEXT_TAX_ERROR_ADD';
                }

                if ($result) {
                    // Send Slack & email msg to the admin
                    $response = array('status' => true, 'error_message' => Yii::t('taxModule', 'TEXT_TAX_SUCCESS_UPDATE'));
                    $mail_content = 'Customer ID :' . Yii::app()->user->id . "\n";
                    $mail_content .= 'DateTime : ' . date('Y-m-d H:i:s', $time);
                    $subject = Yii::app()->params["DEV_DEBUG_EMAIL_SUBJECT_PREFIX"] . " - Customer Business Tax Submission";
                    $attachments = array(
                        array(
                            'color' => 'warning',
                            'text' => $mail_content
                        )
                    );
                    Yii::app()->slack->send($subject, $attachments, 'ANB_TAX_SUBMISSION');
                    FrontendMailCom::send(Yii::app()->params['ANB_EMAIL_TAX_SUBMISSION'], Yii::app()->params['ANB_EMAIL_TAX_SUBMISSION'], $subject, $mail_content, ConfigurationCom::getValue('STORE_OWNER'), ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS'));
                } else {
                    $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_UPDATE');
                }
            } else {
                $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_DUPLICATE_TAX_NUMBER', array('{TAX_NAME}' => $this->taxInfo['display_title']));
            }
        } else {
            $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_UPDATE');
        }

        return $response;
    }

    public function updateStatusCancel($action) {
        $result = false;
        $response = array('status' => false, 'error_message' => '');
        $time = time();

        if (isset($this->taxInfo['business_status']) && $this->taxInfo['business_status'] == 1) {
            $otcObj = OrdersTaxCustomers::model()->findByAttributes(array('customers_id' => $this->customerId));
            if ($otcObj) {
                // Prep old data move to history
                $oldData = [
                    'customers_id' => $otcObj->customers_id,
                    'orders_tax_customers_id' => $otcObj->id,
                    'orders_tax_customers_data' => $otcObj->orders_tax_customers_data,
                    'orders_tax_customers_status' => $otcObj->orders_tax_customers_status,
                    'created_at' => $time,
                    'updated_at' => $time,
                ];
                // Prep-new data
                $data = [
                    'business_name' => '',
                    'business_tax_number' => '',
                    'orders_tax_customers_status' => 3, // 0-pending, 1=verified, 2-rejected, 3-canceled/removed by customer
                    'updated_at' => $time,
                ];
                // update json string
                $dataArray = json_decode($otcObj->orders_tax_customers_data, true);
                $dataArray['canceled_at'] = $time;
                $dataArray['canceled_by'] = $this->customerId;
                $data['orders_tax_customers_data'] = json_encode($dataArray);

                OrdersTaxCustomersHistory::model()->saveNewRecord($oldData);
                // Save new data
                $result = OrdersTaxCustomers::model()->updateByPk($otcObj->id, $data);
            } else {
                $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_' . strtoupper($action));
            }
        } else {
            Yii::app()->user->setFlash('error', Yii::t('taxModule', 'TEXT_TAX_ERROR_' . strtoupper($action)));
            $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_' . strtoupper($action));
        }

        if ($result) {
            $response = array('status' => true, 'error_message' => Yii::t('taxModule', 'TEXT_TAX_SUCCESS_' . strtoupper($action)));
        } else {
            $response['error_message'] = Yii::t('taxModule', 'TEXT_TAX_ERROR_' . strtoupper($action));
        }

        return $response;
    }

    public function existInOtherCountry($taxInfo, $otcModel) {
        $result = false;

        if (isset($otcModel->orders_tax_id) && $otcModel->orders_tax_id == $taxInfo['id']) {
            $result = true;
        } elseif (isset($otcModel->orders_tax_customers_status) && $otcModel->orders_tax_customers_status == 3) {
            $result = true;
        } elseif (!isset($otcModel->orders_tax_id)) {
            $result = true;
        }

        return $result;
    }

    public function generateDynamicForm($config) {
        $html = '';
        switch ($config['type']) {
            case 'text_box':
                $size = (isset($config['size']) && !empty($config['size'])) ? ('max="' . $config['size'] . '"') : '';
                $maxChar = (isset($config['max_char']) && !empty($config['max_char'])) ? ('maxlength="' . $config['max_char'] . '"') : '';
                return '<input type="text" ' . $size . ' ' . $maxChar . ' name="' . $config['name'] . '" class="input input-deff tax-input-deff field__input js-field-input js-input-mak" value="' . ((isset($config['value']) && !empty($config['value'])) ? $config['value'] : '') . '" ' . (($config['mandatory'] == 1) ? 'required' : '') . '><span class="field__label">' . $config['title'] . '</span>';
            case 'text_area':
                $rows = (isset($config['row']) && !empty($config['row'])) ? ('rows="' . $config['row'] . '"') : '';
                $cols = (isset($config['column']) && !empty($config['column'])) ? ('cols="' . $config['column'] . '"') : '';
                return '<textarea ' . $rows . ' ' . $cols . ' name="' . $config['name'] . '" class="textarea input-deff tax-input-deff field__input js-field-input js-input-mak" ' . (($config['mandatory'] == 1) ? 'required' : '') . '>' . ((isset($config['value']) && !empty($config['value'])) ? $config['value'] : '') . '</textarea><span class="field__label-textarea">' . $config['title'] . '</span>';
            case 'dropdown_menu':
                $optionArray = explode("\n", trim($config['options']));

                $html .= '<span>' . $config['title'] . '</span>';
                $html .= '<select class="select-deff select-deff--decor" name="' . $config['name'] . '" ' . (($config['mandatory'] == 1) ? 'required' : '') . '>';
                foreach ($optionArray as $key => $value) {
                    $selected = '';
                    if (isset($config['value']) && $config['value'] == $value) {
                        $selected = 'selected';
                    }
                    $html .= "<option value='" . $value . "' " . $selected . ">" . $value . "</option>";
                }
                $html .= '</select>';
                return $html;
            case 'radio_button':
                $optionArray = explode("\n", trim($config['options']));

                $html .= '<span>' . $config['title'] . '</span><div class="tax-input-deff">';
                foreach ($optionArray as $key => $value) {
                    $checked = '';
                    if (isset($config['value']) && $config['value'] == $value) {
                        $checked = 'checked';
                    }
                    $html .= '<div class="tax-form-radio"><input type="radio" name="' . $config['name'] . '" value="' . $value . '" ' . $checked . ' ' . (($config['mandatory'] == 1) ? 'required' : '') . '>' . $value . '</div>';
                }
                $html .= '</div>';
                return $html;
            case 'date_selection':
                $value = (isset($config['value']) && !empty($config['value'])) ? date('Y-m-d', strtotime($config['value'])) : date('Y-m-d');
                $fromDate = (isset($config['date_from']) && !empty($config['date_from'])) ? (($config['date_from'] == 'TODAY') ? date('Y-m-d') : date('Y-m-d', strtotime($config['date_from']))) : date('Y-m-d');
                $periodDate = (isset($config['date_period']) && !empty($config['date_period'])) ? date('Y-m-d', strtotime($fromDate . "+" . $config['date_period'] . " days")) : '';
                $html .= '<input type="date" name="' . $config['name'] . '" class="input input-deff tax-input-deff field__input js-field-input js-input-mak" value="' . $value . '" min="' . $fromDate . '" max="' . $periodDate . '" pattern="\d{4}-\d{2}-\d{2}"  ' . (($config['mandatory'] == 1) ? 'required' : '') . '>';
                $html .= '<span class="field__label">' . $config['title'] . '</span>';
                return $html;
            case 'display_label':
                return '<div class="tax-input-deff">' . $config['title'] . '</div>';
            case 'information_text':
                $config['options'] = explode("\n", trim($config['options']));
                return '<div class="tax-input-deff">' . implode('<br>',$config['options']) . '</div>';
                return $html;
        }
    }

    public static function getTaxSettingStatus($ignore_cache = false) {
        /*
         * refer to
         * 1. customer mobile phone country
         * 2. IP if no record found
         * 3. default country if fail to identify IP country
         */
        if (!isset(Yii::app()->session['user.tax.status']) || $ignore_cache) {
            Yii::app()->session['user.tax.status'] = false;

            $m = CustomersBase::model()->findByPk(Yii::app()->user->id);
            if (!empty($m->customers_country_dialing_code_id)) {
                // customer mobile phone country
                $countries_id = $m->customers_country_dialing_code_id;
            } else {
                // IP country or default country
                $ip = Yii::app()->frontPageCom->getIPAddress();
                $m = Yii::app()->geoip->getIPCountryInfo($ip);
                if (isset($m['id']) && !empty($m['id'])) {
                    $countries_id = $m['id'];
                } else {
                    $countries_id = ConfigurationCom::getValue('STORE_COUNTRY');
                }
            }

            $m = Countries::model()->findByPk($countries_id);
            if ($m->countries_iso_code_2) {
                $m_otc = OrdersTaxConfiguration::model()->findByAttributes(["country_code" => $m->countries_iso_code_2]);
                if (isset($m_otc->orders_tax_status)) {
                    Yii::app()->session['user.tax.status'] = $m_otc->orders_tax_status;
                }
            }
        }

        return Yii::app()->session['user.tax.status'];
    }

}
