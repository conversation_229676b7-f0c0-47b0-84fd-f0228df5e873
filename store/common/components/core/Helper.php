<?php
/**
 * You can just write the @method and global to all Application
 */

//http://php.net/manual/en/function.array-replace-recursive.php#92574
if (!function_exists('array_replace_recursive')) {
    function array_replace_recursive($array, $array1)
    {
        function recurse($array, $array1)
        {
            foreach ($array1 as $key => $value) {
                // create new key in $array, if it is empty or not an array
                if (!isset($array[$key]) || (isset($array[$key]) && !is_array($array[$key]))) {
                    $array[$key] = array();
                }

                // overwrite the value in the base array
                if (is_array($value)) {
                    $value = recurse($array[$key], $value);
                }
                $array[$key] = $value;
            }
            return $array;
        }

        // handle the arguments, merge one by one
        $args = func_get_args();
        $array = $args[0];
        if (!is_array($array)) {
            return $array;
        }
        for ($i = 1; $i < count($args); $i++) {
            if (is_array($args[$i])) {
                $array = recurse($array, $args[$i]);
            }
        }
        return $array;
    }
}

function createRandomValue($length, $type = 'mixed')
{
    if (($type != 'mixed') && ($type != 'chars') && ($type != 'digits')) {
        return false;
    }

    $rand_value = '';
    while (strlen($rand_value) < $length) {
        if ($type == 'digits') {
            $char = cpnRand(0, 9);
        } else {
            $char = chr(cpnRand(0, 255));
        }

        if ($type == 'mixed') {
            if (preg_match('/^[a-z0-9]$/i', $char)) {
                $rand_value .= $char;
            }
        } else {
            if ($type == 'chars') {
                if (preg_match('/^[a-z]$/i', $char)) {
                    $rand_value .= $char;
                }
            } else {
                if ($type == 'digits') {
                    if (preg_match('/^[0-9]$/', $char)) {
                        $rand_value .= $char;
                    }
                }
            }
        }
    }

    return $rand_value;
}

function cpnRand($min = null, $max = null)
{
    static $seeded;

    if (!isset($seeded)) {
        mt_srand((double)microtime() * 1000000);
        $seeded = true;
    }

    if (isset($min) && isset($max)) {
        if ($min >= $max) {
            return $min;
        } else {
            return mt_rand($min, $max);
        }
    } else {
        return mt_rand();
    }
}

function getIPAddress()
{
    $ip_header_sequence = ['HTTP_TRUE_CLIENT_IP', 'HTTP_X_AMZ_CF_ID', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];

    foreach ($ip_header_sequence as $header) {
        if (!empty($_SERVER[$header])) {
            $ip = $_SERVER[$header];
            // Validate IP Address
            if ($result = validateIp($ip)) {
                return $result;
            }
        }
    }

    return validateIp(getenv('REMOTE_ADDR'));
}

/*
*  "country" => 223, // 129
   "country_code" => "US", // MY
   "country_name" => "United States",
   "currency" => "USD", // USD
   "language" => "en", // en
   "language_id" => 1, // 1
   "language_name" => "English", // English
   "default_currency" => "USD", // USD
   "css_lang" => "en", // en
   "default_language_id" => 1, // 1
* customers_firstname
* customers_lastname
* customers_groups_id
* customers_groups_name
* customers_email_address
* customers_login_ip
* customers_login_timestamp
* customers_default_address_id
* isGuest
*/
function getSessVal($key, $default = '')
{
    return isset(Yii::app()->session[$key]) ? Yii::app()->session[$key] : $default;
}

function stringToInt($string)
{
    return (int)$string;
}

function validateIp($ip)
{
    if (strpos($ip, ',') !== false) {
        $ip = explode(',', $ip);
    }

    if (is_array($ip)) {
        foreach ($ip as $_ip) {
            if ($result = validateIp($_ip)) {
                return $result;
            }
        }
    } else {
        $ip = trim($ip);
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_IPV6 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            return false;
        } elseif (IPHelper::isReservedIP($ip)) {
            return false;
        }
        return $ip;
    }
    return false;
}

function validateRequest($data_str, $filter = 'string')
{
    $return = '';

    switch ($filter) {
        case 'string':
            if (($str = filter_var($data_str, FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES)) !== false) {
                $return = $str;
            }
            break;
        case 'integer':
            if (($int = filter_var($data_str, FILTER_SANITIZE_NUMBER_INT)) !== false) {
                $return = $int;
            }
            break;
    }

    //trim(stripslashes(strip_tags($data_str)))
    return $return;
}

function notNull($value)
{
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if (($value != '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function notEmpty($value)
{
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if (($value !== '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function isSerialize($params)
{
    return (@unserialize($params) !== false) ? true : false;
}

function isJson($params)
{
    return (@CJSON::decode($params) !== null) ? true : false;
}

function parseEmailString($emailString)
{
    $emailArray = notNull($emailString) ? explode(',', $emailString) : array();
    $emailPattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
    $emailReceivers = array();

    for ($receiver_cnt = 0; $receiver_cnt < count($emailArray); $receiver_cnt++) {
        if (preg_match($emailPattern, $emailArray[$receiver_cnt], $regs)) {
            $receiverName = trim($regs[1]);
            $receiverEmail = trim($regs[2]);

            $emailReceivers[] = array('name' => $receiverName, 'email' => $receiverEmail);
        } else {
            $emailReceivers[] = array('name' => $emailArray[$receiver_cnt], 'email' => $emailArray[$receiver_cnt]);
        }
    }

    return $emailReceivers;
}

function countdim($array)
{
    if (is_array(reset($array))) {
        $return = countdim(reset($array)) + 1;
    } else {
        $return = 1;
    }

    return $return;
}

function domainMapping($source_input_url, $hostDomain = '')
{
    $source_input_url = str_replace('\\', '/', $source_input_url);
    $inputUrl = urldecode(urldecode($source_input_url));

    $inputDomain = explode('.', parse_url($inputUrl, PHP_URL_HOST));

    if ($inputUrl != $source_input_url || !isset($inputDomain[count($inputDomain) - 2]) || !isset($inputDomain[count($inputDomain) - 1])) {
        $attachments = array(
            array(
                'color' => 'warning',
                'text' => json_encode([
                    'input_domain' => $inputDomain,
                    'host_domain' => $hostDomain,
                    'referral_url' => ($_SERVER['HTTP_REFERER'] ?? ''),
                    'full_path' => "http" . (($_SERVER['SERVER_PORT'] == 443) ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']
                ])
            )
        );
        Yii::app()->slack->send('Debug Helper.php :: Double Encode Origin Url', $attachments, 'DEV_DEBUG');
    }

    if (!isset($inputDomain[count($inputDomain) - 2]) || !isset($inputDomain[count($inputDomain) - 1])) {
        return false;
    }

    $inputDomain = '.' . $inputDomain[count($inputDomain) - 2] . '.' . $inputDomain[count($inputDomain) - 1];

    if (empty($hostDomain)) {
        $hostDomain = explode('.', parse_url(Yii::app()->request->getHostInfo(), PHP_URL_HOST));
        $hostDomain = '.' . $hostDomain[count($hostDomain) - 2] . '.' . $hostDomain[count($hostDomain) - 1];
    }

    if (strpos($inputDomain, $hostDomain) === false) {
        $status = false;
    } else {
        $status = true;
    }

    return $status;
}

function _serialize($val)
{
    return urlencode(serialize($val));
}

function _unserialize($val)
{
    return unserialize(urldecode(stripslashes($val)));
}

function outputString($string, $translate = false, $protected = false)
{
    if ($protected == true) {
        return htmlspecialchars($string);
    } else {
        if ($translate == false) {
            return parseInputFieldData($string, array('"' => '&quot;'));
        } else {
            return parseInputFieldData($string, $translate);
        }
    }
}

function parseInputFieldData($data, $parse)
{
    return strtr(trim($data), $parse);
}

function imageButton2($imageType, $url, $label = '', $btnWidthPx = '', $parameters = '')
{
    $returnString = '';
    $minusPx = 6;

    switch ($imageType) {
        case 'green' :
            $btnClass = 'green_btn';
            break;
        case 'yellow' :
            $btnClass = 'yellow_btn';
            break;
        case 'red' :
            $btnClass = 'red_btn';
            $minusPx = 29;
            break;
        case 'gray' :
            $btnClass = 'gray_btn';
            break;
        case 'gray_box':
            $btnClass = 'gray_box';
            break;
        case 'gray_short' :
            $btnClass = 'gray_short_btn';
            break;
        case 'gray_tall' :
            $btnClass = 'gray_tall_btn';
            break;
        case 'gray_big_tall' :
            $btnClass = 'gray_big_tall_btn';
            break;
    }

    $divStyle = notNull($btnWidthPx) ? ' style="width:' . $btnWidthPx . 'px"' : '';
    $parameters .= notNull($btnWidthPx) ? ' style="width:' . ($btnWidthPx - $minusPx) . 'px"' : '';

    $returnString = '<div class="main_btn ' . $btnClass . '"' . $divStyle . '><a href="' . outputString($url) . '"' . $parameters . '><font>' . $label . '</font><span></span></a></div>';

    return $returnString;
}

function parseTelephone($telephone, $id, $type = 'id')
{
    $country_code = '';
    $country_id = '';

    $telephone = preg_replace('/[^\d]/', '', $telephone);

    if ($type == 'id') {
        $m_c_data = CountriesBase::model()->findByPk($id);
        $country_id = (isset($m_c_data->countries_id) ? $m_c_data->countries_id : '');
        $country_code = (isset($m_c_data->countries_international_dialing_code) ? $m_c_data->countries_international_dialing_code : '');
    } else {
        if ($type == 'code') {
            $m_c_data = CountriesBase::model()->findByAttributes(array('countries_international_dialing_code' => $id));
            $country_id = (isset($m_c_data->countries_id) ? $m_c_data->countries_id : '');
            $country_code = $id;
        }
    }

    switch ($country_id) {
        case 105: // Italy (Fixed line has one elading zero but Mobile does not have)
            $extra_reg_rule = '(?:0)';
            break;
        default:
            $extra_reg_rule = '';
            break;
    }

    $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);

    if ($country_code != '') {
        while (strlen($telephone) > 10) {
            if (preg_match('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', $telephone)) {
                $telephone = preg_replace('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', '$2', $telephone);
                $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);
            } else {
                break;
            }
        }
    }

    switch ($country_id) {
        case 105: // Italy (Fixed line has one elading zero but Mobile does not have)
            if (substr($telephone, 0, 2) == '03') { // Mobile number
                $telephone = substr($telephone, 1);
            }
            break;
    }

    return $telephone;
}

function convertLinefeeds($from, $to, $string)
{
    return str_replace($from, $to, $string);
}

function getSafeQueryArray($excluding_array = array(), $url_query = '', $urlencode_val = true)
{
    $return_array = array();
    $url_query = $url_query !== '' ? $url_query : Yii::app()->request->getQueryString();

    if ($url_query) {
        if (is_array($url_query)) {
            $results = $url_query;
        } else {
            parse_str($url_query, $results);
        }

        if ($urlencode_val) {
            foreach ($results as $key => $val) {
                if (!is_array($val) && !in_array($key, $excluding_array)) {
                    $return_array[rawurlencode(stripslashes($key))] = rawurlencode(stripslashes(validateRequest($val)));
                }
            }
        } else {
            foreach ($results as $key => $val) {
                if (!is_array($val) && !in_array($key, $excluding_array)) {
                    $return_array[rawurlencode(stripslashes($key))] = stripslashes(validateRequest($val));
                }
            }
        }
    }

    return $return_array;
}

function isMobile(): bool
{
    $userAgent = ($_SERVER['HTTP_USER_AGENT'] ?? getenv('HTTP_USER_AGENT'));

    return preg_match("/gonative/i", $userAgent);
}

function stripLanguageFromUrl($url): string
{
    $pattern = "/(cn\/|zh-TW\/|id\/|cn\/)/im";

    return preg_replace($pattern, '', $url);
}
