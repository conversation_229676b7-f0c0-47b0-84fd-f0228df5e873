<?php

class MainCom extends CApplicationComponent {

    const ALL_USER_ACCESS_GROUP_ID = 0; // use as the compulsory included group id in query category with customer group 
    const OAUTH_RCI_SESSION_PREFIX = 'RCI'; //  scope: request_customer_info

    protected $proxy;

    public function __construct() {
        $this->proxy = isset(Yii::app()->params['PROXY']) ? Yii::app()->params['PROXY'] : null;
    }

    public function getRegional($key, $default = '') {
        // required enhancement to seperate the regional data from auth to another object
        return isset(Yii::app()->session[$key]) ? Yii::app()->session[$key] : $default; //isset(Yii::app()->auth) ? isset(Yii::app()->auth->regionalData[$key]) ? Yii::app()->auth->regionalData[$key] : $default : NULL;
    }

    protected function getUser($key, $default = '') {
        if ($key == 'id') {
            return notNull(Yii::app()->user->id) ? Yii::app()->user->id : 0;
        } else if ($key == 'customers_groups_id') {
            return isset(Yii::app()->session[$key]) ? Yii::app()->session[$key] : 1;
        } else {
            return isset(Yii::app()->session[$key]) ? Yii::app()->session[$key] : $default;
        }
    }

    public static function reportError($subject, $response_data, $ext_subject = '', $channel = "DEFAULT") {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' from ' . ((!empty($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : '') . ' - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $response_data
            )
        );
        Yii::app()->slack->send($subject, $attachments, $channel);
    }

}
