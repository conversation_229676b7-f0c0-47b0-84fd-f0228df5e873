<?php

class MainModel extends CActiveRecord {
    protected $conn;
    
    public function __construct($scenario='insert') {
        $this->conn = Yii::app()->db;
        parent::__construct($scenario);
    }
    
    protected function lockTable($locktype) {
        return Yii::app()->db->createCommand("LOCK TABLES `".$this->tableName()."` ".$locktype.", `".$this->tableName()."` AS `".$this->getTableAlias()."` ".$locktype)->execute();
    } 
            
    protected function unlockTable() {
        return Yii::app()->db->createCommand('UNLOCK TABLES;')->execute();
    }
    
    public function saveNewRecord($data, $returnPk = false) {
        // Making sure our PK is null before inserting it
        $table = $this->getMetaData()->tableSchema;
        $primaryKey = $table->primaryKey;
        
        if (is_array($primaryKey)) {
            foreach ($primaryKey as $primaryKeyValue) {
                $this->unsetAttributes(array($primaryKeyValue));
            }
        } else {
            $this->unsetAttributes(array($primaryKey));
        }
        
        foreach ($data as $field => $value) {
            //Checks whether this AR has the named attribute
            if ($this->hasAttribute($field)) {
                if ($value == null) {
                    $this->unsetAttributes(array($field));
                } else {
                    $this->setAttribute($field, $value);
                }
            }
        }

        // Insert requires it to be flagged as new record
        $this->isNewRecord = true;
        $status = $this->insert();
        
        if ($returnPk) {
            if (is_array($primaryKey)) {
                return $this;
            } else {
                return $this->$primaryKey;
            }
        } else {
            return $status;
        }
    }
}
