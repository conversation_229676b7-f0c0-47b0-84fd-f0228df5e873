<?php
// Common Main Config
// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

return array(
    // preloading 'log' component
    'preload' => array('log'),
    // autoloading model and component classes
    'import' => array(
        'common.components.*',
        'common.components.core.*',
        'common.models.*',
        // The following two imports are polymorphic and will resolve against wherever the `basePath` is pointing to.
        // We have components and models in all entry points anyway
        'application.models.*',
        'application.components.*',
        'application.components.core.*',
    ),
    'modules' => array(),
    // application components
    'components' => array(
        'curl' => array(
            'class' => 'common.components.CurlCom'
        ),
        'geoip' => array(
            'class' => 'common.extensions.maxmind.MaxmindGeoIP',
        ),
    ),
    // application-level parameters that can be accessed
    // using Yii::app()->params['paramName']
    'params' => array(
        'MEMCACHE_PREFIX' => 'OffGamers/',
        
        // used in yiiGeoip & geoip
        'DEFAULT_TIME_ZONE' => 'Asia/Kuala_Lumpur',
        
        # System log identifier
        'LOG' => array(
            'SC_ACTIVITY_TYPE_PURCHASE' => 'P'
        ),
    ),
    // default website language
    // use Yii::app()->language = 'en' to change website global language
    'language' => 'en',
);