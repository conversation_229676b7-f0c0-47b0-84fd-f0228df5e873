<?php

// Common Local Config
// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

return array(
    'name' => '[Staging] OffGamers Online Game Store',
    'modules' => array(
        'gii' => array(
            'class' => 'system.gii.GiiModule',
            'password' => 'abc',
            // If removed, G<PERSON> defaults to localhost only. Edit carefully to taste.
            'ipFilters' => array(
                '127.0.0.1',
                '::1'
            ),
        ),
    ),
    // application components
    'components' => array(
        'db' => array(
            'connectionString' => 'mysql:host=localhost;dbname=offgamers',
            'schemaCachingDuration' => 86400000, // 86400000 == 60*60*24*1000 seconds == 1000 days
            'emulatePrepare' => true,
            'enableParamLogging' => false,
            'username' => 'root',
            'password' => 'root',
            'charset' => 'latin1',
        ),
        'db_rr' => array(
            'class' => 'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname=offgamers',
            'schemaCachingDuration' => 86400000, // 86400000 == 60*60*24*1000 seconds == 1000 days
            'emulatePrepare' => true,
            'enableParamLogging' => false,
            'username' => 'root',
            'password' => 'root',
            'charset' => 'latin1',
        ),
        'db_offgamers' => array(
            'class' => 'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname=offgamers',
            'schemaCachingDuration' => 86400000, // 86400000 == 60*60*24*1000 seconds == 1000 days
            'emulatePrepare' => true,
            'enableParamLogging' => false,
            'username' => 'root',
            'password' => 'root',
            'charset' => 'utf8',
        ),
        'db_og' => array(
            'class' => 'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname=og',
            'schemaCachingDuration' => 86400000, // 86400000 == 60*60*24*1000 seconds == 1000 days
            'emulatePrepare' => true,
            'enableParamLogging' => false,
            'username' => 'root',
            'password' => 'root',
            'charset' => 'utf8mb4',
        ),
        'db_ogm_session' => array(
            'class' => 'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname=ogmsession',
            'schemaCachingDuration' => 86400000, // 86400000 == 60*60*24*1000 seconds == 1000 days
            'emulatePrepare' => true,
            'enableParamLogging' => false,
            'username' => '',
            'password' => '',
            'charset' => 'utf8',
        ),
        'log' => array(
            'class' => 'CLogRouter',
            'routes' => array(
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error',
                    'categories' => ['exception.CHttpException.500'],
                ),
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error',
                    'except' => ['exception.CHttpException.*'],
                ),
            ),
        ),
        'geoip' => array(
            'filename' => COMMON_DIR . '/extensions/maxmind/db/GeoLite2-Country.mmdb',
        ),
        'cache' => array(
            'class' => 'system.caching.CMemCache',
            'keyPrefix' => "ogm/",
            'useMemcached' => true,
            'hashKey' => false,
            'servers' => array(
                array(
                    'host' => 'localhost',
                    'port' => 11211,
                ),
            ),
        ),
        'redis_cache' => array(
            'class' => 'CRedisCache',
            'hostname' => 'localhost',
            'port' => 6379,
        ),
        'slack' => array(
            'class' => 'common.components.SlackCom',
            'webhook' => array(
                'DEFAULT' => 'T0E3H6QEN/BECKA16JF/4OgPr2JxIXXekbB47RsIV2GX',
                'CANCEL_ORDER' => 'T0E3H6QEN/BECKA16JF/4OgPr2JxIXXekbB47RsIV2GX',
                'ANB_TAX_SUBMISSION' => 'T7J4ZQ065/BB9T1ERRN/UHl64ecctk4SZZWoqehnZ7Uv',
                'DEV_DEBUG' => 'TFQ825EGN/BFTT71D60/UsqxaRyM3HvV45CZX1oOfkP7',
                'DEV_DEBUG_CHECKOUT' => 'TFQ825EGN/BFTT71D60/UsqxaRyM3HvV45CZX1oOfkP7',
                'BDT' => 'TFQ825EGN/BFTT71D60/UsqxaRyM3HvV45CZX1oOfkP7',
                'dtu' => 'TFQ825EGN/BFTT71D60/UsqxaRyM3HvV45CZX1oOfkP7',
            )
        ),
    ),
    // application-level parameters that can be accessed
    'params' => array(
        'AWS_CONFIG' => array(
            'AWS_APP_KEY' => '********************',
            'AWS_APP_SECRET' => 'TCXgvtSHYljoZsng9G1+sm10smNn6aF1rK1KoKjZ',
            'BUCKETS_ARRAY' => array(
                'BUCKET_STATIC' => array(
                    'bucket' => 'dev.carerra.static',
                    'domain' => 'dev.carerra.static.s3.amazonaws.com/',
                    'enabled' => 'true',
                    'enabled_cf' => 'true'
                ),
                'BUCKET_SECURE' => array(
                    'bucket' => 'dev.carerra.secure',
                    'domain' => 'dev.carerra.secure.s3.amazonaws.com/',
                    'enabled' => 'true',
                    'enabled_cf' => 'false'
                ),
                'BUCKET_UPLOAD' => array(
                    'bucket' => '',
                    'domain' => '',
                    'enabled' => 'true',
                    'enabled_cf' => 'false'
                )
            ),
        ),
        // OAuth URL
        'OAUTH_DOMAIN' => 'https://staging-oauth.shasso.com/index.php/oauth2/',

        // OG Crew URL
        'OG_CREW_DOMAIN' => 'https://crew.offgamers.biz',

        # Proxy
        'PROXY' => 'proxy://my-proxy.offgamers.lan:3128',

        #debugging
        'DEV_DEBUG_EMAIL_SUBJECT_PREFIX' => '[OG-Common]',
        
        'SHASSO_CONFIG' => array(
            'MERCHANT_API_ACC' => 'ogm',
            'MERCHANT_API_SECRET' => '123456',
            'SHASSO_URI' => 'https://account.offgamers.com',
            'API_URI' => 'https://account-api.offgamers.com/api',
        ),

        'SECURE_CIPHER' => 'rijndael-128',
        'SECURE_KEY' => '2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8',
        'SECURE_KEY_IV' => '9S?C3B4WMQW6XM8P',
        
        'GIFT_CARD' => array(
            'PRODUCT' => array(
                'ogc' => array(
                    'api' => array(
                        'merchant' => 'OGM',
                        'secret' => 'TESTOGM'
                    ),
                ),
            ),
        ),

        // reCaptcha
        'RECAPTCHA_CONFIG' => array(
            'RECAPTCHA_API_SERVER' => 'http://www.google.com/recaptcha/api/siteverify',
            'RECAPTCHA_API_SECURE_SERVER' => 'https://www.google.com/recaptcha/api/siteverify',
            'PUBLIC_KEY' => '6Le7wP4SAAAAAGugKDZ8GVLvS0r7SERijAZjlJDy',
            'PRIVATE_KEY' => '6Le7wP4SAAAAAJDu6TLQImZOyf2MBDHc4FwEyN2t',
        ),

        // MTCaptcha
        'MTCAPTCHA_CONFIG' => array(
            'API_SERVER' => 'https://service.mtcaptcha.com/mtcv1/api',
            'SITE_KEY' => 'MTPublic-sYgVxmur6',
            'PRIVATE_KEY' => 'MTPrivat-sYgVxmur6-fSWPcSOBc1htbsJu1AUUG7ZHOf6b5CQSFlUVrGlPNe7iOAm6Gd',
        ),
        
        // Store Credit Micro Service
        'MICROSERVICE_STORECREDIT' => array(
            'BASEURL' => 'http://storecredit.offgamers.local',
            'KEY' => 'frontend',
            'SECRET' => '123456',
            'MS_STATUS' => 1, // 0-Disable, 1-Active
        ),
        
        'ANB_EMAIL_TAX_SUBMISSION' => '',
    ),
);
