<?php

class dtu_dtonedvs extends DirectTopupCom
{
    public $title, $enable, $game_code;

    private $request_url, $key, $secret;

    private $error_response;

    function dtu_dtonedvs()
    {
        $this->title = 'DTOneDVS';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        list($prefix, $number) = explode(" ", $games_acc_array['account']);
        return MsProductModel::isPhoneNumberValidated($prefix, $number);
    }

}