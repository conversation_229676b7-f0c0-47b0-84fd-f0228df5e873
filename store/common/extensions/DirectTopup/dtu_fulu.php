<?php

class dtu_fulu extends DirectTopupCom
{
    public $title, $enable;

    private $request_url, $api_secret, $merchant_id;

    const GAME_TOP_UP = 'G', DATA_TOP_UP = 'D', TALKTIME_TOP_UP = 'T';

    function dtu_fulu()
    {
        $this->title = 'Fulu';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $curl_response_array['result_code'] = 2000;
        $server_list = json_decode($this->get_publishers_games_conf($games_acc_array['publishers_games_id'],
            'ACCOUNT_CHARACTER_LIST'), 1);
        return (isset($server_list[$games_acc_array['server']]) ? $server_list[$games_acc_array['server']] : array(0 => '预设'));
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $result_code = '1000';
        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();
        $pid = $games_acc_array['product_id'];
        if ($value = TopUpInfoBase::getTopUpInfoVal($pid, 'product_code')) {
            $top_up_info_product_code = $value;
            list($product_code, $product_type) = explode("_", $top_up_info_product_code);
            $this->merchant_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
            $this->api_secret = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];
            $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
            switch ($product_type) {
                case self::GAME_TOP_UP:
                    // Game DTU doesn't have validate game account feature
                    $result_code = '2000';
                    break;
                case self::DATA_TOP_UP:
                case self::TALKTIME_TOP_UP:
                    $deno = TopUpInfoBase::getTopUpInfoVal($pid, 'amount');
                    if ($this->validatePhoneAcc($games_acc_array['account'], $deno)) {
                        $result_code = '2000';
                    }
                    else{
                        $curl_response_array['game_acc_status'] = 'NOT_RELOADABLE';
                    }
                    break;
                default;
                    break;
            }

        }

        return ($result_code == '2000' ? true : false);
    }

    private function getRequestTimestamp()
    {
        return date("Y-m-d H:i:s");
    }

    private function generatePostFields($params = array(), $secret, $_secretOnly = false)
    {
        ksort($params);
        $postFields = urldecode(http_build_query($params, '', '&'));
        if ($_secretOnly == true) {
            return $this->generateSignature($postFields . $secret);
        }
        $postFields .= '&sign=' . $this->generateSignature($postFields . $secret);
        return $postFields;
    }

    private function generateSignature($str)
    {
        return md5($str);
    }

    public function validatePhoneAcc($account, $deno)
    {
        $action = 'kamenwang.phonegoods.checkandgetprice';
        $params = array(
            'customerid' => $this->merchant_id,
            'method' => $action,
            'timestamp' => $this->getRequestTimeStamp(),
            'chargephone' => $account,
            'chargeparvalue' => $deno,
            'v' => '1.0'
        );

        $response = $this->processRequest($action, $params);
        if ($response) {
            return true;
        }
        return false;
    }


    private function processRequest($action, $params = array())
    {
        $request_log = 'url: ' . $this->request_url . "\n";
        ob_start();
        echo "<pre>";
        print_r($params);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new DirectTopupApiLogCom($action, $request_log,
            array('publishers_id' => (int)$this->publishers_id));

        $postFields = $this->generatePostFields($params, $this->api_secret);

        $response = Yii::app()->curl->sendPost($this->request_url, $postFields);
        $data = explode("\r\n\r\n", $response);
        $curl_response = end($data);
        $response = $this->parse_xml_to_array($curl_response);
        ob_start();
        echo "<pre>";
        print_r($response);
        $response_log = ob_get_contents();
        ob_end_clean();
        $api_log_obj->end_log('2000', $response_log, array('publishers_id' => $this->publishers_id));
        if ($this->validateResponse($response)) {
            return true;
        } else {
            return false;
        }
    }

    private function validateResponse($response)
    {
        if (isset($response['MessageCode']) && $response['MessageCode'] != 2400) {
            return false;
        } else {
            return true;
        }
    }

    public function parse_xml_to_array($raw_xml)
    {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != null) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }

        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }

}