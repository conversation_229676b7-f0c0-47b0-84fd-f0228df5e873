<?php

class dtu_fuluv2 extends DirectTopupCom
{
    public $title, $enable, $game_code;

    private $request_url, $key, $secret;

    private $error_response;

    function dtu_fuluv2()
    {
        $this->title = 'Fulu V2';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        try {
            $model = new InventoryModel();
            $this->publishers_id = $publisher_id;
            $get_publishers_conf_array = $this->get_publishers_conf();
            $publishers_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];

            $product_setting = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'ACCOUNT_PRODUCT_SETTING_LIST');
            $product_setting = json_decode($product_setting, 1);

            $request_data = [
                'publisher_id' => $publishers_id,
                'account' => (!empty($games_acc_array['account']) ? $games_acc_array['account'] : ''),
                'character' => (!empty($games_acc_array['character']) ? $games_acc_array['character'] : ''),
                'server' => (!empty($games_acc_array['server']) ? $games_acc_array['server'] : ''),
                'platform' => (!empty($games_acc_array['account_platform']) ? $games_acc_array['account_platform'] : ''),
                'publisher_product_id' => (!empty($games_acc_array['product_code']) ? $games_acc_array['product_code'] : ''),
                'product_setting' => $product_setting
            ];

            if ($model->validateFuluDTU($request_data) === true) {
                return true;
            }
        } catch (Exception $e) {
        }

        $curl_response_array = ['game_acc_status' => 'NOT_RELOADABLE'];
        return false;
    }

}