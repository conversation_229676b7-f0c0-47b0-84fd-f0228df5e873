<?php

class dtu_indomog extends DirectTopupCom {

    var $title, $enable;

    const RESERVE_METHOD = '5001';
    const RESERVE_METHOD_NAME = 'Reserve';

    public $request_type = '';
    public $request_type_name = '';
    public $secret_key = '123456';

    function dtu_indomog() {
        $this->title = 'Indomog';
        $this->enable = true;
    }

    function get_title() {
        return $this->title;
    }

    function get_enable() {
        return $this->enable;
    }

    private function generateRequestXML($param) {

        $return_xml = '<?xml version="1.0"?>' .
                '<methodCall>' .
                '<methodName>' . $this->request_type_name . '</methodName>' .
                '<params>' .
                '<param>' .
                '<value>' .
                '<struct>';

        $data_type = 'string';

        foreach ($param as $name => $value) {
            if ($name === 'Now') {
                $data_type = 'datetime.iso8601';
            }

            $return_xml .= '<member>
                                 <name>' . $name . '</name>
                                 <value><' . $data_type . '>' . $value . '</' . $data_type . '></value>
                           </member>';
        }

        $return_xml .= '</struct>' .
                '</value>' .
                '</param>' .
                '</params>' .
                '</methodCall>';

        return $return_xml;
    }

    private function generateSignature($param) {
        return sha1(implode('', $param) . $this->secret_key);
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $action = 'validate_game_acc';
        $error = true;
        $result_code = '1000';
        $log_response = '';
        $response_signature = '';
        $request_signature = '';
        $indomog_qid = '';

        $this->request_type = self::RESERVE_METHOD;
        $this->request_type_name = self::RESERVE_METHOD_NAME;

        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $validate_game_acc_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_VALIDATE_GAME_ACC_URL_FLAG');

            if (!$validate_game_acc_url_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'VALIDATE_GAME_ACC_URL');
            }
        }

        if (!notNull($url)) {
            $url = $get_publishers_conf_array['VALIDATE_GAME_ACC_URL']['publishers_configuration_value'];
        }

        if (!notNull($url)) {
            return true;
        }

        $this->secret_key = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];

        $publishers_remark = '';

        $select = " SELECT publishers_remark
                    FROM " . PublishersBase::model()->tableName() . "
                    WHERE publishers_id = :publishers_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":publishers_id", $publisher_id, PDO::PARAM_INT);

        if ($value = $command->queryScalar()) {
            $publishers_remark = $value;
        }


        $indomog_pid = '';

        $select = " SELECT top_up_info_value
                    FROM " . TopUpInfoBase::model()->tableName() . "
                    WHERE top_up_info_key = 'product_code'
                        AND products_id = :products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":products_id", $games_acc_array['product_id'], PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $indomog_pid = $value;
        }

        $email_hp = $games_acc_array['account'];
        $timestamp_iso8601 = date('Ymd\TH:i:s');

        $indomog_dtu_logs_obj = new IndomogDtuLogsBase();
        $indomog_dtu_logs_store_id = $indomog_dtu_logs_obj->create_indomog_log($email_hp, $games_acc_array['product_id'], $indomog_pid, strtoupper($this->request_type_name));
        $indomog_qid = date('Ymd') . substr(str_pad($indomog_dtu_logs_store_id, 4, 0, STR_PAD_LEFT), -4);


        $param = array(
            'RMID' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
            'QID' => $indomog_qid,
            'RC' => $this->request_type,
            'IPD' => getIPAddress(),
            'EmailHP' => $email_hp,
            'ProdID' => $indomog_pid,
            'Remark' => $publishers_remark,
            'Now' => $timestamp_iso8601,
        );


        $request_signature = $this->generateSignature($param);
        $param['Signature'] = $request_signature;

        /* Start API Request Log */

        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();
        $api_log_obj = new DirectTopupApiLogCom($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $indomog_verify_log_id = $api_log_obj->api_log_id;
        $raw_response = '';
        $curl_response = $this->curl_send_request_with_xml($url, $param, $raw_response);
        $xml_array = $curl_response;
        $response_signature = isset($curl_response['Signature']) ? $curl_response['Signature'] : '';

        if (is_array($xml_array)) {
            if ($response_signature == $request_signature && isset($xml_array['RspCode']) && $xml_array['RspCode'] == '000' && isset($xml_array['QID']) && $xml_array['QID'] == $indomog_qid) {
                if ($indomog_dtu_logs_obj->update_indomog_log($indomog_dtu_logs_store_id, array('indomog_verify_log_id' => $indomog_verify_log_id, 'indomog_qid' => $indomog_qid))) {
                    $error = false;
                    $result_code = '2000';
                }
            } else {
                $result_code = '2000';
            }
        }

        if ($error) {
            $curl_response_array['game_acc_status'] = 'NOT_RELOADABLE';
            $curl_response_array['result_code'] = $result_code;
        }

        ob_start();
        echo "<pre>";
        print_r($xml_array);
        $log_response .= ob_get_contents();
        ob_end_clean();

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $log_response, array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return ($error === false ? true : false );
    }

    private function curl_send_request_with_xml($url, $param, & $curl_response = '') {
        $curl_obj = new CurlCom();
        $curl_obj->addHeader('Content-Type', 'text/xml'); // add http_header  (CURLOPT_HTTPHEADER)
        $curl_response = $curl_obj->sendPost($url, $this->generateRequestXML($param));
        list($header, $body) = explode("\r\n\r\n", $curl_response, 2);
        $response = $this->_parse_xml_to_array($body);
        $response_array = array();
        if (isset($response['params']['param']['value']['struct']['member']) && is_array($response['params']['param']['value']['struct']['member'])) {
            foreach ($response['params']['param']['value']['struct']['member'] as $name => $attribute) {
                foreach ($attribute['value'] as $value) {
                    $response_array[$attribute['name']] = $value;
                }
            }
        }
        return $response_array;
    }

    private function _parse_xml_to_array($raw_xml) {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != NULL) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }

        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }

}
