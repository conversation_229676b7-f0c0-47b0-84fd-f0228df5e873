<?php

class dtu_junnet extends DirectTopupCom {

    var $title, $enable;

    function dtu_junnet() {
        $this->title = 'Junnet';
        $this->enable = true;
    }

    function get_title() {
        return $this->title;
    }

    function get_enable() {
        return $this->enable;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $action = 'validate_game_acc';
        $result_code = '1000';
        $log_response = '';

        $cache_key = 'DTU_JUNNET/price_list/array/game_id/' . $games_acc_array['publishers_games_id'];
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $xml_array = $cache_result;
        } else {
            $this->publishers_id = $publisher_id;
            $get_publishers_conf_array = $this->get_publishers_conf();

            if (!isset($get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) {
                return true;
            }

            $url = '';
            if (isset($games_acc_array['publishers_games_id'])) {
                $validate_game_acc_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_VALIDATE_GAME_ACC_URL_FLAG');

                if (!$validate_game_acc_url_flag) {
                    $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'VALIDATE_GAME_ACC_URL');
                }
            }

            if (!notNull($url)) {
                $url = $get_publishers_conf_array['VALIDATE_GAME_ACC_URL']['publishers_configuration_value'];
            }

            if (!notNull($url)) {
                return true;
            }

            $agent_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
            $use_type = (trim($games_acc_array['game']) == 'AATRXXMXVV' ? 5 : 3);
            //$use_type = 3;
            $time_stamp = date('YmdHis');
            $param = 'agent_id=' . $agent_id . '&use_type=' . $use_type . '&time_stamp=' . $time_stamp;
            $sign = strtolower(md5($param . '|||' . $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']));
            $param .= '&category_code=' . $games_acc_array['game'] . '&sign=' . $sign;

            /* Start API Request Log */
            $request_log = 'url: ' . $url . '?' . $param . "\n";
            $api_log_obj = new DirectTopupApiLogCom($action, $request_log, array('publishers_id' => (int) $publisher_id));
            /* End API Request Log */

            $response = Yii::app()->curl->sendPost($url, $param);
            $response = explode("\r\n\r\n", $response);
            $curl_response = end($response);
            $xml_array = $this->_parse_xml_to_array($curl_response);
            Yii::app()->cache->set($cache_key, $xml_array, 900);
        }

        $top_up_info_product_code = '';

        $select = " SELECT top_up_info_value
                    FROM " . TopUpInfoBase::model()->tableName() . "
                    WHERE top_up_info_key = 'product_code'
                        AND products_id = :products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":products_id", $games_acc_array['product_id'], PDO::PARAM_INT);

        if ($value = $command->queryScalar()) {
            $top_up_info_product_code = $value;
        }

        if (is_array($xml_array['ProductList']) && is_array($xml_array['ProductList']['ProductItem'])) {
            $game_product_list = $xml_array['ProductList']['ProductItem'];

            for ($gamePrdCnt = 0, $totalCnt = count($game_product_list); $gamePrdCnt < $totalCnt; $gamePrdCnt++) {
                if ($game_product_list[$gamePrdCnt]['@attributes']['CategoryCode'] == $games_acc_array['game'] && $game_product_list[$gamePrdCnt]['@attributes']['ProductCode'] == $top_up_info_product_code) {
                    $log_response = json_encode($game_product_list[$gamePrdCnt]['@attributes']);
                    $cost_price = $game_product_list[$gamePrdCnt]['@attributes']['PurchasePrice'];
                    $selling_price = 0;

                    $select = " SELECT products_price, products_base_currency
                                FROM " . ProductsBase::model()->tableName() . " 
                                WHERE products_id = :products_id";
                    $command = Yii::app()->db->createCommand($select);
                    $command->bindParam(":products_id", $games_acc_array['product_id'], PDO::PARAM_INT);

                    if ($selling_price_row = $command->queryRow()) {
                        if ($selling_price_row['products_base_currency'] == 'CNY') {
                            $selling_price = $selling_price_row['products_price'];
                        } else {
                            $currency_obj = new CurrenciesCom();
                            $selling_price = $currency_obj->advanceCurrencyConversion($selling_price_row['products_price'], $selling_price_row['products_base_currency'], 'CNY');
                            unset($currency_obj);
                        }
                    }

                    $product_margin = number_format(($selling_price - $cost_price) / $selling_price * 100, 2, '.', '');

                    if ($product_margin < 3.00) {
                        // do not proceed and email out
                        $email_message = 'Product ID: ' . $games_acc_array['product_id'] . ' not able to Add-to-cart now due to low margin' . "\n\n" .
                                'Selling Price: CNY ' . $selling_price . "\n" .
                                'Cost Price: CNY ' . $cost_price . "\n" .
                                'Margin: ' . $product_margin . '%';
                        
                        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - Junnet Product(' . $games_acc_array['product_id'] . ') Margin Low';
                        $attachments = array(
                            array(
                                'color' => 'danger',
                                'text' => $email_message
                            )
                        );
                        Yii::app()->slack->send($subject, $attachments, 'dtu');
                    } else if ($product_margin < 7.00) {
                        // Just email out
                        $result_code = '2000';

                        $email_message = 'Product ID: ' . $games_acc_array['product_id'] . ' still able to Add-to-cart but approaching low margin' . "\n\n" .
                                'Selling Price: CNY ' . $selling_price . "\n" .
                                'Cost Price: CNY ' . $cost_price . "\n" .
                                'Margin: ' . $product_margin . '%';

                        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - Junnet Product(' . $games_acc_array['product_id'] . ') Margin Low';
                        $attachments = array(
                            array(
                                'color' => 'danger',
                                'text' => $email_message
                            )
                        );
                        Yii::app()->slack->send($subject, $attachments, 'dtu');
                    } else {
                        $result_code = '2000';
                    }
                    break;
                }
            }
        }

        if ($cache_result === FALSE) {
            /* Start API Response Log */
            $api_log_obj->end_log($result_code, $log_response, array('publishers_id' => (int) $publisher_id));
            /* End API Response Log */
        }

        return ($result_code == '2000' ? true : false );
    }

    // Use to retrieve Junnet Server List
    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $action = 'get_character_list';

        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $get_character_list_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_CHARACTER_URL_FLAG');
            if (!$get_character_list_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CHARACTER_URL');
            }
        }

        if (!notNull($url)) {
            $url = $get_publishers_conf_array['CHARACTER_URL']['publishers_configuration_value'];
        }

        if (!notNull($url)) {
            return true;
        }

        $url = $url . '?RegionID=' . $games_acc_array['server'];

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        $api_log_obj = new DirectTopupApiLogCom($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $curl_obj = new curl();
        $response = Yii::app()->curl->sendPost($url, '');
        $response = explode("\r\n\r\n", $response);
        $curl_response = end($response);

        $xml_array = $this->_parse_xml_to_array($curl_response);

        if (count($xml_array['GameServerInfo']['GameServerItem']) > 0) {
            if (count($xml_array['GameServerInfo']['GameServerItem']) == 1) {
                foreach ($xml_array['GameServerInfo']['GameServerItem'] as $server_array) {
                    $curl_response_array['characters'][$server_array['ServerID']] = $server_array['ServerName'];
                }
            } else {
                foreach ($xml_array['GameServerInfo']['GameServerItem'] as $server_array) {
                    foreach ($server_array as $server_element) {
                        $curl_response_array['characters'][$server_element['ServerID']] = $server_element['ServerName'];
                    }
                }
            }
        }

        $curl_response_array['result_code'] = 2000; // Used by caller to indicate process is success

        /* Start API Response Log */
        $api_log_obj->end_log($curl_response_array['result_code'], @json_encode($curl_response_array), array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return (isset($curl_response_array['characters']) ? $curl_response_array['characters'] : array());
    }

    private function _parse_xml_to_array($raw_xml) {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != NULL) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }

        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }

}
