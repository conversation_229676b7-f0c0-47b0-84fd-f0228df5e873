<?php

class dtu_offgamers extends DirectTopupCom
{

    var $title, $enable;

    function dtu_offgamers()
    {
        $this->title = 'OffGamers';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $action = 'validate_game_acc';

        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $validate_game_acc_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_VALIDATE_GAME_ACC_URL_FLAG');

            if (!$validate_game_acc_url_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'VALIDATE_GAME_ACC_URL');
            }

            $customer_id_conversion = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');

            if ($customer_id_conversion == '1') {
                $converted_account = $this->convert_customer_email_to_id($games_acc_array['account']);
                $games_acc_array['account'] = !empty($converted_account) ? $converted_account : $games_acc_array['account'];
            }
        }

        if (!notNull($url)) {
            $url = $get_publishers_conf_array['VALIDATE_GAME_ACC_URL']['publishers_configuration_value'];
        }

        if (!notNull($url)) {
            return true;
        }

        $param = array(
            'action' => $action,
            'account' => '',
            'character' => '',
            'game' => '',
            'server' => '',
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']
        );
        if (isset($games_acc_array['account'])) {
            $param['account'] = $games_acc_array['account'];
        }
        if (isset($games_acc_array['character'])) {
            if (stristr($games_acc_array['character'], '##')) {
                $character_tmp = explode("##", $games_acc_array['character']);
                $param['character'] = $character_tmp[0];
            } else {
                $param['character'] = $games_acc_array['character'];
            }
        }
        if (isset($games_acc_array['account_platform'])) {
            $param['account_platform'] = $games_acc_array['account_platform'];
        }

        if (isset($games_acc_array['game'])) {
            $param['game'] = $games_acc_array['game'];
        }
        if (isset($games_acc_array['server'])) {
            $param['server'] = $games_acc_array['server'];
        }

        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* optional, don't have to include in signature , for publisher use to do their own topup limit checking */
        if (isset($games_acc_array['amount_type'])) {
            $param['amount_type'] = $games_acc_array['amount_type'];
        }
        if (isset($games_acc_array['amount'])) {
            $param['amount'] = $games_acc_array['amount'];
        }
        if (isset($games_acc_array['quantity'])) {
            $param['quantity'] = $games_acc_array['quantity'];
        }
        /* end optional, don't have to include in signature */

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new DirectTopupApiLogCom($action, $request_log, array('publishers_id' => (int)$publisher_id));
        /* End API Request Log */

        $curl_response = $this->curl_post($url, $param);
        $curl_response_array = json_decode($curl_response, true);
        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        // check response signature
        $response_signature = sha1('game_acc_status=' . (isset($curl_response_array['game_acc_status']) ? $curl_response_array['game_acc_status'] : '') . '&result_code=' . (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : '') . '&secret_key=' . $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);
        if (!isset($curl_response_array['signature']) || strcmp($response_signature, $curl_response_array['signature']) != 0) {
            $result_code = '1002';
            $attachment = array(
                array(
                    'color' => 'warning',
                    'text' => json_encode([
                        'request_params' => $param,
                        'response' => $curl_response,
                        'publisher_id' => $publisher_id

                    ])
                )
            );
            Yii::app()->slack->send('Fail to validate signature from DTU publisher', $attachment, 'DEFAULT');
        }

        // end check response signature

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int)$publisher_id));
        /* End API Response Log */

        return ($result_code == '2000' && isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'RELOADABLE' ? true : false);
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $action = 'get_character_list';

        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $get_character_list_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_CHARACTER_URL_FLAG');

            if (!$get_character_list_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CHARACTER_URL');
            }

            $customer_id_conversion = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');

            if ($customer_id_conversion == '1') {
                $converted_account = $this->convert_customer_email_to_id($games_acc_array['account']);
                $games_acc_array['account'] = !empty($converted_account) ? $converted_account : $games_acc_array['account'];
            }
        }

        if (!notNull($url)) {
            $url = $get_publishers_conf_array['CHARACTER_URL']['publishers_configuration_value'];
        }

        if (!notNull($url)) {
            return true;
        }

        $param = array(
            'action' => $action,
            'account' => '',
            'game' => '',
            'server' => '',
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']
        );
        if (isset($games_acc_array['account'])) {
            $param['account'] = $games_acc_array['account'];
        }
        if (isset($games_acc_array['game'])) {
            $param['game'] = $games_acc_array['game'];
        }
        if (isset($games_acc_array['server'])) {
            $param['server'] = $games_acc_array['server'];
        }

        if (isset($games_acc_array['account_platform'])) {
            $param['account_platform'] = $games_acc_array['account_platform'];
        }

        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new DirectTopupApiLogCom($action, $request_log, array('publishers_id' => (int)$publisher_id));
        /* End API Request Log */

        $curl_response = $this->curl_post($url, $param);
        $curl_response_array = json_decode($curl_response, true);
        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int)$publisher_id));
        /* End API Response Log */

        return (isset($curl_response_array['characters']) ? $curl_response_array['characters'] : array());
    }

    function get_signature($param, $key)
    {
        $action = array();

        if (isset($param['action'])) {
            $action['action'] = $param['action'];
        }

        unset($param['action']);
        ksort($param);
        reset($param);

        $param = array_merge($action, $param);

        $signature_array = array();
        foreach ($param as $key_loop => $data_loop) {
            $signature_array[] = $key_loop . '=' . $data_loop;
        }
        $signature_array[] = 'secret_key=' . $key;

        return sha1(implode("&", $signature_array));
    }

    private function curl_post($url, $data)
    {
        $ch = curl_init($url);

        $postfields = '';
        if (is_array($data)) {
            while (list($key, $value) = each($data)) {
                $postfields .= $key . '=' . urlencode($value) . '&';
            }
        } else {
            $postfields = $data;
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));

        // Verification of the SSL cert
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);    // false
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);   // 2 // should be false

        $response = curl_exec($ch);

        if ($curl_errno = curl_errno($ch)) {
            self::reportError('dtu_offgamers:curl_post', array(
                    'response' => $response,
                    'errno' => $curl_errno,
                    'error' => curl_error($ch)
                )
            );
        }

        curl_close($ch);

        return $response;
    }

}