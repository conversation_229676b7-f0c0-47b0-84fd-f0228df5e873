<?php

class dtu_pima extends DirectTopupCom
{
    public $title, $enable;
    function dtu_pima()
    {
        $this->title = 'Pima';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $curl_response_array['result_code'] = 2000;
        $server_list = json_decode($this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'ACCOUNT_CHARACTER_LIST'), 1);
        return (!empty($server_list) ? $server_list : array(0 => 'Please Select'));
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        return true;
    }
}