<?php

class dtu_razer extends DirectTopupCom
{
    public $title, $enable;

    function dtu_razer()
    {
        $this->title = 'Razer';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        try {
            $model = new InventoryModel();
            $this->publishers_id = $publisher_id;
            $get_publishers_conf_array = $this->get_publishers_conf();
            $publishers_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];

            $request_data = [
                'publisher_id' => $publishers_id,
                'customer_id' => Yii::app()->user->id,
                'account' => (!empty($games_acc_array['account']) ? $games_acc_array['account'] : ''),
                'character' => (!empty($games_acc_array['character']) ? $games_acc_array['character'] : ''),
                'server' => (!empty($games_acc_array['server']) ? $games_acc_array['server'] : ''),
                'platform' => (!empty($games_acc_array['platform']) ? $games_acc_array['platform'] : ''),
                'publisher_product_id' => (!empty($games_acc_array['product_code']) ? $games_acc_array['product_code'] : ''),
            ];

            if ($model->validateRazerDTU($request_data) === true) {
                return true;
            }

        } catch (Exception $e) {
            // DO Nothing, Response as not reloadable
        }

        $curl_response_array = ['game_acc_status' => 'NOT_RELOADABLE'];
        return false;
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        try {
            $curl_response_array['result_code'] = 1006;

            $model = new InventoryModel();
            $this->publishers_id = $publisher_id;
            $get_publishers_conf_array = $this->get_publishers_conf();
            $publishers_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];

            $request_data = [
                'publisher_id' => $publishers_id,
                'customer_id' => Yii::app()->user->id,
                'account' => (!empty($games_acc_array['account']) ? $games_acc_array['account'] : ''),
                'server' => (!empty($games_acc_array['server']) ? $games_acc_array['server'] : ''),
                'publisher_product_id' => (!empty($games_acc_array['product_code']) ? $games_acc_array['product_code'] : ''),
            ];

            if ($data = $model->getRazerDTUAccount($request_data)) {
                if (!empty($data['character_list'])) {
                    $curl_response_array['result_code'] = 2000;
                    return $data['character_list'];
                }
            }

        } catch (Exception $e) {
            // DO Nothing, Response as not reloadable
        }

        return false;
    }

}