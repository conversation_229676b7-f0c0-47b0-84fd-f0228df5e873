<?php

class dtu_unipin extends DirectTopupCom
{
    public $title, $enable, $game_code;

    private $request_url, $key, $secret;

    private $error_response;

    function dtu_unipin()
    {
        $this->title = 'Unipin';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $curl_response_array['result_code'] = 2000;
        $server_list = json_decode($this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'ACCOUNT_CHARACTER_LIST'), 1);
        return (!empty($server_list) ? $server_list : array(0 => 'Please Select'));
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '')
    {
        $result_code = '1000';
        $this->publishers_id = $publisher_id;
        $get_publishers_conf_array = $this->get_publishers_conf();

        $this->key = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
        $this->secret = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];
        $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];

        $field_list = json_decode($this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'ACCOUNT_FIELD_MAPPING'), 1);

        $validate_data = [];
        foreach ($field_list as $field => $type) {
            if ($field == 'platform') {
                $key = 'account_platform';
            } else {
                $key = $field;
            }

            if (empty($games_acc_array[$key])) {
                return false;
            } else {
                $validate_data[$type['name']] = ($type['type'] == 'number' ? (integer)$games_acc_array[$key] : $games_acc_array[$key]);
            }
        }

        $this->game_code = $games_acc_array['game'];

        if ($this->processRequest('in-game-topup/user/validate', [
            'game_code' => $this->game_code,
            'fields' => $validate_data
        ])) {
            $result_code = 2000;
        }
        if (!empty($this->error_response)) {
            $curl_response_array = $this->error_response;
        }
        return ($result_code == '2000' ? true : false);
    }

    private function processRequest($action, $params = array(), $data = array())
    {
        $time = time();

        $header = [
            'partnerid' => $this->key,
            'timestamp' => $time,
            'path' => $action,
            'auth' => hash_hmac('sha256', $this->key . $time . $action, $this->secret),
            'Content-Type' => 'application/json'
        ];

        $this->request_url = $this->request_url . '/' . $action;

        $request_log = 'url :' . $this->request_url . "\n";
        ob_start();
        echo "<pre>";
        print_r($header);
        print_r($params);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new DirectTopupApiLogCom($action, $request_log,
            array('publishers_id' => (int)$this->publishers_id));

        if ($params) {
            $params = json_encode($params);
        }
        $curl = Yii::app()->curl;
        $curl->request_headers = $header;
        // Set Timeout to 10 second to prevent long queue on publisher side
        $curl->timeout = 10;
        $curl->setProxy(!empty(Yii::app()->params['PROXY']) ? Yii::app()->params['PROXY'] : false);

        $response = $curl->sendPost($this->request_url, $params, true);

        $api_log_obj->end_log('2000', json_encode($response), array('publishers_id' => $this->publishers_id));
        if ($this->validateResponse($response, $curl->getError())) {
            return true;
        } else {
            return false;
        }
    }

    private function validateResponse($response, $curl_error)
    {
        if (empty($curl_error['error_code'])) {
            if (!empty($response['error']) || empty($response['status'])) {
                if (!empty($response['error']['code'])) {
                    $this->error_response = ['game_acc_status' => 'NOT_RELOADABLE'];
                }
            } else {
                return true;
            }
        } else {
            $this->reportUnipinError('curl', $curl_error['error_code'], $curl_error['error_code'] . ' : ' . $curl_error['error_message']);
        }
        return false;
    }

    private function reportUnipinError($type, $error_code, $message)
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'unipin/' . $this->game_code . '/validate/' . $type . '/' . $error_code;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result == false) {
            // Cache for 10 minutes to prevent spamming notification
            Yii::app()->cache->set($cache_key, 1, 600);
            $attachments = array(
                array(
                    'color' => 'warning',
                    'text' => json_encode($message)
                )
            );
            Yii::app()->slack->send('Unipin Account Validation Fail', $attachments, 'DEV_DEBUG');
        }
    }


}