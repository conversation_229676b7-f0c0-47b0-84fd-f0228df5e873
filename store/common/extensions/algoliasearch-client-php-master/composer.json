{"name": "algolia/algoliasearch-client-php", "description": "Algolia Search API Client for PHP", "type": "library", "homepage": "https://github.com/algolia/algoliasearch-client-php", "license": "MIT", "authors": [{"name": "Algolia Team", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.3", "ext-mbstring": "*"}, "require-dev": {"satooshi/php-coveralls": "^1.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4"}, "autoload": {"psr-0": {"AlgoliaSearch": "src/"}}, "autoload-dev": {"psr-0": {"AlgoliaSearch\\Tests": "tests/"}}}