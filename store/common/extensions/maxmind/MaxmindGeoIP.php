<?php

class MaxmindGeoIP extends CApplicationComponent
{
    public $filename = '/usr/local/share/GeoIP/GeoIP.dat';
    private $reader;

    public function getReader()
    {
        if (!$this->reader) {
            spl_autoload_unregister(array(
                'YiiBase',
                'autoload'
            ));
            require ROOT_DIR . '/vendor/autoload.php';
            spl_autoload_register(array(
                'YiiBase',
                'autoload'
            ));
            $this->reader = new GeoIp2\Database\Reader($this->filename);
        }
        return $this->reader;
    }

    public function countryCodeByIP($ip = '')
    {
        if (file_exists($this->filename)) {
            $this->getReader();
            if (empty($ip)) {
                $ip = Yii::app()->frontPageCom->getIPAddress();
            }
            try {
                $this->reader->country($ip);
            } catch (Exception $e) {
                return false;
            }
            return $this->reader->country($ip)->country->isoCode;
        }
        return false;
    }

    public function getIPCountryInfo($ip_address = '')
    {
        $country_info = array();

        if (file_exists($this->filename)) {
            $countries_iso_code_2 = $this->countryCodeByIP($ip_address);

            if ($countries_iso_code_2) {
                $m_ctr = Countries::getCountryByIso2($countries_iso_code_2);

                if (isset($m_ctr->countries_id)) {
                    $country_info = array(
                        'id' => $m_ctr->countries_id,
                        'countries_name' => $m_ctr->countries_name,
                        'countries_iso_code_2' => $m_ctr->countries_iso_code_2,
                        'countries_iso_code_3' => $m_ctr->countries_iso_code_3,
                        'address_format_id' => $m_ctr->address_format_id,
                        'aft_risk_type' => $m_ctr->aft_risk_type
                    );
                }
            }
        }

        return $country_info;
    }
}
