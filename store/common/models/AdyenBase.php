<?php

/**
 * This is the model class for table "adyen".
 *
 * The followings are the available columns in table 'adyen':
 * @property string $adyen_order_id
 * @property string $adyen_event_code
 * @property string $adyen_psp_reference
 * @property string $adyen_original_reference
 * @property string $adyen_merchant_account_code
 * @property string $adyen_event_date
 * @property string $adyen_success
 * @property string $adyen_payment_method
 * @property string $adyen_operations
 * @property string $adyen_reason
 * @property string $adyen_currency
 * @property integer $adyen_amount
 * @property string $adyen_cc_cvc_result
 * @property string $adyen_cc_expiry_date
 * @property integer $adyen_cc_card_bin
 * @property integer $adyen_cc_card_summary
 * @property string $adyen_cc_auth_code
 * @property integer $adyen_cc_fraud_score
 * @property integer $adyen_cc_extra_cost
 * @property string $adyen_cc_three_d_auth
 * @property string $adyen_cc_three_d_auth_offer
 * @property string $adyen_cc_avs_result
 * @property string $adyen_data
 * @property integer $adyen_capture_request
 */
class AdyenBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AdyenBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'adyen';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('adyen_amount, adyen_cc_card_bin, adyen_cc_card_summary, adyen_cc_fraud_score, adyen_cc_extra_cost, adyen_capture_request', 'numerical', 'integerOnly'=>true),
			array('adyen_order_id, adyen_cc_auth_code', 'length', 'max'=>11),
			array('adyen_event_code', 'length', 'max'=>30),
			array('adyen_psp_reference, adyen_original_reference', 'length', 'max'=>20),
			array('adyen_merchant_account_code, adyen_event_date, adyen_payment_method, adyen_cc_cvc_result', 'length', 'max'=>32),
			array('adyen_success, adyen_cc_three_d_auth, adyen_cc_three_d_auth_offer', 'length', 'max'=>5),
			array('adyen_operations, adyen_cc_avs_result', 'length', 'max'=>255),
			array('adyen_reason', 'length', 'max'=>128),
			array('adyen_currency', 'length', 'max'=>3),
			array('adyen_cc_expiry_date', 'length', 'max'=>7),
			array('adyen_data', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('adyen_order_id, adyen_event_code, adyen_psp_reference, adyen_original_reference, adyen_merchant_account_code, adyen_event_date, adyen_success, adyen_payment_method, adyen_operations, adyen_reason, adyen_currency, adyen_amount, adyen_cc_cvc_result, adyen_cc_expiry_date, adyen_cc_card_bin, adyen_cc_card_summary, adyen_cc_auth_code, adyen_cc_fraud_score, adyen_cc_extra_cost, adyen_cc_three_d_auth, adyen_cc_three_d_auth_offer, adyen_cc_avs_result, adyen_data, adyen_capture_request', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'adyen_order_id' => 'Adyen Order',
			'adyen_event_code' => 'Adyen Event Code',
			'adyen_psp_reference' => 'Adyen Psp Reference',
			'adyen_original_reference' => 'Adyen Original Reference',
			'adyen_merchant_account_code' => 'Adyen Merchant Account Code',
			'adyen_event_date' => 'Adyen Event Date',
			'adyen_success' => 'Adyen Success',
			'adyen_payment_method' => 'Adyen Payment Method',
			'adyen_operations' => 'Adyen Operations',
			'adyen_reason' => 'Adyen Reason',
			'adyen_currency' => 'Adyen Currency',
			'adyen_amount' => 'Adyen Amount',
			'adyen_cc_cvc_result' => 'Adyen Cc Cvc Result',
			'adyen_cc_expiry_date' => 'Adyen Cc Expiry Date',
			'adyen_cc_card_bin' => 'Adyen Cc Card Bin',
			'adyen_cc_card_summary' => 'Adyen Cc Card Summary',
			'adyen_cc_auth_code' => 'Adyen Cc Auth Code',
			'adyen_cc_fraud_score' => 'Adyen Cc Fraud Score',
			'adyen_cc_extra_cost' => 'Adyen Cc Extra Cost',
			'adyen_cc_three_d_auth' => 'Adyen Cc Three D Auth',
			'adyen_cc_three_d_auth_offer' => 'Adyen Cc Three D Auth Offer',
			'adyen_cc_avs_result' => 'Adyen Cc Avs Result',
			'adyen_data' => 'Adyen Data',
			'adyen_capture_request' => 'Adyen Capture Request',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('adyen_order_id',$this->adyen_order_id,true);
		$criteria->compare('adyen_event_code',$this->adyen_event_code,true);
		$criteria->compare('adyen_psp_reference',$this->adyen_psp_reference,true);
		$criteria->compare('adyen_original_reference',$this->adyen_original_reference,true);
		$criteria->compare('adyen_merchant_account_code',$this->adyen_merchant_account_code,true);
		$criteria->compare('adyen_event_date',$this->adyen_event_date,true);
		$criteria->compare('adyen_success',$this->adyen_success,true);
		$criteria->compare('adyen_payment_method',$this->adyen_payment_method,true);
		$criteria->compare('adyen_operations',$this->adyen_operations,true);
		$criteria->compare('adyen_reason',$this->adyen_reason,true);
		$criteria->compare('adyen_currency',$this->adyen_currency,true);
		$criteria->compare('adyen_amount',$this->adyen_amount);
		$criteria->compare('adyen_cc_cvc_result',$this->adyen_cc_cvc_result,true);
		$criteria->compare('adyen_cc_expiry_date',$this->adyen_cc_expiry_date,true);
		$criteria->compare('adyen_cc_card_bin',$this->adyen_cc_card_bin);
		$criteria->compare('adyen_cc_card_summary',$this->adyen_cc_card_summary);
		$criteria->compare('adyen_cc_auth_code',$this->adyen_cc_auth_code,true);
		$criteria->compare('adyen_cc_fraud_score',$this->adyen_cc_fraud_score);
		$criteria->compare('adyen_cc_extra_cost',$this->adyen_cc_extra_cost);
		$criteria->compare('adyen_cc_three_d_auth',$this->adyen_cc_three_d_auth,true);
		$criteria->compare('adyen_cc_three_d_auth_offer',$this->adyen_cc_three_d_auth_offer,true);
		$criteria->compare('adyen_cc_avs_result',$this->adyen_cc_avs_result,true);
		$criteria->compare('adyen_data',$this->adyen_data,true);
		$criteria->compare('adyen_capture_request',$this->adyen_capture_request);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}