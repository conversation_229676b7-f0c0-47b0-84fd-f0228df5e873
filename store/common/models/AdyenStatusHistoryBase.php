<?php

/**
 * This is the model class for table "adyen_status_history".
 *
 * The followings are the available columns in table 'adyen_status_history':
 * @property integer $adyen_status_history_id
 * @property integer $adyen_orders_id
 * @property string $adyen_event_code
 * @property string $adyen_date
 * @property string $adyen_reason
 * @property string $changed_by
 */
class AdyenStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AdyenStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'adyen_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('adyen_orders_id', 'numerical', 'integerOnly'=>true),
			array('adyen_event_code', 'length', 'max'=>30),
			array('adyen_reason, changed_by', 'length', 'max'=>128),
			array('adyen_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('adyen_status_history_id, adyen_orders_id, adyen_event_code, adyen_date, adyen_reason, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'adyen_status_history_id' => 'Adyen Status History',
			'adyen_orders_id' => 'Adyen Orders',
			'adyen_event_code' => 'Adyen Event Code',
			'adyen_date' => 'Adyen Date',
			'adyen_reason' => 'Adyen Reason',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('adyen_status_history_id',$this->adyen_status_history_id);
		$criteria->compare('adyen_orders_id',$this->adyen_orders_id);
		$criteria->compare('adyen_event_code',$this->adyen_event_code,true);
		$criteria->compare('adyen_date',$this->adyen_date,true);
		$criteria->compare('adyen_reason',$this->adyen_reason,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}