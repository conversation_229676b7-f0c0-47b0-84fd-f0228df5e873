<?php

/**
 * This is the model class for table "aft_automation".
 *
 * The followings are the available columns in table 'aft_automation':
 * @property integer $aft_automation_id
 * @property integer $aft_automation_category_id
 * @property integer $aft_automation_mode
 * @property string $aft_automation_name
 * @property string $aft_automation_code
 * @property string $aft_automation_version
 * @property string $admin_id
 * @property string $last_modified
 */
class AftAutomationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AftAutomationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'aft_automation';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('aft_automation_code', 'required'),
			array('aft_automation_category_id, aft_automation_mode', 'numerical', 'integerOnly'=>true),
			array('aft_automation_name', 'length', 'max'=>128),
			array('aft_automation_version', 'length', 'max'=>16),
			array('admin_id', 'length', 'max'=>255),
			array('last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('aft_automation_id, aft_automation_category_id, aft_automation_mode, aft_automation_name, aft_automation_code, aft_automation_version, admin_id, last_modified', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'aft_automation_id' => 'Aft Automation',
			'aft_automation_category_id' => 'Aft Automation Category',
			'aft_automation_mode' => 'Aft Automation Mode',
			'aft_automation_name' => 'Aft Automation Name',
			'aft_automation_code' => 'Aft Automation Code',
			'aft_automation_version' => 'Aft Automation Version',
			'admin_id' => 'Admin',
			'last_modified' => 'Last Modified',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('aft_automation_id',$this->aft_automation_id);
		$criteria->compare('aft_automation_category_id',$this->aft_automation_category_id);
		$criteria->compare('aft_automation_mode',$this->aft_automation_mode);
		$criteria->compare('aft_automation_name',$this->aft_automation_name,true);
		$criteria->compare('aft_automation_code',$this->aft_automation_code,true);
		$criteria->compare('aft_automation_version',$this->aft_automation_version,true);
		$criteria->compare('admin_id',$this->admin_id,true);
		$criteria->compare('last_modified',$this->last_modified,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}