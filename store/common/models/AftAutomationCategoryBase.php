<?php

/**
 * This is the model class for table "aft_automation_category".
 *
 * The followings are the available columns in table 'aft_automation_category':
 * @property integer $aft_automation_category_id
 * @property string $aft_automation_category_type
 * @property string $aft_automation_category_key
 */
class AftAutomationCategoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AftAutomationCategoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'aft_automation_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('aft_automation_category_type, aft_automation_category_key', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('aft_automation_category_id, aft_automation_category_type, aft_automation_category_key', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'aft_automation_category_id' => 'Aft Automation Category',
			'aft_automation_category_type' => 'Aft Automation Category Type',
			'aft_automation_category_key' => 'Aft Automation Category Key',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('aft_automation_category_id',$this->aft_automation_category_id);
		$criteria->compare('aft_automation_category_type',$this->aft_automation_category_type,true);
		$criteria->compare('aft_automation_category_key',$this->aft_automation_category_key,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}