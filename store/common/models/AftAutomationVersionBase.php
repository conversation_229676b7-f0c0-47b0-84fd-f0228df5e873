<?php

/**
 * This is the model class for table "aft_automation_version".
 *
 * The followings are the available columns in table 'aft_automation_version':
 * @property string $aft_automation_version
 * @property integer $aft_automation_id
 * @property string $aft_automation_version_code
 * @property string $changed_by
 * @property string $date_added
 */
class AftAutomationVersionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AftAutomationVersionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'aft_automation_version';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('aft_automation_version_code', 'required'),
			array('aft_automation_id', 'numerical', 'integerOnly'=>true),
			array('aft_automation_version', 'length', 'max'=>16),
			array('changed_by', 'length', 'max'=>255),
			array('date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('aft_automation_version, aft_automation_id, aft_automation_version_code, changed_by, date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'aft_automation_version' => 'Aft Automation Version',
			'aft_automation_id' => 'Aft Automation',
			'aft_automation_version_code' => 'Aft Automation Version Code',
			'changed_by' => 'Changed By',
			'date_added' => 'Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('aft_automation_version',$this->aft_automation_version,true);
		$criteria->compare('aft_automation_id',$this->aft_automation_id);
		$criteria->compare('aft_automation_version_code',$this->aft_automation_version_code,true);
		$criteria->compare('changed_by',$this->changed_by,true);
		$criteria->compare('date_added',$this->date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}