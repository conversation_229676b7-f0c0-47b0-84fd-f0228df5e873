<?php

/**
 * This is the model class for table "aft_functions".
 *
 * The followings are the available columns in table 'aft_functions':
 * @property integer $aft_functions_id
 * @property string $aft_functions_name
 * @property string $aft_functions_display_name
 * @property string $aft_functions_description
 * @property integer $aft_functions_action
 * @property integer $line_determine
 * @property string $aft_functions_setting
 */
class AftFunctionsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AftFunctionsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'aft_functions';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('aft_functions_setting', 'required'),
			array('aft_functions_action, line_determine', 'numerical', 'integerOnly'=>true),
			array('aft_functions_name, aft_functions_display_name, aft_functions_description', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('aft_functions_id, aft_functions_name, aft_functions_display_name, aft_functions_description, aft_functions_action, line_determine, aft_functions_setting', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'aft_functions_id' => 'Aft Functions',
			'aft_functions_name' => 'Aft Functions Name',
			'aft_functions_display_name' => 'Aft Functions Display Name',
			'aft_functions_description' => 'Aft Functions Description',
			'aft_functions_action' => 'Aft Functions Action',
			'line_determine' => 'Line Determine',
			'aft_functions_setting' => 'Aft Functions Setting',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('aft_functions_id',$this->aft_functions_id);
		$criteria->compare('aft_functions_name',$this->aft_functions_name,true);
		$criteria->compare('aft_functions_display_name',$this->aft_functions_display_name,true);
		$criteria->compare('aft_functions_description',$this->aft_functions_description,true);
		$criteria->compare('aft_functions_action',$this->aft_functions_action);
		$criteria->compare('line_determine',$this->line_determine);
		$criteria->compare('aft_functions_setting',$this->aft_functions_setting,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}