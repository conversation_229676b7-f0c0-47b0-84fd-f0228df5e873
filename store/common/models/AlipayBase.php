<?php

/**
 * This is the model class for table "alipay".
 *
 * The followings are the available columns in table 'alipay':
 * @property integer $alipay_orders_id
 * @property string $alipay_notify_type
 * @property string $alipay_notify_id
 * @property string $alipay_notify_time
 * @property string $alipay_sign
 * @property string $alipay_sign_type
 * @property string $alipay_trade_no
 * @property string $alipay_payment_type
 * @property double $alipay_total_fee
 * @property string $alipay_currency
 * @property string $alipay_trade_status
 * @property string $alipay_buyer_id
 * @property string $alipay_buyer_email
 */
class AlipayBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AlipayBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'alipay';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('alipay_orders_id', 'numerical', 'integerOnly'=>true),
			array('alipay_total_fee', 'numerical'),
			array('alipay_notify_type, alipay_payment_type', 'length', 'max'=>32),
			array('alipay_notify_id', 'length', 'max'=>125),
			array('alipay_sign', 'length', 'max'=>128),
			array('alipay_sign_type', 'length', 'max'=>16),
			array('alipay_trade_no', 'length', 'max'=>64),
			array('alipay_currency', 'length', 'max'=>3),
			array('alipay_trade_status', 'length', 'max'=>15),
			array('alipay_buyer_id', 'length', 'max'=>30),
			array('alipay_buyer_email', 'length', 'max'=>100),
			array('alipay_notify_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('alipay_orders_id, alipay_notify_type, alipay_notify_id, alipay_notify_time, alipay_sign, alipay_sign_type, alipay_trade_no, alipay_payment_type, alipay_total_fee, alipay_currency, alipay_trade_status, alipay_buyer_id, alipay_buyer_email', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'alipay_orders_id' => 'Alipay Orders',
			'alipay_notify_type' => 'Alipay Notify Type',
			'alipay_notify_id' => 'Alipay Notify',
			'alipay_notify_time' => 'Alipay Notify Time',
			'alipay_sign' => 'Alipay Sign',
			'alipay_sign_type' => 'Alipay Sign Type',
			'alipay_trade_no' => 'Alipay Trade No',
			'alipay_payment_type' => 'Alipay Payment Type',
			'alipay_total_fee' => 'Alipay Total Fee',
			'alipay_currency' => 'Alipay Currency',
			'alipay_trade_status' => 'Alipay Trade Status',
			'alipay_buyer_id' => 'Alipay Buyer',
			'alipay_buyer_email' => 'Alipay Buyer Email',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('alipay_orders_id',$this->alipay_orders_id);
		$criteria->compare('alipay_notify_type',$this->alipay_notify_type,true);
		$criteria->compare('alipay_notify_id',$this->alipay_notify_id,true);
		$criteria->compare('alipay_notify_time',$this->alipay_notify_time,true);
		$criteria->compare('alipay_sign',$this->alipay_sign,true);
		$criteria->compare('alipay_sign_type',$this->alipay_sign_type,true);
		$criteria->compare('alipay_trade_no',$this->alipay_trade_no,true);
		$criteria->compare('alipay_payment_type',$this->alipay_payment_type,true);
		$criteria->compare('alipay_total_fee',$this->alipay_total_fee);
		$criteria->compare('alipay_currency',$this->alipay_currency,true);
		$criteria->compare('alipay_trade_status',$this->alipay_trade_status,true);
		$criteria->compare('alipay_buyer_id',$this->alipay_buyer_id,true);
		$criteria->compare('alipay_buyer_email',$this->alipay_buyer_email,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}