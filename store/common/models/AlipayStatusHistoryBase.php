<?php

/**
 * This is the model class for table "alipay_status_history".
 *
 * The followings are the available columns in table 'alipay_status_history':
 * @property integer $alipay_status_history_id
 * @property integer $alipay_orders_id
 * @property string $alipay_date
 * @property string $alipay_status
 * @property string $alipay_description
 */
class AlipayStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AlipayStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'alipay_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('alipay_orders_id', 'numerical', 'integerOnly'=>true),
			array('alipay_status', 'length', 'max'=>15),
			array('alipay_description', 'length', 'max'=>255),
			array('alipay_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('alipay_status_history_id, alipay_orders_id, alipay_date, alipay_status, alipay_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'alipay_status_history_id' => 'Alipay Status History',
			'alipay_orders_id' => 'Alipay Orders',
			'alipay_date' => 'Alipay Date',
			'alipay_status' => 'Alipay Status',
			'alipay_description' => 'Alipay Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('alipay_status_history_id',$this->alipay_status_history_id);
		$criteria->compare('alipay_orders_id',$this->alipay_orders_id);
		$criteria->compare('alipay_date',$this->alipay_date,true);
		$criteria->compare('alipay_status',$this->alipay_status,true);
		$criteria->compare('alipay_description',$this->alipay_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}