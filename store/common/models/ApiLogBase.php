<?php

/**
 * This is the model class for table "api_log".
 *
 * The followings are the available columns in table 'api_log':
 * @property string $api_log_id
 * @property string $action
 * @property string $publishers_id
 * @property integer $orders_products_id
 * @property string $publisher_ref_id
 * @property string $top_up_id
 * @property integer $result_code
 * @property string $request_log
 * @property string $response_log
 * @property string $request_start
 * @property string $request_end
 * @property string $ip_address
 */
class ApiLogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiLogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_id, result_code', 'numerical', 'integerOnly'=>true),
			array('action, publisher_ref_id', 'length', 'max'=>32),
			array('publishers_id, top_up_id', 'length', 'max'=>10),
			array('ip_address', 'length', 'max'=>128),
			array('request_log, response_log, request_start, request_end', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_log_id, action, publishers_id, orders_products_id, publisher_ref_id, top_up_id, result_code, request_log, response_log, request_start, request_end, ip_address', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_log_id' => 'Api Log',
			'action' => 'Action',
			'publishers_id' => 'Publishers',
			'orders_products_id' => 'Orders Products',
			'publisher_ref_id' => 'Publisher Ref',
			'top_up_id' => 'Top Up',
			'result_code' => 'Result Code',
			'request_log' => 'Request Log',
			'response_log' => 'Response Log',
			'request_start' => 'Request Start',
			'request_end' => 'Request End',
			'ip_address' => 'Ip Address',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_log_id',$this->api_log_id,true);
		$criteria->compare('action',$this->action,true);
		$criteria->compare('publishers_id',$this->publishers_id,true);
		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('publisher_ref_id',$this->publisher_ref_id,true);
		$criteria->compare('top_up_id',$this->top_up_id,true);
		$criteria->compare('result_code',$this->result_code);
		$criteria->compare('request_log',$this->request_log,true);
		$criteria->compare('response_log',$this->response_log,true);
		$criteria->compare('request_start',$this->request_start,true);
		$criteria->compare('request_end',$this->request_end,true);
		$criteria->compare('ip_address',$this->ip_address,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}