<?php

/**
 * This is the model class for table "api_tm_browser".
 *
 * The followings are the available columns in table 'api_tm_browser':
 * @property string $api_tm_query_id
 * @property string $browser_language
 * @property string $browser_string
 * @property string $enabled_js
 * @property string $enabled_fl
 * @property string $enabled_ck
 * @property string $enabled_im
 * @property string $css_image_loaded
 * @property string $flash_version
 * @property string $flash_lang
 * @property string $flash_os
 * @property string $headers_name_value_hash
 * @property string $headers_order_string_hash
 * @property string $http_os_signature
 * @property string $http_referer
 * @property string $plugin_adobe_acrobat
 * @property string $plugin_flash
 * @property string $plugin_hash
 * @property string $plugin_silverlight
 */
class ApiTmBrowserBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmBrowserBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_browser';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('api_tm_query_id', 'length', 'max'=>11),
			array('browser_language, css_image_loaded, flash_version, flash_os, http_os_signature, plugin_adobe_acrobat, plugin_flash, plugin_silverlight', 'length', 'max'=>32),
			array('browser_string', 'length', 'max'=>200),
			array('enabled_js, enabled_fl, enabled_ck, enabled_im', 'length', 'max'=>3),
			array('flash_lang', 'length', 'max'=>10),
			array('headers_name_value_hash, headers_order_string_hash, http_referer, plugin_hash', 'length', 'max'=>36),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, browser_language, browser_string, enabled_js, enabled_fl, enabled_ck, enabled_im, css_image_loaded, flash_version, flash_lang, flash_os, headers_name_value_hash, headers_order_string_hash, http_os_signature, http_referer, plugin_adobe_acrobat, plugin_flash, plugin_hash, plugin_silverlight', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'browser_language' => 'Browser Language',
			'browser_string' => 'Browser String',
			'enabled_js' => 'Enabled Js',
			'enabled_fl' => 'Enabled Fl',
			'enabled_ck' => 'Enabled Ck',
			'enabled_im' => 'Enabled Im',
			'css_image_loaded' => 'Css Image Loaded',
			'flash_version' => 'Flash Version',
			'flash_lang' => 'Flash Lang',
			'flash_os' => 'Flash Os',
			'headers_name_value_hash' => 'Headers Name Value Hash',
			'headers_order_string_hash' => 'Headers Order String Hash',
			'http_os_signature' => 'Http Os Signature',
			'http_referer' => 'Http Referer',
			'plugin_adobe_acrobat' => 'Plugin Adobe Acrobat',
			'plugin_flash' => 'Plugin Flash',
			'plugin_hash' => 'Plugin Hash',
			'plugin_silverlight' => 'Plugin Silverlight',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('browser_language',$this->browser_language,true);
		$criteria->compare('browser_string',$this->browser_string,true);
		$criteria->compare('enabled_js',$this->enabled_js,true);
		$criteria->compare('enabled_fl',$this->enabled_fl,true);
		$criteria->compare('enabled_ck',$this->enabled_ck,true);
		$criteria->compare('enabled_im',$this->enabled_im,true);
		$criteria->compare('css_image_loaded',$this->css_image_loaded,true);
		$criteria->compare('flash_version',$this->flash_version,true);
		$criteria->compare('flash_lang',$this->flash_lang,true);
		$criteria->compare('flash_os',$this->flash_os,true);
		$criteria->compare('headers_name_value_hash',$this->headers_name_value_hash,true);
		$criteria->compare('headers_order_string_hash',$this->headers_order_string_hash,true);
		$criteria->compare('http_os_signature',$this->http_os_signature,true);
		$criteria->compare('http_referer',$this->http_referer,true);
		$criteria->compare('plugin_adobe_acrobat',$this->plugin_adobe_acrobat,true);
		$criteria->compare('plugin_flash',$this->plugin_flash,true);
		$criteria->compare('plugin_hash',$this->plugin_hash,true);
		$criteria->compare('plugin_silverlight',$this->plugin_silverlight,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}