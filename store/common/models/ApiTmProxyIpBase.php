<?php

/**
 * This is the model class for table "api_tm_proxy_ip".
 *
 * The followings are the available columns in table 'api_tm_proxy_ip':
 * @property string $api_tm_query_id
 * @property string $proxy_ip
 * @property integer $proxy_ip_score
 * @property string $proxy_ip_attributes
 * @property string $proxy_ip_activities
 * @property string $proxy_ip_assert_history
 * @property string $proxy_ip_last_update
 * @property integer $proxy_ip_worst_score
 * @property string $proxy_ip_city
 * @property string $proxy_ip_geo
 * @property string $proxy_ip_isp
 * @property string $proxy_ip_latitude
 * @property string $proxy_ip_longitude
 * @property string $proxy_type
 * @property string $proxy_ip_first_seen
 * @property string $proxy_ip_last_event
 * @property string $proxy_ip_organization
 * @property string $proxy_ip_region
 * @property string $proxy_ip_result
 */
class ApiTmProxyIpBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmProxyIpBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_proxy_ip';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('proxy_ip_score, proxy_ip_worst_score', 'numerical', 'integerOnly'=>true),
			array('api_tm_query_id', 'length', 'max'=>11),
			array('proxy_ip, proxy_ip_attributes, proxy_ip_activities', 'length', 'max'=>64),
			array('proxy_ip_assert_history, proxy_ip_city, proxy_ip_isp', 'length', 'max'=>50),
			array('proxy_ip_geo', 'length', 'max'=>2),
			array('proxy_ip_latitude, proxy_ip_longitude', 'length', 'max'=>9),
			array('proxy_type, proxy_ip_region', 'length', 'max'=>32),
			array('proxy_ip_organization', 'length', 'max'=>100),
			array('proxy_ip_result', 'length', 'max'=>10),
			array('proxy_ip_last_update, proxy_ip_first_seen, proxy_ip_last_event', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, proxy_ip, proxy_ip_score, proxy_ip_attributes, proxy_ip_activities, proxy_ip_assert_history, proxy_ip_last_update, proxy_ip_worst_score, proxy_ip_city, proxy_ip_geo, proxy_ip_isp, proxy_ip_latitude, proxy_ip_longitude, proxy_type, proxy_ip_first_seen, proxy_ip_last_event, proxy_ip_organization, proxy_ip_region, proxy_ip_result', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'proxy_ip' => 'Proxy Ip',
			'proxy_ip_score' => 'Proxy Ip Score',
			'proxy_ip_attributes' => 'Proxy Ip Attributes',
			'proxy_ip_activities' => 'Proxy Ip Activities',
			'proxy_ip_assert_history' => 'Proxy Ip Assert History',
			'proxy_ip_last_update' => 'Proxy Ip Last Update',
			'proxy_ip_worst_score' => 'Proxy Ip Worst Score',
			'proxy_ip_city' => 'Proxy Ip City',
			'proxy_ip_geo' => 'Proxy Ip Geo',
			'proxy_ip_isp' => 'Proxy Ip Isp',
			'proxy_ip_latitude' => 'Proxy Ip Latitude',
			'proxy_ip_longitude' => 'Proxy Ip Longitude',
			'proxy_type' => 'Proxy Type',
			'proxy_ip_first_seen' => 'Proxy Ip First Seen',
			'proxy_ip_last_event' => 'Proxy Ip Last Event',
			'proxy_ip_organization' => 'Proxy Ip Organization',
			'proxy_ip_region' => 'Proxy Ip Region',
			'proxy_ip_result' => 'Proxy Ip Result',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('proxy_ip',$this->proxy_ip,true);
		$criteria->compare('proxy_ip_score',$this->proxy_ip_score);
		$criteria->compare('proxy_ip_attributes',$this->proxy_ip_attributes,true);
		$criteria->compare('proxy_ip_activities',$this->proxy_ip_activities,true);
		$criteria->compare('proxy_ip_assert_history',$this->proxy_ip_assert_history,true);
		$criteria->compare('proxy_ip_last_update',$this->proxy_ip_last_update,true);
		$criteria->compare('proxy_ip_worst_score',$this->proxy_ip_worst_score);
		$criteria->compare('proxy_ip_city',$this->proxy_ip_city,true);
		$criteria->compare('proxy_ip_geo',$this->proxy_ip_geo,true);
		$criteria->compare('proxy_ip_isp',$this->proxy_ip_isp,true);
		$criteria->compare('proxy_ip_latitude',$this->proxy_ip_latitude,true);
		$criteria->compare('proxy_ip_longitude',$this->proxy_ip_longitude,true);
		$criteria->compare('proxy_type',$this->proxy_type,true);
		$criteria->compare('proxy_ip_first_seen',$this->proxy_ip_first_seen,true);
		$criteria->compare('proxy_ip_last_event',$this->proxy_ip_last_event,true);
		$criteria->compare('proxy_ip_organization',$this->proxy_ip_organization,true);
		$criteria->compare('proxy_ip_region',$this->proxy_ip_region,true);
		$criteria->compare('proxy_ip_result',$this->proxy_ip_result,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}