<?php

/**
 * This is the model class for table "api_tm_transaction_identifier".
 *
 * The followings are the available columns in table 'api_tm_transaction_identifier':
 * @property string $api_tm_query_id
 * @property integer $transaction_id
 * @property integer $customers_id
 * @property string $customers_login_ip
 * @property string $customers_login_date
 * @property string $request_result
 * @property string $request_id
 * @property string $transaction_type
 * @property string $device_id
 * @property string $create_datetime
 * @property string $query_string
 * @property string $missing_field_bk
 */
class ApiTmTransactionIdentifierBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmTransactionIdentifierBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_transaction_identifier';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('query_string, missing_field_bk', 'required'),
			array('transaction_id, customers_id', 'numerical', 'integerOnly'=>true),
			array('customers_login_ip', 'length', 'max'=>128),
			array('request_result, request_id', 'length', 'max'=>64),
			array('transaction_type', 'length', 'max'=>2),
			array('device_id', 'length', 'max'=>36),
			array('customers_login_date, create_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, transaction_id, customers_id, customers_login_ip, customers_login_date, request_result, request_id, transaction_type, device_id, create_datetime, query_string, missing_field_bk', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'transaction_id' => 'Transaction',
			'customers_id' => 'Customers',
			'customers_login_ip' => 'Customers Login Ip',
			'customers_login_date' => 'Customers Login Date',
			'request_result' => 'Request Result',
			'request_id' => 'Request',
			'transaction_type' => 'Transaction Type',
			'device_id' => 'Device',
			'create_datetime' => 'Create Datetime',
			'query_string' => 'Query String',
			'missing_field_bk' => 'Missing Field Bk',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_login_ip',$this->customers_login_ip,true);
		$criteria->compare('customers_login_date',$this->customers_login_date,true);
		$criteria->compare('request_result',$this->request_result,true);
		$criteria->compare('request_id',$this->request_id,true);
		$criteria->compare('transaction_type',$this->transaction_type,true);
		$criteria->compare('device_id',$this->device_id,true);
		$criteria->compare('create_datetime',$this->create_datetime,true);
		$criteria->compare('query_string',$this->query_string,true);
		$criteria->compare('missing_field_bk',$this->missing_field_bk,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}