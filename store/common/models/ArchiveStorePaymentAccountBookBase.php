<?php

/**
 * This is the model class for table "archive_store_payment_account_book".
 *
 * The followings are the available columns in table 'archive_store_payment_account_book':
 * @property integer $store_payment_account_book_id
 * @property integer $user_id
 * @property string $user_role
 * @property integer $payment_methods_id
 * @property string $payment_methods_alias
 * @property integer $store_payment_account_book_primary
 */
class ArchiveStorePaymentAccountBookBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ArchiveStorePaymentAccountBookBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'archive_store_payment_account_book';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id, payment_methods_id, store_payment_account_book_primary', 'numerical', 'integerOnly'=>true),
			array('user_role', 'length', 'max'=>16),
			array('payment_methods_alias', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_payment_account_book_id, user_id, user_role, payment_methods_id, payment_methods_alias, store_payment_account_book_primary', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_payment_account_book_id' => 'Store Payment Account Book',
			'user_id' => 'User',
			'user_role' => 'User Role',
			'payment_methods_id' => 'Payment Methods',
			'payment_methods_alias' => 'Payment Methods Alias',
			'store_payment_account_book_primary' => 'Store Payment Account Book Primary',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_payment_account_book_id',$this->store_payment_account_book_id);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_role',$this->user_role,true);
		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_methods_alias',$this->payment_methods_alias,true);
		$criteria->compare('store_payment_account_book_primary',$this->store_payment_account_book_primary);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}