<?php

/**
 * This is the model class for table "authorized_token".
 *
 * The followings are the available columns in table 'authorized_token':
 * @property string $auth_token
 * @property string $customers_id
 * @property string $created_datetime
 */
class AuthorizedTokenBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AuthorizedTokenBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'authorized_token';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('auth_token, customers_id, created_datetime', 'required'),
			array('auth_token', 'length', 'max'=>64),
			array('customers_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('auth_token, customers_id, created_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'auth_token' => 'Auth Token',
			'customers_id' => 'Customers',
			'created_datetime' => 'Created Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('auth_token',$this->auth_token,true);
		$criteria->compare('customers_id',$this->customers_id,true);
		$criteria->compare('created_datetime',$this->created_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
	public function cleanupAccessToken(){
		$criteria=new CDbCriteria;
		$criteria->condition = 'now() > (created_datetime + INTERVAL 10 DAY)';
		$this->model()->deleteAll($criteria);
	}
}