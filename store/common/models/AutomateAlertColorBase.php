<?php

/**
 * This is the model class for table "automate_alert_color".
 *
 * The followings are the available columns in table 'automate_alert_color':
 * @property integer $automate_alert_id
 * @property integer $categories_id
 * @property string $automate_alert_type
 * @property string $automate_alert_value
 * @property string $automate_alert_color
 */
class AutomateAlertColorBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AutomateAlertColorBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'automate_alert_color';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id', 'numerical', 'integerOnly'=>true),
			array('automate_alert_type', 'length', 'max'=>20),
			array('automate_alert_value, automate_alert_color', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('automate_alert_id, categories_id, automate_alert_type, automate_alert_value, automate_alert_color', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'automate_alert_id' => 'Automate Alert',
			'categories_id' => 'Categories',
			'automate_alert_type' => 'Automate Alert Type',
			'automate_alert_value' => 'Automate Alert Value',
			'automate_alert_color' => 'Automate Alert Color',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('automate_alert_id',$this->automate_alert_id);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('automate_alert_type',$this->automate_alert_type,true);
		$criteria->compare('automate_alert_value',$this->automate_alert_value,true);
		$criteria->compare('automate_alert_color',$this->automate_alert_color,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}