<?php

/**
 * This is the model class for table "batch_update_price_sets_values".
 *
 * The followings are the available columns in table 'batch_update_price_sets_values':
 * @property integer $batch_update_price_sets_id
 * @property integer $batch_update_qty
 * @property string $batch_update_price
 */
class BatchUpdatePriceSetsValuesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BatchUpdatePriceSetsValuesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'batch_update_price_sets_values';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('batch_update_price_sets_id, batch_update_qty', 'numerical', 'integerOnly'=>true),
			array('batch_update_price', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('batch_update_price_sets_id, batch_update_qty, batch_update_price', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'batch_update_price_sets_id' => 'Batch Update Price Sets',
			'batch_update_qty' => 'Batch Update Qty',
			'batch_update_price' => 'Batch Update Price',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('batch_update_price_sets_id',$this->batch_update_price_sets_id);
		$criteria->compare('batch_update_qty',$this->batch_update_qty);
		$criteria->compare('batch_update_price',$this->batch_update_price,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}