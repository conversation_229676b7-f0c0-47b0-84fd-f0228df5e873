<?php

/**
 * This is the model class for table "bibit".
 *
 * The followings are the available columns in table 'bibit':
 * @property integer $orders_id
 * @property string $bibit_payment_id
 * @property string $bibit_status
 * @property string $bibit_currency
 * @property string $bibit_amount
 * @property string $bibit_payment_method
 * @property string $bibit_cvc_status
 * @property string $bibit_avs_status
 * @property string $bibit_cardholder_aut_result
 * @property integer $bibit_risk_score
 * @property string $bibit_mac
 * @property integer $bibit_capture_request
 * @property string $bibit_card_number
 */
class BibitBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BibitBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'bibit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, bibit_risk_score, bibit_capture_request', 'numerical', 'integerOnly'=>true),
			array('bibit_payment_id', 'length', 'max'=>11),
			array('bibit_status, bibit_payment_method, bibit_mac', 'length', 'max'=>255),
			array('bibit_currency', 'length', 'max'=>3),
			array('bibit_amount', 'length', 'max'=>15),
			array('bibit_cvc_status, bibit_avs_status', 'length', 'max'=>64),
			array('bibit_cardholder_aut_result', 'length', 'max'=>128),
			array('bibit_card_number', 'length', 'max'=>16),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, bibit_payment_id, bibit_status, bibit_currency, bibit_amount, bibit_payment_method, bibit_cvc_status, bibit_avs_status, bibit_cardholder_aut_result, bibit_risk_score, bibit_mac, bibit_capture_request, bibit_card_number', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'bibit_payment_id' => 'Bibit Payment',
			'bibit_status' => 'Bibit Status',
			'bibit_currency' => 'Bibit Currency',
			'bibit_amount' => 'Bibit Amount',
			'bibit_payment_method' => 'Bibit Payment Method',
			'bibit_cvc_status' => 'Bibit Cvc Status',
			'bibit_avs_status' => 'Bibit Avs Status',
			'bibit_cardholder_aut_result' => 'Bibit Cardholder Aut Result',
			'bibit_risk_score' => 'Bibit Risk Score',
			'bibit_mac' => 'Bibit Mac',
			'bibit_capture_request' => 'Bibit Capture Request',
			'bibit_card_number' => 'Bibit Card Number',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('bibit_payment_id',$this->bibit_payment_id,true);
		$criteria->compare('bibit_status',$this->bibit_status,true);
		$criteria->compare('bibit_currency',$this->bibit_currency,true);
		$criteria->compare('bibit_amount',$this->bibit_amount,true);
		$criteria->compare('bibit_payment_method',$this->bibit_payment_method,true);
		$criteria->compare('bibit_cvc_status',$this->bibit_cvc_status,true);
		$criteria->compare('bibit_avs_status',$this->bibit_avs_status,true);
		$criteria->compare('bibit_cardholder_aut_result',$this->bibit_cardholder_aut_result,true);
		$criteria->compare('bibit_risk_score',$this->bibit_risk_score);
		$criteria->compare('bibit_mac',$this->bibit_mac,true);
		$criteria->compare('bibit_capture_request',$this->bibit_capture_request);
		$criteria->compare('bibit_card_number',$this->bibit_card_number,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}