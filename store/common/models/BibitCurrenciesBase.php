<?php

/**
 * This is the model class for table "bibit_currencies".
 *
 * The followings are the available columns in table 'bibit_currencies':
 * @property string $bb_currID
 * @property integer $bb_exponent
 */
class BibitCurrenciesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BibitCurrenciesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'bibit_currencies';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('bb_exponent', 'numerical', 'integerOnly'=>true),
			array('bb_currID', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('bb_currID, bb_exponent', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'bb_currID' => 'Bb Curr',
			'bb_exponent' => 'Bb Exponent',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('bb_currID',$this->bb_currID,true);
		$criteria->compare('bb_exponent',$this->bb_exponent);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}