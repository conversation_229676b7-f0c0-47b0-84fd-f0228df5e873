<?php

/**
 * This is the model class for table "bibit_payment_status_history".
 *
 * The followings are the available columns in table 'bibit_payment_status_history':
 * @property integer $bb_payment_status_history_id
 * @property integer $orders_id
 * @property string $bb_err_no
 * @property string $bb_err_txt
 * @property string $bb_date
 * @property string $bb_status
 * @property string $changed_by
 */
class BibitPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BibitPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'bibit_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('bb_err_no', 'length', 'max'=>32),
			array('bb_err_txt, bb_status', 'length', 'max'=>255),
			array('changed_by', 'length', 'max'=>128),
			array('bb_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('bb_payment_status_history_id, orders_id, bb_err_no, bb_err_txt, bb_date, bb_status, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'bb_payment_status_history_id' => 'Bb Payment Status History',
			'orders_id' => 'Orders',
			'bb_err_no' => 'Bb Err No',
			'bb_err_txt' => 'Bb Err Txt',
			'bb_date' => 'Bb Date',
			'bb_status' => 'Bb Status',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('bb_payment_status_history_id',$this->bb_payment_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('bb_err_no',$this->bb_err_no,true);
		$criteria->compare('bb_err_txt',$this->bb_err_txt,true);
		$criteria->compare('bb_date',$this->bb_date,true);
		$criteria->compare('bb_status',$this->bb_status,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}