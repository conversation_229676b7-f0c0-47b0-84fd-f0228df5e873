<?php

/**
 * This is the model class for table "brackets".
 *
 * The followings are the available columns in table 'brackets':
 * @property integer $brackets_id
 * @property string $brackets_key
 * @property string $brackets_value
 * @property integer $brackets_dependent
 * @property integer $data_pool_level_id
 */
class BracketsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BracketsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'brackets';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('brackets_value', 'required'),
			array('brackets_dependent, data_pool_level_id', 'numerical', 'integerOnly'=>true),
			array('brackets_key', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('brackets_id, brackets_key, brackets_value, brackets_dependent, data_pool_level_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'brackets_id' => 'Brackets',
			'brackets_key' => 'Brackets Key',
			'brackets_value' => 'Brackets Value',
			'brackets_dependent' => 'Brackets Dependent',
			'data_pool_level_id' => 'Data Pool Level',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('brackets_id',$this->brackets_id);
		$criteria->compare('brackets_key',$this->brackets_key,true);
		$criteria->compare('brackets_value',$this->brackets_value,true);
		$criteria->compare('brackets_dependent',$this->brackets_dependent);
		$criteria->compare('data_pool_level_id',$this->data_pool_level_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}