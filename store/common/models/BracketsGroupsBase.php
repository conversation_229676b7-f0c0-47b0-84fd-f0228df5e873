<?php

/**
 * This is the model class for table "brackets_groups".
 *
 * The followings are the available columns in table 'brackets_groups':
 * @property integer $brackets_groups_id
 * @property string $brackets_groups_name
 * @property string $brackets_groups_description
 * @property integer $brackets_groups_visible
 * @property integer $brackets_groups_sort_order
 */
class BracketsGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BracketsGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'brackets_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('brackets_groups_description', 'required'),
			array('brackets_groups_visible, brackets_groups_sort_order', 'numerical', 'integerOnly'=>true),
			array('brackets_groups_name', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('brackets_groups_id, brackets_groups_name, brackets_groups_description, brackets_groups_visible, brackets_groups_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'brackets_groups_id' => 'Brackets Groups',
			'brackets_groups_name' => 'Brackets Groups Name',
			'brackets_groups_description' => 'Brackets Groups Description',
			'brackets_groups_visible' => 'Brackets Groups Visible',
			'brackets_groups_sort_order' => 'Brackets Groups Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('brackets_groups_id',$this->brackets_groups_id);
		$criteria->compare('brackets_groups_name',$this->brackets_groups_name,true);
		$criteria->compare('brackets_groups_description',$this->brackets_groups_description,true);
		$criteria->compare('brackets_groups_visible',$this->brackets_groups_visible);
		$criteria->compare('brackets_groups_sort_order',$this->brackets_groups_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}