<?php

/**
 * This is the model class for table "brackets_groups_to_level_tags".
 *
 * The followings are the available columns in table 'brackets_groups_to_level_tags':
 * @property integer $brackets_groups_id
 * @property integer $data_pool_level_tags_id
 */
class BracketsGroupsToLevelTagsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BracketsGroupsToLevelTagsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'brackets_groups_to_level_tags';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('brackets_groups_id, data_pool_level_tags_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('brackets_groups_id, data_pool_level_tags_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'brackets_groups_id' => 'Brackets Groups',
			'data_pool_level_tags_id' => 'Data Pool Level Tags',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('brackets_groups_id',$this->brackets_groups_id);
		$criteria->compare('data_pool_level_tags_id',$this->data_pool_level_tags_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}