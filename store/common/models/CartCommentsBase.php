<?php

/**
 * This is the model class for table "cart_comments".
 *
 * The followings are the available columns in table 'cart_comments':
 * @property integer $cart_comments_id
 * @property string $cart_comments_title
 * @property string $cart_comments_input_type
 * @property string $cart_comments_input_size
 * @property string $cart_comments_options
 * @property integer $cart_comments_option_title
 * @property integer $cart_comments_required
 * @property integer $cart_comments_status
 * @property integer $cart_comments_sort_order
 * @property integer $cart_comments_type
 */
class CartCommentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CartCommentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cart_comments';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cart_comments_option_title, cart_comments_required, cart_comments_status, cart_comments_sort_order, cart_comments_type', 'numerical', 'integerOnly'=>true),
			array('cart_comments_title', 'length', 'max'=>255),
			array('cart_comments_input_type, cart_comments_input_size', 'length', 'max'=>32),
			array('cart_comments_options', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cart_comments_id, cart_comments_title, cart_comments_input_type, cart_comments_input_size, cart_comments_options, cart_comments_option_title, cart_comments_required, cart_comments_status, cart_comments_sort_order, cart_comments_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cart_comments_id' => 'Cart Comments',
			'cart_comments_title' => 'Cart Comments Title',
			'cart_comments_input_type' => 'Cart Comments Input Type',
			'cart_comments_input_size' => 'Cart Comments Input Size',
			'cart_comments_options' => 'Cart Comments Options',
			'cart_comments_option_title' => 'Cart Comments Option Title',
			'cart_comments_required' => 'Cart Comments Required',
			'cart_comments_status' => 'Cart Comments Status',
			'cart_comments_sort_order' => 'Cart Comments Sort Order',
			'cart_comments_type' => 'Cart Comments Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cart_comments_id',$this->cart_comments_id);
		$criteria->compare('cart_comments_title',$this->cart_comments_title,true);
		$criteria->compare('cart_comments_input_type',$this->cart_comments_input_type,true);
		$criteria->compare('cart_comments_input_size',$this->cart_comments_input_size,true);
		$criteria->compare('cart_comments_options',$this->cart_comments_options,true);
		$criteria->compare('cart_comments_option_title',$this->cart_comments_option_title);
		$criteria->compare('cart_comments_required',$this->cart_comments_required);
		$criteria->compare('cart_comments_status',$this->cart_comments_status);
		$criteria->compare('cart_comments_sort_order',$this->cart_comments_sort_order);
		$criteria->compare('cart_comments_type',$this->cart_comments_type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}