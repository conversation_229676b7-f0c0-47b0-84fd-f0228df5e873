<?php

/**
 * This is the model class for table "categories".
 *
 * The followings are the available columns in table 'categories':
 * @property integer $categories_id
 * @property integer $parent_id
 * @property string $categories_parent_path
 * @property integer $sort_order
 * @property string $date_added
 * @property string $last_modified
 * @property integer $categories_pinned
 * @property integer $categories_status
 * @property integer $c2c_categories_status
 * @property string $categories_url_alias
 * @property integer $categories_auto_seo
 * @property string $categories_auto_seo_type
 * @property string $products_count
 * @property integer $custom_products_type_id
 * @property integer $custom_products_type_child_id
 * @property integer $categories_types_groups_id
 * @property integer $categories_buyback_main_cat
 */
class CategoriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('parent_id, sort_order, categories_pinned, categories_status, c2c_categories_status, categories_auto_seo, custom_products_type_id, custom_products_type_child_id, categories_types_groups_id, categories_buyback_main_cat', 'numerical', 'integerOnly'=>true),
			array('categories_parent_path, categories_url_alias', 'length', 'max'=>255),
			array('categories_auto_seo_type', 'length', 'max'=>64),
			array('products_count', 'length', 'max'=>11),
			array('date_added, last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_id, parent_id, categories_parent_path, sort_order, date_added, last_modified, categories_pinned, categories_status, c2c_categories_status, categories_url_alias, categories_auto_seo, categories_auto_seo_type, products_count, custom_products_type_id, custom_products_type_child_id, categories_types_groups_id, categories_buyback_main_cat', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'CategoriesDescriptionBase' => array(self::HAS_MANY, 'CategoriesDescriptionBase', 'categories_id'),
            'CategoriesGroupsBase' => array(self::HAS_ONE, 'CategoriesGroupsBase', 'categories_id'),
            'ProductsBase' => array(self::MANY_MANY, 'ProductsBase', 'products_to_categories(categories_id, products_id)'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_id' => 'Categories',
			'parent_id' => 'Parent',
			'categories_parent_path' => 'Categories Parent Path',
			'sort_order' => 'Sort Order',
			'date_added' => 'Date Added',
			'last_modified' => 'Last Modified',
			'categories_pinned' => 'Categories Pinned',
			'categories_status' => 'Categories Status',
			'c2c_categories_status' => 'C2c Categories Status',
			'categories_url_alias' => 'Categories Url Alias',
			'categories_auto_seo' => 'Categories Auto Seo',
			'categories_auto_seo_type' => 'Categories Auto Seo Type',
			'products_count' => 'Products Count',
			'custom_products_type_id' => 'Custom Products Type',
			'custom_products_type_child_id' => 'Custom Products Type Child',
			'categories_types_groups_id' => 'Categories Types Groups',
			'categories_buyback_main_cat' => 'Categories Buyback Main Cat',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('categories_parent_path',$this->categories_parent_path,true);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('categories_pinned',$this->categories_pinned);
		$criteria->compare('categories_status',$this->categories_status);
		$criteria->compare('c2c_categories_status',$this->c2c_categories_status);
		$criteria->compare('categories_url_alias',$this->categories_url_alias,true);
		$criteria->compare('categories_auto_seo',$this->categories_auto_seo);
		$criteria->compare('categories_auto_seo_type',$this->categories_auto_seo_type,true);
		$criteria->compare('products_count',$this->products_count,true);
		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('custom_products_type_child_id',$this->custom_products_type_child_id);
		$criteria->compare('categories_types_groups_id',$this->categories_types_groups_id);
		$criteria->compare('categories_buyback_main_cat',$this->categories_buyback_main_cat);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function isCategoryExistByStatus($categories_id, $categories_status = 1) {
        return $this->exists(array(
            'condition' => 'categories_id = :categories_id AND categories_status = :categories_status',
            'params' => array(':categories_id' => $categories_id, ':categories_status' => $categories_status)
        ));
    }
    
    # not in used
    public function getProductType($categories_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'custom_products_type_id, custom_products_type_child_id';
		$criteria->condition = 'categories_id =:categories_id
            AND custom_products_type_id <> 999';
		$criteria->params = array(':categories_id' => $categories_id);
		if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
		return $return_array;
    }
    
    # not in used
	public function getCPathByParentID($parent_id, $tpl = 2) { // default CDK
		$return_str = '';
		
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id, categories_parent_path ';
		$criteria->condition = 'parent_id = :parent_id 
            AND custom_products_type_id = :custom_products_type_id
            AND categories_status = 1';
		$criteria->params = array(
            ':parent_id' => $parent_id,
            'custom_products_type_id' => $tpl
        );
        $criteria->limit = 1;
        if ($result = $this->model()->find($criteria)) {
            $return_str = trim($result->categories_parent_path . $result->categories_id, '_');
        }
        
		return $return_str;
	}
    
    public function getCPathByCategoryID($categories_id) {
        $return_str = '';
		
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_parent_path, categories_id';
		$criteria->condition = 'categories_id = :categories_id';
		$criteria->params = array(
            ':categories_id' => $categories_id
        );
        if ($result = $this->model()->find($criteria)) {
            $return_str = trim($result->categories_parent_path . $result->categories_id, '_');
        }

		return $return_str;
    }

    # not in used
    public function getInfoByCategoryID($categories_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id, custom_products_type_id, categories_parent_path, sort_order';
		$criteria->condition = 'categories_id = :categories_id';
		$criteria->params = array(
            ':categories_id' => $categories_id
        );
        if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }

		return $return_array;
    }

    public function getAllEnabledCagetoriesID() {
        $return_array = array();
        
//        $criteria = new CDbCriteria;
//		$criteria->select = 'categories_id';
//        $criteria->condition = 'categories_status = 1';
//		
//		if ($result = $this->model()->findAll($criteria)) {
//            foreach ($result as $record) {
//                $return_array[] = $record->categories_id;
//            }
//        }
        
        $sql = '	SELECT categories_id
                    FROM ' . $this->tableName() . '
                    WHERE categories_status = 1';
        $command = $this->conn->createCommand($sql);
        if ($value = $command->queryColumn()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
    
    # not in used
    public function getSubCategoryIDByParentID($parent_id, $groups_id, $guest_group_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
        $criteria->with=array(
            'CategoriesGroupsBase' => array('select' => false, 'together' => true)
        );
		$criteria->select = 'categories_id';
		$criteria->condition = 'parent_id = :parent_id
            AND categories_status = 1
            AND (groups_id = :groups_id OR groups_id = :guest_group_id)';
		$criteria->params = array(
            ':parent_id' => $parent_id,
            ':groups_id' => $groups_id,
            ':guest_group_id' => $guest_group_id
        );
        
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->categories_id;
            }
        }
        
		return $return_array;
    }
    
//    public function getSubcategories($parent_id = 0, $status = '') {
//        $return_array = array();
//		
//        if ($cPath = $this->getCPathByCategoryID($parent_id)) {
//            $return_array = $this->getSubcategoriesByCPath($cPath, $status);
//        }
//        
//		return $return_array;
//    }
    
    public function getSubcategoriesByCPath($cPath, $status = '') {
        $return_array = array();
        $category_path = '\_' . str_replace('_', '\_', $cPath) . '\_%';
        
        $sql = '	SELECT categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id, sort_order
                    FROM ' . $this->tableName() . '
                    WHERE categories_parent_path LIKE :categories_parent_path' .
                ($status !== '' ? ' AND categories_status = :categories_status' : '');
        
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_parent_path", $category_path, PDO::PARAM_STR);
        
        if ($status !== '') {
            $command->bindParam(":categories_status", $status, PDO::PARAM_INT);
        }
        
        if ($value = $command->queryAll()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
    
    public function getSubCategoriesInfoByGameID($game_id, $custom_products_type_id, $status = '') {
        $return_array = array();
        $category_path = '\_' . str_replace('_', '\_', $this->getCPathByCategoryID($game_id)) . '\_%';
        
        $sql = '	SELECT categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id, sort_order
                    FROM ' . $this->tableName() . '
                    WHERE categories_parent_path LIKE :categories_parent_path
                        AND custom_products_type_id = :custom_products_type_id' .
                ($status !== '' ? ' AND categories_status = :categories_status' : '');
        
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_parent_path", $category_path, PDO::PARAM_STR);
        $command->bindParam(":custom_products_type_id", $custom_products_type_id, PDO::PARAM_INT);
        
        if ($status !== '') {
            $command->bindParam(":categories_status", $status, PDO::PARAM_INT);
        }
        
        if ($value = $command->queryAll()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
    
    # not in used
    public function getSubCategoryInfoByCustomProductTypeID($categories_parent_path, $custom_products_type_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id';
		$criteria->condition = 'categories_status = 1
            AND categories_parent_path LIKE :categories_parent_path
            AND custom_products_type_id = :custom_products_type_id';
		$criteria->params = array(
            ':categories_parent_path' => '\_' . str_replace('_', '\_', $categories_parent_path) . '\_%',
            ':custom_products_type_id' => $custom_products_type_id,
        );
        
        if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
		return $return_array;
    }

    public function getSubCategoryInfoByCustomProductTypeChildID($categories_parent_path, $custom_products_type_child_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id, categories_parent_path, custom_products_type_id, custom_products_type_child_id';
		$criteria->condition = 'categories_status = 1
            AND categories_parent_path LIKE :categories_parent_path
            AND custom_products_type_child_id = :custom_products_type_child_id';
		$criteria->params = array(
            ':categories_parent_path' => '\_' . str_replace('_', '\_', $categories_parent_path) . '\_%',
            ':custom_products_type_child_id' => $custom_products_type_child_id,
        );
        
        if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
		return $return_array;
    }
    
    # not in used
    public function getTotalActiveProducts($categories_id, $groups_id, $guest_group_id) {
        $criteria = new CDbCriteria;
        $criteria->with=array(
            'ProductsBase' => array('select' => false, 'together' => true), 
            'CategoriesGroupsBase' => array('select' => false, 'together' => true)
        );
		$criteria->select = 'COUNT(t.categories_id) as total';
		$criteria->condition = 't.categories_id = :categories_id 
            AND categories_status = 1 
            AND (CategoriesGroupsBase.groups_id = :groups_id OR CategoriesGroupsBase.groups_id = :guest_group_id) 
            AND ProductsBase.products_status = 1 
            AND ( ProductsBase.products_bundle= "yes" 
                    OR ProductsBase.products_bundle_dynamic = "yes" 
                    OR (ProductsBase.products_bundle != "yes" 
                            AND ProductsBase.products_bundle_dynamic != "yes" AND ProductsBase.products_display = 1
                    )
            )';
		$criteria->params = array(
            ':categories_id' => $categories_id,
            ':groups_id' => $groups_id,
            ':guest_group_id' => $guest_group_id
        );
        
		return $this->model()->count($criteria);
    }

    # not in used
    public function filterCagetoriesIDByCustomerGroupID($categories_id_array, $customers_groups_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
        $criteria->with=array(
            'CategoriesGroupsBase' => array('select' => false, 'together' => true)
        );
		$criteria->select = 'DISTINCT t.categories_id, parent_id, categories_parent_path';
		$criteria->condition = 'categories_status = 1
            AND CategoriesGroupsBase.groups_id IN (0, :groups_id)';
		$criteria->params = array(
            ':groups_id' => $customers_groups_id,
        );
        $criteria->addInCondition('t.categories_id', $categories_id_array);
        $criteria->order = 'sort_order';
        
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_id] = $result->getAttributes(NULL);
            }
        }
        
		return $return_array;
    }
    
    public function getCategoriesIDByCategoriesUrlAlias($categories_url_alias) {
        $return_int = 0;
        
//        $criteria = new CDbCriteria;
//		$criteria->select = 'categories_id, parent_id, categories_parent_path';
//		$criteria->condition = 'categories_url_alias = :categories_url_alias
//            AND categories_status = 1';
//		$criteria->params = array(
//            ':categories_url_alias' => $categories_url_alias
//        );
//        
//		if ($results = $this->model()->findAll($criteria)) {
//            foreach ($results as $result) {
//                if ($result->categories_parent_path === '_' . $result->parent_id . '_') {
//                    $return_int = $result->categories_id;
//                    break;
//                }
//            }
//        }
        
        $sql = "SELECT categories_id, parent_id, categories_parent_path
                FROM " . $this->tableName() . " AS p
                WHERE categories_url_alias = :categories_url_alias
                    AND categories_status = 1";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_url_alias", $categories_url_alias, PDO::PARAM_STR);
        if ($dataset = $command->queryAll()) {
            foreach ($dataset as $result) {
                if ($result['categories_parent_path'] === '_' . $result['parent_id'] . '_') {
                    $return_int = $result['categories_id'];
                    break;
                }
            }
        }
        
		return $return_int;
    }
    
    public function getCategoriesUrlAlias($categories_id) {
        $return_str = '';
        
//        $criteria = new CDbCriteria;
//		$criteria->select = 'categories_url_alias';
//		$criteria->condition = 'categories_id = :categories_id
//            AND categories_status = 1';
//		$criteria->params = array(
//            ':categories_id' => $categories_id
//        );
//        
//		if ($result = $this->model()->find($criteria)) {
//            $return_str = $result->categories_url_alias;
//        }
        
        $sql = "SELECT categories_url_alias
                FROM " . $this->tableName() . " AS p
                WHERE categories_id = :categories_id
                    AND categories_status = 1";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_id", $categories_id, PDO::PARAM_INT);
        if ($dataset = $command->queryScalar()) {
            $return_str = $dataset;
        }
        
		return $return_str;
    }

    public static function getSubCategoriesIdByParentId($parent_id, $custom_products_type_id = 2){
        $return_array = array();

        $criteria = new CDbCriteria;
        $criteria->select = 'categories_id';
        $criteria->condition = 'categories_status = 1
            AND categories_parent_path LIKE :categories_parent_path
            AND custom_products_type_id = :custom_products_type_id';

        $criteria->params = array(
            ':categories_parent_path' => '%\_' . str_replace('_', '\_', $parent_id) . '\_%',
            ':custom_products_type_id' => $custom_products_type_id
        );

        if ($result = self::model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }

        return $return_array;
    }

}