<?php

/**
 * This is the model class for table "categories_configuration".
 *
 * The followings are the available columns in table 'categories_configuration':
 * @property integer $categories_configuration_id
 * @property integer $categories_id
 * @property string $categories_configuration_title
 * @property string $categories_configuration_key
 * @property string $categories_configuration_value
 * @property string $categories_configuration_description
 * @property integer $categories_configuration_group_id
 * @property integer $sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property string $use_function
 * @property string $set_function
 */
class CategoriesConfigurationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesConfigurationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_configuration';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_configuration_value', 'required'),
			array('categories_id, categories_configuration_group_id, sort_order', 'numerical', 'integerOnly'=>true),
			array('categories_configuration_title, categories_configuration_key', 'length', 'max'=>64),
			array('categories_configuration_description, use_function, set_function', 'length', 'max'=>255),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_configuration_id, categories_id, categories_configuration_title, categories_configuration_key, categories_configuration_value, categories_configuration_description, categories_configuration_group_id, sort_order, last_modified, date_added, use_function, set_function', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_configuration_id' => 'Categories Configuration',
			'categories_id' => 'Categories',
			'categories_configuration_title' => 'Categories Configuration Title',
			'categories_configuration_key' => 'Categories Configuration Key',
			'categories_configuration_value' => 'Categories Configuration Value',
			'categories_configuration_description' => 'Categories Configuration Description',
			'categories_configuration_group_id' => 'Categories Configuration Group',
			'sort_order' => 'Sort Order',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'use_function' => 'Use Function',
			'set_function' => 'Set Function',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_configuration_id',$this->categories_configuration_id);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('categories_configuration_title',$this->categories_configuration_title,true);
		$criteria->compare('categories_configuration_key',$this->categories_configuration_key,true);
		$criteria->compare('categories_configuration_value',$this->categories_configuration_value,true);
		$criteria->compare('categories_configuration_description',$this->categories_configuration_description,true);
		$criteria->compare('categories_configuration_group_id',$this->categories_configuration_group_id);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('use_function',$this->use_function,true);
		$criteria->compare('set_function',$this->set_function,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getValueByCategoryID($categories_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_configuration_key, categories_configuration_value';
		$criteria->condition = 'categories_id = :categories_id';
		$criteria->params = array(
            ':categories_id' => $categories_id,
        );
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_configuration_key] = $result->categories_configuration_value;
            }
        }
        
		return $return_array;
    }
    
    public function getValueByKey($categories_id, $categories_configuration_key) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_configuration_key, categories_configuration_value';
		$criteria->condition = 'categories_id = :categories_id
             AND categories_configuration_key = :categories_configuration_key';
		$criteria->params = array(
            ':categories_id' => $categories_id,
            ':categories_configuration_key' => $categories_configuration_key
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_configuration_key] = $result->categories_configuration_value;
            }
        }
        
		return $return_array;
    }
    
    public function getValueByCategoryIDByGroupID($categories_id, $categories_configuration_group_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_configuration_key, categories_configuration_value';
		$criteria->condition = 'categories_id = :categories_id
             AND categories_configuration_group_id = :categories_configuration_group_id';
		$criteria->params = array(
            ':categories_id' => $categories_id,
            ':categories_configuration_group_id' => $categories_configuration_group_id
        );
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_configuration_key] = $result->categories_configuration_value;
            }
        }
        
		return $return_array;
    }
    
}