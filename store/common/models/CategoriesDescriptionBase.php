<?php

/**
 * This is the model class for table "categories_description".
 *
 * The followings are the available columns in table 'categories_description':
 * @property integer $categories_id
 * @property integer $language_id
 * @property string $categories_name
 * @property string $categories_short_name
 * @property string $categories_pin_yin
 * @property string $categories_heading_title
 * @property string $categories_description
 * @property string $categories_image
 * @property string $categories_image_title
 */
class CategoriesDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id, language_id', 'numerical', 'integerOnly'=>true),
			array('categories_name, categories_short_name, categories_pin_yin, categories_heading_title, categories_image, categories_image_title', 'length', 'max'=>64),
			array('categories_description', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_id, language_id, categories_name, categories_short_name, categories_pin_yin, categories_heading_title, categories_description, categories_image, categories_image_title', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_id' => 'Categories',
			'language_id' => 'Language',
			'categories_name' => 'Categories Name',
			'categories_short_name' => 'Categories Short Name',
			'categories_pin_yin' => 'Categories Pin Yin',
			'categories_heading_title' => 'Categories Heading Title',
			'categories_description' => 'Categories Description',
			'categories_image' => 'Categories Image',
			'categories_image_title' => 'Categories Image Title',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('categories_name',$this->categories_name,true);
		$criteria->compare('categories_short_name',$this->categories_short_name,true);
		$criteria->compare('categories_pin_yin',$this->categories_pin_yin,true);
		$criteria->compare('categories_heading_title',$this->categories_heading_title,true);
		$criteria->compare('categories_description',$this->categories_description,true);
		$criteria->compare('categories_image',$this->categories_image,true);
		$criteria->compare('categories_image_title',$this->categories_image_title,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getCategoriesName($categories_id, $language_id = 0, $default_language_id = 0) {
        return $this->getCategoryInfo('categories_name', $categories_id, $language_id, $default_language_id);
    }
    
    public function getCategoriesImage($categories_id, $language_id = 0, $default_language_id = 0) {
        return $this->getCategoryInfo('categories_image', $categories_id, $language_id, $default_language_id);
    }
    
    public function getCategoriesImageTitle($categories_id, $language_id = 0, $default_language_id = 0) {
        return $this->getCategoryInfo('categories_image_title', $categories_id, $language_id, $default_language_id);
    }
    
    public function getCategoriesHeadingTitle($categories_id, $language_id = 0, $default_language_id = 0) {
        return $this->getCategoryInfo('categories_heading_title', $categories_id, $language_id, $default_language_id);
    }
    
    public function getCategoriesPinYin($categories_id, $language_id = 0, $default_language_id = 0) {
        return $this->getCategoryInfo('categories_pin_yin', $categories_id, $language_id, $default_language_id);
    }
    
    protected function getCategoryInfo($field, $categories_id, $language_id = 0, $default_language_id = 0) {
        $return_str = '';
        
        $sql = "	SELECT " . $field . " 
                    FROM " . $this->tableName() . " 
                    WHERE categories_id = :categories_id
                        AND " . $field . " <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(categories_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE categories_id = :categories_id 
                                AND language_id = :language_id
                                AND " . $field . " <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_id", $categories_id, PDO::PARAM_STR);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}