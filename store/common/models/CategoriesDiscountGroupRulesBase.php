<?php

/**
 * This is the model class for table "categories_discount_group_rules".
 *
 * The followings are the available columns in table 'categories_discount_group_rules':
 * @property string $cdgr_id
 * @property integer $cdrules_id
 * @property integer $cdgr_customer_group_id
 * @property string $cdgr_discount
 * @property string $cdgr_wor
 * @property string $cdgr_updated_datetime
 */
class CategoriesDiscountGroupRulesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesDiscountGroupRulesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_discount_group_rules';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cdrules_id, cdgr_customer_group_id', 'required'),
			array('cdrules_id, cdgr_customer_group_id', 'numerical', 'integerOnly'=>true),
			array('cdgr_discount, cdgr_wor', 'length', 'max'=>8),
			array('cdgr_updated_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cdgr_id, cdrules_id, cdgr_customer_group_id, cdgr_discount, cdgr_wor, cdgr_updated_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cdgr_id' => 'Cdgr',
			'cdrules_id' => 'Cdrules',
			'cdgr_customer_group_id' => 'Cdgr Customer Group',
			'cdgr_discount' => 'Cdgr Discount',
			'cdgr_wor' => 'Cdgr Wor',
			'cdgr_updated_datetime' => 'Cdgr Updated Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cdgr_id',$this->cdgr_id,true);
		$criteria->compare('cdrules_id',$this->cdrules_id);
		$criteria->compare('cdgr_customer_group_id',$this->cdgr_customer_group_id);
		$criteria->compare('cdgr_discount',$this->cdgr_discount,true);
		$criteria->compare('cdgr_wor',$this->cdgr_wor,true);
		$criteria->compare('cdgr_updated_datetime',$this->cdgr_updated_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
        
    public function getDiscountInfoByRuleIDByGroupID($rule_id, $groups_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
        $criteria->select = "cdgr_discount, cdgr_wor";
        $criteria->condition = "cdrules_id = :cdrules_id 
            AND cdgr_customer_group_id = :cdgr_customer_group_id";
        $criteria->params = array(
            ':cdrules_id' => $rule_id,
            ':cdgr_customer_group_id' => $groups_id
        );
        if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
        return $return_array;
    }
    
}