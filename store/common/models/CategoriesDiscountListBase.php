<?php

/**
 * This is the model class for table "categories_discount_list".
 *
 * The followings are the available columns in table 'categories_discount_list':
 * @property string $cdl_id
 * @property integer $cdrules_id
 * @property integer $cdl_category_id
 * @property string $cdl_datetime
 */
class CategoriesDiscountListBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesDiscountListBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_discount_list';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cdrules_id, cdl_category_id', 'required'),
			array('cdrules_id, cdl_category_id', 'numerical', 'integerOnly'=>true),
			array('cdl_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cdl_id, cdrules_id, cdl_category_id, cdl_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cdl_id' => 'Cdl',
			'cdrules_id' => 'Cdrules',
			'cdl_category_id' => 'Cdl Category',
			'cdl_datetime' => 'Cdl Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cdl_id',$this->cdl_id,true);
		$criteria->compare('cdrules_id',$this->cdrules_id);
		$criteria->compare('cdl_category_id',$this->cdl_category_id);
		$criteria->compare('cdl_datetime',$this->cdl_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
        
    public function getDiscountRuleByCategoryIDs($categories_id_array) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
        $criteria->select = "cdrules_id, cdl_category_id";
        $criteria->addInCondition('cdl_category_id', $categories_id_array);
        
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->cdl_category_id] = $result->cdrules_id;
            }
        }
        
        return $return_array;
    }
}