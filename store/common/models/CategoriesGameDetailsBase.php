<?php

/**
 * This is the model class for table "categories_game_details".
 *
 * The followings are the available columns in table 'categories_game_details':
 * @property integer $categories_id
 * @property integer $language_id
 * @property string $game_publisher
 * @property string $game_developer
 * @property string $game_platform
 * @property string $game_category
 * @property string $game_model
 * @property string $game_language
 * @property string $game_status
 * @property string $game_interface
 * @property string $game_client_type
 * @property string $game_time_keeping_system
 * @property integer $game_year
 * @property string $game_expansions
 * @property string $game_launching_date
 * @property string $game_website_name
 * @property string $game_website_url
 * @property string $game_esrb
 * @property string $game_keyword
 * @property string $game_download_client_url
 * @property string $game_signup_account_url
 * @property string $game_spec_minimum
 * @property string $game_spec_recommended
 * @property string $game_template
 */
class CategoriesGameDetailsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesGameDetailsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_game_details';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id, language_id, game_year', 'numerical', 'integerOnly'=>true),
			array('game_template', 'length', 'max'=>1),
			array('game_publisher, game_developer, game_platform, game_category, game_model, game_language, game_status, game_interface, game_client_type, game_time_keeping_system, game_expansions, game_launching_date, game_website_name, game_website_url, game_esrb, game_keyword, game_download_client_url, game_signup_account_url, game_spec_minimum, game_spec_recommended', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_id, language_id, game_publisher, game_developer, game_platform, game_category, game_model, game_language, game_status, game_interface, game_client_type, game_time_keeping_system, game_year, game_expansions, game_launching_date, game_website_name, game_website_url, game_esrb, game_keyword, game_download_client_url, game_signup_account_url, game_spec_minimum, game_spec_recommended, game_template', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_id' => 'Categories',
			'language_id' => 'Language',
			'game_publisher' => 'Game Publisher',
			'game_developer' => 'Game Developer',
			'game_platform' => 'Game Platform',
			'game_category' => 'Game Category',
			'game_model' => 'Game Model',
			'game_language' => 'Game Language',
			'game_status' => 'Game Status',
			'game_interface' => 'Game Interface',
			'game_client_type' => 'Game Client Type',
			'game_time_keeping_system' => 'Game Time Keeping System',
			'game_year' => 'Game Year',
			'game_expansions' => 'Game Expansions',
			'game_launching_date' => 'Game Launching Date',
			'game_website_name' => 'Game Website Name',
			'game_website_url' => 'Game Website Url',
			'game_esrb' => 'Game Esrb',
			'game_keyword' => 'Game Keyword',
			'game_download_client_url' => 'Game Download Client Url',
			'game_signup_account_url' => 'Game Signup Account Url',
			'game_spec_minimum' => 'Game Spec Minimum',
			'game_spec_recommended' => 'Game Spec Recommended',
			'game_template' => 'Game Template',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('game_publisher',$this->game_publisher,true);
		$criteria->compare('game_developer',$this->game_developer,true);
		$criteria->compare('game_platform',$this->game_platform,true);
		$criteria->compare('game_category',$this->game_category,true);
		$criteria->compare('game_model',$this->game_model,true);
		$criteria->compare('game_language',$this->game_language,true);
		$criteria->compare('game_status',$this->game_status,true);
		$criteria->compare('game_interface',$this->game_interface,true);
		$criteria->compare('game_client_type',$this->game_client_type,true);
		$criteria->compare('game_time_keeping_system',$this->game_time_keeping_system,true);
		$criteria->compare('game_year',$this->game_year);
		$criteria->compare('game_expansions',$this->game_expansions,true);
		$criteria->compare('game_launching_date',$this->game_launching_date,true);
		$criteria->compare('game_website_name',$this->game_website_name,true);
		$criteria->compare('game_website_url',$this->game_website_url,true);
		$criteria->compare('game_esrb',$this->game_esrb,true);
		$criteria->compare('game_keyword',$this->game_keyword,true);
		$criteria->compare('game_download_client_url',$this->game_download_client_url,true);
		$criteria->compare('game_signup_account_url',$this->game_signup_account_url,true);
		$criteria->compare('game_spec_minimum',$this->game_spec_minimum,true);
		$criteria->compare('game_spec_recommended',$this->game_spec_recommended,true);
		$criteria->compare('game_template',$this->game_template,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}