<?php

/**
 * This is the model class for table "categories_groups".
 *
 * The followings are the available columns in table 'categories_groups':
 * @property integer $linkid
 * @property integer $categories_id
 * @property integer $groups_id
 */
class CategoriesGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id, groups_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('linkid, categories_id, groups_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'linkid' => 'Linkid',
			'categories_id' => 'Categories',
			'groups_id' => 'Groups',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('linkid',$this->linkid);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('groups_id',$this->groups_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getGameIDsByGroupID($group_id) {
        $return_array = array();
        
        $sql = "	SELECT categories_id 
                    FROM " . $this->tableName() . " 
                    WHERE groups_id = :groups_id";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":groups_id", $group_id, PDO::PARAM_INT);

        if ($dataset = $command->queryAll()) {
            foreach ($dataset as $row) {
                $return_array[$row['categories_id']] = $row['categories_id'];
            }
        }
        
        $sql = "	SELECT categories_id 
                    FROM " . $this->tableName() . " 
                    WHERE groups_id = 0";
        $command = $this->conn->createCommand($sql);
        
        if ($dataset = $command->queryAll()) {
            foreach ($dataset as $row) {
                $return_array[$row['categories_id']] = $row['categories_id'];
            }
        }
        
        return $return_array;
    }
}