<?php

/**
 * This is the model class for table "categories_payment_methods_restrictions".
 *
 * The followings are the available columns in table 'categories_payment_methods_restrictions':
 * @property string $id
 * @property string $categories_id
 * @property string $restriction_info
 * @property string $created_date
 * @property string $changed_by
 */
class CategoriesPaymentMethodsRestrictionsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPaymentMethodsRestrictionsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_payment_methods_restrictions';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id, restriction_info, created_date, changed_by', 'required'),
			array('categories_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, pm_id, categories_id, restriction_info, created_date, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'categories_id' => 'Categories ID',
			'restriction_info' => 'Restriction Info',
			'created_date' => 'Created Date',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id,true);
		$criteria->compare('categories_id',$this->categories_id,true);
		$criteria->compare('restriction_info',$this->restriction_info,true);
		$criteria->compare('created_date',$this->created_date,true);
		$criteria->compare('changed_by',$this->changed_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	protected function getPmRestrictions($field, $category_id) {
		$return_str = '';

        $criteria = new CDbCriteria;
		$criteria->select = 'restriction_info';
		$criteria->condition = 'categories_id = :categories_id AND restriction_mode = :restriction_mode';
		$criteria->params = array(
            ':categories_id' => $category_id,
            ':restriction_mode' => $field
        );
		if ($result = $this->model()->find($criteria)) {
            $return_str = $result->restriction_info;
        }

        return $return_str;
	}

	public function getProductsPmRestrictionsAllow($category_id) {
        return $this->getPmRestrictions('Allow', $category_id);
    }
    
    public function getProductsPmRestrictionsDisallow($category_id) {
        return $this->getPmRestrictions('Disallow', $category_id);
    }
}