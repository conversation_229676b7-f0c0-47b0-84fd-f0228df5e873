<?php

/**
 * This is the model class for table "categories_product_types".
 *
 * The followings are the available columns in table 'categories_product_types':
 * @property integer $categories_id
 * @property integer $custom_products_type_id
 * @property integer $custom_products_type_child_id
 */
class CategoriesProductTypesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesProductTypesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_product_types';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_id, custom_products_type_id, custom_products_type_child_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_id, custom_products_type_id, custom_products_type_child_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_id' => 'Categories',
			'custom_products_type_id' => 'Custom Products Type',
			'custom_products_type_child_id' => 'Custom Products Type Child',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('custom_products_type_child_id',$this->custom_products_type_child_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getCustomProductsTypeChildID($categories_id) {
        $return_array = array();
		
        $criteria = new CDbCriteria;
		$criteria->select = 'custom_products_type_child_id';
		$criteria->condition = 'categories_id = :categories_id';
		$criteria->params = array(
            ':categories_id' => $categories_id,
        );
        $criteria->order = 'custom_products_type_child_id';
        
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->custom_products_type_child_id;
            }
        }
        
		return $return_array;
    }
}