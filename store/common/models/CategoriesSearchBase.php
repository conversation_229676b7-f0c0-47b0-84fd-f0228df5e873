<?php

/**
 * This is the model class for table "categories_search".
 *
 * The followings are the available columns in table 'categories_search':
 * @property string $categories_id
 * @property string $language_id
 * @property string $search_value
 */
class CategoriesSearchBase extends MainModel
{
    public $score;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesSearchBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_search';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('search_value', 'required'),
			array('categories_id, language_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_id, language_id, search_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_id' => 'Categories',
			'language_id' => 'Language',
			'search_value' => 'Search Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_id',$this->categories_id,true);
		$criteria->compare('language_id',$this->language_id,true);
		$criteria->compare('search_value',$this->search_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getCategoryIDsByMatchingAgainstScore($keyword) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'DISTINCT *, MATCH(search_value) AGAINST(:keyword) AS score';
		$criteria->condition = 'MATCH(search_value) AGAINST(:keyword)';
		$criteria->params = array(
            ':keyword' => $keyword,
        );
        $criteria->order = 'score DESC';
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_id] = $result->getAttributes(NULL);
            }
        }
        
		return $return_array;
    }
    
    public function getCategoryIDsByKeyword($keyword) {
        $return_array = array();
        $keyword = addcslashes($keyword, '%_');
        
        $criteria = new CDbCriteria;
		$criteria->select = 'DISTINCT *';
		$criteria->condition = 'search_value LIKE :keyword';
		$criteria->params = array(
            ':keyword' => "%$keyword%",
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->categories_id] = $result->getAttributes(NULL);
            }
        }
        
		return $return_array;
    }
    
    public function getCategoryIDsByKeywordSelective($keyword, $cat_ids) {
        $return_array = array();
        $keyword = addcslashes($keyword, '%_');
        
        $criteria = new CDbCriteria;
		$criteria->select = 'DISTINCT *';
		$criteria->condition = 'search_value LIKE :keyword';
		$criteria->params = array(
            ':keyword' => "%$keyword%",
        );
        $criteria->addInCondition('categories_id', $cat_ids); 
                
		if ($results = $this->model()->findAll($criteria)) {

            foreach ($results as $record) {
                if(!in_array($record->categories_id, $return_array)){
                    $return_array[] = $record->categories_id;
                }
            }
        }
        
		return $return_array;
    }
    
    public function getCategoryIDsByMatchingAgainstScoreSelective($keyword, $cat_ids) {
        $return_array = array();
        $criteria = new CDbCriteria;
		$criteria->select = 'DISTINCT *, MATCH(search_value) AGAINST(:keyword) AS score';
		$criteria->condition = 'MATCH(search_value) AGAINST(:keyword)';
		$criteria->params = array(
            ':keyword' => $keyword,
        );
        $criteria->addInCondition('categories_id', $cat_ids); 
        $criteria->order = 'score DESC';
        
		if ($results = $this->model()->findAll($criteria)) {

            foreach ($results as $record) {
                if(!in_array($record->categories_id, $return_array)){
                    $return_array[] = $record->categories_id;
                }
            }
        }  
		return $return_array;
    }
}