<?php

/**
 * This is the model class for table "categories_structures".
 *
 * The followings are the available columns in table 'categories_structures':
 * @property string $categories_structures_key
 * @property string $categories_structures_value
 */
class CategoriesStructuresBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesStructuresBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_structures';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_structures_key', 'length', 'max'=>64),
			array('categories_structures_value', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_structures_key, categories_structures_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_structures_key' => 'Categories Structures Key',
			'categories_structures_value' => 'Categories Structures Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_structures_key',$this->categories_structures_key,true);
		$criteria->compare('categories_structures_value',$this->categories_structures_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getIDs() {
        $return_str = '';
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_structures_value';
        $criteria->condition = 'categories_structures_key = "games"';
		
		if ($result = $this->model()->find($criteria)) {
                $return_str = $result->categories_structures_value;
        }
        
		return $return_str;
    }
}