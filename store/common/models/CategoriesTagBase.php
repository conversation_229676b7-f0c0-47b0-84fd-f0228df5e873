<?php

/**
 * This is the model class for table "categories_tag".
 *
 * The followings are the available columns in table 'categories_tag':
 * @property integer $tag_id
 * @property string $tag_key
 * @property integer $tag_lft
 * @property integer $tag_rgt
 * @property integer $tag_status
 */
class CategoriesTagBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTagBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_tag';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tag_key, tag_lft, tag_rgt', 'required'),
			array('tag_lft, tag_rgt, tag_status', 'numerical', 'integerOnly'=>true),
			array('tag_key', 'length', 'max'=>125),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tag_id, tag_key, tag_lft, tag_rgt, tag_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'parent' => array(self::HAS_ONE, 'CategoriesTagBase', 'tag_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tag_id' => 'Tag',
			'tag_key' => 'Tag Key',
			'tag_lft' => 'Tag Lft',
			'tag_rgt' => 'Tag Rgt',
			'tag_status' => 'Tag Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tag_id',$this->tag_id);
		$criteria->compare('tag_key',$this->tag_key,true);
		$criteria->compare('tag_lft',$this->tag_lft);
		$criteria->compare('tag_rgt',$this->tag_rgt);
		$criteria->compare('tag_status',$this->tag_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getTagByKey($tag_key) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'tag_id, tag_key, tag_lft, tag_rgt, tag_status';
		$criteria->condition = 'tag_key = :tag_key';
		$criteria->params = array(
            ':tag_key' => $tag_key
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
}
        
		return $return_array;
    }

    private function updateRgtGreaterThanMyRgt($my_rgt, $interval = '+2') {
        //UPDATE nested_category SET rgt = rgt + 2 WHERE rgt > @myRight;
        $attributes = array(
            'tag_rgt'=> $interval
        );
		$condition = 'tag_rgt > :my_rgt';
		$params = array(
            ':my_rgt' => $my_rgt
        );
		
        return $this->model()->updateCounters($attributes, $condition, $params);	
    }

    private function updateLftGreaterThanMyLft($my_lft, $interval = '+2') {
        //UPDATE nested_category SET lft = lft + 2 WHERE lft > @myRight;
        $attributes = array(
            'tag_lft' => $interval
        );
		$condition = 'tag_lft > :my_lft';
		$params = array(
            ':my_lft' => $my_lft
        );
		
        return $this->model()->updateCounters($attributes,$condition,$params);	
    }

    private function insertTag($tag_key, $my_lft, $my_rgt) {
        $return_int = false;
        
		try {
            $return_int = $this->saveNewRecord(array(
                'tag_key' => $tag_key,
                'tag_lft' => $my_lft,
                'tag_rgt' => $my_rgt
            ), true);
            
        } catch (Exception $e) {}
		
		return $return_int;		
    }
    
	private function deleteTagBetweenLft($my_lft, $my_rgt){
        //DELETE FROM nested_category WHERE lft BETWEEN @myLeft AND @myRight;
		$criteria=new CDbCriteria;
		$criteria->condition = 'tag_lft BETWEEN :my_lft AND :my_rgt';
		$criteria->params = array(
            ':my_lft' => $my_lft,
            ':my_rgt' => $my_rgt
        );
		$this->model()->deleteAll($criteria);
	}
    
    public function addNewTag($key, $new_label, $type = 'new_node') {
        $return_int = 0;

        switch ($type) {
            case 'new_node':
                // key : parent node key
                $return_int = $this->addNewFirstChildTag($key, $new_label);
                break;
            case 'new_right':
                // key : left node key
                $return_int = $this->addNewRightSameLayerTag($key, $new_label);
                break;
            case 'new_left':
                // key : parent node key
                $return_int = $this->addNewFirstChildTag($key, $new_label);
                break;
        }

        return $return_int;
    }
    
    public function addNewFirstChildTag($parent_key, $new_label) {
        $return_int = 0;
        $this->lockTable("WRITE");

        if ($tag_array = $this->getTagByKey($parent_key)) {
            $this->updateRgtGreaterThanMyRgt($tag_array['tag_lft'], '+2');
            $this->updateLftGreaterThanMyLft($tag_array['tag_lft'], '+2');
            
            //INSERT INTO nested_category(name, lft, rgt) VALUES('GAME CONSOLES', @myRight + 1, @myRight + 2);
            $return_int = $this->insertTag($new_label, $tag_array['tag_lft']+1, $tag_array['tag_lft']+2);
        }

        $this->unlockTable();
        return $return_int;
    }
    
    public function addNewRightSameLayerTag($parent_key, $new_label) {
        $return_int = 0;
        $this->lockTable("WRITE");

        if ($tag_array = $this->getTagByKey($parent_key)) {
            $this->updateRgtGreaterThanMyRgt($tag_array['tag_rgt'], '+2');
            $this->updateLftGreaterThanMyLft($tag_array['tag_rgt'], '+2');
            
            //INSERT INTO nested_category(name, lft, rgt) VALUES('GAME CONSOLES', @myRight + 1, @myRight + 2);
            $return_int = $this->insertTag($new_label, $tag_array['tag_rgt']+1, $tag_array['tag_rgt']+2);
        }

        $this->unlockTable();
        return $return_int;
    }
    
    public function deleteTagAndItsChild($key_to_delete) {
        $this->lockTable("WRITE");
        
        if ($tag_array = $this->getTagByKey($key_to_delete)) {
            $myWidth = $tag_array['tag_rgt'] - $tag_array['tag_lft'] + 1;
            
            $this->deleteTagBetweenLft($tag_array['tag_lft'], $tag_array['tag_rgt']);
            $this->updateRgtGreaterThanMyRgt($tag_array['tag_rgt'], -1*$myWidth);
            $this->updateLftGreaterThanMyLft($tag_array['tag_rgt'], -1*$myWidth);
            
        }
        
        $this->unlockTable();
    }
    
    public function getSinglePathByTagID($tag_id) {
        $return_array = array();

        $sql = "	SELECT parent.tag_id, parent.tag_key
                    FROM " . $this->tableName() . " AS node,
                            " . $this->tableName() . " AS parent
                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                        AND node.tag_id = :tag_id
                        AND node.tag_status = 1
                    ORDER BY node.tag_lft";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":tag_id", $tag_id, PDO::PARAM_INT);
        if ($results = $command->queryAll()) {
            array_shift($results);
            $return_array = $results;
        }

		return $return_array;
    }
    
    public function getFullPathByLabel() {
        $return_array = array();
        /*
        SELECT CONCAT( REPEAT(' ', COUNT(parent.tag_key) - 1), node.tag_key) AS name
        FROM categories_tag AS node,
                categories_tag AS parent
        WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
        GROUP BY node.tag_key
        ORDER BY node.tag_lft;
         */
        
        $sql = "	SELECT (COUNT(parent.tag_key) - 1) AS depth, node.tag_id, node.tag_key
                    FROM " . $this->tableName() . " AS node,
                            " . $this->tableName() . " AS parent
                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                        AND node.tag_status = 1
                    GROUP BY node.tag_key
                    ORDER BY node.tag_lft";
        $command = $this->conn->createCommand($sql);
        if ($results = $command->queryAll()) {
            $return_array = $results;
        }

        return $return_array;
    }
    
    public function getTreeInfoByTagID($tag_id, $key = 'tag_id') {
        $return_array = array();

        if ($results = $this->getTreeByTagID($tag_id)) {
            foreach ($results as $info) {
                $return_array[] = $info[$key];
            }
        }

		return $return_array;
    }
    
    public function getTreeByTagID($tag_id) {
        $return_array = array();

        $sql = "	SELECT node.tag_id, node.tag_key
                    FROM " . $this->tableName() . " AS node,
                            " . $this->tableName() . " AS parent
                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                        AND parent.tag_id = :tag_id
                        AND parent.tag_status = 1
                    ORDER BY node.tag_lft";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":tag_id", $tag_id, PDO::PARAM_INT);
        if ($results = $command->queryAll()) {
            $return_array = $results;
        }

		return $return_array;
    }
    
    protected function checkTree() {
        $return_str = '';
        
        $sql = "	SELECT CONCAT( REPEAT( '-', (COUNT(parent.tag_key) - 1) ), node.tag_key) AS name
                    FROM " . $this->tableName() . " AS node,
                            " . $this->tableName() . " AS parent
                    WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                        AND node.tag_status = 1
                    GROUP BY node.tag_key
                    ORDER BY node.tag_lft";
        $command = $this->conn->createCommand($sql);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}