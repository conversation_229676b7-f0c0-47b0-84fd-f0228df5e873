<?php

/**
 * This is the model class for table "categories_tag_description".
 *
 * The followings are the available columns in table 'categories_tag_description':
 * @property string $tag_id
 * @property string $language_id
 * @property string $tag_title
 * @property string $tag_description
 * @property string $tag_featured_banner
 */
class CategoriesTagDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTagDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_tag_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tag_id, language_id, tag_title, tag_description', 'required'),
			array('tag_id, language_id', 'length', 'max'=>11),
			array('tag_title, tag_description', 'length', 'max'=>125),
			array('tag_featured_banner', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tag_id, language_id, tag_title, tag_description, tag_featured_banner', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tag_id' => 'Tag',
			'language_id' => 'Language',
			'tag_title' => 'Tag Title',
			'tag_description' => 'Tag Description',
			'tag_featured_banner' => 'Tag Featured Banner',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tag_id',$this->tag_id,true);
		$criteria->compare('language_id',$this->language_id,true);
		$criteria->compare('tag_title',$this->tag_title,true);
		$criteria->compare('tag_description',$this->tag_description,true);
		$criteria->compare('tag_featured_banner',$this->tag_featured_banner,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getTagInfo($tag_id, $language_id = 0, $default_language_id = 0) {
        $return_array = array();
        
        $sql = "	SELECT tag_title, tag_description, tag_featured_banner
                    FROM " . $this->tableName() . " 
                    WHERE tag_id = :tag_id
                        AND tag_title <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(tag_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE tag_id = :tag_id 
                                AND language_id = :language_id
                                AND tag_title <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":tag_id", $tag_id, PDO::PARAM_INT);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_array = $value;
        }
        
        return $return_array;
    }
}