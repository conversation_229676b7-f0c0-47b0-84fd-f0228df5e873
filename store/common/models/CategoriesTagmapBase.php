<?php

/**
 * This is the model class for table "categories_tagmap".
 *
 * The followings are the available columns in table 'categories_tagmap':
 * @property string $id
 * @property string $game_id
 * @property string $tag_id
 */
class CategoriesTagmapBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTagmapBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_tagmap';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_id, tag_id', 'required'),
			array('game_id', 'length', 'max'=>32),
			array('tag_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, game_id, tag_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'game_id' => 'Game',
			'tag_id' => 'Tag',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id,true);
		$criteria->compare('game_id',$this->game_id,true);
		$criteria->compare('tag_id',$this->tag_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addNewbinding($game_id, $tag_id) {
        $return_int = false;
        
		try {
            $return_int = $this->saveNewRecord(array(
                'game_id' => $game_id,
                'tag_id' => $tag_id,
            ), true);
            
        } catch (Exception $e) {}
		
		return $return_int;		
    }
    
    public function getGameIDFilteredByTagID($category_id_array, $tag_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'game_id';
        $criteria->condition = 'tag_id = :tag_id';
		$criteria->params = array(
            ':tag_id' => $tag_id
        );
        $criteria->addInCondition('game_id', $category_id_array);
        
		if ($result = $this->model()->findAll($criteria)) {
            foreach ($result as $record) {
                $return_array[] = $record->game_id;
            }
        }
        
		return $return_array;
    }
    
    public function getGameIDsByTagIDs($tag_id_array) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'game_id';
        $criteria->addInCondition('tag_id', $tag_id_array);
        
		if ($result = $this->model()->findAll($criteria)) {
            foreach ($result as $record) {
                $return_array[] = $record->game_id;
            }
        }
        
		return $return_array;
    }
    
    public function getTagIDByGameID($game_id) {
        $return_int = 0;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'tag_id';
        $criteria->condition = 'game_id = :game_id';
		$criteria->params = array(
            ':game_id' => $game_id
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_int = $result->tag_id;
        }
        
		return $return_int;
    }
    
    public function getCagetoryIDByTagID($tag_id) {
        $return_array = array();
        
        $sql = '	SELECT game_id
                    FROM ' . $this->tableName() . '
                    WHERE tag_id = :tag_id';
        
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":tag_id", $tag_id, PDO::PARAM_INT);
        if ($value = $command->queryColumn()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
    
    public function getCagetoryIDGroupByTagID($tag_id, $category_id_array, $limit = NULL) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'game_id';
        $criteria->addInCondition('game_id', $category_id_array);
        $criteria->condition = 'tag_id = :tag_id';
		$criteria->params = array(
            ':tag_id' => $tag_id
        );
        
        if ($limit) {
            $criteria->limit = $limit;
        }
        
		if ($result = $this->model()->findAll($criteria)) {
            foreach ($result as $record) {
                $return_array[] = $record->game_id;
            }
        }
        
		return $return_array;
    }
}