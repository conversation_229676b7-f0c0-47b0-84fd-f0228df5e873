<?php

/**
 * This is the model class for table "categories_types".
 *
 * The followings are the available columns in table 'categories_types':
 * @property integer $categories_types_id
 * @property string $categories_types_value
 * @property string $categories_types_parent_id
 * @property integer $categories_types_display
 */
class CategoriesTypesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTypesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_types';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_types_display', 'numerical', 'integerOnly'=>true),
			array('categories_types_value, categories_types_parent_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_types_id, categories_types_value, categories_types_parent_id, categories_types_display', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_types_id' => 'Categories Types',
			'categories_types_value' => 'Categories Types Value',
			'categories_types_parent_id' => 'Categories Types Parent',
			'categories_types_display' => 'Categories Types Display',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_types_id',$this->categories_types_id);
		$criteria->compare('categories_types_value',$this->categories_types_value,true);
		$criteria->compare('categories_types_parent_id',$this->categories_types_parent_id,true);
		$criteria->compare('categories_types_display',$this->categories_types_display);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}