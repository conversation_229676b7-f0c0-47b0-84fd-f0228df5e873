<?php

/**
 * This is the model class for table "categories_types_sets".
 *
 * The followings are the available columns in table 'categories_types_sets':
 * @property integer $categories_types_sets_id
 * @property integer $categories_types_groups_id
 * @property integer $custom_products_type_id
 * @property integer $categories_types_root_id
 */
class CategoriesTypesSetsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTypesSetsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_types_sets';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_types_groups_id, custom_products_type_id, categories_types_root_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('categories_types_sets_id, categories_types_groups_id, custom_products_type_id, categories_types_root_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_types_sets_id' => 'Categories Types Sets',
			'categories_types_groups_id' => 'Categories Types Groups',
			'custom_products_type_id' => 'Custom Products Type',
			'categories_types_root_id' => 'Categories Types Root',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_types_sets_id',$this->categories_types_sets_id);
		$criteria->compare('categories_types_groups_id',$this->categories_types_groups_id);
		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('categories_types_root_id',$this->categories_types_root_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}