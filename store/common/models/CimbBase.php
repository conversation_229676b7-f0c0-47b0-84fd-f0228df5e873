<?php

/**
 * This is the model class for table "cimb".
 *
 * The followings are the available columns in table 'cimb':
 * @property integer $orders_id
 * @property string $cimb_channel_id
 * @property string $cimb_reference_no
 * @property string $cimb_amount
 * @property string $cimb_currency
 * @property string $cimb_user_full_name
 * @property string $cimb_transaction_date
 * @property string $cimb_transaction_time
 * @property string $cimb_status
 */
class CimbBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CimbBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cimb';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('cimb_channel_id', 'length', 'max'=>4),
			array('cimb_reference_no, cimb_amount', 'length', 'max'=>15),
			array('cimb_currency', 'length', 'max'=>3),
			array('cimb_user_full_name', 'length', 'max'=>40),
			array('cimb_transaction_date', 'length', 'max'=>8),
			array('cimb_transaction_time', 'length', 'max'=>6),
			array('cimb_status', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, cimb_channel_id, cimb_reference_no, cimb_amount, cimb_currency, cimb_user_full_name, cimb_transaction_date, cimb_transaction_time, cimb_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'cimb_channel_id' => 'Cimb Channel',
			'cimb_reference_no' => 'Cimb Reference No',
			'cimb_amount' => 'Cimb Amount',
			'cimb_currency' => 'Cimb Currency',
			'cimb_user_full_name' => 'Cimb User Full Name',
			'cimb_transaction_date' => 'Cimb Transaction Date',
			'cimb_transaction_time' => 'Cimb Transaction Time',
			'cimb_status' => 'Cimb Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('cimb_channel_id',$this->cimb_channel_id,true);
		$criteria->compare('cimb_reference_no',$this->cimb_reference_no,true);
		$criteria->compare('cimb_amount',$this->cimb_amount,true);
		$criteria->compare('cimb_currency',$this->cimb_currency,true);
		$criteria->compare('cimb_user_full_name',$this->cimb_user_full_name,true);
		$criteria->compare('cimb_transaction_date',$this->cimb_transaction_date,true);
		$criteria->compare('cimb_transaction_time',$this->cimb_transaction_time,true);
		$criteria->compare('cimb_status',$this->cimb_status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}