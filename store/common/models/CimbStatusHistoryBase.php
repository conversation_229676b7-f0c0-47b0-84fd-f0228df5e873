<?php

/**
 * This is the model class for table "cimb_status_history".
 *
 * The followings are the available columns in table 'cimb_status_history':
 * @property integer $cimb_status_history_id
 * @property integer $orders_id
 * @property string $cimb_date
 * @property string $cimb_status
 * @property string $cimb_description
 */
class CimbStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CimbStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cimb_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('cimb_status', 'length', 'max'=>1),
			array('cimb_description', 'length', 'max'=>64),
			array('cimb_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cimb_status_history_id, orders_id, cimb_date, cimb_status, cimb_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cimb_status_history_id' => 'Cimb Status History',
			'orders_id' => 'Orders',
			'cimb_date' => 'Cimb Date',
			'cimb_status' => 'Cimb Status',
			'cimb_description' => 'Cimb Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cimb_status_history_id',$this->cimb_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('cimb_date',$this->cimb_date,true);
		$criteria->compare('cimb_status',$this->cimb_status,true);
		$criteria->compare('cimb_description',$this->cimb_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}