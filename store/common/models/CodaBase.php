<?php

/**
 * This is the model class for table "coda".
 *
 * The followings are the available columns in table 'coda':
 * @property string $coda_order_id
 * @property string $coda_txn_id
 * @property integer $coda_result_code
 * @property string $coda_total_price
 * @property string $coda_checksum
 */
class CodaBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CodaBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coda';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coda_result_code', 'numerical', 'integerOnly'=>true),
			array('coda_order_id', 'length', 'max'=>11),
			array('coda_txn_id', 'length', 'max'=>80),
			array('coda_total_price', 'length', 'max'=>15),
			array('coda_checksum', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coda_order_id, coda_txn_id, coda_result_code, coda_total_price, coda_checksum', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coda_order_id' => 'Coda Order',
			'coda_txn_id' => 'Coda Txn',
			'coda_result_code' => 'Coda Result Code',
			'coda_total_price' => 'Coda Total Price',
			'coda_checksum' => 'Coda Checksum',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coda_order_id',$this->coda_order_id,true);
		$criteria->compare('coda_txn_id',$this->coda_txn_id,true);
		$criteria->compare('coda_result_code',$this->coda_result_code);
		$criteria->compare('coda_total_price',$this->coda_total_price,true);
		$criteria->compare('coda_checksum',$this->coda_checksum,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}