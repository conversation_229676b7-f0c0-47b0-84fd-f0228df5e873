<?php

/**
 * This is the model class for table "coda_status_history".
 *
 * The followings are the available columns in table 'coda_status_history':
 * @property integer $coda_status_history_id
 * @property integer $coda_orders_id
 * @property string $coda_date
 * @property integer $coda_result_code
 * @property string $coda_result_description
 * @property string $coda_total_price
 * @property string $changed_by
 */
class CodaStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CodaStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coda_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coda_orders_id, coda_result_code', 'numerical', 'integerOnly'=>true),
			array('coda_result_description', 'length', 'max'=>255),
			array('coda_total_price', 'length', 'max'=>15),
			array('changed_by', 'length', 'max'=>128),
			array('coda_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coda_status_history_id, coda_orders_id, coda_date, coda_result_code, coda_result_description, coda_total_price, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coda_status_history_id' => 'Coda Status History',
			'coda_orders_id' => 'Coda Orders',
			'coda_date' => 'Coda Date',
			'coda_result_code' => 'Coda Result Code',
			'coda_result_description' => 'Coda Result Description',
			'coda_total_price' => 'Coda Total Price',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coda_status_history_id',$this->coda_status_history_id);
		$criteria->compare('coda_orders_id',$this->coda_orders_id);
		$criteria->compare('coda_date',$this->coda_date,true);
		$criteria->compare('coda_result_code',$this->coda_result_code);
		$criteria->compare('coda_result_description',$this->coda_result_description,true);
		$criteria->compare('coda_total_price',$this->coda_total_price,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}