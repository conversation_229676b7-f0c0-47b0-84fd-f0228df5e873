<?php

/**
 * This is the model class for table "competitors".
 *
 * The followings are the available columns in table 'competitors':
 * @property integer $competitors_id
 * @property string $competitors_name
 * @property string $currency_symbol_left
 * @property string $currency_symbol_right
 * @property integer $number_of_connection
 * @property integer $connection_session_delay_time
 */
class CompetitorsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CompetitorsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'competitors';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('number_of_connection, connection_session_delay_time', 'numerical', 'integerOnly'=>true),
			array('competitors_name', 'length', 'max'=>255),
			array('currency_symbol_left, currency_symbol_right', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('competitors_id, competitors_name, currency_symbol_left, currency_symbol_right, number_of_connection, connection_session_delay_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'competitors_id' => 'Competitors',
			'competitors_name' => 'Competitors Name',
			'currency_symbol_left' => 'Currency Symbol Left',
			'currency_symbol_right' => 'Currency Symbol Right',
			'number_of_connection' => 'Number Of Connection',
			'connection_session_delay_time' => 'Connection Session Delay Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('competitors_id',$this->competitors_id);
		$criteria->compare('competitors_name',$this->competitors_name,true);
		$criteria->compare('currency_symbol_left',$this->currency_symbol_left,true);
		$criteria->compare('currency_symbol_right',$this->currency_symbol_right,true);
		$criteria->compare('number_of_connection',$this->number_of_connection);
		$criteria->compare('connection_session_delay_time',$this->connection_session_delay_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}