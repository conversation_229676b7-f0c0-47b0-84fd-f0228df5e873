<?php

/**
 * This is the model class for table "competitors_to_categories".
 *
 * The followings are the available columns in table 'competitors_to_categories':
 * @property integer $competitors_to_categories_id
 * @property integer $competitors_id
 * @property integer $categories_id
 * @property double $competitors_weight
 */
class CompetitorsToCategoriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CompetitorsToCategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'competitors_to_categories';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('competitors_id, categories_id', 'numerical', 'integerOnly'=>true),
			array('competitors_weight', 'numerical'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('competitors_to_categories_id, competitors_id, categories_id, competitors_weight', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'competitors_to_categories_id' => 'Competitors To Categories',
			'competitors_id' => 'Competitors',
			'categories_id' => 'Categories',
			'competitors_weight' => 'Competitors Weight',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('competitors_to_categories_id',$this->competitors_to_categories_id);
		$criteria->compare('competitors_id',$this->competitors_id);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('competitors_weight',$this->competitors_weight);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}