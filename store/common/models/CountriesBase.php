<?php

/**
 * This is the model class for table "countries".
 *
 * The followings are the available columns in table 'countries':
 * @property integer $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property integer $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property integer $address_format_id
 * @property integer $maxmind_support
 * @property string $aft_risk_type
 * @property integer $countries_display
 * @property integer $telesign_support
 */
class CountriesBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CountriesBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()
    {
        return Yii::app()->db_rr;
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'countries';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array(
                'countries_currencies_id, address_format_id, maxmind_support, countries_display, telesign_support',
                'numerical', 'integerOnly' => true),
            array('countries_name, countries_website_domain', 'length', 'max' => 64),
            array('countries_iso_code_2', 'length', 'max' => 2), array('countries_iso_code_3', 'length', 'max' => 3),
            array('countries_international_dialing_code', 'length', 'max' => 5),
            array('aft_risk_type', 'length', 'max' => 10), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'countries_id, countries_name, countries_iso_code_2, countries_iso_code_3, countries_currencies_id, countries_international_dialing_code, countries_website_domain, address_format_id, maxmind_support, aft_risk_type, countries_display, telesign_support',
                'safe', 'on' => 'search'),);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'otc' => array(self::HAS_ONE, 'OrdersTaxConfigurationBase', ''),);
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'countries_id' => 'Countries', 'countries_name' => 'Countries Name',
            'countries_iso_code_2' => 'Countries Iso Code 2', 'countries_iso_code_3' => 'Countries Iso Code 3',
            'countries_currencies_id' => 'Countries Currencies',
            'countries_international_dialing_code' => 'Countries International Dialing Code',
            'countries_website_domain' => 'Countries Website Domain', 'address_format_id' => 'Address Format',
            'maxmind_support' => 'Maxmind Support', 'aft_risk_type' => 'Aft Risk Type',
            'countries_display' => 'Countries Display', 'telesign_support' => 'Telesign Support',);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('countries_id', $this->countries_id);
        $criteria->compare('countries_name', $this->countries_name, true);
        $criteria->compare('countries_iso_code_2', $this->countries_iso_code_2, true);
        $criteria->compare('countries_iso_code_3', $this->countries_iso_code_3, true);
        $criteria->compare('countries_currencies_id', $this->countries_currencies_id);
        $criteria->compare('countries_international_dialing_code', $this->countries_international_dialing_code, true);
        $criteria->compare('countries_website_domain', $this->countries_website_domain, true);
        $criteria->compare('address_format_id', $this->address_format_id);
        $criteria->compare('maxmind_support', $this->maxmind_support);
        $criteria->compare('aft_risk_type', $this->aft_risk_type, true);
        $criteria->compare('countries_display', $this->countries_display);
        $criteria->compare('telesign_support', $this->telesign_support);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,));
    }

}