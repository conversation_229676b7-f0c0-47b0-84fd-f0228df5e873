<?php

/**
 * This is the model class for table "countries_content".
 *
 * The followings are the available columns in table 'countries_content':
 * @property string $countries_id
 * @property string $geo_zone_id
 * @property string $tab_content
 * @property string $footer_all_payment_image
 */
class CountriesContentBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesContentBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'countries_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('countries_id, geo_zone_id', 'length', 'max'=>11),
			array('tab_content, footer_all_payment_image', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('countries_id, geo_zone_id, tab_content, footer_all_payment_image', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'countries_id' => 'Countries',
			'geo_zone_id' => 'Geo Zone',
			'tab_content' => 'Tab Content',
			'footer_all_payment_image' => 'Footer All Payment Image',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('countries_id',$this->countries_id,true);
		$criteria->compare('geo_zone_id',$this->geo_zone_id,true);
		$criteria->compare('tab_content',$this->tab_content,true);
		$criteria->compare('footer_all_payment_image',$this->footer_all_payment_image,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}