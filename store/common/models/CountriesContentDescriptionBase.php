<?php

/**
 * This is the model class for table "countries_content_description".
 *
 * The followings are the available columns in table 'countries_content_description':
 * @property string $countries_id
 * @property string $language_id
 * @property string $geo_zone_id
 * @property string $slider_content
 * @property string $banner_content
 */
class CountriesContentDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesContentDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'countries_content_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('countries_id, language_id, geo_zone_id', 'length', 'max'=>11),
			array('slider_content, banner_content', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('countries_id, language_id, geo_zone_id, slider_content, banner_content', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'countries_id' => 'Countries',
			'language_id' => 'Language',
			'geo_zone_id' => 'Geo Zone',
			'slider_content' => 'Slider Content',
			'banner_content' => 'Banner Content',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('countries_id',$this->countries_id,true);
		$criteria->compare('language_id',$this->language_id,true);
		$criteria->compare('geo_zone_id',$this->geo_zone_id,true);
		$criteria->compare('slider_content',$this->slider_content,true);
		$criteria->compare('banner_content',$this->banner_content,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}