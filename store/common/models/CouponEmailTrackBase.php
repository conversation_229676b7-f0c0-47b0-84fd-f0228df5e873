<?php

/**
 * This is the model class for table "coupon_email_track".
 *
 * The followings are the available columns in table 'coupon_email_track':
 * @property integer $unique_id
 * @property integer $coupon_id
 * @property integer $customer_id_sent
 * @property string $sent_role
 * @property string $sent_email_address
 * @property string $sent_firstname
 * @property string $sent_lastname
 * @property string $emailed_to
 * @property string $date_sent
 */
class CouponEmailTrackBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponEmailTrackBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupon_email_track';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_id, customer_id_sent', 'numerical', 'integerOnly'=>true),
			array('sent_role', 'length', 'max'=>10),
			array('sent_email_address, emailed_to', 'length', 'max'=>96),
			array('sent_firstname, sent_lastname', 'length', 'max'=>32),
			array('date_sent', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('unique_id, coupon_id, customer_id_sent, sent_role, sent_email_address, sent_firstname, sent_lastname, emailed_to, date_sent', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'unique_id' => 'Unique',
			'coupon_id' => 'Coupon',
			'customer_id_sent' => 'Customer Id Sent',
			'sent_role' => 'Sent Role',
			'sent_email_address' => 'Sent Email Address',
			'sent_firstname' => 'Sent Firstname',
			'sent_lastname' => 'Sent Lastname',
			'emailed_to' => 'Emailed To',
			'date_sent' => 'Date Sent',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('unique_id',$this->unique_id);
		$criteria->compare('coupon_id',$this->coupon_id);
		$criteria->compare('customer_id_sent',$this->customer_id_sent);
		$criteria->compare('sent_role',$this->sent_role,true);
		$criteria->compare('sent_email_address',$this->sent_email_address,true);
		$criteria->compare('sent_firstname',$this->sent_firstname,true);
		$criteria->compare('sent_lastname',$this->sent_lastname,true);
		$criteria->compare('emailed_to',$this->emailed_to,true);
		$criteria->compare('date_sent',$this->date_sent,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}