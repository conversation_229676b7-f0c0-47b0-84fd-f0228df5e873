<?php

/**
 * This is the model class for table "coupon_gv_customer".
 *
 * The followings are the available columns in table 'coupon_gv_customer':
 * @property integer $customer_id
 * @property string $amount
 * @property string $sc_reversible_amount
 * @property string $sc_reversible_reserve_amount
 * @property string $sc_irreversible_amount
 * @property string $sc_last_modified
 * @property string $sc_irreversible_reserve_amount
 * @property integer $sc_currency_id
 * @property string $sc_conversion_date
 */
class CouponGvCustomerBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CouponGvCustomerBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'coupon_gv_customer';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('customer_id, sc_currency_id', 'numerical', 'integerOnly' => true),
            array('amount', 'length', 'max' => 8),
            array(
                'sc_reversible_amount, sc_reversible_reserve_amount, sc_irreversible_amount, sc_irreversible_reserve_amount',
                'length',
                'max' => 15
            ),
            array('sc_last_modified, sc_conversion_date', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'customer_id, amount, sc_reversible_amount, sc_reversible_reserve_amount, sc_irreversible_amount, sc_last_modified, sc_irreversible_reserve_amount, sc_currency_id, sc_conversion_date',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'customer_id' => 'Customer',
            'amount' => 'Amount',
            'sc_reversible_amount' => 'Sc Reversible Amount',
            'sc_reversible_reserve_amount' => 'Sc Reversible Reserve Amount',
            'sc_irreversible_amount' => 'Sc Irreversible Amount',
            'sc_last_modified' => 'Sc Last Modified',
            'sc_irreversible_reserve_amount' => 'Sc Irreversible Reserve Amount',
            'sc_currency_id' => 'Sc Currency',
            'sc_conversion_date' => 'Sc Conversion Date',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('customer_id', $this->customer_id);
        $criteria->compare('amount', $this->amount, true);
        $criteria->compare('sc_reversible_amount', $this->sc_reversible_amount, true);
        $criteria->compare('sc_reversible_reserve_amount', $this->sc_reversible_reserve_amount, true);
        $criteria->compare('sc_irreversible_amount', $this->sc_irreversible_amount, true);
        $criteria->compare('sc_last_modified', $this->sc_last_modified, true);
        $criteria->compare('sc_irreversible_reserve_amount', $this->sc_irreversible_reserve_amount, true);
        $criteria->compare('sc_currency_id', $this->sc_currency_id);
        $criteria->compare('sc_conversion_date', $this->sc_conversion_date, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getScCurrency($cid = 0)
    {
        if (empty($cid)) {
            $cid = Yii::app()->user->id;
        }
        $m_data = $this->findByPk($cid);
        return (isset($m_data->sc_currency_id) ? $m_data->sc_currency_id : false);
    }

    public function getScInfo($cid = 0)
    {
        if (empty($cid)) {
            $cid = Yii::app()->user->id;
        }
        $criteria = new CDbCriteria();
        $criteria->select = 'sc_reversible_amount,sc_reversible_reserve_amount,sc_irreversible_amount,sc_irreversible_reserve_amount,sc_currency_id';
        $criteria->condition = 'customer_id = :customerId';
        $criteria->params = array(':customerId' => $cid);
        $result = $this->model()->find($criteria);
        return $result;
    }

    public function scUpdateInfo($cid, $updateArr, $validateBalance = false, $total = 0)
    {

        $attributes = [];
        foreach ($updateArr as $column => $value) {
            if ($column == 'sc_reversible_amount' || $column == 'sc_irreversible_amount') {
                $attributes[$column] = $value;
                unset($updateArr[$column]);
            }
        }
        $condition = 'customer_id =:customerId';
        $params = array(':customerId' => $cid);
        $this->model()->updateAll($updateArr, $condition, $params);
        if (!empty($attributes)) {
            if ($validateBalance) {
                $condition .= ' AND (sc_reversible_amount + sc_irreversible_amount) >= :total';
                $params[':total'] = $total;
            }
            return ($this->model()->updateCounters($attributes, $condition, $params) > 0);
        }
    }

}