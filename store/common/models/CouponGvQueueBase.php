<?php

/**
 * This is the model class for table "coupon_gv_queue".
 *
 * The followings are the available columns in table 'coupon_gv_queue':
 * @property integer $unique_id
 * @property integer $customer_id
 * @property integer $order_id
 * @property string $amount
 * @property string $date_created
 * @property string $ipaddr
 * @property string $release_flag
 */
class CouponGvQueueBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponGvQueueBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupon_gv_queue';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customer_id, order_id', 'numerical', 'integerOnly'=>true),
			array('amount', 'length', 'max'=>8),
			array('ipaddr', 'length', 'max'=>32),
			array('release_flag', 'length', 'max'=>1),
			array('date_created', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('unique_id, customer_id, order_id, amount, date_created, ipaddr, release_flag', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'unique_id' => 'Unique',
			'customer_id' => 'Customer',
			'order_id' => 'Order',
			'amount' => 'Amount',
			'date_created' => 'Date Created',
			'ipaddr' => 'Ipaddr',
			'release_flag' => 'Release Flag',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('unique_id',$this->unique_id);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('order_id',$this->order_id);
		$criteria->compare('amount',$this->amount,true);
		$criteria->compare('date_created',$this->date_created,true);
		$criteria->compare('ipaddr',$this->ipaddr,true);
		$criteria->compare('release_flag',$this->release_flag,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}