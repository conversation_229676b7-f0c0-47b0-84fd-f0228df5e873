<?php

/**
 * This is the model class for table "coupon_redeem_track".
 *
 * The followings are the available columns in table 'coupon_redeem_track':
 * @property integer $unique_id
 * @property integer $coupon_id
 * @property integer $customer_id
 * @property string $redeem_date
 * @property string $redeem_ip
 * @property integer $order_id
 */
class CouponRedeemTrackBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponRedeemTrackBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupon_redeem_track';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_id, customer_id, order_id', 'numerical', 'integerOnly'=>true),
			array('redeem_ip', 'length', 'max'=>128),
			array('redeem_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('unique_id, coupon_id, customer_id, redeem_date, redeem_ip, order_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'unique_id' => 'Unique',
			'coupon_id' => 'Coupon',
			'customer_id' => 'Customer',
			'redeem_date' => 'Redeem Date',
			'redeem_ip' => 'Redeem Ip',
			'order_id' => 'Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('unique_id',$this->unique_id);
		$criteria->compare('coupon_id',$this->coupon_id);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('redeem_date',$this->redeem_date,true);
		$criteria->compare('redeem_ip',$this->redeem_ip,true);
		$criteria->compare('order_id',$this->order_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}