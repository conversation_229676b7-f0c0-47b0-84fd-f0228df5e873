<?php

/**
 * This is the model class for table "coupons".
 *
 * The followings are the available columns in table 'coupons':
 * @property integer $coupon_id
 * @property integer $coupon_generation_id
 * @property string $coupon_type
 * @property string $coupon_code
 * @property string $coupon_amount
 * @property string $coupon_minimum_order
 * @property string $coupon_start_date
 * @property string $coupon_expire_date
 * @property integer $uses_per_coupon
 * @property string $uses_per_coupon_unlimited
 * @property integer $uses_per_user
 * @property string $uses_per_user_unlimited
 * @property string $restrict_to_products
 * @property string $restrict_to_categories
 * @property string $restrict_to_customers
 * @property string $restrict_to_customers_groups
 * @property string $restrict_to_currency_id
 * @property string $restrict_to_payment_id
 * @property string $coupon_active
 * @property int $mobile_only
 * @property string $date_created
 * @property string $date_modified
 */
class CouponsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupons';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_generation_id, uses_per_coupon, uses_per_user', 'numerical', 'integerOnly'=>true),
			array('coupon_type, uses_per_coupon_unlimited, uses_per_user_unlimited, coupon_active', 'length', 'max'=>1),
			array('coupon_code', 'length', 'max'=>32),
			array('coupon_amount, coupon_minimum_order', 'length', 'max'=>8),
			array('restrict_to_customers_groups', 'length', 'max'=>64),
			array('coupon_start_date, coupon_expire_date, restrict_to_products, restrict_to_categories, restrict_to_customers, date_created, date_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coupon_id, coupon_generation_id, coupon_type, coupon_code, coupon_amount, coupon_minimum_order, coupon_start_date, coupon_expire_date, uses_per_coupon, uses_per_coupon_unlimited, uses_per_user, uses_per_user_unlimited, restrict_to_products, restrict_to_categories, restrict_to_customers, restrict_to_customers_groups, coupon_active, date_created, date_modified', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coupon_id' => 'Coupon',
			'coupon_generation_id' => 'Coupon Generation',
			'coupon_type' => 'Coupon Type',
			'coupon_code' => 'Coupon Code',
			'coupon_amount' => 'Coupon Amount',
			'coupon_minimum_order' => 'Coupon Minimum Order',
			'coupon_start_date' => 'Coupon Start Date',
			'coupon_expire_date' => 'Coupon Expire Date',
			'uses_per_coupon' => 'Uses Per Coupon',
			'uses_per_coupon_unlimited' => 'Uses Per Coupon Unlimited',
			'uses_per_user' => 'Uses Per User',
			'uses_per_user_unlimited' => 'Uses Per User Unlimited',
			'restrict_to_products' => 'Restrict To Products',
			'restrict_to_categories' => 'Restrict To Categories',
			'restrict_to_customers' => 'Restrict To Customers',
			'restrict_to_customers_groups' => 'Restrict To Customers Groups',
			'coupon_active' => 'Coupon Active',
			'date_created' => 'Date Created',
			'date_modified' => 'Date Modified',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coupon_id',$this->coupon_id);
		$criteria->compare('coupon_generation_id',$this->coupon_generation_id);
		$criteria->compare('coupon_type',$this->coupon_type,true);
		$criteria->compare('coupon_code',$this->coupon_code,true);
		$criteria->compare('coupon_amount',$this->coupon_amount,true);
		$criteria->compare('coupon_minimum_order',$this->coupon_minimum_order,true);
		$criteria->compare('coupon_start_date',$this->coupon_start_date,true);
		$criteria->compare('coupon_expire_date',$this->coupon_expire_date,true);
		$criteria->compare('uses_per_coupon',$this->uses_per_coupon);
		$criteria->compare('uses_per_coupon_unlimited',$this->uses_per_coupon_unlimited,true);
		$criteria->compare('uses_per_user',$this->uses_per_user);
		$criteria->compare('uses_per_user_unlimited',$this->uses_per_user_unlimited,true);
		$criteria->compare('restrict_to_products',$this->restrict_to_products,true);
		$criteria->compare('restrict_to_categories',$this->restrict_to_categories,true);
		$criteria->compare('restrict_to_customers',$this->restrict_to_customers,true);
		$criteria->compare('restrict_to_customers_groups',$this->restrict_to_customers_groups,true);
		$criteria->compare('coupon_active',$this->coupon_active,true);
		$criteria->compare('date_created',$this->date_created,true);
		$criteria->compare('date_modified',$this->date_modified,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}