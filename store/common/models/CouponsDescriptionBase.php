<?php

/**
 * This is the model class for table "coupons_description".
 *
 * The followings are the available columns in table 'coupons_description':
 * @property integer $coupon_id
 * @property integer $language_id
 * @property string $coupon_name
 * @property string $coupon_description
 */
class CouponsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupons_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_id, language_id', 'numerical', 'integerOnly'=>true),
			array('coupon_name', 'length', 'max'=>32),
			array('coupon_description', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coupon_id, language_id, coupon_name, coupon_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coupon_id' => 'Coupon',
			'language_id' => 'Language',
			'coupon_name' => 'Coupon Name',
			'coupon_description' => 'Coupon Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coupon_id',$this->coupon_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('coupon_name',$this->coupon_name,true);
		$criteria->compare('coupon_description',$this->coupon_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}