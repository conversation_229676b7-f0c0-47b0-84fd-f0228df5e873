<?php

/**
 * This is the model class for table "coupons_generation".
 *
 * The followings are the available columns in table 'coupons_generation':
 * @property integer $coupon_generation_id
 * @property string $coupon_type
 * @property string $coupon_code_prefix
 * @property string $coupon_code_suffix
 * @property string $coupon_amount
 * @property string $coupon_minimum_order
 * @property string $coupon_start_date
 * @property string $coupon_expire_date
 * @property integer $coupon_number
 * @property integer $uses_per_coupon
 * @property string $uses_per_coupon_unlimited
 * @property integer $uses_per_user
 * @property string $uses_per_user_unlimited
 * @property string $restrict_to_products
 * @property string $restrict_to_categories
 * @property string $restrict_to_customers
 * @property string $restrict_to_customers_groups
 * @property string $coupon_generation_status
 * @property integer $requester_id
 * @property string $date_created
 * @property string $date_modified
 */
class CouponsGenerationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponsGenerationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupons_generation';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_number, uses_per_coupon, uses_per_user, requester_id', 'numerical', 'integerOnly'=>true),
			array('coupon_type, uses_per_coupon_unlimited, uses_per_user_unlimited, coupon_generation_status', 'length', 'max'=>1),
			array('coupon_code_prefix, coupon_code_suffix', 'length', 'max'=>5),
			array('coupon_amount, coupon_minimum_order', 'length', 'max'=>8),
			array('restrict_to_customers_groups', 'length', 'max'=>64),
			array('coupon_start_date, coupon_expire_date, restrict_to_products, restrict_to_categories, restrict_to_customers, date_created, date_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coupon_generation_id, coupon_type, coupon_code_prefix, coupon_code_suffix, coupon_amount, coupon_minimum_order, coupon_start_date, coupon_expire_date, coupon_number, uses_per_coupon, uses_per_coupon_unlimited, uses_per_user, uses_per_user_unlimited, restrict_to_products, restrict_to_categories, restrict_to_customers, restrict_to_customers_groups, coupon_generation_status, requester_id, date_created, date_modified', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coupon_generation_id' => 'Coupon Generation',
			'coupon_type' => 'Coupon Type',
			'coupon_code_prefix' => 'Coupon Code Prefix',
			'coupon_code_suffix' => 'Coupon Code Suffix',
			'coupon_amount' => 'Coupon Amount',
			'coupon_minimum_order' => 'Coupon Minimum Order',
			'coupon_start_date' => 'Coupon Start Date',
			'coupon_expire_date' => 'Coupon Expire Date',
			'coupon_number' => 'Coupon Number',
			'uses_per_coupon' => 'Uses Per Coupon',
			'uses_per_coupon_unlimited' => 'Uses Per Coupon Unlimited',
			'uses_per_user' => 'Uses Per User',
			'uses_per_user_unlimited' => 'Uses Per User Unlimited',
			'restrict_to_products' => 'Restrict To Products',
			'restrict_to_categories' => 'Restrict To Categories',
			'restrict_to_customers' => 'Restrict To Customers',
			'restrict_to_customers_groups' => 'Restrict To Customers Groups',
			'coupon_generation_status' => 'Coupon Generation Status',
			'requester_id' => 'Requester',
			'date_created' => 'Date Created',
			'date_modified' => 'Date Modified',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coupon_generation_id',$this->coupon_generation_id);
		$criteria->compare('coupon_type',$this->coupon_type,true);
		$criteria->compare('coupon_code_prefix',$this->coupon_code_prefix,true);
		$criteria->compare('coupon_code_suffix',$this->coupon_code_suffix,true);
		$criteria->compare('coupon_amount',$this->coupon_amount,true);
		$criteria->compare('coupon_minimum_order',$this->coupon_minimum_order,true);
		$criteria->compare('coupon_start_date',$this->coupon_start_date,true);
		$criteria->compare('coupon_expire_date',$this->coupon_expire_date,true);
		$criteria->compare('coupon_number',$this->coupon_number);
		$criteria->compare('uses_per_coupon',$this->uses_per_coupon);
		$criteria->compare('uses_per_coupon_unlimited',$this->uses_per_coupon_unlimited,true);
		$criteria->compare('uses_per_user',$this->uses_per_user);
		$criteria->compare('uses_per_user_unlimited',$this->uses_per_user_unlimited,true);
		$criteria->compare('restrict_to_products',$this->restrict_to_products,true);
		$criteria->compare('restrict_to_categories',$this->restrict_to_categories,true);
		$criteria->compare('restrict_to_customers',$this->restrict_to_customers,true);
		$criteria->compare('restrict_to_customers_groups',$this->restrict_to_customers_groups,true);
		$criteria->compare('coupon_generation_status',$this->coupon_generation_status,true);
		$criteria->compare('requester_id',$this->requester_id);
		$criteria->compare('date_created',$this->date_created,true);
		$criteria->compare('date_modified',$this->date_modified,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}