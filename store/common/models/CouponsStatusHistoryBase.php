<?php

/**
 * This is the model class for table "coupons_status_history".
 *
 * The followings are the available columns in table 'coupons_status_history':
 * @property integer $coupons_status_history_id
 * @property integer $coupon_id
 * @property string $coupon_active
 * @property string $date_added
 * @property string $comments
 * @property string $changed_by
 */
class CouponsStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponsStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'coupons_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('coupon_id', 'numerical', 'integerOnly'=>true),
			array('coupon_active', 'length', 'max'=>1),
			array('changed_by', 'length', 'max'=>128),
			array('date_added, comments', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('coupons_status_history_id, coupon_id, coupon_active, date_added, comments, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'coupons_status_history_id' => 'Coupons Status History',
			'coupon_id' => 'Coupon',
			'coupon_active' => 'Coupon Active',
			'date_added' => 'Date Added',
			'comments' => 'Comments',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('coupons_status_history_id',$this->coupons_status_history_id);
		$criteria->compare('coupon_id',$this->coupon_id);
		$criteria->compare('coupon_active',$this->coupon_active,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('comments',$this->comments,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}