<?php

/**
 * This is the model class for table "cron_aws_cf".
 *
 * The followings are the available columns in table 'cron_aws_cf':
 * @property string $cf_id
 * @property string $bucket_key
 * @property string $filepath
 * @property string $filename
 * @property string $created_datetime
 * @property string $status
 */
class CronAwsCfBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CronAwsCfBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cron_aws_cf';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('bucket_key, filepath, filename, created_datetime, status', 'required'),
			array('bucket_key', 'length', 'max'=>45),
			array('filepath, filename', 'length', 'max'=>125),
			array('status', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cf_id, bucket_key, filepath, filename, created_datetime, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cf_id' => 'Cf',
			'bucket_key' => 'Bucket Key',
			'filepath' => 'Filepath',
			'filename' => 'Filename',
			'created_datetime' => 'Created Datetime',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cf_id',$this->cf_id,true);
		$criteria->compare('bucket_key',$this->bucket_key,true);
		$criteria->compare('filepath',$this->filepath,true);
		$criteria->compare('filename',$this->filename,true);
		$criteria->compare('created_datetime',$this->created_datetime,true);
		$criteria->compare('status',$this->status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}