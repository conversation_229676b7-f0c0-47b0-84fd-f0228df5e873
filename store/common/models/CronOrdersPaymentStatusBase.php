<?php

/**
 * This is the model class for table "cron_orders_payment_status".
 *
 * The followings are the available columns in table 'cron_orders_payment_status':
 * @property integer $orders_id
 * @property integer $counter
 * @property string $check_date
 */
class CronOrdersPaymentStatusBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CronOrdersPaymentStatusBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cron_orders_payment_status';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, counter', 'numerical', 'integerOnly'=>true),
			array('check_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, counter, check_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'counter' => 'Counter',
			'check_date' => 'Check Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('counter',$this->counter);
		$criteria->compare('check_date',$this->check_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}