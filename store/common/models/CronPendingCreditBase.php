<?php

/**
 * This is the model class for table "cron_pending_credit".
 *
 * The followings are the available columns in table 'cron_pending_credit':
 * @property string $cron_pending_credit_trans_type
 * @property string $cron_pending_credit_trans_id
 * @property string $cron_pending_credit_trans_created_date
 * @property string $cron_pending_credit_trans_completed_date
 * @property string $cron_pending_credit_mature_period
 * @property integer $cron_pending_credit_trans_status
 * @property integer $cron_pending_credit_trans_error
 * @property integer $cron_pending_credit_trans_executable_error
 */
class CronPendingCreditBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CronPendingCreditBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cron_pending_credit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cron_pending_credit_trans_status, cron_pending_credit_trans_error, cron_pending_credit_trans_executable_error', 'numerical', 'integerOnly'=>true),
			array('cron_pending_credit_trans_type', 'length', 'max'=>10),
			array('cron_pending_credit_trans_id', 'length', 'max'=>32),
			array('cron_pending_credit_mature_period', 'length', 'max'=>11),
			array('cron_pending_credit_trans_created_date, cron_pending_credit_trans_completed_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cron_pending_credit_trans_type, cron_pending_credit_trans_id, cron_pending_credit_trans_created_date, cron_pending_credit_trans_completed_date, cron_pending_credit_mature_period, cron_pending_credit_trans_status, cron_pending_credit_trans_error, cron_pending_credit_trans_executable_error', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cron_pending_credit_trans_type' => 'Cron Pending Credit Trans Type',
			'cron_pending_credit_trans_id' => 'Cron Pending Credit Trans',
			'cron_pending_credit_trans_created_date' => 'Cron Pending Credit Trans Created Date',
			'cron_pending_credit_trans_completed_date' => 'Cron Pending Credit Trans Completed Date',
			'cron_pending_credit_mature_period' => 'Cron Pending Credit Mature Period',
			'cron_pending_credit_trans_status' => 'Cron Pending Credit Trans Status',
			'cron_pending_credit_trans_error' => 'Cron Pending Credit Trans Error',
			'cron_pending_credit_trans_executable_error' => 'Cron Pending Credit Trans Executable Error',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cron_pending_credit_trans_type',$this->cron_pending_credit_trans_type,true);
		$criteria->compare('cron_pending_credit_trans_id',$this->cron_pending_credit_trans_id,true);
		$criteria->compare('cron_pending_credit_trans_created_date',$this->cron_pending_credit_trans_created_date,true);
		$criteria->compare('cron_pending_credit_trans_completed_date',$this->cron_pending_credit_trans_completed_date,true);
		$criteria->compare('cron_pending_credit_mature_period',$this->cron_pending_credit_mature_period,true);
		$criteria->compare('cron_pending_credit_trans_status',$this->cron_pending_credit_trans_status);
		$criteria->compare('cron_pending_credit_trans_error',$this->cron_pending_credit_trans_error);
		$criteria->compare('cron_pending_credit_trans_executable_error',$this->cron_pending_credit_trans_executable_error);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}