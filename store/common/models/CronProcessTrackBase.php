<?php

/**
 * This is the model class for table "cron_process_track".
 *
 * The followings are the available columns in table 'cron_process_track':
 * @property integer $cron_process_track_in_action
 * @property string $cron_process_track_start_date
 * @property integer $cron_process_track_failed_attempt
 * @property string $cron_process_track_filename
 */
class CronProcessTrackBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CronProcessTrackBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cron_process_track';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cron_process_track_in_action, cron_process_track_failed_attempt', 'numerical', 'integerOnly'=>true),
			array('cron_process_track_filename', 'length', 'max'=>255),
			array('cron_process_track_start_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cron_process_track_in_action, cron_process_track_start_date, cron_process_track_failed_attempt, cron_process_track_filename', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cron_process_track_in_action' => 'Cron Process Track In Action',
			'cron_process_track_start_date' => 'Cron Process Track Start Date',
			'cron_process_track_failed_attempt' => 'Cron Process Track Failed Attempt',
			'cron_process_track_filename' => 'Cron Process Track Filename',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cron_process_track_in_action',$this->cron_process_track_in_action);
		$criteria->compare('cron_process_track_start_date',$this->cron_process_track_start_date,true);
		$criteria->compare('cron_process_track_failed_attempt',$this->cron_process_track_failed_attempt);
		$criteria->compare('cron_process_track_filename',$this->cron_process_track_filename,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}