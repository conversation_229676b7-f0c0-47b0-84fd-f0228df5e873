<?php

/**
 * This is the model class for table "cron_undelivered_report".
 *
 * The followings are the available columns in table 'cron_undelivered_report':
 * @property integer $payment_methods_id
 * @property string $currency
 * @property string $cur
 * @property string $pwl
 * @property string $cdk
 * @property string $sc
 * @property string $hla
 * @property string $subtotal
 */
class CronUndeliveredReportBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CronUndeliveredReportBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'cron_undelivered_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id', 'numerical', 'integerOnly'=>true),
			array('currency', 'length', 'max'=>3),
			array('cur, pwl, cdk, sc, hla, subtotal', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_id, currency, cur, pwl, cdk, sc, hla, subtotal', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_id' => 'Payment Methods',
			'currency' => 'Currency',
			'cur' => 'Cur',
			'pwl' => 'Pwl',
			'cdk' => 'Cdk',
			'sc' => 'Sc',
			'hla' => 'Hla',
			'subtotal' => 'Subtotal',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('currency',$this->currency,true);
		$criteria->compare('cur',$this->cur,true);
		$criteria->compare('pwl',$this->pwl,true);
		$criteria->compare('cdk',$this->cdk,true);
		$criteria->compare('sc',$this->sc,true);
		$criteria->compare('hla',$this->hla,true);
		$criteria->compare('subtotal',$this->subtotal,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}