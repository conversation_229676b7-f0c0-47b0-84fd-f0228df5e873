<?php

/**
 * This is the model class for table "currencies".
 *
 * The followings are the available columns in table 'currencies':
 * @property integer $currencies_id
 * @property string $title
 * @property string $code
 * @property string $symbol_left
 * @property string $symbol_right
 * @property string $decimal_point
 * @property string $thousands_point
 * @property string $decimal_places
 * @property string $value
 * @property string $buy_value
 * @property string $buy_value_adjust
 * @property string $sell_value
 * @property string $sell_value_adjust
 * @property integer $currencies_live_update
 * @property string $currencies_used_for
 * @property string $last_updated
 */
class CurrenciesBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CurrenciesBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()
    {
        return Yii::app()->db_rr;
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'currencies';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('currencies_live_update', 'numerical', 'integerOnly' => true),
            array('title, currencies_used_for', 'length', 'max' => 32),
            array('code', 'length', 'max' => 3),
            array('symbol_left, symbol_right', 'length', 'max' => 12),
            array('decimal_point, thousands_point, decimal_places', 'length', 'max' => 1),
            array('value, buy_value, sell_value', 'length', 'max' => 13),
            array('buy_value_adjust, sell_value_adjust', 'length', 'max' => 7),
            array('last_updated', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'currencies_id, title, code, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, value, buy_value, buy_value_adjust, sell_value, sell_value_adjust, currencies_live_update, currencies_used_for, last_updated',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'currencies_id' => 'Currencies',
            'title' => 'Title',
            'code' => 'Code',
            'symbol_left' => 'Symbol Left',
            'symbol_right' => 'Symbol Right',
            'decimal_point' => 'Decimal Point',
            'thousands_point' => 'Thousands Point',
            'decimal_places' => 'Decimal Places',
            'value' => 'Value',
            'buy_value' => 'Buy Value',
            'buy_value_adjust' => 'Buy Value Adjust',
            'sell_value' => 'Sell Value',
            'sell_value_adjust' => 'Sell Value Adjust',
            'currencies_live_update' => 'Currencies Live Update',
            'currencies_used_for' => 'Currencies Used For',
            'last_updated' => 'Last Updated',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('currencies_id', $this->currencies_id);
        $criteria->compare('title', $this->title, true);
        $criteria->compare('code', $this->code, true);
        $criteria->compare('symbol_left', $this->symbol_left, true);
        $criteria->compare('symbol_right', $this->symbol_right, true);
        $criteria->compare('decimal_point', $this->decimal_point, true);
        $criteria->compare('thousands_point', $this->thousands_point, true);
        $criteria->compare('decimal_places', $this->decimal_places, true);
        $criteria->compare('value', $this->value, true);
        $criteria->compare('buy_value', $this->buy_value, true);
        $criteria->compare('buy_value_adjust', $this->buy_value_adjust, true);
        $criteria->compare('sell_value', $this->sell_value, true);
        $criteria->compare('sell_value_adjust', $this->sell_value_adjust, true);
        $criteria->compare('currencies_live_update', $this->currencies_live_update);
        $criteria->compare('currencies_used_for', $this->currencies_used_for, true);
        $criteria->compare('last_updated', $this->last_updated, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getAllByUsedFor($currencies_used_for = 'SELL')
    {
        $return_array = array();
        $internal_currencies = array();
        $deprecated_currencies = array();

        $criteria = new CDbCriteria();
        $criteria->select = array(
            'currencies_id',
            'code',
            'title',
            'symbol_left',
            'symbol_right',
            'decimal_point',
            'thousands_point',
            'decimal_places',
            'value',
            'buy_value',
            'sell_value',
            'currencies_used_for'
        );
        $criteria->order = 'title, code';

        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $data = array(
                    'currencies_id' => $result->currencies_id,
                    'title' => $result->title,
                    'symbol_left' => $result->symbol_left,
                    'symbol_right' => $result->symbol_right,
                    'decimal_point' => $result->decimal_point,
                    'thousands_point' => $result->thousands_point,
                    'decimal_places' => $result->decimal_places,
                    'value' => $result->value,
                    'buy_value' => $result->buy_value,
                    'sell_value' => $result->sell_value,
                );
                if (in_array($currencies_used_for, explode(',', $result->currencies_used_for))) {
                    $return_array[$result->code] = $data;
                }
                else{
                    $deprecated_currencies[$result->code] = $data;
                }

                $internal_currencies[$result->currencies_id] = $result->code;
            }
        }

        return array($return_array, $deprecated_currencies, $internal_currencies);
    }

    public function getAllCurrencies()
    {
        $return_array = array();
        $results = $this->model()->findAll();
        foreach ($results as $result) {
            $return_array[$result->code] = array(
                'currencies_id' => $result->currencies_id,
                'title' => $result->title,
                'symbol_left' => $result->symbol_left,
                'symbol_right' => $result->symbol_right,
                'decimal_point' => $result->decimal_point,
                'thousands_point' => $result->thousands_point,
                'decimal_places' => $result->decimal_places,
                'value' => $result->value
            );
        }
        return $return_array;
    }

}