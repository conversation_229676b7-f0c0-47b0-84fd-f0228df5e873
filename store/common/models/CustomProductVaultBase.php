<?php

/**
 * This is the model class for table "custom_product_vault".
 *
 * The followings are the available columns in table 'custom_product_vault':
 * @property integer $products_vault_id
 * @property integer $products_id
 * @property string $file_name
 * @property integer $file_size
 * @property string $file_type
 * @property integer $zip_qty
 * @property string $zip_unit_price
 * @property string $zip_date_added
 * @property string $zip_date_modified
 * @property string $zip_date_unzipping
 * @property string $zip_uploaded_by
 * @property string $zip_unzip_by
 * @property integer $zip_status
 * @property string $zip_description
 * @property string $remarks
 * @property string $purchase_orders_id
 */
class CustomProductVaultBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomProductVaultBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'custom_product_vault';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, file_size, zip_qty, zip_status', 'numerical', 'integerOnly'=>true),
			array('file_name', 'length', 'max'=>50),
			array('file_type', 'length', 'max'=>5),
			array('zip_unit_price', 'length', 'max'=>10),
			array('zip_uploaded_by, zip_unzip_by', 'length', 'max'=>65),
			array('zip_description', 'length', 'max'=>255),
			array('purchase_orders_id', 'length', 'max'=>11),
			array('zip_date_added, zip_date_modified, zip_date_unzipping, remarks', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_vault_id, products_id, file_name, file_size, file_type, zip_qty, zip_unit_price, zip_date_added, zip_date_modified, zip_date_unzipping, zip_uploaded_by, zip_unzip_by, zip_status, zip_description, remarks, purchase_orders_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_vault_id' => 'Products Vault',
			'products_id' => 'Products',
			'file_name' => 'File Name',
			'file_size' => 'File Size',
			'file_type' => 'File Type',
			'zip_qty' => 'Zip Qty',
			'zip_unit_price' => 'Zip Unit Price',
			'zip_date_added' => 'Zip Date Added',
			'zip_date_modified' => 'Zip Date Modified',
			'zip_date_unzipping' => 'Zip Date Unzipping',
			'zip_uploaded_by' => 'Zip Uploaded By',
			'zip_unzip_by' => 'Zip Unzip By',
			'zip_status' => 'Zip Status',
			'zip_description' => 'Zip Description',
			'remarks' => 'Remarks',
			'purchase_orders_id' => 'Purchase Orders',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_vault_id',$this->products_vault_id);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('file_name',$this->file_name,true);
		$criteria->compare('file_size',$this->file_size);
		$criteria->compare('file_type',$this->file_type,true);
		$criteria->compare('zip_qty',$this->zip_qty);
		$criteria->compare('zip_unit_price',$this->zip_unit_price,true);
		$criteria->compare('zip_date_added',$this->zip_date_added,true);
		$criteria->compare('zip_date_modified',$this->zip_date_modified,true);
		$criteria->compare('zip_date_unzipping',$this->zip_date_unzipping,true);
		$criteria->compare('zip_uploaded_by',$this->zip_uploaded_by,true);
		$criteria->compare('zip_unzip_by',$this->zip_unzip_by,true);
		$criteria->compare('zip_status',$this->zip_status);
		$criteria->compare('zip_description',$this->zip_description,true);
		$criteria->compare('remarks',$this->remarks,true);
		$criteria->compare('purchase_orders_id',$this->purchase_orders_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}