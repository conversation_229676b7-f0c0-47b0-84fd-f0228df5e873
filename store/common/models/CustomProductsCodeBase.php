<?php

/**
 * This is the model class for table "custom_products_code".
 *
 * The followings are the available columns in table 'custom_products_code':
 * @property integer $custom_products_code_id
 * @property integer $products_id
 * @property integer $orders_products_id
 * @property integer $status_id
 * @property string $file_name
 * @property string $file_type
 * @property string $code_date_added
 * @property string $code_date_modified
 * @property string $code_uploaded_by
 * @property string $remarks
 * @property integer $custom_products_code_viewed
 * @property string $purchase_orders_id
 * @property integer $to_s3
 */
class CustomProductsCodeBase extends MainModel
{

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CustomProductsCodeBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'custom_products_code';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('remarks', 'required'), array(
                'products_id, orders_products_id, status_id, custom_products_code_viewed, to_s3', 'numerical',
                'integerOnly' => true
            ), array('file_name', 'length', 'max' => 50), array('file_type', 'length', 'max' => 5),
            array('code_uploaded_by', 'length', 'max' => 65), array('purchase_orders_id', 'length', 'max' => 11),
            array('code_date_added, code_date_modified', 'safe'), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'custom_products_code_id, products_id, orders_products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, custom_products_code_viewed, purchase_orders_id, to_s3',
                'safe', 'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'custom_products_code_id' => 'Custom Products Code', 'products_id' => 'Products',
            'orders_products_id' => 'Orders Products', 'status_id' => 'Status', 'file_name' => 'File Name',
            'file_type' => 'File Type', 'code_date_added' => 'Code Date Added',
            'code_date_modified' => 'Code Date Modified', 'code_uploaded_by' => 'Code Uploaded By',
            'remarks' => 'Remarks', 'custom_products_code_viewed' => 'Custom Products Code Viewed',
            'purchase_orders_id' => 'Purchase Orders', 'to_s3' => 'To S3',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('custom_products_code_id', $this->custom_products_code_id);
        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('orders_products_id', $this->orders_products_id);
        $criteria->compare('status_id', $this->status_id);
        $criteria->compare('file_name', $this->file_name, true);
        $criteria->compare('file_type', $this->file_type, true);
        $criteria->compare('code_date_added', $this->code_date_added, true);
        $criteria->compare('code_date_modified', $this->code_date_modified, true);
        $criteria->compare('code_uploaded_by', $this->code_uploaded_by, true);
        $criteria->compare('remarks', $this->remarks, true);
        $criteria->compare('custom_products_code_viewed', $this->custom_products_code_viewed);
        $criteria->compare('purchase_orders_id', $this->purchase_orders_id, true);
        $criteria->compare('to_s3', $this->to_s3);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getProductCodeInfo($assignedCdkeyArr)
    {
        $idlist = array();
        foreach ($assignedCdkeyArr as $k => $v) {
            $idlist[] = (int)trim($v);
        }
        $criteria = new CDbCriteria();
        $criteria->select = 'custom_products_code_id, file_name, file_type, orders_products_id, custom_products_code_viewed';
        $criteria->addCondition('status_id = 0');
        $criteria->addInCondition('custom_products_code_id',$idlist);
        $criteria->order = 'custom_products_code_id asc';
        $result = $this->model()->findAll($criteria);
        return $result;
    }

    public function getCustomProductCodeByOrdersProductsId($orders_products_id)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'custom_products_code_id, file_name, file_type, orders_products_id, custom_products_code_viewed';
        $criteria->condition = 'orders_products_id = :op_id AND status_id = 0';
        $criteria->order = 'custom_products_code_id asc';
        $criteria->params = array(':op_id' => $orders_products_id);
        $result = $this->model()->findAll($criteria);
        return $result;
    }

}