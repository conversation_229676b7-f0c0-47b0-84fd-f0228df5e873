<?php

/**
 * This is the model class for table "custom_products_code_log".
 *
 * The followings are the available columns in table 'custom_products_code_log':
 * @property integer $custom_products_code_log_id
 * @property string $custom_products_code_log_user
 * @property string $custom_products_code_log_user_role
 * @property string $log_ip
 * @property string $log_time
 * @property integer $custom_products_code_id
 * @property string $log_system_messages
 * @property string $log_user_messages
 */
class CustomProductsCodeLogBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CustomProductsCodeLogBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'custom_products_code_log';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('log_system_messages', 'required'),
            array('custom_products_code_id', 'numerical', 'integerOnly' => true),
            array('custom_products_code_log_user', 'length', 'max' => 255),
            array('custom_products_code_log_user_role', 'length', 'max' => 16),
            array('log_ip', 'length', 'max' => 128),
            array('log_time', 'safe'), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'custom_products_code_log_id, custom_products_code_log_user, custom_products_code_log_user_role, log_ip, log_time, custom_products_code_id, log_system_messages, log_user_messages',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'custom_products_code_log_id' => 'Custom Products Code Log',
            'custom_products_code_log_user' => 'Custom Products Code Log User',
            'custom_products_code_log_user_role' => 'Custom Products Code Log User Role',
            'log_ip' => 'Log Ip',
            'log_time' => 'Log Time',
            'custom_products_code_id' => 'Custom Products Code',
            'log_system_messages' => 'Log System Messages',
            'log_user_messages' => 'Log User Messages',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('custom_products_code_log_id', $this->custom_products_code_log_id);
        $criteria->compare('custom_products_code_log_user', $this->custom_products_code_log_user, true);
        $criteria->compare('custom_products_code_log_user_role', $this->custom_products_code_log_user_role, true);
        $criteria->compare('log_ip', $this->log_ip, true);
        $criteria->compare('log_time', $this->log_time, true);
        $criteria->compare('custom_products_code_id', $this->custom_products_code_id);
        $criteria->compare('log_system_messages', $this->log_system_messages, true);
        $criteria->compare('log_user_messages', $this->log_user_messages, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }
}