<?php

/**
 * This is the model class for table "custom_products_type".
 *
 * The followings are the available columns in table 'custom_products_type':
 * @property integer $custom_products_type_id
 * @property string $custom_products_type_name
 * @property integer $data_pool_id
 * @property string $custom_products_low_stock_email
 * @property string $custom_products_add_stock_email
 * @property string $custom_products_deduct_stock_email
 * @property string $custom_product_price_email
 * @property string $custom_products_upload_email
 * @property string $custom_products_change_status_email
 * @property string $custom_products_admin_group_id
 */
class CustomProductsTypeBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomProductsTypeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'custom_products_type';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('custom_products_admin_group_id', 'required'),
			array('data_pool_id', 'numerical', 'integerOnly'=>true),
			array('custom_products_type_name', 'length', 'max'=>100),
			array('custom_products_low_stock_email, custom_products_add_stock_email, custom_products_deduct_stock_email, custom_product_price_email, custom_products_upload_email', 'length', 'max'=>64),
			array('custom_products_change_status_email', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('custom_products_type_id, custom_products_type_name, data_pool_id, custom_products_low_stock_email, custom_products_add_stock_email, custom_products_deduct_stock_email, custom_product_price_email, custom_products_upload_email, custom_products_change_status_email, custom_products_admin_group_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'custom_products_type_id' => 'Custom Products Type',
			'custom_products_type_name' => 'Custom Products Type Name',
			'data_pool_id' => 'Data Pool',
			'custom_products_low_stock_email' => 'Custom Products Low Stock Email',
			'custom_products_add_stock_email' => 'Custom Products Add Stock Email',
			'custom_products_deduct_stock_email' => 'Custom Products Deduct Stock Email',
			'custom_product_price_email' => 'Custom Product Price Email',
			'custom_products_upload_email' => 'Custom Products Upload Email',
			'custom_products_change_status_email' => 'Custom Products Change Status Email',
			'custom_products_admin_group_id' => 'Custom Products Admin Group',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('custom_products_type_name',$this->custom_products_type_name,true);
		$criteria->compare('data_pool_id',$this->data_pool_id);
		$criteria->compare('custom_products_low_stock_email',$this->custom_products_low_stock_email,true);
		$criteria->compare('custom_products_add_stock_email',$this->custom_products_add_stock_email,true);
		$criteria->compare('custom_products_deduct_stock_email',$this->custom_products_deduct_stock_email,true);
		$criteria->compare('custom_product_price_email',$this->custom_product_price_email,true);
		$criteria->compare('custom_products_upload_email',$this->custom_products_upload_email,true);
		$criteria->compare('custom_products_change_status_email',$this->custom_products_change_status_email,true);
		$criteria->compare('custom_products_admin_group_id',$this->custom_products_admin_group_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}