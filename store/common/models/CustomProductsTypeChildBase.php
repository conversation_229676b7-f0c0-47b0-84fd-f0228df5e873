<?php

/**
 * This is the model class for table "custom_products_type_child".
 *
 * The followings are the available columns in table 'custom_products_type_child':
 * @property integer $custom_products_type_child_id
 * @property integer $custom_products_type_id
 * @property string $custom_products_type_child_url
 * @property string $custom_products_type_child_name
 * @property integer $display_status
 * @property integer $sort_order
 */
class CustomProductsTypeChildBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomProductsTypeChildBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'custom_products_type_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('custom_products_type_id, display_status, sort_order', 'numerical', 'integerOnly'=>true),
			array('custom_products_type_child_url, custom_products_type_child_name', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('custom_products_type_child_id, custom_products_type_id, custom_products_type_child_url, custom_products_type_child_name, display_status, sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'custom_products_type_child_id' => 'Custom Products Type Child',
			'custom_products_type_id' => 'Custom Products Type',
			'custom_products_type_child_url' => 'Custom Products Type Child Url',
			'custom_products_type_child_name' => 'Custom Products Type Child Name',
			'display_status' => 'Display Status',
			'sort_order' => 'Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('custom_products_type_child_id',$this->custom_products_type_child_id);
		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('custom_products_type_child_url',$this->custom_products_type_child_url,true);
		$criteria->compare('custom_products_type_child_name',$this->custom_products_type_child_name,true);
		$criteria->compare('display_status',$this->display_status);
		$criteria->compare('sort_order',$this->sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
	public function getAllActiveCPTC() { // default CDK
		$return_array = array();
		$return_array2 = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'custom_products_type_id, custom_products_type_child_id, display_status';
        $criteria->order = 'sort_order';
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                if ($result->display_status == 1) {
                    $return_array[] = $result->custom_products_type_id;
                    $return_array2[] = $result->custom_products_type_child_id;
                }
            }
        }
        
		return array($return_array, $return_array2);
	}
    
}