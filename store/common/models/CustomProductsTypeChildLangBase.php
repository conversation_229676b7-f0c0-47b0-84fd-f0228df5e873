<?php

/**
 * This is the model class for table "custom_products_type_child_lang".
 *
 * The followings are the available columns in table 'custom_products_type_child_lang':
 * @property integer $custom_products_type_child_id
 * @property integer $languages_id
 * @property string $custom_products_type_child_name
 */
class CustomProductsTypeChildLangBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomProductsTypeChildLangBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'custom_products_type_child_lang';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('custom_products_type_child_id, languages_id', 'numerical', 'integerOnly'=>true),
			array('custom_products_type_child_name', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('custom_products_type_child_id, languages_id, custom_products_type_child_name', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'custom_products_type_child_id' => 'Custom Products Type Child',
			'languages_id' => 'Languages',
			'custom_products_type_child_name' => 'Custom Products Type Child Name',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('custom_products_type_child_id',$this->custom_products_type_child_id);
		$criteria->compare('languages_id',$this->languages_id);
		$criteria->compare('custom_products_type_child_name',$this->custom_products_type_child_name,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}