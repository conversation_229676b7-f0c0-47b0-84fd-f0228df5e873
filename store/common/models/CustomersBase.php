<?php

/**
 * This is the model class for table "customers".
 *
 * The followings are the available columns in table 'customers':
 * @property integer $customers_id
 * @property string $customers_gender
 * @property string $customers_firstname
 * @property string $customers_lastname
 * @property string $customers_dob
 * @property integer $account_activated
 * @property string $customers_email_address
 * @property integer $email_verified
 * @property string $serial_number
 * @property integer $customers_default_address_id
 * @property integer $customers_country_dialing_code_id
 * @property string $customers_telephone
 * @property string $customers_mobile
 * @property string $customers_fax
 * @property string $customers_msn
 * @property string $customers_qq
 * @property string $customers_yahoo
 * @property string $customers_icq
 * @property string $customers_password
 * @property string $customers_pin_number
 * @property string $customers_newsletter
 * @property string $customers_group_name
 * @property integer $customers_group_id
 * @property string $customers_discount
 * @property integer $customers_groups_id
 * @property string $customers_aft_groups_id
 * @property integer $customers_status
 * @property string $customers_flag
 * @property integer $customers_phone_verified
 * @property string $customers_phone_verified_by
 * @property string $customers_phone_verified_datetime
 * @property integer $affiliate_ref_id
 * @property integer $ref_id
 * @property string $customers_merged_profile
 * @property string $customers_login_sites
 * @property string $customers_reserve_amount
 * @property string $customers_security_start_time
 * @property integer $customers_disable_withdrawal
 */
class CustomersBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('account_activated, email_verified, customers_default_address_id, customers_country_dialing_code_id, customers_group_id, customers_groups_id, customers_status, customers_phone_verified, affiliate_ref_id, ref_id, customers_disable_withdrawal', 'numerical', 'integerOnly'=>true),
			array('customers_gender', 'length', 'max'=>1),
			array('customers_firstname, customers_lastname, customers_telephone, customers_mobile, customers_fax, customers_login_sites', 'length', 'max'=>32),
			array('customers_email_address, customers_msn, customers_yahoo', 'length', 'max'=>96),
			array('serial_number', 'length', 'max'=>12),
			array('customers_qq, customers_icq', 'length', 'max'=>20),
			array('customers_password, customers_pin_number', 'length', 'max'=>40),
			array('customers_newsletter, customers_phone_verified_by, customers_merged_profile', 'length', 'max'=>255),
			array('customers_group_name', 'length', 'max'=>27),
			array('customers_discount', 'length', 'max'=>8),
			array('customers_aft_groups_id', 'length', 'max'=>11),
			array('customers_flag', 'length', 'max'=>10),
			array('customers_reserve_amount', 'length', 'max'=>15),
			array('customers_dob, customers_phone_verified_datetime, customers_security_start_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_gender, customers_firstname, customers_lastname, customers_dob, account_activated, customers_email_address, email_verified, serial_number, customers_default_address_id, customers_country_dialing_code_id, customers_telephone, customers_mobile, customers_fax, customers_msn, customers_qq, customers_yahoo, customers_icq, customers_password, customers_pin_number, customers_newsletter, customers_group_name, customers_group_id, customers_discount, customers_groups_id, customers_aft_groups_id, customers_status, customers_flag, customers_phone_verified, customers_phone_verified_by, customers_phone_verified_datetime, affiliate_ref_id, ref_id, customers_merged_profile, customers_login_sites, customers_reserve_amount, customers_security_start_time, customers_disable_withdrawal', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_gender' => 'Customers Gender',
			'customers_firstname' => 'Customers Firstname',
			'customers_lastname' => 'Customers Lastname',
			'customers_dob' => 'Customers Dob',
			'account_activated' => 'Account Activated',
			'customers_email_address' => 'Customers Email Address',
			'email_verified' => 'Email Verified',
			'serial_number' => 'Serial Number',
			'customers_default_address_id' => 'Customers Default Address',
			'customers_country_dialing_code_id' => 'Customers Country Dialing Code',
			'customers_telephone' => 'Customers Telephone',
			'customers_mobile' => 'Customers Mobile',
			'customers_fax' => 'Customers Fax',
			'customers_msn' => 'Customers Msn',
			'customers_qq' => 'Customers Qq',
			'customers_yahoo' => 'Customers Yahoo',
			'customers_icq' => 'Customers Icq',
			'customers_password' => 'Customers Password',
			'customers_pin_number' => 'Customers Pin Number',
			'customers_newsletter' => 'Customers Newsletter',
			'customers_group_name' => 'Customers Group Name',
			'customers_group_id' => 'Customers Group',
			'customers_discount' => 'Customers Discount',
			'customers_groups_id' => 'Customers Groups',
			'customers_aft_groups_id' => 'Customers Aft Groups',
			'customers_status' => 'Customers Status',
			'customers_flag' => 'Customers Flag',
			'customers_phone_verified' => 'Customers Phone Verified',
			'customers_phone_verified_by' => 'Customers Phone Verified By',
			'customers_phone_verified_datetime' => 'Customers Phone Verified Datetime',
			'affiliate_ref_id' => 'Affiliate Ref',
			'ref_id' => 'Ref',
			'customers_merged_profile' => 'Customers Merged Profile',
			'customers_login_sites' => 'Customers Login Sites',
			'customers_reserve_amount' => 'Customers Reserve Amount',
			'customers_security_start_time' => 'Customers Security Start Time',
			'customers_disable_withdrawal' => 'Customers Disable Withdrawal',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_gender',$this->customers_gender,true);
		$criteria->compare('customers_firstname',$this->customers_firstname,true);
		$criteria->compare('customers_lastname',$this->customers_lastname,true);
		$criteria->compare('customers_dob',$this->customers_dob,true);
		$criteria->compare('account_activated',$this->account_activated);
		$criteria->compare('customers_email_address',$this->customers_email_address,true);
		$criteria->compare('email_verified',$this->email_verified);
		$criteria->compare('serial_number',$this->serial_number,true);
		$criteria->compare('customers_default_address_id',$this->customers_default_address_id);
		$criteria->compare('customers_country_dialing_code_id',$this->customers_country_dialing_code_id);
		$criteria->compare('customers_telephone',$this->customers_telephone,true);
		$criteria->compare('customers_mobile',$this->customers_mobile,true);
		$criteria->compare('customers_fax',$this->customers_fax,true);
		$criteria->compare('customers_msn',$this->customers_msn,true);
		$criteria->compare('customers_qq',$this->customers_qq,true);
		$criteria->compare('customers_yahoo',$this->customers_yahoo,true);
		$criteria->compare('customers_icq',$this->customers_icq,true);
		$criteria->compare('customers_password',$this->customers_password,true);
		$criteria->compare('customers_pin_number',$this->customers_pin_number,true);
		$criteria->compare('customers_newsletter',$this->customers_newsletter,true);
		$criteria->compare('customers_group_name',$this->customers_group_name,true);
		$criteria->compare('customers_group_id',$this->customers_group_id);
		$criteria->compare('customers_discount',$this->customers_discount,true);
		$criteria->compare('customers_groups_id',$this->customers_groups_id);
		$criteria->compare('customers_aft_groups_id',$this->customers_aft_groups_id,true);
		$criteria->compare('customers_status',$this->customers_status);
		$criteria->compare('customers_flag',$this->customers_flag,true);
		$criteria->compare('customers_phone_verified',$this->customers_phone_verified);
		$criteria->compare('customers_phone_verified_by',$this->customers_phone_verified_by,true);
		$criteria->compare('customers_phone_verified_datetime',$this->customers_phone_verified_datetime,true);
		$criteria->compare('affiliate_ref_id',$this->affiliate_ref_id);
		$criteria->compare('ref_id',$this->ref_id);
		$criteria->compare('customers_merged_profile',$this->customers_merged_profile,true);
		$criteria->compare('customers_login_sites',$this->customers_login_sites,true);
		$criteria->compare('customers_reserve_amount',$this->customers_reserve_amount,true);
		$criteria->compare('customers_security_start_time',$this->customers_security_start_time,true);
		$criteria->compare('customers_disable_withdrawal',$this->customers_disable_withdrawal);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getDiscount($customers_id) {
        $return_int = 0;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'customers_discount';
		$criteria->condition = 'customers_id = :customers_id';
		$criteria->params = array(
            ':customers_id' => $customers_id
        );
		if ($result = $this->model()->find($criteria)) {
            $return_int = $result->customers_discount;
        }
        
		return $return_int;
    }
}