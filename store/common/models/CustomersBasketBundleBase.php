<?php

/**
 * This is the model class for table "customers_basket_bundle".
 *
 * The followings are the available columns in table 'customers_basket_bundle':
 * @property integer $customers_basket_bundle_id
 * @property integer $customers_id
 * @property string $products_bundle_id
 * @property string $subproducts_id
 * @property integer $subproducts_quantity
 * @property string $subproducts_price
 * @property string $customers_basket_bundle_date_added
 * @property integer $products_categories_id
 */
class CustomersBasketBundleBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersBasketBundleBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_basket_bundle';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_bundle_id, subproducts_id', 'required'),
			array('customers_id, subproducts_quantity, products_categories_id', 'numerical', 'integerOnly'=>true),
			array('subproducts_price', 'length', 'max'=>15),
			array('customers_basket_bundle_date_added', 'length', 'max'=>8),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_basket_bundle_id, customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_basket_bundle_id' => 'Customers Basket Bundle',
			'customers_id' => 'Customers',
			'products_bundle_id' => 'Products Bundle',
			'subproducts_id' => 'Subproducts',
			'subproducts_quantity' => 'Subproducts Quantity',
			'subproducts_price' => 'Subproducts Price',
			'customers_basket_bundle_date_added' => 'Customers Basket Bundle Date Added',
			'products_categories_id' => 'Products Categories',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_basket_bundle_id',$this->customers_basket_bundle_id);
		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('products_bundle_id',$this->products_bundle_id,true);
		$criteria->compare('subproducts_id',$this->subproducts_id,true);
		$criteria->compare('subproducts_quantity',$this->subproducts_quantity);
		$criteria->compare('subproducts_price',$this->subproducts_price,true);
		$criteria->compare('customers_basket_bundle_date_added',$this->customers_basket_bundle_date_added,true);
		$criteria->compare('products_categories_id',$this->products_categories_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}