<?php

/**
 * This is the model class for table "customers_basket_custom".
 *
 * The followings are the available columns in table 'customers_basket_custom':
 * @property integer $customers_basket_id
 * @property string $customers_basket_custom_key
 * @property string $customers_basket_custom_value
 */
class CustomersBasketCustomBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersBasketCustomBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_basket_custom';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_basket_custom_value', 'required'),
			array('customers_basket_id', 'numerical', 'integerOnly'=>true),
			array('customers_basket_custom_key', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_basket_id, customers_basket_custom_key, customers_basket_custom_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_basket_id' => 'Customers Basket',
			'customers_basket_custom_key' => 'Customers Basket Custom Key',
			'customers_basket_custom_value' => 'Customers Basket Custom Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_basket_id',$this->customers_basket_id);
		$criteria->compare('customers_basket_custom_key',$this->customers_basket_custom_key,true);
		$criteria->compare('customers_basket_custom_value',$this->customers_basket_custom_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}