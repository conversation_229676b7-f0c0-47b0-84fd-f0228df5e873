<?php

/**
 * This is the model class for table "customers_flag_message".
 *
 * The followings are the available columns in table 'customers_flag_message':
 * @property string $customers_id
 * @property integer $flag_id
 * @property string $message
 * @property string $date_added
 * @property string $added_by
 */
class CustomersFlagMessageBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersFlagMessageBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_flag_message';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('message', 'required'),
			array('flag_id', 'numerical', 'integerOnly'=>true),
			array('customers_id', 'length', 'max'=>11),
			array('added_by', 'length', 'max'=>255),
			array('date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, flag_id, message, date_added, added_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'flag_id' => 'Flag',
			'message' => 'Message',
			'date_added' => 'Date Added',
			'added_by' => 'Added By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id,true);
		$criteria->compare('flag_id',$this->flag_id);
		$criteria->compare('message',$this->message,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('added_by',$this->added_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}