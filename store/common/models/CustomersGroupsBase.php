<?php

/**
 * This is the model class for table "customers_groups".
 *
 * The followings are the available columns in table 'customers_groups':
 * @property integer $customers_groups_id
 * @property string $customers_groups_name
 * @property string $customers_groups_legend_color
 * @property string $customers_groups_payment_methods
 * @property string $customers_groups_extra_sc
 * @property integer $sort_order
 */
class CustomersGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_groups_payment_methods', 'required'),
			array('sort_order', 'numerical', 'integerOnly'=>true),
			array('customers_groups_name', 'length', 'max'=>32),
			array('customers_groups_legend_color', 'length', 'max'=>7),
			array('customers_groups_extra_sc', 'length', 'max'=>8),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_groups_id, customers_groups_name, customers_groups_legend_color, customers_groups_payment_methods, customers_groups_extra_sc, sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_groups_id' => 'Customers Groups',
			'customers_groups_name' => 'Customers Groups Name',
			'customers_groups_legend_color' => 'Customers Groups Legend Color',
			'customers_groups_payment_methods' => 'Customers Groups Payment Methods',
			'customers_groups_extra_sc' => 'Customers Groups Extra Sc',
			'sort_order' => 'Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_groups_id',$this->customers_groups_id);
		$criteria->compare('customers_groups_name',$this->customers_groups_name,true);
		$criteria->compare('customers_groups_legend_color',$this->customers_groups_legend_color,true);
		$criteria->compare('customers_groups_payment_methods',$this->customers_groups_payment_methods,true);
		$criteria->compare('customers_groups_extra_sc',$this->customers_groups_extra_sc,true);
		$criteria->compare('sort_order',$this->sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getCustomersGroupsName($cgid) {
        $result = false;

        $m_data = $this->findByPk($cgid);
        if (isset($m_data->customers_groups_name)) {
            $result = $m_data->customers_groups_name;
        }

        return $result;
    }
}