<?php

/**
 * This is the model class for table "customers_groups_discount".
 *
 * The followings are the available columns in table 'customers_groups_discount':
 * @property integer $customers_groups_discount_id
 * @property integer $customers_groups_id
 * @property integer $categories_id
 * @property string $customers_groups_discount
 * @property string $customers_groups_rebate
 * @property string $c2c_customers_groups_discount
 * @property string $c2c_customers_groups_rebate
 */
class CustomersGroupsDiscountBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersGroupsDiscountBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_groups_discount';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_groups_id, categories_id', 'numerical', 'integerOnly'=>true),
			array('customers_groups_discount, customers_groups_rebate, c2c_customers_groups_discount, c2c_customers_groups_rebate', 'length', 'max'=>8),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_groups_discount_id, customers_groups_id, categories_id, customers_groups_discount, customers_groups_rebate, c2c_customers_groups_discount, c2c_customers_groups_rebate', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
//            'CustomersBase' => array(self::HAS_MANY, 'CustomersBase', array('customers_groups_id', 'customers_groups_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_groups_discount_id' => 'Customers Groups Discount',
			'customers_groups_id' => 'Customers Groups',
			'categories_id' => 'Categories',
			'customers_groups_discount' => 'Customers Groups Discount',
			'customers_groups_rebate' => 'Customers Groups Rebate',
			'c2c_customers_groups_discount' => 'C2c Customers Groups Discount',
			'c2c_customers_groups_rebate' => 'C2c Customers Groups Rebate',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_groups_discount_id',$this->customers_groups_discount_id);
		$criteria->compare('customers_groups_id',$this->customers_groups_id);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('customers_groups_discount',$this->customers_groups_discount,true);
		$criteria->compare('customers_groups_rebate',$this->customers_groups_rebate,true);
		$criteria->compare('c2c_customers_groups_discount',$this->c2c_customers_groups_discount,true);
		$criteria->compare('c2c_customers_groups_rebate',$this->c2c_customers_groups_rebate,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getDiscountInfoByCategoryIDByGroupID($categories_id, $groups_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
        $criteria->select = "customers_groups_discount, customers_groups_rebate, customers_groups_discount_id";
        $criteria->condition = "customers_groups_id = :groups_id 
            AND categories_id = :categories_id";
        $criteria->params = array(
            ':groups_id' => $groups_id, 
            ':categories_id' => $categories_id
        );
        if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
        return $return_array;
    }
    
    public function getCustomerGroupDiscountInfo($categoriesId, $groupsId) {
        $c_key = 'customers/group_id/' . $groupsId . '/categories_id/' . $categoriesId . '/array/discount_info/';
        $c_data = Yii::app()->cache->get($c_key);

        if ($c_data === false) {
            $c_data = array();

            $result = $this->findByAttributes(array('customers_groups_id' => $groupsId, 'categories_id' => $categoriesId));
            if (isset($result->customers_groups_discount_id)) {
                $c_data = array(
                    'customers_groups_discount_id' => $result->customers_groups_discount_id,
                    'customer_group_discount' => $result->c2c_customers_groups_discount,
                    'customer_group_rebate' => $result->c2c_customers_groups_rebate
                );
            }
            Yii::app()->cache->set($c_key, $c_data, 21600); // 6 hours
        }

        return $c_data;
    }
    
}