<?php

/**
 * This is the model class for table "customers_groups_extra_op".
 *
 * The followings are the available columns in table 'customers_groups_extra_op':
 * @property string $customers_groups_extra_op_id
 * @property string $customers_groups_discount_id
 * @property string $payment_methods_id
 * @property string $currency
 * @property string $bonus_op
 */
class CustomersGroupsExtraOpBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersGroupsExtraOpBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_groups_extra_op';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_groups_discount_id, payment_methods_id', 'length', 'max'=>11),
			array('currency', 'length', 'max'=>3),
			array('bonus_op', 'length', 'max'=>8),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_groups_extra_op_id, customers_groups_discount_id, payment_methods_id, currency, bonus_op', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_groups_extra_op_id' => 'Customers Groups Extra Op',
			'customers_groups_discount_id' => 'Customers Groups Discount',
			'payment_methods_id' => 'Payment Methods',
			'currency' => 'Currency',
			'bonus_op' => 'Bonus Op',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_groups_extra_op_id',$this->customers_groups_extra_op_id,true);
		$criteria->compare('customers_groups_discount_id',$this->customers_groups_discount_id,true);
		$criteria->compare('payment_methods_id',$this->payment_methods_id,true);
		$criteria->compare('currency',$this->currency,true);
		$criteria->compare('bonus_op',$this->bonus_op,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getTplIDsByMatchingAgainstScore($customers_groups_discount_id, $payment_methods_id, $currency) {
        $return_int = 0;

        $sql = "	SELECT bonus_op 
                    FROM " . $this->tableName() . "
                    WHERE customers_groups_discount_id = :customers_groups_discount_id
                        AND payment_methods_id = :payment_methods_id
                        AND (currency = :currency OR currency = '*')";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customers_groups_discount_id", $customers_groups_discount_id, PDO::PARAM_INT);
        $command->bindParam(":payment_methods_id", $payment_methods_id, PDO::PARAM_INT);
        $command->bindParam(":currency", $currency, PDO::PARAM_STR);
        if ($value = $command->queryScalar()) {
            $return_int = $value;
        }
        
		return $return_int;
    }
    
}