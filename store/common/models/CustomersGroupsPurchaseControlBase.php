<?php

/**
 * This is the model class for table "customers_groups_purchase_control".
 *
 * The followings are the available columns in table 'customers_groups_purchase_control':
 * @property integer $products_id
 * @property integer $customers_groups_id
 * @property integer $purchase_limit
 * @property integer $out_of_stock_flag
 */
class CustomersGroupsPurchaseControlBase extends MainModel {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CustomersGroupsPurchaseControlBase the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'customers_groups_purchase_control';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('products_id, customers_groups_id, purchase_limit, out_of_stock_flag', 'required'),
            array('products_id, customers_groups_id, purchase_limit, out_of_stock_flag', 'numerical', 'integerOnly' => true),
            array('out_of_stock_datetime', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('products_id, customers_groups_id, purchase_limit, out_of_stock_flag, out_of_stock_datetime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'products_id' => 'Products',
            'customers_groups_id' => 'Customers Groups',
            'purchase_limit' => 'Purchase Limit',
            'out_of_stock_flag' => 'Out Of Stock Flag',
            'out_of_stock_datetime' => 'Out Of Stock Datetime',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('customers_groups_id', $this->customers_groups_id);
        $criteria->compare('purchase_limit', $this->purchase_limit);
        $criteria->compare('out_of_stock_flag', $this->out_of_stock_flag);
        $criteria->compare('out_of_stock_datetime', $this->out_of_stock_datetime, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getPurchaseQtyLimitByCustomerGroupAndProductId($customerGroupsId, $productsId) {
        $return_array = array();

        $sql = "    SELECT purchase_limit, out_of_stock_flag
                    FROM " . $this->tableName() . " 
                    WHERE products_id = :productsId AND customers_groups_id = :customerGroupsId";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customerGroupsId", $customerGroupsId, PDO::PARAM_INT);
        $command->bindParam(":productsId", $productsId, PDO::PARAM_INT);

        if ($value = $command->queryRow()) {
            $return_array = $value;
        }

        return $return_array;
    }
    
    public function getAllOutOfStockData($productsId) {
        $return_array = array();
        
        $sql = "SELECT customers_groups_id
                FROM " . $this->tableName() . "
                WHERE products_id = :products_id AND out_of_stock_flag = 1";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $productsId, PDO::PARAM_INT);
        if ($dataset = $command->queryAll()) {
            $return_array = $dataset;
        }
        
        return $return_array;
    }

    public function updateOutOfStockFlag($productsId, $available_quantity) {
        $sql = "UPDATE " . $this->tableName() . "
                    SET out_of_stock_flag = 1,
                        out_of_stock_datetime = now()
                WHERE purchase_limit >= :purchase_limit 
                    AND products_id = :products_id 
                    AND out_of_stock_flag = 0";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $productsId, PDO::PARAM_INT);
        $command->bindParam(":purchase_limit", $available_quantity, PDO::PARAM_INT);

        return $command->execute();
    }

}
