<?php

/**
 * This is the model class for table "customers_groups_purchase_limit".
 *
 * The followings are the available columns in table 'customers_groups_purchase_limit':
 * @property integer $purchase_limit_id
 * @property integer $customers_groups_id
 * @property string $customers_aft_groups_id
 * @property string $aft_countries_risk_type
 * @property integer $purchase_limit_used
 * @property string $purchase_limit_per_day
 * @property string $purchase_limit_per_week
 * @property string $purchase_limit_per_month
 */
class CustomersGroupsPurchaseLimitBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersGroupsPurchaseLimitBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_groups_purchase_limit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_groups_id, purchase_limit_used', 'numerical', 'integerOnly'=>true),
			array('customers_aft_groups_id', 'length', 'max'=>11),
			array('aft_countries_risk_type', 'length', 'max'=>10),
			array('purchase_limit_per_day, purchase_limit_per_week, purchase_limit_per_month', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('purchase_limit_id, customers_groups_id, customers_aft_groups_id, aft_countries_risk_type, purchase_limit_used, purchase_limit_per_day, purchase_limit_per_week, purchase_limit_per_month', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'purchase_limit_id' => 'Purchase Limit',
			'customers_groups_id' => 'Customers Groups',
			'customers_aft_groups_id' => 'Customers Aft Groups',
			'aft_countries_risk_type' => 'Aft Countries Risk Type',
			'purchase_limit_used' => 'Purchase Limit Used',
			'purchase_limit_per_day' => 'Purchase Limit Per Day',
			'purchase_limit_per_week' => 'Purchase Limit Per Week',
			'purchase_limit_per_month' => 'Purchase Limit Per Month',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('purchase_limit_id',$this->purchase_limit_id);
		$criteria->compare('customers_groups_id',$this->customers_groups_id);
		$criteria->compare('customers_aft_groups_id',$this->customers_aft_groups_id,true);
		$criteria->compare('aft_countries_risk_type',$this->aft_countries_risk_type,true);
		$criteria->compare('purchase_limit_used',$this->purchase_limit_used);
		$criteria->compare('purchase_limit_per_day',$this->purchase_limit_per_day,true);
		$criteria->compare('purchase_limit_per_week',$this->purchase_limit_per_week,true);
		$criteria->compare('purchase_limit_per_month',$this->purchase_limit_per_month,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}