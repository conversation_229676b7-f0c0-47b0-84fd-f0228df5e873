<?php

/**
 * This is the model class for table "customers_info".
 *
 * The followings are the available columns in table 'customers_info':
 * @property integer $customers_info_id
 * @property string $customers_info_date_of_last_logon
 * @property integer $customer_info_account_dormant
 * @property integer $customers_info_number_of_logons
 * @property string $customers_info_date_account_created
 * @property string $customers_info_account_created_ip
 * @property integer $customers_info_account_created_from
 * @property string $customers_info_date_account_last_modified
 * @property string $customers_info_changes_made
 * @property integer $global_product_notifications
 * @property integer $customer_info_selected_country
 * @property integer $customer_info_selected_language_id
 */
class CustomersInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_info_changes_made', 'required'),
			array('customers_info_id, customer_info_account_dormant, customers_info_number_of_logons, customers_info_account_created_from, global_product_notifications, customer_info_selected_country, customer_info_selected_language_id', 'numerical', 'integerOnly'=>true),
			array('customers_info_account_created_ip', 'length', 'max'=>128),
			array('customers_info_date_of_last_logon, customers_info_date_account_created, customers_info_date_account_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_info_id, customers_info_date_of_last_logon, customer_info_account_dormant, customers_info_number_of_logons, customers_info_date_account_created, customers_info_account_created_ip, customers_info_account_created_from, customers_info_date_account_last_modified, customers_info_changes_made, global_product_notifications, customer_info_selected_country, customer_info_selected_language_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_info_id' => 'Customers Info',
			'customers_info_date_of_last_logon' => 'Customers Info Date Of Last Logon',
			'customer_info_account_dormant' => 'Customer Info Account Dormant',
			'customers_info_number_of_logons' => 'Customers Info Number Of Logons',
			'customers_info_date_account_created' => 'Customers Info Date Account Created',
			'customers_info_account_created_ip' => 'Customers Info Account Created Ip',
			'customers_info_account_created_from' => 'Customers Info Account Created From',
			'customers_info_date_account_last_modified' => 'Customers Info Date Account Last Modified',
			'customers_info_changes_made' => 'Customers Info Changes Made',
			'global_product_notifications' => 'Global Product Notifications',
			'customer_info_selected_country' => 'Customer Info Selected Country',
			'customer_info_selected_language_id' => 'Customer Info Selected Language',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_info_id',$this->customers_info_id);
		$criteria->compare('customers_info_date_of_last_logon',$this->customers_info_date_of_last_logon,true);
		$criteria->compare('customer_info_account_dormant',$this->customer_info_account_dormant);
		$criteria->compare('customers_info_number_of_logons',$this->customers_info_number_of_logons);
		$criteria->compare('customers_info_date_account_created',$this->customers_info_date_account_created,true);
		$criteria->compare('customers_info_account_created_ip',$this->customers_info_account_created_ip,true);
		$criteria->compare('customers_info_account_created_from',$this->customers_info_account_created_from);
		$criteria->compare('customers_info_date_account_last_modified',$this->customers_info_date_account_last_modified,true);
		$criteria->compare('customers_info_changes_made',$this->customers_info_changes_made,true);
		$criteria->compare('global_product_notifications',$this->global_product_notifications);
		$criteria->compare('customer_info_selected_country',$this->customer_info_selected_country);
		$criteria->compare('customer_info_selected_language_id',$this->customer_info_selected_language_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}