<?php

/**
 * This is the model class for table "customers_info_verification".
 *
 * The followings are the available columns in table 'customers_info_verification':
 * @property integer $customers_id
 * @property string $customers_info_value
 * @property string $serial_number
 * @property integer $verify_try_turns
 * @property integer $info_verified
 * @property string $info_verification_type
 * @property string $customers_info_verification_mode
 * @property string $customers_info_verification_date
 * @property string $call_language
 */
class CustomersInfoVerificationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersInfoVerificationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_info_verification';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, verify_try_turns, info_verified', 'numerical', 'integerOnly'=>true),
			array('customers_info_value', 'length', 'max'=>96),
			array('serial_number', 'length', 'max'=>12),
			array('info_verification_type', 'length', 'max'=>32),
			array('customers_info_verification_mode', 'length', 'max'=>1),
			array('call_language', 'length', 'max'=>40),
			array('customers_info_verification_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_info_value, serial_number, verify_try_turns, info_verified, info_verification_type, customers_info_verification_mode, customers_info_verification_date, call_language', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_info_value' => 'Customers Info Value',
			'serial_number' => 'Serial Number',
			'verify_try_turns' => 'Verify Try Turns',
			'info_verified' => 'Info Verified',
			'info_verification_type' => 'Info Verification Type',
			'customers_info_verification_mode' => 'Customers Info Verification Mode',
			'customers_info_verification_date' => 'Customers Info Verification Date',
			'call_language' => 'Call Language',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_info_value',$this->customers_info_value,true);
		$criteria->compare('serial_number',$this->serial_number,true);
		$criteria->compare('verify_try_turns',$this->verify_try_turns);
		$criteria->compare('info_verified',$this->info_verified);
		$criteria->compare('info_verification_type',$this->info_verification_type,true);
		$criteria->compare('customers_info_verification_mode',$this->customers_info_verification_mode,true);
		$criteria->compare('customers_info_verification_date',$this->customers_info_verification_date,true);
		$criteria->compare('call_language',$this->call_language,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}