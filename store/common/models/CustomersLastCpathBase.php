<?php

/**
 * This is the model class for table "customers_last_cpath".
 *
 * The followings are the available columns in table 'customers_last_cpath':
 * @property integer $customers_id
 * @property string $full_name
 * @property string $session_id
 * @property string $ip_address
 * @property string $time_entry
 * @property string $last_page_url
 * @property string $last_cpath
 */
class CustomersLastCpathBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersLastCpathBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_last_cpath';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id', 'numerical', 'integerOnly'=>true),
			array('full_name, last_page_url, last_cpath', 'length', 'max'=>64),
			array('session_id', 'length', 'max'=>128),
			array('ip_address', 'length', 'max'=>15),
			array('time_entry', 'length', 'max'=>14),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, full_name, session_id, ip_address, time_entry, last_page_url, last_cpath', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'full_name' => 'Full Name',
			'session_id' => 'Session',
			'ip_address' => 'Ip Address',
			'time_entry' => 'Time Entry',
			'last_page_url' => 'Last Page Url',
			'last_cpath' => 'Last Cpath',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('full_name',$this->full_name,true);
		$criteria->compare('session_id',$this->session_id,true);
		$criteria->compare('ip_address',$this->ip_address,true);
		$criteria->compare('time_entry',$this->time_entry,true);
		$criteria->compare('last_page_url',$this->last_page_url,true);
		$criteria->compare('last_cpath',$this->last_cpath,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}