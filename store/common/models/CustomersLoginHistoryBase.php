<?php

/**
 * This is the model class for table "customers_login_history".
 *
 * The followings are the available columns in table 'customers_login_history':
 * @property integer $customers_id
 * @property string $customers_login_date
 * @property string $customers_login_ip
 * @property string $customers_login_ua_info
 * @property string $login_method
 */
class CustomersLoginHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersLoginHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_login_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, customers_login_date, customers_login_ip, customers_login_ua_info, login_method', 'required'),
			array('customers_id', 'numerical', 'integerOnly'=>true),
			array('customers_login_ip', 'length', 'max'=>128),
			array('customers_login_ua_info', 'length', 'max'=>255),
			array('login_method', 'length', 'max'=>12),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_login_date, customers_login_ip, customers_login_ua_info, login_method', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_login_date' => 'Customers Login Date',
			'customers_login_ip' => 'Customers Login Ip',
			'customers_login_ua_info' => 'Customers Login Ua Info',
			'login_method' => 'Login Method',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_login_date',$this->customers_login_date,true);
		$criteria->compare('customers_login_ip',$this->customers_login_ip,true);
		$criteria->compare('customers_login_ua_info',$this->customers_login_ua_info,true);
		$criteria->compare('login_method',$this->login_method,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}