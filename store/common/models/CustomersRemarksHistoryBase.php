<?php

/**
 * This is the model class for table "customers_remarks_history".
 *
 * The followings are the available columns in table 'customers_remarks_history':
 * @property integer $customers_remarks_history_id
 * @property integer $customers_id
 * @property string $date_remarks_added
 * @property string $remarks
 * @property string $remarks_added_by
 */
class CustomersRemarksHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersRemarksHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_remarks_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id', 'numerical', 'integerOnly'=>true),
			array('remarks_added_by', 'length', 'max'=>255),
			array('date_remarks_added, remarks', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_remarks_history_id, customers_id, date_remarks_added, remarks, remarks_added_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_remarks_history_id' => 'Customers Remarks History',
			'customers_id' => 'Customers',
			'date_remarks_added' => 'Date Remarks Added',
			'remarks' => 'Remarks',
			'remarks_added_by' => 'Remarks Added By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_remarks_history_id',$this->customers_remarks_history_id);
		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('date_remarks_added',$this->date_remarks_added,true);
		$criteria->compare('remarks',$this->remarks,true);
		$criteria->compare('remarks_added_by',$this->remarks_added_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}