<?php

/**
 * This is the model class for table "customers_security".
 *
 * The followings are the available columns in table 'customers_security':
 * @property integer $customers_id
 * @property string $customers_security_question_1
 * @property string $customers_security_answer_1
 * @property integer $customers_security_counter
 * @property string $customers_security_question_ask
 * @property string $customers_security_update_time
 */
class CustomersSecurityBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersSecurityBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_security';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, customers_security_counter', 'numerical', 'integerOnly'=>true),
			array('customers_security_question_1', 'length', 'max'=>255),
			array('customers_security_answer_1', 'length', 'max'=>50),
			array('customers_security_question_ask', 'length', 'max'=>5),
			array('customers_security_update_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_security_question_1, customers_security_answer_1, customers_security_counter, customers_security_question_ask, customers_security_update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_security_question_1' => 'Customers Security Question 1',
			'customers_security_answer_1' => 'Customers Security Answer 1',
			'customers_security_counter' => 'Customers Security Counter',
			'customers_security_question_ask' => 'Customers Security Question Ask',
			'customers_security_update_time' => 'Customers Security Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_security_question_1',$this->customers_security_question_1,true);
		$criteria->compare('customers_security_answer_1',$this->customers_security_answer_1,true);
		$criteria->compare('customers_security_counter',$this->customers_security_counter);
		$criteria->compare('customers_security_question_ask',$this->customers_security_question_ask,true);
		$criteria->compare('customers_security_update_time',$this->customers_security_update_time,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}