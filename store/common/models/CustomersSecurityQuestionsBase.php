<?php

/**
 * This is the model class for table "customers_security_questions".
 *
 * The followings are the available columns in table 'customers_security_questions':
 * @property integer $customers_security_question_id
 * @property integer $language_id
 * @property string $customers_security_question
 */
class CustomersSecurityQuestionsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersSecurityQuestionsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_security_questions';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('language_id', 'numerical', 'integerOnly'=>true),
			array('customers_security_question', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_security_question_id, language_id, customers_security_question', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_security_question_id' => 'Customers Security Question',
			'language_id' => 'Language',
			'customers_security_question' => 'Customers Security Question',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_security_question_id',$this->customers_security_question_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('customers_security_question',$this->customers_security_question,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}