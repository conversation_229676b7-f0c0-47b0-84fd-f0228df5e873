<?php

/**
 * This is the model class for table "customers_setting".
 *
 * The followings are the available columns in table 'customers_setting':
 * @property string $customers_id
 * @property string $customers_setting_key
 * @property string $customers_setting_value
 * @property string $created_datetime
 * @property string $updated_datetime
 */
class CustomersSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, customers_setting_key, created_datetime, updated_datetime', 'required'),
			array('customers_id', 'length', 'max'=>11),
			array('customers_setting_key', 'length', 'max'=>64),
			array('customers_setting_value', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_setting_key, customers_setting_value, created_datetime, updated_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_setting_key' => 'Customers Setting Key',
			'customers_setting_value' => 'Customers Setting Value',
			'created_datetime' => 'Created Datetime',
			'updated_datetime' => 'Updated Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id,true);
		$criteria->compare('customers_setting_key',$this->customers_setting_key,true);
		$criteria->compare('customers_setting_value',$this->customers_setting_value,true);
		$criteria->compare('created_datetime',$this->created_datetime,true);
		$criteria->compare('updated_datetime',$this->updated_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}