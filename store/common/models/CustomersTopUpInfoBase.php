<?php

/**
 * This is the model class for table "customers_top_up_info".
 *
 * The followings are the available columns in table 'customers_top_up_info':
 * @property string $top_up_info_id
 * @property integer $orders_products_id
 * @property string $top_up_value
 */
class CustomersTopUpInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersTopUpInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_top_up_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_id', 'numerical', 'integerOnly'=>true),
			array('top_up_info_id', 'length', 'max'=>10),
			array('top_up_value', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('top_up_info_id, orders_products_id, top_up_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'top_up_info_id' => 'Top Up Info',
			'orders_products_id' => 'Orders Products',
			'top_up_value' => 'Top Up Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('top_up_info_id',$this->top_up_info_id,true);
		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('top_up_value',$this->top_up_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getCustomerTopupInfo($opId) {
        $criteria = new CDbCriteria();
        $criteria->select = 'top_up_info_id, top_up_value';
        $criteria->condition = 'orders_products_id = :opId';
        $criteria->params = array(':opId' => $opId);
        $result = $this->model()->findAll($criteria);
        return $result;
    }
}