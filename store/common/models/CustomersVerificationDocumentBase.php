<?php

/**
 * This is the model class for table "customers_verification_document".
 *
 * The followings are the available columns in table 'customers_verification_document':
 * @property integer $customers_id
 * @property string $files_001
 * @property integer $files_001_locked
 * @property string $files_002
 * @property integer $files_002_locked
 * @property string $files_003
 * @property integer $files_003_locked
 * @property string $files_004
 * @property integer $files_004_locked
 * @property string $files_005
 * @property integer $files_005_locked
 */
class CustomersVerificationDocumentBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersVerificationDocumentBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_verification_document';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, files_001_locked, files_002_locked, files_003_locked, files_004_locked, files_005_locked', 'numerical', 'integerOnly'=>true),
			array('files_001, files_002, files_003, files_004, files_005', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, files_001, files_001_locked, files_002, files_002_locked, files_003, files_003_locked, files_004, files_004_locked, files_005, files_005_locked', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'files_001' => 'Files 001',
			'files_001_locked' => 'Files 001 Locked',
			'files_002' => 'Files 002',
			'files_002_locked' => 'Files 002 Locked',
			'files_003' => 'Files 003',
			'files_003_locked' => 'Files 003 Locked',
			'files_004' => 'Files 004',
			'files_004_locked' => 'Files 004 Locked',
			'files_005' => 'Files 005',
			'files_005_locked' => 'Files 005 Locked',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('files_001',$this->files_001,true);
		$criteria->compare('files_001_locked',$this->files_001_locked);
		$criteria->compare('files_002',$this->files_002,true);
		$criteria->compare('files_002_locked',$this->files_002_locked);
		$criteria->compare('files_003',$this->files_003,true);
		$criteria->compare('files_003_locked',$this->files_003_locked);
		$criteria->compare('files_004',$this->files_004,true);
		$criteria->compare('files_004_locked',$this->files_004_locked);
		$criteria->compare('files_005',$this->files_005,true);
		$criteria->compare('files_005_locked',$this->files_005_locked);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function requireVerification() {
        $m_data = $this->findByPk(Yii::app()->user->id);

        if (isset($m_data->customers_id)) {
            $result = $m_data->files_001_locked + $m_data->files_002_locked + $m_data->files_003_locked + $m_data->files_004_locked + $m_data->files_005_locked;

            if ($result === 5) {
                return false;
            }
        } else {
            return false;
        }

        return true;
    }
}