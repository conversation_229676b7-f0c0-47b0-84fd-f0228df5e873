<?php

/**
 * This is the model class for table "customers_verification_document_log".
 *
 * The followings are the available columns in table 'customers_verification_document_log':
 * @property integer $log_id
 * @property string $log_users_id
 * @property string $log_users_type
 * @property integer $log_customers_id
 * @property string $log_docs_id
 * @property string $log_IP
 * @property string $log_datetime
 * @property string $log_filename
 * @property string $log_action
 */
class CustomersVerificationDocumentLogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersVerificationDocumentLogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_verification_document_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('log_customers_id', 'numerical', 'integerOnly'=>true),
			array('log_users_id', 'length', 'max'=>255),
			array('log_users_type', 'length', 'max'=>16),
			array('log_docs_id', 'length', 'max'=>3),
			array('log_IP', 'length', 'max'=>128),
			array('log_filename', 'length', 'max'=>32),
			array('log_action', 'length', 'max'=>20),
			array('log_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('log_id, log_users_id, log_users_type, log_customers_id, log_docs_id, log_IP, log_datetime, log_filename, log_action', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'log_id' => 'Log',
			'log_users_id' => 'Log Users',
			'log_users_type' => 'Log Users Type',
			'log_customers_id' => 'Log Customers',
			'log_docs_id' => 'Log Docs',
			'log_IP' => 'Log Ip',
			'log_datetime' => 'Log Datetime',
			'log_filename' => 'Log Filename',
			'log_action' => 'Log Action',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('log_id',$this->log_id);
		$criteria->compare('log_users_id',$this->log_users_id,true);
		$criteria->compare('log_users_type',$this->log_users_type,true);
		$criteria->compare('log_customers_id',$this->log_customers_id);
		$criteria->compare('log_docs_id',$this->log_docs_id,true);
		$criteria->compare('log_IP',$this->log_IP,true);
		$criteria->compare('log_datetime',$this->log_datetime,true);
		$criteria->compare('log_filename',$this->log_filename,true);
		$criteria->compare('log_action',$this->log_action,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function scopes() {
        return array(
            'lastRecord' => array(
                'condition' => 'log_users_id = ' . Yii::app()->user->id,
                'order' => 'log_datetime DESC',
                'limit' => 1,
            ),
        );
    }


}