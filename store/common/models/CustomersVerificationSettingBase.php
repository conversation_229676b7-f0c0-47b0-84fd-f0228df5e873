<?php

/**
 * This is the model class for table "customers_verification_setting".
 *
 * The followings are the available columns in table 'customers_verification_setting':
 * @property integer $countries_id
 * @property string $custom_products_type
 * @property string $email
 * @property string $telephone
 * @property string $files_001
 * @property string $files_002
 * @property string $files_003
 * @property string $files_004
 * @property string $files_005
 */
class CustomersVerificationSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersVerificationSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_verification_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('countries_id, custom_products_type', 'required'),
			array('countries_id', 'numerical', 'integerOnly'=>true),
			array('custom_products_type', 'length', 'max'=>11),
			array('email, telephone, files_001, files_002, files_003, files_004, files_005', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('countries_id, custom_products_type, email, telephone, files_001, files_002, files_003, files_004, files_005', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'countries_id' => 'Countries',
			'custom_products_type' => 'Custom Products Type',
			'email' => 'Email',
			'telephone' => 'Telephone',
			'files_001' => 'Files 001',
			'files_002' => 'Files 002',
			'files_003' => 'Files 003',
			'files_004' => 'Files 004',
			'files_005' => 'Files 005',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('countries_id',$this->countries_id);
		$criteria->compare('custom_products_type',$this->custom_products_type,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('telephone',$this->telephone,true);
		$criteria->compare('files_001',$this->files_001,true);
		$criteria->compare('files_002',$this->files_002,true);
		$criteria->compare('files_003',$this->files_003,true);
		$criteria->compare('files_004',$this->files_004,true);
		$criteria->compare('files_005',$this->files_005,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}