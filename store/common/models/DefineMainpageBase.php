<?php

/**
 * This is the model class for table "define_mainpage".
 *
 * The followings are the available columns in table 'define_mainpage':
 * @property integer $geo_zone_id
 * @property integer $Id
 * @property integer $language_id
 * @property string $Text
 * @property string $configuration_value
 * @property string $mainpage_slider_content
 * @property string $mainpage_best_selling_image
 * @property string $mainpage_tab_content
 * @property string $footer_all_payment_image
 */
class DefineMainpageBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DefineMainpageBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'define_mainpage';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('Text', 'required'),
			array('geo_zone_id, Id, language_id', 'numerical', 'integerOnly'=>true),
			array('configuration_value, mainpage_slider_content, mainpage_best_selling_image, mainpage_tab_content, footer_all_payment_image', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('geo_zone_id, Id, language_id, Text, configuration_value, mainpage_slider_content, mainpage_best_selling_image, mainpage_tab_content, footer_all_payment_image', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'geo_zone_id' => 'Geo Zone',
			'Id' => 'ID',
			'language_id' => 'Language',
			'Text' => 'Text',
			'configuration_value' => 'Configuration Value',
			'mainpage_slider_content' => 'Mainpage Slider Content',
			'mainpage_best_selling_image' => 'Mainpage Best Selling Image',
			'mainpage_tab_content' => 'Mainpage Tab Content',
			'footer_all_payment_image' => 'Footer All Payment Image',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('geo_zone_id',$this->geo_zone_id);
		$criteria->compare('Id',$this->Id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('Text',$this->Text,true);
		$criteria->compare('configuration_value',$this->configuration_value,true);
		$criteria->compare('mainpage_slider_content',$this->mainpage_slider_content,true);
		$criteria->compare('mainpage_best_selling_image',$this->mainpage_best_selling_image,true);
		$criteria->compare('mainpage_tab_content',$this->mainpage_tab_content,true);
		$criteria->compare('footer_all_payment_image',$this->footer_all_payment_image,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}