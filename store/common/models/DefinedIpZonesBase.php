<?php

/**
 * This is the model class for table "defined_ip_zones".
 *
 * The followings are the available columns in table 'defined_ip_zones':
 * @property integer $defined_ip_zones_id
 * @property string $defined_ip_zones_name
 * @property string $defined_ip_zones_description
 * @property string $defined_ip_zones_last_modified
 * @property string $defined_ip_zones_date_added
 */
class DefinedIpZonesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DefinedIpZonesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'defined_ip_zones';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('defined_ip_zones_name', 'length', 'max'=>32),
			array('defined_ip_zones_description', 'length', 'max'=>255),
			array('defined_ip_zones_last_modified, defined_ip_zones_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('defined_ip_zones_id, defined_ip_zones_name, defined_ip_zones_description, defined_ip_zones_last_modified, defined_ip_zones_date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'defined_ip_zones_id' => 'Defined Ip Zones',
			'defined_ip_zones_name' => 'Defined Ip Zones Name',
			'defined_ip_zones_description' => 'Defined Ip Zones Description',
			'defined_ip_zones_last_modified' => 'Defined Ip Zones Last Modified',
			'defined_ip_zones_date_added' => 'Defined Ip Zones Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('defined_ip_zones_id',$this->defined_ip_zones_id);
		$criteria->compare('defined_ip_zones_name',$this->defined_ip_zones_name,true);
		$criteria->compare('defined_ip_zones_description',$this->defined_ip_zones_description,true);
		$criteria->compare('defined_ip_zones_last_modified',$this->defined_ip_zones_last_modified,true);
		$criteria->compare('defined_ip_zones_date_added',$this->defined_ip_zones_date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}