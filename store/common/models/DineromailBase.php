<?php

/**
 * This is the model class for table "dineromail".
 *
 * The followings are the available columns in table 'dineromail':
 * @property integer $orders_id
 * @property string $dineromail_transaction_number
 * @property string $dineromail_customer_email
 * @property string $dineromail_amount
 * @property string $dineromail_currency
 * @property string $dineromail_currency_flag
 * @property string $dineromail_net_amount
 * @property integer $dineromail_payment_method
 * @property string $dineromail_status
 */
class DineromailBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DineromailBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'dineromail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, dineromail_payment_method', 'numerical', 'integerOnly'=>true),
			array('dineromail_transaction_number', 'length', 'max'=>30),
			array('dineromail_customer_email', 'length', 'max'=>32),
			array('dineromail_amount, dineromail_net_amount', 'length', 'max'=>15),
			array('dineromail_currency', 'length', 'max'=>3),
			array('dineromail_currency_flag, dineromail_status', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, dineromail_transaction_number, dineromail_customer_email, dineromail_amount, dineromail_currency, dineromail_currency_flag, dineromail_net_amount, dineromail_payment_method, dineromail_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'dineromail_transaction_number' => 'Dineromail Transaction Number',
			'dineromail_customer_email' => 'Dineromail Customer Email',
			'dineromail_amount' => 'Dineromail Amount',
			'dineromail_currency' => 'Dineromail Currency',
			'dineromail_currency_flag' => 'Dineromail Currency Flag',
			'dineromail_net_amount' => 'Dineromail Net Amount',
			'dineromail_payment_method' => 'Dineromail Payment Method',
			'dineromail_status' => 'Dineromail Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('dineromail_transaction_number',$this->dineromail_transaction_number,true);
		$criteria->compare('dineromail_customer_email',$this->dineromail_customer_email,true);
		$criteria->compare('dineromail_amount',$this->dineromail_amount,true);
		$criteria->compare('dineromail_currency',$this->dineromail_currency,true);
		$criteria->compare('dineromail_currency_flag',$this->dineromail_currency_flag,true);
		$criteria->compare('dineromail_net_amount',$this->dineromail_net_amount,true);
		$criteria->compare('dineromail_payment_method',$this->dineromail_payment_method);
		$criteria->compare('dineromail_status',$this->dineromail_status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}