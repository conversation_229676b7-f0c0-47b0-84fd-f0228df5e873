<?php

/**
 * This is the model class for table "dineromail_status_history".
 *
 * The followings are the available columns in table 'dineromail_status_history':
 * @property integer $dineromail_status_history_id
 * @property integer $orders_id
 * @property string $dineromail_status_date
 * @property string $dineromail_status
 * @property string $dineromail_status_description
 */
class DineromailStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DineromailStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'dineromail_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('dineromail_status', 'length', 'max'=>1),
			array('dineromail_status_description', 'length', 'max'=>255),
			array('dineromail_status_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('dineromail_status_history_id, orders_id, dineromail_status_date, dineromail_status, dineromail_status_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'dineromail_status_history_id' => 'Dineromail Status History',
			'orders_id' => 'Orders',
			'dineromail_status_date' => 'Dineromail Status Date',
			'dineromail_status' => 'Dineromail Status',
			'dineromail_status_description' => 'Dineromail Status Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('dineromail_status_history_id',$this->dineromail_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('dineromail_status_date',$this->dineromail_status_date,true);
		$criteria->compare('dineromail_status',$this->dineromail_status,true);
		$criteria->compare('dineromail_status_description',$this->dineromail_status_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}