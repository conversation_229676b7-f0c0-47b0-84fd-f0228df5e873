<?php

/**
 * This is the model class for table "egold".
 *
 * The followings are the available columns in table 'egold':
 * @property string $orders_id
 * @property integer $egold_transaction_status
 * @property string $egold_payment_amount
 * @property string $egold_payment_units
 * @property string $egold_payment_metal_id
 * @property string $egold_payment_batch_num
 * @property string $egold_payer_account
 * @property string $egold_actual_payment_ounces
 * @property string $egold_usd_per_ounce
 * @property string $egold_feeweight
 * @property string $egold_payment_timestamp
 * @property string $egold_v2_hash
 */
class EgoldBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EgoldBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'egold';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('egold_transaction_status', 'numerical', 'integerOnly'=>true),
			array('orders_id', 'length', 'max'=>11),
			array('egold_payment_amount, egold_actual_payment_ounces, egold_usd_per_ounce, egold_feeweight', 'length', 'max'=>15),
			array('egold_payment_units', 'length', 'max'=>10),
			array('egold_payment_metal_id', 'length', 'max'=>1),
			array('egold_payment_batch_num, egold_payer_account, egold_payment_timestamp, egold_v2_hash', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, egold_transaction_status, egold_payment_amount, egold_payment_units, egold_payment_metal_id, egold_payment_batch_num, egold_payer_account, egold_actual_payment_ounces, egold_usd_per_ounce, egold_feeweight, egold_payment_timestamp, egold_v2_hash', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'egold_transaction_status' => 'Egold Transaction Status',
			'egold_payment_amount' => 'Egold Payment Amount',
			'egold_payment_units' => 'Egold Payment Units',
			'egold_payment_metal_id' => 'Egold Payment Metal',
			'egold_payment_batch_num' => 'Egold Payment Batch Num',
			'egold_payer_account' => 'Egold Payer Account',
			'egold_actual_payment_ounces' => 'Egold Actual Payment Ounces',
			'egold_usd_per_ounce' => 'Egold Usd Per Ounce',
			'egold_feeweight' => 'Egold Feeweight',
			'egold_payment_timestamp' => 'Egold Payment Timestamp',
			'egold_v2_hash' => 'Egold V2 Hash',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id,true);
		$criteria->compare('egold_transaction_status',$this->egold_transaction_status);
		$criteria->compare('egold_payment_amount',$this->egold_payment_amount,true);
		$criteria->compare('egold_payment_units',$this->egold_payment_units,true);
		$criteria->compare('egold_payment_metal_id',$this->egold_payment_metal_id,true);
		$criteria->compare('egold_payment_batch_num',$this->egold_payment_batch_num,true);
		$criteria->compare('egold_payer_account',$this->egold_payer_account,true);
		$criteria->compare('egold_actual_payment_ounces',$this->egold_actual_payment_ounces,true);
		$criteria->compare('egold_usd_per_ounce',$this->egold_usd_per_ounce,true);
		$criteria->compare('egold_feeweight',$this->egold_feeweight,true);
		$criteria->compare('egold_payment_timestamp',$this->egold_payment_timestamp,true);
		$criteria->compare('egold_v2_hash',$this->egold_v2_hash,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}