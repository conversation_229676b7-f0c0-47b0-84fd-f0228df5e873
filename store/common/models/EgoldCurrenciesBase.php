<?php

/**
 * This is the model class for table "egold_currencies".
 *
 * The followings are the available columns in table 'egold_currencies':
 * @property string $egold_currencies_code
 * @property string $egold_currencies_title
 * @property string $egold_currencies_payment_unit
 */
class EgoldCurrenciesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EgoldCurrenciesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'egold_currencies';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('egold_currencies_code', 'length', 'max'=>3),
			array('egold_currencies_title', 'length', 'max'=>32),
			array('egold_currencies_payment_unit', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('egold_currencies_code, egold_currencies_title, egold_currencies_payment_unit', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'egold_currencies_code' => 'Egold Currencies Code',
			'egold_currencies_title' => 'Egold Currencies Title',
			'egold_currencies_payment_unit' => 'Egold Currencies Payment Unit',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('egold_currencies_code',$this->egold_currencies_code,true);
		$criteria->compare('egold_currencies_title',$this->egold_currencies_title,true);
		$criteria->compare('egold_currencies_payment_unit',$this->egold_currencies_payment_unit,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}