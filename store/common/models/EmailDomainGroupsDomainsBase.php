<?php

/**
 * This is the model class for table "email_domain_groups_domains".
 *
 * The followings are the available columns in table 'email_domain_groups_domains':
 * @property integer $email_domain_groups_domains_id
 * @property string $email_domain_groups_domains_name
 * @property integer $email_domain_groups_id
 */
class EmailDomainGroupsDomainsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EmailDomainGroupsDomainsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'email_domain_groups_domains';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('email_domain_groups_id', 'numerical', 'integerOnly'=>true),
			array('email_domain_groups_domains_name', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('email_domain_groups_domains_id, email_domain_groups_domains_name, email_domain_groups_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'email_domain_groups_domains_id' => 'Email Domain Groups Domains',
			'email_domain_groups_domains_name' => 'Email Domain Groups Domains Name',
			'email_domain_groups_id' => 'Email Domain Groups',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('email_domain_groups_domains_id',$this->email_domain_groups_domains_id);
		$criteria->compare('email_domain_groups_domains_name',$this->email_domain_groups_domains_name,true);
		$criteria->compare('email_domain_groups_id',$this->email_domain_groups_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}