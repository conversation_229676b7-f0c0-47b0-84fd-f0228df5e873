<?php

/**
 * This is the model class for table "events".
 *
 * The followings are the available columns in table 'events':
 * @property integer $events_id
 * @property integer $news_id
 * @property string $events_name
 * @property string $events_remark
 * @property string $events_sender_email
 * @property string $events_admin_copy_email
 * @property string $events_email_tpl
 * @property integer $events_status
 * @property string $events_order_period_date_from
 * @property string $events_order_period_date_to
 * @property string $events_order_period_note
 * @property string $events_order_period_empty_err_msg
 * @property string $events_order_period_invalid_err_msg
 */
class EventsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'events';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('events_remark, events_email_tpl', 'required'),
			array('news_id, events_status', 'numerical', 'integerOnly'=>true),
			array('events_name', 'length', 'max'=>100),
			array('events_sender_email, events_admin_copy_email, events_order_period_note, events_order_period_empty_err_msg, events_order_period_invalid_err_msg', 'length', 'max'=>255),
			array('events_order_period_date_from, events_order_period_date_to', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('events_id, news_id, events_name, events_remark, events_sender_email, events_admin_copy_email, events_email_tpl, events_status, events_order_period_date_from, events_order_period_date_to, events_order_period_note, events_order_period_empty_err_msg, events_order_period_invalid_err_msg', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'events_id' => 'Events',
			'news_id' => 'News',
			'events_name' => 'Events Name',
			'events_remark' => 'Events Remark',
			'events_sender_email' => 'Events Sender Email',
			'events_admin_copy_email' => 'Events Admin Copy Email',
			'events_email_tpl' => 'Events Email Tpl',
			'events_status' => 'Events Status',
			'events_order_period_date_from' => 'Events Order Period Date From',
			'events_order_period_date_to' => 'Events Order Period Date To',
			'events_order_period_note' => 'Events Order Period Note',
			'events_order_period_empty_err_msg' => 'Events Order Period Empty Err Msg',
			'events_order_period_invalid_err_msg' => 'Events Order Period Invalid Err Msg',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('events_id',$this->events_id);
		$criteria->compare('news_id',$this->news_id);
		$criteria->compare('events_name',$this->events_name,true);
		$criteria->compare('events_remark',$this->events_remark,true);
		$criteria->compare('events_sender_email',$this->events_sender_email,true);
		$criteria->compare('events_admin_copy_email',$this->events_admin_copy_email,true);
		$criteria->compare('events_email_tpl',$this->events_email_tpl,true);
		$criteria->compare('events_status',$this->events_status);
		$criteria->compare('events_order_period_date_from',$this->events_order_period_date_from,true);
		$criteria->compare('events_order_period_date_to',$this->events_order_period_date_to,true);
		$criteria->compare('events_order_period_note',$this->events_order_period_note,true);
		$criteria->compare('events_order_period_empty_err_msg',$this->events_order_period_empty_err_msg,true);
		$criteria->compare('events_order_period_invalid_err_msg',$this->events_order_period_invalid_err_msg,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}