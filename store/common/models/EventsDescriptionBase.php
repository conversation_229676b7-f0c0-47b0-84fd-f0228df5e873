<?php

/**
 * This is the model class for table "events_description".
 *
 * The followings are the available columns in table 'events_description':
 * @property integer $events_id
 * @property integer $language_id
 * @property string $events_name
 * @property string $events_email_tpl
 */
class EventsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'events_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('events_email_tpl', 'required'),
			array('events_id, language_id', 'numerical', 'integerOnly'=>true),
			array('events_name', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('events_id, language_id, events_name, events_email_tpl', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'events_id' => 'Events',
			'language_id' => 'Language',
			'events_name' => 'Events Name',
			'events_email_tpl' => 'Events Email Tpl',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('events_id',$this->events_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('events_name',$this->events_name,true);
		$criteria->compare('events_email_tpl',$this->events_email_tpl,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}