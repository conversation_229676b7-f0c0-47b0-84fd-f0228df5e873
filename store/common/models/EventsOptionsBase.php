<?php

/**
 * This is the model class for table "events_options".
 *
 * The followings are the available columns in table 'events_options':
 * @property integer $events_options_id
 * @property integer $events_id
 * @property string $events_options_title
 * @property integer $events_options_input_type
 * @property integer $events_options_max_size
 * @property integer $events_options_row_size
 * @property integer $events_options_column_size
 * @property string $events_options_name
 * @property string $events_options_sub_sort_order
 * @property integer $events_options_required
 * @property string $events_options_note
 * @property string $events_options_err_msg
 * @property integer $events_options_sort_order
 * @property integer $events_options_status
 * @property string $events_options_last_modified
 * @property string $events_options_date_added
 */
class EventsOptionsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventsOptionsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'events_options';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('events_id, events_options_input_type, events_options_max_size, events_options_row_size, events_options_column_size, events_options_required, events_options_sort_order, events_options_status', 'numerical', 'integerOnly'=>true),
			array('events_options_title, events_options_name, events_options_sub_sort_order, events_options_note, events_options_err_msg', 'length', 'max'=>255),
			array('events_options_last_modified, events_options_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('events_options_id, events_id, events_options_title, events_options_input_type, events_options_max_size, events_options_row_size, events_options_column_size, events_options_name, events_options_sub_sort_order, events_options_required, events_options_note, events_options_err_msg, events_options_sort_order, events_options_status, events_options_last_modified, events_options_date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'events_options_id' => 'Events Options',
			'events_id' => 'Events',
			'events_options_title' => 'Events Options Title',
			'events_options_input_type' => 'Events Options Input Type',
			'events_options_max_size' => 'Events Options Max Size',
			'events_options_row_size' => 'Events Options Row Size',
			'events_options_column_size' => 'Events Options Column Size',
			'events_options_name' => 'Events Options Name',
			'events_options_sub_sort_order' => 'Events Options Sub Sort Order',
			'events_options_required' => 'Events Options Required',
			'events_options_note' => 'Events Options Note',
			'events_options_err_msg' => 'Events Options Err Msg',
			'events_options_sort_order' => 'Events Options Sort Order',
			'events_options_status' => 'Events Options Status',
			'events_options_last_modified' => 'Events Options Last Modified',
			'events_options_date_added' => 'Events Options Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('events_options_id',$this->events_options_id);
		$criteria->compare('events_id',$this->events_id);
		$criteria->compare('events_options_title',$this->events_options_title,true);
		$criteria->compare('events_options_input_type',$this->events_options_input_type);
		$criteria->compare('events_options_max_size',$this->events_options_max_size);
		$criteria->compare('events_options_row_size',$this->events_options_row_size);
		$criteria->compare('events_options_column_size',$this->events_options_column_size);
		$criteria->compare('events_options_name',$this->events_options_name,true);
		$criteria->compare('events_options_sub_sort_order',$this->events_options_sub_sort_order,true);
		$criteria->compare('events_options_required',$this->events_options_required);
		$criteria->compare('events_options_note',$this->events_options_note,true);
		$criteria->compare('events_options_err_msg',$this->events_options_err_msg,true);
		$criteria->compare('events_options_sort_order',$this->events_options_sort_order);
		$criteria->compare('events_options_status',$this->events_options_status);
		$criteria->compare('events_options_last_modified',$this->events_options_last_modified,true);
		$criteria->compare('events_options_date_added',$this->events_options_date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}