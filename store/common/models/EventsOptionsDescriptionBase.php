<?php

/**
 * This is the model class for table "events_options_description".
 *
 * The followings are the available columns in table 'events_options_description':
 * @property integer $events_options_id
 * @property integer $language_id
 * @property string $events_options_title
 * @property string $events_options_note
 * @property string $events_options_name
 * @property integer $events_options_max_size
 * @property integer $events_options_row_size
 * @property integer $events_options_column_size
 * @property string $events_options_err_msg
 */
class EventsOptionsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventsOptionsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'events_options_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('events_options_id, language_id, events_options_max_size, events_options_row_size, events_options_column_size', 'numerical', 'integerOnly'=>true),
			array('events_options_title, events_options_note', 'length', 'max'=>255),
			array('events_options_name, events_options_err_msg', 'length', 'max'=>128),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('events_options_id, language_id, events_options_title, events_options_note, events_options_name, events_options_max_size, events_options_row_size, events_options_column_size, events_options_err_msg', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'events_options_id' => 'Events Options',
			'language_id' => 'Language',
			'events_options_title' => 'Events Options Title',
			'events_options_note' => 'Events Options Note',
			'events_options_name' => 'Events Options Name',
			'events_options_max_size' => 'Events Options Max Size',
			'events_options_row_size' => 'Events Options Row Size',
			'events_options_column_size' => 'Events Options Column Size',
			'events_options_err_msg' => 'Events Options Err Msg',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('events_options_id',$this->events_options_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('events_options_title',$this->events_options_title,true);
		$criteria->compare('events_options_note',$this->events_options_note,true);
		$criteria->compare('events_options_name',$this->events_options_name,true);
		$criteria->compare('events_options_max_size',$this->events_options_max_size);
		$criteria->compare('events_options_row_size',$this->events_options_row_size);
		$criteria->compare('events_options_column_size',$this->events_options_column_size);
		$criteria->compare('events_options_err_msg',$this->events_options_err_msg,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}