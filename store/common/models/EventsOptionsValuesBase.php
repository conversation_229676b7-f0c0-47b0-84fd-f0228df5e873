<?php

/**
 * This is the model class for table "events_options_values".
 *
 * The followings are the available columns in table 'events_options_values':
 * @property integer $events_options_values_id
 * @property integer $events_options_id
 * @property integer $language_id
 * @property string $events_options_values
 * @property string $events_options_values_sort_order
 */
class EventsOptionsValuesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventsOptionsValuesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'events_options_values';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('events_options_id, language_id', 'numerical', 'integerOnly'=>true),
			array('events_options_values, events_options_values_sort_order', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('events_options_values_id, events_options_id, language_id, events_options_values, events_options_values_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'events_options_values_id' => 'Events Options Values',
			'events_options_id' => 'Events Options',
			'language_id' => 'Language',
			'events_options_values' => 'Events Options Values',
			'events_options_values_sort_order' => 'Events Options Values Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('events_options_values_id',$this->events_options_values_id);
		$criteria->compare('events_options_id',$this->events_options_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('events_options_values',$this->events_options_values,true);
		$criteria->compare('events_options_values_sort_order',$this->events_options_values_sort_order,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}