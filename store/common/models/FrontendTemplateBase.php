<?php

/**
 * This is the model class for table "frontend_template".
 *
 * The followings are the available columns in table 'frontend_template':
 * @property string $tpl_id
 * @property string $id
 * @property integer $id_type
 * @property string $background_source
 * @property string $background_color
 * @property string $logo_source
 * @property integer $tpl_status
 */
class FrontendTemplateBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return FrontendTemplateBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'frontend_template';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id, id_type, background_source, background_color, logo_source', 'required'),
			array('id_type, tpl_status', 'numerical', 'integerOnly'=>true),
			array('id', 'length', 'max'=>11),
			array('background_source, logo_source', 'length', 'max'=>255),
			array('background_color', 'length', 'max'=>30),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tpl_id, id, id_type, background_source, background_color, logo_source, tpl_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tpl_id' => 'Tpl',
			'id' => 'ID',
			'id_type' => 'Id Type',
			'background_source' => 'Background Source',
			'background_color' => 'Background Color',
			'logo_source' => 'Logo Source',
            'tpl_status' => 'Tpl Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tpl_id',$this->tpl_id,true);
		$criteria->compare('id',$this->id,true);
		$criteria->compare('id_type',$this->id_type);
		$criteria->compare('background_source',$this->background_source,true);
		$criteria->compare('background_color',$this->background_color,true);
		$criteria->compare('logo_source',$this->logo_source,true);
        $criteria->compare('tpl_status',$this->tpl_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getRowByIDByType($id, $id_type) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = '*';
        $criteria->condition = 'id = :id AND id_type = :id_type';
		$criteria->params = array(
            ':id' => $id,
            ':id_type' => $id_type,
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
		return $return_array;
    }
}