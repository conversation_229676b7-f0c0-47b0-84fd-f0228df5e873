<?php

/**
 * This is the model class for table "frontend_template_lang".
 *
 * The followings are the available columns in table 'frontend_template_lang':
 * @property string $tpl_id
 * @property integer $language_id
 * @property string $description
 * @property string $notice
 * @property string $system_requirements
 * @property string $remark
 * @property string $gallery_info
 * @property string $game_title
 * @property string $game_publisher
 * @property string $game_developer
 * @property string $game_release_date
 */
class FrontendTemplateLangBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return FrontendTemplateLangBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'frontend_template_lang';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tpl_id, language_id, description, notice, system_requirements, remark, gallery_info, game_title, game_publisher, game_developer, game_release_date', 'required'),
			array('language_id', 'numerical', 'integerOnly'=>true),
			array('tpl_id', 'length', 'max'=>11),
			array('notice, gallery_info, game_title, game_publisher, game_developer, game_release_date', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tpl_id, language_id, description, notice, system_requirements, remark, gallery_info, game_title, game_publisher, game_developer, game_release_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tpl_id' => 'Tpl',
			'language_id' => 'Language',
			'description' => 'Description',
			'notice' => 'Notice',
			'system_requirements' => 'System Requirements',
			'remark' => 'Remark',
			'gallery_info' => 'Gallery Info',
			'game_title' => 'Game Title',
			'game_publisher' => 'Game Publisher',
			'game_developer' => 'Game Developer',
			'game_release_date' => 'Game Release Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tpl_id',$this->tpl_id,true);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('description',$this->description,true);
		$criteria->compare('notice',$this->notice,true);
		$criteria->compare('system_requirements',$this->system_requirements,true);
		$criteria->compare('remark',$this->remark,true);
		$criteria->compare('gallery_info',$this->gallery_info,true);
		$criteria->compare('game_title',$this->game_title,true);
		$criteria->compare('game_publisher',$this->game_publisher,true);
		$criteria->compare('game_developer',$this->game_developer,true);
		$criteria->compare('game_release_date',$this->game_release_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getRowByIDByLanguage($tpl_id, $language_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = '*';
        $criteria->condition = 'tpl_id = :tpl_id AND language_id = :language_id';
		$criteria->params = array(
            ':tpl_id' => $tpl_id,
            ':language_id' => $language_id,
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes();
        }
        
		return $return_array;
    }
}