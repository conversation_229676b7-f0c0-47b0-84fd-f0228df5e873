<?php

/**
 * This is the model class for table "frontend_template_to_game_info".
 *
 * The followings are the available columns in table 'frontend_template_to_game_info':
 * @property string $tpl_id
 * @property string $game_info_id
 * @property string $game_info_type
 */
class FrontendTemplateToGameInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return FrontendTemplateToGameInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'frontend_template_to_game_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tpl_id, game_info_id, game_info_type', 'required'),
			array('tpl_id, game_info_id', 'length', 'max'=>11),
			array('game_info_type', 'length', 'max'=>60),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tpl_id, game_info_id, game_info_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tpl_id' => 'Tpl',
			'game_info_id' => 'Game Info',
			'game_info_type' => 'Game Info Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tpl_id',$this->tpl_id,true);
		$criteria->compare('game_info_id',$this->game_info_id,true);
		$criteria->compare('game_info_type',$this->game_info_type,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getRowsByIDByLanguage($tpl_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = '*';
        $criteria->condition = 'tpl_id = :tpl_id';
		$criteria->params = array(
            ':tpl_id' => $tpl_id,
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->game_info_type][] = $result->game_info_id;
            }
        }
        
		return $return_array;
    }
}