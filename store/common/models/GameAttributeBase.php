<?php

/**
 * This is the model class for table "game_attribute".
 *
 * The followings are the available columns in table 'game_attribute':
 * @property string $game_attribute_id
 * @property string $game_id
 * @property string $key
 * @property string $disable_attributes
 * @property string $media
 */
class GameAttributeBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameAttributeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_attribute';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_id, key', 'required'),
			array('game_id', 'length', 'max'=>11),
			array('key', 'length', 'max'=>255),
			array('media', 'length', 'max'=>16),
			array('disable_attributes', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_attribute_id, game_id, key, disable_attributes, media', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_attribute_id' => 'Game Attribute',
			'game_id' => 'Game',
			'key' => 'Key',
			'disable_attributes' => 'Disable Attributes',
			'media' => 'Media',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_attribute_id',$this->game_attribute_id,true);
		$criteria->compare('game_id',$this->game_id,true);
		$criteria->compare('key',$this->key,true);
		$criteria->compare('disable_attributes',$this->disable_attributes,true);
		$criteria->compare('media',$this->media,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}