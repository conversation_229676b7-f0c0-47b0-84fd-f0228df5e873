<?php

/**
 * This is the model class for table "game_attribute_setting".
 *
 * The followings are the available columns in table 'game_attribute_setting':
 * @property string $game_attribute_setting_id
 * @property string $game_id
 * @property string $rule
 */
class GameAttributeSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameAttributeSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_attribute_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_id, rule', 'required'),
			array('game_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_attribute_setting_id, game_id, rule', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_attribute_setting_id' => 'Game Attribute Setting',
			'game_id' => 'Game',
			'rule' => 'Rule',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_attribute_setting_id',$this->game_attribute_setting_id,true);
		$criteria->compare('game_id',$this->game_id,true);
		$criteria->compare('rule',$this->rule,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}