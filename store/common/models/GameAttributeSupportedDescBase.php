<?php

/**
 * This is the model class for table "game_attribute_supported_desc".
 *
 * The followings are the available columns in table 'game_attribute_supported_desc':
 * @property string $game_attribute_supported_desc_id
 * @property string $game_attribute_supported_id
 * @property string $language_id
 * @property string $value
 */
class GameAttributeSupportedDescBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameAttributeSupportedDescBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_attribute_supported_desc';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_attribute_supported_id, language_id, value', 'required'),
			array('game_attribute_supported_id', 'length', 'max'=>11),
			array('language_id', 'length', 'max'=>3),
			array('value', 'length', 'max'=>128),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_attribute_supported_desc_id, game_attribute_supported_id, language_id, value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_attribute_supported_desc_id' => 'Game Attribute Supported Desc',
			'game_attribute_supported_id' => 'Game Attribute Supported',
			'language_id' => 'Language',
			'value' => 'Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_attribute_supported_desc_id',$this->game_attribute_supported_desc_id,true);
		$criteria->compare('game_attribute_supported_id',$this->game_attribute_supported_id,true);
		$criteria->compare('language_id',$this->language_id,true);
		$criteria->compare('value',$this->value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}