<?php

/**
 * This is the model class for table "game_blog".
 *
 * The followings are the available columns in table 'game_blog':
 * @property string $game_blog_id
 * @property integer $sort_order
 * @property string $custom_url
 */
class GameBlogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameBlogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_blog';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('sort_order', 'required'),
			array('sort_order', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_blog_id, sort_order, custom_url', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_blog_id' => 'Game Blog',
			'sort_order' => 'Sort Order',
			'custom_url' => 'Custom Url'
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_blog_id',$this->game_blog_id,true);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('custom_url',$this->custom_url);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function getIDByCustomUrl($custom_url) {
		$return_int = 0;
		$criteria = new CDbCriteria;
		$criteria->select = 'game_blog_id';
		$criteria->condition = 'custom_url =:custom_url';
		$criteria->params = array(
			':custom_url' => $custom_url
		);
		if ($result = $this->model()->find($criteria)) {
			$return_int = $result->game_blog_id;
		}
		return $return_int;
	}

	public function getCustomUrl($game_blog_id) {
        $return_str = '';
		$sql = "SELECT custom_url
				FROM " . $this->tableName() . " 
				WHERE game_blog_id = :game_blog_id
				AND custom_url <> ''";
		
		$command = $this->conn->createCommand($sql);
		$command->bindParam(":game_blog_id", $game_blog_id, PDO::PARAM_INT);
		if ($value = $command->queryScalar()) {
			$return_str = $value;
		}
		return $return_str;
	}
}