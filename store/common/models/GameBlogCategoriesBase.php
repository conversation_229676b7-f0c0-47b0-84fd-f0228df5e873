<?php

/**
 * This is the model class for table "game_blog_categories".
 *
 * The followings are the available columns in table 'game_blog_categories':
 * @property string $game_blog_id
 * @property string $categories_id
 */
class GameBlogCategoriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameBlogCategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_blog_categories';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_blog_id, categories_id', 'required'),
			array('game_blog_id, categories_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_blog_id, categories_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'GameBlogBase' => array(self::HAS_ONE, 'GameBlogBase', 'game_blog_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_blog_id' => 'Game Blog',
			'categories_id' => 'Categories',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_blog_id',$this->game_blog_id,true);
		$criteria->compare('categories_id',$this->categories_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getAllCategoryByGamesBlogID($game_blog_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
        $criteria->with=array(
            'GameBlogBase' => array('select' => false)
        );
		$criteria->select = 'categories_id';
        $criteria->condition = 'GameBlogBase.game_blog_id = :game_blog_id';
		$criteria->params = array(
            ':game_blog_id' => $game_blog_id,
        );
        $criteria->order = 'sort_order';
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->categories_id;
            }
        }

		return $return_array;
    }
    
    public function getAllGamesByCategoryID($category_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
        $criteria->with=array(
            'GameBlogBase' => array('select' => false)  //, 'together' => false
        );
		$criteria->select = 'game_blog_id';
        $criteria->condition = 'categories_id = :categories_id';
		$criteria->params = array(
            ':categories_id' => $category_id,
        );
        $criteria->order = 'sort_order';
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->game_blog_id;
            }
        }
        
		return $return_array;
    }
}