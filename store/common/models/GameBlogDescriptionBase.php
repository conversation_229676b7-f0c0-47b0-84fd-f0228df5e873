<?php

/**
 * This is the model class for table "game_blog_description".
 *
 * The followings are the available columns in table 'game_blog_description':
 * @property string $game_blog_id
 * @property integer $language_id
 * @property string $game_blog_description
 */
class GameBlogDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameBlogDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_blog_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_blog_id, language_id, game_blog_description', 'required'),
			array('language_id', 'numerical', 'integerOnly'=>true),
			array('game_blog_id', 'length', 'max'=>11),
			array('game_blog_description', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_blog_id, language_id, game_blog_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_blog_id' => 'Game Blog',
			'language_id' => 'Language',
			'game_blog_description' => 'Game Blog Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_blog_id',$this->game_blog_id,true);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('game_blog_description',$this->game_blog_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getIDByDescription($game_blog_desc) {
        $return_int = 0;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'game_blog_id';
		$criteria->condition = 'game_blog_description =:game_blog_description';
		$criteria->params = array(
            ':game_blog_description' => $game_blog_desc
        );
		if ($result = $this->model()->find($criteria)) {
            $return_int = $result->game_blog_id;
        }
        
		return $return_int;
    }
    
    public function getDescription($game_blog_id, $language_id, $default_language_id) {
        $return_str = '';

        $sql = "SELECT game_blog_description
                    FROM " . $this->tableName() . " 
                    WHERE game_blog_id = :game_blog_id
                        AND game_blog_description <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(game_blog_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE game_blog_id = :game_blog_id 
                                AND language_id = :language_id
                                AND game_blog_description <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":game_blog_id", $game_blog_id, PDO::PARAM_INT);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);

        if ($value = $command->queryScalar()) {
           $return_str = $value;
        }         

        return $return_str;                              
    }
}