<?php

/**
 * This is the model class for table "game_char".
 *
 * The followings are the available columns in table 'game_char':
 * @property integer $game_char_id
 * @property integer $orders_products_id
 * @property string $name
 * @property string $server
 */
class GameCharBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameCharBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_char';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_id', 'numerical', 'integerOnly'=>true),
			array('name', 'length', 'max'=>64),
			array('server', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_char_id, orders_products_id, name, server', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_char_id' => 'Game Char',
			'orders_products_id' => 'Orders Products',
			'name' => 'Name',
			'server' => 'Server',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_char_id',$this->game_char_id);
		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('server',$this->server,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}