<?php

/**
 * This is the model class for table "game_char_history".
 *
 * The followings are the available columns in table 'game_char_history':
 * @property integer $game_char_history_id
 * @property integer $game_char_id
 * @property string $stat_int
 * @property string $stat_agl
 * @property string $stat_sta
 * @property string $stat_str
 * @property string $stat_spr
 * @property string $guild_name
 * @property string $guild_title
 * @property string $guild_rank
 * @property string $race
 * @property string $res_holy
 * @property string $res_frost
 * @property string $res_arcane
 * @property string $res_fire
 * @property string $res_shadow
 * @property string $res_nature
 * @property string $armor
 * @property integer $level
 * @property integer $defense
 * @property integer $talent_points
 * @property integer $money_c
 * @property integer $money_s
 * @property integer $money_g
 * @property string $exp
 * @property string $class
 * @property integer $health
 * @property integer $melee_power
 * @property integer $melee_rating
 * @property string $melee_range
 * @property string $melee_range_tooltip
 * @property string $melee_power_tooltip
 * @property integer $ranged_power
 * @property integer $ranged_rating
 * @property string $ranged_range
 * @property string $ranged_range_tooltip
 * @property string $ranged_power_tooltip
 * @property string $version
 * @property string $game_char_history_date
 * @property string $game_char_lua_contents
 */
class GameCharHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameCharHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_char_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_char_id, level, defense, talent_points, money_c, money_s, money_g, health, melee_power, melee_rating, ranged_power, ranged_rating', 'numerical', 'integerOnly'=>true),
			array('stat_int, stat_agl, stat_sta, stat_str, stat_spr, race, res_holy, res_frost, res_arcane, res_fire, res_shadow, res_nature, armor, exp, class', 'length', 'max'=>32),
			array('guild_name, guild_title', 'length', 'max'=>64),
			array('guild_rank', 'length', 'max'=>11),
			array('melee_range, ranged_range, version', 'length', 'max'=>16),
			array('melee_range_tooltip, melee_power_tooltip, ranged_range_tooltip, ranged_power_tooltip, game_char_history_date, game_char_lua_contents', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_char_history_id, game_char_id, stat_int, stat_agl, stat_sta, stat_str, stat_spr, guild_name, guild_title, guild_rank, race, res_holy, res_frost, res_arcane, res_fire, res_shadow, res_nature, armor, level, defense, talent_points, money_c, money_s, money_g, exp, class, health, melee_power, melee_rating, melee_range, melee_range_tooltip, melee_power_tooltip, ranged_power, ranged_rating, ranged_range, ranged_range_tooltip, ranged_power_tooltip, version, game_char_history_date, game_char_lua_contents', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_char_history_id' => 'Game Char History',
			'game_char_id' => 'Game Char',
			'stat_int' => 'Stat Int',
			'stat_agl' => 'Stat Agl',
			'stat_sta' => 'Stat Sta',
			'stat_str' => 'Stat Str',
			'stat_spr' => 'Stat Spr',
			'guild_name' => 'Guild Name',
			'guild_title' => 'Guild Title',
			'guild_rank' => 'Guild Rank',
			'race' => 'Race',
			'res_holy' => 'Res Holy',
			'res_frost' => 'Res Frost',
			'res_arcane' => 'Res Arcane',
			'res_fire' => 'Res Fire',
			'res_shadow' => 'Res Shadow',
			'res_nature' => 'Res Nature',
			'armor' => 'Armor',
			'level' => 'Level',
			'defense' => 'Defense',
			'talent_points' => 'Talent Points',
			'money_c' => 'Money C',
			'money_s' => 'Money S',
			'money_g' => 'Money G',
			'exp' => 'Exp',
			'class' => 'Class',
			'health' => 'Health',
			'melee_power' => 'Melee Power',
			'melee_rating' => 'Melee Rating',
			'melee_range' => 'Melee Range',
			'melee_range_tooltip' => 'Melee Range Tooltip',
			'melee_power_tooltip' => 'Melee Power Tooltip',
			'ranged_power' => 'Ranged Power',
			'ranged_rating' => 'Ranged Rating',
			'ranged_range' => 'Ranged Range',
			'ranged_range_tooltip' => 'Ranged Range Tooltip',
			'ranged_power_tooltip' => 'Ranged Power Tooltip',
			'version' => 'Version',
			'game_char_history_date' => 'Game Char History Date',
			'game_char_lua_contents' => 'Game Char Lua Contents',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_char_history_id',$this->game_char_history_id);
		$criteria->compare('game_char_id',$this->game_char_id);
		$criteria->compare('stat_int',$this->stat_int,true);
		$criteria->compare('stat_agl',$this->stat_agl,true);
		$criteria->compare('stat_sta',$this->stat_sta,true);
		$criteria->compare('stat_str',$this->stat_str,true);
		$criteria->compare('stat_spr',$this->stat_spr,true);
		$criteria->compare('guild_name',$this->guild_name,true);
		$criteria->compare('guild_title',$this->guild_title,true);
		$criteria->compare('guild_rank',$this->guild_rank,true);
		$criteria->compare('race',$this->race,true);
		$criteria->compare('res_holy',$this->res_holy,true);
		$criteria->compare('res_frost',$this->res_frost,true);
		$criteria->compare('res_arcane',$this->res_arcane,true);
		$criteria->compare('res_fire',$this->res_fire,true);
		$criteria->compare('res_shadow',$this->res_shadow,true);
		$criteria->compare('res_nature',$this->res_nature,true);
		$criteria->compare('armor',$this->armor,true);
		$criteria->compare('level',$this->level);
		$criteria->compare('defense',$this->defense);
		$criteria->compare('talent_points',$this->talent_points);
		$criteria->compare('money_c',$this->money_c);
		$criteria->compare('money_s',$this->money_s);
		$criteria->compare('money_g',$this->money_g);
		$criteria->compare('exp',$this->exp,true);
		$criteria->compare('class',$this->class,true);
		$criteria->compare('health',$this->health);
		$criteria->compare('melee_power',$this->melee_power);
		$criteria->compare('melee_rating',$this->melee_rating);
		$criteria->compare('melee_range',$this->melee_range,true);
		$criteria->compare('melee_range_tooltip',$this->melee_range_tooltip,true);
		$criteria->compare('melee_power_tooltip',$this->melee_power_tooltip,true);
		$criteria->compare('ranged_power',$this->ranged_power);
		$criteria->compare('ranged_rating',$this->ranged_rating);
		$criteria->compare('ranged_range',$this->ranged_range,true);
		$criteria->compare('ranged_range_tooltip',$this->ranged_range_tooltip,true);
		$criteria->compare('ranged_power_tooltip',$this->ranged_power_tooltip,true);
		$criteria->compare('version',$this->version,true);
		$criteria->compare('game_char_history_date',$this->game_char_history_date,true);
		$criteria->compare('game_char_lua_contents',$this->game_char_lua_contents,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}