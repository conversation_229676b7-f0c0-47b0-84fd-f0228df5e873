<?php

/**
 * This is the model class for table "game_char_log".
 *
 * The followings are the available columns in table 'game_char_log':
 * @property integer $game_char_log_id
 * @property integer $game_char_log_orders_id
 * @property string $game_char_log_time
 * @property string $game_char_log_account_name
 * @property string $game_char_log_server
 * @property string $game_char_log_realm
 * @property string $game_char_log_race
 * @property string $game_char_log_sender
 * @property string $game_char_log_receiver
 * @property string $game_char_log_subject
 * @property string $game_char_log_messages
 * @property string $game_char_log_balance_before
 * @property string $game_char_log_balance_after
 * @property string $game_char_log_system_messages
 * @property string $game_char_log_send
 * @property string $game_char_log_receive
 * @property string $game_char_log_type
 * @property string $game_char_log_login_as
 * @property string $game_char_log_computer_name
 * @property string $game_char_log_login_user
 * @property string $game_char_log_user_role
 */
class GameCharLogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameCharLogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_char_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_char_log_orders_id', 'numerical', 'integerOnly'=>true),
			array('game_char_log_account_name, game_char_log_race, game_char_log_sender, game_char_log_receiver, game_char_log_computer_name', 'length', 'max'=>64),
			array('game_char_log_server, game_char_log_realm, game_char_log_login_as', 'length', 'max'=>32),
			array('game_char_log_subject, game_char_log_balance_before, game_char_log_balance_after, game_char_log_send, game_char_log_receive', 'length', 'max'=>255),
			array('game_char_log_type, game_char_log_user_role', 'length', 'max'=>16),
			array('game_char_log_login_user', 'length', 'max'=>96),
			array('game_char_log_time, game_char_log_messages, game_char_log_system_messages', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_char_log_id, game_char_log_orders_id, game_char_log_time, game_char_log_account_name, game_char_log_server, game_char_log_realm, game_char_log_race, game_char_log_sender, game_char_log_receiver, game_char_log_subject, game_char_log_messages, game_char_log_balance_before, game_char_log_balance_after, game_char_log_system_messages, game_char_log_send, game_char_log_receive, game_char_log_type, game_char_log_login_as, game_char_log_computer_name, game_char_log_login_user, game_char_log_user_role', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_char_log_id' => 'Game Char Log',
			'game_char_log_orders_id' => 'Game Char Log Orders',
			'game_char_log_time' => 'Game Char Log Time',
			'game_char_log_account_name' => 'Game Char Log Account Name',
			'game_char_log_server' => 'Game Char Log Server',
			'game_char_log_realm' => 'Game Char Log Realm',
			'game_char_log_race' => 'Game Char Log Race',
			'game_char_log_sender' => 'Game Char Log Sender',
			'game_char_log_receiver' => 'Game Char Log Receiver',
			'game_char_log_subject' => 'Game Char Log Subject',
			'game_char_log_messages' => 'Game Char Log Messages',
			'game_char_log_balance_before' => 'Game Char Log Balance Before',
			'game_char_log_balance_after' => 'Game Char Log Balance After',
			'game_char_log_system_messages' => 'Game Char Log System Messages',
			'game_char_log_send' => 'Game Char Log Send',
			'game_char_log_receive' => 'Game Char Log Receive',
			'game_char_log_type' => 'Game Char Log Type',
			'game_char_log_login_as' => 'Game Char Log Login As',
			'game_char_log_computer_name' => 'Game Char Log Computer Name',
			'game_char_log_login_user' => 'Game Char Log Login User',
			'game_char_log_user_role' => 'Game Char Log User Role',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_char_log_id',$this->game_char_log_id);
		$criteria->compare('game_char_log_orders_id',$this->game_char_log_orders_id);
		$criteria->compare('game_char_log_time',$this->game_char_log_time,true);
		$criteria->compare('game_char_log_account_name',$this->game_char_log_account_name,true);
		$criteria->compare('game_char_log_server',$this->game_char_log_server,true);
		$criteria->compare('game_char_log_realm',$this->game_char_log_realm,true);
		$criteria->compare('game_char_log_race',$this->game_char_log_race,true);
		$criteria->compare('game_char_log_sender',$this->game_char_log_sender,true);
		$criteria->compare('game_char_log_receiver',$this->game_char_log_receiver,true);
		$criteria->compare('game_char_log_subject',$this->game_char_log_subject,true);
		$criteria->compare('game_char_log_messages',$this->game_char_log_messages,true);
		$criteria->compare('game_char_log_balance_before',$this->game_char_log_balance_before,true);
		$criteria->compare('game_char_log_balance_after',$this->game_char_log_balance_after,true);
		$criteria->compare('game_char_log_system_messages',$this->game_char_log_system_messages,true);
		$criteria->compare('game_char_log_send',$this->game_char_log_send,true);
		$criteria->compare('game_char_log_receive',$this->game_char_log_receive,true);
		$criteria->compare('game_char_log_type',$this->game_char_log_type,true);
		$criteria->compare('game_char_log_login_as',$this->game_char_log_login_as,true);
		$criteria->compare('game_char_log_computer_name',$this->game_char_log_computer_name,true);
		$criteria->compare('game_char_log_login_user',$this->game_char_log_login_user,true);
		$criteria->compare('game_char_log_user_role',$this->game_char_log_user_role,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}