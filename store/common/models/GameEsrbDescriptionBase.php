<?php

/**
 * This is the model class for table "game_esrb_description".
 *
 * The followings are the available columns in table 'game_esrb_description':
 * @property integer $game_esrb_id
 * @property integer $language_id
 * @property string $game_esrb_description
 * @property string $note
 */
class GameEsrbDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameEsrbDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_esrb_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_esrb_id, language_id', 'numerical', 'integerOnly'=>true),
			array('game_esrb_description', 'length', 'max'=>5),
			array('note', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_esrb_id, language_id, game_esrb_description, note', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_esrb_id' => 'Game Esrb',
			'language_id' => 'Language',
			'game_esrb_description' => 'Game Esrb Description',
			'note' => 'Note',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_esrb_id',$this->game_esrb_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('game_esrb_description',$this->game_esrb_description,true);
		$criteria->compare('note',$this->note,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}