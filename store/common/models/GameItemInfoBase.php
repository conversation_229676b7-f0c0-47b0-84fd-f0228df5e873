<?php

/**
 * This is the model class for table "game_item_info".
 *
 * The followings are the available columns in table 'game_item_info':
 * @property integer $game_item_info_id
 * @property string $game_item_info_name
 * @property string $game_item_info_color
 * @property string $game_item_info_tooltip
 * @property string $game_item_info_item
 * @property string $game_item_info_texture
 */
class GameItemInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameItemInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_item_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_item_info_tooltip', 'required'),
			array('game_item_info_name, game_item_info_texture', 'length', 'max'=>64),
			array('game_item_info_color, game_item_info_item', 'length', 'max'=>16),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_item_info_id, game_item_info_name, game_item_info_color, game_item_info_tooltip, game_item_info_item, game_item_info_texture', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_item_info_id' => 'Game Item Info',
			'game_item_info_name' => 'Game Item Info Name',
			'game_item_info_color' => 'Game Item Info Color',
			'game_item_info_tooltip' => 'Game Item Info Tooltip',
			'game_item_info_item' => 'Game Item Info Item',
			'game_item_info_texture' => 'Game Item Info Texture',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_item_info_id',$this->game_item_info_id);
		$criteria->compare('game_item_info_name',$this->game_item_info_name,true);
		$criteria->compare('game_item_info_color',$this->game_item_info_color,true);
		$criteria->compare('game_item_info_tooltip',$this->game_item_info_tooltip,true);
		$criteria->compare('game_item_info_item',$this->game_item_info_item,true);
		$criteria->compare('game_item_info_texture',$this->game_item_info_texture,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}