<?php

/**
 * This is the model class for table "game_language_description".
 *
 * The followings are the available columns in table 'game_language_description':
 * @property integer $game_language_id
 * @property integer $language_id
 * @property string $game_language_description
 */
class GameLanguageDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameLanguageDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_language_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_language_id, language_id', 'numerical', 'integerOnly'=>true),
			array('game_language_description', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_language_id, language_id, game_language_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_language_id' => 'Game Language',
			'language_id' => 'Language',
			'game_language_description' => 'Game Language Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_language_id',$this->game_language_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('game_language_description',$this->game_language_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getLanguageDescription($id, $language_id = 0, $default_language_id = 0) {
        $return_str = '';
        
        $sql = "	SELECT game_language_description 
                    FROM " . $this->tableName() . " 
                    WHERE game_language_id = :game_language_id
                        AND game_language_description <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(game_language_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE game_language_id = :game_language_id 
                                AND language_id = :language_id
                                AND game_language_description <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":game_language_id", $id, PDO::PARAM_STR);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}