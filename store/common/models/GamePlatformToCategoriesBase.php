<?php

/**
 * This is the model class for table "game_platform_to_categories".
 *
 * The followings are the available columns in table 'game_platform_to_categories':
 * @property integer $game_platform_id
 * @property integer $categories_id
 */
class GamePlatformToCategoriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GamePlatformToCategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_platform_to_categories';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_platform_id, categories_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_platform_id, categories_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_platform_id' => 'Game Platform',
			'categories_id' => 'Categories',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_platform_id',$this->game_platform_id);
		$criteria->compare('categories_id',$this->categories_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
	public function getCategoriesIDByPlatformID($platform_id) {
		$return_array = array();
		$return_array2 = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id';
        $criteria->condition = 'game_platform_id = :game_platform_id';
		$criteria->params = array(
            ':game_platform_id' => $platform_id
        );
        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->categories_id;
            }
        }
        
		return $return_array;
	}
}