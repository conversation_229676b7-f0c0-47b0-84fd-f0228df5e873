<?php

/**
 * This is the model class for table "game_region_to_language".
 *
 * The followings are the available columns in table 'game_region_to_language':
 * @property integer $game_region_to_language_id
 * @property integer $game_region_id
 * @property integer $game_language_id
 * @property string $default_setting
 */
class GameRegionToLanguageBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GameRegionToLanguageBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'game_region_to_language';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('game_region_id, game_language_id', 'numerical', 'integerOnly'=>true),
			array('default_setting', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('game_region_to_language_id, game_region_id, game_language_id, default_setting', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'game_region_to_language_id' => 'Game Region To Language',
			'game_region_id' => 'Game Region',
			'game_language_id' => 'Game Language',
			'default_setting' => 'Default Setting',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('game_region_to_language_id',$this->game_region_to_language_id);
		$criteria->compare('game_region_id',$this->game_region_id);
		$criteria->compare('game_language_id',$this->game_language_id);
		$criteria->compare('default_setting',$this->default_setting,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}