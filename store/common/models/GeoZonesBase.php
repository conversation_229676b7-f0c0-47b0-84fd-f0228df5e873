<?php

/**
 * This is the model class for table "geo_zones".
 *
 * The followings are the available columns in table 'geo_zones':
 * @property integer $geo_zone_id
 * @property string $geo_zone_name
 * @property string $geo_zone_description
 * @property integer $geo_zone_type
 * @property string $last_modified
 * @property string $date_added
 */
class GeoZonesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GeoZonesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'geo_zones';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('geo_zone_type', 'numerical', 'integerOnly'=>true),
			array('geo_zone_name', 'length', 'max'=>32),
			array('geo_zone_description', 'length', 'max'=>255),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('geo_zone_id, geo_zone_name, geo_zone_description, geo_zone_type, last_modified, date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
             'ZonesToGeoZonesBase' => array(self::HAS_MANY, 'ZonesToGeoZonesBase', 'geo_zone_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'geo_zone_id' => 'Geo Zone',
			'geo_zone_name' => 'Geo Zone Name',
			'geo_zone_description' => 'Geo Zone Description',
			'geo_zone_type' => 'Geo Zone Type',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('geo_zone_id',$this->geo_zone_id);
		$criteria->compare('geo_zone_name',$this->geo_zone_name,true);
		$criteria->compare('geo_zone_description',$this->geo_zone_description,true);
		$criteria->compare('geo_zone_type',$this->geo_zone_type);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getZoneID($country_id, $zone_type) {
        $return_int = NULL;
        
        $criteria = new CDbCriteria;
        $criteria->with=array('ZonesToGeoZonesBase');
		$criteria->select = 'geo_zone_id';
		$criteria->condition = 'geo_zone_type=:geo_zone_type AND ZonesToGeoZonesBase.zone_country_id=:zone_country_id';
		$criteria->params = array(
            ':geo_zone_type' => $zone_type,
            ':zone_country_id' => $country_id
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_int = $result->geo_zone_id;
        }
        
		return $return_int;
    }
}