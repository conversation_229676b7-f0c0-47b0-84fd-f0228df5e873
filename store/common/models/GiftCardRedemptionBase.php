<?php

/**
 * This is the model class for table "gift_card_redemption".
 *
 * The followings are the available columns in table 'gift_card_redemption':
 * @property string $gift_card_redemption_id
 * @property string $customers_id
 * @property string $transaction_id
 * @property string $serial_number
 * @property string $pin_number
 * @property string $gift_card_deno
 * @property string $gift_card_redeem_amount
 * @property string $gift_card_currency_id
 * @property string $gift_card_currency_code
 * @property string $transaction_type
 * @property string $redeem_date
 * @property string $redeem_ip
 * @property string $issued_amount
 * @property string $issued_currency_id
 * @property string $issued_currency_code
 */
class GiftCardRedemptionBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return GiftCardRedemptionBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'gift_card_redemption';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('customers_id, transaction_id, serial_number, pin_number, gift_card_redeem_amount, gift_card_currency_code, redeem_date, issued_amount, issued_currency_code', 'required'),
            array('customers_id, gift_card_currency_id, issued_currency_id', 'length', 'max'=>11),
            array('serial_number', 'length', 'max'=>18),
            array('pin_number', 'length', 'max'=>16),
            array('gift_card_deno, gift_card_redeem_amount, issued_amount', 'length', 'max'=>15),
            array('gift_card_currency_code, issued_currency_code', 'length', 'max'=>3),
            array('transaction_type', 'length', 'max'=>10),
            array('transaction_id', 'length', 'max'=>32),
            array('redeem_ip', 'length', 'max'=>128),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('gift_card_redemption_id, customers_id, transaction_id, serial_number, pin_number, gift_card_deno, gift_card_redeem_amount, gift_card_currency_id, gift_card_currency_code, transaction_type, redeem_date, redeem_ip, issued_amount, issued_currency_id, issued_currency_code', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'gift_card_redemption_id' => 'Gift Card Redemption',
            'customers_id' => 'Customers',
            'transaction_id' => 'Transaction',
            'serial_number' => 'Serial Number',
            'pin_number' => 'Pin Number',
            'gift_card_deno' => 'Gift Card Deno',
            'gift_card_redeem_amount' => 'Gift Card Redeem Amount',
            'gift_card_currency_id' => 'Gift Card Currency',
            'gift_card_currency_code' => 'Gift Card Currency Code',
            'transaction_type' => 'Transaction Type',
            'redeem_date' => 'Redeem Date',
            'redeem_ip' => 'Redeem Ip',
            'issued_amount' => 'Issued Amount',
            'issued_currency_id' => 'Issued Currency',
            'issued_currency_code' => 'Issued Currency Code',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('gift_card_redemption_id', $this->gift_card_redemption_id, true);
        $criteria->compare('customers_id', $this->customers_id, true);
        $criteria->compare('transaction_id', $this->transaction_id, true);
        $criteria->compare('serial_number', $this->serial_number, true);
        $criteria->compare('pin_number', $this->pin_number, true);
        $criteria->compare('gift_card_deno', $this->gift_card_deno, true);
        $criteria->compare('gift_card_redeem_amount', $this->gift_card_redeem_amount, true);
        $criteria->compare('gift_card_currency_id', $this->gift_card_currency_id, true);
        $criteria->compare('gift_card_currency_code', $this->gift_card_currency_code, true);
        $criteria->compare('transaction_type', $this->transaction_type, true);
        $criteria->compare('redeem_date', $this->redeem_date, true);
        $criteria->compare('redeem_ip', $this->redeem_ip, true);
        $criteria->compare('issued_amount', $this->issued_amount, true);
        $criteria->compare('issued_currency_id', $this->issued_currency_id, true);
        $criteria->compare('issued_currency_code', $this->issued_currency_code, true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    public function updateGiftCardRedemption($giftCardRedemptionId, $paramArray)
    {
        $sql = "UPDATE " . $this->tableName() . "
                    SET transaction_id = :transaction_id,
                        issued_amount = :issued_amount,
                        issued_currency_id = :issued_currency_id,
                        issued_currency_code = :issued_currency_code
                WHERE gift_card_redemption_id = :gift_card_redemption_id";
        
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":gift_card_redemption_id", $giftCardRedemptionId, PDO::PARAM_INT);
        $command->bindParam(":transaction_id", $paramArray['transaction_id'], PDO::PARAM_STR);
        $command->bindParam(":issued_amount", $paramArray['issued_amount'], PDO::PARAM_STR);
        $command->bindParam(":issued_currency_id", $paramArray['issued_currency_id'], PDO::PARAM_INT);
        $command->bindParam(":issued_currency_code", $paramArray['issued_currency_code'], PDO::PARAM_STR);

        return $command->execute();
    }

    public function verifyGiftCardRedemption($serial)
    {
        $return_array = array();

        $sql = " SELECT serial_number
    			FROM " . $this->tableName() . "
    			WHERE serial_number = :serial_number
    				AND transaction_id IS NOT NULL
    			LIMIT 1";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":serial_number", $serial, PDO::PARAM_STR);

        if (!$dataset = $command->queryAll()) {
            $sql_data = " SELECT *
    					FROM " . $this->tableName() . "
    					WHERE serial_number = :serial_number
    					ORDER BY gift_card_redemption_id DESC
    					LIMIT 1";

            $command_data = $this->conn->createCommand($sql_data);
            $command_data->bindParam(":serial_number", $serial, PDO::PARAM_STR);

            if ($dataset_data = $command_data->queryRow()) {
                $return_array = $dataset_data;
            }
        }

        return $return_array;
    }
}
