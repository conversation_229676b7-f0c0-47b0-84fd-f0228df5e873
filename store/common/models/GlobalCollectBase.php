<?php

/**
 * This is the model class for table "global_collect".
 *
 * The followings are the available columns in table 'global_collect':
 * @property integer $global_collect_orders_id
 * @property string $global_collect_currency_code
 * @property double $global_collect_amount
 * @property integer $global_collect_status_id
 * @property string $global_collect_status_date
 * @property string $global_collect_payment_reference
 * @property string $global_collect_additional_reference
 * @property string $global_collect_effortid
 * @property string $global_collect_attemptid
 * @property string $global_collect_paymentproductid
 * @property string $global_collect_paymentmethodid
 * @property string $global_collect_receiveddate
 * @property string $global_collect_cvv_result
 * @property string $global_collect_avs_result
 * @property string $global_collect_fraud_result
 * @property string $global_collect_fraud_code
 * @property integer $global_collect_capture_request
 * @property string $global_collect_request_id
 * @property string $global_collect_request_time
 * @property string $global_collect_cc_last_4_digit
 * @property string $global_collect_cc_expiry_date
 * @property integer $global_collect_refund_request
 * @property string $global_collect_eci
 */
class GlobalCollectBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GlobalCollectBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'global_collect';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('global_collect_orders_id, global_collect_status_id, global_collect_capture_request, global_collect_refund_request', 'numerical', 'integerOnly'=>true),
			array('global_collect_amount', 'numerical'),
			array('global_collect_currency_code', 'length', 'max'=>3),
			array('global_collect_status_date, global_collect_payment_reference, global_collect_additional_reference, global_collect_request_time', 'length', 'max'=>20),
			array('global_collect_effortid, global_collect_attemptid, global_collect_paymentproductid, global_collect_paymentmethodid', 'length', 'max'=>8),
			array('global_collect_receiveddate', 'length', 'max'=>14),
			array('global_collect_cvv_result, global_collect_avs_result, global_collect_fraud_result, global_collect_eci', 'length', 'max'=>1),
			array('global_collect_fraud_code, global_collect_cc_last_4_digit, global_collect_cc_expiry_date', 'length', 'max'=>4),
			array('global_collect_request_id', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('global_collect_orders_id, global_collect_currency_code, global_collect_amount, global_collect_status_id, global_collect_status_date, global_collect_payment_reference, global_collect_additional_reference, global_collect_effortid, global_collect_attemptid, global_collect_paymentproductid, global_collect_paymentmethodid, global_collect_receiveddate, global_collect_cvv_result, global_collect_avs_result, global_collect_fraud_result, global_collect_fraud_code, global_collect_capture_request, global_collect_request_id, global_collect_request_time, global_collect_cc_last_4_digit, global_collect_cc_expiry_date, global_collect_refund_request, global_collect_eci', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'global_collect_orders_id' => 'Global Collect Orders',
			'global_collect_currency_code' => 'Global Collect Currency Code',
			'global_collect_amount' => 'Global Collect Amount',
			'global_collect_status_id' => 'Global Collect Status',
			'global_collect_status_date' => 'Global Collect Status Date',
			'global_collect_payment_reference' => 'Global Collect Payment Reference',
			'global_collect_additional_reference' => 'Global Collect Additional Reference',
			'global_collect_effortid' => 'Global Collect Effortid',
			'global_collect_attemptid' => 'Global Collect Attemptid',
			'global_collect_paymentproductid' => 'Global Collect Paymentproductid',
			'global_collect_paymentmethodid' => 'Global Collect Paymentmethodid',
			'global_collect_receiveddate' => 'Global Collect Receiveddate',
			'global_collect_cvv_result' => 'Global Collect Cvv Result',
			'global_collect_avs_result' => 'Global Collect Avs Result',
			'global_collect_fraud_result' => 'Global Collect Fraud Result',
			'global_collect_fraud_code' => 'Global Collect Fraud Code',
			'global_collect_capture_request' => 'Global Collect Capture Request',
			'global_collect_request_id' => 'Global Collect Request',
			'global_collect_request_time' => 'Global Collect Request Time',
			'global_collect_cc_last_4_digit' => 'Global Collect Cc Last 4 Digit',
			'global_collect_cc_expiry_date' => 'Global Collect Cc Expiry Date',
			'global_collect_refund_request' => 'Global Collect Refund Request',
			'global_collect_eci' => 'Global Collect Eci',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('global_collect_orders_id',$this->global_collect_orders_id);
		$criteria->compare('global_collect_currency_code',$this->global_collect_currency_code,true);
		$criteria->compare('global_collect_amount',$this->global_collect_amount);
		$criteria->compare('global_collect_status_id',$this->global_collect_status_id);
		$criteria->compare('global_collect_status_date',$this->global_collect_status_date,true);
		$criteria->compare('global_collect_payment_reference',$this->global_collect_payment_reference,true);
		$criteria->compare('global_collect_additional_reference',$this->global_collect_additional_reference,true);
		$criteria->compare('global_collect_effortid',$this->global_collect_effortid,true);
		$criteria->compare('global_collect_attemptid',$this->global_collect_attemptid,true);
		$criteria->compare('global_collect_paymentproductid',$this->global_collect_paymentproductid,true);
		$criteria->compare('global_collect_paymentmethodid',$this->global_collect_paymentmethodid,true);
		$criteria->compare('global_collect_receiveddate',$this->global_collect_receiveddate,true);
		$criteria->compare('global_collect_cvv_result',$this->global_collect_cvv_result,true);
		$criteria->compare('global_collect_avs_result',$this->global_collect_avs_result,true);
		$criteria->compare('global_collect_fraud_result',$this->global_collect_fraud_result,true);
		$criteria->compare('global_collect_fraud_code',$this->global_collect_fraud_code,true);
		$criteria->compare('global_collect_capture_request',$this->global_collect_capture_request);
		$criteria->compare('global_collect_request_id',$this->global_collect_request_id,true);
		$criteria->compare('global_collect_request_time',$this->global_collect_request_time,true);
		$criteria->compare('global_collect_cc_last_4_digit',$this->global_collect_cc_last_4_digit,true);
		$criteria->compare('global_collect_cc_expiry_date',$this->global_collect_cc_expiry_date,true);
		$criteria->compare('global_collect_refund_request',$this->global_collect_refund_request);
		$criteria->compare('global_collect_eci',$this->global_collect_eci,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}