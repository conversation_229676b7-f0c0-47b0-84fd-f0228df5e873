<?php

/**
 * This is the model class for table "global_collect_status_history".
 *
 * The followings are the available columns in table 'global_collect_status_history':
 * @property integer $global_collect_status_history_id
 * @property integer $global_collect_orders_id
 * @property string $global_collect_date
 * @property integer $global_collect_status
 * @property string $global_collect_description
 * @property string $changed_by
 */
class GlobalCollectStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GlobalCollectStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'global_collect_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('global_collect_orders_id, global_collect_status', 'numerical', 'integerOnly'=>true),
			array('global_collect_description', 'length', 'max'=>255),
			array('changed_by', 'length', 'max'=>128),
			array('global_collect_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('global_collect_status_history_id, global_collect_orders_id, global_collect_date, global_collect_status, global_collect_description, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'global_collect_status_history_id' => 'Global Collect Status History',
			'global_collect_orders_id' => 'Global Collect Orders',
			'global_collect_date' => 'Global Collect Date',
			'global_collect_status' => 'Global Collect Status',
			'global_collect_description' => 'Global Collect Description',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('global_collect_status_history_id',$this->global_collect_status_history_id);
		$criteria->compare('global_collect_orders_id',$this->global_collect_orders_id);
		$criteria->compare('global_collect_date',$this->global_collect_date,true);
		$criteria->compare('global_collect_status',$this->global_collect_status);
		$criteria->compare('global_collect_description',$this->global_collect_description,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}