<?php

/**
 * This is the model class for table "google_wallet".
 *
 * The followings are the available columns in table 'google_wallet':
 * @property string $google_wallet_order_id
 * @property string $google_wallet_merchant_id
 * @property string $google_wallet_create_date
 * @property string $google_wallet_expired_date
 * @property string $google_wallet_transaction_id
 * @property string $google_wallet_amount
 * @property string $google_wallet_currency
 * @property string $google_wallet_return_jwt
 * @property string $google_wallet_custom_signature
 */
class GoogleWalletBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GoogleWalletBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'google_wallet';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('google_wallet_order_id', 'length', 'max'=>11),
			array('google_wallet_merchant_id', 'length', 'max'=>20),
			array('google_wallet_transaction_id', 'length', 'max'=>80),
			array('google_wallet_amount', 'length', 'max'=>15),
			array('google_wallet_currency', 'length', 'max'=>3),
			array('google_wallet_custom_signature', 'length', 'max'=>64),
			array('google_wallet_create_date, google_wallet_expired_date, google_wallet_return_jwt', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('google_wallet_order_id, google_wallet_merchant_id, google_wallet_create_date, google_wallet_expired_date, google_wallet_transaction_id, google_wallet_amount, google_wallet_currency, google_wallet_return_jwt, google_wallet_custom_signature', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'google_wallet_order_id' => 'Google Wallet Order',
			'google_wallet_merchant_id' => 'Google Wallet Merchant',
			'google_wallet_create_date' => 'Google Wallet Create Date',
			'google_wallet_expired_date' => 'Google Wallet Expired Date',
			'google_wallet_transaction_id' => 'Google Wallet Transaction',
			'google_wallet_amount' => 'Google Wallet Amount',
			'google_wallet_currency' => 'Google Wallet Currency',
			'google_wallet_return_jwt' => 'Google Wallet Return Jwt',
			'google_wallet_custom_signature' => 'Google Wallet Custom Signature',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('google_wallet_order_id',$this->google_wallet_order_id,true);
		$criteria->compare('google_wallet_merchant_id',$this->google_wallet_merchant_id,true);
		$criteria->compare('google_wallet_create_date',$this->google_wallet_create_date,true);
		$criteria->compare('google_wallet_expired_date',$this->google_wallet_expired_date,true);
		$criteria->compare('google_wallet_transaction_id',$this->google_wallet_transaction_id,true);
		$criteria->compare('google_wallet_amount',$this->google_wallet_amount,true);
		$criteria->compare('google_wallet_currency',$this->google_wallet_currency,true);
		$criteria->compare('google_wallet_return_jwt',$this->google_wallet_return_jwt,true);
		$criteria->compare('google_wallet_custom_signature',$this->google_wallet_custom_signature,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}