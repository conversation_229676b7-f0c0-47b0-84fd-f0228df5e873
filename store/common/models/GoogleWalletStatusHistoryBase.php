<?php

/**
 * This is the model class for table "google_wallet_status_history".
 *
 * The followings are the available columns in table 'google_wallet_status_history':
 * @property string $google_wallet_status_history_id
 * @property string $google_wallet_order_id
 * @property string $google_wallet_date
 * @property string $google_wallet_status_id
 * @property string $google_wallet_description
 * @property string $google_wallet_changed_by
 */
class GoogleWalletStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GoogleWalletStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'google_wallet_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('google_wallet_order_id', 'length', 'max'=>11),
			array('google_wallet_status_id', 'length', 'max'=>32),
			array('google_wallet_description, google_wallet_changed_by', 'length', 'max'=>128),
			array('google_wallet_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('google_wallet_status_history_id, google_wallet_order_id, google_wallet_date, google_wallet_status_id, google_wallet_description, google_wallet_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'google_wallet_status_history_id' => 'Google Wallet Status History',
			'google_wallet_order_id' => 'Google Wallet Order',
			'google_wallet_date' => 'Google Wallet Date',
			'google_wallet_status_id' => 'Google Wallet Status',
			'google_wallet_description' => 'Google Wallet Description',
			'google_wallet_changed_by' => 'Google Wallet Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('google_wallet_status_history_id',$this->google_wallet_status_history_id,true);
		$criteria->compare('google_wallet_order_id',$this->google_wallet_order_id,true);
		$criteria->compare('google_wallet_date',$this->google_wallet_date,true);
		$criteria->compare('google_wallet_status_id',$this->google_wallet_status_id,true);
		$criteria->compare('google_wallet_description',$this->google_wallet_description,true);
		$criteria->compare('google_wallet_changed_by',$this->google_wallet_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}