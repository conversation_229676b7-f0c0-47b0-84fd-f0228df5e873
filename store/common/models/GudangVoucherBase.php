<?php

/**
 * This is the model class for table "gudang_voucher".
 *
 * The followings are the available columns in table 'gudang_voucher':
 * @property string $gudang_voucher_orders_id
 * @property string $gudang_voucher_payment_method
 * @property string $gudang_voucher_reference_id
 * @property string $gudang_voucher_tran_status
 * @property string $gudang_voucher_voucher_code
 * @property string $gudang_voucher_tran_datetime
 * @property double $gudang_voucher_amount
 * @property string $gudang_voucher_currency
 * @property string $gudang_voucher_remark
 * @property string $gudang_voucher_signature
 */
class GudangVoucherBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GudangVoucherBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'gudang_voucher';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('gudang_voucher_voucher_code, gudang_voucher_currency, gudang_voucher_remark', 'required'),
			array('gudang_voucher_amount', 'numerical'),
			array('gudang_voucher_orders_id, gudang_voucher_payment_method', 'length', 'max'=>11),
			array('gudang_voucher_reference_id', 'length', 'max'=>20),
			array('gudang_voucher_tran_status', 'length', 'max'=>16),
			array('gudang_voucher_voucher_code', 'length', 'max'=>255),
			array('gudang_voucher_currency', 'length', 'max'=>3),
			array('gudang_voucher_remark', 'length', 'max'=>100),
			array('gudang_voucher_signature', 'length', 'max'=>50),
			array('gudang_voucher_tran_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('gudang_voucher_orders_id, gudang_voucher_payment_method, gudang_voucher_reference_id, gudang_voucher_tran_status, gudang_voucher_voucher_code, gudang_voucher_tran_datetime, gudang_voucher_amount, gudang_voucher_currency, gudang_voucher_remark, gudang_voucher_signature', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'gudang_voucher_orders_id' => 'Gudang Voucher Orders',
			'gudang_voucher_payment_method' => 'Gudang Voucher Payment Method',
			'gudang_voucher_reference_id' => 'Gudang Voucher Reference',
			'gudang_voucher_tran_status' => 'Gudang Voucher Tran Status',
			'gudang_voucher_voucher_code' => 'Gudang Voucher Voucher Code',
			'gudang_voucher_tran_datetime' => 'Gudang Voucher Tran Datetime',
			'gudang_voucher_amount' => 'Gudang Voucher Amount',
			'gudang_voucher_currency' => 'Gudang Voucher Currency',
			'gudang_voucher_remark' => 'Gudang Voucher Remark',
			'gudang_voucher_signature' => 'Gudang Voucher Signature',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('gudang_voucher_orders_id',$this->gudang_voucher_orders_id,true);
		$criteria->compare('gudang_voucher_payment_method',$this->gudang_voucher_payment_method,true);
		$criteria->compare('gudang_voucher_reference_id',$this->gudang_voucher_reference_id,true);
		$criteria->compare('gudang_voucher_tran_status',$this->gudang_voucher_tran_status,true);
		$criteria->compare('gudang_voucher_voucher_code',$this->gudang_voucher_voucher_code,true);
		$criteria->compare('gudang_voucher_tran_datetime',$this->gudang_voucher_tran_datetime,true);
		$criteria->compare('gudang_voucher_amount',$this->gudang_voucher_amount);
		$criteria->compare('gudang_voucher_currency',$this->gudang_voucher_currency,true);
		$criteria->compare('gudang_voucher_remark',$this->gudang_voucher_remark,true);
		$criteria->compare('gudang_voucher_signature',$this->gudang_voucher_signature,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}