<?php

/**
 * This is the model class for table "gudang_voucher_status_history".
 *
 * The followings are the available columns in table 'gudang_voucher_status_history':
 * @property string $gudang_voucher_status_history_id
 * @property string $gudang_voucher_orders_id
 * @property string $gudang_voucher_status_reference_id
 * @property string $gudang_voucher_status_key
 * @property string $gudang_voucher_status_signature
 * @property string $gudang_voucher_status_date
 * @property string $gudang_voucher_changed_by
 */
class GudangVoucherStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GudangVoucherStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'gudang_voucher_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('gudang_voucher_changed_by', 'required'),
			array('gudang_voucher_orders_id', 'length', 'max'=>11),
			array('gudang_voucher_status_reference_id', 'length', 'max'=>20),
			array('gudang_voucher_status_key', 'length', 'max'=>16),
			array('gudang_voucher_status_signature', 'length', 'max'=>50),
			array('gudang_voucher_changed_by', 'length', 'max'=>128),
			array('gudang_voucher_status_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('gudang_voucher_status_history_id, gudang_voucher_orders_id, gudang_voucher_status_reference_id, gudang_voucher_status_key, gudang_voucher_status_signature, gudang_voucher_status_date, gudang_voucher_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'gudang_voucher_status_history_id' => 'Gudang Voucher Status History',
			'gudang_voucher_orders_id' => 'Gudang Voucher Orders',
			'gudang_voucher_status_reference_id' => 'Gudang Voucher Status Reference',
			'gudang_voucher_status_key' => 'Gudang Voucher Status Key',
			'gudang_voucher_status_signature' => 'Gudang Voucher Status Signature',
			'gudang_voucher_status_date' => 'Gudang Voucher Status Date',
			'gudang_voucher_changed_by' => 'Gudang Voucher Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('gudang_voucher_status_history_id',$this->gudang_voucher_status_history_id,true);
		$criteria->compare('gudang_voucher_orders_id',$this->gudang_voucher_orders_id,true);
		$criteria->compare('gudang_voucher_status_reference_id',$this->gudang_voucher_status_reference_id,true);
		$criteria->compare('gudang_voucher_status_key',$this->gudang_voucher_status_key,true);
		$criteria->compare('gudang_voucher_status_signature',$this->gudang_voucher_status_signature,true);
		$criteria->compare('gudang_voucher_status_date',$this->gudang_voucher_status_date,true);
		$criteria->compare('gudang_voucher_changed_by',$this->gudang_voucher_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}