<?php

/**
 * This is the model class for table "image_configuration".
 *
 * The followings are the available columns in table 'image_configuration':
 * @property integer $image_configuration_id
 * @property string $image_category
 * @property string $file_path
 * @property string $web_path
 * @property string $aws_s3_info
 * @property string $user_groups
 */
class ImageConfigurationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ImageConfigurationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'image_configuration';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_groups', 'required'),
			array('image_category', 'length', 'max'=>16),
			array('file_path, web_path, aws_s3_info', 'length', 'max'=>80),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('image_configuration_id, image_category, file_path, web_path, aws_s3_info, user_groups', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'image_configuration_id' => 'Image Configuration',
			'image_category' => 'Image Category',
			'file_path' => 'File Path',
			'web_path' => 'Web Path',
			'aws_s3_info' => 'Aws S3 Info',
			'user_groups' => 'User Groups',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('image_configuration_id',$this->image_configuration_id);
		$criteria->compare('image_category',$this->image_category,true);
		$criteria->compare('file_path',$this->file_path,true);
		$criteria->compare('web_path',$this->web_path,true);
		$criteria->compare('aws_s3_info',$this->aws_s3_info,true);
		$criteria->compare('user_groups',$this->user_groups,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}