<?php

/**
 * This is the model class for table "indomog".
 *
 * The followings are the available columns in table 'indomog':
 * @property string $indomog_orders_id
 * @property string $indomog_merchant_id
 * @property string $indomog_transaction_id
 * @property string $indomog_bank_id
 * @property string $indomog_transaction_status
 * @property string $indomog_transaction_date
 * @property string $indomog_amount
 * @property string $indomog_signature
 * @property string $indomog_certificate
 */
class IndomogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IndomogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'indomog';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('indomog_orders_id', 'length', 'max'=>11),
			array('indomog_merchant_id, indomog_transaction_id, indomog_bank_id', 'length', 'max'=>32),
			array('indomog_transaction_status', 'length', 'max'=>20),
			array('indomog_amount', 'length', 'max'=>15),
			array('indomog_signature', 'length', 'max'=>255),
			array('indomog_transaction_date, indomog_certificate', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('indomog_orders_id, indomog_merchant_id, indomog_transaction_id, indomog_bank_id, indomog_transaction_status, indomog_transaction_date, indomog_amount, indomog_signature, indomog_certificate', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'indomog_orders_id' => 'Indomog Orders',
			'indomog_merchant_id' => 'Indomog Merchant',
			'indomog_transaction_id' => 'Indomog Transaction',
			'indomog_bank_id' => 'Indomog Bank',
			'indomog_transaction_status' => 'Indomog Transaction Status',
			'indomog_transaction_date' => 'Indomog Transaction Date',
			'indomog_amount' => 'Indomog Amount',
			'indomog_signature' => 'Indomog Signature',
			'indomog_certificate' => 'Indomog Certificate',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('indomog_orders_id',$this->indomog_orders_id,true);
		$criteria->compare('indomog_merchant_id',$this->indomog_merchant_id,true);
		$criteria->compare('indomog_transaction_id',$this->indomog_transaction_id,true);
		$criteria->compare('indomog_bank_id',$this->indomog_bank_id,true);
		$criteria->compare('indomog_transaction_status',$this->indomog_transaction_status,true);
		$criteria->compare('indomog_transaction_date',$this->indomog_transaction_date,true);
		$criteria->compare('indomog_amount',$this->indomog_amount,true);
		$criteria->compare('indomog_signature',$this->indomog_signature,true);
		$criteria->compare('indomog_certificate',$this->indomog_certificate,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}