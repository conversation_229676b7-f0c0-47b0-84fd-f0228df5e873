<?php

/**
 * This is the model class for table "indomog_dtu_logs".
 *
 * The followings are the available columns in table 'indomog_dtu_logs':
 * @property string $indomog_log_id
 * @property string $products_id
 * @property string $indomog_request_type
 * @property string $email_hp
 * @property string $indomog_pid
 * @property string $indomog_qid
 * @property string $indomog_verify_log_id
 * @property string $indomog_topup_log_id
 * @property string $created_datetime
 */
class IndomogDtuLogsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IndomogDtuLogsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'indomog_dtu_logs';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, indomog_request_type, email_hp, indomog_pid, created_datetime', 'required'),
			array('products_id, indomog_verify_log_id, indomog_topup_log_id', 'length', 'max'=>11),
			array('indomog_request_type', 'length', 'max'=>7),
			array('email_hp', 'length', 'max'=>96),
			array('indomog_pid', 'length', 'max'=>255),
			array('indomog_qid', 'length', 'max'=>12),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('indomog_log_id, products_id, indomog_request_type, email_hp, indomog_pid, indomog_qid, indomog_verify_log_id, indomog_topup_log_id, created_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'indomog_log_id' => 'Indomog Log',
			'products_id' => 'Products',
			'indomog_request_type' => 'Indomog Request Type',
			'email_hp' => 'Email Hp',
			'indomog_pid' => 'Indomog Pid',
			'indomog_qid' => 'Indomog Qid',
			'indomog_verify_log_id' => 'Indomog Verify Log',
			'indomog_topup_log_id' => 'Indomog Topup Log',
			'created_datetime' => 'Created Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
            // Warning: Please modify the following code to remove attributes that
            // should not be searched.

            $criteria=new CDbCriteria;

            $criteria->compare('indomog_log_id',$this->indomog_log_id,true);
            $criteria->compare('products_id',$this->products_id,true);
            $criteria->compare('indomog_request_type',$this->indomog_request_type,true);
            $criteria->compare('email_hp',$this->email_hp,true);
            $criteria->compare('indomog_pid',$this->indomog_pid,true);
            $criteria->compare('indomog_qid',$this->indomog_qid,true);
            $criteria->compare('indomog_verify_log_id',$this->indomog_verify_log_id,true);
            $criteria->compare('indomog_topup_log_id',$this->indomog_topup_log_id,true);
            $criteria->compare('created_datetime',$this->created_datetime,true);

            return new CActiveDataProvider($this, array(
                    'criteria'=>$criteria,
            ));
    }

    public function create_indomog_log($email_hp, $products_id, $indomog_pid, $request_type) {
        $log_data_sql = array();
        $log_data_sql['indomog_request_type'] = $request_type;
        $log_data_sql['products_id'] = $products_id;
        $log_data_sql['email_hp'] = $email_hp;
        $log_data_sql['indomog_pid'] = $indomog_pid;
        $log_data_sql['created_datetime'] = new CDbExpression('NOW()');
        return $this->saveNewRecord($log_data_sql, true);
    }

    public function update_indomog_log($indomog_log_id, $log_data_sql = array()) {
        if ($log_data_sql) {
            return $this->updateByPk($indomog_log_id, $log_data_sql);
        }
        return false;
    }

}