<?php

/**
 * This is the model class for table "indomog_status_history".
 *
 * The followings are the available columns in table 'indomog_status_history':
 * @property string $indomog_status_history_id
 * @property string $indomog_orders_id
 * @property string $indomog_response_code
 * @property string $indomog_response_description
 * @property string $indomog_status_datetime
 * @property string $indomog_changed_by
 */
class IndomogStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IndomogStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'indomog_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('indomog_orders_id', 'length', 'max'=>11),
			array('indomog_response_code', 'length', 'max'=>3),
			array('indomog_response_description, indomog_changed_by', 'length', 'max'=>128),
			array('indomog_status_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('indomog_status_history_id, indomog_orders_id, indomog_response_code, indomog_response_description, indomog_status_datetime, indomog_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'indomog_status_history_id' => 'Indomog Status History',
			'indomog_orders_id' => 'Indomog Orders',
			'indomog_response_code' => 'Indomog Response Code',
			'indomog_response_description' => 'Indomog Response Description',
			'indomog_status_datetime' => 'Indomog Status Datetime',
			'indomog_changed_by' => 'Indomog Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('indomog_status_history_id',$this->indomog_status_history_id,true);
		$criteria->compare('indomog_orders_id',$this->indomog_orders_id,true);
		$criteria->compare('indomog_response_code',$this->indomog_response_code,true);
		$criteria->compare('indomog_response_description',$this->indomog_response_description,true);
		$criteria->compare('indomog_status_datetime',$this->indomog_status_datetime,true);
		$criteria->compare('indomog_changed_by',$this->indomog_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}