<?php

/**
 * This is the model class for table "info_changed_history".
 *
 * The followings are the available columns in table 'info_changed_history':
 * @property integer $info_changed_history_id
 * @property string $info_changed_history_type
 * @property integer $info_changed_history_type_id
 * @property string $info_changed_history_remark
 * @property string $info_changed_history_date_added
 * @property string $info_changed_history_added_by
 */
class InfoChangedHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InfoChangedHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'info_changed_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('info_changed_history_remark', 'required'),
			array('info_changed_history_type_id', 'numerical', 'integerOnly'=>true),
			array('info_changed_history_type', 'length', 'max'=>30),
			array('info_changed_history_added_by', 'length', 'max'=>255),
			array('info_changed_history_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('info_changed_history_id, info_changed_history_type, info_changed_history_type_id, info_changed_history_remark, info_changed_history_date_added, info_changed_history_added_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'info_changed_history_id' => 'Info Changed History',
			'info_changed_history_type' => 'Info Changed History Type',
			'info_changed_history_type_id' => 'Info Changed History Type',
			'info_changed_history_remark' => 'Info Changed History Remark',
			'info_changed_history_date_added' => 'Info Changed History Date Added',
			'info_changed_history_added_by' => 'Info Changed History Added By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('info_changed_history_id',$this->info_changed_history_id);
		$criteria->compare('info_changed_history_type',$this->info_changed_history_type,true);
		$criteria->compare('info_changed_history_type_id',$this->info_changed_history_type_id);
		$criteria->compare('info_changed_history_remark',$this->info_changed_history_remark,true);
		$criteria->compare('info_changed_history_date_added',$this->info_changed_history_date_added,true);
		$criteria->compare('info_changed_history_added_by',$this->info_changed_history_added_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}