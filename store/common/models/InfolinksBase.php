<?php

/**
 * This is the model class for table "infolinks".
 *
 * The followings are the available columns in table 'infolinks':
 * @property integer $infolinks_id
 * @property integer $language_id
 * @property integer $infolinks_groups_id
 * @property string $infolinks_title
 * @property string $infolinks_url_alias
 * @property string $infolinks_image
 * @property string $infolinks_URL
 * @property integer $infolinks_new_window
 * @property integer $infolinks_sort_order
 * @property integer $infolinks_active
 * @property integer $infolinks_imageonly
 * @property integer $infolinks_select
 * @property string $infolinks_align
 * @property integer $infolinks_right_navigation
 * @property integer $infolinks_back_button
 * @property string $infolinks_cat_id
 * @property integer $infolinks_include_subcat
 */
class InfolinksBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InfolinksBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'infolinks';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('infolinks_image', 'required'),
			array('language_id, infolinks_groups_id, infolinks_new_window, infolinks_sort_order, infolinks_active, infolinks_imageonly, infolinks_select, infolinks_right_navigation, infolinks_back_button, infolinks_include_subcat', 'numerical', 'integerOnly'=>true),
			array('infolinks_title, infolinks_url_alias, infolinks_URL', 'length', 'max'=>255),
			array('infolinks_align', 'length', 'max'=>10),
			array('infolinks_cat_id', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('infolinks_id, language_id, infolinks_groups_id, infolinks_title, infolinks_url_alias, infolinks_image, infolinks_URL, infolinks_new_window, infolinks_sort_order, infolinks_active, infolinks_imageonly, infolinks_select, infolinks_align, infolinks_right_navigation, infolinks_back_button, infolinks_cat_id, infolinks_include_subcat', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'infolinks_id' => 'Infolinks',
			'language_id' => 'Language',
			'infolinks_groups_id' => 'Infolinks Groups',
			'infolinks_title' => 'Infolinks Title',
			'infolinks_url_alias' => 'Infolinks Url Alias',
			'infolinks_image' => 'Infolinks Image',
			'infolinks_URL' => 'Infolinks Url',
			'infolinks_new_window' => 'Infolinks New Window',
			'infolinks_sort_order' => 'Infolinks Sort Order',
			'infolinks_active' => 'Infolinks Active',
			'infolinks_imageonly' => 'Infolinks Imageonly',
			'infolinks_select' => 'Infolinks Select',
			'infolinks_align' => 'Infolinks Align',
			'infolinks_right_navigation' => 'Infolinks Right Navigation',
			'infolinks_back_button' => 'Infolinks Back Button',
			'infolinks_cat_id' => 'Infolinks Cat',
			'infolinks_include_subcat' => 'Infolinks Include Subcat',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('infolinks_id',$this->infolinks_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('infolinks_groups_id',$this->infolinks_groups_id);
		$criteria->compare('infolinks_title',$this->infolinks_title,true);
		$criteria->compare('infolinks_url_alias',$this->infolinks_url_alias,true);
		$criteria->compare('infolinks_image',$this->infolinks_image,true);
		$criteria->compare('infolinks_URL',$this->infolinks_URL,true);
		$criteria->compare('infolinks_new_window',$this->infolinks_new_window);
		$criteria->compare('infolinks_sort_order',$this->infolinks_sort_order);
		$criteria->compare('infolinks_active',$this->infolinks_active);
		$criteria->compare('infolinks_imageonly',$this->infolinks_imageonly);
		$criteria->compare('infolinks_select',$this->infolinks_select);
		$criteria->compare('infolinks_align',$this->infolinks_align,true);
		$criteria->compare('infolinks_right_navigation',$this->infolinks_right_navigation);
		$criteria->compare('infolinks_back_button',$this->infolinks_back_button);
		$criteria->compare('infolinks_cat_id',$this->infolinks_cat_id,true);
		$criteria->compare('infolinks_include_subcat',$this->infolinks_include_subcat);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}