<?php

/**
 * This is the model class for table "infolinks_contents".
 *
 * The followings are the available columns in table 'infolinks_contents':
 * @property integer $infolinks_contents_id
 * @property integer $infolinks_id
 * @property string $infolinks_contents
 * @property integer $infolinks_contents_page
 */
class InfolinksContentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InfolinksContentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'infolinks_contents';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('infolinks_contents', 'required'),
			array('infolinks_id, infolinks_contents_page', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('infolinks_contents_id, infolinks_id, infolinks_contents, infolinks_contents_page', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'infolinks_contents_id' => 'Infolinks Contents',
			'infolinks_id' => 'Infolinks',
			'infolinks_contents' => 'Infolinks Contents',
			'infolinks_contents_page' => 'Infolinks Contents Page',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('infolinks_contents_id',$this->infolinks_contents_id);
		$criteria->compare('infolinks_id',$this->infolinks_id);
		$criteria->compare('infolinks_contents',$this->infolinks_contents,true);
		$criteria->compare('infolinks_contents_page',$this->infolinks_contents_page);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}