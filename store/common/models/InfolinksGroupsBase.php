<?php

/**
 * This is the model class for table "infolinks_groups".
 *
 * The followings are the available columns in table 'infolinks_groups':
 * @property integer $infolinks_groups_id
 * @property integer $infolinks_groups_parent_id
 * @property integer $language_id
 * @property string $infolinks_groups_title
 * @property integer $infolinks_groups_sort_order
 * @property string $infolinks_groups_bg_image
 * @property integer $infolinks_groups_show_title
 * @property string $infolinks_groups_align
 * @property integer $infolinks_groups_active
 * @property integer $infolinks_groups_main_page_id
 * @property string $infolinks_groups_seperator_image
 */
class InfolinksGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InfolinksGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'infolinks_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('infolinks_groups_bg_image, infolinks_groups_seperator_image', 'required'),
			array('infolinks_groups_parent_id, language_id, infolinks_groups_sort_order, infolinks_groups_show_title, infolinks_groups_active, infolinks_groups_main_page_id', 'numerical', 'integerOnly'=>true),
			array('infolinks_groups_title', 'length', 'max'=>255),
			array('infolinks_groups_align', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('infolinks_groups_id, infolinks_groups_parent_id, language_id, infolinks_groups_title, infolinks_groups_sort_order, infolinks_groups_bg_image, infolinks_groups_show_title, infolinks_groups_align, infolinks_groups_active, infolinks_groups_main_page_id, infolinks_groups_seperator_image', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'infolinks_groups_id' => 'Infolinks Groups',
			'infolinks_groups_parent_id' => 'Infolinks Groups Parent',
			'language_id' => 'Language',
			'infolinks_groups_title' => 'Infolinks Groups Title',
			'infolinks_groups_sort_order' => 'Infolinks Groups Sort Order',
			'infolinks_groups_bg_image' => 'Infolinks Groups Bg Image',
			'infolinks_groups_show_title' => 'Infolinks Groups Show Title',
			'infolinks_groups_align' => 'Infolinks Groups Align',
			'infolinks_groups_active' => 'Infolinks Groups Active',
			'infolinks_groups_main_page_id' => 'Infolinks Groups Main Page',
			'infolinks_groups_seperator_image' => 'Infolinks Groups Seperator Image',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('infolinks_groups_id',$this->infolinks_groups_id);
		$criteria->compare('infolinks_groups_parent_id',$this->infolinks_groups_parent_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('infolinks_groups_title',$this->infolinks_groups_title,true);
		$criteria->compare('infolinks_groups_sort_order',$this->infolinks_groups_sort_order);
		$criteria->compare('infolinks_groups_bg_image',$this->infolinks_groups_bg_image,true);
		$criteria->compare('infolinks_groups_show_title',$this->infolinks_groups_show_title);
		$criteria->compare('infolinks_groups_align',$this->infolinks_groups_align,true);
		$criteria->compare('infolinks_groups_active',$this->infolinks_groups_active);
		$criteria->compare('infolinks_groups_main_page_id',$this->infolinks_groups_main_page_id);
		$criteria->compare('infolinks_groups_seperator_image',$this->infolinks_groups_seperator_image,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}