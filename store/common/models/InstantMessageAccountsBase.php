<?php

/**
 * This is the model class for table "instant_message_accounts".
 *
 * The followings are the available columns in table 'instant_message_accounts':
 * @property integer $instant_message_accounts_id
 * @property integer $customer_id
 * @property integer $instant_message_type_id
 * @property string $instant_message_userid
 * @property string $instant_message_remarks
 */
class InstantMessageAccountsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InstantMessageAccountsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'instant_message_accounts';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customer_id, instant_message_type_id', 'numerical', 'integerOnly'=>true),
			array('instant_message_userid, instant_message_remarks', 'length', 'max'=>96),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('instant_message_accounts_id, customer_id, instant_message_type_id, instant_message_userid, instant_message_remarks', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'instant_message_accounts_id' => 'Instant Message Accounts',
			'customer_id' => 'Customer',
			'instant_message_type_id' => 'Instant Message Type',
			'instant_message_userid' => 'Instant Message Userid',
			'instant_message_remarks' => 'Instant Message Remarks',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('instant_message_accounts_id',$this->instant_message_accounts_id);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('instant_message_type_id',$this->instant_message_type_id);
		$criteria->compare('instant_message_userid',$this->instant_message_userid,true);
		$criteria->compare('instant_message_remarks',$this->instant_message_remarks,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}