<?php

/**
 * This is the model class for table "instant_message_type".
 *
 * The followings are the available columns in table 'instant_message_type':
 * @property integer $instant_message_type_id
 * @property string $instant_message_type_name
 * @property string $instant_message_type_description
 * @property integer $instant_message_type_order
 */
class InstantMessageTypeBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InstantMessageTypeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'instant_message_type';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('instant_message_type_order', 'numerical', 'integerOnly'=>true),
			array('instant_message_type_name', 'length', 'max'=>96),
			array('instant_message_type_description', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('instant_message_type_id, instant_message_type_name, instant_message_type_description, instant_message_type_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'instant_message_type_id' => 'Instant Message Type',
			'instant_message_type_name' => 'Instant Message Type Name',
			'instant_message_type_description' => 'Instant Message Type Description',
			'instant_message_type_order' => 'Instant Message Type Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('instant_message_type_id',$this->instant_message_type_id);
		$criteria->compare('instant_message_type_name',$this->instant_message_type_name,true);
		$criteria->compare('instant_message_type_description',$this->instant_message_type_description,true);
		$criteria->compare('instant_message_type_order',$this->instant_message_type_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}