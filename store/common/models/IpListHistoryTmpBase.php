<?php

/**
 * This is the model class for table "ip_list_history_tmp".
 *
 * The followings are the available columns in table 'ip_list_history_tmp':
 * @property string $ip_list_history_id
 * @property string $ip_list_history_ip_address
 * @property integer $customers_id
 * @property string $page_view_ip_list_id
 * @property string $ip_list_history_tags
 * @property string $scripts_name
 * @property string $ip_list_history_datatime
 * @property string $ip_list_history_remark
 */
class IpListHistoryTmpBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IpListHistoryTmpBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ip_list_history_tmp';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id', 'numerical', 'integerOnly'=>true),
			array('ip_list_history_ip_address', 'length', 'max'=>15),
			array('page_view_ip_list_id', 'length', 'max'=>11),
			array('ip_list_history_tags', 'length', 'max'=>32),
			array('scripts_name', 'length', 'max'=>255),
			array('ip_list_history_remark', 'length', 'max'=>64),
			array('ip_list_history_datatime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('ip_list_history_id, ip_list_history_ip_address, customers_id, page_view_ip_list_id, ip_list_history_tags, scripts_name, ip_list_history_datatime, ip_list_history_remark', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'ip_list_history_id' => 'Ip List History',
			'ip_list_history_ip_address' => 'Ip List History Ip Address',
			'customers_id' => 'Customers',
			'page_view_ip_list_id' => 'Page View Ip List',
			'ip_list_history_tags' => 'Ip List History Tags',
			'scripts_name' => 'Scripts Name',
			'ip_list_history_datatime' => 'Ip List History Datatime',
			'ip_list_history_remark' => 'Ip List History Remark',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('ip_list_history_id',$this->ip_list_history_id,true);
		$criteria->compare('ip_list_history_ip_address',$this->ip_list_history_ip_address,true);
		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('page_view_ip_list_id',$this->page_view_ip_list_id,true);
		$criteria->compare('ip_list_history_tags',$this->ip_list_history_tags,true);
		$criteria->compare('scripts_name',$this->scripts_name,true);
		$criteria->compare('ip_list_history_datatime',$this->ip_list_history_datatime,true);
		$criteria->compare('ip_list_history_remark',$this->ip_list_history_remark,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}