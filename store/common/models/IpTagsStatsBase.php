<?php

/**
 * This is the model class for table "ip_tags_stats".
 *
 * The followings are the available columns in table 'ip_tags_stats':
 * @property string $page_view_ip_list_id
 * @property string $script_tags_name
 * @property integer $ip_tags_stats_counter
 * @property string $ip_tags_stats_last_visit
 */
class IpTagsStatsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IpTagsStatsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ip_tags_stats';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('ip_tags_stats_counter', 'numerical', 'integerOnly'=>true),
			array('page_view_ip_list_id', 'length', 'max'=>11),
			array('script_tags_name', 'length', 'max'=>32),
			array('ip_tags_stats_last_visit', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('page_view_ip_list_id, script_tags_name, ip_tags_stats_counter, ip_tags_stats_last_visit', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'page_view_ip_list_id' => 'Page View Ip List',
			'script_tags_name' => 'Script Tags Name',
			'ip_tags_stats_counter' => 'Ip Tags Stats Counter',
			'ip_tags_stats_last_visit' => 'Ip Tags Stats Last Visit',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('page_view_ip_list_id',$this->page_view_ip_list_id,true);
		$criteria->compare('script_tags_name',$this->script_tags_name,true);
		$criteria->compare('ip_tags_stats_counter',$this->ip_tags_stats_counter);
		$criteria->compare('ip_tags_stats_last_visit',$this->ip_tags_stats_last_visit,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}