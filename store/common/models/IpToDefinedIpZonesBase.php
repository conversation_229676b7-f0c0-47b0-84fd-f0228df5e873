<?php

/**
 * This is the model class for table "ip_to_defined_ip_zones".
 *
 * The followings are the available columns in table 'ip_to_defined_ip_zones':
 * @property integer $ip_to_defined_ip_zones_id
 * @property string $ip_address
 * @property string $subnet
 * @property integer $defined_ip_zones_id
 * @property string $ip_to_defined_ip_zones_last_modified
 * @property string $ip_to_defined_ip_zones_date_added
 */
class IpToDefinedIpZonesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IpToDefinedIpZonesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ip_to_defined_ip_zones';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('defined_ip_zones_id', 'numerical', 'integerOnly'=>true),
			array('ip_address', 'length', 'max'=>15),
			array('subnet', 'length', 'max'=>2),
			array('ip_to_defined_ip_zones_last_modified, ip_to_defined_ip_zones_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('ip_to_defined_ip_zones_id, ip_address, subnet, defined_ip_zones_id, ip_to_defined_ip_zones_last_modified, ip_to_defined_ip_zones_date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'ip_to_defined_ip_zones_id' => 'Ip To Defined Ip Zones',
			'ip_address' => 'Ip Address',
			'subnet' => 'Subnet',
			'defined_ip_zones_id' => 'Defined Ip Zones',
			'ip_to_defined_ip_zones_last_modified' => 'Ip To Defined Ip Zones Last Modified',
			'ip_to_defined_ip_zones_date_added' => 'Ip To Defined Ip Zones Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('ip_to_defined_ip_zones_id',$this->ip_to_defined_ip_zones_id);
		$criteria->compare('ip_address',$this->ip_address,true);
		$criteria->compare('subnet',$this->subnet,true);
		$criteria->compare('defined_ip_zones_id',$this->defined_ip_zones_id);
		$criteria->compare('ip_to_defined_ip_zones_last_modified',$this->ip_to_defined_ip_zones_last_modified,true);
		$criteria->compare('ip_to_defined_ip_zones_date_added',$this->ip_to_defined_ip_zones_date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}