<?php

/**
 * This is the model class for table "ipay88".
 *
 * The followings are the available columns in table 'ipay88':
 * @property integer $ipay88_orders_id
 * @property string $ipay88_merchant_code
 * @property integer $ipay88_payment_method
 * @property string $ipay88_payment_amount
 * @property string $ipay88_currency
 * @property string $ipay88_remark
 * @property string $ipay88_trans_id
 * @property string $ipay88_bank_auth_code
 * @property integer $ipay88_status
 * @property string $ipay88_err_desc
 * @property string $ipay88_sha1_signature
 */
class Ipay88Base extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Ipay88Base the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ipay88';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('ipay88_orders_id, ipay88_payment_method, ipay88_status', 'numerical', 'integerOnly'=>true),
			array('ipay88_merchant_code', 'length', 'max'=>20),
			array('ipay88_payment_amount', 'length', 'max'=>15),
			array('ipay88_currency', 'length', 'max'=>5),
			array('ipay88_remark, ipay88_err_desc, ipay88_sha1_signature', 'length', 'max'=>100),
			array('ipay88_trans_id', 'length', 'max'=>30),
			array('ipay88_bank_auth_code', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('ipay88_orders_id, ipay88_merchant_code, ipay88_payment_method, ipay88_payment_amount, ipay88_currency, ipay88_remark, ipay88_trans_id, ipay88_bank_auth_code, ipay88_status, ipay88_err_desc, ipay88_sha1_signature', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'ipay88_orders_id' => 'Ipay88 Orders',
			'ipay88_merchant_code' => 'Ipay88 Merchant Code',
			'ipay88_payment_method' => 'Ipay88 Payment Method',
			'ipay88_payment_amount' => 'Ipay88 Payment Amount',
			'ipay88_currency' => 'Ipay88 Currency',
			'ipay88_remark' => 'Ipay88 Remark',
			'ipay88_trans_id' => 'Ipay88 Trans',
			'ipay88_bank_auth_code' => 'Ipay88 Bank Auth Code',
			'ipay88_status' => 'Ipay88 Status',
			'ipay88_err_desc' => 'Ipay88 Err Desc',
			'ipay88_sha1_signature' => 'Ipay88 Sha1 Signature',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('ipay88_orders_id',$this->ipay88_orders_id);
		$criteria->compare('ipay88_merchant_code',$this->ipay88_merchant_code,true);
		$criteria->compare('ipay88_payment_method',$this->ipay88_payment_method);
		$criteria->compare('ipay88_payment_amount',$this->ipay88_payment_amount,true);
		$criteria->compare('ipay88_currency',$this->ipay88_currency,true);
		$criteria->compare('ipay88_remark',$this->ipay88_remark,true);
		$criteria->compare('ipay88_trans_id',$this->ipay88_trans_id,true);
		$criteria->compare('ipay88_bank_auth_code',$this->ipay88_bank_auth_code,true);
		$criteria->compare('ipay88_status',$this->ipay88_status);
		$criteria->compare('ipay88_err_desc',$this->ipay88_err_desc,true);
		$criteria->compare('ipay88_sha1_signature',$this->ipay88_sha1_signature,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}