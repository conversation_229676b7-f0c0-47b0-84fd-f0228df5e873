<?php

/**
 * This is the model class for table "ipay88_payment_status_history".
 *
 * The followings are the available columns in table 'ipay88_payment_status_history':
 * @property integer $ipay88_payment_status_history_id
 * @property integer $ipay88_orders_id
 * @property string $ipay88_status_key
 * @property string $ipay88_date
 * @property integer $ipay88_status
 */
class Ipay88PaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Ipay88PaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ipay88_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('ipay88_orders_id, ipay88_status', 'numerical', 'integerOnly'=>true),
			array('ipay88_status_key', 'length', 'max'=>32),
			array('ipay88_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('ipay88_payment_status_history_id, ipay88_orders_id, ipay88_status_key, ipay88_date, ipay88_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'ipay88_payment_status_history_id' => 'Ipay88 Payment Status History',
			'ipay88_orders_id' => 'Ipay88 Orders',
			'ipay88_status_key' => 'Ipay88 Status Key',
			'ipay88_date' => 'Ipay88 Date',
			'ipay88_status' => 'Ipay88 Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('ipay88_payment_status_history_id',$this->ipay88_payment_status_history_id);
		$criteria->compare('ipay88_orders_id',$this->ipay88_orders_id);
		$criteria->compare('ipay88_status_key',$this->ipay88_status_key,true);
		$criteria->compare('ipay88_date',$this->ipay88_date,true);
		$criteria->compare('ipay88_status',$this->ipay88_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}