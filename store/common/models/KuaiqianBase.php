<?php

/**
 * This is the model class for table "kuaiqian".
 *
 * The followings are the available columns in table 'kuaiqian':
 * @property integer $orders_id
 * @property string $kuaiqian_merchant_acct_id
 * @property string $kuaiqian_version
 * @property string $kuaiqian_pay_type
 * @property string $kuaiqian_bank_id
 * @property string $kuaiqian_order_currency
 * @property string $kuaiqian_order_amount
 * @property string $kuaiqian_deal_id
 * @property string $kuaiqian_bank_deal_id
 * @property string $kuaiqian_deal_time
 * @property string $kuaiqian_pay_currency
 * @property string $kuaiqian_pay_amount
 * @property string $kuaiqian_fee
 * @property string $kuaiqian_pay_result
 * @property string $kuaiqian_err_code
 * @property string $kuaiqian_signMsg
 */
class KuaiqianBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return KuaiqianBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'kuaiqian';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('kuaiqian_signMsg', 'required'),
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('kuaiqian_merchant_acct_id, kuaiqian_deal_id, kuaiqian_bank_deal_id', 'length', 'max'=>30),
			array('kuaiqian_version, kuaiqian_fee, kuaiqian_err_code', 'length', 'max'=>10),
			array('kuaiqian_pay_type, kuaiqian_pay_result', 'length', 'max'=>2),
			array('kuaiqian_bank_id', 'length', 'max'=>8),
			array('kuaiqian_order_currency, kuaiqian_pay_currency', 'length', 'max'=>3),
			array('kuaiqian_order_amount, kuaiqian_pay_amount', 'length', 'max'=>15),
			array('kuaiqian_deal_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, kuaiqian_merchant_acct_id, kuaiqian_version, kuaiqian_pay_type, kuaiqian_bank_id, kuaiqian_order_currency, kuaiqian_order_amount, kuaiqian_deal_id, kuaiqian_bank_deal_id, kuaiqian_deal_time, kuaiqian_pay_currency, kuaiqian_pay_amount, kuaiqian_fee, kuaiqian_pay_result, kuaiqian_err_code, kuaiqian_signMsg', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'kuaiqian_merchant_acct_id' => 'Kuaiqian Merchant Acct',
			'kuaiqian_version' => 'Kuaiqian Version',
			'kuaiqian_pay_type' => 'Kuaiqian Pay Type',
			'kuaiqian_bank_id' => 'Kuaiqian Bank',
			'kuaiqian_order_currency' => 'Kuaiqian Order Currency',
			'kuaiqian_order_amount' => 'Kuaiqian Order Amount',
			'kuaiqian_deal_id' => 'Kuaiqian Deal',
			'kuaiqian_bank_deal_id' => 'Kuaiqian Bank Deal',
			'kuaiqian_deal_time' => 'Kuaiqian Deal Time',
			'kuaiqian_pay_currency' => 'Kuaiqian Pay Currency',
			'kuaiqian_pay_amount' => 'Kuaiqian Pay Amount',
			'kuaiqian_fee' => 'Kuaiqian Fee',
			'kuaiqian_pay_result' => 'Kuaiqian Pay Result',
			'kuaiqian_err_code' => 'Kuaiqian Err Code',
			'kuaiqian_signMsg' => 'Kuaiqian Sign Msg',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('kuaiqian_merchant_acct_id',$this->kuaiqian_merchant_acct_id,true);
		$criteria->compare('kuaiqian_version',$this->kuaiqian_version,true);
		$criteria->compare('kuaiqian_pay_type',$this->kuaiqian_pay_type,true);
		$criteria->compare('kuaiqian_bank_id',$this->kuaiqian_bank_id,true);
		$criteria->compare('kuaiqian_order_currency',$this->kuaiqian_order_currency,true);
		$criteria->compare('kuaiqian_order_amount',$this->kuaiqian_order_amount,true);
		$criteria->compare('kuaiqian_deal_id',$this->kuaiqian_deal_id,true);
		$criteria->compare('kuaiqian_bank_deal_id',$this->kuaiqian_bank_deal_id,true);
		$criteria->compare('kuaiqian_deal_time',$this->kuaiqian_deal_time,true);
		$criteria->compare('kuaiqian_pay_currency',$this->kuaiqian_pay_currency,true);
		$criteria->compare('kuaiqian_pay_amount',$this->kuaiqian_pay_amount,true);
		$criteria->compare('kuaiqian_fee',$this->kuaiqian_fee,true);
		$criteria->compare('kuaiqian_pay_result',$this->kuaiqian_pay_result,true);
		$criteria->compare('kuaiqian_err_code',$this->kuaiqian_err_code,true);
		$criteria->compare('kuaiqian_signMsg',$this->kuaiqian_signMsg,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}