<?php

/**
 * This is the model class for table "kuaiqian_payment_status_history".
 *
 * The followings are the available columns in table 'kuaiqian_payment_status_history':
 * @property integer $kuaiqian_payment_status_history_id
 * @property integer $orders_id
 * @property string $kuaiqian_date
 * @property string $kuaiqian_status
 * @property string $kuaiqian_description
 */
class KuaiqianPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return KuaiqianPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'kuaiqian_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('kuaiqian_status', 'length', 'max'=>2),
			array('kuaiqian_description', 'length', 'max'=>64),
			array('kuaiqian_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('kuaiqian_payment_status_history_id, orders_id, kuaiqian_date, kuaiqian_status, kuaiqian_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'kuaiqian_payment_status_history_id' => 'Kuaiqian Payment Status History',
			'orders_id' => 'Orders',
			'kuaiqian_date' => 'Kuaiqian Date',
			'kuaiqian_status' => 'Kuaiqian Status',
			'kuaiqian_description' => 'Kuaiqian Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('kuaiqian_payment_status_history_id',$this->kuaiqian_payment_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('kuaiqian_date',$this->kuaiqian_date,true);
		$criteria->compare('kuaiqian_status',$this->kuaiqian_status,true);
		$criteria->compare('kuaiqian_description',$this->kuaiqian_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}