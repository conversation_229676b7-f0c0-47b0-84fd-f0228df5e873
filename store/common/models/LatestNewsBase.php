<?php

/**
 * This is the model class for table "latest_news".
 *
 * The followings are the available columns in table 'latest_news':
 * @property integer $news_id
 * @property string $latest_news_url_alias
 * @property string $custom_products_type
 * @property string $date_added
 * @property integer $language
 * @property integer $status
 * @property integer $news_groups_id
 * @property string $url
 * @property string $news_display_sites
 * @property integer $extra_news_display_sites
 */
class LatestNewsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LatestNewsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'latest_news';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('language, status, news_groups_id, extra_news_display_sites', 'numerical', 'integerOnly'=>true),
			array('latest_news_url_alias, custom_products_type, news_display_sites', 'length', 'max'=>255),
			array('url', 'length', 'max'=>200),
			array('date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('news_id, latest_news_url_alias, custom_products_type, date_added, language, status, news_groups_id, url, news_display_sites, extra_news_display_sites', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'news_id' => 'News',
			'latest_news_url_alias' => 'Latest News Url Alias',
			'custom_products_type' => 'Custom Products Type',
			'date_added' => 'Date Added',
			'language' => 'Language',
			'status' => 'Status',
			'news_groups_id' => 'News Groups',
			'url' => 'Url',
			'news_display_sites' => 'News Display Sites',
			'extra_news_display_sites' => 'Extra News Display Sites',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('news_id',$this->news_id);
		$criteria->compare('latest_news_url_alias',$this->latest_news_url_alias,true);
		$criteria->compare('custom_products_type',$this->custom_products_type,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('language',$this->language);
		$criteria->compare('status',$this->status);
		$criteria->compare('news_groups_id',$this->news_groups_id);
		$criteria->compare('url',$this->url,true);
		$criteria->compare('news_display_sites',$this->news_display_sites,true);
		$criteria->compare('extra_news_display_sites',$this->extra_news_display_sites);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}