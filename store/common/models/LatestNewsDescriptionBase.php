<?php

/**
 * This is the model class for table "latest_news_description".
 *
 * The followings are the available columns in table 'latest_news_description':
 * @property integer $news_id
 * @property integer $language_id
 * @property string $headline
 * @property string $latest_news_summary
 * @property string $content
 * @property integer $is_default
 */
class LatestNewsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LatestNewsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'latest_news_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('content', 'required'),
			array('language_id, is_default', 'numerical', 'integerOnly'=>true),
			array('headline', 'length', 'max'=>255),
			array('latest_news_summary', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('news_id, language_id, headline, latest_news_summary, content, is_default', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'news_id' => 'News',
			'language_id' => 'Language',
			'headline' => 'Headline',
			'latest_news_summary' => 'Latest News Summary',
			'content' => 'Content',
			'is_default' => 'Is Default',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('news_id',$this->news_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('headline',$this->headline,true);
		$criteria->compare('latest_news_summary',$this->latest_news_summary,true);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('is_default',$this->is_default);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}