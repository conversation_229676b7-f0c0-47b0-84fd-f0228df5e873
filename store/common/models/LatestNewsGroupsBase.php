<?php

/**
 * This is the model class for table "latest_news_groups".
 *
 * The followings are the available columns in table 'latest_news_groups':
 * @property integer $news_groups_id
 * @property string $news_groups_name
 * @property integer $news_groups_display_status
 * @property integer $news_groups_sort_order
 */
class LatestNewsGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LatestNewsGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'latest_news_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('news_groups_display_status, news_groups_sort_order', 'numerical', 'integerOnly'=>true),
			array('news_groups_name', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('news_groups_id, news_groups_name, news_groups_display_status, news_groups_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'news_groups_id' => 'News Groups',
			'news_groups_name' => 'News Groups Name',
			'news_groups_display_status' => 'News Groups Display Status',
			'news_groups_sort_order' => 'News Groups Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('news_groups_id',$this->news_groups_id);
		$criteria->compare('news_groups_name',$this->news_groups_name,true);
		$criteria->compare('news_groups_display_status',$this->news_groups_display_status);
		$criteria->compare('news_groups_sort_order',$this->news_groups_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}