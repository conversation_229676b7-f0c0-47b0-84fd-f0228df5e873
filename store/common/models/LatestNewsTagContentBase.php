<?php

/**
 * This is the model class for table "latest_news_tag_content".
 *
 * The followings are the available columns in table 'latest_news_tag_content':
 * @property integer $tag_content_id
 * @property integer $content_id
 * @property integer $tag_id
 * @property string $content_type
 */
class LatestNewsTagContentBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LatestNewsTagContentBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'latest_news_tag_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('content_id, tag_id', 'numerical', 'integerOnly'=>true),
			array('content_type', 'length', 'max'=>2),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tag_content_id, content_id, tag_id, content_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tag_content_id' => 'Tag Content',
			'content_id' => 'Content',
			'tag_id' => 'Tag',
			'content_type' => 'Content Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tag_content_id',$this->tag_content_id);
		$criteria->compare('content_id',$this->content_id);
		$criteria->compare('tag_id',$this->tag_id);
		$criteria->compare('content_type',$this->content_type,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}