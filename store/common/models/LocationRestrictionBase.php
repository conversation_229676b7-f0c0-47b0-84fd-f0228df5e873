<?php

/**
 * This is the model class for table "location_restriction".
 *
 * The followings are the available columns in table 'location_restriction':
 * @property string $id
 * @property string $country_iso_code2
 * @property string $restriction_info
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 */
class LocationRestrictionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesContentBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'location_restriction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('country_iso_code2, changed_by', 'required'),
			array('created_at, updated_at', 'numerical', 'integerOnly'=>true),
			array('country_iso_code2', 'length', 'max'=>2),
			array('changed_by', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, country_iso_code2, restriction_info, created_at, updated_at, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
            'country_iso_code2' => 'Country Iso Code2',
            'restriction_info' => 'Restriction Info',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->countries_id,true);
		$criteria->compare('country_iso_code2',$this->geo_zone_id,true);
		$criteria->compare('restriction_info', $this->business_tax_form, true);
		$criteria->compare('created_at', $this->created_at, true);
        $criteria->compare('updated_at', $this->updated_at, true);
        $criteria->compare('changed_by', $this->changed_by, true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}