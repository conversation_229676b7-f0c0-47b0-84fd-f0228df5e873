<?php

/**
 * This is the model class for table "locking".
 *
 * The followings are the available columns in table 'locking':
 * @property integer $locking_trans_id
 * @property string $locking_table_name
 * @property integer $locking_by
 * @property string $locking_from_ip
 * @property string $locking_datetime
 */
class LockingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LockingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'locking';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('locking_trans_id, locking_by', 'numerical', 'integerOnly'=>true),
			array('locking_table_name', 'length', 'max'=>255),
			array('locking_from_ip', 'length', 'max'=>20),
			array('locking_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('locking_trans_id, locking_table_name, locking_by, locking_from_ip, locking_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'locking_trans_id' => 'Locking Trans',
			'locking_table_name' => 'Locking Table Name',
			'locking_by' => 'Locking By',
			'locking_from_ip' => 'Locking From Ip',
			'locking_datetime' => 'Locking Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('locking_trans_id',$this->locking_trans_id);
		$criteria->compare('locking_table_name',$this->locking_table_name,true);
		$criteria->compare('locking_by',$this->locking_by);
		$criteria->compare('locking_from_ip',$this->locking_from_ip,true);
		$criteria->compare('locking_datetime',$this->locking_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}