<?php

/**
 * This is the model class for table "log_eclub_response_bk".
 *
 * The followings are the available columns in table 'log_eclub_response_bk':
 * @property string $id
 * @property string $custom_products_code_id
 * @property string $trans_code
 * @property string $method
 * @property string $sku
 * @property string $token
 * @property string $status
 * @property string $serialnumber
 * @property string $flag_state
 * @property string $error_msg
 * @property string $created_datetime
 */
class LogEclubResponseBkBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LogEclubResponseBkBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'log_eclub_response_bk';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('trans_code, method, sku, token, status, serialnumber, flag_state, error_msg, created_datetime', 'required'),
			array('custom_products_code_id', 'length', 'max'=>11),
			array('trans_code', 'length', 'max'=>32),
			array('method, sku', 'length', 'max'=>16),
			array('token, serialnumber, error_msg', 'length', 'max'=>255),
			array('status, flag_state', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, custom_products_code_id, trans_code, method, sku, token, status, serialnumber, flag_state, error_msg, created_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'custom_products_code_id' => 'Custom Products Code',
			'trans_code' => 'Trans Code',
			'method' => 'Method',
			'sku' => 'Sku',
			'token' => 'Token',
			'status' => 'Status',
			'serialnumber' => 'Serialnumber',
			'flag_state' => 'Flag State',
			'error_msg' => 'Error Msg',
			'created_datetime' => 'Created Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id,true);
		$criteria->compare('custom_products_code_id',$this->custom_products_code_id,true);
		$criteria->compare('trans_code',$this->trans_code,true);
		$criteria->compare('method',$this->method,true);
		$criteria->compare('sku',$this->sku,true);
		$criteria->compare('token',$this->token,true);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('serialnumber',$this->serialnumber,true);
		$criteria->compare('flag_state',$this->flag_state,true);
		$criteria->compare('error_msg',$this->error_msg,true);
		$criteria->compare('created_datetime',$this->created_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}