<?php

/**
 * This is the model class for table "log_files".
 *
 * The followings are the available columns in table 'log_files':
 * @property integer $log_id
 * @property integer $log_userid
 * @property string $log_time
 * @property string $log_ip
 * @property string $log_details
 * @property string $log_categories
 */
class LogFilesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LogFilesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'log_files';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('log_details', 'required'),
			array('log_userid', 'numerical', 'integerOnly'=>true),
			array('log_ip, log_categories', 'length', 'max'=>15),
			array('log_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('log_id, log_userid, log_time, log_ip, log_details, log_categories', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'log_id' => 'Log',
			'log_userid' => 'Log Userid',
			'log_time' => 'Log Time',
			'log_ip' => 'Log Ip',
			'log_details' => 'Log Details',
			'log_categories' => 'Log Categories',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('log_id',$this->log_id);
		$criteria->compare('log_userid',$this->log_userid);
		$criteria->compare('log_time',$this->log_time,true);
		$criteria->compare('log_ip',$this->log_ip,true);
		$criteria->compare('log_details',$this->log_details,true);
		$criteria->compare('log_categories',$this->log_categories,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}