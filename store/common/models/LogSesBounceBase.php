<?php

/**
 * This is the model class for table "log_ses_bounce".
 *
 * The followings are the available columns in table 'log_ses_bounce':
 * @property string $email
 * @property string $error_string
 * @property string $created_datetime
 */
class LogSesBounceBase extends MainModel {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return LogSesBounceBase the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'log_ses_bounce';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('email', 'length', 'max' => 255),
            array('error_string, created_datetime', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('email, error_string, created_datetime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'email' => 'Email',
            'error_string' => 'Error String',
            'created_datetime' => 'Created Datetime',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('email', $this->email, true);
        $criteria->compare('error_string', $this->error_string, true);
        $criteria->compare('created_datetime', $this->created_datetime, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function emailExists($email) {
        $str = explode(" ", $email);
        $email = trim(array_pop($str), "<>");
        
        $criteria = new CDbCriteria();
        $criteria->condition = 'email = :email';
        $criteria->params = array(':email' => $email);
        $result = $this->model()->exists($criteria);
        return $result;
    }

    public function saveSesBounce($sesBounce) {
        $str = explode(" ", $sesBounce['email']);
        $sesBounce['email'] = trim(array_pop($str), "<>");

        $this->setIsNewRecord(true);
        unset($this->email);
        $this->attributes = $sesBounce;
        $this->save();
    }

}
