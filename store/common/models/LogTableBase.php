<?php

/**
 * This is the model class for table "log_table".
 *
 * The followings are the available columns in table 'log_table':
 * @property integer $log_id
 * @property string $log_admin_id
 * @property string $log_ip
 * @property string $log_time
 * @property string $log_products_id
 * @property string $log_system_messages
 * @property string $log_user_messages
 * @property string $log_field_name
 * @property string $log_from_value
 * @property string $log_to_value
 */
class LogTableBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return LogTableBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'log_table';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('log_system_messages, log_user_messages', 'required'),
            array(
                'log_admin_id, log_products_id, log_field_name, log_from_value, log_to_value',
                'length',
                'max' => 255
            ),
            array('log_ip', 'length', 'max' => 128),
            array('log_time', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'log_id, log_admin_id, log_ip, log_time, log_products_id, log_system_messages, log_user_messages, log_field_name, log_from_value, log_to_value',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'log_id' => 'Log',
            'log_admin_id' => 'Log Admin',
            'log_ip' => 'Log Ip',
            'log_time' => 'Log Time',
            'log_products_id' => 'Log Products',
            'log_system_messages' => 'Log System Messages',
            'log_user_messages' => 'Log User Messages',
            'log_field_name' => 'Log Field Name',
            'log_from_value' => 'Log From Value',
            'log_to_value' => 'Log To Value',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('log_id', $this->log_id);
        $criteria->compare('log_admin_id', $this->log_admin_id, true);
        $criteria->compare('log_ip', $this->log_ip, true);
        $criteria->compare('log_time', $this->log_time, true);
        $criteria->compare('log_products_id', $this->log_products_id, true);
        $criteria->compare('log_system_messages', $this->log_system_messages, true);
        $criteria->compare('log_user_messages', $this->log_user_messages, true);
        $criteria->compare('log_field_name', $this->log_field_name, true);
        $criteria->compare('log_from_value', $this->log_from_value, true);
        $criteria->compare('log_to_value', $this->log_to_value, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

}