<?php

/**
 * This is the model class for table "maxmind_history".
 *
 * The followings are the available columns in table 'maxmind_history':
 * @property integer $orders_id
 * @property string $maxmind_history_date
 * @property integer $distance
 * @property string $country_match
 * @property string $ip_country_code
 * @property string $free_mail
 * @property string $anonymous_proxy
 * @property string $score
 * @property string $bin_match
 * @property string $bin_country_code
 * @property string $error
 * @property string $proxy_score
 * @property string $spam_score
 * @property string $ip_region
 * @property string $ip_city
 * @property string $ip_latitude
 * @property string $ip_longitude
 * @property string $bin_name
 * @property string $ip_isp
 * @property string $ip_organization
 * @property string $ip_accuracy_radius
 * @property string $ip_postal_code
 * @property string $ip_area_code
 * @property string $ip_time_zone
 * @property string $ip_user_type
 * @property string $ip_net_speed_cell
 * @property string $is_trans_proxy
 * @property string $ip_corporate_proxy
 * @property string $bin_name_match
 * @property string $bin_phone_match
 * @property string $bin_phone
 * @property string $customer_phone_in_billing_location
 * @property string $high_risk_country
 * @property string $queries_remaining
 * @property string $city_postal_match
 * @property string $shipping_city_postal_match
 * @property string $maxmind_history_source
 * @property string $maxmind_id
 */
class MaxmindHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MaxmindHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'maxmind_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('ip_accuracy_radius, ip_postal_code, ip_area_code, ip_time_zone, ip_user_type, ip_net_speed_cell, is_trans_proxy, ip_corporate_proxy', 'required'),
			array('orders_id, distance', 'numerical', 'integerOnly'=>true),
			array('country_match, free_mail, anonymous_proxy, bin_match, bin_country_code, ip_postal_code, ip_area_code, is_trans_proxy, ip_corporate_proxy, bin_name_match, bin_phone_match, bin_phone, customer_phone_in_billing_location, city_postal_match, shipping_city_postal_match', 'length', 'max'=>9),
			array('ip_country_code, score', 'length', 'max'=>2),
			array('error, ip_region, ip_city, bin_name, ip_isp, ip_organization, queries_remaining, maxmind_id', 'length', 'max'=>64),
			array('proxy_score, spam_score', 'length', 'max'=>4),
			array('ip_latitude, ip_longitude', 'length', 'max'=>5),
			array('ip_accuracy_radius', 'length', 'max'=>11),
			array('ip_time_zone, ip_user_type', 'length', 'max'=>32),
			array('ip_net_speed_cell', 'length', 'max'=>16),
			array('high_risk_country', 'length', 'max'=>3),
			array('maxmind_history_source', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, maxmind_history_date, distance, country_match, ip_country_code, free_mail, anonymous_proxy, score, bin_match, bin_country_code, error, proxy_score, spam_score, ip_region, ip_city, ip_latitude, ip_longitude, bin_name, ip_isp, ip_organization, ip_accuracy_radius, ip_postal_code, ip_area_code, ip_time_zone, ip_user_type, ip_net_speed_cell, is_trans_proxy, ip_corporate_proxy, bin_name_match, bin_phone_match, bin_phone, customer_phone_in_billing_location, high_risk_country, queries_remaining, city_postal_match, shipping_city_postal_match, maxmind_history_source, maxmind_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'maxmind_history_date' => 'Maxmind History Date',
			'distance' => 'Distance',
			'country_match' => 'Country Match',
			'ip_country_code' => 'Ip Country Code',
			'free_mail' => 'Free Mail',
			'anonymous_proxy' => 'Anonymous Proxy',
			'score' => 'Score',
			'bin_match' => 'Bin Match',
			'bin_country_code' => 'Bin Country Code',
			'error' => 'Error',
			'proxy_score' => 'Proxy Score',
			'spam_score' => 'Spam Score',
			'ip_region' => 'Ip Region',
			'ip_city' => 'Ip City',
			'ip_latitude' => 'Ip Latitude',
			'ip_longitude' => 'Ip Longitude',
			'bin_name' => 'Bin Name',
			'ip_isp' => 'Ip Isp',
			'ip_organization' => 'Ip Organization',
			'ip_accuracy_radius' => 'Ip Accuracy Radius',
			'ip_postal_code' => 'Ip Postal Code',
			'ip_area_code' => 'Ip Area Code',
			'ip_time_zone' => 'Ip Time Zone',
			'ip_user_type' => 'Ip User Type',
			'ip_net_speed_cell' => 'Ip Net Speed Cell',
			'is_trans_proxy' => 'Is Trans Proxy',
			'ip_corporate_proxy' => 'Ip Corporate Proxy',
			'bin_name_match' => 'Bin Name Match',
			'bin_phone_match' => 'Bin Phone Match',
			'bin_phone' => 'Bin Phone',
			'customer_phone_in_billing_location' => 'Customer Phone In Billing Location',
			'high_risk_country' => 'High Risk Country',
			'queries_remaining' => 'Queries Remaining',
			'city_postal_match' => 'City Postal Match',
			'shipping_city_postal_match' => 'Shipping City Postal Match',
			'maxmind_history_source' => 'Maxmind History Source',
			'maxmind_id' => 'Maxmind',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('maxmind_history_date',$this->maxmind_history_date,true);
		$criteria->compare('distance',$this->distance);
		$criteria->compare('country_match',$this->country_match,true);
		$criteria->compare('ip_country_code',$this->ip_country_code,true);
		$criteria->compare('free_mail',$this->free_mail,true);
		$criteria->compare('anonymous_proxy',$this->anonymous_proxy,true);
		$criteria->compare('score',$this->score,true);
		$criteria->compare('bin_match',$this->bin_match,true);
		$criteria->compare('bin_country_code',$this->bin_country_code,true);
		$criteria->compare('error',$this->error,true);
		$criteria->compare('proxy_score',$this->proxy_score,true);
		$criteria->compare('spam_score',$this->spam_score,true);
		$criteria->compare('ip_region',$this->ip_region,true);
		$criteria->compare('ip_city',$this->ip_city,true);
		$criteria->compare('ip_latitude',$this->ip_latitude,true);
		$criteria->compare('ip_longitude',$this->ip_longitude,true);
		$criteria->compare('bin_name',$this->bin_name,true);
		$criteria->compare('ip_isp',$this->ip_isp,true);
		$criteria->compare('ip_organization',$this->ip_organization,true);
		$criteria->compare('ip_accuracy_radius',$this->ip_accuracy_radius,true);
		$criteria->compare('ip_postal_code',$this->ip_postal_code,true);
		$criteria->compare('ip_area_code',$this->ip_area_code,true);
		$criteria->compare('ip_time_zone',$this->ip_time_zone,true);
		$criteria->compare('ip_user_type',$this->ip_user_type,true);
		$criteria->compare('ip_net_speed_cell',$this->ip_net_speed_cell,true);
		$criteria->compare('is_trans_proxy',$this->is_trans_proxy,true);
		$criteria->compare('ip_corporate_proxy',$this->ip_corporate_proxy,true);
		$criteria->compare('bin_name_match',$this->bin_name_match,true);
		$criteria->compare('bin_phone_match',$this->bin_phone_match,true);
		$criteria->compare('bin_phone',$this->bin_phone,true);
		$criteria->compare('customer_phone_in_billing_location',$this->customer_phone_in_billing_location,true);
		$criteria->compare('high_risk_country',$this->high_risk_country,true);
		$criteria->compare('queries_remaining',$this->queries_remaining,true);
		$criteria->compare('city_postal_match',$this->city_postal_match,true);
		$criteria->compare('shipping_city_postal_match',$this->shipping_city_postal_match,true);
		$criteria->compare('maxmind_history_source',$this->maxmind_history_source,true);
		$criteria->compare('maxmind_id',$this->maxmind_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}