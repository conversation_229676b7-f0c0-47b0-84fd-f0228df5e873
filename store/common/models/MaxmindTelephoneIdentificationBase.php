<?php

/**
 * This is the model class for table "maxmind_telephone_identification".
 *
 * The followings are the available columns in table 'maxmind_telephone_identification':
 * @property integer $customers_id
 * @property integer $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property integer $customers_telephone_type
 * @property string $city
 * @property string $state
 * @property string $postcode
 * @property string $countries_name
 * @property string $latitude
 * @property string $longitude
 * @property string $error
 * @property string $provider
 * @property string $maxmind_telephone_identification_id
 * @property string $requested_date
 */
class MaxmindTelephoneIdentificationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MaxmindTelephoneIdentificationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'maxmind_telephone_identification';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, customers_country_international_dialing_code, customers_telephone_type', 'numerical', 'integerOnly'=>true),
			array('customers_telephone, city, state', 'length', 'max'=>32),
			array('postcode, latitude, longitude', 'length', 'max'=>10),
			array('countries_name, error, maxmind_telephone_identification_id', 'length', 'max'=>64),
			array('provider', 'length', 'max'=>8),
			array('requested_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, customers_country_international_dialing_code, customers_telephone, customers_telephone_type, city, state, postcode, countries_name, latitude, longitude, error, provider, maxmind_telephone_identification_id, requested_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'customers_country_international_dialing_code' => 'Customers Country International Dialing Code',
			'customers_telephone' => 'Customers Telephone',
			'customers_telephone_type' => 'Customers Telephone Type',
			'city' => 'City',
			'state' => 'State',
			'postcode' => 'Postcode',
			'countries_name' => 'Countries Name',
			'latitude' => 'Latitude',
			'longitude' => 'Longitude',
			'error' => 'Error',
			'provider' => 'Provider',
			'maxmind_telephone_identification_id' => 'Maxmind Telephone Identification',
			'requested_date' => 'Requested Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('customers_country_international_dialing_code',$this->customers_country_international_dialing_code);
		$criteria->compare('customers_telephone',$this->customers_telephone,true);
		$criteria->compare('customers_telephone_type',$this->customers_telephone_type);
		$criteria->compare('city',$this->city,true);
		$criteria->compare('state',$this->state,true);
		$criteria->compare('postcode',$this->postcode,true);
		$criteria->compare('countries_name',$this->countries_name,true);
		$criteria->compare('latitude',$this->latitude,true);
		$criteria->compare('longitude',$this->longitude,true);
		$criteria->compare('error',$this->error,true);
		$criteria->compare('provider',$this->provider,true);
		$criteria->compare('maxmind_telephone_identification_id',$this->maxmind_telephone_identification_id,true);
		$criteria->compare('requested_date',$this->requested_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}