<?php

/**
 * This is the model class for table "maybank".
 *
 * The followings are the available columns in table 'maybank':
 * @property integer $orders_id
 * @property string $maybank_status
 * @property string $maybank_corporate
 * @property string $maybank_account
 * @property string $maybank_currency
 * @property string $maybank_amount
 * @property string $maybank_approval_code
 * @property string $maybank_reference_id
 */
class MaybankBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MaybankBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'maybank';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('maybank_status', 'length', 'max'=>2),
			array('maybank_corporate, maybank_approval_code', 'length', 'max'=>255),
			array('maybank_account', 'length', 'max'=>30),
			array('maybank_currency', 'length', 'max'=>3),
			array('maybank_amount', 'length', 'max'=>15),
			array('maybank_reference_id', 'length', 'max'=>20),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, maybank_status, maybank_corporate, maybank_account, maybank_currency, maybank_amount, maybank_approval_code, maybank_reference_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'maybank_status' => 'Maybank Status',
			'maybank_corporate' => 'Maybank Corporate',
			'maybank_account' => 'Maybank Account',
			'maybank_currency' => 'Maybank Currency',
			'maybank_amount' => 'Maybank Amount',
			'maybank_approval_code' => 'Maybank Approval Code',
			'maybank_reference_id' => 'Maybank Reference',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('maybank_status',$this->maybank_status,true);
		$criteria->compare('maybank_corporate',$this->maybank_corporate,true);
		$criteria->compare('maybank_account',$this->maybank_account,true);
		$criteria->compare('maybank_currency',$this->maybank_currency,true);
		$criteria->compare('maybank_amount',$this->maybank_amount,true);
		$criteria->compare('maybank_approval_code',$this->maybank_approval_code,true);
		$criteria->compare('maybank_reference_id',$this->maybank_reference_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}