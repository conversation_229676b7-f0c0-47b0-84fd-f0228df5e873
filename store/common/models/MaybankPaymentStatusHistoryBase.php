<?php

/**
 * This is the model class for table "maybank_payment_status_history".
 *
 * The followings are the available columns in table 'maybank_payment_status_history':
 * @property integer $maybank_payment_status_history_id
 * @property integer $orders_id
 * @property string $maybank_date
 * @property string $maybank_status
 * @property string $maybank_description
 */
class MaybankPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MaybankPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'maybank_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('maybank_status', 'length', 'max'=>2),
			array('maybank_description', 'length', 'max'=>64),
			array('maybank_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('maybank_payment_status_history_id, orders_id, maybank_date, maybank_status, maybank_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'maybank_payment_status_history_id' => 'Maybank Payment Status History',
			'orders_id' => 'Orders',
			'maybank_date' => 'Maybank Date',
			'maybank_status' => 'Maybank Status',
			'maybank_description' => 'Maybank Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('maybank_payment_status_history_id',$this->maybank_payment_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('maybank_date',$this->maybank_date,true);
		$criteria->compare('maybank_status',$this->maybank_status,true);
		$criteria->compare('maybank_description',$this->maybank_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}