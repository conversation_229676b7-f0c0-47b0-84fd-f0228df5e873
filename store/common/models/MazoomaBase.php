<?php

/**
 * This is the model class for table "mazooma".
 *
 * The followings are the available columns in table 'mazooma':
 * @property integer $orders_id
 * @property string $mazooma_transaction_number
 * @property string $mazooma_amount
 * @property string $mazooma_currency
 * @property string $mazooma_status
 * @property string $mazooma_user_id
 * @property string $mazooma_fee
 * @property string $mazooma_error_code
 */
class MazoomaBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MazoomaBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'mazooma';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('mazooma_transaction_number', 'length', 'max'=>30),
			array('mazooma_amount, mazooma_fee', 'length', 'max'=>15),
			array('mazooma_currency', 'length', 'max'=>3),
			array('mazooma_status', 'length', 'max'=>1),
			array('mazooma_user_id', 'length', 'max'=>20),
			array('mazooma_error_code', 'length', 'max'=>4),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, mazooma_transaction_number, mazooma_amount, mazooma_currency, mazooma_status, mazooma_user_id, mazooma_fee, mazooma_error_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'mazooma_transaction_number' => 'Mazooma Transaction Number',
			'mazooma_amount' => 'Mazooma Amount',
			'mazooma_currency' => 'Mazooma Currency',
			'mazooma_status' => 'Mazooma Status',
			'mazooma_user_id' => 'Mazooma User',
			'mazooma_fee' => 'Mazooma Fee',
			'mazooma_error_code' => 'Mazooma Error Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('mazooma_transaction_number',$this->mazooma_transaction_number,true);
		$criteria->compare('mazooma_amount',$this->mazooma_amount,true);
		$criteria->compare('mazooma_currency',$this->mazooma_currency,true);
		$criteria->compare('mazooma_status',$this->mazooma_status,true);
		$criteria->compare('mazooma_user_id',$this->mazooma_user_id,true);
		$criteria->compare('mazooma_fee',$this->mazooma_fee,true);
		$criteria->compare('mazooma_error_code',$this->mazooma_error_code,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}