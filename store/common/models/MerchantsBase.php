<?php

/**
 * This is the model class for table "merchants".
 *
 * The followings are the available columns in table 'merchants':
 * @property string $merchants_id
 * @property string $merchants_name
 * @property integer $merchants_status
 * @property string $merchants_remark
 * @property string $last_modified
 * @property string $date_added
 * @property integer $last_modified_by
 * @property integer $sort_order
 */
class MerchantsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MerchantsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'merchants';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('merchants_remark', 'required'),
			array('merchants_status, last_modified_by, sort_order', 'numerical', 'integerOnly'=>true),
			array('merchants_name', 'length', 'max'=>32),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('merchants_id, merchants_name, merchants_status, merchants_remark, last_modified, date_added, last_modified_by, sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'merchants_id' => 'Merchants',
			'merchants_name' => 'Merchants Name',
			'merchants_status' => 'Merchants Status',
			'merchants_remark' => 'Merchants Remark',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'last_modified_by' => 'Last Modified By',
			'sort_order' => 'Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('merchants_id',$this->merchants_id,true);
		$criteria->compare('merchants_name',$this->merchants_name,true);
		$criteria->compare('merchants_status',$this->merchants_status);
		$criteria->compare('merchants_remark',$this->merchants_remark,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified_by',$this->last_modified_by);
		$criteria->compare('sort_order',$this->sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}