<?php

/**
 * This is the model class for table "merchants_configuration".
 *
 * The followings are the available columns in table 'merchants_configuration':
 * @property string $merchants_configuration_id
 * @property string $merchants_id
 * @property string $merchants_configuration_title
 * @property string $merchants_configuration_key
 * @property string $merchants_configuration_value
 * @property string $merchants_configuration_description
 * @property integer $sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property integer $last_modified_by
 * @property string $use_function
 * @property string $set_function
 */
class MerchantsConfigurationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MerchantsConfigurationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'merchants_configuration';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('merchants_configuration_value, set_function', 'required'),
			array('sort_order, last_modified_by', 'numerical', 'integerOnly'=>true),
			array('merchants_id', 'length', 'max'=>11),
			array('merchants_configuration_title, merchants_configuration_key', 'length', 'max'=>64),
			array('merchants_configuration_description, use_function', 'length', 'max'=>255),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('merchants_configuration_id, merchants_id, merchants_configuration_title, merchants_configuration_key, merchants_configuration_value, merchants_configuration_description, sort_order, last_modified, date_added, last_modified_by, use_function, set_function', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'merchants_configuration_id' => 'Merchants Configuration',
			'merchants_id' => 'Merchants',
			'merchants_configuration_title' => 'Merchants Configuration Title',
			'merchants_configuration_key' => 'Merchants Configuration Key',
			'merchants_configuration_value' => 'Merchants Configuration Value',
			'merchants_configuration_description' => 'Merchants Configuration Description',
			'sort_order' => 'Sort Order',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'last_modified_by' => 'Last Modified By',
			'use_function' => 'Use Function',
			'set_function' => 'Set Function',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('merchants_configuration_id',$this->merchants_configuration_id,true);
		$criteria->compare('merchants_id',$this->merchants_id,true);
		$criteria->compare('merchants_configuration_title',$this->merchants_configuration_title,true);
		$criteria->compare('merchants_configuration_key',$this->merchants_configuration_key,true);
		$criteria->compare('merchants_configuration_value',$this->merchants_configuration_value,true);
		$criteria->compare('merchants_configuration_description',$this->merchants_configuration_description,true);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified_by',$this->last_modified_by);
		$criteria->compare('use_function',$this->use_function,true);
		$criteria->compare('set_function',$this->set_function,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}