<?php

/**
 * This is the model class for table "mobile_customers_basket".
 *
 * The followings are the available columns in table 'mobile_customers_basket':
 * @property integer $cart_id
 * @property integer $customers_id
 * @property integer $products_id
 * @property integer $customers_basket_quantity
 * @property string $customers_basket_date_added
 * @property integer $products_categories_id
 * @property string $customers_basket_custom_key
 * @property string $customers_basket_custom_value
 */
class MobileCustomersBasketBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MobileCustomersBasketBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'mobile_customers_basket';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cart_id, customers_id, products_id, customers_basket_date_added, products_categories_id, customers_basket_custom_key, customers_basket_custom_value', 'required'),
			array('cart_id, customers_id, products_id, customers_basket_quantity, products_categories_id', 'numerical', 'integerOnly'=>true),
			array('customers_basket_custom_key', 'length', 'max'=>20),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cart_id, customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id, customers_basket_custom_key, customers_basket_custom_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cart_id' => 'Cart',
			'customers_id' => 'Customers',
			'products_id' => 'Products',
			'customers_basket_quantity' => 'Customers Basket Quantity',
			'customers_basket_date_added' => 'Customers Basket Date Added',
			'products_categories_id' => 'Products Categories',
			'customers_basket_custom_key' => 'Customers Basket Custom Key',
			'customers_basket_custom_value' => 'Customers Basket Custom Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cart_id',$this->cart_id);
		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('customers_basket_quantity',$this->customers_basket_quantity);
		$criteria->compare('customers_basket_date_added',$this->customers_basket_date_added,true);
		$criteria->compare('products_categories_id',$this->products_categories_id);
		$criteria->compare('customers_basket_custom_key',$this->customers_basket_custom_key,true);
		$criteria->compare('customers_basket_custom_value',$this->customers_basket_custom_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}