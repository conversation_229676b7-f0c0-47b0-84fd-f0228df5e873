<?php

/**
 * This is the model class for table "mobile_money".
 *
 * The followings are the available columns in table 'mobile_money':
 * @property integer $orders_id
 * @property string $mobile_money_tran_id
 * @property string $mobile_money_tran_status
 * @property string $mobile_money_tran_errcode
 * @property double $mobile_money_amt
 * @property string $mobile_money_currency
 * @property string $mobile_money_ecash_apprcode
 * @property string $mobile_money_tran_mmprocessdt
 */
class MobileMoneyBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MobileMoneyBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'mobile_money';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('mobile_money_amt', 'numerical'),
			array('mobile_money_tran_id', 'length', 'max'=>20),
			array('mobile_money_tran_status', 'length', 'max'=>2),
			array('mobile_money_tran_errcode, mobile_money_ecash_apprcode', 'length', 'max'=>10),
			array('mobile_money_currency', 'length', 'max'=>3),
			array('mobile_money_tran_mmprocessdt', 'length', 'max'=>22),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, mobile_money_tran_id, mobile_money_tran_status, mobile_money_tran_errcode, mobile_money_amt, mobile_money_currency, mobile_money_ecash_apprcode, mobile_money_tran_mmprocessdt', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'mobile_money_tran_id' => 'Mobile Money Tran',
			'mobile_money_tran_status' => 'Mobile Money Tran Status',
			'mobile_money_tran_errcode' => 'Mobile Money Tran Errcode',
			'mobile_money_amt' => 'Mobile Money Amt',
			'mobile_money_currency' => 'Mobile Money Currency',
			'mobile_money_ecash_apprcode' => 'Mobile Money Ecash Apprcode',
			'mobile_money_tran_mmprocessdt' => 'Mobile Money Tran Mmprocessdt',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('mobile_money_tran_id',$this->mobile_money_tran_id,true);
		$criteria->compare('mobile_money_tran_status',$this->mobile_money_tran_status,true);
		$criteria->compare('mobile_money_tran_errcode',$this->mobile_money_tran_errcode,true);
		$criteria->compare('mobile_money_amt',$this->mobile_money_amt);
		$criteria->compare('mobile_money_currency',$this->mobile_money_currency,true);
		$criteria->compare('mobile_money_ecash_apprcode',$this->mobile_money_ecash_apprcode,true);
		$criteria->compare('mobile_money_tran_mmprocessdt',$this->mobile_money_tran_mmprocessdt,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}