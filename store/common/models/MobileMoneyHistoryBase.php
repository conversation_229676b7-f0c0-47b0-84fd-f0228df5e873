<?php

/**
 * This is the model class for table "mobile_money_history".
 *
 * The followings are the available columns in table 'mobile_money_history':
 * @property integer $mobile_money_history_id
 * @property integer $orders_id
 * @property string $money_money_request_date
 * @property string $mobile_money_tran_status
 * @property string $mobile_money_tran_description
 */
class MobileMoneyHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MobileMoneyHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'mobile_money_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('mobile_money_tran_status', 'length', 'max'=>2),
			array('mobile_money_tran_description', 'length', 'max'=>255),
			array('money_money_request_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('mobile_money_history_id, orders_id, money_money_request_date, mobile_money_tran_status, mobile_money_tran_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'mobile_money_history_id' => 'Mobile Money History',
			'orders_id' => 'Orders',
			'money_money_request_date' => 'Money Money Request Date',
			'mobile_money_tran_status' => 'Mobile Money Tran Status',
			'mobile_money_tran_description' => 'Mobile Money Tran Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('mobile_money_history_id',$this->mobile_money_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('money_money_request_date',$this->money_money_request_date,true);
		$criteria->compare('mobile_money_tran_status',$this->mobile_money_tran_status,true);
		$criteria->compare('mobile_money_tran_description',$this->mobile_money_tran_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}