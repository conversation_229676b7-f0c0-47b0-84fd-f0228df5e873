<?php

/**
 * This is the model class for table "moneybookers".
 *
 * The followings are the available columns in table 'moneybookers':
 * @property string $mb_trans_id
 * @property string $mb_mb_trans_id
 * @property integer $mb_status
 * @property string $mb_order_id
 * @property string $mb_payer_email
 * @property string $mb_paid_currency
 * @property string $mb_paid_amount
 */
class MoneybookersBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MoneybookersBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'moneybookers';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('mb_status', 'numerical', 'integerOnly'=>true),
			array('mb_trans_id, mb_payer_email', 'length', 'max'=>255),
			array('mb_mb_trans_id', 'length', 'max'=>18),
			array('mb_order_id', 'length', 'max'=>11),
			array('mb_paid_currency', 'length', 'max'=>3),
			array('mb_paid_amount', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('mb_trans_id, mb_mb_trans_id, mb_status, mb_order_id, mb_payer_email, mb_paid_currency, mb_paid_amount', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'mb_trans_id' => 'Mb Trans',
			'mb_mb_trans_id' => 'Mb Mb Trans',
			'mb_status' => 'Mb Status',
			'mb_order_id' => 'Mb Order',
			'mb_payer_email' => 'Mb Payer Email',
			'mb_paid_currency' => 'Mb Paid Currency',
			'mb_paid_amount' => 'Mb Paid Amount',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('mb_trans_id',$this->mb_trans_id,true);
		$criteria->compare('mb_mb_trans_id',$this->mb_mb_trans_id,true);
		$criteria->compare('mb_status',$this->mb_status);
		$criteria->compare('mb_order_id',$this->mb_order_id,true);
		$criteria->compare('mb_payer_email',$this->mb_payer_email,true);
		$criteria->compare('mb_paid_currency',$this->mb_paid_currency,true);
		$criteria->compare('mb_paid_amount',$this->mb_paid_amount,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}