<?php

/**
 * This is the model class for table "moneybookers_countries".
 *
 * The followings are the available columns in table 'moneybookers_countries':
 * @property integer $osc_cID
 * @property string $mb_cID
 */
class MoneybookersCountriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MoneybookersCountriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'moneybookers_countries';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('osc_cID', 'numerical', 'integerOnly'=>true),
			array('mb_cID', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('osc_cID, mb_cID', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'osc_cID' => 'Osc C',
			'mb_cID' => 'Mb C',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('osc_cID',$this->osc_cID);
		$criteria->compare('mb_cID',$this->mb_cID,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}