<?php

/**
 * This is the model class for table "moneybookers_currencies".
 *
 * The followings are the available columns in table 'moneybookers_currencies':
 * @property string $mb_currID
 * @property string $mb_currName
 */
class MoneybookersCurrenciesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MoneybookersCurrenciesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'moneybookers_currencies';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('mb_currID', 'length', 'max'=>3),
			array('mb_currName', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('mb_currID, mb_currName', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'mb_currID' => 'Mb Curr',
			'mb_currName' => 'Mb Curr Name',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('mb_currID',$this->mb_currID,true);
		$criteria->compare('mb_currName',$this->mb_currName,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}