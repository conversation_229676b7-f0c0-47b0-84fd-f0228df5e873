<?php

/**
 * This is the model class for table "moneybookers_payment_status_history".
 *
 * The followings are the available columns in table 'moneybookers_payment_status_history':
 * @property integer $mb_payment_status_history_id
 * @property string $mb_trans_id
 * @property integer $mb_err_no
 * @property string $mb_err_txt
 * @property string $mb_date
 * @property integer $mb_status
 */
class MoneybookersPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MoneybookersPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'moneybookers_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('mb_err_no, mb_status', 'numerical', 'integerOnly'=>true),
			array('mb_trans_id, mb_err_txt', 'length', 'max'=>255),
			array('mb_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('mb_payment_status_history_id, mb_trans_id, mb_err_no, mb_err_txt, mb_date, mb_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'mb_payment_status_history_id' => 'Mb Payment Status History',
			'mb_trans_id' => 'Mb Trans',
			'mb_err_no' => 'Mb Err No',
			'mb_err_txt' => 'Mb Err Txt',
			'mb_date' => 'Mb Date',
			'mb_status' => 'Mb Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('mb_payment_status_history_id',$this->mb_payment_status_history_id);
		$criteria->compare('mb_trans_id',$this->mb_trans_id,true);
		$criteria->compare('mb_err_no',$this->mb_err_no);
		$criteria->compare('mb_err_txt',$this->mb_err_txt,true);
		$criteria->compare('mb_date',$this->mb_date,true);
		$criteria->compare('mb_status',$this->mb_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}