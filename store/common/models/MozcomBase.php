<?php

/**
 * This is the model class for table "mozcom".
 *
 * The followings are the available columns in table 'mozcom':
 * @property integer $orders_id
 * @property string $mozcom_status
 * @property string $mozcom_ref_num
 * @property string $mozcom_client_email
 * @property string $mozcom_client_name
 * @property string $mozcom_client_phone
 * @property string $mozcom_client_address
 * @property string $mozcom_digest2
 * @property string $mozcom_reason
 */
class MozcomBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MozcomBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'mozcom';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'numerical', 'integerOnly'=>true),
			array('mozcom_status', 'length', 'max'=>7),
			array('mozcom_ref_num, mozcom_client_phone', 'length', 'max'=>16),
			array('mozcom_client_email, mozcom_client_name', 'length', 'max'=>32),
			array('mozcom_client_address, mozcom_reason', 'length', 'max'=>255),
			array('mozcom_digest2', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, mozcom_status, mozcom_ref_num, mozcom_client_email, mozcom_client_name, mozcom_client_phone, mozcom_client_address, mozcom_digest2, mozcom_reason', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'mozcom_status' => 'Mozcom Status',
			'mozcom_ref_num' => 'Mozcom Ref Num',
			'mozcom_client_email' => 'Mozcom Client Email',
			'mozcom_client_name' => 'Mozcom Client Name',
			'mozcom_client_phone' => 'Mozcom Client Phone',
			'mozcom_client_address' => 'Mozcom Client Address',
			'mozcom_digest2' => 'Mozcom Digest2',
			'mozcom_reason' => 'Mozcom Reason',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('mozcom_status',$this->mozcom_status,true);
		$criteria->compare('mozcom_ref_num',$this->mozcom_ref_num,true);
		$criteria->compare('mozcom_client_email',$this->mozcom_client_email,true);
		$criteria->compare('mozcom_client_name',$this->mozcom_client_name,true);
		$criteria->compare('mozcom_client_phone',$this->mozcom_client_phone,true);
		$criteria->compare('mozcom_client_address',$this->mozcom_client_address,true);
		$criteria->compare('mozcom_digest2',$this->mozcom_digest2,true);
		$criteria->compare('mozcom_reason',$this->mozcom_reason,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}