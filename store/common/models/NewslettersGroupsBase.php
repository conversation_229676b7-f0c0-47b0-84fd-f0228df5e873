<?php

/**
 * This is the model class for table "newsletters_groups".
 *
 * The followings are the available columns in table 'newsletters_groups':
 * @property integer $newsletters_groups_id
 * @property string $newsletters_groups_name
 * @property string $module
 * @property integer $newsletters_groups_sort_order
 */
class NewslettersGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return NewslettersGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'newsletters_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('newsletters_groups_sort_order', 'numerical', 'integerOnly'=>true),
			array('newsletters_groups_name, module', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('newsletters_groups_id, newsletters_groups_name, module, newsletters_groups_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'newsletters_groups_id' => 'Newsletters Groups',
			'newsletters_groups_name' => 'Newsletters Groups Name',
			'module' => 'Module',
			'newsletters_groups_sort_order' => 'Newsletters Groups Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('newsletters_groups_id',$this->newsletters_groups_id);
		$criteria->compare('newsletters_groups_name',$this->newsletters_groups_name,true);
		$criteria->compare('module',$this->module,true);
		$criteria->compare('newsletters_groups_sort_order',$this->newsletters_groups_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}