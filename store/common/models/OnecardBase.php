<?php

/**
 * This is the model class for table "onecard".
 *
 * The followings are the available columns in table 'onecard':
 * @property integer $onecard_orders_id
 * @property string $onecard_status
 * @property string $onecard_voucher_code
 * @property integer $onecard_order_voucher_redeem
 * @property string $onecard_date
 * @property string $onecard_expired
 * @property integer $onecard_redeemed
 * @property string $onecard_redeemed_time
 * @property string $onecard_transaction_time
 * @property double $onecard_amount
 * @property string $onecard_currency
 */
class OnecardBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OnecardBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'onecard';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('onecard_orders_id, onecard_order_voucher_redeem, onecard_redeemed', 'numerical', 'integerOnly'=>true),
			array('onecard_amount', 'numerical'),
			array('onecard_status', 'length', 'max'=>16),
			array('onecard_voucher_code', 'length', 'max'=>255),
			array('onecard_currency', 'length', 'max'=>3),
			array('onecard_date, onecard_expired, onecard_redeemed_time, onecard_transaction_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('onecard_orders_id, onecard_status, onecard_voucher_code, onecard_order_voucher_redeem, onecard_date, onecard_expired, onecard_redeemed, onecard_redeemed_time, onecard_transaction_time, onecard_amount, onecard_currency', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'onecard_orders_id' => 'Onecard Orders',
			'onecard_status' => 'Onecard Status',
			'onecard_voucher_code' => 'Onecard Voucher Code',
			'onecard_order_voucher_redeem' => 'Onecard Order Voucher Redeem',
			'onecard_date' => 'Onecard Date',
			'onecard_expired' => 'Onecard Expired',
			'onecard_redeemed' => 'Onecard Redeemed',
			'onecard_redeemed_time' => 'Onecard Redeemed Time',
			'onecard_transaction_time' => 'Onecard Transaction Time',
			'onecard_amount' => 'Onecard Amount',
			'onecard_currency' => 'Onecard Currency',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('onecard_orders_id',$this->onecard_orders_id);
		$criteria->compare('onecard_status',$this->onecard_status,true);
		$criteria->compare('onecard_voucher_code',$this->onecard_voucher_code,true);
		$criteria->compare('onecard_order_voucher_redeem',$this->onecard_order_voucher_redeem);
		$criteria->compare('onecard_date',$this->onecard_date,true);
		$criteria->compare('onecard_expired',$this->onecard_expired,true);
		$criteria->compare('onecard_redeemed',$this->onecard_redeemed);
		$criteria->compare('onecard_redeemed_time',$this->onecard_redeemed_time,true);
		$criteria->compare('onecard_transaction_time',$this->onecard_transaction_time,true);
		$criteria->compare('onecard_amount',$this->onecard_amount);
		$criteria->compare('onecard_currency',$this->onecard_currency,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}