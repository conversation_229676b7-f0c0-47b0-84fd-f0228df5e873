<?php

/**
 * This is the model class for table "onecard_status_history".
 *
 * The followings are the available columns in table 'onecard_status_history':
 * @property integer $onecard_status_history_id
 * @property integer $onecard_orders_id
 * @property string $onecard_status_history_voucher_code
 * @property string $onecard_status_history_status
 * @property string $onecard_status_history_description
 * @property string $onecard_status_history_datetime
 * @property string $onecard_changed_by
 */
class OnecardStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OnecardStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'onecard_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('onecard_status_history_description', 'required'),
			array('onecard_orders_id', 'numerical', 'integerOnly'=>true),
			array('onecard_status_history_voucher_code', 'length', 'max'=>255),
			array('onecard_status_history_status', 'length', 'max'=>16),
			array('onecard_changed_by', 'length', 'max'=>128),
			array('onecard_status_history_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('onecard_status_history_id, onecard_orders_id, onecard_status_history_voucher_code, onecard_status_history_status, onecard_status_history_description, onecard_status_history_datetime, onecard_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'onecard_status_history_id' => 'Onecard Status History',
			'onecard_orders_id' => 'Onecard Orders',
			'onecard_status_history_voucher_code' => 'Onecard Status History Voucher Code',
			'onecard_status_history_status' => 'Onecard Status History Status',
			'onecard_status_history_description' => 'Onecard Status History Description',
			'onecard_status_history_datetime' => 'Onecard Status History Datetime',
			'onecard_changed_by' => 'Onecard Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('onecard_status_history_id',$this->onecard_status_history_id);
		$criteria->compare('onecard_orders_id',$this->onecard_orders_id);
		$criteria->compare('onecard_status_history_voucher_code',$this->onecard_status_history_voucher_code,true);
		$criteria->compare('onecard_status_history_status',$this->onecard_status_history_status,true);
		$criteria->compare('onecard_status_history_description',$this->onecard_status_history_description,true);
		$criteria->compare('onecard_status_history_datetime',$this->onecard_status_history_datetime,true);
		$criteria->compare('onecard_changed_by',$this->onecard_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}