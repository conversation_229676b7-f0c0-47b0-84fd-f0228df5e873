<?php

/**
 * This is the model class for table "orders".
 *
 * The followings are the available columns in table 'orders':
 * @property integer $orders_id
 * @property integer $customers_id
 * @property string $customers_name
 * @property string $customers_company
 * @property string $customers_street_address
 * @property string $customers_suburb
 * @property string $customers_city
 * @property string $customers_postcode
 * @property string $customers_state
 * @property string $customers_country
 * @property string $customers_telephone_country
 * @property string $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property string $customers_email_address
 * @property integer $customers_address_format_id
 * @property integer $customers_groups_id
 * @property string $delivery_name
 * @property string $delivery_company
 * @property string $delivery_street_address
 * @property string $delivery_suburb
 * @property string $delivery_city
 * @property string $delivery_postcode
 * @property string $delivery_state
 * @property string $delivery_country
 * @property integer $delivery_address_format_id
 * @property string $billing_name
 * @property string $billing_company
 * @property string $billing_street_address
 * @property string $billing_suburb
 * @property string $billing_city
 * @property string $billing_postcode
 * @property string $billing_state
 * @property string $billing_country
 * @property integer $billing_address_format_id
 * @property string $payment_method
 * @property integer $payment_methods_parent_id
 * @property integer $payment_methods_id
 * @property string $cc_type
 * @property string $cc_owner
 * @property string $cc_number
 * @property string $cc_expires
 * @property string $last_modified
 * @property string $date_purchased
 * @property integer $orders_status
 * @property integer $orders_cb_status
 * @property string $orders_date_finished
 * @property string $currency
 * @property string $remote_addr
 * @property string $currency_value
 * @property integer $paypal_ipn_id
 * @property string $pm_2CO_cc_owner_firstname
 * @property string $pm_2CO_cc_owner_lastname
 * @property integer $orders_locked_by
 * @property string $orders_locked_from_ip
 * @property string $orders_locked_datetime
 * @property string $orders_follow_up_datetime
 * @property string $orders_tag_ids
 * @property integer $orders_read_mode
 * @property integer $orders_aft_executed
 * @property integer $orders_rebated
 */
class OrdersBase extends MainModel
{
    //LL Flagged
    const LL_FLAG = 'Flag LL';
    const PAYPAL_EMAIL_FLAG = 'Paypal Email';

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array(
                'customers_id, customers_address_format_id, customers_groups_id, delivery_address_format_id, billing_address_format_id, payment_methods_parent_id, payment_methods_id, orders_status, orders_cb_status, paypal_ipn_id, orders_locked_by, orders_read_mode, orders_aft_executed, orders_rebated',
                'numerical',
                'integerOnly' => true
            ),
            array(
                'customers_name, customers_street_address, customers_country, customers_telephone_country, delivery_name, delivery_street_address, delivery_country, billing_name, billing_street_address, billing_country, payment_method, cc_owner, pm_2CO_cc_owner_firstname, pm_2CO_cc_owner_lastname',
                'length',
                'max' => 64
            ),
            array(
                'customers_company, customers_suburb, customers_city, customers_state, customers_telephone, delivery_company, delivery_suburb, delivery_city, delivery_state, billing_company, billing_suburb, billing_city, billing_state, cc_number',
                'length',
                'max' => 32
            ),
            array(
                'customers_postcode, delivery_postcode, billing_postcode',
                'length',
                'max' => 10
            ),
            array(
                'customers_country_international_dialing_code',
                'length',
                'max' => 5
            ),
            array(
                'customers_email_address',
                'length',
                'max' => 96
            ),
            array(
                'cc_type',
                'length',
                'max' => 20
            ),
            array(
                'remote_addr, orders_locked_from_ip',
                'length',
                'max' => 128
            ),
            array(
                'cc_expires',
                'length',
                'max' => 4
            ),
            array(
                'currency',
                'length',
                'max' => 3
            ),
            array(
                'currency_value',
                'length',
                'max' => 14
            ),
            array(
                'orders_tag_ids',
                'length',
                'max' => 255
            ),
            array(
                'last_modified, date_purchased, orders_date_finished, orders_locked_datetime, orders_follow_up_datetime',
                'safe'
            ), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'orders_id, customers_id, customers_name, customers_company, customers_street_address, customers_suburb, customers_city, customers_postcode, customers_state, customers_country, customers_telephone_country, customers_country_international_dialing_code, customers_telephone, customers_email_address, customers_address_format_id, customers_groups_id, delivery_name, delivery_company, delivery_street_address, delivery_suburb, delivery_city, delivery_postcode, delivery_state, delivery_country, delivery_address_format_id, billing_name, billing_company, billing_street_address, billing_suburb, billing_city, billing_postcode, billing_state, billing_country, billing_address_format_id, payment_method, payment_methods_parent_id, payment_methods_id, cc_type, cc_owner, cc_number, cc_expires, last_modified, date_purchased, orders_status, orders_cb_status, orders_date_finished, currency, remote_addr, currency_value, paypal_ipn_id, pm_2CO_cc_owner_firstname, pm_2CO_cc_owner_lastname, orders_locked_by, orders_locked_from_ip, orders_locked_datetime, orders_follow_up_datetime, orders_tag_ids, orders_read_mode, orders_aft_executed, orders_rebated',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'OrdersProductsBase' => array(
                self::HAS_MANY,
                'OrdersProductsBase',
                'orders_id'
            ),
            'OrdersExtraInfoBase' => array(
                self::HAS_MANY,
                'OrdersExtraInfoBase',
                'orders_id'
            )
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_id' => 'Orders',
            'customers_id' => 'Customers',
            'customers_name' => 'Customers Name',
            'customers_company' => 'Customers Company',
            'customers_street_address' => 'Customers Street Address',
            'customers_suburb' => 'Customers Suburb',
            'customers_city' => 'Customers City',
            'customers_postcode' => 'Customers Postcode',
            'customers_state' => 'Customers State',
            'customers_country' => 'Customers Country',
            'customers_telephone_country' => 'Customers Telephone Country',
            'customers_country_international_dialing_code' => 'Customers Country International Dialing Code',
            'customers_telephone' => 'Customers Telephone',
            'customers_email_address' => 'Customers Email Address',
            'customers_address_format_id' => 'Customers Address Format',
            'customers_groups_id' => 'Customers Groups',
            'delivery_name' => 'Delivery Name',
            'delivery_company' => 'Delivery Company',
            'delivery_street_address' => 'Delivery Street Address',
            'delivery_suburb' => 'Delivery Suburb',
            'delivery_city' => 'Delivery City',
            'delivery_postcode' => 'Delivery Postcode',
            'delivery_state' => 'Delivery State',
            'delivery_country' => 'Delivery Country',
            'delivery_address_format_id' => 'Delivery Address Format',
            'billing_name' => 'Billing Name',
            'billing_company' => 'Billing Company',
            'billing_street_address' => 'Billing Street Address',
            'billing_suburb' => 'Billing Suburb',
            'billing_city' => 'Billing City',
            'billing_postcode' => 'Billing Postcode',
            'billing_state' => 'Billing State',
            'billing_country' => 'Billing Country',
            'billing_address_format_id' => 'Billing Address Format',
            'payment_method' => 'Payment Method',
            'payment_methods_parent_id' => 'Payment Methods Parent',
            'payment_methods_id' => 'Payment Methods',
            'cc_type' => 'Cc Type',
            'cc_owner' => 'Cc Owner',
            'cc_number' => 'Cc Number',
            'cc_expires' => 'Cc Expires',
            'last_modified' => 'Last Modified',
            'date_purchased' => 'Date Purchased',
            'orders_status' => 'Orders Status',
            'orders_cb_status' => 'Orders Cb Status',
            'orders_date_finished' => 'Orders Date Finished',
            'currency' => 'Currency',
            'remote_addr' => 'Remote Addr',
            'currency_value' => 'Currency Value',
            'paypal_ipn_id' => 'Paypal Ipn',
            'pm_2CO_cc_owner_firstname' => 'Pm 2 Co Cc Owner Firstname',
            'pm_2CO_cc_owner_lastname' => 'Pm 2 Co Cc Owner Lastname',
            'orders_locked_by' => 'Orders Locked By',
            'orders_locked_from_ip' => 'Orders Locked From Ip',
            'orders_locked_datetime' => 'Orders Locked Datetime',
            'orders_follow_up_datetime' => 'Orders Follow Up Datetime',
            'orders_tag_ids' => 'Orders Tag Ids',
            'orders_read_mode' => 'Orders Read Mode',
            'orders_aft_executed' => 'Orders Aft Executed',
            'orders_rebated' => 'Orders Rebated',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('orders_id', $this->orders_id);
        $criteria->compare('customers_id', $this->customers_id);
        $criteria->compare('customers_name', $this->customers_name, true);
        $criteria->compare('customers_company', $this->customers_company, true);
        $criteria->compare('customers_street_address', $this->customers_street_address, true);
        $criteria->compare('customers_suburb', $this->customers_suburb, true);
        $criteria->compare('customers_city', $this->customers_city, true);
        $criteria->compare('customers_postcode', $this->customers_postcode, true);
        $criteria->compare('customers_state', $this->customers_state, true);
        $criteria->compare('customers_country', $this->customers_country, true);
        $criteria->compare('customers_telephone_country', $this->customers_telephone_country, true);
        $criteria->compare('customers_country_international_dialing_code', $this->customers_country_international_dialing_code, true);
        $criteria->compare('customers_telephone', $this->customers_telephone, true);
        $criteria->compare('customers_email_address', $this->customers_email_address, true);
        $criteria->compare('customers_address_format_id', $this->customers_address_format_id);
        $criteria->compare('customers_groups_id', $this->customers_groups_id);
        $criteria->compare('delivery_name', $this->delivery_name, true);
        $criteria->compare('delivery_company', $this->delivery_company, true);
        $criteria->compare('delivery_street_address', $this->delivery_street_address, true);
        $criteria->compare('delivery_suburb', $this->delivery_suburb, true);
        $criteria->compare('delivery_city', $this->delivery_city, true);
        $criteria->compare('delivery_postcode', $this->delivery_postcode, true);
        $criteria->compare('delivery_state', $this->delivery_state, true);
        $criteria->compare('delivery_country', $this->delivery_country, true);
        $criteria->compare('delivery_address_format_id', $this->delivery_address_format_id);
        $criteria->compare('billing_name', $this->billing_name, true);
        $criteria->compare('billing_company', $this->billing_company, true);
        $criteria->compare('billing_street_address', $this->billing_street_address, true);
        $criteria->compare('billing_suburb', $this->billing_suburb, true);
        $criteria->compare('billing_city', $this->billing_city, true);
        $criteria->compare('billing_postcode', $this->billing_postcode, true);
        $criteria->compare('billing_state', $this->billing_state, true);
        $criteria->compare('billing_country', $this->billing_country, true);
        $criteria->compare('billing_address_format_id', $this->billing_address_format_id);
        $criteria->compare('payment_method', $this->payment_method, true);
        $criteria->compare('payment_methods_parent_id', $this->payment_methods_parent_id);
        $criteria->compare('payment_methods_id', $this->payment_methods_id);
        $criteria->compare('cc_type', $this->cc_type, true);
        $criteria->compare('cc_owner', $this->cc_owner, true);
        $criteria->compare('cc_number', $this->cc_number, true);
        $criteria->compare('cc_expires', $this->cc_expires, true);
        $criteria->compare('last_modified', $this->last_modified, true);
        $criteria->compare('date_purchased', $this->date_purchased, true);
        $criteria->compare('orders_status', $this->orders_status);
        $criteria->compare('orders_cb_status', $this->orders_cb_status);
        $criteria->compare('orders_date_finished', $this->orders_date_finished, true);
        $criteria->compare('currency', $this->currency, true);
        $criteria->compare('remote_addr', $this->remote_addr, true);
        $criteria->compare('currency_value', $this->currency_value, true);
        $criteria->compare('paypal_ipn_id', $this->paypal_ipn_id);
        $criteria->compare('pm_2CO_cc_owner_firstname', $this->pm_2CO_cc_owner_firstname, true);
        $criteria->compare('pm_2CO_cc_owner_lastname', $this->pm_2CO_cc_owner_lastname, true);
        $criteria->compare('orders_locked_by', $this->orders_locked_by);
        $criteria->compare('orders_locked_from_ip', $this->orders_locked_from_ip, true);
        $criteria->compare('orders_locked_datetime', $this->orders_locked_datetime, true);
        $criteria->compare('orders_follow_up_datetime', $this->orders_follow_up_datetime, true);
        $criteria->compare('orders_tag_ids', $this->orders_tag_ids, true);
        $criteria->compare('orders_read_mode', $this->orders_read_mode);
        $criteria->compare('orders_aft_executed', $this->orders_aft_executed);
        $criteria->compare('orders_rebated', $this->orders_rebated);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }


    public function getPendingOrderCount($cid, $period)
    {
        $c = new CDbCriteria;
        $c->select = array('t.orders_id');
        $c->condition = 't.customers_id = :cid 
            AND t.date_purchased > DATE_SUB(CURDATE(), INTERVAL :period MINUTE) 
            AND t.orders_status = 1';
        $c->params = array(
            ':cid' => $cid,
            ':period' => $period
        );
        $c->with = array(
            'OrdersExtraInfoBase' => array(
                'select' => array('OrdersExtraInfoBase.orders_id'),
                'on' => 'OrdersExtraInfoBase.orders_id = t.orders_id',
                'condition' => "OrdersExtraInfoBase.orders_extra_info_key = 'site_id'
                    AND OrdersExtraInfoBase.orders_extra_info_value = :site_id ",
                'params' => array(':site_id' => Yii::app()->params['SITE_ID']),
                'joinType' => 'INNER JOIN'
            )
        );

        return $this->count($c);
    }

    public function isFlagByType($flag_type, $id_array = array())
    {
        foreach ($id_array as $id) {
            $flag_label = $this->getFlagLabelByID($id);

            if ($flag_label == $flag_type) {
                return true;
            }
        }

        return false;
    }

    public function getFlagLabelByID($id)
    {
        $order_tag_info = '';
        if (!empty($id)) {
            $order_tag = OrdersTagBase::model()->findByPk($id);
            $order_tag_info = isset($order_tag->orders_tag_name) && !empty($order_tag->orders_tag_name) ? $order_tag->orders_tag_name : '';
        }

        return $order_tag_info;
    }

    public function isPaymentEmailRequiredVerification($pm_parent_id = 0, $customer_id = 0, $orders_id)
    {
        $return_array = array();

        if ($pm_parent_id && $customer_id) {
            $m_pm = PaymentMethodsBase::model()->findByPk($pm_parent_id);
            $pm_email = '';

            switch ($m_pm->payment_methods_filename) {
                case 'paypal.php':
                    $m_pp = PaypalBase::model()->findByAttributes(array('invoice' => $orders_id));
                    $pm_email = (isset($m_pp->payer_email) ? $m_pp->payer_email : '');
                    break;
                case 'paypalEC.php':
                    $m_pp = PaypalecBase::model()->findByAttributes(array('paypal_order_id' => $orders_id));
                    $pm_email = (isset($m_pp->payer_email) ? $m_pp->payer_email : '');
                    break;
            }

            if ($pm_email) {
                $m_civ = CustomersInfoVerificationBase::model()->findByPk(array(
                    'customers_id' => $customer_id,
                    'customers_info_value' => $pm_email,
                    'info_verification_type' => 'email'
                ));

                if (isset($m_civ->info_verified) && $m_civ->info_verified == 1) {
                    $return_array = array(
                        'payer_mail_verified_dt' => $m_civ->customers_info_verification_date
                    );
                } else {
                    # Email has not verified
                    $return_array['payer_mail'] = $pm_email;
                }
            }
        }

        return $return_array;
    }

    public function verifyOrdersProducts($opId)
    {
        $criteria = new CDbCriteria();
        $criteria->condition = 'customers_id = :customerId AND orders_status IN (2, 3)';
        $criteria->params = array(':customerId' => Yii::app()->user->id);
        $criteria->with = array(
            'OrdersProductsBase' => array(
                'condition' => 'orders_products_id = :opId',
                'params' => array(':opId' => $opId)
            )
        );
        $result = $this->model()->exists($criteria);
        return $result;
    }

    public function verifyOrders($order_id)
    {
        $criteria = new CDbCriteria();
        $criteria->condition = 'customers_id = :customerId AND orders_status IN (2, 3) AND orders_id = :orders_id';
        $criteria->params = array(':customerId' => Yii::app()->user->id, ':orders_id' => $order_id);
        $result = $this->model()->exists($criteria);
        return $result;
    }
}