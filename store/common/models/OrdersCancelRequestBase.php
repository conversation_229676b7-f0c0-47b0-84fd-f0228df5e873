<?php

/**
 * This is the model class for table "orders_cancel_request".
 *
 * The followings are the available columns in table 'orders_cancel_request':
 * @property integer $orders_id
 */
class OrdersCancelRequestBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersCancelRequestBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_cancel_request';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_id', 'numerical', 'integerOnly' => true), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('orders_id', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_id' => 'Orders',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;
        $criteria->compare('orders_id', $this->orders_id);
        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function checkRequestExists($orderId)
    {
        $criteria = new CDbCriteria();
        $criteria->condition = 'orders_id = :orderId';
        $criteria->params = array(':orderId' => $orderId);
        $result = $this->model()->exists($criteria);
        return $result;
    }

    public function saveCancelRequest($cancelRequestData)
    {
        $this->setIsNewRecord(true);
        $this->attributes = $cancelRequestData;
        $this->save();
    }
}