<?php

/**
 * This is the model class for table "orders_comments".
 *
 * The followings are the available columns in table 'orders_comments':
 * @property integer $orders_comments_id
 * @property string $orders_comments_title
 * @property string $orders_comments_text
 * @property integer $orders_comments_sort_order
 * @property integer $orders_comments_status
 * @property string $orders_comments_filename
 */
class OrdersCommentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersCommentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_comments';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_comments_text', 'required'),
			array('orders_comments_sort_order, orders_comments_status', 'numerical', 'integerOnly'=>true),
			array('orders_comments_title, orders_comments_filename', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_comments_id, orders_comments_title, orders_comments_text, orders_comments_sort_order, orders_comments_status, orders_comments_filename', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_comments_id' => 'Orders Comments',
			'orders_comments_title' => 'Orders Comments Title',
			'orders_comments_text' => 'Orders Comments Text',
			'orders_comments_sort_order' => 'Orders Comments Sort Order',
			'orders_comments_status' => 'Orders Comments Status',
			'orders_comments_filename' => 'Orders Comments Filename',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_comments_id',$this->orders_comments_id);
		$criteria->compare('orders_comments_title',$this->orders_comments_title,true);
		$criteria->compare('orders_comments_text',$this->orders_comments_text,true);
		$criteria->compare('orders_comments_sort_order',$this->orders_comments_sort_order);
		$criteria->compare('orders_comments_status',$this->orders_comments_status);
		$criteria->compare('orders_comments_filename',$this->orders_comments_filename,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}