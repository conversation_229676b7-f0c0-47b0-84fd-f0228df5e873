<?php

/**
 * This is the model class for table "orders_compensate_products".
 *
 * The followings are the available columns in table 'orders_compensate_products':
 * @property integer $orders_products_id
 * @property integer $orders_id
 * @property string $compensate_for_orders_products_id
 * @property string $compensate_entered_currency
 * @property string $compensate_entered_currency_value
 * @property string $compensate_order_currency
 * @property string $compensate_order_currency_value
 * @property string $compensate_accident_amount
 * @property string $compensate_non_accident_amount
 * @property string $compensate_supplier_amount
 * @property integer $compensate_by_supplier_id
 * @property string $compensate_by_supplier_firstname
 * @property string $compensate_by_supplier_lastname
 * @property string $compensate_by_supplier_code
 * @property string $compensate_by_supplier_email_address
 * @property string $orders_compensate_products_added_by
 * @property string $orders_compensate_products_messages
 */
class OrdersCompensateProductsBase extends MainModel {

    /**
     * Returns the static model of the specified AR class.
     * @return OrdersCompensateProducts the static model class
     */
    public static function model($className=__CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'orders_compensate_products';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_compensate_products_messages', 'required'),
            array('orders_products_id, orders_id, compensate_by_supplier_id', 'numerical', 'integerOnly' => true),
            array('compensate_for_orders_products_id', 'length', 'max' => 255),
            array('compensate_entered_currency, compensate_order_currency', 'length', 'max' => 3),
            array('compensate_entered_currency_value, compensate_order_currency_value', 'length', 'max' => 14),
            array('compensate_accident_amount, compensate_non_accident_amount, compensate_supplier_amount', 'length', 'max' => 15),
            array('compensate_by_supplier_firstname, compensate_by_supplier_lastname', 'length', 'max' => 32),
            array('compensate_by_supplier_code', 'length', 'max' => 64),
            array('compensate_by_supplier_email_address', 'length', 'max' => 96),
            array('orders_compensate_products_added_by', 'length', 'max' => 128),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('orders_products_id, orders_id, compensate_for_orders_products_id, compensate_entered_currency, compensate_entered_currency_value, compensate_order_currency, compensate_order_currency_value, compensate_accident_amount, compensate_non_accident_amount, compensate_supplier_amount, compensate_by_supplier_id, compensate_by_supplier_firstname, compensate_by_supplier_lastname, compensate_by_supplier_code, compensate_by_supplier_email_address, orders_compensate_products_added_by, orders_compensate_products_messages', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'orders_products_id' => 'Orders Products',
            'orders_id' => 'Orders',
            'compensate_for_orders_products_id' => 'Compensate For Orders Products',
            'compensate_entered_currency' => 'Compensate Entered Currency',
            'compensate_entered_currency_value' => 'Compensate Entered Currency Value',
            'compensate_order_currency' => 'Compensate Order Currency',
            'compensate_order_currency_value' => 'Compensate Order Currency Value',
            'compensate_accident_amount' => 'Compensate Accident Amount',
            'compensate_non_accident_amount' => 'Compensate Non Accident Amount',
            'compensate_supplier_amount' => 'Compensate Supplier Amount',
            'compensate_by_supplier_id' => 'Compensate By Supplier',
            'compensate_by_supplier_firstname' => 'Compensate By Supplier Firstname',
            'compensate_by_supplier_lastname' => 'Compensate By Supplier Lastname',
            'compensate_by_supplier_code' => 'Compensate By Supplier Code',
            'compensate_by_supplier_email_address' => 'Compensate By Supplier Email Address',
            'orders_compensate_products_added_by' => 'Orders Compensate Products Added By',
            'orders_compensate_products_messages' => 'Orders Compensate Products Messages',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria();

        $criteria->compare('orders_products_id', $this->orders_products_id);
        $criteria->compare('orders_id', $this->orders_id);
        $criteria->compare('compensate_for_orders_products_id', $this->compensate_for_orders_products_id, true);
        $criteria->compare('compensate_entered_currency', $this->compensate_entered_currency, true);
        $criteria->compare('compensate_entered_currency_value', $this->compensate_entered_currency_value, true);
        $criteria->compare('compensate_order_currency', $this->compensate_order_currency, true);
        $criteria->compare('compensate_order_currency_value', $this->compensate_order_currency_value, true);
        $criteria->compare('compensate_accident_amount', $this->compensate_accident_amount, true);
        $criteria->compare('compensate_non_accident_amount', $this->compensate_non_accident_amount, true);
        $criteria->compare('compensate_supplier_amount', $this->compensate_supplier_amount, true);
        $criteria->compare('compensate_by_supplier_id', $this->compensate_by_supplier_id);
        $criteria->compare('compensate_by_supplier_firstname', $this->compensate_by_supplier_firstname, true);
        $criteria->compare('compensate_by_supplier_lastname', $this->compensate_by_supplier_lastname, true);
        $criteria->compare('compensate_by_supplier_code', $this->compensate_by_supplier_code, true);
        $criteria->compare('compensate_by_supplier_email_address', $this->compensate_by_supplier_email_address, true);
        $criteria->compare('orders_compensate_products_added_by', $this->orders_compensate_products_added_by, true);
        $criteria->compare('orders_compensate_products_messages', $this->orders_compensate_products_messages, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getCompensateProductInfo($orderProductId) {
        $criteria = new CDbCriteria();
        $criteria->select = 'compensate_for_orders_products_id, compensate_entered_currency, compensate_entered_currency_value, 
                            compensate_order_currency, compensate_order_currency_value, compensate_accident_amount, compensate_non_accident_amount, 
                            compensate_supplier_amount, compensate_by_supplier_id,orders_compensate_products_added_by, 
                            orders_compensate_products_messages ';
        $criteria->condition = 'orders_products_id = :orderProductId';
        $criteria->params = array(':orderProductId' => $orderProductId);
        $result = $this->model()->find($criteria);
        return $result;
    }

}