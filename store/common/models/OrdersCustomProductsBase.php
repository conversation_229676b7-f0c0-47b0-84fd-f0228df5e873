<?php

/**
 * This is the model class for table "orders_custom_products".
 *
 * The followings are the available columns in table 'orders_custom_products':
 * @property integer $orders_custom_products_id
 * @property integer $products_id
 * @property integer $orders_products_id
 * @property string $orders_custom_products_key
 * @property string $orders_custom_products_value
 * @property integer $orders_custom_products_number
 */
class OrdersCustomProductsBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersCustomProductsBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_custom_products';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_custom_products_value', 'required'),
            array('products_id, orders_products_id, orders_custom_products_number', 'numerical', 'integerOnly' => true),
            array('orders_custom_products_key', 'length', 'max' => 100), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'orders_custom_products_id, products_id, orders_products_id, orders_custom_products_key, orders_custom_products_value, orders_custom_products_number',
                'safe', 'on' => 'search'),);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_custom_products_id' => 'Orders Custom Products', 'products_id' => 'Products',
            'orders_products_id' => 'Orders Products', 'orders_custom_products_key' => 'Orders Custom Products Key',
            'orders_custom_products_value' => 'Orders Custom Products Value',
            'orders_custom_products_number' => 'Orders Custom Products Number',);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('orders_custom_products_id', $this->orders_custom_products_id);
        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('orders_products_id', $this->orders_products_id);
        $criteria->compare('orders_custom_products_key', $this->orders_custom_products_key, true);
        $criteria->compare('orders_custom_products_value', $this->orders_custom_products_value, true);
        $criteria->compare('orders_custom_products_number', $this->orders_custom_products_number);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,));
    }

    //TODO Deprecated, Replace feature with direct getting customs products code id from cpc table
    public function getCdkeyInfo($productId, $ordersProductId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'orders_custom_products_value';
        $criteria->condition = 'products_id = :productId AND orders_products_id = :orderProductId AND orders_custom_products_key = "cd_key_id"';
        $criteria->params = array(':productId' => $productId, ':orderProductId' => $ordersProductId);
        $result = $this->model()->findAll($criteria);
        return $result;
    }
}