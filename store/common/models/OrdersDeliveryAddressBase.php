<?php

/**
 * This is the model class for table "customers_top_up_info".
 *
 * The followings are the available columns in table 'customers_top_up_info':
 * @property string $top_up_info_id
 * @property integer $orders_products_id
 * @property string $top_up_value
 */
class OrdersDeliveryAddressBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CustomersTopUpInfoBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()
    {
        return Yii::app()->db_og;
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_delivery_address';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_products_id', 'numerical', 'integerOnly' => true),
            array('recipient_name, contact_number, addr_1, addr_2, city, state, postcode, country_name', 'length', 'max' => 255),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_products_id' => 'Orders Products Id',
            'recipient_name' => 'Recipient Name',
            'contact_number' => 'Contact Number',
            'addr_1' => 'Address 1',
            'addr_2' => 'Address 2',
            'city' => 'City',
            'state' => 'State',
            'country_name' => 'Country',
            'postcode' => 'Postcode',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        return array();
    }
}