<?php

/**
 * This is the model class for table "orders_log_table".
 *
 * The followings are the available columns in table 'orders_log_table':
 * @property integer $orders_log_id
 * @property string $orders_log_admin_id
 * @property string $orders_log_ip
 * @property string $orders_log_time
 * @property integer $orders_log_orders_id
 * @property string $orders_log_system_messages
 * @property string $orders_log_filename
 */
class OrdersLogTableBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersLogTableBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_log_table';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_log_system_messages', 'required'),
			array('orders_log_orders_id', 'numerical', 'integerOnly'=>true),
			array('orders_log_admin_id, orders_log_filename', 'length', 'max'=>255),
			array('orders_log_ip', 'length', 'max'=>128),
			array('orders_log_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_log_id, orders_log_admin_id, orders_log_ip, orders_log_time, orders_log_orders_id, orders_log_system_messages, orders_log_filename', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_log_id' => 'Orders Log',
			'orders_log_admin_id' => 'Orders Log Admin',
			'orders_log_ip' => 'Orders Log Ip',
			'orders_log_time' => 'Orders Log Time',
			'orders_log_orders_id' => 'Orders Log Orders',
			'orders_log_system_messages' => 'Orders Log System Messages',
			'orders_log_filename' => 'Orders Log Filename',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_log_id',$this->orders_log_id);
		$criteria->compare('orders_log_admin_id',$this->orders_log_admin_id,true);
		$criteria->compare('orders_log_ip',$this->orders_log_ip,true);
		$criteria->compare('orders_log_time',$this->orders_log_time,true);
		$criteria->compare('orders_log_orders_id',$this->orders_log_orders_id);
		$criteria->compare('orders_log_system_messages',$this->orders_log_system_messages,true);
		$criteria->compare('orders_log_filename',$this->orders_log_filename,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}