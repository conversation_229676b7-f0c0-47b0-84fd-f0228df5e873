<?php

/**
 * This is the model class for table "orders_products".
 *
 * The followings are the available columns in table 'orders_products':
 * @property integer $orders_products_id
 * @property integer $orders_id
 * @property integer $products_id
 * @property string $products_model
 * @property string $products_name
 * @property string $orders_products_store_price
 * @property string $products_price
 * @property string $final_price
 * @property integer $op_rebate
 * @property integer $op_rebate_delivered
 * @property string $products_tax
 * @property integer $products_quantity
 * @property string $products_delivered_quantity
 * @property string $products_good_delivered_quantity
 * @property string $products_good_delivered_price
 * @property string $products_canceled_quantity
 * @property string $products_canceled_price
 * @property string $products_reversed_quantity
 * @property string $products_reversed_price
 * @property integer $products_bundle_id
 * @property integer $parent_orders_products_id
 * @property integer $products_pre_order
 * @property integer $custom_products_type_id
 * @property integer $orders_products_is_compensate
 * @property integer $orders_products_purchase_eta
 * @property integer $products_categories_id
 */
class OrdersProductsBase extends MainModel
{
    public $amount_delivered, $amount_refunded, $amount_sc_compensate_delivered;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersProductsBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_products';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array(
                'orders_id, products_id, op_rebate, op_rebate_delivered, products_quantity, products_bundle_id, parent_orders_products_id, products_pre_order, custom_products_type_id, orders_products_is_compensate, orders_products_purchase_eta, products_categories_id',
                'numerical',
                'integerOnly' => true
            ),
            array('products_model', 'length', 'max' => 12),
            array('products_name', 'length', 'max' => 255),
            array(
                'orders_products_store_price, products_price, final_price, products_good_delivered_price, products_canceled_price, products_reversed_price',
                'length',
                'max' => 19
            ),
            array('products_tax', 'length', 'max' => 7),
            array(
                'products_delivered_quantity, products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity',
                'length',
                'max' => 15
            ), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'orders_products_id, orders_id, products_id, products_model, products_name, orders_products_store_price, products_price, final_price, op_rebate, op_rebate_delivered, products_tax, products_quantity, products_delivered_quantity, products_good_delivered_quantity, products_good_delivered_price, products_canceled_quantity, products_canceled_price, products_reversed_quantity, products_reversed_price, products_bundle_id, parent_orders_products_id, products_pre_order, custom_products_type_id, orders_products_is_compensate, orders_products_purchase_eta, products_categories_id',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_products_id' => 'Orders Products',
            'orders_id' => 'Orders',
            'products_id' => 'Products',
            'products_model' => 'Products Model',
            'products_name' => 'Products Name',
            'orders_products_store_price' => 'Orders Products Store Price',
            'products_price' => 'Products Price',
            'final_price' => 'Final Price',
            'op_rebate' => 'Op Rebate',
            'op_rebate_delivered' => 'Op Rebate Delivered',
            'products_tax' => 'Products Tax',
            'products_quantity' => 'Products Quantity',
            'products_delivered_quantity' => 'Products Delivered Quantity',
            'products_good_delivered_quantity' => 'Products Good Delivered Quantity',
            'products_good_delivered_price' => 'Products Good Delivered Price',
            'products_canceled_quantity' => 'Products Canceled Quantity',
            'products_canceled_price' => 'Products Canceled Price',
            'products_reversed_quantity' => 'Products Reversed Quantity',
            'products_reversed_price' => 'Products Reversed Price',
            'products_bundle_id' => 'Products Bundle',
            'parent_orders_products_id' => 'Parent Orders Products',
            'products_pre_order' => 'Products Pre Order',
            'custom_products_type_id' => 'Custom Products Type',
            'orders_products_is_compensate' => 'Orders Products Is Compensate',
            'orders_products_purchase_eta' => 'Orders Products Purchase Eta',
            'products_categories_id' => 'Products Categories',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('orders_products_id', $this->orders_products_id);
        $criteria->compare('orders_id', $this->orders_id);
        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('products_model', $this->products_model, true);
        $criteria->compare('products_name', $this->products_name, true);
        $criteria->compare('orders_products_store_price', $this->orders_products_store_price, true);
        $criteria->compare('products_price', $this->products_price, true);
        $criteria->compare('final_price', $this->final_price, true);
        $criteria->compare('op_rebate', $this->op_rebate);
        $criteria->compare('op_rebate_delivered', $this->op_rebate_delivered);
        $criteria->compare('products_tax', $this->products_tax, true);
        $criteria->compare('products_quantity', $this->products_quantity);
        $criteria->compare('products_delivered_quantity', $this->products_delivered_quantity, true);
        $criteria->compare('products_good_delivered_quantity', $this->products_good_delivered_quantity, true);
        $criteria->compare('products_good_delivered_price', $this->products_good_delivered_price, true);
        $criteria->compare('products_canceled_quantity', $this->products_canceled_quantity, true);
        $criteria->compare('products_canceled_price', $this->products_canceled_price, true);
        $criteria->compare('products_reversed_quantity', $this->products_reversed_quantity, true);
        $criteria->compare('products_reversed_price', $this->products_reversed_price, true);
        $criteria->compare('products_bundle_id', $this->products_bundle_id);
        $criteria->compare('parent_orders_products_id', $this->parent_orders_products_id);
        $criteria->compare('products_pre_order', $this->products_pre_order);
        $criteria->compare('custom_products_type_id', $this->custom_products_type_id);
        $criteria->compare('orders_products_is_compensate', $this->orders_products_is_compensate);
        $criteria->compare('orders_products_purchase_eta', $this->orders_products_purchase_eta);
        $criteria->compare('products_categories_id', $this->products_categories_id);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getOrderProducts($order_id)
    {
        $sql = "SELECT o.orders_id, o.currency,o.currency_value, t.orders_products_id, t.custom_products_type_id, t.products_id, t.products_name, t.products_model, t.products_price,
            t.products_tax,products_delivered_quantity, t.products_quantity, t.products_good_delivered_quantity, t.products_canceled_quantity, t.final_price, t.products_pre_order,
            t.products_categories_id, t.orders_products_is_compensate,t.products_bundle_id,t.orders_products_id,t.parent_orders_products_id , ope.orders_products_extra_info_value, t.op_rebate, spid.orders_products_extra_info_value AS sub_products_id, pt.orders_products_extra_info_value AS products_type
            FROM `orders_products` `t` 
            LEFT JOIN orders o ON o.orders_id = t.orders_id 
            LEFT JOIN orders_products_extra_info ope on ope.orders_products_id = t.orders_products_id 
            AND ope.orders_products_extra_info_key = 'delivery_mode'            
            LEFT JOIN orders_products_extra_info pt on pt.orders_products_id = t.orders_products_id 
            AND pt.orders_products_extra_info_key = 'products_type'            
            LEFT JOIN orders_products_extra_info spid on spid.orders_products_id = t.orders_products_id 
            AND spid.orders_products_extra_info_key = 'sub_products_id'
            WHERE t.orders_id = :orderId
            ORDER BY t.orders_products_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":orderId", $order_id, PDO::PARAM_INT);
        $results = $command->queryAll();
        return $results;
    }

    public function getOrderProductsScCompensate($order_id)
    {
        $sql = "SELECT o.currency, t.orders_products_id, t.products_good_delivered_price
            FROM `orders_products` `t` 
            LEFT JOIN orders o ON o.orders_id = t.orders_id 
            WHERE t.orders_id = :orderId AND t.products_id = -1 AND t.orders_products_is_compensate = 1
            ORDER BY t.orders_products_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":orderId", $order_id, PDO::PARAM_INT);
        $results = $command->queryAll();
        return $results;
    }

    public function getAmountDelivered($orderId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'SUM(products_good_delivered_price) AS amount_delivered';
        $criteria->condition = 'orders_id = :orderId AND parent_orders_products_id = "0" AND products_id != -1';
        $criteria->params = array(':orderId' => $orderId);
        $result = $this->model()->find($criteria);
        return isset($result->amount_delivered) ? $result->amount_delivered : null;
    }

    public function getScCompensateAmountDelivered($orderId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'SUM(products_good_delivered_price) AS amount_sc_compensate_delivered';
        $criteria->condition = 'orders_id = :orderId AND parent_orders_products_id = "0" AND products_id = -1 AND orders_products_is_compensate = 1';
        $criteria->params = array(':orderId' => $orderId);
        $result = $this->model()->find($criteria);
        return isset($result->amount_sc_compensate_delivered) ? $result->amount_sc_compensate_delivered : null;
    }

    public function getProductsAmountRefunded($orderId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'SUM(products_canceled_price) as amount_refunded';
        $criteria->condition = 'orders_id = :orderId AND parent_orders_products_id = "0"';
        $criteria->params = array(':orderId' => $orderId);
        $result = $this->model()->find($criteria);
        return isset($result->amount_refunded) ? $result->amount_refunded : null;
    }

}