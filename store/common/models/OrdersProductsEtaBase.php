<?php

/**
 * This is the model class for table "orders_products_eta".
 *
 * The followings are the available columns in table 'orders_products_eta':
 * @property integer $orders_products_id
 * @property integer $expiry_hour
 * @property string $start_time
 * @property integer $total_buyback_time
 */
class OrdersProductsEtaBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersProductsEtaBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_products_eta';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_id, expiry_hour, total_buyback_time', 'numerical', 'integerOnly'=>true),
			array('start_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_products_id, expiry_hour, start_time, total_buyback_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_products_id' => 'Orders Products',
			'expiry_hour' => 'Expiry Hour',
			'start_time' => 'Start Time',
			'total_buyback_time' => 'Total Buyback Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('expiry_hour',$this->expiry_hour);
		$criteria->compare('start_time',$this->start_time,true);
		$criteria->compare('total_buyback_time',$this->total_buyback_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}