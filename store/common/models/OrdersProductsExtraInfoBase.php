<?php

/**
 * This is the model class for table "orders_products_extra_info".
 *
 * The followings are the available columns in table 'orders_products_extra_info':
 * @property integer $orders_products_id
 * @property string $orders_products_extra_info_key
 * @property string $orders_products_extra_info_value
 */
class OrdersProductsExtraInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersProductsExtraInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_products_extra_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_extra_info_value', 'required'),
			array('orders_products_id', 'numerical', 'integerOnly'=>true),
			array('orders_products_extra_info_key', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_products_id, orders_products_extra_info_key, orders_products_extra_info_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_products_id' => 'Orders Products',
			'orders_products_extra_info_key' => 'Orders Products Extra Info Key',
			'orders_products_extra_info_value' => 'Orders Products Extra Info Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('orders_products_extra_info_key',$this->orders_products_extra_info_key,true);
		$criteria->compare('orders_products_extra_info_value',$this->orders_products_extra_info_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}