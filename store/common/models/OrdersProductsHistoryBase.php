<?php

/**
 * This is the model class for table "orders_products_history".
 *
 * The followings are the available columns in table 'orders_products_history':
 * @property integer $orders_products_history_id
 * @property integer $buyback_request_group_id
 * @property integer $orders_id
 * @property integer $orders_products_id
 * @property string $date_added
 * @property string $last_updated
 * @property string $date_confirm_delivered
 * @property integer $received
 * @property integer $rolled_back
 * @property integer $delivered_amount
 * @property string $delivered_character
 * @property string $dispute_comment
 * @property string $orders_products_history_record
 * @property string $changed_by
 */
class OrdersProductsHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersProductsHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_products_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('dispute_comment', 'required'),
			array('buyback_request_group_id, orders_id, orders_products_id, received, rolled_back, delivered_amount', 'numerical', 'integerOnly'=>true),
			array('delivered_character', 'length', 'max'=>50),
			array('orders_products_history_record', 'length', 'max'=>255),
			array('changed_by', 'length', 'max'=>128),
			array('date_added, last_updated, date_confirm_delivered', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_products_history_id, buyback_request_group_id, orders_id, orders_products_id, date_added, last_updated, date_confirm_delivered, received, rolled_back, delivered_amount, delivered_character, dispute_comment, orders_products_history_record, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_products_history_id' => 'Orders Products History',
			'buyback_request_group_id' => 'Buyback Request Group',
			'orders_id' => 'Orders',
			'orders_products_id' => 'Orders Products',
			'date_added' => 'Date Added',
			'last_updated' => 'Last Updated',
			'date_confirm_delivered' => 'Date Confirm Delivered',
			'received' => 'Received',
			'rolled_back' => 'Rolled Back',
			'delivered_amount' => 'Delivered Amount',
			'delivered_character' => 'Delivered Character',
			'dispute_comment' => 'Dispute Comment',
			'orders_products_history_record' => 'Orders Products History Record',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_products_history_id',$this->orders_products_history_id);
		$criteria->compare('buyback_request_group_id',$this->buyback_request_group_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_updated',$this->last_updated,true);
		$criteria->compare('date_confirm_delivered',$this->date_confirm_delivered,true);
		$criteria->compare('received',$this->received);
		$criteria->compare('rolled_back',$this->rolled_back);
		$criteria->compare('delivered_amount',$this->delivered_amount);
		$criteria->compare('delivered_character',$this->delivered_character,true);
		$criteria->compare('dispute_comment',$this->dispute_comment,true);
		$criteria->compare('orders_products_history_record',$this->orders_products_history_record,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}