<?php

/**
 * This is the model class for table "orders_products_item".
 *
 * The followings are the available columns in table 'orders_products_item':
 * @property integer $orders_products_id
 * @property string $products_hla_characters_id
 * @property string $products_hla_characters_name
 * @property string $orders_products_item_info
 */
class OrdersProductsItemBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersProductsItemBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_products_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_products_item_info', 'required'),
			array('orders_products_id', 'numerical', 'integerOnly'=>true),
			array('products_hla_characters_id', 'length', 'max'=>10),
			array('products_hla_characters_name', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_products_id, products_hla_characters_id, products_hla_characters_name, orders_products_item_info', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_products_id' => 'Orders Products',
			'products_hla_characters_id' => 'Products Hla Characters',
			'products_hla_characters_name' => 'Products Hla Characters Name',
			'orders_products_item_info' => 'Orders Products Item Info',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('products_hla_characters_id',$this->products_hla_characters_id,true);
		$criteria->compare('products_hla_characters_name',$this->products_hla_characters_name,true);
		$criteria->compare('orders_products_item_info',$this->orders_products_item_info,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}