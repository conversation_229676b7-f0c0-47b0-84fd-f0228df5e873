<?php

/**
 * This is the model class for table "orders_session_info".
 *
 * The followings are the available columns in table 'orders_session_info':
 * @property string $txn_signature
 * @property integer $orders_id
 * @property integer $sendto
 * @property integer $billto
 * @property string $language
 * @property string $currency
 * @property string $firstname
 * @property string $lastname
 * @property string $content_type
 */
class OrdersSessionInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersSessionInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_session_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, sendto, billto', 'numerical', 'integerOnly'=>true),
			array('txn_signature, language, firstname, lastname, content_type', 'length', 'max'=>32),
			array('currency', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('txn_signature, orders_id, sendto, billto, language, currency, firstname, lastname, content_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'txn_signature' => 'Txn Signature',
			'orders_id' => 'Orders',
			'sendto' => 'Sendto',
			'billto' => 'Billto',
			'language' => 'Language',
			'currency' => 'Currency',
			'firstname' => 'Firstname',
			'lastname' => 'Lastname',
			'content_type' => 'Content Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('txn_signature',$this->txn_signature,true);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('sendto',$this->sendto);
		$criteria->compare('billto',$this->billto);
		$criteria->compare('language',$this->language,true);
		$criteria->compare('currency',$this->currency,true);
		$criteria->compare('firstname',$this->firstname,true);
		$criteria->compare('lastname',$this->lastname,true);
		$criteria->compare('content_type',$this->content_type,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}