<?php

/**
 * This is the model class for table "orders_status_history".
 *
 * The followings are the available columns in table 'orders_status_history':
 * @property integer $orders_status_history_id
 * @property integer $orders_id
 * @property integer $orders_status_id
 * @property string $date_added
 * @property integer $customer_notified
 * @property string $comments
 * @property integer $comments_type
 * @property integer $set_as_order_remarks
 * @property string $changed_by
 */
class OrdersStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, orders_status_id, customer_notified, comments_type, set_as_order_remarks', 'numerical', 'integerOnly'=>true),
			array('changed_by', 'length', 'max'=>128),
			array('date_added, comments', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_status_history_id, orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
        return array(
            'orders_status' => array(self::HAS_MANY, 'OrdersStatusBase', '', 'on' => 'orders_status.orders_status_id = t.orders_status_id', 'joinType' => 'LEFT JOIN', 'alias' => 'orders_status'),
            'os' => array(self::HAS_ONE, 'OrdersStatusBase', '', 'joinType' => 'INNER JOIN'),
        );
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_status_history_id' => 'Orders Status History',
			'orders_id' => 'Orders',
			'orders_status_id' => 'Orders Status',
			'date_added' => 'Date Added',
			'customer_notified' => 'Customer Notified',
			'comments' => 'Comments',
			'comments_type' => 'Comments Type',
			'set_as_order_remarks' => 'Set As Order Remarks',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_status_history_id',$this->orders_status_history_id);
		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('orders_status_id',$this->orders_status_id);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('customer_notified',$this->customer_notified);
		$criteria->compare('comments',$this->comments,true);
		$criteria->compare('comments_type',$this->comments_type);
		$criteria->compare('set_as_order_remarks',$this->set_as_order_remarks);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getOrderStatusHistoryInfo($orderId) {
        $criteria = new CDbCriteria();
        $criteria->condition = 'orders_id = :orderId';
        $criteria->params = array(':orderId' => $orderId);
        $criteria->with = array(
            'orders_status' => array(
                'select' => 'orders_status_name',
                'condition' => '(language_id = 1 OR orders_status.orders_status_id IS NULL)',
            )
        );
        $criteria->order = 'orders_status_history_id DESC';
        $result = $this->findAll($criteria);
        return $result;
    }

    public function getOrderStartDate($orderId) {
        $criteria = new CDbCriteria();
        $criteria->condition = 'orders_id = :orderId';
        $criteria->params = array(':orderId' => $orderId);
        $criteria->order = 'orders_status_history_id ASC';
        $criteria->limit = 1;
        $result = $this->find($criteria);
        return $result->date_added;
    }

    public function getOrderEndDate($orderId) {
        $criteria = new CDbCriteria();
        $criteria->condition = 'orders_id = :orderId';
        $criteria->params = array(':orderId' => $orderId);
        $criteria->order = 'orders_status_history_id DESC';
        $criteria->limit = 1;
        $result = $this->find($criteria);
        return $result->date_added;
    }
}