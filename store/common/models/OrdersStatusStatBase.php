<?php

/**
 * This is the model class for table "orders_status_stat".
 *
 * The followings are the available columns in table 'orders_status_stat':
 * @property integer $orders_id
 * @property integer $orders_status_id
 * @property integer $occurrence
 * @property string $first_date
 * @property string $latest_date
 * @property string $changed_by
 */
class OrdersStatusStatBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersStatusStatBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_status_stat';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id, orders_status_id, occurrence', 'numerical', 'integerOnly'=>true),
			array('changed_by', 'length', 'max'=>128),
			array('first_date, latest_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, orders_status_id, occurrence, first_date, latest_date, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'orders_status_id' => 'Orders Status',
			'occurrence' => 'Occurrence',
			'first_date' => 'First Date',
			'latest_date' => 'Latest Date',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id);
		$criteria->compare('orders_status_id',$this->orders_status_id);
		$criteria->compare('occurrence',$this->occurrence);
		$criteria->compare('first_date',$this->first_date,true);
		$criteria->compare('latest_date',$this->latest_date,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}


    public function getLatestDate($orderId,$orderStatusId) {
        $criteria = new CDbCriteria();
        $criteria->select = 'latest_date';
        $criteria->condition = 'orders_id = :orderId AND orders_status_id = :orderStatusId';
        $criteria->params = array(':orderId' => $orderId, ':orderStatusId' => $orderStatusId);
        $result = $this->model()->find($criteria);
        return isset($result->latest_date) ? $result->latest_date : null;
    }
}