<?php

/**
 * This is the model class for table "orders_tag".
 *
 * The followings are the available columns in table 'orders_tag':
 * @property integer $orders_tag_id
 * @property string $orders_tag_name
 * @property string $orders_tag_status_ids
 * @property string $filename
 */
class OrdersTagBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersTagBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_tag';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_tag_name', 'length', 'max'=>150),
			array('orders_tag_status_ids', 'length', 'max'=>32),
			array('filename', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_tag_id, orders_tag_name, orders_tag_status_ids, filename', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_tag_id' => 'Orders Tag',
			'orders_tag_name' => 'Orders Tag Name',
			'orders_tag_status_ids' => 'Orders Tag Status Ids',
			'filename' => 'Filename',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_tag_id',$this->orders_tag_id);
		$criteria->compare('orders_tag_name',$this->orders_tag_name,true);
		$criteria->compare('orders_tag_status_ids',$this->orders_tag_status_ids,true);
		$criteria->compare('filename',$this->filename,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}