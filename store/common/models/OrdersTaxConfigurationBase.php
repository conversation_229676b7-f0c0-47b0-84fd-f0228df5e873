<?php

/**
 * This is the model class for table "orders_tax_configuration".
 *
 * The followings are the available columns in table 'orders_tax_configuration':
 * @property string $orders_tax_id
 * @property string $country_code
 * @property string $country_name
 * @property string $currency
 * @property string $orders_tax_percentage
 * @property string $business_tax_percentage
 * @property string $orders_tax_status
 * @property integer $orders_provide_invoice_status
 * @property integer $business_tax_status
 * @property integer $orders_include_reverse_charge
 * @property string $start_datetime
 * @property string $address_1
 * @property string $address_2
 * @property string $address_3
 * @property string $contact
 * @property string $website
 * @property string $tax_invoice_title
 * @property string $tax_registration_name
 * @property string $gst_registration_no
 * @property string $company_name
 * @property string $company_logo
 * @property string $business_tax_form
 */
class OrdersTaxConfigurationBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersTaxConfigurationBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()
    {
        return Yii::app()->db_rr;
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_tax_configuration';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('start_datetime, address_1, address_2, address_3, contact, website, gst_registration_no, company_name, company_logo', 'required'),
            array('country_code', 'length', 'max'=>2),
            array('currency', 'length', 'max'=>3),
            array('orders_tax_percentage, business_tax_percentage', 'length', 'max'=>6),
            array('orders_tax_status, orders_provide_invoice_status, business_tax_status, orders_include_reverse_charge', 'length', 'max'=>1),
            array('country_name, address_1, address_2, address_3, tax_invoice_title, tax_registration_name', 'length', 'max'=>64),
            array('contact, website, gst_registration_no, company_name', 'length', 'max'=>32),
            array('company_logo', 'length', 'max'=>255),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('orders_tax_id, country_code, country_name, currency, orders_tax_percentage, business_tax_percentage, orders_tax_status, orders_provide_invoice_status, business_tax_status, orders_include_reverse_charge, start_datetime, address_1, address_2, address_3, contact, website, tax_invoice_title, tax_registration_name, gst_registration_no, company_name, company_logo, business_tax_form', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'otcd1' => array(self::HAS_MANY, 'OrdersTaxConfigurationDescriptionBase', array('orders_tax_id' => 'orders_tax_id')),
            'otcd2' => array(self::HAS_MANY, 'OrdersTaxConfigurationDescriptionBase', array('orders_tax_id' => 'orders_tax_id')),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_tax_id' => 'Orders Tax',
            'country_code' => 'Country Code',
            'country_name' => 'Country Name',
            'currency' => 'Currency',
            'orders_tax_percentage' => 'Orders Tax Percentage',
            'business_tax_percentage' => 'Business Tax Percentage',
            'orders_tax_status' => 'Orders Tax Status',
            'orders_provide_invoice_status' => 'Orders Provide Invoice Status',
            'business_tax_status' => 'Business Tax Status',
            'orders_include_reverse_charge' => 'Orders Include Reverse Charge',
            'start_datetime' => 'Start Datetime',
            'address_1' => 'Address 1',
            'address_2' => 'Address 2',
            'address_3' => 'Address 3',
            'contact' => 'Contact',
            'website' => 'Website',
            'tax_invoice_title' => 'Tax Invoice Title',
            'tax_registration_name' => 'Tax Registration Name',
            'gst_registration_no' => 'Gst Registration No',
            'company_name' => 'Company Name',
            'company_logo' => 'Company Logo',
            'business_tax_form' => 'Business Tax Form',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('orders_tax_id', $this->orders_tax_id, true);
        $criteria->compare('country_code', $this->country_code, true);
        $criteria->compare('country_name', $this->country_name, true);
        $criteria->compare('currency', $this->currency, true);
        $criteria->compare('orders_tax_percentage', $this->orders_tax_percentage, true);
        $criteria->compare('business_tax_percentage', $this->business_tax_percentage, true);
        $criteria->compare('orders_tax_status', $this->orders_tax_status, true);
        $criteria->compare('orders_provide_invoice_status', $this->orders_provide_invoice_status, true);
        $criteria->compare('business_tax_status', $this->business_tax_status, true);
        $criteria->compare('orders_include_reverse_charge', $this->orders_include_reverse_charge, true);
        $criteria->compare('start_datetime', $this->start_datetime, true);
        $criteria->compare('address_1', $this->address_1, true);
        $criteria->compare('address_2', $this->address_2, true);
        $criteria->compare('address_3', $this->address_3, true);
        $criteria->compare('contact', $this->contact, true);
        $criteria->compare('website', $this->website, true);
        $criteria->compare('tax_invoice_title', $this->tax_invoice_title, true);
        $criteria->compare('tax_registration_name', $this->tax_registration_name, true);
        $criteria->compare('gst_registration_no', $this->gst_registration_no, true);
        $criteria->compare('company_name', $this->company_name, true);
        $criteria->compare('company_logo', $this->company_logo, true);
        $criteria->compare('business_tax_form', $this->business_tax_form, true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }
}
