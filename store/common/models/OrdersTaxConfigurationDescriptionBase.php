<?php

/**
 * This is the model class for table "orders_tax_configuration_description".
 *
 * The followings are the available columns in table 'orders_tax_configuration_description':
 * @property string $orders_tax_id
 * @property string $language_id
 * @property string $orders_tax_title
 * @property string $orders_tax_title_short
 * @property string $orders_tax_message
 */
class OrdersTaxConfigurationDescriptionBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersTaxConfigurationDescriptionBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_tax_configuration_description';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_tax_title_short', 'required'),
            array('orders_tax_id, language_id', 'length', 'max'=>11),
            array('orders_tax_title', 'length', 'max'=>32),
            array('orders_tax_title_short', 'length', 'max'=>10),
            array('orders_tax_message', 'required'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('orders_tax_id, language_id, orders_tax_title, orders_tax_title_short, orders_tax_message', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_tax_id' => 'Orders Tax',
            'language_id' => 'Language',
            'orders_tax_title' => 'Orders Tax Title',
            'orders_tax_title_short' => 'Orders Tax Title Short',
            'orders_tax_message' => 'Orders Tax Message',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('orders_tax_id', $this->orders_tax_id, true);
        $criteria->compare('language_id', $this->language_id, true);
        $criteria->compare('orders_tax_title', $this->orders_tax_title, true);
        $criteria->compare('orders_tax_title_short', $this->orders_tax_title_short, true);
        $criteria->compare('orders_tax_message', $this->orders_tax_message, true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }
}
