<?php

/**
 * This is the model class for table "orders_tax_customers".
 *
 * The followings are the available columns in table 'orders_tax_customers':
 * @property string $id
 * @property string $customers_id
 * @property string $orders_tax_id
 * @property string $business_name
 * @property string $business_tax_number
 * @property string $orders_tax_customers_data
 * @property int $orders_tax_customers_status
 * @property int $created_at
 * @property int $updated_at
 */
class OrdersTaxCustomersHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersTaxCustomersBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_tax_customers_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, orders_tax_customers_id', 'required'),
			array('customers_id, orders_tax_customers_id', 'numerical', 'integerOnly' => true),
			array('orders_tax_customers_status', 'length', 'max' => 1),
			array('created_at, updated_at', 'length', 'max' => 11),
			array('orders_tax_customers_data', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, customers_id, orders_tax_customers_id, orders_tax_customers_data, orders_tax_customers_status, created_at, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
            'customers_id' => 'Customers ID',
            'orders_tax_customers_id' => 'Orders Tax ID',
            'orders_tax_customers_data' => 'Orders Tax Customers Data',
            'orders_tax_customers_status' => 'Orders Tax Customers Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id', $this->id, true);
        $criteria->compare('customers_id', $this->customers_id, true);
        $criteria->compare('orders_tax_customers_id', $this->orders_tax_customers_id, true);
        $criteria->compare('orders_tax_customers_data', $this->orders_tax_customers_data, true);
        $criteria->compare('orders_tax_customers_status', $this->orders_tax_customers_status, true);
        $criteria->compare('created_at', $this->created_at, true);
        $criteria->compare('updated_at', $this->updated_at, true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}