<?php

/**
 * This is the model class for table "orders_top_up".
 *
 * The followings are the available columns in table 'orders_top_up':
 * @property integer $orders_products_id
 * @property string $top_up_id
 * @property string $publishers_ref_id
 * @property string $top_up_status
 * @property string $publishers_response_time
 * @property double $customer_before_balance
 * @property double $customer_after_balance
 * @property string $game
 * @property string $server
 * @property string $account
 * @property string $character
 * @property string $publishers_id
 * @property string $top_up_response_info
 * @property string $top_up_process_flag
 * @property integer $result_code
 * @property string $top_up_last_processed_time
 * @property string $top_up_timestamp
 * @property string $top_up_created_date
 */
class OrdersTopUpBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersTopUpBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_top_up';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('top_up_response_info, top_up_timestamp', 'required'),
			array('orders_products_id, result_code', 'numerical', 'integerOnly'=>true),
			array('customer_before_balance, customer_after_balance', 'numerical'),
			array('publishers_ref_id', 'length', 'max'=>32),
			array('top_up_status', 'length', 'max'=>2),
			array('game, server, account, character', 'length', 'max'=>64),
			array('publishers_id', 'length', 'max'=>10),
			array('top_up_process_flag', 'length', 'max'=>1),
			array('publishers_response_time, top_up_last_processed_time, top_up_created_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_products_id, top_up_id, publishers_ref_id, top_up_status, publishers_response_time, customer_before_balance, customer_after_balance, game, server, account, character, publishers_id, top_up_response_info, top_up_process_flag, result_code, top_up_last_processed_time, top_up_timestamp, top_up_created_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_products_id' => 'Orders Products',
			'top_up_id' => 'Top Up',
			'publishers_ref_id' => 'Publishers Ref',
			'top_up_status' => 'Top Up Status',
			'publishers_response_time' => 'Publishers Response Time',
			'customer_before_balance' => 'Customer Before Balance',
			'customer_after_balance' => 'Customer After Balance',
			'game' => 'Game',
			'server' => 'Server',
			'account' => 'Account',
			'character' => 'Character',
			'publishers_id' => 'Publishers',
			'top_up_response_info' => 'Top Up Response Info',
			'top_up_process_flag' => 'Top Up Process Flag',
			'result_code' => 'Result Code',
			'top_up_last_processed_time' => 'Top Up Last Processed Time',
			'top_up_timestamp' => 'Top Up Timestamp',
			'top_up_created_date' => 'Top Up Created Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_products_id',$this->orders_products_id);
		$criteria->compare('top_up_id',$this->top_up_id,true);
		$criteria->compare('publishers_ref_id',$this->publishers_ref_id,true);
		$criteria->compare('top_up_status',$this->top_up_status,true);
		$criteria->compare('publishers_response_time',$this->publishers_response_time,true);
		$criteria->compare('customer_before_balance',$this->customer_before_balance);
		$criteria->compare('customer_after_balance',$this->customer_after_balance);
		$criteria->compare('game',$this->game,true);
		$criteria->compare('server',$this->server,true);
		$criteria->compare('account',$this->account,true);
		$criteria->compare('character',$this->character,true);
		$criteria->compare('publishers_id',$this->publishers_id,true);
		$criteria->compare('top_up_response_info',$this->top_up_response_info,true);
		$criteria->compare('top_up_process_flag',$this->top_up_process_flag,true);
		$criteria->compare('result_code',$this->result_code);
		$criteria->compare('top_up_last_processed_time',$this->top_up_last_processed_time,true);
		$criteria->compare('top_up_timestamp',$this->top_up_timestamp,true);
		$criteria->compare('top_up_created_date',$this->top_up_created_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getTopupStatus($opId) {
        $criteria = new CDbCriteria();
        $criteria->select = 'top_up_status';
        $criteria->condition = 'orders_products_id = :opId';
        $criteria->params = array(':opId' => $opId);
        $result = $this->model()->find($criteria);
        return isset($result->top_up_status) ? $result->top_up_status : null;
    }
}