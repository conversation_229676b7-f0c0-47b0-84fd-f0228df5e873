<?php

/**
 * This is the model class for table "orders_top_up_remark".
 *
 * The followings are the available columns in table 'orders_top_up_remark':
 * @property string $orders_top_up_remark_id
 * @property string $top_up_id
 * @property string $data_added
 * @property string $remark
 * @property string $changed_by
 */
class OrdersTopUpRemarkBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersTopUpRemarkBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'orders_top_up_remark';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('remark', 'required'),
			array('top_up_id', 'length', 'max'=>10),
			array('changed_by', 'length', 'max'=>128),
			array('data_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_top_up_remark_id, top_up_id, data_added, remark, changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_top_up_remark_id' => 'Orders Top Up Remark',
			'top_up_id' => 'Top Up',
			'data_added' => 'Data Added',
			'remark' => 'Remark',
			'changed_by' => 'Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_top_up_remark_id',$this->orders_top_up_remark_id,true);
		$criteria->compare('top_up_id',$this->top_up_id,true);
		$criteria->compare('data_added',$this->data_added,true);
		$criteria->compare('remark',$this->remark,true);
		$criteria->compare('changed_by',$this->changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}