<?php

/**
 * This is the model class for table "outstanding_payment_history".
 *
 * The followings are the available columns in table 'outstanding_payment_history':
 * @property string $outstanding_payment_history_date
 * @property string $payment_category
 * @property integer $user_id
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $outstanding_payment_gross_amount
 * @property string $outstanding_payment_amount
 */
class OutstandingPaymentHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OutstandingPaymentHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'outstanding_payment_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id', 'numerical', 'integerOnly'=>true),
			array('payment_category', 'length', 'max'=>16),
			array('user_firstname, user_lastname', 'length', 'max'=>32),
			array('user_email_address', 'length', 'max'=>96),
			array('outstanding_payment_gross_amount, outstanding_payment_amount', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('outstanding_payment_history_date, payment_category, user_id, user_firstname, user_lastname, user_email_address, outstanding_payment_gross_amount, outstanding_payment_amount', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'outstanding_payment_history_date' => 'Outstanding Payment History Date',
			'payment_category' => 'Payment Category',
			'user_id' => 'User',
			'user_firstname' => 'User Firstname',
			'user_lastname' => 'User Lastname',
			'user_email_address' => 'User Email Address',
			'outstanding_payment_gross_amount' => 'Outstanding Payment Gross Amount',
			'outstanding_payment_amount' => 'Outstanding Payment Amount',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('outstanding_payment_history_date',$this->outstanding_payment_history_date,true);
		$criteria->compare('payment_category',$this->payment_category,true);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_firstname',$this->user_firstname,true);
		$criteria->compare('user_lastname',$this->user_lastname,true);
		$criteria->compare('user_email_address',$this->user_email_address,true);
		$criteria->compare('outstanding_payment_gross_amount',$this->outstanding_payment_gross_amount,true);
		$criteria->compare('outstanding_payment_amount',$this->outstanding_payment_amount,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}