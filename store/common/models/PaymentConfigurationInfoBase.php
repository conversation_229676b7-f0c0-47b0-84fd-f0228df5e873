<?php

/**
 * This is the model class for table "payment_configuration_info".
 *
 * The followings are the available columns in table 'payment_configuration_info':
 * @property integer $payment_configuration_info_id
 * @property integer $payment_methods_id
 * @property string $payment_configuration_info_title
 * @property string $payment_configuration_info_key
 * @property string $payment_configuration_info_description
 * @property integer $payment_configuration_info_sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property string $use_function
 * @property string $set_function
 */
class PaymentConfigurationInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentConfigurationInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_configuration_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id, payment_configuration_info_sort_order', 'numerical', 'integerOnly'=>true),
			array('payment_configuration_info_title, payment_configuration_info_key', 'length', 'max'=>64),
			array('payment_configuration_info_description, use_function, set_function', 'length', 'max'=>255),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_configuration_info_id, payment_methods_id, payment_configuration_info_title, payment_configuration_info_key, payment_configuration_info_description, payment_configuration_info_sort_order, last_modified, date_added, use_function, set_function', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_configuration_info_id' => 'Payment Configuration Info',
			'payment_methods_id' => 'Payment Methods',
			'payment_configuration_info_title' => 'Payment Configuration Info Title',
			'payment_configuration_info_key' => 'Payment Configuration Info Key',
			'payment_configuration_info_description' => 'Payment Configuration Info Description',
			'payment_configuration_info_sort_order' => 'Payment Configuration Info Sort Order',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'use_function' => 'Use Function',
			'set_function' => 'Set Function',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_configuration_info_id',$this->payment_configuration_info_id);
		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_configuration_info_title',$this->payment_configuration_info_title,true);
		$criteria->compare('payment_configuration_info_key',$this->payment_configuration_info_key,true);
		$criteria->compare('payment_configuration_info_description',$this->payment_configuration_info_description,true);
		$criteria->compare('payment_configuration_info_sort_order',$this->payment_configuration_info_sort_order);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('use_function',$this->use_function,true);
		$criteria->compare('set_function',$this->set_function,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}