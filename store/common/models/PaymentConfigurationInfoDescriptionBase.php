<?php

/**
 * This is the model class for table "payment_configuration_info_description".
 *
 * The followings are the available columns in table 'payment_configuration_info_description':
 * @property integer $payment_configuration_info_id
 * @property integer $languages_id
 * @property string $payment_configuration_info_value
 */
class PaymentConfigurationInfoDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentConfigurationInfoDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_configuration_info_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_configuration_info_value', 'required'),
			array('payment_configuration_info_id, languages_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_configuration_info_id, languages_id, payment_configuration_info_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_configuration_info_id' => 'Payment Configuration Info',
			'languages_id' => 'Languages',
			'payment_configuration_info_value' => 'Payment Configuration Info Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_configuration_info_id',$this->payment_configuration_info_id);
		$criteria->compare('languages_id',$this->languages_id);
		$criteria->compare('payment_configuration_info_value',$this->payment_configuration_info_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}