<?php

/**
 * This is the model class for table "payment_extra_info".
 *
 * The followings are the available columns in table 'payment_extra_info':
 * @property string $orders_id
 * @property string $transaction_id
 * @property integer $transaction_status
 * @property string $transaction_amount
 * @property string $transaction_currency
 * @property string $transaction_text_amount
 * @property string $credit_card_type
 * @property string $credit_card_owner
 * @property string $cardholder_aut_result
 * @property string $email_address
 * @property string $billing_address
 * @property string $country
 * @property string $country_code
 * @property string $ip_address
 * @property string $tel
 * @property string $fax
 * @property string $check_result
 * @property string $alert_message
 * @property string $authorisation_mode
 * @property string $authorisation_result
 * @property string $transaction_net_amount
 */
class PaymentExtraInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentExtraInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_extra_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orders_id', 'required'),
			array('transaction_status', 'numerical', 'integerOnly'=>true),
			array('orders_id', 'length', 'max'=>11),
			array('transaction_id, transaction_amount, country_code, transaction_net_amount', 'length', 'max'=>15),
			array('transaction_currency', 'length', 'max'=>10),
			array('transaction_text_amount, tel, fax', 'length', 'max'=>32),
			array('credit_card_type, cardholder_aut_result, ip_address', 'length', 'max'=>64),
			array('credit_card_owner, email_address, billing_address, country, check_result, alert_message', 'length', 'max'=>255),
			array('authorisation_mode', 'length', 'max'=>1),
			array('authorisation_result', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, transaction_id, transaction_status, transaction_amount, transaction_currency, transaction_text_amount, credit_card_type, credit_card_owner, cardholder_aut_result, email_address, billing_address, country, country_code, ip_address, tel, fax, check_result, alert_message, authorisation_mode, authorisation_result, transaction_net_amount', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'transaction_id' => 'Transaction',
			'transaction_status' => 'Transaction Status',
			'transaction_amount' => 'Transaction Amount',
			'transaction_currency' => 'Transaction Currency',
			'transaction_text_amount' => 'Transaction Text Amount',
			'credit_card_type' => 'Credit Card Type',
			'credit_card_owner' => 'Credit Card Owner',
			'cardholder_aut_result' => 'Cardholder Aut Result',
			'email_address' => 'Email Address',
			'billing_address' => 'Billing Address',
			'country' => 'Country',
			'country_code' => 'Country Code',
			'ip_address' => 'Ip Address',
			'tel' => 'Tel',
			'fax' => 'Fax',
			'check_result' => 'Check Result',
			'alert_message' => 'Alert Message',
			'authorisation_mode' => 'Authorisation Mode',
			'authorisation_result' => 'Authorisation Result',
			'transaction_net_amount' => 'Transaction Net Amount',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id,true);
		$criteria->compare('transaction_id',$this->transaction_id,true);
		$criteria->compare('transaction_status',$this->transaction_status);
		$criteria->compare('transaction_amount',$this->transaction_amount,true);
		$criteria->compare('transaction_currency',$this->transaction_currency,true);
		$criteria->compare('transaction_text_amount',$this->transaction_text_amount,true);
		$criteria->compare('credit_card_type',$this->credit_card_type,true);
		$criteria->compare('credit_card_owner',$this->credit_card_owner,true);
		$criteria->compare('cardholder_aut_result',$this->cardholder_aut_result,true);
		$criteria->compare('email_address',$this->email_address,true);
		$criteria->compare('billing_address',$this->billing_address,true);
		$criteria->compare('country',$this->country,true);
		$criteria->compare('country_code',$this->country_code,true);
		$criteria->compare('ip_address',$this->ip_address,true);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('fax',$this->fax,true);
		$criteria->compare('check_result',$this->check_result,true);
		$criteria->compare('alert_message',$this->alert_message,true);
		$criteria->compare('authorisation_mode',$this->authorisation_mode,true);
		$criteria->compare('authorisation_result',$this->authorisation_result,true);
		$criteria->compare('transaction_net_amount',$this->transaction_net_amount,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}