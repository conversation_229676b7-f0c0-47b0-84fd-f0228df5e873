<?php

/**
 * This is the model class for table "payment_fees".
 *
 * The followings are the available columns in table 'payment_fees':
 * @property integer $payment_fees_id
 * @property integer $payment_methods_id
 * @property string $payment_methods_currency_code
 * @property string $payment_methods_mode
 * @property string $payment_fees_operator
 * @property string $payment_fees_max
 * @property string $payment_fees_min
 * @property string $payment_fees_cost_value
 * @property string $payment_fees_cost_percent
 * @property string $payment_fees_cost_percent_min
 * @property string $payment_fees_cost_percent_max
 * @property string $payment_fees_bear_by
 * @property string $payment_fees_below_min
 * @property integer $payment_fees_customers_groups_id
 * @property integer $payment_fees_follow_group
 * @property string $currency_code
 */
class PaymentFeesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentFeesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_fees';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id, payment_fees_customers_groups_id, payment_fees_follow_group', 'numerical', 'integerOnly'=>true),
			array('payment_methods_currency_code, currency_code', 'length', 'max'=>3),
			array('payment_methods_mode', 'length', 'max'=>10),
			array('payment_fees_operator', 'length', 'max'=>2),
			array('payment_fees_max, payment_fees_min', 'length', 'max'=>15),
			array('payment_fees_cost_value, payment_fees_cost_percent_min, payment_fees_cost_percent_max', 'length', 'max'=>8),
			array('payment_fees_cost_percent', 'length', 'max'=>6),
			array('payment_fees_bear_by, payment_fees_below_min', 'length', 'max'=>20),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_fees_id, payment_methods_id, payment_methods_currency_code, payment_methods_mode, payment_fees_operator, payment_fees_max, payment_fees_min, payment_fees_cost_value, payment_fees_cost_percent, payment_fees_cost_percent_min, payment_fees_cost_percent_max, payment_fees_bear_by, payment_fees_below_min, payment_fees_customers_groups_id, payment_fees_follow_group, currency_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_fees_id' => 'Payment Fees',
			'payment_methods_id' => 'Payment Methods',
			'payment_methods_currency_code' => 'Payment Methods Currency Code',
			'payment_methods_mode' => 'Payment Methods Mode',
			'payment_fees_operator' => 'Payment Fees Operator',
			'payment_fees_max' => 'Payment Fees Max',
			'payment_fees_min' => 'Payment Fees Min',
			'payment_fees_cost_value' => 'Payment Fees Cost Value',
			'payment_fees_cost_percent' => 'Payment Fees Cost Percent',
			'payment_fees_cost_percent_min' => 'Payment Fees Cost Percent Min',
			'payment_fees_cost_percent_max' => 'Payment Fees Cost Percent Max',
			'payment_fees_bear_by' => 'Payment Fees Bear By',
			'payment_fees_below_min' => 'Payment Fees Below Min',
			'payment_fees_customers_groups_id' => 'Payment Fees Customers Groups',
			'payment_fees_follow_group' => 'Payment Fees Follow Group',
			'currency_code' => 'Currency Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_fees_id',$this->payment_fees_id);
		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_methods_currency_code',$this->payment_methods_currency_code,true);
		$criteria->compare('payment_methods_mode',$this->payment_methods_mode,true);
		$criteria->compare('payment_fees_operator',$this->payment_fees_operator,true);
		$criteria->compare('payment_fees_max',$this->payment_fees_max,true);
		$criteria->compare('payment_fees_min',$this->payment_fees_min,true);
		$criteria->compare('payment_fees_cost_value',$this->payment_fees_cost_value,true);
		$criteria->compare('payment_fees_cost_percent',$this->payment_fees_cost_percent,true);
		$criteria->compare('payment_fees_cost_percent_min',$this->payment_fees_cost_percent_min,true);
		$criteria->compare('payment_fees_cost_percent_max',$this->payment_fees_cost_percent_max,true);
		$criteria->compare('payment_fees_bear_by',$this->payment_fees_bear_by,true);
		$criteria->compare('payment_fees_below_min',$this->payment_fees_below_min,true);
		$criteria->compare('payment_fees_customers_groups_id',$this->payment_fees_customers_groups_id);
		$criteria->compare('payment_fees_follow_group',$this->payment_fees_follow_group);
		$criteria->compare('currency_code',$this->currency_code,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}