<?php

/**
 * This is the model class for table "payment_gateway_instance".
 *
 * The followings are the available columns in table 'payment_gateway_instance':
 * @property string $payment_gateway_code
 * @property string $payment_gateway_instance_key
 * @property string $currency_code
 * @property string $payment_gateway_instance_value
 */
class PaymentGatewayInstanceBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentGatewayInstanceBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_gateway_instance';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_gateway_code', 'length', 'max'=>32),
			array('payment_gateway_instance_key', 'length', 'max'=>100),
			array('currency_code', 'length', 'max'=>3),
			array('payment_gateway_instance_value', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_gateway_code, payment_gateway_instance_key, currency_code, payment_gateway_instance_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_gateway_code' => 'Payment Gateway Code',
			'payment_gateway_instance_key' => 'Payment Gateway Instance Key',
			'currency_code' => 'Currency Code',
			'payment_gateway_instance_value' => 'Payment Gateway Instance Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_gateway_code',$this->payment_gateway_code,true);
		$criteria->compare('payment_gateway_instance_key',$this->payment_gateway_instance_key,true);
		$criteria->compare('currency_code',$this->currency_code,true);
		$criteria->compare('payment_gateway_instance_value',$this->payment_gateway_instance_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}