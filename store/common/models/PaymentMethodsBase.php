<?php

/**
 * This is the model class for table "payment_methods".
 *
 * The followings are the available columns in table 'payment_methods':
 * @property integer $payment_methods_id
 * @property string $payment_methods_code
 * @property string $payment_methods_title
 * @property integer $payment_methods_send_status
 * @property integer $payment_methods_send_status_mode
 * @property string $payment_methods_send_mode_name
 * @property string $payment_methods_send_required_info
 * @property string $payment_methods_send_currency
 * @property string $payment_methods_estimated_receive_period
 * @property integer $payment_methods_send_mass_payment
 * @property string $payment_methods_send_available_sites
 * @property integer $payment_methods_receive_status
 * @property integer $payment_methods_receive_status_mode
 * @property integer $payment_methods_sort_order
 * @property string $payment_methods_admin_groups_id
 * @property integer $payment_methods_types_id
 * @property integer $payment_methods_parent_id
 * @property string $payment_methods_legend_color
 * @property string $payment_methods_filename
 * @property string $payment_methods_logo
 * @property string $date_added
 * @property string $last_modified
 */
class PaymentMethodsBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return PaymentMethodsBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'payment_methods';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('payment_methods_admin_groups_id', 'required'), array(
                'payment_methods_send_status, payment_methods_send_status_mode, payment_methods_send_mass_payment, payment_methods_receive_status, payment_methods_receive_status_mode, payment_methods_sort_order, payment_methods_types_id, payment_methods_parent_id',
                'numerical', 'integerOnly' => true
            ), array('payment_methods_code', 'length', 'max' => 32), array(
                'payment_methods_title, payment_methods_send_mode_name, payment_methods_filename, payment_methods_logo',
                'length', 'max' => 255
            ), array('payment_methods_send_currency', 'length', 'max' => 3),
            array('payment_methods_estimated_receive_period', 'length', 'max' => 11),
            array('payment_methods_send_available_sites', 'length', 'max' => 12),
            array('payment_methods_legend_color', 'length', 'max' => 7),
            array('payment_methods_send_required_info, date_added, last_modified', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'payment_methods_id, payment_methods_code, payment_methods_title, payment_methods_send_status, payment_methods_send_status_mode, payment_methods_send_mode_name, payment_methods_send_required_info, payment_methods_send_currency, payment_methods_estimated_receive_period, payment_methods_send_mass_payment, payment_methods_send_available_sites, payment_methods_receive_status, payment_methods_receive_status_mode, payment_methods_sort_order, payment_methods_admin_groups_id, payment_methods_types_id, payment_methods_parent_id, payment_methods_legend_color, payment_methods_filename, payment_methods_logo, date_added, last_modified',
                'safe', 'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'payment_methods_id' => 'Payment Methods', 'payment_methods_code' => 'Payment Methods Code',
            'payment_methods_title' => 'Payment Methods Title',
            'payment_methods_send_status' => 'Payment Methods Send Status',
            'payment_methods_send_status_mode' => 'Payment Methods Send Status Mode',
            'payment_methods_send_mode_name' => 'Payment Methods Send Mode Name',
            'payment_methods_send_required_info' => 'Payment Methods Send Required Info',
            'payment_methods_send_currency' => 'Payment Methods Send Currency',
            'payment_methods_estimated_receive_period' => 'Payment Methods Estimated Receive Period',
            'payment_methods_send_mass_payment' => 'Payment Methods Send Mass Payment',
            'payment_methods_send_available_sites' => 'Payment Methods Send Available Sites',
            'payment_methods_receive_status' => 'Payment Methods Receive Status',
            'payment_methods_receive_status_mode' => 'Payment Methods Receive Status Mode',
            'payment_methods_sort_order' => 'Payment Methods Sort Order',
            'payment_methods_admin_groups_id' => 'Payment Methods Admin Groups',
            'payment_methods_types_id' => 'Payment Methods Types',
            'payment_methods_parent_id' => 'Payment Methods Parent',
            'payment_methods_legend_color' => 'Payment Methods Legend Color',
            'payment_methods_filename' => 'Payment Methods Filename', 'payment_methods_logo' => 'Payment Methods Logo',
            'date_added' => 'Date Added', 'last_modified' => 'Last Modified',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('payment_methods_id', $this->payment_methods_id);
        $criteria->compare('payment_methods_code', $this->payment_methods_code, true);
        $criteria->compare('payment_methods_title', $this->payment_methods_title, true);
        $criteria->compare('payment_methods_send_status', $this->payment_methods_send_status);
        $criteria->compare('payment_methods_send_status_mode', $this->payment_methods_send_status_mode);
        $criteria->compare('payment_methods_send_mode_name', $this->payment_methods_send_mode_name, true);
        $criteria->compare('payment_methods_send_required_info', $this->payment_methods_send_required_info, true);
        $criteria->compare('payment_methods_send_currency', $this->payment_methods_send_currency, true);
        $criteria->compare('payment_methods_estimated_receive_period', $this->payment_methods_estimated_receive_period, true);
        $criteria->compare('payment_methods_send_mass_payment', $this->payment_methods_send_mass_payment);
        $criteria->compare('payment_methods_send_available_sites', $this->payment_methods_send_available_sites, true);
        $criteria->compare('payment_methods_receive_status', $this->payment_methods_receive_status);
        $criteria->compare('payment_methods_receive_status_mode', $this->payment_methods_receive_status_mode);
        $criteria->compare('payment_methods_sort_order', $this->payment_methods_sort_order);
        $criteria->compare('payment_methods_admin_groups_id', $this->payment_methods_admin_groups_id, true);
        $criteria->compare('payment_methods_types_id', $this->payment_methods_types_id);
        $criteria->compare('payment_methods_parent_id', $this->payment_methods_parent_id);
        $criteria->compare('payment_methods_legend_color', $this->payment_methods_legend_color, true);
        $criteria->compare('payment_methods_filename', $this->payment_methods_filename, true);
        $criteria->compare('payment_methods_logo', $this->payment_methods_logo, true);
        $criteria->compare('date_added', $this->date_added, true);
        $criteria->compare('last_modified', $this->last_modified, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function getPaymentMethodTypeId($pmId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'payment_methods_types_id';
        $criteria->condition = 'payment_methods_id = :pmId';
        $criteria->params = array(':pmId' => $pmId);
        $result = $this->model()->find($criteria);
        return isset($result->payment_methods_types_id) ? $result->payment_methods_types_id : null;
    }
}