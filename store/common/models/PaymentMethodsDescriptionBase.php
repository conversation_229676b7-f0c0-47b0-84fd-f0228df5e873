<?php

/**
 * This is the model class for table "payment_methods_description".
 *
 * The followings are the available columns in table 'payment_methods_description':
 * @property integer $payment_methods_id
 * @property integer $languages_id
 * @property string $payment_methods_description_title
 */
class PaymentMethodsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_description_title', 'required'),
			array('payment_methods_id, languages_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_id, languages_id, payment_methods_description_title', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_id' => 'Payment Methods',
			'languages_id' => 'Languages',
			'payment_methods_description_title' => 'Payment Methods Description Title',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('languages_id',$this->languages_id);
		$criteria->compare('payment_methods_description_title',$this->payment_methods_description_title,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}