<?php

/**
 * This is the model class for table "payment_methods_fields".
 *
 * The followings are the available columns in table 'payment_methods_fields':
 * @property integer $payment_methods_fields_id
 * @property integer $payment_methods_id
 * @property string $payment_methods_mode
 * @property string $payment_methods_fields_title
 * @property string $payment_methods_fields_pre_info
 * @property string $payment_methods_fields_post_info
 * @property integer $payment_methods_fields_required
 * @property integer $payment_methods_fields_type
 * @property string $payment_methods_fields_system_type
 * @property string $payment_methods_fields_size
 * @property integer $payment_methods_fields_status
 * @property string $payment_methods_fields_option
 * @property integer $payment_methods_fields_options_title
 * @property integer $payment_methods_fields_sort_order
 * @property string $payment_methods_fields_system_mandatory
 */
class PaymentMethodsFieldsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsFieldsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_fields';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id, payment_methods_fields_required, payment_methods_fields_type, payment_methods_fields_status, payment_methods_fields_options_title, payment_methods_fields_sort_order', 'numerical', 'integerOnly'=>true),
			array('payment_methods_mode', 'length', 'max'=>10),
			array('payment_methods_fields_title, payment_methods_fields_pre_info, payment_methods_fields_post_info', 'length', 'max'=>255),
			array('payment_methods_fields_system_type', 'length', 'max'=>64),
			array('payment_methods_fields_size, payment_methods_fields_system_mandatory', 'length', 'max'=>32),
			array('payment_methods_fields_option', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_fields_id, payment_methods_id, payment_methods_mode, payment_methods_fields_title, payment_methods_fields_pre_info, payment_methods_fields_post_info, payment_methods_fields_required, payment_methods_fields_type, payment_methods_fields_system_type, payment_methods_fields_size, payment_methods_fields_status, payment_methods_fields_option, payment_methods_fields_options_title, payment_methods_fields_sort_order, payment_methods_fields_system_mandatory', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_fields_id' => 'Payment Methods Fields',
			'payment_methods_id' => 'Payment Methods',
			'payment_methods_mode' => 'Payment Methods Mode',
			'payment_methods_fields_title' => 'Payment Methods Fields Title',
			'payment_methods_fields_pre_info' => 'Payment Methods Fields Pre Info',
			'payment_methods_fields_post_info' => 'Payment Methods Fields Post Info',
			'payment_methods_fields_required' => 'Payment Methods Fields Required',
			'payment_methods_fields_type' => 'Payment Methods Fields Type',
			'payment_methods_fields_system_type' => 'Payment Methods Fields System Type',
			'payment_methods_fields_size' => 'Payment Methods Fields Size',
			'payment_methods_fields_status' => 'Payment Methods Fields Status',
			'payment_methods_fields_option' => 'Payment Methods Fields Option',
			'payment_methods_fields_options_title' => 'Payment Methods Fields Options Title',
			'payment_methods_fields_sort_order' => 'Payment Methods Fields Sort Order',
			'payment_methods_fields_system_mandatory' => 'Payment Methods Fields System Mandatory',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_fields_id',$this->payment_methods_fields_id);
		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_methods_mode',$this->payment_methods_mode,true);
		$criteria->compare('payment_methods_fields_title',$this->payment_methods_fields_title,true);
		$criteria->compare('payment_methods_fields_pre_info',$this->payment_methods_fields_pre_info,true);
		$criteria->compare('payment_methods_fields_post_info',$this->payment_methods_fields_post_info,true);
		$criteria->compare('payment_methods_fields_required',$this->payment_methods_fields_required);
		$criteria->compare('payment_methods_fields_type',$this->payment_methods_fields_type);
		$criteria->compare('payment_methods_fields_system_type',$this->payment_methods_fields_system_type,true);
		$criteria->compare('payment_methods_fields_size',$this->payment_methods_fields_size,true);
		$criteria->compare('payment_methods_fields_status',$this->payment_methods_fields_status);
		$criteria->compare('payment_methods_fields_option',$this->payment_methods_fields_option,true);
		$criteria->compare('payment_methods_fields_options_title',$this->payment_methods_fields_options_title);
		$criteria->compare('payment_methods_fields_sort_order',$this->payment_methods_fields_sort_order);
		$criteria->compare('payment_methods_fields_system_mandatory',$this->payment_methods_fields_system_mandatory,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}