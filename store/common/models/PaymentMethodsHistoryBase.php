<?php

/**
 * This is the model class for table "payment_methods_history".
 *
 * The followings are the available columns in table 'payment_methods_history':
 * @property integer $payment_methods_id
 * @property string $payment_methods_code
 * @property string $payment_methods_title
 * @property string $payment_methods_history_date
 */
class PaymentMethodsHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id', 'numerical', 'integerOnly'=>true),
			array('payment_methods_code, payment_methods_title', 'length', 'max'=>255),
			array('payment_methods_history_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_id, payment_methods_code, payment_methods_title, payment_methods_history_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_id' => 'Payment Methods',
			'payment_methods_code' => 'Payment Methods Code',
			'payment_methods_title' => 'Payment Methods Title',
			'payment_methods_history_date' => 'Payment Methods History Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_methods_code',$this->payment_methods_code,true);
		$criteria->compare('payment_methods_title',$this->payment_methods_title,true);
		$criteria->compare('payment_methods_history_date',$this->payment_methods_history_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}