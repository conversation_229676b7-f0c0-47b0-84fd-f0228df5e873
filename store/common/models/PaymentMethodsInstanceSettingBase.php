<?php

/**
 * This is the model class for table "payment_methods_instance_setting".
 *
 * The followings are the available columns in table 'payment_methods_instance_setting':
 * @property integer $payment_methods_instance_setting_id
 * @property integer $payment_methods_instance_id
 * @property string $payment_methods_instance_setting_key
 * @property string $payment_methods_instance_setting_value
 */
class PaymentMethodsInstanceSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsInstanceSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_instance_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_instance_setting_value', 'required'),
			array('payment_methods_instance_id', 'numerical', 'integerOnly'=>true),
			array('payment_methods_instance_setting_key', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_instance_setting_id, payment_methods_instance_id, payment_methods_instance_setting_key, payment_methods_instance_setting_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_instance_setting_id' => 'Payment Methods Instance Setting',
			'payment_methods_instance_id' => 'Payment Methods Instance',
			'payment_methods_instance_setting_key' => 'Payment Methods Instance Setting Key',
			'payment_methods_instance_setting_value' => 'Payment Methods Instance Setting Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_instance_setting_id',$this->payment_methods_instance_setting_id);
		$criteria->compare('payment_methods_instance_id',$this->payment_methods_instance_id);
		$criteria->compare('payment_methods_instance_setting_key',$this->payment_methods_instance_setting_key,true);
		$criteria->compare('payment_methods_instance_setting_value',$this->payment_methods_instance_setting_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}