<?php

/**
 * This is the model class for table "payment_methods_outgoing_instance".
 *
 * The followings are the available columns in table 'payment_methods_outgoing_instance':
 * @property integer $payment_methods_outgoing_instance_status
 * @property integer $payment_methods_outgoing_instance_id
 * @property integer $payment_methods_id
 * @property string $currency_code
 * @property integer $payment_methods_outgoing_instance_default
 * @property integer $payment_methods_outgoing_instance_follow_default
 */
class PaymentMethodsOutgoingInstanceBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsOutgoingInstanceBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_outgoing_instance';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_outgoing_instance_status, payment_methods_id, payment_methods_outgoing_instance_default, payment_methods_outgoing_instance_follow_default', 'numerical', 'integerOnly'=>true),
			array('currency_code', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_outgoing_instance_status, payment_methods_outgoing_instance_id, payment_methods_id, currency_code, payment_methods_outgoing_instance_default, payment_methods_outgoing_instance_follow_default', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_outgoing_instance_status' => 'Payment Methods Outgoing Instance Status',
			'payment_methods_outgoing_instance_id' => 'Payment Methods Outgoing Instance',
			'payment_methods_id' => 'Payment Methods',
			'currency_code' => 'Currency Code',
			'payment_methods_outgoing_instance_default' => 'Payment Methods Outgoing Instance Default',
			'payment_methods_outgoing_instance_follow_default' => 'Payment Methods Outgoing Instance Follow Default',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_outgoing_instance_status',$this->payment_methods_outgoing_instance_status);
		$criteria->compare('payment_methods_outgoing_instance_id',$this->payment_methods_outgoing_instance_id);
		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('currency_code',$this->currency_code,true);
		$criteria->compare('payment_methods_outgoing_instance_default',$this->payment_methods_outgoing_instance_default);
		$criteria->compare('payment_methods_outgoing_instance_follow_default',$this->payment_methods_outgoing_instance_follow_default);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}