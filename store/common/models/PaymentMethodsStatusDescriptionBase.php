<?php

/**
 * This is the model class for table "payment_methods_status_description".
 *
 * The followings are the available columns in table 'payment_methods_status_description':
 * @property integer $payment_methods_id
 * @property string $payment_methods_mode
 * @property integer $payment_methods_status
 * @property integer $languages_id
 * @property string $payment_methods_status_message
 */
class PaymentMethodsStatusDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsStatusDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_status_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_id, payment_methods_status, languages_id', 'numerical', 'integerOnly'=>true),
			array('payment_methods_mode', 'length', 'max'=>12),
			array('payment_methods_status_message', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_id, payment_methods_mode, payment_methods_status, languages_id, payment_methods_status_message', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_id' => 'Payment Methods',
			'payment_methods_mode' => 'Payment Methods Mode',
			'payment_methods_status' => 'Payment Methods Status',
			'languages_id' => 'Languages',
			'payment_methods_status_message' => 'Payment Methods Status Message',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_id',$this->payment_methods_id);
		$criteria->compare('payment_methods_mode',$this->payment_methods_mode,true);
		$criteria->compare('payment_methods_status',$this->payment_methods_status);
		$criteria->compare('languages_id',$this->languages_id);
		$criteria->compare('payment_methods_status_message',$this->payment_methods_status_message,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}