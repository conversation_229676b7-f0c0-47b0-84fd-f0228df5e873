<?php

/**
 * This is the model class for table "payment_methods_types".
 *
 * The followings are the available columns in table 'payment_methods_types':
 * @property integer $payment_methods_types_id
 * @property string $payment_methods_types_name
 * @property string $payment_methods_types_mode
 * @property integer $payment_methods_types_system_define
 * @property string $payment_methods_types_sites
 * @property integer $payment_methods_types_sort_order
 */
class PaymentMethodsTypesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsTypesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_types';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_types_system_define, payment_methods_types_sort_order', 'numerical', 'integerOnly'=>true),
			array('payment_methods_types_name', 'length', 'max'=>255),
			array('payment_methods_types_mode', 'length', 'max'=>7),
			array('payment_methods_types_sites', 'length', 'max'=>16),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_types_id, payment_methods_types_name, payment_methods_types_mode, payment_methods_types_system_define, payment_methods_types_sites, payment_methods_types_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_types_id' => 'Payment Methods Types',
			'payment_methods_types_name' => 'Payment Methods Types Name',
			'payment_methods_types_mode' => 'Payment Methods Types Mode',
			'payment_methods_types_system_define' => 'Payment Methods Types System Define',
			'payment_methods_types_sites' => 'Payment Methods Types Sites',
			'payment_methods_types_sort_order' => 'Payment Methods Types Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_types_id',$this->payment_methods_types_id);
		$criteria->compare('payment_methods_types_name',$this->payment_methods_types_name,true);
		$criteria->compare('payment_methods_types_mode',$this->payment_methods_types_mode,true);
		$criteria->compare('payment_methods_types_system_define',$this->payment_methods_types_system_define);
		$criteria->compare('payment_methods_types_sites',$this->payment_methods_types_sites,true);
		$criteria->compare('payment_methods_types_sort_order',$this->payment_methods_types_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}