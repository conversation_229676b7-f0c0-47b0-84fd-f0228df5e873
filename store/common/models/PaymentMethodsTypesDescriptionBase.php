<?php

/**
 * This is the model class for table "payment_methods_types_description".
 *
 * The followings are the available columns in table 'payment_methods_types_description':
 * @property integer $payment_methods_types_id
 * @property integer $languages_id
 * @property string $payment_methods_types_description
 */
class PaymentMethodsTypesDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsTypesDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'payment_methods_types_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('payment_methods_types_id, languages_id', 'numerical', 'integerOnly'=>true),
			array('payment_methods_types_description', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_methods_types_id, languages_id, payment_methods_types_description', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_methods_types_id' => 'Payment Methods Types',
			'languages_id' => 'Languages',
			'payment_methods_types_description' => 'Payment Methods Types Description',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_methods_types_id',$this->payment_methods_types_id);
		$criteria->compare('languages_id',$this->languages_id);
		$criteria->compare('payment_methods_types_description',$this->payment_methods_types_description,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}