<?php

/**
 * This is the model class for table "paynearme_customer".
 *
 * The followings are the available columns in table 'paynearme_customer':
 * @property integer $paynearme_site_order_id
 * @property integer $paynearme_site_customer_id
 * @property string $paynearme_pnm_customer_id
 * @property string $paynearme_pnm_customer_email
 * @property string $paynearme_pnm_customer_phone
 * @property string $paynearme_pnm_customer_city
 * @property string $paynearme_pnm_customer_state
 * @property string $paynearme_pnm_customer_postal_code
 */
class PaynearmeCustomerBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaynearmeCustomerBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paynearme_customer';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paynearme_site_order_id, paynearme_site_customer_id', 'numerical', 'integerOnly'=>true),
			array('paynearme_pnm_customer_id', 'length', 'max'=>30),
			array('paynearme_pnm_customer_email', 'length', 'max'=>128),
			array('paynearme_pnm_customer_phone', 'length', 'max'=>16),
			array('paynearme_pnm_customer_city', 'length', 'max'=>100),
			array('paynearme_pnm_customer_state', 'length', 'max'=>50),
			array('paynearme_pnm_customer_postal_code', 'length', 'max'=>12),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paynearme_site_order_id, paynearme_site_customer_id, paynearme_pnm_customer_id, paynearme_pnm_customer_email, paynearme_pnm_customer_phone, paynearme_pnm_customer_city, paynearme_pnm_customer_state, paynearme_pnm_customer_postal_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paynearme_site_order_id' => 'Paynearme Site Order',
			'paynearme_site_customer_id' => 'Paynearme Site Customer',
			'paynearme_pnm_customer_id' => 'Paynearme Pnm Customer',
			'paynearme_pnm_customer_email' => 'Paynearme Pnm Customer Email',
			'paynearme_pnm_customer_phone' => 'Paynearme Pnm Customer Phone',
			'paynearme_pnm_customer_city' => 'Paynearme Pnm Customer City',
			'paynearme_pnm_customer_state' => 'Paynearme Pnm Customer State',
			'paynearme_pnm_customer_postal_code' => 'Paynearme Pnm Customer Postal Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paynearme_site_order_id',$this->paynearme_site_order_id);
		$criteria->compare('paynearme_site_customer_id',$this->paynearme_site_customer_id);
		$criteria->compare('paynearme_pnm_customer_id',$this->paynearme_pnm_customer_id,true);
		$criteria->compare('paynearme_pnm_customer_email',$this->paynearme_pnm_customer_email,true);
		$criteria->compare('paynearme_pnm_customer_phone',$this->paynearme_pnm_customer_phone,true);
		$criteria->compare('paynearme_pnm_customer_city',$this->paynearme_pnm_customer_city,true);
		$criteria->compare('paynearme_pnm_customer_state',$this->paynearme_pnm_customer_state,true);
		$criteria->compare('paynearme_pnm_customer_postal_code',$this->paynearme_pnm_customer_postal_code,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}