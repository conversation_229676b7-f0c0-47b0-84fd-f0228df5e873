<?php

/**
 * This is the model class for table "paynearme_order".
 *
 * The followings are the available columns in table 'paynearme_order':
 * @property string $paynearme_site_id
 * @property string $paynearme_site_name
 * @property integer $paynearme_site_customer_id
 * @property integer $paynearme_order_id
 * @property string $paynearme_order_created
 * @property string $paynearme_order_status
 * @property string $paynearme_order_amount
 * @property string $paynearme_order_currency
 * @property string $paynearme_order_type
 * @property string $paynearme_order_tracking_url
 * @property integer $paynearme_pnm_order_id
 * @property string $paynearme_pnm_balance_due_amount
 * @property string $paynearme_pnm_balance_due_currency
 */
class PaynearmeOrderBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaynearmeOrderBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paynearme_order';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paynearme_site_customer_id, paynearme_order_id, paynearme_pnm_order_id', 'numerical', 'integerOnly'=>true),
			array('paynearme_site_id', 'length', 'max'=>16),
			array('paynearme_site_name', 'length', 'max'=>24),
			array('paynearme_order_created', 'length', 'max'=>32),
			array('paynearme_order_status', 'length', 'max'=>12),
			array('paynearme_order_amount, paynearme_pnm_balance_due_amount', 'length', 'max'=>15),
			array('paynearme_order_currency, paynearme_pnm_balance_due_currency', 'length', 'max'=>3),
			array('paynearme_order_type', 'length', 'max'=>5),
			array('paynearme_order_tracking_url', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paynearme_site_id, paynearme_site_name, paynearme_site_customer_id, paynearme_order_id, paynearme_order_created, paynearme_order_status, paynearme_order_amount, paynearme_order_currency, paynearme_order_type, paynearme_order_tracking_url, paynearme_pnm_order_id, paynearme_pnm_balance_due_amount, paynearme_pnm_balance_due_currency', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paynearme_site_id' => 'Paynearme Site',
			'paynearme_site_name' => 'Paynearme Site Name',
			'paynearme_site_customer_id' => 'Paynearme Site Customer',
			'paynearme_order_id' => 'Paynearme Order',
			'paynearme_order_created' => 'Paynearme Order Created',
			'paynearme_order_status' => 'Paynearme Order Status',
			'paynearme_order_amount' => 'Paynearme Order Amount',
			'paynearme_order_currency' => 'Paynearme Order Currency',
			'paynearme_order_type' => 'Paynearme Order Type',
			'paynearme_order_tracking_url' => 'Paynearme Order Tracking Url',
			'paynearme_pnm_order_id' => 'Paynearme Pnm Order',
			'paynearme_pnm_balance_due_amount' => 'Paynearme Pnm Balance Due Amount',
			'paynearme_pnm_balance_due_currency' => 'Paynearme Pnm Balance Due Currency',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paynearme_site_id',$this->paynearme_site_id,true);
		$criteria->compare('paynearme_site_name',$this->paynearme_site_name,true);
		$criteria->compare('paynearme_site_customer_id',$this->paynearme_site_customer_id);
		$criteria->compare('paynearme_order_id',$this->paynearme_order_id);
		$criteria->compare('paynearme_order_created',$this->paynearme_order_created,true);
		$criteria->compare('paynearme_order_status',$this->paynearme_order_status,true);
		$criteria->compare('paynearme_order_amount',$this->paynearme_order_amount,true);
		$criteria->compare('paynearme_order_currency',$this->paynearme_order_currency,true);
		$criteria->compare('paynearme_order_type',$this->paynearme_order_type,true);
		$criteria->compare('paynearme_order_tracking_url',$this->paynearme_order_tracking_url,true);
		$criteria->compare('paynearme_pnm_order_id',$this->paynearme_pnm_order_id);
		$criteria->compare('paynearme_pnm_balance_due_amount',$this->paynearme_pnm_balance_due_amount,true);
		$criteria->compare('paynearme_pnm_balance_due_currency',$this->paynearme_pnm_balance_due_currency,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}