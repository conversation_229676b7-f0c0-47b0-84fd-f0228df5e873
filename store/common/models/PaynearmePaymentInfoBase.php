<?php

/**
 * This is the model class for table "paynearme_payment_info".
 *
 * The followings are the available columns in table 'paynearme_payment_info':
 * @property integer $paynearme_site_order_id
 * @property string $paynearme_pending_amount
 * @property string $paynearme_pending_currency
 * @property string $paynearme_pending_timestamp
 * @property string $paynearme_authorization_due_to_site_amount
 * @property string $paynearme_authorization_due_to_site_currency
 * @property string $paynearme_authorization_net_payment_amount
 * @property string $paynearme_authorization_net_payment_currency
 * @property string $paynearme_authorization_payment_amount
 * @property string $paynearme_authorization_payment_currency
 * @property string $paynearme_authorization_payment_date
 * @property integer $paynearme_authorization_pnm_payment_id
 * @property string $paynearme_authorization_pnm_withheld_amount
 * @property string $paynearme_authorization_pnm_withheld_currency
 * @property string $paynearme_authorization_processing_fee_amount
 * @property string $paynearme_authorization_processing_fee_currency
 * @property string $paynearme_authorization_timestamp
 * @property string $paynearme_payment_due_to_site_amount
 * @property string $paynearme_payment_due_to_site_currency
 * @property string $paynearme_payment_net_payment_amount
 * @property string $paynearme_payment_net_payment_currency
 * @property string $paynearme_payment_payment_amount
 * @property string $paynearme_payment_payment_currency
 * @property integer $paynearme_payment_pnm_payment_id
 * @property string $paynearme_payment_pnm_withheld_amount
 * @property string $paynearme_payment_pnm_withheld_currency
 * @property string $paynearme_payment_processing_fee_amount
 * @property string $paynearme_payment_processing_fee_currency
 * @property string $paynearme_payment_status
 * @property string $paynearme_payment_timestamp
 * @property string $paynearme_full_encoding
 * @property string $paynearme_slip_pdf_url
 * @property string $paynearme_card_pdf_url
 */
class PaynearmePaymentInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaynearmePaymentInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paynearme_payment_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paynearme_site_order_id, paynearme_authorization_pnm_payment_id, paynearme_payment_pnm_payment_id', 'numerical', 'integerOnly'=>true),
			array('paynearme_pending_amount, paynearme_authorization_due_to_site_amount, paynearme_authorization_net_payment_amount, paynearme_authorization_payment_amount, paynearme_authorization_pnm_withheld_amount, paynearme_payment_due_to_site_amount, paynearme_payment_net_payment_amount, paynearme_payment_payment_amount, paynearme_payment_pnm_withheld_amount', 'length', 'max'=>15),
			array('paynearme_pending_currency, paynearme_authorization_due_to_site_currency, paynearme_authorization_net_payment_currency, paynearme_authorization_payment_currency, paynearme_authorization_pnm_withheld_currency, paynearme_authorization_processing_fee_currency, paynearme_payment_due_to_site_currency, paynearme_payment_net_payment_currency, paynearme_payment_payment_currency, paynearme_payment_pnm_withheld_currency, paynearme_payment_processing_fee_currency', 'length', 'max'=>3),
			array('paynearme_authorization_processing_fee_amount, paynearme_payment_processing_fee_amount', 'length', 'max'=>10),
			array('paynearme_payment_status', 'length', 'max'=>24),
			array('paynearme_full_encoding', 'length', 'max'=>64),
			array('paynearme_slip_pdf_url, paynearme_card_pdf_url', 'length', 'max'=>250),
			array('paynearme_pending_timestamp, paynearme_authorization_payment_date, paynearme_authorization_timestamp, paynearme_payment_timestamp', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paynearme_site_order_id, paynearme_pending_amount, paynearme_pending_currency, paynearme_pending_timestamp, paynearme_authorization_due_to_site_amount, paynearme_authorization_due_to_site_currency, paynearme_authorization_net_payment_amount, paynearme_authorization_net_payment_currency, paynearme_authorization_payment_amount, paynearme_authorization_payment_currency, paynearme_authorization_payment_date, paynearme_authorization_pnm_payment_id, paynearme_authorization_pnm_withheld_amount, paynearme_authorization_pnm_withheld_currency, paynearme_authorization_processing_fee_amount, paynearme_authorization_processing_fee_currency, paynearme_authorization_timestamp, paynearme_payment_due_to_site_amount, paynearme_payment_due_to_site_currency, paynearme_payment_net_payment_amount, paynearme_payment_net_payment_currency, paynearme_payment_payment_amount, paynearme_payment_payment_currency, paynearme_payment_pnm_payment_id, paynearme_payment_pnm_withheld_amount, paynearme_payment_pnm_withheld_currency, paynearme_payment_processing_fee_amount, paynearme_payment_processing_fee_currency, paynearme_payment_status, paynearme_payment_timestamp, paynearme_full_encoding, paynearme_slip_pdf_url, paynearme_card_pdf_url', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paynearme_site_order_id' => 'Paynearme Site Order',
			'paynearme_pending_amount' => 'Paynearme Pending Amount',
			'paynearme_pending_currency' => 'Paynearme Pending Currency',
			'paynearme_pending_timestamp' => 'Paynearme Pending Timestamp',
			'paynearme_authorization_due_to_site_amount' => 'Paynearme Authorization Due To Site Amount',
			'paynearme_authorization_due_to_site_currency' => 'Paynearme Authorization Due To Site Currency',
			'paynearme_authorization_net_payment_amount' => 'Paynearme Authorization Net Payment Amount',
			'paynearme_authorization_net_payment_currency' => 'Paynearme Authorization Net Payment Currency',
			'paynearme_authorization_payment_amount' => 'Paynearme Authorization Payment Amount',
			'paynearme_authorization_payment_currency' => 'Paynearme Authorization Payment Currency',
			'paynearme_authorization_payment_date' => 'Paynearme Authorization Payment Date',
			'paynearme_authorization_pnm_payment_id' => 'Paynearme Authorization Pnm Payment',
			'paynearme_authorization_pnm_withheld_amount' => 'Paynearme Authorization Pnm Withheld Amount',
			'paynearme_authorization_pnm_withheld_currency' => 'Paynearme Authorization Pnm Withheld Currency',
			'paynearme_authorization_processing_fee_amount' => 'Paynearme Authorization Processing Fee Amount',
			'paynearme_authorization_processing_fee_currency' => 'Paynearme Authorization Processing Fee Currency',
			'paynearme_authorization_timestamp' => 'Paynearme Authorization Timestamp',
			'paynearme_payment_due_to_site_amount' => 'Paynearme Payment Due To Site Amount',
			'paynearme_payment_due_to_site_currency' => 'Paynearme Payment Due To Site Currency',
			'paynearme_payment_net_payment_amount' => 'Paynearme Payment Net Payment Amount',
			'paynearme_payment_net_payment_currency' => 'Paynearme Payment Net Payment Currency',
			'paynearme_payment_payment_amount' => 'Paynearme Payment Payment Amount',
			'paynearme_payment_payment_currency' => 'Paynearme Payment Payment Currency',
			'paynearme_payment_pnm_payment_id' => 'Paynearme Payment Pnm Payment',
			'paynearme_payment_pnm_withheld_amount' => 'Paynearme Payment Pnm Withheld Amount',
			'paynearme_payment_pnm_withheld_currency' => 'Paynearme Payment Pnm Withheld Currency',
			'paynearme_payment_processing_fee_amount' => 'Paynearme Payment Processing Fee Amount',
			'paynearme_payment_processing_fee_currency' => 'Paynearme Payment Processing Fee Currency',
			'paynearme_payment_status' => 'Paynearme Payment Status',
			'paynearme_payment_timestamp' => 'Paynearme Payment Timestamp',
			'paynearme_full_encoding' => 'Paynearme Full Encoding',
			'paynearme_slip_pdf_url' => 'Paynearme Slip Pdf Url',
			'paynearme_card_pdf_url' => 'Paynearme Card Pdf Url',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paynearme_site_order_id',$this->paynearme_site_order_id);
		$criteria->compare('paynearme_pending_amount',$this->paynearme_pending_amount,true);
		$criteria->compare('paynearme_pending_currency',$this->paynearme_pending_currency,true);
		$criteria->compare('paynearme_pending_timestamp',$this->paynearme_pending_timestamp,true);
		$criteria->compare('paynearme_authorization_due_to_site_amount',$this->paynearme_authorization_due_to_site_amount,true);
		$criteria->compare('paynearme_authorization_due_to_site_currency',$this->paynearme_authorization_due_to_site_currency,true);
		$criteria->compare('paynearme_authorization_net_payment_amount',$this->paynearme_authorization_net_payment_amount,true);
		$criteria->compare('paynearme_authorization_net_payment_currency',$this->paynearme_authorization_net_payment_currency,true);
		$criteria->compare('paynearme_authorization_payment_amount',$this->paynearme_authorization_payment_amount,true);
		$criteria->compare('paynearme_authorization_payment_currency',$this->paynearme_authorization_payment_currency,true);
		$criteria->compare('paynearme_authorization_payment_date',$this->paynearme_authorization_payment_date,true);
		$criteria->compare('paynearme_authorization_pnm_payment_id',$this->paynearme_authorization_pnm_payment_id);
		$criteria->compare('paynearme_authorization_pnm_withheld_amount',$this->paynearme_authorization_pnm_withheld_amount,true);
		$criteria->compare('paynearme_authorization_pnm_withheld_currency',$this->paynearme_authorization_pnm_withheld_currency,true);
		$criteria->compare('paynearme_authorization_processing_fee_amount',$this->paynearme_authorization_processing_fee_amount,true);
		$criteria->compare('paynearme_authorization_processing_fee_currency',$this->paynearme_authorization_processing_fee_currency,true);
		$criteria->compare('paynearme_authorization_timestamp',$this->paynearme_authorization_timestamp,true);
		$criteria->compare('paynearme_payment_due_to_site_amount',$this->paynearme_payment_due_to_site_amount,true);
		$criteria->compare('paynearme_payment_due_to_site_currency',$this->paynearme_payment_due_to_site_currency,true);
		$criteria->compare('paynearme_payment_net_payment_amount',$this->paynearme_payment_net_payment_amount,true);
		$criteria->compare('paynearme_payment_net_payment_currency',$this->paynearme_payment_net_payment_currency,true);
		$criteria->compare('paynearme_payment_payment_amount',$this->paynearme_payment_payment_amount,true);
		$criteria->compare('paynearme_payment_payment_currency',$this->paynearme_payment_payment_currency,true);
		$criteria->compare('paynearme_payment_pnm_payment_id',$this->paynearme_payment_pnm_payment_id);
		$criteria->compare('paynearme_payment_pnm_withheld_amount',$this->paynearme_payment_pnm_withheld_amount,true);
		$criteria->compare('paynearme_payment_pnm_withheld_currency',$this->paynearme_payment_pnm_withheld_currency,true);
		$criteria->compare('paynearme_payment_processing_fee_amount',$this->paynearme_payment_processing_fee_amount,true);
		$criteria->compare('paynearme_payment_processing_fee_currency',$this->paynearme_payment_processing_fee_currency,true);
		$criteria->compare('paynearme_payment_status',$this->paynearme_payment_status,true);
		$criteria->compare('paynearme_payment_timestamp',$this->paynearme_payment_timestamp,true);
		$criteria->compare('paynearme_full_encoding',$this->paynearme_full_encoding,true);
		$criteria->compare('paynearme_slip_pdf_url',$this->paynearme_slip_pdf_url,true);
		$criteria->compare('paynearme_card_pdf_url',$this->paynearme_card_pdf_url,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}