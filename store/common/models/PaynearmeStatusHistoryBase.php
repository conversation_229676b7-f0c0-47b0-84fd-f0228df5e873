<?php

/**
 * This is the model class for table "paynearme_status_history".
 *
 * The followings are the available columns in table 'paynearme_status_history':
 * @property integer $paynearme_status_history_id
 * @property integer $paynearme_order_id
 * @property string $paynearme_date
 * @property string $paynearme_status
 * @property string $paynearme_description
 * @property string $paynearme_changed_by
 */
class PaynearmeStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaynearmeStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paynearme_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paynearme_order_id', 'numerical', 'integerOnly'=>true),
			array('paynearme_status', 'length', 'max'=>24),
			array('paynearme_description', 'length', 'max'=>250),
			array('paynearme_changed_by', 'length', 'max'=>128),
			array('paynearme_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paynearme_status_history_id, paynearme_order_id, paynearme_date, paynearme_status, paynearme_description, paynearme_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paynearme_status_history_id' => 'Paynearme Status History',
			'paynearme_order_id' => 'Paynearme Order',
			'paynearme_date' => 'Paynearme Date',
			'paynearme_status' => 'Paynearme Status',
			'paynearme_description' => 'Paynearme Description',
			'paynearme_changed_by' => 'Paynearme Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paynearme_status_history_id',$this->paynearme_status_history_id);
		$criteria->compare('paynearme_order_id',$this->paynearme_order_id);
		$criteria->compare('paynearme_date',$this->paynearme_date,true);
		$criteria->compare('paynearme_status',$this->paynearme_status,true);
		$criteria->compare('paynearme_description',$this->paynearme_description,true);
		$criteria->compare('paynearme_changed_by',$this->paynearme_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}