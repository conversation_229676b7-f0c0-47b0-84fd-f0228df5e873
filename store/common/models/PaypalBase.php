<?php

/**
 * This is the model class for table "paypal".
 *
 * The followings are the available columns in table 'paypal':
 * @property string $paypal_ipn_id
 * @property string $txn_type
 * @property string $reason_code
 * @property string $payment_type
 * @property string $payment_status
 * @property string $pending_reason
 * @property string $invoice
 * @property string $mc_currency
 * @property string $first_name
 * @property string $last_name
 * @property string $payer_business_name
 * @property string $address_name
 * @property string $address_street
 * @property string $address_city
 * @property string $address_state
 * @property string $address_zip
 * @property string $address_country
 * @property string $address_status
 * @property string $payer_email
 * @property string $payer_id
 * @property string $payer_status
 * @property string $payment_date
 * @property string $business
 * @property string $receiver_email
 * @property string $receiver_id
 * @property string $txn_id
 * @property string $parent_txn_id
 * @property integer $num_cart_items
 * @property string $mc_gross
 * @property string $mc_fee
 * @property string $payment_gross
 * @property string $payment_fee
 * @property string $settle_amount
 * @property string $settle_currency
 * @property string $exchange_rate
 * @property string $notify_version
 * @property string $verify_sign
 * @property string $last_modified
 * @property string $date_added
 * @property string $memo
 */
class PaypalBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaypalBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paypal';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('num_cart_items', 'numerical', 'integerOnly'=>true),
			array('txn_type, address_zip, payer_status', 'length', 'max'=>10),
			array('reason_code', 'length', 'max'=>15),
			array('payment_type, mc_gross, mc_fee, payment_gross, payment_fee, settle_amount', 'length', 'max'=>7),
			array('payment_status, txn_id, parent_txn_id', 'length', 'max'=>17),
			array('pending_reason', 'length', 'max'=>14),
			array('invoice, payer_business_name, address_street, address_country', 'length', 'max'=>64),
			array('mc_currency, settle_currency, notify_version', 'length', 'max'=>3),
			array('first_name, last_name, address_name, address_city, address_state, payer_id, receiver_id', 'length', 'max'=>32),
			array('address_status', 'length', 'max'=>11),
			array('payer_email, business, receiver_email', 'length', 'max'=>96),
			array('exchange_rate', 'length', 'max'=>4),
			array('verify_sign', 'length', 'max'=>128),
			array('payment_date, last_modified, date_added, memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paypal_ipn_id, txn_type, reason_code, payment_type, payment_status, pending_reason, invoice, mc_currency, first_name, last_name, payer_business_name, address_name, address_street, address_city, address_state, address_zip, address_country, address_status, payer_email, payer_id, payer_status, payment_date, business, receiver_email, receiver_id, txn_id, parent_txn_id, num_cart_items, mc_gross, mc_fee, payment_gross, payment_fee, settle_amount, settle_currency, exchange_rate, notify_version, verify_sign, last_modified, date_added, memo', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paypal_ipn_id' => 'Paypal Ipn',
			'txn_type' => 'Txn Type',
			'reason_code' => 'Reason Code',
			'payment_type' => 'Payment Type',
			'payment_status' => 'Payment Status',
			'pending_reason' => 'Pending Reason',
			'invoice' => 'Invoice',
			'mc_currency' => 'Mc Currency',
			'first_name' => 'First Name',
			'last_name' => 'Last Name',
			'payer_business_name' => 'Payer Business Name',
			'address_name' => 'Address Name',
			'address_street' => 'Address Street',
			'address_city' => 'Address City',
			'address_state' => 'Address State',
			'address_zip' => 'Address Zip',
			'address_country' => 'Address Country',
			'address_status' => 'Address Status',
			'payer_email' => 'Payer Email',
			'payer_id' => 'Payer',
			'payer_status' => 'Payer Status',
			'payment_date' => 'Payment Date',
			'business' => 'Business',
			'receiver_email' => 'Receiver Email',
			'receiver_id' => 'Receiver',
			'txn_id' => 'Txn',
			'parent_txn_id' => 'Parent Txn',
			'num_cart_items' => 'Num Cart Items',
			'mc_gross' => 'Mc Gross',
			'mc_fee' => 'Mc Fee',
			'payment_gross' => 'Payment Gross',
			'payment_fee' => 'Payment Fee',
			'settle_amount' => 'Settle Amount',
			'settle_currency' => 'Settle Currency',
			'exchange_rate' => 'Exchange Rate',
			'notify_version' => 'Notify Version',
			'verify_sign' => 'Verify Sign',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'memo' => 'Memo',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paypal_ipn_id',$this->paypal_ipn_id,true);
		$criteria->compare('txn_type',$this->txn_type,true);
		$criteria->compare('reason_code',$this->reason_code,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('payment_status',$this->payment_status,true);
		$criteria->compare('pending_reason',$this->pending_reason,true);
		$criteria->compare('invoice',$this->invoice,true);
		$criteria->compare('mc_currency',$this->mc_currency,true);
		$criteria->compare('first_name',$this->first_name,true);
		$criteria->compare('last_name',$this->last_name,true);
		$criteria->compare('payer_business_name',$this->payer_business_name,true);
		$criteria->compare('address_name',$this->address_name,true);
		$criteria->compare('address_street',$this->address_street,true);
		$criteria->compare('address_city',$this->address_city,true);
		$criteria->compare('address_state',$this->address_state,true);
		$criteria->compare('address_zip',$this->address_zip,true);
		$criteria->compare('address_country',$this->address_country,true);
		$criteria->compare('address_status',$this->address_status,true);
		$criteria->compare('payer_email',$this->payer_email,true);
		$criteria->compare('payer_id',$this->payer_id,true);
		$criteria->compare('payer_status',$this->payer_status,true);
		$criteria->compare('payment_date',$this->payment_date,true);
		$criteria->compare('business',$this->business,true);
		$criteria->compare('receiver_email',$this->receiver_email,true);
		$criteria->compare('receiver_id',$this->receiver_id,true);
		$criteria->compare('txn_id',$this->txn_id,true);
		$criteria->compare('parent_txn_id',$this->parent_txn_id,true);
		$criteria->compare('num_cart_items',$this->num_cart_items);
		$criteria->compare('mc_gross',$this->mc_gross,true);
		$criteria->compare('mc_fee',$this->mc_fee,true);
		$criteria->compare('payment_gross',$this->payment_gross,true);
		$criteria->compare('payment_fee',$this->payment_fee,true);
		$criteria->compare('settle_amount',$this->settle_amount,true);
		$criteria->compare('settle_currency',$this->settle_currency,true);
		$criteria->compare('exchange_rate',$this->exchange_rate,true);
		$criteria->compare('notify_version',$this->notify_version,true);
		$criteria->compare('verify_sign',$this->verify_sign,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('memo',$this->memo,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}