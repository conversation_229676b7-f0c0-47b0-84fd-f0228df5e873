<?php

/**
 * This is the model class for table "paypal_payment_status_history".
 *
 * The followings are the available columns in table 'paypal_payment_status_history':
 * @property integer $payment_status_history_id
 * @property integer $paypal_ipn_id
 * @property string $payment_status
 * @property string $pending_reason
 * @property string $date_added
 */
class PaypalPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaypalPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paypal_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paypal_ipn_id', 'numerical', 'integerOnly'=>true),
			array('payment_status', 'length', 'max'=>17),
			array('pending_reason', 'length', 'max'=>14),
			array('date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('payment_status_history_id, paypal_ipn_id, payment_status, pending_reason, date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'payment_status_history_id' => 'Payment Status History',
			'paypal_ipn_id' => 'Paypal Ipn',
			'payment_status' => 'Payment Status',
			'pending_reason' => 'Pending Reason',
			'date_added' => 'Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('payment_status_history_id',$this->payment_status_history_id);
		$criteria->compare('paypal_ipn_id',$this->paypal_ipn_id);
		$criteria->compare('payment_status',$this->payment_status,true);
		$criteria->compare('pending_reason',$this->pending_reason,true);
		$criteria->compare('date_added',$this->date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}