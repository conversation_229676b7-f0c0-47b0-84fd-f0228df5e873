<?php

/**
 * This is the model class for table "paypalec".
 *
 * The followings are the available columns in table 'paypalec':
 * @property string $paypal_order_id
 * @property string $txn_type
 * @property string $payment_type
 * @property string $payment_status
 * @property string $pending_reason
 * @property string $mc_currency
 * @property string $first_name
 * @property string $last_name
 * @property string $address_name
 * @property string $address_street
 * @property string $address_city
 * @property string $address_state
 * @property string $address_zip
 * @property string $address_country
 * @property string $address_status
 * @property string $payer_email
 * @property string $payer_id
 * @property string $payer_status
 * @property string $payment_date
 * @property string $business
 * @property string $receiver_email
 * @property string $receiver_id
 * @property string $txn_id
 * @property string $mc_gross
 * @property string $mc_fee
 * @property string $payment_gross
 * @property string $payment_fee
 * @property string $notify_version
 * @property string $verify_sign
 * @property string $residence_country
 * @property string $protection_eligibility
 * @property string $last_modified
 * @property string $date_added
 * @property string $data
 */
class PaypalecBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaypalecBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'paypalec';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('paypal_order_id, txn_type', 'length', 'max'=>25),
			array('payment_type, pending_reason, protection_eligibility', 'length', 'max'=>15),
			array('payment_status', 'length', 'max'=>20),
			array('mc_currency', 'length', 'max'=>3),
			array('first_name, last_name, address_name, address_city, address_state, payer_id, receiver_id', 'length', 'max'=>32),
			array('address_street, payer_email, business, receiver_email, verify_sign', 'length', 'max'=>128),
			array('address_zip, payer_status, notify_version, residence_country', 'length', 'max'=>10),
			array('address_country', 'length', 'max'=>64),
			array('address_status', 'length', 'max'=>11),
			array('payment_date', 'length', 'max'=>30),
			array('txn_id', 'length', 'max'=>17),
			array('mc_gross, mc_fee, payment_gross, payment_fee', 'length', 'max'=>12),
			array('last_modified, date_added, data', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('paypal_order_id, txn_type, payment_type, payment_status, pending_reason, mc_currency, first_name, last_name, address_name, address_street, address_city, address_state, address_zip, address_country, address_status, payer_email, payer_id, payer_status, payment_date, business, receiver_email, receiver_id, txn_id, mc_gross, mc_fee, payment_gross, payment_fee, notify_version, verify_sign, residence_country, protection_eligibility, last_modified, date_added, data', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'paypal_order_id' => 'Paypal Order',
			'txn_type' => 'Txn Type',
			'payment_type' => 'Payment Type',
			'payment_status' => 'Payment Status',
			'pending_reason' => 'Pending Reason',
			'mc_currency' => 'Mc Currency',
			'first_name' => 'First Name',
			'last_name' => 'Last Name',
			'address_name' => 'Address Name',
			'address_street' => 'Address Street',
			'address_city' => 'Address City',
			'address_state' => 'Address State',
			'address_zip' => 'Address Zip',
			'address_country' => 'Address Country',
			'address_status' => 'Address Status',
			'payer_email' => 'Payer Email',
			'payer_id' => 'Payer',
			'payer_status' => 'Payer Status',
			'payment_date' => 'Payment Date',
			'business' => 'Business',
			'receiver_email' => 'Receiver Email',
			'receiver_id' => 'Receiver',
			'txn_id' => 'Txn',
			'mc_gross' => 'Mc Gross',
			'mc_fee' => 'Mc Fee',
			'payment_gross' => 'Payment Gross',
			'payment_fee' => 'Payment Fee',
			'notify_version' => 'Notify Version',
			'verify_sign' => 'Verify Sign',
			'residence_country' => 'Residence Country',
			'protection_eligibility' => 'Protection Eligibility',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'data' => 'Data',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('paypal_order_id',$this->paypal_order_id,true);
		$criteria->compare('txn_type',$this->txn_type,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('payment_status',$this->payment_status,true);
		$criteria->compare('pending_reason',$this->pending_reason,true);
		$criteria->compare('mc_currency',$this->mc_currency,true);
		$criteria->compare('first_name',$this->first_name,true);
		$criteria->compare('last_name',$this->last_name,true);
		$criteria->compare('address_name',$this->address_name,true);
		$criteria->compare('address_street',$this->address_street,true);
		$criteria->compare('address_city',$this->address_city,true);
		$criteria->compare('address_state',$this->address_state,true);
		$criteria->compare('address_zip',$this->address_zip,true);
		$criteria->compare('address_country',$this->address_country,true);
		$criteria->compare('address_status',$this->address_status,true);
		$criteria->compare('payer_email',$this->payer_email,true);
		$criteria->compare('payer_id',$this->payer_id,true);
		$criteria->compare('payer_status',$this->payer_status,true);
		$criteria->compare('payment_date',$this->payment_date,true);
		$criteria->compare('business',$this->business,true);
		$criteria->compare('receiver_email',$this->receiver_email,true);
		$criteria->compare('receiver_id',$this->receiver_id,true);
		$criteria->compare('txn_id',$this->txn_id,true);
		$criteria->compare('mc_gross',$this->mc_gross,true);
		$criteria->compare('mc_fee',$this->mc_fee,true);
		$criteria->compare('payment_gross',$this->payment_gross,true);
		$criteria->compare('payment_fee',$this->payment_fee,true);
		$criteria->compare('notify_version',$this->notify_version,true);
		$criteria->compare('verify_sign',$this->verify_sign,true);
		$criteria->compare('residence_country',$this->residence_country,true);
		$criteria->compare('protection_eligibility',$this->protection_eligibility,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('data',$this->data,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}