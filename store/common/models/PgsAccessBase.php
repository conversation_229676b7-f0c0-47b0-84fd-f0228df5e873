<?php

/**
 * This is the model class for table "pgs_access".
 *
 * The followings are the available columns in table 'pgs_access':
 * @property string $trans_id
 * @property string $access_code
 * @property string $expiry
 */
class PgsAccessBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PgsAccessBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pgs_access';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('trans_id', 'required'),
			array('trans_id, expiry', 'length', 'max'=>11),
			array('access_code', 'length', 'max'=>40),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('trans_id, access_code, expiry', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'trans_id' => 'Trans',
			'access_code' => 'Access Code',
			'expiry' => 'Expiry',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('trans_id',$this->trans_id,true);
		$criteria->compare('access_code',$this->access_code,true);
		$criteria->compare('expiry',$this->expiry,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}