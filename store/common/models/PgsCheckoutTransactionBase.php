<?php

/**
 * This is the model class for table "pgs_checkout_transaction".
 *
 * The followings are the available columns in table 'pgs_checkout_transaction':
 * @property string $orders_id
 * @property string $merchant_id
 * @property string $payment_method_id
 * @property string $currency
 * @property string $amount
 * @property string $data
 * @property string $surcharge
 */
class PgsCheckoutTransactionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PgsCheckoutTransactionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pgs_checkout_transaction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('currency, amount, data, surcharge', 'required'),
			array('orders_id', 'length', 'max'=>16),
			array('merchant_id, payment_method_id', 'length', 'max'=>11),
			array('currency', 'length', 'max'=>3),
			array('amount, surcharge', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, merchant_id, payment_method_id, currency, amount, data, surcharge', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'merchant_id' => 'Merchant',
			'payment_method_id' => 'Payment Method',
			'currency' => 'Currency',
			'amount' => 'Amount',
			'data' => 'Data',
			'surcharge' => 'Surcharge',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id,true);
		$criteria->compare('merchant_id',$this->merchant_id,true);
		$criteria->compare('payment_method_id',$this->payment_method_id,true);
		$criteria->compare('currency',$this->currency,true);
		$criteria->compare('amount',$this->amount,true);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('surcharge',$this->surcharge,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}