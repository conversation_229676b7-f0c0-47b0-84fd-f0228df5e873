<?php

/**
 * This is the model class for table "pin_request".
 *
 * The followings are the available columns in table 'pin_request':
 * @property integer $pin_request_id
 * @property string $products_id
 * @property string $products_quantity_order
 * @property string $pin_request_qty
 * @property string $pin_currency
 * @property string $pin_amount
 * @property integer $pin_module_trans_id
 * @property string $pin_request_date
 * @property string $pin_receive_date
 * @property integer $pin_request_status
 * @property string $pin_request_message
 * @property string $pin_request_log
 */
class PinRequestBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PinRequestBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pin_request';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pin_module_trans_id, pin_request_status', 'numerical', 'integerOnly'=>true),
			array('products_id', 'length', 'max'=>11),
			array('products_quantity_order, pin_request_qty', 'length', 'max'=>4),
			array('pin_currency', 'length', 'max'=>3),
			array('pin_amount', 'length', 'max'=>15),
			array('pin_request_message', 'length', 'max'=>64),
			array('pin_request_date, pin_receive_date, pin_request_log', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('pin_request_id, products_id, products_quantity_order, pin_request_qty, pin_currency, pin_amount, pin_module_trans_id, pin_request_date, pin_receive_date, pin_request_status, pin_request_message, pin_request_log', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'pin_request_id' => 'Pin Request',
			'products_id' => 'Products',
			'products_quantity_order' => 'Products Quantity Order',
			'pin_request_qty' => 'Pin Request Qty',
			'pin_currency' => 'Pin Currency',
			'pin_amount' => 'Pin Amount',
			'pin_module_trans_id' => 'Pin Module Trans',
			'pin_request_date' => 'Pin Request Date',
			'pin_receive_date' => 'Pin Receive Date',
			'pin_request_status' => 'Pin Request Status',
			'pin_request_message' => 'Pin Request Message',
			'pin_request_log' => 'Pin Request Log',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('pin_request_id',$this->pin_request_id);
		$criteria->compare('products_id',$this->products_id,true);
		$criteria->compare('products_quantity_order',$this->products_quantity_order,true);
		$criteria->compare('pin_request_qty',$this->pin_request_qty,true);
		$criteria->compare('pin_currency',$this->pin_currency,true);
		$criteria->compare('pin_amount',$this->pin_amount,true);
		$criteria->compare('pin_module_trans_id',$this->pin_module_trans_id);
		$criteria->compare('pin_request_date',$this->pin_request_date,true);
		$criteria->compare('pin_receive_date',$this->pin_receive_date,true);
		$criteria->compare('pin_request_status',$this->pin_request_status);
		$criteria->compare('pin_request_message',$this->pin_request_message,true);
		$criteria->compare('pin_request_log',$this->pin_request_log,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}