<?php

/**
 * This is the model class for table "pin_request_info".
 *
 * The followings are the available columns in table 'pin_request_info':
 * @property integer $pin_request_id
 * @property string $custom_products_code_id
 * @property integer $pin_module_pin_id
 */
class PinRequestInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PinRequestInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pin_request_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pin_request_id, pin_module_pin_id', 'numerical', 'integerOnly'=>true),
			array('custom_products_code_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('pin_request_id, custom_products_code_id, pin_module_pin_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'pin_request_id' => 'Pin Request',
			'custom_products_code_id' => 'Custom Products Code',
			'pin_module_pin_id' => 'Pin Module Pin',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('pin_request_id',$this->pin_request_id);
		$criteria->compare('custom_products_code_id',$this->custom_products_code_id,true);
		$criteria->compare('pin_module_pin_id',$this->pin_module_pin_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}