<?php

/**
 * This is the model class for table "pipwave_payment".
 *
 * The followings are the available columns in table 'pipwave_payment':
 * @property string $orders_id
 * @property string $status
 * @property integer $require_upload
 * @property string $api_key
 * @property string $notification_id
 * @property string $notification_date
 * @property string $pw_id
 * @property string $amount
 * @property string $tax_exempted_amount
 * @property string $processing_fee_amount
 * @property string $tax_amount
 * @property string $total_amount
 * @property string $final_amount
 * @property string $currency_code
 * @property string $transaction_status
 * @property string $txn_sub_status
 * @property string $type
 * @property string $subscription_token
 * @property integer $charge_index
 * @property string $payment_method_code
 * @property integer $reversible_payment
 * @property string $settlement_account
 * @property integer $require_capture
 * @property string $mobile_number
 * @property integer $mobile_number_verification
 * @property string $pg_status
 * @property string $pg_reason
 * @property string $pg_date
 * @property string $pg_txn_id
 * @property string $pg_raw_data
 * @property string $risk_management_data
 * @property string $matched_rules
 * @property integer $pipwave_score
 * @property string $rules_action
 * @property string $extra_param1
 * @property string $extra_param2
 * @property string $extra_param3
 * @property string $risk_service_type
 */
class PipwavePaymentBase extends CActiveRecord
{
    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return OrderPipwavePayment the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'pipwave_payment';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_id, status, require_upload, api_key, pw_id, amount, total_amount, final_amount, currency_code, transaction_status, type', 'required'),
            array('require_upload, charge_index, reversible_payment, require_capture, mobile_number_verification, pipwave_score', 'numerical', 'integerOnly'=>true),
            array('orders_id', 'length', 'max'=>11),
            array('status, transaction_status, pg_status', 'length', 'max'=>5),
            array('api_key, pw_id, payment_method_code, settlement_account, mobile_number, pg_reason, extra_param1, extra_param2, extra_param3', 'length', 'max'=>255),
            array('notification_id, type, subscription_token', 'length', 'max'=>32),
            array('amount, tax_exempted_amount, processing_fee_amount, tax_amount, total_amount, final_amount', 'length', 'max'=>19),
            array('currency_code', 'length', 'max'=>3),
            array('txn_sub_status, pg_txn_id', 'length', 'max'=>64),
            array('rules_action', 'length', 'max'=>16),
            array('risk_service_type', 'length', 'max'=>128),
            array('notification_date, pg_date, pg_raw_data, risk_management_data, matched_rules', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('orders_id, status, require_upload, api_key, notification_id, notification_date, pw_id, amount, tax_exempted_amount, processing_fee_amount, tax_amount, total_amount, final_amount, currency_code, transaction_status, txn_sub_status, type, subscription_token, charge_index, payment_method_code, reversible_payment, settlement_account, require_capture, mobile_number, mobile_number_verification, pg_status, pg_reason, pg_date, pg_txn_id, pg_raw_data, risk_management_data, matched_rules, pipwave_score, rules_action, extra_param1, extra_param2, extra_param3, risk_service_type', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'orders_id' => 'Orders',
            'status' => 'Status',
            'require_upload' => 'Require Upload',
            'api_key' => 'Api Key',
            'notification_id' => 'Notification',
            'notification_date' => 'Notification Date',
            'pw_id' => 'Pw',
            'amount' => 'Amount',
            'tax_exempted_amount' => 'Tax Exempted Amount',
            'processing_fee_amount' => 'Processing Fee Amount',
            'tax_amount' => 'Tax Amount',
            'total_amount' => 'Total Amount',
            'final_amount' => 'Final Amount',
            'currency_code' => 'Currency Code',
            'transaction_status' => 'Transaction Status',
            'txn_sub_status' => 'Txn Sub Status',
            'type' => 'Type',
            'subscription_token' => 'Subscription Token',
            'charge_index' => 'Charge Index',
            'payment_method_code' => 'Payment Method Code',
            'reversible_payment' => 'Reversible Payment',
            'settlement_account' => 'Settlement Account',
            'require_capture' => 'Require Capture',
            'mobile_number' => 'Mobile Number',
            'mobile_number_verification' => 'Mobile Number Verification',
            'pg_status' => 'Pg Status',
            'pg_reason' => 'Pg Reason',
            'pg_date' => 'Pg Date',
            'pg_txn_id' => 'Pg Txn',
            'pg_raw_data' => 'Pg Raw Data',
            'risk_management_data' => 'Risk Management Data',
            'matched_rules' => 'Matched Rules',
            'pipwave_score' => 'Pipwave Score',
            'rules_action' => 'Rules Action',
            'extra_param1' => 'Extra Param1',
            'extra_param2' => 'Extra Param2',
            'extra_param3' => 'Extra Param3',
            'risk_service_type' => 'Risk Service Type',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('orders_id',$this->orders_id,true);
        $criteria->compare('status',$this->status,true);
        $criteria->compare('require_upload',$this->require_upload);
        $criteria->compare('api_key',$this->api_key,true);
        $criteria->compare('notification_id',$this->notification_id,true);
        $criteria->compare('notification_date',$this->notification_date,true);
        $criteria->compare('pw_id',$this->pw_id,true);
        $criteria->compare('amount',$this->amount,true);
        $criteria->compare('tax_exempted_amount',$this->tax_exempted_amount,true);
        $criteria->compare('processing_fee_amount',$this->processing_fee_amount,true);
        $criteria->compare('tax_amount',$this->tax_amount,true);
        $criteria->compare('total_amount',$this->total_amount,true);
        $criteria->compare('final_amount',$this->final_amount,true);
        $criteria->compare('currency_code',$this->currency_code,true);
        $criteria->compare('transaction_status',$this->transaction_status,true);
        $criteria->compare('txn_sub_status',$this->txn_sub_status,true);
        $criteria->compare('type',$this->type,true);
        $criteria->compare('subscription_token',$this->subscription_token,true);
        $criteria->compare('charge_index',$this->charge_index);
        $criteria->compare('payment_method_code',$this->payment_method_code,true);
        $criteria->compare('reversible_payment',$this->reversible_payment);
        $criteria->compare('settlement_account',$this->settlement_account,true);
        $criteria->compare('require_capture',$this->require_capture);
        $criteria->compare('mobile_number',$this->mobile_number,true);
        $criteria->compare('mobile_number_verification',$this->mobile_number_verification);
        $criteria->compare('pg_status',$this->pg_status,true);
        $criteria->compare('pg_reason',$this->pg_reason,true);
        $criteria->compare('pg_date',$this->pg_date,true);
        $criteria->compare('pg_txn_id',$this->pg_txn_id,true);
        $criteria->compare('pg_raw_data',$this->pg_raw_data,true);
        $criteria->compare('risk_management_data',$this->risk_management_data,true);
        $criteria->compare('matched_rules',$this->matched_rules,true);
        $criteria->compare('pipwave_score',$this->pipwave_score);
        $criteria->compare('rules_action',$this->rules_action,true);
        $criteria->compare('extra_param1',$this->extra_param1,true);
        $criteria->compare('extra_param2',$this->extra_param2,true);
        $criteria->compare('extra_param3',$this->extra_param3,true);
        $criteria->compare('risk_service_type',$this->risk_service_type,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }
}