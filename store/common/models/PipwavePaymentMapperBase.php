<?php

/**
 * This is the model class for table "pipwave_payment_mapper".
 *
 * The followings are the available columns in table 'pipwave_payment_mapper':
 * @property string $id
 * @property string $pm_id
 * @property string $pipwave_payment_code
 * @property string $pm_display_name
 * @property string $pg_display_name
 * @property string $pg_id
 * @property string $pg_code
 * @property integer $is_rp
 * @property integer $site_id
 */
class PipwavePaymentMapperBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return PipwavePaymentMapperBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'pipwave_payment_mapper';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return [
            ['pm_id, pipwave_payment_code, pm_display_name, pg_display_name, pg_id, pg_code, is_rp', 'required'],
            ['is_rp, site_id', 'numerical', 'integerOnly' => true],
            ['pm_id, pg_id', 'length', 'max' => 11],
            ['pipwave_payment_code, pm_display_name, pg_display_name, pg_code', 'length', 'max' => 255],
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            ['id, pm_id, pipwave_payment_code, pm_display_name, pg_display_name, pg_id, pg_code, is_rp', 'safe', 'on' => 'search'],
        ];
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return [
        ];
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'pm_id' => 'Pm',
            'pipwave_payment_code' => 'Pipwave Payment Code',
            'pm_display_name' => 'Pm Display Name',
            'pg_display_name' => 'Pg Display Name',
            'pg_id' => 'Pg',
            'pg_code' => 'Pg Code',
            'is_rp' => 'Is Rp',
            'site_id' => 'site_id',
        ];
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('pm_id', $this->pm_id, true);
        $criteria->compare('pipwave_payment_code', $this->pipwave_payment_code, true);
        $criteria->compare('pm_display_name', $this->pm_display_name, true);
        $criteria->compare('pg_display_name', $this->pg_display_name, true);
        $criteria->compare('pg_id', $this->pg_id, true);
        $criteria->compare('pg_code', $this->pg_code, true);
        $criteria->compare('is_rp', $this->is_rp);

        return new CActiveDataProvider($this, [
            'criteria' => $criteria,
        ]);
    }

    public static function setPaymentRestriction(...$restriction_list)
    {
        $restriction_mode = 'No Restrictions';
        $restriction_info = [];
        foreach ($restriction_list as $restriction) {
            if (is_array($restriction) && !empty($restriction['restriction_mode']) && isset($restriction['restriction_info'])) {
                $restriction['restriction_info'] = (is_array($restriction['restriction_info']) ? $restriction['restriction_info'] : explode(',', $restriction['restriction_info']));
                switch ($restriction['restriction_mode']) {
                    case 'Allow':
                        switch ($restriction_mode) {
                            case 'No Restrictions':
                                $restriction_mode = 'Allow';
                                $restriction_info = $restriction['restriction_info'];
                                break;

                            case 'Allow':
                                $restriction_info = array_intersect($restriction_info, $restriction['restriction_info']);
                                break;

                            case 'Disallow':
                                $restriction_mode = 'Allow';
                                foreach ($restriction['restriction_info'] as $_pmid) {
                                    foreach ($restriction_info as $id => $val) {
                                        if ($val == $_pmid) {
                                            unset($restriction_info[$id]);
                                        }
                                    }
                                }
                                $restriction_info = $restriction['restriction_info'];
                                break;
                        }
                        break;

                    case 'Disallow':
                        switch ($restriction_mode) {
                            case 'No Restrictions':
                                $restriction_mode = 'Disallow';
                                $restriction_info = $restriction['restriction_info'];
                                break;

                            case 'Allow':
                                $restriction_mode = 'Allow';
                                foreach ($restriction['restriction_info'] as $_pmid) {
                                    foreach ($restriction_info as $id => $val) {
                                        if ($val == $_pmid) {
                                            unset($restriction_info[$id]);
                                        }
                                    }
                                }
                                break;

                            case 'Disallow':
                                $restriction_info = array_unique(array_merge($restriction_info, $restriction['restriction_info']));
                                break;
                        }
                }
            }
        }

        return [$restriction_mode, $restriction_info];
    }
}