<?php

/**
 * This is the model class for table "po_company".
 *
 * The followings are the available columns in table 'po_company':
 * @property string $po_company_id
 * @property string $po_company_code
 * @property string $po_company_name
 * @property string $po_company_contact_name
 * @property string $po_company_street_address
 * @property string $po_company_suburb
 * @property string $po_company_city
 * @property string $po_company_postcode
 * @property string $po_company_state
 * @property integer $po_company_country_id
 * @property integer $po_company_zone_id
 * @property integer $po_company_format_id
 * @property string $po_company_telephone
 * @property string $po_company_fax
 * @property string $po_company_invoice_footer
 * @property string $po_company_gst_percentage
 */
class PoCompanyBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PoCompanyBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'po_company';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('po_company_country_id, po_company_zone_id, po_company_format_id', 'numerical', 'integerOnly'=>true),
			array('po_company_code', 'length', 'max'=>2),
			array('po_company_name, po_company_suburb, po_company_city, po_company_state, po_company_telephone, po_company_fax', 'length', 'max'=>32),
			array('po_company_contact_name, po_company_street_address', 'length', 'max'=>64),
			array('po_company_postcode', 'length', 'max'=>10),
			array('po_company_gst_percentage', 'length', 'max'=>6),
			array('po_company_invoice_footer', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('po_company_id, po_company_code, po_company_name, po_company_contact_name, po_company_street_address, po_company_suburb, po_company_city, po_company_postcode, po_company_state, po_company_country_id, po_company_zone_id, po_company_format_id, po_company_telephone, po_company_fax, po_company_invoice_footer, po_company_gst_percentage', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'po_company_id' => 'Po Company',
			'po_company_code' => 'Po Company Code',
			'po_company_name' => 'Po Company Name',
			'po_company_contact_name' => 'Po Company Contact Name',
			'po_company_street_address' => 'Po Company Street Address',
			'po_company_suburb' => 'Po Company Suburb',
			'po_company_city' => 'Po Company City',
			'po_company_postcode' => 'Po Company Postcode',
			'po_company_state' => 'Po Company State',
			'po_company_country_id' => 'Po Company Country',
			'po_company_zone_id' => 'Po Company Zone',
			'po_company_format_id' => 'Po Company Format',
			'po_company_telephone' => 'Po Company Telephone',
			'po_company_fax' => 'Po Company Fax',
			'po_company_invoice_footer' => 'Po Company Invoice Footer',
			'po_company_gst_percentage' => 'Po Company Gst Percentage',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('po_company_id',$this->po_company_id,true);
		$criteria->compare('po_company_code',$this->po_company_code,true);
		$criteria->compare('po_company_name',$this->po_company_name,true);
		$criteria->compare('po_company_contact_name',$this->po_company_contact_name,true);
		$criteria->compare('po_company_street_address',$this->po_company_street_address,true);
		$criteria->compare('po_company_suburb',$this->po_company_suburb,true);
		$criteria->compare('po_company_city',$this->po_company_city,true);
		$criteria->compare('po_company_postcode',$this->po_company_postcode,true);
		$criteria->compare('po_company_state',$this->po_company_state,true);
		$criteria->compare('po_company_country_id',$this->po_company_country_id);
		$criteria->compare('po_company_zone_id',$this->po_company_zone_id);
		$criteria->compare('po_company_format_id',$this->po_company_format_id);
		$criteria->compare('po_company_telephone',$this->po_company_telephone,true);
		$criteria->compare('po_company_fax',$this->po_company_fax,true);
		$criteria->compare('po_company_invoice_footer',$this->po_company_invoice_footer,true);
		$criteria->compare('po_company_gst_percentage',$this->po_company_gst_percentage,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}