<?php

/**
 * This is the model class for table "price_tags".
 *
 * The followings are the available columns in table 'price_tags':
 * @property integer $price_tags_id
 * @property integer $price_groups_id
 * @property string $price_tags_name
 * @property string $price_tags_description
 * @property string $tags_price
 * @property string $price_tags_field
 * @property string $price_tags_update_field
 * @property integer $tags_update_language_id
 * @property integer $price_tags_order
 */
class PriceTagsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PriceTagsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'price_tags';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('price_groups_id, tags_update_language_id, price_tags_order', 'numerical', 'integerOnly'=>true),
			array('price_tags_name', 'length', 'max'=>64),
			array('tags_price, price_tags_field, price_tags_update_field', 'length', 'max'=>255),
			array('price_tags_description', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('price_tags_id, price_groups_id, price_tags_name, price_tags_description, tags_price, price_tags_field, price_tags_update_field, tags_update_language_id, price_tags_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'price_tags_id' => 'Price Tags',
			'price_groups_id' => 'Price Groups',
			'price_tags_name' => 'Price Tags Name',
			'price_tags_description' => 'Price Tags Description',
			'tags_price' => 'Tags Price',
			'price_tags_field' => 'Price Tags Field',
			'price_tags_update_field' => 'Price Tags Update Field',
			'tags_update_language_id' => 'Tags Update Language',
			'price_tags_order' => 'Price Tags Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('price_tags_id',$this->price_tags_id);
		$criteria->compare('price_groups_id',$this->price_groups_id);
		$criteria->compare('price_tags_name',$this->price_tags_name,true);
		$criteria->compare('price_tags_description',$this->price_tags_description,true);
		$criteria->compare('tags_price',$this->tags_price,true);
		$criteria->compare('price_tags_field',$this->price_tags_field,true);
		$criteria->compare('price_tags_update_field',$this->price_tags_update_field,true);
		$criteria->compare('tags_update_language_id',$this->tags_update_language_id);
		$criteria->compare('price_tags_order',$this->price_tags_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}