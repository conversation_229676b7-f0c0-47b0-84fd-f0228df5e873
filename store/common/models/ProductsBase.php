<?php

/**
 * This is the model class for table "products".
 *
 * The followings are the available columns in table 'products':
 * @property integer $products_id
 * @property integer $products_quantity
 * @property integer $products_actual_quantity
 * @property string $products_model
 * @property string $products_price
 * @property string $products_base_currency
 * @property string $products_date_added
 * @property string $products_last_modified
 * @property string $products_date_available
 * @property string $products_weight
 * @property integer $products_status
 * @property integer $products_display
 * @property integer $products_skip_inventory
 * @property integer $products_purchase_mode
 * @property integer $products_tax_class_id
 * @property integer $manufacturers_id
 * @property integer $products_ordered
 * @property string $products_ship_price
 * @property integer $products_quantity_order
 * @property integer $products_pre_order_level
 * @property double $products_eta
 * @property string $products_add_to_cart_msg
 * @property string $products_preorder_msg
 * @property string $products_out_of_stock_msg
 * @property integer $products_out_of_stock_level
 * @property string $products_bundle
 * @property string $products_bundle_dynamic
 * @property integer $products_bundle_dynamic_qty
 * @property integer $products_main_cat_id
 * @property string $products_cat_id_path
 * @property string $products_cat_path
 * @property string $products_url_alias
 * @property integer $products_auto_seo
 * @property string $products_flag_id
 * @property integer $products_sort_order
 * @property integer $custom_products_type_id
 * @property integer $custom_products_type_child_id
 * @property string $products_buyback_quantity
 * @property string $products_buyback_price
 * @property string $products_quantity_fifo_cost
 * @property string $products_actual_quantity_fifo_cost
 * @property string $products_payment_mature_period
 */
class ProductsBase extends MainModel
{
    public $date_available_string;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return ProductsBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'products';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('products_bundle, products_bundle_dynamic', 'required'),
            array(
                'products_quantity, products_actual_quantity, products_status, products_display, products_skip_inventory, products_purchase_mode, products_tax_class_id, manufacturers_id, products_ordered, products_quantity_order, products_pre_order_level, products_out_of_stock_level, products_bundle_dynamic_qty, products_main_cat_id, products_auto_seo, products_sort_order, custom_products_type_id, custom_products_type_child_id',
                'numerical',
                'integerOnly' => true,
            ),
            array('products_eta', 'numerical'),
            array('products_model', 'length', 'max' => 25),
            array(
                'products_price, products_ship_price, products_buyback_price, products_quantity_fifo_cost, products_actual_quantity_fifo_cost',
                'length',
                'max' => 15,
            ),
            array('products_base_currency', 'length', 'max' => 3),
            array('products_weight', 'length', 'max' => 10),
            array('products_add_to_cart_msg, products_preorder_msg, products_out_of_stock_msg', 'length', 'max' => 128),
            array(
                'products_cat_id_path, products_cat_path, products_url_alias, products_flag_id, products_buyback_quantity',
                'length',
                'max' => 255,
            ),
            array('products_payment_mature_period', 'length', 'max' => 11),
            array('products_date_added, products_last_modified, products_date_available', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'products_id, products_quantity, products_actual_quantity, products_model, products_price, products_base_currency, products_date_added, products_last_modified, products_date_available, products_weight, products_status, products_display, products_skip_inventory, products_purchase_mode, products_tax_class_id, manufacturers_id, products_ordered, products_ship_price, products_quantity_order, products_pre_order_level, products_eta, products_add_to_cart_msg, products_preorder_msg, products_out_of_stock_msg, products_out_of_stock_level, products_bundle, products_bundle_dynamic, products_bundle_dynamic_qty, products_main_cat_id, products_cat_id_path, products_cat_path, products_url_alias, products_auto_seo, products_flag_id, products_sort_order, custom_products_type_id, custom_products_type_child_id, products_buyback_quantity, products_buyback_price, products_quantity_fifo_cost, products_actual_quantity_fifo_cost, products_payment_mature_period',
                'safe',
                'on' => 'search',
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'CategoriesWithCategoriesGroup' => array(
                self::MANY_MANY,
                'CategoriesBase',
                'products_to_categories(products_id, categories_id)',
                'with' => 'CategoriesGroupsBase',
            ),
            'CategoriesBase' => array(
                self::MANY_MANY,
                'CategoriesBase',
                'products_to_categories(products_id, categories_id)',
            ),
            'ProductsBundlesBase' => array(self::HAS_MANY, 'ProductsBundlesBase', 'subproduct_id'),
            'ProductsDescriptionBase' => array(self::HAS_MANY, 'ProductsDescriptionBase', 'products_id'),
            'ProductsDescriptionBase' => array(self::HAS_MANY, 'ProductsDescriptionBase', 'products_id'),
            'ProductsToCategoriesBase' => array(self::HAS_MANY, 'ProductsToCategoriesBase', 'products_id'),
            'ProductsToGameBlogBase' => array(self::HAS_MANY, 'ProductsToGameBlogBase', 'products_id'),
            'ProductsFollowPriceBase' => array(self::HAS_ONE, 'ProductsFollowPriceBase', 'products_id'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'products_id' => 'Products',
            'products_quantity' => 'Products Quantity',
            'products_actual_quantity' => 'Products Actual Quantity',
            'products_model' => 'Products Model',
            'products_price' => 'Products Price',
            'products_base_currency' => 'Products Base Currency',
            'products_date_added' => 'Products Date Added',
            'products_last_modified' => 'Products Last Modified',
            'products_date_available' => 'Products Date Available',
            'products_weight' => 'Products Weight',
            'products_status' => 'Products Status',
            'products_display' => 'Products Display',
            'products_skip_inventory' => 'Products Skip Inventory',
            'products_purchase_mode' => 'Products Purchase Mode',
            'products_tax_class_id' => 'Products Tax Class',
            'manufacturers_id' => 'Manufacturers',
            'products_ordered' => 'Products Ordered',
            'products_ship_price' => 'Products Ship Price',
            'products_quantity_order' => 'Products Quantity Order',
            'products_pre_order_level' => 'Products Pre Order Level',
            'products_eta' => 'Products Eta',
            'products_add_to_cart_msg' => 'Products Add To Cart Msg',
            'products_preorder_msg' => 'Products Preorder Msg',
            'products_out_of_stock_msg' => 'Products Out Of Stock Msg',
            'products_out_of_stock_level' => 'Products Out Of Stock Level',
            'products_bundle' => 'Products Bundle',
            'products_bundle_dynamic' => 'Products Bundle Dynamic',
            'products_bundle_dynamic_qty' => 'Products Bundle Dynamic Qty',
            'products_main_cat_id' => 'Products Main Cat',
            'products_cat_id_path' => 'Products Cat Id Path',
            'products_cat_path' => 'Products Cat Path',
            'products_url_alias' => 'Products Url Alias',
            'products_auto_seo' => 'Products Auto Seo',
            'products_flag_id' => 'Products Flag',
            'products_sort_order' => 'Products Sort Order',
            'custom_products_type_id' => 'Custom Products Type',
            'custom_products_type_child_id' => 'Custom Products Type Child',
            'products_buyback_quantity' => 'Products Buyback Quantity',
            'products_buyback_price' => 'Products Buyback Price',
            'products_quantity_fifo_cost' => 'Products Quantity Fifo Cost',
            'products_actual_quantity_fifo_cost' => 'Products Actual Quantity Fifo Cost',
            'products_payment_mature_period' => 'Products Payment Mature Period',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('products_quantity', $this->products_quantity);
        $criteria->compare('products_actual_quantity', $this->products_actual_quantity);
        $criteria->compare('products_model', $this->products_model, true);
        $criteria->compare('products_price', $this->products_price, true);
        $criteria->compare('products_base_currency', $this->products_base_currency, true);
        $criteria->compare('products_date_added', $this->products_date_added, true);
        $criteria->compare('products_last_modified', $this->products_last_modified, true);
        $criteria->compare('products_date_available', $this->products_date_available, true);
        $criteria->compare('products_weight', $this->products_weight, true);
        $criteria->compare('products_status', $this->products_status);
        $criteria->compare('products_display', $this->products_display);
        $criteria->compare('products_skip_inventory', $this->products_skip_inventory);
        $criteria->compare('products_purchase_mode', $this->products_purchase_mode);
        $criteria->compare('products_tax_class_id', $this->products_tax_class_id);
        $criteria->compare('manufacturers_id', $this->manufacturers_id);
        $criteria->compare('products_ordered', $this->products_ordered);
        $criteria->compare('products_ship_price', $this->products_ship_price, true);
        $criteria->compare('products_quantity_order', $this->products_quantity_order);
        $criteria->compare('products_pre_order_level', $this->products_pre_order_level);
        $criteria->compare('products_eta', $this->products_eta);
        $criteria->compare('products_add_to_cart_msg', $this->products_add_to_cart_msg, true);
        $criteria->compare('products_preorder_msg', $this->products_preorder_msg, true);
        $criteria->compare('products_out_of_stock_msg', $this->products_out_of_stock_msg, true);
        $criteria->compare('products_out_of_stock_level', $this->products_out_of_stock_level);
        $criteria->compare('products_bundle', $this->products_bundle, true);
        $criteria->compare('products_bundle_dynamic', $this->products_bundle_dynamic, true);
        $criteria->compare('products_bundle_dynamic_qty', $this->products_bundle_dynamic_qty);
        $criteria->compare('products_main_cat_id', $this->products_main_cat_id);
        $criteria->compare('products_cat_id_path', $this->products_cat_id_path, true);
        $criteria->compare('products_cat_path', $this->products_cat_path, true);
        $criteria->compare('products_url_alias', $this->products_url_alias, true);
        $criteria->compare('products_auto_seo', $this->products_auto_seo);
        $criteria->compare('products_flag_id', $this->products_flag_id, true);
        $criteria->compare('products_sort_order', $this->products_sort_order);
        $criteria->compare('custom_products_type_id', $this->custom_products_type_id);
        $criteria->compare('custom_products_type_child_id', $this->custom_products_type_child_id);
        $criteria->compare('products_buyback_quantity', $this->products_buyback_quantity, true);
        $criteria->compare('products_buyback_price', $this->products_buyback_price, true);
        $criteria->compare('products_quantity_fifo_cost', $this->products_quantity_fifo_cost, true);
        $criteria->compare('products_actual_quantity_fifo_cost', $this->products_actual_quantity_fifo_cost, true);
        $criteria->compare('products_payment_mature_period', $this->products_payment_mature_period, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function deactivateProductStatus($product_id)
    {
        $params = array('products_status' => 0);
        return $this->model()->updateByPk($product_id, $params);
    }

    # not in used
    public function isProductExistByStatus($products_id, $products_status = 1)
    {
        $return_int = false;

        $criteria = new CDbCriteria;
        $criteria->with = array(
            // exclude follow products
            'ProductsFollowPriceBase' => array('select' => false, 'together' => true),
        );
        $criteria->select = 'products_display';
        $criteria->condition = 't.products_id = :products_id 
                AND products_status = :products_status 
                AND ProductsFollowPriceBase.products_id is NULL';
        $criteria->params = array(
            ':products_id' => $products_id,
            ':products_status' => $products_status,
        );

        if ($result = $this->model()->find($criteria)) {
            $return_int = (int)$result->products_display;
        }

        return $return_int;
    }

    public function getActiveProductByID($products_id, $groups_id, $guest_group_id, $show_virtual_products = false)
    {
        $return_array = array();

        $sql = " SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, p.products_sort_order,
                    c.categories_id, c.categories_parent_path, c.sort_order
                FROM " . $this->tableName() . " AS p
                INNER JOIN " . ProductsToCategoriesBase::model()->tableName() . " AS ptc
                    ON (p.products_id = ptc.products_id)
                INNER JOIN " . CategoriesBase::model()->tableName() . " AS c
                    ON (ptc.categories_id = c.categories_id)
                INNER JOIN " . CategoriesGroupsBase::model()->tableName() . " AS cg
                    ON (c.categories_id = cg.categories_id)
                LEFT JOIN " . ProductsFollowPriceBase::model()->tableName() . " AS pfp
                    ON (p.products_id = pfp.products_id)
                WHERE ptc.products_id = :products_id
                    AND pfp.products_id is NULL
                    AND p.products_status = 1
                    AND p.custom_products_type_id <> 3
                    AND (cg.groups_id = :groups_id OR cg.groups_id = :guest_group_id)
                    AND c.categories_status = 1";

        if (!$show_virtual_products) {
            $sql .= ' AND (p.products_type != 3 OR p.products_type IS NULL)';
        }

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        $command->bindParam(":groups_id", $groups_id, PDO::PARAM_INT);
        $command->bindParam(":guest_group_id", $guest_group_id, PDO::PARAM_INT);
        if ($dataset = $command->queryRow()) {
            $return_array = array(
                'products_id' => $dataset['products_id'],
                'products_bundle' => $dataset['products_bundle'],
                'products_bundle_dynamic' => $dataset['products_bundle_dynamic'],
                'custom_products_type_id' => $dataset['custom_products_type_id'],
                'products_sort_order' => $dataset['products_sort_order'],
                'category' => array(
                    'categories_id' => $dataset['categories_id'],
                    'categories_parent_path' => $dataset['categories_parent_path'],
                    'sort_order' => $dataset['sort_order'],
                ),
            );
        }

        return $return_array;
    }

    /*
     * not in used yet
     */
    public function getActiveProductsByCategoryIDs($category_id_array, $custom_products_type_id, $groups_id, $guest_group_id, $return_item_count = false, $limit = '', $offset = 0)
    {
        $return_array = array();

        $criteria = new CDbCriteria;
        $criteria->with = array(
            'CategoriesBase' => array(
                'select' => 'categories_id, categories_parent_path, sort_order',
                'together' => true,
            ),
        );
        $criteria->select = 't.products_id,t.products_bundle,t.products_bundle_dynamic,t.custom_products_type_id';
        $criteria->distinct = true;
        $criteria->condition = 'products_status = 1 
            AND products_display = 1';
        $criteria->addInCondition('CategoriesBase.categories_id', $category_id_array);
        $criteria->order = 'CategoriesBase.sort_order, products_sort_order';

        if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $cwcg = $result->CategoriesBase[0];

                $return_array[] = array_merge($cwcg->getAttributes(null), $result->getAttributes(null));
            }
        }

        return $return_array;
    }

    public function getTaxExemptedProductsList()
    {
        $tax_exempted_category = (!empty(Yii::app()->params['TAX_EXEMPTION_CATEGORIES']) ? Yii::app()->params['TAX_EXEMPTION_CATEGORIES'] : []);
        $category_list = [];
        $product_list = [];

        foreach ($tax_exempted_category as $category_id) {
            $category_info_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryInfoByGameID($category_id);

            foreach ($category_info_array as $category_info) {
                if (!empty($category_info['categories_id'])) {
                    $category_list[] = $category_info['categories_id'];
                }
            }
        }

        if ($category_list) {
            foreach ($this->getAllProductByCategories($category_list) as $row) {
                $product_list[] = $row['products_id'];
            }
            $tax_exempted_exclusion = (!empty(Yii::app()->params['TAX_EXEMPTION_PRODUCTS_EXCLUSION']) ? Yii::app()->params['TAX_EXEMPTION_PRODUCTS_EXCLUSION'] : []);

            if ($tax_exempted_exclusion) {
                $product_list = array_diff($product_list, $tax_exempted_exclusion);
            }

        }

        return $product_list;
    }

    public function getAllProductByCategories($category_list)
    {
        $return_array = array();

        $sql = " SELECT DISTINCT(p.products_id)
                FROM " . $this->tableName() . " AS p
                INNER JOIN " . ProductsToCategoriesBase::model()->tableName() . " AS ptc
                    ON (p.products_id = ptc.products_id)
                LEFT JOIN " . ProductsFollowPriceBase::model()->tableName() . " AS pfp                    
                    ON (p.products_id = pfp.products_id)
                WHERE ptc.categories_id IN (" . implode(',', $category_list) . ")
                    AND pfp.products_id is NULL
                    AND p.products_status = 1";

        $command = $this->conn->createCommand($sql);

        if ($dataset = $command->queryAll()) {
            $return_array = $dataset;
        }

        return $return_array;
    }

    public function getAllProductByCategoryID($category_id, $status, $display_status, $group_id, $guest_group_id)
    {
        $return_array = array();

        $sql = " SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, p.products_sort_order
                    FROM " . $this->tableName() . " AS p
                    INNER JOIN " . ProductsToCategoriesBase::model()->tableName() . " AS ptc
                        ON (p.products_id = ptc.products_id)
                    LEFT JOIN " . ProductsFollowPriceBase::model()->tableName() . " AS pfp                    
                        ON (p.products_id = pfp.products_id)
                    LEFT JOIN " . ProductsToGameBlogBase::model()->tableName() . " AS ptgb
                        ON (p.products_id = ptgb.products_id)
                    INNER JOIN " . CategoriesGroupsBase::model()->tableName() . " AS cg
                        ON (ptc.categories_id = cg.categories_id)
                    WHERE ptc.categories_id = :categories_id
                        AND pfp.products_id is NULL
                        AND p.products_status = :products_status
                        AND (cg.groups_id = :groups_id OR cg.groups_id = :guest_group_id)";
        if ($display_status == 0) {
            $sql .= ' AND (p.products_display = 1 OR ptgb.products_id IS NOT NULL)';
        } else {
            $sql .= ' AND products_display = 1';
        }
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_id", $category_id, PDO::PARAM_INT);
        $command->bindParam(":products_status", $status, PDO::PARAM_INT);
        $command->bindParam(":groups_id", $group_id, PDO::PARAM_INT);
        $command->bindParam(":guest_group_id", $guest_group_id, PDO::PARAM_INT);
        if ($dataset = $command->queryAll()) {
            $return_array = $dataset;
        }

        return $return_array;
    }

    public function getAllProductByGameBlogID($game_blog_id, $category_id, $status, $group_id, $guest_group_id)
    {
        $return_array = array();

        $sql = " SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, p.products_sort_order
                    FROM " . $this->tableName() . " AS p
                    INNER JOIN " . ProductsToCategoriesBase::model()->tableName() . " AS ptc
                        ON (p.products_id = ptc.products_id)
                    INNER JOIN " . ProductsToGameBlogBase::model()->tableName() . " AS ptgb
                        ON (p.products_id = ptgb.products_id)
                    LEFT JOIN " . ProductsFollowPriceBase::model()->tableName() . " AS pfp
                        ON (p.products_id = pfp.products_id)
                    INNER JOIN " . CategoriesGroupsBase::model()->tableName() . " AS cg
                        ON (ptc.categories_id = cg.categories_id)
                    WHERE ptc.categories_id = :categories_id
                        AND ptgb.game_blog_id = :game_blog_id
                        AND pfp.products_id is NULL
                        AND products_status = :products_status
                        AND (cg.groups_id = :groups_id OR cg.groups_id = :guest_group_id)";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":categories_id", $category_id, PDO::PARAM_INT);
        $command->bindParam(":game_blog_id", $game_blog_id, PDO::PARAM_INT);
        $command->bindParam(":products_status", $status, PDO::PARAM_INT);
        $command->bindParam(":groups_id", $group_id, PDO::PARAM_INT);
        $command->bindParam(":guest_group_id", $guest_group_id, PDO::PARAM_INT);

        if ($dataset = $command->queryAll()) {
            $return_array = $dataset;
        }

        return $return_array;
    }

    public function getCategoryIDByActiveID($products_id)
    {
        $return_int = 0;

        $criteria = new CDbCriteria;
        $criteria->with = array(
            'ProductsToCategoriesBase' => array('select' => 'categories_id', 'together' => true),
            // exclude follow products
            'ProductsFollowPriceBase' => array('select' => false, 'together' => true),
        );
        $criteria->select = 'ProductsToCategoriesBase.*';
        $criteria->condition = 't.products_id = :products_id
            AND ProductsFollowPriceBase.products_id is NULL';
        $criteria->params = array(
            ':products_id' => $products_id,
        );

        if ($result = $this->model()->find($criteria)) {
            $return_int = $result->ProductsToCategoriesBase[0]->categories_id;
        }

        return $return_int;
    }

    public function getInfoByID($products_id)
    {
        $return_array = array();

        $sql = "SELECT p.*, DATE_FORMAT(products_date_available, '%M %e, %Y') AS date_available_string
                FROM " . $this->tableName() . " AS p
                WHERE p.products_id = :products_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        if ($dataset = $command->queryRow()) {
            $return_array = $dataset;
            if ($return_array['products_type'] == 3) {
                if (!empty($_REQUEST['sub_products_id'])) {
                    $data = Yii::app()->frontPageCom->getProductObj()->getSubProductInfoByID($products_id, $_REQUEST['sub_products_id']);
                    $return_array['products_price'] = $data['sales_price'];
                    $return_array['products_base_currency'] = $data['sales_currency'];
                } else {
                    throw new Exception('MISSING_SUB_PRODUCTS_ID');
                }
            }
        }

        return $return_array;
    }

    public function getProductsBundleInfoByBundleID($bundle_id)
    {
        $return_array = array();

        $sql = "SELECT p.*, pb.*
                FROM " . $this->tableName() . " AS p
                INNER JOIN " . ProductsBundlesBase::model()->tableName() . " AS pb
                    ON (p.products_id = pb.subproduct_id)
                WHERE pb.bundle_id = :bundle_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":bundle_id", $bundle_id, PDO::PARAM_INT);
        if ($dataset = $command->queryAll()) {
            $return_array = $dataset;
        }

        return $return_array;
    }

    public function getProductsIDByProductsUrlAlias($products_url_alias)
    {
        $return_int = 0;

        $sql = "SELECT products_id
                FROM " . $this->tableName() . " AS p
                WHERE products_url_alias = :products_url_alias
                    AND custom_products_type_id IN (0, 2)";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_url_alias", $products_url_alias, PDO::PARAM_STR);
        if ($dataset = $command->queryScalar()) {
            $return_int = $dataset;
        }

        return $return_int;
    }

    public function getProductsUrlAlias($products_id)
    {
        $return_str = '';

        $sql = "SELECT products_url_alias
                FROM " . $this->tableName() . " AS p
                WHERE products_id = :products_id
                    AND custom_products_type_id IN (0, 2)
                    AND products_status = 1";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        if ($dataset = $command->queryScalar()) {
            $return_str = $dataset;
        }

        return $return_str;
    }

    public static function getCPathByProductsId($products_id)
    {
        $cat_id = 0;

        $criteria = new CDbCriteria;
        $criteria->select = 'products_cat_id_path';
        $criteria->condition = 'products_id = :products_id';
        $criteria->params = array(
            ':products_id' => $products_id
        );
        if ($result = self::model()->find($criteria)) {
            $str = trim($result->products_cat_id_path, '_');
            if ($cat_arr = explode('_', $str)) {
                if (isset($cat_arr[1])) {
                    return (int)$cat_arr[1];
                }
            }
        }

        return $cat_id;
    }

}
