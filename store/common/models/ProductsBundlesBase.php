<?php

/**
 * This is the model class for table "products_bundles".
 *
 * The followings are the available columns in table 'products_bundles':
 * @property integer $bundle_id
 * @property integer $subproduct_id
 * @property integer $subproduct_qty
 * @property integer $subproduct_weight
 */
class ProductsBundlesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsBundlesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_bundles';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('bundle_id, subproduct_id, subproduct_qty, subproduct_weight', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('bundle_id, subproduct_id, subproduct_qty, subproduct_weight', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'bundle_id' => 'Bundle',
			'subproduct_id' => 'Subproduct',
			'subproduct_qty' => 'Subproduct Qty',
			'subproduct_weight' => 'Subproduct Weight',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('bundle_id',$this->bundle_id);
		$criteria->compare('subproduct_id',$this->subproduct_id);
		$criteria->compare('subproduct_qty',$this->subproduct_qty);
		$criteria->compare('subproduct_weight',$this->subproduct_weight);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}