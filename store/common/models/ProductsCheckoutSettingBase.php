<?php

/**
 * This is the model class for table "products_checkout_setting".
 *
 * The followings are the available columns in table 'products_checkout_setting':
 * @property integer $products_id
 * @property integer $max_purchase_quantity
 * @property integer $max_purchase_period
 */
class ProductsCheckoutSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsCheckoutSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_checkout_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, max_purchase_quantity, max_purchase_period', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, max_purchase_quantity, max_purchase_period', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'max_purchase_quantity' => 'Max Purchase Quantity',
			'max_purchase_period' => 'Max Purchase Period',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('max_purchase_quantity',$this->max_purchase_quantity);
		$criteria->compare('max_purchase_period',$this->max_purchase_period);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}