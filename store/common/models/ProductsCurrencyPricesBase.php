<?php

/**
 * This is the model class for table "products_currency_prices".
 *
 * The followings are the available columns in table 'products_currency_prices':
 * @property integer $products_id
 * @property string $products_currency_prices_code
 * @property string $products_currency_prices_value
 */
class ProductsCurrencyPricesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsCurrencyPricesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_currency_prices';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id', 'numerical', 'integerOnly'=>true),
			array('products_currency_prices_code', 'length', 'max'=>3),
			array('products_currency_prices_value', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, products_currency_prices_code, products_currency_prices_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'products_currency_prices_code' => 'Products Currency Prices Code',
			'products_currency_prices_value' => 'Products Currency Prices Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('products_currency_prices_code',$this->products_currency_prices_code,true);
		$criteria->compare('products_currency_prices_value',$this->products_currency_prices_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getInfoByID($products_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = '*';
		$criteria->condition = 'products_id = :products_id';
		$criteria->params = array(
            ':products_id' => $products_id
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->getAttributes(NULL);
            }
        }
        
		return $return_array;
    }
    
    public function getInfoWithKeyValueByID($products_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'products_currency_prices_value, products_currency_prices_code';
		$criteria->condition = 'products_id = :products_id';
		$criteria->params = array(
            ':products_id' => $products_id
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[$result->products_currency_prices_code] = $result->products_currency_prices_value;
            }
        }
        
		return $return_array;
    }
}