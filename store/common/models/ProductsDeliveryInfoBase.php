<?php

/**
 * This is the model class for table "products_delivery_info".
 *
 * The followings are the available columns in table 'products_delivery_info':
 * @property integer $products_id
 * @property string $products_delivery_mode_id
 */
class ProductsDeliveryInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsDeliveryInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_delivery_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id', 'numerical', 'integerOnly'=>true),
			array('products_delivery_mode_id', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, products_delivery_mode_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'products_delivery_mode_id' => 'Products Delivery Mode',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('products_delivery_mode_id',$this->products_delivery_mode_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getProductDeliverModeIDByProductID($products_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'products_delivery_mode_id';
		$criteria->condition = 'products_id = :products_id';
		$criteria->params = array(
            ':products_id' => $products_id
        );
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->products_delivery_mode_id;
            }
        }
        
		return $return_array;
    }
}