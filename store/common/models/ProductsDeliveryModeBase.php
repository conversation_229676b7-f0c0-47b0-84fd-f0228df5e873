<?php

/**
 * This is the model class for table "products_delivery_mode".
 *
 * The followings are the available columns in table 'products_delivery_mode':
 * @property string $products_delivery_mode_id
 * @property string $custom_products_type
 * @property string $products_delivery_mode_title
 */
class ProductsDeliveryModeBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsDeliveryModeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_delivery_mode';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('custom_products_type', 'length', 'max'=>255),
			array('products_delivery_mode_title', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_delivery_mode_id, custom_products_type, products_delivery_mode_title', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_delivery_mode_id' => 'Products Delivery Mode',
			'custom_products_type' => 'Custom Products Type',
			'products_delivery_mode_title' => 'Products Delivery Mode Title',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_delivery_mode_id',$this->products_delivery_mode_id,true);
		$criteria->compare('custom_products_type',$this->custom_products_type,true);
		$criteria->compare('products_delivery_mode_title',$this->products_delivery_mode_title,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getTitleByArrayID($dm_array) {
        $return_array = array();

        $criteria = new CDbCriteria;
		$criteria->select = 'products_delivery_mode_id, products_delivery_mode_title';
        $criteria->addInCondition('products_delivery_mode_id', $dm_array);
        
		if ($results = $this->model()->findAll($criteria)) {
            foreach ($results as $result) {
                $return_array[] = $result->getAttributes(NULL);
            }
        }
        
        return $return_array;
    }
}