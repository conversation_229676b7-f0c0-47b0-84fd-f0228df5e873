<?php

/**
 * This is the model class for table "products_description".
 *
 * The followings are the available columns in table 'products_description':
 * @property integer $products_id
 * @property integer $language_id
 * @property string $products_name
 * @property string $products_keyword
 * @property string $products_description
 * @property string $products_image
 * @property string $products_image_title
 * @property string $products_description_image
 * @property string $products_description_image_title
 * @property string $products_url
 * @property integer $products_viewed
 * @property string $products_location
 * @property string $products_dtu_extra_info_1
 * @property string $products_dtu_extra_info_2
 * @property string $products_dtu_extra_info_3
 */
class ProductsDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_dtu_extra_info_1, products_dtu_extra_info_2, products_dtu_extra_info_3', 'required'),
			array('language_id, products_viewed', 'numerical', 'integerOnly'=>true),
			array('products_name, products_url', 'length', 'max'=>255),
			array('products_keyword, products_image, products_image_title, products_description_image, products_description_image_title', 'length', 'max'=>64),
			array('products_dtu_extra_info_1, products_dtu_extra_info_2, products_dtu_extra_info_3', 'length', 'max'=>32),
			array('products_description, products_location', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, language_id, products_name, products_keyword, products_description, products_image, products_image_title, products_description_image, products_description_image_title, products_url, products_viewed, products_location, products_dtu_extra_info_1, products_dtu_extra_info_2, products_dtu_extra_info_3', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'language_id' => 'Language',
			'products_name' => 'Products Name',
			'products_keyword' => 'Products Keyword',
			'products_description' => 'Products Description',
			'products_image' => 'Products Image',
			'products_image_title' => 'Products Image Title',
			'products_description_image' => 'Products Description Image',
			'products_description_image_title' => 'Products Description Image Title',
			'products_url' => 'Products Url',
			'products_viewed' => 'Products Viewed',
			'products_location' => 'Products Location',
			'products_dtu_extra_info_1' => 'Products Dtu Extra Info 1',
			'products_dtu_extra_info_2' => 'Products Dtu Extra Info 2',
			'products_dtu_extra_info_3' => 'Products Dtu Extra Info 3',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('products_name',$this->products_name,true);
		$criteria->compare('products_keyword',$this->products_keyword,true);
		$criteria->compare('products_description',$this->products_description,true);
		$criteria->compare('products_image',$this->products_image,true);
		$criteria->compare('products_image_title',$this->products_image_title,true);
		$criteria->compare('products_description_image',$this->products_description_image,true);
		$criteria->compare('products_description_image_title',$this->products_description_image_title,true);
		$criteria->compare('products_url',$this->products_url,true);
		$criteria->compare('products_viewed',$this->products_viewed);
		$criteria->compare('products_location',$this->products_location,true);
		$criteria->compare('products_dtu_extra_info_1',$this->products_dtu_extra_info_1,true);
		$criteria->compare('products_dtu_extra_info_2',$this->products_dtu_extra_info_2,true);
		$criteria->compare('products_dtu_extra_info_3',$this->products_dtu_extra_info_3,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getProductsName($product_id, $language_id = 0, $default_language_id = 0) {
        if (isset($_REQUEST['sub_products_id'])) {
            $data = Yii::app()->frontPageCom->getProductObj()->getSubProductInfoByID($product_id,$_REQUEST['sub_products_id']);
            return $data['title'];
        }
        return $this->getProductInfo('products_name', $product_id, $language_id, $default_language_id);
    }

    public function getAltProductsName($product_id, $language_id = 0, $default_language_id = 0) {
        if (isset($_REQUEST['sub_products_id'])) {
            $data = Yii::app()->frontPageCom->getProductObj()->getSubProductInfoByID($product_id,$_REQUEST['sub_products_id']);
            return $data['title'];
        }
        return $this->getProductInfo('products_alt_name', $product_id, $language_id, $default_language_id);
    }

    public function getProductsRedemptionGuide($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_redemption_guide', $product_id, $language_id, $default_language_id);
    }

    public function getProductsDescription($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_description', $product_id, $language_id, $default_language_id);
    }
    
    public function getProductsImage($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_image', $product_id, $language_id, $default_language_id);
    }
    
    public function getProductsImageTitle($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_image_title', $product_id, $language_id, $default_language_id);
    }
    
    protected function getProductInfo($field, $product_id, $language_id = 0, $default_language_id = 0) {
        $return_str = '';
        
        $sql = "	SELECT " . $field . " 
                    FROM " . $this->tableName() . " 
                    WHERE products_id = :products_id
                        AND " . $field . " <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(products_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE products_id = :products_id 
                                AND language_id = :language_id
                                AND " . $field . " <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $product_id, PDO::PARAM_STR);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}