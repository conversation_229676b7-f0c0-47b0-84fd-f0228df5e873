<?php

/**
 * This is the model class for table "products_extra_info".
 *
 * The followings are the available columns in table 'products_extra_info':
 * @property string $products_id
 * @property string $products_extra_info_key
 * @property string $products_extra_info_value
 */
class ProductsExtraInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsExtraInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_extra_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id', 'required'),
			array('products_id', 'length', 'max'=>11),
			array('products_extra_info_key, products_extra_info_value', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, products_extra_info_key, products_extra_info_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'products_extra_info_key' => 'Products Extra Info Key',
			'products_extra_info_value' => 'Products Extra Info Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id,true);
		$criteria->compare('products_extra_info_key',$this->products_extra_info_key,true);
		$criteria->compare('products_extra_info_value',$this->products_extra_info_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getProductsZeroPriceSetting($products_id) {
        $return_str = null;

        $sql = "SELECT products_extra_info_value
                FROM " . $this->tableName() . " AS p
                WHERE products_id = :products_id
                    AND products_extra_info_key = 'zero_price_product'";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        $dataset = $command->queryScalar();
        
        if ($dataset !== false) {
            $return_str = $dataset;
        }
            
        return $return_str;
    }
    
    public function getExtraInfo($products_id, $key) {
        $return_str = null;

        $sql = "SELECT products_extra_info_value
                FROM " . $this->tableName() . " AS p
                WHERE products_id = :products_id
                    AND products_extra_info_key = :key";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        $command->bindParam(":key", $key, PDO::PARAM_STR);
        
        $dataset = $command->queryScalar();
        
        if ($dataset !== false) {
            $return_str = $dataset;
        }
        
        return $return_str;
    }
}