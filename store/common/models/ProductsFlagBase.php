<?php

/**
 * This is the model class for table "products_flag".
 *
 * The followings are the available columns in table 'products_flag':
 * @property integer $products_flag_id
 * @property string $products_flag_name
 * @property string $products_flag_image
 * @property string $date_added
 * @property string $last_modified
 * @property integer $languages_id
 */
class ProductsFlagBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsFlagBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_flag';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('languages_id', 'numerical', 'integerOnly'=>true),
			array('products_flag_name', 'length', 'max'=>32),
			array('products_flag_image', 'length', 'max'=>64),
			array('date_added, last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_flag_id, products_flag_name, products_flag_image, date_added, last_modified, languages_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_flag_id' => 'Products Flag',
			'products_flag_name' => 'Products Flag Name',
			'products_flag_image' => 'Products Flag Image',
			'date_added' => 'Date Added',
			'last_modified' => 'Last Modified',
			'languages_id' => 'Languages',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_flag_id',$this->products_flag_id);
		$criteria->compare('products_flag_name',$this->products_flag_name,true);
		$criteria->compare('products_flag_image',$this->products_flag_image,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('languages_id',$this->languages_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}