<?php

/**
 * This is the model class for table "products_groups".
 *
 * The followings are the available columns in table 'products_groups':
 * @property integer $customers_group_id
 * @property string $customers_group_price
 * @property integer $products_id
 * @property string $products_price
 */
class ProductsGroupsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_group_id, products_id', 'numerical', 'integerOnly'=>true),
			array('customers_group_price, products_price', 'length', 'max'=>15),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_group_id, customers_group_price, products_id, products_price', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_group_id' => 'Customers Group',
			'customers_group_price' => 'Customers Group Price',
			'products_id' => 'Products',
			'products_price' => 'Products Price',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_group_id',$this->customers_group_id);
		$criteria->compare('customers_group_price',$this->customers_group_price,true);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('products_price',$this->products_price,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}