<?php

/**
 * This is the model class for table "products_low_stock".
 *
 * The followings are the available columns in table 'products_low_stock':
 * @property integer $products_id
 * @property integer $custom_products_type_id
 * @property string $low_stock_date
 * @property string $low_stock_tag_ids
 * @property integer $low_stock_status
 */
class ProductsLowStockBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsLowStockBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_low_stock';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, custom_products_type_id, low_stock_status', 'numerical', 'integerOnly'=>true),
			array('low_stock_tag_ids', 'length', 'max'=>255),
			array('low_stock_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, custom_products_type_id, low_stock_date, low_stock_tag_ids, low_stock_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'custom_products_type_id' => 'Custom Products Type',
			'low_stock_date' => 'Low Stock Date',
			'low_stock_tag_ids' => 'Low Stock Tag Ids',
			'low_stock_status' => 'Low Stock Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('custom_products_type_id',$this->custom_products_type_id);
		$criteria->compare('low_stock_date',$this->low_stock_date,true);
		$criteria->compare('low_stock_tag_ids',$this->low_stock_tag_ids,true);
		$criteria->compare('low_stock_status',$this->low_stock_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}