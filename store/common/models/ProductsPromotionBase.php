<?php

/**
 * This is the model class for table "products_promotion".
 *
 * The followings are the available columns in table 'products_promotion':
 * @property string $products_id
 * @property string $promotion_start_date
 * @property string $promotion_end_date
 * @property integer $promotion_box_only
 * @property integer $promotion_selling_status
 * @property integer $promotion_limited_stock
 * @property integer $promotion_limited_stock_qty
 */
class ProductsPromotionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPromotionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_promotion';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('promotion_box_only, promotion_selling_status, promotion_limited_stock, promotion_limited_stock_qty', 'numerical', 'integerOnly'=>true),
			array('products_id', 'length', 'max'=>11),
			array('promotion_start_date, promotion_end_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, promotion_start_date, promotion_end_date, promotion_box_only, promotion_selling_status, promotion_limited_stock, promotion_limited_stock_qty', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'promotion_start_date' => 'Promotion Start Date',
			'promotion_end_date' => 'Promotion End Date',
			'promotion_box_only' => 'Promotion Box Only',
			'promotion_selling_status' => 'Promotion Selling Status',
			'promotion_limited_stock' => 'Promotion Limited Stock',
			'promotion_limited_stock_qty' => 'Promotion Limited Stock Qty',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id,true);
		$criteria->compare('promotion_start_date',$this->promotion_start_date,true);
		$criteria->compare('promotion_end_date',$this->promotion_end_date,true);
		$criteria->compare('promotion_box_only',$this->promotion_box_only);
		$criteria->compare('promotion_selling_status',$this->promotion_selling_status);
		$criteria->compare('promotion_limited_stock',$this->promotion_limited_stock);
		$criteria->compare('promotion_limited_stock_qty',$this->promotion_limited_stock_qty);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getActivePromotionProductInfoByID($products_id) {
        $return_array = array();
        
        $criteria = new CDbCriteria;
		$criteria->select = 'products_id, promotion_box_only, promotion_selling_status, promotion_limited_stock, promotion_limited_stock_qty, promotion_end_date';
		$criteria->condition = '(promotion_start_date != "0000-00-00 00:00:00" AND (promotion_end_date = "0000-00-00 00:00:00" AND now() >= promotion_start_date)
									OR (promotion_end_date != "0000-00-00 00:00:00" AND promotion_end_date > now() AND now() >= promotion_start_date))
									AND products_id = :products_id';
		$criteria->params = array(
            ':products_id' => $products_id
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_array = $result->getAttributes(NULL);
        }
        
		return $return_array;
    }
}