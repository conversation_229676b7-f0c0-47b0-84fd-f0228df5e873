<?php

/**
 * This is the model class for table "products_promotion_description".
 *
 * The followings are the available columns in table 'products_promotion_description':
 * @property string $products_id
 * @property integer $language_id
 * @property string $promotion_image
 * @property string $promotion_image_title
 */
class ProductsPromotionDescriptionBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPromotionDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_promotion_description';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('language_id', 'numerical', 'integerOnly'=>true),
			array('products_id', 'length', 'max'=>11),
			array('promotion_image', 'length', 'max'=>32),
			array('promotion_image_title', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, language_id, promotion_image, promotion_image_title', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'language_id' => 'Language',
			'promotion_image' => 'Promotion Image',
			'promotion_image_title' => 'Promotion Image Title',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id,true);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('promotion_image',$this->promotion_image,true);
		$criteria->compare('promotion_image_title',$this->promotion_image_title,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getPromotionImage($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('promotion_image', $product_id, $language_id, $default_language_id);
    }
    
    public function getPromotionImageTitle($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('promotion_image_title', $product_id, $language_id, $default_language_id);
    }
    
    protected function getProductInfo($field, $product_id, $language_id = 0, $default_language_id = 0) {
        $return_str = '';
        
        $sql = "	SELECT " . $field . " 
                    FROM " . $this->tableName() . " 
                    WHERE products_id = :products_id
                        AND " . $field . " <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(products_id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE products_id = :products_id 
                                AND language_id = :language_id
                                AND " . $field . " <> ''), 0, language_id = :default_languages_id)))";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":products_id", $product_id, PDO::PARAM_STR);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}