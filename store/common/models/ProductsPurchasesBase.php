<?php

/**
 * This is the model class for table "products_purchases".
 *
 * The followings are the available columns in table 'products_purchases':
 * @property integer $products_purchases_lists_id
 * @property integer $products_id
 * @property integer $products_purchases_max_quantity
 * @property integer $products_purchases_suggest_quantity
 * @property integer $products_purchases_selling_quantity
 * @property integer $products_purchases_quantity_overwrite
 * @property integer $products_purchases_max_quantity_overwrite
 * @property integer $products_purchases_disabled
 */
class ProductsPurchasesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPurchasesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_purchases';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_purchases_lists_id, products_id, products_purchases_max_quantity, products_purchases_suggest_quantity, products_purchases_selling_quantity, products_purchases_quantity_overwrite, products_purchases_max_quantity_overwrite, products_purchases_disabled', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_purchases_lists_id, products_id, products_purchases_max_quantity, products_purchases_suggest_quantity, products_purchases_selling_quantity, products_purchases_quantity_overwrite, products_purchases_max_quantity_overwrite, products_purchases_disabled', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_purchases_lists_id' => 'Products Purchases Lists',
			'products_id' => 'Products',
			'products_purchases_max_quantity' => 'Products Purchases Max Quantity',
			'products_purchases_suggest_quantity' => 'Products Purchases Suggest Quantity',
			'products_purchases_selling_quantity' => 'Products Purchases Selling Quantity',
			'products_purchases_quantity_overwrite' => 'Products Purchases Quantity Overwrite',
			'products_purchases_max_quantity_overwrite' => 'Products Purchases Max Quantity Overwrite',
			'products_purchases_disabled' => 'Products Purchases Disabled',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_purchases_lists_id',$this->products_purchases_lists_id);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('products_purchases_max_quantity',$this->products_purchases_max_quantity);
		$criteria->compare('products_purchases_suggest_quantity',$this->products_purchases_suggest_quantity);
		$criteria->compare('products_purchases_selling_quantity',$this->products_purchases_selling_quantity);
		$criteria->compare('products_purchases_quantity_overwrite',$this->products_purchases_quantity_overwrite);
		$criteria->compare('products_purchases_max_quantity_overwrite',$this->products_purchases_max_quantity_overwrite);
		$criteria->compare('products_purchases_disabled',$this->products_purchases_disabled);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}