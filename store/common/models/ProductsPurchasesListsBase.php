<?php

/**
 * This is the model class for table "products_purchases_lists".
 *
 * The followings are the available columns in table 'products_purchases_lists':
 * @property integer $products_purchases_lists_id
 * @property string $products_purchases_lists_name
 * @property integer $products_purchases_lists_cat_id
 * @property integer $products_purchases_lists_qty_round_up
 * @property integer $products_purchases_lists_sort_order
 * @property string $products_purchases_lists_reference_date
 * @property string $products_purchases_lists_last_modified
 */
class ProductsPurchasesListsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPurchasesListsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_purchases_lists';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_purchases_lists_cat_id, products_purchases_lists_qty_round_up, products_purchases_lists_sort_order', 'numerical', 'integerOnly'=>true),
			array('products_purchases_lists_name', 'length', 'max'=>255),
			array('products_purchases_lists_reference_date, products_purchases_lists_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_purchases_lists_id, products_purchases_lists_name, products_purchases_lists_cat_id, products_purchases_lists_qty_round_up, products_purchases_lists_sort_order, products_purchases_lists_reference_date, products_purchases_lists_last_modified', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_purchases_lists_id' => 'Products Purchases Lists',
			'products_purchases_lists_name' => 'Products Purchases Lists Name',
			'products_purchases_lists_cat_id' => 'Products Purchases Lists Cat',
			'products_purchases_lists_qty_round_up' => 'Products Purchases Lists Qty Round Up',
			'products_purchases_lists_sort_order' => 'Products Purchases Lists Sort Order',
			'products_purchases_lists_reference_date' => 'Products Purchases Lists Reference Date',
			'products_purchases_lists_last_modified' => 'Products Purchases Lists Last Modified',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_purchases_lists_id',$this->products_purchases_lists_id);
		$criteria->compare('products_purchases_lists_name',$this->products_purchases_lists_name,true);
		$criteria->compare('products_purchases_lists_cat_id',$this->products_purchases_lists_cat_id);
		$criteria->compare('products_purchases_lists_qty_round_up',$this->products_purchases_lists_qty_round_up);
		$criteria->compare('products_purchases_lists_sort_order',$this->products_purchases_lists_sort_order);
		$criteria->compare('products_purchases_lists_reference_date',$this->products_purchases_lists_reference_date,true);
		$criteria->compare('products_purchases_lists_last_modified',$this->products_purchases_lists_last_modified,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}