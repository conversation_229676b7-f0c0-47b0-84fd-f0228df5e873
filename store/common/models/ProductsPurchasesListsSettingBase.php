<?php

/**
 * This is the model class for table "products_purchases_lists_setting".
 *
 * The followings are the available columns in table 'products_purchases_lists_setting':
 * @property integer $products_purchases_lists_id
 * @property string $products_purchases_lists_setting_key
 * @property string $products_purchases_lists_setting_value
 */
class ProductsPurchasesListsSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsPurchasesListsSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_purchases_lists_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_purchases_lists_id', 'numerical', 'integerOnly'=>true),
			array('products_purchases_lists_setting_key, products_purchases_lists_setting_value', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_purchases_lists_id, products_purchases_lists_setting_key, products_purchases_lists_setting_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_purchases_lists_id' => 'Products Purchases Lists',
			'products_purchases_lists_setting_key' => 'Products Purchases Lists Setting Key',
			'products_purchases_lists_setting_value' => 'Products Purchases Lists Setting Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_purchases_lists_id',$this->products_purchases_lists_id);
		$criteria->compare('products_purchases_lists_setting_key',$this->products_purchases_lists_setting_key,true);
		$criteria->compare('products_purchases_lists_setting_value',$this->products_purchases_lists_setting_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}