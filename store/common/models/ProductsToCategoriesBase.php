<?php

/**
 * This is the model class for table "products_to_categories".
 *
 * The followings are the available columns in table 'products_to_categories':
 * @property integer $products_id
 * @property integer $categories_id
 * @property integer $products_is_link
 */
class ProductsToCategoriesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsToCategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'products_to_categories';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, categories_id, products_is_link', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('products_id, categories_id, products_is_link', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'CategoriesBase' => array(self::HAS_ONE, 'CategoriesBase', 'categories_id'),
            'ProductsBase' => array(self::HAS_ONE, 'ProductsBase', 'products_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'products_id' => 'Products',
			'categories_id' => 'Categories',
			'products_is_link' => 'Products Is Link',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('categories_id',$this->categories_id);
		$criteria->compare('products_is_link',$this->products_is_link);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getCategoryID($products_id) {
        $return_int = 0;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'categories_id';
		$criteria->condition = 'products_id = :products_id
             AND products_is_link = 0';
		$criteria->params = array(
            ':products_id' => $products_id
        );
		if ($result = $this->model()->find($criteria)) {
            $return_int = $result->categories_id;
        }
        
		return $return_int;
    }
}