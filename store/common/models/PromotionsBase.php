<?php

/**
 * This is the model class for table "promotions".
 *
 * The followings are the available columns in table 'promotions':
 * @property integer $promotions_id
 * @property string $promotions_title
 * @property string $promotions_description
 * @property string $promotions_from
 * @property string $promotions_to
 * @property string $promotions_min_value
 * @property integer $promotions_status
 */
class PromotionsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PromotionsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'promotions';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('promotions_status', 'numerical', 'integerOnly'=>true),
			array('promotions_title', 'length', 'max'=>64),
			array('promotions_min_value', 'length', 'max'=>7),
			array('promotions_description, promotions_from, promotions_to', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value, promotions_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'promotions_id' => 'Promotions',
			'promotions_title' => 'Promotions Title',
			'promotions_description' => 'Promotions Description',
			'promotions_from' => 'Promotions From',
			'promotions_to' => 'Promotions To',
			'promotions_min_value' => 'Promotions Min Value',
			'promotions_status' => 'Promotions Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('promotions_id',$this->promotions_id);
		$criteria->compare('promotions_title',$this->promotions_title,true);
		$criteria->compare('promotions_description',$this->promotions_description,true);
		$criteria->compare('promotions_from',$this->promotions_from,true);
		$criteria->compare('promotions_to',$this->promotions_to,true);
		$criteria->compare('promotions_min_value',$this->promotions_min_value,true);
		$criteria->compare('promotions_status',$this->promotions_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}