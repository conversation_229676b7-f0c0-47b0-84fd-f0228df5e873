<?php

/**
 * This is the model class for table "publishers".
 *
 * The followings are the available columns in table 'publishers':
 * @property string $publishers_id
 * @property string $publishers_name
 * @property integer $publishers_status
 * @property string $publishers_remark
 * @property string $last_modified
 * @property string $date_added
 * @property integer $last_modified_by
 * @property integer $sort_order
 */
class PublishersBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PublishersBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'publishers';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('publishers_remark', 'required'),
			array('publishers_status, last_modified_by, sort_order', 'numerical', 'integerOnly'=>true),
			array('publishers_name', 'length', 'max'=>32),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('publishers_id, publishers_name, publishers_status, publishers_remark, last_modified, date_added, last_modified_by, sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'publishers_id' => 'Publishers',
			'publishers_name' => 'Publishers Name',
			'publishers_status' => 'Publishers Status',
			'publishers_remark' => 'Publishers Remark',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'last_modified_by' => 'Last Modified By',
			'sort_order' => 'Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('publishers_id',$this->publishers_id,true);
		$criteria->compare('publishers_name',$this->publishers_name,true);
		$criteria->compare('publishers_status',$this->publishers_status);
		$criteria->compare('publishers_remark',$this->publishers_remark,true);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified_by',$this->last_modified_by);
		$criteria->compare('sort_order',$this->sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}