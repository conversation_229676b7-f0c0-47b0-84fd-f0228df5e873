<?php

/**
 * This is the model class for table "publishers_games".
 *
 * The followings are the available columns in table 'publishers_games':
 * @property string $publishers_games_id
 * @property string $publishers_id
 * @property string $publishers_game
 * @property integer $categories_id
 * @property integer $publishers_games_status
 * @property double $publishers_games_daily_limit
 * @property double $publishers_games_today_topped_amount
 * @property string $publishers_games_pending_message
 * @property string $publishers_games_reloaded_message
 * @property string $publishers_games_failed_message
 * @property string $publishers_games_remark
 * @property string $publishers_server
 * @property double $publishers_games_daily_topped_amount
 */
class PublishersGamesBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return PublishersGamesBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'publishers_games';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('publishers_server', 'required'),
            array('categories_id, publishers_games_status', 'numerical', 'integerOnly' => true), array(
                'publishers_games_daily_limit, publishers_games_today_topped_amount, publishers_games_daily_topped_amount',
                'numerical'), array('publishers_id', 'length', 'max' => 10),
            array('publishers_game', 'length', 'max' => 64), array(
                'publishers_games_pending_message, publishers_games_reloaded_message, publishers_games_failed_message, publishers_games_remark',
                'length', 'max' => 255), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'publishers_games_id, publishers_id, publishers_game, categories_id, publishers_games_status, publishers_games_daily_limit, publishers_games_today_topped_amount, publishers_games_pending_message, publishers_games_reloaded_message, publishers_games_failed_message, publishers_games_remark, publishers_server, publishers_games_daily_topped_amount',
                'safe', 'on' => 'search'),);
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        return array(
            'publishers_products' => array(self::HAS_MANY, 'PublishersProducts', '', 'on' => 'publishers_products.publishers_games_id = t.publishers_games_id', 'joinType' => 'INNER JOIN', 'alias' => 'publishers_products'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'publishers_games_id' => 'Publishers Games', 'publishers_id' => 'Publishers',
            'publishers_game' => 'Publishers Game', 'categories_id' => 'Categories',
            'publishers_games_status' => 'Publishers Games Status',
            'publishers_games_daily_limit' => 'Publishers Games Daily Limit',
            'publishers_games_today_topped_amount' => 'Publishers Games Today Topped Amount',
            'publishers_games_pending_message' => 'Publishers Games Pending Message',
            'publishers_games_reloaded_message' => 'Publishers Games Reloaded Message',
            'publishers_games_failed_message' => 'Publishers Games Failed Message',
            'publishers_games_remark' => 'Publishers Games Remark', 'publishers_server' => 'Publishers Server',
            'publishers_games_daily_topped_amount' => 'Publishers Games Daily Topped Amount',);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('publishers_games_id', $this->publishers_games_id, true);
        $criteria->compare('publishers_id', $this->publishers_id, true);
        $criteria->compare('publishers_game', $this->publishers_game, true);
        $criteria->compare('categories_id', $this->categories_id);
        $criteria->compare('publishers_games_status', $this->publishers_games_status);
        $criteria->compare('publishers_games_daily_limit', $this->publishers_games_daily_limit);
        $criteria->compare('publishers_games_today_topped_amount', $this->publishers_games_today_topped_amount);
        $criteria->compare('publishers_games_pending_message', $this->publishers_games_pending_message, true);
        $criteria->compare('publishers_games_reloaded_message', $this->publishers_games_reloaded_message, true);
        $criteria->compare('publishers_games_failed_message', $this->publishers_games_failed_message, true);
        $criteria->compare('publishers_games_remark', $this->publishers_games_remark, true);
        $criteria->compare('publishers_server', $this->publishers_server, true);
        $criteria->compare('publishers_games_daily_topped_amount', $this->publishers_games_daily_topped_amount);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,));
    }

    public function getGameInfo($publishers_id, $products_id)
    {
        $return_array = array();

        $select = " SELECT pg.publishers_game, pg.publishers_games_id 
                    FROM " . $this->tableName() . " AS pg 
                    INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp
                        ON pp.publishers_games_id = pg.publishers_games_id
                    WHERE pg.publishers_id = :publishers_id
                        AND pp.products_id = :products_id";
        $command = $this->conn->createCommand($select);
        $command->bindParam(":publishers_id", $publishers_id, PDO::PARAM_INT);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_array = $value;
        }

        return $return_array;
    }

    public function getGamesByProductID($products_id)
    {
        $return_array = array();

        $select = "	SELECT pg.publishers_id, pg.publishers_games_id, pg.publishers_game 
                    FROM " . $this->tableName() . " AS pg
                    INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp
                        ON pg.publishers_games_id = pp.publishers_games_id
                    WHERE pp.products_id = :products_id";
        $command = $this->conn->createCommand($select);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_array = $value;
        }

        return $return_array;
    }

    public function getServerByProductID($products_id)
    {
        $return_str = '';

        $select = "	SELECT pg.publishers_server
                    FROM " . $this->tableName() . " AS pg
                    INNER JOIN " . PublishersProductsBase::model()->tableName() . " AS pp
                        ON pg.publishers_games_id = pp.publishers_games_id
                    WHERE pp.products_id = :products_id";
        $command = $this->conn->createCommand($select);
        $command->bindParam(":products_id", $products_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_str = $value['publishers_server'];
        }

        return $return_str;
    }

    public function getPublisherMsg($productId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'publishers_games_pending_message, publishers_games_reloaded_message, publishers_games_failed_message';
        $criteria->with = array(
            'publishers_products' => array(
                'condition' => 'products_id = :productsId', 'params' => array(':productsId' => $productId)));
        $result = $this->model()->find($criteria);
        return $result;
    }
}