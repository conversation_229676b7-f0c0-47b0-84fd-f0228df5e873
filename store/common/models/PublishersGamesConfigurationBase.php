<?php

/**
 * This is the model class for table "publishers_games_configuration".
 *
 * The followings are the available columns in table 'publishers_games_configuration':
 * @property string $publishers_games_configuration_id
 * @property string $publishers_games_id
 * @property string $publishers_games_configuration_title
 * @property string $publishers_games_configuration_key
 * @property string $publishers_games_configuration_value
 * @property string $publishers_games_configuration_description
 * @property integer $sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property integer $last_modified_by
 * @property string $use_function
 * @property string $set_function
 */
class PublishersGamesConfigurationBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PublishersGamesConfigurationBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'publishers_games_configuration';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('publishers_games_configuration_value, set_function', 'required'),
			array('sort_order, last_modified_by', 'numerical', 'integerOnly'=>true),
			array('publishers_games_id', 'length', 'max'=>10),
			array('publishers_games_configuration_title, publishers_games_configuration_key', 'length', 'max'=>64),
			array('publishers_games_configuration_description, use_function', 'length', 'max'=>255),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('publishers_games_configuration_id, publishers_games_id, publishers_games_configuration_title, publishers_games_configuration_key, publishers_games_configuration_value, publishers_games_configuration_description, sort_order, last_modified, date_added, last_modified_by, use_function, set_function', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'publishers_games_configuration_id' => 'Publishers Games Configuration',
			'publishers_games_id' => 'Publishers Games',
			'publishers_games_configuration_title' => 'Publishers Games Configuration Title',
			'publishers_games_configuration_key' => 'Publishers Games Configuration Key',
			'publishers_games_configuration_value' => 'Publishers Games Configuration Value',
			'publishers_games_configuration_description' => 'Publishers Games Configuration Description',
			'sort_order' => 'Sort Order',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'last_modified_by' => 'Last Modified By',
			'use_function' => 'Use Function',
			'set_function' => 'Set Function',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('publishers_games_configuration_id',$this->publishers_games_configuration_id,true);
		$criteria->compare('publishers_games_id',$this->publishers_games_id,true);
		$criteria->compare('publishers_games_configuration_title',$this->publishers_games_configuration_title,true);
		$criteria->compare('publishers_games_configuration_key',$this->publishers_games_configuration_key,true);
		$criteria->compare('publishers_games_configuration_value',$this->publishers_games_configuration_value,true);
		$criteria->compare('publishers_games_configuration_description',$this->publishers_games_configuration_description,true);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified_by',$this->last_modified_by);
		$criteria->compare('use_function',$this->use_function,true);
		$criteria->compare('set_function',$this->set_function,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}