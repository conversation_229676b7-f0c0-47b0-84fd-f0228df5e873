<?php

/**
 * This is the model class for table "publishers_products".
 *
 * The followings are the available columns in table 'publishers_products':
 * @property string $publishers_games_id
 * @property integer $products_id
 */
class PublishersProductsBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return PublishersProductsBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'publishers_products';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('products_id', 'numerical', 'integerOnly' => true),
            array('publishers_games_id', 'length', 'max' => 10), // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('publishers_games_id, products_id', 'safe', 'on' => 'search'),);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'pg' => array(self::BELONGS_TO, 'PublishersGamesBase', 'publishers_games_id')
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'publishers_games_id' => 'Publishers Games', 'products_id' => 'Products',);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('publishers_games_id', $this->publishers_games_id, true);
        $criteria->compare('products_id', $this->products_id);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,));
    }

    public function getBundleProductID($publishers_games_products_id)
    {
        $return_int = 0;

        $select = " SELECT pp.products_id
                    FROM " . $this->tableName() . " AS pp 
                    INNER JOIN " . ProductsDeliveryInfoBase::model()->tableName() . " AS pdi
                        ON pdi.products_id = pp.products_id 
                    INNER JOIN " . ProductsBundlesBase::model()->tableName() . " AS pb
                        ON pp.products_id=pb.subproduct_id
                    WHERE pb.bundle_id = :bundle_id
                        AND pdi.products_delivery_mode_id = '6'
                    LIMIT 1";
        $command = $this->conn->createCommand($select);
        $command->bindParam(":bundle_id", $publishers_games_products_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_int = $value['products_id'];
        }

        return $return_int;
    }


    public function getServers($pid)
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PublishersProducts::model()->tableName() . '/products_id/' . $pid . '/publishers_server';
        $dtu_servers = Yii::app()->cache->get($cache_key);

        if ($dtu_servers === false) {
            $c = new CDbCriteria();
            $c->select = 't.publishers_games_id';
            $c->with = array('pg');
            $c->condition = 't.products_id = :pid';
            $c->params = array(':pid' => $pid);
            $c_data = $this->find($c);

            if (isset($c_data->pg->publishers_server)) {
                $dtu_servers = CJSON::decode($c_data->pg->publishers_server);
                Yii::app()->cache->set($cache_key, $dtu_servers, 86400);
            }
        }
        return $dtu_servers;
    }
}