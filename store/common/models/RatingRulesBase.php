<?php

/**
 * This is the model class for table "rating_rules". 
 * 
 * The followings are the available columns in table 'rating_rule': 
 * @property integer $rule_id
 * @property string $rule_name
 * @property string $rule_type
 * @property string $rule_operator
 * @property string $rule_url
 * @property string $created_date
 * @property string $last_modified_date
 */
class RatingRulesBase extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class. 
     * @param string $className active record class name. 
     * @return RatingRule the static model class 
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name 
     */
    public function tableName() {
        return 'rating_rules';
    }

    /**
     * @return array validation rules for model attributes. 
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that 
        // will receive user inputs. 
        return array(
            array('rule_name, rule_type, rule_operator, rule_url, created_date, last_modified_date', 'required'),
            array('rule_name, rule_type, rule_operator', 'length', 'max' => 32),
            // The following rule is used by search(). 
            // Please remove those attributes that should not be searched. 
            array('rule_id, rule_name, rule_type, rule_operator, rule_value, created_date, last_modified_date', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules. 
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related 
        // class name for the relations automatically generated below. 
//        return array( 'rulesTags' => array(self::BELONGS_TO, 'RatingRulesTagsBase', 'rule_id'),
//            
//            'allTags'=>array(
//                self::HAS_MANY,'RatingTagsBase',array('tag_id'=>'tag_id'),'through'=>'rulesTags','order'=>'allTags.last_modified_date ASC'
//            ),
//            
//        ); 
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label) 
     */
    public function attributeLabels() {
        return array(
            'rule_id' => 'Rule',
            'rule_name' => 'Rule Name',
            'rule_type' => 'Rule Type',
            'rule_operator' => 'Rule Operator',
            'rule_value' => 'Rule Value',
            'created_date' => 'Created Date',
            'last_modified_date' => 'Last Modified Date',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions. 
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that 
        // should not be searched. 

        $criteria = new CDbCriteria;

        $criteria->compare('rule_id', $this->rule_id);
        $criteria->compare('rule_name', $this->rule_name, true);
        $criteria->compare('rule_type', $this->rule_type, true);
        $criteria->compare('rule_operator', $this->rule_operator, true);
        $criteria->compare('rule_value', $this->rule_value, true);
        $criteria->compare('created_date', $this->created_date, true);
        $criteria->compare('last_modified_date', $this->last_modified_date, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }


}
