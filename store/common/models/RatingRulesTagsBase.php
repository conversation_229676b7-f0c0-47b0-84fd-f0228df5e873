<?php

/** 
 * This is the model class for table "rating_rules_tags". 
 * 
 * The followings are the available columns in table 'rating_rules_tags': 
 * @property integer $rule_tag_id
 * @property integer $rule_id
 * @property integer $tag_id
 * 
 * The followings are the available model relations: 
 * @property RatingRules $rule
 */ 
class RatingRulesTagsBase extends CActiveRecord
{ 
    /** 
     * Returns the static model of the specified AR class. 
     * @param string $className active record class name. 
     * @return RatingRulesTags the static model class 
     */ 
    public static function model($className=__CLASS__) 
    { 
        return parent::model($className); 
    } 

    /** 
     * @return string the associated database table name 
     */ 
    public function tableName() 
    { 
        return 'rating_rules_tags'; 
    } 

    /** 
     * @return array validation rules for model attributes. 
     */ 
    public function rules() 
    { 
        // NOTE: you should only define rules for those attributes that 
        // will receive user inputs. 
        return array( 
            array('rule_id, tag_id', 'required'),
            array('rule_id, tag_id', 'numerical', 'integerOnly'=>true),
            // The following rule is used by search(). 
            // Please remove those attributes that should not be searched. 
            array('rule_tag_id, rule_id, tag_id', 'safe', 'on'=>'search'), 
        ); 
    } 

    /** 
     * @return array relational rules. 
     */ 
    public function relations() 
    { 
        // NOTE: you may need to adjust the relation name and the related 
        // class name for the relations automatically generated below. 
        return array( 
            
        ); 
    } 

    /** 
     * @return array customized attribute labels (name=>label) 
     */ 
    public function attributeLabels() 
    { 
        return array( 
            'rule_tag_id' => 'Rule Tag',
            'rule_id' => 'Rule',
            'tag_id' => 'Tag',
        ); 
    } 

    /** 
     * Retrieves a list of models based on the current search/filter conditions. 
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */ 
    public function search() 
    { 
        // Warning: Please modify the following code to remove attributes that 
        // should not be searched. 

        $criteria=new CDbCriteria; 

        $criteria->compare('rule_tag_id',$this->rule_tag_id);
        $criteria->compare('rule_id',$this->rule_id);
        $criteria->compare('tag_id',$this->tag_id);

        return new CActiveDataProvider($this, array( 
            'criteria'=>$criteria, 
        )); 
    } 
}