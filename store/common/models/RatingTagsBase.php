<?php

/** 
 * This is the model class for table "rating_tags". 
 * 
 * The followings are the available columns in table 'rating_tag': 
 * @property integer $tag_id
 * @property string $tag_name
 * @property string $tag_method
 * @property string $tag_trigger_content
 * @property integer $rule_id
 * @property string $created_date
 * @property string $last_modified_date
 */ 
class RatingTagsBase extends CActiveRecord
{ 
    /** 
     * Returns the static model of the specified AR class. 
     * @param string $className active record class name. 
     * @return RatingTag the static model class 
     */ 
    public static function model($className=__CLASS__) 
    { 
        return parent::model($className); 
    } 

    /** 
     * @return string the associated database table name 
     */ 
    public function tableName() 
    { 
        return 'rating_tags'; 
    } 
    

    /** 
     * @return array validation rules for model attributes. 
     */ 
    public function rules() 
    { 
        // NOTE: you should only define rules for those attributes that 
        // will receive user inputs. 
        return array( 
            array('tag_name, tag_method, tag_trigger_content,tag_sequence,internal_index_sequence,created_date, last_modified_date', 'required'),
            array('tag_name, tag_method', 'length', 'max'=>32),
            // The following rule is used by search(). 
            // Please remove those attributes that should not be searched. 
            array('tag_id, tag_name, tag_method, tag_trigger_content, tag_sequence,internal_index_sequence, created_date, last_modified_date', 'safe', 'on'=>'search'),
        ); 
    } 

    /** 
     * @return array relational rules. 
     */ 
    
 public function relations() 
    { 
        // NOTE: you may need to adjust the relation name and the related 
        // class name for the relations automatically generated below. 
        return array( 
        ); 
    } 
    /** 
     * @return array customized attribute labels (name=>label) 
     */ 
    public function attributeLabels() 
    { 
        return array( 
            'tag_id' => 'Tag',
            'tag_name' => 'Tag Name',
            'tag_method' => 'Tag Method',
            'tag_trigger_content' => 'Tag Trigger Content',
            'tag_sequence' => 'Tag Sequence',
            'created_date' => 'Created Date',
            'last_modified_date' => 'Last Modified Date',
        ); 
    } 

    /** 
     * Retrieves a list of models based on the current search/filter conditions. 
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */ 
    public function search() 
    { 
        // Warning: Please modify the following code to remove attributes that 
        // should not be searched. 

        $criteria=new CDbCriteria; 

        $criteria->compare('tag_id',$this->tag_id);
        $criteria->compare('tag_name',$this->tag_name,true);
        $criteria->compare('tag_method',$this->tag_method,true);
        $criteria->compare('tag_trigger_content',$this->tag_trigger_content,true);
        $criteria->compare('rule_id',$this->rule_id);
        $criteria->compare('created_date',$this->created_date,true);
        $criteria->compare('last_modified_date',$this->last_modified_date,true);

        return new CActiveDataProvider($this, array( 
            'criteria'=>$criteria, 
        )); 
    } 
}