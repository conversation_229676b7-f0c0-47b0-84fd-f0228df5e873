<?php

/**
 * This is the model class for table "rhb".
 *
 * The followings are the available columns in table 'rhb':
 * @property integer $rhb_orders_id
 * @property string $rhb_status
 * @property string $rhb_amount
 * @property string $rhb_currency
 * @property string $rhb_return_code
 */
class RhbBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return RhbBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'rhb';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('rhb_orders_id', 'numerical', 'integerOnly'=>true),
			array('rhb_status', 'length', 'max'=>20),
			array('rhb_amount', 'length', 'max'=>15),
			array('rhb_currency', 'length', 'max'=>3),
			array('rhb_return_code', 'length', 'max'=>2),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('rhb_orders_id, rhb_status, rhb_amount, rhb_currency, rhb_return_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'rhb_orders_id' => 'Rhb Orders',
			'rhb_status' => 'Rhb Status',
			'rhb_amount' => 'Rhb Amount',
			'rhb_currency' => 'Rhb Currency',
			'rhb_return_code' => 'Rhb Return Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('rhb_orders_id',$this->rhb_orders_id);
		$criteria->compare('rhb_status',$this->rhb_status,true);
		$criteria->compare('rhb_amount',$this->rhb_amount,true);
		$criteria->compare('rhb_currency',$this->rhb_currency,true);
		$criteria->compare('rhb_return_code',$this->rhb_return_code,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}