<?php

/**
 * This is the model class for table "rhb_payment_status_history".
 *
 * The followings are the available columns in table 'rhb_payment_status_history':
 * @property integer $rhb_payment_status_history_id
 * @property integer $rhb_orders_id
 * @property string $rhb_date
 * @property string $rhb_status
 * @property string $rhb_description
 * @property string $rhb_changed_by
 */
class RhbPaymentStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return RhbPaymentStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'rhb_payment_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('rhb_orders_id', 'numerical', 'integerOnly'=>true),
			array('rhb_status', 'length', 'max'=>20),
			array('rhb_description', 'length', 'max'=>64),
			array('rhb_changed_by', 'length', 'max'=>128),
			array('rhb_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('rhb_payment_status_history_id, rhb_orders_id, rhb_date, rhb_status, rhb_description, rhb_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'rhb_payment_status_history_id' => 'Rhb Payment Status History',
			'rhb_orders_id' => 'Rhb Orders',
			'rhb_date' => 'Rhb Date',
			'rhb_status' => 'Rhb Status',
			'rhb_description' => 'Rhb Description',
			'rhb_changed_by' => 'Rhb Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('rhb_payment_status_history_id',$this->rhb_payment_status_history_id);
		$criteria->compare('rhb_orders_id',$this->rhb_orders_id);
		$criteria->compare('rhb_date',$this->rhb_date,true);
		$criteria->compare('rhb_status',$this->rhb_status,true);
		$criteria->compare('rhb_description',$this->rhb_description,true);
		$criteria->compare('rhb_changed_by',$this->rhb_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}