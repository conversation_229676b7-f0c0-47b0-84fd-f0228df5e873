<?php

/**
 * This is the model class for table "sales_activities".
 *
 * The followings are the available columns in table 'sales_activities':
 * @property integer $sales_activities_id
 * @property string $sales_activities_date
 * @property integer $sales_activities_orders_id
 * @property integer $sales_activities_orders_products_id
 * @property integer $sales_activities_products_id
 * @property string $sales_activities_code
 * @property string $sales_activities_operator
 * @property string $sales_activities_amount
 * @property integer $sales_activities_quantity
 * @property string $sales_activities_by_admin_id
 */
class SalesActivitiesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SalesActivitiesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'sales_activities';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('sales_activities_orders_id, sales_activities_orders_products_id, sales_activities_products_id, sales_activities_quantity', 'numerical', 'integerOnly'=>true),
			array('sales_activities_code', 'length', 'max'=>5),
			array('sales_activities_operator', 'length', 'max'=>1),
			array('sales_activities_amount', 'length', 'max'=>15),
			array('sales_activities_by_admin_id', 'length', 'max'=>255),
			array('sales_activities_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('sales_activities_id, sales_activities_date, sales_activities_orders_id, sales_activities_orders_products_id, sales_activities_products_id, sales_activities_code, sales_activities_operator, sales_activities_amount, sales_activities_quantity, sales_activities_by_admin_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'sales_activities_id' => 'Sales Activities',
			'sales_activities_date' => 'Sales Activities Date',
			'sales_activities_orders_id' => 'Sales Activities Orders',
			'sales_activities_orders_products_id' => 'Sales Activities Orders Products',
			'sales_activities_products_id' => 'Sales Activities Products',
			'sales_activities_code' => 'Sales Activities Code',
			'sales_activities_operator' => 'Sales Activities Operator',
			'sales_activities_amount' => 'Sales Activities Amount',
			'sales_activities_quantity' => 'Sales Activities Quantity',
			'sales_activities_by_admin_id' => 'Sales Activities By Admin',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('sales_activities_id',$this->sales_activities_id);
		$criteria->compare('sales_activities_date',$this->sales_activities_date,true);
		$criteria->compare('sales_activities_orders_id',$this->sales_activities_orders_id);
		$criteria->compare('sales_activities_orders_products_id',$this->sales_activities_orders_products_id);
		$criteria->compare('sales_activities_products_id',$this->sales_activities_products_id);
		$criteria->compare('sales_activities_code',$this->sales_activities_code,true);
		$criteria->compare('sales_activities_operator',$this->sales_activities_operator,true);
		$criteria->compare('sales_activities_amount',$this->sales_activities_amount,true);
		$criteria->compare('sales_activities_quantity',$this->sales_activities_quantity);
		$criteria->compare('sales_activities_by_admin_id',$this->sales_activities_by_admin_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}