<?php

/**
 * This is the model class for table "search_criteria".
 *
 * The followings are the available columns in table 'search_criteria':
 * @property integer $search_criteria_id
 * @property string $filename
 * @property string $search_criteria_name
 * @property string $search_criteria_string
 * @property string $date_search_criteria_added
 * @property string $date_search_criteria_last_modified
 * @property string $search_criteria_created_by
 * @property string $last_modified_by
 */
class SearchCriteriaBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SearchCriteriaBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'search_criteria';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('filename, search_criteria_name, search_criteria_created_by, last_modified_by', 'length', 'max'=>255),
			array('search_criteria_string, date_search_criteria_added, date_search_criteria_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('search_criteria_id, filename, search_criteria_name, search_criteria_string, date_search_criteria_added, date_search_criteria_last_modified, search_criteria_created_by, last_modified_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'search_criteria_id' => 'Search Criteria',
			'filename' => 'Filename',
			'search_criteria_name' => 'Search Criteria Name',
			'search_criteria_string' => 'Search Criteria String',
			'date_search_criteria_added' => 'Date Search Criteria Added',
			'date_search_criteria_last_modified' => 'Date Search Criteria Last Modified',
			'search_criteria_created_by' => 'Search Criteria Created By',
			'last_modified_by' => 'Last Modified By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('search_criteria_id',$this->search_criteria_id);
		$criteria->compare('filename',$this->filename,true);
		$criteria->compare('search_criteria_name',$this->search_criteria_name,true);
		$criteria->compare('search_criteria_string',$this->search_criteria_string,true);
		$criteria->compare('date_search_criteria_added',$this->date_search_criteria_added,true);
		$criteria->compare('date_search_criteria_last_modified',$this->date_search_criteria_last_modified,true);
		$criteria->compare('search_criteria_created_by',$this->search_criteria_created_by,true);
		$criteria->compare('last_modified_by',$this->last_modified_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}