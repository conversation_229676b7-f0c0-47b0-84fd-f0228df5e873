<?php

/**
 * This is the model class for table "search_keywords".
 *
 * The followings are the available columns in table 'search_keywords':
 * @property string $tpl_id
 * @property string $id
 * @property integer $id_type
 * @property string $language_id
 * @property string $search_value
 */
class SearchKeywordsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SearchKeywordsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'search_keywords';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tpl_id, id_type, search_value', 'required'),
			array('id_type', 'numerical', 'integerOnly'=>true),
			array('tpl_id, id, language_id', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('tpl_id, id, id_type, language_id, search_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tpl_id' => 'Tpl',
			'id' => 'ID',
			'id_type' => 'Id Type',
			'language_id' => 'Language',
			'search_value' => 'Search Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tpl_id',$this->tpl_id,true);
		$criteria->compare('id',$this->id,true);
		$criteria->compare('id_type',$this->id_type);
		$criteria->compare('language_id',$this->language_id,true);
		$criteria->compare('search_value',$this->search_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    
    public function getTplIDsByMatchingAgainstScore($keyword) {
        $return_array = array();
//        $criteria = new CDbCriteria;
//		$criteria->select = 'DISTINCT *, MATCH(search_value) AGAINST(:keyword) AS score';
//		$criteria->condition = 'MATCH(search_value) AGAINST(:keyword)';
//		$criteria->params = array(
//            ':keyword' => $keyword,
//        );
//        $criteria->order = 'score DESC';
//        
//		if ($results = $this->model()->findAll($criteria)) {
//            foreach ($results as $result) {
//                $return_array[$result->tpl_id] = $result->getAttributes(NULL);
//            }
//        }
        
        $sql = "	SELECT DISTINCT *, MATCH(search_value) AGAINST(:keyword) AS score 
                    FROM " . $this->tableName() . " 
                    WHERE MATCH(search_value) AGAINST(:keyword)
                    ORDER BY score DESC";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":keyword", $keyword, PDO::PARAM_STR);

        if ($value = $command->queryAll()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
    
    public function getTplIDsByKeyword($keyword) {
        $return_array = array();
//        $keyword = addcslashes($keyword, '%_');
        $keyword = "%" . addcslashes($keyword, '%_') . "%";
        
//        $criteria = new CDbCriteria;
//		$criteria->select = 'DISTINCT *';
//		$criteria->condition = 'search_value LIKE :keyword';
//		$criteria->params = array(
//            ':keyword' => "%$keyword%",
//        );
//        
//		if ($results = $this->model()->findAll($criteria)) {
//            foreach ($results as $result) {
//                $return_array[$result->tpl_id] = $result->getAttributes(NULL);
//            }
//        }
        
        $sql = "SELECT DISTINCT *
                FROM " . $this->tableName() . "
                WHERE search_value LIKE :keyword";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":keyword", $keyword, PDO::PARAM_STR);
        
        if ($value = $command->queryAll()) {
            $return_array = $value;
        }
        
		return $return_array;
    }
}