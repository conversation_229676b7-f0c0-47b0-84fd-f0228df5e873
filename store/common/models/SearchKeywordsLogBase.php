<?php

/**
 * This is the model class for table "search_keywords_log".
 *
 * The followings are the available columns in table 'search_keywords_log':
 * @property integer $search_keywords_log_id
 * @property string $search_keywords_log_date
 * @property string $search_keywords_log_keywords
 * @property string $search_keywords_log_categories_id
 * @property string $search_keywords_log_customer_id
 * @property string $search_keywords_log_ip
 * @property string $search_keywords_log_ip_country
 */
class SearchKeywordsLogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SearchKeywordsLogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'search_keywords_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('search_keywords_log_categories_id, search_keywords_log_customer_id, search_keywords_log_ip_country', 'length', 'max'=>32),
			array('search_keywords_log_ip', 'length', 'max'=>15),
			array('search_keywords_log_date, search_keywords_log_keywords', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('search_keywords_log_id, search_keywords_log_date, search_keywords_log_keywords, search_keywords_log_categories_id, search_keywords_log_customer_id, search_keywords_log_ip, search_keywords_log_ip_country', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'search_keywords_log_id' => 'Search Keywords Log',
			'search_keywords_log_date' => 'Search Keywords Log Date',
			'search_keywords_log_keywords' => 'Search Keywords Log Keywords',
			'search_keywords_log_categories_id' => 'Search Keywords Log Categories',
			'search_keywords_log_customer_id' => 'Search Keywords Log Customer',
			'search_keywords_log_ip' => 'Search Keywords Log Ip',
			'search_keywords_log_ip_country' => 'Search Keywords Log Ip Country',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('search_keywords_log_id',$this->search_keywords_log_id);
		$criteria->compare('search_keywords_log_date',$this->search_keywords_log_date,true);
		$criteria->compare('search_keywords_log_keywords',$this->search_keywords_log_keywords,true);
		$criteria->compare('search_keywords_log_categories_id',$this->search_keywords_log_categories_id,true);
		$criteria->compare('search_keywords_log_customer_id',$this->search_keywords_log_customer_id,true);
		$criteria->compare('search_keywords_log_ip',$this->search_keywords_log_ip,true);
		$criteria->compare('search_keywords_log_ip_country',$this->search_keywords_log_ip_country,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}