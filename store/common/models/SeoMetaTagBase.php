<?php

/**
 * This is the model class for table "seo_meta_tag".
 *
 * The followings are the available columns in table 'seo_meta_tag':
 * @property integer $seo_meta_id
 * @property integer $language_id
 * @property integer $seo_meta_page_type
 * @property string $seo_meta_baseurl
 * @property string $seo_meta_query_string
 * @property string $seo_meta_title
 * @property integer $seo_meta_title_overwrite
 * @property string $seo_meta_description
 * @property integer $seo_meta_description_overwrite
 * @property string $seo_meta_keywords
 * @property integer $seo_meta_keywords_overwrite
 * @property integer $seo_meta_robots
 * @property string $seo_meta_lastupdate
 */
class SeoMetaTagBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SeoMetaTagBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'seo_meta_tag';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('language_id, seo_meta_page_type, seo_meta_title_overwrite, seo_meta_description_overwrite, seo_meta_keywords_overwrite, seo_meta_robots', 'numerical', 'integerOnly'=>true),
			array('seo_meta_baseurl', 'length', 'max'=>50),
			array('seo_meta_query_string', 'length', 'max'=>100),
			array('seo_meta_title, seo_meta_keywords', 'length', 'max'=>255),
			array('seo_meta_description, seo_meta_lastupdate', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('seo_meta_id, language_id, seo_meta_page_type, seo_meta_baseurl, seo_meta_query_string, seo_meta_title, seo_meta_title_overwrite, seo_meta_description, seo_meta_description_overwrite, seo_meta_keywords, seo_meta_keywords_overwrite, seo_meta_robots, seo_meta_lastupdate', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'seo_meta_id' => 'Seo Meta',
			'language_id' => 'Language',
			'seo_meta_page_type' => 'Seo Meta Page Type',
			'seo_meta_baseurl' => 'Seo Meta Baseurl',
			'seo_meta_query_string' => 'Seo Meta Query String',
			'seo_meta_title' => 'Seo Meta Title',
			'seo_meta_title_overwrite' => 'Seo Meta Title Overwrite',
			'seo_meta_description' => 'Seo Meta Description',
			'seo_meta_description_overwrite' => 'Seo Meta Description Overwrite',
			'seo_meta_keywords' => 'Seo Meta Keywords',
			'seo_meta_keywords_overwrite' => 'Seo Meta Keywords Overwrite',
			'seo_meta_robots' => 'Seo Meta Robots',
			'seo_meta_lastupdate' => 'Seo Meta Lastupdate',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('seo_meta_id',$this->seo_meta_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('seo_meta_page_type',$this->seo_meta_page_type);
		$criteria->compare('seo_meta_baseurl',$this->seo_meta_baseurl,true);
		$criteria->compare('seo_meta_query_string',$this->seo_meta_query_string,true);
		$criteria->compare('seo_meta_title',$this->seo_meta_title,true);
		$criteria->compare('seo_meta_title_overwrite',$this->seo_meta_title_overwrite);
		$criteria->compare('seo_meta_description',$this->seo_meta_description,true);
		$criteria->compare('seo_meta_description_overwrite',$this->seo_meta_description_overwrite);
		$criteria->compare('seo_meta_keywords',$this->seo_meta_keywords,true);
		$criteria->compare('seo_meta_keywords_overwrite',$this->seo_meta_keywords_overwrite);
		$criteria->compare('seo_meta_robots',$this->seo_meta_robots);
		$criteria->compare('seo_meta_lastupdate',$this->seo_meta_lastupdate,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}