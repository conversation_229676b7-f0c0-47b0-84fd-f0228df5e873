<?php

/**
 * This is the model class for table "shipping_classes".
 *
 * The followings are the available columns in table 'shipping_classes':
 * @property integer $shipping_classes_id
 * @property string $shipping_classes_name
 * @property string $shipping_module
 */
class ShippingClassesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ShippingClassesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'shipping_classes';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('shipping_classes_name, shipping_module', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('shipping_classes_id, shipping_classes_name, shipping_module', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'shipping_classes_id' => 'Shipping Classes',
			'shipping_classes_name' => 'Shipping Classes Name',
			'shipping_module' => 'Shipping Module',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('shipping_classes_id',$this->shipping_classes_id);
		$criteria->compare('shipping_classes_name',$this->shipping_classes_name,true);
		$criteria->compare('shipping_module',$this->shipping_module,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}