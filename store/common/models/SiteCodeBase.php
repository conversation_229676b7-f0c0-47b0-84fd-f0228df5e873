<?php

/**
 * This is the model class for table "site_code".
 *
 * The followings are the available columns in table 'site_code':
 * @property integer $site_id
 * @property string $site_name
 * @property integer $site_has_buyback
 * @property string $admin_groups_id
 * @property string $buyback_admin_groups_id
 */
class SiteCodeBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SiteCodeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'site_code';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('admin_groups_id, buyback_admin_groups_id', 'required'),
			array('site_id, site_has_buyback', 'numerical', 'integerOnly'=>true),
			array('site_name', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('site_id, site_name, site_has_buyback, admin_groups_id, buyback_admin_groups_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'site_id' => 'Site',
			'site_name' => 'Site Name',
			'site_has_buyback' => 'Site Has Buyback',
			'admin_groups_id' => 'Admin Groups',
			'buyback_admin_groups_id' => 'Buyback Admin Groups',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('site_id',$this->site_id);
		$criteria->compare('site_name',$this->site_name,true);
		$criteria->compare('site_has_buyback',$this->site_has_buyback);
		$criteria->compare('admin_groups_id',$this->admin_groups_id,true);
		$criteria->compare('buyback_admin_groups_id',$this->buyback_admin_groups_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}