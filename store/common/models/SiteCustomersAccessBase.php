<?php

/**
 * This is the model class for table "site_customers_access".
 *
 * The followings are the available columns in table 'site_customers_access':
 * @property integer $site_id
 * @property integer $customers_groups_id
 * @property string $admin_groups_id
 * @property string $discount_setting_admin_groups_id
 * @property string $discount_setting_notification
 */
class SiteCustomersAccessBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SiteCustomersAccessBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'site_customers_access';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('admin_groups_id, discount_setting_admin_groups_id', 'required'),
			array('site_id, customers_groups_id', 'numerical', 'integerOnly'=>true),
			array('discount_setting_notification', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('site_id, customers_groups_id, admin_groups_id, discount_setting_admin_groups_id, discount_setting_notification', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'site_id' => 'Site',
			'customers_groups_id' => 'Customers Groups',
			'admin_groups_id' => 'Admin Groups',
			'discount_setting_admin_groups_id' => 'Discount Setting Admin Groups',
			'discount_setting_notification' => 'Discount Setting Notification',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('site_id',$this->site_id);
		$criteria->compare('customers_groups_id',$this->customers_groups_id);
		$criteria->compare('admin_groups_id',$this->admin_groups_id,true);
		$criteria->compare('discount_setting_admin_groups_id',$this->discount_setting_admin_groups_id,true);
		$criteria->compare('discount_setting_notification',$this->discount_setting_notification,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}