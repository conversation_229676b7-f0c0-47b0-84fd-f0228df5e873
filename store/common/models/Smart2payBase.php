<?php

/**
 * This is the model class for table "smart2pay".
 *
 * The followings are the available columns in table 'smart2pay':
 * @property string $smart2pay_order_id
 * @property string $smart2pay_merchant_id
 * @property string $smart2pay_payment_method
 * @property string $smart2pay_transaction_id
 * @property string $smart2pay_amount
 * @property string $smart2pay_currency
 * @property integer $smart2pay_status_id
 * @property string $smart2pay_payment_date
 */
class Smart2payBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Smart2payBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'smart2pay';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('smart2pay_status_id', 'numerical', 'integerOnly'=>true),
			array('smart2pay_order_id', 'length', 'max'=>11),
			array('smart2pay_merchant_id, smart2pay_transaction_id', 'length', 'max'=>37),
			array('smart2pay_payment_method', 'length', 'max'=>20),
			array('smart2pay_amount', 'length', 'max'=>15),
			array('smart2pay_currency', 'length', 'max'=>3),
			array('smart2pay_payment_date', 'length', 'max'=>30),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('smart2pay_order_id, smart2pay_merchant_id, smart2pay_payment_method, smart2pay_transaction_id, smart2pay_amount, smart2pay_currency, smart2pay_status_id, smart2pay_payment_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'smart2pay_order_id' => 'Smart2pay Order',
			'smart2pay_merchant_id' => 'Smart2pay Merchant',
			'smart2pay_payment_method' => 'Smart2pay Payment Method',
			'smart2pay_transaction_id' => 'Smart2pay Transaction',
			'smart2pay_amount' => 'Smart2pay Amount',
			'smart2pay_currency' => 'Smart2pay Currency',
			'smart2pay_status_id' => 'Smart2pay Status',
			'smart2pay_payment_date' => 'Smart2pay Payment Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('smart2pay_order_id',$this->smart2pay_order_id,true);
		$criteria->compare('smart2pay_merchant_id',$this->smart2pay_merchant_id,true);
		$criteria->compare('smart2pay_payment_method',$this->smart2pay_payment_method,true);
		$criteria->compare('smart2pay_transaction_id',$this->smart2pay_transaction_id,true);
		$criteria->compare('smart2pay_amount',$this->smart2pay_amount,true);
		$criteria->compare('smart2pay_currency',$this->smart2pay_currency,true);
		$criteria->compare('smart2pay_status_id',$this->smart2pay_status_id);
		$criteria->compare('smart2pay_payment_date',$this->smart2pay_payment_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}