<?php

/**
 * This is the model class for table "smart2pay_globalpay".
 *
 * The followings are the available columns in table 'smart2pay_globalpay':
 * @property string $smart2pay_globalpay_order_id
 * @property string $smart2pay_globalpay_merchant_id
 * @property string $smart2pay_globalpay_method_id_returned
 * @property string $smart2pay_globalpay_transaction_id
 * @property string $smart2pay_globalpay_amount
 * @property string $smart2pay_globalpay_currency
 * @property integer $smart2pay_globalpay_status_id
 * @property string $smart2pay_globalpay_return_info
 * @property string $smart2pay_globalpay_hash
 */
class Smart2payGlobalpayBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Smart2payGlobalpayBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'smart2pay_globalpay';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('smart2pay_globalpay_status_id', 'numerical', 'integerOnly'=>true),
			array('smart2pay_globalpay_order_id', 'length', 'max'=>11),
			array('smart2pay_globalpay_merchant_id', 'length', 'max'=>12),
			array('smart2pay_globalpay_method_id_returned, smart2pay_globalpay_transaction_id', 'length', 'max'=>20),
			array('smart2pay_globalpay_amount', 'length', 'max'=>15),
			array('smart2pay_globalpay_currency', 'length', 'max'=>3),
			array('smart2pay_globalpay_return_info', 'length', 'max'=>128),
			array('smart2pay_globalpay_hash', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('smart2pay_globalpay_order_id, smart2pay_globalpay_merchant_id, smart2pay_globalpay_method_id_returned, smart2pay_globalpay_transaction_id, smart2pay_globalpay_amount, smart2pay_globalpay_currency, smart2pay_globalpay_status_id, smart2pay_globalpay_return_info, smart2pay_globalpay_hash', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'smart2pay_globalpay_order_id' => 'Smart2pay Globalpay Order',
			'smart2pay_globalpay_merchant_id' => 'Smart2pay Globalpay Merchant',
			'smart2pay_globalpay_method_id_returned' => 'Smart2pay Globalpay Method Id Returned',
			'smart2pay_globalpay_transaction_id' => 'Smart2pay Globalpay Transaction',
			'smart2pay_globalpay_amount' => 'Smart2pay Globalpay Amount',
			'smart2pay_globalpay_currency' => 'Smart2pay Globalpay Currency',
			'smart2pay_globalpay_status_id' => 'Smart2pay Globalpay Status',
			'smart2pay_globalpay_return_info' => 'Smart2pay Globalpay Return Info',
			'smart2pay_globalpay_hash' => 'Smart2pay Globalpay Hash',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('smart2pay_globalpay_order_id',$this->smart2pay_globalpay_order_id,true);
		$criteria->compare('smart2pay_globalpay_merchant_id',$this->smart2pay_globalpay_merchant_id,true);
		$criteria->compare('smart2pay_globalpay_method_id_returned',$this->smart2pay_globalpay_method_id_returned,true);
		$criteria->compare('smart2pay_globalpay_transaction_id',$this->smart2pay_globalpay_transaction_id,true);
		$criteria->compare('smart2pay_globalpay_amount',$this->smart2pay_globalpay_amount,true);
		$criteria->compare('smart2pay_globalpay_currency',$this->smart2pay_globalpay_currency,true);
		$criteria->compare('smart2pay_globalpay_status_id',$this->smart2pay_globalpay_status_id);
		$criteria->compare('smart2pay_globalpay_return_info',$this->smart2pay_globalpay_return_info,true);
		$criteria->compare('smart2pay_globalpay_hash',$this->smart2pay_globalpay_hash,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}