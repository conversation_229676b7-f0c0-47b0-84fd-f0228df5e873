<?php

/**
 * This is the model class for table "smart2pay_globalpay_status_history".
 *
 * The followings are the available columns in table 'smart2pay_globalpay_status_history':
 * @property string $smart2pay_globalpay_status_history_id
 * @property string $smart2pay_globalpay_order_id
 * @property string $smart2pay_globalpay_date
 * @property integer $smart2pay_globalpay_status_id
 * @property string $smart2pay_globalpay_description
 * @property string $smart2pay_globalpay_changed_by
 */
class Smart2payGlobalpayStatusHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Smart2payGlobalpayStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'smart2pay_globalpay_status_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('smart2pay_globalpay_status_id', 'numerical', 'integerOnly'=>true),
			array('smart2pay_globalpay_order_id', 'length', 'max'=>11),
			array('smart2pay_globalpay_description, smart2pay_globalpay_changed_by', 'length', 'max'=>128),
			array('smart2pay_globalpay_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('smart2pay_globalpay_status_history_id, smart2pay_globalpay_order_id, smart2pay_globalpay_date, smart2pay_globalpay_status_id, smart2pay_globalpay_description, smart2pay_globalpay_changed_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'smart2pay_globalpay_status_history_id' => 'Smart2pay Globalpay Status History',
			'smart2pay_globalpay_order_id' => 'Smart2pay Globalpay Order',
			'smart2pay_globalpay_date' => 'Smart2pay Globalpay Date',
			'smart2pay_globalpay_status_id' => 'Smart2pay Globalpay Status',
			'smart2pay_globalpay_description' => 'Smart2pay Globalpay Description',
			'smart2pay_globalpay_changed_by' => 'Smart2pay Globalpay Changed By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('smart2pay_globalpay_status_history_id',$this->smart2pay_globalpay_status_history_id,true);
		$criteria->compare('smart2pay_globalpay_order_id',$this->smart2pay_globalpay_order_id,true);
		$criteria->compare('smart2pay_globalpay_date',$this->smart2pay_globalpay_date,true);
		$criteria->compare('smart2pay_globalpay_status_id',$this->smart2pay_globalpay_status_id);
		$criteria->compare('smart2pay_globalpay_description',$this->smart2pay_globalpay_description,true);
		$criteria->compare('smart2pay_globalpay_changed_by',$this->smart2pay_globalpay_changed_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}