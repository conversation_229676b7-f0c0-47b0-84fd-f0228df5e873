<?php

/**
 * This is the model class for table "sms_report".
 *
 * The followings are the available columns in table 'sms_report':
 * @property string $customers_id
 * @property string $sms_request_date
 * @property string $sms_request_type
 * @property string $sms_request_phone_number
 * @property string $sms_request_phone_country_id
 * @property string $sms_request_page
 * @property string $sms_provider
 */
class SmsReportBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SmsReportBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'sms_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, sms_request_phone_country_id', 'length', 'max'=>11),
			array('sms_request_type, sms_provider', 'length', 'max'=>16),
			array('sms_request_phone_number', 'length', 'max'=>32),
			array('sms_request_page', 'length', 'max'=>128),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, sms_request_date, sms_request_type, sms_request_phone_number, sms_request_phone_country_id, sms_request_page, sms_provider', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'sms_request_date' => 'Sms Request Date',
			'sms_request_type' => 'Sms Request Type',
			'sms_request_phone_number' => 'Sms Request Phone Number',
			'sms_request_phone_country_id' => 'Sms Request Phone Country',
			'sms_request_page' => 'Sms Request Page',
			'sms_provider' => 'Sms Provider',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id,true);
		$criteria->compare('sms_request_date',$this->sms_request_date,true);
		$criteria->compare('sms_request_type',$this->sms_request_type,true);
		$criteria->compare('sms_request_phone_number',$this->sms_request_phone_number,true);
		$criteria->compare('sms_request_phone_country_id',$this->sms_request_phone_country_id,true);
		$criteria->compare('sms_request_page',$this->sms_request_page,true);
		$criteria->compare('sms_provider',$this->sms_provider,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}