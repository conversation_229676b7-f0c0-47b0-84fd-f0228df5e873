<?php

/**
 * This is the model class for table "sso_token".
 *
 * The followings are the available columns in table 'sso_token':
 * @property string $sso_token
 * @property string $customers_id
 * @property string $batch_id
 * @property string $login_method
 * @property string $datetime
 */
class SsoTokenBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SsoTokenBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'sso_token';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('sso_token, customers_id, datetime', 'required'),
			array('sso_token', 'length', 'max'=>36),
			array('customers_id', 'length', 'max'=>11),
			array('batch_id', 'length', 'max'=>32),
			array('login_method', 'length', 'max'=>12),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('sso_token, customers_id, batch_id, login_method, datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'sso_token' => 'Sso Token',
			'customers_id' => 'Customers',
			'batch_id' => 'Batch',
			'login_method' => 'Login Method',
			'datetime' => 'Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('sso_token',$this->sso_token,true);
		$criteria->compare('customers_id',$this->customers_id,true);
		$criteria->compare('batch_id',$this->batch_id,true);
		$criteria->compare('login_method',$this->login_method,true);
		$criteria->compare('datetime',$this->datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}