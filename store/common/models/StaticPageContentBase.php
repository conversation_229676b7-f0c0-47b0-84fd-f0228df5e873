<?php

/**
 * This is the model class for table "static_page_content".
 *
 * The followings are the available columns in table 'static_page_content':
 * @property integer $static_page_content_id
 * @property integer $static_page_id
 * @property integer $language_id
 * @property string  $title
 * @property string  $content
 */
class StaticPageContentBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AddressBookBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getDbConnection()
    {
        return Yii::app()->db_og;
    }

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'static_page_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('static_page_content_id, static_page_id, language_id', 'numerical', 'integerOnly'=>true),
			array('static_page_content_id, static_page_id', 'length', 'max'=>11),
			array('language_id', 'length', 'max'=>1),
			array('title', 'length', 'max'=>255),
			array('content', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('static_page_content_id, static_page_id, language_id, title, content', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'static_page_content_id' => 'Static Page Content ID',
			'static_page_id' => 'Static Page ID',
			'language_id' => 'Language ID',
			'title' => 'Title',
			'content' => 'Content'
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('static_page_id',$this->static_page_id);
		$criteria->compare('title',$this->title, true);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function getStaticPageContent($staticPageTypeCode, $language_id = 1) {
        return $this->getPageContent($staticPageTypeCode, $language_id);
    }

    protected function getPageContent($staticPageTypeCode, $language_id = 1) {
        $return_str = '';
        
        $sql = "	SELECT spc.content
					FROM static_page_type AS spt
					INNER JOIN static_page AS sp
						ON spt.static_page_type_id = sp.static_page_type_id
					INNER JOIN static_page_content AS spc
						ON sp.static_page_id = spc.static_page_id
					    AND spc.language_id = :language_id
					WHERE spt.type_code = :type_code Limit 1
                    ";
        $command = Yii::app()->db_og->createCommand($sql);
        $command->bindParam(":type_code", $staticPageTypeCode, PDO::PARAM_STR);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }
}