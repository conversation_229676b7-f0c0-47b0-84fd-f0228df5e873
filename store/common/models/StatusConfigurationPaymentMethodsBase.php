<?php

/**
 * This is the model class for table "status_configuration_payment_methods".
 *
 * The followings are the available columns in table 'status_configuration_payment_methods':
 * @property string $status_configuration_trans_type
 * @property integer $status_configuration_source_status_id
 * @property integer $status_configuration_destination_status_id
 * @property integer $status_configuration_user_groups_id
 * @property string $status_configuration_payment_methods_id
 */
class StatusConfigurationPaymentMethodsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StatusConfigurationPaymentMethodsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'status_configuration_payment_methods';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('status_configuration_payment_methods_id', 'required'),
			array('status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id', 'numerical', 'integerOnly'=>true),
			array('status_configuration_trans_type', 'length', 'max'=>5),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_payment_methods_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'status_configuration_trans_type' => 'Status Configuration Trans Type',
			'status_configuration_source_status_id' => 'Status Configuration Source Status',
			'status_configuration_destination_status_id' => 'Status Configuration Destination Status',
			'status_configuration_user_groups_id' => 'Status Configuration User Groups',
			'status_configuration_payment_methods_id' => 'Status Configuration Payment Methods',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('status_configuration_trans_type',$this->status_configuration_trans_type,true);
		$criteria->compare('status_configuration_source_status_id',$this->status_configuration_source_status_id);
		$criteria->compare('status_configuration_destination_status_id',$this->status_configuration_destination_status_id);
		$criteria->compare('status_configuration_user_groups_id',$this->status_configuration_user_groups_id);
		$criteria->compare('status_configuration_payment_methods_id',$this->status_configuration_payment_methods_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}