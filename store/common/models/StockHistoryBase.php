<?php

/**
 * This is the model class for table "stock_history".
 *
 * The followings are the available columns in table 'stock_history':
 * @property string $stock_history_date
 * @property integer $products_id
 * @property string $products_cat_path
 * @property integer $products_quantity
 * @property integer $products_actual_quantity
 * @property string $products_quantity_fifo_cost
 * @property string $products_actual_quantity_fifo_cost
 */
class StockHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StockHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'stock_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('products_id, products_quantity, products_actual_quantity', 'numerical', 'integerOnly'=>true),
			array('products_quantity_fifo_cost, products_actual_quantity_fifo_cost', 'length', 'max'=>15),
			array('products_cat_path', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('stock_history_date, products_id, products_cat_path, products_quantity, products_actual_quantity, products_quantity_fifo_cost, products_actual_quantity_fifo_cost', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'stock_history_date' => 'Stock History Date',
			'products_id' => 'Products',
			'products_cat_path' => 'Products Cat Path',
			'products_quantity' => 'Products Quantity',
			'products_actual_quantity' => 'Products Actual Quantity',
			'products_quantity_fifo_cost' => 'Products Quantity Fifo Cost',
			'products_actual_quantity_fifo_cost' => 'Products Actual Quantity Fifo Cost',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('stock_history_date',$this->stock_history_date,true);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('products_cat_path',$this->products_cat_path,true);
		$criteria->compare('products_quantity',$this->products_quantity);
		$criteria->compare('products_actual_quantity',$this->products_actual_quantity);
		$criteria->compare('products_quantity_fifo_cost',$this->products_quantity_fifo_cost,true);
		$criteria->compare('products_actual_quantity_fifo_cost',$this->products_actual_quantity_fifo_cost,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}