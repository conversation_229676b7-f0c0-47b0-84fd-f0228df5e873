<?php

/**
 * This is the model class for table "store_account_balance".
 *
 * The followings are the available columns in table 'store_account_balance':
 * @property integer $user_id
 * @property string $user_role
 * @property string $store_account_balance_currency
 * @property string $store_account_balance_amount
 * @property string $store_account_reserve_amount
 * @property string $store_account_po_wsc
 * @property string $store_account_credit_note_amount
 * @property string $store_account_last_modified
 */
class StoreAccountBalanceBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreAccountBalanceBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_account_balance';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id', 'numerical', 'integerOnly'=>true),
			array('user_role', 'length', 'max'=>16),
			array('store_account_balance_currency', 'length', 'max'=>3),
			array('store_account_balance_amount, store_account_reserve_amount, store_account_po_wsc, store_account_credit_note_amount', 'length', 'max'=>15),
			array('store_account_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('user_id, user_role, store_account_balance_currency, store_account_balance_amount, store_account_reserve_amount, store_account_po_wsc, store_account_credit_note_amount, store_account_last_modified', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'user_id' => 'User',
			'user_role' => 'User Role',
			'store_account_balance_currency' => 'Store Account Balance Currency',
			'store_account_balance_amount' => 'Store Account Balance Amount',
			'store_account_reserve_amount' => 'Store Account Reserve Amount',
			'store_account_po_wsc' => 'Store Account Po Wsc',
			'store_account_credit_note_amount' => 'Store Account Credit Note Amount',
			'store_account_last_modified' => 'Store Account Last Modified',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_role',$this->user_role,true);
		$criteria->compare('store_account_balance_currency',$this->store_account_balance_currency,true);
		$criteria->compare('store_account_balance_amount',$this->store_account_balance_amount,true);
		$criteria->compare('store_account_reserve_amount',$this->store_account_reserve_amount,true);
		$criteria->compare('store_account_po_wsc',$this->store_account_po_wsc,true);
		$criteria->compare('store_account_credit_note_amount',$this->store_account_credit_note_amount,true);
		$criteria->compare('store_account_last_modified',$this->store_account_last_modified,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}