<?php

/**
 * This is the model class for table "store_account_comments".
 *
 * The followings are the available columns in table 'store_account_comments':
 * @property integer $store_account_comments_id
 * @property integer $store_account_history_id
 * @property string $store_account_comments_date_added
 * @property integer $store_account_comments_notified
 * @property string $store_account_comments
 * @property string $store_account_comments_added_by
 */
class StoreAccountCommentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreAccountCommentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_account_comments';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_account_comments', 'required'),
			array('store_account_history_id, store_account_comments_notified', 'numerical', 'integerOnly'=>true),
			array('store_account_comments_added_by', 'length', 'max'=>255),
			array('store_account_comments_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_account_comments_id, store_account_history_id, store_account_comments_date_added, store_account_comments_notified, store_account_comments, store_account_comments_added_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_account_comments_id' => 'Store Account Comments',
			'store_account_history_id' => 'Store Account History',
			'store_account_comments_date_added' => 'Store Account Comments Date Added',
			'store_account_comments_notified' => 'Store Account Comments Notified',
			'store_account_comments' => 'Store Account Comments',
			'store_account_comments_added_by' => 'Store Account Comments Added By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_account_comments_id',$this->store_account_comments_id);
		$criteria->compare('store_account_history_id',$this->store_account_history_id);
		$criteria->compare('store_account_comments_date_added',$this->store_account_comments_date_added,true);
		$criteria->compare('store_account_comments_notified',$this->store_account_comments_notified);
		$criteria->compare('store_account_comments',$this->store_account_comments,true);
		$criteria->compare('store_account_comments_added_by',$this->store_account_comments_added_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}