<?php

/**
 * This is the model class for table "store_account_history".
 *
 * The followings are the available columns in table 'store_account_history':
 * @property integer $store_account_history_id
 * @property integer $user_id
 * @property string $user_role
 * @property string $store_account_history_date
 * @property integer $store_account_transaction_reserved
 * @property string $store_account_history_currency
 * @property string $store_account_history_account_type
 * @property string $store_account_history_debit_amount
 * @property string $store_account_history_credit_amount
 * @property string $store_account_history_after_balance
 * @property string $store_account_history_trans_type
 * @property string $store_account_history_trans_id
 * @property string $store_account_history_activity_title
 * @property string $store_account_history_activity_desc
 * @property integer $store_account_history_activity_desc_show
 * @property string $store_account_history_added_by
 * @property string $store_account_history_added_by_role
 * @property string $store_account_history_admin_messages
 */
class StoreAccountHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreAccountHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_account_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_account_history_activity_desc, store_account_history_admin_messages', 'required'),
			array('user_id, store_account_transaction_reserved, store_account_history_activity_desc_show', 'numerical', 'integerOnly'=>true),
			array('user_role, store_account_history_added_by_role', 'length', 'max'=>16),
			array('store_account_history_currency', 'length', 'max'=>3),
			array('store_account_history_account_type', 'length', 'max'=>5),
			array('store_account_history_debit_amount, store_account_history_credit_amount, store_account_history_after_balance', 'length', 'max'=>15),
			array('store_account_history_trans_type', 'length', 'max'=>10),
			array('store_account_history_trans_id', 'length', 'max'=>255),
			array('store_account_history_activity_title, store_account_history_added_by', 'length', 'max'=>128),
			array('store_account_history_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_account_history_id, user_id, user_role, store_account_history_date, store_account_transaction_reserved, store_account_history_currency, store_account_history_account_type, store_account_history_debit_amount, store_account_history_credit_amount, store_account_history_after_balance, store_account_history_trans_type, store_account_history_trans_id, store_account_history_activity_title, store_account_history_activity_desc, store_account_history_activity_desc_show, store_account_history_added_by, store_account_history_added_by_role, store_account_history_admin_messages', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_account_history_id' => 'Store Account History',
			'user_id' => 'User',
			'user_role' => 'User Role',
			'store_account_history_date' => 'Store Account History Date',
			'store_account_transaction_reserved' => 'Store Account Transaction Reserved',
			'store_account_history_currency' => 'Store Account History Currency',
			'store_account_history_account_type' => 'Store Account History Account Type',
			'store_account_history_debit_amount' => 'Store Account History Debit Amount',
			'store_account_history_credit_amount' => 'Store Account History Credit Amount',
			'store_account_history_after_balance' => 'Store Account History After Balance',
			'store_account_history_trans_type' => 'Store Account History Trans Type',
			'store_account_history_trans_id' => 'Store Account History Trans',
			'store_account_history_activity_title' => 'Store Account History Activity Title',
			'store_account_history_activity_desc' => 'Store Account History Activity Desc',
			'store_account_history_activity_desc_show' => 'Store Account History Activity Desc Show',
			'store_account_history_added_by' => 'Store Account History Added By',
			'store_account_history_added_by_role' => 'Store Account History Added By Role',
			'store_account_history_admin_messages' => 'Store Account History Admin Messages',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_account_history_id',$this->store_account_history_id);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_role',$this->user_role,true);
		$criteria->compare('store_account_history_date',$this->store_account_history_date,true);
		$criteria->compare('store_account_transaction_reserved',$this->store_account_transaction_reserved);
		$criteria->compare('store_account_history_currency',$this->store_account_history_currency,true);
		$criteria->compare('store_account_history_account_type',$this->store_account_history_account_type,true);
		$criteria->compare('store_account_history_debit_amount',$this->store_account_history_debit_amount,true);
		$criteria->compare('store_account_history_credit_amount',$this->store_account_history_credit_amount,true);
		$criteria->compare('store_account_history_after_balance',$this->store_account_history_after_balance,true);
		$criteria->compare('store_account_history_trans_type',$this->store_account_history_trans_type,true);
		$criteria->compare('store_account_history_trans_id',$this->store_account_history_trans_id,true);
		$criteria->compare('store_account_history_activity_title',$this->store_account_history_activity_title,true);
		$criteria->compare('store_account_history_activity_desc',$this->store_account_history_activity_desc,true);
		$criteria->compare('store_account_history_activity_desc_show',$this->store_account_history_activity_desc_show);
		$criteria->compare('store_account_history_added_by',$this->store_account_history_added_by,true);
		$criteria->compare('store_account_history_added_by_role',$this->store_account_history_added_by_role,true);
		$criteria->compare('store_account_history_admin_messages',$this->store_account_history_admin_messages,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}