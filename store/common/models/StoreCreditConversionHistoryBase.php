<?php

/**
 * This is the model class for table "store_credit_conversion_history".
 *
 * The followings are the available columns in table 'store_credit_conversion_history':
 * @property integer $store_credit_conversion_history_id
 * @property integer $customer_id
 * @property string $store_credit_conversion_history_date
 * @property string $store_credit_account_type
 * @property integer $store_credit_conversion_from_currency_id
 * @property string $store_credit_conversion_from_amount
 * @property string $store_credit_conversion_from_reserve_amount
 * @property integer $store_credit_conversion_to_currency_id
 * @property string $store_credit_conversion_to_amount
 * @property string $store_credit_conversion_to_reserve_amount
 * @property string $store_credit_conversion_currency_values
 */
class StoreCreditConversionHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreCreditConversionHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_credit_conversion_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customer_id, store_credit_conversion_from_currency_id, store_credit_conversion_to_currency_id', 'numerical', 'integerOnly'=>true),
			array('store_credit_account_type', 'length', 'max'=>4),
			array('store_credit_conversion_from_amount, store_credit_conversion_from_reserve_amount, store_credit_conversion_to_amount, store_credit_conversion_to_reserve_amount', 'length', 'max'=>15),
			array('store_credit_conversion_currency_values', 'length', 'max'=>128),
			array('store_credit_conversion_history_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_credit_conversion_history_id, customer_id, store_credit_conversion_history_date, store_credit_account_type, store_credit_conversion_from_currency_id, store_credit_conversion_from_amount, store_credit_conversion_from_reserve_amount, store_credit_conversion_to_currency_id, store_credit_conversion_to_amount, store_credit_conversion_to_reserve_amount, store_credit_conversion_currency_values', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_credit_conversion_history_id' => 'Store Credit Conversion History',
			'customer_id' => 'Customer',
			'store_credit_conversion_history_date' => 'Store Credit Conversion History Date',
			'store_credit_account_type' => 'Store Credit Account Type',
			'store_credit_conversion_from_currency_id' => 'Store Credit Conversion From Currency',
			'store_credit_conversion_from_amount' => 'Store Credit Conversion From Amount',
			'store_credit_conversion_from_reserve_amount' => 'Store Credit Conversion From Reserve Amount',
			'store_credit_conversion_to_currency_id' => 'Store Credit Conversion To Currency',
			'store_credit_conversion_to_amount' => 'Store Credit Conversion To Amount',
			'store_credit_conversion_to_reserve_amount' => 'Store Credit Conversion To Reserve Amount',
			'store_credit_conversion_currency_values' => 'Store Credit Conversion Currency Values',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_credit_conversion_history_id',$this->store_credit_conversion_history_id);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('store_credit_conversion_history_date',$this->store_credit_conversion_history_date,true);
		$criteria->compare('store_credit_account_type',$this->store_credit_account_type,true);
		$criteria->compare('store_credit_conversion_from_currency_id',$this->store_credit_conversion_from_currency_id);
		$criteria->compare('store_credit_conversion_from_amount',$this->store_credit_conversion_from_amount,true);
		$criteria->compare('store_credit_conversion_from_reserve_amount',$this->store_credit_conversion_from_reserve_amount,true);
		$criteria->compare('store_credit_conversion_to_currency_id',$this->store_credit_conversion_to_currency_id);
		$criteria->compare('store_credit_conversion_to_amount',$this->store_credit_conversion_to_amount,true);
		$criteria->compare('store_credit_conversion_to_reserve_amount',$this->store_credit_conversion_to_reserve_amount,true);
		$criteria->compare('store_credit_conversion_currency_values',$this->store_credit_conversion_currency_values,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}