<?php

/**
 * This is the model class for table "store_credit_daily_history".
 *
 * The followings are the available columns in table 'store_credit_daily_history':
 * @property string $store_credit_daily_history_date
 * @property string $store_credit_daily_history_currency
 * @property string $store_credit_daily_history_credit_type
 * @property string $store_credit_daily_history_amount
 * @property string $store_credit_daily_history_reserved_amount
 * @property string $user_id
 * @property string $user_role
 */
class StoreCreditDailyHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreCreditDailyHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_credit_daily_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_credit_daily_history_currency', 'length', 'max'=>3),
			array('store_credit_daily_history_credit_type', 'length', 'max'=>4),
			array('store_credit_daily_history_amount, store_credit_daily_history_reserved_amount', 'length', 'max'=>15),
			array('user_id', 'length', 'max'=>32),
			array('user_role', 'length', 'max'=>16),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_credit_daily_history_date, store_credit_daily_history_currency, store_credit_daily_history_credit_type, store_credit_daily_history_amount, store_credit_daily_history_reserved_amount, user_id, user_role', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_credit_daily_history_date' => 'Store Credit Daily History Date',
			'store_credit_daily_history_currency' => 'Store Credit Daily History Currency',
			'store_credit_daily_history_credit_type' => 'Store Credit Daily History Credit Type',
			'store_credit_daily_history_amount' => 'Store Credit Daily History Amount',
			'store_credit_daily_history_reserved_amount' => 'Store Credit Daily History Reserved Amount',
			'user_id' => 'User',
			'user_role' => 'User Role',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_credit_daily_history_date',$this->store_credit_daily_history_date,true);
		$criteria->compare('store_credit_daily_history_currency',$this->store_credit_daily_history_currency,true);
		$criteria->compare('store_credit_daily_history_credit_type',$this->store_credit_daily_history_credit_type,true);
		$criteria->compare('store_credit_daily_history_amount',$this->store_credit_daily_history_amount,true);
		$criteria->compare('store_credit_daily_history_reserved_amount',$this->store_credit_daily_history_reserved_amount,true);
		$criteria->compare('user_id',$this->user_id,true);
		$criteria->compare('user_role',$this->user_role,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}