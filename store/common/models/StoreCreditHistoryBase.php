<?php

/**
 * This is the model class for table "store_credit_history".
 *
 * The followings are the available columns in table 'store_credit_history':
 * @property integer $store_credit_history_id
 * @property integer $customer_id
 * @property string $store_credit_account_type
 * @property string $store_credit_history_date
 * @property integer $store_credit_transaction_reserved
 * @property integer $store_credit_history_currency_id
 * @property string $store_credit_history_debit_amount
 * @property string $store_credit_history_credit_amount
 * @property string $store_credit_history_r_after_balance
 * @property string $store_credit_history_nr_after_balance
 * @property string $store_credit_history_trans_type
 * @property string $store_credit_history_trans_id
 * @property string $store_credit_activity_type
 * @property string $store_credit_history_activity_title
 * @property string $store_credit_history_activity_desc
 * @property integer $store_credit_history_activity_desc_show
 * @property string $store_credit_history_added_by
 * @property string $store_credit_history_added_by_role
 * @property string $store_credit_history_admin_messages
 */
class StoreCreditHistoryBase extends MainModel
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return StoreCreditHistoryBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'store_credit_history';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('store_credit_history_activity_desc', 'required'),
            array(
                'customer_id, store_credit_transaction_reserved, store_credit_history_currency_id, store_credit_history_activity_desc_show',
                'numerical',
                'integerOnly' => true
            ),
            array('store_credit_account_type', 'length', 'max' => 3),
            array(
                'store_credit_history_debit_amount, store_credit_history_credit_amount, store_credit_history_r_after_balance, store_credit_history_nr_after_balance',
                'length',
                'max' => 15
            ),
            array('store_credit_history_trans_type', 'length', 'max' => 10),
            array('store_credit_history_trans_id', 'length', 'max' => 255),
            array('store_credit_activity_type', 'length', 'max' => 2),
            array('store_credit_history_activity_title, store_credit_history_added_by', 'length', 'max' => 128),
            array('store_credit_history_added_by_role', 'length', 'max' => 16),
            array('store_credit_history_date', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array(
                'store_credit_history_id, customer_id, store_credit_account_type, store_credit_history_date, store_credit_transaction_reserved, store_credit_history_currency_id, store_credit_history_debit_amount, store_credit_history_credit_amount, store_credit_history_r_after_balance, store_credit_history_nr_after_balance, store_credit_history_trans_type, store_credit_history_trans_id, store_credit_activity_type, store_credit_history_activity_title, store_credit_history_activity_desc, store_credit_history_activity_desc_show, store_credit_history_added_by, store_credit_history_added_by_role, store_credit_history_admin_messages',
                'safe',
                'on' => 'search'
            ),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'store_credit_history_id' => 'Store Credit History',
            'customer_id' => 'Customer',
            'store_credit_account_type' => 'Store Credit Account Type',
            'store_credit_history_date' => 'Store Credit History Date',
            'store_credit_transaction_reserved' => 'Store Credit Transaction Reserved',
            'store_credit_history_currency_id' => 'Store Credit History Currency',
            'store_credit_history_debit_amount' => 'Store Credit History Debit Amount',
            'store_credit_history_credit_amount' => 'Store Credit History Credit Amount',
            'store_credit_history_r_after_balance' => 'Store Credit History R After Balance',
            'store_credit_history_nr_after_balance' => 'Store Credit History Nr After Balance',
            'store_credit_history_trans_type' => 'Store Credit History Trans Type',
            'store_credit_history_trans_id' => 'Store Credit History Trans',
            'store_credit_activity_type' => 'Store Credit Activity Type',
            'store_credit_history_activity_title' => 'Store Credit History Activity Title',
            'store_credit_history_activity_desc' => 'Store Credit History Activity Desc',
            'store_credit_history_activity_desc_show' => 'Store Credit History Activity Desc Show',
            'store_credit_history_added_by' => 'Store Credit History Added By',
            'store_credit_history_added_by_role' => 'Store Credit History Added By Role',
            'store_credit_history_admin_messages' => 'Store Credit History Admin Messages',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('store_credit_history_id', $this->store_credit_history_id);
        $criteria->compare('customer_id', $this->customer_id);
        $criteria->compare('store_credit_account_type', $this->store_credit_account_type, true);
        $criteria->compare('store_credit_history_date', $this->store_credit_history_date, true);
        $criteria->compare('store_credit_transaction_reserved', $this->store_credit_transaction_reserved);
        $criteria->compare('store_credit_history_currency_id', $this->store_credit_history_currency_id);
        $criteria->compare('store_credit_history_debit_amount', $this->store_credit_history_debit_amount, true);
        $criteria->compare('store_credit_history_credit_amount', $this->store_credit_history_credit_amount, true);
        $criteria->compare('store_credit_history_r_after_balance', $this->store_credit_history_r_after_balance, true);
        $criteria->compare('store_credit_history_nr_after_balance', $this->store_credit_history_nr_after_balance, true);
        $criteria->compare('store_credit_history_trans_type', $this->store_credit_history_trans_type, true);
        $criteria->compare('store_credit_history_trans_id', $this->store_credit_history_trans_id, true);
        $criteria->compare('store_credit_activity_type', $this->store_credit_activity_type, true);
        $criteria->compare('store_credit_history_activity_title', $this->store_credit_history_activity_title, true);
        $criteria->compare('store_credit_history_activity_desc', $this->store_credit_history_activity_desc, true);
        $criteria->compare('store_credit_history_activity_desc_show', $this->store_credit_history_activity_desc_show);
        $criteria->compare('store_credit_history_added_by', $this->store_credit_history_added_by, true);
        $criteria->compare('store_credit_history_added_by_role', $this->store_credit_history_added_by_role, true);
        $criteria->compare('store_credit_history_admin_messages', $this->store_credit_history_admin_messages, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function saveInfo($historyInfo)
    {
        $this->customer_id = $historyInfo['customer_id'];
        $this->store_credit_account_type = $historyInfo['store_credit_account_type'];
        $this->store_credit_history_date = $historyInfo['store_credit_history_date'];
        $this->store_credit_history_currency_id = $historyInfo['store_credit_history_currency_id'];
        $this->store_credit_history_debit_amount = $historyInfo['store_credit_history_debit_amount'];
        $this->store_credit_history_credit_amount = $historyInfo['store_credit_history_credit_amount'];
        $this->store_credit_history_r_after_balance = $historyInfo['store_credit_history_r_after_balance'];
        $this->store_credit_history_nr_after_balance = $historyInfo['store_credit_history_nr_after_balance'];
        $this->store_credit_history_trans_type = $historyInfo['store_credit_history_trans_type'];
        $this->store_credit_history_trans_id = $historyInfo['store_credit_history_trans_id'];
        $this->store_credit_activity_type = $historyInfo['store_credit_activity_type'];
        $this->store_credit_history_activity_title = $historyInfo['store_credit_history_activity_title'];
        $this->store_credit_history_activity_desc = $historyInfo['store_credit_history_activity_desc'];
        $this->store_credit_history_activity_desc_show = $historyInfo['store_credit_history_activity_desc_show'];
        $this->store_credit_history_added_by = $historyInfo['store_credit_history_added_by'];
        $this->store_credit_history_added_by_role = $historyInfo['store_credit_history_added_by_role'];
        $this->save();

        $latestKey = $this->primaryKey;
        return $latestKey;
    }
}