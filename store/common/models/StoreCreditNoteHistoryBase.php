<?php

/**
 * This is the model class for table "store_credit_note_history".
 *
 * The followings are the available columns in table 'store_credit_note_history':
 * @property string $store_credit_note_history_id
 * @property integer $customer_id
 * @property string $store_credit_note_history_date
 * @property string $store_credit_note_history_currency
 * @property string $store_credit_note_history_debit_amount
 * @property string $store_credit_note_history_credit_amount
 * @property string $store_credit_note_history_after_balance
 * @property string $store_credit_note_history_trans_type
 * @property string $store_credit_note_history_trans_id
 * @property string $store_credit_note_history_activity_type
 * @property string $store_credit_note_history_activity_title
 * @property string $store_credit_note_history_added_by
 * @property string $store_credit_note_history_added_by_role
 */
class StoreCreditNoteHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StoreCreditNoteHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_credit_note_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customer_id', 'numerical', 'integerOnly'=>true),
			array('store_credit_note_history_currency', 'length', 'max'=>3),
			array('store_credit_note_history_debit_amount, store_credit_note_history_credit_amount, store_credit_note_history_after_balance', 'length', 'max'=>20),
			array('store_credit_note_history_trans_type', 'length', 'max'=>10),
			array('store_credit_note_history_trans_id', 'length', 'max'=>255),
			array('store_credit_note_history_activity_type', 'length', 'max'=>2),
			array('store_credit_note_history_activity_title, store_credit_note_history_added_by', 'length', 'max'=>128),
			array('store_credit_note_history_added_by_role', 'length', 'max'=>16),
			array('store_credit_note_history_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_credit_note_history_id, customer_id, store_credit_note_history_date, store_credit_note_history_currency, store_credit_note_history_debit_amount, store_credit_note_history_credit_amount, store_credit_note_history_after_balance, store_credit_note_history_trans_type, store_credit_note_history_trans_id, store_credit_note_history_activity_type, store_credit_note_history_activity_title, store_credit_note_history_added_by, store_credit_note_history_added_by_role', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_credit_note_history_id' => 'Store Credit Note History',
			'customer_id' => 'Customer',
			'store_credit_note_history_date' => 'Store Credit Note History Date',
			'store_credit_note_history_currency' => 'Store Credit Note History Currency',
			'store_credit_note_history_debit_amount' => 'Store Credit Note History Debit Amount',
			'store_credit_note_history_credit_amount' => 'Store Credit Note History Credit Amount',
			'store_credit_note_history_after_balance' => 'Store Credit Note History After Balance',
			'store_credit_note_history_trans_type' => 'Store Credit Note History Trans Type',
			'store_credit_note_history_trans_id' => 'Store Credit Note History Trans',
			'store_credit_note_history_activity_type' => 'Store Credit Note History Activity Type',
			'store_credit_note_history_activity_title' => 'Store Credit Note History Activity Title',
			'store_credit_note_history_added_by' => 'Store Credit Note History Added By',
			'store_credit_note_history_added_by_role' => 'Store Credit Note History Added By Role',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_credit_note_history_id',$this->store_credit_note_history_id,true);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('store_credit_note_history_date',$this->store_credit_note_history_date,true);
		$criteria->compare('store_credit_note_history_currency',$this->store_credit_note_history_currency,true);
		$criteria->compare('store_credit_note_history_debit_amount',$this->store_credit_note_history_debit_amount,true);
		$criteria->compare('store_credit_note_history_credit_amount',$this->store_credit_note_history_credit_amount,true);
		$criteria->compare('store_credit_note_history_after_balance',$this->store_credit_note_history_after_balance,true);
		$criteria->compare('store_credit_note_history_trans_type',$this->store_credit_note_history_trans_type,true);
		$criteria->compare('store_credit_note_history_trans_id',$this->store_credit_note_history_trans_id,true);
		$criteria->compare('store_credit_note_history_activity_type',$this->store_credit_note_history_activity_type,true);
		$criteria->compare('store_credit_note_history_activity_title',$this->store_credit_note_history_activity_title,true);
		$criteria->compare('store_credit_note_history_added_by',$this->store_credit_note_history_added_by,true);
		$criteria->compare('store_credit_note_history_added_by_role',$this->store_credit_note_history_added_by_role,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}