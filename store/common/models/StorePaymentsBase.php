<?php

/**
 * This is the model class for table "store_payments".
 *
 * The followings are the available columns in table 'store_payments':
 * @property integer $store_payments_id
 * @property integer $user_id
 * @property string $user_role
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $user_country_international_dialing_code
 * @property string $user_telephone
 * @property string $user_mobile
 * @property string $store_payments_date
 * @property integer $store_payments_status
 * @property string $store_payments_request_currency
 * @property string $store_payments_request_amount
 * @property string $store_payments_fees
 * @property string $store_payments_after_fees_amount
 * @property string $store_payments_paid_currency
 * @property string $store_payments_paid_currency_value
 * @property string $store_payments_paid_amount
 * @property string $store_payments_reference
 * @property integer $store_payments_methods_id
 * @property string $store_payments_methods_name
 * @property integer $store_payment_account_book_id
 * @property string $user_payment_methods_alias
 * @property string $store_payments_fees_calculation
 * @property string $store_payments_last_modified
 * @property integer $store_payments_read_mode
 * @property integer $store_payments_lock
 */
class StorePaymentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_payments';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id, store_payments_status, store_payments_methods_id, store_payment_account_book_id, store_payments_read_mode, store_payments_lock', 'numerical', 'integerOnly'=>true),
			array('user_role', 'length', 'max'=>16),
			array('user_firstname, user_lastname, user_telephone, user_mobile, store_payments_reference, user_payment_methods_alias', 'length', 'max'=>32),
			array('user_email_address', 'length', 'max'=>96),
			array('user_country_international_dialing_code', 'length', 'max'=>5),
			array('store_payments_request_currency, store_payments_paid_currency', 'length', 'max'=>3),
			array('store_payments_request_amount, store_payments_fees, store_payments_after_fees_amount, store_payments_paid_amount', 'length', 'max'=>15),
			array('store_payments_paid_currency_value', 'length', 'max'=>14),
			array('store_payments_methods_name', 'length', 'max'=>255),
			array('store_payments_date, store_payments_fees_calculation, store_payments_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_payments_id, user_id, user_role, user_firstname, user_lastname, user_email_address, user_country_international_dialing_code, user_telephone, user_mobile, store_payments_date, store_payments_status, store_payments_request_currency, store_payments_request_amount, store_payments_fees, store_payments_after_fees_amount, store_payments_paid_currency, store_payments_paid_currency_value, store_payments_paid_amount, store_payments_reference, store_payments_methods_id, store_payments_methods_name, store_payment_account_book_id, user_payment_methods_alias, store_payments_fees_calculation, store_payments_last_modified, store_payments_read_mode, store_payments_lock', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_payments_id' => 'Store Payments',
			'user_id' => 'User',
			'user_role' => 'User Role',
			'user_firstname' => 'User Firstname',
			'user_lastname' => 'User Lastname',
			'user_email_address' => 'User Email Address',
			'user_country_international_dialing_code' => 'User Country International Dialing Code',
			'user_telephone' => 'User Telephone',
			'user_mobile' => 'User Mobile',
			'store_payments_date' => 'Store Payments Date',
			'store_payments_status' => 'Store Payments Status',
			'store_payments_request_currency' => 'Store Payments Request Currency',
			'store_payments_request_amount' => 'Store Payments Request Amount',
			'store_payments_fees' => 'Store Payments Fees',
			'store_payments_after_fees_amount' => 'Store Payments After Fees Amount',
			'store_payments_paid_currency' => 'Store Payments Paid Currency',
			'store_payments_paid_currency_value' => 'Store Payments Paid Currency Value',
			'store_payments_paid_amount' => 'Store Payments Paid Amount',
			'store_payments_reference' => 'Store Payments Reference',
			'store_payments_methods_id' => 'Store Payments Methods',
			'store_payments_methods_name' => 'Store Payments Methods Name',
			'store_payment_account_book_id' => 'Store Payment Account Book',
			'user_payment_methods_alias' => 'User Payment Methods Alias',
			'store_payments_fees_calculation' => 'Store Payments Fees Calculation',
			'store_payments_last_modified' => 'Store Payments Last Modified',
			'store_payments_read_mode' => 'Store Payments Read Mode',
			'store_payments_lock' => 'Store Payments Lock',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_payments_id',$this->store_payments_id);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_role',$this->user_role,true);
		$criteria->compare('user_firstname',$this->user_firstname,true);
		$criteria->compare('user_lastname',$this->user_lastname,true);
		$criteria->compare('user_email_address',$this->user_email_address,true);
		$criteria->compare('user_country_international_dialing_code',$this->user_country_international_dialing_code,true);
		$criteria->compare('user_telephone',$this->user_telephone,true);
		$criteria->compare('user_mobile',$this->user_mobile,true);
		$criteria->compare('store_payments_date',$this->store_payments_date,true);
		$criteria->compare('store_payments_status',$this->store_payments_status);
		$criteria->compare('store_payments_request_currency',$this->store_payments_request_currency,true);
		$criteria->compare('store_payments_request_amount',$this->store_payments_request_amount,true);
		$criteria->compare('store_payments_fees',$this->store_payments_fees,true);
		$criteria->compare('store_payments_after_fees_amount',$this->store_payments_after_fees_amount,true);
		$criteria->compare('store_payments_paid_currency',$this->store_payments_paid_currency,true);
		$criteria->compare('store_payments_paid_currency_value',$this->store_payments_paid_currency_value,true);
		$criteria->compare('store_payments_paid_amount',$this->store_payments_paid_amount,true);
		$criteria->compare('store_payments_reference',$this->store_payments_reference,true);
		$criteria->compare('store_payments_methods_id',$this->store_payments_methods_id);
		$criteria->compare('store_payments_methods_name',$this->store_payments_methods_name,true);
		$criteria->compare('store_payment_account_book_id',$this->store_payment_account_book_id);
		$criteria->compare('user_payment_methods_alias',$this->user_payment_methods_alias,true);
		$criteria->compare('store_payments_fees_calculation',$this->store_payments_fees_calculation,true);
		$criteria->compare('store_payments_last_modified',$this->store_payments_last_modified,true);
		$criteria->compare('store_payments_read_mode',$this->store_payments_read_mode);
		$criteria->compare('store_payments_lock',$this->store_payments_lock);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}