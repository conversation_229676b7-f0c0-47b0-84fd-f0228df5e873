<?php

/**
 * This is the model class for table "store_payments_details".
 *
 * The followings are the available columns in table 'store_payments_details':
 * @property integer $store_payments_id
 * @property integer $payment_methods_fields_id
 * @property string $payment_methods_fields_title
 * @property string $payment_methods_fields_value
 * @property integer $payment_methods_fields_sort_order
 */
class StorePaymentsDetailsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsDetailsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_payments_details';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_payments_id, payment_methods_fields_id, payment_methods_fields_sort_order', 'numerical', 'integerOnly'=>true),
			array('payment_methods_fields_title', 'length', 'max'=>255),
			array('payment_methods_fields_value', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_payments_id, payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_value, payment_methods_fields_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_payments_id' => 'Store Payments',
			'payment_methods_fields_id' => 'Payment Methods Fields',
			'payment_methods_fields_title' => 'Payment Methods Fields Title',
			'payment_methods_fields_value' => 'Payment Methods Fields Value',
			'payment_methods_fields_sort_order' => 'Payment Methods Fields Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_payments_id',$this->store_payments_id);
		$criteria->compare('payment_methods_fields_id',$this->payment_methods_fields_id);
		$criteria->compare('payment_methods_fields_title',$this->payment_methods_fields_title,true);
		$criteria->compare('payment_methods_fields_value',$this->payment_methods_fields_value,true);
		$criteria->compare('payment_methods_fields_sort_order',$this->payment_methods_fields_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}