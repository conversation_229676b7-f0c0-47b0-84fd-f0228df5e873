<?php

/**
 * This is the model class for table "store_payments_status".
 *
 * The followings are the available columns in table 'store_payments_status':
 * @property integer $store_payments_status_id
 * @property integer $language_id
 * @property string $store_payments_status_name
 * @property integer $store_payments_status_sort_order
 */
class StorePaymentsStatusBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsStatusBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_payments_status';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_payments_status_id, language_id, store_payments_status_sort_order', 'numerical', 'integerOnly'=>true),
			array('store_payments_status_name', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_payments_status_id, language_id, store_payments_status_name, store_payments_status_sort_order', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_payments_status_id' => 'Store Payments Status',
			'language_id' => 'Language',
			'store_payments_status_name' => 'Store Payments Status Name',
			'store_payments_status_sort_order' => 'Store Payments Status Sort Order',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_payments_status_id',$this->store_payments_status_id);
		$criteria->compare('language_id',$this->language_id);
		$criteria->compare('store_payments_status_name',$this->store_payments_status_name,true);
		$criteria->compare('store_payments_status_sort_order',$this->store_payments_status_sort_order);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}