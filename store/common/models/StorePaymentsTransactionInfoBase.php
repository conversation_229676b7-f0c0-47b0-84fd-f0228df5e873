<?php

/**
 * This is the model class for table "store_payments_transaction_info".
 *
 * The followings are the available columns in table 'store_payments_transaction_info':
 * @property string $store_payments_transaction_id
 * @property string $store_payments_id
 * @property string $store_payments_reimburse_id
 * @property string $store_payments_reimburse_table
 * @property string $store_payments_reimburse_amount
 * @property string $store_payments_reimburse_currency
 */
class StorePaymentsTransactionInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsTransactionInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_payments_transaction_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_payments_id, store_payments_reimburse_id', 'length', 'max'=>11),
			array('store_payments_reimburse_table', 'length', 'max'=>255),
			array('store_payments_reimburse_amount', 'length', 'max'=>15),
			array('store_payments_reimburse_currency', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_payments_transaction_id, store_payments_id, store_payments_reimburse_id, store_payments_reimburse_table, store_payments_reimburse_amount, store_payments_reimburse_currency', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_payments_transaction_id' => 'Store Payments Transaction',
			'store_payments_id' => 'Store Payments',
			'store_payments_reimburse_id' => 'Store Payments Reimburse',
			'store_payments_reimburse_table' => 'Store Payments Reimburse Table',
			'store_payments_reimburse_amount' => 'Store Payments Reimburse Amount',
			'store_payments_reimburse_currency' => 'Store Payments Reimburse Currency',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_payments_transaction_id',$this->store_payments_transaction_id,true);
		$criteria->compare('store_payments_id',$this->store_payments_id,true);
		$criteria->compare('store_payments_reimburse_id',$this->store_payments_reimburse_id,true);
		$criteria->compare('store_payments_reimburse_table',$this->store_payments_reimburse_table,true);
		$criteria->compare('store_payments_reimburse_amount',$this->store_payments_reimburse_amount,true);
		$criteria->compare('store_payments_reimburse_currency',$this->store_payments_reimburse_currency,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}