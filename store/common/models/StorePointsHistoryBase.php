<?php

/**
 * This is the model class for table "store_points_history".
 *
 * The followings are the available columns in table 'store_points_history':
 * @property integer $store_points_history_id
 * @property integer $customer_id
 * @property string $store_points_history_date
 * @property string $store_points_history_debit_amount
 * @property string $store_points_history_credit_amount
 * @property string $store_points_history_after_balance
 * @property string $store_points_history_trans_type
 * @property string $store_points_history_trans_id
 * @property string $store_points_history_activity_type
 * @property string $store_points_history_activity_title
 * @property string $store_points_history_activity_desc
 * @property integer $store_points_history_activity_desc_show
 * @property string $store_points_history_added_by
 * @property string $store_points_history_added_by_role
 * @property string $store_points_history_admin_messages
 */
class StorePointsHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePointsHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_points_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_points_history_activity_desc, store_points_history_admin_messages', 'required'),
			array('customer_id, store_points_history_activity_desc_show', 'numerical', 'integerOnly'=>true),
			array('store_points_history_debit_amount, store_points_history_credit_amount, store_points_history_after_balance', 'length', 'max'=>15),
			array('store_points_history_trans_type', 'length', 'max'=>10),
			array('store_points_history_trans_id', 'length', 'max'=>255),
			array('store_points_history_activity_type', 'length', 'max'=>2),
			array('store_points_history_activity_title, store_points_history_added_by', 'length', 'max'=>128),
			array('store_points_history_added_by_role', 'length', 'max'=>16),
			array('store_points_history_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_points_history_id, customer_id, store_points_history_date, store_points_history_debit_amount, store_points_history_credit_amount, store_points_history_after_balance, store_points_history_trans_type, store_points_history_trans_id, store_points_history_activity_type, store_points_history_activity_title, store_points_history_activity_desc, store_points_history_activity_desc_show, store_points_history_added_by, store_points_history_added_by_role, store_points_history_admin_messages', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_points_history_id' => 'Store Points History',
			'customer_id' => 'Customer',
			'store_points_history_date' => 'Store Points History Date',
			'store_points_history_debit_amount' => 'Store Points History Debit Amount',
			'store_points_history_credit_amount' => 'Store Points History Credit Amount',
			'store_points_history_after_balance' => 'Store Points History After Balance',
			'store_points_history_trans_type' => 'Store Points History Trans Type',
			'store_points_history_trans_id' => 'Store Points History Trans',
			'store_points_history_activity_type' => 'Store Points History Activity Type',
			'store_points_history_activity_title' => 'Store Points History Activity Title',
			'store_points_history_activity_desc' => 'Store Points History Activity Desc',
			'store_points_history_activity_desc_show' => 'Store Points History Activity Desc Show',
			'store_points_history_added_by' => 'Store Points History Added By',
			'store_points_history_added_by_role' => 'Store Points History Added By Role',
			'store_points_history_admin_messages' => 'Store Points History Admin Messages',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_points_history_id',$this->store_points_history_id);
		$criteria->compare('customer_id',$this->customer_id);
		$criteria->compare('store_points_history_date',$this->store_points_history_date,true);
		$criteria->compare('store_points_history_debit_amount',$this->store_points_history_debit_amount,true);
		$criteria->compare('store_points_history_credit_amount',$this->store_points_history_credit_amount,true);
		$criteria->compare('store_points_history_after_balance',$this->store_points_history_after_balance,true);
		$criteria->compare('store_points_history_trans_type',$this->store_points_history_trans_type,true);
		$criteria->compare('store_points_history_trans_id',$this->store_points_history_trans_id,true);
		$criteria->compare('store_points_history_activity_type',$this->store_points_history_activity_type,true);
		$criteria->compare('store_points_history_activity_title',$this->store_points_history_activity_title,true);
		$criteria->compare('store_points_history_activity_desc',$this->store_points_history_activity_desc,true);
		$criteria->compare('store_points_history_activity_desc_show',$this->store_points_history_activity_desc_show);
		$criteria->compare('store_points_history_added_by',$this->store_points_history_added_by,true);
		$criteria->compare('store_points_history_added_by_role',$this->store_points_history_added_by_role,true);
		$criteria->compare('store_points_history_admin_messages',$this->store_points_history_admin_messages,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}