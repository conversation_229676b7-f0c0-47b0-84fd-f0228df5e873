<?php

/**
 * This is the model class for table "store_points_redeem".
 *
 * The followings are the available columns in table 'store_points_redeem':
 * @property integer $store_points_redeem_id
 * @property integer $user_id
 * @property string $user_role
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $user_country_international_dialing_code
 * @property string $user_telephone
 * @property string $user_mobile
 * @property string $store_points_redeem_date
 * @property integer $store_points_redeem_status
 * @property string $store_points_redeem_amount
 * @property string $store_points_request_currency
 * @property string $store_points_request_currency_amount
 * @property string $store_points_paid_amount
 * @property string $store_points_paid_currency
 * @property string $store_points_paid_currency_amount
 * @property string $store_points_exchange_rate
 * @property string $store_points_redeem_reference
 * @property string $store_points_redeem_last_modified
 * @property integer $store_points_read_mode
 */
class StorePointsRedeemBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePointsRedeemBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_points_redeem';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id, store_points_redeem_status, store_points_read_mode', 'numerical', 'integerOnly'=>true),
			array('user_role', 'length', 'max'=>16),
			array('user_firstname, user_lastname, user_telephone, user_mobile, store_points_redeem_reference', 'length', 'max'=>32),
			array('user_email_address', 'length', 'max'=>96),
			array('user_country_international_dialing_code', 'length', 'max'=>5),
			array('store_points_redeem_amount, store_points_request_currency_amount, store_points_paid_amount, store_points_paid_currency_amount', 'length', 'max'=>15),
			array('store_points_request_currency, store_points_paid_currency', 'length', 'max'=>3),
			array('store_points_exchange_rate', 'length', 'max'=>14),
			array('store_points_redeem_date, store_points_redeem_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_points_redeem_id, user_id, user_role, user_firstname, user_lastname, user_email_address, user_country_international_dialing_code, user_telephone, user_mobile, store_points_redeem_date, store_points_redeem_status, store_points_redeem_amount, store_points_request_currency, store_points_request_currency_amount, store_points_paid_amount, store_points_paid_currency, store_points_paid_currency_amount, store_points_exchange_rate, store_points_redeem_reference, store_points_redeem_last_modified, store_points_read_mode', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_points_redeem_id' => 'Store Points Redeem',
			'user_id' => 'User',
			'user_role' => 'User Role',
			'user_firstname' => 'User Firstname',
			'user_lastname' => 'User Lastname',
			'user_email_address' => 'User Email Address',
			'user_country_international_dialing_code' => 'User Country International Dialing Code',
			'user_telephone' => 'User Telephone',
			'user_mobile' => 'User Mobile',
			'store_points_redeem_date' => 'Store Points Redeem Date',
			'store_points_redeem_status' => 'Store Points Redeem Status',
			'store_points_redeem_amount' => 'Store Points Redeem Amount',
			'store_points_request_currency' => 'Store Points Request Currency',
			'store_points_request_currency_amount' => 'Store Points Request Currency Amount',
			'store_points_paid_amount' => 'Store Points Paid Amount',
			'store_points_paid_currency' => 'Store Points Paid Currency',
			'store_points_paid_currency_amount' => 'Store Points Paid Currency Amount',
			'store_points_exchange_rate' => 'Store Points Exchange Rate',
			'store_points_redeem_reference' => 'Store Points Redeem Reference',
			'store_points_redeem_last_modified' => 'Store Points Redeem Last Modified',
			'store_points_read_mode' => 'Store Points Read Mode',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_points_redeem_id',$this->store_points_redeem_id);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_role',$this->user_role,true);
		$criteria->compare('user_firstname',$this->user_firstname,true);
		$criteria->compare('user_lastname',$this->user_lastname,true);
		$criteria->compare('user_email_address',$this->user_email_address,true);
		$criteria->compare('user_country_international_dialing_code',$this->user_country_international_dialing_code,true);
		$criteria->compare('user_telephone',$this->user_telephone,true);
		$criteria->compare('user_mobile',$this->user_mobile,true);
		$criteria->compare('store_points_redeem_date',$this->store_points_redeem_date,true);
		$criteria->compare('store_points_redeem_status',$this->store_points_redeem_status);
		$criteria->compare('store_points_redeem_amount',$this->store_points_redeem_amount,true);
		$criteria->compare('store_points_request_currency',$this->store_points_request_currency,true);
		$criteria->compare('store_points_request_currency_amount',$this->store_points_request_currency_amount,true);
		$criteria->compare('store_points_paid_amount',$this->store_points_paid_amount,true);
		$criteria->compare('store_points_paid_currency',$this->store_points_paid_currency,true);
		$criteria->compare('store_points_paid_currency_amount',$this->store_points_paid_currency_amount,true);
		$criteria->compare('store_points_exchange_rate',$this->store_points_exchange_rate,true);
		$criteria->compare('store_points_redeem_reference',$this->store_points_redeem_reference,true);
		$criteria->compare('store_points_redeem_last_modified',$this->store_points_redeem_last_modified,true);
		$criteria->compare('store_points_read_mode',$this->store_points_read_mode);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}