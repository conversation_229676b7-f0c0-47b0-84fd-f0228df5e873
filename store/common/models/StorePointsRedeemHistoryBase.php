<?php

/**
 * This is the model class for table "store_points_redeem_history".
 *
 * The followings are the available columns in table 'store_points_redeem_history':
 * @property integer $store_points_redeem_history_id
 * @property integer $store_points_redeem_id
 * @property integer $store_points_redeem_status
 * @property string $date_added
 * @property integer $payee_notified
 * @property string $comments
 * @property string $changed_by
 * @property string $changed_by_role
 */
class StorePointsRedeemHistoryBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePointsRedeemHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'store_points_redeem_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('store_points_redeem_id, store_points_redeem_status, payee_notified', 'numerical', 'integerOnly'=>true),
			array('changed_by', 'length', 'max'=>128),
			array('changed_by_role', 'length', 'max'=>16),
			array('date_added, comments', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('store_points_redeem_history_id, store_points_redeem_id, store_points_redeem_status, date_added, payee_notified, comments, changed_by, changed_by_role', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'store_points_redeem_history_id' => 'Store Points Redeem History',
			'store_points_redeem_id' => 'Store Points Redeem',
			'store_points_redeem_status' => 'Store Points Redeem Status',
			'date_added' => 'Date Added',
			'payee_notified' => 'Payee Notified',
			'comments' => 'Comments',
			'changed_by' => 'Changed By',
			'changed_by_role' => 'Changed By Role',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('store_points_redeem_history_id',$this->store_points_redeem_history_id);
		$criteria->compare('store_points_redeem_id',$this->store_points_redeem_id);
		$criteria->compare('store_points_redeem_status',$this->store_points_redeem_status);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('payee_notified',$this->payee_notified);
		$criteria->compare('comments',$this->comments,true);
		$criteria->compare('changed_by',$this->changed_by,true);
		$criteria->compare('changed_by_role',$this->changed_by_role,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}