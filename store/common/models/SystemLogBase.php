<?php

/**
 * This is the model class for table "system_log".
 *
 * The followings are the available columns in table 'system_log':
 * @property integer $system_log_id
 * @property string $system_log_ip
 * @property string $system_log_time
 * @property string $system_log_action
 * @property string $system_log_admin_id
 * @property string $table_name
 * @property string $entry_id
 * @property string $field_name
 * @property string $from_value
 * @property string $to_value
 */
class SystemLogBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SystemLogBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'system_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('system_log_ip', 'length', 'max'=>15),
			array('system_log_action, system_log_admin_id, table_name, entry_id, field_name, from_value, to_value', 'length', 'max'=>255),
			array('system_log_time', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('system_log_id, system_log_ip, system_log_time, system_log_action, system_log_admin_id, table_name, entry_id, field_name, from_value, to_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'system_log_id' => 'System Log',
			'system_log_ip' => 'System Log Ip',
			'system_log_time' => 'System Log Time',
			'system_log_action' => 'System Log Action',
			'system_log_admin_id' => 'System Log Admin',
			'table_name' => 'Table Name',
			'entry_id' => 'Entry',
			'field_name' => 'Field Name',
			'from_value' => 'From Value',
			'to_value' => 'To Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('system_log_id',$this->system_log_id);
		$criteria->compare('system_log_ip',$this->system_log_ip,true);
		$criteria->compare('system_log_time',$this->system_log_time,true);
		$criteria->compare('system_log_action',$this->system_log_action,true);
		$criteria->compare('system_log_admin_id',$this->system_log_admin_id,true);
		$criteria->compare('table_name',$this->table_name,true);
		$criteria->compare('entry_id',$this->entry_id,true);
		$criteria->compare('field_name',$this->field_name,true);
		$criteria->compare('from_value',$this->from_value,true);
		$criteria->compare('to_value',$this->to_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}