<?php

/**
 * This is the model class for table "temp_process".
 *
 * The followings are the available columns in table 'temp_process':
 * @property integer $temp_id
 * @property string $page_name
 * @property string $match_case
 * @property string $extra_info
 * @property string $created_date
 */
class TempProcessBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TempProcessBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'temp_process';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('page_name, match_case, extra_info', 'length', 'max'=>255),
			array('created_date', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('temp_id, page_name, match_case, extra_info, created_date', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'temp_id' => 'Temp',
			'page_name' => 'Page Name',
			'match_case' => 'Match Case',
			'extra_info' => 'Extra Info',
			'created_date' => 'Created Date',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('temp_id',$this->temp_id);
		$criteria->compare('page_name',$this->page_name,true);
		$criteria->compare('match_case',$this->match_case,true);
		$criteria->compare('extra_info',$this->extra_info,true);
		$criteria->compare('created_date',$this->created_date,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}