<?php

/**
 * This is the model class for table "themes".
 *
 * The followings are the available columns in table 'themes':
 * @property integer $themes_id
 * @property integer $themes_language_id
 * @property string $themes_title
 * @property string $themes_description
 * @property string $themes_date_created
 * @property string $themes_last_modified
 * @property string $themes_type
 */
class ThemesBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ThemesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'themes';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('themes_language_id', 'numerical', 'integerOnly'=>true),
			array('themes_title', 'length', 'max'=>128),
			array('themes_description', 'length', 'max'=>255),
			array('themes_type', 'length', 'max'=>20),
			array('themes_date_created, themes_last_modified', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('themes_id, themes_language_id, themes_title, themes_description, themes_date_created, themes_last_modified, themes_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'themes_id' => 'Themes',
			'themes_language_id' => 'Themes Language',
			'themes_title' => 'Themes Title',
			'themes_description' => 'Themes Description',
			'themes_date_created' => 'Themes Date Created',
			'themes_last_modified' => 'Themes Last Modified',
			'themes_type' => 'Themes Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('themes_id',$this->themes_id);
		$criteria->compare('themes_language_id',$this->themes_language_id);
		$criteria->compare('themes_title',$this->themes_title,true);
		$criteria->compare('themes_description',$this->themes_description,true);
		$criteria->compare('themes_date_created',$this->themes_date_created,true);
		$criteria->compare('themes_last_modified',$this->themes_last_modified,true);
		$criteria->compare('themes_type',$this->themes_type,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}