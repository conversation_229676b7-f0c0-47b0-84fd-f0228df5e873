<?php

/**
 * This is the model class for table "top_up_info".
 *
 * The followings are the available columns in table 'top_up_info':
 * @property string $top_up_info_id
 * @property integer $products_id
 * @property string $top_up_info_title
 * @property string $top_up_info_key
 * @property string $top_up_info_description
 * @property string $top_up_info_value
 * @property string $top_up_info_type_id
 * @property integer $sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property integer $last_modified_by
 * @property string $use_function
 * @property string $set_function
 */
class TopUpInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TopUpInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'top_up_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('set_function', 'required'),
			array('products_id, sort_order, last_modified_by', 'numerical', 'integerOnly'=>true),
			array('top_up_info_title, top_up_info_key', 'length', 'max'=>64),
			array('top_up_info_description, top_up_info_value, use_function', 'length', 'max'=>255),
			array('top_up_info_type_id', 'length', 'max'=>10),
			array('last_modified, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('top_up_info_id, products_id, top_up_info_title, top_up_info_key, top_up_info_description, top_up_info_value, top_up_info_type_id, sort_order, last_modified, date_added, last_modified_by, use_function, set_function', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'top_up_info_id' => 'Top Up Info',
			'products_id' => 'Products',
			'top_up_info_title' => 'Top Up Info Title',
			'top_up_info_key' => 'Top Up Info Key',
			'top_up_info_description' => 'Top Up Info Description',
			'top_up_info_value' => 'Top Up Info Value',
			'top_up_info_type_id' => 'Top Up Info Type',
			'sort_order' => 'Sort Order',
			'last_modified' => 'Last Modified',
			'date_added' => 'Date Added',
			'last_modified_by' => 'Last Modified By',
			'use_function' => 'Use Function',
			'set_function' => 'Set Function',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('top_up_info_id',$this->top_up_info_id,true);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('top_up_info_title',$this->top_up_info_title,true);
		$criteria->compare('top_up_info_key',$this->top_up_info_key,true);
		$criteria->compare('top_up_info_description',$this->top_up_info_description,true);
		$criteria->compare('top_up_info_value',$this->top_up_info_value,true);
		$criteria->compare('top_up_info_type_id',$this->top_up_info_type_id,true);
		$criteria->compare('sort_order',$this->sort_order);
		$criteria->compare('last_modified',$this->last_modified,true);
		$criteria->compare('date_added',$this->date_added,true);
		$criteria->compare('last_modified_by',$this->last_modified_by);
		$criteria->compare('use_function',$this->use_function,true);
		$criteria->compare('set_function',$this->set_function,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public static function getTopUpInfoVal($pid, $key){
        $select = " SELECT top_up_info_value
                    FROM " . TopUpInfoBase::model()->tableName() . "
                    WHERE top_up_info_key = :top_up_info_key
                        AND products_id = :products_id";
        $command = Yii::app()->db->createCommand($select);
        $command->bindParam(":products_id", $pid, PDO::PARAM_INT);
        $command->bindParam(":top_up_info_key", $key, PDO::PARAM_STR);
        if ($value = $command->queryScalar()) {
            return $value;
        }
        return false;
    }
}