<?php

/**
 * This is the model class for table "top_up_info_lang".
 *
 * The followings are the available columns in table 'top_up_info_lang':
 * @property string $top_up_info_id
 * @property string $top_up_info_display
 * @property integer $languages_id
 */
class TopUpInfoLangBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TopUpInfoLangBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'top_up_info_lang';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('languages_id', 'numerical', 'integerOnly'=>true),
			array('top_up_info_id', 'length', 'max'=>10),
			array('top_up_info_display', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('top_up_info_id, top_up_info_display, languages_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'top_up_info' => array(self::BELONGS_TO, 'TopUpInfoBase', '', 'on' => 'top_up_info.top_up_info_id = t.top_up_info_id', 'joinType' => 'INNER JOIN', 'alias' => 'top_up_info'),
        );
    }

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'top_up_info_id' => 'Top Up Info',
			'top_up_info_display' => 'Top Up Info Display',
			'languages_id' => 'Languages',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('top_up_info_id',$this->top_up_info_id,true);
		$criteria->compare('top_up_info_display',$this->top_up_info_display,true);
		$criteria->compare('languages_id',$this->languages_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getTopupDisplay($customersTopupInfoArr) {
        $topupInfoList = array();
        foreach($customersTopupInfoArr as $k => $v) {
            $topupInfoList[] = (int) trim($k);
        }
        $criteria = new CDbCriteria();
        $criteria->select = 'top_up_info_display, top_up_info_id';
        $criteria->condition = 't.top_up_info_id IN ("' . implode('", "', $topupInfoList) . '") AND languages_id = "' . Yii::t("dev", "LANG_CODE_TO_ID") . '"';
        $criteria->with = array('top_up_info' => array('select' => 'top_up_info_key'));
        $result = $this->model()->findAll($criteria);
        return $result;

    }
}