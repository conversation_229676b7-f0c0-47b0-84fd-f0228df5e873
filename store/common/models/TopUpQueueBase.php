<?php

/**
 * This is the model class for table "top_up_queue".
 *
 * The followings are the available columns in table 'top_up_queue':
 * @property string $top_up_id
 * @property integer $counter
 * @property string $top_up_queue_action
 * @property string $check_date
 * @property string $date_added
 */
class TopUpQueueBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TopUpQueueBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'top_up_queue';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('counter', 'numerical', 'integerOnly'=>true),
			array('top_up_queue_action', 'length', 'max'=>1),
			array('check_date, date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('top_up_id, counter, top_up_queue_action, check_date, date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'top_up_id' => 'Top Up',
			'counter' => 'Counter',
			'top_up_queue_action' => 'Top Up Queue Action',
			'check_date' => 'Check Date',
			'date_added' => 'Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('top_up_id',$this->top_up_id,true);
		$criteria->compare('counter',$this->counter);
		$criteria->compare('top_up_queue_action',$this->top_up_queue_action,true);
		$criteria->compare('check_date',$this->check_date,true);
		$criteria->compare('date_added',$this->date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}