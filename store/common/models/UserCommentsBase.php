<?php

/**
 * This is the model class for table "user_comments".
 *
 * The followings are the available columns in table 'user_comments':
 * @property integer $user_comments_auto_id
 * @property integer $user_comments_id
 * @property integer $user_id
 * @property string $user_comments
 * @property string $user_role
 */
class UserCommentsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserCommentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'user_comments';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_comments_id, user_id', 'numerical', 'integerOnly'=>true),
			array('user_role', 'length', 'max'=>16),
			array('user_comments', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('user_comments_auto_id, user_comments_id, user_id, user_comments, user_role', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'user_comments_auto_id' => 'User Comments Auto',
			'user_comments_id' => 'User Comments',
			'user_id' => 'User',
			'user_comments' => 'User Comments',
			'user_role' => 'User Role',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('user_comments_auto_id',$this->user_comments_auto_id);
		$criteria->compare('user_comments_id',$this->user_comments_id);
		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('user_comments',$this->user_comments,true);
		$criteria->compare('user_role',$this->user_role,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}