<?php

/**
 * This is the model class for table "user_favourite_products".
 *
 * The followings are the available columns in table 'user_favourite_products':
 * @property integer $user_id
 * @property integer $products_id
 * @property integer $favourite_products_presale_notice
 */
class UserFavouriteProductsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserFavouriteProductsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'user_favourite_products';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_id, products_id, favourite_products_presale_notice', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('user_id, products_id, favourite_products_presale_notice', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'user_id' => 'User',
			'products_id' => 'Products',
			'favourite_products_presale_notice' => 'Favourite Products Presale Notice',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('user_id',$this->user_id);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('favourite_products_presale_notice',$this->favourite_products_presale_notice);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}