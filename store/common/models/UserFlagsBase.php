<?php

/**
 * This is the model class for table "user_flags".
 *
 * The followings are the available columns in table 'user_flags':
 * @property integer $user_flags_id
 * @property string $user_flags_name
 * @property string $user_flags_description
 * @property string $user_flags_alias
 * @property integer $user_flags_status
 * @property string $user_flags_css_style
 * @property string $user_flags_on_notification
 * @property string $user_flags_off_notification
 * @property string $user_flags_admin_groups_id
 */
class UserFlagsBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserFlagsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'user_flags';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_flags_admin_groups_id', 'required'),
			array('user_flags_status', 'numerical', 'integerOnly'=>true),
			array('user_flags_name', 'length', 'max'=>128),
			array('user_flags_description, user_flags_css_style, user_flags_on_notification, user_flags_off_notification', 'length', 'max'=>255),
			array('user_flags_alias', 'length', 'max'=>12),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_status, user_flags_css_style, user_flags_on_notification, user_flags_off_notification, user_flags_admin_groups_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'user_flags_id' => 'User Flags',
			'user_flags_name' => 'User Flags Name',
			'user_flags_description' => 'User Flags Description',
			'user_flags_alias' => 'User Flags Alias',
			'user_flags_status' => 'User Flags Status',
			'user_flags_css_style' => 'User Flags Css Style',
			'user_flags_on_notification' => 'User Flags On Notification',
			'user_flags_off_notification' => 'User Flags Off Notification',
			'user_flags_admin_groups_id' => 'User Flags Admin Groups',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('user_flags_id',$this->user_flags_id);
		$criteria->compare('user_flags_name',$this->user_flags_name,true);
		$criteria->compare('user_flags_description',$this->user_flags_description,true);
		$criteria->compare('user_flags_alias',$this->user_flags_alias,true);
		$criteria->compare('user_flags_status',$this->user_flags_status);
		$criteria->compare('user_flags_css_style',$this->user_flags_css_style,true);
		$criteria->compare('user_flags_on_notification',$this->user_flags_on_notification,true);
		$criteria->compare('user_flags_off_notification',$this->user_flags_off_notification,true);
		$criteria->compare('user_flags_admin_groups_id',$this->user_flags_admin_groups_id,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}


    private function getFlagsData() {
        $criteria = new CDbCriteria();
        $criteria->select = 'user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_css_style';
        $criteria->condition = 'user_flags_status = "1"';
        $criteria->order = 'user_flags_id';
        $result = $this->model()->findAll($criteria);
        return $result;
    }

    public function getUserFlags(){
        $userFlagsArray = array();
        $userFlags = $this->getFlagsData();
        foreach ($userFlags as $flagInfo) {
            $userFlagsArray[$flagInfo->user_flags_id] = $flagInfo;
        }
        return $userFlagsArray;
    }
}