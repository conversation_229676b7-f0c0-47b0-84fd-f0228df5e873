<?php

/**
 * This is the model class for table "user_setting".
 *
 * The followings are the available columns in table 'user_setting':
 * @property integer $user_setting_id
 * @property integer $user_setting_user_id
 * @property string $user_setting_key
 * @property string $user_setting_value
 */
class UserSettingBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'user_setting';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('user_setting_user_id', 'numerical', 'integerOnly'=>true),
			array('user_setting_key, user_setting_value', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('user_setting_id, user_setting_user_id, user_setting_key, user_setting_value', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'user_setting_id' => 'User Setting',
			'user_setting_user_id' => 'User Setting User',
			'user_setting_key' => 'User Setting Key',
			'user_setting_value' => 'User Setting Value',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('user_setting_id',$this->user_setting_id);
		$criteria->compare('user_setting_user_id',$this->user_setting_user_id);
		$criteria->compare('user_setting_key',$this->user_setting_key,true);
		$criteria->compare('user_setting_value',$this->user_setting_value,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}