<?php

/**
 * This is the model class for table "webmoney".
 *
 * The followings are the available columns in table 'webmoney':
 * @property string $webmoney_payment_no
 * @property integer $webmoney_operative_mode
 * @property string $webmoney_payment_amount
 * @property string $webmoney_payee_purse
 * @property string $webmoney_payer_wm
 * @property string $webmoney_payer_purse
 * @property string $webmoney_invoices_no
 * @property string $webmoney_transaction_no
 * @property string $webmoney_payment_timestamp
 * @property string $webmoney_hash
 */
class WebmoneyBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WebmoneyBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'webmoney';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('webmoney_operative_mode', 'numerical', 'integerOnly'=>true),
			array('webmoney_payment_no', 'length', 'max'=>11),
			array('webmoney_payment_amount, webmoney_invoices_no, webmoney_transaction_no', 'length', 'max'=>15),
			array('webmoney_payee_purse, webmoney_payer_purse', 'length', 'max'=>13),
			array('webmoney_payer_wm', 'length', 'max'=>20),
			array('webmoney_payment_timestamp, webmoney_hash', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('webmoney_payment_no, webmoney_operative_mode, webmoney_payment_amount, webmoney_payee_purse, webmoney_payer_wm, webmoney_payer_purse, webmoney_invoices_no, webmoney_transaction_no, webmoney_payment_timestamp, webmoney_hash', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'webmoney_payment_no' => 'Webmoney Payment No',
			'webmoney_operative_mode' => 'Webmoney Operative Mode',
			'webmoney_payment_amount' => 'Webmoney Payment Amount',
			'webmoney_payee_purse' => 'Webmoney Payee Purse',
			'webmoney_payer_wm' => 'Webmoney Payer Wm',
			'webmoney_payer_purse' => 'Webmoney Payer Purse',
			'webmoney_invoices_no' => 'Webmoney Invoices No',
			'webmoney_transaction_no' => 'Webmoney Transaction No',
			'webmoney_payment_timestamp' => 'Webmoney Payment Timestamp',
			'webmoney_hash' => 'Webmoney Hash',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('webmoney_payment_no',$this->webmoney_payment_no,true);
		$criteria->compare('webmoney_operative_mode',$this->webmoney_operative_mode);
		$criteria->compare('webmoney_payment_amount',$this->webmoney_payment_amount,true);
		$criteria->compare('webmoney_payee_purse',$this->webmoney_payee_purse,true);
		$criteria->compare('webmoney_payer_wm',$this->webmoney_payer_wm,true);
		$criteria->compare('webmoney_payer_purse',$this->webmoney_payer_purse,true);
		$criteria->compare('webmoney_invoices_no',$this->webmoney_invoices_no,true);
		$criteria->compare('webmoney_transaction_no',$this->webmoney_transaction_no,true);
		$criteria->compare('webmoney_payment_timestamp',$this->webmoney_payment_timestamp,true);
		$criteria->compare('webmoney_hash',$this->webmoney_hash,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}