<?php

/**
 * This is the model class for table "zones_info".
 *
 * The followings are the available columns in table 'zones_info':
 * @property integer $geo_zone_id
 * @property string $geo_zone_info
 */
class ZonesInfoBase extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ZonesInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'zones_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('geo_zone_id', 'numerical', 'integerOnly'=>true),
			array('geo_zone_info', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('geo_zone_id, geo_zone_info', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'geo_zone_id' => 'Geo Zone',
			'geo_zone_info' => 'Geo Zone Info',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('geo_zone_id',$this->geo_zone_id);
		$criteria->compare('geo_zone_info',$this->geo_zone_info,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getZoneInfoByID($zone_id) {
        $return_str = '';
        
        $criteria = new CDbCriteria;
		$criteria->select = 'geo_zone_info';
		$criteria->condition = 'geo_zone_id=:geo_zone_id';
		$criteria->params = array(
            ':geo_zone_id' => $zone_id,
        );
        
		if ($result = $this->model()->find($criteria)) {
            $return_str = $result->geo_zone_info;
        }
        
		return $return_str;
    }
}