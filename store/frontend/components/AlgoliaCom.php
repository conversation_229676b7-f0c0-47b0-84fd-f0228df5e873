<?php

require_once COMMON_DIR . '/extensions/algoliasearch-client-php-master/algoliasearch.php';

class AlgoliaCom extends MainCom
{
    public function _init()
    {

    }

    public static function getAlgoliaParams()
    {
        return Yii::app()->params['ALGOLIA'];
    }

    private static function getCurlOpt()
    {
        $proxy = Yii::app()->params['PROXY'];
        $curlopt = array();
        if (isset($proxy) && !empty($proxy)) {
            $curlopt = ["CURLOPT_PROXY" => $proxy];
        }
        return $curlopt;
    }

    public static function getFacetValues($indices, $facet_title , $filter_str)
    {
        $algolia_param = self::getAlgoliaParams();
        $client = new \AlgoliaSearch\Client($algolia_param['APPLICATION_ID'], $algolia_param['SEARCH_API_KEY'], null,
            ['curloptions' => self::getCurlOpt()]);
        $index = $client->initIndex($indices);

        $res = $index->searchForFacetValues($facet_title, "", [
            'filters' => $filter_str
        ]);

        return $res;
    }

    public static function sendRequest($indices, $query, $filter = [])
    {
        $algolia_param = self::getAlgoliaParams();

        $client = new \AlgoliaSearch\Client($algolia_param['APPLICATION_ID'], $algolia_param['SEARCH_API_KEY'], null,
            ['curloptions' => self::getCurlOpt()]);

        $client->setForwardedFor(getIPAddress());
        $client->setAlgoliaUserToken((!empty(Yii::app()->user->id) ? Yii::app()->user->id : session_id()));

        $index = $client->initIndex($indices);

        $res = $index->search($query, $filter);
        return $res;
    }

    public static function sendMultipleQueries($query, $strategy = 'none')
    {
        $algolia_param = self::getAlgoliaParams();
        $client = new \AlgoliaSearch\Client($algolia_param['APPLICATION_ID'], $algolia_param['SEARCH_API_KEY'], null,
            ['curloptions' => self::getCurlOpt()]);

        $client->setForwardedFor(getIPAddress());
        $client->setAlgoliaUserToken((!empty(Yii::app()->user->id) ? Yii::app()->user->id : session_id()));

        $res = $client->multipleQueries($query, 'indexName', $strategy);
        return $res;
    }
}