<?php

class ApiGeneralCom extends CApplicationComponent
{

    public $purifier;

    public function _isSerialize($data)
    {
        return (@unserialize($data) != NULL) ? true : false;
    }

    public function _isJson($data)
    {
        return (@CJSON::decode($data) != NULL) ? true : false;
    }

    public function _purify($data)
    {
        if (!is_object($this->purifier)) {
            $this->purifier = new CHtmlPurifier();
        }

        if (is_array($data)) {
            $data = array_map(array($this, '_purify'), $data);
        } else {
            $data = $this->purifier->purify($data);
        }

        return $data;
    }

    public function _readInput()
    {
        $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : $_GET;

        if (isset($_SERVER['CONTENT_TYPE'])) {
            if ($_SERVER['CONTENT_TYPE'] == 'application/json') {
                $_data = file_get_contents("php://input");
            }
        }

        if (!empty($_data)) {
            if ($this->_isJson($_data)) {
                $_data = CJSON::decode($_data);
            } else if ($this->_isSerialize($_data)) {
                $_data = unserialize($_data);
            } else if (is_object($_data)) {
                $_data = (array) $_data;
            }
        }

        return $this->_purify($_data);
    }

    public function _sendResponse($result)
    {
        header("Content-Type: application/json");
        header("Cache-Control: no-store");

        echo CJSON::encode($this->removeInvalidCharsFromArray($result));

        Yii::app()->end();
    }

    public static function removeInvalidChars($text)
    {
        $regex = '/( [\x00-\x7F] |  [\xC0-\xDF][\x80-\xBF] | [\xE0-\xEF][\x80-\xBF]{2} | [\xF0-\xF7][\x80-\xBF]{3} ) | ./x';

        return preg_replace($regex, '$1', $text);
    }

    public static function removeInvalidCharsFromArray($array)
    {
        foreach ($array as &$data) {
            if (is_array($data)) {
                $data = self::removeInvalidCharsFromArray($data);
            } else if (is_string($data)) {
                $data = self::removeInvalidChars($data);
            }
        }

        return $array;
    }
}
