<?php

class AuthCom extends SSOCom {

    public static function registerAppSess() {
        if (!Yii::app()->request->isAjaxRequest) {
            SSOCom::SSOLogin();

            if (Yii::app()->user->id) {
                $userCountry = RegionalSettingCom::regionalCountryCache('g', ['uid' => Yii::app()->user->id]);
                if ($userCountry === false) {
                    Yii::app()->session->offsetUnset('country');
                    Yii::app()->session->offsetUnset('country_code');
                    Yii::app()->params["regionalSettingChecked"] = false;
                }
            }

            RegionalSettingCom::checkRegionalSetting();

            if (Yii::app()->user->id && (isset(Yii::app()->session['firstTimeLogin']) && Yii::app()->session['firstTimeLogin'] == "TRUE")) {
                //retrieve customer data
                $u_data = self::_setProfilerSession();

                if (isset($u_data["status"]) && $u_data["status"]) {
                    Yii::app()->session->offsetUnset('firstTimeLogin');
                    // update user password verifying cookie
                    self::updateLifetimeCookies();
                    //keep user preference
                    RegionalSettingCom::saveCustomerPreference();

                    //keep login history
                    CustomersLoginHistory::model()->insertHistory(Yii::app()->user->id, Yii::app()->frontPageCom->getIPAddress(), (isset(Yii::app()->user->login_method) ? Yii::app()->user->login_method : "-"));
                }
            } else if (isset(Yii::app()->request->cookies['SHASSHO_UPDATE_PROFILER']) && Yii::app()->request->cookies['SHASSHO_UPDATE_PROFILER'] == "TRUE") {
                unset(Yii::app()->request->cookies['SHASSHO_UPDATE_PROFILER']);
                self::_setProfilerSession();
            }
        } else {
            RegionalSettingCom::checkRegionalSetting();
        }

        return true;
    }

    public static function flushProfilerSession(){
        self::_setProfilerSession();
    }

    private static function _setProfilerSession() {
        if (Yii::app()->user->id) {
            if (isset(Yii::app()->user->login_method) && Yii::app()->user->login_method !== 'Normal') {
                Yii::app()->session['fb_uid'] = 1;
            } else {
                Yii::app()->session['fb_uid'] = 0;
            }

            if ($u_data = CustomerCom::getCustomerData(Yii::app()->user->id, 'firstname,lastname,group_name,email,group_id,address_id')) {
                //set profile session
                Yii::app()->session['customers_firstname'] = $u_data['firstname'];
                Yii::app()->session['customers_lastname'] = $u_data['lastname'];
                Yii::app()->session['customers_groups_name'] = $u_data['group_name'];
                Yii::app()->session['customers_email_address'] = $u_data['email'];

                Yii::app()->session['customers_groups_id'] = $u_data['group_id'];
                Yii::app()->session['customers_login_ip'] = Yii::app()->frontPageCom->getIPAddress();
                Yii::app()->session['customers_login_timestamp'] = time();
                Yii::app()->session['customers_default_address_id'] = $u_data['address_id'];

                $countryInfo = Yii::app()->geoip->getIPCountryInfo(Yii::app()->session['customers_login_ip']);

                if (isset($countryInfo['countries_iso_code_2'])) {
                    Yii::app()->session['login_country_iso_code_2'] = $countryInfo['countries_iso_code_2'];
                }

                return array('status' => 1);
            }
        }

        return array('status' => 0);
    }

    public static function updateLifetimeCookies() {
        Yii::app()->session['lifetime_secret'] = AccessTokenCom::generateAccessToken(time() . Yii::app()->user->id);

        if (isset(Yii::app()->user->id) && notEmpty(Yii::app()->user->id)) {
            if (Yii::app()->session['fb_uid']) {
                $cookiesExpired = '';
            } else {
                $cookiesExpired = time() + (Yii::app()->params['VERIFYING_USER_PASSWORD_LIFESPAN'] * 60);
            }

            $content = $cookiesExpired . '-' . md5($cookiesExpired . Yii::app()->session['lifetime_secret']);
        } else {
            $cookiesExpired = time() - (Yii::app()->params['VERIFYING_USER_PASSWORD_LIFESPAN'] * 60);
            $content = '';
        }

        $cookie = new CHttpCookie('u_valid', $content);
        $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
        if ($cookiesExpired) {
            $cookie->expire = $cookiesExpired;
        }
        $cookie->path = '/';
        Yii::app()->request->cookies['u_valid'] = $cookie;
    }

    public static function checkIsLifetimeCookiesExisted($extendedInSec = 0) {
        $returnBool = FALSE;

        if (Yii::app()->user->id && isset(Yii::app()->session['lifetime_secret'])) {
            // TODO fb_uid set to 1 when non Normal login, which include Remember me, and other SSO Login, which cause store credit confirmation doesn't show on remember me
            if (Yii::app()->session['fb_uid']) {
                $returnBool = TRUE;
            } else {
                if (isset(Yii::app()->request->cookies['u_valid'])) {
                    $splitContent = explode('-', htmlspecialchars(Yii::app()->request->cookies['u_valid']->value));
                    if (count($splitContent) == 2) {
                        if ((int) $splitContent[0] > (time() - $extendedInSec)) {
                            if ($splitContent[1] === md5($splitContent[0] . Yii::app()->session['lifetime_secret'])) {
                                $returnBool = TRUE;
                            }
                        }
                    }
                }
            }
        }

        return $returnBool;
    }

}
