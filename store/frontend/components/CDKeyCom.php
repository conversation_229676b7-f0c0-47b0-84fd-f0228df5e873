<?php

class CDKeyCom extends MainCom
{
    public $custom_products_code_array, $aws_obj;

    const CUSTOM_PUBLISHER_PREFIX = array(
        'CMP' => 'Cmap',
        'BHNV2' => 'BHNV2'
    );

    public function getCdkeyImg($keyident, $customKey = false, $download = false, $retry = false)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();
        if (Yii::app()->customerCom->isActiveCustomer() === false) {
            return Yii::t('myogm', 'TEXT_CDKEY_SUSPENDED_FOR_VIEWING');
        }
        $cpCodeId = self::verifyCdkeyOwner($keyident);

        if ($cpCodeId !== false) {
            $productCodeInfo = $modelCustomProductsCode->findByPk($cpCodeId);
            if (isset($productCodeInfo->custom_products_code_id)) {
                // Flag this cd key as viewed
                if ($productCodeInfo->custom_products_code_viewed == '0') {
                    $classLogFilesComponent = new LogCom();

                    $cdkeyOldLog = $modelCustomProductsCode::model()->findByPk($cpCodeId);
                    $oldLog['custom_products_code_viewed'] = isset($cdkeyOldLog->custom_products_code_viewed) ? $cdkeyOldLog->custom_products_code_viewed : null;

                    if ($cdkeyOldLog) {
                        $cdkeyOldLog->custom_products_code_viewed = 1;
                        $cdkeyOldLog->update();
                    }

                    $cdkeyNewLog = $modelCustomProductsCode::model()->findByPk($cpCodeId);
                    $newLog['custom_products_code_viewed'] = isset($cdkeyNewLog->custom_products_code_viewed) ? $cdkeyNewLog->custom_products_code_viewed : null;

                    $cdkeyChangesArray = $classLogFilesComponent->detectChanges($oldLog, $newLog);
                    $cdkeyChangesFormattedArray = $classLogFilesComponent->constructLogMessage($cdkeyChangesArray);
                    if (count($cdkeyChangesFormattedArray)) {
                        $changesStr = 'Changes made:' . "\n";
                        for ($i = 0; $i < count($cdkeyChangesFormattedArray); $i++) {
                            if (count($cdkeyChangesFormattedArray[$i])) {
                                foreach ($cdkeyChangesFormattedArray[$i] as $field => $res) {
                                    if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                        $changesStr .= $res['text'] . "\n";
                                    } else {
                                        $changesStr .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                    }
                                }
                            }
                        }
                        $classLogFilesComponent->insertCdkeyHistoryLog('customers', $cpCodeId, $changesStr);
                    }
                }

                $customProductsCodeId = $productCodeInfo['custom_products_code_id'];

                $to_s3 = $productCodeInfo['to_s3'];
                $products_id = $productCodeInfo['products_id'];
                $code_date_added = $productCodeInfo['code_date_added'];

                $cpc_obj = new CustomProductsCodeBase();
                $OrdersCdkeys = $this->getOrdersCdkeys($customProductsCodeId);

                if ($OrdersCdkeys !== false) {
                    if ($OrdersCdkeys->status == 0 || ($OrdersCdkeys->status == 1 && empty($OrdersCdkeys->data_encrypted))) {
                        $theData = $this->getCode($customProductsCodeId, $to_s3, $products_id, $code_date_added);
                        $this->saveOrdersCdkeys($customProductsCodeId, $theData);
                    } else {
                        $theData = $OrdersCdkeys->data_encrypted;
                    }
                } else {
                    $params = array(
                        'orders_products_id' => $productCodeInfo['orders_products_id'],
                        'custom_products_code_id' => $customProductsCodeId
                    );
                    $this->saveNewOrdersCdkeys($params);
                    $theData = $this->getCode($customProductsCodeId, $to_s3, $products_id, $code_date_added);
                    $this->saveOrdersCdkeys($customProductsCodeId, $theData);
                }

                unset($cpc_obj);

                if ($theData !== false) {
                    $theDataStr = self::decryptData($theData);

                    // Find Product Model and determine custom voucher
                    $modelProducts = new ProductsBase();
                    $products = $modelProducts->findByPk($products_id);

                    if ($prefix = $this->checkProductsModel($products->products_model)) {
                        return $this->customTheDataStr($prefix, $customProductsCodeId, $theDataStr, $customKey, $download);
                    }

                    return $theDataStr;
                } else {
                    $this->deleteOrdersCdkeys($keyident);
                    if ($retry === false) {
                        return $this->getCdkeyImg($keyident, $customKey, $download, true);
                    }
                    return '';
                }
            }
        }
    }

    public function getAllCdkey($keyident_arr)
    {
        foreach ($keyident_arr as $keyident) {
            $modelCustomProductsCode = new CustomProductsCodeBase();
            if (Yii::app()->customerCom->isActiveCustomer() === false) {
                return Yii::t('myogm', 'TEXT_CDKEY_SUSPENDED_FOR_VIEWING');
            }
            $cpCodeId = self::verifyCdkeyOwner($keyident);

            if ($cpCodeId !== false) {
                $productCodeInfo = $modelCustomProductsCode->findByPk($cpCodeId);
                if (isset($productCodeInfo->custom_products_code_id)) {
                    $customProductsCodeId = $productCodeInfo['custom_products_code_id'];

                    $to_s3 = $productCodeInfo['to_s3'];
                    $products_id = $productCodeInfo['products_id'];
                    $code_date_added = $productCodeInfo['code_date_added'];

                    $cpc_obj = new CustomProductsCodeBase();
                    $OrdersCdkeys = $this->getOrdersCdkeys($customProductsCodeId);

                    if ($OrdersCdkeys !== false) {
                        if ($OrdersCdkeys->status == 0) {
                            $theData = $this->getCode($customProductsCodeId, $to_s3, $products_id, $code_date_added);
                            $this->saveOrdersCdkeys($customProductsCodeId, $theData);
                        } else {
                            $theData = $OrdersCdkeys->data_encrypted;
                        }
                    } else {
                        $params = array(
                            'orders_products_id' => $productCodeInfo['orders_products_id'],
                            'custom_products_code_id' => $customProductsCodeId
                        );
                        $this->saveNewOrdersCdkeys($params);
                        $theData = $this->getCode($customProductsCodeId, $to_s3, $products_id, $code_date_added);
                        $this->saveOrdersCdkeys($customProductsCodeId, $theData);
                    }
                    unset($cpc_obj);
                }
            }
        }
        return true;
    }

    public static function verifyCdkeyOwner($keyident)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();
        $modelOrders = new OrdersBase();

        $keyident = trim($keyident);

        $productCodeInfo = $modelCustomProductsCode->findByPk($keyident);
        if (isset($productCodeInfo->custom_products_code_id)) {
            $orderIsVerify = $modelOrders->verifyOrdersProducts($productCodeInfo->orders_products_id);
            if ($orderIsVerify) {
                return $productCodeInfo->custom_products_code_id;
            }
        }
        return false;
    }

    public function getCode($custom_products_code_id, $is_s3 = null, $product_id = 0, $code_add_datetime = null, $bucket_key = null)
    {
        $customProductCode = CustomProductsCodeBase::model()->findByPK($custom_products_code_id);
        if ((int)$is_s3 == 1) {
            return $this->getFromAWS($customProductCode, $bucket_key);
        }
    }


    private function getFromAWS($customProductCode, $bucket_key)
    {
        $code_add_datetime = $customProductCode->code_date_added;
        $product_id = $customProductCode->products_id;
        if (empty($this->aws_obj)) {
            $this->aws_obj = new AmazonWsCom();
            $this->aws_obj->set_bucket_key('BUCKET_SECURE');
            $this->aws_obj->set_storage('STORAGE_REDUCED');
        }

        if ($this->aws_obj->is_s3_bucket_config_enabled()) {
            $dir = date('Ym', strtotime($code_add_datetime));
            $filename = $customProductCode->custom_products_code_id . '.key';
            $filepath = $dir . '/' . $product_id . '/';

            $this->aws_obj->set_filename($filename);
            $this->aws_obj->set_filepath($filepath);
            $theData = $this->aws_obj->get_file();

            if ($theData != '') {
                return $theData->body;
            } else {
                // aws obj will report error
            }

            return false;
        } else {
            return false;
        }
    }

    public static function decryptData($theData)
    {
        if ($theData) {
            $key = Yii::app()->params['SECURE_KEY'];
            $iv = Yii::app()->params['SECURE_KEY_IV'];

            $theData = substr($theData, 20);
            $theData = openssl_decrypt($theData, "AES-256-CBC", $key, OPENSSL_ZERO_PADDING, $iv);

            $theData = rtrim($theData, "\0");
            $theData = base64_decode($theData);
        }
        return $theData;
    }


    public static function image($src, $alt = '', $width = '', $height = '', $params = '')
    {
        $image = '<a href="' . $src . '" target="_blank"><img class="codes__preview-img" src="' . $src . '" border="0" alt="' . $alt . '"';
        if ($alt) {
            $image .= ' title=" ' . $alt . ' "';
        }
        if ($width) {
            $image .= ' width="' . $width . '"';
        }
        if ($height) {
            $image .= ' height="' . $height . '"';
        }
        if ($params) {
            $image .= ' ' . $params;
        }
        $image .= '></a>';
        return $image;
    }

    public function getBase64Img($keyident)
    {
        $model = (new CustomProductsCodeBase())->findByPk($keyident);
        if ($model && $model->file_type == 'soft') {
            ; // Text format
        } else {
            switch (strtolower($model->file_type)) {
                case 'jpg':
                    $filetype = 'jpeg';
                    break;

                default:
                    $filetype = $model->file_type;
            }
        }
        $image = '<a href="' . Yii::app()->createUrl('account/showImage', array('keyident' => (int)$keyident)) . '" target="_blank">';
        $image .= '<img class="codes__preview-img" src="data:image/' . $filetype . ';base64, ' . base64_encode($this->getCdkeyImg($keyident)) . '">';
        $image .= '</a>';
        return $image;
    }

    public static function generateHashByOpId($op_id)
    {
        return hash_hmac('sha256', serialize([$op_id]), Yii::app()->params['SECURE_KEY']);
    }

    public static function getProductsName($product_id)
    {
        $modelProductsDescription = new ProductsDescriptionBase();
        $productName = $modelProductsDescription->getProductsName($product_id, Yii::t("dev", "LANG_CODE_TO_ID"));
        return $productName;
    }

    public static function br2nl($text)
    {
        $text = str_replace("<br />", "\r\n", $text);
        $text = str_replace("<br>", "\r\n", $text);
        return $text;
    }

    public function makeBase64ImgZip($order_id, $product_id, $cdkey_list)
    {
        $batch_name = "{$order_id}_{$product_id}_Qty" . count($cdkey_list);
        $soft_pin = '';

        if (count($cdkey_list) > 0) {
            foreach ($cdkey_list as $cpc_row) {
                $file_type = (trim($cpc_row->file_type)) ? $cpc_row->file_type : 'jpg';
                if ($file_type == 'soft') {
                    $data = strip_tags(self::br2nl($this->getCdkeyImg($cpc_row->custom_products_code_id, false, true)));
                    if (!empty($data)) {
                        $soft_pin .= $data . "\r\n";
                    }
                } else {
                    $bin_img = $this->getCdkeyImg((int)$cpc_row->custom_products_code_id);
                    Yii::app()->zipfile->addFile($bin_img, $cpc_row->custom_products_code_id . '.' . $cpc_row->file_type);
                }
            }

            if (!empty($soft_pin)) {
                $soft_pin = strip_tags($this->getProductsName($product_id)) . "\r\n" . $soft_pin;
                $filename = "softpin.txt";
                Yii::app()->zipfile->addFile($soft_pin, $filename);
            }

            Yii::app()->zipfile->sendFile($batch_name);
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }
    }


    public function showBase64Img($keyident)
    {
        $model = (new CustomProductsCodeBase())->findByPk($keyident);
        if ($model && $model->file_type == 'soft') {
            ; // Text format
        } else {
            switch (strtolower($model->file_type)) {
                case 'jpg':
                    $filetype = 'jpeg';
                    break;

                default:
                    $filetype = $model->file_type;
            }

            header("Content-type: image/$filetype");
            header("Expires: Mon, 02 May 2001 23:00:00 GMT");
            header("Cache-Control: no-store, no-cache, must-revalidate");
            header("Cache-Control: post-check=0, pre-check=0", false);
            header("Pragma: no-cache");
        }

        echo $this->getCdkeyImg($keyident);
        exit;
    }

    public static function getCdkeyIdentifier($productId, $ordersProductId)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();

        $cdk_identifier_arr = array();
        $return_arr = array();

        $productCode = $modelCustomProductsCode->getCustomProductCodeByOrdersProductsId($ordersProductId);

        foreach ($productCode as $codeInfo) {
            $cdk_identifier_arr[] = $codeInfo->custom_products_code_id;
            $return_arr[] = array(
                'key_identifier' => $codeInfo->custom_products_code_id,
                'file_name' => $productId . '_' . $codeInfo->custom_products_code_id,
                'is_viewed' => $codeInfo->custom_products_code_viewed,
                'file_type' => !empty($codeInfo->file_type) ? $codeInfo->file_type : 'jpg',
            );
        }

        return array('cdk' => $cdk_identifier_arr, 'info' => $return_arr);
    }

    private function checkProductsModel($productsModel)
    {
        if (!empty($productsModel)) {
            $modelPrefixArray = explode("_", $productsModel);
            if (array_key_exists($modelPrefixArray[0], self::CUSTOM_PUBLISHER_PREFIX) && !empty(self::CUSTOM_PUBLISHER_PREFIX[$modelPrefixArray[0]])) {
                return $modelPrefixArray[0];
            }
        }

        return false;
    }

    private function customTheDataStr($prefix, $customProductsCodeId, $dataStr, $customKey = false, $download = false)
    {
        $code = [];
        switch ($prefix) {
            case 'CMP': // CMAP Custom link
                $strArray = explode('<br>', $dataStr);
                foreach ($strArray as $key => $value) {
                    if ($key == 0 && !$customKey && !$download) {
                        $url = $this->getVoucherUrl($value);
                        $code[] = '<a href="' . $url . '" target="_blank">Retrieve Your Voucher Here</a>';
                    } elseif ($key == 0 && $customKey && !$download) {
                        return $this->getVoucherUrl($value);
                    } else {
                        $code[] = $value;
                    }
                }
                break;

            case 'BHNV2': // BlackHawk Custom Link
                $strArray = explode('<br>', $dataStr);
                if (!$download && count($strArray) === 1) {
                    $row = explode(':', $strArray[0], 2);
                    if (isset($row[0]) && isset($row[1])) {
                        $key = trim($row[0]);
                        $value = trim($row[1]);
                        if ($key === 'Apple Redeem Code') {
                            $apple_redeem_url = '<a href="https://www.appstore.com/redeem/%s" target="_blank">%s</a>';
                            $code[] = 'Redeem Code : ' . sprintf($apple_redeem_url, $value, $value);
                        }
                    }
                }

                if (empty($code)) {
                    $code = $strArray;
                }
                break;
        }

        return (!empty($code) ? implode("<br>", $code) : null);
    }

    private function getVoucherUrl($urlInfo)
    {
        $urlArray = explode(':', $urlInfo);
        unset($urlArray[0]);
        return implode(':', $urlArray);
    }

    private function getOrdersCdkeys($cpId)
    {
        $orders_cdkeys = new OrdersCdkeys();
        $result = $orders_cdkeys->getOrdersCdkeys($cpId);
        if (isset($result)) {
            return $result;
        } else {
            return false;
        }
    }

    private function saveNewOrdersCdkeys($params)
    {
        DbHelper::insertIgnore('orders_cdkeys', [
            'orders_products_id' => $params['orders_products_id'],
            'custom_products_code_id' => $params['custom_products_code_id'],
            'status' => 0,
            'data_encrypted' => '',
            'created_at' => date("Y-m-d H:i:s", time()),
            'updated_at' => date("Y-m-d H:i:s", time())
        ]);

        return (new OrdersCdkeys)->getOrdersCdkeys($params['custom_products_code_id']);
    }

    public function saveAllOrdersCdkeys($cpc_id_array)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();
        $OrdersCdkeys = new OrdersCdkeys;

        foreach ($cpc_id_array as $cpc_id) {
            $cpCodeId = $this->verifyCdkeyOwner($cpc_id);
            if ($cpCodeId) {
                $productCodeInfo = $modelCustomProductsCode->findByPk($cpc_id);
                $exists = $OrdersCdkeys->isExist($cpc_id);
                $params = array(
                    'orders_products_id' => $productCodeInfo->orders_products_id,
                    'custom_products_code_id' => $cpc_id
                );
                if (!$exists) {
                    $this->saveNewOrdersCdkeys($params);
                }
            }
        }

        return true;
    }

    private function saveOrdersCdkeys($cpId, $data_encrypted)
    {
        $params = array(
            'data_encrypted' => $data_encrypted,
            'updated_at' => date("Y-m-d H:i:s", time()),
            'status' => 1
        );
        $m_cond = 'custom_products_code_id = :cpId';
        $m_params = array(':cpId' => $cpId);

        OrdersCdkeys::model()->updateAll($params, $m_cond, $m_params);
    }

    private function deleteOrdersCdkeys($cpId)
    {
        OrdersCdkeys::model()->deleteAllByAttributes(['custom_products_code_id' => $cpId]);
    }

}