<?php

class Clevertap extends CApplicationComponent
{
    public $app_id;

    public function __construct()
    {
        $this->app_id = Yii::app()->params['CLEVERTAP'];
    }

    public function webSDK()
    {
        if ($this->app_id) {
            echo <<<HTML
            <script type="text/javascript">
                var clevertap = {event:[], profile:[], account:[], onUserLogin:[], region:'sg1', notifications:[], privacy:[]};
                // replace with the CLEVERTAP_ACCOUNT_ID with the actual ACCOUNT ID value from your Dashboard -> Settings page
                clevertap.account.push({"id": "$this->app_id"});
                clevertap.privacy.push({optOut: false}); //set the flag to true, if the user of the device opts out of sharing their data
                clevertap.privacy.push({useIP: true}); //set the flag to true, if the user agrees to share their IP data
                (function () {
                    var wzrk = document.createElement('script');
                    wzrk.type = 'text/javascript';
                    wzrk.defer = true;
                    wzrk.src = ('https:' == document.location.protocol ? 'https://d2r1yp2w7bby2u.cloudfront.net' : 'http://static.clevertap.com') + '/js/clevertap.min.js';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(wzrk, s);
                })();
            </script>
HTML;
        }
    }

    /*
     * Identify Users
     */
    public function userLogin()
    {
        if ($this->app_id && Yii::app()->session->get('clevertap_userLogin')) {
            $criteria = new CDbCriteria();
            $criteria->condition = 'customers_id = :cid';
            $criteria->params = [':cid' => Yii::app()->user->id];
            $m_cust = Customers::model()->find($criteria);
            if (isset($m_cust->customers_id)) {
                // name
                $name = implode(" ", [$m_cust->customers_firstname, $m_cust->customers_lastname]);

                // phone number
                $criteria->condition = 'countries_id = :ctry';
                $criteria->params = [':ctry' => $m_cust->customers_country_dialing_code_id];
                $m_ctry = Countries::model()->find($criteria);
                $phone = ((isset($m_ctry->countries_international_dialing_code) && !empty($m_ctry->countries_international_dialing_code)) ? '+' . $m_ctry->countries_international_dialing_code : "") . $m_cust->customers_telephone;

                $js = <<<JS
                clevertap.onUserLogin.push({
                 "Site": {
                   "Name": "$name",                          // String
                   "Identity": $m_cust->customers_id,        // String or number
                   "Email": "$m_cust->customers_email_address",    // Email address of the user
                   "Phone": "$phone",                        // Phone (with the country code)
                   // optional fields. controls whether the user will be sent email, push etc.
                   "MSG-email": true,                        // Disable email notifications
                   "MSG-push": true,                         // Enable push notifications
                   "MSG-sms": true,                          // Enable sms notifications
                   "MSG-whatsapp": true,                     // Enable WhatsApp notifications
                 }
                })
JS;

                Yii::app()->clientScript->registerScript('clevertap', $js, CClientScript::POS_READY);
                Yii::app()->session->remove('clevertap_userLogin');
            }
        }
    }

    public function eventUserLogin() {
        Yii::app()->session->add('clevertap_userLogin', true);
    }

}