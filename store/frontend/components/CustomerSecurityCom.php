<?php

class CustomerSecurityCom extends MainCom {

    private $transition_period, $country, $phone;
    private $customers_id;
    private $max_request = 5;

    function CustomerSecurityCom($phone = '', $country = 0) {
        $this->country = $country;
        $this->phone = $phone;
        $this->transition_period = 0;
    }

    public function setCustomerId($customerId) {
        $this->customers_id = $customerId;
    }

    private function verifyTokenRequest($customerId, $update = FALSE, $tokenType = 'security_token_request') {
        $modelCustomersSetting = new CustomersSetting();
        $returnInt = FALSE;    // 1 = new request, 2 = reuse

        if ($customerId) {
            $tokenVerifyAttempt = $modelCustomersSetting->getTokenVerifyAttempt($customerId, $tokenType);
            if (isset($tokenVerifyAttempt->customers_setting_value)) {
                $currentRequestCnt = (int) $tokenVerifyAttempt->customers_setting_value;
                if (isset($tokenVerifyAttempt->same_day_request) && $tokenVerifyAttempt->same_day_request == '1') {
                    if (isset($tokenVerifyAttempt->active_request) && $tokenVerifyAttempt->active_request == '1') {    // Reuse
                        $returnInt = 2;
                    } else {
                        if ($currentRequestCnt >= $this->max_request) {
                            $returnInt = FALSE;
                        } else {
                            $returnInt = 1;
                            $currentRequestCnt += 1;

                            if ($update) {
                                $tokenData = array(
                                    'customers_setting_value' => $currentRequestCnt,
                                    'updated_datetime' => date("Y-m-d H:i:s")
                                );

                                $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $customerId, $tokenType);
                            }
                        }
                    }
                } else {
                    $returnInt = 1;

                    if ($update) {
                        $tokenData = array(
                            'customers_setting_value' => '1',
                            'updated_datetime' => date("Y-m-d H:i:s"),
                            'created_datetime' => date("Y-m-d H:i:s")
                        );

                        $modelCustomersSetting->updateTokenVerifyAttempt($tokenData, $customerId, $tokenType);
                    }
                }
            } else {
                $returnInt = 1;
                if ($update) {
                    $tokenData = array(
                        'customers_id' => $customerId,
                        'customers_setting_key' => $tokenType,
                        'customers_setting_value' => '1',
                        'created_datetime' => date("Y-m-d H:i:s"),
                        'updated_datetime' => date("Y-m-d H:i:s")
                    );

                    $modelCustomersSetting->saveCustomerSetting($tokenData);
                }
            }
        }
        return $returnInt;
    }

    function generateToken($noOfPin) {
        $codeLength = 0;
        $pin = '';

        while ($codeLength < $noOfPin) {
            $pin .= cpnRand(0, 9);
            $codeLength++;
        }

        return $pin;
    }

    function updateCustomerToken($customerId, $token, $requestType = 'security_token_request') {
        $modelCustomersOtp = new CustomersOtp();
        $tokenData = array('customers_id' => $customerId,
            'customers_otp_type' => $requestType,
            'customers_otp_digit' => $token,
            'customers_otp_request_date' => date('Y-m-d H:i:s')
        );

        $customerExists = $modelCustomersOtp->customerExists($customerId, $requestType);

        if ($customerExists) {
            $modelCustomersOtp->updateCustomerOpt($tokenData, $customerId, $requestType);
        } else {
            $modelCustomersOtp->saveCustomerOpt($tokenData);
        }
    }

    public function requestSecurityTokenAgainViaEmail($customerId) {
        $modelCustomersOtp = new CustomersOtp();
        $modelCustomersSetting = new CustomersSetting();
        $requestType = 'security_token_request';
        $requestStatus = $this->verifyTokenRequest($customerId, FALSE, $requestType);
        $responseResult = array();

        if ($requestStatus !== FALSE) {

            if ($requestStatus == '1') {   // create new security token when user attempt the token for the first time of the day
                $tokenVal = $this->generateToken(6);
                $this->updateCustomerToken($customerId, $tokenVal);
                $this->verifyTokenRequest($customerId, TRUE, $requestType);
            }

            $tokenInfo = $modelCustomersOtp->getTokenInfo($customerId);
            if (isset($tokenInfo->customers_otp_digit) && $tokenInfo->customers_otp_digit != '') {
                $tokenVal = $tokenInfo->customers_otp_digit;

                $resetCustomerSettingDataArray = array(
                    'customers_setting_value' => '1',
                    'updated_datetime' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d') + 1, date('Y'))),
                    'created_datetime' => date('Y-m-d H:i:s')
                );
                $modelCustomersSetting->updateTokenVerifyAttempt($resetCustomerSettingDataArray, Yii::app()->user->id, $requestType);

                $tokenDataArray = array(
                    'customers_otp_request_date' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d') + 1, date('Y')))
                );
                $modelCustomersOtp->updateCustomerOpt($tokenDataArray, $customerId, $requestType);
                
                $this->sendTokenViaEmail($tokenVal);
                
                $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_SUCCESSFULLY_SEND_MAIL');
            } else {
                $responseResult['res_text'] = '';
            }
        } else {
            $responseResult['res_text'] = Yii::t('smsToken', 'TEXT_REQUEST_TOKEN_FAIL_MSG');
        }

        $responseResult['res_code'] = $requestStatus;
        return $responseResult;
    }
    
    public function sendTokenViaEmail($tokenVal) {
        $modelCustomers = new Customers();
        
        if ($customerInfo = $modelCustomers->getCustomerInfo()) {
            $title = implode(' ', array(ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX'), Yii::t('smsToken', 'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION')));
            $emailGreeting = FrontendMailCom::getEmailGreeting($customerInfo->customers_firstname, $customerInfo->customers_lastname, '');
            $textContent = $emailGreeting . sprintf(Yii::t('smsToken', 'TEXT_SECURITY_TOKEN_RESET_CONTENT'), $tokenVal, 24) . "<br/><br/>" . ConfigurationCom::getValue('STORE_EMAIL_SIGNATURE');
            
            FrontendMailCom::send($customerInfo->customers_firstname . ' ' . $customerInfo->customers_lastname, $customerInfo->customers_email_address, $title, $textContent, ConfigurationCom::getValue('STORE_OWNER'), Yii::app()->params['MAIL_SENDER']['ALERT']);
            
            return array('email' => $customerInfo->customers_email_address);
        } else {
            return false;
        }
    }

}