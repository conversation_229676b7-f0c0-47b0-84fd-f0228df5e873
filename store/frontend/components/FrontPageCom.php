<?php

class FrontPageCom extends MainCom
{

    private $inner_array = array(), $inner_obj;
    public $currentUrl, $country_id, $currency_id, $language_id, $default_language_id, $customer_group_id, $extended_params, $customer_id, $customer_ip_country, $max_banner_container_allow_category = 10, $max_best_selling = 10, $recent_viewed_product_cookie_name = 'rvp', $recent_viewed_product_cookie_day = 30, $HEADER_KB_URL, $FOOTER_DEFAULT_TOLL_FREE, $FOOTER_SOCIAL_LINKEDIN_URL, $FOOTER_SOCIAL_TWITTER_URL, $FOOTER_SOCIAL_FB_URL, $FOOTER_SOCIAL_YOUTUBE_URL, $DISCLAIMER_URL, // used in product detail page
        $RSS_FEED_URL, $FOOTER_SOCIAL_WEIBO_URL, $FOOTER_SOCIAL_INSTAGRAM_URL, $FOOTER_SOCIAL_TIKTOK_URL, $FOOTER_SOCIAL_DOUYIN_URL, $FOOTER_SOCIAL_REDBOOK_URL;
    private $best_selling;

    public function _init($extended_params = array())
    {
        $this->extended_params = $extended_params;

        $this->currentUrl = Yii::app()->request->getHostInfo() . Yii::app()->request->getUrl();
        $this->country_id = $extended_params['country_id'] ?? $this->getRegional(
            'country'
        );
        $this->currency_id = $extended_params['currency_id'] ?? $this->getRegional(
            'currency'
        );
        $this->language_id = $extended_params['language_id'] ?? $this->getRegional(
            'language_id'
        );
        $this->default_language_id = $extended_params['default_language_id'] ?? $this->getRegional(
            'default_language_id'
        );
        $this->customer_group_id = $extended_params['customer_group_id'] ?? $this->getUser(
            'customers_groups_id'
        );

        $session = Yii::app()->session;

        $session_ip = (isset($session['customer_ip']) ? $session['customer_ip'] : $this->getIPAddress());
        $ip = $this->getIPAddress();

        if (!isset($session['customer_ip_country']) || $ip !== $session_ip) {
            if (isset(Yii::app()->geoip->getIPCountryInfo()['countries_iso_code_2'])) {
                $country_code = Yii::app()->geoip->getIPCountryInfo()['countries_iso_code_2'];
            } else {
                $country_code = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_COUNTRY_CODE'];
            }
            $this->customer_ip_country = $country_code;
            $session['customer_ip_country'] = $country_code;
            $session['customer_ip'] = $ip;
        } else {
            $this->customer_ip_country = $session['customer_ip_country'];
        }
    }

    public function getIPAddress()
    {
        if (!isset($this->inner_array['ip'])) {
            $this->inner_array['ip'] = getIPAddress();
        }

        return $this->inner_array['ip'];
    }

    public function getPageSupportLink()
    {
        if (!isset($this->inner_array['support_link'])) {
            if (is_array($this->HEADER_KB_URL)) {
                $this->inner_array['support_link'] = isset($this->HEADER_KB_URL[$this->language_id]) ? $this->HEADER_KB_URL[$this->language_id] : $this->HEADER_KB_URL[1];
            } else {
                $this->inner_array['support_link'] = $this->HEADER_KB_URL;
            }
        }

        return $this->inner_array['support_link'];
    }

    public function getURLInfo($key, $default = '')
    {
        if (!isset($this->inner_array['url_info'])) {
            $this->inner_array['url_info'] = array(
                'route' => Yii::app()->request->baseUrl . '/' . Yii::app()->request->pathInfo,
                'query' => CJSON::encode(getSafeQueryArray())
            );
        }

        return isset($this->inner_array['url_info'][$key]) ? $this->inner_array['url_info'][$key] : $default;
    }

    public function getBestSellingGameIDs()
    {
        // current best selling is based on category id instead of game id
        return $this->getCountryContent('tab_content');
    }

    public function getDBConfig($key, $default = '')
    {
        $return_str = '';

        if (!$return_str = ConfigurationCom::getValue($key)) {
            $return_str = $default;
        }

        return $return_str;
    }

    public function getPaymentMethodsAvailable()
    {
        $ctry_code = $this->getRegional('country_code');
        $cur_code = $this->getRegional('currency');

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'getPaymentMethodsAvailable/array/country_id/' . $ctry_code . '/currency_id/' . $cur_code;
        $cache_result = Yii::app()->cache->get($cache_key);
        if ($cache_result == false) {
            $cache_result = [];
            $curl = new CurlCom();
            $curl->setProxy(Yii::app()->params['PROXY']);
            $curl->timeout = 10;
            $url = Yii::app()->params['PG_URL'] . 'local-payment-method/' . $ctry_code . '/' . $cur_code;
            $resp = $curl->sendGet($url, '', true);
            if (!empty($resp) && isset($resp['pm_data'])) {
                foreach ($resp['pm_data'] as $image_data) {
                    if (isset($image_data['logo_url']) && isset($image_data['url_name'])) {
                        $cache_result[] = array(
                            'pg_info' => array(
                                'src' => $image_data['logo_url'],
                                'href' => $image_data['url_name']
                            )
                        );
                    }
                }
                Yii::app()->cache->set($cache_key, $cache_result, 86400);
            } else {
                // Cache Empty Result for 5 minutes
                Yii::app()->cache->set($cache_key, $cache_result, 300);
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => $url
                    )
                );
                Yii::app()->slack->send('Error Getting Payment Method Footer', $attachments, 'DEV_DEBUG');
            }
        }
        return $cache_result;
    }

    public function getCountryContent($key = '', $default = array())
    {
        if (!isset($this->inner_array['content'])) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'getCountryContent/array/country_id/' . $this->country_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== false) {
                $this->inner_array['content'] = $cache_result;
            } else {
                if ($content_row = CountriesContent::model()->getTabContent($this->country_id)) {
                    foreach ($content_row as $fieldname => $fieldval) {
                        switch ($fieldname) {
                            case 'tab_content':
                                $return_best_selling_array = array();
                                $data = CJSON::decode($content_row[$fieldname]);
                                $best_selling = array();

                                if (isset($data['best_selling'])) {
                                    $best_selling = $data['best_selling'];

                                    foreach ($best_selling as $gid) {
                                        $return_best_selling_array[$gid] = $gid;
                                    }
                                }

                                $this->inner_array['content']['tab_content'] = $return_best_selling_array;
                                unset($return_best_selling_array);
                                break;
                            case 'footer_all_payment_image':
                                break;
                            case 'supported_pg':
                                break;
                            default:
                                $this->inner_array['content'][$fieldname] = $content_row[$fieldname];
                                break;
                        }
                    }
                } else {
                    $this->inner_array['content'] = $default;
                }

                Yii::app()->cache->set($cache_key, $this->inner_array['content'], 86400); // cache 60 mins
            }
        }

        return $this->inner_array['content'][$key] ?? $default;
    }

    public function getHeaderBanner()
    {
        $return_array = $this->getCountryContentDescription('main_best_selling');
        return ($return_array[1] ?? '');
    }

    public function getMobileFooterBanner()
    {
        $return_array = $this->getCountryContentDescription('main_best_selling');
        return ($return_array[2] ?? '');
    }

    public function getWhatIsHotBanner()
    {
        $return_array = $this->getCountryContentDescription('main_best_selling');
        return array(($return_array[0] ?? ''), ($return_array[3] ?? ''));
    }

    public function getCountryContentDescription($array_key)
    {
        if (!isset($this->inner_array['description'])) {
            if ($content_row = CountriesContentDescription::model()->getBannerContent(
                $this->country_id,
                $this->language_id
            )) {
                foreach ($content_row as $fieldname => $fieldval) {
                    $array = array();

                    switch ($fieldname) {
                        case 'slider_content':
                            if (isset($content_row[$fieldname]) && $content_row[$fieldname]) {
                                $data = unserialize($content_row[$fieldname]);

                                if (notEmpty($data)) {
                                    foreach ($data as $num => $vals) {
                                        foreach ($vals as $key => $val) {
                                            $array[$num][$key] = $val;
                                        }
                                    }
                                }
                            }

                            $this->inner_array['description']['main_slider'] = $array;
                            break;

                        case 'banner_content':
                            if (isset($content_row[$fieldname]) && $content_row[$fieldname]) {
                                $data = unserialize($content_row[$fieldname]);

                                if (notEmpty($data)) {
                                    foreach ($data as $key => $val) {
                                        $array[] = $val;
                                    }
                                }
                            }

                            $this->inner_array['description']['main_best_selling'] = $array;
                            break;
                    }
                }
            }
        }

        return isset($this->inner_array['description'][$array_key]) ? $this->inner_array['description'][$array_key] : array();
    }

    public function getLoginURL($sns = '')
    {
        $params = array(
            'next_url' => $this->currentUrl
        );

        if ($sns !== '') {
            $params['sns'] = $sns;
        }

        return $this->createUrl("/sso/login/", $params);
    }

    public function getGameListsSortedByBestSelling($game_array, $best_selling)
    {
        $sorted_game_id_array = array_intersect($best_selling, $game_array);
        $unsorted_game_id_array = array_diff($game_array, $sorted_game_id_array);
        return $sorted_game_id_array + $unsorted_game_id_array;
    }

    public function getMegaMenuContent()
    {
        $game_array = $this->getGeneralBestSelling();
        $tree_info = Yii::app()->cTagCom->getFullTreeByLanguageID();
        $tree_array = $tree_info['tree'];
        return array(
            'tree' => $tree_array,
            'game' => $game_array
        );
    }

    public function getMainUIStaticIconURL($icon_filename)
    {
        return Yii::app()->assetManager->getPublishedUrl(
                ROOT_DIR . '/frontend/packages/main-ui'
            ) . '/images/icons/' . $icon_filename;
    }

    public function getMainUIStaticImageURL($icon_filename)
    {
        return Yii::app()->assetManager->getPublishedUrl(
                ROOT_DIR . '/frontend/packages/main-ui'
            ) . '/images/' . $icon_filename;
    }

    public function getUserBarContent()
    {
        $main_ui = Yii::app()->assetManager->publish(
            ROOT_DIR . '/frontend/packages/theme',
            $hashByName = false,
            $level = -1,
            Yii::app()->debug_mode
        );

        $sls_region = $this->getRegional('language');
        if(strtolower($sls_region) == "cn"){
            $sls_region = "zh-CN";
        }

        $return_array = array(
            'TEXT_OR_CONNECT' => Yii::t('page_content', 'TEXT_OR_CONNECT'),
            'REGIONAL_SETTING' => Yii::app()->params['REGIONAL_SETTING'],
            'region_info' => array(
                'form_url' => Yii::app()->createAbsoluteUrl('userBar/regional'),
                'form_desc' => Yii::t('page_content', 'TEXT_REGIONAL_NOTE'),
                'form_btn_label' => Yii::t('page_content', 'BTN_SAVE_CHANGES'),
                'ip_country_code' => $this->customer_ip_country,
                'country_code' => $this->getRegional('country_code'),
                'currency' => $this->getRegional('currency'),
                'language' => $this->getRegional('language'),
                'sls_language' => $sls_region,
                'language_name' => $this->getRegional('language_name'),
                'previous_url' => Yii::app()->frontPageCom->getURLInfo('route'),
                'previous_path' => (isset(Yii::app()->controller->module->id) ? Yii::app()->controller->module->id . '/' : '') . Yii::app()->controller->id . '/' . Yii::app()->controller->action->id
            ),
        );

        if (Yii::app()->user->id) {
            $return_array['loggedIn_info'] = array(
                'user_info' => array(
                    'id' => Yii::app()->user->id,
                    'customer_first_name' => $this->getUser('customers_firstname') . ' ' . $this->getUser(
                            'customers_lastname'
                        ),
                    'customers_email_address' => $this->getUser('customers_email_address'),
                    'customer_group' => $this->getUser('customers_groups_name'),
                    'avatar' => isset(Yii::app()->user->avatar) ? Yii::app()->user->avatar : $main_ui . '/img/no-image.jpg',
                    'menu' => array(
                        array('label' => Yii::t('page_content', 'TEXT_ACCOUNT_ID') . ' : ' . Yii::app()->user->id),
                        array(
                            'label' => Yii::t('page_content', 'TEXT_BUY_STATUS') . ' : ' . $this->getUser(
                                    'customers_groups_name'
                                )
                        ),
                        array('divider' => 1),
                        array(
                            'href' => Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/profile/index',
                            'label' => Yii::t('page_content', 'TEXT_MANAGE_PROFILE')
                        ),
                        array(
                            'href' => Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/social/connect',
                            'label' => Yii::t('page_content', 'TEXT_SOCIAL_CONNECT')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/storeCredit/index'),
                            'label' => Yii::t(
                                    'page_content',
                                    'TEXT_STORE_CREDITS'
                                ) . ' : <span class="header-profile__text-color ub-sc"><b><i>' . Yii::t(
                                    'myogm',
                                    'TEXT_IS_LOADING'
                                ) . '</i></b></span>'
                        ),
                        array(
                            'href' => Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/op/index',
                            'label' => Yii::t(
                                    'page_content',
                                    'TEXT_OP'
                                ) . ' : <span class="header-profile__actions-text-color ub-wor"><b><i>' . Yii::t(
                                    'myogm',
                                    'TEXT_IS_LOADING'
                                ) . '</i></b></span>'
                        ),
                        array('divider' => 1),
                        array(
                            'href' => $this->createUrl("/sso/logout/", ['next_url' => $this->currentUrl]),
                            'label' => Yii::t('page_content', 'TEXT_LOGOUT')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/account/index'),
                            'label' => Yii::t('page_content', 'TEXT_OVERVIEW')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/storeCredit/index'),
                            'label' => Yii::t('page_content', 'TEXT_TOPUP')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/account/viewOrders'),
                            'label' => Yii::t('page_content', 'TEXT_BUY_HISTORY')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/storeCredit/redeemGiftCard'),
                            'label' => Yii::t('page_content', 'TEXT_REDEEM_GIFT_CARD')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/storeCredit/statement'),
                            'label' => Yii::t('page_content', 'TEXT_SC_STATEMENT')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/account/tax'),
                            'label' => Yii::t('taxModule', 'TEXT_TAX')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/account/view-order-review'),
                            'label' => Yii::t('page_content', 'TEXT_REVIEW')
                        ),
                        array(
                            'href' => Yii::app()->createUrl('/storeCredit/index'),
                            'label' => Yii::t('page_content', 'TEXT_STORE_CREDITS')
                        ),
                    )
                ),
            );
        } else {
            $return_array['loggedout_info'] = array(
                'signup_info' => array(
                    'title' => Yii::t('page_content', 'TEXT_SIGN_UP'),
                    'href' => $this->createUrl("/sso/signup/", ['next_url' => $this->currentUrl])
                ),
                'login_info' => array(
                    'href' => $this->getLoginURL(),
                    'label' => Yii::t('page_content', 'TEXT_LOG_IN'),
                    'sns' => array(
                        array(
                            'type' => 'fb',
                            'href' => $this->getLoginURL('facebook')
                        ),
                        array(
                            'type' => 'tw',
                            'href' => $this->getLoginURL('twitter')
                        ),
                        array(
                            'type' => 'gp',
                            'href' => $this->getLoginURL('google')
                        ),
                    )
                )
            );
        }

        return $return_array;
    }

    public function getRecentViewedProductCookieIDs($limit = null)
    {
        $return_array = array();
        $return_product_array = array();

        if (isset(Yii::app()->request->cookies[$this->recent_viewed_product_cookie_name])) {
            $recent_viewed_arr = explode(
                ',',
                strip_tags(Yii::app()->request->cookies[$this->recent_viewed_product_cookie_name])
            );
            $counter = 0;

            foreach ($recent_viewed_arr as $id) {
                $id_array = explode(':', $id . ':0:0:0', 3);
                list($pid, $dm, $pm) = $id_array;

                if (($pid = (int)$pid) && ($dm = (int)$dm)) {
                    $counter += 1;
                    $return_array[$pid . ":" . $dm] = array(
                        'idx' => $counter,
                        'key' => $pid . ":" . $dm . ":0"
                    );
                    $return_product_array[$pid] = $pid;

                    if ($counter === $limit) {
                        break;
                    }
                }
            }
        }

        return $return_array ? array(
            $return_array,
            $return_product_array
        ) : array();
    }

    public function registerRecentViewedProduct($targetId)
    {
        if ($sp_raw = $this->getRecentViewedProductCookieIDs()) {
            $sp = array_values($sp_raw[0]);
            $new_sp = array();

            foreach ($sp as $idx => $id) {
                if ($id['key'] !== $targetId) {
                    $new_sp[] = $id['key'];
                }
            }

            array_unshift($new_sp, $targetId);
            $sp = array_slice($new_sp, 0, 10);
        } else {
            $sp = array(
                $targetId
            );
        }

        $cookie = new CHttpCookie($this->recent_viewed_product_cookie_name, implode(',', $sp));
        $cookie->expire = time() + 60 * 60 * 24 * $this->recent_viewed_product_cookie_day;
        $cookie->path = "/";
        Yii::app()->request->cookies[$this->recent_viewed_product_cookie_name] = $cookie;
    }

    public function getCategoryObj($reload = false, $extended_params = array())
    {
        if (!isset($this->inner_obj['cat']) || $reload) {
            $this->inner_obj['cat'] = new FrontendCategoryCom($this->language_id, $this->default_language_id);
        }

        return $this->inner_obj['cat'];
    }

    public function getBestSelling($type, $limit = 6)
    {
        if (empty($this->best_selling)) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/best-selling/' . Yii::app()->session['language_id'] . '/' . Yii::app()->session['country_code'];
            $cache_result = Yii::app()->cache->get($cache_key);
            if ($cache_result === false) {
                $data = (new MsProductModel)->getBestSelling(
                    Yii::app()->session['language_id'],
                    Yii::app()->session['country_code'],
                    Yii::app()->frontPageCom->customer_ip_country
                );
                if (!empty($data)) {
                    $this->best_selling = $data;
                    Yii::app()->cache->set($cache_key, $data, 86400);
                }
            } else {
                $this->best_selling = $cache_result;
            }
        }
        return (!empty($this->best_selling[$type]) ? array_slice($this->best_selling[$type], 0, $limit) : []);
    }

    public function getGeneralBestSelling()
    {
        return $this->getBestSelling('general', 10);
    }

    public function getVoucherBestSelling()
    {
        return $this->getBestSelling('voucher');
    }

    public function getDTUBestSelling()
    {
        return $this->getBestSelling('dtu');
    }

    public function getPhysicalGoodsSelling()
    {
        return $this->getBestSelling('physical_goods');
    }

    public function getPageContentObj($reload = false, $extended_params = array())
    {
        if (!isset($this->inner_obj['pgcon']) || $reload) {
            $this->inner_obj['pgcon'] = new FrontendPageContentCom();
        }
        return $this->inner_obj['pgcon'];
    }

    public function getProductObj($reload = false, $extended_params = array())
    {
        if (!isset($this->inner_obj['prod']) || $reload) {
            $this->inner_obj['prod'] = new FrontendProductCom();
            $this->inner_obj['prod']->_init($this->extended_params);
        }

        return $this->inner_obj['prod'];
    }

    public function clearRecentlyPurchasedCache()
    {
        $c_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'frontpage-recent-purchase/' . Yii::app()->user->id;
        Yii::app()->cache->delete($c_key);
    }

    public function registerEmbedlyAssets()
    {
        Yii::app()->clientScript->registerScriptFile(
            "//cdn.embedly.com/widgets/platform.js",
            CClientScript::POS_END,
            array(
                'defer' => true,
                'charset' => 'UTF-8'
            )
        );

        $css = <<<STYLE
        <script>
            function registerEmbedly(url){
                document.getElementsByClassName('description__preview')[0].insertAdjacentHTML('beforeend','<blockquote class="embedly-card" data-card-controls="0"><h4><a href="'+url+'"></a></h4><p></p></blockquote>');
            }
        </script>
        <style class="embedly-css">
            .card .feed-entry h4 {
                    font-size: 13px !important;
            }

            #embdscl0 {
                display: none !important;
            }
        </style>
STYLE;
        return $css;
    }

    public function __destruct()
    {
        unset($this->inner_array, $this->inner_obj);
    }

    protected function createUrl($path, $params = []) {
        $url = Yii::app()->createUrl($path);

        if($params) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }

}
