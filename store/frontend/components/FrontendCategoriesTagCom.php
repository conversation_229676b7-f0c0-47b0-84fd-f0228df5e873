<?php

class FrontendCategoriesTagCom extends CategoriesTagCom {

    public function getGameIDByTagIDs($tag_id_array) {
        $return_array = array();

        foreach ($tag_id_array as $tag_id) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesTagmapBase::model()->tableName() . '/game_id/integer/tag_id/' . $tag_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== FALSE) {
                $result = $cache_result;
            } else {
                $result = $this->getGameIDByTagID($tag_id);
                Yii::app()->cache->set($cache_key, $result, 86400);
            }

            $return_array[$tag_id] = $result;
        }

        return $return_array;
    }

    private function getAllDescriptionInfo(&$tree_array, $language_id = NULL, $default_language_id = NULL, &$tag_id_array = array()) {
        foreach ($tree_array as $idx => $tree) {
            if ($tree['depth'] > 1) {
                $tag_id_array[] = $tree['tag_id'];
            }

            $tree_array[$idx]['info'] = $this->getDescriptionByTagID($tree['tag_id'], $language_id, $default_language_id);
            $tree_array[$idx]['link'] = Yii::app()->createUrl('search/index', array('keyword' => Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE')));
            $this->getAllDescriptionInfo($tree_array[$idx]['children'], $language_id, $default_language_id, $tag_id_array);
        }

        return $tree_array;
    }

    public function getFullTreeByLanguageID($language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'full_tree/array/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $tag_id_array = array();
            $full_tree = $this->getFullTree();
            $return_array = array(
                'tree' => $this->getAllDescriptionInfo($full_tree, $language_id, $default_language_id, $tag_id_array),
                'game_ids' => $this->getGameIDByTagIDs($tag_id_array)
            );

            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        return $return_array;
    }

    public function getTagIDByGameID($game_id) {
        $return_int = 0;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesTagmapBase::model()->tableName() . '/tag_id/integer/game_id/' . $game_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_int = $cache_result;
        } else {
            $return_int = parent::getTagIDByGameID($game_id);

            Yii::app()->cache->set($cache_key, $return_int, 86400);
        }

        return $return_int;
    }

    function getGameIDFilteredByTagsID($tag_array){
        $game_array = array();
        foreach($tag_array as $tag){
            $game_array = array_merge($game_array,$this::getGameIDFilteredByTagIDAndChild($tag));
        }
        return $game_array;
    }

    public function getGameIDFilteredByTagIDAndChild($tag_id) {
        $return_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesTagmapBase::model()->tableName() . '/all_game_ids/array/tag_id/' . $tag_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $return_array = parent::getGameIDFilteredByTagIDAndChild($tag_id);
            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        return $return_array;
    }

    public function getSinglePathByTagID($tag_id) {
        $return_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesTagBase::model()->tableName() . '/single_path/array/tag_id/' . $tag_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $return_array = parent::getSinglePathByTagID($tag_id);
            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        return $return_array;
    }

    public function getDescriptionByTagID($tag_id, $language_id = NULL, $default_language_id = NULL, $field = NULL) {
        $return_array = array();
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesTagDescriptionBase::model()->tableName() . '/tag_info/array/tag_id/' . $tag_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $return_array = parent::getDescriptionByTagID($tag_id, $language_id, $default_language_id);
            Yii::app()->cache->set($cache_key, $return_array, 86400);
        }

        return $field !== NULL ? $return_array[$field] : $return_array;
    }

}