<?php

class FrontendCategoryCom extends CategoryCom {

    private $category_id,
            $language_id,
            $default_language_id,
            $parent_id;

    public function __construct($language_id = NULL, $default_language_id = NULL) {
        $this->language_id = notNull($language_id) ? $language_id : $this->getRegional('language_id');
        $this->default_language_id = notNull($default_language_id) ? $default_language_id : $this->getRegional('default_language_id');
    }

    public function isCategoryIDActive($category_id) {
        $return_bool = 0;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Categories::model()->tableName() . '/categories_status/array/categories_id/' . $category_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_bool = $cache_result;
        } else {
            // active
            $return_bool = parent::isCategoryIDActive($category_id);
            Yii::app()->cache->set($cache_key, $return_bool, 900);
        }

        return $return_bool;
    }

    public function setCategoryID($category_id) {
        if (!is_null($category_id)) {
            $this->category_id = $category_id;
        }
    }

    public function getCategoriesUrlAlias($cid) {
        $return_string = '';
        
        #key:categories/get/categories_url_alias/categories_id/xxx
        $cache_key = Categories::model()->tableName() . '/get/categories_url_alias/categories_id/' . $cid;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_string = $cache_result;
        } else {
            if ($return_string = Categories::model()->getCategoriesUrlAlias($cid)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400); // cache 1 day
            }
        }

        return $return_string;
    }

    public function getCategoriesIDByUrlAlias($url_alias) {
        $return_int = 0;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Categories::model()->tableName() . 'categories_id/array/categories_url_alias/' . $url_alias;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_int = $cache_result;
        } else {
            if ($return_int = Categories::model()->getCategoriesIDByCategoriesUrlAlias($url_alias)) {
                Yii::app()->cache->set($cache_key, $return_int, 86400); // cache 1 day
            }
        }

        return $return_int;
    }

    public function getCPathByID($category_id = NULL) {
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;

        return parent::getCPathByID($category_id);
    }

    public function getCPathByParentID($parent_id = NULL) {
        $parent_id = $parent_id !== NULL ? $parent_id : $this->parent_id;

        return parent::getCPathByParentID($parent_id);
    }

    // function get_available_product_child_type
    public function getCPTCIDs($category_id = NULL) {
        $return_array = array();
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;

        #key:categories_product_types/custom_products_type_child_id/array/categories_id/xxx
        $cache_key = CategoriesProductTypes::model()->tableName() . '/custom_products_type_child_id/array/categories_id/' . $category_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            if ($return_array = parent::getCPTCIDs($category_id)) {
                Yii::app()->cache->set($cache_key, $return_array, 86400);
            }
        }

        return $return_array;
    }

    # enhancement (exclude inactive category ID from the response)
    public function getCustomerGroupCategoryIDs($customers_groups_id) {
        $result_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . CategoriesGroups::model()->tableName() . '/category_id/array/customers_groups_id/' . $customers_groups_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $result_array = $cache_result;
        } else {
            // active
            $result_array = parent::getCustomerGroupCategoryIDs($customers_groups_id);
            Yii::app()->cache->set($cache_key, $result_array, 900);
        }

        return $result_array;
    }

    public function getHeadingTitle($category_id = NULL, $language_id = NULL, $default_languages_id = NULL) {
        $return_str = '';
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;
        $language_id = $language_id !== NULL ? $language_id : $this->language_id;
        $default_languages_id = $default_languages_id !== NULL ? $default_languages_id : $this->default_language_id;
        
        #key:categories_description/get/categories_heading_title/categories_id/xxx/language/xxx
        $cache_key = CategoriesDescription::model()->tableName() . '/get/categories_heading_title/categories_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            if ($return_str = parent::getHeadingTitle($category_id, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_str, 86400);
            }
        }

        return $return_str;
    }

    public function getName($category_id = NULL, $language_id = NULL, $default_languages_id = NULL) {
        $return_string = '';
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;
        $language_id = $language_id !== NULL ? $language_id : $this->language_id;
        $default_languages_id = $default_languages_id !== NULL ? $default_languages_id : $this->default_language_id;
        
        #key:categories_description/get/categories_name/categories_id/xxx/language/xxx
        $cache_key = CategoriesDescription::model()->tableName() . '/get/categories_name/categories_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_string = $cache_result;
        } else {
            if ($return_string = parent::getName($category_id, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    public function getCategoriesImage($category_id = NULL, $language_id = NULL, $default_languages_id = NULL) {
        $return_str = '';
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;
        $language_id = $language_id !== NULL ? $language_id : $this->language_id;
        $default_languages_id = $default_languages_id !== NULL ? $default_languages_id : $this->default_language_id;

        #key:categories_description/get/categories_image/categories_id/xxx/language/xxx
        $cache_key = CategoriesDescription::model()->tableName() . '/get/categories_image/categories_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            $return_str = parent::getCategoriesImage($category_id, $language_id, $default_languages_id);
            $return_str = Yii::app()->ImgCom->getCategoryDimension($return_str);
            Yii::app()->cache->set($cache_key, $return_str, 86400);
        }

        return array('title' => '', 'dimension' => $return_str);
    }

    public function getCategoriesImageTitle($category_id, $language_id = NULL, $default_languages_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : $this->language_id;
        $default_languages_id = $default_languages_id !== NULL ? $default_languages_id : $this->default_language_id;

        #key:categories_description/get/categories_image_title/categories_id/xxx/language/xxx
        $cache_key = CategoriesDescription::model()->tableName() . '/get/categories_image_title/categories_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            if ($return_str = parent::getCategoriesImageTitle($category_id, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_str, 86400);
            }
        }

        return $return_str;
    }

    public function getPinYin($category_id = NULL, $language_id = NULL, $default_languages_id = NULL) {
        $return_string = '';
        $category_id = $category_id !== NULL ? $category_id : $this->category_id;
        $language_id = $language_id !== NULL ? $language_id : $this->language_id;
        $default_languages_id = $default_languages_id !== NULL ? $default_languages_id : $this->default_language_id;
        
        #key:categories_description/get/categories_pin_yin/categories_id/xxx/language/xxx
        $cache_key = CategoriesDescription::model()->tableName() . '/get/categories_pin_yin/categories_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_string = $cache_result;
        } else {
            if ($return_string = parent::getPinYin($category_id, $language_id, $default_languages_id)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400);
            }
        }

        return $return_string;
    }

    public function getCategoriesInfoByCPT($pass_path, $product_types_id) {
        $return_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Categories::model()->tableName() . '/product_type_category_info/array/custom_products_type_id/' . $product_types_id . '/categories_parent_path/' . $pass_path;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            if ($return_array = parent::getCategoriesInfoByCPT($pass_path, $product_types_id)) {
                Yii::app()->cache->set($cache_key, $return_array, 86400);
            }
        }

        return $return_array;
    }

    public function getCategoriesInfoByCPTC($pass_path, $product_child_types_id) {
        $return_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Categories::model()->tableName() . '/product_type_category_info/array/custom_products_type_child_id/' . $product_child_types_id . '/categories_parent_path/' . $pass_path;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            if ($return_array = parent::getCategoriesInfoByCPTC($pass_path, $product_child_types_id)) {
                Yii::app()->cache->set($cache_key, $return_array, 86400);
            }
        }

        return $return_array;
    }

    // get 3rd layer id
    public function getCategoryInfoByGameID($game_id, $product_types_id = 2) {
        $return_array = array();
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Categories::model()->tableName() . '/product_type_category_info/array/custom_products_type_id/' . $product_types_id . '/game_id/' . $game_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            // can add check category status here
            $return_array = $cache_result;
        } else {
            $return_array = parent::getCategoryInfoByGameID($game_id, $product_types_id);
            Yii::app()->cache->set($cache_key, $return_array, 900);    // 6 hours
        }

        return $return_array;
    }

}