<?php

class FrontendCustomerCom extends CustomerCom {

    public function isBillingInfoCompleted($customer_id = 0) {
        $return_bool = FALSE;
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array();

        $api_response = self::_callAPI('profile/isBillingInfoComplete', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_bool = $api_response['result'];
        }

        return $return_bool;
    }

    public function getConfirmBillingInfoContent($country, $customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
            'country' => $country,
        );

        $api_response = self::_callAPI('profile/getConfirmBillingInfoContent', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function getCountryByIP($ip = null) {
        $ipAddress = $ip !== null ? $ip : Yii::app()->frontPageCom->getIPAddress();

        $login_ip = '';
        $login_ip_country = '';

        if (isset(Yii::app()->session['login_country_iso_code_2']) && !empty(Yii::app()->session['login_country_iso_code_2'])) {
            $login_ip = Yii::app()->session['customers_login_ip'];
            $login_ip_country = Yii::app()->session['login_country_iso_code_2'];
        } else if (isset(Yii::app()->session['customers_login_ip'])) {
            $login_ip = Yii::app()->session['customers_login_ip'];
            $countryInfo = Yii::app()->geoip->getIPCountryInfo($login_ip);

            if (isset($countryInfo['countries_iso_code_2'])) {
                Yii::app()->session['login_country_iso_code_2'] = $countryInfo['countries_iso_code_2'];
            }
        }

        # cross checking on login IP country and current IP country when confirm want to checkout
        $countryInfo = Yii::app()->geoip->getIPCountryInfo($ipAddress);
        $current_ip_country_iso_2 = isset($countryInfo['countries_iso_code_2']) ? $countryInfo['countries_iso_code_2'] : '';

        return $current_ip_country_iso_2;
    }

    public function updateConfirmBillingInfo($extraParams, $customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams['language'] = $language_code;

        $api_response = self::_callAPI('profile/updateConfirmBillingInfo', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function requestPhoneVerifyingToken($customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
        );

        $api_response = self::_callAPI('profile/requestPhoneVerifyingToken', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function requestResendPhoneVerifyingToken($customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
        );

        $api_response = self::_callAPI('profile/RequestPhoneVerifyingTokenResend', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function requestSecurityToken($customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
        );

        $api_response = self::_callAPI('profile/requestSecurityToken', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function requestResendSecurityToken($customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
        );

        $api_response = self::_callAPI('profile/requestSecurityTokenResend', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function requestSecurityTokenByLastPhoneDigit($lastDigit, $customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
            'lastDigit' => $lastDigit
        );

        $api_response = self::_callAPI('profile/generateEmailSecurityToken', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function validatePassword($password, $customer_id = 0) {
        $return_str = '';
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'password' => $password,
        );

        $api_response = self::_callAPI('profile/validatePassword', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_str = $api_response['result'];
        }

        return $return_str;
    }

    public function validatePhoneVerifyingToken($token, $customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
            'token' => $token,
        );

        $api_response = self::_callAPI('profile/validatePhoneVerifyingToken', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

    public function validateSecurityToken($token, $customer_id = 0, $language_code = '') {
        $return_array = array();
        $customer_id = $this->getCustomerID($customer_id);
        $extraParams = array(
            'language' => $language_code,
            'token' => $token,
        );

        $api_response = self::_callAPI('profile/validateSecurityToken', $customer_id, '', $extraParams);

        if (isset($api_response['status']) && $api_response['status']) {
            $return_array = $api_response['result'];
        }

        return $return_array;
    }

}
