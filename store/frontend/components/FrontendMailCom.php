<?php

class FrontendMailCom extends MailCom
{

    public static function getEmailGreeting($firstname, $lastname, $gender = '')
    {
        $_result = '';

        if (ConfigurationCom::getValue('ACCOUNT_GENDER') == 'true') {
            if ($gender == 'm') {
                $_result = sprintf(Yii::t('page_content', 'EMAIL_GREET_MR'), $lastname);
            } else if ($gender == 'f') {
                $_result = sprintf(Yii::t('page_content', 'EMAIL_GREET_MS'), $lastname);
            } else {
                $_result = sprintf(Yii::t('page_content', 'EMAIL_GREET_NONE'), $firstname);
            }
        } else {
            $_result = sprintf(Yii::t('page_content', 'EMAIL_GREET_NONE'), $firstname);
        }

        return $_result;
    }


    // mbstring module related
    public static function mbConvertEncoding($str, $toEncoding, $fromEncoding)
    {
        $doEncoding = true;
        if (extension_loaded('mbstring')) {
            $toEncoding = strtoupper($toEncoding);
            $fromEncoding = strtoupper($fromEncoding);

            if (!empty($fromEncoding)) {
                if (mb_detect_encoding($str) != $fromEncoding) {
                    $doEncoding = false;
                }
            } else {
                $fromEncoding = mb_detect_encoding($str);
            }
            if ($doEncoding) {
                $str = mb_convert_encoding($str, $toEncoding, $fromEncoding);
            }
        }

        return $str;
    }

    public static function parseEmailString($emailString)
    {
        $emailArray = !empty($emailString) ? explode(',', $emailString) : array();
        $emailPattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
        $emailReceivers = array();

        for ($receiverCnt = 0; $receiverCnt < count($emailArray); $receiverCnt++) {
            if (preg_match($emailPattern, $emailArray[$receiverCnt], $regs)) {
                $receiverName = trim($regs[1]);
                $receiverEmail = trim($regs[2]);

                $emailReceivers[] = array('name' => $receiverName, 'email' => $receiverEmail);
            }
        }

        return $emailReceivers;
    }

}