<?php

class FrontendPageContentCom extends PageContentCom 
{
    public $data = array(),
            $tpl_id,
            $id,
            $id_type;
	
    public function _init($id = NULL, $id_type = NULL) {
        $this->id = $id;
        $this->id_type = $id_type;
    }

    public function getAllCategoryIDByGameBlogID($game_blog_id = NULL) {
        $game_blog_id = $game_blog_id !== NULL ? $game_blog_id : $this->id;
        
        return parent::getAllCategoryIDByGameBlogID($game_blog_id);
    }
    
    public function getAllGamesByCategoryID($category_id = NULL) {
        $category_id = $category_id !== NULL ? $category_id : $this->id;
        
        return parent::getAllGamesByCategoryID($category_id);
    }
    
    public function getAllInfo($id = NULL, $id_type = NULL, $language_id = NULL) {
        $id = $id !== NULL ? $id : $this->id;
        $id_type = $id_type !== NULL ? $id_type : $this->id_type;
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        
        if (!isset($this->data[$id.$id_type.$language_id])) {
            if ($template_data = $this->getTemplateInfoByIDByType($id, $id_type)) {
                $this->data[$id.$id_type.$language_id]['game'] = $template_data;
                $this->tpl_id = $template_data['tpl_id'];
                
                if ($language_id !== Yii::app()->frontPageCom->default_language_id) {
                    $this->data[$id.$id_type.$language_id]['game_detail']['default'] = $this->getTemplateLangInfoByTplIDByLanguageID($this->tpl_id, Yii::app()->frontPageCom->default_language_id);
                }
                
                $this->data[$id.$id_type.$language_id]['game_detail']['current'] = $this->getTemplateLangInfoByTplIDByLanguageID($this->tpl_id, $language_id);
                $this->data[$id.$id_type.$language_id]['game_info'] = $this->getAllGameInfoByTplID($this->tpl_id);
            }
        }
        
        return isset($this->data[$id.$id_type.$language_id]) ? $this->data[$id.$id_type.$language_id] : array();
    }
    
    public function getAllGameInfoByTplID($tpl_id = NULL) {
        $tpl_id = $tpl_id !== NULL ? $tpl_id : $this->tpl_id;
        
        return parent::getAllGameInfoByTplID($tpl_id);
    }
    
    public function getGameValue($key, $default = '') {
        $data = $this->getAllInfo();
        return (isset($data['game'][$key]) && !empty($data['game'][$key])) ? $data['game'][$key] : $default;
    }

    public function getGameInfo() {
        $return_array = array();
        
        if ($data = $this->getAllInfo()) {
            $return_array = array(
                'game_title' => $this->getGameDetailInfo('game_title', '-'),
                'game_publisher' => $this->getGameDetailInfo('game_publisher', '-'),
                'game_developer' => $this->getGameDetailInfo('game_developer', '-'),
                'game_release_date' => $this->getGameDetailInfo('game_release_date', '-'),
            );

            foreach ($data['game_info'] as $type => $ids_arr) {
                    $return_array[$type] = $this->getGameInfoDetail($type, $ids_arr);
            }
        }

        return $return_array;
    }
    
    public function getGameInfoDetail($type, $ids, $language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;
        
        return parent::getAllGameInfoDetail($type, $ids, $language_id, $default_language_id);
    }
    
    public function getGenreDescription($game_genre_id, $language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;
        
        return parent::getGenreDescription($game_genre_id, $language_id, $default_language_id);
    }
    
    public function getLanguageDescription($game_language_id, $language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;
        
        return parent::getLanguageDescription($game_language_id, $language_id, $default_language_id);
    }
    
    public function getPlatformDescription($game_platform_id, $language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;
        
        return parent::getPlatformDescription($game_platform_id, $language_id, $default_language_id);
    }
    
    public function getRegionDescription($game_region_id, $language_id = NULL, $default_language_id = NULL) {
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;
        
        return parent::getRegionDescription($game_region_id, $language_id, $default_language_id);
    }
    
    public function getTemplateInfoByIDByType($id = NULL, $id_type = NULL) {
        $id = $id !== NULL ? $id : $this->id;
        $id_type = $id_type !== NULL ? $id_type : $this->id_type;
        
        return parent::getTemplateInfoByIDByType($id, $id_type);
    }
    
    public function getTemplateLangInfoByTplIDByLanguageID($tpl_id = NULL, $language_id = NULL) {
        $tpl_id = $tpl_id !== NULL ? $tpl_id : $this->tpl_id;
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        
        return parent::getTemplateLangInfoByTplIDByLanguageID($tpl_id, $language_id);
    }
    
    public function getRelatedLink() {
        $return_array = array();
        
        if ($data = $this->getGameDetailInfo('related_link_info')) {
            $return_array = json_decode($data, true);
        }
        
        return $return_array;
    }

    public function getGameDetailInfo($key, $default = '') {
        $data = $this->getAllInfo();
        
        return (isset($data['game_detail']['current'][$key]) && notEmpty($data['game_detail']['current'][$key])) ? $data['game_detail']['current'][$key] : 
            ((isset($data['game_detail']['default'][$key]) && notEmpty($data['game_detail']['default'][$key])) ? $data['game_detail']['default'][$key] : $default);
    }

    public function getCategoriesDescription($category_id = NULL, $language_id = NULL, $default_language_id = NULL) {
        $return_str = '';
        $game_blog_id = $category_id !== NULL ? $category_id : $this->id;
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . GameBlogDescriptionBase::model()->tableName() . '/categories_description/string/category_id/' . $category_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            if ($return_str = parent::getCategoriesDescription($game_blog_id, $language_id, $default_language_id)) {
                Yii::app()->cache->set($cache_key, $return_str, 86400);
            }
        }

        return $return_str;
    }
    
    public function getGameBlogDescription($game_blog_id = NULL, $language_id = NULL, $default_language_id = NULL) {
        $return_str = '';
        $game_blog_id = $game_blog_id !== NULL ? $game_blog_id : $this->id;
        $language_id = $language_id !== NULL ? $language_id : Yii::app()->frontPageCom->language_id;
        $default_language_id = $default_language_id !== NULL ? $default_language_id : Yii::app()->frontPageCom->default_language_id;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . GameBlogDescriptionBase::model()->tableName() . '/game_blog_description/string/game_blog_id/' . $game_blog_id . '/language/' . $language_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            if ($return_str = parent::getGameBlogDescription($game_blog_id, $language_id, $default_language_id)) {
                Yii::app()->cache->set($cache_key, $return_str, 86400);
            }
        }
        
        return $return_str;
    }

    public function getGameBlogCustomUrl($game_blog_id = NULL) {
        $return_str = '';
        $game_blog_id = $game_blog_id !== NULL ? $game_blog_id : $this->id;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . GameBlogBase::model()->tableName() . '/game_blog/string/game_blog_id/' . $game_blog_id;
        $cache_result = Yii::app()->cache->get($cache_key);
        
        if ($cache_result !== FALSE) {
            $return_str = $cache_result;
        } else {
            if ($return_str = parent::getGameBlogCustomUrl($game_blog_id)) {
                Yii::app()->cache->set($cache_key, $return_str, 86400);
            }
        }
        return $return_str;
    }
    
}