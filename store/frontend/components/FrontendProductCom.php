<?php

class FrontendProductCom extends ProductCom
{
    public function _init($extended_param = array())
    {
        parent::_init($extended_param);
    }

    public function deactivateProduct($product_id)
    {
        $game_id = $this->getGameIDByID($product_id);

        if (parent::deactivateProduct($product_id)) {
            # clear memcache
            if ($game_id) {
                #key:category_and_products/array/category_id/xxx
                $cache_key = 'category_and_products/array/category_id/' . $game_id;
                Yii::app()->cache->delete($cache_key);

                if ($game_id_array = Yii::app()->frontPageCom->getPageContentObj()->getAllGamesByCategoryID($game_id)) {
                    foreach ($game_id_array as $game_blog_id) {
                        #key:category_and_products/array/game_blog_id/xxx/category_id/xxx
                        $cache_key = 'category_and_products/array/game_blog_id/' . $game_blog_id . '/category_id/' . $game_id;
                        Yii::app()->cache->delete($cache_key);
                    }
                }
            }

            # send notification
            $cat_cfg_array = ConfigurationCom::getCategoryConfigValueByProductID($product_id, 'STOCK_ZERO_PRICE_PRODUCT_EMAIL');
            if (isset($cat_cfg_array['STOCK_ZERO_PRICE_PRODUCT_EMAIL'])) {
                $from_name = ConfigurationCom::getValue('STORE_OWNER');
                $from_email = ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS');
                $email_subject = implode(' ', array(ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX'), 'Zero Price Product Detected And Deactivated'));
                $cPath_array = Yii::app()->frontPageCom->getProductObj()->getCPathByID($product_id);
                $product_id_link = '<a href="' . Yii::app()->params['OG_CREW_DOMAIN'] . '?cPath=' . implode('_', $cPath_array) . '&pID=' . $product_id . '&selected_box=catalog' . '"  target="_blank">' . $product_id . '</a>';

                $email_contents = Yii::t('page_content', 'EMAIL_PRODUCT_ZERO_AMOUNT_NOTIFICATION_CONTENT', array(
                    '{PRODUCT_ID}' => $product_id_link,
                    '{PRODUCT_NAME}' => $this->getProductsName($product_id, 1),
                    '{PRICE_FROM}' => 0,
                    '{PRICE_TO}' => 0 . ' (Activated)',
                    '{UPDATE_DATE}' => date("Y-m-d H:i:s"),
                    '{UPDATE_IP}' => getIPAddress(),
                    '{UPDATE_USER}' => 'OGM system'
                ));
                $email_to_array = parseEmailString($cat_cfg_array['STOCK_ZERO_PRICE_PRODUCT_EMAIL']);

                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                    FrontendMailCom::send($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $email_subject, $email_contents, $from_name, $from_email);
                }
            }
        }
    }

    private function getProductImage($product_id, $category_id = null, $language_id = null, $default_languages_id = null)
    {
        $img_array = array();
        $image_title = '';
        $img_alt = '';

        if ($filename = $this->getProductsImage($product_id, $language_id, $default_languages_id)) {
            $image_title = $this->getProductsImageTitle($product_id, $language_id, $default_languages_id);

            if ($image_dim = Yii::app()->ImgCom->getProductDimension($filename, '')) {
                $img_array = array('title' => $image_title, 'dimension' => $image_dim);
                $img_alt = $image_title;
            }
        }

        if ($category_id) {
            if ($img_array == array()) {
                $img_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesImage($category_id, $language_id, $default_languages_id);

                if ($image_title == '') {
                    $img_array['title'] = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesImageTitle($category_id, $language_id, $default_languages_id);
                } else {
                    $img_array['title'] = $image_title;
                }
            }
            if (empty($img_alt)) {
                $img_alt = Yii::app()->frontPageCom->getCategoryObj()->getName($category_id, $language_id, $default_languages_id);
            }
        }

        $img_array['alt'] = strtolower(str_replace(array('(', ')'), '', $img_alt));

        return $img_array;
    }

    public function getProductPromotionImage($product_id, $category_id = null, $language_id = null, $default_languages_id = null)
    {
        $img_array = array();

        if ($filename = $this->getProductsPromotionImage($product_id, $language_id, $default_languages_id)) {
            $image_title = $this->getProductsPromotionImageTitle($product_id, $language_id, $default_languages_id);

            if ($image_dim = Yii::app()->ImgCom->getProductDimension($filename, '')) {
                $img_array = array('title' => $image_title, 'dimension' => $image_dim);

                $img_array['alt'] = strtolower(str_replace(array('(', ')'), '', Yii::app()->frontPageCom->getCategoryObj()->getName($category_id, $language_id, $default_languages_id)));
            }
        }

        if ($img_array == array()) {
            $img_array = $this->getProductImage($product_id, $category_id, $language_id, $default_languages_id);
        }

        return $img_array;
    }

    public function getProductsUrlAlias($pid)
    {
        $return_string = '';
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Products::model()->tableName() . 'products_url_alias/array/products_id/' . $pid;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_string = $cache_result;
        } else {
            if ($return_string = Products::model()->getProductsUrlAlias($pid)) {
                Yii::app()->cache->set($cache_key, $return_string, 86400); // cache 10 minutes
            }
        }

        return $return_string;
    }

    public function getProductIDByUrlAlias($product_url_alias)
    {
        $return_int = 0;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . Products::model()->tableName() . 'products_id/array/products_url_alias/' . $product_url_alias;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_int = $cache_result;
        } else {
            if ($return_int = Products::model()->getProductsIDByProductsUrlAlias($product_url_alias)) {
                Yii::app()->cache->set($cache_key, $return_int, 86400); // cache 10 minutes
            }
        }

        return $return_int;
    }

    /*
     * $category_info_array consist of 3rd layer and 4th layer category info
     * sorting pattern 
     *  - 3rd 1[5(category sort order)][2(delivery type)][5(product sort order)][6(product id)] == 1[xxxxx][1x][xxxxx]xxxxxx
     *  - 4th 1[00000][2(delivery type)][5(product sort order)][6(product id)] == 1[00000][1x][xxxxx]xxxxxx
     */
    private function getCategories($game_id, $category_info_array, $product_filtering_array = array())
    {
        $return_array = array();

        foreach ($category_info_array as $category_info) {
            $category_info['game_id'] = $game_id;
            $category_sort_order = $category_info['custom_products_type_id'] === '999' ? '1' . str_pad($category_info['sort_order'], 5, '0', STR_PAD_LEFT) : '100000';
            $this->getProducts($category_info['categories_id'], $category_sort_order, $category_info, $product_filtering_array, $return_array);
        }

        ksort($return_array);

        return $return_array;
    }

    # not in used

    private function getProducts($category_id, $category_sort_order, $category_info, $product_filtering_array = array(), &$return_array = array())
    {
        // Get Game Blog's Products ====================================================================================
        if (isset($product_filtering_array['game_blog_id']) && $product_filtering_array['game_blog_id']) {
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'category_and_products/array/game_blog_id/' . $product_filtering_array['game_blog_id'] . '/category_id/' . $category_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== false) {
                $return_array = $cache_result;
            } else {
                $cache_array = array();
                $product_array = array();

                $product_list_1 = $this->getAllProductsByGameBlogID($product_filtering_array['game_blog_id'], $category_id, 1);
                $product_list_2 = $this->getAllProductsByCategoryID($category_id, 1, 1);
                $product_list = array_merge($product_list_1, $product_list_2);

                foreach ($product_list as $product_info) {
                    if (in_array($product_info['products_id'], $product_array)) {
                        continue;
                    }

                    # set this is to reduce the check in Recently Viewed Products section to check on get this product info again.
                    $this->setActiveProductByIDCache($product_info['products_id'], Yii::app()->frontPageCom->customer_group_id, $product_info, $category_info);

                    $product_array[] = $product_info['products_id'];
                    $product_sort_order = str_pad($product_info['products_sort_order'], 5, '0', STR_PAD_LEFT) . str_pad($product_info['products_id'], 6, '0', STR_PAD_LEFT);
                    $result_array = $this->getProductsList($product_info);

                    // idx value is either 10 (dtu) || 11 (softpin) and will not more than 9, and it should be deprecated after fully seperate the dtu from softpin
                    foreach ($result_array as $idx => $result) {
                        $cache_array[$idx . $product_sort_order] = $result;

                        $result['category'] = $category_info;
                        $return_array[$category_sort_order . $idx . $product_sort_order] = $result;
                    }
                }

                Yii::app()->cache->set($cache_key, $return_array, 900); // cache 24 hours
            }
            // Get Game Blog's Products ====================================================================================
        } else {
            // Get Category's Products ====================================================================================
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'category_and_products/array/category_id/' . $category_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== false) {
                $return_array = $cache_result;
            } else {
                $cache_array = array();
                $product_list = $this->getAllProductsByCategoryID($category_id, 1, 1);

                foreach ($product_list as $product_info) {
                    # set this is to reduce the check in Recently Viewed Products section to check on get this product info again.
                    $this->setActiveProductByIDCache($product_info['products_id'], Yii::app()->frontPageCom->customer_group_id, $product_info, $category_info);

                    $product_sort_order = str_pad($product_info['products_sort_order'], 5, '0', STR_PAD_LEFT) . str_pad($product_info['products_id'], 6, '0', STR_PAD_LEFT);
                    $result_array = $this->getProductsList($product_info);

                    // idx value is either 10 (dtu) || 11 (softpin) and will not more than 9, and it should be deprecated after fully seperate the dtu from softpin
                    foreach ($result_array as $idx => $result) {
                        $cache_array[$idx . $product_sort_order] = $result;

                        $result['category'] = $category_info;
                        $return_array[$category_sort_order . $idx . $product_sort_order] = $result;
                    }
                }

                Yii::app()->cache->set($cache_key, $cache_array, 900); // cache 24 hours
            }
        }

        return $return_array;
    }

    public function getAllProductByGameID($game_id, $display_status = 1)
    {
        $return_array = array();
        $customer_group_id = (!empty(Yii::app()->frontPageCom->customer_group_id) ? Yii::app()->frontPageCom->customer_group_id : self::ALL_USER_ACCESS_GROUP_ID);

        #key:category_and_products/array/category_id/xxx/groups_id/xxx
        $cache_key = 'category_and_products/array/category_id/' . $game_id . '/display_status/' . $display_status . '/groups_id/' . $customer_group_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_array = $cache_result;
        } else {
            $category_info = array();
            $category_info_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryInfoByGameID($game_id);

            foreach ($category_info_array as $category_info) {
                $category_id = $category_info['categories_id'];
                $category_info['game_id'] = $game_id;
                $category_sort_order = $category_info['custom_products_type_id'] === '999' ? '1' . str_pad($category_info['sort_order'], 5, '0', STR_PAD_LEFT) : '100000';
                $product_list = $this->getAllProductsByCategoryID($category_id, 1, $display_status, $customer_group_id);

                foreach ($product_list as $product_info) {
                    # set this is to reduce the check in Recently Viewed Products section to check on get this product info again.
                    $this->setActiveProductByIDCache($product_info['products_id'], $customer_group_id, $product_info, $category_info);

                    $product_sort_order = str_pad($product_info['products_sort_order'], 5, '0', STR_PAD_LEFT) . str_pad($product_info['products_id'], 6, '0', STR_PAD_LEFT);
                    $result_array = $this->getProductsList($product_info);

                    // idx value is either 10 (dtu) || 11 (softpin) and will not more than 9, and it should be deprecated after fully seperate the dtu from softpin
                    foreach ($result_array as $idx => $result) {
                        $result['category'] = $category_info;
                        $return_array[$category_sort_order . $idx . $product_sort_order] = $result;
                    }
                }
            }

            ksort($return_array);

            Yii::app()->cache->set($cache_key, $return_array, 900); // cache 24 hours
        }

        return $return_array;
    }

    public function getAllProductByGameIDByGameBlogID($game_id, $product_filtering_array)
    {
        $return_array = array();
        $customer_group_id = (!empty(Yii::app()->frontPageCom->customer_group_id) ? Yii::app()->frontPageCom->customer_group_id : self::ALL_USER_ACCESS_GROUP_ID);

        if (isset($product_filtering_array['game_blog_id']) && $product_filtering_array['game_blog_id']) {
            #key:category_and_products/array/game_blog_id/xxx/category_id/xxx/groups_id/xxx
            $cache_key = 'category_and_products/array/game_blog_id/' . $product_filtering_array['game_blog_id'] . '/category_id/' . $game_id . '/groups_id/' . $customer_group_id;
            $cache_result = Yii::app()->cache->get($cache_key);

            if ($cache_result !== false) {
                $return_array = $cache_result;
            } else {
                $product_array = array();
                $category_info_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryInfoByGameID($game_id);

                foreach ($category_info_array as $category_info) {
                    $category_id = $category_info['categories_id'];
                    $category_info['game_id'] = $game_id;
                    $category_sort_order = $category_info['custom_products_type_id'] === '999' ? '1' . str_pad($category_info['sort_order'], 5, '0', STR_PAD_LEFT) : '100000';

                    $product_list_1 = $this->getAllProductsByGameBlogID($product_filtering_array['game_blog_id'], $category_id, 1, $customer_group_id);
                    $product_list_2 = $this->getAllProductsByCategoryID($category_id, 1, 1, $customer_group_id);
                    $product_list = array_merge($product_list_1, $product_list_2);

                    foreach ($product_list as $product_info) {
                        if (in_array($product_info['products_id'], $product_array)) {
                            continue;
                        }

                        # set this is to reduce the check in Recently Viewed Products section to check on get this product info again.
                        $this->setActiveProductByIDCache($product_info['products_id'], $customer_group_id, $product_info, $category_info);

                        $product_array[] = $product_info['products_id'];
                        $product_sort_order = str_pad($product_info['products_sort_order'], 5, '0', STR_PAD_LEFT) . str_pad($product_info['products_id'], 6, '0', STR_PAD_LEFT);
                        $result_array = $this->getProductsList($product_info);

                        // idx value is either 10 (dtu) || 11 (softpin) and will not more than 9, and it should be deprecated after fully seperate the dtu from softpin
                        foreach ($result_array as $idx => $result) {
                            $result['category'] = $category_info;
                            $return_array[$category_sort_order . $idx . $product_sort_order] = $result;
                        }
                    }
                }

                ksort($return_array);

                Yii::app()->cache->set($cache_key, $return_array, 900); // cache 24 hours
            }
            // Get Game Blog's Products ====================================================================================
        }

        return $return_array;
    }

    private function getProductsList($product_info_row, $return_array = array())
    {
        $product_delivery_mode_array = $this->getProductDeliveryMode($product_info_row['products_id'], $product_info_row);
        $only_dtu_box_flag = 0; // If only DTU
        // Group All Direct Top Up products
        if (in_array('6', $product_delivery_mode_array)) {  // is_dtu_flag
            $only_dtu_box_flag = count($product_delivery_mode_array) == 1 ? 1 : 0; // If only DTU

            $return_array['10'] = array_merge($product_info_row, array(
                'is_dtu_flag' => 1,
                'default_dm' => 6,
                'promo_type' => 0,
                'delivery_method' => $product_delivery_mode_array,
            ));
        }

        // Group All General products
        if (!$only_dtu_box_flag) {
            $return_array['11'] = array_merge($product_info_row, array(
                'is_dtu_flag' => 0,
                'default_dm' => 5,
                'promo_type' => 0,
                'delivery_method' => $product_delivery_mode_array,
            ));
        }

        unset($product_delivery_mode_array);

        return $return_array;
    }

    public function getProductPurchaseStatus($products_id)
    {
        $status = 'ACTIVE';
        $product_info_array = $this->getProductBundleInfoByID($products_id);

        if ($product_info_array) {
            if ($product_info_array['actual']['products_bundle'] == "yes") {
                // static bundle product
                foreach ($product_info_array['bundle'] as $bundle_data) {
                    if ($product_info = $this->getProductInfoByID($bundle_data["products_id"])) {
                        if (!$product_info["products_status"]) { // Inactive or Out of Stock
                            $status = 'INACTIVE';
                            break;
                        }
                    }
                }
            } elseif ($product_info_array['actual']['products_bundle_dynamic'] == "yes") {
                // not in used
                $status = 'INACTIVE';
            } else {
                // single product
                if (!$product_info_array['actual']["products_status"]) { // Inactive or Out of Stock
                    $status = 'INACTIVE';
                }
            }
        } else {
            $status = 'INACTIVE';
        }

        return $status;
    }

    public function validateZeroPriceProduct($products_id)
    {
        $return_bool = false;
        $product_info_array = $this->getProductBundleInfoByID($products_id);

        if ($product_info_array) {
            if ($product_info_array['actual']['products_price'] > 0) {
                $return_bool = true;
            } else {
                # zero price product
                # check zero price product flag, yes : return true
                if ($this->isProductZeroPriceEnabled($products_id)) {
                    $return_bool = true;
                } else {
                    # de-activate product
                    $this->deactivateProduct($products_id);
                }
            }
        }

        return $return_bool;
    }

    public function getSingleProductStockStatus($products_info, $purchase_qty = null, &$available_stock = 0)
    {
        $status = '';
        $product_info = isset($products_info['products_purchase_mode']) ? $products_info : $this->getProductInfoByID($products_info['products_id']);

        if ($product_info) {
            switch ($product_info["products_purchase_mode"]) {
                case '1': // Always Add to Cart
                    $status = 'AVAILABLE';
                    break;
                case '2': // Always Pre-Order
                    $status = 'PRE_ORDER';
                    break;
                case '3': // Always Out of Stock
                    $status = 'OUT_OF_STOCK';
                    break;
                case '4': // Auto Mode
                    $status = 'AUTO';

                    if (notNull($purchase_qty)) {
                        if ($this->validateCustomOutOfStockRules($products_info['products_id'], $product_info['products_quantity'], $purchase_qty, $available_stock) === 'OUT_OF_STOCK') {
                            $status = 'OUT_OF_STOCK';
                        } elseif (notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_out_of_stock_level"] < $purchase_qty) { // If there is setting for out of stock level
                            $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                            $status = 'OUT_OF_STOCK';
                        } elseif (notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_pre_order_level"] < $purchase_qty) {
                            #Test on Pre-order
                            $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                            $status = 'PRE_ORDER';
                        } else {
                            if ($product_info["products_pre_order_level"]) {
                                $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                            } elseif ($product_info["products_out_of_stock_level"]) {
                                $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                            } else {
                                $available_stock = $product_info['products_quantity'];
                            }

                            $status = 'AVAILABLE';
                        }
                    }
                    break;
            }
        }

        return $status;
    }

    public function isProductOutOfStock($products_id, $purchase_qty = null)
    {
        $status = '';
        $product_info_array = $this->getProductBundleInfoByID($products_id);

        if ($product_info_array) {
            if ($product_info_array['actual']['products_bundle'] == "yes") {
                // static bundle product
                foreach ($product_info_array['bundle'] as $bundle_data) {
                    if ($product_info = $this->getProductInfoByID($bundle_data["products_id"])) {
                        $status = $this->getSingleProductStockStatus($product_info, ($bundle_data['subproduct_qty'] * $purchase_qty));

                        if ($status === 'OUT_OF_STOCK') {
                            break;
                        }
                    }
                }
            } elseif ($product_info_array['actual']['products_bundle_dynamic'] == "yes") {
                // not in used
            } else {
                // single product
                $prod_inventory_row = $product_info_array['actual'];
                $status = $this->getSingleProductStockStatus($prod_inventory_row, $purchase_qty);
            }
        }

        return $status;
    }

    public function getProductsListByID($products_id, $customer_group_id = null, $return_array = array(), $extra_info = array())
    {
        $customer_group_id = $customer_group_id !== null ? $customer_group_id : Yii::app()->frontPageCom->customer_group_id;
        $show_virtual_products = (isset($extra_info['load_virtual_products']) ? $extra_info['load_virtual_products'] : false);

        if ($active_product_array = $this->getActiveProductByID($products_id, $customer_group_id, $show_virtual_products)) {
            $product_info_array = $this->getProductsList($active_product_array);
            $return_array = $this->getPaginationProductsInfo($product_info_array, $customer_group_id, $return_array);
        }



        return $return_array;
    }

    private function getProductsPromotionStatus($product_info_array, $status_array, $setting_array)
    {
        $return_status = '';
        $selling_status_enabled = false;

        // GET qty from 3 bundle, bundle dynamic and single
        if ($product_info_array['products_bundle'] == 'yes') {
            $qty = (int)$status_array['bundle_qty'];
        } elseif ($product_info_array['products_bundle_dynamic'] == 'yes') {
            $qty = 0; // unknown
        } else {
            $qty = (int)$product_info_array['products_quantity'];
        }

        /*
          0 = out of stock
          1 = add-to-cart
          2 = pre order
         */
        if ((int)$status_array['show'] > 0) {
            if ((int)$setting_array['promotion_limited_stock'] == 1) {
                if ($qty < (int)$setting_array['promotion_limited_stock_qty']) {
                    $return_status = 'LIMITED_STOCK';
                } else {
                    $selling_status_enabled = true;
                }
            } else {
                $selling_status_enabled = true;
            }

            if ($selling_status_enabled) {
                switch ($setting_array['promotion_selling_status']) {
                    case 1:
                        $return_status = 'FAST_SELLING';
                        break;
                    case 2:
                        $return_status = 'PRICE_SLASH';
                        break;
                    default:
                }
            }
        }

        return $return_status;
    }

    public function getPaginationProductsInfo($product_raw_info_array, $customers_groups_id, $return_array = array())
    {
        $product_info_array = array();

        foreach ($product_raw_info_array as $product_info_row) {
            $pid = $product_info_row['products_id'];
            $category_info = $product_info_row['category'];

            if (!isset($product_info_array[$pid])) {
                $product_info_row = array_merge($product_info_row, $this->getProductInfoByID($pid, '', ''));
                $product_name = $this->getAltProductsName($pid);

                $customers_groups_info_array = array(
                    'discount' => Yii::app()->customerCom->getDiscount(),
                    'group_discount' => Yii::app()->customerCom->getGroupDiscountByProductID($pid, $category_info),
                );

                $status_info = $this->getPurchasePermission($product_info_row['products_id'], $this->getProductPackageType($product_info_row), $product_info_row);
                $show_it = $status_info['show'];
                $product_price_array = $this->getProductPricesInfoByID($pid, $product_info_row);

                // Start Get Price Info
                $original_price = Yii::app()->currency->display_price($product_price_array, null, 1, ' ');
                $product_price = Yii::app()->currency->display_price($product_price_array, $customers_groups_info_array, 1, ' ');

                if ($show_it === 0) {
                    $extra_notice = $this->getEtaString($status_info['pre_order_time']);
                    $extra_notice = (notNull($extra_notice) ? $extra_notice : '-');
                    $delivery_status = array('dm' => 'softpin', 'type' => 'out_of_stock'); //IMAGE_BUTTON_OUT_OF_STOCK
                    $stock_status = 'OutOfStock';
                } elseif ($show_it === 1 || $status_info['is_future_product'] != '1') {
                    $extra_notice = '';
                    $delivery_status = array('dm' => 'softpin', 'type' => 'buy');   //IMAGE_BUTTON_IN_CART
                    $stock_status = 'InStock';
                } else {
                    $extra_notice = $this->getEtaString($status_info['pre_order_time']);
                    $extra_notice = (notNull($extra_notice) ? $extra_notice : '-');
                    $delivery_status = array('dm' => 'softpin', 'type' => 'pre_order'); //IMAGE_BUTTON_PRE_ORDER
                    $stock_status = 'PreOrder';
                }

                $product_info_array[$pid] = array(
                    'name' => $product_name,
                    'products_type' => (!empty($product_info_row['products_type']) ? $product_info_row['products_type'] : 0),
                    'price' => $product_price,
                    'original_price' => $original_price,
                    'op' => Yii::app()->currency->rebate_point,
                    'gst_title' => '',
                    'product_notice' => $extra_notice,
                    'delivery_status' => $delivery_status,
                    'stock_status' => $stock_status,
                    'promotion_info' => '',
                );
                // if is promotion product, force only display promotion and hide the rest
                if ($promo_product_array = $this->getPromotionProducts($pid)) {
                    $product_info_array[$pid]['image'] = $this->getProductPromotionImage($pid, $category_info['categories_id']);
                    $product_info_array[$pid]['promotion_info'] = array(
                        'ttl_seconds' => isset($promo_product_array['ttl_seconds']) ? $promo_product_array['ttl_seconds'] : 0,
                        'status' => $this->getProductsPromotionStatus($product_info_row, $status_info, $promo_product_array)
                    );
                } else {
                    $product_info_array[$pid]['image'] = $this->getProductImage($pid, $category_info['categories_id']);
                }
            }
            // End Get Price Info

            $product_info = array_merge($product_info_row, $product_info_array[$pid]);

            if (isset($product_info_row['is_dtu_flag']) && $product_info_row['is_dtu_flag']) {
                $product_info['delivery_status'] = array('dm' => 'dtu', 'type' => 'buy');
            }

            $return_array[] = array_merge($product_info, array(
                'pid' => $pid,
                'cid' => $category_info['categories_id'],
                'products_bundle' => $product_info_row['products_bundle'],
            ));
        }

        return $return_array;
    }

    public function getPurchasePermission($product_id, $package_type, $product_row = array())
    {
        $status_info = array();
        $show_it = 0;
        $pre_order_time = 0;
        $cur_eta = 0;
        $subproduct_qty = '';
        $is_future_product = false;
        $max_future_date = '';
        $bundle_qty = '';
        $auto_purchase_mode = false;
        $product_row = $product_row !== array() ? $product_row : $this->getProductInfoByID($product_id);

        switch ($package_type) {
            case self::PRODUCT_BUNDLE:
                if ($bundle_data_array = $this->getProductsBundleByBundleID($product_id)) {
                    $show_it = 1;

                    foreach ($bundle_data_array as $bundle_data) {
                        // Inactive or Out of Stock
                        if (!$bundle_data["products_status"] || $bundle_data["products_purchase_mode"] == '3') {
                            $show_it = 0;
                            break;
                        }

                        $subproduct_qty = $bundle_data['subproduct_qty'];
                        $bundle_prd_qty = $bundle_data['products_quantity'] / $subproduct_qty;
                        // Get SYSTEM_PRODUCT_ETA category configuration of this single product. Cannot use package id since its subproduct might not from the same category
                        $cat_cfg_array = ConfigurationCom::getCategoryConfigValueByProductID($bundle_data["products_id"], 'SYSTEM_PRODUCT_ETA');

                        if (!notNull($bundle_qty)) {
                            $bundle_qty = $bundle_prd_qty;
                        } else {
                            $bundle_qty = ($bundle_prd_qty > $bundle_qty ? $bundle_qty : $bundle_prd_qty);
                        }

                        switch ($bundle_data["products_purchase_mode"]) {
                            case '1': // Always Add to Cart
                                ; // do nothing, remain current whatsoever state
                                break;
                            case '2': // Always Pre-Order
                                $show_it = 2; // pre-order
                                $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                                $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                                if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                    if ($bundle_data['products_date_available'] > $max_future_date) {
                                        $max_future_date = $bundle_data['products_date_available'];
                                    }

                                    $is_future_product = true;
                                }
                                break;
                            case '4': // Auto Mode
                                $pre_order_level = ($bundle_data["products_pre_order_level"] != '') ? $bundle_data["products_pre_order_level"] : '';
                                $diff = $bundle_data['products_quantity'] - $bundle_data["subproduct_qty"];
                                $auto_purchase_mode = true;
                                $available_stock = 0;

                                if ($this->validateCustomOutOfStockRules($bundle_data["products_id"], $bundle_data['products_quantity'], 1, $available_stock) === 'OUT_OF_STOCK') {
                                    $show_it = 0;
                                    break 2;
                                } elseif (notNull($bundle_data["products_out_of_stock_level"]) && $diff < $bundle_data["products_out_of_stock_level"]) { // If there is setting for out of stock level
                                    $show_it = 0; // out of stock
                                    break 2;
                                } elseif (notNull($pre_order_level) && $diff < $pre_order_level) {
                                    $show_it = 2;
                                    $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                                    $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                                    if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                        if ($bundle_data['products_date_available'] > $max_future_date) {
                                            $max_future_date = $bundle_data['products_date_available'];
                                        }

                                        $is_future_product = true;
                                    }
                                } else {
                                    ; // do nothing, remain current whatsoever state
                                }
                                break;
                        }
                    }

                    if (!$show_it) {
                        $is_future_product = false;
                        $pre_order_time = '';
                    }

                    // Format pre-order time
                    if ($is_future_product) {
                        $dateObj = explode(' ', trim($max_future_date));
                        list($yr, $mth, $day) = explode('-', $dateObj[0]);
                        list($hr, $min, $sec) = explode(':', $dateObj[1]);
                        $pre_order_time = '#DATE#' . date("F j, Y", mktime((int)$hr, (int)$min, (int)$sec, $mth, $day, $yr)) . '#DATE#';
                    }
                } else {
                }
                break;
            case self::PRODUCT_BUNDLE_DYNAMIC:
                break;
            default:
                if ($product_row != array()) {
                    $qty = $product_row['products_quantity'];
                    // Get SYSTEM_PRODUCT_ETA category configuration of this single product.
                    $cat_cfg_array = ConfigurationCom::getCategoryConfigValueByProductID($product_id, 'SYSTEM_PRODUCT_ETA');

                    switch ($product_row["products_purchase_mode"]) {
                        case '1': // Always Add to Cart
                            $show_it = 1;
                            break;
                        case '2': // Always Pre-Order
                            $show_it = 2; // pre-order

                            if ($product_row['products_date_available'] > date('Y-m-d H:i:s')) {
                                $is_future_product = true;
                                $pre_order_time = '#DATE#' . $product_row['date_available_string'] . '#DATE#';
                            } else {
                                $pre_order_time = $product_row["products_eta"] != '' ? $product_row["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                            }
                            break;
                        case '3': // Always Out of Stock
                            $show_it = 0;
                            break;
                        case '4': // Auto Mode
                            $pre_order_level = ($product_row["products_pre_order_level"] != '') ? $product_row["products_pre_order_level"] : '';
                            $auto_purchase_mode = true;
                            $available_stock = 0;

                            // If there is setting for out of stock level 
                            if ($this->validateCustomOutOfStockRules($product_id, $product_row['products_quantity'], 1, $available_stock) === 'OUT_OF_STOCK') {
                                $show_it = 0;
                            } elseif (notNull($product_row["products_out_of_stock_level"]) && $qty <= (int)$product_row["products_out_of_stock_level"]) {
                                $show_it = 0;
                            } elseif (isset($purchase_limit_qty) && $product_row['products_quantity'] <= $purchase_limit_qty) {
                                $show_it = 0;
                            } elseif (notNull($pre_order_level) && $qty <= $pre_order_level) {
                                // pre-order
                                $show_it = 2;

                                if ($product_row['products_date_available'] > date('Y-m-d H:i:s')) {
                                    $is_future_product = true;
                                    $pre_order_time = '#DATE#' . $product_row['date_available_string'] . '#DATE#';
                                } else {
                                    $pre_order_time = $product_row["products_eta"] != '' ? $product_row["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                                }
                            } else {
                                $show_it = 1;
                            }
                            break;
                    }
                }
                break;
        }

        switch ($show_it) {
            case '0':
                if (notNull($product_row['products_out_of_stock_msg'])) {
                    $pre_order_time = '#DATE#' . $product_row['products_out_of_stock_msg'] . '#DATE#';
                }
                break;
            case '1':
                if (notNull($product_row['products_add_to_cart_msg'])) {
                    $pre_order_time = '#DATE#' . $product_row['products_add_to_cart_msg'] . '#DATE#';
                }
                break;
            case '2':
                if (notNull($product_row['products_preorder_msg'])) {
                    $pre_order_time = '#DATE#' . $product_row['products_preorder_msg'] . '#DATE#';
                }
                break;
        }

        $status_info["show"] = $show_it;
        $status_info["pre_order_time"] = $pre_order_time;
        $status_info["is_future_product"] = $is_future_product ? '1' : '0';
        $status_info["subproduct_qty"] = $subproduct_qty;
        $status_info['bundle_qty'] = $bundle_qty;
        $status_info['auto_purchase_mode'] = $auto_purchase_mode;

        return $status_info;
    }

    protected function getTaxRate($class_id, $country_id = -1, $zone_id = -1)
    {
        if (($country_id == -1) && ($zone_id == -1)) {
            $country_id = Yii::app()->customerCom->getValue('country');
            $zone_id = Yii::app()->customerCom->getZoneID();
        }

        if ($tax_query = TaxRates::model()->getRateByZoneIDByCountryIDByClassID($zone_id, $country_id, $class_id)) {
            $tax_multiplier = 1.0;

            foreach ($tax_query as $tax) {
                $tax_multiplier *= 1.0 + ($tax['tax_rate'] / 100);
            }

            return ($tax_multiplier - 1.0) * 100;
        } else {
            return 0;
        }
    }

    public function validateCustomOutOfStockRules($products_id, $products_quantity, $purchase_qty, &$available_qty = 0, $customer_group_id = 0, $skip_cache = false)
    {
        return parent::validateCustomOutOfStockRules($products_id, $products_quantity, $purchase_qty, $available_qty, Yii::app()->frontPageCom->customer_group_id, $skip_cache);
    }

}
