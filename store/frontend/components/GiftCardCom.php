<?php

class GiftCardCom extends CApplicationComponent
{

    public $code = "";
    public $api_url = "";
    public $api_merchant = "";
    public $api_secret = "";
    public $api_scope = "";

    public function _authenticate()
    {
    }

    public function _merchant($input)
    {
        if (isset($input["rd_code"]) && isset(Yii::app()->params["GIFT_CARD"]["PRODUCT"][$input["rd_code"]])) {
            $api_cfg = Yii::app()->params["GIFT_CARD"]["PRODUCT"][$input["rd_code"]]["api"];
            $this->code = $input["rd_code"];
            $this->api_url = $api_cfg["url"];
            $this->api_merchant = $api_cfg["merchant"];
            $this->api_secret = $api_cfg["secret"];
        }
    }

    public static function product()
    {
        $result = array();

        if (isset(Yii::app()->params["GIFT_CARD"]) && isset(Yii::app()->params["GIFT_CARD"]["PRODUCT"])) {
            $result = Yii::app()->params["GIFT_CARD"]["PRODUCT"];
        }

        return $result;
    }
}
