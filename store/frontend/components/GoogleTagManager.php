<?php

class GoogleTagManager extends CApplicationComponent
{
    public $app_id;

    public function __construct()
    {
        $this->app_id = Yii::app()->params['gtm'];
    }

    public function webSDK()
    {
        if ($this->app_id) {
            echo <<<HTML
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.defer=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','$this->app_id');</script>
HTML;
        }
    }

    public function bodySDK()
    {
        if ($this->app_id) {
            echo <<<HTML
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=$this->app_id" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
HTML;
        }
    }

    public function addToCart($product, $precheckout_model)
    {
        if ($this->app_id) {
            $cart = $precheckout_model->getBuyNowPreCheckoutDetails();

            $pid = $product["products_id"];
            $pname = $product["name"];
            $qty = $cart["quantity"];

            list($brand, $category) = explode(">", $product["products_cat_path"]);
            $brand = trim($brand);
            $category = trim($category);

            $price = Yii::app()->currency->advanceCurrencyConversion($product["products_price"], $product["products_base_currency"], "USD");
            $total = Yii::app()->currency->advanceCurrencyConversion($cart["ot"]["subtotal"]["value"], Yii::app()->session['currency'], "USD");

            $js = <<<JS
            dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.
            dataLayer.push({
              event: "add_to_cart",
              ecommerce: {
                currency: "USD",
                value: $total,
                items: [
                {
                  item_id: "$pid",
                  item_name: "$pname",
                  item_brand: "$brand",
                  item_category: "$category",
                  price: $price,
                  quantity: $qty
                }
                ]
              }
            });
JS;

            Yii::app()->clientScript->registerScript('gtag-add-to-cart', $js, CClientScript::POS_READY);
        }
    }

}