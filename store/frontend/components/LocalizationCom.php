<?php

class LocalizationCom extends MainCom {

    private $zone_info_array = array();

    public function getZoneCategoriesID($type = 'zone_categories_id') {
        $return_array = array();
        // 1: Games & Product
        $zone_game_obj = $this->getZoneInfoByType(1);

        if (isset($zone_game_obj->$type) && is_array($zone_game_obj->$type) && !empty($zone_game_obj->$type)) {
            $return_array = $zone_game_obj->$type;
        }

        unset($zone_game_obj);

        return $return_array;
    }

    private function getZoneInfoByType($zone_type) {
        if (!isset($this->zone_info_array[$zone_type])) {
            $zones_id = $this->getZoneID($zone_type);
            $this->zone_info_array[$zone_type] = new stdClass();
            $this->zone_info_array[$zone_type]->zone_id = $zones_id;
            $this->zone_info_array[$zone_type] = $this->getZoneInfoByID($zones_id);
        }

        return $this->zone_info_array[$zone_type];
    }

    // class methods
    function get_zone_configuration() {
        // 1: Games & Product
        // 2: Language
        // 3: Currency
        // 4: Payment Gateway
        // 5: Content
        for ($loc_type = 1; $loc_type <= 4; $loc_type++) {
            $zone_info_array[$loc_type] = $this->getZoneInfoByType($loc_type);
        }

        return $zone_info_array;
    }

    public function getZoneID($zone_type, $country_id = NULL) {
        $geo_result_array = array();
        $fresh_request = true;
        $country_id = ($country_id !== NULL) ? $country_id : Yii::app()->frontPageCom->country_id;
        $default_country = ConfigurationCom::getValue('STORE_COUNTRY');

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . GeoZones::model()->tableName() . '/geo_zone_type/array/' . $zone_type . '/country_id/' . $country_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $geo_result_array = $cache_result;

            if (isset($geo_result_array[$country_id]) && is_array($geo_result_array[$country_id])) {
                $fresh_request = false;
            }
        }

        if ($fresh_request) {
            if ($countries_zones_id_row = GeoZones::model()->getZoneID($country_id, $zone_type)) {
                $geo_result_array[$country_id]['geo_zone_id'] = $countries_zones_id_row;
            } else {
                $geo_result_array[$country_id]['geo_zone_id'] = $countries_zones_id_row = GeoZones::model()->getZoneID($default_country, $zone_type);
            }

            Yii::app()->cache->set($cache_key, $geo_result_array, 86400);
        }

        return $geo_result_array[$country_id]['geo_zone_id'];
    }

    private function getZoneInfoByID($zones_id) {
        $zone_setting_result = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . ZonesInfo::model()->tableName() . '/geo_zone_id/' . $zones_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $zone_setting_result = $cache_result;
        } else {
            if ($zones_info_json = ZonesInfo::model()->getZoneInfoByID($zones_id)) {
                $zone_setting_result = CJSON::decode($zones_info_json, false);

                if (isset($zone_setting_result->zone_categories_id)) {
                    $zone_setting_result->zone_categories_id = Yii::app()->frontPageCom->getCategoryObj()->filterEnabledCagetoriesID($zone_setting_result->zone_categories_id);
                }
            }

            Yii::app()->cache->set($cache_key, $zone_setting_result, 86400);
        }

        return $zone_setting_result;
    }

    public function verifyGSTCondition($ip, $language_id) {
        $_result = array();

        $mobileCountry = false;
        if (Yii::app()->user->id) {
            $userCountry = RegionalSettingCom::regionalCountryCache('g', ['uid' => Yii::app()->user->id]);
            if ($userCountry == false) {
                $mobileCountry = RegionalSettingCom::getMobileCountry();
            } else {
                $mobileCountry['phone_country_code'] = $userCountry;
            }
        }

        if (!empty($ip) && !isset($mobileCountry['phone_country_code'])) {
            $_info = Yii::app()->geoip->getIPCountryInfo($ip);
            if (isset($_info['id']) && !empty($_info['id'])) {
                $country = $_info['id'];
            }

            if (empty($country)) {
                $country = ConfigurationCom::getValue('STORE_COUNTRY');
            }

            $countryInfo = Countries::model()->findByPk($country);
        } else {
            $countryInfo = Countries::model()->findByAttributes(array("countries_iso_code_2" => $mobileCountry['phone_country_code']));
        }

        if ($countryInfo) {
            if ($m_oc_data = OrdersTaxConfiguration::model()->getGSTTaxInfo($countryInfo['countries_iso_code_2'], $language_id)) {
                // Check rules to open tax
                $taxCountryStatus = ($m_oc_data['orders_tax_status'] == 1 && $m_oc_data['start_datetime'] <= date('Y-m-d H:i:s')) ? true : false;
                $taxBusinessStatus = ($m_oc_data['orders_tax_status'] == 1 && $m_oc_data['business_tax_status'] == 1) ? true : false;
                if ($taxCountryStatus || $taxBusinessStatus) {
                    $_result = array(
                        'id' => $m_oc_data['orders_tax_id'],
                        'country' => $countryInfo['countries_id'],
                        'country_name' => $countryInfo['countries_name'],
                        'percentage' => $m_oc_data['orders_tax_percentage'],
                        'title' => $m_oc_data['orders_tax_title'],
                        'display_title' => $m_oc_data['orders_tax_title_short'],
                        'display_message' => $m_oc_data['orders_tax_message'],
                        'start_datetime' => $m_oc_data['start_datetime'],
                        'field_data' => $m_oc_data['business_tax_form'],
                        'business_status' => $m_oc_data['business_tax_status'],
                        'status' => $m_oc_data['orders_tax_status']
                    );
                }
            }
        }

        return $_result;
    }

    public function showGSTAlertBar($use_cookie = true) {
        $_result = array();

        if ($use_cookie) {
            if (!(isset(Yii::app()->request->cookies['alertbar_cookie_gst-alert']) && !empty(Yii::app()->request->cookies['alertbar_cookie_gst-alert']))) {
                $_result = $this->verifyGSTCondition(Yii::app()->frontPageCom->getIPAddress(), Yii::app()->frontPageCom->language_id);
            }
        } else {
            $_result = $this->verifyGSTCondition(Yii::app()->frontPageCom->getIPAddress(), Yii::app()->frontPageCom->language_id);
        }

        return $_result;
    }

    public static function CountryISO2ToISO3($iso2) {
        $mapping = [
            "BD" => "BGD",
            "BE" => "BEL",
            "BF" => "BFA",
            "BG" => "BGR",
            "BA" => "BIH",
            "BB" => "BRB",
            "WF" => "WLF",
            "BL" => "BLM",
            "BM" => "BMU",
            "BN" => "BRN",
            "BO" => "BOL",
            "BH" => "BHR",
            "BI" => "BDI",
            "BJ" => "BEN",
            "BT" => "BTN",
            "JM" => "JAM",
            "BV" => "BVT",
            "BW" => "BWA",
            "WS" => "WSM",
            "BQ" => "BES",
            "BR" => "BRA",
            "BS" => "BHS",
            "JE" => "JEY",
            "BY" => "BLR",
            "BZ" => "BLZ",
            "RU" => "RUS",
            "RW" => "RWA",
            "RS" => "SRB",
            "TL" => "TLS",
            "RE" => "REU",
            "TM" => "TKM",
            "TJ" => "TJK",
            "RO" => "ROU",
            "TK" => "TKL",
            "GW" => "GNB",
            "GU" => "GUM",
            "GT" => "GTM",
            "GS" => "SGS",
            "GR" => "GRC",
            "GQ" => "GNQ",
            "GP" => "GLP",
            "JP" => "JPN",
            "GY" => "GUY",
            "GG" => "GGY",
            "GF" => "GUF",
            "GE" => "GEO",
            "GD" => "GRD",
            "GB" => "GBR",
            "GA" => "GAB",
            "SV" => "SLV",
            "GN" => "GIN",
            "GM" => "GMB",
            "GL" => "GRL",
            "GI" => "GIB",
            "GH" => "GHA",
            "OM" => "OMN",
            "TN" => "TUN",
            "JO" => "JOR",
            "HR" => "HRV",
            "HT" => "HTI",
            "HU" => "HUN",
            "HK" => "HKG",
            "HN" => "HND",
            "HM" => "HMD",
            "VE" => "VEN",
            "PR" => "PRI",
            "PS" => "PSE",
            "PW" => "PLW",
            "PT" => "PRT",
            "SJ" => "SJM",
            "PY" => "PRY",
            "IQ" => "IRQ",
            "PA" => "PAN",
            "PF" => "PYF",
            "PG" => "PNG",
            "PE" => "PER",
            "PK" => "PAK",
            "PH" => "PHL",
            "PN" => "PCN",
            "PL" => "POL",
            "PM" => "SPM",
            "ZM" => "ZMB",
            "EH" => "ESH",
            "EE" => "EST",
            "EG" => "EGY",
            "ZA" => "ZAF",
            "EC" => "ECU",
            "IT" => "ITA",
            "VN" => "VNM",
            "SB" => "SLB",
            "ET" => "ETH",
            "SO" => "SOM",
            "ZW" => "ZWE",
            "SA" => "SAU",
            "ES" => "ESP",
            "ER" => "ERI",
            "ME" => "MNE",
            "MD" => "MDA",
            "MG" => "MDG",
            "MF" => "MAF",
            "MA" => "MAR",
            "MC" => "MCO",
            "UZ" => "UZB",
            "MM" => "MMR",
            "ML" => "MLI",
            "MO" => "MAC",
            "MN" => "MNG",
            "MH" => "MHL",
            "MK" => "MKD",
            "MU" => "MUS",
            "MT" => "MLT",
            "MW" => "MWI",
            "MV" => "MDV",
            "MQ" => "MTQ",
            "MP" => "MNP",
            "MS" => "MSR",
            "MR" => "MRT",
            "IM" => "IMN",
            "UG" => "UGA",
            "TZ" => "TZA",
            "MY" => "MYS",
            "MX" => "MEX",
            "IL" => "ISR",
            "FR" => "FRA",
            "IO" => "IOT",
            "SH" => "SHN",
            "FI" => "FIN",
            "FJ" => "FJI",
            "FK" => "FLK",
            "FM" => "FSM",
            "FO" => "FRO",
            "NI" => "NIC",
            "NL" => "NLD",
            "NO" => "NOR",
            "NA" => "NAM",
            "VU" => "VUT",
            "NC" => "NCL",
            "NE" => "NER",
            "NF" => "NFK",
            "NG" => "NGA",
            "NZ" => "NZL",
            "NP" => "NPL",
            "NR" => "NRU",
            "NU" => "NIU",
            "CK" => "COK",
            "XK" => "XKX",
            "CI" => "CIV",
            "CH" => "CHE",
            "CO" => "COL",
            "CN" => "CHN",
            "CM" => "CMR",
            "CL" => "CHL",
            "CC" => "CCK",
            "CA" => "CAN",
            "CG" => "COG",
            "CF" => "CAF",
            "CD" => "COD",
            "CZ" => "CZE",
            "CY" => "CYP",
            "CX" => "CXR",
            "CR" => "CRI",
            "CW" => "CUW",
            "CV" => "CPV",
            "CU" => "CUB",
            "SZ" => "SWZ",
            "SY" => "SYR",
            "SX" => "SXM",
            "KG" => "KGZ",
            "KE" => "KEN",
            "SS" => "SSD",
            "SR" => "SUR",
            "KI" => "KIR",
            "KH" => "KHM",
            "KN" => "KNA",
            "KM" => "COM",
            "ST" => "STP",
            "SK" => "SVK",
            "KR" => "KOR",
            "SI" => "SVN",
            "KP" => "PRK",
            "KW" => "KWT",
            "SN" => "SEN",
            "SM" => "SMR",
            "SL" => "SLE",
            "SC" => "SYC",
            "KZ" => "KAZ",
            "KY" => "CYM",
            "SG" => "SGP",
            "SE" => "SWE",
            "SD" => "SDN",
            "DO" => "DOM",
            "DM" => "DMA",
            "DJ" => "DJI",
            "DK" => "DNK",
            "VG" => "VGB",
            "DE" => "DEU",
            "YE" => "YEM",
            "DZ" => "DZA",
            "US" => "USA",
            "UY" => "URY",
            "YT" => "MYT",
            "UM" => "UMI",
            "LB" => "LBN",
            "LC" => "LCA",
            "LA" => "LAO",
            "TV" => "TUV",
            "TW" => "TWN",
            "TT" => "TTO",
            "TR" => "TUR",
            "LK" => "LKA",
            "LI" => "LIE",
            "LV" => "LVA",
            "TO" => "TON",
            "LT" => "LTU",
            "LU" => "LUX",
            "LR" => "LBR",
            "LS" => "LSO",
            "TH" => "THA",
            "TF" => "ATF",
            "TG" => "TGO",
            "TD" => "TCD",
            "TC" => "TCA",
            "LY" => "LBY",
            "VA" => "VAT",
            "VC" => "VCT",
            "AE" => "ARE",
            "AD" => "AND",
            "AG" => "ATG",
            "AF" => "AFG",
            "AI" => "AIA",
            "VI" => "VIR",
            "IS" => "ISL",
            "IR" => "IRN",
            "AM" => "ARM",
            "AL" => "ALB",
            "AO" => "AGO",
            "AQ" => "ATA",
            "AS" => "ASM",
            "AR" => "ARG",
            "AU" => "AUS",
            "AT" => "AUT",
            "AW" => "ABW",
            "IN" => "IND",
            "AX" => "ALA",
            "AZ" => "AZE",
            "IE" => "IRL",
            "ID" => "IDN",
            "UA" => "UKR",
            "QA" => "QAT",
            "MZ" => "MOZ"
        ];

        return (isset($mapping[$iso2]) ? $mapping[$iso2] : "USA");
    }

}
