<?php

class LogCom
{
    public function detectChanges($oldData, $newData)
    {
        $changesArray = array();
        if (count($oldData) && is_array($oldData) && count($newData) && is_array($newData)) {
            foreach ($oldData as $key => $value) {
                if (strcmp($newData[$key], $value) !== 0) {
                    $changesArray[$key] = array('from' => $value, 'to' => $newData[$key]);
                }
            }
        }
        return $changesArray;
    }

    public function constructLogMessage($changesArray)
    {
        $messageStr = array();
        if (count($changesArray)) {
            foreach ($changesArray as $key => $changes) {
                $readableArray = $this->getReadableLogInput($key, $changes['from'], $changes['to']);
                if (count($readableArray)) {
                    $messageStr[] = $readableArray;
                }
            }
        }

        return $messageStr;
    }

    public function getReadableLogInput($fieldName, $oldVal, $newVal)
    {
        $modelCustomersGroups = new CustomersGroupsBase();
        $plainResult = false;

        $result = array();
        $oldVal = trim($oldVal);
        $newVal = trim($newVal);
        switch ($fieldName) {
            case 'customers_flag':
                $oldString = $newString = '';
                $userFlagsArray = UserFlagsBase::model()->getUserFlags();

                $changesArray = array();
                $oldArray = !empty($oldVal) ? explode(',', $oldVal) : array();
                $newArray = !empty($newVal) ? explode(',', $newVal) : array();

                $flaggedArray = arrayDiff($newArray, $oldArray);
                $unflaggedArray = arrayDiff($oldArray, $newArray);

                if (count($flaggedArray)) { // From Off->On
                    foreach ($flaggedArray as $flagId) {
                        $flagLabel = $userFlagsArray[$flagId]['user_flags_name'] . (strpos($userFlagsArray[$flagId]['user_flags_name'],
                                str_replace(':', '',
                                    Yii::t('myogm', 'ENTRY_CUSTOMERS_FLAG'))) !== false ? ':' : ' ' . Yii::t('myogm',
                                    'ENTRY_CUSTOMERS_FLAG'));

                        $changesArray[$flagId] = '<span class="' . $userFlagsArray[$flagId]['user_flags_css_style'] . '">' . $flagLabel . '</span> Off --> On' . (isset($userFlagsArray[$flagId]['user_flags_description']) ? ' (<span class="redIndicator">' . $userFlagsArray[$flagId]['user_flags_description'] . '</span>)' : '');
                    }
                }

                if (count($unflaggedArray)) { // From On->Off
                    foreach ($unflaggedArray as $flagId) {
                        $flagLabel = $userFlagsArray[$flagId]['user_flags_name'] . (strpos($userFlagsArray[$flagId]['user_flags_name'],
                                str_replace(':', '',
                                    Yii::t('myogm', 'ENTRY_CUSTOMERS_FLAG'))) !== false ? ':' : ' ' . Yii::t('myogm',
                                    'ENTRY_CUSTOMERS_FLAG'));
                        $changesArray[$flagId] = '<span class="' . $userFlagsArray[$flagId]['user_flags_css_style'] . '">' . $flagLabel . '</span> On --> Off';
                    }
                }

                ksort($changesArray);
                reset($changesArray);

                $text = implode("\n", $changesArray);
                $plainResult = true;
                break;
            case 'customers_groups_id':
                if ((int)$oldVal > 0) {
                    $customersGroupsName = $modelCustomersGroups->getCustomersGroupsName($oldVal);
                    if (!empty($customersGroupsName)) {
                        $oldString = $customersGroupsName;
                    } else {
                        $oldString = "Customer group not found!";
                    }
                }

                if ((int)$newVal > 0) {
                    $customersGroupsName = $modelCustomersGroups->getCustomersGroupsName($newVal);
                    if (!empty($customersGroupsName)) {
                        $newString = $customersGroupsName;
                    } else {
                        $newString = "Customer group not found!";
                    }
                }

                $text = 'Group';
                break;
            case 'customers_gender':
                if (!empty($oldVal)) {
                    $oldString = ($oldVal == 'm') ? 'Male' : 'Female';
                } else {
                    $oldString = 'EMPTY';
                }

                $newString = ($newVal == 'm') ? 'Male' : 'Female';
                $text = 'Gender';
                break;
            case 'customers_dob':
                if (!empty($oldVal)) {
                    $oldString = $this->dateShort($oldVal);
                } else {
                    $oldString = 'EMPTY';
                }

                $newString = $this->dateShort($newVal);
                $text = 'Date of Birth';
                break;
            case 'customers_phone_verified':
                $oldString = ((int)$oldVal == '1') ? 'Verify' : 'Unverify';
                $newString = ((int)$newVal == '1') ? 'Verify' : 'Unverify';
                $text = 'Phone Verification';
                break;
            case 'entry_country_id':
                $country = Countries::model()->getCountries($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = Countries::model()->getCountries($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Country';
                break;
            case 'entry_zone_id':
                return;
                break;
            case 'customers_country_dialing_code_id':
                $country = Countries::model()->getCountries($oldVal);
                $oldString = isset($country->countries_name) ? $country->countries_name : null;

                $country = Countries::model()->getCountries($newVal);
                $newString = isset($country->countries_name) ? $country->countries_name : null;
                $text = 'Location';
                break;
            case 'custom_products_code_viewed':
                $oldString = ((int)$oldVal == '1') ? 'Viewed' : 'Not Viewed';
                $newString = ((int)$newVal == '1') ? 'Viewed' : 'Not Viewed';
                $text = 'CD Key View Status';
                break;
            case 'custom_products_code_status_id':
                $codeStatusArray = array('-1' => 'Disabled', '-2' => 'On Hold', '0' => 'Sold', '1' => 'Actual');
                $oldString = $codeStatusArray[(int)$oldVal];
                $newString = $codeStatusArray[(int)$newVal];
                $text = 'CD Key Status';
                break;
            default:
                $displayLabel = array(
                    'customers_firstname' => 'First Name',
                    'customers_lastname' => 'Last Name',
                    'customers_email_address' => 'E-Mail Address',
                    'customers_telephone' => 'Telephone Number',
                    'customers_fax' => 'Fax Number',
                    'customers_discount' => 'Customer Discount Rate',
                    'customers_newsletter' => 'Newsletter',
                    'customers_phone_verified' => 'Phone Verification',
                    'customers_phone_verified_datetime' => 'Phone Verification Date',
                    'entry_street_address' => 'Street Address',
                    'entry_postcode' => 'Post Code',
                    'entry_city' => 'City',
                    'entry_company' => 'Company',
                    'entry_suburb' => 'Suburb',
                    'entry_state' => 'State',
                    'char_wow_account' => 'WOW Account Name',
                    'char_account_name' => 'Account Name',
                    'char_account_pwd' => 'Account Password',
                    'char_name' => 'Character Name',
                    'delivery_mode' => 'Delivery Mode'
                );
                $oldString = (trim($oldVal) != '') ? $oldVal : "EMPTY";
                $newString = (trim($newVal) != '') ? $newVal : "EMPTY";
                $text = isset($displayLabel[$fieldName]) ? $displayLabel[$fieldName] : $fieldName;
                break;
        }
        $result[$fieldName] = array(
            'text' => $text,
            'from' => $oldString,
            'to' => $newString,
            'plain_result' => ($plainResult ? '1' : '0')
        );
        return $result;
    }

    public function insertCdkeyHistoryLog($userRole, $cpId, $sysMsg = '', $userMsg = '')
    {
        $modelCustomProductsCodeLog = new CustomProductsCodeLogBase();

        $saveData = array(
            'custom_products_code_log_user' => Yii::app()->user->id,
            'custom_products_code_log_user_role' => $userRole,
            'log_ip' => Yii::app()->frontPageCom->getIPAddress(),
            'log_time' => date("Y-m-d H:i:s"),
            'custom_products_code_id' => $cpId,
            'log_system_messages' => $sysMsg,
            'log_user_messages' => $userMsg
        );
        $modelCustomProductsCodeLog->setIsNewRecord(true);
        $modelCustomProductsCodeLog->setAttributes($saveData);
        $modelCustomProductsCodeLog->validate();
        $modelCustomProductsCodeLog->save();
    }

    public function dateShort($rawDate)
    {
        if (($rawDate == '0000-00-00 00:00:00') || empty($rawDate)) {
            return false;
        }

        $year = substr($rawDate, 0, 4);
        $month = (int)substr($rawDate, 5, 2);
        $day = (int)substr($rawDate, 8, 2);
        $hour = (int)substr($rawDate, 11, 2);
        $minute = (int)substr($rawDate, 14, 2);
        $second = (int)substr($rawDate, 17, 2);

        if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
            return date(Yii::t('myogm', 'DATE_FORMAT'), mktime($hour, $minute, $second, $month, $day, $year));
        } else {
            return preg_replace('/2037$/', $year,
                date(Yii::t('myogm', 'DATE_FORMAT'), mktime($hour, $minute, $second, $month, $day, 2037)));
        }
    }

}