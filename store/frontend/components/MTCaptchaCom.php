<?php

/**
 * The reCAPTCHA server URL's
 */
class MTCaptchaCom extends CApplicationComponent
{

    function load_html()
    {
        $site_key = Yii::app()->params['MTCAPTCHA_CONFIG']['SITE_KEY'];
        $return_html =<<<HTML
            <script>
    var mtcaptchaConfig = {
        "sitekey": "$site_key"
    };
   (function(){var mt_service = document.createElement('script');mt_service.async = true;mt_service.src = 'https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js';(document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(mt_service);
       var mt_service2 = document.createElement('script');mt_service2.async = true;mt_service2.src = 'https://service2.mtcaptcha.com/mtcv1/client/mtcaptcha2.min.js';(document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(mt_service2);}) ();
   </script>
<div class="mtcaptcha"></div>
HTML;
        return $return_html;
    }

    function verify_response($response)
    {
        if (empty($response['mtcaptcha-verifiedtoken'])) {
            return false;
        }
        $curl = new CurlCom();

        $config = Yii::app()->params['MTCAPTCHA_CONFIG'];
        $curl->setProxy(Yii::app()->params['PROXY']);

        $url = $config['API_SERVER'].'/checktoken?privatekey='.$config['PRIVATE_KEY'].'&token='.$response['mtcaptcha-verifiedtoken'];

        $resp = $curl->sendGet($url,'',true);

        return (!empty($resp["success"]) ? $resp["success"] : false);
    }
}

