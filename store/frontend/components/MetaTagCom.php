<?php

class MetaTagCom
{

    public static function getDefaultSeoMetaTag()
    {
        return array(
            'title' => Yii::t('meta_tag', 'TEXT_DEFAULT_TITLE'),
            'keyword' => Yii::t('meta_tag', 'TEXT_DEFAULT_KEYWORDS'),
            'description' => Yii::t('meta_tag', 'TEXT_DEFAULT_DESCRIPTION')
        );
    }

    public static function registerMetaTag($meta_tag_info)
    {
        $default_meta_tag = self::getDefaultSeoMetaTag();

        if ($meta_tag_info && isset($meta_tag_info['page_type'])) {
            $id = (!empty($meta_tag_info['id']) ? $meta_tag_info['id'] : 0);
            $seo_content = (new CustomSeoModel())->getSeoMetaContent($meta_tag_info['page_type'], $id, Yii::app()->session['language_id']);
            if (!empty($seo_content)) {
                extract($seo_content);
                if (!empty($meta_tag_info['title'])) {
                    $title = sprintf($title, $meta_tag_info['title']);
                    $keyword = sprintf($keyword, $meta_tag_info['title']);
                    $description = sprintf($description, $meta_tag_info['title']);

                    if (in_array($meta_tag_info['page_type'], ["category", "game blog", "products"])) {
                        $title .= Yii::t('meta_tag', 'DATE_' . strtoupper(date("M")), ['{SYS_YEAR}' => date("Y")]);
                    }
                }
            } else {
                extract(self::fallbackMetaTag($meta_tag_info, $default_meta_tag));
            }
        } elseif (isset($meta_tag_info['title']) && isset($meta_tag_info['keyword']) && isset($meta_tag_info['description'])) {
            extract($meta_tag_info);
        } else {
            extract($default_meta_tag);
        }

        if (empty($keyword)) {
            $keyword = $default_meta_tag['keyword'];
        }
        Yii::app()->clientScript->registerMetaTag($keyword, 'keywords');
        Yii::app()->clientScript->registerMetaTag($description, 'description');
        Yii::app()->clientScript->registerMetaTag('https://d130xiciw9h9wz.cloudfront.net/infolink/484%20x%20252.jpg', null, null, array('property' => 'og:image'));

        unset($default_meta_tag);

        return $title;
    }

    public static function fallbackMetaTag($meta_tag_info, $default_meta_tag)
    {
        switch (strtolower($meta_tag_info['page_type'])) {
            case 'browse':
            case 'game key':
                $title = Yii::t('meta_tag', 'TEXT_BUY_TITLE') . ' ' . $meta_tag_info['title'] . ' - ' . $default_meta_tag['title'];
                $keyword = Yii::t('meta_tag', 'TEXT_BUY_KEYWORD') . ' ' . $meta_tag_info['title'];
                $description = $default_meta_tag['description'];
                break;
            case 'search':
                $title = (($meta_tag_info['title'] != '' && $meta_tag_info['title'] != Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE')) ? $meta_tag_info['title'] : Yii::t(
                        'meta_tag',
                        'TEXT_ALL_PRODUCTS_TITLE'
                    )) . ' - ' . $default_meta_tag['title'];
                $keyword = (($meta_tag_info['keywords'] != '' && $meta_tag_info['keywords'] != Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE')) ? $meta_tag_info['keywords'] : Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_KEYWORD'));
                $description = $default_meta_tag['description'];
                break;
            case 'category':
            case 'game_blog':
                $title = Yii::t('meta_tag', 'TEXT_BUY_TITLE') . ' ' . $meta_tag_info['title'] . ' - ' . $default_meta_tag['title'];
                $keyword = $meta_tag_info['keywords'];
                $description = $meta_tag_info['description'];

                $title .= Yii::t('meta_tag', 'DATE_' . strtoupper(date("M")), ['{SYS_YEAR}' => date("Y")]);
                break;
            case 'products':
                $title = Yii::t('meta_tag', 'TEXT_CHEAP_TITLE') . ' ' . $meta_tag_info['title'] . ' - ' . $default_meta_tag['title'];
                $keyword = Yii::t('meta_tag', 'TEXT_CHEAP_KEYWORD') . ' ' . $meta_tag_info['title'];
                $description = $meta_tag_info['description'];

                $title .= Yii::t('meta_tag', 'DATE_' . strtoupper(date("M")), ['{SYS_YEAR}' => date("Y")]);
                break;
            case 'main_page':
                $title = Yii::t('meta_tag', 'TEXT_MAIN_TITLE') . ' - ' . $default_meta_tag['title'];
                $keyword = $default_meta_tag['keywords'];
                $description = $default_meta_tag['description'];
                break;
            default:
                extract($default_meta_tag);
        }
        $return_arr = ['title' => $title, 'keyword' => $keyword, 'description' => $description];

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'custom-seo/get-seo-content/' . $meta_tag_info['page_type'] . '/' . (!empty($meta_tag_info['id']) ? $meta_tag_info['id'] : 0) . '/' . Yii::app()->session['language_id'];

        Yii::app()->cache->set($cache_key, $return_arr, 86400); // cache 1 day

        return $return_arr;
    }

    public static function getFirstParagraph($content, $html_tag)
    {
        $content = preg_replace('/<h1[^>]*>([\s\S]*?)<\/h1[^>]*>/', '', $content);
        $parts = explode($html_tag, $content);
        foreach ($parts as $part) {
            $string = trim(strip_tags($part));
            if ($string !== "") {
                return $string;
            }
        }
        $result = trim(strip_tags(implode("", $parts)));
        return $result;
    }

}
