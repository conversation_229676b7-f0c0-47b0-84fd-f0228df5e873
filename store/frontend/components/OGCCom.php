<?php

class OGCCom extends GiftCardCom
{

    public function _authenticate()
    {
        $url = $this->api_url . '/api_auth.php';
        $hash = time();
        $auth = md5($this->api_merchant . $this->api_secret . $hash);
        $data = array(
            'merchant_code' => $this->api_merchant,
            'auth' => $auth,
            'hash' => $hash,
            'scope' => $this->api_scope
        );

        $curl_obj = new CurlCom();
        $result = $curl_obj->sendPost($url, $data, true);
        
        return $result;
    }

    public function _merchant_error($resp = array())
    {
        $result = array(
            "status" => false,
            "error" => ""
        );

        if (empty($resp) || !isset($resp["status"]) || !isset($resp["redeem"]) || (!$resp["status"] && isset($resp["error"]) && !$resp["error"])) {
            $result["error"] = Yii::t("storeCredit", "ERROR_REDEEM_FAIL");
        } else {
            if (isset($resp["error"]) && $resp["error"]) {
                $result["error"] = $resp["error"];
            } else if ($resp["status"] == 0) {
                $result["error"] = Yii::t("storeCredit", "ERROR_REDEEM_FAIL");
            } else if ($resp["redeem"] == 1) {
                $result["error"] = Yii::t("storeCredit", "ERROR_REDEEM_FAIL_REDEEMED");
            } else {
                if (isset($resp['start_date']) && isset($resp['end_date'])) {
                    $start_date = strtotime($resp['start_date'] . ' 00:00:00');
                    $end_date = strtotime($resp['end_date'] . ' 23:59:59');
                    if (time() < $start_date || time() > $end_date) {
                        $result["error"] = Yii::t("storeCredit", "ERROR_REDEEM_FAIL_EXPIRED");
                    } else if (!Yii::app()->currency->currencies[$resp['currency']]) {
                        $result["error"] = Yii::t("storeCredit", "ERROR_REDEEM_FAIL_INVALID_CURRENCY");
                    } else {
                        $result["status"] = true;
                    }
                } else {
                    $result["status"] = true;
                }
            }
        }

        return $result;
    }

    public function redeem($input)
    {
        $result = array(
            "status" => false,
            "result" => array(),
            "error" => ""
        );

        $this->_merchant($input);
        if ($this->code) {
            $data = array();

            $this->api_scope = 'redeem';
            $auth = $this->_authenticate();
            if (isset($auth['token']) && !empty($auth['token'])) {
                $this->api_url = $this->api_url . '/api_redeem.php';
                $data = array(
                    'pin' => $input["rd_pin"],
                    'serial' => $input["rd_serial"],
                    'token' => $auth['token']
                );

                $curl_obj = new CurlCom();
                $response = $curl_obj->sendPost($this->api_url, $data, true);

                $resp = $response;
                if (isset($resp["success"])) {
                    $result["status"] = true;
                } else {
                    $stat = $this->_merchant_error($resp);
                    $result["error"] = $stat["error"];
                }
            }
        }

        return $result;
    }

    public function validatePin($input)
    {
        $result = array("status" => false, "result" => array(), "error" => "");

        // Check local db first if its already inserted
        if ($gcInfo = GiftCardRedemptionBase::model()->verifyGiftCardRedemption($input["rd_serial"])) {
            // match with customer_id
            if (empty($gcInfo['transaction_id']) && $gcInfo['customers_id'] == Yii::app()->user->id) {
                $result = array(
                    "status" => true,
                    "result" => array(
                        'currency' => $gcInfo['gift_card_currency_code'],
                        'deno' => $gcInfo['gift_card_deno'],
                        'og_db' => true,
                    )
                );
            } else {
                $result = array("status" => 2, "result" => array(), "error" => "");
            }
        } else {
            $this->_merchant($input);
            if ($this->code) {
                $data = array();

                $this->api_scope = 'getPinInfo';
                $auth = $this->_authenticate();
                
                if (isset($auth['token']) && !empty($auth['token'])) {
                    $this->api_url = $this->api_url . '/api_get_balance.php';
                    $data = array(
                        'pin' => $input["rd_pin"],
                        'serial' => $input["rd_serial"],
                        'token' => $auth['token']
                    );
                    
                    $curl_obj = new CurlCom();
                    $response = $curl_obj->sendPost($this->api_url, $data, true);

                    $resp = $response;
                    $stat = $this->_merchant_error($resp);

                    if ($stat["status"]) {
                        $result["status"] = true;
                        $result["result"] = $resp;
                    } else {
                        $result["error"] = $stat["error"];
                    }
                }
            }
        }

        return $result;
    }
}
