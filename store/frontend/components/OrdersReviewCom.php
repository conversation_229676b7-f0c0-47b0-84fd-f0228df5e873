<?php

class OrdersReviewCom extends MainCom
{
    public function getReviewLayout($orders_id, $op_row, $review_row, $readonly = false)
    {
        $model = new OrderReviewModel();
        $orders_products_id = $op_row['orders_products_id'];
        $product_name = $op_row['products_name'];
        $data = false;
        $review_id = 0;
        $comment = '';
        if (!empty($review_row)) {
            extract($review_row);
            $data = true;
        }
        $result = '';

        $star_li = '';
        for ($i = 1; $i <= 5; $i++) {
            $star_class = 'star';
            if ($data) {
                $star_class .= ($i <= $review_score ? ' selected' : '');
            }
            $star_class .= ($readonly ? ' noclick' : '');
            $star_li .= <<<STAR
<li class="$star_class" data-value="$i">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <use xlink:href="#rating-star"></use>
    </svg>
</li>
STAR;
        }

        $result .= '<div class="order-review-table" id="' . $orders_products_id . '" data-rid="' . ($data ? $review_id : '') . '" data-oid="' . $orders_id . '">';
        $result .= <<<TABLE
    <div class="order-review-table-col left">
        <div class="order-review-table-row">
            <H2 class="order-review-item-title">$product_name</H2>
        </div>
        <div class="order-review-table-row">
            <section class="rating-widget-small">
                <div class="rating-stars text-left rating-stars-small" id="">
                    <ul class="order-review-stars" id="$orders_products_id">
                        $star_li
                    </ul>
                </div>
            </section>
        </div>
    </div>
    <div class="order-review-table-col right">
        <div style="height:100%; width:100%; display:inline-block">
TABLE;
        $result .= '<textarea class="order-review-textarea" id="' . $orders_products_id . '" placeholder="The code works!" maxlength="255" rows="5"' . ($readonly ? ' disabled' : '') . '>' . ($data ? htmlspecialchars($comment) : '') . '</textarea>';
        $result .= '<span class="order-review-text-length" id="' . $orders_products_id . '"' . ($readonly ? ' style="display: none"' : '') . '></span>';
        $result .= <<<TABLE
            <span class="order-review-text-warning" id="$orders_products_id" style="display: none"></span>
        </div>
    </div> 
</div>
TABLE;
        $remarks = $model->getReviewRemarksByRid($review_id);
        if (!empty($remarks)) {
            $remark_title = Yii::t('page_content', 'TEXT_OGM_REPLY');
            $result .= <<<REMARKS
<div class="order-review-remark-holder">
<div class="order-review-table-col left">
</div>
<div class="order-review-table-col right">
REMARKS;
            foreach ($remarks as $remark_row) {
                $remark = htmlspecialchars($remark_row['remarks']);
                $datetime = date('Y-m-d H:i:s', $remark_row['created_at']);
                $result .= <<<REMARKS
<div class="order-review-remark">
    <div class="order-review-remark-content title">$remark_title</div>
    <div class="order-review-remark-content remarks">$remark</div>
    <div class="order-review-remark-content datetime">$datetime</div>
</div>
<hr class="solid" style="margin: 3px 0">
REMARKS;

            }
            $result .= '</div></div>';
        }

        return $result;
    }

    public function openOrdersReviewByOid($orders_id)
    {
        $op_model = new OrdersProducts();

        $op = $op_model->getOrderProducts($orders_id);
        $result = '';

        $title = Yii::t('page_content', 'TEXT_REVIEW_TITLE');

        $result .= <<<HTML
<div class="order-review-item-title">$title</div>
<div class="order_review-div">
HTML;
        $edit = false;

        foreach ($op as $op_row) {

            if ($op_row['orders_products_is_compensate'] == '1' || $op_row['products_bundle_id'] != '0') {
                continue;
            }

            $model = new OrderReviewModel();

            $orders_products_id = $op_row['orders_products_id'];

            $order_review_record = $model->getLatestOrdersReviewByOpid($orders_products_id);

            $review_id = null;

            if (empty($order_review_record) || $order_review_record['status'] == '3') {
                $result .= $this->getReviewLayout($orders_id, $op_row, $order_review_record);
                $edit = true;
            } else {
                $result .= $this->getReviewLayout($orders_id, $op_row, $order_review_record, true);
            }

        }
        $result .= '</div>';
        if ($edit) {
            $result .= '<hr class="solid" style="margin:16px 0;' . ($edit ? '"' : 'style="display: none"') . '>';
            $result .= '<div class="order-review-btn-container" ><a href="javascript:void(0)" class="btn btn-green order-review__btn" id="$orders_id"' . ($edit ? '' : 'style="display: none"') . '>' . Yii::t('page_content', 'BTN_SAVE_CHANGES') . '</a></div>';
        }
        return $result;
    }

    public function sendReviewNotification($model, $orders_products)
    {
        $msorder = new MsOrderModel();
        $emailDataSqs = array(
            'filetype' => 'order_email',
            'email_template' => 'order-low-rating',
            'email_subject_prefix' => '',
            'store_owner' => ConfigurationBase::model()->getConfigValue('STORE_OWNER'),
            'customer' => array(
                'id' => $model->customer_id,
                'firstname' => $model->customer_name,
                'language' => $model->language,
                'email' => $model->customer_email,
            ),
            'orders' => array(
                'orders_id' => $model->order_id,
                'orders_products' => $orders_products,
                'date_purchased' => $model->order_date,
            ),
        );
        $msorder->pushMailQueue($emailDataSqs);
    }

    public function getThirdPartyReviewPlatform()
    {
        // $selectedPlatform = [
        //     'name' => 'ResellerRatings',
        //     'url' => 'https://www.resellerratings.com/store/survey/new/Offgamers_com/rating/5',
        //     'range' => [41, 100],
        // ];
        // $platforms = [
        //     [
        //         'name' => 'Trustpilot',
        //         'url' => 'https://www.trustpilot.com/evaluate/www.offgamers.com?stars=5',
        //         'range' => [1, 20],
        //     ],
        //     [
        //         'name' => 'Google Reviews',
        //         'url' => 'https://g.page/r/CW15F0yu4ytFEBM/review',
        //         'range' => [21, 40],
        //     ],
        //     [
        //         'name' => 'ResellerRatings',
        //         'url' => 'https://www.resellerratings.com/store/survey/new/Offgamers_com/rating/5',
        //         'range' => [41, 100],
        //     ],
        // ];

        // $random = rand(1, 100);
        // foreach ($platforms as $platform) {
        //     if ($random >= $platform['range'][0] && $random <= $platform['range'][1]) {
        //         $selectedPlatform = $platform;
        //         break;
        //     }
        // }

        return [
            'name' => 'Trustpilot',
            'url' => 'https://www.trustpilot.com/evaluate/www.offgamers.com?stars=5',
            'range' => [1, 20],
        ];
    }

}