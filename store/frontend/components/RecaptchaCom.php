<?php

/**
 * The reCAPTCHA server URL's
 */
class RecaptchaCom extends CApplicationComponent
{

    function load_html()
    {
        $return_html  = "<script src='//www.google.com/recaptcha/api.js'></script>\n";
        $return_html .= "<div class='g-recaptcha' style='width:300px;' data-sitekey=" . Yii::app()->params['RECAPTCHA_CONFIG']['PUBLIC_KEY'] . "></div>";
        return $return_html;
    }

    function verify_response($response)
    {
        if (empty($response['g-recaptcha-response'])) {
            return false;
        }
        $curl = new CurlCom();
        $postData = array(
            'secret' => Yii::app()->params['RECAPTCHA_CONFIG']['PRIVATE_KEY'],
            'response' => $response['g-recaptcha-response']);

        $url = Yii::app()->params['RECAPTCHA_CONFIG']['RECAPTCHA_API_SECURE_SERVER'];
        $resp = $curl->sendPost($url, $postData, true, true);
        
        return $resp["success"];
    }
}
