<?php

class RegionalSettingCom
{
    const REGIONAL_COOKIE_NAME = 'ogm_regional';

    public static function curl($type = 'country')
    {
        $curl_obj = Yii::app()->curl;
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'regional/' . $type;
        $return_array = Yii::app()->cache->get($cache_key);

        if ($return_array === false) {
            switch ($type) {
                case 'country':
                    if ($return_str = $curl_obj->readRSS(Yii::app()->params['REGIONAL_SETTING']['COUNTRY_STATIC_DOMAIN'])) {
                        $return_array = CJSON::decode(preg_replace('/.+?({.+}).+/', '$1', $return_str));
                    }
                    break;
                case 'region':
                    if ($return_str = $curl_obj->readRSS(Yii::app()->params['REGIONAL_SETTING']['REGION_STATIC_DOMAIN'])) {
                        $return_array = CJSON::decode(preg_replace('/.+?({.+}).+/', '$1', $return_str));
                    }
                    break;
            }
            if (!empty($return_array)) {
                Yii::app()->cache->set($cache_key, $return_array, 3600);
            } else {
                $subject = 'Failed to fetch regional data from S3';
                Yii::app()->slack->send($subject);
                throw new CHttpException(500, 'Unable to read regional setting');
            }
        }

        return $return_array;
    }

    public static function checkRegionalSetting($saveRegional = false)
    {
        $iso2 = self::_getCountryIso2(Yii::app()->session['customers_login_ip']);

        if (isset(Yii::app()->session['country_code'])) {
            if (
                ($iso2 != Yii::app()->session['country_code'] || in_array(Yii::app()->session['currency'], json_decode(self::getExcludedCurrencyJSON())))
                && !Yii::app()->user->isGuest
                && isset(Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID'])
                && !in_array(Yii::app()->user->id, Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID'])
            ) {
                Yii::app()->session->offsetUnset('country');
                Yii::app()->session->offsetUnset('country_code');
                Yii::app()->params["regionalSettingChecked"] = false;
            }
        }

        if ((isset(Yii::app()->params["regionalSettingChecked"]) && Yii::app()->params["regionalSettingChecked"]) && !$saveRegional) {
            return;
        }

        if ($saveRegional === true) {
            $p = new CHtmlPurifier();
            if (isset($_REQUEST['reg_ctry']) && (Yii::app()->user->isGuest || (isset(Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID']) && in_array(
                            Yii::app()->user->id,
                            Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID']
                        )))) {
                $country_code = $p->purify($_REQUEST['reg_ctry']);
            } else {
                $country_code = Yii::app()->session['country_code'];
            }

            $cookieValueArray = [];

            if ($regional_cookies = CookiesCom::readCookie(self::REGIONAL_COOKIE_NAME)) {
                $cookieValueArray = json_decode($regional_cookies, true);
            }

            $postDataArray = array(
                'reg_ctry' => $country_code,
                'reg_cur' => (isset($_REQUEST['reg_cur'])) ? $p->purify($_REQUEST['reg_cur']) : (Yii::app()->session['currency'] ?? $cookieValueArray['currency'] ?? self::_getDefaultCurrency($country_code)),
                'reg_lang' => $p->purify($_REQUEST['reg_lang'] ?? $cookieValueArray['language'] ?? 'en')
            );

            $regionalSettingData = self::_validatePostRegionalData($postDataArray);
            self::_updateRegionalCookie($regionalSettingData['currency'], $regionalSettingData['language']);
            self::_saveRegionalData($regionalSettingData);
        } elseif (!isset(Yii::app()->session['country']) || (isset(Yii::app()->session['country']) && empty(Yii::app()->session['country']))) {
            $regionalSettingData = self::_getDefaultRegionalData();

            if (is_null(CookiesCom::readCookie(self::REGIONAL_COOKIE_NAME))) {
                if (isset($iso2) && !empty($iso2)) {
                    $countryJsonData = self::curl('country');

                    if ($countryJsonData && isset($countryJsonData[$iso2])) {
                        // get default language and currency based on country code
                        $languageArray = self::_getDefaultLanguage($iso2);
                        $currency = self::_getDefaultCurrency($iso2);
                        $regionalSettingData = array(
                            "country" => $countryJsonData[$iso2]['id'],
                            "country_code" => $iso2,
                            "currency" => $currency,
                            "language" => $languageArray['language'],
                            "language_name" => $languageArray['language_name']
                        );
                    }
                }
            } else {
                $cookieValueArray = json_decode(CookiesCom::readCookie(self::REGIONAL_COOKIE_NAME), true);
                $p = new CHtmlPurifier();
                $postDataArray = array(
                    'reg_ctry' => (Yii::app()->user->isGuest) ? ((isset($_REQUEST['reg_ctry'])) ? $p->purify($_REQUEST['reg_ctry']) : $iso2) : $iso2,
                    'reg_cur' => ($cookieValueArray['currency'] ?? self::_getDefaultCurrency($iso2)),
                    'reg_lang' => ($cookieValueArray['language'] ?? 'en')
                );
                $regionalSettingData = self::_validatePostRegionalData($postDataArray);
            }

            self::_updateRegionalCookie($regionalSettingData['currency'], $regionalSettingData['language']);
            self::_saveRegionalData($regionalSettingData);
        }

        Yii::app()->params["skipedCookieCurrency"] = false;
        Yii::app()->params["regionalSettingChecked"] = true;
        Yii::app()->language = Yii::app()->session['language'];
    }

    public static function _updateRegionalCookie($currency, $language)
    {
        $cookie_expire_time = time() + 60 * 60 * 24 * 365;
        $cookieValue = array(
            'currency' => $currency,
            'language' => $language
        );
        CookiesCom::storeCookieArray(self::REGIONAL_COOKIE_NAME, $cookieValue, 0, $cookie_expire_time);
    }

    public static function saveCustomerPreference()
    {
        if (Yii::app()->user->id) {
            $preferenceKey = array(
                'country' => 'country_code',
                'language' => 'language',
                'currency' => 'currency'
            );
            foreach ($preferenceKey as $key => $value) {
                CustomersPreference::model()->updateCustomersPreference(Yii::app()->user->id, $key, Yii::app()->session[$value]);
            }
        }
    }

    private static function _saveRegionalData($regionalSettingData)
    {
        if (!(isset($regionalSettingData['country']) && !empty($regionalSettingData['country']))) {
            $regionalSettingData = self::_getDefaultRegionalData();
        }
        Yii::app()->session['country'] = $regionalSettingData['country'];
        Yii::app()->session['country_code'] = $regionalSettingData['country_code'];
        Yii::app()->session['country_name'] = self::getUserCountryName();
        Yii::app()->language = $regionalSettingData['language'];
        Yii::app()->session['language'] = $regionalSettingData['language'];
        Yii::app()->session['language_id'] = Yii::t('dev', 'LANG_CODE_TO_ID');
        Yii::app()->session['language_name'] = $regionalSettingData['language_name'];
        Yii::app()->session['currency'] = $regionalSettingData['currency'];
        Yii::app()->session['default_currency'] = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_CURRENCY'];
        Yii::app()->session['default_language_id'] = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE_ID'];

        self::saveCustomerPreference();
    }

    private static function _getDefaultRegionalData()
    {
        return array(
            "country" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_COUNTRY'], // 129
            "country_code" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_COUNTRY_CODE'],
            "currency" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_CURRENCY'], // USD
            "language" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE'], // en
            "language_id" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE_ID'], // 1
            "language_name" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE_NAME'], // English
            "default_language_id" => Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE_ID'], // 1
        );
    }

    private static function _getDefaultCurrency($countryCode)
    {
        $regionJsonData = self::curl('region');
        $currency = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_CURRENCY'];

        if ($regionJsonData) {
            if (isset($regionJsonData[$countryCode]['currency']['def']) && !empty($regionJsonData[$countryCode]['currency']['def'])) {
                // Check the defaul currency according to the country code is in the restriction list
                $_excl_cur = json_decode(self::getExcludedCurrencyJSON());
                if (in_array($regionJsonData[$countryCode]['currency']['def'], $_excl_cur)) {
                    // get the first currency not in the restriction list to be set as default
                    foreach ($regionJsonData[$countryCode]['currency']['val'] as $key => $value) {
                        if (!in_array($key, $_excl_cur)) {
                            return $key;
                        }
                    }
                    // return store default currency if cannot find currency from json list
                    return $currency;
                }
                // Set default currency usign country default currency setting from json
                if (isset($regionJsonData[$countryCode]['currency']['val'][$regionJsonData[$countryCode]['currency']['def']])) {
                    $currency = $regionJsonData[$countryCode]['currency']['def'];
                }
            }
        }
        // return store default currency if json list empty
        return $currency;
    }

    private static function _getDefaultLanguage($iso2)
    {
        $mapping = [
            'cn' => 'cn',
            'tw' => 'cn',
            'hk' => 'cn',
            'id' => 'id',
            // 'vn' => 'vi',
            // 'th' => 'th',
            // 'mm' => 'mm',
            // 'kr' => 'ko',
            // 'jp' => 'jp',
            // 'de' => 'de',
            // 'ch' => 'de',
            // 'at' => 'de',
            // 'es' => 'es',
            // 'mx' => 'es',
            // 'ar' => 'es',
            // 'co' => 'es',
            // 'cl' => 'es',
            // 'pe' => 'es',
            // 'ec' => 'es',
            // 'gt' => 'es',
            // 'pa' => 'es',
            // 'cr' => 'es',
            // 'uy' => 'es',
            // 'py' => 'es',
            // 'bo' => 'es',
            // 'sv' => 'es',
            // 'hn' => 'es',
            // 'ni' => 'es',
            // 'gq' => 'es',
            // 'gn' => 'es',
            // 'fr' => 'fr',
            // 'lu' => 'fr',
            // 'ga' => 'fr',
            // 'ht' => 'fr',
            // 'vu' => 'fr',
            // 'ml' => 'fr',
            // 'bf' => 'fr',
            // 'td' => 'fr',
            // 'bi' => 'fr',
            // 'sc' => 'fr',
            // 'km' => 'fr',
            // 'pt' => 'pt',
            // 'br' => 'pt',
            // 'tl' => 'pt',
            // 'st' => 'pt',
            // 'cv' => 'pt',
            // 'it' => 'it',
            // 'in' => 'hi',
            // 'bd' => 'bn',
            // 'pk' => 'pa',
            // 'dz' => 'ar',
            // 'bh' => 'ar',
            // 'eg' => 'ar',
            // 'iq' => 'ar',
            // 'jo' => 'ar',
            // 'kw' => 'ar',
            // 'lb' => 'ar',
            // 'ly' => 'ar',
            // 'ma' => 'ar',
            // 'om' => 'ar',
            // 'ps' => 'ar',
            // 'qa' => 'ar',
            // 'sa' => 'ar',
            // 'sd' => 'ar',
            // 'sy' => 'ar',
            // 'tn' => 'ar',
            // 'ae' => 'ar',
            // 'ye' => 'ar',
            // 'dj' => 'ar',
            // 'mr' => 'ar',
            // 'so' => 'ar',
            // 'ru' => 'ru',
            // 'bl' => 'ru',
            // 'gh' => 'ha',
            // 'ci' => 'ha',
            // 'ne' => 'ha',
            // 'sn' => 'ha',
            // 'bj' => 'ha',
            // 'tg' => 'ha',
            // 'sl' => 'ha',
            // 'lr' => 'ha',
            // 'gm' => 'ha',
            // 'gw' => 'ha',
            // 'sh' => 'ha',
            // 'et' => 'sw',
            // 'tz' => 'sw',
            // 'ke' => 'sw',
            // 'ug' => 'sw',
            // 'mz' => 'sw',
            // 'mg' => 'sw',
            // 'mw' => 'sw',
            // 'zm' => 'sw',
            // 'zw' => 'sw',
            // 'ss' => 'sw',
            // 'rw' => 'sw',
            // 'er' => 'sw',
            // 'mu' => 'sw',
            // 're' => 'sw',
            // 'yt' => 'sw',
            // 'nl' => 'nl',
            // 'ro' => 'ro',
            // 'kh' => 'kh',
            // 'la' => 'la',
        ];
        $country_code = strtolower($iso2);
        $language_list = self::getLanguageList();
        $languageArray = array();
        if (isset($mapping[$country_code])) {
            $languageArray['language'] = $mapping[$country_code];
            $languageArray['language_name'] = $language_list[$languageArray['language']];
        } else {
            $languageArray['language'] = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE'];
            $languageArray['language_name'] = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_LANGUAGE_NAME'];
        }

        return $languageArray;
    }

    public static function _getCountryIso2($ipAddress)
    {
        $iso2 = '';
        // Check mobile country phone
        $mobileCountry = false;
        if (Yii::app()->user->id) {
            $mobileCountry = self::getMobileCountry();
        }

        if (isset($mobileCountry['phone_country_code'])) {
            $iso2 = $mobileCountry['phone_country_code'];
        } else { // Use IP if mobile checking not exist
            // IP checking
            $countryInfo = Yii::app()->geoip->getIPCountryInfo($ipAddress);
            $iso2 = (isset($countryInfo['countries_iso_code_2'])) ? $countryInfo['countries_iso_code_2'] : '';
        }

        return $iso2;
    }

    /*
     * Validate post regional data (country, currency and language)
     */

    private static function _validatePostRegionalData($postDataArray)
    {
        $countryJsonData = self::curl('country');
        $regionJsonData = self::curl('region');
        $regionalSettingData = self::_getDefaultRegionalData();
        // Check POST country id is valid
        if ($countryJsonData && isset($postDataArray['reg_ctry']) && isset($countryJsonData[$postDataArray['reg_ctry']])) {
            $regionalSettingData['country'] = $countryJsonData[$postDataArray['reg_ctry']]['id'];
            $regionalSettingData['country_code'] = $postDataArray['reg_ctry'];

            $languageArray = self::_getDefaultLanguage($postDataArray['reg_ctry']);
            $regionalSettingData['language'] = $languageArray['language'];
            $regionalSettingData['language_name'] = $languageArray['language_name'];
            $regionalSettingData['currency'] = self::_getDefaultCurrency($postDataArray['reg_ctry']);

            if ($regionJsonData) {
                // Check POST language id is valid
                if (isset(self::getLanguageList()[$postDataArray['reg_lang']])) {
                    $regionalSettingData['language'] = $postDataArray['reg_lang'];
                    $regionalSettingData['language_name'] = self::getLanguageList()[$postDataArray['reg_lang']];
                }
                // Check POST currency is valid
                if (isset($postDataArray['reg_cur'])) {
                    if (isset($regionJsonData[$postDataArray['reg_ctry']]['currency']['val'][$postDataArray['reg_cur']])) {
                        $regionalSettingData['currency'] = $postDataArray['reg_cur'];
                    } else {
                        if (isset($regionJsonData['default']['currency']['val'][$postDataArray['reg_cur']])) {
                            $regionalSettingData['currency'] = $postDataArray['reg_cur'];
                        }
                    }
                }
            }
        } else {
            // Check IP
            $iso2 = self::_getCountryIso2(Yii::app()->frontPageCom->getIPAddress());

            $countryJsonData = self::curl('country');
            if ($countryJsonData && isset($countryJsonData[$iso2])) {
                // get default language and currency based on country code
                $languageArray = self::_getDefaultLanguage($iso2);
                $currency = self::_getDefaultCurrency($iso2);
                $regionalSettingData = array(
                    "country" => $countryJsonData[$iso2]['id'],
                    "country_code" => $iso2,
                    "currency" => $currency,
                    "language" => $languageArray['language'],
                    "language_name" => $languageArray['language_name']
                );
            }
        }

        return $regionalSettingData;
    }

    public static function updateLanguage($lang)
    {
        if (Yii::app()->session['country_code'] && Yii::app()->session['currency']) {
            if ($lang != Yii::app()->session['language']) {
                $postDataArray = array(
                    'reg_ctry' => Yii::app()->session['country_code'],
                    'reg_cur' => Yii::app()->session['currency'],
                    'reg_lang' => $lang
                );

                if ($regionalSettingData = self::_validatePostRegionalData($postDataArray)) {
                    self::_updateRegionalCookie($regionalSettingData['currency'], $regionalSettingData['language']);
                    self::_saveRegionalData($regionalSettingData);
                }
            }
        } else {
            if ($cdata = CookiesCom::readCookie(self::REGIONAL_COOKIE_NAME)) {
                $cdata_array = json_decode($cdata, true);

                $iso2 = self::_getCountryIso2(Yii::app()->session['customers_login_ip']);

                $p = new CHtmlPurifier();
                $postDataArray = array(
                    'reg_ctry' => $iso2,
                    'reg_cur' => $p->purify($cdata_array['currency']),
                    'reg_lang' => $p->purify($lang)
                );

                if ($regionalSettingData = self::_validatePostRegionalData($postDataArray)) {
                    self::_updateRegionalCookie($regionalSettingData['currency'], $regionalSettingData['language']);
                    self::_saveRegionalData($regionalSettingData);
                }
            } else {
                Yii::app()->params["regionalSettingChecked"] = false;
                self::checkRegionalSetting();
                self::updateLanguage($lang);
            }
        }
    }

    public static function getUserCountryName()
    {
        $country_name = '';
        if (isset(Yii::app()->session['country_code']) && !empty(Yii::app()->session['country_code'])) {
            $m_ctr = Countries::getCountryByIso2(Yii::app()->session['country_code']);
            if (isset($m_ctr->countries_id)) {
                $country_name = $m_ctr->countries_name;
            }
        }
        return $country_name;
    }

    public static function getMobileCountry()
    {
        $result = array();
        $u_data = self::getCustomerInfo(Yii::app()->user->id, 'phone_country_code');
        if (isset($u_data["status"]) && $u_data["status"]) {
            //set user country 
            if (isset($u_data['result']['phone_country_code']) && ($u_data['result']['phone_country_code'])) {
                $result = array(
                    'phone_country_code' => $u_data['result']['phone_country_code'],
                );
                self::regionalCountryCache('s', ['uid' => Yii::app()->user->id], $u_data['result']['phone_country_code'], 86400);
            }
        }
        return $result;
    }

    public static function getCustomerInfo($uid, $requestField = '')
    {
        $url = Yii::app()->params['SHASSO_CONFIG']['API_URI'] . "/profile/userInfo";
        return self::_callAPI($uid, $url, $requestField);
    }

    private static function _callAPI($uid, $url, $requestField = "", $extraParams = array())
    {
        $params = array(
            'merchant' => Yii::app()->params['SHASSO_CONFIG']['MERCHANT_API_ACC'],
            'signature' => md5($uid . '|' . Yii::app()->params['SHASSO_CONFIG']['MERCHANT_API_SECRET']),
            'cid' => $uid,
            'request' => $requestField,
            'extra' => $extraParams
        );

        $proxy_setting_holder = Yii::app()->curl->getProxy();
        Yii::app()->curl->setProxy(false);
        Yii::app()->curl->debug_mode = true;
        $response = Yii::app()->curl->sendPost($url, $params, true);
        Yii::app()->curl->setProxy($proxy_setting_holder);

        $error = Yii::app()->curl->getError();

        if (isset($error['error_code']) && $error['error_code']) {
            self::reportError('RegionalSettingCom:_callAPI', array('error' => $error));
        }

        unset($error);

        return $response;
    }

    public static function getExcludedCurrencyJSON()
    {
        $currencyRestrictionJson = '[]';

        if (!Yii::app()->user->id) {
            return $currencyRestrictionJson;
        }

        if (isset(Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID']) && in_array(Yii::app()->user->id, Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID'])) {
            return $currencyRestrictionJson;
        }

        if (Yii::app()->session['country_code'] && Yii::app()->session['currency']) {
            $iso2 = Yii::app()->session['country_code'];
        } else {
            $iso2 = self::_getCountryIso2(Yii::app()->session['customers_login_ip']);
        }

        // Check if cache exist?
        $restrictionList = self::restrictionCurrencyCache('g', ['country_code' => $iso2]);
        if ($restrictionList) {
            return $restrictionList;
        }

        // Get currency restriction if available
        $loc_rest = LocationRestriction::model()->findByAttributes(array("country_iso_code2" => $iso2));
        $currencyRestrictionArray = [];
        if (isset($loc_rest->id)) {
            $currencyRestrictionArray = json_decode($loc_rest->restriction_info, true);
        }

        //get region json from shasso (old crew setting) and merge with crew2 restriction
        $region = self::curl('region');
        $all_currencies = $region['refer']['currency'];
        $allow_currencies = (isset($region[$iso2]['currency']) ? $region[$iso2]['currency']['val'] : $region['default']['currency']['val']);
        $currency_restrictionArray = [];
        foreach ($all_currencies as $currency => $currency_name) {
            if (!key_exists($currency, $allow_currencies)) {
                $currency_restrictionArray[] = $currency;
            }
        }


        $currencyRestrictionJson = json_encode(array_values(array_unique(array_merge($currencyRestrictionArray, $currency_restrictionArray))));

        // Store in cache reduce db call
        self::restrictionCurrencyCache('s', ['country_code' => $iso2], $currencyRestrictionJson, 86400);

        return $currencyRestrictionJson;
    }

    public static function regionalCountryCache($type = "g", $params = array(), $data = "", $cachePeriod = 604800)
    {
        $cacheKey = Yii::app()->params['MEMCACHE_PREFIX'] . 'regional_country/' . $params['uid'];
        if ($type == "s") {
            return Yii::app()->cache->set($cacheKey, $data, $cachePeriod);
        }
        if ($type == 'd') {
            return Yii::app()->cache->delete($cacheKey);
        } else {
            return Yii::app()->cache->get($cacheKey);
        }
    }

    public static function restrictionCurrencyCache($type = "g", $params = array(), $data = "", $cachePeriod = 604800)
    {
        $cacheKey = Yii::app()->params['MEMCACHE_PREFIX'] . 'restriction_currency/' . $params['country_code'];
        if ($type == "s") {
            return Yii::app()->cache->set($cacheKey, $data, $cachePeriod);
        }
        if ($type == 'd') {
            return Yii::app()->cache->delete($cacheKey);
        } else {
            return Yii::app()->cache->get($cacheKey);
        }
    }


    /**
     * @return array
     */
    public static function getLanguageList()
    {
        return [
            'en' => 'English',
            'cn' => '简体中文',
            'id' => 'Bahasa Indonesia',
            // 'vi' => 'Vietnamese (tiếng Việt)',
            // 'th' => 'Siamese(ภาษาไทย)',
            // 'mm' => 'Burmese (မြန်မာဘာသာ)',
            // 'ko' => 'Korean (한국어)',
            // 'jp' => 'Japanese (日本語)',
            // 'de' => 'German (Deutsch)',
            // 'es' => 'Spanish (Español)',
            // 'fr' => 'France (Français)',
            // 'pt' => 'Portuguese (Português)',
            // 'it' => 'Italian (Italiano)',
            // 'hi' => 'Hindi (Hindī)',
            // 'bn' => 'Bangladesh (Bengali)',
            // 'pa' => 'Pakistan (Punjabi)',
            // 'ar' => '(اَلْعَرَبِيَّةُ) Arabic',
            // 'ru' => 'Russian (Pусский)',
            // 'ha' => 'Hausa (Hausawa)',
            // 'sw' => 'Swahili (Kiswahili)',
            // 'tr' => 'Turkey (Turkish)',
            // 'nl' => 'Dutch (Netherland)',
            // 'ro' => 'Romanian (Romania)',
            // 'kh' => 'Khmer (Khmêr)',
            // 'la' => 'Laos (າສາລາວ)',
        ];
    }

    public static function reportError($subject, $response_data, $ext_subject = '', $channel = "DEFAULT")
    {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' from ' . ((!empty($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : '') . ' - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $response_data
            )
        );
        Yii::app()->slack->send($subject, $attachments, $channel);
    }
}
