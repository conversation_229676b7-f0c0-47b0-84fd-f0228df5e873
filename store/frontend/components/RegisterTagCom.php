<?php

class RegisterTagCom {

    public $macro_info = array(), $tag_info = array();
    public $stack;
    private $rules_validation_info = array();

    public function init() {
        $this->registerMacro('{url}', substr(Yii::app()->controller->createUrl(Yii::app()->controller->action->id), 1));
        $this->stack = array();
    }

    public function registerMacro($key, $macro) {
        $this->macro_info[$key] = $macro;
    }

    public function registerRuleTags() {
        $output = array(
            'html' => array(),
        );
        $match_tag = array();

        $model = new RatingTags();
        list($tag_dataset, $tag_array) = $model->getAllTags();

        if (isset($tag_dataset)) {
            $to_fire_this_tag = true;

            foreach ($tag_dataset as $idx => $rule_array) {
                if ($this->validateRules($rule_array['rule_id'], $rule_array['rule_type'], $rule_array['rule_operator'], $rule_array['rule_value']) === false) {
                    $to_fire_this_tag = false;
                    break;
                } else if ($this->validateRules($rule_array['rule_id'], $rule_array['rule_type'], $rule_array['rule_operator'], $rule_array['rule_value']) != false) {
                    $match_tag[$rule_array['tag_id']][] = $rule_array['rule_id'];
                }

                $count1 = count($match_tag[$rule_array['tag_id']]);
                $count2 = count($tag_array[$rule_array['tag_id']]);

                if ($to_fire_this_tag && $count1 == $count2) {
                    $this->tag_info[] = $rule_array;
                }
            }
        }

        if ($this->tag_info) {
            foreach ($this->tag_info as $key => $row) {
                $tag_method = $row['tag_method'];
                $content_array = json_decode($row['tag_trigger_content'], true);
                $internal_index_sequence = $row['internal_index_sequence'];

                if (count($content_array) > 1) {
                    $content = $content_array[$internal_index_sequence];
                    $count = count($content_array);
                    $range = range(0, $count - 1);
                    $content_shift = array_shift($range);
                    array_push($range, $content_shift);
                    $internal_index_sequence_change = $range[$internal_index_sequence];
                    RatingTags::model()->updateByPk($row['tag_id'], array('internal_index_sequence' => $internal_index_sequence_change, 'last_modified_date' => date('Y-m-d H:i:s')));
                } else {

                    $content = $content_array[0];
                }

                if ($tag_method == 'HTML') {
                    $output['html'][] = htmlspecialchars_decode($content);
                } else if ($tag_method == 'JS') {
                    $output['js'][] = $content;
                }
            }

            if (isset($output['js'])) {
                Yii::app()->clientScript->registerScript('rulesTag', implode('', $output['js']));
            }
        }

        return implode('', $output['html']);
    }

    private function validateRules($rule_id, $rule_type, $rule_operator, $rule_value) {

        if (!isset($this->rules_validation_info[$rule_id])) {
            $this->rules_validation_info[$rule_id] = false;

            if (isset($this->macro_info[$rule_type])) {
                if ($rule_operator === 'contains') {
                    $pos = strpos($rule_value, $this->macro_info[$rule_type]);
                    $this->rules_validation_info[$rule_id] = ($pos === false) ? false : true;
                } else if ($rule_operator === 'does_not_contains') {
                    $pos = strpos($rule_value, $this->macro_info[$rule_type]);
                    $this->rules_validation_info[$rule_id] = ($pos === false) ? true : false;
                } else if ($rule_operator === 'equals') {
                    $this->rules_validation_info[$rule_id] = ($rule_value === $this->macro_info[$rule_type]) ? true : false;
                } else if ($rule_operator === 'does_not_equal') {
                    $this->rules_validation_info[$rule_id] = ($rule_value !== $this->macro_info[$rule_type]) ? true : false;
                } else if ($rule_operator === 'greater') {
                    $this->rules_validation_info[$rule_id] = bccomp($this->macro_info[$rule_type], $rule_value) === 1;
                } else if ($rule_operator === 'lesser') {
                    $this->rules_validation_info[$rule_id] = bccomp($this->macro_info[$rule_type], $rule_value) === -1;
                }
            }
        }

        return $this->rules_validation_info[$rule_id];
    }

//    public function registerRuleTag($param) {
//        $match = addcslashes($param, '%_');
//        $output = array();
//        $tagTrigger = array();
//        $q = new CDbCriteria(array(
//            'condition' => "rule_value LIKE :match",
//            'params' => array(':match' => "%$match%")
//        ));
//        $data_rules = RatingRulesBase::model()->with('rulesTags', 'allTags')->findAll($q);
//        foreach ($data_rules as $data) {
//            foreach ($data->allTags as $row) {
//                $tag_trigger_content = $row['tag_trigger_content'];
//                if ($row['tag_method'] == 'JS') {
//                    $method = 'JS';
//                } elseif ($row['tag_method'] == 'HTML') {
//                    $method = 'HTML';
//                }
//                $tagTrigger['id'][] = $row['id'];
//                $tagTrigger['content'][] = $tag_trigger_content;
//            }
//        }
//        $this->stack = $tagTrigger;
//        if (isset($tagTrigger['content'][0])) {
//            $content = json_decode($tagTrigger['content'][0], true);
//            $output[$method] = htmlspecialchars_decode($content[0]);
//            RatingTagsBase::model()->updateByPk($tagTrigger['id'][0], array('last_modified_date' => date('Y-m-d H:i:s')));
//        }
//
//        return $output;
//    }
}
