<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

class RssFeedCom extends MainCom {
    public static function getRssFeed($feed_url) {
        $xml = FALSE;
        
        $curl_obj = new CurlCom();
        $curl_obj->useragent = 'spider';    // temp
        
        if ($content = $curl_obj->readRSS($feed_url)) {
            if ($xml = @simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA)) {
            } else {
                $xml = FALSE;
            }
        }
        
        unset($curl_obj);
        
        return $xml;
    }
    
    public static function getParsedRSS($feed_url) {
        if ($xml = self::getRssFeed($feed_url)) {
            if (isset($xml->channel)) {
                $cnt = count($xml->channel->item);
                
                for ($i=0; $i<$cnt; $i++) {
                    $url 	= $xml->channel->item[$i]->link;
                    $title 	= $xml->channel->item[$i]->title;
                    $desc = $xml->channel->item[$i]->description;

//                    echo '<a href="'.$url.'">'.$title.'</a>'.$desc.'';
                }
            }
        }
        
        return $rssFeed;
    }

}