<?php

/*
 * http://www.yiiframework.com/doc/api/1.1/CAssetManager & https://github.com/andremetzen/yii-s3assetmanager
 * 'components' = array(
  'assetManager' => array(
  'class' => 'S3AssetManager',
  //            'host' => 'Your-bucket.s3.amazonaws.com', // changing this you can point to your CloudFront hostname
  'bucket' => 'Your-bucket',
  'path' => 'assets', //or any other folder you want
  ),
  )
 */

class S3AssetManager extends CAssetManager {

    const MEMCACHE_DURATION = 86400;   // seconds

    public $enabledFlag;
    public $bucketKey;
    public $host;
    public $secureHost;
    public $path;
    public $vue_path;
    public $aws_obj;
    private $_baseUrl;
    private $_basePath;
    private $_vuePath;
    private $_published;

    /**
     * @return string the root directory storing the published asset files. Defaults to 'WebRoot/assets'.
     */
    public function getBasePath() {
        if ($this->enabledS3()) {
            if ($this->_basePath === null) {
                $this->_basePath = $this->path;
            }

            return $this->_basePath;
        } else {
            return parent::getBasePath();
        }
    }

    public function getVuePath() {
        if ($this->enabledS3()) {
            if ($this->_vuePath === null) {
                $this->_vuePath = $this->vue_path;
            }
            return $this->_vuePath;
        } else {
            return parent::getBasePath();
        }
    }

    public function getBaseUrl($filepath = '') {
        if ($this->enabledS3()) {
            if (!isset($this->_baseUrl[$filepath])) {
                if ($this->host !== '') {
                    $this->_baseUrl[$filepath] = (isset($_SERVER['HTTPS']) ? 'https://' . $this->secureHost : 'http://' . $this->host) . '/' . $filepath;
                } else {
                    $this->getS3()->set_filename('');
                    $this->_baseUrl[$filepath] = $this->getS3()->get_image_url_by_instance();
                }
            }

            return $this->_baseUrl[$filepath];
        } else {
            if ($this->host !== '') {
                return $this->host . parent::getBaseUrl();
            }

            return parent::getBaseUrl();
        }
    }

    private function getS3() {
        if (is_null($this->aws_obj)) {
            $this->aws_obj = new AmazonWsCom();
            $this->aws_obj->set_acl('ACL_PUBLIC');
            $this->aws_obj->set_bucket_key($this->bucketKey);
        }

        return $this->aws_obj;
    }

    private function getCacheKey($path) {
        return $this->hash(Yii::app()->request->serverName) . '.' . $path;
    }

    private function disabledS3() {
        $this->enabledFlag = FALSE;
    }

    private function enabledS3() {
        if ($this->enabledFlag === TRUE) {
            $this->enabledFlag = $this->getS3()->is_aws_s3_enabled();
        } else {
            $this->enabledFlag = FALSE;
        }

        return $this->enabledFlag;
    }

    public function publish($path, $hashByName = false, $level = -1, $forceCopy = false, $vue_path = false) {
        if (isset($this->_published[$path])) {
            return $this->_published[$path];
        } else if (($src = realpath($path)) !== false) {
            if ($this->enabledS3()) {
                $s3_filename = '';
                if($vue_path == true){
                    $cache_key = $this->vue_path . '.' . $this->getCacheKey($path);
                    $s3_filepath = Yii::app()->params['AWS_S3_PREFIX'] . $this->getVuePath();
                } else {
                    $cache_key = $this->path . '.' . $this->getCacheKey($path);
                    $s3_filepath = Yii::app()->params['AWS_S3_PREFIX'] . $this->getBasePath() . '/' . $this->hash(basename($src));
                }

                $this->getS3()->set_filepath($s3_filepath);

                if (is_file($src)) {
                    $s3_filename = '/' . basename($src);

                    if (Yii::app()->cache->get($cache_key) === false) {
                        $this->getS3()->set_filename($s3_filename);
                        $this->getS3()->set_file(array('tmp_name' => $src));
                        $this->getS3()->set_file_header('Cache-Control', 'max-age=2592000');

                        if (!$this->getS3()->save_file()) {
                            // AWS S3 will send error report
                            // temp use local storage
                            $this->disabledS3();
                            $this->publish($path, $hashByName, $level, $forceCopy, $vue_path);
                            exit;
                        }

                        Yii::app()->cache->set($cache_key, true, self::MEMCACHE_DURATION);
                    }
                } else if (is_dir($src)) {
                    if (Yii::app()->cache->get($cache_key) === false) {
                        $files_array = CFileHelper::findFiles($src, array(
                                    'exclude' => $this->excludeFiles,
                                    'level' => $level,
                                        )
                        );

                        foreach ($files_array as $file) {
                            $this->getS3()->set_file(array('tmp_name' => $file));
                            $this->getS3()->set_filename('/' . str_replace($src . DIRECTORY_SEPARATOR, "", $file));
                            $this->getS3()->set_file_header('Cache-Control', 'max-age=2592000');

                            if (!$this->getS3()->save_file()) {
                                // AWS S3 will send error report
                                // temp use local storage
                                $this->disabledS3();
                                $this->publish($path, $hashByName, $level, $forceCopy, $vue_path);
                                exit;
                            }
                        }

                        Yii::app()->cache->set($cache_key, true, self::MEMCACHE_DURATION);
                    }
                }

                return $this->_published[$path] = $this->getBaseUrl($s3_filepath) . $s3_filename;
            } else {
                return $this->_published[$path] = parent::publish($path, $hashByName, $level, $forceCopy);
            }
        }

        throw new CException(Yii::t('yii', 'The asset "{asset}" to be published does not exist.', array('{asset}' => $path)));
    }

    public function getPublishedPath($path, $hashByName = false) {
        if ($this->enabledS3()) {
            return false;   // need to add in for s3 case if needed.
        } else {
            return parent::getPublishedPath($path, $hashByName);
        }
    }

    public function getPublishedUrl($path, $hashByName = false) {
        if (isset($this->_published[$path])) {
            return $this->_published[$path];
        }

        if ($this->enabledS3()) {
            if (($src = realpath($path)) !== false) {
                $s3_filepath = Yii::app()->params['AWS_S3_PREFIX'] . $this->getBasePath() . '/' . $this->hash(basename($src));
                $s3_filename = '';

                if (is_file($src)) {
                    $s3_filename = '/' . basename($src);
                }

                return $this->getBaseUrl($s3_filepath) . $s3_filename;
            }

            return false;   // need to add in for s3 case if needed.
        } else {
            return parent::getPublishedUrl($path, $hashByName);
        }
    }

}
