<?php

class SearchBoxCom extends MainCom
{

    public $search_string, $ordering = array(
        3 => 'SORT_BY_RELEVANT',
        1 => 'SORTING_BY_ALPHABETIC_AZ',
        2 => 'SORTING_BY_ALPHABETIC_ZA',
    ),

        $alpha_string, $alpha_category_array;

    public function getSearchResultByKeywords($search_array = array())
    {
        $record_count = 0;
        $max = 10;
        $return_array = array();
        $search_string = isset($search_array['search_string']) ? $search_array['search_string'] : $this->search_string;

        if (notNull($search_string)) {
            if ($result_array = Yii::app()->frontPageCom->getPageContentObj()->getTplIDsBySearchKeyword($search_string)) {
                $category_array = Yii::app()->localizationCom->getZoneCategoriesID();
                $customer_zone_category_ids_array = $this->filterCategoryIDByGroupID($category_array);

                foreach ($result_array as $info_array) {
                    switch ($info_array['id_type']) {
                        case '0':
                            if ($name = trim(Yii::app()->frontPageCom->getCategoryObj()->getName($info_array['id'],
                                1))) {
                                if (!isset($return_array[$name . ':C:']) && !isset($return_array[$name . ':G:']) && in_array($info_array['id'],
                                        $customer_zone_category_ids_array)) {
                                    $record_count += 1;

                                    $return_array[$name . ':C:'] = array(
                                        'id' => 'C' . $info_array['id'],
                                        'dc' => Yii::app()->frontPageCom->getCategoryObj()->getName($info_array['id']),
                                        'url' => Yii::app()->createUrl('category/index',
                                            array('cid' => $info_array['id'])),
                                        't' => Yii::t('page_content', 'SEARCH_CATEGORY_GROUP_LABEL'),
                                        'tokens' => explode(',', $info_array['search_value'])
                                    );
                                }
                            }
                            break;
                        case '2':
                            if ($name = trim(Yii::app()->frontPageCom->getPageContentObj()->getGameBlogDescription($info_array['id'],
                                1))) {
                                if (!isset($return_array[$name . ':G:'])) {
                                    if (isset($return_array[$name . ':C:'])) {
                                        unset($return_array[$name . ':C:']);
                                    } else {
                                        $record_count += 1;
                                    }

                                    $return_array[$name . ':G:'] = array(
                                        'id' => 'G' . $info_array['id'],
                                        'dc' => Yii::app()->frontPageCom->getPageContentObj()->getGameBlogDescription($info_array['id']),
                                        'url' => Yii::app()->createUrl('game/index',
                                            array('gbid' => $info_array['id'])),
                                        't' => Yii::t('page_content', 'SEARCH_GAME_GROUP_LABEL'),
                                        'tokens' => explode(',', $info_array['search_value'])
                                    );
                                }
                            }
                            break;
                    }

                    if ($record_count >= $max) {
                        break;
                    }
                }
            }
        }

        return $return_array;
    }

    public function filterCategoryIDByGroupID($game_ids_array, $customers_groups_id = null)
    {
        $return_array = array();
        $customers_groups_id = $customers_groups_id !== null ? $customers_groups_id : Yii::app()->frontPageCom->customer_group_id;

        if ($game_ids_array) {
            if (countdim($game_ids_array) > 1) {
                foreach ($game_ids_array as $idx => $game_ids) {
                    $return_array[$idx] = $this->filterCategoryIDByGroupID($game_ids, $customers_groups_id);
                }
            } else {
                $group_game_ids_array = Yii::app()->frontPageCom->getCategoryObj()->getCustomerGroupCategoryIDs($customers_groups_id);
                $return_array = array_intersect($game_ids_array, $group_game_ids_array);
            }
        }

        return $return_array;
    }

    public function getPaginationProductListByTagID($tag_id, $filtering_array = array())
    {
        $game_id_array = array();
        $search_string = isset($filtering_array['search_string']) ? $filtering_array['search_string'] : $this->search_string;

        try {
            if ($tag_game_id_array = Yii::app()->cTagCom->getGameIDFilteredByTagIDAndChild($tag_id)) {
                if (notNull($search_string)) {
                    $tag_game_id_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryIDsBySearchKeywordSelective($search_string,
                        $tag_game_id_array);
                }

                $game_id_array = $this->getAllActiveProductCategoryIDByGameID($this->filter4Display($tag_game_id_array),
                    0);
            } else {
                // tag id cannot be found
                throw new Exception('ERROR_TAG_ID_NOT_FOUND');
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        //get product category group by alphabet
        $alpha_category_array = $this->getAlphaCategoryArray($game_id_array, $filtering_array);
        $this->getProductCategoryFilteredByAlphabet($game_id_array, $filtering_array, $alpha_category_array);
        list($product_array, $total_available_items) = $this->getPaginationProductsDataset($game_id_array,
            $filtering_array);

        return array(
            $product_array,
            $total_available_items,
            $alpha_category_array
        );
    }

    private function getAllActiveProductCategoryIDByGameID($game_info_array, $display_status = 1)
    {
        $return_array = array();

        foreach ($game_info_array as $game_id) {
            if ($product_array = Yii::app()->frontPageCom->getProductObj()->getAllProductByGameID($game_id,
                $display_status)) {
                $return_array[$game_id] = $product_array;
            }
        }

        return $return_array;
    }

    public function filter4Display($game_ids_array, $customers_groups_id = null)
    {
        $return_array = array();

        if ($game_ids_array) {
            if ($allow_group_id_array = $this->filterCategoryIDByGroupID($game_ids_array, $customers_groups_id)) {
                if (!$return_array = $this->filterCategoryIDByZone($allow_group_id_array)) {
                    // not allow for your region
                    throw new Exception('ERROR_REGION_RESTRICTION');
                }
            } else {
                // not allow for your customer group
                throw new Exception('ERROR_CUSTOMER_GROUP_RESTRICTION');
            }
        }

        return $return_array;
    }

    public function filterHardBlocking($game_ids_array)
    {
        $return_array = array();

        if ($game_ids_array) {
            if ($allow_group_id_array = $this->filterCategoryIDByGroupID($game_ids_array)) {
                if (!$return_array = $this->filterHardBlockingCategoryID($allow_group_id_array)) {
                    // not allow for your region
                    throw new Exception('ERROR_REGION_RESTRICTION');
                }
            } else {
                // not allow for your customer group
                throw new Exception('ERROR_CUSTOMER_GROUP_RESTRICTION');
            }
        }

        return $return_array;
    }

    public function filterSoftBlocking($game_ids_array)
    {
        if ($game_ids_array) {
            if (!$return_array = $this->filterSoftBlockingCategoryID($game_ids_array)) {
                // not allow for your region
                return true;
            }
        }

        return false;
    }

    public function filterHardBlockingCategoryID($game_ids_array)
    {
        $return_array = array();

        if ($game_ids_array) {
            if (countdim($game_ids_array) > 1) {
                foreach ($game_ids_array as $idx => $game_ids) {
                    $return_array[$idx] = $this->filterHardBlockingCategoryID($game_ids);
                }
            } else {
                $return_array = $game_ids_array;

                $yii_params = Yii::app()->params;
                $whitelist = $yii_params['IP_RESTRICTION_CATEGORY']['WHITELIST'];
                $blacklist = $yii_params['IP_RESTRICTION_CATEGORY']['BLACKLIST'];

                $ip_country = Yii::app()->frontPageCom->customer_ip_country;
                
                $filtered_category = array();
                $blocked_category = array();

                foreach ($whitelist as $key => $value) {
                    if ($key !== $ip_country) {
                        $filtered_category = array_merge($blocked_category, $value);
                    }

                    $return_array = array_diff($return_array, $filtered_category);
                }

                if (isset($blacklist[$ip_country])) {
                    $return_array = array_diff($return_array, $blacklist[$ip_country]);
                }
            }
        }

        return $return_array;
    }

    public function filterSoftBlockingCategoryID($game_ids_array, $zone_category_array = array())
    {
        $return_array = array();
        $zone_game_ids_array = $zone_category_array === array() ? Yii::app()->localizationCom->getZoneCategoriesID() : $zone_category_array;

        if ($game_ids_array) {
            if (countdim($game_ids_array) > 1) {
                foreach ($game_ids_array as $idx => $game_ids) {
                    $return_array[$idx] = $this->filterSoftBlockingCategoryID($game_ids, $zone_game_ids_array);
                }
            } else {
                $return_array = array_intersect($zone_game_ids_array, $game_ids_array);
            }
        }

        return $return_array;
    }

    public function filterCategoryIDByZone($game_ids_array, $zone_category_array = array(), $ip_country = null)
    {
        $return_array = array();
        $zone_game_ids_array = $zone_category_array === array() ? Yii::app()->localizationCom->getZoneCategoriesID() : $zone_category_array;

        if ($game_ids_array) {
            if (countdim($game_ids_array) > 1) {
                foreach ($game_ids_array as $idx => $game_ids) {
                    $return_array[$idx] = $this->filterCategoryIDByZone($game_ids, $zone_game_ids_array);
                }
            } else {
                $return_array = array_intersect($zone_game_ids_array, $game_ids_array);

                $yii_params = Yii::app()->params;
                $whitelist = $yii_params['IP_RESTRICTION_CATEGORY']['WHITELIST'];
                $blacklist = $yii_params['IP_RESTRICTION_CATEGORY']['BLACKLIST'];

                if ($ip_country === null) {
                    $ip_country = Yii::app()->frontPageCom->customer_ip_country;
                }
                $filtered_category = array();
                $blocked_category = array();

                foreach ($whitelist as $key => $value) {
                    if ($key !== $ip_country) {
                        $filtered_category = array_merge($blocked_category, $value);
                    }

                    $return_array = array_diff($return_array, $filtered_category);
                }

                if (isset($blacklist[$ip_country])) {
                    $return_array = array_diff($return_array, $blacklist[$ip_country]);
                }
            }
        }

        return $return_array;
    }

    private function getAlphaCategoryArray($prod_category_id_array)
    {
        $alpha_category_array = array();

        foreach ($prod_category_id_array as $game_id => $category_info_array) {
            $letter = strtolower(substr(Yii::app()->frontPageCom->getCategoryObj()->getPinYin($game_id), 0, 1));
            if (ctype_alpha($letter)) {
                $alpha_category_array[$letter][$game_id] = $category_info_array;
            } else {
                $alpha_category_array['0-9'][$game_id] = $category_info_array;
            }
        }

        return $alpha_category_array;
    }

    /*
     * $prod_category_id_array consist of array[game_id][] = category id
     */

    private function getProductCategoryFilteredByAlphabet(
        & $prod_category_id_array,
        $filtering_array,
        $alpha_category_array
    ) {
        if ($prod_category_id_array) {
            //filter by alphabet
            if (isset($filtering_array['alpha_string'])) {
                $alpha_string = strtolower($filtering_array['alpha_string']);
                $prod_category_id_array = (isset($alpha_category_array[$alpha_string]) ? $alpha_category_array[$alpha_string] : array());
            }
        }
    }

    private function getPaginationProductsDataset($game_products_array, $filtering_array = array())
    {   #, $product_filtering_array = array()
        $return_array = array();
        $total_items = 0;

        if ($game_products_array) {
            if ($filtering_array) {
                $item_per_page = isset($filtering_array['item_per_page']) ? $filtering_array['item_per_page'] : 0;
                $offset = isset($filtering_array['offset']) ? $filtering_array['offset'] : 0;
                $offset_index = $offset == 0 ? 0 : (int)($offset / $item_per_page);
                $sorted_product_array = $this->getOrderingByKey($filtering_array, $game_products_array);
                $total_items = count($sorted_product_array);

                if ($products_chunk_array = array_chunk($sorted_product_array, $item_per_page)) {
                    $return_array = isset($products_chunk_array[$offset_index]) ? $products_chunk_array[$offset_index] : array();
                }
                unset($sorted_product_array);
            } else {
                $return_array = $this->getOrderingByKey(array('ordering' => 999), $game_products_array);
                $total_items = count($return_array);
            }
        }
        return array(
            Yii::app()->frontPageCom->getProductObj()->getPaginationProductsInfo($return_array,
                Yii::app()->frontPageCom->customer_group_id),
            $total_items,
            array()
        );
    }

    private function getOrderingByKey($key_array, $game_products_array)
    {
        $return_array = array();

        if ($game_products_array) {
            $idx = 0;
            $category_ordering_array = array();
            $ordering = isset($key_array['ordering']) ? $key_array['ordering'] : '';

            switch ($ordering) {
                case 3:
                case 999:   // no ordering
                    $category_ordering_array = $game_products_array;

                    break;
                case 1: // A-Z
                    foreach ($game_products_array as $game_id => $products_array) {
                        $category_ordering_array[strtolower(Yii::app()->frontPageCom->getCategoryObj()->getPinYin($game_id)) . $idx++] = $products_array;
                    }

                    ksort($category_ordering_array, SORT_STRING);

                    break;
                case 2: // Z-A
                    foreach ($game_products_array as $game_id => $products_array) {
                        $category_ordering_array[strtolower(Yii::app()->frontPageCom->getCategoryObj()->getPinYin($game_id)) . $idx++] = $products_array;
                    }

                    krsort($category_ordering_array, SORT_STRING);

                    break;
                case 0: // Best Selling
                default:
                    $best_selling_category_ids = Yii::app()->frontPageCom->getBestSellingGameIDs();
                    $category_ordering = array_intersect_key($best_selling_category_ids, $game_products_array);
                    $category_ordering_array = array_replace_recursive($category_ordering, $game_products_array);

                    break;
            }

            $return_array = call_user_func_array('array_merge', $category_ordering_array);

            # before improve 2015-09-04
            //            foreach ($category_ordering_array as $products_array) {
            //                foreach ($products_array as $product_info) {
            //                    $return_array[] = $product_info;
            //                }
            //            }

            unset($game_products_array);
        }

        return $return_array;
    }

    public function getPaginationProductListFromSearch($filtering_array = array())
    {
        $return_array = array();
        $search_string = isset($filtering_array['search_string']) ? $filtering_array['search_string'] : $this->search_string;
        if (notNull($search_string)) {
            
                $game_ids_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryIDsBySearchKeyword($search_string);
                $filtered_category_array = $this->filterCategoryIDByZone(array_keys($game_ids_array));
            

            $return_array = $this->getAllActiveProductCategoryIDByGameID($this->filterCategoryIDByGroupID($filtered_category_array),
                0);
        }

        return $this->getPaginationProductsDataset($return_array, $filtering_array);
    }

    public function getProductListByGameIDByGameBlogID($game_id, $product_filtering_array = array())
    {
        try {
            # before improve 2015-09-04
            //            $prod_category_id_array = $this->getAllActiveProductCategoryIDByGameID($this->filter4Display($game_id_array));
            //            list($return_array,) = $this->getPaginationProductsDataset($prod_category_id_array, array(), $product_filtering_array);

            $prod_category_id_array = $this->getAllActiveProductCategoryIDByGameIDByGameBlogID($this->filter4Display(array($game_id)),
                $product_filtering_array);
            list($return_array,) = $this->getPaginationProductsDataset($prod_category_id_array);

            return $return_array;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return array();
    }

    private function getAllActiveProductCategoryIDByGameIDByGameBlogID($game_info_array, $product_filtering_array)
    {
        $return_array = array();

        foreach ($game_info_array as $game_id) {
            if ($product_array = Yii::app()->frontPageCom->getProductObj()->getAllProductByGameIDByGameBlogID($game_id,
                $product_filtering_array)) {
                $return_array[$game_id] = $product_array;
            }
        }

        return $return_array;
    }

    public function getProductListByGameID($game_id_array)
    {
        try {
            $return_array = $this->getProductListByGameID2($this->filterHardBlocking($game_id_array));

            return $return_array;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return array();
    }

    public function getSupportedProduct($game_id_array)
    {
        $prod_category_id_array = $this->getAllActiveProductCategoryIDByGameID($game_id_array, 1);
        list($return_array,) = $this->getPaginationProductsDataset($prod_category_id_array);

        return $return_array;
    }

    public function getProductListByGameID2($game_id_array)
    {
        $prod_category_id_array = $this->getAllActiveProductCategoryIDByGameID($game_id_array, 1);
        list($return_array,) = $this->getPaginationProductsDataset($prod_category_id_array);

        return $return_array;
    }

    public function getProductListByProductIDs($product_info_array, $customers_groups_id = null)
    {
        $return_array = array();
        $customers_groups_id = $customers_groups_id !== null ? $customers_groups_id : Yii::app()->frontPageCom->customer_group_id;

        foreach ($product_info_array as $product_id) {
            $return_array = Yii::app()->frontPageCom->getProductObj()->getProductsListByID($product_id,
                $customers_groups_id, $return_array);
        }
        return $return_array;
    }

    private function getProductsIndices($product_indice)
    {
        return $product_indice['en'];
    }

    public function getFacetTitle()
    {
        $language_id = Yii::app()->session['language_id'];
        $facet_title = 'sort_';
        switch ($language_id) {
            case 1:
                $facet_title .= 'en';
                break;

            case 2:
                $facet_title .= 'sc';
                break;

            case 4:
                $facet_title .= 'id';
                break;
        }
        return $facet_title;
    }

    // Function to get alphabet, not in use, direct use result when query
    public function getFilterAlpha($path, $params, $filter = '')
    {
        $country_code = Yii::app()->frontPageCom->getRegional('country_code');
        $ip_country_code = Yii::app()->frontPageCom->customer_ip_country;

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/filter/' . ($filter) . '/language/' . Yii::app()->session['language_id'] . '/country/' . $country_code . '/ip_country/' . $ip_country_code;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $return_str = $cache_result;
        } else {
            $filter = "(allowed_country:$country_code OR allowed_country:*) AND NOT blocked_country:$country_code AND NOT blocked_country:* AND (ip_allowed_country:$ip_country_code OR ip_allowed_country:*) AND NOT ip_blocked_country:$ip_country_code " . (empty($filter) ? '' : 'AND ' . $filter);
            $array = [];
            $array['all'] = [
                'item' => 'all',
                'active' => true,
                'href' => Yii::app()->createUrl($path, $params)
            ];

            foreach (range('a', 'z') as $alpha) {
                $array[$alpha] = [
                    'item' => $alpha,
                    'active' => false
                ];
            }

            $array['#'] = [
                'item' => '#',
                'active' => false
            ];

            $algolia_params = Yii::app()->params['ALGOLIA'];

            $product_indice = $this->getProductsIndices($algolia_params['INDICES']['PRODUCTS']);

            $res = AlgoliaCom::getFacetValues($product_indice, $this->getFacetTitle(), $filter);

            foreach ($res['facetHits'] as $facet) {
                $alpha = $facet['value'];
                if (!empty($array[$alpha])) {
                    $array[$alpha]['active'] = true;
                    $array[$alpha]['href'] = Yii::app()->createUrl($path, array_merge(['fa' => $alpha], $params));
                }
            }

            $body = '';
            $count = 1;

            foreach ($array as $item) {
                $class = '';
                if ($count == 1) {
                    $body .= '<tr class="filter-drop__table-row">';
                }
                if ($item['active'] == false) {
                    $item['href'] = 'javascript:void(0);';
                    $class = 'filter-drop__table-link--decor';
                }
                $body .= '<td class="filter-drop__table-data"><a href="' . $item['href'] . '" rel="nofollow" class="filter-drop__table-link ' . $class . '">' . $item['item'] . '</a></td>';
                if ($count == 7) {
                    $body .= '<tr>';
                    $count = 1;
                } else {
                    $count++;
                }
            }

            $return_str = <<<HTML
<div class="filter-drop__content js-drop-content">
		<table class="filter-drop__table">
			<tbody>
                $body
		    </tbody>
		</table>
	</div>
HTML;
            Yii::app()->cache->set($cache_key, $return_str, 86400);
        }


        return $return_str;
    }

    public function getFilterAlphabet($path, $params, $facets)
    {
        $array = [];
        $array['all'] = [
            'item' => 'all',
            'active' => true,
            'href' => Yii::app()->createUrl($path, $params)
        ];

        foreach (range('a', 'z') as $alpha) {
            $array[$alpha] = [
                'item' => $alpha,
                'active' => false
            ];
        }

        $array['#'] = [
            'item' => '#',
            'active' => false
        ];

        foreach ($facets as $key => $val) {
            if (!empty($array[$key])) {
                $array[$key]['active'] = true;
                $array[$key]['href'] = Yii::app()->createUrl($path, array_merge(['fa' => $key], $params));
            }
        }

        $body = '';
        $count = 1;

        foreach ($array as $item) {
            $class = '';
            if ($count == 1) {
                $body .= '<tr class="filter-drop__table-row">';
            }
            if ($item['active'] == false) {
                $item['href'] = 'javascript:void(0);';
                $class = 'filter-drop__table-link--decor';
            }
            $body .= '<td class="filter-drop__table-data"><a href="' . $item['href'] . '" rel="nofollow" class="filter-drop__table-link ' . $class . '">' . $item['item'] . '</a></td>';
            if ($count == 7) {
                $body .= '<tr>';
                $count = 1;
            } else {
                $count++;
            }
        }

        $return_str = <<<HTML
<div class="filter-drop__content js-drop-content">
		<table class="filter-drop__table">
			<tbody>
                $body
		    </tbody>
		</table>
	</div>
HTML;

        return $return_str;
    }

    public function getPaginationProductListFromSearch3($filtering_array = array(), $page = 1, $hitsPerPage = 20)
    {
        $return_array = array();
        $algolia_params = Yii::app()->params['ALGOLIA'];
        $product_indice = $this->getProductsIndices($algolia_params['INDICES']['PRODUCTS']);
        $filter_string = (!empty($filtering_array['filter']) ? html_entity_decode($filtering_array['filter']) : '');
        $filter_alphabet = (!empty($filtering_array['fa']) ? $filtering_array['fa'] : '');
        $facet_title = $this->getFacetTitle();

        $filter = [
            'attributesToRetrieve' => ['objectID'],
            "filters" => 'type:game_product' . (empty($filter_string) ? '' : ' AND ' . $filter_string) . (empty($filter_alphabet) ? '' : ' AND ' . $this->getFacetTitle() . ":" . $filter_alphabet),
            'page' => $page,
            'hitsPerPage' => $hitsPerPage,
            'facets' => [
                $this->getFacetTitle()
            ]

        ];

        if(!empty($filter_string)){
            $filter['analyticsTags'] = [
                'filter_game_key'
            ];
        }
        else{
            $filter['analytics'] = 'false';
        }

        $algolia_results = AlgoliaCom::sendRequest($product_indice, '', $filter);

        $results_array = $algolia_results['hits'];

        foreach ($results_array as $product) {
            $return_array[] = str_replace('p-', '', $product['objectID']);
        }

        return array(
            $this->getProductListByProductIDs($return_array, Yii::app()->frontPageCom->customer_group_id),
            $algolia_results['nbHits'],
            (!empty($algolia_results['facets'][$facet_title]) ? $algolia_results['facets'][$facet_title] : []),
        );
    }


    public function getPaginationProductListFromSearch2($filtering_array = array(), $page = 1, $hitsPerPage = 20)
    {
        $return_array = array();
        $search_string = isset($filtering_array['search_string']) ? $filtering_array['search_string'] : $this->search_string;
        $country_code = Yii::app()->frontPageCom->getRegional('country_code');
        $ip_country_code = Yii::app()->frontPageCom->customer_ip_country;
        $filter_alphabet = (!empty($filtering_array['fa']) ? $filtering_array['fa'] : '');
        $filter_type = (!empty($filtering_array['type']) ? $filtering_array['type'] : '');

        $filter = "(allowed_country:$country_code OR allowed_country:*) AND NOT blocked_country:$country_code AND NOT blocked_country:* AND (ip_allowed_country:$ip_country_code OR ip_allowed_country:*) AND NOT ip_blocked_country:$ip_country_code ";
        $algolia_params = Yii::app()->params['ALGOLIA'];
        $product_indice = $this->getProductsIndices($algolia_params['INDICES']['PRODUCTS']);
        $category_indice = $algolia_params['INDICES']['CATEGORIES'];
        $facet_title = $this->getFacetTitle();

        $queries = [
            [
                'indexName' => $product_indice,
                'query' => $search_string,
                "filters" => $filter . (empty($filter_alphabet) ? '' : ' AND ' . $facet_title . ":" . $filter_alphabet) . (empty($filter_type) ? '' : ' AND ' . $filter_type),
                'page' => $page,
                'hitsPerPage' => $hitsPerPage,
                'facets' => [
                    $this->getFacetTitle()
                ]
            ],
            [
                'indexName' => $category_indice,
                'query' => $search_string,
                "filters" => $filter . ' AND (type:games OR type:category) ',
                'hitsPerPage' => 5,
                'removeWordsIfNoResults' => 'allOptional'
            ]
        ];

        $algolia_results = AlgoliaCom::sendMultipleQueries($queries, 'stopIfEnoughMatches')['results'];
        $results_array = $algolia_results[0]['hits'];
        foreach ($results_array as $product) {
            $return_array[] = str_replace('p-', '', $product['objectID']);
        }
        $suggested_item = [];
        if ($algolia_results[0]['nbHits'] == 0) {
            foreach ($algolia_results[1]['hits'] as $item) {
                $language_id = strtolower(Yii::app()->session['language']);
                switch ($language_id) {
                    case 'id':
                        $lang_tag = 'name_id';
                        break;
                    case 'zh-cn':
                        $lang_tag = 'name_sc';
                        break;
                    default:
                        $lang_tag = 'name';
                        break;
                }
                $title = (isset($item[$lang_tag]) && $item[$lang_tag] !== '' ? $item[$lang_tag] : $item['name']);
                $suggested_item[] = [
                    "title" => $title,
                    "link" => $item['url']
                ];
            }
        }
        return array(
            $this->getProductListByProductIDs($return_array, Yii::app()->frontPageCom->customer_group_id),
            (isset($algolia_results) ? $algolia_results[0]['nbHits'] : 0),
            $suggested_item,
            (isset($algolia_results[0]['facets'][$facet_title]) ? $algolia_results[0]['facets'][$facet_title] : []),
        );
    }
}