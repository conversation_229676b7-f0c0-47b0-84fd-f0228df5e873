<?php

class SeoCom extends CBaseUrlRule
{

    const PRODUCT_PAGE_SUFFIX = '.html';

    public $connectionID = 'db';
    private $alias_filter = array(
        'from' => array(
            '/'
        ),
        'to' => array(
            '-'
        )
    );

    public function createUrl($manager, $route, $params, $ampersand)
    {
        $seoUrl = false;
        $prepend_country_code = false;
        $urlParam = $params;

        switch ($route) {
            case 'brand/index':
                $seoUrl = implode('/', $params['path']);
                unset($urlParam['path']);
                if (!isset($urlParam['x-country'])) {
                    $prepend_country_code = true;
                } else {
                    unset($urlParam['x-country']);
                }
                break;
            case 'site/index':
            case '':
            case 'direct-topup':
            case 'voucher':
            case 'physical-goods':
            case 'search/index':
                $prepend_country_code = true;
                break;
            case 'game/index':
                if (isset($params['gbid']) && ($gbid = $params['gbid'])) {
                    if ($game_name = Yii::app()->frontPageCom->getPageContentObj()->getGameBlogCustomUrl($gbid)) {
                        $seoUrl = 'game' . '/' . $game_name;
                        unset($urlParam['gbid']);
                    }
                }
                break;
            case 'product/index':
                if (isset($params['pid']) && ($pid = $params['pid'])) {
                    if ($product_url_alias = Yii::app()->frontPageCom->getProductObj()->getProductsUrlAlias($pid)) {
                        $seoUrl = 'buynow' . '/' . $product_url_alias;

                        if (isset($params['dm'])) {
                            // this can be removed if dtu separated from softpin
                            if ($params['dm'] == 6) {
                                $seoUrl .= '-dtu';
                            }

                            unset($urlParam['dm']);
                        }

                        if (isset($params['pm'])) {
                            unset($urlParam['pm']);
                        }

                        $seoUrl .= self::PRODUCT_PAGE_SUFFIX;

                        unset($urlParam['pid']);
                    }
                }
                break;
            case 'reviews/index':
                if (isset($params['pid']) && ($pid = $params['pid'])) {
                    if ($product_url_alias = Yii::app()->frontPageCom->getProductObj()->getProductsUrlAlias($pid)) {
                        $seoUrl = 'reviews' . '/' . $product_url_alias;

                        if (isset($params['dm'])) {
                            // this can be removed if dtu separated from softpin
                            if ($params['dm'] == 6) {
                                $seoUrl .= '-dtu';
                            }

                            unset($urlParam['dm']);
                        }

                        if (isset($params['pm'])) {
                            unset($urlParam['pm']);
                        }

                        $seoUrl .= self::PRODUCT_PAGE_SUFFIX;

                        unset($urlParam['pid']);
                        unset($urlParam['cid']);
                    }
                }
                if (isset($params['cid']) && ($cid = $params['cid'])) {
                    if ($categories_url_alias = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesUrlAlias(
                        $cid
                    )) {
                        if ($tag_key = Yii::app()->cTagCom->getSinglePathTagKeyByGameID($cid)) {
                            $seoUrl = 'reviews/categories/' . str_ireplace(
                                    '_',
                                    '-',
                                    strtolower(implode('/', $tag_key))
                                ) . '/' . $categories_url_alias;
                        } else {
                            $seoUrl = $categories_url_alias;
                        }

                        unset($urlParam['pid']);
                        unset($urlParam['cid']);
                    }
                }
                break;
            case 'category/index':
                if (isset($params['cid']) && ($cid = $params['cid'])) {
                    if ($categories_url_alias = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesUrlAlias(
                        $cid
                    )) {
                        if ($tag_key = Yii::app()->cTagCom->getSinglePathTagKeyByGameID($cid)) {
                            $seoUrl = str_ireplace(
                                    '_',
                                    '-',
                                    strtolower(implode('/', $tag_key))
                                ) . '/' . $categories_url_alias;
                        } else {
                            $seoUrl = $categories_url_alias;
                        }

                        unset($urlParam['cid']);
                    }
                }
                break;
            case 'site/page':
                if (isset($params['view'])) {
                    $view = explode('.', $params['view']);

                    if (count($view) == 2 && isset($view[1])) {
                        $seoUrl = $view[1];
                        unset($urlParam['view']);
                    } else {
                        if (count($view) == 1) {
                            $seoUrl = $params['view'];
                            unset($urlParam['view']);
                        }
                    }
                }
                break;
            case 'site/termsOfService':
                $seoUrl = 'terms-of-service';
                break;
            case 'site/refundPolicy':
                $seoUrl = 'refund-policy';
                break;
            case 'site/advertisingPolicy':
                $seoUrl = 'advertising-policy';
                break;
            case 'game-key/index':
                $seoUrl = 'game-key/index';
                break;
            case 'checkout':
                $seoUrl = $route;
                break;
            case 'account/index':
                $seoUrl = 'account/overview';
                break;
            case 'account/viewOrders':
                $seoUrl = 'account/purchase';
                break;
            case 'account/view-order-review':
            case 'account/viewReviews':
                $seoUrl = 'account/view-order-review';
                break;
            case 'account/orderDetail':
                $seoUrl = 'account/purchase/order/' . $urlParam['orders_id'];
                unset($urlParam['orders_id']);
                break;
            case 'account/orderCancelForm':
                $seoUrl = 'account/cancel-purchase/order/' . $urlParam['orders_id'];
                unset($urlParam['orders_id']);
                break;
            case 'account/orderComplaintForm':
                $seoUrl = 'account/purchase-issue/order/' . $urlParam['orders_id'];
                unset($urlParam['orders_id']);
                break;
            case 'account/getCdkeyImage':
                $seoUrl = 'account/view-code';
                break;
            case 'account/tax':
                $seoUrl = 'account/tax';
                break;
            case 'account/taxCancel':
                $seoUrl = 'account/tax-cancel';
                break;
            case 'account/getCdkeyImageMultiple':
                $seoUrl = 'account/view-code/all';
                break;
            case 'account/downloadAllCdKey':
                $seoUrl = 'account/view-code/download';
                break;
            case 'account/showImage':
                $seoUrl = 'account/view-code/image/' . $urlParam['keyident'];
                unset($urlParam['keyident']);
                break;
            case 'account/captcha':
                $seoUrl = 'account/captcha';
                break;
            case 'storeCredit/index':
                $seoUrl = 'account/store-credit/index';
                break;
            case 'storeCredit/statement':
                $seoUrl = 'account/store-credit/statement';
                break;
            case 'storeCredit/redeemGiftCard':
                $seoUrl = 'account/store-credit/redeem-gift-card';
                break;
            case 'storeCredit/verifyGiftCard':
                $seoUrl = 'account/store-credit/verify-gift-card';
                break;
            case 'storeCredit/redeemGiftCardSuccess':
                $seoUrl = 'account/store-credit/redeem-gift-card-success';
                break;
        }

        if ($prepend_country_code) {
            if (isset($urlParam['x-default'])) {
                unset($urlParam['x-default']);
            } else {
                $country_code = strtolower(($urlParam['country_code'] ?? Yii::app()->session['country_code']));
                $language_code = strtolower(($urlParam['language_code'] ?? Yii::app()->language));
                if ($seoUrl !== false) {
                    $seoUrl = $country_code . '/' . $language_code . '/' . $seoUrl;
                } else {
                    if (!empty($route)) {
                        $seoUrl = $country_code . '/' . $language_code . '/' . $route;
                    } else {
                        $seoUrl = $country_code . '/' . $language_code;
                    }
                }
            }
            unset($urlParam['language_code']);
            unset($urlParam['country_code']);
        } elseif (!isset($urlParam['language'])) {
            $language_code = strtolower(($urlParam['language_code'] ?? Yii::app()->language));
            unset($urlParam['language_code']);
            if ($seoUrl !== false) {
                $seoUrl = $language_code . '/' . $seoUrl;
            } else {
                if (!empty($route)) {
                    $seoUrl = $language_code . '/' . $route;
                } else {
                    $seoUrl = $language_code;
                }
            }
        }


        // Retaining other $_GET param
        if ($seoUrl !== false && count($urlParam)) {
            foreach ($urlParam as $index => $param) {
                $urlParam[$index] = htmlspecialchars($param);
            }
            $seoUrl .= '?' . $manager->createPathInfo($urlParam, '=', $ampersand);
        }

        return $seoUrl;
    }

    public function parseUrl($manager, $request, $pathInfo, $rawPathInfo)
    {
        $return_path = false;
        $portal_language = array_keys(RegionalSettingCom::getLanguageList());

        if (count($pathInfo_array = explode('/', $pathInfo)) > 0) {
            $country_list = RegionalSettingCom::curl('country');

            if (isset($pathInfo_array[1]) && strlen($pathInfo_array[1]) == 2 && isset($country_list[strtoupper($pathInfo_array[0])])) {
                if (!isset($_REQUEST['reg_ctry'])) {
                    $_REQUEST['reg_ctry'] = strtoupper($pathInfo_array[0]);
                }
                array_shift($pathInfo_array);
            }

            if (isset($_REQUEST['reg_ctry'])) {
                unset($_REQUEST['reg_ctry']);
            }
            
            if (in_array(strtolower($pathInfo_array[0]), $portal_language)) {
                if (!isset($_REQUEST['reg_lang'])) {
                    $_REQUEST['reg_lang'] = $pathInfo_array[0];
                }

                array_shift($pathInfo_array);
            }
            Yii::app()->currency->_init();
            $is_invalid_currency = !isset(Yii::app()->currency->currencies[Yii::app()->session['currency']]);

            if (isset($_REQUEST['reg_ctry']) || isset($_REQUEST['reg_lang']) || $is_invalid_currency) {
                RegionalSettingCom::checkRegionalSetting(true);
            }

            if (count($pathInfo_array) > 0) {
                $this->redirectUrl(implode('/', $pathInfo_array));
                switch ($pathInfo_array[0]) {
                    case 'buynow':  // Product details
                        $suffix_len = strlen(self::PRODUCT_PAGE_SUFFIX);
                        $ttl_itm = count($pathInfo_array) - 1;

                        for ($i = $ttl_itm; $i > 0; $i--) {
                            if (substr(
                                    $pathInfo_array[$i],
                                    -1 * $suffix_len,
                                    $suffix_len
                                ) === self::PRODUCT_PAGE_SUFFIX) {
                                $product_url_alias = substr(validateRequest($pathInfo_array[$i]), 0, -1 * $suffix_len);

                                if ($product_url_alias_array = explode('-', $product_url_alias)) {
                                    if ($ttl_itm2 = (count($product_url_alias_array) - 1)) {
                                        for ($i = $ttl_itm2; $i > 0; $i--) {
                                            if ($product_url_alias_array[$i] === 'dtu') {
                                                $_REQUEST['dm'] = $_GET['dm'] = 6;
                                                unset($product_url_alias_array[$i]);
                                            }
                                        }
                                    }

                                    $product_url_alias = implode('-', $product_url_alias_array);
                                    unset($product_url_alias_array);
                                }
                                if ($pid = Yii::app()->frontPageCom->getProductObj()->getProductIDByUrlAlias(
                                    $product_url_alias
                                )) {
                                    $return_path = 'product/index';
                                    $_REQUEST['pid'] = $_GET['pid'] = $pid;
                                }
                                break;
                            }
                        }
                        break;
                    case 'reviews':
                        switch ($pathInfo_array[1]) {
                            case 'categories':
                                $ttl_itm = count($pathInfo_array) - 1;

                                for ($i = $ttl_itm; $i > 1; $i--) {
                                    if ($cid = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesIDByUrlAlias(
                                        validateRequest($pathInfo_array[$i])
                                    )) {
                                        $return_path = 'productReview/index';
                                        $_REQUEST['cid'] = $_GET['cid'] = $cid;
                                        $_REQUEST['type'] = 'categories';
                                    }
                                }
                                break;
                            default:
                                $suffix_len = strlen(self::PRODUCT_PAGE_SUFFIX);
                                $ttl_itm = count($pathInfo_array) - 1;

                                for ($i = $ttl_itm; $i > 0; $i--) {
                                    if (substr(
                                            $pathInfo_array[$i],
                                            -1 * $suffix_len,
                                            $suffix_len
                                        ) === self::PRODUCT_PAGE_SUFFIX) {
                                        $product_url_alias = substr(
                                            validateRequest($pathInfo_array[$i]),
                                            0,
                                            -1 * $suffix_len
                                        );

                                        if ($product_url_alias_array = explode('-', $product_url_alias)) {
                                            if ($ttl_itm2 = (count($product_url_alias_array) - 1)) {
                                                for ($i = $ttl_itm2; $i > 0; $i--) {
                                                    if ($product_url_alias_array[$i] === 'dtu') {
                                                        $_REQUEST['dm'] = $_GET['dm'] = 6;
                                                        unset($product_url_alias_array[$i]);
                                                    }
                                                }
                                            }

                                            $product_url_alias = implode('-', $product_url_alias_array);
                                            unset($product_url_alias_array);
                                        }

                                        if ($pid = Yii::app()->frontPageCom->getProductObj()->getProductIDByUrlAlias(
                                            $product_url_alias
                                        )) {
                                            $return_path = 'productReview/index';
                                            $_REQUEST['pid'] = $_GET['pid'] = $pid;
                                            $_REQUEST['type'] = 'products';
                                        }
                                    }
                                }
                                break;
                        }
                        break;
                    // case 'search':
                    //     if (isset($pathInfo_array[1])) {
                    //         $keyword = strtolower(validateRequest($pathInfo_array[1]));

                    //         if ($keyword === 'all') {
                    //             $return_path = 'search/index';
                    //             $_REQUEST['keyword'] = $_GET['keyword'] = '';
                    //         }
                    //     }
                    //     break;
                    case 'game-key':
                        $return_path = 'gameKey/index';
                        break;
                    case 'terms-of-service':
                    case 'refund-policy':
                    case 'advertising-policy':
                    case 'privacy-policy':
                        $return_path = 'site/termsOfService';
                        $_REQUEST['view'] = $pathInfo_array[0];
                        break;
                    case 'why-us':
                        $return_path = 'site/page';
                        $viewFileName = 'why-us';
                        $_REQUEST['view'] = $_GET['view'] = (isset(Yii::app()->session['language']) ? Yii::app()->session['language'] . '.' . $viewFileName : 'en.' . $viewFileName);
                        break;
                    case 'survey':
                        $return_path = 'site/page';
                        $viewFileName = 'survey';
                        $_REQUEST['view'] = $_GET['view'] = (isset(Yii::app()->session['language']) ? Yii::app()->session['language'] . '.' . $viewFileName : 'en.' . $viewFileName);
                        break;
                    case '_api':
                        $return_path = 'api/index';
                        break;
                    case 'account':
                        if (isset($pathInfo_array[1])) {
                            switch ($pathInfo_array[1]) {
                                case 'overview':
                                    $return_path = 'account/index';
                                    break;
                                case 'captcha':
                                    $return_path = 'account/captcha';
                                    break;
                                case 'tax-cancel':
                                    $return_path = 'account/taxCancel';
                                    break;
                                case 'purchase':
                                    if (isset($pathInfo_array[2])) {
                                        switch ($pathInfo_array[2]) {
                                            case 'order':
                                                $return_path = 'account/orderDetail';
                                                if(isset($pathInfo_array[3])){
                                                    $_REQUEST['orders_id'] = (int)$pathInfo_array[3];
                                                }
                                                else{
                                                    throw new CHttpException(400);
                                                }
                                                if (isset($pathInfo_array[4]) && $pathInfo_array[4] == 'review') {
                                                    $_REQUEST['review'] = true;
                                                }
                                                break;
                                            default:
                                                $return_path = 'account/viewOrders';
                                                break;
                                        }
                                    } else {
                                        $return_path = 'account/viewOrders';
                                    }
                                    break;
                                case 'cancel-purchase':
                                case 'purchase-issue':
                                    $return_path = 'account/orderCancelForm';
                                    if (!Yii::app()->request->isAjaxRequest) {
                                        if(isset($pathInfo_array[3])){
                                            $_REQUEST['orders_id'] = (int)$pathInfo_array[3];
                                        }
                                        else{
                                            throw new CHttpException(400);
                                        }
                                    }
                                    break;
                                case 'view-code':
                                    if (isset($pathInfo_array[2])) {
                                        switch ($pathInfo_array[2]) {
                                            case 'all':
                                                $return_path = 'account/getCdkeyImageMultiple';
                                                break;
                                            case 'save-all':
                                                $return_path = 'account/getAllCdkey';
                                                break;
                                            case 'get-cdkeys-status':
                                                $return_path = 'account/getAllCdkeyStatus';
                                                break;
                                            case 'download':
                                                $return_path = 'account/downloadAllCdKey';
                                                break;
                                            case 'image':
                                                $return_path = 'account/showImage';
                                                $_REQUEST['keyident'] = (int)$pathInfo_array[3];
                                                break;
                                            default:
                                                $return_path = 'account/getCdkeyImage';
                                                break;
                                        }
                                    } else {
                                        $return_path = 'account/getCdkeyImage';
                                    }
                                    break;
                                case 'store-credit':
                                    if (isset($pathInfo_array[2])) {
                                        switch ($pathInfo_array[2]) {
                                            case 'statement':
                                                $return_path = 'storeCredit/statement';
                                                break;
                                            case 'redeem-gift-card':
                                                $return_path = 'storeCredit/redeemGiftCard';
                                                break;
                                            case 'verify-gift-card':
                                                $return_path = 'storeCredit/verifyGiftCard';
                                                break;
                                            case 'redeem-gift-card-success':
                                                $return_path = 'storeCredit/redeemGiftCardSuccess';
                                                break;
                                            default:
                                                $return_path = 'storeCredit/index';
                                                break;
                                        }
                                    } else {
                                        $return_path = 'account/viewOrders';
                                    }
                                    break;
                                case 'order-reviewed':
                                    $return_path = 'account/orderReviewed';
                                    break;
                                case 'view-order-review':
                                    $return_path = 'account/viewOrderReview';
                                    break;
                                case 'open_order_review_by_oid':
                                    $return_path = 'account/openOrderReviewByOid';
                                    break;
                                case 'save_order_review':
                                    $return_path = 'account/saveOrderReview';
                                    break;
                            }
                        }
                        break;
                    case 'promo':
                        $return_path = 'promo/index';
                        break;
                    case 'mobile-recharge':
                        if (isset($pathInfo_array[1])) {
                            switch ($pathInfo_array[1]) {
                                case 'validate-phone':
                                    $return_path = 'mobileRecharge/validatePhoneNum';
                                    break;
                                case 'hide-phone':
                                    $return_path = 'mobileRecharge/hidePhoneNum';
                                    break;
                                default:
                                    $return_path = 'mobileRecharge/index';
                                    break;
                            }
                        } else {
                            $return_path = 'mobileRecharge/index';
                        }
                        break;
                    case 'site':
                    // case 'trending':
                    // case 'poc':
                    case 'userBar':
                    case 'checkout':
                    case 'order':
                    case 'sso':
                    case 'sls':
                        break;
                    case 'voucher':
                    case 'direct-topup':
                    case 'physical-goods':
                        $_REQUEST['filter'] = $pathInfo_array[0];
                        $return_path = 'search/gallery';
                        break;
                    case 'brand':
                        if(!isset($pathInfo_array[1]))
                            throw new CHttpException(404);
                        switch ($pathInfo_array[1]) {
                            case 'buy':
                                $return_path = 'brand/buy';
                                break;
                            case 'get-character-list':
                                $return_path = 'brand/getCharacterList';
                                break;
                            case 'review':
                                $return_path = 'brand/review';
                                break;
                        }

                        break;
                    case '':
                        $return_path = 'vue/index';
                        // $return_path = 'site/index';
                        // $return_path = 'trending/index';
                        // $return_path = 'poc/search';
                        break;
                    default:        // Brand
                        // $brand_301 = BrandList301::BRAND_LIST_301;
                        // if(is_array($pathInfo_array) && isset($pathInfo_array[0])){
                        //     $seo_lower = strtolower($pathInfo_array[0]);
                        //     if(isset($brand_301[$seo_lower])){
                        //         if(count($pathInfo_array) > 1){
                        //                 unset($pathInfo_array[0]);
                        //         } else {
                        //             $pathInfo_array = array($brand_301[$seo_lower]);
                        //         }
                        //         Yii::app()->request->redirect("/". implode('/', $pathInfo_array));
                        //     }
                        // }

                        // $return_path = 'brand/index';
                        // $_REQUEST['brand_path'] = $pathInfo_array;
                        $return_path = 'vue/index';
                        break;
                }
            }
        }


        return $return_path;  // this rule does not apply
    }

    private function format_url($str, $strToLowerBool)
    {
        $returnStr = strtolower(urldecode(validateRequest($str)));

        if ($strToLowerBool === false) {
            $returnStr = urldecode(validateRequest($str));
        }
        return $returnStr;
    }

    private function redirectUrl(string $pathInfo): void
    {
        $url = stripLanguageFromUrl($pathInfo);

        (new SeoUrlRedirectCom())->redirectToNewUrl($url);
    }
}
