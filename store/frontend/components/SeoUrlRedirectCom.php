<?php

class SeoUrlRedirectCom extends MainCom
{
    private const CACHE_DURATION = 86400;

    public function redirectToNewUrl(string $url): void
    {
        $trimmedUrl = trim($url, '/');

        $encodedUrl = $this->encodeToUTF8($trimmedUrl);

        $cacheKey = $this->getCacheKey($encodedUrl);

        $urlDataString = $this->getCacheValue($cacheKey);

        if ($urlDataString === FALSE) {
            $data = SeoUrlRedirect::model()->getByOldUrl($encodedUrl);

            $urlDataString = $this->getSeoUrlRedirect($data);

            $this->setCacheValue($cacheKey, $urlDataString);
        }

        if (!is_array($urlDataString) && intval($urlDataString) == -1) {
            return;
        }

        $urlData = json_decode($urlDataString, true);

        if (isset($urlData['new_url']) && isset($urlData['redirect_type'])) {
            $newUrl = Yii::app()->createAbsoluteUrl($urlData['new_url']);

            $redirectType = intval($urlData['redirect_type']);

            $this->redirect($newUrl, $redirectType);
        }
    }

    private function getCacheKey(string $url): string
    {
        return Yii::app()->params['MEMCACHE_PREFIX']
            . 'seo_url_redirect'
            . '/' . $url;
    }

    private function getCacheValue(string $cacheKey)
    {
        return Yii::app()->cache->get($cacheKey);
    }

    private function setCacheValue(string $key, string $value): void
    {
        Yii::app()->cache->set($key, $value, self::CACHE_DURATION);
    }

    private function encodeToUTF8(string $url): string
    {
        return mb_convert_encoding($url, "UTF-8");
    }

    private function getSeoUrlRedirect($data): string
    {
        if ($data instanceof SeoUrlRedirect) {
            $urlDataString = json_encode($data->attributes);
        } else {
            // cache non-redirected url
            $urlDataString = -1;
        }

        return $urlDataString;
    }

    private function redirect($newUrl, $redirectType): void
    {
        // Prevent browser cache the redirection (disk cache)
        header("Cache-Control: no-store, no-cache, must-revalidate");
        header("Expires: Thu, 19 Nov 1981 08:52:00 GMT");

        Yii::app()->request->redirect($newUrl, true, $redirectType);
    }
}