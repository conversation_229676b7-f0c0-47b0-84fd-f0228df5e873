<?php

class WebpushCom extends MainCom 
{
	private static function sendToken($data) {
        $url = Yii::app()->params['WEBPUSH']['API_URL'] . "/webpush/tokens";
        $data_string = json_encode($data);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // don't check certificate
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // don't check certificate
        curl_setopt($ch, CURLOPT_VERBOSE, TRUE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        	'Content-Type: application/json',
        	'Content-Length: ' . strlen($data_string))
    	);

        $server_output = curl_exec($ch);

        if ($server_output === false) {
            self::reportError('WebpushCom:sendToken', array('error' => 'cURL resource: ' . (string) $ch . '; cURL error: ' . curl_error($ch) . ' (cURL error code ' . curl_errno($ch) . ')'));
		}
        
        curl_close($ch);

        if (!empty($server_output)) {
            $result = CJSON::decode($server_output);
        } else {
            $result = array();
        }
        
        return $result;
    }

    public static function setWebpushToken($token)
    {
    	$webpushTime = time();

    	$data = [
    		'token' => $token,
    		'user_id' => Yii::app()->user->id,
    		'merchant' => Yii::app()->params['WEBPUSH']['CLIENT_ID'],
    		'signature' => md5(Yii::app()->params['WEBPUSH']['CLIENT_ID'].$webpushTime.'|'.Yii::app()->params['WEBPUSH']['CLIENT_SECRET']),
    		'time' => $webpushTime
    	];

    	return self::sendToken($data);
    }
}