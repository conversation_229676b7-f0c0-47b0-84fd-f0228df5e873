<?php

class DBSession extends CDbHttpSession {

    /**
     * @param mixed $db
     * @param mixed $tableName
     * @return
     */
    protected function createSessionTable($db, $tableName) {
        $driver = $db->getDriverName();
        if ($driver === 'mysql')
            $blob = 'LONGBLOB';
        else if ($driver === 'pgsql')
            $blob = 'BYTEA';
        else
            $blob = 'BLOB';
        $db->createCommand()->createTable($tableName, array(
            'id' => 'CHAR(32) PRIMARY KEY',
            'user_id' => 'INT(11) unsigned NULL',
            'expire' => 'integer',
            'data' => $blob,
        ));
        $db->createCommand("ALTER TABLE $tableName ADD INDEX idx_user_id (user_id);")->execute();
        $db->createCommand("ALTER TABLE $tableName ADD INDEX idx_expiry (expire);")->execute();
    }

    public function setUID($uid) {
        $db = $this->getDbConnection();
        $db->setActive(true);
        $db->createCommand()->update($this->sessionTableName, array('user_id' => (int) $uid), 'id=:id', array(':id' => $this->sessionId));
    }

}