<?php

/**
 * Base class for controllers at frontend.
 *
 * Includes all assets required for frontend and also registers Google Analytics widget if there's code specified.
 *
 * @package YiiBoilerplate\Frontend
 */
class FrontendController extends CController
{

    /**
     * @var string the default layout for the controller view. Defaults to '//layouts/column1',
     * meaning using a single column layout. See 'protected/views/layouts/column1.php'.
     */
    public $theme = 2017;
    public $layout = '//layouts/default';

    /**
     * @var array the breadcrumbs of the current page. The value of this property will
     * be assigned to {@link CBreadcrumbs::links}. Please refer to {@link CBreadcrumbs::links}
     * for more details on how to specify this property.
     */
    public $breadcrumbs = array(), $background_image_url = '', $background_color = '', $MainBanner, $search_keywords = '', $maintenance_mode = false, $warn_maintenance = false, $meta_tag_info = array(), $enabled_service = array(), $wrapper_class = 'wrapper';

    public $maintenance_message_always_show = false;

    public function init($trigger_authcom = true)
    {
        if ($trigger_authcom == true) {
            AuthCom::registerAppSess();
        }

        // Fix Missing Customers Group Sometime
        if (Yii::app()->user->id && empty(Yii::app()->session['customers_groups_id'])) {
            AuthCom::flushProfilerSession();
        }

        Yii::app()->frontPageCom->_init();
        Yii::app()->customerCom->_init();
        Yii::app()->currency->_init();

        parent::init();
    }

    /**
     * What to do before rendering the view file.
     *
     * We include Google Analytics code if ID was specified and register the frontend assets.
     *
     * @param string $view
     * @return bool
     */
    public function beforeRender($view)
    {
        $this->registerAssets();
        $this->addGoogleAnalyticsCode();
        $this->addGoogleTranslate();
        $this->addMetaTag();

        return parent::beforeRender($view);
    }

    private function addGoogleAnalyticsCode()
    {
        $gtmid = Yii::app()->params['gtm'];
        if ($gtmid && $this->isServiceEnabled('google')) {
            $this->widget('frontend.widgets.GoogleAnalytics.GoogleAnalyticsWidget', array(
                'gtmid' => $gtmid,
                'customerID' => Yii::app()->user->id ? Yii::app()->user->id : 0,
                'customerGroup' => getSessVal('customers_groups_name', '-'),
                'language' => Yii::app()->frontPageCom->getRegional('language')
            ));
        }
    }

    private function addMetaTag()
    {
        $this->pageTitle = MetaTagCom::registerMetaTag($this->meta_tag_info);
        Yii::app()->clientScript->registerMetaTag('IE=11', null, 'X-UA-Compatible');
    }

    protected function isServiceEnabled($type)
    {
        if (!isset($this->enabled_service[$type])) {
            $this->enabled_service[$type] = true;

            if (!isset(Yii::app()->session['ses_ip_country_iso_code_2'])) {
                if ($countryInfo = Yii::app()->geoip->getIPCountryInfo(Yii::app()->frontPageCom->getIPAddress())) {
                    $country_code = $countryInfo['countries_iso_code_2'];
                } else {
                    $country_code = Yii::app()->params['REGIONAL_SETTING']['DEFAULT_COUNTRY_CODE'];
                }

                Yii::app()->session['ses_ip_country_iso_code_2'] = $country_code;
            }

            if ($type == 'google') {
                if (in_array(Yii::app()->session['ses_ip_country_iso_code_2'], array('CN'))) {
                    $this->enabled_service[$type] = false;
                }
            }
        }

        return $this->enabled_service[$type];
    }

    public function setLocalizedUrl($route, $params = [])
    {
        $url = Yii::app()->createUrl(
            $route,
            array_merge($params, [
                'country_code' => Yii::app()->session['country_code'],
                'language_code' => Yii::app()->session['language']
            ])
        );

        if (!isset($_REQUEST['reg_ctry']) || !isset($_REQUEST['reg_lang']) || strcasecmp(Yii::app()->session['country_code'], $_REQUEST['reg_ctry']) != 0 || strcasecmp(Yii::app()->session['language'], $_REQUEST['reg_lang']) != 0) {
            if (Yii::app()->user->isGuest) {
                Yii::app()->clientScript->registerScript(
                    'redirect',
                    "
                setUrl(\"$url\");
"
                );
            } else {
                $this->redirect($url);
            }
        }
    }

    public function appendCssClass($current, $new)
    {
        return $new != '' ? $current . ' ' . $new : $current;
    }

    private function registerAssets()
    {
        Yii::app()->clientScript->coreScriptPosition = CClientScript::POS_END;
        $registry = Yii::app()->clientScript;
        $publisher = Yii::app()->assetManager;

        if ($this->theme === 2017) {
            $main_ui = $publisher->publish(
                ROOT_DIR . '/frontend/packages/theme',
                $hashByName = false,
                $level = -1,
                Yii::app()->debug_mode
            );

            $config_array = array(
                'WWW_AJAX_URL' => $this->createAbsoluteUrl("/"),
                'CHECKOUT_AJAX_URL' => $this->createAbsoluteUrl("/checkout"),
                'LOGIN_URL' => Yii::app()->frontPageCom->getLoginURL(),
                'cid' => Yii::app()->user->id,
                'country' => getSessVal('country_code'),
                'ip_iso_2' => Yii::app()->frontPageCom->customer_ip_country,
                'language' => getSessVal('language'),
                'regional_url' => $this->createAbsoluteUrl("/userBar/regional"),
                'origin' => Yii::app()->frontPageCom->getURLInfo('route'),
                'url_params' => Yii::app()->frontPageCom->getURLInfo('query'),
                'algolia_application' => Yii::app()->params['ALGOLIA']['APPLICATION_ID'],
                'algolia_key' => Yii::app()->params['ALGOLIA']['SEARCH_API_KEY'],
                'brand_indices' => Yii::app()->params['ALGOLIA']['INDICES']['BRAND'],
                'categories_indices' => Yii::app()->params['ALGOLIA']['INDICES']['CATEGORIES'],
                'products_indices' => Yii::app()->params['ALGOLIA']['INDICES']['PRODUCTS']['en'],
                'imageBaseUrl' => Yii::app()->params['AWS_CONFIG']['BUCKETS_ARRAY']['BUCKET_STATIC']['domain'] . "images/products/",
                'no_product_image' => "{$main_ui}/img/no_product_image.png",
                'webpushSenderId' => Yii::app()->params['WEBPUSH']['SENDER_ID'],
                'tabCallBackText' => Yii::t('page_content', 'TEXT_CALLBACK_TAB'),
                'tabCallBackTitle' => Yii::t('page_content', 'TEXT_CALLBACK_TITLE'),
                'tabCallBackMsg' => Yii::t('page_content', 'TEXT_CALLBACK_MSG'),
                'tabCallBackIcon' => Yii::app()->params['WEBPUSH']['ICON'],
                'tabCallBackImage' => Yii::app()->params['WEBPUSH']['IMAGE'],
                'tabCallBackTimer' => Yii::app()->params['WEBPUSH']['BROWER_VIS_TIMER'],
                'tabCallBackCookiesTimer' => Yii::app()->params['WEBPUSH']['COOKIES_TIMER'],
            );

            $registry
                ->registerCssFile("{$main_ui}/css/main.min.css")
                ->registerScriptFile("{$main_ui}/js/vendor.min.js", CClientScript::POS_END)
                ->registerScriptFile("{$main_ui}/js/main.js", CClientScript::POS_END)
                ->registerScript(
                    "main_config",
                    "var config=" . CJSON::encode($config_array) . ";",
                    CClientScript::POS_BEGIN
                )
                ->registerScript(
                    "i18n",
                    "var translate={session_expired:'" . Yii::t(
                        'page_content',
                        'ERROR_SESSION_EXPIRED'
                    ) . "',general_error:'" . Yii::t(
                        'page_content',
                        'ERROR_GENERAL'
                    ) . "',general_error_reload:'" . Yii::t(
                        'buttons',
                        'BTN_RELOAD'
                    ) . "',general_error_close:'" . Yii::t('buttons', 'BTN_CLOSE') . "'};",
                    CClientScript::POS_BEGIN
                )
                ->registerScriptFile("{$main_ui}/js/algoliasearch.min.js", CClientScript::POS_END)
                ->registerScriptFile("{$main_ui}/js/store_credit.min.js", CClientScript::POS_END)
                ->registerScriptFile(
                    "https://static.addtoany.com/menu/page.js",
                    CClientScript::POS_END,
                    array('defer' => true)
                )
                ->registerScript(
                    "share_config",
                    <<<SHARE_CONFIG
                  var a2a_config = a2a_config || {};
                  a2a_config.prioritize = ["copy_link"];
                  a2a_config.exclude_services = [ "myspace", "linkedin", "amazon_wish_list", "aol_mail", "balatarin", "bibsonomy", "bitty_browser", "blogger", "blogmarks", "bookmarks_fr",
                      "box_net", "buffer", "care2_news", "citeulike", "design_float", "diary_ru", "diaspora", "digg", "diigo", "douban",
                      "draugiem", "dzone", "evernote", "fark", "flipboard", "folkd", "email", "google_gmail", "google_bookmarks", "google_classroom",
                      "hacker_news", "hatena", "houzz", "instapaper", "kakao", "kik", "known", "livejournal", "mail_ru",
                      "mastodon", "mendeley", "meneame", "mewe", "mix", "mixi", "netvouz", "odnoklassniki", "outlook_com", "papaly", "pinboard",
                      "pinterest", "plurk", "pocket", "print", "printfriendly", "protopage_bookmarks", "pusha", "kindle_it", "qzone", "reddit",
                      "rediff", "refind", "renren", "sina_weibo", "sitejot", "skype", "slashdot", "stocktwits", "svejo",
                      "symbaloo_bookmarks", "threema", "trello", "tuenti", "tumblr", "twiddla", "typepad_post", "viadeo", "viber",
                      "vk", "wanelo", "wordpress", "wykop", "xing", "yahoo_mail", "yoolink", "yummly" ];
SHARE_CONFIG
                    ,
                    CClientScript::POS_BEGIN
                )
                ->setCoreScriptUrl('');

            $registry->registerLinkTag('icon', 'image/x-icon', "{$main_ui}/img/icon.png", false, false);
            $registry->registerLinkTag('icon', 'apple-touch-icon', "{$main_ui}/img/icon.png", false, false);
        } elseif ($this->theme === 2022) {
            $main_ui = $publisher->publish(
                ROOT_DIR . '/frontend/packages/theme-2022',
                $hashByName = false,
                $level = -1,
                Yii::app()->debug_mode
            );
            $config_array = array(
                'country' => getSessVal('country_code'),
                'ip_iso_2' => Yii::app()->frontPageCom->customer_ip_country,
                'language' => getSessVal('language'),
                'general_error' => Yii::t('page_content', 'ERROR_GENERAL'),
                'algolia_application' => Yii::app()->params['ALGOLIA']['APPLICATION_ID'],
                'algolia_key' => Yii::app()->params['ALGOLIA']['SEARCH_API_KEY'],
                'brand_indices' => Yii::app()->params['ALGOLIA']['INDICES']['BRAND'],
                'no_product_image' => "{$main_ui}/img/brand-default.jpg",
                'webpushSenderId' => Yii::app()->params['WEBPUSH']['SENDER_ID'],
                'tabCallBackText' => Yii::t('page_content', 'TEXT_CALLBACK_TAB'),
                'tabCallBackTitle' => Yii::t('page_content', 'TEXT_CALLBACK_TITLE'),
                'tabCallBackMsg' => Yii::t('page_content', 'TEXT_CALLBACK_MSG'),
                'tabCallBackIcon' => Yii::app()->params['WEBPUSH']['ICON'],
                'tabCallBackImage' => Yii::app()->params['WEBPUSH']['IMAGE'],
                'tabCallBackTimer' => Yii::app()->params['WEBPUSH']['BROWER_VIS_TIMER'],
                'tabCallBackCookiesTimer' => Yii::app()->params['WEBPUSH']['COOKIES_TIMER'],
                'region_json' => Yii::app()->params['REGIONAL_SETTING']['REGION_STATIC_DOMAIN'],
                'country_json' => Yii::app()->params['REGIONAL_SETTING']['COUNTRY_STATIC_DOMAIN'],
                'excluded_cur' => RegionalSettingCom::getExcludedCurrencyJSON()
            );

            $registry->registerLinkTag('icon', 'image/x-icon', "{$main_ui}/img/icon.png", false, false);
            $registry->registerLinkTag('icon', 'apple-touch-icon', "{$main_ui}/img/icon.png", false, false);

            $registry->registerScript(
                "share_config",
                <<<SHARE_CONFIG
                  var a2a_config = a2a_config || {};
                  a2a_config.prioritize = ["copy_link"];
                  a2a_config.exclude_services = [ "myspace", "linkedin", "amazon_wish_list", "aol_mail", "balatarin", "bibsonomy", "bitty_browser", "blogger", "blogmarks", "bookmarks_fr",
                      "box_net", "buffer", "care2_news", "citeulike", "design_float", "diary_ru", "diaspora", "digg", "diigo", "douban",
                      "draugiem", "dzone", "evernote", "fark", "flipboard", "folkd", "email", "google_gmail", "google_bookmarks", "google_classroom",
                      "hacker_news", "hatena", "houzz", "instapaper", "kakao", "kik", "known", "livejournal", "mail_ru",
                      "mastodon", "mendeley", "meneame", "mewe", "mix", "mixi", "netvouz", "odnoklassniki", "outlook_com", "papaly", "pinboard",
                      "pinterest", "plurk", "pocket", "print", "printfriendly", "protopage_bookmarks", "pusha", "kindle_it", "qzone", "reddit",
                      "rediff", "refind", "renren", "sina_weibo", "sitejot", "skype", "slashdot", "stocktwits", "svejo",
                      "symbaloo_bookmarks", "threema", "trello", "tuenti", "tumblr", "twiddla", "typepad_post", "viadeo", "viber",
                      "vk", "wanelo", "wordpress", "wykop", "xing", "yahoo_mail", "yoolink", "yummly" ];
SHARE_CONFIG
                ,
                CClientScript::POS_BEGIN
            )->registerScript(
                "main_config",
                "var config=" . CJSON::encode($config_array) . ";",
                CClientScript::POS_BEGIN
            );
        }

        $live_chat_string = false;

        // Remove live chat string as per requested by James
//        $this->getLiveChatContent(
//            'function initFreshChat(){window.fcWidget.init(LIVE_CHAT_PARAMS),jQuery("#freshchat_widgets").css("display","block");}function initialize(a,b){var c;a.getElementById(b)?initFreshChat():((c=a.createElement("script")).id=b,c.defer=true,c.src="https://wchat.freshchat.com/js/widget.js",c.onload=initFreshChat,a.head.appendChild(c))}function initiateCall(){initialize(document,"freshchat-js-sdk")}window.addEventListener?window.addEventListener("load",initiateCall,!1):window.attachEvent("load",initiateCall,!1);'
//        );

        if (isset(Yii::app()->controller->id) && isset(Yii::app()->controller->action->id)) {
            $controllerId = strtolower(Yii::app()->controller->id);
            $actionId = strtolower(Yii::app()->controller->action->id);
            switch($controllerId){
                case "account":
                    if($actionId == "orderdetail"){
                        if(isset($this->order_status) && $this->order_status == "7"){
                            $live_chat_string = true;
                        }
                    }
                    break;
            }
        }



        if ($live_chat_string) {
            $registry->registerScriptFile("//fw-cdn.com/2623928/3076726.js", CClientScript::POS_END, array("chat" => 'true', 'widgetId'=>'0d3106b6-e631-4f70-a3d7-2db7d9e40cb6', 'defer' => true));
        }
        else{
            $freshwork_string = $this->getFreshworksContent('window.fwSettings={widget_id:**********},function(){var i;"function"!=typeof window.FreshworksWidget&&((i=function(){i.q.push(arguments)}).q=[],window.FreshworksWidget=i)}(),FreshworksWidget("hide","launcher"),FreshworksWidget("identify","ticketForm",FRESH_WORKS_PARAMS);jQuery("#freshworks_widgets").css("display","block")');
            $registry->registerScriptFile("https://widget.freshworks.com/widgets/**********.js", CClientScript::POS_END, array('defer' => true));
            $registry->registerScript("main_config", $freshwork_string, CClientScript::POS_END);
        }

        $prefetch_list = Yii::app()->params['HTTP_PREFETCH'];
        if ($this->isServiceEnabled('google')) {
            $prefetch_list = array_merge($prefetch_list, Yii::app()->params['GTM_HTTP_PREFETCH']);
        }

        foreach ($prefetch_list as $url) {
            $registry->registerLinkTag('preconnect', false, $url, false, false);
        }
    }

    public function redirectToLogin()
    {
        if (Yii::app()->request->isAjaxRequest) {
            echo "INVALID REQUEST";
            Yii::app()->end();
        } else {
            $this->redirect(Yii::app()->frontPageCom->getLoginURL());
        }
    }

    public function redirectToHome()
    {
        $this->redirect(Yii::app()->user->getReturnUrl($this->createAbsoluteUrl("/")));
    }

    protected function performAjaxValidation($model, $formId)
    {
        if ($model && $formId) {
            if (isset($_POST['ajax']) && $_POST['ajax'] === $formId) {
                echo CActiveForm::validate($model);
                Yii::app()->end();
            }
        }
    }

    public function getBreadcrumbsWidgetContent($link = '')
    {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return array(
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
        );
    }

    public function addGoogleTranslate()
    {
        if ($this->isServiceEnabled('google') && $this->theme == 2017) {
            Yii::app()->clientScript->registerScriptFile(
                "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit",
                CClientScript::POS_END,
                array('defer' => true)
            );
        }
    }

    public function getLiveChatContent($live_chat_string)
    {
        $params = [
            'token' => '6e44e18e-ce41-43c8-9dc0-0ed1cc68c6b4',
            'host' => 'https://wchat.freshchat.com',
            "config" => [
                "headerProperty" => [
                    "hideChatButton" => true
                ]
            ]
        ];
        $user_info = [];
        $live_chat_cust_group = (!empty(Yii::app()->params['LIVE_CHAT_CUSTOMERS_GROUP']) ? Yii::app()->params['LIVE_CHAT_CUSTOMERS_GROUP'] : []);

        if (Yii::app()->user->id) {
            if ($customers_groups_id = getSessVal('customers_groups_id')) {
                $user_info = [
                    'externalId' => Yii::app()->user->id,
                    'firstName' => getSessVal('customers_firstname'),
                    'lastName' => getSessVal('customers_lastname'),
                    'email' => getSessVal('customers_email_address'),
                    'customerGroupId' => $customers_groups_id,
                    'customerGroupName' => getSessVal('customers_groups_name')
                ];

                if (!in_array($customers_groups_id, $live_chat_cust_group)) {
                    $live_chat_string = '';
                }
            }
        } elseif (!in_array(0, $live_chat_cust_group)) {
            $live_chat_string = '';
        }

        $live_chat_string = str_replace(
            "LIVE_CHAT_PARAMS",
            json_encode(array_merge($params, $user_info)),
            $live_chat_string
        );


        return $live_chat_string;
    }

    public function getFreshworksContent($fresh_works_string)
    {
        $user_info = [];
        if (Yii::app()->user->id) {
            $user_info = [
                'name' => getSessVal('customers_firstname') . ' ' . getSessVal('customers_lastname'),
                'email' => getSessVal('customers_email_address'),
            ];
        }
        $fresh_works_string = str_replace("FRESH_WORKS_PARAMS", json_encode($user_info), $fresh_works_string);
        return $fresh_works_string;
    }

    public function getMegaMenuWidgetContent()
    {
        return array(
            'data' => Yii::app()->frontPageCom->getMegaMenuContent(),
            'all_prefix' => Yii::t('buttons', 'MEGA_MENU_SHOW_ALL_CATEGORY_PREFIX') . '&nbsp;',
        );
    }

    public function getSupportedPGContent()
    {
        return Yii::app()->frontPageCom->getPaymentMethodsAvailable();
    }

    public function getUserBarWidgetContent()
    {
        return Yii::app()->frontPageCom->getUserBarContent();
    }

    public function getFooterContent()
    {
        $toll_free = Yii::app()->frontPageCom->getCountryContent('toll_free', '');

        $footer_left_info = array(
            'CORPORATE' => array(
                'http://corp.offgamers.com/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'BLOG' => array(
                'https://blog.offgamers.com/blog/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'CAREERS' => array(
                'https://corp.offgamers.com/career/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'WHY_US' => array(
                Yii::app()->createUrl('site/page', array('view' => 'why-us')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'PAYMENT_GUIDE' => array(
                'https://www.offgamers.com/payment-guide/',
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'TNC' => array(
                Yii::app()->createUrl('site/page', array('view' => 'terms-of-service')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'PRIVACY' => array(
                Yii::app()->createUrl('site/page', array('view' => 'privacy-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'REFUND' => array(
                Yii::app()->createUrl('site/page', array('view' => 'refund-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'ADS' => array(
                Yii::app()->createUrl('site/page', array('view' => 'advertising-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
        );

        $footer_right_info = array(
            'in' => Yii::app()->frontPageCom->FOOTER_SOCIAL_LINKEDIN_URL,
            'youtube' => Yii::app()->frontPageCom->FOOTER_SOCIAL_YOUTUBE_URL,
            'twitter' => Yii::app()->frontPageCom->FOOTER_SOCIAL_TWITTER_URL,
            'fb' => Yii::app()->frontPageCom->FOOTER_SOCIAL_FB_URL,
            'weibo' => Yii::app()->frontPageCom->FOOTER_SOCIAL_WEIBO_URL,
            'instagram' => Yii::app()->frontPageCom->FOOTER_SOCIAL_INSTAGRAM_URL,
            'tiktok' => Yii::app()->frontPageCom->FOOTER_SOCIAL_TIKTOK_URL,
            'douyin' => Yii::app()->frontPageCom->FOOTER_SOCIAL_DOUYIN_URL,
            'redbook' => Yii::app()->frontPageCom->FOOTER_SOCIAL_REDBOOK_URL,
        );

        if (!$this->isServiceEnabled('google')) {
            unset($footer_right_info['gp'], $footer_right_info['fb']);
        }

        return array(
            'left_content' => $footer_left_info,
            'right_content' => $footer_right_info,
            'toll_free' => ($toll_free != '' ? $toll_free : Yii::app()->frontPageCom->FOOTER_DEFAULT_TOLL_FREE)
        );
    }

    public function getNewFooterContent()
    {
        $footer_left_info = array(
            'CORPORATE' => array(
                'http://corp.offgamers.com/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'BLOG' => array(
                'https://blog.offgamers.com/blog/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'MARKETING' => array(
                'https://corp.offgamers.com/partnerships/business-partners/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'CAREERS' => array(
                'https://corp.offgamers.com/career/',
                array('target' => '_blank', 'class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'WHY_US' => array(
                Yii::app()->createUrl('site/page', array('view' => 'why-us')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'PAYMENT_GUIDE' => array(
                'https://www.offgamers.com/payment-guide/',
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'TNC' => array(
                Yii::app()->createUrl('site/page', array('view' => 'terms-of-service')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'PRIVACY' => array(
                Yii::app()->createUrl('site/page', array('view' => 'privacy-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'REFUND' => array(
                Yii::app()->createUrl('site/page', array('view' => 'refund-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
            'ADS' => array(
                Yii::app()->createUrl('site/page', array('view' => 'advertising-policy')),
                array('class' => 'footer-list__link footer-link-items-link', 'rel' => 'nofollow')
            ),
        );

        return array(
            'left_content' => $footer_left_info,
        );
    }
}
