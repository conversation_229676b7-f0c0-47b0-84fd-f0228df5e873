<?php
/**
 * This is the helper class for you to have frontend-specific stuff in WebApplication instance.
 * Note that you might want to describe your application components
 * using `@property` declarations in this docblock.
 *
 * @package YiiBoilerplate\Frontend
 */
class FrontendWebApplication extends CWebApplication
{
    public  $debug_mode = FALSE;
    
    public function init() {
        parent::init();
        
        $this->debug_mode = defined('YII_DEBUG') ? YII_DEBUG : FALSE;
    }
} 