<?php

class SSOCom extends MainCom 
{
    public static function SSOLogin($loginPage = false, $redirectNextUrl = '', $snsType = '', $shasso_url_extra_params = '') {     
        $S3ID = '';
        if (isset($_GET['S3ID']) && !empty($_GET['S3ID'])) {
            $S3ID = $_GET['S3ID'];
            $S3RM = true;
        } else if (isset(Yii::app()->request->cookies['S3ID']) && !empty(Yii::app()->request->cookies['S3ID'])) {
            $S3ID = htmlspecialchars(Yii::app()->request->cookies['S3ID']->value);
        }

        if ($S3ID) {
            //delete S3ID cookie
            self::deleteCookie("S3ID");

            $data = self::_loginValidation($S3ID);

            if ((isset($data["status"]) && $data["status"]) && isset($data["result"][0]["user_id"])) {
                $doLogin = false;
                if (Yii::app()->user->isGuest) {
                    $doLogin = true;
                } else if (Yii::app()->user->id != $data["result"][0]["user_id"]) {
                    Yii::app()->user->logout();
                    $doLogin = true;
                } else {
                    //When SHASSO update firstname, lastname or email
                    //use for portal to update profiler session
                    Yii::app()->session['S3UP'] = "TRUE";
                }

                if ($doLogin === true) {
                    self::deleteCookie("webpush");
                    $model = new SSOLogin();
                    $model->username = $data["result"][0]["user_id"];
                    if ($model->validate() && $model->login()) {
                        //set firstTimeLogin session, use for portal
                        Yii::app()->session['firstTimeLogin'] = "TRUE";
                        //set state for shasso return data
                        Yii::app()->user->setState('login_method', $data["result"][0]["login_method"]);
                        if (isset($data["result"][0]["avatar"]) && !empty($data["result"][0]["avatar"])) {
                            Yii::app()->user->setState('avatar', $data["result"][0]["avatar"]);
                        }
                        //set remember me cookie for GET method only
                        if (isset($S3RM)) {
                            if (isset($_GET['S3RM']) && $_GET['S3RM'] == "1") {
                                $cookie = new CHttpCookie('S3RM', $_GET['S3RM']);
                                $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
                                $cookie->expire = time() + Yii::app()->params['SHASSO_CONFIG']['SSO_S3RM_COOKIE_DURATION'];
                                $cookie->path = '/';
                                Yii::app()->request->cookies['S3RM'] = $cookie;
                            } else {
                                self::deleteCookie("S3RM");
                            }
                        }

                        // 3rd party Analytic Tool
                        (new Clevertap())->eventUserLogin();
                    }
                }
            }
        } else {
            if (Yii::app()->user->isGuest) {
                if (isset(Yii::app()->request->cookies['S3RM']) && Yii::app()->request->cookies['S3RM'] == "1") {
                    //delete S3RM cookie
                    self::deleteCookie("S3RM");
                    $loginPage = true;
                }
                
                if ($loginPage === true) {
                    if (!$redirectNextUrl) {
                        $redirectNextUrl = Yii::app()->request->getHostInfo() . Yii::app()->request->getUrl();
                    }
                    $shassoLoginUrl = Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . "/sso/index?action=login&origin=" . urlencode(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']) . "&service=" . Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'] . "&hl=" . Yii::app()->language . "&next_url=" . urlencode($redirectNextUrl);
                    if ($snsType) {
                        $shassoLoginUrl .= "&sns=" . $snsType;
                    }
                    
                    if ($shasso_url_extra_params) {
                        $shassoLoginUrl .= '&' . $shasso_url_extra_params;
                    }
                    
                    Yii::app()->request->redirect($shassoLoginUrl);
                }
            } else {
                if ($redirectNextUrl) {
                    if (domainMapping($redirectNextUrl) === TRUE) {
                        Yii::app()->request->redirect($redirectNextUrl);
                    } else {
                        Yii::app()->request->redirect(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']);
                    }
                }
            }
        }

        if (isset($_GET['next_url'])) {
            $redirectNextUrl = urldecode($_GET['next_url']);
            if (domainMapping($redirectNextUrl) === TRUE) {
                    Yii::app()->request->redirect($redirectNextUrl);
            } else {
                    Yii::app()->request->redirect(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']);
            }
        }
    }

    private static function _loginValidation($S3ID) {
        $url = Yii::app()->params['SHASSO_CONFIG']['API_URI'] . "/sso/requestUid";
        $params = array(
            'S3ID' => $S3ID,
            'service' => Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'],
            'signature' => md5(Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'] . $S3ID . Yii::app()->params['SHASSO_CONFIG']['CLIENT_SECRET']),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // don't check certificate
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // don't check certificate
        curl_setopt($ch, CURLOPT_VERBOSE, TRUE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $server_output = curl_exec($ch);

        if ($server_output === false) {
            self::reportError('SSOCom:_loginValidation', array('error' => 'cURL resource: ' . (string) $ch . '; cURL error: ' . curl_error($ch) . ' (cURL error code ' . curl_errno($ch) . ')'));
		}
        
        curl_close($ch);

        if (!empty($server_output)) {
            $result = CJSON::decode($server_output);
        } else {
            $result = array();
        }
        
        return $result;
    }

    private static function deleteCookie($cookieName = null) {
        if ($cookieName) {
            $cookie = new CHttpCookie($cookieName, "");
            $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
            $cookie->expire = time() - 1;
            $cookie->path = '/';
            Yii::app()->request->cookies[$cookieName] = $cookie;
        }
    }

}

