<?php
/**
 * This is the code which assembles the global configuration for all entry points of the app.
 *
 * Config is being constructed from three parts: base and local configuration overrides,
 *
 * NOTE that this global config will be overridden further by the configuration of each individual entry point
 *
 * @see `console/config/main.php`
 * @see `frontend/config/main.php`
 * @see `backend/config/main.php`
 */

return CMap::mergeArray(
    (require ROOT_DIR . '/common/config/main.php'),
    (require dirname(__FILE__) . '/main_base.php'),
    (file_exists(dirname(__FILE__) . '/main_local.php') ? require(dirname(__FILE__) . '/main_local.php') : array())
);