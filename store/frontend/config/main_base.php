<?php

// Frontend Main Config
// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

return array(
    'basePath' => dirname(__FILE__) . DIRECTORY_SEPARATOR . '..',
    // autoloading model and component classes
    'import' => array(
        'application.controllers.*'
    ),
    'modules' => array(
        'checkout' => array(),
        'order' => array(),
    ),
    // application components
    'components' => array(
        'user' => array(
            'class' => 'application.components.core.WebUser',
            // enable cookie-based authentication
            'allowAutoLogin' => false,
        ),
        'request' => array(
            //            'enableCsrfValidation' => true,
            //            'enableCookieValidation'=>true,   // this setting will include hash key together with the value. enhancement required b4 apply
        ),
        // uncomment the following to enable URLs in path-format
        'urlManager' => array(
            'urlFormat' => 'path',
            'showScriptName' => false,
            'rules' => array(
                array(
                    'class' => 'application.components.SeoCom',
                    'connectionID' => 'db',
                ),
                '<country:\w{2}>/<language:(en|id|cn|ar|bn|de|es|fr|ha|hi|it|jp|kh|ko|la|mm|nl|pa|pt|ro|ru|sw|th|tr|vi)>' => 'site/index',
                '/' => 'site/index',
                '<language:(en|id|cn|ar|bn|de|es|fr|ha|hi|it|jp|kh|ko|la|mm|nl|pa|pt|ro|ru|sw|th|tr|vi)>/' => 'site/index',
                '<country:\w{2}>/<language:(en|id|cn|ar|bn|de|es|fr|ha|hi|it|jp|kh|ko|la|mm|nl|pa|pt|ro|ru|sw|th|tr|vi)>/<controller:\w+>/<action:\w+>' => array(
                    '<controller>/<action>',
                    'urlSuffix' => '',
                    'caseSensitive' => false
                ),
                '<language:(en|id|cn|ar|bn|de|es|fr|ha|hi|it|jp|kh|ko|la|mm|nl|pa|pt|ro|ru|sw|th|tr|vi)>/<controller:\w+>/<action:\w+>' => array(
                    '<controller>/<action>',
                    'urlSuffix' => '.htm',
                    'caseSensitive' => false
                ),
                '<controller:\w+>/<action:\w+>' => array(
                    '<controller>/<action>',
                    'urlSuffix' => '.htm',
                    'caseSensitive' => false
                ),
                '<language:(en|id|cn|ar|bn|de|es|fr|ha|hi|it|jp|kh|ko|la|mm|nl|pa|pt|ro|ru|sw|th|tr|vi)>/<module:(checkout|order)>/<controller:\w+>/<action:\w+>' => array(
                    '<module>/<controller>/<action>',
                    'urlSuffix' => '.htm',
                    'caseSensitive' => false
                ),
                '<module:(checkout|order)>/<controller:\w+>/<action:\w+>' => array(
                    '<module>/<controller>/<action>',
                    'urlSuffix' => '.htm',
                    'caseSensitive' => false
                ),
            ),
        ),
        'zipfile' => array(
            'class' => 'application.extensions.zipfile.Zipfile'
        ),
        'errorHandler' => array(
            'errorAction' => 'site/error',
        ),
        'cTagCom' => array(
            'class' => 'frontend.components.FrontendCategoriesTagCom'
        ),
        'customerCom' => array(
            'class' => 'frontend.components.FrontendCustomerCom'
        ),
        'currency' => array(
            'class' => 'common.components.CurrenciesCom'
        ),
        'localizationCom' => array(
            'class' => 'frontend.components.LocalizationCom'
        ),
        'searchBoxCom' => array(
            'class' => 'frontend.components.SearchBoxCom'
        ),
        'cdKeyCom' => array(
            'class' => 'frontend.components.CDKeyCom'
        ),
        //        'pageContentCom' => array(
        //            'class' => 'frontend.components.FrontendPageContentCom'
        //        ),
        'registerTagCom' => array(
            'class' => 'frontend.components.RegisterTagCom',
        ),
        'clientScript' => array(
            'corePackages' => array()
        ),
        'ordersReviewCom' => array(
            'class' => 'frontend.components.OrdersReviewCom'
        ),
    ),
    // application-level parameters that can be accessed
    // using Yii::app()->params['paramName']
    'params' => array(
        'SITE_ID' => '0',
        'OGM_PRODUCT_TYPE' => '2',
        'LISTING_PRICE' => array(
            '2' => array(
                'decimal' => 6,
                'min' => 0.000001
            ),
        ),
        'PRODUCT_PER_PAGE' => 20,
        'GAME_PRODUCT_PER_PAGE' => 24,
        'MAX_RECENT_VIEWED_PRODUCTS' => 16,
        'MAX_RECENT_PURCHASED_PRODUCTS' => 6,
        'LAZY_LOADER_PRODUCT_PER_PAGE' => 15,
        // Password verifying lifespan
        'VERIFYING_USER_PASSWORD_LIFESPAN' => 10,

        // Use cookie for GST NOTICE Bar
        'GST_NOTICE_BY_COOKIE' => true
    ),
    // default website language
    // use Yii::app()->language = 'en' to change website global language
    'language' => 'en',
);