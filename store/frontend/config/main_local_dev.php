<?php

// Frontend Local Config
// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

return array(
    'name' => 'OffGamers Online Game Store',
    // application components
    'components' => array(
        'assetManager' => array(
            'class' => 'S3AssetManager',
            'bucketKey' => 'BUCKET_STATIC',
            'host' => '',
            'secureHost' => '',
            'path' => 'assets', // or any name for the assets folder in aws s3
            'vue_path' => 'assets', // or any name for the vue assets folder in aws s3
            'enabledFlag' => false,
            'excludeFiles' => array('CVS'), // folder name in case sensitive
        ),
        'session' => array(
            'class' => 'CCacheHttpSession',
            'cacheID' => 'redis_cache',
            'sessionName' => 'OGMV3SESID',
            'timeout' => 7200,
            'cookieParams' => array(
                'domain' => 'www.offgamers.local',
                'path' => '/',
                'httpOnly' => true,
                'secure' => true
            ),
        ),
        'ImgCom' => array(
            'class' => 'frontend.components.FrontendImageCom',
            'DIR_FS_IMAGES' => 'C:/wamp/vhosts/offgamers.lan/www.offgamers.lan/httpdocs/images/',
            'DIR_WS_IMAGES' => 'http://www.offgamers.lan/images/',
        ),
        'frontPageCom' => array(
            'class' => 'frontend.components.FrontPageCom',
            'max_banner_container_allow_category' => 10,
            'max_best_selling' => 10,
            'recent_viewed_product_cookie_name' => 'rvp',
            'recent_viewed_product_cookie_day' => 30,

            'HEADER_KB_URL' => array(
                1 => 'http://kb.offgamers.com',
                2 => 'http://kb.offgamers.com/zhcn',
            ),
            'FOOTER_DEFAULT_TOLL_FREE' => '+603-9222-6654',
            'FOOTER_SOCIAL_LINKEDIN_URL' => '/',
            'FOOTER_SOCIAL_TWITTER_URL' => 'http://twitter.com/offgamers',
            'FOOTER_SOCIAL_YOUTUBE_URL' => 'https://www.youtube.com/user/offgamers/',
            'FOOTER_SOCIAL_FB_URL' => 'http://www.facebook.com/OffGamers',
            'FOOTER_SOCIAL_WEIBO_URL' => 'https://www.weibo.com/offgamers',
            'FOOTER_SOCIAL_INSTAGRAM_URL' => 'https://www.instagram.com/offgamers/',
            'FOOTER_SOCIAL_TIKTOK_URL' => 'https://www.tiktok.com/@offgamers?lang=en',
            'FOOTER_SOCIAL_DOUYIN_URL' => 'https://www.douyin.com/user/MS4wLjABAAAAO3albO338VPJOH4gqt07kQSUu4Cs2NrYqItqUCjUriYvoFB8zjhrJ-2NWRHCKJ9W?enter_method=video_title&author_id=3677217753931095&group_id=6994727755459972365&log_pb=%7B%22impr_id%22%3A%22021628751409601fdbddc0100fff0030ad32c210000000aed9e09%22%7D&enter_from=video_detail',
            'FOOTER_SOCIAL_REDBOOK_URL' => 'https://www.xiaohongshu.com/user/profile/615292310000000002019530?xhsshare=CopyLink&appuid=567e07dcb8ce1a68a0d2dee7&apptime=1634204627',

            'DISCLAIMER_URL' => array(
                1 => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884330',
                2 => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884331',
                3 => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884331',
                4 => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884332',
            ),
            // used in product detail page
            'RSS_FEED_URL' => 'https://blog.offgamers.com/blog/category/promotions/feed/',
        ),
    ),
    // application-level parameters that can be accessed
    'params' => array(
        'env' => 'stage', //stage, prod
        # S3AssetManager
        'AWS_S3_PREFIX' => 'OffGamers/',
        'PGS_URL' => 'http://www.pgs.lan/index.php',
        'PGS_MERCHANT_SECRET' => '123456',
        'OAUTH_CLIENT' => array(
            'DEFAULT_CLIENT' => array(
                'CLIENT_ID' => 'ogm',
                'CLIENT_ID_S2S' => 'ogm_s2s',
                'CLIENT_SECRET' => '123456',
                'REDIRECT_URI' => 'http://www.offgamers.lan/v2',
            ),
        ),
        'PG_URL' => 'https://www.offgamers.com/payment-guide/',
        # cookie domain
        'COOKIE_DOMAIN' => '.offgamers.lan',
        # debugging
        'DEV_DEBUG_EMAIL_SUBJECT_PREFIX' => '[OG]',
        # pipwave - Upload Screenshoot
        'PIPWAVE_SECURE_DOMAIN' => 'https://staging-checkout.pipwave.com',
        'STORE_OWNER_EMAIL_ADDRESS' => '<EMAIL>',
        'CANCEL_ORDER_STORE_OWNER_EMAIL_ADDRESS' => '<EMAIL>',
        'CANCEL_ORDER_CODE_STORE_OWNER_EMAIL_ADDRESS' => '<EMAIL>',
        'COMPLAINT_ORDER_STORE_OWNER_EMAIL_ADDRESS' => '<EMAIL>',
        'SHASSO_CONFIG' => array(
            'STORE_OWNER' => 'Shasso_test',
            'STORE_OWNER_EMAIL_ADDRESS' => '<EMAIL>',
            'EMAIL_SUBJECT_PREFIX' => '[Shasso]',
            'STORE_NAME' => 'Shasso',
            'STORE_EMAIL_SIGNATURE' => 'Shasso.biz',
            'PORTAL_ORI_URI' => 'http://www.offgamers.lan/v2',
            'CLIENT_ID' => 'ogm',
            'CLIENT_SECRET' => '123456',
            'SSO_S3ID_COOKIE_DURATION' => 60 * 60 * 24,
            'SSO_S3RM_COOKIE_DURATION' => 30 * 60 * 60 * 24,
        ),
        'REGIONAL_SETTING' => array(
            'COUNTRY_STATIC_DOMAIN' => 'https://dev-static.offgamers.com/account/data/localization/country.json',
            'REGION_STATIC_DOMAIN' => 'https://dev-static.offgamers.com/account/data/localization/region.json',
            'DEFAULT_COUNTRY' => 129,
            'DEFAULT_COUNTRY_CODE' => 'MY',
            'DEFAULT_LANGUAGE' => 'en',
            'DEFAULT_LANGUAGE_ID' => 1,
            'DEFAULT_LANGUAGE_NAME' => 'English',
            'DEFAULT_CURRENCY' => 'USD',
        ),
        # Clear Memcache after update setting
        'IP_RESTRICTION_CATEGORY' => array(
            'WHITELIST' => array(
                'MY' => array(4444), //2299
                'CN' => array(5236),
            ),
            'BLACKLIST' => array(
                'MY' => array(7946),
            ),
        ),
        // mapper id, combine with product PG setting, Disallow will remove Allow list
        'IP_RESTRICTION_PAYMENT_METHOD' => array(
            'Allow' => array(),
            'Disallow' => array(
                'UA' => array(17, 255),
                'IQ' => array(17, 255),
            ),
        ),
        // Order tagging for Paypal Review & Echeck
        'ORDER_TAG_ID_PAYPAL_REVIEW' => 509,
        // Live 366
        'ORDER_TAG_ID_PAYPAL_ECHECK' => 510,
        // Live 609
        'ALGOLIA' => array(
            'APPLICATION_ID' => '',
            'SEARCH_API_KEY' => '',
            'INDICES' => array(
                'PRODUCTS' => array(
                    'en' => '',
                    'zh-cn' => '',
                    'id' => '',
                ),
                'CATEGORIES' => '',
                'BRAND' => '',
            ),
        ),
        'SEARCH_PREFIX_KEYWORD' => array(
            'Itunes',
            'Google Play',
            'PSN',
            'Steam',
            'Karma Koin',
            array(
                'href' => 'https://www.offgamers.com/game-card/universal-game-cards/multi-game-card-global',
                'text' => 'Multi Game Card',
                'class' => 'navigation__holder--mobile',
            ),
        ),
        'SEARCH_SUFFIX_KEYWORD' => array(
            array(
                'href' => '/promo',
                'text' => 'Promo',
                'class' => 'navigation__holder--decor',
            ),
            array(
                'href' => '/mobile-recharge',
                'text' => 'Mobile Recharge',
                'class' => 'navigation__holder--decor',
            ),
        ),

        'BEST_SELLING_LIST' => array(
            ['href' => 'itunes-gift-card', 'text' => 'iTunes Gift Cards'],
            ['href' => 'nexon-game-card', 'text' => 'Nexon Game Card'],
            ['href' => 'razer-gold', 'text' => 'Razer Gold'],
            ['href' => 'steam-wallet-codes', 'text' => 'Steam Wallet Codes'],
            ['href' => 'gocash-game-card', 'text' => 'GoCash Game Card'],
            ['href' => 'playstation-store-gift-cards', 'text' => 'PlayStation Store Gift Cards'],
            ['href' => 'google-play-gift-card', 'text' => 'Google Play Gift Cards'],
            ['href' => 'pubg-mobile-uc', 'text' => 'PUBG Mobile'],
            ['href' => 'free-fire', 'text' => 'Free Fire'],
            ['href' => 'multi-game-card', 'text' => 'Multi Game Card'],
        ),

        //Preconnect Required External Resources
        'HTTP_PREFETCH' => [
            'https://IMAGE_BUCKET_CDN',
            'https://ASSETS_BUCKET_BUCKET_CDN',
            'https://PRODUCT_IMAGE_BUCKET_CDN',
        ],

        //Preconnect GTM Resources
        'GTM_HTTP_PREFETCH' => [
            'https://static.offgamers.com',
            'https://www.google-analytics.com',
            'https://www.googleadservices.com',
            'https://fonts.googleapis.com',
            'https://www.gstatic.com',
            // FreshChat
            'https://wchat.freshchat.com',
            // MailChimp
            'https://mc.us12.list-manage.com',
            'https://downloads.mailchimp.com',
            // Tiktok Pixel
            'https://analytics.tiktok.com'
        ],

        'WEBPUSH' => array(
            'SENDER_ID' => '55548573151',
            'CLIENT_ID' => 'ogm',
            'CLIENT_SECRET' => '123456',
            'API_URL' => 'https://api.shasso2.local/',
            'BROWER_VIS_TIMER' => 120000,
            'COOKIES_TIMER' => 1,
            'ICON' => 'https://d130xiciw9h9wz.cloudfront.net/banners/2/ogm%20logo%20icon_192x192px-20181210-022127.png',
            'IMAGE' => 'https://d130xiciw9h9wz.cloudfront.net/infolink/ff16%20hp%20en-2015.jpg',
        ),

        'MS_PRODUCT_CONFIG' => array(
            'baseUrl' => 'http://product.offgamers.local',
            'key' => 'backend',
            'secret' => '123456',
        ),

        'MS_ORDER_CONFIG' => array(
            'baseUrl' => 'http://order.offgamers.local',
            'key' => 'backend',
            'secret' => '123456',
        ),

        'MS_INVENTORY_CONFIG' => array(
            'baseUrl' => 'http://inventory.offgamers.local',
            'key' => 'backend',
            'secret' => '123456',
        ),

        'MS_CHECKOUT_CONFIG' => array(
            'baseUrl' => 'http://checkout.offgamers.local',
            'key' => 'backend',
            'secret' => '123456',
        ),

        'MAINTENANCE_MODE' => array(
            'enabled' => false,
            'color' => 'green', //green/red
            'text' => 'OG_WOR_TOKEN_RENAME', //message at page_content translation, or direct input
            'cookie' => 'op_rename_noticebar_dismiss', //cookies to store dismiss setting
            'always_show' => false, //always show toggle
            'always_show_on' => [], //controllers to always show msg
        ),

        // Store Credit Params
        # Currency denominations
        'CURRENCY_DENOMINATIONS' => array(
            'USD' => array('2', '5', '10', '20', '30', '50', '100'),
            'GBP' => array('10', '20', '30', '50', '100'),
            'SGD' => array('10', '20', '30', '50', '100'),
            'SEK' => array('70', '140', '210', '350', '700'),
            'NOK' => array('60', '120', '180', '300', '600'),
            'MYR' => array('10', '20', '30', '50', '100', '200', '500'),
            'IDR' => array('50000', '100000', '200000', '300000', '500000'),
            'EUR' => array('10', '20', '30', '50', '100'),
            'DKK' => array('60', '120', '180', '300', '600'),
            'CHF' => array('10', '20', '30', '50', '100'),
            'CNY' => array('70', '140', '210', '350', '700'),
            'CAD' => array('10', '20', '30', '50', '100'),
            'BRL' => array('20', '40', '60', '100', '200'),
            'AUD' => array('10', '20', '30', '50', '100'),
            'THB' => array('200', '300', '500', '1000', '3000'),
            'MMK' => array('10000', '20000', '40000', '65000', '130000'),
            'JPY' => array('1000', '2000', '3000', '6000', '10000'),
            'PHP' => array('250', '500', '1000', '2000', '4000'),
            'MXN' => array('180', '360', '540', '900', '1800'),
            'VND' => array('228000', '456000', '684000', '1140000', '2280000'),
        ),
        # regional
        "REG_CONFIG" => array(
            'DEF_COUNTRY_ID' => 223,
            'DEF_LANGUAGE_ID' => 1,
            'DEF_COUNTRY_CODE' => 'MY',
            'DEF_CURRENCY_CODE' => 'USD',
            'DEF_LANGUAGE_CODE' => 'en',
            'DEF_LANGUAGE_NAME' => 'English',
        ),
        // blocked country list
        'BLOCKED_COUNTRY' => array(
            'CD',
            'CG',
            'CU',
            'IR',
            'KP',
            'LY',
            'SD',
            'SO',
            'SS',
            'SY',
            'YE',
        ),
        'SANCTION_COUNTRY' => array(
            247 => 'CD',
            49 => 'CG',
            54 => 'CU',
            101 => 'IR',
            112 => 'KP',
            121 => 'LY',
            199 => 'SD',
            192 => 'SO',
            252 => 'SS',
            205 => 'SY',
            235 => 'YE',
        ),
        'ANTI_FRAUD_PRODUCTS_LIST' => array(39691),
        'GIFT_CARD' => array(
            'TRIAL' => 3,
            'PRODUCT' => array(
                'ogc' => array(
                    'name' => 'OffGamers Gift Card',
                    'class' => 'OGCCom',
                    'purchase_link' => 'https://www.offgamers.com/search/index.htm?keyword=ogc',
                    'api' => array(
                        'url' => 'https://api.offgamers.biz/ws/pin',
                    ),
                ),
            ),
        ),
        'REGION_RESTRICTION_BYPASS_CUSTOMER_ID' => [],
        'LIVE_CHAT_CUSTOMERS_GROUP' => [],
        'HEADER_MGC_URL' => 'https://www.offgamers.com/game-card/universal-game-cards/multi-game-card-global',
        'HEADER_MGC_BANNER_URL' => 'https://dev-s3-og-image.s3.amazonaws.com/banners/2/2021/09/mgc.png',
        'PAYMENT_METHOD_WHITELIST_USER_ID' => [],
        'PAYMENT_METHOD_GLOBAL_BLACKLIST' => [],
        'TAX_EXEMPTION_CATEGORIES' => [12616],
        'TAX_EXEMPTION_PRODUCTS_EXCLUSION' => [170813,170814],
        'REVIEW_SCHEMA_PRODUCTS_EXCLUSION' => [],
        'CRYPTOCURRENCY_PRODUCT_LIST' => [],

        'MAIL_SENDER' => [
            'ALERT' => '<EMAIL>',
            'INFO' => '<EMAIL>',
            'ORDER' => '<EMAIL>'
        ],

        // 3rd party Analytic Tool
        'gtm' => '',    // GTM
        'CLEVERTAP' => '',  // clevertap
        #sls endpoint
        'sls.url' => 'https://staging-sls.offgamers.com', //https://sls.offgamers.com
        'sls.asset.url' => 'https://staging-assets.offgamers.com' //https://assets.offgamers.com
    ),
);