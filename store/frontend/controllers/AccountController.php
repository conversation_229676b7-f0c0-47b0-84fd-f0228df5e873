<?php

class AccountController extends FrontendController
{

    public $order_status = false;

    public function filters()
    {
        return array(
            'accessControl',
            // perform access control for CRUD operations
        );
    }

    public function accessRules()
    {
        /**
         * @ : authenticated user
         * ? : anonymous user
         * * : any user
         */
        return array(
            array(
                'allow',
                'actions' => array(
                    'index',
                    'viewOrders',
                    'orderDetail',
                    'tax',
                    'taxCancel',
                    'orderCancelForm',
                    'getCdkeyImage',
                    'getCdkeyImageMultiple',
                    'downloadAllCdKey',
                    'showImage',
                    'captcha',
                    'orderReviewed',
                    'getAllCdkeyStatus',
                    'getAllCdkey',
                    'openOrderReviewByOid',
                    'saveOrderReview',
                    'viewOrderReview',
                ),
                'users' => array('@')
            ),
            array(
                'deny',
                'users' => array('*'),
                'deniedCallback' => array(
                    $this,
                    'redirectToLogin'
                ),
            ),
        );
    }

    public function actionIndex()
    {
        $model = new MyOffgamersForm();

        $this->render('index', array('model' => $model));
    }

    public function actionViewOrders()
    {
        $page = (isset($_GET['page']) ? (int)(new CHtmlPurifier())->purify($_GET['page']) : 1);
        if (empty($page) || !is_int($page)) {
            $page = 1;
        }
        $model = new MyOffgamersForm();
        $model->page = $page;
        $this->render('order_history', array('model' => $model));
    }

    public function actionOrderDetail()
    {
        $model = new MyOffgamersForm();
        $model->order_id = $_REQUEST['orders_id'];
        $model->getOrderDetails();
        $model->getOrderStatusHistoryDetails();
        $model->getOrderProducts();
        $model->getOrdersStatusMessage();
        $model->getAllCDDTUKey();
        $this->order_status = $model->order_status;
        if (!Yii::app()->request->isAjaxRequest) {
            if (isset($_REQUEST['review']) && $_REQUEST['review']) {
                $this->render('order_details', array('model' => $model));
                echo '<script type="text/javascript">',
                    'openOrderReviewByOid(' . $model->order_id . ');',
                '</script>';
            } else {
                $this->render('order_details', array('model' => $model));
            }
        } else {
            $selectedReviewPlatform = Yii::app()->ordersReviewCom->getThirdPartyReviewPlatform();
            $response_array['content'] = $this->renderPartial('_order_content', array('model' => $model, 'selectedReviewPlatform' => $selectedReviewPlatform), true, true);
            echo CJSON::encode($response_array);
        }
    }

    public function actionTax()
    {
        $otcModel = '';
        $error = '';
        $model = new MyOffgamersForm();
        $otcObj = new TaxModuleCom(Yii::app()->user->id);
        // Check IP Country
        $taxInfo = Yii::app()->localizationCom->showGSTAlertBar(false);
        if ($taxInfo) {
            $otcModel = $otcObj->getOrdersTaxCustomers();

            if (isset(Yii::app()->session['need_captcha'])) {
                return $this->redirect('/account/captcha');
            } elseif (Yii::app()->request->isPostRequest) {
                $result = $otcObj->save($_POST);
                if ($result['status']) {
                    Yii::app()->user->setFlash('success', $result['error_message']);
                    return $this->redirect('/account/tax');
                } else {
                    $error = $result['error_message'];
                    CaptchaCom::checkNeedCaptcha('sessionCheck', array(
                        'returnUrl' => Yii::app()->createUrl('account/tax'),
                        'maxTries' => 3,
                        'sessionKey' => 'cc_taxRegister',
                    ));
                }
            }

            if ($error) {
                Yii::app()->user->setFlash('error', $error);
            }
        }

        $this->render('tax', array(
            'model' => $model,
            'otcObj' => $otcObj,
            'otcModel' => $otcModel,
            'taxInfo' => $taxInfo,
            'taxConflict' => $otcObj->existInOtherCountry($taxInfo, $otcModel),
        ));
    }

    public function actionTaxCancel()
    {
        $action = (isset($_GET['action']) ? $_GET['action'] : 'remove');
        // Check IP Country
        $taxInfo = Yii::app()->localizationCom->showGSTAlertBar(false);
        if ($taxInfo) {
            $otcObj = new TaxModuleCom(Yii::app()->user->id);
            echo CJSON::encode($otcObj->updateStatusCancel($action));
        }
    }

    public function actionOrderCancelForm()
    {
        if (Yii::app()->request->isAjaxRequest) {
            $reason = '';
            $cancelFeedback = '';
            $refundOption = '';
            $status = '';
            $error = '';
            $p = new CHtmlPurifier();
            $model = new MyOffgamersForm();
            $orderCancelForm = new OrderCancelForm();
            $attachments_allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
            $mb = 1048576; // 1024 * 1024

            if (isset($_POST['orderId']) && Yii::app()->customerCom->isActiveCustomer()) {
                if(isset($_FILES['attachments']) && isset($_FILES['attachments']['tmp_name'])) {
                    foreach ($_FILES['attachments']['tmp_name'] as $idx => $tmp_name) {
                        $mime_type = mime_content_type($tmp_name);
                        if (!in_array($mime_type, $attachments_allowed_types)) {
                            $error = "Please submit only attachments in .jpeg, .jpg, and .png formats.";
                        }
                        if ($_FILES['attachments']['size'][$idx] > 2 * $mb) {
                            $error = $error ? $error . "\n" : '';
                            $error .= "Please submit only attachment with file size less than 2MB per file.";
                        }
                        if ($error) {
                            echo CJSON::encode(array(
                                'status' => 0,
                                'errorMsg' => $error
                            ));
                            return;
                        }
                    }
                }
                $orderId = (int)$_POST['orderId'];
                $model->order_id = $orderId;
                $model->validateCancelOrderRequest();
                $model->getOrderProducts();
                if (isset($_POST['cancelFeedback'])) {
                    $cancelFeedback = $p->purify($_POST['cancelFeedback']);
                }
                if (isset($_POST['cancelReason'])) {
                    $_reason = (int)$_POST['cancelReason'];
                    $_code_reason = (int)$_POST['cancelCodeReason'];
                    if ($model->order_status == 3) {
                        $reason_array = $orderCancelForm->getOrderComplainReason();
                        $code_reason_array = $orderCancelForm->getCodeIssueReason();
                        if (isset($reason_array[$_reason])) {
                            $reason = $reason_array[$_reason];
                            if($_reason == 1 && isset($code_reason_array[$_code_reason])) {
                                $reason .= " (".$code_reason_array[$_code_reason].")";
                            }
                        } else {
                            $reason = '';
                        }
                    } else {
                        if (in_array($model->order_status, array(1, 7))) {
                            $reason_array = $orderCancelForm->getOrderCancelReason();
                            if (isset($reason_array[$_reason])) {
                                $reason = $reason_array[$_reason];
                            } else {
                                $reason = '';
                            }
                        } else {
                            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
                        }
                    }
                }

                $s3_filepath = Yii::app()->params['AWS_S3_PREFIX'] . 'order_cancel/' . $orderId . '/';
                $aws_obj = new AmazonWsCom();
                $aws_obj->set_bucket_key('BUCKET_UPLOAD');
                $aws_obj->set_filepath($s3_filepath);

                $now = time();
                $saved_file = [];
                if(isset($_FILES['attachments']) && isset($_FILES['attachments']['tmp_name'])) {
                    foreach($_FILES['attachments']['tmp_name'] as $idx => $tmp_name) {
                        $tmp_name = realpath($tmp_name);
                        $extension = pathinfo($_FILES['attachments']['name'][$idx], PATHINFO_EXTENSION);
                        $filename = $idx . '_' . $now . '.' . $extension;
                        $aws_obj->set_filename($filename);
                        $aws_obj->set_file(array('tmp_name' => $tmp_name));
                        if($aws_obj->save_file()) {
                            $saved_file[] = [
                                'fileName' => $filename,
                                'contentType' => 'image/'.$extension,
                                's3Url' => $aws_obj->get_file_url(432000)
                            ];
                        }
                    }
                }

                if ($model->order_status == 7 && isset($_POST['refundOption'])) {
                    $_refund_option = (int)$_POST['refundOption'];
                    $order = $model->order_details;
                    $payment_method = $model->getPaymentMethod();

                    $refund_option_array = $orderCancelForm->getRefundMethod($order, ($payment_method->is_refundable ?? false));
                    if (isset($refund_option_array[$_refund_option])) {
                        $refundOption = $refund_option_array[$_refund_option];
                    }
                }
                $return_arr = $orderCancelForm->cancelOrder($model, $cancelFeedback, $reason, $refundOption, $_reason ?? 0, $saved_file);
                $status = $return_arr['status'];
                $error = $return_arr['message'];
            } else {
                $status = 0;
                $error = Yii::t('page_content', 'ERROR_SESSION_EXPIRED');
            }

            echo CJSON::encode(array(
                'status' => $status,
                'errorMsg' => $error
            ));
        } else {
            $model = new MyOffgamersForm();
            $orderCancelForm = new OrderCancelForm();
            $model->order_id = $_REQUEST['orders_id'];
            $model->validateCancelOrderRequest();
            if (in_array($model->order_status, array(
                3,
                7
            ))) {
                if ($model->order_status == 3) {
                    $cancelReasonArray = $orderCancelForm->getOrderComplainReason();
                    $cancelCodeReasonArray = $orderCancelForm->getCodeIssueReason();
                    $this->render('order_complaint', array(
                        'model' => $model,
                        'cancelReason' => $cancelReasonArray,
                        'cancelCodeReason' => $cancelCodeReasonArray
                    ));
                } else {
                    $payment_method = $model->getPaymentMethod();
                    $order = $model->order_details;
                    $msg = "";

                    if ($payment_method && !$payment_method->is_refundable) {
                        // The payment is non-refundable, display message
                        $no_refund_msg = Yii::t('page_content', 'TEXT_PAYMENT_NOT_SUPPORT_REFUND');
                        $account_url = Yii::app()->createUrl('/account/store-credit/statement');
                        $redirect_msg = Yii::t('page_content', 'TEXT_CHECK_STORE_CREDIT_URL', array(
                            '{{URL}}' => $account_url
                        ));
                        $msg = $no_refund_msg . "<br>" . $redirect_msg;
                    }

                    $refundOptionArray = $orderCancelForm->getRefundMethod($order, ($payment_method->is_refundable ?? false));

                    $cancelReasonArray = $orderCancelForm->getOrderCancelReason();

                    $this->render('order_cancel', array(
                        'model' => $model,
                        'refundOption' => $refundOptionArray,
                        'cancelReason' => $cancelReasonArray,
                        'msg' => $msg,
                    ));
                }
            }
        }
    }

    public function actionGetCdkeyImage()
    {
        $model = new MyOffgamersForm();
        $cpId = 0;
        if (isset($_POST['cpId'])) {
            $cpId = (int)$_POST['cpId'];
        }
        if (isset(Yii::app()->user->id)) {
            if ($cpId != 0) {
                echo $model->getProductCodeImage($cpId);
            }
        }
    }

    public function actionGetCdkeyImageMultiple()
    {
        $model = new MyOffgamersForm();
        if (isset($_POST['cpId'])) {
            $temp_array = explode(',', $_POST['cpId']);
            echo $model->getAllProductCodeImage($temp_array);
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }
    }

    public function actionGetAllCdkey()
    {
        $model = new MyOffgamersForm();
        if (isset($_POST['cpId'])) {
            $temp_array = explode(',', $_POST['cpId']);
            echo $model->saveAllCdkey($temp_array);
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }
    }

    public function actionShowImage()
    {
        if (isset($_REQUEST['keyident'])) {
            $keyident = (int)$_REQUEST['keyident'];
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }

        $this->renderPartial('showImage', array('keyident' => $keyident));
    }

    public function actionDownloadAllCdKey()
    {
        $model = new MyOffgamersForm();
        $op_id = $_GET['op_id'];
        $hash = $_GET['hash'];
        if ($op_id && !empty(Yii::app()->user->id) && $hash === (Yii::app()->cdKeyCom->generateHashByOpId($model['orders_products_id']))) {
            $model->orders_products_id = $op_id;
            $model->downloadAllCdKey();
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }
    }

    public function actionCaptcha()
    {
        $f_data = array();

        $captcha_com = new CaptchaCom();
        $f_data['model'] = new MyOffgamersForm();

        if (Yii::app()->request->isPostRequest) {
            $f_data['status'] = 2;
            $return_url = $captcha_com->verify_response($_POST);

            if ($return_url !== false) {
                return $this->redirect($return_url);
            } else {
                Yii::app()->user->setFlash('error', Yii::t('page_content', 'ERROR_RECAPTCHA_INVALID_CODE'));
            }
        }

        $captcha = $captcha_com->load_html();

        $f_data['captcha'] = $captcha;

        return $this->render('captcha', $f_data);
    }

    public function actionOrderReviewed($orders_id)
    {
        try {
            if (!OrdersReview::isReviewed($orders_id)) {
                $model = new OrdersReview();
                $model->orders_id = $orders_id;
                $model->customers_id = Yii::app()->user->id;
                $model->created_at = time();
                $model->save();
            }
        } catch (\Exception $e) {
            // Do Nothing, Duplicate Submission
        }

        exit;
    }

    public function actionGetAllCdkeyStatus()
    {

        if (isset($_POST['opId'])) {
            $orderCdkeys = new OrdersCdkeys();
            $result = $orderCdkeys->getAllCdkeysStatus($_POST['opId']);
            if ($result) {
                echo 'true';
            } else {
                echo 'false';
            }
        } else {
            echo 'true';
        }
    }

    public function actionSaveOrderReview()
    {
        $input = json_decode($_REQUEST['params']);
        $op = array();
        $or = array();
        $ogm = new MyOffgamersForm();
        $send_email = false;

        foreach ($input as $row) {


            $order_review = new OrderReviewModel();

            if (!$order_review->getOrderReviewEditFlagByOpid($row->orders_products_id)) {
                continue;
            }

            $orders_products = new OrdersProducts();
            $products_to_categories = new ProductsToCategories();


            $criteria = new CDbCriteria();
            $criteria->select = 'orders_id, products_id, products_name, products_delivered_quantity';
            $criteria->condition = "orders_products_id = :orderProductsId
            and orders_products_is_compensate <> '1' 
            and products_bundle_id = '0'";
            $criteria->params = array(':orderProductsId' => $row->orders_products_id);
            $result = $orders_products->model()->find($criteria);

            if (isset($result)) {
                $params['orders_id'] = $result->orders_id;
                $params['products_id'] = $result->products_id;
                $params['categories_id'] = $products_to_categories->getCategoryID($params['products_id']);
                $ogm->order_id = $params['orders_id'];
                if ($ogm->checkOrdersOwnership()) {
                    $ogm->getOrderDetails();
                    $ogm->getOrderProducts();
                } else {
                    $result = "<h2>Error occurred, please try again later.</h2>";
                    echo $result;
                    return true;
                }
            } else {
                $result = "<h2>Error occurred, please try again later.</h2>";
                echo $result;
                return true;
            }


            $params['orders_products_id'] = $row->orders_products_id;
            $params['user_id'] = Yii::app()->user->id;
            $params['review_id'] = $row->reviewId;
            $params['comment'] = $row->comment;

            if ($row->review_score > 5) {
                $params['review_score'] = 5;
            } else {
                $params['review_score'] = $row->review_score;
            }

            if ($params['review_score'] <= 2) {
                $send_email = true;
                $op[] = [
                    'products_name' => $result['products_name'],
                    'products_quantity' => $result['products_delivered_quantity'],
                    'review_score' => $params['review_score'],
                    'review_comment' => $params['comment']
                ];
            }

            $result = $order_review->saveOrdersReview($params);

            if ($result) {
                $result = "true";
            }
        }

        if ($send_email) {
            Yii::app()->ordersReviewCom->sendReviewNotification($ogm, $op);
        }

        $result = "true";

        echo $result;
    }


    public function actionOpenOrderReviewByOid()
    {
        if (isset($_REQUEST['orders_id'])) {
            $orders_id = (int)$_REQUEST['orders_id'];
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }

        $result = Yii::app()->ordersReviewCom->openOrdersReviewByOid($orders_id);

        echo $result;

    }

    public function actionViewOrderReview()
    {
        $page = (isset($_GET['page']) ? (int)(new CHtmlPurifier())->purify($_GET['page']) : 1);
        if (empty($page) || !is_int($page)) {
            $page = 1;
        }
        $model = new MyOffgamersForm();
        $model->page = $page;
        $this->render('order_review', array('model' => $model));
    }
}
