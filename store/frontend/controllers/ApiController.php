<?php
/**
 * @package YiiBoilerplate\Frontend
 */
class ApiController extends FrontendController
{
//    public $layout='//layouts/frontpage';

    public function actionIndex(){
        $country_code = Yii::app()->frontPageCom->getRegional('country_code');
        $filter = "(allowed_country:$country_code OR allowed_country:*) AND NOT blocked_country:$country_code AND NOT blocked_country:*";
        $algolia_params = AlgoliaCom::getAlgoliaParams();
        $queries = [
          ['indexName' => $algolia_params['INDICES']['CATEGORIES'], 'query' => $_REQUEST['q'],'hitsPerPage' => 10,"filters"=>$filter],
          ['indexName' => $algolia_params['INDICES']['PRODUCTS'], 'query' => $_REQUEST['q'], 'hitsPerPage' => 3,"filters"=>$filter]
        ];
        $result = AlgoliaCom::sendMultipleQueries($queries)['results'];
        $return_array = [];
        foreach($result[0]['hits'] as $value){
            $return_array[] = ["category" => "" ,"label"=>$value["name"], "href"=> "http://www.offgamers.local".$value['url']];
        }
        foreach($result[1]['hits'] as $value){
            $return_array[] = ["category" => "products" ,"label"=>$value["name"], "href"=>"http://www.offgamers.local".$value['url']];
        }
        echo $_GET['callback'] . "(".json_encode($return_array).")";
    }

    public function actionSearchProduct(){
        $country_code = Yii::app()->frontPageCom->getRegional('country_code');
        $filter = ["filters"=>"(allowed_country:$country_code OR allowed_country:*) AND NOT blocked_country:$country_code AND NOT blocked_country:*"];
        $result = AlgoliaCom::sendRequest('products',$_REQUEST['q'],$filter)['hits'];
        $return_array = [];
        foreach($result as $value){
            $return_array[] = ["category" => "" ,"label"=>$value["name"], "href"=>$value['url']];
        }
        echo $_GET['callback'] . "(".json_encode($return_array).")";
    }

}