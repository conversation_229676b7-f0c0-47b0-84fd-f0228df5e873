<?php

class BrandController extends FrontendController
{
    public $layout = '//layouts-2022/main';
    public $theme = 2022;

    public function actionIndex()
    {
        $model = new BrandModel();
        $error_msg = [
            'ERROR_SELECT_BRAND' => Yii::t('page_content', 'ERROR_SELECT_BRAND'),
            'ERROR_SELECT_VALUE' => Yii::t('page_content', 'ERROR_SELECT_VALUE'),
            'ERROR_SELECT_PRODUCT' => Yii::t('page_content', 'ERROR_SELECT_PRODUCT'),
        ];
        Yii::app()->clientScript->registerScript(
            "brand_error",
            "var brand_error=" . CJSON::encode($error_msg) . ";",
            CClientScript::POS_BEGIN
        );
        try {
            $model->processUrl();
            $model->view->getReview();

            if (Yii::app()->request->isAjaxRequest) {
                $data = $model->getData();

                if ($model->next_url === 'product') {
                    $data['dtu_title'] = ($data['product_type'] == 4) ? Yii::t('page_content', 'TEXT_DELIVERY_INFO') : Yii::t('page_content', 'TEXT_TOP_UP_INFO');
                    $data['dtu_info'] = $model->view->dtu_info;
                    $data['listing'] = $this->renderPartial('_product_listing', ['model' => $model->view], 1);
                    $data['no_supported_product'] = $model->view->no_supported_product;
                } else {
                    $data['listing'] = $this->renderPartial(
                        '_listing',
                        ['listing' => $model->supported_item, 'model' => $model->view],
                        1
                    );
                    $data['no_supported_category'] = $model->view->no_supported_category;
                }
                $data['description'] = $this->renderPartial('_description', ['model' => $model->view], 1);
                $data['short_description'] = $this->renderPartial('_short_description', ['model' => $model->view], 1);
                echo CJSON::encode($data);
            } else {
                $this->setLocalizedUrl('brand/index', ['path' => $_REQUEST['brand_path']]);
                $language_code_list = ['cn', 'id', 'en'];
                foreach ($language_code_list as $language_code) {
                    $url = Yii::app()->createAbsoluteUrl(
                        "brand/index",
                        ['x-country' => true, 'path' => $model->current_path, 'language_code' => $language_code]
                    );
                    $option = ['hreflang' => ($language_code == 'cn' ? 'zh' : $language_code)];
                    Yii::app()->clientScript->registerLinkTag('alternate', null, $url, null, $option);
                }

                $option = ['hreflang' => 'x-default'];
                $url = Yii::app()->createAbsoluteUrl("brand/index", ['x-default' => true, 'path' => $model->current_path]);
                Yii::app()->clientScript->registerLinkTag('alternate', null, $url, null, $option);
                if ($model->view->selected_type == 'product') {
                    $this->meta_tag_info = array(
                        'id' => $model->view->products_id,
                        'title' => $model->view->title,
                        'keywords' => $model->view->title,
                        'description' => MetaTagCom::getFirstParagraph($model->view->description, '<br>'),
                        'page_type' => 'products'
                    );
                } else {
                    $this->meta_tag_info = [
                        'title' => $model->view->meta_title . Yii::t('meta_tag', 'DATE_' . strtoupper(date("M")), ['{SYS_YEAR}' => date("Y")]),
                        'description' => $model->view->meta_description,
                        'keyword' => $model->view->meta_keyword
                    ];
                }
                $this->render('index', ['model' => $model->view]);
            }
        } catch (\Exception $e) {
            $message = Yii::t('page_content', 'TEXT_INVALID_PRODUCTS_CURRENCY_MESSAGE');
            if (Yii::app()->request->isAjaxRequest) {
                echo CJSON::encode(['error' => $message]);
            }
            throw $e;
        }
    }

    public function actionBuy()
    {
        Yii::app()->clientScript->registerMetaTag('noindex', 'robots');
        if (!isset($_REQUEST['products_id']) || !isset($_REQUEST['qty'])) {
            throw new CHttpException(300, 'Invalid Parameters');
        }
        $products_id = (int)$_REQUEST['products_id'];
        $qty = (int)$_REQUEST['qty'];
        $dtu = $_REQUEST['dtu'];
        $arr = [];
        parse_str($dtu, $arr);
        $error = '';
        $productsCom = new FrontendProductCom();

        $checkout_quantity_control = ProductsCheckoutSetting::getCheckoutQuantityLimit($products_id);

        $product['min_quantity'] = 1;
        $product['max_quantity'] = 100;
        if ($checkout_quantity_control) {
            if (!empty($checkout_quantity_control->min_checkout_quantity)) {
                $product['min_quantity'] = $checkout_quantity_control->min_checkout_quantity;
            }
            if (!empty($checkout_quantity_control->max_checkout_quantity)) {
                $product['max_quantity'] = $checkout_quantity_control->max_checkout_quantity;
            }
        }

        if ($productsCom->isProductOutOfStock(
                $products_id,
                $qty
            ) == 'OUT_OF_STOCK' || $product['min_quantity'] > $qty || $qty > $product['max_quantity']) {
            $error = Yii::t('page_content', 'ERROR_STOCK_NOT_AVAILABLE');
        }

        if (isset($arr['deliver_addr'])) {
            if (!array_key_exists('state', $arr['deliver_addr'])) {
                $error = Yii::t('page_content', 'ERROR_INVALID_ADDRESS_INFO');
            }

            foreach ($arr['deliver_addr'] as $key => $validate_item) {
                if (empty($validate_item) && !in_array($key, ['addr_2'])) {
                    $error = Yii::t('page_content', 'ERROR_INVALID_ADDRESS_INFO');
                    break;
                }
            }
            Yii::app()->session['physical_goods'] = $dtu;
        }

        if (isset($arr['game_info'])) {
            if (Yii::app()->user->isGuest) {
                $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
            } else {
                foreach ($arr['game_info'] as $key => $validate_item) {
                    if (empty($validate_item)) {
                        $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                        break;
                    }
                }
                $direct_topup_obj = new DirectTopupCom();
                $publishers_games_products_id = $products_id;
                $publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id);
                $games_mapping_row = PublishersGames::model()->getGameInfo(
                    $publishers_id,
                    $publishers_games_products_id
                );

                if (empty($games_mapping_row)) {
                    Yii::app()->slack->logError('Missing Direct Top Up Info', $_REQUEST);
                    $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                } else {
                    $get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);

                    $validate_game_acc_array = [
                        'game' => $games_mapping_row['publishers_game'],
                        'publishers_games_id' => $games_mapping_row['publishers_games_id'],
                        'product_id' => $products_id,
                        'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
                        'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
                        'product_code' => ($get_top_up_info_array['product_code']['top_up_info_value'] ?? ''),
                        'quantity' => $qty,
                    ];

                    if ($direct_topup_obj->validate_game_acc(
                        $publishers_id,
                        array_merge($arr['game_info'], $validate_game_acc_array),
                        $curl_response_array
                    )) {
                        Yii::app()->session['dtu'] = $dtu;
                    } else {
                        if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
                            $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                        } else {
                            $error = Yii::t('page_content', 'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT');
                        }
                    }
                }
            }
        }

        $params = ['pid' => $products_id, 'qty' => $qty];

        if (!empty($_REQUEST['sub_products_id'])) {
            $params['sub_products_id'] = $_REQUEST['sub_products_id'];
        }

        if (!empty($_REQUEST['account'])) {
            $params['account'] = $_REQUEST['account'];
        }

        echo CJSON::encode([
            'products_id' => $products_id,
            'qty' => $qty,
            'dtu_info' => $arr,
            'error' => $error,
            'redirect' => $this->createUrl('/checkout/buyNow/buy', $params)
        ]);
    }

    public function actionGetCharacterList()
    {
        Yii::app()->clientScript->registerMetaTag('noindex', 'robots');
        if (!isset($_REQUEST['products_id'])) {
            throw new CHttpException(300, 'Invalid Parameters');
        }
        $products_id = (int)$_REQUEST['products_id'];
        $dtu = $_REQUEST['dtu'];
        $arr = [];
        parse_str($dtu, $arr);
        $error = '';
        $characters_array = [];

        if (isset($arr['game_info'])) {
            if (Yii::app()->user->isGuest) {
                $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
            } else {
                foreach ($arr['game_info'] as $key => $validate_item) {
                    if (empty($validate_item)) {
                        $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                        break;
                    }
                }
                $direct_topup_obj = new DirectTopupCom();
                $publishers_games_products_id = $products_id;
                $publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id);
                $games_mapping_row = PublishersGames::model()->getGameInfo(
                    $publishers_id,
                    $publishers_games_products_id
                );

                if (empty($games_mapping_row)) {
                    Yii::app()->slack->logError('Missing Direct Top Up Info', $_REQUEST);
                    $error = Yii::t('page_content', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                } else {
                    $games_acc_array = array();
                    $direct_top_up_info = $arr['game_info'];

                    $games_acc_array['account'] = $direct_top_up_info['account'];
                    $games_acc_array['server'] = $direct_top_up_info['server'];
                    $games_acc_array['game'] = $games_mapping_row['publishers_game'];
                    $games_acc_array['publishers_games_id'] = $games_mapping_row['publishers_games_id'];

                    $curl_response_array = [];
                    $characters_array = $direct_topup_obj->get_character_list($publishers_id, $games_acc_array, $curl_response_array);

                    if (empty($characters_array)) {
                        $error = Yii::t('page_content', 'ERROR_DTU_NO_CHARACTERS_IN_LIST');
                    }
                }
            }
        }

        echo CJSON::encode([
            'character_list' => $characters_array,
            'error' => $error,
        ]);
    }

    public function actionReview()
    {
        $model = new BrandModel();
        if (!Yii::app()->request->isAjaxRequest || !isset($_REQUEST['page']) || !isset($_REQUEST['type']) || !isset($_REQUEST['url_alias'])) {
            throw new CHttpException(300, 'Invalid Parameters');
        }
        $page = (int)$_REQUEST['page'];
        $type = $_REQUEST['type'];
        if (!in_array($type, ['brand', 'category', 'product'])) {
            throw new Exception('Invalid Type');
        }
        $url_alias = $_REQUEST['url_alias'];
        $model->view->brand_url = $_REQUEST['url_alias'];
        $model->view->last_selected_url = $url_alias;
        $model->view->selected_type = $type;
        $model->view->getReview($page);
        $data['review'] = $this->renderPartial('_review', ['model' => $model->view], 1);
        echo CJSON::encode($data);
    }
}