<?php

class CategoryController extends FrontendController {

//    public $layout = '//layouts/gamepage';

    public function actionIndex() {
        $params = array(
            'cid' => isset($_REQUEST['cid']) ? $_REQUEST['cid'] : 0,
            'google_enabled' => $this->isServiceEnabled('google')
        );
        $model = new CategoryForm($params);
        
        $title = $model->getTitle();

        $meta_title_info = array(
            'iTunes (US)' => 'iTunes Gift Card [US] Online',
            'Playstation Network Card (US)' => 'PSN Card [US] Online',
            'Google Play (US)' => 'Google Play Gift Card [US] Online',
            'iTunes (UK)' => 'iTunes Gift Card [UK] Online',
            'Steam Wallet Card (US)' => 'Steam Wallet Card [US] Online',
            'Xbox Live (US)' => 'Xbox Live Card [US] Online',
            'World Of Warcraft (EU)' => 'World of Warcraft [EU] CD Key Online',
            'Steam Wallet (ASIA/Middle East)' => 'Steam Wallet Card [Asia/MENA] Online',
            'iTunes (Japan)' => 'iTunes Gift Card [JP] Online',
            'Nexon (EU)' => 'Nexon Cash Card [EU] Online',
            'Steam Wallet Card (UK)' => 'Steam Wallet Card [UK] Online',
            'World Of Warcraft (US)' => 'World of Warcraft [US] CD Key Online',
            'Playstation Network Card (UK)' => 'PSN Card [UK] Online',
            'Amazon (US) Gift Card' => 'Amazon Gift Card [US] Online',
            'Nintendo eShop (US)' => 'Nintendo eShop Cards [US] Online',
            'Guild Wars 2 (Global)' => 'Guild Wars 2 [Global] CD Key Online',
            'FF14: A Realm Reborn (US)' => 'Final Fantasy XIV: A Realm Reborn [US] CD Key Online',
            'Zynga' => 'Zynga Game Card & Credits Online',
            'Xbox Live Gold (Global)' => 'Xbox Live Gold [Global] Online',
            'Minecraft (US)' => 'Minecraft Game Card Online',
            'Battlenet (US)' => 'Battlenet Gift Card Online',
            'Amazon (UK) Gift Card' => 'Amazon Gift Card [UK] Online',
            'FF14: A Realm Reborn (EU)' => 'Final Fantasy XIV: A Realm Reborn [UK] CD Key Online',
            'Sony Online Entertainment (Global)' => 'Sony Online Entertainment | SOE Game Card Online',
            'Steam Wallet (SG)' => 'Steam Wallet Card [SG] Online',
            'League of Legends (NA)' => 'League of Legends | LOL Game Card Online',
            'Xbox Live (UK)' => 'Xbox Live Card [UK] Online',
            'iTunes (Australia)' => 'iTunes Gift Card [AU] Online',
            'Playstation Network Card (ID)' => 'PSN Card [ID] Online',
            'Facebook Credits (Global)' => 'Facebook Credits [Global] Online',
        );

        if (isset($meta_title_info[$title])) {
            $title = $meta_title_info[$title];
        }

        $this->meta_tag_info = array(
            'id' => $model->category_id,
            'title' => $title,
            'keywords' => SearchKeywords::model()->getKeywordInfo('search_value', $model->category_id, 0, Yii::app()->session['language_id'], Yii::app()->session['default_langugage_id']),
            'description' => MetaTagCom::getFirstParagraph($model->getDescription(), '<br>'),
            'page_type' => 'category'
        );

        $model->getProductReviews();
       
        $category_id = $model->category_id;

        $this->render('index', array('model' => $model));
    }

    public function actionGetSupportedGame($id) {
        $supported_games = Yii::app()->frontPageCom->getPageContentObj()->getCategorySupportedGames($id);
        foreach ($supported_games as $idx=>$game_array) {
            $link = Yii::app()->createUrl('game/index', array('gbid' => $game_array['id']));
            $supported_games[$idx]['url'] = $link;
        }
        echo CJSON::encode($supported_games);
    }



    // Uncomment the following methods and override them if needed
    /*
      public function filters()
      {
      // return the filter configuration for this controller, e.g.:
      return array(
      'inlineFilterName',
      array(
      'class'=>'path.to.FilterClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }

      public function actions()
      {
      // return external action classes, e.g.:
      return array(
      'action1'=>'path.to.ActionClass',
      'action2'=>array(
      'class'=>'path.to.AnotherActionClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }
     */
}
