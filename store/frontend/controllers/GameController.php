<?php

class GameController extends FrontendController
{
    // public $layout='//layouts/gamepage';

    public function actionIndex()
    {
        $model = new GameForm($_REQUEST);

        $title = $model->getTitle();

        $meta_title_info = array(
            'Age Of Kungfu (SEA)' => 'Age of Kungfu | AOK Cubits',
            'Atlantica Online (Global)' => 'Atlantica Online NX',
            'Planetside 2 (Global)' => 'Planetside 2 Station Cash',
            'League of Legends Garena' => 'League of Legends Garena Shells',
            'Perfect World (Global)' => 'Perfect World Gcoins',
        );

        if (isset($meta_title_info[$title])) {
            $title = $meta_title_info[$title];
        }

        $this->meta_tag_info = array(
            'id' => $model->game_blog_id,
            'title' => $title,
            'keywords' => SearchKeywords::model()->getKeywordInfo('search_value', $model->game_blog_id, 2, Yii::app()->session['language_id'], Yii::app()->session['default_langugage_id']),
            'description' => MetaTagCom::getFirstParagraph($model->getDescription(), '<br>'),
            'page_type' => 'game blog'
        );

        $this->render('index', array('model' => $model));
    }

    public function actionGetSupportedCategory($id)
    {
        $supported_games = Yii::app()->frontPageCom->getPageContentObj()->getGameBlogSupportedCategory($id);
        foreach ($supported_games as $idx => $game_array) {
            $link = Yii::app()->createUrl('category/index', array('cid' => $game_array['id']));
            $supported_games[$idx]['url'] = $link;
        }
        echo CJSON::encode($supported_games);
    }

    // Uncomment the following methods and override them if needed
    /*
    public function filters()
    {
        // return the filter configuration for this controller, e.g.:
        return array(
            'inlineFilterName',
            array(
                'class'=>'path.to.FilterClass',
                'propertyName'=>'propertyValue',
            ),
        );
    }

    public function actions()
    {
        // return external action classes, e.g.:
        return array(
            'action1'=>'path.to.ActionClass',
            'action2'=>array(
                'class'=>'path.to.AnotherActionClass',
                'propertyName'=>'propertyValue',
            ),
        );
    }
    */
}