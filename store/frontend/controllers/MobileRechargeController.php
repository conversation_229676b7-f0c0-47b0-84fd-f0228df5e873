<?php

class MobileRechargeController extends FrontendController
{
    public function actionIndex()
    {
        $this->meta_tag_info = array(
            'title' => 'Mobile Recharge',
            'page_type' => 'browse'
        );

        return $this->render('index');
    }

    public function actionValidatePhoneNum($prefix, $phone)
    {
        $prefix = (int)$prefix;
        $phone = (int)$phone;

        $return_arr = [
            'status' => false
        ];

        if ($prefix && $phone) {
            $data = (new MsProductModel)->validatePhoneAccount($prefix, $phone, Yii::app()->session['language_id']);

            if (is_array($data) && $data['status'] == true) {
                $return_arr = [
                    'status' => true,
                    'html_content' => $this->renderPartial('deno', ['operator_data' => $data], 1)
                ];
            }
        }

        echo json_encode($return_arr);
    }

    public function actionHidePhoneNum($prefix, $phone)
    {
        $prefix = (int)$prefix;
        $phone = (int)$phone;

        if ($prefix && $phone) {
            (new OrderModel())->hideMobileRechargeHistory($prefix, $phone);
        }

        return true;
    }
}