<?php

class ProductController extends FrontendController
{
    public function actionIndex()
    {
        $model = new ProductForm($_REQUEST);

        $title = $model->getTitle();

        $this->meta_tag_info = array(
            'id' => $model->product_id,
            'title' => $title,
            'keywords' => $title,
            'description' => MetaTagCom::getFirstParagraph($model->getDescription(), '<br>'),
            'page_type' => 'products'
        );

        $model->getProductReviews();

        if ($model->product_array['products_type'] == 2) {
            $this->render('game-product', array('model' => $model));
        } else {
            $this->render('index', array('model' => $model));
        }
    }

}