<?php

class ProductReviewController extends FrontendController
{
    public function actionIndex()
    {
        $type = (!isset($_REQUEST['type']) ? 'products' : $_REQUEST['type']);

        if($type == 'products'){
            $id = $_REQUEST['pid'];
        }else{
            $id = $_REQUEST['cid'];
        }

        $model = new ProductReviewForm($_REQUEST);

        $title = Yii::t('page_content', 'HEADER_PAGE_PRODUCT_REVIEW',array('{product_name}'=>$model->getTitle()));

        $this->meta_tag_info = array(
            'id' => $id,
            'title' => $title,
            'keywords' => $title,
            'description' => MetaTagCom::getFirstParagraph($model->getDescription(), '<br>'),
            'page_type' => 'promo'
        );

        if(isset($_REQUEST['page']))
        {
            if(!ctype_digit($_REQUEST['page']))
            {
                throw new CHttpException(404);
            }
            $page = (int)$_REQUEST['page'];
        }else{
            $page = 1;
        }
        if(isset($_REQUEST['sort']))
        {
            if(!ctype_digit($_REQUEST['sort']) || !in_array((int)$_REQUEST['sort'],[0,1,2,3]))
            {
                throw new CHttpException(404);
            }
            $sort = (int)$_REQUEST['sort'];
        }else{
            $sort = 1;
        }
        $limit = 9;

        $model->getProductReviews($page, $limit, $sort, $type);

        if(!empty($model->product_reviews) && !$model->product_reviews['blacklist']) {
            $this->render('index', array('model' => $model, 'type' => $type));
        }else{
            throw new CHttpException(404);
        }
    }

}