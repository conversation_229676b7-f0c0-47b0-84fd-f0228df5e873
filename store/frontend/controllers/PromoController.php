<?php

class PromoController extends FrontendController
{
    public function actionIndex()
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . PromoContent::model()->tableName() . '/promotion';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $model = $cache_result;
        } else {
            $model = PromoContent::getPromoContent();

            if (!$model) {
                $model = PromoContent::getDefaultPromo();
                if (!$model) {
                    $this->redirect('/');
                }
                $next_promo_start_time = PromoContent::getNextPromoStartTime();
                $cache_period = $next_promo_start_time - time();
            }
            else{
                $cache_period = $model->end_time - time();
            }
            
            Yii::app()->cache->set($cache_key, true, 3600);

            $model->processProductList();

            if($cache_period > 5){
                Yii::app()->cache->set($cache_key, $model, $cache_period);
            }
        }

        $this->meta_tag_info = array(
            'title' => $model->page_title,
            'page_type' => 'promo'
        );

        return $this->render('index', ['model' => $model]);
    }
}