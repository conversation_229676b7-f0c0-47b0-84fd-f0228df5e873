<?php

class RequestController extends FrontendController
{
    const TRIGGER_AUTHCOM = false;
    
    public function init() {
        parent::init(self::TRIGGER_AUTHCOM);
    }
    
	public function actionIndex() {
        exit;
		$this->render('index');
	}

    public function actionLogin() {
        $query_array = getSafeQueryArray();
        $category_id = isset($query_array['cid']) ? $query_array['cid'] : 0;
        $product_id = isset($query_array['pid']) ? $query_array['pid'] : 0;
        $delivery_method = isset($query_array['dtu']) ? 6 : 5;
        $snsType = isset($query_array['client']) ? $query_array['client'] : '';
        $shasso_url_extra_params = '';
        
        unset($query_array['client']);
        
        if ($category_id && $product_id) {
            $redirectNextUrl = Yii::app()->createAbsoluteUrl('category/index', array('cid' => $category_id, 'pid' => $product_id));
            unset($query_array['cid'], $query_array['pid']);
        } else if ($category_id) {
            $redirectNextUrl = Yii::app()->createAbsoluteUrl('category/index', array('cid' => $category_id));
            unset($query_array['cid']);
        } else if ($product_id) {
            $redirectNextUrl = Yii::app()->createAbsoluteUrl('product/index', array('pid' => $product_id, 'dm' => $delivery_method, 'pm' => 0));
            unset($query_array['pid']);
        } else {
            $redirectNextUrl = Yii::app()->createAbsoluteUrl('/');
        }
        
        if ($query_array) {
            $shasso_url_extra_params = http_build_query($query_array, null, '&');
        }
        
        SSOCom::SSOLogin(true, $redirectNextUrl, $snsType, $shasso_url_extra_params);
	}
    
    public function actionFeedDTU() {
        $query_array = getSafeQueryArray(array(), '', false);
        $next_url = $this->createAbsoluteUrl('/');
        
        $cookie_array = array();
        $gameID = (isset($query_array['gameID']) && is_numeric($query_array['gameID'])) ? $query_array['gameID'] : '';
        $dtu_server = isset($query_array['server']) ? htmlentities($query_array['server'], ENT_QUOTES, "utf-8") : '';
        $dtu_platform = isset($query_array['platform']) ? htmlentities($query_array['platform'], ENT_QUOTES, "utf-8") : '';
        $dtu_character = isset($query_array['character']) ? htmlentities($query_array['character'], ENT_QUOTES, "utf-8") : '';
        $dtu_account = isset($query_array['account']) ? htmlentities($query_array['account'], ENT_QUOTES, "utf-8") : '';
        
        if (notEmpty($gameID)) {
            $dtu_object = new DirectTopupCom();
            
            if ($dtu_object->is_customer_id_to_email_conversion_needed($gameID)) {
                if ($u_data = Yii::app()->customerCom->getCustomerData($dtu_account, 'email')) {
                    $email_address = $u_data['email'];
                }
                
                if (isset($email_address) && !empty($email_address)) $dtu_account = $email_address;
            }
            
            if (Yii::app()->frontPageCom->getCategoryObj()->isCategoryIDActive($gameID)) {
                if (notEmpty($dtu_server)) {
                    $cookie_array['server'] = $dtu_server;
                }

                if (notEmpty($dtu_platform)) {
                    $cookie_array['account_platform'] = $dtu_platform;
                }

                if (notEmpty($dtu_character)) {
                    $cookie_array['character'] = $dtu_character;
                }

                if (notEmpty($dtu_account)) {
                    $cookie_array['account'] = $dtu_account;
                }

                Yii::app()->getModule('checkout');
                $precheckout_form = new PreCheckoutForm();
                $precheckout_form->update_dtu_game_info(0, $cookie_array, $gameID);
                unset($precheckout_form);
                
                $next_url = Yii::app()->createUrl('category/index', array('cid' => $gameID));
            }
            
            Yii::app()->request->redirect($next_url);
        }
	}
    
    public function actionGetEventData() {
//        $promo_array = array();
//        $event = isset($_GET['e']) ? $_GET['e'] : '';
//        $event_list = array('BF15' => 'Black Friday 2015', 'CM15' => 'Cyber Monday 2015');
//        $event_name = isset($event_list[$event]) ? $event_list[$event] : $event_list['BF15'];
        $check_list = (isset($_GET['ws']) && Yii::app()->user->id == '325473') ? 1 : 0;
        
        $date = new DateTime();
        $date->setTimezone(new DateTimeZone('America/Los_Angeles'));  //America/Los_Angeles Asia/Kuala_Lumpur
        
        # configuration
        if ($check_list === 0) {
            # 21549 => 2522
            #738 => 2299
            $promo_config_array = array(
                'datetime' => $date->format('Y-m-d H:i:s'),   # This required to change to GMT - 8 !!!!
                'to_open_list' => array(
                    # Day 12
                    '2015-12-04 00:00:00' => array(4841, 4835, 4832, 4829, 21278),
                    # Day 11
                    '2015-12-03 00:00:00' => array(22749, 17884, 23210, 23260, 12715),
                    # Day 10
                    '2015-12-02 00:00:00' => array(20771, 22790, 17712, 21485, 4130),
                    # Day 9
                    '2015-12-01 00:00:00' => array(8601, 17880, 18295, 20573, 16059),
                    # Day 8
                    '2015-11-30 00:00:00' => array(17855, 22569, 20359, 20052, 22671),
                    # Day 7
                    '2015-11-29 00:00:00' => array(22661, 22660, 18181, 20994, 3456),
                    # Day 6
                    '2015-11-28 00:00:00' => array(22553, 21180, 2522, 2299, 21165),
                    # Day 5
                    '2015-11-27 00:00:00' => array(4444, 5236, 17710, 18261),
                    # Day 4
                    '2015-11-26 00:00:00' => array(3466, 19752, 6031, '21393-2', '21393-1'),
                    # Day 3
                    '2015-11-25 00:00:00' => array(5329, 5147, 18267, 21000, 21683),
                    # Day 2
                    '2015-11-24 00:00:00' => array(23208, 5164, 21704, 19148, 19149),
                    # Day 1
                    '2015-11-23 00:00:00' => array(6306, 21492, 19958, 23253, 17634, 22581, 17949, 20318, 21166, 21169, 21123, 21172, 20329, 19193),
                ),
                'custom_mapped' => array(
                    '6031' => array('link' => 'https://blog.offgamers.com/blog/black-friday-wtfast-20-off-promotion/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-WTFast-blog'),
                    '20775' => array('link' => 'https://blog.offgamers.com/blog/black-friday-sexy-three-kingdoms-offgamers-exclusive-promotion/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-SexyThreeKingdom-blog'),
                    '21393-1' => array('cid' => '21393', 'name' => '格斗宝贝 (GLOBAL) MGC', 'link' => 'https://blog.offgamers.com/blog/%E3%80%90%E6%A0%BC%E6%96%97%E5%AE%9D%E8%B4%9D%E3%80%91offgamers-black-friday-%E6%B4%BB%E5%8A%A8/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-Gedoubaobei-blog'),
                    '23253' => array('link' => 'https://blog.offgamers.com/blog/smartpixel-50-off/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-SmartPixel-blog'),
                    '6306' => array('link' => 'https://blog.offgamers.com/blog/rixty-black-friday-sweepstakes/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-RixtyGameCard-blog'),
                    '21393-2' => array('cid' => '21393', 'name' => '不败战神 (GLOBAL)', 'link' => 'https://blog.offgamers.com/blog/%E3%80%8A%E4%B8%8D%E8%B4%A5%E6%88%98%E7%A5%9E%E3%80%8B-%E5%B0%8A%E4%BA%AB%E7%A4%BC%E5%8C%85%E5%85%8D%E8%B4%B9%E6%B4%BE%E9%80%81%EF%BC%81/?utm_campaign=black-friday-campaign-bubaizhanshen&utm_source=web&utm_medium=referral'),
                )
            );
        } else {
            $dt = $date->format('Y-m-d H:i:s');
            $c = (int) $_GET['ws'];
            $promo_config_array = array(
                'custom_datetime' => strtotime($dt) + (60 * 60 * $c),   # This required to change to GMT - 8 !!!!
                'datetime' => $dt,   # This required to change to GMT - 8 !!!!
                'to_open_list' => array(
                    # Day 12
                    '2015-12-04 00:00:00' => array(4841, 4835, 4832, 4829, 21278),
                    # Day 11
                    '2015-12-03 00:00:00' => array(22749, 17884, 23210, 23260, 12715),
                    # Day 10
                    '2015-12-02 00:00:00' => array(20771, 22790, 17712, 21485, 4130),
                    # Day 9
                    '2015-12-01 00:00:00' => array(8601, 17880, 18295, 20573, 16059),
                    # Day 8
                    '2015-11-30 00:00:00' => array(17855, 22569, 20359, 20052, 22671),
                    # Day 7
                    '2015-11-29 00:00:00' => array(22661, 22660, 18181, 20994, 3456),
                    # Day 6
                    '2015-11-28 00:00:00' => array(22553, 21180, 2522, 2299, 21165),
                    # Day 5
                    '2015-11-27 19:00:00' => array(4444, 5236, 17710, 18261),
                    # Day 4
                    '2015-11-26 19:00:00' => array(3466, 19752, 6031, '21393-2', '21393-1'),
                    # Day 3
                    '2015-11-25 19:00:00' => array(5329, 5147, 18267, 21000, 21683),
                    # Day 2
                    '2015-11-24 19:00:00' => array(23208, 5164, 21704, 19148, 19149),
                    # Day 1
                    '2015-11-22 19:00:00' => array(6306, 21492, 19958, 23253, 17634, 22581, 17949, 20318, 21166, 21169, 21123, 21172, 20329, 19193),
                ),
                'custom_mapped' => array(
                    '6031' => array('link' => 'https://blog.offgamers.com/blog/black-friday-wtfast-20-off-promotion/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-WTFast-blog'),
                    '20775' => array('link' => 'https://blog.offgamers.com/blog/black-friday-sexy-three-kingdoms-offgamers-exclusive-promotion/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-SexyThreeKingdom-blog'),
                    '21393-1' => array('cid' => '21393', 'name' => '格斗宝贝 (GLOBAL) MGC', 'link' => 'https://blog.offgamers.com/blog/%E3%80%90%E6%A0%BC%E6%96%97%E5%AE%9D%E8%B4%9D%E3%80%91offgamers-black-friday-%E6%B4%BB%E5%8A%A8/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-Gedoubaobei-blog'),
                    '23253' => array('link' => 'https://blog.offgamers.com/blog/smartpixel-50-off/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-SmartPixel-blog'),
                    '6306' => array('link' => 'https://blog.offgamers.com/blog/rixty-black-friday-sweepstakes/?utm_campaign=black-friday-campaign&utm_source=web&utm_medium=referral&utm_content=GL-RixtyGameCard-blog'),
                    '21393-2' => array('cid' => '21393', 'name' => '不败战神 (GLOBAL)', 'link' => 'https://blog.offgamers.com/blog/%E3%80%8A%E4%B8%8D%E8%B4%A5%E6%88%98%E7%A5%9E%E3%80%8B-%E5%B0%8A%E4%BA%AB%E7%A4%BC%E5%8C%85%E5%85%8D%E8%B4%B9%E6%B4%BE%E9%80%81%EF%BC%81/?utm_campaign=black-friday-campaign-bubaizhanshen&utm_source=web&utm_medium=referral'),
                )
            );
        }
        
//        switch($event_name) {
//            case 'Black Friday 2015':
                $promo_array = $this->BlackFriday2015($promo_config_array);
//                break;
//        }

        $return_array = array(
            'config' => array(
                'tz' => $promo_config_array['datetime'],
                'a' => $promo_array['is_active'],
                't' => $promo_array['remain_ts'],   
                'df' => array(
//                    'l' => $this->createAbsoluteUrl('request/link', array('rid' => '')),
                )
            ),
            'data' => $promo_array['data']
        );
        
//        header("Access-Control-Allow-Origin: https://promo.offgamers.com");
//        header("Cache-Control: no-cache");
//        header("Cache-Control: post-check=0,pre-check=0");
//        header("Cache-Control: max-age=0");
//        header("Pragma: no-cache");
        header("content-type: text/javascript");
//        header("Content-Type: application/json");
        echo "angular.callbacks._0(".json_encode($return_array).")";
//        echo ")]}',\n" . json_encode($return_array);
    }
    
    private function BlackFriday2015($all_config) {
        $curr_time = isset($all_config['custom_datetime']) ? $all_config['custom_datetime'] : strtotime($all_config['datetime']);
        
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'event/BlackFridayCyberMonday/expiry_time';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $remain_time = 0;
            $next_ts = 0;
            $is_active = true;
            $show_hide_config = $all_config['to_open_list'];
            $custom_mapped_array = $all_config['custom_mapped'];
            $promo_category = array();
            $odd = 1;
            $new = 0;
            
            foreach ($show_hide_config as $start_date => $category_id_array) {
                $event_ts = strtotime($start_date);
                $odd = ($odd+1) % 2;
                
                if ($event_ts < $curr_time) {
                    $new += 1;
                    
                    foreach ($category_id_array as $cid) {
                        $this_cid = isset($custom_mapped_array[$cid]['cid']) ? $custom_mapped_array[$cid]['cid'] : $cid;
                        $category_product_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryInfoByGameID($this_cid);
                        $this_cid = isset($category_product_array[0]['categories_id']) ? $category_product_array[0]['categories_id'] : $this_cid;
                        $image_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesImage($this_cid, 1);

                        $promo_category[] = array(
                            "dc" => ($odd ? 'even' : 'odd'),
                            "d" => $this->getCustomMappedStr($cid, $custom_mapped_array, 'name'),
                            "c" => $this->getCustomMappedStr($cid, $custom_mapped_array, 'link'),
                            "i" => $image_array['dimension'][0],
                            "h" => $new === 1 ? 'hot' : '-',
                        );
                    }
                } else {
                    $next_ts = $event_ts;
                    $remain_time = $next_ts - $curr_time - 30;
                }
            }

            # First time list
            if (!$promo_category) {
                $get_first_id_array = array_pop($show_hide_config);
                $cid = $get_first_id_array[0];
                
                $this_cid = isset($custom_mapped_array[$cid]['cid']) ? $custom_mapped_array[$cid]['cid'] : $cid;
                $category_product_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoryInfoByGameID($this_cid);
                $this_cid = isset($category_product_array[0]['categories_id']) ? $category_product_array[0]['categories_id'] : $this_cid;
                $default_image_array = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesImage($this_cid, 1);
                $default_name = $this->getCustomMappedStr($cid, $custom_mapped_array, 'name');
                $default_link = $this->getCustomMappedStr($cid, $custom_mapped_array, 'link');
                $is_active = false;
                
                for ($i=0; $i < 15; $i++) {
                    $promo_category[] = array(
                        "dc" => 'even',
                        "d" => $default_name,
                        "c" => $default_link,
                        "i" => $default_image_array['dimension'][0],
                        "h" => '-',
                    );
                }
            } else if ($new == 12 && $remain_time == 0) {
                # deal over
                $remain_time = 30 * 24 * 60 * 60;
            }
            
            $return_array = array(
                'is_active' => $is_active,
                'next_ts' => $next_ts,
                'data' => $promo_category
            );

            if ($remain_time > 30) {
                Yii::app()->cache->set($cache_key, $return_array, $remain_time); // Cache for 30 days
            }
        }
        
        $return_array['remain_ts'] = $return_array['next_ts'] > 0 ? $return_array['next_ts'] - $curr_time : $return_array['next_ts'];
            
        return $return_array;
    }
    
    private function getCustomMappedStr($cid, $custom_mapped_array, $type) {
        $return_str = '';
        
        if (isset($custom_mapped_array[$cid][$type])) {
            $return_str = $custom_mapped_array[$cid][$type];
        } else {
            if ($type === 'link') {
                $page_url = Yii::app()->createAbsoluteUrl('category/index', array('cid' => $cid));
                $temp_arr = explode('/', $page_url);
                $campaign_key = '';
                
                if ($campaign_key = array_pop($temp_arr)) {
                    $campaign_key = '-' . $campaign_key;
                }
                
                $return_str = Yii::app()->createAbsoluteUrl('category/index', array('cid' => $cid)) . '?utm_campaign=black-friday-campaign' . $campaign_key . '&utm_source=web&utm_medium=referral';
            } else if ($type === 'name') {
                $return_str = Yii::app()->frontPageCom->getCategoryObj()->getName($cid, 1);
            }
        }
        
        return $return_str;
    }
    
    public function actionLink() {
        $get_array = getSafeQueryArray(array('rid'), $_GET);
        $id = isset($_GET['rid']) ? (int) $_GET['rid'] : 0; 
        $link = $this->createAbsoluteUrl("/");
        
        if ($id) {
            $type = $get_array['t'];    # id type
            unset($get_array['t']);
            
            switch ($type) {
                default:
                    $get_array['cid'] = $id;
                    $go_link = $this->createUrl('category/index', $get_array);

                    if (substr($go_link, 0, 15) !== '/category/index') {
                        $link = $go_link;
                    }
            }
        }
        
        $this->redirect($link);
    }
    
	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/
}