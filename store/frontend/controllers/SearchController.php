<?php
/**
 * @package YiiBoilerplate\Frontend
 */
class SearchController extends FrontendController
{
    public $layout = '//layouts-2022/main';
    public $theme = 2022;

    public function actionIndex() {
        $model =  new SearchForm($_REQUEST);
        if (Yii::app()->request->isAjaxRequest && (isset($_REQUEST['fk']) || isset($_REQUEST['o']) || isset($_REQUEST['fa']))) {
            $this->renderPartial('_product', array('model' => $model));
        }
        else{
            $this->setLocalizedUrl('search/index',$_REQUEST);
            $this->meta_tag_info = array(
                'title' => (!empty($model->keywords) ? $model->keywords : Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE')),
                'keywords' => $model->keywords,
                'description' => '',
                'page_type' => 'search'
            );
            $this->render('2022', array('model' => $model));
        }
    }

    public function actionGallery(){
        $this->setLocalizedUrl($_REQUEST['filter']);
        $model =  new SearchForm($_REQUEST);
        $this->meta_tag_info = array(
            'title' => (!empty($model->keywords) ? $model->keywords : Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE')),
            'keywords' => $model->keywords,
            'description' => '',
            'page_type' => 'search'
        );
        $this->render('gallery', array('model' => $model));
    }
    
    public function actionAjaxSuggestKeywords() {
        if (Yii::app()->request->isAjaxRequest) {
            $s_obj = new SearchForm($_REQUEST, true);
            echo CJSON::encode($s_obj->getKeywordResults());
            
            unset($s_obj);
        }
    }
    
    public function actionAjaxPreFectchSuggestKeywords() {
        if (Yii::app()->request->isAjaxRequest) {
            $s_obj = new SearchForm($_REQUEST);
            echo CJSON::encode($s_obj->getAllKeywordResults());
            
            unset($s_obj);
        }
    }
    
    public function actionError() {
        $this->layout = '//layouts/default';
        $this->render('error');
    }
}