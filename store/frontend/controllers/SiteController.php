<?php

/**
 * @package YiiBoilerplate\Frontend
 */
class SiteController extends FrontendController
{
    public $layout = '//layouts-2022/main';
    public $model = '';

    public function actions()
    {
        return [
            'page' => [
                'class' => 'CViewAction',
                'layout' => '//layouts/default',
            ],
        ];
    }
    
    public function actionGetUser()
    {
        $authbar_data = $this->getUserBarWidgetContent();
        if(isset($authbar_data["REGIONAL_SETTING"])){
            unset($authbar_data["REGIONAL_SETTING"]);
        }
        echo json_encode($authbar_data);
    }

    public function actionGetProductList()
    {
        $products_list = [];
        $cat_id = isset($_REQUEST["cat_id"]) ? (int)$_REQUEST["cat_id"] : 0;
        if($cat_id > 0){
            $model = new BrandModel();
            $model->getSupportedProductListByCat($cat_id);
            $products_list = $model->view->product_list;
        }
         echo json_encode($products_list);
    }

    public function actionIndex()
    {
        // $this->setLocalizedUrl('/');
        // $this->theme = 2022;
        // $this->meta_tag_info = [
        //     'page_type' => 'home',
        // ];
        // $model = new FrontPageForm($_REQUEST);
        // $this->model = $model;
        // $this->render('index', ['model' => $model]);
        $this->layout = '//layouts-2022/main_vue';
        $this->render('//vue/index');
    }

    public function actionError()
    {
        $this->layout = '//layouts/default';

        if ($error = Yii::app()->errorHandler->error) {
            if ($error['code'] != 300) {
                $error['url_referrer'] = (isset(Yii::app()->request->urlReferrer) ? Yii::app()->request->urlReferrer : '-');
                $error['user_id'] = (isset(Yii::app()->user->id) ? Yii::app()->user->id : '-');
                $error['user_ip'] = Yii::app()->frontPageCom->getIPAddress();
                $error['message'] = '';
            }

            if ($error['message'] != 'ERROR_REGION_RESTRICTION') {
                $error['message'] = Yii::t('page_content', $error['message']);
                $this->render('error', $error);
            } elseif ($error['message'] == 'ERROR_IPCOUNTRY_RESTRICTION') {
                $error['message'] = Yii::t('page_content', $error['message']);
                $error['blockedCountry'] = true;
                $this->render('error_restrict', $error);
            } else {
                $error['message'] = Yii::t('page_content', $error['message']);
                $this->render('error_restrict', $error);
            }
        } elseif ($code = Yii::app()->customerCom->isPhoneSanctionCountry()) {
            $this->render('error_sanction', ['country_code' => $code]);
        } else {
            $this->redirect(Yii::app()->createAbsoluteUrl('/'));
        }
    }

    public function registerTag($param)
    {
        $match = addcslashes($param, '%_');

        $q = new CDbCriteria([
            'condition' => "rule_url LIKE :match",
            'params' => [':match' => "%$match%"],
        ]);

        $data_rules = RatingRulesBase::model()->with('rulesTags', 'allTags')->findAll($q);

        return $data_rules;
    }

    public function actionWebpush()
    {
        echo CJSON::encode(WebpushCom::setWebpushToken($_REQUEST['token']));
    }

    public function actionSetTabSession()
    {
        Yii::app()->session['tab_vis'] = 1;
    }

    public function actionGetTabSession()
    {
        echo((isset(Yii::app()->session['tab_vis'])) ? CJSON::encode(Yii::app()->session['tab_vis']) : CJSON::encode(
            0
        ));
    }

    public function actionTermsOfService()
    {
        $this->layout = '//layouts/default';
        $staticPageCom = new StaticPageCom();
        $staticPageCom->_init();
        // Id = 1 for term and condition page

        $page_type = "TNC";
        if (isset($_REQUEST['view'])) {
            switch ($_REQUEST['view']) {
                case "advertising-policy":
                    $page_type = "SGADS";
                    if(strtoupper(Yii::app()->session['country_code']) == "MY"){
                        $page_type = "MYADS";
                    } 
                    break;
                case "refund-policy":
                    $this->meta_tag_info = [
                        'title' => 'Refund and Exchange Policy | OffGamers Online Game Store',
                        'keyword' => 'Refund and exchange policy, OffGamers Refund policy',
                        'description' => 'Shop confidently at OffGamers. Learn more about our comprehensive Refund & Exchange policy today!'
                    ];
                    $page_type = "RFUND";
                    break;
                case "privacy-policy":
                    $this->meta_tag_info = [
                        'title' => 'Terms of Service | OffGamers Online Game Store',
                        'keyword' => 'Terms of service, Offgamers Terms of service, Terms And Conditions',
                        'description' => 'Explore our Terms of Service today to ensure a seamless and enjoyable experience while utilising our services.'
                    ];
                    $page_type = "PP";
                    break;
                default:
                    $this->meta_tag_info = [
                        'title' => 'Terms of Service | OffGamers Online Game Store',
                        'keyword' => 'Terms of service, Offgamers Terms of service, Terms And Conditions',
                        'description' => 'Explore our Terms of Service today to ensure a seamless and enjoyable experience while utilising our services.'
                    ];
                    $page_type = "TNC";
                    break;
            }
        }

        if ($page_type == "TNC") {
            $lang = (Yii::app()->session['language'] == 'cn') ? 'zh-CN' : Yii::app()->session['language'];
            $viewPath = 'pages/' . $lang . '/tos';
            $this->render($viewPath);

        } elseif ($page_type == "PP") {
            $lang = (Yii::app()->session['language'] == 'cn') ? 'zh-CN' : Yii::app()->session['language'];
            $viewPathSuffix = "";
            if(strtoupper(Yii::app()->session['country_code']) == "MY") {
                $viewPathSuffix = "-my";
            }
            $viewPath = 'pages/' . $lang . '/privacy-policy' . $viewPathSuffix;
            $this->render($viewPath);
        
        } elseif ($staticPageContent = $staticPageCom->getStaticPageContent($page_type)) {
            $this->render('terms-of-service', ['content' => $staticPageContent]);
        } else {
            $this->redirect(Yii::app()->createAbsoluteUrl('/'));
        }
    }

    public function actionRefundPolicy()
    {
        $this->layout = '//layouts/default';
        $staticPageCom = new StaticPageCom();
        $staticPageCom->_init();
        // Id = 1 for term and contdition page
        if ($staticPageContent = $staticPageCom->getStaticPageContent('RFUND')) {
            $this->render('terms-of-service', ['content' => $staticPageContent]);
        } else {
            $this->redirect(Yii::app()->createAbsoluteUrl('/'));
        }
    }

    public function actionPrivacyPolicy()
    {
        $this->layout = '//layouts/default';
        
        $privacy_policy_page = 'privacy-policy';
        if(strtoupper(Yii::app()->session['country_code']) == "MY"){
            $privacy_policy_page = privacy_policy_page + '-my';
        }
        $this->render($privacy_policy_page, ['content' => $staticPageContent]);
    }

    public function actionAdvertisingPolicy()
    {
        $this->layout = '//layouts/default';
        $staticPageCom = new StaticPageCom();
        $staticPageCom->_init();
        // Id = 1 for term and contdition page
        $page_type = "SGADS";
        if(strtoupper(Yii::app()->session['country_code']) == "MY"){
            $page_type = "MYADS";
        } 
        if ($staticPageContent = $staticPageCom->getStaticPageContent($page_type)) {
            $this->render('terms-of-service', ['content' => $staticPageContent]);
        } else {
            $this->redirect(Yii::app()->createAbsoluteUrl('/'));
        }
    }
}
