<?php

/**
 * @package YiiBoilerplate\Frontend
 */
class SlsController extends CController
{
    public $input = array();
    public $result = array();
    public $status = false;
    public $code = 404;
    public $error;

    protected function beforeAction($action) {
        $api_obj = new ApiGeneralCom();

        $this->input = $api_obj->_readInput();
        // if(isset( $this->input["access_secret"]) && $this->input["access_secret"] == Yii::app()->params['sls_access_secret']){
        if(isset( $this->input["access_secret"]) && $this->input["access_secret"] == "2234"){
            return true;
        } else {
            $this->error = 'Invalid signature';
        }

        $api_obj->_sendResponse(array(
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error
        ));
        return false;
    }

    protected function afterAction($action) {
        $api_obj = new ApiGeneralCom();

        $api_obj->_sendResponse(array(
            'status' => $this->status,
            'code' => $this->code,
            'result' => $this->result,
            'error' => $this->error,
        ));
        return true;
    }

    public function init() {
        
    }

    public function actionGetGameSetting()
    {
        if (isset($this->input['products_id']) && $this->input['products_id']) {
            $products_id = (int) $this->input['products_id'];
            $productData = Products::model()->findByPk($products_id);
            if($productData){
                $result = CategoriesBrandList::model()->getGameSetting($productData);
                if($result === false){
                    $this->status = 200;
                    $this->code = 4041;
                    $this->result = false;
                } else {   
                    $this->status = 200;
                    $this->result = $result;
                }
            }

        } else {
            $this->error = 'Missing ID';
        }

    }

    public function actionGetTopUpInfo()
    {
        if (isset($this->input['top_up_info_id']) && $this->input['top_up_info_id']) {
            $top_up_info_id = (int) $this->input['top_up_info_id'];
            $topupInfo = TopUpInfoBase::model()->findByPk($top_up_info_id);
            if($topupInfo){
                $languages = LanguagesBase::model()->findAll();
				$language_list = [];
				if($languages){
					foreach($languages as $value){
						$language_list[$value->languages_id] = strtolower($value->code);
					}
				}
                $result = $topupInfo->attributes;
                $result["lang"] = [];
                $topupInfoLang = TopUpInfoLangBase::model()->findAllByAttributes(array("top_up_info_id" =>$top_up_info_id));
                foreach($topupInfoLang as $topupInfoLangValue){
                    if(isset($language_list[$topupInfoLangValue->languages_id])){
                        $result["lang"][$language_list[$topupInfoLangValue->languages_id]] = $topupInfoLangValue->top_up_info_display;
                    }
                }
        
                $this->status = 200;
                $this->result = $result;
            }

        } else {
            $this->error = 'Missing ID';
        }
    }

}
