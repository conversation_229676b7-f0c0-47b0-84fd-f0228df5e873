<?php

class SmsTokenController extends FrontendController {

    public function filters() {
        return array(
            'accessControl', // perform access control for CRUD operations
        );
    }

    public function accessRules() {
        /**
         * @ : authenticated user
         * ? : anonymous user
         * * : any user
         */
        return array(
            array('deny',
                'users' => array('?'),
            ),
        );
    }

	public function actionIndex(){
//		echo $this->render('securityQuestionPopupHtml', array('index' => $index, 'requestType' => 'security_token_request'), true, true);
	}
	
    public function actionRequestSmsToken() {
        $p = new CHtmlPurifier();
        $modelCustomers = new Customers();
        
        $resCode = 0;
        $resText = '';
        
        $phone = '';
        $country = 0;
        $phone_last_four_digit = '';
        
        
        $phoneExist = 1;
        $seconds_counting_down = 0; 
        $action = isset($_POST['requestType']) ? $_POST['requestType'] : '';
        
        if (isset($_POST['fdigit'])) {
            $phone_last_four_digit = $p->purify($_POST['fdigit']);
            $phone_last_four_digit = (int) $phone_last_four_digit;
        }
        
        if (isset($_POST['phone'])) {
            $phone = $p->purify($_POST['phone']);
        }

        if (isset($_POST['country'])) {
            $country = $p->purify($_POST['country']);
        }

        if ($phone == '' || $country == 0) {
            $field = array('customers_telephone');
            $cond = array('customers_id' => Yii::app()->user->id);
            $customerPhone = $modelCustomers->getCustomerByField($field, $cond);
            
            if ((isset($customerPhone->customers_telephone) && $customerPhone->customers_telephone == '')||!isset($customerPhone->customers_telephone)) {
                $phoneExist = 0;
            }
        }
        
        if ($phoneExist) {
            switch ($action) {
                case 'request_email_token':
                    if ($requestResult = Yii::app()->customerCom->requestSecurityTokenByLastPhoneDigit($phone_last_four_digit, Yii::app()->user->id, Yii::app()->session['language'])) {
                        $resText = $requestResult['message'];
                        $resToken = $requestResult['token'];
                        
                        if ($resCode = $requestResult['status'] && $resToken) {
                            $classCustomerSecurityComponent = new CustomerSecurityCom();
                            
                            if ($respone = $classCustomerSecurityComponent->sendTokenViaEmail($resToken)) {
                                // success
                                $resText = sprintf($resText, $respone['email']);
                            } else {
                                $resCode = 0;
                                $resText = '';
                            }
                        }
                    }
                    
                    break;
                case 'request_resend_sms_token':
                    if ($requestResult = Yii::app()->customerCom->requestResendSecurityToken(Yii::app()->user->id, Yii::app()->session['language'])) {
                        $seconds_counting_down = $requestResult['seconds_till_resend'] > 0 ? $requestResult['seconds_till_resend'] : 0;
                        $resCode = (int) $requestResult['res_code'] > 0 ? $requestResult['res_code'] : 0;
                        $resText = $requestResult['res_text'];
                    }
                    break;
                case 'request_sms_token':
                    if ($requestResult = Yii::app()->customerCom->requestSecurityToken(Yii::app()->user->id, Yii::app()->session['language'])) {
                        $seconds_counting_down = $requestResult['seconds_till_resend'] > 0 ? $requestResult['seconds_till_resend'] : 0;
                        $resCode = (int) $requestResult['res_code'] > 0 ? $requestResult['res_code'] : 0;
                        $resText = $requestResult['res_text'];
                    }
                    break;
                case 'request_resend_verifying_token':
                    if ($requestResult = Yii::app()->customerCom->requestResendPhoneVerifyingToken(Yii::app()->user->id, Yii::app()->session['language'])) {
                        $seconds_counting_down = $requestResult['seconds_till_resend'] > 0 ? $requestResult['seconds_till_resend'] : 0;
                        $resCode = (int) $requestResult['res_code'] > 0 ? $requestResult['res_code'] : 0;
                        $resText = $requestResult['res_text'];
                    }
                    break;
            }
        } else {
            $resText = Yii::t('smsToken', 'TEXT_PLEASE_SETUP_MOBILE', array(
                '{EDIT_PHONE_URL}' => Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/profile/index'
            ));
        }

        if ($resCode) {
            $status_icon = Yii::app()->frontPageCom->getMainUIStaticIconURL('success.gif');
        } else {
            $status_icon = Yii::app()->frontPageCom->getMainUIStaticIconURL('error.gif');
            
            if (!$resText) {
                $resText = Yii::t('page_content', 'ERROR_GENERAL');
            }
        }
        
        echo CJSON::encode(array(
            'resCode' => $resCode,
            'message' => '<img src="' . $status_icon . '"> ' . $resText,
            'seconds' => $seconds_counting_down
        ));
    }

}