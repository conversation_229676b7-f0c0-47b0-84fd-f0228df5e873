<?php

class SsoController extends CController {

    //use by shassa splash page
    public function actionSetID() {
        header('P3P:CP="This is not a P3P policy! See http://http://www.gamernizer.com/site/page?view=en.tos for more info"');
        
        //plant cookie from SSO
        if (isset($_GET['S3ID'])) {
            $cookie = new CHttpCookie('S3ID', $_GET['S3ID']);
            $cookie->expire = time() + Yii::app()->params['SHASSO_CONFIG']['SSO_S3ID_COOKIE_DURATION'];
            $cookie->path = '/';
            Yii::app()->request->cookies['S3ID'] = $cookie;
            if (isset($_GET['S3RM'])) {
                $cookie = new CHttpCookie('S3RM', $_GET['S3RM']);
                $cookie->expire = time() + Yii::app()->params['SHASSO_CONFIG']['SSO_S3RM_COOKIE_DURATION'];
                $cookie->path = '/';
                Yii::app()->request->cookies['S3RM'] = $cookie;
            }
        }
    }

    //use by shassa splash page
    public function actionDeleteID() {
        header('P3P:CP="This is not a P3P policy! See http://http://www.gamernizer.com/site/page?view=en.tos for more info"');
        
        $this->_doLogout();
    }
    
    public function actionKickUser() {
        $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : "";
        
        if (!empty($_data) && isset($_data["user_id"])) {
            if ($_data["signature"] == md5($_data["user_id"] . "|" . Yii::app()->params["SHASSO_CONFIG"]["CLIENT_SECRET"])) {
                // Kick User N/A after move to redis session
                // OgmYiiSession::model()->deleteAll("user_id = " . $_data["user_id"]);
            }
        }
    }

    public function actionClearRegionCache() {
        $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : $_GET;

        if (!empty($_data) && isset($_data["cid"])) {
            $array = ['cid', 'timestamp', 'dialing_code', 'phone_country_code'];
            $s = "";
            foreach ($array as $key => $value) {
                if (isset($_data[$value])) {
                    $s .= $_data[$value] . '|';
                }
            }
            $s .= Yii::app()->params["SHASSO_CONFIG"]["CLIENT_SECRET"];

            if ($_data["hash"] == md5($s)) {
                RegionalSettingCom::regionalCountryCache('d', ['uid' => $_data["cid"]]);
            }
        }
    }
    
    public function actionSignUp() {
        Yii::app()->session->offsetUnset('country');
        Yii::app()->session->offsetUnset('country_code');
        Yii::app()->params["regionalSettingChecked"] = false;
        RegionalSettingCom::checkRegionalSetting();
        if (isset($_GET['next_url']) && !empty($_GET['next_url'])) {
            $redirectNextUrl = $_GET['next_url'];
        } else {
            $redirectNextUrl = $this->createAbsoluteUrl("/");
        }
        
        $shassoSignUpUrl = Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . "/sso/index?action=signup&origin=" . urlencode(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']) . "&service=" . Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'] . "&hl=" . Yii::app()->language . "&next_url=" . urlencode($redirectNextUrl);

        $this->redirect($shassoSignUpUrl);
    }

    //Portal click login action
    public function actionLogin() {
        Yii::app()->session->offsetUnset('country');
        Yii::app()->session->offsetUnset('country_code');
        Yii::app()->params["regionalSettingChecked"] = false;
        RegionalSettingCom::checkRegionalSetting();
        $snsType = '';
        if (isset($_GET['sns']) && !empty($_GET['sns'])) {
            $snsType = CHtml::encode($_GET['sns']);
        }

        if (isset($_GET['next_url']) && !empty($_GET['next_url'])) {
            $redirectNextUrl = $_GET['next_url'];
        } else {
            $redirectNextUrl = $this->createAbsoluteUrl("/");
        }

        SSOCom::SSOLogin(true, $redirectNextUrl, $snsType);
    }

    //Portal click logout action
    public function actionLogout() {
        //portal logout
        if (isset($_GET['next_url']) && !empty($_GET['next_url'])) {
            $redirectNextUrl = $_GET['next_url'];
        } else {
            $redirectNextUrl = $this->createAbsoluteUrl("/");
        }

        if (Yii::app()->user->isGuest) {
            if (domainMapping($redirectNextUrl) === TRUE) {
                $this->redirect($redirectNextUrl);
            } else {
                $this->redirect(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']);
            }
        } else {
            $this->_doLogout();
            //redirect to shasso logout
            $shassoLoginUrl = Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . "/sso/index?action=logout&origin=" . urlencode(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']) . "&service=" . Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'] . "&hl=" . Yii::app()->language . "&next_url=" . urlencode($redirectNextUrl);
            $this->redirect($shassoLoginUrl);
        }
    }

    public function actionChangeLocation() {
        if (isset($_GET['next_url']) && !empty($_GET['next_url'])) {
            $next_url = $_GET['next_url'];
        } else {
            $next_url = $this->createAbsoluteUrl("/");
        }

        if (Yii::app()->user->id) {
            RegionalSettingCom::regionalCountryCache('d', ['uid' => Yii::app()->user->id]);
            Yii::app()->session->offsetUnset('country');
            Yii::app()->session->offsetUnset('country_code');
            Yii::app()->params["regionalSettingChecked"] = false;
        }
        $shassoLoginUrl = Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . "/profile/security?next_url=" . urlencode($next_url) . "&origin=" . urlencode(Yii::app()->params['SHASSO_CONFIG']['PORTAL_ORI_URI']) . "&service=" . Yii::app()->params['SHASSO_CONFIG']['CLIENT_ID'] . "&hl=" . Yii::app()->language . "#change-phone";
        $this->redirect($shassoLoginUrl);
    }

    //sharing logout function
    private function _doLogout() {
        Yii::app()->user->logout();
        //unset(Yii::app()->request->cookies['S3ID']);
        //unset(Yii::app()->request->cookies['S3RM']);
        
        $cookie = new CHttpCookie("S3ID", "");
        $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
        $cookie->expire = time() - Yii::app()->params['SSO_S3ID_COOKIE_DURATION'];
        $cookie->path = '/';
        Yii::app()->request->cookies["S3ID"] = $cookie;

        $cookie = new CHttpCookie("S3RM", "");
        $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
        $cookie->expire = time() - Yii::app()->params['SSO_S3RM_COOKIE_DURATION'];
        $cookie->path = '/';
        Yii::app()->request->cookies["S3RM"] = $cookie;

        $cookie = new CHttpCookie("webpush", "");
        $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
        $cookie->expire = time() - 1;
        $cookie->path = '/';
        Yii::app()->request->cookies["webpush"] = $cookie;
    }

}