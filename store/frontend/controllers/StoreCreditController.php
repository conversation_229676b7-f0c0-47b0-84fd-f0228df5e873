<?php

class StoreCreditController extends FrontendController
{

    const SESSION_KEY_GC_REDEEM_RESULT = 'store-credit/redeem-gift-card-success/redeem_result';

    public $menu = 'LINK_SC_TOPUP';

    public function filters()
    {
        return array(
            'accessControl',
            // perform access control for CRUD operations
        );
    }

    public function accessRules()
    {
        /**
         * @ : authenticated user
         * ? : anonymous user
         * * : any user
         */
        return array(
            array(
                'allow',
                'actions' => array(
                    'index',
                    'redeemGiftCard',
                    'verifyGiftCard',
                    'redeemGiftCardSuccess',
                    'statement'
                ),
                'users' => array('@')
            ),
            array(
                'deny',
                'users' => array('*'),
                'deniedCallback' => array(
                    $this,
                    'redirectToLogin'
                ),
            ),
        );
    }

    public function actionIndex()
    {
        $model = new MyOffgamersForm();

        // Check MS status and Customer Store Credit Account status
        $scSiteStatus = Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'];
        if ($scSiteStatus == 0) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_MAINTENANCE')
            ));
        }
        $scStatus = StoreCreditCom::storeCreditBalance();
        if ($scStatus['status'] == false) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_DISABLE')
            ));
        }

        // Init Customer Store Credit Account
        $f_data = $this->initCustomerStoreCredit(isset($_POST['customcurcode']) ? $_POST['customcurcode'] : null);
        $cur_code = $f_data['cur_code'];
        // check if country blocked
        if (Yii::app()->customerCom->isPhoneSanctionCountry()) {
            if (Yii::app()->request->isPostRequest && $cur_code) {
                Yii::app()->user->setFlash('error', Yii::t('storeCredit', 'ERROR_BLOCKED_COUNTRY'));
            }
            return $this->redirect('/site/error');
        }

        $_max_pending_num = ConfigurationCom::getValue('B2C_MAX_NUM_OF_PENDING');
        $_max_pending_min = ConfigurationCom::getValue('B2C_MAX_PENDING_WITHIN_MIN');

        if (!empty($_max_pending_num)) {
            $_current_pending = OrdersBase::model()->getPendingOrderCount(Yii::app()->user->id, $_max_pending_min);
            if ($_current_pending >= $_max_pending_num) {
                if (Yii::app()->request->isPostRequest && $cur_code) {
                    Yii::app()->user->setFlash('error', sprintf(Yii::t('storeCredit', 'ERROR_MAX_PENDING_ORDER'), $_current_pending));
                    return $this->redirect('/account/store-credit/index');
                }
            }
        }

        // Aditional checking for location restriction currency
        if ($currRestrictionArray = json_decode(RegionalSettingCom::getExcludedCurrencyJSON())) {
            if (in_array($cur_code, $currRestrictionArray)) {
                return $this->render('location-restriction', array(
                    'model' => $model,
                    'page' => 'topup',
                    'title' => Yii::t('page_content', 'TEXT_TOPUP')
                ));
            }
        }

        $sc_obj = new StoreCreditCom();
        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();

        $f_data['currencies'] = $this->getAllowedCurrencies($sc_obj);

        // top-up checkout
        if (Yii::app()->request->isPostRequest && $cur_code) {
            $customqty = isset($_POST['customqty']) ? (int)$_POST['customqty'] : 0;
            if ($customqty != 0 && $customqty >= $f_data['currencies'][$cur_code]['min_topup'] && $f_data['currencies'][$cur_code]['max_topup'] >= $customqty) {
                $pid = Categories::model()->storeCreditProductID(Yii::app()->session['customers_groups_id']);
                if ($pid) {
                    CustomersScCart::model()->removeCart();
                    DbHelper::insertIgnore('customers_sc_cart', [
                        'customers_id' => Yii::app()->user->id,
                        'products_id' => $pid,
                        'customers_sc_cart_quantity' => $customqty,
                        'customers_sc_cart_date_added' => new CDbExpression('NOW()'),
                        'customers_sc_cart_currency' => $cur_code
                    ]);

                    return $this->redirect('/checkout/buyNow/confirmCheckoutSC');
                } else {
                    Yii::app()->user->setFlash('error', Yii::t('storeCredit', 'ERROR_SC_NOT_AVAILABLE'));
                }
            } else {
                Yii::app()->user->setFlash('error', Yii::t('storeCredit', 'ERR_MSG_ENTER_INVALID_SC_AMOUNT'));
            }
        }

        // top-up success
        if (isset($_GET["tid"]) && !empty($_GET["tid"])) {
            $customers_id = Yii::app()->user->id;
            $select = " SELECT o.orders_id
                        FROM " . Orders::model()->tableName() . " AS o
                        INNER JOIN " . OrdersProducts::model()->tableName() . " AS op
                            ON op.orders_id = o.orders_id AND op.custom_products_type_id = " . StoreCreditCom::PRODUCT_TYPE_ID_STORE_CREDIT . "
                        WHERE o.orders_id = :orders_id
                            AND o.customers_id = :customers_id";
            $command = Yii::app()->db->createCommand($select);
            $command->bindParam(":orders_id", $_GET["tid"], PDO::PARAM_INT);
            $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);

            if ($order = $command->queryScalar()) {
                $topup_amt = '';
                Yii::app()->getModule('order');
                $order_obj = new OrderCom(array('order_id' => $_GET["tid"]));
                foreach ($order_obj->products as $product) {
                    if ($product['custom_products_type_id'] == StoreCreditCom::PRODUCT_TYPE_ID_STORE_CREDIT) {
                        $unit_price = bcmul($product['storage_price']['final_price'], $order_obj->info['currency_value'], 8);
                        if ($product['quantity'] > 1 && $order_obj->info['currency'] !== 'USD') {
                            $unit_price = round($unit_price);
                        }
                        if (!empty($product['sc_currency']) && $product['sc_currency'] != $order_obj->info['currency']) {
                            $sc_cur = $product['sc_currency'];
                            $unit_price = 1;
                        } else {
                            $sc_cur = $order_obj->info['currency'];
                        }
                        // Set Quantity as 1 and display total price
                        $total_price = Yii::app()->currency->roundByCurrency($sc_cur, $unit_price * $product['quantity']);
                        $topup_amt = $cur_obj->format($total_price, false, $sc_cur, '', null, ' ');
                        break;
                    }
                }

                $sc_bal = StoreCreditCom::storeCreditBalance(true);
                return $this->render('topup-success', [
                    'model' => $model,
                    'tid' => $_GET["tid"],
                    'topup_amt' => $topup_amt,
                    'new_bal' => $sc_bal['amount'],
                ]);
            }
        }

        // top-up error
        if (isset($_GET['topup_error'])) {
            $m_csc_obj = CustomersScCart::model()->findByPk(Yii::app()->user->id);
            if (!empty($m_csc_obj->customers_sc_cart_last_message)) {
                Yii::app()->user->setFlash('error', $m_csc_obj->customers_sc_cart_last_message);
                $m_csc_obj->customers_sc_cart_last_message = '';
                $m_csc_obj->save();
            }
        }

        return $this->render('index', array(
            'model' => $model,
            'fdata' => $f_data,
            'scSiteStatus' => $scSiteStatus,
            'scStatus' => $scStatus['status']
        ));
    }

    public function actionRedeemGiftCard()
    {
        $model = new MyOffgamersForm();

        // Check SC status for this user
        $scSiteStatus = Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'];
        if ($scSiteStatus == 0) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_MAINTENANCE')
            ));
        }
        $scStatus = StoreCreditCom::storeCreditBalance();
        if ($scStatus['status'] == false) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_DISABLE')
            ));
        }

        // Init Customer Store Credit Account
        $f_data = $this->initCustomerStoreCredit(isset($_POST['customcurcode']) ? $_POST['customcurcode'] : null);
        $cur_code = $f_data['cur_code'];
        // check if country blocked
        if (Yii::app()->customerCom->isPhoneSanctionCountry()) {
            if ($f_data['valid_sc'] && Yii::app()->request->isPostRequest && $cur_code) {
                Yii::app()->user->setFlash('error', Yii::t('storeCredit', 'ERROR_BLOCKED_COUNTRY'));
            }
            return $this->redirect('/site/error');
        }

        // Aditional checking for location restriction currency
        if ($currRestrictionArray = json_decode(RegionalSettingCom::getExcludedCurrencyJSON())) {
            if (in_array($cur_code, $currRestrictionArray)) {
                return $this->render('location-restriction', array(
                    'model' => $model,
                    'page' => 'redeem',
                    'title' => Yii::t('storeCredit', 'TITLE_REDEEM_GIFT_CARD')
                ));
            }
        }

        $form = new RedeemGiftCardForm();

        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();

        // gift card redemption
        $f_data['giftcards'] = GiftCardCom::product();

        $error = '';

        if (isset(Yii::app()->session['need_captcha'])) {
            return $this->redirect('/account/captcha');
        } elseif ($f_data['valid_sc'] && Yii::app()->request->isPostRequest && $cur_code) {
            $redeem_details = Yii::app()->params['GIFT_CARD']['PRODUCT'][$_POST['RedeemGiftCardForm']['rd_code']];
            $gc_class = $redeem_details['class'];
            $gc_obj = new $gc_class();

            if (!isset($gc_obj)) {
                ;
            } else {
                $gc_res = $gc_obj->validatePin($_POST['RedeemGiftCardForm']);

                if ($gc_res["status"] === true) {
                    if (isset($gc_res['result']['og_db']) && $gc_res['result']['og_db']) {
                        $result = array('status' => true);
                    } else {
                        $result = $gc_obj->redeem($_POST['RedeemGiftCardForm']);
                    }

                    if ($result["status"] === true) {
                        $from_rate = $cur_obj->format(1, false, $gc_res["result"]["currency"]);
                        $rate = $cur_obj->advanceCurrencyConversion(1, $gc_res["result"]["currency"], $cur_code, true, 'sell');
                        $to_rate = $cur_obj->format($rate, false, $cur_code);

                        $gc_value = $cur_obj->format($gc_res["result"]["deno"], false, $gc_res["result"]["currency"]);
                        $deno = $cur_obj->advanceCurrencyConversion($gc_res["result"]["deno"], $gc_res["result"]["currency"], $cur_code, true, 'sell');
                        $value = $cur_obj->format($deno, false, $cur_code);

                        $before = $cur_obj->format($scStatus['amount'], false, $cur_code);
                        $rd_data = array(
                            "pin" => $_POST['RedeemGiftCardForm']['rd_pin'],
                            "serial" => $_POST['RedeemGiftCardForm']['rd_serial'],
                            "orig_deno" => $gc_res["result"]["deno"],
                            "orig_cur_id" => $cur_obj->currencies[$gc_res["result"]["currency"]]["currencies_id"],
                            "orig_cur_code" => $gc_res["result"]["currency"],
                            "redeem_deno" => $deno,
                            "redeem_cur_id" => $cur_obj->currencies[$cur_code]["currencies_id"],
                            "redeem_cur_code" => $cur_code
                        );

                        $sc_obj = new StoreCreditCom();
                        $redeemResult = $sc_obj->redeemGiftCard($rd_data);

                        if ($redeemResult['status']) {
                            // latest balance
                            $_bal = StoreCreditCom::storeCreditBalance(false);
                            $after = $cur_obj->format($_bal['amount'], false, $cur_code);

                            if (isset(Yii::app()->session['cc_trial'])) {
                                unset(Yii::app()->session['cc_trial']);
                            }

                            Yii::app()->session->add(
                                static::SESSION_KEY_GC_REDEEM_RESULT,
                                array_merge($gc_res["result"], [
                                    'gc_value' => $gc_value,
                                    'from_rate' => $from_rate,
                                    'to_rate' => $to_rate,
                                    'topup_value' => $value,
                                    "before" => $before,
                                    "after" => $after
                                ])
                            );

                            return $this->redirect('/account/store-credit/redeem-gift-card-success');
                        } else {
                            $error = $this->parseGiftCardResult(['status' => $redeemResult['error_code']]);
                        }
                    } else {
                        $error = $this->parseGiftCardResult($result);
                    }
                } else {
                    $error = $this->parseGiftCardResult($gc_res);

                    CaptchaCom::checkNeedCaptcha('sessionCheck', array(
                        'returnUrl' => Yii::app()->createUrl('storeCredit/redeemGiftCard'),
                        'maxTries' => Yii::app()->params['GIFT_CARD']['TRIAL'],
                        'sessionKey' => 'cc_redeemgift',
                    ));
                }
            }
        }

        if ($error) {
            Yii::app()->user->setFlash('error', $error);
        }

        // Do not use the model loaded with POST data, keep form empty
        $f_data['model'] = $model;
        $f_data['form_model'] = $form;

        # `Convert To` currency list
        foreach ($cur_obj->currencies as $_code => $_data) {
            if ($currRestrictionArray = json_decode(RegionalSettingCom::getExcludedCurrencyJSON())) {
                if (in_array($_code, $currRestrictionArray)) {
                    continue;
                }
            }
            $f_data['currencyList'][$_code] = $_data['title'] . " ( " . html_entity_decode($_data['symbol_left'], ENT_NOQUOTES, 'UTF-8') . " " . html_entity_decode($_data['symbol_right'], ENT_NOQUOTES, 'UTF-8') . " ) ";
        }

        return $this->render('redeem-gift-card', $f_data);
    }

    public function actionVerifyGiftCard()
    {
        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();
        $is_sancton = Yii::app()->customerCom->isPhoneSanctionCountry(true);
        $f_data = array(
            'status' => false,
            'result' => array(),
            'error' => '',
            'link' => ''
        );

        if ($is_sancton) {
            $f_data['link'] = Yii::app()->createAbsoluteUrl('/site/error');
        } elseif (isset(Yii::app()->session['need_captcha'])) {
            $f_data['link'] = Yii::app()->createAbsoluteUrl('account/captcha');
        } elseif (isset($_POST['rd_code']) && isset($_POST['rd_serial']) && isset($_POST['rd_pin'])) {
            $sc_res = StoreCreditCom::accountCurrency();

            if (!$sc_res['status']) {
                $f_data['error'] = Yii::t('storeCredit', 'ERROR_INVALID_STORE_CREDIT_CURRENCY');
            } elseif (!$sc_res['result']['sc_set'] && isset($_POST['rd_curr'])) {
                //If not supporting input-based currency, remove this IF block
                $cur_code = $_POST['rd_curr'];
            } else {
                $cur_code = $sc_res['result']['cur_code'];
            }

            if (!$f_data['error']) {
                $redeem_details = Yii::app()->params['GIFT_CARD']['PRODUCT'][$_POST['rd_code']];
                $model = new Customers();
                $gc_class = $redeem_details['class'];
                $gc_obj = new $gc_class();

                $gc_res = $gc_obj->validatePin($_POST);

                $f_data['status'] = $gc_res['status'] === true;
                if ($f_data['status']) {
                    $from_rate = $cur_obj->format(1, false, $gc_res['result']['currency']);
                    $rate = $cur_obj->advanceCurrencyConversion(1, $gc_res['result']['currency'], $cur_code, true, 'sell');
                    $to_rate = $cur_obj->format($rate, false, $cur_code);

                    $gc_value = $cur_obj->format($gc_res['result']['deno'], false, $gc_res['result']['currency']);
                    $deno = $cur_obj->advanceCurrencyConversion($gc_res['result']['deno'], $gc_res['result']['currency'], $cur_code, true, 'sell');
                    $value = $cur_obj->format($deno, false, $cur_code);

                    $f_data['result'] = array(
                        'gc_value' => $gc_value,
                        'from_rate' => $from_rate,
                        'to_rate' => $to_rate,
                        'topup_value' => $value,
                        'from_cur' => $gc_res['result']['currency'],
                        'to_cur' => $cur_code,
                    );
                } else {
                    $f_data['error'] = $this->parseGiftCardResult($gc_res);
                }
            }

            if (!$f_data['status']) {
                $requireCaptcha = CaptchaCom::checkNeedCaptcha('sessionCheck', array(
                    'returnUrl' => Yii::app()->createAbsoluteUrl('account/store-credit/redeem-gift-card'),
                    'redirect' => false,
                    'maxTries' => Yii::app()->params['GIFT_CARD']['TRIAL'],
                    'sessionKey' => 'cc_redeemgift',
                ));
                if ($requireCaptcha) {
                    if (!empty($f_data['error'])) {
                        Yii::app()->user->setFlash('danger', $f_data['error']);
                    }
                    $f_data['link'] = Yii::app()->createAbsoluteUrl('account/captcha');
                }
            }
        }

        echo json_encode($f_data);
    }

    public function actionRedeemGiftCardSuccess()
    {
        $redeem_result = Yii::app()->session->get(static::SESSION_KEY_GC_REDEEM_RESULT, []);
        Yii::app()->session->offsetUnset(static::SESSION_KEY_GC_REDEEM_RESULT);
        if (empty($redeem_result)) {
            return $this->redirect('/account/store-credit/redeem-gift-card');
        }

        $this->menu = 'LINK_SC_REDEEM_GC';
        $redeem_result['model'] = new MyOffgamersForm();
        return $this->render('redeem-gift-card-success', $redeem_result);
    }

    public function actionStatement()
    {
        $model = new MyOffgamersForm();

        // Check MS status and Customer Store Credit Account status
        $scSiteStatus = Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'];
        if ($scSiteStatus == 0) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_MAINTENANCE')
            ));
        }
        $scStatus = StoreCreditCom::storeCreditBalance(true);
        if ($scStatus['status'] == false) {
            return $this->render('disable-ms', array(
                'model' => $model,
                'title' => Yii::t('page_content', 'TEXT_TOPUP'),
                'f_message' => Yii::t('page_content', 'OG_STORE_CREDIT_DISABLE')
            ));
        }

        $dates = [];
        $now = time();
        $mth = date('n', $now);
        $yr = date('Y', $now);

        for ($i = 0; $i < StoreCreditCom::$stmt_filt_month; $i++) {
            $time = mktime(0, 0, 0, $mth - $i, 1, $yr);
            $dates[$time] = $i == 0 ? Yii::t('storeCredit', 'LABEL_STATEMENT_THIS_MONTH') : Yii::t('storeCredit', 'LABEL_STATEMENT_DATE', [
                '{SYS_DATE}' => date('M Y', $time)
            ]);
        }

        reset($dates);

        $date = (isset($_GET['date']) && isset($dates[$_GET['date']])) ? $_GET['date'] : key($dates);
        $page = (isset($_GET['page']) ? (int)(new CHtmlPurifier())->purify($_GET['page']) : 1);

        if (empty($page) || !is_int($page)) {
            $page = 1;
        }

        $pageCount = 20;
        $offset = ($page - 1) * $pageCount;

        $statementArray = StoreCreditCom::storeCreditStatement(array(
            'start_date' => $date,
            'end_date' => strtotime("+1 month", $date),
            'page' => $page,
            'limit' => $pageCount,
        ));

        if (!$statementArray) {
            Yii::app()->user->setFlash('error', Yii::t('storeCredit', 'ERROR_STORE_CREDIT_CONTACT_ADMIN'));
        }

        return $this->render('statement', array(
            'model' => $model,
            'balance' => $scStatus['amount'],
            'sc_list' => $statementArray['list'],
            'scSiteStatus' => $scSiteStatus,
            'scStatus' => $scStatus['status'],
            'list_customs' => $statementArray['list_customs'],
            'pagination' => $statementArray['pagination'],
            'dates' => $dates,
            'date' => $date,
            'page' => $page,
        ));
    }

    protected function initCustomerStoreCredit($selected_currency = null)
    {
        $sc_obj = new StoreCreditCom();
        $fp_obj = new FrontPageCom();

        $cur_code = "";
        $sc_set = false;
        $f_data = array(
            "cur_code" => "",
            "cur_symbol_left" => "",
            "cur_symbol_right" => "",
            "cur_title" => "",
            "valid_sc" => true,
        );

        $cust_sc = StoreCreditCom::accountCurrency($selected_currency);

        if (!$cust_sc['status']) {
            $f_data['valid_sc'] = false;
            Yii::app()->user->setFlash('danger', Yii::t('storeCredit', 'ERROR_INVALID_STORE_CREDIT_CURRENCY'));
        }

        if (!empty($cust_sc['result']['cur_code'])) {
            $f_data = array_merge($f_data, $cust_sc['result']);
            $f_data["min_topup"] = $sc_obj->getMinimumStoreCredit($cust_sc['result']['cur_code']);
        } else {
            throw new ErrorException(Yii::t('storeCredit', 'ERROR_STORE_CREDIT_CONTACT_ADMIN'));
        }

        //check ip if from blocked countries
        $ipAdd = $fp_obj->getIPAddress();
        if ($ipAdd) {
            $blockedCountry = StoreCreditCom::getBlockedCountry($ipAdd);
        }

        $f_data["blocked_country"] = $blockedCountry;

        return $f_data;
    }

    protected function getAllowedCurrencies($sc_obj)
    {
        $cur_obj = new CurrenciesCom();
        $cur_obj->_init();
        $currencies = $cur_obj->getAllCurrencies();
        $array = array();

        foreach ($currencies as $cur_code => $cur_name) {
            if (!isset($cur_obj->currencies[$cur_code])) {
                continue;
            }

            if ($currRestrictionArray = json_decode(RegionalSettingCom::getExcludedCurrencyJSON())) {
                if (in_array($cur_code, $currRestrictionArray)) {
                    continue;
                }
            }

            $_data = $cur_obj->currencies[$cur_code];

            if (in_array(Yii::app()->session['customers_groups_id'], [6, 8])) {
                $maximum_top_up_in_usd = 20000;
            } else {
                $maximum_top_up_in_usd = 5000;
            }

            $min_topup = $sc_obj->getMinimumStoreCredit($cur_code);
            $max_topup = $sc_obj->getMaximumStoreCredit($cur_code, $maximum_top_up_in_usd);
            $deno_list = [];
            $deno_string = [];

            foreach (!empty(Yii::app()->params['CURRENCY_DENOMINATIONS'][$cur_code]) ? Yii::app()->params['CURRENCY_DENOMINATIONS'][$cur_code] : [] as $deno) {
                if ($min_topup <= $deno) {
                    $deno_list[] = $deno;
                    $deno_string[] = $cur_obj->format($deno, false, $cur_code);
                }
            }

            $array[$cur_code] = array(
                'label' => $_data['title'] . " ( " . html_entity_decode($_data['symbol_left'], ENT_NOQUOTES, 'UTF-8') . " " . html_entity_decode($_data['symbol_right'], ENT_NOQUOTES, 'UTF-8') . " ) ",
                'name' => $_data['title'],
                'min_topup' => $min_topup,
                'max_topup' => $max_topup,
                'min_topup_display' => number_format($min_topup, 0),
                'max_topup_display' => number_format($max_topup, 0),
                'symbol_left' => $_data['symbol_left'],
                'symbol_right' => $_data['symbol_right'],
                'deno' => $deno_list,
                'deno_string' => $deno_string,
            );
        }
        return $array;
    }

    protected function parseGiftCardResult($result)
    {
        if ($result['status'] === true) {
            //success
            return '';
        }
        switch ($result['status']) {
            case 1:
                return Yii::t("storeCredit", "ERROR_REDEEM_FAIL_SC");
            case 5:
                return Yii::t("storeCredit", "ERROR_REDEEM_FAIL");
            case 2:
            case 3:
                return Yii::t("storeCredit", "ERROR_REDEEM_FAIL_REDEEMED");
            case 4:
                return Yii::t("storeCredit", "ERROR_REDEEM_FAIL_INVALID_CURRENCY");
            default:
                return isset($result['error']) ? $result['error'] : 'Unknown error has occured';
        }
    }

}
