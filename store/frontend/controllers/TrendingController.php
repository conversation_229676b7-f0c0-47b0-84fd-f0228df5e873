<?php

/**
 * @package YiiBoilerplate\Frontend
 */
class TrendingController extends CController
{
    public $layout = '//layouts-2022/main_vue';
    public $model = '';

    public function actions()
    {
        return [
            'page' => [
                'class' => 'CViewAction',
                'layout' => '//layouts/default',
            ],
        ];
    }
    public function actionSoftware()
    {
        $this->render('vue');
    }

    public function actionGetUser()
    {
        echo json_encode(["session" => $_SESSION, "cookie" => $_COOKIE, "user" => Yii::app()->user->id]);
    }
}
