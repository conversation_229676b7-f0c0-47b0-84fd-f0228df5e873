<?php

/**
 * @package YiiBoilerplate\Frontend
 */
class UserBarController extends FrontendController
{

    public function actionRegional()
    {
        if(isset($_REQUEST['reg_lang'])){
            if(strtolower($_REQUEST['reg_lang']) == "zh-cn"){
                $_REQUEST['reg_lang'] = "cn";
            }
        }
        $supported_language_code = array_keys(RegionalSettingCom::getLanguageList());
        foreach ($supported_language_code as $i => $code) {
            $supported_language_code[$i] = '/' . $code . '/';
        }
        $controller_action = isset($_REQUEST['previousPath']) ? validateRequest($_REQUEST['previousPath']) : '';

        if ((isset($_REQUEST['reg_lang'])) || (Yii::app()->user->isGuest && isset($_REQUEST['reg_ctry']) && isset($_REQUEST['reg_cur']) && isset($_REQUEST['reg_lang']))) {
            RegionalSettingCom::checkRegionalSetting(true);
        }
        if ($controller_action !== 'site/index' && isset($_REQUEST['previousUrl'])) {
            if (in_array($controller_action, ['brand/index', 'search/gallery', 'search/index'])) {
                $path = explode('/', trim(validateRequest($_REQUEST['previousUrl']), '/'));
                if (isset($path[1]) && strlen($path[1]) == 2) {
                    $path[0] = strtolower(Yii::app()->session['country_code']);
                    $path[1] = strtolower(Yii::app()->session['language']);
                } elseif (isset($path[0]) && strlen($path[0]) == 2) {
                    $path[0] = strtolower(Yii::app()->session['language']);
                }
                $url = '/' . implode('/', $path);
            } else {
                $url = str_replace($supported_language_code, array('/', '/', '/'), validateRequest($_REQUEST['previousUrl']));

                $selected_lang_code = isset($_REQUEST['reg_lang']) ? validateRequest($_REQUEST['reg_lang']) : 'en';
                if (in_array('/' . $selected_lang_code . '/', $supported_language_code)) {
                    $url = '/' . $selected_lang_code . '/' . ltrim($url, '/');
                }
            }

            if (isset($_REQUEST['getParam'])) {
                if ($queryArray = CJSON::decode(urldecode(validateRequest($_REQUEST['getParam'])))) {
                    if (isset($queryArray['language'])) {
                        unset($queryArray['language']);
                    }
                    if (is_array($queryArray) || is_object($queryArray)) {
                        $url .= '?' . http_build_query($queryArray);
                    }
                }
            }
        } else {
            if ($controller_action) {
                if (isset($_REQUEST['getParam'])) {
                    if ($queryArray = CJSON::decode(urldecode(validateRequest($_REQUEST['getParam'])))) {
                        if (isset($queryArray['language']) && $queryArray['language'] === 'en') {
                            unset($queryArray['language']);
                        }
                        $url = Yii::app()->createUrl($controller_action, $queryArray);
                    }
                }
            }
        }

        $redirectUrl = (isset($url) && (strpos($url, '//') === false)) ? $url : Yii::app()->createAbsoluteUrl('/');
        if (isset($_REQUEST['origin'])) {
            $originUrl = Yii::app()->request->hostInfo . $_REQUEST['origin'];

            if (filter_var($originUrl, FILTER_VALIDATE_URL)) {
                if (domainMapping($originUrl) === true) {
                    $redirectUrl = $originUrl;
                }
            }
            return $this->redirect($_REQUEST['origin']);
        }

        $safePreviousUrl = Yii::app()->createAbsoluteUrl($redirectUrl);

        if (!filter_var($safePreviousUrl, FILTER_VALIDATE_URL)) {
            $redirectUrl = Yii::app()->createAbsoluteUrl('/');
        }

        return $this->redirect($redirectUrl);
    }

    public function actionGetCustomerScWor()
    {
        $worAmount = 0;
        $responseArray = array(
            'sc' => array(
                'error' => true,
                'val' => '-',
            ),
            'wor' => array(
                'error' => true,
                'val' => '-',
            ),
        );

        if (Yii::app()->user->id) {
            $responseArray['sc'] = Yii::app()->customerCom->getCustomerSC(Yii::app()->user->id);
            $responseArray['wor'] = Yii::app()->customerCom->getCustomerWor(Yii::app()->user->id);
        }

        echo CJSON::encode($responseArray);
    }

    public function actionSetNotificationAlertCookie()
    {
        if ($cname = validateRequest(Yii::app()->request->getParam('cname'))) {
            $val = null;
            $expiry_time = time() + 86400 * 90;

            if ($cname == 'alertbar_cookie_gst-alert') {
                $val = 1;
            } else {
                if ($cval = validateRequest(Yii::app()->request->getParam('cvalue'))) {
                    $data_array = array();

                    if (isset(Yii::app()->request->cookies[$cname])) {
                        $val_array = explode(',', htmlspecialchars(Yii::app()->request->cookies[$cname]->value));

                        if ($val_array) {
                            $temp_array = array_flip($val_array);
                            unset($temp_array[$cval]);
                            $data_array = array_flip($temp_array);
                        }
                    }

                    $data_array[] = $cval;
                    $val = implode(',', $data_array);
                }
            }

            if (notNull($val)) {
                $cookie = new CHttpCookie($cname, $val);
                $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
                $cookie->expire = $expiry_time;
                $cookie->path = '/';
                Yii::app()->request->cookies[$cname] = $cookie;
            }
        }
    }

}
