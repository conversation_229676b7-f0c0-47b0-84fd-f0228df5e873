<?php

class AuthUserBarWidget extends CWidget
{
    public  $region_info,
            $loggedIn_info,
            $loggedout_info,
            
            $TEXT_OR_CONNECT,
            $REGIONAL_SETTING;
    
    public function init() {
        Yii::app()->clientScript->registerScript("userbar_v2",
                "var REFRESH_TIME = 90000;
var EXCLUDED_CUR = '" . RegionalSettingCom::getExcludedCurrencyJSON() . "';
var COUNTRY_STATIC_DOMAIN = '" . $this->getCountryJsonURL() . "';
var REGION_STATIC_DOMAIN = '" . $this->getRegionJsonURL() . "';
var USER_BAR_URL = '" . Yii::app()->createAbsoluteUrl('/userBar') ."';
jQuery(document).ready(function() {
    og.userBar.init('" . $this->region_info['country_code'] . "', '" . $this->region_info['currency'] . "', '" . $this->region_info['language'] . "');
});", CClientScript::POS_END);
    }
 
    public function run() {
        return $this->render('userBar_v2',null,true);
    }
    
    public function getCountryJsonURL() {
        return $this->REGIONAL_SETTING['COUNTRY_STATIC_DOMAIN'];
    }

    public function getRegionJsonURL() {
        return $this->REGIONAL_SETTING['REGION_STATIC_DOMAIN'];
    }
}