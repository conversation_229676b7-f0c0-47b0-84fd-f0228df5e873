<?php
$changeLocationUrl = Yii::app()->createUrl("/sso/changeLocation/", array("next_url" => Yii::app()->request->getHostInfo() . Yii::app()->request->getUrl()));
Yii::app()->clientScript->registerScript('i18n-userbar', 'translate.change_country ="' . Yii::t('myogm', 'LOCATION_RESTRICTION_CHANGE_COUNTRY_DESCRIPTION') . '";', CClientScript::POS_END);
?>
<?php if ($this->loggedIn_info) { ?>
        <ul class="header__actions-list js-header-act">
            <li class="header__actions-item">
                <div class="header__actions-holder js-header-act-item">
                    <a href="#" class="header__actions-link js-header-act-trigger">
                        <span class="header__actions-icon">
                            <svg class="icon-world"><use xlink:href="#world"></use></svg>
                        </span> </a>
                    <div class="header-regional js-header-act-content">
                        <span class="header-regional__text"><?php echo $this->region_info['form_desc']; ?></span>
                        <div id="reg-setting-dd" class="header-regional__form">
                            <?php echo CHtml::beginForm($this->region_info['form_url'], 'post', array('class' => 'regional-form')); ?>
                            <div class="header-regional__row">
                                <?php
                                if (isset(Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID']) && in_array(Yii::app()->user->id, Yii::app()->params['REGION_RESTRICTION_BYPASS_CUSTOMER_ID'])) {
                                    ?>
                                <div>
                                    <select class="og-select2 clear-margin-left select2-offscreen reg_ctry"
                                            name="reg_ctry" tabindex="-1" title="">
                                        <option value=""><?php echo Yii::t('myogm', 'TEXT_IS_LOADING'); ?></option>
                                    </select>
                                </div>
                                <?php
                                }
                                else {
                                ?>
                                <div class="header-regional__phone-lock">
                                    <a href="javascript:changeLocationPopup('<?=$changeLocationUrl?>', '<?php echo Yii::app()->session['country_name']; ?>');" class="header-profile__main-link">
                                        <div class="header-profile__main-col header-profile__main-col--region">
                                            <span class="header-profile__actions-region"><?php echo Yii::app()->session['country_name']; ?></span>
                                        </div>
                                    </a>
                                </div>
                                <?php
                                }
                                ?>
                            </div>
                            <div class="header-regional__row">
                                <div class="reg_cur-div">
                                    <select class="og-select2 clear-margin-left select2-offscreen reg_cur"
                                        name="reg_cur" tabindex="-1" title="">
                                        <option value=""><?php echo Yii::t('myogm', 'TEXT_IS_LOADING'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="header-regional__row">
                                <select class="og-select2 clear-margin-left select2-offscreen reg_lang" name="reg_lang" tabindex="-1" title="">
                                    <?php
                                    foreach(RegionalSettingCom::getLanguageList() as $code => $lang){
                                        echo "<option value='$code'>$lang</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="header-regional__accept">
                                <button type="submit" class="btn btn-blue header-regional__accept-link"
                                        name="yt0"><?php echo $this->region_info['form_btn_label']; ?></button>
                            </div>

                            <?php
                            echo CHtml::hiddenField('getParam', Yii::app()->frontPageCom->getURLInfo('query'),['id'=>false]);
                            echo CHtml::hiddenField('previousPath', $this->region_info['previous_path'],['id'=>false]);
                            echo CHtml::hiddenField('previousUrl', $this->region_info['previous_url'],['id'=>false]);
                            echo CHtml::endForm();
                            ?>
                            </form>
                        </div>
                    </div>
                </div>
            </li>
            <li class="header__actions-item">
                <div class="header__actions-holder js-header-act-item">
                    <a href="#"
                            class="header__actions-link header__actions-link--avatar js-header-act-trigger">
                        <span class="header__actions-icon header__actions-icon--pic">
                            <img src="<?php echo $this->loggedIn_info['user_info']['avatar']; ?>"
                                    style="width:30px;height:30px;border-radius:50%;" alt="pic"
                                    class="header__actions-img">
                            <span class="header__actions-image">
                                <svg class="icon-arrow-bot"><use xlink:href="#arrow-bot"></use></svg>
                            </span>
                        </span> </a>
                    <div class="header-profile js-header-act-content">
                        <div class="header-profile__main">
                            <a href="<?php echo $this->loggedIn_info['user_info']['menu'][3]['href']; ?>" class="header-profile__main-link">
                                <div class="header-profile__main-col header-profile__main-col--pic">
                                    <img src="<?php echo $this->loggedIn_info['user_info']['avatar']; ?>"
                                            alt="pic" class="header-profile__main-img">
                                </div>
                                <div class="header-profile__main-col header-profile__main-col--content">
                                    <span class="header-profile__main-title"><?php echo $this->loggedIn_info['user_info']['customer_first_name']; ?></span>
                                    <span class="header-profile__main-text"><?php echo $this->loggedIn_info['user_info']['menu'][0]['label']; ?></span>
                                    <span class="header-profile__main-text"><?php echo $this->loggedIn_info['user_info']['menu'][1]['label']; ?></span>
                                </div>
                            </a>
                        </div>
                        <div class="header-profile__actions">
                            <?php if (Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'] == 1) { ?>
                            <!-- Start Store Credit -->
                            <div class="header-profile__actions-item">
                                <a href="javascript:void(0)" onclick="$(this).closest('.header-profile__actions').find('.sc-menu').slideToggle();" class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-link"><use xlink:href="#sc-coin-main"></use></svg>
                                        </span> <span class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text header-profile__expand"><?php echo $this->loggedIn_info['user_info']['menu'][5]['label']; ?></span>
                                        </span> </a>
                            </div>
                            <div class="sc-menu">
                                <div class="header-profile__actions-item">
                                    <a href="<?php echo $this->loggedIn_info['user_info']['menu'][10]['href']; ?>" class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use xlink:href="#sc-top-up"></use></svg>
                                        </span> <span class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][10]['label']; ?></span>
                                        </span> </a>
                                </div>
                                <div class="header-profile__actions-item">
                                    <a href="<?php echo $this->loggedIn_info['user_info']['menu'][13]['href']; ?>" class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use xlink:href="#sc-list"></use></svg>
                                        </span> <span class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][13]['label']; ?></span>
                                        </span> </a>
                                </div>
                                <div class="header-profile__actions-item">
                                    <a href="<?php echo $this->loggedIn_info['user_info']['menu'][12]['href']; ?>" class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use xlink:href="#sc-gift-card"></use></svg>
                                        </span> <span class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][12]['label']; ?></span>
                                        </span> </a>
                                </div>
                            </div>
                            <!-- End Store Credit -->
                            <?php } ?>

                            <!-- Start Overview -->
                            <div class="header-profile__actions-item">
                                <a href="<?php echo $this->loggedIn_info['user_info']['menu'][9]['href']; ?>"
                                        class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use
                                                        xlink:href="#view-thumb"></use></svg>
                                        </span> <span
                                            class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][9]['label']; ?></span>
                                        </span> </a>
                            </div>
                            <!-- End Overview -->

                            <!-- Start View Orders -->
                            <div class="header-profile__actions-item">
                                <a href="<?php echo $this->loggedIn_info['user_info']['menu'][11]['href']; ?>"
                                        class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-to-do"><use xlink:href="#to-do"></use></svg>
                                        </span> <span
                                            class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][11]['label']; ?></span>
                                        </span> </a>
                            </div>
                            <!-- End View Orders -->

                            <!-- Start WOR -->
                            <div class="header-profile__actions-item">
                                <a href="<?php echo $this->loggedIn_info['user_info']['menu'][6]['href']; ?>"
                                        class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-wor"><use xlink:href="#op-grey"></use></svg>
                                        </span> <span
                                            class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][6]['label']; ?></span>
                                        </span> </a>
                            </div>
                            <!-- End WOR -->

                            <!-- Start Tax -->
                            <?php if (TaxModuleCom::getTaxSettingStatus() == 1) { ?>
                            <div class="header-profile__actions-item">
                                <a href="<?php echo $this->loggedIn_info['user_info']['menu'][14]['href']; ?>"
                                        class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use xlink:href="#bank"></use></svg>
                                        </span> <span
                                            class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][14]['label']; ?></span>
                                        </span> </a>
                            </div>
                            <?php } ?>
                            <!-- End Tax -->
                            <!-- Start Review -->
                                <div class="header-profile__actions-item">
                                    <a href="<?php echo $this->loggedIn_info['user_info']['menu'][15]['href']; ?>"
                                       class="header-profile__actions-link">
                                        <span class="header-profile__actions-col header-profile__actions-col--img">
                                            <svg class="icon-view-thumb"><use xlink:href="#star"></use></svg>
                                        </span> <span
                                                class="header-profile__actions-col header-profile__actions-col--text">
                                            <span class="header-profile__actions-text"><?php echo $this->loggedIn_info['user_info']['menu'][15]['label']; ?></span>
                                        </span> </a>
                                </div>
                            <!-- End Review -->
                        </div>
                        <div class="header-profile__logout">
                            <a href="<?php echo $this->loggedIn_info['user_info']['menu'][8]['href']; ?>"
                                    class="header-profile__logout-link">
                                <span class="header-profile__logout-col header-profile__logout-col--img">
                                    <svg class="icon-enter"><use xlink:href="#enter"></use></svg>
                                </span> <span
                                        class="header-profile__logout-col header-profile__logout-col--text"><?php echo $this->loggedIn_info['user_info']['menu'][8]['label']; ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </li>
        </ul>

<?php } else { ?>
        <ul class="header__actions-list js-header-act">
            <li class="header__actions-item">
                <div class="header__actions-holder">
                    <a href="<?php echo $this->loggedout_info['signup_info']['href']; ?>"
                            class="header__actions-link"><?php echo $this->loggedout_info['signup_info']['title']; ?></a>
                </div>
            </li>
            <li class="header__actions-item">
                <div class="header__actions-holder">
                    <a href="<?php echo $this->loggedout_info['login_info']['href']; ?>"
                            class="header__actions-link"><?php echo $this->loggedout_info['login_info']['label']; ?></a>
                </div>
            </li>
            <li class="header__actions-item">
                <div class="header__actions-holder js-header-act-item">
                    <a href="#" class="header__actions-link js-header-act-trigger">
                        <span class="header__actions-icon">
                            <svg class="icon-world"><use xmlns:xlink="http://www.w3.org/1999/xlink"
                                        xlink:href="#world"></use></svg>
                        </span> </a>
                    <div class="header-regional js-header-act-content" style="display: none;">
                        <span class="header-regional__text"><?php echo $this->region_info['form_desc']; ?></span>
                        <div id="reg-setting-dd" class="header-regional__form">
                            <?php echo CHtml::beginForm($this->region_info['form_url'], 'post', array('class' => 'regional-form')); ?>
                            <?php
                            echo CHtml::beginForm($this->region_info['form_url'], 'post', array('class' => 'regional-form'));
                            ?>
                            <div class="header-regional__row">
                                <select class="og-select2 clear-margin-left select2-offscreen reg_ctry"
                                        name="reg_ctry" tabindex="-1" title="" onchange="regionalCurrencyLanguageModify(event)">
                                    <option value=""><?php echo Yii::t('myogm', 'TEXT_IS_LOADING'); ?></option>
                                </select>
                            </div>
                            <div class="header-regional__row">
                                <div class="reg_cur-div">
                                    <select class="og-select2 clear-margin-left select2-offscreen reg_cur"
                                            name="reg_cur" tabindex="-1" title="">
                                        <option value=""><?php echo Yii::t('myogm', 'TEXT_IS_LOADING'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="header-regional__row">
                                <select class="og-select2 clear-margin-left select2-offscreen reg_lang"
                                        name="reg_lang" tabindex="-1" title="">
                                    <?php
                                    foreach(RegionalSettingCom::getLanguageList() as $code => $lang){
                                        echo "<option value='$code'>$lang</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="header-regional__accept">
                                <button type="submit" class="btn btn-blue header-regional__accept-link"
                                        name="yt0"><?php echo $this->region_info['form_btn_label']; ?></button>
                            </div>


                            <?php
                            echo CHtml::hiddenField('getParam', Yii::app()->frontPageCom->getURLInfo('query'),['id'=>false]);
                            echo CHtml::hiddenField('previousPath', $this->region_info['previous_path'],['id'=>false]);
                            echo CHtml::hiddenField('previousUrl', $this->region_info['previous_url'],['id'=>false]);
                            echo CHtml::endForm();
                            ?>
                            </form>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
<?php } ?>
