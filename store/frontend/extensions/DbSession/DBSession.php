<?php

class DBSession extends CDbHttpSession {

    /**
     * @param mixed $db
     * @param mixed $tableName
     * @return
     */
    protected function createSessionTable($db, $tableName) {
        $driver = $db->getDriverName();
        if ($driver === 'mysql')
            $blob = 'LONGBLOB';
        else if ($driver === 'pgsql')
            $blob = 'BYTEA';
        else
            $blob = 'BLOB';
        $db->createCommand()->createTable($tableName, array(
            'id' => 'CHAR(32) PRIMARY KEY',
            'user_id' => 'INT(11) unsigned NULL',
            'expire' => 'integer',
            'data' => $blob,
        ));
        $db->createCommand("ALTER TABLE $tableName ADD INDEX idx_user_id (user_id);")->execute();
    }

    /**
     * CoreCDbHttpSession::writeSession()
     * 
     * @param mixed $id
     * @param integer $user_id
     * @param mixed $data
     * @return boolean
     */
    public function writeSession($id, $data) {
        try {
            $expire = time() + $this->getTimeout();
            $db = $this->getDbConnection();
            if ($db->createCommand()->select('id')->from($this->sessionTableName)->where('id=:id', array(':id' => $id))->queryScalar() === false)
                $db->createCommand()->insert($this->sessionTableName, array(
                    'id' => $id,
                    'user_id' => (isset(Yii::app()->user->id) ? Yii::app()->user->id : null),
                    'data' => $data,
                    'expire' => $expire,
                ));
            else
                $db->createCommand()->update($this->sessionTableName, array(
                    'user_id' => (isset(Yii::app()->user->id) ? Yii::app()->user->id : null),
                    'data' => $data,
                    'expire' => $expire
                        ), 'id=:id', array(':id' => $id));
        } catch (Exception $e) {
            if (YII_DEBUG)
                echo $e->getMessage();
            return false;
        }
        return true;
    }

}

?>