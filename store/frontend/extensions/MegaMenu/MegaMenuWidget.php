<?php

class MegaMenuWidget extends CWidget
{
    public  $all_prefix,
            $data;
    
    public function init() {
        // this method is called by CController::beginWidget()
    }
 
    public function run() {
        // this method is called by CController::endWidget()
        $this->render('menu');
    }
    
    public function getFeaturedBanner($banner_array) {
        $return_str = '';
        
        if (isset($banner_array['src']) && !empty($banner_array['src'])) {
            $banner_link = isset($banner_array['createURL']) ? Yii::app()->createUrl($banner_array['createURL'][0], $banner_array['createURL'][1]) : $banner_array['href'];
            $id = 'nav_imgmap-' . rand();
            $return_str = $this->widget('frontend.widgets.Template.HtmlWidget', array(
                        'type' => 'img',
                        'attributes_info' => array(
                            'class' => 'nav_subcat_img hidden-sm',
                            'usemap' => '#'.$id,
                            'src' => $banner_array['src'],
                            'width' => '520px',
                            'height' => '420px',
                        ),
                        'map_info' => array(
                            'id' => $id,
                            'name' => $id,
                            'area' => array(
                                array(
                                    'href' => $banner_link,
                                    'coords' => '220,120,499,398',
                                    'shape' => 'rect',
                                    'style' => 'outline:none;',
                                ),
                                array(
                                    'href' => $banner_link,
                                    'coords' => '499,398,125,398,499,13',
                                    'shape' => 'poly',
                                    'style' => 'outline:none;',
                                ),
                            )
                        )
                    ), TRUE);
        }
        
        return $return_str;
    }
}