<?php
$game_card_image = json_decode($this->data['tree'][1]['info']['tag_featured_banner'])->src;
$game_card_url = Yii::app()->createUrl('search/index', array('keyword' => ''));

$prefix_link = Yii::app()->params['SEARCH_PREFIX_KEYWORD'];
$link_arr = [];

foreach ($prefix_link as $value) {
    if (is_array($value)) {
        if($value['text'] == 'Multi Game Card' && (Yii::app()->frontPageCom->customer_ip_country == 'SG' || Yii::app()->frontPageCom->getRegional('country_code') == 'SG')){
            continue;
        }
        $value['href'] = Yii::app()->createUrl('brand/index', ['path' => [$value['href']]]);
        $link_arr[] = $value;
    } else {
        $link_arr[] = [
            'class' => '',
            'href' => Yii::app()->createUrl('search/index', array('keyword' => $value)),
            'text' => $value
        ];
    }
}

$suffix_link = Yii::app()->params['SEARCH_SUFFIX_KEYWORD'];
$suffix_arr = [];

foreach ($suffix_link as $value) {
    if (is_array($value)) {
        $suffix_arr[] = $value;
    } else {
        $suffix_arr[] = [
            'class' => '',
            'href' => Yii::app()->createUrl('search/index', array('keyword' => $value)),
            'text' => $value
        ];
    }
}

?>
<!-- Start Navi -->
<div class="header__navigation js-slick-desktop-nav" style="display: none;">
    <div class="container">
        <div class="header__navigation-inner js-slick-desktop-holder">
            <div class="navigation">
                <div class="navigation__inner">
                    <ul class="navigation__list">
                        <?php
                        foreach ($link_arr as $link) {
                            echo '<li class="navigation__item"><div class="navigation__holder ' . $link['class'] . '"><a href="' . $link['href'] . '" class="navigation__link" rel="nofollow">' . $link['text'] . '</a></div></li>';
                        }
                        echo '<li class="navigation__item"><div class="navigation__holder js-submenu-trigger navigation__holder--decor"> <a href="javascript:void(0);" class="navigation__link js-submenu-link"> ' . Yii::t('page_content',
                                'MEGA_MENU_GAME_CARD') . ' <span class="navigation__link-decor"><svg class="icon-arrow-side-small"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-sm-right"></use></svg></span> </a> <div class="navigation__content js-submenu-content"> <div class="navigation__content-holder"> <div class="navigation__content-menu"> <div class="navigation__content-inner"> <span class="navigation__content-heading js-submenu-link"> <span class="navigation__content-heading-decor"> <svg class="icon-arrow-side-small"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-sm-left"></use></svg> </span>' . Yii::t('page_content',
                                'MEGA_MENU_GAME_CARD') . '</span> <ul class="navigation__subtitle">';
                        $data = $this->data['game'];
                        $i = 1;

                        foreach ($data as $game) {
                            $url = Yii::app()->createUrl('brand/index', array('path' => [$game['seo_url']]));
                            $game_name = $game['name'];
                            echo "<li class=\"navigation__subtitle-item\"> <div class=\"navigation__subtitle-holder\"> <a href=\"$url\" rel=\"nofollow\" class=\"navigation__subtitle-link\">$game_name</a> </div> </li>";
                            $i++;
                            if ($i > 10) {
                                break;
                            }
                        }
                        echo '</ul><span class="navigation__more"> <a href="' . $game_card_url . '" class="navigation__more-link" rel="nofollow"> <span class="navigation__more-col navigation__more-col--text">' . Yii::t('buttons',
                                'VIEW_MORE') . '</span> <span class="navigation__more-col navigation__more-col--icon"> <svg class="icon-arrow-right-small"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-right"></use></svg> </span> </a> </span> </div> </div> </div> <div class="navigation__bg"><a href="' . $game_card_url . '" class="navigation__bg-link" style="background-image:url(' . $game_card_image . ')"></a></div> </div> </div></li>';

                        echo '<li class="navigation__item "><div class="navigation__holder navigation__holder--decor"><a href="' . Yii::app()->createUrl('game-key/index') . '" class="navigation__link" rel="nofollow">' . Yii::t('page_content',
                                'MEGA_MENU_GAME_KEY') . '</a></div></li>';

                        foreach ($suffix_arr as $link) {
                            echo '<li class="navigation__item"><div class="navigation__holder ' . $link['class'] . '"><a href="' . $link['href'] . '" class="navigation__link" rel="nofollow">' . $link['text'] . '</a></div></li>';
                        }
                        ?>

                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>