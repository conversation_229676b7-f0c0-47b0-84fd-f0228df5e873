<?php

return array(
    'BTN_RESEND_SECURITY_TOKEN' => '重发安全码',
    'BTN_REQUEST_SECURITY_TOKEN' => '获取验证码',
    'BTN_REQUEST_EMAIL_TOKEN' => '电邮验证码',
    'BTN_RESEND_TOKEN' => '重新发送',
    //
    'MSG_PHONE_WARNING' => '验证码已发送至您的手机。如果您提供的手机号码无效或已丢失，请联系我们的客服寻求帮助。',
    //
    'TEXT_EMAIL_TOKEN_MSG' => '如果您之前填写的手机号码已经丢失或者不再使用，请申请安全口令。安全码将发送到您注册的电子邮箱中。',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '请输入6位数字验证码',
    'TEXT_FOR_AUTHENTICATION_PURPOSE' => '鉴于安全认证，请输入您当前电话号码的最后4位数字：',
    'TEXT_PLEASE_SETUP_MOBILE' => '您提供的手机号码已使用或不存在。请提供另一个手机号码。 点击 <a class="d2BlueSz11" href="{EDIT_PHONE_URL}" target="_blank">这里</a>',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => '您的验证码使用次数已超过最大限制，请24小时后重试。',
    'TEXT_REQUEST_TOKEN_MSG' => '您的验证码将在10分钟后失效。同一天内，申请验证码不应超过5次。',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '您的新安全码是%s。 这安全码会在您收到这邮件的24小时过期。',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => '您的安全码已经成功发送至您注册的邮箱。',
    'TEXT_LOST_PHONE' => '<a href="{SUPPORT_US_LINK}" target="_blank">需要帮忙？</a> / <a href="javascript:void(0);" onclick="{LOST_PHONE_LINK}">手机丢失？</a>',
    'TEXT_BACK' => '<a href="javascript:void(0);" onclick="{BACK_LINK}">返回</a>',
    'TEXT_DORMANT_ACCOUNT_QNA_REQUEST' => '超过90天没有登录帐户，需要使用安全令牌重新激活帐户才可以购物。<br><br>索取的验证码将在10分钟内失效。若欲索取新的验证码每天只限不超过5次。',
    'TEXT_RESEND_COUNTER_LEFT_LABEL' => '重新发送',
    'TEXT_RESEND_COUNTER_RIGHT_LABEL' => '秒...',
    'TEXT_ENTER_LAST_4_DIGIT' => '输入手机号码的最后4位数字,验证码将发送至您的注册电邮。',
    //
    'TITLE_SECURITY_TOKEN' => '安全码',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => '您的安全码已重设。',
);