<?php

return array(
    'BTN_RESEND_SECURITY_TOKEN' => 'Resend security token',
    'BTN_REQUEST_SECURITY_TOKEN' => 'Request token',
    'BTN_REQUEST_EMAIL_TOKEN' => 'Email token',
    'BTN_RESEND_TOKEN' => 'Resend',
    //
    'MSG_PHONE_WARNING' => 'To edit/change phone number, security token will be sent to your previous registered number. For lost number or invalid number, please contact our Customer Service.',
    //
    'TEXT_EMAIL_TOKEN_MSG' => 'If the mobile number is lost or no longer in use, do request for a security token below. The code will be sent to your registered e-mail.',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => 'Enter the 6 digits security token',
    'TEXT_FOR_AUTHENTICATION_PURPOSE' => 'For authentication purposes, kindly fill in the last 4 digit of your current phone number below:',
    'TEXT_PLEASE_SETUP_MOBILE' => 'The mobile number you had provided is in used or invalid. Please key in another mobile number. Click <a class="d2BlueSz11" href="{EDIT_PHONE_URL}" target="_blank">here</a>',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => 'You have exceeded the allowable limit for security tokens. Please try again after 24 hours.',
    'TEXT_REQUEST_TOKEN_MSG' => 'Your security token will expire in 10 minutes. Subsequent request for new security token should not exceed more than 5 times in a single day.',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => 'Your new Security Token is %s. This token will expired in %s hours from the time you receive this email.',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => 'Your security token has been successfully sent to your registered email address.',
    'TEXT_LOST_PHONE' => '<a href="{SUPPORT_US_LINK}" target="_blank">Need help?</a> / <a href="javascript:void(0);" onclick="{LOST_PHONE_LINK}">Lost phone?</a>',
    'TEXT_BACK' => '<a href="javascript:void(0);" onclick="{BACK_LINK}">Back</a>',
    'TEXT_DORMANT_ACCOUNT_QNA_REQUEST' => 'Dormant accounts for more than 90 days are required to enter the Security Token to reactivate the account for purchases.',
    'TEXT_RESEND_COUNTER_LEFT_LABEL' => 'Resend in',
    'TEXT_RESEND_COUNTER_RIGHT_LABEL' => 'sec...',
    'TEXT_ENTER_LAST_4_DIGIT' => 'Enter the last 4 digits mobile phone number to receive security token in your registered e-mail.',
    //
    'TITLE_SECURITY_TOKEN' => 'SECURITY TOKEN',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => 'Your Security Token has been reset',
);