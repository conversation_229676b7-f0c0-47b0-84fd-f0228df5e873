<?php

return array(
    'BTN_RESEND_SECURITY_TOKEN' => 'セキュリティートークンの再送',
    'BTN_REQUEST_SECURITY_TOKEN' => 'トークンのリクエスト',
    'BTN_REQUEST_EMAIL_TOKEN' => 'Eメールトークン',
    'BTN_RESEND_TOKEN' => '再送',
    //
    'MSG_PHONE_WARNING' => '電話番号を編集、変更する場合は、以前登録していた電話番号にセキュリティートークンが送信されます。存在しない、もしくは無効な電話番号です。カスタマーサービスにご連絡ください。',
    //
    'TEXT_EMAIL_TOKEN_MSG' => '電話番号を忘れた、または持っていない場合は、以下でセキュリティートークンの発行をリクエストしてください。コードは登録したメールアドレスに送信されます。',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '6桁のセキュリティートークンを入力してください',
    'TEXT_FOR_AUTHENTICATION_PURPOSE' => '認証のため、以下に現在お使いの電話番号の最後の4桁を入力してください。',
    'TEXT_PLEASE_SETUP_MOBILE' => '入力された電話番号は既に使われているか、無効です。他の電話番号を入力してください。<a class="d2BlueSz11" href="{EDIT_PHONE_URL}" target="_blank">こちら</a>をクリック',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => 'セキュリティートークンの制限回数を超過しました。24時間後に再度お試しください。',
    'TEXT_REQUEST_TOKEN_MSG' => 'セキュリティートークンは10分で失効します。新しいセキュリティートークンのリクエストが1日5回を超えないようにしてください。',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '新しいセキュリティートークンは %s です。このトークンはEメール受信後、 %s 時間で失効します。',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => 'セキュリティトークンが登録されたメールアドレスに送信されました。',
    'TEXT_LOST_PHONE' => '<a href="{SUPPORT_US_LINK}" target="_blank">サポートが必要ですか？</a> / <a href="javascript:void(0);" onclick="{LOST_PHONE_LINK}">携帯電話を紛失した場合</a>',
    'TEXT_BACK' => '<a href="javascript:void(0);" onclick="{BACK_LINK}">戻る</a>',
    'TEXT_DORMANT_ACCOUNT_QNA_REQUEST' => '90日以上休止しているアカウントを商品の購入目的で復活させる場合は、セキュリティートークンの入力が必須です。',
    'TEXT_RESEND_COUNTER_LEFT_LABEL' => '再送まで：',
    'TEXT_RESEND_COUNTER_RIGHT_LABEL' => 'お待ちください...',
    'TEXT_ENTER_LAST_4_DIGIT' => '登録済みのEメールアドレスでセキュリティートークンを受け取るには、電話番号の最後の4桁を入力してください。',
    //
    'TITLE_SECURITY_TOKEN' => 'セキュリティートークン',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => 'セキュリティートークンがリセットされました',
);