<?php

return array(
    'BTN_RESEND_SECURITY_TOKEN' => '보안 토큰 재전송',
    'BTN_REQUEST_SECURITY_TOKEN' => '토큰 요청',
    'BTN_REQUEST_EMAIL_TOKEN' => '이메일 토큰',
    'BTN_RESEND_TOKEN' => '재전송',
    //
    'MSG_PHONE_WARNING' => '전화번호 수정/변경 절차를 위해하려면 이전에 등록된 전화번호로 보안 코드가 발송됩니다. 번호를 잊어버렸거나 번호가 유효하지 않은 경우 고객 서비스팀으로 문의해 주세요.',
    //
    'TEXT_EMAIL_TOKEN_MSG' => '휴대전화 번호를 잊어버렸거나 더 이상 사용하지 않는 경우 아래에서 보안 토큰을 요청하세요. 코드는 등록된 이메일로 전송됩니다.',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '6자리 보안 토큰 입력',
    'TEXT_FOR_AUTHENTICATION_PURPOSE' => '인증을 위해 현재 사용하고 있는 휴대전화 번호의 마지막 4자리를 아래에 입력해 주세요:',
    'TEXT_PLEASE_SETUP_MOBILE' => '입력한 휴대전화 번호가 이미 사용 중이거나 유효하지 않습니다. 다른 휴대전화 번호를 입력하세요. <a class="d2BlueSz11" href="{EDIT_PHONE_URL}" target="_blank">여기</a>를 클릭하세요',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => '보안 토큰에 대한 허용 가능한 한도를 초과했습니다. 24시간 이후에 다시 시도하세요.',
    'TEXT_REQUEST_TOKEN_MSG' => '보안 토큰이 10분 후에 만료됩니다. 이후 새로운 보안 토큰 요청은 일일 5회를 초과하면 안 됩니다.',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '새로운 보안 토큰은 %s입니다. 이 토큰은 이 이메일을 수신한 시간으로부터 %s시간 후에 만료됩니다.',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => '보안 토큰이 등록된 이메일 주소로 전송되었습니다.',
    'TEXT_LOST_PHONE' => '<a href="{SUPPORT_US_LINK}" target="_blank">도움이 필요하신가요p?</a> / <a href="javascript:void(0);" onclick="{LOST_PHONE_LINK}">휴대폰을 잃어버리셨나요?</a>',
    'TEXT_BACK' => '<a href="javascript:void(0);" onclick="{BACK_LINK}">뒤로</a>',
    'TEXT_DORMANT_ACCOUNT_QNA_REQUEST' => '90일 이상 활동이 없는 계정은 보안 토큰을 입력해야 계정을 재활성화해 구매를 진행할 수 있습니다.',
    'TEXT_RESEND_COUNTER_LEFT_LABEL' => '재전송 시간:',
    'TEXT_RESEND_COUNTER_RIGHT_LABEL' => '초...',
    'TEXT_ENTER_LAST_4_DIGIT' => '등록된 이메일로 보안 코드를 받으려면 휴대전화 번호 마지막 4자리를 입력하세요.',
    //
    'TITLE_SECURITY_TOKEN' => '보안 토큰',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => '보안 토큰이 초기화되었습니다',
);