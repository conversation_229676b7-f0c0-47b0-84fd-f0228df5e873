<?php

return array(
    'BTN_REQUEST_SECURITY_TOKEN' => '獲取驗證碼',
    'BTN_RESEND_SECURITY_TOKEN' => '重發安全碼',
    'BTN_REQUEST_EMAIL_TOKEN' => '電郵驗證碼',
    'BTN_RESEND_TOKEN' => '重新發送',
    //
    'MSG_PHONE_WARNING' => '驗證碼已發送至您的手機。如果您提供的手機號碼無效或已丟失，請聯繫我們的客服尋求幫助。',
    //
    'TEXT_EMAIL_TOKEN_MSG' => '如果您之前填寫的手機號碼已經丟失或者不再使用，請申請安全口令。安全碼將發送到您註冊的電子郵箱中。',
    'TEXT_ENTER_6_SERUCITY_TOKEN' => '請輸入6位數字驗證碼。',
    'TEXT_FOR_AUTHENTICATION_PURPOSE' => '鑑於安全認證，請輸入您當前電話號碼的最後4位數字：',
    'TEXT_PLEASE_SETUP_MOBILE' => '您提供的手機號碼已使用或不存在。請提供另一個手機號碼。點擊 <a class="d2BlueSz11" href="{EDIT_PHONE_URL}" target="_blank">這裡</a>',
    'TEXT_REQUEST_TOKEN_MSG' => '您的驗證碼將在10分鐘後失效。同一天內，申請驗證碼不應超過5次。',
    'TEXT_REQUEST_TOKEN_FAIL_MSG' => '您的驗證碼使用次數已超過最大限制，請24小時後重試。',
    'TEXT_SECURITY_TOKEN_RESET_CONTENT' => '您的新安全碼是%s。這安全碼會在您收到這郵件的24小時過期。',
    'TEXT_SUCCESSFULLY_SEND_MAIL' => '您的安全碼已經成功發送至您註冊的郵箱。',
    'TEXT_LOST_PHONE' => '<a href="{SUPPORT_US_LINK}" target="_blank">需要幫忙？</a> / <a href="javascript:void(0);" onclick="{LOST_PHONE_LINK}">手機丟失？</a>',
    'TEXT_BACK' => '<a href="javascript:void(0);" onclick="{BACK_LINK}">返回</a>',
    'TEXT_DORMANT_ACCOUNT_QNA_REQUEST' => '超過90天沒有登錄帳戶，需要使用安全標識重新激活帳戶才可以購物。<br><br>索取的驗證碼將在10分鐘內失效。若欲索取新的驗證碼每天只限不超過5次。',
    'TEXT_RESEND_COUNTER_LEFT_LABEL' => '重新發送',
    'TEXT_RESEND_COUNTER_RIGHT_LABEL' => '秒...',
    'TEXT_ENTER_LAST_4_DIGIT' => '輸入手機號碼的最後4位數字，驗證碼將發送至您的註冊電郵。',
    //
    'TITLE_SECURITY_TOKEN' => '驗證碼',
    'TITLE_SECURITY_TOKEN_RESET_NOTIFICATION' => '您的安全碼已重設',
);