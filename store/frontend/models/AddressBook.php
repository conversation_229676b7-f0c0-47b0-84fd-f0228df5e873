<?php

/**
 * This is the model class for table "address_book".
 *
 * The followings are the available columns in table 'address_book':
 * @property integer $address_book_id
 * @property integer $customers_id
 * @property string $entry_gender
 * @property string $entry_company
 * @property string $entry_firstname
 * @property string $entry_lastname
 * @property string $entry_street_address
 * @property string $entry_suburb
 * @property string $entry_postcode
 * @property string $entry_city
 * @property string $entry_state
 * @property integer $entry_country_id
 * @property integer $entry_zone_id
 */
class AddressBook extends AddressBookBase {

    public $firstname,
            $lastname,
            $company,
            $street_address,
            $suburb,
            $city,
            $postcode,
            $state,
            $zone_id,
            $country_id;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return AddressBook the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getAddressByCustomerAndAddressId($customerId, $addressId) {

        $criteria = new CDbCriteria();
        $criteria->select = array('entry_firstname as firstname', 'entry_lastname as lastname', 'entry_company as company', 'entry_street_address as street_address', 'entry_suburb as suburb', 'entry_city as city', 'entry_postcode as postcode', 'entry_state as state', 'entry_zone_id as zone_id', 'entry_country_id as country_id');
        $criteria->condition = 'customers_id = :customerId AND address_book_id = :addressId';
        $criteria->params = array(":customerId" => $customerId, ":addressId" => $addressId);
        $result = $this->model()->find($criteria);
        return $result;
    }

}
