<?php

/**
 * This is the model class for table "address_format".
 *
 * The followings are the available columns in table 'address_format':
 * @property integer $address_format_id
 * @property string $address_format
 * @property string $address_summary
 */
class AddressFormat extends AddressFormatBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return AddressFormat the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

}
