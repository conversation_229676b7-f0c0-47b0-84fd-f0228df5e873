<?php


class BaseAPIModel extends CFormModel
{

    public $title, $client, $client_option, $header, $baseUrl, $controller, $action, $method, $s_key, $params;

    private $config;

    public function __construct()
    {
        $this->client = Yii::app()->curl;

        $this->client->setProxy(false);

        $this->header = array('Content-Type' => 'application/json');
    }

    private function getConfig()
    {
        if (empty($this->config)) {
            $key = $this->s_key;
            if (empty(Yii::app()->params[$this->s_key])) {
                throw new \http\Exception\InvalidArgumentException();
            }
            $this->config = Yii::app()->params[$key];
        }
        return $this->config;
    }

    public function request($params = [])
    {
        $config = $this->getConfig();

        $url = $config['baseUrl'] . '/' . $this->controller . '/' . $this->action;

        $timestamp = time();

        $attach = array(
            'api_key' => $config['key'],
            'timestamp' => $timestamp,
        );

        if (!is_array($params)) {
            $request_body = array_merge($attach, ['data' => $params]);
        } else {
            $attach['data'] = $params;
            $request_body = $attach;
        }

        ksort($request_body);

        $body = json_encode($request_body);

        $signature = hash_hmac('sha256', $body, $config['secret']);

        $this->header['signature'] = $signature;

        $this->client->request_headers = $this->header;

        $res = $this->client->sendRequest($url, $body, $this->method, $url, true);

        return (isset($res['response']) ? $res['response'] : $this->client->getError());
    }
}