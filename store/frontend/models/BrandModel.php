<?php

class BrandModel extends CFormModel
{
    public $brand_id;
    public $brand_path;
    public $supported_item;
    public $view;
    public $product_list;
    public $out_of_stock_list;
    public $base_path = '';
    public $current_path = [];
    public $next_url;
    public $total_price = 0;
    public $total_op = 0;
    public $product_content_obj;
    public $state_list;

    public function __construct($params = [], $scenario = '')
    {
        $this->brand_path = ($_REQUEST['brand_path'] ?? '');
        $this->view = (new BrandViewForm());
        parent::__construct($scenario);
    }

    public function processUrl()
    {
        $valid_path = [];
        $next_layer = '';
        try {
            foreach ($this->brand_path as $index => $url_alias) {
                # Special Rules for game key to prevent memory exhausted issues
                if ($index === 0 && $url_alias === 'game-keys') {
                    Yii::app()->getController()->redirect(Yii::app()->createUrl('game-key/index'));
                    Yii::app()->end();
                }

                if ($index === 0 && $brand_info = $this->getPrimaryBrandInfoByUrl($url_alias)) {
                    if ($brand_info['hard_block_country'] == 1) {
                        throw new CHttpException(404, 'Invalid Brand');
                    }
                    $this->view->last_selected_url = $url_alias;
                    $this->view->brand_url = $url_alias;
                    if ($url_alias == $this->view->listing['brand']['value']) {
                        $this->base_path = Yii::app()->createAbsoluteUrl('brand/index', ['path' => $valid_path]);
                        $valid_path[] = $url_alias;
                    } else {
                        $valid_path[] = $url_alias;
                        $this->base_path = Yii::app()->createAbsoluteUrl('brand/index', ['path' => $valid_path]);
                    }
                    $this->view->addBreadcrumb($valid_path);
                    Yii::app()->clientScript->registerScript(
                        'i18n-trans',
                        'let base_url = "' . trim($this->base_path, '/') . '";',
                        CClientScript::POS_END
                    );
                    if ($brand_info['soft_block_country'] == 1) {
                        $this->view->soft_block_country = true;
                        break;
                    }
                } elseif ($next_layer != 'product' && isset($this->supported_item[$url_alias])) {
                    $next_layer = $this->supported_item[$url_alias]['type'];
                    switch ($next_layer) {
                        case 'brand':
                            $this->getBrandInfoByUrl($valid_path[0], $url_alias);
                            $this->view->setSelected('brand', $url_alias);
                            $this->view->addBreadcrumb($valid_path);
                            $valid_path[] = $url_alias;
                            break;
                        case 'category':
                            $this->getSupportedProductList($url_alias);
                            if (isset($this->view->listing['category']['supported_item'][$url_alias]['soft_block']) && !$this->view->listing['category']['supported_item'][$url_alias]['soft_block']) {
                                $this->view->setSelected('category', $url_alias);
                                $next_layer = 'product';
                                $valid_path[] = $url_alias;
                                $this->view->addBreadcrumb($valid_path);
                                $this->next_url = $next_layer;
                            } else {
                                throw new Exception('Invalid Product', 300);
                            }
                    }
                } elseif ($index === count(
                        $this->brand_path
                    ) - 1 && $next_layer === 'product' && $products_id = $this->getProductsId($url_alias)) {
                    $product = ($this->view->product_list[$url_alias] ?? null);
                    if (isset($product['stock_status']) && ($product['stock_status'] == 'buy' || $product['stock_status'] == 'pre_order')) {
                        $this->view->products_id = $products_id;
                        $this->view->title = $product['title'];
                        $this->getProductsDescription($products_id);
                        $this->view->dtu_info = $this->getDirectTopUpField($products_id);
                        $final_price = Yii::app()->currency->parseStringToNumber(
                            Yii::app()->session['currency'],
                            $product['final_price']
                        );

                        $qty = (!empty($_GET['qty']) ? $_GET['qty'] : 1);
                        $this->total_price = $final_price * $qty;
                        $this->total_op = $product['op'] * $qty;
                        $this->view->setSelected('product', $url_alias);
                        $this->view->addBreadcrumb($valid_path);
                        $valid_path[] = $url_alias;
                    } else {
                        throw new Exception('Invalid Product', 300);
                    }
                } else {
                    throw new Exception('Invalid Product', 300);
                }
            }
        } catch (\Exception $e) {
            if (empty($valid_path)) {
                throw new CHttpException(404, 'Invalid Brand');
            }
            $new_url = Yii::app()->createUrl("brand/index", ['path' => $valid_path]);
            Yii::app()->request->redirect($new_url, true);
        }

        $this->current_path = $valid_path;

        return true;
    }

    public function getProductsId($url_alias)
    {
        $pid = Yii::app()->frontPageCom->getProductObj()->getProductIDByUrlAlias($url_alias);
        return $pid;
    }

    public function getSupportedProductList($url_alias)
    {
        $data = (new MsProductModel)->getCategoryInfo(
            $url_alias,
            Yii::app()->session['language_id'],
            Yii::app()->session['country_code'],
            Yii::app()->session['country_code']
        );
        if (isset($data['categories_id'])) {
            foreach (Yii::app()->searchBoxCom->getSupportedProduct([$data['categories_id']]) as $product) {
                if ($product['products_type'] == 2) {
                    continue;
                }
                $product = $this->getProductInfo($product);
                switch ($product['stock_status']) {
                    case 'pre_order':
                    case 'buy':
                        $this->view->no_supported_product = false;
                        $sort_order = 1;
                        break;
                    case 'out_of_stock':
                        $sort_order = 99;
                        break;
                }
                $product['display_order'] = $sort_order;
                $this->view->product_list[$product['url_alias']] = $product;
            }

            uasort($this->view->product_list, function ($a, $b) {
                $retval = $a['display_order'] <=> $b['display_order'];
                if ($retval == 0) {
                    $retval = ($a['bundle_sort'] <=> $b['bundle_sort']);
                }
                if ($retval == 0) {
                    $retval = $a['sort_order'] <=> $b['sort_order'];
                }
                if ($retval == 0) {
                    $retval = $a['title'] <=> $b['title'];
                }
                return $retval;
            });

            $this->view->setAttributes($data, false);

            $list = array_merge($this->view->product_list, $this->view->out_of_stock_list);

            $this->view->addSelection('product', $list);
        }
    }

    public function getSupportedProductListByCat($categories_id)
    {
        if ($categories_id) {
            foreach (Yii::app()->searchBoxCom->getSupportedProduct([$categories_id]) as $product) {
                if ($product['products_type'] == 2) {
                    continue;
                }
                $product = $this->getProductInfo($product);
                switch ($product['stock_status']) {
                    case 'pre_order':
                    case 'buy':
                        $this->view->no_supported_product = false;
                        $sort_order = 1;
                        break;
                    case 'out_of_stock':
                        $sort_order = 99;
                        break;
                }
                $product['display_order'] = $sort_order;
                $product['raw_final_price'] = Yii::app()->currency->parseStringToNumber(
                    Yii::app()->session['currency'],
                    $product['final_price']
                );
                $this->view->product_list[$product['url_alias']] = $product;
            }

            // uasort($this->view->product_list, function ($a, $b) {
            //     $retval = $a['display_order'] <=> $b['display_order'];
            //     if ($retval == 0) {
            //         $retval = ($a['bundle_sort'] <=> $b['bundle_sort']);
            //     }
            //     if ($retval == 0) {
            //         $retval = $a['sort_order'] <=> $b['sort_order'];
            //     }
            //     if ($retval == 0) {
            //         $retval = $a['title'] <=> $b['title'];
            //     }
            //     return $retval;
            // });

            // $this->view->setAttributes($data, false);

            // $list = array_merge($this->view->product_list, $this->view->out_of_stock_list);

            // $this->view->addSelection('product', $list);
            return $this->view->product_list;
        }
    }

    public function getProductInfo($product)
    {
        $image_url = '';
        if ($product['products_type'] == 4) {
            $image_url = $product['image']['dimension'][0];
        }
        return [
            'products_id' => $product['products_id'],
            'title' => $product['name'],
            'stock_status' => $product['delivery_status']['type'],
            'date_available' => $product['date_available_string'],
            'url_alias' => $product['products_url_alias'],
            'price' => ($product['price'] == $product['original_price'] ? '' : $product['original_price']),
            'final_price' => $product['price'],
            'sort_order' => ($product['products_sort_order'] ?: 0),
            'raw_price' => Yii::app()->currency->parseStringToNumber(
                Yii::app()->session['currency'],
                $product['price']
            ),
            'op' => $product['op'],
            'image_url' => $image_url,
            'bundle_sort' => (!empty($product['products_bundle']) ? 0 : 1)
        ];
    }

    public function getPrimaryBrandInfoByUrl($url_alias)
    {
        $data = (new MsProductModel)->getPrimaryBrandInfo(
            $url_alias,
            Yii::app()->session['language_id'],
            Yii::app()->session['country_code'],
            Yii::app()->frontPageCom->customer_ip_country
        );

        if (empty($data['supported_item'])) {
            throw new CHttpException(404, 'Invalid Brand');
        }

        $this->supported_item = $data['supported_item'];

        $this->view->setAttributes($data, false);
        $this->view->brand_title = $this->view->title;

        $this->view->addSelection('brand', $data['supported_item']);

        if (!empty($data['supported_category']) && isset($data['supported_item'][$url_alias])) {
            $this->supported_item = $data['supported_category'];
            $this->view->addSelection('category', $data['supported_category']);
            $this->view->setSelected('brand', $url_alias);
            $this->checkSupportedCategory($data);
        }

        return $data;
    }

    public function getBrandInfoByUrl($main_url_alias, $url_alias)
    {
        $data = (new MsProductModel)->getSubBrandInfo(
            $main_url_alias,
            $url_alias,
            Yii::app()->session['language_id'],
            Yii::app()->session['country_code'],
            Yii::app()->frontPageCom->customer_ip_country
        );

        $this->supported_item = $data['supported_item'];

        $this->view->setAttributes($data, false);

        $this->view->addSelection('category', $data['supported_item']);

        $this->checkSupportedCategory($data);

        return true;
    }

    public function checkSupportedCategory($data)
    {
        if (!empty($data['supported_category'])) {
            $first_item = array_key_first($data['supported_category']);
            if (isset($data['supported_category'][$first_item]['soft_block']) && $data['supported_category'][$first_item]['soft_block'] == 0) {
                $this->view->no_supported_category = false;
            }
        } elseif (!empty($data['supported_item'])) {
            $first_item = array_key_first($data['supported_item']);
            if (isset($data['supported_item'][$first_item]['soft_block']) && $data['supported_item'][$first_item]['soft_block'] == 0) {
                $this->view->no_supported_category = false;
            }
        }
    }

    public function getProductsDescription($products_id, $products_type = 0)
    {
        $this->product_content_obj = new FrontendPageContentCom();
        $this->product_content_obj->_init($products_id, 1);

        $this->view->setAttributes([
            'description' => nl2br($this->product_content_obj->getGameDetailInfo('description', '')),
            'notice' => $this->product_content_obj->getGameDetailInfo('notice', '')
        ], false);
    }

    public function getData()
    {
        $view = $this->view;

        return [
            'login_url' => Yii::app()->frontPageCom->getLoginURL(),
            'current_url' => Yii::app()->createUrl('brand/index', ['path' => $this->current_path]),
            'short_description' => $view->short_description,
            'description' => $view->description,
            'review' => [],
            'supported_item' => $this->supported_item,
            'product_type' => $this->getProductObj()->getProductInfo('products_type'),
        ];
    }

    public function getProductObj()
    {
        return Yii::app()->frontPageCom->getProductObj();
    }

    public function getDirectTopUpField($products_id)
    {
        $str = '';
        $default_dm = '';
        $delivery_methods_array = [];
        $product_delivery_mode_array = [];
        $this->getProductObj()->setProductID($products_id);
        $product_delivery_mode = $this->getProductObj()->getProductDeliveryMode($products_id);
        $product_type = $this->getProductObj()->getProductInfo('products_type');
        $delivery_mode = 6;

        foreach ($product_delivery_mode as $dm_id) {
            $product_delivery_mode_array[$dm_id] = [];
        }

        $products_delivery_mode_array = $this->getProductObj()->getProductDeliveryModeTitleByArrayID(
            $product_delivery_mode
        );

        foreach ($products_delivery_mode_array as $products_delivery_mode_row) {
            $product_delivery_mode_array[$products_delivery_mode_row['products_delivery_mode_id']] = $products_delivery_mode_row['products_delivery_mode_title'];
        }

        if (notNull($delivery_mode) && count($product_delivery_mode_array)) {
            switch ($delivery_mode) {
                case 6 : // DTU
                    if (isset($product_delivery_mode_array['6'])) {
                        $product_delivery_mode_array = ['6' => $product_delivery_mode_array['6']];
                    }
                    break;
                default : // General
                    unset($product_delivery_mode_array['6']);
                    break;
            }
        }

        if (count($product_delivery_mode_array)) {
            foreach ($product_delivery_mode_array as $dm_id => $product_delivery_mode_data_loop) {
                $default_dm = $default_dm == '' ? $dm_id : $default_dm;
                $display_delivery_mode_label_array = [];

                if ($product_type == 4) {
                    $this->view->product_type = 4;

                    $field_arrays = [
                        [
                            'id' => 'recipient_name',
                            'caption' => 'Name',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'contact_number',
                            'caption' => 'Contact Number',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'addr_1',
                            'caption' => 'Address 1',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'addr_2',
                            'caption' => 'Address 2',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'postcode',
                            'caption' => 'Zip Code',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'city',
                            'caption' => 'City',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'state',
                            'caption' => 'State',
                            'type' => $this->getStateFieldType(),
                            'readonly' => false,
                            'max_length' => '20',
                            'value' => $this->state_list,
                        ],
                        [
                            'id' => 'country_name',
                            'caption' => 'Country',
                            'type' => 'text',
                            'max_length' => '20',
                            'readonly' => true,
                            'value' => Yii::app()->session['country_name'],
                        ],
                    ];
                    foreach ($field_arrays as $field) {
                        $str .= '<div> <div> <label class="text-xs font-bold">' . $field['caption'] . '</label> </div> <div> ';
                        switch ($field['type']) {
                            case 'text':
                                $str .= CHtml::textField(
                                    "deliver_addr[" . $field['id'] . "]",
                                    ($field['value'] ?? ''),
                                    [
                                        'maxlength' => $field['max_length'],
                                        'size' => '20',
                                        'readonly' => $field['readonly'],
                                        'placeholder' => $field['caption'],
                                        'class' => 'text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full border px-5 py-3 border border-gray-200',
                                    ]
                                );
                                break;
                            case 'dropdown':
                                $str .= CHtml::dropDownList(
                                    "deliver_addr[" . $field['id'] . "]",
                                    ($arr[$field['id']] ?? ''),
                                    $field['value'],
                                    [
                                        'encode' => false,
                                        'default_text' => Yii::t('checkout', 'PULL_DOWN_DEFAULT'),
                                        'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                        'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                        'class' => 'text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full border px-5 py-3 border border-gray-200',
                                    ]
                                );
                                break;
                        }
                        $str .= '</div></div>';
                    }
                } else {
                    switch ($dm_id) {
                        case '6':
                            $this->view->product_type = 2;
                            $direct_topup_obj = new DirectTopupCom();
                            $main_product_id = $products_id;
                            $product_info_array = $this->getProductObj()->getProductBundleInfoByID();

                            if ($product_info_array['actual']['products_bundle'] == 'yes' || $product_info_array['actual']['products_bundle_dynamic'] == 'yes') {
                                $main_product_id = PublishersProducts::model()->getBundleProductID($products_id);
                                $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
                            } else {
                                $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
                            }

                            if (count($game_input_array)) {
                                // Get Prefill DTU info
                                $prefill_dtu_info_array = $this->get_dtu_game_info($products_id);
                                // Get Prefill DTU info

                                foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
                                    $top_up_info_key = $game_input_data_loop['top_up_info_key'];
                                    $dtu_field_title = ($game_input_data_loop['top_up_info_display'] != '' ? $game_input_data_loop['top_up_info_display'] : '');
                                    $default_info = isset($prefill_dtu_info_array[$top_up_info_key]) && notEmpty(
                                        $prefill_dtu_info_array[$top_up_info_key]
                                    ) ? htmlentities(
                                        htmlspecialchars_decode($prefill_dtu_info_array[$top_up_info_key]),
                                        ENT_COMPAT | ENT_QUOTES
                                    ) : '';
                                    $str .= '<div> <div> <label class="text-xs font-bold">' . $dtu_field_title . '</label> </div> <div> ';
                                    switch ($top_up_info_key) {
                                        case 'server':
                                            // server list
                                            $publishers_server = PublishersGames::model()->getServerByProductID(
                                                $main_product_id
                                            );
                                            $servers_tmp_array = json_decode($publishers_server, 1);
                                            $servers_array = [];
                                            $servers_array[''] = $dtu_field_title;

                                            if (isset($servers_tmp_array)) {
                                                foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
                                                    $servers_array[$servers_id_loop] = trim($server_name_loop);
                                                }
                                            }

                                            $str .= CHtml::dropDownList(
                                                "game_info[" . $game_input_key_loop . "]",
                                                $default_info,
                                                $servers_array,
                                                [
                                                    'encode' => false,
                                                    'default_text' => '',
                                                    'class' => 'text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full border px-5 py-3 border border-gray-200'
                                                ]
                                            );
                                            break;
                                        case 'account_platform':
                                            // account platform
                                            if (!$direct_topup_obj->used_as_text_field($main_product_id)) {
                                                $account_platform_tmp_array = $direct_topup_obj->get_account_platform(
                                                    $main_product_id
                                                );
                                                $account_platform_array = [];
                                                $account_platform_array[''] = $dtu_field_title;

                                                if (isset($account_platform_tmp_array)) {
                                                    foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
                                                        $account_platform_array[$account_platform_id_loop] = trim(
                                                            $account_platform_name_loop
                                                        );
                                                    }
                                                }

                                                $str .= CHtml::dropDownList(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    $account_platform_array,
                                                    [
                                                        'encode' => false,
                                                        'default_text' => Yii::t(
                                                            'checkout',
                                                            'PULL_DOWN_DEFAULT'
                                                        ),
                                                        'class' => 'text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full border px-5 py-3 border border-gray-200',
                                                    ]
                                                );
                                            } else {
                                                $str .= CHtml::textField(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    [
                                                        'maxlength' => '64',
                                                        'size' => '20',
                                                        'style' => (isset($_REQUEST['sub_products_id']) ? 'display:none' : false),
                                                        'default_text' => '',
                                                        'placeholder' => $dtu_field_title,
                                                        'class' => 'px-5 py-3 border w-full border-gray-200 text-xs',
                                                    ]
                                                );
                                            }
                                            break;
                                        case 'character':
                                            if ($direct_topup_obj->character_is_sync($main_product_id)) {
                                                $str .= CHtml::dropDownList(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    [],
                                                    [
                                                        'encode' => false,
                                                        'default_text' => '',
                                                        'class' => 'text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full border px-5 py-3 border border-gray-200',
                                                    ],
                                                );
                                                $str .= '<button type="button" class="products-buy-now active my-3" onclick="load_character_list()">Get Character List</button>';
                                            }
                                            else {
                                                $str .= CHtml::textField(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    [
                                                        'maxlength' => '64',
                                                        'size' => '20',
                                                        'default_text' => '',
                                                        'placeholder' => $dtu_field_title,
                                                        'class' => 'px-5 py-3 border w-full border-gray-200 text-xs',
                                                    ]
                                                );
                                            }
                                            break;

                                        default: // account
                                            $str .= CHtml::textField(
                                                "game_info[" . $game_input_key_loop . "]",
                                                (isset($_REQUEST['sub_products_id']) && !empty($_GET['account']) ? $_GET['account'] : $default_info),
                                                [
                                                    'maxlength' => '64',
                                                    'size' => '18',
                                                    'encode' => false,
                                                    'default_text' => '',
                                                    'placeholder' => $dtu_field_title,
                                                    'class' => 'px-5 py-3 border w-full border-gray-200 text-xs',
                                                ]
                                            );
                                            break;
                                    }
                                    $str .= '</div></div>';
                                }
                            }

                            break;
                    }
                }
                $delivery_methods_array[$dm_id] = implode("", $display_delivery_mode_label_array);
            }
        }

        return $str;
    }

    public function get_dtu_game_info($productID)
    {
        $cookie_array = [];

        $prefill_game_info = isset(Yii::app()->request->cookies['DTU']) ? Yii::app()->request->cookies['DTU']->value : [];
        $gameID = notEmpty($productID) ? $this->getProductObj()->getGameIDByID($productID) : 0;

        if (isset($prefill_game_info[$gameID])) {
            $temp_array = json_decode(($prefill_game_info[$gameID]), true);

            if (is_array($temp_array) && count($temp_array)) {
                foreach ($temp_array as $field => $value) {
                    $value = htmlspecialchars($value);
                    $field = validateRequest(htmlspecialchars($field));

                    if (notEmpty($value) && notEmpty($field)) {
                        $cookie_array[$field] = $value;
                    }
                }
            }

            unset($temp_array);
        }

        return $cookie_array;
    }

    public function getStateFieldType()
    {
        $states = $this->getStates();
        if (empty($states)) {
            $this->state_list = "";
            return 'text';
        }
        $this->state_list = $states;

        return 'dropdown';
    }

    public function getStates()
    {
        $zones_model = new Zones();
        $states = $zones_model->getStatesList(Yii::app()->session['country']);

        return $states;
    }
}