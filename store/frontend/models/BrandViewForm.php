<?php

class BrandViewForm extends CFormModel
{
    public $brand_title;
    public $title;
    public $supported_type = [];
    public $image_url;
    public $short_description;
    public $description;
    public $notice;
    public $meta_title;
    public $meta_description = '';
    public $meta_keyword = '';
    public $review;
    public $review_score = '-';
    public $total_review;
    public $review_page = 1;
    public $info;
    public $supported_item = [];
    public $listing = [];
    public $product_list = [];
    public $out_of_stock_list = [];
    public $selected = [];
    public $show_region = false;
    public $dtu_info = '';
    public $product_type = '';
    public $soft_block_country = false;
    public $products_id = 0;
    public $brand_url = '';
    public $no_supported_category = true;
    public $no_supported_product = true;
    public $selected_type = 'brand';
    public $last_selected_url = '';
    public $breadcrumb = [];

    public function addSelection($type, $supported_item)
    {
        $this->listing[$type] = [
            'type' => $type,
            'selected_title' => '',
            'value' => null,
            'supported_item' => $supported_item
        ];
    }

    public function setSelected($type, $url_alias)
    {
        $this->listing[$type]['value'] = $url_alias;
        $this->listing[$type]['title'] = $this->listing[$type]['supported_item'][$url_alias]['title'];
        $this->selected_type = $type;
        $this->last_selected_url = $url_alias;
    }

    public function showBrandRegion()
    {
        $this->show_region = true;
    }

    public function reset()
    {
        $this->show_region = false;
        $this->selected = [];
        $this->supported_item = [];
    }

    public function setAttributes($values, $safeOnly = true)
    {
        foreach ($values as $index => $value) {
            if (empty($value)) {
                unset($values[$index]);
            }
        }
        parent::setAttributes($values, $safeOnly);
    }

    public function getReview($page = 1, $type = 'brand')
    {
        $review = (new MsProductModel)->getReviewByBrandId($type, $this->brand_url, $page, 10);

        $this->review_page = $page;

        if (!empty($review['summary'])) {
            $this->review_score = (!empty($review['summary']['avg_score']) ? $review['summary']['avg_score'] : 'N/A');
            $this->review = ($review['reviews'] ?? []);
            $this->total_review = ($review['summary']['total'] ?? 0);
        }
    }

    public function getSupportedTypeSvg()
    {
        $return_str = '';
        $type = ['voucher', 'dtu', 'game_key', 'mobile_recharge', 'physical'];
        foreach ($this->supported_type as $type_id) {
            if (ctype_digit($type_id) === false) {
                continue;
            }
            $return_str .= <<<TYPE
                    <span class="mr-2">
                        <svg class="svg-base">
                            <use xlink:href="/offgamers.svg?v=4#{$type[$type_id - 1]}"></use>
                        </svg>
                    </span>
TYPE;
        }
        return $return_str;
    }

    public function addBreadcrumb($path)
    {
        $this->breadcrumb[] = [
            'title' => $this->title,
            'url' => Yii::app()->createAbsoluteUrl(
                "brand/index",
                ['x-country' => true, 'path' => $path]
            )
        ];
    }

    public function getBreadcrumbSnippet()
    {
        $list = [];
        foreach ($this->breadcrumb as $index => $item) {
            $list[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $item['title'],
                "item" => $item['url']
            ];
        }
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => $list
        ];
        return '<script type="application/ld+json">' . json_encode($schema, JSON_INVALID_UTF8_IGNORE) . '</script>';
    }

    public function getReviewSnippet()
    {
        if (!empty($this->total_review)) {
            $schema = [
                "@context" => "https://schema.org/",
                "@type" => "Product",
                "name" => $this->title,
                "url" => strip_tags(Yii::app()->createAbsoluteUrl(Yii::app()->request->url)),
                "image" => $this->image_url,
                "description" => $this->meta_description
            ];
            $review_list = [];
            foreach ($this->review as $review) {
                $rating_score = (int)$review['review_score'];
                $reviewer_names = Customers::getCustomersName($review['created_by']);
                if (empty($reviewer_names) || (empty($reviewer_names->customers_firstname) && empty($reviewer_names->customers_lastname))) {
                    $author = 'Anonymous';
                } else {
                    $author = $reviewer_names->customers_firstname . ' ' . $reviewer_names->customers_lastname;
                }
                $review_list[] = [
                    '@type' => 'Review',
                    'author' => [
                        '@type' => 'Person',
                        'name' => $author,
                    ],
                    'datePublished' => date('y-M-d', $review['created_at']),
                    'reviewRating' => [
                        '@type' => 'Rating',
                        'ratingValue' => $rating_score,
                    ]
                ];
            }
            $schema['review'] = $review_list;
            $schema['aggregateRating'] = [
                '@type' => 'AggregateRating',
                'ratingValue' => $this->review_score,
                'bestRating' => '5',
                'ratingCount' => $this->total_review
            ];
            return '<script type="application/ld+json">' . json_encode($schema, JSON_INVALID_UTF8_IGNORE) . '</script>';
        }
        return '';
    }
}