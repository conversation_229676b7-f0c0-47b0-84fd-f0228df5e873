<?php

class Categories extends CategoriesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Categories the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function storeCreditProductID($grpid) {
        $result = false;
        
        $c = Yii::app()->db;
        $sql = "SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
                FROM " . Categories::model()->tableName() . " AS c 
                INNER JOIN " . CategoriesGroups::model()->tableName() . " AS cg 
                    ON (c.categories_id = cg.categories_id) 
                WHERE c.categories_status = 1 
                    AND c.custom_products_type_id = '3'
                    AND ((cg.groups_id = '" . (int) $grpid . "') OR (cg.groups_id = 0)) 
                ORDER BY c.categories_id";
        $res = $c->createCommand($sql)->query();
        if (($row = $res->read()) !== false) {
            if ($row["parent_id"] > 0) {
                $cPath = substr($row["categories_parent_path"], 1) . $row["categories_id"];
            } else {
                $cPath = $row["categories_id"];
            }

            $cat = explode("_", $cPath);
            $prod_sql = "   SELECT DISTINCT(p.products_id)
                            FROM " . Products::model()->tableName() . " AS p
                            INNER JOIN " . ProductsToCategories::model()->tableName() . " AS ptc
                                ON (p.products_id = ptc.products_id)
                            INNER JOIN " . CategoriesGroups::model()->tableName() . " AS cg
                                ON (cg.categories_id = ptc.categories_id)
                            INNER JOIN " . Categories::model()->tableName() . " AS c
                                ON (c.categories_id = ptc.categories_id AND c.categories_status = 1)
                            WHERE ptc.categories_id IN ('" . implode("', '", $cat) . "')
                                AND p.custom_products_type_id = 3
                                AND p.products_status = 1
                                AND p.products_display = 1
                                AND ((cg.groups_id = '" . (int) $grpid . "') OR (cg.groups_id = 0))
                            ORDER BY p.products_sort_order";
            $prod_res = $c->createCommand($prod_sql)->query();
            if (($prod_row = $prod_res->read()) !== false) {
                $result = $prod_row["products_id"];
            }
        }

        return $result;
    }
}