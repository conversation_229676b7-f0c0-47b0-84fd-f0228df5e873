<?php

/**
 * This is the model class for table "categories_brand_list".
 *
 * The followings are the available columns in table 'categories_brand_list':
 * @property integer $categories_brand_list_id
 * @property integer $categories_id
 * @property integer $brand_id

 */
class CategoriesBrandList extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AddressBookBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getDbConnection()
    {
        return Yii::app()->db_og;
    }

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'categories_brand_list';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('categories_brand_list_id, categories_id, brand_id', 'numerical', 'integerOnly'=>true),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'categories_brand_list_id' => 'Categories Brand List Id',
			'categories_id' => 'Categories Id',
			'brand_id' => 'Brand ID'
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('categories_brand_list_id',$this->categories_brand_list_id);
		$criteria->compare('categories_id',$this->categories_id, true);
		$criteria->compare('brand_id',$this->brand_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function getGameSetting($productData) {
		$payload = array(
			"product_id" => $productData->products_id,
			"region" => "GLOBAL",
			"service" => "gc",
			"product_name" => [],
			"brand_id_old" => "",
			"brand_id_new" => "",
			"brand_name" => [],
			"brand_seo_url" => "",
			"brand_sort_order" => "",
			"brand_synonyms" => "",
			"brand_description" => [
				"notice" => [],
				"short_description" => [],
				"description" => [],
			],
			"brand_meta" => [
				"meta_title" => [],
				"meta_keyword" => [],
				"meta_description" => [],
			],
			"marketing_title" => [],
			"soft_block" => [],
			"category_id" => "",
			"category_seo_url" => "",
			"category_sort_order" => "",
			"top_up_info" => []
		);

		$old_brand_id = $productData->products_main_cat_id;
		$brand_list = $this->findByAttributes(array('categories_id' => $old_brand_id));
		if($brand_list){
			$products_cat_id_path = $productData->products_cat_id_path;
			$str = trim($products_cat_id_path, '_');
            if ($cat_arr = explode('_', $str)) {

				$criteria = new CDbCriteria;
				$criteria->select = 'categories_id';
				$criteria->condition = 'categories_status = 0';
				$criteria->addInCondition('categories_id', $cat_arr);
				$result = Categories::model()->exists($criteria);
				if($result){
					return false;
				}
				

                if (isset($cat_arr[1])) {
                    $cat_id = (int)$cat_arr[1];
					$payload["category_id"] =$cat_id;
                }
            }
			if(isset($cat_id)){
				$db = Yii::app()->db;
				$db_og = Yii::app()->db_og;
				$sql = "SELECT * FROM languages";
				$result = $db->createCommand($sql)->queryAll();
				$language_list = [];
				if($result){
					foreach($result as $value){
						$language_list[$value["languages_id"]] = strtolower($value["code"]);
					}
				}

				//get product name
				$sql = "SELECT * FROM products_description WHERE products_id = $productData->products_id";
				$result = $db->createCommand($sql)->queryAll();
				if($result){
					foreach($result as $value){
						if(isset($language_list[$value["language_id"]])){
							$payload["product_name"][$language_list[$value["language_id"]]] = $value["products_name"];
						}
					}
				}

				//get new_brand_id
				$new_brand_id = $brand_list->brand_id;
				$payload["brand_id_old"] =$old_brand_id;
				$payload["brand_id_new"] =$new_brand_id;

				//get brand data
				$_sql = "SELECT * FROM brand WHERE brand_id = $new_brand_id";
				$result = $db_og->createCommand($_sql)->queryRow();
				if($result){
					$payload["brand_seo_url"] = $result["seo_url"];
					$payload["brand_sort_order"] = $result["sort_order"];
					$payload["brand_synonyms"] = $result["search_keyword"];
				}

				//get new category_id
				$_sql = "SELECT * FROM category WHERE brand_id = $new_brand_id AND categories_id = $cat_id";
				$result = $db_og->createCommand($_sql)->queryRow();
				if($result){
					$new_cat_id = $result["category_id"];
					$payload["category_seo_url"] = $result["seo_url"];
					$payload["category_sort_order"] = $result["sort_order"];
				} else {
					return false;
				}

				//get brand name, notice, short_description, description
				$sql = "SELECT * FROM brand_description WHERE brand_id = $new_brand_id";
				$result = $db_og->createCommand($sql)->queryAll();
				if($result){
					foreach($result as $value){
						if(isset($language_list[$value["language_id"]])){
							$payload["brand_name"][$language_list[$value["language_id"]]] = $value["name"];
							$payload["brand_description"]["notice"][$language_list[$value["language_id"]]] = $value["notice"];
							$payload["brand_description"]["short_description"][$language_list[$value["language_id"]]] = $value["short_description"];
							$payload["brand_description"]["description"][$language_list[$value["language_id"]]] = $value["description"];
						}
					}
				}

				//get SEO title, keyword, description
				$sql = "SELECT * FROM brand_metadata WHERE brand_id = $new_brand_id";
				$result = $db_og->createCommand($sql)->queryAll();
				if($result){
					foreach($result as $value){
						if(isset($language_list[$value["language_id"]])){
							$payload["brand_meta"]["meta_title"][$language_list[$value["language_id"]]] = $value["meta_title"];
							$payload["brand_meta"]["meta_keyword"][$language_list[$value["language_id"]]] = $value["meta_keyword"];
							$payload["brand_meta"]["meta_description"][$language_list[$value["language_id"]]] = $value["meta_description"];
						}
					}
				}
				//do not yet game_info (geme_genre, platform and etc...)
				
				//get 2nd layer (region and soft block) from brand_category, og->category categories_id + brand_id to get new category_id the join category_soft_block.category_id
				//based on products `products_cat_id_path` 2nd layer
				$sql = "SELECT * FROM category_soft_block WHERE category_id = $new_cat_id";
				$result = $db_og->createCommand($sql)->queryAll();
				if($result){
					foreach($result as $value){
						$payload["soft_block"][] = $value["country_code"];
					}
				}
				//get category marketing title (category_description)
				$sql = "SELECT * FROM category_description  WHERE category_id = $new_cat_id";
				$result = $db_og->createCommand($sql)->queryAll();
				if($result){
					foreach($result as $value){
						if(isset($language_list[$value["language_id"]])){
							$payload["marketing_title"][$language_list[$value["language_id"]]] = $value["name"];

							if($language_list[$value["language_id"]] == "en"){
								// Use preg_match to find the last set of brackets and extract the value
								if (preg_match('/\(([^)]+)\)$/', $value["name"], $matches)) {
									$supported_regions = explode("/", $matches[1]);
									$supported_regions_new = [];
									foreach($supported_regions as $supported_region){
										if(trim($supported_region)){
											$supported_regions_new[] = strtoupper(trim($supported_region));
										}
									}
									asort($supported_regions_new);
									$payload["region"] = implode("/",$supported_regions_new);
								} 
							}
						}
					}
				}
				
				//get brand soft block
				if(!$payload["soft_block"]){
					$sql = "SELECT * FROM brand_soft_block WHERE brand_id = $new_brand_id";
					$result = $db_og->createCommand($sql)->queryAll();
					if($result){
						foreach($result as $value){
							$payload["soft_block"][] = $value["country_code"];
						}
					}
				}

				//get service type, detect got publisher topup info data or not
				$sql = "SELECT * FROM top_up_info WHERE products_id = $productData->products_id";
				$result = $db->createCommand($sql)->queryAll();
				if($result){
					$payload["service"] = "dtu";
					foreach($result as $top_up_info){
						$top_up_info_id = $top_up_info["top_up_info_id"];
						$payload["top_up_info"][$top_up_info_id] = $top_up_info;
						$payload["top_up_info"][$top_up_info_id]["lang"] = [];
						$topupInfoLang = TopUpInfoLangBase::model()->findAllByAttributes(array("top_up_info_id" =>$top_up_info_id));
						foreach($topupInfoLang as $topupInfoLangValue){
							if(isset($language_list[$topupInfoLangValue->languages_id])){
								$payload["top_up_info"][$top_up_info_id]["lang"][$language_list[$topupInfoLangValue->languages_id]] = $topupInfoLangValue->top_up_info_display;
							}
						}
					}
				}
			} else {
				return false;
			}
		} else {
			return false;
		}

		return $payload;
    }

}