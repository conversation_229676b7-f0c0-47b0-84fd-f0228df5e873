<?php

/**
 * This is the model class for table "categories_groups".
 *
 * The followings are the available columns in table 'categories_groups':
 * @property integer $linkid
 * @property integer $categories_id
 * @property integer $groups_id
 */
class CategoriesGroups extends CategoriesGroupsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}