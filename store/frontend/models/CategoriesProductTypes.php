<?php

/**
 * This is the model class for table "categories_product_types".
 *
 * The followings are the available columns in table 'categories_product_types':
 * @property integer $categories_id
 * @property integer $custom_products_type_id
 * @property integer $custom_products_type_child_id
 */
class CategoriesProductTypes extends CategoriesProductTypesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesProductTypesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}