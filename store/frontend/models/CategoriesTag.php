<?php

/**
 * This is the model class for table "categories_tag".
 *
 * The followings are the available columns in table 'categories_tag':
 * @property integer $tag_id
 * @property string $tag_label
 * @property string $tag_desc
 * @property integer $tag_lft
 * @property integer $tag_rgt
 */
class CategoriesTag extends CategoriesTagBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesTagBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}