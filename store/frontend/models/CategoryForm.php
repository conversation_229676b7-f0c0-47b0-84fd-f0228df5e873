<?php

class CategoryForm extends CFormModel {

    const TEMPLATE_ID_TYPE = 0;

    private $product_array,
            $tag_info = array(),
            $google_service_enabled = false;

    public  $category_id,
            $item_per_page,
            $total_available_items,
            $page, $offset, $product_reviews,
            $error_soft_block = false;

    public function __construct($request_array) {
        $this->category_id = isset($request_array['cid']) ? (int) $request_array['cid'] : 0;
        $this->google_service_enabled = isset($request_array['google_enabled']) ? $request_array['google_enabled'] : false;
        Yii::app()->frontPageCom->getPageContentObj()->_init($this->category_id, self::TEMPLATE_ID_TYPE);

        try {
            if ($this->product_array = Yii::app()->searchBoxCom->getProductListByGameID(array($this->category_id))) {
                // Check for soft blocking
                if (Yii::app()->searchBoxCom->filterSoftBlocking(array($this->category_id))) {
                    $this->error_soft_block = true;
                }

                if (!$this->tag_info = Yii::app()->cTagCom->getSinglePathByGameID($this->category_id)) {
                    // tag does not found / inactived
                    throw new Exception('ERROR_CATEGORY_TAG_NOT_FOUND');
                }

            } else {
                // Soft block the page
                $this->error_soft_block = true;
            }

            $this->tag_info[] = Yii::app()->frontPageCom->getCategoryObj()->getName($this->category_id);
            Yii::app()->frontPageCom->getPageContentObj()->getAllInfo();
            
        } catch (Exception $e) {
            // hav to log the category that has no product reason here
            throw new CHttpException(300, $e->getMessage());
        }
    }

    public function getAFNotice() {
        $cookie_key = 'notice_alert_cookie';
        $return_int = Yii::app()->frontPageCom->getPageContentObj()->getGameValue('af_notice_enable_status', '0');

        if (isset(Yii::app()->request->cookies[$cookie_key])) {
            $val_array = explode(',', htmlspecialchars(Yii::app()->request->cookies[$cookie_key]->value));
            $return_int = in_array('c' . $this->category_id, $val_array) ? '0' : $return_int;
        }
        
        return $return_int;
    }
    
    public function getBackgroundColor() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameValue('background_color', '#fff');   //transparent
    }

    public function getBackgroundImage() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameValue('background_source', '');
    }

    public function getBreadcrumbsWidgetContent($link = '') {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return array(
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
            'htmlOptions' => array('class'=>'breadcrumbs  breadcrumbs--decor')
        );
    }

    public function getBreadcrumbs() {
        $return_array = array();

        if ($tag_array = $this->getTagInfo()) {
            $current_title = array_pop($tag_array);

            $return_array[Yii::t('page_content', 'MEGA_MENU_GAME_CARD')] = array('search/index' , 'keyword' => Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE'));

            $return_array[] = $current_title;
        }

        return $return_array;
    }

    public function getDescription() {
        return str_replace("\n", "<br>", Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('description', ''));
    }

    public function getGameInfoList() {
        $return_array = array();
        $game_info_array = Yii::app()->frontPageCom->getPageContentObj()->getGameInfo();

        foreach ($game_info_array as $key => $game_array) {
            $return_array[] = array(
                'label' => Yii::t('page_content', 'GAME_INFO_' . strtoupper($key)),
                'value' => is_array($game_array) ? implode(", ", $game_array) : $game_array
            );
        }

        return $return_array;
    }

    public function getLogoImageInfo() {
        $return_array = array();

        if ($url = Yii::app()->frontPageCom->getPageContentObj()->getGameValue('logo_source', '')) {
            $return_array = array(
                'attributes_info' => array(
                    'src' => $url
                )
            );
        }

        return $return_array;
    }

    public function getNotice() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('notice', '');
    }

    public function getTitle() {
        $tagInfo = $this->getTagInfo();
        return end($tagInfo);
    }

    public function getRelatedLinkList() {
        $return_array = array();
        $game_info_array = Yii::app()->frontPageCom->getPageContentObj()->getRelatedLink();

        foreach ($game_info_array as $game_array) {
            $return_array[] = array(
                'label' => $game_array['label'],
                'href' => $game_array['link']
            );
        }

        return $return_array;
    }

    public function getResultSummary() {
        return '';
    }

    public function getSupportedGame() {
        return Yii::app()->frontPageCom->getPageContentObj()->getCategorySupportedGames();
    }

    public function getSystemRequirement() {
        return str_replace("\n", "<br>", Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('system_requirements', ''));
    }

    private function getTagInfo() {
        return $this->tag_info;
    }

    public function getItems() {
        $return_array = array();

        foreach ($this->product_array as $idx => $product) {
            if(!isset($product['products_last_modified']) || empty($product['products_last_modified'])){
                $last_modified = $product['pid'];
            }
            else{
                $last_modified = strtotime($product['products_last_modified']);
            }
            $return_array[] = array(
                'desc' => $product['name'],
                'price' => $product['price'],
                'op_info' => array(
                    'label' => '<span class="op_tooltips" title="' . Yii::t('page_content', 'TOOLTIPS_OFFGAMERS_POINTS') . '"></span>' . $product['op'],
                ),
                'more_detail_info' => array(
                    'href' => Yii::app()->createUrl('product/index', array('pid' => $product['pid'], 'dm' => $product['default_dm'], 'pm' => $product['promo_type'])),
                    'label' => Yii::t('buttons', 'LINK_MORE_DETALS'),
                ),
                'image_info' => array(
                    'attributes_info' => array(
                        'title' => $product['image']['title'],
                        'alt' => $product['image']['alt'],
                        'src' => (array_values(array_slice(explode("/",$product['image']['dimension'][0]), -1))[0] !== "no_product_image.png" ? $product['image']['dimension'][0]."?v=".$last_modified : $product['image']['dimension'][0]),
                    )
                ),
                'button_info' => array(
                    'type' => $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                    'label' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                    'attributes_info' => array(
                        'id' => 'c_' . $product['pid'] . $idx,
                    )
                ),
                'extra_info' => array(
                    'data-N' => array(
                        'data-id' => 'data_c_' . $product['pid'] . $idx,
                        'data-name' => htmlspecialchars($product['name']),
                        'data-type' => 'buy',
                        'data-pid' => $product['pid'],
                        'data-dtu' => $product['is_dtu_flag'],
                        'data-bundle' => $product['products_bundle'],
                        'data-dm' => $product['default_dm'],
                        'data-pm' => $product['promo_type'],
                    ),
                )
            );
        }

        return $return_array;
    }

    public function getProductReviews($page = 1, $limit = 6, $sort = '0')
    {
        $review_model = new OrderReviewModel();
        $products_review = array();
        if($this->category_id != '0') {
            $products_review = $review_model->getReviewsByCategoriesId($this->category_id, $page, $limit, $sort);
        }
        $this->product_reviews = $products_review;
    }

}
