<?php

/**
 * This is the model class for table "countries".
 *
 * The followings are the available columns in table 'countries':
 * @property integer $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property integer $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property integer $address_format_id
 * @property integer $maxmind_support
 * @property string $aft_risk_type
 * @property integer $countries_display
 * @property integer $telesign_support
 */
class Countries extends CountriesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getCountryByIso2($countries_iso_code_2){
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/country_by_iso2/' . $countries_iso_code_2;
        $cache_result = Yii::app()->cache->get($cache_key);
        if ($cache_result === false) {
            $cache_result = CountriesBase::model()->findByAttributes(array('countries_iso_code_2' => $countries_iso_code_2));
            Yii::app()->cache->set($cache_key, $cache_result, 604800);
        }
        return $cache_result;
    }

    public function getCountryIdByDialingCode($dialingCode) {
        $criteria = new CDbCriteria();
        $criteria->select = 'countries_id';
        $criteria->condition = 'countries_international_dialing_code = :dialingCode';
        $criteria->params = array(':dialingCode' => $dialingCode);
        $result = $this->model()->find($criteria);
        return isset($result->countries_id)?$result->countries_id:0;
    }
    
    public function getGSTCurrency($country) {
        $_result = '';

        $c = new CDbCriteria();
        $c->select = array('t.countries_id');
        $c->with = array(
            'otc' => array(
                'select' => array('otc.currency'),
                'condition' => 'otc.orders_tax_status = "1"',
                'on' => 'otc.country_code = t.countries_iso_code_2',
                'joinType' => 'INNER JOIN'
            )
        );
        $c->condition = 't.countries_id = :country';
        $c->params = array(
            ':country' => $country
        );
        $c->limit = 1;
        $m_oc_data = $this->model()->find($c);

        if (!empty($m_oc_data)) {
            $_result = $m_oc_data->otc->currency;
        }

        return $_result;
    }

    public function getGSTTaxInfo($country, $currency, $language_id) {
        $_result = array(
            'percentage' => 0,
            'title' => '',
            'display_title' => ''
        );

        $c = new CDbCriteria();
        $c->select = array('t.countries_id');
        $c->with = array(
            'otc' => array(
                'select' => array('otc.orders_tax_percentage'),
                'condition' => 'otc.orders_tax_status = "1" AND otc.currency = :cur',
                'params' => array(
                    ':cur' => $currency
                ),
                'on' => 'otc.country_code = t.countries_iso_code_2',
                'joinType' => 'INNER JOIN',
                'with' => array(
                    'otcd1' => array(
                        'select' => array('otcd1.orders_tax_title'),
                        'condition' => 'otcd1.language_id = :lang',
                        'params' => array(
                            ':lang' => 1    // default-language-id
                        ),
                        'on' => 'otcd1.orders_tax_id = otc.orders_tax_id',
                        'joinType' => 'INNER JOIN',
                        'through' => 'otc',
                    ),
                    'otcd2' => array(
                        'select' => array('otcd2.orders_tax_title'),
                        'condition' => 'otcd2.language_id = :lang',
                        'params' => array(
                            ':lang' => (notNull($language_id) ? $language_id : 1) // default-language-id
                        ),
                        'on' => 'otcd2.orders_tax_id = otc.orders_tax_id',
                        'joinType' => 'INNER JOIN',
                        'through' => 'otc',
                    )
                )
            ),
        );
        $c->condition = 't.countries_id = :country';
        $c->params = array(
            ':country' => $country
        );
        $m_oc_data = $this->model()->find($c);

        if (!empty($m_oc_data)) {
            if (notNull($m_oc_data->otc->orders_tax_percentage) && ($m_oc_data->otc->orders_tax_percentage > 0)) {
                $_result = array(
                    'percentage' => $m_oc_data->otc->orders_tax_percentage,
                    'title' => $m_oc_data->otc->otcd1[0]->orders_tax_title,
                    'display_title' => $m_oc_data->otc->otcd2[0]->orders_tax_title
                );
            }
        }

        return $_result;
    }
    
    public function getDialingInfo($countryId) {
        $criteria = new CDbCriteria;
        $criteria->select = 'countries_id, countries_international_dialing_code, countries_name';
        $criteria->condition = 'countries_id = :countryId';
        $criteria->params = array(':countryId' => $countryId);
        $result = $this->model()->find($criteria);
        return $result;
    }
    
    public function getCountries($country_id = '', $with_iso_codes = false) {
        $_data = array();

        $criteria = new CDbCriteria;

        if ($country_id != '') {
            if ($with_iso_codes == true) {
                $criteria->select = 't.countries_name, t.countries_iso_code_2, t.countries_iso_code_3';
                $criteria->condition = 't.countries_id = :country_id';
                $criteria->params = array(':country_id' => $country_id);
                $criteria->order = 't.countries_name';

                $c_data = $this->model()->find($criteria);

                if (!is_null($c_data)) {
                    $_data = array(
                        'countries_name' => $c_data->countries_name,
                        'countries_iso_code_2' => $c_data->countries_iso_code_2,
                        'countries_iso_code_3' => $c_data->countries_iso_code_3
                    );
                }
            } else {
                $criteria->select = 't.countries_name';
                $criteria->condition = 't.countries_display = "1" AND t.countries_id = :country_id';
                $criteria->params = array(':country_id' => $country_id);
                $criteria->order = 't.countries_name';

                $c_data = $this->model()->find($criteria);

                $_data = array('countries_name' => $c_data->countries_name);
            }
        } else {
            $criteria->select = 't.countries_id, t.countries_name';
            $criteria->condition = 't.countries_display = "1"';
            $criteria->order = 't.countries_name';

            $c_data = $this->model()->findAll($criteria);

            foreach ($c_data as $data) {
                $_data = array('countries_id' => $data->countries_id,
                    'countries_name' => $data->countries_name);
            }
        }

        return $_data;
    }
    
    public function getAllCountries($display_flag = NULL) {
        $return_array = array();
        $cacheKey = Yii::app()->params['MEMCACHE_PREFIX'] . $this->tableName() . '/array';
        $cacheValue = Yii::app()->cache->get($cacheKey);

        if ($cacheValue === false) {
            $criteria = new CDbCriteria;
            $criteria->select = 'countries_id, countries_name, countries_display';
            $criteria->order = "countries_name";
            $search_result = $this->model()->findAll($criteria);

            foreach ($search_result as $country_info) {
                $country[] = $country_info->getAttributes();
            }
            //$data = $this->model()->findAll($criteria);
            Yii::app()->cache->set($cacheKey, $country, 86400);
        } else {
            $country = $cacheValue;
        }
        
        if (!is_null($display_flag)) {
            foreach ($country as $data) {
                if ($data['countries_display'] == '1') {
                    $return_array[$data['countries_id']] = $data['countries_name'];
                }
            }
        } else {
            $return_array = CHtml::listData($country, 'countries_id', 'countries_name');
        }

        return $return_array;
    }
    
    public function getAllCountryDialingInfo() {
        $criteria = new CDbCriteria;
        $criteria->select = 'countries_id, countries_name, countries_international_dialing_code';
        $criteria->order = 'countries_name';
        $result = $this->model()->findAll($criteria);
        return $result;
    }
    
    public function getCountryAddressFormatId($countries_id){
        $criteria = new CDbCriteria;
        $criteria->select = 'address_format_id';
        $criteria->condition = 'countries_id = :countries_id';
        $criteria->params = array(':countries_id' => $countries_id);
        $result = $this->model()->find($criteria);
        return $result;
    }

    public static function getCountryISO2($countries_id){
        $criteria = new CDbCriteria;
        $criteria->select = 'countries_iso_code_2';
        $criteria->condition = 'countries_id = :countries_id';
        $criteria->params = array(':countries_id' => $countries_id);
        $result = self::model()->find($criteria);
        return ($result ? $result->countries_iso_code_2 : '');
    }
}