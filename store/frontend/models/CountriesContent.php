<?php

class CountriesContent extends CountriesContentBase
{
    /**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesContent the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
	public function getTabContent($countries_id)
	{
        $return_array = array();

        $sql = '	SELECT *
                    FROM ' . $this->tableName() . '
                    WHERE countries_id = :countries_id
                    LIMIT 1';
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":countries_id", $countries_id, PDO::PARAM_INT);
        
        if ($value = $command->queryRow()) {
            $return_array = $value;
        }
        
		return $return_array;
	}
}