<?php

/**
 * This is the model class for table "countries_content_description".
 *
 * The followings are the available columns in table 'countries_content_description':
 * @property string $countries_id
 * @property string $language_id
 * @property string $geo_zone_id
 * @property string $slider_content
 * @property string $banner_content
 */
class CountriesContentDescription extends CountriesContentDescriptionBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CountriesContentDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getBannerContent($countries_id, $language_id)
	{
        $return_array = array();

        $sql = "	SELECT slider_content, banner_content
                    FROM " . $this->tableName() . " 
                    WHERE countries_id = :countries_id
                        AND language_id = :language_id";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":countries_id", $countries_id, PDO::PARAM_INT);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_INT);
        if ($value = $command->queryRow()) {
            $return_array = $value;
        }
        
		return $return_array;
	}
}