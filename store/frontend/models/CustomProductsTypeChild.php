<?php

/**
 * This is the model class for table "custom_products_type_child".
 *
 * The followings are the available columns in table 'custom_products_type_child':
 * @property integer $custom_products_type_child_id
 * @property integer $custom_products_type_id
 * @property string $custom_products_type_child_url
 * @property string $custom_products_type_child_name
 * @property integer $display_status
 * @property integer $sort_order
 */
class CustomProductsTypeChild extends CustomProductsTypeChildBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomProductsTypeChildBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}