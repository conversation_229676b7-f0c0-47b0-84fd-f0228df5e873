<?php

class CustomSeoModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_PRODUCT_CONFIG';
        $this->controller = 'custom-seo';
        $this->method = 'get';
        parent::__construct();
    }

    public function getSeoMetaContent($type, $id, $language_id)
    {
        $this->action = 'get-seo-content';
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'custom-seo/get-seo-content/' . $type . '/' . $id . '/' . $language_id;
        $return_arr = Yii::app()->cache->get($cache_key);
        if ($return_arr === false) {
            try {
                if (empty($language_id)) {
                    $attachment = [
                        [
                            'color' => 'warning',
                            'text' => [
                                'url' => Yii::app()->request->url,
                                'session_data' => $_SESSION,
                                'get' => $_GET,
                                'post' => $_POST,
                                'body' => Yii::app()->request->getRawBody(),
                            ],
                        ],
                    ];
                    Yii::app()->slack->send('Request without session id', $attachment, 'DEFAULT');
                }
                $return_arr = $this->request(['id' => $id, 'type' => $type, 'language_id' => $language_id]);
                if (!empty($return_arr) && !empty($return_arr['title'])) {
                    Yii::app()->cache->set($cache_key, $return_arr, 604800); // cache 7 days
                } else {
                    throw new Exception("Failed to retrieve SEO Content \n App: og-frontend \nRequest : " . json_encode(['id' => $id, 'type' => $type, 'language_id' => $language_id]) . "\nResponse : " . json_encode($return_arr));
                }
            } catch (Exception $e) {
                $cache_key = $cache_key . '/is_notified';
                $is_notified = Yii::app()->cache->get($cache_key);
                if (!$is_notified) {
                    $raw_data = $e->getMessage();
                    $attachment = [
                        [
                            'color' => 'warning',
                            'text' => $raw_data,
                        ],
                    ];
                    Yii::app()->cache->set($cache_key, true, 3600);
                    Yii::app()->slack->send('Failed to retrieve SEO Content', $attachment, 'DEFAULT');
                }
                return null;
            }
        }

        return $return_arr;
    }

    public function request($params = [])
    {
        //TODO : Temporary adjust to 30 second
        $this->client->timeout = 30; // set Timeout to 5 second
        return parent::request($params);
    }

}
