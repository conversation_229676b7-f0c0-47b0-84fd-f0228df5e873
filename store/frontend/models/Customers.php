<?php

class Customers extends CustomersBase
{

    public $confirm_password;

    /**
     * Returns the static model of the specified AR class.
     * @return Users the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

//    public function beforeValidate() {
//        if ($this->scenario == 'register') {
//            $purifier = new CHtmlPurifier();
//            $purifier->options = array('HTML' => array('Allowed' => ''));
//            $this->customers_email_address = $purifier->purify($this->customers_email_address);
//            $this->customers_password = $purifier->purify($this->customers_password);
//            $this->confirm_password = $purifier->purify($this->confirm_password);
//            $this->customers_firstname = $purifier->purify($this->customers_firstname);
//            $this->customers_lastname = $purifier->purify($this->customers_lastname);
//        }
//
//        return parent::beforeValidate();
//    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        return CMap::mergeArray(
            parent::relations(),
            [
                'countries' => [self::HAS_ONE, 'Countries', '', 'on' => 'countries.countries_id = t.customers_country_dialing_code_id', 'joinType' => 'LEFT JOIN', 'alias' => 'countries'],
            ]
        );
    }

    public function getPhoneInfo()
    {
        $criteria = new CDbCriteria;
        $criteria->select = 'customers_country_dialing_code_id, customers_telephone, customers_default_address_id';
        $criteria->condition = 'customers_id = :customerId';
        $criteria->params = [':customerId' => Yii::app()->user->id];
        $result = $this->model()->find($criteria);
        return $result;
    }

    public function getCustomerByField($field, $cond)
    {
        $_cond = [];
        $_result = [];

        $c = new CDbCriteria;
        $p = new CHtmlPurifier();

        foreach ($cond as $key => $val) {
            $_cond[] = 't.' . $key . ' = "' . $p->purify($val) . '"';
        }

        $c->select = implode(', ', $field);
        $c->condition = implode(' AND ', $_cond);

        $_result = $this->model()->find($c);

        return $_result;
    }

    public static function getCustomersName($customers_id)
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'customers/name/' . $customers_id;
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $criteria = new CDbCriteria();
            $criteria->select = 'customers_firstname, customers_lastname';
            $criteria->with = ['countries' => ['select' => 'countries_international_dialing_code']];
            $criteria->condition = 'customers_id = :customersId';
            $criteria->params = [':customersId' => $customers_id];
            $return_arr = self::model()->find($criteria);
            Yii::app()->cache->set($cache_key, $return_arr);
        }

        return $return_arr;
    }

    public function getCustomerInfo()
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'customers_firstname, customers_lastname, customers_gender, customers_dob, customers_email_address, customers_telephone, 
                            customers_newsletter,customers_mobile,customers_default_address_id';
        $criteria->with = ['countries' => ['select' => 'countries_international_dialing_code']];
        $criteria->condition = 'customers_id = :customersId';
        $criteria->params = [':customersId' => Yii::app()->user->id];
        $customer_info = $this->model()->find($criteria);
        return $customer_info;
    }

    public function isCbCustomer($customerId)
    {
        $criteria = new CDbCriteria();
        $criteria->condition = 'customers_id = :customerId AND FIND_IN_SET("4", customers_flag)';
        $criteria->params = [':customerId' => $customerId];
        $result = $this->exists($criteria);
        return $result;
    }

    public function isActiveCustomer($customerId)
    {
        $criteria = new CDbCriteria();
        $criteria->condition = 'customers_id = :customerId AND customers_status = 1';
        $criteria->params = [':customerId' => $customerId];
        $result = $this->exists($criteria);
        return $result;
    }

    public static function isSanctionCountry($customerId, $ignore_cache)
    {
        if (!isset(Yii::app()->session['customers_country_dialing_code_id']) || $ignore_cache) {
            $criteria = new CDbCriteria();
            $criteria->select = 'customers_country_dialing_code_id, customers_default_address_id';
            $criteria->condition = 'customers_id = :customersId';
            $criteria->params = [':customersId' => $customerId];
            $customer_info = self::model()->find($criteria);

            if (!empty($customer_info->customers_country_dialing_code_id) && !empty(Yii::app()->params['SANCTION_COUNTRY'][$customer_info->customers_country_dialing_code_id])) {
                $result = Yii::app()->params['SANCTION_COUNTRY'][$customer_info->customers_country_dialing_code_id];
            } else {
                $c = new CDbCriteria();
                $c->select = 'entry_country_id';
                $c->condition = 'address_book_id = :abid';
                $c->params = [':abid' => $customer_info->customers_default_address_id];
                $m = AddressBook::model()->find($c);

                if (isset($m->entry_country_id) && !empty($m->entry_country_id) && !empty(Yii::app()->params['SANCTION_COUNTRY'][$m->entry_country_id])) {
                    $result = Yii::app()->params['SANCTION_COUNTRY'][$m->entry_country_id];
                } else {
                    $result = 0;
                }
            }
            Yii::app()->session['customers_country_dialing_code_id'] = $result;
        }

        return Yii::app()->session['customers_country_dialing_code_id'];
    }

    public function getCustomerGroupId()
    {
        $c_data = $this->findByPk(Yii::app()->user->id);
        if (isset($c_data->customers_id)) {
            return $c_data->customers_groups_id;
        }
        return false;
    }
}
