<?php

/**
 * This is the model class for table "customers_login_history".
 *
 * The followings are the available columns in table 'customers_login_history':
 * @property integer $customers_id
 * @property string $customers_login_date
 * @property string $customers_login_ip
 * @property string $customers_login_ua_info
 * @property string $login_method
 */
class CustomersLoginHistory extends CustomersLoginHistoryBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersLoginHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function insertHistory($customers_id, $login_ip, $login_method) {
        $return_int = false;
        
		try {
            $return_int = $this->saveNewRecord(array(
                "customers_id" => $customers_id,
                "customers_login_date" => new CDbExpression('NOW()'),
                "customers_login_ip" => $login_ip,
                "customers_login_ua_info" => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
                "login_method" => $login_method,
            ), true);
            
        } catch (Exception $e) {}
		
		return $return_int;		
    }
}