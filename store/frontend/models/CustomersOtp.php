<?php

/**
 * This is the model class for table "customers_otp".
 *
 * The followings are the available columns in table 'customers_otp':
 * @property string $customers_id
 * @property string $customers_otp_type
 * @property string $customers_otp_digit
 * @property string $customers_otp_request_date
 */
class CustomersOtp extends CustomersOtpBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersOtpBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function customerExists($customerId, $requestType){
		$criteria = new CDbCriteria();
		$criteria->condition = 'customers_id = :customerId AND customers_otp_type = :requestType';
		$criteria->params = array(':customerId'=>$customerId, ':requestType'=>$requestType);
		$result = $this->model()->exists($criteria);
		return $result;
	}
	
	public function updateCustomerOpt($attribute, $customerId, $requestType){
		$attributes = $attribute;
		$condition = 'customers_id = :customerId AND customers_otp_type = :requestType';
		$params = array(':customerId'=>$customerId, ':requestType'=>$requestType);
		$this->model()->updateAll($attributes,$condition,$params);
	}
	
	public function saveCustomerOpt($customerOptData){
		$this->setIsNewRecord(true);
		$this->attributes = $customerOptData;
		$this->save();
	}
	
	public function getCustomerSecurityToken($customerId, $requestType){
		$criteria = new CDbCriteria();		
		$criteria->select = array('customers_otp_digit, customers_otp_request_date > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS active_token');
		$criteria->condition = 'customers_id = :customerId AND customers_otp_type = :requestType';
		$criteria->params = array(':customerId'=>$customerId, ':requestType'=>$requestType);
		$result = $this->model()->find($criteria);
		return $result;
	}
	
	public function getTokenInfo($customerId){
		$criteria = new CDbCriteria();
		$criteria->select = 'customers_id, customers_otp_digit';
		$criteria->condition = 'customers_id = :customerId';
		$criteria->params = array(':customerId'=>$customerId);
		$result = $this->model()->find($criteria);
		return $result;
	}
}