<?php

/**
 * This is the model class for table "customers_preference".
 *
 * The followings are the available columns in table 'customers_preference':
 * @property integer $customers_id
 * @property string $preference_key
 * @property string $value
 */
class CustomersPreference extends CustomersPreferenceBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersPreferenceBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function updateCustomersPreference($customersId, $preferenceKey, $value) {
        DbHelper::insertIgnore('customers_preference', [
            'customers_id' => $customersId,
            'preference_key' => $preferenceKey,
            'value' => $value
        ]);
    }
}