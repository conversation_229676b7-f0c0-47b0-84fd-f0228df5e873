<?php

/**
 * This is the model class for table "coupon_gv_customer".
 *
 * The followings are the available columns in table 'coupon_gv_customer':
 * @property integer $customer_id
 * @property string $amount
 * @property string $sc_reversible_amount
 * @property string $sc_reversible_reserve_amount
 * @property string $sc_irreversible_amount
 * @property string $sc_last_modified
 * @property string $sc_irreversible_reserve_amount
 * @property integer $sc_currency_id
 * @property string $sc_conversion_date
 */
class CustomersScCart extends CustomersScCartBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CouponGvCustomerBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}