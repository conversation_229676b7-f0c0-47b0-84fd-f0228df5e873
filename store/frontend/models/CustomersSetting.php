<?php

/**
 * This is the model class for table "customers_setting".
 *
 * The followings are the available columns in table 'customers_setting':
 * @property string $customers_id
 * @property string $customers_setting_key
 * @property string $customers_setting_value
 * @property string $created_datetime
 * @property string $updated_datetime
 */
class CustomersSetting extends CustomersSettingBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getPhoneVerifyAttempt($customerId){
		$criteria = new CDbCriteria();
		$criteria->select = array('customers_setting_value, updated_datetime > DATE_SUB(curdate(), INTERVAL 1 DAY) AS request_active');
		$criteria->condition = 'customers_id = :customerId AND customers_setting_key = "phone_verification_attempt"';
		$criteria->params = array(":customerId"=>$customerId);
		$result = $this->model()->find($criteria);
		return $result;
	}
	
	public function updatePhoneVerifyAttempt($attribute, $customerId){
		$attributes = $attribute;
		$condition = 'customers_setting_key = "phone_verification_attempt" AND customers_id = :customerId';
		$params = array(':customerId'=>$customerId);
		$this->model()->updateAll($attributes,$condition,$params);
	}
	
	public function savePhoneVerifyAttempt($phoneVerifyInfo){
		$this->setIsNewRecord(true);
		$this->attributes = $phoneVerifyInfo;
		$this->save();
	}
	
	public function getTokenVerifyAttempt($customerId, $tokenType){
		$criteria = new CDbCriteria();
		$criteria->select = array('customers_setting_value, created_datetime > DATE_SUB(NOW(), INTERVAL 24 HOUR) AS same_day_request, 
                                        updated_datetime > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS active_request');
		$criteria->condition = 'customers_id = :customerId AND customers_setting_key = :tokenType';
		$criteria->params = array(':customerId'=>$customerId,':tokenType'=>$tokenType);
		$result = $this->model()->find($criteria);
		return $result;
	}
	
	public function updateTokenVerifyAttempt($attribute, $customerId, $requestType){
		$attributes = $attribute;
		$condition = 'customers_setting_key = :requestType AND customers_id = :customerId';
		$params = array(':customerId'=>$customerId, ':requestType'=>$requestType);
		$this->model()->updateAll($attributes,$condition,$params);
	}
	
	public function saveCustomerSetting($tokenData){
		$this->setIsNewRecord(true);
		$this->attributes = $tokenData;
		$this->save();
	}
}