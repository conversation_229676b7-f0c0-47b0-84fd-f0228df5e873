<?php

/**
 * This is the model class for table "events".
 *
 * The followings are the available columns in table 'events':
 * @property integer $events_id
 * @property integer $news_id
 * @property string $events_name
 * @property string $events_remark
 * @property string $events_sender_email
 * @property string $events_admin_copy_email
 * @property string $events_email_tpl
 * @property integer $events_status
 * @property string $events_order_period_date_from
 * @property string $events_order_period_date_to
 * @property string $events_order_period_note
 * @property string $events_order_period_empty_err_msg
 * @property string $events_order_period_invalid_err_msg
 */
class Events extends EventsBase {

    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

}
