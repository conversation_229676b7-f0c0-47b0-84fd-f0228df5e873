<?php

/**
 * This is the model class for table "events_description".
 *
 * The followings are the available columns in table 'events_description':
 * @property integer $events_id
 * @property integer $language_id
 * @property string $events_name
 * @property string $events_email_tpl
 */
class EventsDescription extends EventsDescriptionBase {

    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getEventsName($events_id, $languages_id, $default_languages_id) {
        $return = array();

        $sql = "SELECT ed.events_name
                FROM " . $this->tableName() . " AS ed
                WHERE ed.events_id = :events_id
                        AND ( IF(ed.language_id = :languages_id, 1,
                                  IF ((SELECT COUNT(ed_inner.events_id) > 0
                                                FROM " . $this->tableName() . " AS ed_inner
                                                WHERE ed_inner.events_id = :events_id
                                                        AND ed_inner.events_name <> ''
                                                        AND ed_inner.language_id =:languages_id), 0,
                                                                ed.language_id = :default_languages_id)
                                                )
                                         )";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":events_id", $events_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryRow()) {
            $return = $result;
        }

        return $return;
    }

    public function getEventEmailTemplate($events_id, $languages_id, $default_languages_id) {
        $return = array();

        $sql = "SELECT events_name, events_email_tpl
		FROM " . $this->tableName() . " 
                WHERE events_id = :events_id 
                    AND events_name <> '' 
                    AND (IF (language_id = :languages_id, 1, IF(( SELECT COUNT(events_id) > 0 
                                                                                    FROM " . $this->tableName() . " 
                                                                                    WHERE events_id = :events_id  
                                                                                    AND language_id = :languages_id 
                                                                                    AND events_name <> ''), 0, language_id = :default_languages_id)))";


        $command = $this->conn->createCommand($sql);
        $command->bindParam(":events_id", $events_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryRow()) {
            $return = $result;
        }

        return $return;
    }

}
