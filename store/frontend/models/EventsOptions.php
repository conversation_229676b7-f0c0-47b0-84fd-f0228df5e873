<?php

/**
 * This is the model class for table "events_options".
 *
 * The followings are the available columns in table 'events_options':
 * @property integer $events_options_id
 * @property integer $events_id
 * @property string $events_options_title
 * @property integer $events_options_input_type
 * @property integer $events_options_max_size
 * @property integer $events_options_row_size
 * @property integer $events_options_column_size
 * @property string $events_options_name
 * @property string $events_options_sub_sort_order
 * @property integer $events_options_required
 * @property string $events_options_note
 * @property string $events_options_err_msg
 * @property integer $events_options_sort_order
 * @property integer $events_options_status
 * @property string $events_options_last_modified
 * @property string $events_options_date_added
 */
class EventsOptions extends EventsOptionsBase {

    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getEventsOptions($events_id = 0, $languages_id = 0, $default_languages_id = 0) {
        $return = array();

        $sql = "SELECT eo.events_options_id, eo.events_options_input_type, eo.events_options_required, eod.events_options_title, eod.events_options_max_size, eod.events_options_row_size, eod.events_options_column_size, eod.events_options_name, eod.events_options_note
                FROM " . $this->tableName() . " AS eo
                INNER JOIN " . EventsOptionsDescription::model()->tableName() . " as eod
                        ON (eo.events_options_id = eod.events_options_id)
                WHERE eo.events_id = :events_id
                        AND eo.events_options_status = '1'
                        AND ( IF(eod.language_id = :languages_id && eod.events_options_title <> '', 1,
                                        IF ((	SELECT count(eod_inner.events_options_id) > 0 from " . EventsOptionsDescription::model()->tableName() . " as eod_inner
                                                        WHERE eod_inner.events_options_id = eo.events_options_id 
                                                                AND eod_inner.events_options_title <> ''
                                                                AND eod_inner.language_id = :languages_id), 0, eod.language_id = :default_languages_id)))
                                        ORDER BY eo.events_options_sort_order";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":events_id", $events_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryAll()) {
            $return = $result;
        }

        return $return;
    }

}
