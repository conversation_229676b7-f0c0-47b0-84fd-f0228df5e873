<?php

/**
 * This is the model class for table "events_options_description".
 *
 * The followings are the available columns in table 'events_options_description':
 * @property integer $events_options_id
 * @property integer $language_id
 * @property string $events_options_title
 * @property string $events_options_note
 * @property string $events_options_name
 * @property integer $events_options_max_size
 * @property integer $events_options_row_size
 * @property integer $events_options_column_size
 * @property string $events_options_err_msg
 */
class EventsOptionsDescription extends EventsOptionsDescriptionBase {

    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getErrorMessages($events_options_id, $languages_id, $default_languages_id) {
        $return = array();

        $sql = "SELECT events_options_err_msg
                FROM " . $this->tableName() . "
                WHERE events_options_id = :events_options_id
		AND ( IF(language_id = :languages_id, 1,
                    IF ((SELECT COUNT(edo_inner.events_options_id) > 0
                    FROM " . $this->tableName() . " as edo_inner
                    WHERE edo_inner.events_options_id = :events_options_id
                            AND edo_inner.events_options_err_msg <> ''
                            AND edo_inner.language_id =:languages_id), 0,language_id = :default_languages_id)
                    )
             )";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":events_options_id", $events_options_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryRow()) {
            $return = $result;
        }

        return $return;
    }

}
