<?php

/**
 * This is the model class for table "events_options_values".
 *
 * The followings are the available columns in table 'events_options_values':
 * @property integer $events_options_values_id
 * @property integer $events_options_id
 * @property integer $language_id
 * @property string $events_options_values
 * @property string $events_options_values_sort_order
 */
class EventsOptionsValues extends EventsOptionsValuesBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return EventsOptionsValues the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getOptionsValues($events_options_id, $languages_id, $default_languages_id) {
        $return = array();

        $sql = "SELECT events_options_values
		FROM " . $this->tableName() . "
		WHERE events_options_id = :events_options_id
		AND (IF (language_id = :languages_id, 1, IF(( SELECT COUNT(inner_eov.events_options_id) > 0 
                    FROM " . $this->tableName() . " as inner_eov
                    WHERE inner_eov.events_options_id = :events_options_id 
                    AND inner_eov.events_options_values <> ''
                    AND inner_eov.language_id = :languages_id), 0, language_id = :default_languages_id)))
                    ORDER BY events_options_values_sort_order";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":events_options_id", $events_options_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryAll()) {
            $return = $result;
        }

        return $return;
    }

}
