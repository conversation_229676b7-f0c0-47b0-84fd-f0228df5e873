<?php

class FrontPageForm extends CFormModel
{
    public $keywords = '', $item_per_page, $total_available_items, $page, $offset;

    public function __construct()
    {
    }

    private function getBestSellingProducts()
    {
        $return_array = array();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'getBestSellingProducts/array/country_id/' . Yii::app()->frontPageCom->country_id . '/customer_group_id/' . Yii::app()->frontPageCom->customer_group_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $best_selling_game_ids = $cache_result;
        } else {
            try {
                $best_selling_game_ids = array();
                $best_selling_games = Yii::app()->frontPageCom->getBestSellingGameIDs();

                if ($game_ids = Yii::app()->searchBoxCom->filter4Display($best_selling_games)) {
                    $best_selling_game_ids = array_slice($game_ids, 0, Yii::app()->frontPageCom->max_best_selling);
                }

                Yii::app()->cache->set($cache_key, $best_selling_game_ids, 86400);
            } catch (Exception $e) {
                // $e->getMessage()
            }
        }

        return $best_selling_game_ids;
    }

    public function getBestSellingContent()
    {
        $best_selling_content = array();
        if ($best_selling = $this->getBestSellingProducts()) {
            foreach ($best_selling as $cat_id) {
                $category_url = Yii::app()->createUrl('category/index', array('cid' => $cat_id));
                $third_layer_category = CategoriesBase::getSubCategoriesIdByParentId($cat_id);
                if ($third_layer_category) {
                    $third_layer_category_id = $third_layer_category['categories_id'];
                    Yii::app()->frontPageCom->getCategoryObj()->setCategoryID($third_layer_category_id);
                    $name = Yii::app()->frontPageCom->getCategoryObj()->getName();
                    $image_data = Yii::app()->frontPageCom->getCategoryObj()->getCategoriesImage($third_layer_category_id);

                    $best_selling_content[] = array(
                        'desc' => $name,
                        'image_info' => array(
                            'attributes_info' => array(
                                'class' => 'actual-size',
                                'src' => $image_data['dimension'][0],
                            )
                        ),
                        'category_url' => $category_url,
                    );
                }
            }
        }

        return $best_selling_content;
    }

    public function getRecentViewedContent()
    {
        $return_array = array();
        $max = Yii::app()->params['MAX_RECENT_VIEWED_PRODUCTS'];
        if ($recent_viewed_products = $this->getRecentViewedProduct($max)) {
            foreach ($recent_viewed_products as $idx => $product) {
                $product_url = Yii::app()->createUrl('product/index', array(
                    'pid' => $product['pid'],
                    'dm' => $product['default_dm'],
                    'pm' => $product['promo_type']
                ));
                if (!isset($product['products_last_modified']) || empty($product['products_last_modified'])) {
                    $last_modified = $product['pid'];
                } else {
                    $last_modified = strtotime($product['products_last_modified']);
                }
                $return_array[] = array(
                    'type' => 'two-col-layout',
                    'desc' => array(
                        'type' => 'link',
                        'href' => $product_url,
                        'label' => $product['name'],
                    ),
                    'class' => 'col-lg-20 col-md-30 col-sm-60',
                    'extra_info' => array(
                        'data-N' => array(
                            'data-id' => 'data_rvp_' . $idx,
                            'data-name' => htmlspecialchars($product['name']),
                            'data-type' => 'buy',
                            'data-pid' => $product['pid'],
                            'data-dtu' => $product['is_dtu_flag'],
                            'data-bundle' => $product['products_bundle'],
                            'data-dm' => $product['default_dm'],
                            'data-pm' => $product['promo_type'],
                        ),
                    ),
                    'purchase_info' => array(
                        'price' => $product['price'],
                    ),
                    'button_info' => array(
                        'type' => $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                        'label_str_limit' => 20,
                        'label' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                        //                        'label_suffix' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                        'attributes_info' => array(
                            'id' => 'rvp_' . $idx,
                        )
                    ),
                    'image_info' => array(
                        'attributes_info' => array(
                            'src' => (array_values(
                                array_slice(explode("/", $product['image']['dimension'][0]), -1)
                            )[0] !== "no_product_image.png" ? $product['image']['dimension'][0] . "?v=" . $last_modified : $product['image']['dimension'][0])
                        ),
                        'map_info' => array(
                            'href' => $product_url,
                        ),
                    ),
                );
            }
        }
        return $return_array;
    }

    private function getRecentViewedProduct($total_itms)
    {
        $return_array = array();

        if ($product_id_array = Yii::app()->frontPageCom->getRecentViewedProductCookieIDs($total_itms)) {
            $products_id_array = $product_id_array[1];

            if ($products_array = Yii::app()->searchBoxCom->getProductListByProductIDs($products_id_array)) {
                $recent_view_product_mapping = $product_id_array[0];

                foreach ($products_array as $product_info) {
                    if (isset($recent_view_product_mapping[$product_info['products_id'] . ":" . $product_info['default_dm']])) {
                        $idx = $recent_view_product_mapping[$product_info['products_id'] . ":" . $product_info['default_dm']]['idx'];
                        $return_array[$idx] = $product_info;
                    }
                }

                ksort($return_array);
            }
        }
        return $return_array;
    }

    //    public function getMainBanner() {
    //        return Yii::app()->frontPageCom->getCountryContentDescription('main_slider');
    //    }

    private function getWhatIsHotNews()
    {
        $return_array = array();
        //        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'RSS_FEED/what_is_hot_news/array/';
        //        $cache_result = Yii::app()->cache->get($cache_key);
        //
        //        if ($cache_result !== FALSE) {
        //            $return_array = $cache_result;
        //        } else {
        if ($xml = RssFeedCom::getRssFeed(Yii::app()->frontPageCom->RSS_FEED_URL)) {
            if (isset($xml->channel)) {
                $cnt = count($xml->channel->item);

                for ($i = 0; $i < $cnt; $i++) {
                    preg_match('/^(<img[^>]+>)(.*)/i', (string)$xml->channel->item[$i]->description, $matches);

                    if (isset($matches[1]) && notEmpty($matches[1])) {
                        $return_array[] = array(
                            'link' => (string)$xml->channel->item[$i]->link,
                            'title' => (string)$xml->channel->item[$i]->title,
                            'img' => str_ireplace('src="http://blog.offgamers.com/', 'src="//https://blog.offgamers.com/blog/', $matches[1]),
                            'desc' => $matches[2]
                        );
                    }
                }
                //                    Yii::app()->cache->set($cache_key, $return_array, 300); // cache 15 min
            }
        }
        //        }

        return $return_array;
    }

    public function getWhatIsHotNewsContent()
    {
        $what_hot_content = array();

        if ($what_is_hot_news = $this->getWhatIsHotNews()) {
            foreach ($what_is_hot_news as $news) {
                $what_hot_content[] = array(
                    'type' => 'image',
                    'desc' => $news['title'],
                    'box_link' => $news['link'],
                    'image_info' => array('html_tag' => $news['img'])
                );
            }
        }

        return $what_hot_content;
    }

    public function getWhatIsHotBanner()
    {
        return Yii::app()->frontPageCom->getCountryContentDescription('main_best_selling');
    }

    public function getRecentPurchasedContent()
    {
        $max = Yii::app()->params['MAX_RECENT_PURCHASED_PRODUCTS'];

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'frontpage-recent-purchase/' . Yii::app()->user->id;
        $data = Yii::app()->cache->get($cache_key);

        if ($data === false) {
            $data = [];
            if ($recent_viewed_products = $this->getRecentPurchasedProduct($max)) {
                $list = [];
                foreach ($recent_viewed_products as $idx => $product) {
                    $list[] = $product['pid'];
                }
                $return_arr = (new MsProductModel)->getProductUrlImage($list, Yii::app()->frontPageCom->language_id);
            }

            if (!isset($return_arr['error_code']) && !empty($return_arr)) {
                foreach ($return_arr as $pid => $item) {
                    if (!empty($item)) {
                        $data[] = $item;
                    }
                }
            }

            Yii::app()->cache->set($cache_key, $data);
        }

        return $data;
    }

    private function getRecentPurchasedProduct($max)
    {
        $return_array = array();
        try {
            if (Yii::app()->user->id) {
                $recent_purchased_product_id = OrdersCustomersPurchased::model()->getCustomerPurchased(Yii::app()->user->id, $max);
                $return_array = Yii::app()->searchBoxCom->getProductListByProductIDs($recent_purchased_product_id);
            }
        } catch (Exception $e) {
            $e->getMessage();
        }

        return $return_array;
    }
}