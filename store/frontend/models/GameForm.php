<?php

class GameForm extends CFormModel
{
    const TEMPLATE_ID_TYPE = 2;
    
    private $product_array;
    
    public  $all_category_id_array,
            $game_blog_id,
            $tag_info = array(),
            $tag_id,
            $item_per_page,
            $total_available_items,
            $page,
            $offset,
            $error_empty_category = false;

    public function __construct($request_array) {
        $this->tag_id = isset($request_array['tid']) ? (int) $request_array['tid'] : 0;
        $this->game_blog_id = isset($request_array['gbid']) ? (int) $request_array['gbid'] : 0;
        Yii::app()->frontPageCom->getPageContentObj()->_init($this->game_blog_id, self::TEMPLATE_ID_TYPE);
        
        $this->all_category_id_array = Yii::app()->frontPageCom->getPageContentObj()->getAllCategoryIDByGameBlogID();

        if(!count($this->all_category_id_array)){
            $this->error_empty_category = true;
        }
    }
    
    public function rules() {
        return array(
            array('username, password', 'required'),
            array('rememberMe', 'boolean'),
            array('password', 'authenticate'),
        );
    }
    
    public function getAFNotice() {
        $cookie_key = 'notice_alert_cookie';
        $return_int = Yii::app()->frontPageCom->getPageContentObj()->getGameValue('af_notice_enable_status', '0');

        if (isset(Yii::app()->request->cookies[$cookie_key])) {
            $val_array = explode(',', htmlspecialchars(Yii::app()->request->cookies[$cookie_key]->value));
            $return_int = in_array('g' . $this->game_blog_id, $val_array) ? '0' : $return_int;
        }
        
        return $return_int;
    }
    
    public function getBackgroundColor() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameValue('background_color', 'transparent');
    }
    
    public function getBackgroundImage() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameValue('background_source', '');
    }


    public function getBreadcrumbsWidgetContent($link = '') {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return array(
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
            'htmlOptions' => array('class'=>'breadcrumbs  breadcrumbs--decor')
        );
    }
    
    public function getBreadcrumbs() {
        $return_array = array();

        if ($tag_array = $this->getTagInfo()) {
            $current_title = array_pop($tag_array);

            foreach ($tag_array as $tid => $label) {
                $return_array[$label] = array('search/index','keyword' => Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE'));
            }

            $return_array[] = $current_title;
        }
        return $return_array;
    }

    private function getTagInfo() {
        return array(7 => "Game Card",$this->getTitle());
    }


    public function getDescription() {
        return str_replace("\n", "<br>", Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('description', ''));
    }
    
    public function getGameInfoList() {
        $return_array = array();
        $game_info_array = Yii::app()->frontPageCom->getPageContentObj()->getGameInfo();
        
        foreach ($game_info_array as $key => $game_array) {
            $return_array[] = array(
                'label' => Yii::t('page_content', 'GAME_INFO_' . strtoupper($key)),
                'value' => is_array($game_array) ? implode(", ", $game_array) : $game_array
            );
        }
        
        return $return_array;
    }
    
    public function getLogoImageInfo() {
        $return_array = array();
        
        if ($url = Yii::app()->frontPageCom->getPageContentObj()->getGameValue('logo_source', '')) {
            $return_array = array(
                'attributes_info' => array(
                    'src' => $url
                )
            );
        }
        
        return $return_array;
    }
    
    public function getNotice() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('notice', '');
    }
    
    public function getTitle() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameBlogDescription();
    }
    
    public function getRelatedLinkList() {
        $return_array = array();
        $game_info_array = Yii::app()->frontPageCom->getPageContentObj()->getRelatedLink();
        
        foreach ($game_info_array as $game_array) {
            $return_array[] = array(
                'label' => $game_array['label'],
                'href' => $game_array['link']
            );
        }
        
        return $return_array;
    }
    
    public function getRemark() {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('remark', '');
    }

    public function getResultSummary() {
        return '';
    }

    public function getSystemRequirement() {
        return str_replace("\n", "<br>", Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('system_requirements', ''));
    }
    
    public function getGallery() {
        $return_array = array();
        
        if ($gallery_json = Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('gallery_info', '')) {
            if ($gallery_array = json_decode($gallery_json, true)) {
                foreach ($gallery_array as $gallery) {
                    if ($gallery['link'] && $gallery['source']) {
                        $return_array[] = array(
                            'href' => $gallery['link'],
                            'src' => $gallery['source']
                        );
                    }
                }
            }
        }
        
        return $return_array;
    }

    public function getSupportedCategory(){
        return Yii::app()->frontPageCom->getPageContentObj()->getGameBlogSupportedCategory();
    }
    
    public function getItems() {
        $return_array = array(
            array(
                'active' => 0,
                'href' => '',
                'class' => 'desc',
                'label' => Yii::t('page_content', 'GAME_PAGE_TAG_HEADER_SUPPORTED_BY'),
                'tab_items' => array()
            )
        );
        
        foreach ($this->product_array as $category_id => $product) {
            $temp_array = array(
                'active' => count($return_array) == 1 ? 1 : 0,
                'label' => $product['name'],
            );
            
            foreach ($product['product'] as $product) {
                if(!isset($product['products_last_modified']) || empty($product['products_last_modified'])){
                    $last_modified = $product['pid'];
                }
                else{
                    $last_modified = strtotime($product['products_last_modified']);
                }
                $temp_array['tab_items'][] = array(
                    'desc' => $product['name'],
                    'price' => $product['price'],
                    'op_info' => array(
                        'label' => '<span class="op_tooltips" title="'.Yii::t('page_content', 'TOOLTIPS_OFFGAMERS_POINTS').'"></span>' . $product['op'],
                    ),
                    'more_detail_info' => array(
                        'href' => Yii::app()->createUrl('product/index', array('pid' => $product['pid'], 'dm' => $product['default_dm'], 'pm' => $product['promo_type'])),
                        'label' => Yii::t('buttons', 'LINK_MORE_DETALS'),
                    ),
                    'image_info' => array(
                        'attributes_info' => array(
                            'title' => $product['image']['title'],
                            'alt' => $product['image']['alt'],
                            'src' => (array_values(array_slice(explode("/",$product['image']['dimension'][0]), -1))[0] !== "no_product_image.png" ? $product['image']['dimension'][0]."?v=".$last_modified : $product['image']['dimension'][0]),
                        )
                    ),
                    'button_info' => array(
                        'type' => $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                        'label' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                        'attributes_info' => array(
                            'id' => 'g_' . $product['pid'] . $category_id,
                        )
                    ),
                    'extra_info' => array(
                        'data-N' => array(
                            'data-id' => 'data_g_' . $product['pid'] . $category_id,
                            'data-name' => htmlspecialchars($product['name']),
                            'data-type' => 'buy',
                            'data-pid' => $product['pid'],
                            'data-dtu' => $product['is_dtu_flag'],
                            'data-bundle' => $product['products_bundle'],
                            'data-dm' => $product['default_dm'],
                            'data-pm' => $product['promo_type'],
                        ),
                    )
                );
            }
            
            $return_array[] = $temp_array;
        }
        
        return $return_array;
    }
}
