<?php

class GameProductForm extends CFormModel
{
    private $filtering_array,
        $product_array;

    public $alphabet, $item_per_page,
        $total_available_items,
        $facet,
        $page, $offset;

    public function __construct($request_array, $is_ajax = false)
    {
        $this->item_per_page = Yii::app()->params['GAME_PRODUCT_PER_PAGE'];
        $this->page = isset($request_array['page']) ? (int)$request_array['page'] - 1 : 0;
        $this->offset = $this->page * $this->item_per_page;
        $this->filtering_array = $this->captureUserInput($request_array);
        if ($is_ajax !== true) {
            list($this->product_array, $this->total_available_items, $this->facet) = Yii::app()->searchBoxCom->getPaginationProductListFromSearch3($this->filtering_array,
                $this->page, $this->item_per_page);
        }
    }

    private function createPageURL()
    {
        return Yii::app()->createUrl('game-key/index', array(
            'fa' => $this->alphabet
        ));
    }

    public function getBreadcrumbs()
    {
        return array(Yii::t('page_content', 'MEGA_MENU_GAME_KEY'));
    }

    public function getTitle()
    {
        $return_str = Yii::t('page_content', 'MEGA_MENU_GAME_KEY');

        return $return_str;
    }

    public function getPageInfo()
    {
        return array(
            'header' => '',
            'hiddenPageCssClass' => 'disabled',
            'selectedPageCssClass' => 'active',
            'firstPageLabel' => '<<',
            'lastPageLabel' => '>>', //ceil($this->total_available_items/$this->item_per_page) . ' >>',
            'nextPageLabel' => '>',
            'prevPageLabel' => '<',
            'nextPageCssClass' => 'hidden',
            'previousPageCssClass' => 'hidden',
            'htmlOptions' => array(
                'class' => 'pagination'
            ),
            'currentPage' => $this->page,
            'itemCount' => $this->total_available_items,
            'pageSize' => $this->item_per_page,
            'maxButtonCount' => 6,
        );
    }

    public function getResultSummary()
    {
        $productOffset = (count($this->product_array) == 0) ? 0 : 1;
        return Yii::t('page_content', 'SEARCH_NUMBER_RESULT_SUMMARY', array(
            '{{FROM}}' => $productOffset,
            '{{TO}}' => $this->offset + count($this->product_array),
            '{{TOTAL}}' => $this->total_available_items
        ));
    }

    public function getItems()
    {
        $return_array = array();

        foreach ($this->product_array as $idx => $product) {
            if (!isset($product['products_last_modified']) || empty($product['products_last_modified'])) {
                $last_modified = $product['pid'];
            } else {
                $last_modified = strtotime($product['products_last_modified']);
            }
            $return_array[] = array(
                'desc' => $product['name'],
                'price' => $product['price'],
                'products_type' => $product['products_type'],
                'original_price' => $product['original_price'],
                'op_info' => array(
                    'label' => '<span class="op_tooltips" title="' . Yii::t('page_content',
                            'TOOLTIPS_OFFGAMERS_POINTS') . '"></span>' . $product['op'],
                ),
                'more_detail_info' => array(
                    'href' => Yii::app()->createUrl('product/index', array(
                        'pid' => $product['pid'],
                        'dm' => $product['default_dm'],
                        'pm' => $product['promo_type']
                    )),
                    'label' => Yii::t('buttons', 'LINK_MORE_DETALS'),
                ),
                'image_info' => array(
                    'attributes_info' => array(
                        'title' => $product['image']['title'],
                        'alt' => $product['image']['alt'],
                        'src' => (array_values(array_slice(explode("/", $product['image']['dimension'][0]),
                            -1))[0] !== "no_product_image.png" ? $product['image']['dimension'][0] . "?v=" . $last_modified : $product['image']['dimension'][0]),
                    )
                ),
                'button_info' => array(
                    'type' => $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                    'label' => Yii::t('buttons',
                        strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                    'attributes_info' => array(
                        'id' => 's_' . $product['pid'] . $idx,
                    )
                ),
                'extra_info' => array(
                    'data-N' => array(
                        'data-id' => 'data_s_' . $product['pid'] . $idx,
                        'data-name' => htmlspecialchars($product['name']),
                        'data-type' => 'buy',
                        'data-pid' => $product['pid'],
                        'data-dtu' => $product['is_dtu_flag'],
                        'data-bundle' => $product['products_bundle'],
                        'data-dm' => $product['default_dm'],
                        'data-pm' => $product['promo_type'],
                    ),
                )
            );
        }

        return $return_array;
    }

    public function getAllKeywordResults()
    {
        $filtering_array = array();

        return $filtering_array;
    }

    public function getLoadMoreContent()
    {
        $page_info = $this->getPageInfo();
        $content = '';
        $total_pages = ceil($page_info['itemCount'] / $page_info['pageSize']);
        $next_page = $page_info['currentPage'] + 2;

        if ($next_page > $total_pages) {
            //last page no next link
        } else {
            $content .= "<div class=\"content-box__more\"><a href=\"javascript:void(0)\" onclick=\"refreshGamePageContent($this->page + 2);\" class=\"btn btn-brown content-box__more-link\">" . Yii::t('page_content',
                    'TEXT_LOAD_MORE') . "</a></div>";

        }
        return $content;
    }

    private function captureUserInput($data)
    {
        $filtering_array = array(
            'item_per_page' => $this->item_per_page,
            'offset' => $this->offset
        );

        if (isset($data['filter'])) {
            $p = new CHtmlPurifier();
            $search_key = $p->purify($data['filter']);
            $filtering_array['filter'] = $search_key;
            unset($p);
        }

        if (isset($data['fa'])) {
            $this->alphabet = $data['fa'];
            $filtering_array['fa'] = $this->alphabet;
        }

        if (isset($data['tag'])) {
            $filtering_array['tag'] = $data['tag'];
        }

        return $filtering_array;
    }

    public function getPageUrl()
    {
        return $this->createPageURL();
    }

}

