<?php

class GameProductModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_PRODUCT_CONFIG';
        $this->controller = 'game-publisher-product';
        $this->method = 'get';
        parent::__construct();
    }

    public function getActiveAttribute()
    {
        $this->action = 'get-active-attributes';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'game-product/get-active-attribute';
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request();
            if (!isset($return_arr['error_message'])) {
                Yii::app()->cache->set($cache_key, $return_arr, 3600); // cache 30 minutes
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve game product attribute', $attachments, 'DEFAULT');
            }
        }

        return $return_arr;
    }

    public function getGameProductsInfo($products_id)
    {
        $this->action = 'view-game-product-by-product-id';
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'game-product/game-products-page-detail/' . $products_id;
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request(['id' => $products_id]);
            if (!isset($return_arr['error_message'])) {
                Yii::app()->cache->set($cache_key, $return_arr, 3600); // cache 30 minutes
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve game product info', $attachments, 'DEFAULT');
            }
        }

        return $return_arr;
    }

}
