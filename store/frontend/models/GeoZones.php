<?php

/**
 * This is the model class for table "geo_zones".
 *
 * The followings are the available columns in table 'geo_zones':
 * @property integer $geo_zone_id
 * @property string $geo_zone_name
 * @property string $geo_zone_description
 * @property integer $geo_zone_type
 * @property string $last_modified
 * @property string $date_added
 */
class GeoZones extends GeoZonesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return GeoZonesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}