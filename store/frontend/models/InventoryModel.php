<?php

class InventoryModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_INVENTORY_CONFIG';
        $this->controller = 'direct-top-up';
        $this->method = 'get';
        parent::__construct();
    }

    public function validateMintRouteDTU($data)
    {
        $this->action = 'validate-mint-route';
        return $this->request($data);
    }
    
    public function validateNetDragonDTU($data)
    {
        $this->action = 'validate-net-dragon';
        return $this->request($data);
    }

    public function validateFuluDTU($data)
    {
        $this->action = 'validate-fulu';
        return $this->request($data);
    }

    public function getRazerDTUAccount($data)
    {
        $this->action = 'get-character-razer';
        return $this->request($data);
    }

    public function validateRazerDTU($data)
    {
        $this->action = 'validate-razer';
        return $this->request($data);
    }

}
