<?php

/**
 * This is the model class for table "latest_news_description".
 *
 * The followings are the available columns in table 'latest_news_description':
 * @property integer $news_id
 * @property integer $language_id
 * @property string $headline
 * @property string $latest_news_summary
 * @property string $content
 * @property integer $is_default
 */
class LatestNewsDescription extends LatestNewsDescriptionBase {

    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getLatestNewsContent($news_id, $languages_id, $default_languages_id) {
        $return = array();

        $sql = "SELECT lnd.content, e.events_id
                FROM " . $this->tableName() . " AS lnd 
                INNER JOIN " . Events::model()->tableName() . " AS e 
                        ON lnd.news_id = e.news_id 
                WHERE lnd.news_id = :news_id 
                        AND lnd.headline <> '' 
                        AND ( if(lnd.language_id = :languages_id, 1, 
                                          if ((select count(lnd_inner.news_id) > 0 from " . $this->tableName() . " as lnd_inner
                                                        where lnd_inner.news_id =:news_id 
                                                        and lnd_inner.language_id =:languages_id), 
                                                        0,
                                                        lnd.language_id = :default_languages_id)
                                                        )
                                                 )";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":news_id", $news_id, PDO::PARAM_INT);
        $command->bindParam(":languages_id", $languages_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_languages_id, PDO::PARAM_STR);

        if ($result = $command->queryRow()) {
            $return = $result;
        }

        return $return;
    }

}
