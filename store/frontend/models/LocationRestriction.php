<?php

/**
 * This is the model class for table "localation_restriction".
 *
 * The followings are the available columns in table 'localation_restriction':
 * @property string $id
 * @property string $country_iso_code2
 * @property string $restriction_info
 * @property int $created_at
 * @property int $updated_at
 * @property string $changed_by
 */
class LocationRestriction extends LocationRestrictionBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LocationRestrictionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}