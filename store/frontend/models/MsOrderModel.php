<?php

class MsOrderModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_ORDER_CONFIG';
        $this->controller = 'order';
        $this->method = 'post';
        parent::__construct();
    }

    public function pushOrderQueue($order_id)
    {

        $timestamp = time();
        $payload = ['data' => [ 'params' => ['order_id' => $order_id, 'timestamp' =>$timestamp]]];
        $this->action = 'push-order-queue';

        $result = $this->request(['payload' => $payload]);
        if (!$result)
        {
            Yii::app()->slack->send('Failed to push order queue', $payload, 'DEFAULT');
        }

        return true;
    }

    public function pushMailQueue($data)
    {
        $this->action = 'push-mail-queue';

        $result = $this->request(['data' => $data]);
        if (!$result)
        {
            Yii::app()->slack->send('Failed to push mail queue', $data, 'DEFAULT');
        }

        return true;
    }

    public function getTaxInfo($data){
        $this->action = 'get-order-tax';

        $result = $this->request($data);

        return $result;
    }

    public function createOrder($data)
    {
        $this->action = 'place-order';

        $result = $this->request($data);

        return $result;
    }
}
