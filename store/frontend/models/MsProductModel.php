<?php

class MsProductModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_PRODUCT_CONFIG';
        $this->controller = 'mobile-recharge';
        $this->method = 'get';
        parent::__construct();
    }

    public function getRegionList()
    {
        $this->action = 'get-active-region';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'mobile-recharge/get-active-region';
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request();
            if (!empty(is_array($return_arr)) && !empty($return_arr[0]['iso3'])) {
                Yii::app()->cache->set($cache_key, $return_arr, 3600); // cache 30 minutes
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to get region list', $attachments, 'DEFAULT');
            }
        }

        return $return_arr;
    }

    public function validatePhoneAccount($prefix, $phone, $language_id)
    {
        $this->action = 'get-operator-by-phone?' . http_build_query(['phone' => $phone, 'prefix' => $prefix, 'language_id' => $language_id]);
        $response = $this->request();

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'mobile-recharge/validate_phone/' . $prefix . '/' . $phone;

        if (!empty($response['status']) && $response['status'] == true) {
            Yii::app()->cache->set($cache_key, true, 3600);
        } else {
            Yii::app()->cache->delete($cache_key);
        }

        return $response;
    }

    public static function isPhoneNumberValidated($prefix, $phone)
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'mobile-recharge/validate_phone/' . $prefix . '/' . $phone;
        return Yii::app()->cache->get($cache_key);
    }

    public function getDenomination($id, $language_id)
    {
        $this->action = 'get-denomination-detail?' . http_build_query(['id' => $id, 'language_id' => $language_id]);
        return $this->request();
    }

    public function getPrimaryBrandInfo($url_alias, $language_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'brand';
        $this->action = 'get-brand-info';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'brand/primary-brand/' . $url_alias . '/' . $language_id . '/' . $country_code . '/' . $ip_country;

        $result = Yii::app()->cache->get($cache_key);

        if ($result === false) {
            $data = [
                'url_alias' => $url_alias,
                'language_id' => $language_id,
                'country_code' => $country_code,
                'ip_country' => $ip_country
            ];

            $result = $this->request($data);

            if (empty($result['error_code'])) {
                Yii::app()->cache->set($cache_key, $result);
            }
        }

        return $result;
    }

    public function getSubBrandInfo($main_url_alias, $url_alias, $language_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'brand';
        $this->action = 'get-sub-brand-info';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'brand' . $main_url_alias . '/sub-brand/' . $url_alias . '/' . $language_id . '/' . $country_code . '/' . $ip_country;

        $result = Yii::app()->cache->get($cache_key);

        if ($result === false) {
            $data = [
                'main_url_alias' => $main_url_alias,
                'url_alias' => $url_alias,
                'language_id' => $language_id,
                'country_code' => $country_code,
                'ip_country' => $ip_country
            ];
            $result = $this->request($data);

            if (empty($result['error_code'])) {
                Yii::app()->cache->set($cache_key, $result);
            }
        }

        return $result;
    }


    public function getCategoryInfo($url_alias, $language_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'brand';
        $this->action = 'get-category-info';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'brand/category/' . $url_alias . '/' . $language_id . '/' . $country_code . '/' . $ip_country;

        $result = Yii::app()->cache->get($cache_key);

        if ($result === false) {
            $data = [
                'url_alias' => $url_alias,
                'language_id' => $language_id,
                'country_code' => $country_code,
                'ip_country' => $ip_country
            ];
            $result = $this->request($data);

            if (empty($result['error_code'])) {
                Yii::app()->cache->set($cache_key, $result);
            }
        }

        return $result;
    }

    public function getBestSelling($language_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'best-selling';
        $this->action = 'index';

        $data = [
            'language_id' => $language_id,
            'country_code' => $country_code,
            'ip_country' => $ip_country
        ];

        $result = $this->request($data);

        return $result;
    }

    public function getBrandList($page, $language_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'brand';
        $this->action = 'listing';

        $data = [
            'page' => $page,
            'pageSize' => 6,
            'language_id' => $language_id,
            'country' => $country_code,
            'ip_country' => $ip_country
        ];

        $result = $this->request($data);

        return $result;
    }

    public function getCheckoutPermission($products_id, $country_code, $ip_country)
    {
        $this->method = 'POST';
        $this->controller = 'product';
        $this->action = 'get-product-checkout-permission';

        $data = [
            'products_id' => $products_id,
            'country_code' => $country_code,
            'ip_country' => $ip_country
        ];

        $result = $this->request($data);

        return $result;
    }


    public function getReviewByBrandId($type, $url_alias, $page, $limit)
    {
        $this->method = 'POST';
        $this->controller = 'brand';
        $this->action = 'get-review';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'brand/review/' . $type . '/' . $url_alias . '/' . $page . '/' . $limit;

        $result = Yii::app()->cache->get($cache_key);

        if ($result === false) {
            $data = [
                'type' => $type,
                'url_alias' => $url_alias,
                'page' => $page,
                'limit' => $limit
            ];
            $result = $this->request($data);

            if (empty($result['error_code'])) {
                Yii::app()->cache->set($cache_key, $result);
            }
        }

        return $result;
    }

    public function getProductUrlImage($products_list, $language_id)
    {
        $this->method = 'POST';
        $this->controller = 'product';
        $this->action = 'get-product-url';

        $data = [
            'products_id' => $products_list,
            'language_id' => $language_id,
        ];

        $result = $this->request($data);

        return $result;
    }
}
