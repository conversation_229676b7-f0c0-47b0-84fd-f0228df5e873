<?php

class MyOffgamersForm extends CFormModel
{

    public $page_content, $title, $order_id, $orders_products_id, $customer_id, $customer_name, $customer_email, $order_details, $order_products, $sub_products, $order_history, $order_status, $order_status_title, $order_status_text, $order_date, $order_currency, $order_currency_value, $order_pm, $order_msg, $order_msg_type, $order_expiry_msg, $order_timer, $order_timer_type, $order_cancelled, $show_amount_delivered, $show_amount_refunded, $amount_delivered, $amount_refunded, $order_total, $payment_method_list = array(), $cdk_list = array(), $aft_executed, $sc_amount, $page = 1, $language, $pending_payment;

    public function __construct()
    {
        $this->getCustomersInfo();
    }

    public function getBreadcrumbs()
    {
        $return_array = array();
        $return_array[Yii::t('page_content', 'TEXT_MY_OGM')] = array('account/index');
        $return_array[] = $this->title;
        return $return_array;
    }

    public function getLatestTransaction()
    {
        return OrdersCustomersPurchased::model()->getCustomersOrderHistory(Yii::app()->user->id, 3, 'INTERVAL 14 DAY');
    }

    public function getOrderHistory($page = 1, $order_status = 'all')
    {
        $pageCount = 20;
        $offset = ($page - 1) * $pageCount;
        return OrdersCustomersPurchased::model()->getCustomersOrderHistory(Yii::app()->user->id, $pageCount, "INTERVAL 24 MONTH", true, $offset, $order_status);
    }

    public function getReviewStatus($orders_id)
    {
        $model = new OrderReviewModel();
        return $model->getOrderReviewStatusByOid($orders_id);
    }

    public function checkOrdersOwnership()
    {
        if (count(OrdersCustomersPurchased::model()->getOrderDetail(Yii::app()->user->id, $this->order_id))) {
            return true;
        } else {
            return false;
        }
    }

    public function getCustomersInfo()
    {
        $data = Yii::app()->frontPageCom->getUserBarContent();
        $return_array = [
            'user_info' => [
                'customer_name' => $data['loggedIn_info']['user_info']['customer_first_name'],
                'avatar' => $data['loggedIn_info']['user_info']['avatar'],
                'user_id' => $data['loggedIn_info']['user_info']['menu'][0]['label'],
                'buyer_status' => $data['loggedIn_info']['user_info']['menu'][1]['label'],
                'href' => $data['loggedIn_info']['user_info']['menu'][3]['href'],
            ],
            'menu' => [
                [
                    "label" => Yii::t('page_content', 'TEXT_OVERVIEW'),
                    'icon_css' => 'icon-view-thumb',
                    'icon' => 'view-thumb',
                    'href' => Yii::app()->createUrl('account/index')
                ],
                [
                    "label" => Yii::t('page_content', 'TEXT_BUY_HISTORY'),
                    'icon_css' => 'icon-to-do-bigger',
                    'icon' => 'to-do',
                    'href' => Yii::app()->createUrl('account/viewOrders')
                ],
            ]
        ];
        // Check if Store Credit Enable/Disable?
        if (Yii::app()->params['MICROSERVICE_STORECREDIT']['MS_STATUS'] == 1) {
            $return_array['menu'][] = [
                "label" => Yii::t('page_content', 'TEXT_STORE_CREDITS'),
                'icon_css' => 'icon-link',
                'icon' => 'sc-coin-main',
                'href' => '#',
                'submenu' => [
                    [
                        'label' => Yii::t('page_content', 'TEXT_TOPUP'),
                        'icon_css' => 'icon-view-thumb',
                        'icon' => 'sc-top-up',
                        'href' => Yii::app()->createUrl('storeCredit/index'),
                    ],
                    [
                        'label' => Yii::t('page_content', 'TEXT_SC_STATEMENT'),
                        'icon_css' => 'icon-view-thumb',
                        'icon' => 'sc-list',
                        'href' => Yii::app()->createUrl('storeCredit/statement'),
                    ],
                    [
                        'label' => Yii::t('page_content', 'TEXT_REDEEM_GIFT_CARD'),
                        'icon_css' => 'icon-view-thumb',
                        'icon' => 'sc-gift-card',
                        'href' => Yii::app()->createUrl('storeCredit/redeemGiftCard'),
                    ],
                ]
            ];
        }

        if (TaxModuleCom::getTaxSettingStatus(true) == 1) {
            $return_array['menu'][] = [
                'label' => Yii::t('taxModule', 'TEXT_TAX'),
                'icon_css' => 'icon-view-thumb',
                'icon' => 'bank',
                'href' => Yii::app()->createUrl('account/tax')
            ];
        }

        $return_array['menu'][] = [
            'label' => Yii::t('page_content', 'TEXT_REVIEW'),
            'icon_css' => 'icon-view-thumb',
            'icon' => 'star',
            'href' => Yii::app()->createUrl('account/viewReviews')
        ];

        $this->page_content = $return_array;
    }

    public function getOrderDetails()
    {
        $results = OrdersCustomersPurchased::model()->getOrderDetail(Yii::app()->user->id, $this->order_id);
        $cur_obj = Yii::app()->currency;
        $f_data = array();
        if (count($results)) {
            $data = $results[0];
            $this->order_status = $data['orders_status'];
            $this->order_status_text = $data['orders_status_name'];
            $this->order_status_title = $this->getOrderStatusLabel($data['orders_status']);
            $this->order_pm = $data['payment_methods_id'];
            $this->order_currency = $data['currency'];
            $date_purchased = $data['date_purchased'];
            $this->customer_id = $data['customers_id'];
            $this->customer_name = $data['customers_name'];
            $this->customer_email = $data['customers_email_address'];
            $this->order_date = $date_purchased;
            $this->order_currency_value = $data['currency_value'];
            $this->language = Yii::app()->session->get('language');
            $order_details = array(
                'payment_method' => $data['payment_method'],
                'order_id' => $data['orders_id'],
                'date_purchased' => $data['date_purchased']
            );
            $f_data['orders_tag_ids'] = (!empty($data['orders_tag_ids']) ? explode(",", $data['orders_tag_ids']) : array());
            $f_data['payment_methods_parent_id'] = (!empty($data['payment_methods_parent_id']) ? $data['payment_methods_parent_id'] : 0);
            $f_data['customers_id'] = (!empty($data['customers_id']) ? $data['customers_id'] : 0);
            $f_data['pm_receipt'] = false;
            $f_data['detail'] = $order_details;
            if ($data['payment_method'] == '') {
                $this->pending_payment = true;
            }
        } else {
            Yii::app()->user->setFlash('error', Yii::t('myogm', 'ERROR_MSG_INVALID_ORDER_NUM'));
            Yii::app()->request->redirect(Yii::app()->createAbsoluteUrl('account/viewOrders'));
        }
        foreach ($results as $_data) {
            $_currency = $_data['currency'];
            $_currency_val = $_data['currency_value'];
            $_class = $_data['class'];
            $_title = $_data['title'];
            $_text = $_data['text'];
            $_val = $cur_obj->reformat($_currency, $_text);

            switch ($_class) {
                case 'ot_subtotal':
                    $_class = 'subtotal';
                    break;

                case 'ot_surcharge':
                    $_class = 'surcharge';
                    break;

                case 'ot_ogc':
                    $_class = 'ogc';
                    $_title = str_replace(array('Gift Card', ':'), '', $_title);
                    break;

                case 'ot_gv':
                    $_class = 'sc';
                    break;

                case 'ot_gst':
                    $_class = 'gst';
                    break;

                case 'ot_coupon':
                    $_class = 'coupon';
                    $_title = str_replace(array('Discount Coupons', '(if there is any)', ':'), '', $_title);
                    break;

                case 'ot_total':
                    $_class = 'total';
                    break;
            }

            $f_data['payment'][$_class] = array('title' => $_title, 'text' => $_text, 'value' => $_val);
        }
        $this->order_details = $f_data;

        if (OrdersCancelRequestBase::model()->checkRequestExists($this->order_id)) {
            $this->order_cancelled = true;
        }

        /* Sub total */
        if ((!$this->order_cancelled && ($this->order_status != 1)) || ($this->order_cancelled && ($this->order_status == 3))) {
            $this->show_amount_delivered = true;
            if (!empty($amtRefunded) && $amtRefunded > 0) {
                $this->show_amount_refunded = true;
            }
            $this->show_amount_refunded = true;
        }

        //Show Expiry Message
        $expiryTimeMsg = '';
        if ($this->order_status == 1 && ($this->order_cancelled == false)) {
            $pmExpiryTimeArray = $this->getOrderExpiryTime($this->order_date);
            $expiryTimeMsg .= sprintf(Yii::t('myogm', 'MSG_ORDER_CANCELLED'), $pmExpiryTimeArray['hrs'], $pmExpiryTimeArray['mins']);
            $expiryTimeMsg .= '<br />' . Yii::t('myogm', 'MSG_PAYMENT_INFORMATION');
        }

        $this->order_expiry_msg = $expiryTimeMsg;
    }

    public function getOrderProducts()
    {
        $order_products = OrdersProductsBase::model()->getOrderProducts($this->order_id);
        $products_list = [];
        $sub_products_list = [];
        foreach ($order_products as $product) {
            if ($product['products_bundle_id'] == 0) {
                // Check for Compensate Store Credit
                if ($product['products_id'] == -1 && $product['orders_products_is_compensate'] == 1 && $product['currency'] != ConfigurationCom::getValue('DEFAULT_CURRENCY')) {
                    // get rate for compensate order currency
                    $compensateInfo = OrdersCompensateProductsBase::model()->getCompensateProductInfo($product['orders_products_id']);
                    $unit_price = Yii::app()->currency->rounding($product['currency'], $product['final_price'], $compensateInfo->compensate_order_currency_value);
                    $delivered_price = Yii::app()->currency->roundByCurrency($product['currency'], $unit_price * $product['products_good_delivered_quantity']);
                    $canceled_price = Yii::app()->currency->roundByCurrency($product['currency'], $unit_price * $product['products_canceled_quantity']);
                } elseif ($product['custom_products_type_id'] == 3) {
                    $price_in_cur = bcmul($product['final_price'], $product['currency_value'], 8);
                    if ($product['products_quantity'] > 1 && $product['currency'] !== 'USD') {
                        $price_in_cur = round($price_in_cur);
                    }
                    // Set Quantity as 1 and display total price
                    $unit_price = Yii::app()->currency->roundByCurrency($product['currency'], $price_in_cur * $product['products_quantity']);
                    $delivered_price = Yii::app()->currency->roundByCurrency($product['currency'], $price_in_cur * $product['products_good_delivered_quantity']);
                    $canceled_price = Yii::app()->currency->roundByCurrency($product['currency'], $price_in_cur * $product['products_canceled_quantity']);
                    $product['products_quantity'] = 1;
                    $product['products_good_delivered_quantity'] = 1;
                    $product['products_canceled_quantity'] = 1;
                } else {
                    $unit_price = Yii::app()->currency->rounding($product['currency'], $product['final_price'], $product['currency_value']);
                    $delivered_price = Yii::app()->currency->roundByCurrency($product['currency'], $unit_price * $product['products_good_delivered_quantity']);
                    $canceled_price = Yii::app()->currency->roundByCurrency($product['currency'], $unit_price * $product['products_canceled_quantity']);
                }

                $_product = array(
                    'id' => $product['products_id'],
                    'name' => $product['products_name'],
                    'quantity' => $product['products_quantity'],
                    'products_type' => $product['products_type'],
                    'custom_products_type_id' => $product['custom_products_type_id'],
                    'delivered_qty' => $product['products_good_delivered_quantity'],
                    'cancelled_qty' => $product['products_canceled_quantity'],
                    'unit_price' => Yii::app()->currency->display_format($unit_price, false, $product['currency'], '', 'sell', ' '),
                    'sub_total' => Yii::app()->currency->display_format($unit_price * $product['products_quantity'], false, $product['currency'], $product['currency_value'], 'sell', ' '),
                    'orders_id' => $product['orders_id'],
                    'orders_products_id' => $product['orders_products_id'],
                    'delivery_mode' => $product['orders_products_extra_info_value'],
                    'show_cdk' => true
                );
                $this->amount_delivered += $delivered_price;
                $this->amount_refunded += $canceled_price;
                $products_list[$product['orders_products_id']] = $_product;
            } else {
                $_sub_product = array(
                    'id' => $product['products_id'],
                    'name' => $product['products_name'],
                    'quantity' => $product['products_quantity'],
                    'products_type' => $product['products_type'],
                    'custom_products_type_id' => $product['custom_products_type_id'],
                    'orders_id' => $product['orders_id'],
                    'delivered_qty' => $product['products_delivered_quantity'],
                    'cancelled_qty' => $product['products_canceled_quantity'],
                    'orders_products_id' => $product['orders_products_id'],
                    'delivery_mode' => $product['orders_products_extra_info_value'],
                );

                // Hide Parent Products when contains sub products, by right shouldn't showing both
                $products_list[$product['parent_orders_products_id']]['show_cdk'] = false;
                $sub_products_list[] = $_sub_product;
            }
        }
        if($this->show_amount_delivered && $this->amount_delivered > 0){
            $this->amount_delivered =  Yii::app()->currency->display_format($this->amount_delivered, false, $this->order_currency, '', 'sell', ' ');
        }
        else{
            $this->show_amount_delivered = false;
        }
        if($this->show_amount_refunded && $this->amount_refunded > 0){
            $this->amount_refunded =  Yii::app()->currency->display_format($this->amount_refunded, false, $this->order_currency, '', 'sell', ' ');
        }
        else{
            $this->show_amount_refunded = false;
        }

        $this->sub_products = $sub_products_list;
        return $this->order_products = $products_list;
    }

    public function getOrderStatusHistoryDetails()
    {
        $historyInfo = OrdersStatusHistoryBase::model()->getOrderStatusHistoryInfo($this->order_id);
        $_result = array();
        foreach ($historyInfo as $info) {
            $status_name = (isset($info->orders_status[0]->orders_status_name) ? $info->orders_status[0]->orders_status_name : '');
            if ($info->customer_notified == 1 && (!empty($info->comments))) {
                $comment = ($info->customer_notified == 1 ? nl2br($info->comments) : '');
                $_result[] = array(
                    'statusName' => $status_name,
                    'statusId' => $info->orders_status_id,
                    'statusDate' => date('d M Y', strtotime($info->date_added)),
                    'statusComment' => $comment,
                    'statusRead' => $info->customer_notified
                );
            }
        }
        $this->order_history = $_result;
    }

    public function getOrdersStatusMessage()
    {
        $datetime1 = strtotime(OrdersStatusStatBase::model()->getLatestDate($this->order_id, $this->order_status));
        $datetime2 = strtotime('now');
        $second_passed = $datetime2 - $datetime1;
        $week_end_timer = 172800;
        switch ($this->order_status) {
            case 1: // Pending (Awaiting Payment)
                $this->order_msg = Yii::t('myogm', 'TEXT_CONTACT_US', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                $this->order_msg_type = '';
                $offline_tid = OrdersCustomersPurchased::model()->getPaymentMethodTypeId($this->order_pm);
                $offline_pid = $this->order_pm;

                # order canceled
                if ($this->order_cancelled) {
                    $this->order_msg = Yii::t('myogm', 'TEXT_CANCELLATION_PROGRESS');
                } else {
                    if (in_array($offline_tid, array('7', '8', '9'))) {
                        if ($second_passed < $week_end_timer) {
                            # hidden timer
                            $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_PAYMENT');
                            $this->order_timer_type = 'hide';
                            $this->order_timer = $week_end_timer - $second_passed;
                            //                        $this->order_msg .= '<br>(' .         $classOrderComponent->submitPaymentMessage($offline_pid) . ')';
                        }

                        # Non-Offline PG
                    } else {
                        if ($second_passed < 300) { // 5 minutes
                            $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_TIMER', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                            $this->order_timer = 300 - $second_passed;
                        } else {
                            $this->order_msg = Yii::t('myogm', 'TEXT_NO_PAYMENT', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                        }
                    }
                }

                if ($payment_obj = PipwavePaymentBase::model()->findByPK($this->order_id)) {
                    if ($payment_obj->require_upload == 1) {

                        $pmReceiptSignature = hash('sha256', $payment_obj->api_key . $this->order_id . $payment_obj->pw_id);
                        $this->order_details['pm_receipt_url'] = Yii::app()->params['PIPWAVE_SECURE_DOMAIN'] . '/pay/instructions/?api_key=' . $payment_obj->api_key . '&txn_id=' . $this->order_id . '&signature=' . $pmReceiptSignature;

                        if ($m_pei = OrdersExtraInfoBase::model()->findByAttributes(array(
                            'orders_id' => $this->order_id,
                            'orders_extra_info_key' => 'payment_images_1'
                        ))) {
                            $this->order_msg = Yii::t('myogm', 'TEXT_VERIFYING_PAYMENT_RECEIPT');
                        } else {
                            $this->order_details['pm_receipt'] = true;
                            $this->order_msg = Yii::t('myogm', 'TEXT_NO_PAYMENT_RECEIPT');
                        }
                    }
                }

                break;
            case 7: // Verifying (Payment Received)
                $this->order_msg_type = '';

                #  order canceled
                if ($this->order_cancelled) {
                    $this->order_msg = Yii::t('myogm', 'TEXT_CANCELLATION_PROGRESS');
                } else {
                    if (OrdersBase::model()->isFlagByType(OrdersBase::LL_FLAG, $this->order_details['orders_tag_ids'])) {
                        if (!CustomersVerificationDocumentBase::model()->requireVerification()) {
                            if ($latest_upload_date = CustomersVerificationDocumentLogBase::model()->lastRecord()->find()) {
                                # hidden timer
                                $this->order_msg = Yii::t('myogm', 'TEXT_DOCUMENT_REVIEW');
                                $this->order_timer_type = 'hide';
                                $this->order_timer = $datetime2 + $week_end_timer - strtotime($latest_upload_date->log_datetime);
                            }
                        } else {
                            $this->order_msg = Yii::t('myogm', 'TEXT_SECURITY_MEASURE', array(
                                '{URL}' => Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/verificationForm/index',
                                '{URL2}' => 'https://helpdesk.offgamers.com/support/solutions/articles/5000884266-types-of-alternate-payment-options'
                            ));
                            $this->order_msg_type = 'alert';
                        }
                    } else {
                        if (OrdersBase::model()->isFlagByType(OrdersBase::PAYPAL_EMAIL_FLAG, $this->order_details['orders_tag_ids'])) {
                            $email_info = OrdersBase::model()->isPaymentEmailRequiredVerification($this->order_details['payment_methods_parent_id'], $this->order_details['customers_id'],
                                $this->order_id);

                            if (isset($email_info['payer_mail'])) {
                                $this->order_msg = Yii::t('myogm', 'TEXT_VERIFY_EMAIL', array('{PAYPAL_EMAIL}' => $this->maskEmailAddress($email_info['payer_mail'])));
                                $this->order_msg_type = 'alert';
                            } 
                        }
                    }
                }

                if (!isset($this->order_msg)) {
                    if ($second_passed < 1800) {
                        # hidden timer
                        $this->order_msg = Yii::t('myogm', 'TEXT_PENDING_VERIFICATION');
                        $this->order_timer_type = 'hide';
                        $this->order_timer = 1800 - $second_passed;
                    } else {
                        $this->order_msg = Yii::t('myogm', 'TEXT_CONTACT_US', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                    }
                }
                break;
            case 2: // Processing (In Progress)
                $this->order_msg_type = '';

                if ($this->order_cancelled) {
                    // order canceled
                    $this->order_msg = Yii::t('myogm', 'TEXT_CANCELLATION_PROGRESS');
                } else {
                    if ($second_passed < 259200) { // 72 hours
                        $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_DELIVERY_PROGRESS');
                        $this->order_timer = 259200 - $second_passed;
                    } else {
                        $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_DELIVERY_CONTACT', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                    }
                }
                break;
            case 3: // Completed
                if ($this->amount_refunded > 0) {
                    # amount refunded
                    $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_REFUNDED', array('{URL}' => '#order_message'));
                    $this->order_msg_type = '';
                } else {
                    $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_COMPLETED');
                }
                break;
            case 5: // cancel
                // order canceled
                $this->order_msg = Yii::t('myogm', 'TEXT_ORDER_CANCELLED', array('{URL}' => '#order_message'));
                $this->order_msg_type = '';
                break;
            case 8: // On Hold (Payment Disputed)
                $this->order_msg = Yii::t('myogm', 'TEXT_PAYMENT_DISPUTE', array('{URL}' => Yii::t('myogm', 'LINK_SUPPORT_URL')));
                $this->order_msg_type = 'alert';
                break;
        }
    }

    public function getOrderExpiryTime($datePurchased)
    {
        $datePurchased = explode(" ", $datePurchased);
        $date = explode("-", $datePurchased[0]);
        $time = explode(":", $datePurchased[1]);

        $pmCancelPeriod = 120; // 2 hours in minutes
        $pmCancelTimeInSecs = mktime($time[0], $time[1] + $pmCancelPeriod, $time[2], $date[1], $date[2], $date[0]);
        $currentTimeInSecs = mktime(date('H'), date('i'), date('s'), date('m'), date('d'), date('Y'));
        $pmCancelHoursLeft = ($pmCancelTimeInSecs - $currentTimeInSecs) / 3600;
        $pmCancelMinutesLeft = ($pmCancelHoursLeft - intval($pmCancelHoursLeft)) * 60;

        $pmCancelPeriodArray = array('hrs' => intval($pmCancelHoursLeft), 'mins' => intval($pmCancelMinutesLeft));

        return $pmCancelPeriodArray;
    }

    public function maskEmailAddress($email)
    {
        $prefix = substr($email, 0, strrpos($email, '@'));
        $suffix = substr($email, strripos($email, '@'));
        return substr($prefix, 0, strlen($prefix) - 3) . '***' . $suffix;
    }

    public function orderDetailTemplate($text, $value, $id = null)
    {
        echo '<div class="order-total__descr-row"><div class="order-total__descr-col order-total__descr-col--title"><span class="order-total__descr-inner">' . $text . '</span></div><div class="order-total__descr-col order-total__descr-col--val"><span ' . (!empty($id) ? 'id=' . $id : '') . ' class="order-total__descr-inner">' . $value . '</span></div></div>';
    }

    public function dtuMessageTemplate($dtu)
    {
        $message = '';
        foreach ($dtu as $_key => $_val) {
            if ($_key == 'info') {
                foreach ($_val as $_data) {
                    $message .= $_data;
                }
            } else {
                $message .= $_val;
            }
        }
        return $message;
    }

    public function getOrderStatusLabel($orders_status_id)
    {
        $return_str = '';

        switch ($orders_status_id) {
            case 1: // Pending (Awaiting Payment)
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_PENDING');
                break;
            case 7: // Verifying (Payment Received)
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_VERIFYING');
                break;
            case 2: // Processing (In Progress)
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_PROCESSING');
                break;
            case 3: // Completed
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_COMPLETED');
                break;
            case 5: // cancel
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_CANCELED');
                break;
            case 8: // On Hold (Payment Disputed)
                $return_str = Yii::t('myogm', 'TEXT_ORDER_STATUS_ON_HOLD');
                break;
        }

        return $return_str;
    }

    function getTopUpStatus($passId = '')
    {
        $topupStatusArr = array();
        $topupStatusArr[0] = 'Unknown';
        $topupStatusArr[1] = 'Pending';
        $topupStatusArr[3] = 'Reloaded';
        $topupStatusArr[10] = 'Failed';
        $topupStatusArr[11] = 'Not Found';

        return (isset($topupStatusArr[(int)$passId]) ? $topupStatusArr[(int)$passId] : $topupStatusArr[0]);
    }

    public function getAllCDDTUKey()
    {
        foreach ($this->order_products as $index => $product) {
            if ($product['custom_products_type_id'] == 2) {
                if ($product['show_cdk'] && $product['delivery_mode'] == 6) {
                    $this->order_products[$product['orders_products_id']]['dtu_info'] = $this->getDTUInfo($product['id'], $product['orders_products_id']);
                } else {
                    if ($product['show_cdk'] && $product['delivery_mode'] != 6 && $product['products_type'] != 4) {
                        if (in_array($this->order_status, array(2, 3))) {
                            $this->cdk_list[] = $this->getCDKInfo($product);
                        }
                    }
                }
            }
        }

        if (in_array($this->order_status, array(2, 3))) {
            foreach ($this->sub_products as $product) {
                if ($product['delivery_mode'] != 6 && $product['custom_products_type_id'] == 2 && $product['products_type'] != 4) {
                    $this->cdk_list[] = $this->getCDKInfo($product);
                }
            }
        }
    }

    private function getCDKInfo($product)
    {
        $cdk_info = CDKeyCom::getCdkeyIdentifier($product['id'], $product['orders_products_id']);
        return array(
            'products_id' => $product['id'],
            'products_name' => $product['name'],
            'orders_products_id' => $product['orders_products_id'],
            'quantity' => $product['quantity'],
            'delivered_qty' => $product['delivered_qty'],
            'cancelled_qty' => $product['cancelled_qty'],
            'cdk_info' => $cdk_info['cdk'],
            'info' => $cdk_info['info']
        );
    }

    private function getDTUInfo($pid, $opid)
    {
        $_dtu_info = array();

        if (!empty($pid) && !empty($opid)) {
            $m_data = CustomersTopUpInfoBase::model()->getCustomerTopupInfo($opid);

            if (!empty($m_data)) {
                $_data = array();

                foreach ($m_data as $_topup) {
                    $_data[$_topup->top_up_info_id]['value'] = $_topup->top_up_value;
                }

                if (!empty($_data)) {
                    $m_data = TopUpInfoLangBase::model()->getTopupDisplay($_data);

                    foreach ($m_data as $topupDisplay) {
                        if (isset($topupDisplay->top_up_info->top_up_info_key) && $topupDisplay->top_up_info->top_up_info_key == 'server') {
                            $dtu_server = PublishersProductsBase::model()->getServers($pid);

                            if (isset($dtu_server[$_data[$topupDisplay->top_up_info_id]['value']])) {
                                $_data[$topupDisplay->top_up_info_id]['value'] = $dtu_server[$_data[$topupDisplay->top_up_info_id]['value']];
                            }
                        } else {
                            if (isset($topupDisplay->top_up_info->top_up_info_key) && $topupDisplay->top_up_info->top_up_info_key == 'character') {
                                if (stristr($_data[$topupDisplay->top_up_info_id]['value'], '##')) {
                                    $characterTmp = explode("##", $_data[$topupDisplay->top_up_info_id]['value']);
                                    $_data[$topupDisplay->top_up_info_id]['value'] = $characterTmp[1];
                                }
                            }
                        }

                        $_dtu_info['info'][$topupDisplay->top_up_info_id] = $topupDisplay->top_up_info_display . ': ' . $_data[$topupDisplay->top_up_info_id]['value'] . "<br/>";
                    }
                }

                $topupStatus = OrdersTopUpBase::model()->getTopupStatus($opid);
                $_dtu_info['status'] = Yii::t('myogm', 'TEXT_TOP_UP_STATUS') . ": " . (isset($topupStatus) && $topupStatus > 0 ? $this->getTopupStatus($topupStatus) : '-') . "<br>";

                if (isset($topupStatus) && in_array($topupStatus, array(1, 3, 10))) {
                    $m_data = PublishersGamesBase::model()->getPublisherMsg($pid);
                    $_dtu_info['message'] = Yii::t('myogm', 'TEXT_TOP_UP_MESSAGE') . ' : ';

                    if (isset($m_data)) {
                        switch ($topupStatus) {
                            case 1:
                                $_dtu_info['message'] .= $m_data['publishers_games_pending_message'];
                                break;
                            case 3:
                                $_dtu_info['message'] .= $m_data['publishers_games_reloaded_message'];
                                break;
                            case 10:
                                $_dtu_info['message'] .= $m_data['publishers_games_failed_message'];
                                break;
                        }
                    }
                }
            }
        }

        return $_dtu_info;
    }

    public function validateCancelOrderRequest()
    {
        $results = OrdersCustomersPurchased::model()->getOrderDetail(Yii::app()->user->id, $this->order_id);
        if (!$results) {
            throw new CHttpException(300, 'ERROR_MSG_UNAUTHORISED_CANCEL_ORDER');
        }
        if (OrdersCancelRequestBase::model()->checkRequestExists($this->order_id)) {
            $this->order_cancelled = true;
            throw new CHttpException(300, 'ERROR_MSG_UNAUTHORISED_CANCEL_ORDER');
        }
        if (!$this->order_cancelled) {
            $cur_obj = Yii::app()->currency;
            $f_data = array();
            $paymentMethodsArray = array();
            if (count($results)) {
                $data = $results[0];
                $this->order_status = $data['orders_status'];
                if (!in_array($this->order_status, array(1, 3, 7))) {
                    throw new CHttpException(300, 'ERROR_MSG_UNAUTHORISED_CANCEL_ORDER');
                };
                $this->order_status_title = $this->getOrderStatusLabel($data['orders_status']);
                $this->customer_id = $data['customers_id'];
                $this->customer_name = $data['customers_name'];
                $this->customer_email = $data['customers_email_address'];
                $this->order_status_text = $data['orders_status_name'];
                $this->order_pm = $data['payment_methods_id'];
                $this->order_currency = $data['currency'];
                $this->aft_executed = $data['orders_aft_executed'];
                $date_purchased = $data['date_purchased'];
                $this->order_date = $date_purchased;
                $this->amount_refunded = OrdersProductsBase::model()->getProductsAmountRefunded($this->order_id);
                $order_details = array(
                    'payment_method' => $data['payment_method'],
                    'order_id' => $data['orders_id'],
                    'date_purchased' => $data['date_purchased']
                );
                $f_data['orders_tag_ids'] = (!empty($data['orders_tag_ids']) ? explode(",", $data['orders_tag_ids']) : array());
                $f_data['payment_methods_parent_id'] = (!empty($data['payment_methods_parent_id']) ? $data['payment_methods_parent_id'] : 0);
                $f_data['customers_id'] = (!empty($data['customers_id']) ? $data['customers_id'] : 0);
                $f_data['pm_receipt'] = false;
                $f_data['detail'] = $order_details;
            }
            foreach ($results as $_data) {
                $_currency = $_data['currency'];
                $_currency_val = $_data['currency_value'];
                $_class = $_data['class'];
                $_title = $_data['title'];
                $_text = $_data['text'];
                $_val = $_data['value'];

                if ($_class === 'ot_coupon') {
                    $paymentMethodsArray[] = Yii::t('myogm', 'TEXT_DISCOUNT_COUPON');
                }
                if ($_class === 'ot_gv') {
                    $paymentMethodsArray[] = Yii::t('myogm', 'TEXT_STORE_CREDIT');
                    $this->sc_amount = $_text;
                }
                if ($_class === 'ot_total' && $_val > 0) {
                    $paymentMethodsArray[] = $data['payment_method']; //'CashU';
                }
                if ($_class === 'ot_subtotal') {
                    $this->order_total = $cur_obj->display_format($_val, true, $_currency, $_currency_val, 'sell', ' ');
                }
                $f_data['payment'][$_class] = array('title' => $_title, 'text' => $_text, 'value' => $_val);
            }
            $this->payment_method_list = $paymentMethodsArray;
            $this->order_details = $f_data;
        }
    }

    public function getProductCodeImage($cpId)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();
        $modelOrders = new OrdersBase();
        $imgSource = '';
        if (Yii::app()->customerCom->isActiveCustomer() === false) {
            $imgSource = Yii::t('myogm', 'TEXT_CDKEY_SUSPENDED_FOR_VIEWING');
            $imgSource = <<<TEXT
                            <div class="codes__preview-col codes__preview-col--content">
								<span class="codes__preview-text">$imgSource</span>
							</div>
TEXT;
        } else {
            $productCodeInfo = $modelCustomProductsCode->findByPk($cpId);
            if (isset($productCodeInfo->custom_products_code_id)) {
                $orderIsVerify = $modelOrders->verifyOrdersProducts($productCodeInfo->orders_products_id);
                if ($orderIsVerify) {
                    if ($productCodeInfo->file_type == 'soft') {
                        $imgSource = Yii::app()->cdKeyCom->getCdkeyImg($cpId);
                        $imgSource = <<<TEXT
                            <div class="codes__preview-col codes__preview-col--content">
								<span class="codes__preview-text">$imgSource</span>
							</div>
TEXT;
                    } else {
                        $imgSource = Yii::app()->cdKeyCom->getBase64Img($cpId);
                        $imgSource = <<<TEXT
<div class="codes__preview-col codes__preview-col--img">
								$imgSource
							</div>
TEXT;
                    }
                }
            }
        }

        return '<cdkey_image><![CDATA[' . $imgSource . ']]></cdkey_image>';
    }

    public function getAllProductCodeImage($temp_array)
    {
        $modelCustomProductsCode = new CustomProductsCodeBase();
        $modelOrders = new OrdersBase();

        $ids_arr = array();
        $tmpXmlResStr = '';

        foreach ($temp_array as $inputCDKeyID) {
            if (is_numeric($inputCDKeyID) && $inputCDKeyID > 0) {
                $ids_arr[] = $inputCDKeyID;
            }
        }

        $allImgAreValid = true;
        if (count($ids_arr) > 0) {
            $tmpXmlResStr = '<cdkey_image>';
            $ids_arr_chunk = array_chunk($ids_arr, 50);
            foreach ($ids_arr_chunk as $ids_arr) {
                $cdKeyInfoResult = $modelCustomProductsCode->getProductCodeInfo($ids_arr);
                foreach ($cdKeyInfoResult as $value) {
                    if (Yii::app()->customerCom->isActiveCustomer() === false) {
                        $imgSource = Yii::t('myogm', 'TEXT_CDKEY_SUSPENDED_FOR_VIEWING');
                        $imgSource = <<<TEXT
                            <div class="codes__preview-col codes__preview-col--content">
								<span class="codes__preview-text">$imgSource</span>
							</div>
TEXT;
                    } else {
                        if ((int)$value->orders_products_id > 0) {
                            if ($modelOrders->verifyOrdersProducts($value->orders_products_id)) { // This is the cd key for this customer
                                if ($value->file_type == 'soft') {
                                    $imgSource = Yii::app()->cdKeyCom->getCdkeyImg((int)$value->custom_products_code_id);
                                    $imgSource = <<<TEXT
                            <div class="codes__preview-col codes__preview-col--content">
								<span class="codes__preview-text">$imgSource</span>
							</div>
TEXT;
                                } else {
                                    $imgSource = Yii::app()->cdKeyCom->getBase64Img($value->custom_products_code_id);
                                    $imgSource = <<<TEXT
<div class="codes__preview-col codes__preview-col--img">
								$imgSource
							</div>
TEXT;
                                }
                            } else {
                                $allImgAreValid = false;
                                break;
                            }
                        } else {
                            $allImgAreValid = false;
                            break;
                        }
                    }

                    $tmpXmlResStr .= "<image_" . $value->custom_products_code_id . ">";
                    $tmpXmlResStr .= '<![CDATA[';
                    $tmpXmlResStr .= $imgSource;
                    $tmpXmlResStr .= ']]>';
                    $tmpXmlResStr .= "</image_" . $value->custom_products_code_id . ">";
                }
            }

            $tmpXmlResStr .= '</cdkey_image>';

            if ($allImgAreValid) {
                return $tmpXmlResStr;
            }
        }
        return '';
    }

    public function saveAllCdkey($temp_array)
    {
        $ids_arr = array();
        if (Yii::app()->customerCom->isActiveCustomer() !== false) {
            foreach ($temp_array as $inputCDKeyID) {
                if (is_numeric($inputCDKeyID) && $inputCDKeyID > 0) {
                    $ids_arr[] = $inputCDKeyID;
                }
            }

            if (count($ids_arr) > 0) {
                Yii::app()->cdKeyCom->saveAllOrdersCdkeys($ids_arr);
                Yii::app()->cdKeyCom->getAllCdkey($ids_arr);
            }
        }
    }

    public function downloadAllCdKey()
    {
        $modelOrders = new OrdersBase();
        $cdkey_array = array();
        if ($modelOrders->verifyOrdersProducts($this->orders_products_id)) {
            $op_rows = OrdersProducts::model()->find([
                'condition' => 'orders_products_id = :orders_products_id',
                'params' => array(
                    ':orders_products_id' => $this->orders_products_id,
                )
            ]);

            $cdkey_list = CustomProductsCodeBase::model()->findAll([
                'condition' => 'orders_products_id = :orders_products_id AND status_id = 0',
                'params' => array(
                    ':orders_products_id' => $this->orders_products_id,
                )
            ]);

            foreach ($cdkey_list as $cdkey_row) {
                $cdkey_array[] = $cdkey_row->custom_products_code_id;
            }

            $this->saveAllCdkey($cdkey_array);

            Yii::app()->cdKeyCom->makeBase64ImgZip($op_rows->orders_id, $op_rows->products_id, $cdkey_list);
        } else {
            throw new CHttpException(300, 'ERROR_MSG_NO_AUTHORIZE');
        }
    }

    public function getReviewContent()
    {
        Yii::app()->clientScript->registerScript("js", <<<JS
    
    if(jQuery("#stars").length){
        $('#stars li').on('mouseover', function(){
            var onStar = parseInt($(this).data('value'), 10);

            $(this).parent().children('li.star').each(function(e){
                if (e < onStar) {
                    $(this).addClass('hover');
                }
                else {
                    $(this).removeClass('hover');
                }
            });

        }).on('mouseout', function(){
            $(this).parent().children('li.star').each(function(e){
                $(this).removeClass('hover');
            });
        });

        $('#stars li').on('click', function(){
            var onStar = parseInt($(this).data('value'), 10); // The star currently selected
            var stars = $(this).parent().children('li.star');
            
            for (i = 0; i < stars.length; i++) {
              $(stars[i]).removeClass('selected');
            }
            
            for (i = 0; i < onStar; i++) {
              $(stars[i]).addClass('selected');
            }
            openOrderReviewByOid($this->order_id);
        });
    }
JS
            , CClientScript::POS_END);
    }

    public function getPaymentMethod()
    {
        if ($this->order_pm) {
            $criteria = new CDbCriteria();
            $criteria->select = array('pm_display_name', 'is_refundable');
            $criteria->condition = 'pm_id = :pm_id AND site_id = :site_id';
            $criteria->params = array(":pm_id" => $this->order_pm, ":site_id" => 0);
            return PipwavePaymentMapperBase::model()->find($criteria);
        }

        return false;
    }

}
