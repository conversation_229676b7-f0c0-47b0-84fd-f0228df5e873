<?php

class OrderCancelForm extends CFormModel
{

    const CHANGE_BY = 'OGM system';

    public function getRefundMethod($order, $is_refundable_payment)
    {
        $optionList = array(
            0 => Yii::t('myogm', 'TEXT_PLEASE_SELECT'),
            1 => Yii::t('myogm', 'TITLE_STORE_CREDIT'),
        );

        if (isset($order['detail']['payment_method']) && $is_refundable_payment) {
            if (!isset($order['payment']['ot_gv'])) {
                $optionList[2] = $order['detail']['payment_method'];
            } else {
                $optionList[3] = Yii::t('myogm', 'TITLE_STORE_CREDIT') . " and " . $order['detail']['payment_method'];
            }
        }

        return $optionList;
    }

    public function getOrderCancelReason()
    {
        return array(
            0 => Yii::t('myogm', 'TEXT_PLEASE_SELECT'),
            1 => Yii::t('myogm', 'TEXT_I_MAKE_MISTAKE_ORDER'),
            // 2 => Yii::t('myogm', 'TEXT_I_DO_NOT_WISH_TO_WAIT'),
            3 => Yii::t('myogm', 'TEXT_VERIFY_PROCESS_TOO_LONG'),
            4 => Yii::t('myogm', 'TEXT_DELIVERY_IS_TAKING_TOO_LONG'),
            5 => Yii::t('myogm', 'TEXT_I_CHANGE_MY_MIND'),
        );
    }

    public function getOrderComplainReason()
    {
        return array(
            0 => Yii::t('myogm', 'TEXT_PLEASE_SELECT'),
            1 => Yii::t('myogm', 'TEXT_COMPLAINT_CODE_ISSUE'),
            2 => Yii::t('myogm', 'TEXT_COMPLAINT_DTU_ISSUE'),
            3 => Yii::t('myogm', 'TEXT_COMPLAINT_OTHERS')
        );
    }

    public function getCodeIssueReason()
    {
        // TODO add in translations
        return array(
            0 => Yii::t('myogm', 'TEXT_PLEASE_SELECT'),
            1 => 'Invalid Code', // Yii::t('myogm', 'TEXT_INVALID_CODE'),
            2 => 'The code has been used', // Yii::t('myogm', 'TEXT_CODE_HAS_BEEN_USED'),
            3 => 'The code is not active', // Yii::t('myogm', 'TEXT_CODE_IS_NOT_ACTIVE'),
            4 => 'Others', // Yii::t('myogm', 'TEXT_OTHERS')
        );
    }

    public function cancelOrder($ogmForm, $cancelFeedback, $reason, $refundMethod, $issue_type, $order_attachments = [])
    {
        $status = 0;
        $message = '';
        $instantCancel = false;

        if (in_array($ogmForm->order_status, array(1, 3, 7))) {
            if ($ogmForm->order_status == 1) {
                // pending order instant cancel
                $instantCancel = $this->processCancelOrder($ogmForm, 5);
                if (!$instantCancel['status']) {
                    $status = 0;
                    $message = $instantCancel['message'];
                }
            } else {
                if (!$reason) {
                    $status = 0;
                    $message = ($ogmForm->order_status == 3 ? Yii::t('myogm', 'ERROR_MSG_PLEASE_SELECT_COMPLAINT_REASON') : Yii::t('myogm', 'ERROR_MSG_PLEASE_SELECT_CANCEL_REASON'));
                    return array('status' => $status, 'message' => $message);
                } else {
                    if (empty($cancelFeedback)) {
                        $status = 0;
                        $message = ($ogmForm->order_status == 3 ? Yii::t('myogm', 'ERROR_MSG_PLEASE_ENTER_COMPLAINT_FEEDBACK') : Yii::t('myogm', 'ERROR_MSG_PLEASE_ENTER_CANCEL_FEEDBACK'));
                        return array('status' => $status, 'message' => $message);
                    } else {
                        // Log user message into Order Remark History
                        $m_os_attr = array(
                            'orders_id' => $ogmForm->order_id,
                            'orders_status_id' => 0,
                            'date_added' => new CDbExpression('NOW()'),
                            'customer_notified' => 1,
                            'comments' => "<b>Refund Method : </b><br />" . $refundMethod . "<br /><br /><b>Reason : </b><br />" . $reason . "<br /><br /><b>Comment :</b><br />" . $cancelFeedback,
                            'comments_type' => 1,
                            'changed_by' => $ogmForm->customer_email
                        );
                        $this->updateOrdersStatusCounter($m_os_attr);
                        OrdersStatusHistoryBase::model()->saveNewRecord($m_os_attr);
                        unset($m_os_attr);

                        // CET help cancel
                        OrdersCancelRequestBase::model()->saveNewRecord(array('orders_id' => $ogmForm->order_id));
                    }
                }
            }
            # notify crew
            $email_template = $issue_type == 1 ? 'order-code-cancel-issue' : 'order-cancel-issue';
            $receiver_email = in_array($issue_type, [1, 2]) ? Yii::app()->params['CANCEL_ORDER_CODE_STORE_OWNER_EMAIL_ADDRESS'] : Yii::app()->params['CANCEL_ORDER_STORE_OWNER_EMAIL_ADDRESS'];
            $products_id = 0;
            $products_name = '';
            if (isset($ogmForm['order_products'])) {
                $products_id = implode(', ', array_column($ogmForm['order_products'], 'id'));
                $products_name = implode(', ', array_column($ogmForm['order_products'], 'name'));
            }

            // set basic info for email sending
            $emailDataSqs = array(
                'filetype' => 'order_email',
                'email_template' => $email_template,
                'email_subject_prefix' => '',
                'store_owner' => ConfigurationBase::model()->getConfigValue('STORE_OWNER'),
                'store_owner_email_address' => Yii::app()->params['STORE_OWNER_EMAIL_ADDRESS'],
                'customer' => array(
                    'id' => $ogmForm->customer_id,
                    'firstname' => ConfigurationBase::model()->getConfigValue('STORE_OWNER'),
                    'language' => 'en',
                    'email' => $receiver_email,
                ),
                'orders' => array(
                    'products_id' => $products_id,
                    'products_name' => $products_name,
                    'orders_id' => $ogmForm->order_id,
                    'comments' => $cancelFeedback,
                    'orders_total' => $ogmForm->order_total, // in text e.g RM10.00
                    'customers_request' => 'cancel',
                    'payment_method' => implode(', ', $ogmForm->payment_method_list),
                    'reason' => $reason,
                    'refund_method' => $refundMethod,
                    'attachments' => $order_attachments
                ),
            );

            $msorder = new MsOrderModel();
            // Process cancellation for Order in Verifying status
            if ($ogmForm->order_status == 7) {
                // Sent to CET
                $msorder->pushMailQueue($emailDataSqs);

                // Set email content for customer cancellation request
                $emailDataSqs['email_template'] = 'order-cancel-customer';
                $emailDataSqs['customer']['firstname'] = $ogmForm->customer_name;
                $emailDataSqs['customer']['language'] = Yii::app()->language;
                $emailDataSqs['customer']['email'] = $ogmForm->customer_email;

                # notify customer
                $msorder->pushMailQueue($emailDataSqs);

                $message = Yii::t('myogm', 'TEXT_CANCELLATION_REQUEST_SUBMITTED');
                $status = 1;
            } elseif ($ogmForm->order_status == 3) { // from complete (order has issue)
                $emailDataSqs['orders']['customers_request'] = 'issue';
                // Sent to CET
                $msorder->pushMailQueue($emailDataSqs);

                $message = Yii::t('myogm', 'TEXT_CANCELLATION_REQUEST_SUBMITTED');
                $status = 1;
            }

            if ($ogmForm->order_status == 1 && $instantCancel['status']) {
                // Set email content for customer instant cancelation
                $emailDataSqs['email_template'] = 'order-cancel-customer';
                $emailDataSqs['customer']['firstname'] = $ogmForm->customer_name;
                $emailDataSqs['customer']['language'] = Yii::app()->language;
                $emailDataSqs['customer']['email'] = $ogmForm->customer_email;
                $emailDataSqs['orders']['customers_request'] = 'instant';

                # notify customer
                $msorder->pushMailQueue($emailDataSqs);

                $message = Yii::t('myogm', 'TEXT_ORDER_HAS_BEEN_CANCELLED');
                $status = 1;
            }
        }
        return array('status' => $status, 'message' => $message);
    }

    private function processCancelOrder($ogmForm, $to_status)
    {
        $scResult = true;
        // update order status
        $m_attr = array(
            'orders_status' => $to_status,
            'last_modified' => new CDbExpression('NOW()')
        );
        if ($ogmForm['aft_executed'] != '-1') {
            $m_attr['orders_aft_executed'] = 0;
        }
        OrdersBase::model()->updateByPk($ogmForm->order_id, $m_attr);
        unset($m_attr);

        // cancel order
        if ($to_status == 5) {
            // release coupon
            $m_crt = CouponRedeemTrackBase::model()->findByAttributes(array('customer_id' => $ogmForm->customer_id, 'order_id' => $ogmForm->order_id));
            if (isset($m_crt->unique_id)) {
                $m_crt->coupon_id = 0;
                $m_crt->save();
            }
        }

        // refund sc if any used
        $sc_used_array = $this->getScUsedWithCurrency($ogmForm);
        if (count($sc_used_array) && $ogmForm->amount_refunded == 0) {
            $curCom = Yii::app()->currency;
            $ScCom = new StoreCreditCom();
            $scStatus = StoreCreditCom::storeCreditBalance();
            if ($scStatus['status']) {
                foreach ($sc_used_array as $key => $credit_amount_array) {
                    //retrieve customer store credit currency
                    $to_currency = $ScCom->accountCurrency();
                    $to_cur_type_code = isset($to_currency['result']['cur_code']) ? $to_currency['result']['cur_code'] : '';
                    $to_cur_type_id = isset($to_currency['result']['cur_id']) ? $to_currency['result']['cur_id'] : '';
                    if (isset($credit_amount_array['amount']) && $credit_amount_array['amount'] > 0) {
                        if ($credit_amount_array['currency_code'] != $to_cur_type_code) {
                            $round = true;
                        } else {
                            $round = false;
                        }
                        $to_amount = $curCom->advanceCurrencyConversion($credit_amount_array['amount'], $credit_amount_array['currency_code'], $to_cur_type_code, $round, 'sell');

                        // Check discrepancy if any and send notification to A&B
                        $usd_delivery_amount = $curCom->advanceCurrencyConversion($credit_amount_array['amount'], $credit_amount_array['currency_code'], 'USD', true, 'sell');
                        // Get current SC balance
                        $sc_current_balance = $ScCom->getCurrentCreditsBalance(Yii::app()->user->id);
                        $usd_sc_amount = $curCom->advanceCurrencyConversion($sc_current_balance['sc_balance'], $to_cur_type_code, 'USD', true, 'sell');

                        // Prepare to add SC amount back to user account
                        $trans_array = array(
                            'orders_id' => $ogmForm->order_id,
                            'reference_id' => $ogmForm->order_id,
                            'requesting_id' => $ogmForm->customer_email,
                            'requesting_role' => 'customer',
                            'amount' => $to_amount,
                            'total_amount' => $to_amount,
                            'currency_id' => $to_cur_type_id,
                            'activity' => 'X',
                            'show_customer' => 0
                        );
                        // Add Store Credit to customer account
                        $scResult = $ScCom->scMiscellaneousAddAmount($trans_array, 'Customer Order ' . $ogmForm->order_id . ' Cancellation (by ' . $ogmForm->customer_email . ')', ' Order Instant Cancel');

                        if ($scResult) {
                            // Discrepancy checking mechanism cont.
                            // Get currenct NRSC balance after the addition
                            $sc_current_balance = $ScCom->getCurrentCreditsBalance(Yii::app()->user->id);
                            $usd_sc_new_amount = $curCom->advanceCurrencyConversion($sc_current_balance['sc_balance'], $to_cur_type_code, 'USD', true, 'sell');
                            // Notification for discrepancy checking
                            if (($usd_sc_new_amount - $usd_sc_amount - $usd_delivery_amount) > 5) {
                                $classEmail = new FrontendMailCom();
                                // Email subject
                                $emailSubject = sprintf(Yii::t('myogm', 'EMAIL_DISCREPANCY_SUBJECT'), $ogmForm->order_id);
                                $emailSubject = FrontendMailCom::mbConvertEncoding(FrontendMailCom::mbConvertEncoding(sprintf($emailSubject, $ogmForm->order_id), Yii::t('myogm', 'EMAIL_CHARSET'), Yii::t('myogm', 'CHARSET')), Yii::t('myogm', 'CHARSET'), Yii::t('myogm', 'EMAIL_CHARSET'));
                                // Email sub body
                                $emailTextOgm = sprintf(Yii::t('myogm', 'EMAIL_DISCREPANCY_SUB_CONTENT'), $usd_sc_new_amount, $usd_sc_amount, 'SC', $usd_delivery_amount);
                                // Email main body
                                $emailTextOgm = sprintf(Yii::t('myogm', 'EMAIL_DISCREPANCY_CONTENT'), $ogmForm->order_id, date('Y-m-d H:i:s'), $ogmForm->customer_email, getenv("REMOTE_ADDR"), $emailTextOgm);
                                // Get Email array from OG Crew configuration
                                $emailArray = FrontendMailCom::parseEmailString(ConfigurationBase::model()->getConfigValue('NRSC_DISCREPANCY_CHECKING_EMAIL'));
                                if (!empty($emailArray)) {
                                    for ($i = 0, $cnt = count($emailArray); $cnt > $i; $i++) {
                                        $classEmail->send($emailArray[$i]['name'], $emailArray[$i]['email'], $emailSubject, $emailTextOgm, ConfigurationBase::model()->getConfigValue('STORE_OWNER'), ConfigurationBase::model()->getConfigValue('STORE_OWNER_EMAIL_ADDRESS'));
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                $scResult = false;
            }
        }

        if ($scResult) {
            // order status log
            $comments = null;

            if (!empty($ogmForm->sc_amount)) {
                $comments = Yii::t('myogm', 'TEXT_ORDER_MESSAGE_INSTANT_CANCEL_REFUNDED', array('{REFUNDED_AMOUNT}' => $ogmForm->sc_amount));
            }
            if (is_null($comments)) {
                $comments = Yii::t('myogm', 'TEXT_ORDER_MESSAGE_INSTANT_CANCEL');
            }

            $m_os_attr = array(
                'orders_id' => $ogmForm->order_id,
                'orders_status_id' => $to_status,
                'date_added' => new CDbExpression('NOW()'),
                'customer_notified' => 1,
                'comments' => $comments,
                'changed_by' => $ogmForm['customer_email']
            );
            // update orderStatusCounter
            $this->updateOrdersStatusCounter($m_os_attr);
            OrdersStatusHistoryBase::model()->saveNewRecord($m_os_attr);
            unset($m_os_attr);

            return array('status' => true, 'message' => '');
        } else {
            // update order status
            $m_attr = array(
                'orders_status' => 1,
                'last_modified' => new CDbExpression('NOW()')
            );
            OrdersBase::model()->updateByPk($ogmForm->order_id, $m_attr);
            unset($m_attr);

            return array('status' => false, 'message' => Yii::t('storeCredit', 'ERROR_STORE_CREDIT_CONTACT_ADMIN_IC'));
        }
    }

    private function updateOrdersStatusCounter($data, $increment = 1)
    {
        if (!isset($data['changed_by'])) {
            $data['changed_by'] = self::CHANGE_BY;
        }

        if ($data['orders_id'] && ($data['orders_status_id'] > 0)) {
            $m_oss = OrdersStatusStatBase::model()->findByPk(array(
                'orders_id' => $data['orders_id'],
                'orders_status_id' => $data['orders_status_id']
            ));
            if (isset($m_oss->orders_id)) {
                $m_oss->occurrence = $m_oss->occurrence + (int)$increment;
                $m_oss->latest_date = new CDbExpression('NOW()');
                $m_oss->changed_by = $data['changed_by'];
                $m_oss->save();
            } else {
                $m_attr = array(
                    'orders_id' => $data['orders_id'],
                    'orders_status_id' => $data['orders_status_id'],
                    'occurrence' => $increment,
                    'first_date' => new CDbExpression('NOW()'),
                    'latest_date' => new CDbExpression('NOW()'),
                    'changed_by' => $data['changed_by']
                );
                OrdersStatusStatBase::model()->saveNewRecord($m_attr);
            }
        }
    }

    public function getOrdersProductsTitle($orders_products)
    {
        $productName = [];
        foreach ($orders_products as $orderProducts) {
            $productName[] = $orderProducts['name'];
        }
        return implode(",", $productName);
    }

    private function getScUsedWithCurrency($ogmForm)
    {
        $sc_used_info = array();
        $curCode = '';

        $orders = OrdersTotalBase::model()->findByAttributes(array(
            'orders_id' => $ogmForm->order_id,
            'class' => 'ot_gv'
        ));

        if ($orders) {
            // get all transaction related to this order
            $statementArray = StoreCreditCom::getAllStoreCreditStatement(array(
                'orders_id' => $ogmForm->order_id,
                'activity' => 'P'
            ));

            foreach ($statementArray as $sc_history_info) {
                $sc_used_info[] = array(
                    'amount' => $sc_history_info['transaction_amount'],
                    'currency_code' => $sc_history_info['transaction_currency']
                );
            }
        }
        return $sc_used_info;
    }
}
