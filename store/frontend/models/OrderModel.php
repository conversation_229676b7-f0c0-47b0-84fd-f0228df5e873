<?php

class OrderModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_ORDER_CONFIG';
        $this->controller = 'mobile-recharge';
        $this->method = 'get';
        parent::__construct();
    }

    public function getMobileRechargeHistory()
    {
        $this->action = 'get-recharge-history';
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'mobile-recharge/get-recharge-history/' . Yii::app()->user->id;
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request(['customer_id' => Yii::app()->user->id]);
            Yii::app()->cache->set($cache_key, $return_arr, 3600); // cache 30 minutes
        }

        return $return_arr;
    }

    public function hideMobileRechargeHistory($prefix, $phone)
    {
        $prefix = (int)$prefix;
        $phone = (int)$phone;
        $this->action = 'hide-recharge-history';
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'mobile-recharge/get-recharge-history/' . Yii::app()->user->id;
        $this->request(['prefix' => $prefix, 'phone_no' => $phone, 'customer_id' => Yii::app()->user->id]);
        Yii::app()->cache->delete($cache_key);
    }


}
