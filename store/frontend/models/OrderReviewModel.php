<?php

class OrderReviewModel extends BaseAPIModel
{

    public function __construct()
    {
        $this->s_key = 'MS_PRODUCT_CONFIG';
        $this->controller = 'order-review';
        $this->method = 'get';
        parent::__construct();
    }

    public function getLatestOrdersReviewByOpid($orders_products_id)
    {
        $this->action = 'get-latest-orders-review-by-opid';

        $return_arr = false;

        if ($return_arr === false) {
            $return_arr = $this->request(['orders_products_id' => $orders_products_id]);
            if (!isset($return_arr['error_message'])) {
                null;
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve orders review', $attachments, 'DEFAULT');
            }
        }
        return $return_arr;
    }

    public function getReviewRemarksByRid($review_id)
    {
        $this->action = 'get-review-remarks-by-rid';

        $return_arr = false;

        if ($return_arr === false) {
            $return_arr = $this->request(['review_id' => $review_id]);
            if (!isset($return_arr['error_message'])) {
                null;
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve orders review', $attachments, 'DEFAULT');
            }
        }
        return $return_arr;
    }

    public function saveOrdersReview($params)
    {
        $this->method = 'post';
        $this->action = 'save-orders-review';

        $return_arr = $this->request(['params' => $params]);
        if (!isset($return_arr['error_message'])) {
            null;
        } else {
            $attachments = array(
                array(
                    'color' => 'danger',
                    'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                )
            );
            Yii::app()->slack->send('Failed to save orders review', $attachments, 'DEFAULT');
        }

    }

    public function getOrderReviewStatusByOid($orders_id)
    {
        $this->action = 'get-order-review-status-by-oid';

        $return_arr = $this->request(['orders_id' => $orders_id]);
        if (!isset($return_arr['error_message'])) {
            null;
        } else {
            $attachments = array(
                array(
                    'color' => 'danger',
                    'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                )
            );
            Yii::app()->slack->send('Failed to retrieve orders review', $attachments, 'DEFAULT');
        }

        return $return_arr;
    }

    public function getOrderReviewEditFlagByOpid($orders_products_id)
    {
        $this->action = 'get-order-review-edit-flag-by-opid';

        $return_arr = $this->request(['orders_products_id' => $orders_products_id]);
        if (!isset($return_arr['error_message'])) {
            null;
        } else {
            $attachments = array(
                array(
                    'color' => 'danger',
                    'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                )
            );
            Yii::app()->slack->send('Failed to retrieve orders review', $attachments, 'DEFAULT');
        }

        return $return_arr;
    }

    public function getReviewsByProductsId($products_id, $page = 1, $limit = 6, $sort = '0')
    {
        $this->action = 'get-reviews-by-products-id';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'order-reviews/get-review-pid/' . $products_id.'/'.$page.'/'.$limit.'/'.$sort;
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request([
                'products_id' => $products_id,
                'page' => $page,
                'limit' => $limit,
                'sort' => $sort,
            ]);
            if (!isset($return_arr['error_message'])) {
                Yii::app()->cache->set($cache_key, $return_arr, 1800); // cache 30 minutes
                null;
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve product reviews', $attachments, 'DEFAULT');
            }
        }

        return $return_arr;
    }

    public function getReviewsByCategoriesId($categories_id, $page = 1, $limit = 6, $sort = '0')
    {
        $this->action = 'get-reviews-by-categories-id';

        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'order-reviews/get-review-cid/' . $categories_id.'/'.$page.'/'.$limit.'/'.$sort;
        $return_arr = Yii::app()->cache->get($cache_key);

        if ($return_arr === false) {
            $return_arr = $this->request([
                'categories_id' => $categories_id,
                'page' => $page,
                'limit' => $limit,
                'sort' => $sort,
            ]);
            if (!isset($return_arr['error_message'])) {
                Yii::app()->cache->set($cache_key, $return_arr, 1800); // cache 30 minutes
                null;
            } else {
                $attachments = array(
                    array(
                        'color' => 'danger',
                        'text' => "App: og-frontend \n Response: " . json_encode($return_arr)
                    )
                );
                Yii::app()->slack->send('Failed to retrieve product reviews', $attachments, 'DEFAULT');
            }
        }

        return $return_arr;
    }

}
