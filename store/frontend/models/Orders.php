<?php

/**
 * This is the model class for table "orders".
 *
 * The followings are the available columns in table 'orders':
 * @property integer $orders_id
 * @property integer $customers_id
 * @property string $customers_name
 * @property string $customers_company
 * @property string $customers_street_address
 * @property string $customers_suburb
 * @property string $customers_city
 * @property string $customers_postcode
 * @property string $customers_state
 * @property string $customers_country
 * @property string $customers_telephone_country
 * @property string $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property string $customers_email_address
 * @property integer $customers_address_format_id
 * @property integer $customers_groups_id
 * @property string $delivery_name
 * @property string $delivery_company
 * @property string $delivery_street_address
 * @property string $delivery_suburb
 * @property string $delivery_city
 * @property string $delivery_postcode
 * @property string $delivery_state
 * @property string $delivery_country
 * @property integer $delivery_address_format_id
 * @property string $billing_name
 * @property string $billing_company
 * @property string $billing_street_address
 * @property string $billing_suburb
 * @property string $billing_city
 * @property string $billing_postcode
 * @property string $billing_state
 * @property string $billing_country
 * @property integer $billing_address_format_id
 * @property string $payment_method
 * @property integer $payment_methods_parent_id
 * @property integer $payment_methods_id
 * @property string $cc_type
 * @property string $cc_owner
 * @property string $cc_number
 * @property string $cc_expires
 * @property string $last_modified
 * @property string $date_purchased
 * @property integer $orders_status
 * @property integer $orders_cb_status
 * @property string $orders_date_finished
 * @property string $currency
 * @property string $remote_addr
 * @property string $currency_value
 * @property integer $paypal_ipn_id
 * @property string $pm_2CO_cc_owner_firstname
 * @property string $pm_2CO_cc_owner_lastname
 * @property integer $orders_locked_by
 * @property string $orders_locked_from_ip
 * @property string $orders_locked_datetime
 * @property string $orders_follow_up_datetime
 * @property string $orders_tag_ids
 * @property integer $orders_read_mode
 * @property integer $orders_aft_executed
 * @property integer $orders_rebated
 */
class Orders extends OrdersBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Orders the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function checkOrderExistBetweenDate($ordersId, $dateFrom, $dateTo) {
        $criteria = new CDbCriteria();
        $criteria->select = 'orders_id';
        $criteria->addCondition(array('orders_id = :ordersId', 'last_modified >= :dateFrom', 'last_modified <= :dateTo'));
        $criteria->params = array(':ordersId' => $ordersId, ':dateFrom' => $dateFrom, ':dateTo' => $dateTo);
        $result = $this->model()->find($criteria);
        return isset($result->orders_id) ? true : false;
    }

}
