<?php

/**
 * This is the model class for table "orders_review".
 *
 * The followings are the available columns in table 'orders_review':
 * @property integer $id
 * @property integer $orders_products_id
 * @property integer $custom_products_code_id
 * @property integer $status
 * @property string $data_encrypted
 * @property datetime $created_at
 * @property datetime $updated_at
 */
class OrdersCdkeys extends CActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_cdkeys';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('orders_products_id, custom_products_code_id, status', 'numerical', 'integerOnly' => true),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'orders_products_id' => 'Orders Products Id',
            'custom_products_code_id' => 'Custom Products Code Id',
            'status' => 'Status',
            'data_encrypted' => 'Data Encrypted',
            'created_at' => 'Created at',
            'updated_at' => 'Updated at',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function getOrdersCdkeys($cpId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = array('custom_products_code_id', 'data_encrypted', 'status');
        $criteria->condition = 'custom_products_code_id = :cpId';
        $criteria->params = array(':cpId' => $cpId);

        $result = $this->model()->find($criteria);
        return isset($result) ? $result : null;
    }


    public function getAllCdkeysStatus($opId)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'custom_products_code_id';
        $criteria->condition = 'orders_products_id = :opId and status = 0';
        $criteria->params = array(':opId' => $opId);
        $result = $this->model()->findAll($criteria);
        $count = count($result);
        //return isset($result->custom_products_code_id) ? $result->custom_products_code_id : null;
        if ($count == 0 || empty($result)) { //no status = 0, done
            return true;
        } else {
            return false;
        }
    }

    public function isExist($cpc_id)
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'custom_products_code_id';
        $criteria->condition = 'custom_products_code_id = :cpcId';
        $criteria->params = array(':cpcId' => $cpc_id);
        $result = $this->model()->findAll($criteria);
        $count = count($result);
        if ($count == 0 || empty($result)) { //no status = 0, done
            return false;
        } else {
            return true;
        }
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return OrdersReview the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }


}
