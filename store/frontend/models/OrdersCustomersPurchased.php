<?php

/**
 * This is the model class for table "orders".
 *
 * The followings are the available columns in table '':
 * @property string $customers_id
 * @property string $purchased_status
 */
class OrdersCustomersPurchased extends Orders
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersCustomersPurchased the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'OrdersProductsBase' => array(self::HAS_MANY, 'OrdersProductsBase', 'orders_id'),
            'OrdersExtraInfoBase' => array(self::HAS_MANY, 'OrdersExtraInfoBase', 'orders_id'),
        );
    }

    public function customerOrderHistoryQueryBuilder($select, $customerId, $limit = '', $interval, $order_status = 'all')
    {
        $sql = "SELECT " . $select . " FROM " . $this->tableName() . " o 
        LEFT JOIN " . OrdersTotalBase::model()->tableName() . " AS ot
            ON ot.class = 'ot_subtotal'
                AND ot.orders_id = o.orders_id
        LEFT JOIN " . OrdersExtraInfoBase::model()->tableName() . " AS oei
            ON oei.orders_id = o.orders_id
                AND oei.orders_extra_info_key = 'site_id'
        LEFT JOIN orders_status os
            ON o.orders_status = os.orders_status_id
                AND os.language_id  = 1
        WHERE o.customers_id = :customerId
            AND o.date_purchased > (NOW() - $interval)
            AND (oei.orders_extra_info_value = 0 OR oei.orders_extra_info_value IS NULL)";
        if ($order_status == 'all') {
            $sql .= " AND o.orders_status <> 5 ";
        } else {
            $sql .= " AND o.orders_status = " . $order_status;
        }
        $sql .= " ORDER BY o.date_purchased DESC" . $limit;
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customerId", $customerId, PDO::PARAM_INT);

        return $command;
    }

    public function getCustomersOrderHistory($customerId, $limit, $interval, $getCount = false, $offset = 0, $order_status = 'all')
    {
        $return_array = [];
        $item_array = [];

        $sql = self::customerOrderHistoryQueryBuilder('o.orders_id, o.date_purchased, o.orders_status, os.orders_status_name, ot.text', $customerId, " LIMIT $limit OFFSET $offset", $interval, $order_status);

        if ($results = $sql->queryAll()) {
            foreach ($results as $result) {
                $order_line['formatted_date'] = $result['date_purchased'];
                $order_line['orders_id'] = $result['orders_id'];
                $order_line['orders_status'] = $result['orders_status_name'];
                $order_line['formatted_amount'] = $result['text'];
                $item_array[] = $order_line;
            }
        }
        if ($getCount) {
            $pagination = array(
                'display_start' => (count($results) > 0 ? $offset + 1 : 0),
                'display_end' => $offset + count($results),
            );
            $count_sql = self::customerOrderHistoryQueryBuilder('COUNT(o.orders_id) as total', $customerId, '', $interval, $order_status);
            if ($results = $count_sql->queryRow()) {
                $pagination['total'] = $results['total'];
                $pagination['total_pages'] = ceil($pagination['total'] / $limit);
                $pagination['page'] = ($offset / $limit) + 1;
            }
            $return_array['data'] = $item_array;
            $return_array['pagination'] = $pagination;
            return $return_array;
        } else {
            return $item_array;
        }
    }

    public function getCustomerPurchased($customerId, $limit = 8)
    {
        $sql = "SELECT op.products_id FROM " . $this->tableName() . " o 
                LEFT JOIN " . OrdersProductsBase::model()->tableName() . " op ON o.orders_id = op.orders_id 
                LEFT JOIN " . OrdersExtraInfoBase::model()->tableName() . " AS oei
                    ON oei.orders_id = o.orders_id
                    AND oei.orders_extra_info_key = 'site_id'
                INNER JOIN " . Products::model()->tableName() . " p ON op.products_id = p.products_id AND (p.products_type != 3 OR p.products_type IS NULL)
                WHERE o.customers_id = :customerId
                AND o.date_purchased > (NOW() - INTERVAL 6 MONTH)
                AND op.custom_products_type_id = 2
                AND op.parent_orders_products_id = 0
                AND (oei.orders_extra_info_value = 0 OR oei.orders_extra_info_value IS NULL)
                GROUP BY op.products_id
                ORDER BY MAX(o.date_purchased) DESC
                LIMIT $limit";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customerId", $customerId, PDO::PARAM_INT);
        $return_array = [];
        if ($results = $command->queryAll()) {
            foreach ($results as $result) {
                $return_array[] = $result['products_id'];
            }
        }
        return $return_array;
    }

    public function getOrderDetail($customerId, $orderId)
    {
        $sql = "SELECT o.orders_id, o.orders_status, os.orders_status_name, o.payment_methods_id, o.currency, o.currency_value, o.date_purchased, o.customers_id, o.customers_name, o.customers_email_address, o.payment_method, o.orders_tag_ids, o.payment_methods_parent_id, o.orders_aft_executed, ot.title, ot.text, ot.value, ot.class FROM " . $this->tableName() . " o 
        LEFT JOIN " . OrdersTotalBase::model()->tableName() . " AS ot
            ON ot.orders_id = o.orders_id
        LEFT JOIN " . OrdersExtraInfoBase::model()->tableName() . " AS oei
            ON oei.orders_id = o.orders_id
            AND oei.orders_extra_info_key = 'site_id'
        LEFT JOIN orders_status os
            ON o.orders_status = os.orders_status_id
                AND os.language_id  = 1
        WHERE o.customers_id = :customerId
            AND o.orders_id = :orders_id
            AND (oei.orders_extra_info_value = 0 OR oei.orders_extra_info_value IS null)
            AND o.date_purchased > (NOW() - INTERVAL 24 MONTH)";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customerId", $customerId, PDO::PARAM_INT);
        $command->bindParam(":orders_id", $orderId, PDO::PARAM_INT);
        $results = $command->queryAll();
        return $results;
    }

    public function getOrderHeader($customerId, $orderId)
    {
        $sql = "SELECT o.orders_id, o.orders_status, o.payment_methods_id, o.currency, o.currency_value, o.date_purchased, o.customers_id, o.customers_name, o.customers_email_address, o.customers_street_address, o.customers_suburb, o.customers_city, o.customers_state, o.customers_postcode, o.customers_country_international_dialing_code, o.customers_telephone, o.payment_method, o.orders_tag_ids, o.payment_methods_parent_id, o.orders_aft_executed FROM " . $this->tableName() . " o 
        LEFT JOIN " . OrdersTotalBase::model()->tableName() . " AS ot
            ON ot.orders_id = o.orders_id
        LEFT JOIN " . OrdersExtraInfoBase::model()->tableName() . " AS oei
            ON oei.orders_id = o.orders_id
            AND oei.orders_extra_info_key = 'site_id'
        WHERE o.customers_id = :customerId
            AND o.orders_id = :orders_id
            AND (oei.orders_extra_info_value = 0 OR oei.orders_extra_info_value IS null)";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customerId", $customerId, PDO::PARAM_INT);
        $command->bindParam(":orders_id", $orderId, PDO::PARAM_INT);
        $results = $command->queryRow();
        return $results;
    }

    public function getOrderTotal($orderId)
    {
        $sql = "SELECT ot.title, ot.text, ot.value, ot.class FROM " . OrdersTotalBase::model()->tableName() . " AS ot WHERE ot.orders_id = :orders_id";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":orders_id", $orderId, PDO::PARAM_INT);

        $results = $command->queryAll();
        return $results;
    }

    public function getPaymentMethodTypeId($payment_method_id = null)
    {
        $return_int = 0;
        $pm_id = $payment_method_id != null ? $payment_method_id : (isset($this->info['payment_methods_id']) ? $this->info['payment_methods_id'] : 0);

        if ($pm_id) {
            if ($result = PaymentMethodsBase::model()->getPaymentMethodTypeId($pm_id)) {
                $return_int = $result;
            }
        }

        return $return_int;
    }
}