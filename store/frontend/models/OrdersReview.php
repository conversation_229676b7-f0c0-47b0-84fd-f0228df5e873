<?php

/**
 * This is the model class for table "orders_review".
 *
 * The followings are the available columns in table 'orders_review':
 * @property string $id
 * @property integer $customers_id
 * @property integer $orders_id
 * @property string $created_at
 */
class OrdersReview extends CActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'orders_review';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('created_at', 'required'),
            array('customers_id, orders_id', 'numerical', 'integerOnly' => true),
            array('created_at', 'length', 'max' => 20),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, customers_id, orders_id, created_at', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'customers_id' => 'Customers',
            'orders_id' => 'Orders',
            'created_at' => 'Created At',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('customers_id', $this->customers_id);
        $criteria->compare('orders_id', $this->orders_id);
        $criteria->compare('created_at', $this->created_at, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return OrdersReview the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public static function isReviewed($orders_id)
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'orders_reviews/' . $orders_id;
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result !== false) {
            $result = $cache_result;
        } else {
            $record = self::model()->find('orders_id = ' . $orders_id);
            $result = ($record ? 1 : 0);
            Yii::app()->cache->set($cache_key, $result, 86400);
        }
        return $result;
    }

    public function afterSave()
    {
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'orders_reviews/' . $this->orders_id;
        Yii::app()->cache->delete($cache_key);
        return parent::afterSave();
    }

}
