<?php

/**
 * This is the model class for table "orders_tax_configuration".
 *
 * The followings are the available columns in table 'orders_tax_configuration':
 * @property string $orders_tax_id
 * @property string $country_code
 * @property string $currency
 * @property string $orders_tax_percentage
 * @property string $orders_tax_status
 */
class OrdersTaxConfiguration extends OrdersTaxConfigurationBase
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersTaxConfigurationBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return CMap::mergeArray(
        	parent::relations(),
        	array()
        );
    }

    public function getGSTTaxInfo($country_code, $language_id)
    {
        $return_array = array();

        $result = $this->with(array('otcd1' => array(
        	'condition' => 'language_id = :language_id',
        	'params' => array(':language_id' => $language_id),
        )))->findByAttributes(array('country_code' => $country_code), array('together' => true));

        if ($result && $result->otcd1[0]) {
            $return_array = array_merge($result->getAttributes(null), $result->otcd1[0]->getAttributes(null));
        }

        return $return_array;
    }
}
