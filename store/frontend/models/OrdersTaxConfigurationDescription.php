<?php

/**
 * This is the model class for table "orders_tax_configuration_description".
 *
 * The followings are the available columns in table 'orders_tax_configuration_description':
 * @property string $orders_tax_id
 * @property string $language_id
 * @property string $orders_tax_title
 */
class OrdersTaxConfigurationDescription extends OrdersTaxConfigurationDescriptionBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersTaxConfigurationDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}