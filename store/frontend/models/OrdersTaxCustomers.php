<?php

/**
 * This is the model class for table "orders_tax_configuration".
 *
 * The followings are the available columns in table 'orders_tax_configuration':
 * @property string $orders_tax_id
 * @property string $country_code
 * @property string $currency
 * @property string $orders_tax_percentage
 * @property string $orders_tax_status
 */
class OrdersTaxCustomers extends OrdersTaxCustomersBase
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrdersTaxConfigurationBase the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}
