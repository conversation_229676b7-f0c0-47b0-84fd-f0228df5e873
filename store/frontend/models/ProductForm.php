<?php

class ProductForm extends CFormModel
{
    const TEMPLATE_ID_TYPE = 1;
    const TEMPLATE_PARENT_ID_TYPE = 0;

    public $product_content_obj, $page_content_obj, $product_array, $tag_info = array();

    public $game_id, $product_id, $delivery_method, $promo_type, $tag_id, $item_per_page, $total_available_items, $page, $offset, $game_product_array, $product_reviews, $error_soft_block = false;

    public function __construct($request_array)
    {
        $this->tag_id = isset($request_array['tid']) ? (int)$request_array['tid'] : 0;
        $this->product_id = isset($request_array['pid']) ? (int)$request_array['pid'] : 0;
        $this->delivery_method = isset($request_array['dm']) ? (int)$request_array['dm'] : 5;
        $this->promo_type = isset($request_array['pm']) ? (int)$request_array['pm'] : 0;

        try {
            $this->game_id = Yii::app()->frontPageCom->getProductObj()->getGameIDByID($this->product_id);
            if ($this->game_id && Yii::app()->frontPageCom->getCategoryObj()->isCategoryIDActive($this->game_id)) {
                // Check for hardblock
                if (Yii::app()->searchBoxCom->filterHardBlocking(array($this->game_id))) {
                    // Check for soft block
                    if (Yii::app()->searchBoxCom->filterSoftBlocking(array($this->game_id))) {
                        $this->error_soft_block = true;
                    }
                    /**
                     * @var ProductCom $product_com
                     */
                    $product_com = Yii::app()->frontPageCom->getProductObj();
                    if ($products_array = $product_com->getProductsListByID($this->product_id)) {
                        foreach ($products_array as $product_info) {
                            if ($product_info['default_dm'] == $this->delivery_method && $product_info['promo_type'] == $this->promo_type) {
                                $this->product_array = $product_info;
                                break;
                            }
                        }
                    }
                    $this->page_content_obj = new FrontendPageContentCom();
                    $this->page_content_obj->_init($this->game_id, self::TEMPLATE_PARENT_ID_TYPE);
                    if ($this->product_array) {
                        if ($this->product_array['products_type'] == 2) {
                            $this->tag_info[] = array(
                                'label' => Yii::t('page_content', 'MEGA_MENU_GAME_KEY'),
                                'link' => array('game-key/index'),
                            );
                            $this->tag_info[] = $this->product_array['name'];

                            $game_product_detail = (new GameProductModel())->getGameProductsInfo($this->product_id);
                            $this->game_product_array = $game_product_detail;

                        } else {
                            Yii::app()->frontPageCom->getPageContentObj()->_init($this->game_id, 0);
                            Yii::app()->frontPageCom->getPageContentObj()->getAllInfo();
                            $this->product_content_obj = new FrontendPageContentCom();
                            $this->product_content_obj->_init($this->product_id, self::TEMPLATE_ID_TYPE);

                            $this->tag_info[] = array(
                                'label' => Yii::app()->frontPageCom->getCategoryObj()->getName($this->game_id),
                                'link' => array('category/index', 'cid' => $this->game_id),
                            );
                            $this->tag_info[] = $this->product_array['name'];
                        }

                        // register recent viewed product
                        Yii::app()->frontPageCom->registerRecentViewedProduct($this->product_id . ":" . $this->delivery_method . ":" . $this->promo_type);
                    } else {
                        $url = Yii::app()->createUrl('category/index',
                            ['cid' => $this->game_id]
                        );
                        Yii::app()->request->redirect($url);
                    }
                }
            } else {
                throw new Exception('ERROR_PRODUCT_GAME_ID_NOT_FOUND');
            }
        } catch (Exception $e) {
            // hav to log the category that has no product reason here
            throw new CHttpException(404, $e->getMessage());
        }
    }

    private function drawCounterBox($ttl_seconds, $status_key)
    {
        $count_index = 'p_' . rand();
        $return_string = '';
        $hr = $mn = $sc = 0;

        if ($ttl_seconds > 0) {
            $ttls = $ttl_seconds;

            $dy = floor(($ttls / 86400));
            if ($dy > 0) {
                $ttls = $ttls - ($dy * 86400);
                if ($dy > 99) {
                    $dy = 99;
                }
            }

            $hr = floor(($ttls / 3600));
            if ($hr > 0) {
                $ttls = $ttls - ($hr * 3600);
            }

            $mn = floor(($ttls / 60));
            if ($mn > 0) {
                $sc = $ttls - ($mn * 60);
            }

            $dy = str_pad($dy, 2, "0", STR_PAD_LEFT);
            $hr = str_pad($hr, 2, "0", STR_PAD_LEFT);
            $mn = str_pad($mn, 2, "0", STR_PAD_LEFT);
            $sc = str_pad($sc, 2, "0", STR_PAD_LEFT);

            $return_string = '<div id="cb_' . $count_index . '" class="counterBox">
								<input id="cbd_' . $count_index . '" type="hidden" value="' . $ttl_seconds . '">
								<div class="counterBox_left"><!-- --></div>
								<div class="counterBox_middle">
								  <div class="md"><span class="digit">' . $dy . '</span><span class="lbl">' . Yii::t('page_content', 'COUNTER_DAY') . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="mh"><span class="digit">' . $hr . '</span><span class="lbl">' . Yii::t('page_content', 'COUNTER_HOURS') . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="mm"><span class="digit">' . $mn . '</span><span class="lbl">' . Yii::t('page_content', 'COUNTER_MINUTES') . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="ms"><span class="digit">' . $sc . '</span><span class="lbl">' . Yii::t('page_content', 'COUNTER_SECONDS') . '</span></div>
							  	</div>
							 	<div class="counterBox_right"><!-- --></div>
							  </div>';

            if ($status_key) {
                $return_string .= '<div style="font-size: 11px">
                                    <div style="float:left;">' . Yii::t('page_content', 'PROMOTION_STATUS') . ':</div><div style="float:left;padding-left:5px;color:#FF0000;">' . Yii::t('page_content', 'COUNTER_' . $status_key) . '!</div><div style="clear:both;"></div>
                                   </div>';
            }
        }

        Yii::app()->clientScript->registerScript('counter', 'setInterval("countdown([\'' . $count_index . '\'])", 1000);', CClientScript::POS_END);

        return $return_string;
    }

    public function getAFNotice()
    {
        $cookie_key = 'notice_alert_cookie';
        $return_int = $this->page_content_obj->getGameValue('af_notice_enable_status', '0');

        if (isset(Yii::app()->request->cookies[$cookie_key])) {
            $val_array = explode(',', htmlspecialchars(Yii::app()->request->cookies[$cookie_key]->value));
            $return_int = in_array('c' . $this->game_id, $val_array) ? '0' : $return_int;
        }

        return $return_int;
    }

    public function getBackgroundImage()
    {
        return Yii::app()->frontPageCom->getPageContentObj()->getGameValue('background_source', '');
    }

    public function getBreadcrumbs()
    {
        $return_array = array();
        if ($tag_array = $this->getTagInfo()) {

            $current_title = array_pop($tag_array);
            $new_arr = [];
            if ($this->product_array['products_type'] != 2) {
                $return_array[Yii::t('page_content', 'MEGA_MENU_GAME_CARD')] = array('search/index', 'keyword' => Yii::t('meta_tag', 'TEXT_ALL_PRODUCTS_TITLE'));
            }

            $category_link = end($tag_array);

            $return_array[$category_link['label']] = $category_link['link'];

            $return_array[] = $current_title;

        }
        return $return_array;
    }

    private function get_delivery_method_label($delivery_methods_array, $delimiter = ', ')
    {
        $delivery_mode_array = array();

        foreach ($delivery_methods_array as $delivery_methods_id_loop) {
            switch ($delivery_methods_id_loop) {
                case '5':
                    $delivery_mode_array[] = Yii::t('page_content', 'DELIVERY_INFO_SEND_TO_ACCOUNT');
                    break;
                case '6':
                    $delivery_mode_array[] = Yii::t('page_content', 'DELIVERY_INFO_DIRECT_TOP_UP');
                    break;
                case '7':
                    $delivery_mode_array[] = Yii::t('page_content', 'DELIVERY_INFO_IN_STORE_PICKUP');
                    break;
                default:
                    break;
            }
        }

        return implode($delimiter, $delivery_mode_array);
    }

    public function getDescription()
    {
        if ($this->product_array['products_type'] == 2) {
            $return_str = $this->getGameData($this->game_product_array, 'description');
        } else {
            if (!$return_str = $this->product_content_obj->getGameDetailInfo('description', '')) {
                $return_str = $this->page_content_obj->getGameDetailInfo('description', '');
            }
        }

        return str_replace("\n", "<br>", $return_str);
    }


    public function getBreadcrumbsWidgetContent($link = '')
    {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return array(
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
            'htmlOptions' => array('class' => 'breadcrumbs  breadcrumbs--decor'),
        );
    }

    public function getGameInfoList()
    {
        $return_array = array();
        $game_info_array = $this->page_content_obj->getGameInfo();

        foreach ($game_info_array as $key => $game_array) {
            $return_array[] = array(
                'label' => Yii::t('page_content', 'GAME_INFO_' . strtoupper($key)),
                'value' => is_array($game_array) ? implode(", ", $game_array) : $game_array,
            );
        }

        return $return_array;
    }

    public function getLogoImageInfo()
    {
        $return_array = array();

        if ($url = $this->page_content_obj->getGameValue('logo_source', '')) {
            $return_array = array(
                'attributes_info' => array(
                    'src' => $url,
                ),
            );
        }

        return $return_array;
    }

    public function getNotice()
    {
        $return_str = '';

        if (!$return_str = $this->product_content_obj->getGameDetailInfo('notice', '')) {
            $return_str = $this->page_content_obj->getGameDetailInfo('notice', '');
        }

        return $return_str;
    }

    public function getTitle()
    {
        $tagInfo = $this->getTagInfo();
        return end($tagInfo);
    }

    public function getRelatedLinkList()
    {
        $return_array = array();
        $game_info_array = $this->page_content_obj->getRelatedLink();

        foreach ($game_info_array as $game_array) {
            $return_array[] = array(
                'label' => $game_array['label'],
                'href' => $game_array['link'],
            );
        }

        return $return_array;
    }

    public function getResultSummary()
    {
        return '';
    }

    public function getSystemRequirement()
    {
        $return_str = '';

        if (!$return_str = $this->product_content_obj->getGameDetailInfo('system_requirements', '')) {
            $return_str = $this->page_content_obj->getGameDetailInfo('system_requirements', '');
        }

        return str_replace("\n", "<br>", $return_str);
    }

    public function getGallery()
    {
        $return_array = array();

        if ($gallery_json = $this->product_content_obj->getGameDetailInfo('gallery_info', '')) {
            if ($gallery_array = json_decode($gallery_json, true)) {
                foreach ($gallery_array as $gallery) {
                    if ($gallery['link'] && $gallery['source']) {
                        $return_array[] = array(
                            'href' => $gallery['link'],
                            'src' => $gallery['source'],
                        );
                    }
                }
            }
        }

        return $return_array;
    }

    private function getTagInfo()
    {
        return $this->tag_info;
    }

    public function getGameData($data, $key)
    {
        $lang_id = Yii::app()->frontPageCom->language_id;
        $default_lang = Yii::app()->frontPageCom->default_language_id;
        if (!empty($data[$lang_id][$key])) {
            return $data[$lang_id][$key];
        } elseif (!empty($data[$default_lang][$key])) {
            return $data[$default_lang][$key];
        } elseif (!empty($data[$key])) {
            return $data[$key];
        } else {
            return '';
        }
    }

    public function getItems()
    {
        $product = $this->product_array;
        if (!isset($product['products_last_modified']) || empty($product['products_last_modified'])) {
            $last_modified = $product['pid'];
        } else {
            $last_modified = strtotime($product['products_last_modified']);
        }
        $return_array = array(
            //                'desc' => $product['name'],
            'type' => $product['products_type'],
            'stock_status' => $product['stock_status'],
            'more_detail_info' => array(
                'dm' => array(
                    'label' => Yii::t('page_content', 'PRODUCT_PAGE_LABEL_DELIVERY_MODE'),
                    'value' => $this->get_delivery_method_label($product['delivery_method']),
                ),
                'dt' => array(
                    'label' => Yii::t('page_content', 'PRODUCT_PAGE_LABEL_DELIVERY_TIME'),
                    'value' => ($product['product_notice'] != '' ? $product['product_notice'] : 'Instant'),
                ),
            ),
            'purchase_info' => array(
                'price' => $product['price'],
                'original_price' => $product['original_price'],
                'op_icon' => $product['op'],
            ),
            'image_info' => array(
                'attributes_info' => array(
                    'title' => $product['image']['title'],
                    'alt' => $product['image']['alt'],
                    'class' => 'product__img',
                    'src' => (array_values(array_slice(explode("/", $product['image']['dimension'][0]), -1))[0] !== "no_product_image.png" ? $product['image']['dimension'][0] . "?v=" . $last_modified : $product['image']['dimension'][0]),
                ),
            ),
            'button_info' => array(
                'type' => $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                'label' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                'attributes_info' => array(
                    'id' => 'p_' . $product['pid'],
                ),
                'class' => 'btn-lg ',
            ),
            'extra_info' => array(
                'data-N' => array(
                    'data-id' => 'data_p_' . $product['pid'],
                    'data-name' => htmlspecialchars($product['name']),
                    'data-type' => 'buy',
                    'data-pid' => $product['pid'],
                    'data-dtu' => $product['is_dtu_flag'],
                    'data-bundle' => $product['products_bundle'],
                    'data-dm' => $product['default_dm'],
                    'data-pm' => $product['promo_type'],
                ),
                'bottom_left' => array(
                    'return_policy' => array(
                        'link' => Yii::app()->createUrl('site/page', array('view' => 'refund-policy')),
                        'label' => Yii::t('buttons', 'LINK_RETURN_POLICY'),
                        'attributes_info' => array(
                            'target' => '_blank',
                        ),
                    ),
                    'disclaimer' => array(
                        'link' => isset(Yii::app()->frontPageCom->DISCLAIMER_URL[Yii::app()->frontPageCom->language_id]) ? Yii::app()->frontPageCom->DISCLAIMER_URL[Yii::app()->frontPageCom->language_id] : 'javascript:void(0);',
                        'label' => Yii::t('buttons', 'LINK_DISCLAIMER'),
                        'attributes_info' => array(
                            'target' => isset(Yii::app()->frontPageCom->DISCLAIMER_URL[Yii::app()->frontPageCom->language_id]) ? '_blank' : '_self',
                        ),
                    ),
                ),
                'bottom_right' => array(),
            ),
        );

        return $return_array;
    }

    public function getProductReviews($page = 1, $limit = 6, $sort = '0')
    {
        $review_model = new OrderReviewModel();
        $products_review = array();
        if($this->product_id != '0'){
            $products_review = $review_model->getReviewsByProductsId($this->product_id, $page, $limit, $sort);
        }

        $this->product_reviews = $products_review;
    }

    public function getSupportedGame()
    {
        return Yii::app()->frontPageCom->getPageContentObj()->getCategorySupportedGames();
    }
}
