<?php

class ProductReviewForm extends CFormModel
{
    const TEMPLATE_ID_TYPE = 1;
    const TEMPLATE_PARENT_ID_TYPE = 0;

    public $product_content_obj,
        $page_content_obj,
        $product_array,
        $tag_info = array();

    public $game_id,
        $product_id, $categories_id,
        $delivery_method,
        $promo_type,
        $tag_id,
        $item_per_page,
        $total_available_items,
        $page, $offset, $game_product_array, $product_reviews, $type;

    public function __construct($request_array)
    {
        $this->tag_id = isset($request_array['tid']) ? (int)$request_array['tid'] : 0;
        $this->product_id = isset($request_array['pid']) ? (int)$request_array['pid'] : 0;
        $this->categories_id = isset($request_array['cid']) ? (int)$request_array['cid'] : 0;
        $this->delivery_method = isset($request_array['dm']) ? (int)$request_array['dm'] : 5;
        $this->promo_type = isset($request_array['pm']) ? (int)$request_array['pm'] : 0;
        $this->type = $request_array['type'];

        if ($this->type == 'products') {
            try {
                $this->game_id = Yii::app()->frontPageCom->getProductObj()->getGameIDByID($this->product_id);

                if ($this->game_id) {

                    if (Yii::app()->searchBoxCom->filter4Display(array($this->game_id))) {
                        if ($products_array = Yii::app()->frontPageCom->getProductObj()->getProductsListByID($this->product_id)) {
                            foreach ($products_array as $product_info) {
                                if ($product_info['default_dm'] == $this->delivery_method && $product_info['promo_type'] == $this->promo_type) {
                                    $this->product_array = $product_info;
                                    break;
                                }
                            }
                        }
                        $this->page_content_obj = new FrontendPageContentCom();
                        $this->page_content_obj->_init($this->game_id, self::TEMPLATE_PARENT_ID_TYPE);

                        if ($this->product_array) {
                            if ($this->product_array['products_type'] == 2) {

                                $this->tag_info[] = array(
                                    'label' => Yii::t('page_content', 'MEGA_MENU_GAME_KEY'),
                                    'link' => array('game-key/index')
                                );
                                $this->tag_info[] = $this->product_array['name'];

                                $game_product_detail = (new GameProductModel())->getGameProductsInfo($this->product_id);
                                $this->game_product_array = $game_product_detail;

                            } else {
                                Yii::app()->frontPageCom->getPageContentObj()->_init($this->game_id, 0);
                                Yii::app()->frontPageCom->getPageContentObj()->getAllInfo();
                                $this->product_content_obj = new FrontendPageContentCom();
                                $this->product_content_obj->_init($this->product_id, self::TEMPLATE_ID_TYPE);

                                $this->tag_info[] = array(
                                    'label' => $this->product_array['name'],
                                    'link' => array('product/index', 'pid' => $this->product_id)
                                );
                                $this->tag_info[] = Yii::t('page_content', 'TEXT_REVIEW');
                            }

                            // register recent viewed product
                            Yii::app()->frontPageCom->registerRecentViewedProduct($this->product_id . ":" . $this->delivery_method . ":" . $this->promo_type);
                        } else {
                            // product not found
                            throw new Exception('ERROR_PRODUCT_ID_NOT_FOUND');
                        }
                    }
                } else {
                    throw new Exception('ERROR_PRODUCT_GAME_ID_NOT_FOUND');
                }
            } catch (Exception $e) {
                // hav to log the category that has no product reason here
                throw new CHttpException(300, $e->getMessage());
            }
        } else {
            try {
                if ($this->product_array = Yii::app()->searchBoxCom->getProductListByGameID(array($this->categories_id))) {
                    if (!$this->tag_info = Yii::app()->cTagCom->getSinglePathByGameID($this->categories_id)) {
                        // tag does not found / inactived
                        throw new Exception('ERROR_CATEGORY_TAG_NOT_FOUND');
                    }

                    $this->tag_info[] = array(
                        'label' => Yii::app()->frontPageCom->getCategoryObj()->getName($this->categories_id),
                        'link' => array('category/index', 'cid' => $this->categories_id)
                    );
                    $this->tag_info[] = Yii::t('page_content', 'TEXT_REVIEW');

                    Yii::app()->frontPageCom->getPageContentObj()->getAllInfo();
                } else {
                    // Throw Region Restricted Error when no product on game, Requested by Leonard
                    // throw new Exception('ERROR_CATEGORY_PRODUCT_ID_NOT_FOUND');
                    throw new Exception('ERROR_REGION_RESTRICTION');
                }
            } catch (Exception $e) {
                // hav to log the category that has no product reason here
                throw new CHttpException(300, $e->getMessage());
            }
        }
    }

    public function getBreadcrumbs()
    {
        $return_array = array();
        if ($tag_array = $this->getTagInfo()) {

            $current_title = array_pop($tag_array);
            $new_arr = [];

            $category_link = end($tag_array);

            $return_array[$category_link['label']] = $category_link['link'];

            $return_array[] = $current_title;

        }
        return $return_array;
    }

    public function getDescription()
    {
        if ($this->type == 'products') {
            if ($this->product_array['products_type'] == 2) {
                $return_str = $this->getGameData($this->game_product_array, 'description');
            } else {
                if (!$return_str = $this->product_content_obj->getGameDetailInfo('description', '')) {
                    $return_str = $this->page_content_obj->getGameDetailInfo('description', '');
                }
            }

            return str_replace("\n", "<br>", $return_str);
        }else{
            return str_replace("\n", "<br>", Yii::app()->frontPageCom->getPageContentObj()->getGameDetailInfo('description', ''));
        }

    }


    public function getBreadcrumbsWidgetContent($link = '')
    {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return array(
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
            'htmlOptions' => array('class' => 'breadcrumbs  breadcrumbs--decor')
        );
    }

    public function getTitle()
    {
        if ($this->product_id != '0') {
            return $this->product_array['name'];
        } else {
            return Yii::app()->frontPageCom->getCategoryObj()->getName($this->categories_id);
        }
    }

    private function getTagInfo()
    {
        return $this->tag_info;
    }

    public function getProductReviews($page = 1, $limit = 6, $sort = '0', $type = 'products')
    {
        $review_model = new OrderReviewModel();

        if ($type == 'products') {
            $products_review = $review_model->getReviewsByProductsId($this->product_id, $page, $limit, $sort);
        } else {
            $products_review = $review_model->getReviewsByCategoriesId($this->categories_id, $page, $limit, $sort);
        }


        $this->product_reviews = $products_review;

    }
}
