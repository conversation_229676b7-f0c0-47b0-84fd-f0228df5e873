<?php

/**
 * This is the model class for table "products_checkout_setting".
 *
 * The followings are the available columns in table 'products_checkout_setting':
 * @property integer $products_id
 * @property integer $max_purchase_quantity
 * @property integer $max_purchase_period
 */
class ProductsCheckoutSetting extends ProductsCheckoutSettingBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsCheckoutSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getCheckoutQuantityLimit($pid){
	    return ProductsCheckoutSetting::model()->findByAttributes(array('products_id'=>$pid));
    }

    public static function validateCheckoutQuantity($pid, $qty){
        $checkout_quantity_control = self::getCheckoutQuantityLimit($pid);
        if($checkout_quantity_control){
            $min_qty = (!empty($checkout_quantity_control->min_checkout_quantity) ? $checkout_quantity_control->min_checkout_quantity : 1);
            $max_qty = (!empty($checkout_quantity_control->max_checkout_quantity) ? $checkout_quantity_control->max_checkout_quantity : 100);
            if($min_qty > $qty || $max_qty < $qty){
                return false;
            }
        }
        return true;
    }

}