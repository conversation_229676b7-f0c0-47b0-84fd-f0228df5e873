<?php

/**
 * This is the model class for table "products_delivery_mode".
 *
 * The followings are the available columns in table 'products_delivery_mode':
 * @property string $products_delivery_mode_id
 * @property string $custom_products_type
 * @property string $products_delivery_mode_title
 */
class ProductsDeliveryMode extends ProductsDeliveryModeBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsDeliveryModeBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}