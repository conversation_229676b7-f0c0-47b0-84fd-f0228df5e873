<?php

/**
 * This is the model class for table "products_description".
 *
 * The followings are the available columns in table 'products_description':
 * @property integer $products_id
 * @property integer $language_id
 * @property string $products_name
 * @property string $products_keyword
 * @property string $products_description
 * @property string $products_image
 * @property string $products_image_title
 * @property string $products_description_image
 * @property string $products_description_image_title
 * @property string $products_url
 * @property integer $products_viewed
 * @property string $products_location
 * @property string $products_dtu_extra_info_1
 * @property string $products_dtu_extra_info_2
 * @property string $products_dtu_extra_info_3
 */
class ProductsDescription extends ProductsDescriptionBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ProductsDescriptionBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}