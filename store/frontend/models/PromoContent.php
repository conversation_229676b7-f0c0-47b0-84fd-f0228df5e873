<?php

/**
 * This is the model class for table "promo_content".
 *
 * @property int $id
 * @property string $title
 * @property string $page_title
 * @property string $start_time
 * @property string $end_time
 * @property string $html
 * @property string $css
 * @property string $javascript
 * @property string $json_data
 * @property int $status
 * @property string $modified_by
 * @property string $created_at
 * @property string $updated_at
 */

class PromoContent extends MainModel
{
    public $data;
    public $sub;
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return AddressBookBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()

    {
        return Yii::app()->db_offgamers;
    }


    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'promo_content';
    }

    public static function getPromoContent()
    {
        $return_int = 0;

        $criteria = new CDbCriteria;
        $criteria->select = 'title,page_title,html,css,javascript,json_data,start_time,end_time';
        $criteria->condition = 'status = 1 AND title != "DEFAULT" AND start_time <= ' . time() . ' AND end_time >= ' . time();
        if ($result = self::model()->find($criteria)) {
            return $result;
        }

        return $return_int;
    }

    public static function getDefaultPromo()
    {
        $return_int = 0;

        $criteria = new CDbCriteria;
        $criteria->select = 'title,page_title,html,css,javascript,json_data,start_time,end_time';
        $criteria->condition = 'status = 1 AND title="DEFAULT"';
        if ($result = self::model()->find($criteria)) {
            return $result;
        }

        return $return_int;
    }

    public static function getNextPromoStartTime(){
        $criteria = new CDbCriteria;
        $criteria->select = 'start_time';
        $criteria->condition = 'status = 1 AND title != "DEFAULT" AND start_time > '.time();
        $criteria->order = 'start_time';
        if ($result = self::model()->find($criteria)) {
            return $result->start_time;
        }

        return time() + 604800;
    }

    public function processProductList(){
        $source = json_decode($this->json_data);
        $data = [];
        $sub = [];

        foreach ($source as $item) {
            if (empty($item->name)) {
                continue;
            }
            if (!empty($item->parent_id)) {
                $sub[$item->parent_id][] = $item;
            } else {
                $data[$item->category][] = $item;
            }
        }

        $this->data = json_encode($data);
        $this->sub = json_encode($sub);
    }

}