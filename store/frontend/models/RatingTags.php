<?php

/**
 * This is the model class for table "rating_tags". 
 * 
 * The followings are the available columns in table 'rating_tag': 
 * @property integer $tag_id
 * @property string $tag_name
 * @property string $tag_method
 * @property string $tag_trigger_content
 * @property integer $rule_id
 * @property string $created_date
 * @property string $last_modified_date
 */
class RatingTags extends RatingTagsBase {

    /**
     * Returns the static model of the specified AR class. 
     * @param string $className active record class name. 
     * @return RatingTag the static model class 
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getAllTags() {
        $tags_array = array();
//        $cacheKey = Yii::app()->params['MEMCACHE_PREFIX'] . $this->tableName() . '/array';
//        $cacheValue = Yii::app()->cache->get($cacheKey);
//        
//        if ($cacheValue === false) {
        $sql = "SELECT r.rule_operator,r.rule_type,r.rule_value,rrt.rule_id, rt.tag_id,rt.tag_sequence, rt.tag_method, rt.tag_trigger_content,rt.internal_index_sequence
                    FROM " . $this->tableName() . " AS rt
                    RIGHT JOIN " . RatingRulesTags::model()->tableName() . " AS rrt ON (rt.tag_id = rrt.tag_id)
                    LEFT JOIN " . RatingRules::model()->tableName() . " AS r ON (rrt.rule_id = r.rule_id)    
                    ORDER BY rt.tag_sequence ASC
                    ";

        $command = Yii::app()->db->createCommand($sql);

        $dataset = $command->queryAll();

        foreach ($dataset as $idx => $value) {
            $tags_array[$value['tag_id']][] = $value['rule_id'];
        }

//            Yii::app()->cache->set($cacheKey, $dataset, 900);
//        } else {
//            $dataset = $cacheValue;
//        }

        return array(
            $dataset,
            $tags_array
        );
    }

}
