<?php

class RedeemGiftCardForm extends CFormModel {
    
    public $rd_code;
    public $rd_serial;
    public $rd_pin;
    
    public function rules() {
        return array(
            array('rd_code, rd_serial, rd_pin', 'required', 'message' => Yii::t('page_content', 'ERROR_EMPTY_FIELD')),
            array('rd_code, rd_serial, rd_pin', 'length', 'max' => 255),
            array('rd_code','in','range' => !empty(Yii::app()->params['GIFT_CARD']['PRODUCT']) ? array_keys(Yii::app()->params['GIFT_CARD']['PRODUCT']) : array()),
        );
    }

    public function attributeLabels() {
        return array(
            'rd_code' => 'Redeem type',
            'rd_serial' => 'Serial num',
            'rd_pin' => 'Pin code',
        );
    }

}
