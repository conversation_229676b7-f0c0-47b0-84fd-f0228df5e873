<?php

/**
 * LoginForm class.
 * LoginForm is the data structure for keeping
 * user login form data. It is used by the 'login' action of 'SiteController'.
 */
class SSOLogin extends CFormModel {

	public $username;
	private $_identity;

	/**
	 * Declares the validation rules.
	 * The rules state that username and password are required,
	 * and password needs to be authenticated.
	 */
	public function rules() {
		return array(
			// username and password are required
			array('username', 'required'),
			array('username', 'numerical', 'integerOnly' => true),
			array('username', 'authenticate'),
		);
	}

	public function beforeValidate() {
		$purifier = new CHtmlPurifier();
		$this->username = $purifier->purify($this->username);
		return parent::beforeValidate();
	}

	/**
	 * Declares attribute labels.
	 */
	public function attributeLabels() {
		return array(
		);
	}

	/**
	 * Authenticates the password.
	 * This is the 'authenticate' validator as declared in rules().
	 */
	public function authenticate($attribute, $params) {
		if (!$this->hasErrors()) {
			$this->_identity = new SSOIdentity($this->username, $this->username);
			$this->_identity->authenticate();
			switch ($this->_identity->errorCode) {
				case SSOIdentity::ERROR_NONE:
					Yii::app()->user->login($this->_identity);
					break;
			}
		}
	}

	/**
	 * Logs in the user using the given username and password in the model.
	 * @return boolean whether login is successful
	 */
	public function login() {

		if ($this->username <= 0) {
			return false;
		}

		if ($this->_identity === null) {
			$this->_identity = new SSOIdentity($this->username, $this->username);
			$this->_identity->authenticate();
		}
//

		if ($this->_identity->errorCode === SSOIdentity::ERROR_NONE) {
			return true;
		}
//
		else
			return false;
	}

}
