<?php

/**
 * This is the model class for table "search_keywords".
 *
 * The followings are the available columns in table 'search_keywords':
 * @property string $tpl_id
 * @property string $id
 * @property integer $id_type
 * @property string $language_id
 * @property string $search_value
 */
class SearchKeywords extends SearchKeywordsBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return SearchKeywords the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getKeywordInfo($field, $id, $id_type = 0, $language_id = 0, $default_language_id = 0) {
        $return_str = '';

        $sql = "SELECT " . $field . " 
                    FROM " . $this->tableName() . " 
                    WHERE id = :id
                        AND id_type = :id_type
                        AND " . $field . " <> '' 
                        AND (IF (language_id = :language_id, 1, IF(( SELECT COUNT(id) > 0 
                            FROM " . $this->tableName() . " 
                            WHERE id = :id 
                                AND id_type = :id_type
                                AND language_id = :language_id
                                AND " . $field . " <> ''), 0, language_id = :default_languages_id)))";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":id", $id, PDO::PARAM_STR);
        $command->bindParam(":id_type", $id_type, PDO::PARAM_INT);
        $command->bindParam(":language_id", $language_id, PDO::PARAM_STR);
        $command->bindParam(":default_languages_id", $default_language_id, PDO::PARAM_STR);
        if ($value = $command->queryScalar()) {
            $return_str = $value;
        }

        return $return_str;
    }

}
