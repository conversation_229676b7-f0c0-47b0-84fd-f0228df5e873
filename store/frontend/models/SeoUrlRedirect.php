<?php

/**
 * This is the model class for table "seo_url_redirect".
 *
 * @property int $seo_url_redirect_id
 * @property string $old_url
 * @property string $new_url
 * @property int $redirect_type
 * @property int $created_at
 * @property int $updated_at
 */
class SeoUrlRedirect extends MainModel
{

    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public function getDbConnection()
    {
        return Yii::app()->db_offgamers;
    }

    public function tableName(): string
    {
        return 'seo_url_redirect';
    }

    public function getByOldUrl(string $url)
    {
        $url = str_replace(" ","+",$url);

        return self::model()->find('old_url=:oldUrl', [':oldUrl' => $url]);
    }
}