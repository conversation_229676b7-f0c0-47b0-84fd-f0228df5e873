<?php

/**
 * This is the model class for table "tax_rates".
 *
 * The followings are the available columns in table 'tax_rates':
 * @property integer $tax_rates_id
 * @property integer $tax_zone_id
 * @property integer $tax_class_id
 * @property integer $tax_priority
 * @property string $tax_rate
 * @property string $tax_description
 * @property string $last_modified
 * @property string $date_added
 */
class TaxRates extends TaxRatesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TaxRatesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}