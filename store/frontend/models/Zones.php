<?php

/**
 * This is the model class for table "zones".
 *
 * The followings are the available columns in table 'zones':
 * @property integer $zone_id
 * @property integer $zone_country_id
 * @property string $zone_code
 * @property string $zone_name
 */
class Zones extends ZonesBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Zones the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getStatesList($country_id)
    {
        $return_array = array();
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . '/' . $country_id . '/states_list';
        $cache_result = Yii::app()->cache->get($cache_key);
        if ($cache_result === false) {
            $result = [];
            $criteria = new CDbCriteria;
            $criteria->addSearchCondition("zone_country_id", $country_id);
            $zones = $this->model()->findAll($criteria);

            foreach ($zones as $zone) {
                $result[$zone->zone_name] = $zone->zone_name;
            }

            $cache_result = $result;
            Yii::app()->cache->set($cache_key, $cache_result, 86400);
        }

        $return_array = $cache_result;

        return $return_array;
    }
}
