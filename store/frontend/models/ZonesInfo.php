<?php

/**
 * This is the model class for table "zones_info".
 *
 * The followings are the available columns in table 'zones_info':
 * @property integer $geo_zone_id
 * @property string $geo_zone_info
 */
class ZonesInfo extends ZonesInfoBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ZonesInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}