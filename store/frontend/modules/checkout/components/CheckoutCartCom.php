<?php

class CheckoutCartCom extends MainCom
{
    private $customerSetting = array(), $cartID = 0, $cItem = array(), $cartFullInfo = array(), $product_obj;

    public function __construct($customerID = null)
    {
        parent::__construct();
        $this->setCustomerSetting('customer_id', $customerID);
        $customer_id = $this->getCustomerID();

        if (!is_null($customer_id)) {
            # API will not fall into this condition
            if ($cart_id = $this->getCartId($new_if_not_exist = false, $throw_error = false)) {
                $this->restoreCart($cart_id, $customer_id);
            }
        }
    }

    public function addCart($data_array, $add_cart_scenario = 'add_cart')
    {
        $return_bool = false;

        try {
            $this->getCartId($new_if_not_exist = true);
            $cartItem_obj = new CartItemForm($add_cart_scenario);
            $cartItem_obj->setAttributes($data_array);

            if ($cartItem_obj->validate()) {
                $itm_key = $cartItem_obj->getUniqueKey();
                $this->cItem[$itm_key] = $cartItem_obj;

                if ($this->checkoutPermission($itm_key)) {
                    $return_bool = $this->save2DB();
                }
            } else {
                if ($errors_array = $cartItem_obj->getErrors()) {
                    foreach ($errors_array as $key => $error_array) {
                        throw new Exception($error_array[0]);
                    }
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return $return_bool;
    }

    public function checkoutPermission($itm_key = '')
    {
        if ($this->isCartEmpty() !== true) {
            $pg_payment_ip = $this->getCustomerSetting('pg_payment_ip');
            $country_iso2 = Yii::app()->customerCom->getCountryByIP($pg_payment_ip);

            foreach ($this->cItem as $ikey => $_item_cart_obj) {
                # verify product in Region Game purchase list
                if ($this->getProductObj()->checkProductRegionPermission($_item_cart_obj->products_id, Yii::app()->session['country_code'], $country_iso2)) {
                    $products_status = $this->getProductObj()->getProductPurchaseStatus($_item_cart_obj->products_id);

                    # verify listing status, invalid when -1 inactive
                    if ($products_status !== 'ACTIVE') {
                        throw new Exception('ERROR_PRODUCT_IS_NOT_AVAILABLE__3');
                    }

                    # verify zero price product
                    if ($this->getProductObj()->validateZeroPriceProduct($_item_cart_obj->products_id) !== true) {
                        throw new Exception(Yii::t('checkoutModule.checkout', 'ERROR_PRODUCT_IS_NOT_AVAILABLE', array('{SUPPORT_LINK}' => Yii::app()->frontPageCom->getPageSupportLink())));
                    }

                    $cPath_array = $this->getProductObj()->getCPathByID($_item_cart_obj->products_id);

                    # verify category permission
                    if (CheckoutCategories::model()->isInactivedGame($cPath_array)) {
                        throw new Exception('ERROR_PRODUCT_IS_NOT_AVAILABLE__4');
                    }

                    # verify customer group permission
                    if (CheckoutCategoriesGroups::model()->isIllegalCustomerGroupGame($cPath_array, $this->getCustomerGroupID())) {
                        throw new Exception('ERROR_PRODUCT_IS_NOT_AVAILABLE__5');
                    }

                    # ONLY perform check when customer has session
                    if (isset(Yii::app()->params['IP_RESTRICTION_CATEGORY']) && !empty(Yii::app()->params['IP_RESTRICTION_CATEGORY'])) {
                        if ($cat_path = $this->getProductObj()->getCPathByID($_item_cart_obj->products_id)) {
                            $second_layer_category_id = isset($cat_path[1]) ? $cat_path[1] : 0;

                            if ($second_layer_category_id) {
                                $region_locked_category_id_array = Yii::app()->params['IP_RESTRICTION_CATEGORY'];

                                foreach ($region_locked_category_id_array as $block_type => $block_list) {
                                    if ($block_type == 'WHITELIST') {
                                        foreach ($block_list as $allow_country => $category_id_array) {
                                            if (in_array($second_layer_category_id, $category_id_array)) {
                                                if ($allow_country !== $country_iso2) {
                                                    throw new Exception('ERROR_REGION_RESTRICTION__1');
                                                    //$this->reportTrace($inputData, $country_iso2, $pg_payment_ip, 'WHITELIST');
                                                }
                                            }
                                        }
                                    } else {
                                        foreach ($block_list as $disallow_country => $category_id_array) {
                                            if (in_array($second_layer_category_id, $category_id_array)) {
                                                if ($disallow_country === $country_iso2) {
                                                    throw new Exception('ERROR_REGION_RESTRICTION__2');
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    throw new Exception('ERROR_PRODUCT_IS_NOT_AVAILABLE__6');
                }
            }
        }

        return $itm_key !== '' ? isset($this->cItem[$itm_key]) : (count($this->cItem) > 0);
    }

    public function cleanCartID()
    {
        #key:shopping_cart/clean_cart_id
        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . 'shopping_cart/clean_cart_id';
        $cache_result = Yii::app()->cache->get($cache_key);

        if ($cache_result === false) {
            OgmCustomersBasket::model()->cleanCartID();

            Yii::app()->cache->set($cache_key, 1, 3600); // cache 1 hour
        }
    }

    private function compileOTRequiredInfo($cart_product, $coupon_info_array, $currency)
    {
        $current_ip = $this->getCustomerSetting('ip');
        $current_ip_country = '';

        if ($current_ip_country_info = Yii::app()->geoip->getIPCountryInfo($current_ip)) {
            $current_ip_country = $current_ip_country_info['countries_iso_code_2'];
        }

        return array(
            'ot' => array(
                'products' => $cart_product['product'],
                'customer' => array(
                    'id' => $this->getCustomerID(),
                    'group_id' => $this->getCustomerGroupID(),
                    'country' => $this->getCountryID(),
                    'language' => $this->getLanguageID(),
                    'language_code' => $this->getCustomerSetting('language'),
                    'default_language' => $this->getCustomerSetting('default_language_id'),
                ),
                'checkout_info' => array(
                    'ip' => $current_ip,
                    'currency' => $currency,
                    'default_currency' => $this->getCustomerSetting('default_currency'),
                    'store_currency' => $this->getCustomerSetting('store_currency', $currency),
                    'coupon_code' => $coupon_info_array['code'],
                    'currency_value' => $cart_product['currency_value'],
                    'subtotal' => $cart_product['subtotal'],
                    'surcharge' => $this->getCustomerSetting('pg_surcharge', 0),
                    'tax_info' => '',
                    'payment_ip' => $this->getCustomerSetting('pg_payment_ip', $current_ip),
                    'payment_ip_country' => $this->getCustomerSetting('pg_payment_ip_country', $current_ip_country),
                    'tax_exempted_items' => $cart_product['tax_exempted_items'],
                ),
                'payment_info' => array(
                    'payment_method_parent_id' => $this->getCustomerSetting('payment_method_parent_id', ''),
                    'payment_method_id' => $this->getCustomerSetting('payment_method_id', ''),
                    'title' => $this->getCustomerSetting('payment_method_title', ''),
                    'paypal_ipn_id' => $this->getCustomerSetting('paypal_ipn_id', ''),
                ),
            ),
            'extra_info' => array(
                'return_url' => $this->getReturnURL(),
                'user_password_authorized' => AuthCom::checkIsLifetimeCookiesExisted(),
                'access_code' => $this->getCustomerSetting('access_code', ''),
                'pg_surcharge' => $this->getCustomerSetting('pg_surcharge', 0),
                'pg_tax_info' => $this->getCustomerSetting('pg_tax_info', ''),
                'pg_payment_ip' => $this->getCustomerSetting('pg_payment_ip', ''),
            ),
        );
    }

    private function generateCartId($length = 5)
    {
        $valid = 0;
        $limit_try = 5;

        do {
            $limit_try--;
            $cartId = createRandomValue($length, 'digits');
            $valid = OgmCustomersBasket::model()->validCartId($cartId);
        } while ($valid && $limit_try);

        return $cartId;
    }

    public function getAllErrorMessage()
    {
        $return_array = array();

        try {
            $cartID = $this->getCartId();
            $customer_id = $this->getCustomerID();
            $error_info_array = self::getExtraInfoFromDB('error', $cartID, 'cart_id', $customer_id);

            foreach ($error_info_array as $idx => $error_info) {
                $return_array[] = self::getErrorMessage($error_info);
            }
        } catch (Exception $e) {
            $return_array[] = $e->getMessage();
        }

        return $return_array;
    }

    public function getAllProducts($currency = '', $payment_method_id = 0)
    {
        $cart_product = array();

        try {
            $cartID = $this->getCartId();

            if ($customer_id = $this->getCustomerID()) {
                $_m_data = OgmCustomersBasket::model()->getCartInfoByCartId($cartID, $customer_id);

                if (!empty($_m_data)) {
                    Yii::app()->currency->setCustomerID($customer_id);

                    $product_obj = new CheckoutProductCom();

                    $cart_product = array(
                        'subtotal' => 0,
                        'optotal' => 0,
                        'ex_optotal' => 0,
                        'currency_value' => Yii::app()->currency->getValue($currency, 'sell'),
                        'product' => array(),
                        'tax_exempted_items' => [],    // store tax exempted product id
                    );

                    // Cart product array
                    foreach ($_m_data as $_data_obj) {
                        Yii::app()->currency->reset();

                        $cItem = new CartItemForm('restore_item');
                        $cItem->setSerializedData($_data_obj->customers_basket_custom_value);

                        if (!empty($cItem->sub_products_id)) {
                            $_REQUEST['sub_products_id'] = $cItem->sub_products_id;
                        }

                        $products_id = $cItem->products_id;

                        $_REQUEST['checkout_pid'] = $products_id;

                        $products_package = array();
                        $_item_info = $product_obj->getProductBundleInfoByID($products_id);

                        if (in_array($products_id, ProductsBase::model()->getTaxExemptedProductsList())) {
                            $cart_product['tax_exempted_items'][] = $products_id;
                        }

                        if ($_item_info) {
                            $product_custom_content_array = $cItem->getProductCustomContent();

                            $_actual_item_info = $_item_info['actual'];
                            $custom_product_type_info = array();

                            # let the currency obj to decide
                            $product_price_array = $product_obj->getProductPricesInfoByID($products_id, $_actual_item_info);

                            # Get Product Price Setting - convert the store credit product price based on the customer store credit currency
                            if ($_actual_item_info['custom_products_type_id'] == 3) {
                                # store credit base currency alwasy same with checkout currency
                                $product_base_currency = $currency;
                                //                                $store_credit_currency_code = CheckoutStoreCreditCom::get_customer_store_credit_currency_code($customer_id, $currency);
                                $store_credit_currency_code = isset($product_custom_content_array['orders_sc_currency_code']) ? $product_custom_content_array['orders_sc_currency_code'] : CheckoutStoreCreditCom::get_customer_store_credit_currency_code(
                                    $customer_id,
                                    $currency
                                );

                                # product price
                                $price = $_actual_item_info['products_price'];
                                # before discount
                                $normal_price = $price;

                                if ($store_credit_currency_code != $currency) {
                                    $price = Yii::app()->currency->advanceCurrencyConversion($price, $store_credit_currency_code, $currency, false, 'buy');
                                }

                                # after discount
                                $product_price_array['price'] = $final_price = $price;
                            } else {
                                # product base currency
                                $product_base_currency = $_actual_item_info['products_base_currency'];

                                # product price
                                $price = Yii::app()->currency->getProductNoDiscountPrice(
                                    $_actual_item_info['products_price'],
                                    $product_base_currency,
                                    $currency,
                                    $product_price_array['defined_price']
                                );
                                # before discount
                                $normal_price = $price;
                                # after discount
                                $final_price = Yii::app()->currency->getProductPrice($products_id, $product_price_array, $product_base_currency, $currency);
                            }

                            $storage_price = $price / Yii::app()->currency->getValue($currency, 'sell');
                            $storage_normal_price = $storage_price;
                            $storage_final_price = $final_price / Yii::app()->currency->getValue($currency, 'sell');

                            # retrieve parent cateogry id for `products_categories_id`
                            $category_path = $product_obj->getCPathByID($products_id);
                            $_pci = current($category_path);

                            # rebate
                            # storage price ( USD )
                            Yii::app()->currency->rebatePoint($products_id, $product_price_array, $product_base_currency, $currency, $cItem->qty, $payment_method_id, $customer_id);

                            if ($_actual_item_info['products_bundle'] == 'yes' || $_actual_item_info['products_bundle_dynamic'] == 'yes') {
                                foreach ($_item_info['bundle'] as $bundle_item_info) {
                                    $subproduct_price_array = $product_obj->getProductCurrencyPriceInfoByID($bundle_item_info['products_id']);
                                    $subproduct_normal_price = Yii::app()->currency->getProductNoDiscountPrice(
                                        $bundle_item_info['products_price'],
                                        $bundle_item_info['products_base_currency'],
                                        $currency,
                                        $subproduct_price_array
                                    );

                                    $products_package[$bundle_item_info['products_id']] = array(
                                        'qty' => $bundle_item_info['subproduct_qty'],
                                        'name' => $product_obj->getProductsName($bundle_item_info['products_id'], $this->getLanguageID(), $this->getCustomerSetting('default_language_id')),
                                        'model' => $bundle_item_info['products_model'],
                                        'normal_price' => $subproduct_normal_price,
                                        'normal_store_price' => $subproduct_normal_price / Yii::app()->currency->getValue($currency, 'sell'),
                                        'delivery_info' => array(
                                            'delivery_mode' => '5', // this is not in used yet
                                        ),
                                    );

                                    if (!isset($custom_product_type_info['custom_products_type_id'])) {
                                        $custom_product_type_info['custom_products_type_id'] = $bundle_item_info['custom_products_type_id'];
                                        $custom_product_type_info['custom_products_type_child_id'] = $bundle_item_info['custom_products_type_child_id'];
                                    }
                                }
                            } else {
                                $custom_product_type_info['custom_products_type_id'] = $_actual_item_info['custom_products_type_id'];
                                $custom_product_type_info['custom_products_type_child_id'] = $_actual_item_info['custom_products_type_child_id'];
                            }

                            $cart_product['product'][] = array(
                                'id' => $products_id,
                                'name' => $product_obj->getAltProductsName($products_id, $this->getLanguageID(), $this->getCustomerSetting('default_language_id')),
                                'price' => $price,
                                'base_currency' => $_actual_item_info['products_base_currency'],
                                'quantity' => $cItem->qty,
                                'final_price' => $final_price,
                                'normal_price' => $normal_price,
                                'products_categories_id' => $_pci,
                                'products_package' => $products_package,
                                'cat_id_path' => implode('_', $category_path),
                                'discounts' => $this->getProductDiscount($products_id, $customer_id),
                                'custom_products_type_id' => $custom_product_type_info['custom_products_type_id'],
                                'custom_products_type_child_id' => $custom_product_type_info['custom_products_type_child_id'],
                                'custom_content' => $product_custom_content_array,
                                'model' => $_actual_item_info['products_model'],
                                'sub_products_id' => $cItem->sub_products_id,
                                'products_type' => $_actual_item_info['products_type'],
                                'storage_price' => array(
                                    'normal_price' => $storage_normal_price,
                                    'price' => $storage_price,
                                    'final_price' => $storage_final_price,
                                ),
                                'op_info' => array(
                                    'rebate_point' => Yii::app()->currency->rebate_point,
                                    'rebate_point_extra' => Yii::app()->currency->rebate_point_extra,
                                    'rebate_point_formula' => Yii::app()->currency->rebate_point_formula,
                                ),
                            );

                            # price in USD
                            $cart_product['subtotal'] += $cItem->qty * $final_price;
                            $cart_product['optotal'] += Yii::app()->currency->rebate_point;
                            $cart_product['ex_optotal'] += Yii::app()->currency->rebate_point_extra;
                        }

                        unset($cItem);
                    }
                } else {
                    # capture the issue
                    $debug_data = array();

                    $_m_data2 = OgmCustomersBasket::model()->getCartInfoByCartId($cartID);
                    if (!empty($_m_data2)) {
                        foreach ($_m_data2 as $_data_obj) {
                            $debug_data[] = $_data_obj->getAttributes();
                        }
                    }

                    $this->resetCart();
                    $this->reportError('[Method] getAllProducts', array('customer_id' => $customer_id, 'cartID' => $cartID, 'without_cid' => $debug_data), '', 'DEV_DEBUG_CHECKOUT');
                    throw new Exception('ERROR_GENERAL__E15');
                }
            } else {
                throw new Exception('ERROR_GENERAL__E16');
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return $cart_product;
    }

    public function getCartFullDetails()
    {
        $order_total_required_array = array();
        $process_result = array();
        $collect_result = array();
        $extra_info = array();

        if ($this->cartFullInfo === array()) {
            try {
                $currency = $this->getCustomerSetting('currency', $this->getCustomerSetting('default_currency'));
                $cart_product = $this->getAllProducts($currency, $this->getCustomerSetting('payment_method_id', 0));

                if ($cart_product !== array()) {
                    $extra_info = array(
                        'optotal' => $cart_product['optotal'],
                        'ex_optotal' => $cart_product['ex_optotal'],
                    );
                    $coupon_info_array = $this->getCoupon();
                    $order_total_required_array = $this->compileOTRequiredInfo($cart_product, $coupon_info_array, $currency);

                    // ==================================== call ORDER module ====================================
                    Yii::app()->getModule('order');
                    $ot_obj = new OrderTotalCom($order_total_required_array['ot']);

                    if ($coupon_info_array['has_checked'] !== true) {
                        $this->setCoupon($coupon_info_array['code'], $has_checked = true);
                        $collect_result = $ot_obj->collectPosts();
                    }

                    $pre_process = $ot_obj->process();

                    $tax_info = $this->getTaxAmount($order_total_required_array['ot'], $pre_process);

                    if (!empty($tax_info)) {
                        $tax_amount = 0;
                        $products_tax_info = [];

                        foreach ($tax_info['tax_info'] as $product) {
                            $products_tax_info[$product['products_id']] = $product;
                            $tax_amount = bcadd($tax_amount, $product['tax_amount'], 8);
                        }

                        $order_total_required_array['ot']['checkout_info']['products_tax_info'] = $products_tax_info;
                        $order_total_required_array['ot']['checkout_info']['tax_info'] = array(
                            'title' => $tax_info['tax_title_short'] . ' (' . round($tax_info['tax_percentage']) . '%)',
                            'amount' => round($tax_amount, 2),
                            'rate' => round($tax_info['tax_percentage']),
                            'country' => $tax_info['tax_country'],
                        );
                    }

                    $ot_obj = new OrderTotalCom($order_total_required_array['ot']);

                    if ($coupon_info_array['has_checked'] !== true) {
                        $this->setCoupon($coupon_info_array['code'], $has_checked = true);
                        $collect_result = $ot_obj->collectPosts();
                    }

                    $process_result = $ot_obj->process();

                    if (isset($process_result['error'])) {
                        throw new Exception($process_result['error']);
                    }

                    unset($ot_obj);
                    // ==================================== call ORDER module ====================================
                }

                $this->cartFullInfo = array(
                    'request_result' => $order_total_required_array,
                    'process_result' => $process_result,
                    'collect_result' => $collect_result,
                    'extra_info' => $extra_info,
                );
            } catch (Exception $e) {
                throw new Exception($e->getMessage(), $e->getCode());
            }
        }
        return $this->cartFullInfo;
    }

    public function checkCoupon()
    {
        $model = OrderCoupons::model()->checkCoupon();
        return isset($model->coupon_id) ? true : false;
    }

    public function getCartId($new_if_not_exist = false, $throw_error = true)
    {
        if ($this->cartID === 0) {
            $cartID = Yii::app()->session->get('cartID', 0);

            if ($cartID > 0 && OgmCustomersBasket::model()->validCartId($cartID, true)) {
                // valid id
                $this->cartID = $cartID;
            } else {
                if ($new_if_not_exist === true) {
                    if (($cartID = $this->generateCartId()) > 0) {
                        $this->setCartId($cartID);
                    } else {
                        $this->setCartId(0);

                        if ($throw_error) {
                            $this->reportError('[Method] getCartId', array('error' => 'ERROR_CART_GENERATE_CART_ID'), '', 'DEV_DEBUG_CHECKOUT');
                            throw new Exception('ERROR_GENERAL__E18');
                        }
                    }
                } else {
                    $this->setCartId(0);

                    if ($throw_error) {
                        $this->reportError('[Method] getCartId', array('error' => 'ERROR_INVALID_CART_ID', 'cartID' => $cartID), '', 'DEV_DEBUG_CHECKOUT');
                        throw new Exception('ERROR_GENERAL__E19');
                    }
                }
            }
        }

        return $this->cartID;
    }

    public function getTaxAmount($ot, $ot2)
    {
        $total_amount = 0;
        $discount_amount = 0;
        foreach ($ot2 as $temp_ot) {
            if ($temp_ot['code'] == 'ot_subtotal') {
                $total_amount = $temp_ot['checkout_value'];
            } elseif ($temp_ot['code'] == 'ot_coupon') {
                $discount_amount = $temp_ot['checkout_value'];
            }
        }
        if ((double)$total_amount == 0) {
            return '';
        }
        $ip_iso2 = Yii::app()->customerCom->getCountryByIP(getIPAddress());
        $m_customer = OrderCustomers::model()->customerDetail($ot['customer']['id']);
        if (empty($m_customer)) {
            $attachments = array(
                array(
                    'color' => 'warning',
                    'text' => json_encode([
                        $ot
                    ])
                )
            );
            Yii::app()->slack->send('Debug CheckoutCart :: Missing Customers Info', $attachments, 'DEV_DEBUG');
            throw new Exception("Your account has been disabled. Please contact support for assistance.", 400);
        }
        $billing_country = isset($m_customer->ab->countries->countries_id) ? Countries::getCountryISO2($m_customer->ab->countries->countries_id) : '';
        $phone_country = isset($m_customer->customers_country_dialing_code_id) ? Countries::getCountryISO2($m_customer->customers_country_dialing_code_id) : '';
        $email_country = explode('.', strtoupper($m_customer->customers_email_address));
        end($email_country);

        //TODO :: Temporary Mapping for known email countries
        if (!in_array($email_country, ['MY', 'SG', 'US', 'AU', 'ID'])) {
            $email_country = '';
        }

        $params = [
            'customers_id' => $ot['customer']['id'],
            'customers_info' => [
                'ip_country' => ($ip_iso2 ?: 'US'),
                'billing_country' => $billing_country,
                'phone_country' => $phone_country,
                'email_country' => $email_country,
                'currency' => $ot['checkout_info']['currency'],
            ],
            'total_amount' => $total_amount,
            'discount_amount' => $discount_amount,
            'total_payable' => $total_amount - $discount_amount,
        ];

        $sc_products_id = Categories::model()->storeCreditProductID(Yii::app()->session['customers_groups_id']);

        foreach ($ot['products'] as $product) {
            if ($product['id'] == $sc_products_id) {
                return '';
            }
            $product_info = [
                'products_id' => $product['id'],
                'quantity' => $product['quantity'],
                'selling_price' => $product['final_price'],
            ];
            if ($product['products_package']) {
                foreach ($product['products_package'] as $id => $sub_product) {
                    $product_info['bundle_items'][] = [
                        'products_id' => $id,
                        'quantity' => $sub_product['qty'],
                        'selling_price' => $sub_product['normal_price'],
                    ];
                }
            }
            $params['products_info'][] = $product_info;
        }

        $tax_data = (new MsOrderModel)->getTaxInfo($params);

        if (isset($tax_data['error_code']) || isset($tax_data['status']) && $tax_data['status'] > 200) {
            throw new Exception('Fail to get tax info');
        }

        return $tax_data;
    }

    public function getBuyNowCartObject()
    {
        $return_obj = null;

        if ($this->isCartEmpty() !== true) {
            foreach ($this->cItem as $cobj) {
                $return_obj = $cobj;
                break;
            }
        }

        return $return_obj;
    }

    public function getCoupon($default = array('code' => '', 'has_checked' => true))
    {
        return Yii::app()->session->get('cc_info', array('code' => '', 'has_checked' => true));
    }

    /*
     * Country ID eg. Malaysia : 129
     */

    public function getCountryID()
    {
        return $this->getCustomerSetting('country', '');
    }

    public function getCustomerID()
    {
        return $this->getCustomerSetting('customer_id', 0);
    }

    public function getCustomerGroupID()
    {
        return $this->getCustomerSetting('customers_groups_id', 1);
    }

    private function getCustomerSetting($field, $return_str = null)
    {
        if (isset($this->customerSetting[$field])) {
            $return_str = $this->customerSetting[$field];
        } else {
            if (in_array($field, array('ip', 'customer_id', 'game_zone_id'))) {
                if ($field == 'ip') {
                    $return_str = getIPAddress();
                } else {
                    if ($field == 'customer_id') {
                        $return_str = Yii::app()->user->id;
                    } else {
                        if ($field == 'game_zone_id') {
                            $return_str = Yii::app()->localizationCom->getZoneID(1, $this->getCountryID());
                        }
                    }
                }

                $this->setCustomerSetting($field, $return_str);
            } else {
                $return_str = Yii::app()->session->get($field, $return_str);
            }
        }

        return $return_str;
    }

    public static function getErrorMessage($error_info, $delimiter = '<br>', $error_msg_array = array())
    {
        if (is_array($error_info)) {
            foreach ($error_info as $error_key) {
                $error_msg_array[] = self::getErrorMessage($error_key, $delimiter, $error_msg_array);
            }
        } else {
            if ($error_info !== '') {
                $prefix = '';
                $error_info_array = explode('__', $error_info);

                if (count($error_info_array) == 2) {
                    list($error_info, $prefix) = $error_info_array;
                    $prefix = '[' . $prefix . '] ';
                }

                $error_msg = Yii::t('checkoutModule.checkout', $error_info);

                if ($error_info !== $error_msg) {
                    $error_msg_array[] = $prefix . $error_msg;
                } else {
                    $error_msg_array[] = $prefix . Yii::t('page_content', $error_info);
                }
            }
        }

        return implode($delimiter, $error_msg_array);
    }

    public static function getExtraInfoFromDB($info_key, $id = 0, $id_type = 'tran_id', $customer_id = 0)
    {
        $return_array = array();

        if ($id_type == 'cart_id') {
            $cart_info_obj = OgmCustomersBasket::model()->getCartInfoByCID($id, $customer_id);
        } else {
            if ($id_type == 'tran_id') {
                $cart_info_obj = OgmCustomersBasket::model()->getCartInfoByTID($id, $customer_id);
            }
        }

        if (!is_null($cart_info_obj)) {
            $customer_setting_array = json_decode($cart_info_obj->cart_info, true);
            $return_array = isset($customer_setting_array['extra_info'][$info_key]) ? $customer_setting_array['extra_info'][$info_key] : array();
        }

        return $return_array;
    }

    public function getLanguageID()
    {
        return $this->getCustomerSetting('language_id', 0);
    }

    public function getPaymentInfo()
    {
        return array(
            'payment_method_parent_id' => $this->getCustomerSetting('payment_method_parent_id', ''),
            'payment_method_id' => $this->getCustomerSetting('payment_method_id', ''),
            'title' => $this->getCustomerSetting('payment_method_title', ''),
            'paypal_ipn_id' => $this->getCustomerSetting('paypal_ipn_id', ''),
        );
    }

    public function getPaymentConfirmCompleteDays()
    {
        return $this->getCustomerSetting('payment_confirm_complete_days', 0);
    }

    private function getProductDiscount($products_id, $customer_id = 0)
    {
        $return_array = array();

        Yii::app()->customerCom->setCustomerID($customer_id);
        $_cust_disc = Yii::app()->customerCom->getDiscountInfo($products_id, 'total_discount');

        if (abs($_cust_disc) > 0) {
            $return_array[] = $_cust_disc;
        }

        return $return_array;
    }

    private function getProductObj()
    {
        if (!is_object($this->product_obj)) {
            $this->product_obj = new CheckoutProductCom();
        }

        return $this->product_obj;
    }

    public function getReturnURL()
    {
        return $this->getCustomerSetting('return_url', array());
    }

    public function getThreatMetrixInfo()
    {
        return $this->getCustomerSetting('tm_info', array());
    }

    public function isCartEmpty()
    {
        return empty($this->cItem);
    }

    public function isUserPasswordAuthorized()
    {
        return $this->getCustomerSetting('user_password_authorized', AuthCom::checkIsLifetimeCookiesExisted());
    }

    public function loadCustomerSettingByTID($pg_action, $tran_id, $payment_return_array)
    {
        $enabled_cart_info_validation = true;

        if (!is_null($pg_action) && !is_null($tran_id) && $payment_return_array !== '') {
            $customer_setting_obj = OgmCustomersBasket::model()->getCartInfoByTID($tran_id);

            if (!is_null($customer_setting_obj)) {
                $cart_info_array = json_decode($customer_setting_obj->cart_info, true);
                $this->setCartId($customer_setting_obj->cart_id);
                $this->restoreCart($customer_setting_obj->cart_id);

                switch ($pg_action) {
                    case 'request_update_customer_info':
                        $enabled_cart_info_validation = false;
                        $this->setCustomerSettings($cart_info_array, $coupon_has_checked = true);
                        break;
                    case 'pg_change_currency':
                        $enabled_cart_info_validation = false;
                        $this->setCustomerSettings($cart_info_array, $coupon_has_checked = true);
                        $this->setCustomerSetting('store_currency', $this->getCustomerSetting('store_currency'));
                        $this->setCustomerSetting('currency', $payment_return_array['currency_code']);
                        $this->setCustomerSetting('pg_payment_ip', $payment_return_array['checkout_IP']);
                        break;
                    case 'pg_redeem_coupon':
                        $enabled_cart_info_validation = false;
                        $this->setCustomerSettings($cart_info_array);
                        $this->setCoupon($payment_return_array['coupon_code'], $coupon_has_checked = false);
                        break;
                    case 'pg_confirm_checkout':
                        if (isset($payment_return_array['tax_info']['amount'])) {
                            $payment_return_array['tax_info']['amount'] = (double)$payment_return_array['tax_info']['amount'];
                        }

                        $this->setCustomerSettings($cart_info_array, $coupon_has_checked = false);
                        $this->setCustomerSettings(array(
                            'extra_info' => array(
                                'payment_method_id' => $payment_return_array['payment_id'],
                                'payment_method_parent_id' => $payment_return_array['payment_parent_id'],
                                'payment_method_title' => $payment_return_array['payment_name'],
                                'payment_confirm_complete_days' => $payment_return_array['payment_confirm_complete_days'],
                                'paypal_ipn_id' => '',
                                'pg_surcharge' => $payment_return_array['surcharge'],
                                'pg_tax_info' => (isset($payment_return_array['tax_info']) && count($payment_return_array['tax_info'])) ? $payment_return_array['tax_info'] : '',
                                'pg_payment_ip' => $payment_return_array['checkout_IP'],
                                'pg_payment_ip_country' => isset($payment_return_array['checkout_IP_country']) ? $payment_return_array['checkout_IP_country'] : '',
                            ),
                        ));
                        break;
                }

                Yii::app()->frontPageCom->_init(array(
                    'country_id' => $this->getCountryID(),
                ));
                Yii::app()->customerCom->_init(array(
                    'customer_id' => $this->getCustomerID(),
                    'customer_groups_id' => $this->getCustomerGroupID(),
                ));
                Yii::app()->currency->_init($this->getCustomerSetting('default_currency'), $this->getCustomerSetting('currency'));

                if ($enabled_cart_info_validation && $this->validateSimilarityCartInfo($this->getCartFullDetails(), $payment_return_array) !== true) {
                    throw new Exception('ERROR_GENERAL__E1');
                    // ERROR_CART_MISMATCH_CART_INFO : error report will be sent in this condition.
                }
            } else {
                $this->reportError('[Method] loadCustomerSettingByTID', array('error' => 'ERROR_CART_MISSING_CART_INFO', 'pg_action' => $pg_action, 'tran_id' => $tran_id), '', 'DEV_DEBUG_CHECKOUT');
                throw new Exception('ERROR_GENERAL__E2');
            }

            unset($customer_setting_obj);
        } else {
            if (!is_null($pg_action) && $payment_return_array !== '') {
                # Custom way to overwrite customer setting
                switch ($pg_action) {
                    case 'sc_topup_change_currency':
                        $sc_checkout_cur = $payment_return_array['currency_code'];
                        $current_checkout_cur = $this->getCustomerSetting('currency');
                        $currency_restriction = in_array($sc_checkout_cur, ['MYR', 'SGD']);
                        if ($currency_restriction || $current_checkout_cur != 'USD') {
                            $this->setCustomerSetting('currency', $sc_checkout_cur);
                        }
                        break;
                }
            }
        }
    }

    public function resetCart($customer_id = 0)
    {
        if (empty($customer_id)) {
            $customer_id = $this->getCustomerID();
        }
        $this->cItem = array();
        $this->setCartId(0);
        self::removeCartFromDB($customer_id);
    }

    public static function removeCartFromDB($customer_id = 0)
    {
        OgmCustomersBasket::model()->removeOldCart($customer_id);
    }

    public function removeCoupon()
    {
        if (Yii::app()->session->contains('cc_info')) {
            Yii::app()->session->offsetUnset('cc_info');
        }
    }

    private function restoreCart($cartID, $customer_id = 0)
    {
        try {
            if ($cartID > 0) {
                $_m_data = OgmCustomersBasket::model()->getCartInfoByCartId($cartID, $customer_id);

                if (!empty($_m_data)) {
                    $isCustomerSettingUpdated = false;

                    foreach ($_m_data as $_data_obj) {
                        $cItem = new CartItemForm('restore_cart');
                        $cItem->setSerializedData($_data_obj->customers_basket_custom_value);

                        $this->cItem[$cItem->getUniqueKey()] = $cItem;

                        if ($isCustomerSettingUpdated) {
                            $row = $_data_obj->getAttributes();
                            $isCustomerSettingUpdated = true;

                            $this->setCustomerSettings($customer_setting_array = json_decode($row['cart_info'], true), $coupon_has_checked = true);
                        }
                    }
                } else {
                    throw new Exception('ERROR_GENERAL__E17');
                }
            }
        } catch (Exception $e) {
            $this->resetCart();
        }
    }

    private function save2DB()
    {
        $return_bool = false;
        // assume cartID already exist in this stage as it's call internally

        foreach ($this->cItem as $key => $data_obj) {
            $return_bool = OgmCustomersBasket::model()->saveToCart($this->cartID, $data_obj->products_id, $data_obj->qty, 0, $data_obj->custom_key, $data_obj->getSerializedData()) > 0;
        }

        if ($return_bool === false) {
            $this->reportError('[Method] save2DB', array('error' => 'ERROR_CART_DB_SAVE_FAILED', 'cart_id' => $this->cartID, 'cItem' => $this->cItem), '', 'DEV_DEBUG_CHECKOUT');
            throw new Exception('ERROR_GENERAL__E3');
        }

        return $return_bool;
    }

    public function saveCustomerSettingByCartID2DB($cart_id, $tran_id)
    {
        $update_status = null;

        try {
            $customer_setting_array = $this->getCartFullDetails();

            if (isset($customer_setting_array['request_result']) && $customer_setting_array['request_result'] !== array()) {
                $update_status = OgmCustomersBasket::model()->updateAll(array(
                    'pg_tran_id' => $tran_id,
                    'cart_info' => json_encode($customer_setting_array['request_result']),
                ), 'cart_id=:cart_id', array(
                    ':cart_id' => $cart_id,
                ));

                if ($update_status === false) {
                    $this->reportError(
                        '[Method] saveCustomerSettingByCartID2DB',
                        array('error' => 'ERROR_CART_DB_UPDATE_FAILED', 'cart_details' => $customer_setting_array['request_result'], 'tran_id' => $tran_id),
                        '',
                        'DEV_DEBUG_CHECKOUT'
                    );
                    throw new Exception('ERROR_GENERAL__E4');
                }
            }

            unset($customer_setting_array);
        } catch (Exception $e) {
            $this->reportError('[Method] saveCustomerSettingByCartID2DB', array('error' => $e->getMessage(), 'tran_id' => $tran_id), '', 'DEV_DEBUG_CHECKOUT');
            throw new Exception('ERROR_GENERAL__E5');
        }

        return $update_status;
    }

    public static function saveErrorCode2DB($error_code, $id = 0, $id_type = 'cart_id')
    {
        $error_code = is_array($error_code) ? $error_code : array($error_code);
        return self::saveExtraInfo2DB($error_code, 'error', $id, $id_type, $array_append = true) > 0 ? null : $error_code;
    }

    public static function saveThreatMetrixInfo2DB($info_array, $id = 0, $id_type = 'cart_id')
    {
        self::saveExtraInfo2DB($info_array, 'tm_info', $id, $id_type);
    }

    public static function saveExtraInfo2DB($info_array, $info_key, $id = 0, $id_type = 'cart_id', $array_append = false)
    {
        $update_status = null;
        $cart_id = 0;
        $tran_id = 0;
        $cart_info_array = array();

        if ($id_type == 'cart_id') {
            $cart_info_obj = OgmCustomersBasket::model()->getCartInfoByCID($id);
        } else {
            if ($id_type == 'tran_id') {
                $cart_info_obj = OgmCustomersBasket::model()->getCartInfoByTID($id);
            }
        }

        if (!is_null($cart_info_obj)) {
            $cart_id = $cart_info_obj->cart_id;
            $tran_id = isset($cart_info_obj->pg_tran_id) ? $cart_info_obj->pg_tran_id : $tran_id;
            $cart_info_array = !empty($cart_info_obj->cart_info) ? json_decode($cart_info_obj->cart_info, true) : array();
        }

        if ($cart_id > 0) {
            if ($array_append === true) {
                if (isset($cart_info_array['extra_info'][$info_key])) {
                    $info_array = array_merge((array)$cart_info_array['extra_info'][$info_key], $info_array);
                }
            }

            $cart_info_array['extra_info'][$info_key] = $info_array;
            $update_status = OgmCustomersBasket::model()->updateAll(array(
                'pg_tran_id' => $tran_id,
                'cart_info' => json_encode($cart_info_array),
            ), 'cart_id=:cart_id', array(
                ':cart_id' => $cart_id,
            ));
        }

        return $update_status;
    }

    private function setCartId($cart_id)
    {
        $this->cartID = $cart_id;
        Yii::app()->session->add('cartID', $cart_id);
    }

    public function setCoupon($coupon_str, $has_checked = false)
    {
        if ($coupon_str !== '') {
            $coupon_info_array = array(
                'code' => $coupon_str,
                'has_checked' => $has_checked,
            );
            Yii::app()->session->add('cc_info', $coupon_info_array);
        }
    }

    public function setCustomerSetting($idx, $data)
    {
        if (!is_null($data)) {
            $this->customerSetting[$idx] = $data;
        }
    }

    private function setCustomerSettings($data_array, $coupon_has_checked = true)
    {
        $process_array = array();

        $customerSetting = array(
            'id' => 'customer_id',
            'country' => 'country',
            'language' => 'language_id',
            'language_code' => 'language',
            'default_language' => 'default_language_id',
            'group_id' => 'customers_groups_id',
            'ip' => 'ip',
            'currency' => 'currency',
            'store_currency' => 'store_currency',
            'default_currency' => 'default_currency',
            'coupon_code' => '',
        );

        // ot or extra_info
        foreach ($data_array as $group => $sub_process_array) {
            if (in_array($group, array('extra_info'))) {
                foreach ($sub_process_array as $field => $value) {
                    $this->setCustomerSetting($field, $value);
                }
            } else {
                foreach ($sub_process_array as $category => $sub_data_array) {
                    if (in_array($category, array('customer', 'checkout_info'))) {
                        $process_array = array_merge($process_array, $sub_data_array);
                    }
                }
            }
        }

        foreach ($customerSetting as $field => $idx) {
            if (isset($process_array[$field])) {
                if ($field == 'coupon_code') {
                    $this->setCoupon($process_array[$field], $coupon_has_checked);
                } else {
                    $this->setCustomerSetting($idx, $process_array[$field]);
                }

                unset($customerSetting[$field]);

                if (count($customerSetting) == 0) {
                    break;
                }
            }
        }

        unset($process_array);
    }

    public function setPGSAccessCode($code)
    {
        $this->setCustomerSetting('access_code', $code);
        $this->updateCartFullDetailsExtraInfo('access_code', $code);
    }

    public function setReturnURL($route, $params_array = array())
    {
        if ($route !== '') {
            $error_array = array(
                'route' => $route,
                'params' => $params_array,
            );

            Yii::app()->session->add('return_url', $error_array);
            $this->setCustomerSetting('return_url', $error_array);
        }
    }

    private function updateCartFullDetailsExtraInfo($key, $value)
    {
        if (isset($this->cartFullInfo['request_result']['extra_info'][$key])) {
            $this->cartFullInfo['request_result']['extra_info'][$key] = $value;
        }
    }

    private function validateSimilarityCartInfo($db_cart_info_array, $pg_cart_info_array)
    {
        $return_bool = false;
        $new_amount = 0;
        $collect_result_array = isset($db_cart_info_array['collect_result']) ? $db_cart_info_array['collect_result'] : array();
        $process_result_array = isset($db_cart_info_array['process_result']) ? $db_cart_info_array['process_result'] : array();
        $checkout_info_array = isset($db_cart_info_array['request_result']['ot']['checkout_info']) ? $db_cart_info_array['request_result']['ot']['checkout_info'] : array();
        $error_msg = '';

        if (count($collect_result_array) !== 0) {
            // checkout process will handle this type of error
            $return_bool = true;
        } else {
            $process_result_array = array_reverse($process_result_array);

            foreach ($process_result_array as $ot_info) {
                if ($ot_info['code'] == 'ot_total') {
                    $new_amount = $ot_info['checkout_value'];
                    break;
                }
            }

            if ($checkout_info_array !== array()) {
                $tax_info = (isset($pg_cart_info_array['tax_info']) && count($pg_cart_info_array['tax_info'])) ? $pg_cart_info_array['tax_info'] : '';
                $tax_amt = isset($tax_info['amount']) ? $tax_info['amount'] : 0;
                $pg_amount = number_format(
                    ($pg_cart_info_array['amount'] + $pg_cart_info_array['surcharge'] + $tax_amt),
                    Yii::app()->currency->currencies[$checkout_info_array['currency']]['decimal_places'],
                    Yii::app()->currency->currencies[$checkout_info_array['currency']]['decimal_point'],
                    ''
                );

                if ($checkout_info_array['currency'] !== $pg_cart_info_array['currency_code']) {
                    // check currency_code
                    $error_msg = ' - due to cart currency ' . $checkout_info_array['currency'] . ' !== ' . $pg_cart_info_array['currency_code'];
                } else {
                    if (bccomp($new_amount, $pg_amount, Yii::app()->currency->currencies[$checkout_info_array['currency']]['decimal_places']) !== 0) {
                        // check amount
                        $error_msg = ' - due to new amt ' . $new_amount . ' !== (' . $pg_cart_info_array['amount'] . ' + ' . $pg_cart_info_array['surcharge'] . ' + ' . $tax_amt . ')';
                    } else {
                        $return_bool = true;
                    }
                }
            } else {
                $error_msg = ' - due to empty checkout info array.';
            }
        }

        if ($return_bool !== true) {
            $this->reportError(
                '[Method] validateCartInfo' . $error_msg,
                array('process' => $process_result_array, 'checkout' => $checkout_info_array, 'pg_data' => $pg_cart_info_array),
                '',
                'DEV_DEBUG_CHECKOUT'
            );
        }

        unset($collect_result_array, $process_result_array, $checkout_info_array);

        return $return_bool;
    }

    public function setReturnURLWithRegionalSetting()
    {
        $return_url = $this->getReturnURL();

        $return_url['params']['retry'] = 1;

        if (!empty($return_url)) {
            $return_url = $this->getChangeCurrencyUrl($return_url['route'], $return_url['params']);
            $this->setReturnURL($return_url[0], $return_url[1]);
        }
    }

    public function getOrderSuccessUrl($orders_id)
    {
        $route = '/checkout/buyNow/success';
        $params = ['oid' => $orders_id];
        $redirectUrl = Yii::app()->createAbsoluteUrl($route, $params);

        if ($this->getCustomerSetting('store_currency') !== $this->getCustomerSetting('currency')) {
            $return_url = $this->getChangeCurrencyUrl($route, $params);
            return Yii::app()->createAbsoluteUrl($return_url[0], $return_url[1]);
        }

        return $redirectUrl;
    }

    public function getCheckoutPGUrl($orders_id)
    {
        $route = '/checkout/buyNow/redirectToPG';
        $params = ['oid' => $orders_id];
        $redirectUrl = Yii::app()->createAbsoluteUrl($route, $params);

        return $redirectUrl;
    }

    private function getChangeCurrencyUrl($route, $params)
    {
        $params = [
            'reg_cur' => $this->getCustomerSetting('currency'),
            'reg_ctry' => Countries::model()->find('countries_id = ' . $this->getCustomerSetting('country'))->countries_iso_code_2,
            'reg_lang' => $this->getCustomerSetting('language'),
            'previousUrl' => $route,
            'getParam' => urlencode(json_encode($params)),
        ];

        return ['/userBar/regional', $params];
    }

}