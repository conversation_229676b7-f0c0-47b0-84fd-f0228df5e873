<?php

class CheckoutCom extends MainCom
{

    private $storeCreditBalanceInfo = array();
    private $checkout_cart_obj;
    private $zero_amount_order = 0;
    private $error = null;

    // wee siong
    private function captureErrorCode($error_message, $id = 0, $id_type = 'cart_id')
    {
        try {
            $id = ($id == 0) ? $this->getCartObj()->getCartId() : $id;

            if ($id !== 0) {
                $this->error = CheckoutCartCom::saveErrorCode2DB($error_message, $id, $id_type);
            } else {
                $this->error = $error_message;
            }
        } catch (Exception $e) {
            $this->error = $error_message;
        }

        return 'PROCESS_TO_ERROR_PAGE';
    }

    // wee siong
    private function createOrder($cartFullDetails)
    {
        $return_array = false;
        
        if (isset($cartFullDetails['request_result']) && $cartFullDetails['request_result'] !== array()) {
            // ==================================== call ORDER module ====================================

            try {
                $data = [
                    'customers_id' => $cartFullDetails['request_result']['ot']['customer']['id'],
                    'country_id' => $cartFullDetails['request_result']['ot']['customer']['country'],
                    'currency' => $cartFullDetails['request_result']['ot']['checkout_info']['currency'],
                    'currency_value' => $cartFullDetails['request_result']['ot']['checkout_info']['currency_value'],
                    'coupon_code' => $cartFullDetails['request_result']['ot']['checkout_info']['coupon_code'],
                    'ip_address' => $cartFullDetails['request_result']['ot']['checkout_info']['payment_ip'],
                    'ip_country' => $cartFullDetails['request_result']['ot']['checkout_info']['payment_ip_country'],
                    'products' => $cartFullDetails['request_result']['ot']['products'],
                    'products_tax_info' => ($cartFullDetails['request_result']['ot']['checkout_info']['products_tax_info'] ?? []),
                    'ot' => $cartFullDetails['process_result']
                ];

                $return_array = (new MsOrderModel())->createOrder($data);
                if (empty($return_array['orders_id'])) {
                    throw new Exception('Failed to create order '.json_encode(['response' => $return_array, 'request' => $data]));
                }

            } catch (Exception $e) {
                $this->reportError('[Method][1] createOrder', array('error' => $e->getMessage(), 'response' => $cartFullDetails));
                throw new Exception(Yii::t('orderModule.OrderCom', 'ERROR_MISSING_CART_ITEM'));
            }

            unset($order_obj);

            // ==================================== call ORDER module ====================================
        } else {
            // this will only occur during development
            $this->reportError('[Method][2] createOrder', $cartFullDetails);
            throw new Exception(Yii::t('orderModule.OrderCom', 'ERROR_MISSING_CART_ITEM'));
        }

        return $return_array;
    }

    // wee siong
    public function getErrorReturnURL($default_route = '/site/index')
    {
        $params = array();
        $url_route = $default_route;

        $returnURL_array = $this->getCartObj()->getReturnURL();

        // redirect back to buy now page with error code
        if ($returnURL_array !== array()) {
            $params = $returnURL_array['params'];
            $url_route = $returnURL_array['route'];
        }

        if (filter_var($url_route, FILTER_VALIDATE_URL)) {
            return $url_route;
        } elseif ($params !== array()) {
            if (!is_null($this->error)) {
                $params['error'] = CheckoutCartCom::getErrorMessage($this->error);
            }

            $url_route = Yii::app()->createAbsoluteUrl($url_route) . '?' . http_build_query($params, null, '&');
        } elseif ($url_route == '/site/index') {
            // Temporary fix for customer land to pipwave /site/index page when cart missing
            // TODO implement CTA when cart missing
            $url_route = Yii::app()->createAbsoluteUrl($url_route);
        }

        if (filter_var($url_route, FILTER_VALIDATE_URL)) {
            if (strpos($url_route, 'retry=1') === false) {
                $url_route .= (strpos($url_route, '?') !== false ? '&' : '?') . 'retry=1';
            }
        }

        return $url_route;
    }

    public function processCheckout($pg_action = null, $trans_id = null, $payment_return = array())
    {
        $status_code = '';
        $order_id = 0;
        $redirect_url = '';

        try {
            Yii::app()->getModule('order');
            $this->getCartObj()->loadCustomerSettingByTID($pg_action, $trans_id, $payment_return);  // only used by PG API - restore cart, get customer setting loaded.
            $cartFullDetails = $this->getCartObj()->getCartFullDetails();

            if ($this->getCartObj()->checkoutPermission()) {
                $this->resetFullCreditCorvered();
                $this->getOTMappingInfo($cartFullDetails);

                if ($this->isDormant()) {
                    throw new Exception('ERROR_IS_DORMANT_ACCOUNT');
                } else {
                    if (is_null($this->error)) {   // if ot doesn't return error
                        $status_code = 'PROCESS_TO_PAYMENT_PAGE';
                        if ($this->isFullCreditCorvered() && $this->getCartObj()->isUserPasswordAuthorized() !== true) {
                            $this->getCartObj()->setReturnURLWithRegionalSetting();
                            throw new Exception(Yii::t('orderModule.OrderCom', 'ERROR_CONFIRM_SC_PASSWORD'));
                        } else {
                            if ($return_arr = $this->createOrder($cartFullDetails)) {
                                $order_id = (!empty($return_arr['orders_id']) ? $return_arr['orders_id'] : 0);

                                if ($order_id > 0) {
                                    $status_code = 'PROCESS_TO_SUCCESS_PAGE';
                                    $redirect_url = $this->getCartObj()->getOrderSuccessUrl($order_id);
                                    $this->getCartObj()->resetCart();
                                    if (!$this->isFullCreditCorvered()) {
                                        $status_code = 'PROCESS_TO_PAYMENT_PAGE';
                                        $redirect_url = $this->getCartObj()->getCheckoutPGUrl($order_id);
                                    }
                                }
                            }
                        }
                    } else {
                        $status_code = $this->captureErrorCode($this->error);
                    }
                }
            }
        } catch (Exception $e) {
            // Missing Cart
            if ($e->getMessage() == 'ERROR_GENERAL__E19') {
                if ($pid = Yii::app()->session->get('last_checkout_pid')) {
                    $params = ['pid' => $pid, 'retry' => 1, 'error_msg' => Yii::t('orderModule.OrderCom', 'ERROR_MISSING_CART_ITEM')];

                    if(!empty($_REQUEST['sub_products_id'])) {
                        $params['sub_products_id'] = $_REQUEST['sub_products_id'];
                    }

                    if(!empty($_REQUEST['account'])) {
                        $params['account'] = $_REQUEST['account'];
                    }

                    Yii::app()->controller->redirect(Yii::app()->createUrl('checkout/buyNow/buy', $params));
                } else {
                    Yii::app()->controller->redirect(Yii::app()->createUrl('/', array('pid' => $pid)));
                }
            } else {
                $msg = $e->getMessage();
                $accepted_error = [
                    Yii::t('orderModule.OrderCom', 'ERROR_MISSING_CART_ITEM'),
                    Yii::t('orderModule.OrderCom', 'ERROR_CONFIRM_SC_PASSWORD'),
                    Yii::t('orderModule.OrderCom', 'ERROR_PURCHASE_QUANTITY_EXCEEDED'),
                    Yii::t('orderModule.OrderCom', 'ERROR_MISSING_DTU_INFO')
                ];
                if (!in_array($msg, $accepted_error)) {
                    $slack_string = (!empty($_REQUEST['extra_param_1']) ? $_REQUEST['extra_param_1'] : '') . $msg . $e->getTraceAsString();
                    $attachments = array(
                        array(
                            'color' => 'danger',
                            'text' => $slack_string
                        )
                    );
                    Yii::app()->slack->send('Checkout fail to create order', $attachments, 'DEV_DEBUG');
                }
                if (isset($_REQUEST['checkout_pid'])) {
                    $redirect_data = ['pid' => $_REQUEST['checkout_pid'], 'retry' => 1, 'error_msg' => $e->getMessage()];
                    if (!empty($_REQUEST['sub_products_id'])) {
                        $redirect_data['sub_products_id'] = $_REQUEST['sub_products_id'];
                    }

                    if(!empty($_REQUEST['account'])) {
                        $redirect_data['account'] = $_REQUEST['account'];
                    }

                    Yii::app()->controller->redirect(Yii::app()->createUrl('checkout/buyNow/buy', $redirect_data));
                }
            }
            $cartFullDetails = array();
            $status_code = $this->captureErrorCode($e->getMessage());
        }

        return array(
            'status_code' => $status_code,
            'cartFullDetails' => $cartFullDetails,
            'redirect_url' => $redirect_url,
            'order_id' => $order_id,
        );
    }

    public function processPGUpdateSetting($payment_trans_id)
    {
        $status_code = 'PROCESS_TO_PAYMENT_PAGE';

        try {
            $cart_id = $this->getCartObj()->getCartId();

            $this->getCartObj()->saveCustomerSettingByCartID2DB($cart_id, $payment_trans_id);
        } catch (Exception $e) {
            $status_code = $this->captureErrorCode($e->getMessage());
        }

        return array(
            'status_code' => $status_code,
        );
    }

    // wee siong
    // this is call by pg to create order.
    // user session does not exist
    public function processPGCheckout($payment_trans_id)
    {
        $status_code = '';
        $order_id = 0;
        $redirect_url = '';

        try {
            $customer_id = $this->getCartObj()->getCustomerID();
            $is_rp_type_pg = CheckoutPGCom::isRPTypePG($this->getCartObj()->getPaymentConfirmCompleteDays());
            $payment_info = $this->getCartObj()->getPaymentInfo();

            if ($is_rp_type_pg && Yii::app()->customerCom->isBillingInfoCompleted($customer_id) !== true) {
                $status_code = 'PROCESS_TO_POST_PAYMENT_PAGE';
            } else {
                $cart_details_array = $this->getCartObj()->getCartFullDetails();


                if ($return_arr = $this->createOrder($cart_details_array)) {
                    $order_id = (!empty($return_arr['order_id']) ? $return_arr['order_id'] : 0);
                    if (isset($GLOBALS['cart']) && !empty($GLOBALS['cart']['customer']['id'])) {
                        $this->getCartObj()->resetCart($GLOBALS['cart']['customer']['id']);
                    }
                }

                if ($order_id > 0) {
                    $status_code = 'PROCESS_TO_SUCCESS_PAGE';
                    $redirect_url = $this->getCartObj()->getOrderSuccessUrl($order_id);
                    // remove because if order been create and redirect to PG, user cancel the payment. It will redirect back to PGS, user still able to checkout
                    // $this->getCartObj()->resetCart();
                }
            }
        } catch (Exception $e) {
            $status_code = $this->captureErrorCode($e->getMessage(), $payment_trans_id, 'tran_id');
        }

        return array(
            'status_code' => $status_code,
            'order_id' => $order_id,
            'redirect_url' => $redirect_url
        );
    }

    // wee siong
    public function processOrder(
        $order_id,
        $language_id,
        $verify_amount = null,
        $verify_currency = null,
        $update_to_status = 7,
        $paypal_ipn_id = 0
    )
    {
        $amount = 0;
        $return_int = 0;
        $return_array = array();

        if ($order_id > 0) {
            // ==================================== call ORDER module ====================================

            $cart_param = array(
                'order_id' => $order_id,
                'update_to_status' => $update_to_status,
                'customer' => array(
                    'language' => $language_id,
                ),
                'payment_info' => array(
                    'paypal_ipn_id' => $paypal_ipn_id,
                )
            );

            try {
                # this currency init should be remove after added in the API controller's init. Required testing after remove.
                Yii::app()->currency->_init();

                Yii::app()->getModule('order');
                $order_obj = new OrderCom($cart_param);

                if (!is_null($verify_amount) && !is_null($verify_currency)) {   // For PG checkout, Full SC has no these data
                    if (!isset($order_obj->info['total'])) {
                        throw new Exception('ERROR_MISSING_ORDER_MODULE_DATA');
                    }

                    if (isset(Yii::app()->currency->currencies[$verify_currency])) {
                        $amount = Yii::app()->currency->currencies[$verify_currency]['symbol_left'] . number_format($verify_amount,
                                Yii::app()->currency->currencies[$verify_currency]['decimal_places'], Yii::app()->currency->currencies[$verify_currency]['decimal_point'],
                                Yii::app()->currency->currencies[$verify_currency]['thousands_point']) . Yii::app()->currency->currencies[$verify_currency]['symbol_right'];
                    }

                    if ($amount != $order_obj->info['total']) {
                        $attachments = array(
                            array(
                                'color' => 'danger',
                                'text' => json_encode([
                                    'orders_id' => $order_id,
                                    'orders_total' => $order_obj->info['total'],
                                    'pg_amount' => $amount
                                ])
                            )
                        );
                        Yii::app()->slack->send('ERROR_TOTAL_AMOUNT_MISMATCH : ', $attachments, 'DEV_DEBUG');
                        throw new Exception('ERROR_TOTAL_AMOUNT_MISMATCH : ' . $order_obj->info['total'] . ' != ' . $amount);
                    }
                }

                $return_array = $order_obj->processOrder();

                if (isset($return_array['error'])) {
                    if (stristr($return_array['error'], 'Invalid Customer Order status update from') !== false) {
                        $return_int = -1;   // Invalid Customer Order status update from {SYS_STATUS_FROM} to {SYS_STATUS_TO}
                    } else {
                        throw new Exception('[Order Module Error] ' . $return_array['error']);
                    }
                } else {
                    if (isset($return_array['order_id'])) {
                        $return_int = $return_array['order_id'];
                    } else {
                        throw new Exception('ERROR_MISSING_ORDER_MODULE_RESPONSE_DATA');
                    }
                }

                unset($order_obj);
            } catch (Exception $e) {
                unset($order_obj);
                $this->reportError('[Method] processOrder', array('response' => $cart_param, 'error' => $e->getMessage(), 'processOrder' => $return_array));
                throw new Exception('ERROR_GENERAL_E12');
            }

            // ==================================== call ORDER module ====================================
        }

        return $return_int;
    }

    // wee siong
    // Paypal Review & E-Cheque Status
    public function reviewOrder(
        $order_id,
        $language_id,
        $update_to_status = 7,
        $paypal_ipn_id = 0,
        $payment_status = 'Review'
    )
    {
        if ($order_id > 0) {
            $error_text = ($payment_status == 'Review') ? 'reviewOrder' : 'eCheckOrder';

            // ==================================== call ORDER module ====================================

            $cart_param = array(
                'order_id' => $order_id,
                'update_to_status' => $update_to_status,
                'customer' => array(
                    'language' => $language_id,
                ),
                'payment_info' => array(
                    'paypal_ipn_id' => $paypal_ipn_id,
                    'payment_status' => $payment_status,
                )
            );

            try {
                Yii::app()->getModule('order');
                $order_obj = new OrderCom($cart_param);

                if ($order_obj->reviewOrder($payment_status) !== true) {
                    $this->reportError('[Method] ' . $error_text, array('response' => $cart_param, 'error' => 'Email does not sent.'));
                }

                unset($order_obj);
            } catch (Exception $e) {
                unset($order_obj);
                $this->reportError('[Method] ' . $error_text, array('response' => $cart_param, 'error' => $e->getMessage()));
            }
            // ==================================== call ORDER module ====================================
        }

        return true;
    }

    private function isZeroAmountOrder()
    {
        return $this->zero_amount_order === 1;
    }

    private function setNotZeroOrder()
    {
        $this->zero_amount_order = 0;
    }

    private function setZeroOrder()
    {
        $this->zero_amount_order = 1;
    }

    private function getCartObj()
    {
        if (!is_object($this->checkout_cart_obj)) {
            $this->checkout_cart_obj = new CheckoutCartCom();
        }

        return $this->checkout_cart_obj;
    }

    // wee siong
    private function getOTMappingInfoError($data_array, $code)
    {
        $return_error = '';

        foreach ($data_array as $idx => $error_array) {
            if ($code == $error_array['code']) {
                $return_error = $error_array['error'];
                $this->error[] = $error_array['error'];
                break;
            }
        }

        return $return_error;
    }

    // wee siong
    public function getOTMappingInfo($full_cart_details_array = array())
    {
        $return_array = array();
        if (isset($full_cart_details_array['process_result'])) {
            $process_result = $full_cart_details_array['process_result'];
            $collect_result = $full_cart_details_array['collect_result'];
            $currency = $GLOBALS['cart']['checkout_info']['currency'];
            foreach ($process_result as $idx => $ot_array) {
                $item_array = array();

                switch ($ot_array['code']) {
                    case 'ot_subtotal':
                        $key = 'subtotal';
                        $item_array = array(
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text']
                        );

                        // ====================================  SET is Zero Amount Order  ====================================
                        if ($ot_array['checkout_value'] > 0) {
                            $this->setNotZeroOrder();
                        } else {
                            $this->setZeroOrder();
                        }
                        // ====================================  SET is Zero Amount Order  ====================================

                        break;
                    case 'ot_coupon':
                        $key = 'coupon';
                        $item_array = array(
                            'code' => $ot_array['input']['coupon_code'],
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text'],
                            'error_label' => $this->getOTMappingInfoError($collect_result, $ot_array['code'])
                        );
                        break;
                    case 'ot_gv':
                        $key = 'store_credit';
                        $item_array = array(
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text']
                        );

                        $this->setStoreCreditBalanceInfo('balance', array(
                            'label' => $ot_array['output']['opening_balance']['title'],
                            'formatted_value' => Yii::app()->currency->format($ot_array['output']['opening_balance']['value'], true, $currency, 1, 'sell', ' ')
                        ));

                        $this->setStoreCreditBalanceInfo('remain', array(
                            'label' => $ot_array['output']['closing_balance']['title'],
                            'formatted_value' => Yii::app()->currency->format($ot_array['output']['closing_balance']['value'], true, $currency, 1, 'sell', ' ')
                        ));

                        // ====================================  Validate Dormant  ====================================
                        $this->validateDormant(bcsub($ot_array['output']['opening_balance']['value'], $ot_array['output']['closing_balance']['value'], 6));
                        // ====================================  SET used Store Credit  ====================================

                        break;
                    case 'ot_gst':
                        $key = 'order_gst';
                        $item_array = array(
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text'],
                            'error_label' => $this->getOTMappingInfoError($collect_result, $ot_array['code'])
                        );
                        break;
                    case 'ot_total':
                        $key = 'order_total';
                        $item_array = array(
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text']
                        );

                        // ====================================  SET is Full Credit Corvered  ====================================
                        if ($ot_array['checkout_value'] > 0) {
                            $this->resetFullCreditCorvered();
                        } else {
                            $this->setFullCreditCorvered();
                        }
                        // ====================================  SET is Full Credit Corvered  ====================================

                        break;
                    default:
                        $key = $ot_array['code'];
                        $item_array = array(
                            'label' => $ot_array['display_title'],
                            'value' => $ot_array['checkout_value'],
                            'formatted_value' => $ot_array['storage_text']
                        );
                        break;
                }

                $item_array['display_value'] = Yii::app()->currency->format($ot_array['checkout_value'], true, $currency, 1, 'sell', ' ');
                $return_array[$key] = $item_array;
            }
        }

        return $return_array;
    }

    // wee siong
    // with user session
    public function getPaymentGatewayURL()
    {
        $status_code = '';
        $payment_url = '';
        $payment_trans_id = '';

        try {
            $customer_id = $this->getCartObj()->getCustomerID();
            $cart_id = $this->getCartObj()->getCartId();
            $cart_details_array = $this->getCartObj()->getCartFullDetails();

            // ==================================== call PG API ====================================

            $pg_obj = new CheckoutPGCom();
            $get_url_array = $pg_obj->getURL($cart_details_array);

            unset($pg_obj);
            $payment_trans_id = $get_url_array['tm_tid'];
            $payment_url = $get_url_array['full_url'];
            // ==================================== call PG API ====================================
            // save cart_info into db
            $this->getCartObj()->saveCustomerSettingByCartID2DB($cart_id, $payment_trans_id);
            $status_code = 'PROCESS_TO_PRE_PAYMENT_PAGE';

        } catch (Exception $e) {
            $status_code = $this->captureErrorCode($e->getMessage());
        }

        return array(
            'status_code' => $status_code,
            'payment_url' => $payment_url
        );
    }

    // wee siong
    public function getStoreCreditBalanceInfo($type)
    {
        return $this->storeCreditBalanceInfo[$type] ?? [];
    }

    // wee siong
    private function setStoreCreditBalanceInfo($type, $data_array)
    {
        $this->storeCreditBalanceInfo[$type] = $data_array;
    }

    // wee siong
    public function isFullCreditCorvered()
    {
        $return_bool = false;

        if (Yii::app()->session->contains('credit_covers')) {
            $return_bool = true;
        }

        return $return_bool;
    }

    // wee siong
    public function resetFullCreditCorvered()
    {
        if (Yii::app()->session->contains('credit_covers')) {
            Yii::app()->session->offsetUnset('credit_covers');
        }
    }

    // wee siong
    private function setFullCreditCorvered()
    {
        Yii::app()->session->add('credit_covers', true);
    }

    private function validateDormant($used_sc_value)
    {
        if (Yii::app()->user->id) {
            if ($used_sc_value > 0 && CheckoutCustomersInfo::model()->getDormantStatus(Yii::app()->user->id) == 1) {
                Yii::app()->session->add('need_sc_usage_qna', true);
            } else {
                if (Yii::app()->session->contains('need_sc_usage_qna')) {
                    Yii::app()->session->offsetUnset('need_sc_usage_qna');
                }
            }
        }
    }

    public function isDormant()
    {
        $return_bool = false;

        if (Yii::app()->session->contains('need_sc_usage_qna')) {
            if (Yii::app()->session['fb_uid']) {
                // skip
            } else {
                $return_bool = true;
            }
        }

        return $return_bool;
    }

    public static function reportError($subject, $response_data, $ext_subject = '', $channel = 'DEV_DEBUG_CHECKOUT')
    {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $response_data
            )
        );
        Yii::app()->slack->send($subject, $attachments, $channel);
    }

}
