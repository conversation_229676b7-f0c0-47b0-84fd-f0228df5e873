<?php

class CheckoutModulesCom extends MainCom
{
    public $order_products;
    public $sub_products;
    public $op_rebate = 0;
    public $restriction_mode;
    public $restriction_info;
    public $coupon_code;
    public $ip_restrict_pm = array();

    public $is_adyen_prohibited_product = false;

    public function getRequestDetails($order_id)
    {
        $this->ip_restrict_pm = Yii::app()->params['IP_RESTRICTION_PAYMENT_METHOD'];
        $order = OrdersCustomersPurchased::model()->getOrderHeader(Yii::app()->user->id, $order_id);
        $order_total = OrdersCustomersPurchased::model()->getOrderTotal($order_id);
        $sc_amount = 0;;

        $this->getOrderProducts($order_id);

        $customer_data_array = Yii::app()->customerCom->getCustomerData2(1, Yii::app()->user->id);

        if (!isset($customer_data_array['address_book'])) {
            $message = "Error Message : address_book not found \n";
            $message .= "Customer ID : " . json_encode(Yii::app()->user->id) . "\n";
            $message .= "Params : " . json_encode($customer_data_array) . "\n";

            $subject = "Address Book : CheckoutModulesCom@getRequestDetails";
            $attachments = array(
                array(
                    'color' => 'danger',
                    'text' => $message,
                )
            );
            Yii::app()->slack->send($subject, $attachments, 'DEV_ALERT');

            throw new Exception("Something went wrong, please try again.");
        }

        $country_array = CheckoutCountries::model()->getCountries($customer_data_array['address_book']['entry_country_id'], true);

        $data = [
            'version' => 2,
            'orders_id' => $order_id,
            'customer_id' => $order['customers_id'],
            'country_iso' => '',
            'language_id' => (!empty(Yii::app()->session['language_id']) ? Yii::app()->session['language_id'] : 1),
            'currency_code' => $order['currency'],
            "checkout_IP" => getIPAddress(),
            "checkout_IP_country" => Yii::app()->customerCom->getCountryByIP(getIPAddress()),
            "currency_value" => $order['currency_value'],
            'customer_info' => [
                'email_address' => $order['customers_email_address'],
                'firstname' => $this->getUser('customers_firstname'),
                'lastname' => $this->getUser('customers_lastname'),
                'customer_group' => CustomerCom::getCustomerGroupID($order['customers_id']),
                'street_address' => $order['customers_street_address'],
                'suburb' => $order['customers_suburb'],
                'city' => $order['customers_city'],
                'state' => $order['customers_state'],
                'postcode' => $order['customers_postcode'],
                'telephone' => $order['customers_country_international_dialing_code'] . $order['customers_telephone'],
                'country_ISO' => '', //worldpay use, customer address country ISO
                'fax' => $customer_data_array['customer']['customers_fax'],
            ],
            'additional_desc' => [],
            'products_info' => $this->order_products,
            'is_crypto_voucher' => $this->is_adyen_prohibited_product
        ];

        if ($taxOverride = $this->getTaxExemptionCountry()) {
            $data['tax_override'] = $taxOverride;
        }

        if (isset($country_array['countries_iso_code_2'])) {
            $data['country_iso'] = $country_array['countries_iso_code_2'];
            $data['customer_info']['country_ISO'] = $country_array['countries_iso_code_2'];
        }

        foreach ($order_total as $ot) {
            switch ($ot['class']) {
                case 'ot_total':
                    $data['amount'] = Yii::app()->currency->parseStringToNumber($order['currency'], $ot['text']);
                    break 2;
                // Block checkout when surcharge available
                case 'ot_surcharge':
                    return;
            }

            $ot_class = ucwords(explode('_', $ot['class'])[1]);

            $title = $ot['title'];

            switch (strtolower($ot_class)) {
                case 'gst':
                    $data['tax_amount'] = Yii::app()->currency->parseStringToNumber($data['currency_code'], $ot['text']);
                    $sort = 1;
                    $title = $ot['title'];
                    break;
                case 'gv':
                    $sort = 3;
                    $title = $ot['title'];
                    $sc_amount = Yii::app()->currency->parseStringToNumber($data['currency_code'], $ot['text']);
                    break;
                case 'coupon':
                    $sort = 2;
                    $coupon_code = explode(':', $ot['title']);
                    if (!empty($coupon_code[1])) {
                        $this->coupon_code = trim($coupon_code[1]);
                    }
                    break;
                case 'subtotal':
                    $sort = 0;
                    break;
            }

            $data['additional_desc'][$sort] = [
                'title' => $title,
                'class' => $ot['class'],
                'value' => $ot['value'] * $order['currency_value'],
                'text' => $ot['text'],
            ];
        }

        // Block Checkout When SC Amount Mismatch with Total Collected Amount
        if ($sc_amount > 0 && $sc_amount != StoreCreditCom::getOrderScPaidAmount($order_id)) {
            return;
        }

        ksort($data['additional_desc']);
        $data['extra_total_info'] = [
            [
                'name' => Yii::t('checkoutModule.checkout', 'TEXT_TOTAL_REBATE'),
                'value' => $this->op_rebate,
            ],
        ];

        // if payment restrictions set
        $ip_iso2 = Yii::app()->customerCom->getCountryByIP(getIPAddress());
        $this->setPaymentRestrictionsByIP($ip_iso2);
        $this->setCouponCodePaymentRestriction();

        if (!isset(Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID']) || !in_array(Yii::app()->user->id, Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID'])) {
            $this->setGlobalPMRestriction();
            $payment_code_array = $this->getPaymentRestrictionsCode();
        } else {
            $this->restriction_mode = null;
        }

        if (isset($this->restriction_mode) && $this->restriction_mode == 'Allow') {
            $prefered_payment = implode(",", $payment_code_array);
            $data['prefered_payment'] = (!empty($prefered_payment) ? $prefered_payment : '9999999');
        } elseif ($this->restriction_mode == 'Disallow') {
            $exclude_payment = implode(",", $payment_code_array);
            $data['exclude_payment'] = $exclude_payment;
        }

        $checkout = new MsCheckoutModel;

        $return = $checkout->sendOrderDetail($data);

        return $return;
    }

    public function getOrderProducts($orders_id)
    {
        $order_products = OrdersProductsBase::model()->getOrderProducts($orders_id);
        $products_list = [];
        $sub_products_list = [];

        foreach ($order_products as $product) {
            if ($this->is_adyen_prohibited_product === false && $this->checkIsAdyenOpenLoopProduct($product)) {
                $this->is_adyen_prohibited_product = true;
            }

            if ($product['products_bundle_id'] == 0) {
                if ($product['sub_products_id']) {
                    $_REQUEST['sub_products_id'] = $product['sub_products_id'];
                }

                if ($product['custom_products_type_id'] == 3) {
                    $unit_price = bcmul($product['final_price'], $product['currency_value'], 8);
                    if ($product['products_quantity'] > 1 && $product['currency'] !== 'USD') {
                        $unit_price = round($unit_price);
                    }
                    // Set Quantity as 1 and display total price
                    $unit_price = Yii::app()->currency->roundByCurrency($product['currency'], $unit_price * $product['products_quantity']);
                    $product['products_quantity'] = 1;
                } else {
                    $unit_price = Yii::app()->currency->roundByCurrency($product['currency'], $product['final_price'] * $product['currency_value']);
                }

                $_product = array(
                    'id' => $product['products_id'],
                    'name' => $product['products_name'],
                    'category' => ProductsBase::getCPathByProductsId($product['products_id']),
                    'quantity' => $product['products_quantity'],
                    'custom_products_type_id' => $product['custom_products_type_id'],
                    'final_price' => $unit_price,
                    'unit_price' => $unit_price,
                    'sub_total' => $unit_price * $product['products_quantity'],
                    'orders_id' => $product['orders_id'],
                    'delivery_mode' => $product['orders_products_extra_info_value'],
                );
                $products_list[$product['orders_products_id']] = $_product;
                $this->op_rebate += $product['op_rebate'];
                $this->setPaymentRestrictions($product['products_id']);
            } else {
                $_sub_product = array(
                    'id' => $product['products_id'],
                    'name' => $product['products_name'],
                    'quantity' => $product['products_quantity'],
                    'custom_products_type_id' => $product['custom_products_type_id'],
                    'orders_id' => $product['orders_id'],
                    'orders_products_id' => $product['orders_products_id'],
                    'delivery_mode' => $product['orders_products_extra_info_value'],
                );

                $sub_products_list[] = $_sub_product;
            }
        }
        $this->sub_products = $sub_products_list;
        $this->order_products = $products_list;
    }

    private function checkIsAdyenOpenLoopProduct($product)
    {
        // Detect if products is gift card, excluding DTU products (products_type is null or 1, delivery mode not 6)
        if ((empty($product['products_type']) || $product['products_type'] == '1') && $product['orders_products_extra_info_value'] != 6) {
            return true;
        }
        // mobile recharge (contain sub_products_id)
        elseif ($product['orders_products_extra_info_value'] == '6' && !empty($product['sub_products_id'])) {
            return true;
        }

        return false;
    }

    private function setPaymentRestrictions($pid)
    {
        $product_obj = new CheckoutProductCom();

        $restriction = $product_obj->getProductsPaymentMethodsRestrictions($pid);

        $this->restriction_mode = $restriction['restriction_mode'];
        $this->restriction_info = $restriction['restriction_info'];
    }

    private function setPaymentRestrictionsByIP($ip_iso2)
    {
        if (!empty($this->ip_restrict_pm['Allow'][$ip_iso2])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction([
                'restriction_mode' => $this->restriction_mode,
                'restriction_info' => $this->restriction_info
            ], ['restriction_mode' => 'Allow', 'restriction_info' => $this->ip_restrict_pm['Allow'][$ip_iso2]]);
        }

        if (!empty($this->ip_restrict_pm['Disallow'][$ip_iso2])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction([
                'restriction_mode' => $this->restriction_mode,
                'restriction_info' => $this->restriction_info
            ], ['restriction_mode' => 'Disallow', 'restriction_info' => $this->ip_restrict_pm['Disallow'][$ip_iso2]]);
        }
    }

    private function setGlobalPMRestriction()
    {
        if (!empty(Yii::app()->params['PAYMENT_METHOD_GLOBAL_BLACKLIST'])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction([
                'restriction_mode' => $this->restriction_mode,
                'restriction_info' => $this->restriction_info
            ], ['restriction_mode' => 'Disallow', 'restriction_info' => Yii::app()->params['PAYMENT_METHOD_GLOBAL_BLACKLIST']]);
        }
    }

    private function setCouponCodePaymentRestriction()
    {
        if (!empty($this->coupon_code)) {
            $criteria = new CDbCriteria;
            $criteria->select = 'restrict_to_payment_id';
            $criteria->condition = 'coupon_code = :coupon_code AND restrict_to_payment_id != ""';
            $criteria->params = array(
                ':coupon_code' => $this->coupon_code,
            );
            if ($result = CouponsBase::model()->find($criteria)) {
                list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction([
                    'restriction_mode' => $this->restriction_mode,
                    'restriction_info' => $this->restriction_info
                ], ['restriction_mode' => 'Allow', 'restriction_info' => $result->restrict_to_payment_id]);
            }
        }
    }

    private function getPaymentRestrictionsCode()
    {
        $payment_code_array = array();
        if (isset($this->restriction_info)) {
            $pip_mapper_id_array = (is_array($this->restriction_info) ? $this->restriction_info : explode(",", $this->restriction_info));
            foreach ($pip_mapper_id_array as $pip_mapper_id) {
                if ($pip_mapper_id == '0') {
                    continue;
                }
                $payment_code_obj = PipwavePaymentMapperBase::model()->findByAttributes(array('id' => $pip_mapper_id));
                if (isset($payment_code_obj->pipwave_payment_code)) {
                    $payment_code_array[] = $payment_code_obj->pipwave_payment_code;
                }
            }
        }
        return $payment_code_array;
    }

    private function isCryptoVoucher()
    {
        $crypto_currency_list = Yii::app()->params['CRYPTOCURRENCY_PRODUCT_LIST'] ?? [];

        foreach (array_merge($this->order_products, $this->sub_products) as $_product) {
            if (in_array($_product['id'], $crypto_currency_list)) {
                return true;
            }
        }

        return false;
    }

    private function getTaxExemptionCountry()
    {
        // Check orders_tax_customers table if customer already verified
        if ($custTax = OrdersTaxCustomersBase::model()->findByAttributes(array('customers_id' => Yii::app()->user->id, 'orders_tax_customers_status' => 1))) {
            // get the country iso-2 code from orders_tax_configureation
            if ($taxExemption = OrdersTaxConfigurationBase::model()->findByAttributes(
                array(
                    'orders_tax_id' => $custTax->orders_tax_id,
                    'business_tax_status' => 1,
                    'orders_tax_status' => "1",
                ),
                'start_datetime <= :start_datetime',
                array(':start_datetime' => date('Y-m-d H:i:s'))
            )) {
                return array($taxExemption->country_code => $taxExemption->business_tax_percentage);
            }
        }

        return false;
    }
}