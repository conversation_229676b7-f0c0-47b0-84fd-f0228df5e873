<?php

class CheckoutPGCom extends CApplicationComponent {

    private $oauth_obj,
            $merchant_secret,
            $customer_regional = array(),
            $customer_info = array(),
            $extra_info_array = array(),
            $ot_array = array(),
            $product_array = array(),
            $purchase_info_array = array(),
            $coupon_code,
            $trans_id, $order_id,
            $restriction_mode, $restriction_info,
            $error_message = array(),
            $ip_restrict_pm = array();

    public function __construct() {
        $this->oauth_obj = new OAuthClientCom();
        $this->oauth_obj->setScope('request_checkout');
        // $this->oauth_obj->setReusableToken($session_token_prefix = 'RC');
        $this->oauth_obj->setClientInfo(Yii::app()->params['OAUTH_CLIENT']['DEFAULT_CLIENT'], Yii::app()->params['OAUTH_DOMAIN']);
        $this->oauth_obj->enabledJsonMode();

        $this->merchant_secret = Yii::app()->params['PGS_MERCHANT_SECRET'];
        $this->ip_restrict_pm = Yii::app()->params['IP_RESTRICTION_PAYMENT_METHOD'];
    }

    public function generateAPIResponse($action, $result_array) {
        $return_array = array();
        $status = isset($result_array['status']) ? $result_array['status'] : 0;
        $cartFullDetails = isset($result_array['cartFullDetails']) ? $result_array['cartFullDetails'] : '';
        $redirect_url = isset($result_array['redirect_url']) ? $result_array['redirect_url'] : '';

        if (isset($result_array['order_id'])) {
            $this->setOrderID($result_array['order_id']);
        }

        switch ($action) {
            case 'request_update_customer_info':
                $this->loadCartInfo($cartFullDetails);

                $trans_id = $this->getTransID();
                $ts = time();
                $access_code = $this->getCustomerInfo('access_code');
                $amount = $this->getPurchaseInfo('amount', 0);
                $currency_code = $this->getPurchaseInfo('currency');
                $customer_info = $this->getCustomerInfo('billing_info', array());

                if ($customer_info !== array()) {
                    $return_array = array(
                        'trans_id' => $trans_id,
                        'amount' => $amount,
                        'currency_code' => $currency_code,
                        'customer_info' => $customer_info,
                        'timestamp' => $ts,
                        'tax_exampted_amount' => $this->getTaxExemptedAmount(),
                        'signature' => sha1($trans_id . $access_code . $currency_code . $amount . $ts . $this->merchant_secret)
                    );
                }
                break;
            case 'request_pg_url':
                $this->loadCartInfo($cartFullDetails);

                $return_array = $this->getRequestURLParams();
                $return_array['signature'] = sha1($this->oauth_obj->getAccessToken() . $return_array['amount'] . $return_array['currency_code'] . $return_array['timestamp'] . $this->merchant_secret);

                break;
            case 'pg_change_currency':
                $this->loadCartInfo($cartFullDetails);

                $trans_id = $this->getTransID();

                $return_array = $this->getRequestURLParams();
                $return_array['trans_id'] = $trans_id;
                $return_array['redirect_url'] = $redirect_url;
                $return_array['signature'] = sha1($trans_id . $return_array['amount'] . $return_array['currency_code'] . $this->merchant_secret);
                break;
            case 'pg_confirm_checkout':
                $this->loadCartInfo($cartFullDetails, $customer_flag = FALSE, $checkout_flag = TRUE, $product_flag = FALSE, $OT_flag = TRUE, $OT_error_flag = TRUE, $extra_info_flag = FALSE, $checkout_extra_info_flag = TRUE);

                $trans_id = $this->getTransID();
                $order_id = $this->getOrderID();
                $amount = $this->getPurchaseInfo('amount', 0);
                $currency_code = $this->getPurchaseInfo('currency');
                $surcharge = $this->getCustomerInfo('pg_surcharge', 0);

                $tax_info = $this->getCustomerInfo('pg_tax_info', array());
                $tax_amt = isset($tax_info['amount']) ? $tax_info['amount'] : 0;

                $pg_amount = Yii::app()->currency->advanceCurrencyConversion(($amount - $surcharge - $tax_amt), $currency_code, $currency_code);

                $return_array = array(
                    'trans_id' => $trans_id,
                    'amount' => $pg_amount,
                    'currency_code' => $currency_code,
                    'order_id' => $order_id,
                    'surcharge' => $surcharge, // this surcharge is the same one that get from PGS b4 convert to base currency.
                    'status' => $status,
                    'redirect_url' => $redirect_url,
                    'tax_exampted_amount' => $this->getTaxExemptedAmount(),
                    'signature' => sha1($trans_id . $order_id . $pg_amount . $currency_code . $status . $this->merchant_secret)
                );
                break;
            case 'pg_move_order':
                // if ($status !== 0 && is_numeric($status) && $status > 0) {
                $return_array = array(
                    'output' => 'plain',
                    'response' => 'OK'
                );
                // }
                break;
        }

        return $return_array;
    }

    private function getCouponCode() {
        return !is_null($this->coupon_code) ? $this->coupon_code : '';
    }

    private function getCustomerInfo($field, $default = '') {
        return isset($this->customer_info[$field]) ? $this->customer_info[$field] : $default;
    }

    private function getCustomerRegionalInfo($field, $default = '') {
        return isset($this->customer_regional[$field]) ? $this->customer_regional[$field] : $default;
    }

    private function getErrorMessage($key) {
        return isset($this->error_message[$key]) ? $this->error_message[$key] : '';
    }

    private function getExtraInfo() {
        return $this->extra_info_array;
    }

    private function getProducts() {
        return $this->product_array;
    }

    private function getPurchaseInfo($field, $default = '') {
        return isset($this->purchase_info_array[$field]) ? $this->purchase_info_array[$field] : $default;
    }

    private function getRequestURL($action) {
        return Yii::app()->params['PGS_URL'] . $action;
    }

    private function getTaxExemptedAmount() {
        if ($this->getPurchaseInfo('tax_exempted_items', array()) !== array()) {
            return $this->getPurchaseInfo('amount', 0);
        }

        return '';
    }

    public function getOrderID() {
        return !is_null($this->order_id) ? $this->order_id : 0;
    }

    private function getOTInfo() {
        return $this->ot_array;
    }

    public function getTransID() {
        return !is_null($this->trans_id) ? $this->trans_id : 0;
    }

    // wee siong
    // with user session
    public function getURL($cart_details_array) {
        $action = '/api/create-transaction';
        try {
            $checkout_details_array = array('cartFullDetails' => $cart_details_array);
            $response = $this->apiRequest($action, $checkout_details_array);

            if (isset($response['result_code']) && $response['result_code'] == 2000) {
                $payment_url = $response['full_url'];
                $tran_id = $response['tm_tid'];
            } else {
                throw new Exception('ERROR_CART_FAILED_TO_GET_PG_URL');
            }

            unset($request_param_array, $response);
        } catch (Exception $e) {
            $response = isset($response) ? $response : '';
            $this->reportError(array('response' => $response, 'error' => $e->getMessage()), '[Method] getURL');
            throw new Exception('ERROR_GENERAL__E13');
        }

        return array(
            'full_url' => $payment_url,
            'tm_tid' => $tran_id
        );
    }

    public static function isRPTypePG($payment_confirm_complete_days) {
        return $payment_confirm_complete_days > 0;
    }

    private function loadCartInfo($request_param_array, $customer_flag = TRUE, $checkout_flag = TRUE, $product_flag = TRUE, $OT_flag = TRUE, $OT_error_flag = TRUE, $extra_info_flag = TRUE, $checkout_extra_info_flag = TRUE) {
        $product_array = isset($request_param_array['request_result']['ot']['products']) ? $request_param_array['request_result']['ot']['products'] : array();
        $customer_info_array = isset($request_param_array['request_result']['ot']['customer']) ? $request_param_array['request_result']['ot']['customer'] : array();
        $checkout_info_array = isset($request_param_array['request_result']['ot']['checkout_info']) ? $request_param_array['request_result']['ot']['checkout_info'] : array();
        $checkout_extra_info_array = isset($request_param_array['request_result']['extra_info']) ? $request_param_array['request_result']['extra_info'] : array();

        $ot_info_array = isset($request_param_array['process_result']) ? $request_param_array['process_result'] : array();
        $ot_info_error_array = isset($request_param_array['collect_result']) ? $request_param_array['collect_result'] : array();
        $extra_info_array = isset($request_param_array['extra_info']) ? $request_param_array['extra_info'] : array();

        if ($customer_flag) {
            $this->processCustomerInfo($customer_info_array);
        }

        if ($checkout_flag) {
            $this->processCheckoutInfo($checkout_info_array);
        }

        if ($product_flag) {
            $this->processProduct($product_array);
        }

        if ($OT_error_flag) {
            $this->processOTErrorInfo($ot_info_error_array);
        }

        if ($OT_flag) {
            $this->processOTInfo($ot_info_array);
        }

        if ($extra_info_flag) {
            $this->processExtraInfo($extra_info_array);
        }

        if ($checkout_extra_info_flag) {
            $this->processCheckoutExtraInfo($checkout_extra_info_array);
        }
    }

    private function getPaymentRestrictionsCode() {
        $payment_code_array = array();
        if (isset($this->restriction_info)) {
            $pip_mapper_id_array = (is_array($this->restriction_info) ? $this->restriction_info : explode(",", $this->restriction_info));
            foreach ($pip_mapper_id_array as $pip_mapper_id) {
                if ($pip_mapper_id == '0') {
                    continue;
                }
                $payment_code_obj = PipwavePaymentMapperBase::model()->findByAttributes(array('id' => $pip_mapper_id));
                if (isset($payment_code_obj->pipwave_payment_code)) {
                    $payment_code_array[] = $payment_code_obj->pipwave_payment_code;
                } else {
                    continue;
                }
            }
        }
        return $payment_code_array;
    }

    private function getTaxExemptionCountry() {
        // Check orders_tax_customers table if customer already verified
        if ($custTax = OrdersTaxCustomersBase::model()->findByAttributes(array('customers_id' => $this->getCustomerInfo('id'), 'orders_tax_customers_status' => 1))) {
            // get the country iso-2 code from orders_tax_configureation
            if ($taxExemption = OrdersTaxConfigurationBase::model()->findByAttributes(
                array(
                    'orders_tax_id' => $custTax->orders_tax_id,
                    'business_tax_status' => 1,
                    'orders_tax_status' => "1",
                ),
                'start_datetime <= :start_datetime',
                array(':start_datetime' => date('Y-m-d H:i:s'))
            )) {
                return array($taxExemption->country_code => $taxExemption->business_tax_percentage);
            }
        }

        return false;
    }

    private function getCheckoutRequestParams(){
        $data = [
            'country_iso' => $this->getCustomerRegionalInfo('country_iso'),
            'languages_id' => $this->getCustomerRegionalInfo('languages_id'),
            'amount' => $this->getPurchaseInfo('amount', 0),
            'currency_code' => $this->getPurchaseInfo('currency'),
            'customer_id' => $this->getCustomerInfo('id'),
            'timestamp' => $this->getPurchaseInfo('ts'),
            'products_info' => $this->getProducts(),
            'additional_desc' => $this->getOTInfo(),
            'extra_total_info' => $this->getExtraInfo(),
            'customer_info' => $this->getCustomerInfo('billing_info', array()),
            'coupon_code' => $this->getCouponCode()
        ];

        // if payment restrictions set
        $ip_iso2 = Yii::app()->customerCom->getCountryByIP(getIPAddress());
        $this->setPaymentRestrictionsByIP($ip_iso2);
        $this->setCouponCodePaymentRestriction();

        if(!isset(Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID']) || !in_array($this->getCustomerInfo('id'), Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID'])){
            $this->setGlobalPMRestriction();
            $payment_code_array = $this->getPaymentRestrictionsCode();
        }
        else{
            $this->restriction_mode = null;
        }

        if (isset($this->restriction_mode) && $this->restriction_mode == 'Allow') {
            $prefered_payment = implode(",", $payment_code_array);
            $request_url_param_array['prefered_payment'] = (!empty($prefered_payment) ? $prefered_payment : '9999999');
        } elseif ($this->restriction_mode == 'Disallow') {
            $exclude_payment = implode(",", $payment_code_array);
            $request_url_param_array['exclude_payment'] = $exclude_payment;
        }

        // Tax exemtion on business customer purchase
        if ($taxOverride = $this->getTaxExemptionCountry()) {
            $request_url_param_array['tax_override'] = $taxOverride;
        }

        return $request_url_param_array;
    }

    private function getRequestURLParams() {
        $this->setPurchaseInfo('ts', time());

        $request_url_param_array = array(
            'country_iso' => $this->getCustomerRegionalInfo('country_iso'),
            'languages_id' => $this->getCustomerRegionalInfo('languages_id'),
            'amount' => $this->getPurchaseInfo('amount', 0),
            'currency_code' => $this->getPurchaseInfo('currency'),
            'customer_id' => $this->getCustomerInfo('id'),
            'timestamp' => $this->getPurchaseInfo('ts'),
//            'products_info' => $this->getProducts(),
            'additional_desc' => $this->getOTInfo(),
            'extra_total_info' => $this->getExtraInfo(),
            'customer_info' => $this->getCustomerInfo('billing_info', array()),
            'coupon_code' => $this->getCouponCode()
        );

        // if payment restrictions set
        $ip_iso2 = Yii::app()->customerCom->getCountryByIP(getIPAddress());
        $this->setPaymentRestrictionsByIP($ip_iso2);
        $this->setCouponCodePaymentRestriction();

        if(!isset(Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID']) || !in_array($this->getCustomerInfo('id'), Yii::app()->params['PAYMENT_METHOD_WHITELIST_USER_ID'])){
            $this->setGlobalPMRestriction();
            $payment_code_array = $this->getPaymentRestrictionsCode();
        }
        else{
            $this->restriction_mode = null;
        }

        if (isset($this->restriction_mode) && $this->restriction_mode == 'Allow') {
            $prefered_payment = implode(",", $payment_code_array);
            $request_url_param_array['prefered_payment'] = (!empty($prefered_payment) ? $prefered_payment : '9999999');
        } elseif ($this->restriction_mode == 'Disallow') {
            $exclude_payment = implode(",", $payment_code_array);
            $request_url_param_array['exclude_payment'] = $exclude_payment;
        }

        // Tax exemtion on business customer purchase
        if ($taxOverride = $this->getTaxExemptionCountry()) {
            $request_url_param_array['tax_override'] = $taxOverride;
        }

        return $request_url_param_array;
    }

    public function isUserTransIDValid($trans_id, $customer_id) {
        $return_bool = FALSE;

        if ($trans_id > 0) {
            $cart_info_obj = OgmCustomersBasket::model()->getCartInfoByTID($trans_id, $customer_id);

            if (!is_null($cart_info_obj)) {
                $return_bool = TRUE;
            }

            unset($cart_info_obj);
        }

        return $return_bool;
    }

    private function processCheckoutExtraInfo($checkout_extra_info_array) {
        foreach ($checkout_extra_info_array as $key => $value) {
            if (in_array($key, array('pg_surcharge', 'pg_tax_info'))) {
                $this->setCustomerInfo($key, $value);
            }
        }
    }

    private function processCheckoutInfo($checkout_info_array) {
        foreach ($checkout_info_array as $key => $value) {
            switch ($key) {
                case 'currency':
                case 'currency_value':
                case 'surcharge':
                case 'tax_exempted_items':
                    $this->setPurchaseInfo($key, $value);
                    break;
            }
        }
    }

    private function processCustomerInfo($customer_info_array) {
        foreach ($customer_info_array as $key => $value) {
            switch ($key) {
                case 'id':
                    $this->setCustomerInfo($key, $value);

                    // get customer billing info
                    $customer_data_array = Yii::app()->customerCom->getCustomerData2($with_address_book = 1, $value);

                    if ($customer_data_array !== array()) {
                        $country_array = CheckoutCountries::model()->getCountries($customer_data_array['address_book']['entry_country_id'], true);
                        $country_dialing_code_obj = CheckoutCountries::model()->getDialingInfo($customer_data_array['customer']['customers_country_dialing_code_id']);

                        $customer_info_array2 = array(
                            'email_address' => $customer_data_array['customer']['customers_email_address'],
                            'firstname' => $customer_data_array['customer']['customers_firstname'],
                            'lastname' => $customer_data_array['customer']['customers_lastname'],
                            'street_address' => $customer_data_array['address_book']['entry_street_address'],
                            'suburb' => $customer_data_array['address_book']['entry_suburb'],
                            'city' => $customer_data_array['address_book']['entry_city'],
                            'state' => $customer_data_array['address_book']['entry_state'],
                            'postcode' => $customer_data_array['address_book']['entry_postcode'],
                            'telephone' => $customer_data_array['customer']['customers_telephone'], //include country code
                            'country_ISO' => '', //worldpay use, customer address country ISO
                        );

                        if (isset($country_array['countries_iso_code_2'])) {
                            $customer_info_array2['country_ISO'] = $country_array['countries_iso_code_2'];
                        }

                        if (!is_null($country_dialing_code_obj)) {
                            $customer_info_array2['telephone'] = $country_dialing_code_obj->countries_international_dialing_code . $customer_info_array2['telephone'];
                        }

                        $this->setCustomerInfo('billing_info', $customer_info_array2);

                        unset($country_array, $country_dialing_code_obj);
                    }

                    break;
                case 'country':
                    $country_array = CheckoutCountries::model()->getCountries($value, true);

                    if (isset($country_array['countries_iso_code_2'])) {
                        $this->setCustomerRegionalInfo('country_iso', $country_array['countries_iso_code_2']);
                    }

                    unset($country_array);
                    break;
                case 'language':
                    $this->setCustomerRegionalInfo('languages_id', $value);
                    break;
            }
        }
    }

    private function processExtraInfo($extra_info_array) {
        foreach ($extra_info_array as $key => $value) {
            if ($value > 0 && ($key == 'optotal' || $key == 'ex_optotal')) {
                $this->setExtraInfo(Yii::t('checkoutModule.checkout', 'TEXT_TOTAL_REBATE'), $this->display_op($value));
                break;
            }
        }
    }

    private function processOTErrorInfo($ot_error_info_array) {
        foreach ($ot_error_info_array as $data) {
            if ($data['code'] == 'ot_coupon') {
                $this->setErrorMessage($data['code'], $data['error']);
            }
        }
    }

    private function processOTInfo($ot_info_array) {
        foreach ($ot_info_array as $data) {
            if ($data['storage_value'] > 0) {
                $set_ot_flag = TRUE;

                if ($data['code'] == 'ot_coupon') {
                    if ($this->getErrorMessage($data['code']) !== '') {
                        $set_ot_flag = FALSE;
                    } else {
                        $this->setCouponCode($data['input']['coupon_code']);
                    }
                } else if ($data['code'] == 'ot_total') {
                    $this->setPurchaseInfo('amount', $data['checkout_value']);
                }

                if ($set_ot_flag) {
                    $this->setOTInfo($data['display_title'], $data['storage_text']);
                }
            }
        }
    }

    private function processProduct($product_array) {
        foreach ($product_array as $data) {
            Yii::app()->currency->decimal_places = isset(Yii::app()->params['LISTING_PRICE'][$data['custom_products_type_child_id']]) ? Yii::app()->params['LISTING_PRICE'][$data['custom_products_type_child_id']]['decimal'] : NULL;
            $formatted_amount = Yii::app()->currency->format($data['final_price'] * $data['quantity'], false, $this->getPurchaseInfo('currency'));

            # SC Purchase will not consist quantity attribute in the display
            if ($data['custom_products_type_id'] == 3 && isset($data['custom_content']['orders_sc_currency_code'])) {
                $name = $data['name'] . ' (' . $data['custom_content']['orders_sc_currency_code'] . ')';
                $quantity = $data['quantity'];
            } else {
                $name = $data['name'];
                $quantity = $data['quantity'];
            }

            $region_cid_array = explode('_', $data['cat_id_path']);
            $region_cid = isset($region_cid_array[1]) ? $region_cid_array[1] : 0;

            $this->setProducts($name, $quantity, $formatted_amount, $data['id'], $data['final_price'], $region_cid);
            $this->setPaymentRestrictions($data['id']);
        }

        Yii::app()->currency->reset();
    }

    private function apiRequest($action, $checkout_details_array, $try = 0) {
        switch ($action) {
            case '/api/create-transaction':
                $this->resetAttributes();  // required to reset before re-generate API response to prevent duplicate entry.
                $request_path = $this->getRequestURL($action);
                $request_param_array = $this->generateAPIResponse('request_pg_url', $checkout_details_array);
                break;
            default:
                $request_path = $action;
                $request_param_array = $checkout_details_array;
                break;
        }
        $response = $this->oauth_obj->curl($request_path, json_encode(['data' => $request_param_array]),true);

        if ($try < 2 && isset($response['response']['error_code']) && in_array($response['response']['error_code'], array(1003, 1005))) { // 1003:Invalid/Unsupported Token, 1005:Access Token Expired
            if ($this->oauth_obj->getAccessTokenByRefreshToken() !== TRUE) {
                $this->oauth_obj->resetAcessToken();
            }

            $response = $this->apiRequest($action, $checkout_details_array, $try++);
        }

        return $response;
    }

    public function setCustomerInfo($key, $value) {
        if (!empty($key)) {
            $this->customer_info[$key] = $value;
        }
    }

    private function setCouponCode($code) {
        if (is_null($this->coupon_code)) {
            $this->coupon_code = $code;
        }
    }

    private function setErrorMessage($key, $message) {
        $this->error_message[$key] = $message;
    }

    private function setExtraInfo($display_title, $value) {
        $this->extra_info_array[] = array(
            'name' => $display_title,
            'value' => $value
        );
    }

    public function setCustomerRegionalInfo($key, $value) {
        if (!empty($key)) {
            $this->customer_regional[$key] = $value;
        }
    }

    public function setOrderID($id) {
        $this->order_id = $id;
    }

    private function setOTInfo($display_title, $formatted_amount, $active = '1') {
        $this->ot_array[] = array(
            'name' => $display_title,
            'value' => $formatted_amount,
            'active' => $active
        );
    }

    private function setProducts($name, $qty, $formatted_amount, $pid, $final_price, $region_cid) {
        $this->product_array[] = array(
            'name' => $name,
            'qty' => $qty,
            'amount' => $formatted_amount,
            'sku' => $pid,
            'raw_amount' => $final_price,
            'currency' => $this->getPurchaseInfo('currency'),
            'category' => $region_cid
        );
    }

    private function setPaymentRestrictions($pid) {
        $product_obj = new CheckoutProductCom();

        $restriction = $product_obj->getProductsPaymentMethodsRestrictions($pid);

        $this->restriction_mode = $restriction['restriction_mode'];
        $this->restriction_info = $restriction['restriction_info'];
    }

    private function setPaymentRestrictionsByIP($ip_iso2) {
        if (!empty($this->ip_restrict_pm['Allow'][$ip_iso2])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $this->restriction_mode, 'restriction_info' => $this->restriction_info], ['restriction_mode' => 'Allow', 'restriction_info' => $this->ip_restrict_pm['Allow'][$ip_iso2]]);
        }

        if (!empty($this->ip_restrict_pm['Disallow'][$ip_iso2])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $this->restriction_mode, 'restriction_info' => $this->restriction_info], ['restriction_mode' => 'Disallow', 'restriction_info' => $this->ip_restrict_pm['Disallow'][$ip_iso2]]);
        }
    }

    private function setGlobalPMRestriction() {
        if (!empty(Yii::app()->params['PAYMENT_METHOD_GLOBAL_BLACKLIST'])) {
            list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $this->restriction_mode, 'restriction_info' => $this->restriction_info], ['restriction_mode' => 'Disallow', 'restriction_info' => Yii::app()->params['PAYMENT_METHOD_GLOBAL_BLACKLIST']]);
        }
    }

    private function setCouponCodePaymentRestriction() {
        if (!empty($this->coupon_code)) {
            $criteria = new CDbCriteria;
            $criteria->select = 'restrict_to_payment_id';
            $criteria->condition = 'coupon_code = :coupon_code AND restrict_to_payment_id != ""';
            $criteria->params = array(
                ':coupon_code' => $this->coupon_code
            );
            if ($result = OrderCoupons::model()->find($criteria)) {
                list($this->restriction_mode, $this->restriction_info) = PipwavePaymentMapperBase::setPaymentRestriction(['restriction_mode' => $this->restriction_mode, 'restriction_info' => $this->restriction_info], ['restriction_mode' => 'Allow', 'restriction_info' => $result->restrict_to_payment_id]);
            }
        }
    }

    private function setPurchaseInfo($key, $value) {
        if (!empty($key)) {
            $this->purchase_info_array[$key] = $value;
        }
    }

    public function setTransID($id) {
        $this->trans_id = $id;
    }

    public function updateCustomerInfo($action, $checkout_details_array) {
        $response = '';
        $payment_url = '';

        try {
            $request_path = $this->getRequestURL('/action/updateCustomerInfo/');
            $request_param_array = $this->generateAPIResponse($action, $checkout_details_array);

            if ($request_param_array !== array()) {
                $response = $this->apiRequest($request_path, $request_param_array);

                if (isset($response['response']['status']) && $response['response']['status'] == 'Success') {
                    $verify_array = $response['response'] + $request_param_array;

                    if ($this->verifyAPISignature($action, $verify_array)) {
                        $payment_url = $response['response']['access_url'];
                    }
                } else {
                    throw new Exception('ERROR_API_REQUEST_FAILED');
                }
            } else {
                throw new Exception('ERROR_TO_GENERATE_API_RESPONSE');
            }
        } catch (Exception $e) {
            $this->reportError(array('response' => $response, 'error' => $e->getMessage()), '[Method] updateCustomerInfo');
            throw new Exception('ERROR_GENERAL__E14');
        }

        return $payment_url;
    }

    public function verifyAPISignature($action, $data) {
        $return_bool = FALSE;
        $calc_signature = '';

        try {
            switch ($action) {
                case 'request_pg_url':
                    $calc_signature = sha1($data['access_code'] . $data['trans_id'] . $data['access_url'] . $this->merchant_secret);
                    break;
                case 'request_update_customer_info':
                    $calc_signature = sha1($data['trans_id'] . $data['currency_code'] . $data['amount'] . $data['status'] . $data['access_url'] . $this->merchant_secret);
                    break;
                case 'pg_change_currency':
                    $calc_signature = sha1($data['action'] . $data['currency_code'] . $data['amount'] . $data['trans_id'] . $data['timestamp'] . $this->merchant_secret);
                    break;
                case 'pg_confirm_checkout':
                    $calc_signature = sha1($data['action'] . $data['payment_id'] . $data['currency_code'] . $data['amount'] . $data['trans_id'] . $data['timestamp'] . $data['surcharge'] . $this->merchant_secret);
                    break;
                case 'pg_move_order':
                    $calc_signature = sha1($data['action'] . $data['orders_id'] . $data['payment_order_status'] . $data['amount'] . $data['currency'] . $data['payment_title'] . $data['update_to_status'] . $data['timestamp'] . $this->merchant_secret);
                    break;
            }

            if (!empty($calc_signature) && $calc_signature === $data['signature']) {
                $return_bool = TRUE;
            } else {
                $this->reportError(array('action' => $action, 'data' => $data, 'calc_signature' => $calc_signature), '[Method] verifyAPISignature');
                throw new Exception('ERROR_GENERAL');
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return $return_bool;
    }

    private function display_op($total_op, $extend_left_content = '') {
        if (!empty($total_op)) {
            return '<div style="background: url(\'' . Yii::app()->frontPageCom->getMainUIStaticIconURL('token.png') . '\') no-repeat scroll 0px 0px transparent;height: 18px;width: 18px;padding-left: 18px;"><span style="line-height: 18px;">' . $total_op . $extend_left_content . '</span></div>';
        }

        return '';
    }

    private function reportError($error_str, $ext_subject = '') {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($error_str);
        echo "========================================================<BR>";
        echo "Request : " . getenv('REQUEST_URI');
        $debug_html = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - CheckoutPGCom Error - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $debug_html
            )
        );
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

    private function resetAttributes() {
        $this->customer_regional = array();
        $this->customer_info = array();
        $this->extra_info_array = array();
        $this->ot_array = array();
        $this->product_array = array();
        $this->restriction_mode = NULL;
        $this->restriction_info = NULL;
        $this->purchase_info_array = array();
        $this->coupon_code = NULL;
        $this->error_message = array();
    }

}

?>