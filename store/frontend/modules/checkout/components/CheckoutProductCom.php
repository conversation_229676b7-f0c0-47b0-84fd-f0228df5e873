<?php

class CheckoutProductCom extends FrontendProductCom
{
    public function __construct()
    {
        parent::__construct();
        $this->_init();
    }

    public function getProductsDtuExtraInfo($products_id = '', $language_id = null, $default_languages_id = null)
    {
        $products_id = $products_id != '' ? $products_id : $this->product_id;
        $language_id = notNull($language_id) ? $language_id : $this->language_id;
        $default_languages_id = notNull($default_languages_id) ? $default_languages_id : $this->default_language_id;

        return array(
            1 => CheckoutProductsDescription::model()->getProductsDtuExtraInfoOne(
                $products_id,
                $language_id,
                $default_languages_id
            ),
            2 => CheckoutProductsDescription::model()->getProductsDtuExtraInfoTwo(
                $products_id,
                $language_id,
                $default_languages_id
            ),
            3 => CheckoutProductsDescription::model()->getProductsDtuExtraInfoThree(
                $products_id,
                $language_id,
                $default_languages_id
            ),
        );
    }

    public function getProductInfo($field, $return_string = '')
    {
        if ($product = $this->getProductBundleInfoByID()) {
            switch ($field) {
                case 'custom_products_type_id':
                    if ($product['actual']['products_bundle'] != 'yes' && $product['actual']['products_bundle_dynamic'] != 'yes') {    // Single Product
                        $return_string = $product['actual']['custom_products_type_id'];
                    } else {
                        if ($product['bundle']) {
                            foreach ($product['bundle'] as $subproduct_type_row) {
                                $return_string = $subproduct_type_row['custom_products_type_id'];
                                break;
                            }
                        }
                    }
                    break;
                default:
                    if (isset($product['actual'][$field])) {
                        $return_string = $product['actual'][$field];
                    }
            }
        }

        return $return_string;
    }

    public function checkProductRegionPermission($product_id, $country_iso2, $ip_country)
    {
        $product_id = $product_id != '' ? $product_id : $this->product_id;

        $data = (new MsProductModel)->getCheckoutPermission($product_id, $country_iso2, $ip_country);
        
        return $data;
    }

    function tep_get_products_stock($product_info_array, $offset_out_of_stock_level = false)
    {
        if ($offset_out_of_stock_level) {
            return ($product_info_array['products_quantity'] - (int)$product_info_array['products_out_of_stock_level']);
        } else {
            return $product_info_array['products_quantity'];
        }
    }

}