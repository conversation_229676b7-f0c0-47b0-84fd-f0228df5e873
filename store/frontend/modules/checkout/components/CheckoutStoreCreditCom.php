<?php

class CheckoutStoreCreditCom extends StoreCreditCom 
{

    public static function get_customer_store_credit_currency_code($customer_id, $currency_code) {
        $return_str = '';

        $sc_info = (new self($customer_id))->_get_current_credits_balance(true);

        if (isset($sc_info['sc_currency_id'])) {
            $return_str = Yii::app()->currency->getCodeById($sc_info['sc_currency_id']);
        } else {
            $return_str = $currency_code;
        }
        
        return $return_str;
    }

}