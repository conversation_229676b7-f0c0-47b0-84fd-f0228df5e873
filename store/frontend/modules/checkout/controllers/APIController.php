<?php

class APIController extends FrontendController {

    private static $PG_ACTION_MAPPING = array(
        'ChangeCurrency' => 'pg_change_currency',
        'ConfirmCheckout' => 'pg_confirm_checkout',
        'IpnMoveOrder' => 'pg_move_order'
    );

    public function init($trigger_authcom = true) {
        # For Payment API, all the _init() below will be called by loadCustomerSettingByTID() when it has value
        // Yii::app()->frontPageCom->_init();
        // Yii::app()->customerCom->_init();
        Yii::app()->currency->_init();
    }

    public function actionIndex() {
        $response_array = array();
        $response_pg_array = array();
        $inputData = $this->grabIncomingData();

        $pg_obj = new CheckoutPGCom();
        $checkoutCom = new CheckoutCom();

        try {
            if (isset($inputData['action'])) {
                $action = self::$PG_ACTION_MAPPING[$inputData['action']];
                $payment_trans_id = isset($inputData['trans_id']) ? $inputData['trans_id'] : 0;

                if ($pg_obj->verifyAPISignature($action, $inputData)) {     // check signature by action
                    $pg_obj->setTransID($payment_trans_id);
                    switch ($action) {
                        case 'pg_move_order':
                            $response_array = $this->verifyingMovingOrder($checkoutCom, $inputData);
                            break;
                    }
                }

                $response_pg_array = $pg_obj->generateAPIResponse($action, $response_array);    // generate output by action
            }
        } catch (Exception $e) {
            $response_pg_array['error'] = $e->getMessage();
        }

        echo $this->displayOutput($response_pg_array);

        unset($inputData, $pg_obj, $checkoutCom, $response_pg_array);
    }

    public function actionIPN() {
        $response_array = array();
        $ipn = new IPNForm($_POST);

        //post back to PayPal system to validate	
        if (!$ipn->authenticate()) {
            //Check both the receiver_email and business ID fields match
            if ($ipn->validate_receiver_email()) {
                if ($ipn->txn_type() == 'masspay') {
                    $response_array = $ipn->processMasspay();
                }
            } else {
                $response_array = $ipn->dienice();
            }
        } else {
            $response_array = $ipn->dienice();
        }

        if (isset($_GET['json']) && $_GET['json'] == 1) {
            echo $this->displayOutput($response_array);
        } else if (isset($response_array['response']['header'])) {
            header($response_array['response']['header']);
        }
    }

    # Shasso cURL to here to rerun order genesis by customer ID and customer email
    public function actionPaymentEmailVerification() {
        $verification_obj = new PaymentVerificationForm();
        $verification_obj->setAttributes($_POST);

        if ($verification_obj->validate()) {
            //Check both the receiver_email and business ID fields match
            $verification_obj->processPaymentVerification();
        }
    }

    private function displayOutput($response_pg_array) {
        $return_str = '';

        if (isset($response_pg_array['output']) && $response_pg_array['output'] == 'plain') {
            $return_str = $response_pg_array['response'];
        } else {
            $return_str = CJSON::encode(
                            $response_pg_array
            );
        }

        return $return_str;
    }

    private function verifyingChangeSetting($checkoutCom, $checkout_details_array, $payment_trans_id) {
        switch ($checkout_details_array['status_code']) {
            case 'PROCESS_TO_SUCCESS_PAGE':
                $redirect_url = $checkout_details_array['redirect_url'];
                break;
            case 'PROCESS_TO_PAYMENT_PAGE':
                $process_change_currency_array = $checkoutCom->processPGUpdateSetting($payment_trans_id);

                switch ($process_change_currency_array['status_code']) {
                    case 'PROCESS_TO_PAYMENT_PAGE':
                        $redirect_url = '';
                        break;
                    default:
                        // redirect back to buy now page with error code
                        $redirect_url = $checkoutCom->getErrorReturnURL();
                        break;
                }
                break;
            default:
                // redirect back to buy now page with error code
                $redirect_url = $checkoutCom->getErrorReturnURL();
                break;
        }

        return array(
            'redirect_url' => $redirect_url,
        );
    }

    private function verifyingConfirmCheckout($checkoutCom, $checkout_details_array, $payment_trans_id) {
        $status = 'redirect';
        $order_id = 0;
        $redirect_url = '';

        switch ($checkout_details_array['status_code']) {
            case 'PROCESS_TO_SUCCESS_PAGE':
                $redirect_url = $checkout_details_array['redirect_url'];
                break;
            case 'PROCESS_TO_PAYMENT_PAGE':
                $checkout4pg_array = $checkoutCom->processPGCheckout($payment_trans_id);

                switch ($checkout4pg_array['status_code']) {
                    case 'PROCESS_TO_POST_PAYMENT_PAGE':
                        $redirect_url = $this->createAbsoluteUrl('/checkout/buyNow/postPayment', array('tid' => $payment_trans_id));
                        break;
                    case 'PROCESS_TO_SUCCESS_PAGE':
                        $status = 'proceed';
                        $order_id = $checkout4pg_array['order_id'];
                        $redirect_url = $checkout4pg_array['redirect_url'];
                        break;
                    default:
                        // redirect back to buy now page with error code
                        $redirect_url = $checkoutCom->getErrorReturnURL();
                        break;
                }

                unset($checkout4pg_array);
                break;
            default:
                // redirect back to buy now page with error code
                $redirect_url = $checkoutCom->getErrorReturnURL();
                break;
        }

        return array(
            'status' => $status,
            'order_id' => $order_id,
            'redirect_url' => $redirect_url,
        );
    }

    private function verifyingMovingOrder($checkoutCom, $pg_return_data) {
        $return_status = 0;

        $order_id = isset($pg_return_data['orders_id']) ? $pg_return_data['orders_id'] : 0;
        $update_to_status = isset($pg_return_data['update_to_status']) ? $pg_return_data['update_to_status'] : '';
        $payment_status = isset($pg_return_data['payment_status']) ? $pg_return_data['payment_status'] : '';
        $language_id = isset($pg_return_data['languages_id']) ? $pg_return_data['languages_id'] : 1;
        $paypal_ipn_id = isset($pg_return_data['paypal_ipn_id']) ? $pg_return_data['paypal_ipn_id'] : 0;

        $amount = isset($pg_return_data['amount']) ? $pg_return_data['amount'] : 0;
        $currency = isset($pg_return_data['currency']) ? $pg_return_data['currency'] : '';
        if (is_numeric($order_id) && $order_id > 0) {
            if ($payment_status == 'Success') {
                try {
                    $return_order_id = $checkoutCom->processOrder($order_id, $language_id, $amount, $currency, $update_to_status, $paypal_ipn_id);

                    if ($return_order_id > 0) {
                        $return_status = $return_order_id;
                    } else if ($return_order_id < 0) {
                        $return_status = 999;
                    }
                } catch (Exception $e) {
                    // checkoutCom will report this error
                }
            } else if ($payment_status == 'Review' || $payment_status == 'Echeck') { // Paypal Review & eCheck status
                $checkoutCom->reviewOrder($order_id, $language_id, $update_to_status, $paypal_ipn_id, $payment_status);
                $return_status = 1;
            }
        }

        return array(
            'status' => $return_status
        );
    }

    private function grabIncomingData() {
        $data = array();
        $inputData = '';
        // Check that exactly one method was used
        $methodsUsed = isset($_GET['access_token']) + isset($_POST['access_token']) + isset($inputData['access_token']);

        if ($methodsUsed > 1) {
            echo 'Only one method may be used to authenticate at a time (JSON, GET or POST)';
            exit;
        } else {
            $inputData = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : $_GET;

            if (empty($inputData) && isset($_SERVER['CONTENT_TYPE']) && $_SERVER['CONTENT_TYPE'] == 'application/json') {
                $inputData = file_get_contents("php://input");
            }
        }

        if (!empty($inputData)) {
            if ($this->isJson($inputData)) {
                $data = CJSON::decode($inputData);
            } else if ($this->isSerialize($inputData)) {
                $data = unserialize($inputData);
            } else if (is_object($inputData)) {
                $data = (array) $inputData;
            } else {
                $data = $inputData;
            }
        }

        return $data;
    }

    private function isSerialize($data) {
        return (@unserialize($data) !== FALSE) ? true : false;
    }

    private function isJson($data) {
        return (@CJSON::decode($data) !== NULL) ? true : false;
    }

    public static function reportError($subject, $response_data, $ext_subject = '') {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $response_data
            )
        );
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

}

?>
