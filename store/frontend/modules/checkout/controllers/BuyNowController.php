<?php

class BuyNowController extends FrontendController
{

    public function accessRules()
    {
        return array(
            array(
                'allow',
                'actions' => array(
                    'index',
                    'alertMsg'
                ),
                'users' => array('*'),
            ),
            array(
                'allow',
                'actions' => array(
                    'redirectToPG',
                    'success',
                    'confirmCheckout',
                    'confirmCheckoutSC',
                    'buy'
                ),
                'users' => array('@')
            ),
            array(
                'deny',
                // deny all users
                'users' => array('*'),
                'deniedCallback' => array(
                    $this,
                    'redirectToLogin'
                ),
            )
        );
    }

    public function init($trigger_authcom = true)
    {
        parent::init($trigger_authcom);
        AuthCom::flushProfilerSession();
    }

    public function filters()
    {
        return array(
            'accessControl',
            // perform access control for CRUD operations
        );
    }

    public function actionBuy()
    {
        $precheckout_form = new PreCheckoutForm();

        if (!isset($_REQUEST['confirmed'])) {
            if (isset($_REQUEST['pid'])) {
                $pid = $_REQUEST['pid'];
                Yii::app()->session->add('last_checkout_pid', $pid);
                $qty = (isset($_REQUEST['qty']) ? $_REQUEST['qty'] : 1);
                $cfm = (isset($_REQUEST['confirmed']) ? $_REQUEST['confirmed'] : 11);
                $productCom = new FrontendProductCom();
                $productCom->_init();
                $product = $productCom->getProductsListByID($pid, null, [], ['load_virtual_products' => true]);
                if (isset($product[0])) {
                    $product = $product[0];
                    $checkout_quantity_control = ProductsCheckoutSetting::getCheckoutQuantityLimit($pid);
                    $product['min_quantity'] = 1;
                    $product['max_quantity'] = 100;
                    if ($checkout_quantity_control) {
                        if (!empty($checkout_quantity_control->min_checkout_quantity)) {
                            $product['min_quantity'] = $checkout_quantity_control->min_checkout_quantity;
                        }
                        if (!empty($checkout_quantity_control->max_checkout_quantity)) {
                            $product['max_quantity'] = $checkout_quantity_control->max_checkout_quantity;
                        }
                    }

                    if ($product['min_quantity'] > $qty) {
                        $qty = $product['min_quantity'];
                    }

                    $precheckout_form->setAttributes([
                        "products_id" => $pid,
                        "cpt_id" => 2,
                        "delivery_mode" => $product["default_dm"],
                        "product_bundle" => $product["products_bundle"],
                        "confirmed" => $cfm,
                        "buyqty" => $qty
                    ]);

                    $products_id_list = [$pid];
                    $show_checkout_tnc = false;

                    if ($product['products_bundle'] == 'yes' || $product['products_bundle_dynamic'] == 'yes') {
                        $products_bundle_info = $productCom->getProductBundleInfoByID($pid);
                        if (isset($products_bundle_info['bundle'])) {
                            foreach ($products_bundle_info['bundle'] as $bundle_item) {
                                $products_id_list[] = $bundle_item['products_id'];
                            }
                        }
                    }
                    foreach ($products_id_list as $products_id) {
                        if (in_array($products_id, Yii::app()->params['ANTI_FRAUD_PRODUCTS_LIST'])) {
                            $show_checkout_tnc = true;
                        }
                    }
                } else {
                    throw new CHttpException(300);
                }
            } else {
                throw new CHttpException(400);
            }
        } else {
            $precheckout_form->setAttributes($_POST);
        }

        if (isset($_REQUEST['action']) && $_REQUEST['action'] == 'cfm_password') {
            $precheckout_form->scenario = $_REQUEST['action'];
            $precheckout_form->validate();
        }

        $precheckout_form->scenario = 'buy_code';


        $content = '';

        if (empty($precheckout_form->getErrors()) && $precheckout_form->validate()) {
            if ($error_msg = $this->getErrorMsg()) {
                $error = 1;
                $response_array['msg4top'] = $error_msg;
            }
            if ($precheckout_form->buyCode()) {
                switch ($precheckout_form->user_confirmed['skip_cfm_order']) {
                    case 1:
                        list($extra_info, $qty_on_change) = $precheckout_form->getExtraInfo();

                        $cartInfo = $precheckout_form->getBuyNowPreCheckoutDetails();
                        $cartInfo['extra_info']['debug_mode'] = PipwavePaymentMapperBase::model()->countByAttributes(array('site_id' => '0'));

                        if ($cartInfo['error_message']) {
                            $error = 1;
                            $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($cartInfo['error_message']);
                        } else {
                            $sc_content = '';

                            if ($precheckout_form->needPopupScChoice(Yii::app()->user->id, Yii::app()->session['currency'])) {
                                $showBtn = true;
                                $ip = getIPAddress();

                                $gst = Yii::app()->localizationCom->verifyGSTCondition($ip, Yii::app()->session['language_id']);

                                $info = $precheckout_form->getConfirmStoreCreditBox(Yii::app()->user->id, Yii::app()->session['currency']);

                                $params = getSafeQueryArray(array(), $_GET, false);

                                $sc_content = $this->renderPartial('convertSc', array(
                                    'url' => '/checkout/buyNow/buy',
                                    'params' => urlencode(json_encode($params)),
                                    'info' => $info,
                                    'showBtn' => $showBtn,
                                    'gstCode' => isset($gst['currency']) ? $gst['currency'] : ''
                                ), true, true);
                            }

                            return $this->render('buyNow', array(
                                // 'index' => $index,
                                'convertSc' => $sc_content,
                                'model' => $precheckout_form,
                                'products' => $product,
                                'price' => $cartInfo['ot']['subtotal']['display_value'],
                                'general_error' => '',
                                'dm_info' => array(
                                    'html' => $precheckout_form->get_cdkey_delivery_section_content(),
                                    'note' => ''
                                ),
                                'qty_info' => array(
                                    'select' => $precheckout_form->buyqty,
                                    'data' => $precheckout_form->getQtyData(),
                                    'onchange' => $qty_on_change
                                ),
                                'extra_info_str' => $extra_info,
                                'not_enough_sc' => ((isset($cartInfo['extra_info']['sc_checkout_only']) && $cartInfo['extra_info']['sc_checkout_only'] == 1) ? true : false),
                                'ot_content' => $this->renderPartial('ot', array('cartInfo' => $cartInfo), true, true),
                                'show_checkout_tnc' => $show_checkout_tnc
                            ));
                        }

                        Yii::app()->params['error_page_content'] = [
                            'message_title' => "Failed to create order",
                            'message' => $cartInfo['error_message']
                        ];

                        throw new CHttpException($cartInfo['error_code']);
                    case 2:
                    case 3:
                        $cartInfo = $precheckout_form->getBuyNowPreCheckoutDetails();
                        if ($cartInfo['error_message']) {
                            $error = 1;
                            $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($cartInfo['error_message']);
                        } else {
                            $response_array['line_total'] = $cartInfo['ot']['subtotal']['display_value'];
                            $content = $this->renderPartial('ot', array('cartInfo' => $cartInfo), true, true);
                        }
                        break;
                    default:
                        if ($precheckout_form->next_action == 'go_chkout') {
                            $response_array['redirect'] = $this->createUrl('buyNow/confirmCheckout');
                        }
                        $content = '';
                }
            } elseif (!Yii::app()->request->isPostRequest) {
                throw new CHttpException(300);
            }
        } elseif (!Yii::app()->request->isPostRequest) {
            throw new CHttpException(300);
        }


        if ($errors_array = $precheckout_form->getErrors()) {
            $error = 1;

            foreach ($errors_array as $key => $error_array) {
                $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($error_array[0]);
            }
            $response_array['err'] = $error;
        }
        $response_array['content'] = $content;
        $response_array['header_ext'] = ''; // $header_label;
        $response_array['pname'] = ''; // $pname;
        $response_array['cfm'] = implode('', $precheckout_form->user_confirmed);
        $response_array['next_is'] = $precheckout_form->next_action;

        if ($precheckout_form->next_action === 'ask_password' || $precheckout_form->next_action === 'ask_password2') {
            $response_array['title'] = Yii::t('checkoutModule.checkout', 'CONFIRM_LOGIN_PASSWORD_DESC');
        }

        if (!Yii::app()->request->isPostRequest) {
            $this->redirect('site/error');
        } else {
            echo CJSON::encode($response_array);
        }
    }

    private function getErrorMsg()
    {
        $message = '';
        $classCheckoutCartCom = new CheckoutCartCom();
        $cartEmpty = $classCheckoutCartCom->isCartEmpty();

        if (!$cartEmpty) {
            $errMsg = $classCheckoutCartCom->getAllErrorMessage();

            foreach ($errMsg as $msg) {
                if ($msg == 'ERROR_CART_PASSWORD_AUTHORIZATION_FAILED') { // Been to PGS but after change currency either SC or coupon can cover total purchase amount.
                    $message = '';
                    break;
                } else {
                    $message .= $msg . '<br/>';
                }
            }
        }

        return $message;
    }

    public function actionSuccess()
    {
        $orderId = 0;
        $gtm_str_trackTrans = $gtm_str_order_success = $gtm_str_purchase = '';
        Yii::app()->frontPageCom->clearRecentlyPurchasedCache();
        if (isset($_GET['oid']) && is_numeric($_GET['oid'])) {
            $orderId = $_GET['oid'];
            if (!CheckoutOrders::model()->orderExists($orderId)) {
                $this->redirect($this->createAbsoluteUrl('/'));
            }
        } else {
            $this->redirect($this->createAbsoluteUrl('/'));
        }

        // Skip success page if PGS return skip_tq = 1 (indicating from upload receipt page-pipwave and success page has been shown previously.)
        $skip_tq = (isset($_GET['skip_tq']) && is_numeric($_GET['oid'])) ? $_GET['skip_tq'] : '';
        if ($skip_tq == 1) {
            $this->redirect(Yii::app()->createUrl('account/orderDetail', array('orders_id' => $orderId)));
        }

        Yii::app()->getModule('order');

        $_cart = array(
            'order_id' => $orderId,
            'customer' => array(
                'language' => Yii::app()->session['language_id'],
            )
        );
        $order_obj = new OrderCom($_cart);

        # Detect whether product is SC, if SC clear CheckoutCustomersScCart then do redirection
        if ($order_obj->products[0]['custom_products_type_id'] == 3) {
            CheckoutCustomersScCart::model()->deleteByPk(Yii::app()->user->id);
            $this->redirect('/account/store-credit/index?tid=' . $orderId);
        }

        # Register the macro used in RulesTag Module
        Yii::app()->registerTagCom->registerMacro('{payment_methods_parent_id}', $order_obj->info['payment_method']['payment_methods_parent_id']);
        Yii::app()->registerTagCom->registerMacro('{customers_groups_id}', $order_obj->customer['customers_groups_id']);
        Yii::app()->registerTagCom->registerMacro('{subtotal}', (double)$order_obj->info['subtotal_value']);

        $model = new CheckoutSuccessForm();
        $model->order_products_id_array = $this->getMultiArrayByKey($order_obj->products, 'id');

        $sc = Yii::app()->customerCom->getCustomerSC(Yii::app()->user->id);
        $wor = Yii::app()->customerCom->getCustomerWor(Yii::app()->user->id);

        $gtmid = Yii::app()->params['gtm'];

        if ($gtmid && $this->isServiceEnabled('google')) {
            $gtm_array = $model->generateOrderGtmInfo($order_obj, "trackTrans");
            if ($gtm_array) {
                $gtm_str_trackTrans = json_encode($gtm_array, JSON_NUMERIC_CHECK);
            }

            $gtm_array = $model->generateOrderGtmInfo($order_obj, "order_success");
            if ($gtm_array) {
                $gtm_str_order_success = json_encode($gtm_array, JSON_NUMERIC_CHECK);
            }

            $gtm_array = $model->generateOrderGtmInfo($order_obj, "purchase");
            if ($gtm_array) {
                $gtm_str_purchase = json_encode($gtm_array, JSON_NUMERIC_CHECK);
            }
        }
        $model->generateOrderFBInfo($order_obj);
        $orderSummary = array(
            'date_purchased' => '-',
            'store_credit_balance' => $sc['val'],
            'wor_token_balance' => $wor['val']
        );

        if (isset($order_obj->info['date_purchased'])) {
            $orderSummary['date_purchased'] = date('d M Y (H:i)', strtotime($order_obj->info['date_purchased']));
        }

        $social_info = array(
            'fb' => '//facebook.com/sharer.php?u=http://offgamers.com&amp;t=I%20just%20bought%20' . stripcslashes(strip_tags($order_obj->products[0]['name'])) . '%20from%20www.OffGamers.com%21',
            'tw' => '//twitter.com/home?status=I%20just%20bought%20' . stripcslashes(strip_tags($order_obj->products[0]['name'])) . '%20from%20www.OffGamers.com%21',
            'gp' => '//plus.google.com/share?url=http://offgamers.com'
        );

        if (!$this->isServiceEnabled('google')) {
            unset($social_info['gp'], $social_info['fb']);
        }

        $subTotal = $order_obj->info['subtotal_value'];
        $product_id = $order_obj->products[0]['id'];

        // get the second layer category id 
        $cat_id = ProductsBase::getCPathByProductsId($product_id);

        $this->render('success', array(
            'orderSummary' => $orderSummary,
            'social_info' => $social_info,
            'model' => $model,
            'orderId' => $orderId,
            'gtm_str_trackTrans' => $gtm_str_trackTrans,
            'gtm_str_order_success' => $gtm_str_order_success,
            'gtm_str_purchase' => $gtm_str_purchase
        ));
    }

    public function actionRedirectToPG()
    {
        if (isset($_GET['oid']) && is_numeric($_GET['oid'])) {
            $orderId = $_GET['oid'];
            if (CheckoutOrders::model()->pendingOrderExists($orderId)) {
                $checkoutCom = new CheckoutModulesCom();
                $redirect_url = $checkoutCom->getRequestDetails($_GET['oid']);
                if (!$redirect_url) {
                    $this->redirect(Yii::app()->createUrl('account/orderDetail', array('orders_id' => $orderId)));
                }
                if(!isset($redirect_url['url'])){
                    $attachments = array(
                        array(
                            'color' => 'warning',
                            'text' => json_encode([
                                'input_domain' => $redirect_url,
                                'order_id' => $_GET['oid'],
                                'customers_id' => Yii::app()->user->id
                            ])
                        )
                    );
                    Yii::app()->slack->send('Fail to redirect to PG', $attachments, 'DEV_DEBUG');
                    $this->redirect(Yii::app()->createUrl('account/orderDetail', array('orders_id' => $orderId)));
                    Yii::app()->end();
                }
                return $this->renderPartial('processPg', ['url' => $redirect_url['url'], 'access_token' => $redirect_url['access_token'], 's' => $redirect_url['signature']]);
            } else {
                $this->redirect(Yii::app()->createUrl('account/orderDetail', array('orders_id' => $orderId)));
            }
        } else {
            $this->redirect($this->createAbsoluteUrl('/'));
        }
    }

    public function getMultiArrayByKey($input_array, $key)
    {
        $return_array = array();

        if (is_array($input_array)) {
            foreach ($input_array as $value) {
                if (array_key_exists($key, $value)) {
                    $return_array[] = $value[$key];
                }
            }
        }

        return $return_array;
    }

    public function actionIndex()
    {
        $error = 0;
        $response_array = array();
        $action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

        $precheckout_form = new PreCheckoutForm();
        $precheckout_form->scenario = $action;
        $precheckout_form->setAttributes($_REQUEST);
        list($index,) = $precheckout_form->getIndexAndType();

        if (notNull($action)) {
            switch ($action) {
                case 'buy_code':
                    $content = '';
                    $pid = $_REQUEST['pid'];
                    $productCom = new FrontendProductCom();
                    $productCom->_init();
                    $product = $productCom->getProductsListByID($pid, null, [], ['load_virtual_products' => true])[0];
                    $precheckout_form->delivery_mode = $product['default_dm'];
                    if ($precheckout_form->validate()) {
                        if ($error_msg = $this->getErrorMsg()) {
                            $error = 1;
                            $response_array['msg4top'] = $error_msg;
                        }

                        if ($precheckout_form->buyCode()) {
                            switch ($precheckout_form->user_confirmed['skip_cfm_order']) {
                                case 1:
                                    $side_dtu_tooltips_html = "<b>" . Yii::t('checkoutModule.checkout', 'TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT') . "</b><br>" . Yii::t(
                                            'checkoutModule.checkout',
                                            'TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION'
                                        ) . "<br><br>";
                                    $side_dtu_tooltips_html .= "<b>" . Yii::t('checkoutModule.checkout', 'TEXT_INFO_DELIVERY_DIRECT_TOP_UP') . "</b><br>" . Yii::t(
                                            'checkoutModule.checkout',
                                            'TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION'
                                        ) . "<br><br>";
                                    $delivery_methods_tooltips = '<div style="float:left;padding-top:2px;"><span class="hds3">' . Yii::t('checkoutModule.checkout', 'ENTRY_DELIVERY_METHOD') . ': </span></div>';
                                    $delivery_methods_tooltips .= '<div style="float: left; margin-left: 2px; cursor:pointer;">' . CHtml::image(Yii::app()->frontPageCom->getMainUIStaticIconURL('help-small.png'), '', array(
                                            'width' => '16',
                                            'height' => '16',
                                            'class' => 'dmTT',
                                            'id' => 'tt_precheckout_dm',
                                            'data-toggle' => 'tooltip',
                                            'data-placement' => 'bottom',
                                            'data-title' => $side_dtu_tooltips_html
                                        )) . '</div><div style="clear:both;"></div>';
                                    $delivery_methods_tooltips .= '<script>ogm_tooltip("tt_precheckout_dm")</script>';

                                    list($extra_info, $qty_on_change) = $precheckout_form->getExtraInfo();

                                    $cartInfo = $precheckout_form->getBuyNowPreCheckoutDetails();
                                    $cartInfo['extra_info']['debug_mode'] = PipwavePaymentMapperBase::model()->countByAttributes(array('site_id' => '0'));
                                    if ($cartInfo['error_message']) {
                                        $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($cartInfo['error_message']);
                                    } else {
                                        $content = $this->renderPartial('addCart', array(
                                            'index' => $index,
                                            'general_error' => '',
                                            //$precheckout_form->message
                                            'dm_info' => array(
                                                'html' => $precheckout_form->get_cdkey_delivery_section_content(),
                                                'tooltips' => $delivery_methods_tooltips,
                                                'note' => ''
                                            ),
                                            'qty_info' => array(
                                                'select' => $precheckout_form->buyqty,
                                                'data' => $precheckout_form->getQtyData(),
                                                'onchange' => $qty_on_change
                                            ),
                                            'extra_info_str' => $extra_info,
                                            'ot_content' => $this->renderPartial('ot', array('cartInfo' => $cartInfo), true, true),
                                        ), true);
                                    }

                                    break;
                                case 2:
                                case 3:
                                    $cartInfo = $precheckout_form->getBuyNowPreCheckoutDetails();

                                    if ($cartInfo['error_message']) {
                                        $error = 1;
                                        $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($cartInfo['error_message']);
                                    } else {
                                        $content = $this->renderPartial('ot', array('cartInfo' => $cartInfo), true, true);
                                    }
                                    break;
                                default:
                                    $content = '';
                            }
                        }
                    }

                    if ($errors_array = $precheckout_form->getErrors()) {
                        $error = 1;

                        foreach ($errors_array as $key => $error_array) {
                            if (in_array($key, array('buyqty'))) {
                                $response_array['msg4qty'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                            } elseif (in_array($key, array('game_info'))) {
                                $response_array['msg4dm'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                            } elseif (!isset($response_array['msg4top'])) {
                                $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                            }
                        }
                    }

                    $response_array['idx'] = $index;
                    $response_array['err'] = 1;
                    $response_array['content'] = $content;
                    $response_array['header_ext'] = ''; // $header_label;
                    $response_array['pname'] = ''; // $pname;
                    $response_array['cfm'] = implode('', $precheckout_form->user_confirmed);

                    break;
                case 'cfm_password':
                case 'cfm_password2':
                    $error = false;

                    $userCfmPassword = isset($_POST['cfm_password']) ? validateRequest($_POST['cfm_password'], 'string') : '';

                    $validate = $precheckout_form->checkPassword(Yii::app()->user->id, $userCfmPassword);

                    if ($validate == 'success') {
                        AuthCom::updateLifetimeCookies();
                        CheckoutCustomersInfo::model()->clearDormant(Yii::app()->user->id);
                        $response_array['redirect'] = $this->createUrl('buyNow/confirmCheckout'); //go to payment page
                    } else {
                        $response_array['msg'] = Yii::t('checkoutModule.checkout', 'ERROR_WRONG_PASSWORD');
                        $error = true;
                    }
                    $response_array['err'] = $error;

                    break;
                case 'cfm_qna':
                    $error = true;
                    #security token
                    $answer = isset($_POST['answer']) ? validateRequest($_POST['answer'], 'integer') : '';

                    if (Yii::app()->user->id && isset(Yii::app()->session['need_sc_usage_qna']) && Yii::app()->session['need_sc_usage_qna'] == '1') {
                        $validateAnswer = Yii::app()->customerCom->validateSecurityToken($answer, Yii::app()->user->id, Yii::app()->session['language']);

                        if ($validateAnswer['error'] == true) {
                            if (!$validateAnswer['error_message']) {
                                $validateAnswer['error_message'] = Yii::t('page_content', 'ERROR_GENERAL');
                            }

                            $status_icon = Yii::app()->frontPageCom->getMainUIStaticIconURL('error.gif');
                            $response_array['message'] = '<img src="' . $status_icon . '"> ' . $validateAnswer['error_message'];
                        } else {
                            CheckoutCustomersInfo::model()->clearDormant(Yii::app()->user->id);
                            AuthCom::updateLifetimeCookies();

                            //unset(Yii::app()->session['need_sc_usage_qna']);
                            $error = false;
                        }
                    }

                    $response_array['err'] = $error;
                    if (!$error && $index !== 0) {
                        $response_array['redirect'] = $this->createUrl('buyNow/confirmCheckout'); // redirect to checkout tep_href_link(FILENAME_CHECKOUT_SHIPPING, 'cfm_proceed=1', 'SSL');
                    }
                    break;
                case 'load_dtu_character_list':
                    $error_flag = 1;
                    $error_msg = Yii::t('checkoutModule.checkout', 'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT__1007-1');
                    $characters_array = array();
                    if (isset($_REQUEST['pid']) && (int)$_REQUEST['pid'] > 0 && (isset($_REQUEST['account']) && notNull($_REQUEST['account'])) || (isset($_REQUEST['server']) && notNull($_REQUEST['server']))) {
                        $pid = validateRequest($_REQUEST['pid'], 'integer');
                        $account = $_REQUEST['account'];
                        $server = $_REQUEST['server'];

                        $product_obj = new CheckoutProductCom();
                        $product_info_array = $product_obj->getProductBundleInfoByID($pid);
                        unset($product_obj);

                        if (isset($product_info_array['actual']['products_bundle']) && ($product_info_array['actual']['products_bundle'] == 'yes' || $product_info_array['actual']['products_bundle_dynamic'] == 'yes')) {
                            $pid = CheckoutPublishersProducts::model()->getBundleProductID($pid);
                        }

                        if ($publishers_row = CheckoutPublishersGames::model()->getGamesByProductID($pid)) {
                            $direct_topup_obj = new DirectTopupCom();

                            $games_acc_array = array();
                            $games_acc_array['account'] = $account;
                            $games_acc_array['server'] = $server;
                            $games_acc_array['game'] = $publishers_row['publishers_game'];
                            $games_acc_array['publishers_games_id'] = $publishers_row['publishers_games_id'];

                            $characters_array = $direct_topup_obj->get_character_list($publishers_row['publishers_id'], $games_acc_array, $curl_response_array);

                            if (isset($curl_response_array['result_code'])) {
                                if ($curl_response_array['result_code'] == '2000') {
                                    if (count($characters_array) > 0) {
                                        // got content
                                        $error_flag = 0;
                                    } else {
                                        $error_flag = 1;
                                        $error_msg = Yii::t('checkoutModule.checkout', 'ERROR_DTU_NO_CHARACTERS_IN_LIST__1008-1');
                                    }
                                } elseif ($curl_response_array['result_code'] == '1006') {
                                    $error_flag = 1;
                                    $error_msg = Yii::t('checkoutModule.checkout', 'ERROR_DTU_NO_CHARACTERS_IN_LIST__1008-2');
                                } elseif ($curl_response_array['result_code'] == '1007') {
                                    $error_flag = 1;
                                    $error_msg = Yii::t('checkoutModule.checkout', 'ERROR_DTU_NO_CHARACTERS_IN_LIST__1008-3');
                                }
                            }
                        }
                    }

                    if ($error_flag) {
                        $response_array['error_message'] = $error_msg;
                        $response_array['error'] = 1;
                    } else {
                        $response_array['character'] = $characters_array;
                        $response_array['error'] = 0;
                    }
                    break;
                case 'get_flow':
                    if (notEmpty($index) && $index !== 0) {
                        $cookieLifeTime = time() + 60 * 60 * 24;
                        $cookie = new CHttpCookie('pfv_idx', $index);
                        $cookie->domain = Yii::app()->params['COOKIE_DOMAIN'];
                        $cookie->expire = $cookieLifeTime;
                        $cookie->path = '/';
                        Yii::app()->request->cookies['pfv_idx'] = $cookie;
                    } elseif (isset(Yii::app()->request->cookies['pfv_idx'])) {
                        $index = htmlspecialchars(Yii::app()->request->cookies['pfv_idx']->value);
                    }
                    if (Yii::app()->request->getParam('targetId')) {
                        $targetId = validateRequest(Yii::app()->request->getParam('targetId'));
                        // passing products id for payment methods restrictions
                        list($pid, $dm, $pm) = explode(":", $targetId);
                        $productCom = new FrontendProductCom();
                        $productCom->_init();
                        $productInfo = $productCom->getProductsListByID($pid, null, [], ['load_virtual_products' => true]);
                        if (!empty($productInfo)) {
                            $product = $productInfo[0];
                            if ($dm == -1) {
                                $dm = $product['default_dm'];
                            }
                            $precheckout_form->products_id = $pid;
                            $precheckout_form->delivery_mode = $dm;
                            $precheckout_form->setPaymentRestrictionsChecking($pid);
                        } else {
                            $precheckout_form->products_id = null;
                        }
                    }
                    $next_is = $precheckout_form->pfvNextAccess();

                    if ($error) {
                        // $response_array['msg'] = $errMsg;
                    } else {
                        if ($next_is === 'ask_password') {
                            $response_array['header'] = Yii::t('checkoutModule.checkout', 'TEXT_LOGIN_PASSWORD');
                            $response_array['title'] = Yii::t('checkoutModule.checkout', 'CONFIRM_LOGIN_PASSWORD_DESC');
                        } elseif ($next_is === 'ask_password2') {
                            $response_array['title'] = Yii::t('checkoutModule.checkout', 'CONFIRM_LOGIN_PASSWORD_DESC');
                            $response_array['header'] = Yii::t('checkoutModule.checkout', 'TEXT_LOGIN_PASSWORD');
                        } elseif ($next_is === 'ask_qna') {
                            $response_array['header'] = '<div style="height: 7px"></div>'; //Yii::t('checkoutModule.checkout', 'TEXT_SECRET_QNA');
                            $response_array['content'] = $this->renderPartial('secretQna', array(
                                'index' => $index,
                                'requestType' => 'security_token_request'
                            ), true, true);
                            $response_array['width'] = '530px';
                        } elseif ($next_is === 'go_chkout') {
                            if (!$precheckout_form->validatePendingOrder()) {
                                $response_array = $precheckout_form->setResponseArrayError($response_array);
                            } else {
                                $response_array['redirect'] = $this->createUrl('buyNow/confirmCheckout'); //go to payment page
                            }
                        } elseif ($next_is === 'confirm_order') {
                            $params = ['pid' => $pid];

                            if(!empty($_REQUEST['sub_products_id'])) {
                                $params['sub_products_id'] = $_REQUEST['sub_products_id'];
                            }

                            if(!empty($_REQUEST['account'])) {
                                $params['account'] = $_REQUEST['account'];
                            }

                            if (!Yii::app()->request->isPostRequest) {
                                Yii::app()->controller->redirect($this->createUrl('buyNow/buy', $params));
                            }
                            $precheckout_form->buyqty = 1;
                            $precheckout_form->bypass_DTU_check = false;
                            $precheckout_form->cpt_id = 2;
                            if (!$precheckout_form->validate() || !$precheckout_form->validatePurchaseQty() || !$precheckout_form->validateProduct() || !$precheckout_form->buyCode() || !$precheckout_form->validatePendingOrder()) {
                                $response_array = $precheckout_form->setResponseArrayError($response_array);
                            } else {
                                Yii::app()->frontPageCom->registerRecentViewedProduct($targetId);
                                $response_array['redirect'] = $this->createUrl('buyNow/buy', $params);
                            }
                        }
                    }

                    $response_array['idx'] = $index;    // default : 0
                    $response_array['nxt'] = $next_is;
                    // $response_array['loginBoxTitle'] = Yii::t('myogm', 'TITLE_LOGIN_TO_ACCOUNT');

                    break;
            }
        }

        unset($precheckout_form);

        echo CJSON::encode($response_array);
    }

    // wee siong
    public function actionConfirmCheckout()
    {
        if (!is_null(Yii::app()->user->id)) {
            if (Yii::app()->customerCom->isPhoneSanctionCountry(true)) {
                return $this->redirect('/site/error');
            }
            $checkoutCom = new CheckoutCom();
            $process_checkout_details_array = $checkoutCom->processCheckout();

            switch ($process_checkout_details_array['status_code']) {
                case 'PROCESS_TO_SUCCESS_PAGE':
                    $redirect_url = $this->createUrl('buyNow/success', array('oid' => $process_checkout_details_array['order_id']));
                    break;
                case 'PROCESS_TO_PAYMENT_PAGE':
                    $redirect_url = $process_checkout_details_array['redirect_url'];
                    break;
                default:
                    // redirect back to buy now page with error code
                    $redirect_url = $checkoutCom->getErrorReturnURL();
                    break;
            }

            $this->redirect($redirect_url);
        }
    }

    public function actionConfirmCheckoutSC()
    {
        $redirect_url = '/account/store-credit/index?topup_error=1';
        $err_key = array();
        $currency_code = null;
        $customer_sc_cart_obj = null;

        if (!is_null(Yii::app()->user->id)) {
            $proceed_checkout = false;
            $custom_products_type_id = 3;

            if ($customer_sc_cart_obj = CheckoutCustomersScCart::model()->findByPK(Yii::app()->user->id)) {
                $currency_code = $customer_sc_cart_obj->customers_sc_cart_currency;

                $addToCart_array = array(
                    'products_id' => $customer_sc_cart_obj->products_id,
                    'buyqty' => $customer_sc_cart_obj->customers_sc_cart_quantity,
                    'orders_sc_currency_code' => $currency_code
                );

                $precheckout_form = new PreCheckoutForm();
                $precheckout_form->scenario = 'top_sc';
                $precheckout_form->setAttributes($addToCart_array);

                // validation
                if ($precheckout_form->validate()) {
                    if ($precheckout_form->buyCode($custom_products_type_id)) {
                        # success
                        $proceed_checkout = true;
                    }
                }

                if ($errors_array = $precheckout_form->getErrors()) {
                    foreach ($errors_array as $key => $error_array) {
                        $err_key[] = $error_array[0];
                    }
                }
            } else {
                // cart has data
                $err_key[] = 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__5';
            }

            if ($proceed_checkout) {
                $checkoutCom = new CheckoutCom();
                $process_checkout_details_array = $checkoutCom->processCheckout('sc_topup_change_currency', null, array('currency_code' => $currency_code));

                switch ($process_checkout_details_array['status_code']) {
                    case 'PROCESS_TO_PAYMENT_PAGE':
                        $redirect_url = $process_checkout_details_array['redirect_url'];
                        break;
                    default:
                        // redirect back to buy now page with error code
                        $redirect_url = $checkoutCom->getErrorReturnURL();
                        break;
                }
            }
        } else {
            $err_key[] = 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__6';
        }

        if ($err_key) {
            $err_array = array();

            foreach ($err_key as $key => $error) {
                $err_array[] = CheckoutCartCom::getErrorMessage($error);
            }

            if($customer_sc_cart_obj){
                $customer_sc_cart_obj->customers_sc_cart_last_message = implode('<br>', $err_array);
                $customer_sc_cart_obj->save();
            }
        }

        $this->redirect($redirect_url);
    }

}
