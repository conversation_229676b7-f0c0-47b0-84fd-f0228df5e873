<?php

return array(
    "BTN_PROCEED_TO_CHECKOUT" => "结账",
    'BTN_PROCEED_TO_CHECKOUT2' => '一键购物代金券结账',
    'BTN_PROCEED_TO_CHECKOUT3' => '充值商店信用',
    'BREADCRUMB_TITLE_CHECKOUT' => '结账',
    //
    'CONFIRM_LOGIN_PASSWORD_DESC' => "请输入您的登录密码以便继续使用购物代金券付款。",
    'CONFIRM_LOGIN_PASSWORD_DESC2' => "为安全起见，请输入登录密码。",
    //
    "ENTRY_ACCOUNT_NAME" => "帐户名称",
    "ENTRY_CHARACTER_NAME" => "角色名",
    "ENTRY_INFORMATION" => "资料",
    "ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT" => "仅适用于魔兽",
    "ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT" => "购买魔兽金币，请填写您的Battle.net账户。",
    "ENTRY_QTY" => "数量",
    "ENTRY_WOW_ACCOUNT_NAME" => "魔兽帐号名",
    'HEADER_BAR_CART' => '购物车',
    'HEADER_BAR_PAYMENT' => '付款',
    'HEADER_BAR_SUCCESS' => '&nbsp;&nbsp;&nbsp;&nbsp;订单成功&nbsp;&nbsp;&nbsp;',
    //
    "ERROR_CART_EXCEED_MAX_QTY" => "Purchase quantity exceeded.",
    "ERROR_CART_INVALID_CUSTOM_VALUE" => "ERROR_CART_INVALID_CUSTOM_VALUE",
    "ERROR_CART_INVALID_PRODUCT_PERMISSION" => "ERROR_CART_INVALID_PRODUCT_PERMISSION",
    "ERROR_CART_INVALID_PRODUCT_STATUS" => "ERROR_CART_INVALID_PRODUCT_STATUS",
    "ERROR_CART_INVALID_PRODUCT_TYPE" => "ERROR_CART_INVALID_PRODUCT_TYPE",
    "ERROR_CART_INVALID_SELLER" => "ERROR_CART_INVALID_SELLER",
    "ERROR_CART_INVALID_SELLER_STATUS" => "ERROR_CART_INVALID_SELLER_STATUS",
    "ERROR_CART_MIN_PURCHASE" => "ERROR_CART_MIN_PURCHASE",
    "ERROR_CART_NO_PERMISSION" => "Product is not allow to purchase in your region",
    "ERROR_CART_OUT_OF_STOCK_PURCHASE" => "ERROR_CART_OUT_OF_STOCK_PURCHASE",
    "ERROR_CART_PASSWORD_AUTHORIZATION_FAILED" => "Password Session Expired.",
    "ERROR_CART_STOCK_NOT_AVAILABLE" => "Quantity requested not available.",
    "ERROR_PLS_ENTER_ACCOUNT_NAME" => "请输入帐户名称",
    "ERROR_PLS_ENTER_CHAR_NAME" => "请输入游戏角色名",
    "ERROR_PLS_ENTER_PASSWORD" => "请输入密码",
    "ERROR_WRONG_PASSWORD" => "密码错误，请重试。",
    "ERROR_IS_DORMANT_ACCOUNT" => "超过90天没有登录帐户，需要使用安全令牌重新激活帐户才可以购物。",
    'ERROR_STOCK_NOT_AVAILABLE_GREATER_THAN_50' => '您要求购买的产品数量不足。如果要大宗购物，请联系 <a href="mailto:<EMAIL>"><EMAIL></a>',
    'ERROR_STOCK_NOT_AVAILABLE' => '要求购买的产品数量不足。查询到货，请联系 <a href=mailto:<EMAIL>><EMAIL></a>',
    'ERROR_CHECKOUT_QUANTITY_NOT_ALLOWED' => '请在提供的范围内提交正确的数量。',
    'ERROR_CHECKOUT_POPUP_TITLE' => '结账失败！',
    'ERROR_PRODUCT_IS_NOT_AVAILABLE' => '以下产品目前无法购买，请联系我们的技术支持：<a href="<EMAIL>" target="_blank"><EMAIL></a>',
    "ERROR_MAX_PENDING_ORDER" => "您有{{ORDER_NO}}个待处理订单，请检查先前订单的付款状态。",
    //
    "HEADER_CONFIRM_BILLING_INFO" => "请确认/完成您的帐单信息",
    "HEADER_STORE_CREDIT_USE" => "Store Credit",
    //
    "IMAGE_BUTTON_PAY_WITHOUT_SC" => "无购物代金券支付",
    "IMAGE_BUTTON_PAY_WITH_SC_CURRENCY" => "支付 %s (购物代金券)",
    "IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY" => "支付 %s",
    //
    "LOGIN_PASSWORD" => "密码",
    //
    "MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER" => "错误，请稍候再尝试。",
    "MSG_EXCEED_AVAILABLE_QUANTITY" => "Exceed available quantity",
    "MSG_INACTIVE_STATUS" => "这卖家处于非激活状态。",
    "MSG_LOWER_THAN_MINUMUM_PURCHASE" => "低于最低购买数额。",
    "MSG_OWN_LISTING" => "您的列表。",
    "MSG_PRODUCT_IS_EXPIRED" => "产品已逾期。",
    "MSG_PRODUCT_LOW_STOCK" => "Product is out of stock.",
    'MESSAGE_VALIDATION_DELIVERY_ADDRESS' => 'Please fill the delivery address form.',
    //
    "OPTION_BY_MAIL" => "邮寄",
    "OPTION_FACE_TO_FACE" => "面对面交易",
    "OPTION_OPEN_STORE" => "摆摊",
    "OPTION_PUT_IN_MY_ACCOUNT" => "放入我的帐户",
    //
    "TEXT_CHECKOUT_SUCCESS_HEADING_MSG" => "您的订单号为 <b>{SYS_ORDER_NUMBER}</b>",
    "TEXT_CHECKOUT_SUCCESS_HEADING_MSG2" => "恭喜您，下单成功。",
    "TEXT_CHECK_OFFGAMERS_POINT_LINK" => "查询 OP",
    "TEXT_CHECK_STORE_CREDIT_LINK" => "查询购物代金券余额",
    "TEXT_EXTRA_OP_MSG" => "Share to your friend to get extra OP",
    "TEXT_FROM" => "从",
    "TEXT_INVALID_PRODUCT_ID" => "Invalid product id",
    "TEXT_INVALID_STAGE" => "Invalid stage",
    "TEXT_LIVE_CHAT_LINK" => "即时聊天",
    "TEXT_LOGIN_PASSWORD" => "LOGIN PASSWORD",
    "TEXT_OP" => "OP是您购物完成后退给您的奖励积分，可以用于兑换代金券。<br/><br/><a href='http://kb.offgamers.com/zhcn/category/my-account/wor-token' target='_blank'>更多详情</a>",
    "TEXT_ORDER_SUMMARY" => "订单概要:",
    "TEXT_PASSWORD" => "密码",
    "TEXT_PLEASE_WAIT_WHILE_REDIRECT" => "重定向页面，请稍等",
    "TEXT_PRODUCT_NOT_IN_REGION" => "Product does not exist in your region",
    "TEXT_REDEEM_DISCOUNT_COUPON" => "折扣优惠券",
    "TEXT_SECRET_QNA" => "SECURITY TOKEN",
    'TEXT_STORE_CREDITS' => '购物代金券',
    "TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT" => "<p>如果您要使用代金券购货金额为您当前订单付款，您需要检查您购买的产品的币种是否与您的代金券购货金额的币种相符。</p><p>您想改将代金券购货金额的币种转变为购物车内产品的币种？</p>",
    "TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY" => "将购物代金券币种换成与网站相同的币种 (%s)。",
    "TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY" => "将网站币种换成与购物代金券相同的币种 (%s)。",
    "TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE" => "不使用购物代金券支付本次交易。",
    "TEXT_STORE_CREDIT_POPUP_DESCRIPTION" => "您的购物代金券的币种与您事先选择的网站币种不同。<b>请按照以下任一方法进行修改：-</b>",
    'TEXT_STORE_CREDIT_POPUP_CURRENCY_RESTRICTED' => '马来西亚客户只允许使用马币（MYR）结账。',
    "TEXT_TOTAL_REBATE" => "总共回扣",
    "TEXT_OUT_OF_STOCK" => "无可销售库存",
    "TEXT_LISTING_NO_LONGER_AVAILABLE" => "您所查看的销售列表已不存在。",
    "TEXT_MANUALLY_REDIRECT_DESC" => "如果页面没有自动跳转，<br>请点击:",
    'TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT' => 'OffGamers帐户',
    'TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION' => '在OffGamers购买订单历史记录里查看。<a href="http://kb.offgamers.com/zhcn/?p=155" target="new">更多详情</a>',
    'TEXT_INFO_DELIVERY_DIRECT_TOP_UP' => '直充服务',
    'TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION' => '请填写您帐户帐号，所购买的产品会直接充值到您的帐户内。<a href="http://kb.offgamers.com/zhcn/?p=145" target="new">更多详情</a>',
    'ENTRY_DELIVERY_METHOD' => '交易方式',
    'TEXT_ORDER_CHANGE_DEFAULT_CHARACTER' => '更改您的默认游戏角色',
    'TEXT_STORE_CREDIT_NOT_ENOUGH' => '没有足够的商店信用额',
    'TEXT_FULL_STORE_CREDIT_CHECKOUT' => '价值%value%的购物代金券将从你的余额中扣除。',

    'TEXT_INFO_DELIVERY_IN_STORE_PICKUP' => '经销铺购买',
    'TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION' => '请至Pudu OffGamers经销铺领取您的产品。',
    'ERROR_INVALID_TOP_UP_ACCOUNT' => '充值帐户资讯错误。',
    'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT' => '发布者的服务器当前已关闭。直充选项已关闭直到另行通知。会尽快恢复操作。',
    'ERROR_DTU_NO_CHARACTERS_IN_LIST' => '您所提供的帐户资讯沒有角色記錄。',
    
    'PULL_DOWN_DEFAULT' => '请选择',
    'TEXT_RETYPE' => '重新输入',

    'TEXT_COUNTRY_MALAYSIA' => '马来西亚',
);
?>