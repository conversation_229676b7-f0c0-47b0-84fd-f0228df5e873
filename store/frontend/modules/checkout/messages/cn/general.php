<?php

return array(
   "BTN_CONFIRM" => "确认",
    "BTN_REDEEM" => "赎回",
    "BTN_BUYOUT" => "全部买下",
    "BTN_REDIRECT_NOW" => "现在跳转",
    
    'DEBUG' => 'Debug',
    'DEBUG_MSG' => "\nOriginal Post\n{EMAIL_SEPARATOR}\n{DEBUG_STR}\n\n\nReconstructed Post\n{EMAIL_SEPARATOR}\n{RESPONSE_STR}\n\n",
    
    'EMAIL_SEPARATOR' => '------------------------------------------------------',
    'EMAIL_RECEIVER' => 'Email and Business ID config',
    'EMAIL_RECEIVER_MSG' => "Store Configuration Settings\nPrimary PayPal Email Address: {RECEIVER_EMAIL}\nBusiness ID: {BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal Configuration Settings\nPrimary PayPal Email Address: {PAYPAL_RECEIVER_EMAIL}\nBusiness ID: {PAYPAL_BUSINESS}\n\n",
    'EMAIL_RECEIVER_ERROR_MSG' => "Store Configuration Settings\nPrimary PayPal Email Address: {RECEIVER_EMAIL}\nBusiness ID: {BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal Configuration Settings\nPrimary PayPal Email Address: {PAYPAL_RECEIVER_EMAIL}\nBusiness ID: {PAYPAL_BUSINESS}\n\nPayPal Transaction ID: {PAYPAL_TXN_ID}\n\n",
    'EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT' => 'Payment Update #{STORE_PAYMENTS_ID}',
    'EMAIL_CONTACT' => "For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to {EMAIL_TO}. Thank you for shopping at {STORE_NAME}\n\n\n",
    
    "HEADER_RELATED_PRODUCTS" => "RELATED PRODUCTS",
    
    'PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION' => 'Send Mass Payment Notification Receive',
    'RESPONSE_VERIFIED' => 'Verified',
    'RESPONSE_MSG' => "Connection Type\n{EMAIL_SEPARATOR}\ncurl= {CURL_FLAG}, socket= {SOCKET}, domain= {DOMAIN}, port= {PORT} \n\nPayPal Response\n{EMAIL_SEPARATOR}\n{PAYPAL_RESPONSE} \n\n",
    'RESPONSE_INVALID' => 'Invalid PayPal Response',
    'RESPONSE_UNKNOWN' => 'Unknown Verfication',
    
    "TEXT_ORDER_RECEIVED" => "您的订单已经收到",
    "TEXT_ORDER_THANK_YOU" => "感谢您的惠顾",
    'TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED' => 'Please be informed that your payment has been successfully processed by our Payment Institution service provider, please allow 1-7 business working days for the funds to show up in your account.<br>Kindly contact <a href="mailto:<EMAIL>"><EMAIL></a> if you require further assistance. Thank you.',
    
    'UNKNOWN_TXN_TYPE' => 'Unknown Transaction Type',
    'UNKNOWN_TXN_TYPE_MSG' => "An unknown transaction ({TXN_TYPE}) occurred from {IP_ADDRESS}\nAre you running any tests?\n\n",
    'UNKNOWN_POST' => 'Unknown Post',
    'UNKNOWN_POST_MSG' => "An unknown POST from {IP_ADDRESS} was received.\nAre you running any tests?\n\n",
    
);
?>