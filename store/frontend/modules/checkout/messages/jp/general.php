<?php

return array(
    'BTN_CONFIRM' => "確認する",
    'BTN_REDEEM' => "引き換え",
    'BTN_BUYOUT' => "Buyout",
    'BTN_REDIRECT_NOW' => "Redirect Now",
    
    'DEBUG' => 'Debug',
    'DEBUG_MSG' => "\nOriginal Post\n{EMAIL_SEPARATOR}\n{DEBUG_STR}\n\n\nReconstructed Post\n{EMAIL_SEPARATOR}\n{RESPONSE_STR}\n\n",
    
    'EMAIL_SEPARATOR' => '------------------------------------------------------',
    'EMAIL_RECEIVER' => 'Eメール、ビジネスID設定',
    'EMAIL_RECEIVER_MSG' => "ストア環境設定\nメインのPayPal Eメールアドレス：{RECEIVER_EMAIL}\nビジネス ID：{BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal環境設定\nメインのPayPal Eメールアドレス：{PAYPAL_RECEIVER_EMAIL}\nビジネスID： {PAYPAL_BUSINESS}\n\n",
    'EMAIL_RECEIVER_ERROR_MSG' => "Store Configuration Settings\nPrimary PayPal Email Address: {RECEIVER_EMAIL}\nBusiness ID: {BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal Configuration Settings\nPrimary PayPal Email Address: {PAYPAL_RECEIVER_EMAIL}\nBusiness ID: {PAYPAL_BUSINESS}\n\nPayPal Transaction ID: {PAYPAL_TXN_ID}\n\n",
    'EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT' => '支払い更新 #{STORE_PAYMENTS_ID}',
    'EMAIL_CONTACT' => "ご不明点がある、もしくはサポートが必要な場合は、オンラインライブサポートサービスをご利用いただくか、{EMAIL_TO} 宛てにEメールでお問い合わせください。{STORE_NAME} で商品をご購入頂きありがとうございます。\n\n\n",
    
    'HEADER_RELATED_PRODUCTS' => "関連商品",
    
    'PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION' => '一括払いの通知を送信',
    'RESPONSE_VERIFIED' => 'Verified',
    'RESPONSE_MSG' => "Connection Type\n{EMAIL_SEPARATOR}\ncurl= {CURL_FLAG}, socket= {SOCKET}, domain= {DOMAIN}, port= {PORT} \n\nPayPal Response\n{EMAIL_SEPARATOR}\n{PAYPAL_RESPONSE} \n\n",
    'RESPONSE_INVALID' => 'Invalid PayPal Response',
    'RESPONSE_UNKNOWN' => 'Unknown Verfication',
    
    'TEXT_ORDER_RECEIVED' => "注文を受け付けました。",
    'TEXT_ORDER_THANK_YOU' => "商品をご購入頂きありがとうございます。",
    'TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED' => 'Please be informed that your payment has been successfully processed by our Payment Institution service provider, please allow 1-7 business working days for the funds to show up in your account.<br>Kindly contact <a href="mailto:<EMAIL>"><EMAIL></a> if you require further assistance. Thank you.',
    
    'UNKNOWN_TXN_TYPE' => 'Unknown Transaction Type',
    'UNKNOWN_TXN_TYPE_MSG' => "An unknown transaction ({TXN_TYPE}) occurred from {IP_ADDRESS}\nAre you running any tests?\n\n",
    'UNKNOWN_POST' => 'Unknown Post',
    'UNKNOWN_POST_MSG' => "An unknown POST from {IP_ADDRESS} was received.\nAre you running any tests?\n\n",
    
);
?>