<?php

return array(
    'BTN_CONFIRM' => "확인",
    'BTN_REDEEM' => "사용",
    'BTN_BUYOUT' => "Buyout",
    'BTN_REDIRECT_NOW' => "Redirect Now",
    
    'DEBUG' => 'Debug',
    'DEBUG_MSG' => "\nOriginal Post\n{EMAIL_SEPARATOR}\n{DEBUG_STR}\n\n\nReconstructed Post\n{EMAIL_SEPARATOR}\n{RESPONSE_STR}\n\n",
    
    'EMAIL_SEPARATOR' => '------------------------------------------------------',
    'EMAIL_RECEIVER' => 'ອີເມວ ແລະ ການຕັ້ງຄ່າ ID ທຸລະກິດ',
    'EMAIL_RECEIVER_MSG' => "스토어 구성 설정\n기본 PayPal 이메일 주소: {RECEIVER_EMAIL}\n비즈니스 ID: {BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal 구성 설정\n기본 PayPal 이메일 주소: {PAYPAL_RECEIVER_EMAIL}\n비즈니스 ID: {PAYPAL_BUSINESS}\n\n",
    'EMAIL_RECEIVER_ERROR_MSG' => "스토어 구성 설정\n기본 PayPal 이메일 주소: {RECEIVER_EMAIL}\n비즈니스 ID: {BUSINESS}\n{EMAIL_SEPARATOR}\nPayPal 구성 설정\n기본 PayPal 이메일 주소: {PAYPAL_RECEIVER_EMAIL}\n비즈니스 ID: {PAYPAL_BUSINESS}\n\nPayPal 거래 ID: {PAYPAL_TXN_ID}\n\n",
    'EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT' => '결제 업데이트 #{STORE_PAYMENTS_ID}',
    'EMAIL_CONTACT' => "궁금한 점이 있거나 도움이 필요하시면 온라인 실시간 지원(Online Live Support) 서비스를 이용하시거나 문의를 {EMAIL_TO}에 보내 주세요. {STORE_NAME}에서 구매해 주셔서 감사합니다\n\n\n",
    
    'HEADER_RELATED_PRODUCTS' => "관련 제품",
    
    'PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION' => '대량 결제 알림 수신 전송',
    'RESPONSE_VERIFIED' => 'Verified',
    'RESPONSE_MSG' => "Connection Type\n{EMAIL_SEPARATOR}\ncurl= {CURL_FLAG}, socket= {SOCKET}, domain= {DOMAIN}, port= {PORT} \n\nPayPal Response\n{EMAIL_SEPARATOR}\n{PAYPAL_RESPONSE} \n\n",
    'RESPONSE_INVALID' => 'Invalid PayPal Response',
    'RESPONSE_UNKNOWN' => 'Unknown Verfication',
    
    'TEXT_ORDER_RECEIVED' => "주문이 접수되었습니다.",
    'TEXT_ORDER_THANK_YOU' => "구매해 주셔서 감사합니다",
    'TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED' => 'Please be informed that your payment has been successfully processed by our Payment Institution service provider, please allow 1-7 business working days for the funds to show up in your account.<br>Kindly contact <a href="mailto:<EMAIL>"><EMAIL></a> if you require further assistance. Thank you.',
    
    'UNKNOWN_TXN_TYPE' => 'Unknown Transaction Type',
    'UNKNOWN_TXN_TYPE_MSG' => "An unknown transaction ({TXN_TYPE}) occurred from {IP_ADDRESS}\nAre you running any tests?\n\n",
    'UNKNOWN_POST' => 'Unknown Post',
    'UNKNOWN_POST_MSG' => "An unknown POST from {IP_ADDRESS} was received.\nAre you running any tests?\n\n",
    
);
?>