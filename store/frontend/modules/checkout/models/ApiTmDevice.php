<?php

/**
 * This is the model class for table "api_tm_device".
 *
 * The followings are the available columns in table 'api_tm_device':
 * @property string $api_tm_query_id
 * @property string $device_id
 * @property string $device_result
 * @property string $os
 * @property string $screen_res
 * @property string $local_time_offset
 * @property string $local_time_offset_range
 * @property integer $time_zone
 * @property integer $device_score
 * @property string $device_attributes
 * @property string $device_activities
 * @property string $device_assert_history
 * @property string $device_last_update
 * @property integer $device_worst_score
 * @property string $profiling_datetime
 * @property string $device_first_seen
 * @property string $device_last_event
 * @property string $device_match_result
 * @property string $offset_measure_time
 * @property string $os_anomaly
 * @property string $os_fonts_hash
 * @property string $os_fonts_number
 */
class ApiTmDevice extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmDevice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_device';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('time_zone, device_score, device_worst_score', 'numerical', 'integerOnly'=>true),
			array('api_tm_query_id, local_time_offset, local_time_offset_range, profiling_datetime, offset_measure_time', 'length', 'max'=>11),
			array('device_id, os_fonts_hash', 'length', 'max'=>36),
			array('device_result, device_match_result, os_fonts_number', 'length', 'max'=>10),
			array('os', 'length', 'max'=>32),
			array('screen_res', 'length', 'max'=>12),
			array('device_attributes, device_activities, device_assert_history', 'length', 'max'=>64),
			array('os_anomaly', 'length', 'max'=>3),
			array('device_last_update, device_first_seen, device_last_event', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, device_id, device_result, os, screen_res, local_time_offset, local_time_offset_range, time_zone, device_score, device_attributes, device_activities, device_assert_history, device_last_update, device_worst_score, profiling_datetime, device_first_seen, device_last_event, device_match_result, offset_measure_time, os_anomaly, os_fonts_hash, os_fonts_number', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'device_id' => 'Device',
			'device_result' => 'Device Result',
			'os' => 'Os',
			'screen_res' => 'Screen Res',
			'local_time_offset' => 'Local Time Offset',
			'local_time_offset_range' => 'Local Time Offset Range',
			'time_zone' => 'Time Zone',
			'device_score' => 'Device Score',
			'device_attributes' => 'Device Attributes',
			'device_activities' => 'Device Activities',
			'device_assert_history' => 'Device Assert History',
			'device_last_update' => 'Device Last Update',
			'device_worst_score' => 'Device Worst Score',
			'profiling_datetime' => 'Profiling Datetime',
			'device_first_seen' => 'Device First Seen',
			'device_last_event' => 'Device Last Event',
			'device_match_result' => 'Device Match Result',
			'offset_measure_time' => 'Offset Measure Time',
			'os_anomaly' => 'Os Anomaly',
			'os_fonts_hash' => 'Os Fonts Hash',
			'os_fonts_number' => 'Os Fonts Number',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('device_id',$this->device_id,true);
		$criteria->compare('device_result',$this->device_result,true);
		$criteria->compare('os',$this->os,true);
		$criteria->compare('screen_res',$this->screen_res,true);
		$criteria->compare('local_time_offset',$this->local_time_offset,true);
		$criteria->compare('local_time_offset_range',$this->local_time_offset_range,true);
		$criteria->compare('time_zone',$this->time_zone);
		$criteria->compare('device_score',$this->device_score);
		$criteria->compare('device_attributes',$this->device_attributes,true);
		$criteria->compare('device_activities',$this->device_activities,true);
		$criteria->compare('device_assert_history',$this->device_assert_history,true);
		$criteria->compare('device_last_update',$this->device_last_update,true);
		$criteria->compare('device_worst_score',$this->device_worst_score);
		$criteria->compare('profiling_datetime',$this->profiling_datetime,true);
		$criteria->compare('device_first_seen',$this->device_first_seen,true);
		$criteria->compare('device_last_event',$this->device_last_event,true);
		$criteria->compare('device_match_result',$this->device_match_result,true);
		$criteria->compare('offset_measure_time',$this->offset_measure_time,true);
		$criteria->compare('os_anomaly',$this->os_anomaly,true);
		$criteria->compare('os_fonts_hash',$this->os_fonts_hash,true);
		$criteria->compare('os_fonts_number',$this->os_fonts_number,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addTmRecord($attributes) {
		$this->isNewRecord = true;        
		foreach ($attributes as $key => $val) {
			$this->$key = $val;
		}
        
        $this->save(false);
	}
}