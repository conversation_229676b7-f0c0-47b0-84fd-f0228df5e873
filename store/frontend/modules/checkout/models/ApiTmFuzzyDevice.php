<?php

/**
 * This is the model class for table "api_tm_fuzzy_device".
 *
 * The followings are the available columns in table 'api_tm_fuzzy_device':
 * @property string $api_tm_query_id
 * @property string $fuzzy_device_id
 * @property string $fuzzy_device_first_seen
 * @property integer $fuzzy_device_id_confidence
 * @property string $fuzzy_device_last_event
 * @property string $fuzzy_device_last_update
 * @property string $fuzzy_device_match_result
 * @property string $fuzzy_device_result
 * @property integer $fuzzy_device_score
 * @property integer $fuzzy_device_worst_score
 */
class ApiTmFuzzyDevice extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmFuzzyDevice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_fuzzy_device';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('fuzzy_device_id_confidence, fuzzy_device_score, fuzzy_device_worst_score', 'numerical', 'integerOnly'=>true),
			array('api_tm_query_id', 'length', 'max'=>11),
			array('fuzzy_device_id', 'length', 'max'=>36),
			array('fuzzy_device_match_result, fuzzy_device_result', 'length', 'max'=>10),
			array('fuzzy_device_first_seen, fuzzy_device_last_event, fuzzy_device_last_update', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, fuzzy_device_id, fuzzy_device_first_seen, fuzzy_device_id_confidence, fuzzy_device_last_event, fuzzy_device_last_update, fuzzy_device_match_result, fuzzy_device_result, fuzzy_device_score, fuzzy_device_worst_score', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'fuzzy_device_id' => 'Fuzzy Device',
			'fuzzy_device_first_seen' => 'Fuzzy Device First Seen',
			'fuzzy_device_id_confidence' => 'Fuzzy Device Id Confidence',
			'fuzzy_device_last_event' => 'Fuzzy Device Last Event',
			'fuzzy_device_last_update' => 'Fuzzy Device Last Update',
			'fuzzy_device_match_result' => 'Fuzzy Device Match Result',
			'fuzzy_device_result' => 'Fuzzy Device Result',
			'fuzzy_device_score' => 'Fuzzy Device Score',
			'fuzzy_device_worst_score' => 'Fuzzy Device Worst Score',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('fuzzy_device_id',$this->fuzzy_device_id,true);
		$criteria->compare('fuzzy_device_first_seen',$this->fuzzy_device_first_seen,true);
		$criteria->compare('fuzzy_device_id_confidence',$this->fuzzy_device_id_confidence);
		$criteria->compare('fuzzy_device_last_event',$this->fuzzy_device_last_event,true);
		$criteria->compare('fuzzy_device_last_update',$this->fuzzy_device_last_update,true);
		$criteria->compare('fuzzy_device_match_result',$this->fuzzy_device_match_result,true);
		$criteria->compare('fuzzy_device_result',$this->fuzzy_device_result,true);
		$criteria->compare('fuzzy_device_score',$this->fuzzy_device_score);
		$criteria->compare('fuzzy_device_worst_score',$this->fuzzy_device_worst_score);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addTmRecord($attributes) {
		$this->isNewRecord = true;        
		foreach ($attributes as $key => $val) {
			$this->$key = $val;
		}
        
        $this->save(false);
	}
}