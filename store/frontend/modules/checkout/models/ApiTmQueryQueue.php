<?php

/**
 * This is the model class for table "api_tm_query_queue".
 *
 * The followings are the available columns in table 'api_tm_query_queue':
 * @property string $orders_id
 * @property string $transaction_type
 * @property string $query_string
 * @property string $extra_info
 * @property string $created_datetime
 */
class ApiTmQueryQueue extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmQueryQueue the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_query_queue';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('query_string, extra_info', 'required'),
			array('orders_id', 'length', 'max'=>11),
			array('transaction_type', 'length', 'max'=>2),
			array('created_datetime', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orders_id, transaction_type, query_string, extra_info, created_datetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orders_id' => 'Orders',
			'transaction_type' => 'Transaction Type',
			'query_string' => 'Query String',
			'extra_info' => 'Extra Info',
			'created_datetime' => 'Created Datetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orders_id',$this->orders_id,true);
		$criteria->compare('transaction_type',$this->transaction_type,true);
		$criteria->compare('query_string',$this->query_string,true);
		$criteria->compare('extra_info',$this->extra_info,true);
		$criteria->compare('created_datetime',$this->created_datetime,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function isTmQueryExist($order_id) {
        return self::model()->exists(array(
            'condition' => 'orders_id = :orders_id',
            'params' => array(':orders_id' => $order_id)
        ));
    }
    
    public function getApiTmQuery($orderId) {
        $criteria = new CDbCriteria();
        $criteria->select ='query_string, extra_info';
        $criteria->params = array(':orderId'=>$orderId);
		$criteria->condition = "orders_id = :orderId";
		$result = $this->model()->find($criteria);

        return $result;
    }
    
    public function insertApiTmQuery($save_array) {
        $this->orders_id = $save_array['orders_id'];
        $this->transaction_type = $save_array['transaction_type'];
        $this->query_string = $save_array['query_string'];
        $this->extra_info = $save_array['extra_info'];
        $this->created_datetime = date('Y-m-d H:i:s');
        $this->setIsNewRecord(true);
        return $this->save();
    }
    
    public function deleteApiTmQuerybyOneDayExpiry() {
		$criteria = new CDbCriteria;
		$criteria->condition = 'created_datetime < DATE_SUB(:now,INTERVAL 1 DAY)';
		$criteria->params = array ( ':now' => new CDbExpression('NOW()'));
		$this->model()->deleteAll($criteria);
	}
    
    public function deleteApiTmQuerybyOrderId($orderId) {
		$criteria = new CDbCriteria;
		$criteria->condition = 'orders_id = :orderId';
		$criteria->params = array ( ':orderId' => $orderId);
		$this->model()->deleteAll($criteria);
	}
}