<?php

/**
 * This is the model class for table "api_tm_risk_summary_n_policy".
 *
 * The followings are the available columns in table 'api_tm_risk_summary_n_policy':
 * @property string $api_tm_query_id
 * @property integer $summary_risk_score
 * @property integer $policy_score
 * @property string $reason_code
 */
class ApiTmRiskSummaryNPolicy extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmRiskSummaryNPolicy the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_risk_summary_n_policy';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('summary_risk_score, policy_score', 'numerical', 'integerOnly'=>true),
			array('api_tm_query_id', 'length', 'max'=>11),
			array('reason_code', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, summary_risk_score, policy_score, reason_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'summary_risk_score' => 'Summary Risk Score',
			'policy_score' => 'Policy Score',
			'reason_code' => 'Reason Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('summary_risk_score',$this->summary_risk_score);
		$criteria->compare('policy_score',$this->policy_score);
		$criteria->compare('reason_code',$this->reason_code,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addTmRecord($attributes) {
		$this->isNewRecord = true;        
		foreach ($attributes as $key => $val) {
			$this->$key = $val;
		}
        
        $this->save(false);
	}
}