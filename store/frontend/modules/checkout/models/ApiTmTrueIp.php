<?php

/**
 * This is the model class for table "api_tm_true_ip".
 *
 * The followings are the available columns in table 'api_tm_true_ip':
 * @property string $api_tm_query_id
 * @property string $true_ip
 * @property string $true_ip_activities
 * @property string $true_ip_assert_history
 * @property string $true_ip_attributes
 * @property string $true_ip_city
 * @property string $true_ip_geo
 * @property string $true_ip_isp
 * @property string $true_ip_last_update
 * @property string $true_ip_latitude
 * @property string $true_ip_longitude
 * @property integer $true_ip_worst_score
 * @property integer $true_ip_score
 * @property string $true_ip_first_seen
 * @property string $true_ip_last_event
 * @property string $true_ip_organization
 * @property string $true_ip_region
 * @property string $true_ip_result
 */
class ApiTmTrueIp extends MainModel
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ApiTmTrueIp the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'api_tm_true_ip';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('true_ip_worst_score, true_ip_score', 'numerical', 'integerOnly'=>true),
			array('api_tm_query_id', 'length', 'max'=>11),
            array('true_ip', 'length', 'max'=>128),
			array('true_ip_activities, true_ip_attributes', 'length', 'max'=>64),
			array('true_ip_assert_history', 'length', 'max'=>3),
			array('true_ip_city, true_ip_isp', 'length', 'max'=>50),
			array('true_ip_geo', 'length', 'max'=>2),
			array('true_ip_latitude, true_ip_longitude', 'length', 'max'=>9),
			array('true_ip_organization', 'length', 'max'=>100),
			array('true_ip_region', 'length', 'max'=>32),
			array('true_ip_result', 'length', 'max'=>10),
			array('true_ip_last_update, true_ip_first_seen, true_ip_last_event', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('api_tm_query_id, true_ip, true_ip_activities, true_ip_assert_history, true_ip_attributes, true_ip_city, true_ip_geo, true_ip_isp, true_ip_last_update, true_ip_latitude, true_ip_longitude, true_ip_worst_score, true_ip_score, true_ip_first_seen, true_ip_last_event, true_ip_organization, true_ip_region, true_ip_result', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'api_tm_query_id' => 'Api Tm Query',
			'true_ip' => 'True Ip',
			'true_ip_activities' => 'True Ip Activities',
			'true_ip_assert_history' => 'True Ip Assert History',
			'true_ip_attributes' => 'True Ip Attributes',
			'true_ip_city' => 'True Ip City',
			'true_ip_geo' => 'True Ip Geo',
			'true_ip_isp' => 'True Ip Isp',
			'true_ip_last_update' => 'True Ip Last Update',
			'true_ip_latitude' => 'True Ip Latitude',
			'true_ip_longitude' => 'True Ip Longitude',
			'true_ip_worst_score' => 'True Ip Worst Score',
			'true_ip_score' => 'True Ip Score',
			'true_ip_first_seen' => 'True Ip First Seen',
			'true_ip_last_event' => 'True Ip Last Event',
			'true_ip_organization' => 'True Ip Organization',
			'true_ip_region' => 'True Ip Region',
			'true_ip_result' => 'True Ip Result',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('api_tm_query_id',$this->api_tm_query_id,true);
		$criteria->compare('true_ip',$this->true_ip,true);
		$criteria->compare('true_ip_activities',$this->true_ip_activities,true);
		$criteria->compare('true_ip_assert_history',$this->true_ip_assert_history,true);
		$criteria->compare('true_ip_attributes',$this->true_ip_attributes,true);
		$criteria->compare('true_ip_city',$this->true_ip_city,true);
		$criteria->compare('true_ip_geo',$this->true_ip_geo,true);
		$criteria->compare('true_ip_isp',$this->true_ip_isp,true);
		$criteria->compare('true_ip_last_update',$this->true_ip_last_update,true);
		$criteria->compare('true_ip_latitude',$this->true_ip_latitude,true);
		$criteria->compare('true_ip_longitude',$this->true_ip_longitude,true);
		$criteria->compare('true_ip_worst_score',$this->true_ip_worst_score);
		$criteria->compare('true_ip_score',$this->true_ip_score);
		$criteria->compare('true_ip_first_seen',$this->true_ip_first_seen,true);
		$criteria->compare('true_ip_last_event',$this->true_ip_last_event,true);
		$criteria->compare('true_ip_organization',$this->true_ip_organization,true);
		$criteria->compare('true_ip_region',$this->true_ip_region,true);
		$criteria->compare('true_ip_result',$this->true_ip_result,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addTmRecord($attributes) {
		$this->isNewRecord = true;        
		foreach ($attributes as $key => $val) {
			$this->$key = $val;
		}
        
        $this->save(false);
	}
}