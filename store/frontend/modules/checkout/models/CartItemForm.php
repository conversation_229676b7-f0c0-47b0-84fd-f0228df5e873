<?php

class CartItemForm extends CFormModel 
{
    public  $products_id,
            $sub_products_id,
            $qty,
            $custom_key,
            $custom_value = array();
    
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('products_id, custom_key', 'required', 'message' => Yii::t('checkoutModule.checkout', 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER'), 'on' => array('add_cart', 'cfm_cart')),
            array('products_id, sub_products_id', 'numerical', 'integerOnly' => true, 'on' => array('add_cart', 'cfm_cart')),
            
            array('qty', 'filterQty', 'on' => array('add_cart', 'cfm_cart')),
            array('custom_key', 'filterCustomKey', 'on' => array('add_cart', 'cfm_cart')),
            array('custom_value', 'filterCustomValue', 'on' => array('add_cart', 'cfm_cart')),
            
            array('custom_value', 'validateMandatoryData', 'on' => 'cfm_cart'),
        );
    }
    
    public function filterQty($attribute, $params) {
        $this->qty = (int) $this->qty;
        
        if (!$this->qty) {
            $this->qty = 1;
        }
    }

    public function filterCustomKey($attribute, $params) {
        $this->custom_key = validateRequest($this->custom_key, 'integer');
        
        if (!in_array($this->custom_key, array(2,3))) {
            $this->custom_key = 2;
        }
    }
    
    private function validate_custom_value($value) {
        if (is_array($value)) {
            foreach ($value as $key => $val) {
                $value[$key] = $this->validate_custom_value($val);
            }
        } else {
            $value = htmlspecialchars($value);
        }
        
        return $value;
    }
    
    public function filterCustomValue($attribute, $params) {
        if ($this->custom_key == 3) {
            # store credit does not required delivery mode
        } else {
            if (isset($this->custom_value['delivery_mode']) && $this->custom_value['delivery_mode'] == 6) {
                foreach ($this->custom_value as $key => $value) {
                    $this->custom_value[$key] = $this->validate_custom_value($value); 
                }
            } else {
                $this->custom_value['delivery_mode'] = 5;   // default
            }
        }
    }

    public function validateMandatoryData() {
        
//        $this->addError('validateMandatoryData', 'ERROR_CHECKOUT_INVALID');
//        return false;
    }
    
    public function getDeliveryMethod() {
        $delivery_method = 5;

        if (isset($this->custom_value['delivery_mode'])) {
            $delivery_method = $this->custom_value['delivery_mode'];
        }

        return $delivery_method;
    }

    public function getProductCustomContent() {
        $_result = array();

        if (!empty($this->custom_value)) {
            switch ($this->custom_key) {
                default:
                    foreach ($this->custom_value as $key => $val) {
                        switch ($key) {
                            case 'delivery_mode':
                                $_result['delivery_info'][$key] = $val;
                                break;
                            case 'top_up_info':
                                $_result['top_up_info'] = $val;
                                break;
                            default:
                                $_result[$key] = $val;
                                break;
                        }
                    }

                    break;
            }
        }

        return $_result;
    }

    public function setSerializedData($data) {
        $this->setAttributes(json_decode($data, true), false);
    }
    public function getSerializedData() {
        return json_encode($this->getAttributes());
    }

    public function getUniqueKey() {
        return md5($this->products_id);
    }

}