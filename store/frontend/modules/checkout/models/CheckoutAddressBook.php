<?php

/**
 * This is the model class for table "address_book".
 *
 * The followings are the available columns in table 'address_book':
 * @property integer $address_book_id
 * @property integer $customers_id
 * @property string $entry_gender
 * @property string $entry_company
 * @property string $entry_firstname
 * @property string $entry_lastname
 * @property string $entry_street_address
 * @property string $entry_suburb
 * @property string $entry_postcode
 * @property string $entry_city
 * @property string $entry_state
 * @property integer $entry_country_id
 * @property integer $entry_zone_id
 */
class CheckoutAddressBook extends MainModel {

    /**
     * Returns the static model of the specified AR class.
     * @return AddressBook the static model class
     */
    public static function model($className=__CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'address_book';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('customers_id, entry_country_id, entry_zone_id', 'numerical', 'integerOnly' => true),
            array('entry_gender', 'length', 'max' => 1),
            array('entry_company, entry_firstname, entry_lastname, entry_suburb, entry_city, entry_state', 'length', 'max' => 32),
            array('entry_street_address', 'length', 'max' => 64),
            array('entry_postcode', 'length', 'max' => 10),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('address_book_id, customers_id, entry_gender, entry_company, entry_firstname, entry_lastname, entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'cartZones' => array(self::HAS_ONE, 'Zones', '', 'on' => 'cartZones.zone_id = address_book.entry_zone_id', 'joinType' => 'LEFT JOIN', 'alias' => 'cartZones'),
            'cartCountries' => array(self::HAS_ONE, 'Countries', '', 'on' => 'cartCountries.countries_id = address_book.entry_country_id', 'joinType' => 'LEFT JOIN', 'alias' => 'cartCountries'),
            'zones' => array(self::HAS_ONE, 'Zones', '', 'on' => 'zones.zone_id = t.entry_zone_id', 'joinType' => 'LEFT JOIN', 'alias' => 'zones'),
            'countries' => array(self::HAS_ONE, 'Countries', '', 'on' => 'countries.countries_id = t.entry_country_id', 'joinType' => 'LEFT JOIN', 'alias' => 'countries'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'address_book_id' => 'Address Book',
            'customers_id' => 'Customers',
            'entry_gender' => 'Entry Gender',
            'entry_company' => 'Entry Company',
            'entry_firstname' => 'Entry Firstname',
            'entry_lastname' => 'Entry Lastname',
            'entry_street_address' => 'Entry Street Address',
            'entry_suburb' => 'Entry Suburb',
            'entry_postcode' => 'Entry Postcode',
            'entry_city' => 'Entry City',
            'entry_state' => 'Entry State',
            'entry_country_id' => 'Entry Country',
            'entry_zone_id' => 'Entry Zone',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('address_book_id', $this->address_book_id);
        $criteria->compare('customers_id', $this->customers_id);
        $criteria->compare('entry_gender', $this->entry_gender, true);
        $criteria->compare('entry_company', $this->entry_company, true);
        $criteria->compare('entry_firstname', $this->entry_firstname, true);
        $criteria->compare('entry_lastname', $this->entry_lastname, true);
        $criteria->compare('entry_street_address', $this->entry_street_address, true);
        $criteria->compare('entry_suburb', $this->entry_suburb, true);
        $criteria->compare('entry_postcode', $this->entry_postcode, true);
        $criteria->compare('entry_city', $this->entry_city, true);
        $criteria->compare('entry_state', $this->entry_state, true);
        $criteria->compare('entry_country_id', $this->entry_country_id);
        $criteria->compare('entry_zone_id', $this->entry_zone_id);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }
    
    public function getCountryInfo($customer_id, $customer_address_id) {
        $criteria = new CDbCriteria;
        $criteria->select = 'entry_country_id, entry_zone_id';
        $criteria->condition = 'customers_id =:customers_id AND address_book_id = :address_book_id';
        $criteria->params = array(':customers_id' => $customer_id, ':address_book_id' => $customer_address_id);
        $country_info = $this->model()->find($criteria);
        return $country_info;
    }
    
    public function getEntryCityById($customer_default_address_id) {
        $criteria = new CDbCriteria;
        $criteria->select = 'entry_city';
        $criteria->condition = 'address_book_id = :customer_default_address_id';
        $criteria->params = array(':customer_default_address_id' => $customer_default_address_id);
        $result = $this->model()->find($criteria);
        
        return isset($result->entry_city) ? $result->entry_city : '';
    }
    
    public function getMoreInfo($address_book_id, $customerID = '') {
        $customerID = !empty($customerID) ? $customerID : Yii::app()->session['customer_id'];
        
        $criteria = new CDbCriteria;
        $criteria->select = 'entry_street_address, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id';
        $criteria->condition = 'customers_id = :customers_id AND address_book_id = :address_book_id';
        $criteria->params = array(
            ':customers_id' => $customerID, 
            ':address_book_id' => $address_book_id
        );
        
        return $this->model()->find($criteria);
    }
    
	public function countAddress($customerId, $billto){
		$criteria = new CDbCriteria;
		$criteria->condition = 'customers_id = :customerId AND address_book_id = :billto';
		$criteria->params = array(':customerId'=>$customerId, ':billto'=>$billto);
		$result = $this->model()->count($criteria);
		return $result;
	}
}