<?php

/**
 * This is the model class for table "analysis_pg_info".
 *
 * The followings are the available columns in table 'analysis_pg_info':
 * @property string $orders_id
 * @property string $info_key
 * @property string $info_value
 * @property string $created_at
 */
class CheckoutAnalysisPgInfo extends AnalysisPgInfoBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return AnalysisPgInfoBase the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getAllOrderInVerifyingStatus($pg_code, $payer_email, $customers_id) {
        $return_array = array();
        $email_info_key = 'payer_email';
        
        switch ($pg_code) {
            case 'paypalEC':
                $email_info_key = 'payer_email';
                break;
            case 'moneybookers':
                $email_info_key = 'moneybooker_email';
                break;
        }
        
        $sql = "    SELECT o.orders_id  
                    FROM " . $this->tableName() . " AS p 
                    INNER JOIN " . CheckoutOrders::model()->tableName() . " AS o 
                        ON (p.orders_id = o.orders_id) 
                    WHERE p.info_value = :payer_email
                        AND p.info_key = :payer_email_key
                        AND o.customers_id = :customers_id
                        AND o.orders_status = 7";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":payer_email", $payer_email, PDO::PARAM_STR);
        $command->bindParam(":payer_email_key", $email_info_key, PDO::PARAM_STR);
        $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);

        if ($result = $command->queryAll()) {
            $return_array = $result;
        }

        return $return_array;
    }

}
