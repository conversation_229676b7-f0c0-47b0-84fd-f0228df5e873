<?php

/**
 * This is the model class for table "categories".
 *
 * The followings are the available columns in table 'categories':
 * @property integer $categories_id
 * @property integer $parent_id
 * @property string $categories_parent_path
 * @property integer $sort_order
 * @property string $date_added
 * @property string $last_modified
 * @property integer $categories_pinned
 * @property integer $categories_status
 * @property integer $c2c_categories_status
 * @property string $categories_url_alias
 * @property integer $categories_auto_seo
 * @property string $categories_auto_seo_type
 * @property string $products_count
 * @property integer $custom_products_type_id
 * @property integer $custom_products_type_child_id
 * @property integer $categories_types_groups_id
 * @property integer $categories_buyback_main_cat
 */
class CheckoutCategories extends CategoriesBase
{
    public $total;
    
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function isInactivedGame($cPath_array) {
        $return_bool = false;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'COUNT(categories_id) AS total';
		$criteria->condition = 'categories_status = 0';
        $criteria->addInCondition('t.categories_id', $cPath_array);
        
        if ($result = $this->model()->find($criteria)) {
            if ($result->total > 0) {
                $return_bool = true;
            }
        }
        
		return $return_bool;
    }
    
}