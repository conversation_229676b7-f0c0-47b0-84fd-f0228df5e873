<?php

/**
 * This is the model class for table "categories_groups".
 *
 * The followings are the available columns in table 'categories_groups':
 * @property integer $linkid
 * @property integer $categories_id
 * @property integer $groups_id
 */
class CheckoutCategoriesGroups extends CategoriesGroupsBase
{
    public $total;
    
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CategoriesGroupsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function isIllegalCustomerGroupGame($cPath_array, $customers_groups_id) {
        $return_bool = true;
        
        $criteria = new CDbCriteria;
		$criteria->select = 'linkid';
		$criteria->condition = '(groups_id = :groups_id or groups_id=0) AND categories_id = :categories_id';
        $criteria->params = array(
            ':groups_id' => $customers_groups_id,
            ':categories_id' => end($cPath_array)
        );

        if ($result = $this->model()->find($criteria)) {
            $return_bool = false;
        }
        
		return $return_bool;
    }
}