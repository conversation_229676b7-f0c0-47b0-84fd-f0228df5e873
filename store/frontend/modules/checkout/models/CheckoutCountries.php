<?php

/**
 * This is the model class for table "countries".
 *
 * The followings are the available columns in table 'countries':
 * @property integer $countries_id
 * @property string $countries_name
 * @property string $countries_iso_code_2
 * @property string $countries_iso_code_3
 * @property integer $countries_currencies_id
 * @property string $countries_international_dialing_code
 * @property string $countries_website_domain
 * @property integer $address_format_id
 * @property integer $maxmind_support
 * @property string $aft_risk_type
 * @property integer $countries_display
 */
class CheckoutCountries extends Countries
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return Countries the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}