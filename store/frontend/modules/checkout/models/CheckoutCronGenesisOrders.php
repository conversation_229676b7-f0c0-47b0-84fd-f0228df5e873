<?php

/**
 * This is the model class for table "cron_genesis_orders".
 *
 * The followings are the available columns in table 'cron_genesis_orders':
 * @property integer $orders_id
 * @property integer $flag
 * @property integer $re_run
 * @property string $last_modified
 */
class CheckoutCronGenesisOrders extends CronGenesisOrdersBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CronGenesisOrdersBase the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function insertReRunGenesis($orders_id) {
        $data_array = array(
            'orders_id' => $orders_id,
            'flag' => 0,
            're_run' => 1,
            'last_modified' => new CDbExpression('NOW()')
        );
        
        return $this->saveNewRecord($data_array);
    }
}
