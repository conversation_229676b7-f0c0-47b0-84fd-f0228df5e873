<?php

/**
 * This is the model class for table "customers_info".
 *
 * The followings are the available columns in table 'customers_info':
 * @property integer $customers_info_id
 * @property string $customers_info_date_of_last_logon
 * @property integer $customer_info_account_dormant
 * @property integer $customers_info_number_of_logons
 * @property string $customers_info_date_account_created
 * @property string $customers_info_account_created_ip
 * @property integer $customers_info_account_created_country
 * @property integer $customers_info_account_created_from
 * @property string $customers_info_date_account_last_modified
 * @property string $customers_info_changes_made
 * @property integer $global_product_notifications
 * @property integer $customer_info_zone_of_last_logon
 * @property integer $customer_info_selected_country
 * @property integer $customer_info_selected_language_id
 */
class CheckoutCustomersInfo extends CustomersInfoBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return CheckoutCustomersInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getDormantStatus($customerId){
		$criteria = new CDbCriteria;
		$criteria->select = 'customer_info_account_dormant';
		$criteria->condition = 'customers_info_id =:customerId';
		$criteria->params = array(':customerId'=>$customerId);
		$result = $this->model()->find($criteria);
		return isset($result->customer_info_account_dormant) ? $result->customer_info_account_dormant : '';
	}
	
	public function clearDormant($customerId){
		$attributes =array('customer_info_account_dormant' => 0);
		$condition = 'customers_info_id =:customerId';
		$params =array(':customerId'=>$customerId);		
		$this->model()->updateAll($attributes,$condition,$params);
        if (Yii::app()->session->contains('need_sc_usage_qna')) {
            Yii::app()->session->offsetUnset('need_sc_usage_qna');
        }
	}
}