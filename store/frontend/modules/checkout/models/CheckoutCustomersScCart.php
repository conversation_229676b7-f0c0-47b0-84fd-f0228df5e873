<?php

/**
 * This is the model class for table "customers_sc_cart".
 *
 * The followings are the available columns in table 'customers_sc_cart':
 * @property integer $customers_id
 * @property integer $products_id
 * @property integer $customers_sc_cart_quantity
 * @property string $customers_sc_cart_date_added
 */
class CheckoutCustomersScCart extends CustomersScCartBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CustomersScCartBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'customers_sc_cart';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('customers_id, products_id, customers_sc_cart_quantity', 'numerical', 'integerOnly'=>true),
			array('customers_sc_cart_date_added', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('customers_id, products_id, customers_sc_cart_quantity, customers_sc_cart_date_added', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'customers_id' => 'Customers',
			'products_id' => 'Products',
			'customers_sc_cart_quantity' => 'Customers Sc Cart Quantity',
			'customers_sc_cart_date_added' => 'Customers Sc Cart Date Added',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('customers_id',$this->customers_id);
		$criteria->compare('products_id',$this->products_id);
		$criteria->compare('customers_sc_cart_quantity',$this->customers_sc_cart_quantity);
		$criteria->compare('customers_sc_cart_date_added',$this->customers_sc_cart_date_added,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}