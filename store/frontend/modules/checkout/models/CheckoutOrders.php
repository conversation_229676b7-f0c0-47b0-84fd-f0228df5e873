<?php

/**
 * This is the model class for table "orders".
 *
 * The followings are the available columns in table 'orders':
 * @property integer $orders_id
 * @property integer $customers_id
 * @property string $customers_name
 * @property string $customers_company
 * @property string $customers_street_address
 * @property string $customers_suburb
 * @property string $customers_city
 * @property string $customers_postcode
 * @property string $customers_state
 * @property string $customers_country
 * @property string $customers_telephone_country
 * @property string $customers_country_international_dialing_code
 * @property string $customers_telephone
 * @property string $customers_email_address
 * @property integer $customers_address_format_id
 * @property integer $customers_groups_id
 * @property string $delivery_name
 * @property string $delivery_company
 * @property string $delivery_street_address
 * @property string $delivery_suburb
 * @property string $delivery_city
 * @property string $delivery_postcode
 * @property string $delivery_state
 * @property string $delivery_country
 * @property integer $delivery_address_format_id
 * @property string $billing_name
 * @property string $billing_company
 * @property string $billing_street_address
 * @property string $billing_suburb
 * @property string $billing_city
 * @property string $billing_postcode
 * @property string $billing_state
 * @property string $billing_country
 * @property integer $billing_address_format_id
 * @property string $payment_method
 * @property integer $payment_methods_parent_id
 * @property integer $payment_methods_id
 * @property string $cc_type
 * @property string $cc_owner
 * @property string $cc_number
 * @property string $cc_expires
 * @property string $last_modified
 * @property string $date_purchased
 * @property integer $orders_status
 * @property integer $orders_cb_status
 * @property string $orders_date_finished
 * @property string $currency
 * @property string $remote_addr
 * @property string $currency_value
 * @property integer $paypal_ipn_id
 * @property string $pm_2CO_cc_owner_firstname
 * @property string $pm_2CO_cc_owner_lastname
 * @property integer $orders_locked_by
 * @property string $orders_locked_from_ip
 * @property string $orders_locked_datetime
 * @property string $orders_follow_up_datetime
 * @property string $orders_tag_ids
 * @property integer $orders_read_mode
 * @property integer $orders_aft_executed
 * @property integer $orders_rebated
 */
class CheckoutOrders extends OrdersBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function orderExists($oId) {
        $criteria = new CDbCriteria();

        $criteria->condition = 'orders_id = :orderId AND customers_id = :customerId';
        $criteria->params = array(':orderId' => $oId, ':customerId' => Yii::app()->user->id);
        $result = $this->model()->exists($criteria);

        return $result;
    }

    public function pendingOrderExists($oId) {
        $criteria = new CDbCriteria();

        $criteria->condition = 'orders_id = :orderId AND customers_id = :customerId AND orders_status = 1 AND payment_methods_id = 0';
        $criteria->params = array(':orderId' => $oId, ':customerId' => Yii::app()->user->id);
        $result = $this->model()->exists($criteria);

        return $result;
    }

    public function getPaymentMethodID($oid) {
        $return_int = 0;
        
        $sql = "SELECT payment_methods_id
                FROM " . $this->tableName() . " AS p
                WHERE orders_id = :orders_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":orders_id", $oid, PDO::PARAM_INT);
        
        if ($dataset = $command->queryScalar()) {
            $return_int = $dataset;
        }
        
		return $return_int;
    }
    
    public function getRelatedProducts($exclude_products_ids, $customers_id, $total_items){
        $return_array = array();
        $last_week_date = date('Y-m-d', strtotime('-7 days'));
        $query_total_items = $total_items*2;
        
        $sql = "SELECT DISTINCT op.products_id, COUNT(o.customers_id) AS total_purchase
                    FROM " . $this->tableName() . " AS o 
                        INNER JOIN " . CheckoutOrdersProducts::model()->tableName() . " AS op ON ( o.orders_id = op.orders_id AND op.parent_orders_products_id = 0 AND op.custom_products_type_id = 2 )
                    WHERE o.date_purchased > :last_week_date
                        AND o.orders_status IN (2, 3)
                        AND o.customers_id <> :customers_id
                    GROUP BY op.products_id, o.customers_id
                    ORDER BY total_purchase DESC
                    LIMIT :total_items";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);
        $command->bindParam(":last_week_date", $last_week_date, PDO::PARAM_STR);
        $command->bindParam(":total_items", $query_total_items, PDO::PARAM_INT);

        if ($result = $command->queryAll()) {
            foreach($result as $row){
                if(!in_array($row['products_id'], $exclude_products_ids)){
                    $return_array[$row['products_id']] = $row['products_id'];
                }
            }
        }
        
        return $return_array !== array() ? array_slice($return_array, 0, $total_items) : $return_array;
    }
    
    public function getPaymentMethodParentID($customers_id, $order_status = 7) {
        $return_array = array();

        $sql = "SELECT DISTINCT o.payment_methods_parent_id 
                FROM " . $this->tableName() . " AS o 
                WHERE o.customers_id = :customers_id
                    AND o.orders_status = :orders_status";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);
        $command->bindParam(":orders_status", $order_status, PDO::PARAM_INT);
        
        if ($result = $command->queryAll()) {
            $return_array = $result;
        }
        
        return $return_array;
    }
    
    public function getPaymentMethodIDByCustomerID($customers_id, $order_status = 7) {
        $return_array = array();

        $sql = "SELECT DISTINCT o.payment_methods_id, o.payment_methods_parent_id 
                FROM " . $this->tableName() . " AS o 
                WHERE o.customers_id = :customers_id
                    AND o.orders_status = :orders_status
                    AND o.payment_methods_id > 0";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);
        $command->bindParam(":orders_status", $order_status, PDO::PARAM_INT);
        
        if ($result = $command->queryAll()) {
            $return_array = $result;
        }
        
        return $return_array;
    }
}