<?php

/**
 * This is the model class for table "orders_products".
 *
 * The followings are the available columns in table 'orders_products':
 * @property integer $orders_products_id
 * @property integer $orders_id
 * @property integer $products_id
 * @property string $products_model
 * @property string $products_name
 * @property string $orders_products_store_price
 * @property string $products_price
 * @property string $final_price
 * @property integer $op_rebate
 * @property integer $op_rebate_delivered
 * @property string $products_tax
 * @property integer $products_quantity
 * @property string $products_delivered_quantity
 * @property string $products_good_delivered_quantity
 * @property string $products_good_delivered_price
 * @property string $products_canceled_quantity
 * @property string $products_canceled_price
 * @property string $products_reversed_quantity
 * @property string $products_reversed_price
 * @property integer $products_bundle_id
 * @property integer $parent_orders_products_id
 * @property integer $products_pre_order
 * @property integer $custom_products_type_id
 * @property integer $orders_products_is_compensate
 * @property integer $orders_products_purchase_eta
 * @property integer $products_categories_id
 */
class CheckoutOrdersProducts extends OrdersProductsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CheckoutOrdersProducts the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}