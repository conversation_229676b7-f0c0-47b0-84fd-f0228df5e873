<?php

/**
 * This is the model class for table "orders_status_history".
 *
 * The followings are the available columns in table 'orders_status_history':
 * @property integer $orders_status_history_id
 * @property integer $orders_id
 * @property integer $orders_status_id
 * @property string $date_added
 * @property integer $customer_notified
 * @property string $comments
 * @property integer $comments_type
 * @property integer $set_as_order_remarks
 * @property string $changed_by
 */
class CheckoutOrdersStatusHistory extends OrdersStatusHistoryBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return OrdersStatusHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}