<?php

/**
 * This is the model class for table "payment_configuration_info".
 *
 * The followings are the available columns in table 'payment_configuration_info':
 * @property integer $payment_configuration_info_id
 * @property integer $payment_methods_id
 * @property string $payment_configuration_info_title
 * @property string $payment_configuration_info_key
 * @property string $payment_configuration_info_description
 * @property integer $payment_configuration_info_sort_order
 * @property string $last_modified
 * @property string $date_added
 * @property string $use_function
 * @property string $set_function
 */
class CheckoutPaymentConfigurationInfo extends PaymentConfigurationInfoBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentConfigurationInfoBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    public function getPaymentConfigurationInfo($payment_method_id, $language_id){
        $return_array = array();

        $sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                    FROM " . $this->tableName() . " as pci
                    LEFT JOIN " . CheckoutPaymentConfigurationInfoDescription::model()->tableName() . " as pcid 
                        ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                            AND pcid.languages_id = :languages_id
                    WHERE pci.payment_methods_id = :payment_methods_id
                    ORDER BY pci.payment_configuration_info_sort_order";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":languages_id", $language_id, PDO::PARAM_INT);
        $command->bindParam(":payment_methods_id", $payment_method_id, PDO::PARAM_INT);
        
        if ($result = $command->queryAll()) {
            foreach($result as $row){
                $return_array[$row['payment_configuration_info_key']] = $row['payment_configuration_info_value'];
            }
        }
        
        return $return_array;
    }

}