<?php

/**
 * This is the model class for table "payment_methods".
 *
 * The followings are the available columns in table 'payment_methods':
 * @property integer $payment_methods_id
 * @property string $payment_methods_code
 * @property string $payment_methods_title
 * @property integer $payment_methods_send_status
 * @property integer $payment_methods_send_status_mode
 * @property string $payment_methods_send_mode_name
 * @property string $payment_methods_send_required_info
 * @property string $payment_methods_send_currency
 * @property string $payment_methods_estimated_receive_period
 * @property integer $payment_methods_send_mass_payment
 * @property string $payment_methods_send_available_sites
 * @property integer $payment_methods_receive_status
 * @property integer $payment_methods_receive_status_mode
 * @property integer $payment_methods_receive_featured_status
 * @property integer $payment_methods_sort_order
 * @property string $payment_methods_admin_groups_id
 * @property integer $payment_methods_types_id
 * @property integer $payment_methods_parent_id
 * @property string $payment_methods_legend_color
 * @property string $payment_methods_filename
 * @property string $payment_methods_logo
 * @property string $date_added
 * @property string $last_modified
 */
class CheckoutPaymentMethods extends PaymentMethodsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CheckoutPaymentMethods the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    public function getEstimatedReceivePeriod($payment_methods_id) {
        $return_int = NULL;
        
        $sql = "SELECT payment_methods_estimated_receive_period
                FROM " . $this->tableName() . " AS p
                WHERE payment_methods_id = :payment_methods_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":payment_methods_id", $payment_methods_id, PDO::PARAM_INT);
        $dataset = $command->queryScalar();
        
        if ($dataset !== false) {
            $return_int = $dataset;
        }
        
		return $return_int;
    }
    
}