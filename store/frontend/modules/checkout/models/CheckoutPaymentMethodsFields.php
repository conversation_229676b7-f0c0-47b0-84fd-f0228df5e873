<?php

/**
 * This is the model class for table "payment_methods_fields".
 *
 * The followings are the available columns in table 'payment_methods_fields':
 * @property integer $payment_methods_fields_id
 * @property integer $payment_methods_id
 * @property string $payment_methods_mode
 * @property string $payment_methods_fields_title
 * @property string $payment_methods_fields_pre_info
 * @property string $payment_methods_fields_post_info
 * @property integer $payment_methods_fields_required
 * @property integer $payment_methods_fields_type
 * @property string $payment_methods_fields_system_type
 * @property string $payment_methods_fields_size
 * @property integer $payment_methods_fields_status
 * @property string $payment_methods_fields_option
 * @property integer $payment_methods_fields_options_title
 * @property integer $payment_methods_fields_sort_order
 * @property string $payment_methods_fields_system_mandatory
 */
class CheckoutPaymentMethodsFields extends PaymentMethodsFieldsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsFieldsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getPaymentMethodDetails($store_payments_id) {
        $return_array = array();
        
        $sql = "	SELECT spd.payment_methods_fields_value, pmf.payment_methods_fields_system_type
                    FROM " . $this->tableName() . " as pmf
                    INNER JOIN " . CheckoutStorePaymentsDetails::model()->tableName() . " as spd
                        ON pmf.payment_methods_fields_id = spd.payment_methods_fields_id
                    WHERE store_payments_id = :store_payments_id";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":store_payments_id", $store_payments_id, PDO::PARAM_INT);
        
        if ($result = $command->queryAll()) {
            $return_array = $result;
        }
        
        return $return_array;
    }
}