<?php

/**
 * This is the model class for table "payment_methods_instance".
 *
 * The followings are the available columns in table 'payment_methods_instance':
 * @property integer $payment_methods_instance_id
 * @property integer $payment_methods_id
 * @property string $currency_code
 * @property integer $payment_methods_instance_default
 * @property integer $payment_methods_instance_follow_default
 */
class CheckoutPaymentMethodsInstance extends PaymentMethodsInstanceBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsInstanceBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getPaymentMethodInstanceID($payment_method_id, $selected_currency){
        $return_int = 0;

        $sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
                FROM " . $this->tableName() . " as pmi
                WHERE pmi.payment_methods_id = :payment_methods_id
                LIMIT 1";
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":payment_methods_id", $payment_method_id, PDO::PARAM_INT);
        
        if ($value = $command->queryRow()) {
            $sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
                    FROM " . $this->tableName() . " as pmi
                    WHERE pmi.currency_code = :currency_code
                        AND pmi.payment_methods_id = :payment_methods_id";
            $command = $this->conn->createCommand($sql);
            $command->bindParam(":payment_methods_id", $payment_method_id, PDO::PARAM_INT);
            $command->bindParam(":currency_code", $selected_currency, PDO::PARAM_STR);
            
            if ($value = $command->queryRow()) {
                if ((int)$value['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $return_int = (int) $value['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $return_int = (int) $value['payment_methods_instance_id'];
                }
            }
        } else {
            $sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
                    FROM " . $this->tableName() . " as pmi
                    INNER JOIN " . CheckoutPaymentMethods::model()->tableName() . " as pm
                        ON pm.payment_methods_parent_id = pmi.payment_methods_id
                    WHERE pmi.currency_code = :currency_code 
                        AND pm.payment_methods_id = :payment_methods_id";
            $command = $this->conn->createCommand($sql);
            $command->bindParam(":payment_methods_id", $payment_method_id, PDO::PARAM_INT);
            $command->bindParam(":currency_code", $selected_currency, PDO::PARAM_STR);
            
            if ($value = $command->queryRow()) {
                if ((int) $value['payment_methods_instance_follow_default'] > 0) {
                    $return_int = (int) $value['payment_methods_instance_follow_default'];
                } else {
                    $return_int = (int) $value['payment_methods_instance_id'];
                }
            }
        }
        
        return $return_int;
    }
}