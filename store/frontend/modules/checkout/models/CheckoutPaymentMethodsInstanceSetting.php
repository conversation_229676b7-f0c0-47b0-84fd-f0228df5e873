<?php

/**
 * This is the model class for table "payment_methods_instance_setting".
 *
 * The followings are the available columns in table 'payment_methods_instance_setting':
 * @property integer $payment_methods_instance_setting_id
 * @property integer $payment_methods_instance_id
 * @property string $payment_methods_instance_setting_key
 * @property string $payment_methods_instance_setting_value
 */
class CheckoutPaymentMethodsInstanceSetting extends PaymentMethodsInstanceSettingBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PaymentMethodsInstanceSettingBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getPMConfig($payment_methods_instance_id) {
        $return_array = array();

        $sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
                FROM " . $this->tableName() . " as pmis
                WHERE pmis.payment_methods_instance_id = :payment_methods_instance_id";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":payment_methods_instance_id", $payment_methods_instance_id, PDO::PARAM_INT);

        if ($result = $command->queryAll()) {
            $return_array = $result;
        }

        return $return_array;
    }
    
}