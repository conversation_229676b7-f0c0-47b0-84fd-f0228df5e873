<?php

/**
 * This is the model class for table "paypalec".
 *
 * The followings are the available columns in table 'paypalec':
 * @property string $paypal_order_id
 * @property string $txn_type
 * @property string $payment_type
 * @property string $payment_status
 * @property string $pending_reason
 * @property string $mc_currency
 * @property string $first_name
 * @property string $last_name
 * @property string $address_name
 * @property string $address_street
 * @property string $address_city
 * @property string $address_state
 * @property string $address_zip
 * @property string $address_country
 * @property string $address_status
 * @property string $payer_email
 * @property string $payer_id
 * @property string $payer_status
 * @property string $payment_date
 * @property string $business
 * @property string $receiver_email
 * @property string $receiver_id
 * @property string $txn_id
 * @property string $mc_gross
 * @property string $mc_fee
 * @property string $payment_gross
 * @property string $payment_fee
 * @property string $notify_version
 * @property string $verify_sign
 * @property string $residence_country
 * @property string $protection_eligibility
 * @property string $last_modified
 * @property string $date_added
 * @property string $data
 */
class CheckoutPaypalec extends PaypalecBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return PaypalecBase the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public function getAllOrderInVerifyingStatus($payer_email, $customers_id) {
        $return_array = array();

        $sql = "    SELECT o.orders_id  
                    FROM " . $this->tableName() . " AS p 
                    INNER JOIN " . CheckoutOrders::model()->tableName() . " AS o 
                        ON (p.paypal_order_id = o.orders_id) 
                    WHERE p.payer_email = :payer_email
                        AND o.customers_id = :customers_id
                        AND o.orders_status = 7";

        $command = $this->conn->createCommand($sql);
        $command->bindParam(":payer_email", $payer_email, PDO::PARAM_STR);
        $command->bindParam(":customers_id", $customers_id, PDO::PARAM_INT);
        
        if ($result = $command->queryAll()) {
            $return_array = $result;
        }
        
        return $return_array;
    }
}
