<?php

/**
 * This is the model class for table "products_description".
 *
 * The followings are the available columns in table 'products_description':
 * @property integer $products_id
 * @property integer $language_id
 * @property string $products_name
 * @property string $products_keyword
 * @property string $products_description
 * @property string $products_image
 * @property string $products_image_title
 * @property string $products_description_image
 * @property string $products_description_image_title
 * @property string $products_url
 * @property integer $products_viewed
 * @property string $products_location
 * @property string $products_dtu_extra_info_1
 * @property string $products_dtu_extra_info_2
 * @property string $products_dtu_extra_info_3
 */
class CheckoutProductsDescription extends ProductsDescriptionBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return CheckoutProductsDescription the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getProductsDtuExtraInfoOne($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_dtu_extra_info_1', $product_id, $language_id, $default_language_id);
    }
    
    public function getProductsDtuExtraInfoTwo($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_dtu_extra_info_2', $product_id, $language_id, $default_language_id);
    }
    
    public function getProductsDtuExtraInfoThree($product_id, $language_id = 0, $default_language_id = 0) {
        return $this->getProductInfo('products_dtu_extra_info_3', $product_id, $language_id, $default_language_id);
    }
}