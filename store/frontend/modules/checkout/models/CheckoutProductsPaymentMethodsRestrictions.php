<?php

/**
 * This is the model class for table "products_payment_methods_restrictions".
 *
 * The followings are the available columns in table 'products_payment_methods_restrictions':
 * @property string $id
 * @property string $products_id
 * @property string $restriction_info
 * @property string $created_date
 * @property string $changed_by
 */

/**
* 
*/
class CheckoutProductsPaymentMethodsRestrictions extends ProductsPaymentMethodsRestrictionsBase
{
	
	/**
	 * Returns the static model of the specified AR class.
	 * @return CheckoutProductsDescription the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getProductsPmRestrictionsAllow($product_id) {
        return $this->getPmRestrictions('Allow', $product_id);
    }
    
    public function getProductsPmRestrictionsDisallow($product_id) {
        return $this->getPmRestrictions('Disallow', $product_id);
    }
}