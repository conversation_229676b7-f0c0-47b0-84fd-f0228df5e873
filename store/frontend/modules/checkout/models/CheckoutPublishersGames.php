<?php

/**
 * This is the model class for table "publishers_games".
 *
 * The followings are the available columns in table 'publishers_games':
 * @property string $publishers_games_id
 * @property string $publishers_id
 * @property string $publishers_game
 * @property integer $categories_id
 * @property integer $publishers_games_status
 * @property double $publishers_games_daily_limit
 * @property double $publishers_games_today_topped_amount
 * @property string $publishers_games_pending_message
 * @property string $publishers_games_reloaded_message
 * @property string $publishers_games_failed_message
 * @property string $publishers_games_remark
 * @property string $publishers_server
 * @property double $publishers_games_daily_topped_amount
 */
class CheckoutPublishersGames extends PublishersGamesBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PublishersGamesBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}