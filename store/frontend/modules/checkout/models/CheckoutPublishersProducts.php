<?php

/**
 * This is the model class for table "publishers_products".
 *
 * The followings are the available columns in table 'publishers_products':
 * @property string $publishers_games_id
 * @property integer $products_id
 */
class CheckoutPublishersProducts extends PublishersProductsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PublishersProductsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}