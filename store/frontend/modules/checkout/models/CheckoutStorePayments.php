<?php

/**
 * This is the model class for table "store_payments".
 *
 * The followings are the available columns in table 'store_payments':
 * @property integer $store_payments_id
 * @property integer $user_id
 * @property string $user_role
 * @property string $user_firstname
 * @property string $user_lastname
 * @property string $user_email_address
 * @property string $user_country_international_dialing_code
 * @property string $user_telephone
 * @property string $user_mobile
 * @property string $store_payments_date
 * @property integer $store_payments_status
 * @property string $store_payments_request_currency
 * @property string $store_payments_request_amount
 * @property string $store_payments_fees
 * @property string $store_payments_after_fees_amount
 * @property string $store_payments_paid_currency
 * @property string $store_payments_paid_currency_value
 * @property string $store_payments_paid_amount
 * @property string $store_payments_reference
 * @property integer $store_payments_methods_id
 * @property string $store_payments_methods_name
 * @property integer $store_payment_account_book_id
 * @property string $user_payment_methods_alias
 * @property string $store_payments_fees_calculation
 * @property string $store_payments_last_modified
 * @property integer $store_payments_read_mode
 * @property integer $store_payments_lock
 */
class CheckoutStorePayments extends StorePaymentsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    public function getStorePaymentInfoByID($returned_unique_id) {
        $return_array = array();

        $sql = "	SELECT store_payments_id, store_payments_request_amount, store_payments_methods_id, store_payments_fees, store_payments_request_currency,
                        store_payments_after_fees_amount, store_payments_paid_currency, store_payments_paid_currency_value, store_payments_status, user_firstname, user_lastname, user_email_address
                    FROM " . $this->tableName() . "
                    WHERE store_payments_id = :store_payments_id";
        
        $command = $this->conn->createCommand($sql);
        $command->bindParam(":store_payments_id", $returned_unique_id, PDO::PARAM_INT);

        if ($result = $command->queryRow()) {
            $return_array = $result;
        }

        return $return_array;
    }
    
    public function updateStorePaymentBYPK($id, $data) {
        return $this->updateByPk($id, $data);
    }
    
}