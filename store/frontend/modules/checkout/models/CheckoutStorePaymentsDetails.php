<?php

/**
 * This is the model class for table "store_payments_details".
 *
 * The followings are the available columns in table 'store_payments_details':
 * @property integer $store_payments_id
 * @property integer $payment_methods_fields_id
 * @property string $payment_methods_fields_title
 * @property string $payment_methods_fields_value
 * @property integer $payment_methods_fields_sort_order
 */
class CheckoutStorePaymentsDetails extends StorePaymentsDetailsBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsDetailsBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}