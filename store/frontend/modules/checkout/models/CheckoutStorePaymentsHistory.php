<?php

/**
 * This is the model class for table "store_payments_history".
 *
 * The followings are the available columns in table 'store_payments_history':
 * @property integer $store_payments_history_id
 * @property integer $store_payments_id
 * @property integer $store_payments_status
 * @property string $date_added
 * @property integer $payee_notified
 * @property string $comments
 * @property string $changed_by
 * @property string $changed_by_role
 */
class CheckoutStorePaymentsHistory extends StorePaymentsHistoryBase
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StorePaymentsHistoryBase the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
}