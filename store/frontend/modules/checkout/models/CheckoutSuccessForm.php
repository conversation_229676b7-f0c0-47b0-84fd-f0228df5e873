<?php

class CheckoutSuccessForm extends CFormModel
{

    public $order_products_id_array = array();

    public function getRelatedProduct()
    {
        $return_array = array();
        $products_id_array = array();
        $related_products = array();

        if ($products_id_array = CheckoutOrders::model()->getRelatedProducts($this->order_products_id_array, Yii::app()->user->id, 6)) {
            $related_products = Yii::app()->searchBoxCom->getProductListByProductIDs($products_id_array);
        }

        if ($related_products) {
            foreach ($related_products as $idx => $product) {
                $product_url = Yii::app()->createUrl('product/index', array('pid' => $product['pid'], 'dm' => $product['default_dm'], 'pm' => $product['promo_type']));

                $return_array[] = array(
                    'type' => 'two-col-layout',
                    'desc' => array(
                        'type' => 'link',
                        'href' => $product_url,
                        'label' => $product['name'],
                    ),
                    'class' => 'col-lg-20 col-md-30 col-sm-60',
                    'extra_info' => array(
                        'data-N' => array(
                            'data-id' => 'data_rvp_' . $idx,
                            'data-name' => htmlspecialchars($product['name']),
                            'data-type' => 'buy',
                            'data-pid' => $product['pid'],
                            'data-dtu' => $product['is_dtu_flag'],
                            'data-bundle' => $product['products_bundle'],
                            'data-dm' => $product['default_dm'],
                            'data-pm' => $product['promo_type'],
                        ),
                    ),
                    'button_info' => array(
                        'type' => 'with_price-' . $product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'],
                        'label_str_limit' => 20,
                        'label' => $product['price'],
                        'label_suffix' => Yii::t('buttons', strtoupper($product['delivery_status']['dm'] . '-' . $product['delivery_status']['type'])),
                        'attributes_info' => array(
                            'id' => 'rvp_' . $idx,
                        )
                    ),
                    'image_info' => array(
                        'attributes_info' => array(
                            'src' => $product['image']['dimension'][0]
                        ),
                        'map_info' => array(
                            'href' => $product_url,
//                            'target' => '_blank'
                        ),
                    ),
                );
            }
        }

        return $return_array;
    }

    public function generateOrderFBInfo($order)
    {
        if (is_object($order)) {
            $fb_tracking_code = <<<EOL
(function() {
var _fbq = window._fbq || (window._fbq = []);
if (!_fbq.loaded) {
var fbds = document.createElement('script');
fbds.async = true;
fbds.src = '//connect.facebook.net/en_US/fbds.js';
var s = document.getElementsByTagName('script')[0];
s.parentNode.insertBefore(fbds, s);
_fbq.loaded = true;
}
})();
window._fbq = window._fbq || [];
window._fbq.push(['track', '6027640665911', {'value':'{$order->info['total_value']}','currency':'{$order->info['currency']}'}]);
EOL;
            Yii::app()->clientScript->registerScript(
                    'fb-tracking-code', $fb_tracking_code, CClientScript::POS_END
            );
        }
    }

    public function generateOrderGtmInfo($order, $opt = "trackTrans")
    {
        $gtm_array = array();

        if (is_object($order)) {
            switch ($opt) {
                case "trackTrans":
                    $payment_methods_parent_title = '';
                    $total_product_count = count($order->products);

                    if ($order->info['payment_method']['payment_methods_parent_id'] > 0) {
                        $pg_row = CheckoutPaymentMethods::model()->findByPk($order->info['payment_method']['payment_methods_parent_id']);

                        if ($pg_row) {
                            $payment_methods_parent_title = $pg_row->payment_methods_title;
                        }
                    }

                    // https://support.google.com/tagmanager/answer/3002596
                    $gtm_array['event'] = 'trackTrans';
                    $gtm_array['transactionId'] = $order->order_id;
                    $gtm_array['transactionTotal'] = $order->info['total_value'];

                    // additional info (Optional)
                    $gtm_array['paymentGatewayID'] = $order->info['payment_method']['payment_methods_parent_id'];
                    $gtm_array['paymentGateway'] = $payment_methods_parent_title;
                    $gtm_array['paymentMethodID'] = $order->info['payment_method']['payment_methods_id'];
                    $gtm_array['paymentMethod'] = $order->info['payment_method'];
                    $gtm_array['transactionCity'] = $order->customer['city'];
                    $gtm_array['transactionState'] = $order->customer['state'];
                    $gtm_array['transactionCountry'] = $order->customer['country'];

                    for ($p_cnt = 0; $p_cnt < $total_product_count; $p_cnt++) {
                        $gtm_array['transactionProducts'][] = array(
                            'sku' => $order->products[$p_cnt]['id'],
                            'name' => $order->products[$p_cnt]['name'],
                            'price' => $order->products[$p_cnt]["storage_price"]['final_price'],
                            'quantity' => $order->products[$p_cnt]['quantity']
                        );
                    }
                    break;

                case "order_success":
                    // GTM & Google Ads tracking 2022.12.05
                    // Google Support Team advice to disable GTM Sale2, change to new dataLayer
                    $gtm_array['event'] = 'order_success';
                    $gtm_array['order_value'] = $order->info['total_value'];
                    $gtm_array['order_id'] = $order->order_id;
                    $gtm_array['enhanced_conversion_data']['email'] = $order->customer['email_address'];
                    $gtm_array['enhanced_conversion_data']['phone_number'] = $order->customer['telephone'];
                    $gtm_array['enhanced_conversion_data']['first_name'] = $order->customer['name'];
                    $gtm_array['enhanced_conversion_data']['last_name'] = $order->customer['name'];
                    $gtm_array['enhanced_conversion_data']['street'] = $order->customer['street_address'];
                    $gtm_array['enhanced_conversion_data']['city'] = $order->customer['city'];
                    $gtm_array['enhanced_conversion_data']['region'] = "";
                    $gtm_array['enhanced_conversion_data']['postal_code'] = $order->customer['postcode'];
                    $gtm_array['enhanced_conversion_data']['country'] = $order->customer['country'];
                    break;

                case "purchase":
                    $payment_methods_parent_title = '';
                    $total_product_count = count($order->products);

                    if ($order->info['payment_method']['payment_methods_parent_id'] > 0) {
                        $pg_row = CheckoutPaymentMethods::model()->findByPk($order->info['payment_method']['payment_methods_parent_id']);

                        if ($pg_row) {
                            $payment_methods_parent_title = $pg_row->payment_methods_title;
                        }
                    }
                    
                    $gtm_array["event"] = "purchase";
                    $gtm_array['transactionId'] = $order->order_id;
                    $gtm_array['transactionTotal'] = $order->info['total_value'];

                    // additional info (Optional)
                    $gtm_array['paymentGatewayID'] = $order->info['payment_method']['payment_methods_parent_id'];
                    $gtm_array['paymentGateway'] = $payment_methods_parent_title;
                    $gtm_array['paymentMethodID'] = $order->info['payment_method']['payment_methods_id'];
                    $gtm_array['paymentMethod'] = $order->info['payment_method'];

                    for ($p_cnt = 0; $p_cnt < $total_product_count; $p_cnt++) {
                        $gtm_array['items'][] = array(
                            'sku' => $order->products[$p_cnt]['id'],
                            'name' => $order->products[$p_cnt]['name'],
                            'price' => $order->products[$p_cnt]["storage_price"]['final_price'],
                            'quantity' => $order->products[$p_cnt]['quantity']
                        );
                    }
                    break;
            }
        }

        return $gtm_array;
    }

}
