<?php

/**
 * This is the model class for table "zones".
 *
 * The followings are the available columns in table 'zones':
 * @property integer $zone_id
 * @property integer $zone_country_id
 * @property string $zone_code
 * @property string $zone_name
 */
class CheckoutZones extends ZonesBase {

    /**
     * Returns the static model of the specified AR class.
     * @return Zones the static model class
     */
    public static function model($className=__CLASS__) {
        return parent::model($className);
    }
    
    public function getStateList($countryId) {
        $criteria = new CDbCriteria();
        $criteria->select = 'zone_id, zone_name';
        $criteria->condition = 'zone_country_id =:countryId';
        $criteria->params = array(':countryId' => $countryId);
        $criteria->order = 'zone_name';
        $result = $this->model()->findAll($criteria);
        return $result;
    }
}