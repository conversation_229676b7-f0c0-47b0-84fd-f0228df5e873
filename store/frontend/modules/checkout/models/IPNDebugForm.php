<?php

class IPNDebugForm extends CFormModel {
    public $email_address, 
            $enabled,
            $error,
            $info;
            
    function __construct($email_address, $debug_enabled = 'No') {
        $this->email_address = $email_address;
        $this->enabled = ($debug_enabled == 'Yes') ? true : false;
        $this->error = false;
        $this->info = array();
    }

    function initalize($debug_str, $response_str) {
        if ($this->enabled) {
            $debug_string = Yii::t('checkoutModule.general', 'DEBUG_MSG', array(
                        '{EMAIL_SEPARATOR}' => Yii::t('checkoutModule.general', 'EMAIL_SEPARATOR'),
                        '{DEBUG_STR}' => str_replace('&', "\r\n", $debug_str),
                        '{RESPONSE_STR}' => str_replace('&', "\r\n", $response_str)
            ));

            $this->add(Yii::t('checkoutModule.general', 'DEBUG'), $debug_string);
        }
    }

    function add($subject, $msg = '') {
        if ($this->enabled) {
            $this->info[] = array('subject' => $subject, 'msg' => $msg);
        }
    }

    function raise_error($subject, $msg, $clear_stack = false) {
        if ($clear_stack === true) {
            unset($this->info);
        }

        $this->add($subject, $msg);
        $this->error = true;
    }

    function info($html = false) {
        $debug_string = '';
        $seperator = "\r\n" . Yii::t('checkoutModule.general', 'EMAIL_SEPARATOR') . "\r\n";
        $debug = $this->info;
        reset($debug);
        $debug_msg_total = count($debug);

        for ($i = 0; $i < $debug_msg_total; $i++) {
            $debug_string .= $seperator . $debug[$i]['subject'] . $seperator . $debug[$i]['msg'] . "\r\n";
        }

        if ($html === true) {
            $debug_string = str_replace("\n", "\n<br />", $debug_string);
        }

        return $debug_string;
    }

    function send_email() {
        if ($this->enabled) {
            if (count($this->info) > 0) {
                $to_name = '';
                $to_address = $this->email_address;
                $subject = "PayPal_Shopping_Cart_IPN";
                $msg = strip_tags(nl2br($this->info()));

                FrontendMailCom::send($to_name, $to_address, $subject, $msg, ConfigurationCom::getValue('STORE_OWNER'), ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS'));
            }
        }
    }

}
