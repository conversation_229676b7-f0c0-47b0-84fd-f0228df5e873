<?php

class IPNForm {
    const CHANGE_BY = 'OGM system';
    
    private $pg_info, $pg_config, $invoice_id;
    public $debug_obj,
            $key,
            $response_string,
            $curl_reponse,
            $ipn_id;

    function __construct($post_vars) {
        $transaction_list = array('web_accept', 'cart', 'send_money', 'new_case', 'masspay', 'adjustment'); //accepted transactions v1.6
        // https://www.paypal.com/IntegrationCenter/ic_ipn-pdt-variable-reference.html
        $payment_status_list = array('Reversed', 'Refunded', 'Canceled-Reversal', 'Completed', 'Denied', 'Expired', 'Failed', 'In-Progress', 'Pending', 'Processed', 'Voided');

        $this->invoice_id = isset($post_vars['invoice']) ? (int) $post_vars['invoice'] : 0;

        // new_case has no payment status
        if (isset($post_vars['txn_type']) && $post_vars['txn_type'] == 'new_case') { // Does not has 'payment_status' info from IPN
            $this->dispute_notification($post_vars);

            if (isset($post_vars['txn_id']) && strlen($post_vars['txn_id']) >= 17 && strlen($post_vars['txn_id']) <= 19) {
                $this->init($post_vars); //Looks like a PayPal transaction
            }
        } else {
            if (isset($post_vars['txn_type']) && !in_array($post_vars['txn_type'], $transaction_list) && isset($post_vars['payment_status']) && !in_array($post_vars['payment_status'], $payment_status_list)) {
                $this->getDebugObj()->add(
                        $this->t('UNKNOWN_TXN_TYPE'), $this->t('UNKNOWN_TXN_TYPE_MSG', array(
                            '{TXN_TYPE})' => $post_vars['txn_type'],
                            '{IP_ADDRESS}' => getIPAddress()
                        ))
                );
            } else if ((isset($post_vars['txn_id']) && strlen($post_vars['txn_id']) >= 17 && strlen($post_vars['txn_id']) <= 19) || (isset($post_vars['txn_type']) && $post_vars['txn_type'] == 'masspay')) {
                $this->init($post_vars); //Looks like a PayPal transaction
            } else {
                $this->getDebugObj()->add(
                        $this->t('UNKNOWN_POST'), $this->t('UNKNOWN_POST_MSG', array(
                            '{IP_ADDRESS}' => getIPAddress()
                        ))
                );
            }
        }
    }

    private function dispute_notification($post_vars) {
        $cb_email_content = 'Order ID: ' . (isset($post_vars['invoice']) ? $post_vars['invoice'] : '-') .
                '<br>Case ID:' . (isset($post_vars['case_id']) ? $post_vars['case_id'] : '-') .
                '<br>Case Type:' . (isset($post_vars['case_type']) ? $post_vars['case_type'] : '-') .
                '<br>Reason Code:' . (isset($post_vars['reason_code']) ? $post_vars['reason_code'] : '-');

        $this->sendMail('<EMAIL>', '<EMAIL>', '[OffGamers] PayPal Dispute/Chargeback Notification', $cb_email_content);
    }

    function init($post_vars) {
        $debug_string = '';
        $this->key = array();
        $this->response_string = 'cmd=_notify-validate';
        reset($post_vars);

        foreach ($post_vars as $var => $val) {
            $debug_string .= $var . '=' . $val . '&';
            $val = validateRequest($val);

            if (!strcasecmp($var, 'cmd') || !preg_match("/^[_0-9a-z-]{1,32}$/i", $var)) {
                unset($var);
                unset($val);
            } elseif ($var != '') {
                $this->key[$var] = $val;
                $this->response_string .= '&' . $var . '=' . urlencode($val);
            }
        }

        $this->getDebugObj()->initalize($debug_string, $this->response_string);
        unset($post_vars, $debug_string);
    }

    function authenticate() {
        $domain = $this->getPGInfo('domain');
        $ipn_url = $this->getPGInfo('ipn_validate_url');
        $ipn_test_mode = $this->getPGInfo('ipn_test_mode');
        $curl_flag = true;
        $this->curl_reponse = array();
        
        if ($ipn_url && $ipn_test_mode == 'Off') {
            $ch = @curl_init();
            @curl_setopt($ch, CURLOPT_URL, $ipn_url);
            @curl_setopt($ch, CURLOPT_POST, 1);
            @curl_setopt($ch, CURLOPT_POSTFIELDS, $this->response_string);
            @curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            @curl_setopt($ch, CURLOPT_HEADER, 0);
            @curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            @curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            $paypal_response = @curl_exec($ch);
            $this->curl_reponse['raw'] = $paypal_response;
            
            if ($paypal_response == '') {
                $curl_flag = false;
                $this->curl_reponse['curl'] = 'cURL resource: ' . (string) $ch . '; cURL error: ' . curl_error($ch) . ' (cURL error code ' . curl_errno($ch) . ')';
            }
            
            @curl_close($ch);
            
            if (strstr($paypal_response, 'VERIFIED')) {
                $this->getDebugObj()->add($this->t('RESPONSE_VERIFIED'), $this->t('RESPONSE_MSG', array(
                            '{EMAIL_SEPARATOR}' => $this->t('EMAIL_SEPARATOR'),
                            '{CURL_FLAG}' => $curl_flag,
                            '{SOCKET}' => '',
                            '{DOMAIN}' => $domain,
                            '{PORT}' => '',
                            '{PAYPAL_RESPONSE}' => $paypal_response))
                );

                return true;
            } else if (strstr($paypal_response, 'INVALID')) {
                $this->getDebugObj()->add($this->t('RESPONSE_INVALID'), $this->t('RESPONSE_MSG', array(
                            '{EMAIL_SEPARATOR}' => $this->t('EMAIL_SEPARATOR'),
                            '{CURL_FLAG}' => $curl_flag,
                            '{SOCKET}' => '',
                            '{DOMAIN}' => $domain,
                            '{PORT}' => '',
                            '{PAYPAL_RESPONSE}' => $paypal_response))
                );

                return false;
            } else {
                $this->getDebugObj()->add($this->t('RESPONSE_UNKNOWN'), $this->t('RESPONSE_MSG', array(
                            '{EMAIL_SEPARATOR}' => $this->t('EMAIL_SEPARATOR'),
                            '{CURL_FLAG}' => $curl_flag,
                            '{SOCKET}' => '',
                            '{DOMAIN}' => $domain,
                            '{PORT}' => '',
                            '{PAYPAL_RESPONSE}' => $paypal_response))
                );

                return false;
            }
        }

        return false;
    }

    //Test both receiver email address and business ID
    function validate_receiver_email() {
        $receiver_email = $this->getPGConfig('paypal_id');
        $business = $this->getPGConfig('business_id');

        if (!strcmp(strtolower($receiver_email), strtolower($this->getKey('receiver_email'))) && !strcmp(strtolower($business), strtolower($this->getKey('business')))) {
            $this->getDebugObj()->add($this->t('EMAIL_RECEIVER'), $this->t('EMAIL_RECEIVER_MSG', array(
                        '{EMAIL_SEPARATOR}' => $this->t('EMAIL_SEPARATOR'),
                        '{RECEIVER_EMAIL}' => $receiver_email,
                        '{BUSINESS}' => $business,
                        '{PAYPAL_RECEIVER_EMAIL}' => $this->getKey('receiver_email'),
                        '{PAYPAL_BUSINESS}' => $this->getKey('business')))
            );
            return true;
        } else {
            $this->getDebugObj()->add($this->t('EMAIL_RECEIVER'), $this->t('EMAIL_RECEIVER_ERROR_MSG', array(
                        '{EMAIL_SEPARATOR}' => $this->t('EMAIL_SEPARATOR'),
                        '{RECEIVER_EMAIL}' => $receiver_email,
                        '{BUSINESS}' => $business,
                        '{PAYPAL_RECEIVER_EMAIL}' => $this->getKey('receiver_email'),
                        '{PAYPAL_BUSINESS}' => $this->getKey('business'),
                        '{PAYPAL_TXN_ID}' => $this->getKey('txn_id')))
            );
            return false;
        }
    }

    /*
     * not in used
     */

    function unique_txn_id() {
        return false;
    }

    /*
     * not in used
     */

    function insert_ipn_txn() {
        return false;
    }

    /*
     * not in used
     */

    function update_status($paypal_ipn_id, $payment_status, $pending_reason) {
        
    }

    /*
     * not in used
     */

    function valid_payment() {
        return false;
    }

    //returns TABLE_PAYPAL.paypal_ipn_id
    function id() {
        return $this->ipn_id;
    }

    //returns the transaction type (paypal.txn_type)
    function txn_type() {
        return $this->getKey('txn_type');
    }

    function datetime_to_sql_format($raw_datetime) {
        $months = array('Jan' => '01', 'Feb' => '02', 'Mar' => '03', 'Apr' => '04', 'May' => '05', 'Jun' => '06', 'Jul' => '07', 'Aug' => '08', 'Sep' => '09', 'Oct' => '10', 'Nov' => '11', 'Dec' => '12');
        $hour = substr($raw_datetime, 0, 2);
        $minute = substr($raw_datetime, 3, 2);
        $second = substr($raw_datetime, 6, 2);
        $month = $months[substr($raw_datetime, 9, 3)];
        $day = substr($raw_datetime, 13, 2);
        $year = substr($raw_datetime, 17, 4);
        return ($year . "-" . $month . "-" . $day . " " . $hour . ":" . $minute . ":" . $second);
    }

    function dienice() {
        $return_array = array();

        if (strcmp(phpversion(), '3.0') <= 0) {
            if ($this->getKey('digest_key') == $this->digest_key()) {
                $return_array = array(
                    'response' => array(
                        'header' => 'status: 204',
                    )
                );
            } else {
                $return_array = array(
                    'response' => array(
                        'header' => 'status: 500',
                    )
                );
            }
        } else {
            if ($this->getKey('digest_key') == $this->digest_key()) {
                $return_array = array(
                    'response' => array(
                        'header' => 'HTTP/1.0 204 No Response',
                    )
                );
            } else {
                $return_array = array(
                    'response' => array(
                        'header' => 'HTTP/1.0 204 No Response',
                    )
                );
            }
        }
        return $return_array;
    }

    function digest_key() {
        $paypal_ipn_digest_key = ConfigurationCom::getValue('MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY', false);
        return strrev(md5(md5(strrev(md5($paypal_ipn_digest_key)))));
    }

    public function getDebugObj() {
        if (!$this->debug_obj) {
            $email_address = $this->getPGInfo('ipn_debug_email');
            $debug_enabled = $this->getPGInfo('ipn_debug');

            $this->debug_obj = new IPNDebugForm($email_address, $debug_enabled);
        }

        return $this->debug_obj;
    }

    private function t($key, $params = array()) {
        return Yii::t('checkoutModule.general', $key, $params);
    }

    private function getKey($key, $default = '') {
        return isset($this->key[$key]) ? $this->key[$key] : $default;
    }

    private function getPGInfo($key, $default = '') {
        if (!$this->pg_info && $this->invoice_id) {
            $pm_id = CheckoutOrders::model()->getPaymentMethodID($this->invoice_id);

            if ($pm_id) {
                $payment_methods_row = CheckoutPaymentMethods::model()->findByPk($pm_id);

                if ($payment_methods_row) {
                    $this->pg_info['payment_methods_id'] = (int) $payment_methods_row->payment_methods_id;
                    $this->pg_info['payment_methods_parent_id'] = (int) $payment_methods_row->payment_methods_parent_id;
                    $this->pg_info['code'] = $payment_methods_row->payment_methods_code;
                    $this->pg_info['title'] = $payment_methods_row->payment_methods_title; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
                    $this->pg_info['sort_order'] = $payment_methods_row->payment_methods_sort_order; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
                    $this->pg_info['enabled'] = $payment_methods_row->payment_methods_receive_status_mode;
                    $this->pg_info['legend_display_colour'] = $payment_methods_row->payment_methods_legend_color;
                    $this->pg_info['receive_status'] = $payment_methods_row->payment_methods_receive_status;

                    // SEND PAYMENT
                    $this->pg_info['send_enabled'] = $payment_methods_row->payment_methods_send_status;

                    //load 'TABLE_PAYMENT_CONFIGURATION_INFO'
                    if ($configuration_setting_array = $this->load_pm_setting($this->pg_info['payment_methods_id'], $this->pg_info['payment_methods_parent_id'])) {
                        if (!isset($configuration_setting_array['MODULE_PAYMENT_PAYPAL_CURRENCY'])){
                            header("HTTP/1.1 200 OK");
                            Yii::app()->end();
                        }
                        $this->pg_info['currency'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CURRENCY'];
                        $this->pg_info['processing_status_id'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID'];
                        $this->pg_info['order_status_id'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID'];
                        $this->pg_info['invoice_required'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED'];
                        $this->pg_info['cs'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CS'];
                        $this->pg_info['processing_logo'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO'];
                        $this->pg_info['store_logo'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_STORE_LOGO'];
                        $this->pg_info['page_style'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PAGE_STYLE'];
                        $this->pg_info['no_note'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_NO_NOTE'];
                        $this->pg_info['method'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_METHOD'];
                        $this->pg_info['shipping_allowed'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED'];
                        $this->pg_info['ipn_debug'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DEBUG'];
                        $this->pg_info['ipn_digest_key'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY'];
                        $this->pg_info['ipn_test_mode'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE'];
                        $this->pg_info['ipn_cart_test'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_CART_TEST'];
                        $this->pg_info['ipn_debug_email'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL'];
                        $this->pg_info['domain'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_DOMAIN'];
                        $this->pg_info['rm'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_RM'];
                        $this->pg_info['message'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MESSAGE'];
                        $this->pg_info['email_message'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE'];
                        $this->pg_info['confirm_complete_days'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE'];
                        $this->pg_info['verified_email_notification'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION'];
                        $this->pg_info['verified_email_notification_status'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS'];
                        $this->pg_info['ewp'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_EWP'];
                        $this->pg_info['require_address_information'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_ADDRESS_FIELD'];
                        $this->pg_info['require_ic_information'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_IC_FIELD'];
                        $this->pg_info['require_contact_information'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_CONTACT_FIELD'];

                        $this->pg_info['customer_payment_info'] = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CUSTOMER_PAYMENT_INFO'];
                    }

                    if ($this->pg_info['ipn_test_mode'] == 'On') {
                        $this->pg_info['api_endpoint_url'] = 'https://api-3t.sandbox.paypal.com/nvp';
                        $this->pg_info['ipn_validate_url'] = 'https://www.sandbox.paypal.com/cgi-bin/webscr';
                    } else {
                        $this->pg_info['api_endpoint_url'] = 'https://api-3t.paypal.com/nvp';
                        $this->pg_info['ipn_validate_url'] = 'https://ipnpb.paypal.com/cgi-bin/webscr';
                    }
                }
            }
        }

        return isset($this->pg_info[$key]) ? $this->pg_info[$key] : $default;
    }

    function getPGConfig($key, $default = '') {
        if (!$this->pg_config) {
            $payment_methods_id = $this->getPGInfo('payment_methods_id');
            $selected_currency = $this->getKey('mc_currency');

            if ($selected_currency && $payment_methods_id) {
                $payment_methods_instance_id = CheckoutPaymentMethodsInstance::model()->getPaymentMethodInstanceID($payment_methods_id, $selected_currency);
                $payment_gateway_instance_setting_result = CheckoutPaymentMethodsInstanceSetting::model()->getPMConfig($payment_methods_instance_id);

                foreach ($payment_gateway_instance_setting_result as $payment_gateway_instance_setting_row) {
                    switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                        case 'MODULE_PAYMENT_PAYPAL_ID':
                            $this->pg_config['paypal_id'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID':
                            $this->pg_config['ewp_cert_id'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID':
                            $this->pg_config['business_id'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_API_USERNAME':
                            $this->pg_config['api_username'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_API_PASSWORD':
                            $this->pg_config['api_password'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE':
                            $this->pg_config['api_signature'] = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_PRIVATE_KEY_FILE':
                            $this->pg_config['private_key_file'] = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_PUBLIC_KEY_FILE':
                            $this->pg_config['public_key_file'] = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                        case 'MODULE_PAYMENT_PAYPAL_PAYPAL_PUBLIC_KEY_FILE':
                            $this->pg_config['paypal_public_key_file'] = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                            break;
                    }
                }
            }
        }

        return isset($this->pg_config[$key]) ? $this->pg_config[$key] : $default;
    }

    private function load_pm_setting($payment_method_id, $payment_gateway_id, $language_id = 1) {
        $pm_setting_array = array();

        if (!$pm_setting_array = CheckoutPaymentConfigurationInfo::model()->getPaymentConfigurationInfo($payment_method_id, $language_id)) {
            if ($payment_gateway_id > 0) {
                $pm_setting_array = CheckoutPaymentConfigurationInfo::model()->getPaymentConfigurationInfo($payment_gateway_id, $language_id);
            }
        }

        return $pm_setting_array;
    }

    public function processMasspay() {
        $this->getDebugObj()->add($this->t('PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION'));
        $num_of_order = 0;

        foreach ($this->key as $ipn_key_loop => $ipn_key_data) {
            if (preg_match("/status_[0-9]+$/i", $ipn_key_loop))
                $num_of_order++;
        }

        for ($count_order = 1; $count_order <= $num_of_order; $count_order++) {
            $returned_masspay_txn_id = (isset($this->key['masspay_txn_id_' . $count_order]) ? $this->key['masspay_txn_id_' . $count_order] : '');
            $returned_mc_currency = (isset($this->key['mc_currency_' . $count_order]) ? $this->key['mc_currency_' . $count_order] : '');
            $returned_mc_gross = (isset($this->key['mc_gross_' . $count_order]) ? $this->key['mc_gross_' . $count_order] : '');
            $returned_mc_fee = (isset($this->key['mc_fee_' . $count_order]) ? $this->key['mc_fee_' . $count_order] : '');
            $returned_mc_handling = (isset($this->key['mc_handling' . $count_order]) ? $this->key['mc_handling' . $count_order] : '');
            $returned_receiver_email = (isset($this->key['receiver_email_' . $count_order]) ? $this->key['receiver_email_' . $count_order] : '');
            $returned_status = (isset($this->key['status_' . $count_order]) ? $this->key['status_' . $count_order] : '');
            $returned_unique_id = (isset($this->key['unique_id_' . $count_order]) ? $this->key['unique_id_' . $count_order] : '');

            $masspay_txn_id = $returned_masspay_txn_id; // this var is not found, assume it's return masspay txn id

            $comments_log_str = "<u>MassPay IPN</u>\n";
            $comments_log_str .= "MassPay Transaction ID: " . $returned_masspay_txn_id . "\n";
            $comments_log_str .= "Currency: " . $returned_mc_currency . "\n";
            $comments_log_str .= "Gross: " . $returned_mc_gross . "\n";
            $comments_log_str .= "Fee: " . $returned_mc_fee . "\n";
            $comments_log_str .= "Handling: " . $returned_mc_handling . "\n";
            $comments_log_str .= "Receiver Email: " . $returned_receiver_email . "\n";
            $comments_log_str .= "Status: " . $returned_status . "\n\n";

            if ($store_payments_row = CheckoutStorePayments::model()->getStorePaymentInfoByID($returned_unique_id)) {
                $return_paypal_gross_amt = number_format($returned_mc_gross, Yii::app()->currency->getInfo($returned_mc_currency, 'decimal_places'), Yii::app()->currency->getInfo($returned_mc_currency, 'decimal_point'), Yii::app()->currency->getInfo($returned_mc_currency, 'thousands_point'));
                
                $paypal_currency = $store_payments_row['store_payments_paid_currency'];
                $paypal_currency_rate = $store_payments_row['store_payments_paid_currency_value'];
                $paypal_after_fees_amount = $paypal_currency_rate == null ? $store_payments_row['store_payments_after_fees_amount'] : ($paypal_currency_rate * $store_payments_row['store_payments_after_fees_amount']);
                $paypal_gross_amt = number_format($paypal_after_fees_amount, Yii::app()->currency->getInfo($paypal_currency, 'decimal_places'), Yii::app()->currency->getInfo($paypal_currency, 'decimal_point'), Yii::app()->currency->getInfo($paypal_currency, 'thousands_point'));

                if (Yii::app()->currency->getInfo($returned_mc_currency, 'symbol_left') . $return_paypal_gross_amt . Yii::app()->currency->getInfo($returned_mc_currency, 'symbol_right') == Yii::app()->currency->getInfo($paypal_currency, 'symbol_left') . $paypal_gross_amt . Yii::app()->currency->getInfo($paypal_currency, 'symbol_right')) {
                    $store_payments_array = array();

                    $store_payments_details_result = CheckoutPaymentMethodsFields::model()->getPaymentMethodDetails($store_payments_row['store_payments_id']);

                    foreach ($store_payments_details_result as $store_payments_details_row) {
                        if (in_array($store_payments_details_row['payment_methods_fields_system_type'], array('MODULE_PAYPAL_SEND_EMAIL', 'MODULE_PAYPALEC_SEND_EMAIL'))) {
                            $store_payments_array['SEND_EMAIL'] = $store_payments_details_row['payment_methods_fields_value'];
                        } else {
                            $store_payments_array[$store_payments_details_row['payment_methods_fields_system_type']] = $store_payments_details_row['payment_methods_fields_value'];
                        }
                    }

                    if (isset($store_payments_array['SEND_EMAIL']) && $store_payments_array['SEND_EMAIL'] == $returned_receiver_email) {
                        if (strtolower($returned_status) == 'completed') {
                            if ($store_payments_row['store_payments_status'] != 3) {
                                $comments_log_str .= "<u>Result</u>\n";
                                $comments_log_str .= "Send Mass Payment Completed\nStatus: Completed.\nReference: " . $returned_masspay_txn_id;

                                $store_payments_data_sql = array(
                                    'store_payments_reference' => "Reference: " . $returned_masspay_txn_id,
                                    'store_payments_last_modified' => new CDbExpression('NOW()'),
                                    'store_payments_paid_amount' => $returned_mc_gross,
                                    'store_payments_status' => 3
                                );
                                CheckoutStorePayments::model()->updateStorePaymentBYPK($store_payments_row['store_payments_id'], $store_payments_data_sql);

                                $store_payments_history_data_sql = array(
                                    'store_payments_id' => $store_payments_row['store_payments_id'],
                                    'store_payments_status' => 3,
                                    'date_added' => new CDbExpression('NOW()'),
                                    'payee_notified' => 0,
                                    'comments' => $comments_log_str,
                                    'changed_by' => self::CHANGE_BY,
                                    'changed_by_role' => 'system'
                                );
                                CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);

                                $payment_received_by_str = '';
                                $payment_methods_estimated_receive_period = CheckoutPaymentMethods::model()->getEstimatedReceivePeriod($store_payments_row['store_payments_methods_id']);

                                if ($payment_methods_estimated_receive_period !== NULL) {
                                    $payment_received_by_str = $this->t('TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED');
                                }

                                // Insert payment history
                                $payment_history_sql_data_array = array(
                                    'store_payments_id' => $store_payments_row['store_payments_id'],
                                    'store_payments_status' => 3,
                                    'date_added' => new CDbExpression('NOW()'),
                                    'payee_notified' => 1,
                                    'comments' => $payment_received_by_str,
                                    'changed_by' => self::CHANGE_BY,
                                    'changed_by_role' => 'system'
                                );
                                CheckoutStorePaymentsHistory::model()->saveNewRecord($payment_history_sql_data_array);

                                // Email to beneficiary
                                if ($store_payments_row['user_email_address']) {
                                    $email_subject = ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX') . $this->t('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', array('{STORE_PAYMENTS_ID}' => $store_payments_row['store_payments_id']));
                                    $email_content = $store_payments_row['user_firstname'] . ' ' . $store_payments_row['user_lastname'] . "\n\n" .
                                            $payment_received_by_str . "\n\n\n" .
                                            $this->t('EMAIL_CONTACT', array(
                                                '{EMAIL_TO}' => ConfigurationCom::getValue('EMAIL_TO'),
                                                '{STORE_NAME}' => ConfigurationCom::getValue('STORE_NAME')
                                            )) .
                                            ConfigurationCom::getValue('STORE_EMAIL_SIGNATURE');
                                    $this->sendMail($store_payments_row['user_firstname'] . ' ' . $store_payments_row['user_lastname'], $store_payments_row['user_email_address'], $email_subject, $email_content);
                                }
                            }
                        } else if (strtolower($returned_status) == 'processed') { // status processed
                            $comments_log_str .= "<u>Result</u>\n";
                            $comments_log_str .= "Send Mass Payment Processed\nStatus: Processed.\nReference: " . $masspay_txn_id;

                            $store_payments_data_sql = array(
                                'store_payments_reference' => $masspay_txn_id,
                                'store_payments_last_modified' => new CDbExpression('NOW()'),
                                'store_payments_status' => 2
                            );
                            CheckoutStorePayments::model()->updateStorePaymentBYPK($store_payments_row['store_payments_id'], $store_payments_data_sql);

                            $store_payments_history_data_sql = array(
                                'store_payments_id' => $store_payments_row['store_payments_id'],
                                'store_payments_status' => 2,
                                'date_added' => new CDbExpression('NOW()'),
                                'payee_notified' => 0,
                                'comments' => $comments_log_str,
                                'changed_by' => self::CHANGE_BY,
                                'changed_by_role' => 'system'
                            );
                            CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);
                        } else if (strtolower($returned_status) == 'denied' || strtolower($returned_status) == 'unclaimed') { // status denied
                            $comments_log_str .= "<u>Result</u>\n";
                            $comments_log_str .= "Send Mass Payment rejected\nStatus: " . strtolower($returned_status) . ".\nReference: " . $masspay_txn_id;

                            $store_payments_data_sql = array(
                                'store_payments_reference' => $masspay_txn_id,
                                'store_payments_last_modified' => new CDbExpression('NOW()'),
                                'store_payments_status' => 2
                            );
                            CheckoutStorePayments::model()->updateStorePaymentBYPK($store_payments_row['store_payments_id'], $store_payments_data_sql);

                            $store_payments_history_data_sql = array(
                                'store_payments_id' => $store_payments_row['store_payments_id'],
                                'store_payments_status' => 2,
                                'date_added' => new CDbExpression('NOW()'),
                                'payee_notified' => 0,
                                'comments' => $comments_log_str,
                                'changed_by' => self::CHANGE_BY,
                                'changed_by_role' => 'system'
                            );
                            CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);
                        } else {
                            $comments_log_str .= "<u>Result</u>\n";
                            $comments_log_str .= "Send Mass Payment On Hold\nStatus: Unknown.\nReference: " . $masspay_txn_id;

                            $store_payments_history_data_sql = array(
                                'store_payments_id' => $store_payments_row['store_payments_id'],
                                'store_payments_status' => 0,
                                'date_added' => new CDbExpression('NOW()'),
                                'payee_notified' => 0,
                                'comments' => $comments_log_str,
                                'changed_by' => self::CHANGE_BY,
                                'changed_by_role' => 'system'
                            );
                            CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);
                        }
                    } else { // receiver email not match
                        $store_payments_paypal_send_email = isset($store_payments_array['SEND_EMAIL']) ? $store_payments_array['SEND_EMAIL'] : '';
                        $comments_log_str .= "<u>Result</u>\n";
                        $comments_log_str .= "Receiver Email Not Match, " . $store_payments_paypal_send_email . '!=' . $returned_receiver_email . "\n\n";

                        $store_payments_history_data_sql = array(
                            'store_payments_id' => $store_payments_row['store_payments_id'],
                            'store_payments_status' => 0,
                            'date_added' => new CDbExpression('NOW()'),
                            'payee_notified' => 0,
                            'comments' => $comments_log_str,
                            'changed_by' => self::CHANGE_BY,
                            'changed_by_role' => 'system'
                        );
                        CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);
                    }
                } else { // amount or currency not match
                    $comments_log_str .= "<u>Result</u>\n";
                    $comments_log_str .= "Amount not matched, " . Yii::app()->currency->getInfo($returned_mc_currency, 'symbol_left') . $return_paypal_gross_amt . Yii::app()->currency->getInfo($returned_mc_currency, 'symbol_right') . '!=' . Yii::app()->currency->getInfo($paypal_currency, 'symbol_left') . $paypal_gross_amt . Yii::app()->currency->getInfo($paypal_currency, 'symbol_right') . "\n\n";

                    $store_payments_history_data_sql = array(
                        'store_payments_id' => $store_payments_row['store_payments_id'],
                        'store_payments_status' => 0,
                        'date_added' => new CDbExpression('NOW()'),
                        'payee_notified' => 0,
                        'comments' => $comments_log_str,
                        'changed_by' => self::CHANGE_BY,
                        'changed_by_role' => 'system'
                    );
                    CheckoutStorePaymentsHistory::model()->saveNewRecord($store_payments_history_data_sql);
                }
            }
        }
        $this->getDebugObj()->send_email();
        
        return array();
    }

    private function sendMail($to_name, $to_email_address, $email_subject, $email_text) {
        FrontendMailCom::send($to_name, $to_email_address, $email_subject, $email_text, ConfigurationCom::getValue('STORE_OWNER'), Yii::app()->params['MAIL_SENDER']['INFO']);
    }
    
    protected function reportError($error_str, $ext_subject = '') {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($error_str);
        echo "========================================================<BR>";
        echo "Request : " . getenv('REQUEST_URI');
        $debug_html = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' PG IPN - ' . date("F j, Y H:i");
        $attachments = array(
            array(
                'color' => 'danger',
                'text' => $debug_html
            )
        );
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

}