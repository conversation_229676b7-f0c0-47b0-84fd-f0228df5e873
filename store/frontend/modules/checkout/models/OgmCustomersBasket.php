<?php

/**
 * This is the model class for table "ogm_customers_basket".
 *
 * The followings are the available columns in table 'ogm_customers_basket':
 * @property integer $cart_id
 * @property integer $customers_id
 * @property integer $products_id
 * @property integer $customers_basket_quantity
 * @property string $customers_basket_date_added
 * @property integer $products_categories_id
 * @property string $customers_basket_custom_key
 * @property string $customers_basket_custom_value
 * @property string $pg_tran_id
 * @property string $cart_info
 */
class OgmCustomersBasket extends CActiveRecord
{
    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OgmCustomersBasketBase the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ogm_customers_basket';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('cart_id, customers_id, products_id, customers_basket_date_added, products_categories_id', 'required'),   //, pg_tran_id, cart_info
            array('cart_id, customers_id, products_id, customers_basket_quantity, products_categories_id', 'numerical', 'integerOnly' => true),
            array('customers_basket_custom_key', 'length', 'max' => 3),
            array('pg_tran_id', 'length', 'max' => 11),
            array('customers_basket_custom_value', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('cart_id, customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id, customers_basket_custom_key, customers_basket_custom_value, pg_tran_id, cart_info', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'cart_id' => 'Cart',
            'customers_id' => 'Customers',
            'products_id' => 'Products',
            'customers_basket_quantity' => 'Customers Basket Quantity',
            'customers_basket_date_added' => 'Customers Basket Date Added',
            'products_categories_id' => 'Products Categories',
            'customers_basket_custom_key' => 'Customers Basket Custom Key',
            'customers_basket_custom_value' => 'Customers Basket Custom Value',
            'pg_tran_id' => 'Pg Tran',
            'cart_info' => 'Cart Info',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('cart_id', $this->cart_id);
        $criteria->compare('customers_id', $this->customers_id);
        $criteria->compare('products_id', $this->products_id);
        $criteria->compare('customers_basket_quantity', $this->customers_basket_quantity);
        $criteria->compare('customers_basket_date_added', $this->customers_basket_date_added, true);
        $criteria->compare('products_categories_id', $this->products_categories_id);
        $criteria->compare('customers_basket_custom_key', $this->customers_basket_custom_key, true);
        $criteria->compare('customers_basket_custom_value', $this->customers_basket_custom_value, true);
        $criteria->compare('pg_tran_id', $this->pg_tran_id, true);
        $criteria->compare('cart_info', $this->cart_info, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    function validCartId($cartId, $checkCustID = false)
    {
        $criteria = new CDbCriteria;

        $criteria->condition = 'cart_id = :cartId ' . ($checkCustID ? ' AND customers_id = ' . Yii::app()->user->id : '');
        $criteria->params = array(':cartId' => $cartId);

        $result = $this->model()->count($criteria);

        return (int)$result;
    }

    public function saveToCart($cartId, $products_id, $unit, $categories_id, $prodType, $serializeInfo)
    {
        $date = date("Y-m-d H:i:s");

        if (Yii::app()->user->id) {
            $customerId = Yii::app()->user->id;
        } else {
            $customerId = '0';
        }

        try {
            DbHelper::insertIgnore($this->tableName(), [
                'cart_id' => $cartId,
                'customers_id' => $customerId,
                'products_id' => $products_id,
                'customers_basket_quantity' => $unit,
                'customers_basket_date_added' => $date,
                'products_categories_id' => $categories_id,
                'customers_basket_custom_key' => $prodType,
                'customers_basket_custom_value' => $serializeInfo
            ]);
        } catch (Exception $e) {
        }

        $criteria = new CDbCriteria();
        $criteria->compare('customers_id', $customerId);
        $criteria->limit = 1;

        $latest = OgmCustomersBasket::model()->find($criteria);

        return $latest->cart_id;
    }

    public function getCartInfoByTID($pg_tran_Id, $customers_id = 0)
    {
        $condition = 'pg_tran_id = :pg_tran_id';
        $params = array(
            ':pg_tran_id' => $pg_tran_Id,
        );

        if ($customers_id !== 0) {
            $condition .= ' AND customers_id=:customers_id';
            $params[':customers_id'] = $customers_id;
        }

        $criteria = new CDbCriteria;
        $criteria->select = 'cart_id, customers_id, cart_info, pg_tran_id';
        $criteria->condition = $condition;
        $criteria->params = $params;

        return $this->model()->find($criteria);
    }

    public function getCartInfoByCID($cart_Id, $customers_id = 0)
    {
        $condition = 'cart_id = :cartId';
        $params = array(
            ':cartId' => $cart_Id,
        );

        if ($customers_id !== 0) {
            $condition .= ' AND customers_id=:customers_id';
            $params[':customers_id'] = $customers_id;
        }

        $criteria = new CDbCriteria;
        $criteria->select = 'cart_id, customers_id, cart_info, pg_tran_id';
        $criteria->condition = $condition;
        $criteria->params = $params;

        return $this->model()->find($criteria);
    }

    public function getCartInfoByCartId($cartId, $customerId = 0)
    {
        $condition = 'cart_id = :cartId';
        $params = array(
            ':cartId' => $cartId,
        );

        if ($customerId !== 0) {
            $condition .= ' AND customers_id=:customers_id';
            $params[':customers_id'] = $customerId;
        }

        $criteria = new CDbCriteria;
        $criteria->select = 'customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id, 
							customers_basket_custom_key, customers_basket_custom_value, pg_tran_id';
        $criteria->condition = $condition;
        $criteria->params = $params;

        return $this->model()->findAll($criteria);
    }

    public function removeOldCart($customerId = 0, $customers_basket_custom_key = '')
    {
        $customerId = !empty($customerId) ? $customerId : Yii::app()->user->id;

        $criteria = new CDbCriteria;
        $criteria->condition = 'customers_id = :customerId';
        $criteria->params = array(':customerId' => $customerId);

        if ($customers_basket_custom_key) {
            $criteria->condition .= ' AND customers_basket_custom_key = :customers_basket_custom_key';
            $criteria->params[':customers_basket_custom_key'] = $customers_basket_custom_key;
        }

        $this->model()->deleteAll($criteria);
    }

    public function cleanCartID()
    {
        $criteria = new CDbCriteria;
        $criteria->condition = 'customers_basket_date_added < DATE_SUB(NOW(), INTERVAL 1 DAY)';
        $this->model()->deleteAll($criteria);
    }

}