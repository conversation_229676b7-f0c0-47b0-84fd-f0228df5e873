<?php

/**
 * This is the model class for table "coupons".
 *
 * The followings are the available columns in table 'coupons':
 * @property integer $coupon_id
 * @property integer $coupon_generation_id
 * @property string $coupon_type
 * @property string $coupon_code
 * @property string $coupon_amount
 * @property string $coupon_minimum_order
 * @property string $max_cap
 * @property string $coupon_start_date
 * @property string $coupon_expire_date
 * @property integer $uses_per_coupon
 * @property string $uses_per_coupon_unlimited
 * @property integer $uses_per_user
 * @property string $uses_per_user_unlimited
 * @property string $restrict_to_products
 * @property string $restrict_to_categories
 * @property string $restrict_to_customers
 * @property string $restrict_to_customers_groups
 * @property string $restrict_to_currency_id
 * @property string $restrict_to_payment_id
 * @property string $coupon_active
 * @property int $mobile_only
 * @property string $date_created
 * @property string $date_modified
 */
class OrderCoupons extends CouponsBase {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return OrderCoupons the static model class
     */
    public static function model($className=__CLASS__) {
        return parent::model($className);
    }

    public function checkCoupon(){
        $cust_group = Yii::app()->frontPageCom->customer_group_id;
        $criteria = new CDbCriteria;
        $criteria->select = 'coupon_id';
        $criteria->condition = "`coupon_type` != 'G' AND `coupon_active` = 'Y' AND `coupon_start_date` <= NOW() AND `coupon_expire_date` > NOW() AND (`restrict_to_customers_groups` = 'ALL' OR FIND_IN_SET(:cust_group,`restrict_to_customers_groups`))";
        $criteria->params = array(':cust_group' => $cust_group);

        $result = $this->model()->find($criteria);
        return $result;
    }

    public static function checkPaymentRestriction($code)
    {
        $criteria = new CDbCriteria;
        $criteria->select = 'coupon_id';
        $criteria->condition = "`coupon_type` != 'G' AND `coupon_active` = 'Y' AND `coupon_code` = :coupon_code AND restrict_to_payment_id != ''";
        $criteria->params = [':coupon_code' => $code];

        $result = self::model()->find($criteria);
        return $result;
    }

}