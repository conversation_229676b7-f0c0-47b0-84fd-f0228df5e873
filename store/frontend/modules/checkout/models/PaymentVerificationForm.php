<?php

class PaymentVerificationForm extends CFormModel {
    const CHANGE_BY = 'OGM system';
    
    private $container = array();
    
    public $email, $customer_id;
    
    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('email, customer_id', 'required'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array();
    }
    
    public function processPaymentVerification() {
        if ($this->customer_id && $this->email) {
            $order_info_array = CheckoutOrders::model()->getPaymentMethodIDByCustomerID($this->customer_id);
            $rerun_array = array();
            
            foreach ($order_info_array as $order_info) {
                $rerun_array[$order_info['payment_methods_parent_id']][$order_info['payment_methods_id']] = '-';
            }
            
            foreach ($rerun_array as $pm_parent_id => $pm_arr) {
                foreach ($pm_arr as $pm_id => $ignore) {
                    $this->enableReRunByPaymentMethod($pm_parent_id, $pm_id, $this->email, $this->customer_id);
                }
            }
        }
        
        return isset($this->container['order_id']) && count($this->container['order_id']);
    }
    
    public function enableReRunByPaymentMethod($pm_parent_id, $pm_id, $customer_email, $customers_id) {
        $payment_code_obj = PipwavePaymentMapperBase::model()->findByAttributes(array('pm_id' => $pm_id));
        $payment_code = isset($payment_code_obj->pg_code) ? $payment_code_obj->pg_code : '';

        if (!$payment_code) {
            $m_pm = CheckoutPaymentMethods::model()->findByPk($pm_parent_id);
            
            if (isset($m_pm->payment_methods_filename)) {
                switch ($m_pm->payment_methods_filename) {
                    case 'paypalEC.php':
                        $payment_code = 'paypalEC';
                        break;
                }
            }
        }
        
        $orders_array = array();
        $order_id_array = array();
        
        if (isset($payment_code)) {
            switch ($payment_code) {
                case 'paypalEC':
                case 'moneybookers':
                    /* Remove after 2018-01-01 (Since is searching for current order and already fully checkout via pipwave)
                    if ($orders_array1 = CheckoutPaypalec::model()->getAllOrderInVerifyingStatus($customer_email, $customers_id)) {
                        foreach ($orders_array1 as $arr) {
                            $order_id_array[$arr['orders_id']] = '-';
                        }
                    }
                    */
                    if ($orders_array = CheckoutAnalysisPgInfo::model()->getAllOrderInVerifyingStatus($payment_code, $customer_email, $customers_id)) {
                        foreach ($orders_array as $arr) {
                            $order_id_array[$arr['orders_id']] = '-';
                        }
                    }
                    
                    if ($order_id_array) {
                        $orders_array = array_keys($order_id_array);
                    }
                    break;
            }
        }
        
        foreach ($orders_array as $orders_id) {
            $is_exist = CheckoutCronGenesisOrders::model()->exists(array(
                'condition' => 'orders_id = :orders_id',
                'params' => array(':orders_id' => $orders_id)
            ));
            
            if (!$is_exist && CheckoutCronGenesisOrders::model()->insertReRunGenesis($orders_id)) {
                $this->container['order_id'][] = $orders_id;
                
                $m_attr = array(
                    'orders_id' => $orders_id,
                    'orders_status_id' => 0,
                    'date_added' => new CDbExpression('NOW()'),
                    'customer_notified' => '0',
                    'comments' => 'Payer Email verified, re-run genesis action has been added into process queue.',
                    'changed_by' => self::CHANGE_BY
                );
                CheckoutOrdersStatusHistory::model()->saveNewRecord($m_attr);
            }
        }
    }

}