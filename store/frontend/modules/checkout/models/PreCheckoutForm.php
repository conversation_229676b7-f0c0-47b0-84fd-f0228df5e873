<?php

class PreCheckoutForm extends CFormModel
{
    private $product_obj,
        $cart_obj,
        $checkout_obj,

        $response_publishers_id,
        $response_publishers_games_id;

    public $cfm_password, $payment_info, $cpt_id, $buyqty, $action, $products_id, $delivery_mode, $process_cart, $ext_error_message, $deliver_addr,
        $message, $index, $returnUrlRoute, $returnUrlQuery, $confirmed, $game_info, $gv_redeem_code, $payment_restrictions_pid, $breadcrumbs,

        $orders_sc_currency_code,

        $user_confirmed,
        $bypass_DTU_check = false,
        $next_action,
        $state_list,
        $countryCode = "MY";


    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        $rules = [
            [
                'products_id, buyqty, confirmed, delivery_mode',
                'required',
                'message' => Yii::t('checkoutModule.checkout', 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__1001-1'),
                'on' => 'buy_code'
            ],
            [
                'products_id, buyqty, orders_sc_currency_code',
                'required',
                'message' => Yii::t('checkoutModule.checkout', 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__1001-2'),
                'on' => 'top_sc'
            ],
            [
                'confirmed',
                'required',
                'message' => Yii::t('checkoutModule.checkout', 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__1001-3'),
                'on' => 'get_flow'
            ],
            [
                'buyqty',
                'numerical',
                'integerOnly' => true,
                'min' => 1,
                'tooSmall' => Yii::t('checkoutModule.checkout', 'ERROR_CHECKOUT_QUANTITY_NOT_ALLOWED__1002-5')
            ],
            ['products_id, confirmed, delivery_mode', 'numerical', 'integerOnly' => true, 'on' => 'buy_code'],
            [
                'cfm_password',
                'validatePassword',
                'on' => 'cfm_password',
                'message' => Yii::t('checkoutModule.checkout', 'ERROR_WRONG_PASSWORD')
            ],
            ['confirmed', 'validateConfirmed'],
            ['delivery_mode', 'filterDeliveryMode', 'on' => 'buy_code'],
            ['gv_redeem_code', 'filterRedeemCode', 'on' => 'buy_code'],

            ['products_id', 'validateProduct', 'on' => ['buy_code', 'top_sc']],
            ['orders_sc_currency_code', 'validateOrderScCurrencyCode', 'on' => ['top_sc']],
            ['products_id', 'validatePhoneSanctionCountry'],
            ['buyqty', 'validatePurchaseQty', 'on' => ['buy_code']],
            ['game_info', 'validateGameInfo', 'on' => 'buy_code'],
            ['deliver_addr', 'validateDeliverAddress', 'on' => 'buy_code'],
            ['returnUrlQuery', 'filterURLParams', 'on' => 'buy_code'],
            ['returnUrlRoute', 'filterURLRoute', 'on' => 'buy_code'],
            [
                'products_id, buyqty, confirmed, delivery_mode, game_info, returnUrlQuery, returnUrlRoute, orders_sc_currency_code, gv_redeem_code, cfm_password, deliver_addr',
                'safe'
            ],
            ['index', 'safe'],
        ];

        return $rules;
    }

    public function validateDeliverAddress()
    {
        if (isset($this->deliver_addr) && is_array($this->deliver_addr)) {
            foreach ($this->deliver_addr as $key => &$value) {
                $value = strip_tags($value);
                if (empty($value) && !in_array($key, ['addr_2'])) {
                    $this->addError(
                        'deliver_addr',
                        Yii::t('checkoutModule.checkout', 'MESSAGE_VALIDATION_DELIVERY_ADDRESS')
                    );
                    return false;
                }
            }
        }

        return true;
    }

    public function filterDeliveryMode()
    {
        if ($this->products_id) {
            $product_delivery_mode_array = $this->getProductObj()->getProductDeliveryMode($this->products_id);
            $this->delivery_mode = in_array(
                $this->delivery_mode,
                $product_delivery_mode_array
            ) ? $this->delivery_mode : 0;

            if ($this->delivery_mode == 0) {
                foreach ($product_delivery_mode_array as $dm) {
                    if ((int)$dm != 6) {
                        $this->delivery_mode = (int)$dm;
                        break;
                    }
                }
            }
        } else {
            $this->delivery_mode = 5;
        }

        return true;
    }

    public function filterRedeemCode()
    {
        if ($this->user_confirmed['skip_cfm_order'] === 1) {
            $this->getCartObj()->removeCoupon();
        } else {
            if ($this->gv_redeem_code) {
                if (notEmpty($this->gv_redeem_code)) {
                    $this->getCartObj()->setCoupon($this->gv_redeem_code);
                } else {
                    $this->getCartObj()->removeCoupon();
                }
            }
        }
        return true;
    }

    public function filterURLParams()
    {
        if ($this->returnUrlQuery) {
            $this->returnUrlQuery = getSafeQueryArray([], CJSON::decode($this->returnUrlQuery), false);
        } else {
            $this->returnUrlQuery = [];
        }

        $this->returnUrlQuery['pid'] = $this->products_id;

        return true;
    }

    public function filterURLRoute()
    {
//        if ($this->returnUrlRoute) {
//            $this->returnUrlRoute = Yii::app()->request->getBaseURL(true) . '/' . ltrim($this->returnUrlRoute, '/');
//        } else {
//            $this->returnUrlRoute = Yii::app()->request->getBaseURL(true);
//        }
    }

    public function validatePurchaseQty()
    {
        if ($this->hasErrors()) {
            return false;
        }

        if ($this->buyqty > 100) {
            $this->addError('buyqty', Yii::t('checkoutModule.checkout', 'ERROR_CHECKOUT_QUANTITY_NOT_ALLOWED__1002-6'));
            return false;
        }

        // Only check for status in checkout page
        if (!in_array($this->confirmed, [
                0,
                10,
                11
            ]) && !ProductsCheckoutSetting::validateCheckoutQuantity($this->products_id, $this->buyqty)) {
            $this->addError('buyqty', Yii::t('checkoutModule.checkout', 'ERROR_CHECKOUT_QUANTITY_NOT_ALLOWED__1002-4'));
            return false;
        }

        if ($this->bypass_DTU_check !== true && $this->delivery_mode == 6) {
        } else {
            $stock_status = $this->getProductObj()->isProductOutOfStock($this->products_id, $this->buyqty);
            if ($stock_status == 'OUT_OF_STOCK') {
                if ($this->buyqty >= 50) {
                    $this->addError('buyqty', 'ERROR_STOCK_NOT_AVAILABLE_GREATER_THAN_50__1002-2');
                } else {
                    $this->addError('buyqty', 'ERROR_STOCK_NOT_AVAILABLE__1002-1');
                }

                return false;
            }
        }

        return true;
    }

    public function validateProduct()
    {
        if ($this->products_id) {
            $this->getProductObj()->setProductID($this->products_id);
            $status = $this->getProductObj()->getProductPurchaseStatus($this->products_id);

            if ($status !== 'ACTIVE') {
                $this->addError('products_id', 'ERROR_STOCK_NOT_AVAILABLE__1002-3');
                return false;
            }

            $custom_products_type_id = $this->getProductObj()->getProductInfo('custom_products_type_id');
            $product_type = $this->getProductObj()->getProductInfo('products_type');

            if (!in_array($custom_products_type_id, [2, 3])) {
                $this->addError('products_id', 'ERROR_PRODUCT_IS_NOT_AVAILABLE__1003-2');
                return false;
            }

            // DTU 
            if ($this->bypass_DTU_check !== true && $this->delivery_mode == 6) {
                $direct_topup_obj = new DirectTopupCom();

                if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($this->products_id)) {
                    $this->response_publishers_id = $publishers_id;
                } else {
                    $this->addError('products_id', 'ERROR_PRODUCT_IS_NOT_AVAILABLE__1003-3');
                    return false;
                }
            } else {
                if ($this->getProductObj()->checkProductRegionPermission($this->products_id, Yii::app()->session['country_code'], Yii::app()->frontPageCom->customer_ip_country)) {
                } else {
                    // product does not exist in your region.
                    $this->addError('products_id', 'ERROR_CART_NO_PERMISSION__1004-1');
                    return false;
                }
            }

            return true;
        }

        $this->addError('products_id', 'ERROR_PRODUCT_IS_NOT_AVAILABLE__1003-1');
        return false;
    }

    public function validateConfirmed()
    {
        $next_is = $this->pfvNextAccess();
        // DTU product does not need to check account for pop up confirm order box.
        if ($this->user_confirmed['skip_cfm_order'] === 1 || $this->user_confirmed['skip_cfm_order'] === 2) {
            $this->bypass_DTU_check = true;
        }

        return true;
    }

    public function validateGameInfo()
    {
        if ($this->bypass_DTU_check !== true && $this->delivery_mode == 6) {
            if (!$this->game_info || !isset($this->game_info['account']) || !notNull($this->game_info['account'])) {
                $this->addError('game_info', 'ERROR_INVALID_TOP_UP_ACCOUNT__1005-1');
                return false;
            }

            if (isset($this->game_info['account_2']) && isset($this->game_info['account'])) {
                if (trim($this->game_info['account']) != trim($this->game_info['account_2'])) {
                    $this->addError('game_info', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                    return false;
                }
            }

            $this->game_info['account'] = trim($this->game_info['account']);

            $direct_topup_obj = new DirectTopupCom();
            $product_info_array = $this->getProductObj()->getProductBundleInfoByID();
            $publishers_games_products_id = $this->products_id;

            if ($product_info_array['actual']['products_bundle'] == 'yes' || $product_info_array['actual']['products_bundle_dynamic'] == 'yes') {
                $publishers_games_products_id = CheckoutPublishersProducts::model()->getBundleProductID(
                    $publishers_games_products_id
                );
            }

            $games_mapping_row = CheckoutPublishersGames::model()->getGameInfo(
                $this->response_publishers_id,
                $publishers_games_products_id
            );
            $get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);

            $validate_game_acc_array = [
                'game' => $games_mapping_row['publishers_game'],
                'publishers_games_id' => $games_mapping_row['publishers_games_id'],
                'product_id' => $this->products_id,
                'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
                'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
                'product_code' => (isset($get_top_up_info_array['product_code']['top_up_info_value']) ? $get_top_up_info_array['product_code']['top_up_info_value'] : ''),
                'quantity' => $this->buyqty,
            ];

            if ($direct_topup_obj->validate_game_acc(
                $this->response_publishers_id,
                array_merge($this->game_info, $validate_game_acc_array),
                $curl_response_array
            )) {
                $this->response_publishers_games_id = $games_mapping_row['publishers_games_id'];
            } else {
                if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '1508') {
                    $this->addError('buyqty', 'ERROR_DTU_EXCEED_TOP_UP_LIMIT_1006-1');
                    return false;
                } else {
                    if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
                        $this->addError('game_info', 'ERROR_INVALID_TOP_UP_ACCOUNT');
                        if (!empty($curl_response_array['publisher_msg'])) {
                            $this->addError('game_info', $curl_response_array['publisher_msg']);
                        }
                        return false;
                    } else {
                        $this->addError('game_info', 'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT');
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public function validateOrderScCurrencyCode()
    {
        if ($this->hasErrors()) {
            return false;
        }

        if ($this->orders_sc_currency_code) {
            # test ID exist
            $currency_id_test = Yii::app()->currency->getIdByCode($this->orders_sc_currency_code);

            if ($currency_id_test > 0) {
                return true;
            }
        }

        $this->addError('orders_sc_currency_code', 'MESSAGE_ERROR_PLEASE_TRY_AGAIN_LATER__4');
        return false;
    }

    public function validatePendingOrder()
    {
        $_max_pending_num = ConfigurationCom::getValue('B2C_MAX_NUM_OF_PENDING');
        $_max_pending_min = ConfigurationCom::getValue('B2C_MAX_PENDING_WITHIN_MIN');

        if (!empty($_max_pending_num)) {
            $_current_pending = OrdersBase::model()->getPendingOrderCount(Yii::app()->user->id, $_max_pending_min);
            if ($_current_pending >= $_max_pending_num) {
                $this->addError(
                    'orders_sc_currency_code',
                    Yii::t('CheckoutModule.checkout', 'ERROR_MAX_PENDING_ORDER', ['{{ORDER_NO}}' => $_current_pending])
                );
                return false;
            }
        }
        return true;
    }

    public function validatePhoneSanctionCountry()
    {
        if (Yii::app()->customerCom->isPhoneSanctionCountry()) {
            $this->addError('products_id', Yii::t('page_content', 'TEXT_SANCTION_COUNTRY'));
            return false;
        }
        return true;
    }


    protected function afterValidate()
    {
        parent::afterValidate();

        if ($this->returnUrlRoute) {
            $this->getCartObj()->setReturnURL('/checkout/buyNow/buy', $this->returnUrlQuery);
        }
    }

    public function buyCode($custom_products_type_id = 2)
    {
        $error = false;

        try {
            $this->getCartObj()->cleanCartID();
            $this->getCartObj()->resetCart();

            // cd key
            if ($this->bypass_DTU_check !== true && $this->delivery_mode == 6) {
                $direct_topup_obj = new DirectTopupCom();

                $extra_info_array = [
                    'delivery_mode' => $this->delivery_mode,
                ];

                if ($this->game_info) {
                    $extra_info_array['top_up_info'] = $this->game_info;
                    $this->update_dtu_game_info($this->products_id, $this->game_info);
                    $customer_id_conversion = $direct_topup_obj->get_publishers_games_conf(
                        $this->response_publishers_games_id,
                        'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG'
                    );

                    if ($customer_id_conversion == '1' && !empty($extra_info_array['top_up_info']['account']) && !is_numeric(
                            $extra_info_array['top_up_info']['account']
                        )) {
                        $extra_info_array['top_up_info']['account'] = $extra_info_array['top_up_info']['account'] . ':~:' . $direct_topup_obj->convert_customer_email_to_id(
                                $extra_info_array['top_up_info']['account']
                            );
                    }
                }

                if ($this->delivery_mode == 6 && empty($this->game_info) && $this->confirmed != 0 && $this->confirmed != 10 && $this->confirmed != 11) {
                    $attachments = [
                        [
                            'color' => 'danger',
                            'text' => json_encode([
                                'request_url' => Yii::app()->request->url,
                                'POST' => $_POST,
                                'confirmed' => $this->confirmed,
                                'scenario' => $this->getScenario(),
                            ]),
                        ],
                    ];

                    Yii::app()->slack->send('*[frontend] Missing DTU Account Info*', $attachments, 'DEV_DEBUG');
                }

                $addToCart_array = [
                    'products_id' => $this->products_id,
                    'qty' => $this->buyqty,
                    'custom_key' => $custom_products_type_id,
                    'custom_value' => $extra_info_array,
                ];
                if (isset($_REQUEST['sub_products_id'])) {
                    $addToCart_array['sub_products_id'] = $_REQUEST['sub_products_id'];
                }
                $add_cart_scenario = in_array($this->user_confirmed['skip_cfm_order'], [
                    1,
                    2
                ]) ? 'add_cart' : 'cfm_cart';
                $this->getCartObj()->addCart($addToCart_array, $add_cart_scenario);
            } else {
                $extra_info_array = [];

                # SC does not required dm
                if ($custom_products_type_id != 3) {
                    $extra_info_array['delivery_mode'] = $this->delivery_mode;
                } else {
                    $extra_info_array['orders_sc_currency_code'] = $this->orders_sc_currency_code;
                }

                if ($this->deliver_addr) {
                    $extra_info_array['deliver_addr'] = $this->deliver_addr;
                }

                if ($this->delivery_mode == 6 && empty($this->game_info) && $this->confirmed != 10 && $this->confirmed != 11) {
                    $attachments = [
                        [
                            'color' => 'danger',
                            'text' => json_encode([
                                'request_url' => Yii::app()->request->url,
                                'POST' => $_POST,
                                'confirmed' => $this->confirmed,
                                'scenario' => $this->getScenario(),
                            ]),
                        ],
                    ];

                    Yii::app()->slack->send('*[frontend] Missing DTU Account Info*', $attachments, 'DEV_DEBUG');
                }

                $addToCart_array = [
                    'products_id' => $this->products_id,
                    'qty' => $this->buyqty,
                    'custom_key' => $custom_products_type_id,
                    'custom_value' => $extra_info_array,
                ];
                if (isset($_REQUEST['sub_products_id'])) {
                    $addToCart_array['sub_products_id'] = $_REQUEST['sub_products_id'];
                }

                $add_cart_scenario = in_array($this->user_confirmed['skip_cfm_order'], [
                    1,
                    2
                ]) ? 'add_cart' : 'cfm_cart';
                $this->getCartObj()->addCart($addToCart_array, $add_cart_scenario);
            }
        } catch (Exception $e) {
            $error = true;
            $this->addError('buyCode', $e->getMessage());
        }

        return $error !== true;
    }

    function checkPassword($customerId, $password)
    {
        return Yii::app()->customerCom->validatePassword($password, $customerId);
    }

    private function getCartObj()
    {
        if (!is_object($this->cart_obj)) {
            $this->cart_obj = new CheckoutCartCom();
        }

        return $this->cart_obj;
    }

    private function getCheckoutObj()
    {
        if (!is_object($this->checkout_obj)) {
            $this->checkout_obj = new CheckoutCom();
        }

        return $this->checkout_obj;
    }

    public function setPaymentRestrictionsChecking($products_id)
    {
        $this->payment_restrictions_pid = $products_id;
    }

    private function getProductObj()
    {
        if (!is_object($this->product_obj)) {
            $this->product_obj = new CheckoutProductCom();
        }

        return $this->product_obj;
    }

    public function get_cdkey_delivery_section_content()
    {
        $default_dm = '';
        $delivery_methods_array = [];
        $product_delivery_mode_array = [];
        $product_delivery_mode = $this->getProductObj()->getProductDeliveryMode($this->products_id);
        $product_type = $this->getProductObj()->getProductInfo('products_type');

        foreach ($product_delivery_mode as $dm_id) {
            $product_delivery_mode_array[$dm_id] = [];
        }

        $products_delivery_mode_array = $this->getProductObj()->getProductDeliveryModeTitleByArrayID(
            $product_delivery_mode
        );
        foreach ($products_delivery_mode_array as $products_delivery_mode_row) {
            $product_delivery_mode_array[$products_delivery_mode_row['products_delivery_mode_id']] = $products_delivery_mode_row['products_delivery_mode_title'];
        }

        if (notNull($this->delivery_mode) && count($product_delivery_mode_array)) {
            switch ($this->delivery_mode) {
                case 6 : // DTU
                    if (isset($product_delivery_mode_array['6'])) {
                        $product_delivery_mode_array = ['6' => $product_delivery_mode_array['6']];
                    }
                    break;
                default : // General
                    unset($product_delivery_mode_array['6']);
                    break;
            }
        }

        if (count($product_delivery_mode_array)) {
            foreach ($product_delivery_mode_array as $dm_id => $product_delivery_mode_data_loop) {
                $default_dm = $default_dm == '' ? $dm_id : $default_dm;
                $display_delivery_mode_label_array = [];

                if ($product_type == 4) {
                    $this->loadPhysicalGoodsDesign();
                    $prefill = '';
                    if (isset(Yii::app()->session['physical_goods'])) {
                        $prefill = Yii::app()->session['physical_goods'];
                    }
                    $arr = [];
                    parse_str(
                        $prefill,
                        $arr
                    );
                    $arr = $arr['deliver_addr'];
                    $field_arrays = [
                        [
                            'id' => 'recipient_name',
                            'caption' => 'Name',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'contact_number',
                            'caption' => 'Contact Number',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'addr_1',
                            'caption' => 'Address 1',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'addr_2',
                            'caption' => 'Address 2',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'postcode',
                            'caption' => 'Zip Code',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'city',
                            'caption' => 'City',
                            'type' => 'text',
                            'max_length' => '100',
                            'readonly' => false
                        ],
                        [
                            'id' => 'state',
                            'caption' => 'State',
                            'type' => $this->getStateFieldType(),
                            'readonly' => false,
                            'max_length' => '20',
                            'value' => $this->state_list,
                        ],
                        [
                            'id' => 'country_name',
                            'caption' => 'Country',
                            'type' => 'text',
                            'max_length' => '20',
                            'readonly' => true,
                            'value' => Yii::app()->session['country_name'],
                        ],
                    ];

                    foreach ($field_arrays as $field) {
                        switch ($field['type']) {
                            case 'text':
                                $display_delivery_mode_label_array[] = CHtml::textField(
                                    "deliver_addr[" . $field['id'] . "]",
                                    ($arr[$field['id']] ?? ''),
                                    [
                                        'maxlength' => $field['max_length'],
                                        'size' => '20',
                                        'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                        'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                        'default_text' => '',
                                        'readonly' => $field['readonly'],
                                        'placeholder' => $field['caption'],
                                        'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                    ]
                                );
                                break;
                            case 'dropdown':
                                $display_delivery_mode_label_array[] = CHtml::dropDownList(
                                    "deliver_addr[" . $field['id'] . "]",
                                    ($arr[$field['id']] ?? ''),
                                    ($field['value'] ?? []),
                                    [
                                        'encode' => false,
                                        'default_text' => Yii::t('checkoutModule.checkout', 'PULL_DOWN_DEFAULT'),
                                        'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                        'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                        'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                    ]
                                );
                                break;
                            default:
                                break;
                        }
                        $dm_id = 5;
                    }
                } else {
                    switch ($dm_id) {
                        case '5':
                            break;
                        case '7':
                            break;
                        case '6':
                            $direct_topup_obj = new DirectTopupCom();
                            $main_product_id = $this->products_id;
                            $product_info_array = $this->getProductObj()->getProductBundleInfoByID();

                            if ($product_info_array['actual']['products_bundle'] == 'yes' || $product_info_array['actual']['products_bundle_dynamic'] == 'yes') {
                                $main_product_id = CheckoutPublishersProducts::model()->getBundleProductID(
                                    $this->products_id
                                );
                                $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
                            } else {
                                $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
                            }

                            $direct_topup_obj->publishers_id = $direct_topup_obj->get_product_publisher($main_product_id);

                            if (count($game_input_array)) {
                                // Get Prefill DTU info
                                $prefill_dtu_info_array = $this->get_dtu_game_info($this->products_id);
                                // Get Prefill DTU info

                                foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
                                    $top_up_info_key = $game_input_data_loop['top_up_info_key'];
                                    $dtu_field_title = ($game_input_data_loop['top_up_info_display'] != '' ? $game_input_data_loop['top_up_info_display'] : '');
                                    $default_info = isset($prefill_dtu_info_array[$top_up_info_key]) && notEmpty(
                                        $prefill_dtu_info_array[$top_up_info_key]
                                    ) ? htmlentities(
                                        htmlspecialchars_decode($prefill_dtu_info_array[$top_up_info_key]),
                                        ENT_COMPAT | ENT_QUOTES
                                    ) : '';
                                    switch ($top_up_info_key) {
                                        case 'server':
                                            // server list
                                            $publishers_server = CheckoutPublishersGames::model()->getServerByProductID(
                                                $main_product_id
                                            );
                                            $servers_tmp_array = json_decode($publishers_server, 1);
                                            $servers_array = [];
                                            $servers_array[''] = $dtu_field_title;

                                            if (isset($servers_tmp_array)) {
                                                foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
                                                    $servers_array[$servers_id_loop] = trim($server_name_loop);
                                                }
                                            }

                                            $display_delivery_mode_label_array[] = CHtml::dropDownList(
                                                "game_info[" . $game_input_key_loop . "]",
                                                $default_info,
                                                $servers_array,
                                                [
                                                    'encode' => false,
                                                    'default_text' => Yii::t(
                                                        'checkoutModule.checkout',
                                                        'PULL_DOWN_DEFAULT'
                                                    ),
                                                    'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                    'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                    'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                                ]
                                            );
                                            break;
                                        case 'account_platform':
                                            // account platform
                                            if (!$direct_topup_obj->used_as_text_field($main_product_id)) {
                                                $account_platform_tmp_array = $direct_topup_obj->get_account_platform(
                                                    $main_product_id
                                                );
                                                $account_platform_array = [];
                                                $account_platform_array[''] = $dtu_field_title;

                                                if (isset($account_platform_tmp_array)) {
                                                    foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
                                                        $account_platform_array[$account_platform_id_loop] = trim(
                                                            $account_platform_name_loop
                                                        );
                                                    }
                                                }

                                                $display_delivery_mode_label_array[] = CHtml::dropDownList(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    $account_platform_array,
                                                    [
                                                        'encode' => false,
                                                        'default_text' => Yii::t(
                                                            'checkoutModule.checkout',
                                                            'PULL_DOWN_DEFAULT'
                                                        ),
                                                        'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                        'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                        'class' => 'productListingDTUPreInput',
                                                    ]
                                                );
                                            } else {
                                                $display_delivery_mode_label_array[] = CHtml::textField(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    [
                                                        'maxlength' => '64',
                                                        'size' => '20',
                                                        'onfocus' => 'jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                        'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                        'style' => (isset($_REQUEST['sub_products_id']) ? 'display:none' : false),
                                                        'default_text' => '',
                                                        'placeholder' => $dtu_field_title,
                                                        'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                                    ]
                                                );
                                            }
                                            break;
                                        case 'character':
                                            if ($direct_topup_obj->character_is_sync($main_product_id)) {
                                                $extend_description = in_array($main_product_id, [
                                                        '159290',
                                                        '156279',
                                                        '158728'
                                                    ]
                                                ) ? '<div class="specialnote" style="padding-top:2px"><a href="http://r3k.gamernizer.com/game/server" target="_blank">' . Yii::t(
                                                        'checkoutModule.checkout',
                                                        'TEXT_ORDER_CHANGE_DEFAULT_CHARACTER'
                                                    ) . '</a></div>' : '';

                                                $character_list_array = $direct_topup_obj->get_character_list($direct_topup_obj->publishers_id, $prefill_dtu_info_array, $curl_response_array);

                                                if (empty($character_list_array)) {
                                                    $character_list_array = [
                                                        '' => $dtu_field_title
                                                    ];
                                                }

                                                $display_delivery_mode_label_array[] = CHtml::dropDownList(
                                                        "game_info[" . $game_input_key_loop . "]",
                                                        $default_info,
                                                        $character_list_array,
                                                        [
                                                            'id' => 'dtu_character_sel',
                                                            'encode' => false,
                                                            'default_text' => Yii::t(
                                                                'checkoutModule.checkout',
                                                                'PULL_DOWN_DEFAULT'
                                                            ),
                                                            'onfocus' => 'load_character_list(this, \'' . $this->products_id . '\', \'' . $dm_id . '\');jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                            'onblur' => 'if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                            'class' => 'productListingDTUPreInput' . (count(
                                                                    $character_list_array
                                                                ) > 1 ? ' character_list_loaded' : ''),
                                                        ]
                                                    ) . $extend_description;
                                            } else {
                                                $display_delivery_mode_label_array[] = CHtml::textField(
                                                    "game_info[" . $game_input_key_loop . "]",
                                                    $default_info,
                                                    [
                                                        'maxlength' => '64',
                                                        'size' => '20',
                                                        'onfocus' => 'this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                        'onblur' => 'if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                        'style' => (isset($_REQUEST['sub_products_id']) ? 'display:none' : false),
                                                        'default_text' => '',
                                                        'placeholder' => $dtu_field_title,
                                                        'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                                    ]
                                                );
                                            }
                                            break;
                                        default: // account
                                            $display_delivery_mode_label_array[] = CHtml::textField(
                                                "game_info[" . $game_input_key_loop . "]",
                                                (isset($_REQUEST['sub_products_id']) && !empty($_GET['account']) ? CHtml::encode($_GET['account']) : $default_info),
                                                [
                                                    'maxlength' => '64',
                                                    'size' => '18',
                                                    'encode' => false,
                                                    'onblur' => 'if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                    'default_text' => '',
                                                    'readonly' => (isset($_REQUEST['sub_products_id']) ? true : false),
                                                    'placeholder' => $dtu_field_title,
                                                    'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                                ]
                                            );

                                            if ($direct_topup_obj->retype_account($main_product_id)) {
                                                $default_info = ($default_info == $game_input_data_loop['top_up_info_display']) ? Yii::t(
                                                        'checkoutModule.checkout',
                                                        'TEXT_RETYPE'
                                                    ) . $default_info : $default_info;
                                                $display_delivery_mode_label_array[] = Yii::t(
                                                        'checkoutModule.checkout',
                                                        'TEXT_RETYPE'
                                                    ) . CHtml::textField(
                                                        "game_info[" . $game_input_key_loop . "_2]",
                                                        $default_info,
                                                        [
                                                            'maxlength' => '64',
                                                            'size' => '18',
                                                            'onfocus' => 'this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')',
                                                            'onblur' => 'if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}',
                                                            'default_text' => Yii::t(
                                                                    'checkoutModule.checkout',
                                                                    'TEXT_RETYPE'
                                                                ) . $game_input_data_loop['top_up_info_display'],
                                                            'class' => 'productListingDTUPreInput dtu_customer_input_game_info',
                                                        ]
                                                    );
                                            }
                                            break;
                                    }
                                }
                            }

                            break;
                        default:
                            $display_delivery_mode_label_array[] = $product_delivery_mode_data_loop;
                            break;
                    }
                }
                $delivery_methods_array[$dm_id] = implode("", $display_delivery_mode_label_array);
            }
        } else {
            $delivery_methods_array[5] = Yii::t('checkoutModule.checkout', 'TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT');
        }

        return $delivery_methods_array;
    }

    public function update_dtu_game_info($productID, $data_array, $gameID = null)
    {
        $cookie_array = [];

        if ($gameID === null) {
            $gameID = notEmpty($productID) ? $this->getProductObj()->getGameIDByID($productID) : 0;
        }

        foreach ($data_array as $field => $value) {
            if (notEmpty($value)) {
                $cookie_array[$field] = $value;
            }
        }

        if (count($cookie_array) && notEmpty($gameID)) {
            $prefill_game_info = json_encode($cookie_array);

            $cookie = new CHttpCookie('DTU[' . $gameID . ']', $prefill_game_info);
            $cookie->expire = time() + 60 * 60 * 24 * 365;
            $cookie->path = '/';
            Yii::app()->request->cookies['DTU[' . $gameID . ']'] = $cookie;
        }
    }

    private function get_dtu_game_info($productID)
    {
        $cookie_array = [];

        $prefill_game_info = isset(Yii::app()->request->cookies['DTU']) ? Yii::app()->request->cookies['DTU']->value : [];
        $gameID = notEmpty($productID) ? $this->getProductObj()->getGameIDByID($productID) : 0;

        if (isset(Yii::app()->session['dtu'])) {
            $prefill = '';
            if (isset(Yii::app()->session['dtu'])) {
                $prefill = Yii::app()->session['dtu'];
            }
            $arr = [];
            parse_str(
                $prefill,
                $arr
            );
            $cookie_array = $arr['game_info'];
        } elseif (isset($prefill_game_info[$gameID])) {
            $temp_array = json_decode(($prefill_game_info[$gameID]), true);

            if (is_array($temp_array) && count($temp_array)) {
                foreach ($temp_array as $field => $value) {
                    $value = htmlspecialchars($value);
                    $field = validateRequest(htmlspecialchars($field));

                    if (notEmpty($value) && notEmpty($field)) {
                        $cookie_array[$field] = $value;
                    }
                }
            }

            unset($temp_array);
        }

        return $cookie_array;
    }

    public function getExtraInfo()
    {
        $extra_info_str = '';
        $qty_onchange = 'pfv_confirm_order_qty(this);';

        if (false && $this->delivery_mode == 6) {
            $dtu_calc_array = $this->getProductObj()->getProductsDtuExtraInfo();

            foreach ($dtu_calc_array as $idx => $data) {
                if (notEmpty($data)) {
                    if ($idx == 1) {
                        $extra_info_str .= '<span class="hd3 ext' . $idx . '" data-default="' . $data . '">' . $data . '</span>';
                    } else {
                        if ($idx == 2) {
                            $extra_info_str .= '<span class="hd3 ext' . $idx . '" data-default="' . $data . '">' . $data * $this->buyqty . '</span>';
                        } else {
                            $extra_info_str .= '<span class="hd5 ext' . $idx . '">' . $data . '</span>';
                        }
                    }
                }
            }

            if (notEmpty($extra_info_str)) {
                $qty_onchange = 'updateDTUextra(this);' . $qty_onchange;
                $extra_info_str = '<div class="dtu_extra_info">=&nbsp;' . $extra_info_str . '</div>';
            }

            unset($dtu_calc_array);
        }

        return [$extra_info_str, $qty_onchange];
    }

    function getConfirmStoreCreditBox($customerId, $currency)
    {
        $info['sc_currency_code'] = '';
        $info['location_restriction_currency'] = false;
        $info['sc_restrictions'] = $this->checkPaymentRestriction($this->payment_restrictions_pid);
        $info['debug_mode'] = $this->payment_restrictions_pid;
        $info['sc_convert_in_my_account'] = (isset(Yii::app()->session['RegionGST']['tax_title']) && notNull(
            Yii::app()->session['RegionGST']['tax_title']
        ) ? 1 : 0);
        $classStoreCredit = new CheckoutStoreCreditCom(Yii::app()->user->id);

        if (isset($classStoreCredit->credit_accounts['sc_currency_id'])) {
            $info['sc_currency_code'] = Yii::app()->currency->getCodeById(
                $classStoreCredit->credit_accounts['sc_currency_id']
            );
        }

        // Aditional checking for location restriction currency
        if ($currRestrictionArray = json_decode(RegionalSettingCom::getExcludedCurrencyJSON())) {
            if (in_array($info['sc_currency_code'], $currRestrictionArray)) {
                $info['location_restriction_currency'] = true;
                $info['country_name'] = Yii::app()->session['country_name'];
                $info['currency'] = Yii::app()->session['currency'];
            }
        }

        return $info;
    }

    public function getIndexAndType()
    {
        $returnIndex = 0;
        $returnType = '';

        if (notEmpty($this->index)) {
            if (stripos($this->index, '_') > 0) {
                list($returnType, $index) = explode("_", $this->index);
                $returnIndex = $returnType . '_' . (int)$index;
            } else {
                $returnIndex = (int)$this->index;
            }
        }

        return [$returnIndex, $returnType];
    }

    public function getQtyData()
    {
        $qty_array = [];
        $qty_selection_range_array = range(1, 100);
        foreach ($qty_selection_range_array as $idx) {
            $qty_array[$idx] = $idx;
        }
        return $qty_array;
    }

    // {x}{x} : {0: check sc ,1: skip sc}{1: open confirm box; 2: change qty; 3: confirmed order; 4: proceed checkout}
    public function pfvNextAccess()
    {
        $next = '';

        // {0: check sc ,1: skip sc}
        if (!is_array($this->user_confirmed)) {
            if ($this->confirmed > 0) {
                $this->user_confirmed = [
                    'skip_sc' => floor($this->confirmed / 10),
                    'skip_cfm_order' => ($this->confirmed % 10),
                ];
            } else {
                $this->user_confirmed = [
                    'skip_sc' => 0,
                    'skip_cfm_order' => 0,
                ];
            }
        }

        // Check for products payment methods restrictions
        // {1:only Store Credit, 2:not allow store credit}
        if (!is_array($this->user_confirmed)) {
            if ($this->confirmed > 0) {
                if ($this->checkPaymentRestriction($this->payment_restrictions_pid) == 1) {
                    $this->user_confirmed = [
                        'skip_sc' => 0,
                        'skip_cfm_order' => 0,
                    ];
                } elseif ($this->checkPaymentRestriction($this->payment_restrictions_pid) == 2) {
                    $this->user_confirmed = [
                        'skip_sc' => floor($this->confirmed / 10),
                        'skip_cfm_order' => ($this->confirmed % 10),
                    ];
                }
            }
        }

        // {1: open confirm box; 2: change qty; 3: confirmed order; 4: proceed checkout}
        if (!isset(Yii::app()->user->id)) {
            $next = 'login';
        } else {
            $next = 'confirm_order';

            if ($this->user_confirmed['skip_cfm_order'] === 4) {
                if ($this->getCheckoutObj()->isDormant()) {
                    $next = 'ask_password2';  // ask_qna
                } else {
                    if ($this->getCheckoutObj()->isFullCreditCorvered()) {
                        if (AuthCom::checkIsLifetimeCookiesExisted(-5 * 60) !== true) {
                            $next = 'ask_password';
                        } else {
                            $next = 'go_chkout';
                        }
                    } else {
                        $next = 'go_chkout';
                    }
                }
            }
        }
        $this->next_action = $next;
        return $next;
    }

    function needPopupScChoice($customerId, $regionCurrencyCode)
    {
        $returnBool = true;

        $scObj = new CheckoutStoreCreditCom($customerId);

        if (isset($scObj->credit_accounts['sc_currency_id'])) {
            $scCurrencyCode = Yii::app()->currency->getCodeById($scObj->credit_accounts['sc_currency_id']);

            if ($scCurrencyCode === $regionCurrencyCode || empty($scCurrencyCode)) {
                $returnBool = false;
            } else {
                $scBalance = $scObj->credit_accounts['sc_balance'];

                if ($scBalance <= 0) {
                    $returnBool = false;
                }
            }
        } else {
            $returnBool = false;   // it's possible user does not have sc account
        }

        return $returnBool;
    }

    // wee siong
    public function getBuyNowPreCheckoutDetails()
    {
        $dm = 0;
        $quantity = 0;
        $rebate_point = 0;
        $rebate_point_extra = 0;
        $error_message = '';
        $error_code = 0;
        $ot_info_array = [];

        try {
            $this->getCheckoutObj()->resetFullCreditCorvered();
            $full_cart_details_array = $this->getCartObj()->getCartFullDetails();

            $ot_info_array = $this->getCheckoutObj()->getOTMappingInfo($full_cart_details_array);
            if (($product_array = $full_cart_details_array['request_result']['ot']['products']) !== []) {
                // assume buyNow only got 1 product
                $dm = isset($product_array[0]['custom_content']['delivery_info']['delivery_mode']) ? $product_array[0]['custom_content']['delivery_info']['delivery_mode'] : $this->delivery_mode;
                $quantity = $product_array[0]['quantity'] ?? 0;

                $rebate_point = isset($full_cart_details_array['extra_info']['optotal']) ? $full_cart_details_array['extra_info']['optotal'] : 0;
                $rebate_point_extra = isset($full_cart_details_array['extra_info']['ex_optotal']) ? $full_cart_details_array['extra_info']['ex_optotal'] : 0;
            }

            unset($full_cart_details_array);
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            $error_code = $e->getCode();
            $errors = [
                [
                    'color' => 'errors',
                    'text' => json_encode($error_message)
                ]
            ];
            Yii::app()->slack->send('Fail to get checkout details', $errors, 'DEV_DEBUG');
        }

        if (!empty(Yii::app()->params['checkout_failed'])) {
            $error = Yii::app()->params['error_page_content'];
            throw new CHttpException($error['http_code'], $error['error']);
        }

        $full_sc_checkout = $this->getCheckoutObj()->isFullCreditCorvered();

        $classStoreCredit = new CheckoutStoreCreditCom();
        $store_credit_balance = $this->getCheckoutObj()->getStoreCreditBalanceInfo('balance');
        $store_credit_remain = $this->getCheckoutObj()->getStoreCreditBalanceInfo('remain');

        # overwrite balance array
        if (isset($classStoreCredit->credit_accounts['sc_currency_id'])) {
            $scCode = Yii::app()->currency->getCodeById($classStoreCredit->credit_accounts['sc_currency_id']);

            if ($scCode != Yii::app()->customerCom->getValue('currency')) {
                $store_credit_balance['formatted_value'] = '-';
                $store_credit_remain['formatted_value'] = '-';
            }
        } else {
            $store_credit_balance['formatted_value'] = '-';
            $store_credit_remain['formatted_value'] = '-';
        }

        // Check Store Credit Restrictions
        $sc_checkout_only = $this->products_id;
        if ($this->checkPaymentRestriction($this->products_id) == 1) {
            foreach ($ot_info_array as $key => $val) {
                switch ($key) {
                    case 'order_total':
                        if ($val['value'] > 0) {
                            $sc_checkout_only = 1;
                        }
                        break;
                }
            }
        } elseif ($this->checkPaymentRestriction($this->products_id) == 2) {
            $store_credit_balance['formatted_value'] = '-';
            $store_credit_remain['formatted_value'] = '-';
        }

        unset($classStoreCredit);

        return [
            'show_coupon' => $this->getCartObj()->checkCoupon(),
            'error_message' => $error_message,
            'error_code' => $error_code,
            'delivery_mode' => $dm,
            'quantity' => $quantity,
            'op' => [
                'rebate_point' => $rebate_point,
                'rebate_point_extra' => $rebate_point_extra,
            ],
            'ot' => $ot_info_array,
            'extra_info' => [
                'store_credit_balance' => $store_credit_balance,
                'store_credit_remain' => $store_credit_remain,
                'full_sc_checkout' => $full_sc_checkout,
                'sc_checkout_only' => $sc_checkout_only,
                'debug_mode' => $this->checkPaymentRestriction($this->products_id),
            ],
        ];
    }

    public function validatePassword()
    {
        $userCfmPassword = validateRequest($this->cfm_password, 'string');

        $validate = $this->checkPassword(Yii::app()->user->id, $userCfmPassword);

        if ($validate == 'success') {
            AuthCom::updateLifetimeCookies();
            CheckoutCustomersInfo::model()->clearDormant(Yii::app()->user->id);
            return true;
        }

        $this->addError('cfm_password', Yii::t('checkoutModule.checkout', 'ERROR_WRONG_PASSWORD'));

        return false;
    }

    private function checkPaymentRestriction($products_id)
    {
        // {0:allow all payment, 1:only Store Credit, 2:not allow store credit
        $pm_restrictions = 0;
        $restriction_array = [];
        // Check for products payment methods restrictions
        if ($products_id) {
            $products_payment_restrictions = $this->getProductObj()->getProductsPaymentMethodsRestrictions(
                $products_id
            );
            $restriction_array = explode(",", $products_payment_restrictions['restriction_info']);
            if ($restriction_array) {
                if ($products_payment_restrictions['restriction_mode'] == 'Allow') { // Restriction mode "Allow"
                    if (count($restriction_array) == 1 && in_array(
                            '0',
                            $restriction_array
                        )) { // only 1 pm in array & it is SC$re_ha
                        $pm_restrictions = 1;
                    } elseif (!in_array('0', $restriction_array)) { // no SC in array
                        $pm_restrictions = 2;
                    }
                } elseif ($products_payment_restrictions['restriction_mode'] == 'Disallow') { // Restriction mode "Disallow"
                    if (count($restriction_array) == 1 && in_array(
                            '0',
                            $restriction_array
                        )) { // only 1 pm in array & it is SC
                        $pm_restrictions = 2;
                    } elseif (count($restriction_array) == 1 && !in_array(
                            '0',
                            $restriction_array
                        )) { // only 1 pm in array & not SC
                        $pm_restrictions = 0;
                    } elseif (count($restriction_array) > 1 && in_array(
                            '0',
                            $restriction_array
                        )) { // more than 1 pm & SC
                        $pm_restrictions = 2;
                    } elseif (count($restriction_array) > 1 && !in_array(
                            '0',
                            $restriction_array
                        )) { // more than 1 pm & no SC
                        $count = PipwavePaymentMapperBase::model()->countByAttributes(['site_id' => '0']);
                        if (count($restriction_array) == $count) { // All pm except SC
                            $pm_restrictions = 1;
                        } else {
                            $pm_restrictions = 0;
                        }
                    }
                }
            }
        }
        return $pm_restrictions;
    }

    public function getBreadcrumbs()
    {
        $return_array = [];
        $return_array[] = Yii::t('checkoutModule.checkout', 'BREADCRUMB_TITLE_CHECKOUT');
        $this->breadcrumbs = $return_array;
        return $return_array;
    }

    public function getBreadcrumbsWidgetContent($link = '')
    {
        $link = $link == '' ? $this->breadcrumbs : $link;

        return [
            'links' => $link,
            'encodeLabel' => false,
            'homeLink' => '<li class="breadcrumbs__item"><a href="/" class="breadcrumbs__holder"><svg class="icon-home"><use xlink:href="#home"></use></svg></a><svg class=\'icon-arrow-side-small breadcrumbs__decor\'><use xmlns:xlink=\'http://www.w3.org/1999/xlink\' xlink:href=\'#arrow-sm-right\'></use></svg></li>',
            'activeLinkTemplate' => "<li class='breadcrumbs__item'> <a href='{url}' class='breadcrumbs__holder'>{label} </a> <svg class='icon-arrow-side-small breadcrumbs__decor'><use xmlns:xlink='http://www.w3.org/1999/xlink' xlink:href='#arrow-sm-right'></use></svg> </li>",
            'inactiveLinkTemplate' => '<li class="breadcrumbs__item"><span class="breadcrumbs__holder breadcrumbs__holder--static">{label}</span></li>',
            'htmlOptions' => ['class' => 'breadcrumbs breadcrumbs--noindent'],
        ];
    }

    public function setResponseArrayError($response_array = [])
    {
        if ($errors_array = $this->getErrors()) {
            $response_array['err'] = 1;
            foreach ($errors_array as $key => $error_array) {
                if (in_array($key, ['buyqty'])) {
                    $response_array['msg4qty'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                } else {
                    if (in_array($key, ['game_info'])) {
                        $response_array['msg4dm'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                    } else {
                        if (!isset($response_array['msg4top'])) {
                            $response_array['msg4top'] = CheckoutCartCom::getErrorMessage($error_array[0]);
                        }
                    }
                }
            }
        }
        return $response_array;
    }

    protected function reportError($error_str, $ext_subject = '')
    {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($error_str);
        echo "========================================================<BR>";
        echo "Request : " . getenv('REQUEST_URI');
        $debug_html = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' PreCheckoutForm Error - ' . date(
                "F j, Y H:i"
            );
        $attachments = [
            [
                'color' => 'danger',
                'text' => $debug_html,
            ],
        ];
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

    public function loadPhysicalGoodsDesign()
    {
        $registerCss = 'input { margin-top : 5px; width : 90%; padding : 7px 10px; border-radius :4px; border : 1px solid #ccc; font-size : 12px; color : #2e3141; line-height : normal;}
        input:disabled{background-color : #e6e9ef;} input[type="text"]::placeholder{color : #999; padding-left : 3px;}';

        Yii::app()->clientScript->registerCss('physical-goods', $registerCss);
    }

    public function getStateFieldType()
    {
        $states = $this->getStates();
        if (empty($states)) {
            $this->state_list = "";
            return 'text';
        }
        $this->state_list = $states;

        return 'dropdown';
    }

    public function getStates()
    {
        $zones_model = new Zones();
        $states = $zones_model->getStatesList(Yii::app()->session['country']);

        return $states;
    }
}
