<?php
$delivery_section_content = '';

if (count($dm_info)) {
    $delivery_section_content = '<div class="tooltips">' . $dm_info['tooltips'] . '</div>';
    $delivery_section_content .= CHtml::beginForm('', 'post', array('id' => 'dm_form', 'name' => 'dm_form', 'onsubmit' => 'return false;')) . $dm_info['html'] . '</form>';
    $delivery_section_content .= '<div class="error_msg icon_err_msg"></div>';
    $delivery_section_content .= '<div class="dmnote">' . $dm_info['note'] . '</div>';
}
?>

<tr>
    <td class="dm"><?php echo $delivery_section_content; ?></td>
    <td class="qty">
        <span class="hds3"><?php echo Yii::t('checkoutModule.checkout', 'ENTRY_QTY'); ?>:</span> 
        <div class="shd1" style="padding-top: 15px; width: 100px;"><?php echo CHtml::dropDownList('listname', $qty_info['select'], $qty_info['data'], array('id' => 'pfv_qty', 'style' => 'width: 100px;', 'onchange' => $qty_info['onchange'])); ?></div> 
        <?php echo $extra_info_str; ?>
        <div class="error_msg icon_err_msg"></div> 
    </td>
    <td class="ot"><?php echo $ot_content; ?></td>
</tr>