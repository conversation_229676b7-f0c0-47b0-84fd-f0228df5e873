<?php
$this->breadcrumbs = $model->getBreadcrumbs();

if (isset($this->breadcrumbs)) {
    $this->widget('frontend.widgets.Breadcrumbs', $model->getBreadcrumbsWidgetContent());
}

if (!empty($_GET['retry'])) {
    $url = str_replace('&retry=1', '', $_SERVER['REQUEST_URI']);
    if (!empty($_GET['error_msg'])) {
        $msg = strip_tags($_GET['error_msg']);
        $url = str_replace('&error_msg=' . urlencode($_GET['error_msg']), '', $url);
    } else {
        $msg = 'Please try again';
    }

    Yii::app()->clientScript->registerScript("js", <<<JS
jQuery(function($) {
      Swal.fire(
      'Failed to create order',
      '$msg',
      'error'
    )
  window.history.pushState('page', 'Title', '$url');
});
JS
        , CClientScript::POS_END);
}

echo $convertSc;

?>
<div class="steps">
    <div class="container">
        <div class="steps__holder">
            <div class="steps__item is-active">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_CART'); ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line"></span>
            <div class="steps__item y">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_PAYMENT'); ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line steps__line--last"></span>
            <div class="steps__item ">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_SUCCESS'); ?><span class="steps__item-point"></span></span>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <?php
    $currency_code = strtoupper(Yii::app()->session['currency']);
    if (!in_array($currency_code, ['MYR', 'USD', 'SGD']) && isset(Yii::app()->currency->currencies[$currency_code])) {
        ?>

        <div class="currency-select" id="currency-select">
            <span class="currency-option" id="pay-in-text">Pay in</span>
            <span class="currency-option" id="normal-size">
            <a href="#" onclick="set_localization_value('USD' , '/checkout/buyNow/buy' , '<?php echo htmlentities(json_encode($_GET)) ?>');">
                <span class="desktop-view"><?php echo Yii::app()->currency->currencies['USD']['title'] ?>(USD)</span>
                <span class="mobile-view">USD</span>
            </a>
        </span>
            <span class="currency-option active" id="normal-size">
            <a href="#"
               onclick="set_localization_value('<?php echo $currency_code ?>' , '/checkout/buyNow/buy' , '<?php echo htmlentities(json_encode($_GET)) ?>');">
                <span class="desktop-view"><?php echo Yii::app()->currency->currencies[$currency_code]['title'] . "($currency_code)" ?></span>
                <span class="mobile-view"><?php echo $currency_code; ?></span>
            </a>
        </span>
        </div>
        <?php
    }
    ?>
    <?php
    if ($gstInfo = Yii::app()->localizationCom->showGSTAlertBar(Yii::app()->params['GST_NOTICE_BY_COOKIE'])) {
        echo "<div style='margin-bottom:20px;'>";
        $this->widget('frontend.widgets.Template.GstalertWidget', array(
            'country_name' => $gstInfo['country_name'],
            'title' => $gstInfo['title'],
            'percentage' => $gstInfo['percentage'],
            'display_title' => $gstInfo['display_title'],
            'display_message' => $gstInfo['display_message'],
            'use_cookie' => Yii::app()->params['GST_NOTICE_BY_COOKIE'],
            'attributes_info' => array('style' => 'margin-bottom:0px'),
            'business_status' => $gstInfo['business_status'],
            'start_datetime' => $gstInfo['start_datetime'],
        ));
        echo "</div>";
    }
    ?>

    <div class="precheckout-box">
        <div class="precheckout-box__row">
            <div class="precheckout-box__col precheckout-box__col--wide">
                <div class="precheckout">
                    <div class="precheckout__holder">
                        <div class="precheckout__row precheckout__row--heading">
                            <div class="precheckout__col precheckout__col--head precheckout__col--name"><?php echo Yii::t('myogm', 'TITLE_PRODUCT_LIST'); ?>
                            </div>
                            <div class="precheckout__col precheckout__col--head precheckout__col--quanity">
                                <?php echo Yii::t('myogm', 'TITLE_QTY'); ?>
                            </div>
                            <div class="precheckout__col precheckout__col--head precheckout__col--amount">
                                <span class="precheckout__amount"><?php echo Yii::t('myogm', 'TITLE_AMOUNT_LOWERCASE'); ?></span>
                            </div>
                        </div>

                        <div class="precheckout__row">
                            <div class="precheckout__col precheckout__col--name">
                                <span class="precheckout__title"><?php echo $products["name"]; ?></span> <br>
                                <?php
                                $dtu_info = $dm_info['html'][$products['default_dm']];
                                if (!empty($dtu_info)) {
                                    echo CHtml::beginForm('', 'post', array('id' => 'dm_form', 'name' => 'dm_form', 'onsubmit' => 'return false;'));
                                    echo $dtu_info;
                                    echo Chtml::endForm();
                                }
                                ?>
                            </div>
                            <div class="precheckout__col precheckout__col--quanity">
                                <span class="precheckout__subtitle">Quantity:</span>
                                <div class="precheckout__quanity js-quanity">
                                    <div class="wan-spinner wan-spinner-1" data-min="<?php echo $products['min_quantity'] ?>" data-max="<?php echo $products['max_quantity'] ?>">
                                        <a href="javascript:void(0)" class="minus">-</a>
                                        <input id="buyQty" type="text" value="<?php echo $qty_info['select']; ?>">
                                        <a href="javascript:void(0)" class="plus">+</a>
                                    </div>
                                    <br>
                                    <?php
                                    if (!empty($extra_info_str)) {
                                        echo $extra_info_str;
                                    }

                                    ?>
                                </div>
                            </div>
                            <div style="display:none">
                                <input id="data-dm" value="<?php echo $products['default_dm']; ?>">
                                <input id="data-pm" value="<?php echo $products['promo_type']; ?>">
                                <input id="data-bd" value="<?php echo $products['products_bundle']; ?>">
                                <input id="data-id" value="<?php echo $products['products_id']; ?>">
                            </div>
                            <div class="precheckout__col precheckout__col--amount">
                                    <span class="precheckout__amount"><span class="precheckout__subtitle precheckout__subtitle--inline"><?php echo Yii::t('myogm',
                                                'TITLE_AMOUNT_LOWERCASE'); ?>: </span><span class="item__price"><?php echo $price; ?></span>
					</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php echo $ot_content; ?>
        </div>
        <div class="precheckout-box__more">
            <?php
            if ($show_checkout_tnc) {
                ?>
                <div class="precheckout__tnc">
                    <form action="javascript:void(0)">
                        <label>
                            <input id="checkout_tnc" type="checkbox" required>
                            <?php echo Yii::t('page_content', 'CHECKOUT_CONFIRM_MESSAGE'); ?>
                        </label>
                        <button id="checkout_tnc_confirm" type="submit"></button>
                    </form>
                </div>
                <?php
            }
            if (!$not_enough_sc) { ?>
                <button id="confirm_checkout" type="button" onclick="pfv_confirm_order();" class="btn btn-blue btn-side-indents btn-inline precheckout-box__more-link">
                    <span class="btn__text"><?php echo Yii::t('buttons', 'BTN_CHECKOUT'); ?></span>
                    <span class="btn__icon"><svg class="icon-arrow-side-md"><use xlink:href="#arrow-right"></use></svg></span>
                </button>
            <?php } else { ?>
                <button id="sc_topup" type="button" onclick="window.location ='/account/store-credit/index'" class="btn btn-blue btn-side-indents btn-inline precheckout-box__more-link">
                    <span class="btn__text"><?php echo Yii::t('checkoutModule.checkout', 'BTN_PROCEED_TO_CHECKOUT3'); ?></span>
                </button>
            <?php } ?>
        </div>
    </div>
</div>
<?php (new GoogleTagManager())->addToCart($products, $model); ?>

<?php
Yii::app()->clientScript->registerScript('i18n-trans', 'translate.full_sc_checkout ="' . Yii::t('checkoutModule.checkout', 'TEXT_FULL_STORE_CREDIT_CHECKOUT') . '";', CClientScript::POS_END);
?>
