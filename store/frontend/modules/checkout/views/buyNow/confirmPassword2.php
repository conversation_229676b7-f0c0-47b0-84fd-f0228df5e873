<div style="border-bottom: 1px dotted #cecece;padding: 0 0 15px;">
    <?php
    $form = $this->beginWidget('CActiveForm', array(
        'id' => 'cfm_password_form',
        'htmlOptions' => array('OnSubmit' => 'jQuery("#cfm_password").click();return false;'),
    ));
    echo CHtml::hiddenField('index', $index);
    ?>
    <table border="0" cellspacing="0" cellpadding="1" height="100%" width="450px">
        <tr>
            <td colspan="3" style="padding:0px;">
                <div style="background-color: #FFFDDB">
                    <div class="error_msg" style="line-height: 25px;padding-left:5pt;"></div>
                </div>
            </td>
        </tr>
        <tr><td colspan="3"><?php echo Yii::t('checkoutModule.checkout', 'CONFIRM_LOGIN_PASSWORD_DESC2'); ?></td></tr>
        <tr><td>&nbsp;</td></tr>
        <tr>
            <td style="width: 20%;padding:5px 0px;" nowrap><?php echo Yii::t('checkoutModule.checkout', 'TEXT_PASSWORD'); ?></td>
            <td><div class="ihd1">
                    <?php echo CHtml::passwordField('cfm_password', '', array('id' => 'cfm_password_txt', 'class' => 'passwordField', 'size' => 50, 'onfocus' => 'this.value=""', 'style' => 'width:250px;')); ?>
                </div></td>
        </tr>
    </table>
    <?php $this->endWidget(); ?>
</div>
<div style="text-align: center; padding-top: 15px;">
    <?php echo imageButton2('gray_short', "javascript:void(0);", Yii::t('checkoutModule.general', 'BTN_CONFIRM'), 80, ' id="cfm_password" onclick="pfv_cfm_password2(this.id)"') ?>
</div>