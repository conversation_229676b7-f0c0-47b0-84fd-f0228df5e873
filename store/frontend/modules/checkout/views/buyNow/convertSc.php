<!-- The Modal -->
<div id="myModal" class="modal" style="display:block;">

    <!-- Modal content -->
    <div class="modal-content" style="top: calc(50% - 200px);">
        <div class="modal-body" style="
    margin-bottom: 50px;
">
            <div style="
    display: block;
    height: 20px;
"><a href="javascript:void(0)" class="icon-remove-round precheckout__remove"
                        onclick="(function(){$('#myModal').hide();}())" style="
    right: 20px;
    top: 20px;
">
                    <svg class="icon-cross-xsmall  icon-remove-round__decor">
                        <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#cross"></use>
                    </svg>
                </a></div>
            <p style="
    font: 500 13px &quot;Helvetica Neue&quot;,Helvetica,sans-serif, Verdana;
    color: black;
    font-size: 15px;
    margin-bottom: 8px;
">
            <p><?php echo Yii::t('checkoutModule.checkout', 'TEXT_STORE_CREDITS'); ?></p><br>
            <?php
            if ($info['sc_restrictions'] !== 2) {
                if ($info['location_restriction_currency']) {
                    ?>
                    <div>
                        <span class="span"><?php echo Yii::t('checkoutModule.checkout', 'TEXT_STORE_CREDIT_POPUP_CURRENCY_RESTRICTED') ?></span>
                    </div>
                    <hr>
                <?php } else { ?>
                    <div>
                        <span class="span"><?php echo sprintf(Yii::t('checkoutModule.checkout', 'TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY'), $info['sc_currency_code']) ?></span>
                        <button type="button" id="rvp_2" class="btn btn-buy-narrow btn-green  store-credit__btn"
                                onclick="set_localization_value('<?php echo $info['sc_currency_code'] . "' , '" . $url  . "' , '" . $params; ?>');">
                            <?php echo sprintf(Yii::t('checkoutModule.checkout', 'IMAGE_BUTTON_PAY_WITH_SC_CURRENCY'), $info['sc_currency_code']); ?>
                        </button>
                    </div>
                    <hr>
                <?php } ?>
            <?php } ?>
            <?php
            if ($info['sc_restrictions'] !== 1){
            ?>
            <div>
                <span class="span"><?php echo Yii::t('checkoutModule.checkout', 'TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE'); ?></span>
                <button type="button" id="rvp_2" class="btn btn-buy-narrow btn-green  store-credit__btn"
                        onclick="$('#myModal').hide();"><?php echo Yii::t('checkoutModule.checkout', 'IMAGE_BUTTON_PAY_WITHOUT_SC'); ?></button>
            </div>
        </div>
        <?php } ?>
    </div>

</div>