<div class="precheckout-box__col precheckout-box__col--tight">
    <div class="common-description">
        <div class="common-description__holder">
            <ul class="common-description__list">
                <li class="common-description__item">
                    <span class="common-description__col common-description__col--val">
                        <span class="common-description__inner"><?php echo $cartInfo['ot']['subtotal']['label']; ?></span>
                    </span> <span class="common-description__col common-description__col--text">
                        <strong class="common-description__inner"><?php echo $cartInfo['ot']['subtotal']['display_value']; ?></strong>
                    </span>
                </li>
                <?php
                $coupon = (isset($cartInfo['ot']['coupon']) ? $cartInfo['ot']['coupon'] : null);
                $coupon_class = '';
                if ($coupon && empty($coupon['code']) && $coupon['error_label'] != '') {
                    $coupon_class = 'is-active is-error';
                }
                ?>
                <?php if ((isset($cartInfo['show_coupon']) && $cartInfo['show_coupon'])) { ?>
                    <li class="common-description__item js-discount-holder <?php echo $coupon_class; ?>">
                        <span class="common-description__col common-description__col--val common-description__col--color-blue">
                            <span class="common-description__inner">
                                <a href="#" class="common-description__link js-discount-trigger"><?php echo Yii::t('checkoutModule.checkout', 'TEXT_REDEEM_DISCOUNT_COUPON'); ?></a>
                                <span class="common-description__input">
                                    <input id="data-coupon" type="text" class="input" data-placeholder=" " data-mask="9-9-9-9-9-9" placeholder="Enter coupon code">
                                </span>
                                <span class="common-description__error" <?php echo (!empty($GLOBALS['error_label']) ? 'style="display:block;"' : '') ?>>
                                    <?php echo (!empty($GLOBALS['error_label']) ? $GLOBALS['error_label'] : ''); ?></span>
                            </span>
                        </span>
                        <span class="common-description__col common-description__col--text common-description__col--color-gray" style="vertical-align: top">
                            <strong class="common-description__inner">
                                <span class="common-description__inner-text"><?php echo $coupon['display_value']; ?></span>
                                <a href="javascript:void(0)" onclick="pfv_cfm_coupon();" class="btn btn-green-border common-description__btn">Redeem</a>
                            </strong>
                        </span>
                    </li>
                <?php } ?>
                <li class="common-description__item common-description__item--decor">
                    <span class="common-description__col common-description__col--val">
                        <span class="common-description__inner"><?php echo $cartInfo['ot']['store_credit']['label']; ?></span>
                    </span>
                    <span class="common-description__col common-description__col--text common-description__col--color-gray">
                        <strong class="common-description__inner"><?php echo $cartInfo['ot']['store_credit']['display_value']; ?></strong>
                    </span>
                </li>
                <?php
                    if(!empty($cartInfo['ot']['order_gst']['value'])){
                ?>
                <li class="common-description__item">
                    <span class="common-description__col common-description__col--val">
                        <span class="common-description__inner"><?php echo $cartInfo['ot']['order_gst']['label']; ?></span>
                    </span>
                    <span class="common-description__col common-description__col--text">
                        <strong class="common-description__inner"><?php echo $cartInfo['ot']['order_gst']['display_value']; ?></strong>
                    </span>
                </li>
                <?php
                    }
                ?>
                <li class="common-description__item">
                    <span class="common-description__col common-description__col--val">
                        <strong class="common-description__inner"><?php echo $cartInfo['ot']['order_total']['label']; ?></strong>
                    </span> <span class="common-description__col common-description__col--text">
                        <strong class="common-description__inner"><?php echo $cartInfo['ot']['order_total']['display_value']; ?></strong>
                    </span>
                </li>
                <?php
                if (isset($cartInfo['op']['rebate_point']) && $cartInfo['op']['rebate_point'] > 0) {
                    ?>
                    <li class="common-description__item">
                        <span class="common-description__col common-description__col--val common-description__col--size">
                            <span class="common-description__inner"><?php echo Yii::t('checkoutModule.checkout', 'TEXT_TOTAL_REBATE'); ?></span>
                        </span>
                        <span class="common-description__col common-description__col--text common-description__col--size">
                            <span class="common-description__inner">
                                <strong class="common-description__wor"><svg class="icon-wor-md-yel common-description__wor-icon"><use xlink:href="#op-color"></use></svg><?php echo $cartInfo['op']['rebate_point']; ?></strong>
                            </span>
                        </span>
                    </li>
                <?php } ?>
                <?php
                if (isset($cartInfo['op']['rebate_point_extra']) && $cartInfo['op']['rebate_point_extra'] > 0) {
                    ?>
                    <li class="common-description__item">
                        <span class="common-description__col common-description__col--val common-description__col--size">
                            <span class="common-description__inner"><?php echo Yii::t('checkoutModule.checkout', 'TEXT_TOTAL_REBATE'); ?></span>
                        </span>
                        <span class="common-description__col common-description__col--text common-description__col--size">
                            <span class="common-description__inner">
                                <strong class="common-description__wor"><svg class="icon-wor-md-yel common-description__wor-icon"><use xlink:href="#op-color"></use></svg><?php echo $cartInfo['op']['rebate_point_extra']; ?></strong>
                            </span>
                        </span>
                    </li>
                <?php } ?>
                <li class="common-description__item">
                    <span class="common-description__col common-description__col--val common-description__col--size">
                        <span class="common-description__inner"><?php echo $cartInfo['extra_info']['store_credit_remain']['label']; ?></span>
                    </span>
                    <span class="common-description__col common-description__col--text common-description__col--size">
                        <strong class="common-description__inner"><?php echo $cartInfo['extra_info']['store_credit_remain']['formatted_value']; ?></strong>
                    </span>
                </li>
                <li style="display:none">
                    <input id="data-full-store-credit" value="<?php echo($cartInfo['extra_info']['full_sc_checkout'] ? 1 : 0); ?>">
                    <input id="data-full-store-credit-value" value="<?php echo $cartInfo['ot']['store_credit']['display_value']; ?>">
                    <?php
                    if (isset($cartInfo['extra_info']['sc_checkout_only']) && $cartInfo['extra_info']['sc_checkout_only'] == 1) {
                        echo '<input id="data-not-enough-sc" value="1">';
                    }
                    ?>
                </li>
            </ul>
        </div>
    </div>
</div>
