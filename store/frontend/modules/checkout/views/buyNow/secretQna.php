<script type="text/javascript">
    var TEXT_ENTER_6_SERUCITY_TOKEN = '<?php echo Yii::t('smsToken', 'TEXT_ENTER_6_SERUCITY_TOKEN'); ?>';
</script>
<?php echo CHtml::hiddenField('index', $index, array('id' => 'index'));
?>

<div id="qna_table" class="<?php echo Yii::app()->session['language']; ?>">
    <form class="form-inline">
    <table class="tbl_borderless" width="100%">
        <tr>
            <td colspan="2">
                <div style="padding-bottom: 5px;">
                    <span style="font-size: 14px;font-weight: bold;"><?php echo Yii::t('smsToken', 'TITLE_SECURITY_TOKEN'); ?></span>
                    <?php echo CHtml::link('<img src="' . Yii::app()->frontPageCom->getMainUIStaticIconURL('help-small.png') . '">', 'javascript:void(0)', array('data-title' => Yii::t('smsToken', 'MSG_PHONE_WARNING'), 'id' => 'tt_precheckout_dormant', 'data-placement' => 'bottom', 'data-toggle' => 'tooltip', 'class' => 'dmTT', 'style' => 'text-decoration:none')); ?>
                    <script>ogm_tooltip("tt_precheckout_dormant");</script>
                </div>
            </td>
        </tr>
        <tbody id="sms_token">
        <tr>
            <td colspan="2">
                <div style="padding-bottom: 15px;"><?php echo Yii::t('smsToken', 'TEXT_DORMANT_ACCOUNT_QNA_REQUEST'); ?></div>
            </td>
        </tr>
        <tr><td colspan="2"><div class="error_msg"></div></td></tr>
        <tr>
            <td class="col_token">
                <div class="form-group">
                <?php
                echo CHtml::textField('tokenAnswer', Yii::t('smsToken', 'TEXT_ENTER_6_SERUCITY_TOKEN'), array(
                    'autocomplete' => 'off',
                    'style' => 'width: 220px;',
                    'class' => 'tokenAnswer form-control input-sm',
                    'onFocus' => 'hideDefaultText()',
                    'onBlur' => 'displayDefaultText()')
                );
                ?>
                </div>
            </td>
            <td class="col_btn">
                <div class="req">
                    <img src='<?php echo Yii::app()->frontPageCom->getMainUIStaticIconURL('icon_lock.gif'); ?>'>
                    <?php
                    echo CHtml::htmlButton(Yii::t('smsToken', 'BTN_REQUEST_SECURITY_TOKEN'), array(
                        'id' => 'submitBtn',
                        'class' => 'btn btn-default',
                        'onClick' => "js:triggerToken('request_sms_token')",
                        'style' => 'padding-left: 25px;'
                    ));
                    ?>
                </div>
                <div class="res">
                    <?php
                    echo CHtml::link(Yii::t('smsToken', 'BTN_RESEND_TOKEN'), 'javascript:void(0);', array(
                        'id' => 'resendBtn',
                        'onClick' => "js:triggerToken('request_resend_sms_token')"
                    ));
                    ?>
                </div>
                <div class="tim">
                    <span class="lbl_left"><?php echo Yii::t('smsToken', 'TEXT_RESEND_COUNTER_LEFT_LABEL'); ?></span>&nbsp;<span class="lbl_num"></span>&nbsp;<span class="lbl_right"><?php echo Yii::t('smsToken', 'TEXT_RESEND_COUNTER_RIGHT_LABEL'); ?></span>
                </div>
                <div class="load"><i class="fa fa-refresh fa-spin fa-2x fa-fw"></i></div>
            </td>
        </tr>
        <tr>
            <td id="lostLink" colspan="2" style="text-align: center;padding: 10px 0">
                <?php echo Yii::t('smsToken', 'TEXT_LOST_PHONE', array(
                    '{SUPPORT_US_LINK}' => Yii::app()->frontPageCom->getPageSupportLink(),
                    '{LOST_PHONE_LINK}' => "triggerToken('clk_lost_phone');",
                )); ?>
            </td>
        </tr>
        <tr>
            <td colspan="2" align="center" style="padding:10px 0px;">
                <?php
                echo CHtml::htmlButton(Yii::t('CheckoutModule.general', 'BTN_CONFIRM'), array(
                    'id' => 'submitBtn',
                    'class' => 'btn btn-default',
                    'onClick' => "js:cfm_qna()"
                ));
                ?>
            </td>
        </tr>
        </tbody>

        <tbody id="email_token" style="display: none;">
        <tr>
            <td colspan="2">
                <div style="padding-bottom: 15px;"><?php echo Yii::t('smsToken', 'TEXT_ENTER_LAST_4_DIGIT'); ?></div>
            </td>
        </tr>
        <tr><td colspan="2"><div class="error_msg"></div></td></tr>
        <tr>
            <td style="width: 250px;text-align: right;padding-right: 10px">
                <div class="form-group">
                <?php
                echo CHtml::openTag('input', array(
                    'type' => 'number',
                    'name' => 'emailToken',
                    'autocomplete' => 'off',
                    'style' => 'width: 220px;',
                    'maxlength' => 4,
                    'class' => 'tokenAnswer form-control input-sm')
                );
                ?>
                </div>
            </td>
            <td style="position: relative;">
                <?php
                echo CHtml::htmlButton(Yii::t('smsToken', 'BTN_REQUEST_EMAIL_TOKEN'), array(
                    'id' => 'submitBtn',
                    'class' => 'btn btn-default',
                    'onClick' => "js:triggerToken('request_email_token')"
                ));
                ?>
            </td>
        </tr>
        <tr>
            <td id="lostLink" colspan="2" style="text-align: center;padding: 10px 0">
                <?php echo Yii::t('smsToken', 'TEXT_BACK', array(
                    '{BACK_LINK}' => "triggerToken('clk_back_to_sms_token')"
                )); ?>
            </td>
        </tr>
        </tbody>
    </table>
    </form>
</div>
           