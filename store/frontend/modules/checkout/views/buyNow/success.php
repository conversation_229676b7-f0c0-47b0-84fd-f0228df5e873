<div class="steps">
    <div class="container">
        <div class="steps__holder">
            <div class="steps__item">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_CART'); ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line"></span>
            <div class="steps__item y">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_PAYMENT'); ?><span class="steps__item-point"></span></span>
            </div>
            <span class="steps__line steps__line--last"></span>
            <div class="steps__item ">
                <span class="steps__item-text"><?php echo Yii::t('checkoutModule.checkout', 'HEADER_BAR_SUCCESS'); ?><span class="steps__item-point"></span></span>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="success-info">
        <div class="success-info__icon">
            <svg class="icon-checkmark">
            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#checkmark"></use>
            </svg>
        </div>
        <span class="success-info__title"><?php echo Yii::t('CheckoutModule.general', 'TEXT_ORDER_RECEIVED'); ?></span>
        <span class="success-info__subheading"><?php echo Yii::t('CheckoutModule.general', 'TEXT_ORDER_THANK_YOU'); ?></span>
        <span class="success-info__btn"> <a
                href="<?php echo Yii::app()->createUrl('account/orderDetail', array('orders_id' => $orderId)); ?>"
                class="btn btn-blue-border btn-inline success-info__btn-link" target="_blank"> <span
                    class="btn__text"><?php echo Yii::t('myogm', 'BTN_VIEW_ORDER_DETAIL'); ?></span> <span
                    class="btn__icon">
                    <svg class="icon-arrow-side-md">
                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-right"></use>
                    </svg>
                </span> </a> </span>
        <ul class="success-info__list">
            <li class="success-info__item">
                <span class="success-info__subtitle">You order number</span> <span
                    class="success-info__text"><?php echo $orderId; ?></span>
            </li>
            <li class="success-info__item">
                <span class="success-info__subtitle">Order date</span> <span
                    class="success-info__text"><?php echo $orderSummary['date_purchased']; ?></span>
            </li>
            <li class="success-info__item">
                <span class="success-info__subtitle">Store Credit balance</span> <span
                    class="success-info__text"><?php echo $orderSummary['store_credit_balance']; ?></span>
            </li>
            <li class="success-info__item">
                <span class="success-info__subtitle">OP balance</span> <span
                    class="success-info__text success-info__text--indent">
                    <svg class="icon-wor-md-yel success-info__text-icon">
                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#op-color"></use>
                    </svg><?php echo $orderSummary['wor_token_balance']; ?></span>
            </li>
        </ul>
    </div>
</div>

<?php
if ($gtm_str_trackTrans != '') {
    $gtmPushScript = "jQuery(function($) {dataLayer.push({$gtm_str_trackTrans});});";
    Yii::app()->clientScript->registerScript('gtm-trackTrans-' . $orderId, $gtmPushScript, CClientScript::POS_READY);
}

if ($gtm_str_order_success != '') {
    $gtmPushScript = "jQuery(function($) {dataLayer.push({$gtm_str_order_success});});";
    Yii::app()->clientScript->registerScript('gtm-order_success-' . $orderId, $gtmPushScript, CClientScript::POS_READY);
}

if ($gtm_str_purchase != '') {
    $gtmPushScript = "jQuery(function($) {dataLayer.push({$gtm_str_purchase});});";
    Yii::app()->clientScript->registerScript('gtm-purchase-' . $orderId, $gtmPushScript, CClientScript::POS_READY);
}
?>