<?php

class OrderCom extends CApplicationComponent
{

    const CHANGE_BY = 'OGM system';
    const FILENAME = 'orders.php';
    const IDENTITY = 0;
    const LOCK_LOG = '##l_1##';
    const MOVE_ORDER = 'OGM_ORDER_RUN_GENESIS';

    public $order_id;
    public $info, $customer, $products;
    public $ot_obj, $ot_total, $product_obj;

    public function __construct($_cart)
    {
        $this->info = [];
        $this->products = [];
        $this->customer = [];

        Yii::app()->language = Yii::app()->session['language'];

        if (isset($_cart['order_id'])) {
            $GLOBALS['cart'] = $_cart;

            $this->order_id = $_cart['order_id'];
            $this->updateOrder();
            $this->query();
        } else {
            $this->ot_obj = new OrderTotalCom($_cart);
            $this->ot_total = $this->ot_obj->process();
            if (!empty($this->ot_total) && !isset($this->ot_total['code']) && !isset($this->ot_total['error'])) {
                if (isset($GLOBALS['cart'])) {
                    return $this->cart();
                }
            } else {
                return $this->ot_total;
            }
        }
    }

    private function cart()
    {
        $c_customer = $GLOBALS['cart']['customer'];
        $c_info = $GLOBALS['cart']['checkout_info'];
        $c_product = $GLOBALS['cart']['products'];
        $c_payment = $GLOBALS['cart']['payment_info'];

        $m_customer = OrderCustomers::model()->customerDetail($c_customer['id']);
        if (!empty($m_customer)) {
            $telephone = '';

            # customer detail
            if ((isset($m_customer->customers_telephone) && notNull($m_customer->customers_telephone)) && (isset($m_customer->customers_country_dialing_code_id) && notNull($m_customer->customers_country_dialing_code_id))) {
                $telephone = parseTelephone($m_customer->customers_telephone, $m_customer->customers_country_dialing_code_id, 'id');
            }

            $m_dialcode = OrderCustomers::model()->internationalDialingCode($c_customer['id']);

            $paymentMethodInstanceInfo = '';
            $paymentMethodInstance = OrderPaymentMethodsInstance::model()->getPaymentEmailInfo($c_payment['payment_method_id'], $c_info['currency']);
            if (isset($paymentMethodInstance->OrderPaymentMethodsInstanceSetting->payment_methods_instance_setting_value)) {
                $paymentMethodInstanceInfo = $paymentMethodInstance->OrderPaymentMethodsInstanceSetting->payment_methods_instance_setting_value;
            }

            $this->info = [
                'order_status' => 1,
                'currency' => $c_info['currency'],
                'currency_value' => Yii::app()->currency->getValue($c_info['currency'], 'sell'),
                'payment_method' => [
                    'title' => $c_payment['title'],
                    'payment_methods_id' => $c_payment['payment_method_id'],
                    'payment_methods_parent_id' => $c_payment['payment_method_parent_id'],
                    'paypal_ipn_id' => $c_payment['paypal_ipn_id'],
                    'email_info' => $paymentMethodInstanceInfo,
                ],
                'subtotal' => $c_info['subtotal'],
                'total' => $c_info['total'],
                'remote_addr' => $c_info['ip'],
                'payment_ip_addr' => $c_info['payment_ip'],
                'payment_ip_country' => isset($c_info['payment_ip_country']) ? $c_info['payment_ip_country'] : '-',
                'tax_info' => (!empty($c_info['tax_info']) ? $c_info['tax_info'] : ''),
                'products_tax_info' => (!empty($c_info['products_tax_info']) ? $c_info['products_tax_info'] : ''),
            ];

            $this->customer = [
                'id' => $m_customer->customers_id,
                'firstname' => $m_customer->customers_firstname,
                'lastname' => $m_customer->customers_lastname,
                'gender' => $m_customer->customers_gender,
                'company' => isset($m_customer->ab->entry_company) ? $m_customer->ab->entry_company : '',
                'street_address' => isset($m_customer->ab->entry_street_address) ? $m_customer->ab->entry_street_address : '',
                'suburb' => isset($m_customer->ab->entry_suburb) ? $m_customer->ab->entry_suburb : '',
                'city' => isset($m_customer->ab->entry_city) ? $m_customer->ab->entry_city : '',
                'postcode' => isset($m_customer->ab->entry_postcode) ? $m_customer->ab->entry_postcode : '',
                'state' => (isset($m_customer->ab->entry_state) && notNull($m_customer->ab->entry_state) ? $m_customer->ab->entry_state : (isset($m_customer->ab->zones->zone_name) && notNull($m_customer->ab->zones->zone_name) ? $m_customer->ab->zones->zone_name : '')),
                'zone_id' => isset($m_customer->ab->entry_zone_id) ? $m_customer->ab->entry_zone_id : '',
                'country' => [
                    'id' => isset($m_customer->ab->countries->countries_id) ? $m_customer->ab->countries->countries_id : '',
                    'title' => isset($m_customer->ab->countries->countries_name) ? $m_customer->ab->countries->countries_name : '',
                    'iso_code_2' => isset($m_customer->ab->countries->countries_iso_code_2) ? $m_customer->ab->countries->countries_iso_code_2 : '',
                    'iso_code_3' => isset($m_customer->ab->countries->countries_iso_code_3) ? $m_customer->ab->countries->countries_iso_code_3 : '',
                ],
                'address_book_id' => isset($m_customer->customers_default_address_id) ? $m_customer->customers_default_address_id : '',
                'format_id' => isset($m_customer->ab->countries->address_format_id) ? $m_customer->ab->countries->address_format_id : '',
                'telephone_country_name' => isset($m_dialcode->countries->countries_name) ? $m_dialcode->countries->countries_name : '',
                'int_dialing_code' => isset($m_dialcode->countries->countries_international_dialing_code) ? $m_dialcode->countries->countries_international_dialing_code : '',
                'telephone' => $telephone,
                'email_address' => $m_customer->customers_email_address,
                'customers_groups_id' => $m_customer->customers_groups_id,
            ];

            $this->products = $c_product;
        }
    }

    public function createOrder()
    {
        $time = time();
        $dbTime = date("Y-m-d H:i:s", $time);
        $is_sanction = Yii::app()->customerCom->isPhoneSanctionCountry(true);
        if (isset($this->order_id) && notNull($this->order_id)) {
            $_result = [
                'status' => false,
                'code' => 'order',
                'extra_param_1' => $this->order_id,
                'error' => Yii::t('orderModule.OrderCom', 'ERROR_ORDER_IN_PROGRESS'),
            ];
        } elseif (empty($this->customer['id'])) {
            $_result = [
                'status' => false,
                'code' => 'order',
                'error' => Yii::t('orderModule.OrderCom', 'ERROR_DISABLED_CUSTOMER_OR_NOT_EXIST'),
            ];
        } elseif (!$is_sanction) {
            $_result = $this->preOrderRules(0, 1);

            if (isset($_result['status']) && ($_result['status'])) {
                $preorder_product_id_array = isset($_result['extra_params']['preorder_product_id_array']) ? $_result['extra_params']['preorder_product_id_array'] : [];
                $_result = [];

                $m_tp = OrderTempProcess::model()->getCheckoutLock($this->customer['id']);
                if (isset($m_tp->temp_id)) {
                    if ((int)$m_tp->check_time) {
                        $_result = [
                            'status' => false,
                            'code' => 'order',
                            'extra_param_1' => $this->customer['id'],
                            'error' => Yii::t('orderModule.OrderCom', 'ERROR_ORDER_IN_PROGRESS'),
                        ];
                    } else {
                        $m_attr = ['created_date' => $dbTime];
                        $m_cond = 'temp_id = :tid';
                        $m_params = [':tid' => $m_tp->temp_id];
                        OrderTempProcess::model()->updateAll($m_attr, $m_cond, $m_params);
                    }
                } else {
                    $m_attr = [
                        'page_name' => 'OGMOrderModule',
                        'match_case' => $this->customer['id'],
                        'created_date' => $dbTime,
                    ];
                    OrderTempProcess::model()->saveNewRecord($m_attr);
                    unset($m_attr);
                }

                if (empty($_result)) {
                    $m_attr = [
                        'customers_id' => $this->customer['id'],
                        'customers_name' => $this->customer['firstname'] . ' ' . $this->customer['lastname'],
                        'customers_company' => $this->customer['company'],
                        'customers_street_address' => $this->customer['street_address'],
                        'customers_suburb' => $this->customer['suburb'],
                        'customers_city' => $this->customer['city'],
                        'customers_postcode' => $this->customer['postcode'],
                        'customers_state' => $this->customer['state'],
                        'customers_country' => $this->customer['country']['title'],
                        'customers_telephone_country' => $this->customer['telephone_country_name'],
                        'customers_country_international_dialing_code' => $this->customer['int_dialing_code'],
                        'customers_telephone' => $this->customer['telephone'],
                        'customers_email_address' => $this->customer['email_address'],
                        'customers_address_format_id' => $this->customer['format_id'],
                        'customers_groups_id' => $this->customer['customers_groups_id'],
                        'delivery_name' => $this->customer['firstname'] . ' ' . $this->customer['lastname'],
                        'delivery_company' => $this->customer['company'],
                        'delivery_street_address' => $this->customer['street_address'],
                        'delivery_suburb' => $this->customer['suburb'],
                        'delivery_city' => $this->customer['city'],
                        'delivery_postcode' => $this->customer['postcode'],
                        'delivery_state' => $this->customer['state'],
                        'delivery_country' => $this->customer['country']['title'],
                        'delivery_address_format_id' => (int)$this->customer['format_id'],
                        'billing_name' => $this->customer['firstname'] . ' ' . $this->customer['lastname'],
                        'billing_company' => $this->customer['company'],
                        'billing_street_address' => $this->customer['street_address'],
                        'billing_suburb' => $this->customer['suburb'],
                        'billing_city' => $this->customer['city'],
                        'billing_postcode' => $this->customer['postcode'],
                        'billing_state' => $this->customer['state'],
                        'billing_country' => $this->customer['country']['title'],
                        'billing_address_format_id' => $this->customer['format_id'],
                        'payment_method' => $this->info['payment_method']['title'],
                        'payment_methods_id' => $this->info['payment_method']['payment_methods_id'],
                        'payment_methods_parent_id' => $this->info['payment_method']['payment_methods_parent_id'],
                        'paypal_ipn_id' => $this->info['payment_method']['paypal_ipn_id'],
                        'date_purchased' => $dbTime,
                        'last_modified' => $dbTime,
                        'orders_status' => $this->info['order_status'],
                        'currency' => $this->info['currency'],
                        'remote_addr' => $this->info['remote_addr'],
                        'currency_value' => $this->info['currency_value'],
                    ];
                    $this->order_id = OrderOrders::model()->saveNewRecord($m_attr, true);
                    unset($m_attr);

                    $m_attr = [
                        'orders_id' => $this->order_id,
                        'counter' => 0,
                        'check_date' => $dbTime,
                    ];
                    OrderCronOrdersPaymentStatus::model()->saveNewRecord($m_attr);
                    unset($m_attr);

                    # capture actual checkout country
                    $_info = Yii::app()->geoip->getIPCountryInfo($this->info['remote_addr']);
                    if (isset($_info['id']) && !empty($_info['id'])) {
                        OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'ip_country', $_info['id']);
                    }

                    # record checkout Site-ID
                    OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'site_id', Yii::app()->params['SITE_ID']);

                    # record payment IP Address
                    OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'payment_ip', $this->info['payment_ip_addr']);

                    # record payment IP Country
                    if ($this->info['payment_ip_country'] !== '-') {
                        OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'payment_ip_country', $this->info['payment_ip_country']);
                    }

                    # order total
                    $country_code = '';
                    $tax_percentage = 0;
                    $orders_total_amount = 0;
                    $zero_price_products = false;
                    $sc_used = false;

                    if (!empty($this->ot_total)) {
                        $ot_allow_zero_amt = ['ot_subtotal', 'ot_total', 'ot_gst'];

                        for ($i = 0, $cnt = count($this->ot_total); $cnt > $i; $i++) {
                            if (($this->ot_total[$i]['storage_value'] > 0) || (($this->ot_total[$i]['storage_value'] == 0) && in_array($this->ot_total[$i]['code'], $ot_allow_zero_amt))) {
                                $m_attr = [
                                    'orders_id' => $this->order_id,
                                    'title' => $this->ot_total[$i]['title'],
                                    'text' => $this->ot_total[$i]['storage_text'],
                                    'value' => $this->ot_total[$i]['storage_value'],
                                    'class' => $this->ot_total[$i]['code'],
                                    'sort_order' => $this->ot_total[$i]['sort_order'],
                                ];
                                OrderOrdersTotal::model()->saveNewRecord($m_attr);
                                unset($m_attr);

                                /* -- GST :: record GST percentage -- */
                                if (($this->ot_total[$i]['code'] == 'ot_gst')) {
                                    if (isset($this->ot_total[$i]['output']) && $this->ot_total[$i]['output']) {
                                        foreach ($this->ot_total[$i]['output'] as $output_key => $output_val) {
                                            OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_' . $output_key, $output_val);
                                            // get country code
                                            if ($output_key == 'country') {
                                                $country_code = $output_val;
                                            }
                                            // get the tax amount
                                            if ($output_key == 'percentage') {
                                                $tax_percentage = $output_val;
                                            }
                                        }
                                    }
                                }

                                if (($this->ot_total[$i]['code'] == 'ot_total')) {
                                    $orders_total_amount = $this->ot_total[$i]['storage_value'];
                                }

                                if (($this->ot_total[$i]['code'] == 'ot_subtotal') && (double) $this->ot_total[$i]['storage_value'] == 0) {
                                    $zero_price_products = true;
                                }

                                if (($this->ot_total[$i]['code'] == 'ot_gv') && (double) $this->ot_total[$i]['storage_value'] > 0) {
                                    $sc_used = true;
                                }
                            }
                        }
                    }

                    if ($orders_total_amount > 0) {
                        OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'checkoutSite', 'pipwave');
                    }

                    // Add to orders_extra_info for regular tax percentage
                    if ($country_code && $tax_percentage) {
                        // find current percentage
                        $tax_info = OrderOrdersTaxConfiguration::model()->getTaxNormalPercentage($country_code);
                        if ($tax_percentage != $tax_info->orders_tax_percentage) {
                            // find tax descriptions EN language
                            OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_normal_percentage', $tax_info->orders_tax_percentage);
                            // check if reverse need to calculate?
                            if ($tax_info->business_tax_percentage == 0 && $tax_info->business_tax_status == 1 && $tax_info->orders_include_reverse_charge == 1) {
                                OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_invoice_reverse_charge', $tax_info->orders_include_reverse_charge);
                                $reverse_tax_amount = round(($orders_total_amount * $tax_info->orders_tax_percentage / 100), 2);
                                OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_invoice_reverse_amount', $reverse_tax_amount);
                            }
                        }
                        // for invoice snapshot
                        $tax_desc = OrderOrdersTaxConfigurationDescription::model()->getTaxDescription($tax_info->orders_tax_id, 1);
                        OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_short_title', $tax_desc->orders_tax_title_short);
                        OrderOrdersExtraInfo::model()->insertOrdersExtraInfo($this->order_id, 'tax_invoice_title', $tax_info->tax_invoice_title);
                    }

                    $m_attr = [
                        'orders_id' => $this->order_id,
                        'orders_status_id' => $this->info['order_status'],
                        'date_added' => $dbTime,
                        'customer_notified' => '1',
                        'comments' => (isset($GLOBALS['cart']['update_comment']) ? $GLOBALS['cart']['update_comment'] : ''),
                        'changed_by' => (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY),
                    ];
                    OrderOrdersStatusHistory::model()->saveNewRecord($m_attr);
                    OrderOrdersStatusStat::model()->updateOrdersStatusCounter($m_attr);
                    unset($m_attr);

                    # record purchased product(s)
                    if (!empty($this->products)) {
                        for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                            $m_attr = [
                                'orders_id' => $this->order_id,
                                'products_id' => $this->products[$i]['id'],
                                'products_model' => $this->products[$i]['model'], // added for ogm
                                'products_name' => $this->products[$i]['name'],
                                'orders_products_store_price' => $this->products[$i]['storage_price']['normal_price'],
                                'products_price' => $this->products[$i]['storage_price']['price'],
                                'final_price' => $this->products[$i]['storage_price']['final_price'],
                                'op_rebate' => ($this->products[$i]['op_info']['rebate_point'] + $this->products[$i]['op_info']['rebate_point_extra']),
                                'op_rebate_delivered' => 0,
                                'products_quantity' => $this->products[$i]['quantity'],
                                'products_pre_order' => (in_array($this->products[$i]['id'], $preorder_product_id_array) ? 1 : 0),
                                'custom_products_type_id' => $this->products[$i]['custom_products_type_id'],
                                'products_categories_id' => $this->products[$i]['products_categories_id'],
                            ];
                            $opid = OrderOrdersProducts::model()->saveNewRecord($m_attr, true);

                            OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'products_type',
                                (!empty($this->products[$i]['products_type']) ? $this->products[$i]['products_type'] : 0));
                            OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'sub_products_id',
                                (!empty($this->products[$i]['sub_products_id']) ? $this->products[$i]['sub_products_id'] : 0));

                            if ($tax_percentage) {
                                if (!empty($this->info['products_tax_info'][$this->products[$i]['id']])) {
                                    $tax_item = $this->info['products_tax_info'][$this->products[$i]['id']];
                                    OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'tax_amount', $tax_item['tax_amount']);
                                    if (!isset($tax_item['bundle_items'])) {
                                        OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'tax_type', $tax_item['tax_type']);
                                        OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, strtolower('tax_' . $tax_item['tax_type'] . '_formula'), $tax_item['remarks']);
                                    }
                                }
                            }

                            unset($m_attr);

                            #Extra OP
                            Yii::app()->currency->rebate_point_formula = $this->products[$i]['op_info']['rebate_point_formula'];
                            if ($this->products[$i]['op_info']['rebate_point_extra'] > 0) {
                                OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'OP_EXTRA', $this->products[$i]['op_info']['rebate_point_extra']);
                                Yii::app()->currency->rebate_point_formula = Yii::app()->currency->rebate_point_formula . '<br /><br />Extra OP: ' . $this->products[$i]['op_info']['rebate_point_extra'];
                            }

                            #OP
                            OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'OP_FORMULA', Yii::app()->currency->rebate_point_formula);
                            Yii::app()->currency->reset();

                            if ($country_code && $tax_percentage) {
                                $tax_exempted_products_list = ProductsBase::model()->getTaxExemptedProductsList();
                                if (in_array($this->products[$i]['id'], $tax_exempted_products_list)) {
                                    OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, 'tax_exempted_amount', $this->products[$i]['storage_price']['final_price']);
                                }
                            }

                            # Record Order Product is bundle
                            foreach ($this->products[$i]['products_package'] as $subproduct_id => & $subproduct_array) {
                                $m_attr = [
                                    'orders_id' => $this->order_id,
                                    'products_id' => $subproduct_id,
                                    'products_model' => $subproduct_array['model'], // added for ogm
                                    'products_name' => $subproduct_array['name'],
                                    'orders_products_store_price' => $subproduct_array['normal_store_price'],
                                    'products_price' => '0.00',
                                    'final_price' => '0.00',
                                    'op_rebate' => 0,
                                    'op_rebate_delivered' => 0,
                                    'products_quantity' => $subproduct_array['qty'] * $this->products[$i]['quantity'],
                                    'products_bundle_id' => $this->products[$i]['id'],
                                    'parent_orders_products_id' => $opid,
                                    'products_categories_id' => $this->products[$i]['products_categories_id'],
                                ];

                                $subproduct_array['op_id'] = OrderOrdersProducts::model()->saveNewRecord($m_attr, true);
                                unset($m_attr);

                                if ($tax_percentage) {
                                    if (!empty($this->info['products_tax_info'][$this->products[$i]['id']])) {
                                        $tax_item = $this->info['products_tax_info'][$this->products[$i]['id']];
                                        foreach ($tax_item['bundle_items'] as $bundle_sub_item) {
                                            if ($bundle_sub_item['products_id'] == $subproduct_id) {
                                                OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($subproduct_array['op_id'], 'tax_amount', $bundle_sub_item['tax_amount']);
                                                OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($subproduct_array['op_id'], 'tax_type', $bundle_sub_item['tax_type']);
                                                OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($subproduct_array['op_id'],
                                                    'tax_' . strtolower($bundle_sub_item['tax_type']) . '_formula', $bundle_sub_item['remarks']);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            # Record custom content
                            if (isset($this->products[$i]['custom_content']['delivery_info']) && is_array($this->products[$i]['custom_content']['delivery_info'])) {
                                foreach ($this->products[$i]['custom_content']['delivery_info'] as $_key => $_val) {
                                    if (!empty($_val)) {
                                        OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($opid, $_key, $_val);
                                    }
                                }


                                //save address
                                if(isset($this->products[$i]['custom_content']['deliver_addr'])) {
                                    $delivery_address = $this->products[$i]['custom_content']['deliver_addr'];
                                    $delivery_address['orders_products_id'] = $opid;
                                    OrderDeliveryAddress::model()->saveNewRecord($delivery_address);
                                }

                                $_saved = false;
                                if ($this->products[$i]['custom_content']['delivery_info']['delivery_mode'] == 6 && isset($this->products[$i]['custom_content']['top_up_info'])) {
                                    $info_key_array = array_keys($this->products[$i]['custom_content']['top_up_info']);

                                    if ($this->products[$i]['products_package']) {
                                        # Bundle
                                        foreach ($this->products[$i]['products_package'] as $subproduct_id => $subproduct_array) {
                                            # some reason it needs dm for sub-op, op id already holding the delivery info anyway this required to double confirm again before can remove
                                            foreach ($this->products[$i]['custom_content']['delivery_info'] as $_key => $_val) {
                                                OrderOrdersProductsExtraInfo::model()->insertOrderProductsExtraInfo($subproduct_array['op_id'], $_key, $_val);
                                            }

                                            if ($top_up_info_array = OrderTopUpInfo::model()->getTopUpInfo($info_key_array, $subproduct_id)) {
                                                foreach ($this->products[$i]['custom_content']['top_up_info'] as $extra_key => $extra_val) {
                                                    $m_attr = [
                                                        'orders_products_id' => $subproduct_array['op_id'],
                                                        'top_up_info_id' => $top_up_info_array[$extra_key],
                                                        'top_up_value' => $extra_val ? $extra_val : '',
                                                    ];
                                                    OrderCustomersTopUpInfo::model()->saveNewRecord($m_attr, true);
                                                }
                                            }
                                        }
                                    } else {
                                        # Single
                                        if ($top_up_info_array = OrderTopUpInfo::model()->getTopUpInfo($info_key_array, $this->products[$i]['id'])) {
                                            foreach ($this->products[$i]['custom_content']['top_up_info'] as $extra_key => $extra_val) {
                                                $m_attr = [
                                                    'orders_products_id' => $opid,
                                                    'top_up_info_id' => $top_up_info_array[$extra_key],
                                                    'top_up_value' => $extra_val ? $extra_val : '',
                                                ];
                                                $_saved = OrderCustomersTopUpInfo::model()->saveNewRecord($m_attr, true);
                                            }
                                        }
                                    }
                                }

                                //Temporary Code to Track Missing DTU Info
                                if (!empty($this->products[$i]['custom_content']['delivery_info']['delivery_mode']) && $this->products[$i]['custom_content']['delivery_info']['delivery_mode'] == 6 && (empty($this->products[$i]['custom_content']['top_up_info']) || !$_saved)) {
                                    $data = [
                                        'orders_id' => $this->order_id,
                                        'request_params' => $_REQUEST,
                                        'products_info' => $this->products,
                                        'cart' => $GLOBALS['cart'],
                                    ];

                                    $attachments = [
                                        [
                                            'color' => 'danger',
                                            'text' => json_encode($data),
                                        ],
                                    ];

                                    Yii::app()->slack->send('*[frontend] Missing DTU Account Info*', $attachments, 'DEV_DEBUG');
                                }
                            }

                            # Register Order Custom Product
                            if ($this->products[$i]['custom_products_type_id'] == 3) {
                                # Store Credit
                                // $customer_sc_currency_obj = OrderCouponGvCustomer::model()->findByPK($this->customer['id']);

                                if (isset($this->products[$i]['custom_content']['orders_sc_currency_code'])) {
                                    $order_sc_currency_id = Yii::app()->currency->getIdByCode($this->products[$i]['custom_content']['orders_sc_currency_code']);
                                } else {
                                    $sc_balance = (new StoreCreditCom($this->customer['id']))->_get_current_credits_balance(true);
                                    $customer_sc_currency_obj = $sc_balance;
                                    $order_sc_currency_id = isset($customer_sc_currency_obj['sc_currency_id']) ? $customer_sc_currency_obj['sc_currency_id'] : 0;
                                    unset($customer_sc_currency_obj);
                                }

                                $m_attr = [
                                    'products_id' => $this->products[$i]['id'],
                                    'orders_products_id' => $opid,
                                    'orders_custom_products_key' => 'store_credit_currency',
                                    'orders_custom_products_value' => $order_sc_currency_id ? $order_sc_currency_id : Yii::app()->currency->getIdByCode($this->info['currency']),
                                    'orders_custom_products_number' => 0,
                                ];
                                OrderOrdersCustomProducts::model()->saveNewRecord($m_attr, true);

                                # Store Credit promotion
                                $sc_promotion_percentage = StoreCreditCom::get_sc_promotion_percentage($this->customer['id']);

                                if ($sc_promotion_percentage > 0) {
                                    $store_credit_currency_array = [
                                        'products_id' => $this->products[$i]['id'],
                                        'orders_products_id' => $opid,
                                        'orders_custom_products_key' => 'store_credit_promotion_percentage',
                                        'orders_custom_products_value' => $sc_promotion_percentage,
                                        'orders_custom_products_number' => 0,
                                    ];
                                    OrderOrdersCustomProducts::model()->saveNewRecord($store_credit_currency_array, true);
                                }
                            }
                        }
                    }
                    $_result = ['order_id' => $this->order_id];

                    $move_order = $this->ot_obj->applyCredit($this->order_id);

                    if (($move_order && $orders_total_amount <= 0) || $zero_price_products || ($sc_used === false  && $orders_total_amount == 0)) {
                        # clear checkout quantity cache
                        if (!empty($this->products)) {
                            for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                                if ($this->products[$i]['products_package']) {
                                    # Bundle
                                    foreach ($this->products[$i]['products_package'] as $subproduct_id => $subproduct_array) {
                                        $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . OrderProductsCheckoutSetting::model()->tableName() . '/exceed_limit_info/array/customers_id/' . $this->customer['id'] . '/products_id/' . $subproduct_id;
                                        Yii::app()->cache->delete($cache_key);
                                    }
                                } else {
                                    $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . OrderProductsCheckoutSetting::model()->tableName() . '/exceed_limit_info/array/customers_id/' . $this->customer['id'] . '/products_id/' . $this->products[$i]['id'];
                                    Yii::app()->cache->delete($cache_key);
                                }
                            }
                        }

                        $this->postOrderRules(0, $this->info['order_status']);

                        $m_attr = [
                            'customers_id' => $this->customer['id'],
                            'orders_id' => $this->order_id,
                            'orders_type' => 'CO',
                            'site_id' => Yii::app()->params['SITE_ID'],
                        ];
                        OrderOrdersNotification::model()->saveNewRecord($m_attr);
                        unset($m_attr);
                        $_result['move_order'] = true;
                        // $this->sendOrderNotification();
                    }

                    OrderTempProcess::model()->deleteAll('page_name = "OGMOrderModule" AND match_case = :cid', [':cid' => $this->customer['id']]);

                    // TODO add script to move order to cancel when store credit payment fail
//                    if (!$move_order) {
//                        throw new Exception('Fail To Deduct Store Credit');
//                    }
                }
            }
            $msorder = new MsOrderModel();
            $msorder->pushOrderQueue($this->order_id);
        } else {
            $_result = [
                'status' => false,
                'code' => 'order',
                'extra_param_1' => ($is_sanction ? 'Sanction Country' : 'Unknown Error'),
                'error' => Yii::t('orderModule.OrderCom', 'ERROR_ORDER_IN_PROGRESS'),
            ];
        }

        return $_result;
    }

    private function query()
    {
        $m_order = OrderOrders::model()->findByPk($this->order_id);
        if (isset($m_order->customers_country) && notNull($m_order->customers_country)) {
            $m_country = OrderCountries::model()->findByAttributes(['countries_name' => $m_order->customers_country]);
        }

        if (isset($m_order->orders_id)) {
            $m_ot = OrderOrdersTotal::model()->findAll([
                'order' => 'sort_order',
                'condition' => 'orders_id = :oid',
                'params' => [':oid' => $this->order_id],
            ]);

            foreach ($m_ot as $_key => $_val) {
                $this->ot_total[] = [
                    'title' => $_val->title,
                    'text' => $_val->text,
                    'value' => $_val->value,
                    'class' => $_val->class,
                ];

                if ($_val->class == 'ot_subtotal') {
                    $ot_subtotal = $_val;
                } else {
                    if ($_val->class == 'ot_total') {
                        $ot_total = $_val;
                    }
                }
            }

            $m_os = OrderOrdersStatus::model()->findByPk([
                'orders_status_id' => $m_order->orders_status,
                'language_id' => 1,
            ]);

            $paymentMethodInstanceInfo = '';
            $paymentMethodInstance = OrderPaymentMethodsInstance::model()->getPaymentEmailInfo($m_order->payment_methods_id, $m_order->currency);
            if (isset($paymentMethodInstance->OrderPaymentMethodsInstanceSetting->payment_methods_instance_setting_value)) {
                $paymentMethodInstanceInfo = $paymentMethodInstance->OrderPaymentMethodsInstanceSetting->payment_methods_instance_setting_value;
            }

            $this->info = [
                'currency' => $m_order->currency,
                'currency_value' => $m_order->currency_value,
                'payment_method' => [
                    'title' => $m_order->payment_method,
                    'payment_methods_id' => $m_order->payment_methods_id,
                    'payment_methods_parent_id' => $m_order->payment_methods_parent_id,
                    'paypal_ipn_id' => $m_order->paypal_ipn_id,
                    'email_info' => $paymentMethodInstanceInfo,
                ],
                'subtotal_value' => $ot_subtotal->value,
                'total_value' => $ot_total->value,
                'orders_status_id' => $m_order->orders_status,
                'orders_status' => (isset($m_os->orders_status_name) ? $m_os->orders_status_name : ''),
                'remote_addr' => $m_order->remote_addr,
                'last_modified' => $m_order->last_modified,
                'subtotal' => strip_tags($ot_subtotal->text),
                'total' => strip_tags($ot_total->text),
                'orders_aft_executed' => $m_order->orders_aft_executed,
                'date_purchased' => $m_order->date_purchased,
            ];

            $this->customer = [
                'id' => $m_order->customers_id,
                'name' => $m_order->customers_name,
                'company' => $m_order->customers_company,
                'street_address' => $m_order->customers_street_address,
                'suburb' => $m_order->customers_suburb,
                'city' => $m_order->customers_city,
                'postcode' => $m_order->customers_postcode,
                'state' => $m_order->customers_state,
                'country' => $m_order->customers_country,
                'telephone_country' => (notNull($m_order->customers_telephone_country) ? $m_order->customers_telephone_country : (isset($m_country->countries_name) ? $m_country->countries_name : '')),
                'order_country_code' => (notNull($m_order->customers_country_international_dialing_code) ? $m_order->customers_country_international_dialing_code : (isset($m_country->countries_international_dialing_code) ? $m_country->countries_international_dialing_code : '')),
                'format_id' => $m_order->customers_address_format_id,
                'telephone' => $m_order->customers_telephone,
                'email_address' => $m_order->customers_email_address,
                'customers_groups_id' => $m_order->customers_groups_id,
            ];

            $m_prod = OrderOrdersProducts::model()->findAll('t.orders_id = :oid', [':oid' => $this->order_id]);
            if (!empty($m_prod)) {
                $products = [];

                foreach ($m_prod as $_key) {
                    if ($_key->products_bundle_id == 0) {
                        # Main product
                        $m_dm = OrderOrdersProductsExtraInfo::model()->findByAttributes([
                            'orders_products_id' => $_key->orders_products_id,
                            'orders_products_extra_info_key' => 'delivery_mode',
                        ]);

                        $products[$_key->products_id] = [
                            'id' => $_key->products_id,
                            'name' => $_key->products_name,
                            'quantity' => $_key->products_quantity,
                            'delivered_qty' => $_key->products_delivered_quantity,
                            'custom_products_type_id' => $_key->custom_products_type_id,
                            'products_categories_id' => $_key->products_categories_id,
                            'is_compensate' => $_key->orders_products_is_compensate,
                            'products_package' => [],
                            'model' => $_key->products_model,
                            'custom_content' => [
                                'delivery_info' => isset($m_dm->orders_products_extra_info_value) ? $m_dm->orders_products_extra_info_value : '',
                            ],
                            'storage_price' => [
                                'normal_price' => $_key->orders_products_store_price,
                                'products_price' => $_key->products_price,
                                'final_price' => $_key->final_price,
                            ],
                            #Query additional attr
                            'orders_products_id' => $_key->orders_products_id,
                        ];

                        if($products[$_key->products_id]['custom_products_type_id'] == 3){
                            $products[$_key->products_id]['sc_currency'] = OrderOrdersCustomProducts::getSCDeliveryCurrency($_key->products_id,$_key->orders_products_id);
                        }
                    }
                }

                foreach ($m_prod as $_key) {
                    if ($_key->products_bundle_id > 0) {
                        # Bundle package
                        $m_dm = OrderOrdersProductsExtraInfo::model()->findByAttributes([
                            'orders_products_id' => $_key->orders_products_id,
                            'orders_products_extra_info_key' => 'delivery_mode',
                        ]);

                        $products[$_key->products_bundle_id]['products_package'][$_key->products_id] = [
                            'qty' => bcdiv($_key->products_quantity, $products[$_key->products_bundle_id]['quantity']),
                            'name' => $_key->products_name,
                            'model' => $_key->products_model,
                            'normal_store_price' => $_key->orders_products_store_price,
                            'delivery_info' => [
                                'delivery_mode' => isset($m_dm->orders_products_extra_info_value) ? $m_dm->orders_products_extra_info_value : '',
                            ],
                            #Query additional attr
                            'orders_products_id' => $_key->orders_products_id,
                        ];
                    }
                }

                $this->products = array_values($products);
            }
        }
    }

    public function processOrder()
    {
        $_result = [];

        if (!empty($this->info)) {
            if (isset($GLOBALS['cart']['update_to_status']) && !empty($GLOBALS['cart']['update_to_status'])) {
                $_result = $this->updateOrderStatus($GLOBALS['cart']['update_to_status']);

                if (isset($_result['status']) && $_result['status']) {
                    unset($_result);
                    # Send order update
                    $this->statusUpdateNotification('C', $this->order_id, (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY), $this->info['orders_status_id'],
                        $GLOBALS['cart']['update_to_status'], 'A');

                    $m_order = OrderOrders::model()->findByPk($this->order_id);
                    $_result = [
                        'order_id' => $m_order->orders_id,
                        'order_status' => $m_order->orders_status,
                    ];
                }
            } else {
                $_result = [
                    'code' => 'order',
                    'error' => Yii::t('orderModule.OrderCom', 'ERROR_INVALID_ORDER_STATUS'),
                ];
            }
        } else {
            $_result = [
                'code' => 'order',
                'error' => sprintf(Yii::t('orderModule.OrderCom', 'ERROR_INVALID_ORDER'), $this->order_id),
            ];
        }

        return $_result;
    }

    public function reviewOrder($payment_status = 'Review')
    {
        $result = false;

        if (!empty($this->order_id)) {
            $result = $this->customerInfoVerification($payment_status);
        }

        return $result;
    }

    public function sendOrderNotification()
    {
        if (!empty($this->customer)) {
            $addressbook = (isset($this->customer['address_book_id']) ? $this->customer['address_book_id'] : '');
            $firstname = '';
            $fullname = '';
            $lastname = '';
            $mail = '';

            if (isset($this->customer['firstname'])) {
                $firstname = $this->customer['firstname'];
                $fullname .= $this->customer['firstname'] . ' ';
            } else {
                $firstname = $fullname = $this->customer['name'] . ' ';
            }

            if (isset($this->customer['lastname'])) {
                $lastname = $this->customer['lastname'];
                $fullname .= $this->customer['lastname'];
            } else {
                $lastname = $fullname = $this->customer['name'];
            }

            $gender = (isset($this->customer['gender']) ? $this->customer['gender'] : '');

            $STORE_OWNER = $this->getSendMailInfo('STORE_OWNER');
            $STORE_OWNER_EMAIL_ADDRESS = Yii::app()->params['MAIL_SENDER']['ORDER'];
            $STORE_NAME = $this->getSendMailInfo('STORE_NAME');
            $STORE_EMAIL_SIGNATURE = $this->getSendMailInfo('STORE_EMAIL_SIGNATURE');
            $EMAIL_SUBJECT_PREFIX = $this->getSendMailInfo('EMAIL_SUBJECT_PREFIX');

            $mail .= FrontendMailCom::getEmailGreeting($firstname, $lastname, $gender);
            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_ORDER_THANK_YOU', [
                    '{SUBTOTAL}' => Yii::app()->currency->format($this->info['subtotal'], false, $this->info['currency']),
                    '{STORE_NAME}' => $STORE_NAME,
                ]) . "\n\n";

            if (notNull($this->info['payment_method']['title'])) {
                $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_PAYMENT_METHOD') . "\n" . Yii::t('page_content', 'EMAIL_SEPARATOR') . "\n";
                $mail .= strip_tags($this->info['payment_method']['title']) . "\n\n";
            }

            if ($addressbook == '') {
                $m_c = OrderCustomers::model()->findByPk($this->customer['id']);
                $addressbook = $m_c->customers_default_address_id;
            }

            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_BILLING_ADDRESS') . "\n" . Yii::t('page_content', 'EMAIL_SEPARATOR') . "\n";
            $mail .= FrontendMailCom::addressLabel($this->customer['id'], $addressbook, false, '', "\n") . "\n\n";

            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_ORDER_SUMMARY') . "\n" . Yii::t('page_content', 'EMAIL_SEPARATOR') . "\n";
            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_ORDER_NUMBER') . ' ' . $this->order_id . "\n" . Yii::t('orderModule.OrderCom',
                    'EMAIL_TEXT_DATE_ORDERED') . ' ' . strftime('%A, %d %B, %Y') . "\n\n";

            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_PRODUCTS') . "\n" . Yii::t('page_content', 'EMAIL_SEPARATOR') . "\n";

            if (!empty($this->products)) {
                for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                    $mail .= $this->products[$i]['quantity'] . ' x ';

                    $m_prod = OrderProducts::model()->findByPk($this->products[$i]['id']);
                    if (isset($m_prod->products_cat_path)) {
                        $mail .= $m_prod->products_cat_path . ' > ';
                    }

                    $mail .= $this->products[$i]['name'] . ' = ' . Yii::app()->currency->format(($this->products[$i]['storage_price']['final_price'] * $this->products[$i]['quantity']), true,
                            $this->info['currency'], $this->info['currency_value']) . "\n";
                }

                $mail .= "\n\n";
            }

            $mail .= Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_CLOSING', [
                    '{SYS_STORE_NAME}' => $STORE_NAME,
                    '{SYS_SUPPORT_EMAIL}' => ConfigurationCom::getValue('EMAIL_TO'),
                ]) . "\n\n" . $STORE_EMAIL_SIGNATURE;
            $subject = implode(' ', [
                $EMAIL_SUBJECT_PREFIX,
                sprintf(Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_SUBJECT'), $this->order_id),
            ]);

            FrontendMailCom::send($fullname, $this->customer['email_address'], $subject, $mail, $STORE_OWNER, $STORE_OWNER_EMAIL_ADDRESS);
        }
    }

    private function stockMovement($type, $sign, $from_status_id, $to_status_id)
    {
        /*
         * qty_type :
         * 1 = forecast quantity
         * 2 = available quantity
         * 3 = actual quantity
         */
        if (!empty($this->products)) {
            $fieldname = '';

            switch ($type) {
                // case 1:
                //     $fieldname = 'forecast_quantity';
                //     break;
                case 2:
                    $fieldname = 'available_quantity';
                    break;
                case 3:
                    $fieldname = 'actual_quantity';
                    break;
            }

            for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                $pid = $this->products[$i]['id'];

                if ($this->products[$i]['products_package']) {
                    # Bundle
                    foreach ($this->products[$i]['products_package'] as $subproduct_id => $subproduct_array) {
                        if ($subproduct_array['delivery_info']['delivery_mode'] == 6) {

                        } else {
                            # deduct stock
                            $this->stockDeduction($subproduct_id, $subproduct_array, bcmul($subproduct_array['qty'], $this->products[$i]['quantity']), $from_status_id, $to_status_id);
                        }
                    }
                } else {
                    # Single
                    if (isset($this->products[$i]['custom_content']['delivery_info']['delivery_mode']) && $this->products[$i]['custom_content']['delivery_info']['delivery_mode'] == 6) {

                    } else {
                        # deduct stock
                        $this->stockDeduction($pid, $this->products[$i], $this->products[$i]['quantity'], $from_status_id, $to_status_id);
                    }
                }
            }
        }
    }

    private function stockDeduction($product_id, $product_array, $quantity, $from_status_id, $to_status_id)
    {
        # deduct stock
        $product_obj = OrderProducts::model()->findByPK($product_id);

        if (!$product_obj->products_skip_inventory) {
            $estimate_stock_left = $product_obj->products_quantity - $quantity;

            $previous_product_quantity = $product_obj->products_quantity;
            $previous_product_actual_quantity = $product_obj->products_actual_quantity;

            $m_attr = [
                'products_quantity' => $estimate_stock_left,
            ];
            OrderProducts::model()->updateByPk($product_id, $m_attr);

            $product_obj2 = OrderProducts::model()->findByPK($product_id);
            $new_product_quantity = $product_obj2->products_quantity;
            $new_product_actual_quantity = $product_obj2->products_actual_quantity;

            $m_attr = [
                'log_admin_id' => (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY),
                'log_ip' => $this->info['remote_addr'],
                'log_time' => new CDbExpression('NOW()'),
                'log_products_id' => $product_id,
                'log_system_messages' => Yii::t('orderModule.OrderCom', 'LOG_SALES_ORDER', [
                    '{ORDER_ID}' => $this->order_id,
                    '{SYS_STATUS_FROM}' => $from_status_id,
                    '{SYS_STATUS_TO}' => $to_status_id,
                ]),
                'log_user_messages' => '',
                'log_field_name' => 'products_quantity',
                'log_from_value' => $previous_product_quantity . ':~:' . $previous_product_actual_quantity,
                'log_to_value' => $new_product_quantity . ':~:' . $new_product_actual_quantity,
            ];
            OrderLogTable::model()->saveNewRecord($m_attr);

            $cat_cfg_array = ConfigurationCom::getCategoryConfigValueByProductID($product_id);

            if (isset($cat_cfg_array['LOW_STOCK_EMAIL']) && $email_to_array = parseEmailString($cat_cfg_array['LOW_STOCK_EMAIL'])) {
                $warning_stock = ($product_obj2->products_quantity_order != "") ? $product_obj2->products_quantity_order : $cat_cfg_array['STOCK_REORDER_LEVEL'];
                $current_stock = $estimate_stock_left;

                $low_stock_email = '<b>Low stock warning:</b> ' .
                    $product_array['name'] . "\n" .
                    '<b>Model No.:</b> ' . $product_array['model'] . "\n" .
                    '<b>Quantity:</b> ' . $estimate_stock_left . "\n" .
                    // '<b>Product URL:</b> ' . HTTP_SERVER . DIR_WS_HTTP_CATALOG . 'custom_product_info.php?products_id=' . $product_id . "\n\n" .
                    '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n" .
                    '<b>Category Path:</b> ' . $product_obj2->products_cat_path;
                $low_stock_subject = 'Low Stock Warning: ' . strip_tags($product_array['name']);

                if ($current_stock <= $warning_stock) {
                    if ($product_obj2->custom_products_type_id == 2) {
                        OrderProductsLowStock::model()->addLowStockWarning($product_id, $product_obj2->custom_products_type_id);
                    }

                    if ((int)$product_obj2->custom_products_type_id > 0) {
                        //If custom product, check if exists config for low stock email to overwrite global.
                        $cpt_obj = OrderCustomProductsType::model()->findByPK($product_obj2->custom_products_type_id);

                        if (isset($cpt_obj->custom_products_low_stock_email) && notNull($cpt_obj->custom_products_low_stock_email)) {
                            if (isset($cat_cfg_array[$cpt_obj->custom_products_low_stock_email]) && $cat_cfg_array[$cpt_obj->custom_products_low_stock_email]) {
                                $email_to_array = parseEmailString($cat_cfg_array[$cpt_obj->custom_products_low_stock_email]);
                            }
                        }
                    }

                    for ($i = 0, $cnt = count($email_to_array); $cnt > $i; $i++) {
                        FrontendMailCom::send($email_to_array[$i]['name'], $email_to_array[$i]['email'], $low_stock_subject, $low_stock_email, ConfigurationCom::getValue('STORE_OWNER'),
                            ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS'));
                    }
                }
            }

            $this->getOrderProductObj()->updateCustomOutOfStockRuleOutOfStock($product_id, $new_product_quantity);
        }
    }

    private function updateOrder()
    {
        if (!empty($this->order_id)) {
            $m_o = OrderOrders::model()->findByPk($this->order_id);

            # update Paypal IPN ID
            if ($m_o && isset($GLOBALS['cart']['payment_info']['paypal_ipn_id']) && !empty($GLOBALS['cart']['payment_info']['paypal_ipn_id'])) {
                $m_attr = [
                    'last_modified' => new CDbExpression('NOW()'),
                    'paypal_ipn_id' => $GLOBALS['cart']['payment_info']['paypal_ipn_id'],
                ];
                OrderOrders::model()->updateByPk($this->order_id, $m_attr);
                unset($m_attr);
            }
        }
    }

    private function updateOrderStatus($orders_status)
    {
        $_result = $this->preOrderRules($this->info['orders_status_id'], $orders_status);

        if (isset($_result['status']) && $_result['status']) {
            unset($_result);

            // update order status
            $m_attr = [
                'orders_status' => $orders_status,
                'last_modified' => new CDbExpression('NOW()'),
            ];
            if ($this->info['orders_aft_executed'] != '-1') {
                $m_attr['orders_aft_executed'] = 0;
            }
            OrderOrders::model()->updateByPk($this->order_id, $m_attr);
            unset($m_attr);

            // order status log
            $m_os_attr = [
                'orders_id' => $this->order_id,
                'orders_status_id' => $orders_status,
                'date_added' => new CDbExpression('NOW()'),
                'customer_notified' => 1,
                'comments' => (isset($GLOBALS['cart']['update_comment']) ? $GLOBALS['cart']['update_comment'] : ''),
                'changed_by' => (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY),
            ];
            OrderOrdersStatusStat::model()->updateOrdersStatusCounter($m_os_attr);
            OrderOrdersStatusHistory::model()->saveNewRecord($m_os_attr);
            unset($m_os_attr);

            $_result = $this->postOrderRules($this->info['orders_status_id'], $orders_status);

            if (isset($_result['status']) && $_result['status']) {
                switch ($orders_status) {
                    case 7: // Verifying
                        $m_cgo = OrderCronGenesisOrders::model()->findByPk($this->order_id);
                        if (!$m_cgo) {
                            $m_attr = [
                                'orders_id' => $this->order_id,
                                'flag' => 0,
                                'last_modified' => new CDbExpression('NOW()'),
                            ];
                            OrderCronGenesisOrders::model()->saveNewRecord($m_attr);
                            unset($m_attr);

                            # lock order to prevent crew manual execution before system aft process
                            $m_attr = [
                                'orders_locked_by' => OrderCom::IDENTITY,
                                'orders_locked_datetime' => new CDbExpression('NOW()'),
                            ];
                            OrderOrders::model()->updateByPk($this->order_id, $m_attr);
                            unset($m_attr);

                            $extra_info = [
                                'locked_by' => OrderCom::IDENTITY,
                                'locked_ip' => getIPAddress(),
                            ];
                            $m_attr = [
                                'page_name' => OrderCom::MOVE_ORDER,
                                'match_case' => $this->order_id,
                                'created_date' => new CDbExpression('NOW()'),
                                'extra_info' => json_encode($extra_info),
                            ];
                            OrderTempProcess::model()->saveNewRecord($m_attr);
                            unset($m_attr);
                            unset($extra_info);

                            $m_attr = [
                                'orders_log_admin_id' => OrderCom::IDENTITY,
                                'orders_log_ip' => getIPAddress(),
                                'orders_log_time' => new CDbExpression('NOW()'),
                                'orders_log_orders_id' => $this->order_id,
                                'orders_log_system_messages' => OrderCom::LOCK_LOG,
                                'orders_log_filename' => OrderCom::FILENAME,
                            ];
                            OrderOrdersLogTable::model()->saveNewRecord($m_attr);
                            unset($m_attr);
                        }
                        break;
                }
            }
        }

        return $_result;
    }

    private function statusUpdateNotification(
        $trans_type,
        $trans_id,
        $change_by,
        $from_status,
        $to_status,
        $mode = 'M',
        $comments = ''
    ) {
        $notification_field = $mode == 'M' ? 'status_configuration_manual_notification' : 'status_configuration_auto_notification';

        $m_osc = OrderStatusConfiguration::model()->findByAttributes([
            'status_configuration_trans_type' => $trans_type,
            'status_configuration_source_status_id' => $from_status,
            'status_configuration_destination_status_id' => $to_status,
        ]);

        if (isset($m_osc->status_configuration_trans_type)) {
            $subject = '';
            $mail = '';

            $mail_arr = parseEmailString($m_osc->$notification_field);
            if (!empty($mail_arr)) {
                if ($trans_type == 'C') {
                    $subject = implode(' ', [
                        ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX'),
                        sprintf(Yii::t('orderModule.OrderCom', 'EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT'), $trans_id),
                    ]);

                    $m_os = OrderOrdersStatus::model()->getOrdersStatusName();
                    $m_order = OrderOrders::model()->getOrderInfo($this->order_id);
                    $notify_mode = ($mode == 'M' ? Yii::t('orderModule.OrderCom', 'EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION') : Yii::t('orderModule.OrderCom',
                        'EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION'));
                    $mail = sprintf(Yii::t('orderModule.OrderCom', 'EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT'), $trans_id, $m_order['date_purchased'], strip_tags($m_order['text']),
                        $m_order['payment_method'], $notify_mode, $m_os[$from_status], $m_os[$to_status], date("Y-m-d H:i:s"), getIPAddress(), $change_by, $comments);
                }

                if ($subject) {
                    $from_name = ConfigurationCom::getValue('STORE_OWNER');
                    $from_email = ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS');

                    for ($i = 0, $cnt = count($mail_arr); $cnt > $i; $i++) {
                        FrontendMailCom::send($mail_arr[$i]['name'], $mail_arr[$i]['email'], $subject, $mail, $from_name, $from_email);
                    }
                }
            }
        }
    }

    private function preOrderRules_1(
        $customer_id,
        $products_id,
        $delivery_mode,
        &$checkout_ctrl,
        &$preorder_id,
        $bundle_id = ''
    ) {
        $_message = '';

        # validate product status
        $product_array = OrderProducts::model()->getInfoByID($products_id);
        if (!$product_array['products_status']) {
            $_message = Yii::t('orderModule.OrderCom', 'TEXT_STOCK_NOT_AVAILABLE');
        }

        # validate stock
        if ($delivery_mode == 6) {
            // DTU
        } else {
            $total_purchase_product_qty = $this->getTotalProductPurchase($products_id);
            $stock_checking = $this->getSingleProductStockStatus($product_array, $total_purchase_product_qty);

            if ($stock_checking === 'OUT_OF_STOCK') {
                $_message = Yii::t('orderModule.OrderCom', 'TEXT_STOCK_NOT_AVAILABLE');
            } else {
                if ($stock_checking === 'PRE_ORDER') {
                    $preorder_id[] = $bundle_id != '' ? $bundle_id : $products_id;
                }
            }
        }

        $this->getCustomerProductStockStatus($customer_id, $products_id, $checkout_ctrl, $bundle_id);

        return $_message;
    }

    private function preOrderRules($from_status, $to_status)
    {
        /*
         * order status
         * 0 : before create order
         * 1 : pending
         * 2 : processing
         * 3 : complete
         * 5 : cancel
         * 6 : refund
         * 7 : verifying
         * 8 : on hold
         *
         * status
         * true : success
         * false : fail
         */

        $_status = false;
        $_message = '';
        $extra_params = [];

        switch ($from_status) {
            case 0: // before create order
                switch ($to_status) {
                    case 1: // pending
                        $_error = true;

                        if (isset($this->products) && count($this->products)) {
                            $_error = false;
                            $checkout_ctrl = [];
                            $preorder_product_id_array = [];
                            # Validate product / bundled-product status
                            # Validate stock
                            # Group item purchased limit
                            for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                                if ($this->products[$i]['id']) {
                                    $delivery_mode = isset($this->products[$i]['custom_content']['delivery_info']['delivery_mode']) ? $this->products[$i]['custom_content']['delivery_info']['delivery_mode'] : 5;

                                    if ($this->products[$i]['products_package']) {
                                        foreach ($this->products[$i]['products_package'] as $subproduct_id => $subproduct_array) {
                                            if ($_message = $this->preOrderRules_1($this->customer['id'], $subproduct_id, $delivery_mode, $checkout_ctrl, $preorder_product_id_array,
                                                $this->products[$i]['id'])) {
                                                $_error = true;
                                                break 2;
                                            }
                                        }
                                    } else {
                                        if ($_message = $this->preOrderRules_1($this->customer['id'], $this->products[$i]['id'], $delivery_mode, $checkout_ctrl, $preorder_product_id_array)) {
                                            $_error = true;
                                            break;
                                        }
                                        if($delivery_mode == 6 && empty($this->products[$i]['custom_content']['top_up_info'])){
                                            $_message = Yii::t('orderModule.OrderCom', 'ERROR_MISSING_DTU_INFO');
                                        }
                                    }
                                }
                            }

                            if ($_message === '') {
                                # validate item purchased limit
                                for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                                    if (isset($checkout_ctrl[$this->products[$i]['id']]['exceed']) && $checkout_ctrl[$this->products[$i]['id']]['exceed'] == 1) {
                                        $_error = true;
                                        $_message = Yii::t('orderModule.OrderCom', 'ERROR_PURCHASE_QUANTITY_EXCEEDED');
                                        break;
                                    }
                                }
                            }
                        }

                        if ($_message === '') {
                            # validate pending order limit
                            $_max_pending_num = ConfigurationCom::getValue('B2C_MAX_NUM_OF_PENDING');
                            $_max_pending_min = ConfigurationCom::getValue('B2C_MAX_PENDING_WITHIN_MIN');

                            if (!empty($_max_pending_num)) {
                                $_current_pending = OrderOrders::model()->getPendingOrderCount($this->customer['id'], $_max_pending_min);

                                if ($_current_pending >= $_max_pending_num) {
                                    $_max_pending_recipient = ConfigurationCom::getValue('B2C_EXCEED_MAX_PENDING_RECIPIENT');

                                    $mail_arr = parseEmailString($_max_pending_recipient);
                                    if (!empty($mail_arr)) {
                                        $subject = implode(' ', [
                                            ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX'),
                                            sprintf(Yii::t('orderModule.OrderCom', 'EMAIL_MAX_PENDING_ORDER_SUBJECT'), $this->customer['id']),
                                        ]);
                                        $mail = sprintf(Yii::t('orderModule.OrderCom', 'EMAIL_MAX_PENDING_ORDER_TEXT'), $this->customer['id']);

                                        for ($i = 0, $cnt = count($mail_arr); $cnt > $i; $i++) {
                                            FrontendMailCom::send($mail_arr[$i]['name'], $mail_arr[$i]['email'], $subject, $mail, ConfigurationCom::getValue('STORE_OWNER'),
                                                ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS'));
                                        }
                                    }

                                    $_message = sprintf(Yii::t('orderModule.OrderCom', 'ERROR_MAX_PENDING_ORDER'), $_current_pending);
                                } else {
                                    $_status = true;
                                }
                            } else {
                                $_status = true;
                            }

                            if ($_status) {
                                $extra_params['preorder_product_id_array'] = $preorder_product_id_array;
                            }
                        }
                        break;
                }
                break;

            case 1: // pending
                switch ($to_status) {
                    case 5:
                    case 7:
                        $_status = true;
                        break;
                }
                break;
        }

        if (!$_status) {
            if (empty($_message)) {
                $m_os1 = OrderOrdersStatus::model()->findByPk([
                    'orders_status_id' => $from_status,
                    'language_id' => 1,
                ]);
                $m_os2 = OrderOrdersStatus::model()->findByPk([
                    'orders_status_id' => $to_status,
                    'language_id' => 1,
                ]);
                $_message = Yii::t('orderModule.OrderCom', 'ERROR_INVALID_ORDER_STATUS_UPDATE', [
                    '{SYS_STATUS_FROM}' => (isset($m_os1->orders_status_name) ? $m_os1->orders_status_name : $from_status),
                    '{SYS_STATUS_TO}' => (isset($m_os2->orders_status_name) ? $m_os2->orders_status_name : $to_status),
                ]);
            }
        }

        $_result = [
            'status' => $_status,
            'code' => 'order',
            'error' => $_message,
            'extra_params' => $extra_params,
        ];

        return $_result;
    }

    private function postOrderRules($from_status, $to_status)
    {
        /*
         * order status
         * 0 : before create order
         * 1 : pending
         * 2 : processing
         * 3 : complete
         * 5 : cancel
         * 6 : refund
         * 7 : verifying
         * 8 : on hold
         *
         * status
         * true : success
         * false : invalid status update
         */

        $_status = false;
        $_message = '';

        switch ($from_status) {
            case 0: // before create order
                switch ($to_status) {
                    case 1:
                        # OGM does not required to do this
                        // $this->stockMovement(1, '-');
                        // $_status = true;
                        break;
                }
                break;

            case 1: // pending
                switch ($to_status) {
                    case 5: // cancel
                        $this->reportError('postOrderRules : pending -> cancel : ' . $this->order_id, $this->products);
                        break;

                    case 7: // verifying
                        $this->stockMovement(2, '-', $from_status, $to_status);
                        $this->customerInfoVerification();
                        $_status = true;
                        break;
                }
                break;

            case 7: // verifying
                switch ($to_status) {
                    case 5: // cancel
                        $this->reportError('postOrderRules : verifying -> cancel : ' . $this->order_id, $this->products);
                        break;

                    case 6: // refund
                    case 8: // on hold
                        // $this->stockMovement(1, '+');
                        // $this->stockMovement(2, '+');
                        // $_status = true;
                        $this->reportError('postOrderRules : verifying -> (refund || on hold) : ' . $this->order_id, $this->products);
                        break;
                }
                break;
        }

        if (!$_status) {
            $m_os1 = OrderOrdersStatus::model()->findByPk([
                'orders_status_id' => $from_status,
                'language_id' => 1,
            ]);
            $m_os2 = OrderOrdersStatus::model()->findByPk(['orders_status_id' => $to_status, 'language_id' => 1]);
            $_message = Yii::t('orderModule.OrderCom', 'ERROR_INVALID_ORDER_STATUS_UPDATE', [
                    '{SYS_STATUS_FROM}' => (isset($m_os1->orders_status_name) ? $m_os1->orders_status_name : $from_status),
                    '{SYS_STATUS_TO}' => (isset($m_os2->orders_status_name) ? $m_os2->orders_status_name : $to_status),
                ]
            );
        }

        $_result = [
            'status' => $_status,
            'code' => 'order',
            'error' => $_message,
        ];

        return $_result;
    }

    public function customerInfoVerification($payment_status = 'Verify')
    {
        $result = false;
        $send_email_verification = false;
        if (!empty($this->order_id) && !empty($this->info['payment_method']['payment_methods_id'])) {
            $_pm_email = '';

            $payment_code_obj = PipwavePaymentMapperBase::model()->findByAttributes(['pm_id' => $this->info['payment_method']['payment_methods_id']]);
            $payment_code = isset($payment_code_obj->pg_code) ? $payment_code_obj->pg_code : '';

            if ($payment_code) {
                switch ($payment_code) {
                    case 'paypalEC':
                        $m_pp = AnalysisPgInfoBase::model()->findByPk([
                            'orders_id' => $this->order_id,
                            'info_key' => 'payer_email',
                        ]);
                        $_pm_email = (isset($m_pp->info_value) ? $m_pp->info_value : '');
                        $send_email_verification = false;
                        break;
                    case 'moneybookers':
                        $m_pp = AnalysisPgInfoBase::model()->findByPk([
                            'orders_id' => $this->order_id,
                            'info_key' => 'moneybooker_email',
                        ]);
                        $_pm_email = (isset($m_pp->info_value) ? $m_pp->info_value : '');
                        $send_email_verification = false;
                        break;
                }
            }

            if (!empty($_pm_email)) {
                if ($payment_status == 'Review' || $payment_status == 'Echeck') {
                    $firstname = '';
                    $fullname = '';
                    $lastname = '';
                    $mail = '';

                    if (isset($this->customer['firstname'])) {
                        $firstname = $this->customer['firstname'];
                        $fullname .= $this->customer['firstname'];
                    } else {
                        $firstname = $fullname = $this->customer['name'];
                    }

                    if (isset($this->customer['lastname'])) {
                        $lastname = $this->customer['lastname'];
                        $fullname .= $this->customer['lastname'];
                    } else {
                        $lastname = $fullname = $this->customer['name'];
                    }

                    $STORE_OWNER = $this->getSendMailInfo('STORE_OWNER');
                    $STORE_OWNER_EMAIL_ADDRESS = $this->getSendMailInfo('STORE_OWNER_EMAIL_ADDRESS');

                    // Data array to send to SQS
                    $emailDataSqs = array(
                        'filetype' => 'order_email',
                        'email_template' => 'order-paypal-review',
                        'email_subject_prefix' => '',
                        'store_owner' => $STORE_OWNER,
                        'store_owner_email_address' => $STORE_OWNER_EMAIL_ADDRESS,
                        'customer' => array(
                            'id' => $this->customer['id'],
                            'firstname' => $fullname,
                            'language' => Yii::app()->language,
                            'email' => $_pm_email,
                            'payment_review' => ($payment_status == 'Review') ? 'paypal-review' : 'paypal-echeck',
                        ),
                        'orders' => array(
                            'orders_id' => (int)$this->order_id,
                        )
                    );

                    $msorder = new MsOrderModel();
                    $msorder->pushMailQueue($emailDataSqs);
                    // Compare customer email with paypal email
                    if (!empty($this->customer['email_address']) && ($_pm_email != $this->customer['email_address'])) {   // Both same email
                        // If customer use diff email than paypal email
                        $emailDataSqs['customer']['email'] = $this->customer['email_address'];
                        $msorder->pushMailQueue($emailDataSqs);
                    }

                    // Set Email for Paypal Review or Paypal eCheck
                    if ($payment_status == 'Review') {
                        $paypal_orders_tag_id = Yii::app()->params['ORDER_TAG_ID_PAYPAL_REVIEW'];
                        $payment_history_text = 'PayPal payment review notification email sent';
                    } else {
                        $paypal_orders_tag_id = Yii::app()->params['ORDER_TAG_ID_PAYPAL_ECHECK'];
                        $payment_history_text = 'PayPal eCheck notification email sent';
                    }

                    // Add Orders Status History
                    $m_attr = [
                        'orders_id' => $this->order_id,
                        'orders_status_id' => 0,
                        'date_added' => new CDbExpression('NOW()'),
                        'customer_notified' => 1,
                        'comments' => (isset($GLOBALS['cart']['update_comment']) ? $GLOBALS['cart']['update_comment'] : $payment_history_text),
                        'changed_by' => (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY),
                    ];
                    OrderOrdersStatusHistory::model()->saveNewRecord($m_attr);
                    unset($m_attr);

                    $m_order = new OrderOrders();
                    //Tag Order for CET
                    if (isset($paypal_orders_tag_id)) {
                        if (!$m_order->tagStatus($this->order_id, $paypal_orders_tag_id)) {
                            $m_order->tagAdd($this->order_id, $paypal_orders_tag_id);
                        }
                    }

                    $result = true;
                } else {
                    if ($send_email_verification) {
                        $_oauth = Yii::app()->customerCom->getCustomerInfoVerifyStatus($this->customer['id'], $_pm_email, true, 'email');
                        if (isset($_oauth['status']) && !$_oauth['status'] && isset($_oauth['token']) && !empty($_oauth['token'])) {
                            $STORE_OWNER = $this->getSendMailInfo('STORE_OWNER');
                            $STORE_OWNER_EMAIL_ADDRESS = Yii::app()->params['MAIL_SENDER']['ALERT'];
                            $STORE_NAME = $this->getSendMailInfo('STORE_NAME');
                            $STORE_EMAIL_SIGNATURE = $this->getSendMailInfo('STORE_EMAIL_SIGNATURE');
                            $EMAIL_SUBJECT_PREFIX = $this->getSendMailInfo('EMAIL_SUBJECT_PREFIX');

                            $url = Yii::app()->params['SHASSO_CONFIG']['SHASSO_URI'] . '/verify-email/index?action=verify&serialNumber=' . $_oauth['token'] . '&email=' . urlencode($_pm_email) . '&oid=' . $this->order_id;
                            $m_cust = OrderCustomers::model()->findByPk($this->customer['id']);

                            $_greeting = FrontendMailCom::getEmailGreeting($this->customer['name'], $this->customer['name'], (isset($m_cust->customers_gender) ? $m_cust->customers_gender : ''));
                            # EMAIL_FOOTER == STORE_EMAIL_SIGNATURE
                            $_footer = $STORE_EMAIL_SIGNATURE;

                            $subject = $EMAIL_SUBJECT_PREFIX . ' ' . Yii::t('orderModule.OrderCom', 'EMAIL_PAYMENT_METHOD_VERIFICATION_SUBJECT',
                                    ['{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title']]);
                            $mail = $_greeting . Yii::t('OrderModule.OrderCom', 'EMAIL_PAYMENT_METHOD_VERIFICATION', [
                                    '{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title'],
                                    '{SYS_ORDER_AMOUNT}' => $this->info['total'],
                                    '{SYS_STORE_NAME}' => $STORE_NAME,
                                    '{SYS_CUSTOMER_EMAIL}' => $_pm_email,
                                    '{SYS_VERIFICATION_LINK}' => '<a href="' . $url . '" target="_blank">' . $url . '</a><br />',
                                    '{SYS_SUPPORT_EMAIL}' => ConfigurationCom::getValue('EMAIL_TO'),
                                ]) . $_footer;
                            FrontendMailCom::send($this->customer['name'], $_pm_email, $subject, $mail, $STORE_OWNER, $STORE_OWNER_EMAIL_ADDRESS);
                            $_comment = Yii::t('OrderModule.OrderCom', 'TEXT_PAYMENT_METHOD_VERIFICATION_SENT', ['{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title']]);

                            if ($this->customer['email_address'] != $_pm_email) {
                                $notice_subject = $EMAIL_SUBJECT_PREFIX . ' ' . Yii::t('orderModule.OrderCom', 'EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE_SUBJECT',
                                        ['{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title']]);
                                $notice = $_greeting . Yii::t('OrderModule.OrderCom', 'EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE', [
                                        '{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title'],
                                        '{SYS_ORDER_AMOUNT}' => $this->info['total'],
                                        '{SYS_STORE_NAME}' => $STORE_NAME,
                                        '{SYS_CUSTOMER_EMAIL}' => $_pm_email,
                                        '{SYS_MAIL_SUBJECT}' => $subject,
                                    ]) . $_footer;
                                FrontendMailCom::send($this->customer['name'], $this->customer['email_address'], $notice_subject, $notice, $STORE_OWNER, $STORE_OWNER_EMAIL_ADDRESS);
                                $_comment .= "\n" . Yii::t('OrderModule.OrderCom', 'TEXT_PAYMENT_METHOD_VERIFICATION_NOTICE_SENT', ['{SYS_PAYMENT_METHOD}' => $this->info['payment_method']['title']]);
                            }

                            $m_attr = [
                                'orders_id' => $this->order_id,
                                'orders_status_id' => 0,
                                'date_added' => new CDbExpression('NOW()'),
                                'customer_notified' => '1',
                                'comments' => $_comment,
                                'changed_by' => (isset($GLOBALS['cart']['update_by']) ? $GLOBALS['cart']['update_by'] : OrderCom::CHANGE_BY),
                            ];
                            OrderOrdersStatusHistory::model()->saveNewRecord($m_attr);
                            unset($m_attr);
                        }
                    }
                }
            }
        }

        return $result;
    }

    private function getSendMailInfo($field = '')
    {
        if (!isset($this->customer['mailer_info'])) {
            $this->customer['mailer_info'] = [
                'EMAIL_SUBJECT_PREFIX' => ConfigurationCom::getValue('EMAIL_SUBJECT_PREFIX'),
                'STORE_OWNER' => ConfigurationCom::getValue('STORE_OWNER'),
                'STORE_OWNER_EMAIL_ADDRESS' => ConfigurationCom::getValue('STORE_OWNER_EMAIL_ADDRESS'),
                'STORE_NAME' => ConfigurationCom::getValue('STORE_NAME'),
                'STORE_EMAIL_SIGNATURE' => ConfigurationCom::getValue('STORE_EMAIL_SIGNATURE'),
            ];

            if (!empty($this->products)) {
                for ($i = 0, $cnt = count($this->products); $cnt > $i; $i++) {
                    # Top Up Store Credit Needs To Use Another Mail Sender
                    if (isset($this->products[$i]['custom_products_type_id']) && $this->products[$i]['custom_products_type_id'] == 3) {
                        $this->customer['mailer_info'] = [
                            'EMAIL_SUBJECT_PREFIX' => Yii::app()->params['SHASSO_CONFIG']['EMAIL_SUBJECT_PREFIX'],
                            'STORE_OWNER' => Yii::app()->params['SHASSO_CONFIG']['STORE_OWNER'],
                            'STORE_OWNER_EMAIL_ADDRESS' => Yii::app()->params['SHASSO_CONFIG']['STORE_OWNER_EMAIL_ADDRESS'],
                            'STORE_NAME' => Yii::app()->params['SHASSO_CONFIG']['STORE_NAME'],
                            'STORE_EMAIL_SIGNATURE' => Yii::app()->params['SHASSO_CONFIG']['STORE_EMAIL_SIGNATURE'],
                        ];
                    }
                }
            }
        }

        return ($field !== '' && isset($this->customer['mailer_info'][$field])) ? $this->customer['mailer_info'][$field] : $this->customer['mailer_info'];
    }

    private function getCustomerProductStockStatus($customer_id, $products_id, &$checkout_ctrl = [], $bundle_id = null)
    {
        $checkout_ctrl_id = notNull($bundle_id) ? $bundle_id : $products_id;

        // Products Checkout Quantity Control [START]
        #key:products_checkout_setting/checkout_setting/array/products_id/xxx
        $cache_key = OrderProductsCheckoutSetting::model()->tableName() . '/checkout_setting/array/products_id/' . $products_id;
        $product_cache_result = Yii::app()->cache->get($cache_key);

        if ($product_cache_result !== false) {
            $product_cache = $product_cache_result;
        } else {
            $row = OrderProductsCheckoutSetting::model()->findByPk($products_id);

            $product_cache = [
                'quantity' => isset($row->max_purchase_quantity) ? $row->max_purchase_quantity : '',
                'minute' => isset($row->max_purchase_period) ? $row->max_purchase_period : '',
            ];
            Yii::app()->cache->set($cache_key, $product_cache, 86400); // cache 1 day
        }

        // Remove checking if products checkout quantity limit is 0
        // TODO no error message display when hit this condition
        if (notNull($product_cache['quantity']) && notNull($product_cache['minute']) && ($product_cache['quantity'] != 0 && $product_cache['minute'] != 0)) {
            $customer_cache = [];

            #key:products_checkout_setting/exceed_limit_info/array/customers_id/xxx/products_id/xxx
            $cache_key = Yii::app()->params['MEMCACHE_PREFIX'] . OrderProductsCheckoutSetting::model()->tableName() . '/exceed_limit_info/array/customers_id/' . $customer_id . '/products_id/' . $products_id;
            $customer_cache_result = Yii::app()->cache->get($cache_key);

            if (($customer_cache_result !== false) && ($product_cache_result !== false) && ((int)$customer_cache_result['quantity'] > 0)) {
                if (isset($customer_cache_result['minute']) && $customer_cache_result['minute'] == $product_cache['minute']) {
                    $customer_cache = $customer_cache_result;
                }
            }

            if ($customer_cache === []) {
                // CO history checking, exclude "Cancel"
                // quantity & orders_id_list
                $customer_cache = OrderOrders::model()->getTotalQuantityPurchaseByCustomerID($products_id, $customer_id, $product_cache['minute']);
                $customer_cache['minute'] = $product_cache['minute'];

                Yii::app()->cache->set($cache_key, $customer_cache, 3600); // cache 1 hours
            }

            $total_prod_in_cart = $this->getTotalProductPurchase($products_id, $bundle_id);

            if (($customer_cache['quantity'] + $total_prod_in_cart) > $product_cache['quantity']) {
                $checkout_ctrl[$checkout_ctrl_id]['exceed'] = 1;

                if (notNull($customer_cache['orders_id_list'])) {
                    if (notNull($bundle_id) && isset($checkout_ctrl[$checkout_ctrl_id]['orders']) && is_array($checkout_ctrl[$checkout_ctrl_id]['orders'])) {
                        $checkout_ctrl[$checkout_ctrl_id]['orders'] = array_unique(array_merge($checkout_ctrl[$checkout_ctrl_id]['orders'], $customer_cache['orders_id_list']));
                    } else {
                        $checkout_ctrl[$checkout_ctrl_id]['orders'] = $customer_cache['orders_id_list'];
                    }
                }
            } else {
                if (!notNull($bundle_id) && isset($checkout_ctrl[$checkout_ctrl_id]['exceed'])) {
                    $checkout_ctrl[$checkout_ctrl_id]['exceed'] = 0;
                }
            }
        } else {
            $checkout_ctrl[$checkout_ctrl_id]['exceed'] = 0;
        }
        // Products Checkout Quantity Control [END]

        return $checkout_ctrl;
    }

    private function getTotalProductPurchase($products_id, $exclude_bundle_id = '')
    {
        $cart_prod_total = 0;

        foreach ($this->products as $idx => $product_array) {
            if ($product_array['products_package']) {  // bundle product
                foreach ($product_array['products_package'] as $subproduct_id => $subproduct_array) {
                    if ($subproduct_id == $products_id) {
                        $cart_prod_total += $product_array['quantity'] * $subproduct_array['qty'];
                    }
                }
            } else {
                if ($product_array['id'] == $products_id) {    // single product
                    $cart_prod_total += $product_array['quantity'];
                }
            }
        }

        return $cart_prod_total;
    }

    public function getSingleProductStockStatus($product_info, $purchase_qty = null, &$available_stock = 0)
    {
        $status = '';

        if ($product_info) {
            switch ($product_info["products_purchase_mode"]) {
                case '1':    // Always Add to Cart
                    $status = 'AVAILABLE';
                    break;
                case '2':    // Always Pre-Order
                    $status = 'PRE_ORDER';
                    break;
                case '3':    // Always Out of Stock
                    $status = 'OUT_OF_STOCK';
                    break;
                case '4':    // Auto Mode
                    $status = 'AUTO';

                    if (notNull($purchase_qty)) {
                        if ($this->getOrderProductObj()->validateCustomOutOfStockRules($product_info['products_id'], $product_info['products_quantity'], $purchase_qty, $available_stock,
                                $this->customer['customers_groups_id']) === 'OUT_OF_STOCK') {
                            $status = 'OUT_OF_STOCK';
                        } else {
                            if (notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_out_of_stock_level"] < $purchase_qty) {    // If there is setting for out of stock level
                                $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                                $status = 'OUT_OF_STOCK';
                            } else {
                                if (notNull($product_info["products_out_of_stock_level"]) && $product_info['products_quantity'] - $product_info["products_pre_order_level"] < $purchase_qty) {
                                    #Test on Pre-order
                                    $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                                    $status = 'PRE_ORDER';
                                } else {
                                    if ($product_info["products_pre_order_level"]) {
                                        $available_stock = $product_info['products_quantity'] - $product_info["products_pre_order_level"];
                                    } else {
                                        if ($product_info["products_out_of_stock_level"]) {
                                            $available_stock = $product_info['products_quantity'] - $product_info["products_out_of_stock_level"];
                                        } else {
                                            $available_stock = $product_info['products_quantity'];
                                        }
                                    }

                                    $status = 'AVAILABLE';
                                }
                            }
                        }
                    }
                    break;
            }
        }

        return $status;
    }

    private function getOrderProductObj()
    {
        if (!$this->product_obj) {
            $this->product_obj = new OrderProductCom();
        }

        return $this->product_obj;
    }

    public static function reportError($subject, $response_data, $ext_subject = '')
    {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' - ' . date("F j, Y H:i");
        $attachments = [
            [
                'color' => 'danger',
                'text' => $response_data,
            ],
        ];
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

}
