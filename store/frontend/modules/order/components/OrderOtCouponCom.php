<?php

class OrderOtCouponCom extends CApplicationComponent
{

    public $code, $title, $display_title, $enabled, $sort_order;
    public $deduction, $output, $is_top_sc;
    public $coupon_code;

    public function __construct()
    {
        $this->code = 'ot_coupon';

        $this->title = Yii::t('orderModule.OrderOtCouponCom', 'MODULE_ORDER_TOTAL_COUPON_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtCouponCom', 'MODULE_ORDER_TOTAL_COUPON_DISPLAY_TITLE');

        $this->enabled = true;
        $this->sort_order = 9;

        $this->deduction = 0;
        $this->output = [];

        $this->coupon_code = '';
    }

    public function collectPosts()
    {
        $_error = '';

        $c_info = $GLOBALS['cart']['checkout_info'];
        if (isset($c_info['coupon_code']) && notEmpty($c_info['coupon_code'])) {
            $_error = $this->validateCoupon();
        }

        if ($_error != '') {
            $GLOBALS['cart']['checkout_info']['coupon_code'] = '';
        }

        return $_error;
    }

    public function process()
    {
        $_checkout_value = 0;
        $_storage_value = $this->calculateCredit();
        $this->deduction = $_storage_value;

        $c_info = $GLOBALS['cart']['checkout_info'];

        if ($_storage_value > 0) {
            $_checkout_value = Yii::app()->currency->advanceCurrencyConversion($_storage_value, OrderConfiguration::model()->getConfigValue('DEFAULT_CURRENCY'), $c_info['currency'], true, 'buy');
            if ($_checkout_value > 0) {
                $GLOBALS['cart']['checkout_info']['total'] = $c_info['total'] - $_checkout_value;
                if ($GLOBALS['cart']['checkout_info']['total'] < 0) {
                    $GLOBALS['cart']['checkout_info']['total'] = 0;
                }
            } else {
                $_storage_value = 0;
                $this->deduction = 0;
            }
        }

        if ($this->is_top_sc !== true) {
            $_checkout_value = number_format($_checkout_value, Yii::app()->currency->currencies[$c_info['currency']]['decimal_places'], Yii::app()->currency->currencies[$c_info['currency']]['decimal_point'], '');

            $this->output[] = [
                'title' => $this->title . ': ' . $this->coupon_code,
                'display_title' => $this->display_title,
                'checkout_value' => $_checkout_value,
                'storage_text' => '<b>' . Yii::app()->currency->format($_checkout_value, false, $c_info['currency']) . '</b>',
                'storage_value' => $_storage_value, // USD
                'input' => [
                    'coupon_code' => $GLOBALS['cart']['checkout_info']['coupon_code'],
                ],
                'output' => [],
            ];
        }
    }

    public function calculateCredit()
    {

        $c_info = $GLOBALS['cart'];
        $p_info = $c_info['products'];
        $od_amount = 0;

        # check if is top up store credit
        if (!empty($p_info)) {
            for ($i = 0, $cnt = count($p_info); $cnt > $i; $i++) {
                if ($p_info[$i]['custom_products_type_id'] == 3) {
                    $this->is_top_sc = true;
                }
            }
        }

        if ($this->is_top_sc !== true) {
            if (isset($GLOBALS['cart']['checkout_info']['coupon_code']) && notNull($GLOBALS['cart']['checkout_info']['coupon_code'])) {
                $amount = $c_info['checkout_info']['total'] / $c_info['checkout_info']['currency_value'];

                if ($this->validateCoupon() == '' && notNull($GLOBALS['cart']['checkout_info']['coupon_code'])) {
                    $m_c_data = OrderCoupons::model()->findByAttributes(['coupon_code' => $GLOBALS['cart']['checkout_info']['coupon_code']]);
                    if (!empty($m_c_data)) {
                        $this->coupon_code = $m_c_data->coupon_code;

                        $c_deduct = $m_c_data->coupon_amount;

                        if ($amount >= $m_c_data->coupon_minimum_order) {
                            if ((($m_c_data->coupon_type == 'P') || ($m_c_data->coupon_type == 'F')) && ($m_c_data->restrict_to_products || $m_c_data->restrict_to_categories)) {
                                $eligible_discount_amount = 0;
                                for ($i = 0; $i < sizeof($c_info['products']); $i++) {
                                    if ($m_c_data->restrict_to_products) {
                                        $coupon_prod_ids = explode(",", $m_c_data->restrict_to_products);
                                        if (in_array($c_info['products'][$i]['id'], $coupon_prod_ids)) {
                                            $pr_c = $c_info['products'][$i]['storage_price']['final_price'] * $c_info['products'][$i]['quantity'];
                                            if ($m_c_data->coupon_type == 'P') {
                                                $pod_amount = round($pr_c * 100) / 100 * $c_deduct / 100; // change from 10 to 100 to increase the rounded to have 2 decimals
                                                $od_amount = $od_amount + $pod_amount;
                                            } else {
                                                $eligible_discount_amount += $pr_c;
                                            }
                                        }
                                    } else {
                                        if ($m_c_data->restrict_to_categories) {
                                            $coupon_cat_ids = explode(',', $m_c_data->restrict_to_categories);
                                            $product_cat_ids = explode('_', $c_info['products'][$i]['cat_id_path']);
                                            $matching_cat_ids = array_intersect($product_cat_ids, $coupon_cat_ids);
                                            if (count($matching_cat_ids) > 0) {
                                                (float)$pr_c = $c_info['products'][$i]['storage_price']['final_price'] * $c_info['products'][$i]['quantity'];
                                                if ($m_c_data->coupon_type == 'P') {
                                                    $od_amount += round($pr_c * 100) / 100 * $c_deduct / 100; // change from 10 to 100 to increase the rounded to have 2 decimals
                                                } else {
                                                    $eligible_discount_amount += $pr_c;
                                                }
                                            }
                                        }
                                    }
                                }

                                if ($m_c_data->coupon_type != 'P') {
                                    if ($c_deduct > $eligible_discount_amount) {
                                        $od_amount = $eligible_discount_amount;
                                    } else {
                                        $od_amount = $c_deduct;
                                    }
                                }
                            } else {
                                if ($m_c_data->coupon_type == 'F') {
                                    $od_amount = $c_deduct;
                                } elseif($m_c_data->coupon_type == 'P') {
                                    $od_amount = $amount * $m_c_data->coupon_amount / 100;
                                }
                            }

                            if (!empty((float) $m_c_data->max_cap) && $od_amount > $m_c_data->max_cap) {
                                $od_amount = $m_c_data->max_cap;
                            }
                        }

                        $currencies_id = Yii::app()->currency->currencies[$c_info['checkout_info']['currency']]['currencies_id'];
                        if (!empty($m_c_data->restrict_to_currency_id) && !in_array($currencies_id, explode(',', $m_c_data->restrict_to_currency_id))) {
                            $od_amount = 0;
                        }
                    }
                }

                if ($od_amount > $amount) {
                    $od_amount = $amount;
                }

                if(!empty($m_c_data) && $m_c_data->coupon_type == 'F' && $od_amount){
                    $od_amount = Yii::app()->currency->advanceCurrencyConversion($od_amount, OrderConfiguration::model()->getConfigValue('DEFAULT_CURRENCY'), $c_info['checkout_info']['currency'], true, 'spot');
                    $od_amount = $od_amount / $c_info['checkout_info']['currency_value'];
                }
            }
        }

        return $od_amount;
    }

    public function applyCredit($order_id)
    {
        $c_info = $GLOBALS['cart']['checkout_info'];

        if (notNull($c_info['coupon_code']) && ($this->deduction != 0)) {
            $m_coupon = OrderCoupons::model()->findByAttributes(['coupon_code' => $c_info['coupon_code']]);
            if (isset($m_coupon->coupon_id)) {
                $m_ocrt_obj = new OrderCouponRedeemTrack();
                $m_ocrt_obj->coupon_id = $m_coupon->coupon_id;
                $m_ocrt_obj->customer_id = $GLOBALS['cart']['customer']['id'];
                $m_ocrt_obj->redeem_date = date('Y-m-d H:i:s');
                $m_ocrt_obj->redeem_ip = $c_info['ip'];
                $m_ocrt_obj->order_id = $order_id;
                $m_ocrt_obj->save();
            }
        }
    }

    public function validateCoupon()
    {
        $_error = '';

        $c_cart = $GLOBALS['cart'];
        $c_info = $GLOBALS['cart']['checkout_info'];
        $c_customer = $GLOBALS['cart']['customer'];

        $amount = $c_info['total'] / $c_info['currency_value'];    // USD

        $m_coupon = OrderCoupons::model()->findByAttributes(['coupon_code' => $c_info['coupon_code']]);
        if (isset($m_coupon->coupon_id) && $m_coupon->coupon_active == 'Y') {
            if ($m_coupon->coupon_type != 'G') {
                if ($amount >= $m_coupon->coupon_minimum_order) {
                    $_current = time();
                    if (strtotime($m_coupon->coupon_start_date) >= $_current) {
                        $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_INVALID_STARTDATE_COUPON');
                    } else {
                        if ($_current >= strtotime($m_coupon->coupon_expire_date)) {
                            $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_INVALID_FINISDATE_COUPON');
                        } else {
                            if ($m_coupon->restrict_to_customers_groups != 'ALL') {
                                $m_customer = OrderCustomers::model()->findByPk($c_customer['id']);
                                if (!in_array($m_customer->customers_groups_id, explode(',', $m_coupon->restrict_to_customers_groups))) {
                                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_NO_INVALID_REDEEM_COUPON');
                                }
                            }

                            if (($_error == '') && ($m_coupon->restrict_to_customers)) {
                                if (!in_array($c_customer['id'], explode(',', $m_coupon->restrict_to_customers))) {
                                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_NO_INVALID_REDEEM_COUPON');
                                }
                            }

                            if (($_error == '') && ($m_coupon->mobile_only && !isMobile())) {
                                $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_MOBILE_ONLY_COUPON');
                            }

                            if (($_error == '') && ($m_coupon->uses_per_coupon_unlimited == 'N')) {
                                if (($m_coupon->uses_per_coupon > 0) && (OrderCouponRedeemTrack::model()->getCouponRedeemTotal($m_coupon->coupon_id) >= $m_coupon->uses_per_coupon)) {
                                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_INVALID_USES_COUPON');
                                }
                            }

                            if (($_error == '') && ($m_coupon->uses_per_user_unlimited == 'N')) {
                                if (($m_coupon->uses_per_user > 0) && (OrderCouponRedeemTrack::model()->getCouponRedeemTotalByCustomer($m_coupon->coupon_id, $c_customer['id']) >= $m_coupon->uses_per_user)) {
                                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_INVALID_USES_USER_COUPON');
                                }
                            }

                            if (($_error == '') && ($m_coupon->restrict_to_products || $m_coupon->restrict_to_categories)) {
                                $_valid_product = false;
                                $_valid_category = false;

                                for ($i = 0; $i < sizeof($c_cart['products']); $i++) {
                                    if ($m_coupon->restrict_to_products) {
                                        $coupon_prod_ids = explode(",", $m_coupon->restrict_to_products);
                                        if (in_array($c_cart['products'][$i]['id'], $coupon_prod_ids)) {
                                            $_valid_product = true;
                                        }
                                    } else {
                                        if ($m_coupon->restrict_to_categories) {
                                            $coupon_cat_ids = explode(',', $m_coupon->restrict_to_categories);
                                            $product_cat_ids = explode('_', $c_cart['products'][$i]['cat_id_path']);
                                            $matching_cat_ids = array_intersect($product_cat_ids, $coupon_cat_ids);
                                            if (count($matching_cat_ids) > 0) {
                                                $_valid_category = true;
                                            }
                                        }
                                    }
                                }

                                if (!$_valid_product && !$_valid_category) {
                                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_NO_INVALID_REDEEM_COUPON');
                                }
                            }
                            if ($_error == '' && !empty($m_coupon->restrict_to_currency_id)) {
                                $currencies_id = Yii::app()->currency->currencies[$c_info['currency']]['currencies_id'];
                                if (!in_array($currencies_id, explode(',', $m_coupon->restrict_to_currency_id))) {
                                    if(strtolower(Yii::app()->controller->id) == 'buynow'){
                                        $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_NO_INVALID_REDEEM_COUPON');
                                    }
                                    else{
                                        $GLOBALS['cart']['checkout_info']['coupon_code'] = '';
                                    }
                                }
                            }
                            if ($_error == '' && !empty($m_coupon->restrict_to_payment_id)) {
                                if ($m_coupon->restrict_to_payment_id) {
                                    $pm_name = [];
                                    $pm_list = PipwavePaymentMapperBase::model()->findAll(['condition' => 'id IN (' . $m_coupon->restrict_to_payment_id . ')']);
                                    foreach ($pm_list as $pm) {
                                        $pm_name[] = $pm->pm_display_name;
                                    }
                                    $GLOBALS['error_label'] = Yii::t('orderModule.OrderOtCouponCom', 'MODULE_ORDER_PM_RESTRICTION', ['{PM_LIST}' => implode(', ', $pm_name)]);
                                }
                            }
                        }
                    }
                } else {
                    $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_INVALID_ORDER_MIN_AMOUNT', ['{SYS_COUPON_AMT}' => Yii::app()->currency->format($m_coupon->coupon_minimum_order, true, $c_info['currency'], $c_info['currency_value'])]);
                }
            }
        } else {
            $_error = Yii::t('orderModule.OrderOtCouponCom', 'ERROR_NO_INVALID_REDEEM_COUPON');
        }

        if (!empty($_error)) {
            $GLOBALS['error_label'] = $_error;
        }

        return $_error;
    }

    public static function reportError($subject, $response_data, $ext_subject = '')
    {
        ob_start();
        echo $ext_subject . '<br>';
        echo "========================RESPONSE=========================<BR><pre>";
        print_r($response_data);
        echo "========================================================<BR>";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = Yii::app()->params['DEV_DEBUG_EMAIL_SUBJECT_PREFIX'] . ' - ' . $subject . ' - ' . date("F j, Y H:i");
        $attachments = [
            [
                'color' => 'danger',
                'text' => $response_data,
            ],
        ];
        Yii::app()->slack->send($subject, $attachments, 'DEV_DEBUG_CHECKOUT');
    }

}
