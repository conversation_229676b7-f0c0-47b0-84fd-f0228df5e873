<?php

class OrderOtGstCom extends CApplicationComponent {

    public $code, $title, $display_title, $enabled, $sort_order, $output;
    public $gst_country = '';

    public function __construct() {
        $this->code = 'ot_gst';

        $_gst_config = $GLOBALS['cart']['checkout_info']['tax_info'];

        $this->title = (isset($_gst_config['title']) && notNull($_gst_config['title']) ? $_gst_config['title'] : '');
        $this->display_title = (isset($_gst_config['title']) && notNull($_gst_config['title']) ? $_gst_config['title'] : '');
        $this->gst_country = (isset($_gst_config['country']) && notNull($_gst_config['country']) ? $_gst_config['country'] : '');

        $this->enabled = true;
        $this->sort_order = 860;

        $this->output = array();
    }

    public function process() {
        $c_info = $GLOBALS['cart']['checkout_info'];

        # amount can be 0 or more than 0
        if (isset($c_info['tax_info']) && is_array($c_info['tax_info'])) {
            $gst_percentage = (isset($c_info['tax_info']['rate']) && $c_info['tax_info']['rate']) ? $c_info['tax_info']['rate'] : '0';
            $gst_amt = (isset($c_info['tax_info']['amount']) && $c_info['tax_info']['amount']) ? $c_info['tax_info']['amount'] : 0;

            // based on PGS passing value
            if (($GLOBALS['cart']['checkout_info']['total'] > 0) && ($gst_amt > 0)) {
                $GLOBALS['cart']['checkout_info']['total'] = number_format((double) trim($GLOBALS['cart']['checkout_info']['total']) + (double) trim($gst_amt), Yii::app()->currency->currencies[$c_info['currency']]['decimal_places'], Yii::app()->currency->currencies[$c_info['currency']]['decimal_point'], '');
            }

            $this->output[] = array(
                'title' => $this->title . ':',
                'display_title' => $this->display_title . ':',
                'checkout_value' => $gst_amt,
                'storage_text' => '<b>' . Yii::app()->currency->format($gst_amt, false, $c_info['currency']) . '</b>',
                'storage_value' => ($gst_amt > 0 ? ($gst_amt / $c_info['currency_value']) : 0), // USD
                'input' => array(),
                'output' => array(
                    'percentage' => $gst_percentage,
                    'country' => $this->gst_country,
                    'amount' => $gst_amt > 0 ? $gst_amt : '0',
                )
            );
        }
    }

}