<?php

class OrderOtGvCom extends CApplicationComponent
{

    public $code, $title, $display_title, $enabled, $sort_order;
    public $output, $deduction, $is_top_sc;
    private $restriction_mode, $restriction_info, $user_gv_data;

    public function __construct()
    {
        // Store Credit
        $this->code = 'ot_gv';

        $this->title = Yii::t('orderModule.OrderOtGvCom', 'MODULE_ORDER_TOTAL_GV_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtGvCom', 'MODULE_ORDER_TOTAL_GV_DISPLAY_TITLE');

        $this->enabled = true;
        $this->sort_order = 740;

        $this->output = array();
    }

    function process()
    {
        $c_info = $GLOBALS['cart']['checkout_info'];
        $currency = $c_info['currency'];
        $order_total = $c_info['total'];

        $_checkout_value = 0;

        if ($order_total > 0) {
            $_checkout_value = $this->calculateCredit($order_total);
            if (!empty($_checkout_value)) {
                $_checkout_value = number_format($_checkout_value,
                    Yii::app()->currency->currencies[$currency]['decimal_places'],
                    Yii::app()->currency->currencies[$currency]['decimal_point'], '');
                $_total = $order_total - $_checkout_value;
                if (($_total <= 0) && ($_checkout_value != $order_total)) {
                    $_total = 0;
                    $_checkout_value = $order_total;
                }

                $this->deduction = $_checkout_value;
                $GLOBALS['cart']['checkout_info']['total'] = $_total;
            }
        }

        # If it's not top store credit
        if ($this->is_top_sc !== true) {
            $_open = $this->userGvAmount();

            if($_open <= 0){
                $_checkout_value = 0;
            }

            $_close = (($_open > 0) ? $_open - $_checkout_value : 0);

            $_checkout_value = number_format($_checkout_value,
                Yii::app()->currency->currencies[$currency]['decimal_places'],
                Yii::app()->currency->currencies[$currency]['decimal_point'], '');

            $this->output[] = array(
                'title' => $this->title . ':',
                'display_title' => Yii::t('orderModule.OrderOtGvCom', 'TEXT_INFO_STORE_CREDIT_DEDUCTION'),
                'checkout_value' => $_checkout_value,
                'storage_text' => '<b>' . Yii::app()->currency->format($_checkout_value, true, $currency, 1) . '</b>',
                'storage_value' => $_checkout_value > 0 ? ($_checkout_value / $c_info['currency_value']) : 0, // USD
                'input' => array(),
                'output' => array(
                    'opening_balance' => array(
                        'title' => Yii::t('orderModule.OrderOtGvCom', 'TEXT_INFO_STORE_CREDIT_BALANCE'),
                        'text' => Yii::app()->currency->format($_open, true, $currency, 1),
                        'value' => $_open
                    ),
                    'closing_balance' => array(
                        'title' => Yii::t('orderModule.OrderOtGvCom', 'TEXT_INFO_STORE_CREDIT_RAMAINING'),
                        'text' => Yii::app()->currency->format($_close, true, $currency, 1),
                        'value' => $_close
                    )
                )
            );
        }
    }

    public function calculateCredit($order_total)
    {
        $p_info = $GLOBALS['cart']['products'];
        $gv_payment_amount = 0;
        $this->is_top_sc = false;
        $restrictions_for_sc = false;
        $coupon_restriction = (!empty($GLOBALS['cart']['checkout_info']['coupon_code']) && OrderCoupons::checkPaymentRestriction($GLOBALS['cart']['checkout_info']['coupon_code']) ? true : false);

        # check if is top up store credit
        if (!empty($p_info)) {
            for ($i = 0, $cnt = count($p_info); $cnt > $i; $i++) {
                if ($p_info[$i]['custom_products_type_id'] == 3) {
                    $this->is_top_sc = true;
                }
                // Check store credit payment restrictions
                $this->setPaymentRestrictions($p_info[$i]['id']);

                $restriction_info_array = explode(",", $this->restriction_info);
                if ($this->restriction_mode == 'Allow') {
                    if (!in_array('0', $restriction_info_array)) {
                        $restrictions_for_sc = true;
                    }
                } elseif ($this->restriction_mode == 'Disallow') {
                    if (in_array('0', $restriction_info_array)) {
                        $restrictions_for_sc = true;
                    }
                }

                if($coupon_restriction){
                    $restrictions_for_sc = true;
                }
            }
        }

        if ($this->is_top_sc !== true) {
            $gv_payment_amount = $this->userGvAmount();
            $full_cost = $order_total - $gv_payment_amount;
            if($gv_payment_amount < 0){
                $gv_payment_amount = 0;
            }
            if (0 > $full_cost) {
                $gv_payment_amount = $order_total;
            }
        }

        if ($restrictions_for_sc == true) {
            $gv_payment_amount = 0;
        }

        return $gv_payment_amount;
    }

    private function setPaymentRestrictions($pid)
    {
        $product_obj = new OrderProductCom();

        $restrictions_array = $product_obj->getProductsPaymentMethodsRestrictions($pid);

        $this->restriction_mode = $restrictions_array['restriction_mode'];
        $this->restriction_info = $restrictions_array['restriction_info'];
    }

    public function userGvAmount($return_total = true)
    {
        $c_customer = $GLOBALS['cart']['customer'];
        $currency = Yii::app()->currency->getIdByCode($GLOBALS['cart']['checkout_info']['currency']);

        $sc_amount = 0;

        if($this->user_gv_data){
            $m_cgc_data = $this->user_gv_data;
        }
        else{
            $sc_balance = (new StoreCreditCom($c_customer['id']))->_get_current_credits_balance(true);
            $m_cgc_data = $sc_balance;
        }

        if ($m_cgc_data && $m_cgc_data['sc_currency_id'] == $currency) {
            $this->user_gv_data = $m_cgc_data;
            $sc_amount = (double)$m_cgc_data['sc_balance'];
        }

        if ($return_total) {
            return (double)$sc_amount;
        } else {
            return array(
                'sc_amount' => (double)$sc_amount,
                'sc_currency_id' => $currency
            );
        }
    }

    public function applyCredit($order_id)
    {
        $ot_cid = $GLOBALS['cart']['customer']['id'];
        $total_deduct_amount = (double)$this->deduction;
        if ($this->userGvAmount()) {
            if ($total_deduct_amount > 0) {
                
                $sc_amount_deduct = 0;
                $update_sc_array = array();

                $sc_array = $this->userGvAmount(false);

                if ($total_deduct_amount > 0 && $sc_array['sc_amount'] > 0) {
                    if ($total_deduct_amount > $sc_array['sc_amount']) {
                        $sc_amount_deduct = $sc_array['sc_amount'];
                        $total_deduct_amount -= $sc_array['sc_amount'];
                    } else {
                        $sc_amount_deduct = $total_deduct_amount;
                        $total_deduct_amount = 0;
                    }
                }

                if ($sc_amount_deduct > 0) {
                    $update_sc_array = array(
                        'orders_id' => $order_id,
                        'reference_id' => $order_id,
                        'amount' => $sc_amount_deduct,
                        'total_amount' => $sc_amount_deduct,
                        'currency_id' => $sc_array['sc_currency_id'],
                        'activity' => Yii::app()->params['LOG']['SC_ACTIVITY_TYPE_PURCHASE'],
                        'transaction_type' => 'DEDUCT_CREDIT',
                        'show_customer' => 1
                    );
                }

                $sc_obj = new StoreCreditCom($ot_cid);
                return $sc_obj->set_store_credit_balance($update_sc_array, '', '');
            }
        } elseif ($this->deduction > 0) {
            //return false when no sc balance and with checkout value
            return false;
        }
        // default to true when without sc account & empty balance + deduction
        return false;
    }

}