<?php

class OrderOtLoworderfeeCom extends CApplicationComponent {

    public $code, $title, $display_title, $enabled, $sort_order, $output;

    public function __construct() {
        $this->code = 'ot_loworderfee';

        $this->title = Yii::t('orderModule.OrderOtLoworderfeeCom', 'MODULE_ORDER_TOTAL_LOWORDERFEE_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtLoworderfeeCom', 'MODULE_ORDER_TOTAL_LOWORDERFEE_TITLE');

        $this->enabled = false;
        $this->sort_order = 4;

        $this->output = array();
    }

}