<?php

class OrderOtSurchargeCom extends CApplicationComponent {

    public $code, $title, $display_title, $enabled, $sort_order, $output;
    public $selected_to_currency = '';

    public function __construct($pass_currency = '') {
        $this->code = 'ot_surcharge';

        $this->title = Yii::t('orderModule.OrderOtSurchargeCom', 'MODULE_ORDER_TOTAL_SURCHARGE_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtSurchargeCom', 'MODULE_ORDER_TOTAL_SURCHARGE_DISPLAY_TITLE');

        $this->enabled = false;
        $this->sort_order = 2;

        $this->output = array();
        $this->selected_to_currency = (!empty($pass_currency) ? $pass_currency : ( $GLOBALS['cart']['checkout_info']['currency'] ? $GLOBALS['cart']['checkout_info']['currency'] : OrderConfiguration::model()->getConfigValue('DEFAULT_CURRENCY') ) );
    }

    public function process() {
        $c_info = $GLOBALS['cart']['checkout_info'];
        $surcharge_amt = $c_info['surcharge'];

        // based on PGS passing value
        if (($GLOBALS['cart']['checkout_info']['total'] > 0) && ($surcharge_amt > 0)) {
            $GLOBALS['cart']['checkout_info']['total'] = number_format((double) trim($GLOBALS['cart']['checkout_info']['total']) + (double) trim($surcharge_amt), Yii::app()->currency->currencies[$c_info['currency']]['decimal_places'], Yii::app()->currency->currencies[$c_info['currency']]['decimal_point'], '');
        }

        $this->output[] = array(
            'title' => $this->title . ':',
            'display_title' => $this->display_title . ':',
            'checkout_value' => $surcharge_amt,
            'storage_text' => '<b>' . Yii::app()->currency->format($surcharge_amt, false, $c_info['currency']) . '</b>',
            'storage_value' => $surcharge_amt / $c_info['currency_value'], // USD
            'input' => array(),
            'output' => array()
        );
    }

}