<?php

class OrderOtTaxCom extends CApplicationComponent {

    public $code, $title, $display_title, $enabled, $sort_order, $output;

    public function __construct() {
        $this->code = 'ot_tax';

        $this->title = Yii::t('orderModule.OrderOtTaxCom', 'MODULE_ORDER_TOTAL_TAX_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtTaxCom', 'MODULE_ORDER_TOTAL_TAX_TITLE');

        $this->enabled = false;
        $this->sort_order = 3;

        $this->output = array();
    }

}