<?php

class OrderOtTotalCom extends CApplicationComponent {

    public $code, $title, $display_title, $enabled, $sort_order, $output;

    public function __construct() {
        $this->code = 'ot_total';

        $this->title = Yii::t('orderModule.OrderOtTotalCom', 'MODULE_ORDER_TOTAL_TOTAL_TITLE');
        $this->display_title = Yii::t('orderModule.OrderOtTotalCom', 'MODULE_ORDER_TOTAL_TOTAL_DISPLAY_TITLE');

        $this->enabled = true;
        $this->sort_order = 900;

        $this->output = array();
    }

    function process() {
        $c_info = $GLOBALS['cart']['checkout_info'];

        $_total = number_format($c_info['total'], Yii::app()->currency->currencies[$c_info['currency']]['decimal_places'], Yii::app()->currency->currencies[$c_info['currency']]['decimal_point'], '');
        $GLOBALS['cart']['checkout_info']['total'] = $_total;
        
        $this->output[] = array(
            'title' => $this->title . ':',
            'display_title' => $this->display_title,
            'checkout_value' => $_total,
            'storage_text' => '<b>' . Yii::app()->currency->format($_total, false, $c_info['currency']) . '</b>',
            'storage_value' => $_total / $c_info['currency_value'], // USD
            'input' => array(),
            'output' => array()
        );
    }

}