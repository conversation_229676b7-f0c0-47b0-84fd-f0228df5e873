<?php

class OrderProductCom extends ProductCom {
    public function validateCustomOutOfStockRules($products_id, $products_quantity, $purchase_qty, & $available_qty = 0, $customer_group_id = 0, $skip_cache = false) {
        # Force to use DB setting
        $skip_cache = true;
        return parent::validateCustomOutOfStockRules($products_id, $products_quantity, $purchase_qty, $available_qty, $customer_group_id, $skip_cache);
    }
    
    public function updateCustomOutOfStockRuleOutOfStock($products_id, $current_available_quantity) {
        return parent::updateCustomOutOfStockRuleOutOfStock($products_id, $current_available_quantity);
    }

}